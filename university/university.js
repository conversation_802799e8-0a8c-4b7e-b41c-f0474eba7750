/**
@researchCost - цена за обучение факультету в долларах(без центов)
@researchTime - время обучения факультету в часах
@completeCooldown - кд между играми(общий для всех факультетов) в часах
@stages - название мини-игр для каждого факультета
**/

module.exports = {
    completeCooldown: 20,

    universityQuests: [74, 75, 76, 77, 78, 79, 80, 81, 82],

    bookStorages: [
        { x: -1594.6549, y: 212.756, z: 59.9802, r: 25.5118 },
        { x: -1585.134, y: 220.6418, z: 59.9802, r: 113.3858 },
        { x: -1578.0132, y: 219.956, z: 59.9802, r: -65.1969 },
        { x: -1574.4528, y: 221.5517, z: 59.9802, r: -59.5276 },
        { x: -1574.5187, y: 226.5758, z: 59.9802, r: 28.3465 },
        { x: -1570.5758, y: 223.2923, z: 59.9802, r: -65.1969 },
        { x: -1570.1538, y: 228.567, z: 59.9802, r: 22.6772 },
        { x: -1566.6461, y: 225.2044, z: 59.9802, r: -59.5276 },
        { x: -1562.5582, y: 229.4505, z: 59.9802, r: -62.3622 },
        { x: -1559.5385, y: 223.1209, z: 59.9802, r: -56.6929 },
        { x: -1561.2528, y: 218.8615, z: 59.9802, r: -147.4016 },
        { x: -1556.9934, y: 217.6483, z: 59.9802, r: -65.1969 },
        { x: -1557.8506, y: 211.4242, z: 59.9802, r: -150.2362 },
        { x: -1554.1978, y: 211.7011, z: 59.9802, r: -56.6929 },
        { x: -1555.0022, y: 205.0945, z: 59.9802, r: -141.7323 },
        { x: -1551.811, y: 206.5978, z: 59.9802, r: -62.3622 },
        { x: -1554.9495, y: 203.0901, z: 59.9802, r: 119.0551 },
        { x: -1549.8594, y: 202.2989, z: 59.9802, r: -62.3622 },
        { x: -1554.7252, y: 198.211, z: 59.9802, r: -147.4016 },
        { x: -1558.8792, y: 201.0593, z: 59.9802, r: 113.3858 },
        { x: -1560.3297, y: 195.6659, z: 59.9802, r: -147.4016 },
        { x: -1566.3033, y: 197.578, z: 59.9802, r: 110.5512 },
        { x: -1572.3165, y: 192.9494, z: 59.9802, r: 119.0551 },
        { x: -1570.4572, y: 201.8374, z: 59.9802, r: 25.5118 },
        { x: -1575.9824, y: 201.0198, z: 59.9802, r: 113.3858 },
        { x: -1579.2131, y: 207.956, z: 59.9802, r: 116.2205 },
        { x: -1575.8374, y: 213.3231, z: 59.9802, r: 22.6772 },
        { x: -1577.8286, y: 217.6879, z: 59.9802, r: 28.3465 },
        { x: -1598.4264, y: 204.4615, z: 59.2388, r: 113.3858 },
        { x: -1596.0659, y: 199.3714, z: 59.2388, r: 116.2205 },
        { x: -1587.5341, y: 184.0088, z: 59.9802, r: -158.7402 },
        { x: -1590.1714, y: 194.0044, z: 63.8894, r: 22.6772 },
        { x: -1595.9604, y: 199.4637, z: 63.8894, r: 116.2205 },
        { x: -1594.7076, y: 205.2527, z: 63.8894, r: -65.1969 },
        { x: -1598.6241, y: 205.1077, z: 63.8894, r: 119.0551 },
        { x: -1595.3275, y: 209.3934, z: 63.8894, r: 28.3465 },
        { x: -1584.5802, y: 214.5099, z: 63.8894, r: 22.6772 },
        { x: -1585.0154, y: 211.2527, z: 63.8894, r: -150.2362 },
        { x: -1582.7076, y: 206.4132, z: 63.8894, r: 31.1811 },
        { x: -1578.2902, y: 207.0462, z: 63.8894, r: -70.8661 },
        { x: -1576.2329, y: 202.7341, z: 63.8894, r: -56.6929 },
        { x: -1581.1252, y: 203.0242, z: 63.8894, r: -155.9055 },
        { x: -1578.989, y: 198.3297, z: 63.8894, r: -150.2362 },
        { x: -1577.578, y: 194.4396, z: 63.8894, r: -144.5669 },
        { x: -1581.6263, y: 192.5802, z: 63.8894, r: -153.0709 },
        { x: -1585.6088, y: 190.655, z: 63.8894, r: -150.2362 },
        { x: -1585.2, y: 220.5758, z: 63.8894, r: 119.0551 },
        { x: -1578.1714, y: 219.7714, z: 63.8894, r: -65.1969 },
        { x: -1574.3868, y: 221.5648, z: 63.8894, r: -56.6929 },
        { x: -1574.4923, y: 226.6418, z: 63.8894, r: 25.5118 },
        { x: -1570.1538, y: 228.633, z: 63.8894, r: 25.5118 },
        { x: -1570.5758, y: 223.3978, z: 63.8894, r: -59.5276 },
        { x: -1566.6329, y: 225.244, z: 63.8894, r: -62.3622 },
        { x: -1562.1362, y: 228.7385, z: 63.8894, r: -59.5276 },
        { x: -1559.5121, y: 223.0813, z: 63.8894, r: -65.1969 },
        { x: -1561.3978, y: 218.8615, z: 63.8894, r: -153.0709 },
        { x: -1557.9561, y: 211.556, z: 63.8894, r: -147.4016 },
        { x: -1553.9341, y: 211.3714, z: 63.8894, r: -65.1969 },
        { x: -1551.9824, y: 207.2176, z: 63.8894, r: -62.3622 },
        { x: -1554.8835, y: 202.9978, z: 63.8894, r: 113.3858 },
        { x: -1558.866, y: 201.0593, z: 63.8894, r: 121.8898 },
        { x: -1560.3297, y: 195.6527, z: 63.8894, r: -150.2362 },
        { x: -1562.6901, y: 199.2527, z: 63.8894, r: 113.3858 },
        { x: -1565.8286, y: 193.0549, z: 63.8894, r: -158.7402 },
        { x: -1568.4791, y: 197.5385, z: 63.8894, r: 22.6772 },
        { x: -1572.4088, y: 193.1341, z: 63.8894, r: 113.3858 },
        { x: -1574.5187, y: 203.4989, z: 63.8894, r: 113.3858 },
        { x: -1576.5626, y: 207.9165, z: 63.8894, r: 113.3858 },
    ],

    faculties: {
        medicine: {
            npcId: 381,
            startNpcId: 380,

            researchCost: 850000,
            stages: [
                'medicine_1',
                'seamstress',
                'medicine_2',
                'alchemy',
                'medicine_3',
                'medicVaccinations',
                'medicine_4',
                'medicRescue'
            ],

            buffs: [
                'medicine1',
                'medicine2',
                'medicine3',
                'medicine4',
                'medicine5',
                'medicine6',
                'medicine7',
                'medicine8',
                'medicine9',
            ],

            clothesReward: {
                0: {
                    component: 7,
                    drawableId: 2149,
                    texture: 2,
                    isProp: 0
                },
                1: {
                    component: 7,
                    drawableId: 2127,
                    texture: 2,
                    isProp: 0
                }
            },
            startQuestId: 74,
            completeBuff: 'UniversityMed',
            buffDuration: -1, // время, сколько действует бафф в мс(-1 — бесконечный бафф)
            startDate: '2024-08-28 10:00:00',
        },

        engineering: {
            npcId: 382,
            startNpcId: 380,

            researchCost: 700000,
            stages: [
                'engineering_1',
                'breakDoor',
                'engineering_2',
                'hijackIgnitionMaze',
                'engineering_3',
                'hijackIgnitionSingleLine',
                'engineering_4',
                'danceBattle'
            ],

            buffs: [
                'engineering1',
                'engineering2',
                'engineering3',
                'engineering4',
                'engineering5',
                'engineering6',
                'engineering7',
                'engineering8',
            ],

            clothesReward: {
                0: {
                    component: 0,
                    drawableId: 2092,
                    texture: 0,
                    isProp: 1
                },
                1: {
                    component: 0,
                    drawableId: 2096,
                    texture: 0,
                    isProp: 1
                }
            },

            startQuestId: 75,
            completeBuff: 'UniversityTeh',
            buffDuration: -1, // время, сколько действует бафф в мс(-1 — бесконечный бафф)
            startDate: '2024-08-28 10:00:00',
        },

        cooking: {
            npcId: 383,
            startNpcId: 380,

            researchCost: 600000,
            stages: [
                'cooking_1',
                'floorWasher',
                'cooking_2',
                'farmer',
                'cooking_3',
                'makeBurgers',
                'cooking_4',
                'dessertMaster'
            ],

            buffs: [
                'cooking1',
                'cooking2',
                'cooking3',
                'cooking4',
                'cooking5',
                'cooking6',
                'cooking7',
                'cooking8',
            ],

            clothesReward: {
                0: {
                    component: 0,
                    drawableId: 2093,
                    texture: 0,
                    isProp: 1
                },
                1: {
                    component: 0,
                    drawableId: 2097,
                    texture: 0,
                    isProp: 1
                }
            },

            startQuestId: 76,
            completeBuff: 'UniversityCook',
            buffDuration: -1, // время, сколько действует бафф в мс(-1 — бесконечный бафф)
            startDate: '2024-08-28 10:00:00',
        },

        physycal: {
            npcId: 384,
            startNpcId: 380,

            researchCost: 650000,
            stages: [
                'physycal_1',
                'stadiumRunning',
                'physycal_2',
                'swimShark',
                'physycal_3',
                'parachuteJumps',
                'physycal_4',
                'ringFight'
            ],

            buffs: [
                'physycal1',
                'physycal2',
                'physycal3',
                'physycal4',
                'physycal5',
                'physycal6',
                'physycal7',
                'physycal8',
            ],

            startQuestId: 77,
            completeBuff: 'UniversitySport',
            buffDuration: -1, // время, сколько действует бафф в мс(-1 — бесконечный бафф)
            startDate: '2024-09-26 10:00:00',

            clothesReward: {
                0: [
                    {
                        component: 0,
                        drawableId: 2094,
                        texture: 0,
                        isProp: 1
                    },
                    {
                        component: 0,
                        drawableId: 2094,
                        texture: 1,
                        isProp: 1
                    },
                    {
                        component: 0,
                        drawableId: 2094,
                        texture: 2,
                        isProp: 1
                    },
                ],
                1: [
                    {
                        component: 0,
                        drawableId: 2098,
                        texture: 0,
                        isProp: 1
                    },
                    {
                        component: 0,
                        drawableId: 2098,
                        texture: 1,
                        isProp: 1
                    },
                    {
                        component: 0,
                        drawableId: 2098,
                        texture: 2,
                        isProp: 1
                    },
                ],
            },
        },

        jurisprudence: {
            npcId: 385,
            startNpcId: 380,

            researchCost: 650000,
            stages: [
                'jurisprudence_1',
                'hijackIgnitionRememberNumber',
                'jurisprudence_2',
                'tirGame',
                'jurisprudence_3',
                'hijackIgnitionCatchBall',
                'jurisprudence_4',
                'investigation'
            ],

            buffs: [
                'jurisprudence1',
                'jurisprudence2',
                'jurisprudence3',
                'jurisprudence4',
                'jurisprudence5',
                'jurisprudence6',
                'jurisprudence7',
                'jurisprudence8',
            ],

            clothesReward: {
                0: [
                    {
                        component: 1,
                        drawableId: 2028,
                        texture: 0,
                        isProp: 1
                    },
                    {
                        component: 1,
                        drawableId: 2028,
                        texture: 1,
                        isProp: 1
                    },
                    {
                        component: 1,
                        drawableId: 2028,
                        texture: 2,
                        isProp: 1
                    },
                ],
                1: [
                    {
                        component: 1,
                        drawableId: 2029,
                        texture: 0,
                        isProp: 1
                    },
                    {
                        component: 1,
                        drawableId: 2029,
                        texture: 1,
                        isProp: 1
                    },
                    {
                        component: 1,
                        drawableId: 2029,
                        texture: 2,
                        isProp: 1
                    },
                ],
            },

            startQuestId: 78,
            completeBuff: 'UniversityLaw',
            buffDuration: -1, // время, сколько действует бафф в мс(-1 — бесконечный бафф)
            startDate: '2024-08-28 10:00:00',
        },

        'languages-spanish': {
            npcId: 386,
            startNpcId: 380,

            researchCost: 200000,
            stages: [
                'languages-spanish_1',
                'geographyMapGame_1',
                'languages-spanish_2',
                'geographyMapGame_3',
                'geographyMapGame_5',
            ],

            buffs: [
                'spanish1',
                'spanish2',
            ],

            startQuestId: 79,

            completeBuff: 'Lang_ESP',
            buffDuration: -1, // время, сколько действует бафф в мс(-1 — бесконечный бафф)
            startDate: '2024-08-28 10:00:00',
        },

        'languages-japanese': {
            npcId: 387,
            startNpcId: 380,

            researchCost: 1200000,
            stages: [
                'languages-japanese_1',
                'geographyMapGame_2',
                'languages-japanese_2',
                'geographyMapGame_4',
                'geographyMapGame_6',
            ],

            buffs: [
                'japanese1',
                'japanese2',
            ],

            startQuestId: 80,

            completeBuff: 'Lang_JAP',
            buffDuration: -1, // время, сколько действует бафф в мс(-1 — бесконечный бафф)
            startDate: '2024-08-28 10:00:00',
        },

        'languages-russian': {
            npcId: 388,
            startNpcId: 380,

            researchCost: 600000,
            stages: [
                'languages-russian_1',
                'geographyMapGame_1',
                'languages-russian_2',
                'geographyMapGame_4',
                'geographyMapGame_5',
            ],

            buffs: [
                'russian1',
                'russian2',
            ],

            startQuestId: 81,
            completeBuff: 'Lang_RU',
            buffDuration: -1, // время, сколько действует бафф в мс(-1 — бесконечный бафф)
            startDate: '2024-08-28 10:00:00',
        },

        'languages-italian': {
            npcId: 389,
            startNpcId: 380,

            researchCost: 400000,
            stages: [
                'languages-italian_1',
                'geographyMapGame_2',
                'languages-italian_2',
                'geographyMapGame_3',
                'geographyMapGame_6',
            ],

            buffs: [
                'italian1',
                'italian2',
            ],

            startQuestId: 82,
            completeBuff: 'Lang_ITA',
            buffDuration: -1, // время, сколько действует бафф в мс(-1 — бесконечный бафф)
            startDate: '2024-09-01 10:00:00',
        },
    },

    minigames: {
        seamstress: { // Швея
            teleports: {
                start: { x: 255.1516, y: -1351.8198, z: 24.545, heading: -130.3937 },
                end: { x: -1649.0505, y: 159.9560, z: 70.5619, heading: 120 },
            },

            duration: 420, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 20, // Сколько надо потратить минимально времени на прохождение в секундах

            hint: {
                title: 'fun.university.minigames.seamstress.hints.1.title',
                desc: 'fun.university.minigames.seamstress.hints.1.desc',
                timeout: 15000,
            },
            timerText: 'fun.university.minigames.seamstress.timerBar',

            ambientMusic: {
                path: 'university.seamstress',
                options: {
                    volume: 25,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            }
        },
        alchemy: { // Алхимия
            teleports: {
                start: { x: -1654.3121, y: 157.4110, z: 70.5619, heading: 113 },
                end: { x: -1643.3671, y: 159.1516, z: 70.5619, heading: 113 },
            },

            duration: 900, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 20, // Сколько надо потратить минимально времени на прохождение в секундах

            hint: {
                title: 'fun.university.minigames.alchemy.hints.1.title',
                desc: 'fun.university.minigames.alchemy.hints.1.desc',
                timeout: 15000,
            },
            timerText: 'fun.university.minigames.alchemy.timerBar',

            ambientMusic: {
                path: 'university.seamstress',
                options: {
                    volume: 25,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            },

            cookOptions: [95, 112, 142, 155, 185, 199, 258]
        },
        medicVaccinations: { // Вакцинация
            teleports: {
                start: { x: -1616.8484, y: 210.4615, z: 60.0476, heading: 25 },
                end: { x: -1643.3671, y: 159.1516, z: 70.5619, heading: 113 },
            },

            duration: 420, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 60, // Сколько надо потратить минимально времени на прохождение в секундах

            questions: [
                {
                    title: 'fun.university.minigames.medicVaccinations.makeSelect',
                    text: 'fun.university.minigames.medicVaccinations.questions.1.text',
                    buttons: [{ text: 'fun.university.minigames.medicVaccinations.questions.1.buttons.1' }, { text: 'fun.university.minigames.medicVaccinations.questions.1.buttons.2', correct: true }]
                },
                {
                    title: 'fun.university.minigames.medicVaccinations.makeSelect',
                    text: 'fun.university.minigames.medicVaccinations.questions.2.text',
                    buttons: [{ text: 'fun.university.minigames.medicVaccinations.questions.2.buttons.1' }, { text: 'fun.university.minigames.medicVaccinations.questions.2.buttons.2', correct: true }]
                },
                {
                    title: 'fun.university.minigames.medicVaccinations.makeSelect',
                    text: 'fun.university.minigames.medicVaccinations.questions.3.text',
                    buttons: [{ text: 'fun.university.minigames.medicVaccinations.questions.3.buttons.1' }, { text: 'fun.university.minigames.medicVaccinations.questions.3.buttons.2', correct: true }]
                },
                {
                    title: 'fun.university.minigames.medicVaccinations.makeSelect',
                    text: 'fun.university.minigames.medicVaccinations.questions.4.text',
                    buttons: [{ text: 'fun.university.minigames.medicVaccinations.questions.4.buttons.1', correct: true }, { text: 'fun.university.minigames.medicVaccinations.questions.4.buttons.2' }]
                },
                {
                    title: 'fun.university.minigames.medicVaccinations.makeSelect',
                    text: 'fun.university.minigames.medicVaccinations.questions.5.text',
                    buttons: [{ text: 'fun.university.minigames.medicVaccinations.questions.5.buttons.1', correct: true }, { text: 'fun.university.minigames.medicVaccinations.questions.5.buttons.2' }]
                },
                {
                    title: 'fun.university.minigames.medicVaccinations.makeSelect',
                    text: 'fun.university.minigames.medicVaccinations.questions.6.text',
                    buttons: [{ text: 'fun.university.minigames.medicVaccinations.questions.6.buttons.1', correct: true }, { text: 'fun.university.minigames.medicVaccinations.questions.6.buttons.2' }]
                },
                {
                    title: 'fun.university.minigames.medicVaccinations.makeSelect',
                    text: 'fun.university.minigames.medicVaccinations.questions.7.text',
                    buttons: [{ text: 'fun.university.minigames.medicVaccinations.questions.7.buttons.1', correct: true }, { text: 'fun.university.minigames.medicVaccinations.questions.7.buttons.2' }]
                },
            ],

            peds: [
                {
                    model: 'A_F_M_TrampBeac_01',
                    pos: { x: -1637.7098, y: 214.5495, z: 60.6373, heading: 113 },

                    anim: ['timetable@reunited@ig_10', 'base_amanda'],
                    flag: 1,
                },
                {
                    model: 'A_F_Y_EastSA_01',
                    pos: { x: -1654.4176, y: 216.5143, z: 60.7554, heading: 110 },

                    anim: ['amb@lo_res_idles@', 'world_human_picnic_female_lo_res_base'],

                    flag: 1,
                },
                {
                    model: 'A_M_M_Beach_02',
                    pos: { x: -1656.8572, y: 238.2857, z: 62.3898, heading: -155 },

                    anim: ['amb@world_human_leaning@male@wall@back@legs_crossed@base', 'base'],
                    flag: 1,
                },
                {
                    model: 'A_F_Y_Tennis_01',
                    pos: { x: -1629.4022, y: 258.7912, z: 59.5421, heading: -155 },

                    anim: ['amb@lo_res_idles@', 'world_human_picnic_female_lo_res_base'],
                    flag: 1,
                },
                {
                    model: 'A_M_M_Skidrow_01',
                    pos: { x: -1629.4154, y: 227.1824, z: 60.8564, heading: -25 },

                    anim: ['amb@world_human_leaning@male@wall@back@foot_up@idle_b', 'idle_d'],
                    flag: 1,
                },
                {
                    model: 'MP_F_CHBar_01',
                    pos: { x: -1632.5802, y: 189.7714, z: 60.7216, heading: -59.5276 },

                    anim: ['timetable@jimmy@mics3_ig_15@', 'idle_a_jimmy'],
                    flag: 1,
                },
                {
                    model: 'S_F_M_SweatShop_01',
                    pos: { x: -1598.7296, y: 223.7802, z: 59.104, heading: 17.0079 },

                    anim: ['rcmjosh1', 'idle'],
                    flag: 1,
                },
            ],
            blips: [
                {
                    sprite: 9,
                    pos: { x: -1634.3209, y: 221.1033, z: 60.8733 },
                    scale: 1,
                    options: {
                        radius: 50,
                        alpha: 100,
                        color: 5
                    },
                }
            ],

            hint: {
                title: 'fun.university.minigames.medicVaccinations.hints.1.title',
                desc: 'fun.university.minigames.medicVaccinations.hints.1.desc',
                timeout: 25000,
            },
            timerText: 'fun.university.minigames.medicVaccinations.timerBar',

            ambientMusic: {
                path: 'university.medicVaccinations',
                options: {
                    volume: 35,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            }
        },
        medicRescue: { // Реанимация
            teleports: {
                start: { x: -1692.8308, y: 194.9802, z: 63.8389, heading: 153 },
                end: { x: -1652.1758, y: 156.4352, z: 70.5619, heading: 22 },
            },

            duration: 600, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 60, // Сколько надо потратить минимально времени на прохождение в секундах

            questions: [
                {
                    title: 'fun.university.minigames.medicRescue.makeSelect',
                    text: 'fun.university.minigames.medicRescue.questions.1.text',
                    buttons: [{ text: 'fun.university.minigames.medicRescue.questions.1.buttons.1', correct: true }, { text: 'fun.university.minigames.medicRescue.questions.1.buttons.2' }]
                },
                {
                    title: 'fun.university.minigames.medicRescue.makeSelect',
                    text: 'fun.university.minigames.medicRescue.questions.2.text',
                    buttons: [{ text: 'fun.university.minigames.medicRescue.questions.2.buttons.1' }, { text: 'fun.university.minigames.medicRescue.questions.2.buttons.2', correct: true }]
                },
                {
                    title: 'fun.university.minigames.medicRescue.makeSelect',
                    text: 'fun.university.minigames.medicRescue.questions.3.text',
                    buttons: [{ text: 'fun.university.minigames.medicRescue.questions.3.buttons.1' }, { text: 'fun.university.minigames.medicRescue.questions.3.buttons.2', correct: true }]
                },
                {
                    title: 'fun.university.minigames.medicRescue.makeSelect',
                    text: 'fun.university.minigames.medicRescue.questions.4.text',
                    buttons: [{ text: 'fun.university.minigames.medicRescue.questions.4.buttons.1', correct: true }, { text: 'fun.university.minigames.medicRescue.questions.4.buttons.2' }]
                },
                {
                    title: 'fun.university.minigames.medicRescue.makeSelect',
                    text: 'fun.university.minigames.medicRescue.questions.5.text',
                    buttons: [{ text: 'fun.university.minigames.medicRescue.questions.5.buttons.1', correct: true }, { text: 'fun.university.minigames.medicRescue.questions.5.buttons.2' }]
                },
                {
                    title: 'fun.university.minigames.medicRescue.makeSelect',
                    text: 'fun.university.minigames.medicRescue.questions.6.text',
                    buttons: [{ text: 'fun.university.minigames.medicRescue.questions.6.buttons.1' }, { text: 'fun.university.minigames.medicRescue.questions.6.buttons.2', correct: true }]
                },
                {
                    title: 'fun.university.minigames.medicRescue.makeSelect',
                    text: 'fun.university.minigames.medicRescue.questions.7.text',
                    buttons: [{ text: 'fun.university.minigames.medicRescue.questions.7.buttons.1' }, { text: 'fun.university.minigames.medicRescue.questions.7.buttons.2', correct: true }]
                },
                {
                    title: 'fun.university.minigames.medicRescue.makeSelect',
                    text: 'fun.university.minigames.medicRescue.questions.8.text',
                    buttons: [{ text: 'fun.university.minigames.medicRescue.questions.8.buttons.1', correct: true }, { text: 'fun.university.minigames.medicRescue.questions.8.buttons.2' }]
                },
                {
                    title: 'fun.university.minigames.medicRescue.makeSelect',
                    text: 'fun.university.minigames.medicRescue.questions.9.text',
                    buttons: [{ text: 'fun.university.minigames.medicRescue.questions.9.buttons.1', correct: true }, { text: 'fun.university.minigames.medicRescue.questions.9.buttons.2' }]
                },
                {
                    title: 'fun.university.minigames.medicRescue.makeSelect',
                    text: 'fun.university.minigames.medicRescue.questions.10.text',
                    buttons: [{ text: 'fun.university.minigames.medicRescue.questions.10.buttons.1' }, { text: 'fun.university.minigames.medicRescue.questions.10.buttons.2', correct: true }]
                },
            ],

            peds: [
                {
                    model: 'A_F_Y_CarClub_01',
                    pos: { x: -1734.8572, y: 201.9956, z: 64.3612, heading: -53 },

                    anim: ['random@mugging4', 'flee_backward_loop_shopkeeper'],
                    flag: 1,
                },
                {
                    model: 'A_M_M_Hillbilly_02',
                    pos: { x: -1787.0637, y: 168.9758, z: 64.7656, heading: -150 },

                    anim: ['dead', 'dead_d'],
                    flag: 1,
                },
                {
                    model: 'A_M_Y_Beach_03',
                    pos: { x: -1755.6791, y: 185.8154, z: 64.4286, heading: 8.5 },

                    anim: ['combat@damage@writheidle_c', 'writhe_idle_g'],
                    flag: 1,
                },
                {
                    model: 'A_F_Y_Beach_01',
                    pos: { x: -1767.3759, y: 162.5407, z: 64.3612, heading: -138 },

                    anim: ['missmic2leadinmic_2_intleadout', 'ko_on_floor_idle'],
                    flag: 1,
                },
                {
                    model: 'A_M_M_StLat_02',
                    pos: { x: -1712.1890, y: 161.3539, z: 64.3949, heading: -144 },

                    anim: ['combat@damage@rb_writhe', 'rb_writhe_loop'],
                    flag: 1,
                },
                {
                    model: 'A_M_Y_BreakDance_01',
                    pos: { x: -1750.9846, y: 135.9956, z: 64.3612, heading: 25 },

                    anim: ['misschinese2_crystalmaze', '2int_loop_a_taocheng'],
                    flag: 1,
                },
                {
                    model: 'IG_RoccoPelosi',
                    pos: { x: -1710.567, y: 183.9429, z: 64.2095, heading: 34.189 },

                    anim: ['missfbi5ig_0', 'lyinginpain_loop_steve'],
                    flag: 1,
                },
                {
                    model: 'U_M_Y_StagGrm_01',
                    pos: { x: -1775.2087, y: 199.6352, z: 64.3612, heading: -121.8898 },

                    anim: ['combat@damage@writheidle_c', 'writhe_idle_g'],
                    flag: 1,
                },
                {
                    model: 'S_F_Y_Hooker_03',
                    pos: { x: -1711.4242, y: 138.9758, z: 64.3612, heading: -141.7323 },

                    anim: ['combat@damage@writheidle_c', 'writhe_idle_g'],
                    flag: 1,
                },
                {
                    model: 'S_F_Y_Hooker_01',
                    pos: { x: -1747.2395, y: 113.7626, z: 64.3612, heading: -39.685 },

                    anim: ['missmic2leadinmic_2_intleadout', 'ko_on_floor_idle'],
                    flag: 1,
                },
            ],

            blips: [
                {
                    sprite: 9,
                    pos: { x: -1743.4550, y: 162.6989, z: 64.3612 },
                    scale: 1,
                    options: {
                        radius: 50,
                        alpha: 100,
                        color: 5
                    },
                }
            ],

            hint: {
                title: 'fun.university.minigames.medicRescue.hints.1.title',
                desc: 'fun.university.minigames.medicRescue.hints.1.desc',
                timeout: 25000,
            },
            timerText: 'fun.university.minigames.medicRescue.timerBar',
            ambientMusic: {
                path: 'university.medicRescue',
                options: {
                    volume: 35,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            }
        },
        breakDoor: { // Взлом замка
            teleports: {
                start: { x: -1659.6923, y: 183.5473, z: 70.5619, heading: -155 },
                end: { x: -1657.0286, y: 188.3473, z: 70.5619, heading: -155 },
            },

            duration: 420, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 5, // Сколько надо потратить минимально времени на прохождение в секундах

            locksAmount: 5,
            countComplete: 2,

            hint: {
                title: 'fun.university.minigames.breakDoor.hints.1.title',
                desc: 'fun.university.minigames.breakDoor.hints.1.desc',
                timeout: 15000,
            },
            timerText: 'fun.university.minigames.breakDoor.timerBar'
        },
        hijackIgnitionMaze: { // Взлом двигателя угонка 1 уровень
            teleports: {
                start: { x: 1076.1626, y: -1978.9187, z: 31.4703, heading: 144 },
                end: { x: -1654.1274, y: 189.4549, z: 70.5619, heading: 22 },
            },

            duration: 420, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 3, // Сколько надо потратить минимально времени на прохождение в секундах

            countComplete: 3,

            timerText: 'fun.university.minigames.hijackIgnitionMaze.timerBar'
        },
        hijackIgnitionSingleLine: { // Взлом двигателя угонка 3 уровень
            teleports: {
                start: { x: 1081.2263, y: -1996.2461, z: 30.9312, heading: -119 },
                end: { x: -1657.0286, y: 188.3473, z: 70.5619, heading: -155 },
            },

            duration: 300, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 3, // Сколько надо потратить минимально времени на прохождение в секундах

            countComplete: 2,

            timerText: 'fun.university.minigames.hijackIgnitionSingleLine.timerBar'
        },
        hijackIgnitionRememberNumber: { // Взлом двигателя угонка N уровень
            teleports: {
                start: { x: -1667.5385, y: 181.9516, z: 70.5619, heading: -65 },
                end: { x: -1667.1165, y: 185.657, z: 70.5619, heading: -141 },
            },

            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 3, // Сколько надо потратить минимально времени на прохождение в секундах

            countComplete: 1,

            timerText: 'fun.university.minigames.hijackIgnitionRememberNumber.timerBar'
        },
        hijackIgnitionCatchBall: { // Взлом двигателя угонка N уровень
            teleports: {
                start: { x: -1667.5385, y: 181.9516, z: 70.5619, heading: -65 },
                end: { x: -1667.1165, y: 185.657, z: 70.5619, heading: -141 },
            },

            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 3, // Сколько надо потратить минимально времени на прохождение в секундах

            countComplete: 1,

            timerText: 'fun.university.minigames.hijackIgnitionCatchBall.timerBar'
        },
        danceBattle: { // DanceBattle
            // teleports: {
            //     start: { x: 1079.0637, y: -1981.6615, z: 31.4703, heading: 144 },
            //     end: { x: -1654.1274, y: 189.4549, z: 70.5619, heading: 22 },
            // },

            duration: 600, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 20, // Сколько надо потратить минимально времени на прохождение в секундах

            hint: {
                title: 'fun.university.minigames.danceBattle.hints.1.title',
                desc: 'fun.university.minigames.danceBattle.hints.1.desc',
                timeout: 15000,
            },
            musicId: 40,
        },
        floorWasher: { // Мыть полы
            teleports: {
                start: { x: -1656.1318, y: 192.1582, z: 66.6022, heading: 167 },
                end: { x: -1657.1868, y: 184.4967, z: 66.6022, heading: -158 },
            },

            duration: 60 * 7, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 20, // Сколько надо потратить минимально времени на прохождение в секундах

            points: [
                { x: -1655.2087, y: 190.5363, z: 66.6022, heading: -136 },
                { x: -1661.6439, y: 189.7451, z: 66.6022, heading: 39 },
                { x: -1658.4000, y: 183.1253, z: 66.6022, heading: 153 },
                { x: -1665.4022, y: 176.3604, z: 66.6022, heading: 110.5512 },
                { x: -1655.5516, y: 180.8044, z: 66.6022, heading: -70.8661 },
                { x: -1646.8879, y: 184.3517, z: 66.6022, heading: 102.0473 },
            ],
            pointsCountRandom: [5, 6],

            hint: {
                title: 'fun.university.minigames.floorWasher.hints.1.title',
                desc: 'fun.university.minigames.floorWasher.hints.1.desc',
                timeout: 15000,
            },
            timerText: 'fun.university.minigames.floorWasher.timerBar',
            ambientMusic: {
                path: 'university.floorWasher',
                options: {
                    volume: 35,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            }
        },
        farmer: { // Собрать цветочки
            teleports: {
                start: { x: -1618.1011, y: 217.5560, z: 59.9634, heading: -62 },
                end: { x: -1657.1868, y: 184.4967, z: 66.6022, heading: -158 },
            },

            duration: 60 * 10, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 20, // Сколько надо потратить минимально времени на прохождение в секундах

            points: [
                { x: -1642.1670, y: 152.2418, z: 61.8674, heading: 25, flowerObject: 'prop_plant_01b' },
                { x: -1644.5934, y: 151.0154, z: 62.0190, heading: 28, flowerObject: 'prop_plant_fern_01b' },
                { x: -1605.2704, y: 207.6923, z: 59.4242, heading: -56, flowerObject: 'prop_plant_fern_01a' },
                { x: -1603.3055, y: 203.1297, z: 59.3568, heading: -56, flowerObject: 'prop_plant_01b' },
                { x: -1595.5912, y: 186.9494, z: 59.1714, heading: -53, flowerObject: 'prop_plant_fern_01b' },
                { x: -1556.9539, y: 229.7143, z: 59.5084, heading: 119.0551, flowerObject: 'prop_plant_fern_01a' },
                { x: -1596.8704, y: 222.9363, z: 59.0198, heading: -153, flowerObject: 'prop_plant_fern_01b' },
                { x: -1594.2990, y: 223.9780, z: 58.9355, heading: -153, flowerObject: 'prop_plant_01b' },
            ],
            pointsCountRandom: [5, 8],

            hint: {
                title: 'fun.university.minigames.farmer.hints.1.title',
                desc: 'fun.university.minigames.farmer.hints.1.desc',
                timeout: 20000,
            },
            timerText: 'fun.university.minigames.farmer.timerBar',

            ambientMusic: {
                path: 'university.farmer',
                options: {
                    volume: 35,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            }
        },
        makeBurgers: { // Приготовление бургеров
            teleports: {
                start: { x: -1658.7032, y: 191.4198, z: 66.6022, heading: 119 },
                end: { x: -1657.1868, y: 184.4967, z: 66.6022, heading: -158 },
            },

            duration: 60 * 5, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 20, // Сколько надо потратить минимально времени на прохождение в секундах
            ambientMusic: {
                path: 'university.makeBurgers',
                options: {
                    volume: 35,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            }
        },
        dessertMaster: {
            teleports: {
                start: { x: -1658.7032, y: 191.4198, z: 66.6022, heading: 119 },
                end: { x: -1657.1868, y: 184.4967, z: 66.6022, heading: -158 },
            },

            duration: 60 * 10, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 20, // Сколько надо потратить минимально времени на прохождение в секундах
            ambientMusic: {
                path: 'university.makeBurgers',
                options: {
                    volume: 35,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            }
        },
        bookGame: {
            teleports: {
                start: { x: -1562.2682, y: 221.3830, z: 63.6503, heading: 17 },
                end: { x: -1595.7362, y: 209.3934, z: 59.9802, heading: 110 },
            },

            // duration: 60 * 10, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 20, // Сколько надо потратить минимально времени на прохождение в секундах
        },
        // GeographyMap game
        geographyMapGame_1: {
            teleports: {
                start: { x: -1655.0769, y: 193.5429, z: 61.7495, heading: -62.3622 },
                end: { x: -1659.3890, y: 187.2132, z: 61.7495, heading: -155 },
            },
            // duration: 60 * 10, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 3, // Сколько надо потратить минимально времени на прохождение в секундах
        },
        geographyMapGame_2: {
            teleports: {
                start: { x: -1655.0769, y: 193.5429, z: 61.7495, heading: -62.3622 },
                end: { x: -1659.3890, y: 187.2132, z: 61.7495, heading: -155 },
            },
            // duration: 60 * 10, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 3, // Сколько надо потратить минимально времени на прохождение в секундах
        },
        geographyMapGame_3: {
            teleports: {
                start: { x: -1655.0769, y: 193.5429, z: 61.7495, heading: -62.3622 },
                end: { x: -1659.3890, y: 187.2132, z: 61.7495, heading: -155 },
            },
            // duration: 60 * 10, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 3, // Сколько надо потратить минимально времени на прохождение в секундах
        },
        geographyMapGame_4: {
            teleports: {
                start: { x: -1655.0769, y: 193.5429, z: 61.7495, heading: -62.3622 },
                end: { x: -1659.3890, y: 187.2132, z: 61.7495, heading: -155 },
            },
            // duration: 60 * 10, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 3, // Сколько надо потратить минимально времени на прохождение в секундах
        },
        geographyMapGame_5: {
            teleports: {
                start: { x: -1655.0769, y: 193.5429, z: 61.7495, heading: -62.3622 },
                end: { x: -1659.3890, y: 187.2132, z: 61.7495, heading: -155 },
            },
            // duration: 60 * 10, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 5, // Сколько надо потратить минимально времени на прохождение в секундах
        },
        geographyMapGame_6: {
            teleports: {
                start: { x: -1655.0769, y: 193.5429, z: 61.7495, heading: -62.3622 },
                end: { x: -1659.3890, y: 187.2132, z: 61.7495, heading: -155 },
            },
            // duration: 60 * 10, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 5, // Сколько надо потратить минимально времени на прохождение в секундах
        },
        tirGame: {
            teleports: {
                start: { x: 484.5363, y: -1009.1208, z: 30.6952, heading: 90 },
                end: { x: -1667.1165, y: 185.657, z: 70.5619, heading: -141 },
            },

            ignoreTeleport: true,
            ignoreWeapon: true,

            minCompleteTime: 10,
            failCooldown: 3, // Время кд в минутах при неудаче

            fade: 500,
            weapon: 'weapon_gusenberg',
            ammo: 250,

            gameTime: 240, // Время на игру в секундах
            targetTargetsAmount: 85, // Суммарное количество мишеней
            completeScore: 70, // Количество поинтов, которое будет считаться за выполненное задание университета
            targetsTogether: 3, // ?
            maxTargetsOnce: 6, // В данный момент не используется, взято из прошлых конфигов
            minTranslationTime: 1500,
            maxTranslationTime: 4500,
            startTargets: 3, // В данный момент не используется, взято из прошлых конфигов

            bulletsAmountMax: 85, // Количество доступных выстрелов на миссию

            targetPositions: [
                {
                    from: { x: 472.373535, y: -1015.26648, z: 30.007513 },
                    to: { x: 472.373535, y: -1013.35638, z: 31.4599934 },
                },
                {
                    from: { x: 472.4347, y: -1015.25793, z: 31.5176029 },
                    to: { x: 472.4347, y: -1015.25793, z: 30.0127029 },
                },
                {
                    from: { x: 472.404358, y: -1003.50854, z: 31.5324841 },
                    to: { x: 472.404358, y: -1006.09589, z: 30.0074444 },
                },
                {
                    from: { x: 472.37323, y: -1005.6792, z: 31.5164261 },
                    to: { x: 472.37323, y: -1005.6792, z: 30.02599 },
                },
                {
                    from: { x: 472.367523, y: -1003.6203, z: 30.0320263 },
                    to: { x: 472.367523, y: -1008.20709, z: 30.0320263 },
                },
                {
                    from: { x: 472.381683, y: -1005.75323, z: 30.0461788 },
                    to: { x: 472.381683, y: -1008.432, z: 31.54108 },
                },
                {
                    from: { x: 472.348755, y: -1006.37531, z: 30.0156422 },
                    to: { x: 472.348755, y: -1009.60583, z: 31.4710712 },
                },
                {
                    from: { x: 472.379, y: -1007.11755, z: 30.0254269 },
                    to: { x: 472.379, y: -1010.46173, z: 31.4928379 },
                },
                {
                    from: { x: 472.305176, y: -1006.37567, z: 31.4763012 },
                    to: { x: 472.305176, y: -1012.50854, z: 31.4763012 },
                },
                {
                    from: { x: 472.348755, y: -1007.86151, z: 29.9987564 },
                    to: { x: 472.348755, y: -1003.63806, z: 30.8589649 },
                },
                {
                    from: { x: 472.392944, y: -1008.76019, z: 29.9979515 },
                    to: { x: 472.392944, y: -1012.08032, z: 31.4170532 },
                },
                {
                    from: { x: 472.359222, y: -1009.5885, z: 30.004055 },
                    to: { x: 472.359222, y: -1014.69995, z: 30.7580223 },
                },
                {
                    from: { x: 472.403625, y: -1010.42065, z: 30.0008755 },
                    to: { x: 472.403625, y: -1004.67218, z: 30.0008755 },
                },
                {
                    from: { x: 472.37735, y: -1011.04358, z: 31.48962 },
                    to: { x: 472.37735, y: -1004.3924, z: 31.48962 },
                },
                {
                    from: { x: 472.412964, y: -1011.71625, z: 31.381834 },
                    to: { x: 472.412964, y: -1014.98541, z: 30.62252 },
                },
                {
                    from: { x: 472.3976, y: -1012.89703, z: 30.0041656 },
                    to: { x: 472.3976, y: -1005.01166, z: 31.4989376 },
                },
                {
                    from: { x: 472.420715, y: -1013.924, z: 30.0425625 },
                    to: { x: 472.420715, y: -1008.03223, z: 31.3989353 },
                },
            ],
            hint: {
                title: 'fun.university.minigames.tirGame.hints.1.title',
                desc: 'fun.university.minigames.tirGame.hints.1.desc',
                timeout: 20000,
            },
            onShootCrossNotify: 'fun.university.minigames.tirGame.onShootCrossNotify',
            targetProp: 'gr_prop_gr_target_w_02b',
            targetPropCrossed: 'gr_prop_gr_target_02b',
            ambientMusic: {
                path: 'university.tirGame',
                options: {
                    volume: 35,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            }

        },
        investigation: {
            teleports: {
                start: { x: -1643.7362, y: 224.9538, z: 60.6373, heading: -65 },
                end: { x: -1667.1165, y: 185.657, z: 70.5619, heading: -141 },
            },

            duration: 600, // Время на прохождение в секундах
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 5,

            timecycle: 'lab_none_dark_fog',

            clues: [
                {
                    prop: 'ch_prop_collectibles_garbage_01a',
                    position: new mp.Vector3(-1575.714, 252.303253, 57.931427),
                    rotation: new mp.Vector3(0, 0, 50),
                },
                {
                    prop: 'prop_cs_panties',
                    position: new mp.Vector3(-1640.85693, 226.017426, 60.8479233),
                    rotation: new mp.Vector3(0, 0, -15)
                },
                {
                    prop: 'v_ilev_mp_bedsidebook',
                    position: new mp.Vector3(-1631.962, 226.327667, 59.77263),
                    rotation: new mp.Vector3(0, 0, 50)
                },
                {
                    prop: 'ch_prop_collectibles_limb_01a',
                    position: new mp.Vector3(-1609.591, 216.408279, 58.49903),
                    rotation: new mp.Vector3(0, 0, 50)
                },
                {
                    prop: 'v_res_d_ramskull',
                    position: new mp.Vector3(-1617.55554, 244.999329, 58.74051),
                    rotation: new mp.Vector3(-19.29, 5.38, 35.92)
                },
                {
                    prop: 'prop_ld_fireaxe',
                    position: new mp.Vector3(-1596.21716, 247.576569, 58.9925423),
                    rotation: new mp.Vector3(11.31, -43.40, -141.66)
                },
                {
                    prop: 'ch_prop_ch_serialkiller_01a',
                    position: new mp.Vector3(-1638.86792, 235.068268, 62.103157),
                    rotation: new mp.Vector3(-1.42, 6.97, 24.58)
                },
                {
                    prop: 'h4_prop_h4_photo_fire_01b',
                    position: new mp.Vector3(-1598.078, 223.396851, 60.0282326),
                    rotation: new mp.Vector3(90, 196.62, 0)
                },
                {
                    prop: 'm23_2_prop_m32_bolt_cutter_01a',
                    position: new mp.Vector3(-1582.58826, 237.27594, 58.4138145),
                    rotation: new mp.Vector3(57.27, 1, 24.51)
                },
            ],
            maxClues: 6,

            time: 23, // Локальное время для игрока (в часах)
            weapon: {
                name: 'weapon_flashlight',
                ammo: 0
            },
            hint: {
                title: 'fun.university.minigames.investigation.hints.1.title',
                desc: 'fun.university.minigames.investigation.hints.1.desc',
                timeout: 20000,
            },
            timerText: 'fun.university.minigames.investigation.timerBar',
            findZone: {
                id: 9,
                position: new mp.Vector3(-1607.2483, 242.0571, 59.3905),
                options: {
                    radius: 37,
                    alpha: 100,
                    color: 5,
                    dimension: -1
                }
            },
            ambientMusic: {
                path: 'university.investigation',
                options: {
                    volume: 31,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            },
            enableBindings: ['phone']
        },
        parachuteJumps: {
            teleports: {
                start: { x: -1539.4418, y: -101.3802, z: 1075.7898, heading: 31.5669 },
                end: { x: -1772.3473, y: 191.7231, z: 64.3612, heading: 147.4016 },
            },
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 5, // Сколько надо потратить минимально времени на прохождение в секундах

            weapon: {
                name: 'gadget_parachute',
                ammo: 1
            },
            hint: {
                title: 'fun.university.minigames.parachuteJumps.hints.1.title',
                desc: 'fun.university.minigames.parachuteJumps.hints.1.desc',
                timeout: 20000,
            },
            ambientMusic: {
                path: 'university.parachuteJumps',
                options: {
                    volume: 15,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            },
            points: [
                { x: -1580.9143, y: -50.1626, z: 950.9312, heading: -144.5669, radius: 15, marker: [{ type: 6, scale: 15, options: { dimension: -1, color: [235, 183, 52, 155] } }, { type: 40, scale: 5, options: { dimension: -1, color: [245, 240, 240, 155] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1643.6835, y: 68.1758, z: 851.4497, heading: -153.0709, radius: 15, marker: [{ type: 6, scale: 15, options: { dimension: -1, color: [235, 183, 52, 155] } }, { type: 40, scale: 5, options: { dimension: -1, color: [245, 240, 240, 155] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1651.8594, y: 167.0242, z: 751.5642, heading: 175.748, radius: 15, marker: [{ type: 6, scale: 15, options: { dimension: -1, color: [235, 183, 52, 155] } }, { type: 40, scale: 5, options: { dimension: -1, color: [245, 240, 240, 155] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1619.3011, y: 253.5956, z: 659.2717, heading: -34.4016, radius: 15, marker: [{ type: 6, scale: 15, options: { dimension: -1, color: [235, 183, 52, 155] } }, { type: 40, scale: 5, options: { dimension: -1, color: [245, 240, 240, 155] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1549.2527, y: 366.3692, z: 552.8374, heading: 167.2441, radius: 15, marker: [{ type: 6, scale: 15, options: { dimension: -1, color: [235, 183, 52, 155] } }, { type: 40, scale: 5, options: { dimension: -1, color: [245, 240, 240, 155] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1610.3341, y: 471.6791, z: 452.4629, heading: -136.063, radius: 15, marker: [{ type: 6, scale: 15, options: { dimension: -1, color: [235, 183, 52, 155] } }, { type: 40, scale: 5, options: { dimension: -1, color: [245, 240, 240, 155] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1713.3627, y: 556.6682, z: 351.2798, heading: -138.8976, radius: 15, marker: [{ type: 6, scale: 15, options: { dimension: -1, color: [235, 183, 52, 155] } }, { type: 40, scale: 5, options: { dimension: -1, color: [245, 240, 240, 155] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1796.9934, y: 471.2571, z: 251.7311, heading: -170.0787, radius: 15, marker: [{ type: 6, scale: 15, options: { dimension: -1, color: [235, 183, 52, 155] } }, { type: 40, scale: 5, options: { dimension: -1, color: [245, 240, 240, 155] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1789.2131, y: 319.8198, z: 171.2565, heading: -164.4095, radius: 15, marker: [{ type: 6, scale: 15, options: { dimension: -1, color: [235, 183, 52, 155] } }, { type: 40, scale: 5, options: { dimension: -1, color: [245, 240, 240, 155] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1739.1165, y: 160.1143, z: 64.3612, heading: -138.8976, radius: 23, marker: [{ type: 27, scale: 15, options: { dimension: -1, color: [235, 183, 52, 155] } }, { type: 5, scale: 8, options: { dimension: -1, color: [245, 240, 240, 155] } }], blip: { sprite: 1, options: { dimension: -1 } } },
            ]
        },
        ringFight: {
            teleports: {
                start: { x: -2975.2747, y: 51.4681, z: 12.3458, heading: -153.0709 },
                end: { x: -1772.3473, y: 191.7231, z: 64.3612, heading: 147.4016 },
            },
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 5, // Сколько надо потратить минимально времени на прохождение в секундах

            duration: 60 * 3, // Время на прохождение в секундах
            timerText: 'fun.university.minigames.ringFight.timerBar',
            hint: {
                title: 'fun.university.minigames.ringFight.hints.1.title',
                desc: 'fun.university.minigames.ringFight.hints.1.desc',
                timeout: 20000,
            },
            ambientMusic: {
                path: 'university.ringFight',
                options: {
                    volume: 10,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            },
            peds: [
                {
                    model: 'G_M_Y_Lost_01',
                    pos: { x: -2972.334, y: 50.3604, z: 12.3458, heading: 68.0315 },
                },
                {
                    model: 'G_M_Y_Lost_02',
                    pos: { x: -2973.9297, y: 47.6703, z: 12.3458, heading: 14.1732 },
                },
                {
                    model: 'G_M_Y_Lost_03',
                    pos: { x: -2976.8308, y: 48.5802, z: 12.3458, heading: -31.1811 },
                }
            ]
        },
        stadiumRunning: {
            startTimer: 10, // > 1 players in sec
            waitingPlayersTimer: 30, // < 2 players in sec timer
            hint: {
                title: 'fun.university.minigames.stadiumRunning.hints.1.title',
                desc: 'fun.university.minigames.stadiumRunning.hints.1.desc',
                timeout: 20000,
            },

            timeRunning: 60 * 2, // Время на прохождение в секундах
            startPos: { x: -1777.2263, y: 175.3055, z: 64.3612, heading: 141.7323 },
            points: {
                positions: [
					{ x: -1772.8484, y: 161.8549, z: 64.3612, heading: -144.5669, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1756.589, y: 138.6593, z: 64.3612, heading: -144.5669, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1737.7715, y: 120.356, z: 64.3612, heading: -102.0473, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1709.0505, y: 127.0286, z: 64.3612, heading: -36.8504, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1706.4923, y: 161.7758, z: 64.3612, heading: 34.0157, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1734.4352, y: 195.811, z: 64.3612, heading: 65.1969, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1770.3033, y: 184.2857, z: 64.3612, heading: 155.9055, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1772.8484, y: 161.8549, z: 64.3612, heading: -144.5669, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1756.589, y: 138.6593, z: 64.3612, heading: -144.5669, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1737.7715, y: 120.356, z: 64.3612, heading: -102.0473, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1709.0505, y: 127.0286, z: 64.3612, heading: -36.8504, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1706.4923, y: 161.7758, z: 64.3612, heading: 34.0157, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1734.4352, y: 195.811, z: 64.3612, heading: 65.1969, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1770.3033, y: 184.2857, z: 64.3612, heading: 155.9055, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1772.8484, y: 161.8549, z: 64.3612, heading: -144.5669, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1756.589, y: 138.6593, z: 64.3612, heading: -144.5669, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
					{ x: -1738.022, y: 112.2725, z: 64.3612, heading: -144.5669, radius: 4, marker: [{ type: 6, scale: 7, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 4, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                ],

                players: [
                    {
                        pos: { x: -1778.3341, y: 174.9231, z: 64.3612, heading: -138 },
                    },
                    {
                        pos: { x: -1779.0857, y: 174.4615, z: 64.3612, heading: -138 },
                    },
                    {
                        pos: { x: -1779.745, y: 173.9077, z: 64.3612, heading: -138 },
                    },
                    {
                        pos: { x: -1780.4572, y: 173.4593, z: 64.3612, heading: -138 },
                    },
                    {
                        pos: { x: -1781.156, y: 172.9582, z: 64.3612, heading: -138 },
                    },
                    {
                        pos: { x: -1781.8286, y: 172.4835, z: 64.3612, heading: -138 },
                    },
                    {
                        pos: { x: -1782.567, y: 172.0352, z: 64.3612, heading: -138 },
                    },
                    {
                        pos: { x: -1783.2263, y: 171.5209, z: 64.3612, heading: -138 },
                    },
                ]
            }
        },
        swimShark: {
            teleports: {
                start: { x: -1418.0571, y: 5637.1387, z: 2.4381, heading: -8.5039 },
                end: { x: -1772.3473, y: 191.7231, z: 64.3612, heading: 147.4016 },
            },
            failCooldown: 3, // Время кд в минутах при неудаче
            minCompleteTime: 5, // Сколько надо потратить минимально времени на прохождение в секундах

            hint: {
                title: 'fun.university.minigames.swimShark.hints.1.title',
                desc: 'fun.university.minigames.swimShark.hints.1.desc',
                timeout: 20000,
            },
            ambientMusic: {
                path: 'university.swimShark',
                options: {
                    volume: 31,
                    fade: 2000,
                    waitTime: 1000,
                    looped: false,
                    force: true
                }
            },
            points: [
                { x: -1410.9099, y: 5667.9824, z: -14.8499, heading: 0, radius: 5, setOxygenPercent: 30, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1411.0022, y: 5698.7207, z: -24.5048, heading: -2.8346, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1409.5385, y: 5726.0571, z: -26.0381, heading: -2.8346, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1397.2087, y: 5765.9736, z: -22.7524, heading: -22.6772, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1375.7539, y: 5820.7383, z: -21.2358, heading: -17.0079, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1366.4572, y: 5850.6328, z: -23.4264, heading: -14.1732, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1358.4659, y: 5876.0571, z: -27.1501, heading: -14.1732, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1352.611, y: 5909.9341, z: -39.3158, heading: 28.3465, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1381.6219, y: 5925.1387, z: -56.1487, heading: 82.2047, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1398.2638, y: 5926.312, z: -73.3187, heading: 85.0394, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1419.8638, y: 5925.6792, z: -93.9092, heading: 93.5433, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1434.6725, y: 5923.8198, z: -113.9268, heading: 96.378, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1462.1011, y: 5928.791, z: -121.3575, heading: 62.3622, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1488.8044, y: 5941.5034, z: -130.0183, heading: 79.3701, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1517.2351, y: 5947.9121, z: -136.9436, heading: 85.0394, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1545.877, y: 5939.8022, z: -145.9246, heading: 136.063, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1565.6044, y: 5914.3647, z: -138.2748, heading: 141.7323, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1583.288, y: 5888.1626, z: -129.5465, heading: 144.5669, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1608.3561, y: 5860.4702, z: -128.9568, heading: 127.5591, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1639.5956, y: 5848.2461, z: -134.2476, heading: 102.0473, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1659.2968, y: 5842.958, z: -130.5238, heading: 104.8819, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1672.9583, y: 5839.0815, z: -117.5494, heading: 104.8819, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1688.9539, y: 5833.7935, z: -104.5077, heading: 110.5512, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1701.0461, y: 5829.231, z: -94.701, heading: 110.5512, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1713.2307, y: 5824.4175, z: -84.5238, heading: 110.5512, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1727.3802, y: 5818.2856, z: -74.8857, heading: 113.3858, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1744.6945, y: 5809.3848, z: -64.9106, heading: 116.2205, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1767.7979, y: 5794.9053, z: -51.5319, heading: 121.8898, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1797.0198, y: 5776.8921, z: -32.1208, heading: 119.0551, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1813.8462, y: 5767.0947, z: -17.1414, heading: 119.0551, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 20, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
                { x: -1822.9319, y: 5760.4878, z: -5.4645, heading: 124.7244, radius: 5, setOxygenPercent: 100, marker: [{ type: 6, scale: 4, options: { dimension: -1, color: [0, 149, 255, 55] } }, { type: 4, scale: 2, options: { dimension: -1, color: [255, 255, 255, 115] } }], blip: { sprite: 1, options: { dimension: -1 } } },
            ],

            peds: [
                {
                    model: 'A_C_SharkHammer',
                    center: { x: -1397.2087, y: 5765.9736, z: -22.7524, heading: -22.6772 },
                    radius: 4 // как далеко будет от центра
                },
                {
                    model: 'A_C_Dolphin',
                    center: { x: -1407.3099, y: 5939.644, z: -57.3451, heading: -110.5512 },
                    radius: 6 // как далеко будет от центра
                },
                {
                    model: 'A_C_SharkTiger',
                    center: { x: -1462.1011, y: 5928.791, z: -121.3575, heading: 62.3622 },
                    radius: 7 // как далеко будет от центра
                },
                {
                    model: 'A_C_SharkHammer',
                    center: { x: -1545.877, y: 5939.8022, z: -145.9246, heading: 136.063 },
                    radius: 8 // как далеко будет от центра
                },
                {
                    model: 'A_C_Dolphin',
                    center: { x: -1639.5956, y: 5848.2461, z: -134.2476, heading: 102.0473 },
                    radius: 6 // как далеко будет от центра
                },
                {
                    model: 'A_C_SharkTiger',
                    center: { x: -1688.9539, y: 5833.7935, z: -104.5077, heading: 110.5512 },
                    radius: 8 // как далеко будет от центра
                },
                {
                    model: 'A_C_SharkHammer',
                    center: { x: -1727.3802, y: 5818.2856, z: -74.8857, heading: 113.3858 },
                    radius: 5 // как далеко будет от центра
                },
                {
                    model: 'A_C_SharkTiger',
                    center: { x: -1813.8462, y: 5767.0947, z: -17.1414, heading: 119.0551 },
                    radius: 8 // как далеко будет от центра
                },
            ]
        },
    }
}
