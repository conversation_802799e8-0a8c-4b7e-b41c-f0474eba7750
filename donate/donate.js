module.exports = {
    money: {
        items: [
            { discount: 5,  item: '10k',    price: 100,    value: 10000    },
            { discount: 10, item: '50k',    price: 500,    value: 50000    },
            { discount: 15, item: '200k',   price: 2000,   value: 200000   },
            { discount: 20, item: '500k',   price: 5000,   value: 500000   },
            { discount: 25, item: '2000k',  price: 20000,  value: 2000000  },
            { discount: 30, item: '10000k', price: 100000, value: ******** }
        ]
    },

    account: {
        items: [
            { discount: 50, item: 'nickname',         price: 50   },
            { discount: 50, item: 'person',           price: 500  },
            { discount: 30, item: 'warn',             price: 500  },
            { discount: 50, item: 'customization',    price: 250  },
            // { discount: 50, item: 'vehicleNumber',    price: 5000 },
            { discount: 50, item: 'phoneNumber',      price: 2000 },
            { discount: 90, item: 'promoCode',        price: 50   },
            { discount: 50, item: 'garage',           price: 1000 },
            { discount: 90, item: 'weddingDivorce',   price: 200  },
            { discount: 50, item: 'armyCertificate',  price: 1500 },
            { discount: 50, item: 'criminalRecord',   price: 500  },
        ]
    },

    starter: {
        items: [
            { discount: 10, item: 'bronze',   price: 250  },
            { discount: 15, item: 'gold',     price: 500  },
            { discount: 20, item: 'platinum', price: 1000 }
        ]
    },

    animations: {
        items: [
            { id: 0,      title: 'animations.list.BlindingLights',           animData: ['majestic_animations', 'blinding_lights'],                  flag: 0,   price: 1000,   discount: 35, looped: false,  music: 'blinding_lights' }, // SeasonPass Summer 2022 & donate
            { id: 1,      title: 'animations.list.BoogieDown',               animData: ['majestic_animations', 'boogie_down'],                      flag: 1,   price: 500,    discount: 30, looped: true,   music: 'boogie_down' },
            { id: 2,      title: 'animations.list.KneeSlapper',              animData: ['majestic_animations', 'cowboy_dance'],                     flag: 1,   price: 750,    discount: 40, looped: true,   music: 'knee_slapper' },
            { id: 3,      title: 'animations.list.Crossbounce',              animData: ['majestic_animations', 'crossbounce'],                      flag: 1,   price: 1500,   discount: 30, looped: true,   music: 'crossbounce' },
            { id: 4,      title: 'animations.list.DiscoFever',               animData: ['majestic_animations', 'disco_dance'],                      flag: 1,   price: 500,    discount: 35, looped: true,   music: 'disco_fever' },
            { id: 5,      title: 'animations.list.DontStartNow',             animData: ['majestic_animations', 'dont_start_now'],                   flag: 1,   price: 1500,   discount: 30, looped: true,   music: 'dont_start_now' },
            { id: 6,      title: 'animations.list.Floss',                    animData: ['majestic_animations', 'floss_dance'],                      flag: 1,   price: 750,    discount: 35, looped: true,   music: 'floss' },
            { id: 7,      title: 'animations.list.Fresh',                    animData: ['majestic_animations', 'fresh'],                            flag: 1,   price: 500,    discount: 35, looped: true,   music: 'fresh' },
            { id: 8,      title: 'animations.list.GangnamStyle',             animData: ['majestic_animations', 'gangnam_style'],                    flag: 1,   price: 1500,   discount: 30, looped: true,   music: 'gangnam_style' },
            { id: 9,      title: 'animations.list.HeartRizon',               animData: ['majestic_animations', 'i_heart_you'],                      flag: 0,   price: 500,    discount: 25, looped: false },
            { id: 10,     title: 'animations.list.JabbaSwitchway',           animData: ['majestic_animations', 'jabba_switchway'],                  flag: 1,   price: 1500,   discount: 25, looped: true,   music: 'jabba_switchway' },
            { id: 11,     title: 'animations.list.TheMacarena',              animData: ['majestic_animations', 'macarena'],                         flag: 1,   price: 1500,   discount: 30, looped: true,   music: 'macarena' },
            { id: 12,     title: 'animations.list.LastForever',              animData: ['majestic_animations', 'last_forever'],                     flag: 1,   price: 1750,   discount: 10, looped: true,   music: 'last_forever' },
            { id: 13,     title: 'animations.list.RideThePony',              animData: ['majestic_animations', 'ridethepony_v2'],                   flag: 1,   price: 500,    discount: 35, looped: true,   music: 'ride_the_pony' },
            { id: 14,     title: 'animations.list.Rollie',                   animData: ['majestic_animations', 'rollie'],                           flag: 1,   price: 1250,   discount: 15, looped: true,   music: 'rollie' },
            { id: 15,     title: 'animations.list.SaySo',                    animData: ['majestic_animations', 'say_so'],                           flag: 1,   price: 1500,   discount: 30, looped: true,   music: 'say_so' },
            { id: 16,     title: 'animations.list.SignatureShuffle',         animData: ['majestic_animations', 'shuffle2'],                         flag: 1,   price: 1500,   discount: 35, looped: true,   music: 'signature_shuffle' },
            { id: 17,     title: 'animations.list.SquatKick',                animData: ['majestic_animations', 'squat_kick'],                       flag: 1,   price: 1500,   discount: 25, looped: true,   music: 'squat_kick' },
            { id: 18,     title: 'animations.list.StepItUp',                 animData: ['majestic_animations', 'step_it_up'],                       flag: 0,   price: 500,    discount: 30, looped: false,  music: 'step_it_up' },
            { id: 19,     title: 'animations.list.TheFlow',                  animData: ['majestic_animations', 'the_flow'],                         flag: 1,   price: 500,    discount: 40, looped: true,   music: 'the_flow' },
            { id: 20,     title: 'animations.list.TheRenegade',              animData: ['majestic_animations_2', 'renegade'],                       flag: 1,   price: 1500,   discount: 40, looped: true,   music: 'the_renegade' },
            { id: 21,     title: 'animations.list.Stuck',                    animData: ['majestic_animations_2', 'stuck'],                          flag: 1,   price: 1250,   discount: 15, looped: true,   music: 'stuck' },
            { id: 22,     title: 'animations.list.PumpUpTheJam',             animData: ['majestic_animations_2', 'pump_up'],                        flag: 1,   price: 1500,   discount: 40, looped: true,   music: 'pump_up' },
            // {id: 23,   title: 'animations.list.Socks',                    animData: ['majestic_animations_2', 'socks'],                          flag: 1,   price: 1500,  looped: true,   music: 'socks'}, // SeasonPass Summer 2022
            { id: 24,     title: 'animations.list.MyWorld',                  animData: ['majestic_animations_2', 'my_world'],                       flag: 1,   price: 1750,   discount: 10, looped: true,   music: 'my_world' },
            { id: 25,     title: 'animations.list.WakeUp',                   animData: ['majestic_animations_2', 'wake_up'],                        flag: 1,   price: 1000,   discount: 40, looped: true,   music: 'wake_up' },
            { id: 26,     title: 'animations.list.OndaOnda',                 animData: ['majestic_animations_2', 'onda'],                           flag: 1,   price: 750,    discount: 40, looped: true,   music: 'onda' },
            { id: 27,     title: 'animations.list.GetGriddy',                animData: ['majestic_animations_2', 'gridy'],                          flag: 1,   price: 1750,   discount: 10, looped: true,   music: 'get_griddy'},
            { id: 28,     title: 'animations.list.HitIt',                    animData: ['majestic_animations_2', 'hit_it'],                         flag: 1,   price: 1500,   discount: 30, looped: true,   music: 'hit_it'},
            // {id: 29,   title: 'animations.list.LeaveTheDoorOpen',         animData: ['majestic_animations_2', 'leave_door_open'],                flag: 1,   price: 1000,  looped: true,   music: 'leave_door_open'}, // SeasonPass Summer 2022
            // {id: 30,   title: 'animations.list.ChickenWingIt',            animData: ['majestic_animations_2', 'chicken_wing'],                   flag: 1,   price: 1500,  looped: true,   music: 'chicken_wing'}, // SeasonPass Summer 2022
            { id: 31,     title: 'animations.list.Savage',                   animData: ['majestic_animations_2', 'savage'],                         flag: 1,   price: 1500,   discount: 30, looped: true,   music: 'savage'},
            { id: 32,     title: 'animations.list.ElectroSwing',             animData: ['majestic_animations_2', 'electro_swing'],                  flag: 1,   price: 1000,   discount: 35, looped: true,   music: 'electro_swing'},
            { id: 33,     title: 'animations.list.Sprinkler',                animData: ['majestic_animations_2', 'sprinkler'],                      flag: 1,   price: 750,    discount: 35, looped: true,   music: 'sprinkler' },
            { id: 34,     title: 'animations.list.Smeeze',                   animData: ['majestic_animations_3', 'smeeze'],                         flag: 1,   price: 1000,   discount: 40, looped: true,   music: 'smeeze'},
            { id: 35,     title: 'animations.list.GoMufasa',                 animData: ['majestic_animations_3', 'mufasa'],                         flag: 1,   price: 1500,   discount: 25, looped: true,   music: 'mufasa'},
            // {id: 36,   title: 'animations.list.HeyNow',                   animData: ['majestic_animations_3', 'hey_now'],                        flag: 1,   price: 1000,  looped: true,   music: 'hey_now'},
            // {id: 37,   title: 'animations.list.BuildUp',                  animData: ['majestic_animations_3', 'build_up'],                       flag: 1,   price: 1500,  looped: true,   music: 'build_up'},
            { id: 38,     title: 'animations.list.TakeTheL',                 animData: ['majestic_animations_3', 'take_the_l'],                     flag: 1,   price: 750,    discount: 15, looped: true,   music: 'take_the_l' },
            // {id: 39,   title: 'animations.list.Breakdown',                animData: ['majestic_animations', 'hip_hop'],                          flag: 1,   price: 1000,  looped: true,   music: 'breakdown'},
            // {id: 40,   title: 'animations.list.IAintAfraid',              animData: ['majestic_animations_3', 'i_aint_afraid'],                  flag: 1,   price: 1000,  looped: true,   music: 'i_aint_afraid'},
            // {id: 41,   title: 'animations.list.GetGone',                  animData: ['majestic_animations_3', 'get_gone'],                       flag: 1,   price: 1500,  looped: true,   music: 'get_gone'},
            // {id: 42,   title: 'animations.list.MaximumBounce',            animData: ['majestic_animations_3', 'maximum_bounce'],                 flag: 1,   price: 1000,  looped: true,   music: 'maximum_bounce'},
            // {id: 43,   title: 'animations.list.ILikeToMoveIt',            animData: ['majestic_animations_3', 'like_to_move'],                   flag: 1,   price: 1500,  looped: true,   music: 'like_to_move'},
            // {id: 44,   title: 'animations.list.LeiltElomr',               animData: ['majestic_animations_3', 'leilt_elomr'],                    flag: 1,   price: 1000,  looped: true,   music: 'leilt_elomr'},
            // {id: 45,   title: 'animations.list.Tidy',                     animData: ['majestic_animations_3', 'tidy'],                           flag: 1,   price: 1000,  looped: true,   music: 'tidy'},
            // {id: 46,   title: 'animations.list.BhangraBoogie',            animData: ['majestic_animations_3', 'bhangra_boogie'],                 flag: 1,   price: 1500,  looped: true,   music: 'bhangra_boogie'},
            // {id: 47,   title: 'animations.list.OutWest',                  animData: ['majestic_animations_3', 'out_west'],                       flag: 1,   price: 1500,  looped: true,   music: 'out_west'},
            // {id: 48,   title: 'animations.list.ToosieSlide',              animData: ['majestic_animations_3', 'toosie_slide'],                   flag: 1,   price: 1500,  looped: true,   music: 'toosie_slide'},
            // {id: 49,   title: 'animations.list.PullUp',                   animData: ['majestic_animations_3', 'pull_up'],                        flag: 1,   price: 1500,  looped: true,   music: 'pull_up'}, // SeasonPass Winter 2022
            // {id: 50,   title: 'animations.list.TheCraneKick',             animData: ['majestic_animations_3', 'the_crane_kick'],                 flag: 0,   price: 1000,  looped: false,  music: 'the_crane_kick'},
            // {id: 51,   title: 'animations.list.BillyBounce',              animData: ['majestic_animations_3', 'billy_bounce'],                   flag: 1,   price: 1000,  looped: true,   music: 'billy_bounce'},
            // {id: 52,   title: 'animations.list.ElectroShuffle',           animData: ['majestic_animations_3', 'electro_shuffle'],                flag: 1,   price: 1000,  looped: true,   music: 'electro_shuffle'}, // SeasonPass Winter 2022
            // {id: 53,   title: 'animations.list.WorkItOut',                animData: ['majestic_animations_4', 'work_it_out'],                    flag: 1,   price: 750,   looped: true,   music: 'work_it_out'}, // SeasonPass Winter 2022
            // {id: 54,   title: 'animations.list.Zany',                     animData: ['majestic_animations_2', 'zany'],                           flag: 1,   price: 750,   looped: true,   music: 'zany'}, // SeasonPass Winter 2022
            // {id: 55,   title: 'animations.list.SmoothMoves',              animData: ['majestic_animations_3', 'smooth_moves'],                   flag: 1,   price: 1500,  looped: true,   music: 'smooth_moves'}, // SeasonPass Winter 2022
            // {id: 56,   title: 'animations.list.Vivacious',                animData: ['majestic_animations_4', 'vivacious'],                      flag: 1,   price: 500,   looped: true,   music: 'vivacious'}, // SeasonPass Winter 2022
            // {id: 57,   title: 'animations.list.Hula',                     animData: ['majestic_animations_4', 'hula'],                           flag: 1,   price: 500,   looped: true,   music: 'hula'},
            // {id: 58,   title: 'animations.list.TrueHeart',                animData: ['majestic_animations_4', 'true_heart'],                     flag: 1,   price: 500,   looped: true,   music: 'true_heart'},
            // {id: 59,   title: 'animations.list.Reanimated',               animData: ['majestic_animations_4', 'reanimated'],                     flag: 1,   price: 500,   looped: true,   music: 'reanimated'}, // Halloween 2022
            // {id: 60,   title: 'animations.list.InDaParty',                animData: ['majestic_animations_4', 'in_da_party'],                    flag: 1,   price: 1500,  looped: true,   music: 'in_da_party'},
            // {id: 61,   title: 'animations.list.BimBamBoom',               animData: ['majestic_animations_4', 'bim_bam_boom'],                   flag: 1,   price: 1500,  looped: true,   music: 'bim_bam_boom'},
            // {id: 62,   title: 'animations.list.WannaSeeMe',               animData: ['majestic_animations_4', 'wanna_see_me'],                   flag: 1,   price: 1000,  looped: true,   music: 'wanna_see_me'},
            // {id: 63,   title: 'animations.list.DynamicShuffle',           animData: ['majestic_animations_4', 'dynamic_shuffle'],                flag: 1,   price: 1000,  looped: true,   music: 'dynamic_shuffle'},
            // {id: 64,   title: 'animations.list.NeverGonna',               animData: ['majestic_animations_4', 'never_gonna'],                    flag: 1,   price: 1500,  looped: true,   music: 'never_gonna'},
            // {id: 65,   title: 'animations.list.FrightFunk',               animData: ['majestic_animations_4', 'fright_funk'],                    flag: 1,   price: 750,   looped: true,   music: 'fright_funk'}, // Halloween 2022
            // {id: 66,   title: 'animations.list.Jitterbug',                animData: ['majestic_animations_4', 'jitterbug'],                      flag: 1,   price: 750,   looped: true,   music: 'jitterbug'},
            { id: 67,     title: 'animations.list.Infectious',               animData: ['majestic_animations_4', 'infectious'],                     flag: 1,   price: 1500,   discount: 20, looped: true,   music: 'infectious' },
            // {id: 68,   title: 'animations.list.WhereIsMatt',              animData: ['majestic_animations_4', 'where_is_matt'],                  flag: 1,   price: 500,   looped: true,   music: 'where_is_matt'},
            // {id: 69,   title: 'animations.list.SavorTheW',                animData: ['majestic_animations_4', 'savor_the_w'],                    flag: 1,   price: 500,   looped: true,   music: 'savor_the_w'}, // SeasonPass Winter 2022
            // {id: 70,   title: 'animations.list.DanceTherapy',             animData: ['majestic_animations_5', 'dance_therapy'],                  flag: 1,   price: 1500,  looped: true,   music: 'dance_therapy'},
            // {id: 71,   title: 'animations.list.Intensity',                animData: ['majestic_animations_5', 'intensity'],                      flag: 1,   price: 1500,  looped: true,   music: 'intensity'},
            // {id: 72,   title: 'animations.list.RushinAround',             animData: ['majestic_animations_5', 'rushin_around'],                  flag: 1,   price: 1500,  looped: true,   music: 'rushin_around'},
            // {id: 73,   title: 'animations.list.AdvancedMath',             animData: ['majestic_animations_5', 'advanced_math'],                  flag: 1,   price: 500,   looped: true,   music: 'advanced_math'},
            // {id: 74,   title: 'animations.list.BoldStance',               animData: ['majestic_animations_5', 'bold_stance'],                    flag: 1,   price: 1000,  looped: true,   music: 'bold_stance'},
            // {id: 75,   title: 'animations.list.Freemix',                  animData: ['majestic_animations_5', 'freemix'],                        flag: 1,   price: 1000,  looped: true,   music: 'freemix'}, // SeasonPass Winter 2022
            // {id: 76,   title: 'animations.list.Extraterrestrial',         animData: ['majestic_animations_5', 'extraterrestrial'],               flag: 1,   price: 750,   looped: true,   music: 'extraterrestrial'},
            // {id: 77,   title: 'animations.list.Crabby',                   animData: ['majestic_animations_5', 'crabby'],                         flag: 1,   price: 1000,  looped: true,   music: 'crabby'},
            // {id: 78,   title: 'animations.list.Lavish',                   animData: ['majestic_animations_5', 'lavish'],                         flag: 1,   price: 750,   looped: true,   music: 'lavish'},
            // {id: 79,   title: 'animations.list.MimeTime',                 animData: ['majestic_animations_5', 'mime_time'],                      flag: 1,   price: 500,   looped: true,   music: 'mime_time'},
            // {id: 80,   title: 'animations.list.TaiChi',                   animData: ['majestic_animations_5', 'tai_chi'],                        flag: 1,   price: 500,   looped: true,   music: 'tai_chi'},
            // {id: 81,   title: 'animations.list.LilBounce',                animData: ['majestic_animations_props', 'hydraulics_player'],          flag: 33,  price: 2000,  looped: true,   music: 'lil_bounce'}, // SeasonPass Summer 2022
            // {id: 82,   title: 'animations.list.Daydream',                 animData: ['majestic_animations_5', 'daydream'],                       flag: 1,   price: 1500,  looped: true,   music: 'daydream'},
            // {id: 83,   title: 'animations.list.WorkIt',                   animData: ['majestic_animations_5', 'work_it'],                        flag: 1,   price: 750,   looped: true,   music: 'work_it'},
            // {id: 84,   title: 'animations.list.Slick',                    animData: ['majestic_animations_5', 'slick'],                          flag: 1,   price: 500,   looped: true,   music: 'slick'},
            // {id: 85,   title: 'animations.list.Bombastic',                animData: ['majestic_animations_5', 'bombastic'],                      flag: 1,   price: 750,   looped: true,   music: 'bombastic'},
            // {id: 86,   title: 'animations.list.ItsaVibe',                 animData: ['majestic_animations_5', 'its_a_vibe'],                     flag: 1,   price: 1000,  looped: true,   music: 'its_a_vibe'},
            // {id: 87,   title: 'animations.list.WuTangIsForever',          animData: ['majestic_animations_5', 'wutang_is_forever'],              flag: 1,   price: 1000,  looped: true,   music: 'wutang_is_forever'},
            // {id: 88,   title: 'animations.list.RootinTootin',             animData: ['majestic_animations_5', 'rootin_tootin'],                  flag: 0,   price: 500,   looped: false,  music: 'rootin_tootin'},
            // {id: 89,   title: 'animations.list.Triumphant',               animData: ['majestic_animations_5', 'triumphant'],                     flag: 1,   price: 1000,  looped: true,   music: 'triumphant'},
            // {id: 90,   title: 'animations.list.LilDiplodoculus',          animData: ['majestic_animations_props', 'alfredo_player'],             flag: 33,  price: 2000,  looped: true,   music: 'lil_diplodoculus'}, // SeasonPass Summer 2022
            // {id: 91,   title: 'animations.list.Frolic',                   animData: ['majestic_animations_props', 'layers_player'],              flag: 1,   price: 1000,  looped: true,   music: 'frolic'},
            // {id: 92,   title: 'animations.list.PhoneItIn',                animData: ['majestic_animations_props', 'epic_sax'],                   flag: 1,   price: 1000,  looped: true,   music: 'phone_it_in'},
            // {id: 93,   title: 'animations.list.LlamaBell',                animData: ['majestic_animations_props', 'llama_cowbell'],              flag: 1,   price: 1500,  looped: true,   music: 'llama_bell'},
            // {id: 94,   title: 'animations.list.MajesticCoinFlip',         animData: ['majestic_animations_props', 'majestic_flipped'],           flag: 0,   price: 2000,  looped: false,  music: 'majestic_coinflip'},
            // {id: 95,   title: 'animations.list.LilFloaticorn',            animData: ['majestic_animations_props', 'llama_float_player'],         flag: 33,  price: 2000,  looped: true,   music: 'lil_floaticorn'},
            // {id: 96,   title: 'animations.list.Glowsticks',               animData: ['majestic_animations_props', 'glowstick_dance'],            flag: 1,   price: 1000,  looped: true,   music: 'glowsticks'},
            // {id: 97,   title: 'animations.list.ShakeItUp',                animData: ['majestic_animations_props', 'shake_dance'],                flag: 1,   price: 1000,  looped: true,   music: 'shake_it_up'},
            // {id: 98,   title: 'animations.list.IslandVibes',              animData: ['majestic_animations_props', 'ukulele_dance'],              flag: 1,   price: 1000,  looped: true,   music: 'island_vibes'},
            // {id: 99,   title: 'animations.list.SnareSolo',                animData: ['majestic_animations_props', 'snare_solo_player'],          flag: 1,   price: 2000,  looped: true,   music: 'snare_solo'},
            // {id: 100,  title: 'animations.list.RockOut',                  animData: ['majestic_animations_props', 'rock_out_player'],            flag: 1,   price: 2000,  looped: true,   music: 'rock_out'},
            // {id: 101,  title: 'animations.list.LilOctane',                animData: ['majestic_animations_props', 'rhyme_lock_player'],          flag: 33,  price: 2000,  looped: true,   music: 'lil_octane'},
            // {id: 102,  title: 'animations.list.Unicycle',                 animData: ['majestic_animations_props', 'unicycle_gadget_player'],     flag: 33,  price: 2000,  looped: true,   music: 'unicycle'},
            // {id: 103,  title: 'animations.list.BannerWaves',              animData: ['majestic_animations_props', 'banner_flag_player'],         flag: 1,   price: 2000,  looped: true},
            // {id: 104,  title: 'animations.list.BloominBouquet',           animData: ['majestic_animations_props', 'bouquet_hat_player'],         flag: 0,   price: 1500,  looped: false,  music: 'bloomin_bouquet'},
            // {id: 105,  title: 'animations.list.LilMonster',               animData: ['majestic_animations_props', 'car_lifted_player'],          flag: 33,  price: 2000,  looped: true,   music: 'lil_monster'},
            // {id: 106,  title: 'animations.list.GuitarWalk',               animData: ['majestic_animations_props', 'guitar_walk_player'],         flag: 1,   price: 2000,  looped: true,   music: 'guitar_walk'},
            // {id: 107,  title: 'animations.list.BestMates',                animData: ['majestic_animations_6', 'best_mates'],                     flag: 1,   price: 750,   looped: true,   music: 'best_mates'}, // SeasonPass Winter 2022
            // {id: 108,  title: 'animations.list.OnYourMarks',              animData: ['majestic_animations_6', 'on_your_marks'],                  flag: 0,   price: 500,   looped: false,  music: 'on_your_marks'},
            // {id: 109,  title: 'animations.list.LaidBackShuffle',          animData: ['majestic_animations_6', 'laid_back_shuffle'],              flag: 1,   price: 500,   looped: true,   music: 'laid_back_shuffle'},
            // {id: 110,  title: 'animations.list.ThePolloDance',            animData: ['majestic_animations_6', 'pollo_dance'],                    flag: 1,   price: 750,   looped: true,   music: 'pollo_dance'},
            // {id: 111,  title: 'animations.list.Scenario',                 animData: ['majestic_animations_6', 'scenario'],                       flag: 1,   price: 750,   looped: true,   music: 'scenario'},
            // {id: 112,  title: 'animations.list.BuckleUp',                 animData: ['majestic_animations_6', 'buckle_up'],                      flag: 1,   price: 750,   looped: true,   music: 'buckle_up'},
            // {id: 113,  title: 'animations.list.ItsComplicated',           animData: ['majestic_animations_6', 'its_complicated'],                flag: 1,   price: 500,   looped: true,   music: 'its_complicated'},
            // {id: 114,  title: 'animations.list.FreedomWheels',            animData: ['majestic_animations_6', 'freedom_wheels'],                 flag: 1,   price: 1000,  looped: true,   music: 'freedom_wheels'},
            // {id: 115,  title: 'animations.list.EverybodyLovesMe',         animData: ['majestic_animations_6', 'everybody_loves_me'],             flag: 1,   price: 1000,  looped: true,   music: 'everybody_loves_me'},
            // {id: 116,  title: 'animations.list.Pirouette',                animData: ['majestic_animations_6', 'pirouette'],                      flag: 1,   price: 1500,  looped: true,   music: 'pirouette'}, // SeasonPass Winter 2022
            // {id: 117,  title: 'animations.list.LazerBlast',               animData: ['majestic_animations_6', 'lazer_blast'],                    flag: 1,   price: 1000,  looped: true,   music: 'lazer_blast'},
            // {id: 118,  title: 'animations.list.Poki',                     animData: ['majestic_animations_6', 'poki'],                           flag: 1,   price: 1000,  looped: true,   music: 'poki'},
            // {id: 119,  title: 'animations.list.Leapin',                   animData: ['majestic_animations_6', 'leapin'],                         flag: 1,   price: 1000,  looped: true,   music: 'leapin'},
            // {id: 120,  title: 'animations.list.WellRounded',              animData: ['majestic_animations_6', 'well_rounded'],                   flag: 1,   price: 750,   looped: true,   music: 'well_rounded'},
            // {id: 121,  title: 'animations.list.Flux',                     animData: ['majestic_animations_6', 'flux'],                           flag: 1,   price: 500,   looped: true,   music: 'flux'},
            // {id: 122,  title: 'animations.list.Whirlwind',                animData: ['majestic_animations_6', 'whirlwind'],                      flag: 1,   price: 1000,  looped: true,   music: 'whirlwind'},
            // {id: 123,  title: 'animations.list.Jamboree',                 animData: ['majestic_animations_6', 'jamboree'],                       flag: 1,   price: 750,   looped: true,   music: 'jamboree'},
            // {id: 124,  title: 'animations.list.SlapHappy',                animData: ['majestic_animations_6', 'slap_happy'],                     flag: 1,   price: 500,   looped: true,   music: 'slap_happy'},
            // {id: 125,  title: 'animations.list.DreamFeet',                animData: ['majestic_animations_6', 'dream_feet'],                     flag: 1,   price: 1000,  looped: true,   music: 'dream_feet'},
            // {id: 126,  title: 'animations.list.Switchstep',               animData: ['majestic_animations_6', 'switchstep'],                     flag: 1,   price: 750,   looped: true,   music: 'switchstep'},
            // {id: 127,  title: 'animations.list.Glitter',                  animData: ['majestic_animations_6', 'glitter'],                        flag: 1,   price: 1500,  looped: true,   music: 'glitter'},
            // {id: 128,  title: 'animations.list.SugarRush',                animData: ['majestic_animations_6', 'sugar_rush'],                     flag: 1,   price: 1000,  looped: true,   music: 'sugar_rush'},
            // {id: 129,  title: 'animations.list.Twist',                    animData: ['majestic_animations_6', 'twist'],                          flag: 1,   price: 1000,  looped: true,   music: 'twist'},
            // {id: 130,  title: 'animations.list.Howl',                     animData: ['majestic_animations_6', 'howl'],                           flag: 0,   price: 500,   looped: false,  music: 'howl'}, // Halloween 2022
            // {id: 131,  title: 'animations.list.CrazyFeet',                animData: ['majestic_animations_6', 'crazy_feet'],                     flag: 1,   price: 750,   looped: true,   music: 'crazy_feet'},
            // {id: 132,  title: 'animations.list.HotMarat',                 animData: ['majestic_animations_6', 'hot_marat'],                      flag: 1,   price: 1500,  looped: true,   music: 'hot_marat'},
            // {id: 133,  title: 'animations.list.ShowStopper',              animData: ['majestic_animations_6', 'show_stopper'],                   flag: 1,   price: 1000,  looped: true,   music: 'show_stopper'},
            // {id: 134,  title: 'animations.list.Boneless',                 animData: ['majestic_animations_6', 'boneless'],                       flag: 1,   price: 1000,  looped: true,   music: 'boneless'},
            // {id: 135,  title: 'animations.list.PopLock',                  animData: ['majestic_animations_6', 'pop_lock'],                       flag: 1,   price: 750,   looped: true,   music: 'pop_lock'},
            // {id: 136,  title: 'animations.list.Steady',                   animData: ['majestic_animations_6', 'steady'],                         flag: 1,   price: 1500,  looped: true,   music: 'steady'},
            // {id: 137,  title: 'animations.list.Shimmer',                  animData: ['majestic_animations_6', 'shimmer'],                        flag: 1,   price: 1500,  looped: true,   music: 'shimmer'},
            // {id: 138,  title: 'animations.list.Springy',                  animData: ['majestic_animations_6', 'springy'],                        flag: 1,   price: 1000,  looped: true,   music: 'springy'},
            // {id: 139,  title: 'animations.list.FreeFlow',                 animData: ['majestic_animations_6', 'free_flow'],                      flag: 1,   price: 1500,  looped: true,   music: 'free_flow'},
            // {id: 140,  title: 'animations.list.Conga',                    animData: ['majestic_animations_6', 'conga'],                          flag: 1,   price: 1500,  looped: true,   music: 'conga'},
            // {id: 141,  title: 'animations.list.DeepEnd',                  animData: ['majestic_animations_6', 'deep_end'],                       flag: 1,   price: 1000,  looped: true,   music: 'deep_end'},
            // {id: 142,  title: 'animations.list.Pumpernickel',             animData: ['majestic_animations_6', 'pumpernickel'],                   flag: 1,   price: 1500,  looped: true,   music: 'pumpernickel'},
            // {id: 143,  title: 'animations.list.Jubilation',               animData: ['majestic_animations_6', 'jubilation'],                     flag: 1,   price: 500,   looped: true,   music: 'jubilation'},
            // {id: 144,  title: 'animations.list.Jaywalking',               animData: ['majestic_animations_6', 'jaywalking'],                     flag: 1,   price: 1000,  looped: true,   music: 'jaywalking'},
            // {id: 145,  title: 'animations.list.PeaceOut',                 animData: ['majestic_animations_6', 'peace_out'],                      flag: 0,   price: 500,   looped: false,  music: 'peace_out'},
            // {id: 146,  title: 'animations.list.Hype',                     animData: ['majestic_animations_6', 'hype'],                           flag: 1,   price: 750,   looped: true,   music: 'hype'},
            // {id: 147,  title: 'animations.list.OrangeJustice',            animData: ['majestic_animations_6', 'orange_justice'],                 flag: 1,   price: 1000,  looped: true,   music: 'orange_justice'},
            // {id: 148,  title: 'animations.list.SwipeIt',                  animData: ['majestic_animations_6', 'swipe_it'],                       flag: 1,   price: 750,   looped: true,   music: 'swipe_it'},
            // {id: 149,  title: 'animations.list.JumpAround',               animData: ['majestic_animations_7', 'jump_around'],                    flag: 33,  price: 1000,  looped: true,   music: 'jump_around'},
            // {id: 150,  title: 'animations.list.MonsterMash',              animData: ['majestic_animations_7', 'monster_mash'],                   flag: 1,   price: 1500,  looped: true,   music: 'monster_mash'},
            // {id: 151,  title: 'animations.list.FeelTheFlow',              animData: ['majestic_animations_7', 'feel_the_flow'],                  flag: 1,   price: 1500,  looped: true,   music: 'feel_the_flow'},
            // {id: 152,  title: 'animations.list.Copines',                  animData: ['majestic_animations_7', 'copines'],                        flag: 1,   price: 1500,  looped: true,   music: 'copines'},
            // {id: 153,  title: 'animations.list.JiggleJiggle',             animData: ['majestic_animations_7', 'jiggle'],                         flag: 1,   price: 1000,  looped: true,   music: 'jiggle'},
            // {id: 154,  title: 'animations.list.ForgetMeNot',              animData: ['majestic_animations_7', 'forget_me'],                      flag: 1,   price: 750,   looped: true,   music: 'forget_me_not'},
            // {id: 155,  title: 'animations.list.DrippinFlavor',            animData: ['majestic_animations_7', 'chilled'],                        flag: 1,   price: 750,   looped: true,   music: 'drippin_flavor'},
            // {id: 156,  title: 'animations.list.Distraction',              animData: ['majestic_animations_7', 'distraction'],                    flag: 1,   price: 500,   looped: true,   music: 'distraction'},
            // {id: 157,  title: 'animations.list.UCantCMe',                 animData: ['majestic_animations_7', 'ucan_cme'],                       flag: 1,   price: 500,   looped: true,   music: 'ucan_cme'},
            // {id: 158,  title: 'animations.list.TacoTime',                 animData: ['majestic_animations_props', 'taco_time'],                  flag: 1,   price: 1500,  looped: true,   music: 'taco_time'},
            // {id: 159,  title: 'animations.list.Snowshaker',               animData: ['majestic_animations_props_2', 'snowglobe'],                flag: 0,   price: 750,   looped: false,  music: 'snowglobe'}, // SeasonPass Winter 2022
            // {id: 160,  title: 'animations.list.RideAlong',                animData: ['majestic_animations_props_2', 'mj_sleigh_player'],         flag: 33,  price: 2000,  looped: true,   music: 'ride_along'}, // SeasonPass Winter 2022
            // {id: 161,  title: 'animations.list.SingAlong',                animData: ['majestic_animations_props_2', 'sing_along_player'],        flag: 1,   price: 2000,  looped: true,   music: 'sing_along_1'}, // SeasonPass Winter 2022
            // {id: 162,  title: 'animations.list.Unwrapped',                animData: ['majestic_animations_props_2', 'unwrapped_player'],         flag: 0,   price: 1000,  looped: false,  music: 'unwrapped'}, // SeasonPass Winter 2022
            // {id: 163,  title: 'animations.list.DoubleUp',                 animData: ['majestic_animations_7', 'double_up'],                      flag: 1,   price: 1500,  looped: true,   music: 'double_up'},
            // {id: 164,  title: 'animations.list.Sway',                     animData: ['majestic_animations_7', 'sway_1'],                         flag: 1,   price: 1500,  looped: true,   music: 'sway'},
            // {id: 165,  title: 'animations.list.ItsDynamite',              animData: ['majestic_animations_7', 'its_dynamite'],                   flag: 1,   price: 1000,  looped: true,   music: 'its_dynamite'},
            // {id: 166,  title: 'animations.list.MaYaHi',                   animData: ['majestic_animations_props_2', 'mayahi_player'],            flag: 1,   price: 1000,  looped: true,   music: 'mayahi'},
            // {id: 167,  title: 'animations.list.JugBand',                  animData: ['majestic_animations_props_2', 'jug'],                      flag: 1,   price: 2000,  looped: true,   music: 'jug_1'},
            // {id: 168,  title: 'animations.list.GetSchwifty',              animData: ['majestic_animations_props_2', 'get_swifty_1'],             flag: 1,   price: 1500,  looped: true,   music: 'get_swifty'},
            // {id: 169,  title: 'animations.list.ShantyForASquad',          animData: ['majestic_animations_props_2', 'shanty'],                   flag: 49,  price: 2000,  looped: true,   music: 'shanty_1'},
            // {id: 170,  title: 'animations.list.LilPrancer',               animData: ['majestic_animations_props_2', 'prancer_player'],           flag: 33,  price: 1500,  looped: true,   music: 'prancer'}, // SeasonPass Winter 2022
            // {id: 171,  title: 'animations.list.SlalomStyle',              animData: ['majestic_animations_props_2', 'slalom_player'],            flag: 33,  price: 1000,  looped: true,   music: 'slalom'}, // SeasonPass Winter 2022
            // {id: 172,  title: 'animations.list.BounceWitIt',              animData: ['majestic_animations_7', 'bounce_wit_it'],                  flag: 1,   price: 1500,  looped: true,   music: 'bounce_wit_it'},
            // {id: 173,  title: 'animations.list.DanceMonkey',              animData: ['majestic_animations_7', 'dance_monkey'],                   flag: 1,   price: 750,   looped: true,   music: 'dance_monkey'},
            // {id: 174,  title: 'animations.list.SideShuffle',              animData: ['majestic_animations_8', 'side_shuffle'],                   flag: 1,   price: 500,   looped: true,   music: 'side_shuffle'},
            // {id: 175,  title: 'animations.list.Flapper',                  animData: ['majestic_animations_8', 'flapper'],                        flag: 1,   price: 1000,  looped: true,   music: 'flapper'},
            // {id: 176,  title: 'animations.list.Vibin',                    animData: ['majestic_animations_8', 'vibin'],                          flag: 1,   price: 1000,  looped: true,   music: 'vibin'},
            // {id: 177,  title: 'animations.list.TheRobot',                 animData: ['majestic_animations_8', 'the_robot'],                      flag: 1,   price: 1000,  looped: true,   music: 'the_robot'},
            // {id: 178,  title: 'animations.list.GrooveJam',                animData: ['majestic_animations_8', 'groove_jam'],                     flag: 1,   price: 1500,  looped: true,   music: 'groove_jam'},
            // {id: 179,  title: 'animations.list.Flamenco',                 animData: ['majestic_animations_8', 'flamenco'],                       flag: 1,   price: 1500,  looped: true,   music: 'flamenco'},
            // {id: 180,  title: 'animations.list.TheRickDance',             animData: ['majestic_animations_8', 'rick_dance'],                     flag: 1,   price: 1000,  looped: true,   music: 'rick_dance'},
            // {id: 181,  title: 'animations.list.Crackdown',                animData: ['majestic_animations_8', 'crackdown'],                      flag: 1,   price: 1500,  looped: true,   music: 'crackdown'},
            // {id: 182,  title: 'animations.list.PrimoMoves',               animData: ['majestic_animations_8', 'primo_moves'],                    flag: 1,   price: 1000,  looped: true,   music: 'primo_moves'},
            // {id: 183,  title: 'animations.list.Balletic',                 animData: ['majestic_animations_8', 'balletic'],                       flag: 1,   price: 1000,  looped: true,   music: 'balletic'},
            // {id: 184,  title: 'animations.list.InfiniteDab',              animData: ['majestic_animations_8', 'infinite_dab'],                   flag: 1,   price: 1500,  looped: true,   music: 'infinite_dab'},
            // {id: 185,  title: 'animations.list.HandSignals',              animData: ['majestic_animations_8', 'hand_signals'],                   flag: 1,   price: 1500,  looped: true,   music: 'hand_signals'},
            // {id: 186,  title: 'animations.list.FancyFeet',                animData: ['majestic_animations_8', 'fancy_feet'],                     flag: 1,   price: 1000,  looped: true,   music: 'fancy_feet'},
            // {id: 187,  title: 'animations.list.CleanGroove',              animData: ['majestic_animations_8', 'clean_groove'],                   flag: 1,   price: 1000,  looped: true,   music: 'clean_groove'},
            // {id: 188,  title: 'animations.list.OldSchool',                animData: ['majestic_animations_8', 'old_school'],                     flag: 1,   price: 1000,  looped: true,   music: 'old_school'},
            // {id: 189,  title: 'animations.list.Intdroducing',             animData: ['majestic_animations_8', 'introducing'],                    flag: 1,   price: 1500,  looped: true,   music: 'introducing'},
            // {id: 190,  title: 'animations.list.SkaStraTerrestrial',       animData: ['majestic_animations_8', 'terrestrial'],                    flag: 1,   price: 1500,  looped: true,   music: 'terrestrial'},
            // {id: 191,  title: 'animations.list.YoureAwesome',             animData: ['majestic_animations_8', 'youre_awesome'],                  flag: 0,   price: 750,   looped: false,  music: 'youre_awesome'},
            // {id: 192,  title: 'animations.list.CluckStrut',               animData: ['majestic_animations_8', 'cluck_strut'],                    flag: 1,   price: 1000,  looped: true,   music: 'cluck_strut'},
            // {id: 193,  title: 'animations.list.Slitherin',                animData: ['majestic_animations_8', 'slitherin'],                      flag: 1,   price: 1500,  looped: true,   music: 'slitherin'},
            // {id: 194,  title: 'animations.list.ItsGoTime',                animData: ['majestic_animations_8', 'its_go_time'],                    flag: 1,   price: 1500,  looped: true,   music: 'its_go_time'},
            // {id: 195,  title: 'animations.list.GetFunky',                 animData: ['majestic_animations_8', 'get_funky'],                      flag: 1,   price: 1000,  looped: true,   music: 'get_funky'},
            // {id: 196,  title: 'animations.list.NanaNana',                 animData: ['majestic_animations_8', 'nana_nana'],                      flag: 1,   price: 1000,  looped: true,   music: 'nana_nana'},
            // {id: 197,  title: 'animations.list.SideHustle',               animData: ['majestic_animations_8', 'side_hustle'],                    flag: 1,   price: 1500,  looped: true,   music: 'side_hustle'},
            // {id: 198,  title: 'animations.list.Droop',                    animData: ['majestic_animations_8', 'droop'],                          flag: 1,   price: 1000,  looped: true,   music: 'droop'},
            // {id: 199,  title: 'animations.list.MashedPotato',             animData: ['majestic_animations_8', 'mashed_potato'],                  flag: 1,   price: 1000,  looped: true,   music: 'mashed_potato'},
            // {id: 200,  title: 'animations.list.Verve',                    animData: ['majestic_animations_8', 'verve'],                          flag: 1,   price: 1500,  looped: true,   music: 'verve'},
            // {id: 201,  title: 'animations.list.Gloss',                    animData: ['majestic_animations_8', 'gloss'],                          flag: 1,   price: 1500,  looped: true,   music: 'gloss'},
            // {id: 202,  title: 'animations.list.MyIdol',                   animData: ['majestic_animations_8', 'my_idol'],                        flag: 1,   price: 750,   looped: true,   music: 'my_idol'},
            // {id: 203,  title: 'animations.list.PawsClaws',                animData: ['majestic_animations_9', 'paws_claws'],                     flag: 1,   price: 1000,  looped: true,   music: 'paws_claws'},
            // {id: 204,  title: 'animations.list.RunningMan',               animData: ['majestic_animations_9', 'running_man'],                    flag: 1,   price: 1500,  looped: true,   music: 'running_man'},
            // {id: 205,  title: 'animations.list.LivingLarge',              animData: ['majestic_animations_9', 'living_large'],                   flag: 1,   price: 750,   looped: true,   music: 'living_large'},
            // {id: 206,  title: 'animations.list.Hootenanny',               animData: ['majestic_animations_9', 'hootenanny'],                     flag: 1,   price: 750,   looped: true,   music: 'hootenanny'},
            // {id: 207,  title: 'animations.list.DirtbikeChallenge',        animData: ['majestic_animations_9', 'dirtbike_challenge'],             flag: 1,   price: 750,   looped: true,   music: 'dirtbike_challenge'},
            // {id: 208,  title: 'animations.list.LunarParty',               animData: ['majestic_animations_9', 'lunar_party'],                    flag: 1,   price: 1500,  looped: true,   music: 'lunar_party'},
            // {id: 209,  title: 'animations.list.TheLook',                  animData: ['majestic_animations_9', 'the_look'],                       flag: 1,   price: 750,   looped: true,   music: 'the_look'},
            // {id: 210,  title: 'animations.list.Revel',                    animData: ['majestic_animations_9', 'revel'],                          flag: 1,   price: 500,   looped: true,   music: 'revel'},
            // {id: 211,  title: 'animations.list.ImDiamond',                animData: ['majestic_animations_9', 'im_diamond'],                     flag: 1,   price: 1500,  looped: true,   music: 'im_diamond'},
            // {id: 212,  title: 'animations.list.Hitchhiker',               animData: ['majestic_animations_9', 'hitchhiker'],                     flag: 1,   price: 500,   looped: true,   music: 'hitchhiker'},
            // {id: 213,  title: 'animations.list.Waterworks',               animData: ['majestic_animations_9', 'waterworks'],                     flag: 1,   price: 500,   looped: true,   music: 'waterworks'},
            // {id: 214,  title: 'animations.list.PickItUp',                 animData: ['majestic_animations_9', 'pick_it_up'],                     flag: 1,   price: 750,   looped: true,   music: 'pick_it_up'},
            // {id: 215,  title: 'animations.list.CaliforniaGurls',          animData: ['majestic_animations_9', 'california_gurls'],               flag: 1,   price: 2000,  looped: true,   music: 'california_gurls'},
            // {id: 216,  title: 'animations.list.BBoomBBoom',               animData: ['majestic_animations_9', 'bboom_bboom'],                    flag: 1,   price: 2000,  looped: true,   music: 'bboom_bboom'},
            // {id: 217,  title: 'animations.list.HangLooseCelebration',     animData: ['majestic_animations_10', 'hang_loose_celebration'],        flag: 1,   price: 1000,  looped: true,   music: 'hang_loose_celebration'},
            // {id: 218,  title: 'animations.list.Tootsee',                  animData: ['majestic_animations_10', 'tootsee'],                       flag: 1,   price: 1500,  looped: true,   music: 'tootsee'},
            // {id: 219,  title: 'animations.list.TheDanceLaroi',            animData: ['majestic_animations_10', 'the_dance_laroi'],               flag: 1,   price: 1500,  looped: true,   music: 'the_dance_laroi'},
            // {id: 220,  title: 'animations.list.DanceOff',                 animData: ['majestic_animations_10', 'dance_off'],                     flag: 1,   price: 1000,  looped: true,   music: 'dance_off'},
            // {id: 221,  title: 'animations.list.FishyFlourish',            animData: ['majestic_animations_10', 'fishy_flourish'],                flag: 1,   price: 1000,  looped: true,   music: 'fishy_flourish'},
            // {id: 222,  title: 'animations.list.Freestylin',               animData: ['majestic_animations_10', 'freestylin'],                    flag: 1,   price: 1500,  looped: true,   music: 'freestylin'},
            // {id: 223,  title: 'animations.list.Glyphic',                  animData: ['majestic_animations_10', 'glyphic'],                       flag: 1,   price: 1500,  looped: true,   music: 'glyphic'},
            // {id: 224,  title: 'animations.list.Fandangle',                animData: ['majestic_animations_10', 'fandalangle'],                   flag: 1,   price: 1000,  looped: true,   music: 'fandalangle'},
            // {id: 225,  title: 'animations.list.MarshWalk',                animData: ['majestic_animations_10', 'marsh_walk'],                    flag: 1,   price: 1500,  looped: true,   music: 'marsh_walk'},
            // {id: 226,  title: 'animations.list.LazyShuffle',              animData: ['majestic_animations_10', 'lazy_shuffle'],                  flag: 1,   price: 1500,  looped: true,   music: 'lazy_shuffle'},
            // {id: 227,  title: 'animations.list.Backstroke',               animData: ['majestic_animations_10', 'backstroke'],                    flag: 1,   price: 750,   looped: true,   music: 'backstroke'},
            // {id: 228,  title: 'animations.list.CrissCross',               animData: ['majestic_animations_10', 'criss_cross'],                   flag: 1,   price: 1000,  looped: true,   music: 'criss_cross'},
            // {id: 229,  title: 'animations.list.PartyHips',                animData: ['majestic_animations_10', 'party_hips'],                    flag: 1,   price: 750,   looped: true,   music: 'party_hips'},
            // {id: 230,  title: 'animations.list.LlamaConga',               animData: ['majestic_animations_10', 'llama_conga'],                   flag: 1,   price: 750,   looped: true,   music: 'llama_conga'},
            // {id: 231,  title: 'animations.list.JumpingJacks',             animData: ['majestic_animations_10', 'jumping_jacks'],                 flag: 1,   price: 500,   looped: true,   music: 'jumping_jacks'},
            // {id: 232,  title: 'animations.list.Shout',                    animData: ['majestic_animations_10', 'shout'],                         flag: 1,   price: 750,   looped: true,   music: 'shout'},
            // {id: 233,  title: 'animations.list.Yay',                      animData: ['majestic_animations_10', 'yay'],                           flag: 1,   price: 500,   looped: true,   music: 'yay'},
            // {id: 234,  title: 'animations.list.Forever',                  animData: ['majestic_animations_9',  'forever'],                       flag: 1,   price: 2000,  looped: true,   music: 'forever'},
            // {id: 235,  title: 'animations.list.TheMagicBomb',             animData: ['majestic_animations_10', 'the_magic_bomb'],                flag: 1,   price: 2000,  looped: true,   music: 'the_magic_bomb'},
            // {id: 236,  title: 'animations.list.RollNRock',                animData: ['majestic_animations_10', 'roll_n_rock'],                   flag: 1,   price: 2000,  looped: true,   music: 'roll_n_rock'},
            // {id: 237,  title: 'animations.list.WarmUp',                   animData: ['majestic_animations_11', 'warm_up'],                       flag: 1,   price: 750,   looped: true,   music: 'warm_up'},
            // {id: 238,  title: 'animations.list.GunslingerSmokeshow',      animData: ['majestic_animations_11', 'gungslinger_smokeshow'],         flag: 1,   price: 1500,  looped: true,   music: 'gungslinger_smokeshow'},
            // {id: 239,  title: 'animations.list.SweetShot',                animData: ['majestic_animations_11', 'sweet_shot'],                    flag: 1,   price: 1500,  looped: true,   music: 'sweet_shot'},
            // {id: 240,  title: 'animations.list.VibrantVibin',             animData: ['majestic_animations_11', 'vibrant_vibin'],                 flag: 1,   price: 1000,  looped: true,   music: 'vibrant_vibin'},
            // {id: 241,  title: 'animations.list.KoiDance',                 animData: ['majestic_animations_11', 'koi_dance'],                     flag: 1,   price: 1000,  looped: true,   music: 'koi_dance'},
            // {id: 242,  title: 'animations.list.TheQuickStyle',            animData: ['majestic_animations_11', 'quick_style'],                   flag: 1,   price: 1500,  looped: true,   music: 'quick_style'},
            // {id: 243,  title: 'animations.list.MadeYouLook',              animData: ['majestic_animations_11', 'made_you_look'],                 flag: 1,   price: 1500,  looped: true,   music: 'made_you_look'},
            // {id: 244,  title: 'animations.list.AskMe',                    animData: ['majestic_animations_11', 'ask_me'],                        flag: 1,   price: 1000,  looped: true,   music: 'ask_me'},
            // {id: 245,  title: 'animations.list.Komarovo',                 animData: ['majestic_animations_props_3', 'atomic_synth_player'],      flag: 1,   price: 2000,  looped: true,   music: 'atomic_synth'},
            // {id: 246,  title: 'animations.list.Sleds',                    animData: ['majestic_animations_props_2', 'sled_player'],              flag: 33,  price: 2000,  looped: true },
            // {id: 247,  title: 'animations.list.RingItOn',                 animData: ['majestic_animations_props_3', 'ring_it_on'],               flag: 1,   price: 1500,  looped: true,   music: 'ring_it_on'},
            // {id: 248,  title: 'animations.list.Boomin',                   animData: ['majestic_animations_props_3', 'boombox_player'],           flag: 1,   price: 1500,  looped: true,   music: 'boomin'},
            // {id: 249,  title: 'animations.list.BootsNCats',               animData: ['majestic_animations_props_3', 'boots_n_cats_player'],      flag: 1,   price: 1500,  looped: true,   music: 'boots_and_cats'},
            // {id: 250,  title: 'animations.list.Rage',                     animData: ['majestic_animations_props_3', 'mic_stand_player'],         flag: 1,   price: 1000,  looped: true,   music: 'rage'},
            // {id: 251,  title: 'animations.list.IDeclare',                 animData: ['majestic_animations_props_3', 'declare'],                  flag: 1,   price: 1000,  looped: true,   music: 'declare'},
            // {id: 252,  title: 'animations.list.RocketRodeo',              animData: ['majestic_animations_props_3', 'rocket_rodeo_player'],      flag: 1,   price: 2000,  looped: true,   music: 'rocket_rodeo'},
            // {id: 253,  title: 'animations.list.DrumMajor',                animData: ['majestic_animations_props_3', 'drum_major_player'],        flag: 1,   price: 1000,  looped: true,   music: 'drum_major'},
            // {id: 254,  title: 'animations.list.PumpItUp',                 animData: ['majestic_animations_props_3', 'pump_it_up'],               flag: 1,   price: 1000,  looped: true,   music: 'pump_it_up'},
            // {id: 255,  title: 'animations.list.CheerUp',                  animData: ['majestic_animations_props_3', 'cheer_up'],                 flag: 1,   price: 1500,  looped: true,   music: 'cheer_up'},
            // {id: 256,  title: 'animations.list.EmpressFanDance',          animData: ['majestic_animations_props_3', 'empress_fan_dance'],        flag: 1,   price: 1500,  looped: true,   music: 'empress_fan_dance'},
            // {id: 257,  title: 'animations.list.ManeraMir',                animData: ['majestic_animations_dances', 'manera'],                    flag: 1,   price: 1500,  looped: true,   music: 'manera_mir'},
            // {id: 258,  title: 'animations.list.AirShredder',              animData: ['majestic_animations_11', 'air_shredder'],                  flag: 1,   looped: true,    music: 'air_shredder'},
            // {id: 259,  title: 'animations.list.CrazyBoy',                 animData: ['majestic_animations_11', 'crazy_boy'],                     flag: 1,   looped: true,    music: 'crazy_boy'},
            // {id: 260,  title: 'animations.list.Fishin',                   animData: ['majestic_animations_11', 'fishin'],                        flag: 33,  looped: true,    music: 'fishin'},
            // {id: 261,  title: 'animations.list.NinjaStyle',               animData: ['majestic_animations_11', 'ninja_style'],                   flag: 1,   looped: true,    music: 'ninja_style'},
            // {id: 262,  title: 'animations.list.TheWorm',                  animData: ['majestic_animations_11', 'the_worm'],                      flag: 1,   looped: true,    music: 'the_worm'},
            // {id: 263,  title: 'animations.list.Wiggle',                   animData: ['majestic_animations_11', 'wiggle'],                        flag: 1,   looped: true,    music: 'wiggle'},
            // {id: 264,  title: 'animations.list.StarPower',                animData: ['majestic_animations_11', 'star_power'],                    flag: 1,   looped: true,    music: 'star_power'},
            // {id: 265,  title: 'animations.list.Rambunctious',             animData: ['majestic_animations_12', 'rambunctious'],                  flag: 1,   looped: true,    music: 'rambunctious'},
            // {id: 266,  title: 'animations.list.Rawr',                     animData: ['majestic_animations_12', 'rawr'],                          flag: 0,   looped: true,    music: 'rawr'},
            // {id: 267,  title: 'animations.list.FastFeet',                 animData: ['majestic_animations_12', 'fast_feet'],                     flag: 1,   looped: true,    music: 'fast_feet'},
            // {id: 268,  title: 'animations.list.Capoeira',                 animData: ['majestic_animations_12', 'capoeira'],                      flag: 1,   looped: true,    music: 'capoeira'},
            // {id: 269,  title: 'animations.list.Bobbin',                   animData: ['majestic_animations_12', 'bobbin'],                        flag: 1,   looped: true,    music: 'bobbin'},
            // {id: 270,  title: 'animations.list.Overdrive',                animData: ['majestic_animations_12', 'overdrive'],                     flag: 1,   looped: true,    music: 'overdrive'},
            // {id: 271,  title: 'animations.list.Fanciful',                 animData: ['majestic_animations_12', 'fanciful'],                      flag: 33,  looped: true,    music: 'fanciful'},
            // {id: 272,  title: 'animations.list.BunnyHop',                 animData: ['majestic_animations_12', 'bunny_hop'],                     flag: 33,  looped: true,    music: 'bunny_hop'},
            // {id: 273,  title: 'animations.list.No Sweat',                 animData: ['majestic_animations_12', 'no_sweat'],                      flag: 1,   looped: true,    music: 'no_sweat'},
            // {id: 274,  title: 'animations.list.WindmillFloss',            animData: ['majestic_animations_12', 'windmill_floss'],                flag: 1,   looped: true,    music: 'windmill_floss'},
            // {id: 275,  title: 'animations.list.SwoleCat',                 animData: ['majestic_animations_12', 'swole_cat'],                     flag: 1,   looped: true,    music: 'swole_cat'},
            // {id: 276,  title: 'animations.list.HeadBanger',               animData: ['majestic_animations_12', 'head_banger'],                   flag: 1,   looped: true,    music: 'head_banger'},
            // {id: 277,  title: 'animations.list.GetLoose',                 animData: ['majestic_animations_12', 'get_loose'],                     flag: 1,   looped: true,    music: 'get_loose'},
            // {id: 278,  title: 'animations.list.Bully',                    animData: ['majestic_animations_12', 'bully'],                         flag: 1,   looped: true,    music: 'bully'},
            // {id: 279,  title: 'animations.list.BringItAround',            animData: ['majestic_animations_12', 'bring_it_around'],               flag: 1,   looped: true,    music: 'bring_it_around'},
            // {id: 280,  title: 'animations.list.SquareUp',                 animData: ['majestic_animations_12', 'square_up'],                     flag: 1,   looped: true,    music: 'square_up'},
            // {id: 281,  title: 'animations.list.WithoutYou',               animData: ['majestic_animations_12', 'without_you'],                   flag: 1,   looped: true,    music: 'without_you'},
            // {id: 282,  title: 'animations.list.RunItDown',                animData: ['majestic_animations_12', 'run_it_down'],                   flag: 1,   looped: true,    music: 'run_it_down'},
            // {id: 283,  title: 'animations.list.Goated',                   animData: ['majestic_animations_12', 'goated'],                        flag: 1,   looped: true,    music: 'goated'},
            // {id: 284,  title: 'animations.list.CelebrateMe',              animData: ['majestic_animations_12', 'celebrate_me'],                  flag: 1,   looped: true,    music: 'celebrate_me'},
            // {id: 285,  title: 'animations.list.PayItOff',                 animData: ['majestic_animations_12', 'pay_it_off'],                    flag: 1,   looped: true,    music: 'pay_it_off'},
            // {id: 286,  title: 'animations.list.FastFlex',                 animData: ['majestic_animations_12', 'fast_flex'],                     flag: 1,   looped: true,    music: 'fast_flex'},
            // {id: 287,  title: 'animations.list.GetOutOfYourMind',         animData: ['majestic_animations_12', 'get_out_of_your_mind'],          flag: 1,   looped: true,    music: 'get_out_of_your_mind'},
            // {id: 288,  title: 'animations.list.LitDance',                 animData: ['majestic_animations_dances_1', 'lit_dance'],               flag: 1,   looped: true,    music: 'lit_dance'},
            {id: 355,  title: 'animations.list.LookingToTheSky',             animData: ['majestic_animations_custom_4', 'alpha_warrior_loop'],      flag: 1,   looped: true,    price: 1000,   },
            {id: 397,  title: 'animations.list.Shine',                       animData: ['majestic_animations_dances_3', 'shine'],                   flag: 1,   looped: true,    music: 'shine_majestic',    price: 1000,   },
        ]
    },

    // xmas: {
    //     items: [
    //         { item: 'small',    price: 300,  itemId: 427 },
    //         { item: 'medium',   price: 600,  itemId: 428 },
    //         { item: 'large',    price: 1200, itemId: 429 }
    //     ]
    // },

    cars: {
        items: [
            // exclusive
            // { item: 'urus58', limit: 58, server: 'RU5', price: 58000 },

            { item: 'vesta',                price: 3999, discount: 50, serverExclude: 'PL' },
            { item: 'priora',               price: 3999, discount: 20, serverExclude: 'PL' },
            { item: 'urban',                price: 3999, discount: 30, serverExclude: 'PL' },
            { item: 'samara',               price: 3999, discount: 30, serverExclude: 'PL' },
            { item: 'vaz2107',              price: 3999, discount: 25, serverExclude: 'PL' },
            { item: 'vaz2109',              price: 3999, discount: 10, serverExclude: 'PL' },
            { item: 'matiz',                price: 3999, discount: 10  },
            { item: 'mjc',                  price: 4999, discount: 20  },
            { item: 'dsprinter',            price: 6999, discount: 30  },
            { item: 'msprinter',            price: 7999, discount: 5  },
            { item: 'vclass',               price: 5999, discount: 15  },
            { item: 'nisgtr',               price: 7499, discount: 15  },
            { item: 'v4sp',                 price: 8999, discount: 30  },
            { item: 'velar',                price: 9999, discount: 20  },
            { item: 'brutale',              price: 9999, discount: 20  },
            { item: '718bs',                price: 10999, discount: 20 },
            { item: 'amggt',                price: 11999, discount: 25 },
            { item: 'e63s',                 price: 14999, discount: 30 },
            { item: 'm5comp',               price: 14999, discount: 25 },
            { item: 'cls63s',               price: 11999, discount: 15 },
            { item: 'bentaygast',           price: 11999, discount: 25 },
            { item: 'g63',                  price: 19999, discount: 15 },
            { item: 'panamera17turbo',      price: 17999, discount: 30 },
            { item: 'm8gc',                 price: 17999, discount: 20 },
            { item: 'ghost',                price: 19999, discount: 25 },
            { item: 's63cab',               price: 18999, discount: 20 },
            { item: '63gls2',               price: 24999, discount: 20 },
            { item: 'pts21',                price: 25999, discount: 35 },
            { item: 'continental',          price: 25999, discount: 60 },
            { item: 'huracan',              price: 29999, discount: 50 },
            { item: 'urus',                 price: 31999, discount: 35 },
            { item: 'asvj',                 price: 37999, discount: 35 },
            { item: 'g63amg6x6',            price: 37999, discount: 45 },
            { item: 'cullinan',             price: 37999, discount: 30 },
            { item: 'mp1',                  price: 37999, discount: 20 },
            { item: 'chiron19',             price: 44999, discount: 25 },
            { item: 'laferrari',            price: 44999, discount: 35 },
            { item: 'bdivo',                price: 74999, discount: 25 },
            { item: 'ram',                  price: 13999, discount: 30 },
            { item: 'c300',                 price: 12999, discount: 30 },

            // Вертолеты
            { item: 'maverickc',            price: 25000, discount: 10 },
            { item: 'sparrowc',             price: 40000, discount: 25 },
            { item: 'froggerc',             price: 40000, discount: 25 },
            { item: 'buzzard2c',            price: 50000, discount: 15 },
            { item: 'supervolitoc',         price: 70000, discount: 45 },
            { item: 'supervolito2c',        price: 80000, discount: 45 },
            { item: 'swiftc',               price: 100000, discount: 45 },
            { item: 'volatusc',             price: 150000, discount: 50 },
        ]
    },

    clothesItems: [
        // male
        {
            head: [
                { component: 0, isProp: 1, drawable: 2029, price: 1500,  slider: 1, items: 3, discount: 70 }, // Крид
                { component: 0, isProp: 1, drawable: 2030, price: 1500,  slider: 1, items: 3, discount: 70 }, // Крид

                { component: 0, isProp: 1, drawable: 2009, price: 7500,  slider: 1, items: 1, discount: 20   },
                { component: 0, isProp: 1, drawable: 2010, price: 3800,  slider: 1, items: 1, discount: 30   },
                { component: 0, isProp: 1, drawable: 2011, price: 1500,  slider: 1, items: 7, discount: 60 },
                { component: 0, isProp: 1, drawable: 2012, price: 1900,  slider: 1, items: 6, discount: 30   },
                { component: 0, isProp: 1, drawable: 2013, price: 5700,  slider: 1, items: 15, discount: 75  },
                { component: 0, isProp: 1, drawable: 2015, price: 1000,  slider: 1, items: 2, discount: 60   },
                { component: 0, isProp: 1, drawable: 2014, price: 1000,  slider: 1, items: 1, discount: 50   },
                { component: 0, isProp: 1, drawable: 2018, price: 2000,  slider: 1, items: 7, discount: 60   },
                { component: 0, isProp: 1, drawable: 2019, price: 1500,  slider: 1, items: 13, discount: 50  },
            ],

            tops: [
                { component: 11, isProp: 0, drawable: 2182, slider: 1,   items: 2,  price: 1500, discount: 30 },  // Крид
                { component: 11, isProp: 0, drawable: 2184, slider: 1,   items: 2,  price: 2558, discount: 35 },  // Крид
                { component: 11, isProp: 0, drawable: 2186, slider: 1,   items: 2,  price: 1558, discount: 30 },  // Крид
                { component: 11, isProp: 0, drawable: 2181, texture: 0,  slider: 0, price: 1500, discount: 35 },  // Крид
                { component: 11, isProp: 0, drawable: 2181, texture: 1,  slider: 0, price: 1500, discount: 35 },  // Крид
                { component: 11, isProp: 0, drawable: 2181, texture: 2,  slider: 0, price: 2000, discount: 40 },  // Крид
                { component: 11, isProp: 0, drawable: 2181, texture: 3,  slider: 0, price: 2000, discount: 40 },  // Крид


                { component: 11, isProp: 0, drawable: 2022, price: 800,    slider: 1, items: 11, discount: 30 },
                { component: 11, isProp: 0, drawable: 2023, price: 400,    slider: 1, items: 10, discount: 25 },
                { component: 11, isProp: 0, drawable: 2024, price: 400,    slider: 1, items: 5, discount: 25 },
                { component: 11, isProp: 0, drawable: 2027, price: 800,    slider: 1, items: 6, discount: 15 },
                { component: 11, isProp: 0, drawable: 2028, price: 800,    slider: 1, items: 8, discount: 15 },
                { component: 11, isProp: 0, drawable: 2029, price: 600,    slider: 1, items: 8, discount: 25 },
                { component: 11, isProp: 0, drawable: 2030, price: 400,    slider: 1, items: 7, discount: 20 },
                { component: 11, isProp: 0, drawable: 2031, price: 600,    slider: 1, items: 8, discount: 10 },
                { component: 11, isProp: 0, drawable: 2036, price: 800,    slider: 1, items: 16, discount: 10 },
                { component: 11, isProp: 0, drawable: 2044, price: 400,    slider: 1, items: 2, discount: 15 },
                { component: 11, isProp: 0, drawable: 2049, price: 800,    slider: 1, items: 3, discount: 35 },
                { component: 11, isProp: 0, drawable: 2050, price: 800,    slider: 1, items: 4, discount: 25 },
                { component: 11, isProp: 0, drawable: 2051, price: 1200,   slider: 1, items: 1, discount: 35 },
                { component: 11, isProp: 0, drawable: 2059, price: 600,    slider: 1, items: 8, discount: 15 },
                { component: 11, isProp: 0, drawable: 2060, price: 600,    slider: 1, items: 8, discount: 35 },
                { component: 11, isProp: 0, drawable: 2065, price: 1500,   slider: 1, items: 8, discount: 25 },
                { component: 11, isProp: 0, drawable: 2066, price: 800,    slider: 1, items: 5, discount: 20 },
                { component: 11, isProp: 0, drawable: 2067, price: 1000,   slider: 1, items: 2, discount: 35 },
                { component: 11, isProp: 0, drawable: 2069, price: 1000,   slider: 1, items: 12, discount: 20 },
                { component: 11, isProp: 0, drawable: 2080, price: 500,    slider: 1, items: 10, discount: 35 },
                { component: 11, isProp: 0, drawable: 2088, price: 1000,   slider: 1, items: 9, discount: 35 },
                { component: 11, isProp: 0, drawable: 2089, price: 500,    slider: 1, items: 8, discount: 20 },
                { component: 11, isProp: 0, drawable: 2118, price: 1500,   slider: 1, items: 24, discount: 20 },
                { component: 11, isProp: 0, drawable: 2122, price: 900,    slider: 1, items: 8, discount: 25 },
                { component: 11, isProp: 0, drawable: 2124, price: 1000,   slider: 1, items: 8, discount: 25 },
                { component: 11, isProp: 0, drawable: 2130, price: 1500,   slider: 1, items: 54, discount: 20 },
                { component: 11, isProp: 0, drawable: 2137, price: 1250,   slider: 1, items: 28, discount: 25 },
                { component: 11, isProp: 0, drawable: 2138, price: 900,    slider: 1, items: 20, discount: 20 },
                { component: 11, isProp: 0, drawable: 2150, price: 400,    slider: 1, items: 13, discount: 10 },
                { component: 11, isProp: 0, drawable: 2151, price: 500,    slider: 1, items: 11, discount: 30 },
                { component: 11, isProp: 0, drawable: 2152, price: 500,    slider: 1, items: 8, discount: 35 },
                { component: 11, isProp: 0, drawable: 2165, price: 750,    slider: 1, items: 15, discount: 10 },
                { component: 11, isProp: 0, drawable: 2166, price: 500,    slider: 1, items: 9, discount: 25 },
                { component: 11, isProp: 0, drawable: 2169, price: 450,    slider: 1, items: 7, discount: 20 },
                { component: 11, isProp: 0, drawable: 2173, price: 1000,   slider: 1, items: 7, discount: 20 },
                { component: 11, isProp: 0, drawable: 2189, price: 1500,   slider: 1, items: 64, discount: 15 },
                { component: 11, isProp: 0, drawable: 2190, price: 1000,   slider: 1, items: 125, discount: 25 },
                { component: 11, isProp: 0, drawable: 2191, price: 2500,   slider: 1, items: 21, discount: 30 },
                { component: 11, isProp: 0, drawable: 2192, price: 800,    slider: 1, items: 7, discount: 10 },
                { component: 11, isProp: 0, drawable: 2193, price: 800,    slider: 1, items: 8, discount: 10 },
                { component: 11, isProp: 0, drawable: 2195, price: 800,    slider: 1, items: 9, discount: 10 },
                { component: 11, isProp: 0, drawable: 2196, price: 500,    slider: 1, items: 7, discount: 10 },
                { component: 11, isProp: 0, drawable: 2197, price: 250,    slider: 1, items: 2, discount: 20 },
                { component: 11, isProp: 0, drawable: 2198, price: 2000,   slider: 1, items: 4, discount: 20 },
                { component: 11, isProp: 0, drawable: 2199, price: 2000,   slider: 1, items: 4, discount: 20 },
                { component: 11, isProp: 0, drawable: 2200, price: 2500,   slider: 1, items: 17, discount: 25 },
                { component: 11, isProp: 0, drawable: 2204, price: 750,    slider: 1, items: 9, discount: 30 },
                { component: 11, isProp: 0, drawable: 2205, price: 800,    slider: 1, items: 20, discount: 25 },
                { component: 11, isProp: 0, drawable: 2207, price: 1200,   slider: 1, items: 1, discount: 15 },
                { component: 11, isProp: 0, drawable: 2214, price: 300,    slider: 1, items: 9, discount: 25 },
                { component: 11, isProp: 0, drawable: 2215, price: 1500,   slider: 1, items: 11, discount: 35 },
                { component: 11, isProp: 0, drawable: 2220, price: 500,    slider: 1, items: 9, discount: 25 },
                { component: 11, isProp: 0, drawable: 2221, price: 500,    slider: 1, items: 15, discount: 15 },
                { component: 11, isProp: 0, drawable: 2225, price: 500,    slider: 1, items: 10, discount: 25 },
                { component: 11, isProp: 0, drawable: 2229, price: 500,    slider: 1, items: 8, discount: 25 },
                { component: 11, isProp: 0, drawable: 2230, price: 500,    slider: 1, items: 8, discount: 15 },
                { component: 11, isProp: 0, drawable: 2232, price: 400,    slider: 1, items: 8, discount: 15 },
                { component: 11, isProp: 0, drawable: 2235, price: 800,    slider: 1, items: 8, discount: 25 },
                { component: 11, isProp: 0, drawable: 2241, price: 1250,   slider: 1, items: 6, discount: 25 },
                { component: 11, isProp: 0, drawable: 2250, price: 3500,   slider: 1, items: 21, discount: 15 },
                { component: 11, isProp: 0, drawable: 2251, price: 1000,   slider: 1, items: 10, discount: 10 },
                { component: 11, isProp: 0, drawable: 2252, price: 1500,   slider: 1, items: 7, discount: 25 },
                { component: 11, isProp: 0, drawable: 2370, price: 350,    slider: 1, items: 1, discount: 20 },

                { component: 11, isProp: 0, drawable: 2440, price: 3000,  slider: 1, items: 1, discount: 60 }, // Benzo hoodie 2024
                { component: 11, isProp: 0, drawable: 2441, price: 3000,  slider: 1, items: 1, discount: 60 }, // Benzo hoodie 2024

                { component: 11, isProp: 0, drawable: 2456, price: 1000,  slider: 1, items: 6, discount: 30 },
                { component: 11, isProp: 0, drawable: 2460, price: 1000,  slider: 1, items: 8, discount: 30 },
                { component: 11, isProp: 0, drawable: 2461, price: 750,  slider: 1, items: 5, discount: 30 },
                { component: 11, isProp: 0, drawable: 2466, price: 2000,  slider: 1, items: 10, discount: 30 },

                { component: 11, isProp: 0, drawable: 2392, price: 9999,  slider: 1, items: 1, server: 'launcherhoodie' },
                { component: 11, isProp: 0, drawable: 2452, price: 9999,  slider: 1, items: 1, server: 'SummerEventHoodie' }, // Summer Event 2024
                { component: 11, isProp: 0, drawable: 2467, price: 2000,  slider: 1, items: 9, server: '2466action'},
                { component: 11, isProp: 0, drawable: 2064, price: 9999,  slider: 1, items: 8, server: '2063action' },

                { component: 11, isProp: 0, drawable: 2243, price: 1500,   slider: 1, items: 10, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2061, price: 1500,   slider: 1, items: 11, server: 'delete'  }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2072, price: 1000,   slider: 1, items: 9, server: 'delete'   }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2112, price: 1500,   slider: 1, items: 10, server: 'delete'  }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2120, price: 1250,   slider: 1, items: 10, server: 'delete'  }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2121, price: 900,    slider: 1, items: 4, server: 'delete'   }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2021, price: 800,    slider: 1, items: 11, server: 'delete'  }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2136, price: 1000,   slider: 1, items: 21, server: 'delete'  }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2119, price: 900,    slider: 1, items: 14, server: 'delete'  }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2128, price: 1000,   slider: 1, items: 1, server: 'delete'   }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2056, price: 1500,   slider: 1, items: 3, server: 'delete'   }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2058, price: 1500,   slider: 1, items: 3, server: 'delete'   }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2054, price: 1500,   slider: 1, items: 4, server: 'delete'   }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2062, price: 1500,   slider: 1, items: 11, server: 'delete'  }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2063, price: 800,    slider: 1, items: 8, server: 'delete'   }, // @FOR_DELETE
            ],

            legs: [
                { component: 4, isProp: 0, drawable: 2006, price: 800,    slider: 1, items: 6, discount: 15 },
                { component: 4, isProp: 0, drawable: 2013, price: 800,    slider: 1, items: 2, discount: 20 },
                { component: 4, isProp: 0, drawable: 2015, price: 400,    slider: 1, items: 8, discount: 15 },
                { component: 4, isProp: 0, drawable: 2016, price: 400,    slider: 1, items: 8, discount: 25 },
                { component: 4, isProp: 0, drawable: 2017, price: 1200,   slider: 1, items: 8, discount: 15 },
                { component: 4, isProp: 0, drawable: 2020, price: 500,    slider: 1, items: 12, discount: 30 },
                { component: 4, isProp: 0, drawable: 2023, price: 500,    slider: 1, items: 9, discount: 40 },
                { component: 4, isProp: 0, drawable: 2031, price: 500,    slider: 1, items: 10, discount: 35 },
                { component: 4, isProp: 0, drawable: 2039, price: 500,    slider: 1, items: 10, discount: 30 },
                { component: 4, isProp: 0, drawable: 2040, price: 500,    slider: 1, items: 8, discount: 40 },
                { component: 4, isProp: 0, drawable: 2055, price: 1000,   slider: 1, items: 7, discount: 30 },
                { component: 4, isProp: 0, drawable: 2057, price: 750,    slider: 1, items: 2, discount: 30 },
                { component: 4, isProp: 0, drawable: 2058, price: 1000,   slider: 1, items: 4, discount: 40 },
                { component: 4, isProp: 0, drawable: 2078, price: 400,    slider: 1, items: 13, discount: 20 },
                { component: 4, isProp: 0, drawable: 2079, price: 500,    slider: 1, items: 11, discount: 40 },
                { component: 4, isProp: 0, drawable: 2080, price: 500,    slider: 1, items: 8, discount: 40 },
                { component: 4, isProp: 0, drawable: 2092, price: 1000,   slider: 1, items: 10, discount: 20 },
                { component: 4, isProp: 0, drawable: 2093, price: 500,    slider: 1, items: 14, discount: 25 },
                { component: 4, isProp: 0, drawable: 2095, price: 500,    slider: 1, items: 10, discount: 25 },
                { component: 4, isProp: 0, drawable: 2104, price: 500,    slider: 1, items: 8, discount: 20 },
                { component: 4, isProp: 0, drawable: 2107, price: 800,    slider: 1, items: 8, discount: 10 },
                { component: 4, isProp: 0, drawable: 2108, price: 1500,   slider: 1, items: 16, discount: 25 },
                { component: 4, isProp: 0, drawable: 2109, price: 1500,   slider: 1, items: 91, discount: 30 },
                { component: 4, isProp: 0, drawable: 2116, price: 300,    slider: 1, items: 9, discount: 30 },
                { component: 4, isProp: 0, drawable: 2117, price: 500,    slider: 1, items: 22, discount: 15 },
                { component: 4, isProp: 0, drawable: 2122, price: 500,    slider: 1, items: 8, discount: 40 },
                { component: 4, isProp: 0, drawable: 2123, price: 250,    slider: 1, items: 15, discount: 10 },
                { component: 4, isProp: 0, drawable: 2127, price: 500,    slider: 1, items: 10, discount: 40 },
                { component: 4, isProp: 0, drawable: 2131, price: 200,    slider: 1, items: 8, discount: 15 },
                { component: 4, isProp: 0, drawable: 2132, price: 500,    slider: 1, items: 8, discount: 35 },
                { component: 4, isProp: 0, drawable: 2134, price: 400,    slider: 1, items: 8, discount: 25 },
                { component: 4, isProp: 0, drawable: 2137, price: 400,    slider: 1, items: 8, discount: 30 },
                { component: 4, isProp: 0, drawable: 2140, price: 1250,   slider: 1, items: 20, discount: 35 },
                { component: 4, isProp: 0, drawable: 2142, price: 750,    slider: 1, items: 9, discount: 30 },
                { component: 4, isProp: 0, drawable: 2148, price: 1700,   slider: 1, items: 7, discount: 20 },
                { component: 4, isProp: 0, drawable: 2149, price: 350,    slider: 1, items: 9, discount: 20 },
                { component: 4, isProp: 0, drawable: 2238, price: 2000,   slider: 1, items: 6, discount: 40 },
                { component: 4, isProp: 0, drawable: 2242, price: 2000,   slider: 1, items: 6, discount: 35 },
                { component: 4, isProp: 0, drawable: 2007, price: 400,    slider: 1, items: 8, discount: 10 },
                { component: 4, isProp: 0, drawable: 2053, price: 750,    slider: 1, items: 12, discount: 10 },

                { component: 4, isProp: 0, drawable: 2243, price: 1500,   slider: 1, items: 3, server: 'delete' }, // @FOR_DELETE
                { component: 4, isProp: 0, drawable: 2059, price: 900,    slider: 1, items: 5, server: 'delete' }, // @FOR_DELETE
                { component: 4, isProp: 0, drawable: 2061, price: 1000,   slider: 1, items: 9, server: 'delete' }, // @FOR_DELETE
            ],

            shoes: [
                { component: 6, isProp: 0, drawable: 2001, price: 800,    slider: 1, items: 9, discount: 15 },
                { component: 6, isProp: 0, drawable: 2002, price: 800,    slider: 1, items: 7, discount: 25 },
                { component: 6, isProp: 0, drawable: 2003, price: 800,    slider: 1, items: 9, discount: 30 },
                { component: 6, isProp: 0, drawable: 2004, price: 800,    slider: 1, items: 7, discount: 20 },
                { component: 6, isProp: 0, drawable: 2005, price: 1200,   slider: 1, items: 4, discount: 15 },
                { component: 6, isProp: 0, drawable: 2006, price: 800,    slider: 1, items: 6, discount: 15 },
                { component: 6, isProp: 0, drawable: 2007, price: 800,    slider: 1, items: 10, discount: 10 },
                { component: 6, isProp: 0, drawable: 2008, price: 800,    slider: 1, items: 10, discount: 20 },
                { component: 6, isProp: 0, drawable: 2011, price: 1200,   slider: 1, items: 10, discount: 20 },
                { component: 6, isProp: 0, drawable: 2012, price: 400,    slider: 1, items: 10, discount: 15 },
                { component: 6, isProp: 0, drawable: 2013, price: 1200,   slider: 1, items: 7, discount: 35 },
                { component: 6, isProp: 0, drawable: 2014, price: 1900,   slider: 1, items: 16, discount: 10 },
                { component: 6, isProp: 0, drawable: 2017, price: 800,    slider: 1, items: 8, discount: 35 },
                { component: 6, isProp: 0, drawable: 2019, price: 1900,   slider: 1, items: 6, discount: 25 },
                { component: 6, isProp: 0, drawable: 2020, price: 1500,   slider: 1, items: 13, discount: 10 },
                { component: 6, isProp: 0, drawable: 2022, price: 1900,   slider: 1, items: 11, discount: 35 },
                { component: 6, isProp: 0, drawable: 2024, price: 800,    slider: 1, items: 8, discount: 30 },
                { component: 6, isProp: 0, drawable: 2026, price: 1200,   slider: 1, items: 8, discount: 30 },
                { component: 6, isProp: 0, drawable: 2028, price: 800,    slider: 1, items: 8, discount: 20 },
                { component: 6, isProp: 0, drawable: 2030, price: 1200,   slider: 1, items: 16, discount: 25 },
                { component: 6, isProp: 0, drawable: 2031, price: 800,    slider: 1, items: 16, discount: 35 },
                { component: 6, isProp: 0, drawable: 2032, price: 1200,   slider: 1, items: 10, discount: 35 },
                { component: 6, isProp: 0, drawable: 2033, price: 1900,   slider: 1, items: 16, discount: 30 },
                { component: 6, isProp: 0, drawable: 2035, price: 900,    slider: 1, items: 12, discount: 20 },
                { component: 6, isProp: 0, drawable: 2036, price: 1200,   slider: 1, items: 16, discount: 35 },
                { component: 6, isProp: 0, drawable: 2037, price: 800,    slider: 1, items: 16, discount: 25 },
                { component: 6, isProp: 0, drawable: 2038, price: 1500,   slider: 1, items: 8, discount: 30 },
                { component: 6, isProp: 0, drawable: 2041, price: 1900,   slider: 1, items: 3, discount: 30 },
                { component: 6, isProp: 0, drawable: 2046, price: 1900,   slider: 1, items: 16, discount: 30 },
                { component: 6, isProp: 0, drawable: 2050, price: 1350,   slider: 1, items: 15, discount: 30 },
                { component: 6, isProp: 0, drawable: 2051, price: 1750,   slider: 1, items: 15, discount: 25 },
                { component: 6, isProp: 0, drawable: 2056, price: 1000,   slider: 1, items: 9, discount: 30 },
                { component: 6, isProp: 0, drawable: 2057, price: 2000,   slider: 1, items: 2, discount: 10 },
                { component: 6, isProp: 0, drawable: 2059, price: 1000,   slider: 1, items: 9, discount: 20 },
                { component: 6, isProp: 0, drawable: 2113, price: 2000,   slider: 1, items: 10, discount: 35 },
                { component: 6, isProp: 0, drawable: 2114, price: 2000,   slider: 1, items: 12, discount: 35 },
                { component: 6, isProp: 0, drawable: 2123, price: 1000,   slider: 1, items: 16, discount: 35 },

                { component: 6, isProp: 0, drawable: 2042, price: 1500,   slider: 1, items: 6, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2034, price: 1900,   slider: 1, items: 9, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2027, price: 1200,   slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2055, price: 1500,   slider: 1, items: 6, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2021, price: 1500,   slider: 1, items: 6, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2025, price: 1200,   slider: 1, items: 12, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2029, price: 1900,   slider: 1, items: 16, server: 'delete' }, // @FOR_DELETE
            ],

            accessories: [
                { component: 7, isProp: 0, drawable: 2061, price: 5058,  slider: 1, items: 2, discount: 60 }, // Крид
                { component: 7, isProp: 0, drawable: 2062, price: 9958,  slider: 1, items: 1, discount: 60 }, // Крид
                //{component: 7, isProp: 0, drawable: 2063, price: 5058,  slider: 1, items: 2}, // Крид
                { component: 7, isProp: 0, drawable: 2024, price: 7500,   slider: 1, items: 4, discount: 25 },
                { component: 7, isProp: 0, drawable: 2026, price: 3800,   slider: 1, items: 6, discount: 30   },
                { component: 7, isProp: 0, drawable: 2034, price: 7500,   slider: 1, items: 1, discount: 25 },
                { component: 7, isProp: 0, drawable: 2035, price: 500,    slider: 1, items: 3, discount: 30   },
                { component: 7, isProp: 0, drawable: 2036, price: 250,    slider: 1, items: 3, discount: 30   },
                { component: 7, isProp: 0, drawable: 2039, price: 250,    slider: 1, items: 3, discount: 40   },
                { component: 7, isProp: 0, drawable: 2041, price: 500,    slider: 1, items: 3, discount: 50   },
                { component: 7, isProp: 0, drawable: 2043, price: 250,    slider: 1, items: 3, discount: 40   },
                { component: 7, isProp: 0, drawable: 2044, price: 500,    slider: 1, items: 3, discount: 40   },
                { component: 7, isProp: 0, drawable: 2045, price: 750,    slider: 1, items: 3, discount: 35   },
                { component: 7, isProp: 0, drawable: 2067, price: 500,    slider: 1, items: 25, discount: 5 },
                { component: 7, isProp: 0, drawable: 2069, price: 2000,   slider: 1, items: 1, discount: 35   },
                { component: 7, isProp: 0, drawable: 2070, price: 1500,   slider: 1, items: 1, discount: 25 },
                { component: 7, isProp: 0, drawable: 2071, price: 2500,   slider: 1, items: 1, discount: 40   },

                { component: 7, isProp: 0, drawable: 2144, price: 4444,  slider: 1, items: 1, discount: 60 }, // Benzo Chain
                { component: 7, isProp: 0, drawable: 2145, price: 4444,  slider: 1, items: 1, discount: 60 }, // Benzo Chain
                { component: 7, isProp: 0, drawable: 2146, price: 4444,  slider: 1, items: 1, discount: 60 }, // Benzo Chain
                { component: 7, isProp: 0, drawable: 2147, price: 4444,  slider: 1, items: 1, discount: 60 }, // Benzo Chain
                { component: 7, isProp: 0, drawable: 2148, price: 4444,  slider: 1, items: 1, discount: 60 }, // Benzo Chain
            ],

            masks: [
                { component: 1, isProp: 0, drawable: 2098, price: 5858,   slider: 1, items: 1, discount: 50 },  // Крид
                { component: 1, isProp: 0, drawable: 2005, price: 500,    slider: 1, items: 8, discount: 30 },
                { component: 1, isProp: 0, drawable: 2015, price: 250,    slider: 1, items: 9, discount: 25 },
                { component: 1, isProp: 0, drawable: 2023, price: 500,    slider: 1, items: 9, discount: 30 },
                { component: 1, isProp: 0, drawable: 2026, price: 500,    slider: 1, items: 10, discount: 35 },
                { component: 1, isProp: 0, drawable: 2029, price: 500,    slider: 1, items: 9, discount: 40   },
                { component: 1, isProp: 0, drawable: 2030, price: 500,    slider: 1, items: 8, discount: 20 },
                { component: 1, isProp: 0, drawable: 2031, price: 500,    slider: 1, items: 8, discount: 35 },
                { component: 1, isProp: 0, drawable: 2033, price: 500,    slider: 1, items: 8, discount: 15 },
                { component: 1, isProp: 0, drawable: 2034, price: 500,    slider: 1, items: 8, discount: 20 },
                { component: 1, isProp: 0, drawable: 2035, price: 500,    slider: 1, items: 8, discount: 25 },
                { component: 1, isProp: 0, drawable: 2037, price: 500,    slider: 1, items: 8, discount: 35 },
                { component: 1, isProp: 0, drawable: 2038, price: 500,    slider: 1, items: 8, discount: 20 },
                { component: 1, isProp: 0, drawable: 2039, price: 500,    slider: 1, items: 19, discount: 30  },
                { component: 1, isProp: 0, drawable: 2040, price: 250,    slider: 1, items: 9, discount: 15 },
                { component: 1, isProp: 0, drawable: 2041, price: 500,    slider: 1, items: 9, discount: 20 },
                { component: 1, isProp: 0, drawable: 2042, price: 500,    slider: 1, items: 8, discount: 25 },
                { component: 1, isProp: 0, drawable: 2046, price: 500,    slider: 1, items: 9, discount: 25 },
                { component: 1, isProp: 0, drawable: 2047, price: 500,    slider: 1, items: 9, discount: 25 },
                { component: 1, isProp: 0, drawable: 2051, price: 500,    slider: 1, items: 8, discount: 15 },
                { component: 1, isProp: 0, drawable: 2054, price: 375,    slider: 1, items: 14, discount: 15 },
                { component: 1, isProp: 0, drawable: 2055, price: 375,    slider: 1, items: 13, discount: 25 },
                { component: 1, isProp: 0, drawable: 2056, price: 500,    slider: 1, items: 62, discount: 5 },
                { component: 1, isProp: 0, drawable: 2057, price: 500,    slider: 1, items: 62, discount: 15 },
                { component: 1, isProp: 0, drawable: 2058, price: 980,    slider: 1, items: 62, discount: 10 },
                { component: 1, isProp: 0, drawable: 2060, price: 500,    slider: 1, items: 62, discount: 5 },
                { component: 1, isProp: 0, drawable: 2059, price: 500,    slider: 1, items: 62, discount: 25 },
                { component: 1, isProp: 0, drawable: 2061, price: 500,    slider: 1, items: 62, discount: 20 },
                { component: 1, isProp: 0, drawable: 2062, price: 500,    slider: 1, items: 62, discount: 25 },
                { component: 1, isProp: 0, drawable: 2064, price: 1000,   slider: 1, items: 17, discount: 10 },
                { component: 1, isProp: 0, drawable: 2065, price: 2000,   slider: 1, items: 17, discount: 15 },
                { component: 1, isProp: 0, drawable: 2079, price: 400,    slider: 1, items: 13, discount: 20 },
                { component: 1, isProp: 0, drawable: 2080, price: 500,    slider: 1, items: 8, discount: 20 },
                { component: 1, isProp: 0, drawable: 2081, price: 200,    slider: 1, items: 8, discount: 25 },
                { component: 1, isProp: 0, drawable: 2095, price: 2000,   slider: 1, items: 15, discount: 30  },
                { component: 1, isProp: 0, drawable: 2097, price: 1000,   slider: 1, items: 1, discount: 20 },
                { component: 1, isProp: 0, drawable: 2100, price: 1500,   slider: 1, items: 1, discount: 25 },
                { component: 1, isProp: 0, drawable: 2102, price: 500,    slider: 1, items: 12, discount: 15 },
                { component: 1, isProp: 0, drawable: 2105, price: 300,    slider: 1, items: 9, discount: 20 },
                { component: 1, isProp: 0, drawable: 2106, price: 250,    slider: 1, items: 20, discount: 5 },
                { component: 1, isProp: 0, drawable: 2111, price: 250,    slider: 1, items: 4, discount: 25 },
                { component: 1, isProp: 0, drawable: 2112, price: 200,    slider: 1, items: 15, discount: 10 },
                { component: 1, isProp: 0, drawable: 2116, price: 500,    slider: 1, items: 10, discount: 30  },
                { component: 1, isProp: 0, drawable: 2120, price: 500,    slider: 1, items: 8, discount: 25 },
                { component: 1, isProp: 0, drawable: 2121, price: 800,    slider: 1, items: 8, discount: 25 },
                { component: 1, isProp: 0, drawable: 2123, price: 250,    slider: 1, items: 2, discount: 15 },
                { component: 1, isProp: 0, drawable: 2126, price: 250,    slider: 1, items: 8, discount: 15 },
                { component: 1, isProp: 0, drawable: 2127, price: 1000,   slider: 1, items: 1, discount: 30 },
                { component: 1, isProp: 0, drawable: 2128, price: 100,    slider: 1, items: 13, discount: 10 },

                { component: 1, isProp: 0, drawable: 2093, price: 9999,   slider: 1, items: 1, server: 'halloween' }, // Halloween event
                { component: 1, isProp: 0, drawable: 2094, price: 9999,   slider: 1, items: 1, server: 'halloween' }, // Halloween event
                { component: 1, isProp: 0, drawable: 2096, price: 9999,   slider: 1, items: 1, server: 'halloween' }, // Halloween event
                // { component: 1, isProp: 0, drawable: 2099, price: 9999,  slider: 1, items: 20, server: 'seasonpass2022' },

                { component: 1, isProp: 0, drawable: 2024, price: 500,    slider: 1, items: 7, server: 'delete' },  // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2032, price: 500,    slider: 1, items: 9, server: 'delete' },  // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2036, price: 500,    slider: 1, items: 1, server: 'delete' },  // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2048, price: 500,    slider: 1, items: 9, server: 'delete' },  // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2027, price: 500,    slider: 1, items: 8, server: 'delete' },  // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2028, price: 500,    slider: 1, items: 8, server: 'delete' },  // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2049, price: 500,    slider: 1, items: 9, server: 'delete' },  // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2008, price: 250,    slider: 1, items: 7, server: 'delete' },  // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2025, price: 500,    slider: 1, items: 7, server: 'delete' },  // @FOR_DELETE
            ],

            ears: [
            ],

            glasses: [
                { component: 1, isProp: 1, drawable: 2005, price: 2058,  slider: 1, items: 1, discount: 25 },  // Крид

                { component: 1, isProp: 1, drawable: 2003, price: 1000,  slider: 1, items: 6, discount: 15 },
                { component: 1, isProp: 1, drawable: 2004, price: 750,   slider: 1, items: 10, discount: 15 },
                { component: 1, isProp: 1, drawable: 2029, price: 1000,   slider: 1, items: 8, discount: 40 },

                { component: 1, isProp: 1, drawable: 2030, price: 1500,  slider: 1, items: 6, server: 'delete' },  // @FOR_DELETE
                { component: 1, isProp: 1, drawable: 2032, price: 750,  slider: 1, items: 5, server: 'delete' },   // @FOR_DELETE
            ],

            watches: [
                { component: 6, isProp: 1, drawable: 2001, price: 9000,  slider: 1, items: 1, discount: 80 },  // Крид
                { component: 6, isProp: 1, drawable: 2002, price: 7000,  slider: 1, items: 1, discount: 80 },  // Крид
                //{component: 6, isProp: 1, drawable: 2003, price: 7000,  slider: 1, items: 1},  // Крид

                { component: 6, isProp: 1, drawable: 2000, price: 500,  slider: 1, items: 6, discount: 40 },
            ],

            decals: [
                { component: 10, isProp: 0, drawable: 2038, price: 2000,  slider: 1, items: 1, discount: 25 },
                { component: 10, isProp: 0, drawable: 2039, price: 750,   slider: 1, items: 12, discount: 15 },
                { component: 10, isProp: 0, drawable: 2099, price: 3000,   slider: 1, items: 7, discount: 40 },

                { component: 10, isProp: 0, drawable: 2095, price: 9999,  slider: 1, items: 1, server: 'kinguin' },

            ],

            bags: [
                { component: 5, isProp: 0, drawable: 2010, price: 1000,  slider: 1, items: 1, discount: 35 },
                { component: 5, isProp: 0, drawable: 2011, price: 1750,  slider: 1, items: 7, discount: 10 },
                { component: 5, isProp: 0, drawable: 2013, price: 1500,  slider: 1, items: 6, discount: 20 },
                { component: 5, isProp: 0, drawable: 2014, price: 1500,  slider: 1, items: 3, discount: 25 },
                { component: 5, isProp: 0, drawable: 2018, price: 1500,  slider: 1, items: 4, discount: 10 },
                { component: 5, isProp: 0, drawable: 2019, price: 1500,  slider: 1, items: 7, discount: 15 },
                { component: 5, isProp: 0, drawable: 2081, price: 1750,  slider: 1, items: 7, discount: 40 },
                { component: 5, isProp: 0, drawable: 2082, price: 1250,  slider: 1, items: 8, discount: 30 },
                { component: 5, isProp: 0, drawable: 2085, price: 1750,  slider: 1, items: 13, discount: 45 },
                { component: 5, isProp: 0, drawable: 2086, price: 2000,  slider: 1, items: 10, discount: 40 },
                { component: 5, isProp: 0, drawable: 2087, price: 2000,  slider: 1, items: 10, discount: 45 },
                { component: 5, isProp: 0, drawable: 2088, price: 1500,  slider: 1, items: 10, discount: 30 },

                { component: 5, isProp: 0, drawable: 2091, price: 9999,  slider: 1, items: 10, server: '2088action' },
            ]
        },

        // female
        {
            head: [
                { component: 0, isProp: 1, drawable: 2035, price: 1500,  slider: 1, items: 3, discount: 50  }, // Крид
                { component: 0, isProp: 1, drawable: 2036, price: 1500,  slider: 1, items: 3, discount: 50  }, // Крид

                { component: 0, isProp: 1, drawable: 2009, price: 7500,  slider: 1, items: 1, discount: 25 },
                { component: 0, isProp: 1, drawable: 2010, price: 3800,  slider: 1, items: 1, discount: 20   },
                { component: 0, isProp: 1, drawable: 2011, price: 1500,  slider: 1, items: 7, discount: 40 },
                { component: 0, isProp: 1, drawable: 2012, price: 1900,  slider: 1, items: 6, discount: 20 },
                { component: 0, isProp: 1, drawable: 2013, price: 5700,  slider: 1, items: 15, discount: 40 },
                { component: 0, isProp: 1, drawable: 2014, price: 1500,  slider: 1, items: 15, discount: 50   },
                { component: 0, isProp: 1, drawable: 2015, price: 1500,  slider: 1, items: 15, discount: 50 },
                { component: 0, isProp: 1, drawable: 2016, price: 4000,  slider: 1, items: 20, discount: 30 },
                { component: 0, isProp: 1, drawable: 2017, price: 2500,  slider: 1, items: 8, discount: 40 },
                { component: 0, isProp: 1, drawable: 2018, price: 4000,  slider: 1, items: 20, discount: 40 },
                { component: 0, isProp: 1, drawable: 2023, price: 2500,  slider: 1, items: 5, discount: 50 },
                { component: 0, isProp: 1, drawable: 2026, price: 1500,  slider: 1, items: 13, discount: 30 },
                { component: 0, isProp: 1, drawable: 2028, price: 2000,  slider: 1, items: 7, discount: 25 },
                { component: 0, isProp: 1, drawable: 2031, price: 1000,  slider: 1, items: 5, discount: 25 },
                { component: 0, isProp: 1, drawable: 2033, price: 500,   slider: 1, items: 1, discount: 30 },
                { component: 0, isProp: 1, drawable: 2037, price: 1500,  slider: 1, items: 1, discount: 50 },
            ],

            tops: [
                { component: 11, isProp: 0, drawable: 2196, slider: 1,    items: 2,   price: 2558, discount: 50 },  // Крид
                { component: 11, isProp: 0, drawable: 2192, slider: 1,    items: 2,   price: 1500, discount: 30 },  // Крид
                { component: 11, isProp: 0, drawable: 2193, texture: 4,   slider: 0,  price: 1500, discount: 60 },  // Крид
                { component: 11, isProp: 0, drawable: 2193, texture: 3,   slider: 0,  price: 1500, discount: 60 },  // Крид
                { component: 11, isProp: 0, drawable: 2193, texture: 0,   slider: 0,  price: 2000, discount: 60 },  // Крид
                { component: 11, isProp: 0, drawable: 2193, texture: 1,   slider: 0,  price: 2000, discount: 60 },  // Крид
                { component: 11, isProp: 0, drawable: 2198, slider: 1,    items: 2,   price: 1558, discount: 50 },  // Крид

                { component: 11, isProp: 0, drawable: 2022, price: 800,    slider: 1, items: 6, discount: 35 },
                { component: 11, isProp: 0, drawable: 2026, price: 800,    slider: 1, items: 8, discount: 30 },
                { component: 11, isProp: 0, drawable: 2033, price: 600,    slider: 1, items: 16, discount: 30 },
                { component: 11, isProp: 0, drawable: 2039, price: 400,    slider: 1, items: 5, discount: 10 },
                { component: 11, isProp: 0, drawable: 2040, price: 1500,   slider: 1, items: 9, discount: 30 },
                { component: 11, isProp: 0, drawable: 2042, price: 1500,   slider: 1, items: 8, discount: 10 },
                { component: 11, isProp: 0, drawable: 2043, price: 800,    slider: 1, items: 20, discount: 30 },
                { component: 11, isProp: 0, drawable: 2044, price: 600,    slider: 1, items: 25, discount: 20 },
                { component: 11, isProp: 0, drawable: 2045, price: 800,    slider: 1, items: 1, discount: 25 },
                { component: 11, isProp: 0, drawable: 2050, price: 1500,   slider: 1, items: 8, discount: 35 },
                { component: 11, isProp: 0, drawable: 2072, price: 3000,   slider: 1, items: 22, discount: 30 },
                { component: 11, isProp: 0, drawable: 2073, price: 1000,   slider: 1, items: 12, discount: 30 },
                { component: 11, isProp: 0, drawable: 2074, price: 700,    slider: 1, items: 7, discount: 35 },
                { component: 11, isProp: 0, drawable: 2099, price: 700,    slider: 1, items: 8, discount: 30 },
                { component: 11, isProp: 0, drawable: 2100, price: 1200,   slider: 1, items: 15, discount: 25 },
                { component: 11, isProp: 0, drawable: 2101, price: 700,    slider: 1, items: 17, discount: 25 },
                { component: 11, isProp: 0, drawable: 2104, price: 1450,   slider: 1, items: 24, discount: 15 },
                { component: 11, isProp: 0, drawable: 2107, price: 700,    slider: 1, items: 10, discount: 25 },
                { component: 11, isProp: 0, drawable: 2113, price: 900,    slider: 1, items: 10, discount: 35 },
                { component: 11, isProp: 0, drawable: 2114, price: 900,    slider: 1, items: 8, discount: 30 },
                { component: 11, isProp: 0, drawable: 2116, price: 500,    slider: 1, items: 9, discount: 10 },
                { component: 11, isProp: 0, drawable: 2117, price: 1000,   slider: 1, items: 10, discount: 35 },
                { component: 11, isProp: 0, drawable: 2119, price: 1000,   slider: 1, items: 15, discount: 35 },
                { component: 11, isProp: 0, drawable: 2122, price: 1500,   slider: 1, items: 20, discount: 30 },
                { component: 11, isProp: 0, drawable: 2124, price: 800,    slider: 1, items: 23, discount: 30 },
                { component: 11, isProp: 0, drawable: 2125, price: 1200,   slider: 1, items: 15, discount: 30 },
                { component: 11, isProp: 0, drawable: 2126, price: 800,    slider: 1, items: 19, discount: 30 },
                { component: 11, isProp: 0, drawable: 2128, price: 2000,   slider: 1, items: 68, discount: 30 },
                { component: 11, isProp: 0, drawable: 2129, price: 1500,   slider: 1, items: 54, discount: 35 },
                { component: 11, isProp: 0, drawable: 2130, price: 500,    slider: 1, items: 20, discount: 10 },
                { component: 11, isProp: 0, drawable: 2134, price: 1500,   slider: 1, items: 14, discount: 30 },
                { component: 11, isProp: 0, drawable: 2136, price: 1200,   slider: 1, items: 112, discount: 25 },
                { component: 11, isProp: 0, drawable: 2150, price: 400,    slider: 1, items: 13, discount: 25 },
                { component: 11, isProp: 0, drawable: 2151, price: 1000,   slider: 1, items: 11, discount: 10 },
                { component: 11, isProp: 0, drawable: 2164, price: 1000,   slider: 1, items: 8, discount: 30 },
                { component: 11, isProp: 0, drawable: 2167, price: 1500,   slider: 1, items: 15, discount: 25 },
                { component: 11, isProp: 0, drawable: 2168, price: 1500,   slider: 1, items: 15, discount: 25 },
                { component: 11, isProp: 0, drawable: 2169, price: 750,    slider: 1, items: 15, discount: 30 },
                { component: 11, isProp: 0, drawable: 2172, price: 500,    slider: 1, items: 9, discount: 25 },
                { component: 11, isProp: 0, drawable: 2177, price: 1250,   slider: 1, items: 23, discount: 30 },
                { component: 11, isProp: 0, drawable: 2185, price: 1250,   slider: 1, items: 9, discount: 25 },
                { component: 11, isProp: 0, drawable: 2186, price: 450,    slider: 1, items: 33, discount: 35 },
                { component: 11, isProp: 0, drawable: 2193, price: 1500,   slider: 1, items: 1, discount: 40 },
                { component: 11, isProp: 0, drawable: 2200, price: 1500,   slider: 1, items: 38, discount: 20 },
                { component: 11, isProp: 0, drawable: 2201, price: 1500,   slider: 1, items: 1, discount: 25 },
                { component: 11, isProp: 0, drawable: 2203, price: 750,    slider: 1, items: 8, discount: 30 },
                { component: 11, isProp: 0, drawable: 2206, price: 2250,   slider: 1, items: 5, discount: 30 },
                { component: 11, isProp: 0, drawable: 2211, price: 750,    slider: 1, items: 10, discount: 40 },
                { component: 11, isProp: 0, drawable: 2213, price: 1500,   slider: 1, items: 10, discount: 30 },
                { component: 11, isProp: 0, drawable: 2216, price: 1500,   slider: 1, items: 1, discount: 35 },
                { component: 11, isProp: 0, drawable: 2218, price: 1500,   slider: 1, items: 1, discount: 30 },
                { component: 11, isProp: 0, drawable: 2219, price: 1000,   slider: 1, items: 1, discount: 25 },
                { component: 11, isProp: 0, drawable: 2221, price: 1500,   slider: 1, items: 1, discount: 40 },
                { component: 11, isProp: 0, drawable: 2226, price: 300,    slider: 1, items: 9, discount: 35 },
                { component: 11, isProp: 0, drawable: 2227, price: 1000,   slider: 1, items: 11, discount: 15 },
                { component: 11, isProp: 0, drawable: 2233, price: 400,    slider: 1, items: 15, discount: 25 },
                { component: 11, isProp: 0, drawable: 2237, price: 500,    slider: 1, items: 10, discount: 40 },
                { component: 11, isProp: 0, drawable: 2241, price: 500,    slider: 1, items: 8, discount: 35 },
                { component: 11, isProp: 0, drawable: 2242, price: 500,    slider: 1, items: 8, discount: 40 },
                { component: 11, isProp: 0, drawable: 2243, price: 400,    slider: 1, items: 8, discount: 35 },
                { component: 11, isProp: 0, drawable: 2244, price: 400,    slider: 1, items: 8, discount: 35 },
                { component: 11, isProp: 0, drawable: 2247, price: 800,    slider: 1, items: 8, discount: 35 },
                { component: 11, isProp: 0, drawable: 2252, price: 500,    slider: 1, items: 6, discount: 25 },
                { component: 11, isProp: 0, drawable: 2260, price: 1250,   slider: 1, items: 1, discount: 30 },
                { component: 11, isProp: 0, drawable: 2261, price: 500,    slider: 1, items: 5, discount: 30 },
                { component: 11, isProp: 0, drawable: 2262, price: 300,    slider: 1, items: 7, discount: 15 },
                { component: 11, isProp: 0, drawable: 2263, price: 350,    slider: 1, items: 8, discount: 25 },
                { component: 11, isProp: 0, drawable: 2264, price: 300,    slider: 1, items: 9, discount: 30 },
                { component: 11, isProp: 0, drawable: 2365, price: 300,    slider: 1, items: 1, discount: 10 },
                { component: 11, isProp: 0, drawable: 2462, price: 1250,   slider: 1, items: 6, discount: 40 },
                { component: 11, isProp: 0, drawable: 2469, price: 1500,   slider: 1, items: 10, discount: 40 },
                { component: 11, isProp: 0, drawable: 2437, price: 3000,  slider: 1, items: 1, discount: 50 }, // Benzo hoodie 2024
                { component: 11, isProp: 0, drawable: 2438, price: 3000,  slider: 1, items: 1, discount: 50 }, // Benzo hoodie 2024

                { component: 11, isProp: 0, drawable: 2389, price: 9999,  slider: 1, items: 1, server: 'launcherhoodie' },
                { component: 11, isProp: 0, drawable: 2470, price: 9999,  slider: 1, items: 9, server: '2469action' },
                { component: 11, isProp: 0, drawable: 2049, price: 9999,  slider: 1, items: 8, server: '2048Action' },
                { component: 11, isProp: 0, drawable: 2458, price: 9999,   slider: 1, items: 1, server: 'SummerEventHoodie' }, // Summer Event 2024

                { component: 11, isProp: 0, drawable: 2070, price: 1000,   slider: 1, items: 9, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2152, price: 500,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2137, price: 1000,   slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2025, price: 800,    slider: 1, items: 11, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2029, price: 800,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2027, price: 600,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2078, price: 600,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2041, price: 1900,   slider: 1, items: 26, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2052, price: 1000,   slider: 1, items: 12, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2254, price: 1000,   slider: 1, items: 1, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2034, price: 800,    slider: 1, items: 11, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2046, price: 1500,   slider: 1, items: 11, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2048, price: 800,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2232, price: 500,    slider: 1, items: 9, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2055, price: 1000,   slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2062, price: 500,    slider: 1, items: 10, server: 'delete' }, // @FOR_DELETE
                { component: 11, isProp: 0, drawable: 2047, price: 1500,   slider: 1, items: 11, server: 'delete' }, // @FOR_DELETE
            ],

            legs: [
                { component: 4, isProp: 0, drawable: 2005, price: 400,    slider: 1, items: 6, discount: 30 },
                { component: 4, isProp: 0, drawable: 2008, price: 800,    slider: 1, items: 7, discount: 20 },
                { component: 4, isProp: 0, drawable: 2009, price: 800,    slider: 1, items: 7, discount: 30 },
                { component: 4, isProp: 0, drawable: 2010, price: 400,    slider: 1, items: 3, discount: 20 },
                { component: 4, isProp: 0, drawable: 2011, price: 800,    slider: 1, items: 7, discount: 10 },
                { component: 4, isProp: 0, drawable: 2012, price: 400,    slider: 1, items: 15, discount: 15 },
                { component: 4, isProp: 0, drawable: 2013, price: 1000,   slider: 1, items: 24, discount: 10 },
                { component: 4, isProp: 0, drawable: 2016, price: 600,    slider: 1, items: 3, discount: 20 },
                { component: 4, isProp: 0, drawable: 2017, price: 1200,   slider: 1, items: 8, discount: 25 },
                { component: 4, isProp: 0, drawable: 2036, price: 500,    slider: 1, items: 9, discount: 35 },
                { component: 4, isProp: 0, drawable: 2048, price: 300,    slider: 1, items: 6, discount: 25 },
                { component: 4, isProp: 0, drawable: 2049, price: 350,    slider: 1, items: 16, discount: 10 },
                { component: 4, isProp: 0, drawable: 2050, price: 500,    slider: 1, items: 16, discount: 15 },
                { component: 4, isProp: 0, drawable: 2051, price: 750,    slider: 1, items: 16, discount: 10 },
                { component: 4, isProp: 0, drawable: 2052, price: 500,    slider: 1, items: 7, discount: 25 },
                { component: 4, isProp: 0, drawable: 2054, price: 300,    slider: 1, items: 5, discount: 25 },
                { component: 4, isProp: 0, drawable: 2055, price: 800,    slider: 1, items: 10, discount: 20 },
                { component: 4, isProp: 0, drawable: 2056, price: 1500,   slider: 1, items: 10, discount: 20 },
                { component: 4, isProp: 0, drawable: 2060, price: 900,    slider: 1, items: 24, discount: 30 },
                { component: 4, isProp: 0, drawable: 2061, price: 1200,   slider: 1, items: 22, discount: 30 },
                { component: 4, isProp: 0, drawable: 2064, price: 800,    slider: 1, items: 18, discount: 25 },
                { component: 4, isProp: 0, drawable: 2079, price: 400,    slider: 1, items: 13, discount: 25 },
                { component: 4, isProp: 0, drawable: 2080, price: 1000,   slider: 1, items: 11, discount: 20 },
                { component: 4, isProp: 0, drawable: 2098, price: 1000,   slider: 1, items: 9, discount: 30 },
                { component: 4, isProp: 0, drawable: 2103, price: 1000,   slider: 1, items: 14, discount: 10 },
                { component: 4, isProp: 0, drawable: 2109, price: 1500,   slider: 1, items: 12, discount: 40 },
                { component: 4, isProp: 0, drawable: 2111, price: 1000,   slider: 1, items: 30, discount: 35 },
                { component: 4, isProp: 0, drawable: 2113, price: 1000,   slider: 1, items: 12, discount: 40 },
                { component: 4, isProp: 0, drawable: 2114, price: 1000,   slider: 1, items: 10, discount: 35 },
                { component: 4, isProp: 0, drawable: 2116, price: 1000,   slider: 1, items: 10, discount: 25 },
                { component: 4, isProp: 0, drawable: 2118, price: 1000,   slider: 1, items: 22, discount: 30 },
                { component: 4, isProp: 0, drawable: 2124, price: 300,    slider: 1, items: 9, discount: 30 },
                { component: 4, isProp: 0, drawable: 2125, price: 500,    slider: 1, items: 22, discount: 25 },
                { component: 4, isProp: 0, drawable: 2130, price: 500,    slider: 1, items: 8, discount: 40 },
                { component: 4, isProp: 0, drawable: 2131, price: 250,    slider: 1, items: 15, discount: 20 },
                { component: 4, isProp: 0, drawable: 2139, price: 200,    slider: 1, items: 8, discount: 30 },
                { component: 4, isProp: 0, drawable: 2141, price: 400,    slider: 1, items: 8, discount: 35 },
                { component: 4, isProp: 0, drawable: 2142, price: 400,    slider: 1, items: 8, discount: 40 },
                { component: 4, isProp: 0, drawable: 2145, price: 400,    slider: 1, items: 8, discount: 35 },
                { component: 4, isProp: 0, drawable: 2147, price: 500,    slider: 1, items: 6, discount: 30 },
                { component: 4, isProp: 0, drawable: 2149, price: 500,    slider: 1, items: 8, discount: 30 },
                { component: 4, isProp: 0, drawable: 2153, price: 750,    slider: 1, items: 1, discount: 25 },
                { component: 4, isProp: 0, drawable: 2256, price: 1000,   slider: 1, items: 6, discount: 40 },

                { component: 4, isProp: 0, drawable: 2104, price: 500,    slider: 1, items: 15, server: 'delete' }, // @FOR_DELETE
                { component: 4, isProp: 0, drawable: 2135, price: 500,    slider: 1, items: 10, server: 'delete' }, // @FOR_DELETE
                { component: 4, isProp: 0, drawable: 2140, price: 500,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 4, isProp: 0, drawable: 2015, price: 800,    slider: 1, items: 3, server: 'delete' }, // @FOR_DELETE
                { component: 4, isProp: 0, drawable: 2154, price: 500,    slider: 1, items: 6, server: 'delete' }, // @FOR_DELETE
                { component: 4, isProp: 0, drawable: 2081, price: 500,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 4, isProp: 0, drawable: 2022, price: 500,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 4, isProp: 0, drawable: 2029, price: 500,    slider: 1, items: 10, server: 'delete' }, // @FOR_DELETE
            ],

            shoes: [
                { component: 6, isProp: 0, drawable: 2001, price: 800,    slider: 1, items: 10, discount: 25 },
                { component: 6, isProp: 0, drawable: 2002, price: 800,    slider: 1, items: 10, discount: 15 },
                { component: 6, isProp: 0, drawable: 2004, price: 800,    slider: 1, items: 6, discount: 15 },
                { component: 6, isProp: 0, drawable: 2005, price: 800,    slider: 1, items: 10, discount: 10 },
                { component: 6, isProp: 0, drawable: 2006, price: 800,    slider: 1, items: 9, discount: 10 },
                { component: 6, isProp: 0, drawable: 2007, price: 800,    slider: 1, items: 7, discount: 20 },
                { component: 6, isProp: 0, drawable: 2008, price: 800,    slider: 1, items: 9, discount: 30 },
                { component: 6, isProp: 0, drawable: 2009, price: 800,    slider: 1, items: 7, discount: 10 },
                { component: 6, isProp: 0, drawable: 2010, price: 1200,   slider: 1, items: 4, discount: 20 },
                { component: 6, isProp: 0, drawable: 2013, price: 1200,   slider: 1, items: 10, discount: 25 },
                { component: 6, isProp: 0, drawable: 2015, price: 400,    slider: 1, items: 6, discount: 30 },
                { component: 6, isProp: 0, drawable: 2016, price: 1900,   slider: 1, items: 16, discount: 10 },
                { component: 6, isProp: 0, drawable: 2021, price: 1900,   slider: 1, items: 6, discount: 25 },
                { component: 6, isProp: 0, drawable: 2022, price: 1500,   slider: 1, items: 13, discount: 10 },
                { component: 6, isProp: 0, drawable: 2030, price: 800,    slider: 1, items: 8, discount: 20 },
                { component: 6, isProp: 0, drawable: 2032, price: 1200,   slider: 1, items: 16, discount: 30 },
                { component: 6, isProp: 0, drawable: 2033, price: 800,    slider: 1, items: 16, discount: 40 },
                { component: 6, isProp: 0, drawable: 2035, price: 1900,   slider: 1, items: 16, discount: 40 },
                { component: 6, isProp: 0, drawable: 2036, price: 1900,   slider: 1, items: 9, discount: 40 },
                { component: 6, isProp: 0, drawable: 2037, price: 900,    slider: 1, items: 12, discount: 15 },
                { component: 6, isProp: 0, drawable: 2039, price: 800,    slider: 1, items: 16, discount: 20 },
                { component: 6, isProp: 0, drawable: 2040, price: 1500,   slider: 1, items: 8, discount: 30 },
                { component: 6, isProp: 0, drawable: 2043, price: 1900,   slider: 1, items: 3, discount: 20 },
                { component: 6, isProp: 0, drawable: 2048, price: 1900,   slider: 1, items: 16, discount: 35 },
                { component: 6, isProp: 0, drawable: 2053, price: 1750,   slider: 1, items: 15, discount: 35 },
                { component: 6, isProp: 0, drawable: 2059, price: 2500,   slider: 1, items: 2, discount: 30 },
                { component: 6, isProp: 0, drawable: 2061, price: 1000,   slider: 1, items: 9, discount: 20 },
                { component: 6, isProp: 0, drawable: 2063, price: 1000,   slider: 1, items: 9, discount: 15 },
                { component: 6, isProp: 0, drawable: 2134, price: 2000,   slider: 1, items: 10, discount: 25 },
                { component: 6, isProp: 0, drawable: 2135, price: 1500,   slider: 1, items: 12, discount: 20 },
                { component: 6, isProp: 0, drawable: 2136, price: 2000,   slider: 1, items: 12, discount: 40 },
                { component: 6, isProp: 0, drawable: 2146, price: 1000,   slider: 1, items: 16, discount: 35 },

                { component: 6, isProp: 0, drawable: 2026, price: 800,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2027, price: 1200,   slider: 1, items: 12, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2028, price: 1200,   slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2029, price: 1200,   slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2034, price: 1200,   slider: 1, items: 10, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2038, price: 1200,   slider: 1, items: 16, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2044, price: 1500,   slider: 1, items: 6, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2014, price: 1200,   slider: 1, items: 7, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2019, price: 800,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2023, price: 1500,   slider: 1, items: 6, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2024, price: 1900,   slider: 1, items: 11, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2031, price: 1900,   slider: 1, items: 16, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2052, price: 1350,   slider: 1, items: 15, server: 'delete' }, // @FOR_DELETE
                { component: 6, isProp: 0, drawable: 2058, price: 1500,   slider: 1, items: 6, server: 'delete' }, // @FOR_DELETE
            ],

            accessories: [
                { component: 7, isProp: 0, drawable: 2049, price: 5058,  slider: 1, items: 2, discount: 60 }, // Крид
                { component: 7, isProp: 0, drawable: 2050, price: 9958,  slider: 1, items: 1, discount: 60 }, // Крид
                //{component: 7, isProp: 0, drawable: 2051, price: 5058,  slider: 1, items: 2}, // Крид

                { component: 7, isProp: 0, drawable: 2014, price: 7500,   slider: 1, items: 3, discount: 15 },
                { component: 7, isProp: 0, drawable: 2015, price: 3800,   slider: 1, items: 6, discount: 10 },
                { component: 7, isProp: 0, drawable: 2022, price: 7500,   slider: 1, items: 1, discount: 30 },
                { component: 7, isProp: 0, drawable: 2025, price: 1000,   slider: 1, items: 4, discount: 40 },
                { component: 7, isProp: 0, drawable: 2027, price: 500,    slider: 1, items: 2, discount: 20 },
                { component: 7, isProp: 0, drawable: 2028, price: 1000,   slider: 1, items: 4, discount: 40 },
                { component: 7, isProp: 0, drawable: 2029, price: 500,    slider: 1, items: 2, discount: 15 },
                { component: 7, isProp: 0, drawable: 2030, price: 750,    slider: 1, items: 4, discount: 25 },
                { component: 7, isProp: 0, drawable: 2031, price: 500,    slider: 1, items: 2, discount: 20 },
                { component: 7, isProp: 0, drawable: 2032, price: 750,    slider: 1, items: 5, discount: 20 },
                { component: 7, isProp: 0, drawable: 2033, price: 750,    slider: 1, items: 3, discount: 40 },
                { component: 7, isProp: 0, drawable: 2044, price: 1000,   slider: 1, items: 4, discount: 25 },
                { component: 7, isProp: 0, drawable: 2047, price: 750,    slider: 1, items: 1, discount: 35 },
                { component: 7, isProp: 0, drawable: 2048, price: 750,    slider: 1, items: 1, discount: 10 },
                { component: 7, isProp: 0, drawable: 2057, price: 1500,   slider: 1, items: 1, discount: 40 },
                { component: 7, isProp: 0, drawable: 2054, price: 500,    slider: 1, items: 24, discount: 5 },
                { component: 7, isProp: 0, drawable: 2058, price: 2500,   slider: 1, items: 1, discount: 40 },

                { component: 7, isProp: 0, drawable: 2122, price: 2222,  slider: 1, items: 1, discount: 50 }, // Benzo Accessories 2024
                { component: 7, isProp: 0, drawable: 2123, price: 2222,  slider: 1, items: 1, discount: 50 }, // Benzo Accessories 2024
                { component: 7, isProp: 0, drawable: 2124, price: 2222,  slider: 1, items: 1, discount: 50 }, // Benzo Accessories 2024
                { component: 7, isProp: 0, drawable: 2125, price: 2222,  slider: 1, items: 1, discount: 50 }, // Benzo Accessories 2024
                { component: 7, isProp: 0, drawable: 2126, price: 2222,  slider: 1, items: 1, discount: 50 }, // Benzo Accessories 2024

                { component: 7, isProp: 0, drawable: 2056, price: 2000,   slider: 1, items: 1, server: 'delete' }, // @FOR_DELETE
            ],

            masks: [
                { component: 1, isProp: 0, drawable: 2087, price: 4000,  slider: 1, items: 1, discount: 50 },  // Крид

                { component: 1, isProp: 0, drawable: 2015, price: 250,    slider: 1, items: 9, discount: 30 },
                { component: 1, isProp: 0, drawable: 2023, price: 500,    slider: 1, items: 9, discount: 40 },
                { component: 1, isProp: 0, drawable: 2026, price: 500,    slider: 1, items: 10, discount: 35 },
                { component: 1, isProp: 0, drawable: 2028, price: 500,    slider: 1, items: 8, discount: 40 },
                { component: 1, isProp: 0, drawable: 2029, price: 500,    slider: 1, items: 9, discount: 40 },
                { component: 1, isProp: 0, drawable: 2030, price: 500,    slider: 1, items: 8, discount: 40 },
                { component: 1, isProp: 0, drawable: 2033, price: 500,    slider: 1, items: 8, discount: 35 },
                { component: 1, isProp: 0, drawable: 2034, price: 500,    slider: 1, items: 8, discount: 20 },
                { component: 1, isProp: 0, drawable: 2035, price: 500,    slider: 1, items: 8, discount: 20 },
                { component: 1, isProp: 0, drawable: 2038, price: 500,    slider: 1, items: 8, discount: 20 },
                { component: 1, isProp: 0, drawable: 2039, price: 500,    slider: 1, items: 19, discount: 35 },
                { component: 1, isProp: 0, drawable: 2040, price: 250,    slider: 1, items: 9, discount: 20 },
                { component: 1, isProp: 0, drawable: 2041, price: 500,    slider: 1, items: 9, discount: 25 },
                { component: 1, isProp: 0, drawable: 2047, price: 500,    slider: 1, items: 9, discount: 30 },
                { component: 1, isProp: 0, drawable: 2049, price: 500,    slider: 1, items: 9, discount: 40 },
                { component: 1, isProp: 0, drawable: 2051, price: 500,    slider: 1, items: 8, discount: 15 },
                { component: 1, isProp: 0, drawable: 2054, price: 375,    slider: 1, items: 14, discount: 25 },
                { component: 1, isProp: 0, drawable: 2056, price: 1000,   slider: 1, items: 17, discount: 5 },
                { component: 1, isProp: 0, drawable: 2057, price: 2000,   slider: 1, items: 17, discount: 10 },
                { component: 1, isProp: 0, drawable: 2071, price: 400,    slider: 1, items: 13, discount: 20 },
                { component: 1, isProp: 0, drawable: 2072, price: 1000,   slider: 1, items: 8, discount: 35 },
                { component: 1, isProp: 0, drawable: 2085, price: 1000,   slider: 1, items: 15, discount: 40 },
                { component: 1, isProp: 0, drawable: 2093, price: 250,    slider: 1, items: 20, discount: 15 },
                { component: 1, isProp: 0, drawable: 2110, price: 250,    slider: 1, items: 8, discount: 20 },
                { component: 1, isProp: 0, drawable: 2114, price: 500,   slider: 1, items: 1, discount: 40 },
                { component: 1, isProp: 0, drawable: 2115, price: 100,    slider: 1, items: 13, discount: 10 },

                { component: 1, isProp: 0, drawable: 2084, price: 9999,   slider: 1, items: 1, server: 'halloween' }, // Halloween event
                { component: 1, isProp: 0, drawable: 2086, price: 9999,   slider: 1, items: 1, server: 'halloween' }, // Halloween event

                { component: 1, isProp: 0, drawable: 2073, price: 200,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2042, price: 500,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2046, price: 500,    slider: 1, items: 9, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2048, price: 500,    slider: 1, items: 9, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2036, price: 500,    slider: 1, items: 1, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2037, price: 500,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2031, price: 500,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2032, price: 500,    slider: 1, items: 9, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2005, price: 500,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2008, price: 250,    slider: 1, items: 7, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2024, price: 500,    slider: 1, items: 7, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2025, price: 500,    slider: 1, items: 7, server: 'delete' }, // @FOR_DELETE
                { component: 1, isProp: 0, drawable: 2027, price: 500,    slider: 1, items: 8, server: 'delete' }, // @FOR_DELETE
            ],

            ears: [
                { component: 2, isProp: 1, drawable: 2001, price: 250,   slider: 1, items: 3, discount: 15 },
                { component: 2, isProp: 1, drawable: 2002, price: 250,   slider: 1, items: 2, discount: 10 },
                { component: 2, isProp: 1, drawable: 2004, price: 250,   slider: 1, items: 3, discount: 10 },
                { component: 2, isProp: 1, drawable: 2007, price: 250,   slider: 1, items: 4, discount: 20 },
                { component: 2, isProp: 1, drawable: 2008, price: 250,   slider: 1, items: 1, discount: 30 },
                { component: 2, isProp: 1, drawable: 2009, price: 250,   slider: 1, items: 2, discount: 30 },
                { component: 2, isProp: 1, drawable: 2010, price: 750,   slider: 1, items: 20, discount: 20 },
            ],

            glasses: [
                { component: 1, isProp: 1, drawable: 2005, price: 2058,  slider: 1, items: 1, discount: 10 },  // Крид

                { component: 1, isProp: 1, drawable: 2003, price: 1000,  slider: 1, items: 6, discount: 20 },
                { component: 1, isProp: 1, drawable: 2004, price: 750,  slider: 1, items: 10, discount: 10 },
                { component: 1, isProp: 1, drawable: 2030, price: 1000,  slider: 1, items: 8, discount: 20 },
                { component: 1, isProp: 1, drawable: 2031, price: 1000,  slider: 1, items: 6, discount: 30 },
                { component: 1, isProp: 1, drawable: 2033, price: 750,  slider: 1, items: 5, discount: 30 },
            ],

            watches: [
                { component: 6, isProp: 1, drawable: 2000, price: 500,  slider: 1, items: 6, discount: 10 },
                { component: 6, isProp: 1, drawable: 2001, price: 5000,  slider: 1, items: 1, discount: 20 },  // Крид
                { component: 6, isProp: 1, drawable: 2002, price: 2500,  slider: 1, items: 1, discount: 20 },  // Крид
                //{component: 6, isProp: 1, drawable: 2003, price: 7000,  slider: 1, items: 1},  // Крид

            ],

            decals: [
                { component: 10, isProp: 0, drawable: 2041, price: 1500,  slider: 1, items: 1, discount: 30 },
                { component: 10, isProp: 0, drawable: 2042, price: 750,   slider: 1, items: 12, discount: 10 },
                { component: 10, isProp: 0, drawable: 2099, price: 2500,   slider: 1, items: 7, discount: 20 },

                { component: 10, isProp: 0, drawable: 2091, price: 9999,  slider: 1, items: 1, server: 'kinguin' },
            ],

            bags: [
                { component: 5, isProp: 0, drawable: 2010, price: 1000,  slider: 1, items: 1, discount: 30 },
                { component: 5, isProp: 0, drawable: 2011, price: 1750,  slider: 1, items: 7, discount: 10 },
                { component: 5, isProp: 0, drawable: 2013, price: 1500,  slider: 1, items: 20, discount: 15 },
                { component: 5, isProp: 0, drawable: 2014, price: 1500,  slider: 1, items: 6, discount: 30 },
                { component: 5, isProp: 0, drawable: 2015, price: 1000,  slider: 1, items: 2, discount: 15 },
                { component: 5, isProp: 0, drawable: 2019, price: 1500,  slider: 1, items: 3, discount: 10 },
                { component: 5, isProp: 0, drawable: 2020, price: 1500,  slider: 1, items: 7, discount: 20 },
                { component: 5, isProp: 0, drawable: 2084, price: 1500,  slider: 1, items: 7, discount: 35 },
                { component: 5, isProp: 0, drawable: 2086, price: 1000,  slider: 1, items: 8, discount: 25 },
                { component: 5, isProp: 0, drawable: 2089, price: 1500,  slider: 1, items: 13, discount: 40 },
                { component: 5, isProp: 0, drawable: 2090, price: 1750,  slider: 1, items: 10, discount: 40 },
                { component: 5, isProp: 0, drawable: 2091, price: 1000,  slider: 1, items: 10, discount: 30 },
                { component: 5, isProp: 0, drawable: 2092, price: 1500,  slider: 1, items: 10, discount: 35 },

                { component: 5, isProp: 0, drawable: 2095, price: 9999,  slider: 1, items: 9, server: '2092action' },
            ]
        }
    ]
}
