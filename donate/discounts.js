export const types = {
    clothes: 'clothes',
    vehicle: 'vehicle',
    animation: 'animation',
    case: 'case'
}

export const colorsType = {
    blue: 'blue',
    gray: 'gray',
    red: 'red',
    gold: 'gold',
    green: 'green',
    purple: 'purple'
}


export const sizes = {
    small: 'small',
    medium: 'medium',
    large: 'large',
}

export const categoryMap = [
    { type: 'masks',       isProp: 0, component: 1  },
    { type: 'legs',        isProp: 0, component: 4  },
    { type: 'shoes',       isProp: 0, component: 6  },
    { type: 'accessories', isProp: 0, component: 7  },
    { type: 'undershirts', isProp: 0, component: 8  },
    { type: 'tops',        isProp: 0, component: 11 },
    { type: 'head',        isProp: 1, component: 0  },
    { type: 'glasses',     isProp: 1, component: 1  },
    { type: 'ears',        isProp: 1, component: 2  },
    { type: 'watches',     isProp: 1, component: 6  },
    { type: 'bracelets',   isProp: 1, component: 7  },
    { type: 'torsos',      isProp: 0, component: 3  },
    { type: 'gloves',      isProp: 0, component: 3  },
    { type: 'bags',        isProp: 0, component: 5  },
    { type: 'armor',       isProp: 0, component: 9  },
    { type: 'decals',      isProp: 0, component: 10 }
]

export const discounts = [
    {
        list: [
            /*{//0
                size: sizes.large,
                discount: 10,
                color: colorsType.purple,

                data: {
                    type: types.clothes,

                    clothesData: { 0: { gender: 0, component: 11, drawable: 2181, texture: 0, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2193, texture: 0, isProp: 0 } }
                }
            },
            {//1
                size: sizes.small,
                discount: 10,

                color: colorsType.purple,

                data: {
                    type: types.vehicle,
                    model: 'matiz'
                }
            },
            {//2
                size: sizes.small,
                discount: 10,


                color: colorsType.purple,

                data: {
                    type: types.animation,
                    id: 12
                }
            },
            {//3
                size: sizes.small,
                discount: 10,


                color: colorsType.purple,

                data: {
                    type: types.case,
                    case: 'default'
                }
            },
            {//4
                size: sizes.small,
                discount: 10,


                color: colorsType.purple,

                data: {
                    type: types.animation,
                    id: 13
                }
            },
            {//5
                size: sizes.small,
                discount: 10,


                color: colorsType.purple,

                data: {
                    type: types.animation,
                    id: 14
                }
            },
            {//6
                size: sizes.small,
                discount: 10,


                color: colorsType.purple,

                data: {
                    type: types.animation,
                    id: 15
                }
            },*/
        ],
    }
]
