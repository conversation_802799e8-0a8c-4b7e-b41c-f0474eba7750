module.exports = {
	id: 20,
	title: 'donate.config.cases.winterVehicles2025.title',

	start: ['2024-11-30', 'YYYY-MM-DD'],
	end: ['2025-03-15 06:00:00', 'YYYY-MM-DD HH:mm:ss'],

	seasonPassId: 6,

	// disabledBuy: true,
	readOnly: false,

	// Сумма распыления указан в опыте батлпаса. Если батлпас активен
	sellRule: 40, // Если батлпас уже не активен, то задать процент конвертации price в коины. Например 100 exp / 100 * 25 = 25 коинов

	price: 1500,
	transferDays: 90,

	colors: [
		{
			color: 'gray',
			chance: 51,
		},
		{
			color: 'blue',
			chance: 24,
		},
		{
			color: 'purple',
			chance: 14,
			types: [
				{ type: 'wheels', chance: 45 },
				{ type: 'vehicleDetail', chance: 55 },
			],
		},
		{
			color: 'red',
			chance: 6,
		},
		{
			color: 'gold',
			chance: 2.5,
			types: [
				{ type: 'wheels', chance: 65 },
				{ type: 'vehicle', chance: 35 },
			],
		},
		{
			color: 'unique',
			chance: 0.15,
		},
	],

	caseContent: [
		// GRAY - начало
		{ type: 'vehicle', color: 'gray', chance: 2.5, price: 600, value: 'blista2' },
		{ type: 'vehicle', color: 'gray', chance: 2.25, price: 626, value: 'chimera' },
		{ type: 'vehicle', color: 'gray', chance: 2, price: 639, value: 'manana' },
		{ type: 'vehicle', color: 'gray', chance: 1.75, price: 652, value: 'dominator10' },
		{ type: 'vehicle', color: 'gray', chance: 1.5, price: 665, value: 'vorschlaghammer' },
		{ type: 'vehicle', color: 'gray', chance: 1.4, price: 678, value: 'gauntlet2' },
		{ type: 'vehicle', color: 'gray', chance: 1.3, price: 704, value: 'eurosx32' },
		{ type: 'vehicle', color: 'gray', chance: 1.2, price: 717, value: 'omnis' },
		{ type: 'vehicle', color: 'gray', chance: 1.1, price: 730, value: 'yosemite1500' },
		// GRAY - начало

		// BLUE - начало
		{ type: 'vehicle', color: 'blue', chance: 2.5, price: 1000, value: 'coquette5' },
		{ type: 'vehicle', color: 'blue', chance: 2.25, price: 1050, value: 'sultan2' },
		{ type: 'vehicle', color: 'blue', chance: 2, price: 1100, value: 'jester3' },
		{ type: 'vehicle', color: 'blue', chance: 1.75, price: 1150, value: 'envisage' },
		{ type: 'vehicle', color: 'blue', chance: 1.5, price: 1200, value: 'castigator' },
		{ type: 'vehicle', color: 'blue', chance: 1.4, price: 1250, value: 'revolter' },
		{ type: 'vehicle', color: 'blue', chance: 1.3, price: 1300, value: 'niobe' },
		{ type: 'vehicle', color: 'blue', chance: 1.2, price: 1350, value: 'paragon3' },
		{ type: 'vehicle', color: 'blue', chance: 1.1, price: 1400, value: 'pipistrello' },
		// BLUE - начало

		// PURPLE - начало
		{ type: 'wheels', color: 'purple', chance: 13, price: 1500, value: 13007 },
		{ type: 'wheels', color: 'purple', chance: 13, price: 1500, value: 12007 },
		{ type: 'wheels', color: 'purple', chance: 13, price: 1500, value: 11007 },
		{ type: 'wheels', color: 'purple', chance: 13, price: 1500, value: 10007 },
		{ type: 'wheels', color: 'purple', chance: 12, price: 1700, value: 13008 },
		{ type: 'wheels', color: 'purple', chance: 12, price: 1700, value: 12008 },
		{ type: 'wheels', color: 'purple', chance: 12, price: 1700, value: 11008 },
		{ type: 'wheels', color: 'purple', chance: 12, price: 1700, value: 10008 },
		{ type: 'wheels', color: 'purple', chance: 11, price: 1900, value: 13010 },
		{ type: 'wheels', color: 'purple', chance: 11, price: 1900, value: 12010 },
		{ type: 'wheels', color: 'purple', chance: 11, price: 1900, value: 11010 },
		{ type: 'wheels', color: 'purple', chance: 11, price: 1900, value: 10010 },
		{ type: 'wheels', color: 'purple', chance: 10, price: 2100, value: 12011 },
		{ type: 'wheels', color: 'purple', chance: 10, price: 2100, value: 13011 },
		{ type: 'wheels', color: 'purple', chance: 10, price: 2100, value: 11011 },
		{ type: 'wheels', color: 'purple', chance: 10, price: 2100, value: 10011 },

		{ type: 'vehicleDetail', color: 'purple', chance: 7, price: 2500, value: 1007 },
		{ type: 'vehicleDetail', color: 'purple', chance: 6, price: 2500, value: 1009 },
		{ type: 'vehicleDetail', color: 'purple', chance: 5, price: 2500, value: 1010 },
		{ type: 'vehicleDetail', color: 'purple', chance: 4, price: 2750, value: 1015 },
		{ type: 'vehicleDetail', color: 'purple', chance: 3, price: 2750, value: 1017 },
		{ type: 'vehicleDetail', color: 'purple', chance: 2, price: 2750, value: 1018 },
		{ type: 'vehicleDetail', color: 'purple', chance: 0.5, price: 3000, value: 5001 },
		{ type: 'vehicleDetail', color: 'purple', chance: 0.5, price: 3000, value: 5002 },
		// PURPLE - начало

		// RED - начало
		{ type: 'vehicle', color: 'red', chance: 19, price: 3000, value: 'barracuda' },
		{ type: 'vehicleMod', color: 'red', chance: 13, price: 3500, value: { model: 'barracuda', setId: 1, group: 1 } },
		{ type: 'vehicleMod', color: 'red', chance: 9, price: 3750, value: { model: 'barracuda', setId: 2, group: 1 } },
		{ type: 'vehicleMod', color: 'red', chance: 5, price: 4000, value: { model: 'barracuda', setId: 3, group: 1 } },

		{ type: 'vehicle', color: 'red', chance: 14, price: 4500, value: 'rsx' },
		{ type: 'vehicleMod', color: 'red', chance: 10, price: 5000, value: { model: 'rsx', setId: 1, group: 2 } },
		{ type: 'vehicleMod', color: 'red', chance: 8, price: 5500, value: { model: 'rsx', setId: 2, group: 2 } },
		{ type: 'vehicleMod', color: 'red', chance: 6, price: 6000, value: { model: 'rsx', setId: 3, group: 2 } },

		{ type: 'vehicle', color: 'red', chance: 14, price: 6500, value: 'ml' },
		{ type: 'vehicleMod', color: 'red', chance: 7, price: 7000, value: { model: 'ml', setId: 1, group: 3 } },
		{ type: 'vehicleMod', color: 'red', chance: 5, price: 7500, value: { model: 'ml', setId: 2, group: 3 } },
		{ type: 'vehicleMod', color: 'red', chance: 2.5, price: 8000, value: { model: 'ml', setId: 3, group: 3 } },

		{ type: 'vehicle', color: 'red', chance: 11.5, price: 8500, value: 'mazda3' },
		{ type: 'vehicleMod', color: 'red', chance: 4.5, price: 9000, value: { model: 'mazda3', setId: 1 } },

		{ type: 'vehicle', color: 'red', chance: 7.5, price: 9500, value: 'f7' },
		{ type: 'vehicleMod', color: 'red', chance: 3.5, price: 10000, value: { model: 'f7', setId: 1 } },
		// RED - начало

		// GOLD - начало
		{ type: 'wheels', color: 'gold', chance: 31, price: 11000, value: 13009 },
		{ type: 'wheels', color: 'gold', chance: 30, price: 11500, value: 13012 },
		{ type: 'wheels', color: 'gold', chance: 26, price: 12000, value: 12009 },
		{ type: 'wheels', color: 'gold', chance: 25, price: 12500, value: 12012 },
		{ type: 'wheels', color: 'gold', chance: 21, price: 13000, value: 11009 },
		{ type: 'wheels', color: 'gold', chance: 20, price: 13500, value: 11012 },
		{ type: 'wheels', color: 'gold', chance: 16, price: 14000, value: 10009 },
		{ type: 'wheels', color: 'gold', chance: 15, price: 14000, value: 10012 },

		{ type: 'vehicle', color: 'gold', chance: 8.5, price: 16000, value: 'ev6' },
		{ type: 'vehicle', color: 'gold', chance: 5, price: 17000, value: 'sportage' },
		{ type: 'vehicle', color: 'gold', chance: 3, price: 18000, value: 'testarossa' },
		{ type: 'vehicle', color: 'gold', chance: 0.5, price: 19000, value: 'su7' },
		// GOLD - начало

		// UNIQUE - начало
		{ type: 'vehicleMod', color: 'unique', chance: 0.5, price: 20000, value: { model: 'su7', setId: 1 } },
		{ type: 'vehicle', color: 'unique', chance: 0.25, price: 25000, value: 'nevera' },
		// UNIQUE - конец
	],
};
