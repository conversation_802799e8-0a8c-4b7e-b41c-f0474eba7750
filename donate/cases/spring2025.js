module.exports = {
	id: 22,
	title: 'donate.config.cases.spring2025.title',
	// discount: 5,

	start: ['2025-03-19 06:00:00', 'YYYY-MM-DD HH:mm:ss'],
	end: ['2025-06-01 00:00:00', 'YYYY-MM-DD HH:mm:ss'],

	price: 500, // Цена кейса в коинах

	// servers: ['TEST0'],

	transferDays: 30,

	disabledBuy: false,

	readOnly: false,

	colors: [
		{
			color: 'gray',
			chance: 55,
			types: [
				{ type: 'money', chance: 30 },
				{ type: 'clothesDiscount', chance: 15 },
				{ type: 'vehicleDiscount', chance: 15 },
				{ type: 'animation', chance: 13 },
				{ type: 'clothes', chance: 13 },
                { type: 'vehicle', chance: 22 },
			],
		},
		{
			color: 'blue',
			chance: 24,
			types: [
				{ type: 'money', chance: 30 },
				{ type: 'clothesDiscount', chance: 15 },
				{ type: 'vehicleDiscount', chance: 15 },
				{ type: 'vehicleDetail', chance: 30 },
                { type: 'vehicle', chance: 32 },
				{ type: 'subscription', chance: 12 },
				{ type: 'armourSkin', chance: 10 },
				{ type: 'animation', chance: 20 },
				{ type: 'clothes', chance: 25 },
			],
		},
		{
			color: 'purple',
			chance: 11,
			types: [
				{ type: 'money', chance: 30 },
				{ type: 'subscription', chance: 12 },
				{ type: 'clothesDiscount', chance: 25 },
				{ type: 'vehicleDiscount', chance: 25 },
				{ type: 'animation', chance: 20 },
				{ type: 'weaponSkin', chance: 15 },
                { type: 'vehicle', chance: 16 },
                { type: 'vehicleMod', chance: 23 },
				{ type: 'clothes', chance: 30 },
			],
		},
		{
			color: 'red',
			chance: 6,
			types: [
				{ type: 'money', chance: 30 },
				{ type: 'subscription', chance: 12 },
				{ type: 'clothesDiscount', chance: 25 },
				{ type: 'vehicleDiscount', chance: 25 },
				{ type: 'animation', chance: 20 },
				{ type: 'armourSkin', chance: 15 },
				{ type: 'weaponSkin', chance: 20 },
				{ type: 'vehicleDetail', chance: 30 },
                { type: 'vehicleMod', chance: 27 },
				{ type: 'clothes', chance: 30 },
			],
		},
		{
			color: 'gold',
			chance: 3.5,
			types: [
				{ type: 'money', chance: 25 },
				{ type: 'subscription', chance: 12 },
				{ type: 'clothesDiscount', chance: 25 },
				{ type: 'vehicleDiscount', chance: 25 },
				{ type: 'animation', chance: 20 },
				{ type: 'armourSkin', chance: 15 },
				{ type: 'weaponSkin', chance: 15 },
				{ type: 'clothes', chance: 30 },
				{ type: 'vehicleDetail', chance: 30 },
                { type: 'vehicleMod', chance: 14 },
                { type: 'vehicle', chance: 5.2 },
			],
		},
		{
            color: 'unique',
            chance: 0.4,
            types: [
                { type: 'vehicleMod',      chance: 13 },
                { type: 'vehicle',         chance: 3 },
            ],
        }
	],


	caseContent: [
		// Vehicles mods
		{ type: 'vehicleDetail', color: 'blue', chance: 15, price: 250,  value: 1025 },
		{ type: 'vehicleDetail', color: 'blue', chance: 14, price: 250,  value: 1026 },
		{ type: 'vehicleDetail', color: 'blue', chance: 13, price: 250,  value: 1027 },

		{ type: 'vehicleDetail', color: 'red',  chance: 10, price: 1000, value: 1023 },
		{ type: 'vehicleDetail', color: 'red',  chance: 10, price: 1000, value: 1024 },

		{ type: 'vehicleDetail', color: 'gold', chance: 10, price: 2000, value: 5006 },
		{ type: 'vehicleDetail', color: 'gold', chance: 10, price: 2000, value: 5007 },


        // Транспорт и обвесы
		{ type: 'vehicle',    color: 'gray',    chance: 6,    price: 150,  value: 'buccaneer2'  },
		{ type: 'vehicle',    color: 'gray',    chance: 6,    price: 150,  value: 'dominator2'  },
		{ type: 'vehicle',    color: 'gray',    chance: 6,    price: 150,  value: 'slamvan'  },
		{ type: 'vehicle',    color: 'gray',    chance: 6,    price: 150,  value: 'dune'  },

		{ type: 'vehicle',    color: 'blue',    chance: 6,    price: 250,  value: 'bodhi2'  },
		{ type: 'vehicle',    color: 'blue',    chance: 6,    price: 250,  value: 'seminole2'  },
		{ type: 'vehicle',    color: 'blue',    chance: 6,    price: 250,  value: 'futo'  },
		{ type: 'vehicle',    color: 'blue',    chance: 6,    price: 250,  value: 'kuruma'  },

        { type: 'vehicle',    color: 'purple',  chance: 9,  price: 750, customPrice: 250000,  value: 'solaris'  },
		{ type: 'vehicle',    color: 'purple',  chance: 6,  price: 800, customPrice: 300000,  value: 'gnx'  },

        { type: 'vehicleMod', color: 'purple', chance: 4.5,  price: 800,  customPrice: 300000,   value: { model: 'solaris', setId: 1 } },
        { type: 'vehicleMod', color: 'purple', chance: 3.5,  price: 850,  customPrice: 350000,   value: { model: 'gnx', setId: 1 } },
		{ type: 'vehicleMod', color: 'purple', chance: 2,  price: 900,  customPrice: 400000,  value: { model: 'durango', setId: 1, group: 2 } },
		{ type: 'vehicleMod', color: 'purple', chance: 1.5, price: 950,  customPrice: 400000,   value: { model: 'durango', setId: 2, group: 2 } },
		{ type: 'vehicleMod', color: 'purple', chance: 0.9, price: 1100,  customPrice: 500000,  value: { model: 'nisgtr', setId: 5, group: 3 } },
        { type: 'vehicleMod', color: 'purple', chance: 0.9, price: 1100,  customPrice: 550000, value: { model: 'nisgtr', setId: 6, group: 3 } },
		{ type: 'vehicleMod', color: 'purple', chance: 0.5, price: 1200,  customPrice: 600000,  value: { model: 'cayen19', setId: 3 } },

		{ type: 'vehicleMod', color: 'red', chance: 2.5,  price: 1400,  customPrice: 750000, value: { model: 'gnx', setId: 2, group: 1  } },
		{ type: 'vehicleMod', color: 'red', chance: 2.5,  price: 1400,  customPrice: 750000, value: { model: 'gnx', setId: 3, group: 1  } },
		{ type: 'vehicleMod', color: 'red', chance: 3.5,  price: 1600,  customPrice: 1000000,  value: { model: 'cayen19', setId: 4 } },
		{ type: 'vehicleMod', color: 'red', chance: 1,  price: 1750,  customPrice: 1200000,  value: { model: 'x6m2', setId: 4, group: 4 } },
        { type: 'vehicleMod', color: 'red', chance: 0.5,  price: 1800,  customPrice: 1250000, value: { model: 'x6m2', setId: 5, group: 4 } },
		{ type: 'vehicleMod', color: 'red', chance: 1.5,  price: 1900,  customPrice: 1300000,  value: { model: 'a8', setId: 2 } },

		{ type: 'vehicleMod', color: 'gold', chance: 6,  price: 4000,  customPrice: 1500000, value: { model: 'nisgtr', setId: 10 } },
		{ type: 'vehicleMod', color: 'gold', chance: 5,  price: 7000,  customPrice: 2000000, value: { model: 'a8', setId: 3 } },

		{ type: 'vehicle',    color: 'gold', chance: 4,  price: 8000, customPrice: 2000000,  value: 'f8'  },

		{ type: 'vehicleMod', color: 'unique', chance: 2.85,  price: 15000,  customPrice: 3000000, value: { model: 'x6m2', setId: 6 } },
		{ type: 'vehicleMod', color: 'unique', chance: 1.0,  price: 17500,  customPrice: 4000000, value: { model: 'f8', setId: 1 } },
		{ type: 'vehicleMod', color: 'unique', chance: 1.4,  price: 20000,  customPrice: 5000000, value: { model: 'mp1', setId: 4 } },

		{ type: 'vehicle',    color: 'unique', chance: 0.25, price: 25000,  customPrice: 6000000, value: 'idr'  },


		// Animations
        { type: 'animation',       color: 'gray',    chance: 17,   price: 150,    value: 401 }, // Surfin Bird
		{ type: 'animation',       color: 'gray',    chance: 16,   price: 150,    value: 399 }, // Lemon Melon Coockie
		{ type: 'animation',       color: 'gray',    chance: 16,   price: 150,    value: 403 }, // Lucid Dreams
		{ type: 'animation',       color: 'gray',    chance: 16,   price: 150,    value: 408 }, // ASMR Keys

        { type: 'animation',       color: 'blue',    chance: 14,   price: 300,    value: 400 }, // Sweet Bumble Bee
		{ type: 'animation',       color: 'blue',    chance: 16,   price: 300,    value: 410 }, // Miku LIVE
		{ type: 'animation',       color: 'blue',    chance: 16,   price: 300,    value: 402 }, // Deep Explorer
		{ type: 'animation',       color: 'blue',    chance: 16,   price: 300,    value: 404 }, // Smitten
		{ type: 'animation',       color: 'blue',    chance: 16,   price: 300,    value: 343 }, // To the Beat

        { type: 'animation',       color: 'purple',  chance: 14,   price: 600,    value: 405 }, // Commited
		{ type: 'animation',       color: 'purple',  chance: 14,   price: 600,    value: 409 }, // Cupid Arrows
		{ type: 'animation',       color: 'purple',  chance: 14,   price: 600,    value: 406 }, // Ribbon Dancer

		{ type: 'animation',       color: 'red',     chance: 15,   price: 1000,   value: 228 }, // Rat Dance
		{ type: 'animation',       color: 'red',     chance: 14,   price: 1000,   value: 398 }, // Exum Shuffle
		{ type: 'animation',       color: 'red',     chance: 13,   price: 1000,   value: 411 }, // Accolades
		{ type: 'animation',       color: 'red',     chance: 15,   price: 1000,   value: 407 }, // Amazing Cube

        { type: 'animation',       color: 'gold',    chance: 13,   price: 1600,   value: 105 }, // Lil Monster


		// WeaponSkins
        { type: 'weaponSkin', color: 'purple', chance: 0.85, price: 700, value: { entityName: 'weapon_tecpistol', skinId: 13 } },
        { type: 'weaponSkin', color: 'purple', chance: 0.85, price: 700, value: { entityName: 'weapon_gusenberg', skinId: 18 } },

        { type: 'weaponSkin', color: 'red', chance: 0.75, price: 1000, value: { entityName: 'weapon_tacticalrifle', skinId: 13 } },
        { type: 'weaponSkin', color: 'red', chance: 0.75, price: 1000, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 25 } },
        { type: 'weaponSkin', color: 'red', chance: 0.75, price: 1000, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 26 } },
        { type: 'weaponSkin', color: 'red', chance: 0.75, price: 1000, value: { entityName: 'weapon_heavyshotgun', skinId: 29 } },

        { type: 'weaponSkin', color: 'gold', chance: 0.65, price: 1600, value: { entityName: 'weapon_heavysniper_mk2', skinId: 24 } },
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 1600, value: { entityName: 'weapon_heavyrifle', skinId: 13 } },
        { type: 'weaponSkin', color: 'gold', chance: 0.65, price: 1600, value: { entityName: 'weapon_combatmg_mk2', skinId: 13 } },

		// ArmourSkins
        { type: 'armourSkin', color: 'blue', chance: 0.95, price: 400, value: { entityName: 'light', skinId: 148 } },
    	{ type: 'armourSkin', color: 'blue', chance: 0.95, price: 400, value: { entityName: 'light', skinId: 149 } },

        { type: 'armourSkin', color: 'red', chance: 0.75, price: 1000, value: { entityName: 'light', skinId: 147 } },

		{ type: 'armourSkin', color: 'gold', chance: 0.65, price: 1600, value: { entityName: 'light', skinId: 145 } },
        { type: 'armourSkin', color: 'gold', chance: 0.65, price: 1600, value: { entityName: 'light', skinId: 146 } },


		// Clothes
        { type: 'clothes', color: 'gray', chance: 1.0, price: 250, value: { gender: 1, component: 6, drawable: 2170, textures: 10, isProp: 0 } },
        { type: 'clothes', color: 'gray', chance: 1.0, price: 250, value: { gender: 0, component: 6, drawable: 2140, textures: 10, isProp: 0 } },
        { type: 'clothes', color: 'gray', chance: 1.0, price: 250, value: { gender: 0, component: 4, drawable: 2274, textures: 6, isProp: 0 } },
        { type: 'clothes', color: 'gray', chance: 1.0, price: 250, value: { gender: 0, component: 6, drawable: 2142, textures: 6, isProp: 0 } },
        { type: 'clothes', color: 'gray', chance: 1.0, price: 250, value: { gender: 1, component: 11, drawable: 2510, textures: 6, isProp: 0 } },
        { type: 'clothes', color: 'gray', chance: 1.0, price: 250, value: { gender: 1, component: 4, drawable: 2286, textures: 3, isProp: 0 } },
        { type: 'clothes', color: 'gray', chance: 1.0, price: 250, value: { gender: 1, component: 6, drawable: 2172, textures: 6, isProp: 0 } },
        { type: 'clothes', color: 'gray', chance: 1.0, price: 450, value: { gender: 0, component: 11, drawable: 2507, textures: 8, isProp: 0 } },

        { type: 'clothes', color: 'blue', chance: 1.0, price: 450, value: { gender: 1, component: 4, drawable: 2291, textures: 5, isProp: 0 } },
        { type: 'clothes', color: 'blue', chance: 1.0, price: 450, value: { gender: 0, component: 4, drawable: 2280, textures: 5, isProp: 0 } },
        { type: 'clothes', color: 'blue', chance: 1.0, price: 450, value: { gender: 1, component: 6, drawable: 2169, textures: 8, isProp: 0 } },
        { type: 'clothes', color: 'blue', chance: 1.0, price: 450, value: { gender: 0, component: 4, drawable: 2277, textures: 6, isProp: 0 } },
		{ type: 'clothes', color: 'blue', chance: 1.0, price: 450, value: { gender: 0, component: 11, drawable: 2511, textures: 6, isProp: 0 } },
        { type: 'clothes', color: 'blue', chance: 1.0, price: 450, value: { gender: 1, component: 5, drawable: 2118, textures: 12, isProp: 0 } },
		{ type: 'clothes', color: 'blue', chance: 1.0, price: 450, value: { gender: 0, component: 11, drawable: 2509, textures: 9, isProp: 0 } },
		{ type: 'clothes', color: 'blue', chance: 1.0, price: 450, value: { gender: 0, component: 4, drawable: 2275, textures: 9, isProp: 0 } },
		{ type: 'clothes', color: 'blue', chance: 1.0, price: 450, value: { gender: 1, component: 4, drawable: 2285, textures: 4, isProp: 0 } },


        { type: 'clothes', color: 'purple', chance: 1.0, price: 700, value: { gender: 0, component: 11, drawable: 2506, textures: 10, isProp: 0 } },
        { type: 'clothes', color: 'purple', chance: 1.0, price: 700, value: { gender: 0, component: 4, drawable: 2278, textures: 7, isProp: 0 } },
        { type: 'clothes', color: 'purple', chance: 1.0, price: 700, value: { gender: 0, component: 11, drawable: 2512, textures: 8, isProp: 0 } },
        { type: 'clothes', color: 'purple', chance: 1.0, price: 700, value: { gender: 0, component: 6, drawable: 2143, textures: 8, isProp: 0 } },
        { type: 'clothes', color: 'purple', chance: 1.0, price: 700, value: { gender: 0, component: 1, drawable: 2035, textures: 10, isProp: 1 } },
        { type: 'clothes', color: 'purple', chance: 1.0, price: 700, value: { gender: 1, component: 6, drawable: 2173, textures: 8, isProp: 0 } },
        { type: 'clothes', color: 'purple', chance: 1.0, price: 700, value: { gender: 1, component: 11, drawable: 2505, textures: 8, isProp: 0 } },
        { type: 'clothes', color: 'purple', chance: 1.0, price: 700, value: { gender: 1, component: 11, drawable: 2507, textures: 8, isProp: 0 } },
        { type: 'clothes', color: 'purple', chance: 1.0, price: 700, value: { gender: 1, component: 4, drawable: 2287, textures: 7, isProp: 0 } },
        { type: 'clothes', color: 'purple', chance: 1.0, price: 700, value: { gender: 1, component: 1, drawable: 2036, textures: 10, isProp: 1 } },

        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 5, drawable: 2110, textures: 6, isProp: 0 } },
        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 11, drawable: 2517, textures: 8, isProp: 0 } },
        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 6, drawable: 2141, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 11, drawable: 2510, textures: 4, isProp: 0 } },
        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 4, drawable: 2276, textures: 4, isProp: 0 } },
        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 11, drawable: 2508, textures: 11, isProp: 0 } },
        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 1, drawable: 2037, textures: 10, isProp: 1 } },
        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 6, drawable: 2171, textures: 10, isProp: 0 } },
        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 5, drawable: 2119, textures: 6, isProp: 0 } },
        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 11, drawable: 2506, textures: 11, isProp: 0 } },
        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 4, drawable: 2289, textures: 9, isProp: 0 } },
        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 11, drawable: 2511, textures: 9, isProp: 0 } },
        { type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 1, drawable: 2038, textures: 10, isProp: 1 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 11, drawable: 2514, textures: 11, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 11, drawable: 2509, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 4, drawable: 2290, textures: 11, isProp: 0 } },

        { type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 5, drawable: 2111, textures: 8, isProp: 0 } },
        { type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 4, drawable: 2279, textures: 9, isProp: 0 } },
        { type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 11, drawable: 2518, textures: 7, isProp: 0 } },
        { type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 6, drawable: 2139, textures: 10, isProp: 0 } },
        { type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 11, drawable: 2516, textures: 9, isProp: 0 } },
        { type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 1, drawable: 2036, textures: 6, isProp: 1 } },
        { type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 11, drawable: 2508, textures: 3, isProp: 0 } },
        { type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 4, drawable: 2288, textures: 3, isProp: 0 } },
        { type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 6, drawable: 2168, textures: 10, isProp: 0 } },
        { type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 5, drawable: 2120, textures: 8, isProp: 0 } },
        { type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 1, drawable: 2037, textures: 6, isProp: 1 } },


		// Скидка на транспорт
		{ type: 'vehicleDiscount', color: 'gray', chance: 50.0, price: 100, value: 5 },
		{ type: 'vehicleDiscount', color: 'blue', chance: 30.0, price: 250, value: 10 },
		{ type: 'vehicleDiscount', color: 'purple', chance: 15.0, price: 400, value: 15 },
		{ type: 'vehicleDiscount', color: 'red', chance: 4.0, price: 1000, value: 20 },
		{ type: 'vehicleDiscount', color: 'gold', chance: 1.0, price: 2500, value: 25 },

		// Скидка на одежду
		{ type: 'clothesDiscount', color: 'gray', chance: 50.0, price: 100, value: 5 },
		{ type: 'clothesDiscount', color: 'blue', chance: 30.0, price: 250, value: 10 },
		{ type: 'clothesDiscount', color: 'purple', chance: 15.0, price: 400, value: 15 },
		{ type: 'clothesDiscount', color: 'red', chance: 4.0, price: 1000, value: 20 },
		{ type: 'clothesDiscount', color: 'gold', chance: 1.0, price: 2500, value: 25 },

		// Вирты
		{ type: 'money', color: 'gray', chance: 50.0, price: 100, value: 10000 },
		{ type: 'money', color: 'blue', chance: 30.0, price: 250, value: 25000 },
		{ type: 'money', color: 'purple', chance: 15.0, price: 400, value: 40000 },
		{ type: 'money', color: 'red', chance: 4.0, price: 1000, value: 100000 },
		{ type: 'money', color: 'gold', chance: 1.0, price: 2500, value: 250000 },

		// Premium
		{ type: 'subscription', color: 'blue', chance: 2, price: 250, value: 7 },
		{ type: 'subscription', color: 'purple', chance: 2, price: 400, value: 30 },
		{ type: 'subscription', color: 'red', chance: 2, price: 1000, value: 90 },
		{ type: 'subscription', color: 'gold', chance: 2, price: 2500, value: 180 },
	],
};
