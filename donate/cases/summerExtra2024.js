module.exports = {
    id: 15,
    title: 'donate.config.cases.summerExtra2024.title',
    // discount: 10,

    start: ['2024-05-20', 'YYYY-MM-DD'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    // seasonPassId: 5,

    price: 500,
    transferDays: 30,
    // disabledBuy: true,
    // readOnly: true,

    // servers: ['STAGING0', 'DEVELOP0', 'TEST2'],

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    colors: [
        {
            color: 'gray',
            chance: 67,
        },
        {
            color: 'blue',
            chance: 17,
        },
        {
            color: 'purple',
            chance: 8.5,
        },
        {
            color: 'red',
            chance: 5,
        },
        {
            color: 'gold',
            chance: 2.5,
        }
    ],

    caseContent: [
        // gray
        { type: 'clothes',    color: 'gray',   chance: 30,   price: 250,  value: [ { gender: 0, component: 4,   drawable: 2229, textures: 6, isProp: 0 }, { gender: 1, component: 4,  drawable: 2247, textures: 6, isProp: 0 } ] },
        { type: 'clothes',    color: 'gray',   chance: 30,   price: 250,  value: [ { gender: 0, component: 11,  drawable: 2446, textures: 4, isProp: 0 }, { gender: 1, component: 11, drawable: 2452, textures: 4, isProp: 0 } ] },

        { type: 'tattoo',     color: 'gray',   chance: 25,   price: 250,   value: [ 3379, 6446 ] },
        { type: 'tattoo',     color: 'gray',   chance: 25,   price: 250,   value: [ 3387, 6452 ] },
        { type: 'tattoo',     color: 'gray',   chance: 25,   price: 250,   value: [ 3383, 6453 ] },

        { type: 'money',      color: 'gray',   chance: 55,   price: 150,   value: 15000    },
        { type: 'money',      color: 'gray',   chance: 45,   price: 200,   value: 20000    },
        { type: 'money',      color: 'gray',   chance: 35,   price: 250,   value: 25000    },

        { type: 'coins',      color: 'gray',   chance: 50,   price: 150,   value: 150      },
        { type: 'coins',      color: 'gray',   chance: 40,   price: 200,   value: 200      },
        { type: 'coins',      color: 'gray',   chance: 30,   price: 250,   value: 250      },

        // blue
        { type: 'clothes',    color: 'blue',   chance: 17,   price: 450,  value: [ { gender: 0, component: 4,   drawable: 2235, textures: 1, isProp: 0 }, { gender: 1, component: 4,  drawable: 2252, textures: 2, isProp: 0 } ] },
        { type: 'clothes',    color: 'blue',   chance: 17,   price: 450,  value: [ { gender: 0, component: 11,  drawable: 2451, textures: 9, isProp: 0 }, { gender: 1, component: 11,  drawable: 2447, textures: 2, isProp: 0 } ] },
        { type: 'clothes',    color: 'blue',   chance: 17,   price: 450,  value: [ { gender: 0, component: 11,  drawable: 2444, textures: 3, isProp: 0 }, { gender: 1, component: 11, drawable: 2450, textures: 3, isProp: 0 } ] },
        { type: 'clothes',    color: 'blue',   chance: 17,   price: 450,  value: [ { gender: 0, component: 4,   drawable: 2227, textures: 5, isProp: 0 }, { gender: 1, component: 11,  drawable: 2439, textures: 6, isProp: 0 } ] },
        { type: 'clothes',    color: 'blue',   chance: 17,   price: 450,  value: [ { gender: 0, component: 4,  drawable: 2231, textures: 4, isProp: 0 }, { gender: 1, component: 4, drawable: 2255, textures: 6, isProp: 0 } ] },
        { type: 'clothes',    color: 'blue',   chance: 17,   price: 450,  value: { gender: 1,  component: 11,  drawable: 2455, textures: 6, isProp: 0 } },

        { type: 'tattoo',     color: 'blue',   chance: 30,   price: 450,   value: [ 3381, 6444 ] },
        { type: 'tattoo',     color: 'blue',   chance: 30,   price: 450,   value: [ 3382, 6445 ] },
        { type: 'tattoo',     color: 'blue',   chance: 30,   price: 450,   value: [ 3355, 6421 ] },

        { type: 'money',      color: 'blue',   chance: 55,   price: 350,   value: 35000    },
        { type: 'money',      color: 'blue',   chance: 45,   price: 400,   value: 40000    },

        { type: 'coins',      color: 'blue',   chance: 50,   price: 350,   value: 350      },
        { type: 'coins',      color: 'blue',   chance: 40,   price: 400,   value: 400      },

        // purple
        { type: 'clothes',    color: 'purple', chance: 5,    price: 950,  value: [ { gender: 0, component: 6,   drawable: 2107, textures: 11, isProp: 0 }, { gender: 1, component: 6, drawable: 2128, textures: 1, isProp: 0 } ] },
        { type: 'clothes',    color: 'purple', chance: 5,    price: 950,  value: [ { gender: 0, component: 11,  drawable: 2448, textures: 2, isProp: 0 }, { gender: 1, component: 11,  drawable: 2443, textures: 5, isProp: 0 } ] },
        { type: 'clothes',    color: 'purple', chance: 5,    price: 950,  value: [ { gender: 0, component: 4,   drawable: 2233, textures: 2, isProp: 0 }, { gender: 1, component: 4,  drawable: 2250, textures: 7, isProp: 0 } ] },
        { type: 'clothes',    color: 'purple', chance: 5,    price: 950,  value: [ { gender: 0, component: 1,   drawable: 2157, textures: 0, isProp: 0 }, { gender: 1, component: 1,  drawable: 2144, textures: 0, isProp: 0 } ] },
        { type: 'clothes',    color: 'purple', chance: 5,    price: 950,  value: [ { gender: 0, component: 4,   drawable: 2232, textures: 7, isProp: 0 }, { gender: 1, component: 4,  drawable: 2253, textures: 7, isProp: 0 } ] },
        { type: 'clothes',    color: 'purple', chance: 5,    price: 950,  value: [ { gender: 0, component: 11,  drawable: 2442, textures: 6, isProp: 0 }, { gender: 1, component: 11,  drawable: 2448, textures: 7, isProp: 0 } ] },
        { type: 'clothes',    color: 'purple', chance: 5,    price: 950,  value: [ { gender: 0, component: 5,  drawable: 2075, textures: 6, isProp: 0 }, { gender: 1, component: 5,  drawable: 2076, textures: 7, isProp: 0 } ] },
        { type: 'clothes',    color: 'purple', chance: 5,    price: 950,  value: [ { gender: 0, component: 6,  drawable: 2109, textures: 11, isProp: 0 }, { gender: 1, component: 6,  drawable: 2130, textures: 11, isProp: 0 } ] },
        { type: 'clothes',    color: 'purple', chance: 5,    price: 950,  value: [ { gender: 0, component: 5,  drawable: 2073, textures: 12, isProp: 0 }, { gender: 1, component: 5,  drawable: 2074, textures: 12, isProp: 0 } ] },
        { type: 'clothes',    color: 'purple', chance: 5,    price: 950,  value: { gender: 1,  component: 11,  drawable: 2451, textures: 5, isProp: 0 } },

        { type: 'weaponSkin', color: 'purple', chance: 25,   price: 900, value: { entityName: 'weapon_tecpistol', skinId: 6 } },
        { type: 'weaponSkin', color: 'purple', chance: 25,   price: 900, value: { entityName: 'weapon_gusenberg', skinId: 11 } },

        { type: 'tattoo',     color: 'purple', chance: 30,   price: 850,   value: [ 3380, 6447 ] },
        { type: 'tattoo',     color: 'purple', chance: 30,   price: 850,   value: [ 3375, 6442 ] },
        { type: 'tattoo',     color: 'purple', chance: 30,   price: 850,   value: [ 3378, 6450 ] },

        { type: 'money',      color: 'purple', chance: 45,   price: 700,   value: 70000    },
        { type: 'money',      color: 'purple', chance: 35,   price: 800,   value: 80000    },

        { type: 'coins',      color: 'purple', chance: 50,   price: 700,   value: 700      },
        { type: 'coins',      color: 'purple', chance: 40,   price: 800,   value: 800      },

        // red
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000,  value: { gender: 1,  component: 11,  drawable: 2454, textures: 3, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000, value: [ { gender: 0, component: 4,   drawable: 2228, textures: 4, isProp: 0 }, { gender: 1, component: 4,  drawable: 2249, textures: 7, isProp: 0 } ] },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000, value: [ { gender: 0, component: 6,   drawable: 2111, textures: 8, isProp: 0 }, { gender: 1, component: 6,  drawable: 2132, textures: 8, isProp: 0 } ] },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000, value: [ { gender: 0, component: 11,  drawable: 2445, textures: 7, isProp: 0 }, { gender: 1, component: 11, drawable: 2442, textures: 7, isProp: 0 } ] },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000, value: [ { gender: 0, component: 1,   drawable: 2156, textures: 3, isProp: 0 }, { gender: 1, component: 1,  drawable: 2143, textures: 3, isProp: 0 } ] },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000, value: [ { gender: 0, component: 6,   drawable: 2106, textures: 12, isProp: 0 }, { gender: 1, component: 6, drawable: 2127, textures: 12, isProp: 0 } ] },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000, value: [ { gender: 0, component: 1,   drawable: 2149, textures: 8, isProp: 0 }, { gender: 1, component: 1,  drawable: 2136, textures: 8, isProp: 0 } ] },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000, value: [ { gender: 0, component: 4,   drawable: 2226, textures: 4, isProp: 0 }, { gender: 1, component: 4,  drawable: 2251, textures: 8, isProp: 0 } ] },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000, value: [ { gender: 0, component: 1,  drawable: 2150, textures: 0, isProp: 0 }, { gender: 1, component: 1, drawable: 2137, textures: 0, isProp: 0 } ] },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000, value: [ { gender: 0, component: 1,  drawable: 2153, textures: 0, isProp: 0 }, { gender: 1, component: 1, drawable: 2140, textures: 0, isProp: 0 } ] },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000, value: [ { gender: 0, component: 6,   drawable: 2108, textures: 13, isProp: 0 }, { gender: 1, component: 6,  drawable: 2129, textures: 13, isProp: 0 } ] },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000, value: [ { gender: 0, component: 5,   drawable: 2076, textures: 12, isProp: 0 }, { gender: 1, component: 5,  drawable: 2078, textures: 12, isProp: 0 } ] },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000,  value: { gender: 1, component: 5,  drawable: 2073, textures: 8, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 3.3,  price: 2000,  value: { gender: 1, component: 11,  drawable: 2446, textures: 6, isProp: 0 } },

        { type: 'weaponSkin', color: 'red',    chance: 12.5, price: 1900, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 18 } },
        { type: 'weaponSkin', color: 'red',    chance: 12.5, price: 1900, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 19 } },
        { type: 'weaponSkin', color: 'red',    chance: 12.5, price: 1900, value: { entityName: 'weapon_heavyshotgun', skinId: 22 } },
        { type: 'weaponSkin', color: 'red',    chance: 12.5, price: 1900, value: { entityName: 'weapon_tacticalrifle', skinId: 6 } },

        { type: 'tattoo',     color: 'red',    chance: 30,   price: 1750,  value: [ 3377, 6451 ] },
        { type: 'tattoo',     color: 'red',    chance: 30,   price: 1750,  value: [ 3376, 6443 ] },
        { type: 'tattoo',     color: 'red',    chance: 30,   price: 1750,  value: [ 3374, 6441 ] },

        { type: 'money',      color: 'red',    chance: 45,   price: 1500,  value: 150000   },
        { type: 'money',      color: 'red',    chance: 35,   price: 2000,  value: 200000   },

        { type: 'coins',      color: 'red',    chance: 50,   price: 1500,  value: 1500     },
        { type: 'coins',      color: 'red',    chance: 40,   price: 2000,  value: 2000     },

        // gold
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: [ { gender: 0, component: 4,   drawable: 2236, textures: 4, isProp: 0 }, { gender: 1, component: 4,  drawable: 2248, textures: 5, isProp: 0 } ] },
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: [ { gender: 0, component: 11,   drawable: 2449, textures: 8, isProp: 0 }, { gender: 1, component: 11, drawable: 2440, textures: 6, isProp: 0 } ] },
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: [ { gender: 0, component: 1,   drawable: 2160, textures: 3, isProp: 0 }, { gender: 1, component: 1,  drawable: 2147, textures: 3, isProp: 0 } ] },
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: [ { gender: 0, component: 4,  drawable: 2234, textures: 8, isProp: 0 }, { gender: 1, component: 11, drawable: 2441, textures: 9, isProp: 0 } ] },
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: { gender: 1,  component: 5,  drawable: 2077, textures: 8, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: [ { gender: 0, component: 6,   drawable: 2112, textures: 13, isProp: 0 }, { gender: 1, component: 6,  drawable: 2133, textures: 13, isProp: 0 } ] },
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: [ { gender: 0, component: 4,   drawable: 2230, textures: 7, isProp: 0 }, { gender: 1, component: 8,  drawable: 2120, textures: 6, isProp: 0 } ] },
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: [ { gender: 0, component: 6,   drawable: 2110, textures: 12, isProp: 0 }, { gender: 1, component: 6,  drawable: 2131, textures: 12, isProp: 0 } ] },
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: { gender: 1,  component: 11,  drawable: 2444, textures: 4, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: { gender: 1,  component: 11,  drawable: 2456, textures: 6, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: { gender: 1,  component: 11,  drawable: 2457, textures: 3, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 3.7,  price: 8000, value: [ { gender: 0, component: 0,   drawable: 2091, textures: 6, isProp: 1 }, { gender: 1, component: 0,  drawable: 2094, textures: 6, isProp: 1 } ] },

        { type: 'armourSkin', color: 'gold',   chance: 28,   price: 750, value: { entityName: 'light', skinId: 121 } },
        { type: 'armourSkin', color: 'gold',   chance: 28,   price: 750, value: { entityName: 'light', skinId: 122 } },

        { type: 'weaponSkin', color: 'gold',   chance: 18,   price: 5000, value: { entityName: 'weapon_heavysniper_mk2', skinId: 17 } },
        { type: 'weaponSkin', color: 'gold',   chance: 18,   price: 5000, value: { entityName: 'weapon_heavyrifle', skinId: 6 } },
        { type: 'weaponSkin', color: 'gold',   chance: 18,   price: 5000, value: { entityName: 'weapon_combatmg_mk2', skinId: 6 } },

        { type: 'tattoo',     color: 'gold',   chance: 30,   price: 5000, value: [ 3385, 6449 ] },
        { type: 'tattoo',     color: 'gold',   chance: 30,   price: 5000, value: [ 3386, 6455 ] },
        { type: 'tattoo',     color: 'gold',   chance: 30,   price: 5000, value: [ 3388, 6448 ] },

        { type: 'money',      color: 'gold',   chance: 45,   price: 5000,  value: 500000   },
        { type: 'money',      color: 'gold',   chance: 35,   price: 10000, value: 1000000  },

        { type: 'coins',      color: 'gold',   chance: 50,   price: 5000,  value: 5000     },
        { type: 'coins',      color: 'gold',   chance: 40,   price: 10000, value: 10000    },
    ]
}
