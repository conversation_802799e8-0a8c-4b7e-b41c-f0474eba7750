module.exports = {
    id: 17,
    title: 'donate.config.cases.summer2024.title',
    // discount: 5,

    start: ['2024-05-20', 'YYYY-MM-DD'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    seasonPassId: 5,

    // Сумма распыления указан в опыте батлпаса. Если батлпас активен
    sellRule: 40, // Если батлпас уже не активен, то задать процент конвертации price в коины. Например 100 exp / 100 * 25 = 25 коинов

    price: 1000,
    transferDays: 30,

    // servers: ['STAGING0', 'DEVELOP0', 'TEST2'],
    // disabledBuy: true,
    // readOnly: true,

    // specificEnd: {
    //     'RU12': ['2024-09-15', 'YYYY-MM-DD'],
    //     'TEST0': ['2024-09-15', 'YYYY-MM-DD'],
    //     'TEST7': ['2024-09-15', 'YYYY-MM-DD']
    // },

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    colors: [
        {
            color: 'gray',
            chance: 60,
            types: [
                { type: 'vehicleDiscount', chance: 40 },
                { type: 'clothesDiscount', chance: 40 },
                { type: 'wheels', chance: 35 },
                { type: 'animation', chance: 35 },
                { type: 'item', chance: 30 },
                { type: 'money', chance: 20 },
                { type: 'coins', chance: 15 },
                { type: 'clothes', chance: 15 },
            ],
        },
        {
            color: 'blue',
            chance: 18,
            types: [
                { type: 'vehicleDiscount', chance: 40 },
                { type: 'clothesDiscount', chance: 40 },
                { type: 'wheels', chance: 35 },
                { type: 'animation', chance: 35 },
                { type: 'mediaSound', chance: 35 },
                { type: 'tattoo', chance: 35 },
                { type: 'item', chance: 30 },
                { type: 'money', chance: 25 },
                { type: 'coins', chance: 25 },
                { type: 'clothes', chance: 20 },
            ],
        },
        {
            color: 'purple',
            chance: 9,
            types: [
                { type: 'animation', chance: 40 },
                { type: 'tattoo', chance: 40 },
                { type: 'wheels', chance: 40 },
                { type: 'mediaSound', chance: 35 },
                { type: 'armourSkin', chance: 35 },
                { type: 'weaponSkin', chance: 35 },
                { type: 'vehicleDiscount', chance: 30 },
                { type: 'clothesDiscount', chance: 30 },
                { type: 'money', chance: 25 },
                { type: 'coins', chance: 25 },
                { type: 'vehicleSet', chance: 30 },
                { type: 'item', chance: 15 },
                { type: 'clothes', chance: 20 },
            ],
        },
        {
            color: 'red',
            chance: 6,
            types: [
                { type: 'tattoo', chance: 45 },
                { type: 'animation', chance: 45 },
                { type: 'armourSkin', chance: 40 },
                { type: 'weaponSkin', chance: 50 },
                { type: 'mediaSound', chance: 40 },
                { type: 'money', chance: 35 },
                { type: 'coins', chance: 35 },
                { type: 'vehicleDiscount', chance: 30 },
                { type: 'clothesDiscount', chance: 30 },
                { type: 'wheels', chance: 25 },
                { type: 'vehicleSet', chance: 35 },
                { type: 'item', chance: 15 },
                { type: 'clothes', chance: 30 },
                { type: 'vehicle', chance: 25 },
            ],
        },
        {
            color: 'gold',
            chance: 3.5,
            types: [
                { type: 'tattoo', chance: 45 },
                { type: 'animation', chance: 45 },
                { type: 'mediaSound', chance: 45 },
                { type: 'money', chance: 40 },
                { type: 'coins', chance: 40 },
                { type: 'wheels', chance: 35 },
                { type: 'armourSkin', chance: 35 },
                { type: 'weaponSkin', chance: 35 },
                { type: 'vehicleDiscount', chance: 30 },
                { type: 'clothesDiscount', chance: 30 },
                { type: 'vehicleSet', chance: 45 },
                { type: 'item', chance: 20 },
                { type: 'clothes', chance: 30 },
                { type: 'vehicle', chance: 35 },
            ],
        },
        {
            color: 'unique',
            chance: 0.1,
            types: [
                { type: 'vehicle', chance: 10 },
            ],
        },
    ],

    caseContent: [
        // gray
        { type: 'money', color: 'gray', chance: 40, price: 75, value: 7500 },
        { type: 'money', color: 'gray', chance: 35, price: 100, value: 10000 },
        { type: 'money', color: 'gray', chance: 25, price: 100, value: 15000 },
        { type: 'money', color: 'gray', chance: 20, price: 200, value: 20000 },

        { type: 'item', color: 'gray', chance: 1.00, price: 80, value: { itemId: 89, count: 10 } }, // Желтая аптечка (хил 75хп)
        { type: 'item', color: 'gray', chance: 1.00, price: 250, value: { itemId: 334, count: 20 } }, // ИРП Армии США
        { type: 'item', color: 'gray', chance: 1.00, price: 250, value: { itemId: 255, count: 0 } }, // Бронебойный пистолет
        { type: 'item', color: 'gray', chance: 1.00, price: 80, value: { itemId: 737, count: 0 } }, // Консервированные бобы
        { type: 'item', color: 'gray', chance: 1.00, price: 200, value: { itemId: 130, count: 0 } }, // Кинжал
        { type: 'item', color: 'gray', chance: 1.00, price: 200, value: { itemId: 132, count: 0 } }, // Складной нож
        { type: 'item', color: 'gray', chance: 1.00, price: 80, value: { itemId: 736, count: 8 } }, // Гавайская пицца
        { type: 'item', color: 'gray', chance: 1.00, price: 250, value: { itemId: 262, count: 0 } }, // Пистолет Marksman

        { type: 'wheels', color: 'gray', chance: 0.50, price: 100, value: 234 },
        { type: 'wheels', color: 'gray', chance: 0.50, price: 100, value: 240 },
        { type: 'wheels', color: 'gray', chance: 0.50, price: 100, value: 246 },
        { type: 'wheels', color: 'gray', chance: 0.50, price: 100, value: 285 },
        { type: 'wheels', color: 'gray', chance: 0.50, price: 100, value: 291 },

        { type: 'animation', color: 'gray', chance: 1.00, price: 250, value: 185 }, // Hand Signals
        { type: 'animation', color: 'gray', chance: 1.00, price: 250, value: 184 }, // Infinite Dab
        { type: 'animation', color: 'gray', chance: 1.00, price: 250, value: 276 }, // headbanger
        { type: 'animation', color: 'gray', chance: 1.00, price: 250, value: 310 }, // doom doom yes yes

        { type: 'vehicleDiscount', color: 'gray', chance: 1.00, price: 125, value: 5 },
        { type: 'clothesDiscount', color: 'gray', chance: 1.00, price: 125, value: 5 },

        { type: 'coins', color: 'gray', chance: 45, price: 50, value: 50 },
        { type: 'coins', color: 'gray', chance: 35, price: 100, value: 100 },

        { type: 'clothes', color: 'gray', chance: 1.00, price: 250, value: [{ gender: 0, component: 4, drawable: 2210, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2239, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'gray', chance: 1.00, price: 250, value: [{ gender: 0, component: 11, drawable: 2426, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2431, textures: 9, isProp: 0 }] },

        // blue
        { type: 'wheels', color: 'blue', chance: 0.50, price: 250, value: 252 },
        { type: 'wheels', color: 'blue', chance: 0.50, price: 250, value: 258 },
        { type: 'wheels', color: 'blue', chance: 0.50, price: 250, value: 267 },
        { type: 'wheels', color: 'blue', chance: 0.50, price: 250, value: 273 },
        { type: 'wheels', color: 'blue', chance: 0.50, price: 250, value: 279 },

        { type: 'clothes', color: 'blue', chance: 1.00, price: 500, value: [{ gender: 0, component: 4, drawable: 2215, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2233, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'blue', chance: 1.00, price: 500, value: [{ gender: 0, component: 6, drawable: 2105, textures: 9, isProp: 0 }, { gender: 1, component: 6, drawable: 2126, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'blue', chance: 1.00, price: 500, value: [{ gender: 0, component: 11, drawable: 2427, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2422, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'blue', chance: 1.00, price: 500, value: [{ gender: 0, component: 4, drawable: 2211, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2235, textures: 9, isProp: 0 }] },

        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3344, 6410] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3347, 6413] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3351, 6417] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3352, 6418] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3353, 6419] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3371, 6437] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3358, 6424] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3369, 6435] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3359, 6425] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3360, 6426] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3366, 6432] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 350, value: [3368, 6434] },

        { type: 'item', color: 'blue', chance: 1.00, price: 200, value: { itemId: 960, count: 0 } }, // Энергетик
        { type: 'item', color: 'blue', chance: 1.00, price: 160, value: { itemId: 727, count: 25 } }, // (25 штук) Качественный ремонтный набор
        { type: 'item', color: 'blue', chance: 1.00, price: 160, value: { itemId: 625, count: 10 } }, // Адреналин (= эпинефрин) 10-30 шт
        { type: 'item', color: 'blue', chance: 1.00, price: 160, value: { itemId: 732, count: 0 } }, // Протеиновый батончик
        { type: 'item', color: 'blue', chance: 1.00, price: 160, value: { itemId: 734, count: 0 } }, // Благодарственное письмо губернатора
        { type: 'item', color: 'blue', chance: 1.00, price: 160, value: { itemId: 728, count: 0 } }, // Биодобавка 1 уровня
        { type: 'item', color: 'blue', chance: 1.00, price: 500, value: { itemId: 444, count: 0 } }, // Ключ карта Fleeca
        { type: 'item', color: 'blue', chance: 1.00, price: 160, value: { itemId: 735, count: 0 } }, // Странный бургер
        { type: 'item', color: 'blue', chance: 1.00, price: 400, value: { itemId: 963, count: 2 } }, // Сверхтяжелый бронежилет
        { type: 'item', color: 'blue', chance: 1.00, price: 150, value: { itemId: 733, count: 10 } }, // Стайлинг ManeMaster

        { type: 'animation', color: 'blue', chance: 1.00, price: 500, value: 332 }, // Jubi Slide
        { type: 'animation', color: 'blue', chance: 1.00, price: 500, value: 166 }, // Ma Ya Hi
        { type: 'animation', color: 'blue', chance: 1.00, price: 500, value: 251 }, // I Declare
        { type: 'animation', color: 'blue', chance: 1.00, price: 500, value: 329 }, // Evil Plan
        { type: 'animation', color: 'blue', chance: 1.00, price: 500, value: 316 }, // Keep Em Crispy
        { type: 'animation', color: 'blue', chance: 1.00, price: 500, value: 51 }, // Billy Bounce
        { type: 'animation', color: 'blue', chance: 1.00, price: 500, value: 37 }, // Build Up
        { type: 'animation', color: 'blue', chance: 1.00, price: 500, value: 323 }, // Bad Guy

        { type: 'mediaSound', color: 'blue', chance: 1.00, price: 320, value: 'donk' }, // Озвучка 'Удар!'
        { type: 'mediaSound', color: 'blue', chance: 1.00, price: 320, value: 'sleep' }, // Озвучка 'Спит...!'
        { type: 'mediaSound', color: 'blue', chance: 1.00, price: 320, value: 'music' }, // Озвучка 'Ту-ту-ту-ту-ту-ту!'
        { type: 'mediaSound', color: 'blue', chance: 1.00, price: 320, value: 'bell' }, // Озвучка 'Колокол!'

        { type: 'money', color: 'blue', chance: 120, price: 300, value: 30000 },

        { type: 'coins', color: 'blue', chance: 80, price: 150, value: 150 },

        { type: 'vehicleDiscount', color: 'blue', chance: 1.00, price: 250, value: 10 },
        { type: 'clothesDiscount', color: 'blue', chance: 1.00, price: 250, value: 10 },

        // purple
        { type: 'vehicleSet', color: 'purple', chance: 0.50, price: 5000, value: { model: 'victoria', setId: 5, group: 19 } }, // обвес Ford Crown Victoria TEST2 в антихроме
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1100, value: { model: 'victoria', setId: 6, group: 19 } }, // обвес Ford Crown Victoria TEST2 в карбоне
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1200, value: { model: 'victoria', setId: 7, group: 19 } }, // обвес Ford Crown Victoria TEST2 в кованном карбоне
        { type: 'vehicleSet', color: 'purple', chance: 0.50, price: 5000, value: { model: 's2000', setId: 6, group: 20 } }, // обвес Honda S2000 TEST2 в антихроме
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1100, value: { model: 's2000', setId: 7, group: 20 } }, // обвес Honda S2000 TEST2 в карбоне
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1200, value: { model: 's2000', setId: 8, group: 20 } }, // обвес Honda S2000 TEST2 в кованном карбоне
        { type: 'vehicleSet', color: 'purple', chance: 0.50, price: 5000, value: { model: 'eclipse', setId: 13, group: 21 } }, // обвес Mitshubishi Eclipse D30 TEST4 в антихроме
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1100, value: { model: 'eclipse', setId: 14, group: 21 } }, // обвес Mitshubishi Eclipse D30 TEST4 в карбоне
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1200, value: { model: 'eclipse', setId: 15, group: 21 } }, // обвес Mitshubishi Eclipse D30 TEST4 в кованном карбоне
        { type: 'vehicleSet', color: 'purple', chance: 0.50, price: 5000, value: { model: 'q60s', setId: 13, group: 22 } }, // обвес Infiniti Q60S TEST4 в антихроме
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1100, value: { model: 'q60s', setId: 14, group: 22 } }, // обвес Infiniti Q60S TEST4 в карбоне
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1200, value: { model: 'q60s', setId: 15, group: 22 } }, // обвес Infiniti Q60S TEST4 в кованном карбоне
        { type: 'vehicleSet', color: 'purple', chance: 0.50, price: 5000, value: { model: 'brz', setId: 18, group: 23 } }, // обвес Subaru BRZ Rocket Bunny в антихроме
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1100, value: { model: 'brz', setId: 19, group: 23 } }, // обвес Subaru BRZ Rocket Bunny в карбоне
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1200, value: { model: 'brz', setId: 20, group: 23 } }, // обвес Subaru BRZ Rocket Bunny в кованном карбоне
        { type: 'vehicleSet', color: 'purple', chance: 0.50, price: 5000, value: { model: 'carrera', setId: 10, group: 24 } }, // обвес Porsche Carrera RWB в антихроме
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1100, value: { model: 'carrera', setId: 11, group: 24 } }, // обвес Porsche Carrera RWB в карбоне
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1200, value: { model: 'carrera', setId: 12, group: 24 } }, // обвес Porsche Carrera RWB в кованном карбоне
        { type: 'vehicleSet', color: 'purple', chance: 0.50, price: 5000, value: { model: 'civic2', setId: 3, group: 25 } }, // обвес Honda Civic FK1 Type-R в антихроме
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1100, value: { model: 'civic2', setId: 4, group: 25 } }, // обвес Honda Civic FK1 Type-R в карбоне
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1200, value: { model: 'civic2', setId: 5, group: 25 } }, // обвес Honda Civic FK1 Type-R в кованном карбоне
        { type: 'vehicleSet', color: 'purple', chance: 0.50, price: 5000, value: { model: 'civic2', setId: 6, group: 26 } }, // обвес Honda Civic FK1 Type-R в антихроме
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1100, value: { model: 'civic2', setId: 7, group: 26 } }, // обвес Honda Civic FK1 Type-R в карбоне
        //{ type: 'vehicleSet', color: 'purple', chance: 0.50, price: 1200, value: { model: 'civic2', setId: 8, group: 26 } }, // обвес Honda Civic FK1 Type-R в кованном карбоне

        { type: 'wheels', color: 'purple', chance: 0.50, price: 350, value: 235 },
        { type: 'wheels', color: 'purple', chance: 0.50, price: 350, value: 241 },
        { type: 'wheels', color: 'purple', chance: 0.50, price: 350, value: 247 },
        { type: 'wheels', color: 'purple', chance: 0.50, price: 350, value: 286 },
        { type: 'wheels', color: 'purple', chance: 0.50, price: 350, value: 292 },

        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 11, drawable: 2434, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2426, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 4, drawable: 2218, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2237, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 5, drawable: 2071, textures: 9, isProp: 0 }, { gender: 1, component: 5, drawable: 2071, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 4, drawable: 2220, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2238, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 11, drawable: 2436, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2428, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 4, drawable: 2209, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2232, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 11, drawable: 2429, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2420, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 4, drawable: 2213, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2229, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 11, drawable: 2437, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2423, textures: 3, isProp: 0 }] },
        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 10, drawable: 2098, textures: 9, isProp: 0 }, { gender: 1, component: 10, drawable: 2098, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 5, drawable: 2061, textures: 9, isProp: 0 }, { gender: 1, component: 5, drawable: 2056, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'purple', chance: 1.00, price: 750, value: [{ gender: 0, component: 11, drawable: 2396, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2392, textures: 9, isProp: 0 }] },

        { type: 'tattoo', color: 'purple', chance: 1.00, price: 350, value: [3354, 6420] },
        { type: 'tattoo', color: 'purple', chance: 1.00, price: 500, value: [3355, 6421] },
        { type: 'tattoo', color: 'purple', chance: 1.00, price: 500, value: [3356, 6422] },
        { type: 'tattoo', color: 'purple', chance: 1.00, price: 500, value: [3367, 6433] },

        { type: 'armourSkin', color: 'purple', chance: 0.85, price: 750, value: { entityName: 'light', skinId: 113 } },
        { type: 'armourSkin', color: 'purple', chance: 0.85, price: 750, value: { entityName: 'light', skinId: 114 } },

        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 1000, value: { entityName: 'weapon_tecpistol', skinId: 2 } }, // Skin Flowers
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 1000, value: { entityName: 'weapon_gusenberg', skinId: 7 } }, // Skin Flowers
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 1000, value: { entityName: 'weapon_tecpistol', skinId: 3 } }, // Skin Pirate
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 1000, value: { entityName: 'weapon_gusenberg', skinId: 8 } }, // Skin Pirate
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 1000, value: { entityName: 'weapon_gusenberg', skinId: 9 } }, // Skin Katana
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 1000, value: { entityName: 'weapon_tecpistol', skinId: 4 } }, // Skin Katana
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 1000, value: { entityName: 'weapon_tecpistol', skinId: 1 } }, // Skin Fire
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 1000, value: { entityName: 'weapon_gusenberg', skinId: 6 } }, // Skin Fire
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 1000, value: { entityName: 'weapon_tecpistol', skinId: 5 } }, // Skin Bonus
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 1000, value: { entityName: 'weapon_gusenberg', skinId: 10 } }, // Skin Bonus

        { type: 'item', color: 'purple', chance: 1.00, price: 240, value: { itemId: 726, count: 0 } }, // Экспериментальная пилюля "Имморталитикс
        { type: 'item', color: 'purple', chance: 1.00, price: 240, value: { itemId: 525, count: 0 } }, // BIOLINK
        { type: 'item', color: 'purple', chance: 1.00, price: 240, value: { itemId: 738, count: 0 } }, // Непредсказуемый коктейль Реднека
        { type: 'item', color: 'purple', chance: 1.00, price: 600, value: { itemId: 956, count: 3 } }, // Набор самореанимации
        { type: 'item', color: 'purple', chance: 1.00, price: 600, value: { itemId: 443, count: 0 } }, // Дрель 1500w
        { type: 'item', color: 'purple', chance: 1.00, price: 750, value: { itemId: 337, count: 0 } }, // Винтовка Marksman Mk2
        { type: 'item', color: 'purple', chance: 1.00, price: 240, value: { itemId: 729, count: 0 } }, // Биодобавка 2 уровня
        { type: 'item', color: 'purple', chance: 1.00, price: 600, value: { itemId: 965, count: 0 } }, // Указ о налоговых льготах
        { type: 'item', color: 'purple', chance: 1.00, price: 600, value: { itemId: 958, count: 0 } }, // Улучшенная рация
        { type: 'item', color: 'purple', chance: 1.00, price: 600, value: { itemId: 333, count: 0 } }, // Ручной пулемет Мk2
        { type: 'item', color: 'purple', chance: 1.00, price: 240, value: { itemId: 525, count: 10 } }, // BIOLINK

        { type: 'animation', color: 'purple', chance: 1.00, price: 750, value: 346 }, // just wanna rock
        { type: 'animation', color: 'purple', chance: 1.00, price: 750, value: 147 }, // Orange Justice
        { type: 'animation', color: 'purple', chance: 1.00, price: 750, value: 60 }, // in da party
        { type: 'animation', color: 'purple', chance: 1.00, price: 750, value: 309 }, // Starlit
        { type: 'animation', color: 'purple', chance: 1.00, price: 750, value: 36 }, // Hey Now
        { type: 'animation', color: 'purple', chance: 1.00, price: 750, value: 216 }, // BBoom BBoom
        { type: 'animation', color: 'purple', chance: 1.00, price: 750, value: 236 }, // Roll N Rock
        { type: 'animation', color: 'purple', chance: 1.00, price: 750, value: 235 }, // The Magic Bomb

        { type: 'mediaSound', color: 'purple', chance: 1.00, price: 600, value: 'amogus' }, // Озвучка 'АМОГУС!'
        { type: 'mediaSound', color: 'purple', chance: 1.00, price: 600, value: 'generator' }, // Озвучка 'Отдаю свою энергию, как генератор!'
        { type: 'mediaSound', color: 'purple', chance: 1.00, price: 600, value: 'paris' }, // Озвучка 'Я в париже!'
        { type: 'mediaSound', color: 'purple', chance: 1.00, price: 600, value: 'mood' }, // Озвучка 'Диктор канала Мастерская настроения!'

        { type: 'money', color: 'purple', chance: 120, price: 500, value: 50000 },
        { type: 'coins', color: 'purple', chance: 80, price: 300, value: 300 },

        { type: 'vehicleDiscount', color: 'purple', chance: 1.00, price: 500, value: 15 },
        { type: 'clothesDiscount', color: 'purple', chance: 1.00, price: 500, value: 15 },

        // red
        { type: 'vehicleSet', color: 'red', chance: 0.50, price: 1000, value: { model: 'i8', setId: 3, group: 27 } }, // обвес BMW i8 TEST1 в антихроме
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1100, value: { model: 'i8', setId: 4, group: 27 } }, // обвес BMW i8 TEST1 в карбоне
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1200, value: { model: 'i8', setId: 5, group: 27 } }, // обвес BMW i8 TEST1 в кованном карбоне
        { type: 'vehicleSet', color: 'red', chance: 0.50, price: 1300, value: { model: 'bronco', setId: 3, group: 28 } }, // обвес Ford Bronco Raptor
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1400, value: { model: 'bronco', setId: 4, group: 28 } }, // обвес Ford Bronco Raptor в карбоне
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1500, value: { model: 'bronco', setId: 5, group: 28 } }, // обвес Ford Bronco Raptor в кованном карбоне
        { type: 'vehicleSet', color: 'red', chance: 0.50, price: 1000, value: { model: 'f150', setId: 6, group: 29 } }, // обвес Ford F-150 Raptor TEST2
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1250, value: { model: 'f150', setId: 7, group: 29 } }, // обвес Ford F-150 Raptor TEST2 в карбоне
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1500, value: { model: 'f150', setId: 8, group: 29 } }, // обвес Ford F-150 Raptor TEST2 в кованном карбоне
        { type: 'vehicleSet', color: 'red', chance: 0.50, price: 1250, value: { model: 'k5', setId: 7, group: 30 } }, // обвес KIA K5 Restyling в антихроме
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1500, value: { model: 'k5', setId: 8, group: 30 } }, // обвес KIA K5 Restyling в карбоне
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1750, value: { model: 'k5', setId: 9, group: 30 } }, // обвес KIA K5 Restyling в кованном карбоне
        { type: 'vehicleSet', color: 'red', chance: 0.50, price: 1000, value: { model: 'civic2', setId: 9, group: 31 } }, // обвес Honda Civic FK1 TEST1 в антихроме
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1100, value: { model: 'civic2', setId: 10, group: 31 } }, // обвес Honda Civic FK1 TEST1 в карбоне
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1200, value: { model: 'civic2', setId: 11, group: 31 } }, // обвес Honda Civic FK1 TEST1 в кованном карбоне
        { type: 'vehicleSet', color: 'red', chance: 0.50, price: 1000, value: { model: '5series2', setId: 4, group: 32 } }, // обвес BMW 5-Series G61 M-Pro в антихроме
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1250, value: { model: '5series2', setId: 5, group: 32 } }, // обвес BMW 5-Series G61 M-Pro в карбоне
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1500, value: { model: '5series2', setId: 6, group: 32 } }, // обвес BMW 5-Series G61 M-Pro в кованном карбоне
        { type: 'vehicleSet', color: 'red', chance: 0.50, price: 1000, value: { model: 'db11', setId: 9, group: 33 } }, // обвес Aston Martin DB11 TEST3 в антихроме
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1250, value: { model: 'db11', setId: 10, group: 33 } }, // обвес Aston Martin DB11 TEST3 в карбоне
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1500, value: { model: 'db11', setId: 11, group: 33 } }, // обвес Aston Martin DB11 TEST3 в кованном карбоне
        { type: 'vehicleSet', color: 'red', chance: 0.50, price: 1000, value: { model: 'countach', setId: 3, group: 34 } }, // обвес Lamborghini Countach TEST1 в антихроме
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1250, value: { model: 'countach', setId: 4, group: 34 } }, // обвес Lamborghini Countach TEST1 в карбоне
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1500, value: { model: 'countach', setId: 5, group: 34 } }, // обвес Lamborghini Countach TEST1 в кованном карбоне
        { type: 'vehicleSet', color: 'red', chance: 0.50, price: 1000, value: { model: 'civic2', setId: 12, group: 35 } }, // обвес Honda Civic FK1 TEST2 в антихроме
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1100, value: { model: 'civic2', setId: 13, group: 35 } }, // обвес Honda Civic FK1 TEST2 в карбоне
        //{ type: 'vehicleSet', color: 'red', chance: 0.50, price: 1200, value: { model: 'civic2', setId: 14, group: 35 } }, // обвес Honda Civic FK1 TEST2 в кованном карбоне

        { type: 'wheels', color: 'red', chance: 0.50, price: 450, value: 253 },
        { type: 'wheels', color: 'red', chance: 0.50, price: 450, value: 259 },
        { type: 'wheels', color: 'red', chance: 0.50, price: 450, value: 268 },
        { type: 'wheels', color: 'red', chance: 0.50, price: 450, value: 274 },
        { type: 'wheels', color: 'red', chance: 0.50, price: 450, value: 280 },

        { type: 'clothes', color: 'red', chance: 1.00, price: 1000, value: [{ gender: 0, component: 4, drawable: 2216, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2240, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'red', chance: 1.00, price: 1000, value: [{ gender: 0, component: 11, drawable: 2433, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2432, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'red', chance: 1.00, price: 1000, value: [{ gender: 0, component: 4, drawable: 2221, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2243, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'red', chance: 1.00, price: 1000, value: [{ gender: 0, component: 11, drawable: 2430, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2434, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'red', chance: 1.00, price: 1000, value: [{ gender: 0, component: 6, drawable: 2104, textures: 9, isProp: 0 }, { gender: 1, component: 6, drawable: 2124, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'red', chance: 1.00, price: 1000, value: [{ gender: 0, component: 5, drawable: 2069, textures: 9, isProp: 0 }, { gender: 1, component: 5, drawable: 2068, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'red', chance: 1.00, price: 1000, value: [{ gender: 0, component: 4, drawable: 2212, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2230, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'red', chance: 1.00, price: 1000, value: [{ gender: 0, component: 11, drawable: 2428, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2421, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'red', chance: 1.00, price: 1000, value: [{ gender: 0, component: 5, drawable: 2072, textures: 9, isProp: 0 }, { gender: 1, component: 5, drawable: 2072, textures: 9, isProp: 0 }] },

        { type: 'tattoo', color: 'red', chance: 1.00, price: 750, value: [3345, 6411] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 750, value: [3346, 6412] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 750, value: [3350, 6416] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 750, value: [3357, 6423] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 750, value: [3361, 6427] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 750, value: [3362, 6428] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 750, value: [3365, 6431] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 750, value: [3370, 6436] },

        { type: 'armourSkin', color: 'red', chance: 0.75, price: 1000, value: { entityName: 'light', skinId: 112 } },
        { type: 'armourSkin', color: 'red', chance: 0.75, price: 1000, value: { entityName: 'light', skinId: 116 } },
        { type: 'armourSkin', color: 'red', chance: 0.75, price: 1000, value: { entityName: 'light', skinId: 119 } },

        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_tacticalrifle', skinId: 2 } }, // Skin Flowers
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_tacticalrifle', skinId: 3 } }, // Skin Pirate
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_tacticalrifle', skinId: 4 } }, // Skin Katana
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 14 } }, // Skin Flowers
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 15 } }, // Skin Pirate
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 16 } }, // Skin Katana
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 15 } }, // Skin Flowers
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 16 } }, // Skin Pirate
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 17 } }, // Skin Katana
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_heavyshotgun', skinId: 18 } }, // Skin Flowers
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_heavyshotgun', skinId: 19 } }, // Skin Pirate
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_heavyshotgun', skinId: 20 } }, // Skin Katana
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 13 } }, // Skin Fire
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 14 } }, // Skin Fire
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_heavyshotgun', skinId: 17 } }, // Skin Fire
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_tacticalrifle', skinId: 1 } }, // Skin Fire
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 17 } }, // Skin Bonus
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 18 } }, // Skin Bonus
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_heavyshotgun', skinId: 21 } }, // Skin Bonus
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 1250, value: { entityName: 'weapon_tacticalrifle', skinId: 5 } }, // Skin Bonus

        { type: 'item', color: 'red', chance: 1.00, price: 400, value: { itemId: 725, count: 0 } }, // Ремонтный комплект для оружия
        { type: 'item', color: 'red', chance: 1.00, price: 1200, value: { itemId: 959, count: 0 } }, // Точильный камень
        { type: 'item', color: 'red', chance: 1.00, price: 400, value: { itemId: 730, count: 0 } }, // Биодобавка 3 уровня
        { type: 'item', color: 'red', chance: 1.00, price: 1200, value: { itemId: 573, count: 0 } }, // Списанный дефибриллятор
        { type: 'item', color: 'red', chance: 1.00, price: 400, value: { itemId: 731, count: 20 } }, // (20 штук) Капсулы восстановления
        { type: 'item', color: 'red', chance: 1.00, price: 1200, value: { itemId: 811, count: 0 } }, // Свисток
        { type: 'item', color: 'red', chance: 1.00, price: 1200, value: { itemId: 957, count: 0 } }, // Глушилка связи

        { type: 'animation', color: 'red', chance: 1.00, price: 1500, value: 169 }, // Shanty For A Squad
        { type: 'animation', color: 'red', chance: 1.00, price: 1500, value: 167 }, // Jug Band
        { type: 'animation', color: 'red', chance: 1.00, price: 1500, value: 347 }, // 9mm go bang

        { type: 'mediaSound', color: 'red', chance: 1.00, price: 1500, value: 'snipedflight' }, // Озвучка 'Меня снайпнули в полёте!'
        { type: 'mediaSound', color: 'red', chance: 1.00, price: 1500, value: 'Yeahbuddy' }, // Озвучка 'Yeah buddy!'
        { type: 'mediaSound', color: 'red', chance: 1.00, price: 1500, value: 'Tylerdisappeared' }, // Озвучка 'Тайлер исчез...'
        { type: 'mediaSound', color: 'red', chance: 1.00, price: 1500, value: 'gojosatoruarrived' }, // Озвучка 'Прибыл Годжо Сатору!'

        { type: 'money', color: 'red', chance: 125, price: 2000, value: 200000 },
        { type: 'coins', color: 'red', chance: 90, price: 1000, value: 1000 },

        { type: 'vehicleDiscount', color: 'red', chance: 1.00, price: 750, value: 20 },
        { type: 'clothesDiscount', color: 'red', chance: 1.00, price: 750, value: 20 },

        { type: 'vehicle', color: 'red', chance: 4.00, price: 4000, customPrice: 300000, value: 'civic2' }, // Honda Civic FK1
        { type: 'vehicle', color: 'red', chance: 2.00, price: 4500, customPrice: 575000, value: 'bronco' }, // Ford Bronco
        { type: 'vehicle', color: 'red', chance: 3.00, price: 5000, customPrice: 750000, value: 'd8' }, // Donkervoort D8 GTO
        { type: 'vehicle', color: 'red', chance: 1.00, price: 5500, customPrice: 875000, value: '5series2' }, // BMW 5-Series G61

        // gold
        { type: 'vehicle', color: 'gold', chance: 4.00, price: 6000, customPrice: 1062500, value: 'lfa' }, // Lexus LFA
        { type: 'vehicle', color: 'gold', chance: 4.00, price: 6500, customPrice: 1375000, value: 'rrs' }, // Range Rover Sport L461
        { type: 'vehicle', color: 'gold', chance: 3.00, price: 7000, customPrice: 1750000, value: 'air' }, // Lucid Air
        { type: 'vehicle', color: 'gold', chance: 3.00, price: 7500, customPrice: 2000000, value: 'macan2' }, // Porsche Macan EV
        { type: 'vehicle', color: 'gold', chance: 2.00, price: 8000, customPrice: 3000000, value: 'amggt2' }, // Mercedes-AMG GT C192
        { type: 'vehicle', color: 'gold', chance: 2.00, price: 8500, customPrice: 3750000, value: 'evija' }, // Lotus Evija
        { type: 'vehicle', color: 'gold', chance: 4.00, price: 5000, customPrice: 2000000, value: 'cybertruck' }, // Tesla Cybertruck

        { type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1000, value: { model: 'rrphantom', setId: 12, group: 36 } }, // Rolls-Royce Phantom VIII Novitec
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1250, value: { model: 'rrphantom', setId: 13, group: 36 } }, // Rolls-Royce Phantom VIII Novitec в антихроме
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1500, value: { model: 'rrphantom', setId: 14, group: 36 } }, // Rolls-Royce Phantom VIII Novitec в карбоне
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1750, value: { model: 'rrphantom', setId: 15, group: 36 } }, // Rolls-Royce Phantom VIII Novitec в кованном карбоне
        { type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1000, value: { model: 'emira', setId: 15, group: 37 } }, // обвес Lotus Emira TEST5
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1250, value: { model: 'emira', setId: 16, group: 37 } }, // обвес Lotus Emira TEST5 в карбоне
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1500, value: { model: 'emira', setId: 17, group: 37 } }, // обвес Lotus Emira TEST5 в кованном карбоне
        { type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1000, value: { model: 'f1502', setId: 15, group: 38 } }, // обвес Ford F-150 Raptor TEST5
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1250, value: { model: 'f1502', setId: 16, group: 38 } }, // обвес Ford F-150 Raptor TEST5 в карбоне
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1500, value: { model: 'f1502', setId: 17, group: 38 } }, // обвес Ford F-150 Raptor TEST5 в кованном карбоне
        { type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1000, value: { model: 'civic2', setId: 15, group: 39 } }, // обвес Honda Civic FK1 TEST3 в антихроме
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1100, value: { model: 'civic2', setId: 16, group: 39 } }, // обвес Honda Civic FK1 TEST3 в карбоне
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1200, value: { model: 'civic2', setId: 17, group: 39 } }, // обвес Honda Civic FK1 TEST3 в кованном карбоне
        { type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1000, value: { model: 'macan2', setId: 3, group: 40 } }, // обвес Porsche Macan EV Turbo
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1250, value: { model: 'macan2', setId: 4, group: 40 } }, // обвес Porsche Macan EV Turbo в карбоне
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1500, value: { model: 'macan2', setId: 5, group: 40 } }, // обвес Porsche Macan EV Turbo в кованном карбоне
        { type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1000, value: { model: 'huracan', setId: 13, group: 41 } }, // обвес Lamborghini Huracan Mansory в антихроме
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1250, value: { model: 'huracan', setId: 14, group: 41 } }, // обвес Lamborghini Huracan Mansory в карбоне
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1500, value: { model: 'huracan', setId: 15, group: 41 } }, // обвес Lamborghini Huracan Mansory в кованном карбоне
        { type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1000, value: { model: 'amggt2', setId: 4, group: 42 } }, // обвес Mercedes-AMG GT C192 63 в антихроме
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1250, value: { model: 'amggt2', setId: 5, group: 42 } }, // обвес Mercedes-AMG GT C192 63 в карбоне
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1500, value: { model: 'amggt2', setId: 6, group: 42 } }, // обвес Mercedes-AMG GT C192 63 в кованном карбоне
        { type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1000, value: { model: 'civic2', setId: 18, group: 43 } }, // обвес Honda Civic FK1 TEST4 в антихроме
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1100, value: { model: 'civic2', setId: 19, group: 43 } }, // обвес Honda Civic FK1 TEST4 в карбоне
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1200, value: { model: 'civic2', setId: 20, group: 43 } }, // обвес Honda Civic FK1 TEST4 в кованном карбоне
        { type: 'vehicleSet', color: 'gold', chance: 0.50, price: 2000, value: { model: 's15', setId: 22 } }, // обвес Nissan Slivia S15 Rocket Bunny
        { type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1000, value: { model: 'chiron19', setId: 6, group: 44 } }, // обвес Bugatti Chiron Profilee
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1250, value: { model: 'chiron19', setId: 7, group: 44 } }, // обвес Bugatti Chiron Profilee в карбоне
        //{ type: 'vehicleSet', color: 'gold', chance: 0.50, price: 1500, value: { model: 'chiron19', setId: 8, group: 44 } }, // обвес Bugatti Chiron Profilee в кованном карбоне

        { type: 'wheels', color: 'gold', chance: 0.50, price: 600, value: 236 },
        { type: 'wheels', color: 'gold', chance: 0.50, price: 600, value: 242 },
        { type: 'wheels', color: 'gold', chance: 0.50, price: 600, value: 248 },
        { type: 'wheels', color: 'gold', chance: 0.50, price: 600, value: 287 },
        { type: 'wheels', color: 'gold', chance: 0.50, price: 600, value: 293 },
        { type: 'wheels', color: 'gold', chance: 0.50, price: 600, value: 254 },
        { type: 'wheels', color: 'gold', chance: 0.50, price: 600, value: 260 },
        { type: 'wheels', color: 'gold', chance: 0.50, price: 600, value: 269 },
        { type: 'wheels', color: 'gold', chance: 0.50, price: 600, value: 275 },
        { type: 'wheels', color: 'gold', chance: 0.50, price: 600, value: 281 },

        { type: 'clothes', color: 'gold', chance: 1.00, price: 1500, value: { gender: 0, component: 4, drawable: 2224, textures: 9, isProp: 0 } },
        { type: 'clothes', color: 'gold', chance: 1.00, price: 1500, value: { gender: 0, component: 11, drawable: 2439, textures: 9, isProp: 0 } },
        { type: 'clothes', color: 'gold', chance: 1.00, price: 1500, value: [{ gender: 0, component: 1, drawable: 2146, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2236, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'gold', chance: 1.00, price: 1500, value: [{ gender: 0, component: 5, drawable: 2068, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2424, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'gold', chance: 1.00, price: 1500, value: [{ gender: 0, component: 4, drawable: 2222, textures: 9, isProp: 0 }, { gender: 1, component: 4, drawable: 2242, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'gold', chance: 1.00, price: 1500, value: [{ gender: 0, component: 11, drawable: 2438, textures: 9, isProp: 0 }, { gender: 1, component: 11, drawable: 2433, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'gold', chance: 1.00, price: 1500, value: [{ gender: 0, component: 6, drawable: 2103, textures: 9, isProp: 0 }, { gender: 1, component: 6, drawable: 2123, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'gold', chance: 1.00, price: 1500, value: [{ gender: 0, component: 1, drawable: 2148, textures: 9, isProp: 0 }, { gender: 1, component: 1, drawable: 2135, textures: 9, isProp: 0 }] },
        { type: 'clothes', color: 'gold', chance: 1.00, price: 1500, value: [{ gender: 0, component: 8, drawable: 2140, textures: 9, isProp: 0 }, { gender: 1, component: 8, drawable: 2119, textures: 9, isProp: 0 }] },

        { type: 'tattoo', color: 'gold', chance: 1.00, price: 1000, value: [3363, 6429] },
        { type: 'tattoo', color: 'gold', chance: 1.00, price: 1000, value: [3349, 6415] },
        { type: 'tattoo', color: 'gold', chance: 1.00, price: 1000, value: [3364, 6430] },
        { type: 'tattoo', color: 'gold', chance: 1.00, price: 1000, value: [3348, 6414] },

        { type: 'armourSkin', color: 'gold', chance: 0.65, price: 1250, value: { entityName: 'light', skinId: 111 } },
        { type: 'armourSkin', color: 'gold', chance: 0.65, price: 1250, value: { entityName: 'light', skinId: 115 } },
        { type: 'armourSkin', color: 'gold', chance: 0.65, price: 1250, value: { entityName: 'light', skinId: 118 } },
        { type: 'armourSkin', color: 'gold', chance: 0.65, price: 1250, value: { entityName: 'light', skinId: 117 } },

        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 1500, value: { entityName: 'weapon_heavyrifle', skinId: 2 } }, // Skin Flowers
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 1500, value: { entityName: 'weapon_heavyrifle', skinId: 3 } }, // Skin Pirate
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 1500, value: { entityName: 'weapon_heavyrifle', skinId: 4 } }, // Skin Katana
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 1500, value: { entityName: 'weapon_combatmg_mk2', skinId: 2 } }, // Skin Flowers
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 1500, value: { entityName: 'weapon_combatmg_mk2', skinId: 3 } }, // Skin Pirate
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 1500, value: { entityName: 'weapon_combatmg_mk2', skinId: 4 } }, // Skin Katana
        { type: 'weaponSkin', color: 'gold', chance: 0.85, price: 1500, value: { entityName: 'weapon_heavysniper_mk2', skinId: 13 } }, // Skin Flowers
        { type: 'weaponSkin', color: 'gold', chance: 0.85, price: 1500, value: { entityName: 'weapon_heavysniper_mk2', skinId: 14 } }, // Skin Pirate
        { type: 'weaponSkin', color: 'gold', chance: 0.85, price: 1500, value: { entityName: 'weapon_heavysniper_mk2', skinId: 15 } }, // Skin Katana
        { type: 'weaponSkin', color: 'gold', chance: 0.85, price: 1500, value: { entityName: 'weapon_heavysniper_mk2', skinId: 12 } }, // Skin Fire
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 1500, value: { entityName: 'weapon_heavyrifle', skinId: 1 } }, // Skin Fire
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 1500, value: { entityName: 'weapon_combatmg_mk2', skinId: 1 } }, // Skin Fire
        { type: 'weaponSkin', color: 'gold', chance: 0.85, price: 1500, value: { entityName: 'weapon_heavysniper_mk2', skinId: 16 } }, // Skin Bonus
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 1500, value: { entityName: 'weapon_heavyrifle', skinId: 5 } }, // Skin Bonus
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 1500, value: { entityName: 'weapon_combatmg_mk2', skinId: 5 } }, // Skin Bonus

        { type: 'item', color: 'gold', chance: 1.00, price: 2400, value: { itemId: 328, count: 0 } }, // Тяжелая снайперская винтовка Mk2
        { type: 'item', color: 'gold', chance: 1.00, price: 2400, value: { itemId: 452, count: 0 } }, // Дрон
        { type: 'item', color: 'gold', chance: 1.00, price: 2500, value: { itemId: 967, count: 0 } }, // Самокат как предмет инвентаря
        { type: 'item', color: 'gold', chance: 1.00, price: 3000, value: { itemId: 961, count: 0 } }, // GPS трекер
        { type: 'item', color: 'gold', chance: 1.00, price: 2400, value: { itemId: 627, count: 0 } }, // Дефибриллятор Mk2
        { type: 'item', color: 'gold', chance: 1.00, price: 4000, value: { itemId: 724, count: 0 } }, // Улучшенный металлоискатель
        { type: 'item', color: 'gold', chance: 1.00, price: 4000, value: { itemId: 966, count: 0 } }, // Смена номера
        { type: 'item', color: 'gold', chance: 1.00, price: 4000, value: { itemId: 957, count: 0 } }, // Глушилка связи
        { type: 'item', color: 'gold', chance: 1.00, price: 4000, value: { itemId: 962, count: 0 } }, // Ремень безопасности для мотоцикла

        { type: 'animation', color: 'gold', chance: 1.00, price: 3000, value: 102 }, // Unicycle
        { type: 'animation', color: 'gold', chance: 1.00, price: 3000, value: 101 }, // Lil Octane

        { type: 'mediaSound', color: 'gold', chance: 1.00, price: 3000, value: 'fiftytwo' }, // Озвучка '52'
        { type: 'mediaSound', color: 'gold', chance: 1.00, price: 3000, value: 'hushboy' },  // Озвучка 'Yuuechka - Тише мальчик...'

        { type: 'money', color: 'gold', chance: 108, price: 5000, value: 500000 },
        { type: 'money', color: 'gold', chance: 2, price: 10000, value: 1000000 },
        { type: 'coins', color: 'gold', chance: 35, price: 4000, value: 4000 },
        { type: 'coins', color: 'gold', chance: 23, price: 5000, value: 5000 },
        { type: 'coins', color: 'gold', chance: 2, price: 10000, value: 10000 },

        { type: 'vehicleDiscount', color: 'gold', chance: 1.00, price: 1500, value: 25 },
        { type: 'clothesDiscount', color: 'gold', chance: 1.00, price: 1000, value: 25 },

        // unique
        { type: 'vehicle', color: 'unique', chance: 2.00, price: 9000, customPrice: 5000000, value: 'cc850' }, // Koenisegg CC850
        { type: 'vehicle', color: 'unique', chance: 1.40, price: 10000, customPrice: 5000000, value: 'r66' }, // Вертолёт Robinson R66
    ]
}
