// Этот кейс юзается для ивентов, он не отображается в списке кейсов
module.exports = {
	id: 999,
	title: 'empty',
    status: 'disabled',

	start: ['3000-1-1', 'YYYY-MM-DD'],
	end: ['3000-1-1', 'YYYY-MM-DD'],

	seasonPassId: null,

	// disabledBuy: true,
	readOnly: false,

	sellRule: 0,

	price: 0,
	transferDays: 7,

	colors: [],
    caseContent: [
        // Pajama sets
        {
            type: 'clothesPack',
            color: 'red',
            chance: 0,
            price: 0,
            value: {
                preview: 'LoversPijamasM_preview',
                name: 'LoversPijamasM_pack',
                textures: 6,
                items: [
                    { type: 'clothes', value: { gender: 0, component: 6, drawable: 2138, isProp: 0 } },
                    { type: 'clothes', value: { gender: 0, component: 11, drawable: 2505, isProp: 0 } },
                    { type: 'clothes', value: { gender: 0, component: 4, drawable: 2273, isProp: 0 } },
                    { type: 'animation', value: 396 }, // Nya!
                ],
            },
        },
        {
            type: 'clothesPack',
            color: 'red',
            chance: 0,
            price: 0,
            value: {
                preview: 'LoversPijamasF_preview',
                name: 'LoversPijamasF_pack',
                textures: 6,
                items: [
                    { type: 'clothes', value: { gender: 1, component: 6, drawable: 2167, isProp: 0 } },
                    { type: 'clothes', value: { gender: 1, component: 11, drawable: 2504, isProp: 0 } },
                    { type: 'clothes', value: { gender: 1, component: 4, drawable: 2284, isProp: 0 } },
                    { type: 'animation', value: 396 }, // Nya!
                ],
            },
        },
        // Lovers Pijamas Set
    ]
}
