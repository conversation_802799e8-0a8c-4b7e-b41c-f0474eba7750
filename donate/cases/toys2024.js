module.exports = {
    id: 12,
    title: 'donate.config.cases.toys2024.title',

    start: ['2023-12-26 00:00:00', 'YYYY-MM-DD HH:mm:ss'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    price: 500,
    // discount: 30,
    transferDays: 30,
    // servers: [
    //     'RU1',
    //     'RU2',
    //     'RU3',
    //     'RU4',
    //     'RU5',
    //     'RU6',
    //     'RU7',
    //     'RU8',
    //     'RU9',
    //     'STAGING0',
    //     'DEVELOP0',
    //     'TEST0',
    //     'TEST2'
    // ],

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    // disabledBuy: true,
    readOnly: false,

    colors: [
        {
            color: 'gray',
            chance: 50,
            types: [
                { type: 'clothes', chance: 12 },
            ]
        },
        {
            color: 'blue',
            chance: 23,
            types: [
                { type: 'clothes', chance: 20 },
            ]
        },
        {
            color: 'purple',
            chance: 10,
            types: [
                { type: 'clothes', chance: 20 },
            ]
        },
        {
            color: 'red',
            chance: 6.5,
            types: [
                { type: 'clothes', chance: 20 },
            ]
        },
        {
            color: 'gold',
            chance: 4,
            types: [
                { type: 'clothes', chance: 20 },
            ]
        },
        {
            color: 'unique',
            chance: 0.5,
            types: [
                { type: 'clothes', chance: 20 },
            ]
        }
    ],

    caseContent: [
        {
            type: 'clothes', color: 'gray', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2133, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2111, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'gray', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2142, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2120, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'gray', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2132, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2110, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'gray', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2141, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2119, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'blue', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2131, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2109, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'blue', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2140, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2118, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'blue', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2130, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2108, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'blue', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2139, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2117, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'purple', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2128, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2106, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'purple', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2137, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2115, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'red', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2126, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2104, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'red', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2135, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2113, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'red', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2125, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2103, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'red', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2134, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2112, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'gold', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2129, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2107, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'gold', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2138, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2116, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'unique', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2127, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2105, texture: 0, isProp: 0 }
            ]
        },
        {
            type: 'clothes', color: 'unique', chance: 40, price: 275, value: [
                { gender: 0, component: 7, drawable: 2136, texture: 0, isProp: 0 },
                { gender: 1, component: 7, drawable: 2114, texture: 0, isProp: 0 }
            ]
        }
    ]
};
