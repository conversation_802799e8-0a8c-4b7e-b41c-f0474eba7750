module.exports = {
    id: 6,
    title: 'donate.config.cases.summerSkins2023.title',
    // discount: 50,

    seasonPassId: 3,
    transferDays: 30,

    // Сумма распыления указан в опыте батлпаса. Если батлпас активен
    sellRule: 40, // Если батлпас уже не активен, то задать процент конвертации price в коины. Например 100 exp / 100 * 25 = 25 коинов

    price: 500,

    // servers: ['TEST0', 'RU22'],
    // disabledBuy: true,
    // end: '2023-09-15',
    // readOnly: false,

    start: ['2023-06-02', 'YYYY-MM-DD'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    colors: [
        {
            color: 'gray',
            chance: 120,
        },
        {
            color: 'blue',
            chance: 50,
        },
        {
            color: 'purple',
            chance: 25,
        },
        {
            color: 'red',
            chance: 4,
        },
        {
            color: 'gold',
            chance: 1,
        },
    ],

    caseContent: [
        // armourSkins
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 2   } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 3   } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 4   } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 8   } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 9   } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 13  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 14  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 15  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 17  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 18  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 20  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 21  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 22  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 23  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 24  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 25  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 26  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 27  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 28  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 29  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 30  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 31  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 32  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 33  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 34  } },
        // { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 35  } },

        // weaponSkins
        { type: 'weaponSkin',   color: 'gray',   chance: 1.00,   price: 50,   value: { entityName: 'weapon_heavypistol',        skinId: 0 } },
        { type: 'weaponSkin',   color: 'gray',   chance: 1.00,   price: 50,   value: { entityName: 'weapon_heavypistol',        skinId: 1 } },
        { type: 'weaponSkin',   color: 'gold',   chance: 1.00,   price: 1000, value: { entityName: 'weapon_heavysniper_mk2',    skinId: 0 } },
        { type: 'weaponSkin',   color: 'gold',   chance: 1.00,   price: 1000, value: { entityName: 'weapon_heavysniper_mk2',    skinId: 1 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 250,  value: { entityName: 'weapon_appistol',           skinId: 0 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 250,  value: { entityName: 'weapon_appistol',           skinId: 1 } },
        { type: 'weaponSkin',   color: 'purple', chance: 1.00,   price: 500,  value: { entityName: 'weapon_assaultrifle',       skinId: 0 } },
        { type: 'weaponSkin',   color: 'purple', chance: 1.00,   price: 500,  value: { entityName: 'weapon_assaultrifle',       skinId: 1 } },
        { type: 'weaponSkin',   color: 'red',    chance: 1.00,   price: 750,  value: { entityName: 'weapon_assaultrifle_mk2',   skinId: 1 } },
        { type: 'weaponSkin',   color: 'red',    chance: 1.00,   price: 750,  value: { entityName: 'weapon_assaultrifle_mk2',   skinId: 2 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 250,  value: { entityName: 'weapon_bullpuprifle',       skinId: 0 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 250,  value: { entityName: 'weapon_bullpuprifle',       skinId: 1 } },
        { type: 'weaponSkin',   color: 'purple', chance: 1.00,   price: 500,  value: { entityName: 'weapon_carbinerifle',       skinId: 5 } },
        { type: 'weaponSkin',   color: 'purple', chance: 1.00,   price: 500,  value: { entityName: 'weapon_carbinerifle',       skinId: 6 } },
        { type: 'weaponSkin',   color: 'red',    chance: 1.00,   price: 750,  value: { entityName: 'weapon_carbinerifle_mk2',   skinId: 2 } },
        { type: 'weaponSkin',   color: 'red',    chance: 1.00,   price: 750,  value: { entityName: 'weapon_carbinerifle_mk2',   skinId: 3 } },
        { type: 'weaponSkin',   color: 'gray',   chance: 1.00,   price: 50,   value: { entityName: 'weapon_gusenberg',          skinId: 1 } },
        { type: 'weaponSkin',   color: 'gray',   chance: 1.00,   price: 50,   value: { entityName: 'weapon_gusenberg',          skinId: 2 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 250,  value: { entityName: 'weapon_machinepistol',      skinId: 0 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 250,  value: { entityName: 'weapon_machinepistol',      skinId: 1 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 250,  value: { entityName: 'weapon_marksmanpistol',     skinId: 0 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 250,  value: { entityName: 'weapon_marksmanpistol',     skinId: 1 } },
        { type: 'weaponSkin',   color: 'purple', chance: 1.00,   price: 500,  value: { entityName: 'weapon_revolver',           skinId: 6 } },
        { type: 'weaponSkin',   color: 'purple', chance: 1.00,   price: 500,  value: { entityName: 'weapon_revolver',           skinId: 7 } },
        { type: 'weaponSkin',   color: 'purple', chance: 1.00,   price: 500,  value: { entityName: 'weapon_specialcarbine',     skinId: 0 } },
        { type: 'weaponSkin',   color: 'purple', chance: 1.00,   price: 500,  value: { entityName: 'weapon_specialcarbine',     skinId: 1 } },
        { type: 'weaponSkin',   color: 'gray',   chance: 1.00,   price: 50,   value: { entityName: 'weapon_vintagepistol',      skinId: 0 } },
        { type: 'weaponSkin',   color: 'gray',   chance: 1.00,   price: 50,   value: { entityName: 'weapon_vintagepistol',      skinId: 1 } },
    ]
}
