module.exports = {
    id: 5,
    title: 'donate.config.cases.summer2023.title',
    // discount: 10,

    seasonPassId: 3,

    transferDays: 60,

    // Сумма распыления указан в опыте батлпаса. Если батлпас активен
    sellRule: 40, // Если батлпас уже не активен, то задать процент конвертации price в коины. Например 100 exp / 100 * 25 = 25 коинов

    price: 750,
    // servers: ['TEST0'],

    // disabledBuy: true,
    // readOnly: true,
    // end: '2023-09-15',

    start: ['2023-06-01', 'YYYY-MM-DD'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    transferDays: 30,

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    colors: [
        {
            color: 'gray',
            chance: 60,
            types: [
                { type: 'vehicle',         chance: 25 },
                { type: 'wheels',          chance: 30 },
                { type: 'clothes',         chance: 20 },
                { type: 'tattoo',          chance: 40 },
                { type: 'armourSkin',      chance: 30 },
                { type: 'weaponSkin',      chance: 40 },
                { type: 'item',            chance: 50 },
                { type: 'animation',       chance: 45 },
                // { type: 'status',          chance: 40 },
            ],
        },
        {
            color: 'blue',
            chance: 18,
            types: [
                { type: 'vehicle',         chance: 35 },
                { type: 'vehicleSet',      chance: 30 },
                { type: 'wheels',          chance: 40 },
                { type: 'clothes',         chance: 45 },
                { type: 'tattoo',          chance: 40 },
                { type: 'armourSkin',      chance: 30 },
                { type: 'weaponSkin',      chance: 30 },
                { type: 'item',            chance: 55 },
                { type: 'animation',       chance: 45 },
                // { type: 'status',          chance: 40 },

            ],
        },
        {
            color: 'purple',
            chance: 9,
            types: [
                { type: 'vehicle',         chance: 35 },
                { type: 'vehicleSet',      chance: 30 },
                { type: 'wheels',          chance: 40 },
                { type: 'clothes',         chance: 40 },
                { type: 'tattoo',          chance: 45 },
                { type: 'armourSkin',      chance: 30 },
                { type: 'weaponSkin',      chance: 30 },
                { type: 'item',            chance: 50 },
                { type: 'animation',       chance: 45 },
                // { type: 'status',          chance: 40 },
            ],
        },
        {
            color: 'red',
            chance: 6,
            types: [
                { type: 'vehicle',         chance: 20 },
                { type: 'vehicleSet',      chance: 30 },
                { type: 'wheels',          chance: 30 },
                { type: 'clothes',         chance: 20 },
                { type: 'tattoo',          chance: 45 },
                { type: 'armourSkin',      chance: 25 },
                { type: 'item',            chance: 30 },
                { type: 'animation',       chance: 45 },
                // { type: 'status',          chance: 35 },
            ],
        },
        {
            color: 'gold',
            chance: 3.5,
            types: [
                { type: 'vehicle',         chance: 45 },
                { type: 'vehicleSet',      chance: 25 },
                { type: 'wheels',          chance: 30 },
                { type: 'clothes',         chance: 40 },
                { type: 'tattoo',          chance: 45 },
                { type: 'armourSkin',      chance: 30 },
                { type: 'item',            chance: 30 },
                { type: 'animation',       chance: 45 },
                // { type: 'status',          chance: 35 },
            ],
        }
    ],

    caseContent: [
        // VIP
        // { type: 'status',       color: 'gray',   chance: 1.00,   price: 125,  value: { type: 'Bronze', days: 7 } },
        // { type: 'status',       color: 'blue',   chance: 1.00,   price: 250,  value: { type: 'Silver', days: 7 } },
        // { type: 'status',       color: 'purple', chance: 1.00,   price: 350,  value: { type: 'Gold', days: 7 } },
        // { type: 'status',       color: 'red',    chance: 1.00,   price: 750,  value: { type: 'Platinum', days: 14 } },
        // { type: 'status',       color: 'gold',   chance: 1.00,   price: 1500, value: { type: 'Platinum', days: 90 } },

        // Транспорт
        { type: 'vehicle',      color: 'gray',    chance: 2.00,   price: 125,   customPrice: 15000, value: 'boor'       },
        { type: 'vehicle',      color: 'gray',    chance: 1.00,   price: 125,   customPrice: 35000, value: 'tahoma'     },

        { type: 'vehicle',      color: 'blue',    chance: 3.00,   price: 250,   customPrice: 45000, value: 'manchez3'   },
        { type: 'vehicle',      color: 'blue',    chance: 2.00,   price: 250,   customPrice: 45000, value: 'tulip2'     },
        { type: 'vehicle',      color: 'blue',    chance: 1.00,   price: 250,   customPrice: 90000, value: 'surfer3'    },

        { type: 'vehicle',      color: 'purple',  chance: 3.00,   price: 350,   customPrice: 600000, value: 'r300'       },
        { type: 'vehicle',      color: 'purple',  chance: 2.00,   price: 350,   customPrice: 160000, value: 'broadway'   },
        { type: 'vehicle',      color: 'purple',  chance: 1.00,   price: 350,   customPrice: 420000, value: 'issi8'      },

        { type: 'vehicle',      color: 'red',     chance: 1.00,   price: 750,   customPrice: 450000,  value: 'ae86'       },
        { type: 'vehicle',      color: 'red',     chance: 1.00,   price: 750,   customPrice: 2300000, value: 'fx50s'      },
        { type: 'vehicle',      color: 'red',     chance: 3.00,   price: 750,   customPrice: 900000,  value: 'panthere'   },
        { type: 'vehicle',      color: 'red',     chance: 2.00,   price: 750,   customPrice: 1500000, value: 'entity3'    },

        { type: 'vehicle',      color: 'gold',    chance: 180.00, price: 1000,  customPrice: 830000,  value: 'xc90'       },
        { type: 'vehicle',      color: 'gold',    chance: 150.00, price: 1500,  customPrice: 890000,  value: 'diavel'     },
        { type: 'vehicle',      color: 'gold',    chance: 110.00, price: 1700,  customPrice: 1250000, value: 'm3g81'      },
        { type: 'vehicle',      color: 'gold',    chance: 80.00,  price: 3000,  customPrice: 1600000, value: 'giulia'     },
        { type: 'vehicle',      color: 'gold',    chance: 70.00,  price: 3000,  customPrice: 1600000, value: 'cls2'       },
        { type: 'vehicle',      color: 'gold',    chance: 50.00,  price: 3200,  customPrice: 2000000, value: 'slr'        },
        { type: 'vehicle',      color: 'gold',    chance: 40.00,  price: 3500,  customPrice: 2000000, value: '400z'       },
        { type: 'vehicle',      color: 'gold',    chance: 30.00,  price: 4000,  customPrice: 2500000, value: 'ab2'        },
        { type: 'vehicle',      color: 'gold',    chance: 15.00,  price: 5000,  customPrice: 4000000, value: 'sclass4'    },
        { type: 'vehicle',      color: 'gold',    chance: 10.00,  price: 6000,  customPrice: 4300000, value: 'hummer3'    },
        { type: 'vehicle',      color: 'gold',    chance: 8.00,   price: 6500,  customPrice: 4300000, value: 'mc20'       },
        { type: 'vehicle',      color: 'gold',    chance: 7.00,   price: 7000,  customPrice: 4300000, value: 'rrphantom2' },
        { type: 'vehicle',      color: 'gold',    chance: 4.00,   price: 8000,  customPrice: 4600000, value: 'revuelto'   },
        { type: 'vehicle',      color: 'gold',    chance: 2.50,   price: 10000, customPrice: 5300000, value: 'mistral'    },
        { type: 'vehicle',      color: 'gold',    chance: 1.00,   price: 15000, customPrice: 6600000, value: 'ec120'      },

        // Обвесы
        { type: 'vehicleSet', color: 'blue', chance: 1, price: 250, value: { model: 'xes', setId: 4, group: 24 } },
        //{ type: 'vehicleSet', color: 'blue', chance: 1, price: 250, value: { model: 'xes', setId: 5, group: 24 } },
        //{ type: 'vehicleSet', color: 'blue', chance: 1, price: 250, value: { model: 'xes', setId: 6, group: 24 } },

        { type: 'vehicleSet', color: 'purple', chance: 10, price: 500, value: { model: 'macan', setId: 4, group: 25 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 10, price: 500, value: { model: 'macan', setId: 5, group: 25 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 10, price: 500, value: { model: 'macan', setId: 6, group: 25 } },
        { type: 'vehicleSet', color: 'purple', chance: 8, price: 500, value: { model: 'giulia', setId: 4, group: 26 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 8, price: 500, value: { model: 'giulia', setId: 5, group: 26 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 8, price: 500, value: { model: 'giulia', setId: 6, group: 26 } },
        { type: 'vehicleSet', color: 'purple', chance: 8, price: 500, value: { model: 'giulia', setId: 7, group: 26 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 8, price: 500, value: { model: 'giulia', setId: 8, group: 26 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 8, price: 500, value: { model: 'giulia', setId: 9, group: 26 } },
        { type: 'vehicleSet', color: 'purple', chance: 8, price: 500, value: { model: 'giulia', setId: 10, group: 26 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 8, price: 500, value: { model: 'giulia', setId: 11, group: 26 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 8, price: 500, value: { model: 'giulia', setId: 12, group: 26 } },
        { type: 'vehicleSet', color: 'purple', chance: 6, price: 500, value: { model: 'panamera17turbo', setId: 6, group: 27 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 6, price: 500, value: { model: 'panamera17turbo', setId: 7, group: 27 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 6, price: 500, value: { model: 'panamera17turbo', setId: 8, group: 27 } },
        { type: 'vehicleSet', color: 'purple', chance: 5, price: 500, value: { model: '918s', setId: 6, group: 28 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 5, price: 500, value: { model: '918s', setId: 7, group: 28 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 5, price: 500, value: { model: '918s', setId: 8, group: 28 } },
        { type: 'vehicleSet', color: 'purple', chance: 3, price: 500, value: { model: '400z', setId: 3, group: 29 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 3, price: 500, value: { model: '400z', setId: 4, group: 29 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 3, price: 500, value: { model: '400z', setId: 5, group: 29 } },
        { type: 'vehicleSet', color: 'purple', chance: 3, price: 500, value: { model: '400z', setId: 6, group: 29 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 3, price: 500, value: { model: '400z', setId: 7, group: 29 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 3, price: 500, value: { model: '400z', setId: 8, group: 29 } },
        { type: 'vehicleSet', color: 'purple', chance: 3, price: 500, value: { model: '400z', setId: 9, group: 29 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 3, price: 500, value: { model: '400z', setId: 10, group: 29 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 3, price: 500, value: { model: '400z', setId: 11, group: 29 } },

        { type: 'vehicleSet', color: 'red', chance: 5, price: 750, value: { model: 'g63', setId: 11, group: 30 } },
        //{ type: 'vehicleSet', color: 'red', chance: 5, price: 750, value: { model: 'g63', setId: 12, group: 30 } },
        //{ type: 'vehicleSet', color: 'red', chance: 5, price: 750, value: { model: 'g63', setId: 13, group: 30 } },
        { type: 'vehicleSet', color: 'red', chance: 4, price: 750, value: { model: 'gt63s', setId: 7, group: 31 } },
        //{ type: 'vehicleSet', color: 'red', chance: 4, price: 750, value: { model: 'gt63s', setId: 8, group: 31 } },
        //{ type: 'vehicleSet', color: 'red', chance: 4, price: 750, value: { model: 'gt63s', setId: 9, group: 31 } },
        { type: 'vehicleSet', color: 'red', chance: 3, price: 750, value: { model: 'veyron', setId: 8, group: 32 } },
        //{ type: 'vehicleSet', color: 'red', chance: 3, price: 750, value: { model: 'veyron', setId: 9, group: 32 } },
        //{ type: 'vehicleSet', color: 'red', chance: 3, price: 750, value: { model: 'veyron', setId: 10, group: 32 } },
        { type: 'vehicleSet', color: 'red', chance: 2, price: 750, value: { model: 'q8', setId: 16, group: 33 } },
        //{ type: 'vehicleSet', color: 'red', chance: 2, price: 750, value: { model: 'q8', setId: 17, group: 33 } },
        //{ type: 'vehicleSet', color: 'red', chance: 2, price: 750, value: { model: 'q8', setId: 18, group: 33 } },

        { type: 'vehicleSet', color: 'gold', chance: 9, price: 1500, value: { model: 'cls2', setId: 4, group: 34 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 9, price: 1500, value: { model: 'cls2', setId: 5, group: 34 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 9, price: 1500, value: { model: 'cls2', setId: 6, group: 34 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 9, price: 1500, value: { model: 'cls2', setId: 7, group: 34 } },
        { type: 'vehicleSet', color: 'gold', chance: 7, price: 1500, value: { model: 'sclass3', setId: 8, group: 35 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 7, price: 1500, value: { model: 'sclass3', setId: 9, group: 35 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 7, price: 1500, value: { model: 'sclass3', setId: 10, group: 35 } },
        { type: 'vehicleSet', color: 'gold', chance: 5, price: 1500, value: { model: 'hummer3', setId: 3, group: 36 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 5, price: 1500, value: { model: 'hummer3', setId: 4, group: 36 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 5, price: 1500, value: { model: 'hummer3', setId: 5, group: 36 } },
        { type: 'vehicleSet', color: 'gold', chance: 3, price: 1500, value: { model: 'rrphantom2', setId: 4, group: 37 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 3, price: 1500, value: { model: 'rrphantom2', setId: 5, group: 37 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 3, price: 1500, value: { model: 'rrphantom2', setId: 6, group: 37 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 3, price: 1500, value: { model: 'rrphantom2', setId: 7, group: 37 } },
        { type: 'vehicleSet', color: 'gold', chance: 1, price: 1500, value: { model: 'sclass4', setId: 14, group: 38 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 1, price: 1500, value: { model: 'sclass4', setId: 15, group: 38 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 1, price: 1500, value: { model: 'sclass4', setId: 16, group: 38 } },


        // Диски
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 78   },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 106  },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 141  },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 176  },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 113  },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 148  },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 99   },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 134  },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 169  },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 85   },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 120  },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 155  },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 92   },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 127  },
        { type: 'wheels',	    color: 'gray',     chance: 1.00,  price: 150,   value: 162  },

        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 79   },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 107  },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 142  },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 177  },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 114  },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 149  },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 100  },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 135  },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 170  },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 86   },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 121  },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 156  },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 93   },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 128  },
        { type: 'wheels',	    color: 'blue',     chance: 1.00,  price: 300,   value: 163  },

        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 80   },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 108  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 143  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 178  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 115  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 150  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 101  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 136  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 171  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 87   },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 122  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 157  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 94   },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 129  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 164  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 81   },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 109  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 144  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 179  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 116  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 151  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 102  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 137  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 172  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 88   },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 123  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 158  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 95   },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 130  },
        { type: 'wheels',	    color: 'purple',   chance: 1.00,  price: 500,   value: 165  },

        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 82   },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 110  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 145  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 180  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 117  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 152  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 103  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 138  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 173  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 89   },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 96   },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 131  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 166  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 124  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 159  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 83   },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 111  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 146  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 181  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 118  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 153  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 104  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 139  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 174  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 90   },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 97   },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 132  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 167  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 125  },
        { type: 'wheels',	    color: 'red',      chance: 1.00,  price: 750,  value: 160  },

        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 84   },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 112  },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 147  },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 182  },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 119  },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 154  },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 105  },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 140  },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 175  },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 91   },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 126  },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 161  },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 98   },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 133  },
        { type: 'wheels',	    color: 'gold',     chance: 1.00,  price: 1500,  value: 168  },

        // Одежда
        { type: 'clothes',      color: 'gray',     chance: 1.00,  price: 125,   value: [ { gender: 0, component: 4,  drawable: 2164, textures: 8,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2169, textures: 7,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gray',     chance: 1.00,  price: 125,   value: [ { gender: 0, component: 11, drawable: 2364, textures: 8,  isProp: 0 }, { gender: 1, component: 11, drawable: 2330, textures: 7,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gray',     chance: 1.00,  price: 125,   value: [ { gender: 0, component: 4,  drawable: 2171, texture:  0,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2192, textures: 7,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gray',     chance: 1.00,  price: 125,   value: [ { gender: 0, component: 11, drawable: 2366, textures: 7,  isProp: 0 }, { gender: 1, component: 11, drawable: 2360, textures: 7,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gray',     chance: 1.00,  price: 125,   value: [ { gender: 0, component: 11, drawable: 2367, textures: 8,  isProp: 0 }, { gender: 1, component: 11, drawable: 2364, textures: 7,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gray',     chance: 1.00,  price: 125,   value: [ { gender: 0, component: 11, drawable: 2368, textures: 6,  isProp: 0 }, { gender: 1, component: 11, drawable: 2361, textures: 8,   isProp: 0 } ] },

        { type: 'clothes',      color: 'blue',     chance: 1.00,  price: 250,   value: [ { gender: 0, component: 4,  drawable: 2179, textures: 9,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2193, textures: 7,   isProp: 0 } ] },
        { type: 'clothes',      color: 'blue',     chance: 1.00,  price: 250,   value: [ { gender: 0, component: 11, drawable: 2356, textures: 10, isProp: 0 }, { gender: 1, component: 11, drawable: 2362, textures: 7,   isProp: 0 } ] },
        { type: 'clothes',      color: 'blue',     chance: 1.00,  price: 250,   value: [ { gender: 0, component: 6,  drawable: 2065, textures: 7,  isProp: 0 }, { gender: 1, component: 11, drawable: 2329, textures: 8,   isProp: 0 } ] },
        { type: 'clothes',      color: 'blue',     chance: 1.00,  price: 250,   value: [ { gender: 0, component: 11, drawable: 2340, textures: 7,  isProp: 0 }, { gender: 1, component: 11, drawable: 2332, textures: 8,   isProp: 0 } ] },
        { type: 'clothes',      color: 'blue',     chance: 1.00,  price: 250,   value: [ { gender: 0, component: 4,  drawable: 2165, textures: 7,  isProp: 0 }, { gender: 1, component: 11, drawable: 2338, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'blue',     chance: 1.00,  price: 250,   value: [ { gender: 0, component: 11, drawable: 2341, textures: 7,  isProp: 0 }, { gender: 1, component: 11, drawable: 2334, textures: 11,  isProp: 0 } ] },
        { type: 'clothes',      color: 'blue',     chance: 1.00,  price: 250,   value: [ { gender: 0, component: 4,  drawable: 2172, texture:  0,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2181, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'blue',     chance: 1.00,  price: 250,   value: [ { gender: 0, component: 4,  drawable: 2169, texture:  0,  isProp: 0 }, { gender: 1, component: 11, drawable: 2339, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'blue',     chance: 1.00,  price: 250,   value: [ { gender: 0, component: 11, drawable: 2348, texture:  0,  isProp: 0 }, { gender: 1, component: 11, drawable: 2351, texture:  2,   isProp: 0 } ] },
        { type: 'clothes',      color: 'blue',     chance: 1.00,  price: 250,   value: [ { gender: 0, component: 4,  drawable: 2175, texture:  0,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2172, texture:  3,   isProp: 0 } ] },
        { type: 'clothes',      color: 'blue',     chance: 1.00,  price: 250,   value: [ { gender: 0, component: 11, drawable: 2339, texture:  7,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2173, texture:  8,   isProp: 0 } ] },

        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 4,  drawable: 2178, textures: 10, isProp: 0 }, { gender: 1, component: 4,  drawable: 2175, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 11, drawable: 2343, textures: 8,  isProp: 0 }, { gender: 1, component: 11, drawable: 2340, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 11, drawable: 2345, textures: 8,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2178, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 6,  drawable: 2067, textures: 4,  isProp: 0 }, { gender: 1, component: 11, drawable: 2333, textures: 8,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 4,  drawable: 2166, textures: 6,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2171, textures: 7,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 11, drawable: 2346, textures: 8,  isProp: 0 }, { gender: 1, component: 11, drawable: 2331, textures: 7,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 4,  drawable: 2182, textures: 7,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2191, textures: 8,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 11, drawable: 2365, textures: 7,  isProp: 0 }, { gender: 1, component: 11, drawable: 2359, textures: 8,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 1,  drawable: 2018, textures: 10, isProp: 1 }, { gender: 1, component: 4,  drawable: 2194, textures: 7,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 0,  drawable: 2077, textures: 8,  isProp: 1 }, { gender: 1, component: 11, drawable: 2336, textures: 8,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 4,  drawable: 2177, textures: 10, isProp: 0 }, { gender: 1, component: 4,  drawable: 2170, textures: 8,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 11, drawable: 2363, textures: 6,  isProp: 0 }, { gender: 1, component: 11, drawable: 2328, textures: 8,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 4,  drawable: 2170, texture:  0,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2180, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 11, drawable: 2351, texture:  0,  isProp: 0 }, { gender: 1, component: 11, drawable: 2337, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 1,  drawable: 2017, texture:  0,  isProp: 1 }, { gender: 1, component: 4,  drawable: 2184, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'purple',   chance: 1.00,  price: 500,   value: [ { gender: 0, component: 4,  drawable: 2176, texture:  2,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2180, texture:  0,   isProp: 0 } ] },

        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 4,  drawable: 2173, textures: 8,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2186, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 11, drawable: 2354, textures: 8,  isProp: 0 }, { gender: 1, component: 11, drawable: 2357, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 11, drawable: 2342, textures: 7,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2078, textures: 5,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 4,  drawable: 2181, textures: 8,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2085, textures: 20,  isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 11, drawable: 2344, textures: 8,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2084, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 4,  drawable: 2183, textures: 7,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2174, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 11, drawable: 2369, textures: 7,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2177, textures: 4,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 1,  drawable: 2019, textures: 10, isProp: 1 }, { gender: 1, component: 4,  drawable: 2179, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 11, drawable: 2358, textures: 10, isProp: 0 }, { gender: 1, component: 11, drawable: 2349, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 4,  drawable: 2167, texture:  0,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2190, textures: 5,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 11, drawable: 2349, texture:  0,  isProp: 0 }, { gender: 1, component: 11, drawable: 2352, textures: 5,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 6,  drawable: 2071, textures: 20, isProp: 0 }, { gender: 1, component: 6,  drawable: 2083, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 6,  drawable: 2068, textures: 5,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2045, textures: 7,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 1,  drawable: 2015, texture:  0,  isProp: 1 }, { gender: 1, component: 11, drawable: 2355, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 4,  drawable: 2168, texture:  0,  isProp: 0 }, { gender: 1, component: 11, drawable: 2350, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 11, drawable: 2352, texture:  0,  isProp: 0 }, { gender: 1, component: 11, drawable: 2348, texture:  4,   isProp: 0 } ] },
        { type: 'clothes',      color: 'red',      chance: 1.00,  price: 750,  value: [ { gender: 0, component: 6,  drawable: 2070, texture:  0,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2080, texture:  0,   isProp: 0 } ] },

        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 4,  drawable: 2180, textures: 5,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2176, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 11, drawable: 2362, textures: 6,  isProp: 0 }, { gender: 1, component: 11, drawable: 2347, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 5,  drawable: 2052, textures: 7,  isProp: 0 }, { gender: 1, component: 11, drawable: 2341, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 11, drawable: 2347, textures: 6,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2046, textures: 3,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 6,  drawable: 2072, textures: 10, isProp: 0 }, { gender: 1, component: 4,  drawable: 2187, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 5,  drawable: 2051, textures: 12, isProp: 0 }, { gender: 1, component: 4,  drawable: 2188, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 11, drawable: 2350, texture:  0,  isProp: 0 }, { gender: 1, component: 11, drawable: 2358, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 1,  drawable: 2014, texture:  0,  isProp: 1 }, { gender: 1, component: 5,  drawable: 2047, textures: 12,  isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 5,  drawable: 2053, textures: 3,  isProp: 0 }, { gender: 1, component: 1,  drawable: 2017, texture:  0,   isProp: 1 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 4,  drawable: 2174, textures: 8,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2081, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 11, drawable: 2353, textures: 8,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2183, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 1,  drawable: 2016, texture:  0,  isProp: 1 }, { gender: 1, component: 6,  drawable: 2086, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 11, drawable: 2360, texture:  0,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2189, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 6,  drawable: 2069, texture:  0,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2079, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 6,  drawable: 2066, texture:  0,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2075, texture:  0,   isProp: 0 } ] },
        { type: 'clothes',      color: 'gold',     chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 6,  drawable: 2073, texture:  0,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2087, texture:  0,   isProp: 0 } ] },

        // Татуировки
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3284, 6350 ] },
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3285, 6351 ] },
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3288, 6354 ] },
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3311, 6377 ] },
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3312, 6378 ] },
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3313, 6379 ] },
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3293, 6359 ] },
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3295, 6361 ] },
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3299, 6365 ] },
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3300, 6366 ] },
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3301, 6367 ] },
        { type: 'tattoo',       color: 'gray',     chance: 1.00,  price: 125,   value: [3307, 6373 ] },

        { type: 'tattoo',       color: 'blue',     chance: 1.00,  price: 250,   value: [3286, 6352 ] },
        { type: 'tattoo',       color: 'blue',     chance: 1.00,  price: 250,   value: [3287, 6353 ] },
        { type: 'tattoo',       color: 'blue',     chance: 1.00,  price: 250,   value: [3289, 6355 ] },
        { type: 'tattoo',       color: 'blue',     chance: 1.00,  price: 250,   value: [3292, 6358 ] },
        { type: 'tattoo',       color: 'blue',     chance: 1.00,  price: 250,   value: [3298, 6364 ] },
        { type: 'tattoo',       color: 'blue',     chance: 1.00,  price: 250,   value: [3297, 6363 ] },
        { type: 'tattoo',       color: 'blue',     chance: 1.00,  price: 250,   value: [3306, 6372 ] },
        { type: 'tattoo',       color: 'blue',     chance: 1.00,  price: 250,   value: [3308, 6374 ] },
        { type: 'tattoo',       color: 'blue',     chance: 1.00,  price: 250,   value: [3310, 6376 ] },
        { type: 'tattoo',       color: 'blue',     chance: 1.00,  price: 250,   value: [3302, 6368 ] },

        { type: 'tattoo',       color: 'purple',   chance: 1.00,  price: 500,   value: [3296, 6362 ] },
        { type: 'tattoo',       color: 'purple',   chance: 1.00,  price: 500,   value: [3294, 6360 ] },
        { type: 'tattoo',       color: 'purple',   chance: 1.00,  price: 500,   value: [3309, 6375 ] },

        { type: 'tattoo',       color: 'red',      chance: 1.00,  price: 750,  value: [3290, 6356 ] },
        { type: 'tattoo',       color: 'red',      chance: 1.00,  price: 750,  value: [3303, 6369 ] },

        { type: 'tattoo',       color: 'gold',     chance: 1.00,  price: 1500,  value: [3305, 6371 ] },
        { type: 'tattoo',       color: 'gold',     chance: 1.00,  price: 1500,  value: [3304, 6370 ] },

        // Скины на броню
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 3 } },  // Бронежилет Валдберис +
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 8 } },  // Бронежилет Аниме так себе
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 9 } },  // Бронежилет Аниме так себе
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 13 } }, // Бронежилет мем Доги так себе
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 23 } }, // Бронежилет Evangelion Dark Red
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 24 } }, // Бронежилет Evangelion Gray
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 25 } }, // Бронежилет Evangelion Purple
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 26 } }, // Бронежилет Evangelion Red
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 27 } }, // Бронежилет Evangelion Brown
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 28 } }, // Бронежилет Evangelion Green
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 29 } }, // Бронежилет Evangelion Dark Blue
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 30 } }, // Бронежилет Evangelion Blue
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 31 } }, // Бронежилет Evangelion Gray 2
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 32 } }, // Бронежилет Evangelion Purple
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 33 } }, // Бронежилет Evangelion Black
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 34 } }, // Бронежилет Evangelion Yellow
        { type: 'armourSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'light', skinId: 35 } }, // Бронежилет Evangelion White

        { type: 'armourSkin',   color: 'blue',     chance: 1.00,  price: 250,   value: { entityName: 'light', skinId: 2 } },  // Бронежилет Почта россии
        { type: 'armourSkin',   color: 'blue',     chance: 1.00,  price: 250,   value: { entityName: 'light', skinId: 4 } },  // Бронежилет Алиэкспресс
        { type: 'armourSkin',   color: 'blue',     chance: 1.00,  price: 250,   value: { entityName: 'light', skinId: 20 } }, // Бронежилет Brand collections Monster Green

        { type: 'armourSkin',   color: 'purple',   chance: 1.00,  price: 500,   value: { entityName: 'light', skinId: 21 } }, // Бронежилет Аниме прикольное
        { type: 'armourSkin',   color: 'purple',   chance: 1.00,  price: 500,   value: { entityName: 'light', skinId: 22 } }, // Бронежилет Off-White
        { type: 'armourSkin',   color: 'purple',   chance: 1.00,  price: 500,   value: { entityName: 'light', skinId: 17 } }, // Бронежилет Brand collections Bape Purple

        { type: 'armourSkin',   color: 'red',      chance: 1.00,  price: 750,  value: { entityName: 'light', skinId: 15 } }, // Бронежилет Brand collections Supreme Red
        { type: 'armourSkin',   color: 'red',      chance: 1.00,  price: 750,  value: { entityName: 'light', skinId: 18 } }, // Бронежилет Brand collections NASA Blue

        { type: 'armourSkin',   color: 'gold',     chance: 1.00,  price: 1500,  value: { entityName: 'light', skinId: 14 } }, // Бронежилет Brand collections Louis Vuitton Black


        // Скины на оружие
        { type: 'weaponSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'WEAPON_CARBINERIFLE',     skinId: 1 } }, // Аниме так себе
        { type: 'weaponSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'WEAPON_COMBATPDW',        skinId: 0 } }, // Аниме так себе
        { type: 'weaponSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'WEAPON_HEAVYSHOTGUN',     skinId: 2 } }, // Что-то непонятное
        { type: 'weaponSkin',   color: 'gray',     chance: 1.00,  price: 125,   value: { entityName: 'WEAPON_REVOLVER',         skinId: 2 } }, // Говно

        { type: 'weaponSkin',   color: 'blue',     chance: 1.00,  price: 250,   value: { entityName: 'WEAPON_CARBINERIFLE',     skinId: 4 } }, // Аниме Норм
        { type: 'weaponSkin',   color: 'blue',     chance: 1.00,  price: 250,   value: { entityName: 'WEAPON_CARBINERIFLE_MK2', skinId: 0 } }, // Цветной микс
        { type: 'weaponSkin',   color: 'blue',     chance: 1.00,  price: 250,   value: { entityName: 'WEAPON_SMG_MK2',          skinId: 1 } }, // Цветной микс
        { type: 'weaponSkin',   color: 'blue',     chance: 1.00,  price: 250,   value: { entityName: 'WEAPON_REVOLVER',         skinId: 0 } }, // Цветной микс
        { type: 'weaponSkin',   color: 'blue',     chance: 1.00,  price: 250,   value: { entityName: 'WEAPON_HEAVYSHOTGUN',     skinId: 1 } }, // Цветной микс

        { type: 'weaponSkin',   color: 'purple',   chance: 1.00,  price: 500,   value: { entityName: 'WEAPON_COMBATPDW',        skinId: 2 } }, // Золотая калиграфия
        { type: 'weaponSkin',   color: 'purple',   chance: 1.00,  price: 500,   value: { entityName: 'WEAPON_REVOLVER',         skinId: 4 } }, // Прикольный

        // Предметы инвентаря
        { type: 'item',         color: 'gray',     chance: 1.00,  price: 100,   value: { itemId: 736, count: 8  } }, // Гавайская пицца
        { type: 'item',         color: 'gray',     chance: 1.00,  price: 100,   value: { itemId: 737, count: 0  } }, // Консервированные бобы

        { type: 'item',         color: 'blue',     chance: 1.00,  price: 200,   value: { itemId: 89, count: 20  } }, // Желтая аптечка (хил 75хп)
        { type: 'item',         color: 'blue',     chance: 1.00,  price: 200,   value: { itemId: 732, count: 0  } }, // Протеиновый батончик
        { type: 'item',         color: 'blue',     chance: 1.00,  price: 200,   value: { itemId: 735, count: 0  } }, // Странный бургер
        { type: 'item',         color: 'blue',     chance: 1.00,  price: 200,   value: { itemId: 734, count: 0  } }, // Благодарственное письмо губернатора
        { type: 'item',         color: 'blue',     chance: 1.00,  price: 200,   value: { itemId: 728, count: 0  } }, // Биодобавка 1 уровня

        { type: 'item',         color: 'purple',   chance: 1.00,  price: 300,   value: { itemId: 525, count: 0  } }, // BIOLINK
        { type: 'item',         color: 'purple',   chance: 1.00,  price: 300,   value: { itemId: 726, count: 0  } }, // Экспериментальная пилюля Имморталитикс
        { type: 'item',         color: 'purple',   chance: 1.00,  price: 300,   value: { itemId: 738, count: 0  } }, // Непредсказуемый коктейль Реднека
        { type: 'item',         color: 'purple',   chance: 1.00,  price: 300,   value: { itemId: 727, count: 25 } }, // (25 штук) Качественный ремонтный набор
        { type: 'item',         color: 'purple',   chance: 1.00,  price: 300,   value: { itemId: 729, count: 0  } }, // Биодобавка 2 уровня
        { type: 'item',         color: 'purple',   chance: 1.00,  price: 300,   value: { itemId: 337, count: 0  } }, // Винтовка Marksman Mk2
        { type: 'item',         color: 'purple',   chance: 1.00,  price: 300,   value: { itemId: 625, count: 30 } }, // Адреналин (= эпинефрин)

        { type: 'item',         color: 'red',      chance: 1.00,  price: 500,   value: { itemId: 725, count: 0  } }, // Ремонтный комплект для оружия
        { type: 'item',         color: 'red',      chance: 1.00,  price: 500,   value: { itemId: 733, count: 10 } }, // Стайлинг ManeMaster
        { type: 'item',         color: 'red',      chance: 1.00,  price: 500,   value: { itemId: 730, count: 0  } }, // Биодобавка 3 уровня
        { type: 'item',         color: 'red',      chance: 1.00,  price: 500,   value: { itemId: 731, count: 20 } }, // (20 штук) Капсулы восстановления

        { type: 'item',         color: 'gold',     chance: 10.00, price: 1000,  value: { itemId: 627, count: 0  } }, // Дефибриллятор Mk2
        { type: 'item',         color: 'gold',     chance: 6.00,  price: 1000,  value: { itemId: 452, count: 0  } }, // Дрон
        { type: 'item',         color: 'gold',     chance: 4.00,  price: 1000,  value: { itemId: 328, count: 0  } }, // Тяжелая снайперская винтовка Mk2
        { type: 'item',         color: 'gold',     chance: 7.00,  price: 1000,  value: { itemId: 757, count: 0  } }, // Ролики
        { type: 'item',         color: 'gold',     chance: 5.00,  price: 1000,  value: { itemId: 724, count: 0  } }, // Улучшенный металлоискатель
        { type: 'item',         color: 'gold',     chance: 7.00,  price: 1000,  value: { itemId: 756, count: 0  } }, // Скейтборд

        // Скидка на транспорт
        { type: 'vehicleDiscount', color: 'gray',   chance: 1.00, price: 125,   value: 5  },
        { type: 'vehicleDiscount', color: 'blue',   chance: 1.00, price: 250,   value: 10 },
        { type: 'vehicleDiscount', color: 'purple', chance: 1.00, price: 500,   value: 15 },
        { type: 'vehicleDiscount', color: 'red',    chance: 1.00, price: 750,  value: 20 },
        { type: 'vehicleDiscount', color: 'gold',   chance: 1.00, price: 1500,  value: 25 },

        // Скидка на одежду
        { type: 'clothesDiscount', color: 'gray',   chance: 1.00, price: 125,   value: 5  },
        { type: 'clothesDiscount', color: 'blue',   chance: 1.00, price: 250,   value: 10 },
        { type: 'clothesDiscount', color: 'purple', chance: 1.00, price: 500,   value: 15 },
        { type: 'clothesDiscount', color: 'red',    chance: 1.00, price: 750,   value: 20 },
        { type: 'clothesDiscount', color: 'gold',   chance: 1.00, price: 1000,  value: 25 },

        // animations
        { type: 'animation',    color: 'gray',   chance: 1.00,   price: 125,  value: 77  },
        { type: 'animation',    color: 'gray',   chance: 1.00,   price: 125,  value: 141 },
        { type: 'animation',    color: 'gray',   chance: 1.00,   price: 125,  value: 158 },

        { type: 'animation',    color: 'blue',   chance: 1.00,   price: 250,  value: 98  },
        { type: 'animation',    color: 'blue',   chance: 1.00,   price: 250,  value: 255 },

        { type: 'animation',    color: 'purple', chance: 1.00,   price: 500,  value: 110 },
        { type: 'animation',    color: 'purple', chance: 1.00,   price: 500,  value: 91  },
        { type: 'animation',    color: 'purple', chance: 1.00,   price: 500,  value: 97  },

        { type: 'animation',    color: 'red',    chance: 1.00,   price: 750,  value: 245 },
        { type: 'animation',    color: 'red',    chance: 1.00,   price: 750,  value: 248 },

        { type: 'animation',    color: 'gold',   chance: 1.00,   price: 1000,  value: 257 },
        { type: 'animation',    color: 'gold',   chance: 1.00,   price: 1000,  value: 215 },
    ]
}
