module.exports = {
    id: 19,
    title: 'donate.config.cases.winter2025.title',

    start: ['2024-11-30', 'YYYY-MM-DD'],
    end: ['2025-03-15 06:00:00', 'YYYY-MM-DD HH:mm:ss'],

    // servers: ['TEST0'],
    seasonPassId: 6,

    disabledBuy: true,
    readOnly: false,

    // Сумма распыления указан в опыте батлпаса. Если батлпас активен
    sellRule: 40, // Если батлпас уже не активен, то задать процент конвертации price в коины. Например 100 exp / 100 * 25 = 25 коинов

    price: 750,
    transferDays: 60,

    colors: [
        {
            color: 'gray',
            chance: 50,
            types: [
                { type: 'vehicleDiscount', chance: 8 },
                { type: 'clothesDiscount', chance: 8 },
                { type: 'item',            chance: 10 },
                { type: 'animation',       chance: 10 },
                { type: 'tattoo',          chance: 10 },
                { type: 'clothes',         chance: 7 },
                { type: 'vehicle',         chance: 8 },
            ],
        },
        {
            color: 'blue',
            chance: 30,
            types: [
                { type: 'vehicleDiscount', chance: 8 },
                { type: 'clothesDiscount', chance: 8 },
                { type: 'wheels',          chance: 10 },
                { type: 'item',            chance: 8 },
                { type: 'clothes',         chance: 8 },
                { type: 'tattoo',          chance: 10 },
                { type: 'animation',       chance: 10 },
                { type: 'vehicle',         chance: 2.5 },
            ],
        },
        {
            color: 'purple',
            chance: 9.5,
            types: [
                { type: 'vehicleDiscount', chance: 8 },
                { type: 'clothesDiscount', chance: 8 },
                { type: 'wheels',          chance: 10 },
                { type: 'vehicleDetail',   chance: 8 },
                { type: 'clothes',         chance: 12 },
                { type: 'armourSkin',      chance: 8 },
                { type: 'weaponSkin',      chance: 12 },
                { type: 'tattoo',          chance: 10 },
                { type: 'vehicleSet',      chance: 10 },
            ],
        },
        {
            color: 'red',
            chance: 5,
            types: [
                { type: 'vehicleDiscount', chance: 8 },
                { type: 'clothesDiscount', chance: 8 },
                { type: 'item',            chance: 10 },
                { type: 'vehicleDetail',   chance: 9 },
                { type: 'clothes',         chance: 14 },
                { type: 'armourSkin',      chance: 7 },
                { type: 'weaponSkin',      chance: 12 },
                { type: 'tattoo',          chance: 10 },
                { type: 'animation',       chance: 9 },
                { type: 'vehicleSet',      chance: 10 },
                { type: 'vehicle',         chance: 6 },
            ],
        },
        {
            color: 'gold',
            chance: 2.8,
            types: [
                { type: 'vehicleDiscount', chance: 8 },
                { type: 'clothesDiscount', chance: 8 },
                { type: 'item',            chance: 10 },
                { type: 'wheels',          chance: 10 },
                { type: 'clothes',         chance: 11 },
                { type: 'weaponSkin',      chance: 13 },
                { type: 'armourSkin',      chance: 10 },
                { type: 'animation',       chance: 6 },
                { type: 'vehicleSet',      chance: 7 },
                { type: 'vehicle',         chance: 17 },
            ],
        }
    ],

    caseContent: [
        // gray start
        { type: 'vehicleDiscount', color: 'gray',   chance: 1, price: 500,   value: 5 },
        { type: 'clothesDiscount', color: 'gray',   chance: 1, price: 500,   value: 5  },

        { type: 'item', value: { itemId: 334, count: 10 }, chance: 1, price: 500,  color: 'gray' }, // ИРП армии США
        { type: 'item', value: { itemId: 728, count: 0  }, chance: 1, price: 500,  color: 'gray' }, // Биодобавка 1 уровня
        { type: 'item', value: { itemId: 965, count: 0  }, chance: 1, price: 500,  color: 'gray' }, // Указ о налоговых льготах
        { type: 'item', value: { itemId: 963, count: 0  }, chance: 1, price: 500,  color: 'gray' }, // Сверхтяжелый бронежилет
        { type: 'item', value: { itemId: 733, count: 10 }, chance: 1, price: 500,  color: 'gray' }, // Стайлинг ManeMaster

        { type: 'clothes', price: 500, color: 'gray',  chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2484, textures: 6,  isProp: 0 }, { gender: 1, component: 11, drawable: 2478, textures: 7,  isProp: 0 } ] },
        { type: 'clothes', price: 500, color: 'gray',  chance: 1.0, value: [ { gender: 0, component: 6,  drawable: 2130, textures: 7,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2153, textures: 7,  isProp: 0 } ] },

        { type: 'tattoo', color: 'gray', chance: 1.00, price: 500, value: [ 3389, 6456 ] },
        { type: 'tattoo', color: 'gray', chance: 1.00, price: 500, value: [ 3390, 6457 ] },
        { type: 'tattoo', color: 'gray', chance: 1.00, price: 500, value: [ 3392, 6459 ] },
        { type: 'tattoo', color: 'gray', chance: 1.00, price: 500, value: [ 3397, 6463 ] },
        { type: 'tattoo', color: 'gray', chance: 1.00, price: 500, value: [ 3396, 6462 ] },

        { type: 'animation', price: 500,  color: 'gray',   chance: 1.0, value: 369 }, // Snow Flakey
        { type: 'animation', price: 500,  color: 'gray',   chance: 1.0, value: 374 }, // Toys Flip
        { type: 'animation', price: 500,  color: 'gray',   chance: 1.0, value: 325 }, // Bood Up Groove, prem 24-31 lvl
        { type: 'animation', price: 500,  color: 'gray',   chance: 1.0, value: 179 }, // Flamenco, free 22-29 lvl
        { type: 'animation', price: 500,  color: 'gray',   chance: 1.0, value: 368 }, // Ice Moves
        { type: 'animation', price: 500,  color: 'gray',   chance: 1.0, value: 370 }, // Tea Time
        { type: 'animation', price: 500,  color: 'gray',   chance: 1.0, value: 383 }, // Company Jig

        { type: 'vehicle', price: 700,   color: 'gray',    chance: 1.3,  value: 'chino' },
        { type: 'vehicle', price: 700,   color: 'gray',    chance: 1.3,  value: 'club' },
        { type: 'vehicle', price: 700,   color: 'gray',    chance: 1.3,  value: 'rebel' },
        { type: 'vehicle', price: 700,   color: 'gray',    chance: 1.3,  value: 'ingot' },
        { type: 'vehicle', price: 700,   color: 'gray',    chance: 1.3,  value: 'peyote' },
        // gray end

        // blue start
        { type: 'vehicleDiscount', color: 'blue',   chance: 1, price: 1200,   value: 10 },
        { type: 'clothesDiscount', color: 'blue',   chance: 1, price: 1200,   value: 10 },

        { type: 'wheels', price: 1200, color: 'blue', chance: 36,  value: 10001 }, // Хромовое колесо
        { type: 'wheels', price: 1200, color: 'blue', chance: 35,  value: 10004 }, // Хромовое колесо
        { type: 'wheels', price: 1200, color: 'blue', chance: 31,  value: 11001 }, // Хромовое колесо
        { type: 'wheels', price: 1200, color: 'blue', chance: 30,  value: 11004 }, // Хромовое колесо
        { type: 'wheels', price: 1200, color: 'blue', chance: 26,  value: 12001 }, // Хромовое колесо
        { type: 'wheels', price: 1200, color: 'blue', chance: 25,  value: 12004 }, // Хромовое колесо
        { type: 'wheels', price: 1200, color: 'blue', chance: 21,  value: 13001 }, // Хромовое колесо
        { type: 'wheels', price: 1200, color: 'blue', chance: 20,  value: 13004 }, // Хромовое колесо

        { type: 'item', value: { itemId: 726, count: 1  }, chance: 1, price: 1200,  color: 'blue' }, // Экспериментальная пилюля "Имморталитикс"
        { type: 'item', value: { itemId: 729, count: 0  }, chance: 1, price: 1200,  color: 'blue' }, // Биодобавка 2 уровня
        { type: 'item', value: { itemId: 956, count: 3  }, chance: 1, price: 1200,  color: 'blue' }, // Набор самореанимации
        { type: 'item', value: { itemId: 958, count: 0  }, chance: 1, price: 1200,  color: 'blue' }, // Улучшенная рация

        { type: 'clothes', price: 1200,   color: 'blue',   chance: 1.0, value: [ { gender: 0, component: 5,  drawable: 2103, textures: 8,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2109, textures: 8,  isProp: 0 } ] },
        { type: 'clothes', price: 1200,   color: 'blue',   chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2250, textures: 6,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2102, textures: 10, isProp: 0 } ] },
        { type: 'clothes', price: 1200,   color: 'blue',   chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2249, textures: 7,  isProp: 0 }, { gender: 1, component: 11, drawable: 2484, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 1200,   color: 'blue',   chance: 1.0, value: [ { gender: 0, component: 5,  drawable: 2095, textures: 7,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2099, textures: 7,  isProp: 0 } ] },

        { type: 'tattoo', color: 'blue', chance: 1.00, price: 1200, value: [ 3395, 6461 ] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 1200, value: [ 3400, 6467 ] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 1200, value: [ 3399, 6466 ] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 1200, value: [ 3408, 6475 ] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 1200, value: [ 3398, 6465 ] },
        { type: 'tattoo', color: 'blue', chance: 1.00, price: 1200, value: [ 3414, 6481 ] },

        { type: 'animation', price: 1200,  color: 'blue', chance: 1.0, value: 288 }, // Lit Dance
        { type: 'animation', price: 1200,  color: 'blue', chance: 1.0, value: 381 }, // What You Want
        { type: 'animation', price: 1200,  color: 'blue', chance: 1.0, value: 385 }, // Point And Strut
        { type: 'animation', price: 1200,  color: 'blue', chance: 1.0, value: 377 }, // Boing
        { type: 'animation', price: 1200,  color: 'blue', chance: 1.0, value: 386 }, // Breakneck
        { type: 'animation', price: 1200,  color: 'blue', chance: 1.0, value: 387 }, // No Tears
        { type: 'animation', price: 1200,  color: 'blue', chance: 1.0, value: 384 }, // Heartbreak Shuffle

        { type: 'vehicle', price: 1500,   color: 'blue',    chance: 1.3,  value: 'astron' },
        // blue end

        // purple start
        { type: 'vehicleDiscount', color: 'purple', chance: 1, price: 2000,   value: 15 },
        { type: 'clothesDiscount', color: 'purple', chance: 1, price: 2000,   value: 15 },

        { type: 'wheels', price: 2000, color: 'purple', chance: 26,  value: 10002 }, // Покрасочное колесо
        { type: 'wheels', price: 2000, color: 'purple', chance: 25, value: 10005 }, // Покрасочное колесо
        { type: 'wheels', price: 2000, color: 'purple', chance: 21,  value: 11002 }, // Покрасочное колесо
        { type: 'wheels', price: 2000, color: 'purple', chance: 20,  value: 11005 }, // Покрасочное колесо
        { type: 'wheels', price: 2000, color: 'purple', chance: 16,  value: 12002 }, // Покрасочное колесо
        { type: 'wheels', price: 2000, color: 'purple', chance: 15,  value: 12005 }, // Покрасочное колесо
        { type: 'wheels', price: 2000, color: 'purple', chance: 11,  value: 13002 }, // Покрасочное колесо
        { type: 'wheels', price: 2000, color: 'purple', chance: 10,  value: 13005 }, // Покрасочное колесо

        { type: 'vehicleDetail', price: 2000,  color: 'purple', chance: 25,  value: 1008 }, // Винил
        { type: 'vehicleDetail', price: 2000, color: 'purple', chance: 20,  value: 1011 }, // Винил
        { type: 'vehicleDetail', price: 2000, color: 'purple', chance: 15,  value: 1012 }, // Винил

        { type: 'clothes', price: 2000,   color: 'purple', chance: 1.0, value: [ { gender: 0, component: 1,  drawable: 2164, textures: 13, isProp: 0 }, { gender: 1, component: 1,  drawable: 2151, textures: 13, isProp: 0 } ] },
        { type: 'clothes', price: 2000,   color: 'purple', chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2252, textures: 4,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2272, textures: 7,  isProp: 0 } ] },
        { type: 'clothes', price: 2000,   color: 'purple', chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2251, textures: 3,  isProp: 0 }, { gender: 1, component: 11, drawable: 2481, textures: 8,  isProp: 0 } ] },
        { type: 'clothes', price: 2000,   color: 'purple', chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2480, textures: 3,  isProp: 0 }, { gender: 1, component: 11, drawable: 2479, textures: 10, isProp: 0 } ] },
        { type: 'clothes', price: 2000,   color: 'purple', chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2260, textures: 6,  isProp: 0 }, { gender: 1, component: 11, drawable: 2487, textures: 12, isProp: 0 } ] },
        { type: 'clothes', price: 2000,   color: 'purple', chance: 1.0, value: [ { gender: 0, component: 6,  drawable: 2128, textures: 6,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2151, textures: 6,  isProp: 0 } ] },
        { type: 'clothes', price: 2000,   color: 'purple', chance: 1.0, value: [ { gender: 0, component: 7,  drawable: 2152, textures: 8,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2130, textures: 8,  isProp: 0 } ] },
        { type: 'clothes', price: 2000,   color: 'purple', chance: 1.0, value: [ { gender: 0, component: 5,  drawable: 2096, textures: 4,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2100, textures: 4,  isProp: 0 } ] },
        { type: 'clothes', price: 2000,   color: 'purple', chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2489, textures: 7,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2271, textures: 8,  isProp: 0 } ] },

        { type: 'armourSkin', price: 2000,   color: 'purple', chance: 1.0, value: { entityName: 'light', skinId: 134 } },
        { type: 'armourSkin', price: 2000,   color: 'purple', chance: 1.0, value: { entityName: 'light', skinId: 141 } },
        { type: 'armourSkin', price: 2000,   color: 'purple', chance: 1.0, value: { entityName: 'light', skinId: 137 } },

        { type: 'weaponSkin', price: 2000,  color: 'purple', chance: 1.0, value: { skinId: 9,  entityName: 'weapon_tecpistol'        } },
        { type: 'weaponSkin', price: 2000,  color: 'purple', chance: 1.0, value: { skinId: 14, entityName: 'weapon_gusenberg'        } },
        { type: 'weaponSkin', price: 2000,  color: 'purple', chance: 1.0, value: { skinId: 10, entityName: 'weapon_tecpistol'        } },
        { type: 'weaponSkin', price: 2000,  color: 'purple', chance: 1.0, value: { skinId: 15, entityName: 'weapon_gusenberg'        } },
        { type: 'weaponSkin', price: 2000,  color: 'purple', chance: 1.0, value: { skinId: 11, entityName: 'weapon_tecpistol'        } },
        { type: 'weaponSkin', price: 2000,  color: 'purple', chance: 1.0, value: { skinId: 16, entityName: 'weapon_gusenberg'        } },

        { type: 'tattoo', color: 'purple', chance: 1.00, price: 2000, value: [ 3401, 6468 ] },
        { type: 'tattoo', color: 'purple', chance: 1.00, price: 2000, value: [ 3402, 6469 ] },
        { type: 'tattoo', color: 'purple', chance: 1.00, price: 2000, value: [ 3391, 6458 ] },
        { type: 'tattoo', color: 'purple', chance: 1.00, price: 2000, value: [ 3412, 6479 ] },
        { type: 'tattoo', color: 'purple', chance: 1.00, price: 2000, value: [ 3406, 6473 ] },
        { type: 'tattoo', color: 'purple', chance: 1.00, price: 2000, value: [ 3403, 6470 ] },
        { type: 'tattoo', color: 'purple', chance: 1.00, price: 2000, value: [ 3413, 6480 ] },

        { type: 'vehicleSet', price: 2200, color: 'purple', chance: 35,  value: { setId: 9,  model: 'f150'     } }, // Обвес Ford F-150 Raptor (Кастомный)
        { type: 'vehicleSet', price: 2200, color: 'purple', chance: 30,  value: { setId: 1,  model: '350z'     } }, // Обвес Nissan 350Z (Кастомный 1)
        { type: 'vehicleSet', price: 2200, color: 'purple', chance: 25,  value: { setId: 2,  model: '350z'     } }, // Обвес Nissan 350Z (Кастомный 2)
        { type: 'vehicleSet', price: 2400, color: 'purple', chance: 20,  value: { setId: 3,  model: '350z'     } }, // Обвес Nissan 350Z (Кастомный 3)
        { type: 'vehicleSet', price: 2500, color: 'purple', chance: 17,  value: { setId: 2,  model: 'ct5'      } }, // Обвес Cadillac CT-5 Black Wing
        { type: 'vehicleSet', price: 2600, color: 'purple', chance: 15,  value: { setId: 1,  model: 'model3'   } }, // Обвес Tesla Model 3 Restyling
        // purple end

        // red start
        { type: 'vehicleDiscount', color: 'red',    chance: 1, price: 3500,   value: 20 },
        { type: 'clothesDiscount', color: 'red',    chance: 1, price: 3500,   value: 20 },

        { type: 'item', value: { itemId: 725, count: 0  }, chance: 1, price: 3500, color: 'red'    }, // Ремонтный комплейкт для оружия
        { type: 'item', value: { itemId: 992, count: 0  }, chance: 1, price: 3500, color: 'red'    }, // Автосигнализация
        { type: 'item', value: { itemId: 989, count: 0  }, chance: 1, price: 3500, color: 'red'    }, // Стальной кастет
        { type: 'item', value: { itemId: 988, count: 0  }, chance: 1, price: 3500, color: 'red'    }, // Карманный ингалятор
        { type: 'item', value: { itemId: 957, count: 0  }, chance: 1, price: 3500, color: 'red'    }, // Глушилка связи
        { type: 'item', value: { itemId: 990, count: 0  }, chance: 1, price: 3500, color: 'red'    }, // Воздушный горн
        { type: 'item', value: { itemId: 812, count: 0  }, chance: 1, price: 3500, color: 'red'    }, // Лыжи
        { type: 'item', value: { itemId: 813, count: 0  }, chance: 1, price: 3500, color: 'red'    }, // Коньки

        { type: 'vehicleDetail', price: 3500, color: 'red',   chance: 20,  value: 1013 }, // Винил
        { type: 'vehicleDetail', price: 3600, color: 'red',   chance: 15,  value: 1014 }, // Винил
        { type: 'vehicleDetail', price: 3700, color: 'red',   chance: 10,  value: 1016 }, // Винил
        { type: 'vehicleDetail', price: 3800, color: 'red',   chance: 10,  value: 5003 }, // Номерной знак
        { type: 'vehicleDetail', price: 4000, color: 'red',   chance: 5.0, value: 5004 }, // Номерной знак

        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2253, textures: 5,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2270, textures: 8,  isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2486, textures: 5,  isProp: 0 }, { gender: 1, component: 11, drawable: 2485, textures: 8,  isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2263, textures: 3,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2154, textures: 4,  isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 5,  drawable: 2097, textures: 10, isProp: 0 }, { gender: 1, component: 5,  drawable: 2101, textures: 10, isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2255, textures: 7,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2157, textures: 11, isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2487, textures: 8,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2104, textures: 8,  isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 5,  drawable: 2100, textures: 7,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2106, textures: 7,  isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2481, textures: 8,  isProp: 0 }, { gender: 1, component: 11, drawable: 2489, textures: 10, isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2259, textures: 8,  isProp: 0 }, { gender: 1, component: 11, drawable: 2480, textures: 8,  isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2492, textures: 8,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2152, textures: 4,  isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2258, textures: 8,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2273, textures: 11, isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2495, textures: 7,  isProp: 0 }, { gender: 1, component: 11, drawable: 2488, textures: 11, isProp: 0 } ] },
        { type: 'clothes', price: 3500,  color: 'red',    chance: 1.0, value: [ { gender: 0, component: 6,  drawable: 2129, textures: 4,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2155, textures: 4,  isProp: 0 } ] },

        { type: 'armourSkin', price: 3500,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 139 } },
        { type: 'armourSkin', price: 3500,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 133 } },
        { type: 'armourSkin', price: 3500,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 132 } },

        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 21, entityName: 'weapon_assaultrifle_mk2' } },
        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 22, entityName: 'weapon_carbinerifle_mk2' } },
        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 24, entityName: 'weapon_carbinerifle_mk2' } },
        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 22, entityName: 'weapon_assaultrifle_mk2' } },
        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 23, entityName: 'weapon_carbinerifle_mk2' } },
        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 23, entityName: 'weapon_assaultrifle_mk2' } },
        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 25, entityName: 'weapon_heavyshotgun'     } },
        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 26, entityName: 'weapon_heavyshotgun'     } },
        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 9,  entityName: 'weapon_tacticalrifle'    } },
        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 27, entityName: 'weapon_heavyshotgun'     } },
        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 11, entityName: 'weapon_tacticalrifle'    } },
        { type: 'weaponSkin', price: 3500,  color: 'red',    chance: 1.0, value: { skinId: 10, entityName: 'weapon_tacticalrifle'    } },

        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3394, 6464 ] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3411, 6478 ] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3393, 6460 ] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3410, 6477 ] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3405, 6472 ] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3416, 6483 ] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3415, 6482 ] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3409, 6476 ] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3417, 6484 ] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3418, 6485 ] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3407, 6474 ] },
        { type: 'tattoo', color: 'red', chance: 1.00, price: 3500, value: [ 3404, 6471 ] },

        { type: 'animation', price: 3500, color: 'red',    chance: 1.0, value: 375 }, // Deer Cadabra
        { type: 'animation', price: 3500, color: 'red',    chance: 1.0, value: 382 }, // Entranced
        { type: 'animation', price: 3500, color: 'red',    chance: 1.0, value: 379 }, // Caffeinated
        { type: 'animation', price: 3500, color: 'red',    chance: 1.0, value: 378 }, // Bye Bye Bye
        { type: 'animation', price: 3500, color: 'red',    chance: 1.0, value: 367 }, // Pick Me Up
        { type: 'animation', price: 3500, color: 'red',    chance: 1.0, value: 373 }, // AirBoard

        { type: 'vehicleSet', price: 3700, color: 'red',    chance: 17,  value: { setId: 19, model: 's15'      } }, // Обвес Nissan Silvia S15 GarageMak
        { type: 'vehicleSet', price: 3800, color: 'red',    chance: 15,  value: { setId: 1,  model: 'sls'      } }, // Обвес Mercedes-AMG SLS Black Series
        { type: 'vehicleSet', price: 3900, color: 'red',    chance: 13,  value: { setId: 2,  model: 'glc'      } }, // Обвес Mercedes-Benz GLC C254 AMG
        { type: 'vehicleSet', price: 4000, color: 'red',    chance: 11,  value: { setId: 12, model: 'i8'       } }, // Обвес BMW i8 Energy Motorsport
        { type: 'vehicleSet', price: 4100, color: 'red',    chance: 9.0, value: { setId: 5,  model: 'e63s'     } }, // Обвес Mercedes-Benz E-Class W212 Restyling
        { type: 'vehicleSet', price: 4200, color: 'red',    chance: 7.0, value: { setId: 1,  model: 'etron2'   } }, // обвес Audi Q8 E-tron S
        { type: 'vehicleSet', price: 4300, color: 'red',    chance: 5.0, value: { setId: 3,  model: 'nsx'      } }, // Обвес Honda NSX (Кастомный)

        { type: 'vehicle', price: 7500,   color: 'red',    chance: 1.3,  value: '350z',       customPrice: 500000  }, // Машина Nissan 350Z
        { type: 'vehicle', price: 8000,   color: 'red',    chance: 1.0,  value: 'model3',     customPrice: 750000  }, // Машина Tesla Model 3
        // red end

        // gold start
        { type: 'vehicleDiscount', color: 'gold',   chance: 1, price: 6000,  value: 25 },
        { type: 'clothesDiscount', color: 'gold',   chance: 1, price: 6000,  value: 25 },

        { type: 'item', value: { itemId: 452, count: 0  }, chance: 1, price: 6000, color: 'gold'   }, // Гражданский дрон
        { type: 'item', value: { itemId: 627, count: 0  }, chance: 1, price: 6000, color: 'gold'   }, // Дефибриллятор
        { type: 'item', value: { itemId: 961, count: 0  }, chance: 1, price: 6000, color: 'gold'   }, // GPS трекер
        { type: 'item', value: { itemId: 991, count: 0  }, chance: 1, price: 6000, color: 'gold'   }, // Набор дверных щокеров
        { type: 'item', value: { itemId: 724, count: 0  }, chance: 1, price: 6000, color: 'gold'   }, // Улучшенный металоискатель
        { type: 'item', value: { itemId: 962, count: 0  }, chance: 1, price: 6000, color: 'gold'   }, // Система крепления
        { type: 'item', value: { itemId: 986, count: 0  }, chance: 1, price: 6000, color: 'gold'   }, // Дрон-сканер

        { type: 'wheels', price: 6000, color: 'gold', chance: 15,  value: 10003 }, // Неоновое колесо
        { type: 'wheels', price: 6000, color: 'gold', chance: 14,  value: 10006 }, // Неоновое колесо
        { type: 'wheels', price: 6000, color: 'gold', chance: 12,  value: 11003 }, // Неоновое колесо
        { type: 'wheels', price: 6000, color: 'gold', chance: 11,  value: 11006 }, // Неоновое колесо
        { type: 'wheels', price: 6000, color: 'gold', chance: 9.0, value: 12003 }, // Неоновое колесо
        { type: 'wheels', price: 6000, color: 'gold', chance: 8.0, value: 12006 }, // Неоновое колесо
        { type: 'wheels', price: 6000, color: 'gold', chance: 6.0, value: 13003 }, // Неоновое колесо
        { type: 'wheels', price: 6000, color: 'gold', chance: 5.0, value: 13006 }, // Неоновое колесо

        { type: 'clothes', price: 6000,  color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 7,  drawable: 2029, texture: 2,   isProp: 0 }, { gender: 1, component: 7,  drawable: 2012, texture: 2,   isProp: 0 } ] },
        { type: 'clothes', price: 6000,  color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2483, textures: 5,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2274, textures: 6,  isProp: 0 } ] },
        { type: 'clothes', price: 6000,  color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 5,  drawable: 2105, textures: 8,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2111, textures: 8,  isProp: 0 } ] },
        { type: 'clothes', price: 6000,  color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2485, textures: 9,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2267, textures: 7,  isProp: 0 } ] },
        { type: 'clothes', price: 6000,  color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 3,  drawable: 2067, textures: 8,  isProp: 0 }, { gender: 1, component: 3,  drawable: 2400, textures: 8,  isProp: 0 } ] },
        { type: 'clothes', price: 6000,  color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 7,  drawable: 2151, textures: 16, isProp: 0 }, { gender: 1, component: 7,  drawable: 2129, textures: 16, isProp: 0 } ] },
        { type: 'clothes', price: 6000,  color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 5,  drawable: 2104, textures: 6,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2110, textures: 6,  isProp: 0 } ] },

        { type: 'armourSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { entityName: 'light', skinId: 135 } },
        { type: 'armourSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { entityName: 'light', skinId: 136 } },
        { type: 'armourSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { entityName: 'light', skinId: 138 } },
        { type: 'armourSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { entityName: 'light', skinId: 140 } },

        { type: 'weaponSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { skinId: 10, entityName: 'weapon_combatmg_mk2'     } },
        { type: 'weaponSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { skinId: 9,  entityName: 'weapon_heavyrifle'       } },
        { type: 'weaponSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { skinId: 9,  entityName: 'weapon_combatmg_mk2'     } },
        { type: 'weaponSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { skinId: 20, entityName: 'weapon_heavysniper_mk2'  } },
        { type: 'weaponSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { skinId: 11, entityName: 'weapon_combatmg_mk2'     } },
        { type: 'weaponSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { skinId: 10, entityName: 'weapon_heavyrifle'       } },
        { type: 'weaponSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { skinId: 21, entityName: 'weapon_heavysniper_mk2'  } },
        { type: 'weaponSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { skinId: 11, entityName: 'weapon_heavyrifle'       } },
        { type: 'weaponSkin', price: 6000,  color: 'gold',   chance: 1.0, value: { skinId: 22, entityName: 'weapon_heavysniper_mk2'  } },

        { type: 'animation', price: 6000, color: 'gold',   chance: 1.0, value: 338 }, // Rebellious
        { type: 'animation', price: 6000, color: 'gold',   chance: 1.0, value: 371 }, // Chugga Chugga
        { type: 'animation', price: 6000, color: 'gold',   chance: 1.0, value: 372 }, // Money Blastin

        { type: 'vehicleSet', price: 10000, color: 'gold',   chance: 6.0, value: { setId: 12, model: 'chiron19' } }, // Обвес Bugatti Chiron (Кастомный)
        { type: 'vehicleSet', price: 11000, color: 'gold',   chance: 4.0, value: { setId: 11, model: 'veyron'   } }, // обвес Bugatti Veyron Hyper Sport
        { type: 'vehicleSet', price: 12000, color: 'gold',   chance: 2.0, value: { setId: 25, model: 'huracan'  } }, // Обвес Lamborghini Huracan GT3
        { type: 'vehicleSet', price: 12000, color: 'gold',   chance: 2.0, value: { setId: 1,  model: 'cle'      } }, // Обвес Mercedes-Benz CLE C253 AMG

        { type: 'vehicle', price: 20000,  color: 'gold',   chance: 2.3,  value: 'ex90',       customPrice: 1500000 }, // Машина Volvo EX90
        { type: 'vehicle', price: 18000,  color: 'gold',   chance: 2.2,  value: 'glc',        customPrice: 1250000 }, // Машина Mercedes-Benz GLC C254
        { type: 'vehicle', price: 22000,  color: 'gold',   chance: 2.0,  value: 'cle',        customPrice: 1750000 }, // Машина Mercedes-Benz CLE C236
        { type: 'vehicle', price: 8000,   color: 'gold',   chance: 1.8,  value: 'snowmobile', customPrice: 500000  }, // Снегоход BRP Ski-Doo Sammit Rev-XM
        { type: 'vehicle', price: 24000,  color: 'gold',   chance: 1.4,  value: 'etron2',     customPrice: 2000000 }, // Машина Audi Q8 E-tron
        { type: 'vehicle', price: 26000,  color: 'gold',   chance: 1.2,  value: 'roma',       customPrice: 2875000 }, // Машина Ferrari Roma
        { type: 'vehicle', price: 26000,  color: 'gold',   chance: 1.0,  value: 'spectre',    customPrice: 3250000 }, // Машина Rolls-Royce Spectre
        { type: 'vehicle', price: 28000,  color: 'gold',   chance: 0.8,  value: 'temerario',  customPrice: 4250000 }, // Машина Lamborghini Temerario
        { type: 'vehicle', price: 30000,  color: 'gold',   chance: 0.2,  value: 'tourbillon', customPrice: 5000000 }, // Машина Bugatti Tourbillon
        { type: 'vehicle', price: 40000,  color: 'gold',   chance: 0.2,  value: 'ah6',        customPrice: 5000000 }, // Вертолёт MD Helicopters AH-6 Little Bird
        // gold end
    ]
}
