module.exports = {
    id: 11,
    title: 'donate.config.cases.winter2024.title',

    start: ['2023-11-25', 'YYYY-MM-DD'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    // servers: ['TEST0'],
    seasonPassId: 4,

    // disabledBuy: true,
    readOnly: false,

    // Сумма распыления указан в опыте батлпаса. Если батлпас активен
    sellRule: 40, // Если батлпас уже не активен, то задать процент конвертации price в коины. Например 100 exp / 100 * 25 = 25 коинов

    price: 1000,
    transferDays: 30,

    // discount: 5,

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    colors: [
        {
            color: 'gray',
            chance: 50,
            types: [
                { type: 'tattoo',          chance: 40 },
                { type: 'weaponSkin',      chance: 30 },
                { type: 'armourSkin',      chance: 30 },
                { type: 'clothes',         chance: 25 },
                { type: 'vehicleDiscount', chance: 30 },
                { type: 'clothesDiscount', chance: 35 },
                { type: 'wheels',          chance: 35 },
            ],
        },
        {
            color: 'blue',
            chance: 35,
            types: [
                { type: 'vehicle',         chance: 45 },
                { type: 'wheels',          chance: 40 },
                { type: 'tattoo',          chance: 40 },
                { type: 'weaponSkin',      chance: 30 },
                { type: 'armourSkin',      chance: 30 },
                { type: 'animation',       chance: 45 },
                { type: 'clothes',         chance: 45 },
                { type: 'vehicleDiscount', chance: 30 },
                { type: 'clothesDiscount', chance: 35 },
                { type: 'vehicleSet',      chance: 25 },
            ],
        },
        {
            color: 'purple',
            chance: 7.5,
            types: [
                { type: 'item',            chance: 40 },
                { type: 'tattoo',          chance: 45 },
                { type: 'weaponSkin',      chance: 35 },
                { type: 'armourSkin',      chance: 35 },
                { type: 'animation',       chance: 45 },
                { type: 'vehicleSet',      chance: 25 },
                { type: 'vehicleDiscount', chance: 30 },
                { type: 'clothesDiscount', chance: 35 },
                { type: 'wheels',          chance: 40 },
                { type: 'clothes',         chance: 35 },
            ],
        },
        {
            color: 'red',
            chance: 5,
            types: [
                { type: 'item',            chance: 20 },
                { type: 'vehicleDiscount', chance: 25 },
                { type: 'clothesDiscount', chance: 30 },
                { type: 'tattoo',          chance: 45 },
                { type: 'weaponSkin',      chance: 30 },
                { type: 'armourSkin',      chance: 30 },
                { type: 'animation',       chance: 45 },
                { type: 'vehicleSet',      chance: 30 },
                { type: 'clothes',         chance: 20 },
            ],
        },
        {
            color: 'gold',
            chance: 2.5,
            types: [
                { type: 'vehicleDiscount', chance: 25 },
                { type: 'clothesDiscount', chance: 30 },
                { type: 'item',            chance: 30 },
                { type: 'tattoo',          chance: 45 },
                { type: 'animation',       chance: 40 },
                { type: 'weaponSkin',      chance: 35 },
                { type: 'armourSkin',      chance: 35 },
                { type: 'clothes',         chance: 25 },
                { type: 'vehicle',         chance: 15 },
                { type: 'vehicleMod',      chance: 10 },
            ],
        }
    ],

    caseContent: [
        { type: 'vehicleDiscount', color: 'gray',   chance: 1,   price: 250,   value: 5 },
        { type: 'vehicleDiscount', color: 'blue',   chance: 1,   price: 300,   value: 10 },
        { type: 'vehicleDiscount', color: 'purple', chance: 1,   price: 350,   value: 15 },
        { type: 'vehicleDiscount', color: 'red',    chance: 1,   price: 600,   value: 20 },
        { type: 'vehicleDiscount', color: 'gold',   chance: 1,   price: 1500,  value: 25 },

        { type: 'clothesDiscount', color: 'gray',   chance: 1.0, price: 125,   value: 5  },
        { type: 'clothesDiscount', color: 'blue',   chance: 1.0, price: 250,   value: 10 },
        { type: 'clothesDiscount', color: 'purple', chance: 1.0, price: 500,   value: 15 },
        { type: 'clothesDiscount', color: 'red',    chance: 1.0, price: 750,   value: 20 },
        { type: 'clothesDiscount', color: 'gold',   chance: 1.0, price: 1000,  value: 25 },

        // { type: 'statTrack', value: { amount: 1 }, price: 750, color: 'purple' }, // Счётчик Stat-Track для оружий

        { type: 'animation', price: 750,  color: 'purple', chance: 1.0, value: 289 }, // Анимация: Take The Elf
        { type: 'animation', price: 1500, color: 'red',    chance: 1.0, value: 175 }, // Анимация: Flapper
        { type: 'animation', price: 500,  color: 'blue',   chance: 1.0, value: 247 }, // Анимация: Ring It On
        { type: 'animation', price: 500,  color: 'blue',   chance: 1.0, value: 294 }, // Анимация: Sparkler
        { type: 'animation', price: 500,  color: 'blue',   chance: 1.0, value: 292 }, // Анимация: Shaolin Sip
        { type: 'animation', price: 500,  color: 'blue',   chance: 1.0, value: 296 }, // Анимация: Tangerine Juggling
        { type: 'animation', price: 750,  color: 'purple', chance: 1.0, value: 181 }, // Анимация: Crackdown
        { type: 'animation', price: 750,  color: 'purple', chance: 1.0, value: 253 }, // Анимация: Drum Major
        { type: 'animation', price: 1500, color: 'red',    chance: 1.0, value: 290 }, // Анимация: Snow Day
        { type: 'animation', price: 1500, color: 'red',    chance: 1.0, value: 291 }, // Анимация: Choice Knit
        { type: 'animation', price: 3000, color: 'gold',   chance: 1.0, value: 295 }, // Анимация: Telekinetic Cookies
        { type: 'animation', price: 3000, color: 'gold',   chance: 1.0, value: 293 }, // Анимация: Lil Treat

        { type: 'item', price: 300,  color: 'purple', chance: 1.0, value: { itemId: 815, count: 0 } }, // Волшебная чёрная конфета
        { type: 'item', price: 300,  color: 'purple', chance: 1.0, value: { itemId: 816, count: 0 } }, // Волшебная белая конфета
        { type: 'item', price: 750,  color: 'purple', chance: 1.0, value: { itemId: 333, count: 0 } }, // Ручной пулемет Мk2
        { type: 'item', price: 3000, color: 'gold',   chance: 1.0, value: { itemId: 452, count: 0 } }, // Дрон
        { type: 'item', price: 3000, color: 'gold',   chance: 1.0, value: { itemId: 328, count: 0 } }, // Тяжелая снайперская винтовка Mk2
        { type: 'item', price: 5000, color: 'gold',   chance: 1.0, value: { itemId: 724, count: 0 } }, // Улучшенный металлоискатель
        { type: 'item', price: 1500, color: 'red',    chance: 1.0, value: { itemId: 811, count: 0 } }, // Свисток
        { type: 'item', price: 3000, color: 'gold',   chance: 1.0, value: { itemId: 812, count: 0 } }, // Лыжи
        { type: 'item', price: 3000, color: 'gold',   chance: 1.0, value: { itemId: 813, count: 0 } }, // Коньки
        { type: 'item', price: 3000, color: 'gold',   chance: 1.0, value: { itemId: 814, count: 0 } }, // Сани (предположительно)

        { type: 'tattoo', price: 250, color: 'blue',   chance: 1.0, value: [ 3314, 6380 ] },
        { type: 'tattoo', price: 250, color: 'blue',   chance: 1.0, value: [ 3315, 6381 ] },
        { type: 'tattoo', price: 250, color: 'blue',   chance: 1.0, value: [ 3316, 6382 ] },
        { type: 'tattoo', price: 250, color: 'gray',   chance: 1.0, value: [ 3285, 6351 ] },
        { type: 'tattoo', price: 250, color: 'blue',   chance: 1.0, value: [ 3317, 6383 ] },
        { type: 'tattoo', price: 250, color: 'blue',   chance: 1.0, value: [ 3318, 6384 ] },
        { type: 'tattoo', price: 250, color: 'blue',   chance: 1.0, value: [ 3319, 6385 ] },
        { type: 'tattoo', price: 250, color: 'blue',   chance: 1.0, value: [ 3320, 6386 ] },
        { type: 'tattoo', price: 250, color: 'blue',   chance: 1.0, value: [ 3321, 6387 ] },
        { type: 'tattoo', price: 250, color: 'blue',   chance: 1.0, value: [ 3325, 6391 ] },
        { type: 'tattoo', price: 250, color: 'blue',   chance: 1.0, value: [ 3326, 6392 ] },
        { type: 'tattoo', price: 500, color: 'purple', chance: 1.0, value: [ 3331, 6397 ] },
        { type: 'tattoo', price: 500, color: 'purple', chance: 1.0, value: [ 3332, 6398 ] },
        { type: 'tattoo', price: 500, color: 'purple', chance: 1.0, value: [ 3333, 6399 ] },
        { type: 'tattoo', price: 500, color: 'purple', chance: 1.0, value: [ 3334, 6400 ] },
        { type: 'tattoo', price: 500, color: 'purple', chance: 1.0, value: [ 3335, 6401 ] },
        { type: 'tattoo', price: 500, color: 'purple', chance: 1.0, value: [ 3336, 6402 ] },
        { type: 'tattoo', price: 500, color: 'purple', chance: 1.0, value: [ 3337, 6403 ] },
        { type: 'tattoo', price: 500, color: 'purple', chance: 1.0, value: [ 3338, 6404 ] },
        { type: 'tattoo', price: 550, color: 'red',    chance: 1.0, value: [ 3322, 6388 ] },
        { type: 'tattoo', price: 550, color: 'red',    chance: 1.0, value: [ 3323, 6389 ] },
        { type: 'tattoo', price: 550, color: 'red',    chance: 1.0, value: [ 3324, 6390 ] },
        { type: 'tattoo', price: 550, color: 'red',    chance: 1.0, value: [ 3327, 6393 ] },
        { type: 'tattoo', price: 550, color: 'red',    chance: 1.0, value: [ 3328, 6394 ] },
        { type: 'tattoo', price: 550, color: 'red',    chance: 1.0, value: [ 3329, 6395 ] },
        { type: 'tattoo', price: 550, color: 'red',    chance: 1.0, value: [ 3330, 6396 ] },
        { type: 'tattoo', price: 550, color: 'red',    chance: 1.0, value: [ 3339, 6405 ] },
        { type: 'tattoo', price: 550, color: 'red',    chance: 1.0, value: [ 3340, 6406 ] },
        { type: 'tattoo', price: 550, color: 'red',    chance: 1.0, value: [ 3341, 6407 ] },
        { type: 'tattoo', price: 550, color: 'gold',   chance: 1.0, value: [ 3342, 6408 ] },
        { type: 'tattoo', price: 550, color: 'gold',   chance: 1.0, value: [ 3343, 6409 ] },

        // { type: 'mediaSound', price: 250,  color: 'blue',   chance: 1.0, value: { soundPath: 'feelSorryKid'          } }, // Озвучка 'Ilichili - Ой, жалко-то как пацана'
        // { type: 'mediaSound', price: 400,  color: 'blue',   chance: 1.0, value: { soundPath: 'soonBan'               } }, // Озвучка 'Mayday - Вы скоро будете забанены!'
        // { type: 'mediaSound', price: 250,  color: 'blue',   chance: 1.0, value: { soundPath: 'seeInCourt'            } }, // Озвучка 'Condagar - Увидимся в суде!'
        // { type: 'mediaSound', price: 250,  color: 'blue',   chance: 1.0, value: { soundPath: 'realSorryNoLoad'       } }, // Озвучка 'Olegha77 - Реально прости, непрогруз'
        // { type: 'mediaSound', price: 750,  color: 'purple', chance: 1.0, value: { soundPath: 'whatWrong'             } }, // Озвучка 'SinSet - Ой, а что случилось?'
        // { type: 'mediaSound', price: 750,  color: 'purple', chance: 1.0, value: { soundPath: 'kissedYou'             } }, // Озвучка 'Wavee - Так бы и зацеловала тебя!'
        // { type: 'mediaSound', price: 400,  color: 'blue',   chance: 1.0, value: { soundPath: 'whatsSayEnjoyGame'     } }, // Озвучка 'Olegha77 - Что я могу тебе сказать, приятной игры!'
        // { type: 'mediaSound', price: 250,  color: 'blue',   chance: 1.0, value: { soundPath: 'notViolateServerRules' } }, // Озвучка 'FILANT - Молодые люди, правила сервера не нарушать!'
        // { type: 'mediaSound', price: 750,  color: 'purple', chance: 1.0, value: { soundPath: 'eat_apples'            } }, // Озвучка 'Wavee - А я пока яблочки покушаю'
        // { type: 'mediaSound', price: 1500, color: 'red',    chance: 1.0, value: { soundPath: 'Greathaha'             } }, // Озвучка 'Buster - Классно, ха-ха!'
        // { type: 'mediaSound', price: 1500, color: 'red',    chance: 1.0, value: { soundPath: 'guilty'                } }, // Озвучка 'FrameTamer - Ну виноват, ну чё ты'
        // { type: 'mediaSound', price: 750,  color: 'purple', chance: 1.0, value: { soundPath: 'collectTears'          } }, // Озвучка 'Mayday - Слёзки собирайте уважаемый'
        // { type: 'mediaSound', price: 750,  color: 'purple', chance: 1.0, value: { soundPath: 'nononobrother'         } }, // Озвучка 'Asiv0y - Оооо всё, не не не брат, не'
        // { type: 'mediaSound', price: 1500, color: 'red',    chance: 1.0, value: { soundPath: 'phahahaha'             } }, // Озвучка 'Macksos - Пха-ха-ха-ха-ха-ха'
        // { type: 'mediaSound', price: 1500, color: 'red',    chance: 1.0, value: { soundPath: 'goodwillbad'           } }, // Озвучка 'Danila_Gorilla - Не хочешь по хорошему, будем по плохому!'
        // { type: 'mediaSound', price: 750,  color: 'purple', chance: 1.0, value: { soundPath: 'whatbastard'           } }, // Озвучка 'Asiv0y - Оооу, что за заварушка?'
        // { type: 'mediaSound', price: 1500, color: 'red',    chance: 1.0, value: { soundPath: 'howMuch'               } }, // Озвучка 'SinSet - Скока скока?'
        // { type: 'mediaSound', price: 1500, color: 'red',    chance: 1.0, value: { soundPath: 'ewooomatiz'            } }, // Озвучка 'Koresh - Ewooo matiz'
        // { type: 'mediaSound', price: 3000, color: 'gold',   chance: 1.0, value: { soundPath: 'ohsogood'              } }, // Озвучка 'LITVIN - Ой-ой-ой, какая хорошая'
        // { type: 'mediaSound', price: 1500, color: 'red',    chance: 1.0, value: { soundPath: 'wayking'               } }, // Озвучка 'Alamantik - Дорогу королю!'
        // { type: 'mediaSound', price: 3000, color: 'gold',   chance: 1.0, value: { soundPath: 'really'                } }, // Озвучка 'Paradeev1ch - Ээ-ээ-ээ, неужели!'
        // { type: 'mediaSound', price: 3000, color: 'gold',   chance: 1.0, value: { soundPath: 'waitwaitwait'          } }, //Озвучка 'Koresh - Вейт вейт, вейт вейт!'

        { type: 'weaponSkin', price: 250,  color: 'gray',   chance: 1.0, value: { skinId: 7,  entityName: 'weapon_machinepistol'    } },
        { type: 'weaponSkin', price: 250,  color: 'gray',   chance: 1.0, value: { skinId: 6,  entityName: 'weapon_machinepistol'    } },
        { type: 'weaponSkin', price: 500,  color: 'blue',   chance: 1.0, value: { skinId: 12, entityName: 'weapon_carbinerifle'     } },
        { type: 'weaponSkin', price: 750,  color: 'purple', chance: 1.0, value: { skinId: 12, entityName: 'weapon_revolver'         } },
        { type: 'weaponSkin', price: 500,  color: 'blue',   chance: 1.0, value: { skinId: 13, entityName: 'weapon_carbinerifle'     } },
        { type: 'weaponSkin', price: 750,  color: 'purple', chance: 1.0, value: { skinId: 12, entityName: 'weapon_heavyshotgun'     } },
        { type: 'weaponSkin', price: 750,  color: 'purple', chance: 1.0, value: { skinId: 11, entityName: 'weapon_revolver'         } },
        { type: 'weaponSkin', price: 750,  color: 'purple', chance: 1.0, value: { skinId: 11, entityName: 'weapon_heavyshotgun'     } },
        { type: 'weaponSkin', price: 750,  color: 'purple', chance: 1.0, value: { skinId: 13, entityName: 'weapon_revolver'         } },
        { type: 'weaponSkin', price: 750,  color: 'purple', chance: 1.0, value: { skinId: 13, entityName: 'weapon_heavyshotgun'     } },
        { type: 'weaponSkin', price: 1000, color: 'red',    chance: 1.0, value: { skinId: 8,  entityName: 'weapon_assaultrifle_mk2' } },
        { type: 'weaponSkin', price: 1000, color: 'red',    chance: 1.0, value: { skinId: 7,  entityName: 'weapon_assaultrifle_mk2' } },
        { type: 'weaponSkin', price: 1000, color: 'red',    chance: 1.0, value: { skinId: 9,  entityName: 'weapon_assaultrifle_mk2' } },
        { type: 'weaponSkin', price: 1000, color: 'red',    chance: 1.0, value: { skinId: 3,  entityName: 'weapon_heavysniper'      } },
        { type: 'weaponSkin', price: 1000, color: 'red',    chance: 1.0, value: { skinId: 2,  entityName: 'weapon_heavysniper'      } },
        { type: 'weaponSkin', price: 1000, color: 'red',    chance: 1.0, value: { skinId: 4,  entityName: 'weapon_heavysniper'      } },
        { type: 'weaponSkin', price: 1250, color: 'gold',   chance: 1.0, value: { skinId: 9,  entityName: 'weapon_carbinerifle_mk2' } },
        { type: 'weaponSkin', price: 1250, color: 'gold',   chance: 1.0, value: { skinId: 7,  entityName: 'weapon_heavysniper_mk2'  } },
        { type: 'weaponSkin', price: 1250, color: 'gold',   chance: 1.0, value: { skinId: 8,  entityName: 'weapon_carbinerifle_mk2' } },
        { type: 'weaponSkin', price: 1250, color: 'gold',   chance: 1.0, value: { skinId: 6,  entityName: 'weapon_heavysniper_mk2'  } },
        { type: 'weaponSkin', price: 1250, color: 'gold',   chance: 1.0, value: { skinId: 10, entityName: 'weapon_carbinerifle_mk2' } },
        { type: 'weaponSkin', price: 1250, color: 'gold',   chance: 1.0, value: { skinId: 8,  entityName: 'weapon_heavysniper_mk2'  } },

        { type: 'armourSkin', price: 250,  color: 'gray',   chance: 1.0, value: { entityName: 'light', skinId: 68 } },
        { type: 'armourSkin', price: 250,  color: 'gray',   chance: 1.0, value: { entityName: 'light', skinId: 69 } },
        { type: 'armourSkin', price: 500,  color: 'blue',   chance: 1.0, value: { entityName: 'light', skinId: 92 } },
        { type: 'armourSkin', price: 500,  color: 'blue',   chance: 1.0, value: { entityName: 'light', skinId: 93 } },
        { type: 'armourSkin', price: 750,  color: 'purple', chance: 1.0, value: { entityName: 'light', skinId: 90 } },
        { type: 'armourSkin', price: 750,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 70 } },
        { type: 'armourSkin', price: 500,  color: 'purple', chance: 1.0, value: { entityName: 'light', skinId: 91 } },
        { type: 'armourSkin', price: 750,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 71 } },
        { type: 'armourSkin', price: 750,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 79 } },
        { type: 'armourSkin', price: 750,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 75 } },
        { type: 'armourSkin', price: 750,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 88 } },
        { type: 'armourSkin', price: 750,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 77 } },
        { type: 'armourSkin', price: 750,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 89 } },
        { type: 'armourSkin', price: 750,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 73 } },
        { type: 'armourSkin', price: 850,  color: 'gold',   chance: 1.0, value: { entityName: 'light', skinId: 72 } },
        { type: 'armourSkin', price: 750,  color: 'red',    chance: 1.0, value: { entityName: 'light', skinId: 74 } },
        { type: 'armourSkin', price: 850,  color: 'gold',   chance: 1.0, value: { entityName: 'light', skinId: 78 } },
        { type: 'armourSkin', price: 850,  color: 'gold',   chance: 1.0, value: { entityName: 'light', skinId: 76 } },

        { type: 'clothes', price: 250,  color: 'gray',   chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2200, textures: 9,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2209, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 250,  color: 'gray',   chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2398, textures: 9,  isProp: 0 }, { gender: 1, component: 11, drawable: 2391, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 250,  color: 'gray',   chance: 1.0, value: [ { gender: 0, component: 6,  drawable: 2088, textures: 9,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2097, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 500,  color: 'blue',   chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2399, textures: 9,  isProp: 0 }, { gender: 1, component: 11, drawable: 2394, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 500,  color: 'blue',   chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2401, textures: 9,  isProp: 0 }, { gender: 1, component: 11, drawable: 2396, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 500,  color: 'blue',   chance: 1.0, value: [ { gender: 0, component: 6,  drawable: 2085, textures: 9,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2098, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 750,  color: 'purple', chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2199, textures: 9,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2208, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 750,  color: 'purple', chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2393, textures: 9,  isProp: 0 }, { gender: 1, component: 11, drawable: 2393, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 750,  color: 'purple', chance: 1.0, value: [ { gender: 0, component: 6,  drawable: 2090, textures: 9,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2211, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 1500, color: 'red',    chance: 1.0, value: [ { gender: 0, component: 4,  drawable: 2201, textures: 9,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2210, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 1500, color: 'red',    chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2403, textures: 9,  isProp: 0 }, { gender: 1, component: 11, drawable: 2395, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 1500, color: 'red',    chance: 1.0, value: [ { gender: 0, component: 5,  drawable: 2062, textures: 9,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2058, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 1500, color: 'red',    chance: 1.0, value: [ { gender: 0, component: 6,  drawable: 2087, textures: 9,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2100, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 1500, color: 'red',    chance: 1.0, value: [ { gender: 0, component: 6,  drawable: 2086, textures: 9,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2099, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 1500, color: 'red',    chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2400, textures: 9,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2059, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 1500, color: 'red',    chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2394, textures: 9,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2057, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 6,  drawable: 2089, textures: 9,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2101, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 5,  drawable: 2058, textures: 9,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2053, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 5,  drawable: 2059, textures: 9,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2054, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 1,  drawable: 2144, textures: 9,  isProp: 0 }, { gender: 1, component: 1,  drawable: 2130, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 1,  drawable: 2143, textures: 9,  isProp: 0 }, { gender: 1, component: 1,  drawable: 2129, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 1,  drawable: 2022, textures: 9,  isProp: 1 }, { gender: 1, component: 1,  drawable: 2023, textures: 9,  isProp: 1 } ] },
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 1,  drawable: 2023, textures: 9,  isProp: 1 }, { gender: 1, component: 1,  drawable: 2024, textures: 9,  isProp: 1 } ] },
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 7,  drawable: 2076, textures: 21, isProp: 0 }, { gender: 1, component: 7,  drawable: 2066, textures: 21, isProp: 0 } ] }, // Кобура на 2 ноги, золотая
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 1,  drawable: 2024, textures: 9,  isProp: 1 }, { gender: 1, component: 1,  drawable: 2025, textures: 9,  isProp: 1 } ] },
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 11, drawable: 2395, textures: 9,  isProp: 0 }, { gender: 1, component: 11, drawable: 2397, textures: 9,  isProp: 0 } ] },
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 0,  drawable: 2084, textures: 8,  isProp: 1 }, { gender: 1, component: 0,  drawable: 2087, textures: 8,  isProp: 1 } ] },
        { type: 'clothes', price: 3000, color: 'gold',   chance: 1.0, value: [ { gender: 0, component: 1,  drawable: 2142, textures: 8,  isProp: 0 }, { gender: 1, component: 1,  drawable: 2128, textures: 8,  isProp: 0 } ] },

        { type: 'vehicle', price: 250,   color: 'blue', chance: 10, value: 'inductor',   customPrice: 40000 }, // Велосипед
        { type: 'vehicle', price: 6000,  color: 'gold', chance: 16, value: 'm2g42',      customPrice: 1000000 },
        { type: 'vehicle', price: 6000,  color: 'gold', chance: 15, value: 'mustang3',   customPrice: 1400000 },
        { type: 'vehicle', price: 6000,  color: 'gold', chance: 14, value: 'polestar',   customPrice: 1800000 },
        { type: 'vehicle', price: 6000,  color: 'gold', chance: 13, value: 'eclass4',    customPrice: 2200000 },
        { type: 'vehicle', price: 6000,  color: 'gold', chance: 12, value: 'ix',         customPrice: 2800000 },
        { type: 'vehicle', price: 6000,  color: 'gold', chance: 11, value: 'ram2',       customPrice: 3200000 },
        { type: 'vehicle', price: 6000,  color: 'gold', chance: 10, value: 'dbx',        customPrice: 3600000 },
        { type: 'vehicle', price: 8000,  color: 'gold', chance: 9,  value: 'purosangue', customPrice: 4000000 },
        { type: 'vehicle', price: 9000,  color: 'gold', chance: 8,  value: 'lanzador',   customPrice: 4300000 },
        { type: 'vehicle', price: 10000, color: 'gold', chance: 7,  value: 'xk',         customPrice: 2000000 },
        { type: 'vehicle', price: 10000, color: 'gold', chance: 6,  value: 'daytona',    customPrice: 4600000 },
        { type: 'vehicle', price: 10000, color: 'gold', chance: 5,  value: 'xclass2',    customPrice: 5000000 },

        { type: 'vehicleSet', price: 1500, color: 'blue', chance: 40, value: { setId: 7, model: 'aclass', group: 57 } },
        //{ type: 'vehicleSet', price: 1500, color: 'blue', chance: 40, value: { setId: 8, model: 'aclass', group: 57 } },
        //{ type: 'vehicleSet', price: 1500, color: 'blue', chance: 40, value: { setId: 9, model: 'aclass', group: 57 } },
        { type: 'vehicleSet', price: 2000, color: 'blue', chance: 40, value: { setId: 3, model: 'm2g42', group: 58 } },
        //{ type: 'vehicleSet', price: 2000, color: 'blue', chance: 40, value: { setId: 4, model: 'm2g42', group: 58 } },
        //{ type: 'vehicleSet', price: 2000, color: 'blue', chance: 40, value: { setId: 5, model: 'm2g42', group: 58 } },
        { type: 'vehicleSet', price: 2000, color: 'blue', chance: 40, value: { setId: 6, model: 'm2g42', group: 58 } },
        //{ type: 'vehicleSet', price: 2000, color: 'blue', chance: 40, value: { setId: 7, model: 'm2g42', group: 58 } },
        //{ type: 'vehicleSet', price: 2000, color: 'blue', chance: 40, value: { setId: 8, model: 'm2g42', group: 58 } },
        { type: 'vehicleSet', price: 2100, color: 'blue', chance: 35, value: { setId: 4, model: '190e', group: 59 } },
        //{ type: 'vehicleSet', price: 2100, color: 'blue', chance: 35, value: { setId: 5, model: '190e', group: 59 } },
        //{ type: 'vehicleSet', price: 2100, color: 'blue', chance: 35, value: { setId: 6, model: '190e', group: 59 } },
        { type: 'vehicleSet', price: 2100, color: 'blue', chance: 35, value: { setId: 7, model: '190e', group: 59 } },
        //{ type: 'vehicleSet', price: 2100, color: 'blue', chance: 35, value: { setId: 8, model: '190e', group: 59 } },
        //{ type: 'vehicleSet', price: 2100, color: 'blue', chance: 35, value: { setId: 9, model: '190e', group: 59 } },
        { type: 'vehicleSet', price: 2100, color: 'blue', chance: 35, value: { setId: 14, model: '190e', group: 59 } },
        //{ type: 'vehicleSet', price: 2100, color: 'blue', chance: 35, value: { setId: 15, model: '190e', group: 59 } },
        //{ type: 'vehicleSet', price: 2100, color: 'blue', chance: 35, value: { setId: 16, model: '190e', group: 59 } },
        { type: 'vehicleSet', price: 2200, color: 'blue', chance: 35, value: { setId: 2, model: 'charger2', group: 60 } },
        //{ type: 'vehicleSet', price: 2200, color: 'blue', chance: 35, value: { setId: 3, model: 'charger2', group: 60 } },
        { type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 3, model: 'nsx2', group: 61 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 4, model: 'nsx2', group: 61 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 5, model: 'nsx2', group: 61 } },
        { type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 6, model: 'nsx2', group: 61 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 7, model: 'nsx2', group: 61 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 8, model: 'nsx2', group: 61 } },
        { type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 9, model: 'nsx2', group: 61 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 10, model: 'nsx2', group: 61 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 11, model: 'nsx2', group: 61 } },
        { type: 'vehicleSet', price: 5000, color: 'blue', chance: 35, value: { setId: 9, model: 'taycan', group: 62 } },
        //{ type: 'vehicleSet', price: 5000, color: 'blue', chance: 35, value: { setId: 10, model: 'taycan', group: 62 } },
        //{ type: 'vehicleSet', price: 5000, color: 'blue', chance: 35, value: { setId: 11, model: 'taycan', group: 62 } },
        { type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 3, model: 'gtr32', group: 63 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 4, model: 'gtr32', group: 63 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 5, model: 'gtr32', group: 63 } },
        { type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 6, model: 'gtr32', group: 63 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 7, model: 'gtr32', group: 63 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 8, model: 'gtr32', group: 63 } },
        { type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 9, model: 'gtr32', group: 63 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 10, model: 'gtr32', group: 63 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 11, model: 'gtr32', group: 63 } },
        { type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 12, model: 'gtr32', group: 63 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 13, model: 'gtr32', group: 63 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 14, model: 'gtr32', group: 63 } },
        { type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 15, model: 'gtr32', group: 63 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 16, model: 'gtr32', group: 63 } },
        //{ type: 'vehicleSet', price: 2300, color: 'blue', chance: 35, value: { setId: 17, model: 'gtr32', group: 63 } },
        { type: 'vehicleSet', price: 5000, color: 'blue', chance: 35, value: { setId: 9, model: 'm2g42', group: 64 } },
        //{ type: 'vehicleSet', price: 6000, color: 'blue', chance: 35, value: { setId: 10, model: 'm2g42', group: 64 } },
        //{ type: 'vehicleSet', price: 6000, color: 'blue', chance: 35, value: { setId: 11, model: 'm2g42', group: 64 } },
        { type: 'vehicleSet', price: 5000, color: 'blue', chance: 35, value: { setId: 4, model: 'mustang3', group: 65 } },
        //{ type: 'vehicleSet', price: 6000, color: 'blue', chance: 35, value: { setId: 5, model: 'mustang3', group: 65 } },
        //{ type: 'vehicleSet', price: 6000, color: 'blue', chance: 35, value: { setId: 6, model: 'mustang3', group: 65 } },
        { type: 'vehicleSet', price: 5000, color: 'purple', chance: 30, value: { setId: 14, model: 'sclass3', group: 66 } },
        //{ type: 'vehicleSet', price: 5000, color: 'purple', chance: 30, value: { setId: 15, model: 'sclass3', group: 66 } },
        //{ type: 'vehicleSet', price: 5000, color: 'purple', chance: 30, value: { setId: 16, model: 'sclass3', group: 66 } },
        { type: 'vehicleSet', price: 2500, color: 'purple', chance: 30, value: { setId: 10, model: '190e', group: 67 } },
        //{ type: 'vehicleSet', price: 2500, color: 'purple', chance: 30, value: { setId: 11, model: '190e', group: 67 } },
        //{ type: 'vehicleSet', price: 2500, color: 'purple', chance: 30, value: { setId: 12, model: '190e', group: 67 } },
        //{ type: 'vehicleSet', price: 2500, color: 'purple', chance: 30, value: { setId: 13, model: '190e', group: 67 } },
        { type: 'vehicleSet', price: 2500, color: 'purple', chance: 30, value: { setId: 17, model: '190e', group: 67 } },
        //{ type: 'vehicleSet', price: 2500, color: 'purple', chance: 30, value: { setId: 18, model: '190e', group: 67 } },
        //{ type: 'vehicleSet', price: 2500, color: 'purple', chance: 30, value: { setId: 19, model: '190e', group: 67 } },
        { type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 18, model: 'gtr32', group: 68 } },
        //{ type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 19, model: 'gtr32', group: 68 } },
        //{ type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 20, model: 'gtr32', group: 68 } },
        { type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 22, model: 'gtr32', group: 68 } },
        //{ type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 23, model: 'gtr32', group: 68 } },
        //{ type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 24, model: 'gtr32', group: 68 } },
        { type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 12, model: 'nsx2', group: 69 } },
        //{ type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 13, model: 'nsx2', group: 69 } },
        //{ type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 14, model: 'nsx2', group: 69 } },
        { type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 4, model: 'charger2', group: 70 } },
        //{ type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 5, model: 'charger2', group: 70 } },
        { type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 6, model: 'charger2', group: 70 } },
        //{ type: 'vehicleSet', price: 2600, color: 'purple', chance: 30, value: { setId: 7, model: 'charger2', group: 70 } },
        { type: 'vehicleSet', price: 3000, color: 'purple', chance: 30, value: { setId: 7, model: 'aclass2', group: 71 } },
        //{ type: 'vehicleSet', price: 3000, color: 'purple', chance: 30, value: { setId: 8, model: 'aclass2', group: 71 } },
        //{ type: 'vehicleSet', price: 3000, color: 'purple', chance: 30, value: { setId: 9, model: 'aclass2', group: 71 } },
        { type: 'vehicleSet', price: 5000, color: 'purple', chance: 30, value: { setId: 12, model: 'm2g42', group: 72 } },
        //{ type: 'vehicleSet', price: 6000, color: 'purple', chance: 30, value: { setId: 13, model: 'm2g42', group: 72 } },
        //{ type: 'vehicleSet', price: 6000, color: 'purple', chance: 30, value: { setId: 14, model: 'm2g42', group: 72 } },
        { type: 'vehicleSet', price: 5000, color: 'purple', chance: 30, value: { setId: 7, model: 'm7g70', group: 73 } },
        //{ type: 'vehicleSet', price: 5000, color: 'purple', chance: 30, value: { setId: 8, model: 'm7g70', group: 73 } },
        //{ type: 'vehicleSet', price: 5000, color: 'purple', chance: 30, value: { setId: 9, model: 'm7g70', group: 73 } },
        { type: 'vehicleSet', price: 5000, color: 'purple', chance: 30, value: { setId: 16, model: 's15', group: 74 } },
        //{ type: 'vehicleSet', price: 5000, color: 'purple', chance: 30, value: { setId: 17, model: 's15', group: 74 } },
        //{ type: 'vehicleSet', price: 5000, color: 'purple', chance: 30, value: { setId: 18, model: 's15', group: 74 } },
        { type: 'vehicleSet', price: 4000, color: 'red', chance: 25, value: { setId: 4, model: 'escalade2', group: 75 } },
        //{ type: 'vehicleSet', price: 4000, color: 'red', chance: 25, value: { setId: 5, model: 'escalade2', group: 75 } },
        //{ type: 'vehicleSet', price: 4000, color: 'red', chance: 25, value: { setId: 6, model: 'escalade2', group: 75 } },
        { type: 'vehicleSet', price: 4000, color: 'red', chance: 25, value: { setId: 4, model: 'aclass2', group: 76 } },
        //{ type: 'vehicleSet', price: 4000, color: 'red', chance: 25, value: { setId: 5, model: 'aclass2', group: 76 } },
        //{ type: 'vehicleSet', price: 4000, color: 'red', chance: 25, value: { setId: 6, model: 'aclass2', group: 76 } },
        { type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 21, model: 'gtr32', group: 77 } },
        { type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 20, model: '190e', group: 78 } },
        { type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 3, model: 'polestar', group: 79 } },
        //{ type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 4, model: 'polestar', group: 79 } },
        //{ type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 5, model: 'polestar', group: 79 } },
        { type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 6, model: 'polestar', group: 79 } },
        //{ type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 7, model: 'polestar', group: 79 } },
        //{ type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 8, model: 'polestar', group: 79 } },
        { type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 9, model: 'polestar', group: 79 } },
        //{ type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 10, model: 'polestar', group: 79 } },
        //{ type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 11, model: 'polestar', group: 79 } },
        { type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 12, model: 'polestar', group: 79 } },
        //{ type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 13, model: 'polestar', group: 79 } },
        //{ type: 'vehicleSet', price: 5000, color: 'red', chance: 20, value: { setId: 14, model: 'polestar', group: 79 } },
        { type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 3, model: 'corvette2', group: 80 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 4, model: 'corvette2', group: 80 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 5, model: 'corvette2', group: 80 } },
        { type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 6, model: 'corvette2', group: 80 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 7, model: 'corvette2', group: 80 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 8, model: 'corvette2', group: 80 } },
        { type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 3, model: 'regera', group: 81 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 4, model: 'regera', group: 81 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 5, model: 'regera', group: 81 } },
        { type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 6, model: 'regera', group: 81 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 7, model: 'regera', group: 81 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 8, model: 'regera', group: 81 } },
        { type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 9, model: 'regera', group: 81 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 10, model: 'regera', group: 81 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 11, model: 'regera', group: 81 } },
        { type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 3, model: 'ix', group: 82 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 4, model: 'ix', group: 82 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 5, model: 'ix', group: 82 } },
        { type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 4, model: 'dbx', group: 83 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 5, model: 'dbx', group: 83 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 6, model: 'dbx', group: 83 } },
        { type: 'vehicleSet', price: 5000, color: 'red', chance: 15, value: { setId: 15, model: 'polestar', group: 84 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 16, model: 'polestar', group: 84 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 17, model: 'polestar', group: 84 } },
        { type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 18, model: 'polestar', group: 84 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 19, model: 'polestar', group: 84 } },
        //{ type: 'vehicleSet', price: 6000, color: 'red', chance: 15, value: { setId: 20, model: 'polestar', group: 84 } },
        { type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 9, model: 'corvette2', group: 85 } },
        //{ type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 10, model: 'corvette2', group: 85 } },
        //{ type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 11, model: 'corvette2', group: 85 } },
        { type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 12, model: 'regera', group: 86 } },
        //{ type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 13, model: 'regera', group: 86 } },
        //{ type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 14, model: 'regera', group: 86 } },
        { type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 9, model: 'diablo', group: 87 } },
        //{ type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 10, model: 'diablo', group: 87 } },
        //{ type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 11, model: 'diablo', group: 87 } },
        { type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 3, model: 'xm', group: 88 } },
        //{ type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 4, model: 'xm', group: 88 } },
        //{ type: 'vehicleSet', price: 7000, color: 'red', chance: 10, value: { setId: 5, model: 'xm', group: 88 } },

        //{ type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 1, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        //{ type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 2, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        { type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 3, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        //{ type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 4, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        //{ type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 5, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        { type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 6, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        //{ type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 7, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        //{ type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 8, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        { type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 9, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        //{ type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 10, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        //{ type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 11, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        { type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 12, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        //{ type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 13, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        //{ type: 'vehicleMod', price: 8000, color: 'gold', chance: 10, value: { setId: 14, model: 'm2g42', group: 89 }, customPrice: 1000000 },
        //{ type: 'vehicleMod', price: 9000, color: 'gold', chance: 10, value: { setId: 1, model: 'mustang3', group: 90 }, customPrice: 1400000 },
        //{ type: 'vehicleMod', price: 9000, color: 'gold', chance: 10, value: { setId: 2, model: 'mustang3', group: 90 }, customPrice: 1400000 },
        //{ type: 'vehicleMod', price: 9000, color: 'gold', chance: 10, value: { setId: 3, model: 'mustang3', group: 90 }, customPrice: 1400000 },
        { type: 'vehicleMod', price: 9000, color: 'gold', chance: 10, value: { setId: 4, model: 'mustang3', group: 90 }, customPrice: 1400000 },
        //{ type: 'vehicleMod', price: 9000, color: 'gold', chance: 10, value: { setId: 5, model: 'mustang3', group: 90 }, customPrice: 1400000 },
        //{ type: 'vehicleMod', price: 9000, color: 'gold', chance: 10, value: { setId: 6, model: 'mustang3', group: 90 }, customPrice: 1400000 },
        { type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 3, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 4, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 5, model: 'polestar', group: 91 }, customPrice: 1800000 },
        { type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 6, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 7, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 8, model: 'polestar', group: 91 }, customPrice: 1800000 },
        { type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 9, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 10, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 11, model: 'polestar', group: 91 }, customPrice: 1800000 },
        { type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 12, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 13, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 14, model: 'polestar', group: 91 }, customPrice: 1800000 },
        { type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 15, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 16, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 17, model: 'polestar', group: 91 }, customPrice: 1800000 },
        { type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 18, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 19, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 20, model: 'polestar', group: 91 }, customPrice: 1800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 1, model: 'eclass4', group: 92 }, customPrice: 2200000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 2, model: 'eclass4', group: 92 }, customPrice: 2200000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 10, value: { setId: 3, model: 'eclass4', group: 92 }, customPrice: 2200000 },
        //{ type: 'vehicleMod', price: 12000, color: 'gold', chance: 8, value: { setId: 1, model: 'ix', group: 93 }, customPrice: 2800000 },
        //{ type: 'vehicleMod', price: 12000, color: 'gold', chance: 8, value: { setId: 2, model: 'ix', group: 93 }, customPrice: 2800000 },
        { type: 'vehicleMod', price: 12000, color: 'gold', chance: 8, value: { setId: 3, model: 'ix', group: 93 }, customPrice: 2800000 },
        //{ type: 'vehicleMod', price: 12000, color: 'gold', chance: 8, value: { setId: 4, model: 'ix', group: 93 }, customPrice: 2800000 },
        //{ type: 'vehicleMod', price: 12000, color: 'gold', chance: 8, value: { setId: 5, model: 'ix', group: 93 }, customPrice: 2800000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 9, value: { setId: 1, model: 'ram2', group: 94 }, customPrice: 3200000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 9, value: { setId: 2, model: 'ram2', group: 94 }, customPrice: 3200000 },
        //{ type: 'vehicleMod', price: 10000, color: 'gold', chance: 9, value: { setId: 3, model: 'ram2', group: 94 }, customPrice: 3200000 },
        //{ type: 'vehicleMod', price: 15000, color: 'gold', chance: 6, value: { setId: 1, model: 'dbx', group: 95 }, customPrice: 3600000 },
        //{ type: 'vehicleMod', price: 15000, color: 'gold', chance: 6, value: { setId: 2, model: 'dbx', group: 95 }, customPrice: 3600000 },
        //{ type: 'vehicleMod', price: 15000, color: 'gold', chance: 6, value: { setId: 3, model: 'dbx', group: 95 }, customPrice: 3600000 },
        { type: 'vehicleMod', price: 15000, color: 'gold', chance: 6, value: { setId: 4, model: 'dbx', group: 95 }, customPrice: 3600000 },
        //{ type: 'vehicleMod', price: 15000, color: 'gold', chance: 6, value: { setId: 5, model: 'dbx', group: 95 }, customPrice: 3600000 },
        //{ type: 'vehicleMod', price: 15000, color: 'gold', chance: 6, value: { setId: 6, model: 'dbx', group: 95 }, customPrice: 3600000 },
        //{ type: 'vehicleMod', price: 16000, color: 'gold', chance: 5, value: { setId: 1, model: 'purosangue', group: 96 }, customPrice: 4000000 },
        //{ type: 'vehicleMod', price: 16000, color: 'gold', chance: 5, value: { setId: 2, model: 'purosangue', group: 96 }, customPrice: 4000000 },
        //{ type: 'vehicleMod', price: 18000, color: 'gold', chance: 4, value: { setId: 1, model: 'lanzador', group: 97 }, customPrice: 4300000 },
        //{ type: 'vehicleMod', price: 18000, color: 'gold', chance: 4, value: { setId: 2, model: 'lanzador', group: 97 }, customPrice: 4300000 },
        //{ type: 'vehicleMod', price: 22500, color: 'gold', chance: 3, value: { setId: 1, model: 'daytona', group: 98 }, customPrice: 4600000 },
        //{ type: 'vehicleMod', price: 22500, color: 'gold', chance: 3, value: { setId: 2, model: 'daytona', group: 98 }, customPrice: 4600000 },
        //{ type: 'vehicleMod', price: 20000, color: 'gold', chance: 4, value: { setId: 1, model: 'xclass2', group: 99 }, customPrice: 5000000 },
        //{ type: 'vehicleMod', price: 20000, color: 'gold', chance: 4, value: { setId: 2, model: 'xclass2', group: 99 }, customPrice: 5000000 },
        //{ type: 'vehicleMod', price: 22500, color: 'gold', chance: 3, value: { setId: 3, model: 'xclass2', group: 99 }, customPrice: 5000000 },
        { type: 'vehicleMod', price: 22500, color: 'gold', chance: 3, value: { setId: 4, model: 'xclass2', group: 99 }, customPrice: 5000000 },
        //{ type: 'vehicleMod', price: 22500, color: 'gold', chance: 3, value: { setId: 5, model: 'xclass2', group: 99 }, customPrice: 5000000 },
        { type: 'vehicleMod', price: 25000, color: 'gold', chance: 2, value: { setId: 6, model: 'xclass2', group: 99 }, customPrice: 5000000 },
        //{ type: 'vehicleMod', price: 25000, color: 'gold', chance: 2, value: { setId: 7, model: 'xclass2', group: 99 }, customPrice: 5000000 },
        //{ type: 'vehicleMod', price: 25000, color: 'gold', chance: 2, value: { setId: 8, model: 'xclass2', group: 99 }, customPrice: 5000000 },


        { type: 'wheels', price: 200, color: 'gray',   chance: 70, value: 183 },
        { type: 'wheels', price: 200, color: 'gray',   chance: 70, value: 186 },
        { type: 'wheels', price: 200, color: 'gray',   chance: 70, value: 189 },
        { type: 'wheels', price: 200, color: 'gray',   chance: 70, value: 192 },
        { type: 'wheels', price: 200, color: 'gray',   chance: 70, value: 195 },
        { type: 'wheels', price: 200, color: 'gray',   chance: 70, value: 198 },
        { type: 'wheels', price: 200, color: 'gray',   chance: 70, value: 201 },
        { type: 'wheels', price: 200, color: 'gray',   chance: 70, value: 204 },
        { type: 'wheels', price: 200, color: 'gray',   chance: 70, value: 207 },
        { type: 'wheels', price: 300, color: 'gray',   chance: 60, value: 210 },
        { type: 'wheels', price: 300, color: 'gray',   chance: 60, value: 213 },
        { type: 'wheels', price: 300, color: 'gray',   chance: 60, value: 216 },
        { type: 'wheels', price: 300, color: 'gray',   chance: 60, value: 219 },
        { type: 'wheels', price: 300, color: 'gray',   chance: 60, value: 222 },
        { type: 'wheels', price: 300, color: 'gray',   chance: 60, value: 225 },
        { type: 'wheels', price: 300, color: 'gray',   chance: 60, value: 228 },
        { type: 'wheels', price: 300, color: 'gray',   chance: 60, value: 231 },
        { type: 'wheels', price: 350, color: 'blue',   chance: 55, value: 184 },
        { type: 'wheels', price: 350, color: 'blue',   chance: 55, value: 187 },
        { type: 'wheels', price: 350, color: 'blue',   chance: 55, value: 190 },
        { type: 'wheels', price: 350, color: 'blue',   chance: 55, value: 193 },
        { type: 'wheels', price: 350, color: 'blue',   chance: 55, value: 196 },
        { type: 'wheels', price: 350, color: 'blue',   chance: 55, value: 199 },
        { type: 'wheels', price: 350, color: 'blue',   chance: 55, value: 202 },
        { type: 'wheels', price: 350, color: 'blue',   chance: 55, value: 206 },
        { type: 'wheels', price: 350, color: 'blue',   chance: 55, value: 208 },
        { type: 'wheels', price: 450, color: 'blue',   chance: 45, value: 211 },
        { type: 'wheels', price: 450, color: 'blue',   chance: 45, value: 214 },
        { type: 'wheels', price: 450, color: 'blue',   chance: 45, value: 217 },
        { type: 'wheels', price: 450, color: 'blue',   chance: 45, value: 220 },
        { type: 'wheels', price: 450, color: 'blue',   chance: 45, value: 223 },
        { type: 'wheels', price: 450, color: 'blue',   chance: 45, value: 226 },
        { type: 'wheels', price: 450, color: 'blue',   chance: 45, value: 229 },
        { type: 'wheels', price: 450, color: 'blue',   chance: 45, value: 232 },
        { type: 'wheels', price: 500, color: 'purple', chance: 35, value: 185 },
        { type: 'wheels', price: 500, color: 'purple', chance: 35, value: 188 },
        { type: 'wheels', price: 500, color: 'purple', chance: 35, value: 191 },
        { type: 'wheels', price: 500, color: 'purple', chance: 35, value: 194 },
        { type: 'wheels', price: 500, color: 'purple', chance: 35, value: 197 },
        { type: 'wheels', price: 500, color: 'purple', chance: 35, value: 200 },
        { type: 'wheels', price: 500, color: 'purple', chance: 35, value: 203 },
        { type: 'wheels', price: 500, color: 'purple', chance: 35, value: 207 },
        { type: 'wheels', price: 500, color: 'purple', chance: 35, value: 209 },
        { type: 'wheels', price: 600, color: 'purple', chance: 30, value: 212 },
        { type: 'wheels', price: 600, color: 'purple', chance: 30, value: 215 },
        { type: 'wheels', price: 600, color: 'purple', chance: 30, value: 218 },
        { type: 'wheels', price: 600, color: 'purple', chance: 30, value: 221 },
        { type: 'wheels', price: 600, color: 'purple', chance: 30, value: 224 },
        { type: 'wheels', price: 600, color: 'purple', chance: 30, value: 227 },
        { type: 'wheels', price: 600, color: 'purple', chance: 30, value: 230 },
        { type: 'wheels', price: 600, color: 'purple', chance: 30, value: 233 },

    ]
}
