module.exports = {
    id: 16,
    title: 'donate.config.cases.summerVehicles2024.title',
    // discount: 5,

    start: ['2024-05-20', 'YYYY-MM-DD'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    seasonPassId: 5,

    // Сумма распыления указан в опыте батлпаса. Если батлпас активен
    sellRule: 40, // Если батлпас уже не активен, то задать процент конвертации price в коины. Например 100 exp / 100 * 25 = 25 коинов

    price: 1500,
    transferDays: 30,

    // servers: ['STAGING0', 'DEVELOP0', 'TEST2'],
    // disabledBuy: true,
    // readOnly: false,

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    colors: [
        {
            color: 'gray',
            chance: 58,
            types: [
                { type: 'wheels',     chance: 25 },
                { type: 'vehicle',    chance: 15 },
                { type: 'vehicleMod', chance: 10 },
            ],
        },
        {
            color: 'blue',
            chance: 18,
            types: [
                { type: 'wheels',     chance: 25 },
                { type: 'vehicle',    chance: 15 },
                { type: 'vehicleMod', chance: 10 },
            ],
        },
        {
            color: 'purple',
            chance: 11,
            types: [
                { type: 'wheels',     chance: 25 },
                { type: 'vehicle',    chance: 15 },
                { type: 'vehicleMod', chance: 10 },
            ],
        },
        {
            color: 'red',
            chance: 6.5,
            types: [
                { type: 'wheels',     chance: 25 },
                { type: 'vehicle',    chance: 15 },
                { type: 'vehicleMod', chance: 10 },
            ],
        },
        {
            color: 'gold',
            chance: 2.2,
            types: [
                { type: 'wheels',     chance: 25 },
                { type: 'vehicle',    chance: 12 },
                { type: 'vehicleMod', chance: 10 },
            ],
        },
        {
            color: 'unique',
            chance: 0.2,
            types: [
                { type: 'vehicle',    chance: 15 },
                { type: 'vehicleMod', chance: 10 },
            ],
        },

    ],

    caseContent: [
        { type: 'wheels',       color: 'gray', chance: 5.6,  price: 350, value: 237 },
        { type: 'wheels',       color: 'gray', chance: 5.6,  price: 350, value: 249 },
        { type: 'wheels',       color: 'gray', chance: 5.6,  price: 350, value: 264 },
        { type: 'wheels',       color: 'gray', chance: 5.6,  price: 350, value: 276 },
        { type: 'wheels',       color: 'gray', chance: 5.6,  price: 350, value: 282 },

        { type: 'vehicle',      color: 'gray', chance: 6,    price: 500, customPrice: 30000, value: 'impaler6' },
        { type: 'vehicleMod',   color: 'gray', chance: 2.0,  price: 550, customPrice: 30000, value: { model: 'impaler6', setId: 1, group: 1 } },
        { type: 'vehicleMod',   color: 'gray', chance: 1.7,  price: 500, customPrice: 30000, value: { model: 'impaler6', setId: 2, group: 1 } },
        { type: 'vehicleMod',   color: 'gray', chance: 1.3,  price: 550, customPrice: 30000, value: { model: 'impaler6', setId: 3, group: 1 } },
        { type: 'vehicleMod',   color: 'gray', chance: 1.0,  price: 500, customPrice: 30000, value: { model: 'impaler6', setId: 4, group: 1 } },

        { type: 'vehicle',      color: 'gray', chance: 22.0,  price: 550, customPrice: 50000, value: 'impaler5' },
        { type: 'vehicleMod',   color: 'gray', chance: 21.0,  price: 550, customPrice: 50000, value: { model: 'impaler5', setId: 1, group: 2 } },
        { type: 'vehicleMod',   color: 'gray', chance: 20.5,  price: 550, customPrice: 50000, value: { model: 'impaler5', setId: 2, group: 2 } },
        { type: 'vehicleMod',   color: 'gray', chance: 20.0,  price: 550, customPrice: 50000, value: { model: 'impaler5', setId: 3, group: 2 } },
        { type: 'vehicleMod',   color: 'gray', chance: 19.5,  price: 550, customPrice: 50000, value: { model: 'impaler5', setId: 4, group: 2 } },
        { type: 'vehicleMod',   color: 'gray', chance: 19.0,  price: 550, customPrice: 50000, value: { model: 'impaler5', setId: 5, group: 2 } },

        { type: 'vehicle',      color: 'gray', chance: 17.0,  price: 600, customPrice: 70000, value: 'fr36' },
        { type: 'vehicleMod',   color: 'gray', chance: 16.5,  price: 600, customPrice: 70000, value: { model: 'fr36', setId: 1, group: 3 } },
        { type: 'vehicleMod',   color: 'gray', chance: 16.0,  price: 600, customPrice: 70000, value: { model: 'fr36', setId: 2, group: 3 } },
        { type: 'vehicleMod',   color: 'gray', chance: 15.5,  price: 600, customPrice: 70000, value: { model: 'fr36', setId: 3, group: 3 } },
        { type: 'vehicleMod',   color: 'gray', chance: 15.0,  price: 600, customPrice: 70000, value: { model: 'fr36', setId: 4, group: 3 } },
        { type: 'vehicleMod',   color: 'gray', chance: 14.5,  price: 600, customPrice: 70000, value: { model: 'fr36', setId: 5, group: 3 } },
        { type: 'vehicleMod',   color: 'gray', chance: 14.0,  price: 600, customPrice: 70000, value: { model: 'fr36', setId: 6, group: 3 } },

        { type: 'vehicle',      color: 'gray', chance: 12.0,  price: 650, customPrice: 90000, value: 'dorado' },
        { type: 'vehicleMod',   color: 'gray', chance: 11.5,  price: 650, customPrice: 90000, value: { model: 'dorado', setId: 1, group: 4 } },
        { type: 'vehicleMod',   color: 'gray', chance: 11.0,  price: 650, customPrice: 90000, value: { model: 'dorado', setId: 2, group: 4 } },
        { type: 'vehicleMod',   color: 'gray', chance: 10.5,  price: 650, customPrice: 90000, value: { model: 'dorado', setId: 3, group: 4 } },
        { type: 'vehicleMod',   color: 'gray', chance: 10.0,  price: 650, customPrice: 90000, value: { model: 'dorado', setId: 4, group: 4 } },

        { type: 'vehicle',      color: 'gray', chance: 8.5,  price: 700, customPrice: 140000, value: 'vivanite' },
        { type: 'vehicleMod',   color: 'gray', chance: 8.0,  price: 700, customPrice: 140000, value: { model: 'vivanite', setId: 1, group: 5 } },
        { type: 'vehicleMod',   color: 'gray', chance: 7.5,  price: 700, customPrice: 140000, value: { model: 'vivanite', setId: 2, group: 5 } },
        { type: 'vehicleMod',   color: 'gray', chance: 7.0,  price: 700, customPrice: 140000, value: { model: 'vivanite', setId: 3, group: 5 } },
        { type: 'vehicleMod',   color: 'gray', chance: 6.5,  price: 700, customPrice: 140000, value: { model: 'vivanite', setId: 4, group: 5 } },
        { type: 'vehicleMod',   color: 'gray', chance: 6.0,  price: 700, customPrice: 140000, value: { model: 'vivanite', setId: 5, group: 5 } },

        { type: 'vehicle',      color: 'gray', chance: 4.0,  price: 750, customPrice: 160000, value: 'terminus' },
        { type: 'vehicleMod',   color: 'gray', chance: 3.5,  price: 750, customPrice: 160000, value: { model: 'terminus', setId: 1, group: 6 } },
        { type: 'vehicleMod',   color: 'gray', chance: 3.0,  price: 750, customPrice: 160000, value: { model: 'terminus', setId: 2, group: 6 } },
        { type: 'vehicleMod',   color: 'gray', chance: 2.5,  price: 750, customPrice: 160000, value: { model: 'terminus', setId: 3, group: 6 } },
        { type: 'vehicleMod',   color: 'gray', chance: 2.0,  price: 750, customPrice: 160000, value: { model: 'terminus', setId: 4, group: 6 } },
        { type: 'vehicleMod',   color: 'gray', chance: 1.5,  price: 750, customPrice: 160000, value: { model: 'terminus', setId: 5, group: 6 } },
        { type: 'vehicleMod',   color: 'gray', chance: 1.0,  price: 750, customPrice: 160000, value: { model: 'terminus', setId: 6, group: 6 } },
        // GRAY - конец

        // BLUE - начало
        { type: 'wheels',       color: 'blue', chance: 5.6,  price: 600, value: 243 },
        { type: 'wheels',       color: 'blue', chance: 5.6,  price: 600, value: 255 },
        { type: 'wheels',       color: 'blue', chance: 5.6,  price: 600, value: 261 },
        { type: 'wheels',       color: 'blue', chance: 5.6,  price: 600, value: 270 },
        { type: 'wheels',       color: 'blue', chance: 5.6,  price: 600, value: 288 },


        { type: 'vehicle',      color: 'blue', chance: 26.0,  price: 900, customPrice: 110000, value: 'aleutian' },
        { type: 'vehicleMod',   color: 'blue', chance: 25.5,  price: 900, customPrice: 110000, value: { model: 'aleutian', setId: 1, group: 7 } },
        { type: 'vehicleMod',   color: 'blue', chance: 25.0,  price: 900, customPrice: 110000, value: { model: 'aleutian', setId: 2, group: 7 } },
        { type: 'vehicleMod',   color: 'blue', chance: 24.5,  price: 900, customPrice: 110000, value: { model: 'aleutian', setId: 3, group: 7 } },
        { type: 'vehicleMod',   color: 'blue', chance: 24.0,  price: 900, customPrice: 110000, value: { model: 'aleutian', setId: 4, group: 7 } },

        { type: 'vehicle',      color: 'blue', chance: 22.0,  price: 1000, customPrice: 140000, value: 'baller8' },
        { type: 'vehicleMod',   color: 'blue', chance: 21.5,  price: 1000, customPrice: 140000, value: { model: 'baller8', setId: 1, group: 8 } },
        { type: 'vehicleMod',   color: 'blue', chance: 21.0,  price: 1000, customPrice: 140000, value: { model: 'baller8', setId: 2, group: 8 } },
        { type: 'vehicleMod',   color: 'blue', chance: 20.5,  price: 1000, customPrice: 140000, value: { model: 'baller8', setId: 3, group: 8 } },
        { type: 'vehicleMod',   color: 'blue', chance: 20.0,  price: 1000, customPrice: 140000, value: { model: 'baller8', setId: 4, group: 8 } },
        { type: 'vehicleMod',   color: 'blue', chance: 19.5,  price: 1000, customPrice: 140000, value: { model: 'baller8', setId: 5, group: 8 } },
        { type: 'vehicleMod',   color: 'blue', chance: 19.0,  price: 1000, customPrice: 140000, value: { model: 'baller8', setId: 6, group: 8 } },

        { type: 'vehicle',      color: 'blue', chance: 17.5,  price: 1100, customPrice: 160000, value: 'dominator9' },
        { type: 'vehicleMod',   color: 'blue', chance: 17.0,  price: 1100, customPrice: 160000, value: { model: 'dominator9', setId: 1, group: 9 } },
        { type: 'vehicleMod',   color: 'blue', chance: 16.5,  price: 1100, customPrice: 160000, value: { model: 'dominator9', setId: 2, group: 9 } },
        { type: 'vehicleMod',   color: 'blue', chance: 16.0,  price: 1100, customPrice: 160000, value: { model: 'dominator9', setId: 3, group: 9 } },

        { type: 'vehicle',      color: 'blue', chance: 14.0,  price: 1200, customPrice: 180000, value: 'vigero3' },
        { type: 'vehicleMod',   color: 'blue', chance: 13.5,  price: 1200, customPrice: 180000, value: { model: 'vigero3', setId: 1, group: 10 } },
        { type: 'vehicleMod',   color: 'blue', chance: 13.0,  price: 1200, customPrice: 180000, value: { model: 'vigero3', setId: 2, group: 10 } },
        { type: 'vehicleMod',   color: 'blue', chance: 12.5,  price: 1200, customPrice: 180000, value: { model: 'vigero3', setId: 3, group: 10 } },
        { type: 'vehicleMod',   color: 'blue', chance: 12.0,  price: 1200, customPrice: 180000, value: { model: 'vigero3', setId: 4, group: 10 } },
        { type: 'vehicleMod',   color: 'blue', chance: 11.5,  price: 1200, customPrice: 180000, value: { model: 'vigero3', setId: 5, group: 10 } },
        { type: 'vehicleMod',   color: 'blue', chance: 11.0,  price: 1200, customPrice: 180000, value: { model: 'vigero3', setId: 6, group: 10 } },
        { type: 'vehicleMod',   color: 'blue', chance: 10.5,  price: 1200, customPrice: 180000, value: { model: 'vigero3', setId: 8, group: 10 } },
        { type: 'vehicleMod',   color: 'blue', chance: 10.0,  price: 1200, customPrice: 180000, value: { model: 'vigero3', setId: 9, group: 10 } },

        { type: 'vehicle',      color: 'blue', chance: 8.0,  price: 1300, customPrice: 200000, value: 'cavalcade3' },
        { type: 'vehicleMod',   color: 'blue', chance: 7.5,  price: 1300, customPrice: 200000, value: { model: 'cavalcade3', setId: 1, group: 11 } },
        { type: 'vehicleMod',   color: 'blue', chance: 7.0,  price: 1300, customPrice: 200000, value: { model: 'cavalcade3', setId: 2, group: 11 } },
        { type: 'vehicleMod',   color: 'blue', chance: 6.5,  price: 1300, customPrice: 200000, value: { model: 'cavalcade3', setId: 3, group: 11 } },
        { type: 'vehicleMod',   color: 'blue', chance: 6.0,  price: 1300, customPrice: 200000, value: { model: 'cavalcade3', setId: 4, group: 11 } },

        { type: 'vehicle',      color: 'blue', chance: 4.0,  price: 1400, customPrice: 300000, value: 'turismo3' },
        { type: 'vehicleMod',   color: 'blue', chance: 3.5,  price: 1400, customPrice: 300000, value: { model: 'turismo3', setId: 1, group: 12 } },
        { type: 'vehicleMod',   color: 'blue', chance: 3.0,  price: 1400, customPrice: 300000, value: { model: 'turismo3', setId: 2, group: 12 } },
        { type: 'vehicleMod',   color: 'blue', chance: 2.5,  price: 1400, customPrice: 300000, value: { model: 'turismo3', setId: 3, group: 12 } },
        { type: 'vehicleMod',   color: 'blue', chance: 2.0,  price: 1400, customPrice: 300000, value: { model: 'turismo3', setId: 4, group: 12 } },
        { type: 'vehicleMod',   color: 'blue', chance: 1.5,  price: 1400, customPrice: 300000, value: { model: 'turismo3', setId: 5, group: 12 } },
        { type: 'vehicleMod',   color: 'blue', chance: 1.0,  price: 1400, customPrice: 300000, value: { model: 'turismo3', setId: 6, group: 12 } },
        // BLUE - конец

        // PURPLE - начало
        { type: 'wheels',       color: 'purple', chance: 4,    price: 1200, value: 238 },
        { type: 'wheels',       color: 'purple', chance: 4,    price: 1200, value: 250 },
        { type: 'wheels',       color: 'purple', chance: 4,    price: 1200, value: 265 },
        { type: 'wheels',       color: 'purple', chance: 4,    price: 1200, value: 277 },
        { type: 'wheels',       color: 'purple', chance: 4,    price: 1200, value: 283 },


        { type: 'vehicle',      color: 'purple', chance: 40.5,  price: 1500, customPrice: 85000, value: 'impala2' },
        //{ type: 'vehicleMod',   color: 'purple', chance: 40.0,  price: 1500, customPrice: 85000, value: { model: 'impala2', setId: 1 } },

        { type: 'vehicle',      color: 'purple', chance: 38.5,  price: 1600, customPrice: 105000, value: 'fury' },
        //{ type: 'vehicleMod',   color: 'purple', chance: 38.0,  price: 1600, customPrice: 105000, value: { model: 'fury', setId: 1 } },

        { type: 'vehicle',      color: 'purple', chance: 36.5,  price: 1700, customPrice: 112500, value: 'golf2' },
        //{ type: 'vehicleMod',   color: 'purple', chance: 36.0,  price: 1700, customPrice: 112500, value: { model: 'golf2', setId: 1, group: 15 } },
        { type: 'vehicleMod',   color: 'purple', chance: 34.0,  price: 1800, customPrice: 112500, value: { model: 'golf2', setId: 2, group: 15 } },
        { type: 'vehicleMod',   color: 'purple', chance: 32.0,  price: 1800, customPrice: 112500, value: { model: 'golf2', setId: 3, group: 15 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 31.5,  price: 1900, customPrice: 112500, value: { model: 'golf2', setId: 4, group: 15 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 31.0,  price: 1900, customPrice: 112500, value: { model: 'golf2', setId: 5, group: 15 } },

        { type: 'vehicle',      color: 'purple', chance: 29.5,  price: 2000, customPrice: 150000, value: 'civic' },
        //{ type: 'vehicleMod',   color: 'purple', chance: 29.0,  price: 2000, customPrice: 150000, value: { model: 'civic', setId: 1, group: 17 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 28.5,  price: 2100, customPrice: 150000, value: { model: 'civic', setId: 2, group: 17 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 28.0,  price: 2100, customPrice: 150000, value: { model: 'civic', setId: 3, group: 17 } },
        { type: 'vehicleMod',   color: 'purple', chance: 26.0,  price: 2200, customPrice: 150000, value: { model: 'civic', setId: 4, group: 17 } },
        { type: 'vehicleMod',   color: 'purple', chance: 24.5,  price: 2200, customPrice: 150000, value: { model: 'civic', setId: 5, group: 17 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 23.5,  price: 2300, customPrice: 150000, value: { model: 'civic', setId: 6, group: 17 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 23.0,  price: 2300, customPrice: 150000, value: { model: 'civic', setId: 7, group: 17 } },

        { type: 'vehicle',      color: 'purple', chance: 21.5,  price: 2400, customPrice: 187500, value: 'mx5' },
        //{ type: 'vehicleMod',   color: 'purple', chance: 21.0,  price: 2400, customPrice: 187500, value: { model: 'mx5', setId: 1, group: 19 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 20.5,  price: 2500, customPrice: 187500, value: { model: 'mx5', setId: 2, group: 19 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 20.0,  price: 2500, customPrice: 187500, value: { model: 'mx5', setId: 3, group: 19 } },
        { type: 'vehicleMod',   color: 'purple', chance: 18.5,  price: 2600, customPrice: 187500, value: { model: 'mx5', setId: 4, group: 19 } },
        { type: 'vehicleMod',   color: 'purple', chance: 18.0,  price: 2600, customPrice: 187500, value: { model: 'mx5', setId: 5, group: 19 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 17.5,  price: 2600, customPrice: 187500, value: { model: 'mx5', setId: 6, group: 19 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 17.0,  price: 2600, customPrice: 187500, value: { model: 'mx5', setId: 7, group: 19 } },

        { type: 'vehicle',      color: 'purple', chance: 15.5,  price: 2700, customPrice: 400000, value: 'hummer5' },
        //{ type: 'vehicleMod',   color: 'purple', chance: 15.0,  price: 2700, customPrice: 400000, value: { model: 'hummer5' } },

        { type: 'vehicle',      color: 'purple', chance: 13.5,  price: 2700, customPrice: 500000, value: 'countryman' },
        //{ type: 'vehicleMod',   color: 'purple', chance: 13.0,  price: 2700, customPrice: 500000, value: { model: 'countryman', setId: 1, group: 22 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 12.5,  price: 2700, customPrice: 500000, value: { model: 'countryman', setId: 2, group: 22 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 12.0,  price: 2700, customPrice: 500000, value: { model: 'countryman', setId: 3, group: 22 } },
        { type: 'vehicleMod',   color: 'purple', chance: 10.0,  price: 2800, customPrice: 500000, value: { model: 'countryman', setId: 4, group: 22 } },

        { type: 'vehicle',      color: 'purple', chance: 8.5,  price: 2800, customPrice: 750000, value: 'i82' },
        //{ type: 'vehicleMod',   color: 'purple', chance: 8.0,  price: 2800, customPrice: 750000, value: { model: 'i82', setId: 1, group: 23 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 7.5,  price: 2800, customPrice: 750000, value: { model: 'i82', setId: 2, group: 23 } },
        { type: 'vehicleMod',   color: 'purple', chance: 7.0,  price: 2800, customPrice: 750000, value: { model: 'i82', setId: 3, group: 23 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 4.5,  price: 2900, customPrice: 750000, value: { model: 'i82', setId: 4, group: 23 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 4.0,  price: 2900, customPrice: 750000, value: { model: 'i82', setId: 5, group: 23 } },

        { type: 'vehicle',      color: 'purple', chance: 2.5,  price: 3000, customPrice: 875000, value: 'tt2' },
        //{ type: 'vehicleMod',   color: 'purple', chance: 2.0,  price: 3000, customPrice: 875000, value: { model: 'tt2', setId: 1, group: 25 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 1.5,  price: 3000, customPrice: 875000, value: { model: 'tt2', setId: 2, group: 25 } },
        //{ type: 'vehicleMod',   color: 'purple', chance: 1.0,  price: 3000, customPrice: 875000, value: { model: 'tt2', setId: 3, group: 25 } },
        // PURPLE - нонец

        // RED - начало
        { type: 'wheels',       color: 'red', chance: 2,    price: 2800, value: 244 },
        { type: 'wheels',       color: 'red', chance: 2,    price: 2800, value: 256 },
        { type: 'wheels',       color: 'red', chance: 2,    price: 2800, value: 262 },
        { type: 'wheels',       color: 'red', chance: 2,    price: 2800, value: 271 },
        { type: 'wheels',       color: 'red', chance: 2,    price: 2800, value: 289 },


        { type: 'vehicleMod',   color: 'red', chance: 44.0,  price: 3000, customPrice: 112500, value: { model: 'golf2', setId: 6, group: 26 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 43.5,  price: 3200, customPrice: 112500, value: { model: 'golf2', setId: 7, group: 26 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 43.0,  price: 3400, customPrice: 112500, value: { model: 'golf2', setId: 8, group: 26 } },

        { type: 'vehicleMod',   color: 'red', chance: 41.0,  price: 3600, customPrice: 150000, value: { model: 'civic', setId: 8, group: 27 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 40.5,  price: 3800, customPrice: 150000, value: { model: 'civic', setId: 9, group: 27 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 40.0,  price: 4000, customPrice: 150000, value: { model: 'civic', setId: 10, group: 27 } },

        { type: 'vehicleMod',   color: 'red', chance: 38.5,  price: 4200, customPrice: 187500, value: { model: 'mx5', setId: 8, group: 28 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 38.0,  price: 4400, customPrice: 187500, value: { model: 'mx5', setId: 9, group: 28 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 37.5,  price: 4600, customPrice: 187500, value: { model: 'mx5', setId: 10, group: 28 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 37.0,  price: 4800, customPrice: 187500, value: { model: 'mx5', setId: 11, group: 28 } },

        { type: 'vehicleMod',   color: 'red', chance: 35.0,  price: 5000, customPrice: 500000, value: { model: 'countryman', setId: 5, group: 29 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 34.5,  price: 5200, customPrice: 500000, value: { model: 'countryman', setId: 6, group: 29 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 34.0,  price: 5400, customPrice: 500000, value: { model: 'countryman', setId: 7, group: 29 } },

        { type: 'vehicleMod',   color: 'red', chance: 32.0,  price: 5600, customPrice: 750000, value: { model: 'i82', setId: 6, group: 30 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 31.5,  price: 5800, customPrice: 750000, value: { model: 'i82', setId: 7, group: 30 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 31.0,  price: 6000, customPrice: 750000, value: { model: 'i82', setId: 8, group: 30 } },

        { type: 'vehicleMod',   color: 'red', chance: 29.5,  price: 6200, customPrice: 875000, value: { model: 'tt2', setId: 4, group: 31 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 28.5,  price: 6400, customPrice: 875000, value: { model: 'tt2', setId: 5, group: 31 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 28.0,  price: 6600, customPrice: 875000, value: { model: 'tt2', setId: 6, group: 31 } },

        { type: 'vehicle',      color: 'red', chance: 26.5,  price: 6800, customPrice: 1250000, value: 'corvette3' },
        //{ type: 'vehicleMod',   color: 'red', chance: 26.0,  price: 7000, customPrice: 1250000, value: { model: 'corvette3', setId: 1 } },

        { type: 'vehicle',      color: 'red', chance: 24.5,  price: 7200, customPrice: 1375000, value: 'levante' },
        //{ type: 'vehicleMod',   color: 'red', chance: 24.0,  price: 7400, customPrice: 1375000, value: { model: 'levante', setId: 1, group: 33 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 23.5,  price: 7600, customPrice: 1375000, value: { model: 'levante', setId: 2, group: 33 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 23.0,  price: 7800, customPrice: 1375000, value: { model: 'levante', setId: 3, group: 33 } },

        { type: 'vehicle',      color: 'red', chance: 21.5,  price: 8000, customPrice: 1500000, value: 'eqs' },
        //{ type: 'vehicleMod',   color: 'red', chance: 21.0,  price: 8200, customPrice: 1500000, value: { model: 'eqs', setId: 1, group: 34 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 20.5,  price: 8400, customPrice: 1500000, value: { model: 'eqs', setId: 2, group: 34 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 20.0,  price: 8600, customPrice: 1500000, value: { model: 'eqs', setId: 3, group: 34 } },

        { type: 'vehicle',      color: 'red', chance: 18.5,  price: 8800, customPrice: 1500000, value: 'panamera2' },
        //{ type: 'vehicleMod',   color: 'red', chance: 18.0,  price: 9000, customPrice: 1500000, value: { model: 'panamera2', setId: 1, group: 35 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 17.5,  price: 9200, customPrice: 1500000, value: { model: 'panamera2', setId: 2, group: 35 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 17.0,  price: 9400, customPrice: 1500000, value: { model: 'panamera2', setId: 3, group: 35 } },

        { type: 'vehicle',      color: 'red', chance: 15.5,  price: 9600,  customPrice: 1750000, value: 'lc500' },
        //{ type: 'vehicleMod',   color: 'red', chance: 15.0,  price: 9800,  customPrice: 1750000, value: { model: 'lc500', setId: 1, group: 36 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 14.5,  price: 1000,  customPrice: 1750000, value: { model: 'lc500', setId: 2, group: 36 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 14.0,  price: 10200, customPrice: 1750000, value: { model: 'lc500', setId: 3, group: 36 } },

        { type: 'vehicle',      color: 'red', chance: 12.0,  price: 10400, customPrice: 2000000, value: 'db112' },
        //{ type: 'vehicleMod',   color: 'red', chance: 11.5,  price: 10600, customPrice: 2000000, value: { model: 'db112', setId: 1, group: 37 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 11.0,  price: 10800, customPrice: 2000000, value: { model: 'db112', setId: 2, group: 37 } },

        { type: 'vehicle',      color: 'red', chance: 9.0,  price: 11000, customPrice: 2250000, value: 'bentayga2' },
        //{ type: 'vehicleMod',   color: 'red', chance: 8.5,  price: 11200, customPrice: 2250000, value: { model: 'bentayga2', setId: 1, group: 38 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 8.0,  price: 11400, customPrice: 2250000, value: { model: 'bentayga2', setId: 2, group: 38 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 7.5,  price: 11600, customPrice: 2250000, value: { model: 'bentayga2', setId: 3, group: 38 } },

        { type: 'vehicle',      color: 'red', chance: 5.5,  price: 11800, customPrice: 2500000, value: 'sl' },
        //{ type: 'vehicleMod',   color: 'red', chance: 5.0,  price: 12000, customPrice: 2500000, value: { model: 'sl', setId: 1, group: 39 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 4.5,  price: 12200, customPrice: 2500000, value: { model: 'sl', setId: 2, group: 39 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 4.0,  price: 12400, customPrice: 2500000, value: { model: 'sl', setId: 3, group: 39 } },

        { type: 'vehicle',      color: 'red', chance: 2.0,  price: 12600, customPrice: 3000000, value: 'huracan2' },
        //{ type: 'vehicleMod',   color: 'red', chance: 1.5,  price: 12800, customPrice: 3000000, value: { model: 'huracan2', setId: 1, group: 40 } },
        //{ type: 'vehicleMod',   color: 'red', chance: 1.0,  price: 13000, customPrice: 3000000, value: { model: 'huracan2', setId: 2, group: 40 } },
        // RED - конец

        // GOLD - начало
        { type: 'wheels',       color: 'gold', chance: 2,    price: 11000, value: 239 },
        { type: 'wheels',       color: 'gold', chance: 2,    price: 11000, value: 251 },
        { type: 'wheels',       color: 'gold', chance: 2,    price: 11000, value: 266 },
        { type: 'wheels',       color: 'gold', chance: 2,    price: 11000, value: 278 },
        { type: 'wheels',       color: 'gold', chance: 2,    price: 11000, value: 284 },
        { type: 'wheels',       color: 'gold', chance: 2,    price: 11000, value: 245 },
        { type: 'wheels',       color: 'gold', chance: 2,    price: 11000, value: 257 },
        { type: 'wheels',       color: 'gold', chance: 2,    price: 11000, value: 263 },
        { type: 'wheels',       color: 'gold', chance: 2,    price: 11000, value: 272 },
        { type: 'wheels',       color: 'gold', chance: 2,    price: 11000, value: 290 },


        { type: 'vehicleMod',   color: 'gold', chance: 35.0,  price: 13000, customPrice: 1375000, value: { model: 'levante', setId: 4, group: 41 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 34.5,  price: 13000, customPrice: 1375000, value: { model: 'levante', setId: 5, group: 41 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 34.0,  price: 13000, customPrice: 1375000, value: { model: 'levante', setId: 6, group: 41 } },

        { type: 'vehicleMod',   color: 'gold', chance: 32.0,  price: 14000, customPrice: 1500000, value: { model: 'eqs', setId: 4, group: 42 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 31.5,  price: 14000, customPrice: 1500000, value: { model: 'eqs', setId: 5, group: 42 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 31.0,  price: 14000, customPrice: 1500000, value: { model: 'eqs', setId: 6, group: 42 } },

        { type: 'vehicleMod',   color: 'gold', chance: 29.0,  price: 14500, customPrice: 1500000, value: { model: 'panamera2', setId: 4, group: 43 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 28.5,  price: 14500, customPrice: 1500000, value: { model: 'panamera2', setId: 5, group: 43 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 28.0,  price: 14500, customPrice: 1500000, value: { model: 'panamera2', setId: 6, group: 43 } },

        { type: 'vehicleMod',   color: 'gold', chance: 26.0,  price: 15000, customPrice: 1750000, value: { model: 'lc500', setId: 4, group: 44 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 25.5,  price: 15000, customPrice: 1750000, value: { model: 'lc500', setId: 5, group: 44 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 25.0,  price: 15000, customPrice: 1750000, value: { model: 'lc500', setId: 6, group: 44 } },

        { type: 'vehicleMod',   color: 'gold', chance: 23.0,  price: 15500, customPrice: 2000000, value: { model: 'db112', setId: 3, group: 45 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 22.5,  price: 15500, customPrice: 2000000, value: { model: 'db112', setId: 4, group: 45 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 22.0,  price: 15500, customPrice: 2000000, value: { model: 'db112', setId: 5, group: 45 } },

        { type: 'vehicleMod',   color: 'gold', chance: 20.0,  price: 16000, customPrice: 2250000, value: { model: 'bentayga2', setId: 4, group: 46 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 19.5,  price: 16000, customPrice: 2250000, value: { model: 'bentayga2', setId: 5, group: 46 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 19.0,  price: 16000, customPrice: 2250000, value: { model: 'bentayga2', setId: 6, group: 46 } },

        { type: 'vehicleMod',   color: 'gold', chance: 17.0,  price: 16500, customPrice: 2500000, value: { model: 'sl', setId: 4, group: 47 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 16.5,  price: 16500, customPrice: 2500000, value: { model: 'sl', setId: 5, group: 47 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 16.0,  price: 16500, customPrice: 2500000, value: { model: 'sl', setId: 6, group: 47 } },

        { type: 'vehicleMod',   color: 'gold', chance: 14.0,  price: 17000, customPrice: 4000000, value: { model: 'huracan2', setId: 4, group: 48 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 13.5,  price: 17000, customPrice: 4000000, value: { model: 'huracan2', setId: 5, group: 48 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 13.0,  price: 17000, customPrice: 4000000, value: { model: 'huracan2', setId: 6, group: 48 } },

        { type: 'vehicle',      color: 'gold', chance: 11.0,  price: 17000, customPrice: 3500000, value: 'victoria2' },
        //{ type: 'vehicleMod',   color: 'gold', chance: 10.5,  price: 17000, customPrice: 3500000, value: { model: 'victoria2', setId: 1, group: 49 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 10.0,  price: 17000, customPrice: 3500000, value: { model: 'victoria2', setId: 2, group: 49 } },

        { type: 'vehicle',      color: 'gold', chance: 8.0,  price: 17500, customPrice: 4000000, value: 'j50' },
        //{ type: 'vehicleMod',   color: 'gold', chance: 7.5,  price: 17500, customPrice: 4000000, value: { model: 'j50', setId: 1, group: 50 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 7.0,  price: 17500, customPrice: 4000000, value: { model: 'j50', setId: 2, group: 50 } },

        { type: 'vehicle',      color: 'gold', chance: 5.0,  price: 17500, customPrice: 4500000, value: 'invencible' },
        //{ type: 'vehicleMod',   color: 'gold', chance: 4.5,  price: 17500, customPrice: 4500000, value: { model: 'invencible', setId: 1, group: 51 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 4.0,  price: 17500, customPrice: 4500000, value: { model: 'invencible', setId: 2, group: 51 } },

        { type: 'vehicle',      color: 'gold', chance: 2.0,  price: 18000, customPrice: 5000000, value: 'speedtail' },
        //{ type: 'vehicleMod',   color: 'gold', chance: 1.5,  price: 18000, customPrice: 5000000, value: { model: 'speedtail', setId: 1, group: 52 } },
        //{ type: 'vehicleMod',   color: 'gold', chance: 1.0,  price: 18000, customPrice: 5000000, value: { model: 'speedtail', setId: 2, group: 52 } },
        // GOLD - конец

        // UNIQUE - начало
        { type: 'vehicleMod',   color: 'unique', chance: 25.0,  price: 18500, customPrice: 112000, value: { model: 'golf2', setId: 15, group: 53 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 24.5,  price: 18500, customPrice: 112000, value: { model: 'golf2', setId: 16, group: 53 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 24.0,  price: 18500, customPrice: 112000, value: { model: 'golf2', setId: 17, group: 53 } },

        { type: 'vehicleMod',   color: 'unique', chance: 21.5,  price: 19000, customPrice: 187000, value: { model: 'mx5', setId: 15, group: 54 } },
        { type: 'vehicleMod',   color: 'unique', chance: 21.0,  price: 19000, customPrice: 187000, value: { model: 'mx5', setId: 16, group: 54 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 20.5,  price: 19000, customPrice: 187000, value: { model: 'mx5', setId: 17, group: 54 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 20.0,  price: 19000, customPrice: 187000, value: { model: 'mx5', setId: 18, group: 54 } },

        { type: 'vehicleMod',   color: 'unique', chance: 18.0,  price: 19500, customPrice: 400000, value: { model: 'hummer5',    setId: 2  } },

        { type: 'vehicleMod',   color: 'unique', chance: 16.0,  price: 20000, customPrice: 500000, value: { model: 'countryman', setId: 14, group: 55 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 15.5,  price: 20000, customPrice: 500000, value: { model: 'countryman', setId: 15, group: 55 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 15.0,  price: 20000, customPrice: 500000, value: { model: 'countryman', setId: 16, group: 55 } },

        { type: 'vehicleMod',   color: 'unique', chance: 13.0,  price: 20500, customPrice: 750000, value: { model: 'i82', setId: 15, group: 56 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 12.5,  price: 20500, customPrice: 750000, value: { model: 'i82', setId: 16, group: 56 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 12.0,  price: 20500, customPrice: 750000, value: { model: 'i82', setId: 17, group: 56 } },

        { type: 'vehicleMod',   color: 'unique', chance: 10.0,  price: 21000, customPrice: 1250000, value: { model: 'corvette3',  setId: 2  } },

        { type: 'vehicleMod',   color: 'unique', chance: 8.0,   price: 21500, customPrice: 2000000, value: { model: 'db112', setId: 12, group: 57 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 7.5,   price: 21500, customPrice: 2000000, value: { model: 'db112', setId: 13, group: 57 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 7.0,   price: 21500, customPrice: 2000000, value: { model: 'db112', setId: 14, group: 57 } },

        { type: 'vehicleMod',   color: 'unique', chance: 5.0,   price: 22000, customPrice: 3000000, value: { model: 'huracan2', setId: 25, group: 58 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 4.5,   price: 22000, customPrice: 3000000, value: { model: 'huracan2', setId: 26, group: 58 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 4.0,   price: 22000, customPrice: 3000000, value: { model: 'huracan2', setId: 27, group: 58 } },

        { type: 'vehicle',      color: 'unique', chance: 2.0,   price: 25000, customPrice: 7500000, value: 'tecnomar' },
        //{ type: 'vehicleMod',   color: 'unique', chance: 1.5,   price: 25000, customPrice: 7500000, value: { model: 'tecnomar', setId: 1, group: 59 } },
        //{ type: 'vehicleMod',   color: 'unique', chance: 1.0,   price: 25000, customPrice: 7500000, value: { model: 'tecnomar', setId: 2, group: 59 } },
        // UNIQUE - начало конец
    ]
}
