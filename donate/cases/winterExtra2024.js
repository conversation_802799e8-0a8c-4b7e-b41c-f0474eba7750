module.exports = {
	id: 13,
	title: 'donate.config.cases.winterExtra2024.title',

	start: ['2023-11-27', 'YYYY-MM-DD'],
	end: ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

	// servers: ['TEST0'],
	// seasonPassId: 4,

	// disabledBuy: true,
	readOnly: false,

	price: 500,
	transferDays: 30,

	// discount: 10,

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
		'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

	colors: [
		{
			color: 'gray',
			chance: 67,
			types: [
				{ type: 'clothes', chance: 60 },
				{ type: 'weaponSkin', chance: 30 },
			],
		},
		{
			color: 'blue',
			chance: 18,
			types: [
				{ type: 'clothes', chance: 40 },
				{ type: 'armourSkin', chance: 50 },
				{ type: 'weaponSkin', chance: 60 },
			],
		},
		{
			color: 'purple',
			chance: 7.5,
			types: [
				{ type: 'clothes', chance: 40 },
				{ type: 'weaponSkin', chance: 60 },
			],
		},
		{
			color: 'red',
			chance: 5,
			types: [
				{ type: 'clothes', chance: 40 },
				{ type: 'weaponSkin', chance: 65 },
			],
		},
		{
			color: 'gold',
			chance: 2.5,
			types: [
				{ type: 'clothes', chance: 35 },
				{ type: 'weaponSkin', chance: 60 },
				{ type: 'vehicle', chance: 30 },
				{ type: 'vehicleMod', chance: 25 },
			],
		},
		{
			color: 'unique',
			chance: 0.5,
			types: [{ type: 'clothes', chance: 100 }],
		},
	],

	caseContent: [
		// Clothes
		{ type: 'clothes', color: 'gray', chance: 1.0, price: 100, value: { gender: 1, component: 4, drawable: 2212, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'gray', chance: 1.0, price: 100, value: { gender: 1, component: 6, drawable: 2107, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'gray', chance: 1.0, price: 100, value: { gender: 1, component: 11, drawable: 2399, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'gray', chance: 1.0, price: 100, value: { gender: 0, component: 6, drawable: 2092, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'gray', chance: 1.0, price: 100, value: { gender: 0, component: 4, drawable: 2202, textures: 10, isProp: 0 } },

		{ type: 'clothes', color: 'blue', chance: 1.0, price: 150, value: { gender: 0, component: 6, drawable: 2091, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'blue', chance: 1.0, price: 150, value: { gender: 1, component: 10, drawable: 2096, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'blue', chance: 1.0, price: 150, value: { gender: 0, component: 10, drawable: 2097, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'blue', chance: 1.0, price: 150, value: { gender: 1, component: 6, drawable: 2104, textures: 10, isProp: 0 } },

		{ type: 'clothes', color: 'purple', chance: 1.0, price: 200, value: { gender: 1, component: 6, drawable: 2103, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 200, value: { gender: 1, component: 10, drawable: 2095, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 200, value: { gender: 0, component: 10, drawable: 2096, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 200, value: { gender: 1, component: 4, drawable: 2214, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 200, value: { gender: 1, component: 11, drawable: 2402, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 200, value: { gender: 1, component: 5, drawable: 2060, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 200, value: { gender: 0, component: 5, drawable: 2063, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 200, value: { gender: 0, component: 11, drawable: 2406, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 200, value: { gender: 0, component: 11, drawable: 2405, textures: 10, isProp: 0 } },

		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 1, component: 11, drawable: 2403, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 1, component: 0, drawable: 2088, textures: 10, isProp: 1 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 1, component: 11, drawable: 2400, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 1, component: 11, drawable: 2401, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 1, component: 4, drawable: 2215, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 1, component: 6, drawable: 2106, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 1, component: 6, drawable: 2102, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 1, component: 1, drawable: 2131, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 0, component: 0, drawable: 2087, textures: 10, isProp: 1 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 0, component: 1, drawable: 2145, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 0, component: 0, drawable: 2086, textures: 10, isProp: 1 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 250, value: { gender: 0, component: 11, drawable: 2408, textures: 10, isProp: 0 } },

		{ type: 'clothes', color: 'gold', chance: 1.0, price: 400, value: { gender: 1, component: 4, drawable: 2213, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 400, value: { gender: 0, component: 11, drawable: 2407, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 400, value: { gender: 0, component: 11, drawable: 2404, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 400, value: { gender: 1, component: 1, drawable: 2026, textures: 10, isProp: 1 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 400, value: { gender: 0, component: 1, drawable: 2025, textures: 10, isProp: 1 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 400, value: { gender: 1, component: 6, drawable: 2108, textures: 10, isProp: 0 } },

		{ type: 'clothes', color: 'unique', chance: 1.0, price: 750, value: { gender: 0, component: 5, drawable: 2064, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'unique', chance: 1.0, price: 750, value: { gender: 1, component: 5, drawable: 2061, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'unique', chance: 1.0, price: 750, value: { gender: 1, component: 6, drawable: 2105, textures: 10, isProp: 0 } },

		// armourSkins
		{ type: 'armourSkin', color: 'blue', chance: 0.85, price: 150, value: { entityName: 'light', skinId: 99 } },
		{ type: 'armourSkin', color: 'blue', chance: 0.85, price: 150, value: { entityName: 'light', skinId: 100 } },
		{ type: 'armourSkin', color: 'blue', chance: 0.85, price: 150, value: { entityName: 'light', skinId: 101 } },
		{ type: 'armourSkin', color: 'blue', chance: 0.85, price: 150, value: { entityName: 'light', skinId: 102 } },
		{ type: 'armourSkin', color: 'blue', chance: 0.85, price: 150, value: { entityName: 'light', skinId: 103 } },

		// weaponSkins
		{ type: 'weaponSkin', color: 'gray', chance: 1.0, price: 100, value: { entityName: 'weapon_machinepistol', skinId: 9 } },
		{ type: 'weaponSkin', color: 'gray', chance: 1.0, price: 100, value: { entityName: 'weapon_machinepistol', skinId: 10 } },
		{ type: 'weaponSkin', color: 'blue', chance: 0.95, price: 150, value: { entityName: 'weapon_carbinerifle', skinId: 14 } },
		{ type: 'weaponSkin', color: 'blue', chance: 0.95, price: 150, value: { entityName: 'weapon_carbinerifle', skinId: 15 } },
		{ type: 'weaponSkin', color: 'purple', chance: 0.9, price: 200, value: { entityName: 'weapon_heavyshotgun', skinId: 14 } },
		{ type: 'weaponSkin', color: 'purple', chance: 0.95, price: 200, value: { entityName: 'weapon_revolver', skinId: 14 } },
		{ type: 'weaponSkin', color: 'purple', chance: 0.9, price: 200, value: { entityName: 'weapon_heavyshotgun', skinId: 15 } },
		{ type: 'weaponSkin', color: 'purple', chance: 0.95, price: 200, value: { entityName: 'weapon_revolver', skinId: 15 } },
		{ type: 'weaponSkin', color: 'red', chance: 0.9, price: 250, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 10 } },
		{ type: 'weaponSkin', color: 'red', chance: 0.85, price: 250, value: { entityName: 'weapon_heavysniper', skinId: 5 } },
		{ type: 'weaponSkin', color: 'red', chance: 0.9, price: 250, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 11 } },
		{ type: 'weaponSkin', color: 'red', chance: 0.85, price: 250, value: { entityName: 'weapon_heavysniper', skinId: 6 } },
		{ type: 'weaponSkin', color: 'gold', chance: 0.85, price: 750, value: { entityName: 'weapon_heavysniper_mk2', skinId: 9 } },
		{ type: 'weaponSkin', color: 'gold', chance: 0.9, price: 750, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 11 } },
		{ type: 'weaponSkin', color: 'gold', chance: 0.85, price: 750, value: { entityName: 'weapon_heavysniper_mk2', skinId: 10 } },
		{ type: 'weaponSkin', color: 'gold', chance: 0.9, price: 750, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 12 } },

		// vehicles
		{ type: 'vehicle', color: 'gold', chance: 10, price: 1000, value: 'carrera' },
		//{ type: 'vehicleMod', color: 'gold', chance: 9, price: 1200, value: { model: 'carrera', setId: 1, group: 100 } },
		{ type: 'vehicle', color: 'gold', chance: 8, price: 1500, value: 's2000' },
		//{ type: 'vehicleMod', color: 'gold', chance: 7, price: 1500, value: { model: 's2000', setId: 1, group: 101 } },
		//{ type: 'vehicleMod', color: 'gold', chance: 6, price: 1500, value: { model: 's2000', setId: 2, group: 101 } },
		{ type: 'vehicle', color: 'gold', chance: 5, price: 1500, value: 'q60s' },
		//{ type: 'vehicleMod', color: 'gold', chance: 4, price: 2000, value: { model: 'q60s', setId: 1, group: 102 } },
		//{ type: 'vehicleMod', color: 'gold', chance: 3, price: 2000, value: { model: 'q60s', setId: 2, group: 102 } },
		//{ type: 'vehicleMod', color: 'gold', chance: 2, price: 2000, value: { model: 'q60s', setId: 3, group: 102 } },
	],
};
