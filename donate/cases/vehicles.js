module.exports = {
    id: 2,
    main: true,
    title: 'donate.config.cases.vehicles.title',

    price: 600,
    // discount: 10,
    // servers: ['TEST4', 'TEST0'],
    // transferDays: 10,

    start: ['2023-05-01', 'YYYY-MM-DD'],

    colors: [
        { color: 'gray',   chance: 85  },
        { color: 'blue',   chance: 13  },
        { color: 'purple', chance: 4   },
        { color: 'red',    chance: 2   },
        { color: 'gold',   chance: 0.8 },
        { color: 'unique', chance: 0.05 }
    ],

    caseContent: [
        // gray
        { type: 'vehicle', color: 'gray',  value: 'akuma' },
        { type: 'vehicle', color: 'gray',  value: 'bf400' },
        { type: 'vehicle', color: 'gray',  value: 'lectro' },
        { type: 'vehicle', color: 'gray',  value: 'hexer' },
        { type: 'vehicle', color: 'gray',  value: 'innovation' },
        { type: 'vehicle', color: 'gray',  value: 'emperor2' },
        { type: 'vehicle', color: 'gray',  value: 'stratum' },
        { type: 'vehicle', color: 'gray',  value: 'dilettante' },
        { type: 'vehicle', color: 'gray',  value: 'emperor' },
        { type: 'vehicle', color: 'gray',  value: 'double' },
        { type: 'vehicle', color: 'gray',  value: 'asbo' },
        { type: 'vehicle', color: 'gray',  value: 'bati' },
        { type: 'vehicle', color: 'gray',  value: 'regina' },
        { type: 'vehicle', color: 'gray',  value: 'tornado' },
        { type: 'vehicle', color: 'gray',  value: 'zion' },
        { type: 'vehicle', color: 'gray',  value: 'premier' },
        { type: 'vehicle', color: 'gray',  value: 'primo' },
        { type: 'vehicle', color: 'gray',  value: 'michelli' },
        { type: 'vehicle', color: 'gray',  value: 'hakuchou' },
        { type: 'vehicle', color: 'gray',  value: 'blista' },
        { type: 'vehicle', color: 'gray',  value: 'veto' },
        { type: 'vehicle', color: 'gray',  value: 'surge' },
        { type: 'vehicle', color: 'gray',  value: 'voodoo' },
        { type: 'vehicle', color: 'gray',  value: 'bfinjection' },
        { type: 'vehicle', color: 'gray',  value: 'asea' },
        { type: 'vehicle', color: 'gray',  value: 'tornado2' },
        { type: 'vehicle', color: 'gray',  value: 'virgo' },
        { type: 'vehicle', color: 'gray',  value: 'rebel' },
        { type: 'vehicle', color: 'gray',  value: 'glendale' },
        { type: 'vehicle', color: 'gray',  value: 'washington' },
        { type: 'vehicle', color: 'gray',  value: 'dynasty' },
        { type: 'vehicle', color: 'gray',  value: 'panto' },
        { type: 'vehicle', color: 'gray',  value: 'virgo3' },
        { type: 'vehicle', color: 'gray',  value: 'ingot' },
        { type: 'vehicle', color: 'gray',  value: 'nebula' },
        { type: 'vehicle', color: 'gray',  value: 'retinue' },
        { type: 'vehicle', color: 'gray',  value: 'veto2' },
        { type: 'vehicle', color: 'gray',  value: 'brioso2' },
        { type: 'vehicle', color: 'gray',  value: 'manana2' },
        { type: 'vehicle', color: 'gray',  value: 'peyote' },
        { type: 'vehicle', color: 'gray',  value: 'club' },
        { type: 'vehicle', color: 'gray',  value: 'rhapsody' },
        { type: 'vehicle', color: 'gray',  value: 'asterope' },
        { type: 'vehicle', color: 'gray',  value: 'kanjo' },
        { type: 'vehicle', color: 'gray',  value: 'sentinel' },
        { type: 'vehicle', color: 'gray',  value: 'chino' },
        { type: 'vehicle', color: 'gray',  value: 'picador' },
        { type: 'vehicle', color: 'gray',  value: 'intruder' },
        { type: 'vehicle', color: 'gray',  value: 'stanier' },
        { type: 'vehicle', color: 'gray',  value: 'warrener' },
        { type: 'vehicle', color: 'gray',  value: 'manana' },
        { type: 'vehicle', color: 'gray',  value: 'clubgtr' },
        { type: 'vehicle', color: 'gray',  value: 'hermes' },
        { type: 'vehicle', color: 'gray',  value: 'weevil' },
        { type: 'vehicle', color: 'gray',  value: 'rebel2' },
        { type: 'vehicle', color: 'gray',  value: 'prairie' },
        { type: 'vehicle', color: 'gray',  value: 'brioso' },
        { type: 'vehicle', color: 'gray',  value: 'blade' },
        { type: 'vehicle', color: 'gray',  value: 'impaler' },
        { type: 'vehicle', color: 'gray',  value: 'primo2' },
        { type: 'vehicle', color: 'gray',  value: 'buffalo' },
        { type: 'vehicle', color: 'gray',  value: 'phoenix' },
        { type: 'vehicle', color: 'gray',  value: 'seraph3' },
        { type: 'vehicle', color: 'gray',  value: 'stalion' },
        { type: 'vehicle', color: 'gray',  value: 'rancherxl' },
        { type: 'vehicle', color: 'gray',  value: 'tailgater' },
        { type: 'vehicle', color: 'gray',  value: 'warrener2' },
        { type: 'vehicle', color: 'gray',  value: 'futo' },
        { type: 'vehicle', color: 'gray',  value: 'sentinel2' },
        { type: 'vehicle', color: 'gray',  value: 'clique' },
        { type: 'vehicle', color: 'gray',  value: 'slamvan3' },
        { type: 'vehicle', color: 'gray',  value: 'tampa' },
        { type: 'vehicle', color: 'gray',  value: 'seminole' },
        { type: 'vehicle', color: 'gray',  value: 'glendale2' },
        { type: 'vehicle', color: 'gray',  value: 'schafter2' },
        { type: 'vehicle', color: 'gray',  value: 'nightblade' },
        { type: 'vehicle', color: 'gray',  value: 'buccaneer' },
        { type: 'vehicle', color: 'gray',  value: 'gauntlet3' },
        { type: 'vehicle', color: 'gray',  value: 'sabregt' },
        { type: 'vehicle', color: 'gray',  value: 'virgo2' },
        { type: 'vehicle', color: 'gray',  value: 'fusilade' },
        { type: 'vehicle', color: 'gray',  value: 'pigalle' },
        { type: 'vehicle', color: 'gray',  value: 'zion3' },
        { type: 'vehicle', color: 'gray',  value: 'schwartzerc' },

        // blue
        { type: 'vehicle', color: 'blue',  value: 'vamos' },
        { type: 'vehicle', color: 'blue',  value: 'futo2' },
        { type: 'vehicle', color: 'blue',  value: 'zion2' },
        { type: 'vehicle', color: 'blue',  value: 'buccaneer2' },
        { type: 'vehicle', color: 'blue',  value: 'gauntlet' },
        { type: 'vehicle', color: 'blue',  value: 'riata' },
        { type: 'vehicle', color: 'blue',  value: 'rocoto' },
        { type: 'vehicle', color: 'blue',  value: 'serrano' },
        { type: 'vehicle', color: 'blue',  value: 'baller' },
        { type: 'vehicle', color: 'blue',  value: 'chino2' },
        { type: 'vehicle', color: 'blue',  value: 'seminole2' },
        { type: 'vehicle', color: 'blue',  value: 'issi2' },
        { type: 'vehicle', color: 'blue',  value: 'dukes' },
        { type: 'vehicle', color: 'blue',  value: 'dominator7' },
        { type: 'vehicle', color: 'blue',  value: 'bjxl' },
        { type: 'vehicle', color: 'blue',  value: 'tampa2' },
        { type: 'vehicle', color: 'blue',  value: 'felon' },
        { type: 'vehicle', color: 'blue',  value: 'previon' },
        { type: 'vehicle', color: 'blue',  value: 'gauntlet5' },
        { type: 'vehicle', color: 'blue',  value: 'remus' },
        { type: 'vehicle', color: 'blue',  value: 'fugitive' },
        { type: 'vehicle', color: 'blue',  value: 'scout' },
        { type: 'vehicle', color: 'blue',  value: 'felon2' },
        { type: 'vehicle', color: 'blue',  value: 'dukes3' },
        { type: 'vehicle', color: 'blue',  value: 'yosemite3' },
        { type: 'vehicle', color: 'blue',  value: 'sugoi' },
        { type: 'vehicle', color: 'blue',  value: 'calico' },
        { type: 'vehicle', color: 'blue',  value: 'rapidgt3' },
        { type: 'vehicle', color: 'blue',  value: 'savestra' },
        { type: 'vehicle', color: 'blue',  value: 'cheburek' },
        { type: 'vehicle', color: 'blue',  value: 'stinger' },
        { type: 'vehicle', color: 'blue',  value: 'cavalcade' },
        { type: 'vehicle', color: 'blue',  value: 'patriot' },
        { type: 'vehicle', color: 'blue',  value: 'jackal' },
        { type: 'vehicle', color: 'blue',  value: 'oracle' },
        { type: 'vehicle', color: 'blue',  value: 'faction2' },
        { type: 'vehicle', color: 'blue',  value: 'kamacho' },
        { type: 'vehicle', color: 'blue',  value: 'baller2' },
        { type: 'vehicle', color: 'blue',  value: 'fq2' },
        { type: 'vehicle', color: 'blue',  value: 'rt3000' },
        { type: 'vehicle', color: 'blue',  value: 'dominator' },
        { type: 'vehicle', color: 'blue',  value: 'sabregt2' },
        { type: 'vehicle', color: 'blue',  value: 'dominator8' },
        { type: 'vehicle', color: 'blue',  value: 'greenwood' },
        { type: 'vehicle', color: 'blue',  value: 'sigma2' },
        { type: 'vehicle', color: 'blue',  value: 'mesa' },
        { type: 'vehicle', color: 'blue',  value: 'exemplar' },
        { type: 'vehicle', color: 'blue',  value: 'moonbeam2' },
        { type: 'vehicle', color: 'blue',  value: 'ruiner' },
        { type: 'vehicle', color: 'blue',  value: 'vigero' },
        { type: 'vehicle', color: 'blue',  value: 'granger' },
        { type: 'vehicle', color: 'blue',  value: 'novak' },
        { type: 'vehicle', color: 'blue',  value: 'landstalker2' },
        { type: 'vehicle', color: 'blue',  value: 'schwarzer' },
        { type: 'vehicle', color: 'blue',  value: 'penumbra2' },
        { type: 'vehicle', color: 'blue',  value: 'pentro' },
        { type: 'vehicle', color: 'blue',  value: 'pentro2' },
        { type: 'vehicle', color: 'blue',  value: 'gauntlets' },
        { type: 'vehicle', color: 'blue',  value: 'moonbeam' },
        { type: 'vehicle', color: 'blue',  value: 'xls' },
        { type: 'vehicle', color: 'blue',  value: 'zr350' },
        { type: 'vehicle', color: 'blue',  value: 'cogcabrio' },
        { type: 'vehicle', color: 'blue',  value: 'baller3' },
        { type: 'vehicle', color: 'blue',  value: 'cavalcade2' },
        { type: 'vehicle', color: 'blue',  value: 'dubsta' },
        { type: 'vehicle', color: 'blue',  value: 'huntley' },
        { type: 'vehicle', color: 'blue',  value: 'pentro3' },
        { type: 'vehicle', color: 'blue',  value: 'gresley' },
        { type: 'vehicle', color: 'blue',  value: 'deviant' },
        { type: 'vehicle', color: 'blue',  value: 'nightshade' },
        { type: 'vehicle', color: 'blue',  value: 'landstalker' },
        { type: 'vehicle', color: 'blue',  value: 'flashgt' },
        { type: 'vehicle', color: 'blue',  value: 'gb200' },
        { type: 'vehicle', color: 'blue',  value: 'pentrogpr' },
        { type: 'vehicle', color: 'blue',  value: 'pentrogpr2' },
        { type: 'vehicle', color: 'blue',  value: 'vincent2' },
        { type: 'vehicle', color: 'blue',  value: 's230' },
        { type: 'vehicle', color: 'blue',  value: 'f620' },
        { type: 'vehicle', color: 'blue',  value: 'oracle2' },
        { type: 'vehicle', color: 'blue',  value: 'freecrawler' },
        { type: 'vehicle', color: 'blue',  value: 'sandking2' },
        { type: 'vehicle', color: 'blue',  value: 'buffalo2' },
        { type: 'vehicle', color: 'blue',  value: 'elegy' },
        { type: 'vehicle', color: 'blue',  value: 'sultan3' },

        // purple
        { type: 'vehicle', color: 'purple',  value: 'gauntlet4' },
        { type: 'vehicle', color: 'purple',  value: 'sandking' },
        { type: 'vehicle', color: 'purple',  value: 'cog55' },
        { type: 'vehicle', color: 'purple',  value: 'zionks' },
        { type: 'vehicle', color: 'purple',  value: 'caracara2' },
        { type: 'vehicle', color: 'purple',  value: 'banshee' },
        { type: 'vehicle', color: 'purple',  value: 'dominator3' },
        { type: 'vehicle', color: 'purple',  value: 'hellion' },
        { type: 'vehicle', color: 'purple',  value: 'baller4' },
        { type: 'vehicle', color: 'purple',  value: 'alpha' },
        { type: 'vehicle', color: 'purple',  value: 'rapidgt' },
        { type: 'vehicle', color: 'purple',  value: 'rapidgt2' },
        { type: 'vehicle', color: 'purple',  value: 'sultan' },
        { type: 'vehicle', color: 'purple',  value: 'ferocid' },
        { type: 'vehicle', color: 'purple',  value: 'sultan2' },
        { type: 'vehicle', color: 'purple',  value: 'yosemiteswb' },
        { type: 'vehicle', color: 'purple',  value: 'cognoscenti' },
        { type: 'vehicle', color: 'purple',  value: 'voltic' },
        { type: 'vehicle', color: 'purple',  value: 'everon' },
        { type: 'vehicle', color: 'purple',  value: 'comet2' },
        { type: 'vehicle', color: 'purple',  value: 'contender' },
        { type: 'vehicle', color: 'purple',  value: 'dubsta2' },
        { type: 'vehicle', color: 'purple',  value: 'tailgater2' },
        { type: 'vehicle', color: 'purple',  value: 'jester3' },
        { type: 'vehicle', color: 'purple',  value: 'bison4' },
        { type: 'vehicle', color: 'purple',  value: 'hellion2' },
        { type: 'vehicle', color: 'purple',  value: 'bestiagts' },
        { type: 'vehicle', color: 'purple',  value: 'elegy2' },
        { type: 'vehicle', color: 'purple',  value: 'surano' },
        { type: 'vehicle', color: 'purple',  value: 'euros' },
        { type: 'vehicle', color: 'purple',  value: 'schafter3' },
        { type: 'vehicle', color: 'purple',  value: 'infernus' },
        { type: 'vehicle', color: 'purple',  value: 'raiden' },
        { type: 'vehicle', color: 'purple',  value: 'jackgpr' },
        { type: 'vehicle', color: 'purple',  value: 'feltzer2' },
        { type: 'vehicle', color: 'purple',  value: 'lynx' },
        { type: 'vehicle', color: 'purple',  value: 'ninef' },
        { type: 'vehicle', color: 'purple',  value: 'ninef2' },
        { type: 'vehicle', color: 'purple',  value: 'schafter4' },
        { type: 'vehicle', color: 'purple',  value: 'windsor' },
        { type: 'vehicle', color: 'purple',  value: 'rebla' },
        { type: 'vehicle', color: 'purple',  value: 'stafford' },
        { type: 'vehicle', color: 'purple',  value: 'carbonizzare' },
        { type: 'vehicle', color: 'purple',  value: 'furoregt' },
        { type: 'vehicle', color: 'purple',  value: 'massacro' },
        { type: 'vehicle', color: 'purple',  value: 'massacro2' },
        { type: 'vehicle', color: 'purple',  value: 'growler' },
        { type: 'vehicle', color: 'purple',  value: 'jester4' },
        { type: 'vehicle', color: 'purple',  value: 'bullet' },
        { type: 'vehicle', color: 'purple',  value: 'specter' },
        { type: 'vehicle', color: 'purple',  value: 'zr380c' },
        { type: 'vehicle', color: 'purple',  value: 'vectre' },
        { type: 'vehicle', color: 'purple',  value: 'khamelion' },
        { type: 'vehicle', color: 'purple',  value: 'cypher' },
        { type: 'vehicle', color: 'purple',  value: 'vstr' },
        { type: 'vehicle', color: 'purple',  value: 'drafter' },
        { type: 'vehicle', color: 'purple',  value: 'comet6' },
        { type: 'vehicle', color: 'purple',  value: 'jester5' },
        { type: 'vehicle', color: 'purple',  value: 'specter2' },
        { type: 'vehicle', color: 'purple',  value: 'zr380s' },
        { type: 'vehicle', color: 'purple',  value: 'windsor2' },
        { type: 'vehicle', color: 'purple',  value: 'superd' },
        { type: 'vehicle', color: 'purple',  value: 'jester' },
        { type: 'vehicle', color: 'purple',  value: 'komoda' },
        { type: 'vehicle', color: 'purple',  value: 'sultanrs' },
        { type: 'vehicle', color: 'purple',  value: 'revolter' },
        { type: 'vehicle', color: 'purple',  value: 'pounder3' },
        { type: 'vehicle', color: 'purple',  value: 'jester2' },
        { type: 'vehicle', color: 'purple',  value: 'comet5' },
        { type: 'vehicle', color: 'purple',  value: 'jugular' },
        { type: 'vehicle', color: 'purple',  value: 'neon' },
        { type: 'vehicle', color: 'purple',  value: 'seven70' },
        { type: 'vehicle', color: 'purple',  value: 'coquette4' },
        { type: 'vehicle', color: 'purple',  value: 'gp1' },
        { type: 'vehicle', color: 'purple',  value: 'tigon' },

        // red
        { type: 'vehicle', color: 'red',     value: 'coquette' },
        { type: 'vehicle', color: 'red',     value: 'schlagen' },
        { type: 'vehicle', color: 'red',     value: 'omnisegt' },
        { type: 'vehicle', color: 'red',     value: 'adder' },
        { type: 'vehicle', color: 'red',     value: 'banshee2' },
        { type: 'vehicle', color: 'red',     value: 'cyclone' },
        { type: 'vehicle', color: 'red',     value: 'osiris' },
        { type: 'vehicle', color: 'red',     value: 'tyrus' },
        { type: 'vehicle', color: 'red',     value: 'zorrusso' },
        { type: 'vehicle', color: 'red',     value: 'tenf' },
        { type: 'vehicle', color: 'red',     value: 'neo' },
        { type: 'vehicle', color: 'red',     value: 'pariah' },
        { type: 'vehicle', color: 'red',     value: 'corsita' },
        { type: 'vehicle', color: 'red',     value: 'cheetah' },
        { type: 'vehicle', color: 'red',     value: 'pfister811' },
        { type: 'vehicle', color: 'red',     value: 'tempesta' },
        { type: 'vehicle', color: 'red',     value: 'turismor' },
        { type: 'vehicle', color: 'red',     value: 'xa21' },
        { type: 'vehicle', color: 'red',     value: 'tenf2' },
        { type: 'vehicle', color: 'red',     value: 'paragon' },
        { type: 'vehicle', color: 'red',     value: 'sm722' },
        { type: 'vehicle', color: 'red',     value: 'reaper' },
        { type: 'vehicle', color: 'red',     value: 't20' },
        { type: 'vehicle', color: 'red',     value: 'autarch' },
        { type: 'vehicle', color: 'red',     value: 'emerus' },
        { type: 'vehicle', color: 'red',     value: 'furia' },
        { type: 'vehicle', color: 'red',     value: 'tyrant' },
        { type: 'vehicle', color: 'red',     value: 'visione' },
        { type: 'vehicle', color: 'red',     value: 'nero' },
        { type: 'vehicle', color: 'red',     value: 'nspeedo' },
        { type: 'vehicle', color: 'red',     value: 'italigto' },
        { type: 'vehicle', color: 'red',     value: 'nero2' },
        { type: 'vehicle', color: 'red',     value: 'torero2' },
        { type: 'vehicle', color: 'red',     value: 'thrax' },
        { type: 'vehicle', color: 'red',     value: 'italirsx' },
        { type: 'vehicle', color: 'red',     value: 'lm87' },
        { type: 'vehicle', color: 'red',     value: 'zentorno' },
        { type: 'vehicle', color: 'red',     value: 'matiz' },
        { type: 'vehicle', color: 'red',     value: 'priora' },
        { type: 'vehicle', color: 'red',     value: 'urban' },
        { type: 'vehicle', color: 'red',     value: 'vesta' },
        { type: 'vehicle', color: 'red',     value: 'samara' },
        { type: 'vehicle', color: 'red',     value: 'vaz2107' },
        { type: 'vehicle', color: 'red',     value: 'octavia18' },
        { type: 'vehicle', color: 'red',     value: 'bmwe38' },
        { type: 'vehicle', color: 'red',     value: 's600' },
        { type: 'vehicle', color: 'red',     value: 'mjc' },
        { type: 'vehicle', color: 'red',     value: 'camry2' },
        { type: 'vehicle', color: 'red',     value: 'vclass' },
        { type: 'vehicle', color: 'red',     value: 'bmwe39' },
        { type: 'vehicle', color: 'red',     value: 'w210' },
        { type: 'vehicle', color: 'red',     value: 'golf7r' },
        { type: 'vehicle', color: 'red',     value: 'focusrs' },
        { type: 'vehicle', color: 'red',     value: 'impala' },
        { type: 'vehicle', color: 'red',     value: 'touareg2' },
        { type: 'vehicle', color: 'red',     value: 'dsprinter' },
        { type: 'vehicle', color: 'red',     value: 'nisgtr' },
        { type: 'vehicle', color: 'red',     value: 'subwrx' },
        { type: 'vehicle', color: 'red',     value: 'msprinter' },
        { type: 'vehicle', color: 'red',     value: 'camaro2' },
        { type: 'vehicle', color: 'red',     value: 'v4sp' },
        { type: 'vehicle', color: 'red',     value: '16challenger' },
        { type: 'vehicle', color: 'red',     value: 'camry70' },
        { type: 'vehicle', color: 'red',     value: 'accord' },
        { type: 'vehicle', color: 'red',     value: 'mustang2' },
        { type: 'vehicle', color: 'red',     value: 'mark2' },
        { type: 'vehicle', color: 'red',     value: 'velar' },
        { type: 'vehicle', color: 'red',     value: 'brutale' },
        { type: 'vehicle', color: 'red',     value: 's15' },
        { type: 'vehicle', color: 'red',     value: 'a80' },
        { type: 'vehicle', color: 'red',     value: '370z' },

        // gold
        { type: 'vehicle', color: 'gold',    value: '718bs' },
        { type: 'vehicle', color: 'gold',    value: 'rx7' },
        { type: 'vehicle', color: 'gold',    value: 'supragr' },
        { type: 'vehicle', color: 'gold',    value: 'touareg' },
        { type: 'vehicle', color: 'gold',    value: 'tahoe2' },
        { type: 'vehicle', color: 'gold',    value: 'bentaygast' },
        { type: 'vehicle', color: 'gold',    value: 'amggt' },
        { type: 'vehicle', color: 'gold',    value: 'cls63s' },
        { type: 'vehicle', color: 'gold',    value: 'evo9' },
        { type: 'vehicle', color: 'gold',    value: 'm5e60' },
        { type: 'vehicle', color: 'gold',    value: 'c300' },
        { type: 'vehicle', color: 'gold',    value: 'kiastinger' },
        { type: 'vehicle', color: 'gold',    value: 'bmwg20' },
        { type: 'vehicle', color: 'gold',    value: '2020mustang' },
        { type: 'vehicle', color: 'gold',    value: 'camaro21' },
        { type: 'vehicle', color: 'gold',    value: 'ram' },
        { type: 'vehicle', color: 'gold',    value: 'm5comp' },
        { type: 'vehicle', color: 'gold',    value: 'e63s' },
        { type: 'vehicle', color: 'gold',    value: 'lc200' },
        { type: 'vehicle', color: 'gold',    value: 'x5me70' },
        { type: 'vehicle', color: 'gold',    value: 'rs7' },
        { type: 'vehicle', color: 'gold',    value: 'evo10' },
        { type: 'vehicle', color: 'gold',    value: 'rrab' },
        { type: 'vehicle', color: 'gold',    value: 'lex570' },
        { type: 'vehicle', color: 'gold',    value: 'fx50s' },
        { type: 'vehicle', color: 'gold',    value: 'panamera17turbo' },
        { type: 'vehicle', color: 'gold',    value: 'm8gc' },
        { type: 'vehicle', color: 'gold',    value: 'charger20' },
        { type: 'vehicle', color: 'gold',    value: 'gsx1000' },
        { type: 'vehicle', color: 'gold',    value: 'cesc21' },
        { type: 'vehicle', color: 'gold',    value: 'modelx' },
        { type: 'vehicle', color: 'gold',    value: 'skyline' },
        { type: 'vehicle', color: 'gold',    value: 's63cab' },
        { type: 'vehicle', color: 'gold',    value: 'g63' },
        { type: 'vehicle', color: 'gold',    value: 'ghost' },
        { type: 'vehicle', color: 'gold',    value: 'ast' },
        { type: 'vehicle', color: 'gold',    value: 'corvett08' },
        { type: 'vehicle', color: 'gold',    value: 'i8' },
        { type: 'vehicle', color: 'gold',    value: 'f150' },
        { type: 'vehicle', color: 'gold',    value: '63gls2' },
        { type: 'vehicle', color: 'gold',    value: 'pts21' },
        { type: 'vehicle', color: 'gold',    value: 'continental' },
        { type: 'vehicle', color: 'gold',    value: 'macan' },
        { type: 'vehicle', color: 'gold',    value: 'rs6' },
        { type: 'vehicle', color: 'gold',    value: 'lc300' },
        { type: 'vehicle', color: 'gold',    value: 'x5g05' },
        { type: 'vehicle', color: 'gold',    value: 'z4b' },
        { type: 'vehicle', color: 'gold',    value: 'jgc' },
        { type: 'vehicle', color: 'gold',    value: 'm4comp' },
        { type: 'vehicle', color: 'gold',    value: 'rs5' },
        { type: 'vehicle', color: 'gold',    value: 'nsx' },
        { type: 'vehicle', color: 'gold',    value: 'huracan' },
        { type: 'vehicle', color: 'gold',    value: 'm850' },
        { type: 'vehicle', color: 'gold',    value: 'g55' },
        { type: 'vehicle', color: 'gold',    value: 'r820' },
        { type: 'vehicle', color: 'gold',    value: 'm850' },
        { type: 'vehicle', color: 'gold',    value: 'urus' },
        { type: 'vehicle', color: 'gold',    value: 'gle63' },
        { type: 'vehicle', color: 'gold',    value: 'ftype' },
        { type: 'vehicle', color: 'gold',    value: 'x6m' },
        { type: 'vehicle', color: 'gold',    value: 'xes' },
        { type: 'vehicle', color: 'gold',    value: 'viper' },
        { type: 'vehicle', color: 'gold',    value: 's63w222' },
        { type: 'vehicle', color: 'gold',    value: 'bmwm7' },
        { type: 'vehicle', color: 'gold',    value: 'asvj' },
        { type: 'vehicle', color: 'gold',    value: 'g63amg6x6' },
        { type: 'vehicle', color: 'gold',    value: 'cullinan' },
        { type: 'vehicle', color: 'gold',    value: 'mp1' },
        { type: 'vehicle', color: 'gold',    value: '63gls' },
        { type: 'vehicle', color: 'gold',    value: 'cayen19' },
        { type: 'vehicle', color: 'gold',    value: 'bmwx7' },
        { type: 'vehicle', color: 'gold',    value: 'rs72' },
        { type: 'vehicle', color: 'gold',    value: 'chiron19' },
        { type: 'vehicle', color: 'gold',    value: 'laferrari' },
        { type: 'vehicle', color: 'gold',    value: 'gt63s' },
        { type: 'vehicle', color: 'gold',    value: 'x6m2' },
        { type: 'vehicle', color: 'gold',    value: 'q8' },
        { type: 'vehicle', color: 'gold',    value: 'sclass3' },
        { type: 'vehicle', color: 'gold',    value: 'models' },
        { type: 'vehicle', color: 'gold',    value: 'reventon' },
        { type: 'vehicle', color: 'gold',    value: 'vulcan' },
        { type: 'vehicle', color: 'gold',    value: 'etron' },
        { type: 'vehicle', color: 'gold',    value: 'taycan' },
        { type: 'vehicle', color: 'gold',    value: '488pista' },
        { type: 'vehicle', color: 'gold',    value: 'veyron' },
        { type: 'vehicle', color: 'gold',    value: 'bdivo' },
        { type: 'vehicle', color: 'gold',    value: 'ff12' },
        { type: 'vehicle', color: 'gold',    value: 'countach' },
        { type: 'vehicle', color: 'gold',    value: 'gemera' },
        { type: 'vehicle', color: 'gold',    value: 'rrphantom' },
        { type: 'vehicle', color: 'gold',    value: '720s' },
        { type: 'vehicle', color: 'gold',    value: 'jesko20' },
        { type: 'vehicle', color: 'gold',    value: '918s' },
        { type: 'vehicle', color: 'gold',    value: 'imola' },
        { type: 'vehicle', color: 'gold',    value: 'amgone' },
        { type: 'vehicle', color: 'gold',    value: '5series' },

        // unique
        { type: 'vehicle', color: 'unique',  value: 'xclass' },
        { type: 'vehicle', color: 'unique',  value: 'essenza' },
        { type: 'vehicle', color: 'unique',  value: 'bolide' },
        { type: 'vehicle', color: 'unique',  value: 'gclass4' },
    ]
}
