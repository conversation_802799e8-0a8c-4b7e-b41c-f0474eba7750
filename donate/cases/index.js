import autumn2023Case from './autumn2023';
import autumn2024Case from './autumn2024';
import defaultCase from './default';
import halloween2023Case from './halloween2023';
import halloween2024Case from './halloween2024';
import summer2022Case from './summer2022';
import summer2023Case from './summer2023';
import summerSkins2023Case from './summerSkins2023';
import vehiclesCase from './vehicles';
import winter2023Case from './winter2023';
import winter2024Case from './winter2024';
import winter2025Case from './winter2025';
import winterExtra2024Case from './winterExtra2024';
import winterExtra2025Case from './winterExtra2025';
import winterVehicles2024Case from './winterVehicles2024';
import winterVehicles2025Case from './winterVehicles2025';
import toys2024Case from './toys2024';
import spring2024Case from './spring2024';
import summer2024Case from './summer2024';
import summerExtra2024Case from './summerExtra2024';
import summerVehicles2024Case from './summerVehicles2024';
import empty from './empty';
import spring2025Case from './spring2025';

export const casesConfig = {
	autumn2023: autumn2023Case,
	autumn2024: autumn2024Case,
	default: defaultCase,
	halloween2023: halloween2023Case,
	halloween2024: halloween2024Case,
	summer2022: summer2022Case,
	summer2023: summer2023Case,
	summerSkins2023: summerSkins2023Case,
	vehicles: vehiclesCase,
	winter2023: winter2023Case,
	winter2024: winter2024Case,
	winter2025: winter2025Case,
	winterExtra2024: winterExtra2024Case,
	winterExtra2025: winterExtra2025Case,
	winterVehicles2024: winterVehicles2024Case,
	winterVehicles2025: winterVehicles2025Case,
	toys2024: toys2024Case,
	spring2024: spring2024Case,
	spring2025: spring2025Case,
	summer2024: summer2024Case,
	summerExtra2024: summerExtra2024Case,
	summerVehicles2024: summerVehicles2024Case,
	empty: empty,  // Этот кейс для того чтобы можно было
};
