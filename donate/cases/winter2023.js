module.exports = {
    id: 4,
    title: 'donate.config.cases.winter2023.title',
    // discount: 10,

    start: ['2022-11-01', 'YYYY-MM-DD'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    // servers: ['TEST0'],

    seasonPassId: 2,

    // Сумма распыления указан в опыте батлпаса. Если батлпас активен
    sellRule: 40, // Если батлпас уже не активен, то задать процент конвертации price в коины. Например 100 exp / 100 * 25 = 25 коинов

    price: 750,
    transferDays: 30,

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    colors: [
        {
            color: 'gray',
            chance: 67,
            types: [
                { type: 'item',            chance: 50 },
                { type: 'tattoo',          chance: 40 },
                { type: 'wheels',          chance: 40 },
                { type: 'money',           chance: 55 },
                { type: 'coins',           chance: 55 },
                { type: 'vehicleDiscount', chance: 30 },
            ],
        },
        {
            color: 'blue',
            chance: 18,
            types: [
                { type: 'item',            chance: 45 },
                { type: 'vehicle',         chance: 45 },
                { type: 'tattoo',          chance: 40 },
                { type: 'animation',       chance: 45 },
                { type: 'wheels',          chance: 30 },
                { type: 'clothes',         chance: 55 },
                { type: 'vehicleDiscount', chance: 30 },
            ],
        },
        {
            color: 'purple',
            chance: 7.5,
            types: [
                { type: 'item',            chance: 50 },
                { type: 'tattoo',          chance: 45 },
                { type: 'animation',       chance: 45 },
                { type: 'vehicleSet',      chance: 30 },
                { type: 'vehicleDiscount', chance: 35 },
                { type: 'wheels',          chance: 30 },
                { type: 'clothes',         chance: 40 },
            ],
        },
        {
            color: 'red',
            chance: 5,
            types: [
                { type: 'vehicleDiscount', chance: 25 },
                { type: 'item',            chance: 30 },
                // { type: 'status',          chance: 30 },
                { type: 'animation',       chance: 45 },
                { type: 'wheels',          chance: 40 },
                { type: 'vehicleSet',      chance: 30 },
                { type: 'clothes',         chance: 20 },
            ],
        },
        {
            color: 'gold',
            chance: 2.5,
            types: [
                { type: 'vehicleDiscount', chance: 25 },
                { type: 'animation',       chance: 30 },
                { type: 'vehicleSet',      chance: 30 },
                { type: 'clothes',         chance: 30 },
                { type: 'vehicle',         chance: 32 },
            ],
        }
    ],

    caseContent: [
        // gray
        { type: 'item',            color: 'gray',   chance: 90,   price: 150,    value: { itemId: 89,  count: 10 } }, // Желтая аптечка (хил +75хп)
        { type: 'item',            color: 'gray',   chance: 90,   price: 150,    value: { itemId: 625, count: 10 } }, // Адреналин (= Эпинефрин)
        { type: 'item',            color: 'gray',   chance: 90,   price: 150,    value: { itemId: 444, count: 0 } }, // Ключ карта Fleeca
        { type: 'item',            color: 'gray',   chance: 90,   price: 150,    value: { itemId: 427, count: 3 } }, // Маленький подарок

        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3189, 6252] },
        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3190, 6253] },
        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3191, 6255] },
        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3192, 6258] },
        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3193, 6257] },
        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3194, 6261] },
        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3195, 6260] },
        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3196, 6254] },
        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3197, 6250] },
        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3202, 6256] },
        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3203, 6251] },
        { type: 'tattoo',          color: 'gray',   chance: 70,   price: 200,    value: [3208, 6259] },

        { type: 'wheels',          color: 'gray',   chance: 60,   price: 200,    value: 16 },
        { type: 'wheels',          color: 'gray',   chance: 60,   price: 200,    value: 17 },
        { type: 'wheels',          color: 'gray',   chance: 60,   price: 200,    value: 18 },
        { type: 'wheels',          color: 'gray',   chance: 60,   price: 200,    value: 19 },
        { type: 'wheels',          color: 'gray',   chance: 60,   price: 200,    value: 20 },
        { type: 'wheels',          color: 'gray',   chance: 60,   price: 200,    value: 21 },
        { type: 'wheels',          color: 'gray',   chance: 60,   price: 200,    value: 22 },
        { type: 'wheels',          color: 'gray',   chance: 60,   price: 200,    value: 23 },

        { type: 'money',           color: 'gray',   chance: 80,   price: 250,    value: 25000 },

        { type: 'coins',           color: 'gray',   chance: 60,   price: 250,    value: 250 },

        { type: 'vehicleDiscount', color: 'gray',   chance: 50,   price: 250,    value: 5 },

        // blue
        { type: 'item',            color: 'blue',   chance: 90,   price: 250,    value: { itemId: 428, count: 1 } }, // Средний новогодний подарок

        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'brioso3',   customPrice: 50000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'ruiner4',   customPrice: 175000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'greenwood', customPrice: 95000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'sentinel4', customPrice: 90000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'weevil2',   customPrice: 100000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'vigero2',   customPrice: 200000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'rhinehart', customPrice: 55000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'draugur',   customPrice: 350000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'tenf',      customPrice: 475000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'tenf2',     customPrice: 525000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'sm722',     customPrice: 550000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'corsita',   customPrice: 500000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'lm87',      customPrice: 1000000 },
        { type: 'vehicle',         color: 'blue',   chance: 60,   price: 250,    value: 'torero2',   customPrice: 900000 },

        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3220, 6286] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3221, 6287] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3222, 6288] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3223, 6289] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3224, 6290] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3225, 6291] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3226, 6292] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3227, 6293] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3228, 6294] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3229, 6295] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3230, 6296] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3231, 6297] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3232, 6298] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3233, 6299] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3209, 6275] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3210, 6276] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3211, 6277] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3212, 6278] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3213, 6279] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3215, 6281] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3216, 6282] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3217, 6283] },
        { type: 'tattoo',          color: 'blue',   chance: 60,   price: 250,    value: [3218, 6284] },

        { type: 'animation',       color: 'blue',   chance: 55,   price: 275,    value: 159 }, // Низкий бесплатный
        { type: 'animation',       color: 'blue',   chance: 55,   price: 275,    value: 52  }, // Не крутой танец, бесплатный
        { type: 'animation',       color: 'blue',   chance: 55,   price: 275,    value: 56  }, // Не крутой танец, бесплатный/премиум низкий лвл
        { type: 'animation',       color: 'blue',   chance: 55,   price: 275,    value: 54  }, // Не крутой танец, бесплатный
        { type: 'animation',       color: 'blue',   chance: 55,   price: 275,    value: 55  }, // Средний танец, бесплатный/премиум
        { type: 'animation',       color: 'blue',   chance: 55,   price: 275,    value: 107 }, // Средний бесплатный

        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 24 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 25 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 26 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 27 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 28 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 29 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 30 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 31 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 32 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 33 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 34 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 35 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 36 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 37 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 38 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 39 },
        { type: 'wheels',          color: 'blue',   chance: 50,   price: 275,    value: 40 },


        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 11, drawable: 2267, textures: 4, isProp: 0 }, // Худи с рубашкой
                { gender: 1, component: 6,  drawable: 2065, textures: 9, isProp: 0 }  // Высокие ботинки
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 1,  drawable: 2006, textures: 5, isProp: 1 }, // Новогодние очки
                { gender: 1, component: 1,  drawable: 2006, textures: 5, isProp: 1 }  // Новогодние очки
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 4,  drawable: 2153, textures: 4, isProp: 0 }, // Джинсы
                { gender: 1, component: 4,  drawable: 2159, textures: 7, isProp: 0 }  // Джинсы
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 11, drawable: 2265, textures: 2, isProp: 0 },  // Худи с крестиком
                { gender: 1, component: 11, drawable: 2271, textures: 2, isProp: 0 }   // Худи с крестиком
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 1,  drawable: 2007, textures: 8, isProp: 1 },  // Новогодние очки снежинка
                { gender: 1, component: 1,  drawable: 2007, textures: 8, isProp: 1 }   // Новогодние очки снежинка
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 11, drawable: 2268, textures: 10, isProp: 0 },  // Свитшот Puma
                { gender: 1, component: 6,  drawable: 2066, textures: 8, isProp: 0 }    // Замшевые ботинки
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 0,  drawable: 2046, textures: 7, isProp: 1 },  // Наушники
                { gender: 1, component: 0,  drawable: 2055, textures: 7, isProp: 1 }   // Наушники
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 6,  drawable: 2060, textures: 10, isProp: 0 },  // Уги с кроликом
                { gender: 1, component: 6,  drawable: 2067, textures: 10, isProp: 0 }   // Уги с кроликом
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 0,  drawable: 2045, textures: 10, isProp: 1 },  // Шапка
                { gender: 1, component: 0,  drawable: 2054, textures: 10, isProp: 1 }   // Шапка
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 11, drawable: 2259, textures: 5, isProp: 0 },  // Свитер Фред Пери
                { gender: 1, component: 11, drawable: 2270, textures: 4, isProp: 0 }   // Жилет Moncler
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 11, drawable: 2266, textures: 4, isProp: 0 },  // Худи в духе Burbery
                { gender: 1, component: 11, drawable: 2272, textures: 4, isProp: 0 }   // Худи в духе Burbery
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 0,  drawable: 2042, textures: 45, isProp: 1 },  // Горнолыжные очки на голову
                { gender: 1, component: 0,  drawable: 2050, textures: 45, isProp: 1 }   // Горнолыжные очки на голову
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 11, drawable: 2263, textures: 9, isProp: 0 },  // Худи Dolce & Gabbana
                { gender: 1, component: 6,  drawable: 2064, textures: 10, isProp: 0 }  // Глянцевые ботинки
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 0,  drawable: 2032, textures: 1, isProp: 1 },  // Рога оленя (Lum)
                { gender: 1, component: 0,  drawable: 2039, textures: 1, isProp: 1 }   // Рога оленя (Lum)
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 0,  drawable: 2040, textures: 6, isProp: 1 },  // Новогодний монокль
                { gender: 1, component: 0,  drawable: 2047, textures: 6, isProp: 1 }   // Новогодний монокль
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 1,  drawable: 2009, textures: 15, isProp: 1 },  // Горнолыжные очки
                { gender: 1, component: 1,  drawable: 2009, textures: 15, isProp: 1 }   // Горнолыжные очки
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 11, drawable: 2261, textures: 15, isProp: 0 }, // Тканиевая куртка 2
                { gender: 1, component: 11, drawable: 2274, textures: 4, isProp: 0 }   // Худи Gucci
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 0,  drawable: 2034, textures: 3, isProp: 1 },  // Рога оленя красные (Lum)
                { gender: 1, component: 0,  drawable: 2041, textures: 3, isProp: 1 }   // Рога оленя красные (Lum)
            ]},
        { type: 'clothes',         color: 'blue',   chance: 40,   price: 275,    value: [
                { gender: 0, component: 11, drawable: 2272, textures: 4, isProp: 0 },  // Ветровка
                { gender: 1, component: 0,  drawable: 2056, textures: 13, isProp: 1 }  // Шапка с котиком
            ]},

        { type: 'vehicleDiscount', color: 'blue',   chance: 40,   price: 300,    value: 10 },

        // purple
        { type: 'item',            color: 'purple', chance: 70,    price: 350,    value: { itemId: 429, count: 1 } }, // Большой новогодний подарок
        { type: 'item',            color: 'purple', chance: 70,    price: 350,    value: { itemId: 333, count: 0 } }, // Ручной пулемет Мk2

        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3184, 6264] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3185, 6263] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3186, 6266] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3187, 6265] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3188, 6262] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3198, 6269] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3199, 6270] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3200, 6274] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3201, 6267] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3204, 6271] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3205, 6273] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3206, 6272] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3207, 6268] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3214, 6280] },
        { type: 'tattoo',          color: 'purple', chance: 50,    price: 350,   value: [3219, 6285] },

        { type: 'animation',       color: 'purple', chance: 50,    price: 350,   value: 162 }, // Средний премиу
        { type: 'animation',       color: 'purple', chance: 50,    price: 350,   value: 53  }, // Средний танец, премиум
        { type: 'animation',       color: 'purple', chance: 50,    price: 350,   value: 75  }, // Средний танец, премиум/бесплатный

        { type: 'vehicleSet', color: 'purple', chance: 30, price: 350, value: { model: 'm850', setId: 4, group: 39 } },
        { type: 'vehicleSet', color: 'purple', chance: 30, price: 350, value: { model: 'nisgtr', setId: 9, group: 40 } },
        { type: 'vehicleSet', color: 'purple', chance: 30, price: 350, value: { model: 'm4comp', setId: 4, group: 41 } },
        { type: 'vehicleSet', color: 'purple', chance: 30, price: 350, value: { model: 'm3e46', setId: 4, group: 42 } },
        { type: 'vehicleSet', color: 'purple', chance: 30, price: 350, value: { model: 'm4f82', setId: 4, group: 43 } },
        { type: 'vehicleSet', color: 'purple', chance: 30, price: 350, value: { model: 'm4f82', setId: 5, group: 43 } },
        { type: 'vehicleSet', color: 'purple', chance: 30, price: 350, value: { model: 'm4f82', setId: 6, group: 43 } },
        { type: 'vehicleSet', color: 'purple', chance: 30, price: 350, value: { model: 'm4f82', setId: 7, group: 43 } },
        { type: 'vehicleSet', color: 'purple', chance: 30, price: 350, value: { model: 'bentaygast', setId: 4, group: 44 } },
        { type: 'vehicleSet', color: 'purple', chance: 30, price: 350, value: { model: 'amggt', setId: 4, group: 45 } },
        { type: 'vehicleSet', color: 'purple', chance: 30, price: 350, value: { model: 'rs6', setId: 6, group: 46 } },

        { type: 'vehicleDiscount', color: 'purple', chance: 35,    price: 350,   value: 15 },

        { type: 'wheels',          color: 'purple', chance: 25,    price: 350,   value: 15 },
        { type: 'wheels',          color: 'purple', chance: 25,    price: 350,   value: 41 },
        { type: 'wheels',          color: 'purple', chance: 25,    price: 350,   value: 42 },
        { type: 'wheels',          color: 'purple', chance: 25,    price: 350,   value: 43 },
        { type: 'wheels',          color: 'purple', chance: 25,    price: 350,   value: 44 },
        { type: 'wheels',          color: 'purple', chance: 25,    price: 350,   value: 45 },
        { type: 'wheels',          color: 'purple', chance: 25,    price: 350,   value: 46 },
        { type: 'wheels',          color: 'purple', chance: 25,    price: 350,   value: 47 },
        { type: 'wheels',          color: 'purple', chance: 25,    price: 350,   value: 48 },
        { type: 'wheels',          color: 'purple', chance: 25,    price: 350,   value: 49 },


        { type: 'clothes',         color: 'purple', chance: 20,    price: 350,   value: [
                { gender: 0, component: 4,  drawable: 2154, textures: 4, isProp: 0 }, // Джоггеры Jordan
                { gender: 1, component: 4,  drawable: 2162, textures: 4, isProp: 0 }  // Джоггеры The Noth Face
            ]},
        { type: 'clothes',         color: 'purple', chance: 20,    price: 350,   value: [
                { gender: 0, component: 11, drawable: 2255, textures: 7, isProp: 0 },  // Худи Puma
                { gender: 1, component: 11, drawable: 2266, textures: 9, isProp: 0 }   // Цветной свитер
            ]},
        { type: 'clothes',         color: 'purple', chance: 20,    price: 350,   value: [
                { gender: 0, component: 11, drawable: 2256, textures: 11, isProp: 0 },  // Свитер Stone Island
                { gender: 1, component: 11, drawable: 2267, textures: 6, isProp: 0 }    // Топик + юбка
            ]},
        { type: 'clothes',         color: 'purple', chance: 20,    price: 350,   value: [
                { gender: 0, component: 11, drawable: 2262, textures: 20, isProp: 0 }, // Кимоно
                { gender: 1, component: 4,  drawable: 2161, textures: 4, isProp: 0 }   // Юбка Alexander Wang
            ]},
        { type: 'clothes',         color: 'purple', chance: 20,    price: 350,   value: [
                { gender: 0, component: 1,  drawable: 2008, textures: 7, isProp: 1 },  // Новогодние очки снежинка (Lum)
                { gender: 1, component: 1,  drawable: 2008, textures: 7, isProp: 1 }   // Новогодние очки снежинка (Lum)
            ]},
        { type: 'clothes',         color: 'purple', chance: 20,    price: 350,   value: [
                { gender: 0, component: 11, drawable: 2271, textures: 4, isProp: 0 },  // Свитшот Dior
                { gender: 1, component: 4,  drawable: 2160, textures: 4, isProp: 0 }   // Джинсы Gucci
            ]},
        { type: 'clothes',         color: 'purple', chance: 20,    price: 350,   value: [
                { gender: 0, component: 0,  drawable: 2041, textures: 9, isProp: 1 },  // Наушники мишки
                { gender: 1, component: 0,  drawable: 2048, textures: 9, isProp: 1 }   // Наушники мишки
            ]},
        { type: 'clothes',         color: 'purple', chance: 20,    price: 350,   value: [
                { gender: 0, component: 11, drawable: 2269, textures: 9, isProp: 0 },  // Пуховик
                { gender: 1, component: 6,  drawable: 2068, textures: 10, isProp: 0 }  // Меховые ботинки
            ]},
        { type: 'clothes',         color: 'purple', chance: 20,    price: 350,   value: [
                { gender: 0, component: 4,  drawable: 2152, textures: 15, isProp: 0 },  // Классические шорты
                { gender: 1, component: 4,  drawable: 2158, textures: 9, isProp: 0 }    // Брюки
            ]},
        { type: 'clothes',         color: 'purple', chance: 20,    price: 350,   value: [
                { gender: 0, component: 0,  drawable: 2035, textures: 1, isProp: 1 },  // Новогодний Mini колпак
                { gender: 1, component: 0,  drawable: 2042, textures: 1, isProp: 1 }   // Новогодний Mini колпак
            ]},
        { type: 'clothes',         color: 'purple', chance: 20,    price: 350,   value: [
                { gender: 0, component: 7,  drawable: 2063, textures: 2, isProp: 0 },  // Белые бусы
                { gender: 1, component: 7,  drawable: 2051, textures: 2, isProp: 0 }   // Белые бусы
            ]},


        // red
        { type: 'vehicleDiscount', color: 'red',    chance: 90,    price: 600,   value: 20 },

        { type: 'item',            color: 'red',    chance: 80,    price: 600,   value: { itemId: 328, count: 0 } }, // Тяжелая снайперская винтовка Mk2

        // { type: 'status',          color: 'red',    chance: 70,    price: 700,   value: { type: 'Platinum', days: 90 } },

        { type: 'animation',       color: 'red',    chance: 60,    price: 600,   value: 116 }, // Топовый бесплатный
        { type: 'animation',       color: 'red',    chance: 60,    price: 600,   value: 69  }, // Крутой танец, премиум либо бесплатный но высокий уровень
        { type: 'animation',       color: 'red',    chance: 60,    price: 600,   value: 160 }, // Топовый платный
        { type: 'animation',       color: 'red',    chance: 60,    price: 600,   value: 49  }, // Крутой танец, премиум

        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 50 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 51 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 52 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 53 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 54 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 55 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 56 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 57 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 58 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 59 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 60 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 61 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 62 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 63 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 64 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 65 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 66 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 67 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 68 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 69 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 70 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 71 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 72 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 73 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 74 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 75 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 76 },
        { type: 'wheels',          color: 'red',    chance: 50,    price: 600,   value: 77 },

        { type: 'vehicleSet', color: 'red', chance: 40, price: 750, value: { model: 'evo10', setId: 5, group: 47 } },
        { type: 'vehicleSet', color: 'red', chance: 40, price: 750, value: { model: 'msprinter', setId: 4, group: 48 } },
        { type: 'vehicleSet', color: 'red', chance: 40, price: 750, value: { model: 'taycan', setId: 3, group: 49 } },
        //{ type: 'vehicleSet', color: 'red', chance: 40, price: 750, value: { model: 'taycan', setId: 4, group: 49 } },
        //{ type: 'vehicleSet', color: 'red', chance: 40, price: 750, value: { model: 'taycan', setId: 5, group: 49 } },
        { type: 'vehicleSet', color: 'red', chance: 40, price: 750, value: { model: 'e63s', setId: 10, group: 50 } },
        { type: 'vehicleSet', color: 'red', chance: 40, price: 750, value: { model: 'm5comp', setId: 9, group: 51 } },
        { type: 'vehicleSet', color: 'red', chance: 40, price: 750, value: { model: 'e63s', setId: 11, group: 52 } },
        { type: 'vehicleSet', color: 'red', chance: 40, price: 750, value: { model: 'g63', setId: 8, group: 53 } },
        //{ type: 'vehicleSet', color: 'red', chance: 40, price: 750, value: { model: 'g63', setId: 9, group: 53 } },
        //{ type: 'vehicleSet', color: 'red', chance: 40, price: 750, value: { model: 'g63', setId: 10, group: 53 } },


        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 6,  drawable: 2061, textures: 13, isProp: 0 },  // Ботинки на лямках
                { gender: 1, component: 6,  drawable: 2069, textures: 13, isProp: 0 }   // Ботинки на лямках
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 11, drawable: 2260, textures: 10, isProp: 0 },  // Тканиевая куртка
                { gender: 1, component: 0,  drawable: 2058, textures: 9, isProp: 1 }    // Панама цветная
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 7, drawable: 2027, textures: 1, isProp: 0 },   // Фотоаппарат на шее
                { gender: 1, component: 7, drawable: 2016, textures: 1, isProp: 0 }    // Фотоаппарат на шее
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 5,  drawable: 2023, textures: 6, isProp: 0 },  // Рюкзак Gucci
                { gender: 1, component: 5,  drawable: 2024, textures: 6, isProp: 0 }   // Рюкзак Gucci
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 11, drawable: 2258, textures: 6, isProp: 0 },   // Кожанная куртка
                { gender: 1, component: 11, drawable: 2269, textures: 5, isProp: 0 }   // Платье
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 0, drawable: 53, textures: 1, isProp: 1 },  // Шлем черный
                { gender: 1, component: 0, drawable: 52, textures: 1, isProp: 1 }   // Шлем черный
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 11, drawable: 2270, textures: 8, isProp: 0 },  // Жилет The Noth Face
                { gender: 1, component: 6,  drawable: 2070, textures: 8, isProp: 0 }   // Ботинки с ушками
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 11, drawable: 2257, textures: 8, isProp: 0 },  // Брендовая рубашка
                { gender: 1, component: 11, drawable: 2268, textures: 10, isProp: 0 }  // Меховая куртка Tomy
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 7,  drawable: 2064, textures: 1, isProp: 0 },   // Новогодняя игрушка на плечо
                { gender: 1, component: 7,  drawable: 2052, textures: 1, isProp: 0 }    // Новогодняя игрушка на плечо
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 7,  drawable: 2073, textures: 4, isProp: 0 },   // катаны
                { gender: 1, component: 7,  drawable: 2063, textures: 4, isProp: 0 }    // катаны
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 1,  drawable: 2010, textures: 2, isProp: 1 },  // Очки круглые
                { gender: 1, component: 1,  drawable: 2010, textures: 2, isProp: 1 }   // Очки круглые
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 11, drawable: 2264, textures: 4, isProp: 0 },  // Куртка Moncler
                { gender: 1, component: 11, drawable: 2273, textures: 4, isProp: 0 }   // Куртка Moncler
            ]},
        { type: 'clothes',         color: 'red',    chance: 30,    price: 750,   value: [
                { gender: 0, component: 1,  drawable: 2099, textures: 2, isProp: 0 },  // Маска деда мороза
                { gender: 1, component: 0,  drawable: 2051, textures: 4, isProp: 1 }   // Желтый шлем с ушками
            ]},


        // gold
        { type: 'vehicleDiscount', color: 'gold',   chance: 70,    price: 1500,   value: 25 },

        { type: 'animation',       color: 'gold',   chance: 70,    price: 1500,   value: 161 }, // Эта анимка премиум только. У нее фишка что она парная, и другие игроки могут вместе петь песни

        { type: 'vehicleSet', color: 'gold', chance: 40, price: 1500, value: { model: 'gt63s', setId: 4, group: 54 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 40, price: 1500, value: { model: 'gt63s', setId: 5, group: 54 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 40, price: 1500, value: { model: 'gt63s', setId: 6, group: 54 } },
        { type: 'vehicleSet', color: 'gold', chance: 30, price: 1500, value: { model: 'fxxk', setId: 3, group: 55 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 30, price: 1500, value: { model: 'fxxk', setId: 4, group: 55 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 30, price: 1500, value: { model: 'fxxk', setId: 5, group: 55 } },
        { type: 'vehicleSet', color: 'gold', chance: 40, price: 1500, value: { model: 'huracan', setId: 22, group: 56 } },


        { type: 'clothes',         color: 'gold',   chance: 25,    price: 2000,   value: [
                { gender: 0, component: 5,  drawable: 2024, textures: 6, isProp: 0 },  // Рюкзак Louis Vuitton
                { gender: 1, component: 5,  drawable: 2025, textures: 6, isProp: 0 }   // Рюкзак Louis Vuitton
            ]},
        { type: 'clothes',         color: 'gold',   chance: 30,    price: 2000,   value: [
                { gender: 0, component: 6,  drawable: 2003, textures: 1, isProp: 1 },  // Часы Richard Mille
                { gender: 1, component: 6,  drawable: 2003, textures: 1, isProp: 1 }   // Часы Richard Mille
            ]},
        { type: 'clothes',         color: 'gold',   chance: 30,    price: 2000,   value: [
                { gender: 0, component: 7,  drawable: 2029, textures: 3, isProp: 0 },  // Кобура на 2 ноги, белая
                { gender: 1, component: 7,  drawable: 2012, textures: 3, isProp: 0 }   // Кобура на 2 ноги, белая
            ]},
        { type: 'clothes',         color: 'gold',   chance: 30,    price: 2000,   value: [
                { gender: 0, component: 7,  drawable: 2076, textures: 22, isProp: 0 },  // Кобура на 2 ноги
                { gender: 1, component: 7,  drawable: 2066, textures: 22, isProp: 0 }   // Кобура на 2 ноги
            ]},
        { type: 'clothes',         color: 'gold',   chance: 30,    price: 2000,   value: [
                { gender: 0, component: 0,  drawable: 2047, textures: 8, isProp: 1 },  // Золотые рога
                { gender: 1, component: 0,  drawable: 2049, textures: 8, isProp: 1 }   // Золотые рога
            ]},
        { type: 'clothes',         color: 'gold',   chance: 30,    price: 2000,   value: [
                { gender: 0, component: 7,  drawable: 2075, textures: 1, isProp: 0 },  // Крылья
                { gender: 1, component: 7,  drawable: 2065, textures: 1, isProp: 0 }   // Крылья
            ]},

        { type: 'vehicle',         color: 'gold',   chance: 28,    price: 1500,  value: 'm3e46',    customPrice: 750000  },
        { type: 'vehicle',         color: 'gold',   chance: 28,    price: 1500,  value: 'm4f82',    customPrice: 1000000 },
        { type: 'vehicle',         color: 'gold',   chance: 23,    price: 1500,  value: 'z800',     customPrice: 1250000 },
        { type: 'vehicle',         color: 'gold',   chance: 23,    price: 1750,  value: 'delorean', customPrice: 1500000 },
        { type: 'vehicle',         color: 'gold',   chance: 23,    price: 2000,  value: 'gtr50',    customPrice: 2000000 },
        { type: 'vehicle',         color: 'gold',   chance: 23,    price: 2500,  value: 'cayenne2', customPrice: 2500000 },
        { type: 'vehicle',         color: 'gold',   chance: 18,    price: 5000,  value: 'bacalar',  customPrice: 3000000 },
        { type: 'vehicle',         color: 'gold',   chance: 14,    price: 10000, value: 'fxxk',     customPrice: 4000000 },
        { type: 'vehicle',         color: 'gold',   chance: 10,    price: 10000, value: 'terzo',    customPrice: 5000000 },
        { type: 'vehicle',         color: 'gold',   chance: 5,     price: 20000, value: 'aeroboat', customPrice: 5000000 },
        { type: 'vehicle',         color: 'gold',   chance: 2,     price: 35000, value: 'ec135',    customPrice: 5000000 },
        { type: 'vehicle',         color: 'gold',   chance: 0.5,   price: 50000, value: 'l650',     customPrice: 5000000 },
    ]
}
