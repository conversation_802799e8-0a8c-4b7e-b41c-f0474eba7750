module.exports = {
    id: 7,
    title: 'donate.config.cases.autumn2023.title',
    // discount: 15,

    start: ['2023-09-01', 'YYYY-MM-DD'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    price: 500,
    transferDays: 30,
    // servers: ['TEST0'],

    disabledBuy: false,

    readOnly: false,

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    colors: [
        {
            color: 'gray',
            chance: 55,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'clothes',         chance: 20 },
                { type: 'coins',           chance: 15 },
                // { type: 'armourSkin',      chance: 15 },
                // { type: 'weaponSkin',      chance: 10 },
                { type: 'vehicleDiscount', chance: 6 },
                // { type: 'vehicleSet',      chance: 3 },
                // { type: 'vehicle',         chance: 1 },
            ],
        },
        {
            color: 'blue',
            chance: 23,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'clothes',         chance: 20 },
                { type: 'coins',           chance: 15 },
                { type: 'armourSkin',      chance: 15 },
                { type: 'weaponSkin',      chance: 10 },
                { type: 'vehicleDiscount', chance: 6 },
                // { type: 'vehicleSet',      chance: 3 },
                // { type: 'vehicle',         chance: 1 },

            ],
        },
        {
            color: 'purple',
            chance: 9,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'clothes',         chance: 20 },
                { type: 'coins',           chance: 15 },
                { type: 'armourSkin',      chance: 15 },
                { type: 'weaponSkin',      chance: 10 },
                { type: 'vehicleSet',      chance: 10 },
                { type: 'vehicleDiscount', chance: 6 },
                // { type: 'vehicle',         chance: 1 },
            ],
        },
        {
            color: 'red',
            chance: 6,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'clothes',         chance: 20 },
                { type: 'coins',           chance: 15 },
                { type: 'armourSkin',      chance: 15 },
                { type: 'weaponSkin',      chance: 10 },
                { type: 'vehicleSet',      chance: 7 },
                { type: 'vehicleDiscount', chance: 6 },
                // { type: 'vehicle',         chance: 1 },
            ],
        },
        {
            color: 'gold',
            chance: 3.5,
            types: [
                { type: 'money',           chance: 25 },
                { type: 'clothes',         chance: 20 },
                { type: 'coins',           chance: 15 },
                { type: 'armourSkin',      chance: 15 },
                { type: 'weaponSkin',      chance: 10 },
                { type: 'vehicleSet',      chance: 8 },
                { type: 'vehicleDiscount', chance: 6 },
                { type: 'vehicle',         chance: 2 },
            ],
        }
    ],

    caseContent: [

        // armourSkins
        { type: 'armourSkin', color: 'gray',    chance: 1.00, price: 250,   value: { entityName: 'light', skinId: 48 } },
        { type: 'armourSkin', color: 'blue',    chance: 1.00, price: 350,   value: { entityName: 'light', skinId: 49 } },
        { type: 'armourSkin', color: 'blue',    chance: 1.00, price: 350,   value: { entityName: 'light', skinId: 50 } },
        { type: 'armourSkin', color: 'gray',    chance: 1.00, price: 250,   value: { entityName: 'light', skinId: 51 } },
        { type: 'armourSkin', color: 'purple',  chance: 1.00, price: 500,   value: { entityName: 'light', skinId: 52 } },
        { type: 'armourSkin', color: 'blue',    chance: 1.00, price: 350,   value: { entityName: 'light', skinId: 53 } },
        { type: 'armourSkin', color: 'gray',    chance: 1.00, price: 250,   value: { entityName: 'light', skinId: 54 } },
        { type: 'armourSkin', color: 'gray',    chance: 1.00, price: 500,   value: { entityName: 'light', skinId: 55 } },
        { type: 'armourSkin', color: 'red',     chance: 1.00, price: 500,   value: { entityName: 'light', skinId: 56 } },
        { type: 'armourSkin', color: 'gold',    chance: 1.00, price: 750,   value: { entityName: 'light', skinId: 57 } },

        // weaponSkins Osiris
        { type: 'weaponSkin', color: 'gold',    chance: 1.00, price: 1000,  value: { entityName: 'weapon_heavysniper_mk2',   skinId: 2 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_assaultrifle_mk2',  skinId: 3 } },
        { type: 'weaponSkin', color: 'purple',  chance: 1.00, price: 500,   value: { entityName: 'weapon_carbinerifle',      skinId: 7 } },
        { type: 'weaponSkin', color: 'gold',    chance: 1.00, price: 1000,  value: { entityName: 'weapon_carbinerifle_mk2',  skinId: 4 } },
        { type: 'weaponSkin', color: 'blue',    chance: 1.00, price: 250,   value: { entityName: 'weapon_machinepistol',     skinId: 2 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_heavysniper',       skinId: 0 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_heavyshotgun',      skinId: 9 } },

        // weaponSkins Jap
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_carbinerifle',      skinId: 8 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_assaultrifle_mk2',  skinId: 4 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_assaultrifle',      skinId: 2 } },
        { type: 'weaponSkin', color: 'blue',    chance: 1.00, price: 250,   value: { entityName: 'weapon_bullpuprifle',      skinId: 2 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_carbinerifle_mk2',  skinId: 5 } },
        { type: 'weaponSkin', color: 'purple',  chance: 1.00, price: 500,   value: { entityName: 'weapon_gusenberg',         skinId: 3 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_revolver',          skinId: 8 } },
        { type: 'weaponSkin', color: 'gold',    chance: 1.00, price: 1000,  value: { entityName: 'weapon_heavysniper_mk2',   skinId: 3 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_heavypistol',       skinId: 2 } },
        { type: 'weaponSkin', color: 'purple',  chance: 1.00, price: 500,   value: { entityName: 'weapon_machinepistol',     skinId: 3 } },
        { type: 'weaponSkin', color: 'blue',    chance: 1.00, price: 250,   value: { entityName: 'weapon_marksmanpistol',    skinId: 2 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 500,   value: { entityName: 'weapon_specialcarbine',    skinId: 2 } },
        { type: 'weaponSkin', color: 'blue',    chance: 1.00, price: 250,   value: { entityName: 'weapon_vintagepistol',     skinId: 2 } },
        { type: 'weaponSkin', color: 'blue',    chance: 1.00, price: 250,   value: { entityName: 'weapon_appistol',          skinId: 2 } },

        // weaponSkins Rwave
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_carbinerifle',      skinId: 9 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_assaultrifle_mk2',  skinId: 5 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_assaultrifle',      skinId: 3 } },
        { type: 'weaponSkin', color: 'blue',    chance: 1.00, price: 250,   value: { entityName: 'weapon_bullpuprifle',      skinId: 3 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_carbinerifle_mk2',  skinId: 6 } },
        { type: 'weaponSkin', color: 'purple',  chance: 1.00, price: 500,   value: { entityName: 'weapon_gusenberg',         skinId: 4 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_revolver',          skinId: 9 } },
        { type: 'weaponSkin', color: 'gold',    chance: 1.00, price: 1000,  value: { entityName: 'weapon_heavysniper_mk2',   skinId: 4 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 750,   value: { entityName: 'weapon_heavypistol',       skinId: 3 } },
        { type: 'weaponSkin', color: 'purple',  chance: 1.00, price: 500,   value: { entityName: 'weapon_machinepistol',     skinId: 4 } },
        { type: 'weaponSkin', color: 'blue',    chance: 1.00, price: 250,   value: { entityName: 'weapon_marksmanpistol',    skinId: 3 } },
        { type: 'weaponSkin', color: 'red',     chance: 1.00, price: 500,   value: { entityName: 'weapon_specialcarbine',    skinId: 3 } },
        { type: 'weaponSkin', color: 'blue',    chance: 1.00, price: 250,   value: { entityName: 'weapon_vintagepistol',     skinId: 3 } },
        { type: 'weaponSkin', color: 'blue',    chance: 1.00, price: 250,   value: { entityName: 'weapon_appistol',          skinId: 3 } },

        // Clothes
        { type: 'clothes',    color: 'gold',    chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 11, drawable: 2375, textures: 10,  isProp: 0 }, { gender: 1, component: 11, drawable: 2370, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'gold',    chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 11, drawable: 2378, textures: 5,   isProp: 0 }, { gender: 1, component: 4,  drawable: 2195, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'gold',    chance: 1.00,  price: 1500,  value: [ { gender: 0, component: 6,  drawable: 2076, textures: 10,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2091, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'red',     chance: 1.00,  price: 750,   value: [ { gender: 0, component: 6,  drawable: 2077, textures: 9,   isProp: 0 }, { gender: 1, component: 4,  drawable: 2199, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'red',     chance: 1.00,  price: 750,   value: [ { gender: 0, component: 4,  drawable: 2188, textures: 3,   isProp: 0 }, { gender: 1, component: 6,  drawable: 2088, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'red',     chance: 1.00,  price: 750,   value: [ { gender: 0, component: 11, drawable: 2374, textures: 10,  isProp: 0 }, { gender: 1, component: 11, drawable: 2335, textures: 9,   isProp: 0 } ] },
        { type: 'clothes',    color: 'red',     chance: 1.00,  price: 750,   value: [ { gender: 0, component: 4,  drawable: 2186, textures: 10,  isProp: 0 }, { gender: 1, component: 11, drawable: 2371, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'red',     chance: 1.00,  price: 750,   value: [ { gender: 0, component: 6,  drawable: 2078, textures: 7,   isProp: 0 }, { gender: 1, component: 11, drawable: 2366, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'purple',  chance: 1.00,  price: 500,   value: [ { gender: 0, component: 1,  drawable: 2020, textures: 10,  isProp: 1 }, { gender: 1, component: 4,  drawable: 2196, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'purple',  chance: 1.00,  price: 500,   value: [ { gender: 0, component: 5,  drawable: 2054, texture: 5,    isProp: 0 }, { gender: 1, component: 5,  drawable: 2049, texture: 5,    isProp: 0 } ] },
        { type: 'clothes',    color: 'purple',  chance: 1.00,  price: 500,   value: [ { gender: 0, component: 11, drawable: 2371, textures: 11,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2200, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'purple',  chance: 1.00,  price: 500,   value: [ { gender: 0, component: 4,  drawable: 2187, textures: 10,  isProp: 0 }, { gender: 1, component: 1,  drawable: 2021, textures: 10,  isProp: 1 } ] },
        { type: 'clothes',    color: 'blue',    chance: 1.00,  price: 250,   value: [ { gender: 0, component: 11, drawable: 2372, textures: 10,  isProp: 0 }, { gender: 1, component: 11, drawable: 2367, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'blue',    chance: 1.00,  price: 250,   value: [ { gender: 0, component: 4,  drawable: 2184, textures: 10,  isProp: 0 }, { gender: 1, component: 11, drawable: 2368, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'gray',    chance: 1.00,  price: 125,   value: [ { gender: 0, component: 6,  drawable: 2075, textures: 10,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2198, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'gray',    chance: 1.00,  price: 125,   value: [ { gender: 0, component: 6,  drawable: 2074, textures: 10,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2089, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'gray',    chance: 1.00,  price: 125,   value: [ { gender: 0, component: 4,  drawable: 2185, textures: 11,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2090, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'gray',    chance: 1.00,  price: 125,   value: [ { gender: 0, component: 11, drawable: 2373, textures: 10,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2197, textures: 10,  isProp: 0 } ] },
        { type: 'clothes',    color: 'gray',    chance: 1.00,  price: 125,   value: [ { gender: 0, component: 11, drawable: 2376, textures: 11,  isProp: 0 }, { gender: 1, component: 11, drawable: 2369, textures: 20,  isProp: 0 } ] },
        { type: 'clothes',    color: 'gray',    chance: 1.00,  price: 125,   value: [ { gender: 0, component: 0,  drawable: 2078, textures: 10,  isProp: 1 }, { gender: 1, component: 0,  drawable: 2079, textures: 10,  isProp: 1 } ] },

        // vehMod
        { type: 'vehicleSet', color: 'purple', chance: 18, price: 500, value: { model: 'taycan', setId: 6, group: 1 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 16, price: 500, value: { model: 'taycan', setId: 7, group: 1 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 14, price: 500, value: { model: 'taycan', setId: 8, group: 1 } },
        { type: 'vehicleSet', color: 'purple', chance: 16, price: 500, value: { model: 'panamera17turbo', setId: 3, group: 2 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 14, price: 500, value: { model: 'panamera17turbo', setId: 4, group: 2 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 12, price: 500, value: { model: 'panamera17turbo', setId: 5, group: 2 } },
        { type: 'vehicleSet', color: 'purple', chance: 14, price: 500, value: { model: 'sclass3', setId: 11, group: 3 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 12, price: 500, value: { model: 'sclass3', setId: 12, group: 3 } },
        //{ type: 'vehicleSet', color: 'purple', chance: 10, price: 500, value: { model: 'sclass3', setId: 13, group: 3 } },
        { type: 'vehicleSet', color: 'red', chance: 12, price: 1000, value: { model: 'q8', setId: 10, group: 4 } },
        //{ type: 'vehicleSet', color: 'red', chance: 10, price: 1500, value: { model: 'q8', setId: 11, group: 4 } },
        //{ type: 'vehicleSet', color: 'red', chance: 8, price: 2000, value: { model: 'q8', setId: 12, group: 4 } },
        { type: 'vehicleSet', color: 'red', chance: 10, price: 1000, value: { model: 'gt63s', setId: 10, group: 5 } },
        //{ type: 'vehicleSet', color: 'red', chance: 8, price: 1500, value: { model: 'gt63s', setId: 11, group: 5 } },
        //{ type: 'vehicleSet', color: 'red', chance: 6, price: 2000, value: { model: 'gt63s', setId: 12, group: 5 } },
        { type: 'vehicleSet', color: 'red', chance: 8, price: 1500, value: { model: 'g63', setId: 14, group: 6 } },
        //{ type: 'vehicleSet', color: 'red', chance: 6, price: 2000, value: { model: 'g63', setId: 15, group: 6 } },
        //{ type: 'vehicleSet', color: 'red', chance: 4, price: 2000, value: { model: 'g63', setId: 16, group: 6 } },
        { type: 'vehicleSet', color: 'gold', chance: 6, price: 5000, value: { model: 'hummer', setId: 3, group: 7 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 5, price: 6000, value: { model: 'hummer', setId: 4, group: 7 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 4, price: 7000, value: { model: 'hummer', setId: 5, group: 7 } },
        { type: 'vehicleSet', color: 'gold', chance: 6, price: 5000, value: { model: 'diablo', setId: 6, group: 8 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 5, price: 6000, value: { model: 'diablo', setId: 7, group: 8 } },
        //{ type: 'vehicleSet', color: 'gold', chance: 4, price: 7000, value: { model: 'diablo', setId: 8, group: 8 } },


        // Vehicles
        { type: 'vehicle',    color: 'gold',    chance: 15.00, price: 10000, customPrice: 1125000, value: 'hummer'   },
        { type: 'vehicle',    color: 'gold',    chance: 10.00, price: 12000, customPrice: 1750000, value: 'diablo'   },
        { type: 'vehicle',    color: 'gold',    chance: 8.00,  price: 15000, customPrice: 2125000, value: 'xm'       },
        { type: 'vehicle',    color: 'gold',    chance: 3.00,  price: 20000, customPrice: 5000000, value: 'missionr' },

        // Скидка на транспорт
        { type: 'vehicleDiscount', color: 'gray',   chance: 50.00, price: 125,  value: 5  },
        { type: 'vehicleDiscount', color: 'blue',   chance: 30.00, price: 250,  value: 10 },
        { type: 'vehicleDiscount', color: 'purple', chance: 15.00, price: 350,  value: 15 },
        { type: 'vehicleDiscount', color: 'red',    chance: 4.00,  price: 700,  value: 20 },
        { type: 'vehicleDiscount', color: 'gold',   chance: 1.00,  price: 1500, value: 25 },

        // Вирты
        { type: 'money',   color: 'gray',           chance: 50.00, price: 75,   value: 7000    },
        { type: 'money',   color: 'blue',           chance: 30.00, price: 100,  value: 15000   },
        { type: 'money',   color: 'purple',         chance: 15.00, price: 400,  value: 50000   },
        { type: 'money',   color: 'red',            chance: 4.00,  price: 800,  value: 100000  },
        { type: 'money',   color: 'gold',           chance: 1.00,  price: 2500, value: 300000  },

        // Коины
        { type: 'coins',   color: 'gray',           chance: 50.00, price: 50,   value: 50   },
        { type: 'coins',   color: 'blue',           chance: 30.00, price: 150,  value: 150  },
        { type: 'coins',   color: 'purple',         chance: 15.00, price: 300,  value: 300  },
        { type: 'coins',   color: 'red',            chance: 4.00,  price: 1000, value: 1000 },
        { type: 'coins',   color: 'gold',           chance: 1.00,  price: 5000, value: 5000 },
    ]
}
