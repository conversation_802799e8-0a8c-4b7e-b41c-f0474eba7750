module.exports = {
    id: 17,
    title: 'donate.config.cases.autumn2024.title',
    // discount: 5,

    start: ['2024-10-01', 'YYYY-MM-DD'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    price: 500,
    transferDays: 30,

    // servers: ['STAGING0', 'DEVELOP0', 'TEST2'],
    // disabledBuy: true,
    // readOnly: false,

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    colors: [
        {
            color: 'gray',
            chance: 55,
            types: [
                { type: 'money',           chance: 35 },
                { type: 'coins',           chance: 25 },
                { type: 'animation',       chance: 20 },
                { type: 'clothesDiscount', chance: 15 },
                { type: 'vehicleDiscount', chance: 15 },
                { type: 'clothes',         chance: 20 },
                { type: 'vehicle',         chance: 15 },
            ],
        },
        {
            color: 'blue',
            chance: 23,
            types: [
                { type: 'money',           chance: 35 },
                { type: 'coins',           chance: 25 },
                { type: 'animation',       chance: 20 },
                { type: 'clothesDiscount', chance: 15 },
                { type: 'vehicleDiscount', chance: 15 },
                { type: 'clothes',         chance: 20 },
                { type: 'vehicle',         chance: 15 },
                { type: 'subscription',    chance: 13 },
            ],
        },
        {
            color: 'purple',
            chance: 11,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'coins',           chance: 25 },
                { type: 'animation',       chance: 20 },
                { type: 'clothesDiscount', chance: 25 },
                { type: 'vehicleDiscount', chance: 20 },
                { type: 'clothes',         chance: 15 },
                { type: 'vehicle',         chance: 13 },
                { type: 'subscription',    chance: 11 },
            ],
        },
        {
            color: 'red',
            chance: 6,
            types: [
                { type: 'money',           chance: 25 },
                { type: 'coins',           chance: 20 },
                { type: 'animation',       chance: 20 },
                { type: 'clothesDiscount', chance: 21 },
                { type: 'vehicleDiscount', chance: 20 },
                { type: 'clothes',         chance: 15 },
                { type: 'vehicleSet',      chance: 14 },
                { type: 'vehicleMod',      chance: 12 },
                { type: 'vehicle',         chance: 10 },
                { type: 'subscription',    chance: 11 },
            ],
        },
        {
            color: 'gold',
            chance: 3.5,
            types: [
                { type: 'money',           chance: 25 },
                { type: 'coins',           chance: 20 },
                { type: 'clothesDiscount', chance: 15 },
                { type: 'vehicleDiscount', chance: 15 },
                { type: 'clothes',         chance: 14 },
                { type: 'vehicleSet',      chance: 13 },
                { type: 'vehicleMod',      chance: 11 },
                { type: 'vehicle',         chance: 9 },
                { type: 'subscription',    chance: 9 },
            ],
        },
        {
            color: 'unique',
            chance: 0.8,
            types: [
                { type: 'clothes',         chance: 15 },
                { type: 'vehicleMod',      chance: 11 },
                { type: 'vehicle',         chance: 8 },
            ],
        }
    ],

    caseContent: [
        // Animations
        { type: 'animation',       color: 'gray',   chance: 20,   price: 150,    value: 213 }, // Waterworks
        { type: 'animation',       color: 'gray',   chance: 20,   price: 150,    value: 87 }, // Wu Tang Is Forever

        { type: 'animation',       color: 'blue',   chance: 17,   price: 400,    value: 335 }, // Popular Vibe
        { type: 'animation',       color: 'blue',   chance: 17,   price: 400,    value: 47 }, // Out West
        { type: 'animation',       color: 'blue',   chance: 17,   price: 400,    value: 72 }, // Rushin Around
        { type: 'animation',       color: 'blue',   chance: 17,   price: 400,    value: 218 }, // Tootse
        { type: 'animation',       color: 'blue',   chance: 17,   price: 400,    value: 229 }, // Party Hips
        { type: 'animation',       color: 'blue',   chance: 17,   price: 400,    value: 328 }, // Dancin Domino
        { type: 'animation',       color: 'blue',   chance: 17,   price: 400,    value: 334 }, // No Cure
        { type: 'animation',       color: 'blue',   chance: 17,   price: 400,    value: 342 }, // The Squabble
        { type: 'animation',       color: 'blue',   chance: 17,   price: 400,    value: 301 }, // Headbanger 2

        { type: 'animation',       color: 'purple', chance: 14,   price: 700,    value: 48 }, // Toosie Slide
        { type: 'animation',       color: 'purple', chance: 14,   price: 700,    value: 279 }, // Bring It Around
        { type: 'animation',       color: 'purple', chance: 14,   price: 700,    value: 330 }, // Go With The Flow
        { type: 'animation',       color: 'purple', chance: 14,   price: 700,    value: 314 }, // Cardistry
        { type: 'animation',       color: 'purple', chance: 14,   price: 700,    value: 315 }, // Target Training
        { type: 'animation',       color: 'purple', chance: 14,   price: 700,    value: 351 }, // Squirrelly
        { type: 'animation',       color: 'purple', chance: 14,   price: 700,    value: 354 }, // Baller

        { type: 'animation',       color: 'red',    chance: 11,   price: 1000,   value: 303 }, // Bust A Move
        { type: 'animation',       color: 'red',    chance: 11,   price: 1000,   value: 348 }, // Expressionism
        { type: 'animation',       color: 'red',    chance: 11,   price: 1000,   value: 349 }, // SqueezieDoesIt
        { type: 'animation',       color: 'red',    chance: 11,   price: 1000,   value: 352 }, // ZoidbergScuttle
        { type: 'animation',       color: 'red',    chance: 11,   price: 1000,   value: 353 }, // GGWP
        { type: 'animation',       color: 'red',    chance: 11,   price: 1000,   value: 350 }, // LilSupercar

        // Clothes
        { type: 'clothes',      color: 'gray',   chance: 1.00,   price: 250,  value: { gender: 1, component: 6,  drawable: 2139, textures: 10, isProp: 0 } },
        { type: 'clothes',      color: 'gray',   chance: 1.00,   price: 250,  value: { gender: 1, component: 5,  drawable: 2094, textures: 9, isProp: 0 } },
        { type: 'clothes',      color: 'gray',   chance: 1.00,   price: 250,  value: { gender: 0, component: 5,  drawable: 2090, textures: 9, isProp: 0 } },
        { type: 'clothes',      color: 'gray',   chance: 1.00,   price: 250,  value: { gender: 0, component: 6,  drawable: 2116, textures: 10, isProp: 0 } },

        { type: 'clothes',      color: 'blue',   chance: 1.00,   price: 450,  value: { gender: 0, component: 6,  drawable: 2115, textures: 4, isProp: 0 } },
        { type: 'clothes',      color: 'blue',   chance: 1.00,   price: 450,  value: { gender: 1, component: 6,  drawable: 2137, textures: 4, isProp: 0 } },
        { type: 'clothes',      color: 'blue',   chance: 1.00,   price: 450,  value: { gender: 0, component: 1,  drawable: 2031, textures: 7, isProp: 1 } },
        { type: 'clothes',      color: 'blue',   chance: 1.00,   price: 450,  value: { gender: 1, component: 1,  drawable: 2032, textures: 7, isProp: 1 } },

        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 700,  value: { gender: 1, component: 4,  drawable: 2258, textures: 3, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 700,  value: { gender: 1, component: 5,  drawable: 2082, textures: 4, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 700,  value: { gender: 0, component: 5,  drawable: 2080, textures: 4, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 700,  value: { gender: 1, component: 6,  drawable: 2140, textures: 14, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 700,  value: { gender: 1, component: 11,  drawable: 2464, textures: 4, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 700,  value: { gender: 1, component: 5,  drawable: 2087, textures: 13, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 700,  value: { gender: 0, component: 5,  drawable: 2083, textures: 13, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 700,  value: { gender: 0, component: 6,  drawable: 2117, textures: 14, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 700,  value: { gender: 0, component: 11,  drawable: 2459, textures: 8, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 700,  value: { gender: 0, component: 4,  drawable: 2241, textures: 8, isProp: 0 } },

        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 1, component: 11,  drawable: 2465, textures: 6, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 1, component: 4,  drawable: 2260, textures: 7, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 1, component: 5,  drawable: 2081, textures: 10, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 1, component: 11,  drawable: 2463, textures: 8, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 1, component: 4,  drawable: 2259, textures: 6, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 1, component: 5,  drawable: 2083, textures: 5, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 1, component: 6,  drawable: 2143, textures: 7, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 1, component: 5,  drawable: 2088, textures: 13, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 0, component: 5,  drawable: 2079, textures: 10, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 0, component: 5,  drawable: 2084, textures: 13, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 0, component: 4,  drawable: 2239, textures: 7, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 1000,  value: { gender: 0, component: 6,  drawable: 2120, textures: 7, isProp: 0 } },

        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 1, component: 11,  drawable: 2467, textures: 7, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 1, component: 4,  drawable: 2257, textures: 4, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 0, component: 11,  drawable: 2458, textures: 2, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 0, component: 6,  drawable: 2122, textures: 15, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 1, component: 6,  drawable: 2145, textures: 15, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 0, component: 5,  drawable: 2089, textures: 9, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 0, component: 4,  drawable: 2240, textures: 2, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 1, component: 5,  drawable: 2093, textures: 9, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 1, component: 5,  drawable: 2085, textures: 9, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 0, component: 6,  drawable: 2118, textures: 1, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 1, component: 6,  drawable: 2141, textures: 1, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 1, component: 10,  drawable: 2100, textures: 7, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 0, component: 1,  drawable: 2033, textures: 6, isProp: 1 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 1600,  value: { gender: 1, component: 1,  drawable: 2034, textures: 6, isProp: 1 } },

        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 3000,  value: { gender: 0, component: 11,  drawable: 2462, textures: 4, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 3000,  value: { gender: 0, component: 11,  drawable: 2465, textures: 9, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 3000,  value: { gender: 1, component: 11,  drawable: 2466, textures: 7, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 3000,  value: { gender: 1, component: 4,  drawable: 2261, textures: 7, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 3000,  value: { gender: 0, component: 11,  drawable: 2468, textures: 9, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 3000,  value: { gender: 0, component: 5,  drawable: 2078, textures: 6, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 3000,  value: { gender: 1, component: 5,  drawable: 2080, textures: 6, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 3000,  value: { gender: 1, component: 6,  drawable: 2142, textures: 6, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 3000,  value: { gender: 0, component: 6,  drawable: 2119, textures: 6, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 3000,  value: { gender: 1, component: 6,  drawable: 2144, textures: 5, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 3000,  value: { gender: 0, component: 6,  drawable: 2121, textures: 5, isProp: 0 } },

		// Vehicles
        { type: 'vehicle',    color: 'gray',    chance: 5,   price: 200,  customPrice: 20000,  value: 'buccaneer'  },
        { type: 'vehicle',    color: 'gray',    chance: 5,   price: 200,  customPrice: 20000,  value: 'gb200'  },
        { type: 'vehicle',    color: 'gray',    chance: 5,   price: 200,  customPrice: 20000,  value: 'cavalcade'  },
        { type: 'vehicle',    color: 'gray',    chance: 5,   price: 200,  customPrice: 20000,  value: 'sultan'  },
        { type: 'vehicle',    color: 'gray',    chance: 5,   price: 200,  customPrice: 20000,  value: 'pentro'  },
        { type: 'vehicle',    color: 'gray',    chance: 5,   price: 200,  customPrice: 20000,  value: 'bjxl'  },
        { type: 'vehicle',    color: 'gray',    chance: 5,   price: 200,  customPrice: 20000,  value: 'vigero'  },

        { type: 'vehicle',    color: 'blue',    chance: 8,   price: 400,  customPrice: 100000,  value: 'xes'  },
        { type: 'vehicle',    color: 'blue',    chance: 7,   price: 400,  customPrice: 100000,  value: 'ftype'  },
        { type: 'vehicle',    color: 'blue',    chance: 6,   price: 400,  customPrice: 100000,  value: 'fx50s'  },
        { type: 'vehicle',    color: 'blue',    chance: 5,   price: 400,  customPrice: 100000,  value: 'x6m'  },

		{ type: 'vehicle',    color: 'purple',    chance: 25,   price: 750,  customPrice: 200000,  value: 'rio2'  },
		{ type: 'vehicle',    color: 'purple',    chance: 22,   price: 800,  customPrice: 275000,  value: 'astra'  },
		{ type: 'vehicle',    color: 'purple',    chance: 19,   price: 900,  customPrice: 350000,  value: 'a4'  },
		{ type: 'vehicle',    color: 'purple',    chance: 16,   price: 1000,  customPrice: 550000,  value: 'cclass2'  },

		{ type: 'vehicle',    color: 'red',       chance: 18,   price: 1500,  customPrice: 750000,  value: '5series3'  },
		{ type: 'vehicle',    color: 'red',       chance: 16,   price: 1600,  customPrice: 750000,  value: 'eclass5'  },
		{ type: 'vehicle',    color: 'red',       chance: 14,   price: 1700,  customPrice: 1125000,  value: 'defender'  },
		{ type: 'vehicle',    color: 'red',       chance: 12,    price: 1800,  customPrice: 1250000,  value: 'ct5'  },

		{ type: 'vehicle',    color: 'gold',      chance: 6,    price: 5000, customPrice: 1500000,  value: 'silverado'  },
		{ type: 'vehicle',    color: 'gold',      chance: 4,    price: 6500, customPrice: 1625000,  value: 'xts'  },
		{ type: 'vehicle',    color: 'gold',      chance: 3,    price: 8000, customPrice: 1750000,  value: 'sls'  },

		{ type: 'vehicle',    color: 'unique',    chance: 6,    price: 15000, customPrice: 3000000,  value: 'batur'  },
		{ type: 'vehicle',    color: 'unique',    chance: 4,    price: 18000, customPrice: 3500000,  value: 'valour'  },
		{ type: 'vehicle',    color: 'unique',    chance: 2,    price: 20000, customPrice: 5000000,  value: 'noire'  },

		// vehMod
		{ type: 'vehicleMod', color: 'red',    chance: 24, price: 1200,  customPrice: 200000,  value: { model: 'rio2', setId: 1}  },
		{ type: 'vehicleMod', color: 'red',    chance: 22, price: 1300,  customPrice: 275000,  value: { model: 'astra', setId: 1 } },
		{ type: 'vehicleMod', color: 'red',    chance: 20, price: 1400,  customPrice: 350000,  value: { model: 'a4', setId: 1 } },

		{ type: 'vehicleMod', color: 'gold',   chance: 14, price: 3500,  customPrice: 550000,  value: { model: 'cclass2', setId: 2 } },
		{ type: 'vehicleMod', color: 'gold',   chance: 12, price: 5000,  customPrice: 750000,  value: { model: 'eclass5', setId: 2 } },
		{ type: 'vehicleMod', color: 'gold',   chance: 10, price: 5000,  customPrice: 750000,  value: { model: '5series3', setId: 1 } },
        { type: 'vehicleMod', color: 'gold',   chance: 8,  price: 6500,  customPrice: 1250000, value: { model: 'ct5', setId: 1 } },

        { type: 'vehicleMod', color: 'unique', chance: 10, price: 4500,  customPrice: 350000,  value: { model: 'a4', setId: 2 } },
        { type: 'vehicleMod', color: 'unique', chance: 8,  price: 5000,  customPrice: 550000,  value: { model: 'cclass2', setId: 3 } },
		{ type: 'vehicleMod', color: 'unique', chance: 6,  price: 6000,  customPrice: 750000,  value: { model: '5series3', setId: 2 } },
		{ type: 'vehicleMod', color: 'unique', chance: 4,  price: 7000,  customPrice: 750000,  value: { model: 'eclass5', setId: 3 } },

		// Vehicles tuning
		{ type: 'vehicleSet', color: 'red',  chance: 19, price: 1600, value:  { model: 'rrphantom', setId: 4 } },
		{ type: 'vehicleSet', color: 'red',  chance: 17, price: 1700, value:  { model: 'lc500',     setId: 7 } },
		{ type: 'vehicleSet', color: 'red',  chance: 15, price: 1800, value:  { model: 'f150',      setId: 15 } },

		{ type: 'vehicleSet', color: 'gold', chance: 14, price: 3000, value:  { model: 'huracan',   setId: 10 } },
		{ type: 'vehicleSet', color: 'gold', chance: 12, price: 3000, value:  { model: '918s',      setId: 9 } },
		{ type: 'vehicleSet', color: 'gold', chance: 10, price: 3000, value:  { model: 'chiron19',  setId: 18 } },

        // Скидка на транспорт
        { type: 'vehicleDiscount', color: 'gray',   chance: 50.00, price: 125,  value: 5  },
        { type: 'vehicleDiscount', color: 'blue',   chance: 30.00, price: 250,  value: 10 },
        { type: 'vehicleDiscount', color: 'purple', chance: 15.00, price: 350,  value: 15 },
        { type: 'vehicleDiscount', color: 'red',    chance: 4.00,  price: 700,  value: 20 },
        { type: 'vehicleDiscount', color: 'gold',   chance: 1.00,  price: 1500, value: 25 },

        // Скидка на одежду
        { type: 'clothesDiscount', color: 'gray',   chance: 50.00, price: 125,  value: 5  },
        { type: 'clothesDiscount', color: 'blue',   chance: 30.00, price: 250,  value: 10 },
        { type: 'clothesDiscount', color: 'purple', chance: 15.00, price: 350,  value: 15 },
        { type: 'clothesDiscount', color: 'red',    chance: 4.00,  price: 700,  value: 20 },
        { type: 'clothesDiscount', color: 'gold',   chance: 1.00,  price: 1500, value: 25 },

        // Вирты
        { type: 'money',   color: 'gray',           chance: 50.00, price: 100,  value: 10000  },
        { type: 'money',   color: 'blue',           chance: 30.00, price: 250,  value: 25000  },
        { type: 'money',   color: 'purple',         chance: 15.00, price: 400,  value: 40000  },
        { type: 'money',   color: 'red',            chance: 4.00,  price: 1000, value: 100000 },
        { type: 'money',   color: 'gold',           chance: 1.00,  price: 2500, value: 250000 },

        // Коины
        { type: 'coins',   color: 'gray',           chance: 50.00, price: 100,  value: 100  },
        { type: 'coins',   color: 'blue',           chance: 30.00, price: 250,  value: 250  },
        { type: 'coins',   color: 'purple',         chance: 15.00, price: 400,  value: 400  },
        { type: 'coins',   color: 'red',            chance: 4.00,  price: 1000, value: 1000 },
        { type: 'coins',   color: 'gold',           chance: 1.00,  price: 2500, value: 2500 },

        // Premium
        { type: 'subscription',  color: 'blue',     chance: 2,  price: 250,  value: 7 },
        { type: 'subscription',  color: 'purple',   chance: 2,  price: 400,  value: 30 },
        { type: 'subscription',  color: 'red',      chance: 2,  price: 1000, value: 90 },
        { type: 'subscription',  color: 'gold',     chance: 2,  price: 2500, value: 180 },
    ]
}
