module.exports = {
    id: 3,
    title: 'donate.config.cases.summer2022.title',
    // discount: 10,

    seasonPassId: 1,
    transferDays: 60,

    // Сумма распыления указан в опыте батлпаса. Если батлпас активен
    sellRule: 40, // Если батлпас уже не активен, то задать процент конвертации price в коины. Например 100 exp / 100 * 25 = 25 коинов

    price: 750,
    transferDays: 30,

    // disabledBuy: true,
    // end: '2023-09-15',

    start: ['2022-06-01', 'YYYY-MM-DD'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],
    transferDays: 30,

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    colors: [
        {
            color: 'gray',
            chance: 67,
            types: [
                { type: 'item',            chance: 70 },
                { type: 'weaponSkin',      chance: 60 },
                { type: 'armourSkin',      chance: 50 },
                { type: 'clothes',         chance: 40 },
                { type: 'money',           chance: 40 },
                { type: 'coins',           chance: 40 },
                { type: 'vehicle',         chance: 30 },
            ],
        },
        {
            color: 'blue',
            chance: 18,
            types: [
                { type: 'item',            chance: 70 },
                { type: 'animation',       chance: 60 },
                // { type: 'status',          chance: 55 },
                { type: 'tattoo',          chance: 50 },
                { type: 'wheels',          chance: 45 },
                { type: 'weaponSkin',      chance: 40 },
                { type: 'armourSkin',      chance: 35 },
                { type: 'clothes',         chance: 30 },
            ],
        },
        {
            color: 'purple',
            chance: 7.5,
            types: [
                { type: 'item',            chance: 70 },
                // { type: 'status',          chance: 60 },
                { type: 'animation',       chance: 55 },
                { type: 'tattoo',          chance: 50 },
                { type: 'wheels',          chance: 45 },
                { type: 'weaponSkin',      chance: 40 },
                { type: 'clothes',         chance: 35 },
            ],
        },
        {
            color: 'red',
            chance: 5,
            types: [
                { type: 'item',            chance: 70 },
                // { type: 'status',          chance: 60 },
                { type: 'animation',       chance: 50 },
                { type: 'weaponSkin',      chance: 40 },
                // { type: 'status',          chance: 40 },
                { type: 'clothes',         chance: 30 },
                { type: 'vehicleSet',      chance: 20 },
                { type: 'vehicle',         chance: 10 },
            ],
        },
        {
            color: 'gold',
            chance: 2.5,
            types: [
                { type: 'item',            chance: 70 },
                { type: 'animation',       chance: 60 },
                { type: 'weaponSkin',      chance: 50 },
                { type: 'clothes',         chance: 40 },
                { type: 'vehicleSet',      chance: 20 },
                { type: 'vehicle',         chance: 10 },
            ],
        }
    ],

    caseContent: [
        // gray
        { type: 'item',         color: 'gray',   chance: 1.00,   price: 250,   value: { itemId: 255, count: 0  } },
        { type: 'item',         color: 'gray',   chance: 1.00,   price: 250,   value: { itemId: 610, count: 0  } },
        { type: 'item',         color: 'gray',   chance: 1.00,   price: 250,   value: { itemId: 262, count: 0  } },
        { type: 'item',         color: 'gray',   chance: 1.00,   price: 250,   value: { itemId: 334, count: 20 } },
        { type: 'item',         color: 'gray',   chance: 1.00,   price: 250,   value: { itemId: 532, count: 1  } },

        { type: 'weaponSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'weapon_smg_mk2',  skinId: 0 } },
        { type: 'weaponSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'weapon_revolver', skinId: 1 } },

        { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 0  } },
        { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 5  } },
        { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 6  } },
        { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 12 } },
        { type: 'armourSkin',   color: 'gray',   chance: 1.00,   price: 250,  value: { entityName: 'light', skinId: 1  } },

        { type: 'clothes',      color: 'gray',   chance: 1.00,   price: 250,  value: [{ gender: 0, component: 11, drawable: 2244, texture: 6,  isProp: 0 }, { gender: 1, component: 11, drawable: 2256, texture: 0,  isProp: 0 }] },
        { type: 'clothes',      color: 'gray',   chance: 1.00,   price: 250,  value: [{ gender: 0, component: 4,  drawable: 2143, texture: 12, isProp: 0 }, { gender: 1, component: 7,  drawable: 2061, texture: 0,  isProp: 0 }] },

        { type: 'vehicle',      color: 'gray',   chance: 1.00,   price: 250,  value: 'akuma'    },
        { type: 'vehicle',      color: 'gray',   chance: 1.00,   price: 250,  value: 'bmx'      },
        { type: 'vehicle',      color: 'gray',   chance: 1.00,   price: 250,  value: 'f620'     },
        { type: 'vehicle',      color: 'gray',   chance: 1.00,   price: 250,  value: 'daemon2'  },
        { type: 'vehicle',      color: 'gray',   chance: 1.00,   price: 250,  value: 'moonbeam' },

        { type: 'money',        color: 'gray',   chance: 1.00,   price: 250,      value: 25000 },

        { type: 'coins',        color: 'gray',   chance: 1.00,   price: 250,      value: 250 },


        // blue
        { type: 'item',         color: 'blue',   chance: 1.00,   price: 500,  value: { itemId: 431, count: 0 } },
        { type: 'item',         color: 'blue',   chance: 1.00,   price: 500,  value: { itemId: 130, count: 0 } }, // Кинжал
        { type: 'item',         color: 'blue',   chance: 1.00,   price: 500,  value: { itemId: 444, count: 0 } }, // Ключ карта Fleeca
        { type: 'item',         color: 'blue',   chance: 1.00,   price: 500,  value: { itemId: 132, count: 0 } }, // Складной нож
        { type: 'item',         color: 'blue',   chance: 1.00,   price: 500,  value: { itemId: 333, count: 0 } }, // Ручной пулемет Мk2

        { type: 'animation',    color: 'blue',   chance: 1.00,   price: 500,  value: 29 },
        { type: 'animation',    color: 'blue',   chance: 1.00,   price: 500,  value: 0  },

        // { type: 'status',       color: 'blue',   chance: 1.00,   price: 500,  value: { type: 'Silver', days: 90 } },

        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3107, 6082] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3106, 6083] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3103, 6124] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3086, 6084] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3104, 6085] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3105, 6086] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3029, 6087] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3030, 6088] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3031, 6089] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3032, 6090] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3100, 6091] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3101, 6092] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3079, 6093] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3080, 6094] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3108, 6099] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3073, 6100] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3074, 6101] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3102, 6102] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3098, 6103] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3062, 6104] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3063, 6105] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3099, 6106] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3094, 6108] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3095, 6109] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3096, 6107] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3097, 6110] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3035, 6111] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3038, 6112] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3093, 6113] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3047, 6118] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3017, 6095] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3018, 6096] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3020, 6097] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3021, 6098] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3024, 6114] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3025, 6115] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3026, 6116] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3027, 6117] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3081, 6120] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3082, 6119] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3083, 6121] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3084, 6122] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3012, 6123] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3013, 6054] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3015, 6057] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3016, 6059] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3008, 6046] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3009, 6048] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3010, 6049] },
        { type: 'tattoo',       color: 'blue',   chance: 1.00,   price: 500,  value: [3011, 6050] },

        { type: 'wheels',       color: 'blue',   chance: 1.00,   price: 500,  value: 8  },
        { type: 'wheels',       color: 'blue',   chance: 1.00,   price: 500,  value: 1  },
        { type: 'wheels',       color: 'blue',   chance: 1.00,   price: 500,  value: 9  },
        { type: 'wheels',       color: 'blue',   chance: 1.00,   price: 500,  value: 2  },
        { type: 'wheels',       color: 'blue',   chance: 1.00,   price: 500,  value: 12 },
        { type: 'wheels',       color: 'blue',   chance: 1.00,   price: 500,  value: 5  },

        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'weapon_microsmg',         skinId: 0 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'weapon_gusenberg',        skinId: 0 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'weapon_assaultrifle_mk2', skinId: 0 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'weapon_carbinerifle',     skinId: 0 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'weapon_combatpdw',        skinId: 1 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'weapon_revolver',         skinId: 5 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'weapon_militaryrifle',    skinId: 0 } },
        { type: 'weaponSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'weapon_smg_mk2',          skinId: 2 } },

        { type: 'armourSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'light', skinId: 19 } },
        { type: 'armourSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'light', skinId: 10 } },
        { type: 'armourSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'light', skinId: 11 } },
        { type: 'armourSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'light', skinId: 16 } },
        { type: 'armourSkin',   color: 'blue',   chance: 1.00,   price: 500,  value: { entityName: 'light', skinId: 7  } },

        { type: 'clothes',     color: 'blue',    chance: 1.00,   price: 500,  value: [{ gender: 0, component: 11, drawable: 2245, texture: 2,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2151, texture: 0,  isProp: 0 }] },
        { type: 'clothes',     color: 'blue',    chance: 1.00,   price: 500,  value: [{ gender: 0, component: 11, drawable: 2246, texture: 17, isProp: 0 }, { gender: 1, component: 11, drawable: 2259, texture: 3,  isProp: 0 }] },
        { type: 'clothes',     color: 'blue',    chance: 1.00,   price: 500,  value: [{ gender: 0, component: 1,  drawable: 2010, texture: 0,  isProp: 1 }, { gender: 1, component: 1,  drawable: 2010, texture: 1,  isProp: 1 }] },
        { type: 'clothes',     color: 'blue',    chance: 1.00,   price: 500,  value: [{ gender: 0, component: 6,  drawable: 2058, texture: 2,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2062, texture: 2,  isProp: 0 }] },
        { type: 'clothes',     color: 'blue',    chance: 1.00,   price: 500,  value: [{ gender: 0, component: 5,  drawable: 2017, texture: 0,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2017, texture: 0,  isProp: 0 }] },
        { type: 'clothes',     color: 'blue',    chance: 1.00,   price: 500,  value: [{ gender: 0, component: 0,  drawable: 2042, texture: 1,  isProp: 1 }, { gender: 1, component: 0,  drawable: 2050, texture: 1,  isProp: 1 }] },
        { type: 'clothes',     color: 'blue',    chance: 1.00,   price: 500,  value: [{ gender: 0, component: 4,  drawable: 2144, texture: 7,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2152, texture: 0,  isProp: 0 }] },
        { type: 'clothes',     color: 'blue',    chance: 1.00,   price: 500,  value: [{ gender: 0, component: 11, drawable: 2248, texture: 13, isProp: 0 }, { gender: 1, component: 11, drawable: 2259, texture: 6,  isProp: 0 }] },


        // purple
        { type: 'item',        color: 'purple',  chance: 1.00,   price: 750,  value: { itemId: 337, count: 0 } }, // Винтовка Marksman Mk2
        { type: 'item',        color: 'purple',  chance: 1.00,   price: 750,  value: { itemId: 443, count: 0 } }, // Дрель 1500w
        { type: 'item',        color: 'purple',  chance: 1.00,   price: 750,  value: { itemId: 573, count: 0 } }, // Списанный дефибриллятор
        { type: 'item',        color: 'purple',  chance: 1.00,   price: 750, value: { itemId: 281, count: 0 } }, // Дефибриллятор

        // { type: 'status',      color: 'purple',  chance: 1.00,   price: 750,  value: { type: 'Gold', days: 90 } },

        { type: 'animation',   color: 'purple',  chance: 1.00,   price: 750,  value: 30 },
        { type: 'animation',   color: 'purple',  chance: 1.00,   price: 750,  value: 23 },

        { type: 'tattoo',      color: 'purple',  chance: 1.00,   price: 750,  value: [3002, 6045] },
        { type: 'tattoo',      color: 'purple',  chance: 1.00,   price: 750,  value: [3003, 6061] },
        { type: 'tattoo',      color: 'purple',  chance: 1.00,   price: 750,  value: [3005, 6062] },
        { type: 'tattoo',      color: 'purple',  chance: 1.00,   price: 750,  value: [3006, 6068] },

        { type: 'wheels',      color: 'purple',  chance: 1.00,   price: 750,  value: 11 },
        { type: 'wheels',      color: 'purple',  chance: 1.00,   price: 750,  value: 4  },
        { type: 'wheels',      color: 'purple',  chance: 1.00,   price: 750,  value: 10 },
        { type: 'wheels',      color: 'purple',  chance: 1.00,   price: 750,  value: 3  },
        { type: 'wheels',      color: 'purple',  chance: 1.00,   price: 750,  value: 13 },
        { type: 'wheels',      color: 'purple',  chance: 1.00,   price: 750,  value: 6  },
        { type: 'wheels',      color: 'purple',  chance: 1.00,   price: 750,  value: 14 },
        { type: 'wheels',      color: 'purple',  chance: 1.00,   price: 750,  value: 7  },

        { type: 'weaponSkin',  color: 'purple',  chance: 1.00,   price: 750,  value: { entityName: 'weapon_heavyshotgun', skinId: 6 } },
        { type: 'weaponSkin',  color: 'purple',  chance: 1.00,   price: 750,  value: { entityName: 'weapon_carbinerifle', skinId: 2 } },
        { type: 'weaponSkin',  color: 'purple',  chance: 1.00,   price: 750,  value: { entityName: 'weapon_heavyshotgun', skinId: 8 } },
        { type: 'weaponSkin',  color: 'purple',  chance: 1.00,   price: 750,  value: { entityName: 'weapon_combatpdw',    skinId: 3 } },
        { type: 'weaponSkin',  color: 'purple',  chance: 1.00,   price: 750,  value: { entityName: 'weapon_heavyshotgun', skinId: 7 } },
        { type: 'weaponSkin',  color: 'purple',  chance: 1.00,   price: 750,  value: { entityName: 'weapon_revolver',     skinId: 3 } },
        { type: 'weaponSkin',  color: 'purple',  chance: 1.00,   price: 750,  value: { entityName: 'weapon_heavyshotgun', skinId: 5 } },

        { type: 'clothes',     color: 'purple',  chance: 1.00,   price: 750,  value: [{ gender: 0, component: 11, drawable: 2246, texture: 1,  isProp: 0 }, { gender: 1, component: 11, drawable: 2255, texture: 0,  isProp: 0 }] },
        { type: 'clothes',     color: 'purple',  chance: 1.00,   price: 750,  value: [{ gender: 0, component: 7,  drawable: 2072, texture: 0,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2062, texture: 0,  isProp: 0 }] },
        { type: 'clothes',     color: 'purple',  chance: 1.00,   price: 750,  value: [{ gender: 0, component: 1,  drawable: 2009, texture: 0,  isProp: 1 }, { gender: 1, component: 1,  drawable: 2009, texture: 0,  isProp: 1 }] },
        { type: 'clothes',     color: 'purple',  chance: 1.00,   price: 750,  value: [{ gender: 0, component: 7,  drawable: 2074, texture: 0,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2064, texture: 0,  isProp: 0 }] },
        { type: 'clothes',     color: 'purple',  chance: 1.00,   price: 750,  value: [{ gender: 0, component: 11, drawable: 2247, texture: 10, isProp: 0 }, { gender: 1, component: 11, drawable: 2257, texture: 0,  isProp: 0 }] },
        { type: 'clothes',     color: 'purple',  chance: 1.00,   price: 750,  value: [{ gender: 0, component: 4,  drawable: 2145, texture: 6,  isProp: 0 }, { gender: 1, component: 0,  drawable: 2051, texture: 0,  isProp: 1 }] },
        { type: 'clothes',     color: 'purple',  chance: 1.00,   price: 750,  value: [{ gender: 0, component: 11, drawable: 2248, texture: 7,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2061, texture: 3,  isProp: 0 }] },
        { type: 'clothes',     color: 'purple',  chance: 1.00,   price: 750,  value: [{ gender: 0, component: 11, drawable: 2246, texture: 17, isProp: 0 }, { gender: 1, component: 11, drawable: 2255, texture: 6,  isProp: 0 }] },
        { type: 'clothes',     color: 'purple',  chance: 1.00,   price: 750,  value: [{ gender: 0, component: 11, drawable: 2248, texture: 13, isProp: 0 }, { gender: 1, component: 6,  drawable: 2062, texture: 1,  isProp: 0 }] },


        // red
        { type: 'item',        color: 'red',     chance: 1.00,   price: 2000, value: { itemId: 279, count: 0 } }, // Тяжелая снайперская винтовка

        // { type: 'status',      color: 'red',     chance: 1.00,   price: 1500, value: { type: 'Platinum', days: 90 } },

        { type: 'animation',   color: 'red',     chance: 1.00,   price: 1500, value: 90 },

        { type: 'weaponSkin',  color: 'red',     chance: 1.00,   price: 1500, value: { entityName: 'weapon_carbinerifle',     skinId: 3 } },
        { type: 'weaponSkin',  color: 'red',     chance: 1.00,   price: 1500, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 1 } },
        { type: 'weaponSkin',  color: 'red',     chance: 1.00,   price: 1500, value: { entityName: 'weapon_heavyshotgun',     skinId: 3 } },

        { type: 'clothes',     color: 'red',     chance: 1.00,   price: 1000, value: [{ gender: 0, component: 11, drawable: 2249, texture: 0,  isProp: 0 }, { gender: 1, component: 11, drawable: 2258, texture: 7,  isProp: 0 }] },
        { type: 'clothes',     color: 'red',     chance: 1.00,   price: 1000, value: [{ gender: 0, component: 4,  drawable: 2146, texture: 0,  isProp: 0 }, { gender: 1, component: 1,  drawable: 2010, texture: 0,  isProp: 1 }] },
        { type: 'clothes',     color: 'red',     chance: 1.00,   price: 1000, value: [{ gender: 0, component: 5,  drawable: 2017, texture: 3,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2018, texture: 4,  isProp: 0 }] },
        { type: 'clothes',     color: 'red',     chance: 1.00,   price: 1000, value: [{ gender: 0, component: 7,  drawable: 2072, texture: 2,  isProp: 0 }, { gender: 1, component: 0,  drawable: 2051, texture: 2,  isProp: 1 }] },
        { type: 'clothes',     color: 'red',     chance: 1.00,   price: 1000, value: [{ gender: 0, component: 4,  drawable: 2146, texture: 1,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2061, texture: 2,  isProp: 0 }] },
        { type: 'clothes',     color: 'red',     chance: 1.00,   price: 1000, value: [{ gender: 0, component: 11, drawable: 2248, texture: 9,  isProp: 0 }, { gender: 1, component: 4,  drawable: 2152, texture: 7,  isProp: 0 }] },
        { type: 'clothes',     color: 'red',     chance: 1.00,   price: 1000, value: [{ gender: 0, component: 7,  drawable: 2074, texture: 1,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2064, texture: 1,  isProp: 0 }] },

        { type: 'vehicleSet',  color: 'red',     chance: 1.00,   price: 2000, value: { model: 'supragr', setId: 1, group: 19 } },
        { type: 'vehicleSet',  color: 'red',     chance: 1.00,   price: 2500, value: { model: 'i8',      setId: 1, group: 20 } },
        { type: 'vehicleSet',  color: 'red',     chance: 1.00,   price: 2500, value: { model: 'rs72',    setId: 1, group: 21 } },

        { type: 'vehicle',     color: 'red',     chance: 1.00,   price: 3000, customPrice: 1500000, value: 'f40' },

        // { type: 'status',      color: 'red',     chance: 1.00,   price: 1000,     value: { type: 'Platinum', days: 90 } },


        // gold
        { type: 'item',        color: 'gold',    chance: 1.00,   price: 5000, value: { itemId: 328, count: 0 } }, // Тяжелая снайперская винтовка Mk2

        { type: 'animation',   color: 'gold',    chance: 1.00,   price: 3000, value: 81 },

        { type: 'weaponSkin',  color: 'gold',    chance: 1.00,   price: 3000, value: { entityName: 'weapon_heavyshotgun', skinId: 0 } },

        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 7,  drawable: 2001, texture: 0,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2002, texture: 0,  isProp: 0 }] },
        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 7,  drawable: 2001, texture: 3,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2002, texture: 2,  isProp: 0 }] },
        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 7,  drawable: 2074, texture: 0,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2064, texture: 0,  isProp: 0 }] },
        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 7,  drawable: 2027, texture: 0,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2016, texture: 0,  isProp: 0 }] },
        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 11, drawable: 2249, texture: 1,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2062, texture: 2,  isProp: 0 }] },
        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 6,  drawable: 2058, texture: 4,  isProp: 0 }, { gender: 1, component: 6,  drawable: 2062, texture: 4,  isProp: 0 }] },
        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 7,  drawable: 2029, texture: 1,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2012, texture: 1,  isProp: 0 }] },
        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 7,  drawable: 2073, texture: 0,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2063, texture: 0,  isProp: 0 }] },
        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 4,  drawable: 2145, texture: 8,  isProp: 0 }, { gender: 1, component: 11, drawable: 2257, texture: 7,  isProp: 0 }] },
        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 7,  drawable: 2029, texture: 2,  isProp: 0 }, { gender: 1, component: 7,  drawable: 2012, texture: 2,  isProp: 0 }] },
        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 11, drawable: 2249, texture: 3,  isProp: 0 }, { gender: 1, component: 11, drawable: 2259, texture: 7,  isProp: 0 }] },
        { type: 'clothes',     color: 'gold',    chance: 1.00,   price: 3000, value: [{ gender: 0, component: 5,  drawable: 2015, texture: 3,  isProp: 0 }, { gender: 1, component: 5,  drawable: 2016, texture: 4,  isProp: 0 }] },

        { type: 'vehicleSet',  color: 'gold',    chance: 10.00,  price: 7000,  value: { model: 'gle63',    setId: 1, group: 22 } },
        { type: 'vehicleSet',  color: 'gold',    chance: 7.00,   price: 10000, value: { model: '488pista', setId: 1, group: 23 } },

        { type: 'vehicle',     color: 'gold',    chance: 5.00,   price: 15000, customPrice: 3000000, value: 'dawn'       },
        { type: 'vehicle',     color: 'gold',    chance: 4.00,   price: 25000, customPrice: 2500000, value: 'fordgt'     },
        { type: 'vehicle',     color: 'gold',    chance: 3.00,   price: 30000, customPrice: 3500000, value: 'sf90'       },
        { type: 'vehicle',     color: 'gold',    chance: 2.50,   price: 40000, customPrice: 4000000, value: 'centenario' },
        { type: 'vehicle',     color: 'gold',    chance: 1.50,   price: 50000, customPrice: 5000000, value: 'veneno'     },
    ]
}
