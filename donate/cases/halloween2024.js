module.exports = {
    id: 18,
    title: 'donate.config.cases.halloween2024.title',
    // discount: 5,

    start: ['2024-10-01 10:00:00', 'YYYY-MM-DD HH:mm:ss'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    price: 600, // Цена кейса в коинах

    altPrice: 2200, // Цена кейса в альтернативной валюте
    altCurrency: 'halloween', // Тип альтернативной валюты

    sellType: 'halloween', // Тип основной валюты распыления предметов - на время пока ивент активен

    // servers: ['TEST0'],

    transferDays: 30,

    disabledBuy: false,

    readOnly: false,

    colors: [
        {
            color: 'gray',
            chance: 55,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'coins',           chance: 25 },
                { type: 'clothesDiscount', chance: 20 },
                { type: 'vehicleDiscount', chance: 20 },
                { type: 'clothes',         chance: 20 },
            ],
        },
        {
            color: 'blue',
            chance: 23,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'coins',           chance: 25 },
                { type: 'animation',       chance: 20 },
                { type: 'clothesDiscount', chance: 20 },
                { type: 'vehicleDiscount', chance: 20 },
                { type: 'clothes',         chance: 20 },
            ],
        },
        {
            color: 'purple',
            chance: 11,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'coins',           chance: 25 },
                { type: 'animation',       chance: 20 },
                { type: 'clothesDiscount', chance: 25 },
                { type: 'vehicleDiscount', chance: 20 },
                { type: 'clothes',         chance: 15 },
                { type: 'armourSkin',      chance: 10 },
                { type: 'weaponSkin',      chance: 10 },
                { type: 'clothesPack',     chance: 8 },
            ],
        },
        {
            color: 'red',
            chance: 6,
            types: [
                { type: 'money',           chance: 20 },
                { type: 'coins',           chance: 20 },
                { type: 'animation',       chance: 20 },
                { type: 'clothesDiscount', chance: 20 },
                { type: 'vehicleDiscount', chance: 15 },
                { type: 'clothes',         chance: 15 },
                { type: 'armourSkin',      chance: 15 },
                { type: 'weaponSkin',      chance: 15 },
                { type: 'clothesPack',     chance: 10, genderOnly: 0 },
            ],
        },
        {
            color: 'gold',
            chance: 3.5,
            types: [
                { type: 'money',           chance: 25 },
                { type: 'coins',           chance: 20 },
                { type: 'weaponSkin',      chance: 20 },
                { type: 'armourSkin',      chance: 20 },
                { type: 'animation',       chance: 20 },
                { type: 'clothesDiscount', chance: 20 },
                { type: 'vehicleDiscount', chance: 15 },
                { type: 'clothes',         chance: 15 },
                { type: 'vehicle',         chance: 10 },
                { type: 'clothesPack',     chance: 8, genderOnly: 0 },
            ],
        },
        {
            color: 'unique',
            chance: 0.8,
            types: [
                { type: 'clothes',         chance: 15 },
                { type: 'vehicle',         chance: 10 },
                { type: 'clothesPack',     chance: 12 },
            ],
        }
    ],

    caseContent: [
	    // Анимации
	    { type: 'animation',       color: 'blue',    chance: 17,   price: 800,    value: 358 }, // MiracleTrickshot
	    { type: 'animation',       color: 'blue',    chance: 17,   price: 800,    value: 360 }, // Pickin
	    { type: 'animation',       color: 'blue',    chance: 17,   price: 800,    value: 45 }, // Tidy

	    { type: 'animation',       color: 'purple',  chance: 14,   price: 2000,    value: 356 }, // LiveFromHaddonfield
	    { type: 'animation',       color: 'purple',  chance: 17,   price: 2000,    value: 357 }, // ItIsATrick

	    { type: 'animation',       color: 'red',     chance: 11,   price: 3500,   value: 359 }, // ReaperShowtime

	    { type: 'animation',       color: 'gold',    chance: 8,    price: 8000,   value: 313 }, // WitchWay

        // Clothes Packs
        {
            type: 'clothesPack',
            color: 'purple',
            chance: 1.0,
            price: 2500,
            value: {
              preview: 'PrisonerMugshotM_preview',
              name: 'prisonerm_pack',
              textures: 1,
              items: [
                { type: 'animation', value: 362 },
                { type: 'clothes', value: { gender: 0, component: 11, drawable: 2473, isProp: 0 } },
                { type: 'clothes', value: { gender: 0, component: 4, drawable: 2246, isProp: 0 } },
              ],
            },
        },
        {
            type: 'clothesPack',
            color: 'purple',
            chance: 1.0,
            price: 1600,
            value: {
              preview: 'PrisonerMugshotF_preview',
              name: 'prisonerf_pack',
              textures: 1,
              items: [
                { type: 'animation', value: 363 },
                { type: 'clothes', value: { gender: 1, component: 11, drawable: 2475, isProp: 0 } },
                { type: 'clothes', value: { gender: 1, component: 4, drawable: 2264, isProp: 0 } },
              ],
            },
        },
        {
            type: 'clothesPack',
            color: 'red',
            chance: 1.0,
            price: 5000,
            value: {
              preview: 'RonaldGreeting_preview',
              name: 'ronald_pack',
              textures: 2,
              items: [
                { type: 'animation', value: 366 },
                { type: 'clothes', value: { gender: 0, component: 11, drawable: 2470, isProp: 0 } },
                { type: 'clothes', value: { gender: 0, component: 4, drawable: 2245, isProp: 0 } },
                { type: 'clothes', value: { gender: 0, component: 1, drawable: 2161, isProp: 0 } },
              ],
            },
        },
        {
            type: 'clothesPack',
            color: 'gold',
            chance: 1.0,
            price: 20000,
            value: {
              preview: 'PilaTricycle_preview',
              name: 'pila_pack',
              textures: 1,
              items: [
                { type: 'animation', value: 361 },
                { type: 'clothes', value: { gender: 0, component: 11, drawable: 2476, isProp: 0 } },
                { type: 'clothes', value: { gender: 0, component: 4, drawable: 2247, isProp: 0 } },
                { type: 'clothes', value: { gender: 0, component: 1, drawable: 2162, isProp: 0 } },
              ],
            },
        },
        {
            type: 'clothesPack',
            color: 'unique',
            chance: 1.0,
            price: 35000,
            value: {
              preview: 'SamuraiStrike_preview',
              name: 'samurai_pack',
              textures: 3,
              items: [
                { type: 'animation', value: 364 },
                { type: 'clothes', value: { gender: 0, component: 11, drawable: 2477, isProp: 0 } },
                { type: 'clothes', value: { gender: 0, component: 4, drawable: 2248, isProp: 0 } },
                { type: 'clothes', value: { gender: 0, component: 6, drawable: 2126, isProp: 0 } },
                { type: 'clothes', value: { gender: 0, component: 1, drawable: 2163, isProp: 0 } },
                { type: 'clothes', value: { gender: 0, component: 0, drawable: 2101, isProp: 1 } },
              ],
            },
        },
        {
            type: 'clothesPack',
            color: 'unique',
            chance: 1.0,
            price: 35000,
            value: {
              preview: 'KiotoFan_preview',
              name: 'kiotofan_pack',
              textures: 3,
              items: [
                { type: 'animation', value: 365 },
                { type: 'clothes', value: { gender: 1, component: 11, drawable: 2476, isProp: 0 } },
                { type: 'clothes', value: { gender: 1, component: 6, drawable: 2149, isProp: 0 } },
                { type: 'clothes', value: { gender: 1, component: 1, drawable: 2150, isProp: 0 } },
              ],
            },
        },

        // Clothes
        { type: 'clothes',      color: 'gray',   chance: 1.00,   price: 250,  value: { gender: 1, component: 6,  drawable: 2148, textures: 6, isProp: 0 } },
        { type: 'clothes',      color: 'gray',   chance: 1.00,   price: 250,  value: { gender: 0, component: 6,  drawable: 2125, textures: 6, isProp: 0 } },

        { type: 'clothes',      color: 'blue',   chance: 1.00,   price: 875,  value: { gender: 0, component: 11,  drawable: 2471, textures: 2, isProp: 0 } },
        { type: 'clothes',      color: 'blue',   chance: 1.00,   price: 875,  value: { gender: 1, component: 11,  drawable: 2473, textures: 2, isProp: 0 } },
        { type: 'clothes',      color: 'blue',   chance: 1.00,   price: 875,  value: { gender: 0, component: 0,  drawable: 2097, textures: 6, isProp: 1 } },
        { type: 'clothes',      color: 'blue',   chance: 1.00,   price: 875,  value: { gender: 1, component: 0,  drawable: 2102, textures: 6, isProp: 1 } },

        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 2100,  value: { gender: 1, component: 0,  drawable: 2101, textures: 5, isProp: 1 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 2100,  value: { gender: 0, component: 0,  drawable: 2096, textures: 5, isProp: 1 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 2100,  value: { gender: 0, component: 11,  drawable: 2474, textures: 3, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 2100,  value: { gender: 1, component: 10,  drawable: 2101, texture: 0, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 2100,  value: { gender: 0, component: 10,  drawable: 2100, texture: 0, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 2100,  value: { gender: 1, component: 11,  drawable: 2474, textures: 5, isProp: 0 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 2100,  value: { gender: 1, component: 0,  drawable: 2100, textures: 8, isProp: 1 } },
        { type: 'clothes',      color: 'purple',   chance: 1.00,   price: 2100,  value: { gender: 0, component: 0,  drawable: 2095, textures: 8, isProp: 1 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 3500,  value: { gender: 1, component: 11,  drawable: 2472, textures: 5, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 3500,  value: { gender: 0, component: 11,  drawable: 2472, textures: 4, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 3500,  value: { gender: 0, component: 6,  drawable: 2124, textures: 6, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 3500,  value: { gender: 1, component: 6,  drawable: 2147, textures: 5, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 3500,  value: { gender: 1, component: 1,  drawable: 2148, textures: 2, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 3500,  value: { gender: 0, component: 5,  drawable: 2094, textures: 4, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 3500,  value: { gender: 1, component: 5,  drawable: 2098, textures: 4, isProp: 0 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 3500,  value: { gender: 0, component: 0,  drawable: 2098, texture: 0, isProp: 1 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 3500,  value: { gender: 1, component: 0,  drawable: 2103, texture: 0, isProp: 1 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 3500,  value: { gender: 0, component: 2,  drawable: 2004, textures: 5, isProp: 1 } },
        { type: 'clothes',      color: 'red',   chance: 1.00,   price: 3500,  value: { gender: 1, component: 2,  drawable: 2028, textures: 5, isProp: 1 } },

        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 8750,  value: { gender: 1, component: 1,  drawable: 2149, texture: 0, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 8750,  value: { gender: 1, component: 11,  drawable: 2471, textures: 5, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 8750,  value: { gender: 1, component: 4,  drawable: 2263, textures: 5, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 8750,  value: { gender: 0, component: 11,  drawable: 2469, textures: 6, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 8750,  value: { gender: 0, component: 5,  drawable: 2093, textures: 9, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 8750,  value: { gender: 1, component: 5,  drawable: 2097, textures: 9, isProp: 0 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 8750,  value: { gender: 0, component: 0,  drawable: 2099, texture: 0, isProp: 1 } },
        { type: 'clothes',      color: 'gold',   chance: 1.00,   price: 8750,  value: { gender: 1, component: 0,  drawable: 2104, texture: 0, isProp: 1 } },

        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 30000,  value: { gender: 1, component: 3,  drawable: 2386, textures: 6, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 30000,  value: { gender: 0, component: 3,  drawable: 2055, textures: 6, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 30000,  value: { gender: 1, component: 5,  drawable: 2096, textures: 8, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 30000,  value: { gender: 0, component: 5,  drawable: 2092, textures: 8, isProp: 0 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 30000,  value: { gender: 1, component: 0,  drawable: 2099, textures: 4, isProp: 1 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 30000,  value: { gender: 1, component: 0,  drawable: 2105, texture: 0, isProp: 1 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 30000,  value: { gender: 0, component: 0,  drawable: 2100, texture: 0, isProp: 1 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 30000,  value: { gender: 1, component: 2,  drawable: 2027, textures: 7, isProp: 1 } },
        { type: 'clothes',      color: 'unique',   chance: 1.00,   price: 30000,  value: { gender: 0, component: 2,  drawable: 2003, textures: 7, isProp: 1 } },


        // weaponSkins Ghost
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 2100, value: { entityName: 'weapon_tecpistol', skinId: 7 } },
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 2100, value: { entityName: 'weapon_gusenberg', skinId: 12 } },

        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 3500, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 19 } },
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 3500, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 20 } },
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 3500, value: { entityName: 'weapon_heavyshotgun', skinId: 23 } },
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 3500, value: { entityName: 'weapon_tacticalrifle', skinId: 7 } },

        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 8750, value: { entityName: 'weapon_heavyrifle', skinId: 7 } },
        { type: 'weaponSkin', color: 'gold', chance: 0.85, price: 8750, value: { entityName: 'weapon_heavysniper_mk2', skinId: 18 } },
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 8750, value: { entityName: 'weapon_combatmg_mk2', skinId: 7 } },


        // weaponSkins Infinite
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 2100, value: { entityName: 'weapon_tecpistol', skinId: 8 } },
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 2100, value: { entityName: 'weapon_gusenberg', skinId: 13 } },

        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 3500, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 20    } },
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 3500, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 21 } },
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 3500, value: { entityName: 'weapon_heavyshotgun', skinId: 24 } },
        { type: 'weaponSkin', color: 'red', chance: 0.90, price: 3500, value: { entityName: 'weapon_tacticalrifle', skinId: 8 } },

        { type: 'weaponSkin', color: 'gold', chance: 0.85, price: 8750, value: { entityName: 'weapon_heavysniper_mk2', skinId: 19 } },
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 8750, value: { entityName: 'weapon_heavyrifle', skinId: 8 } },
        { type: 'weaponSkin', color: 'gold', chance: 0.90, price: 8750, value: { entityName: 'weapon_combatmg_mk2', skinId: 8 } },

        // armourSkins
        { type: 'armourSkin', color: 'purple', chance: 0.85, price: 2100, value: { entityName: 'light', skinId: 123 } },

        { type: 'armourSkin', color: 'red', chance: 0.95, price: 3500, value: { entityName: 'light', skinId: 126 } },
        { type: 'armourSkin', color: 'red', chance: 0.95, price: 3500, value: { entityName: 'light', skinId: 129 } },
        { type: 'armourSkin', color: 'red', chance: 0.95, price: 3500, value: { entityName: 'light', skinId: 131 } },

        { type: 'armourSkin', color: 'gold', chance: 0.65, price: 8750, value: { entityName: 'light', skinId: 124 } },
        { type: 'armourSkin', color: 'gold', chance: 0.65, price: 8750, value: { entityName: 'light', skinId: 125 } },
        { type: 'armourSkin', color: 'gold', chance: 0.65, price: 8750, value: { entityName: 'light', skinId: 127 } },
        { type: 'armourSkin', color: 'gold', chance: 0.65, price: 8750, value: { entityName: 'light', skinId: 128 } },
        { type: 'armourSkin', color: 'gold', chance: 0.65, price: 8750, value: { entityName: 'light', skinId: 130 } },


		// Vehicles
        { type: 'vehicle',    color: 'gold',      chance: 25,   price: 10000,  customPrice: 250000,   value: 'wrangler'  },
        { type: 'vehicle',    color: 'gold',      chance: 13,   price: 15000,  customPrice: 625000,   value: 'firebird'  },
        { type: 'vehicle',    color: 'gold',      chance: 5,    price: 20000,  customPrice: 1250000,  value: 'm12b'  },

        { type: 'vehicle',    color: 'unique',    chance: 8,    price: 30000,  customPrice: 2000000,  value: 'turbor'  },
        { type: 'vehicle',    color: 'unique',    chance: 5,    price: 35000, customPrice: 3250000,  value: 'regalia'  },


        // Скидка на транспорт
        { type: 'vehicleDiscount', color: 'gray',   chance: 50.00, price: 525,  value: 5  },
        { type: 'vehicleDiscount', color: 'blue',   chance: 30.00, price: 875,  value: 10 },
        { type: 'vehicleDiscount', color: 'purple', chance: 15.00, price: 2100,  value: 15 },
        { type: 'vehicleDiscount', color: 'red',    chance: 4.00,  price: 3500,  value: 20 },
        { type: 'vehicleDiscount', color: 'gold',   chance: 1.00,  price: 8750, value: 25 },

        // Скидка на одежду
        { type: 'clothesDiscount', color: 'gray',   chance: 50.00, price: 525,  value: 5  },
        { type: 'clothesDiscount', color: 'blue',   chance: 30.00, price: 875,  value: 10 },
        { type: 'clothesDiscount', color: 'purple', chance: 15.00, price: 2100,  value: 15 },
        { type: 'clothesDiscount', color: 'red',    chance: 4.00,  price: 3500,  value: 20 },
        { type: 'clothesDiscount', color: 'gold',   chance: 1.00,  price: 8750, value: 25 },

        // Вирты
        { type: 'money',   color: 'gray',           chance: 50.00, price: 525,  value: 15000  },
        { type: 'money',   color: 'blue',           chance: 30.00, price: 875,  value: 25000  },
        { type: 'money',   color: 'purple',         chance: 15.00, price: 2100, value: 60000  },
        { type: 'money',   color: 'red',            chance: 4.00,  price: 3500, value: 100000 },
        { type: 'money',   color: 'gold',           chance: 1.00,  price: 8750, value: 250000 },

        // Коины
        { type: 'coins',   color: 'gray',           chance: 50.00, price: 525,  value: 150  },
        { type: 'coins',   color: 'blue',           chance: 30.00, price: 875,  value: 250  },
        { type: 'coins',   color: 'purple',         chance: 15.00, price: 2100, value: 600  },
        { type: 'coins',   color: 'red',            chance: 4.00,  price: 3500, value: 1000 },
        { type: 'coins',   color: 'gold',           chance: 1.00,  price: 8750, value: 2500 },
    ]
}
