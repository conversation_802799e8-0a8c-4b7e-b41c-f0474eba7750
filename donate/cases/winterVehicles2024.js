module.exports = {
    id: 10,
    title: 'donate.config.cases.winterVehicles2024.title',

    start: ['2023-11-26', 'YYYY-MM-DD'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    seasonPassId: 4,

    // disabledBuy: true,
    readOnly: false,

    // Сумма распыления указан в опыте батлпаса. Если батлпас активен
    sellRule: 40, // Если батлпас уже не активен, то задать процент конвертации price в коины. Например 100 exp / 100 * 25 = 25 коинов

    price: 1500,
    transferDays: 30,

    // discount: 5,

    specificEnd: {
        'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
        'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    },

    colors: [
        {
            color: 'gray',
            chance: 51,
        },
        {
            color: 'blue',
            chance: 20,
        },
        {
            color: 'purple',
            chance: 14,
        },
        {
            color: 'red',
            chance: 6,
        },
        {
            color: 'gold',
            chance: 2.5,
        },
        {
            color: 'unique',
            chance: 0.5,
        },

    ],

    caseContent: [
        { type: 'vehicle',         color: 'gray', chance: 60,      price: 600, customPrice: 27100, value: 'tornado5'    },
        { type: 'vehicle',         color: 'gray', chance: 60,      price: 600, customPrice: 60000, value: 'clique2'     },
        { type: 'vehicle',         color: 'gray', chance: 58,      price: 650, customPrice: 27000, value: 'blista3'     },
        { type: 'vehicle',         color: 'gray', chance: 58,      price: 650, customPrice: 39000, value: 'stalion2'    },
        { type: 'vehicle',         color: 'gray', chance: 56,      price: 700, customPrice: 41000, value: 'penumbra'    },
        { type: 'vehicle',         color: 'gray', chance: 56,      price: 700, customPrice: 88000, value: 'omnis'       },
        { type: 'vehicle',         color: 'gray', chance: 56,      price: 700, customPrice: 105000, value: 'raptor'      },
        { type: 'vehicle',         color: 'gray', chance: 56,      price: 700, customPrice: 100000, value: 'ratel'       },
        { type: 'vehicle',         color: 'gray', chance: 54,      price: 750, customPrice: 185000, value: 'monstrociti' },
        { type: 'vehicle',         color: 'gray', chance: 54,      price: 750, customPrice: 123700, value: 'gt500'       },

        { type: 'vehicle',         color: 'blue', chance: 50,      price: 900,  customPrice: 450000, value: 'banshee2'  },
        { type: 'vehicle',         color: 'blue', chance: 48,      price: 1000, customPrice: 240000, value: 'sc1'       },
        { type: 'vehicle',         color: 'blue', chance: 48,      price: 1000, customPrice: 102500, value: 'comet4'    },
        { type: 'vehicle',         color: 'blue', chance: 46,      price: 1100, customPrice: 410000, value: 'gauntlet6' },
        { type: 'vehicle',         color: 'blue', chance: 46,      price: 1100, customPrice: 320000, value: 'buffalo5'  },
        { type: 'vehicle',         color: 'blue', chance: 44,      price: 1200, customPrice: 800000, value: 'stingertt' },
        { type: 'vehicle',         color: 'blue', chance: 44,      price: 1200, customPrice: 97000,  value: 'hotring'   },
        { type: 'vehicle',         color: 'blue', chance: 42,      price: 1300, customPrice: 1350000, value: 'krieger'   },
        { type: 'vehicle',         color: 'blue', chance: 42,      price: 1300, customPrice: 247000, value: 'vagner'    },
        { type: 'vehicle',         color: 'blue', chance: 40,      price: 1400, customPrice: 1000000, value: 'zentorno'  },


        { type: 'vehicle',         color: 'purple', chance: 47,    price: 1500, customPrice: 600000, value: 'yz450f' },
        //{ type: 'vehicleMod',      color: 'purple', chance: 40,    price: 1600, customPrice: 600000, value: { model: 'yz450f',    setId: 1, group: 103  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 40,    price: 1700, customPrice: 600000, value: { model: 'yz450f',    setId: 2, group: 103  } },

        { type: 'vehicle',         color: 'purple', chance: 10,    price: 1900, customPrice: 850000, value: '190e' },
        { type: 'vehicleMod',      color: 'purple', chance: 9,     price: 2000, customPrice: 850000, value: { model: '190e',    setId: 3, group: 104  }  },
        { type: 'vehicleMod',      color: 'purple', chance: 9,     price: 2100, customPrice: 850000, value: { model: '190e',    setId: 4, group: 104  }  },
        //{ type: 'vehicleMod',      color: 'purple', chance: 9,     price: 2200, customPrice: 850000, value: { model: '190e',    setId: 5, group: 104  }  },
        //{ type: 'vehicleMod',      color: 'purple', chance: 9,     price: 2300, customPrice: 850000, value: { model: '190e',    setId: 6, group: 104  }  },
        { type: 'vehicleMod',      color: 'purple', chance: 9,     price: 2100, customPrice: 850000, value: { model: '190e',    setId: 7, group: 104  }  },
        //{ type: 'vehicleMod',      color: 'purple', chance: 9,     price: 2200, customPrice: 850000, value: { model: '190e',    setId: 8, group: 104  }  },
        //{ type: 'vehicleMod',      color: 'purple', chance: 9,     price: 2300, customPrice: 850000, value: { model: '190e',    setId: 9, group: 104  }  },
        { type: 'vehicleMod',      color: 'purple', chance: 9,     price: 2200, customPrice: 850000, value: { model: '190e',    setId: 14, group: 104  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 9,     price: 2300, customPrice: 850000, value: { model: '190e',    setId: 15, group: 104  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 9,     price: 2400, customPrice: 850000, value: { model: '190e',    setId: 16 , group: 104 } },

        { type: 'vehicle',         color: 'purple', chance: 28,    price: 1900, customPrice: 980000, value: 'charger2' },
        //{ type: 'vehicleMod',      color: 'purple', chance: 23,    price: 2000, customPrice: 980000, value: { model: 'charger2',    setId: 1, group: 105  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 23,    price: 2100, customPrice: 980000, value: { model: 'charger2',    setId: 2, group: 105  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 23,    price: 2200, customPrice: 980000, value: { model: 'charger2',    setId: 3, group: 105  } },

        { type: 'vehicle',         color: 'purple', chance: 10,    price: 2000, customPrice: 1200000, value: 'nsx2' },
        //{ type: 'vehicleMod',      color: 'purple', chance: 8,     price: 2100, customPrice: 1200000, value: { model: 'nsx2',    setId: 1, group: 106  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 8,     price: 2200, customPrice: 1200000, value: { model: 'nsx2',    setId: 2, group: 106  } },
        { type: 'vehicleMod',      color: 'purple', chance: 8,     price: 2000, customPrice: 1200000, value: { model: 'nsx2',    setId: 3, group: 106  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 8,     price: 2250, customPrice: 1200000, value: { model: 'nsx2',    setId: 4, group: 106  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 8,     price: 2500, customPrice: 1200000, value: { model: 'nsx2',    setId: 5, group: 106  } },
        { type: 'vehicleMod',      color: 'purple', chance: 8,     price: 2000, customPrice: 1200000, value: { model: 'nsx2',    setId: 6, group: 106  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 8,     price: 2250, customPrice: 1200000, value: { model: 'nsx2',    setId: 7, group: 106  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 8,     price: 2500, customPrice: 1200000, value: { model: 'nsx2',    setId: 8, group: 106  } },
        { type: 'vehicleMod',      color: 'purple', chance: 8,     price: 2500, customPrice: 1200000, value: { model: 'nsx2',    setId: 9, group: 106  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 8,     price: 2750, customPrice: 1200000, value: { model: 'nsx2',    setId: 10, group: 106  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 8,     price: 3000, customPrice: 1200000, value: { model: 'nsx2',    setId: 11, group: 106  } },

        { type: 'vehicle',         color: 'purple', chance: 8,     price: 2000, customPrice: 1200000, value: 'gtr32' },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2100, customPrice: 1200000, value: { model: 'gtr32',    setId: 1, group: 107  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2200, customPrice: 1200000, value: { model: 'gtr32',    setId: 2, group: 107  } },
        { type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2000, customPrice: 1200000, value: { model: 'gtr32',    setId: 3, group: 107  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2250, customPrice: 1200000, value: { model: 'gtr32',    setId: 4, group: 107  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2500, customPrice: 1200000, value: { model: 'gtr32',    setId: 5, group: 107  } },
        { type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2000, customPrice: 1200000, value: { model: 'gtr32',    setId: 6, group: 107  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2250, customPrice: 1200000, value: { model: 'gtr32',    setId: 7, group: 107  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2500, customPrice: 1200000, value: { model: 'gtr32',    setId: 8, group: 107  } },
        { type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2500, customPrice: 1200000, value: { model: 'gtr32',    setId: 9, group: 107  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2750, customPrice: 1200000, value: { model: 'gtr32',    setId: 10, group: 107  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 3000, customPrice: 1200000, value: { model: 'gtr32',    setId: 11, group: 107  } },
        { type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2500, customPrice: 1200000, value: { model: 'gtr32',    setId: 12, group: 107  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2750, customPrice: 1200000, value: { model: 'gtr32',    setId: 13, group: 107  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 3000, customPrice: 1200000, value: { model: 'gtr32',    setId: 14, group: 107  } },
        { type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2500, customPrice: 1200000, value: { model: 'gtr32',    setId: 15, group: 107  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 2750, customPrice: 1200000, value: { model: 'gtr32',    setId: 16, group: 107  } },
        //{ type: 'vehicleMod',      color: 'purple', chance: 4.5,   price: 3000, customPrice: 1200000, value: { model: 'gtr32',    setId: 17, group: 107  } },

        { type: 'vehicleMod', color: 'red', chance: 23, price: 3000, customPrice: 850000, value: { model: '190e', setId: 10, group: 108 } },
        //{ type: 'vehicleMod', color: 'red', chance: 22, price: 3250, customPrice: 850000, value: { model: '190e', setId: 11, group: 108 } },
        //{ type: 'vehicleMod', color: 'red', chance: 21, price: 3500, customPrice: 850000, value: { model: '190e', setId: 12, group: 108 } },
        //{ type: 'vehicleMod', color: 'red', chance: 21, price: 3750, customPrice: 850000, value: { model: '190e', setId: 13, group: 108 } },
        { type: 'vehicleMod', color: 'red', chance: 21, price: 3500, customPrice: 850000, value: { model: '190e', setId: 17, group: 108 } },
        //{ type: 'vehicleMod', color: 'red', chance: 21, price: 3750, customPrice: 850000, value: { model: '190e', setId: 18, group: 108 } },
        //{ type: 'vehicleMod', color: 'red', chance: 21, price: 4000, customPrice: 850000, value: { model: '190e', setId: 19, group: 108 } },
        //{ type: 'vehicleMod', color: 'red', chance: 17, price: 8000, customPrice: 850000, value: { model: '190e', setId: 20, group: 108 } },

        { type: 'vehicleMod', color: 'red', chance: 27, price: 5000, customPrice: 1200000, value: { model: 'gtr32', setId: 18, group: 109 } },
        //{ type: 'vehicleMod', color: 'red', chance: 26, price: 5500, customPrice: 1200000, value: { model: 'gtr32', setId: 19, group: 109 } },
        //{ type: 'vehicleMod', color: 'red', chance: 25, price: 6000, customPrice: 1200000, value: { model: 'gtr32', setId: 20, group: 109 } },
        { type: 'vehicleMod', color: 'red', chance: 24, price: 6500, customPrice: 1200000, value: { model: 'gtr32', setId: 22, group: 109 } },
        //{ type: 'vehicleMod', color: 'red', chance: 24, price: 7000, customPrice: 1200000, value: { model: 'gtr32', setId: 23, group: 109 } },
        //{ type: 'vehicleMod', color: 'red', chance: 24, price: 7500, customPrice: 1200000, value: { model: 'gtr32', setId: 24, group: 109 } },
        { type: 'vehicleMod', color: 'red', chance: 18, price: 7500, customPrice: 1200000, value: { model: 'gtr32', setId: 21, group: 109 } },

        { type: 'vehicleMod', color: 'red', chance: 46, price: 5000, customPrice: 1200000, value: { model: 'nsx2', setId: 12, group: 110 } },
        //{ type: 'vehicleMod', color: 'red', chance: 45, price: 5500, customPrice: 1200000, value: { model: 'nsx2', setId: 13, group: 110 } },
        //{ type: 'vehicleMod', color: 'red', chance: 44, price: 6000, customPrice: 1200000, value: { model: 'nsx2', setId: 14, group: 110 } },

        { type: 'vehicleMod', color: 'red', chance: 33, price: 6000, customPrice: 980000, value: { model: 'charger2', setId: 4, group: 111 } },
        //{ type: 'vehicleMod', color: 'red', chance: 34, price: 6500, customPrice: 980000, value: { model: 'charger2', setId: 5, group: 111 } },
        { type: 'vehicleMod', color: 'red', chance: 36, price: 5000, customPrice: 980000, value: { model: 'charger2', setId: 6, group: 111 } },
        //{ type: 'vehicleMod', color: 'red', chance: 35, price: 5500, customPrice: 980000, value: { model: 'charger2', setId: 7, group: 111 } },

        { type: 'vehicle', color: 'red', chance: 22, price: 5000, customPrice: 1200000, value: 'aclass2' },
        //{ type: 'vehicleMod', color: 'red', chance: 18, price: 6000, customPrice: 1200000, value: { model: 'aclass2', setId: 1, group: 112 } },
        //{ type: 'vehicleMod', color: 'red', chance: 18, price: 6500, customPrice: 1200000, value: { model: 'aclass2', setId: 2, group: 112 } },
        //{ type: 'vehicleMod', color: 'red', chance: 18, price: 7000, customPrice: 1200000, value: { model: 'aclass2', setId: 3, group: 112 } },
        { type: 'vehicleMod', color: 'red', chance: 18, price: 9000, customPrice: 1200000, value: { model: 'aclass2', setId: 7, group: 112 } },
        //{ type: 'vehicleMod', color: 'red', chance: 17, price: 9500, customPrice: 1200000, value: { model: 'aclass2', setId: 8, group: 112 } },
        //{ type: 'vehicleMod', color: 'red', chance: 17, price: 10000, customPrice: 1200000, value: { model: 'aclass2', setId: 9, group: 112 } },

        { type: 'vehicle', color: 'red', chance: 13, price: 8000, customPrice: 5000000, value: 'escalade2' },
        //{ type: 'vehicleMod', color: 'red', chance: 12, price: 9000, customPrice: 5000000, value: { model: 'escalade2', setId: 1, group: 113 } },
        //{ type: 'vehicleMod', color: 'red', chance: 11, price: 9500, customPrice: 5000000, value: { model: 'escalade2', setId: 2, group: 113 } },
        //{ type: 'vehicleMod', color: 'red', chance: 10, price: 10000, customPrice: 5000000, value: { model: 'escalade2', setId: 3, group: 113 } },
        { type: 'vehicleMod', color: 'gold', chance: 54, price: 11000, customPrice: 5000000, value: { model: 'escalade2', setId: 4, group: 113 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 52, price: 11500, customPrice: 5000000, value: { model: 'escalade2', setId: 5, group: 113 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 50, price: 12000, customPrice: 5000000, value: { model: 'escalade2', setId: 6, group: 113 } },

        { type: 'vehicleMod', color: 'gold', chance: 49, price: 11000, customPrice: 1200000, value: { model: 'aclass2', setId: 4, group: 114 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 48, price: 11500, customPrice: 1200000, value: { model: 'aclass2', setId: 5, group: 114 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 47, price: 12000, customPrice: 1200000, value: { model: 'aclass2', setId: 6, group: 114 } },

        { type: 'vehicle', color: 'gold', chance: 16, price: 14000, customPrice: 12000000, value: 'corvette2' },
        //{ type: 'vehicleMod', color: 'gold', chance: 15, price: 15000, customPrice: 12000000, value: { model: 'corvette2', setId: 1, group: 115 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 15, price: 15500, customPrice: 12000000, value: { model: 'corvette2', setId: 2, group: 115 } },
        { type: 'vehicleMod', color: 'gold', chance: 15, price: 16000, customPrice: 12000000, value: { model: 'corvette2', setId: 3, group: 115 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 14, price: 16500, customPrice: 12000000, value: { model: 'corvette2', setId: 4, group: 115 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 14, price: 17000, customPrice: 12000000, value: { model: 'corvette2', setId: 5, group: 115 } },
        { type: 'vehicleMod', color: 'gold', chance: 14, price: 16000, customPrice: 12000000, value: { model: 'corvette2', setId: 6, group: 115 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 13, price: 16500, customPrice: 12000000, value: { model: 'corvette2', setId: 7, group: 115 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 13, price: 17000, customPrice: 12000000, value: { model: 'corvette2', setId: 8, group: 115 } },

        { type: 'vehicle', color: 'gold', chance: 10, price: 14000, customPrice: 16000000, value: 'regera' },
        //{ type: 'vehicleMod', color: 'gold', chance: 10, price: 15000, customPrice: 16000000, value: { model: 'regera', setId: 1, group: 116 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 9, price: 15500, customPrice: 16000000, value: { model: 'regera', setId: 2, group: 116 } },
        { type: 'vehicleMod', color: 'gold', chance: 9, price: 16000, customPrice: 16000000, value: { model: 'regera', setId: 3, group: 116 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 9, price: 16500, customPrice: 16000000, value: { model: 'regera', setId: 4, group: 116 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 9, price: 17000, customPrice: 16000000, value: { model: 'regera', setId: 5, group: 116 } },
        { type: 'vehicleMod', color: 'gold', chance: 9, price: 16000, customPrice: 16000000, value: { model: 'regera', setId: 6, group: 116 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 9, price: 16500, customPrice: 16000000, value: { model: 'regera', setId: 7, group: 116 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 9, price: 17000, customPrice: 16000000, value: { model: 'regera', setId: 8, group: 116 } },
        { type: 'vehicleMod', color: 'gold', chance: 8, price: 16000, customPrice: 16000000, value: { model: 'regera', setId: 9, group: 116 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 8, price: 16500, customPrice: 16000000, value: { model: 'regera', setId: 10, group: 116 } },
        //{ type: 'vehicleMod', color: 'gold', chance: 8, price: 17000, customPrice: 16000000, value: { model: 'regera', setId: 11, group: 116 } },

        { type: 'vehicleMod', color: 'unique', chance: 5, price: 18000, customPrice: 12000000, value: { model: 'corvette2', setId: 9, group: 117 } },
        //{ type: 'vehicleMod', color: 'unique', chance: 4, price: 18500, customPrice: 12000000, value: { model: 'corvette2', setId: 10, group: 117 } },
        //{ type: 'vehicleMod', color: 'unique', chance: 3, price: 19000, customPrice: 12000000, value: { model: 'corvette2', setId: 11, group: 117 } },

        { type: 'vehicleMod', color: 'unique', chance: 5, price: 18000, customPrice: 16000000, value: { model: 'regera', setId: 12, group: 118 } },
        //{ type: 'vehicleMod', color: 'unique', chance: 4, price: 18500, customPrice: 16000000, value: { model: 'regera', setId: 13, group: 118 } },
        //{ type: 'vehicleMod', color: 'unique', chance: 3, price: 19000, customPrice: 16000000, value: { model: 'regera', setId: 14, group: 118 } },

        { type: 'vehicle',         color: 'unique', chance: 3,   price: 20000, customPrice: 20000000, value: 'utopia' },
    ]
}
