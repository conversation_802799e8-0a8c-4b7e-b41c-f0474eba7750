module.exports = {
    id: 8,
    title: 'donate.config.cases.halloween2023.title',
    // discount: 15,

    start: ['2024-10-01 10:00:00', 'YYYY-MM-DD HH:mm:ss'],
    end:   ['2025-01-02 05:59:00', 'YYYY-MM-DD HH:mm:ss'],

    price: 600, // Цена кейса в коинах

    altPrice: 2200, // Цена кейса в альтернативной валюте
    altCurrency: 'halloween', // Тип альтернативной валюты

    sellType: 'halloween', // Тип основной валюты распыления предметов - на время пока ивент активен

    // servers: ['TEST0'],

    disabledBuy: false,

    readOnly: false,

    colors: [
        {
            color: 'gray',
            chance: 50,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'coins',           chance: 15 },
                { type: 'clothes',         chance: 12 },
                { type: 'clothesDiscount', chance: 6 },
                { type: 'armourSkin',      chance: 5 },
            ],
        },
        {
            color: 'blue',
            chance: 23,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'clothes',         chance: 20 },
                { type: 'coins',           chance: 15 },
                { type: 'armourSkin',      chance: 15 },
                { type: 'weaponSkin',      chance: 10 },
                { type: 'animation',       chance: 10 },
                { type: 'clothesDiscount', chance: 8 },
            ],
        },
        {
            color: 'purple',
            chance: 10,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'clothes',         chance: 20 },
                { type: 'coins',           chance: 15 },
                { type: 'armourSkin',      chance: 15 },
                { type: 'weaponSkin',      chance: 10 },
                { type: 'animation',       chance: 10 },
                { type: 'clothesDiscount', chance: 7 },
                { type: 'vehicle',         chance: 5 },
            ],
        },
        {
            color: 'red',
            chance: 6.5,
            types: [
                { type: 'money',           chance: 30 },
                { type: 'clothes',         chance: 20 },
                { type: 'coins',           chance: 15 },
                { type: 'armourSkin',      chance: 15 },
                { type: 'weaponSkin',      chance: 10 },
                { type: 'animation',       chance: 10 },
                { type: 'clothesDiscount', chance: 7 },
                { type: 'vehicle',         chance: 5 },
            ],
        },
        {
            color: 'gold',
            chance: 4,
            types: [
                { type: 'money',           chance: 20 },
                { type: 'clothes',         chance: 20 },
                { type: 'coins',           chance: 15 },
                { type: 'armourSkin',      chance: 15 },
                { type: 'weaponSkin',      chance: 10 },
                { type: 'animation',       chance: 10 },
                { type: 'clothesDiscount', chance: 11 },
                { type: 'vehicle',         chance: 7 },
                { type: 'vehicleMod',      chance: 4 },
            ],
        }
    ],

    caseContent: [
        // Скидка на одежду
        { type: 'clothesDiscount', color: 'gray',   chance: 50.00, price: 525,  value: 5  },
        { type: 'clothesDiscount', color: 'blue',   chance: 30.00, price: 875,  value: 10 },
        { type: 'clothesDiscount', color: 'purple', chance: 15.00, price: 2100, value: 15 },
        { type: 'clothesDiscount', color: 'red',    chance: 4.00,  price: 3500, value: 20 },
        { type: 'clothesDiscount', color: 'gold',   chance: 1.00,  price: 8750, value: 25 },

        // Вирты
        { type: 'money',           color: 'gray',   chance: 50.00, price: 525,  value: 15000  },
        { type: 'money',           color: 'blue',   chance: 30.00, price: 875,  value: 25000  },
        { type: 'money',           color: 'purple', chance: 15.00, price: 2100, value: 60000  },
        { type: 'money',           color: 'red',    chance: 4.00,  price: 3500, value: 100000 },
        { type: 'money',           color: 'gold',   chance: 1.00,  price: 8750, value: 250000 },

        // Коины
        { type: 'coins',           color: 'gray',   chance: 50.00, price: 525,  value: 150  },
        { type: 'coins',           color: 'blue',   chance: 30.00, price: 875,  value: 250  },
        { type: 'coins',           color: 'purple', chance: 15.00, price: 2100, value: 600  },
        { type: 'coins',           color: 'red',    chance: 4.00,  price: 3500, value: 1000 },
        { type: 'coins',           color: 'gold',   chance: 1.00,  price: 8750, value: 2500 },

        // armourSkins
        { type: 'armourSkin',      color: 'gray',   chance: 1.00,  price: 525, value: { entityName: 'light', skinId: 58 } },
        { type: 'armourSkin',      color: 'gray',   chance: 1.00,  price: 525, value: { entityName: 'light', skinId: 61 } },
        { type: 'armourSkin',      color: 'gray',   chance: 1.00,  price: 525, value: { entityName: 'light', skinId: 64 } },
        { type: 'armourSkin',      color: 'gray',   chance: 1.00,  price: 525, value: { entityName: 'light', skinId: 65 } },

        { type: 'armourSkin',      color: 'blue',   chance: 1.00,  price: 875, value: { entityName: 'light', skinId: 59 } },
        { type: 'armourSkin',      color: 'blue',   chance: 1.00,  price: 875, value: { entityName: 'light', skinId: 60 } },
        { type: 'armourSkin',      color: 'blue',   chance: 1.00,  price: 875, value: { entityName: 'light', skinId: 63 } },

        { type: 'armourSkin',      color: 'purple', chance: 1.00,  price: 2100, value: { entityName: 'light', skinId: 62 } },

        { type: 'armourSkin',      color: 'red',    chance: 1.00,  price: 3500, value: { entityName: 'light', skinId: 66 } },

        { type: 'armourSkin',      color: 'gold',   chance: 1.00,  price: 8500, value: { entityName: 'light', skinId: 67 } },

        // weaponSkins Pumpkin
        { type: 'weaponSkin',      color: 'blue',   chance: 1.00,  price: 875, value: { entityName: 'weapon_machinepistol', skinId: 5 } },

        { type: 'weaponSkin',      color: 'purple', chance: 0.95,  price: 2100, value: { entityName: 'weapon_carbinerifle', skinId: 10 } },

        { type: 'weaponSkin',      color: 'red',    chance: 0.85,  price: 3500, value: { entityName: 'weapon_heavysniper', skinId: 1 } },
        { type: 'weaponSkin',      color: 'red',    chance: 0.90,  price: 3500, value: { entityName: 'weapon_heavyshotgun', skinId: 10 } },
        { type: 'weaponSkin',      color: 'red',    chance: 0.95,  price: 3500, value: { entityName: 'weapon_revolver', skinId: 10 } },
        { type: 'weaponSkin',      color: 'red',    chance: 0.90,  price: 3500, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 6 } },

        { type: 'weaponSkin',      color: 'gold',   chance: 0.85,  price: 8500, value: { entityName: 'weapon_heavysniper_mk2', skinId: 5 } },
        { type: 'weaponSkin',      color: 'gold',   chance: 0.90,  price: 8500, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 7 } },

        // гташные авто
        { type: 'vehicle',         color: 'purple', chance: 35.00, price: 2100, value: 'lurcher'    },
        { type: 'vehicle',         color: 'purple', chance: 35.00, price: 2100, value: 'dune2'      },
        { type: 'vehicle',         color: 'purple', chance: 35.00, price: 2100, value: 'btype2'     },
        { type: 'vehicle',         color: 'purple', chance: 35.00, price: 2100, value: 'brutus'     },
        { type: 'vehicle',         color: 'purple', chance: 35.00, price: 2100, value: 'issi4'      },
        { type: 'vehicle',         color: 'purple', chance: 35.00, price: 2100, value: 'slamvan4'   },
        { type: 'vehicle',         color: 'purple', chance: 35.00, price: 2100, value: 'impaler2'    },
        { type: 'vehicle',         color: 'purple', chance: 35.00, price: 2100, value: 'imperator'  },
        { type: 'vehicle',         color: 'purple', chance: 35.00, price: 2100, value: 'deathbike'  },

        // гташные авто
        { type: 'vehicle',         color: 'red',    chance: 25.00, price: 3500, value: 'speedo2'    },
        { type: 'vehicle',         color: 'red',    chance: 25.00, price: 3500, value: 'brutus2'    },
        { type: 'vehicle',         color: 'red',    chance: 25.00, price: 3500, value: 'issi5'      },
        { type: 'vehicle',         color: 'red',    chance: 25.00, price: 3500, value: 'slamvan5'   },
        { type: 'vehicle',         color: 'red',    chance: 25.00, price: 3500, value: 'impaler3'   },
        { type: 'vehicle',         color: 'red',    chance: 25.00, price: 3500, value: 'imperator2' },
        { type: 'vehicle',         color: 'red',    chance: 25.00, price: 3500, value: 'brigham'    },
        { type: 'vehicle',         color: 'red',    chance: 25.00, price: 3500, value: 'deathbike2' },
        { type: 'vehicle',         color: 'red',    chance: 25.00, price: 3500, value: 'sanctus'    },

        // реальные авто
        { type: 'vehicle',         color: 'gold',   chance: 20.00, price: 12000, customPrice: 750000,  value: 'modelt'   },
        { type: 'vehicle',         color: 'gold',   chance: 17.00, price: 15000, customPrice: 1000000, value: 'db5'      },
        { type: 'vehicle',         color: 'gold',   chance: 15.00, price: 20000, customPrice: 1500000, value: 'charger3' },

        // реальные авто с обвесами
        { type: 'vehicleMod',      color: 'gold',   chance: 17.0,  price: 12000, customPrice: 750000,  value: { model: 'modelt',    setId: 2  } },
        { type: 'vehicleMod',      color: 'gold',   chance: 15.0,  price: 13000, customPrice: 1000000, value: { model: 'db5',       setId: 2  } },
        //{ type: 'vehicleMod',      color: 'gold',   chance: 13.0,  price: 14000, customPrice: 1000000, value: { model: 'db5',       setId: 3  } },
        { type: 'vehicleMod',      color: 'gold',   chance: 11.0,  price: 15000, customPrice: 1500000, value: { model: 'charger3',  setId: 2  } },
        { type: 'vehicleMod',      color: 'gold',   chance: 11.0,  price: 15000, customPrice: 1000000, value: { model: 'db5',       setId: 4  } },
        //{ type: 'vehicleMod',      color: 'gold',   chance: 9.0,   price: 19000, customPrice: 1500000, value: { model: 'charger3',  setId: 3  } },
        //{ type: 'vehicleMod',      color: 'gold',   chance: 9.0,   price: 21000, customPrice: 1000000, value: { model: 'db5',       setId: 5  } },

        // анимации
        { type: 'animation',       color: 'blue',   chance: 55,    price: 875,     value: 254 }, // pump it up
        { type: 'animation',       color: 'blue',   chance: 55,    price: 875,     value: 40 }, // i aint afraid

        { type: 'animation',       color: 'purple', chance: 55,    price: 2100,     value: 190 }, // ska ska terrestrial

        { type: 'animation',       color: 'red',    chance: 55,    price: 3500,     value: 134 }, // boneless
        { type: 'animation',       color: 'red',    chance: 55,    price: 3500,     value: 76 }, // extraterrestrial

        { type: 'animation',       color: 'gold',   chance: 55,    price: 8500,     value: 150 }, // monster mash
        { type: 'animation',       color: 'gold',   chance: 55,    price: 8500,     value: 79 }, // mime time

        //одежда
        { type: 'clothes',         color: 'gray',   chance: 40,    price: 525,    value: [{ gender: 0, component: 1, drawable: 2137, texture: 0, isProp: 0 }, { gender: 1, component: 1,  drawable: 2126, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'gray',   chance: 40,    price: 525,    value: [{ gender: 0, component: 1, drawable: 2139, textures: 2, isProp: 0 }, { gender: 1, component: 1,  drawable: 2122, textures: 2, isProp: 0 }]},
        { type: 'clothes',         color: 'gray',   chance: 40,    price: 525,    value: [{ gender: 0, component: 4, drawable: 2197, texture: 0, isProp: 0 }, { gender: 1, component: 4,  drawable: 2204, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'gray',   chance: 40,    price: 525,    value: [{ gender: 0, component: 6, drawable: 2080, texture: 0, isProp: 0 }, { gender: 1, component: 6,  drawable: 2093, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'gray',   chance: 40,    price: 525,    value: [{ gender: 0, component: 8, drawable: 2130, texture: 0, isProp: 0 }, { gender: 1, component: 8,  drawable: 2109, texture: 0, isProp: 0 }]},

        { type: 'clothes',         color: 'blue',   chance: 40,    price: 875,    value: [{ gender: 0, component: 11, drawable: 2388, textures: 1, isProp: 0 }, { gender: 1, component: 11,  drawable: 2375, textures: 9, isProp: 0 }]},
        { type: 'clothes',         color: 'blue',   chance: 40,    price: 875,    value: [{ gender: 0, component: 1, drawable: 2138, textures: 1, isProp: 0 }, { gender: 1, component: 1,  drawable: 2124, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'blue',   chance: 40,    price: 875,    value: [{ gender: 0, component: 1, drawable: 2135, texture: 0, isProp: 0 }, { gender: 1, component: 10,  drawable: 2092, textures: 9, isProp: 0 }]},
        { type: 'clothes',         color: 'blue',   chance: 40,    price: 875,    value: [{ gender: 0, component: 4, drawable: 2198, textures: 1, isProp: 0 }, { gender: 1, component: 4,  drawable: 2205, textures: 1, isProp: 0 }]},
        { type: 'clothes',         color: 'blue',   chance: 40,    price: 875,    value: [{ gender: 0, component: 6, drawable: 2082, textures: 1, isProp: 0 }, { gender: 1, component: 11,  drawable: 2382, textures: 1, isProp: 0 }]},
        { type: 'clothes',         color: 'blue',   chance: 40,    price: 875,    value: [{ gender: 0, component: 4, drawable: 2193, texture: 0, isProp: 0 }, { gender: 1, component: 4,  drawable: 2202, textures: 9, isProp: 0 }]},
        { type: 'clothes',         color: 'blue',   chance: 40,    price: 875,    value: [{ gender: 0, component: 7, drawable: 2123, texture: 0, isProp: 0 }, { gender: 1, component: 7,  drawable: 2102, texture: 0, isProp: 0 }]},

        { type: 'clothes',         color: 'purple', chance: 40,    price: 2100,   value: [{ gender: 0, component: 6, drawable: 2081, texture: 0, isProp: 0 }, { gender: 1, component: 6,  drawable: 2095, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'purple', chance: 40,    price: 2100,   value: [{ gender: 0, component: 4, drawable: 2195, texture: 0, isProp: 0 }, { gender: 1, component: 4,  drawable: 2203, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'purple', chance: 40,    price: 2100,   value: [{ gender: 0, component: 1, drawable: 2134, texture: 0, isProp: 0 }, { gender: 1, component: 1,  drawable: 2120, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'purple', chance: 40,    price: 2100,   value: [{ gender: 0, component: 11, drawable: 2383, texture: 0, isProp: 0 }, { gender: 1, component: 11,  drawable: 2380, textures: 1, isProp: 0 }]},
        { type: 'clothes',         color: 'purple', chance: 40,    price: 2100,   value: [{ gender: 0, component: 1, drawable: 2133, texture: 0, isProp: 0 }, { gender: 1, component: 1,  drawable: 2125, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'purple', chance: 40,    price: 2100,   value: [{ gender: 0, component: 7, drawable: 2124, textures: 3, isProp: 0 }, { gender: 1, component: 7,  drawable: 2101, textures: 3, isProp: 0 }]},
        { type: 'clothes',         color: 'purple', chance: 40,    price: 2100,   value: [{ gender: 0, component: 1,  drawable: 2132, texture: 0, isProp: 0 }, { gender: 1, component: 1,  drawable: 2119, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'purple', chance: 40,    price: 2100,   value: [{ gender: 0, component: 0,  drawable: 2080, texture: 0, isProp: 1 }, { gender: 1, component: 0,  drawable: 2083, texture: 0, isProp: 1 }]},
        { type: 'clothes',         color: 'purple', chance: 40,    price: 2100,   value: [{ gender: 0, component: 4,  drawable: 2194, textures: 9, isProp: 0 }, { gender: 1, component: 11,  drawable: 2379, textures: 8, isProp: 0 }]},
        { type: 'clothes',         color: 'purple', chance: 40,    price: 2100,   value: [{ gender: 0, component: 5,  drawable: 2055, textures: 4, isProp: 0 }, { gender: 1, component: 5,  drawable: 2050, textures: 4, isProp: 0 }]},
        { type: 'clothes',         color: 'purple', chance: 40,    price: 2100,   value: [{ gender: 0, component: 4,  drawable: 2191, texture: 0, isProp: 0 }, { gender: 1, component: 0,  drawable: 2084, texture: 0, isProp: 0 }]},

        { type: 'clothes',         color: 'red',    chance: 40,    price: 3500,   value: [{ gender: 0, component: 4,  drawable: 2189, textures: 9, isProp: 0 }, { gender: 1, component: 11,  drawable: 2373, textures: 9, isProp: 0 }]},
        { type: 'clothes',         color: 'red',    chance: 40,    price: 3500,   value: [{ gender: 0, component: 11,  drawable: 2380, texture: 0, isProp: 0 }, { gender: 1, component: 11,  drawable: 2376, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'red',    chance: 40,    price: 3500,   value: [{ gender: 0, component: 11,  drawable: 2390, texture: 0, isProp: 0 }, { gender: 1, component: 11,  drawable: 2387, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'red',    chance: 40,    price: 3500,   value: [{ gender: 0, component: 11,  drawable: 2389, textures: 9, isProp: 0 }, { gender: 1, component: 11,  drawable: 2385, textures: 9, isProp: 0 }]},
        { type: 'clothes',         color: 'red',    chance: 40,    price: 3500,   value: [{ gender: 0, component: 5,  drawable: 2056, textures: 11, isProp: 0 }, { gender: 1, component: 5,  drawable: 2052, textures: 11, isProp: 0 }]},
        { type: 'clothes',         color: 'red',    chance: 40,    price: 3500,   value: [{ gender: 0, component: 0,  drawable: 2028, texture: 0, isProp: 1 }, { gender: 1, component: 0,  drawable: 2034, texture: 0, isProp: 1 }]},
        { type: 'clothes',         color: 'red',    chance: 40,    price: 3500,   value: [{ gender: 0, component: 0,  drawable: 2083, textures: 7, isProp: 1 }, { gender: 1, component: 0,  drawable: 2086, textures: 7, isProp: 1 }]},
        { type: 'clothes',         color: 'red',    chance: 40,    price: 3500,   value: [{ gender: 0, component: 11,  drawable: 2171, texture: 0, isProp: 0 }, { gender: 1, component: 0,  drawable: 2081, texture: 0, isProp: 1 }]},

        { type: 'clothes',         color: 'gold',   chance: 40,    price: 8750,   value: [{ gender: 0, component: 1,  drawable: 2136, texture: 0, isProp: 0 }, { gender: 1, component: 1,  drawable: 2121, texture: 0, isProp: 0 }]},
        { type: 'clothes',         color: 'gold',   chance: 40,    price: 8750,   value: [{ gender: 0, component: 1,  drawable: 2140, textures: 7, isProp: 0 }, { gender: 1, component: 1,  drawable: 2123, textures: 7, isProp: 0 }]},
        { type: 'clothes',         color: 'gold',   chance: 40,    price: 8750,   value: [{ gender: 0, component: 5,  drawable: 2057, textures: 3, isProp: 0 }, { gender: 1, component: 5,  drawable: 2051, textures: 3, isProp: 0 }]},
        { type: 'clothes',         color: 'gold',   chance: 40,    price: 8750,   value: [{ gender: 0, component: 11,  drawable: 2387, textures: 9, isProp: 0 }, { gender: 1, component: 11,  drawable: 2377, textures: 9, isProp: 0 }]},
        { type: 'clothes',         color: 'gold',   chance: 40,    price: 8750,   value: [{ gender: 0, component: 11,  drawable: 2381, textures: 1, isProp: 0 }, { gender: 1, component: 11,  drawable: 2384, textures: 9, isProp: 0 }]},
        { type: 'clothes',         color: 'gold',   chance: 40,    price: 8750,   value: [{ gender: 0, component: 6,  drawable: 2083, texture: 0, isProp: 0 }, { gender: 1, component: 11,  drawable: 2386, textures: 9, isProp: 0 }]},
        { type: 'clothes',         color: 'gold',   chance: 40,    price: 8750,   value: [{ gender: 0, component: 11,  drawable: 2384, texture: 0, isProp: 0 }, { gender: 1, component: 0,  drawable: 2080, textures: 9, isProp: 1 }]},
        { type: 'clothes',         color: 'gold',   chance: 40,    price: 8750,   value: [{ gender: 0, component: 1,  drawable: 2141, texture: 0, isProp: 0 }, { gender: 1, component: 11,  drawable: 2381, textures: 9, isProp: 0 }]},
        { type: 'clothes',         color: 'gold',   chance: 40,    price: 8750,   value: [{ gender: 0, component: 0,  drawable: 2082, textures: 10, isProp: 1 }, { gender: 1, component: 0,  drawable: 2085, textures: 10, isProp: 1 }]},
        { type: 'clothes',         color: 'gold',   chance: 40,    price: 8750,   value: [{ gender: 0, component: 0,  drawable: 2011, textures: 7, isProp: 1 },{ gender: 1, component: 0,  drawable: 2082, textures: 9, isProp: 1 }]},
    ]
}
