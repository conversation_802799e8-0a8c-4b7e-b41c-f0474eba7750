module.exports = {
	id: 21,
	title: 'donate.config.cases.winterExtra2025.title',
	// discount: 5,

	start: ['2025-01-28 06:00:00', 'YYYY-MM-DD HH:mm:ss'],
	end: ['2025-03-01 00:00:00', 'YYYY-MM-DD HH:mm:ss'],

	price: 500, // Цена кейса в коинах

	// servers: ['TEST0'],

	transferDays: 30,

	disabledBuy: false,

	readOnly: false,

	colors: [
		{
			color: 'gray',
			chance: 55,
			types: [
				{ type: 'money', chance: 30 },
				{ type: 'clothesDiscount', chance: 25 },
				{ type: 'vehicleDiscount', chance: 25 },
				{ type: 'tattoo', chance: 20 },
				{ type: 'animation', chance: 15 },
				{ type: 'clothes', chance: 15 },
			],
		},
		{
			color: 'blue',
			chance: 23,
			types: [
				{ type: 'money', chance: 30 },
				{ type: 'clothesDiscount', chance: 25 },
				{ type: 'vehicleDiscount', chance: 25 },
				{ type: 'tattoo', chance: 30 },
				{ type: 'subscription', chance: 20 },
				{ type: 'animation', chance: 15 },
				{ type: 'clothes', chance: 15 },
			],
		},
		{
			color: 'purple',
			chance: 11,
			types: [
				{ type: 'tattoo', chance: 40 },
				{ type: 'money', chance: 30 },
				{ type: 'subscription', chance: 15 },
				{ type: 'clothesDiscount', chance: 25 },
				{ type: 'vehicleDiscount', chance: 25 },
				{ type: 'animation', chance: 20 },
				{ type: 'armourSkin', chance: 15 },
				{ type: 'weaponSkin', chance: 15 },
				{ type: 'wheels', chance: 10 },
				{ type: 'clothesPack', chance: 5 },
				{ type: 'clothes', chance: 20 },
			],
		},
		{
			color: 'red',
			chance: 6,
			types: [
				{ type: 'tattoo', chance: 40 },
				{ type: 'money', chance: 30 },
				{ type: 'subscription', chance: 15 },
				{ type: 'clothesDiscount', chance: 25 },
				{ type: 'vehicleDiscount', chance: 25 },
				{ type: 'animation', chance: 20 },
				{ type: 'armourSkin', chance: 15 },
				{ type: 'weaponSkin', chance: 15 },
				{ type: 'wheels', chance: 10 },
				{ type: 'clothesPack', chance: 5 },
				{ type: 'clothes', chance: 20 },
				{ type: 'vehicleDetail', chance: 15 },
			],
		},
		{
			color: 'gold',
			chance: 3.5,
			types: [
				{ type: 'tattoo', chance: 40 },
				{ type: 'money', chance: 30 },
				{ type: 'subscription', chance: 15 },
				{ type: 'clothesDiscount', chance: 25 },
				{ type: 'vehicleDiscount', chance: 25 },
				{ type: 'animation', chance: 20 },
				{ type: 'armourSkin', chance: 15 },
				{ type: 'weaponSkin', chance: 15 },
				{ type: 'wheels', chance: 10 },
				{ type: 'clothesPack', chance: 5 },
				{ type: 'clothes', chance: 20 },
				{ type: 'vehicleDetail', chance: 15 },
			],
		},
	],

	caseContent: [
		{ type: 'wheels', price: 400, color: 'purple', chance: 20, value: 14001 }, // Хромовое колесо
		{ type: 'wheels', price: 1000, color: 'red', chance: 15, value: 14002 }, // Покрасочное колесо
		{ type: 'wheels', price: 2500, color: 'gold', chance: 10, value: 14003 }, // Неоновое колесо

		{ type: 'vehicleDetail', price: 1000, color: 'red', chance: 20, value: 1020 }, // Винил
		{ type: 'vehicleDetail', price: 1000, color: 'red', chance: 15, value: 1021 }, // Винил
		{ type: 'vehicleDetail', price: 2500, color: 'gold', chance: 20, value: 1019 }, // Винил
		{ type: 'vehicleDetail', price: 2500, color: 'gold', chance: 15, value: 1022 }, // Винил
		{ type: 'vehicleDetail', price: 2500, color: 'gold', chance: 5.0, value: 5005 }, // Номерной знак

		// Анимации зимний-ивент кейс 2025
		{ type: 'animation', color: 'gray', chance: 17, price: 100, value: 390 }, // Waddle Away
		{ type: 'animation', color: 'gray', chance: 17, price: 100, value: 277 }, // Get Loose
		{ type: 'animation', color: 'blue', chance: 16, price: 250, value: 111 }, // Scenario
		{ type: 'animation', color: 'blue', chance: 16, price: 250, value: 388 }, // LOFI Headbang
		{ type: 'animation', color: 'blue', chance: 16, price: 250, value: 389 }, // Brite Moves
		{ type: 'animation', color: 'blue', chance: 16, price: 250, value: 92 }, // Phone It In
		{ type: 'animation', color: 'purple', chance: 13, price: 400, value: 340 }, // Social Climber
		{ type: 'animation', color: 'purple', chance: 13, price: 400, value: 318 }, // Click Click Flash
		{ type: 'animation', color: 'red', chance: 12, price: 1000, value: 252 }, // Rocket Rodeo
		{ type: 'animation', color: 'gold', chance: 12, price: 2500, value: 395 }, // Alaska Puffer

		// Clothes Packs
		{
			type: 'clothesPack',
			color: 'purple',
			chance: 1.0,
			price: 750,
			value: {
				preview: 'ElfM_preview',
				name: 'elfm_pack',
				textures: 3,
				items: [
					{ type: 'animation', value: 391 },
					{ type: 'clothes', value: { gender: 0, component: 0, drawable: 2104, isProp: 1 } },
					{ type: 'clothes', value: { gender: 0, component: 11, drawable: 2504, isProp: 0 } },
					{ type: 'clothes', value: { gender: 0, component: 6, drawable: 2137, isProp: 0 } },
					{ type: 'clothes', value: { gender: 0, component: 4, drawable: 2272, isProp: 0 } },
				],
			},
		},
		{
			type: 'clothesPack',
			color: 'purple',
			chance: 1.0,
			price: 750,
			value: {
				preview: 'ElfF_preview',
				name: 'elff_pack',
				textures: 3,
				items: [
					{ type: 'animation', value: 391 },
					{ type: 'clothes', value: { gender: 1, component: 0, drawable: 2108, isProp: 1 } },
					{ type: 'clothes', value: { gender: 1, component: 11, drawable: 2503, isProp: 0 } },
					{ type: 'clothes', value: { gender: 1, component: 6, drawable: 2166, isProp: 0 } },
					{ type: 'clothes', value: { gender: 1, component: 4, drawable: 2283, isProp: 0 } },
				],
			},
		},
		{
			type: 'clothesPack',
			color: 'red',
			chance: 1.0,
			price: 2000,
			value: {
				preview: 'GingerM_preview',
				name: 'gingerm_pack',
				textures: 2,
				items: [
					{ type: 'animation', value: 394 },
					{ type: 'clothes', value: { gender: 0, component: 11, drawable: 2494, isProp: 0 } },
					{ type: 'clothes', value: { gender: 0, component: 4, drawable: 2264, isProp: 0 } },
					{ type: 'clothes', value: { gender: 0, component: 1, drawable: 2165, isProp: 0 } },
				],
			},
		},
		{
			type: 'clothesPack',
			color: 'red',
			chance: 1.0,
			price: 2000,
			value: {
				preview: 'GingerF_preview',
				name: 'gingerf_pack',
				textures: 2,
				items: [
					{ type: 'animation', value: 394 },
					{ type: 'clothes', value: { gender: 1, component: 11, drawable: 2483, isProp: 0 } },
					{ type: 'clothes', value: { gender: 1, component: 4, drawable: 2268, isProp: 0 } },
					{ type: 'clothes', value: { gender: 1, component: 1, drawable: 2152, isProp: 0 } },
				],
			},
		},

		{
			type: 'clothesPack',
			color: 'gold',
			chance: 1.0,
			price: 5000,
			value: {
				preview: 'WinterGirl_preview',
				name: 'wintergirl_pack',
				textures: 3,
				items: [
					{ type: 'animation', value: 393 },
					{ type: 'clothes', value: { gender: 1, component: 11, drawable: 2498, isProp: 0 } },
					{ type: 'clothes', value: { gender: 1, component: 4, drawable: 2282, isProp: 0 } },
				],
			},
		},

		{
			type: 'clothesPack',
			color: 'gold',
			chance: 1.0,
			price: 5000,
			value: {
				preview: 'Santa_preview',
				name: 'santa_pack',
				textures: 3,
				items: [
					{ type: 'animation', value: 392 },
					{ type: 'clothes', value: { gender: 0, component: 11, drawable: 2503, isProp: 0 } },
					{ type: 'clothes', value: { gender: 0, component: 4, drawable: 2271, isProp: 0 } },
					{ type: 'clothes', value: { gender: 0, component: 1, drawable: 2166, isProp: 0 } },
				],
			},
		},

		// Clothes
		{ type: 'clothes', color: 'gray', chance: 1.0, price: 100, value: { gender: 1, component: 11, drawable: 2493, texture: 0, isProp: 0 } },
		{ type: 'clothes', color: 'gray', chance: 1.0, price: 100, value: { gender: 0, component: 11, drawable: 2496, textures: 4, isProp: 0 } },

		{ type: 'clothes', color: 'blue', chance: 1.0, price: 250, value: { gender: 0, component: 4, drawable: 2268, textures: 4, isProp: 0 } },
		{ type: 'clothes', color: 'blue', chance: 1.0, price: 250, value: { gender: 1, component: 4, drawable: 2280, textures: 5, isProp: 0 } },
		{ type: 'clothes', color: 'blue', chance: 1.0, price: 250, value: { gender: 0, component: 6, drawable: 2134, textures: 4, isProp: 0 } },
		{ type: 'clothes', color: 'blue', chance: 1.0, price: 250, value: { gender: 1, component: 6, drawable: 2159, textures: 8, isProp: 0 } },

		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 1, component: 5, drawable: 2117, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 0, component: 5, drawable: 2109, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 0, component: 6, drawable: 2133, textures: 6, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 1, component: 6, drawable: 2161, textures: 6, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 1, component: 4, drawable: 2279, textures: 8, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 0, component: 11, drawable: 2497, textures: 9, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 0, component: 4, drawable: 2269, textures: 9, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 1, component: 11, drawable: 2497, textures: 5, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 1, component: 4, drawable: 2275, textures: 4, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 1, component: 6, drawable: 2160, textures: 5, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 1, component: 4, drawable: 2276, textures: 4, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 1, component: 11, drawable: 2494, textures: 7, isProp: 0 } },
		{ type: 'clothes', color: 'purple', chance: 1.0, price: 400, value: { gender: 1, component: 5, drawable: 2113, textures: 6, isProp: 0 } },

		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 11, drawable: 2491, textures: 9, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 1, drawable: 2154, textures: 9, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 4, drawable: 2270, textures: 3, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 5, drawable: 2106, textures: 8, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 5, drawable: 2112, textures: 8, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 4, drawable: 2277, textures: 10, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 11, drawable: 2498, textures: 9, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 6, drawable: 2165, texture: 0, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 11, drawable: 2501, textures: 9, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 6, drawable: 2163, textures: 5, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 0, component: 6, drawable: 2135, textures: 11, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 6, drawable: 2162, textures: 11, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 4, drawable: 2278, textures: 5, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 11, drawable: 2495, textures: 11, isProp: 0 } },
		{ type: 'clothes', color: 'red', chance: 1.0, price: 1000, value: { gender: 1, component: 5, drawable: 2115, textures: 11, isProp: 0 } },

		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 5, drawable: 2108, textures: 7, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 5, drawable: 2116, textures: 7, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 5, drawable: 2107, textures: 7, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 5, drawable: 2114, textures: 7, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 6, drawable: 2132, textures: 8, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 6, drawable: 2136, textures: 12, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 11, drawable: 2500, textures: 9, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 0, component: 11, drawable: 2502, textures: 5, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 11, drawable: 2496, textures: 9, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 6, drawable: 2164, textures: 12, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 6, drawable: 2158, textures: 8, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 1, drawable: 2153, textures: 6, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 4, drawable: 2281, texture: 0, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 11, drawable: 2499, texture: 0, isProp: 0 } },
		{ type: 'clothes', color: 'gold', chance: 1.0, price: 2500, value: { gender: 1, component: 11, drawable: 2501, textures: 3, isProp: 0 } },

		// weaponSkins Snake
		{ type: 'weaponSkin', color: 'purple', chance: 0.9, price: 400, value: { entityName: 'weapon_gusenberg', skinId: 17 } },
		{ type: 'weaponSkin', color: 'purple', chance: 0.9, price: 400, value: { entityName: 'weapon_tecpistol', skinId: 12 } },
		{ type: 'weaponSkin', color: 'red', chance: 0.9, price: 1000, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 24 } },
		{ type: 'weaponSkin', color: 'red', chance: 0.9, price: 1000, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 25 } },
		{ type: 'weaponSkin', color: 'red', chance: 0.9, price: 1000, value: { entityName: 'weapon_heavyshotgun', skinId: 28 } },
		{ type: 'weaponSkin', color: 'red', chance: 0.9, price: 1000, value: { entityName: 'weapon_tacticalrifle', skinId: 12 } },
		{ type: 'weaponSkin', color: 'gold', chance: 0.85, price: 2500, value: { entityName: 'weapon_heavysniper_mk2', skinId: 23 } },
		{ type: 'weaponSkin', color: 'gold', chance: 0.9, price: 2500, value: { entityName: 'weapon_heavyrifle', skinId: 12 } },
		{ type: 'weaponSkin', color: 'gold', chance: 0.9, price: 2500, value: { entityName: 'weapon_combatmg_mk2', skinId: 12 } },

		// armourSkins
		{ type: 'armourSkin', color: 'purple', chance: 0.75, price: 400, value: { entityName: 'light', skinId: 143 } },
		{ type: 'armourSkin', color: 'red', chance: 0.95, price: 1000, value: { entityName: 'light', skinId: 142 } },
		{ type: 'armourSkin', color: 'gold', chance: 0.65, price: 2500, value: { entityName: 'light', skinId: 144 } },

		// Tattoo
		{ type: 'tattoo', color: 'gray', chance: 1.0, price: 100, value: [3419, 6486] },
		{ type: 'tattoo', color: 'gray', chance: 1.0, price: 100, value: [3434, 6501] },
		{ type: 'tattoo', color: 'gray', chance: 1.0, price: 100, value: [3435, 6502] },

		{ type: 'tattoo', color: 'blue', chance: 1.0, price: 250, value: [3428, 6495] },
		{ type: 'tattoo', color: 'blue', chance: 1.0, price: 250, value: [3424, 6491] },
		{ type: 'tattoo', color: 'blue', chance: 1.0, price: 250, value: [3437, 6504] },
		{ type: 'tattoo', color: 'blue', chance: 1.0, price: 250, value: [3438, 6505] },
		{ type: 'tattoo', color: 'blue', chance: 1.0, price: 250, value: [3433, 6500] },
		{ type: 'tattoo', color: 'blue', chance: 1.0, price: 250, value: [3440, 6507] },
		{ type: 'tattoo', color: 'blue', chance: 1.0, price: 250, value: [3442, 6509] },
		{ type: 'tattoo', color: 'blue', chance: 1.0, price: 250, value: [3447, 6514] },
		{ type: 'tattoo', color: 'blue', chance: 1.0, price: 250, value: [3448, 6515] },
		{ type: 'tattoo', color: 'blue', chance: 1.0, price: 250, value: [3444, 6511] },
		{ type: 'tattoo', color: 'blue', chance: 1.0, price: 250, value: [3445, 6512] },

		{ type: 'tattoo', color: 'purple', chance: 1.0, price: 400, value: [3439, 6506] },
		{ type: 'tattoo', color: 'purple', chance: 1.0, price: 400, value: [3441, 6508] },
		{ type: 'tattoo', color: 'purple', chance: 1.0, price: 400, value: [3443, 6510] },
		{ type: 'tattoo', color: 'purple', chance: 1.0, price: 400, value: [3429, 6496] },
		{ type: 'tattoo', color: 'purple', chance: 1.0, price: 400, value: [3430, 6497] },
		{ type: 'tattoo', color: 'purple', chance: 1.0, price: 400, value: [3431, 6498] },
		{ type: 'tattoo', color: 'purple', chance: 1.0, price: 400, value: [3432, 6499] },

		{ type: 'tattoo', color: 'red', chance: 1.0, price: 1000, value: [3446, 6513] },
		{ type: 'tattoo', color: 'red', chance: 1.0, price: 1000, value: [3422, 6489] },
		{ type: 'tattoo', color: 'red', chance: 1.0, price: 1000, value: [3426, 6493] },
		{ type: 'tattoo', color: 'red', chance: 1.0, price: 1000, value: [3427, 6494] },
		{ type: 'tattoo', color: 'red', chance: 1.0, price: 1000, value: [3436, 6503] },

		{ type: 'tattoo', color: 'gold', chance: 1.0, price: 2500, value: [3420, 6487] },
		{ type: 'tattoo', color: 'gold', chance: 1.0, price: 2500, value: [3421, 6488] },
		{ type: 'tattoo', color: 'gold', chance: 1.0, price: 2500, value: [3423, 6490] },
		{ type: 'tattoo', color: 'gold', chance: 1.0, price: 2500, value: [3425, 6492] },

		// Скидка на транспорт
		{ type: 'vehicleDiscount', color: 'gray', chance: 50.0, price: 100, value: 5 },
		{ type: 'vehicleDiscount', color: 'blue', chance: 30.0, price: 250, value: 10 },
		{ type: 'vehicleDiscount', color: 'purple', chance: 15.0, price: 400, value: 15 },
		{ type: 'vehicleDiscount', color: 'red', chance: 4.0, price: 1000, value: 20 },
		{ type: 'vehicleDiscount', color: 'gold', chance: 1.0, price: 2500, value: 25 },

		// Скидка на одежду
		{ type: 'clothesDiscount', color: 'gray', chance: 50.0, price: 100, value: 5 },
		{ type: 'clothesDiscount', color: 'blue', chance: 30.0, price: 250, value: 10 },
		{ type: 'clothesDiscount', color: 'purple', chance: 15.0, price: 400, value: 15 },
		{ type: 'clothesDiscount', color: 'red', chance: 4.0, price: 1000, value: 20 },
		{ type: 'clothesDiscount', color: 'gold', chance: 1.0, price: 2500, value: 25 },

		// Вирты
		{ type: 'money', color: 'gray', chance: 50.0, price: 100, value: 10000 },
		{ type: 'money', color: 'blue', chance: 30.0, price: 250, value: 25000 },
		{ type: 'money', color: 'purple', chance: 15.0, price: 400, value: 40000 },
		{ type: 'money', color: 'red', chance: 4.0, price: 1000, value: 100000 },
		{ type: 'money', color: 'gold', chance: 1.0, price: 2500, value: 250000 },

		// Premium
		{ type: 'subscription', color: 'blue', chance: 2, price: 250, value: 7 },
		{ type: 'subscription', color: 'purple', chance: 2, price: 400, value: 30 },
		{ type: 'subscription', color: 'red', chance: 2, price: 1000, value: 90 },
		{ type: 'subscription', color: 'gold', chance: 2, price: 2500, value: 180 },
	],
};
