module.exports = {
    id: 1,
    main: true,

    title: 'donate.config.cases.default.title',

    price: 200,
    // discount: 10,

    start: ['2020-05-01', 'YYYY-MM-DD'],

    colors: [
        { color: 'gray',   chance: 83   },
        { color: 'blue',   chance: 12   },
        { color: 'purple', chance: 4.5  },
        { color: 'red',    chance: 1.7  },
        { color: 'gold',   chance: 0.3  }
    ],

    caseContent: [
        // 120
        { type: 'money',   color: 'gray',  chance: 40,  price: 75,  value: 7500  },
        { type: 'money',   color: 'gray',  chance: 35,  price: 100, value: 10000 },
        { type: 'money',   color: 'gray',  chance: 25,  price: 100, value: 15000 },
        { type: 'money',   color: 'gray',  chance: 20,  price: 200, value: 20000 },

        // 80
        { type: 'coins',   color: 'gray',  chance: 45,  price: 50,  value: 50 },
        { type: 'coins',   color: 'gray',  chance: 35,  price: 100,  value: 100 },

        // 40
        { type: 'vehicle', color: 'gray',  chance: 4,  price: 150, value: 'asbo' },
        { type: 'vehicle', color: 'gray',  chance: 4,  price: 150, value: 'veto' },
        { type: 'vehicle', color: 'gray',  chance: 3.5,  price: 150, value: 'dilettante' },
        { type: 'vehicle', color: 'gray',  chance: 3.5,  price: 150, value: 'blista' },
        { type: 'vehicle', color: 'gray',  chance: 3.5,  price: 150, value: 'panto' },
        { type: 'vehicle', color: 'gray',  chance: 3,  price: 150, value: 'akuma' },
        { type: 'vehicle', color: 'gray',  chance: 3,  price: 150, value: 'bati' },
        { type: 'vehicle', color: 'gray',  chance: 3,  price: 150, value: 'double' },
        { type: 'vehicle', color: 'gray',  chance: 2.5,  price: 150, value: 'hakuchou' },
        { type: 'vehicle', color: 'gray',  chance: 2.5,  price: 150, value: 'bfinjection' },
        { type: 'vehicle', color: 'gray',  chance: 2.5,  price: 150, value: 'asea' },
        { type: 'vehicle', color: 'gray',  chance: 2.5,  price: 150, value: 'emperor' },
        { type: 'vehicle', color: 'gray',  chance: 2.5,  price: 150, value: 'ingot' },

        // blue
        { type: 'money',   color: 'blue',  chance: 120,  price: 300, value: 30000 },

        { type: 'coins',   color: 'blue',  chance: 80,  price: 150, value: 150 },

        { type: 'subscription',  color: 'blue',  chance: 20,  price: 300, value: 3 },

        { type: 'vehicle', color: 'blue',  chance: 1.5,  price: 300, value: 'glendale2' },
        { type: 'vehicle', color: 'blue',  chance: 1.5,  price: 300, value: 'primo2' },
        { type: 'vehicle', color: 'blue',  chance: 1.5,  price: 300, value: 'regina' },
        { type: 'vehicle', color: 'blue',  chance: 1.5,  price: 300, value: 'stanier' },
        { type: 'vehicle', color: 'blue',  chance: 1.5,  price: 300, value: 'surge' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'washington' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'asterope' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'futo' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'manana2' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'manana' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'michelli' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'nebula' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'peyote' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'pigalle' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'retinue' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'zion3' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'seraph3' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'issi2' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'prairie' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'rhapsody' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'weevil' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'schafter2' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'tailgater' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'warrener2' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'buffalo' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'fusilade' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'sentinel3' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'tampa2' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'dynasty' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'rapidgt3' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'clubgtr' },
        { type: 'vehicle', color: 'blue',  chance: 1.25,  price: 300, value: 'schwartzerc' },

        // purple
        { type: 'money',   color: 'purple',  chance: 120,  price: 500, value: 50000 },

        { type: 'coins',   color: 'purple',  chance: 80,  price: 300, value: 300 },

        { type: 'subscription',  color: 'purple',  chance: 15,  price: 500, value: 7 },

        { type: 'vehicle', color: 'purple',  chance: 1.5,  price: 650, value: 'cogcabrio' },
        { type: 'vehicle', color: 'purple',  chance: 1.5,  price: 650, value: 'exemplar' },
        { type: 'vehicle', color: 'purple',  chance: 1.5,  price: 650, value: 'f620' },
        { type: 'vehicle', color: 'purple',  chance: 1.5,  price: 650, value: 'oracle2' },
        { type: 'vehicle', color: 'purple',  chance: 1.5,  price: 650, value: 'windsor' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'deviant' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'dominator3' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'gauntlet4' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'ruiner' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'baller3' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'contender' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'dubsta2' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'gresley' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'rebla' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'toros' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'cognoscenti' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'stafford' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'tailgater2' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'alpha' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'banshee' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'bestiagts' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'carbonizzare' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'comet5' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'coquette' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'elegy2' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'furoregt' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'italigto' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'jugular' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'jester' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'lynx' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'schlagen' },
        { type: 'vehicle', color: 'purple',  chance: 1.25, price: 650, value: 'growler' },

        // red
        { type: 'money',   color: 'red',  chance: 125,  price: 2000, value: 200000 },

        { type: 'coins',   color: 'red',  chance: 90,  price: 1000, value: 1000 },

        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'adder' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'autarch' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 2000, value: 'banshee2' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'bullet' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'cheetah' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'cyclone' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'emerus' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'furia' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'nero' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'osiris' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'pfister811' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'reaper' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'tempesta' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'thrax' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'turismor' },
        { type: 'vehicle', color: 'red',  chance: 3,  price: 1800, value: 'tyrant' },
        { type: 'vehicle', color: 'red',  chance: 2.5,  price: 1800, value: 'tyrus' },
        { type: 'vehicle', color: 'red',  chance: 2.5,  price: 1800, value: 'visione' },
        { type: 'vehicle', color: 'red',  chance: 2.5,  price: 1800, value: 'xa21' },
        { type: 'vehicle', color: 'red',  chance: 2.5,  price: 1800, value: 'zorrusso' },
        { type: 'vehicle', color: 'red',  chance: 2,  price: 1800, value: 'tigon' },
        { type: 'vehicle', color: 'red',  chance: 2,  price: 1800, value: 'italirsx' },

        // Машинки за вирты дешевые
        { type: 'vehicle', color: 'red',  chance: 0.5,  price: 3000, value: 'ae86' },
        { type: 'vehicle', color: 'red',  chance: 0.5,  price: 3000, value: 'rio' },

        // Донатные машинки дешевые
        { type: 'vehicle', color: 'red',  chance: 0.25,  price: 3000, value: 'matiz' },
        { type: 'vehicle', color: 'red',  chance: 0.25,  price: 3000, value: 'priora' },
        { type: 'vehicle', color: 'red',  chance: 0.25,  price: 3000, value: 'urban' },
        { type: 'vehicle', color: 'red',  chance: 0.25,  price: 3000, value: 'vesta' },
        { type: 'vehicle', color: 'red',  chance: 0.25,  price: 3000, value: 'samara' },

        // gold
        { type: 'money',   color: 'gold',  chance: 108,  price: 5000,  value: 500000 },
        { type: 'money',   color: 'gold',  chance: 2,    price: 10000, value: 1000000 },

        { type: 'coins',   color: 'gold',  chance: 35,  price: 4000,  value: 4000 },
        { type: 'coins',   color: 'gold',  chance: 23,  price: 5000,  value: 5000 },
        { type: 'coins',   color: 'gold',  chance: 2,  price: 10000, value: 10000 },

        { type: 'vehicle', color: 'gold',  chance: 2.14,  price: 3750, value: 'mjc' }, // За коины
        { type: 'vehicle', color: 'gold',  chance: 2.11,  price: 4000, value: 'octavia18' },
        { type: 'vehicle', color: 'gold',  chance: 2.08,  price: 4000, value: 'bmwe38' },
        { type: 'vehicle', color: 'gold',  chance: 2.05,  price: 4000, value: 's600' },
        { type: 'vehicle', color: 'gold',  chance: 2.02,  price: 4500, value: 'vclass' }, // За коины
        { type: 'vehicle', color: 'gold',  chance: 1.99,  price: 4500, value: 'camry2' },
        { type: 'vehicle', color: 'gold',  chance: 1.96,  price: 5000, value: 'bmwe39' },
        { type: 'vehicle', color: 'gold',  chance: 1.93,  price: 5000, value: 'w210' },
        { type: 'vehicle', color: 'gold',  chance: 1.90,  price: 5000, value: 'golf7r' },
        { type: 'vehicle', color: 'gold',  chance: 1.87,  price: 5000, value: 'focusrs' },
        { type: 'vehicle', color: 'gold',  chance: 1.84,  price: 5200, value: 'impala' },
        { type: 'vehicle', color: 'gold',  chance: 1.81,  price: 5200, value: 'touareg2' },
        { type: 'vehicle', color: 'gold',  chance: 1.78,  price: 5250, value: 'dsprinter' }, // За коины
        { type: 'vehicle', color: 'gold',  chance: 1.75,  price: 5500, value: 'subwrx' },
        { type: 'vehicle', color: 'gold',  chance: 1.71,  price: 5625, value: 'nisgtr' }, // За коины
        { type: 'vehicle', color: 'gold',  chance: 1.68,  price: 5750, value: 'camaro2' },
        { type: 'vehicle', color: 'gold',  chance: 1.65,  price: 6000, value: 'msprinter' }, // За коины
        { type: 'vehicle', color: 'gold',  chance: 1.62,  price: 6000, value: '16challenger' },
        { type: 'vehicle', color: 'gold',  chance: 1.59,  price: 6000, value: 'camry70' },
        { type: 'vehicle', color: 'gold',  chance: 1.56,  price: 6000, value: 'accord' },
        { type: 'vehicle', color: 'gold',  chance: 1.53,  price: 6000, value: 'mustang2' },
        { type: 'vehicle', color: 'gold',  chance: 1.50,  price: 6200, value: 'mark2' },
        { type: 'vehicle', color: 'gold',  chance: 1.47,  price: 6500, value: 's15' },
        { type: 'vehicle', color: 'gold',  chance: 1.44,  price: 6500, value: 'a80' },
        { type: 'vehicle', color: 'gold',  chance: 1.41,  price: 6500, value: '370z' },
        { type: 'vehicle', color: 'gold',  chance: 1.38,  price: 6500, value: 'rx7' },
        { type: 'vehicle', color: 'gold',  chance: 1.35,  price: 6500, value: 'supragr' },
        { type: 'vehicle', color: 'gold',  chance: 1.32,  price: 6500, value: 'touareg' },
        { type: 'vehicle', color: 'gold',  chance: 1.29,  price: 6500, value: 'tahoe2' },
        { type: 'vehicle', color: 'gold',  chance: 1.26,  price: 6750, value: 'v4sp' }, // За коины
        { type: 'vehicle', color: 'gold',  chance: 1.23,  price: 6750, value: 'evo9' },
        { type: 'vehicle', color: 'gold',  chance: 1.20,  price: 6750, value: 'm5e60' },
        { type: 'vehicle', color: 'gold',  chance: 1.17,  price: 6750, value: 'kiastinger' },
        { type: 'vehicle', color: 'gold',  chance: 1.14,  price: 6750, value: 'bmwg20' },
        { type: 'vehicle', color: 'gold',  chance: 1.11,  price: 6750, value: '2020mustang' },
        { type: 'vehicle', color: 'gold',  chance: 1.08,  price: 6750, value: 'camaro21' },
        { type: 'vehicle', color: 'gold',  chance: 1.05,  price: 7000, value: 'lc200' },
        { type: 'vehicle', color: 'gold',  chance: 1.02,  price: 7000, value: 'x5me70' },
        { type: 'vehicle', color: 'gold',  chance: 0.99,  price: 7200, value: 'rs7' },
        { type: 'vehicle', color: 'gold',  chance: 0.96,  price: 7200, value: 'evo10' },
        { type: 'vehicle', color: 'gold',  chance: 0.93,  price: 7200, value: 'rrab' },
        { type: 'vehicle', color: 'gold',  chance: 0.90,  price: 7300, value: 'lex570' },
        { type: 'vehicle', color: 'gold',  chance: 0.87,  price: 7300, value: 'fx50s' },
        { type: 'vehicle', color: 'gold',  chance: 0.84,  price: 7500, value: 'velar' }, // За коины
        { type: 'vehicle', color: 'gold',  chance: 0.81,  price: 7500, value: 'brutale' }, // За коины
        { type: 'vehicle', color: 'gold',  chance: 0.78,  price: 7500, value: 'charger20' },
        { type: 'vehicle', color: 'gold',  chance: 0.75,  price: 7500, value: 'gsx1000' },
        { type: 'vehicle', color: 'gold',  chance: 0.72,  price: 7500, value: 'cesc21' },
        { type: 'vehicle', color: 'gold',  chance: 0.69,  price: 7500, value: 'modelx' },
        { type: 'vehicle', color: 'gold',  chance: 0.66,  price: 7500, value: 'skyline' },
        { type: 'vehicle', color: 'gold',  chance: 0.63,  price: 7750, value: 'ast' },
        { type: 'vehicle', color: 'gold',  chance: 0.60,  price: 7750, value: 'corvett08' },
        { type: 'vehicle', color: 'gold',  chance: 0.57,  price: 7750, value: 'i8' },
        { type: 'vehicle', color: 'gold',  chance: 0.54,  price: 7750, value: 'f150' },
        { type: 'vehicle', color: 'gold',  chance: 0.51,  price: 7750, value: 'macan' },
        { type: 'vehicle', color: 'gold',  chance: 0.48,  price: 7750, value: 'rs6' },
        { type: 'vehicle', color: 'gold',  chance: 0.45,  price: 7750, value: 'lc300' },
        { type: 'vehicle', color: 'gold',  chance: 0.42,  price: 7750, value: 'x5g05' },
        { type: 'vehicle', color: 'gold',  chance: 0.39,  price: 7750, value: 'z4b' },
        { type: 'vehicle', color: 'gold',  chance: 0.36,  price: 8000, value: 'jgc' },
        { type: 'vehicle', color: 'gold',  chance: 0.33,  price: 8000, value: 'm4comp' },
        { type: 'vehicle', color: 'gold',  chance: 0.30,  price: 8000, value: 'rs5' },
        { type: 'vehicle', color: 'gold',  chance: 0.27,  price: 8000, value: 'nsx' },
        { type: 'vehicle', color: 'gold',  chance: 0.24,  price: 8200, value: 'm850' },
        { type: 'vehicle', color: 'gold',  chance: 0.21,  price: 8200, value: 'g55' },
        { type: 'vehicle', color: 'gold',  chance: 0.18,  price: 8200, value: 'r820' },
        { type: 'vehicle', color: 'gold',  chance: 0.15,  price: 8250, value: '718bs' }, // За коины

        { type: 'subscription',  color: 'gold',  chance: 2,  price: 1200, value: 180 },
    ]
}
