module.exports = {
    id: 14,
    title: 'donate.config.cases.spring2024.title',
    discount: 10,

    start: ['2025-03-10', 'YYYY-MM-DD'],
    end: ['2025-06-01 00:00:00', 'YYYY-MM-DD HH:mm:ss'],
    transferDays: 30,

    price: 500,

    // altPrice: 150, // Цена кейса в альтернативной валюте
    // altCurrency: 'spring', // Тип альтернативной валюты


    // servers: ['STAGING0', 'DEVELOP0', 'TEST2'],

    disabledBuy: true,
    readOnly: false,

    // specificEnd: {
    //     'RU14': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    //     'TEST10': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    //     'STAGING0': ['2025-02-16 23:59:59', 'YYYY-MM-DD HH:mm:ss'],
    // },

    colors: [
        {
            color: 'gray',
            chance: 55,
            types: [
                { type: 'money',           chance: 40 },
                { type: 'coins',           chance: 30 },
                { type: 'animation',       chance: 20 },
                { type: 'vehicleDiscount', chance: 15 },
                { type: 'clothes',         chance: 20 },
            ],
        },
        {
            color: 'blue',
            chance: 23,
            types: [
                { type: 'money',           chance: 40 },
                { type: 'coins',           chance: 30 },
                { type: 'animation',       chance: 20 },
                { type: 'vehicleDiscount', chance: 15 },
                { type: 'clothes',         chance: 20 },

            ],
        },
        {
            color: 'purple',
            chance: 9,
            types: [
                { type: 'money',           chance: 40 },
                { type: 'coins',           chance: 30 },
                { type: 'animation',       chance: 20 },
                { type: 'armourSkin',      chance: 15 },
                { type: 'weaponSkin',      chance: 15 },
                { type: 'vehicleDiscount', chance: 15 },
                { type: 'clothes',         chance: 15 },
                { type: 'vehicleSet',      chance: 12 },
                { type: 'vehicle',         chance: 8 },
                { type: 'vehicleMod',      chance: 5 },
            ],
        },
        {
            color: 'red',
            chance: 6,
            types: [
                { type: 'money',           chance: 40 },
                { type: 'coins',           chance: 30 },
                { type: 'animation',       chance: 20 },
                { type: 'armourSkin',      chance: 15 },
                { type: 'weaponSkin',      chance: 15 },
                { type: 'vehicleDiscount', chance: 15 },
                { type: 'clothes',         chance: 15 },
                { type: 'vehicleSet',      chance: 12 },
                { type: 'vehicle',         chance: 8 },
                { type: 'vehicleMod',      chance: 5 },
            ],
        },
        {
            color: 'gold',
            chance: 3.2,
            types: [
                { type: 'money',           chance: 40 },
                { type: 'coins',           chance: 30 },
                { type: 'animation',       chance: 25 },
                { type: 'armourSkin',      chance: 20 },
                { type: 'weaponSkin',      chance: 20 },
                { type: 'vehicleDiscount', chance: 15 },
                { type: 'clothes',         chance: 10 },
                { type: 'vehicleSet',      chance: 10 },
                { type: 'vehicle',         chance: 5 },
                { type: 'vehicleMod',      chance: 2 },
            ],
        },
        {
            color: 'unique',
            chance: 0.4,
            types: [
                { type: 'clothes',         chance: 20 },
                { type: 'vehicle',         chance: 6 },
                { type: 'vehicleMod',      chance: 2 },
            ],
        }
    ],

    caseContent: [

        // Clothes
        { type: 'clothes',    color: 'gray',   chance: 1.00,   price: 200,   value: { gender: 0, component: 6,  drawable: 2098, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gray',   chance: 1.00,   price: 200,   value: { gender: 1, component: 6,  drawable: 2116, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gray',   chance: 1.00,   price: 200,   value: { gender: 1, component: 10, drawable: 2097, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gray',   chance: 1.00,   price: 200,   value: { gender: 1, component: 11, drawable: 2409, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gray',   chance: 1.00,   price: 200,   value: { gender: 1, component: 4,  drawable: 2222, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gray',   chance: 1.00,   price: 200,   value: { gender: 1, component: 6,  drawable: 2113, textures: 10, isProp: 0 } },

        { type: 'clothes',    color: 'blue',   chance: 1.00,   price: 300,   value: { gender: 1, component: 4,  drawable: 2227, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'blue',   chance: 1.00,   price: 300,   value: { gender: 1, component: 11, drawable: 2404, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'blue',   chance: 1.00,   price: 300,   value: { gender: 1, component: 4,  drawable: 2217, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'blue',   chance: 1.00,   price: 300,   value: { gender: 0, component: 11, drawable: 2410, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'blue',   chance: 1.00,   price: 300,   value: { gender: 0, component: 4,  drawable: 2204, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'blue',   chance: 1.00,   price: 300,   value: { gender: 0, component: 11, drawable: 2421, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'blue',   chance: 1.00,   price: 300,   value: { gender: 1, component: 4,  drawable: 2216, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'blue',   chance: 1.00,   price: 300,   value: { gender: 0, component: 11, drawable: 2423, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'blue',   chance: 1.00,   price: 300,   value: { gender: 1, component: 6,  drawable: 2117, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'blue',   chance: 1.00,   price: 300,   value: { gender: 0, component: 6,  drawable: 2099, textures: 10, isProp: 0 } },

        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 0, component: 6,  drawable: 2094, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 0, component: 11, drawable: 2411, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 0, component: 4,  drawable: 2205, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 0, component: 5,  drawable: 2067, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 1, component: 5,  drawable: 2066, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 0, component: 6,  drawable: 2093, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 1, component: 4,  drawable: 2221, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 1, component: 11, drawable: 2408, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 1, component: 6,  drawable: 2111, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 1, component: 6,  drawable: 2112, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 1, component: 11, drawable: 2410, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 1, component: 4,  drawable: 2224, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 1, component: 11, drawable: 2417, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 1, component: 4,  drawable: 2228, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 0, component: 11, drawable: 2420, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 0, component: 4,  drawable: 2207, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 0, component: 11, drawable: 2412, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 0, component: 4,  drawable: 2206, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 0, component: 11, drawable: 2413, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'purple', chance: 1.00,   price: 400,   value: { gender: 0, component: 11, drawable: 2424, textures: 10, isProp: 0 } },

        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 11, drawable: 2416, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 11, drawable: 2419, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 1,  drawable: 2132, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 0,  drawable: 2089, textures: 10, isProp: 1 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 11, drawable: 2406, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 4,  drawable: 2219, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 0, component: 6,  drawable: 2097, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 6,  drawable: 2115, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 4,  drawable: 2223, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 11, drawable: 2412, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 11, drawable: 2415, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 7,  drawable: 2121, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 0, component: 7,  drawable: 2143, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 0, component: 4,  drawable: 2208, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 0, component: 11, drawable: 2414, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 0, component: 11, drawable: 2422, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 5,  drawable: 2065, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 0, component: 6,  drawable: 2102, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'red',    chance: 1.00,   price: 750,  value: { gender: 1, component: 6,  drawable: 2121, textures: 10, isProp: 0 } },

        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 6,  drawable: 2122, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 0, component: 6,  drawable: 2101, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 0, component: 11, drawable: 2416, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 0, component: 11, drawable: 2409, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 0, component: 4,  drawable: 2203, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 6,  drawable: 2120, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 6,  drawable: 2004, textures: 3,  isProp: 1 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 6,  drawable: 2005, textures: 3,  isProp: 1 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 6,  drawable: 2114, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 0, component: 6,  drawable: 2095, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 11, drawable: 2405, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 4,  drawable: 2218, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 4,  drawable: 2225, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 11, drawable: 2411, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 4,  drawable: 2226, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 11, drawable: 2413, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 0, component: 6,  drawable: 2096, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 6,  drawable: 2118, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 1, component: 5,  drawable: 2062, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'gold',   chance: 1.00,   price: 1200,  value: { gender: 0, component: 5,  drawable: 2065, textures: 10, isProp: 0 } },

        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 0, component: 11, drawable: 2418, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 0, component: 11, drawable: 2425, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 0, component: 6,  drawable: 2100, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 1, component: 6,  drawable: 2119, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 1, component: 11, drawable: 2414, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 1, component: 11, drawable: 2418, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 1, component: 5,  drawable: 2063, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 0, component: 5,  drawable: 2066, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 1, component: 1,  drawable: 2027, texture:  0,  isProp: 1 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 1, component: 2,  drawable: 2026, textures: 5,  isProp: 1 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 1, component: 3,  drawable: 2180, textures: 10, isProp: 0 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 0, component: 1,  drawable: 2026, texture:  0,   isProp: 1 } },
        { type: 'clothes',    color: 'unique', chance: 1.00,   price: 2500,  value: { gender: 0, component: 2,  drawable: 2002, textures: 5,  isProp: 1 } },

        // armourSkins
        { type: 'armourSkin', color: 'purple', chance: 0.85, price: 400,   value: { entityName: 'light', skinId: 106 } },
        { type: 'armourSkin', color: 'purple', chance: 0.85, price: 400,   value: { entityName: 'light', skinId: 107 } },

        { type: 'armourSkin', color: 'red',    chance: 0.75, price: 750,  value: { entityName: 'light', skinId: 104 } },
        { type: 'armourSkin', color: 'red',    chance: 0.75, price: 750,  value: { entityName: 'light', skinId: 105 } },
        { type: 'armourSkin', color: 'red',    chance: 0.75, price: 750,  value: { entityName: 'light', skinId: 110 } },

        { type: 'armourSkin', color: 'gold',   chance: 0.65, price: 1200,  value: { entityName: 'light', skinId: 108 } },
        { type: 'armourSkin', color: 'gold',   chance: 0.65, price: 1200,  value: { entityName: 'light', skinId: 109 } },

        // weaponSkins Love
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 400, value: { entityName: 'weapon_tecpistol', skinId: 0 } },
        { type: 'weaponSkin', color: 'purple', chance: 0.90, price: 400, value: { entityName: 'weapon_gusenberg', skinId: 5 } },

        { type: 'weaponSkin', color: 'red',    chance: 0.90, price: 750, value: { entityName: 'weapon_assaultrifle_mk2', skinId: 12 } },
        { type: 'weaponSkin', color: 'red',    chance: 0.90, price: 750, value: { entityName: 'weapon_carbinerifle_mk2', skinId: 13 } },
        { type: 'weaponSkin', color: 'red',    chance: 0.90, price: 750, value: { entityName: 'weapon_heavyshotgun', skinId: 16 } },
        { type: 'weaponSkin', color: 'red',    chance: 0.90, price: 750, value: { entityName: 'weapon_tacticalrifle', skinId: 0} },

        { type: 'weaponSkin', color: 'gold',   chance: 0.90, price: 1200, value: { entityName: 'weapon_heavyrifle', skinId: 0 } },
        { type: 'weaponSkin', color: 'gold',   chance: 0.85, price: 1200, value: { entityName: 'weapon_heavysniper_mk2', skinId: 11} },
        { type: 'weaponSkin', color: 'gold',   chance: 0.90, price: 1200, value: { entityName: 'weapon_combatmg_mk2', skinId: 0 } },


		// Vehicles
		{ type: 'vehicle',    color: 'purple',    chance: 25,   price: 900,  value: 'victoria'  },
		{ type: 'vehicle',    color: 'purple',    chance: 15,   price: 900,  value: 'eclipse'   },

		{ type: 'vehicle',    color: 'red', chance: 20,   price: 2500,  value: 'k5'        },
		{ type: 'vehicle',    color: 'red', chance: 15,   price: 3000,  value: 'brz'       },

		{ type: 'vehicle',    color: 'gold',   chance: 30,   price: 6000, value: 'db11'      },
		{ type: 'vehicle',    color: 'gold',   chance: 15,   price: 8000, value: 'f1502'     },
		{ type: 'vehicle',    color: 'gold',   chance: 5,   price: 10000, value: 'emira'     },

		{ type: 'vehicle',    color: 'unique', chance: 10,   price: 30000, value: 'senna'     },

		// vehMod
		{ type: 'vehicleMod', color: 'purple', chance: 40, price: 1200, value: { model: 'victoria', setId: 2, group: 9 } },
		//{ type: 'vehicleMod', color: 'purple', chance: 35, price: 1300, value: { model: 'victoria', setId: 3, group: 9 } },
		//{ type: 'vehicleMod', color: 'purple', chance: 30, price: 1400, value: { model: 'victoria', setId: 4, group: 9 } },
		{ type: 'vehicleMod', color: 'purple', chance: 30, price: 1200, value: { model: 'eclipse', setId: 4, group: 10 } },
		//{ type: 'vehicleMod', color: 'purple', chance: 25, price: 1300, value: { model: 'eclipse', setId: 5, group: 10 } },
		//{ type: 'vehicleMod', color: 'purple', chance: 20, price: 1400, value: { model: 'eclipse', setId: 6, group: 10 } },

		{ type: 'vehicleMod', color: 'red', chance: 12, price: 3500, value: { model: 'brz', setId: 3, group: 11 } },
		//{ type: 'vehicleMod', color: 'red', chance: 9, price: 3700, value: { model: 'brz', setId: 4, group: 11 } },
		//{ type: 'vehicleMod', color: 'red', chance: 6, price: 4000, value: { model: 'brz', setId: 5, group: 11 } },

		{ type: 'vehicleMod', color: 'gold', chance: 35, price: 6500, value: { model: 'db11', setId: 3, group: 12 } },
		//{ type: 'vehicleMod', color: 'gold', chance: 30, price: 7000, value: { model: 'db11', setId: 4, group: 12 } },
		//{ type: 'vehicleMod', color: 'gold', chance: 25, price: 7500, value: { model: 'db11', setId: 5, group: 12 } },
		{ type: 'vehicleMod', color: 'gold', chance: 60, price: 6500, value: { model: 'victoria', setId: 8, group: 13 } },
		//{ type: 'vehicleMod', color: 'gold', chance: 55, price: 7000, value: { model: 'victoria', setId: 9, group: 13 } },
		{ type: 'vehicleMod', color: 'gold', chance: 12, price: 11000, value: { model: 'emira', setId: 3, group: 14 } },
		//{ type: 'vehicleMod', color: 'gold', chance: 9,  price: 12000, value: { model: 'emira', setId: 4, group: 14 } },
		//{ type: 'vehicleMod', color: 'gold', chance: 6,  price: 13000, value: { model: 'emira', setId: 5, group: 14 } },
		{ type: 'vehicleMod', color: 'gold', chance: 50, price: 6000, value: { model: 'k5', setId: 4, group: 15 } },
		//{ type: 'vehicleMod', color: 'gold', chance: 45, price: 6500, value: { model: 'k5', setId: 5, group: 15 } },
		//{ type: 'vehicleMod', color: 'gold', chance: 40, price: 7000, value: { model: 'k5', setId: 6, group: 15 } },

		{ type: 'vehicleMod', color: 'unique', chance: 25, price: 15000, value: { model: 'f1502', setId: 3, group: 16 } },
		//{ type: 'vehicleMod', color: 'unique', chance: 20, price: 16000, value: { model: 'f1502', setId: 4, group: 16 } },
		//{ type: 'vehicleMod', color: 'unique', chance: 15, price: 17000, value: { model: 'f1502', setId: 5, group: 16 } },
		{ type: 'vehicleMod', color: 'unique', chance: 20, price: 20000, value: { model: 'emira', setId: 9, group: 17 } },
		//{ type: 'vehicleMod', color: 'unique', chance: 15, price: 22000, value: { model: 'emira', setId: 10, group: 17 } },
		//{ type: 'vehicleMod', color: 'unique', chance: 10, price: 25000, value: { model: 'emira', setId: 11, group: 17 } },
		{ type: 'vehicleMod', color: 'unique', chance: 9, price: 30000, value: { model: 'chiron19', setId: 9, group: 18 } },
		//{ type: 'vehicleMod', color: 'unique', chance: 6, price: 31000, value: { model: 'chiron19', setId: 10, group: 18 } },
		//{ type: 'vehicleMod', color: 'unique', chance: 3, price: 32000, value: { model: 'chiron19', setId: 11, group: 18 } },

		// Vehicles tuning
		{ type: 'vehicleSet', color: 'purple', chance: 15,   price: 600, value:  { model: 'q60s',    setId: 4, group: 19 } },
		//{ type: 'vehicleSet', color: 'purple', chance: 12,   price: 700, value:  { model: 'q60s',    setId: 5, group: 19 } },
		//{ type: 'vehicleSet', color: 'purple', chance: 9,    price: 800, value:  { model: 'q60s',    setId: 6, group: 19 } },
		{ type: 'vehicleSet', color: 'purple', chance: 15,   price: 700, value:  { model: 's2000',    setId: 3, group: 20 } },
		//{ type: 'vehicleSet', color: 'purple', chance: 12,   price: 600, value:  { model: 's2000',    setId: 4, group: 20} },
		//{ type: 'vehicleSet', color: 'purple', chance: 9,    price: 800, value:  { model: 's2000',    setId: 5, group: 20 } },
		{ type: 'vehicleSet', color: 'purple', chance: 18,   price: 600, value:  { model: 'carrera',    setId: 2, group: 21 } },
		//{ type: 'vehicleSet', color: 'purple', chance: 15,   price: 700, value:  { model: 'carrera',    setId: 3, group: 21 } },
		//{ type: 'vehicleSet', color: 'purple', chance: 12,   price: 800, value:  { model: 'carrera',    setId: 4, group: 21 } },
		//{ type: 'vehicleSet', color: 'purple', chance: 9,    price: 900, value:  { model: 'carrera',    setId: 5, group: 21 } },

		{ type: 'vehicleSet', color: 'red', chance: 15,   price: 900,  value: { model: 'q8',    setId: 13, group: 22 } },
		//{ type: 'vehicleSet', color: 'red', chance: 12,   price: 1000, value: { model: 'q8',    setId: 14, group: 22 } },
		//{ type: 'vehicleSet', color: 'red', chance: 9,    price: 1200, value: { model: 'q8',    setId: 15, group: 22 } },
		{ type: 'vehicleSet', color: 'red', chance: 15,   price: 900,  value: { model: 'panamera17turbo',    setId: 9, group: 23 } },
		//{ type: 'vehicleSet', color: 'red', chance: 12,   price: 1000, value: { model: 'panamera17turbo',    setId: 10, group: 23 } },
		//{ type: 'vehicleSet', color: 'red', chance: 9,    price: 1200, value: { model: 'panamera17turbo',    setId: 11, group: 23 } },

		{ type: 'vehicleSet', color: 'gold', chance: 18,   price: 2000, value: { model: 'rrphantom',    setId: 8, group: 24 } },
		//{ type: 'vehicleSet', color: 'gold', chance: 15,   price: 2500, value: { model: 'rrphantom',    setId: 9, group: 24 } },
		//{ type: 'vehicleSet', color: 'gold', chance: 12,   price: 3000, value: { model: 'rrphantom',    setId: 10, group: 24 } },
		//{ type: 'vehicleSet', color: 'gold', chance: 9,    price: 3500, value: { model: 'rrphantom',    setId: 11, group: 24 } },
		{ type: 'vehicleSet', color: 'gold', chance: 15,   price: 2500, value: { model: 'countach',    setId: 6, group: 25 } },
		//{ type: 'vehicleSet', color: 'gold', chance: 12,   price: 3000, value: { model: 'countach',    setId: 7, group: 25 } },
		//{ type: 'vehicleSet', color: 'gold', chance: 9,    price: 3500, value: { model: 'countach',    setId: 8, group: 25 } },

        // Анимации
        { type: 'animation',       color: 'gray',   chance: 1,   price: 200,    value: 320 }, // Kiss Kiss
        { type: 'animation',       color: 'gray',   chance: 1,   price: 200,    value: 319 }, // Pony Up, аналог из магазина только с розовым конем
        { type: 'animation',       color: 'gray',   chance: 1,   price: 200,    value: 64  }, // Never Gonna

        { type: 'animation',       color: 'blue',   chance: 1,   price: 300,    value: 321 }, // Heart Sign
        { type: 'animation',       color: 'blue',   chance: 1,   price: 300,    value: 127 }, // Glitter
        { type: 'animation',       color: 'blue',   chance: 1,   price: 300,    value: 173 }, // Dance Monkey
        { type: 'animation',       color: 'blue',   chance: 1,   price: 300,    value: 263 }, // Wiggle

        { type: 'animation',       color: 'purple', chance: 1,   price: 400,    value: 298 }, // Omg I Love It
        { type: 'animation',       color: 'purple', chance: 1,   price: 400,    value: 118 }, // Poki
        { type: 'animation',       color: 'purple', chance: 1,   price: 400,    value: 219 }, // The Dance Laroi
        { type: 'animation',       color: 'purple', chance: 1,   price: 400,    value: 208 }, // Lunar Party

        { type: 'animation',       color: 'red',    chance: 1,   price: 750,    value: 297 }, // Heart Attach
        { type: 'animation',       color: 'red',    chance: 1,   price: 750,    value: 61  }, // Bim Bam Boom

        { type: 'animation',       color: 'gold',   chance: 1,   price: 900,   value: 95  }, // Lil Floaticorn


        // Скидка на транспорт
        { type: 'vehicleDiscount', color: 'gray',   chance: 50.00, price: 125,  value: 5  },

        { type: 'vehicleDiscount', color: 'blue',   chance: 30.00, price: 250,  value: 10 },

        { type: 'vehicleDiscount', color: 'purple', chance: 15.00, price: 500,  value: 15 },

        { type: 'vehicleDiscount', color: 'red',    chance: 4.00,  price: 750,  value: 20 },

        { type: 'vehicleDiscount', color: 'gold',   chance: 1.00,  price: 1200, value: 25 },

        // Вирты
        { type: 'money',   color: 'gray',           chance: 50.00, price: 150,   value: 12000    },

        { type: 'money',   color: 'blue',           chance: 30.00, price: 300,  value: 25000   },

        { type: 'money',   color: 'purple',         chance: 15.00, price: 500,  value: 45000   },

        { type: 'money',   color: 'red',            chance: 4.00,  price: 1000,  value: 90000  },

        { type: 'money',   color: 'gold',           chance: 1.00,  price: 2500, value: 240000  },

        // Коины
        { type: 'coins',   color: 'gray',           chance: 50.00, price: 150,   value: 150   },

        { type: 'coins',   color: 'blue',           chance: 30.00, price: 300,  value: 300  },

        { type: 'coins',   color: 'purple',         chance: 15.00, price: 500,  value: 500  },

        { type: 'coins',   color: 'red',            chance: 4.00,  price: 1000, value: 1000 },

        { type: 'coins',   color: 'gold',           chance: 1.00,  price: 2500, value: 2500 },
    ]
}
