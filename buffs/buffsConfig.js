export const diseaseBuffs = [
    'Cold',
    'Covid',
    'Poisoning',
    'Diarrhea'
];

export const buffs = {
    VirusProtection: {
        icon: 1,
        bgColor: [0, 250, 150, 0.5],
        hiddenOnMinimap: true,
    },

    UseItem: {
        icon: 2,
        bgColor: [0, 250, 150, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'UseItemEffect',
            }
        ],
        hiddenOnMinimap: true,
    },

    UseItemBong: {
        icon: 2,
        bgColor: [0, 250, 150, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'UseItemBongEffect',
            }
        ],
        hiddenOnMinimap: true,
    },

    UseItemMedkit: {
        icon: 2,
        bgColor: [0, 250, 150, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'UseItemMedkitEffect',
            }
        ],
        hiddenOnMinimap: true,
    },

    UseItemCig: {
        icon: 2,
        bgColor: [0, 250, 150, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'UseItemCigEffect',
            }
        ],
        hiddenOnMinimap: true,
    },

    UseItemVape: {
        icon: 2,
        bgColor: [0, 250, 150, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'UseItemCigEffect',
            }
        ],
        hiddenOnMinimap: true,
    },

    Cold: {
        icon: 1,
        bgColor: [190, 34, 50, 0.5],
        effects: [
            {
                name: 'DiseaseEffect',
                params: {
                    name: 'illness.cold',
                    debuffs: { hp: -10 },
                    debuffsEveryTime: 5,
                    infectious: true,
                    infectSound: true,
                    infectRate: 5,

                    gameEffectsEveryTime: 5,
                    gameEffects: ['sneeze', 'cough', 'sniff'],

                    cameraEffectsEveryTime: 5,
                    cameraEffects: ['drunk'],

                    screenEffectsEveryTime: 5,
                    screenEffects: ['InchOrange'],

                    healItem: 168
                }
            },
            // {
            //     name: 'ModifyStatsEffect',
            //     params: {
            //         stamina: -5,
            //         lung_capacity: -5,
            //         strength: -5
            //     }
            // }
        ],
    },

    Covid: {
        icon: 1,
        bgColor: [190, 34, 50, 0.5],
        // shared: false,
        // hidden: false,
        effects: [
            {
                name: 'DiseaseEffect',
                params: {
                    name: 'illness.covid',

                    debuffs: { hp: -15 },
                    debuffsEveryTime: 5,

                    infectious: true,
                    infectSound: true,
                    infectRate: 8,

                    gameEffectsEveryTime: 5,
                    gameEffects: ['sneeze', 'cough', 'sniff'],

                    cameraEffectsEveryTime: 5,
                    cameraEffects: ['drunk'],

                    screenEffectsEveryTime: 5,
                    screenEffects: ['InchOrange'],

                    healItem: 124
                }
            },
            // {
            //     name: 'ModifyStatsEffect',
            //     params: {
            //         stamina: -10,
            //         lung_capacity: -50,
            //         strength: -5
            //     }
            // }
        ],
    },

    Poisoning: {
        icon: 1,
        bgColor: [190, 34, 50, 0.5],
        effects: [
            {
                name: 'DiseaseEffect',
                params: {
                    debuffs: { hp: -10, hunger: -25, water: -25 },
                    debuffsEveryTime: 30,

                    infectious: false,
                    infectSound: true,

                    gameEffectsEveryTime: 30,
                    gameEffects: ['vomit'],

                    screenEffectsEveryTime: 30,
                    screenEffects: ['ChopVision'],

                    //particleEffectsEveryTime: 30,
                    //particleEffects: [{lib: 'cut_bigscr', name: 'cut_bigscr_vomit', bone: 20279, duration: 1500}],

                    healItem: 169
                }
            },
            // {
            //     name: 'ModifyStatsEffect',
            //     params: {
            //         stamina: -5,
            //         strength: -20
            //     }
            // }
        ],
    },

    Diarrhea: {
        icon: 1,
        bgColor: [190, 34, 50, 0.5],
        effects: [
            {
                name: 'DiseaseEffect',
                params: {
                    debuffs: { hp: -10, hunger: -15, water: -30 },
                    debuffsEveryTime: 30,

                    infectious: false,
                    infectSound: true,

                    gameEffectsEveryTime: 30,
                    gameEffects: ['diarrhea'],

                    // screenEffectsEveryTime: 30,
                    // screenEffects: [],

                    //particleEffectsEveryTime: 30,
                    //particleEffects: [{lib: 'cut_bigscr', name: 'cut_bigscr_vomit', bone: 20279, duration: 1500}],

                    healItem: 169
                }
            }
        ],
    },

    // Мухомор
    Agaric: {
        icon: 35,
        bgColor: [134, 67, 201, 0.5],
        stack: 1,
        maxStack: 4,
        dispellOnDeath: true,
        effects: [
            {
                name: 'WeedEffect',
                params: {
                    type: 1,
                    healConfig: {
                        1: 0.4,
                        2: 0.8,
                        3: 1.2,
                    },
                    conflictBuffs: ['Green', 'Blue', 'White', 'HealCapsule']
                },
            },
        ]
    },

    Green: {
        icon: 2,
        bgColor: [74, 116, 53, 0.5],
        stack: 1,
        maxStack: 4,
        dispellOnDeath: true,
        effects: [
            {
                name: 'WeedEffect',
                params: {
                    type: 1,
                    healConfig: {
                        1: 0.4,
                        2: 0.8,
                        3: 1.2,
                    },
                    conflictBuffs: ['Blue', 'White', 'Agaric', 'HealCapsule']
                }
            },
        ]
    },

    Blue: {
        icon: 2,
        bgColor: [96, 116, 220, 0.5],
        stack: 1,
        maxStack: 4,
        dispellOnDeath: true,
        effects: [
            {
                name: 'WeedEffect',
                params: {
                    type: 2,
                    healConfig: {
                        1: 1.2,
                        2: 1.6,
                        3: 2,
                    },
                    conflictBuffs: ['Green', 'White', 'Agaric', 'HealCapsule']
                }
            }
        ]
    },

    White: {
        icon: 2,
        bgColor: [158, 158, 158, 0.5],
        stack: 1,
        maxStack: 4,
        dispellOnDeath: true,
        effects: [
            {
                name: 'WeedEffect',
                params: {
                    type: 3,
                    healConfig: {
                        1: 2,
                        2: 2.4,
                        3: 2.8,
                    },
                    conflictBuffs: ['Green', 'Blue', 'Agaric', 'HealCapsule']
                }
            }
        ]
    },

    Hunger: {
        icon: 3,
        bgColor: [245, 152, 60, 0.5],
        effects: [
            {
                name: 'HungerEffect',
            }
        ]
    },

    Dehydration: {
        icon: 4,
        bgColor: [82, 160, 232, 0.5],
        effects: [
            {
                name: 'DehydrationEffect',
            }
        ]
    },

    Drunk: {
        icon: 5,
        bgColor: [123, 26, 61, 0.5],
        stack: 1,
        maxStack: 6,
        dispellOnDeath: true,
        effects: [
            {
                name: 'DrunkEffect',
            }
        ]
    },

    HealCapsule: {
        icon: 6,
        bgColor: [0, 250, 150, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'WeedEffect',
                params: {
                    type: 1,
                    healConfig: {
                        1: 2.8,
                    },
                    conflictBuffs: ['Green', 'Blue', 'White', 'Agaric']
                },
            },
        ]
    },

    Styling: {
        icon: 7,
        bgColor: [68, 96, 243, 0.5],
        dispellOnDeath: false,
        dispellOnFullDeath: true,
    },

    GovernorAppreciation: {
        icon: 8,
        bgColor: [52, 166, 34, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    policePaydayMoneyMult: 50,
                    fibPaydayMoneyMult: 50,
                    emsPaydayMoneyMult: 50,
                    armyPaydayMoneyMult: 50,
                    govPaydayMoneyMult: 50,
                    newsPaydayMoneyMult: 50,
                }
            }
        ]
    },

    Biolink: {
        icon: 9,
        bgColor: [54, 164, 44, 0.5],
        hiddenOnMinimap: true,
    },

    Bioadditive: {
        icon: 10,
        bgColor: [68, 96, 243, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    farmerGainMult: 100,
                    fishingGainMult: 100,
                    garbagecollectorGainMult: 100,
                    gopostalGainMult: 100,
                    moneycollectorGainMult: 100,
                    truckerGainMult: 100,
                    lumberjackGainMult: 100,
                    mushroomerGainMult: 100,
                    quarrymanGainMult: 100,
                    busdriverGainMult: 100,
                    miningGainMult: 100,
                    orgcrimeGainMult: 100,
                    orgjobGainMult: 100,
                }
            }
        ]
    },

    Subscription: {
        icon: 10,
        bgColor: [68, 96, 243, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    dailyCoins: 100,
                }
            }
        ]
    },

    SomeBuff2: {
        icon: 10,
        bgColor: [68, 96, 243, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    farmerGainMult: -30,
                    fishingGainMult: 250,
                }
            }
        ]
    },

    ProteinBar: {
        icon: 11,
        bgColor: [245, 152, 60, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    staminaGainMult: 100,
                    strengthGainMult: 100,
                    lungCapacityGainMult: 100,
                }
            }
        ]
    },

    MagicCandyBlack: {
        icon: 12,
        bgColor: [146, 79, 214, 0.5],
        dispellOnFullDeath: true,
        effects: [
            {
                name: 'DispellEffect',
                params: {
                    targets: ['MagicCandyWhite']
                }
            },
            {
                name: 'MagicCandyEffect',
                params: {
                    skins: ['elffour', 'elffive', 'elfsix'],
                    sound: 'xmas/candy_black',
                    screenEffect: 'MP_OrbitalCannon',
                    updateSync: true,
                }
            },
        ]
    },

    MagicCandyWhite: {
        icon: 13,
        bgColor: [67, 201, 153, 0.5],
        dispellOnFullDeath: true,
        effects: [
            {
                name: 'DispellEffect',
                params: {
                    targets: ['MagicCandyBlack']
                }
            },
            {
                name: 'MagicCandyEffect',
                params: {
                    skins: ['elffirst', 'elfsecond', 'elfthird'],
                    sound: 'xmas/candy_white',
                    screenEffect: 'DrugsTrevorClownsFight',
                    updateSync: false,
                }
            },
        ]
    },

    Mutesound: {
        icon: 14,
        bgColor: [39, 39, 39, 0.5],
    },

    Mutemicrophone: {
        icon: 40,
        bgColor: [39, 39, 39, 0.5],
    },

    Ooc: {
        icon: 16,
        bgColor: [39, 39, 39, 0.5],
    },

    Gunban: {
        icon: 17,
        bgColor: [190, 34, 50, 0.5],
        effects: [
            {
                name: 'GunbanEffect'
            }
        ]
    },

    // Глушилка. Бафф
    Jammergreen: {
        icon: 18,
        bgColor: [71, 154, 31, 0.5],
        effects: [
            {
                name: 'JammergreenEffect'
            }
        ]
    },

    // Глушилка. Дебафф
    Jammerred: {
        icon: 18,
        bgColor: [190, 34, 50, 0.5],
        effects: [
            {
                name: 'JammerredEffect'
            }
        ]
    },

    EnergyDrink: {
        icon: 19,
        bgColor: [134, 67, 201, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    movementSpeedMult: 10,
                    stamina: 100,
                    overloadImmune: 1
                }
            }
        ]
    },

    Overweight: {
        icon: 20,
        bgColor: [39, 39, 39, 0.5],
        effects: [
            {
                name: 'AdvantageEffect'
            }
        ]
    },

    DemorganOnExit: {
        icon: 21,
        bgColor: [39, 39, 39, 0.5],
    },

    Cuffed: {
        icon: 21,
        bgColor: [39, 39, 39, 0.5],
        dispellOnFullDeath: true,
        effects: [
            {
                name: 'CuffEffect',
                params: {
                    syncVariable: 'cuffed',
                    attachProp: 'cuffs'
                }
            },
            {
                name: 'DemorganOnExit'
            }
        ]
    },

    Tied: {
        icon: 21,
        bgColor: [39, 39, 39, 0.5],
        dispellOnFullDeath: true,
        effects: [
            {
                name: 'CuffEffect',
                params: {
                    syncVariable: 'tiedHands',
                    attachProp: 'ziptie'
                }
            }
        ]
    },

    Jail: {
        icon: 22,
        bgColor: [39, 39, 39, 0.5],
        effects: [
            {
                name: 'JailEffect',
            }
        ]
    },

    Demorgan: {
        icon: 22,
        bgColor: [39, 39, 39, 0.5],
        effects: [
            {
                name: 'DemorganEffect',
            }
        ]
    },

    Fire: {
        icon: 23,
        bgColor: [190, 34, 50, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'FireEffect',
            }
        ]
    },

    Drowning: {
        icon: 24,
        bgColor: [39, 39, 39, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'DrowningEffect',
            }
        ]
    },

    Godmode: {
        icon: 25,
        bgColor: [71, 154, 31, 0.5],
        effects: [
            {
                name: 'GodmodeEffect',
            }
        ]
    },

    Invisibility: {
        icon: 26,
        bgColor: [134, 67, 201, 0.5],
        effects: [
            {
                name: 'InvisibilityEffect',
            }
        ]
    },

    Fracture: {
        icon: 27,
        bgColor: [190, 34, 50, 0.5],
        effects: [
            {
                name: 'FractureEffect',
            }
        ]
    },

    Shocker: {
        icon: 28,
        bgColor: [190, 34, 50, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'ShockerEffect',
            }
        ]
    },

    DispellDebuffs: {
        icon: 29,
        bgColor: [52, 166, 34, 0.5],
        effects: [
            {
                name: 'DispellEffect',
                params: {
                    targets: ['Green', 'Blue', 'White', 'Drunk']
                }
            }
        ]
    },

    ResetStamina: {
        icon: 30,
        bgColor: [52, 166, 34, 0.5],
        effects: [
            {
                name: 'ResetStaminaEffect',
            },
        ]
    },

    HiddenIdentity: {
        icon: 39,
        bgColor: [39, 39, 39, 0.5],
        hiddenOnMinimap: true,
        shared: true,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    hiddenIdentity: 1,
                }
            },
        ]
    },

    HiddenIdentityCream: {
        icon: 39,
        bgColor: [39, 39, 39, 0.5],
        hiddenOnMinimap: false,
        shared: true,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    hiddenIdentity: 1
                }
            },
        ]
    },

    Jammergps: {
        icon: 32,
        bgColor: [190, 34, 50, 0.5],
        // effects: [
        //     {
        //         name: 'JammergpsEffect',
        //     },
        // ]
    },

    Taxes: {
        icon: 33,
        bgColor: [55, 117, 236, 0.5],
        hiddenOnMinimap: true,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    taxesMult: -50
                }
            },
        ]
    },

    ExtractionQuarryman: {
        icon: 34,
        bgColor: [55, 117, 236, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    quarrymanX2Chance: 25,
                }
            },
        ]
    },

    ExtractionLumberjack: {
        icon: 34,
        bgColor: [55, 117, 236, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    lumberjackX2Chance: 25
                }
            },
        ]
    },

    ExtractionHunter: {
        icon: 34,
        bgColor: [55, 117, 236, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    hunterX2Chance: 25
                }
            },
        ]
    },

    ExtractionMushroomer: {
        icon: 34,
        bgColor: [55, 117, 236, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    mushroomerX2Chance: 25
                }
            },
        ]
    },

    ExtractionFarmer: {
        icon: 34,
        bgColor: [55, 117, 236, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    farmerX2Chance: 25
                }
            },
        ]
    },

    HealCovid: {
        icon: 1,
        bgColor: [0, 0, 0, 0.5],
        effects: [
            {
                name: 'DispellEffect',
                params: {
                    targets: ['Covid']
                }
            },
        ]
    },

    HealCold: {
        icon: 1,
        bgColor: [0, 0, 0, 0.5],
        effects: [
            {
                name: 'DispellEffect',
                params: {
                    targets: ['Cold']
                }
            },
        ]
    },

    HealPoisoning: {
        icon: 1,
        bgColor: [0, 0, 0, 0.5],
        effects: [
            {
                name: 'DispellEffect',
                params: {
                    targets: ['Poisoning']
                }
            },
        ]
    },

    PowerPillows: {
        icon: 1,
        bgColor: [255, 255, 255, 0.5],
        shared: false,
        hidden: false,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    stength: 2,
                }
            }
        ]
    },

    ShootgunDelay: {
        icon: 13,
        bgColor: [0, 250, 150, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    movementSpeedMult: -50,
                }
            }
        ]
    },

    Describe: {
        icon: 13,
        bgColor: [0, 250, 150, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    movementSpeedMult: -50,
                }
            }
        ]
    },

    ViolaBloodDrunk: {
        icon: 13,
        bgColor: [0, 250, 150, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'ViolaBloodEffect'
            }
        ]
    },

    FastBoots: {
        icon: 11,
        bgColor: [255, 255, 255, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    movementSpeedMult: 1000,
                }
            }
        ]
    },

    Overdose: {
        icon: 1,
        bgColor: [255, 0, 0, 0.5],
        dispellOnDeath: true,
        effects: [
            {
                name: 'OverdoseEffect',
            }
        ]
    },

    StatsDebug: {
        icon: 20,
        bgColor: [0, 0, 0, 0.5],
    },

    Metabolism: {
        icon: 3,
        bgColor: [255, 255, 255, 0.5],
        shared: false,
        effects: [
            {
                name: 'MetabolismEffect',
            }
        ],
        hiddenOnMinimap: true,
    },

    DispellCovid: {
        icon: 1,
        bgColor: [0, 0, 0, 0.5],
        effects: [
            {
                name: 'DispellEffect',
                params: {
                    targets: ['Covid']
                }
            }
        ]
    },

    HealDiarrhea: {
        icon: 1,
        bgColor: [0, 0, 0, 0.5],
        effects: [
            {
                name: 'DispellEffect',
                params: {
                    targets: ['Diarrhea']
                }
            },
        ]
    },

    ActivatedCoal: {
        icon: 1,
        bgColor: [0, 0, 0, 0.5],
        effects: [
            {
                name: 'DispellEffect',
                params: {
                    targets: ['Poisoning']
                }
            }
        ]
    },

    CovidVacine: {
        icon: 1,
        bgColor: [0, 0, 0, 0.5],
        effects: [
            {
                name: 'DispellEffect',
                params: {
                    targets: ['Covid']
                }
            }
        ]
    },

    Vacine: {
        icon: 1,
        bgColor: [75, 0, 130, 0.5],
        // hiddenOnMinimap: true,
        effects: [
            {
                name: 'DispellEffect',
                params: {
                    targets: ['Cold', 'Covid', 'Poisoning', 'Diarrhea']
                }
            }
        ]
    },

    GrindingStone: {
        icon: 34,
        bgColor: [150, 150, 150, 0.5],
    },

    TapedMouth: {
        icon: 36,
        bgColor: [190, 34, 50, 0.5],
        effects: [
            {
                name: 'GagEffect'
            }
        ]
    },

    TiedLegs: {
        icon: 21,
        bgColor: [150, 150, 150, 0.5],
        effects: [
            {
                name: 'TiedLegsEffect'
            }
        ]
    },

    SomeBuff: {
        icon: 5,
        bgColor: [150, 150, 150, 0.5],
        effects: [
            {
                name: 'SomeEffect1'
            },
            {
                name: 'SomeEffect2'
            },
            {
                name: 'ModifyStatsEffect',
                params: {
                    someStat: 1
                }
            }
        ]
    },


    BongCommon: {
        icon: 2,
        bgColor: [150, 150, 150, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    greenDurationMult: 250,
                }
            }
        ]
    },

    BongUncommon: {
        icon: 2,
        bgColor: [150, 150, 150, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    greenDurationMult: 250,
                    blueDurationMult: 250,
                    whiteDurationMult: 150
                }
            }
        ]
    },

    BongRare: {
        icon: 2,
        bgColor: [150, 150, 150, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    greenDurationMult: 250,
                    blueDurationMult: 250,
                    whiteDurationMult: 250
                }
            }
        ]
    },

    BongMarley: {
        icon: 2,
        bgColor: [150, 150, 150, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    greenDurationMult: 500,
                    blueDurationMult: 500,
                    whiteDurationMult: 500
                }
            }
        ]
    },

    Overload: {
        icon: 20,
        bgColor: [39, 39, 39, 0.5],
        effects: [
            {
                name: 'OverloadEffect'
            }
        ]
    },

    Capacity: {
        icon: 1,
        bgColor: [255, 255, 255, 255],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    inventoryOverloadCapacity: 5000
                }
            }
        ]
    },

    BostonSpecialEvent_1: {
        icon: 8,
        bgColor: [255, 221, 0, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    paydayExpGain: 1,
                    seasonPassExpMult: 100,
                }
            }
        ],
        hiddenOnMinimap: true,
    },

    JobsX2: {
        icon: 8,
        bgColor: [255, 221, 0, 0.5],
        hiddenOnMinimap: true,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    emsPaydayMoneyMult: 100,
                    policePaydayMoneyMult: 100,
                    fibPaydayMoneyMult: 100,
                    armyPaydayMoneyMult: 100,
                    govPaydayMoneyMult: 100,
                    newsPaydayMoneyMult: 100,
                }
            }
        ]
    },

    UniversityMed: {
        icon: 1,
        bgColor: [255, 0, 0, 0.5],
        hiddenOnMinimap: true,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    reanimationSpeedMult: 10,
                    poisoningChance: 10,
                    overdoseChanceReduce: 25,
                    extraHealthFromHealthItems: 10,
                    reanimationChanceWithoutDefMult: 10,
                    emsPaydayMoneyMult: 7,
                    greenDurationMult: 7,
                    blueDurationMult: 7,
                    whiteDurationMult: 7,
                    emsAbulanceHealthPerTick: 10,
                }
            }
        ]
    },

    UniversityTeh: {
        icon: 1,
        bgColor: [0, 255, 0, 0.5],
        hiddenOnMinimap: true,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    craftDurationMult: 10,
                    lscStoPriceMult: 4,
                    hijackerSkipMinigameChance: 5,
                    weaponWearAndTearMult: 15,
                    vehicleWearAndTearMult: 15,
                    vehicleRepairDurationMult: 50,
                    vehicleFuelConsumptionMult: 10
                }
            }
        ]
    },

    UniversityCook: {
        icon: 1,
        bgColor: [0, 0, 255, 0.5],
        hiddenOnMinimap: true,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    bbqHighQualityChance: 70,
                    bbqDurationMult: 50,
                    mushroomSellMult: 3,
                    fishSellMult: 3,
                    vegetableSellMult: 3,
                    waterReduce: 7,
                    hungerReduce: 7,
                    extraHungerMult: 15,
                    extraWaterMult: 15,
                    poisoningChance: 10,
                    clubDiscont: 20
                }
            }
        ]
    },

    UniversitySport: {
        icon: 1,
        bgColor: [255, 0, 255, 0.5],
        hiddenOnMinimap: true,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    knockHitChance: 1,
                    staminaGainMult: 20,
                    strengthGainMult: 20,
                    lungCapacityGainMult: 20,
                    inventoryCapacity: 2000,
                    inventoryOverloadCapacity: 2000,
                    melleExtraDamage: 1,
                    armyPaydayMoneyMult: 7,
                    movementSpeedMult: 3,
                    cuffItemsBrokeChance: 0.5,
                    tieItemsBrokeChance: 2.5,
                }
            }
        ]
    },

    UniversityLaw: {
        icon: 1,
        bgColor: [255, 255, 0, 0.5],
        hiddenOnMinimap: true,
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    emsPaydayMoneyMult: 3,
                    policePaydayMoneyMult: 3,
                    fibPaydayMoneyMult: 3,
                    armyPaydayMoneyMult: 3,
                    govPaydayMoneyMult: 8,
                    newsPaydayMoneyMult: 3,
                    dmvFinesReduce: 15,
                    dmvEvacuatePriceReduce: 15,
                    universityPriceReduce: 15,
                    evidenceMoneyMult: 10,
                    robChance: -10,
                    bailPriceMult: 20
                }
            }
        ]
    },

    Lang_EN: {
        icon: 1,
        hiddenOnMinimap: true,
        bgColor: [255, 0, 0, 0.5],
    },

    Lang_ITA: {
        icon: 1,
        hiddenOnMinimap: true,
        bgColor: [255, 0, 0, 0.5],
    },

    Lang_ESP: {
        icon: 1,
        hiddenOnMinimap: true,
        bgColor: [255, 0, 0, 0.5],
    },

    Lang_JAP: {
        icon: 1,
        hiddenOnMinimap: true,
        bgColor: [255, 0, 0, 0.5],
    },

    Lang_RU: {
        icon: 1,
        hiddenOnMinimap: true,
        bgColor: [255, 0, 0, 0.5],
    },

    Knocked: {
        icon: 1,
        bgColor: [255, 0, 0, 0.5],
        effects: [
            { name: 'RagdollEffect' }
        ]
    },

    Contusion: {
        icon: 44,
        bgColor: [67, 201, 153, 0.5],
        dispellOnDeath: true,
        effects: [
            { name: 'ContusionEffect' }
        ]
    },

    CasinoLuckyWheel: {
        icon: 41,
        bgColor: [163, 0, 245, 0.5],
        minStack: 1,
        maxStack: 6,
        stack: 6,
        hiddenOnMinimap: true
    },

    Perception: {
        icon: 42,
        bgColor: [67, 201, 153, 0.5],
        hiddenOnMinimap: false,
        minStack: 1,
        maxStack: 99,
        effects: [
            {
                name: 'ModifyStatsEffectStackable',
                params: {
                    greenDurationMult: 4,
                    blueDurationMult: 4,
                    whiteDurationMult: 4,
                }
            }
        ]
    },

    GhettoWarGovReward: {
        icon: 43,
        bgColor: [74, 116, 53, 0.5],
        hiddenOnMinimap: false,
        minStack: 1,
        maxStack: 99,
        effects: [
            {
                name: 'ModifyStatsEffectStackable',
                params: {
                    policePaydayMoneyMult: 30,
                    fibPaydayMoneyMult: 30,
                }
            }
        ]
    },

    BlockWeapon: {
        icon: 17,
        bgColor: [190, 34, 50, 0.6],
        effects: [
            {
                name: 'BlockWeaponEffectClient'
            }
        ]
    },

    SteelKnuckle: {
        icon: 44,
        bgColor: [67, 201, 153, 0.5],
        effects: [
            {
                name: 'ModifyStatsEffect',
                params: {
                    contusionHitChance: 100
                }
            }
        ]
    },

    Deafness: {
        icon: 40,
        bgColor: [190, 34, 50, 0.5],
        effects: [
            {
                name: 'DeafnessEffect',
                params: {
                    withAnim: false,
                }
            },

        ]
    },

    DeafnessStrong: {
        icon: 40,
        bgColor: [190, 34, 50, 0.5],
        effects: [
            {
                name: 'DeafnessEffect',
                params: {
                    withAnim: true
                }
            }
        ]
    },

    ElectricStunLeftArm: {
        icon: 28,
        bgColor: [190, 34, 50, 0.5],
        effects: [
            {
                name: 'ElectricStunEffect',
                params: {
                    boneFrom: 4089,
                    boneTo: 39317,
                }
            }
        ]
    },

    ElectricStunRightArm: {
        icon: 28,
        bgColor: [190, 34, 50, 0.5],
        effects: [
            {
                name: 'ElectricStunEffect',
                params: {
                    boneFrom: 64016,
                    boneTo: 39317,
                }
            }
        ]
    },

    Instinct: { 
        icon: 45,
        bgColor: [245, 152, 60, 0.5],
    },

    Minigames_snowman: {
        icon: 46,
        bgColor: [55, 117, 236, 0.5],
        effects: [
            {
                name: 'AttachmentEffect',
                params: {
                    attachments: [
                        'snowman'
                    ]
                }
            },
            {
                name: 'ProofsEffect',
                params: {
                    proofs: {
                        fireProof: true,
                        explosionProof: true,
                        collisionProof: true,
                        meleeProof: true,
                        bulletProof: true,
                    }
                }
            },
            {
                name: 'GameControllsEffect',
                params: {
                    controllsToDisable: [
                        21, 22, 23, 24, 25, 26, 30, 31, 32, 33, 34, 35, 44, 140, 141
                    ]
                }
            },
            {
                name: 'PlayerAlphaEffect',
                params: {
                    alpha: 0,
                }
            }
        ]
    }
}
