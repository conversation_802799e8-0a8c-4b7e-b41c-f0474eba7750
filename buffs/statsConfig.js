export const StatsDefaultMinMax = Object.freeze({
    min: 0,
    max: 100,
});

export const PlayerStatsBase = Object.freeze({
    someStat: {
        min: 0,
        max: 100000,
        default: 1000
    },
    //
    // Дефолт значения статов для игрока
    //
    // Стамина
    stamina: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable',
        category: 'character',
        displayF2: true,
    },

    // Объем легких
    lung_capacity: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable',
        category: 'character',
        displayF2: true,
    },

    // Сила
    strength: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable',
        category: 'character',
        displayF2: true,
    },

    // Скрытность
    stealth_ability: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable',
        category: 'character',
        displayF2: true,
    },

    // Управление воздушными ТС
    flying_ability: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable',
        category: 'character',
        displayF2: true,
    },

    // Управление ТС
    wheelie_ability: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable',
        category: 'character',
        displayF2: true,
    },

    // Стрельба
    shooting_ability: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable',
        category: 'character',
        displayF2: true,
    },

    // Строитель
    builder: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: false,
    },

    // Мясник
    butcher: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: false,
    },

    // Закладчик банд
    gangjob: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Инкасатор
    moneycollector: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Винодел
    vineyard: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
    },

    // Лесоруб
    lumberjack: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Сборщик мусора
    garbagecollector: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Почтальон
    gopostal: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Водитель автобуса
    busdriver: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Рыбак
    fishing: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Дальнобой
    trucker: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Фермер
    farmer: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Грибник
    mushroomer: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Шахтер
    mining: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Карьерщик
    quarryman: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Бургер мейкер
    burger: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
    },

    // Механик
    mechanic: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: false,
    },

    // Угонщик
    hijacker: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Угонщик
    orgcrime: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Закладчик семьи
    orgjob: {
        min: 0,
        max: 100000,
        default: 0,
        units: 'countable',
        category: 'professions',
        displayF2: true,
    },

    // Восстановление здоровья в тик здоровья
    healthRegeneration: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable'
    },

    // Не подключено
    maxHealth: 100,
    maxHunger: 100,
    maxWater: 100,

    // Множитель зп от пейдея (Устаревший)
    payDayMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Множитель опыта работ
    jobExpMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Множитель опыта получения скиллов (Устаревший)
    skillGainMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Множитель скорости бега
    movementSpeedMult: {
        min: 10,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения стамины
    staminaGainMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения силы
    strengthGainMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения объема легких
    lungCapacityGainMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта фермера
    farmerGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта рыбака
    fishingGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта мусорщика
    garbagecollectorGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта почтальона
    gopostalGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта инкасатора
    moneycollectorGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта дальнобоя
    truckerGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта лесоруба
    lumberjackGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта грибника
    mushroomerGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта карьерщика
    quarrymanGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта водителя автобуса
    busdriverGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта шахтера
    miningGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта закладчика?
    orgcrimeGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта закладчика?
    orgjobGainMult: {
        min: 0,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта навыка водителя
    driverGainMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Множитель получнения опыта навыка пилота
    flyGainMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта навыка стрельба
    shootGainMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Множитель получения опыта навыка стелса
    stealthGainMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Заразность игрока(Работает через болезни)
    infectiosRate: {
        min: 0,
        max: 100,
        default: 0,
        units: 'percentages'
    },

    // Множитель налогов
    taxesMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Шанс получения x2 на карьерщике
    quarrymanX2Chance: {
        min: 0,
        max: 250,
        default: 0,
        units: 'percentages'
    },

    // Шанс получения x2 на лесорубе
    lumberjackX2Chance: {
        min: 0,
        max: 250,
        default: 0,
        units: 'percentages'
    },

    // Шанс получения x2 на охотнике
    hunterX2Chance: {
        min: 0,
        max: 250,
        default: 0,
        units: 'percentages'
    },

    // Шанс получения x2 на грибнике
    mushroomerX2Chance: {
        min: 0,
        max: 250,
        default: 0,
        units: 'percentages'
    },

    // Шанс получения x2 на фермере
    farmerX2Chance: {
        min: 0,
        max: 250,
        default: 0,
        units: 'percentages'
    },

    // Не подключено
    contractsMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    gangjobMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    builderMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    burgerMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    busdriverMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    butcherMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    farmerMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    floorwasherMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    garbageCollectorMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    gopostalMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages',
    },

    // Не подключено
    hijackerMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    miningMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages',
    },

    // Не подключено
    moneycollectorMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    truckerMoneyMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Множитель макс суммы денег которую можно передать через /pay (Не подключено)
    payAmountMult: {
        min: 0,
        max: 250,
        default: 100,
        units: 'percentages'
    },

    // Кол-во хп которое выдается игроку при попадании в больку
    ambulanceHealth: {
        min: 10,
        max: 100,
        default: 10,
        units: 'countable',
    },

    // Время ожидания респавна в секундах (Не подключено)
    waitingForRespawnTime: {
        min: 30,
        max: 180,
        default: 180,
        units: 'countable',
    },

    // Кол-во дней доступоное для пролонгации бизнеса (Не подключено)
    buisnessProlongationDays: {
        min: 7,
        max: 28,
        default: 14,
        units: 'countable',
    },

    // Кол-во дней доступоное для пролонгации дома (Не подключено)
    homeProlongationDays: {
        min: 7,
        max: 28,
        default: 14,
        units: 'countable',
    },

    // Множитель шанса успешного крафта (Не подключено)
    craftChanceMult: {
        min: -1000,
        max: 1000,
        default: 100,
        units: 'percentages'
    },

    // Множитель максимально возможного кол-ва наличных которые можно снять с атм (Не подключено)
    atmMaxCashOutMult: {
        min: -1000,
        max: 1000,
        default: 100,
        units: 'percentages'
    },

    // Кол-во коинов за 5 часов игры (Не подключено)
    dailyCoins: {
        min: 0,
        max: 2000,
        default: 200,
        units: 'countable',
    },

    // Множитель уменьшения жажды в тик
    waterReduce: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable',
    },

    // Множитель уменьшения голода в тик
    hungerReduce: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable',
    },

    // Множитель времени грин
    greenDurationMult: {
        min: 0,
        max: 1000,
        default: 100,
        units: 'percentages'
    },

    // Множитель времени блю
    blueDurationMult: {
        min: 0,
        max: 1000,
        default: 100,
        units: 'percentages'
    },

    // Множитель времени вайт
    whiteDurationMult: {
        min: 0,
        max: 1000,
        default: 100,
        units: 'percentages'
    },

    // Точка когда наступает перегруз
    inventoryOverloadCapacity: {
        min: 0,
        max: 40_000,
        default: 30_000,
        units: 'countable'
    },

    // Вместмость инвентаря
    inventoryCapacity: {
        min: 0,
        max: 100_000,
        default: 40_000,
        units: 'countable',
    },

    // Не подключено
    casinoChipsToMoney: {
        min: 1,
        max: 500,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    demorganTimeMult: {
        min: 0,
        max: 1000,
        default: 100,
        units: 'percentages'
    },

    // Не подключено
    extraCarSlots: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable'
    },

    // Не подключено
    waterAndHungerMultiplier: {
        min: 0,
        max: 100,
        default: 0,
        units: 'countable'
    },

    // Множитель опыта сезонного пропуска
    seasonPassExpMult: {
        min: 100,
        max: 200,
        default: 100,
        units: 'percentages'
    },

    // Кол-во опыта перса в тик
    paydayExpGain: {
        min: 1,
        max: 2,
        default: 1,
        units: 'countable'
    },

    // Работы

    // Множитель зп в ЕМС
    emsPaydayMoneyMult: {
        min: 0,
        max: 300,
        default: 0,
        units: 'percentages'
    },

    // Множитель зп в Полиции
    policePaydayMoneyMult: {
        min: 0,
        max: 500,
        default: 0,
        units: 'percentages'
    },

    // Множитель зп в ФИБ
    fibPaydayMoneyMult: {
        min: 0,
        max: 500,
        default: 0,
        units: 'percentages'
    },

    // Множитель зп в Армии
    armyPaydayMoneyMult: {
        min: 0,
        max: 300,
        default: 0,
        units: 'percentages'
    },

    // Множитель зп в ГОВ
    govPaydayMoneyMult: {
        min: 0,
        max: 300,
        default: 0,
        units: 'percentages'
    },

    // Множитель зп в Визлах
    newsPaydayMoneyMult: {
        min: 0,
        max: 300,
        default: 0,
        units: 'percentages'
    },

    // Множитель скорости реанимации
    reanimationSpeedMult: {
        min: 0,
        max: 10,
        default: 0,
        units: 'percentages'
    },

    // Снижение шанса отравления
    poisoningChance: {
        min: 0,
        max: 10,
        default: 0,
        units: 'percentages'
    },

    // Эффективность предметов восстанавливающих здоровье
    extraHealthFromHealthItems: {
        min: 0,
        max: 10,
        default: 0,
        units: 'percentages'
    },

    // Шанс реанимировать без дефибриллятора
    reanimationChanceWithoutDefMult: {
        min: 0,
        max: 10,
        default: 0,
        units: 'percentages'
    },

    // Лечение в EMS за тик
    emsAbulanceHealthPerTick: {
        min: 10,
        max: 30,
        default: 20,
        units: 'countable'
    },

    // Множитель времени крафта
    craftDurationMult: {
        min: 0,
        max: 10,
        default: 0,
        units: 'percentages'
    },

    // Множитель стоимости услуги СТО
    lscStoPriceMult: {
        min: 0,
        max: 4,
        default: 0,
        units: 'percentages'
    },

    // Множитель времени ремонта машины ремкомлпектом
    vehicleRepairDurationMult: {
        min: 0,
        max: 50,
        default: 0,
        units: 'percentages'
    },

    // Шанс скипа миниигры угонщика по взлому авто
    hijackerSkipMinigameChance: {
        min: 0,
        max: 5,
        default: 0,
        units: 'percentages'
    },

    // Множитель износа оружия
    weaponWearAndTearMult: {
        min: 0,
        max: 15,
        default: 0,
        units: 'percentages'
    },

    // Множитель износа транспорта
    vehicleWearAndTearMult: {
        min: 0,
        max: 15,
        default: 0,
        units: 'percentages'
    },

    // Множитель потребления топлива транспортом
    vehicleFuelConsumptionMult: {
        min: 0,
        max: 10,
        default: 0,
        units: 'percentages'
    },

    // Множитель времени готовки на BBQ
    bbqDurationMult: {
        min: 0,
        max: 50,
        default: 0,
        units: 'percentages'
    },

    // Шанс на изготовление предмета высокого класса на BBQ
    bbqHighQualityChance: {
        min: 0,
        max: 70,
        default: 0,
        units: 'percentages'
    },

    // Дополнительные очки голода при использовании предметов
    extraHungerMult: {
        min: 0,
        max: 10,
        default: 0,
        units: 'percentages'
    },

    // Дополнительные очки жажды при использовании предметов
    extraWaterMult: {
        min: 0,
        max: 10,
        default: 0,
        units: 'percentages'
    },

    // Шанс нокаута с удара рукой
    knockHitChance: {
        min: 0,
        max: 5,
        default: 0,
        units: 'percentages'
    },

    // Доп урон в ближнем бою
    melleExtraDamage: {
        min: 0,
        max: 5,
        default: 0,
        units: 'countable'
    },

    // Шанс того что при каффе наручники сломаются
    cuffItemsBrokeChance: {
        min: 0,
        max: 5,
        default: 0,
        units: 'percentages'
    },

    // Шанс того что при каффе стяжки сломаются
    tieItemsBrokeChance: {
        min: 0,
        max: 5,
        default: 0,
        units: 'percentages'
    },

    // Снижение стоимости автоштрафов
    dmvFinesReduce: {
        min: 0,
        max: 15,
        default: 0,
        units: 'percentages'
    },

    // Снижение стоимости выкупа авто со штрафстоянки
    dmvEvacuatePriceReduce: {
        min: 0,
        max: 15,
        default: 0,
        units: 'percentages'
    },

    // Снижение стоимости изучения факультетов университета
    universityPriceReduce: {
        min: 0,
        max: 25,
        default: 0,
        units: 'percentages'
    },

    // Доп прибыль при продаже грибов на рынке
    mushroomSellMult: {
        min: 0,
        max: 3,
        default: 0,
        units: 'percentages'
    },

    // Доп прибыль при продаже рыбы на рынке
    fishSellMult: {
        min: 0,
        max: 3,
        default: 0,
        units: 'percentages'
    },

    // Доп прибыль при продаже овощей на рынке
    vegetableSellMult: {
        min: 0,
        max: 3,
        default: 0,
        units: 'percentages'
    },

    // Шанс на ограбление
    robChance: {
        min: 5,
        max: 90,
        default: 20,
        units: 'percentages'
    },

    // Дополнительные деньги при сдаче улик
    evidenceMoneyMult: {
        min: 0,
        max: 20,
        default: 0,
        units: 'percentages'
    },

    // Снижение стоимости выхода под залог
    bailPriceMult: {
        min: 0,
        max: 20,
        default: 0,
        units: 'percentages'
    },

    // Шанс не получить передозировку
    overdoseChanceReduce: {
        min: 0,
        max: 25,
        default: 0,
        units: 'percentages'
    },

    // Скидка во всех закусочных
    clubDiscont: {
        min: 0,
        max: 30,
        default: 0,
        units: 'percentages'
    },

    buffDurationMult: {
        min: 0,
        max: 25,
        default: 0,
        units: 'percentages'
    },

    // Скрытие личности
    hiddenIdentity: {
        min: 0,
        max: 1,
        default: 0,
        units: 'countable'
    },

    // Шанс контузии удара стальным кастетом
    contusionHitChance: {
        min: 0,
        max: 100,
        default: 0,
        units: 'percentages'
    },

    // Иммунитет к перевесу
    overloadImmune: {
        min: 0,
        max: 1,
        default: 0,
        units: 'countable'
    },
});
