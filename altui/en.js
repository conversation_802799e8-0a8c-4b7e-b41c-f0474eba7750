export default {
    'altui': {
        'header': { 'close': 'Exit the game' },
        'nav': {
            'disconnect': 'Disconnect',
            'continue-game': 'Continue playing',
            'start-game': 'Start the game',
            'server-list': 'Server List',
            'manual-connection': 'Manual connection',
            'settings': 'Settings',
            'exit': 'Exit the game'
        },
        'home': {
            'error': 'Error loading the list of servers',
            'recommend-for-newbies': 'Advice for beginners',
            'all-servers': 'All Servers',
            'in-another-langs': 'In other languages',
            'play': 'Play',
            'advantages': { 'experience': 'X{multiplier} Experience' }
        },
        'settings': {
            'main': 'General',
            'language': 'Language',
            'ui-volume': 'Interface Volume',
            'dl-speed-limit': 'Upload speed limit',
            'kbps': 'Kbps',
            'show-discord-activity': 'Show Discord activity',
            'show-netgraph': 'Show FPS and Netgraph',
            'voice-chat': 'Voice Chat',
            'input-device': 'Input device',
            'mic-volume': 'Microphone volume',
            'volume-test': 'Volume check',
            'test-mic': 'Check the microphone',
            'test-mic-stop': 'Stop',
            'chat-volume': 'Chat volume',
            'voice-normalization': 'Voice normalization',
            'noise-cancellation': 'Noise reduction'
        },
        'modal': {
            'exit': {
                'title': 'Are you sure you want to quit?',
                'text': 'We look forward to your return!\nAll your progress has been saved.',
                'quit-to-desktop': 'Exit to desktop'
            },
            'not-supported-platform': {
                'title': 'It\'s still early',
                'text': 'The selected server is not yet available on the current platform',
                'okay': 'Good'
            },
            'game-restart': {
                'title': 'Game restart',
                'text': 'Logging in to the selected server will restart your game.\nAre you sure you want to continue?',
                'restart': 'Restart'
            },
            'manual-connect': {
                'title': 'Manual connection',
                'ip': 'Server address',
                'password': 'Password (if available)',
                'connect': 'Connect',
                'format': 'The server address must be in the format <mark>address:port</mark><br>For example: <mark>*********.1:7788</mark> or <mark>example.server.com:443</mark>'
            }
        },
        'shared': { 'go-back': 'Back' },
        'connection': {
            'connecting-to': 'Connecting to <span>{serverName}</span>',
            'connection-failed': 'Connection error',
            'disconnected': 'The connection is broken',
            'downloading-content': 'Uploading content',
            'validating-content': 'Content verification',
            'downloading-content-update': 'Uploading content updates',
            'validating-content-update': 'Checking for content updates',
            'try-again': 'Try again',
            'cancel': 'Cancellation',
            'x-of-y': '{x} of {y}'
        }
    }
};