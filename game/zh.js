export default {
    system: {
        text: '{text}',
        onlyServer5: '此道具僅在伺服器5上可用！',
        currentTime: '當前時間為 {time}！',
        errorOccured: '發生錯誤',
        errorOccured2: '發生錯誤 $t({error})',
        notEnoughMoney: '你沒有足夠的錢！',
        notEnoughCoins: '你沒有足夠的MC ！',
        wrongPrice: '價格不正確！',
        wrongAmount: '金額不正確！',
        wrongDate: '無效日期！',
        wrongValue: '值不正確！',
        countError: '數量無效！',
        hour: '{count, select, one{{count} час} few{{count} часа} many{{count} часа} other{{count} час}}!',
        notNow: '你現在不能這樣做！',
        spaceOccupied: '這個座位已經就位了！',
        wrongEmail: '無效的郵箱！',
        emailUsed: '此郵箱已被使用！',
        emailChanged: '您已成功更改您的郵箱！',
        loginUsed: '此用戶名已被使用！',
        passwordDifferent: '您輸入的密碼與目前的密碼不符',
        passwordChangeSuccess: '您已成功更改密碼！',
        notTooFast: '不常見！',
        inputReason: '錯誤！請提供原因！',
        tooBigValue: '太大了！',
        processingError: '處理您的申請時發生錯誤！',
        cantInCar: '你不能坐在交通工具上！',
        sumRange: '金額必須介於 ${from} 和 ${to}之間',
        canBeRange: '可能從 ${from} 到 ${to}！',
        youGot: '你得到了 ${amount}',
        ok: '好啊，好啊',
        cancel: '取消',
        yes: '是的，我是。',
        no: '不，我不知道。',
        restartIn: '重新開始於',
        recorderTitle: 'Rockstar Editor',
        recorderSaved: '剪輯已儲存到Rockstar編輯器',
        pauseMenuCash: '銀行 ${bank} 現金 ${cash}',
        cashText: '${amount} 現金',
        mcoinsText: '{amount} MCoins',
        vipText: '{amount} 天{title} VIP',
        x2Days: '今天有機會以x2促銷購買偉大硬幣（ MC ） ！',
        welcome: '歡迎來到偉大的角色扮演， {name}！',
        cantTooFast: '不可能太多次了！',
        highFPS: '您的FPS高於 {limitFPS}， FPS下降和微量日志是可能的。',
        highFPS2: '我們建議您將其限制為 {recommendedFPS} FPS。',
        colors: {
            black: '黑色',
            grey: '灰色',
            darkGreen: '深綠色',
            blue: '藍色',
            brown: '棕色',
            cream: '奶油味',
            white: '白色',
            silverBuromaline: '血清嘌呤鹼',
            red: '紅色'
        },
        none: '沒什麼。'
    },
    skills: {
        builder: '生成器',
        busdriver: '巴士司機',
        flying_ability: '航班',
        garbagecollector: '拾荒者',
        moneycollector: '收款人',
        butcher: '屠夫',
        lumberjack: '伐木工',
        lung_capacity: '呼吸',
        shooting_ability: '射擊',
        stamina: '耐力',
        stealth_ability: '隱祕性',
        strength: '強度',
        trucker: 'Trucker',
        fishing: '漁夫',
        wheelie_ability: '駕駛'
    },
    vehicleTuning: {
        extra: '額外的',
        spoiler: '搗亂者',
        frontBumper: '前保險槓',
        rearBumper: '後保險槓',
        sills: '閾值',
        exhaust: '消音器',
        frame: '框架(F)',
        radiator: '散熱器',
        hood: '兜帽',
        fender: '翅膀',
        rightFender: '右翼',
        roof: '屋頂',
        windows: '窗口',
        neon: '霓虹',
        engine: '馬達',
        brakes: '剎車',
        transmission: '歯輪箱',
        horn: '喇叭',
        suspension: '暫停',
        armor: '護甲',
        turbocharger: 'Turbocharging',
        utilShadowSilver: '利用影子銀',
        speaker: '喇叭',
        trunk: '主幹',
        hydraulics: '水力學',
        engineBlock: '引擎座',
        airFilter: '空氣過濾器',
        supports: '支持',
        archCovering: '拱形塗料',
        fogLights: '霧燈',
        roofVent: '屋頂風管',
        tank: '坦克',
        livery: '肝',
        numberPlate: '牌照',
        xenon: 'Xenon',
        wheels: '輪子',
        rearWheels: '後輪',
        numberPlateHolder: '號碼持有人',
        numberPlateColor: '數字顏色',
        finishingDesign: '完成設計',
        ornaments: '裝飾品',
        dashboard: '控制面板',
        dialColor: '轉盤顏色',
        dial: 'Dial',
        doorSpeaker: '門喇叭',
        rudder: '方向舵',
        transmissionLever: '齒輪箱杆',
        plaques: '斑塊',
        tints: '已着色',
        reducedGripTires: '減少黏附輪胎',
        driftReductionSuspension: '漂移降懸吊',
        primaryColor: '主色',
        additionalColor: '次要顏色',
        discColor: '光盤顏色',
        pearlColor: '珍珠色',
        repair: '修復',
        plateHolders: '板架',
        trim: '完成',
        decorations: '珠寶',
        duct: '通風管道',
        boost: '加速'
    },
    vehicle: {
        notFound: '找不到車輛！',
        noAccess: '您無法使用車輛！',
        noKeys: '你沒有鑰匙！',
        yourVehicleIsRent: '你的車輛已出租至 {date}！',
        farAway: '交通工具太遠了！',
        engineBroken: '引擎壞了！',
        doorLockBroken: '門鎖壞了！',
        noBattery: '電池沒電了！',
        notYours: '這不是你的私人車！',
        noFuel: '沒有燃料！',
        onYourCar: '你應該在自己的車裡！',
        cantDoWithYours: '你不能這樣做與你的車！',
        shouldntBeOnCarMarket: '汽車不應該在汽車市場賣！',
        doorsOpened: '門 {title} - ~g~開啟',
        doorsClosed: '門 {title} - ~r~已關閉',
        trunkOpened: '中繼 {title} - ~g~打開',
        trunkClosed: 'Багажник {title} - ~r~закрыт',
        trunkAlreadyOpened: '後備箱已打開！',
        engineOn: '引擎 ~g~已啓動',
        engineOff: '引擎 ~r~已停止',
        parkedGarage: '你把車停在車庫裡了！',
        parkedHere: '你把車停在這個地方！',
        ejectPlayer: '你把 {player}扔出車外',
        ejectedByPlayer: '{player} 把你扔下車',
        somebodyInside: '有人坐在車裡！',
        cantTuning: '有人坐在車裡！',
        cantSellOnAuction: '無法出售已拍賣的車輛！',
        cantParkInGreenZone: '你不能在綠地區停車！',
        cantSpawnThisModel: '你不能讓這個交通工具變成蜂巢！',
        cantSpawnAccess: '你的排名無法使用此運輸工具！',
        cantSpawnError: '您無法使用此交通工具！',
        noVehiclesNear: '附近沒有車輛！',
        locked: '交通關閉！',
        noSpace: '車裡沒有空間！',
        closedNoAccess: '交通已關閉，您無法進入！',
        notPrivateVehicle: '這不是你的私人車輛！',
        alreadyStarted: '傳輸已經開始！',
        yourNotDriver: '你不是開車的司機！',
        drivingFast: '小心點，騎在碰撞上會帶來監獄的',
        drivingFastTitle: '接近的通道',
        seatbeltOff: '安全帶 ~r~已移除',
        seatbeltOn: '安全帶 ~g~緊固',
        carMarket: {
            notInZone: '您不在汽車市場區域內！',
            noFreeSpace: '沒有可供銷售車輛的地方！'
        },
        fixcar: {
            notEnoughMoney: '你沒有足夠的錢！生成成本： ${amount}',
            notParked: '這輛車不在停車場。你可以在手機上的AutoPlus應用程式中找到它！',
            noHouseAndApartments: '您沒有房子和公寓可以讓其他車輛進入！',
            noSpaceInGarage: '你的車庫裡沒有足夠的空間來生產這輛車！',
            spawnSuccess: '你已成功從服務中取回你的交通工具 {title} ！'
        },
        dump: {
            title: '垃圾填埋',
            wantTrashIt: '你想把交通工具帶到垃圾堆嗎？你將獲得 ${amount}',
            cantDumpRent: '你不能填埋租用的車輛！',
            cantDumpForeign: '你不能把別人的交通工具放在垃圾堆上！',
            dumpSuccess: '您已成功將車送到垃圾堆！'
        },
        tuning: {
            availableDriverOnly: '只有司機可以使用優化功能！',
            cantTuneWhenOnAuction: '無法調節供拍賣的車輛！',
            slotOccupied: '這個座位已經就位了！',
            cantSetThisModType: '此調節無法安裝在此車輛上！',
            disabledTuning: '此調節無法購買！',
            disabledTuningForOriginalModel: '您的模型有一種特殊的調諧。無法安裝降序調諧！'
        },
        repairStation: {
            availableDriverOnly: '只有司機才能修復！',
            alreadyNew: '這車有這個新零件！',
            repairSuccess: '您已成功修復此部件！',
            failRepair: '您未能替換 $t({item})！',
            dontHaveItems: '你沒有 $t({item}) 和 $t({item2})！',
            noNeedToRepair: '這個運輸無需修復！',
            slotOccupied: '這個座位已經就位了！',
            battery: '累加器',
            oil: '石油'
        },
        rentCar: {
            reachLimit: '您租用車輛不能超過3輛！',
            rentSuccess: '您已成功租用 {title} 乘 {time}。您可以在手機上的應用程序中使用交通工具。',
            cooldown: '你目前有車輛租賃的冷卻時間，請稍後再試'
        },
        drone: {
            noDrone: '你沒有無人機！',
            damagedRecently: '你最近受到了傷害！你現在不能使用無人機',
            notTooFast: '你不能經常運行無人機！',
            broken: '你的無人機壞了！',
            onlyGov: '無人機只能由功率結構控制！'
        },
        autopilot: {
            noWaypoint: '若要使自動飛行器正常工作，請在地圖上指定目的地！',
            arrived: '您已到達目的地！',
            started: '自動駕駛已啟動！',
            stopped: '自動飛行器停止！'
        }
    },
    user: {
        notMarried: '不，我不知道。',
        ucpSettingsPassword: '密碼',
        periodOnline: '{hours}hr {minutes}min',
        admin: '管理',
        leader: '領導者',
        player: '玩家',
        noRegistration: '沒有注冊',
        offline: '玩家已下線！',
        youFarAway: '你太遠了！',
        farAway: '玩家太遠了！',
        notHere: '附近沒有玩家！',
        notInCar: '玩家不在車裡！',
        youNotInVehicle: '你不是在交通工具！',
        notWhenInCar: '你不能有一個玩家在車裡！',
        notWhenInTrunk: '你不能讓玩家躺在後車廂裡！',
        inCar: '玩家在車裡！',
        cantPushInTrunk: '你不能把它放在這輛車裡！',
        cantDoWithYourself: '你不能這樣對待自己！',
        playerNotFound: '未找到播放器！',
        playerFarAway: '玩家在很遠的地方！',
        noLicense: '您沒有 {title}類許可證',
        needMinimumXP: '你至少需要 {amount} 個經驗。你有 {currentSkills}個',
        errorLoading: '載入帳戶時發生錯誤！請重新連接到伺服器',
        alreadyAuthed: '此角色已在遊戲中！無法登入！',
        alreadyInTrunk: '玩家已經在後車廂裡！',
        cashbackRecieved: 'За прошлый месяц вам начислено ${amount} кэшбэка!',
        noMoneyForBankTariff: '您的帳戶中沒有足夠的資金來支付銀行稅費。您的帳戶已被封鎖！',
        bankTariffPayment: '收取銀行關稅 {tariff} ，金額為 ${amount}',
        banned: '您被屏蔽到 {amount}。原因： {reason}',
        nameUsed: '此名稱已被其他玩家使用！',
        longName: '名字和姓氏大於 {amount} 字符！',
        skillIncreased: '技能 {skill}提升',
        exitRecently: '你最近離開了遊戲，你將可以在這個地方出現 {amount}。或者選擇生產的最後一個位置，馬上進入',
        notMember: '你不是組織成員！',
        muted: '你有一只兔子 {amount} 分鐘！',
        dead: '你昏迷了！',
        gaged: '你在堵嘴！',
        playerNotFoundById: '此ID的玩家不在遊戲中！',
        inMovement: '玩家正在出發！',
        escortingSomeone: '你已經領導了某人！',
        notTied: '玩家沒有連結！',
        mustBeCuffed: '玩家必須被捆緊/戴上手銬！',
        mustBeInVehicle: '你應該在車裡！',
        notInTrunk: '玩家不在後備箱裏！',
        promoActivated: 'Код {promocode} от {isMedia, select, 1{партнёра} other{игрока}} принят! Вы получите {isMedia, select, 1{20 000$ и Platinum VIP на 7 дней} other{10 000$ и Platinum VIP на 3 дня}} при достижении 3 уровня!',
        promoReg: '使用優惠碼 {promocode}註冊！',
        bonusEntered: '您已成功輸入獎勵代碼 {bonuscode}。接收： {comment}',
        noWeaponLic: '你沒有槍支執照！',
        canChangeEmailIn: '可以在 {amount} 秒後再次更改郵箱！',
        canChangePasswordIn: '您將可以在 {amount} 秒內再次變更密碼！',
        passwordDontMatch: '密碼不符！',
        offerNotActive: '此優惠已失效！',
        doesntHaveEnoughMoney: '{name} 沒有足夠的錢。',
        hasNoHouse: '玩家沒有房子。',
        hasNoApartment: '玩家沒有公寓。',
        playerDead: '昏迷玩家',
        alreadyInFraction: '玩家已經在陣營中',
        alreadyInFamily: '玩家已加入家族',
        needCreateFamilyRank: '在您的家庭設定中建立額外排名，接待房客！',
        someoneAuthedUnderThisAccount: '其他人已在此用戶下登錄！',
        wrongLoginOrPassword: '用戶名或密碼無效！剩餘次數： {amount}次',
        wrongEmailRetry: '無效郵件！剩餘次數： {amount}',
        wrongVerificationCode: '驗證碼無效！剩餘嘗試次數： {amount}次',
        removeActiveItemFirst: '首先，從你的手中移除活躍的餐點！',
        socialRegistered: '此社交俱樂部已在伺服器上註冊！登入。',
        voiceChatReloaded: '部隊已成功重新上載。約10秒後，遊戲將重新開始！',
        cantLeaveTrunkTied: '你不能離開後備箱因爲你被捆起來了！',
        cantInCuffs: '你不能戴手銬！',
        cantWhileCarryng: '你不能把人抱在手上！',
        cantInteractNow: '你現在無法互動！',
        techWorksNoAuth: '已啟用維護模式。無法登入！',
        tillDeath: '直到死亡',
        rescuingPlayer: '提升玩家',
        dialog: {
            noPropertyForTrade: '你沒有財產可以兌換！',
            playerNoPropertyForTrade: '玩家沒有可以兌換的財產！',
            tradeNotAccepted: '{name} 未接受兌換優惠',
            lookedAtLic: '{name} 檢視您的授權',
            denyLookAtLic: '{name} отказался смотреть ваши лицензии',
            lookedAtId: '{name} просмотрел ваше удостоверение',
            denyLookAtId: '{name} 人拒絕查看您的身分證件',
            lookedAtDocs: '{name} 次查看您的文件',
            denyLookAtDocs: '{name} 拒絕查看您的文件',
            jailTimeRange: '逮捕時間從 {from} 到 {to} 分鐘！',
            handShakeWithYou: '{name} 向您打招呼',
            youHandShakeWith: '你打招呼 {name}',
            denyHandShakeWithYou: '{name} 拒絕與你打招呼',
            denyYouHandShakeWith: 'Вы отказались с {name} здороваться',
            starsRange: '從 {from} 到 {to}的星星！',
            denierHealing: '{name} отказался от лечения',
            noHealingPills: '你沒有任何可待因丸！',
            noSpaceForKey: '您在鑰匙庫中沒有空間！',
            noSpaceForLic: '您的文件清單中沒有空格！',
            playerNoKeyBlank: '玩家沒有空白鍵！',
            cantGiveMoreKeysThanHouseRooms: '發佈的住宿數量不能超過住宿數量！',
            playerTookKeysFromWarehouse: '{name} 拿走了你家庫房的鑰匙。',
            tookKeysFromWarehouse: '你拿走了{house}號房子的鑰匙',
            denyPlayerTakeKeysFromWarehouse: '{name} 拒絕拿走你家儲藏室的鑰匙。',
            youDenyTakeKeysFromWarehouse: '你拒絕把鑰匙帶到家裡的庫房。',
            certificatesOnlyInHospital: '您只能在醫院簽發證書！',
            physicalCertificateGivenRecently: '身體健康證書已經發出不到7天了！',
            gavePhysicalCertificate: '{name} 給你 ${amount}的醫療健康證明',
            youGavePhysicalCertificate: '您已發出 {name} 張身體健康證書。已獲得 ${amount}',
            youDeniedPhysicalCertificate: '您已選擇退出身體健康證書',
            playerDeniedPhysicalCertificate: '{name} 人拒絕獲得身心健康證書',
            mentalCertificateGivenRecently: '精神健康證書已經發出不到7天了！',
            gaveMentalCertificate: '{name} 已給予您 ${amount}個心理健康證書',
            youGaveMentalCertificate: '您已發出 {name} 張精神健康證書。已獲得 ${amount}',
            youDeniedMentalCertificate: '你已選擇退出精神健康證書',
            playerDeniedMentalCertificate: '{name} 拒絕獲得心理健康證書',
            playerLookedAtMedicalCertificate: '{name} 檢查你的醫療證書',
            playerDeniedLookAtMedicalCertificate: '{name} 人拒絕看你的醫療證書',
            youAlreadyHaveHouse: '你已經有房子了。',
            alreadyRentingHouse: '您已出租其他房源或公寓。',
            farAwayFromHouse: '你離家太遠了！',
            farAwayFromApartment: '你離公寓太遠了！',
            rentRoomEnded: '入住地點已被佔用。',
            playerMovedToYourHouse: '{name} 已成功入住您的房源。',
            youMovedToHouse: '您已成功入住{house}號房。',
            youDeniedMoveToHouse: '你拒絕入住。',
            playerDeniedMoveToYourHouse: '{name} 人拒絕入住您的房源。',
            sentRequestToPayFine: '你答應 {name} 要支付 ${amount}的罰款',
            alreadyHasOwnApartment: '您已經有公寓了！',
            youMovedToApartment: '您已成功入住#{apartment}公寓。',
            playerDeniedMoveToYourApartment: '{name} 拒絕入住您的公寓。',
            youDeniedMoveToApartment: '你拒絕入住公寓。',
            playerMovedToYourApartment: '{name} 已成功入住您的公寓。',
            playerTookKeysFromWarehouseApartment: '{name} 拿走了你公寓食品庫的鑰匙。',
            tookKeysFromWarehouseApartment: '你拿走了{apartment}號公寓倉庫的鑰匙',
            denyPlayerTakeKeysFromWarehouseApartment: '{name} 拒絕拿走您公寓儲藏室的鑰匙。',
            youDenyTakeKeysFromWarehouseApartment: '你拒絕拿走公寓儲藏室的鑰匙。',
            playerKissedWithYou: '{name} {gender, select, 0{поцеловался} 1{поцеловалась}} с вами',
            youKissedWith: '你吻了 {name}',
            youDeniedToKiss: '你拒絕和 {name}接吻',
            playerDeniedToKiss: '{name} {gender, select, 0{отказался} 1{отказалась}} с вами целоваться',
            playerHuggedWithYou: '{name} {gender, select, 0{обнялся} 1{обнялась}} с вами',
            youHuggedWith: '你抱着 {name}',
            youDeniedToHug: '你拒絕與 {name}擁抱',
            playerDeniedToHug: '{name} {gender, select, 0{отказался} 1{отказалась}} с вами обниматься',
            playerFiveWithYou: '{name} {gender, select, 0{дал} 1{дала}} вам пять',
            youFiveWith: '你用 {name}給了五個',
            youDeniedToFive: 'Вы отказались давать пять для {name}',
            playerDeniedToFive: '{name} {gender, select, 0{отказался} 1{отказалась}} дать пять',
            playerSlapWithYou: '{name} {gender, select, 0{шлёпнул} 1{шлёпнула}} вас',
            youSlapWith: '你扇了 {name}巴掌',
            youDeniedToSlap: '你不允許 {name} 扇你',
            playerDeniedToSlap: '{name} {gender, select, 0{отказался} 1{отказалась}} шлепнуть',
            playerTookCarryngs: '{name} {gender, select, 0{взял} 1{взяла}} вас',
            playerAcceptedCarryngs: '{name} {gender, select, 0{принял} 1{приняла}} ваш запрос',
            youDeniedCarryings: 'Вы отказали {name} взять вас на руки',
            playerDeniedCarryings: '{name} не {gender, select, 0{разрешил} 1{разрешила}} взять себя на руки',
            playerAlreadyInJail: '玩家已經在監獄裡！',
            youGaveMoney: '你通過了 {name} ${amount}',
            givenMoneyBy: '{name} 通過你 ${amount}',
            gloveLock: 'Бардачок {title} - {unlocked, select, 1{~g~открыт~w~} other{~r~закрыт~w~}}',
            playerNoMedicalCertOrExpired: '玩家沒有醫療證書或已過期',
            youNoMedicalCertOrExpired: '您沒有醫療證書或證書已過期',
            armyCertificateGivenRecently: '服兵役證書已經發出不到7天了！',
            gaveArmyCertificate: '{name} 已向您發出服兵役證明。',
            youGaveArmyCertificate: '您已發出 {name} 份服兵役證書。',
            playerNoArmyCertOrExpired: '玩家沒有服兵役證',
            youNoArmyCertOrExpired: '您沒有服兵役證明',
            playerLookedAtArmyCertificate: '{name} 人已審核你的軍事證書',
            youDeniedArmyCertificate: '你拒絕獲得服兵役證明',
            playerDeniedArmyCertificate: '{name} 人拒收兵役證',
            playerDeniedLookAtArmyCertificate: '{name} 拒絕查看你的軍事證書',
            tookArmyCertificate: '{name} 已取得服兵役證書。',
            youTookArmyCertificate: '你從 {name} 採取了服兵役證明。',
            alreadyHasArmyCertificate: '玩家已經有服兵役證！',
            hospitalExitTitle: '醫院',
            hospitalExitBody: '你想下牀嗎？',
            hospitalExitBtn1: '從左腿起身',
            hospitalExitBtn2: '從右腳站起來',
            ticketFineTitle: '罰款',
            ticketFineBody: '指定罰款金額',
            ticketFineReasonTitle: '罰款',
            ticketFineReasonBody: '指定罰款的原因',
            ticketFinePayTitle: '罰款',
            ticketFinePayBody: '{name} 表示願意支付罰款 {reason}.價格 ${amount}',
            dicePlayTitle: '玩骰子',
            dicePlayBody: '{name} 表示願意在 ${amount}上玩骰子',
            airportTitle: 'Aeroport',
            airportBody: '你想要飛到島上為 ${amount}',
            taxiTitle: '出租車費用',
            taxiBody: 'Путь {distance}м стоит ${amount}. Хотите оплатить?',
            deathTitle: '危急狀態',
            deathBody: '人物受重傷。身體從最後的力量為生命戰鬥。做個選擇。',
            deathBtn1: '正在等待醫護人員',
            deathBtn2: '失去意識',
            passportTitle: '護照',
            passportBody: '{name} 想向您展示文件。',
            medLicTitle: '醫療證書',
            medLicBody: '{name} 希望向您出示醫療證明。',
            armyLicTitle: '軍事證書',
            armyLicBody: '{name} 想向您展示軍事證書。',
            licTitle: '授權',
            licBody: '{name} 想要顯示授權。',
            greetTitle: '您好！',
            greetBody: '{name} 想跟你打招呼。',
            kissTitle: '親吻',
            kissBody: '{name} 想吻你。',
            hugTitle: '抱抱',
            hugBody: '{name} 想抱你。',
            fiveTitle: '給我五分鐘。',
            fiveBody: '{name} 想給你高五分。',
            slapTitle: '扇我耳光',
            slapBody: '{name} 想扇你耳光',
            carryTitle: '抱在懷裏',
            carryBody: '{name} 想把你抱在懷裏',
            payTitle: '轉帳金額',
            payBody: '輸入美元金額。',
            fractionInviteTitle: '邀請函',
            fractionInviteBody: '是否要加入 {title}派？',
            familyInviteBody: '是否要加入家庭 {title}？',
            pillsTitle: '平板電腦套廉價',
            pillsBody: '{name} 表示願意為你買藥片 ${amount}。',
            medCertPhysicalTitle: '醫療證書',
            medCertPhysicalBody: '{name} 表示願意向您提供 ${amount}的健康證明。',
            medCertMentalTitle: '醫療證書',
            medCertMentalBody: '{name} 表示願意向您提供 ${amount}的心理健康證明。',
            armyCertTitle: '軍事證書',
            armyCertBody: '{name} 表示願意向你發出服兵役證明。',
            arrestTimeTitle: '逮捕',
            arrestTimeBody: '指示逮捕玩家的分鐘數。1到100分鐘！',
            idCardTitle: '身份證明文件',
            idCardBody: '{name} 想向您展示身份證明文件',
            jerryCanTitle: '罐子',
            jerryCanBody: '指定要填充多少升。1到5升！',
            diceInputTitle: '出價金額',
            diceInputBody: '輸入美元金額',
            tradePropertyTitle: '財產交換',
            tradePropertyBody: '{name} 邀請你開始兌換屬性。你同意嗎？',
            tradeItemsTitle: '物件交換',
            tradeItemsBody: '{name} 邀請你開始兌換道具。你同意嗎？',
            houseRentTitle: '清算',
            houseRentBody: '{name} 邀請你和他一起住在{houseId}號房子裡。你同意嗎？',
            apartmentRentTitle: '清算',
            apartmentRentBody: '{name} предлагает вам подселиться к нему в квартиру №{apartmentId}. Вы согласны?',
            warehouseHouseTitle: '食品庫鑰匙',
            warehouseHouseBody: '{name} 為您提供房屋{houseId}號庫房的鑰匙。你想拿走它嗎？',
            warehouseApartmentTitle: '食品庫鑰匙',
            warehouseApartmentBody: '{name} 為您提供{apartmentId}號公寓庫房的鑰匙。你想拿走它嗎？',
            wantedTitle: '宣布BOLO',
            wantedBody: '指定星數(0-5)',
            sellAtmTitle: '出售房地產',
            sellAtmBody: '您確定要以 ${amount} （ 25%傭金）出售ATM #{id} 嗎？',
            sellPropertyTitle: '出售房地產',
            sellPropertyBody: '您確定要以 ${amount} （ 25%傭金）賣出 {title} 嗎？',
            sellApartmentsTitle: '待售公寓',
            sellApartmentsBody: '你確定要以 ${amount} （ 25%的傭金）出售{id} 號公寓嗎？',
            armwrestlingTitle: '手臂摔跤',
            armwrestlingBody: '你確定要下注為 ${amount}的雙臂摔跤嗎？',
            gangCaptureTitle: '捕捉完成',
            gangCaptureBody: '你想留在虛擬世界直到 {time}嗎？',
            yes: '是的，我是。',
            no: '不，我不知道。'
        },
        events: {
            notSavedFromDeath: '如果沒人救你，過一會你就在醫院了！',
            drowned: '你淹死了在水裏。不可能呼叫救援人員！',
            noMedicalCertificate: '沒有醫療證明！',
            noArmyCertificate: '沒有服兵役證明！',
            noKeyBlank: '你沒有交鑰匙工件！',
            cantGiveMoreDuplicatesForVehicle: '每輛車的複製次數不能超過 {amount} 次！',
            vehicleDuplicateKeysSuccess: '您已成功建立傳輸 {title}的重複鑰匙',
            notYourRentCar: '這不是你的租車！',
            rentCancelSuccess: '您已取消租用交通工具 {title}',
            yourCarRentCancelSuccess: '{name} 已取消您的車輛租賃 {title}',
            changeVehicleKey: '您已成功更改傳輸鎖 {title}',
            cantWhileSomeoneInCar: '你不能有你之一在車裡！',
            showDocsToPlayerRequest: '你建議為 {name} 查看你的文件',
            showMedicalDocsToPlayerRequest: 'Вы предложили {name} посмотреть вашу мед. справку',
            showArmyDocsToPlayerRequest: '你答應 {name} 要看你的服兵役證',
            showLicToPlayerRequest: '您提出 {name} 查看您的許可證',
            greetToPlayerRequest: '你答應 {name} 要打招呼',
            kissToPlayerRequest: 'Вы предложили {name} поцеловаться',
            hugToPlayerRequest: 'Вы предложили {name} обняться',
            fiveToPlayerRequest: '你答應 {name} 分5分',
            slapToPlayerRequest: '你答應打 {name} 巴掌',
            carryToPlayerRequest: '你答應把 {name} 放到懷裏',
            showIdCardToPlayerRequest: '你答應 {name} 查看你的身份證明文件',
            cantPickPlayerThisWay: '你不能讓玩家以這種方式昏迷！',
            noScrewDriver: '你沒有螺絲刀！',
            notNearJail: '你不在監獄附近！',
            playerNotInJail: '球員沒有被投入監獄！',
            cantUnjailFromHere: '你不能走出這樣的監獄！',
            cantDoInCar: '你不能在車裡做！',
            cantWhenPlayerInCar: '當玩家在車裡時，你不能！',
            noIdCard: '您沒有身份證明文件！',
            couldntRescuePlayer: '您未能保存玩家！',
            noEpinephrine: '你沒有腎上腺素！',
            playerNotIll: '玩家沒有生病！',
            vaccinatedBy: '{name} вакцинировал вас',
            vaccinatePlayer: 'Вы вакцинировали {name}',
            noMedicines: '你沒有你需要的藥！',
            robbedPlayerRecently: '你最近犯了偷竊罪！嘗試 {date}',
            couldntRobPlayer: '你不能搶劫 {name}',
            robbedByPlayer: '{name} 在 ${amount}上搶劫了你',
            youRobbedPlayer: '你搶了 {name} 乘以 ${amount}',
            youCouldntRobPlayer: '你不能搶劫 {name}',
            triedToRobYou: '{name} 試圖搶劫你！',
            nobodyInTrunk: '沒有人在後備箱裏！',
            breathalyzerData: 'Алкотестер показал {type, select, 1{положительный} other{негативный}} результат',
            cantHijackThisVehicle: '這個運輸工具不能被黑客入侵！',
            hijackedRecently: '你最近闖進了一輛車。你可以黑 {date}',
            hijackSuccess: '您已成功黑客入侵引擎！',
            hijackFail: '你沒有黑引擎！',
            weaponLicGivenBy: '{name} 向你發放武器執照',
            youGaveWeaponLic: 'Вы выдали {name} лицензию на оружие',
            weaponLicTakenBy: '{name} 拿了你的武器執照',
            youTookWeaponLic: '你拿了 {name} 的武器執照',
            fishLicGivenBy: '{name} 已向您發放捕撈執照',
            fishLicTakenBy: '{name} 撤回您的捕魚執照',
            youGaveFishLic: '您已發放 {name} 個釣魚執照',
            youTookFishLic: '您已從 {name} 撤回您的捕魚執照',
            driveDLicTakenBy: '{name} забрал у вас лицензию Drive D',
            youTookDriveDLic: '你從 {name} 取得D駕駛執照',
            driveMLicTakenBy: '{name} забрал у вас лицензию Drive M',
            youTookDriveMLic: '你從 {name} 取得了Drive M執照',
            notYourItem: '這不是你的主題！',
            noDiceToPlay: '您沒有任何骰子可以玩！',
            tradeDisabledTemp: '物業暫時無法兌換！',
            tradePropertySentRequest: '您已提出 {name} 來開始內容兌換。請稍候回覆。',
            tradeItemsSentRequest: '您已提出開始兌換項目為 {name} 。請稍候回覆。',
            cantWhileTrading: '在兌換期間不可能！',
            secondPlayerAlreadyTrading: '第二個玩家已經參與了兌換交易！',
            playerAlreadyHasHouse: '玩家已經有自己的房子了！',
            playerAlreadyHasApartment: '玩家已經有自己的公寓了！',
            playerAlreadyRenting: '玩家已經出租了另一間房子或公寓！',
            tookDonateMaskOff: '{name} 移除您的口罩（在捐贈庫存中）',
            tookMaskOff: '{name} 摘下您的口罩',
            youTookMaskOff: '您已從 {name}中移除蒙版',
            repairVehicleSuccess: '您已成功修復車輛！',
            notInstruments: '您沒有任何工具！',
            lookedAtPass: '{name} 次查看您的護照',
            nextReportIn: '撰寫下一報告將是可能的 {date}',
            youGotReanimated: '你復活了！',
            youReanimatedPlayer: '你復活了 {name}',
            jailedNotByYourFraction: '你的陣營沒有將玩家降落！',
            cantEscortDeath: '你不能帶著一個死人！',
            playerUnjailedYou: '{name} 放你出獄',
            youUnjailedPlayer: '你讓 {name} 從監獄出來。',
            cantDrillLock: '請勿打破這輛車的門',
            drillLockSuccess: '你已成功入侵這輛車的門！'
        },
        noSpaceForLicCard: '您的執照卡庫內沒有空間。你可以稍後在市政廳拿到',
        addedMC: '應計 {amount} MC. {comment}',
        thirsty: '你迫切需要喝水，你正在失去力量！',
        hungry: '你迫切需要吃，你正在失去力量！',
        playerIll: '我覺得你病了，我們建議你去看醫生！',
        playerUnmuted: '你已經被除變了！',
        dailyBonusMC: '你每天贏得 {hours} 小時的獎勵，並獲得 {amount} 枚偉大的金幣！',
        dailyBonusRoulette: '你每天贏得 {hours} 小時，並有機會自由捲動！ （ F2 - Donat - Roulette ） ！',
        healedSuccess: '您已成功完成治療過程。你可以離開醫院了！',
        moneyRecievePromo: '使用優惠碼 {inviteCode}註冊可獲得 ${amount} 點獎勵！',
        vipRenewed: '您的VIP白金帳戶已延長 {amount} 天，可使用優惠碼 {inviteCode}進行註冊！',
        vipRenewed2: '使用優惠碼 {inviteCode}註冊，您的VIP帳戶（已經有另一個帳戶）已被延長 {amount} 天！',
        vipGiven: '白金帳戶已發放 {amount} 天，以優惠碼 {inviteCode}登記！',
        giveMoneyForInvite: 'Зачислено ${amount} за игрока {name}, достигшего {level} уровня!',
        notPayDayAFK: '由於你曾參加AFC ，你將無法獲得報酬！',
        openBankForUnemployment: '打開銀行卡以獲得薪水或失業福利！',
        noDepositForAFK: '由於你在AFC ，你將不會收到押金！',
        depositAdded: '您的存款已充值至 ${amount}。總金額： ${profit}.完成小時： {hours}.',
        depositEnded: '您的存款已充值至 ${amount}。總金額： ${profit}.存款完成，可提取錢款。',
        donatePayments: '{count} 次支付，金額爲 {amount} MC ！',
        gotBan: 'Вы получили бан на {amount} {type, select, minutes{минут} hours{часов} days{дней}}! Причина: {reason}',
        socialClubAlreadyUsed: '您的社交俱樂部已被其他帳戶使用'
    },
    lottery: {
        start15min: '彩票結果將在15分鐘後公佈！您仍然可以在其中一家商店購買彩票以參加。',
        noWinner: '彩票：由於參加者缺席，未決定中獎者。',
        winner: '彩票： {player} 成為彩票的贏家並獲得 ${amount}獎！'
    },
    casino: {
        happyHourStart: '鑽石賭場：快樂時光已經開放！有更大機會贏得他們！',
        happyHourEnd: '鑽石賭場：快樂時光結束了！'
    },
    inventory: {
        noSpace: '你的庫存中沒有任何空間！',
        capacityLimit: '由於重量限制，無法移動！',
        giveItemCapacityLimit: '物品不適合重量！',
        takeFromBoxSucces: 'Вы успешно достали $t({title}) {count, plural, =0{} other{{count} шт.}} из ящика. В ящике осталось: {boxCount} шт.',
        noSpaceInVehicle: '車子沒有空閒的空間！',
        noItem: '你沒有這個道具！',
        noItems: '你沒有所需的道具！',
        noItem2: '你沒有 $t({title})',
        itemNotExists: '物品不存在！',
        itemSlotError: '發生了庫存錯誤！',
        cantTakeOffDonateBag: '你不能扔掉捐贈的背包！將其從刪除到庫存中的物品中解放出來。',
        wrongItem: '沒有這樣的主題！',
        walletLimit: '最多可有3個錢包！',
        collectedItem: '您已收集 $t({title})',
        cantTakePart: '此物品不能部分使用！',
        cantPutPart: '此物品不能放置部分！',
        tooBigValue: '數量太大！',
        dontHaveCount: '你沒有那麼多！',
        putForbidden: '禁止放置此物品！',
        gotAmount: '你得到了 {amount} PCS。',
        cantUseTooFast: '不能使用太多',
        cantInsideInventory: '不要在室內！',
        itemFar: '這個主體太遠了！',
        cantDivide: '此物品無法分離！',
        cantSeize: '此道具無法拿走！',
        cantMove: '無法移動此項目！',
        alreadyHasItemInHands: '你手上已有道具！',
        notMore150: '你不能拿超過150 ！',
        notMore20kg: '重量不能超過20公斤的物體！此物品的重量爲 {weight} 公斤。',
        cantKeepInInventory: '此物品無法儲存在庫存中！',
        cantKeepBagInInventory: '你不能把這個道具放在你的背包裡！',
        noSpaceForClothes: '你的存貨中沒有任何空間可以移除你的物品！',
        weaponStuck: '武器卡住了！',
        clothesId: '服裝組合 #{id}',
        events: {
            cashoutSuccess: '您已成功兌現 ${amount}',
            cantUseWeaponNewbie: '要使用1級武器，請取得武器執照',
            noLighter: '你沒有打火機可以抽！',
            noBong: '你沒有吸煙的條子！',
            cantSmokeFast: '你不能抽太多！',
            overEat: '你吃得太多了！',
            cantHealFast: '你不能經常接受治療！',
            alreadyMaxHP: '您已有最大生命值！',
            alreadyHasArmour: '你已經有護甲了！',
            someonePickedItem: '其他人已經拿起了這個道具！',
            cantPickInsideCar: '在車內時，您無法拿取物品！',
            wrongAmountForDivide: '要拆分的數量無效！',
            noSpaceForDivide: '沒有地方分開這個項目！',
            carError: '車輛錯誤！',
            cantCombine: '這些道具無法合併！',
            maxAmountStack: '此道具已在堆棧中擁有最大數量！',
            cantInPrison: '在監獄裡你不能用物體！',
            weaponBroken: '這件武器壞了！',
            takeOffActiveItem: '首先，從你的手中移除活躍的餐點！',
            cantThrowFractionItems: '陣營東西不能扔掉！',
            cantThrowThisItem: '此物品不能丟棄！',
            moveDonateClothes: '您已將附件從捐贈項目清單中移除。你可以透過F2 -捐贈-項目清單退回。',
            takeOffFractionClothes: '先從這個空間移除陣營道具！',
            alreadyHaveBag: '先把另一只背包拿下來！',
            alreadyWearedBag: '你已經有你的背包！你不能擡空的背包，你只能穿上它。',
            wrongGenderClothes: '無法穿上異性套件！',
            cantPutInWallet: '您無法將此項目放入錢包中！',
            cantPutInBag: '你不能把這個道具放在你的背包裡！',
            cantPutInFridge: '此物品不能放入冰箱！',
            alreadyAddedToFastSlots: '此道具已添加到快速插槽中！',
            notAddedToFastSlots: '此物品尚未添加到快速插槽！',
            noSpaceInFastSlots: '快速插槽中沒有可用的空間！',
            noSeizeAccess: '您不符合免登記資格！',
            seizedFrom: '您已從 #{static}中提取 $t({title}) ！',
            seizedBy: '#{static} 已從您提取 $t({title})！',
            armourNotWorn: '你沒有穿防彈背心！',
            cancelTrade: '玩家取消了兌換！',
            loadingData: '數據正在載入中，請再試一次！',
            cantTakeMoreFish: '你不能載多於 {fishLimit} 條魚！',
            cantDragError: '無法移動！更新庫存！',
            loadError: '載入資料時發生錯誤！',
            itemIsNotCanHandItem: '無法拾取此道具！',
            cantDigHere: '你不能在這個地方挖！',
            digNotFound: '你什麼也沒挖出來！',
            dugUpItem: '你挖了些什麼！'
        },
        bbq: {
            pickUp: '你拿了烤肉！',
            cantCookFast: '你不能太多次油炸！',
            notEnoughFish: '你沒有足夠的魚炒！',
            cookFishSuccess: '你炸了魚！'
        },
        boombox: { pickUp: '你拿走了炸彈盒！' },
        hookah: {
            pickUp: '你拿走了Hookah!',
            cantSmokeFast: '你不能抽太多！'
        },
        tent: { pickUp: '你拿走了炸彈盒！' },
        jerrycan: {
            amountRange: '指定1到20升的升數！',
            fullTank: '車子滿了！',
            tooManyLiter: '你太多升了！',
            noItem: '你手裏沒有罐子！',
            noLiterInJerrycan: '這個罐子沒有那麼多升！',
            fuelTankSuccess: '你從罐子裏倒了 {amount} 升'
        },
        warehouse: {
            wrongKey: '鑰匙無法使用。擁有者更換了鎖。',
            notYourHouse: '這不是你的房子。你沒有鑰匙！',
            warehouseNoAccess: '你的排名無法進入倉庫！',
            notYourApartment: '這不是你的公寓。你沒有鑰匙！',
            noTrunkAccess: '不能使用後備箱！',
            loadingData: '數據正在載入中，請再試一次！',
            noMaterialsForDelivery: '你沒有需要送遞的材料！',
            craftCapacityLimit: '超出中繼重量限制！',
            notEnoughMaterialsForCraft: '你沒有足夠的材料來製作！',
            craftSuccess: 'Вы успешно скрафтили $t({boxTitle}) {count, plural, =0{} other{{count} шт.}} за {materials} $t({materialsTitle})'
        }
    },
    halloween: {
        arrived: '現在是萬聖節！前往艾娃的市場了解更多！',
        events: {
            started: '萬聖節活動「甜蜜或惡劣」已經開始！從市場女巫那裡拿一塊糖果，戴上面具，在房子裡收集糖果！',
            continue: '「甜蜜或厭惡」活動繼續開展！在不同的房子收集糖果，以便未來可以從女巫手中兌換獎品！只是不要忘了筐子和面具！',
            finished: '甜蜜或惡劣活動已經結束！你可以兌換從女巫那裡收集到的所有糖果來獲得美味！'
        },
        notHalloweenHour: '這不是萬聖節時間！從 {hour1} 通道到 {hour2}通道',
        houseUsed: '你已經在這個房子裡要求咖啡了！',
        houseGive: '你得到了 {amount} 片糖果！',
        houseReject: '這房子的主人不想給你糖果！',
        maniacEvent: {
            unluckyHour: '小心，那個瘋子出去打獵了！僞裝成樣子，以免變成受害者。',
            unluckyHourEnd: '瘋子逃脫了！總受害者： {amount}'
        },
        pumpkinCollection: '有人正在收集這個南瓜！'
    },
    schoolday: {
        arrived: '這是一個學習節！請造訪大學的Niera ，了解更多！',
        finished: '訓練節結束了！感謝大家的參與！',
        bookCollection: '有人正在收集這本書！'
    },
    market: {
        error: '商店錯誤，請再試一次！',
        stockError: '沒有商品！',
        noItem: '此商品不存在！',
        itemFinished: '產品已售罄！',
        itemBuyLimit: '已達到購買上限！',
        buySuccess: '您已成功購買 $t({title}) 為 ${amount}',
        sellSuccess: '你已成功賣出 $t({title}) 為 ${amount}',
        sellSuccessAndFraction: '您已成功出售 $t({title}) 並收到 ${amount}。餾分安全性已補充 ${amount2}',
        tradeSuccess: '你已成功將道具兌換為 $t({title})'
    },
    job: {
        hijacker: {
            sms: '您好，今天我們有 {model}，色彩： $t({color})，數字： {numberPlate}，您地圖上的大概數據，您有1小時的一切',
            notYours: '這不是你的車',
            deliverySuccess: '你已成功送遞車輛，並已收到 ${amount}！',
            crackWindow: '你打破了窗戶，因此加快了黑客入侵過程，但你會賺取一半收入！',
            breakDoorSuccess: '你已成功入侵車門！',
            failEngine: '你打不開火的！',
            failBreakDoor: '你打不開門！渦輪解碼器壞了！',
            taskDeliver: '將車輛帶到地圖上標記的車庫！',
            failTime: '時間結束了！任務失敗了！',
            farFromGarage: '你不是在法西斯派的建築附近！',
            noBreakLock: '你沒有渦輪解碼器可以破門而入！在市場上購買！',
            noBreakEngine: '你沒有 $t({name}) 打破着火！在市場上購買！',
            inSafeZone: '這輛車已經接近送餐地點了！',
            youGot: '你得到了 ${amount}',
            cantHijackWithout: '不能黑客沒有 $t({title})',
            hijackHint: '要打破鎖，請拾取鑰匙，當你聽到聲音時，鑰匙的一部分會被拾起',
            timeLeft: '剩餘時間',
            reward: '獎勵',
            deliveredTitle: '~w~已交付',
            deliveredBody: '你已成功通過車輛！',
            policeMessage: '汽車品牌 {title}被偷了。顏色： $t({color}).數字： {numberPlate}'
        },
        lumberjack: {
            error: '樹有錯誤！代碼： {code}',
            somebodyChopping: '有人在砍這棵樹！',
            noSkills: '你沒有足夠的經驗來砍伐這棵樹。你需要 {amount} 個經驗',
            growing: '這棵樹還沒有生長！',
            notYours: '這不是你的樹！',
            harvestSuccess: '您已成功裁剪 $t({title})',
            needAxeInHand: '要砍掉一棵樹，你必須拿著一把斧頭！',
            woodGathered: '這棵樹已經被砍伐了！預計會生長！',
            chopping: '切割木材',
            notXmas: '砍掉這棵樹不是聖誕節！'
        },
        mining: {
            somebodyCollecting: '這個礦正在被某人開採',
            notEnoughXP: '你沒有足夠的經驗來獲得這個礦石。你需要 {amount} 個經驗',
            needPickaxeToHarvest: '要開採礦石，你需要手中拿著一把拾毛斧！',
            harvestSuccess: '您已成功取得 $t({title})',
            digging: '礦開採'
        },
        bikerjob: {
            endJob: '你已完成任務！',
            noDrugTab: '你沒什麼可抵押的！',
            earned: '賺取 ~g~+${award}'
        },
        gangjob: {
            endJob: '你已完成任務！',
            noDrugTab: '你沒什麼可抵押的！',
            earned: '賺取 ~g~+${award}'
        },
        mafiajob: {
            endJob: '你已完成任務！',
            noDrugTab: '你沒什麼可抵押的！',
            earned: '賺取 ~g~+${award}'
        },
        builder: {
            endJob: '你已完成建造者的工作！',
            earned: '~g~+${award} ~w~(Заработано всего: ${amount})',
            moveCementAround: '拖放地圖上標記的水泥袋',
            elevatorCheckpoints: '爬上電梯，跟隨檢查點',
            takeSalary: '您已提取了 ${amount}的薪水',
            cantWorkInVehicle: '你不能在車裡工作！你失敗了！',
            bagOnFloor: '袋子在 {amount} 地板上',
            youDroppedBag: '你把袋子掉了！'
        },
        burger: {
            timeout: '你沒有按時完成訂單。獎勵薪水已失效！',
            locked: '漢堡房今天關門了！'
        },
        busdriver: {
            title: '巴士司機',
            description: '與市民同行，在全州各地騎行。巴士司機工作不多！無論您是開車出入城鎮，都由您決定！',
            hireSuccess: '您已成功找到巴士司機工作！',
            leaveJobSuccess: '您已成功辭去巴士司機的工作！',
            startJob: '你已經開始成為巴士駕駛。沿著路線走！',
            jobBlipsAround: '在地圖上標記工作點',
            earned: '你賺了 ${amount}！',
            waitSomeTime: '等待 {amount} 秒！',
            pointFarAway: '站太遠了！'
        },
        butcher: {
            endJob: '你已經完成了屠夫的工作！',
            startJob: '去檢查站，開始切肉。',
            earned: '~g~+${award} ~w~(Заработано всего: ${amount})',
            takeSalary: '您已提取了 ${amount}的薪水'
        },
        farmer: {
            notFoundAnything: '你什麼也沒找到！',
            fruitCollected: '您已採摘水果 $t({name})',
            cantPlantTooFast: '你不能太多次種植！',
            plantedTooMuch: '你種植了太多植物，請稍後再試！',
            alreadyHasPlantNearby: '附近已有植物！',
            tooManyPlantsOnField: '田間植物太多，請稍後再試！',
            notOnField: '你不是在田裏種這種植物！',
            notEnoughXP: '你沒有足夠的經驗種植這種植物。你需要 {amount} 個經驗！',
            plantNotFound: '錯誤！找不到工廠！',
            youCantPlantThis: '你不能那樣做！',
            noWateringCan: '你沒有水罐在你的手！',
            noWaterInCan: '灌水罐沒水了！',
            waterPlantSuccess: '您已成功澆灌植物',
            wateringCanFull: '灌水罐已灌滿！',
            putWaterInCanSuccess: '您已成功重新填充灌注罐！',
            notYourBush: '這不是你的灌木！',
            dugUpPlant: '你挖了 $t({item})',
            nothingDugUp: '你什麼也沒挖出來！',
            noItemsForSale: '你沒有這些食物！',
            soldItemsSuccess: 'Вы продали {count} $t({title}) за ${price}!',
            needWateringCan: '你需要一個灌水罐來灌溉植物！',
            getWateringCanFirst: '先把罐子拿出來！'
        },
        fisherman: {
            blipsForFish: '您可以在地圖上標記釣魚的點',
            fishMoreToFishHere: '在這裡再捕魚 {amount} 次！',
            nothingCaught: '你什麼也沒抓到！',
            noSpaceForFish: '你沒有地方把捕捉。',
            caughtTrash: '你釣了 $t({title})！',
            caughtFish: '你用 {fishWeight} 克的重量抓住了 $t({title}) ！',
            maxFishInventory: '你已經有最大的魚量。魚量已經下跌！',
            noBait: '你沒有誘餌，沒有它，漁獲量會更少！',
            alreadyFishing: '你已經在釣魚了！',
            toFishGetRod: '釣魚，拿釣竿',
            noRodToFish: '你沒有魚竿！',
            waitTillFishPeck: '等魚吃完再走',
            fishingFail: '魚咬碎了！',
            pullFishTimebar: '把魚拔出來',
            caughtFishEvent: '%player {gender, select, 0{выловил} 1{выловила}} $t({title}) с весом {weight} грамм',
            howToCatch: '如果魚向右移動，請按住A鍵。或按住D鍵，如果魚向左移動',
            tooFastCatch: '你抓魚太快了！',
            farFromPoint: '你離接下來的地方太遠了！'
        },
        garbagecollector: {
            title: '垃圾車',
            description: '不要把你的鼻子離開這個看似無代表性的工作。如果不適時清潔廢料，城市的正常運作可能會中斷，這些廢料不斷堆積在整個州的罐中。',
            hireSuccess: '您已成功找到垃圾貨車的工作！',
            leaveJobSuccess: '您已成功退出垃圾車工作！',
            farFromLeader: '你離領袖太遠了，點沒有被創建！',
            takenAllBags: '你已經拿走了所有的袋子。等著領袖！',
            takenAllBagsNext: '你把所有的袋子。前往下一個檢查站！',
            playerNotWorking: '玩家不是垃圾工！',
            earned: '你賺了 ${amount}！',
            leaveVehicleToTakeTrash: '下車去拿垃圾！',
            vehicleFarAway: '你的垃圾車離你太遠了！',
            unloadedTrash: '您已卸下垃圾。您可以進一步收集垃圾！',
            enteredVehicleWithTrash: '你上車時沒有拿走垃圾。你不能那樣做！',
            pointFarAway: '垃圾堆離你太遠了！',
            vehicleFarAwayFromPoint: '您的垃圾車距離垃圾點太遠了！'
        },
        gopostal: {
            title: '郵差',
            description: '作為郵遞員，您將可以在廣大的州域內送遞各種信件和包裹。誰知道他們裡面可能有什麼？',
            hireSuccess: '您已成功申請郵差工作！',
            leaveJobSuccess: '您已成功辭去郵差工作！',
            jobCooldown: '現在是冷卻時間了，再試一次 {date}',
            deliverAllBoxes: '你已成功載入箱子！把箱子拿到隔壁房子去',
            deliverAllBoxesLeader: '你帶走了所有箱子。等著領袖！',
            deliverAllBoxesGetNew: '你把所有箱子都拿走了請上傳更多以繼續！',
            farFromLeader: '你離領袖太遠了，點沒有被創建！',
            noActivePoint: '沒有活躍點！',
            noLic: '你沒有 {title} 執照工作！',
            loadPoints: '在地圖上標記載載點',
            unloadedBoxes: '你已成功卸下箱子！',
            earned: '你賺了 ${amount}！',
            vehicleFarAway: '你的卡車離你太遠了！',
            loadingBoxes: '裝載箱子',
            pointFarAway: '卸貨點距離你太遠了！',
            vehicleFarAwayFromPoint: '你的卡車距離卸貨點太遠了！'
        },
        moneycollector: {
            title: '收款人',
            description: '收藏家的工作不僅有趣，而且有時也很危險，讓你有機會在ATM機和銀行之間轉帳到全州。你不必擔心自己的生命安全，這項工作由一輛裝甲卡車接管，大部分時間你都會在卡車後面開車。',
            hireSuccess: '您已成功找到收銀員的工作！',
            leaveJobSuccess: '您已成功辭去收銀員的工作！',
            farFromLeader: '你離領袖太遠了，點沒有被創建！',
            noLic: '你沒有 {title} 執照工作！',
            jobCooldown: '現在是冷卻時間了，再試一次 {date}',
            vehicleNotExists: '沒有收藏車！',
            vehicleFarAway: '收藏家的車離你太遠了！',
            unloadedAllMoney: '你帶來了所有的錢。請上傳更多以繼續！',
            loadPoints: '在地圖上標記載貨點',
            loadedSuccess: '您已成功加載錢！把錢帶到ATM機！',
            unloadedSuccess: '您已成功卸貨！',
            earned: '你賺了 ${amount}！',
            loadingMoney: '載入金錢',
            pointFarAway: '卸貨點距離你太遠了！',
            vehicleFarAwayFromPoint: '你的卡車距離卸貨點太遠了！'
        },
        mushroomer: {
            jobDisabled: '你還不能摘蘑菇！',
            somebodyCollecting: '這個蘑菇有人正在收集',
            notEnoughXP: '你沒有足夠的經驗來收集這個蘑菇。你需要 {amount} 個經驗',
            needKnifeToHarvest: '要收集手中的蘑菇，你需要拿一把狩獵刀！'
        },
        taxi: {
            title: '計程車司機',
            description: '這項作品為你提供了一個機會，幫助任何想從州的一個點到另一個點的人。工作的要旨是接受命令、運送旅客和接受旅客的付款。',
            hireSuccess: '您已成功僱傭出租車司機！',
            leaveJobSuccess: '您已成功辭去計程車司機的工作！',
            orderNotExists: '秩序已不存在！',
            mustBeInVehicle: '您必須在車上才能接受訂單！',
            orderTakenByOther: '訂單已被另一位計程車司機提取！',
            orderTakeSuccess: '你已取回訂單。透過GPS前往顧客那裡！',
            driverStartComing: '司機 {driver} 已取回訂單，並開始向您前進。車輛： {vehicle}.距離： {dist}米',
            declinedToPay: '{name} 拒絕支付車資',
            youDeclinedToPay: '你拒絕支付車資',
            noMoreThanLimit: '一次不超過 ${amount} ！',
            paidSuccess: '您已成功付款 ${amount}！',
            playerPaidSuccess: '{name} 已支付 ${amount}次票價！',
            notWorkingInTaxi: '你不是在計程車裡工作！',
            newOrder: '新建排序',
            driverQuit: '司機無法提供服務！訂單已取消。',
            passengerQuit: '乘客無法使用。訂單已取消。',
            passengerCancelOrder: '乘客取消了訂單。',
            orderTitle: '地址： {address}.<br>乘客距離： {distance}米<br><br>若要接受，請前往Uber Drive App。'
        },
        floorwasher: {
            startJob: '您可以開始清洗地圖上標記的地板',
            cleanBlips: '地圖顯示您需要清洗地板的點',
            endJob: '您已完成工作，您的收入爲 ${amount}',
            cantWorkInVehicle: '你不能在車裡工作！你失敗了！'
        },
        trucker: {
            title: 'Trucker',
            description: '沒有卡車運貨人，國家經濟就無法正常運作。用卡車和拖車送貨是你的主要任務和責任。',
            hireSuccess: '您已成功僱用卡車運輸員！',
            leaveJobSuccess: '您已成功辭去卡車運輸員的工作！',
            noLic: '你沒有 {title} 執照工作！',
            needMoreSkillToWork1: '再完成 {count} 次運送，即可使用他人的卡車！',
            needMoreSkillToWork2: '再完成 {count} 次運輸，即可使用其他人的拖拉機！',
            jobBlipsAround: '在地圖上標記工作點',
            deliverLoad: '您已成功載入貨物！送出地圖上標記的貨物！',
            unloadedBoxes: '你已成功卸下貨物！',
            earned: '你賺了 ${amount}！',
            notYourVehicle: '這不是你的送餐車！',
            loadingCargo: '裝載貨物...等待10秒鐘！',
            unloadingCargo: '正在卸載...等待10秒鐘！',
            loading: '載入中',
            unloading: '卸載中',
            vehicleNotAccess: '您無法使用此車輛！'
        },
        beekeeper: {
            alreadyHasBees: '這個蜂房已經有蜂了',
            noBees: '受精需要： $t({title1}) 和 $t({title2})',
            waspAttackDestroy: '一羣黃蜂攻擊並摧毀了你的蜂巢！',
            waspDefenceSuccess: '你已經成功擊退了黃蜂羣！蜜蜂是安全的。',
            pacifySuccess: '你成功地制服了蜜蜂。現在你可以安全地得到蜂蜜了。你有 {amount} 秒',
            alreadyPacify: '蜜蜂已經倒下了！'
        }
    },
    winter: {
        preintro: '雪就要下了，一起為這種氣氛做準備吧！',
        started: '冬天已經開始了，快進入美麗的冬天氣氛吧！',
        eventStarted: '聖誕節活動已經開始！前往市場了解更多！',
        prizeOnChristmas: '你只能為聖誕節活動打開禮物！',
        arrived: '現在是冬天！前往艾娃的市場，進一步了解禮物！',
        houseQuest: {
            houseUsed: '你已經給這個房子一個禮物了！',
            houseGive: '您已成功送出禮物，並收到金額為 {amount}的糖果！'
        },
        nightQuotes: {
            one: '冬天喚醒了胃口。在街上下雪的時候，巧克力蛋糕是最好的藥物。',
            two: '冬天是回到童年的最佳時機。下雪的時候，我們感覺又像孩子一樣。',
            three: '冬天，晚上，雪花，溫暖的格子熱巧克力，公斤糖果，浪漫喜劇。',
            four: '雪落在天空中，在奇怪的華爾茲的節奏中旋轉，仿佛它邀請所有人參加白雪舞蹈的旋風。',
            five: '雪總是帶來回憶。雪花--就像一塊小記憶--落在我們手上，然後融化，讓位給下一個。',
            six: '如果你在房子裡看著它，手拿著熱巧克力的溫暖毯子，附近還有幾條橘子，冬天看起來尤其美麗。',
            seven: '空氣聞起來好像從來沒有人呼吸過它—寒冷，脆弱，原始。',
            eight: '冬天也帶來了惰性風，當你可以穿過人體時，它們不知道為什麼要輪到人體。'
        }
    },
    crateFight: {
        hackRequested: '盒子已被黑了，它將在 {amount} 秒內打開！',
        notReady: '這個抽屜將在 {amount} 秒內開啟！',
        drop: '飛機掉下載物，一段時間後，箱子會掉到地上！尋找並拿走戰利品。'
    },
    bankHeist: {
        noBanksFound: '附近沒有銀行！',
        noRank: '你的排名沒有啟動搶劫的能力！',
        cooldown: '這家銀行有一個冷卻！',
        alreadyRobbed: '你的陣營今天已經打劫了銀行！',
        noPlan: '你沒有計劃開始搶劫！',
        stages: {
            started: '銀行劫案已經開始了，你有一小時的時間！',
            stage2: '我們可以進入金庫門，在警察到達前進行黑客襲擊！',
            vaultHackSuccess: '您已成功打開儲存門',
            cageHackSuccess: '您已成功開啟其他大門'
        },
        needToScare: '我們必須先嚇唬銀行員工！',
        canHackAgainIn: '你可以再次黑客 {amount}',
        somebodyHacking: '有人闖進這扇門！',
        somebodyUsing: '有人已經使用了！',
        noItemsNeeded: '你沒有需要破解的道具！',
        thermalInProgress: '溫度單位正在進行中！',
        forHackNeed: '要破解，您需要： $t({item1}) 或 $t({item2})',
        forHackNeed2: '要破解，您需要： $t({item})',
        trolleyMoneyFinished: '這個託盤沒錢了！',
        safeNoMoney: '這個保險箱沒有東西了！',
        allSafeHacked: '所有的保險箱都被黑了！',
        canRobDuring: '你可以從 {amount1} 到 {amount2}搶劫銀行！',
        toStartNeedPlayers: '附近至少有 {amount} 名玩家才能開始搶劫',
        couldntHackDoor: '你不能打破門！',
        drillHint: '要打破牢房，鑽所有的鎖。如果鑽孔太強，鑽孔將開始過熱。',
        pinHint: '記住按壓的順序，然後重複以找到PIN碼。',
        scaring: '恐嚇',
        bankRobbed: '銀行劫案發生'
    },
    animations: {
        favouriteLimit: '您最多可以添加 {amount} 個最喜歡的動畫！',
        circleExists: '此動畫已存在於圓形選單中！',
        list: {
            handsUp: '擡起你的手',
            handsUp1: '擡起手來',
            salute: '榮譽1',
            salute1: '榮譽2',
            natMorPeh: 'Natalia Marines',
            search: '搜索播放器',
            whistle: '吹口哨',
            warmHands: '暖手',
            waiting: '稍等片刻',
            press: '按練習',
            searchOnTheGround: '偷地',
            flapHends: '揮手',
            inspect: '檢查',
            inspGround: '檢查地面',
            pant: '呼吸急促',
            pushUp: '俯臥撐',
            kick: '踢',
            expect: '好好想想',
            shrug: '聳肩',
            showTwoFingers: '顯示兩根手指',
            pointFinger: '用手指指',
            putInMouth: '把東西放進嘴裡',
            prayer: '祈禱',
            drum: '敲敲',
            rubPalms: '擦手掌',
            rubPalms1: '摩擦手掌2',
            rubNeck: '揉揉脖子',
            rubAss: '抓住屁股',
            wash: '淋浴時洗澡',
            nervLookAround: '緊張的看著兩側',
            pickInHands: '手中收集',
            waveFinger: '揮手指',
            tackleBelly: '抓住你的肚子',
            tackleHeart: '抓住心',
            tss: 'Shhh。',
            lyingBlow: '撞到撒謊的人',
            washFace: '清洗',
            transmitOnRadio: '與對話對話機交談',
            wrtPaper: '在傳單上寫作',
            easyDance1: '舞蹈1',
            easyDance2: '舞蹈2',
            easyDance3: '舞蹈3',
            easyDance4: '舞蹈4',
            wrtNotebook: '寫入記事本',
            washFace1: '在湖裏洗',
            drinkCoffee: '在牆邊喝咖啡',
            copInThrong: '警察在人羣中',
            layBum: '像流浪漢一樣躺着',
            lean: '靠在欄杆上',
            sitOnChair1: '坐在椅子上1',
            sitOnChair2: '坐在椅子上2',
            sitOnChair3: '坐在椅子上3',
            sitOnChair4: '坐在椅子上4',
            sitOnChair5: '坐在椅子上5',
            sitOnChair6: '坐在椅子上6',
            sitOnSofa: '坐在沙發上1',
            expanding: '指印',
            handsToSide: '把手臂放在身體兩側',
            handsFeetToSide: '雙臂和腿向兩側張開',
            handsInSides: '手放到身旁',
            handsInSides1: '手放在兩側2',
            foldHandsLookAround: '胳膊交叉，四周看看',
            foldHands: '交叉你的手臂',
            handsOverHead: '把手放在頭後面',
            handsOverBack: '手放在背後',
            volchok: '紡紗',
            runInPlace: '跑到位',
            runInPlaceWaggle: '奔跑到位，展開你的手臂',
            lieOnSide: '躺在你身邊',
            lieOnBelly: '躺在肚子上',
            lieOnBack1: '仰臥1',
            lieOnBack2: '仰臥2',
            lieOnBack3: '仰面躺下3',
            yoga: '瑜伽',
            posSecurity: '守衛姿勢',
            showBiceps: '展示肱二頭肌',
            sit1: '坐下1',
            sit3: '坐下3',
            sit2: '坐下2',
            cryMercy: '求憐憫',
            warmUp: '拉伸',
            foldHands2: '交叉你的手臂',
            arnold: '阿諾德',
            standWall1: '牆壁帖子1',
            standWall2: '牆帖2',
            standWall3: '牆帖3',
            standWall4: '牆帖4',
            standWall5: '牆帖5',
            standWall6: '牆帖6',
            standWall7: '牆帖7',
            standWall8: '牆帖8',
            chillGaz1: '在草坪上放鬆1',
            chillGaz2: '在草坪上放鬆2',
            chillGaz3: '在草坪上放鬆3',
            sunbathe: '在海灘上享受日光浴',
            betterHand: '我強，你弱',
            waveHand: '把手放在空中',
            meditation: '冥想',
            chesatAss: '摸摸你的屁股',
            posSecurity2: '守衛姿勢2',
            fingUp: '手指向上',
            jumpHappy1: '與快樂一起跳躍',
            jumpHappy2: '與快樂一起跳躍2',
            jumpHappy3: '與快樂一起跳躍3',
            gordSelf1: '爲自己感到自豪',
            gordSelf2: '爲自己感到自豪2',
            uspokoit: '放心',
            applause1: '鼓掌1',
            applause2: '鼓掌2',
            applause3: '鼓掌3',
            airkiss: 'Air Kiss',
            kissPlayer: '親吻玩家',
            bro: '哥哥',
            fingersUp1: '手指向上1',
            fingersUp2: '手指向上2',
            jeeEst: 'Zhi是',
            pickInNose: '挖鼻子',
            refusal: '失敗',
            skipHand: '揮手',
            myatKulaki: '薄荷拳',
            upset: '哀悼',
            krytitVisok: '寺廟扭曲',
            stupid: '傻瓜。',
            handFace: '手工面部',
            shootingGun1: '炮火',
            shootingGun2: '用槍射擊',
            baseball: '棒球',
            stand: '機架',
            rage: '生氣',
            showFuck: '顯示傳真',
            facepalm: 'Faispalm',
            badger: '挑逗',
            badger1: '挑逗1',
            badger2: '挑逗2',
            badger3: '挑逗3',
            ugrozhat: '威脅',
            krytitVisok2: '在寺廟旋轉',
            crazy: '去瘋了。',
            stinks: '好臭',
            whatTheHeck: '這是什麼玩意？',
            dj: 'DJ',
            easyDance15: 'Пританзвать',
            lesgin: 'Пезгинка',
            hardBass: '硬貝斯',
            twerk: 'Пверк',
            clap: '拍手',
            dance1: '舞蹈1',
            dance2: '舞蹈2',
            dance3: '舞蹈3',
            dance4: '舞蹈4',
            dance5: '舞蹈5',
            dance6: '舞蹈6',
            dance7: '舞蹈7',
            dance8: '舞蹈8',
            dance9: '舞蹈9',
            dance10: '舞蹈10',
            dance11: '舞蹈11',
            dance12: '舞蹈12',
            dance13: '舞蹈13',
            dance14: '舞蹈14',
            dance15: '舞蹈15',
            dance16: '舞蹈16',
            dance17: '舞蹈17',
            dance18: '舞蹈18',
            dance19: '舞蹈19',
            dance20: '跳舞20',
            dance21: '舞蹈21',
            dance22: '舞蹈22',
            dance23: '舞蹈23',
            dance24: '舞蹈24',
            dance25: '舞蹈25',
            dance26: '舞蹈26',
            dance27: '舞蹈27',
            dance28: '舞蹈28',
            dance29: '舞蹈29',
            dance30: '跳舞30',
            dance31: '舞蹈31',
            dance32: '跳舞32',
            dance33: '舞蹈33',
            dance34: '舞蹈34',
            dance35: '跳舞35',
            dance36: '跳舞36',
            dance37: '跳舞37',
            dance38: '跳舞38',
            dance39: '跳舞39',
            dance40: '跳舞40',
            stripDance1: '跳脫衣舞1',
            stripDance2: '跳脫衣舞2',
            stripDance3: '跳脫衣舞3',
            stripDance4: '跳脫衣舞4',
            stripDance5: '跳脫衣舞5',
            stripDance6: '跳脫衣舞6',
            stripDance7: '跳脫衣舞7',
            stripDance8: '跳脫衣舞8',
            stripDance9: '跳脫衣舞9',
            stripDance10: '跳脫衣舞10',
            stripDance11: '跳脫衣舞11',
            stripDance12: '跳脫衣舞12',
            stripDance13: '跳脫衣舞13',
            guitarist: '吉他手',
            kachatMyshtsy: '秋千',
            chicken: '雞肉',
            lomka: '互鎖',
            runInPlace2: '現場慢跑',
            rock: '磐石',
            rock2: '巖石2',
            waveHands: '揮手',
            karate: '空手道',
            sexMale1: '性愛男子1',
            sexMale2: '性愛男子2',
            sexMale3: '性愛男子3',
            sexFemale1: '性愛女性1',
            sexFemale2: '性愛女性2',
            sexFemale3: '性愛女性3',
            pullHands: '手抽筋',
            trup: '屍體',
            photographer: '攝影師',
            BlindingLights: '致盲燈',
            BoogieDown: 'Boogie Down',
            KneeSlapper: '拍膝',
            Crossbounce: '交叉跳躍',
            DiscoFever: '迪斯科熱',
            DontStartNow: '不要立即開始',
            Floss: 'Floss',
            Fresh: '新鮮',
            GangnamStyle: '江南式',
            HeartRizon: '心裏子',
            JabbaSwitchway: 'Jabba開關',
            TheMacarena: 'The Macarena',
            LastForever: '永久',
            RideThePony: '騎小馬',
            Rollie: 'Rollie',
            SaySo: '告訴我吧',
            SignatureShuffle: '招牌洗牌',
            SquatKick: '深蹲踢',
            StepItUp: '加快步伐',
            TheFlow: 'The Flow',
            TheRenegade: '叛逆者',
            Stuck: '卡住了',
            PumpUpTheJam: '泵向上堵塞',
            Socks: '襪子',
            MyWorld: '我的世界',
            WakeUp: '喚醒',
            OndaOnda: '恩田恩田',
            GetGriddy: 'Get Griddy',
            HitIt: 'Hit It',
            LeaveTheDoorOpen: '敞開大門',
            ChickenWingIt: 'Chicken Wing It',
            Savage: '野蠻人',
            ElectroSwing: '電動秋千',
            Sprinkler: '灑水器',
            Smeeze: 'Smeeze',
            GoMufasa: 'GoMufasa',
            HeyNow: '嘿，現在',
            BuildUp: '建立',
            Breakdown: 'Breakdown',
            TakeTheL: '拿着L',
            IAintAfraid: '我並不害怕',
            GetGone: '走吧',
            MaximumBounce: '最大彈跳次數',
            ILikeToMoveIt: '我喜歡移動它',
            LeiltElomr: 'Leilt Elomr',
            Tidy: '整潔',
            BhangraBoogie: 'Bhangra Boogie',
            OutWest: '外面西部',
            ToosieSlide: 'Toosie Slide',
            PullUp: '向上拉',
            TheCraneKick: 'The Crane Kick',
            BillyBounce: 'Billy Bounce',
            ElectroShuffle: 'Electro Shuffle',
            WorkItOut: '想想辦法',
            Zany: 'Zany',
            SmoothMoves: '平滑移動',
            Vivacious: '活力四射',
            Hula: '呼拉',
            TrueHeart: '真心',
            Reanimated: '已復活',
            InDaParty: '在大黨內',
            BimBamBoom: 'Bim Bam Boom',
            WannaSeeMe: '想見我',
            DynamicShuffle: 'Dynamic Shuffle',
            NeverGonna: '永遠不會',
            FrightFunk: '恐懼朋克',
            Jitterbug: '抖抖蟲',
            Infectious: '感染性',
            WhereIsMatt: 'Matt在哪裏?',
            SavorTheW: '品味W'
        }
    },
    twoFactor: {
        activatedAlready: '2FA已激活！',
        activatedSuccess: '您已成功激活帳戶上的2FA ！',
        deactivatedSuccess: '您已成功禁用您的帳戶中的2FA ！',
        wrongToken: '輸入的驗證碼無效！'
    },
    wedding: {
        marriedAlready: '你已經結婚了！',
        playerMarried: '玩家已婚！',
        notMarried: '你還沒有結婚！',
        notMarriedOnYou: '玩家沒有娶你！',
        denyMarry: '婚禮不開了！',
        denyDivorce: '離婚了！',
        recentlyWasWedding: '最近有個婚禮請幾分鐘後再試一次',
        marrySuccess: '你已經成功地結婚了！',
        divorceSuccess: '你離婚成功了！',
        nameUsed: '名字和姓氏已被使用！'
    },
    nineMay: { started: '當煙花爆發，沒有人看星空了。看看天空，看看城市上空的煙火！' },
    doors: {
        lock: '你關上了門 #{id}',
        unlock: '你打開了門 #{id}'
    },
    radio: {
        noRadio: '你沒有收音機！',
        outOfRange: '這個頻率超出了無線電範圍！從 {from}到 {to}可用',
        restrictFrequency: '您無法使用此頻率！'
    },
    quest: {
        completed: '你已成功完成任務 $t({name})！',
        nextStage: '任務的下一階段為 $t({name})！',
        cantRepeatQuest: '你不能再完成任務了！',
        canceledQuest: '你取消了任務！',
        notFinished: '未完成',
        list: {
            0: {
                name: '開始時間',
                description: '熟悉度探索',
                stages: { 1: '與生產附近的機械人談話' }
            },
            1: {
                name: '初學者之路',
                description: '熟悉度探索',
                stages: {
                    1: '請參閱《旅遊指南》中的租車清單',
                    2: '開立銀行帳戶',
                    3: '查看ATM機',
                    4: '與您的旅遊攻略討論',
                    5: '兼職工作賺取$ 3,000',
                    6: '全天候前往商店購買iPhone',
                    7: '造訪服裝店',
                    8: '造訪理髮店',
                    9: '與您的旅遊攻略討論',
                    10: '取得駕駛執照',
                    11: '造訪任何汽車經銷商',
                    12: '與您的旅遊攻略討論'
                }
            },
            2: {
                name: '旅行者',
                description: '救救山上的人',
                stages: {
                    1: '獲取可口可樂並帶給旅客',
                    2: '購買4個隕石巧克力和3罐電可樂，然後帶給旅客',
                    3: '從商店現金購買一把刀',
                    4: '把刀帶到旅客那裡'
                }
            },
            3: {
                name: '籃球手',
                description: '遊戲需要音樂',
                stages: {
                    1: '用現金購買爆炸盒',
                    2: '把吊臂盒拿到籃球手那裡'
                }
            },
            4: {
                name: '漁夫',
                description: '協助在河裏捕魚',
                stages: {
                    1: '在河裏釣20公斤魚',
                    2: '出售20公斤在河上捕撈的魚',
                    3: '和河畔的漁民談談'
                }
            },
            5: {
                name: 'Trucker',
                description: '以長途跋涉的方式工作',
                stages: {
                    1: '賺取$ 25,000',
                    2: '與僱主聯絡'
                }
            },
            6: {
                name: '垃圾車',
                description: '處理骯亂的袋子',
                stages: {
                    1: '拿150袋',
                    2: '與僱主聯絡'
                }
            },
            7: {
                name: '收款人',
                description: '賺錢',
                stages: {
                    1: '拿30袋',
                    2: '與銀行員談談'
                }
            },
            8: {
                name: '生成器',
                description: '水泥搬運',
                stages: {
                    1: '賺取$ 3,000',
                    2: '與僱主交談'
                }
            },
            9: {
                name: '伐木工',
                description: '使用樹木',
                stages: {
                    1: '砍下50棵樹',
                    2: '與僱主交談'
                }
            },
            10: {
                name: '巴士司機',
                description: '在城市工作',
                stages: {
                    1: '乘坐100站',
                    2: '與僱主聯絡'
                }
            },
            11: {
                name: '屠夫',
                description: '與肉類一起工作',
                stages: {
                    1: '賺取$ 3,000',
                    2: '與僱主交談'
                }
            },
            12: {
                name: '奶奶的悲哀',
                description: '淚水不是解決辦法',
                stages: {
                    1: '請老太太現金買一杯奶昔',
                    2: '拿奶昔去找老太太'
                }
            },
            13: {
                name: '迷失的獵人',
                description: '拯救在森林中迷路的獵人',
                stages: {
                    1: '尋找森林裡的獵人，並與他交談',
                    2: '把三條繃帶帶給獵人',
                    3: '現金購買雙筒槍',
                    4: '帶著雙筒彈槍去獵人那裡',
                    5: '為獵人帶來12個12ga衝擊鏡',
                    6: '把筆記帶給醫院的兒子'
                }
            },
            14: {
                name: '郵差',
                description: '使用盒子',
                stages: {
                    1: '派送69包',
                    2: '聯絡郵局工作者'
                }
            },
            15: {
                name: 'Rocking Gear',
                description: '肌肉需要糖',
                stages: {
                    1: '使用卡片購買6根EgoChaser棒',
                    2: '取6小條到鴨子'
                }
            },
            16: {
                name: '屁股',
                description: '救助無家可歸者',
                stages: {
                    1: '現金購買急救箱',
                    2: '把急救箱拿到臀部',
                    3: '在Bean Machine咖啡店現金購買甜甜圈',
                    4: '把甜甜圈拿到屁股去'
                }
            },
            17: {
                name: '大哥',
                description: 'Dudushkina兄弟',
                stages: {
                    1: '在Sandy Shores的24/7商店中找到您的兄弟',
                    2: '購買並帶上牛排、夏瓦瑪、雞尾酒和烤面包'
                }
            },
            19: {
                name: '聖誕活動',
                description: '是時候當聖誕老人了！',
                stages: {
                    1: '把禮物帶回家',
                    2: '與伊娃交談'
                }
            }
        },
        worldQuest: {
            completedButSecondAccount: '您已成功完成您的任務！不過，你的另一個角色已經獲得獎勵。此角色無法獲得此任務的獎勵',
            completed: '你已成功完成任務並獲得獎勵！繼續完成，協助他人完成世界任務。您可以在市場上了解更多',
            worldQuest: '世界任務',
            newQuests: '新世界任務已出現！快點完成它們，即可獲得獎勵！您可以在市場上了解更多',
            finishedOne: '完成了全球任務之一！全新任務現已登場！你可以在市場上看到它。',
            finishedAll: '完成所有世界任務！感謝大家的參與！很快就會有新的任務！',
            questList: {
                one: {
                    title: '民間農民',
                    local: '你必須在農場生長和收割才能獲得獎勵',
                    total: '所有玩家必須在農場生長和收割才能獲得獎勵'
                },
                two: {
                    title: '當地漁民',
                    local: '你必須捉到任何魚才能獲得獎勵',
                    total: '所有玩家必須捉到任何魚才能獲得獎勵'
                },
                three: {
                    title: 'NYAmazon送餐服務',
                    local: '你必須在郵差處稀釋包裹才能獲得獎勵',
                    total: '所有玩家必須在郵差處繁殖包裹才能獲得獎勵'
                },
                four: {
                    title: '綠紙送遞',
                    local: '你必須把錢送給收藏家才能獲得獎勵',
                    total: '所有玩家必須向收藏者送錢才能獲得獎勵'
                },
                five: {
                    title: '漫長的道路',
                    local: '你必須用卡車送貨才能獲得獎勵',
                    total: '所有玩家必須在卡車上送貨才能獲得獎勵'
                },
                six: {
                    title: '我的修道院長來了',
                    local: '你必須在專員的工作中騎行標籤才能獲得獎勵',
                    total: '所有玩家必須在巴士駕駛工作時騎上標籤才能獲得獎勵'
                },
                seven: {
                    title: 'WALL-E先生',
                    local: '你必須在垃圾車上工作，從垃圾桶拾取袋子才能獲得獎勵',
                    total: '所有玩家必須在垃圾桶中拾取袋子，並在垃圾車上工作以獲得獎勵'
                }
            }
        }
    },
    gym: { tired: '你疲倦了要變得更強大-來吧 {date}' },
    phone: {
        noPhone: '你沒有手機！在商店裏購買。',
        callHelp: '你已經在 {number}號申請。留在原地，以免取消通話。',
        smsWrongNumber: '無法向此號碼發送短訊！',
        noSimBalance: '您的SIM卡餘額爲零！在其中一臺ATM機上充值。',
        carBuy: '發生錯誤，請再試一次！ {number}',
        carBusy: '先下車！',
        vehModel: '此模型目前正在最後定稿！非常抱歉。',
        serviceSpawn: '再來一次，你可以 {number}',
        noHousesAndAppsForSpawn: '您沒有房子或公寓可以讓其他車輛進入！',
        vehicleSlots: '你的車庫裡沒有足夠的空間來運送這艘船！',
        getVariable: '你已經上車了！',
        park: '你的車輛正在維修中！你可以在汽車服務處取走。',
        getoccupants: '運輸艦上還有另一個玩家。地圖顯示車輛的位置。',
        getoccupants2: '運輸艦上還有另一個玩家。地圖顯示車輛的位置。',
        parkError: '未停車。地圖上會顯示車輛的位置',
        parkError2: '你不在車邊！在地圖上標記了車輛的位置！',
        carPark: '交通 {name} 送達！距離您 {number}米',
        communicationError: '通訊錯誤-通話已結束！',
        openPhoneToReadMessage: '打開手機閱讀訊息',
        newMessage: '新消息',
        couldntCallToUser: '無法聯絡訂閱者',
        userBusy: '訂閱者正忙',
        userUnavailable: '訂閱者暫時無法使用',
        incomingCall: '入局呼叫',
        onlyVehiclesAllowedInGarage: '禁止在車庫裡吐水和飛行設備！在特別指定地區使用汽車服務。',
        sms: { giveMoney: '{type, select, negative{Списание} other {Начисление}} средств ~g~${amount}~w~. Причина {reason}' }
    },
    other: {
        inTrunk: '你已經在車的後車廂裡了',
        trunkBusy: '後備箱裏有沒有人',
        cantTrunk: '你不能進入後車箱',
        youNotTrunk: '你不在車後車廂裡'
    },
    bank: {
        notValidPin: 'PIN碼必須包含4位數字！',
        samePin: '你已經有這個PIN碼！',
        changePinSuccess: '您已成功重置PIN。新PIN ： {code}。記住它。它也已發送到您的電子郵件地址。',
        wrongPin: '輸入的PIN碼無效！',
        wrongAmount: '金額無效！',
        wrongAccount: '無效的帳戶號碼！',
        accountBlocked: '您的銀行帳戶已被封鎖！',
        accountNotBlocked: '您的銀行帳戶沒有被封鎖！',
        notEnoughMoneyOnAccount: '您的銀行帳戶中沒有足夠的資金！',
        cantSendOnSame: '無法轉帳至自己的帳戶！',
        accountNotFound: '沒有這樣的銀行帳戶！',
        accountNotFoundOrPhone: '沒有這樣的電話號碼或銀行帳戶！',
        thisAccountBlocked: '此帳戶已被封鎖！',
        bankSendSuccess: 'Вы успешно перевели {amount} {type, select, card{на банковский счёт №} other{по номеру телефона}} {number}. Комиссия: ${commission}',
        alreadyHasDeposit: '你已有開放存款！',
        notEnoughMoneyForDeposit: '您的帳戶中沒有足夠的資金開立存款！',
        invalidTariff: '無效票價！',
        notExistsTariff: '票價不存在！',
        noDeposit: '你沒有貢獻！',
        sumRange: '金額必須介於 ${amount1} 和 ${amount2}之間',
        hoursRange: '小時數必須介於 {amount1} 和 {amount2}之間',
        openDepositSuccess: '您已成功開立 {hours} 小時的 {title} 存款。捐款金額： ${amount}',
        invalidDeposit: '你沒有那種投入！',
        closedDeposit: '您已成功關閉存款，並退回嵌套 ${amount}',
        closedDepositWithMoney: '您已成功關閉存款，並返回嵌套 ${amount}。利潤為： ${profit}',
        notEnoughCash: '你沒有足夠的現金！',
        transferToBankSuccess: '您已成功將 ${sum} 從現金轉入您的銀行帳戶！',
        cashoutSuccess: '你已成功從銀行帳戶提取 ${sum} 。費用： ${commission}！',
        alreadyActiveTariff: '你已經啟用了此計劃！',
        notEnoughMoneyToPayTariff: '您的帳戶中沒有足夠的金額來支付車資！',
        notEnoughMoneyToAddupTariff: '您的帳戶中沒有足夠的資金來充值您的存款！',
        tariffError: '車資錯誤！',
        noBankCardInInventory: '你的存貨中沒有銀行卡！',
        changeBankTariffSuccess: '你已成功將銀行利率更改為 {tariff}。已向您的帳戶收取 ${amount} 。',
        noAddupDeposit: '您的存款中沒有補充選項！',
        depositExpired: '您的存款已過期！',
        cantBeHigherLimit: '存款金額不能高於上限！',
        addupDepositSuccess: '您已成功用 $t({title}) 到 ${amount}充值您的存款。存款總額： ${totalAmount}',
        repaidDebtSuccess: '您已成功清除您的銀行帳戶欠款 ${amount}',
        alreadyHasCard: '您已經有銀行卡！',
        noSpaceForCard: '地圖庫存中沒有空間！',
        notEnoughMoneyForAccount: '您沒有足夠的現金開立帳戶！',
        bankAccountOpenSuccess: '您已成功開立銀行帳戶 $t({title})。PIN ： {pin}.收費： ${amount}',
        workingBreak: '午休後，銀行將能夠處理你的錢款交易。從13: 00到14: 00休息！',
        workingHours: '銀行只在工作時間處理錢款交易！從 {hour1} 到 {hour2} 點。',
        noBankAccount: '您沒有銀行帳戶！',
        notEnoughMoneyOnBank: '你的銀行帳戶沒有這麼多！',
        thisBankAccountNotExists: '此銀行帳戶不存在！',
        thisBankAccountBlocked: '此銀行帳戶已被封鎖！',
        deposits: {
            save: {
                title: '「儲存」貢獻',
                description: '開始存款，有助節省儲蓄，並獲得高達每小時5,000美元的體面收入'
            },
            improve: {
                title: '貢獻「開發」',
                description: '房價高於普通存款。存款方便存入。每小時賺取高達$ 8,400'
            },
            control: {
                title: '「管理」貢獻',
                description: '存入$ 1,000,000或以上，賺取超過任何其他存款。每小時賺取高達$ 15,000'
            }
        }
    },
    donate: {
        rouletteTempDisabled: '回合已暫時停用！',
        notEnoughCoins: '你沒有足夠的強大硬幣！',
        invalidKeyCode: '您輸入的驗證碼不正確！',
        keyCodeAlreadyUsed: '此代碼已被使用！',
        activateCodeSuccess: '您已成功激活 {amount} MC上的代碼！',
        activateCodeSuccessEU: '您已成功激活 {amount} MC上的代碼！',
        activateCodeSuccessBonusEU: '您已成功激活 {amount} MC上的代碼。獎勵： {additional} 個MC ！',
        kinguinWrongRegion: '你已啟用為其他地區購買的代碼！已重新計算MC速率。',
        onlyServer5: '此道具僅在伺服器5上可用！',
        carLimit: '此產品中的車輛數量已達上限！',
        carForOnlyServer5: '這輛車僅可在伺服器5上購買！',
        noFreeSpin: '您沒有免費的輪盤捲捲！',
        getWeaponSuccess: '你已成功獲得武器。它現在在你的庫存中。',
        getAccessorySuccess: '您已成功收到附件。它現在在你的庫存中。',
        rouletteWin: '輪賽獎金',
        soldWinnings: '商品已成功售出！',
        forBonusCode: '獎勵代碼',
        levelUp: '達到 {level} 級',
        noFreeGarages: '沒有可用的運輸插槽！推薦至項目清單。',
        noFreeGarages2: 'Нет свободных слотов для транспорта! Доступно {amount} слотов.',
        getCarSuccess: '您可以通過電話在Premium Deluxe Motorsport經銷商停車場購買 {title} 。',
        alreadyActiveVIP: '你已經擁有另一個活躍的VIP帳戶。推薦至項目清單。',
        getVIPSuccess: '你有 {title} 個VIP帳戶！有效性-最多 {date}',
        buyVIPSuccess: 'Вы успешно приобрели {title} VIP аккаунт за ${price} MC! Срок действия - до {date}',
        cantSellItem: '不可能賣一件東西！',
        successBuy: '您已成功購買 {price} MC的 {title1} - {title2} ！',
        alreadyHaveVIP: '您已經擁有VIP帳戶！',
        buyCarSuccess: '您已成功以 {price} MC購買 {title} ！你可以透過電話在Premium Deluxe Motorsport經銷商停車場購買。',
        alreadyHaveLatestSlot: '您已有3個空位！',
        buyThirdSlotSuccess: '你已成功購買3個字元的老虎機槽，購買了 {price} 個MC ！',
        noWarns: '你沒有警告！',
        removeWarnSuccess: '您已成功移除 {price} MC的警告！剩餘： {amount}',
        newbieBundleLevelLimit: '入門套件最多可購買 {amount} 級！',
        buySuccess: '您已成功以 {price} MC購買 {title} ！',
        needToBeInCarToBuyLicencePlates: '你必須坐在私人交通工具上，在那裡你購買一個房間！',
        numberIsUsed: '這個房間已經有人了！',
        buyLicencePlatesSuccess: '您已成功為 {price} 個MC購買了運輸號碼 {value} ！',
        noPhone: '你沒有手機！',
        restrictedPhoneNumber: '此號碼無法購買！',
        buyPhoneNumberSuccess: '您已成功為 {price} MC購買電話號碼 {value} ！',
        promoIsUsed: '此優惠碼已被使用！',
        promoUnavailable: '您無法暫時建立優惠碼！',
        buyPromoSuccess: '您已成功購買 {price} 個MC的促銷代碼 {value} ！',
        availableWithHouseOnly: '只有當您有家時才可用！',
        canBuyGaragesLimit: '您最多可以購買50個額外名額！',
        buyGaragesSuccess: '您已成功為 {price} MC購買額外的車庫空間！現在你有 {amount} 個額外的車庫空間。',
        buyArmyCertificateSuccess: '你已成功購買 {price} MC的軍事門票！',
        adminsCantChangeName: '不允許管理員更改名稱。',
        leadersCantChangeName: '領導者不允許更改名稱。',
        nameLengthInfo: '名字+姓氏必須在 {from} 到 {to} 個字元之間！',
        enterNameSurname: '請輸入角色的名字和姓氏！',
        latinOnly: '只能使用拉丁字符！',
        nameMustStartFromCapital: '名字的每一部分都必須以大寫字母開頭！',
        nameIsTaken: '此名稱已被使用！',
        nameChangeSuccess: '您已成功更改 {price} MC的名稱，從 {prevName} 更改爲 {newName} ！',
        clothesBuySuccess: '您已成功購買 {price} 個MC的 {title} 件衣服！',
        takeOfClothesFirst: '先把衣服脫了！',
        changeNameAndCustomizationSuccess: '你已成功變更你的外觀和名稱，從 {prevName} 改為 {newName} ，共 {price} 個MC ！',
        changeCustomizationSuccess: '您已成功更改 {price} MC的外觀！',
        cantWhileDead: '不是在死亡的時候！',
        cantWhileTied: '你不能被捆起來！',
        cantWhileCuffed: '你不能趁着手骨折的時候做!',
        mustBeNearbyClothesShop: '你應該離利奧波德商店不到50公尺！',
        canBuyPromoDays: '優惠券暫時無法購買！',
        canBuyPresentsInChristmas: '禮物只能在聖誕節活動期間購買！',
        alreadyHasAnimation: '你已經有了這個動畫！',
        animationBuySuccess: '您已成功為 {price} 個MC購買動畫 $t({title}) ！',
        alreadyHaveArmyCertificate: '你已經有軍人身份證明文件！',
        config: {
            money: {
                title: '虛擬貨幣',
                description: '缺少錢？將你的MCoin兌換成虛擬貨幣，忘記問題。',
                items: {
                    '10k': {
                        title: '$ 10,000',
                        description: '$ 10,000存入您的遊戲帳戶',
                        fullDescription: '購買遊戲貨幣，你便有機會成為我們州份的明星：車輛、房屋、商家等著你！'
                    },
                    '50k': {
                        title: '$ 50,000',
                        description: '$ 50,000存入您的遊戲帳戶',
                        fullDescription: '購買遊戲貨幣，你便有機會成為我們州份的明星：車輛、房屋、商家等著你！'
                    },
                    '200k': {
                        title: '$ 200,000',
                        description: '$ 200,000存入您的遊戲帳戶',
                        fullDescription: '購買遊戲貨幣，你便有機會成為我們州份的明星：車輛、房屋、商家等著你！'
                    },
                    '500k': {
                        title: '$ 500,000',
                        description: '$ 50萬存入你的遊戲帳戶',
                        fullDescription: '購買遊戲貨幣，你便有機會成為我們州份的明星：車輛、房屋、商家等著你！'
                    },
                    '2000k': {
                        title: '$ 2,000,000',
                        description: '$ 2,000,000存入您的遊戲帳戶',
                        fullDescription: '購買遊戲貨幣，你便有機會成為我們州份的明星：車輛、房屋、商家等著你！'
                    },
                    '10000k': {
                        title: '$ 10,000,000',
                        description: '$ 10,000,000存入您的遊戲帳戶',
                        fullDescription: '購買遊戲貨幣，你便有機會成為我們州份的明星：車輛、房屋、商家等著你！'
                    }
                }
            },
            premium: {
                title: 'Premium Account',
                description: '更多機會、經驗和薪水獎勵。',
                items: {
                    bronze: {
                        title: '青銅色',
                        description: `
                        特權有效期為30天。<br><br>
                        優勢青銅帳戶：<br>
                        -死亡後，你將以50點生命值在醫院出現。<br>
                        -失業福利： 250美元/小時。<br>
                        -加薪： 500美元/小時。<br>
                        -工作收入乘以1.15倍。`
                    },
                    silver: {
                        title: 'Silver',
                        description: `
                        特權有效期為30天。<br><br>
                        銀帳戶福利：<br>
                        -死亡後，您將以60點生命值顯示在醫院。<br>
                        -失業福利： $ 500/小時。<br>
                        -薪金增加： $ 1000/小時。<br>
                        -工作收入乘以1.3倍。`
                    },
                    gold: {
                        title: '金色',
                        description: `
                        特權有效期30天。<br><br>
                        黃金帳戶福利：<br>
                        -死亡後，您將以75點生命值顯示在醫院。<br>
                        -失業福利： 850美元/小時。<br>
                        -薪金補充： 1500美元/小時。<br>
                        -工作收入乘以1.45倍。<br>
                        -提前3週納稅的能力。`
                    },
                    platinum: {
                        title: 'Platinum',
                        description: `
                        特權有效期為30天。<br><br>
                        白金帳戶的好處：<br>
                        -死亡後，您會以90點生命值出現在醫院。<br>
                        -失業福利： 1200美元/小時。<br>
                        -加薪： 2000美元/小時。<br>
                        -工作收入乘以1.6倍。<br>
                        -提前4週納稅的能力。<br>
                        -由此產生的遊戲體驗增加了2倍。`
                    }
                }
            },
            account: {
                title: '角色設定',
                description: '更改名稱、外貌等功能',
                items: {
                    nickname: {
                        title: '改名',
                        description: `
                        更改姓名有很多原因：<br>
                        -角色扮演角色的死亡。<br>
                        -試圖開始一個新的故事。<br>
                        -願意加入另一個陣營或幫派。<br>
                        -失去所有聯繫，熟人。`
                    },
                    person: {
                        title: '額外字符',
                        description: '在你的遊戲帳戶中建立其他角色的能力。'
                    },
                    warn: {
                        title: '放鬆警告',
                        description: '從你的帳戶中解釋警告。'
                    },
                    customization: {
                        title: '外觀變化',
                        description: '你必須在Leopold服裝店的50公尺內才能改變外觀。'
                    },
                    vehicleNumber: {
                        title: '運輸號碼',
                        description: '每位汽車愛好者都祕密地夢想著一個美麗的房間。此外，這是免受路上無禮的保障，甚至是免受某些豁免的保障。'
                    },
                    phoneNumber: {
                        title: '電話號碼',
                        description: '您的個人電話號碼'
                    },
                    promoCode: {
                        title: '優惠券代碼',
                        description: '您的個人優惠碼'
                    },
                    garage: {
                        title: '車庫空間',
                        description: '擁有更多私家車的機會。每座新座位250 MC以上'
                    },
                    weddingDivorce: {
                        title: '婚禮離婚',
                        description: '你要不要等到另一半離婚？那就馬上在這裡做！'
                    },
                    armyCertificate: {
                        title: '軍事門票',
                        description: '沒有軍事卡，你將無法加入安全結構。如果要通過年輕戰士的課程，也可以在國家警衛服務後接受。'
                    }
                }
            },
            starter: {
                title: '初學者套件',
                description: '適合初學者的獨特套件！',
                items: {
                    bronze: {
                        title: '新手包青銅',
                        description: `
                        - Лицензия CDL: B<br>
                        - Лицензия Drive D<br>
                        - Прокачивание способности «Сила» до 20%<br>
                        - Прокачивание способности «Стрельба» до 20%<br>
                        - Прокачивание способности «Выносливость» до 20%<br>
                        - VIP статус игрока уровня «Bronze» на 7 дней<br>
                        - Виртуальная валюта 20.000`
                    },
                    gold: {
                        title: '新手包黃金',
                        description: `
                        - CDL執照： A + B<br>
                        - Drive D執照<br>
                        -力量高達45%<br>
                        -射擊高達45%<br>
                        -耐力高達45%<br>
                        - VIP等級7玩家狀態金牌<br>
                        -虛擬貨幣45.000`
                    },
                    platinum: {
                        title: '新手包白金',
                        description: `
                        -所有許可證同捆包<br>
                        -強度高達100%<br>
                        -拍攝高達100%<br>
                        -耐力高達100%<br>
                        -白金VIP 7天<br>
                        -虛擬貨幣100.000`
                    }
                }
            },
            xmas: {
                title: '聖誕禮物',
                description: '這是獲得獨特獎勵的聖誕禮物的獨特機會！',
                items: {
                    small: {
                        title: '小禮物',
                        fullDescription: '這是獲得獨特獎勵的聖誕禮物的獨特機會！'
                    },
                    medium: {
                        title: '中等禮物',
                        fullDescription: '這是獲得獨特獎勵的聖誕禮物的獨特機會！'
                    },
                    large: {
                        title: '很棒的禮物',
                        fullDescription: '這是獲得獨特獎勵的聖誕禮物的獨特機會！'
                    }
                }
            },
            cars: {
                title: '專屬交通工具',
                description: '您在經銷商店中找不到的運輸工具。',
                items: {
                    urus58: {
                        title: 'LAMBO Urus Kreed版',
                        fullDescription: '限量版。整個專案最多58輛車。最高速度提高到351公裏/小時。有來自Yegor Kreed的親筆籤名不在汽油箱和客艙中。您可以安裝額外的美容調節和重量，僅適用於此車型。那輛車不能賣'
                    },
                    vesta: { fullDescription: 'LADA (Vaz) Vesta I – B級轎車，前輪駕駛。技術、變體和機器人。汽油和LPG引擎的功率從106馬力到122馬力。 <br><br> 不提供汽車的外觀調整。那輛車不能賣' },
                    priora: { fullDescription: 'LADA (Vaz) Priora I - B級轎車，前輪駕駛。力學。汽油引擎從81馬力到98馬力。 <br><br> 不提供汽車的外觀調整。那輛車不能賣' },
                    urban: { fullDescription: '拉達（ VAZ ） 2121 （ 4x4 ） – 3驅SUV。J級，四輪駕駛，技術。汽油引擎， 83馬力。 <br><br> 車輛有美容調節功能。那輛車不能賣' },
                    mjc: { fullDescription: 'MINI Hatch JCW GP - Hatchback 3 DV。B級，前輪駕駛，自動機械人。汽油引擎從231馬力到306馬力。' },
                    dsprinter: { fullDescription: '道奇Sprinter —貨車和客車，美國道奇公司的面包車，奔馳Sprinter的類比。 <br><br> 不提供車輛的化妝調節。汽車不能賣。<br><br>適合卡車運貨人工作。' },
                    msprinter: { fullDescription: '梅賽德斯—奔馳Sprinter —梅賽德斯—奔馳低噸位車家族。 <br><br> 不提供車輛的化妝調整。汽車不能賣。<br><br>適合卡車運貨人工作。' },
                    msprinters1: { fullDescription: '梅賽德斯—奔馳Sprinter —梅賽德斯—奔馳低噸位車家族。 <br><br> 不提供車輛的化妝調整。汽車不能賣。<br><br>適合卡車運貨人工作。' },
                    vclass: { fullDescription: '梅賽德斯-奔馳V Class II是一款M Class迷你車、後輪驅動車和全輪驅動車。技術和自動。動力為136至239馬力之柴油及汽油引擎。 <br><br> 不提供車輛的美容調節。那輛車不能賣' },
                    nisgtr: { fullDescription: '日產GT-R I Restyling 3 – S Class Coupe ， 4WD。機器人。汽油引擎的功率從555馬力到570馬力。 <br><br> 車輛的美麗調整是可用的。那輛車不能賣' },
                    v4sp: { fullDescription: 'Ducati ， Superleggera V4 -世界上第一臺碳結構摩託車，通往公路。 <br><br> 不提供車輛的化妝調整。那輛車不能賣' },
                    velar: { fullDescription: 'Land Rover Range Rover Velar I – SUV 5 DV。J級，四輪駕駛，自動。汽油，柴油和混合引擎從180馬力到550馬力。' },
                    '718bs': { fullDescription: '保時捷Boxster IV 718 （ 982 ）是一款後輪驅動S級道路車。機械手和機器人，汽油引擎，功率從300馬力到400馬力。' },
                    amggt: { fullDescription: '梅賽德斯-奔馳AMG GT I – S Class Coupe ，後輪驅動。機器人。汽油引擎，功率從462馬力到510馬力。 <br><br> 不提供車輛的美容調節。那輛車不能賣' },
                    e63s: { fullDescription: '引擎容量5461釐米。 <br> 引擎功率： 612 hp <br> 車重： 1950公斤。 <br><br> 不提供車的美容調節。那輛車不能賣' },
                    m5comp: { fullDescription: '引擎容量4395立方釐米。 <br> 引擎功率： 625 hp <br> 車重： 1865公斤。 <br><br> 車外觀調整可供選擇。那輛車不能賣' },
                    cls63s: { fullDescription: '梅賽德斯-奔馳CLS AMG II （ W218 ）重塑– E級轎車，全輪驅動和後輪驅動。汽油引擎，功率從557馬力到585馬力。' },
                    bentaygast: { fullDescription: 'Bentley Bentayga I – SUV 5 dv. J級，四輪駕駛，自動。柴油、汽油和混合動力引擎的功率從435馬力到635馬力。 <br><br> 提供車輛的外觀調節。那輛車不能賣' },
                    g63: { fullDescription: '引擎容量3982立方釐米。 <br> 引擎功率： 585 hp <br> 車重： 2485公斤。 <br><br> 有車的外觀調整。那輛車不能賣' },
                    panamera17turbo: { fullDescription: '保時捷Panamera II – Hatchback 5 DV。F級，全輪駕駛和後輪駕駛。機器人。混合動力、汽油和柴油引擎從330馬力到550馬力。 <br><br> 不提供車輛的外觀調整。那輛車不能賣' },
                    m8gc: { fullDescription: 'BMW M8 Gran Coupe是一款S級四輪駕駛轎車。汽油引擎，功率從600到625馬力' },
                    ghost: { fullDescription: 'Rolls-Royce Ghost是一款F級後輪駕駛轎車。全自動。汽油引擎從570到612馬力。 <br><br> 不提供汽車的外觀調整。那輛車不能賣' },
                    x6m2: { fullDescription: 'BMW X6 M III (F96) – SUV 5 DPI J級，四輪駕駛，自動。汽油引擎，功率從600馬力到625馬力。' },
                    s63cab: { fullDescription: '梅賽德斯-奔馳S級AMG III （ W222 ， C217 ）重塑– S級敞篷車，全輪驅動和後輪驅動。汽油引擎，功率從612馬力到630馬力' },
                    '63gls2': { fullDescription: '梅賽德斯-奔馳GLS AMG II （ X167 ） – SUV 5 dv。J級，四輪駕駛，自動。612馬力汽油引擎。' },
                    pts21: { fullDescription: '保時捷911 VIII （ 992 ）是一個S級隔室，後輪驅動和全輪驅動。機器人和機械人。汽油引擎的動力從385馬力到650馬力。 <br><br> 汽車的美麗調整是可用的。那輛車不能賣' },
                    continental: { fullDescription: 'Bentley Continental GT III是一款全輪驅動S級轎車。機器人。汽油引擎，功率從550馬力到635馬力。 <br><br> 提供車輛的外觀調整。那輛車不能賣' },
                    huracan: { fullDescription: 'Lamborghini Huracán – S級隔室，後輪和四輪駕駛。機器人。580至640馬力汽油引擎。' },
                    urus: { fullDescription: 'Lamborghini Urus – SUV 5 dv. J級，四輪駕駛，自動。汽油引擎，容量650馬力。 <br><br> 提供車輛的美麗調節。那輛車不能賣' },
                    asvj: { fullDescription: '不提供車輛的外觀調節。那輛車不能賣' },
                    g63amg6x6: { fullDescription: '梅賽德斯-奔馳G級AMG 6x6 –皮卡雙室J級，全輪駕駛。全自動。汽油引擎，功率爲544馬力。 <br><br> 車輛的美容調節可用。那輛車不能賣' },
                    cullinan: { fullDescription: 'Rolls-Royce Cullinan – SUV 5 dv. J級，四輪駕駛，自動。汽油發動機從571馬力到600馬力。 <br><br> 車輛有美容調節。那輛車不能賣' },
                    mp1: { fullDescription: 'McLaren P1 – S Class Coupe ，後輪驅動。機器人。737馬力混合引擎。 <br><br> 化妝車可調節。那輛車不能賣' },
                    chiron19: { fullDescription: 'Bugatti Chiron – купе S-класса, полный привод. Робот. Бензиновый двигатель мощностью 1500 лошадиных сил. <br><br> Косметический тюнинг автомобиля не предусмотрен. Автомобиль нельзя продать.' },
                    laferrari: { fullDescription: '法拉利拉法拉利是一款獨家S級轎車，限量499輛後輪驅動車。機器人。963馬力混合引擎。' },
                    // Вертолеты
                    maverickc: { fullDescription: 'Maverick雙葉直升機在加拿大製造。' },
                    sparrowc: { fullDescription: '還有什麼比直升機更有趣的？' },
                    froggerc: { fullDescription: '搭配封閉式尾扇的Frogger是今天市場上最安靜的直升機之一。' },
                    buzzard2c: { fullDescription: 'Buzzard是一架輕型巡航/觀察直升機。' },
                    supervolitoc: { fullDescription: 'SuperVolito最初是為山地救援和人道主義救援而設計，此後已經發現其真實目的是作為高科技高級管理者的必備飾品。' },
                    supervolito2c: { fullDescription: '傳奇的SuperVolito的Corbon版本。在高峯時段，在會議之間沒有更方便的方式，船上有香檳冰箱。' },
                    swiftc: { fullDescription: '一套特殊系列的輕型、兩引擎、四座多用途直升機。' },
                    volatusc: { fullDescription: '市面上最優雅的空氣動力、雙掃葉片、扇尾轉子：當你離開氯胺酮團隊建設工作坊時，沒有更昂貴的方式可以在完美舒適和接近完全沉默的情況下以高速行動。這是一個真正的商業階級。' }
                }
            }
        }
    },
    fractions: {
        tag: '[Фракция]',
        names: {
            lspd: '洛斯桑託斯警察局',
            ems: '急救醫療服務',
            shpd: '治安部',
            sang: '聖安德烈亞斯國民警衛隊',
            gov: '政府部門',
            wn: 'Weazel News',
            fib: '聯邦調查局',
            bls: '巴拉斯黑手黨',
            vgs: 'Los Santos Vagos',
            fam: 'The Families',
            blds: '血腥黑幫',
            mrbg: 'Marabunta Grande',
            lcn: 'La Cosa Nostra',
            rm: '俄羅斯黑手黨',
            yak: 'Yakuza',
            mex: '墨西哥卡特爾',
            lost: '失落的MC',
            aod: '死亡天使MC',
            im: '愛爾蘭黑手黨'
        },
        notInFraction: '你不是陣營成員',
        notInThisFraction: '你不是這個陣營的成員',
        fzLimit: '物料已達到上限！',
        ticketAmountError: '罰款金額可以是 ${min} 到 ${max}！',
        access: {
            invite: '加入組織',
            uninvite: '從機構中解僱',
            changeWarns: '發出警告',
            warehouse: '倉庫出入',
            craft: '使用工藝品',
            money: '取得基金餘額',
            safe: '使用安全保險箱',
            giverank: '更改玩家的排名',
            lic: '執照的發放',
            gnews: 'News Feed (/gnews)',
            wnews: '新聞供稿（/wnews ）',
            lockenter: '關閉登入',
            spawncars: '傳輸業務',
            capture: '在領土上發動戰爭',
            bizwar: '開始Podkvar',
            facilitywar: '企業戰爭',
            taxes: '稅收',
            bankheist: '銀行搶劫',
            doors: '門通道',
            armyCertificate: '軍人身分證件簽發',
            addVehicles: '向家庭移交交通工具',
            backVehicles: '從家庭返回的交通工具',
            award: '發放獎勵',
            orderMaterials: '下令提供材料',
            givemoney: 'A.資金',
            startContract: '拿走合同',
            manageRank: '排名管理',
            participateCapture: '參與說明'
        },
        deliveries: {
            orderMaterialsToArmy: '[{tag}] {name} 已下訂單送貨 $t({title}) ，金額為 {amount} 個。價格： ${price}。日期： {date}。',
            orderMaterialsToFraction: '[{tag}] {name} 已下單交付 $t({title}) ，金額爲 {amount} 件，價格： ${price}。'
        },
        ambulance: {
            deathInDimension: '你死在另一個次元，衛生兵無法接近你！',
            callSuccess: '你叫了救護人員，如果他們四分鐘內不來，你就會在醫院',
            resuscitationAvailable: '遊戲恢復的付款將可用 {amount}',
            earnFromResuscitation: '拯救玩家，你得到了 ${salary} 分',
            playerIllnes: '玩家患病： $t({title})',
            playerNoIllness: '玩家沒有生病',
            notHealedToExit: '你還沒痊愈就要出院了',
            healedTo: 'Вы восстановили здоровье до {amount}%'
        },
        army: {
            loadedMaterials: '你上載了物料，送到倉庫！',
            alreadyHaveMaterials: '您已有運送材料！',
            loadMaterialsNotAvailable: '目前無法上載內容！',
            unloadMaterialsNotAvailable: '卸載內容目前無法使用！',
            noMaterialsForUnload: '你沒有可以卸載的材料！',
            loadedMaterialsForFractions: '你上傳了材料，把它們送到國家結構！',
            loadedMaterialsGang: '您已上傳資料，請自行帶走',
            materialsInVehicle: '這個運輸工具已經載入了材料，請運送它們！',
            fractionBlips: '分數材料載入點在地圖上被標記！',
            warehouseFull: '你的倉庫已滿！',
            warehouseDeliverSuccess: '你已成功補充倉庫。當前倉庫： {amount} 物料',
            warehouseEmpty: '你的倉庫是空的！你什麼都不能送！',
            warehouseFullFractions: '倉庫 {title} 已滿！',
            warehouseDeliverSuccessFraction: '你已成功補充倉庫 {title}。當前倉庫： {amount} 種物料',
            armyWarehouseEmpty: '軍事倉庫是空的，你什麼都不能送',
            loadingMaterials: '物料加載'
        },
        biker: {
            attackedBy: '你被 {name}攻擊了！',
            attackFractions: '你的組織攻擊了 {name}！',
            couldntCapture: '組織 {name} 無法接管業務！',
            captureNew: '組織 {name} 能夠捕捉到一個新企業！',
            warAfterWarmup: '準備已經結束！戰爭開始了！',
            defendSuccess: '你的組織已經捍衛了企業！',
            defendFail: '您的組織未能接管業務。你的組織在企業戰爭中的號碼為0 ！',
            facilityLost: '你的組織失去了企業！',
            facilityCapture: '你的組織已接管了企業！',
            facilityDefendTimeout: '時間到了！你的組織已經捍衛了企業！',
            facilityAttackTimeout: '時間到了！你的組織未能抓住企業！',
            capturedAll: '組織 {name} 俘虜了所有領地！',
            facilityNames: {
                cementPlant: '水泥廠',
                unionPlant: '聯合工廠',
                oilRefinery: '煉油廠',
                oilRefinerySmall: '小型煉油廠',
                farm: '農場',
                oilDerrick: '油塔'
            },
            payout: '${amount} 支付企業控制！'
        },
        commands: {
            fcmd: '/f消息',
            ccmd: '/s消息',
            depcmd: '/dep消息',
            fbcmd: '/fb OOC消息',
            cbcmd: '/cb OOC消息',
            mcmd: '/m消息',
            markcmd: '/mark消息',
            gnewscmd: '/gnews消息',
            wnewscmd: '/wnews消息',
            advcmd: '/ADV消息',
            callAccepted: '您已接受呼叫。使用GPS駕駛',
            taxiDriverAccepted: '計程車司機 {name} 已接受您的呼叫。請稍候。',
            EMSAccepted: 'MED {name} 已接受您的呼叫。請稍候。',
            policeAccepted: '警察 {name} 接受了你的呼叫。',
            noRankAccess: '你的排名沒有權限！',
            sentToQueue: '你已將旅居提交至隊列！',
            canSetMarkIn: '你可以下注 {date}',
            sucmd: '/su ID號碼原因',
            'gnews-failed-count': '已有3條或更多的消息正在審核中',
            'gnews-success-send': '您已成功將訊息發送至GOV頻道以進行調整'
        },
        crime: {
            cantRobNow: '你不能再搶劫了！',
            robSuccess: '你成功搶劫了生意！',
            gotCash: '你得到了 ${amount}',
            robbery: '搶劫',
            robberyAt: '$t({title}) #{id}時的搶劫',
            robberyList: '以下是搶劫商家列表： {list}',
            shopWithKey: '$t({title}): {ids}'
        },
        elections: {
            startsFrom: '選舉將在 {date} : 10開始！',
            noInfo: '沒有選舉的情報！',
            inProcess: '選舉已經進行中。等待選舉結束！',
            candidateLength: '必須來自2位求職者！',
            oldDate: '不能是過去的日期！',
            lateCurrentDate: '目前的日期只能在早上10點之前選擇！',
            lateCurrentDate2: '您可以設定一個不晚於目前日期7天的日期！',
            createSuccess: '您已成功建立投票！',
            cantEditRunning: '您無法編輯已經開始的選擇！',
            editSuccess: '您已成功編輯投票！',
            noCurrentElections: '選舉現在沒有進行，或者選舉已經完成！',
            alreadyVoted: '你已經為這個或另一個角色投票了！',
            voteSuccess: '您已成功投下 {name}票！'
        },
        gang: {
            wfmStarted: '爭取材料的戰爭開始了！地圖上有戰爭的地點！',
            wfmEnded: '對材料的戰爭結束了！',
            materialsUnloaded: '您已成功爲垃圾填埋地補充了 {amount} 種材料',
            attackedBy: '你被 {name}攻擊了！',
            attackFractions: '你的幫派攻擊了 {name}！',
            couldntCapture: '幫派 {name} 無法佔領領土！',
            captureNew: '黑道 {name} 已經可以接管一個新的領土！',
            warAfterWarmup: '準備已經結束！戰爭開始了！',
            defendSuccess: '你的幫派保護了自己的領土！',
            defendFail: '你的手下沒能接手領土戰爭中，你的幫派數量為0 ！',
            territoryLost: '你的幫派已經失去領土！',
            territoryCapture: '你的幫派已經接管了！',
            territoryDefendTimeout: '時間到了！你的幫派已經捍衛了自己的領土！',
            territoryAttackTimeout: '時間到了！你的幫派無法佔領領土！',
            capturedAll: '黑道 {name} 已佔領所有領地！',
            materialsLoaded: '你浸入材料，把它們帶到宿舍！',
            enoughMaterials: '你已經有足夠的材料！',
            vehicleNoMaterials: '交通工具沒有材料！',
            vehicleOverloaded: '你的卡車溢出！',
            loadingMaterials: '物料加載',
            payout: '{fractionZones} 個領地已付費 ${amount} ！',
            startCapture: '{name} 已經開始奪取領土！',
            exitFromBigZone: '你還沒有返回俘虜區域，直到俘虜完成，才能返回',
            warningExitFromBigZone: '你有3秒鐘時間回到捕捉範圍'
        },
        mafia: {
            attackedBy: '你被 {name}攻擊了！',
            attackFractions: '你的黑手黨攻擊了 {name}！',
            couldntCapture: '黑手黨 {name} 不能接管生意！',
            captureNew: '黑手黨 {name} 已經接管了一個新生意！',
            warAfterWarmup: '準備已經結束！戰爭開始了！',
            defendSuccess: '你的黑手黨已經捍衛了自己的業務！',
            defendFail: '你的黑手黨沒能接管生意你的組織在商業戰爭中的數量為0 ！',
            businessLost: '你的黑手黨失去了生意！',
            businessCapture: '你的黑手黨已經接手了！',
            businessDefendTimeout: '你的黑手黨已經捍衛了自己的業務！',
            businessAttackTimeout: '你的黑手黨未能接管這個生意！',
            capturedAll: '黑手黨 {name} 已接管所有生意！',
            bizwarFractionInfo: '{name} {gender, select, 0{начал} 1{начала}} войну за бизнес на $t({title}) #{id}',
            payout: '控股生意支付 ${amount} ！'
        },
        events: {
            noCraftAccess: '你的排名不擁有製作能力！',
            noCraftAccessAtThisPoint: '你現在不能製造！',
            notNearCraft: '你距離工藝點太遠了！',
            craftItemIdError: '此道具目前無法製作！',
            craftRankItemIdError: '你的排名無法使用此道具的工藝品！',
            takeRankItemIdError: '你的排名無法存取此道具！',
            craftAccessRank: '此道具可從排名 {amount} 進行製作！',
            inDifferentOrganization: '你屬於另一個組織！',
            notEnoughMaterials: 'Для крафта нужно {amount1} материалов. У вас только {amount2}!',
            craftSuccess: 'Вы успешно скрафтили $t({title}) за {amount} материалов!',
            warnCantJoin: '你有一個警告，你不能加入組織！',
            warnCantJoinPlayer: '{name} 有警告。你不能加入組織！',
            joinSuccess: '您已加入 {name}',
            playerJoinSuccess: '{login} 成功加入 {name}',
            noUnInvitePermission: '你的排名不可能被解僱！',
            playerIsLeaderNoUninvite: '這個玩家是領袖，不能被解僱！',
            uninvitedFrom: '{name} 解僱了你出 {title}！',
            uninvitedPlayer: '你從組織發射了 {name} ！',
            jailExit: '你出獄了不要再犯法了！',
            noCuffs: '你沒有任何手銬！',
            cantInGreenZone: '你不能進入綠地區！',
            cantWhenWithWeapon: '你不能有一個玩家有槍！',
            cantWhenInPrison: '你不能讓一個玩家在監獄裡！',
            screedsNotAvailable: '每位玩家均無法使用刮板，嚇倒目標並擊敗目標！',
            noScreeds: '你沒有任何聯繫！',
            noKnife: '你沒有刀！',
            healedPlayer: '{name} 治愈了你',
            healedPlayer2: '你治好了 {name}',
            healedSuccess: '您已成功康復，可以離開醫院！',
            cantFineOrganizationVehicle: '你不能將陣營/工作交通工具送到罰款停車場！',
            carFineSuccess: '您已發送一輛車到停車場！',
            payFineSuccess: '你支付了罰款！',
            payFineSuccess2: '{name} 支付了罰款！',
            denyPayFine: '你拒絕交罰款！',
            denyPayFine2: '{name} 拒絕支付罰款！',
            tiedHands: '你的手被捆住了！',
            tiedHands2: '你把玩家的手捆起來了！',
            untiedHands: '你的手鬆開了！',
            untiedHands2: '你解開了玩家的手！',
            gaged: '你的嘴被封住了！',
            ungaged: '你嘴巴開了！',
            bagged: '你身上有麻袋！',
            unbagged: '你出袋子了！',
            wantedUpdated: '你已成功升級玩家想要的等級 {name} 。當前等級 {amount}',
            wantedUpdated2: '{name} 已更新您的通訊等級。目前等級 {amount}',
            recentlyCalledServices: '您最近呼叫了服務，請稍後再試！',
            taxiOrderAlreadyExists: '您已有一個有效的計程車呼叫！您可以在等待司機分配4分鐘後建立新的司機夥伴。',
            callServiceCancel: '通話已取消！',
            unidentifiablePlayer: '在遮罩中找不到玩家！',
            currentWantedLevel: '當前玩家想要的： {amount}',
            notWanted: '玩家不需要',
            noRankAccessCapture: '你的排名無法開始俘虜！',
            cantCaptureOwn: '你不能攻擊你的區域！',
            alreadyCapturing: 'Grab在路上了！',
            canCaptureIn: '捕捉可以開始 {date}',
            cantCaptureOwnFacility: '你不能襲擊你的生意！',
            cantCaptureOwnBusiness: '你不能襲擊你的生意！',
            cuffedBy: '{name} {gender, select, 0{надел} 1{надела}} на вас наручники!',
            unCuffedBy: '{name} {gender, select, 0{снял} 1{сняла}} с вас наручники!',
            tieBeforeEscort: '系好領帶開車！',
            notCuffed: '玩家沒有戴手銬！',
            markArrived: '你來這裡是為了增援！',
            playerGetWantedBy: '{name} {gender, select, 0{объявил} 1{объявила}} вас в розыск (Уровень: {amount}). Причина: {reason}',
            playerGetWantedByUpdate: '{name} {gender, select, 0{обновил} 1{обновила}} вам уровень розыска до {amount}. Причина: {reason}',
            startFacilityWar: '{name} 已經開始捕捉企業！',
            tookFromBalance: '{name} 取自資產負債表 ${amount}',
            putInBalance: '{name} пополнил баланс фракции на ${amount}',
            rankChange: '{fromName} {actionType, select, promotion{повысил} other{понизил}} {name} до {rank} ранга! Причина: {reason}',
            uninvite: '{fromName} 解僱 {name} / {fractionName}！原因： {reason}',
            lastWarn: '{fromName} 發出分數警告[3/3]並發出 {name}！原因： {reason}',
            warn: '{fromName} 發出分數警告[{fwarns}/3] {name}!原因: {reason}',
            removeWarn: '{fromName} 移除部分警告[{fwarns}/3] {name}!原因: {reason}',
            awardMoney: '{fromName} 給予 ${amount}的 {name} 獎勵！原因： {reason}',
            awardMoneyADMFamily: '[家庭： {org}] {fromName} 獎得 {name} 分，共 ${amount}分！原因： {reason}',
            awardMoneyADMFraction: '[FRACTION: {org}] {fromName} выдал {name} премию в размере ${amount}! Причина: {reason}',
            setTax: '{rank} {fromName} установил налоговую ставку в размере {value}% для $t({name})',
            unjail: '{fromName} 人出獄 {name} 人',
            giveweaponLic: '{fromName} 開具武器執照 {name}',
            takeweaponLic: '{fromName} 從 {name}取得武器執照',
            givefishingLic: '{fromName} 已發放捕撈執照 {name}',
            takefishingLic: '{fromName} 從 {name}取得捕魚執照',
            takedriveDLic: '{fromName} 從 {name}號取得D駕駛執照',
            takedriveMLic: '{fromName} 從 {name}取得Drive M執照',
            markAdded: '[{rank}] {name} 舉報事件： {text}',
            helpCall: '從 #{static}收到的呼叫，寫下「/接受 {id}」以接受呼叫。',
            suRange: '0到5個需要的等級！',
            acceptDispatch: '[Вызов] {name} 接受呼叫 #{id}！',
            returnClothes: '租衣服',
            gaveWantedLevel: '[Фракция]: {name} 以 {amount} {nameTo}的數量公布通緝名單',
            exitCuffed: '手銬退出'
        },
        supplyMaterials: {
            materialBoxGive: 'Выдан ящик на {amount} материалов',
            materialCollectionEnd: '材料收集完成！',
            supplyFinish: '{name} {gender, select, 0{доставил} 1{доставила}} {amount} шт. $t({title}) для {tag}. Поставка завершена.',
            supplyFinish2: '[ARMY] {name} {gender, select, 0{доставил} 1{доставила}} {amount} шт. $t({title}) для {tag}. Поставка завершена.',
            supplyLeft: '{name} {gender, select, 0{доставил} 1{доставила}} {amount} шт. $t({title}) для {tag}. Осталось: {rest} шт.',
            supplyLeft2: '[ARMY] {name} {gender, select, 0{доставил} 1{доставила}} {amount} шт. $t({title}) для {tag}. Осталось: {rest} шт.',
            materialsArrived: '材料交付到達 {name}',
            titles: {
                SSBukler: '集裝箱承運商SS Bulker',
                AircraftCarrrier: '國民警衛隊航空母艦',
                WalkerLogistics: 'Walker物流倉庫',
                HumaneLabs: '人性化實驗室和研究',
                PalmerTaylor: 'Palmer-Taylor TPP',
                RailwayStation: '火車站',
                SecureUnit: '倉庫安全單位',
                FishermanHouse: '湖畔的漁夫之家',
                RogersRecycling: '羅傑斯回收中心',
                CementFactory: '水泥廠',
                YouTool: '您的工具施工商店'
            }
        }
    },
    fun: {
        '8ball': {
            tableNotExists: '沒有這樣的桌子！',
            mustBeNearTable: '您應該靠近臺球桌！',
            alreadyRegistered: '你已經註冊了臺球！',
            joinSuccess: '您已加入遊戲，請稍候！',
            gameStarted: '遊戲開始了！',
            yourTurn: '你來打臺球',
            yourBalls: 'Ваши мячи - {type, select, STRIPED{Полосатые} other{Целые}}',
            scoredBlackBall: '{name} 得黑球，失敗了！',
            winner: '{name} победитель в бильярде!',
            gameEnd: '遊戲結束了！'
        },
        minigames: {
            cantJoinNow: '你現在不能加入！',
            registerSuccess: '您已成功註冊遊戲！請稍候！',
            deathmatch: {
                notStarted: '因為玩家數量不足，遊戲沒有開始！',
                gameEndedTitle: '遊戲結束了',
                gameEndedBody: '{name} 贏了比賽。',
                winner: '你是贏家，你的獎勵為 ${amount}！',
                timeLeft: '直到遊戲結束',
                kills: '兇案',
                outOfZoneTitle: '區域外',
                outOfZoneBody: '回到 ~y~遊戲區~s~，你有 {amount} 秒鐘。',
                firstPlace: '第一名： {amount} 人死亡',
                secondPlace: '第二名： {amount} 人死亡',
                thirdPlace: '第三名： {amount} 人死亡'
            },
            onRepair: '迷宮銀行競技場正在維修中！',
            shooterGames: {
                armsGame: {
                    title: '武器遊戲',
                    description: '殺戮並升級。優勝者是首先獲得謀殺次數上限的人。'
                },
                revolverGame: {
                    title: '武器遊戲',
                    description: '用左輪手槍殺死並升級。優勝者是首先獲得謀殺次數上限的人。'
                },
                teamClash: {
                    title: '戰鬥指令',
                    description: '兩個隊伍會合。在贏得戰鬥前獲得所需數量的隊伍。'
                },
                duel1: {
                    title: '1 x 1決鬥',
                    description: '以一對一決鬥進行比賽。模式：最好的3'
                },
                duel3: {
                    title: '決鬥3 x 3',
                    description: '比賽3對3決鬥。模式：最好的5'
                }
            }
        },
        seasonEvents: {
            xmas: {
                snowmanCollection: '這個雪人正在被別人集合！',
                collectedSnowball: '你已成功收集到雪球！把它拿出來，玩得開心！',
                giftTitles: {
                    weapon: '武器 $t({title})',
                    vehicle: '車輛 {title}'
                },
                cantOpenNow: '禮物只能在12月25日至1月8日開放！',
                noPresentToClaim: '其餘禮物將在接下來的日子提供！',
                presentClaimed: '您已成功完成 $t({title})！在存貨裡打開它！',
                minLevelRequired: '接受禮物的最低等級： {level}',
                maxSnowmans: '您已建造了最大數量的雪人！',
                creatingSnowman: '造雪人'
            }
        },
        armwrestling: {
            playerLeft: '對手退出遊戲，遊戲停止！',
            youWin: '恭喜你！你贏了！',
            youLose: '你輸了！',
            noSpace: '沒有可用的座位！'
        },
        bets: {
            winBets: '您已從投注辦公室的投注中受益！成功投注： {totalWinCount}.中獎： ${totalWinAmount}',
            winBetsWithReturn: '您已從投注辦公室的投注中退款！退款投注數： {totalWinCount}。退款金額： ${totalWinAmount}',
            tempDisabled: '博彩公司的辦公室暫時無法使用！',
            betRange: '出價金額可以從 ${from} 到 ${to}！',
            noEvent: '沒有這樣的東西！',
            cantMakeBet: '無法下注這場比賽！',
            alreadyMadeBet: '你已經在這場比賽中下注了這種類型的活動！',
            madeBetSuccess: '您已成功下注比賽 {team1} - {team2}， {category} ({description}) - ${amount}，賠率： {odds}',
            types: {
                win: '比賽贏家',
                half1: '上半場贏家',
                half2: '第二半勝利者',
                total: '總計',
                both: '都會得分',
                both1: '二者均得分（一半）',
                both2: '二者均得分（下半年）'
            }
        },
        casino: {
            blackjack: {
                seatOccupied: '這個座位已經就位了！',
                notBehindTable: '你不在桌子上！',
                youLost: '你失去了 ${amount}！',
                youWin: '你贏了 ${amount}！',
                draw: '你打平了，你拿回你的賭注。',
                time: '時間(S)',
                betSum: '出價金額',
                thisBet: '此出價',
                cardsInfo: 'У вас [{myHandCards}] {isMySplit, select, 1{и [{myHandCards_split}]}}, У диллера [{dealerHand}]'
            },
            horserace: {
                alreadyMadeBet: '您已下注！',
                madeBetSuccess: '你下注 ${amount} 在馬#{horse}',
                youLost: '你失去了 ${amount}！',
                youWin: '你贏了 ${amount}！',
                youWinOutSide: '你的馬贏了比賽！你得到了 ${amount}！',
                seatOccupied: '這個座位已經就位了！'
            },
            luckywheel: {
                seatOccupied: '這個座位已經就位了！',
                canRollIn: '你可以旋轉運輪 {leftTime}',
                canRollLevel: '你可以從 {level} 級轉動運氣輪！',
                winCar: '你已贏得運輸 {title}！你可以在Donat的辦公室找到他!',
                winMoney: '你贏了 ${award}！',
                winClothes: '您贏得了品牌服裝！',
                winDonateItem: '你贏了30天 {title} ！你可以在Donat辦公室找到獎品！',
                winReRoll: '你贏得了再次旋轉輪子的機會！',
                winMCoins: 'Вы выиграли {amount} MC!'
            },
            poker: {
                seatOccupied: '這個座位已經就位了！',
                betOutOfLimits: '出價超出限制！',
                playerWin: '{name} 以 {hand}勝 ${amount} 分！',
                divideBank: '{winnerNames} 除以彼此的 ${amount} 之和！'
            },
            roulette: {
                seatOccupied: '這個座位已經就位了！',
                youLost: '你失去了 ${amount}！',
                youWin: '你贏了 ${amount}！',
                time: '時間(S)',
                betSum: '出價金額',
                betsLeft: '剩餘出價',
                thisBet: '此出價'
            },
            slots: {
                seatOccupied: '這個座位已經就位了！',
                youLost: '你失去了 ${amount}！',
                youWin: '你贏了 ${amount}！'
            }
        },
        cinema: {
            adminSkipBlock: '管理員已封鎖跳過影片的功能！',
            adminAddBlock: '管理員已封鎖上傳影片的功能！',
            skipVote: '你投了通行證！',
            skippingVideo: '投票得分爲 {amount} 空白。以下影片已開啟...',
            wrongUrl: '無效的連結格式！',
            videoLimit: '不超過兩部影片！',
            addQueueSuccess: '您已成功排列您的影片！'
        },
        club: { buySuccess: '您已成功購買 $t({title}) 為 ${amount}' },
        dice: {
            maxPerBet: '您可以一次將最大值設定為 ${maxDiceBet} ！',
            sentRequest: '你答應 {name} 在 ${amount}上玩骰子',
            gameDecline: '{name} отказался от игры!',
            diceWin: '{name1} 回合 {dice1}， {name2} 回合 {dice2}。你贏了！',
            diceLose: '{name1} 回合 {dice1}， {name2} 回合 {dice2}，你輸了！',
            diceDraw: '{name1} 落 {dice1}， {name2} 落 {dice2}，平局！'
        },
        golf: {
            playerLeftGame: '{name} 退出比賽',
            leftGame: '你已經離開比賽了！',
            ballResetWater: '由於進入水中，球已返回到最後的位置。',
            ballResetAny: '球恢復到最後的位置。',
            ballResetZone: '由於離開高爾夫區，球回到最後的位置。',
            ballInHole: '玩家 {name} 的球打到洞口。',
            ballNextPlayer: '現在球打到 #{static} {name}！',
            youWin: '你已贏得一場高爾夫球賽，獎品為 ${bank}',
            toExitHint: '要離開遊戲，只需走出競技場即可',
            alreadyRegistered: '您已注冊！',
            maxMembers: '此遊戲已經有最大的玩家數量！',
            cantJoinNow: '你現在不能加入！',
            registerSuccess: '您已成功註冊遊戲！請稍候！',
            onRepair: '高爾夫球杆正在翻新中！'
        },
        piano: { seatOccupied: '這個座位已經就位了！' },
        graffiti: {
            nearMax: '此區域已達到最大塗鴉量',
            nearHasOwn: '在這個區域，你已經使用了塗鴉',
            noItem: '你手裏沒有罐子'
        }
    },
    autoschool: {
        theoryPass: '你已經成功通過理論。下一步-練習',
        theoryFail: '你沒有通過理論測試。請再試一次！',
        cancelTheory: '你已決定不參加測試！',
        practicePass: '您已成功通過實習，並獲得 {title}類權利！',
        practiceFail: '你沒有通過練習試驗！'
    },
    flyingschool: { skillLearned: '你已經學到了一部分技能！' },
    goverment: {
        getLicSuccess: '您已成功獲得許可證 {category}！',
        notInFraction: '你不是陣營成員！',
        alreadyHaveIDCard: '您已有ID ！',
        getFractionIDCardSuccess: '你已成功收到你的陣營身份證明文件！'
    },
    stripclub: { danceInProgress: '私人舞蹈正在進行中！' },
    ipad: {
        noIpad: '你沒有平板電腦！在商店裏購買。',
        noBusiness: '你沒有生意。',
        noRankForBalance: '你的排名無法使用餘額！',
        noRankForSafe: '你的排名無法使用保險箱！',
        noRankForBackVehicles: '你的排名無法從家族中返回運輸工具！',
        noAccessForManageRank: '你的排名沒有排名控制！',
        cannotRemoveLeaderRank: '排行榜排名不能刪除！',
        cannotRemoveFirstRank: '無法刪除第一名！',
        rankName2to24: '等級名稱必須在2至16個字元之間！',
        rankNameAlreadyExists: '您的家族中已存在同名的排名！',
        notMore30Ranks: '不能創建超過30個排名！',
        inputError: '輸入的資料不正確！',
        noSumInBalance: '餘額上沒有這樣的金額！',
        tookFromBalance: '您已從餘額中扣除 ${amount} ！',
        putInBalance: '您已向餘額中添加 ${amount} ！',
        noAccess: '您無法使用此功能！',
        giveRankReasonLength: '原因必須在2至45個字元之間！',
        cantGiveForThisFractions: '無法為這個陣營發佈！',
        sumRange: '金額必須介於 ${amount1} 和 ${amount2}之間',
        errorSum: '金額錯誤！',
        commentLength: '批注長度必須介於 {from} 至 {to} 個字元之間！',
        noMoneyOnFractionBalance: '資產負債表上沒有這麼多資金！',
        financeLimit: '{title} 已達到收到的資金額上限。下周一重置！',
        financeLimitLeft: '陣營資金額度的剩餘限制為 {title}: ${financingAmount}。下周一重置！',
        financeSuccess: '你已經為陣營 {title} 提供了 ${amount}的資金！原因： {comment}',
        cantApplyWithYours: '你不能用在自己身上',
        notInYourFraction: '這個玩家不是你的陣營成員！',
        cantChangeWarnsToLeader: '你不能變更對領袖的警告數量！',
        cantManageWarnByRank: '你無法管理排名高於你的玩家的警告！',
        noWarns: '玩家沒有陣營警告！',
        rankNotExists: '沒有這樣的排名！',
        cantSetLeaderRank: '你不能設定領袖級別！',
        canManageOnlyLowRanks: '您只能更改排名低於您的玩家的排名！',
        cantHigherMyRank: '排名不能高於你！',
        setRankError: '設定排名時發生錯誤！',
        cantChangeLeaderRank: '你不能變更領袖的排名！',
        manageRank: '{name} {actionType, select, promotion{повысил} other{понизил}} вам ранг до {rank}! Причина: {reason}',
        cantUninviteLeader: '你不能解僱領袖！',
        cantUninviteByRank: '你無法解僱排名高於你的玩家！',
        playerUninvited: '{name} 解僱了你從 {title}！原因： {reason}',
        warnWithUninvite: '{name} 給予你陣營警告[3/3]並解僱！原因： {reason}',
        warnPlayer: '{name} выдал вам фракционное предупреждение [{fwarns}/3]! Причина: {reason}',
        removeWarnPlayer: '{name} 解除了你的陣營警告[{fwarns}/3] ！原因： {reason}',
        bonusLimit: '您已達到發放的獎勵數量上限。下周一重置！',
        bonusLimitSum: '您已達到發放的獎勵金額上限。下周一重置！',
        bonusLimitSumLeft: '剩餘金額限制： ${amount}。下周一重置！',
        bonusSuccess: '{name} 給了你 ${amount}的獎勵！原因： {reason}',
        bonusGiveSuccess: '您已發放 ${amount}的 {name} [#{memberId}]獎勵！原因： {reason}',
        nextBonusForPlayer: 'Следующую премию этот игрок сможет получить {date}!',
        setTaxSuccess: '{rank} {name} установил налоговую ставку в размере {amount}% для $t({title})',
        businessAlreadyPaid: '商家已提前 {amount} 天付款！',
        cantPayBusinessMoreThan: '您不能提前 {amount} 天以上支付商務費用！',
        paidBusinessSuccess: '您已成功支付 ${price} 至 {date}的營業稅。',
        successTakeMoneyFromBusiness: 'Вы успешно сняли ${amount} {type, select, cash{с кассы} other{со счёта}} бизнеса.',
        noMoreThanCount: 'Может быть не больше {maxCount, select, 1000000{1 000 000 литров} other{{maxCount} шт}}.!',
        orderPriceNoMoney: '訂單金額： ${amount}。您沒有足夠的現金！',
        orderSuccess: 'Вы успешно заказали доставку {count}шт. {category} за ${amount}.',
        orderSuccess2: 'Вы успешно заказали доставку {count} материалов за ${amount}.',
        orderSuccess3: 'Вы успешно заказали доставку {count} {type, select, 1{шт.} other{литров}} $t({title}) за ${amount}.',
        orderSuccessSim: 'Вы успешно заказали доставку {count} материалов для обслуживания мобильной связи за ${amount}.',
        cantChangeLotteryPrice: '無法更改彩票價格！',
        canSetOnlyPriceRange: '您可以爲此產品設定 ${minPrice} 至 ${maxPrice}的價格',
        successSetItemPrice: '您已成功將價格設定為 $t({title}) 至 ${price}。',
        successSetItemPrice2: 'Вы успешно установили цену на $t({title}) в размере {price}% от закупочной.',
        successSetVehiclePrice: '您已成功將價格設定為 {title} 至 ${price}。',
        noHome: '你沒有家！',
        noApartment: '你沒有公寓！',
        notInhabitated: 'Этот игрок не засёлен в {type, select, house{ваш дом} other{вашу квартиру}}',
        homeOwnerEvicted: 'Владелец дома №{houseId} {name} выселил вас.',
        apartmentOwnerEvicted: 'Владелец квартиры №{apartmentId} {name} выселил вас.',
        evictedSuccess: 'Вы выселили из {type, select, house{своего дома} other{своей квартиры}} {name}.',
        houseAlreadyPaid: '{type, select, house{Дом уже и так оплачен} other{Квартира уже и так оплачена}} на {amount} дней вперёд!',
        cantPayHouseMoreThan: 'Нельзя оплатить {type, select, house{дом} other{квартиру}}, больше, чем на {amount} дней вперёд!',
        paidHouseSuccess: 'Вы успешно оплатили налог на {type, select, house{дом} other{квартиру}} в размере ${price} до {date}.',
        noRepairKit: '你沒有工具包！',
        changeWarehouseKeySuccess: '您已成功更改食品庫鎖！',
        maxMaterials: '倉庫最多可容納 {amount} 個！',
        noFractionMoneyToPayMaterials: '陣營餘額中沒有足夠的資金來支付材料！',
        supplyAlreadyOrdered: '這種類型的材料已經下令交貨！',
        settingsOnlyForLeader: '此設定只能由領導者變更！',
        advSuccess: 'Вы успешно разместили объявление на билборде на {length} дн. за ${price}. Необходимо дождаться проверки содержимого объявления.',
        financeFraction: '{name} 個資助陣營 {fractionName} ，金額為 ${amount}！原因： {comment}',
        financeFractionADM: '[分數： {tag}] {name} 個資助陣營 {fractionName} ，金額為 ${amount}！原因： {comment}',
        storeTake: '我拿走了',
        storePut: '放下',
        storeCraft: '手工制作',
        gotFinancing: '從 {fraction}開始提供資金',
        financing: '資金 {fraction}',
        award: '{name} [#{staticId}]的獎勵',
        fired: '已解散'
    },
    admin: {
        sysadmin: 'ADM: {text}',
        savePosError: 'SavePos - Error: {message}',
        savePosSuccess: 'SavePos -物品已保存： {message}',
        godmodeEnabled: '你已進入神模式！',
        godmodeDisabled: '你出神模式了！',
        kickedFromVehicleByAdmin: '管理員 {name} 把你扔下車！',
        kickedFromFractionByAdmin: '管理員 {name} 將你從陣營中解僱！',
        giveVehicle: '管理員 {name} 給了你交通工具 {model}',
        reportTakenByAdmin: '此報告已被其他管理員採用！',
        ticketHandled: '工單已被處理！',
        billboardAdGranted: '廣告牌#{billboardId} 的廣告 {title} 已被批準。',
        billboardAdDenied: '廣告牌#{billboardId} 的廣告 {title} 已被拒絕。已退款。',
        billboardCreated: 'Игрок хочет разместить рекламное объявление на билборде №{boardId}<br><br>{title}<br>{description}',
        gnewsAccepted: '您的房源 {message} 已被批準。',
        gnewsCanceled: '您的房源 {message} 已被拒絕。',
        savecamPosError: 'SaveCamCoords -錯誤： {message}',
        savecamPosSuccess: 'SaveCamCoords -商品已保存： {message}',
        familyAccepted: '您已成功接受家庭Logo',
        familyDeclined: '您已成功拒絕家族標誌',
        bigTransfer: '大額轉帳！ [{level} 級] [#{id}] {name} ${amount} {type} $t({comment})',
        changeName: '{prevName} #{static} 更改了名稱爲 {name}',
        notAdmin: '您不是管理員！',
        usef2: '使用F2 -聯系人',
        cheats: {
            teleport: '{name} [{id}] #{static} 疑似瞬移！ {dist}米，工作： {status}，在車上： {inVehicle}',
            teleport2: '{name} [{id}] #{static} 疑似瞬移！ {dist}米， {var2}， {var3}，工作： {status}，在車上： {inVehicle}',
            noclip: '{name} [{id}] #{static} подозрение на полёт! РАБОТА: {status}, В АВТО: {inVehicle}. Введите /noclip {id}, чтобы не показывать уведомление по этому игроку.'
        },
        report: {
            from: '[ADMIN] ЖАЛОБА от {name}[{id}] #{static}: {message}',
            reply: '[ADMIN] {name}[{id}]回復 {toName}[{toId}]: {message}',
            replyPlayer: '回復者 {name}[{id}]: {message}',
            yourReport: '您的投訴： {message}'
        },
        fuel: {
            usage: '錯誤！/fuel vehicleId fuelAmount',
            invalidAmount: '錯誤！ 0至 {amount} 升！',
            setSuccess: '您已成功在車輛 {title}中安裝 {amount} 升燃油'
        },
        settime: {
            usage: '錯誤！/決定時間分鐘',
            rangeHours: '錯誤！時間必須介於 {min} 至 {max} 小時之間！',
            rangeMinutes: '錯誤！時間必須介於 {min} 到 {max} 分鐘之間！',
            rangeSeconds: 'Ошибка! Время должно быть от {min} до {max} секунд!',
            log: '{name} #{id} 設定時間為 {hours}:{minutes}:{seconds}'
        },
        resettime: { log: '{name} #{id} 復位時間' },
        setweather: {
            usage: '錯誤！/setweather weatherId',
            range: '錯誤！天氣必須介於 {min} 和 {max}之間！',
            log: '{name} #{id} 將天氣設置為 {weather}'
        },
        clear: {
            usage: '錯誤！/clear staticId原因',
            mustBeOffline: '錯誤！玩家必須離線！',
            playerNotFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 重置玩家 #{accountId} {reason}'
        },
        givedonate: {
            usage: '錯誤！/givedonate accountId金額原因',
            range: '錯誤！金額必須介於 {min} 和 {max}之間！',
            log: '{name} #{id} 給予玩家 #{accountId} {amount} MCoins for {reason}'
        },
        givemoney: {
            usage: '錯誤！/givemoney accountId金額原因',
            range: '錯誤！金額必須介於 {min} 和 {max}之間！',
            log: '{name} #{id} 給予玩家 #{accountId} ${amount} 現金 {reason}'
        },
        takemoney: {
            usage: '錯誤！/takeemoney accountId金額原因',
            range: '錯誤！金額必須介於 {min} 和 {max}之間！',
            log: '{name} #{id} 從玩家 #{accountId} 提取 ${amount} 現金 {reason}',
            lessThanHave: '錯誤！玩家的數量少於你想要奪走的數量！'
        },
        takebank: {
            usage: '錯誤！/takebank accountId金額原因',
            range: '錯誤！金額必須介於 {min} 和 {max}之間！',
            log: '{name} #{id} 從玩家 #{accountId} 的底池中取出 ${amount} {reason}',
            noBankAccount: '錯誤！玩家沒有銀行帳戶！',
            lessThanHave: '錯誤！玩家的數量少於你想要奪走的數量！'
        },
        gh: {
            usage: '錯誤！/gh playerId',
            notFound: '錯誤！找不到玩家！',
            notAuthed: '錯誤！玩家未登入！',
            log: '{name} #{id} 瞬移 {targetName} [{targetId}]'
        },
        getcar: {
            usage: '錯誤！/getcar vehicleId',
            notFound: '錯誤！找不到車輛！',
            log: '{name} #{id} 傳送車輛到自己身邊 {vehicleId} {vehicleTitle}'
        },
        tpcar: {
            usage: '錯誤！/tpcar vehicleId',
            notFound: '錯誤！找不到車輛！',
            log: '{name} #{id} телепортировался к автомобилю {vehicleId}'
        },
        incar: {
            usage: '錯誤！/incar vehicleId seatId',
            notFound: '錯誤！找不到車輛！',
            occupied: '錯誤！有人坐在運輸車裡！',
            log: '{name} #{id} 傳送到車輛 {vehicleId} 到座位 {seatId}'
        },
        tp: {
            usage: '錯誤！/tp playerId',
            notFound: '錯誤！找不到玩家！',
            notAuthed: '錯誤！玩家未登入！',
            log: '{name} #{id} 已傳送到 {targetName} [{targetId}]'
        },
        gtp: { usage: '錯誤！您沒有啟用GPS導航器！' },
        toggleachat: {
            enabled: '您已啓用聊天可見性',
            disabled: '您已禁用聊天可見性'
        },
        hidecheatinfo: {
            enabled: '您已啓用作弊信息可見性',
            disabled: '您已禁用作弊信息的可見性'
        },
        a: {
            usage: '錯誤！/一個文字',
            disabled: '您已禁用聊天功能！'
        },
        gw: {
            usage: 'Error!/gw playerId weaponName ammo',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 把武器 {weaponName} 送到玩家 #{targetId}'
        },
        save: { log: '{name} #{id} 保留位置 {title} - {x}、 {y}、 {z}、 {rot}' },
        skin: {
            usage: '錯誤！/skin playerId skinName',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 更改皮膚玩家 #{targetId} 至 {skinName}'
        },
        o: { usage: '錯誤！/o文字' },
        excar: {
            usage: '錯誤！/excar vehicleId',
            notFound: '錯誤！找不到車輛！',
            log: '{name} #{id} 炸掉 {vehicleId}輛車'
        },
        payday: { log: '{name} #{id} 呼叫PayDay' },
        asms: { usage: '錯誤！/asms playerId文字' },
        alock: { notFound: '錯誤！找不到附近的車輛！' },
        aveh: {
            notFound: '錯誤！找不到附近的車輛！',
            log: '{name} #{id} 創建了車輛 #{vehicleId} {vehName}！原因 {reason}'
        },
        kick: {
            usage: '錯誤！/kick playerId原因',
            notFound: '錯誤！找不到玩家！',
            cantKickAdmin: '錯誤！您無法點擊管理員！',
            kickedByAdmin: '您已被管理員踢出 {name}！原因： {reason}',
            log: '{name} #{id} 從伺服器 #{targetId}點頭！原因 {reason}'
        },
        skick: {
            usage: '錯誤！/skick playerId原因',
            notFound: '錯誤！找不到玩家！',
            cantKickAdmin: '錯誤！您無法點擊管理員！',
            log: '{name} #{id} 從伺服器 #{targetId}悄悄點頭！原因 {reason}'
        },
        warn: {
            usage: '錯誤！/警告staticId原因！',
            notFound: '錯誤！找不到玩家！',
            cantWarnAdmin: '錯誤！您無法發出管理員警告！',
            logBan: '{name} #{id} забанил {playerOnline, plural, =1{} other{оффлайн}} #{staticId} на {amount} дней! Причина: {reason}',
            bannedByAdmin: '您因收到第二次警告而被管理員 {name} #{id} 禁止',
            log: '{name} #{id} выдал {playerOnline, plural, =1{} other{оффлайн}} предупреждение #{staticId}. Причина: {reason}',
            warnedByAdmin: '您已收到管理員 {name} #{id}的警告。原因: {reason}'
        },
        unwarn: {
            usage: '錯誤！/unwarn staticId原因',
            notFound: '錯誤！找不到玩家！',
            noWarns: '錯誤！玩家沒有警告！',
            log: '{name} #{id} 從 #{staticId} 警告中移除。原因： {reason}'
        },
        mute: {
            usage: '錯誤！/mute staticId分鐘原因',
            notFound: '錯誤！找不到玩家！',
            range: '錯誤！時間必須介於 {min} 到 {max} 分鐘之間！',
            muted: '{name} 給了你 {minutes} 分鐘的MUT。原因 {reason}',
            log: '{name} #{id} 發行MUT #{staticId}.原因： {reason}',
            log2: '{name} #{id} выдал мут #{staticId} на {amount} минут. Причина: {reason}'
        },
        unmute: {
            usage: '錯誤！/unmute staticId原因',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} снял {playerOnline, plural, =1{} other{оффлайн}} мут с #{staticId}. Причина: {reason}'
        },
        unban: {
            usage: '錯誤！/unban staticId原因',
            notFound: '錯誤！找不到玩家！',
            notBanned: '錯誤！玩家並未被禁止！',
            notBannedByYou: '你沒有禁止這個玩家！只有發佈禁令的人或高級管理員才能解除禁令',
            log: '{name} #{id} 解禁 #{staticId}.原因： {reason}'
        },
        ban: {
            usage: '錯誤！/ban staticId天原因',
            notFound: '錯誤！找不到玩家！',
            range: '錯誤！時間必須介於 {min} 和 {max} 天之間！',
            cantBanAdmin: '錯誤！你不能禁用管理員！',
            alreadyBanned: '錯誤！玩家已被禁止！',
            log: '{name} #{id} выдал бан {playerOnline, plural, =1{} other{оффлайн}} #{staticId} на {amount} дней! Причина: {reason}',
            log2: '{name} #{id} 下達禁令 #{staticId}!原因: {reason}',
            bannedByAdmin: '您已被管理員禁止 {name} #{id}.原因: {reason}'
        },
        hardban: {
            usage: '錯誤！/hardban staticId天原因',
            range: '錯誤！時間必須介於 {min} 和 {max} 天之間！',
            notFound: '錯誤！找不到玩家！',
            cantBanAdmin: '錯誤！你不能禁用管理員！',
            alreadyBanned: '錯誤！玩家已被禁止！',
            bannedByAdmin: '您已被管理員禁止 {name} #{id}.原因: {reason}',
            log: '{name} #{id} 下達禁令 #{staticId}!原因: {reason}',
            log2: '{name} #{id} выдал {playerOnline, plural, =1{} other{оффлайн}} хардбан #{staticId} на {amount} дней! Причина: {reason}'
        },
        setwind: { usage: 'Error!/setwind level' },
        hp: {
            usage: '錯誤！/hp id金額',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 發放 {amount} 個衛生單位 {targetName} #{targetId}'
        },
        makeleader: {
            usage: '錯誤！/makeleader staticId fractionId',
            notFound: '錯誤！找不到玩家！',
            alreadyHasLeader: '這個陣營已經有領袖了！',
            alreadyLeader: '這個玩家已經是領袖了！',
            notLeader: '這個玩家不是領袖！',
            uninvitedFromLeader: '{name} #{id} 解僱了你的領導者！',
            loguninvite: '{name} #{id} 從首席 {title}的位置開除了一名玩家 {targetName} #{targetId}',
            madeLeader: '{name} #{id} 使你成為陣營 {title}的領袖！',
            loginvite: '{name} #{id} 使玩家 {targetName} #{targetId} 成爲陣營 {title}的領袖'
        },
        templeader: {
            usage: 'Error!/templeader fractionId',
            left: '你離開了臨時領袖！',
            logLeft: '{name} #{id} 離開了臨時領導者',
            join: 'Вы сделали себя временным лидером фракции №{id} - {title}!',
            logJoin: '{name} #{id} сделал себя временным лидером фракции №{fractionId} - {title}!'
        },
        tempfamily: {
            usage: 'Error!/tempfamily familyId',
            left: '你離開了臨時的家族領袖！',
            logLeft: '{name} #{id} 離開了臨時家族首領',
            join: 'Вы сделали себя временным лидером семьи №{id} - {title}!',
            logJoin: '{name} #{id} сделал себя временным лидером семьи №{fractionId} - {title}!'
        },
        inv: {
            enabled: '你已經進入消失。我看不到你！',
            disabled: '你消失了'
        },
        chide: {
            enabled: '您已輸入發票。作弊者不會注意到你，但你無法在聊天室中交談！聽聽球員的聲音！',
            disabled: '你從作弊者那裏得到的便條。'
        },
        repair: {
            noVehicle: '錯誤！你必須在車內或附近！',
            success: '您已成功修復auto {id}！'
        },
        spec: {
            usage: '錯誤！/spec playerId',
            notFound: '錯誤！找不到玩家！'
        },
        ajail: {
            usage: '錯誤！/ajail staticId分鐘原因',
            notFound: '錯誤！找不到玩家！',
            range: '錯誤！時間必須介於 {min} 到 {max} 分鐘之間！',
            log: '{name} #{id} посадил игрока {playerOnline, plural, =1{} other{оффлайн}} #{targetId} в тюрьму на {minutes} минут. Причина: {reason}',
            log2: '{name} #{id} посадил игрока {playerOnline, plural, =1{} other{оффлайн}} #{targetId} в тюрьму. Причина: {reason}',
            jailed: '{name} #{id} посадил вас в тюрьму на {minutes} минут. Причина: {reason}'
        },
        unjail: {
            usage: '錯誤！/unjail staticId',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} выпустил  {playerOnline, plural, =1{} other{оффлайн}} игрока #{targetId} из тюрьмы'
        },
        tph: {
            usage: '錯誤！/tph houseId',
            notFound: '錯誤！找不到房子！',
            success: '您已成功傳送到主頁 #{id}！'
        },
        tpatm: {
            usage: '錯誤！/tpatm atmId',
            notFound: '錯誤！找不到ATM ！',
            success: '您已成功傳送到ATM #{id}！'
        },
        ctp: { usage: '錯誤！/ctp posX posY posZ' },
        setdim: {
            usage: '錯誤！/setdim playerId維度',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 將玩家 #{targetId} 虛擬世界設置為 {dimension}'
        },
        acuff: {
            usage: '錯誤！/acuff playerId',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 手銬玩家 #{targetId}'
        },
        auncuff: {
            usage: 'Error!/auncuff playerId',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 取下玩家 #{targetId}的手銬'
        },
        setskill: {
            usage: '錯誤！/setskill playerId skillName值',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 在 {value}上把技能 {skillName} 到 #{targetId}'
        },
        setlevel: {
            usage: '錯誤！/setlevel playerId level',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 設置玩家等級 #{targetId} 至 {level}'
        },
        givelic: {
            usage: '錯誤！/givelic playerId licName',
            notFound: '錯誤！找不到玩家！',
            notFoundLic: '錯誤！找不到授權！',
            alreadyHasLic: '錯誤！玩家已經有此授權！',
            log: '{name} #{id} 執照 #{targetId} 玩家 {licName}'
        },
        takelic: {
            usage: '錯誤！/takeelic playerId licName',
            notFound: '錯誤！找不到玩家！',
            notFoundLic: '錯誤！找不到授權！',
            notHasLic: '錯誤！玩家沒有此授權！',
            log: '{name} #{id} 拿走了玩家 #{targetId} 的執照 {licName}'
        },
        tempname: {
            usage: '錯誤！/tempname名稱',
            success: '您已成功設定臨時名稱 {name}。在返回伺服器之前，您會收到它',
            log: '{name} #{id} 更改了名稱爲 {tempName}'
        },
        resettempname: {
            success: '您已成功重置臨時名稱',
            log: '{name} #{id} 重置臨時名稱'
        },
        esp: {
            enabled: '您已成功啓用ESP',
            disabled: '您已成功禁用ESP'
        },
        makeadmin: {
            usage: '錯誤！/makeadmin staticId adminLevel',
            range: '錯誤！管理員等級必須介於 {min} 和 {max}之間',
            notFound: '錯誤！找不到玩家！',
            higherAdmin: '錯誤！管理員排名高於你！',
            log: '{name} #{id} сделал {playerOnline, plural, =1{} other{оффлайн}} игрока #{targetId} админом уровня {level}'
        },
        rescue: {
            usage: '錯誤！/rescue playerId',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 復活玩家 #{targetId}'
        },
        setprice: {
            usage: '錯誤！/setprice bizType bizId itemId價格',
            priceLimit: '錯誤！價格從0.1美元到25,000美元！',
            itemNotFound: '錯誤！找不到具有此ID的項目！',
            onlyBiz: '錯誤！只有ammo_shop、item_shop、mobile_shop ！',
            log: '{name} #{id} установил цену на {title} для {bizSelect, select, =all{всех {bizType}} other{{bizType} №{bizId}}} в размере ${price}'
        },
        setwarehouse: {
            usage: '錯誤！/setwarehouse bizType bizId itemId金額',
            amountLimit: '錯誤！數量從0到10000 ！',
            itemNotFound: '錯誤！找不到具有此ID的項目！',
            onlyBiz: '錯誤！只有ammo_shop、item_shop、mobile_shop ！',
            log: '{name} #{id} Установил количество товаров {title} для {bizSelect, select, =all{всех {bizType}} other{{bizType} №{bizId}}} в размере {price} шт.'
        },
        giveitem: {
            usage: '錯誤！/giveitem playerId itemId金額',
            amountLimit: '錯誤！數量從0到1000 ！',
            itemNotFound: '錯誤！找不到具有此ID的項目！',
            log: '{name} #{id} выдал игроку #{targetId} предмет $t({title}) в размере {amount} шт.'
        },
        checkax: { usage: '錯誤！/checkax bizType' },
        acapture: {
            usage: 'Error!/acapture fractionId',
            log: '{name} #{id} 變更了匪賊領地顏色 #{zoneId} 至 {fractionName} [{fractionId}]'
        },
        abizwar: {
            usage: 'Error!/abizwar fractionId',
            log: '{name} #{id} 彩色屋頂業務 {bizName} #{bizId} x {fractionName} [{fractionId}]'
        },
        afacilitywar: {
            usage: 'Error!/afacilitywar fractionId',
            log: '{name} #{id} 企業屋頂顏色 {bizName} #{bizId} x {fractionName} [{fractionId}]'
        },
        setmod: {
            usage: 'Error!/setmod modType modIndex',
            notInVehicle: '錯誤！你必須在運輸中！'
        },
        setneon: {
            usage: '錯誤！/setneon r g b',
            notInVehicle: '錯誤！你必須在運輸中！'
        },
        setmaterials: {
            usage: '錯誤！/setmaterials分數顏色量！',
            colorsRange: '錯誤！輸入必須為紅色、綠色或藍色！',
            fractionNotFound: '錯誤！找不到具有此ID的陣營！',
            materialsRange: '錯誤！ 0到35萬份材料！',
            log: '{name} #{id} 爲分數 {fractionName} [{fractionId}]設置材料 {color} 至 {amount} 的量'
        },
        freez: {
            usage: '錯誤！/freez playerId',
            notFound: '錯誤！找不到玩家！'
        },
        spawncars: {
            usage: 'Error!/spawncars fractionId',
            canSpawnIn: '你可以在 {amount} 分鐘內再做一次！',
            fractionNotFound: '錯誤！找不到具有此ID的陣營！',
            log: '{name} #{id} 爲分數 {fractionName}挖掘 {count} 單位的分數運輸'
        },
        pulltrunk: { usage: '錯誤！/pulltrunk playerId' },
        gs: {
            usage: '錯誤！/gs playerId',
            notFound: '錯誤！找不到玩家！'
        },
        gid: {
            usage: '錯誤！/gid staticId',
            notFound: '錯誤！找不到玩家！'
        },
        id: { usage: '錯誤！/id ID或名稱' },
        showcheats: {
            enabled: '您已啓用作弊顯示',
            disabled: '您已關閉作弊顯示'
        },
        givematerials: {
            usage: '錯誤！/givematerials類型id金額！',
            wrongBizType: '錯誤！此業務類型不適合此團隊！',
            range: '錯誤！必須介於1和100000之間！',
            log: '{name} #{id} 新增 {amount} 件物料至業務 {bizType} #{bizId} （業主： {bizOwner}）'
        },
        givewarehouse: {
            usage: '錯誤！/givewarehouse類型id項目金額！',
            wrongBizType: '錯誤！此業務類型不適合此團隊！',
            range: '錯誤！必須介於1和10000000之間！',
            log: '{name} #{id} 新增 {amount} 項 $t({title}) for {bizType} #{bizId} （所有者： {bizOwner}）'
        },
        setemail: {
            usage: '錯誤！/setemail登入電子郵件',
            emailRange: '錯誤！電子郵件不超過40個字元！',
            invalid: '錯誤！無效的電子郵件格式！',
            loginNotFound: '錯誤！沒有使用此用戶名的用戶！',
            duplicate: '錯誤！此電子郵件已被其他用戶使用！',
            log: '{name} #{id} 集用戶 {login} 郵箱 {email} 而不是 {oldEmail}'
        },
        givepromo: {
            usage: '錯誤！/givepromo促銷登入',
            range: '促銷代碼由3到10個字元！',
            loginRange: '請輸入介於3至20個字元之間的用戶名稱！',
            loginNotFound: '沒有使用此用戶名的玩家！',
            hasPromo: '此玩家已經有媒體狀態和促銷代碼： {inviteCode}！',
            duplicate: '優惠碼 {promo} 已註冊為登入 {login}的玩家！輸入/移除優惠碼 {login}以重設優惠碼。',
            log: '您已成功將流媒體促銷代碼 {promo} 設置為使用者名稱 {login}的玩家'
        },
        removepromo: {
            usage: '錯誤！/removepromo登入',
            loginRange: '請輸入介於3至20個字元之間的用戶名稱！',
            loginNotFound: '沒有使用此用戶名的玩家！',
            log: '您已刪除登入 {login}的玩家的優惠碼 {promo}'
        },
        media: {
            usage: '錯誤！/媒體促銷',
            range: '促銷代碼由3到10個字元！',
            noMediaFound: '此優惠碼沒有媒體',
            log: '{promo} -今天注冊： {dayRegs}，注冊總額： {totalRegs}，確認注冊： {totalConfirmedRegs}，今天捐贈： {dayDonate}，捐贈總額： {totalDonate}'
        },
        setrestart: { usage: '錯誤！/set restart time' },
        aeject: {
            usage: '錯誤！/aeject playerId',
            notFound: '錯誤！玩家已離線！',
            success: '你把 {name} 扔下車了！'
        },
        freezveh: {
            usage: '錯誤！/freezveh vehId',
            notFound: '錯誤！找不到車輛！'
        },
        auninvite: {
            usage: '錯誤！/auninvite playerId',
            notFound: '錯誤！玩家已離線！',
            isLeader: '錯誤！這個玩家是領袖！',
            log: '{name} #{id} 解僱了陣營 {fractionName}的玩家 {targetName} #{targetId} ！'
        },
        eventon: { log: '{name} #{id} 包含事件分數在 {x} {y} {z}！' },
        eventoff: { log: '{name} #{id} 已停用事件積分！' },
        togglecasino: { log: '{name} #{id} 將賭場狀態設為 {state}！' },
        togglebets: { log: '{name} #{id} 將博彩公司辦公室的狀態設置為 {state}！' },
        togglesafe: { log: '{name} #{id} 將安全狀態設置為 {state}！' },
        toggleauction: { log: '{name} #{id} 將拍賣狀態設置為 {state}！' },
        toggletp: { log: '{name} #{id} 將傳送端口上的反訊息狀態設置為 {state}！' },
        toggleitemsexchange: { log: '{name} #{id} 將物品交換狀態設置為 {state}！' },
        toggleexchange: { log: '{name} #{id} 將物業交換狀態設置為 {state}！' },
        toggleanticheat: { log: '{name} #{id} 已將反ICHIT狀態設置為 {state}！' },
        toggleminigames: { log: '{name} #{id} 將迷你遊戲狀態設置為 {state}！' },
        toggleblackjack: { log: '{name} #{id} 將黑傑克狀態設置為 {state}！' },
        togglehorserace: { log: '{name} #{id} 將比賽狀態設為 {state}！' },
        toggleburger: { log: '{name} #{id} 將漢堡房狀態設置為 {state}！' },
        togglegolf: { log: '{name} #{id} 高爾夫狀態爲 {state}！' },
        setname: {
            usage: '錯誤！/setname staticId名稱',
            notFound: '錯誤！找不到玩家！',
            exists: '錯誤！此名已被使用！',
            log: '{name} #{id} изменил имя игроку {targetName} {targetId} на {newName}!'
        },
        aclearchat: { log: '{name} #{id} 已清除聊天！' },
        ainfect: {
            usage: '錯誤！/ainfect playerId',
            notFound: '錯誤！玩家已離線！',
            log: '{name} #{id} 感染 #{infectionId} 玩家 {targetName} #{targetId}！'
        },
        hidemyname: {
            enabled: '你隱藏了你的名字！',
            disabled: '您已顯示您的姓名！'
        },
        roulette: {
            enabled: '你打開了回合！',
            disabled: '你關閉了輪盤！'
        },
        setvip: {
            usage: '錯誤！/setvip staticId vipLevel天！',
            levelRange: '錯誤！通配符等級從0到4 ！',
            durationRange: '錯誤！有效期為0至30天！',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 將 #{vipLevel} 個VIP等級設置為 {targetName} #{targetId} ，持續 {days} 天！'
        },
        lastdriver: {
            usage: '錯誤！/lastdriver vehicleId',
            found: 'Последний водитель автомобиля #{vehicleId} - #{driverId}!'
        },
        blackout: {
            enabled: '{name} #{id} 啓用停機',
            disabled: '{name} #{id} 停機'
        },
        giveclothes: {
            usage: '錯誤！/giveclothes staticId gender component drawable texture isProp comment',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 給玩家穿衣服 {targetName} #{targetId}.原因： {reason}'
        },
        hellhound: { log: '{name} #{id} вызвал hellhound!' },
        setphonenumber: {
            usage: '錯誤！/setphonenumber staticId phoneNumber',
            noPhone: '錯誤！玩家沒有手機！',
            numberBusy: '錯誤！號碼已被佔用！',
            log: '{name} #{id} 將玩家 #{targetId} 的電話號碼更改為 {phoneNumber}'
        },
        setvehiclenumber: {
            usage: '錯誤！/setvehiclenumber vehicleId vehicleNumber',
            noVehicle: '錯誤！找不到車輛！',
            log: '{name} #{id} 將車輛號碼 #{targetId} 改為 {vehicleNumber}'
        },
        mutecheck: {
            usage: '錯誤！/mutecheck staticId',
            notFound: '錯誤！找不到玩家！',
            muted: '玩家 #{staticId} 有一個變態，持續 {time} 分鐘！',
            unmuted: '玩家＃{staticId} 沒有變種！'
        },
        addamphitheater: {
            usage: '錯誤！/addamphitheater id',
            log: '{name} #{id} 包括IPL #{iplId} 露天影院'
        },
        removeamphitheater: {
            usage: '錯誤！/removeamphitheater id',
            log: '{name} #{id} 禁用IPL #{iplId} 露天影院'
        },
        delitem: { owner: '此屬性擁有者： #{id}' },
        togglegreenzone: {
            usage: '錯誤！/togglegreenzone區域名',
            notFound: '錯誤！找不到區域！',
            log: '{name} #{id} {toggle, select, enabled{включил} other{отключил}} зону {zoneName}'
        },
        reset2fa: {
            usage: '錯誤！/reset2fa登入原因',
            notFound: '錯誤！找不到玩家！',
            log: '{name} #{id} 重置雙重登入帳戶 {login}'
        },
        transfervehicle: {
            usage: '錯誤！/transfervehicle vehicleId staticId',
            notFound: '錯誤！找不到車輛！',
            log: '{name} #{id} 將車輛 #{vehicleId} 交給玩家 #{targetId}'
        },
        toauction: {
            usage: '錯誤！/toauction id類型原因',
            notFound: '錯誤！房源不存在！',
            onAuction: '錯誤！此房源已被拍賣！',
            familyHouse: '錯誤！這棟房子屬於這個家庭！',
            log: '{name} #{id} конфисковал на аукцион {tableName} #{propertyId} {fromType, select, player={у {targetName} #{targetId}}}. Причина: {reason}'
        },
        blockcinema: {
            usage: '錯誤！/blockcinema id',
            log: '{name} #{id} {isBlocked, select, blocked={заблокировал} other={разблокировал}} кинотеатр #{cinemaId}'
        },
        setcinemavideo: {
            usage: '錯誤！/setcinemavideo id url',
            log: '{name} #{id} 更改了影院影片 #{cinemaId}'
        },
        coloresp2: {
            usage: '錯誤！/coloresp2 staticId hexColor',
            log: '{name} #{id} 將玩家 #{staticId} 的ESP2顏色更改為 {hexColor}'
        },
        esp2: {
            enabled: '您已成功啓用ESP2',
            disabled: '您已成功禁用ESP2'
        },
        tpabb: {
            usage: '錯誤！/tpabb billboardId',
            notFound: '錯誤！找不到廣告牌！'
        },
        removequeue: {
            usage: '錯誤！/removequeue billboardId [, date] ！',
            notFound: '錯誤！找不到廣告牌！',
            notFoundAd: '錯誤！找不到廣告！',
            empty: '錯誤！未設定廣告！',
            log: 'Вы удалили объявление у билборда #{billboardId} на {date} число'
        },
        setqueue: {
            usage: '錯誤！/setqueue在線！',
            log: '您已將隊列設定為 {online}！'
        },
        createfamily: { notFoundHouse: '你沒有家！' },
        noclip: {
            usage: '錯誤！/noclip播放器！',
            notFound: '錯誤！找不到玩家！',
            notAuthed: '錯誤！玩家尚未登入！'
        },
        familyLogo: { sent: '家族領袖 {title} 已提交頭像供驗證' },
        givecar: { log: '{name} #{id} 交通 {model} [#{staticId}] {targetName}' }
    },
    chat: {
        commands: {
            eventTeleport: '你已傳送到活動！',
            promocode: {
                noPromo: '沒有這樣的促銷代碼！',
                alreadyEntered: '您已輸入優惠碼！',
                alreadyNewLevel: '你已達到3級！',
                cantEnterYours: '您無法輸入優惠碼！'
            },
            bonuscode: {
                noBonus: '沒有這樣的獎勵代碼！',
                limit: '此獎勵代碼已用完！',
                notActive: '此獎勵代碼目前無效！',
                expired: '此獎勵代碼已過期！',
                maxLevel: '此獎勵代碼最多適用於 {maxLevel} 級玩家！',
                notOnThisServer: '此伺服器上無法使用此獎勵代碼！',
                regAfter: '此獎勵適用於在 {date}之前註冊的玩家！',
                alreadyUsed: '你已經使用此獎勵代碼！'
            },
            report: {
                errorOnCreateReportMessage: '建立報告訊息時發生錯誤',
                errorOnCreateReport: '建立報告時發生錯誤',
                reportSuccessfullyCreated: '使用ID #{reportId}建立的報告',
                reportMessageSuccessfullySended: '訊息已成功發送至報告'
            }
        },
        events: {
            saidOnRadio: '%player {gender, select, 0{сказал} 1{сказала}} что-то по рации',
            shout: '%player {gender, select, 0{крикнул} 1{крикнула}}: {text}',
            ooc: '%player: (( {text} ))',
            me: '%player {text}',
            do: '{text} (%player)',
            try: '%player {text} {isLucky, select, 1{@{{3EAC24}}Удачно} other{@{{C82828}}Неудачно}}',
            w: '%player 某事耳語 #{id}',
            speaks: '{name} 表示： {text}',
            whisperTo: '你悄悄說了 {name}: {text}',
            whisperFrom: '{name} {gender, select, 0{шепнул} 1{шепнула}} вам на ухо: {text}',
            familyChat: '[Семья] {rank} {name} [{id}]: {text}',
            fractionChat: '[Фракция] {rank} {name} [{id}]: {text}',
            fractionChat2: '[Фракция] {text} {distPos, select, 1{{distance} метров}}',
            fractionChat3: '[{tag}] {text} {distPos, select, 1{{distance} метров}}',
            stethoscopeListen: '%player {gender, select, 0{послушал} 1{послушала}} в стетоскоп чтобы диагностировать болезнь у #{id}',
            openGates: '%player {gender, select, 0{открыл} 1{открыла}} ворота',
            saidMegaphone: '%player {gender, select, 0{сказал} 1{сказала}} в мегафон: {text}',
            pullOutOfCar: '%player {gender, select, 0{открыл} 1{открыла}} дверь автомобиля и {gender, select, 0{вытащил} 1{вытащила}} #{id}',
            pushInCar: '%player {gender, select, 0{открыл} 1{открыла}} дверь автомобиля и {gender, select, 0{запихнул} 1{запихнула}} #{id}',
            cuffPlayer: '%player {gender, select, 0{снял} 1{сняла}} с пояса наручники и {gender, select, 0{надел} 1{надела}} их на #{id}',
            uncuffPlayer: '%player {gender, select, 0{снял} 1{сняла}} наручники c #{id}',
            escortPlayer: '%player {gender, select, 0{заломал} 1{заломала}} руку задержанному #{id} и {gender, select, 0{потащил} 1{потащила}} за собой',
            unEscortPlayer: '%player {gender, select, 0{отпустил} 1{отпустила}} человека #{id}',
            parkingFine: '%player {gender, select, 0{достал} 1{достала}} тикет со штрафом и {gender, select, 0{положил} 1{положила}} на лобовое стекло для эвакуации авто {title}',
            zipTiePlayer: '%player {gender, select, 0{достал} 1{достала}} из кармана стяжки, и резким движением {gender, select, 0{связал} 1{связала}} #{id}',
            unzipPlayer: '%player {gender, select, 0{развязал} 1{развязала}} #{id}',
            zipRopePlayer: '%player {gender, select, 0{достал} 1{достала}} из кармана веревку, и резким движением {gender, select, 0{связал} 1{связала}} #{id}',
            identikit: '%player {gender, select, 0{сверил} 1{сверила}} фоторобот с #{id}',
            marriageUnion: '{name1} 和 {name2} 已婚！',
            takeBBQ: '%player {gender, select, 0{забрал} 1{забрала}} барбекю',
            takeBoombox: '%player {gender, select, 0{подобрал} 1{подобрала}} бумбокс',
            takeHookah: '%player {gender, select, 0{подобрал} 1{подобрала}} кальян',
            takeTent: '%player {gender, select, 0{забрал} 1{забрала}} палатку',
            withdrewItem: '%player {gender, select, 0{изъял} 1{изъяла}} $t({title}) у #{id}',
            useMedicalKit: '%player {gender, select, 0{достал} 1{достала}} аптечку и {gender, select, 0{начал} 1{начала}} перевязывать рану',
            useBandage: '%player {gender, select, 0{достал} 1{достала}} бинт и {gender, select, 0{начал} 1{начала}} перевязывать рану',
            wearArmour: '%player 套防彈背心',
            takeoffArmour: '%player 移除護身護甲',
            useFireworks: '%player 套煙火',
            putInJail: '%player {gender, select, 0{достал} 1{достала}} ключ от КПЗ и {gender, select, 0{завёл} 1{завела}} #{id} внутрь',
            givePills: '%player {gender, select, 0{взял} 1{взяла}} необходимый лекарственный препарат и {gender, select, 0{передал} 1{передала}} #{id}',
            mentalPaper: '%player {gender, select, 0{достал} 1{достала}} бланк и ручку из сумки, {gender, select, 0{заполнил} 1{заполнила}} бланк о психическом состояние здоровья и {gender, select, 0{передал} 1{передала}} #{id}',
            physicalPaper: '%player {gender, select, 0{достал} 1{достала}} бланк и ручку из сумки, {gender, select, 0{заполнил} 1{заполнила}} бланк о физическом состояние здоровья и {gender, select, 0{передал} 1{передала}} #{id}',
            armyPaper: '%player {gender, select, 0{достал} 1{достала}} бланк и ручку из сумки, {gender, select, 0{заполнил} 1{заполнила}} бланк о военной службе и {gender, select, 0{передал} 1{передала}} #{id}',
            unJail: '%player {gender, select, 0{открыл} 1{открыла}} КПЗ и {gender, select, 0{вывел} 1{вывела}} #{id} из камеры',
            reanimate: '%player {gender, select, 0{начал} 1{начала}} оказывать первую медицинскую помощь для #{id}',
            checkPockets: '%player {gender, select, 0{прощупал} 1{прощупала}} карманы #{id}',
            gag: '%player {gender, select, 0{заклеил} 1{заклеила}} рот #{id}',
            ungag: '%player {gender, select, 0{расклеил} 1{расклеила}} рот #{id}',
            bag: '%player {gender, select, 0{надел} 1{надела}} мешок на #{id}',
            unbag: '%player {gender, select, 0{снял} 1{сняла}} мешок с #{id}',
            throwFromTrunk: '%player {gender, select, 0{выкинул} 1{выкинула}} из багажника #{id}',
            giveLicWeapon: '%player {gender, select, 0{достал} 1{достала}} лицензию на оружие и {gender, select, 0{выдал} 1{выдала}} #{id}',
            takeLicWeapon: '%player {gender, select, 0{забрал} 1{забрала}} лицензию на оружие у #{id}',
            giveLicFishing: '%player {gender, select, 0{достал} 1{достала}} лицензию на рыбалку и {gender, select, 0{выдал} 1{выдала}} #{id}',
            takeLicFishing: '%player {gender, select, 0{забрал} 1{забрала}} лицензию на рыбалку у #{id}',
            takeLicDriveD: '%player {gender, select, 0{забрал} 1{забрала}} лицензию Drive D у #{id}',
            takeLicDriveM: '%player {gender, select, 0{забрал} 1{забрала}} лицензию Drive M у #{id}',
            takeMaskOff: '%player {gender, select, 0{снял} 1{сняла}} маску с #{id}',
            takeDocs: '%player {gender, select, 0{достал} 1{достала}} ID карту из кармана у #{id}',
            takeLics: '%player {gender, select, 0{достал} 1{достала}} лицензии из кармана у #{id}',
            checkAllPockets: '%player {gender, select, 0{прощупал} 1{прощупала}} верхнюю и нижнюю части тела у #{id}',
            useSeatbelt: '%player {gender, select, 0{пристегнул} 1{пристегнула}} ремень безопасности',
            takeOffSeatbelt: '%player {gender, select, 0{отстегнул} 1{отстегнула}} ремень безопасности',
            replaceCarPart: '%player {gender, select, 0{начал} 1{начала}} заменять $t({title}) в автомобиле',
            ticketPlayer: '%player {gender, select, 0{достал} 1{достала}} бланк на оплату штрафа и {gender, select, 0{передал} 1{передала}} #{id}'
        }
    },
    intro: { start: '開始遊戲' },
    auction: {
        tempDisabled: '拍賣暫時不可用！',
        sellLotSuccess: '您已成功在 ${amount}的拍賣中出售了批次#{id} 。傭金爲： ${commission}',
        buyLotSuccess: '您已成功在拍賣中購買了 ${amount}個批次#{id} 。',
        initialBetStep: '起始出價必須高於步驟！',
        notYourCar: '這輛車不屬於你！',
        cantAuctionFamilyCar: '不能拍賣家庭交通工具！',
        cantAuctionRentCar: '你不能拍賣已出租的車輛！',
        carAlreadyInLot: '這輛車已經拍賣過了！',
        cantSellExclusiveCar: '不出售獨家交通工具！',
        cantSellLowerOriginal: '不能以低於州價25%的價格出售！',
        notYourHouse: '這房子不屬於你！',
        houseAlreadyInLot: '這個房子已經被拍賣了！',
        mustBePaidMoreThanLotDate: '該物業的付款期限必須長於拍賣期間！',
        cantSellLowerOriginal2: '不能以低於州價格的價格出售！',
        takeMoneyFromSafeFirst: '先把錢拿出保險庫！',
        notYourApartment: '此公寓不屬於您！',
        apartmentAlreadyInLot: '這間公寓已經上市拍賣！',
        atmNotYours: '這個ATM機不屬於你！',
        atmAlreadyInLot: '這個ATM機已經被拍賣！',
        billboardNotYours: '這個廣告牌不屬於你！',
        billboardAlreadyInLot: '這個廣告牌已經上市拍賣！',
        businessNotYours: '這個生意不屬於你！',
        businessAlreadyInLot: '此生意已被拍賣！',
        setOnAuctionCar: '您已成功拍賣交通工具 {title} 。截止日期： {daysValue} 天。起始價格： ${initialBetValue}',
        setOnAuction: '您已成功拍賣 {title} 。截止日期： {daysValue} 天。起始價格： ${initialBetValue}',
        cantRemoveThisLot: '無法移除此批次！',
        cantRemoveLotWithBets: '無法刪除其中包含投注的批次！',
        removeLotSuccess: '您已成功移除批次#{id}！',
        auctionOnThisLotEnded: '此批次的拍賣已經完成！',
        cantBetOnYourSelf: '你不能自己出價！',
        alreadyMadeBetOnThisType: '你已經在另一個地塊中投注了此旅居類型！',
        alreadyHasHouse: '您已經有自己的房子或出租的房子！',
        alreadyHasApartment: '您已經有自己的或出租的公寓！',
        alreadyHasAtm: '您已有ATM機！',
        alreadyHasBillboard: '您已有廣告牌！',
        alreadyHasBusiness: '你已經有業務了！',
        yourBetAlreadyLeading: '你的賭注已經領先了！',
        lotDataOld: '批次信息已過期',
        madeBetSuccess: '您已成功在拍品#{id}的拍賣中出價 ${bet}',
        noActiveBetOnLot: '您在此項目上沒有有效的投注！',
        cantCancelBet: '您的出價無法取消！',
        cancelBetSuccess: '您已退回批次#{id} 的出價，金額爲 ${amount}',
        yourBetNotLeaderNow: '你投注的批次#{id} 出價已超過 ${amount} ，不再是領導者。如果您想繼續，請提高出價。'
    },
    property: {
        billboards: {
            freeSpace: '廣告位免費',
            youAdHere: '這可能是你的廣告',
            boardId: '主板 #{id}',
            phone: '電話號碼： {number}'
        },
        shopCategories: {
            products: '產品',
            electronics: '電子',
            tools: '工具',
            misc: '其他'
        },
        bizTitles: {
            losSantosCustoms: '洛斯桑託斯海關',
            beekersGarage: 'Beekers Garage',
            tattooShop: '刺青店',
            tinkle: '叮當內飾',
            whiz: 'Whiz內部',
            badger: '徽章內部',
            shop247: '全天候商店',
            shopRobsLiqour: 'Robs酒鋪',
            convenienceStore: '便利店',
            shopLTD: 'Shop LTD',
            ammoshop: '槍彈匣',
            carwash: '洗車服務',
            tuning: '優化沙龍',
            phoneshop: '通信室',
            fuelstation: '加油站',
            fuelstationXero: 'Xero加油站',
            fuelstationGlobe: '球形加油站',
            fuelstationRon: '羅恩加油站',
            fuelstationLTD: '加油站有限公司',
            suburban: '郊區',
            ponsonbys: 'Ponsonbys',
            binco: 'Binco',
            clothesshop: '服裝店',
            barbershop: '理發店',
            autoshop: '經銷商',
            discountStore: '折扣店',
            leopolds: 'Leopolds',
            hairOnHawick: 'Hawick上的毛',
            herrKutz: 'Mr Kutz',
            oSheas: 'O Sheas',
            bobMule: 'Bob Mule',
            premiumDeluxeMotorsport: 'Premium Deluxe Motorsport',
            albanyMotors: 'Albany Motors',
            sandersMotorcycles: 'Sanders Motorcycles',
            benefactor: 'Benefactor',
            vapid: 'Vapid',
            luxuryAutos: 'Luxury Autos',
            joBuiltCommercials: 'JoBuilt廣告',
            majesticMotors: 'Majestic Motors',
            dockTease: 'Dock Tease',
            elitasTravel: 'Elitas Travel',
            atm: 'ATM機',
            billboard: '廣告牌'
        },
        bizTitlesId: {
            ammo_shops: '槍彈匣 #{id}',
            auto_shops: '經銷商 #{id}',
            barber_shops: '理發店 #{id}',
            carwash_shops: '洗車 #{id}',
            clothes_shops: '服裝店 #{id}',
            fuel_stations: '加油 #{id}',
            item_shops: '全天候商店 #{id}',
            tattoo_shops: 'Tattoo parlor #{id}',
            tuning_shops: '優化沙龍 #{id}',
            mobile_shops: '通信室 #{id}',
            houses: 'House #{id}',
            apartments: '公寓 #{id}',
            atm: 'ATM #{id}',
            billboards: '廣告牌 #{id}'
        },
        taxes: {
            ammo_shops: '槍彈匣',
            auto_shops: '汽車經銷商',
            barber_shops: '理髮店',
            carwash_shops: '洗車機',
            clothes_shops: '服裝店',
            fuel_stations: '加油站',
            item_shops: '全天候商店',
            tattoo_shops: '刺青店',
            tuning_shops: '調節沙龍',
            mobile_shops: '交流沙龍',
            atm: 'aTM機',
            billboards: '廣告牌'
        },
        atmTypes: {
            cashInPercent: '用於充值',
            cashOutPercent: '提款',
            mobilePercent: '用於手機充值'
        },
        propertyInfoError: '檢索房源資訊時發生錯誤！',
        notRentingHouse: '你不能出租這棟房子！',
        notRentingApartment: '你不能租這個地方！',
        denyRentHouse: 'Вы отказались от аренды дома №{id}!',
        denyRentApartment: 'Вы отказались от аренды квартиры №{id}!',
        businessNotAvailableForBuy: '此業務無法購買！',
        cantBuyPropertyInAuction: '此物業無法購買，因為它已被拍賣！',
        alreadyHasBusiness: '你已經有業務了！',
        alreadyHasHouse: '您已經有自己的房子或出租的房子！',
        alreadyHasApartment: '您已經有自己的或出租的公寓！',
        cantBuyBusinessWithBets: '當你投賭一筆業務時，就無法購買一筆業務！',
        firstCancelHouseRent: '先別再租房子或公寓了！',
        cantBuyHouseWithBets: '當你賭一張房子時，不可能買房子！',
        cantBuyApartmentWithBets: '當您下注於公寓地塊時，不可能購買公寓！',
        alreadyBought: '已經買過了！',
        propertyNotSelling: '此房源不再出售！',
        successBuyProperty: '您已成功購買 {title} 為 ${price}！',
        successSellProperty: '你已成功賣出 {title} 為 ${price}！',
        sellError: '銷售錯誤！',
        notYourHouse: '這不是你的房子！',
        cantSellHouseOnAuction: '出售已被拍賣的房子是不可能的！',
        takeMoneyFromSafeFirst: '先把錢拿出保險庫！',
        notYourBusiness: '這不關你的事！',
        cantSellBusinessOnAuction: '不可能出售已被拍賣的企業！',
        notYourApartment: '這不是你的公寓！',
        cantSellApartmentOnAuction: '不可能賣掉要拍賣的公寓！',
        evictedFromHouseSold: '你被逐出{id}號房子了',
        evictedFromApartmentSold: '你被逐出{id} 號公寓，因為它的出售。',
        safeMoneyReturn: '退回保險箱內容，金額為 ${amount}！',
        unpaidHouseSold: '房子#{id} 因未付款而以 ${price} 出售！',
        unpaidApartmentSold: '公寓#{id} 因未付款而以 ${price} 出售！',
        unpaidAtmSold: 'ATM #{id} 因未付款而以 ${price} 出售！',
        unpaidBillboardSold: '廣告牌#{id} 因未付款而以 ${price} 出售！',
        unpaidBusinessSold: '業務 {title} #{id} 因未付款而銷售 ${price} ！',
        safeTempDisabled: '暫時無法使用保險箱！',
        notYourApartmentNoKey: '這不是你的公寓。你沒有鑰匙！',
        notYourHouseNoKey: '這不是你的房子。你沒有鑰匙！',
        cantUseSafeWhileOnAuction: '當房源被拍賣時，您無法使用保險箱！',
        safeMaxLimit: '金額錯誤！安全儲存高達 ${amount}！',
        putInSafeSuccess: '您已成功將 ${amount} 放入保險箱。總金額為： ${totalAmount}',
        takeFromSafeSuccess: '您已成功從保險箱中取出 ${amount} 。總金額為： ${totalAmount}',
        robbedRecently: '生意最近被搶了！',
        notInShop: '你不在商店裏！',
        productsFinishInCategory: '此類別的產品已售罄！',
        buySuccess: '您已成功購買 $t({title}) 為 ${price}',
        atmOutOfChecks: 'ATM機支票用完了！',
        min100Dollars: '最低交易金額爲100美元！',
        noMoneyInAtm: 'ATM機沒有現金！可用： ${amount}',
        cardToCash: '您已提取 ${amount}現金卡！費用： ${commission}',
        cashToCard: '您已轉帳至 ${amount}卡！費用： ${commission}',
        transferAfterSecondLvl: '達到2級後即可獲得翻譯！',
        cantTransferToOwnAccount: '您無法將錢轉入自己的帳戶！',
        transferToAccount: '您已轉入{orderId} 號帳戶 ${amount}！費用： ${commission}',
        phoneNumberNotBelong: '這個電話號碼不屬於任何人！',
        maxSimBalance: 'SIM卡餘額不能超過 ${amount}！',
        simTopUpSuccess: '您已成功重新填充您的SIM卡餘額，從 {number} 到 ${amount} 到 ${amountSim}！費用： ${commission}',
        atmNotForSale: '此ATM機不出售！',
        cantBuyAtmWhichOnAuction: '你不能購買一個ATM ，這是要拍賣的！',
        alreadyHasAtm: '您已有ATM機！',
        buyAtmSuccess: '您已成功以 ${price}的價格購買了ATM ＃{id} ！稅項提前14天支付。',
        notYourAtm: '不是你的ATM機！',
        cantSellAtmWhichOnAuction: '你不能賣一個ATM拍賣！',
        sellAtmSuccess: '您已成功出售 ${price}的ATM #{id} ！',
        cantBeMoreChecks: '收據不得超過 {amount} 份！',
        noMoneyPriceBuy: '你沒有足夠的現金！購買價格： ${price}',
        buyChecksSuccess: '您已成功購買 ${price}的 {amount} 張收據。總數為： {materials}',
        taxLimit: '可能從 {from}％到 {to}％ ！',
        changeTaxSuccess: 'Вы успешно изменили процент комиссии {title} на {amount}%',
        notEnoughProfitInAtm: '這個ATM機裡沒有那麼多現金！',
        atmCashOutSuccess: '您已成功從ATM提取 ${amount} 。所得稅額為 ${commission}',
        atmAlreadyPaid: 'ATM機已提前 {amount} 天付款！',
        cantPayAtmMore: '您不能提前 {amount} 天以上支付ATM機！',
        atmPaidSuccess: '您已成功支付 ${amount} 至 {date}的ATM稅。',
        noVehicleInStock: '此模型不可用！',
        vehicleBuySuccess: '您已成功購買 {model} 為 ${amount}！車牌號碼- {number}。車子出現在你附近。',
        vehicleBuySuccessCoins: '您已成功為 {amount} MC購買了 {model} ！車牌號碼- {number}。車子出現在你附近。',
        vehicleWillOnSpecificPlace: '該車輛將在特殊停車場提供',
        noProductInStock: '沒有存貨！',
        barberPaidSuccess: '您已成功支付 ${amount}的 {category} - {title} ！',
        errorCreationBillboards: '建立旅居時發生錯誤！',
        errorCreationBillboards2: '旅居標題錯誤！',
        errorCreationBillboards3: '廣告文字錯誤！',
        errorCreationBillboards4: '電話號碼錯誤！',
        errorCreationBillboards5: '未指定房源日期！',
        billboardNotSelling: '此廣告牌不出售！',
        notEnoughMoneyToBuyBillboard: '您沒有足夠的錢購買這個廣告牌！',
        alreadyHasBillboard: '您已有廣告牌！',
        billboardBuySuccess: '您已成功以 ${amount}的價格購買廣告牌＃{id} ！稅項提前14天支付。',
        notYourBillboard: '不是你的廣告牌！',
        cantSellOnAuctionBillboard: '你不能賣廣告牌，這個廣告牌是要拍賣的！',
        billboardSellSuccess: '您已成功以 ${amount}的價格出售廣告牌#{id} ！',
        noBillboard: '你沒有廣告牌！',
        billboardMaterialsLimit: '不得超過 {amount} 種材料！',
        materialsBuySuccess: '您已成功為 ${price}購買了 {amount} 項商品。總數為： {materials}',
        notEnoughProfitInBillboard: '這個廣告牌沒有那麼多收入！',
        billboardCashOutSuccess: '您已成功從廣告牌中移除 ${amount} 。所得稅額為 ${commission}',
        billboardAlreadyPaid: '廣告牌已提前 {amount} 天付款！',
        cantPayBillboardMore: '您不能提前 {amount} 天以上支付廣告牌費用！',
        paidBillboardSuccess: '您已成功支付 ${price} 至 {date}的廣告牌稅費。',
        billboardChangeAdPrice: '您已成功將24小時的住宿價格更改為 ${value}',
        errorWhileBuyingClothes: '購買同捆包時發生錯誤！',
        firstTakeOffFractionClothes: '先從這個空間移除陣營道具！',
        buyClothesMC: '您已成功為 {amount} MC購買一套服裝套裝！',
        buyClothes: '您已成功購買一套服裝套裝，價格為 ${amount}！',
        turnBackFractionClothes: '你已成功投降多餘的陣營服裝！',
        noFractionClothesRemoved: '你沒有任何實際的衣服！',
        errorFuelType: '汽油類型錯誤！',
        noCarInFuelList: '你的車輛不在列表中！讓開發人員知道！',
        fuelTypeNotSupported: '您的車輛不支援這種燃料類型！',
        notEnoughFuelOnGasStation: '加油站沒有這種燃料！',
        alreadyHasLottery: '您已經有彩票了！',
        howToUseRadioHint: '爲了得到收音機-使用清單。使用無線電鍵盤上的按鈕開啟無線電，然後進入通訊頻道，然後按橙色Enter按鈕。之後，您可以按下「退出」按鈕，並使用鍵盤上的英文M鍵與無線對話。',
        alreadyHasPhone: '你已經有手機了！',
        alreadyHasTattoo: '你已經有這個紋身了！',
        tattooAlreadyRemoved: '此文身已被移除！',
        maxTattooLimit: '最多可購買 {amount} 個紋身！',
        removeTattooSuccess: 'Вы успешно удалили {title} тату за ${amount}!',
        buyTattooSuccess: '您已成功刺青 ${amount}的 {title} 條紋身！',
        successBuyTuningPart: '您已成功購買 {title} {id} ${price}！',
        tryingToBuyFast: '你試圖經常進行購買！',
        waitPurchaseEnd: '等待購買完成！',
        limitedTestDrive: '你有 {amount} 分鐘的時間來測試開車！',
        limitedTestDriveFT: '你有 {amount} 分鐘的時間以最大調諧性能測試駕駛！',
        mustBeNearAutoshop: '你必須在其中一家經銷商附近！',
        cantFuelThisCar: '無法給這輛車加油！',
        barberShopItemTitle: '樣式 {id}'
    },
    trade: {
        propertyList: {
            vehicle: '運送；運送',
            house: '在家裏',
            apartment: '公寓',
            biz: '生意；生意',
            atm: 'aTM機',
            billboards: '廣告牌'
        },
        noSecondPlayer: '沒有第二名參與者！',
        wrongPrice: '兌換價格無效！',
        farFromSecondPlayer: '您與第二位共享者距離太遠！',
        noMoneyForTrade: '你沒有足夠的資金來兌換！',
        noMoneyForTradeSecond: '交易所的第二名參與者資金不足！',
        notYourProperty: '此房源不屬於您！',
        notHisProperty: '此屬性不屬於玩家！',
        cantSellCarOnAuction: '不可能出售已被拍賣的汽車！',
        playerCantSellCarOnAuction: '玩家不能賣一輛要拍賣的汽車！',
        secondPlayerNoGarages: '第二位共享者沒有任何可用的運輸插槽！',
        cantTradeExclusiveCar: '不換專屬交通工具！',
        cantTradeCarFromAutomarket: '您無法從汽車市場兌換汽車！',
        cantTradeCarFamilyCar: '您不能更換家庭車輛！',
        cantTradeRentCar: '你無法更換已出租的車輛！',
        cantSellPropertyOnAuction: '無法出售已被拍賣的物業！',
        playerCantSellPropertyOnAuction: '玩家無法出售供拍賣的財產！',
        playerCantGetPropertyTypeWhenBet: '玩家在競爭中出價時無法獲得這種類型的財產！',
        cantGetPropertyTypeWhenBet: '當你出價拍賣此旅居類型時，你無法獲得此旅居類型！',
        alreadyHasHouseOrRent: '您已經有房源（房屋或出租房屋） ！',
        playerAlreadyHasHouseOrRent: '第二個交易所參與者已經有物業（房屋或出租房屋） ！',
        alreadyHasApartmentOrRent: '您已有住宿（公寓或出租公寓） ！',
        playerAlreadyHasApartmentOrRent: '第二位交流參與者已經有住宿（公寓或出租公寓） ！',
        alreadyHasHouse: '您已經有自己的房子或出租的房子！',
        alreadyHasApartment: '您已經有自己的或出租的公寓！',
        alreadyHasAtm: '您已有ATM機！',
        alreadyHasBillboard: '您已有廣告牌！',
        alreadyHasBusiness: '你已經有業務了！',
        playerAlreadyHasHouse: '第二個交易所已經有房子了！',
        playerAlreadyHasApartment: '第二個交易所已經有公寓了！',
        playerAlreadyHasAtm: '第二個交易所已經有一個ATM機！',
        playerAlreadyHasBillboard: '第二位參加者已經有一個廣告牌！',
        playerAlreadyHasBusiness: '交易所的第二位參與者已經有業務了！',
        mustTakeMoneyFromSafeFirst: '你必須先從保險箱拿起錢！',
        playerMustTakeMoneyFromSafeFirst: '交易所的第二名參與者必須從保險庫拿起錢！',
        tradeSuccess: '交換結束了！',
        missingPlayer: '缺少玩家！',
        vehicleMustBeNear: '交通工具應該就在你附近！',
        rentHours1to120: '可租用1至120小時！',
        farAwayFromPlayer: '您與第二位共享者距離太遠！',
        cantSellExclusiveCar: '不出售獨家交通工具！',
        tradeToPlayerRequestVehicle: '你已提供 {name} 的優惠，以 ${price}價出售你的車輛 {title} 。等待回覆。',
        tradeFromPlayerRequestVehicle: '{name} 已向你提出賣車輛 {title} 換 ${price}。作出決定。',
        tradeToPlayerRequestVehicleRent: 'Вы сделали предложение {name} о сдаче в аренду на {rentHours} час. своего транспорта {title} за ${price}. Ожидайте ответа.',
        tradeFromPlayerRequestVehicleRent: '{name} сделал вам предложение о сдаче в аренду на {rentHours} час. своего транспорта {title} за ${price}. Примите решение.',
        vehicleAlreadyRented: '此車輛已經租給其他玩家！',
        noHome: '你沒有家！',
        notYourHouse: '這不是你的房子！',
        farFromHouse: '你離家太遠了！',
        noApartment: '你沒有公寓！',
        notYourApartment: '這不是你的公寓！',
        farFromApartment: '你離公寓太遠了！',
        noAtm: '你沒有ATM機！',
        notYourAtm: '不是你的ATM機！',
        farFromAtm: '你離ATM機太遠了！',
        noBillboard: '你沒有廣告牌！',
        notYourBillboard: '不是你的廣告牌！',
        farFromBillboard: '你離廣告牌太遠了！',
        noBusiness: '你沒有生意！',
        notYourBusiness: '這不關你的事！',
        farFromBusiness: '你離生意太遠了！',
        tradeToPlayerRequest: '您已提供 {name} 的優惠，以 ${price}代價出售您的 $t({prop}) 。等待回覆。',
        tradeFromPlayerRequest: '{name} 已經向你提出要賣你的 $t({prop}) 為 ${price}。作出決定。',
        declineTradeWith: '您已選擇不與 {name}分享',
        declineTradeFrom: '{name} 拒絕兌換',
        declineSell: '你拒絕賣出你的 $t({property})',
        playerDeclineSell: '{name} отказался от продажи своего $t({property})',
        declineBuy: '你已選擇退出 {property}',
        playerDeclineBuy: '{name} 拒絕購買您的 {property}',
        errorWhileSelling: '銷售期間發生錯誤！',
        vehicleNotOwners: '交通工具不屬於賣家！',
        sellerCantSellVehicleOnAuction: '賣方無法賣出或租賃供拍賣的車輛！',
        farFromSeller: '你離賣家太遠了！',
        notEnoughMoneyToBuy: '你沒有足夠的購物資源！',
        notEnoughMoneyToRent: '您沒有足夠的資金出租！',
        playerNotEnoughMoneyToBuy: '買家沒有足夠的資金來購買！',
        playerNotEnoughMoneyToRent: '買家沒有足夠的資金出租！',
        buyerNotEnoughGarages: '購買者沒有任何免費運輸插槽！',
        successBuyVehicle: '您已成功從 {name} 為 ${price}購買了交通工具 {title} 。',
        successSellVehicle: '你已成功銷售運輸 {title} {name} ${price}。',
        successRentVehicleFrom: '您已成功租用交通工具 {title} ， {name} 爲 ${price} ， {rentHours} 小時。',
        successRentVehicleTo: '您已成功租用交通工具 {title} {name} ${price} {rentHours} 小時。',
        houseNotOwners: '房子不屬於賣家！',
        sellerCantSellHouseOnAuction: '賣家不能賣掉被拍賣的房子！',
        alreadyRentingHouse: '你已經出租房子了！',
        youCantBuyHouseWhenBetActive: '當你拍賣房子時，你不能買房子！',
        apartmentNotOwners: '公寓不屬於賣家！',
        sellerCantSellApartmentOnAuction: '賣家不能賣掉那個要拍賣的公寓！',
        alreadyRentingApartment: '您已出租公寓！',
        youCantBuyApartmentWhenBetActive: '當您對公寓拍賣進行有效出價時，您無法購買公寓！',
        alreadyHasOwnApartment: '您已經有公寓了！',
        atmNotOwners: 'ATM機不屬於賣家！',
        sellerCantSellAtmOnAuction: '賣家無法出售已拍賣的ATM機！',
        youCantBuyAtmWhenBetActive: '當您在ATM拍賣中出價時，您無法購買ATM ！',
        billboardNotOwners: '廣告牌不屬於賣家！',
        sellerCantSellBillboardOnAuction: '賣家無法賣出要拍賣的廣告牌！',
        youCantBuyBillboardWhenBetActive: '當你在廣告牌拍賣中獲得有效出價時，你無法購買廣告牌！',
        businessNotOwners: '商家不屬於賣家！',
        sellerCantSellBusinessOnAuction: '賣方無法出售被拍賣的業務！',
        youCantBuyBusinessWhenBetActive: '當你在商業拍賣中積極出價時，你無法購買商業！',
        successBuy: '您已成功從 {name} 為 ${price}購買了 {title} 。',
        successSell: '您已成功出售 {title} {name} for ${price}。',
        alreadySellingOneVehicle: '你已經在出售一輛車輛！',
        putVehicleOnSellSuccess: '你把你的交通工具 {title} 出售 ${price}。',
        canceled: '交易已取消！',
        noSecondTrader: '沒有第二名參與者！',
        wrongTradeAmount: '兌換價格無效！',
        cantTradeWithoutItems: '沒有道具你不能交易！',
        limitItems: '每面最多可兌換5個道具！',
        farAway: '您與第二位共享者距離太遠！',
        notEnoughMoney: '你沒有足夠的資金來兌換！',
        notEnoughMoneyPlayer: '交易所的第二名參與者資金不足！',
        cantTradeWallet: '你不能兌換錢包！',
        playerCantTradeWallet: '第二位參與者無法兌換錢包！',
        cantTradeDonateClothes: '您無法更換捐贈的物品！',
        playerCantTradeDonateClothes: '第二位參與者無法更換捐贈的物品！',
        playerNoSpace: '第二位共享者沒有足夠的可用空間放置物品！',
        playerNoWeight: '交換的第二位參與者有重量不適合的道具！',
        noSpace: '你沒有足夠的空間放置道具！釋放一些空間！',
        noWeight: '物體不適合重量！',
        noPlayer: '第二名參與者不見了！',
        tradeSucces: 'Обмен предметами успешно завершён! Вы доплатили ${myAmount}. Вам доплатили ${hisAmount}.',
        youCanceledTrade: '您已選擇不與 {name}分享餐點',
        canceledTrade: '{name} 拒絕兌換物品'
    },
    gps: {
        reachPoint: '你已達到目標！',
        cancel: '路由已取消！',
        start: '路由已建立！目的地： {title}',
        bizTitle: '$t({title}) #{id}',
        contractSaleTitle: '移交點- $t({title})',
        contractLoadTitle: '加載點- $t({title})',
        waypoints: {
            general: '重要提示',
            fun: '娛樂',
            fractions: '分數',
            jobs: '工作',
            metro: 'Metro',
            different: '其他',
            atm: 'ATM機',
            billboards: '廣告牌',
            atmitem: 'ATM #{id}',
            billboarditem: '廣告牌 #{id}',
            auto_shops: '汽車經銷商',
            tuning_shops: '美容院',
            item_shops: '商品商店',
            clothes_shops: '服裝店',
            ammo_shops: '武器商店',
            fuel_stations: '加油站',
            tattoo_shops: '刺青店',
            barber_shops: '理發店',
            carwash_shops: '洗車服務',
            hospital_pillbox: 'Pillbox醫院',
            hospital_paleto: 'Paleto Bay醫院',
            hospital_sandy: 'Sandy Shores醫院',
            carmarket: 'Automarket',
            market: '市場',
            auction: '拍賣',
            bank: '本行',
            cityhall: '市政廳',
            autoschool: '駕駛學校',
            flyingschool: '飛行學校',
            university: '大學',
            casino: '賭場',
            mazearena: '迷宮銀行競技場',
            thepalace: '宮殿',
            stipclub: '脫衣舞俱樂部',
            comedyclub: '喜劇俱樂部',
            tequila: 'Tequil-La La',
            bahamas: 'Bahama Mamas',
            cinema: 'Cinema',
            bets: '博彩公司',
            lspd: '警察局',
            ems: '衛生署',
            sheriff: '治安部',
            cityhallls: '洛斯桑託斯市政大廳',
            news: '通訊社',
            army: '軍隊',
            fib: 'FIB',
            trucker: 'Trucker',
            busdriver: '巴士司機',
            builder: '生成器',
            taxi: '計程車司機',
            garbagecollector: '拾荒者',
            moneycollector: '收款人',
            gopostal: '郵差',
            butcher: '屠夫',
            lumberjack: '伐木工',
            farmer: 'Farmer',
            fisherman: '漁夫',
            mushroomer: '蘑菇機',
            miner: '礦工',
            LSIATerminal: 'LSIA 4航站樓',
            LSIAParking: 'LSIA停車場',
            Burton: '伯頓',
            PuertoDelSol: 'Puerto Del Sol',
            Strawberry: '草莓',
            PortolaDrive: 'Portola Drive',
            DelPerro: '德爾佩羅',
            LittleSeoul: '小首爾',
            PillboxSouth: 'Pillbox South',
            Davis: 'Davis',
            boatstation: '船站',
            helistation: '直升機停機坪',
            fishsale: '魚類銷售',
            boatrent: '船舶租賃',
            sellitems: '條款的銷售',
            church: '教會',
            familyHouse: '家庭住宅',
            busStop: '巴士站',
            garbageDump: '垃圾填埋',
            scavengerJob: '拾荒者飛行',
            unloadingPoint: '卸貨點',
            callPlace: '呼叫地點',
            taxiBooked: '已訂購計程車'
        }
    },
    metro: { tempDisabled: '地鐵暫時無法使用！' },
    interiors: { carMeet: { cantEnterVehicle: '這輛車不能開進這個房間！' } },
    prison: {
        closedDoors: '你坐牢了當大門打開時，你將能夠完成任務來完成你的刑期！',
        openedDoors: '你坐牢了完成在地圖上標記的任務，完成你的句子！',
        closingDoors: '籠子關上了，你必須先做一段時間，然後再處理！',
        openingDoors: '籠子打開了。前往選擇器接受任務。完成任務，減刑！',
        cookBurgerSuccess: '您已成功準備餐點！',
        cookBurgerAllSuccess: '您已成功烹煮所有食物！',
        cookBurgerFail: '你煮得不好！',
        swipeFloorSuccess: '您已成功清理地板！',
        swipeFloorFail: '你把地板掃得很糟糕！',
        washFloorSuccess: '您已成功清潔地板！',
        cleanToiletSuccess: '您已成功清潔馬桶！'
    },
    world: { seats: { noSeats: '有沒有地方！' } },
    utils: {
        autopilotActivated: 'Autopilot已激活',
        autopilotDeactivated: '自動飛行器已停用'
    },
    greenzone: {
        entered: '你已進入綠色區域！',
        left: '你出綠地區了！'
    },
    cruiseControl: {
        drivingSlow: '你開得太慢了！',
        setToSpeed: '巡航控制速度爲 ~b~{amount}~b~ 公裏/小時',
        disabled: '巡航控制 ~r~已關閉！'
    },
    displayHelpString: {
        familyContractAction: '按 ~INPUT_CONTEXT~ 以滿足合同條件',
        familyContractFromAction: '按 ~INPUT_CONTEXT~ 收取合同的物品',
        toEnter: '按 ~INPUT_CONTEXT~ 登錄',
        toExit: '按 ~INPUT_CONTEXT~ 退出',
        interact: '按 ~INPUT_CONTEXT~ 交互',
        openAtm: '按 ~INPUT_CONTEXT~ 打開ATM',
        openAuction: '按 ~INPUT_CONTEXT~ 打開拍賣',
        finishTestDrive: '按 ~INPUT_VEH_EXIT~ 結束測試駕駛',
        openBets: '按 ~INPUT_CONTEXT~ 打開投注菜單',
        washCar: '按 ~INPUT_CONTEXT~ 清洗車輛',
        openWardrobe: '按 ~INPUT_CONTEXT~ 打開櫃子',
        openCraft: '按 ~INPUT_CONTEXT~ 打開工藝菜單',
        openDrift: '按 ~INPUT_CONTEXT~ 打開分數板漂移',
        toVote: '按 ~INPUT_CONTEXT~ 投票',
        getPrices: '點擊 ~INPUT_CONTEXT~ 查看價格',
        cantGetNow: '你現在拿不到了！',
        toSellItems: '點擊 ~INPUT_CONTEXT~ 以銷售餐點',
        toBuyTicket: '按 ~INPUT_CONTEXT~ 購買門票',
        currentStation: '工作站 ~g~{stationName}~w~.\n按 ~INPUT_CONTEXT~ 退出',
        getInfo: '點擊 ~INPUT_CONTEXT~ 以了解詳情',
        spawnCar: '按 ~INPUT_CONTEXT~ 打開生成界面',
        toFix: '按 ~INPUT_CONTEXT~ 修復',
        openWarehouse: '按 ~INPUT_CONTEXT~ 打開停車場',
        putBag: '按 ~INPUT_CONTEXT~ 把箱子放在倉庫裡',
        loadUnload: '按 ~INPUT_CONTEXT~ 開始加載/卸載',
        deliverMaterials: '按 ~INPUT_CONTEXT~ 以交付材料',
        craftZone: '按 ~INPUT_CONTEXT~ 打開工藝菜單',
        weddingMenu: '按 ~INPUT_CONTEXT~ 打開婚禮選單',
        guider: {
            yourGuider: '這是你的旅遊攻略',
            helpWithYou: '它將幫助您在此伺服器上開始遊戲',
            askHim: '去和向導談談'
        },
        openDoor: '按 ~INPUT_CONTEXT~ 打開門',
        toHackCell: '按 ~INPUT_CONTEXT~ 以黑入單元格',
        takeMoney: '點擊 ~INPUT_CONTEXT~ 以收取款項',
        openGates: '按 ~INPUT_CONTEXT~ 打開門',
        toLoad: '按 ~INPUT_CONTEXT~ 載入',
        toUnload: '按 ~INPUT_CONTEXT~ 卸載',
        toLoadMaterials: '按 ~INPUT_CONTEXT~ 上傳內容',
        toDeliverMaterials: '按 ~INPUT_CONTEXT~ 以交付材料',
        stealMaterials: '點擊 ~INPUT_CONTEXT~ 以竊取內容',
        unloadStolenMaterials: '按 ~INPUT_CONTEXT~ 卸下被盜物料',
        openCrate: '按 ~INPUT_CONTEXT~ 打開抽屜',
        inCuffs: '你的手在 ~r~手銬~s~，你控制不了！',
        takeBox: '按 ~INPUT_CONTEXT~ 拾取抽屜',
        takeSeat: '點擊 ~INPUT_CONTEXT~ 以坐下',
        seatTable: '按 ~INPUT_CONTEXT~ 坐在桌子旁',
        toMakeBets: '點擊 ~INPUT_ENTER~ 下注',
        exitRoof: '按 ~INPUT_CONTEXT~ 到屋頂',
        enterCasino: '按 ~INPUT_CONTEXT~ 進入賭場',
        toSpinWheel: '按 ~INPUT_CONTEXT~ 捲動鼓',
        toSeeHint: '按 ~INPUT_VEH_HEADLIGHT~ 查看工具提示',
        makeBets: '下注',
        betsMade: '已投注',
        toPlaySlots: '點擊 ~INPUT_CONTEXT~ 以播放插槽~n~投注： ${amount}',
        slotBetInfo: '基準速率： ${min}~n~最大： ${max}~n~當前： ${currentBet}',
        toEnterCabin: '按 ~INPUT_CONTEXT~ 坐在計程車裡',
        waitingCabinStop: '正在等待計程車停車降落',
        toChangeCamMode: '按 ~INPUT_NEXT_CAMERA~ 更改相機模式',
        keyboardToPlay: '使用鍵盤播放',
        toPlay: '按 ~INPUT_CONTEXT~ 播放',
        golfLobby: '按 ~INPUT_CONTEXT~ 一起玩，或按 ~INPUT_ARREST~ 一起玩',
        toJoinGame: '按 ~INPUT_CONTEXT~ 加入遊戲',
        toOpenMenu: '按 ~INPUT_CONTEXT~ 打開菜單',
        seatOccupied: '此座位已就位',
        toggleHands: 'Нажмите ~INPUT_VEH_DUCK~ чтобы {isHandsUp, select, 1{опустить} other{поднять}} руки',
        toCollect: '按 ~INPUT_CONTEXT~ 集合',
        toDig: '按 ~INPUT_CONTEXT~ 取得此',
        toSeeAirlineTickets: '按 ~INPUT_CONTEXT~ 查看航班',
        exitToGarages: '按 ~INPUT_CONTEXT~ 退出艦隊',
        elevatorDown: '按 ~INPUT_CONTEXT~ 下電梯',
        elevatorUp: '按 ~INPUT_CONTEXT~ 爬升機',
        toEnterPenthouse: '按 ~INPUT_CONTEXT~ 進入頂層公寓',
        toUseElevator: '按 ~INPUT_CONTEXT~ 使用升降機',
        goUp: '按 ~INPUT_CONTEXT~ 攀爬',
        goDown: '按 ~INPUT_CONTEXT~ 向下',
        interactTV: '按 ~INPUT_CONTEXT~ 與電視交互',
        toggleMusic: '按 ~INPUT_CONTEXT~ 打開/關閉音樂',
        privateDanceOrder: '按 ~INPUT_CONTEXT~ 以訂購 ${amount}的私人舞蹈',
        layItem: '按 ~INPUT_CONTEXT~ 抵押物品',
        getToJob: '按 ~INPUT_CONTEXT~ 開始工作',
        toStartJob: '按 ~INPUT_CONTEXT~ 開始',
        cutMeat: '按 ~INPUT_CONTEXT~ 切肉',
        waterCrop: '按 ~INPUT_CONTEXT~ 爲作物加水',
        harvestCrop: '按 ~INPUT_CONTEXT~ 收獲',
        throwRod: '按 ~INPUT_CONTEXT~ 扔魚竿',
        somethingPecking: '有東西被咬了！用 ~INPUT_PARACHUTE_TURN_LR~ 來讓魚疲倦',
        fishTired: '魚累了，按住 ~INPUT_ATTACK~ 拉魚',
        toCleanFloor: '按 ~INPUT_CONTEXT~ 擦地板',
        toSneakInTrash: '按 ~INPUT_CONTEXT~ 查看垃圾箱',
        toTakeTrash: '按 ~INPUT_CONTEXT~ 提取垃圾',
        toThrowTrash: '按 ~INPUT_CONTEXT~ 扔垃圾',
        toLoadBoxes: '按 ~INPUT_CONTEXT~ 加載箱子',
        toTakeBox: '按 ~INPUT_CONTEXT~ 拾取抽屜',
        toUnloadBoxes: '按 ~INPUT_CONTEXT~ 開始卸載板條箱',
        toHijackIgnition: '按 ~INPUT_CONTEXT~ 打破着火',
        toDropVehicle: '點按 ~INPUT_CONTEXT~ 以租車',
        toChopTree: '按 ~INPUT_CONTEXT~ 砍伐樹',
        toLoadMoney: '點擊 ~INPUT_CONTEXT~ 以載入錢',
        toFixRoad: '按 ~INPUT_CONTEXT~ 修復',
        toLoadCargo: '按 ~INPUT_CONTEXT~ 加載貨物',
        toUnloadCargo: '按 ~INPUT_CONTEXT~ 卸載',
        toHarvestGrapes: '按 ~INPUT_CONTEXT~ 種植葡萄',
        putGrapes: '按 ~INPUT_CONTEXT~ 排列種子',
        toWaterCan: '按 ~INPUT_CONTEXT~ ，用水填充內容物',
        toCancelCarrying: '按 ~INPUT_JUMP~ 取消手持行李',
        toSweepArea: '按 ~INPUT_CONTEXT~ 掃描區域',
        toCookBurger: '按 ~INPUT_CONTEXT~ 開始烹飪',
        toCleanToilet: '按 ~INPUT_CONTEXT~ 清洗馬桶',
        lookInWarehouse: '按 ~INPUT_CONTEXT~ 查看儲藏室',
        loonInFridge: '按 ~INPUT_CONTEXT~ 查看冰箱',
        toOpenSafe: '按 ~INPUT_CONTEXT~ 打開保險箱',
        toCheckVehicle: '按 ~INPUT_CONTEXT~ 選擇傳送',
        toEnterTuning: '按 ~INPUT_CONTEXT~ 進入調諧',
        toDoExercise: '按 ~INPUT_CONTEXT~ 做練習',
        toGetUp: '按 ~INPUT_CONTEXT~ 站立',
        toSeat: '按 ~INPUT_CONTEXT~ 坐下',
        toBuyVending: '點擊 ~INPUT_CONTEXT~ 以 ${amount}購買 {title}',
        toOpenBank: '按 ~INPUT_CONTEXT~ 打開銀行界面',
        toDumpCar: '按 ~INPUT_CONTEXT~ 移交到垃圾填埋地',
        toStopPusing: '按 ~INPUT_CONTEXT~ 停止推送',
        toPickPhone: '按 ~INPUT_CONTEXT~ 接聽呼叫',
        toCollectPresent: '按 ~INPUT_CONTEXT~ 收取禮物',
        toEnterElevator: '按 ~INPUT_CONTEXT~ 進入電梯',
        toUseGarage: '按 ~INPUT_CONTEXT~ 使用車庫'
    },
    interaction: {
        putFromHandToTrunk: '放入後備箱',
        lockDoors: '門連鎖',
        hood: '兜帽',
        trunk: '主幹',
        doors: '門',
        cancelRent: '取消出租',
        trunkItems: '中繼內容',
        getInTrunk: '到後備箱裏去',
        throwFromTrunk: '扔出行李箱',
        jerrycan: '罐子',
        carFractions: '汽車分數',
        carFamilies: '家庭用車',
        repairCar: '修理車子',
        replaceBattery: '更換電池',
        repalceOil: '更換油',
        replaceLock: '修理門鎖',
        pullPlayer: '退出玩家 {i}',
        vehDuplicateKey: '複製鑰匙',
        changeVehLock: '更改鎖定',
        carIllegal: '車輛非法',
        showDocs: '顯示文檔',
        giveMoney: '轉帳金額',
        showMedLic: '顯示醫療證書',
        showArmyLic: '顯示服兵役證書',
        showLic: '顯示授權',
        greet: '打招呼。',
        emotional: '情感的',
        requestThrowDice: '提供擲骰子',
        giveKey: '把鑰匙傳給我',
        trade: '交易',
        moveInHouse: '添加到房源',
        moveInApartment: '新增至公寓',
        carrying: '抱在懷裏',
        rescue: '復蘇',
        illegal: '非法的',
        fractions: '分數',
        families: 'FAMILY',
        search: '搜索次數',
        nextRadio: '下一家廣播電臺',
        takeBoombox: '拾取吊杆',
        smokeHookah: '吹喇叭',
        takeHookah: '拾起hookah',
        bbqCook: 'BBQ Fry',
        takeBBQ: '拿起烤肉',
        takeTent: '拿起帳篷',
        megaphone: 'Megaphone',
        park: '公園',
        carMarket: 'Automarket',
        glovebox: '手套箱',
        seatbelt: '安全帶',
        ignition: '着火',
        hijackIgnition: '打破火焰',
        eject: '下車',
        backlight: '照明',
        windows: '擋風玻璃',
        radio: '收音機',
        frontLeft: '前左',
        frontRight: '前右',
        backLeft: '左後',
        backRight: '右後',
        disableCarRadio: '關閉收音機',
        setVehOnSale: '提交出售',
        cancelVehSale: '取消促銷',
        ejectPlayer: '放下玩家 {i}',
        itemsTrade: '物件交換',
        tradeVehicle: '出售交通工具',
        vehicleForRent: '租金交通',
        tradeHouse: '出售房屋',
        tradeApartment: '出售公寓',
        tradeBusiness: '賣掉業務',
        tradeAtm: '出售ATM機',
        tradeBillboard: '出售廣告牌',
        tradeProperty: '財產交換',
        houseWarehouseKey: '房屋儲藏室鑰匙',
        apartmentWarehouseKey: '公寓儲藏室的鑰匙',
        hijackDoorLock: '黑入門鎖',
        drillDoorLock: '拆分門鎖',
        breakWindow: '拆分窗口',
        robPlayer: '搶劫玩家',
        tieHands: '捆住手',
        tiePlayer: '連結播放器',
        escortPlayer: '鉛',
        gagPlayer: '密封口',
        putInTrunk: '把它放到後備箱裏',
        bagPlayer: '把袋子放上',
        cutRope: '剪掉繩子',
        cutScreeds: '剪裁系帶',
        ungagPlayer: '張開嘴巴',
        unbagPlayer: '從頭部取下包裝袋',
        uncuff: '取下手銬',
        gloveLock: '手套箱鎖',
        gloveItems: '手套箱內容物',
        kiss: '親吻',
        hug: '抱抱',
        five: '給我五分鐘。',
        slap: '扇我耳光',
        carryPlayer: '拾取 {id}',
        showID: '顯示ID',
        inviteFraction: '邀請加入陣營',
        uninviteFraction: '從派別中解僱',
        inviteFamily: '邀請加入家庭',
        uninviteFamily: '從家庭中解僱',
        breathalyzer: '呼吸器',
        arrest: '逮捕玩家',
        unescortPlayer: '鬆開播放器',
        takeMaskOff: '取消遮罩',
        cuff: '戴上手銬',
        unjail: '鬆開播放器',
        setWanted: '宣布BOLO',
        finePlayer: '寫出罰款',
        identikit: '素描',
        sellPill: '出售平板電腦',
        givePhysicalCertificate: '提供身體健康證明',
        giveMentalCertificate: '提供精神健康證明',
        diagnoseDisease: '診斷疾病',
        giveArmyCertificate: '簽發服兵役證書',
        takeArmyCertificate: '檢索服兵役證書',
        healPlayer: '治愈玩家',
        vaccinate: '接種疫苗',
        pushInCar: '把它塞進車裡',
        evacuateCar: '疏散到停車場',
        returnVehToOwner: '將車輛退還給車主',
        checkDocuments: '查看文件',
        checkInventory: '查看目錄',
        checkLics: '查看授權',
        fillFuel: '加油',
        giveLicWeapon: '部署武器',
        takeLicWeapon: '拿起武器',
        giveLicFishing: '釣魚問題',
        takeLicFishing: '釣魚',
        takeLicDriveD: '在Drive D上車',
        takeLicDriveM: '在Drive M上車',
        lics: '授權',
        more: '還有更多...',
        setFamily: '傳送給家人',
        unsetFamily: '從家人那裏接送',
        apiary: '帕塞卡',
        pacifyTheBees: '淬火蜜蜂',
        pushCar: '推送',
        snowball: '制作雪球',
        snowman: '做雪人',
        breakSnowman: '打破雪人',
        autopilot: 'Autopilot',
        radioList: {
            sunBloom: 'SunBloom',
            radiodanz: 'Radiodanz',
            jazz: '爵士',
            anime: 'Anime',
            road: '公路',
            hipHop: '嘻哈'
        }
    },
    family: {
        tag: '[Семья]',
        createFamilySuccess: '您已成功建立了一個名為 {name}的家庭！',
        updateFamilySuccess: '您已成功更新您的家庭！',
        notInFamily: '你不是家裏人！',
        alreadyInFamily: '你已經是家族的一份子了！',
        vehicleAlreadyInFamily: '車子已經屬於這個家族了！',
        less48HoursFamilyHouse: '家庭住宅的薪酬不到48小時。支付以避免刪除您的家人！',
        cantSetRentVehicle: '您不能製作已出租的家庭車輛！',
        vehicleNotInFamily: '車子不在家裡！',
        vehicleInAuction: '這是不可能轉移到一個家庭的汽車拍賣！',
        setVehicleFamilySuccess: '您已成功將交通工具 {vehicle} 轉移至家庭 {family}。',
        unsetVehicleFamilySuccess: '你從家庭 {family}接了 {vehicle} 次交通工具。',
        leaderUnsetVehicleFamilySuccess: 'Вы вернули транспорт {vehicle} обратно владельцу из семьи.',
        cantDumpFamilyCar: '你不能填埋家庭車輛！',
        cantDuplicateKeyFamilyVehicle: '您無法為家庭運輸複製鑰匙！',
        cantSellFamilyVehicle: '你不能賣一輛家庭車！',
        cantSellRentVehicle: '您不能出售已出租的車輛！',
        cantBuyFamilyVehicle: '你不能買一輛家庭車！',
        cantBuyRentVehicle: '您無法購買已出租的車輛！',
        cantRentFamilyVehicle: '你不能租一輛家庭車！',
        noaAccessToFunction: '您無法使用此功能！',
        needHomeToCreateFamily: '您必須有家才能建立家庭！',
        homeAlreadyTaken: '這棟房子已經涉及到另一個家庭！',
        mustHave4Garage: '要建立家庭，您需要至少有4個車庫的房子！',
        homeAlreadyInAuction: '您的房源不應被拍賣以建立家庭！',
        noMoneyToCreateFamily: '你沒有足夠的現金來創立一個家庭！必須有 ${price}。',
        familyTitleAlreadyTaken: '此名稱已被其他家庭使用！',
        familyTagAlreadyTaken: '此標籤已被其他家族使用！',
        userAlreadyHaveFamily: '你的角色之一已經有一個家庭！你不能創建另一個。',
        vehicleLimit: 'Достигнут лимит количества семейного транспорта ({carSlots} шт.)',
        houseSetLocked: '家庭住宅已關閉',
        houseSetUnlocked: '家庭住宅已開放',
        leaderCannotLeave: '領導人不能離開這個家族！',
        notFamilyLeader: '只有領袖才能刪除家庭！',
        notEmptyFamily: '您只能刪除沒有其他成員的家庭！',
        cannotRemoveWithHouse: '您不必擁有個人住宅就可以移除家庭！因此，家庭住宅可以成爲您的私人住宅。',
        notExists: '家族不存在！',
        youNotLeader: '你不是家族首領！',
        onValidation: '您不能更新系列，因爲更改已被驗證',
        badLogoExtension: '未知logo類型',
        noMoneyToUpdateFamily: '家族沒有足夠的資金來變更參數！您必須有 ${price}',
        delayForUpdate: '你可以每3天更新一次家庭設定',
        successRemove: '您已成功刪除您的家人。家族家園已被你佔有',
        houseAlreadyPaid: '家庭住宅的版權費用已提前 {amount} 天支付！',
        payRoyaltySuccess: '您已成功支付家庭住宅 {days} 天的特許權使用費，金額爲 {royalty} MC。',
        changeFamilyHouseSuccess: '您已成功將您的家庭住宅變更為#{home}，使用費為 {royalty} MC。',
        leaderRemoveYourRank: '[СЕМЬЯ] {name} 已刪除您的排名。你的排名現已設定為 {newRank}',
        adminLogoInteract: 'Администратор {status, select, 0{отклонил} 1{одобрил}} аватарку вашей семьи.',
        changeHouse: '{name} 將家庭住宅改為{home}號，支付 {amount} MC的版權費用。',
        leftHouse: '{name} 離開了家。',
        royaltyPay: '{name} 支付 {royalty} MC的 {days} 天版權費用。',
        returnVehicle: '領導者 {name} 將車輛 {title} 從家族歸還給車主。',
        tookFromBalance: '{name} 取自家族餘額 ${amount}。',
        putInBalance: '{name} 充值家庭平衡 ${amount}。',
        taxiJob: '{name} взял заказ такси на семейном автомобиле {title}. Начислено {familyPercent}% - ${amount}',
        truckerJob: '{name} выполнил рейс дальнобойщика на семейном автомобиле {title}. Начислено {familyPercent}% - ${amount}',
        transferVehicleToFamily: '{name} 已將他們的運輸工具 {title} 轉移給家人。',
        transferVehicleFromFamily: '{name} 從家裏拿走了他的交通工具 {title} 。',
        contracts: {
            item: '主旨',
            money: '錢',
            reputation: '聲望值',
            exp: '經驗',
            startContract: '您已開始合同 $t({title})。 ${money}從家族資產負債表中註銷。您可以在GPS -家庭合同中查看坐標。',
            playerStartContract: '{name} 已激活合同 $t({title})。 ${money}筆從家族資產負債表中註銷。',
            noAccesstoStart: '你的排名無法瀏覽合約！',
            notInFamilyHouse: '你必須在家中才能啟用合約！',
            startCooldown: '您將可以再次開始此合同 {date}！',
            alreadyStarted: '此合同已生效！',
            noMoneyToStart: '你家餘額裡沒有足夠的錢！合同值: ${price}。',
            actionContract: '您已成功交付 {usedUnits}。',
            actionContractNoItems: '你的存貨和背包中沒有所需的物品！',
            actionContractNoItemsInVehicle: '你沒有所需的道具在後備箱裡！',
            actionAlreadyPlayerLImit: '您已爲一位家庭成員提供了最大數量！其餘的都要由別人送來',
            contractFinished: '{name} 完成合同 $t({title})。',
            playerContractFinished: '您已成功完成合同 $t({title})。',
            actionVeryFast: '嘗試通過合約主體的次數太多。請再試一次。',
            onlyLevel3Plus: '只有達到3級的玩家才能通過合約道具！',
            contractInAnotherFamily: '你已經履行或正在履行這份合約，在另一個家族中，你的角色之一！再來一次，你可以 {cooldown}',
            names: {
                Jack: '傑克',
                Jacob: '雅各布。',
                Hugo: '雨果',
                John: 'John',
                Ben: '本。',
                Horace: '霍勒斯',
                Walt: '沃爾特'
            },
            list: {
                '1': {
                    'title': '魚日',
                    'description': '嘿，有個話題，我朋友在出口魚，魚魚在買貨他媽的知道他在哪裡有這麼多魚，但他必須知道自己的生意。嘗試給他一條鮭魚，針對小費，你將獲得20顆綠。',
                    'task': '帶80公斤魚魚到倉庫'
                },
                '2': {
                    'title': '婚宴',
                    'description': '您好，今天是嚴肅人士的婚禮，慶祝會在德爾佩羅海灘附近的一艘遊艇上舉行。但問題是，酒精耗盡了。這個等級的人更喜歡優質的酒精。Rockford Hills葡萄酒將非常有幫助。你準備好幫助我們了嗎？',
                    'task': '帶上10瓶洛克福德山度假村葡萄酒'
                },
                '3': {
                    'title': '有線電纜工',
                    'description': '轉移150箱電纜7.5公斤，履行合約需要5人。最多可接受每人30盒。',
                    'task': '帶來150盒'
                },
                '4': {
                    'title': '415 ，我是基地，回復。',
                    'description': '你好，我是無線電工程俱樂部的代表。我們正在處理無線電裝置和電子設備的確定。我們收到了加拿大的命令，但我需要無線電來做。你能找到民用無線電嗎？',
                    'task': '帶來10個收音機'
                },
                '5': {
                    'title': '自動解裝',
                    'description': '將150箱7.5公斤的零配件從被偷的汽車轉移到港口。合同需要5人。最多可接受1人30盒。',
                    'task': '帶來150盒零配件'
                },
                '6': {
                    'title': '我的',
                    'description': '輸送300份鐵礦。需要5人參加。最多可接待1人入住75人。',
                    'task': '運輸300鐵礦'
                }
            }
        }
    },
    blips: {
        mazeBankArena: '迷宮銀行競技場',
        autoschool: '駕駛學校',
        gym: 'Rocking Gear',
        carMarket: 'Automarket',
        market: '市場',
        golf: '高爾夫球',
        casino: '賭場',
        airport: 'Aeroport',
        auction: '拍賣',
        ferrisWheel: '摩天輪',
        bookmaker: '博彩公司',
        losSantosTuners: 'Los Santos Tuners',
        university: '大學',
        worldQuest: '作業',
        stipclub: '脫衣舞俱樂部',
        bar: '條形',
        junkyard: '垃圾填埋',
        boatStation: '船站',
        heliStation: '直升機停機坪',
        carRent: '租車',
        boatRent: '船舶租賃',
        busdriver: '巴士司機',
        builder: '生成器',
        mining: '礦工',
        trucker: 'Trucker',
        moneycollector: '收款人',
        garbagecollector: '垃圾車',
        taxi: '計程車公司',
        lumberjack: '伐木工',
        butchet: '屠夫',
        gopostal: '郵差',
        farmer: 'Farmer',
        fisherman: '漁夫',
        mushroomer: '蘑菇機',
        farm: '農場',
        startLocation: 'Spavn',
        mushrooms: '蘑菇',
        forest: '森林',
        drift: '漂移',
        church: '教會',
        repairShop: '汽車工作坊',
        apartments: '公寓',
        motoshop: '摩託沙龍',
        autoshop: '經銷商',
        ammoshop: '槍彈匣',
        barbershop: '理發店',
        carwash: '清洗',
        clothesshop: '服裝店',
        fuelstation: '加油站',
        itemshop: '全天候商店',
        tattooshop: 'Tattoosalon',
        tuningshop: '洛斯桑託斯海關',
        mobileshop: '通信室',
        facility: '公司',
        cinema: 'Cinema',
        arcade: '遊樂室',
        autoservice: '汽車服務',
        house: '首頁',
        flightSchool: '飛行學校',
        bizwar: '商業戰爭',
        materialWar: '材料戰爭',
        airdrop: '用品箱',
        facilityWar: '企業戰爭',
        bank: '本行',
        myHouse: '我的家',
        myFamily: '我的家人',
        dirtyToilet: '惡劣的馬桶',
        dirtyFloor: '骯亂的地板',
        dustyArea: '灰塵區域',
        overseer: '總監',
        unloadZone: '卸貨點',
        loadZone: '加載點',
        trashZone: '垃圾點',
        takeBox: '拿着盒子',
        throwTrashZone: '垃圾排出',
        fishingZone: '漁區',
        meatZone: '肉料理位置',
        jobStart: '入門指南',
        elevator: '升降舵',
        jobPoint: '操作穴位',
        checkpoint: '檢查點',
        hijack: '劫車',
        reinforcement: '增援',
        garage: '車庫',
        supply: '派送',
        office: '辦公室',
        craft: '手工制作',
        lspd: '洛斯桑託斯警察局',
        ems: '急救醫療服務',
        shpd: '治安部',
        sang: '聖安德烈亞斯國民警衛隊',
        gov: '政府部門',
        wn: 'Weazel News',
        fib: '聯邦調查局',
        bls: '巴拉斯黑手黨',
        vgs: 'Los Santos Vagos',
        fam: 'The Families',
        blds: '血腥黑幫',
        mrbg: 'Marabunta Grande',
        lcn: 'La Cosa Nostra',
        rm: '俄羅斯黑手黨',
        yak: 'Yakuza',
        mex: '墨西哥卡特爾',
        lost: '失落的MC',
        aod: '死亡天使MC',
        im: '愛爾蘭黑手黨',
        cookingZone: '烹飪場所'
    },
    labels: {
        facility: '$t({name})\n屋頂： {frationName}',
        dump: '向垃圾填埋地移交車輛',
        supply: '$t({title}): {rest} / {amount}',
        bizTitle: '$t({title}) [ID: {id}]',
        craft: '工藝點',
        loadUnload: '載貨/卸貨點',
        contract: '合同穴位'
    },
    tattoo: {
        ZONE_HEAD: '總統',
        ZONE_LEFT_LEG: '左腿',
        ZONE_RIGHT_LEG: '右腿',
        ZONE_LEFT_ARM: '左手',
        ZONE_RIGHT_ARM: '右手',
        ZONE_TORSO: '軀幹',
        remove: '去除刺青'
    },
    biz: {
        head: '頭飾',
        tops: '外套',
        undershirts: 'T恤',
        legs: '底部',
        shoes: '便宜女鞋',
        glasses: '眼鏡',
        watches: '腕表',
        bracelets: '手鐲',
        ears: '耳釘特價',
        masks: '遮罩',
        accessories: '便宜女裝',
        gloves: '手套',
        bags: '背包',
        torso: '軀幹',
        heads: '總統',
        leftHand: '左手',
        rightHand: '右手',
        leftFoot: '左腿',
        rightFoot: '右腿',
        remove: '去除刺青',
        hair: '髮型',
        eyecolor: '最佳數碼相機',
        facialhair: '胡子',
        eyebrows: '眉毛',
        chesthair: '身體毛髮',
        blush: '紅紅',
        face: '面部着色',
        makeup: '最佳化妝刷',
        lipstick: '最佳脣膏',
        styling: '樣式',
        technical: '技術調節',
        exclusive: '排他性',
        additional: '額外的',
        paintingList: '油漆工程',
        repair: '修復',
        washing: '清洗',
        wax: '蠟塗料'
    },
    ibuttons: {
        drillPower: '鑽機功率',
        drillPosition: '鑽孔位置',
        choosingPin: 'PINCODE SELECTION',
        selectPin: '選擇PIN碼',
        fight: '戰鬥',
        leave: '退出登錄',
        exit: '退出',
        increaseBet: '增加出價',
        decreaseBet: '降低出價',
        makeBet: '出價',
        more: '更多信息',
        enough: '夠了。',
        split: '拆分',
        double: 'DOUBLE',
        bets: '出價',
        camera: '相機',
        removeBet: '移除出價',
        start: '啟動',
        aim: '瞄準',
        hit: '命中',
        viewHit: '噴射視圖',
        restore: '還原',
        clubStick: '曲棍球棒',
        scoreCard: 'Scorecard',
        startGame: '開始遊戲',
        hijackLock: '挑一座城堡',
        hack: '黑客',
        stopChop: '停止砍伐',
        chop: '砍伐一棵樹',
        stopDig: '停止挖掘',
        dig: '提取礦石',
        stopSnowman: '停止雕刻',
        snowmanBuild: '雕刻雪景'
    },
    items: {
        executor: '$t({item})',
        clothes: {
            head: '帽子',
            tops: '頂部',
            undershirts: '背心上衣',
            legs: '底部',
            shoes: '便宜女鞋',
            glasses: '眼鏡',
            watches: '腕表/手鐲',
            bracelets: '手鐲',
            ears: '耳釘特價',
            masks: '口罩',
            accessories: '便宜女裝',
            armor: '防彈背心',
            decals: 'PATCHS',
            bags: '背包',
            gloves: '手套'
        },
        gloves: {
            '1': '皮革駕駛手套',
            '2': '皮手套',
            '3': '羊毛手套',
            '4': '羊毛手套',
            '5': '黃色工作手套',
            '6': '皮手套',
            '7': '綠色手套',
            '8': '有花紋的戰術手套',
            '9': '花紋手套',
            '10': '戰術手套',
            '11': '最佳運動手套',
            '12': '自定義手套'
        },
        tags: {
            documents: 'Docs',
            misc: '其他',
            autoParts: '汽車零件',
            medicine: '醫學',
            facilities: '設備',
            consumables: '耗材',
            ammunition: '彈藥',
            books: 'BOOKS',
            personals: '私人物品',
            products: '產品',
            materials: '材料',
            box: '箱子',
            clothesAndAccessories: '便宜女裝',
            rubbish: '垃圾',
            food: '食品',
            agriculture: '農業',
            tools: '工具',
            alcohol: '酒精',
            drugs: '麻醉品',
            ingredients: '成分',
            fish: '魚',
            accessories: '便宜女裝',
            quests: '任務',
            equipment: '歯輪',
            gameItems: '遊戲道具',
            jewelry: '珠寶',
            grandClub: '大俱樂部',
            plant: '植物',
            drugMaterials: '麻醉品材料'
        },
        1: {
            title: '身份證明文件',
            description: '身份證明文件'
        },
        2: {
            title: 'Cash Convolutions',
            description: '三包現金，視覺上是15 -20,000 ，不是為角色的手。'
        },
        3: {
            title: '累加器',
            description: '鋰離子電池，是汽車的零件，而不只是...'
        },
        4: {
            title: '過氧化氫S',
            description: '能夠阻止血液，並用作良好的防腐劑。該藥物用於割傷和受傷。'
        },
        5: {
            title: '現金補償',
            description: '可以兌現的現金。'
        },
        6: {
            title: '焊機',
            description: '用於建築、維修工程等區域。'
        },
        7: {
            title: '檔案',
            description: '切割工具，用於通過逐層切割加工材料。'
        },
        8: {
            title: '螺絲刀',
            description: '經過設計，可以用螺絲擰開和擰開緊固件。'
        },
        9: {
            title: '威士忌',
            description: '用於捲線和其他物品的消耗材料。'
        },
        10: {
            title: '工具包',
            description: '為家庭主人或開車者提供簡單的幫助。'
        },
        11: {
            title: '砍刀',
            description: '很長、很薄、很寬的刀。片面磨刀片，凸刀片，有時彎曲到刀片上。'
        },
        12: {
            title: '鉗子',
            description: '鎖匠工具，專為緊握和抓握而設計'
        },
        13: {
            title: '片罐',
            description: '空白'
        },
        14: {
            title: '掛鎖',
            description: '用於鎖門、蓋子、容器等。'
        },
        15: {
            title: '對講機',
            description: '便攜式收發器。'
        },
        16: {
            title: '咳嗽糖漿',
            description: '非常受歡迎的治療最常見疾病的方法。'
        },
        17: {
            title: '一瓶片',
            description: '空白'
        },
        18: {
            title: '股市# 1',
            description: '關於股市的最著名書籍之一。'
        },
        19: {
            title: '繩索',
            description: '由天然或人造繊維製成的織製或扭結緊固件。'
        },
        20: {
            title: '薄荷T恤',
            description: '空白'
        },
        21: {
            title: '種植勺',
            description: '在露天植物中移植、潛水和種植植物的完美工具。'
        },
        22: {
            title: '耳機保護器',
            description: '用於遮蓋耳朵和防護外部影響的設備。'
        },
        23: {
            title: 'EHC器械',
            description: '特殊裝置，控制：心臟肌肉的功能，並偵測器官工作中的異常。'
        },
        24: {
            title: '一包香煙',
            description: '厚紙包裝，通常含有12根香煙。'
        },
        25: {
            title: 'FIB ID',
            description: '證明所有者的身分、地位和責任的文件。'
        },
        26: {
            title: '機油',
            description: '用於引擎的發動機油。減少引擎零件的摩擦力並移除熱量。'
        },
        27: {
            title: '門鎖',
            description: '汽車門鎖。如果你打不開門，這是完美的解決方案！'
        },
        28: {
            title: '皮革公文包',
            description: '9月1日的時尚寬敞配件！'
        },
        29: {
            title: 'Estancia雪茄包',
            description: '紙包，內含6支雪茄。'
        },
        30: {
            title: '釣竿',
            description: '包括釣竿、釣魚線和釣魚設備，適用於釣魚。'
        },
        31: {
            title: '政府身分證',
            description: '證明所有者的身分、地位和責任的文件。'
        },
        32: {
            title: '威士忌',
            description: '加強膠帶用於密封接縫、管接頭、槽等。'
        },
        33: {
            title: '醫療用品',
            description: '一套醫療組件可以讓您收集各種藥物。'
        },
        34: {
            title: '皮套',
            description: '一個小袋子，裝個人火器。'
        },
        35: {
            title: '廢料',
            description: '用高品質硬化鋼鍛造的強化廢料，助你完成工作所需的額外桿子。'
        },
        36: {
            title: '呼吸器',
            description: '計量呼出空氣中酒精濃度的方法。'
        },
        37: {
            title: '9x19毫米',
            description: '盒子， 9毫米子彈。'
        },
        38: {
            title: '12Ga Buckshots',
            description: '榴彈槍盒， 12磅'
        },
        39: {
            title: '7.62x39毫米',
            description: '盒子，盒子7.62毫米。'
        },
        40: {
            title: '遮罩',
            description: '口罩'
        },
        41: {
            title: '底部',
            description: '腿；腿'
        },
        42: {
            title: '便宜女鞋',
            description: '鞋子'
        },
        43: {
            title: '便宜女裝',
            description: '配件'
        },
        44: {
            title: 'T恤',
            description: '打底衫'
        },
        45: {
            title: '外套',
            description: '上衣'
        },
        46: {
            title: '頭飾',
            description: '水頭；水頭'
        },
        47: {
            title: '眼鏡',
            description: '眼鏡'
        },
        48: {
            title: '耳釘特價',
            description: '耳朵'
        },
        49: {
            title: '腕表',
            description: '手表'
        },
        50: {
            title: '手鐲',
            description: '手鐲'
        },
        51: {
            title: 'PATCHS',
            description: '貼花'
        },
        52: {
            title: '防彈背心',
            description: '盔甲'
        },
        53: {
            title: '背包',
            description: '袋子'
        },
        54: {
            title: '手套',
            description: '手套'
        },
        55: {
            title: '累加器',
            description: '二手電池，比新電池便宜，但隨時都會破壞。'
        },
        56: {
            title: '累加器',
            description: '二手電池，比新電池便宜，但隨時都會破壞。'
        },
        57: {
            title: '防彈背心',
            description: '一個人個人保護的元素，為胴體上部提供部分保護。'
        },
        58: {
            title: 'BONG',
            description: '大麻和煙草吸煙裝置。'
        },
        59: {
            title: '打火機',
            description: '火焰生成裝置。'
        },
        60: {
            title: '技術材料',
            description: '一套武裝組件可以讓你收集全套武器。'
        },
        61: {
            title: 'RedWood Cigarettes',
            description: '一包空的香煙。'
        },
        62: {
            title: '一杯咖啡',
            description: '用油炸的種子制成的飲料。'
        },
        63: {
            title: '一杯咖啡',
            description: '一杯空的咖啡'
        },
        64: {
            title: '一罐藥',
            description: '用於疾病預防和治療的合成或天然來源的物質。'
        },
        65: {
            title: '伏特表',
            description: '用於決定電路中的電壓或EMF的直接讀取裝置。'
        },
        66: {
            title: '車鑰匙',
            description: '用於打開鎖的工具。'
        },
        67: {
            title: 'IPad平板',
            description: '具有觸摸屏的電子裝置，讓您可以控制電腦程序。'
        },
        68: {
            title: '牲畜飼料',
            description: '用於牲畜和牲畜的食品。'
        },
        69: {
            title: '5KW發電機',
            description: '發電設備。燃油類型：汽油。'
        },
        70: {
            title: '20kW發電機',
            description: '發電設備。燃油類型：柴油。'
        },
        71: {
            title: '25kW發電機',
            description: '發電設備。燃油類型：燃氣。'
        },
        72: {
            title: '150kW',
            description: '發電設備。燃油類型：柴油。'
        },
        73: {
            title: '汽缸S',
            description: '壓力過大的內部容器，用於在壓力下儲存壓縮、液化及溶解的氣體。'
        },
        74: {
            title: '汽缸M',
            description: '壓力過大的內部容器，用於在壓力下儲存壓縮、液化及溶解的氣體。'
        },
        75: {
            title: '汽缸L',
            description: '壓力過大的內部容器，用於在壓力下儲存壓縮、液化及溶解的氣體。'
        },
        76: {
            title: 'Boombox',
            description: '20世紀90年代的便攜式音響中心。'
        },
        77: {
            title: '汽油罐',
            description: '汽油罐，你可以給車輛加油。'
        },
        78: {
            title: '急救箱',
            description: '一套用於急救的繃帶材料、工具和器具。'
        },
        79: {
            title: '油漆罐',
            description: '主要在家使用的塗料噴霧裝置。'
        },
        80: {
            title: '輥筒',
            description: '主要在家使用的塗料噴霧裝置。'
        },
        81: {
            title: '油漆罐S',
            description: '物質的罐子，應用於表面以給予某種顏色。'
        },
        82: {
            title: '油漆罐M',
            description: '物質的罐子，應用於表面以給予某種顏色。'
        },
        83: {
            title: '油漆罐L',
            description: '物質的罐子，應用於表面以給予某種顏色。'
        },
        84: {
            title: '相機',
            description: '用於採集和固定材料物體的固定影像的裝置。'
        },
        85: {
            title: '一瓶羅姆人',
            description: '由甘蔗糖副制成的酒精飲料。'
        },
        86: {
            title: '帳篷',
            description: '為人類居住而設計的可崩潰結構。'
        },
        87: {
            title: '保溫帳篷',
            description: '為人類居住而設計的可崩潰結構。'
        },
        88: {
            title: '布藝帳篷',
            description: '為人類居住而設計的可崩潰結構。'
        },
        89: {
            title: '急救箱黃色',
            description: '一套用於急救的繃帶材料、工具和器具。'
        },
        90: {
            title: '衛兵卡片',
            description: '存取銀行金庫的卡片。'
        },
        91: {
            title: 'Tequila',
            description: '強烈的酒精飲料，透過蒸餾發酵的藍龍舌蘭汁而獲得。'
        },
        92: {
            title: '保加利亞語',
            description: '用於切割、研磨和研磨石材、金屬和其他材料的磨牀。'
        },
        93: {
            title: '螺栓刀',
            description: '用於擰/擰螺釘、自攻螺釘和其他緊固件的工具，以及鑽孔。'
        },
        94: {
            title: '消防員的斧子',
            description: '全金斧用拾音斧代替琴琴'
        },
        95: {
            title: '錘子',
            description: '用於敲釘、打碎物體和其他工程的衝擊工具。'
        },
        96: {
            title: '指甲投擲器',
            description: '工具用於敲打扣件，無需使用物理力。'
        },
        97: {
            title: '拾斧',
            description: '一種手持式衝擊工具，旨在破碎巖層。'
        },
        98: {
            title: 'RAKE',
            description: '用於打破已鬆開土壤的塊，清理挖掘出的雑草根，並在植物列之間輕鬆地鬆開後者的農具。'
        },
        99: {
            title: '鏟子',
            description: '您可以使用此工具挖掘、清理、移送土壤和其他鬆散材料。'
        },
        100: {
            title: 'Hoe',
            description: '用於耕種土地種子或水果的工具。'
        },
        101: {
            title: '大錘',
            description: '衝擊工具設計為極其強烈地打擊安裝或拆除結構。'
        },
        102: {
            title: '扳手',
            description: '螺栓、螺母和其他零件螺栓/擰開螺栓的工具。'
        },
        103: {
            title: '搜索提燈',
            description: '具有高亮度和高亮度範圍的燈具。這款電器有一個大型電池隔室。照明範圍–高達250米。'
        },
        104: {
            title: 'Nokia Phone',
            description: '一種來自90年代的裝置，用於以遠距發送和接收聲音。'
        },
        105: {
            title: '絕對伏特加',
            description: '由Vin&Sprit釀造的世界著名瑞典品牌伏特加。'
        },
        106: {
            title: 'Thermos',
            description: '用於保持食物溫度高或低的家用器具。可用作水箱。'
        },
        107: {
            title: 'Monoblock iMac',
            description: '一塊個人電腦。'
        },
        108: {
            title: 'Macbook',
            description: '筆記型電腦家族的一員。'
        },
        109: {
            title: '旅行包',
            description: '產品、布料或皮革，用於手上或肩膀上載物。'
        },
        110: {
            title: '旅行包',
            description: '產品、布料或皮革，用於手上或肩膀上載物。'
        },
        111: {
            title: '配有*物質*的袋子',
            description: '聚乙烯透明袋， *物料名稱*。'
        },
        112: {
            title: '手銬',
            description: '以鏈子相連的手鐲形式的物品，用於限制被拘留者的行動自由。'
        },
        113: {
            title: '背包',
            description: '專屬包包，可長時間背載載物。'
        },
        114: {
            title: '包含文件的文件夾',
            description: '包含資訊的有形物件的資料夾。'
        },
        115: {
            title: '雪茄',
            description: '可以原樣吸煙的圓柱煙草葉扭曲。'
        },
        116: {
            title: '警察收音機',
            description: '專業資訊傳輸裝置。'
        },
        117: {
            title: '聯合',
            description: '自捻或大麻香煙，由特殊香煙紙製成。'
        },
        118: {
            title: '縫紉機',
            description: '縫縫接合及整理材料之技術裝置。'
        },
        119: {
            title: '工藝面料',
            description: '織機上織織的織物，藉由互相垂直的糸系統製成。'
        },
        120: {
            title: 'Gunshot',
            description: '各式各樣的驚人元素，用於填充平滑孔武器的彈匣。'
        },
        121: {
            title: 'Beer Pißwasser',
            description: '一種架構化的德國啤酒出口品牌，經常在電臺和電視上廣告。'
        },
        122: {
            title: '零件輪胎',
            description: '輸送額外輪子以替換放氣或穿刺的輪子。'
        },
        123: {
            title: '扁平輪胎',
            description: '穿孔的車輪。'
        },
        124: {
            title: '抗病毒疫苗',
            description: '冠狀病毒（ COVID-19 ）藥物。'
        },
        125: {
            title: 'Gruppe Sechs',
            description: '私人保安公司的ID。'
        },
        126: {
            title: '大麻罐',
            description: '一種源自大麻的精神作用藥物。'
        },
        127: {
            title: '焊接面具',
            description: '焊接護罩，保護眼睛、臉部和頸部免受燒傷。'
        },
        128: {
            title: '焊機',
            description: '執行焊接的器械。'
        },
        129: {
            title: '破瓶',
            description: '破碎的玻璃瓶，可用來造成刺傷。'
        },
        130: {
            title: '匕首',
            description: '19世紀收藏匕首'
        },
        131: {
            title: '斧子',
            description: '一種將重金屬刀片放置在長可更換手柄上的切割工具。'
        },
        132: {
            title: '折疊刀',
            description: '他們有驚人的速度發現自己在另一個人的肋骨。折疊刀永遠不會過時。'
        },
        133: {
            title: '戰斧',
            description: '對於中世紀的步兵，現代的邊境警衛和頭部被撞的精神病人。'
        },
        134: {
            title: '自捻紙',
            description: '這是一張小紙，幫助它扭曲了一個真正的關節。'
        },
        135: {
            title: '麥克風麥克風',
            description: '將聲音振動轉換成電訊的電聲裝置。'
        },
        136: {
            title: '漢堡包',
            description: '一種三明治，包括切碎的炸煎餅，放在切碎的麵包內。'
        },
        137: {
            title: '人性化ID',
            description: '研究化學實驗室證書。'
        },
        138: {
            title: '日落ID',
            description: '清潔公司的證書。'
        },
        139: {
            title: '吉他',
            description: '弦捏樂器。'
        },
        140: {
            title: '路錐',
            description: '臨時道路標記設備。'
        },
        141: {
            title: '磷酸二銨',
            description: '一種融合了磷和氮的肥料，第一種元素要大得多。'
        },
        142: {
            title: '殘疾人椅子',
            description: '爲無法行動的人提供交通工具。'
        },
        143: {
            title: '配有鑽孔的袋子',
            description: '布料或皮革制品，可用於手中或肩膀上搬運物品。'
        },
        144: {
            title: '鑽機',
            description: '計劃用於鑽井的鑽井設備及設施複合體。'
        },
        145: {
            title: '鑽機',
            description: '計劃用於鑽井的鑽井設備及設施複合體。'
        },
        146: {
            title: '警方尖峯',
            description: '用於透過穿透輪胎來防止或阻止輪式車輛的移動的裝置。'
        },
        147: {
            title: '有錢的行李',
            description: '看來你很幸運，也許正好相反。'
        },
        148: {
            title: '錢包',
            description: '通常用於非法交易的金錢信封。'
        },
        149: {
            title: '警察盾牌',
            description: '一種旨在防止冷手、投擲和火器的武器。'
        },
        150: {
            title: '*物質*的罐子',
            description: '物質的描述。'
        },
        151: {
            title: '汽缸',
            description: '壓力過大的內部容器，用於在壓力下儲存壓縮、液化及溶解的氣體。'
        },
        152: {
            title: '含有*物質*的罐',
            description: '用鋼、塑料和其他材料製成的容器，有手柄。'
        },
        153: {
            title: '柴油罐',
            description: '用鋼、塑料和其他材料製成的容器，有手柄。'
        },
        154: {
            title: '電鋸',
            description: '一種設計執行各種切割操作的構造工具。'
        },
        155: {
            title: 'Shilo',
            description: '有手柄的針。'
        },
        156: {
            title: '過氧化氫L',
            description: '濃水溶液是爆炸性的。過氧化氫是一種很好的溶劑。'
        },
        157: {
            title: '運動鞋',
            description: '運動鞋。'
        },
        158: {
            title: '老本子',
            description: '14世紀出現的最古老的書籍之一。'
        },
        159: {
            title: '簿子',
            description: '第一本書的樣本。'
        },
        160: {
            title: '布拉尼啤酒罐',
            description: '使用焦糖麥芽釀造的深啤酒。'
        },
        161: {
            title: 'Logger啤酒罐',
            description: '使用焦糖麥芽釀造的深啤酒。'
        },
        162: {
            title: 'Sprunk罐',
            description: '碳酸軟飲料、青檸和檸檬口味。'
        },
        163: {
            title: '布拉尼啤酒罐',
            description: '空啤酒罐'
        },
        164: {
            title: 'Logger啤酒罐',
            description: '空啤酒罐'
        },
        165: {
            title: '牛奶',
            description: '由雌性哺乳動物的乳腺產生的營養液。'
        },
        166: {
            title: '燕麥片',
            description: '由雌性哺乳動物的乳腺產生的營養液。'
        },
        167: {
            title: '燕麥片',
            description: '由雌性哺乳動物的乳腺產生的營養液。'
        },
        168: {
            title: '阿奇黴素',
            description: '半合成抗生素。抗菌，抗病毒劑，作用廣泛。'
        },
        169: {
            title: '活性炭',
            description: '多孔物質由各種有機來源的含碳材料獲得。用於對抗中毒。'
        },
        170: {
            title: '可待因片[化學/成分]',
            description: '治療用藥片'
        },
        171: {
            title: '.50 BMG',
            description: '盒子與.50 BMG子彈。尤其是重步槍。'
        },
        172: {
            title: '5.56x45毫米',
            description: '墨盒， 5.56x45mm。'
        },
        173: {
            title: '收銀機',
            description: '設計用於現金交易機械化、帳戶現金收據、註冊貨物購買及列印現金收據。'
        },
        174: {
            title: '毛膏',
            description: '理發店消耗品。'
        },
        175: {
            title: 'Zhumaysynba洗髮精',
            description: '向kuzminkurbutbulesanba說頭屑！'
        },
        176: {
            title: 'Laboratory Balance',
            description: '高精度裝置，專為靜態測量低質量的物體和物質而設計。'
        },
        177: {
            title: '澆水罐',
            description: '最簡單的滴灌工具。現代的滴灌和微滴灌溉系統更先進，但效率也更高。'
        },
        178: {
            title: '制作表',
            description: '合成麻醉藥品的中等成分和器材。'
        },
        179: {
            title: '制作表',
            description: '合成麻醉藥品的最低限度成分和器材。'
        },
        180: {
            title: '手提包',
            description: '手提包，可用於攜帶手提電腦或小件物品。'
        },
        181: {
            title: '大型急救箱',
            description: '一套用於急救的繃帶材料、工具和器具。'
        },
        182: {
            title: '醫用手套',
            description: '一次性手套，是醫院衛生狀況的醫療用品。'
        },
        183: {
            title: '武器工作臺',
            description: '用金屬、木材和其他材料加工或製造產品的輕型工作臺。'
        },
        184: {
            title: '武器工作臺',
            description: '黑色工作臺，用於加工或制作由金屬、木材和其他材料製成的產品。'
        },
        185: {
            title: '武器工作臺',
            description: '用金屬、木材和其他材料加工或製造產品的小型輕型工作臺。'
        },
        186: {
            title: '武器工作臺',
            description: '小型深色桌面，用於加工或制作金屬、木材和其他材料。'
        },
        187: {
            title: '桌面端',
            description: '用金屬、木材和其他材料加工或製造產品的燈具桌。'
        },
        188: {
            title: '桌面端',
            description: '用金屬、木材和其他材料加工或製造產品的暗桌子。'
        },
        189: {
            title: '小桌面',
            description: '用金屬、木材和其他材料加工或製造產品的燈具桌。'
        },
        190: {
            title: '小桌面',
            description: '用金屬、木材和其他材料加工或製造產品的暗桌子。'
        },
        191: {
            title: '鑽孔機',
            description: '金屬和其他材料的加工孔設計。'
        },
        192: {
            title: '鑽孔機',
            description: '金屬和其他材料的加工孔設計。'
        },
        193: {
            title: 'XS工具箱',
            description: '整理手工工具和小型施工工具的最佳方案。'
        },
        194: {
            title: '工具箱S',
            description: '整理手工工具和小型施工工具的最佳方案。'
        },
        195: {
            title: '工具箱M',
            description: '整理手工工具和小型施工工具的最佳方案。'
        },
        196: {
            title: '工具箱L',
            description: '整理手工工具和小型施工工具的最佳方案。'
        },
        197: {
            title: 'Vise',
            description: '鎖匠或木匠工具，用於固定各種處理方式的零件：鋸、鑽、平整。'
        },
        198: {
            title: '拇指驅動器',
            description: '一種連接到電腦或其他USB讀取器的儲存裝置。'
        },
        199: {
            title: '藍色ATM',
            description: '該裝置旨在與使用支付卡一樣自動發行和/或接受現金。'
        },
        200: {
            title: '紅色ATM',
            description: '該裝置旨在與使用支付卡一樣自動發行和/或接受現金。'
        },
        201: { title: 'Sprats' },
        202: {
            title: '黃銅指尖',
            description: '用固體材料打拳的衝擊破碎武器。'
        },
        203: {
            title: '肉片',
            description: '最昂貴的肉，因為它們代表了最好的部分。'
        },
        204: {
            title: '汽車電纜',
            description: '由合成鋼或鋼鐵，或類似於繩索產品的混合股線扭曲或扭曲。'
        },
        205: {
            title: '摩託頭盔',
            description: '用固體材料打拳的衝擊破碎武器。'
        },
        206: {
            title: 'ECola罐',
            description: '一罐軟碳酸飲料。'
        },
        207: {
            title: '籃球',
            description: '主要用於運動遊戲的球形物件。'
        },
        208: {
            title: '棒球棒',
            description: '在一些運動遊戲中使用運動器材來嘗試打球。'
        },
        209: {
            title: '自制雞尾酒',
            description: '大家都知道自制雞尾酒。'
        },
        210: {
            title: '一包平板電腦',
            description: '一包平板電腦'
        },
        211: {
            title: '隕石巧克力',
            description: '大巧克力，消除飢餓。'
        },
        212: {
            title: 'EgoChaser巧克力',
            description: '小巧克力，消除飢餓。'
        },
        213: {
            title: '信用卡',
            description: '銀行卡-別忘了PIN碼。'
        },
        214: {
            title: 'Ps和Qs',
            description: '一包糖果糖果'
        },
        215: {
            title: '郡長部門編號',
            description: '證明所有者的身分、地位和責任的文件。'
        },
        216: {
            title: '警棍',
            description: '用於警察自衛或抑制流氓的目的。'
        },
        217: {
            title: 'Led手電筒',
            description: '黑暗中的一束光。'
        },
        218: {
            title: '毛蠟',
            description: '理發店消耗品。'
        },
        219: {
            title: '凝膠',
            description: '理發店消耗品。'
        },
        220: {
            title: '面霜',
            description: '理發店消耗品。'
        },
        221: {
            title: '毒品託盤',
            description: '空白'
        },
        222: {
            title: '鮭魚',
            description: '每個漁民都想要的獎品。這條魚非常漂亮，變幻莫測。它屬於三文魚科'
        },
        223: {
            title: '沙丁魚',
            description: '魚描述'
        },
        224: {
            title: 'Flounder',
            description: '海洋輻射性底魚，具有不對稱的扁平體，具有葉卵形或菱形形狀。特徵是眼睛在一邊的位置。'
        },
        225: {
            title: '金槍魚',
            description: '海洋掠食性魚類，鯖科。這種移棲物種一生可以穿越數千公裏的海洋。它在全世界不同的地區開採。'
        },
        226: {
            title: '錄音機',
            description: '聽音樂的磁帶錄音機。'
        },
        227: {
            title: '玉米罐',
            description: '由黃粒和白粒品種的糖玉米產生的罐頭食品。'
        },
        228: {
            title: '護士包',
            description: '一套醫療裝備，旨在為傷者和傷者提供急救。'
        },
        229: {
            title: '醫用容器',
            description: '醫療熱容器用於輸送人體器官。'
        },
        230: {
            title: '用於黑客入侵的筆記型電腦',
            description: '一臺筆記型電腦，擁有特殊的黑客程序和黑客程序。'
        },
        231: {
            title: '中繼器',
            description: '連接兩個以上互相遠距離的無線電發射機的設備。'
        },
        232: {
            title: '配備步槍的箱子',
            description: '有一套拆卸的步槍，配有光瞄準器和彈匣。'
        },
        233: {
            title: '線帶',
            description: '捆扎電線或人員的緊固件。'
        },
        234: {
            title: '太平洋起步',
            description: 'Banskovskaya卡/Pacific Standart Bank員工的身份證。'
        },
        235: {
            title: '衝擊鑽',
            description: '一款手持式電動工具，旨在為各種材料鑽孔。'
        },
        236: {
            title: '槍彈匣R',
            description: '彈匣是一個容器，可以將彈匣按某一順序放置。'
        },
        237: {
            title: '空的香煙包裝',
            description: '厚紙包裝，通常含20支香煙。'
        },
        238: {
            title: '指甲',
            description: '一種緊固件，其由頭部和具有尖端的桿組成。'
        },
        239: {
            title: '蘋果11',
            description: '由蘋果研發的一系列智慧型手機。'
        },
        240: {
            title: '醫療繃帶',
            description: '醫療裝置簡化了急救和幫助治療各類傷害。'
        },
        241: {
            title: '突擊步槍',
            description: '發射7.62x39mm彈。這款標準突擊步槍具有大容量彈匣和遠程精準度。'
        },
        242: {
            title: 'Bandana',
            description: '大尺寸的毯子或手帕。'
        },
        243: {
            title: '.50口徑手槍',
            description: '射出9x19mm子彈。防震手槍，提供巨大的能量，但具有極強的反擊力。彈匣裡有9個子彈。'
        },
        244: {
            title: '泰瑟',
            description: '發射一種能夠提供電壓的射彈，讓攻擊者暫時眩暈。'
        },
        245: {
            title: '短手槍',
            description: '射出9x19毫米子彈。像避孕套或噴霧劑一樣，它會放入您的口袋裡，供您在城市中過夜。'
        },
        246: {
            title: '狩獵刀',
            description: '刀具配有雙刃鋸刃，具有更高的穿透能力。'
        },
        247: {
            title: '短榴彈槍',
            description: '射出12GA Buckshots。單管短槍。在近距離戰鬥中具有令人驚嘆的效率。'
        },
        248: {
            title: '改良步槍',
            description: '射出5.56x45毫米子彈。所有突擊步槍中最輕鬆、最小型，精準度與威力無與倫比。'
        },
        249: {
            title: 'Colt 45 Mk2手槍',
            description: '射出9x19毫米子彈。防震手槍，提供巨大的能量，但具有極其強大的反擊力。'
        },
        250: {
            title: '泵榴彈炮',
            description: '射出12GA Buckshots。標準擲彈槍是近戰的理想選擇。高射彈的分布可彌補其低的精確度。'
        },
        251: {
            title: '空袋子',
            description: '我們可以把它放在別人的頭上。'
        },
        252: {
            title: '腎上腺素',
            description: '它用於逼玩家進入意識，並將生命值提升到最大。'
        },
        253: {
            title: '12ga步槍',
            description: '有槍的盒子。12條步槍子彈。射出的炮彈與子彈相似，射出的炮彈保留了能量。'
        },
        254: {
            title: '.45ACP',
            description: '墨盒， .45 ACP。'
        },
        255: {
            title: '穿甲手槍',
            description: '射出9x19mm子彈。高度穿透，全自動手槍。內含18個子彈。'
        },
        256: {
            title: '泵榴彈炮Mk2',
            description: '射出12GA Buckshots。反彈幾乎與子彈一樣致命。'
        },
        257: {
            title: '突擊彈槍',
            description: '射出12GA Buckshots。全自動擲彈槍，有8槍彈和高速射擊。'
        },
        258: {
            title: '雙筒榴彈槍',
            description: '射出12GA Buckshots。當你的槍讓一個人變成美麗的霧霧時，誰需要高速射擊？'
        },
        259: {
            title: '重型榴彈炮',
            description: '射出12ga步槍。當你需要在房間裡搞亂時，你需要使用的武器。'
        },
        260: {
            title: '重型手槍',
            description: '射出9x19毫米子彈。每次都提供精準和嚴重的前臂訓練。'
        },
        261: {
            title: '老式槍',
            description: '射出9x19毫米子彈。拿著這把雕刻的槍，在一場武裝搶劫中從人羣中脫穎而出。'
        },
        262: {
            title: '射手槍',
            description: '射出.357 Magnum彈藥。確保你認為自己會像射擊一樣重新上膛。'
        },
        263: {
            title: '左輪手槍',
            description: '射出.357 Magnum彈藥。如果你沒有子彈，槍的力量足以擊倒瘋狂的犀牛。'
        },
        264: {
            title: '左輪手槍MK2',
            description: '射出.357 Magnum彈藥。如果你能擡起它，這是你可以得到的最近拍攝人與貨車。'
        },
        265: {
            title: '舊式左輪手槍',
            description: '射出.45ACP口徑的子彈因為有時候復仇是連續六次最好的菜餚，就在眼睛之間。'
        },
        266: {
            title: 'SMG',
            description: '射出.45ACP口徑的子彈它被稱為全方位槍支-機關槍。重量輕，配有精確的瞄準器和30個遊覽彈匣容量。'
        },
        267: {
            title: 'SMG Mk2',
            description: '射出.45ACP口徑的子彈重量輕，緊湊，火速高。'
        },
        268: {
            title: '戰鬥PDW',
            description: '射出.45ACP口徑的子彈混合武器，主要基於SIG MPX ，但有不同的差異。'
        },
        269: {
            title: '小PP',
            description: '射出9x19mm的子彈。這個全自動鼓是雙引擎V8低音的小鼓。'
        },
        270: {
            title: '迷你SMG',
            description: '射出9x19mm子彈。隨著市場營銷團隊超越特殊部隊的視野，愈來愈受歡迎。'
        },
        271: {
            title: 'Mk2突擊步槍',
            description: '拍攝7.62x39mm彈匣。經典經典的最終修訂：你只需稍微工作一下，外觀最終會殺人。'
        },
        272: {
            title: '特種卡賓槍MK2',
            description: '射出5.56x45mm子彈。大師剛剛獲得嚴厲晉升：向大師鞠躬。'
        },
        273: {
            title: '特種卡賓幣',
            description: '射擊5.56x45mm彈。極其多用途的突擊步槍，適用於任何戰鬥情況。'
        },
        274: {
            title: 'Mk2 Carbine Rifle',
            description: '射出5.56x45毫米子彈。定制，火力，提供更多愛和關心的子彈。'
        },
        275: {
            title: '卡賓槍',
            description: '射出5.56x45mm子彈。結合遠程精準度與大容量彈匣。'
        },
        276: {
            title: 'Bullpup Rifle',
            description: '射出5.56x45mm子彈。這款最新的中國步槍在暴風雨中攻擊了美國，以其平衡的處理而聞名。'
        },
        277: {
            title: 'Bullpup Mk2 Rifle',
            description: '射出5.56x45mm子彈。如此精確，如此優雅，它不是一堆子彈，而是一首交響曲。'
        },
        278: {
            title: '小步槍',
            description: '射出7.62x39mm子彈。一半大小，全能，雙重反跳：沒有更多風險的方式說「我補償一些東西。'
        },
        279: {
            title: '重型狙擊步槍',
            description: '射出.50 BMG子彈。穿甲彈的功能，可造成重度傷害。激光瞄準器是標準的。'
        },
        280: {
            title: '榴彈發射器',
            description: '具有半自動功能的小型、輕鬆的榴彈發射器。最多可容納10個子彈。'
        },
        281: {
            title: '除顫器',
            description: '用於心律失常電脈衝治療的醫療裝置。'
        },
        282: {
            title: '.357 Magnum',
            description: '美國重型砲塔，由Smith & Wesson在1934年製作。'
        },
        283: {
            title: '使用授權的地圖',
            description: '該文件看起來像一張塑膠卡，讓您可以管理一種或多種類型的車輛。'
        },
        284: {
            title: '機械浮雕',
            description: '用於擠出塑料卡上的身份信息。'
        },
        285: {
            title: '迭片機',
            description: '允許用保護膜層覆蓋板材的設備。'
        },
        286: {
            title: '現金禮包',
            description: '禁用物品，現金包'
        },
        287: {
            title: '武器箱',
            description: '被禁武器案件。'
        },
        288: {
            title: '假護照',
            description: '偽造護照，禁止持有此物品。'
        },
        289: {
            title: '藥物書籤',
            description: '含有麻醉劑的小書籤，禁止存放此物品。'
        },
        290: {
            title: '含量單位：綠色',
            description: '擁有麻醉物質綠色的單位，最多可容納250個單位。'
        },
        291: {
            title: '含量單位：白色',
            description: '含有白色麻醉劑的街區最多可容納250個單位。'
        },
        292: {
            title: '西班牙卡瓦葡萄酒',
            description: '西班牙起泡酒。主要在加泰羅尼亞和巴倫西亞產生。'
        },
        293: {
            title: 'Stzopen啤酒',
            description: 'Schwarzbier啤酒'
        },
        294: {
            title: 'Bourgeois Cognac',
            description: '使用特殊技術從某些葡萄品種產生的強烈酒精飲料。'
        },
        295: {
            title: 'Nogo Premium伏特加',
            description: '一種強烈的酒精飲料，一種無色的水性酒精溶液，具有特色口感和酒精氣味。'
        },
        296: {
            title: '洛克福德山度假村葡萄酒',
            description: '酒精飲料，強度： 9—22% ，通過葡萄汁的全部或部分酒精發酵得到。'
        },
        297: {
            title: '綠色袋子',
            description: '聚乙烯透明袋與Green。'
        },
        298: {
            title: '藍色袋子',
            description: '聚乙烯透明袋與藍色。'
        },
        299: {
            title: '白色袋子',
            description: '聚乙烯透明白色袋子。'
        },
        300: {
            title: '寶藏：綠色',
            description: '藍色區塊，禁止存放此物品。'
        },
        301: {
            title: '寶藏：藍色',
            description: '用綠色封鎖，禁止存放此物品。'
        },
        302: {
            title: '寶藏與實質：白色',
            description: '請用白色封鎖，禁止存放此物品。'
        },
        303: {
            title: '包裝材料：藍色',
            description: '使用藍色麻醉物塊，最多可容納500個單位。'
        },
        304: {
            title: '武器',
            description: '一套武裝組件可以讓你收集全套武器。'
        },
        305: {
            title: '一套前體',
            description: '參與導致靶物質形成的反應的化學試劑。禁止存放此物品。'
        },
        306: {
            title: '軍隊編號',
            description: '證明所有者的身分、地位和責任的文件。'
        },
        307: {
            title: '警方身份證明文件',
            description: '證明所有者的身分、地位和責任的文件。'
        },
        308: {
            title: 'Standart Bank卡',
            description: '具有Standart套餐的銀行卡。'
        },
        309: {
            title: '高端信用卡',
            description: '擁有高級方案的銀行卡。'
        },
        310: {
            title: 'VIP銀行卡',
            description: '一張擁有VIP費用計劃的銀行卡。'
        },
        311: { title: '防毒面具' },
        312: {
            title: '賭場籌碼',
            description: '賭場鑽石老虎機籌碼'
        },
        313: {
            title: '玩骰子',
            description: '允許你與其他玩家競爭'
        },
        314: {
            title: '煙霧榴彈發射器',
            description: '具有半自動功能的小型、輕鬆的榴彈發射器。最多可容納10個子彈。'
        },
        315: {
            title: '重型防彈背心',
            description: '一個人的個別保護元素，為胴體上部提供更大的保護。'
        },
        316: {
            title: '誘餌',
            description: '釣餌。30片在一個包裝。'
        },
        317: {
            title: '彩票',
            description: '每日彩票。彩票在15: 00、18: 00和21: 00舉行。'
        },
        318: {
            title: 'Lemonovka samogon',
            description: '檸檬味的月光適量飲用，不會引起酒精中毒，製造商不明。'
        },
        319: {
            title: '攝像機',
            description: '視頻攝影機是結合鏡頭、形成視頻信號的裝置或數位視頻流的緊湊裝置。'
        },
        320: {
            title: '麥克風麥克風',
            description: '例如，雙向麥克風適用於記錄坐在對面的兩個人之間的對話。'
        },
        321: {
            title: '醫療證書',
            description: '身心健康證明'
        },
        322: {
            title: '交鑰匙工件',
            description: '您可以從中製作車輛、家居或倉庫的鑰匙'
        },
        323: {
            title: 'XIN包',
            description: '一塊傷痕的香煙，包裝上的標記寫著Xin'
        },
        324: {
            title: 'EMS ID',
            description: '證明所有者的身分、地位和責任的文件。'
        },
        325: {
            title: 'Weazel News ID',
            description: '證明所有者的身分、地位和責任的文件。'
        },
        326: {
            title: 'Thompson衝鋒炮',
            description: '射出.45ACP口徑的子彈當卡在羅斯福窗外或與條紋西裝配合時，看起來很棒。'
        },
        327: {
            title: '高爾夫球杆',
            description: '中等長度的高爾夫球杆，搭配中等鐵，橡膠握把，適合短期遊戲。'
        },
        328: {
            title: 'Mk2重型狙擊步槍',
            description: '射出.50 BMG子彈。穿甲彈的功能，可造成重度傷害。激光瞄準器是標準的。'
        },
        329: {
            title: '狙擊步槍',
            description: '射出.50 BMG子彈。穿甲彈的功能，可造成重度傷害。激光瞄準器是標準的。'
        },
        330: {
            title: '戰鬥榴彈槍',
            description: '射出12GA Buckshots。有多少有效的防暴工具可以塞到褲子裏？'
        },
        331: {
            title: '卡拉什尼科夫機槍',
            description: '射出7.62x39mm子彈。通用機槍，結合堅固的設計和可靠的性能。'
        },
        332: {
            title: '手持機槍',
            description: '射出5.56x45mm子彈。重量輕、緊湊的機關槍，帶有優秀的操控性與高開火率。'
        },
        333: {
            title: 'Mk2軽機炮',
            description: '射出5.56x45mm子彈。你再好不過了'
        },
        334: {
            title: '美國軍隊IRD',
            description: '服兵役者的個別口糧。'
        },
        335: {
            title: 'Vape',
            description: '生成高度分散的吸入用蒸汽的電子裝置'
        },
        336: {
            title: 'Hookah',
            description: '過濾和冷卻吸入煙霧的吸煙器具'
        },
        337: {
            title: 'Marksman Mk2 Rifle',
            description: '射出7.62x39mm子彈。這支步槍在軍團中被稱為「移位器」，會摧毀所有敵人。'
        },
        338: {
            title: '甜甜圈',
            description: '最喜歡的兒童和國家機構代表的款待'
        },
        339: {
            title: '糖漿薄煎餅',
            description: '現代化和不變的蛋糕口感，帶有1965年舊的好食譜的黃油糖漿。爲什麼是第65年？聽起來更值得尊敬'
        },
        340: {
            title: '奶昔',
            description: '以牛奶和冰淇淋爲基礎的甜品。對待自己或成人女朋友。'
        },
        341: {
            title: '吐司坑站',
            description: '用白面包用香腸和奶酪烤面包。非常適合外出吃零食。主要是看路。'
        },
        342: {
            title: 'Shawarma',
            description: '這是一道經典的中東菜餚，非常容易在旅途中享用。在美國，「購買10只沐浴露並收集一隻狗」行動不起作用。'
        },
        343: {
            title: '凱撒沙拉',
            description: '每個人都喜歡在路邊咖啡館吃最濃鬱的沙拉。在制作過程中，沒有皇帝受傷，算是吧。'
        },
        344: {
            title: '大牛排',
            description: '「GrandClub」廚師的招牌牛排。時尚，令人滿意，快捷，不價格。'
        },
        345: {
            title: '櫻桃派',
            description: '一片櫻桃派，搭配美國的愛情調味。建議在合理的範圍內吸煙。'
        },
        346: {
            title: '重型防彈背心',
            description: '一個人的個別保護元素，為胴體上部提供更大的保護。'
        },
        347: {
            title: '重型防彈背心',
            description: '一個人的個別保護元素，為胴體上部提供更大的保護。'
        },
        348: {
            title: '水碧鹼',
            description: '海底耀眼的魚。'
        },
        349: {
            title: '金魚',
            description: '真正的寶藏'
        },
        350: {
            title: '紅色士兵',
            description: '每個漁民都想要的獎品。'
        },
        351: {
            title: '卡拉斯',
            description: '是一種蜥蜴科的蜥蜴。'
        },
        352: {
            title: '防彈背心',
            description: '顏色：桑迪。個人防護元素，提供胴體上部的部分防護。'
        },
        353: {
            title: '防彈背心',
            description: '顏色：深灰色。提供胴體上部部分保護的個人防護元素。'
        },
        354: {
            title: '防彈背心',
            description: '顏色：淡綠色。個人防護元件，提供胴體上部的部分防護。'
        },
        355: {
            title: '防彈背心',
            description: '顏色：紫色。提供胴體上部部分保護的個人保護元素。'
        },
        356: {
            title: '防彈背心',
            description: '顏色：黃色。提供胴體上部部分保護的個人保護元件。'
        },
        357: {
            title: '防彈背心',
            description: '顏色：綠色。提供胴體上部部分保護的個人保護元素。'
        },
        358: {
            title: '防彈背心',
            description: '顏色：勃艮第。提供胴體上部部分保護的個人保護元件。'
        },
        359: {
            title: '防彈背心',
            description: '顏色：藍色。提供胴體上部部分保護的個人保護元件。'
        },
        360: {
            title: '防彈背心',
            description: '顏色：白色。個人防護元件，提供胴體上部的部分防護。'
        },
        361: {
            title: '防彈背心',
            description: '顏色：黑色。提供胴體上部部分保護的個人保護元件。'
        },
        362: {
            title: '玫瑰',
            description: '紅玫瑰，把玫瑰給你的靈魂伴侶，贏得一個屁股'
        },
        363: {
            title: '雨傘',
            description: '遮陽傘，妥善使用時保持幹燥'
        },
        364: {
            title: '泰迪熊',
            description: '泰迪熊焦慮。曾經是一個女孩的完美禮物'
        },
        365: {
            title: '降落傘',
            description: '用織物製成的傘形裝置。放慢物體在空中的移動速度。'
        },
        366: {
            title: '配有鑽孔的袋子',
            description: '布料或皮革制品，可用於手中或肩膀上搬運物品。'
        },
        367: {
            title: '配有鑽孔的袋子',
            description: '布料或皮革制品，可用於手中或肩膀上搬運物品。'
        },
        368: {
            title: '配有鑽孔的袋子',
            description: '布料或皮革制品，可用於手中或肩膀上搬運物品。'
        },
        369: {
            title: '配有鑽孔的袋子',
            description: '布料或皮革制品，可用於手中或肩膀上搬運物品。'
        },
        370: {
            title: '配有鑽孔的袋子',
            description: '布料或皮革制品，可用於手中或肩膀上搬運物品。'
        },
        371: {
            title: '配有鑽孔的袋子',
            description: '布料或皮革制品，可用於手中或肩膀上搬運物品。'
        },
        372: {
            title: '配有鑽孔的袋子',
            description: '布料或皮革制品，可用於手中或肩膀上搬運物品。'
        },
        373: {
            title: '配有鑽孔的袋子',
            description: '布料或皮革制品，可用於手中或肩膀上搬運物品。'
        },
        374: {
            title: '配有鑽孔的袋子',
            description: '布料或皮革制品，可用於手中或肩膀上搬運物品。'
        },
        375: {
            title: '配有鑽孔的袋子',
            description: '布料或皮革制品，可用於手中或肩膀上搬運物品。'
        },
        376: {
            title: 'BBQ',
            description: '您可以在會議上烹煮公羊肉的燒烤場。'
        },
        377: {
            title: '炸魚',
            description: '油炸魚，柔嫩可口的魚，舔手指'
        },
        378: {
            title: '橙色',
            description: '柑橘水果，不最酸，甚至甜。'
        },
        379: {
            title: '土豆',
            description: '要是有土豆，幹嘛還要吃白菜？'
        },
        380: {
            title: '土豆種子',
            description: '一包小塊土豆，都是為了避免吃白菜'
        },
        381: {
            title: '卷心菜',
            description: '要是有土豆，幹嘛還要吃白菜？'
        },
        382: {
            title: '卷心菜種子',
            description: '一包卷心菜種子，都是爲了讓你的悄悄者聞起來很美味'
        },
        383: {
            title: '南瓜',
            description: '剪掉你的眼睛，放上蠟燭嚇唬人。'
        },
        384: {
            title: '南瓜籽',
            description: '一包南瓜種子，傑克在等你！'
        },
        385: {
            title: '玉米',
            description: '甜玉米和氣態玉米！'
        },
        386: {
            title: '玉米種子',
            description: '一包玉米籽，生長你的蜘蛛！'
        },
        387: {
            title: '小麥',
            description: '從小米栽培品種的水果中得到的燕麥，通過剝皮從刺片中解放出來'
        },
        388: {
            title: '小麥種子',
            description: '嗯，就像普通的小米叫做種子'
        },
        389: {
            title: '香蕉',
            description: '你肚子餓了就不是你了'
        },
        390: {
            title: '香蕉種子',
            description: '種子，種一根美味的香蕉'
        },
        391: {
            title: '橙鯉',
            description: '源自Amur鯉魚亜種的魚。'
        },
        392: {
            title: '醃灌木',
            description: '一棵灌木，那種無害的植物瀕於滅絕的邊緣，因為警察。'
        },
        393: {
            title: '綠色種子',
            description: '種子既不簡單也不黃金，可以栽種'
        },
        394: {
            title: '煙花盒1',
            description: '在慶祝、節日期間燃燒各種粉末組成時獲得的彩色裝飾燈'
        },
        395: {
            title: '煙花盒2',
            description: '在慶祝、節日期間燃燒各種粉末組成時獲得的彩色裝飾燈'
        },
        396: {
            title: '煙火盒3',
            description: '在慶祝、節日期間燃燒各種粉末組成時獲得的彩色裝飾燈'
        },
        397: {
            title: '煙花盒4',
            description: '在慶祝、節日期間燃燒各種粉末組成時獲得的彩色裝飾燈'
        },
        398: {
            title: '普通香檳',
            description: '一種香蕉蘑菇。又名真正的香檳'
        },
        399: {
            title: '牡蠣蘑菇',
            description: '牡蠣科食用牡蠣蘑菇'
        },
        400: {
            title: '交叉石膏',
            description: '東亞生長的真菌。也種植在歐洲的溫和氣候'
        },
        401: {
            title: 'Flyweed',
            description: '白斑紅帽毒蘑菇'
        },
        402: {
            title: '扶手',
            description: '一種不同尋常的孢子，通常有羊羣居住。在收集這種蘑菇時，請保持警惕，你可能會被Ondatra咬傷。'
        },
        403: {
            title: 'Subbirch',
            description: 'Leccinum屬的一羣真菌物種的通用名稱，以前被分爲Boletus scaber。'
        },
        404: {
            title: 'Irret 715',
            description: '含有合成物質的吸入器，將消費者帶入涅槃狀態。據說，在藥物的作用期間，有些人能聽到奇怪的聲音。成分和來源不明'
        },
        405: {
            title: '突擊式SMG',
            description: '射出.45ACP口徑的子彈大功率衝鋒槍，同時緊湊又輕鬆。一彈匣最多可容納30個子彈。'
        },
        406: {
            title: '甜品籃',
            description: '在家中收集糖果的特別筐子！'
        },
        407: {
            title: '巧克力瓷磚',
            description: '美味的巧克力吧！'
        },
        408: {
            title: '焦糖糖',
            description: '美味的焦糖糖！確保不要粘在一起'
        },
        409: {
            title: 'KitKat糖果',
            description: '牛奶巧克力配酥脆鬆餅'
        },
        410: {
            title: '傑克有趣的南瓜',
            description: '南瓜手電筒是萬聖節最能辨識的屬性之一'
        },
        411: {
            title: '傑克的邪惡南瓜',
            description: '南瓜手電筒是萬聖節最能辨識的屬性之一'
        },
        412: {
            title: '骷髏',
            description: '人體裝飾頭骨'
        },
        413: {
            title: '望遠鏡',
            description: '成爲跟蹤者的完美道具'
        },
        414: {
            title: 'Turbo解碼器',
            description: '專用裝置，用戶可以安全、無損的方式幾乎即時開鎖。'
        },
        415: {
            title: 'ECU Programmer',
            description: '重編程電子控制單元的專業工具。'
        },
        416: {
            title: 'Cable I',
            description: '一條專屬於程式製程器的電纜，用於連接車輛的ECU。'
        },
        417: {
            title: 'Cable II',
            description: '一條專屬於程式製程器的電纜，用於連接車輛的ECU。'
        },
        418: {
            title: 'Cable III',
            description: '一條專屬於程式製程器的電纜，用於連接車輛的ECU。'
        },
        419: {
            title: 'Cable IV',
            description: '一條專屬於程式製程器的電纜，用於連接車輛的ECU。'
        },
        420: {
            title: 'Cable V',
            description: '一條專屬於程式製程器的電纜，用於連接車輛的ECU。'
        },
        421: {
            title: 'Cable VI',
            description: '一條專屬於程式製程器的電纜，用於連接車輛的ECU。'
        },
        422: {
            title: 'Cable VII',
            description: '一條專屬於程式製程器的電纜，用於連接車輛的ECU。'
        },
        423: {
            title: 'Cable VIII',
            description: '一條專屬於程式製程器的電纜，用於連接車輛的ECU。'
        },
        424: {
            title: '糖果拐杖',
            description: '固體焦糖棒呈拐杖形。它可以在市場上的交易商那裡兌換。'
        },
        425: {
            title: '新年舞會',
            description: '聖誕樹裝飾最受歡迎的玩具之一。它可以在市場上的交易商那裡兌換。'
        },
        426: {
            title: 'Festoon',
            description: '裝飾性聖誕飾品。可以在商家市場兌換。'
        },
        427: {
            title: '小禮物',
            description: '開啟新年禮物即可獲得獎品。'
        },
        428: {
            title: '中等禮物',
            description: '開啟新年禮物即可獲得獎品。'
        },
        429: {
            title: '很棒的禮物',
            description: '開啟新年禮物即可獲得獎品。'
        },
        430: {
            title: '海軍左輪手槍',
            description: '射出.357 Magnum彈藥。你想知道西方是如何被徵服的-慢速重裝和一堆流血。'
        },
        431: {
            title: '佩裏科的手槍',
            description: '射出.357 Magnum彈藥。'
        },
        432: {
            title: '戰鬥榴彈槍II',
            description: '射出12GA Buckshots。反彈幾乎與子彈一樣致命。'
        },
        433: {
            title: '軍用步槍',
            description: '射出5.56x45mm子彈。大師剛剛獲得嚴厲晉升：向大師鞠躬。'
        },
        434: {
            title: '金蘑菇',
            description: '一種含有化學元素Au的真菌種類，由真菌與金變種而獲得。'
        },
        435: {
            title: 'Grand Cola',
            description: 'Signature Cola GrandClub系列。時尚的設計和「超現實的味道」不會讓你無所謂。'
        },
        436: {
            title: '打火機“Grand Club”',
            description: '金屬汽油防風打火機，由加盟公司「大俱樂部」製造。'
        },
        437: {
            title: 'Viola Blood',
            description: '瓶子裏有神祕的紫色物質標籤上的碑文已被長時間刪除，上面寫的內容也不清楚。自行承擔風險。'
        },
        438: {
            title: 'Tequila “Grand Club”',
            description: '傳奇的GrandCluba龍舌蘭酒是一款優質的老金龍舌蘭酒。龍舌蘭酒具有令人愉快的甜美口感和強烈的香味，具有特有的未煮熟的龍舌蘭酒色調。'
        },
        439: {
            title: '可彈吉他',
            description: '拔弦器v2.'
        },
        440: {
            title: '打鼓',
            description: '來自鼓家族的樂器。'
        },
        441: {
            title: 'Fleeca Bank Plan',
            description: '擁有存貨中的銀行計劃，你可以開始銀行搶劫。您需要在銀行分行附近，然後致電********。'
        },
        442: {
            title: '電子門線',
            description: '透過這個電纜和程式製程器，你可以破解受保護物體的電子門，例如在銀行。'
        },
        443: {
            title: 'DRIL 1500W',
            description: '有了這個鑽頭，你可以破解進受保護的細胞，例如，在罐子裡。'
        },
        444: {
            title: 'Fleeca鑰匙和卡片',
            description: '提供銀行保險庫門的存取權限。'
        },
        445: {
            title: '溫度單位',
            description: '熱密化單位。它可用於金屬的點熔。'
        },
        446: {
            title: '金塊',
            description: '根據現行標準製造的精煉金條（從雜質中淨化）。'
        },
        447: {
            title: '鑽石骷髏',
            description: '十八世紀鑽石裝飾的白金頭骨'
        },
        448: {
            title: '彪馬雕像',
            description: '此圖由有色金屬的一件式合金製成。'
        },
        449: {
            title: '古板',
            description: '扁平陶瓷桌面產品'
        },
        450: {
            title: '瑞士腕表系列',
            description: '瑞士製作的腕錶系列，由有色合金製成，裝飾有寶石。'
        },
        451: {
            title: '警用無人機',
            description: '無人機是一種小型無人機，原本僅供軍用。'
        },
        452: {
            title: '無人機',
            description: '無人機是一種小型無人機，原本僅供軍用。'
        },
        453: {
            title: 'Sheriff重型防彈背心',
            description: '一個人個人保護的元素，為胴體上部提供部分保護。'
        },
        454: {
            title: 'LSPD重型防彈背心',
            description: '一個人個人保護的元素，為胴體上部提供部分保護。'
        },
        455: {
            title: 'FIB重型防彈背心',
            description: '一個人個人保護的元素，為胴體上部提供部分保護。'
        },
        456: {
            title: '警長輕裝甲卸載',
            description: '一個人個人保護的元素，為胴體上部提供部分保護。'
        },
        457: {
            title: 'LSPD輕型裝甲卸載',
            description: '一個人個人保護的元素，為胴體上部提供部分保護。'
        },
        458: {
            title: 'FIB輕型裝甲卸載',
            description: '一個人個人保護的元素，為胴體上部提供部分保護。'
        },
        459: {
            title: '警長輕裝甲卸載',
            description: '卸載背心與預訂。背心是用特種部隊的最佳做法制作的。'
        },
        460: {
            title: 'LSPD輕型裝甲卸載',
            description: '卸載背心與預訂。背心是用特種部隊的最佳做法制作的。'
        },
        461: {
            title: 'FIB輕型裝甲卸載',
            description: '卸載背心與預訂。背心是用特種部隊的最佳做法制作的。'
        },
        462: {
            title: 'FIB重型盔甲卸載',
            description: '卸載背心與裝甲板。包含很多不同的總計。'
        },
        463: {
            title: 'LSPD重型盔甲卸載',
            description: '卸載背心與裝甲板。包含很多不同的總計。'
        },
        464: {
            title: '尚重甲卸',
            description: '卸載背心與裝甲板。包含很多不同的總計。'
        },
        465: {
            title: '卸重盔甲警官',
            description: '卸載背心與裝甲板。包含很多不同的總計。'
        },
        466: {
            title: '鬆木日志',
            description: '用於圓形的圓形木材範圍，但細粒礦架除外'
        },
        467: {
            title: '橡木日志',
            description: '用於圓形的圓形木材範圍，但細粒礦架除外'
        },
        468: {
            title: '白樺原木',
            description: '用於圓形的圓形木材範圍，但細粒礦架除外'
        },
        469: {
            title: 'Maple日志',
            description: '用於圓形的圓形木材範圍，但細粒礦架除外'
        },
        470: {
            title: '金錐',
            description: '在光澤植物的枝梢發展成覆有薄片的小地塊的改良芽'
        },
        471: {
            title: '橡皮雞',
            description: '吱吱！'
        },
        472: {
            title: '微型SMG',
            description: '射出.45ACP口徑的子彈它被稱為全方位槍支-機關槍。重量輕，配有精確的瞄準器和30個遊覽彈匣容量。'
        },
        473: {
            title: '錢包',
            description: '允許您儲存鑰匙和ID'
        },
        474: {
            title: '車輛號碼',
            description: '車輛的州註冊牌照'
        },
        475: {
            title: 'Simcard',
            description: '已連結至電話號碼'
        },
        476: {
            title: '9x19毫米炮彈盒',
            description: '彈藥盒。在組織的車庫卸貨。'
        },
        477: {
            title: '12Ga Buckshots子彈盒',
            description: '彈藥盒。在組織的車庫卸貨。'
        },
        478: {
            title: '彈藥箱12ga步槍',
            description: '彈藥盒。在組織的車庫卸貨。'
        },
        479: {
            title: '.045 ACP',
            description: '彈藥盒。在組織的車庫卸貨。'
        },
        480: {
            title: '.50 BMG',
            description: '彈藥盒。在組織的車庫卸貨。'
        },
        481: {
            title: '.357 Magnum',
            description: '彈藥盒。在組織的車庫卸貨。'
        },
        482: {
            title: '5.56x45毫米炮彈盒',
            description: '彈藥盒。在組織的車庫卸貨。'
        },
        483: {
            title: '7.62x39毫米',
            description: '彈藥盒。在組織的車庫卸貨。'
        },
        484: {
            title: '配有武器的箱子"泵彈炮Mk2"',
            description: '武器箱，在組織的車庫卸貨。'
        },
        485: {
            title: '武器盒子 ',
            description: '武器箱，在組織的車庫卸貨。'
        },
        486: {
            title: '改良步槍武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        487: {
            title: 'Mk2突擊步槍武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        488: {
            title: '突擊型中小型武器武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        489: {
            title: '武器盒子 ',
            description: '武器箱，在組織的車庫卸貨。'
        },
        490: {
            title: 'Bullpup Mk2步槍盒',
            description: '武器箱，在組織的車庫卸貨。'
        },
        491: {
            title: '“彈匣步槍”武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        492: {
            title: 'Mk2 Carbine Rifle武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        493: {
            title: '重型榴彈槍武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        494: {
            title: 'Marksman Mk2步槍盒',
            description: '武器箱，在組織的車庫卸貨。'
        },
        495: {
            title: '卡拉什尼科夫機槍箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        496: {
            title: '軍用步槍武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        497: {
            title: '戰鬥PDW武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        498: {
            title: '"Pistol.50"炮箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        499: {
            title: 'SMG Mk2武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        500: {
            title: '"特種卡賓槍MK2"武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        501: {
            title: 'Thompson Submachine Gun武器盒',
            description: '武器箱，在組織的車庫卸貨。'
        },
        502: {
            title: '疫苗箱',
            description: '藥箱，把它卸在組織的車庫裡。'
        },
        503: {
            title: '可待因片箱',
            description: '藥箱，把它卸在組織的車庫裡。'
        },
        504: {
            title: '腎上腺素箱',
            description: '藥箱，把它卸在組織的車庫裡。'
        },
        505: {
            title: '阿奇黴素盒',
            description: '藥箱，把它卸在組織的車庫裡。'
        },
        506: {
            title: '配有除顫器的盒子',
            description: '藥箱，把它卸在組織的車庫裡。'
        },
        507: {
            title: '激活的碳盒',
            description: '藥箱，把它卸在組織的車庫裡。'
        },
        508: {
            title: '食品庫鑰匙',
            description: '在房子或公寓內開設食品庫的工具。'
        },
        509: {
            title: '丙酮',
            description: '有機物，飽和酮的最簡單代表。可用於制造毒品。'
        },
        510: {
            title: '丙二醇',
            description: '無色粘稠液體，具有弱特性臭味。可用於制造毒品。'
        },
        511: {
            title: '藍色容器',
            description: '一個帶有藍色物質的大實驗室容器。方便存放或運輸。'
        },
        512: {
            title: '瀝青酸',
            description: '促成鹹形成的酸。可用於制造毒品。'
        },
        513: {
            title: '正磷酸',
            description: '中等強度的無機酸。可用於制造毒品。'
        },
        514: {
            title: '僞麻黃鹼盒',
            description: '用於治療支氣管哮喘的藥劑。可用於制造毒品。'
        },
        515: {
            title: '苯甲酸鈉',
            description: '苯甲酸鈉。白色粉末，無味或略有苯甲醛味。可用於制造毒品。'
        },
        516: {
            title: '過氧化氫',
            description: '濃水溶液是爆炸性的。過氧化氫是一種很好的溶劑。可用於制造毒品。'
        },
        517: {
            title: '甲苯',
            description: '具有特色氣味的無色可移動揮發性液體，顯示出弱的麻醉效果。可用於制造毒品。'
        },
        518: {
            title: '老式雪茄盒',
            description: '十支精選的古巴雪茄證明其材料優勢。製作- GrandClub'
        },
        519: {
            title: '大咖啡',
            description: '令人愉快，經典拿鐵。招牌味道和GrandClub食譜。'
        },
        520: {
            title: '冰淇淋「夏日清爽」',
            description: '在炎熱的夏日，全家最喜歡的美食。'
        },
        521: {
            title: '法布拉',
            description: '一件極其奇怪的神器，握在手中，你會感受到微微的震動。來源不明。'
        },
        522: {
            title: 'D1E硬',
            description: '一個代碼為「頑固」的祕密實驗發展。含有合成物質的吸入器實際上是一種戰鬥刺激物。積極影響戰士的耐力、反應速度和攻擊性，但可能會導致成癮。成分和來源不明。'
        },
        523: {
            title: 'PaNa CEA',
            description: '極其罕見和分類的發展是一種強大的神經刺激物。合成物耐用吸入器主要用於抑制恐懼和疼痛。與同行不同， PaNa CEA不上癮。GrandLeon獨家製作。'
        },
        524: {
            title: '神經生物素X',
            description: '吸入器，耐用，合成物濃度增加。它是一種輕鬆但不穩定的精神健康神經刺激物，用於手術預防傷口和傷痕，並具有止痛特性。可能的副作用：代謝紊亂、胰腺炎、糖尿病和成癮。成分和來源不明。'
        },
        525: {
            title: 'BIOLINK',
            description: '其生產複雜，吸入器在耐用的裝置中，合成物濃度增加。它是腎上腺素受體的刺激物，具有顯著的再生作用，阻礙內部出血，具有鎮靜特性。制造商： GrandClub Corp.'
        },
        526: {
            title: '老式望遠鏡',
            description: '精美的手工Stimpunk雙筒望遠鏡。'
        },
        527: {
            title: '有毒威士忌',
            description: '在加勒比島附近的海底發現了一個非常神祕的噴水器。誰和何時製作，來自未知。自行承擔使用風險。我們警告你，可能會有致命的結果。'
        },
        528: {
            title: '駱駝香煙',
            description: '很舊的一包流行的香煙。他們自1913年起在美國產生。這包保存得很好的駱駝香煙，可以應付舊衛兵的愉快和懷舊風味。'
        },
        529: {
            title: 'Old Cola',
            description: '這個罐子至少有100年了。想像這些年來這麼一條響尾蛇是多麼可怕您可以自行承擔風險，也可以像收藏罐一樣放在家裡的架子上。'
        },
        530: {
            title: '復古急救箱',
            description: '相信你的健康民間或更準確地說，古代醫學！這個急救包的內容足以修復你的傷口並讓你重新排隊，士兵！'
        },
        531: {
            title: '復古午餐盒',
            description: '儘管外觀，這個破舊、生鏽的盒子裡面有兩個人可以忍受的食物。好吧，如果你不想分享，你會有一個非常豐盛的午餐和晚餐。至於盒子本身，爭議並非完全罕見。'
        },
        532: {
            title: 'Lite啤酒',
            description: '很久以前，一艘貨船在巴勒託灣附近沉沒，載著大量酒精。最近有人把一大盒過期啤酒擡到水面上，這裡只有這個派對很不尋常。製造商和系列未知，最有可能的是某種獨家訂購的遊客商店。'
        },
        533: {
            title: '蝴蝶在岸上',
            description: '一隻迷人的蝴蝶被困在罐子裡，發出藍色的光芒。看看它，你可以瀏覽關於未來、過去的思想，並且一般地，讓人想起關於存在的哲學思想。這一切都是如此的神祕...'
        },
        534: {
            title: '古代手稿',
            description: '這本厚重的古代書籍包含古老的知識、故事、資訊或其他可能對狹隘的人和科學家圈子有用的資訊。我不認為你會理解這些，但有一點機會找到關心這本手稿的人。'
        },
        535: {
            title: '混沌硬幣',
            description: '就像死亡的痕跡，這個東西顯然吸引了麻煩。硬幣的起源是濃霧遮蔽的，但有人會顯然欣賞它的不祥氣息。'
        },
        536: {
            title: '失效的內存阻止',
            description: '它以前是64TB硬盤，很酷的東西。事實上，這是垃圾，但誰知道，可能有工匠可以從這些遺骸中得到有用的東西。'
        },
        537: {
            title: '中式牛奶',
            description: '這樣的乳品在中國有很多，但這個包裝已經到達您的手中。我怎麼知道...'
        },
        538: {
            title: '猥褻窺癖',
            description: '伏特加和咖啡利口酒是這種飲料的主要成分，而在這種飲料下，你最有可能被吸引到偷窺的地方。'
        },
        539: {
            title: '墳墓香煙',
            description: '為什麼要抽普通的煙，因為他們是如此難以死。墳墓-這是通往墳墓的直線路徑！只要一口，你就會在墳墓裡感到緊張。太棒了，不是嗎？'
        },
        540: {
            title: '加密貨幣芯片',
            description: '這些芯片儲存加密加密貨幣。'
        },
        541: {
            title: '超耐用的容器',
            description: '裏面有件很重要的事無法在沒有特殊加密鑰匙的情況下開啟容器，而且暴力會損壞和貶值內容。'
        },
        542: {
            title: '機密文件',
            description: '此處可能會儲存某人的祕密、情報或企業資訊。也許這只是一封情書。信封上有印章，最好不要碰它。信封顯然很有價值，但對誰來說？'
        },
        543: {
            title: '數據倉庫- 16 TB',
            description: '此硬碟上儲存了加密資料。'
        },
        544: {
            title: '紅色和白色卡拉花束',
            description: '一束漂亮的白色卡拉花'
        },
        545: {
            title: '黃色和白色卡拉花束',
            description: '一束漂亮的白色卡拉花'
        },
        546: {
            title: '橙黃胡椒花束',
            description: '一束漂亮的白色卡拉花'
        },
        547: {
            title: '白玫瑰花束',
            description: '玫瑰花香'
        },
        548: {
            title: '粉色玫瑰花束',
            description: '粉玫瑰花束'
        },
        549: {
            title: '防彈背心箱',
            description: '一箱戰鬥裝備。在組織的車庫卸貨。'
        },
        550: {
            title: '重型防彈背心箱',
            description: '一箱戰鬥裝備。在組織的車庫卸貨。'
        },
        551: {
            title: '輕型裝甲卸貨箱',
            description: '一箱戰鬥裝備。在組織的車庫卸貨。'
        },
        552: {
            title: '重型裝甲卸貨箱',
            description: '一箱戰鬥裝備。在組織的車庫卸貨。'
        },
        553: {
            title: '狙擊步槍武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        554: {
            title: '戰鬥榴槍II武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        555: {
            title: '配有警用無人機的箱子',
            description: '技術盒。在組織的車庫卸貨。'
        },
        556: {
            title: '美國軍隊IRD盒',
            description: '雜貨箱。在組織的車庫卸貨。'
        },
        557: {
            title: '重型狙擊步槍武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        558: {
            title: '重型狙擊步槍Mk2槍盒',
            description: '武器箱，在組織的車庫卸貨。'
        },
        559: {
            title: '「突擊步槍」武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        560: {
            title: '左輪手槍武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        561: {
            title: '迷你SMG武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        562: {
            title: '抽屜，帶鑽孔1500W',
            description: '技術盒。在組織的車庫卸貨。'
        },
        563: {
            title: '配有“手持機槍”武器的箱子',
            description: '武器箱，在組織的車庫卸貨。'
        },
        564: {
            title: '“Mk2”輕機槍武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        },
        565: {
            title: '有手銬的盒子',
            description: '雜貨箱。在組織的車庫卸貨。'
        },
        566: {
            title: '警棍盒',
            description: '雜貨箱。在組織的車庫卸貨。'
        },
        567: {
            title: '泰瑟盒',
            description: '雜貨箱。在組織的車庫卸貨。'
        },
        568: {
            title: '兵役證書',
            description: '兵役證書'
        },
        569: {
            title: '蠕蟲',
            description: '適合誘餌的蠕蟲。但也可以吃嗎？'
        },
        570: {
            title: '刺激器箱過期',
            description: '醫療箱，在組織的車庫卸貨。'
        },
        571: {
            title: '刺激器過期',
            description: '刺激器將玩家逼入意識，已過期。'
        },
        572: {
            title: '一次性除顫器箱',
            description: '醫療箱，在組織的車庫卸貨。'
        },
        573: {
            title: '已注銷的除顫器',
            description: '用於心律失常電脈衝治療的醫療裝置。'
        },
        574: {
            title: '鐵礦',
            description: '沉積巖，礦物質，最有價值的燃料和化學原料，而不只是。'
        },
        575: {
            title: '銀礦石',
            description: '含有鐵及其化合物的天然礦物組織，當從這些組織的工業提取鐵是方便的時候。'
        },
        576: {
            title: '銅礦',
            description: '含有銅的天然礦物組成，其化合物及濃度在技術上可行且經濟上可行。'
        },
        577: {
            title: '錫礦',
            description: '含有錫的天然礦物組織及其工業使用在技術上可能且經濟上可行的濃度。'
        },
        578: {
            title: '金礦',
            description: '含有鉛的天然礦物層，其所含的化合物及濃度在技術上可能且經濟上可行。'
        },
        579: {
            title: '金屬探測器',
            description: '一種電子裝置，其允許偵測中性或弱導電環境中的金屬物體，因為其導電性。'
        },
        580: {
            title: '零件箱',
            description: '包含物品'
        },
        581: {
            title: '盒子',
            description: '包含物品'
        },
        582: {
            title: '文件卷積',
            description: '包含物品'
        },
        583: {
            title: '電子抽屜',
            description: '包含物品'
        },
        584: {
            title: '盒子',
            description: '包含物品'
        },
        585: {
            title: '煙囪',
            description: '一種用煙薰蜂的裝置，讓蜂蟲平靜下來'
        },
        586: {
            title: '帕塞卡',
            description: '特殊設備的蜜蜂養蜂場所'
        },
        587: {
            title: '蜂子宮',
            description: '有完善生殖器的女性'
        },
        588: {
            title: '隧道',
            description: '公公蜜蜂，需要蜜蜂'
        },
        589: {
            title: '蜂巢',
            description: '由草坪蜜蜂產生'
        },
        590: {
            title: '格洛克P80',
            description: '射出.45ACP子彈'
        },
        591: {
            title: '雪花',
            description: '爲你帶來新年心情'
        },
        592: {
            title: '德拉吉',
            description: '量度器具'
        },
        593: {
            title: '聖誕樹',
            description: '為慶祝新年而創造的一棵樹。'
        },
        594: {
            title: '跳舞炸彈',
            description: '一枚炸彈，讓每個人都忘記一切，開始跳舞的音樂蝙蝠。'
        },
        595: {
            title: '重步槍',
            description: '發射7.62x39mm彈。這款標準突擊步槍具有大容量彈匣和遠程精準度。'
        },
        596: {
            title: '重型步槍武器箱',
            description: '武器箱，在組織的車庫卸貨。'
        }
    },
    party: {
        general: {
            playerJoin: '玩家 {playerName} 加入羣',
            inviteDeclined: '玩家拒絕加入羣組的邀請',
            ownerNotOnline: '羣組所有者已離線',
            youNotOwner: '你不是組長',
            playerRemoveSuccess: '你已成功將玩家從羣組中停用',
            ownerRemoveYou: '羣主把你踢出去了',
            youLeave: '您已退出羣',
            youMakeNewOwner: '您已成功指派新的組長',
            youNewOwner: '您已被授予組織領導權',
            nowYouNewOwner: '你已成為組織的新領導人',
            cantSetYourself: '您不能指定自己',
            alreadyInParty: '玩家已加入羣',
            sentInviteSuccess: '您已成功向團體發送邀請',
            notWorkingInHere: '{name} не {gender, select, 0{устроен} 1{устроена}} на эту работу',
            deleteGroupSuccess: '您已成功解散羣',
            leaderDeletedGroup: '首領解散了組織',
            leaderCanStartOnly: '只有羣主才能開始工作',
            jobInGroupAlredyStarted: '羣組中的工作已經開始',
            tooMuchPlayersForStart: '參與者太多，無法開始活動（最多4名參與者）',
            tooMuchPlayersForStart2: '參與者太多，無法開始任務',
            tooMuchPlayersForStart3: '此活動的參與者太多（最多4名參與者）',
            notEnoughMembersToStartJob: '參與者不足，無法開始任務',
            voteInProgress: '開始工作要投票',
            notInJob: '{players} не {playersLength, plural, =1{устроен} other{устроены}} на эту работу',
            noBodyHasLic: '沒有成員有執照 {lic}',
            notNear: '{playersNotNear} {playersNotNear, plural, =1{участник} =2{участника} other{участников}} не находятся рядом с вами',
            leftGroup: '{name} 退出羣',
            votePlayer: '{name} {response, select, accept{принял} other{отклонил}} свой голос для участия в работе',
            finishAndLeaveGroup: '您已成功完成工作並退出羣',
            canRentNow: '你現在可以租車開始',
            cancelJob: 'Вы действительно хотите перестать работать?\nЕсли вы откажетесь, то покинете группу {isLeader, select, 1{и ваше лидерство передастся другому}}',
            wrongTypeQuest: '未知作業類型'
        },
        dialog: {
            inviteTitle: '邀請加入羣組',
            inviteBody: '玩家 #{ownerId} 邀請您加入羣組',
            accept: '立即加入',
            refuse: '拒絕'
        },
        garbageCollector: {
            title: '垃圾工的作品',
            voteJob: '開始投票開始做拾荒者'
        },
        gopostal: {
            title: '郵差的工作',
            voteJob: '投票開始做郵差'
        },
        moneycollector: {
            title: '收集器操作',
            voteJob: '投票已開始作為發票收集者'
        }
    },
    seasonPass: {
        tasks: {
            greetPlayers: {
                title: '球員的問候',
                description: '跟玩家打招呼'
            },
            bringGifts: {
                title: '收取禮物',
                description: '收集和帶上樹枝內容'
            },
            eatBurgers: {
                title: '我的漢堡王',
                description: '吃漢堡'
            },
            userMedkits: {
                title: '大人物。',
                description: '使用急救箱恢復健康'
            },
            useFireworks: {
                title: '火工技工',
                description: '設定煙花費'
            },
            smokeSomething: {
                title: '史努比狗狗',
                description: '抽點東西'
            },
            fuelVehicle: {
                title: '空罐',
                description: '給車輛加油'
            },
            buyJerrycan: {
                title: '燃料不足',
                description: '在加油站購買一個罐子'
            },
            emotionToPlayer: {
                title: '情緒不在榜單上',
                description: '向玩家表達您的情緒'
            },
            tradeItems: {
                title: '物料交換器',
                description: '與玩家交換道具'
            },
            casinoRoulette: {
                title: '回合投注扇',
                description: '在賭場贏得競賽投注'
            },
            casinoBlackjack: {
                title: '黑傑克投注扇',
                description: '在賭場贏得黑傑克投注'
            },
            casinoHorserace: {
                title: '賽馬愛好者',
                description: '在賭場比賽中贏得賭注'
            },
            casinoSlots: {
                title: '在老虎機的業餘投注',
                description: '在賭場的老虎機中贏得投注'
            },
            metalDetectorDig: {
                title: '寶藏獵人',
                description: '使用金屬探測器尋找並挖掘寶藏'
            },
            jobTrucker: {
                title: 'Trucker',
                description: '在卡車運貨人工作地點完成飛行'
            },
            jobMushroomer: {
                title: '蘑菇機',
                description: '在森林中收集蘑菇'
            },
            jobMoneycollector: {
                title: '收款人',
                description: '在出租辦公室完成航班'
            },
            jobFishing: {
                title: '漁夫',
                description: '釣魚之旅'
            },
            jobLumberjack: {
                title: '伐木工',
                description: '砍掉森林裏的原木'
            },
            jobGopostal: {
                title: '郵差',
                description: '在郵差辦公室完成航班'
            },
            jobGargagecollector: {
                title: '垃圾車',
                description: '在垃圾車上完成旅程'
            },
            throwSnows: {
                title: '標記的聖誕老人',
                description: '收集雪球，扔到玩家身上'
            },
            drinkAlcohol: {
                title: 'Alkash 100LVL',
                description: '喝酒'
            },
            playMinigames: {
                title: '迷你遊戲玩家',
                description: '在競技場戰爭中玩迷你遊戲'
            },
            useGym: {
                title: '強壯的手',
                description: '用搖臂'
            },
            buyInMarket: {
                title: '市場中的購買者',
                description: '在市場上買點東西'
            },
            sellInMarket: {
                title: '市場供應商',
                description: '在市場上銷售一些東西'
            },
            rentVehicle: {
                title: '租車愛好者',
                description: '租車'
            },
            carService: {
                title: '汽車服務',
                description: '使用汽車服務中的車輛呼叫服務'
            },
            useTattoo: {
                title: '刺青藝術家',
                description: '在刺青店裡紋身'
            },
            callUsingPhone: {
                title: '打電話給朋友',
                description: '打電話或接聽電話'
            },
            smsUsingPhone: {
                title: '愉快的消息',
                description: '使用電話撰寫訊息'
            },
            useCinema: {
                title: '大型影院',
                description: '開啟電影院中的任何影片'
            },
            casinoLuckyWheel: {
                title: '幸運就在我身旁',
                description: '在賭場中捲動運氣輪'
            },
            useCarWash: {
                title: '乾淨整潔',
                description: '使用洗車服務'
            },
            winDice: {
                title: '骨骼',
                description: '贏得一個骰子遊戲'
            },
            buyInBarbershop: {
                title: 'Stylaga',
                description: '在理髮店買點東西'
            },
            buyInClothesShop: {
                title: '你打扮得像地獄現在',
                description: '在服裝店買點東西'
            },
            useBBQ: {
                title: 'Паблындос',
                description: '烤一些東西，像魚'
            },
            workInTaxi: {
                title: '計程車司機',
                description: '在做計程車司機的同時，帶玩家前往目的地'
            },
            harvestFarmer: {
                title: 'Farmer',
                description: '收割農場上的任何作物'
            },
            stripDance: {
                title: '18 +',
                description: '在脫衣舞俱樂部訂購私人舞蹈'
            }
        },
        alreadyClaimed: '你已經贏得這個獎品！',
        itemNotFound: '找不到獎品！',
        claimItemSuccess: '您已成功贏得獎品！',
        dustItemSuccess: 'Вы успешно распылили приз и получили {amount} XP!',
        claimVehicleSuccess: '您已成功收到 {title}！你可以在Donat辦公室找到一輛車！',
        alreadyHasVip: '你已經有更好的VIP狀態！',
        getVIPSuccess: 'Вы успешно получили {title} VIP аккаунт! Срок действия - до {date}',
        itemIsHidden: '你不能與未知的獎勵互動！等着它打開！',
        hiddenItem: '獎勵已隱藏',
        noPremium: '你尚未購買優質戰鬥通行證！',
        seasonPassEnded: '賽季通行證已完成！',
        alreadyHasPremium: '你已經購買了高級季節通行證！',
        buyPremiumSuccess: '你已成功購買 {price} MCoin的高級季節通行證！',
        buyXPSuccess: '您已成功購買 {price} 枚MCoin的 {amount} XP ！'
    },
    luckyPhone: {
        success: '你發現了一個快樂的手機，並且成功將 ${amount} 收入你的銀行帳戶！在地圖上搜尋這樣的手機，賺取更多收入。',
        noBank: '由於銀行帳戶有問題，你沒有透過快樂的手機收到款項。您的銀行帳戶可能已被封鎖或不存在。'
    },
    testDrive: { timeBarLabel: '直到完成' },
    discord: {
        adminJoin: '{login} #{staticId} [{adminLevel}]進入遊戲',
        adminLeft: '{login} #{staticId} [{adminLevel}]退出比賽',
        adminsOnline: '聯機管理員: {admins}\n',
        noAdminsOnline: '管理員不在聯機， < @ &{role}>',
        statuses: {
            born: '天生',
            wandering: '漫遊世界',
            ammoshop: '在槍支店。',
            autoshop: '在經銷商',
            barbershop: '在理髮店',
            clothesshop: '在服裝店裡',
            bar: '在酒吧裏',
            fueling: '加油',
            inMetro: '乘坐地鐵',
            phoneshop: '在通信室',
            itemsshop: '店內全天候',
            tattooshop: '在刺青店裏',
            onTestDrive: '在testdrive {title}上',
            cheating: '稀疏石',
            dead: '他死了',
            blackjack: '正在播放"黑傑克"',
            horserace: '賽馬',
            luckywheel: '扮演幸運之輪',
            poker: '玩撲克牌',
            roulette: '播放回合',
            slots: '播放老虎機',
            golf: '打高爾夫球',
            pumpkinCollect: '收集南瓜',
            bookCollect: '收藏書籍',
            snowmanCollect: '聚集雪人',
            bugershop: '在漢堡房',
            orangeCollect: '收集橙子',
            vegetableCollect: '收集蔬菜',
            fishing: '釣魚',
            oreMining: '收集礦物質',
            mushroomCollect: '收集蘑菇',
            tuningshop: '在調諧中',
            vendingMachine: '喝一杯',
            pianoPlaying: '他彈琴',
            workingOnJob: '在某處工作',
            builderJob: '作爲生成器工作',
            burcherJob: '做屠夫',
            floorwashJob: '作為清潔人員',
            garbageCollectorJob: '營運垃圾車',
            gopostalJob: '從事郵差工作',
            moneycollectorJob: '充當收錢人',
            repairing: '已修復',
            gym: '已行使'
        }
    },
    wasted: {
        title: '已花費',
        deathMessages: {
            selfDeath: '你自殺了',
            justDeath: '你死定了',
            justKilled: '宰了你',
            melee: {
                first: '親手戰鬥殺了你',
                second: '我揍了你一頓',
                third: '我揍了你一頓',
                fourth: '我打你。',
                fifth: '宰了你'
            },
            molotov: {
                first: '把你燒了。',
                second: '他把你甩了',
                third: '我做了你Berbeck。'
            },
            knife: {
                first: '他捅了你一刀',
                second: '他捅了你一刀',
                third: '我把你挖出來了'
            },
            pistol: {
                first: '射中你',
                second: '拍你掌。'
            },
            smg: {
                first: '用機器槍殺了你',
                second: '我打中你了',
                third: '他殺了你。'
            },
            rifle: {
                first: '用步槍殺了你',
                second: '我把你放下。',
                third: '把你甩了',
                fourth: '我把你放下。'
            },
            mg: {
                first: '在P.E.殺了你',
                second: '我畫了你。',
                third: '你被打扮了'
            },
            shotgun: {
                first: '我把鉛在你身上。',
                second: '我把你撕碎了',
                third: '他摧毀了你。'
            },
            sniper: {
                first: '用狙擊手殺了你',
                second: '爲您制作了360毫克的無作用域。',
                third: '我把你幹掉了'
            },
            heavy: {
                first: '用機槍摧毀了你。',
                second: '我清理你。'
            },
            minigun: {
                first: '用迷你槍殺了你',
                second: '我把你撕碎了',
                third: '把你從大地上抹去',
                fourth: '我操了你'
            },
            explosive: {
                first: '我炸死你',
                second: '發生了爆炸。'
            },
            rotor: { first: '我砍你。' },
            flatten: { first: '我把你扁了' }
        }
    },
    illness: {
        cold: '流感',
        poisoning: '中毒',
        covid: '冠狀病毒'
    },
    dialogs: {
        salvatoreNuestoName: 'Salvatore Nuesto',
        managingLspDescription: 'LSP管理器',
        chargeReadyAnswerQuestionsBotmessage: '嗨，我是這裡的掌權者。我準備好回答你的所有問題了。',
        placeAnswers: '這是什麼地方？',
        Answers: '有什麼我可以做的？',
        completedTaskAnswers: '我已經完成了任務',
        cancelQuestAnswers: '我想取消任務',
        rentTransportAnswers: '我想租交通工具',
        jobAnswers: '我想找份工作',
        quitAnswers: '我想退出',
        Answers2: '再見！',
        portLosSantosShipsCargoFormContainersyesBotmessage: '這是洛斯桑託斯的港口。載貨的星艦來到這裡，通常是以集裝箱的形式...他們在這裡是無法估量的。沿途也有很多卡車駕駛提供物品。',
        clearAnswers: '我明白了。',
        listTrucksWorkRentBotmessage: '這是可供您使用的卡車清單。您可以租用任何人。',
        Answers3: '好吧，那麼。',
        greatChoiceStartTransportingGoodsPassportCdlbCdlaLicenseBotmessage: '非常好的選擇！現在你需要有護照和CDL ： B或CDL ：開始運輸的執照。',
        readyAnswers: '好了，我準備好了。',
        Answers4: '不，謝謝。',
        newsareQuitBotmessage: '這就是消息...你確定要退出嗎？',
        Answers5: '是的，沒錯。',
        changedMindAnswers: '不，我改主意了',
        deliverGoodsReturnGiveRewardBotmessage: '是的，你可以幫我送貨，我會獎勵你',
        Answers6: '好吧，我會幫忙。',
        Answers7: '不是現在。',
        cancelQuestBotmessage: '你確定要取消任務嗎？',
        Answers8: '是的，我做的。',
        Answers9: '不，我不知道。',
        charlesWhiteName: '查爾斯·懷特',
        headWasteRecyclingDescription: '垃圾回收經理',
        standingBitingBotmessage: '來吧，我沒有咬...',
        placeAnswers2: '這是什麼地方？',
        Answers10: '有什麼我可以做的？',
        completedTaskAnswers2: '我已經完成了任務',
        cancelQuestAnswers2: '我想取消任務',
        jobAnswers2: '我想找份工作',
        quitAnswers2: '我想退出',
        startWorkingAnswers: '我想開始工作',
        stopWorkingAnswers: '我想停止工作',
        rentTransportAnswers2: '我想租交通工具',
        goodbyeAnswers: '-再見',
        garbageReturnGiveRewardBotmessage: '是的，你可以幫我處理垃圾，我會獎勵你',
        Answers11: '好吧，我會幫忙。',
        Answers12: '不是現在。',
        hehGivethisDumpGarbageBroughtSortBotmessage: '呵呵，你知道...這是一個垃圾堆，他們帶垃圾進來這裡，我們分類它。',
        clearAnswers2: '我明白了。',
        listVehiclesRentBotmessage: '以下是可供出租的車輛清單。',
        Answers13: '好吧，那麼。',
        addressOrderStartWorkingPassportCdlBotmessage: '你來對地方了為了開始，您需要持有護照和CDL執照： B.您有交易嗎？',
        agreeAnswers: '是的，我同意',
        Answers14: '不，謝謝。',
        signPapersEyesAnymoreBotmessage: '簽署文件並離開這裡，讓我的眼睛不再見到你！',
        signedAnswers: '訂閱',
        changedMindAnswers2: '我改主意了',
        cancelQuestBotmessage2: '你確定要取消任務嗎？',
        Answers15: '是的，我做的。',
        Answers16: '不，我不知道。',
        enriqueIglesiasName: 'Enrique Iglesias',
        headDowntownCabDescription: '市中心計程車公司主管',
        interestedBotmessage: '您好，您對什麼感興趣？',
        rulesWorkingTaxiAnswers: '告訴我在計程車裡工作的規則',
        jobAnswers3: '我想找份工作',
        quitJobAnswers: '我想辭掉工作',
        rentCarWorkShiftAnswers: '我想租車上班',
        goodbyeAnswers2: '-再見',
        taxiDriversProhibitedRefuseTransportPassengersChangeBotmessage: '禁止計程車司機： 1 ）無正當理由拒絕接載乘客或變更路線。2 ）未經乘客同意帶拼車乘客。3 ）單獨標示車資。',
        clearAnswers3: '我明白了。',
        listVehiclesRentBotmessage2: '以下是可供出租的車輛清單。',
        Answers17: '好吧，那麼。',
        goodPassportDriveLicenseComplyBotmessage: '好的。您需要擁有護照和D駕駛執照，並了解並遵守旅客交通規則。如果你對一切都滿意，我們可以開始處理僱傭文件。',
        Answers18: '是啊，來吧。',
        Answers19: '不，謝謝。',
        newsareQuitBotmessage2: '這就是消息...你確定要退出嗎？',
        Answers20: '是的，沒錯。',
        changedMindAnswers3: '不，我改主意了',
        ivanChashinName: 'Ivan Chashin',
        travelGuideDescription: '旅行指南',
        losSantosBotmessage: '歡迎來到洛斯桑託斯！有什麼我可以幫你的嗎？',
        questAnswers: '關於任務',
        questAnswers2: '關於任務',
        questAnswers3: '關於任務',
        startGameAnswers: '如何開始遊戲？',
        initialQuestsAnswers: '開始任務',
        cancelQuestAnswers3: '我想取消任務',
        transportationAnswers: '我需要交通工具',
        Answers21: '待會見',
        simpleTasksBeginningJourneyExplainBotmessage: '我們非常簡單，我有任務可以在旅程開始時為您提供協助，並解釋這裡的內容和方式。如果您需要更多資訊，歡迎隨時詢問',
        initialTasksAnswers: '初始分配',
        initialTransportAnswers: '初始運輸',
        earnMoneyAnswers: '我在哪裏可以賺錢？',
        Answers22: '我明白了。',
        statesTransportLegsWorryRentBotmessage: '在沒有交通工具的州，就像沒有腿，你不能走遠。但別擔心，我有交通工具可供您租用，如果您想節省費用，附近有地鐵。',
        showTransportRentAnswers: '向我展示租用的車輛',
        Answers23: '我明白了。',
        initialTasksStatesEasierFutureBotmessage: '我的最初任務將幫助你習慣我們的狀態，以便未來對你來說更容易。你想嘗試嗎？',
        Answers24: '是的，我做的。',
        Answers25: '稍後',
        moneyInitialCapitalNewcomersStatesInterestingJobsBotmessage: '沒有錢，即使你有種子錢，你也不會有太多進步。對於州內的新來者，我們有有趣的工作要做',
        workBuilderAnswers: '做建築工人',
        workingButcherAnswers: '做屠夫',
        jobLumberjackAnswers: '伐木工的工作',
        farmersJobAnswers: 'Farmer \'s work',
        fishingJobsAnswers: '釣魚',
        Answers26: '有什麼更好的？',
        Answers27: '我明白了。',
        fishingInterestingBoringBeginBuyBotmessage: '有些人可能對釣魚有興趣，有些人可能很無聊。首先，您需要購買釣竿和誘餌（在「全天候商店」地圖上） ，然後前往河邊（在「釣魚區」地圖上）。您可以在市場上賣魚！',
        Answers28: '我明白了。',
        carsRentInterestedBotmessage: '我有幾輛車。您可以租用其中之一。',
        Answers29: '是的，當然。',
        Answers30: '不，謝謝。',
        nahHappenLoaderConstructionSiteRentBotmessage: '你希望它所有的一次？不，不，不是那樣的。先是做裝載師或在建築現場，然後你租了一間公寓然後來了。美國夢的曲線將通向哪裡。',
        Answers31: '好吧，那麼。',
        cancelQuestBotmessage3: '你確定要取消任務嗎？',
        Answers32: '是的，我做的。',
        Answers33: '不，我不知道。',
        glenJohnsonName: 'Glen Johnson',
        bankEmployeeDescription: '銀行員工',
        Botmessage: '嗨，有什麼可以幫你的嗎？',
        openBankAccountAnswers: '如何開立銀行帳戶？',
        Answers34: '有什麼我可以做的？',
        completedTaskAnswers3: '我已經完成了任務',
        cancelQuestAnswers4: '我想取消任務',
        jobCashCollectorAnswers: '我想找一份收銀員的工作',
        quitJobAnswers2: '我想辭掉工作',
        startWorkingAnswers2: '我想開始工作',
        stopWorkingAnswers2: '我想停止工作',
        rentTransportAnswers3: '我想租交通工具',
        goodbyeAnswers3: '-再見',
        managerTalkBotmessage: '去找經理，和她談談。',
        clearAnswers4: '我明白了。',
        listTrucksWorkRentBotmessage2: '這是可供您使用的卡車清單。您可以租用任何人。',
        Answers35: '好吧，那麼。',
        passportMilitaryCertificateLicensesCdlCdwlDocumentsBotmessage: '為此，您必須擁有：護照、軍事證書、執照： CDL: B、CDWL。你有必要的文件嗎？',
        Answers36: '是的，我是。',
        Answers37: '不，我不知道。',
        deliverGoodsReturnGiveRewardBotmessage2: '是的，你可以幫我送貨，我會獎勵你',
        Answers38: '好吧，我會幫忙。',
        Answers39: '不是現在。',
        goodSigningAgreementTerminationEmploymentContractBotmessage: '我們是否簽署終止協議？',
        Answers40: '是的，我是。',
        Answers41: '不，我不知道。',
        cancelQuestBotmessage4: '你確定要取消任務嗎？',
        Answers42: '是的，我做的。',
        Answers43: '不，我不知道。',
        ericSteinmannName: '埃裏克·斯坦曼',
        hospitalWorkerDescription: '醫院員工',
        Botmessage2: '嗨，有什麼可以幫你的嗎？',
        placeAnswers3: '這是什麼地方？',
        buyMedicineAnswers: '我需要購買藥品',
        Answers44: '我能爲你做什麼？',
        finishCleaningAnswers: '完成清潔',
        goodbyeAnswers4: '-再見',
        cityHospitalMedicalInstitutionConductsInpatientExaminationTreatmentPatientsBotmessage: '這是一家城市醫院，是一家對患者進行住院檢查和治療的醫療機構。',
        clearAnswers5: '我明白了。',
        problemProvideBasicSetMedicationsRecuperationBotmessage: '沒問題，我可以提供一套基本的藥物來恢復力量。你同意嗎？',
        Answers45: '是的，請。',
        Answers46: '不，謝謝。',
        internGuessCleanlinessRoomImportantBotmessage: '所以，實習生，你可以猜到，房間的乾淨度在醫院非常重要，你需要清洗地板。',
        readyAnswers2: '是的，我準備好了。',
        timeAnswers: '下次吧',
        fineMrPropperProudBotmessage: '好吧，普羅珀先生會爲你感到驕傲的',
        Answers47: '好啦，好啦',
        continueCleaningAnswers: '我想繼續清潔',
        mariaGrayName: 'Maria Gray',
        fibSecretaryDescription: 'FIB書記',
        mariaGrayBotmessage: '嗨，我叫瑪麗亞·格雷我能幫你嗎？',
        placeAnswers4: '這是什麼地方？',
        giveEvidenceAnswers: '我想給你證據。',
        goodbyeAnswers5: '-再見',
        handBotmessage: '你想要通過什麼？',
        cashMoneyAnswers: '我想要提款',
        donateDrugsAnswers: '我想放棄毒品',
        Answers48: '沒什麼。',
        headquartersFederalInvestigationBureauBotmessage: '這是聯邦調查局的總部。',
        clearAnswers6: '我明白了。',
        showDocumentsStartBotmessage: '好吧，讓我看看你的證件，你就可以開始了。',
        Answers49: '拿去吧',
        Answers50: '不，謝謝。',
        janeMargolisName: 'Jane Margolis',
        drivingSchoolEmployeeDescription: '駕駛學校的女性員工',
        janeMargolisDrivingSchoolInstructorInterestedBotmessage: '您好，我叫Jane Margolis ，我是一名駕駛教練。你對什麼感興趣？',
        placeAnswers5: '這是什麼地方？',
        licenseDriveCategoryAnswers: '我想取得駕駛執照',
        cdlCategoryLicenseAnswers: '我想要一個CDL執照',
        goodbyeAnswers6: '-再見',
        drivingSchoolLicensesCategoriesVehiclesBotmessage: '這是一所駕駛學校。您可以在這裡獲得不同類別的車輛執照。',
        clearAnswers7: '一切都很清楚',
        licenseDriveVehiclesCategoryPassengerTransportCategoryMotorcycleTransportSelectBotmessage: '這是一個駕駛執照。D類-旅客運輸， M類-摩託運輸。選擇所需的類別。',
        driveAnswers: 'Drive: D',
        driveAnswers2: 'Drive: M',
        backAnswers: '回去吧',
        licenseDriveCommercialVehiclesCategoryPickupsTrucksCategoryBotmessage: '這是商業車輛執照。B類-皮卡和貨車，以及A類-鞍型貨車。選擇所需的類別。',
        cdlAnswers: 'CDL: B',
        cdlAnswers2: 'CDL: A',
        backAnswers2: '回去吧',
        arianaSmithName: 'Ariana Smith',
        secretaryDescription: '辦事員',
        arianaSmithAssistantBuildingBotmessage: '嗨，我叫Ariana Smith ，我是這棟大樓的助理。我能爲你做什麼？',
        placeAnswers6: '這是什麼地方？',
        Answers51: '我能爲你做什麼？',
        finishCleaningAnswers2: '完成清潔',
        giveEvidenceAnswers2: '我想給你證據。',
        goodbyeAnswers7: '-再見',
        handBotmessage2: '你想要通過什麼？',
        cashMoneyAnswers2: '我想要提款',
        donateDrugsAnswers2: '我想放棄毒品',
        Answers52: '沒什麼。',
        cityHallBotmessage: '這是市政廳。',
        clearAnswers8: '一切都很清楚',
        handsReadyStartedBotmessage: '是啊，我們真的需要一些工作手。',
        readyAnswers3: '是的，我準備好了。',
        timeAnswers2: '下次吧',
        Botmessage3: '你做得很好，謝謝你',
        Answers53: '好吧，那麼。',
        continueCleaningAnswers2: '我想繼續清潔',
        ricardoMilosName: '裏卡多·米洛斯',
        gopostalManagerDescription: 'GoPostal管理器',
        managerPlaceBotmessage: '嗨，我是這個地方的經理。我能爲你做什麼？',
        placeAnswers7: '這是什麼地方？',
        workAnswers: '我想爲你工作',
        quitJobAnswers3: '我想辭掉這份工作',
        paidAnswers: '收款',
        goodbyeAnswers8: '-再見',
        gopostalEngagedParcelDeliveryBotmessage: '我是GoPostal ，我們會送包裹',
        clearAnswers9: '我明白了。',
        jobPassportCdlLicenseBotmessage: '要加入我們，您必須擁有：護照， CDL執照： B',
        Answers54: '好吧，那麼。',
        Answers55: '不，謝謝。',
        $xShiftBotmessage: '好吧，好吧，你將獲得$ X這個班次。',
        moneyAnswers: '取錢',
        continueWorkingAnswers: '繼續工作',
        quitJobBotmessage: '你確定要退出你的工作嗎？',
        Answers56: '是的，我是。',
        Answers57: '不，我不知道。',
        alexWhiteName: 'Alex White',
        foremanSawmillDescription: '伐木工工',
        chargeBotmessage: '我掌權，你需要什麼？',
        placeAnswers8: '這是什麼地方？',
        Answers58: '有什麼我可以做的？',
        completedTaskAnswers4: '我已經完成了任務',
        cancelQuestAnswers5: '我想取消任務',
        workAnswers2: '我想爲你工作',
        goodbyeAnswers9: '-再見',
        openEyesCircusSawmillCutBotmessage: '睜開你的眼睛，它看起來不像一場馬戲。這是一個鋸木廠，我們正在拉下來並回收木材。',
        clearAnswers10: '我明白了。',
        orderStartWorkingBuyHatchetMarketBotmessage: '為了開始工作，請先在市場上購買一把斧頭。然後去森林（地圖） ，開始砍樹。對於初學者，最好開始砍伐附近的森林',
        Answers59: '好吧，那麼。',
        constructionReturnGiveRewardBotmessage: '是的，你可以幫助這個建築，我會給你獎勵',
        Answers60: '好吧，我會幫忙。',
        Answers61: '不是現在。',
        cancelQuestBotmessage5: '你確定要取消任務嗎？',
        Answers62: '是的，我做的。',
        Answers63: '不，我不知道。',
        chipPetersonName: '芯片彼得森',
        constructionManagerDescription: '施工主管',
        chipPetersonChargeFacilityBotmessage: '你好，我叫芯片彼得森！我負責這個設施，我能為你做些什麼？',
        placeAnswers9: '這是什麼地方？',
        Answers64: '有什麼我可以做的？',
        completedTaskAnswers5: '我已經完成了任務',
        cancelQuestAnswers6: '我想取消任務',
        workAnswers3: '我想爲你工作',
        quitJobAnswers4: '我想辭掉這份工作',
        paidAnswers2: '收款',
        goodbyeAnswers10: '-再見',
        mileHighClubNewestExcitingComplexCityCenterBotmessage: '這是Mile High Club ，這是市中心最新和最令人興奮的影院。',
        clearAnswers11: '我明白了。',
        vacanciesChooseBotmessage: '我們有一些空缺，你可以選擇一個。',
        loaderAnswers: '載入器',
        installerAnswers: '程序集',
        engineerAnswers: '工程師',
        Answers65: '不，謝謝。',
        quitJobBotmessage2: '你確定要退出你的工作嗎？',
        Answers66: '是的，我是。',
        Answers67: '不，我不知道。',
        constructionReturnGiveRewardBotmessage2: '是的，你可以幫助這個建築，我會給你獎勵',
        Answers68: '好吧，我會幫忙。',
        Answers69: '不是現在。',
        cancelQuestBotmessage6: '你確定要取消任務嗎？',
        Answers70: '是的，我做的。',
        Answers71: '不，我不知道。',
        mariaLopezName: 'Maria Lopez',
        cityHallEmployeeDescription: '市政廳員工',
        mariaLopezBotmessage: '你好，我叫瑪麗亞·洛佩斯我能爲你做什麼？',
        placeAnswers10: '這是什麼地方？',
        cityAnswers: '告訴我們這座城市',
        receivingDocumentsAnswers: '收到文件',
        organizationRegistrationAnswers: '二.組織的注冊',
        goodbyeAnswers11: '-再見',
        cityHallMunicipalBuildingCityHallsLosSantosBotmessage: '這是市政廳市政大樓。它是洛斯桑託斯的四個市長辦公室之一。',
        clearAnswers12: '我明白了。',
        documentsLicensesBotmessage: '您想取得哪些文件或許可證？',
        fishingLicenseAnswers: '我需要釣魚執照',
        idAnswers: '我需要取得身分證件',
        backAnswers3: '回去吧',
        createOrganizationFamilyBuyHouseBotmessage: '要建立組織（或家庭） ，您需要購買一棟房子。它將成為總部。創作成本： $ 3,000,000 +硬幣房屋7天的版權費用。準備好繼續嗎？',
        createAnswers: '是的，建立',
        backAnswers4: '回去吧',
        certificatesNeededMembersFactionBotmessage: '陣營成員必須提供身份證明文件，是否要取得身份證明文件？',
        Answers72: '是的，我是。',
        Answers73: '不，我不知道。',
        fishingLicenseNeededOrderCatchFishSellBotmessage: '你需要釣魚執照才能賣掉它，你想要去海岸嗎？',
        Answers74: '是的，我是',
        Answers75: '不，我不知道。',
        documentsReadyPickBotmessage: '你的文件已準備就緒，你可以立即取回。',
        Answers76: '謝謝',
        losSantosLargestCityStateArrivedRecentlyFindBotmessage: '洛斯桑託斯是州內最大的城市。如果你最近抵達，你可以找到兼職工作，例如在建築現場或送餐服務。您還可以在駕駛學校取得執照，在物流公司做司機，或在大學學習成為醫生。你選擇你自己的路。',
        Answers77: '我明白了，謝謝你。',
        joshuaBelucciName: 'Joshua Belucci',
        managingPostopDescription: 'PostOP管理器',
        chargeBotmessage2: '我掌權，你想要什麼？',
        placeAnswers11: '這是什麼地方？',
        Answers78: '有什麼我可以做的？',
        completedTaskAnswers6: '我已經完成了任務',
        cancelQuestAnswers7: '我想取消任務',
        rentTransportAnswers4: '我想租交通工具',
        jobAnswers4: '我想找份工作',
        quitAnswers3: '我想退出',
        goodbyeAnswers12: '-再見',
        postopWarehousePaletoBayCargoDeliveredLosSantosBotmessage: '這是帕勒託灣的PostOP倉庫，他們在那裡從洛斯桑託斯運送貨物。',
        clearAnswers13: '我明白了。',
        listVehiclesRentBotmessage3: '以下是可供出租的車輛清單。',
        Answers79: '好吧，那麼。',
        passportLicensesBotmessage: '你有護照和執照嗎？',
        Answers80: '是的，我是。',
        Answers81: '不，我不知道。',
        goodRiddanceBotmessage: '嗯，這是一個桌布。',
        goodLuckAnswers: '祝你今天愉快',
        changedMindAnswers4: '我改主意了',
        deliverGoodsReturnGiveRewardBotmessage3: '是的，你可以幫我送貨，我會獎勵你',
        Answers82: '好吧，我會幫忙。',
        Answers83: '不是現在。',
        cancelQuestBotmessage7: '你確定要取消任務嗎？',
        Answers84: '是的，我做的。',
        Answers85: '不，我不知道。',
        carlJenkinName: 'Carl Jenkin',
        lspdInstructorDescription: 'LSPD教師',
        Botmessage4: '我能爲你做些什麼？',
        placeAnswers12: '這是什麼地方？',
        policeAcademyAnswers: '我怎麼上警察學校？',
        kindJobOfferAnswers: '您能提供什麼樣的工作？',
        finishCleaningAnswers3: '完成清潔',
        giveEvidenceAnswers3: '我想給你證據。',
        happilyAnswers: '快樂。',
        handBotmessage3: '你想要通過什麼？',
        cashMoneyAnswers3: '我想要提款',
        donateDrugsAnswers3: '我想放棄毒品',
        Answers86: '沒什麼。',
        mainPoliceDepartmentStateSanAndreasProfessionalsFieldWorkBotmessage: '這是聖安德烈亞斯州警察局。只有業務專業人士在這裡工作。',
        clearAnswers14: '我明白了。',
        policeAcademyMilitaryCertificateDriversLicenseMedicalCertificateCitizenStateBotmessage: '要進入警察學校，你必須取得軍事證明，駕駛執照，醫療證明，並且也是這個州的公民。',
        Answers87: '好吧，那麼。',
        internshipStageBenefitDepartmentWashFloorBotmessage: '學徒期間，可以幫助部門，你需要洗地板',
        readyAnswers4: '是的，我準備好了。',
        timeAnswers3: '下次吧',
        excellentCadetGreatFloorCleanerConfessionBotmessage: '做得好，學員，你做得很好！地板清洗機是你的招供，再來一次。',
        Answers88: '好吧，那麼。',
        continueCleaningAnswers3: '我想繼續清潔',
        geraldDiceName: '傑拉爾德骰子',
        deputySheriffDescription: '副治安官',
        broughtRegionReasonBotmessage: '哦，把你帶到我們的土地。我看你來是有原因的我能爲你做什麼？',
        placeAnswers13: '這是什麼地方？',
        sheriffAnswers: '你怎麼當警長的？',
        Answers89: '我能爲你做什麼？',
        finishCleaningAnswers4: '完成清潔',
        giveEvidenceAnswers4: '我想給你證據。',
        happilyAnswers2: '快樂。',
        handBotmessage4: '你想要通過什麼？',
        cashMoneyAnswers4: '我想要提款',
        donateDrugsAnswers4: '我想放棄毒品',
        Answers90: '沒什麼。',
        localBlaineCountyLawEnforcementDepartmentBotmessage: '這是布萊恩縣的當地執法部門。',
        clearAnswers15: '我明白了。',
        hehMilitaryIdMedicalCertificatePatienceNervesSteelBotmessage: '呵呵，你需要軍人卡，醫療證書，耐心和鋼鐵神經。',
        Answers91: '好吧，那麼。',
        gainingExperienceCleanSiteBotmessage: '當你獲得經驗時，請清理這個區域。',
        readyAnswers5: '是的，我準備好了。',
        timeAnswers4: '下次吧',
        makeGreatJanitorHeheMadBotmessage: '你做得很好！你可以做一個偉大的清潔人。嘿嘿，不要生氣。我們都經歷過這一切。',
        Answers92: '好吧，那麼。',
        continueCleaningAnswers4: '我想繼續清潔',
        jungLeeName: 'Jung Lee',
        travelGuideDescription2: '旅行指南',
        losSantosBotmessage2: '歡迎來到洛斯桑託斯！有什麼我可以幫你的嗎？',
        questAnswers4: '關於任務',
        questAnswers5: '關於任務',
        questAnswers6: '關於任務',
        startGameAnswers2: '如何開始遊戲？',
        initialQuestsAnswers2: '開始任務',
        cancelQuestAnswers8: '我想取消任務',
        transportationAnswers2: '我需要交通工具',
        Answers93: '待會見',
        simpleTasksBeginningJourneyExplainBotmessage2: '我們非常簡單，我有任務可以在旅程開始時為您提供協助，並解釋這裡的內容和方式。如果您需要更多資訊，歡迎隨時詢問',
        initialTasksAnswers2: '初始分配',
        initialTransportAnswers2: '初始運輸',
        earnMoneyAnswers2: '我在哪裏可以賺錢？',
        Answers94: '我明白了。',
        statesTransportLegsWorryRentBotmessage2: '在沒有交通工具的州，就像沒有腿，你不能走遠。但別擔心，我有交通工具可供您租用，如果您想節省費用，附近有地鐵。',
        showTransportRentAnswers2: '向我展示租用的車輛',
        Answers95: '我明白了。',
        initialTasksStatesEasierFutureBotmessage2: '我的最初任務將幫助你習慣我們的狀態，以便未來對你來說更容易。你想嘗試嗎？',
        Answers96: '是的，我做的。',
        Answers97: '稍後',
        moneyInitialCapitalNewcomersStatesInterestingJobsBotmessage2: '沒有錢，即使你有種子錢，你也不會有太多進步。對於州內的新來者，我們有有趣的工作要做',
        workBuilderAnswers2: '做建築工人',
        workingButcherAnswers2: '做屠夫',
        jobLumberjackAnswers2: '伐木工的工作',
        farmersJobAnswers2: 'Farmer \'s work',
        fishingJobsAnswers2: '釣魚',
        Answers98: '有什麼更好的？',
        Answers99: '我明白了。',
        fishingInterestingBoringBeginBuyBotmessage2: '有些人可能對釣魚有興趣，有些人可能很無聊。首先，您需要購買釣竿和誘餌（在「全天候商店」地圖上） ，然後前往河邊（在「釣魚區」地圖上）。您可以在市場上賣魚！',
        Answers100: '我明白了。',
        carsRentInterestedBotmessage2: '我有幾輛車。您可以租用其中之一。',
        Answers101: '是的，當然。',
        Answers102: '不，謝謝。',
        nahHappenLoaderConstructionSiteRentBotmessage2: '你希望它所有的一次？不，不，不是那樣的。先是做裝載師或在建築現場，然後你租了一間公寓然後來了。美國夢的曲線將通向哪裡。',
        Answers103: '好吧，那麼。',
        cancelQuestBotmessage8: '你確定要取消任務嗎？',
        Answers104: '是的，我做的。',
        Answers105: '不，我不知道。',
        michaelPearsName: 'Michael Pears',
        travelGuideDescription3: '旅行指南',
        losSantosBotmessage3: '歡迎來到洛斯桑託斯！有什麼我可以幫你的嗎？',
        questAnswers7: '關於任務',
        questAnswers8: '關於任務',
        questAnswers9: '關於任務',
        startGameAnswers3: '如何開始遊戲？',
        initialQuestsAnswers3: '開始任務',
        cancelQuestAnswers9: '我想取消任務',
        transportationAnswers3: '我需要交通工具',
        Answers106: '待會見',
        simpleTasksBeginningJourneyExplainBotmessage3: '我們非常簡單，我有任務可以在旅程開始時為您提供協助，並解釋這裡的內容和方式。如果您需要更多資訊，歡迎隨時詢問',
        initialTasksAnswers3: '初始分配',
        initialTransportAnswers3: '初始運輸',
        earnMoneyAnswers3: '我在哪裏可以賺錢？',
        Answers107: '我明白了。',
        statesTransportLegsWorryRentBotmessage3: '在沒有交通工具的州，就像沒有腿，你不能走遠。但別擔心，我有交通工具可供您租用，如果您想節省費用，附近有地鐵。',
        showTransportRentAnswers3: '向我展示租用的車輛',
        Answers108: '我明白了。',
        initialTasksStatesEasierFutureBotmessage3: '我的最初任務將幫助你習慣我們的狀態，以便未來對你來說更容易。你想嘗試嗎？',
        Answers109: '是的，我做的。',
        Answers110: '稍後',
        moneyInitialCapitalNewcomersStatesInterestingJobsBotmessage3: '沒有錢，即使你有種子錢，你也不會有太多進步。對於州內的新來者，我們有有趣的工作要做',
        workBuilderAnswers3: '做建築工人',
        workingButcherAnswers3: '做屠夫',
        jobLumberjackAnswers3: '伐木工的工作',
        farmersJobAnswers3: 'Farmer \'s work',
        fishingJobsAnswers3: '釣魚',
        Answers111: '有什麼更好的？',
        Answers112: '我明白了。',
        fishingInterestingBoringBeginBuyBotmessage3: '有些人可能對釣魚有興趣，有些人可能很無聊。首先，您需要購買釣竿和誘餌（在「全天候商店」地圖上） ，然後前往河邊（在「釣魚區」地圖上）。您可以在市場上賣魚！',
        Answers113: '我明白了。',
        carsRentInterestedBotmessage3: '我有幾輛車。您可以租用其中之一。',
        Answers114: '是的，當然。',
        Answers115: '不，謝謝。',
        nahHappenLoaderConstructionSiteRentBotmessage3: '你希望它所有的一次？不，不，不是那樣的。先是做裝載師或在建築現場，然後你租了一間公寓然後來了。美國夢的曲線將通向哪裡。',
        Answers116: '好吧，那麼。',
        cancelQuestBotmessage9: '你確定要取消任務嗎？',
        Answers117: '是的，我做的。',
        Answers118: '不，我不知道。',
        vincentToresName: 'Vincent Tores',
        travelGuideDescription4: '旅行指南',
        losSantosBotmessage4: '歡迎來到洛斯桑託斯！有什麼我可以幫你的嗎？',
        questAnswers10: '關於任務',
        questAnswers11: '關於任務',
        questAnswers12: '關於任務',
        startGameAnswers4: '如何開始遊戲？',
        initialQuestsAnswers4: '開始任務',
        cancelQuestAnswers10: '我想取消任務',
        transportationAnswers4: '我需要交通工具',
        Answers119: '待會見',
        simpleTasksBeginningJourneyExplainBotmessage4: '我們非常簡單，我有任務可以在旅程開始時為您提供協助，並解釋這裡的內容和方式。如果您需要更多資訊，歡迎隨時詢問',
        initialTasksAnswers4: '初始分配',
        initialTransportAnswers4: '初始運輸',
        earnMoneyAnswers4: '我在哪裏可以賺錢？',
        Answers120: '我明白了。',
        statesTransportLegsWorryRentBotmessage4: '在沒有交通工具的州，就像沒有腿，你不能走遠。但別擔心，我有交通工具可供您租用，如果您想節省費用，附近有地鐵。',
        showTransportRentAnswers4: '向我展示租用的車輛',
        Answers121: '我明白了。',
        initialTasksStatesEasierFutureBotmessage4: '我的最初任務將幫助你習慣我們的狀態，以便未來對你來說更容易。你想嘗試嗎？',
        Answers122: '是的，我做的。',
        Answers123: '稍後',
        moneyInitialCapitalNewcomersStatesInterestingJobsBotmessage4: '沒有錢，即使你有種子錢，你也不會有太多進步。對於州內的新來者，我們有有趣的工作要做',
        workBuilderAnswers4: '做建築工人',
        workingButcherAnswers4: '做屠夫',
        jobLumberjackAnswers4: '伐木工的工作',
        farmersJobAnswers4: 'Farmer \'s work',
        fishingJobsAnswers4: '釣魚',
        Answers124: '有什麼更好的？',
        Answers125: '我明白了。',
        fishingInterestingBoringBeginBuyBotmessage4: '有些人可能對釣魚有興趣，有些人可能很無聊。首先，您需要購買釣竿和誘餌（在「全天候商店」地圖上） ，然後前往河邊（在「釣魚區」地圖上）。您可以在市場上賣魚！',
        Answers126: '我明白了。',
        carsRentInterestedBotmessage4: '我有幾輛車。您可以租用其中之一。',
        Answers127: '是的，當然。',
        Answers128: '不，謝謝。',
        nahHappenLoaderConstructionSiteRentBotmessage4: '你希望它所有的一次？不，不，不是那樣的。先是做裝載師或在建築現場，然後你租了一間公寓然後來了。美國夢的曲線將通向哪裡。',
        Answers129: '好吧，那麼。',
        cancelQuestBotmessage10: '你確定要取消任務嗎？',
        Answers130: '是的，我做的。',
        Answers131: '不，我不知道。',
        bobbySingerName: 'Bobby Singer',
        mechanicDescription: '技工',
        chargeBotmessage3: '我掌權，你想要什麼？',
        jobAnswers5: '我想找份工作',
        quitAnswers4: '我想退出',
        goodbyeAnswers13: '-再見',
        vacanciesBotmessage: '到目前為止，我們沒有空位。',
        Answers132: '好吧，好吧。',
        listVehiclesRentBotmessage4: '以下是可供出租的車輛清單。',
        Answers133: '好吧，那麼。',
        Answers134: '不，謝謝。',
        goodRiddanceBotmessage2: '嗯，這是一個桌布。',
        goodLuckAnswers2: '祝你今天愉快',
        changedMindAnswers5: '我改主意了',
        felisaLucianoName: 'Felisa Luciano',
        streetBossDescription: 'Strada boss',
        salveBotmessage: 'Salve ，我能爲你做什麼？',
        placeAnswers14: '這是什麼地方？',
        tasksAnswers: '有什麼事要找我？',
        completeTaskAnswers: '我想完成任務',
        cashMoneyAnswers5: '我想要提款',
        goodbyeAnswers14: '-再見',
        ranchEngagedBreedingHorsesThoughtEquestrianBusinessBotmessage: '這是一個牧場，我們在那裡繁殖馬。誰會想到馬業能帶來好錢！',
        clearAnswers16: '我明白了。',
        kindJobBotmessage: '你想要什麼樣的工作？',
        bookmarksAnswers: '書籤',
        carTheftAnswers: '劫車',
        Answers135: '到目前爲止，沒有',
        finishJobBotmessage: '你確定要完成這項工作嗎？',
        Answers136: '是的，我是。',
        Answers137: '不，我沒有。',
        vicenteGonzalesName: 'Vicente Gonzales',
        plazaBossDescription: '廣場老板',
        holaBotmessage: '你好，我能爲你做什麼？',
        placeAnswers15: '這是什麼地方？',
        tasksAnswers2: '有什麼事要找我？',
        completeTaskAnswers2: '我想完成任務',
        cashMoneyAnswers6: '我想要提款',
        goodbyeAnswers15: '-再見',
        callistosApartmentEngagedHotelBusinessBotmessage: '這是卡利斯託的公寓，我們在那裡經營酒店業務。',
        clearAnswers17: '我明白了。',
        kindJobBotmessage2: '你想要什麼樣的工作？',
        bookmarksAnswers2: '書籤',
        carTheftAnswers2: '劫車',
        Answers138: '到目前爲止，沒有',
        finishJobBotmessage2: '你確定要完成這項工作嗎？',
        Answers139: '是的，我是。',
        Answers140: '不，我沒有。',
        andreyOrlovName: 'Andrey Orlov',
        authorityDescription: '權威',
        Botmessage5: '嘿，我能爲你做什麼？',
        placeAnswers16: '這是什麼地方？',
        tasksAnswers3: '有什麼事要找我？',
        completeTaskAnswers3: '我想完成任務',
        cashMoneyAnswers7: '我想要提款',
        goodbyeAnswers16: '-再見',
        rehabsRehabilitationCenterEngagedPhysicalPsychologicalMoralRecoveryPeopleBotmessage: '這是ReHabs康復中心。在這裡，我們正在參與人們的身心和道德恢復。',
        clearAnswers18: '我明白了。',
        kindJobBotmessage3: '你想要什麼樣的工作？',
        bookmarksAnswers3: '書籤',
        carTheftAnswers3: '劫車',
        Answers141: '到目前爲止，沒有',
        finishJobBotmessage3: '你確定要完成這項工作嗎？',
        Answers142: '是的，我是。',
        Answers143: '不，我不知道。',
        harutoYamaguchiName: '山口春人',
        shateigashiraDescription: 'Shateigashira',
        ohayoBotmessage: 'Ohayo,我能爲你做什麼?',
        placeAnswers17: '這是什麼地方？',
        tasksAnswers4: '有什麼事要找我？',
        completeTaskAnswers4: '我想完成任務',
        cashMoneyAnswers8: '我想要提款',
        goodbyeAnswers17: '-再見',
        playboyMansionEngagedClubActivitiesStateSanAndreasBotmessage: '這是遊戲男孩的豪宅，我們是聖安德烈亞斯的俱樂部',
        clearAnswers19: '我明白了。',
        kindJobBotmessage4: '你想要什麼樣的工作？',
        bookmarksAnswers4: '書籤',
        carTheftAnswers4: '劫車',
        Answers144: '到目前爲止，沒有',
        finishJobBotmessage4: '你確定要完成這項工作嗎？',
        Answers145: '是的，我是。',
        Answers146: '不，我沒有。',
        lincolnGlockName: '老板',
        drugDealerDescription: '毒品交易商',
        whatsBotmessage: '你怎麼樣，你怎麼樣？',
        placeAnswers18: '這是什麼地方？',
        tasksAnswers5: '有什麼事要找我？',
        completeTaskAnswers5: '我想完成任務',
        cashMoneyAnswers9: '我想要提款',
        goodbyeAnswers18: '-再見',
        chamberlainHillsNeighborhoodBasketballClubFamiliesBotmessage: '這是侍從山區。家庭籃球俱樂部位於這裡。',
        clearAnswers20: '我明白了。',
        kindJobBotmessage5: '你想要什麼樣的工作？',
        bookmarksAnswers5: '書籤',
        carTheftAnswers5: '劫車',
        Answers147: '到目前爲止，沒有',
        finishJobBotmessage5: '你確定要完成這項工作嗎？',
        Answers148: '是的，我是。',
        Answers149: '不，我不知道。',
        driftyJacksonName: '小黃鼠狼',
        drugDealerDescription2: '毒品交易商',
        whatsBotmessage2: '你怎麼樣，你怎麼樣？',
        placeAnswers19: '這是什麼地方？',
        tasksAnswers6: '有什麼事要找我？',
        completeTaskAnswers6: '我想完成任務',
        cashMoneyAnswers10: '我想要提款',
        goodbyeAnswers19: '-再見',
        groveStAreaRapClubBallasGangBotmessage: '這是格羅夫街。這裡是說唱俱樂部—巴拉斯黑手黨。',
        clearAnswers21: '我明白了。',
        kindJobBotmessage6: '你想要什麼樣的工作？',
        bookmarksAnswers6: '書籤',
        carTheftAnswers6: '劫車',
        Answers150: '到目前爲止，沒有',
        finishJobBotmessage6: '你確定要完成這項工作嗎？',
        Answers151: '是的，我是。',
        Answers152: '不，我沒有。',
        dobbyKillaName: 'Anthony Johnson',
        drugDealerDescription3: '毒品交易商',
        whatsBotmessage3: '你怎麼樣，你怎麼樣？',
        placeAnswers20: '這是什麼地方？',
        tasksAnswers7: '有什麼事要找我？',
        completeTaskAnswers7: '我想完成任務',
        cashMoneyAnswers11: '我想要提款',
        goodbyeAnswers20: '-再見',
        strawberryDistrictLowriderClubBloodsLocatedBotmessage: '這是草莓街區，是下血騎手俱樂部的所在地。',
        clearAnswers22: '我明白了。',
        kindJobBotmessage7: '你想要什麼樣的工作？',
        bookmarksAnswers7: '書籤',
        carTheftAnswers7: '劫車',
        Answers153: '到目前爲止，沒有',
        finishJobBotmessage7: '你確定要完成這項工作嗎？',
        Answers154: '是的，我是。',
        Answers155: '不，我不知道。',
        dannyHauntedezName: 'Big Poppa',
        drugDealerDescription4: '毒品交易商',
        holaAmigoGladBotmessage: '你好，朋友，很高興見到你？',
        placeAnswers21: '這是什麼地方？',
        tasksAnswers8: '有什麼事要找我？',
        completeTaskAnswers8: '我想完成任務',
        cashMoneyAnswers12: '我想要提款',
        goodbyeAnswers21: '-再見',
        cypressFlatsAreaAutoRepairShopLosSantosVagosBotmessage: '這是柏林公寓區。這是洛斯桑託斯瓦戈斯所在的地方。',
        clearAnswers23: '我明白了。',
        kindJobBotmessage8: '你想要什麼樣的工作？',
        bookmarksAnswers8: '書籤',
        carTheftAnswers8: '劫車',
        Answers156: '到目前爲止，沒有',
        finishJobBotmessage8: '你確定要完成這項工作嗎？',
        Answers157: '是的，我是。',
        Answers158: '不，我不知道。',
        ernestoHauntedName: 'Frederico Nathan',
        drugDealerDescription5: '毒品交易商',
        holaAmigoGladBotmessage2: '你好，朋友，很高興見到你？',
        placeAnswers22: '這是什麼地方？',
        tasksAnswers9: '有什麼事要找我？',
        completeTaskAnswers9: '我想完成任務',
        cashMoneyAnswers13: '我想要提款',
        goodbyeAnswers22: '-再見',
        elBurroHeightsNeighborhoodShootingClubMarabuntaGrandeBotmessage: '這是El burro Heights街區。有一個射擊俱樂部— Marabunta Grande。',
        clearAnswers24: '我明白了。',
        kindJobBotmessage9: '你想要什麼樣的工作？',
        bookmarksAnswers9: '書籤',
        carTheftAnswers9: '劫車',
        Answers159: '到目前爲止，沒有',
        finishJobBotmessage9: '你確定要完成這項工作嗎？',
        Answers160: '是的，我是。',
        Answers161: '不，我沒有。',
        brainLewisName: 'Brain Lewis',
        pierOwnerDescription: '碼頭擁有者',
        gladBotmessage: '很高興見到你，我怎麼幫你？',
        placeAnswers23: '這是什麼地方？',
        boatAnswers: '我需要一艘船',
        goodbyeAnswers23: '-再見',
        greatPlaceFishingEnthusiastsGatherBeautifulQuietWaterSoundsNatureBotmessage: '這裡是釣魚愛好者的好去處。比靜水、大自然的聲音和火焰更美麗的東西。',
        clearAnswers25: '我明白了。',
        listBoatsRentBotmessage: '這是可用的船隻清單。您可以租用任何人。',
        Answers162: '好吧，那麼。',
        Answers163: '不，謝謝。',
        michaelFoxName: 'Michael Fox',
        pierOwnerDescription2: '碼頭擁有者',
        gladBotmessage2: '很高興見到你，我怎麼幫你？',
        placeAnswers24: '這是什麼地方？',
        boatAnswers2: '我需要一艘船',
        goodbyeAnswers24: '-再見',
        greatPlaceFishingEnthusiastsGatherBeautifulQuietWaterSoundsNatureBotmessage2: '這裡是釣魚愛好者的好去處。比靜水、大自然的聲音和火焰更美麗的東西。',
        clearAnswers26: '我明白了。',
        listBoatsRentBotmessage2: '這是可用的船隻清單。您可以租用任何人。',
        Answers164: '好啦，好啦',
        Answers165: '不，謝謝。',
        anthonyBridgesName: 'Anthony Bridges',
        foremanMeatProcessingPlantDescription: '肉加工廠廠長',
        chargeBotmessage4: '嘿，我是這裡的掌權者。',
        placeAnswers25: '這是什麼地方？',
        Answers166: '有什麼我可以做的？',
        completedTaskAnswers7: '我已經完成了任務',
        cancelQuestAnswers11: '我想取消任務',
        workAnswers4: '我想爲你工作',
        quitJobAnswers5: '我想辭掉這份工作',
        paidAnswers3: '收款',
        goodbyeAnswers25: '-再見',
        openEyesCircusMeatProcessingPlantSlaughterCattleBotmessage: '睜開你的眼睛，它看起來不像一場馬戲。這是一個肉類加工廠，我們剪牛和產肉。',
        clearAnswers27: '我明白了。',
        changeWorkBotmessage: '去換衣服，去工作。',
        Answers167: '好吧，那麼。',
        Answers168: '不，謝謝。',
        Botmessage6: '好吧，如果那是你想要的。',
        happilyAnswers3: '快樂。',
        changedMindAnswers6: '我改主意了',
        returnGiveRewardBotmessage: '是的，你可以幫助這個，作為回報，我會給你一個獎勵。',
        Answers169: '好吧，我會幫忙。',
        Answers170: '不是現在。',
        cancelQuestBotmessage11: '你確定要取消任務嗎？',
        Answers171: '是的，我做的。',
        Answers172: '不，我不知道。',
        jamesHetfieldName: '詹姆斯·赫特菲爾德',
        bartenderDescription: '酒保',
        forgottenBotmessage: '嘿，怎麼了？',
        placeAnswers26: '這是什麼地方？',
        showMenuAnswers: '顯示選單',
        jobAnswers6: '有我的工作嗎？',
        completeTaskAnswers10: '我想完成任務',
        cashMoneyAnswers14: '我想要提款',
        Answers173: '待會見',
        angelsDeathBikerClubWeakPlaceBotmessage: '這是死亡天使機車俱樂部。弱者不屬於這裡。',
        clearAnswers28: '我明白了。',
        kindJobBotmessage10: '你想要什麼樣的工作？',
        bookmarksAnswers10: '書籤',
        carTheftAnswers10: '劫車',
        Answers174: '到目前爲止，沒有',
        finishJobBotmessage10: '你想完成這項工作嗎？',
        Answers175: '是的，我是。',
        Answers176: '不，我不知道。',
        claytonSimonsName: 'Clayton Simons',
        crowdogDescription: 'Crowdog',
        forgottenBotmessage2: '嘿，怎麼了？',
        placeAnswers27: '這是什麼地方？',
        showMenuAnswers2: '顯示選單',
        jobAnswers7: '有我的工作嗎？',
        completeTaskAnswers11: '我想完成任務',
        cashMoneyAnswers15: '我想要提款',
        Answers177: '待會見',
        lostMcBikerClubLostForeverBotmessage: '這是失落的MC機車俱樂部，永遠失落。',
        clearAnswers29: '我明白了。',
        kindJobBotmessage11: '你想要什麼樣的工作？',
        bookmarksAnswers11: '書籤',
        carTheftAnswers11: '劫車',
        Answers178: '到目前爲止，沒有',
        finishJobBotmessage11: '你想完成工作？',
        Answers179: '是的，我是。',
        Answers180: '不，我不知道。',
        luiRichardsName: '雷理查茲',
        bartenderDescription2: '酒保',
        Botmessage7: '你好，你想要什麼？',
        placeAnswers28: '這是什麼地方？',
        showMenuAnswers3: '顯示選單',
        Answers181: '待會見',
        bahamaMamasClubBotmessage: '這是巴哈馬媽媽俱樂部。',
        clearAnswers30: '我明白了。',
        violetShanelName: 'Violet Shanel',
        bartenderDescription3: '酒保',
        Botmessage8: '你好，你想要什麼？',
        placeAnswers29: '這是什麼地方？',
        showMenuAnswers4: '顯示選單',
        Answers182: '待會見',
        stripClubBotmessage: '這是一個脫衣舞俱樂部。',
        clearAnswers31: '我明白了。',
        amyEvensonName: '艾米·埃文森',
        bartenderDescription4: '酒保',
        Botmessage9: '你好，你想要什麼？',
        placeAnswers30: '這是什麼地方？',
        showMenuAnswers5: '顯示選單',
        Answers183: '待會見',
        casinoBotmessage: '這是一個賭場。',
        clearAnswers32: '我明白了。',
        amyEvensonName2: '艾米·埃文森',
        bartenderDescription5: '酒保',
        Botmessage10: '你好，你想要什麼？',
        placeAnswers31: '這是什麼地方？',
        showMenuAnswers6: '顯示選單',
        Answers184: '待會見',
        palaceClubBotmessage: '這是宮殿俱樂部。',
        clearAnswers33: '我明白了。',
        phillRockName: '菲爾巖石',
        wardenDescription: '前任主管',
        Botmessage11: '嘿，你需要什麼嗎？',
        transportationAnswers5: '我需要交通工具',
        Answers185: '待會見',
        carsRentBotmessage: '我有一些租車，你想看嗎？',
        Answers186: '是的，當然。',
        Answers187: '不，謝謝。',
        helenCarterName: 'Helen Carter',
        bartenderDescription6: '酒保',
        forgottenBotmessage3: '嘿，怎麼了？',
        showMenuAnswers7: '顯示選單',
        Answers188: '待會見',
        christopherGonzaloName: 'Christopher Gonzalo',
        flightSchoolEmployeeDescription: '飛飛學校員工',
        christopherGonzaloFlightSchoolInstructorInterestedBotmessage: '你好，我叫克裏斯託弗·貢薩洛，我是一名飛行教練。你對什麼感興趣？',
        placeAnswers32: '這是什麼地方？',
        learnFlyAnswers: '我想學習飛行',
        goodbyeAnswers26: '-再見',
        losSantosAirportAdditionTransferPassengersCargoBotmessage: '這是洛斯桑託斯機場。除了載客和貨物外，我們還培訓新來者。',
        clearAnswers34: '一切都很清楚',
        learnFlyBotmessage: '你想和我們一起學習飛行，對不對？',
        learnAnswers: '是的，我想學習',
        Answers189: '不，我不想。',
        сonnorRianName: 'Connor Rian',
        irishmanDescription: 'Irishman',
        barevDzezAperBotmessage: '我能爲你做什麼？',
        placeAnswers33: '這是什麼地方？',
        tasksAnswers10: '有什麼事要找我？',
        completeTaskAnswers12: '我想完成任務',
        cashMoneyAnswers16: '我想要提款',
        goodbyeAnswers27: '-再見',
        irelandRestaurantwineryEngagedRestaurantBusinessWineMakingThoughtBotmessage: '這是愛爾蘭俱樂部，我們在那裡做餐廳生意和製酒。誰會想到一個好的俱樂部可以賺很多錢',
        clearAnswers35: '我明白了。',
        kindJobBotmessage12: '你想要什麼樣的工作？',
        bookmarksAnswers12: '書籤',
        carTheftAnswers12: '劫車',
        Answers190: '到目前爲止，沒有',
        finishJobBotmessage12: '你確定要完成這項工作嗎？',
        Answers191: '是的，我是。',
        Answers192: '不，我不知道。',
        jamesWhiteName: '詹姆斯·懷特',
        farmerDescription: 'Farmer',
        farmerJokesHundredBotmessage: '嗨，我是一個農夫，有300美元的笑話。我能爲你做什麼？',
        placeAnswers34: '這是什麼地方？',
        workAnswers5: '如何在這裡工作？',
        placesGrownAnswers: '他們在哪裡生長？',
        goodbyeAnswers28: '-再見',
        farmGrowCropsKindLegalHarvestBotmessage: '這是一個農場，這是我們生產作物的地方。像是合法的作物',
        clearAnswers36: '我明白了。',
        timePickOrangesMapFarmCollectBotmessage: '如果這是你第一次來這裡，你需要採摘橙子。在地圖上，這是農場1。如果收成足夠的橘子，你可以種植蔬菜或水果。（在清單中，使用種子種植）',
        clearAnswers37: '我明白了。',
        farmOrangesFarmWheatFarmPotatoesFarmCabbageCornFarmBananaBotmessage: '農場1 ：橙子。農場2 ：小麥。農場3 ：土豆。農場4 ：白菜和玉米。農場5 ：香蕉和南瓜',
        clearAnswers38: '我明白了。',
        markEsperName: 'Mark Esper',
        sergeantDescription: '中士',
        Botmessage12: '有什麼需要的？',
        placeAnswers35: '這是什麼地方？',
        Answers193: '我能爲你做什麼？',
        cashMoneyAnswers17: '我想要提款',
        finishCleaningAnswers5: '完成清潔',
        happilyAnswers4: '快樂。',
        armyBabyClearBotmessage: '這是軍隊，寶貝。',
        Answers194: '我明白了。',
        gainingExperienceCleanBotmessage: '只要你有經驗，請在這裡清理。',
        readyAnswers6: '是的，我準備好了。',
        timeAnswers5: '下次吧',
        makeGreatJanitorHeheMadBotmessage2: '你做得很好！你可以做一個偉大的清潔人。嘿嘿，不要生氣。我們都經歷過這一切。',
        Answers195: '好吧，那麼。',
        continueCleaningAnswers5: '我想繼續清潔',
        jayNorrisName: 'Jay Norris',
        consultantDescription: '顧問',
        Botmessage13: '你需要什麼嗎？',
        placeAnswers36: '這是什麼地方？',
        Answers196: '我能爲你做什麼？',
        finishCleaningAnswers6: '完成清潔',
        happilyAnswers5: '快樂。',
        weazelNewsServeAdsSortsThingsBotmessage: '這是Weazel News ，我們在那裡投放廣告並做各種事情',
        Answers197: '好啊，好啊',
        handsReadyStartedBotmessage2: '是啊，我們真的需要一些工作手。',
        readyAnswers7: '是的，我準備好了。',
        timeAnswers6: '下次吧',
        Botmessage14: '你做得很好，謝謝你',
        Answers198: '好吧，那麼。',
        continueCleaningAnswers6: '我想繼續清潔',
        henryWaltName: 'Henry Walt',
        busFleetManagerDescription: '公交車隊經理',
        chargePlaceBotmessage: '嘿，我是負責這個地方。我能爲你做什麼？',
        placeAnswers37: '這是什麼地方？',
        Answers199: '有什麼我可以做的？',
        completedTaskAnswers8: '我已經完成了任務',
        cancelQuestAnswers12: '我想取消任務',
        rentTransportAnswers5: '我想租交通工具',
        jobAnswers8: '我想找份工作',
        quitAnswers5: '我想退出',
        Answers200: '再見！',
        dashoundBusCenterSimplyPutBusParkingBotmessage: '這是Dashound巴士中心。簡而言之，巴士停車位。',
        clearAnswers39: '我明白了。',
        listBusesWorkRentBotmessage: '以下是可供您使用的公共汽車列表。您可以租用任何人。',
        Answers201: '好吧，那麼。',
        greatChoiceStartCarryingPeoplePassportCdlbLicenseBotmessage: '非常好的選擇！現在要開始開車接單，您需要持有護照和CDL ： B執照。',
        readyAnswers8: '好了，我準備好了。',
        Answers202: '不，謝謝。',
        newsQuitBotmessage: '這就是消息...你確定要退出嗎？',
        Answers203: '是的，沒錯。',
        changedMindAnswers7: '不，我改主意了',
        deliverGoodsReturnGiveRewardBotmessage4: '是的，你可以幫我送貨，我會獎勵你',
        Answers204: '好吧，我會幫忙。',
        Answers205: '不是現在。',
        cancelQuestBotmessage12: '你確定要取消任務嗎？',
        Answers206: '是的，我做的。',
        Answers207: '不，我不知道。',
        romanTurchakName: 'Roman Turchak',
        carMarketOwnerDescription: '汽車市場所有權',
        Botmessage15: '嘿，我能幫你嗎？',
        placeAnswers38: '這是什麼地方？',
        sellCarAnswers: '如何賣車？',
        buyCarAnswers: '如何購買汽車？',
        goodbyeAnswers29: '-再見',
        carMarketBuyExchangeCarsBotmessage: '這是一個汽車市場，您可以在這裡購買和兌換汽車。 ',
        clearAnswers40: '我明白了。',
        sellCarPressCarMarketPutSaleBotmessage: '如要銷售車輛，您需要開車來這裡，然後點擊G > Auto market > List for sale。你指定價格標籤並等待客戶！',
        Answers208: '謝謝',
        buyCarSignSalePressBotmessage: '如要購買汽車，請前往任何有出售跡象的汽車，然後按E。',
        Answers209: '謝謝',
        harryTrimbleName: 'Harry Trimble',
        hospitalWorkerDescription2: '醫院員工',
        Botmessage16: '嗨，有什麼可以幫你的嗎？',
        placeAnswers39: '這是什麼地方？',
        buyMedicineAnswers2: '我需要購買藥品',
        giveNoteAnswers: '把字條給我',
        goodbyeAnswers30: '-再見',
        cityHospitalMedicalInstitutionConductsInpatientExaminationTreatmentPatientsBotmessage2: '這是一家城市醫院，是一家對患者進行住院檢查和治療的醫療機構。',
        clearAnswers41: '我明白了。',
        problemProvideBasicSetMedicationsRecuperationBotmessage2: '沒問題，我可以提供一套基本的藥物來恢復力量。你同意嗎？',
        Answers210: '是的，請。',
        Answers211: '不，謝謝。',
        alexRiveroName: 'Alex Rivero',
        hospitalWorkerDescription3: '醫院員工',
        Botmessage17: '嗨，有什麼可以幫你的嗎？',
        placeAnswers40: '這是什麼地方？',
        buyMedicineAnswers3: '我需要購買藥品',
        goodbyeAnswers31: '-再見',
        cityHospitalMedicalInstitutionConductsInpatientExaminationTreatmentPatientsBotmessage3: '這是一家城市醫院，是一家對患者進行住院檢查和治療的醫療機構。',
        clearAnswers42: '我明白了。',
        problemProvideBasicSetMedicationsRecuperationBotmessage3: '沒問題，我可以提供一套基本的藥物來恢復力量。你同意嗎？',
        Answers212: '是的，請。',
        Answers213: '不，謝謝。',
        aleksanderMedvedevName: 'Aleksander Medvedev',
        travelerDescription: '旅行者',
        friendTravelingMountainsBotmessage: '嘿，老兄，我在山上旅行',
        Answers214: '有什麼我可以做的？',
        cancelQuestAnswers13: '我想取消任務',
        giveCokeAnswers: '給一條賭注',
        giveProvisionsAnswers: '放棄條款',
        giveKnifeAnswers: '把刀還我',
        byeAnswers: '再見，再見',
        friendHappyBringColaDieBotmessage: '夥計，如果你給我帶一罐可樂，我會很樂意。我要去死於脫水。',
        Answers215: '好吧，我會幫你。',
        Answers216: '不是現在。',
        cancelQuestBotmessage13: '你確定要取消任務嗎？',
        Answers217: '是的，我做的。',
        Answers218: '不，我不知道。',
        jengasGasseName: 'Jengas Gasse',
        fishermanDescription: '漁夫',
        friendBotmessage: '嘿，哥們，你需要什麼嗎？',
        placeAnswers41: '這是什麼地方？',
        fishAnswers: '怎麼釣魚？',
        Answers219: '有什麼我可以做的？',
        cancelQuestAnswers14: '我想取消任務',
        completedTaskAnswers9: '我已經完成了任務',
        byeAnswers2: '再見，再見',
        riverLotFishSwimmingPopularPlaceFishermenBotmessage: '這是一條河，有很多魚在遊泳，讓它成為釣魚人的熱門地點',
        clearAnswers43: '我明白了。',
        fishermanBuyFishingRodBaitNormalCatchBotmessage: '看來你想當漁夫了好吧，首先，你需要買一些釣竿和誘餌，以便有一個正常的漁魚，你還需要取得釣魚執照。接下來，我們有不同的漁區。首先，在河上捕魚，當你變得更有經驗時，你可以在湖裡甚至在海裡釣魚。你可以在市場上賣魚，祝你好運！',
        clearAnswers44: '我明白了。',
        catchFishReturnGiveRewardBotmessage: '是的，你可以幫我釣魚，我會獎勵你',
        Answers220: '好吧，我會幫忙。',
        Answers221: '不是現在。',
        cancelQuestBotmessage14: '你確定要取消任務嗎？',
        Answers222: '是的，我做的。',
        Answers223: '不，我不知道。',
        michaelJordanName: 'Michael Jordan',
        basketballPlayerDescription: '籃球手',
        playingBasketballBotmessage: '嘿，我們在這裡打籃球嗎？',
        Answers224: '有什麼我可以做的？',
        cancelQuestAnswers15: '我想取消任務',
        giveBoomboxAnswers: '把吊箱給我',
        byeAnswers3: '再見，再見',
        musicSuperGameBuyBotmessage: '是的，你可以幫助我。我們沒有超級遊戲的音樂，你能買嗎？',
        Answers225: '是啊，好吧。',
        Answers226: '不是現在。',
        cancelErrandBotmessage: '你真的要取消我的作業嗎？',
        Answers227: '是的，我做的。',
        Answers228: '不，我不知道。',
        kirillTereshinName: 'Kirill Tereshin',
        jockDescription: '鴨子',
        dryschRockingChairBotmessage: '嘿，貓，這是一個搖椅，你需要的東西嗎？',
        Answers229: '有什麼我可以做的？',
        cancelQuestAnswers16: '我想取消任務',
        giveBarsAnswers: '把條子給我',
        byeAnswers4: '再見，再見',
        candyBarsBuyBotmessage: '是的，你可以幫助我。我們沒有糖果吧，你會買嗎？',
        Answers230: '是啊，好吧。',
        Answers231: '不是現在。',
        cancelErrandBotmessage2: '你真的要取消我的作業嗎？',
        Answers232: '是的，我做的。',
        Answers233: '不，我不知道。',
        claireBoucherName: 'Claire Boucher',
        citizenDescription: 'CITIZEN',
        troubleHappenedFindPersonCloseBotmessage: '哦，麻煩發生在我身上。我找不到親近的人',
        Answers234: '有什麼我可以做的？',
        cancelQuestAnswers17: '我想取消任務',
        calmAnswers: '放心',
        byeAnswers5: '再見，再見',
        husbandMissingGodBotmessage: '我的丈夫不見了，上帝，我該怎麼辦？',
        calmAnswers2: '冷靜下來。',
        Answers235: '哦，就是這樣。',
        cancelErrandBotmessage3: '你真的要取消我的作業嗎？',
        Answers236: '是的，我做的。',
        Answers237: '不，我不知道。',
        albertoBoucherName: 'Alberto Boucher',
        hunterDescription: '獵人',
        huntingGameBotmessage: '嘿，我是來看遊戲的，你需要什麼嗎？',
        hunterAnswers: '你是獵人？',
        cancelTaskAnswers: '我想取消分配',
        giveBandagesAnswers: '把繃帶給我',
        handWeaponAnswers: '放棄你的武器',
        giveCartridgesAnswers: '送出彈藥',
        byeAnswers6: '再見，再見',
        cancelErrandBotmessage4: '你真的要取消我的作業嗎？',
        Answers238: '是的，我做的。',
        Answers239: '不，我不知道。',
        harryJeffersonName: 'Harry Jefferson',
        postopManagerDescription: 'PostOP管理器',
        Botmessage18: '嗨，有什麼可以幫你的嗎？',
        Answers240: '有什麼我可以做的？',
        completedTaskAnswers10: '我已經完成了任務',
        cancelQuestAnswers18: '我想取消任務',
        jobPostmanAnswers: '我想找份郵差工作',
        quitJobAnswers6: '我想辭掉工作',
        startWorkingAnswers3: '我想開始工作',
        stopWorkingAnswers3: '我想停止工作',
        rentTransportAnswers6: '我想租交通工具',
        goodbyeAnswers32: '-再見',
        listTrucksWorkRentBotmessage3: '這是可供您使用的卡車清單。您可以租用任何人。',
        Answers241: '好吧，那麼。',
        passportLicensesCdlDocumentsBotmessage: '爲此，您需要提供：護照、許可證： CDL: B。您是否有必要的文件？',
        Answers242: '是的，我是。',
        Answers243: '不，我不知道。',
        deliverBoxesReturnGiveRewardBotmessage: '是的，你可以幫我送箱子，我會給你一個獎勵',
        Answers244: '好吧，我會幫忙。',
        Answers245: '不是現在。',
        goodSigningAgreementTerminationEmploymentContractBotmessage2: '我們是否簽署終止協議？',
        Answers246: '是的，我是。',
        Answers247: '不，我不知道。',
        cancelQuestBotmessage15: '你確定要取消任務嗎？',
        Answers248: '是的，我做的。',
        Answers249: '不，我不知道。',
        karenRosenbergName: 'Karen Rosenberg',
        bartenderDescription7: '酒保',
        Botmessage19: '嗨，我能幫你嗎？',
        placeAnswers42: '這是什麼地方？',
        showMenuAnswers8: '顯示選單',
        Answers250: '待會見',
        beanMachineCoffeeShopOrderCoffeeTasteBotmessage: '這是豆機咖啡店。在這裡，您可以訂購符合您口味的咖啡',
        clearAnswers45: '我明白了。',
        antonDudushkinName: 'Anton Dudushkin',
        businessmanDescription: '前商人',
        Botmessage20: '嘿，你想要什麼？',
        Answers251: '有什麼我可以做的？',
        cancelQuestAnswers19: '我想取消任務',
        giveAidKitAnswers: '提供急救箱',
        giveDonutAnswers: '把甜甜圈拿走',
        byeAnswers7: '再見，再見',
        friendHurtMoneyMedicineBuyBotmessage: '嘿，夥計，我傷了自己在這裡，我沒有錢買藥。',
        Answers252: '是啊，好吧。',
        Answers253: '不，我不知道。',
        cancelErrandBotmessage5: '你真的要取消我的作業嗎？',
        Answers254: '是的，我做的。',
        Answers255: '不，我不知道。',
        artemDudushkinName: 'Artem Dudushkin',
        shopOwnerDescription: '商店所有者',
        Botmessage21: '嘿，我能幫你嗎？',
        Answers256: '你是什麼人？',
        cancelQuestAnswers20: '我想取消任務',
        brotherDudushkinAnswers: '你是杜杜斯金兄弟？',
        giveFoodAnswers: '把食物扔了',
        byeAnswers8: '再見，再見',
        businessmanBotmessage: '我只是一個生意人，不是嗎？',
        byeAnswers9: '再見，再見',
        cancelErrandBotmessage6: '你真的要取消我的作業嗎？',
        Answers257: '是的，我做的。',
        Answers258: '不，我不知道。',
        miraLazarovaName: '米拉·拉扎羅娃',
        drivingSchoolEmployeeDescription2: '駕駛學校的女性員工',
        miraLazarovaDrivingSchoolAssistantInterestedBotmessage: '你好，我叫Mira Lazarova ，我是一名駕駛學校助理。你對什麼感興趣？',
        placeAnswers43: '這是什麼地方？',
        driversLicenseAnswers: '我需要駕駛執照',
        goodbyeAnswers33: '-再見',
        drivingSchoolLicensesCategoriesVehiclesBotmessage2: '這是一所駕駛學校。您可以在這裡獲得不同類別的車輛執照。',
        clearAnswers46: '一切都很清楚',
        startExamFloorExamRoomTestBotmessage: '要開始考試，請到考試室的二樓進行考試',
        Answers259: '謝謝',
        garryPenkinName: 'Garry Penkin',
        nettleBuyerDescription: 'NETTLE買家',
        Botmessage22: '嘿，你需要什麼嗎？',
        placeAnswers44: '這是什麼地方？',
        exchangeBushesAnswers: '我想交易灌木叢',
        goodbyeAnswers34: '-再見',
        hutBotmessage: '這是我的房子，你不喜歡嗎？',
        clearAnswers47: '一切都很清楚',
        hopeCopBushExchangedSmallBagsBotmessage: '希望你不是警察你看，灌木可以換小袋子。',
        yeahAnswers: '是啊，來吧。',
        Answers260: '不，謝謝。',
        trevorDavinciName: 'Trevor Davinci',
        pierOwnerDescription3: '碼頭擁有者',
        gladBotmessage3: '很高興見到你，我怎麼幫你？',
        placeAnswers45: '這是什麼地方？',
        boatAnswers3: '我需要一艘船',
        goodbyeAnswers35: '-再見',
        greatPlaceFishingEnthusiastsGatherBeautifulQuietWaterSoundsNatureBotmessage3: '這裡是釣魚愛好者的好去處。比靜水、大自然的聲音和火焰更美麗的東西。',
        clearAnswers48: '我明白了。',
        listBoatsRentBotmessage3: '這是可用的船隻清單。您可以租用任何人。',
        Answers261: '好吧，那麼。',
        Answers262: '不，謝謝。',
        nieraAutomataName: 'Niera Automata',
        studentDescription: '學生',
        interestedBotmessage2: '嘿，有什麼你感興趣的嗎？',
        educationalFestivalAnswers: '告訴我們學習節的情況',
        exchangeBooksAnswers: '我想換書',
        byeAnswers10: '再見，再見',
        septemberEducationalFestivalUniversityHiddenHundredsBooksBotmessage: '9月1日12: 00至9月8日21: 00 ，我們有一個訓練節。我們的大學隱藏了數百本舊書遍布全州。我們的任務是尋找綠色或紅色的禮物，並將內容帶到這裡，收集的書籍會獲得獎勵！',
        clearAnswers49: '我明白了。',
        exchangeCollectedItemsRewardsBotmessage: '你可以將收集的道具兌換成獎勵',
        Answers263: '好吧，那麼。',
        Answers264: '我不想去',
        emmaRodgersName: 'Emma Rodgers',
        bartenderDescription8: '酒保',
        Botmessage23: '你好，你想要什麼？',
        placeAnswers46: '這是什麼地方？',
        showMenuAnswers9: '顯示選單',
        Answers265: '待會見',
        palaceClubBotmessage2: '這是宮殿俱樂部。',
        clearAnswers50: '我明白了。',
        guanghuiChenName: '陳光輝',
        gunsmithDescription: 'Gunsmith',
        interestedBotmessage3: '您好，您對什麼感興趣？',
        placeAnswers47: '這是什麼地方？',
        showProductAnswers: '向我展示產品',
        Answers266: '待會見',
        gunShopBotmessage: '這是我的槍店。',
        clearAnswers51: '我明白了。',
        productBotmessage: '哦，你想看看我的商品？',
        Answers267: '好吧，那麼。',
        Answers268: '我不想去',
        shengliWangName: 'Shengli Wang',
        jewelerDescription: '珠寶商',
        Botmessage24: '您好，您需要什麼？',
        placeAnswers48: '這是什麼地方？',
        showProductAnswers2: '顯示您的產品',
        Answers269: '待會見',
        jewelryStoreJewelrySchemeFeatureFilmBotmessage: '這是我的珠寶店。我在這裡做珠寶在"長片..."計劃。',
        clearAnswers52: '我明白了。',
        hepingYangName: '合平楊',
        tailorDescription: '裁縫',
        Botmessage25: '嘿，你需要什麼？',
        placeAnswers49: '這是什麼地方？',
        showAssortmentAnswers: '顯示分類',
        Answers270: '待會見',
        tailorShopSewOrderBuyUnnecessaryClothesBotmessage: '這是一個裁縫店。我可以做我需要訂購的東西，但我也會購買不必要的衣服來製作新衣服',
        clearAnswers53: '我明白了。',
        xinhuaZhouName: '新化周',
        pyrotechnicDescription: '火工技工',
        Botmessage26: '嘿，你需要什麼？',
        placeAnswers50: '這是什麼地方？',
        showProductAnswers3: '顯示您的產品',
        Answers271: '待會見',
        shopSellingFireworksBotmessage: '這是我的店，我在賣煙火',
        clearAnswers54: '我明白了。',
        tsaiName: '安蔡',
        mushroomPickerDescription: '蘑菇機',
        Botmessage27: '嘿，你需要什麼？',
        placeAnswers51: '這是什麼地方？',
        sellMushroomsAnswers: '我想賣蘑菇',
        Answers272: '待會見',
        mushroomShopBotmessage: '這是我的蘑菇店',
        clearAnswers55: '我明白了。',
        gretaThunbergName: 'Greta Thunberg',
        ecologistDescription: '生態學家',
        interestsBotmessage: '您好！有興趣',
        placeAnswers52: '這是什麼地方？',
        showProductAnswers4: '顯示您的產品',
        Answers273: '待會見',
        buyGarbageThrowBuyGarbageMakingBotmessage: '我要收拾你扔掉的垃圾我從你那裡買垃圾，讓環境更乾淨',
        clearAnswers56: '我明白了。',
        rogerPhillipsName: '羅傑·菲利普斯',
        burgerShotManagerDescription: 'Burger Shot Manager',
        managerHigherEducationWorkBurgerShotBotmessage: '嗨，我是Burger Shot的研究生經理，您需要什麼嗎？',
        placeAnswers53: '這是什麼地方？',
        workAnswers6: '如何在這裡工作？',
        Answers274: '待會見',
        burgerShotEarnMoneyBotmessage: '這是漢堡店，你可以在這裡賺錢',
        clearAnswers57: '我明白了。',
        simpleOrderQuicklyAssembleBurgerWorkBotmessage: '這很簡單，你收到訂單，你必須快速獲得一個漢堡。這就是所有的工作！',
        Answers275: '好吧，那麼。',
        evaLevanteName: 'Eva Levante',
        eventOrganizerDescription: '事件限制器',
        evaKnowledgeBotmessage: '嗨，我是艾娃，她會幫助你獲得任何知識！',
        placeAnswers54: '這是什麼地方？',
        exchangeRewardsAnswers: '獎勵兌換',
        basketAnswers: '我想要一個筐子',
        Answers276: '待會見',
        exchangeCollectedItemsRewardsBotmessage2: '你可以將收集的道具兌換成獎勵',
        Answers277: '好吧，那麼。',
        Answers278: '我不想去',
        halloweenShopBotmessage: '這是我的萬聖節商店',
        clearAnswers58: '我明白了。',
        pacoName: 'Paco',
        jengasDogDescription: '狗Jengas',
        WoofWoofBotmessage: '* Woof Woof *',
        Answers279: '你在這裡幹什麼？',
        beautifulAnswers: '你真英俊！',
        byeAnswers11: '再見，再見',
        businessDrunkSoberBotmessage: '這不關你的事，喝醉了。',
        ahaAnswers: '嗯哼',
        wakeTalkingBotmessage: '是的，謝謝，但你應該醒來，因為我不說話。',
        Answers280: '哦，來吧！',
        charlesRichardsName: '查爾斯·理查茲',
        stallCashierDescription: '攤位收款人',
        interestedBotmessage4: '您好，您對什麼有興趣嗎？',
        placeAnswers55: '這是什麼地方？',
        showProductAnswers5: '顯示您的產品',
        Answers281: '待會見',
        dinerBuyFoodDrinksBotmessage: '這是我的餐廳，你可以在這裡買食物和飲料',
        clearAnswers59: '我明白了。',
        yongshengLiName: '李永生',
        mechanicDescription2: '技工',
        interestedBotmessage5: '您好，您對什麼有興趣嗎？',
        placeAnswers56: '這是什麼地方？',
        showProductAnswers6: '顯示您的產品',
        Answers282: '待會見',
        sellMechanicalPartsComponentsEducationalPurposesBotmessage: '我在這裡銷售機械零件和各種組件，用於教育目的',
        clearAnswers60: '我明白了。',
        evaLevanteName2: 'Eva Levante',
        eventOrganizerDescription2: '事件限制器',
        evaInterestedBotmessage: '嗨，我是伊娃，你有興趣的東西嗎？',
        placeAnswers57: '這是什麼地方？',
        christmasGiftsAnswers: '聖誕禮物',
        christmasGiftsAnswers2: '聖誕禮物',
        questAnswers13: '我想去探索一下',
        completedQuestAnswers: '我已經完成了任務',
        cancelQuestAnswers21: '我想取消任務',
        exchangeRewardsAnswers2: '獎勵兌換',
        Answers283: '待會見',
        shopDedicatedOrganizingEventsBotmessage: '這是我的商店！我通過組織各種活動來炫耀自己',
        clearAnswers61: '我明白了。',
        christmasDecemberJanuaryBotmessage: '聖誕節一到，就過來！ 12月25日至1月15日',
        Answers284: '謝謝',
        receiveGiftsFindCaramelCanesChristmasBallsGarlandsExchangedGiftsBotmessage: '要獲得禮物，你必須找到焦糖手杖、聖誕舞會和裝飾，才能兌換禮物。你可以為我的任務、砍伐樹木或在國家各處找到雪人（或寶藏！ ）取得它們。在大學附近的樹下也有禮物。每天都能獲得1份禮物！祝你好運！',
        Answers285: '謝謝',
        exchangeCollectedItemsRewardsBotmessage3: '你可以將收集的道具兌換成獎勵',
        Answers286: '好吧，那麼。',
        Answers287: '我不想去',
        deliverGiftsHousesReturnReceiveSweetsExchangedBotmessage: '請幫我送禮物回家，你會收到他們的糖果，可以在我的房源換取。',
        Answers288: '好吧，我會幫忙。',
        Answers289: '不是現在。',
        cancelQuestBotmessage16: '你確定要取消任務嗎？',
        Answers290: '是的，我做的。',
        Answers291: '不，我不知道。',
        melissaAdlerName: 'Melissa Adler',
        auctionEmployeeDescription: '拍賣員工',
        melissaAdlerAssistantPlaceInterestedBotmessage: '你好，我叫Melissa Adler ，我是一名助手。你對什麼感興趣？',
        placeAnswers58: '這是什麼地方？',
        goodbyeAnswers36: '-再見',
        auctionPutSaleBuyPropertiesBotmessage: '這是拍賣。您可以在這裡出售或購買不同的物業。從物件到生意！',
        clearAnswers62: '一切都很清楚',
        chrisFormageName: 'Chris Formage',
        headChurchDescription: '教堂主管',
        sonDaughterRepentCrimesEpsilonBotmessage: '我的兒子或女兒，在ε星人面前悔過你的過失',
        placeAnswers59: '這是什麼地方？',
        marriedAnswers: '我怎麼結婚？',
        changeThemeChurchAnswers: '改變教會的主題',
        goodbyeAnswers37: '-再見',
        communityCelebrateMarriageMournDeadPrayGoodFutureBotmessage: '這是我們的社區，我們在那裡慶祝婚姻，哀悼死者，為美好的未來祈禱',
        clearAnswers63: '一切都很清楚',
        startWeddingActChooseWeddingThemeChurchArchBotmessage: '要開始婚禮表演，請選擇教堂裡的婚禮主題，然後前往花朵弧。',
        clearAnswers64: '一切都很清楚',
        jackHaleyName: '傑克·海利',
        woodcutterDescription: '伐木工',
        requiredBotmessage: '嘿，你怎麼樣？',
        placeAnswers60: '這是什麼地方？',
        showProductAnswers7: '顯示您的產品',
        Answers292: '待會見',
        brainsMakePersonHappyHappinessThingBotmessage: '大腦不會讓一個人快樂，而快樂是世界上最好的東西。所以我開了木柴店',
        clearAnswers65: '我明白了。',
        kenrickRogerName: 'Kenrick Roger',
        fishermanDescription2: '漁夫',
        Botmessage28: '你好，我能爲你做什麼？',
        placeAnswers61: '這是什麼地方？',
        showProductAnswers8: '顯示您的產品',
        Answers293: '待會見',
        shopSellFishBuyFishKindsBotmessage: '這是我的店，我賣魚，還可以買到不同類型的魚！',
        clearAnswers66: '我明白了。',
        taylorMiltonName: 'Taylor Milton',
        mushroomPickerDescription2: '蘑菇機',
        travelerBotmessage: '您好，旅行者，您需要我提供什麼？',
        placeAnswers62: '這是什麼地方？',
        workMushroomPickerAnswers: '如何做蘑菇制造商？',
        Answers294: '待會見',
        mushroomFieldMushroomsGrowingCollectSellMarketBotmessage: '這是一個蘑菇田，有蘑菇可以收割，然後在市場上出售',
        clearAnswers67: '我明白了。',
        simpleCompetitorsBuyKnifeRunBotmessage: '很簡單，雖然我不喜歡競爭對手，但我會給你一個提示。先買一把刀，然後在田野裡跑來跑去，拿起豬。如果你累了，可以在市場上出售這些蘑菇。順便說一句，如果你喜歡，還有其他蘑菇位置，但你需要學會如何收集它們。祝你好運！',
        Answers295: '謝謝',
        vladilenaMilizeName: 'Vladilena Milize',
        citizenDescription2: 'CITIZEN',
        heyBotmessage: '嘿，怎麼了？',
        Answers296: '你在幹嘛你在幹嘛',
        Answers297: '告訴我你自己。',
        Answers298: '待會見',
        stuckScreenBeautifulPlaceBotmessage: '我沒有做太多。更像是我被困在了美麗的地方',
        Answers299: '我明白了。',
        commanderShineiNosenGroupSpearheadSquadronLeavingDemandingOrdersBotmessage: '我是信奈諾岑集團和槍頭中隊的前任指揮官，在槍頭中隊離開後，我以苛刻的命令和高素質的領導而聞名，因此我獲得了所有人中稱呼的血腥萊茵...',
        Answers300: '我明白了。',
        joaquinDiazName: 'Joaquin Diaz',
        musicianDescription: '音樂人',
        interestedBotmessage6: '您好，您對什麼有興趣嗎？',
        Answers301: '你在幹嘛你在幹嘛',
        Answers302: '告訴我你自己。',
        Answers303: '待會見',
        breakWorkingDayStartBurnBotmessage: '我休息了一下，有時每天工作，你開始疲憊。為了防止這種情況發生，有時你必須分散注意力',
        Answers304: '我明白了。',
        spanishMusicianFolkloristHonoraryPresidentDepartmentTraditionsUniversityBotmessage: '我是西班牙音樂家、民俗學家、巴利亞多利爾大學傳統系榮譽總裁、拉普裏西馬·康塞普西翁皇家藝術學院永久院士。',
        Answers305: '我明白了。',
        edmundHillaryName: '埃德蒙·希拉裏',
        touristDescription: '遊客',
        travelerInterestedBotmessage: '嘿，旅行者，你對什麼感興趣嗎？',
        Answers306: '你在幹嘛你在幹嘛',
        Answers307: '告訴我你自己。',
        Answers308: '待會見',
        conquerPlacesStateMomentPlaceBotmessage: '我徵服了這個州的不同區域。這是我到過最好的地方。除了一個極端...',
        Answers309: '我明白了。',
        zealandExplorerMountaineerPeopleAscendedChilliadBotmessage: '我是紐西蘭探險家兼登山家。我是和Sherpa Tenzing一起開拓Chilliad的兩個人之一。',
        Answers310: '我明白了。',
        clintonDrakeName: '克林頓·德雷克',
        bartenderDescription9: '酒保',
        Botmessage29: '你好，你想要什麼？',
        placeAnswers63: '這是什麼地方？',
        showMenuAnswers10: '顯示選單',
        Answers311: '待會見',
        bookmakersOfficeBotmessage: '這是一個博彩公司的辦公室。',
        clearAnswers68: '我明白了。',
        laverneNevilleName: 'Laverne Neville',
        bartenderDescription10: '酒保',
        Botmessage30: '你好，你想要什麼？',
        placeAnswers64: '這是什麼地方？',
        showMenuAnswers11: '顯示選單',
        Answers312: '待會見',
        blackWoodsSaloonBotmessage: '這是我們的黑樹林酒吧。',
        clearAnswers69: '我明白了。',
        mikeScofieldName: 'Mike Scofield',
        cookDescription: '廚師',
        Botmessage31: '嘿，你需要什麼嗎？',
        placeAnswers65: '這是什麼地方？',
        eatAnswers: '我餓了',
        Answers313: '待會見',
        losSantosFederalPrisonReasonsServeTermCompleteBotmessage: '這是洛斯桑託斯聯邦監獄。他們來這裡有多種原因。若要完成條款，您需要完成分配的任務',
        clearAnswers70: '我明白了。',
        lincolnBurrowsName: 'Lincoln Burrows',
        wardenDescription2: '總監',
        prisonerBotmessage: '你需要什麼，囚犯？',
        placeAnswers66: '這是什麼地方？',
        serveTimeAnswers: '如何服刑',
        completeTasksAnswers: '我想完成任務',
        Answers314: '待會見',
        losSantosFederalPrisonReasonsServeTermCompleteBotmessage2: '這是洛斯桑託斯聯邦監獄。他們來這裡有多種原因。要完成條款，您需要完成分配的任務',
        clearAnswers71: '我明白了。',
        serveTermCompleteTasksBotmessage: '若要完成學期，您需要完成分配的任務。你可以從我那裡接下這些任務。',
        Answers315: '好吧，那麼。',
        creesFormingName: 'Cris Formage',
        quenterDescription: 'Quenter',
        Botmessage32: '嘿，我能幫你嗎？',
        Answers316: '這是怎麼回事？',
        showTasksAnswers: '顯示作業',
        Answers317: '待會見',
        giveTasksJuicyRewardsCompletingBotmessage: '我分派不同的任務，你可以完成這些任務獲得多汁的獎勵',
        clearAnswers72: '我明白了。',
        edgarLotharName: 'Edgar Lothar',
        cookDescription2: '廚師',
        Botmessage33: '嘿，你需要什麼嗎？',
        showMenuAnswers12: '顯示選單',
        Answers318: '待會見',
        blitzSecretName: '閃電祕密',
        smugglerDescription: '走私犯',
        callBlitzEngagedSmugglingSearchingStrangeThingsBotmessage: '你可以叫我閃電我在做走私生意，尋找奇怪的東西，並提供稀有物品。我的溝通圈子裡有很多有用的聯繫人，從海賊到政客，所以我向你保證，你會在這裡發現非常不尋常和高品質的東西。',
        showProductAnswers9: '向我展示產品',
        Answers319: '待會見',
        proxyMonkeyName: '代理猴子',
        unknownDescription: '未知',
        thingSellSpecialThingsFindNiceThingsVastnessBotmessage: '你唯一需要知道的是，透過我，你可以賣你不能使用的特殊東西。我還有一些您在廣敞的城市中找不到的好東西。',
        showProductAnswers10: '向我展示產品',
        Answers320: '待會見',
        jungLeeName2: 'Jung Lee',
        travelGuideDescription5: '旅行指南',
        losSantosBotmessage5: '歡迎來到洛斯桑託斯！有什麼我可以幫你的嗎？',
        questAnswers14: '關於任務',
        questAnswers15: '關於任務',
        questAnswers16: '關於任務',
        startGameAnswers5: '如何開始遊戲？',
        initialQuestsAnswers5: '開始任務',
        cancelQuestAnswers22: '我想取消任務',
        transportationAnswers6: '我需要交通工具',
        Answers321: '待會見',
        simpleTasksBeginningJourneyExplainBotmessage5: '我們非常簡單，我有任務可以在旅程開始時為您提供協助，並解釋這裡的內容和方式。如果您需要更多資訊，歡迎隨時詢問',
        initialTasksAnswers5: '初始分配',
        initialTransportAnswers5: '初始運輸',
        earnMoneyAnswers5: '我在哪裏可以賺錢？',
        Answers322: '我明白了。',
        statesTransportLegsWorryRentBotmessage5: '在沒有交通工具的州，就像沒有腿，你不能走遠。但別擔心，我有交通工具可供您租用，如果您想節省費用，附近有地鐵。',
        showTransportRentAnswers5: '向我展示租用的車輛',
        Answers323: '我明白了。',
        initialTasksStatesEasierFutureBotmessage5: '我的最初任務將幫助你習慣我們的狀態，以便未來對你來說更容易。你想嘗試嗎？',
        Answers324: '是的，我做的。',
        Answers325: '稍後',
        moneyInitialCapitalNewcomersStatesInterestingJobsBotmessage5: '沒有錢，即使你有種子錢，你也不會有太多進步。對於州內的新來者，我們有有趣的工作要做',
        workBuilderAnswers5: '做建築工人',
        workingButcherAnswers5: '做屠夫',
        jobLumberjackAnswers5: '伐木工的工作',
        farmersJobAnswers5: 'Farmer \'s work',
        fishingJobsAnswers5: '釣魚',
        Answers326: '有什麼更好的？',
        Answers327: '我明白了。',
        fishingInterestingBoringBeginBuyBotmessage5: '有些人可能對釣魚有興趣，有些人可能很無聊。首先，您需要購買釣竿和誘餌（在「全天候商店」地圖上） ，然後前往河邊（在「釣魚區」地圖上）。您可以在市場上賣魚！',
        Answers328: '我明白了。',
        carsRentInterestedBotmessage5: '我有幾輛車。您可以租用其中之一。',
        Answers329: '是的，當然。',
        Answers330: '不，謝謝。',
        nahHappenLoaderConstructionSiteRentBotmessage5: '你希望它所有的一次？不，不，不是那樣的。先是做裝載師或在建築現場，然後你租了一間公寓然後來了。美國夢的曲線將通向哪裡。',
        Answers331: '好吧，那麼。',
        cancelQuestBotmessage17: '你確定要取消任務嗎？',
        Answers332: '是的，我做的。',
        Answers333: '不，我不知道。',
        trevorWhiteName: '特雷弗·懷特',
        minerDescription: '礦工',
        hardWorkerBotmessage: '嘿，工作男孩，你需要從我什麼？',
        placeAnswers67: '這是什麼地方？',
        workMinerAnswers: '如何做礦工？',
        showProductAnswers11: '向我展示產品',
        Answers334: '待會見',
        quarryMineralsHuntedPurchasedOrganizationsBotmessage: '這是一個採石場。這裡有礦物質，通常由組織購買',
        clearAnswers73: '我明白了。',
        memberOrganizationsCalledFamiliesActivateFamilyContractBuyPickaxeBotmessage: '你需要成為其中一個組織的成員，或他們所稱的家庭！你啟動家庭契約，買一把拾斧，並尋找礦石挖掘。',
        Answers335: '謝謝',
        productBotmessage2: '哦，你想看看我的商品？',
        Answers336: '好吧，那麼。',
        Answers337: '我不想去',
        jungLeeName3: 'Jung Lee',
        travelGuideDescription6: '旅行指南',
        losSantosBotmessage6: '歡迎來到洛斯桑託斯！有什麼我可以幫你的嗎？',
        questAnswers17: '關於任務',
        questAnswers18: '關於任務',
        questAnswers19: '關於任務',
        startGameAnswers6: '如何開始遊戲？',
        initialQuestsAnswers6: '開始任務',
        cancelQuestAnswers23: '我想取消任務',
        transportationAnswers7: '我需要交通工具',
        Answers338: '待會見',
        simpleTasksBeginningJourneyExplainBotmessage6: '我們非常簡單，我有任務可以在旅程開始時為您提供協助，並解釋這裡的內容和方式。如果您需要更多資訊，歡迎隨時詢問',
        initialTasksAnswers6: '初始分配',
        initialTransportAnswers6: '初始運輸',
        earnMoneyAnswers6: '我在哪裏可以賺錢？',
        Answers339: '我明白了。',
        statesTransportLegsWorryRentBotmessage6: '在沒有交通工具的州，就像沒有腿，你不能走遠。但別擔心，我有交通工具可供您租用，如果您想節省費用，附近有地鐵。',
        showTransportRentAnswers6: '向我展示租用的車輛',
        Answers340: '我明白了。',
        initialTasksStatesEasierFutureBotmessage6: '我的最初任務將幫助你習慣我們的狀態，以便未來對你來說更容易。你想嘗試嗎？',
        Answers341: '是的，我做的。',
        Answers342: '稍後',
        moneyInitialCapitalNewcomersStatesInterestingJobsBotmessage6: '沒有錢，即使你有種子錢，你也不會有太多進步。對於州內的新來者，我們有有趣的工作要做',
        workBuilderAnswers6: '做建築工人',
        workingButcherAnswers6: '做屠夫',
        jobLumberjackAnswers6: '伐木工的工作',
        farmersJobAnswers6: 'Farmer \'s work',
        fishingJobsAnswers6: '釣魚',
        Answers343: '有什麼更好的？',
        Answers344: '我明白了。',
        fishingInterestingBoringBeginBuyBotmessage6: '有些人可能對釣魚有興趣，有些人可能很無聊。首先，您需要購買釣竿和誘餌（在「全天候商店」地圖上） ，然後前往河邊（在「釣魚區」地圖上）。您可以在市場上賣魚！',
        Answers345: '我明白了。',
        carsRentInterestedBotmessage6: '我有幾輛車。您可以租用其中之一。',
        Answers346: '是的，當然。',
        Answers347: '不，謝謝。',
        nahHappenLoaderConstructionSiteRentBotmessage6: '你希望它所有的一次？不，不，不是那樣的。先是做裝載師或在建築現場，然後你租了一間公寓然後來了。美國夢的曲線將通向哪裡。',
        Answers348: '好吧，那麼。',
        cancelQuestBotmessage18: '你確定要取消任務嗎？',
        Answers349: '是的，我做的。',
        Answers350: '不，我不知道。',
        kayneChanName: 'Kayne Chan',
        landlordDescription: '房東',
        Botmessage34: '您好，您需要什麼？',
        transportationAnswers8: '我需要交通工具',
        Answers351: '待會見',
        carsRentBotmessage2: '我有一些租車，你想看嗎？',
        Answers352: '是的，當然。',
        Answers353: '不，謝謝。',
        rafeVickersName: 'Rafe Vickers',
        landlordDescription2: '房東',
        Botmessage35: '您好，您需要什麼？',
        transportationAnswers9: '我需要交通工具',
        Answers354: '待會見',
        carsRentBotmessage3: '我有一些租車，你想看嗎？',
        Answers355: '是的，當然。',
        Answers356: '不，謝謝。',
        gpsRoadAnswer: '這是座標！去那裡，他們會告訴你一切。可能要走很長的路，別忘了運輸',
        alreadyGaveHealing: '我已經給你治療了！',
        youAreAlreadyHealed: '你還好嗎，還有哪裡？',
        okay: '好吧，那麼。',
        thanks: '謝謝',
        startHealing: '太好了，我給了你一個治療。（每分鐘+20 hp ）',
        youDontHaveAnything: '對不起，但你什麼也沒有',
        takeYourMoney: '好吧，這是你的錢，你會得到 ${amount}',
        comeInHalloween: '來吧，萬聖節！',
        alreadyGaveYouF: '我以前給過你這個',
        noSpace: '你沒有地方！',
        halloweenBusket: '拿着糖果籃回家去拿些糖果這些糖果可以換取各種價值',
        drivedLic: '駕駛： D -乘用車：涵蓋大多數車輛，不包括摩託車。價格為 ${amount}。',
        drivemLic: '駕駛： M -僅涵蓋摩託車，並且通常與其他權利相結合。這些權利不包括摩託車。價格 ${amount}。',
        cdlbLic: 'CDL: B -商業運輸執照。接載車、小巴、卡車，價格為 ${amount}',
        cdlaLic: 'CDL: A -商業運輸執照。混合車輛：拖拉機+拖車。價格為 ${amount}。',
        okayITake: '好吧，我接受它。',
        noThanks: '不，謝謝。',
        alreadyHaveLic: '你已經有執照了！',
        notEnoughMoney: '對不起，但你沒有足夠的錢。',
        flyingSchoolPrice: '課程費用 ${amount}。您想開始課程嗎？',
        yesIWant: '是的，我做的。',
        alreadyFinishFlyingSchool: '你已經從飛機學校畢業了！',
        finishedFlygingSchoolRecently: '你最近完成了一個課程。來吧 {date}！',
        fishingLic: 'F&H -捕魚執照。價格爲 ${amount} 美元。',
        sorryYouCant: '對不起，但你不能。',
        sorryNoBankCard: '您必須擁有銀行帳戶才能收款。',
        takeDrugs: '好吧，這是你的行李。你會得到 {amount} 件。',
        canTakeJobIn: '你可以接受任務 {date}。',
        leftServerRecently: '對不起，但你最近工作和離開州，來 {date}',
        cantGiveJobNow: '很抱歉，我現在無法分配',
        nothingToDrugTab: '對不起，但你沒什麼可當的',
        canStartJobNow: '好吧，你可以開始工作。',
        alreadyWorking: '對不起，但你已經在某處工作了，走吧',
        needWorkMore: '對不起，但要在這裡工作，你必須工作 {amount} 次。',
        elevatorToWork: '大樓的另一邊，有電梯。上去上班',
        thanksForHelp: '感謝你的幫助！',
        noProblem: '沒問題。',
        youllGetMoney: '好了，好了，你得到 ${amount}。',
        takeMoney: '取錢',
        haventDoneAnything: '所以，你沒有做任何要求給予薪水！',
        continueWork: '繼續工作',
        wannaRentCar: '太好了！想租交通工具嗎？',
        yesPlease: '是的，請。',
        leaveJobFirst: '等一下，你得先退出你的工作 {title}！否則，我們將無法簽署合約。',
        leaveJob: '好吧，我們正在簽署終止協議。',
        thanksBye: '互相，再見',
        youSalary: '這是你的薪水，祝你愉快',
        drugsDrive: '太好了，你的工作是收藏它。這裡是要採取的地標。',
        wantToStartNow: '太好了！想立即開始？',
        thanksForWork: '事情就是這樣和你一起工作真好',
        alreadyTakenTask: '你已經接受了這份工作。先做，然後來。',
        tooManyTasks: '對不起，但已經發出太多任務，所以請稍等片刻。',
        hijackerStart: '他們會發短訊給你！別忘了拿到破解工具他們都在市場上。',
        prisonClosed: '等等，你是怎麼來的？',
        oops: '哎呀。',
        prisonJobs: '這是任務，記住在地圖上。等你做完了，過來，我再派你去',
        canTakeQuestIn: '此任務可接受 {date}',
        bye: '再見，再見',
        goodLuck: '祝你好運',
        willBringNow: '我會得到它。',
        willBeNow: '一切都會好起來的',
        willBuyNow: '我現在就買',
        timeTasksUnderstandAnswers: '這是你第一次來這裡這裡有一項任務讓你弄清楚這裡面有什麼和這裡面有什麼',
        bankImportantPlaceMoneyEvilPeopleDrinkOpenAnswers: '銀行是儲存惡人錢的重要地方。順便說一句，你喝一杯，打開存貨，看看它，也許你會發現一些有趣的東西。這是你幾天的VIP狀態，讓你可以更快地發展。別忘了你的下一個任務！',
        gladVisitedPlacesDrivingLicenseChooseAnswers: '很高興您造訪了不同的地方！接下來，你需要取得駕駛執照。我會加上： CDL像卡車運貨人一樣的工作需要，它最常被選擇為一開始。順便說一句，這裡有一些錢給你的執照。',
        assignmentsGoodLuckStateAnswers: '這是我為你分配的所有任務，祝你在這個狀態下好運！',
        lotCokeReplenishProvisionsAnswers: '哇，謝謝你的可卡因！請幫助我，我需要補充我的用品。我需要這些東西。*傳送清單*。我非常感謝你的幫助，並將回報你應該喜歡的東西',
        rewardWorkFulfillRequestBuyAnswers: '這是你所做工作的獎勵。我要你做我最後的請求。請買一把刀，因為我的是枯燥的，在山上沒有刀是很難的。',
        dimeExploreMountainsAnswers: '非常感謝。這裡有一些錢謝謝你的幫助。我很快就會探索山脈',
        listenMusicPlayBasketballCashRewardAnswers: '非常感謝！現在我們可以聽音樂和打籃球了！這是現金，獎勵來自我',
        fishRewardBackTomorrowAnswers: '非常感謝你幫助魚。這是你的幫助獎勵。如果你想幫忙，明天再來',
        greatJobRewardBackTomorrowAnswers: '做得好，這是你的幫助獎勵。如果你想幫忙，明天再來',
        problemHusbandLostWoodsRiverAnswers: '非常感謝你，我有麻煩了。我失去了我的丈夫在某個地方在樹林裡的河畔。搜尋正在拖延，幫我找到他',
        bringBandagesBandageWoundWolfAttackAnswers: '狼襲擊後，請帶給我三條繃帶來帶傷口',
        bringingBandagesTimeBuyDoublebarreledShotgunLostAnswers: '非常感謝你準時帶繃帶來給我。給我買雙桶，因為我逃離狼羣時弄丟了',
        lotWeaponForgotBuyPiecesAnswers: '謝謝你這件武器。順便說一句，我忘了告訴你，你能為這件武器買12彈藥給我嗎？',
        uhNoteGiveSonWorksAnswers: '給我兒子，他在醫院工作',
        yeahClearDadPlayingHookyGiveAnswers: '是啊，我看到哪裏我的爸爸輟學了。我把它給我媽媽。這是你的獎勵！',
        greatBazookaHandsRealAnswers: '太好了，現在我的火箭筒手是真的了！',
        bindWoundsRatBiteCoughsBuyEatAnswers: '哦，非常感謝。現在我可以把老鼠咬傷*咳嗽*解開了！對了，給我買點吃的',
        decidingFactBrotherKindBusinessAnswers: '感謝你嘗試幫忙，但我真的需要其他一些幫助。我有個哥哥，他在沙灘上有生意',
        brotherAliveInterestinglyThoughtMillionaireKilledAnswers: '哦，你的意思是我的兄弟？他還活着？我不知道我是否認為他成為百萬富翁並被暴徒殺害。順便說一下，我好久沒去帕勒託灣的餐廳了，我真的很想去那裡嘗嘗美食，拿點來，拜託',
        foodBrotherFindContactFindingAnswers: '非常感謝你的食物。關於我哥哥，我會找到他，然後回來找他。謝謝你找到他。',
        giftsDeliveredBonusWorkExchangeReceivedAwardsAnswers: '非常感謝你贈送的禮物，這是完成工作的獎勵！你可以把你的獎勵兌換成我的禮物',
        heyBeerBringGiveTaskAnswers: '嘿，啤酒呢？先把它帶給我，然後我再給你另一個任務',
        drinkAnswers: '酒在哪裏？',
        provisionsAnswers: '不是全部條款',
        buyKnifeAnswers: '拿到刀的時候再來',
        yoBoomboxAnswers: '喲，炸彈盒在哪裏？',
        waitFishSoldAnswers: '等等，你確定魚是賣的嗎？',
        workCompletedAnswers: '工作尚未完成',
        assignmentCompletedAnswers: '我的作業尚未完成',
        pleaseHelpMe: '求求你...求求你...',
        momentAnswers: '等一等',
        uhAhHurtBandagesAnswers: '哦，哦，好痛，沒有繃帶？',
        doublebarreledShotgunAnswers: '雙桶在哪裡？',
        cartridgesAnswers: '子彈呢？',
        chocolatesAnswers: '巧克力呢？',
        atechkaAnswers: '急救箱在哪裏？',
        doughnutsTodayAnswers: '我猜今天沒有甜甜圈了？',
        foodAnswers: '食物在哪裏？',
        completeGiftDeliveryAnswers: '請完成送禮'
    },
    logs: {
        admin_log: {
            fuel: '將燃油等級 {fuel} 設置為車輛 {id}',
            settime: '設定時間為 {hours}:{minutes}:{seconds}',
            resettime: '重置時間',
            setweather: '設定天氣類型 #{weatherId} - {weather}',
            clear: '已取消帳戶 #{accountId}.原因： {reason}',
            givedonate: '{name} #{static} {amount} MC.原因: {reason}',
            givemoney: 'Выдал {name} #{static} ${amount}. Причина: {reason}',
            takemoney: '他從 {name} #{static} ${amount}開始服用。原因： {reason}',
            takebank: 'Забрал с банка у {name} #{static} ${amount}. Причина: {reason}',
            gh: '傳送給我自己 {name} #{static}',
            getcar: '將車輛傳送給自己 #{id} {title}',
            tpcar: '傳送至傳輸 #{carid}',
            incar: '傳送至傳輸 #{carid}',
            gtp: '由GPS標籤傳送',
            delveh: '已刪除車輛 #{id} {title}',
            gw: '已發放的武器 {weaponName} {name} #{static}',
            save: '保留位置 {title} - {x}、 {y}、 {z}、 {r}',
            skin: '已安裝皮膚 {skinName} 到玩家 {name} #{static}',
            excar: '炸掉運輸工具 #{carid}',
            payday: '呼叫PayDay',
            alock: '開車 #{id}',
            aveh: '已啟動車輛 #{id}',
            kick: '來自伺服器 #{static}的休息！原因： {reason}',
            skick: '從伺服器 {name} #{static}悄悄點頭！原因： {reason}',
            warn_banned: 'Забанил {playerOnline, select, 0{оффлайн}} #{staticId} на {amount} дней! Причина: 2-е предупреждение',
            warn: 'Выдал {playerOnline, select, 0{оффлайн}} предупреждение #{staticId}! Причина: {reason}',
            unwarn: '{name} 1未寫警告 #{staticId}！原因： {reason}',
            mute: 'Выдал {playerOnline, select, 0{оффлайн}} мут #{static} на {minutes} минут. Причина {reason}',
            unmute: 'Снял мут с {playerOnline, select, 0{оффлайн}} игрока {name} #{static}. Причина {reason}',
            unban: '取消淘汰 {name} #{staticId}!原因: {reason}',
            ban: 'Забанил {playerOnline, select, 0{оффлайн}} #{static} на {days} дней! Причина: {reason}',
            hardban: 'Выдал {playerOnline, select, 0{оффлайн}} хардбан #{static} на {days} дней! Причина: {reason}',
            unbanip: '取消阻止IP: {ip}',
            hp: 'Поставил {hp}% здоровья {name} #{static}',
            makeleader_remove: '從領袖 {fractionName}中解僱玩家 {name} #{static}',
            makeleader: '使玩家 {name} #{static} 成爲陣營 {fractionName}的領袖',
            templeader_left: '離開了臨時領導人',
            templeader: '使自己成為陣營的臨時領袖 #{id} {fractionName}',
            tempfamily_left: '離開了臨時家族首領',
            tempfamily: '使自己成爲家庭的臨時領袖 #{id} {familyName}',
            gm: '{enter, select, 0{Вышел из режима} 1{Вошёл в режим}} бога',
            inv: '{enter, select, 0{Вышел из режима} 1{Вошёл в режим}} невидимости',
            chide: '{enter, select, 0{Вышел из режима} 1{Вошёл в режим}} невидимости от читеров',
            repair: '修好車子 {id} - {title}',
            spec: '已進入 {name} #{static}追蹤模式',
            specoff: '退出跟蹤模式',
            ajail: 'Посадил {playerOnline, select, 0{оффлайн}} игрока #{static} в тюрьму на {minutes} минут. Причина {reason}',
            unjail: 'Выпустил {playerOnline, select, 0{оффлайн}} игрока #{static} из тюрьмы',
            tph: '傳送到家 {id}',
            tpatm: '已傳送至ATM {id}',
            ctp: '由XYZ坐標傳送： {x}、 {y}、 {z}',
            setdim: '將玩家 #{static} 虛擬世界設置為 {dimension}',
            acuff: '手銬玩家 #{static}',
            auncuff: '從玩家 #{static}中移除袖口',
            setskill: '已安裝技能到玩家 #{static}。技能 {skillName}數量 {value}',
            setlevel: '將 {value} 級設置為玩家 #{static}',
            givelic: '授予 {licName} 授權給玩家 #{static}',
            takelic: '從玩家 #{static}撤回許可證 {licName}',
            tempname: '將臨時名稱設置為 {name}',
            resettempname: '刪除臨時名稱',
            esp: '{toggle, select, 0{Выключил} 1{Включил}} ESP',
            makeadmin: 'Сделал {playerOnline, select, 0{оффлайн}} игрока #{static} админом уровня {level}',
            setprice: 'Установил цену на {title} для {all, select, 1{всех {bizType}} 0{{bizType} #{bizId}}} в размере ${amount}',
            setwarehouse: 'Установил количество товаров {title} для {all, select, 1{всех {bizType}} 0{{bizType} #{bizId}}} в размере {amount}шт',
            giveitem: '發放 $t({title}) 給玩家 #{static} ，金額為 {count}',
            acapture: '將捕捉區域 #{id} 的色彩變更為 {fractionName} [{fractionId}]',
            abizwar: '變更業務屋頂 {tableName} #{id} 至 {fractionName} [{fractionId}]',
            afacilitywar: '變更業務屋頂 {tableName} #{id} 至 {fractionName} [{fractionId}]',
            setmaterials: '設定分數 #{fractionId} 的材料數量 {color} 至 {amount} {fractionName}',
            freeze: '{froze, select, 0{Разморозил} 1{Заморозил}} игрока #{static}',
            spawncars: 'Заспавнил {count} единиц фракционного транспорта ({fractionName})',
            givematerials: '增加 {amount} 件物料至業務 {type} #{id} （業主： {owner}）',
            givewarehouse: '新增 {amount} 項目 {itemTitle} for {type} #{id} （所有者： {owner}）',
            setemail: '將用戶設定為 {login} #{static} 電子郵件 {email} 而不是 {oldEmail}',
            givepromo: '將媒體促銷代碼 {promo} 設置為使用用戶名稱 {login}的播放器',
            removepromo: '已將媒體促銷代碼 {promo} 移至使用者名稱 {login}的玩家',
            aeject: '#{static} 扔出車外',
            auninvite: 'Уволил #{static} из {fractionName}',
            eventon: '在 {x}、 {y}、 {z}建立事件點',
            eventoff: '已刪除活動點',
            togglecasino: '將賭場狀態設為 {toggle}',
            togglebets: '將博彩公司辦公室的狀態設置為 {toggle}',
            togglesafe: '將Vault狀態設置為 {toggle}',
            toggleauction: '將拍賣狀態設置為 {toggle}',
            toggletp: '將傳送端口上的反訊息狀態設置為 {toggle}',
            toggleitemsexchange: '將物料兌換狀態設置為 {toggle}',
            toggleexchange: '將屬性兌換狀態設為 {toggle}',
            toggleanticheat: '將貨車運送員的抗變更狀態設置為 {toggle}',
            toggleminigames: '將迷你遊戲狀態設置為 {toggle}',
            toggleblackjack: '將千斤頂狀態設置為 {toggle}',
            togglehorserace: '將跳躍狀態設置為 {toggle}',
            toggleburger: '將BURGER房間狀態設置為 {toggle}',
            togglegolf: '設定高爾夫狀態為 {toggle}',
            setname: 'Изменил имя {playerOnline, select, 0{оффлайн}} игрока {login} #{static} на ${name}',
            aclearchat: '空聊天',
            ainfect: '受感染 #{infectionId} 玩家 #{static}',
            setvip: '將VIP #{vipLevel} 設置為玩家 #{static} ，持續 {days} 天',
            toggleblackout: '{toggle, select, 0{Выключил} 1{Включил}} blackout',
            hideesp: '{toggle, select, 1{Спрятал} 0{Отобразил}} свой esp',
            giveclothes: '已發放服裝給玩家 #{static}.原因： {comment}',
            hellhound: '叫做地獄犬',
            setphonenumber: '將玩家設定為 #{static} 號 {phoneNumber}',
            setvehiclenumber: '將車輛號碼 #{static} 設為車輛號碼 {vehicleNumber}',
            mutecheck: '檢查玩家 #{static}的MUT',
            addamphitheater: '包括IPL #{id} 露天影院',
            removeamphitheater: '禁用IPL #{id} amphitheater',
            togglegreenzone: '{toggle, select, 1{Выключил} 0{Включил}} зелёную зону {name}',
            reset2fa: '將2FA重設為帳戶 {login}',
            transfervehicle: '車主變更為 {vehicleId} 到 #{newOwnerId}',
            toauction: '拍賣時沒收 {tableName} #{id}.原因： {reason}',
            toauctionplayer: '拍賣時沒收 {tableName} #{id} 從 #{static}.原因： {reason}',
            blockcinema: '{toggle, select, 1{Заблокировал} 0{Разблокировал}} кинотеатр #{id}',
            setcinemavideo: '在影院 #{id}安裝了他的影片',
            coloresp2: '設定ESP2顏色為 #{staticId} 到 {hexColor}',
            esp2: '{toggle, select, 1{Включил} 0{Выключил}} ESP2',
            tpbb: '傳送到廣告牌 #{id}',
            removequeue: '已從廣告牌 #{id}中移除號碼 {date} 上的廣告',
            setqueue: '將隊列設置為 {online}',
            reloadconfig: '重新載入伺服器配置',
            veh: '已建立車輛 #{id} {vehName}',
            givecar: '下達運輸 {model} #{static}',
            tp: '傳送到 #{static}',
            rescue: '復活玩家 #{static}'
        },
        items_log: {
            removeOfflineDemorgan: '已刪除項目（離線Demorgan ）',
            removeFractionClothes: '已移除陣營服裝',
            sellOnMarket: '在市場上銷售',
            backFromDonate: '自延遲項目取得',
            seizedBy: '提款 #{static}',
            pickUpItem: '提出的物品',
            pickUpItemPart: '在 {id} ({type})中部分提高的項目',
            splitItem: '項目分為 {id} ({type})',
            dropItemPart: '物料已部分丟棄',
            dropItem: '物品已丟棄',
            fullUsed: '物品已全額使用',
            partUsed: '物品已部分使用',
            returnFractionClothes: '已退回分數道具',
            takeOffClothesSwap: '衣服拆卸（更換衣服）',
            takeOffClothes: '衣服被移除',
            wearClothes: '衣服穿上了',
            takePart: '{id} ({type})部分取自 {from} {key} 的物料',
            take: '物品已取用 {from}',
            putPart: '將項目放入 {to} {key} ，部分放入 {id} ({type})',
            put: '將項目放入 {to} {key}',
            transferItem: '交易時收到的金額：{tradeId}英鎊'
        },
        money_log: {
            takebank: 'ADM [#{admin}] -/takebank: {reason}',
            givemoney: 'ADM [#{admin}] -/givemoney: {reason}',
            takemoney: 'ADM [#{admin}] -/takeemoney: {reason}',
            auctionReturn: '拍賣時批次#{lot} 的退價',
            auctionSell: '拍賣＃{lot} 批次',
            reportAdsDeclined: '旅居被拒絕（{count} 項）',
            casinoWinHorseRace: '賭場獎金，賽馬',
            lottoWin: '中彩票',
            fractionAward: '獎項： {reason}',
            taxSellHouse: '未付款出售{key} 號房',
            taxSellApartment: '未付款出售{key} 號公寓',
            taxSellAtm: '因未付款而出售的ATM #{key}',
            taxSellBillboard: '廣告牌{key} 因未付款而售出',
            taxSellBiz: '未付款已售出 {tableName} #{key}',
            taxSellHouseSafeReturn: '未付款時從第{key} 號房屋保險箱退款',
            taxSellApartmentSafeReturn: '未付款時從{key} 號公寓保管箱退款',
            bankTransferFromPlayer: '從 #{static}銀行轉帳',
            bankTransferFromPlayerByOrderId: '轉出帳戶{id}號',
            cashOutFromPlayerToBank: '透過銀行轉帳至銀行帳戶',
            bankInFromPlayerToBank: '透過銀行輸入',
            bankOutFromBankToPlayer: '提款卡（從銀行提款）',
            cashInFromBankToPlayer: '應計現金（銀行提款）',
            bankSendToPlayer: '透過銀行轉帳至 #{static}',
            depositOpened: '開戶存款 {title} {hours} 小時',
            depositReturn: '投資回報{depositId}號存款',
            depositProfit: '應計利潤{depositId}號存款',
            bankChangeTariff: '將銀行稅率從 {old} 改為 {new}',
            depositCashIn: 'Пополнение вклада {title}',
            bankPayTariff: '償還銀行手續費債務',
            createBankCard: '開立銀行帳戶',
            payTo: '已傳送 {static}',
            payFrom: '從 {static}接收',
            gpayTo: '通過G傳輸 {static}',
            gpayFrom: '通過G從 {static} 接收',
            payBonusCode: '獎勵代碼 {bonuscode}',
            rouletteWin: '輪賽獎金',
            donateBuyMoney: '購買虛擬貨幣- {item}',
            donateStarterPack: '購買入門工具包- {item}',
            createFamily: '建立家庭- {tag}',
            rescuePlayer: '拯救了玩家 #{static}',
            crimeCashOut: '搶劫後發現的錢',
            payTicket: '已支付罰款',
            ticketPercent: '已支付罰款收取50%',
            utilizeDrugs: '交出毒品',
            armwrestlingReturn: '手臂摔跤投標退款',
            armwrestlingWin: '手臂摔跤投注贏得',
            armwrestlingBet: '手臂摔跤投注',
            betReturn: '返回BC No.{id}',
            betWin: '在BQ #{id}中勝出',
            createBet: '出價BQ #{id}',
            casinoBlackJackBet: '賭博場賭博。黑傑克',
            casinoBlackJackWin: '在賭場贏得獎勵。',
            casinoHorseRaceBet: '賭博場賽馬',
            casinoHorseRaceWin: '賭場獎金，賽馬',
            casinoLuckyWheelWin: '在賭場贏得獎勵-幸運輪',
            casinoPokerBet: '撲克投注',
            casinoPokerWin: '贏得撲克',
            casinoRouletteBet: '賭博場賭注',
            casinoRouletteWin: '賭場獎勵。輪賽',
            casinoSlotsBet: '賭博場投注單位',
            casinoSlotsWin: '賭場贏得老虎機',
            diceLoose: '迷失在玩家 #{static}的骰子中',
            diceWin: '在立方體中取勝，超過玩家 #{static}',
            golfWin: '贏得高爾夫',
            golfBuyTicket: '購買高爾夫俱樂部 {id}票',
            golfReturn: '高爾夫球杆 {id}票退票',
            luckyPhone: '快樂的手機',
            buyOnVending: '在自助售票機中購買- {machineName}',
            minigamesBuyTicket: '在迷你遊戲中購買門票 {type}',
            gungameWin: '在GunGame贏得獎勵',
            airportToIsland: '購買前往島嶼的門票 {teleportId}',
            autoSchoolBuyLic: '交出第 {category}類權利',
            flyingSchool: '技能飛行學校課程',
            govBuyLic: '在市政廳簽發執照- {category}',
            stripBuyDance: '購買私人舞蹈',
            itemCompensation: '薪酬 {id}',
            payTradeMoney: 'Доплачивает по обмену предметами с #{static}',
            receiveTradeMoney: '與 #{static}更換道具可獲得附加費',
            bizPayTax: '營業稅款繳付',
            takeMoneyFromBiz: '退出營業',
            bizOrderItem: '訂購業務付款',
            propPayTax: 'Оплата налога на {type, select, house{дом} other{квартиру}}',
            withdrawFamilyMoney: '從家庭平衡中取出 {org}',
            withdrawFractionMoney: '取自分數 {org}的餘額',
            replenishFamilyMoney: '重新填充家庭的餘額 {org}',
            replenishFractionMoney: '填充分數 {org}餘額',
            bikerQuest: '在任務中賺取自行車手的獎勵',
            builderSalary: '我在建築工地拿到了薪水',
            burgershotSalary: '在BurgerShot中賺取',
            busdriverSalary: '巴士司機上的DOT已完成',
            butcherSalary: '我有一份屠夫的薪水',
            sellOnFarm: '農場水果銷售',
            floorwasherSalary: '地板清洗費',
            gangQuest: '在幫派任務中賺得一份工作',
            garbageCollectorSalary: '垃圾車檢查點已完成',
            gopostalSalary: '郵寄航班已完成',
            hijackerDeliveCar: '通過一輛被偷的車',
            hijackerReturnCar: '已將車輛退還給車主',
            mafiaQuest: '在黑手黨的任務中賺取了一份工作',
            moneycollectorSalary: '轉換航班已完成',
            taxiPay: '#{static} 出租車費',
            getTaxiPayment: '乘坐計程車需收取 #{static} 起費用',
            truckerSalary: '已完成卡車運貨人飛行',
            auctionBet: '拍賣-批次 {id}出價',
            buyBiz: '購買業務 {tableName} #{id}',
            buyHouse: '購房{id}號',
            buyApartment: 'Покупка квартиры №{id}',
            sellHouse: 'Продажа дома №{key}',
            sellBiz: '賣出業務 {tableName} 號{key}',
            sellApartment: 'Продажа квартиры №{key}',
            cashInSafe: 'Положил в сейф {tableName, select, houses{дома} other{квартиры}} №{keyId}',
            cashOutSafe: 'Взял из сейфа {tableName, select, houses{дома} other{квартиры}} №{keyId}',
            payPropTradeMoney: 'Доплачивает по обмену с #{static} за {type} {id}',
            receivePropTradeMoney: 'Получает доплату по обмену с #{static} за {type} {id}',
            vehicleRentFrom: '從 {static} 租用交通工具 {title} [{id}] {rentHours} 小時。',
            vehicleRentTo: '租用交通工具 {title} [{id}] {static} {rentHours} 小時。',
            vehicleBuyFrom: '購買運輸 {title} [{id}]從 {static}',
            vehicleSendTo: '銷售運輸 {title} [{id}] {static}',
            propBuyFrom: '從 #{static}中購買 {tableName} #{id}',
            propSendTo: '促銷 {tableName} #{id} #{static}',
            repairVehicle: '修理車輛',
            rentcar: '出租交通 {model} at point {id}',
            metroTicket: '地鐵票券',
            fixcar: '從服務中恢復車輛',
            dumpVehicle: '移交 {vehModel} [{vehId}]到垃圾填埋地',
            fractionPayday: '派別的薪水',
            unemploymentBenefits: '失業福利',
            partnerPayment: '加盟者 {code}',
            promocodePayment: '促銷代碼 {code}',
            payBankTariff: '支付一個月的銀行關稅',
            bankCashback: '每月退款',
            robFrom: '從 #{static}偷來的',
            robbedBy: '#{static} украл',
            questActive: '完成任務#{questId}',
            buyHealing: '我從 #{static}那裏買了一個可待因片',
            sellHealing: '已售可待因片 #{static}',
            buyMentalLic: '購買精神健康證書',
            sellMentalLic: '已售出精神健康證書',
            buyPhysicalLic: '購買的身體健康證書',
            sellPhysicalLic: '已售出身體健康證明',
            worldQuest: '完成世界任務#{questId}的當地任務',
            placeBoard: '廣告- {boardId}',
            buyBillboard: 'Покупка билборда №{id}',
            sellBilboard: 'Продажа билборда №{id}',
            orderMaterials: '購買材料',
            billboardTakeMoney: 'Вывод прибыли из билборда №{id}',
            billboardPayTax: '廣告牌稅款',
            atmPayTax: '支付ATM稅',
            atmTakeMoney: '提取{id}期ATM機利潤',
            atmOrderMaterials: '購買支票',
            atmSell: '出售{id}期ATM機',
            atmBuy: '購買ATM機{id}號',
            mobileCashIn: '存入SIM卡 {number}',
            mobileCashInCommission: 'SIM卡充值費用 {number}',
            atmTransferToOrderId: '轉帳至{orderId}號帳戶',
            atmTransferToOrderIdCommission: '轉入{orderId}號帳戶傭金',
            atmCashOutToCard: '透過ATM轉帳至卡',
            atmCashInToCard: '透過ATM進入',
            atmCardOutToCash: '提款卡（從ATM提款）',
            atmCardInToCash: '應計現金（從ATM提款）',
            outcomingPayment: '外傳翻譯',
            taxes: '稅費'
        },
        donate_log: {
            roulette_spin: '回合捲動 {count} 次',
            roulette_freespin: '免費回合卷',
            money: '幣種- {item}',
            premium: '溢價- {item}',
            vehicle: '交通- {item}',
            account: '附加內容- {item}',
            starter: '初學者套件- {item}',
            xmas: '新年禮物- {item}',
            changeName: '名稱更改-從 {prevName} 到 {name}',
            animation: '舞蹈- {item}',
            customization: '外觀變化',
            customizationAndName: '外貌和名稱從 {prevName} 更改爲 {name}',
            createFamily: '建立家庭- {tag}',
            setFamilyHouse: 'Смена семейного дома для семьи №{id} с {oldHome} на {home}',
            familyRoyalty: '支付{id} 號家族特許權使用費，爲期 {days} 天。',
            clothes: '服裝',
            accountItems: {
                person: '額外字符',
                warn: '移除警告',
                vehicleNumber: '運輸車牌',
                phoneNumber: '電話號碼',
                promoCode: '優惠券代碼',
                garage: '車庫空間',
                weddingDivorce: '婚禮離婚',
                armyCertificate: '軍事門票'
            }
        },
        ban: {
            ac: {
                any: '麥克：作弊。代碼： {code}',
                1001: '麥克：作弊。代碼： 1001',
                1002: '麥克：作弊。代碼： 1002',
                1003: '麥克：作弊。代碼： 1003',
                1004: '麥克：作弊。代碼： 1004',
                1005: '麥克：作弊。代碼： 1005',
                1006: '麥克：作弊。代碼： 1006',
                1007: '麥克：作弊。代碼： 1007',
                1008: '麥克：作弊。代碼： 1008',
                1009: '麥克：作弊。代碼： 1009',
                1010: '麥克：作弊。代碼： 1010',
                1011: '麥克：作弊。代碼： 1011',
                1012: '麥克：作弊。代碼： 1012',
                1013: '麥克：作弊。代碼： 1013',
                1050: '麥克：作弊。代碼： 1050',
                1051: '麥克：作弊。代碼： 1051'
            },
            badPasswordReset: '密碼復原嘗試失敗',
            badPasswordLogin: '登錄時登錄名或密碼無效',
            bad2FA: '登錄時2FA無效',
            abuserDonate: '損壞（捐贈）',
            abuser1: '危害1',
            abuser2: '傷害2',
            abuser3: '傷害3',
            abuser4: '危害4',
            abuser5: '傷害5',
            abuser6: '危害6',
            abuser7: '危害7',
            abuserMoney: '損害（金錢）',
            banAC: '你已被禁用 {days} 天！原因： $t({reason})',
            banACAdmin: '{name} #{id} 被Antichit禁止 {days} 天！原因： $t({reason})',
            codeGuessing: '嘗試匹配啟用碼'
        },
        fraction_money_log: {
            barBuy: '在酒吧購物',
            giveLic: '執照的發放',
            tookBalance: '取自分數餘額',
            putBalance: '部分餘額已補充',
            orderedMaterialsFrom: '從 {fraction}訂購 {type} 的材料',
            orderedMaterialsFor: '材料訂單 {type} for {fraction}',
            orderedMaterials: '材料交付已下單 {type}',
            financing: '資金： {comment}',
            gotFinancing: '收到的資金： {comment}',
            award: '獎項： {reason}',
            bikerQuest: '騎車者任務',
            gangQuest: '幫派任務',
            hijackSuccess: '失蹤的車輛已投降',
            hijackReturn: '將車輛退還給車主',
            mafiaQuest: '黑手黨任務',
            marketSale: '在市場上銷售',
            ammoshop: '在槍支店購物',
            auto_shops: '在經銷商購買',
            barber_shops: '理發店購物',
            carwash_shops: '在洗車房購買',
            clothes_shops: '在服裝店購物',
            fuel_stations: '在加油站購物',
            fuel_stationsFuel: '加油站的燃料費',
            item_shops: '全天候在商店購買',
            mobile_shops: '在通訊沙龍購買',
            tattoo_shops: '在刺青店購物',
            tuning_shops: '在調節沙龍購物',
            pillSell: '平板電腦銷售',
            medCertMental: '發出精神健康證書',
            medCertPhysical: '開具的身體健康證明',
            robbery: '搶劫'
        },
        family_money_log: {
            update: '家庭重生',
            tookFromFamilyBalance: '取自家庭平衡',
            putToFamilyBalance: '家庭平衡已補充',
            award: '獎項： {reason}',
            taxi: '出租車司機- {vehTitle} 號{vehId} [{numberPlate}]',
            trucker: '卡車司機- {vehTitle} #{vehId} [{numberPlate}]',
            contractFinished: '已完成的合同#{contractId}',
            startContract: 'Начато выполнение контракта №{contractId}'
        },
        fraction_log: {
            leftLeader: '剝奪了Leader Post {name}',
            setLeader: '被任命為領導人職位 {name}',
            kickedByAdmin: '管理員從 {name}中解僱',
            inviteTo: '邀請到 {name}',
            uninviteFrom: '從 {name}中解散',
            orderedMaterialsFrom: '從 {name} 訂購的材料為 {type} ，數量為 {amount} 個。',
            orderedMaterials: 'Заказаны материалы {type} в размере {amount} шт.',
            financeReceive: 'Получено финансирование в размере ${amount}',
            financeSend: '發放資金額為 ${amount}',
            rankIncrease: '晉升至 {rank} 級',
            rankDecrease: '降級至 {rank} 級',
            warn: '警告：',
            warnRemoved: '警告已清除',
            awardAmount: '${amount}的獎勵',
            giveArmyLic: '簽發軍人身分證件',
            giveWeaponLic: '授予武器執照',
            takeWeaponLic: '他拿了武器的執照',
            territoryControl: '控制領土',
            territoryControlMafia: '用於控制公司'
        },
        family_log: {
            inviteTo: '邀請到 {name}',
            uninviteFrom: '從 {name}中解散',
            leftFrom: '左 {name}',
            returnVehicle: '從家庭返回 {model}',
            delivered: '交付 {usedUnits}',
            removeFamily: '已刪除家庭',
            payRoyalty: '家庭忠誠度延長 {days} 天',
            awardAmount: '${amount}的獎勵',
            giveVehicleToFamily: '已將 {title} 傳送給家族。',
            takeVehicleFromFamily: '從家裏拾起 {title} 。'
        },
        payment_log: {
            givedonate: '從管理員 #{staticId}. {reason}',
            loyalty: '{percent}% of #{staticId}',
            luckyWheel: '在賭場贏得獎勵-幸運輪',
            localWorldQuest: '世界任務當地任務仕{questId}',
            worldQuest: 'Участие в мировом квесте №{questId}',
            coinsDay: '播放 {hours} 小時 {date}',
            bonuscode: '紅利代碼： {bonuscode}',
            rouletteCoins: '輪賽獎金',
            sellRoulette: '以輪盤方式賣出贏金',
            levelUp: '達到 {level} 級'
        }
    },
    sms: {
        houseTaxWarn: '注意！您需要繳納稅費，以避免失去房屋。<br>次沒收只剩下 {days} 天。',
        apartmentTaxWarn: '注意！您需要繳納稅款，以免失去公寓。<br>在沒收前，您有 {days} 天的時間。',
        bizTaxWarn: 'Внимание! Вам необходимо оплатить налоги, чтобы не лишиться бизнесы.<br>До конфискации осталось {days} дн.',
        familyTaxWarn: '注意！您需要繳納稅款以避免失去家人。<br>次沒收只剩 {days} 天了。',
        auctionSellError: '批次#{id} 已完成。發生錯誤。該物業不再由您擁有。',
        auctionNoBets: '批次#{id} 已完成。沒有下注。',
        auctionBuyError: '批次#{id} 已完成。買方無法獲得批次內容。',
        auctionBuyError2: '批次#{id} 已完成。你的出價已中獎，但你沒有可用的空間來接收批次內容。',
        auctionBuySuccessForSeller: '批次#{id} 已完成。買方： {winnerName}.交易金額： ${price}.傭金是： ${commission}。',
        auctionBuySuccessForWinner: '批次#{id} 已完成。您已購買該物業。交易金額為： ${price}。感謝您的參與！',
        auctionLotError: '批次#{id} 已完成。發生批次錯誤。返回 ${amount}',
        auctionBetLose: '批次#{id} 已完成。您的投注超出了出價。已退款 ${amount}'
    },
    mailer: {
        bankReg: {
            subject: 'Majestic Role Play的銀行帳戶',
            hello: '您好， {login}！',
            success: '恭喜你在伺服器「Majestic Role Play」上的遊戲銀行Fleeca開立帳戶。將PIN碼傳送至你的信用卡。',
            pin: '引腳： {code}',
            gotReason: '您收到此訊息是因為您在Majestic Role Play伺服器上開立了銀行帳戶。',
            ignoreIt: '如果您沒有在伺服器上註冊帳戶，只需忽略電子郵件即可。'
        },
        confirmPin: {
            subject: 'Majestic Role Play驗證碼',
            hello: '您好，您好！',
            activate: '若要通過驗證並驗證你的帳戶權限，你需要在遊戲對話框中輸入驗證碼。',
            code: 'Code: {code}',
            gotReason: '您收到此訊息是因為有人在Majestic Role Play伺服器上採取了某些行動。',
            ignoreIt: '如果您沒有在伺服器上註冊帳戶，只需忽略電子郵件即可。'
        },
        resetPassword: {
            subject: '密碼恢復',
            success: '您已成功更改密碼。',
            newPass: '新密碼： <i>隱藏</i>',
            gotReason: '您收到此訊息是因為您或某人更改了Majestic Role Play伺服器上的密碼。',
            resetNow: '如果你不是你，請重設密碼！'
        },
        restorePin: {
            subject: '在偉大的角色扮演中從銀行卡中恢復PIN碼',
            hello: '您好， {login}！',
            success: '你已要求從Majestic Role Play伺服器上的銀行卡中恢復你的個人識別碼。輸入伺服器上的驗證碼。',
            pin: '驗證碼： {code}',
            gotReason: '你收到此訊息是因為你已要求從Majestic Role Play伺服器上的銀行卡中恢復PIN碼。',
            ignoreIt: '如果您沒有在伺服器上註冊帳戶，只需忽略電子郵件即可。'
        },
        newPinCode: {
            subject: '銀行卡上的新PIN碼',
            hello: '您好， {login}！',
            success: '你已重設銀行卡上的PIN碼。',
            pin: '您的新PIN是： {code}',
            gotReason: '你收到此訊息是因為你已要求從Majestic Role Play伺服器上的銀行卡中恢復PIN碼。',
            ignoreIt: '如果您沒有在伺服器上註冊帳戶，只需忽略電子郵件即可。'
        }
    }
};