export default [
    {
        id: 0,

        position: { x: 1718.1099, y: 4851.0068, z: 42.0015 },
        radius: 200,
        shapeRadius: 30,
        dimension: 0,

        defaultActive: true,

        syncedData: {
            ipls: [],
            entitySets: [
                {
                    name: 'ch2_10_rehabmain_yt',
                    ymap: 'ch2_10_rehabmain_milo_',
                    entitySet: 'halloween'
                }
            ]
        },
    },
    {
        id: 1,

        position: { x: -1792.3385, y: -766.8792, z: 14.1487 },
        radius: 150,
        dimension: -1,

        defaultActive: true,

        activateTime: '08.11.2024 06:00:00:00',
        deactivateTime: '08.11.2024 20:00:00:00',

        peds: [
            {
                model: 's_f_y_cop_01',
                position: { x: -1817.8945, y: -728.0967, z: 9.4813 },
                heading: 0.0000,
                flag: 1,

                anim: ['amb@world_human_car_park_attendant@male@base', 'base']
            },
            {
                model: 'csb_cop',
                position: { x: -1749.1252, y: -789.3231, z: 9.4139 },
                heading: 133.2283,
                flag: 1,

                anim: ['amb@world_human_leaning@female@wall@back@holding_elbow@idle_a', 'idle_a']
            },
        ],

        // Звуки которые всегда запускаються при входе в зону
        sounds: [
            {
                soundName: 'university/university_washing_machine',
                volume: 40,
                loop: true,
                position: { x: -1640.5319, y: 182.4923, z: 68.6747 },
                maxDistance: 1,
            },
            {
                soundName: 'university/university_kitchen',
                volume: 40,
                loop: true,
                position: { x: -1654.2990, y: 189.3099, z: 67.7649 },
                maxDistance: 5,
            },
            {
                soundName: 'university/university_chemical',
                volume: 40,
                loop: true,
                position: { x: -1642.6022, y: 159.4418, z: 73.0557 },
                maxDistance: 1,
            },
            {
                soundName: 'university/university_chemical_fridge',
                volume: 70,
                loop: true,
                position: { x: -1651.4901, y: 157.1077, z: 72.5165 },
                maxDistance: 1,
            },
            {
                soundName: 'university/university_toilet',
                volume: 40,
                loop: true,
                position: { x: -1650.0659, y: 174.2110, z: 72.2131 },
                maxDistance: 1,
            },
            {
                soundName: 'university/university_toilet',
                volume: 40,
                loop: true,
                position: { x: -1658.8484, y: 170.2022, z: 72.9041 },
                maxDistance: 1,
            },
            {
                soundName: 'university/university_hall_ambience',
                volume: 60,
                loop: true,
                position: { x: -1650.7384, y: 174.6857, z: 66.2314 },
                maxDistance: 50,
            },
        ],
    },
    {
        id: 2,

        position: { x: -1649.6967, y: 167.5253, z: 61.7495 },
        radius: 150,
        dimension: 0,

        defaultActive: true,

        activateTime: '08.11.2023 20:00:00:00',
        deactivateTime: '08.12.2044 06:00:00:00',

        peds: [
            {
                model: 'S_M_M_Janitor', // Уборщик
                position: { x: -1649.6967, y: 167.5253, z: 61.7495 },
                heading: -79,
                flag: 1,
                scenario: 'WORLD_HUMAN_JANITOR',
            },
            {
                model: 'IG_Car3guy2', // Теннисист охраняющий кубки
                position: { x: -1659.7054, y: 178.3648, z: 61.7495 },
                heading: -113.3858,
                flag: 1,
                scenario: 'WORLD_HUMAN_TENNIS_PLAYER',
            },
            {
                model: 'a_m_y_vinewood_02', // Стоит у стены
                position: { x: -1654.1934, y: 181.4505, z: 61.7495 },
                heading: -161.5748,
                flag: 1,
                anim: ['amb@world_human_leaning@male@wall@back@foot_up@idle_b', 'idle_d']
            },
            {
                model: 'IG_Entourage_B', // Прессует девушку у стены
                position: { x: -1654.4572, y: 180.1451, z: 61.7495 },
                heading: 0,
                flag: 1,
                anim: ['oddjobs@assassinate@vice@hooker', 'argue_a']
            },
            {
                model: 'IG_Entourage_A', // Прессует девушку у стены 2
                position: { x: -1653.3890, y: 180.8440, z: 61.7495 },
                heading: 51,
                flag: 1,
                anim: ['oddjobs@assassinate@hotel@', 'argue_b']
            },
            {
                model: 'IG_Kaylee', // Изучает расписание
                position: { x: -1644.3824, y: 169.8725, z: 61.7495 },
                heading: -141,
                flag: 1,
                anim: ['missfbi3_party_d', 'stand_talk_loop_b_female']
            },
            {
                model: 'IG_Furry', // Фури
                position: { x: -1636.3912, y: 177.2440, z: 61.7495 },
                heading: 107.7,
                flag: 1,
                anim: ['rcmnigel1a_band_groupies', 'idle_b_f2']
            },
            {
                model: 'IG_Imani', // Сидит на сцене
                position: { x: -1656.4747, y: 167.6044, z: 62.4566 },
                heading: -2.8346,
                flag: 1,
                anim: ['timetable@jimmy@mics3_ig_15@', 'idle_a_jimmy']
            },
            {
                model: 'U_M_Y_Gabriel', // Напротив сцены
                position: { x: -1655.8550, y: 169.0022, z: 61.7495 },
                heading: 144.5669,
                flag: 1,
                anim: ['amb@world_human_hang_out_street@female_arm_side@idle_a', 'idle_c']
            },
            {
                model: 'G_F_ImportExport_01', // Танцует для видео
                position: { x: -1655.5121, y: 174.9758, z: 61.7495 },
                heading: -119.0551,
                flag: 1,
                anim: ['majestic_animations_4', 'wanna_see_me']
            },
            {
                model: 'A_F_Y_Indian_01', // Снимает танец
                position: { x: -1652.0703, y: 173.7099, z: 61.7495 },
                heading: 68.1276,
                flag: 1,
                scenario: 'WORLD_HUMAN_TOURIST_MOBILE',
            },
            {
                model: 'U_F_O_Carol', // Бьет по автомату с едой
                position: { x: -1640.1890, y: 167.6835, z: 61.7495 },
                heading: -147.4016,
                flag: 1,
                anim: ['amb@world_human_guard_patrol@male@idle_b', 'idle_e']
            },
            {
                model: 'MP_F_Weed_01', // Протирает кубки
                position: { x: -1652.7296, y: 164.4659, z: 61.7495 },
                heading: -155.9055,
                flag: 1,
                scenario: 'WORLD_HUMAN_MAID_CLEAN',
            },
            {
                model: 'a_m_m_hillbilly_01', // Сдувает листву
                position: { x: -1626.1450, y: 225.0330, z: 60.4520 },
                heading: 53.8583,
                flag: 1,
                scenario: 'WORLD_HUMAN_GARDENER_LEAF_BLOWER',
            },
            {
                model: 'A_F_Y_Runner_01', // Занимается йогой
                position: { x: -1648.1802, y: 205.5165, z: 60.9675 },
                heading: 107.7165,
                flag: 1,
                scenario: 'WORLD_HUMAN_YOGA',
            },
            {
                model: 'A_F_Y_Vinewood_02', // Наблюдает за студентками
                position: { x: -1652.2021, y: 202.0615, z: 61.3925 },
                heading: -53.3858,
                flag: 1,
                anim: ['amb@lo_res_idles@', 'world_human_picnic_female_lo_res_base']
            },
            {
                model: 'IG_Wade', // На лавке
                position: { x: -1637.7098, y: 214.5495, z: 60.6373 },
                heading: 113,
                flag: 1,
                anim: ['timetable@reunited@ig_10', 'base_amanda']
            },
        ],

        // Звуки которые всегда запускаются при входе в зону
        sounds: [
            {
                soundName: 'university/university_washing_machine',
                volume: 40,
                loop: true,
                position: { x: -1640.5319, y: 182.4923, z: 68.6747 },
                maxDistance: 1,
            },
            {
                soundName: 'university/university_kitchen',
                volume: 40,
                loop: true,
                position: { x: -1654.2990, y: 189.3099, z: 67.7649 },
                maxDistance: 5,
            },
            {
                soundName: 'university/university_chemical',
                volume: 40,
                loop: true,
                position: { x: -1642.6022, y: 159.4418, z: 73.0557 },
                maxDistance: 1,
            },
            {
                soundName: 'university/university_chemical_fridge',
                volume: 70,
                loop: true,
                position: { x: -1651.4901, y: 157.1077, z: 72.5165 },
                maxDistance: 1,
            },
            {
                soundName: 'university/university_toilet',
                volume: 40,
                loop: true,
                position: { x: -1650.0659, y: 174.2110, z: 72.2131 },
                maxDistance: 1,
            },
            {
                soundName: 'university/university_toilet',
                volume: 40,
                loop: true,
                position: { x: -1658.8484, y: 170.2022, z: 72.9041 },
                maxDistance: 1,
            },
            {
                soundName: 'university/university_hall_ambience',
                volume: 60,
                loop: true,
                position: { x: -1650.7384, y: 174.6857, z: 66.2314 },
                maxDistance: 50,
            },
        ],
    },

]
