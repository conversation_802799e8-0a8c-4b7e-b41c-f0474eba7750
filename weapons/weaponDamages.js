module.exports = {
    default: {
        // Melee
        weapon_unarmed: {
            damage: 10.00,
            distance: 5,
            shotCount: 1,
            is<PERSON>elee: true,
        },
        weapon_dagger: {
            damage: 33.00,
            distance: 5,
            shotCount: 1,
            is<PERSON>elee: true,
        },
        weapon_bat: {
            damage: 33.00,
            distance: 5,
            shotCount: 1,
            is<PERSON>elee: true,
            fromLvl: 2,
        },
        weapon_bottle: {
            damage: 33.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
        },
        weapon_crowbar: {
            damage: 33.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
        },
        weapon_flashlight: {
            damage: 33.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
            fromLvl: 2,
        },
        weapon_golfclub: {
            damage: 33.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
        },
        weapon_hammer: {
            damage: 33.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
        },
        weapon_hatchet: {
            damage: 33.00,
            distance: 5,
            shotCount: 1,
            is<PERSON>elee: true,
        },
        weapon_knuckle: {
            damage: 27.00,
            distance: 5,
            shotCount: 1,
            is<PERSON>elee: true,
        },
        weapon_knife: {
            damage: 20.00,
            distance: 5,
            shotCount: 1,
            is<PERSON>elee: true,
            fromLvl: 2,
        },
        weapon_machete: {
            damage: 20.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
        },
        weapon_switchblade: {
            damage: 20.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
        },
        weapon_nightstick: {
            damage: 20.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
        },
        weapon_wrench: {
            damage: 20.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
        },
        weapon_battleaxe: {
            damage: 20.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
            fromLvl: 2,
        },
        weapon_poolcue: {
            damage: 20.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
        },
        weapon_candycane: {
            damage: 20.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
        },
        weapon_stone_hatchet: {
            damage: 70.00,
            distance: 5,
            shotCount: 1,
            isMelee: true,
        },

        // Handguns
        weapon_pistol: {
            damage: 17.00,
            distance: 129,
            shotCount: 1,
            time: 0.3700000047683716,
        },
        weapon_pistol_mk2: {
            damage: 16.00,
            distance: 129,
            shotCount: 1,
            time: 0.3700000047683716,
        },
        weapon_combatpistol: {
            damage: 18.00,
            distance: 129,
            shotCount: 1,
            time: 0.3700000047683716,
        },
        weapon_appistol: {
            damage: 9.00,
            distance: 129,
            shotCount: 1,
            time: 0.10000000149011612,
        },
        weapon_stungun_mp: {
            damage: 0.00,
            distance: 129,
            shotCount: 1,
            time: 12,
        },
        weapon_pistol50: {
            damage: 25.00,
            distance: 129,
            shotCount: 1,
            time: 0.5199999809265137,
        },
        weapon_snspistol: {
            damage: 10.00,
            distance: 129,
            shotCount: 1,
            time: 0.3700000047683716,
        },
        weapon_snspistol_mk2: {
            damage: 11.00,
            distance: 129,
            shotCount: 1,
            time: 0.3700000047683716,
        },
        weapon_heavypistol: {
            damage: 20.00,
            distance: 129,
            shotCount: 1,
            time: 0.41999998688697815,
        },
        weapon_vintagepistol: {
            damage: 17.00,
            distance: 129,
            shotCount: 1,
            time: 0.4000000059604645,
        },
        weapon_revolver: {
            damage: 50.00,
            distance: 129,
            shotCount: 1,
            time: 1.8329999446868896,
        },
        weapon_revolver_mk2: {
            damage: 60.00,
            distance: 129,
            shotCount: 1,
            time: 1.3339999914169312,
        },
        weapon_doubleaction: {
            damage: 29.00,
            distance: 129,
            shotCount: 1,
            time: 0.4666669964790344,
        },
        weapon_marksmanpistol: {
            damage: 77.00,
            distance: 129,
            shotCount: 1,
            time: 2.5,
        },
        weapon_ceramicpistol: {
            damage: 15.00,
            distance: 129,
            shotCount: 1,
            time: 0.3700000047683716,
        },
        weapon_navyrevolver: {
            damage: 56.00,
            distance: 129,
            shotCount: 1,
            time: 1.3339999914169312,
        },
        weapon_gadgetpistol: {
            damage: 65.00,
            distance: 129,
            shotCount: 1,
            time: 1.25,
        },
        weapon_glockp80: {
            damage: 15.00,
            distance: 129,
            shotCount: 1,
            time: 0.3700000047683716,
        },

        // Shotguns
        weapon_pumpshotgun: {
            damage: 56.00,
            distance: 35,
            shotCount: 8,
            time: 0.800000011920929,
        },
        weapon_pumpshotgun_mk2: {
            damage: 72.00,
            distance: 40,
            shotCount: 8,
            time: 0.800000011920929,
        },
        weapon_sawnoffshotgun: {
            damage: 88.00,
            distance: 30,
            shotCount: 8,
            time: 1,
        },
        weapon_assaultshotgun: {
            damage: 36.00,
            distance: 30,
            shotCount: 6,
            time: 0.30000001192092896,
        },
        weapon_bullpupshotgun: {
            damage: 33.00,
            distance: 30,
            shotCount: 8,
            time: 1,
        },
        weapon_dbshotgun: {
            damage: 56.00,
            distance: 20,
            shotCount: 8,
            time: 0.10000000149011612,
        },
        weapon_heavyshotgun: {
            damage: 40.00,
            distance: 70,
            shotCount: 1,
            time: 0.30000001192092896,
        },
        weapon_autoshotgun: {
            damage: 25.00,
            distance: 30,
            shotCount: 6,
            time: 0.367000013589859,
        },
        weapon_musket: {
            damage: 100.00,
            distance: 100,
            shotCount: 1,
            time: 1.5670000314712524,
        },
        weapon_combatshotgun: {
            damage: 40.00,
            distance: 35,
            shotCount: 6,
            time: 0.25,
        },

        // Submachine Guns
        weapon_microsmg: {
            damage: 10.00,
            distance: 129,
            shotCount: 1,
            time: 0.10000000149011612,
        },
        weapon_smg: {
            damage: 10.00,
            distance: 129,
            shotCount: 1,
            time: 0.11800000071525574,
        },
        weapon_smg_mk2: {
            damage: 10.00,
            distance: 129,
            shotCount: 1,
            time: 0.11800000071525574,
        },
        weapon_combatpdw: {
            damage: 11.00,
            distance: 129,
            shotCount: 1,
            time: 0.13500000536441803,
        },
        weapon_machinepistol: {
            damage: 10.00,
            distance: 129,
            shotCount: 1,
            time: 0.11800000071525574,
        },
        weapon_minismg: {
            damage: 5.00,
            distance: 129,
            shotCount: 1,
            time: 0.09200000017881393,
        },
        weapon_assaultsmg: {
            damage: 6.00,
            distance: 129,
            shotCount: 1,
            time: 0.12999999523162842,
        },
        weapon_tecpistol: {
            damage: 10.00,
            distance: 129,
            shotCount: 1,
            time: 0.11500000149011612,
        },

        // Assault Rifles
        weapon_assaultrifle: {
            damage: 14.00,
            distance: 129,
            shotCount: 1,
            time: 0.15800000727176666,
        },
        weapon_assaultrifle_mk2: {
            damage: 16.00,
            distance: 129,
            shotCount: 1,
            time: 0.15800000727176666,
        },
        weapon_carbinerifle: {
            damage: 12.00,
            distance: 129,
            shotCount: 1,
            time: 0.13500000536441803,
        },
        weapon_carbinerifle_mk2: {
            damage: 14.00,
            distance: 129,
            shotCount: 1,
            time: 0.13500000536441803,
        },
        weapon_advancedrifle: {
            damage: 12.00,
            distance: 129,
            shotCount: 1,
            time: 0.11999999731779099,
        },
        weapon_specialcarbine: {
            damage: 12.00,
            distance: 129,
            shotCount: 1,
            time: 0.13500000536441803,
        },
        weapon_specialcarbine_mk2: {
            damage: 12.50,
            distance: 129,
            shotCount: 1,
            time: 0.13500000536441803,
        },
        weapon_bullpuprifle: {
            damage: 11.00,
            distance: 129,
            shotCount: 1,
            time: 0.11999999731779099,
        },
        weapon_bullpuprifle_mk2: {
            damage: 12.00,
            distance: 129,
            shotCount: 1,
            time: 0.11999999731779099,
        },
        weapon_compactrifle: {
            damage: 12.00,
            distance: 129,
            shotCount: 1,
            time: 0.15800000727176666,
        },
        weapon_militaryrifle: {
            damage: 12.00,
            distance: 129,
            shotCount: 1,
            time: 0.12999999523162842,
        },
        weapon_heavyrifle: {
            damage: 15.00,
            distance: 129,
            shotCount: 1,
            time: 0.158000,
        },
        weapon_tacticalrifle: {
            damage: 13.00,
            distance: 129,
            shotCount: 1,
            time: 0.135,
        },

        // Light Machine Guns
        weapon_mg: {
            damage: 17.00,
            distance: 129,
            shotCount: 1,
            time: 0.13699999451637268,
        },
        weapon_combatmg: {
            damage: 15.00,
            distance: 129,
            shotCount: 1,
            time: 0.1080000028014183,
        },
        weapon_combatmg_mk2: {
            damage: 17.00,
            distance: 129,
            shotCount: 1,
            time: 0.1080000028014183,
        },
        weapon_gusenberg: {
            damage: 12.00,
            distance: 129,
            shotCount: 1,
            time: 0.1080000028014183,
        },

        // Sniper Rifles
        weapon_sniperrifle: {
            damage: 55.00,
            distance: 300,
            shotCount: 1,
            time: 1.5670000314712524,
        },
        weapon_heavysniper: {
            damage: 100.00,
            distance: 300,
            shotCount: 1,
            time: 1.2000000476837158,
        },
        weapon_heavysniper_mk2: {
            damage: 148.00,
            distance: 300,
            shotCount: 1,
            time: 1.2000000476837158,
        },
        weapon_marksmanrifle: {
            damage: 35.00,
            distance: 300,
            shotCount: 1,
            time: 0.30000001192092896,
        },
        weapon_marksmanrifle_mk2: {
            damage: 35.00,
            distance: 150,
            shotCount: 1,
            time: 0.30000001192092896,
        },
        weapon_precisionrifle: {
            damage: 100.00,
            distance: 129,
            shotCount: 1,
            time: 1.567,
        },

        // Heavy Weapons
        weapon_rpg: {
            damage: 0.00,
            distance: 129,
            shotCount: 1,
            explosion: true,
        },
        weapon_grenadelauncher: {
            damage: 0.00,
            distance: 129,
            shotCount: 1,
            explosion: true,
        },
        weapon_grenadelauncher_smoke: {
            damage: 0.00,
            distance: 129,
            shotCount: 1,
            explosion: true,
        },
        weapon_minigun: {
            damage: 3.00,
            distance: 129,
            shotCount: 1,
            time: 0.019999999552965164,
        },
        weapon_firework: {
            damage: 0.00,
            distance: 129,
            shotCount: 1,
            explosion: true,
        },
        weapon_railgun: {
            damage: 0.00,
            distance: 129,
            shotCount: 1,
            explosion: true,
        },
        weapon_hominglauncher: {
            damage: 0.00,
            distance: 129,
            shotCount: 1,
            explosion: true,
        },
        weapon_compactlauncher: {
            damage: 0.00,
            distance: 129,
            shotCount: 1,
            explosion: true,
        },
        weapon_rayminigun: {
            damage: 5.00,
            distance: 129,
            shotCount: 1,
            time: 0.019999999552965164,
        },
        weapon_emplauncher: {
            damage: 0.00,
            distance: 129,
            shotCount: 1,
            explosion: true,
            time: 1.220000,
        },
        weapon_railgunxm3: {
            damage: 20,
            distance: 129,
            shotCount: 1,
            explosion: true,
            time: 2,
        },
        weapon_fireextinguisher: {
            damage: 0.00,
            distance: 129,
            shotCount: 1,
        },

        // Throwable
        weapon_grenade: {
            damage: 0.00,
            distance: 50,
            shotCount: 1,
            explosion: true,
        },
        weapon_bzgas: {
            damage: 0.00,
            distance: 50,
            shotCount: 1,
            explosion: true,
        },
        weapon_molotov: {
            damage: 0.00,
            distance: 50,
            shotCount: 1,
            explosion: true,
        },
        weapon_stickybomb: {
            damage: 0.00,
            distance: 50,
            shotCount: 1,
            explosion: true,
        },
        weapon_proxmine: {
            damage: 0.00,
            distance: 50,
            shotCount: 1,
            explosion: true,
        },
        weapon_pipebomb: {
            damage: 0.00,
            distance: 50,
            shotCount: 1,
            explosion: true,
        },
        weapon_smokegrenade: {
            damage: 0.00,
            distance: 50,
            shotCount: 1,
            explosion: true,
        },
        weapon_flare: {
            damage: 0.00,
            distance: 50,
            shotCount: 1,
            explosion: true,
        },
        weapon_snowball: {
            damage: 0.00,
            distance: 25,
            shotCount: 1,
        },
        weapon_acidpackage: {
            damage: 1.00,
            distance: 25,
            shotCount: 1,
        },

        // animals
        weapon_animal: {
            damage: 10.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_animal_retriever: {
            damage: 10.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_small_dog: {
            damage: 5.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_cougar: {
            damage: 25.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_tiger_shark: {
            damage: 50.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_hammer_shark: {
            damage: 50.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_killer_whale: {
            damage: 50.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_boar: {
            damage: 20.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_pig: {
            damage: 10.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_coyote: {
            damage: 25.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_deer: {
            damage: 15.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_hen: {
            damage: 10.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_rabbit: {
            damage: 10.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_cat: {
            damage: 10.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_cow: {
            damage: 10.00,
            distance: 5,
            shotCount: 1,
            time: 0.01,
            isMelee: true,
        },
        weapon_statuehalloween: {
            damage: 1.00,
            distance: 25,
            shotCount: 1
        },

        // Vehicle weapons
        vehicle_weapon_turret_technical: {
            damage: 200.00,
            distance: 400,
            shotCount: 1,
            time: 0.18,
        },
        vehicle_weapon_annihilator2_mini: {
            damage: 60.00,
            distance: 400,
            shotCount: 2,
            time: 0.03,
        },
        vehicle_weapon_turret_valkyrie: {
            damage: 60.00,
            distance: 400,
            shotCount: 1,
            time: 0.05,
        },
        vehicle_weapon_nose_turret_valkyrie: {
            damage: 60.00,
            distance: 400,
            shotCount: 1,
            time: 0.25,
        },
        vehicle_weapon_akula_turret_single: {
            damage: 60.00,
            distance: 400,
            shotCount: 1,
            time: 0.05,
        },
        vehicle_weapon_akula_turret_dual: {
            damage: 60.00,
            distance: 400,
            shotCount: 1,
            time: 0.07,
        },
        vehicle_weapon_akula_minigun: {
            damage: 60.00,
            distance: 400,
            shotCount: 1,
            time: 0.07,
        },
        vehicle_weapon_akula_barrage: {
            damage: 0.00,
            distance: 400,
            shotCount: 1,
            time: 0.15,
            explosion: true
        },
    },
    eu: {
        // weapon_heavyshotgun: {
        //     damage: 30.00,
        //     distance: 70,
        //     shotCount: 1,
        //     time: 0.30000001192092896,
        // },
        // weapon_revolver: {
        //     damage: 34.00,
        //     distance: 129,
        //     shotCount: 1,
        //     time: 1.8329999446868896,
        // },
        // weapon_revolver_mk2: {
        //     damage: 39.00,
        //     distance: 129,
        //     shotCount: 1,
        //     time: 1.3339999914169312,
        // },
    }
}
