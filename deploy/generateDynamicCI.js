const { writeFileSync } = require('fs')

const isScheduled = () => process.env['CI_PIPELINE_SOURCE'] === 'schedule'

const createSettingsBlock = () => `image: node:20-alpine3.20

workflow:
  rules:
    - when: always
`

const createCacheBlock = () => `cache:
  - key:
      files: [yarn.lock]
    paths: [node_modules]
    policy: pull
  - key:
      files: [libs/database/src/prisma/schemas/game.prisma, libs/database/src/prisma/schemas/backend.prisma]
    paths: [libs/prisma-client-backend/src/generated, libs/prisma-client-game/src/generated]
    policy: pull
`

const createBeforeScriptsBlock = () => `.before-scripts:
  setup-nx: &setup-nx
    - '[ -z $NX_BASE ] && export NX_BASE=$CI_COMMIT_BEFORE_SHA'
    - apk add git
    - apk add --no-cache build-base cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev
`

const createStagesBlock = () => `stages:
  - pre
  - test
  - publish
  - deploy
`

const createEmptyJob = () => `empty:
  stage: pre
  script:
    - echo 'no apps affected!'
`

const createTestUnitJobBlock = services => `unit:
  stage: test
  before_script: *setup-nx
  script:
    - yarn nx run-many -t test -p ${services.join(' ')}
`

const createTestLintJobBlock = services => `lint:
  stage: test
  before_script: *setup-nx
  script:
    - yarn nx run-many -t lint -p ${services.join(' ')}
`

const createBuildJobBlock = services => `build:
  stage: test
  before_script: *setup-nx
  artifacts:
    expire_in: 1 day
    paths:
      - dist/
  script:
    - yarn nx run-many -t build -p ${services.join(' ')}
`

const createPublishAppJob = service => {
  const contextDir = `dist/apps/${service}`
  const dockerfilePath = `apps/${service}/Dockerfile`
  const buildImageName = `docker.io/majesticgames/majestic-backend-${service}`
  return `publish:${service}:
  when: ${process.env['CI_COMMIT_TAG'] ? 'always' : 'manual'}
  needs: [build]
  stage: publish
  cache: []
  image:
    name: gcr.io/kaniko-project/executor:v1.14.0-debug
    entrypoint: ['']
  script:
    - echo "{\\"auths\\":{\\"https://index.docker.io/v1/\\":{\\"auth\\":\\"$\{DOCKER_HUB_AUTH\}\\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor
      --context ${contextDir}
      --dockerfile ${dockerfilePath}
      --destination "${buildImageName}:$\{BUILD_IMAGE_TAG\}"
`
}

const createDeployAppJob = (services, isProd = false) => {
  const needs = !isScheduled() ? services.map(service => `publish:${service}`) : []
  let webhooks = null
  try {
    webhooks = JSON.parse(
      process.env[isProd ? 'PORTAINER_WEBHOOKS_PROD' : 'PORTAINER_WEBHOOKS_STAGING'],
    )
  } catch {
    throw new Error(`Cannot parse webhook env for ${isProd ? 'production' : 'staging'}`)
  }
  const scripts = services.map(
    service => `curl "${webhooks[service]}?tag=$BUILD_IMAGE_TAG" -X POST --fail`,
  )
  return `deploy:${isProd ? 'prod' : 'staging'}:
    needs: ${JSON.stringify(needs)}
    stage: deploy
    when: ${isScheduled() && isProd ? 'always' : 'manual'}
    image: curlimages/curl
    environment:
      name: ${isProd ? 'production' : 'staging'}
      url: ${
        isProd ? 'https://api1master.majestic-files.com' : 'https://mj-backend-staging.octo.gg'
      }
    cache: []
    script: ${JSON.stringify(scripts)}
`
}

const createCIFile = projects => {
  const blocks = [
    createSettingsBlock(),
    createCacheBlock(),
    createBeforeScriptsBlock(),
    createStagesBlock(),
  ]
  if (!projects.length) {
    blocks.push(createEmptyJob())
    return blocks.join('\n')
  }

  if (!isScheduled()) {
    blocks.push(
      createTestUnitJobBlock(projects),
      createTestLintJobBlock(projects),
      createBuildJobBlock(projects),
    )
    projects.forEach(project => blocks.push(createPublishAppJob(project)))
  }

  blocks.push(createDeployAppJob(projects, false))

  if (process.env['CI_COMMIT_TAG']) {
    blocks.push(createDeployAppJob(projects, true))
  }

  return blocks.join('\n')
}

const createDynamicGitLabFile = () => {
  const [stringifiedAffected] = process.argv.slice(2)
  const content = createCIFile(stringifiedAffected.split('\n').filter(el => el))
  writeFileSync('dynamic-gitlab-ci.yml', content)
}

createDynamicGitLabFile()
