version: '3.8'

x-vars:
  service_props: &service_props
    image: majestic-node
    working_dir: /app
    env_file: ../.env
    environment:
      ADMIN_HOST: ${ADMIN_HOST}
      ADMIN_PORT: ${ADMIN_PORT}
      ADMIN_METRICS_PORT: ${ADMIN_METRICS_PORT}
      COMMON_HOST: ${COMMON_HOST}
      COMMON_PORT: ${COMMON_PORT}
      COMMON_METRICS_PORT: ${COMMON_METRICS_PORT}
      GATEWAY_HOST: ${GATEWAY_HOST}
      GATEWAY_PORT: ${GATEWAY_PORT}
      GATEWAY_METRICS_PORT: ${GATEWAY_METRICS_PORT}
      JWT_SECRET: ${JWT_SECRET}
      OUTER_REDIS_HOST: ${OUTER_REDIS_HOST}
      OUTER_REDIS_PASSWORD: ${OUTER_REDIS_PASSWORD}
      OUTER_REDIS_PORT: ${OUTER_REDIS_PORT}
      REPORTS_HOST: ${REPORTS_HOST}
      REPORTS_PORT: ${REPORTS_PORT}
      REPORTS_METRICS_PORT: ${REPORTS_METRICS_PORT}
      SOCKET_HOST: ${SOCKET_HOST}
      SOCKET_PORT: ${SOCKET_PORT}
      SOCKET_METRICS_PORT: ${SOCKET_METRICS_PORT}
      PAYMENTS_HOST: ${PAYMENTS_HOST}
      PAYMENTS_PORT: ${PAYMENTS_PORT}
      PAYMENTS_METRICS_PORT: ${PAYMENTS_METRICS_PORT}
      UPLOADS_HOST: ${UPLOADS_HOST}
      UPLOADS_PORT: ${UPLOADS_PORT}
      UPLOADS_METRICS_PORT: ${UPLOADS_METRICS_PORT}
    volumes:
      - ../:/app
      - ../runtime/app-data:/app/data
      - ../runtime/node_modules:/app/node_modules

services:
  api-gateway:
    <<: *service_props
    command: /bin/ash -c "yarn && yarn prisma:generate && yarn serve api-gateway"
    ports:
      - 8080:8080
    deploy:
      replicas: 1

  socket-gateway:
    <<: *service_props
    command: /bin/ash -c "yarn serve socket-gateway"
    ports:
      - 8081:8081
    deploy:
      replicas: 1

  admin:
    <<: *service_props
    command: /bin/ash -c "yarn serve admin"
    deploy:
      replicas: 1

  reports:
    <<: *service_props
    command: /bin/ash -c "yarn serve reports"
    environment:
      REPLICA: '{{.Task.Slot}}'
    deploy:
      replicas: 1

  payments:
    <<: *service_props
    command: /bin/ash -c "yarn serve payments"
    deploy:
      replicas: 1

  uploads:
    <<: *service_props
    command: /bin/ash -c "yarn serve uploads"
    deploy:
      replicas: 1

  common:
    <<: *service_props
    command: /bin/ash -c "yarn serve common"
    deploy:
      replicas: 1

  prometheus:
    image: prom/prometheus
    command: >
      --log.level=error
      --storage.tsdb.path=/prometheus
      --storage.tsdb.retention.time=30d
      --config.file=/etc/prometheus/prometheus.yml
    volumes:
      - ./configs/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - 3000:3000

  database:
    image: mariadb:lts
    ports:
      - 3306:3306
    environment:
      MARIADB_ALLOW_EMPTY_ROOT_PASSWORD: 1
      MARIADB_AUTO_UPGRADE: 1
    volumes:
      - ../runtime/database:/var/lib/mysql

  redis:
    image: redis:alpine
    ports:
      - 6379:6379
