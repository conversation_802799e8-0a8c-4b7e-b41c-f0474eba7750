global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: "services"
    dns_sd_configs:
      - type: "A"
        port: 8081
        names:
          - "tasks.api-gateway"
      - type: "A"
        port: 8081
        names:
          - "tasks.socket-gateway"
      - type: "A"
        port: 8081
        names:
          - "tasks.admin"
      - type: "A"
        port: 8081
        names:
          - "tasks.reports"
      - type: "A"
        port: 8081
        names:
          - "tasks.uploads"
      - type: "A"
        port: 8081
        names:
          - "tasks.common"
