version: '3.8'

x-vars:
  secrets_common: &secrets_common
    - backend-config.json
  networks_common: &networks_common
    - default
    - traefik-public
  deploy_common: &deploy_common
    placement:
      constraints:
        - node.labels.majestic-backend == true
    update_config:
      order: start-first
  logging_common: &logging_common
    driver: loki
    options:
      loki-url: http://127.0.0.1:3100/loki/api/v1/push
      loki-retries: 5
      loki-batch-size: 400
  environment_common: &environment_common
    GATEWAY_HOST: api-gateway
    GATEWAY_PORT: 8080
    GATEWAY_METRICS_PORT: 8081
    SOCKET_HOST: socket-gateway
    SOCKET_PORT: 8080
    SOCKET_METRICS_PORT: 8081
    UPLOADS_HOST: uploads
    UPLOADS_PORT: 8080
    UPLOADS_METRICS_PORT: 8081
    REPORTS_HOST: reports
    REPORTS_PORT: 8080
    REPORTS_METRICS_PORT: 8081
    PAYMENTS_HOST: payments
    PAYMENTS_PORT: 8080
    PAYMENTS_METRICS_PORT: 8081
    COMMON_HOST: common
    COMMON_PORT: 8080
    COMMON_METRICS_PORT: 8081
    ADMIN_HOST: admin
    ADMIN_PORT: 8080
    ADMIN_METRICS_PORT: 8081

secrets:
  backend-config.json:
    name: backend-config-prod-v6
    external: true

configs:
  prometheus-config:
    name: backend-prometheus-config-v1
    external: true

networks:
  traefik-public: { external: true }
  monitoring: { external: true }

volumes:
  prometheus-data:

services:
  api-gateway:
    image: majesticgames/majestic-backend-api-gateway:master
    environment: *environment_common
    secrets: *secrets_common
    networks: *networks_common
    logging: *logging_common
    deploy:
      <<: *deploy_common
      replicas: 8
      placement:
        constraints:
          - node.role == manager
      labels:
        traefik.enable: 'true'
        traefik.docker.network: traefik-public
        traefik.http.routers.mj-backend-api.entrypoints: http
        traefik.http.routers.mj-backend-api.rule: Host(`api2master.majestic-files.com`)
        traefik.http.services.mj-backend-api.loadbalancer.server.port: '8080'

  socket-gateway:
    image: majesticgames/majestic-backend-socket-gateway:master
    environment: *environment_common
    secrets: *secrets_common
    networks: *networks_common
    logging: *logging_common
    deploy:
      <<: *deploy_common
      replicas: 3
      placement:
        constraints:
          - node.role == manager
      labels:
        traefik.enable: 'true'
        traefik.docker.network: traefik-public
        traefik.http.routers.mj-backend-socket.entrypoints: http
        traefik.http.routers.mj-backend-socket.rule: Host(`api2master.majestic-files.com`) && PathPrefix(`/websockets`)
        traefik.http.services.mj-backend-socket.loadbalancer.server.port: '8080'

  admin:
    image: majesticgames/majestic-backend-admin:master
    environment: *environment_common
    secrets: *secrets_common
    logging: *logging_common
    deploy:
      <<: *deploy_common
      replicas: 1

  reports:
    image: majesticgames/majestic-backend-reports:master
    environment:
      <<: *environment_common
      REPLICA: '{{.Task.Slot}}'
    secrets: *secrets_common
    logging: *logging_common
    deploy:
      <<: *deploy_common
      replicas: 8

  uploads:
    image: majesticgames/majestic-backend-uploads:master
    environment: *environment_common
    secrets: *secrets_common
    logging: *logging_common
    deploy:
      <<: *deploy_common
      replicas: 2

  payments:
    image: majesticgames/majestic-backend-payments:master
    environment: *environment_common
    secrets: *secrets_common
    logging: *logging_common
    deploy:
      <<: *deploy_common
      replicas: 1

  common:
    image: majesticgames/majestic-backend-common:master
    environment: *environment_common
    secrets: *secrets_common
    logging: *logging_common
    deploy:
      <<: *deploy_common
      replicas: 1

  prometheus:
    image: prom/prometheus
    command: >
      --config.file=/prometheus-config
      --log.level=error
      --storage.tsdb.path=/prometheus
      --storage.tsdb.retention.time=30d
    configs:
      - prometheus-config
    networks:
      - default
      - monitoring
    volumes:
      - prometheus-data:/prometheus
    deploy:
      placement:
        constraints:
          - node.role == manager
