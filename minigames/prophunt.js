export const phConfig = {
	'default': {
		weapon: 'weapon_snowlauncher', // используемое оружие

		propAfkDistance: 2, // дистанция, на которую проп должен сдвинуться, чтобы не попасть в афк таймер
		propAfkTime: 180_000, // допустимое время нахождения на одном месте
		propAfkDamageTime: 5_000, // время до начала получения урона после оповещания об афк
		propAfkDamage: 3, // урон по пропу стоящему в афк
		weaponRefillTime: 2_000, // кд выстрелов из оружия
		playerMissDamage: 6, // урон получаемый охотником при промахе
		withoutPropDamage: 100, // получаемый урон, если игрок по какой-то причине не стал пропом
		healForKill: 100, // лечение за убийство пропа
		modelChangeDamageTimeout: 5_000, // сколько времени проп не сможет менять модель после получения урона
	},
	'Bankinside': {
		propAfkDistance: 1, // дистанция, на которую проп должен сдвинуться, чтобы не попасть в афк таймер
		propAfkTime: 240_000, // допустимое время нахождения на одном месте
		playerMissDamage: 4, // урон получаемый охотником при промахе
	},
	'church': {
		propAfkDistance: 3, // дистанция, на которую проп должен сдвинуться, чтобы не попасть в афк таймер
		propAfkTime: 120_000, // допустимое время нахождения на одном месте
		playerMissDamage: 2, // урон получаемый охотником при промахе
	},
};

export const tauntsConfig = {
	// пауза между началом подготовки насмешки (случайно)
	tauntPauseTimeMin: 10_000, // минимальное значение
	tauntPauseTimeMax: 10_000, // максимальное значение

	// длительность подготовки насмешки (случайно)
	tauntPrepareTimeMin: 100_000, // минимальное значение
	tauntPrepareTimeMax: 200_000, // максимальное значение

	list: [
		{ sound: 'mediaSounds/amogus', volume: 50 },
		{ sound: 'mediaSounds/bell', volume: 50 },
		{ sound: 'mediaSounds/donk', volume: 50 },
		{ sound: 'mediaSounds/sadcrying', volume: 50 },
		{ sound: 'mediaSounds/music', volume: 50 },
		{ sound: 'mediaSounds/sleep', volume: 50 },
		{ sound: 'mediaSounds/catlaugh', volume: 50 },
		{ sound: 'mediaSounds/orizzsounds', volume: 50 },
	]
}

export const winMusic = {
	list: [
		{ sound: 'gungame/win_music_ph_1', volume: 50 },
		{ sound: 'gungame/win_music_ph_2', volume: 50 },
		{ sound: 'gungame/win_music_ph_3', volume: 50 },
		{ sound: 'gungame/win_music_ph_4', volume: 50 },
		{ sound: 'gungame/win_music_ph_5', volume: 50 },
	]
}

export const propsConfig = {
	// BANK
	//prop_snow_trailer01: {
	//	model: 'prop_snow_trailer01',
	//	damage: 10,
	//	maps: ['bank'],
	//},
	//prop_snow_tree_03_e: {
	//	model: 'prop_snow_tree_03_e',
	//	damage: 25,
	//	maps: ['bank', 'church'],
	//},
	//prop_snow_tree_03_i: {
	//	model: 'prop_snow_tree_03_i',
	//	damage: 20,
	//	map: 'bank',
	//},
	prop_snow_telegraph_03: {
		model: 'prop_snow_telegraph_03',
		damage: 30,
		map: 'bank',
	},
	prop_snow_cam_03: {
		model: 'prop_snow_cam_03',
		damage: 40,
		map: 'bank',
	},
	//plg_06_fencepart: {
	//	model: 'plg_06_fencepart',
	//	damage: 35,
	//	map: 'bank',
	//},
	prop_snow_dumpster_01: {
		model: 'prop_snow_dumpster_01',
		damage: 25,
		maps: ['bank'],
	},
	prop_flattruck_01c: {
		model: 'prop_flattruck_01c',
		damage: 40,
		map: 'bank',
	},
	prop_rub_cage01b: {
		model: 'prop_rub_cage01b',
		damage: 40,
		map: 'bank',
	},
	prop_prologue_pillar_01: {
		model: 'prop_prologue_pillar_01',
		damage: 50,
		map: 'bank',
		offset: { x: 0, y: 0, z: 2 },
	},
	prop_box_wood07a: {
		model: 'prop_box_wood07a',
		damage: 35,
		maps: ['bank','Bankinside'],
	},
	prop_box_wood04a: {
		model: 'prop_box_wood04a',
		damage: 35,
		maps: ['bank','Bankinside'],
	},
	prop_box_wood01a: {
		model: 'prop_box_wood01a',
		damage: 60,
		maps: ['bank','Bankinside'],
	},
	prop_box_wood03a: {
		model: 'prop_box_wood03a',
		damage: 40,
		maps: ['bank','Bankinside'],
	},
	//prop_aircon_m_06: {
	//	model: 'prop_aircon_m_06',
	//	damage: 35,
	//	map: 'bank',
	//},
	//prop_aircon_m_04: {
	//	model: 'prop_aircon_m_04',
	//	damage: 30,
	//	map: 'bank',
	//},
	prop_snow_cam_03a: {
		model: 'prop_snow_cam_03a',
		damage: 50,
		map: 'bank',
	},
	//prop_snow_wall_light_09a: {
	//	model: 'prop_snow_wall_light_09a',
	//	damage: 80,
	//	map: 'bank',
	//},
	//prop_telegwall_03a: {
	//	model: 'prop_telegwall_03a',
	//	damage: 70,
	//	map: 'bank',
	//},
	//prop_snow_wall_light_15a: {
	//	model: 'prop_snow_wall_light_15a',
	//	damage: 45,
	//	map: 'bank',
	//},
	//plg_06_05: {
	//	model: 'plg_06_05',
	//	damage: 35,
	//	map: 'bank',
	//},
	plg_06_plg_barier_conc_02a: {
		model: 'plg_06_plg_barier_conc_02a',
		damage: 30,
		map: 'bank',
	},
	mj_box_gift_1: {
		model: 'mj_box_gift_1',
		damage: 65,
		map: 'bank',
		offset: { x: 0, y: 0, z: 0.25 },
	},
	mj_box_gift_2: {
		model: 'mj_box_gift_2',
		damage: 35,
		maps: ['bank','church'],
		offset: { x: 0, y: 0, z: 0.2 },
	},
	mj_box_gift_3: {
		model: 'mj_box_gift_3',
		damage: 35,
		maps: ['bank','church'],
		offset: { x: 0, y: 0, z: 0.75 },
	},
	mj_cool_snowman_1: {
		model: 'mj_cool_snowman_1',
		damage: 50,
		map: 'bank',
		offset: { x: 0, y: 0, z: 0.6 }
	},
	mj_cool_snowman_2: {
		model: 'mj_cool_snowman_2',
		damage: 50,
		map: 'bank',
		offset: { x: 0, y: 0, z: 0.7 },
	},
	mj_little_snowman_1: {
		model: 'mj_little_snowman_1',
		damage: 65,
		map: 'bank',
		offset: { x: 0, y: 0, z: 0.35 },
	},
	mj_little_snowman_2: {
		model: 'mj_little_snowman_2',
		damage: 65,
		map: 'bank',
		offset: { x: 0, y: 0, z: 0.6 },
	},
	mj_little_snowman_3: {
		model: 'mj_little_snowman_3',
		damage: 65,
		map: 'bank',
		offset: { x: 0, y: 0, z: 0.25 },
	},
	mj_xm_tree_no_neon: {
		model: 'mj_xm_tree_no_neon',
		damage: 50,
		map: 'bank',
	},
	mj_xm_sc3: {
		model: 'mj_xm_sc3',
		damage: 25,
		maps: ['bank','church']
	},
	prop_snow_bin_02: {
		model: 'prop_snow_bin_02',
		damage: 30,
		maps: 'bank',
	},

	// CHURCH
	prop_snow_bin_01: {
		model: 'prop_snow_bin_01',
		damage: 30,
		map: 'church',
		offset: { x: 0, y: 0, z: 0.3 },
	},
	prop_snow_bench_01: {
		model: 'prop_snow_bench_01',
		damage: 25,
		map: 'church',
	},
	//plg_03_tomb_018: {
	//	model: 'plg_03_tomb_018',
	//	damage: 30,
	//	group: 'tomb',
	//	map: 'church',
	//},
	//plg_03_tomb_017: {
	//	model: 'plg_03_tomb_017',
	//	damage: 30,
	//	group: 'tomb',
	//	map: 'church',
	//},
	//plg_03_tomb_015: {
	//	model: 'plg_03_tomb_015',
	//	damage: 30,
	//	group: 'tomb',
	//	map: 'church',
	//},
	//plg_03_tomb_014: {
	//	model: 'plg_03_tomb_014',
	//	damage: 30,
	//	group: 'tomb',
	//	map: 'church',
	//},
	//plg_03_tomb_019: {
	//	model: 'plg_03_tomb_019',
	//	damage: 30,
	//	group: 'tomb',
	//	map: 'church',
	//},
	//plg_03_tomb_012: {
	//	model: 'plg_03_tomb_012',
	//	damage: 30,
	//	group: 'tomb',
	//	map: 'church',
	//},
	//plg_03_tomb_005: {
	//	model: 'plg_03_tomb_005',
	//	damage: 30,
	//	group: 'tomb',
	//	map: 'church',
	//},
	//plg_03_tomb_004: {
	//	model: 'plg_03_tomb_004',
	//	damage: 30,
	//	group: 'tomb',
	//	map: 'church',
	//},
	//plg_03_tomb_011: {
	//	model: 'plg_03_tomb_011',
	//	damage: 30,
	//	group: 'tomb',
	//	map: 'church',
	//},
	//plg_03_tomb_013: {
	//	model: 'plg_03_tomb_013',
	//	damage: 30,
	//	group: 'tomb',
	//	map: 'church',
	//},
	//prop_snow_tree_03_h: {
	//	model: 'prop_snow_tree_03_h',
	//	damage: 15,
	//	maps: ['church'],
	//},
	prop_snow_sign_road_01a: {
		model: 'prop_snow_sign_road_01a',
		damage: 100,
		map: 'church',
	},
	prop_snow_traffic_rail_1a: {
		model: 'prop_snow_traffic_rail_1a',
		damage: 30,
		map: 'church',
	},
	prop_snow_elecbox_16: {
		model: 'prop_snow_elecbox_16',
		damage: 40,
		map: 'church',
		offset: { x: 0, y: 0, z: -0.05 },
	},
	prop_snow_streetlight_09: {
		model: 'prop_snow_streetlight_09',
		damage: 50,
		map: 'church',
	},
	//plg_03_graverail001: {
	//	model: 'plg_03_graverail001',
	//	damage: 65,
	//	map: 'church',
	//},
	prop_prlg_gravestone_01a: {
		model: 'prop_prlg_gravestone_01a',
		damage: 90,
		map: 'church',
	},
	prop_prlg_gravestone_02a: {
		model: 'prop_prlg_gravestone_02a',
		damage: 90,
		map: 'church',
	},
	prop_prlg_gravestone_03a: {
		model: 'prop_prlg_gravestone_03a',
		damage: 90,
		map: 'church',
	},
	prop_prlg_gravestone_04a: {
		model: 'prop_prlg_gravestone_04a',
		damage: 90,
		map: 'church',
	},
	prop_prlg_gravestone_05a: {
		model: 'prop_prlg_gravestone_05a',
		damage: 90,
		map: 'church',
		offset: { x: 0, y: 0, z: -1.25 },
	},
	prop_prlg_gravestone_06a: {
		model: 'prop_prlg_gravestone_06a',
		damage: 90,
		map: 'church',
	},
	prop_prlg_gravestone_07a: {
		model: 'prop_prlg_gravestone_07a',
		damage: 90,
		map: 'church',
	},
	//prop_snow_flower_01: {
	//	model: 'prop_snow_flower_01',
	//	damage: 100,
	//	map: 'church',
	//},
	mj_breadman: {
		model: 'mj_breadman',
		damage: 30,
		map: 'church',
		offset: { x: 0, y: 0, z: 1.25 },
	},
	mj_box_big_gift_1: {
		model: 'mj_box_big_gift_1',
		damage: 35,
		map: 'church',
		offset: { x: 0, y: 0, z: 1.75 },
	},
	mj_box_big_gift_2: {
		model: 'mj_box_big_gift_2',
		damage: 35,
		map: 'church',
		offset: { x: 0, y: 0, z: 0.4 },
	},
	mj_cool_snowman_3: {
		model: 'mj_cool_snowman_3',
		damage: 65,
		map: 'church',
		offset: { x: 0, y: 0, z: 0.7 },
	},
	plg_03_bale: {
		model: 'plg_03_bale',
		damage: 50,
		map: 'church',
	},

	// Bankinside
	v_corp_banktrolley: {
		model: 'v_corp_banktrolley',
		damage: 40,
		map: 'Bankinside',
	},
	//prop_fire_exting_3a: {
	//	model: 'prop_fire_exting_3a',
	//	damage: 70,
	//	map: 'Bankinside',
	//},
	v_corp_cd_chair: {
		model: 'v_corp_cd_chair',
		damage: 60,
		map: 'Bankinside',
	},
	v_ind_rc_lowtable: {
		model: 'v_ind_rc_lowtable',
		damage: 50,
		map: 'Bankinside',
	},
	v_ret_fh_pizza02: {
		model: 'v_ret_fh_pizza02',
		damage: 70,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.05 },
	},
	prop_cd_folder_pile1: {
		model: 'prop_cd_folder_pile1',
		damage: 80,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.2 },
	},
	prop_cd_folder_pile3: {
		model: 'prop_cd_folder_pile3',
		damage: 80,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.2 },
	},
	prop_cd_folder_pile2: {
		model: 'prop_cd_folder_pile2',
		damage: 80,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.05 },
	},
	prop_cd_folder_pile4: {
		model: 'prop_cd_folder_pile4',
		damage: 80,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.02 },
	},
	prop_cctv_cont_05: {
		model: 'prop_cctv_cont_05',
		damage: 100,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.05 },
	},
	v_corp_deskdraw: {
		model: 'v_corp_deskdraw',
		damage: 50,
		map: 'Bankinside',
	},
	prop_tapeplayer_01: {
		model: 'prop_tapeplayer_01',
		damage: 85,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.1 },
	},
	prop_cardbordbox_02a: {
		model: 'prop_cardbordbox_02a',
		damage: 60,
		map: 'Bankinside',
	},
	v_corp_cd_desklamp: {
		model: 'v_corp_cd_desklamp',
		damage: 75,
		map: 'Bankinside',
	},
	prop_dest_cctv_01: {
		model: 'prop_dest_cctv_01',
		damage: 40,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.22 },
	},
	prop_cctv_cont_01: {
		model: 'prop_cctv_cont_01',
		damage: 60,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.1 },
	},
	prop_cctv_cont_04: {
		model: 'prop_cctv_cont_04',
		damage: 75,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.1 },
	},
	prop_cctv_cont_02: {
		model: 'prop_cctv_cont_02',
		damage: 60,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.1 },
	},
	prop_dyn_pc: {
		model: 'prop_dyn_pc',
		damage: 70,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.27 },
	},
	prop_cctv_cont_03: {
		model: 'prop_cctv_cont_03',
		damage: 60,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.1 },
	},
	v_res_filebox01: {
		model: 'v_res_filebox01',
		damage: 55,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.18 },
	},
	prop_cctv_cont_06: {
		model: 'prop_cctv_cont_06',
		damage: 100,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.05 },
	},
	v_corp_cd_heater: {
		model: 'v_corp_cd_heater',
		damage: 50,
		map: 'Bankinside',
	},
	v_corp_filecabtall: {
		model: 'v_corp_filecabtall',
		damage: 35,
		map: 'Bankinside',
	},
	prop_watercooler_dark: {
		model: 'prop_watercooler_dark',
		damage: 30,
		map: 'Bankinside',
	},
	v_corp_cd_rectable: {
		model: 'v_corp_cd_rectable',
		damage: 35,
		map: 'Bankinside',
	},
	v_corp_cd_recseat: {
		model: 'v_corp_cd_recseat',
		damage: 25,
		map: 'Bankinside',
	},
	v_corp_cd_intercom: {
		model: 'v_corp_cd_intercom',
		damage: 100,
		map: 'Bankinside',
	},
	prop_crt_mon_02: {
		model: 'prop_crt_mon_02',
		damage: 60,
		map: 'Bankinside',
		offset: { x: 0, y: 0, z: 0.1 },
	},
	v_ind_rc_workbag: {
		model: 'v_ind_rc_workbag',
		damage: 70,
		map: 'Bankinside',
	},
	v_corp_cd_wellies: {
		model: 'v_corp_cd_wellies',
		damage: 80,
		map: 'Bankinside',
	},
	prop_tool_box_04: {
		model: 'prop_tool_box_04',
		damage: 70,
		map: 'Bankinside',
	},

	// BARN
	//prop_bench_06: {
	//	model: 'prop_bench_06',
	//	damage: 30,
	//	map: 'barn',
	//},
	//prop_rub_tyre_02: {
	//	model: 'prop_rub_tyre_02',
	//	damage: 45,
	//	map: 'barn',
	//	offset: { x: 0, y: 0, z: 0.5 },
	//},
	//prop_chair_06: {
	//	model: 'prop_chair_06',
	//	damage: 80,
	//	map: 'barn',
	//},
	//prop_rub_stool: {
	//	model: 'prop_rub_stool',
	//	damage: 90,
	//	map: 'barn',
	//	offset: { x: 0, y: 0, z: 0.22 },
	//},
	//prop_hose_1: {
	//	model: 'prop_hose_1',
	//	damage: 70,
	//	map: 'barn',
	//},
	//prop_paints_can01: {
	//	model: 'prop_paints_can01',
	//	damage: 100,
	//	map: 'barn',
	//},
	//prop_paints_can03: {
	//	model: 'prop_paints_can03',
	//	damage: 100,
	//	map: 'barn',
	//},
	//prop_table_03b: {
	//	model: 'prop_table_03b',
	//	damage: 30,
	//	map: 'barn',
	//	offset: { x: 0, y: 0, z: 0.4 },
	//},
	//prop_snow_diggerbkt_01: {
	//	model: 'prop_snow_diggerbkt_01',
	//	damage: 20,
	//	map: 'barn',
	//},
	//prop_rub_scrap_05: {
	//	model: 'prop_rub_scrap_05',
	//	damage: 30,
	//	map: 'barn',
	//},
	//prop_snow_grain_01: {
	//	model: 'prop_snow_grain_01',
	//	damage: 20,
	//	map: 'barn',
	//},
	//prop_snow_barrel_pile_03: {
	//	model: 'prop_snow_barrel_pile_03',
	//	damage: 30,
	//	map: 'barn',
	//},
	//prop_snow_bailer_01: {
	//	model: 'prop_snow_bailer_01',
	//	damage: 15,
	//	map: 'barn',
	//},
	//prop_snow_tyre_01: {
	//	model: 'prop_snow_tyre_01',
	//	damage: 30,
	//	map: 'barn',
	//},
	//prop_snow_side_spreader_01: {
	//	model: 'prop_snow_side_spreader_01',
	//	damage: 20,
	//	map: 'barn',
	//},
	//prop_snow_light_01: {
	//	model: 'prop_snow_light_01',
	//	damage: 25,
	//	map: 'barn',
	//},
	//prop_snow_woodpile_04a: {
	//	model: 'prop_snow_woodpile_04a',
	//	damage: 45,
	//	map: 'barn',
	//},
	//prop_snow_oldlight_01b: {
	//	model: 'prop_snow_oldlight_01b',
	//	damage: 80,
	//	map: 'barn',
	//},
	//plg_04_bale001: {
	//	model: 'plg_04_bale001',
	//	damage: 45,
	//	map: 'barn',
	//},
	//mj_xm_sc2: {
	//	model: 'mj_xm_sc2',
	//	damage: 25,
	//	map: 'barn',
	//},
	//mj_box_present_1: {
	//	model: 'mj_box_present_1',
	//	damage: 100,
	//	map: 'barn',
	//},
	//mj_box_present_2: {
	//	model: 'mj_box_present_2',
	//	damage: 100,
	//	map: 'barn',
	//},
	//mj_box_present_4: {
	//	model: 'mj_box_present_4',
	//	damage: 100,
	//	map: 'barn',
	//},
	//mj_tree_gift: {
	//	model: 'mj_tree_gift',
	//	damage: 80,
	//	map: 'barn',
	//},
};
