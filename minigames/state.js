export const WheelStateData = {
  timerInitTime: 15000, // initTime
  timerTimeEnd: 5000, // timeEnd
  gameSpinTime: 25000, // spinTime
}

export const JackpotStateDate = {
  timerInitTime: 60000, // initTime
  gameTime: 25000, // timeGame
  gameTimeEnd: 10000, // timeEnd
}

export const CrashStateDate = {
  timerInitTime: 15000, // initTime
  gameTimeEnd: 5000, // timeEnd
}

export const WheelColorsWithCoef = {
  white: { name: 'white', coefficient: 2.0 },
  red: { name: 'red', coefficient: 3.0 },
  green: { name: 'green', coefficient: 5.0 },
  yellow: { name: 'yellow', coefficient: 10.0 },
}

export const MinigamesMaxPlayers = {
  jackpot: 50,
}

export const MinigamesBetAmounts = {
  crash: {
    min: 50,
    max: 20000,
  },
  jackpot: {
    min: 50,
    max: 100000,
  },
  wheel: {
    min: 10,
    max: 30000,
  },
}
