{"name": "@majestic-backend/workspace", "version": "0.0.0", "license": "MIT", "scripts": {"build:all": "nx run-many -t build", "container:all": "nx run-many -t container", "serve": "yarn serve:all -p", "serve:all": "nx run-many -t serve --parallel 100 --output-style stream", "prisma:generate-game": "prisma generate --schema=libs/database/src/prisma/schemas/game.prisma --generator=local", "prisma:generate-backend": "prisma generate --schema=libs/database/src/prisma/schemas/backend.prisma --generator=local --generator=json", "prisma:generate": "yarn prisma:generate-game && yarn prisma:generate-backend", "infra:start": "docker compose -f docker/docker-compose.infra.yml --project-directory . up -d", "infra:stop": "docker compose -f docker/docker-compose.infra.yml --project-directory . down", "stack:build-image": "docker build -t majestic-node -f ./docker/Dockerfile.majestic-node .", "stack:start": "env $(cat .env | grep ^[A-Z] | xargs) docker stack deploy -c ./docker/docker-compose.stack-local.yml majestic-backend", "stack:stop": "docker stack rm majestic-backend"}, "private": true, "dependencies": {"@aws-sdk/client-s3": "^3.534.0", "@gitbeaker/core": "^41.1.1", "@gitbeaker/rest": "^41.1.1", "@golevelup/nestjs-discovery": "^4.0.3", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.0.2", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.2", "@nestjs/cqrs": "^10.2.8", "@nestjs/jwt": "^10.1.1", "@nestjs/microservices": "^10.3.8", "@nestjs/platform-express": "^10.0.2", "@nestjs/platform-socket.io": "^10.4.15", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^7.4.0", "@nestjs/websockets": "^10.3.1", "@prisma/client": "5.18.0", "@sentry/nestjs": "^8.34.0", "@sentry/profiling-node": "^8.34.0", "@types/paypal-rest-sdk": "^1.7.9", "@types/uuid": "^10.0.0", "@willsoto/nestjs-prometheus": "^6.0.1", "amqplib": "0.10.8", "axios": "^1.0.0", "bcrypt": "^5.1.1", "canvas": "^2.11.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "discord.js": "^14.16.3", "express": "^4.19.2", "html-pdf-node": "^1.0.8", "i18next": "^23.7.7", "js-binary": "^1.2.0", "lodash": "^4.17.21", "mailgun.js": "^12.0.1", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "multiparty": "^4.2.3", "necord": "^6.8.6", "nestjs-telegraf": "^2.8.1", "node-2fa": "^2.0.3", "node-telegram-bot-api": "^0.66.0", "nodemailer": "^6.9.15", "paypal-rest-sdk": "1.8.1", "prisma": "5.18.0", "prom-client": "^15.1.3", "redis": "^4.6.11", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.0", "socket.io": "^4.8.1", "telegraf": "^4.16.3", "tslib": "^2.3.0", "uuid": "^10.0.0", "winston": "^3.17.0"}, "devDependencies": {"@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nx-tools/nx-container": "^6.0.0", "@nx/eslint": "17.0.0", "@nx/eslint-plugin": "17.0.0", "@nx/jest": "17.0.0", "@nx/js": "17.0.0", "@nx/nest": "17.0.0", "@nx/node": "17.0.0", "@nx/webpack": "17.0.0", "@nx/workspace": "17.0.0", "@swc-node/register": "~1.6.7", "@swc/core": "~1.3.85", "@types/amqplib": "0.10.7", "@types/bcrypt": "^5.0.1", "@types/html-pdf-node": "~1.0.2", "@types/imagemin-mozjpeg": "^8.0.4", "@types/jest": "^29.4.0", "@types/lodash": "^4.14.202", "@types/multiparty": "^0.0.36", "@types/node": "~18.7.1", "@types/node-telegram-bot-api": "^0.64.7", "@types/nodemailer": "^6.4.16", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "eslint": "~8.46.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.4.1", "jest-environment-node": "^29.4.1", "nx": "17.0.0", "prettier": "^2.6.2", "prisma-json-types-generator": "^3.2.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "~5.1.3"}}