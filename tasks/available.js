import { awardEnum } from './index';

export const levels = {
    low: 1,
    medium: 2,
    high: 3,
}

export const list = [
    {
        id: 'drift_points',
        level: levels.high,

        title: 'tasks.list.0.title',
        description: 'tasks.list.0.description',
        bot: { id: 9, name: '<PERSON>' },
        questType: 'tasks.list.0.questType',

        requirement: {
            title: 'tasks.list.0.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'drift_points',
            quantityForFullProgress: 15000,
        },
    },
    {
        id: 'job_fisherman',
        level: levels.high,

        title: 'tasks.list.1.title',
        description: 'tasks.list.1.description',
        bot: { id: 6, name: '<PERSON>' },
        questType: 'tasks.list.1.questType',

        requirement: {
            title: 'tasks.list.1.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'job_fisherman',
            quantityForFullProgress: 20,
        },
    },
/*     {
        id: 'greet_player',
        level: levels.medium,

        title: 'tasks.list.2.title',
        description: 'tasks.list.2.description',
        bot: { id: 5, name: '<PERSON>' },
        questType: 'tasks.list.2.questType',

        requirement: {
            title: 'tasks.list.2.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'greet_player',
            quantityForFullProgress: 15,
        },
    }, */
    {
        id: 'task_use_fireworks',
        level: levels.medium,

        title: 'tasks.list.3.title',
        description: 'tasks.list.3.description',
        bot: { id: 8, name: 'Cameron Whitney' },
        questType: 'tasks.list.3.questType',

        requirement: {
            title: 'tasks.list.3.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'task_use_fireworks',
            trigger_value: [394, 395, 396, 397],
            quantityForFullProgress: 5,
        },
    },
    {
        id: 'win_casino_roulette',
        level: levels.medium,

        title: 'tasks.list.4.title',
        description: 'tasks.list.4.description',
        bot: { id: 18, name: 'Mollie Malone' },
        questType: 'tasks.list.4.questType',

        requirement: {
            title: 'tasks.list.4.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'win_casino_roulette',
            quantityForFullProgress: 20,
        },
    },
    {
        id: 'win_casino_blackjack',
        level: levels.high,

        title: 'tasks.list.5.title',
        description: 'tasks.list.5.description',
        bot: { id: 18, name: 'Mollie Malone' },
        questType: 'tasks.list.5.questType',

        requirement: {
            title: 'tasks.list.5.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'win_casino_blackjack',
            quantityForFullProgress: 15,
        },
    },
    {
        id: 'win_casino_horserace',
        level: levels.medium,

        title: 'tasks.list.6.title',
        description: 'tasks.list.6.description',
        bot: { id: 18, name: 'Mollie Malone' },
        questType: 'tasks.list.6.questType',

        requirement: {
            title: 'tasks.list.6.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'win_casino_horserace',
            quantityForFullProgress: 5,
        },
    },
    {
        id: 'win_casino_slots',
        level: levels.high,

        title: 'tasks.list.7.title',
        description: 'tasks.list.7.description',
        bot: { id: 18, name: 'Mollie Malone' },
        questType: 'tasks.list.7.questType',

        requirement: {
            title: 'tasks.list.7.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'win_casino_slots',
            quantityForFullProgress: 15,
        },
    },
    {
        id: 'metal_detector_dig',
        level: levels.high,

        title: 'tasks.list.8.title',
        description: 'tasks.list.8.description',
        bot: { id: 20, name: 'Frederick Summers' },
        questType: 'tasks.list.8.questType',

        requirement: {
            title: 'tasks.list.8.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'metal_detector_dig',
            quantityForFullProgress: 3,
        },
    },
    {
        id: 'job_trucker',
        level: levels.high,

        title: 'tasks.list.9.title',
        description: 'tasks.list.9.description',
        bot: { id: 20, name: 'Frederick Summers' },
        questType: 'tasks.list.9.questType',

        requirement: {
            title: 'tasks.list.9.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'job_trucker',
            quantityForFullProgress: 15,
        },
    },
    {
        id: 'job_mushroomer',
        level: levels.high,

        title: 'tasks.list.10.title',
        description: 'tasks.list.10.description',
        bot: { id: 6, name: 'Rudolph Burnett' },
        questType: 'tasks.list.10.questType',

        requirement: {
            title: 'tasks.list.10.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'job_mushroomer',
            quantityForFullProgress: 60,
        },
    },
    {
        id: 'job_lumberjack',
        level: levels.high,

        title: 'tasks.list.11.title',
        description: 'tasks.list.11.description',
        bot: { id: 5, name: 'Otis Knight' },
        questType: 'tasks.list.11.questType',

        requirement: {
            title: 'tasks.list.11.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'job_lumberjack',
            quantityForFullProgress: 20,
        },
    },
    {
        id: 'job_gopostal',
        level: levels.high,

        title: 'tasks.list.12.title',
        description: 'tasks.list.12.description',
        bot: { id: 6, name: 'Rudolph Burnett' },
        questType: 'tasks.list.12.questType',

        requirement: {
            title: 'tasks.list.12.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'job_gopostal',
            quantityForFullProgress: 15,
        },
    },
    {
        id: 'job_garbagecollector',
        level: levels.high,

        title: 'tasks.list.13.title',
        description: 'tasks.list.13.description',
        bot: { id: 20, name: 'Frederick Summers' },
        questType: 'tasks.list.13.questType',

        requirement: {
            title: 'tasks.list.13.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'job_garbagecollector',
            quantityForFullProgress: 90,
        },
    },
    {
        id: 'play_minigames',
        level: levels.high,

        title: 'tasks.list.14.title',
        description: 'tasks.list.14.description',
        bot: { id: 9, name: 'Beatrice Franks' },
        questType: 'tasks.list.14.questType',

        requirement: {
            title: 'tasks.list.14.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'play_minigames',
            quantityForFullProgress: 5,
        },
    },
    {
        id: 'use_gym',
        level: levels.medium,

        title: 'tasks.list.15.title',
        description: 'tasks.list.15.description',
        bot: { id: 8, name: 'Cameron Whitney' },
        questType: 'tasks.list.15.questType',

        requirement: {
            title: 'tasks.list.15.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'use_gym',
            quantityForFullProgress: 20,
        },
    },
    {
        id: 'cinema_set_video',
        level: levels.medium,

        title: 'tasks.list.16.title',
        description: 'tasks.list.16.description',
        bot: { id: 8, name: 'Cameron Whitney' },
        questType: 'tasks.list.16.questType',

        requirement: {
            title: 'tasks.list.16.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'cinema_set_video',
            quantityForFullProgress: 3,
        },
    },
    {
        id: 'use_carwash',
        level: levels.medium,

        title: 'tasks.list.17.title',
        description: 'tasks.list.17.description',
        bot: { id: 20, name: 'Frederick Summers' },
        questType: 'tasks.list.17.questType',

        requirement: {
            title: 'tasks.list.17.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'use_carwash',
            quantityForFullProgress: 3,
        },
    },
    {
        id: 'bbq_use',
        level: levels.medium,

        title: 'tasks.list.18.title',
        description: 'tasks.list.18.description',
        bot: { id: 8, name: 'Cameron Whitney' },
        questType: 'tasks.list.18.questType',

        requirement: {
            title: 'tasks.list.18.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'bbq_use',
            quantityForFullProgress: 5,
        },
    },
    {
        id: 'farm_harvest',
        level: levels.high,

        title: 'tasks.list.19.title',
        description: 'tasks.list.19.description',
        bot: { id: 6, name: 'Rudolph Burnett' },
        questType: 'tasks.list.19.questType',

        requirement: {
            title: 'tasks.list.19.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'farm_harvest',
            quantityForFullProgress: 140,
        },
    },
    {
        id: 'private_dance_buy',
        level: levels.medium,

        title: 'tasks.list.20.title',
        description: 'tasks.list.20.description',
        bot: { id: 8, name: 'Cameron Whitney' },
        questType: 'tasks.list.20.questType',

        requirement: {
            title: 'tasks.list.20.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'private_dance_buy',
            quantityForFullProgress: 1,
        },
    },
    {
        id: 'repair_vehicle',
        level: levels.medium,

        title: 'tasks.list.21.title',
        description: 'tasks.list.21.description',
        bot: { id: 8, name: 'Cameron Whitney' },
        questType: 'tasks.list.21.questType',

        requirement: {
            title: 'tasks.list.21.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'repair_vehicle',
            quantityForFullProgress: 3,
        },
    },
/*     {
        id: 'bookmaker_bet',
        level: levels.medium,

        title: 'tasks.list.22.title',
        description: 'tasks.list.22.description',
        bot: { id: 2, name: 'Eva Levante' },
        questType: 'tasks.list.22.questType',

        requirement: {
            title: 'tasks.list.22.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'bookmaker_bet',
            quantityForFullProgress: 50000,
        },
    }, */
    // {
    //     id: 'tuning_vehicle',
    //     level: levels.medium,

    //     title: 'tasks.list.23.title',
    //     description: 'tasks.list.23.description',
    //     bot: { id: 8, name: 'Cameron Whitney' },
    //     questType: 'tasks.list.23.questType',

    //     requirement: {
    //         title: 'tasks.list.23.requirement.title',
    //         //completed: false,
    //         //progress: 1,
    //         trigger_type: 'tuning_vehicle',
    //         quantityForFullProgress: 4,
    //     },
    // },
    //{
    //    id: 'hijack_vehicle_screwdriver',
    //    level: levels.high,

    //    title: 'tasks.list.24.title',
    //    description: 'tasks.list.24.description',
    //    bot: { id: 8, name: 'Cameron Whitney' },
    //    questType: 'tasks.list.24.questType',

    //    requirement: {
    //        title: 'tasks.list.24.requirement.title',
    //        //completed: false,
    //        //progress: 1,
    //        trigger_type: 'hijack_vehicle_screwdriver',
    //        quantityForFullProgress: 5,
    //    },
    //},
    {
        id: 'parachute_open',
        level: levels.high,

        title: 'tasks.list.25.title',
        description: 'tasks.list.25.description',
        bot: { id: 20, name: 'Frederick Summers' },
        questType: 'tasks.list.25.questType',

        requirement: {
            title: 'tasks.list.25.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'parachute_open',
            quantityForFullProgress: 10,
        },
    },
    {
        id: 'arm_wrestle_play',
        level: levels.medium,

        title: 'tasks.list.26.title',
        description: 'tasks.list.26.description',
        bot: { id: 20, name: 'Frederick Summers' },
        questType: 'tasks.list.26.questType',

        requirement: {
            title: 'tasks.list.26.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'arm_wrestle_play',
            quantityForFullProgress: 5,
        },
    },
    /*{
        id: 'test28',
        level: levels.medium,

        title: 'tasks.list.27.title',
        description: 'tasks.list.27.description',
        bot: { id: 2, name: 'Eva Levante' },
        questType: 'tasks.list.27.questType',

        requirement: {
            title: 'tasks.list.27.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'buy_item',
            trigger_value: [317],
            quantityForFullProgress: 1,
        },
    },
    {
        id: 'test29',
        level: levels.medium,

        title: 'tasks.list.28.title',
        description: 'tasks.list.28.description',
        bot: { id: 2, name: 'Eva Levante' },
        questType: 'tasks.list.28.questType',

        requirement: {
            title: 'tasks.list.28.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'task_eat_item',
            trigger_value: [342],
            quantityForFullProgress: 3,
        },
    },*/
    {
        id: 'push_car',
        level: levels.high,

        title: 'tasks.list.29.title',
        description: 'tasks.list.29.description',
        bot: { id: 20, name: 'Frederick Summers' },
        questType: 'tasks.list.29.questType',

        requirement: {
            title: 'tasks.list.29.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'push_car',
            quantityForFullProgress: 10,
        },
    },
    {
        id: 'change_oil',
        level: levels.medium,

        title: 'tasks.list.30.title',
        description: 'tasks.list.30.description',
        bot: { id: 5, name: 'Otis Knight' },
        questType: 'tasks.list.30.questType',

        requirement: {
            title: 'tasks.list.30.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'change_oil',
            quantityForFullProgress: 1,
        },
    },
    {
        id: 'change_battery',
        level: levels.medium,

        title: 'tasks.list.31.title',
        description: 'tasks.list.31.description',
        bot: { id: 5, name: 'Otis Knight' },
        questType: 'tasks.list.31.questType',

        requirement: {
            title: 'tasks.list.31.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'change_battery',
            quantityForFullProgress: 1,
        },
    },
    {
        id: 'search_trash',
        level: levels.high,

        title: 'tasks.list.32.title',
        description: 'tasks.list.32.description',
        bot: { id: 5, name: 'Otis Knight' },
        questType: 'tasks.list.32.questType',

        requirement: {
            title: 'tasks.list.32.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'search_trash',
            quantityForFullProgress: 20,
        },
    },
    {
        id: 'vending_machine_use',
        level: levels.high,

        title: 'tasks.list.33.title',
        description: 'tasks.list.33.description',
        bot: { id: 18, name: 'Mollie Malone' },
        questType: 'tasks.list.33.questType',

        requirement: {
            title: 'tasks.list.33.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'vending_machine_use',
            quantityForFullProgress: 10,
        },
    },
    // {
    //     id: 'pet_animals',
    //     level: levels.high,

    //     title: 'tasks.list.34.title',
    //     description: 'tasks.list.34.description',
    //     bot: { id: 18, name: 'Mollie Malone' },
    //     questType: 'tasks.list.34.questType',

    //     requirement: {
    //         title: 'tasks.list.34.requirement.title',
    //         //completed: false,
    //         //progress: 1,
    //         trigger_type: 'pet_animals',
    //         quantityForFullProgress: 10,
    //     },
    // },
    {
        id: 'clothesshop_buy',
        level: levels.medium,

        title: 'tasks.list.35.title',
        description: 'tasks.list.35.description',
        bot: { id: 18, name: 'Mollie Malone' },
        questType: 'tasks.list.35.questType',

        requirement: {
            title: 'tasks.list.35.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'clothesshop_buy',
            quantityForFullProgress: 5,
        },
    },
    {
        id: 'salary_job',
        level: levels.high,

        title: 'tasks.list.36.title',
        description: 'tasks.list.36.description',
        bot: { id: 6, name: 'Rudolph Burnett' },
        questType: 'tasks.list.36.questType',

        requirement: {
            title: 'tasks.list.36.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'salary_job',
            quantityForFullProgress: 20000,
        },
    },
    {
        id: 'job_busdriver',
        level: levels.high,

        title: 'tasks.list.37.title',
        description: 'tasks.list.37.description',
        bot: { id: 6, name: 'Rudolph Burnett' },
        questType: 'tasks.list.37.questType',

        requirement: {
            title: 'tasks.list.37.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'job_busdriver',
            quantityForFullProgress: 35,
        },
    },
/*     {
        id: 'test39',
        level: levels.medium,

        title: 'tasks.list.38.title',
        description: 'tasks.list.38.description',
        bot: { id: 2, name: 'Eva Levante' },
        questType: 'tasks.list.38.questType',

        requirement: {
            title: 'tasks.list.38.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'dragy_use',
            quantityForFullProgress: 1,
        },
    }, */
    {
        id: 'payday',
        level: levels.high,

        title: 'tasks.list.39.title',
        description: 'tasks.list.39.description',
        bot: { id: 6, name: 'Rudolph Burnett' },
        questType: 'tasks.list.39.questType',

        requirement: {
            title: 'tasks.list.39.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'payday',
            quantityForFullProgress: 2,
        },
    },
/*     {
        id: 'test41',
        level: levels.medium,

        title: 'tasks.list.40.title',
        description: 'tasks.list.40.description',
        bot: { id: 2, name: 'Eva Levante' },
        questType: 'tasks.list.40.questType',

        requirement: {
            title: 'tasks.list.40.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'mileage',
            quantityForFullProgress: 50,
        },
    }, */
    {
        id: 'job_mining',
        level: levels.high,

        title: 'tasks.list.41.title',
        description: 'tasks.list.41.description',
        bot: { id: 6, name: 'Rudolph Burnett' },
        questType: 'tasks.list.41.questType',

        requirement: {
            title: 'tasks.list.41.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'job_mining',
            quantityForFullProgress: 50,
        },
    },
    {
        id: 'arena_kill20',
        level: levels.medium,

        title: 'tasks.list.42.title',
        description: 'tasks.list.42.description',
        bot: { id: 9, name: 'Beatrice Franks' },
        questType: 'tasks.list.42.questType',

        requirement: {
            title: 'tasks.list.42.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'arena_kill20',
            quantityForFullProgress: 20,
        },
    },
    {
        id: 'arena_kill20PlayerWithRevolver',
        level: levels.medium,

        title: 'tasks.list.43.title',
        description: 'tasks.list.43.description',
        bot: { id: 9, name: 'Beatrice Franks' },
        questType: 'tasks.list.43.questType',

        requirement: {
            title: 'tasks.list.43.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'arena_kill20PlayerWithRevolver',
            quantityForFullProgress: 20,
        },
    },
/*     {
        id: 'test45',
        level: levels.medium,

        title: 'tasks.list.44.title',
        description: 'tasks.list.44.description',
        bot: { id: 2, name: 'Eva Levante' },
        questType: 'tasks.list.44.questType',

        requirement: {
            title: 'tasks.list.44.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'useAutopilot',
            quantityForFullProgress: 1,
        },
    }, */
    /*{
        id: 'task_eat_item',
        level: levels.medium,

        title: 'tasks.list.45.title',
        description: 'tasks.list.45.description',
        bot: { id: 2, name: 'Eva Levante' },
        questType: 'tasks.list.45.questType',

        requirement: {
            title: 'tasks.list.45.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'task_eat_item',
            trigger_value: [599, 600, 601, 602, 603, 604],
            quantityForFullProgress: 5,
        },
    },
    {
        id: 'task_eat_item',
        level: levels.high,

        title: 'tasks.list.46.title',
        description: 'tasks.list.46.description',
        bot: { id: 2, name: 'Eva Levante' },
        questType: 'tasks.list.46.questType',

        requirement: {
            title: 'tasks.list.46.requirement.title',
            //completed: false,
            //progress: 1,
            trigger_type: 'task_eat_item',
            trigger_value: [599, 600, 601, 602, 603, 604],
            quantityForFullProgress: 5,
        },
    }*/
]
