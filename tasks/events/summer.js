import { awardEnum } from '../index';

export const eventConfig = Object.freeze({
    startTime: '6.26.2024 6:0:0',
    disableDelay: 0 // days until disable after event ends
})

export const eventList = [
    {
        day: 1,
        taskId: 'stingrayArticle_1',
        pedId: 367,
        prevTask: '',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 800,
                type: 'seasonPassProgress',
                award: {
                    amount: 800,
                },
            },
            {
                region: 'eu',
                color: 'purple',
                amount: 50,
                type: 'coins',
                award: {
                    amount: 50,
                },
            },
            {
                region: '~eu',
                color: 'purple',
                amount: 1,
                type: 'case',
                award: {
                    case: 'default',
                },
            },
            {
                color: 'purple',
                amount: 800,
                type: 'seasonPassProgress',
                award: {
                    amount: 800,
                },
            },
        ]
    },
    {
        day: 2,
        taskId: 'sunkenPast_1',
        pedId: 370,
        prevTask: 'stingrayArticle_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 800,
                type: 'seasonPassProgress',
                award: {
                    amount: 800,
                },
            },
            {
                color: 'blue',
                amount: 20,
                type: 'item',
                award: {
                    itemId: 805,
                    count: 20
                },
            },
            {
                color: 'purple',
                amount: 800,
                type: 'seasonPassProgress',
                award: {
                    amount: 800,
                },
            },
        ]
    },
    {
        day: 2,
        taskId: 'specialMethod_1',
        pedId: 368,
        prevTask: 'stingrayArticle_1',
        tooltip: 'right bottom',
        rewards: [
            {
                color: 'purple',
                amount: 800,
                type: 'seasonPassProgress',
                award: {
                    amount: 800,
                },
            },
            {
                color: 'gray',
                amount: 10000,
                type: 'item',
                award: {
                    itemId: 873,
                    count: 10000
                },
            },
            {
                color: 'purple',
                amount: 800,
                type: 'seasonPassProgress',
                award: {
                    amount: 800,
                },
            },
        ]
    },
    {
        day: 3,
        taskId: 'vespucciCanals_1',
        pedId: 379,
        prevTask: 'stingrayArticle_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 1000,
                type: 'seasonPassProgress',
                award: {
                    amount: 1000,
                },
            },
            {
                color: 'purple',
                amount: 5,
                type: 'item',
                award: {
                    itemId: 89,
                    count: 5
                },
            },
            {
                color: 'purple',
                amount: 1000,
                type: 'seasonPassProgress',
                award: {
                    amount: 1000,
                },
            },
        ]
    },
    {
        day: 3,
        taskId: 'modernBird_1',
        pedId: 378,
        prevTask: 'stingrayArticle_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
            {
                region: 'eu',
                color: 'purple',
                amount: 50,
                type: 'coins',
                award: {
                    amount: 50,
                },
            },
            {
                region: '~eu',
                color: 'purple',
                amount: 1,
                type: 'case',
                award: {
                    case: 'default',
                },
            },
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
        ]
    },
    {
        day: 3,
        taskId: 'specialMethod_2',
        pedId: 368,
        prevTask: 'specialMethod_1',
        tooltip: 'right bottom',
        rewards: [
            {
                color: 'purple',
                amount: 1000,
                type: 'seasonPassProgress',
                award: {
                    amount: 1000,
                },
            },
            {
                color: 'gray',
                amount: 10000,
                type: 'item',
                award: {
                    itemId: 874,
                    count: 10000
                },
            },
            {
                color: 'purple',
                amount: 1000,
                type: 'seasonPassProgress',
                award: {
                    amount: 1000,
                },
            },
        ]
    },
    {
        day: 4,
        taskId: 'sunkenPast_2',
        pedId: 370,
        prevTask: 'sunkenPast_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 1000,
                type: 'seasonPassProgress',
                award: {
                    amount: 1000,
                },
            },
            {
                color: 'blue',
                amount: 8,
                type: 'item',
                award: {
                    itemId: 806,
                    count: 8
                },
            },
            {
                color: 'purple',
                amount: 1000,
                type: 'seasonPassProgress',
                award: {
                    amount: 1000,
                },
            },
        ]
    },
    {
        day: 4,
        taskId: 'greenMedicine_1',
        pedId: 371,
        prevTask: 'specialMethod_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
            {
                color: 'blue',
                amount: 1,
                type: 'item',
                award: {
                    itemId: 960,
                    count: 1
                },
            },
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
        ]
    },
    {
        day: 4,
        taskId: 'yesSir_1',
        pedId: 373,
        prevTask: 'specialMethod_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 1100,
                type: 'seasonPassProgress',
                award: {
                    amount: 1100,
                },
            },
            {
                color: 'gray',
                amount: 10,
                type: 'item',
                award: {
                    itemId: 334,
                    count: 10
                },
            },
            {
                color: 'purple',
                amount: 1100,
                type: 'seasonPassProgress',
                award: {
                    amount: 1100,
                },
            },
        ]
    },
    {
        day: 5,
        taskId: 'vespucciCanals_2',
        pedId: 379,
        prevTask: 'vespucciCanals_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
            {
                color: 'purple',
                amount: 5,
                type: 'item',
                award: {
                    itemId: 252,
                    count: 5
                },
            },
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
        ]
    },
    {
        day: 5,
        taskId: 'musketeer_1',
        pedId: 374,
        prevTask: 'yesSir_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'red',
                amount: 1400,
                type: 'seasonPassProgress',
                award: {
                    amount: 1400,
                },
            },
            {
                region: 'eu',
                color: 'purple',
                amount: 50,
                type: 'coins',
                award: {
                    amount: 50,
                },
            },
            {
                region: '~eu',
                color: 'purple',
                amount: 1,
                type: 'case',
                award: {
                    case: 'default',
                },
            },
            {
                color: 'red',
                amount: 1400,
                type: 'seasonPassProgress',
                award: {
                    amount: 1400,
                },
            },
        ]
    },
    {
        day: 6,
        taskId: 'sunkenPast_3',
        pedId: 370,
        prevTask: 'sunkenPast_2',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
            {
                color: 'blue',
                amount: 2,
                type: 'item',
                award: {
                    itemId: 542,
                    count: 2
                },
            },
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
        ]
    },
    {
        day: 6,
        taskId: 'stingrayArticle_2',
        pedId: 367,
        prevTask: 'modernBird_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
            {
                color: 'blue',
                amount: 1,
                type: 'item',
                award: {
                    itemId: 734,
                    count: 1
                },
            },
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
        ]
    },
    {
        day: 6,
        taskId: 'yesSir_2',
        pedId: 373,
        prevTask: 'yesSir_1',
        tooltip: 'right bottom',
        rewards: [
            {
                color: 'red',
                amount: 1400,
                type: 'seasonPassProgress',
                award: {
                    amount: 1400,
                },
            },
            {
                color: 'purple',
                amount: 1,
                type: 'item',
                award: {
                    itemId: 525,
                    count: 1
                },
            },
            {
                color: 'red',
                amount: 1400,
                type: 'seasonPassProgress',
                award: {
                    amount: 1400,
                },
            },
        ]
    },
    {
        day: 7,
        taskId: 'killerWhaleVision_1',
        pedId: 371,
        prevTask: 'greenMedicine_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 1000,
                type: 'seasonPassProgress',
                award: {
                    amount: 1000,
                },
            },
            {
                color: 'blue',
                amount: 2,
                type: 'item',
                award: {
                    itemId: 434,
                    count: 2
                },
            },
            {
                color: 'purple',
                amount: 1000,
                type: 'seasonPassProgress',
                award: {
                    amount: 1000,
                },
            },
        ]
    },
    {
        day: 7,
        taskId: 'musketeer_2',
        pedId: 374,
        prevTask: 'musketeer_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'red',
                amount: 1600,
                type: 'seasonPassProgress',
                award: {
                    amount: 1600,
                },
            },
            {
                color: 'blue',
                amount: 1,
                type: 'item',
                award: {
                    itemId: 347,
                    count: 1
                },
            },
            {
                color: 'red',
                amount: 1600,
                type: 'seasonPassProgress',
                award: {
                    amount: 1600,
                },
            },
        ]
    },
    {
        day: 7,
        taskId: 'specialMethod_3',
        pedId: 368,
        prevTask: 'specialMethod_2',
        tooltip: 'right bottom',
        rewards: [
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
            {
                color: 'blue',
                amount: 15000,
                type: 'item',
                award: {
                    itemId: 892,
                    count: 15000
                },
            },
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
        ]
    },
    {
        day: 8,
        taskId: 'sunkenPast_4',
        pedId: 370,
        prevTask: 'sunkenPast_3',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 1300,
                type: 'seasonPassProgress',
                award: {
                    amount: 1300,
                },
            },
            {
                color: 'purple',
                amount: 1,
                type: 'item',
                award: {
                    itemId: 543,
                    count: 1
                },
            },
            {
                color: 'purple',
                amount: 1300,
                type: 'seasonPassProgress',
                award: {
                    amount: 1300,
                },
            },
        ]
    },
    {
        day: 8,
        taskId: 'silence_1',
        pedId: 372,
        prevTask: 'musketeer_1',
        tooltip: 'right bottom',
        rewards: [
            {
                color: 'red',
                amount: 1700,
                type: 'seasonPassProgress',
                award: {
                    amount: 1700,
                },
            },
            {
                region: 'eu',
                color: 'red',
                amount: 200,
                type: 'coins',
                award: {
                    amount: 200,
                },
            },
            {
                region: '~eu',
                color: 'red',
                amount: 1,
                type: 'case',
                award: {
                    case: 'summerExtra2024',
                },
            },
            {
                color: 'red',
                amount: 1700,
                type: 'seasonPassProgress',
                award: {
                    amount: 1700,
                },
            },
        ]
    },
    {
        day: 8,
        taskId: 'yesSir_3',
        pedId: 373,
        prevTask: 'yesSir_2',
        tooltip: 'right bottom',
        rewards: [
            {
                color: 'red',
                amount: 1600,
                type: 'seasonPassProgress',
                award: {
                    amount: 1600,
                },
            },
            {
                color: 'blue',
                amount: 1,
                type: 'item',
                award: {
                    itemId: 732,
                    count: 1
                },
            },
            {
                color: 'red',
                amount: 1600,
                type: 'seasonPassProgress',
                award: {
                    amount: 1600,
                },
            },
        ]
    },
    {
        day: 9,
        taskId: 'vespucciCanals_3',
        pedId: 379,
        prevTask: 'vespucciCanals_2',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
            {
                color: 'purple',
                amount: 5,
                type: 'item',
                award: {
                    itemId: 625,
                    count: 5
                },
            },
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
        ]
    },
    {
        day: 9,
        taskId: 'ownAtmosphere_1',
        pedId: 371,
        prevTask: 'killerWhaleVision_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
            {
                color: 'blue',
                amount: 1,
                type: 'item',
                award: {
                    itemId: 728,
                    count: 1
                },
            },
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
        ]
    },
    {
        day: 9,
        taskId: 'specialMethod_4',
        pedId: 368,
        prevTask: 'specialMethod_3',
        tooltip: 'right bottom',
        rewards: [
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
            {
                color: 'blue',
                amount: 2,
                type: 'item',
                award: {
                    itemId: 950,
                    count: 2
                },
            },
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
        ]
    },
    {
        day: 10,
        taskId: 'sunkenPast_5',
        pedId: 370,
        prevTask: 'sunkenPast_4',
        tooltip: 'left',
        rewards: [
            {
                color: 'purple',
                amount: 1400,
                type: 'seasonPassProgress',
                award: {
                    amount: 1400,
                },
            },
            {
                color: 'purple',
                amount: 1,
                type: 'item',
                award: {
                    itemId: 534,
                    count: 1
                },
            },
            {
                color: 'purple',
                amount: 1400,
                type: 'seasonPassProgress',
                award: {
                    amount: 1400,
                },
            },
        ]
    },
    {
        day: 10,
        taskId: 'killerWhaleVision_2',
        pedId: 371,
        prevTask: 'ownAtmosphere_1',
        tooltip: 'right',
        rewards: [
            {
                color: 'purple',
                amount: 1100,
                type: 'seasonPassProgress',
                award: {
                    amount: 1100,
                },
            },
            {
                color: 'purple',
                amount: 2,
                type: 'item',
                award: {
                    itemId: 470,
                    count: 2
                },
            },
            {
                color: 'purple',
                amount: 1100,
                type: 'seasonPassProgress',
                award: {
                    amount: 1100,
                },
            },
        ]
    },
    {
        day: 10,
        taskId: 'corruptionBoat_1',
        pedId: 369,
        prevTask: 'silence_1',
        tooltip: 'right bottom',
        rewards: [
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
            {
                region: 'eu',
                color: 'red',
                amount: 200,
                type: 'coins',
                award: {
                    amount: 200,
                },
            },
            {
                region: '~eu',
                color: 'red',
                amount: 1,
                type: 'case',
                award: {
                    case: 'vehicles',
                },
            },
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
        ]
    },
    {
        day: 11,
        taskId: 'greenMedicine_2',
        pedId: 371,
        prevTask: 'killerWhaleVision_2',
        tooltip: 'left',
        rewards: [
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
            {
                color: 'red',
                amount: 20,
                type: 'item',
                award: {
                    itemId: 731,
                    count: 20
                },
            },
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
        ]
    },
    {
        day: 11,
        taskId: 'musketeer_3',
        pedId: 374,
        prevTask: 'musketeer_2',
        tooltip: 'left',
        rewards: [
            {
                color: 'red',
                amount: 1700,
                type: 'seasonPassProgress',
                award: {
                    amount: 1700,
                },
            },
            {
                color: 'blue',
                amount: 1,
                type: 'item',
                award: {
                    itemId: 963,
                    count: 1
                },
            },
            {
                color: 'red',
                amount: 1700,
                type: 'seasonPassProgress',
                award: {
                    amount: 1700,
                },
            },
        ]
    },
    {
        day: 12,
        taskId: 'sunkenPast_6',
        pedId: 370,
        prevTask: 'sunkenPast_5',
        tooltip: 'left',
        rewards: [
            {
                color: 'red',
                amount: 1500,
                type: 'seasonPassProgress',
                award: {
                    amount: 1500,
                },
            },
            {
                color: 'purple',
                amount: 1,
                type: 'item',
                award: {
                    itemId: 541,
                    count: 1
                },
            },
            {
                color: 'red',
                amount: 1500,
                type: 'seasonPassProgress',
                award: {
                    amount: 1500,
                },
            },
        ]
    },
    {
        day: 12,
        taskId: 'vespucciCanals_4',
        pedId: 379,
        prevTask: 'vespucciCanals_3',
        tooltip: 'left',
        rewards: [
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
            {
                color: 'purple',
                amount: 3,
                type: 'item',
                award: {
                    itemId: 956,
                    count: 3
                },
            },
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
        ]
    },
    {
        day: 12,
        taskId: 'familyHeirloom_1',
        pedId: 377,
        prevTask: 'corruptionBoat_1',
        tooltip: 'left bottom',
        rewards: [
            {
                color: 'red',
                amount: 1900,
                type: 'seasonPassProgress',
                award: {
                    amount: 1900,
                },
            },
            {
                region: 'eu',
                color: 'purple',
                amount: 200,
                type: 'coins',
                award: {
                    amount: 200,
                },
            },
            {
                region: '~eu',
                color: 'red',
                amount: 1,
                type: 'case',
                award: {
                    case: 'summerVehicles2024',
                },
            },
            {
                color: 'red',
                amount: 1900,
                type: 'seasonPassProgress',
                award: {
                    amount: 1900,
                },
            },
        ]
    },
    {
        day: 13,
        taskId: 'modernBird_2',
        pedId: 378,
        prevTask: 'stingrayArticle_2',
        tooltip: 'left',
        rewards: [
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
        ]
    },
    {
        day: 13,
        taskId: 'killerWhaleVision_3',
        pedId: 371,
        prevTask: 'greenMedicine_2',
        tooltip: 'left',
        rewards: [
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
            {
                color: 'gold',
                type: 'clothes',
                clothesData: {
                    0: {component: 11, drawable: 2452, texture: 1, isProp: 0, gender: 0 },
                    1: {component: 11, drawable: 2458, texture: 1, isProp: 0, gender: 1 }
                }
            },
            {
                color: 'purple',
                amount: 1200,
                type: 'seasonPassProgress',
                award: {
                    amount: 1200,
                },
            },
        ]
    },
    {
        day: 14,
        taskId: 'musketeer_4',
        pedId: 374,
        prevTask: 'musketeer_3',
        tooltip: 'left',
        rewards: [
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
            {
                color: 'gold',
                amount: 1,
                type: 'item',
                award: {
                    itemId: 973,
                    count: 1
                },
            },
            {
                color: 'red',
                amount: 1800,
                type: 'seasonPassProgress',
                award: {
                    amount: 1800,
                },
            },
        ]
    },
    {
        day: 14,
        taskId: 'democracy_1',
        pedId: 375,
        prevTask: 'familyHeirloom_1',
        tooltip: 'left bottom',
        rewards: [
            {
                color: 'gold',
                amount: 2000,
                type: 'seasonPassProgress',
                award: {
                    amount: 2000,
                },
            },
            {
                region: 'eu',
                color: 'gold',
                amount: 300,
                type: 'coins',
                award: {
                    amount: 300,
                },
            },
            {
                region: '~eu',
                color: 'gold',
                amount: 1,
                type: 'case',
                award: {
                    case: 'summer2024',
                },
            },
            {
                color: 'gold',
                amount: 2000,
                type: 'seasonPassProgress',
                award: {
                    amount: 2000,
                },
            },
        ]
    },
]
