import { awardEnum } from '../index';


export const eventList = [
    {
        id: 0,
        title: 'cef.Menu.F4.spring.titles.0',
        description: 'cef.Menu.F4.spring.description.0',
        botName: 'Seed',
        start: ['03/07/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['03/15/2024 23:59:00', 'MM/DD/YYYY HH:mm:ss'],
        startVue: ['02/08/2024 10:00:00', 'MM/dd/yyyy HH:mm:ss'],
        endVue: ['03/15/2024 23:59:00', 'MM/dd/yyyy HH:mm:ss'],
        coolDown: 8,
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.springCoins,
                amount: 11,
            },
            {
                rewardId: 2,
                type: awardEnum.springCoins,
                amount: 11,
            },
            {
                rewardId: 3,
                type: awardEnum.item,
                itemId: 828, // обязательно
                count: 1, // обязательно
            },
            {
                rewardId: 4,
                type: awardEnum.item,
                itemId: 829, // обязательно
                count: 1, // обязательно
            },
        ]
    },
    {
        id: 1,
        title: 'cef.Menu.F4.spring.titles.1',
        description: 'cef.Menu.F4.spring.description.1',
        botName: 'Eric',
        start: ['03/07/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['03/15/2024 23:59:00', 'MM/DD/YYYY HH:mm:ss'],
        startVue: ['02/08/2024 10:00:00', 'MM/dd/yyyy HH:mm:ss'],
        endVue: ['03/15/2024 23:59:00', 'MM/dd/yyyy HH:mm:ss'],
        coolDown: 5,
        rewards: [
            {
                rewardId: 5,
                type: awardEnum.springCoins,
                amount: 6,
            },
            {
                rewardId: 6,
                type: awardEnum.springCoins,
                amount: 6,
            },
            {
                rewardId: 7,
                type: awardEnum.springCoins,
                amount: 6,
            },
            {
                rewardId: 8,
                type: awardEnum.item,
                itemId: 829, // обязательно
                count: 1, // обязательно
            },
        ]
    },
    {
        id: 2,
        title: 'cef.Menu.F4.spring.titles.2',
        description: 'cef.Menu.F4.spring.description.2',
        botName: 'Helen',
        start: ['03/07/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['03/15/2024 23:59:00', 'MM/DD/YYYY HH:mm:ss'],
        startVue: ['02/08/2024 10:00:00', 'MM/dd/yyyy HH:mm:ss'],
        endVue: ['03/15/2024 23:59:00', 'MM/dd/yyyy HH:mm:ss'],
        coolDown: 4,
        rewards: [
            {
                rewardId: 9,
                type: awardEnum.springCoins,
                amount: 4,
            },
            {
                rewardId: 10,
                type: awardEnum.springCoins,
                amount: 4,
            },
            {
                rewardId: 11,
                type: awardEnum.springCoins,
                amount: 4,
            },
            {
                rewardId: 12,
                type: awardEnum.item,
                itemId: 828, // обязательно
                count: 1, // обязательно
            },
        ]
    },
    {
        id: 3,
        title: 'cef.Menu.F4.spring.titles.3',
        description: 'cef.Menu.F4.spring.description.3',
        botName: 'Derek',
        start: ['03/07/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['03/15/2024 23:59:00', 'MM/DD/YYYY HH:mm:ss'],
        startVue: ['02/08/2024 10:00:00', 'MM/dd/yyyy HH:mm:ss'],
        endVue: ['03/15/2024 23:59:00', 'MM/dd/yyyy HH:mm:ss'],
        coolDown: 6,
        rewards: [
            {
                rewardId: 13,
                type: awardEnum.springCoins,
                amount: 6,
            },
            {
                rewardId: 14,
                type: awardEnum.springCoins,
                amount: 6,
            },
            {
                rewardId: 15,
                type: awardEnum.springCoins,
                amount: 6,
            },
            {
                rewardId: 16,
                type: awardEnum.item,
                itemId: 829, // обязательно
                count: 1, // обязательно
            },
        ]
    },
    {
        id: 4,
        title: 'cef.Menu.F4.spring.titles.4',
        description: 'cef.Menu.F4.spring.description.4',
        botName: 'Luna',
        start: ['03/07/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['03/15/2024 23:59:00', 'MM/DD/YYYY HH:mm:ss'],
        startVue: ['02/08/2024 10:00:00', 'MM/dd/yyyy HH:mm:ss'],
        endVue: ['03/15/2024 23:59:00', 'MM/dd/yyyy HH:mm:ss'],
        coolDown: 4,
        rewards: [
            {
                rewardId: 17,
                type: awardEnum.springCoins,
                amount: 4,
            },
            {
                rewardId: 18,
                type: awardEnum.springCoins,
                amount: 4,
            },
            {
                rewardId: 19,
                type: awardEnum.springCoins,
                amount: 4,
            },
            {
                rewardId: 20,
                type: awardEnum.item,
                itemId: 828, // обязательно
                count: 1, // обязательно
            },
        ]
    }
];
