import { awardEnum, colorsType } from '../index';

export const list = [
    {
        price: 50,
        result: 100
    },
    {
        price: 100,
        result: 200
    },
    {
        price: 1000,
        result: 2200,
        bonus: 10,
    },
    {
        price: 5000,
        result: 11500,
        bonus: 15,
    },
    {
        price: 20000,
        result: 50000,
        bonus: 25
    },
    {
        price: 30000,
        result: 90000,
        bonus: 50,
    },
]


/*export const eventList = [

    {
    },
    {
        start: [`10/11/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`10/13/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    },
    {
        start: [`10/13/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`10/15/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    },
    {
        start: [`10/15/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`10/17/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    },
    {
        start: [`10/17/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`10/19/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    },
    {
        start: [`10/19/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`10/21/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    },
]*/

const dayStart = 28;
export const eventList = [
    {
        id: 0,
        title: 'cef.Menu.F4.halloween.dayTitles.0',
        start: [`10/31/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`11/16/2024 23:59:59`, 'MM/DD/YYYY HH:mm:ss'],
        taskName: 'photoLocation',
        coolDown: 6,
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.halloweenCrystals,
                value: 850,
                hasOneReceipt: true
            },
            {
                rewardId: 2,
                type: awardEnum.halloweenCrystals,
                value: 850,
                hasOneReceipt: true
            },
            {
                rewardId: 3,
                type: awardEnum.halloweenCrystals,
                value: 200,
            },
            {
                rewardId: 4,
                type: awardEnum.halloweenCrystals,
                value: 200,
            }
        ]
    },

    {
        id: 1,
        title: 'cef.Menu.F4.halloween.dayTitles.1',
        start: [`11/01/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`11/16/2024 23:59:59`, 'MM/DD/YYYY HH:mm:ss'],
        taskName: 'vampireManor',
        coolDown: 8,
        rewards: [
            {
                rewardId: 5,
                type: awardEnum.item, itemId: 327,
                count: 0,
                hasOneReceipt: true
            },
            {
                rewardId: 6,
                type: awardEnum.halloweenCrystals,
                value: 1200,
                hasOneReceipt: true
            },
            {
                rewardId: 7,
                type: awardEnum.halloweenCrystals,
                value: 225,
            },
            {
                rewardId: 8,
                type: awardEnum.halloweenCrystals,
                value: 225,
            }
        ]
    },

    {
        id: 2,
        title: 'cef.Menu.F4.halloween.dayTitles.2',
        start: [`11/02/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`11/16/2024 23:59:59`, 'MM/DD/YYYY HH:mm:ss'],
        taskName: 'mining',
        coolDown: 10,
        rewards: [
            {
                rewardId: 9,
                type: awardEnum.case, case: 'halloween2023',
                hasOneReceipt: true
            },
            {
                rewardId: 10,
                type: awardEnum.halloweenCrystals,
                value: 1000,
                hasOneReceipt: true
            },
            {
                rewardId: 11,
                type: awardEnum.halloweenCrystals,
                value: 150,
            },
            {
                rewardId: 12,
                type: awardEnum.halloweenCrystals,
                value: 150,
            }
        ]
    },

    {
        id: 3,
        title: 'cef.Menu.F4.halloween.dayTitles.3',
        start: [`11/03/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`11/16/2024 23:59:59`, 'MM/DD/YYYY HH:mm:ss'],
        taskName: 'protectionMonument',
        coolDown: 8,
        rewards: [
            {
                rewardId: 13,
                type: awardEnum.item, itemId: 985,
                count: 0,
                hasOneReceipt: true
            },
            {
                rewardId: 14,
                type: awardEnum.halloweenCrystals,
                value: 1400,
                hasOneReceipt: true
            },
            {
                rewardId: 15,
                type: awardEnum.halloweenCrystals,
                value: 250,
            },
            {
                rewardId: 16,
                type: awardEnum.halloweenCrystals,
                value: 250,
            }
        ]
    },

    {
        id: 4,
        title: 'cef.Menu.F4.halloween.dayTitles.4',
        start: [`11/04/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`11/16/2024 23:59:59`, 'MM/DD/YYYY HH:mm:ss'],
        taskName: 'graffitiSearch',
        coolDown: 8,
        rewards: [
            {
                rewardId: 17,
                type: awardEnum.halloweenCrystals,
                value: 1100,
                hasOneReceipt: true
            },
            {
                rewardId: 18,
                type: awardEnum.item, itemId: 409,
                count: 50,
                hasOneReceipt: true
            },
            {
                rewardId: 19,
                type: awardEnum.halloweenCrystals,
                value: 175,
            },
            {
                rewardId: 20,
                type: awardEnum.halloweenCrystals,
                value: 175,
            }
        ]
    },

    {
        id: 5,
        title: 'cef.Menu.F4.halloween.dayTitles.5',
        start: [`11/05/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`11/16/2024 23:59:59`, 'MM/DD/YYYY HH:mm:ss'],
        taskName: 'alchemy',
        coolDown: 12,
        rewards: [
            {
                rewardId: 21,
                type: awardEnum.halloweenCrystals,
                value: 1000,
                hasOneReceipt: true
            },
            {
                rewardId: 22,
                type: awardEnum.item, itemId: 437,
                count: 3,
                hasOneReceipt: true
            },
            {
                rewardId: 23,
                type: awardEnum.halloweenCrystals,
                value: 150,
            },
            {
                rewardId: 24,
                type: awardEnum.halloweenCrystals,
                value: 150,
            }
        ]
    },

    {
        id: 6,
        title: 'cef.Menu.F4.halloween.dayTitles.6',
        start: [`11/06/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`11/16/2024 23:59:59`, 'MM/DD/YYYY HH:mm:ss'],
        taskName: 'tractor',
        coolDown: 10,
        rewards: [
            {
                rewardId: 25,
                type: awardEnum.halloweenCrystals,
                value: 1300,
                hasOneReceipt: true
            },
            {
                rewardId: 26,
                type: awardEnum.item, itemId: 11,
                count: 0,
                hasOneReceipt: true
            },
            {
                rewardId: 27,
                type: awardEnum.halloweenCrystals,
                value: 200,
            },
            {
                rewardId: 28,
                type: awardEnum.halloweenCrystals,
                value: 200,
            }
        ]
    },

    {
        id: 7,
        title: 'cef.Menu.F4.halloween.dayTitles.7',
        start: [`11/07/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`11/16/2024 23:59:59`, 'MM/DD/YYYY HH:mm:ss'],
        taskName: 'newspaperDelivery',
        coolDown: 10,
        rewards: [
            {
                rewardId: 29,
                type: awardEnum.halloweenCrystals,
                value: 1100,
                hasOneReceipt: true
            },
            {
                rewardId: 30,
                type: awardEnum.item, itemId: 726,
                count: 3,
                hasOneReceipt: true
            },
            {
                rewardId: 31,
                type: awardEnum.halloweenCrystals,
                value: 150,
            },
            {
                rewardId: 32,
                type: awardEnum.halloweenCrystals,
                value: 150,
            }
        ]
    },

    {
        id: 8,
        title: 'cef.Menu.F4.halloween.dayTitles.8',
        start: [`11/08/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`11/16/2024 23:59:59`, 'MM/DD/YYYY HH:mm:ss'],
        taskName: 'escape',
        coolDown: 10,
        rewards: [
            {
                rewardId: 33,
                type: awardEnum.halloweenCrystals,
                value: 1400,
                hasOneReceipt: true
            },
            {
                rewardId: 34,
                type: awardEnum.item, itemId: 331,
                count: 0,
                hasOneReceipt: true
            },
            {
                rewardId: 35,
                type: awardEnum.halloweenCrystals,
                value: 175,
            },
            {
                rewardId: 36,
                type: awardEnum.halloweenCrystals,
                value: 175,
            }
        ]
    },

    {
        id: 9,
        title: 'cef.Menu.F4.halloween.dayTitles.9',
        start: [`11/09/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
        end: [`11/16/2024 23:59:59`, 'MM/DD/YYYY HH:mm:ss'],
        taskName: 'bossfight',
        coolDown: 12,
        rewards: [
            {
                rewardId: 37,
                type: awardEnum.halloweenCrystals,
                value: 1500,
                hasOneReceipt: true
            },
            {
                rewardId: 38,
                type: awardEnum.halloweenCrystals,
                value: 1500,
                hasOneReceipt: true
            },
            {
                rewardId: 39,
                type: awardEnum.halloweenCrystals,
                value: 500,
            },
            {
                rewardId: 40,
                type: awardEnum.halloweenCrystals,
                value: 500,
            }
        ]
    },

    // {
    //     id: 3,
    //     title: 'cef.Menu.F4.halloween.dayTitles.3',
    //     start: [`10/${dayStart + 6}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     end: [`10/${dayStart + 8}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     questId: 36,
    //     rewards: [
    //         {
    //             rewardId: 9,
    //             type: awardEnum.money,
    //             amount: 15000,
    //         },
    //         {
    //             rewardId: 10,
    //             type: awardEnum.halloweenCrystals,
    //             value: 800,
    //             hasDay: true
    //         }
    //     ]
    // },

    // {
    //     id: 4,
    //     title: 'cef.Menu.F4.halloween.dayTitles.4',
    //     start: [`10/${dayStart + 8}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     end: [`10/${dayStart + 10}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     questId: 35,
    //     rewards: [
    //         {
    //             rewardId: 11,
    //             type: awardEnum.item, itemId: 333,
    //             count: 1,
    //         },
    //         {
    //             rewardId: 12,
    //             type: awardEnum.halloweenCrystals,
    //             value: 600,
    //             hasDay: true
    //         }
    //     ]
    // },

    // {
    //     id: 5,
    //     title: 'cef.Menu.F4.halloween.dayTitles.5',
    //     start: [`10/${dayStart + 10}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     end: [`10/${dayStart + 12}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     questId: 65,
    //     rewards: [
    //         {
    //             rewardId: 13,
    //             type: awardEnum.item, itemId: 808,
    //             count: 1,
    //         },
    //         {
    //             rewardId: 14,
    //             type: awardEnum.halloweenCrystals,
    //             value: 800,
    //             hasDay: true
    //         }
    //     ]
    // },

    // {
    //     id: 6,
    //     title: 'cef.Menu.F4.halloween.dayTitles.6',
    //     start: [`10/${dayStart + 12}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     end: [`10/${dayStart + 14}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     questId: 66,
    //     rewards: [
    //         {
    //             rewardId: 15,
    //             type: awardEnum.halloweenCrystals,
    //             value: 300,
    //         },
    //         {
    //             rewardId: 16,
    //             type: awardEnum.halloweenCrystals,
    //             value: 500,
    //             hasDay: true
    //         }
    //     ]
    // },

    // {
    //     id: 7,
    //     title: 'cef.Menu.F4.halloween.dayTitles.7',
    //     start: [`10/${dayStart + 14}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     end: [`10/${dayStart + 16}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     questId: 63,
    //     rewards: [
    //         {
    //             rewardId: 17,
    //             type: awardEnum.item, itemId: 733,
    //             count: 10,
    //         },
    //         {
    //             rewardId: 18,
    //             type: awardEnum.halloweenCrystals,
    //             value: 1000,
    //             hasDay: true
    //         }
    //     ]
    // },

    // {
    //     id: 8,
    //     title: 'cef.Menu.F4.halloween.dayTitles.8',
    //     start: [`10/${dayStart + 16}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     end: [`10/${dayStart + 18}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     questId: 67,
    //     rewards: [
    //         {
    //             rewardId: 19,
    //             type: awardEnum.coins,
    //             amount: 100,
    //         },
    //         {
    //             rewardId: 20,
    //             type: awardEnum.halloweenCrystals,
    //             value: 600,
    //             hasDay: true
    //         }
    //     ]
    // },

    // {
    //     id: 9,
    //     title: 'cef.Menu.F4.halloween.dayTitles.9',
    //     start: [`10/${dayStart + 18}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     end: [`10/${dayStart + 20}/2024 10:00:00`, 'MM/DD/YYYY HH:mm:ss'],
    //     questId: 68,
    //     rewards: [
    //         {
    //             rewardId: 21,
    //             type: awardEnum.halloweenCrystals,
    //             value: 1500,
    //         },
    //         {
    //             rewardId: 22,
    //             type: awardEnum.halloweenCrystals,
    //             value: 1500,
    //             hasDay: true
    //         }
    //     ]
    // }
]
