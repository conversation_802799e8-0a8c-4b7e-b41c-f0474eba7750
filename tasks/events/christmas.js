import { awardEnum, colorsType } from '../index';


export const eventList = [
    {
        id: 0,
        title: 'cef.Menu.F4.christmas.titles.0',
        description: 'cef.Menu.F4.christmas.description.0',
        start: ['12/30/2023 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['12/31/2023 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 6,
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 100,
            },
            {
                rewardId: 2,
                type: awardEnum.seasonPassProgress,
                amount: 100
            },
            {
                rewardId: 3,
                type: awardEnum.seasonPassProgress,
                amount: 100
            },
            {
                rewardId: 4,
                type: awardEnum.seasonPassProgress,
                amount: 100
            },
        ]
    },
    {
        id: 1,
        title: 'cef.Menu.F4.christmas.titles.1',
        description: 'cef.Menu.F4.christmas.description.1',
        start: ['12/31/2023 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/01/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 6,
        rewards: [
            {
                rewardId: 5,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
            {
                rewardId: 6,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
            {
                rewardId: 7,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
            {
                rewardId: 8,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
        ]
    },
    {
        id: 2,
        title: 'cef.Menu.F4.christmas.titles.2',
        description: 'cef.Menu.F4.christmas.description.2',
        start: ['01/01/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/02/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 6,
        rewards: [
            {
                rewardId: 9,
                type: awardEnum.seasonPassProgress,
                amount: 150,
            },
            {
                rewardId: 10,
                type: awardEnum.seasonPassProgress,
                amount: 150,
            },
            {
                rewardId: 11,
                type: awardEnum.seasonPassProgress,
                amount: 150,
            },
            {
                rewardId: 12,
                type: awardEnum.seasonPassProgress,
                amount: 150,
            },
        ]
    },
    {
        id: 3,
        title: 'cef.Menu.F4.christmas.titles.3',
        description: 'cef.Menu.F4.christmas.description.3',
        start: ['01/02/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/03/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 4,
        rewards: [
            {
                rewardId: 13,
                type: awardEnum.seasonPassProgress,
                amount: 100,
            },
            {
                rewardId: 14,
                type: awardEnum.seasonPassProgress,
                amount: 100,
            },
            {
                rewardId: 15,
                type: awardEnum.seasonPassProgress,
                amount: 100,
            },
            {
                rewardId: 16,
                type: awardEnum.seasonPassProgress,
                amount: 100,
            },
        ]
    },
    {
        id: 4,
        title: 'cef.Menu.F4.christmas.titles.4',
        description: 'cef.Menu.F4.christmas.description.4',
        start: ['01/03/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/04/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 8,
        rewards: [
            {
                rewardId: 17,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
            {
                rewardId: 18,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
            {
                rewardId: 19,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
            {
                rewardId: 20,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
        ]
    },
    {
        id: 5,
        title: 'cef.Menu.F4.christmas.titles.5',
        description: 'cef.Menu.F4.christmas.description.5',
        start: ['01/04/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/05/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 4,
        rewards: [
            {
                rewardId: 21,
                type: awardEnum.seasonPassProgress,
                amount: 50,
            },
            {
                rewardId: 22,
                type: awardEnum.seasonPassProgress,
                amount: 50,
            },
            {
                rewardId: 23,
                type: awardEnum.seasonPassProgress,
                amount: 50,
            },
            {
                rewardId: 24,
                type: awardEnum.seasonPassProgress,
                amount: 50,
            },
        ]
    },
    {
        id: 6,
        title: 'cef.Menu.F4.christmas.titles.6',
        description: 'cef.Menu.F4.christmas.description.6',
        start: ['01/05/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/06/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 8,
        rewards: [
            {
                rewardId: 25,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 26,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 27,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 28,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
        ]
    },
    {
        id: 7,
        title: 'cef.Menu.F4.christmas.titles.7',
        description: 'cef.Menu.F4.christmas.description.7',
        start: ['01/06/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/07/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 4,
        rewards: [
            {
                rewardId: 29,
                type: awardEnum.seasonPassProgress,
                amount: 100,
            },
            {
                rewardId: 30,
                type: awardEnum.seasonPassProgress,
                amount: 100,
            },
            {
                rewardId: 31,
                type: awardEnum.seasonPassProgress,
                amount: 100,
            },
            {
                rewardId: 32,
                type: awardEnum.seasonPassProgress,
                amount: 100,
            },
        ]
    },
    {
        id: 8,
        title: 'cef.Menu.F4.christmas.titles.8',
        description: 'cef.Menu.F4.christmas.description.8',
        start: ['01/07/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/08/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 8,
        rewards: [
            {
                rewardId: 33,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 34,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 35,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 36,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
        ]
    },
    {
        id: 9,
        title: 'cef.Menu.F4.christmas.titles.9',
        description: 'cef.Menu.F4.christmas.description.9',
        start: ['01/08/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/09/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 4,
        rewards: [
            {
                rewardId: 37,
                type: awardEnum.seasonPassProgress,
                amount: 50,
            },
            {
                rewardId: 38,
                type: awardEnum.seasonPassProgress,
                amount: 50,
            },
            {
                rewardId: 39,
                type: awardEnum.seasonPassProgress,
                amount: 50,
            },
            {
                rewardId: 40,
                type: awardEnum.seasonPassProgress,
                amount: 50,
            },
        ]
    },
    {
        id: 10,
        title: 'cef.Menu.F4.christmas.titles.10',
        description: 'cef.Menu.F4.christmas.description.10',
        start: ['01/09/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/10/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 8,
        rewards: [
            {
                rewardId: 41,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 42,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 43,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 44,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
        ]
    },
    {
        id: 11,
        title: 'cef.Menu.F4.christmas.titles.11',
        description: 'cef.Menu.F4.christmas.description.11',
        start: ['01/10/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/11/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 6,
        rewards: [
            {
                rewardId: 45,
                type: awardEnum.seasonPassProgress,
                amount: 150,
            },
            {
                rewardId: 46,
                type: awardEnum.seasonPassProgress,
                amount: 150,
            },
            {
                rewardId: 47,
                type: awardEnum.seasonPassProgress,
                amount: 150,
            },
            {
                rewardId: 48,
                type: awardEnum.seasonPassProgress,
                amount: 150,
            },
        ]
    },
    {
        id: 12,
        title: 'cef.Menu.F4.christmas.titles.12',
        description: 'cef.Menu.F4.christmas.description.12',
        start: ['01/11/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/12/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 8,
        rewards: [
            {
                rewardId: 49,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 50,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 51,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
            {
                rewardId: 52,
                type: awardEnum.seasonPassProgress,
                amount: 250,
            },
        ]
    },
    {
        id: 13,
        title: 'cef.Menu.F4.christmas.titles.13',
        description: 'cef.Menu.F4.christmas.description.13',
        start: ['01/12/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/13/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 8,
        rewards: [
            {
                rewardId: 53,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
            {
                rewardId: 54,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
            {
                rewardId: 55,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
            {
                rewardId: 56,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
        ]
    },
    {
        id: 14,
        title: 'cef.Menu.F4.christmas.titles.14',
        description: 'cef.Menu.F4.christmas.description.14',
        start: ['01/13/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/14/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        coolDown: 8,
        rewards: [
            {
                rewardId: 57,
                type: awardEnum.seasonPassProgress,
                amount: 750,
            },
            {
                rewardId: 58,
                type: awardEnum.seasonPassProgress,
                amount: 750,
            },
            {
                rewardId: 59,
                type: awardEnum.seasonPassProgress,
                amount: 750,
            },
            {
                rewardId: 60,
                type: awardEnum.seasonPassProgress,
                amount: 750,
            },
        ]
    },

]


/*export const eventList = [
    {
        id: 0,
        title: 'cef.Menu.F4.christmas.titles.0',
        description: 'cef.Menu.F4.christmas.description.0',
        start: ['12/30/2023 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['12/31/2023 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33,
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            },
            {
                rewardId: 2,
                type: awardEnum.seasonPassProgress,
                amount: 200
            },
            {
                rewardId: 3,
                type: awardEnum.seasonPassProgress,
                amount: 200
            }
        ]
    },
    {
        id: 1,
        title: 'cef.Menu.F4.christmas.titles.1',
        description: 'cef.Menu.F4.christmas.description.1',
        start: ['12/31/2023 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/01/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Загадочная шалость
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 500,
            }
        ]
    },
    {
        id: 2,
        title: 'cef.Menu.F4.christmas.titles.2',
        description: 'cef.Menu.F4.christmas.description.2',
        start: ['01/01/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/02/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Неизвестность (Снайпер)
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 600,
            }
        ]
    },
    {
        id: 3,
        title: 'cef.Menu.F4.christmas.titles.3',
        description: 'cef.Menu.F4.christmas.description.3',
        start: ['01/02/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/03/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Неожиданный сбой (Вернуть эльфов к работе)
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 400,
            }
        ]
    },
    {
        id: 4,
        title: 'cef.Menu.F4.christmas.titles.4',
        description: 'cef.Menu.F4.christmas.description.4',
        start: ['01/03/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/04/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Новый вид жизни (Поиск кристаллов)
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 600,
            }
        ]
    },
    {
        id: 5,
        title: 'cef.Menu.F4.christmas.titles.5',
        description: 'cef.Menu.F4.christmas.description.5',
        start: ['01/04/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/05/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Необходимый комплект (3 в ряд)
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            }
        ]
    },
    {
        id: 6,
        title: 'cef.Menu.F4.christmas.titles.6',
        description: 'cef.Menu.F4.christmas.description.6',
        start: ['01/05/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/06/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Потеря сознания
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 800,
            }
        ]
    },
    {
        id: 7,
        title: 'cef.Menu.F4.christmas.titles.7',
        description: 'cef.Menu.F4.christmas.description.7',
        start: ['01/06/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/07/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Принуждение (заставить эльфов работать)
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 400,
            }
        ]
    },
    {
        id: 8,
        title: 'cef.Menu.F4.christmas.titles.8',
        description: 'cef.Menu.F4.christmas.description.8',
        start: ['01/07/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/08/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Наступление
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 800,
            }
        ]
    },
    {
        id: 9,
        title: 'cef.Menu.F4.christmas.titles.9',
        description: 'cef.Menu.F4.christmas.description.9',
        start: ['01/08/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/09/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Потеря памяти (пары)
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 200,
            }
        ]
    },
    {
        id: 10,
        title: 'cef.Menu.F4.christmas.titles.10',
        description: 'cef.Menu.F4.christmas.description.10',
        start: ['01/09/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/10/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Срочный рейс
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 600,
            }
        ]
    },
    {
        id: 11,
        title: 'cef.Menu.F4.christmas.titles.11',
        description: 'cef.Menu.F4.christmas.description.11',
        start: ['01/10/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/11/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Сбор подарков
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 500,
            }
        ]
    },
    {
        id: 12,
        title: 'cef.Menu.F4.christmas.titles.12',
        description: 'cef.Menu.F4.christmas.description.12',
        start: ['01/11/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/12/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Похищение
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 1000,
            }
        ]
    },
    {
        id: 13,
        title: 'cef.Menu.F4.christmas.titles.13',
        description: 'cef.Menu.F4.christmas.description.13',
        start: ['01/12/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/13/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Срочный рейс
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 2000,
            }
        ]
    },
    {
        id: 14,
        title: 'cef.Menu.F4.christmas.titles.14',
        description: 'cef.Menu.F4.christmas.description.14',
        start: ['01/13/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        end: ['01/14/2024 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
        questId: 33, // Побег
        rewards: [
            {
                rewardId: 1,
                type: awardEnum.seasonPassProgress,
                amount: 4000,
            }
        ]
    },

]*/
