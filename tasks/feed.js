import { awardEnum, colorsType } from './index';

export const mouthDayCount = [
    31,//0
    28,//1
    31,//2
    30,//3
    31,//4
    30,//5
    31,//6
    31,//7
    30,//8
    31,//9
    30,//10
    31//11
]

export const leapMouthDayCount = [
    31,//0
    29,//1
    31,//2
    30,//3
    31,//4
    30,//5
    31,//6
    31,//7
    30,//8
    31,//9
    30,//10
    31//11
]

export const leapYears = [ 2024, 2028, 2032, 2036, 2040, 2044, 2064 ]

export const list = [
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 200 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 337, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 250 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.red, type: awardEnum.coins, amount: 500 } , // КОНЕЦ  ЯНВАРЯ
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 337, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.red, type: awardEnum.item, itemId: 725, count: 1 } , // КОНЕЦ  ФЕВРАЛЯ
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'vehicles' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 333, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.red, type: awardEnum.case, case: 'vehicles' } , // КОНЕЦ  МАРТА
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 333, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } , 
	{ color: colorsType.red, type: awardEnum.item, itemId: 279, count: 0 } , // КОНЕЦ  АПРЕЛЯ
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 250 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 333, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.red, type: awardEnum.item, itemId: 281, count: 1 } , // КОНЕЦ  МАЯ
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'vehicles' } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 337, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.red, type: awardEnum.coins, amount: 600 } , // КОНЕЦ  ИЮНЯ
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 300 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'vehicles' } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.red, type: awardEnum.coins, amount: 600 } ,  // КОНЕЦ ИЮЛЯ
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 728, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 3 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.red, type: awardEnum.case, case: 'vehicles' } , // КОНЕЦ  АВГУСТА
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'vehicles' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 200 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 333, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.red, type: awardEnum.item, itemId: 281, count: 1 } , // КОНЕЦ  СЕНТЯБРЯ	
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 300 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 728, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.red, type: awardEnum.coins, amount: 600 } , // КОНЕЦ  ОКТЯБРЯ
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 728, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 333, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.red, type: awardEnum.case, case: 'vehicles' } , // КОНЕЦ  НОЯБРЯ
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 337, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 974, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.green, type: awardEnum.vehicle, model: 'mustang4' } , // КОНЕЦ  ДЕКАБРЯ	
]

export const leapList = [
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'vehicles' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 200 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 333, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.red, type: awardEnum.coins, amount: 600 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 300 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 728, count: 3 } ,
	{ color: colorsType.red, type: awardEnum.item, itemId: 281, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 337, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.red, type: awardEnum.item, itemId: 279, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'vehicles' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 728, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 333, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.red, type: awardEnum.case, case: 'vehicles' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 200 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 337, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 250 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.red, type: awardEnum.coins, amount: 500 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 337, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 728, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.red, type: awardEnum.item, itemId: 725, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'vehicles' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 333, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.red, type: awardEnum.coins, amount: 600 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 333, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.red, type: awardEnum.item, itemId: 725, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'vehicles' } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 337, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.red, type: awardEnum.coins, amount: 600 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 7500 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 250 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 333, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.red, type: awardEnum.coins, amount: 600 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.blue, type: awardEnum.coins, amount: 300 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 5000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 326, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 75 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.case, case: 'vehicles' } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.red, type: awardEnum.item, itemId: 281, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 737, count: 6 } ,
	{ color: colorsType.blue, type: awardEnum.money, amount: 50000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 735, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 726, count: 8 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.coins, amount: 50 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 625, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 334, count: 3 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 347, count: 1, condition: 100 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 732, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.money, amount: 10000 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 263, count: 0 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 733, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 525, count: 1 } ,
	{ color: colorsType.gray, type: awardEnum.case, case: 'default' } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 89, count: 3 } ,
	{ color: colorsType.blue, type: awardEnum.item, itemId: 731, count: 6 } ,
	{ color: colorsType.gray, type: awardEnum.item, itemId: 727, count: 25 } ,
	{ color: colorsType.red, type: awardEnum.coins, amount: 1000 } ,
	{ color: colorsType.green, type: awardEnum.vehicle, model: 'mustang4' } ,
]

/*
Скрипт рандомизатор
const getItem = (items) => {
    return items[Math.floor(Math.random() * items.length)]
}

for(let i = 1; i <= 365; i++) {
    if (i % 360 === 0) {
        console.log({ id: i, color: colorsType.green }, ',')
    } else if (i % 90 === 0) {
        console.log({ id: i, color: colorsType.gold, type: awardEnum.item, itemId: 334, count: 3 }, ',')
    } else if (i % 30 === 0) {
        console.log({ id: i, color: colorsType.red, type: awardEnum.item, itemId: 334, count: 3 }, ',')
    } else if (i % 7 === 0) {
        const item = getItem([
            { type: awardEnum.money, amount: 50000 },
            { type: awardEnum.item, itemId: 625, count: 10 },
            { type: awardEnum.case, case: 'vehicles' },
            { type: awardEnum.case, case: 'default' },
            { type: awardEnum.coins, amount: 300 },
            { type: awardEnum.coins, amount: 400 },
            { type: awardEnum.coins, amount: 500 },
        ]);

        console.log({ id: i, color: colorsType.blue, ...item }, ',')
    } else {
        const item = getItem([
            { type: awardEnum.money, amount: 5000 },
            { type: awardEnum.money, amount: 7500 },
            { type: awardEnum.money, amount: 10000 },
            { type: awardEnum.item, itemId: 334, count: 3 },
            { type: awardEnum.case, case: 'default' },
            { type: awardEnum.coins, amount: 50 },
            { type: awardEnum.item, itemId: 89, count: 3 },
            { type: awardEnum.item, itemId: 347, count: 1 },
        ]);

        console.log({ id: i, color: colorsType.gray, ...item }, ',')
    }
}

Учитывая день в текущем месяце

const getItem = (items) => {
    return items[Math.floor(Math.random() * items.length)];
}

let currentMonth = 0;
let currentDayInMonth = 1;

for (let i = 1; i <= 365; i++) {
    if (currentDayInMonth % 7 === 0) {
        const item = getItem([
            { type: awardEnum.case, case: 'default' },
            { type: awardEnum.item, itemId: 337, count: 0 },
            { type: awardEnum.item, itemId: 625, count: 6 },
            { type: awardEnum.item, itemId: 625, count: 6 },
            { type: awardEnum.item, itemId: 333, count: 0 },
            { type: awardEnum.item, itemId: 333, count: 0 },
            { type: awardEnum.item, itemId: 728, count: 3 },
            { type: awardEnum.item, itemId: 731, count: 6 },
            { type: awardEnum.item, itemId: 731, count: 6 },
            { type: awardEnum.coins, amount: 200 },
            { type: awardEnum.coins, amount: 250 },
            { type: awardEnum.coins, amount: 300 },
        ]);

        console.log({ id: i, color: colorsType.blue, ...item }, ',')
    } else if (currentDayInMonth === mouthDayCount[currentMonth]) {
        const item = getItem([
        { type: awardEnum.item, itemId: 725, count: 1 },
        { type: awardEnum.item, itemId: 281, count: 1 },
        { type: awardEnum.coins, amount: 600 },
        { type: awardEnum.coins, amount: 500 },
        { type: awardEnum.item, itemId: 279, count: 0 },
        { type: awardEnum.item, itemId: 725, count: 1 },
        { type: awardEnum.item, itemId: 281, count: 1 },
        { type: awardEnum.coins, amount: 600 },
        { type: awardEnum.coins, amount: 500 },
        { type: awardEnum.item, itemId: 279, count: 0 },
        { type: awardEnum.case, case: 'vehicles' },
        ]);

        console.log({ id: i, color: colorsType.red, ...item }, ',')
    } else {
        const item = getItem([
          { type: awardEnum.item, itemId: 89, count: 6 },
		  { type: awardEnum.item, itemId: 737, count: 6 },
		  { type: awardEnum.coins, amount: 50 },
		  { type: awardEnum.coins, amount: 75 },
		  { type: awardEnum.item, itemId: 727, count: 25 },
		  { type: awardEnum.money, amount: 7500 },
		  { type: awardEnum.item, itemId: 334, count: 3 },
		  { type: awardEnum.item, itemId: 347, count: 1 },
		  { type: awardEnum.item, itemId: 726, count: 8 },
		  { type: awardEnum.money, amount: 10000 },
		  { type: awardEnum.item, itemId: 733, count: 6 },
		  { type: awardEnum.money, amount: 5000 },
		  { type: awardEnum.item, itemId: 89, count: 3 },
		  { type: awardEnum.item, itemId: 525, count: 1 },
		  { type: awardEnum.item, itemId: 732, count: 1 },
		  { type: awardEnum.case, case: 'default' },
		  { type: awardEnum.item, itemId: 263, count: 0 },
		  { type: awardEnum.item, itemId: 735, count: 1 },
		  { type: awardEnum.item, itemId: 326, count: 0 },
        ]);

        console.log({ id: i, color: colorsType.gray, ...item }, ',')
    }

    currentDayInMonth++;
    if (currentDayInMonth > mouthDayCount[currentMonth]) {
        currentMonth = (currentMonth + 1) % 12;
        currentDayInMonth = 1;
    }
}

 */
