import { awardEnum, colorsType } from './index';

export const categoriesType = {
    allTypes: 'allTypes',
    org: 'org',
    civil: 'civil',
    fun: 'fun',
    fin: 'fin'
}

export const categories = [
    {
        id: categoriesType.allTypes,
        title: 'achievements.allList',
        difficulty: 0,
    },
    {
        id: categoriesType.civil,
        title: 'achievements.general',
        difficulty: 1,
    },
    {
        id: categoriesType.fun,
        title: 'achievements.fun',
        difficulty: 2,
    },
    {
        id: categoriesType.fin,
        title: 'achievements.fin',
        difficulty: 2,
    },
    {
        id: categoriesType.org,
        title: 'achievements.org',
        difficulty: 3,
    },
];

export const list = [
    {
        id: 'payDay0104',
        // clientTrigger: true,
        byQuest: categoriesType.civil,

        title: 'achievements.achievementsTask1',
        additionalTitle: 'achievements.achievementsTask2',
        progress: 20,

        award: {
            type: awardEnum.item, itemId: 525, count: 3 , // BIOLINK

            color: colorsType.blue,
        }
    },
    {
        id: 'payDay0508',
        byQuest: categoriesType.civil,

        title: 'achievements.achievementsTask3',
        additionalTitle: 'achievements.achievementsTask4',
        progress: 20,

        award: {
            type: awardEnum.item, itemId: 525, count: 3 , // BIOLINK
            value: 20,

            color: colorsType.blue,
        }
    },
    {
        id: 'kissPlayersSex',
        byQuest: categoriesType.fun,

        title: 'achievements.achievementsTask5',
        additionalTitle: null,
        progress: 25,

        award: {
            type: awardEnum.item, itemId: 726, count: 8 , // Экспериментальная пилюля Имморталитикс

            color: colorsType.purple,
        }
    },
    // { // Что за бред
    //     id: 'task4',
    //     byQuest: categoriesType.fun,

    //     title: 'Поцелуйте игроков своего пола',
    //     additionalTitle: null,
    //     progress: 50,

    //     award: {
    //         type: awardEnum.item, itemId: 726, count: 8 , // Экспериментальная пилюля Имморталитикс
    //         value: 1,

    //         color: colorsType.purple,
    //     }
    // },
    // {
    //     id: 'task5',
    //     byQuest: categoriesType.fun,

    //     title: 'Употребите все виды наркотиков',
    //     additionalTitle: null,
    //     progress: 3,

    //     award: {
    //         type: awardEnum.item, itemId: 625, count: 3  , // Адреналин (= эпинефрин)
    //         value: 1,

    //         color: colorsType.red,
    //     }
    // },
    {
        id: 'greetPlayers',
        byQuest: categoriesType.civil,

        title: 'achievements.achievementsTask6',
        additionalTitle: null,
        progress: 50,

        award: {
            type: awardEnum.item, itemId: 334, count: 5 , // ИРП АРМИИ США

            color: colorsType.gray,
        }
    },
    {
        id: 'use_gym',
        byQuest: categoriesType.civil,

        title: 'achievements.achievementsTask7',
        additionalTitle: null,
        progress: 100,

        award: {
            type: awardEnum.item, itemId: 732, count: 1 , // Протеиновый батончик

            color: colorsType.purple,
        }
    },
    {
        id: 'hijackingEarn',
        byQuest: categoriesType.fin,

        title: 'achievements.achievementsTask8',
        additionalTitle: null,
        progress: 500000,

        award: {
            type: awardEnum.case, case: 'vehicles' , // AUTO CASE

            color: colorsType.purple,
        }
    },
    {
        id: 'bankVaultMinigame',//
        byQuest: categoriesType.org,

        title: 'achievements.achievementsTask9',
        additionalTitle: null,
        progress: 1,

        award: {
            type: awardEnum.item, itemId: 443, count: 1 , // itemId: 443  Дрель 1500w 1 шт. BLUE

            color: colorsType.red,
        }
    },
    {
        id: 'deliveryDrugs',
        byQuest: categoriesType.org,

        title: 'achievements.achievementsTask10',
        additionalTitle: null,
        progress: 500,

        award: {
            type: awardEnum.case, case: 'default' , // обычный кейс

            color: colorsType.blue,
        }
    },
    {
        id: 'robbingBizMoney',
        byQuest: categoriesType.org,

        title: 'achievements.achievementsTask11',
        additionalTitle: null,
        progress: 100,

        award: {
            type: awardEnum.item, itemId: 333, count: 0 , // Ручной пулемет Мk2

            color: colorsType.purple,
        }
    },
    // {
    //     id: 'task12',
    //     byQuest: categoriesType.civil,

    //     title: 'Соберите группу с числом участников 30 и более человек',
    //     additionalTitle: null,
    //     progress: 1,

    //     award: {
    //         type: awardEnum.item, itemId: 334, count: 10 , // ИРП

    //         color: colorsType.gray,
    //     }
    // },
    {
        id: 'activeAirDrop',
        byQuest: categoriesType.org,

        title: 'achievements.achievementsTask12',
        additionalTitle: null,
        progress: 3,

        award: {
            type: awardEnum.item, itemId: 337, count: 0 , // Винтовка Marksman Mk2

            color: colorsType.red,
        }
    },
    {
        id: 'returnHijackVehicle',
        byQuest: categoriesType.org,

        title: 'achievements.achievementsTask13',
        additionalTitle: null,
        progress: 100,

        award: {
            type: awardEnum.case, case: 'vehicles' , // AUTO CASE

            color: colorsType.purple,
        }
    },
    {
        id: 'arrestPlayer',
        byQuest: categoriesType.org,

        title: 'achievements.achievementsTask14',
        additionalTitle: null,
        progress: 25,

        award: {
            type: awardEnum.coins, amount: 100 ,

            color: colorsType.gray,
        }
    },
    {
        id: 'cashOut',
        byQuest: categoriesType.org,

        title: 'achievements.achievementsTask15',
        additionalTitle: null,
        progress: 500000,

        award: {
            type: awardEnum.case, case: 'default' , // обычный кейс

            color: colorsType.blue,
        }
    },
    {
        id: 'drift_points',
        clientTrigger: true,
        byQuest: categoriesType.fun,

        title: 'achievements.achievementsTask26',
        additionalTitle: null,
        progress: 100000,

        award: {
            type: awardEnum.coins,
            amount: 100,

            color: colorsType.gray,
        }
    },
    {
        id: 'car_buy_donate',
        byQuest: categoriesType.fin,

        title: 'achievements.achievementsTask16',
        additionalTitle: null,
        progress: 1,

        award: {
            type: awardEnum.money, amount: 150000 ,

            color: colorsType.purple,
        }
    },
    /*{
        id: 'owner_vehicles_20',
        byQuest: categoriesType.fin,

        title: 'achievements.achievementsTask17',
        additionalTitle: null,
        progress: 1,

        award: {
            type: awardEnum.coins, amount: 150 ,

            color: colorsType.gray,
        }
    },*/
    // {
    //     id: 'task20',
    //     byQuest: categoriesType.fin,

    //     title: 'Купите розы в супермаркете',
    //     additionalTitle: null,
    //     progress: 101,

    //     award: {
    //         type: awardEnum.item, itemId: 733, count: 10 , // Стайлинг ManeMaster

    //         color: colorsType.blue,
    //     }
    // },
    {
        id: 'worldQuestsPersonal',
        byQuest: categoriesType.civil,

        title: 'achievements.achievementsTask18',
        additionalTitle: null,
        progress: 10,

        award: {
            type: awardEnum.coins, amount: 300 ,

            color: colorsType.red,
        }
    },
    {
        id: 'earnSellItemsOnMarket',
        byQuest: categoriesType.fin,

        title: 'achievements.achievementsTask19',
        additionalTitle: null,
        progress: 500000,

        award: {
            type: 'clothesDiscount', // СКИДКА НА ОДЕЖДУ 15%
            amount: 15,

            color: colorsType.red,
        }
    },
    {
        id: 'familyContract',
        byQuest: categoriesType.org,

        title: 'achievements.achievementsTask20',
        additionalTitle: null,
        progress: 100,

        award: {
            type: awardEnum.item, itemId: 625, count: 6 , // АДРЕНАЛИН

            color: colorsType.red,
        }
    },
    {
        id: 'getUnemployment',
        byQuest: categoriesType.civil,

        title: 'achievements.achievementsTask21',
        additionalTitle: null,
        progress: 500,

        award: {
            type: awardEnum.case, case: 'default' , // обычный кейс

            color: colorsType.blue,
        }
    },
    {
        id: 'lucky_phone_pick',
        byQuest: categoriesType.fun,

        title: 'achievements.achievementsTask22',
        additionalTitle: null,
        progress: 15,

        award: {
            type: awardEnum.coins, amount: 200,

            color: colorsType.blue,
        }
    },
    // {
    //     id: 'task26',
    //     byQuest: categoriesType.fin,

    //     title: 'Заработайте на аренде автомобилей',
    //     additionalTitle: null,
    //     progress: 1000000,

    //     award: {
    //         type: 'vehicleDiscount', // скидка авто 15% type: 'vehicleDiscount', color: 'purple',  value: 15
    //         amount: 15,

    //         color: colorsType.red,
    //     }
    // },
    {
        id: 'gold_lump',
        byQuest: categoriesType.civil,

        title: 'achievements.achievementsTask23',
        additionalTitle: null,
        progress: 1,

        award: {
            type: awardEnum.coins, amount: 300 ,

            color: colorsType.purple,
        }
    },
    {
        id: 'repair_vehicle',
        byQuest: categoriesType.civil,

        title: 'achievements.achievementsTask24',
        additionalTitle: null,
        progress: 100,

        award: {
            type: awardEnum.item, itemId: 727, count: 25 , // (25 штук) Качественный ремонтный набор

            color: colorsType.purple,
        }
    },
    {
        id: 'blackjack21',
        byQuest: categoriesType.fun,

        title: 'achievements.achievementsTask25',
        additionalTitle: null,
        progress: 100,

        award: {
            type: awardEnum.case, case: 'default' , // обычный кейс

            color: colorsType.blue,
        }
    },
    // {
    //     id: 'hw2024_riftPeds',
    //     byQuest: categoriesType.fun,

    //     title: 'achievements.achievementsTask27',
    //     additionalTitle: null,
    //     progress: 100,

    //     award: {
    //         type: awardEnum.case, case: 'halloween2023', //  кейс

    //         color: colorsType.blue,
    //     }
    // },
    // {
    //     id: 'hw2024_bossFightWin',
    //     byQuest: categoriesType.fun,

    //     title: 'achievements.achievementsTask28',
    //     additionalTitle: null,
    //     progress: 6,

    //     award: {
    //         type: awardEnum.clothes,
    //         clothesData: {
    //             0: { itemId: 43, gender: 0, component: 10, drawable: 2101, texture: 0, isProp: 0 },
    //             1: { itemId: 43, gender: 1, component: 10, drawable: 2102, texture: 0, isProp: 0 }
    //         },

    //         color: colorsType.blue,
    //     }
    // },
    {
        id: 'dealer_war_captureDealers',
        byQuest: categoriesType.org,

        title: 'achievements.achievementsTask29',
        additionalTitle: null,
        progress: 100,

        award: {
            type: awardEnum.item, itemId: 245, count: 1 , // Короткий пистолет

            color: colorsType.purple,
        }
    },
    // {
    //     id: 'task30',
    //     byQuest: categoriesType.fun,

    //     title: 'Выиграйте на Zero в рулетке казино',
    //     additionalTitle: null,
    //     progress: 10,

    //     award: {
    //         type: awardEnum.coins, amount: 150 ,

    //         color: colorsType.gray,
    //     }
    // },
    // {
    //     id: 'task31',
    //     byQuest: categoriesType.fin,

    //     title: 'Потратьте деньги на ставки в букмекерской конторе',
    //     additionalTitle: null,
    //     progress: 10000000,

    //     award: {
    //         type: awardEnum.case, case: 'default' , // обычный кейс

    //         color: colorsType.blue,
    //     }
    // },
    // {
    //     id: 'task32',
    //     byQuest: categoriesType.fun,

    //     title: 'Выиграйте Джекпот в игральных автоматах казино',
    //     additionalTitle: null,
    //     progress: 1,

    //     award: {
    //         type: awardEnum.coins, amount: 150 ,

    //         color: colorsType.gray,
    //     }
    // },
    // {
    //     id: 'win_lottery', // Нам нужен плеер в игре а не статик
    //     byQuest: categoriesType.fun,

    //     title: 'Выиграйте в лотерею',
    //     additionalTitle: null,
    //     progress: 1,

    //     award: {
    //         type: awardEnum.case, case: 'default' , // обычный кейс

    //         color: colorsType.blue,
    //     }
    // },
    // {
    //     id: 'task34',
    //     byQuest: categoriesType.civil,

    //     title: 'Выполните достижения', // В этом нет смысла, мы всегда можем их дополнять
    //     additionalTitle: null,
    //     progress: 30,

    //     award: {
    //         type: awardEnum.coins, amount: 1000 ,

    //         color: colorsType.gold,
    //     }
    // },

]
