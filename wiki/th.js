export default {
  global: {
    all: 'ทั้งหมด',
    back: 'ย้อนกลับ',
    forward: 'ถัดไป',
    paging: 'หน้า {page} จาก {maxPage}',
    close: 'ปิด',
    clear: 'ล้าง',
    save: 'บันทึก',
    delete: 'ลบออก',
    deleteYes: 'แน่ใจไหมว่าต้องการลบ?',
    cancel: 'การยกเลิก',
    publish: 'เผยแพร่',
    edit: 'แก้ไข',
    share: 'แชร์',
    add: 'เพิ่ม',
    active: 'ใช้งานอยู่',
    deleted: 'ลบไปแล้ว',
    blocked: 'บล็อคแล้ว',
    learnMore: 'ดูข้อมูลเพิ่มเติม',
    panel: 'บัญชีรายชื่อ',
    theme: 'เรื่อง',
    language: 'ภาษา',
    user: 'ผู้ใช้',
    confirm: 'ยืนยัน',
    restore: 'การกู้คืน',
    yes: 'ใช่',
    no: 'ไม่มี',
    new: 'ใหม่',
    preview: 'Предпросмотр',
    sortPriceDesc: 'ราคาจากมากไปน้อย',
    sortPriceAsc: 'ราคาจากน้อยไปมาก',
    sortPriceMinMax: 'ช่วงราคา',
    sortDateDesc: 'ตามวันที่ลดลง',
    sortDateAsc: 'ตามวันที่เพิ่มขึ้น',
    noSellPrice: 'ห้ามซื้อ',
    noBuyPrice: 'ห้ามขาย',
    noData: {
      title: 'ไม่พบอะไรเลย',
      description: 'ลองเปลี่ยนหรือทำให้การค้นหาง่ายขึ้น'
    },
    battlePass: {
      summer: 'Battle Pass ฤดูร้อน',
      winter: 'Battle Pass ฤดูหนาว'
    },
    case: {
      default: 'กล่องมาตรฐาน',
      vehicles: 'กล่องรถยนต์',
      halloween: 'กล่องฮัลโลวีน',
      spring: 'กล่องฤดูใบไม้ผลิ',
      autumn: 'เคสฤดูใบไม้ร่วง',
      summer: 'เคสฤดูร้อน',
      summerExtra: 'เคสฤดูร้อนพิเศษ',
      summerSkins: 'กล่องฤดูร้อนพร้อมสกิน',
      summerVehicles: 'เคสฤดูร้อนกับการคมนาคม',
      winter: 'เคสฤดูหนาว',
      winterExtra: 'กล่องฤดูหนาวพิเศษ',
      winterSkins: 'กล่องฤดูหนาวพร้อมสกิน',
      winterVehicles: 'เคสฤดูหนาวกับการคมนาคม',
      toys: 'เคสของเล่น'
    },
    subscription: { premium: 'การเป็นสมาชิกระดับพรี' },
    copy: 'Скопировать'
  },
  footer: { disclaimer: 'Вся информация, размещённая на сайте, носит исключительно ознакомительный характер и не является публичной офертой.' },
  footer: { disclaimer: 'Вся информация, размещённая на сайте, носит исключительно ознакомительный характер и не является публичной офертой.' },
  editor: {
    placeholder: 'มาเขียนโพสต์ดีๆกันเถอะ!',
    text: 'ย่อหน้า',
    heading: 'ชื่อ',
    warning: 'หมายเหตุ',
    quote: 'ใบเสนอราคา',
    code: 'รหัส',
    delimiter: 'ตัวแบ่ง',
    link: 'Reference',
    linkPlaceholder: 'กรอกลิงค์',
    marker: 'มาร์คเกอร์',
    bold: 'ตัวหนา',
    italic: 'ตัวเอียง',
    inlineCode: 'Monospaced',
    underline: 'ขีดเส้นใต้',
    copytext: 'Копируемый',
    log: 'Лог',
    map: {
      name: 'แผนที่',
      params: 'Введите параметры карты',
      caption: 'Введите описание карты'
    },
    table: {
      name: 'โต๊ะ',
      addColumnLeft: 'เพิ่มคอลัมน์ทางด้านซ้าย',
      addColumnRight: 'เพิ่มคอลัมน์ทางด้านขวา',
      deleteColumn: 'ลบคอลัมน์',
      addRowAbove: 'เพิ่มเส้นที่ด้านบน',
      addRowBelow: 'เพิ่มแถวด้านล่าง',
      deleteRow: 'ลบแถว',
      withHeadings: 'พร้อมชื่อเรื่อง',
      withoutHeadings: 'ไม่มีส่วนหัว'
    },
    image: {
      name: 'รูปภาพ',
      withBorder: 'พร้อมกรอบ',
      stretchImage: 'ภาพยืด',
      withBackground: 'พร้อมพื้นหลัง',
      selectImage: 'เลือกรูปภาพ',
      uploadError: 'อัพโหลดรูปภาพไม่สำเร็จโปรดลองอีกครั้ง',
      caption: 'คำอธิบายรูปภาพ',
      withCaption: 'С описанием'
    },
    carousel: {
      name: 'ตารางรูปภาพ',
      caption: 'คำอธิบายรูปภาพ',
      addImage: 'เลือกรูปภาพ'
    },
    list: {
      name: 'รายการ',
      ordered: 'ตัวเลข',
      unordered: 'ไม่มีตัวเลข',
      checklist: 'รายการตรวจสอบ'
    },
    header: {
      heading1: 'หัวข้อ H1',
      heading2: 'หัวข้อ H2',
      heading3: 'หัวข้อ H3',
      heading4: 'หัวข้อ H4'
    },
    embed: { caption: 'คำอธิบายลิงก์' },
    convertTo: 'แปลงเป็น',
    moveUp: 'เลื่อนขึ้น',
    moveDown: 'เลื่อนลง',
    anchor: 'สมอ',
    clickToTune: 'คลิกเพื่อปรับแต่ง',
    dragToMove: 'หรือลากแล้ววาง',
    filter: 'ค้นหา',
    nothingFound: 'ไม่พบอะไรเลย'
  },
  alerts: {
    loginSuccess: 'เข้าสู่ระบบสำเร็จ',
    loginError: 'ข้อผิดพลาดในการกันวงเงิน',
    registerSuccess: 'การลงทะเบียนบนเว็บไซต์สำเร็จ',
    registerError: 'เกิดข้อผิดพลาดในการลงทะเบียน',
    restoreSuccess: 'คำแนะนำการกู้คืนถูกส่งไปยังอีเมล',
    restoreError: 'เกิดข้อผิดพลาดในการกู้คืนการเข้าถึงบัญชี',
    signoutSuccess: 'ออกจากระบบเรียบร้อยแล้ว',
    postLoadError: 'เกิดข้อผิดพลาดในการโหลด',
    postShareSuccess: 'คัดลอกลิงก์ไปยังโพสต์ไปยังคลิปบอร์ดแล้ว',
    copyCommandSuccess: 'คัดลอกคำสั่งไปยังคลิปบอร์ดแล้ว',
    restoreAccountSuccess: 'กู้คืนบัญชีของคุณเรียบร้อยแล้ว',
    restoreAccountError: 'เกิดข้อผิดพลาดในการกู้คืนการเข้าถึงบัญชี',
    verificateAccountSuccess: 'ยืนยันกล่องจดหมายเรียบร้อยแล้ว',
    verificateAccountError: 'ข้อผิดพลาดในการยืนยันกล่องจดหมาย',
    dataUpdateSuccess: 'อัพเดทข้อมูลเรียบร้อยแล้ว',
    dataUpdateError: 'เกิดข้อผิดพลาดในการอัปเดตข้อมูล',
    dataDeleteSuccess: 'ลบข้อมูลสำเร็จแล้ว',
    dataDeleteError: 'เกิดข้อผิดพลาดในการลบข้อมูล',
    dataAddSuccess: 'เพิ่มข้อมูลเรียบร้อยแล้ว',
    dataAddError: 'เกิดข้อผิดพลาดในการเพิ่มข้อมูล',
    mapMoveInfo: 'ย้ายจุดไปยังตำแหน่งอื่นและบันทึก',
    mapCoordinatesCopySuccess: 'Координаты успешно скопированы',
    copyTextSuccess: 'Текст успешно скопирован'
  },
  mail: {
    verificate: {
      subject: 'ยืนยันอีเมล',
      title: 'การยืนยัน',
      description: 'คุณจะต้องคลิกปุ่มด้านล่างเพื่อทำการยืนยันและยืนยันบัญชีของคุณให้เสร็จสมบูรณ์ลิงก์นี้ใช้งานได้ 24 ชั่วโมง'
    },
    restore: {
      subject: 'กำลังกู้คืนการเข้าถึงบัญชีของคุณ',
      title: 'การกู้คืน',
      description: 'หากต้องการเข้าถึงบัญชีของคุณอีกครั้งคุณจะต้องคลิกปุ่มด้านล่างลิงก์นี้ใช้งานได้ 24 ชั่วโมง'
    },
    restoreSuccess: {
      subject: 'กู้คืนการเข้าถึงบัญชีของคุณได้สำเร็จ',
      title: 'การฟื้นตัวที่ประสบความสำเร็จ',
      description: 'คุณได้เปลี่ยนรหัสผ่านของคุณบนเว็บไซต์เรียบร้อยแล้วหากคุณไม่ได้ทำให้เปลี่ยนรหัสผ่านของคุณทันที!'
    },
    verificateSuccess: {
      subject: 'กู้คืนการเข้าถึงบัญชีของคุณได้สำเร็จ',
      title: 'การยืนยันสำเร็จแล้ว',
      description: 'คุณได้ยืนยันกล่องจดหมายของคุณเรียบร้อยแล้วยินดีต้อนรับ!'
    },
    caption: 'อีเมลนี้เป็นการแจ้งเตือนและสร้างขึ้นโดยอัตโนมัติโปรดอย่าตอบกลับอีเมลนี้'
  },
  fields: {
    price: 'ค่าใช้จ่าย',
    buyPrice: 'ราคาซื้อ',
    sellPrice: 'ต้นทุนขาย',
    mcPrice: 'Стоимость покупки в MC',
    money: 'สกุลเงิน',
    coin: 'เหรียญมาเจสติก',
    gift: 'ของขวัญ',
    default: 'มาตรฐาน',
    unique: 'ไม่เหมือนใคร',
    event: 'กิจกรรม',
    battlePass: 'แบตเทิลพาส',
    lootbox: 'เคส',
    capacityKg: 'ความจุ',
    email: 'ไปรษณีย์',
    password: 'รหัสผ่าน',
    username: 'เพื่อนโทรหาฉัน',
    weightKg: 'кг',
    speed: 'ความเร็ว',
    maxSpeed: 'ความเร็วสูงสุด',
    maxSpeedFT: 'ความเร็วสูงสุด (FT)',
    accelerationTo100: 'อัตราเร่ง 100 กม ./ ชม.',
    speedSystem: 'กม ./ ชม.',
    timeSystem: 'วินาที',
    uploadDescription: 'คลิกหรือลากภาพไปยังพื้นที่',
    placeholder: 'ป้อนค่าใน',
    imagePlaceholder: 'ใส่ลิงก์รูปภาพ',
    selectPlaceholder: 'เลือกค่าจากรายการ',
    videoPlaceholder: 'กรุณาใส่ลิงค์วิดีโอ',
    soundPlaceholder: 'กรุณาใส่ลิงค์เสียง',
    id: 'ID',
    serial: 'หมายเลขลำดับ',
    kind: 'ประเภท',
    category: 'หมวดหมู่',
    name: 'ชื่อ',
    view: 'ประเภท',
    layer: 'เลเยอร์',
    icon: 'ไอคอน',
    circle: 'คะแนน',
    polygon: 'รูปหลายเหลี่ยม',
    emoji: 'อีโมจิ',
    description: 'คำอธิบาย',
    gender: 'เพศ',
    value: 'ประเภทของต้นทุน',
    valueName: 'รายละเอียดราคาเพิ่มเติม',
    visibility: 'สถานะว่าง',
    visibilityPrivate: 'จำกัดการเข้าถึง',
    visibilityPublic: 'การมองเห็นสำหรับทุกคน',
    activityHidden: 'ซ่อนจากการแชร์',
    activityVisible: 'ทุกคนเข้าถึงได้',
    garageSlots: 'สล็อตโรงรถ',
    maxTenants: 'จำนวนผู้อยู่อาศัย',
    gasoline: 'เชื้อเพลิง',
    brand: 'แบรนด์',
    model: 'รุ่น',
    trunk: 'ลำตัว',
    tradable: 'ความสามารถในการถ่ายโอน',
    buyable: 'Возможность покупки',
    upgradesPriceFT: 'Доступные улучшения (FT)',
    upgradesEngine: 'มอเตอร์',
    upgradesBreaks: 'เบรก',
    upgradesSuspension: 'ระบบกันสะเทือน',
    upgradesTurbo: 'เทอร์โบ',
    upgradesTransmission: 'กล่อง',
    paintingsMainPrice: 'Основная покраска',
    paintingsSubPrice: 'Дополнительная покраска',
    paintingsOtherPrice: 'Прочая покраска',
    paintingsBrightMetallic: 'เมทัลลิกสดใส',
    paintingsMetallic: 'เมทัลลิก',
    paintingsRichMetallic: 'เมทัลลิกอิ่มตัว',
    paintingsDarkMetallic: 'สีเมทัลลิกเข้ม',
    paintingsMatte: 'ด้าน',
    paintingsMatteMetallic: 'โลหะขัดเงา',
    paintingsSatin: 'ซาติน',
    paintingsMetal: 'โลหะ',
    paintingsShadowChrome: 'Shadow Chrome',
    paintingsPureChrome: 'เพียวโครม',
    paintingsPearl: 'เฉดสีของแม่ไข่มุก',
    paintingsWheels: 'สีของแผ่น',
    paintingsDetails: 'สีชิ้นส่วน',
    paintingsAdditionalDetails: 'สีเพิ่มเติมของชิ้นส่วน',
    paintingsLights: 'ไฟหน้า',
    paintingsSmoke: 'สีควันยาง',
    paintingsNeon: 'สีนีออน',
    total: 'รวม',
    language: 'ภาษา',
    radius: 'Радиус',
    color: 'Цвет',
    colorPlaceholder: 'Выберите цвет',
    opacity: 'Прозрачность',
    alias: 'Псевдоним',
    aliases: 'Псевдонимы',
    looped: 'วนเวียน',
    battlePassId: 'รหัสการต่อสู้',
    gasolineCapacityLiter: 'ล',
    gasolineCapacity: 'ความจุของถัง',
    source: 'แหล่งที่มา',
    sources: 'แหล่งที่มา',
    priceDescription: 'คำอธิบายราคา',
    layerName: 'Название слоя',
    layerNamePlaceholder: 'Введите название слоя',
    layerDescription: 'Описание слоя',
    layerDescriptionPlaceholder: 'Введите описание слоя',
    layerColor: 'Цвет слоя',
    layerColorPlaceholder: 'Выберите цвет',
    layerOpacity: 'Прозрачность слоя',
    zIndexOffset: 'Уровень Z-индекса',
    zIndexOffsetPlaceholder: 'Введите значение уровня Z-индекса',
    comment: 'Комментарий'
  },
  vehiclesPage: {
    meta: {
      title: 'Транспорт и автомобили GTA5 RP | Majestic Wiki',
      description: 'Полный каталог транспорта на сервере Majestic RP в GTA5 RP. Подробные характеристики автомобилей, мотоциклов, вертолетов, самолетов и лодок. Узнайте цены, скорость, маневренность, вместимость багажника и другие параметры. Majestic Motors, Легковые, Грузовые, Вертолеты, Лодки, Мотоциклы, Велосипеды - все для GTA5 RP!',
      custom: {
        title: '{type} | Транспорт GTA5 RP | Majestic Wiki',
        description: 'Подробный каталог {type} на сервере Majestic RP в GTA5 RP. Характеристики, цены, скорость, маневренность и другие параметры. Полная информация о транспорте для GTA5 RP!'
      }
    },
    default: 'มาตรฐาน',
    donate: 'เหรียญมาเจสติก',
    battlePass: 'แบตเทิลพาส',
    case: 'เคส',
    unique: 'ไม่เหมือนใคร',
    title: 'ตารางการขนส่ง',
    search: 'ค้นหาการขนส่ง...',
    irl: 'มาเจสติกมอเตอร์',
    car: 'รถยนต์นั่งส่วนบุคคล',
    freight: 'สินค้า',
    helicopter: 'เฮลิคอปเตอร์',
    plane: 'Самолеты',
    boat: 'เรือ',
    motorbike: 'รถจักรยานยนต์',
    bike: 'จักรยาน',
    trailer: 'พ่วง',
    military: 'ทหาร',
    special: 'พิเศษ',
    priceDisclaimer: 'ค่าใช้จ่ายทั้งหมดอาจแตกต่างกันไปเนื่องจากค่าธรรมเนียม LSC บนเซิร์ฟเวอร์ของคุณ',
    basicInformation: 'ข้อมูลพื้นฐาน',
    additionalInformation: 'ข้อมูลเพิ่มเติม',
    compare: 'Сравнить',
    compareLimit: 'Сравнить можно не более 5 ТС',
    pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{транспорт} few{транспорта} many{транспорта} other{транспорта}}'
  },
  vehiclePage: {
    meta: {
      title: 'Транспорт GTA5 RP | Majestic Wiki',
      description: 'Подробная информация о транспорте на сервере Majestic RP в GTA5 RP. Характеристики, цены, улучшения и покраска. Узнайте о максимальной скорости, разгоне, вместимости багажника, стоимости улучшений и других параметрах каждого транспортного средства.',
      custom: {
        title: '{name} | Транспорт GTA5 RP | Majestic Wiki',
        description: 'Подробная информация о {name} на сервере Majestic RP в GTA5 RP. Характеристики, цены, улучшения и покраска. Узнайте о максимальной скорости, разгоне, вместимости багажника, стоимости улучшений и других параметрах.'
      }
    },
    backToTable: 'Вернуться к списку',
    labelsUniqueId: 'Уникальный ID',
    labelsPrice: 'ค่าใช้จ่าย',
    labelsBuyPrice: 'ราคาซื้อ',
    labelsSellPrice: 'ต้นทุนขาย',
    labelsMaxSpeed: 'ความเร็วสูงสุด',
    labelsMaxSpeedFT: 'ความเร็วสูงสุด (FT)',
    labelsAccelerationTo100: 'อัตราเร่ง 100 กม ./ ชม.',
    labelsTradable: 'ความสามารถในการถ่ายโอน',
    labelsTrunk: 'ความจุของกระโปรงหลัง',
    labelsGasolineCapacity: 'ความจุของถัง',
    labelsGasolineType: 'ประเภทน้ำมัน',
    upgradesTitle: 'Улучшения транспорта',
    upgradesDescription: 'Стоимость улучшений до максимального уровня',
    upgradesEngine: 'มอเตอร์',
    upgradesEngineDescription: 'Раскройте истинный потенциал транспорта',
    upgradesBreaks: 'เบรก',
    upgradesBreaksDescription: 'Полный контроль скорости на любой дороге',
    upgradesTurbo: 'เทอร์โบ',
    upgradesTurboDescription: 'Взрывное ускорение в любой момент',
    upgradesTransmission: 'กล่อง',
    upgradesTransmissionDescription: 'Молниеносное переключение всех передач',
    upgradesSuspension: 'ระบบกันสะเทือน',
    upgradesSuspensionDescription: 'Идеальный комфорт на нужной высоте',
    upgradesTotal: 'Стоимость улучшений',
    upgradesDisclaimer: 'ค่าใช้จ่ายทั้งหมดอาจแตกต่างกันไปเนื่องจากค่าธรรมเนียม LSC บนเซิร์ฟเวอร์ของคุณ',
    colorsTitle: 'Покраска транспорта',
    colorsDescription: 'Доступные типы и виды покраски со стоимостью',
    paintingsMainPrice: 'Основная покраска',
    paintingsSubPrice: 'Дополнительная покраска',
    paintingsOtherPrice: 'Прочая покраска',
    paintingsBrightMetallic: 'เมทัลลิกสดใส',
    paintingsMetallic: 'เมทัลลิก',
    paintingsRichMetallic: 'เมทัลลิกอิ่มตัว',
    paintingsDarkMetallic: 'สีเมทัลลิกเข้ม',
    paintingsMatte: 'ด้าน',
    paintingsMatteMetallic: 'โลหะขัดเงา',
    paintingsSatin: 'ซาติน',
    paintingsMetal: 'โลหะ',
    paintingsShadowChrome: 'Shadow Chrome',
    paintingsPureChrome: 'เพียวโครม',
    paintingsPearl: 'เฉดสีของแม่ไข่มุก',
    paintingsWheels: 'สีของแผ่น',
    paintingsDetails: 'สีชิ้นส่วน',
    paintingsAdditionalDetails: 'สีเพิ่มเติมของชิ้นส่วน',
    paintingsLights: 'ไฟหน้า',
    paintingsSmoke: 'สีควันยาง',
    paintingsNeon: 'สีนีออน',
    paintingsTotal: 'Финальная стоимость покраски',
    shareLink: 'Поделиться ссылкой на транспорт',
    shareLinkCopied: 'Ссылка на транспорт успешно скопирована',
    addToCompare: 'Добавить в сравнение',
    addedToCompare: 'Транспорт добавлен в сравнение',
    goToCompare: 'Перейти к сравнению',
    removeFromCompare: 'Удалить из сравнения',
    removedFromCompare: 'Транспорт удален из сравнения'
  },
  vehiclesComparePage: {
    meta: {
      title: 'Сравнение транспорта GTA5 RP | Majestic Wiki',
      description: 'Сравните характеристики и цены различных транспортных средств на сервере Majestic RP в GTA5 RP. Узнайте, какой автомобиль лучше всего подходит для ваших нужд! Сравнение скорости, цены, вместимости и других параметров.'
    },
    name: 'ชื่อ',
    type: 'ประเภท',
    brand: 'แบรนด์',
    kits: 'Комплектации',
    model: 'รุ่น',
    trunkCapacity: 'ความจุของกระโปรงหลัง',
    maxSpeed: 'ความเร็วสูงสุด',
    maxSpeedFT: 'ความเร็วสูงสุด (FT)',
    price: 'ค่าใช้จ่าย',
    donatePrice: 'Стоимость в Majestic Coin',
    sellPrice: 'ต้นทุนขาย',
    sellPriceBeforeTax: 'Стоимость продажи (до налога)',
    sellPriceWithDiscount: 'Стоимость продажи (со скидкой)',
    accelerationTo100: 'Время разгона до 100 км/ч',
    gasolineCapacity: 'ความจุของถัง',
    gasolineType: 'ประเภทน้ำมัน',
    isTradable: 'ความสามารถในการถ่ายโอน',
    isBuyable: 'Возможность покупки',
    createdAt: 'Дата появления',
    gasolineCapacityUnit: 'ล',
    maxSpeedUnit: 'กม ./ ชม.',
    maxSpeedFTUnit: 'กม ./ ชม.',
    priceUnit: '₽',
    accelerationTo100Unit: 'วินาที',
    trunkCapacityUnit: 'кг',
    showOnlyDifferent: 'Показать только отличающиеся',
    showAll: 'Показать все',
    share: 'Поделиться сравнением',
    linkCopied: 'Ссылка на сравнение успешно скопирована',
    openVehicle: 'Открыть страницу транспорта',
    deleteVehicle: 'Удалить транспорт из сравнения',
    noDifferences: 'Нет отличий'
  },
  skinsPage: {
    meta: {
      title: 'Скины оружия GTA5 RP | Majestic Wiki',
      description: 'Полный каталог скинов оружия на сервере Majestic RP в GTA5 RP. Узнайте о способах получения, стоимости и особенностях каждого скина для различных типов оружия.',
      custom: {
        title: 'Скины на {type} | GTA5 RP | Majestic Wiki',
        description: 'Каталог скинов для {type} на сервере Majestic RP в GTA5 RP. Подробная информация о способах получения, стоимости и особенностях скинов.'
      }
    },
    title: 'ตารางสกิน',
    search: 'ค้นหาสกิน...',
    apPistol: 'ปืนสั้นเจาะเกราะ',
    assaultRifle: 'ไรเฟิลจู่โจม',
    assaultRifleMk2: 'ปืนไรเฟิลจู่โจม Mk2',
    bullpupRifle: 'ปืนไรเฟิล Bullpup',
    carbineRifle: 'ปืนไรเฟิลคาราบิเนอร์',
    carbineRifleMk2: 'ปืนไรเฟิลคาร์ไบน์ Mk2',
    combatPDW: 'ต่อสู้กับ PDW',
    gusenberg: 'ปืนกลมือทอมป์สัน',
    heavyPistol: 'ปืนพกหนัก',
    heavyShotgun: 'ปืนลูกซองหนัก',
    heavySniper: 'ปืนไรเฟิลซุ่มยิงหนัก',
    heavySniperMk2: 'ปืนไรเฟิลซุ่มยิงหนัก Mk2',
    lightvest: 'เสื้อเกราะกันกระสุน',
    machinePistol: 'PP ขนาดเล็ก',
    marksmanPistol: 'ปืนพก Marksman',
    microSMG: 'ไมโคร SMG',
    militaryRifle: 'ปืนไรเฟิลทหาร',
    revolver: 'ปืนลูกโม่',
    smgMk2: 'SMG Mk2',
    specialCarbine: 'คาร์ไบน์พิเศษ',
    vintagePistol: 'ปืนวินเทจ',
    combatMgMk2: 'ปืนกลเบา Mk2',
    heavyRifle: 'ปืนไรเฟิลหนัก',
    tacticalRifle: 'ปืนไรเฟิลแทคติก',
    tecPistol: 'SMG ทางยุทธวิธี',
    noName: 'ผิวหนัง #{id}',
    battlePass: 'แบตเทิลพาส',
    case: 'เคส',
    unique: 'ไม่เหมือนใคร',
    pagingText: 'พบแล้ว{N, plural, few{~ о} many{~}} สกิน {N} {N, plural, one{~ สกิน} few{} many{สกิน} other{สกิน}}'
  },
  animationsPage: {
    meta: {
      title: 'Анимации GTA5 RP | Majestic Wiki',
      description: 'Полный каталог анимаций на сервере Majestic RP в GTA5 RP. Все виды анимаций: действия, позы, танцы и эксклюзивные анимации. Узнайте способы получения и стоимость каждой анимации.',
      custom: {
        title: '{type} | Анимации GTA5 RP | Majestic Wiki',
        description: 'Каталог {type} анимаций на сервере Majestic RP в GTA5 RP. Подробная информация о способах получения, стоимости и использовании анимаций.'
      }
    },
    title: 'ตารางอนิเมชัน',
    search: 'ค้นหาอนิเมชัน...',
    default: 'มาตรฐาน',
    donate: 'เหรียญมาเจสติก',
    battlePass: 'แบตเทิลพาส',
    case: 'เคส',
    unique: 'ไม่เหมือนใคร',
    action: 'กิจกรรม',
    pose: 'ท่าทาง',
    positive: 'บวก',
    negative: 'ลบ',
    dances: 'การเต้น',
    etc: 'อื่นๆ',
    exclusive: 'พิเศษ',
    noName: 'การเคลื่อนไหว #{id}',
    pagingText: 'พบ{N, plural, one{อัน} few{} many{ } other{ }} {N} {N, plural, one{การเคลื่อนไหว} few{การเคลื่อนไหว} many{การเคลื่อนไหว} other{การเคลื่อนไหว}}'
  },
  realtyPage: {
    meta: {
      title: 'Недвижимость GTA5 RP | Majestic Wiki',
      description: 'Полный каталог недвижимости на сервере Majestic RP в GTA5 RP. Дома, квартиры, офисы и склады с подробными характеристиками, ценами и расположением. Узнайте количество жильцов, гаражных мест и другие параметры.',
      custom: {
        title: '{type} | Недвижимость GTA5 RP | Majestic Wiki',
        description: 'Каталог {type} на сервере Majestic RP в GTA5 RP. Подробная информация о характеристиках, ценах, расположении и особенностях недвижимости.'
      }
    },
    title: 'ตารางคุณสมบัติ',
    search: 'ค้นหาที่พัก...',
    majesticMotors: 'มาเจสติกมอเตอร์',
    house: 'ที่บ้าน',
    apartment: 'อพาร์ทเมนท์',
    office: 'สำนักงาน',
    warehouse: 'คลังสินค้า',
    tenantsFor: '{N} {N, plural, one{~ ผู้เช่า} few{~ ผู้เช่า} many{~ ผู้เช่า} other{~ ผู้เช่า}}~',
    garageSlotsFor: '{N} {N, plural, one{~ พื้นที่โรงรถ} few{~ พื้นที่โรงรถ} many{~ พื้นที่โรงรถ} other{~ พื้นที่โรงรถ}}~',
    viewOnMap: 'ดูบนแผนที่',
    mapPosition: 'ที่ตั้ง',
    pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{недвижимость} few{недвижимости} many{недвижимости} other{недвижимости}}',
    noName: 'อสังหาริมทรัพย์ #{id}'
  },
  postsPage: {
    meta: {
      title: 'Редактирование поста | Majestic Wiki',
      description: 'Создание и редактирование постов на Majestic Wiki для GTA5 RP. Публикация актуальной информации о транспорте, недвижимости, бизнесах и других аспектах игры.'
    },
    titlePlaceholder: 'ใส่ชื่อโพสต์',
    category: 'หมวดหมู่',
    noCategory: 'ไม่มีหมวดหมู่',
    availability: 'สถานะว่าง',
    allView: 'การมองเห็นสำหรับทุกคน',
    privateView: 'จำกัดการเข้าถึง',
    relevance: 'ความเกี่ยวข้อง',
    relevanceData: 'ข้อมูลล่าสุด',
    outdatedData: 'ข้อมูลที่ล้าสมัย',
    categoryPosition: 'ตำแหน่งหมวดหมู่',
    categoryPositionDescription: 'ตัวอย่างเช่น “50” หรือ “-5 ”',
    postDeleted: 'ลบโพสต์แล้ว',
    author: 'ผู้เขียนบทความ',
    editor: 'บรรณาธิการบทความ',
    contents: 'สารบัญของบทความ',
    readingTime: 'เวลาอ่านหนังสือ',
    readingTimeLength: '{N} {N, plural, one{~ นาที ~~ นาที} few{~ นาที} many{~ นาที} other{~ นาที}}~',
    publishDate: 'การตีพิมพ์จาก {date}',
    views: '{N} {N, plural, one{~ วิว ~~ วิว} few{~ วิว} many{~ วิว} other{~ วิว}}~',
    youLiked: 'คุณชอบมัน',
    youAndPeopleLiked: 'นอกจากนี้คุณยัง {likes} ชอบ',
    peopleLiked: '{N} {N, plural, one{~ ชอบ ~~ ชอบ} few{~ ชอบ} many{~ ชอบ} other{~ ชอบ ~~ ชอบ}}~',
    categories: 'หมวดหมู่',
    posts: 'โพสต์',
    foundMistake: 'พบข้อผิดพลาดในบทความหรือไม่',
    language: 'ภาษา'
  },
  panelPage: {
    users: {
      title: 'Управление пользователями | Majestic Wiki',
      heading: 'Управление пользователями',
      admin: 'ผู้ดูแลระบบ',
      user: 'ผู้ใช้',
      customUser: 'ผู้ใช้ที่มีสิทธิ์',
      search: 'เริ่มพิมพ์ข้อความค้นหา...',
      comment: 'Комментарий',
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{пользователь} few{пользователя} many{пользователей} other{пользователей}}'
    },
    posts: {
      title: 'Управление постами | Majestic Wiki',
      heading: 'Управление постами',
      views: '{N} {N, plural, one{~ วิว ~~ วิว} few{~ วิว} many{~ วิว} other{~ วิว}}~',
      likes: '{N} {N, plural, one{~ ชอบ ~~ ชอบ} few{~ ชอบ} many{~ ชอบ} other{~ ชอบ ~~ ชอบ}}~'
    },
    categories: {
      title: 'Управление категориями | Majestic Wiki',
      heading: 'Управление категориями'
    },
    mapLayers: {
      title: 'Управление слоями карты | Majestic Wiki',
      heading: 'Управление слоями карты'
    },
    logs: {
      title: 'Аудит логов — Majestic Wiki',
      heading: 'Аудит логов',
      search: 'เริ่มพิมพ์ข้อความค้นหา...',
      biz: 'ธุรกิจ',
      category: 'หมวดหมู่',
      clothing: 'เสื้อผ้า',
      file: 'Файл',
      item: 'เรื่อง',
      mapItem: 'Элемент карты',
      mapLayer: 'Слой карты',
      post: 'โพสต์',
      realty: 'อสังหาริมทรัพย์',
      skin: 'ผิวหนัง',
      user: 'ผู้ใช้',
      vehicle: 'การขนส่ง',
      animation: 'อนิเมชัน',
      actions: {
        unknown: '{username} совершил неизвестное действие',
        bizCreate: '{username} เพิ่มธุรกิจ {id}',
        bizUpdate: '{username} แก้ไขธุรกิจ {id}',
        bizDelete: '{username} ลบบริษัท {id}',
        categoryCreate: '{username} เพิ่มหมวดหมู่ {id}',
        categoryUpdate: '{username} เปลี่ยนหมวดหมู่ {id}',
        categoryDelete: '{username} ลบหมวดหมู่ {id}',
        clothingCreate: '{username} เพิ่มเสื้อผ้า {id}',
        clothingUpdate: '{username} แก้ไขเสื้อผ้า {id}',
        clothingDelete: '{username} ลบเสื้อผ้า {id}',
        fileCreate: '{username} เพิ่มไฟล์ {id}',
        fileUpdate: '{username} แก้ไขไฟล์ {id}',
        fileDelete: '{username} ลบไฟล์ {id}',
        itemCreate: '{username} เพิ่มไอเท็ม {id}',
        itemUpdate: '{username} เปลี่ยนไอเท็ม {id}',
        itemDelete: '{username} ลบไอเท็ม {id}',
        mapItemCreate: '{username} เพิ่มองค์ประกอบที่แผนที่ {id} ที่ "{layer}"',
        mapItemUpdate: '{username} เปลี่ยนองค์ประกอบที่แผนที่ {id} ที่ "{layer}"',
        mapItemDelete: '{username} ลบองค์ประกอบที่แผนที่ {id} ที่ "{layer}"',
        mapLayerCreate: '{username} เพิ่มเลเยอร์แผนที่ {id}',
        mapLayerUpdate: '{username} เปลี่ยนเลเยอร์แผนที่ {id}',
        mapLayerDelete: '{username} ลบเลเยอร์แผนที่ {id}',
        postCreate: '{username} เพิ่มโพสต์ {id}',
        postUpdate: '{username} เปลี่ยนโพสต์ {id}',
        postDelete: '{username} ลบโพสต์ {id}',
        realtyCreate: '{username} เพิ่มอสังหาริมทรัพย์ {id}',
        realtyUpdate: '{username} เปลี่ยนอสังหาริมทรัพย์ {id}',
        realtyDelete: '{username} ลบอสังหาริมทรัพย์ {id}',
        skinCreate: '{username} เพิ่มสกิน {id}',
        skinUpdate: '{username} เปลี่ยนสกิน {id}',
        skinDelete: '{username} ลบสกิน {id}',
        userCreate: '{username} เพิ่มผู้ใช้งาน {id}',
        userUpdate: '{username} เปลี่ยนผู้ใช้งาน {id}',
        userDelete: '{username} ลบผู้ใช้งาน {id}',
        vehicleCreate: '{username} เพิ่มการคมนาคม {id}',
        vehicleUpdate: '{username} เปลี่ยนการคมนาคม {id}',
        vehicleDelete: '{username} ลบการคมนาคม {id}',
        animationCreate: '{username} เพิ่มอนิเมชัน {id}',
        animationUpdate: '{username} เปลี่ยนอนิเมชัน {id}',
        animationDelete: '{username} ลบอนิเมชัน {id}',
        changelogCreate: '{username} добавил ченджлог {id}',
        changelogUpdate: '{username} изменил ченджлог {id}',
        changelogDelete: '{username} удалил ченджлог {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: 'การตั้งค่า',
      users: 'ผู้ใช้',
      categories: 'หมวดหมู่',
      mapLayers: 'เลเยอร์แผนที่',
      posts: 'โพสต์',
      logs: 'Логи'
    }
  },
  changelogPage: {
    titlePlaceholder: 'Введите название изменения',
    descriptionPlaceholder: 'Введите описание изменения',
    availableAt: 'Дата публикации',
    soon: 'через',
    ago: 'назад',
    fix: 'Исправлено',
    feat: 'Добавлено',
    chore: 'Изменено',
    style: 'Дизайн',
    refactor: 'Переработано'
  },
  changelogsPage: {
    meta: {
      title: 'История изменений | Majestic Wiki',
      description: 'Полная история обновлений и изменений на сервере Majestic RP. Следите за новыми функциями, улучшениями геймплея, исправлениями багов и балансировкой. Актуальная информация о всех обновлениях проекта.'
    },
    title: 'История изменений',
    search: 'Поиск изменений...'
  },
  homePage: {
    meta: {
      title: 'Majestic Wiki | GTA5 RP Энциклопедия',
      description: 'Majestic Wiki - полная энциклопедия для игроков GTA5 RP. Информация о транспорте, недвижимости, бизнесах, квестах и других аспектах игры. Актуальные данные о ценах, характеристиках и способах получения.'
    },
    tools: {
      map: {
        title: 'แผนที่',
        description: 'แผนที่เซิร์ฟเวอร์ที่มีตำแหน่งที่ตั้งทั้งหมด'
      },
      vehicles: {
        title: 'การขนส่ง',
        description: 'การขนส่งทั้งหมดที่มีอยู่บนเซิร์ฟเวอร์'
      },
      realty: {
        title: 'อสังหาริมทรัพย์',
        description: 'ข้อมูลเกี่ยวกับบ้านและอพาร์ทเมนท์ทั้งหมด'
      },
      biz: {
        title: 'ธุรกิจ',
        description: 'ตัวเลือกรายได้ขั้นสูง'
      },
      clothesMale: {
        title: 'เสื้อผ้าผู้ชาย',
        description: 'แฟชั่นโชว์สำหรับผู้ชาย'
      },
      clothesFemale: {
        title: 'เสื้อผ้าผู้หญิง',
        description: 'แฟชั่นโชว์ผู้หญิง'
      },
      skins: {
        title: 'สกิน',
        description: 'แค็ตตาล็อกของรูปแบบที่มีอยู่ทั้งหมด'
      },
      items: {
        title: 'รายการ',
        description: 'รายการที่มีอยู่บนเซิร์ฟเวอร์'
      },
      servers: {
        title: 'เซิร์ฟเวอร์',
        description: 'รายชื่อเซิร์ฟเวอร์ Majestic RP ทั้งหมด'
      },
      animations: {
        title: 'การเคลื่อนไหว',
        description: 'การเคลื่อนไหวที่ไม่ซ้ำกันบนเซิร์ฟเวอร์'
      },
      changelogs: {
        title: 'История изменений',
        description: 'История изменений на проекте'
      }
    },
    search: {
      title: 'หมวดหมู่ทั่วไป',
      description: 'ที่นี่คุณสามารถค้นหาข้อมูลใดๆเกี่ยวกับเซิร์ฟเวอร์ Majestic และระบบของเซิร์ฟเวอร์'
    },
    categories: {
      collapseList: 'ยุบรายการ',
      expandList: 'ขยายรายการ'
    },
    online: {
      players: '{count} ออนไลน์',
      offline: 'ปิด'
    }
  },
  errorPage: {
    title: 'ไม่พบหน้านี้',
    description: 'คุณอาจพิมพ์ผิดในที่อยู่ของหน้าเว็บหรือไม่มีอยู่ :(',
    backToHome: 'ไปที่หน้าแรก'
  },
  bizPage: {
    meta: {
      title: 'Бизнесы GTA5 RP | Majestic Wiki',
      description: 'Полный каталог бизнесов на сервере Majestic RP в GTA5 RP. Магазины 24/7, заправки, оружейные магазины, автосалоны и другие предприятия. Узнайте о прибыльности, требованиях и способах приобретения каждого бизнеса.',
      custom: {
        title: '{type} | Бизнесы GTA5 RP | Majestic Wiki',
        description: 'Подробная информация о {type} на сервере Majestic RP в GTA5 RP. Характеристики, прибыльность, требования и способы приобретения бизнеса.'
      }
    },
    title: 'ตารางธุรกิจ',
    search: 'ค้นหาธุรกิจ...',
    groceryStore: 'ช้อปได้ทุกวันตลอด 24 ชั่วโมง',
    gasStation: 'การเติมน้ำมันเชื้อเพลิง',
    atm: 'ATM',
    gunshop: 'ร้านปืน',
    clothingStore: 'ร้านเสื้อผ้า',
    autoShop: 'ตัวแทนจำหน่ายรถยนต์',
    tattoo: 'ร้านสัก',
    lsc: 'ร้านปรับแต่ง',
    barbershop: 'ร้านตัดผม',
    carWash: 'บริการล้างรถ',
    viewOnMap: 'ดูบนแผนที่',
    mapPosition: 'ที่ตั้ง',
    pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{бизнес} few{бизнеса} many{бизнесов} other{бизнесов}}',
    noName: 'ธุรกิจ #{id}'
  },
  clothesPage: {
    meta: {
      title: 'Одежда GTA5 RP | Majestic Wiki',
      description: 'Полный каталог одежды на сервере Majestic RP в GTA5 RP. Мужская и женская одежда, аксессуары, рюкзаки, нашивки, головные уборы и другие предметы гардероба. Узнайте о способах получения и стоимости каждого предмета одежды.',
      custom: {
        title: '{type} | Одежда GTA5 RP | Majestic Wiki',
        description: 'Каталог {type} на сервере Majestic RP в GTA5 RP. Подробная информация о способах получения, использовании и стоимости предметов одежды.'
      }
    },
    titleMale: 'โต๊ะเสื้อผ้าผู้ชาย',
    titleFemale: 'ตารางเสื้อผ้าสตรี',
    search: 'กำลังค้นหาเสื้อผ้า...',
    top: 'ด้านบน',
    legs: 'ด้านล่าง',
    shoes: 'รองเท้า',
    watch: 'ชั่วโมง',
    mask: 'หน้ากาก',
    decal: 'แพทช์',
    accessory: 'เครื่องประดับ',
    head: 'หมวก',
    bag: 'กระเป๋าเป้สะพายหลัง',
    glasses: 'แว่นตา',
    ear: 'หู',
    gloves: 'ถุงมือ',
    undershirt: 'เสื้อยืด',
    noName: 'ชุดเสื้อผ้า #{id}',
    drawable: 'วาดได้',
    texture: 'พื้นผิว',
    male: 'ชาย',
    female: 'Женский',
    donate: 'เหรียญมาเจสติก',
    battlePass: 'แบตเทิลพาส',
    case: 'เคส',
    unique: 'ไม่เหมือนใคร',
    pagingText: 'พบ{N, plural, few{~ เกี่ยวกับ} many{~ เกี่ยวกับ}} ~~ เสื้อผ้า {N} {N, plural, one{~ เสื้อผ้า} few{~ เสื้อผ้า} many{~ เสื้อผ้า} other{~ เสื้อผ้า}}~'
  },
  itemsPage: {
    meta: {
      title: 'Предметы GTA5 RP | Majestic Wiki',
      description: 'Полный каталог предметов на сервере Majestic RP в GTA5 RP. Антиквариат, рыба, руда, древесина, ящики, материалы и другие предметы. Узнайте о способах получения, использовании и стоимости каждого предмета.',
      custom: {
        title: '{type} | Предметы GTA5 RP | Majestic Wiki',
        description: 'Каталог {type} на сервере Majestic RP в GTA5 RP. Подробная информация о способах получения, использовании и стоимости предметов.'
      }
    },
    title: 'ตารางรายการ',
    search: 'กำลังค้นหารายการ...',
    antique: 'โบราณวัตถุ',
    fish: 'ปลา',
    ore: 'สินแร่',
    wood: 'ไม้',
    box: 'กล่อง',
    material: 'วัสดุ',
    event: 'กิจกรรม',
    food: 'ผลิตภัณฑ์',
    alcohol: 'แอลกอฮอล์',
    illegal: 'ผิดกฎหมาย',
    medical: 'ยา',
    equipment: 'อุปกรณ์',
    ammunition: 'กระสุนปืน',
    tool: 'เครื่องมือ',
    unique: 'ไม่เหมือนใคร',
    vehicle: 'อะไหล่รถยนต์',
    others: 'เบ็ดเตล็ด',
    default: 'มาตรฐาน',
    battlePass: 'แบตเทิลพาส',
    case: 'เคส',
    unique: 'ไม่เหมือนใคร',
    pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{предмет} few{предмета} many{предметов} other{предметов}}',
    noName: 'เรื่อง #{id}'
  },
  mapPage: {
    meta: {
      title: 'Карта GTA5 RP | Majestic Wiki',
      description: 'Интерактивная карта сервера Majestic RP в GTA5 RP. Исследуйте локации, находите сокровища, животных, телефоны и мусорки. Детальная карта мира с возможностью добавления меток и зон. Все важные места и точки интереса на одном ресурсе!',
      custom: {
        title: '{type} | Карта GTA5 RP | Majestic Wiki',
        description: 'Детальная карта {type} на сервере Majestic RP в GTA5 RP. Исследуйте локации, находите важные точки и создавайте собственные метки. Полная информация о расположении объектов на карте!'
      }
    },
    dataLoading: 'กำลังโหลดข้อมูล...',
    defaultPoints: 'ป้ายกำกับมาตรฐาน',
    animalLayer: 'แผนที่สัตว์',
    rubbishLayer: 'แผนที่ถังขยะ',
    telephoneLayer: 'แผนที่โทรศัพท์',
    treasureLayer: 'แผนที่สมบัติ',
    defaultLayer: 'แผนที่โลก',
    allLayers: 'เลเยอร์แผนที่',
    backToHome: 'กลับไปที่หน้าแรก',
    zoomIn: 'ซูมเข้า',
    zoomOut: 'ลบออก',
    point: 'คะแนน',
    polygon: 'Зона',
    addPoint: 'Добавить точку',
    addPolygon: 'Добавить зону',
    copyCoordinates: 'Скопировать координаты',
    savePointPosition: 'Сохранить позицию',
    editPointPosition: 'Изменить позицию',
    layerNamePlaceholder: 'Название зоны',
    layerDescriptionPlaceholder: 'Описание зоны'
  },
  serversPage: {
    meta: {
      title: 'Онлайн серверов GTA5 RP | Majestic Wiki',
      description: 'Актуальная статистика онлайн игроков на серверах Majestic RP в GTA5 RP. Текущий онлайн, пиковые значения, очереди и графики активности. Присоединяйтесь к самому популярному ролевому проекту!'
    },
    dataLoading: 'กำลังโหลดข้อมูล...',
    title: 'เซิร์ฟเวอร์ออนไลน์',
    online: '{count} ออนไลน์',
    inQueue: '{count} в очереди',
    peakOnline: '{count} пик',
    currentOnline: 'ออนไลน์ปัจจุบัน',
    peakOnline24h: 'จุดสูงสุดของวันนี้',
    peakOnlineAllTime: 'จุดสูงสุดตลอดกาล',
    peakOnlineTooltip: '{percent}% เมื่อเทียบกับวันที่ผ่านมา',
    techWorks: 'งานทางเทคนิค',
    forBeginners: 'สำหรับผู้เริ่มต้น',
    experience: 'ประสบการณ์ x{multiplier}',
    newServer: 'ใหม่',
    seasonPassMultiplier: 'ข้ามประสบการณ์ x{multiplier}',
    combinedView: 'Общий',
    separateView: 'Раздельный'
  },
  research: {
    placeholder: 'กำลังค้นหาข้อมูลในเว็บไซต์...',
    skin: 'ผิวหนัง',
    vehicle: 'การขนส่ง',
    realty: 'อสังหาริมทรัพย์',
    biz: 'ธุรกิจ',
    clothing: 'เสื้อผ้า',
    item: 'เรื่อง',
    post: 'โพสต์'
  },
  userModal: {
    login: 'เข้าสู่ระบบ',
    signout: 'ออกจากระบบ',
    register: 'การลงทะเบียน',
    registerEnd: 'ลงทะเบียน',
    restore: 'การกู้คืน',
    restoreEnd: 'คืนค่า',
    avatarDelete: 'ลบอวตาร',
    headingUpdate: 'แก้ไขผู้ใช้ #{id}',
    postAdd: 'การโพสต์',
    postUpdate: 'แก้ไขโพสต์',
    postDelete: 'กำลังลบโพสต์',
    vehicleAdd: 'กำลังเพิ่มการขนส่ง',
    vehicleUpdate: 'กำลังแก้ไขการขนส่ง',
    vehicleDelete: 'การนำขนย้ายออก',
    realtyAdd: 'การเพิ่มที่พัก',
    realtyUpdate: 'การเปลี่ยนแปลงในอสังหาริมทรัพย์',
    realtyDelete: 'การเอาทรัพย์สินออก',
    bizAdd: 'การเพิ่มธุรกิจ',
    bizUpdate: 'การเปลี่ยนแปลงธุรกิจ',
    bizDelete: 'การลบธุรกิจ',
    mapItemAdd: 'การเพิ่มเครื่องหมายบอกตำแหน่งบนแผนที่',
    mapItemUpdate: 'การเปลี่ยนเครื่องหมายบอกตำแหน่งบนแผนที่',
    mapItemDelete: 'การลบเครื่องหมายบอกตำแหน่งบนแผนที่',
    clothingAdd: 'การเพิ่มเสื้อผ้า',
    clothingUpdate: 'การเปลี่ยนเสื้อผ้า',
    clothingDelete: 'การถอดเสื้อผ้า',
    skinAdd: 'การเพิ่มสกิน',
    skinUpdate: 'ปรับเปลี่ยนสกิน',
    skinDelete: 'ลบสกินออก',
    itemAdd: 'การเพิ่มรายการ',
    itemUpdate: 'การเปลี่ยนรายการ',
    itemDelete: 'กำลังลบรายการ',
    fileUpload: 'กำลังอัพโหลดภาพ',
    viewPrivate: 'ดูข้อมูลส่วนตัว',
    doMagic: 'เข้าถึงฟีเจอร์มหัศจรรย์',
    animationAdd: 'การเพิ่มแอนิเมชัน',
    animationUpdate: 'การเปลี่ยนแปลงแอนิเมชัน',
    animationDelete: 'ลบอนิเมชัน',
    categoryAdd: 'Добавление категорий',
    categoryUpdate: 'Изменение категорий',
    categoryDelete: 'Удаление категорий',
    mapLayerAdd: 'Добавление слоев карты',
    mapLayerUpdate: 'Изменение слоев карты',
    mapLayerDelete: 'Удаление слоев карты',
    post: 'โพสต์',
    skin: 'ผิวหนัง',
    biz: 'ธุรกิจ',
    clothing: 'เสื้อผ้า',
    item: 'เรื่อง',
    vehicle: 'การขนส่ง',
    animation: 'อนิเมชัน',
    category: 'หมวดหมู่',
    realty: 'อสังหาริมทรัพย์',
    mapLayer: 'Слой карты',
    changelog: 'Ченджлог'
  },
  restoreModal: {
    title: 'กำลังกู้คืนบัญชีของคุณ',
    newPassword: 'รหัสผ่านใหม่',
    newPasswordRepeat: 'ใส่รหัสผ่านใหม่อีกครั้ง',
    passwordConfirm: 'เปลี่ยนรหัสผ่าน',
    passwordError: 'รหัสผ่านไม่ตรงกัน'
  },
  bizModal: {
    titleUpdate: 'การเปลี่ยนแปลงธุรกิจ #{id}',
    titleAdd: 'การเพิ่มธุรกิจใหม่'
  },
  categoryModal: {
    titleUpdate: 'การเปลี่ยนแปลงหมวดหมู่ #{id}',
    titleAdd: 'เพิ่มหมวดหมู่ใหม่'
  },
  mapLayerModal: {
    titleUpdate: 'Изменение слоя карты #{id}',
    titleAdd: 'Добавление нового слоя карты'
  },
  clothingModal: {
    titleUpdate: 'การเปลี่ยนเสื้อผ้า #{id}',
    titleAdd: 'การเพิ่มเสื้อผ้าใหม่',
    itemAdd: 'เพิ่มสี',
    itemGenerate: 'สร้างทุกสี'
  },
  itemModal: {
    titleUpdate: 'การเปลี่ยนเรื่อง #{id}',
    titleAdd: 'การเพิ่มรายการใหม่'
  },
  realtyModal: {
    titleUpdate: 'การเปลี่ยนแปลงในอสังหาริมทรัพย์ #{id}',
    titleAdd: 'การเพิ่มที่พักใหม่'
  },
  skinModal: {
    titleUpdate: 'การเปลี่ยนแปลงของผิวหนัง #{id}',
    titleAdd: 'เพิ่มสกินใหม่'
  },
  vehicleModal: {
    titleUpdate: 'การเปลี่ยนการขนส่ง #{id}',
    titleAdd: 'เพิ่มยานพาหนะใหม่',
    titleDefault: 'ข้อมูลเกี่ยวกับ {name}',
    itemAdd: 'เพิ่ม BODY KIT'
  },
  mapItemModal: {
    titleUpdate: 'เปลี่ยนจุด',
    titleAdd: 'เพิ่มคะแนนใหม่',
    titleDelete: 'Удаление элемента карты',
    descriptionDelete: 'Вы точно хотите удалить элемент с карты? Это действие невозможно отменить.'
  },
  animationModal: {
    titleUpdate: 'การเปลี่ยนแปลงการเคลื่อนไหว #{id}',
    titleAdd: 'การเพิ่มการเคลื่อนไหวใหม่'
  }
};