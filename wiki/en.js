export default {
  global: {
    all: 'All',
    back: 'Back',
    forward: 'Forward',
    paging: 'Page {page} of {maxPage}',
    close: 'Close',
    clear: 'Clear',
    save: 'Save',
    delete: 'Remove',
    deleteYes: 'Are you sure about the removal?',
    cancel: 'Cancel',
    publish: 'Publish',
    edit: 'Edit',
    share: 'Share',
    add: 'Add',
    active: 'Active',
    deleted: 'Deleted',
    blocked: 'Blocked',
    learnMore: 'Read more',
    panel: 'Panel',
    theme: 'Subject',
    language: 'Language',
    user: 'User',
    confirm: 'Confirm',
    restore: 'Recovery',
    yes: 'Yes',
    no: 'None',
    new: 'New',
    preview: 'Preview',
    sortPriceDesc: 'By descending price',
    sortPriceAsc: 'By ascending price',
    sortPriceMinMax: 'Price range',
    sortDateDesc: 'In descending order of date',
    sortDateAsc: 'In ascending order of date',
    noSellPrice: 'Not for purchase',
    noBuyPrice: 'Not for sale',
    noData: {
      title: 'Nothing found',
      description: 'Try changing or simplifying your search query'
    },
    battlePass: {
      summer: 'Summer Battle Pass',
      winter: 'Winter Battle Pass'
    },
    case: {
      default: 'Standard case',
      vehicles: 'Car case',
      halloween: 'Halloween case',
      spring: 'Spring case',
      autumn: 'Fall case',
      summer: 'Summer case',
      summerExtra: 'A special summer case',
      summerSkins: 'Summer skin case',
      summerVehicles: 'Summer transportation case',
      winter: 'Winter case',
      winterExtra: 'A special winter case',
      winterSkins: 'Winter case with skins',
      winterVehicles: 'Winter transportation case',
      toys: 'Toy case'
    },
    subscription: { premium: 'Majestic Premium' },
    copy: 'Copy'
  },
  footer: { disclaimer: 'All information on the site is for informational purposes only and does not constitute a public offer.' },
  footer: { disclaimer: 'All information on the site is for informational purposes only and does not constitute a public offer.' },
  editor: {
    placeholder: 'Let\'s write an awesome post!',
    text: 'Paragraph',
    heading: 'Heading',
    warning: 'Note',
    quote: 'Quote',
    code: 'Code',
    delimiter: 'Separator',
    link: 'Reference',
    linkPlaceholder: 'Enter the link',
    marker: 'Marker',
    bold: 'Bold',
    italic: 'Italics',
    inlineCode: 'Monospaced',
    underline: 'Underline',
    copytext: 'Copyable',
    log: 'Log',
    map: {
      name: 'Map',
      params: 'Enter the card parameters',
      caption: 'Enter a description of the card'
    },
    table: {
      name: 'Table',
      addColumnLeft: 'Add a column on the left',
      addColumnRight: 'Add column on the right',
      deleteColumn: 'Delete column',
      addRowAbove: 'Add a line at the top',
      addRowBelow: 'Add a line at the bottom',
      deleteRow: 'Delete string',
      withHeadings: 'With the headlines',
      withoutHeadings: 'No headlines'
    },
    image: {
      name: 'Image',
      withBorder: 'With frame',
      stretchImage: 'Stretch the picture',
      withBackground: 'With the background',
      selectImage: 'Select an image',
      uploadError: 'Failed to load the image, try a different one',
      caption: 'Image description',
      withCaption: 'With a description'
    },
    carousel: {
      name: 'Image grid',
      caption: 'Image description',
      addImage: 'Select an image'
    },
    list: {
      name: 'List',
      ordered: 'Numbered',
      unordered: 'Unnumbered',
      checklist: 'Checklist'
    },
    header: {
      heading1: 'Heading H1',
      heading2: 'H2 Header',
      heading3: 'Heading H3',
      heading4: 'Header H4'
    },
    embed: { caption: 'Link Description' },
    convertTo: 'Convert to',
    moveUp: 'Move up',
    moveDown: 'Move down',
    anchor: 'Anchor',
    clickToTune: 'Press to customize',
    dragToMove: 'or drag',
    filter: 'Search',
    nothingFound: 'Nothing found'
  },
  alerts: {
    loginSuccess: 'Successful login',
    loginError: 'Error during authorization',
    registerSuccess: 'Successful registration on the website',
    registerError: 'Registration error',
    restoreSuccess: 'Recovery instructions have been sent to your email',
    restoreError: 'Error when restoring access to the account',
    signoutSuccess: 'Successful logout',
    postLoadError: 'Error loading the post',
    postShareSuccess: 'The link to the post is copied to the clipboard',
    copyCommandSuccess: 'The command is copied to the clipboard',
    restoreAccountSuccess: 'Account recovery was successful',
    restoreAccountError: 'Error when restoring access to the account',
    verificateAccountSuccess: 'Mailbox successfully confirmed',
    verificateAccountError: 'Mailbox confirmation error',
    dataUpdateSuccess: 'Successful update of information',
    dataUpdateError: 'Error when updating information',
    dataDeleteSuccess: 'Successful deletion of information',
    dataDeleteError: 'Error when deleting information',
    dataAddSuccess: 'Successful addition of information',
    dataAddError: 'Error when adding information',
    mapMoveInfo: 'Move the point to another position and save',
    mapCoordinatesCopySuccess: 'Coordinates successfully copied',
    copyTextSuccess: 'Text successfully copied'
  },
  mail: {
    verificate: {
      subject: 'Confirmation of e-mail address',
      title: 'Confirmation',
      description: 'To verify and confirm the rights to your account, you need to click on the button below. The link is active for 24 hours.'
    },
    restore: {
      subject: 'Recover account access',
      title: 'Recovery',
      description: 'To recover access to your account, you need to click on the button below. The link is active for 24 hours.'
    },
    restoreSuccess: {
      subject: 'Successfully recovery access to the account',
      title: 'Successful recovery',
      description: 'You have successfully changed your password on the site. If you did not, please change your password immediately!'
    },
    verificateSuccess: {
      subject: 'Successfully recovering access to the account',
      title: 'Successful confirmation',
      description: 'You have successfully confirmed your mailbox, welcome!'
    },
    caption: 'The email is a notification and is automatically generated, please do not reply to it.'
  },
  fields: {
    price: 'Cost',
    buyPrice: 'Purchase cost',
    sellPrice: 'Cost of sale',
    mcPrice: 'Cost of purchase in MC',
    money: 'Currency',
    coin: 'Majestic Coin',
    gift: 'Gift',
    default: 'Default',
    unique: 'Unique',
    event: 'Event',
    battlePass: 'Battle Pass',
    lootbox: 'Case',
    capacityKg: 'Capacity',
    email: 'Mail',
    password: 'Password',
    username: 'Nickname',
    weightKg: 'kg',
    speed: 'Speed',
    maxSpeed: 'Maximum speed',
    maxSpeedFT: 'Maximum speed (FT)',
    accelerationTo100: 'Acceleration to 100 km/h',
    speedSystem: 'km/h',
    timeSystem: 'sec',
    uploadDescription: 'Touch or drag the image to an area',
    placeholder: 'Enter a value in the field',
    imagePlaceholder: 'Enter a link to the picture',
    selectPlaceholder: 'Select a value from the list',
    videoPlaceholder: 'Enter a link to the video',
    soundPlaceholder: 'Enter a link to the audio',
    id: 'ID',
    serial: 'Serial number',
    kind: 'Type',
    category: 'Category',
    name: 'Title',
    view: 'Type',
    layer: 'Layer',
    icon: 'Icon',
    circle: 'Point',
    polygon: 'Polygon',
    emoji: 'Emoji',
    description: 'Description',
    gender: 'Gender',
    value: 'Value type',
    valueName: 'Additional price description',
    visibility: 'Availability',
    visibilityPrivate: 'Limited access',
    visibilityPublic: 'Visibility for all',
    activityHidden: 'Hidden from public access',
    activityVisible: 'Available to all',
    garageSlots: 'Garage slots',
    maxTenants: 'Number of residents',
    gasoline: 'Fuel',
    brand: 'Brand',
    model: 'Model',
    trunk: 'Trunk',
    tradable: 'Transferability',
    buyable: 'Purchase option',
    upgradesPriceFT: 'Available Improvements (FT)',
    upgradesEngine: 'Engine',
    upgradesBreaks: 'Brakes',
    upgradesSuspension: 'Pendant',
    upgradesTurbo: 'Turbo',
    upgradesTransmission: 'Box',
    paintingsMainPrice: 'Basic painting',
    paintingsSubPrice: 'Additional painting',
    paintingsOtherPrice: 'Other painting',
    paintingsBrightMetallic: 'Bright metallic',
    paintingsMetallic: 'Metallic',
    paintingsRichMetallic: 'Rich metallic',
    paintingsDarkMetallic: 'Dark metallic',
    paintingsMatte: 'Matte',
    paintingsMatteMetallic: 'Matte Metal',
    paintingsSatin: 'Satin',
    paintingsMetal: 'Metal',
    paintingsShadowChrome: 'Shadow chrome',
    paintingsPureChrome: 'Pure chrome',
    paintingsPearl: 'Pearlescent hue',
    paintingsWheels: 'Wheel color',
    paintingsDetails: 'Color of parts',
    paintingsAdditionalDetails: 'Additional color of parts',
    paintingsLights: 'Headlamp light',
    paintingsSmoke: 'The color of the tire smoke',
    paintingsNeon: 'Color of neon',
    total: 'Total',
    language: 'Language',
    radius: 'Radius',
    color: 'Colour',
    colorPlaceholder: 'Choose a color',
    opacity: 'Transparency',
    alias: 'Alias',
    aliases: 'Aliases',
    looped: 'Looped',
    battlePassId: 'Battle Pass ID',
    gasolineCapacityLiter: 'l',
    gasolineCapacity: 'Tank capacity',
    source: 'Source',
    sources: 'Sources',
    priceDescription: 'Cost Description',
    layerName: 'Layer name',
    layerNamePlaceholder: 'Enter a name for the layer',
    layerDescription: 'Layer description',
    layerDescriptionPlaceholder: 'Enter a description for the layer',
    layerColor: 'Layer color',
    layerColorPlaceholder: 'Choose a color',
    layerOpacity: 'Layer Transparency',
    zIndexOffset: 'Z-index level',
    zIndexOffsetPlaceholder: 'Enter the value of the Z-index level',
    comment: 'Comment'
  },
  vehiclesPage: {
    meta: {
      title: 'GTA5 RP Vehicles and Transport | Majestic Wiki',
      description: 'Full catalog of vehicles on the server Majestic RP in GTA5 RP. Detailed characteristics of cars, motorcycles, helicopters, airplanes and boats. Find out prices, speed, maneuverability, trunk capacity and other parameters. Majestic Motors, Cars, Trucks, Helicopters, Boats, Motorcycles, Bicycles - all for GTA5 RP!',
      custom: {
        title: '{type} | Transportation GTA5 RP | Majestic Wiki',
        description: 'Detailed catalog {type} on the server Majestic RP in GTA5 RP. Characteristics, prices, speed, maneuverability and other parameters. Full information about transport for GTA5 RP!'
      }
    },
    default: 'Default',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Case',
    unique: 'Unique',
    title: 'Transportation table',
    search: 'Finding Transportation...',
    irl: 'Majestic Motors',
    car: 'Passenger cars',
    freight: 'Cargo',
    helicopter: 'Helicopters',
    plane: 'Planes',
    boat: 'Boats',
    motorbike: 'Motorcycles',
    bike: 'Bicycles',
    trailer: 'trailers',
    military: 'Military',
    special: 'Specialty',
    priceDisclaimer: 'The final cost may vary due to a commission in the LSC on your server',
    basicInformation: 'Basic information',
    additionalInformation: 'Additional information',
    compare: 'Compare',
    compareLimit: 'A maximum of 5 TCs can be compared',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{transportation} few{transportation} many{transportation} other{transportation}}'
  },
  vehiclePage: {
    meta: {
      title: 'GTA5 RP Transportation | Majestic Wiki',
      description: 'Detailed information about transportation on the server Majestic RP in GTA5 RP. Characteristics, prices, improvements and paint. Learn about the maximum speed, acceleration, trunk capacity, cost of improvements and other parameters of each vehicle.',
      custom: {
        title: '{name} | Transportation GTA5 RP | Majestic Wiki',
        description: 'Detailed information about {name} on the server Majestic RP in GTA5 RP. Features, prices, improvements and paint jobs. Learn about top speed, acceleration, trunk capacity, cost of improvements and other parameters.'
      }
    },
    backToTable: 'Back to list',
    labelsUniqueId: 'Unique ID',
    labelsPrice: 'Cost',
    labelsBuyPrice: 'Purchase cost',
    labelsSellPrice: 'Cost of sale',
    labelsMaxSpeed: 'Maximum speed',
    labelsMaxSpeedFT: 'Maximum speed (FT)',
    labelsAccelerationTo100: 'Acceleration to 100 km/h',
    labelsTradable: 'Transferability',
    labelsTrunk: 'Trunk capacity',
    labelsGasolineCapacity: 'Tank capacity',
    labelsGasolineType: 'Fuel type',
    upgradesTitle: 'Transportation improvements',
    upgradesDescription: 'Cost of upgrades to the maximum level',
    upgradesEngine: 'Engine',
    upgradesEngineDescription: 'Unlock the true potential of transportation',
    upgradesBreaks: 'Brakes',
    upgradesBreaksDescription: 'Full speed control on any road',
    upgradesTurbo: 'Turbo',
    upgradesTurboDescription: 'Explosive acceleration at any moment',
    upgradesTransmission: 'Box',
    upgradesTransmissionDescription: 'Lightning-fast shifting of all gears',
    upgradesSuspension: 'Pendant',
    upgradesSuspensionDescription: 'Perfect comfort at the right height',
    upgradesTotal: 'Cost of improvements',
    upgradesDisclaimer: 'The final cost may vary due to a commission in the LSC on your server',
    colorsTitle: 'Vehicle painting',
    colorsDescription: 'Available types and kinds of painting with cost',
    paintingsMainPrice: 'Basic painting',
    paintingsSubPrice: 'Additional painting',
    paintingsOtherPrice: 'Other painting',
    paintingsBrightMetallic: 'Bright metallic',
    paintingsMetallic: 'Metallic',
    paintingsRichMetallic: 'Rich metallic',
    paintingsDarkMetallic: 'Dark metallic',
    paintingsMatte: 'Matte',
    paintingsMatteMetallic: 'Matte Metal',
    paintingsSatin: 'Satin',
    paintingsMetal: 'Metal',
    paintingsShadowChrome: 'Shadow chrome',
    paintingsPureChrome: 'Pure chrome',
    paintingsPearl: 'Pearlescent hue',
    paintingsWheels: 'Wheel color',
    paintingsDetails: 'Color of parts',
    paintingsAdditionalDetails: 'Additional color of parts',
    paintingsLights: 'Headlamp light',
    paintingsSmoke: 'The color of the tire smoke',
    paintingsNeon: 'Color of neon',
    paintingsTotal: 'Final painting cost',
    shareLink: 'Share the link to transportation',
    shareLinkCopied: 'The link to transportation has been successfully copied',
    addToCompare: 'Add to compare',
    addedToCompare: 'Transportation has been added to comparison',
    goToCompare: 'Go to compare',
    removeFromCompare: 'Remove from comparison',
    removedFromCompare: 'Transportation has been removed from the comparison'
  },
  vehiclesComparePage: {
    meta: {
      title: 'GTA5 RP Transportation Comparison | Majestic Wiki',
      description: 'Compare the features and prices of different vehicles on the Majestic RP server in GTA5 RP. Find out which vehicle best suits your needs! Compare speed, price, capacity and other parameters.'
    },
    name: 'Title',
    type: 'Type',
    brand: 'Brand',
    kits: 'Options',
    model: 'Model',
    trunkCapacity: 'Trunk capacity',
    maxSpeed: 'Maximum speed',
    maxSpeedFT: 'Maximum speed (FT)',
    price: 'Cost',
    donatePrice: 'Value in Majestic Coin',
    sellPrice: 'Cost of sale',
    sellPriceBeforeTax: 'Cost of sale (before tax)',
    sellPriceWithDiscount: 'Cost of sale (with discount)',
    accelerationTo100: 'Acceleration time to 100 km/h',
    gasolineCapacity: 'Tank capacity',
    gasolineType: 'Fuel type',
    isTradable: 'Transferability',
    isBuyable: 'Purchase option',
    createdAt: 'Date of Appearance',
    gasolineCapacityUnit: 'l',
    maxSpeedUnit: 'km/h',
    maxSpeedFTUnit: 'km/h',
    priceUnit: '₽',
    accelerationTo100Unit: 'sec',
    trunkCapacityUnit: 'kg',
    showOnlyDifferent: 'Show only those that are different',
    showAll: 'Show all',
    share: 'Share a comparison',
    linkCopied: 'The comparison link has been successfully copied',
    openVehicle: 'Open the transportation page',
    deleteVehicle: 'Remove a vehicle from the comparison',
    noDifferences: 'No difference'
  },
  skinsPage: {
    meta: {
      title: 'GTA5 RP Weapon Skins | Majestic Wiki',
      description: 'Full catalog of weapon skins on the server Majestic RP in GTA5 RP. Learn about how to get, cost and features of each skin for different types of weapons.',
      custom: {
        title: 'Skins for {type} | GTA5 RP | Majestic Wiki',
        description: 'Catalog skins for {type} on the server Majestic RP in GTA5 RP. Detailed information about how to get, cost and features of skins.'
      }
    },
    title: 'Skin table',
    search: 'Skin Search...',
    apPistol: 'Armored pistol',
    assaultRifle: 'Assault Rifle',
    assaultRifleMk2: 'Mk2 Assault Rifle',
    bullpupRifle: 'Bullpup rifle',
    carbineRifle: 'Carbine rifle',
    carbineRifleMk2: 'Mk2 carbine rifle',
    combatPDW: 'Combat PDW',
    gusenberg: 'Thompson submachine gun.',
    heavyPistol: 'Heavy pistol',
    heavyShotgun: 'Heavy Shotgun',
    heavySniper: 'Heavy Sniper Rifle',
    heavySniperMk2: 'Mk2 Heavy Sniper Rifle',
    lightvest: 'Bulletproof vest',
    machinePistol: 'Small PP',
    marksmanPistol: 'Marksman gun',
    microSMG: 'Micro SMG',
    militaryRifle: 'Military Rifle',
    revolver: 'Revolver',
    smgMk2: 'SMG Mk2',
    specialCarbine: 'Special carbine',
    vintagePistol: 'Vintage Pistol',
    combatMgMk2: 'Mk2 submachine gun',
    heavyRifle: 'Heavy rifle',
    tacticalRifle: 'Tactical Rifle',
    tecPistol: 'Tactical SMG',
    noName: 'Skin #{id}',
    battlePass: 'Battle Pass',
    case: 'Case',
    unique: 'Unique',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{skin} few{skin} many{skins} other{skins}}'
  },
  animationsPage: {
    meta: {
      title: 'GTA5 RP Animations | Majestic Wiki',
      description: 'Full catalog of animations on the server Majestic RP in GTA5 RP. All types of animations: actions, poses, dances and exclusive animations. Find out how to get and the cost of each animation.',
      custom: {
        title: '{type} | GTA5 RP Animations | Majestic Wiki',
        description: 'Catalog {type} animations on the server Majestic RP in GTA5 RP. Detailed information about how to get, cost and use animations.'
      }
    },
    title: 'Animation table',
    search: 'Animation Search...',
    default: 'Default',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Case',
    unique: 'Unique',
    action: 'Actions',
    pose: 'Poses',
    positive: 'Positive',
    negative: 'Negative',
    dances: 'Dances',
    etc: 'Other',
    exclusive: 'Exclusive',
    noName: 'Animation #{id}',
    pagingText: 'Найден{N, plural, one{а} few{о} many{о}} {N} {N, plural, one{анимация} few{анимации} many{анимаций} other{анимаций}}'
  },
  realtyPage: {
    meta: {
      title: 'GTA5 RP Real Estate | Majestic Wiki',
      description: 'Full catalog of real estate on the server Majestic RP in GTA5 RP. Houses, apartments, offices and warehouses with detailed characteristics, prices and location. Find out the number of tenants, garage spaces and other parameters.',
      custom: {
        title: '{type} | GTA5 RP Real Estate | Majestic Wiki',
        description: 'Catalog {type} on the server Majestic RP in GTA5 RP. Detailed information about the characteristics, prices, location and features of real estate.'
      }
    },
    title: 'Real estate table',
    search: 'Real Estate Search...',
    majesticMotors: 'Majestic Motors',
    house: 'Houses',
    apartment: 'Appartments',
    office: 'Offices',
    warehouse: 'Warehouses',
    tenantsFor: '{N} {N, plural, one{tenant} few{tenant} many{tenants} other{tenants}}',
    garageSlotsFor: '{N} {N, plural, one{garage space} few{garage spaces} many{garage spaces} other{garage spaces}}',
    viewOnMap: 'View on map',
    mapPosition: 'Location',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{real estate} few{real estate} many{real estate} other{real estate}}',
    noName: 'Real Estate #{id}'
  },
  postsPage: {
    meta: {
      title: 'Editing a post | Majestic Wiki',
      description: 'Create and edit posts on the Majestic Wiki for GTA5 RP. Publishing up-to-date information about transportation, real estate, businesses and other aspects of the game.'
    },
    titlePlaceholder: 'Enter the title of the post',
    category: 'Category',
    noCategory: 'Uncategorized',
    availability: 'Availability',
    allView: 'Visibility for all',
    privateView: 'Limited access',
    relevance: 'Relevance',
    relevanceData: 'Up-to-date information',
    outdatedData: 'Outdated information',
    categoryPosition: 'Category position',
    categoryPositionDescription: 'For example, "50" or "-5."',
    postDeleted: 'Post deleted',
    author: 'Author of the article',
    editor: 'Article Editor',
    contents: 'Article Content',
    readingTime: 'Reading time',
    readingTimeLength: '{N} {N, plural, one{minute} few{minutes} many{minutes} other{minutes}}',
    publishDate: 'Publication from {date}',
    views: '{N} {N, plural, one{views} few{views} many{views} other{views}}',
    youLiked: 'You liked',
    youAndPeopleLiked: 'You liked {likes}',
    peopleLiked: '{N} {N, plural, one{likes} few{likes} many{likes} other{likes}}',
    categories: 'Categories',
    posts: 'Posts',
    foundMistake: 'Found an error in the article?',
    language: 'Language'
  },
  panelPage: {
    users: {
      title: 'User Management | Majestic Wiki',
      heading: 'User management',
      admin: 'Administrator',
      user: 'User',
      customUser: 'Authorized user',
      search: 'Start typing your search query....',
      comment: 'Comment',
      pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{user} few{user} many{users} other{users}}'
    },
    posts: {
      title: 'Post Management | Majestic Wiki',
      heading: 'Post management',
      views: '{N} {N, plural, one{views} few{views} many{views} other{views}}',
      likes: '{N} {N, plural, one{likes} few{likes} many{likes} other{likes}}'
    },
    categories: {
      title: 'Category Management | Majestic Wiki',
      heading: 'Category Management'
    },
    mapLayers: {
      title: 'Managing map layers | Majestic Wiki',
      heading: 'Managing map layers'
    },
    logs: {
      title: 'Log Auditing - Majestic Wiki',
      heading: 'Audit logs',
      search: 'Start typing your search query....',
      biz: 'Business',
      category: 'Category',
      clothing: 'Clothes',
      file: 'File',
      item: 'Item',
      mapItem: 'Map element',
      mapLayer: 'Map Layer',
      post: 'Post',
      realty: 'Real estate',
      skin: 'Skin',
      user: 'User',
      vehicle: 'Vehicles',
      animation: 'Animation',
      actions: {
        unknown: '{username} performed an unknown act',
        bizCreate: '{username} added a business {id}',
        bizUpdate: '{username} changed the business {id}',
        bizDelete: '{username} deleted the business {id}',
        categoryCreate: '{username} added a category {id}',
        categoryUpdate: '{username} changed the category {id}',
        categoryDelete: '{username} deleted category {id}',
        clothingCreate: '{username} added clothes {id}',
        clothingUpdate: '{username} changed clothes {id}',
        clothingDelete: '{username} deleted the clothes {id}',
        fileCreate: '{username} added a file {id}',
        fileUpdate: '{username} modified the file {id}',
        fileDelete: '{username} deleted the file {id}',
        itemCreate: '{username} added the item {id}',
        itemUpdate: '{username} changed the subject {id}',
        itemDelete: '{username} deleted the item {id}',
        mapItemCreate: '{username} added an item to the map {id} to "{layer}"',
        mapItemUpdate: '{username} changed the item on the map {id} to "{layer}"',
        mapItemDelete: '{username} deleted an item on the map {id} to "{layer}"',
        mapLayerCreate: '{username} added a map layer {id}',
        mapLayerUpdate: '{username} changed the map layer {id}',
        mapLayerDelete: '{username} deleted the map layer {id}',
        postCreate: '{username} added a post {id}',
        postUpdate: '{username} modified post {id}',
        postDelete: '{username} deleted post {id}',
        realtyCreate: '{username} added real estate {id}',
        realtyUpdate: '{username} changed real estate {id}',
        realtyDelete: '{username} deleted real estate {id}',
        skinCreate: '{username} added a skin {id}',
        skinUpdate: '{username} changed the skin {id}',
        skinDelete: '{username} deleted the skin {id}',
        userCreate: '{username} added user {id}',
        userUpdate: '{username} changed the user {id}',
        userDelete: '{username} deleted user {id}',
        vehicleCreate: '{username} added transportation {id}',
        vehicleUpdate: '{username} changed transportation {id}',
        vehicleDelete: '{username} deleted the transport {id}',
        animationCreate: '{username} added animation {id}',
        animationUpdate: '{username} changed the animation {id}',
        animationDelete: '{username} deleted the animation {id}',
        changelogCreate: '{username} added changelog {id}',
        changelogUpdate: '{username} changed the changelog {id}',
        changelogDelete: '{username} deleted changelog {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: 'Settings',
      users: 'Users',
      categories: 'Categories',
      mapLayers: 'Map Layers',
      posts: 'Posts',
      logs: 'Logs'
    }
  },
  changelogPage: {
    titlePlaceholder: 'Enter the name of the change',
    descriptionPlaceholder: 'Enter a description of the change',
    availableAt: 'Date of publication',
    soon: 'via',
    ago: 'back',
    fix: 'Corrected',
    feat: 'Added',
    chore: 'Modified',
    style: 'Design',
    refactor: 'Recycled'
  },
  changelogsPage: {
    meta: {
      title: 'History of changes | Majestic Wiki',
      description: 'Complete history of updates and changes to the Majestic RP server. Keep an eye out for new features, gameplay improvements, bug fixes and balancing. Up-to-date information about all project updates.'
    },
    title: 'Change history',
    search: 'Finding Change...'
  },
  homePage: {
    meta: {
      title: 'Majestic Wiki | GTA5 RP Encyclopedia',
      description: 'Majestic Wiki is a complete encyclopedia for GTA5 RP players. Information about transportation, real estate, businesses, quests and other aspects of the game. Up-to-date information on prices, features and how to obtain them.'
    },
    tools: {
      map: {
        title: 'Map',
        description: 'Server map with all locations'
      },
      vehicles: {
        title: 'Vehicles',
        description: 'All available transportation on the server'
      },
      realty: {
        title: 'Real estate',
        description: 'Data for all houses and apartments'
      },
      biz: {
        title: 'Businesses',
        description: 'Advanced earning option'
      },
      clothesMale: {
        title: 'Men\'s clothing',
        description: 'A fashion show for men'
      },
      clothesFemale: {
        title: 'Women\'s clothing',
        description: 'Fashion show for women'
      },
      skins: {
        title: 'Skins',
        description: 'Catalog of all available patterns'
      },
      items: {
        title: 'Items',
        description: 'Available items on the server'
      },
      servers: {
        title: 'Servers',
        description: 'List of all Majestic RP servers'
      },
      animations: {
        title: 'Animations',
        description: 'Unique animations on the server'
      },
      changelogs: {
        title: 'Change history',
        description: 'History of changes on the project'
      }
    },
    search: {
      title: 'General categories',
      description: 'Here you can find any information about the Majestic server and its systems'
    },
    categories: {
      collapseList: 'Fold list',
      expandList: 'Expand list'
    },
    online: {
      players: '{count} online',
      offline: 'Turned off'
    }
  },
  errorPage: {
    title: 'Page not found',
    description: 'You may have a typo in the page address, or it just doesn\'t exist :(',
    backToHome: 'Go to Home'
  },
  bizPage: {
    meta: {
      title: 'GTA5 RP Businesses | Majestic Wiki',
      description: 'Full catalog of businesses on the server Majestic RP in GTA5 RP. 24/7 stores, gas stations, gun stores, car dealerships and other businesses. Learn about the profitability, requirements and how to purchase each business.',
      custom: {
        title: '{type} | GTA5 RP Businesses | Majestic Wiki',
        description: 'Detailed information about {type} on the server Majestic RP in GTA5 RP. Characteristics, profitability, requirements and how to purchase a business.'
      }
    },
    title: 'Table of Businesses',
    search: 'Finding a Business...',
    groceryStore: 'Shop 24/7',
    gasStation: 'Gas station',
    atm: 'ATM',
    gunshop: 'Gun Shop',
    clothingStore: 'Clothing store',
    autoShop: 'Car Dealership',
    tattoo: 'Tattoo parlor',
    lsc: 'Tuning salon',
    barbershop: 'Barbershop',
    carWash: 'Car Wash',
    viewOnMap: 'View on map',
    mapPosition: 'Location',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{businesses} few{businesses} many{businesses} other{businesses}}',
    noName: 'Business #{id}'
  },
  clothesPage: {
    meta: {
      title: 'GTA5 RP clothing | Majestic Wiki',
      description: 'Full catalog of clothing on the server Majestic RP in GTA5 RP. Men\'s and women\'s clothing, accessories, backpacks, patches, hats and other closet items. Find out how to get and the cost of each item of clothing.',
      custom: {
        title: '{type} | GTA5 RP clothing | Majestic Wiki',
        description: 'Catalog {type} on the server Majestic RP in GTA5 RP. Detailed information about how to get, use and cost of clothing items.'
      }
    },
    titleMale: 'Men\'s clothing chart',
    titleFemale: 'Women\'s clothing chart',
    search: 'Apparel Search...',
    top: 'Top',
    legs: 'Bottom',
    shoes: 'Footwear',
    watch: 'Watch',
    mask: 'Masks',
    decal: 'Stripes',
    accessory: 'Accessories',
    head: 'Headgear',
    bag: 'Backpacks',
    glasses: 'Glasses',
    ear: 'Ears',
    gloves: 'Gloves',
    undershirt: 'Undershirts',
    noName: 'Clothing set #{id}',
    drawable: 'Drawable',
    texture: 'Texture',
    male: 'Male',
    female: 'Female',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Case',
    unique: 'Unique',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{clothing} few{clothing} many{clothing} other{clothing}}'
  },
  itemsPage: {
    meta: {
      title: 'GTA5 RP Items | Majestic Wiki',
      description: 'Full catalog of items on the server Majestic RP in GTA5 RP. Antiques, fish, ore, wood, crates, materials and other items. Learn about how to obtain, use and cost of each item.',
      custom: {
        title: '{type} | GTA5 RP Items | Majestic Wiki',
        description: 'Catalog {type} on the server Majestic RP in GTA5 RP. Detailed information about how to get, use and cost of items.'
      }
    },
    title: 'Table of items',
    search: 'Search items...',
    antique: 'Antiques',
    fish: 'Fish',
    ore: 'Ore',
    wood: 'Wood',
    box: 'Boxes',
    material: 'Materials',
    event: 'Event',
    food: 'Products',
    alcohol: 'Alcohol',
    illegal: 'Illegal',
    medical: 'Medicine',
    equipment: 'Equipment',
    ammunition: 'Ammunition',
    tool: 'Tools',
    unique: 'Unique',
    vehicle: 'Auto parts',
    others: 'Misc',
    default: 'Default',
    battlePass: 'Battle Pass',
    case: 'Case',
    unique: 'Unique',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{item} few{item} many{items} other{items}}',
    noName: 'Subject #{id}'
  },
  mapPage: {
    meta: {
      title: 'GTA5 RP map | Majestic Wiki',
      description: 'Interactive map of the Majestic RP server in GTA5 RP. Explore locations, find treasures, animals, phones and trash. Detailed map of the world with the ability to add tags and zones. All important places and points of interest in one resource!',
      custom: {
        title: '{type} | GTA5 RP Map | Majestic Wiki',
        description: 'A detailed map of {type} on the Majestic RP server in GTA5 RP. Explore locations, find important points and create your own markers. Full information about the location of objects on the map!'
      }
    },
    dataLoading: 'Data Loading...',
    defaultPoints: 'Standard tags',
    animalLayer: 'Animal Map',
    rubbishLayer: 'A map of the garbage cans',
    telephoneLayer: 'Phone map',
    treasureLayer: 'Treasure map',
    defaultLayer: 'World map',
    allLayers: 'Map Layers',
    backToHome: 'Go home',
    zoomIn: 'Zoom in',
    zoomOut: 'Zoom out',
    point: 'Point',
    polygon: 'Zone',
    addPoint: 'Add a point',
    addPolygon: 'Add a zone',
    copyCoordinates: 'Copy coordinates',
    savePointPosition: 'Save position',
    editPointPosition: 'Change position',
    layerNamePlaceholder: 'Zone name',
    layerDescriptionPlaceholder: 'Zone Description'
  },
  serversPage: {
    meta: {
      title: 'Online GTA5 RP servers | Majestic Wiki',
      description: 'Current statistics of online players on the servers Majestic RP in GTA5 RP. Current online, peaks, queues and activity graphs. Join the most popular role-playing project!'
    },
    dataLoading: 'Data Loading...',
    title: 'Online servers',
    online: '{count} online',
    inQueue: '{count} in line',
    peakOnline: '{count} peak',
    currentOnline: 'current online',
    peakOnline24h: 'peak today',
    peakOnlineAllTime: 'all-time peak',
    peakOnlineTooltip: '{percent}% relative to the previous day',
    techWorks: 'Technical work',
    forBeginners: 'For beginners',
    experience: 'Experience x{multiplier}',
    newServer: 'New',
    seasonPassMultiplier: 'Skip experience x{multiplier}',
    combinedView: 'General',
    separateView: 'Separate'
  },
  research: {
    placeholder: 'Search for information on the website...',
    skin: 'Skin',
    vehicle: 'Vehicles',
    realty: 'Real estate',
    biz: 'Business',
    clothing: 'Clothes',
    item: 'Item',
    post: 'Post'
  },
  userModal: {
    login: 'Log In',
    signout: 'Logout',
    register: 'Registration',
    registerEnd: 'Register',
    restore: 'Restoration',
    restoreEnd: 'Restore',
    avatarDelete: 'Delete avatar',
    headingUpdate: 'Changing the user #{id}',
    postAdd: 'Publishing posts',
    postUpdate: 'Editing posts',
    postDelete: 'Deleting posts',
    vehicleAdd: 'Adding transportation',
    vehicleUpdate: 'Editing transportation',
    vehicleDelete: 'Removal of transportation',
    realtyAdd: 'Adding real estate',
    realtyUpdate: 'Real estate change',
    realtyDelete: 'Removal of real estate',
    bizAdd: 'Adding a business',
    bizUpdate: 'Business change',
    bizDelete: 'Removing a business',
    mapItemAdd: 'Adding items to the map',
    mapItemUpdate: 'Changing elements on the map',
    mapItemDelete: 'Deleting items on the map',
    clothingAdd: 'Adding clothes',
    clothingUpdate: 'Change of clothes',
    clothingDelete: 'Clothing removal',
    skinAdd: 'Adding skins',
    skinUpdate: 'Changing skins',
    skinDelete: 'Removing skins',
    itemAdd: 'Adding items',
    itemUpdate: 'Changing items',
    itemDelete: 'Removal of objects',
    fileUpload: 'Uploading images',
    viewPrivate: 'View private data',
    doMagic: 'Access to magic functions',
    animationAdd: 'Adding animations',
    animationUpdate: 'Changing animations',
    animationDelete: 'Removing animations',
    categoryAdd: 'Adding categories',
    categoryUpdate: 'Changing categories',
    categoryDelete: 'Deleting categories',
    mapLayerAdd: 'Adding map layers',
    mapLayerUpdate: 'Changing map layers',
    mapLayerDelete: 'Deleting map layers',
    post: 'The post',
    skin: 'Skin',
    biz: 'Business',
    clothing: 'Clothes',
    item: 'Item',
    vehicle: 'Vehicles',
    animation: 'Animation',
    category: 'Category',
    realty: 'Real estate',
    mapLayer: 'Map Layer',
    changelog: 'Changelog'
  },
  restoreModal: {
    title: 'Account Restore',
    newPassword: 'New password',
    newPasswordRepeat: 'Repeat new password',
    passwordConfirm: 'Change password',
    passwordError: 'Passwords do not match'
  },
  bizModal: {
    titleUpdate: 'Changing Businesses #{id}',
    titleAdd: 'Adding a new business'
  },
  categoryModal: {
    titleUpdate: 'Changing the category #{id}',
    titleAdd: 'Adding a new category'
  },
  mapLayerModal: {
    titleUpdate: 'Changing the map layer #{id}',
    titleAdd: 'Adding a new map layer'
  },
  clothingModal: {
    titleUpdate: 'Changing clothes #{id}',
    titleAdd: 'Adding new clothes',
    itemAdd: 'Add color',
    itemGenerate: 'Generate all colors'
  },
  itemModal: {
    titleUpdate: 'Changing the subject #{id}',
    titleAdd: 'Adding a new item'
  },
  realtyModal: {
    titleUpdate: 'Real Estate Change #{id}',
    titleAdd: 'Adding new real estate'
  },
  skinModal: {
    titleUpdate: 'Changing the skin #{id}',
    titleAdd: 'Adding a new skin'
  },
  vehicleModal: {
    titleUpdate: 'Changing transportation #{id}',
    titleAdd: 'Adding new transportation',
    titleDefault: 'Information about {name}',
    itemAdd: 'Add a dodger'
  },
  mapItemModal: {
    titleUpdate: 'Changing a map element',
    titleAdd: 'Adding a new map element',
    titleDelete: 'Deleting a map item',
    descriptionDelete: 'Are you sure you want to remove the item from the map? This action cannot be undone.'
  },
  animationModal: {
    titleUpdate: 'Changing the animation #{id}',
    titleAdd: 'Adding a new animation'
  }
};