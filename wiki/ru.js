export default {
  global: {
    all: 'Все',
    back: 'Назад',
    forward: 'Вперед',
    paging: 'Стран<PERSON><PERSON><PERSON> {page} из {maxPage}',
    close: 'Закрыть',
    clear: 'Очистить',
    save: 'Со<PERSON>ранить',
    delete: 'Удалить',
    deleteYes: 'Вы уверены в удалении?',
    cancel: 'Отменить',
    publish: 'Опубликовать',
    edit: 'Редактировать',
    share: 'Поделиться',
    add: 'Добавить',
    active: 'Активный',
    deleted: 'Удален',
    blocked: 'Заблокирован',
    learnMore: 'Узнать подробнее',
    panel: 'Панель',
    theme: 'Тема',
    language: 'Язык',
    user: 'Пользователь',
    confirm: 'Подтвердить',
    restore: 'Восстановление',
    yes: 'Да',
    no: 'Нет',
    new: 'Новый',
    preview: 'Предпросмотр',
    sortPriceDesc: 'По убыванию цены',
    sortPriceAsc: 'По возрастанию цены',
    sortPriceMinMax: 'Диапазон цены',
    sortDateDesc: 'По убыванию даты',
    sortDateAsc: 'По возрастанию даты',
    noSellPrice: 'Не для покупки',
    noBuyPrice: 'Не продается',
    noData: {
      title: 'Ничего не найдено',
      description: 'Попробуйте изменить или упростить поисковой запрос'
    },
    battlePass: {
      summer: 'Летний Battle Pass',
      winter: 'Зимний Battle Pass'
    },
    case: {
      default: 'Стандартный кейс',
      vehicles: 'Автомобильный кейс',
      halloween: 'Хеллоуинский кейс',
      spring: 'Весенний кейс',
      autumn: 'Осенний кейс',
      summer: 'Летний кейс',
      summerExtra: 'Особенный летний кейс',
      summerSkins: 'Летний кейс со скинами',
      summerVehicles: 'Летний кейс с транспортом',
      winter: 'Зимний кейс',
      winterExtra: 'Особенный зимний кейс',
      winterSkins: 'Зимний кейс со скинами',
      winterVehicles: 'Зимний кейс с транспортом',
      toys: 'Кейс с игрушками'
    },
    subscription: {
      premium: 'Majestic Premium'
    },
    copy: 'Скопировать'
  },
  footer: {
    disclaimer: 'Вся информация, размещённая на сайте, носит исключительно ознакомительный характер и не является публичной офертой.'
  },
  footer: {
    disclaimer: 'Вся информация, размещённая на сайте, носит исключительно ознакомительный характер и не является публичной офертой.'
  },
  editor: {
    placeholder: 'Давай напишем классный пост!',
    text: 'Параграф',
    heading: 'Заголовок',
    warning: 'Примечание',
    quote: 'Цитата',
    code: 'Код',
    delimiter: 'Разделитель',
    link: 'Ссылка',
    linkPlaceholder: 'Введите ссылку',
    marker: 'Маркер',
    bold: 'Полужирный',
    italic: 'Курсив',
    inlineCode: 'Моноширинный',
    underline: 'Подчеркнутый',
    copytext: 'Копируемый',
    log: 'Лог',
    map: {
      name: 'Карта',
      params: 'Введите параметры карты',
      caption: 'Введите описание карты'
    },
    table: {
      name: 'Таблица',
      addColumnLeft: 'Добавить колонку слева',
      addColumnRight: 'Добавить колонку справа',
      deleteColumn: 'Удалить колонку',
      addRowAbove: 'Добавить строку сверху',
      addRowBelow: 'Добавить строку снизу',
      deleteRow: 'Удалить строку',
      withHeadings: 'С заголовками',
      withoutHeadings: 'Без заголовков'
    },
    image: {
      name: 'Изображение',
      withBorder: 'С рамкой',
      stretchImage: 'Растянуть картинку',
      withBackground: 'С фоном',
      selectImage: 'Выберите изображение',
      uploadError: 'Не удалось загрузить изображение, попробуйте другое',
      caption: 'Описание изображения',
      withCaption: 'С описанием'
    },
    carousel: {
      name: 'Сетка изображений',
      caption: 'Описание изображения',
      addImage: 'Выберите изображение'
    },
    list: {
      name: 'Список',
      ordered: 'Нумерованный',
      unordered: 'Ненумерованный',
      checklist: 'Чеклист'
    },
    header: {
      heading1: 'Заголовок H1',
      heading2: 'Заголовок H2',
      heading3: 'Заголовок H3',
      heading4: 'Заголовок H4'
    },
    embed: {
      caption: 'Описание ссылки'
    },
    convertTo: 'Конвертировать в',
    moveUp: 'Переместить вверх',
    moveDown: 'Переместить вниз',
    anchor: 'Якорь',
    clickToTune: 'Нажмите, чтобы настроить',
    dragToMove: 'или перетащите',
    filter: 'Поиск',
    nothingFound: 'Ничего не найдено'
  },
  alerts: {
    loginSuccess: 'Успешный вход в систему',
    loginError: 'Ошибка при авторизации',
    registerSuccess: 'Успешная регистрация на сайте',
    registerError: 'Ошибка при регистрации',
    restoreSuccess: 'Инструкции по восстановлению отправлены на почту',
    restoreError: 'Ошибка при восстановлении доступа к аккаунту',
    signoutSuccess: 'Успешный выход из системы',
    postLoadError: 'Ошибка при загрузке поста',
    postShareSuccess: 'Ссылка на пост скопирована в буфер обмена',
    copyCommandSuccess: 'Команда скопирована в буфер обмена',
    restoreAccountSuccess: 'Восстановление аккаунта прошло успешно',
    restoreAccountError: 'Ошибка при восстановлении доступа к аккаунту',
    verificateAccountSuccess: 'Почтовый ящик успешно подтвержден',
    verificateAccountError: 'Ошибка подтверждения почтового ящика',
    dataUpdateSuccess: 'Успешное обновление информации',
    dataUpdateError: 'Ошибка при обновлении информации',
    dataDeleteSuccess: 'Успешное удаление информации',
    dataDeleteError: 'Ошибка при удалении информации',
    dataAddSuccess: 'Успешное добавление информации',
    dataAddError: 'Ошибка при добавлении информации',
    mapMoveInfo: 'Переместите точку в другую позицию и сохраните',
    mapCoordinatesCopySuccess: 'Координаты успешно скопированы',
    copyTextSuccess: 'Текст успешно скопирован'
  },
  mail: {
    verificate: {
      subject: 'Подтверждение адреса электронной почты',
      title: 'Подтверждение',
      description:
        'Чтобы пройти верификацию и подтвердить права на свой аккаунт, необходимо нажать на кнопку ниже. Ссылка активна в течении 24 часов.'
    },
    restore: {
      subject: 'Восстановление доступа к аккаунту',
      title: 'Восстановление',
      description: 'Чтобы восстановить доступ к своему аккаунту, необходимо нажать на кнопку ниже. Ссылка активна в течении 24 часов.'
    },
    restoreSuccess: {
      subject: 'Успешное восстановление доступа к аккаунту',
      title: 'Успешное восстановление',
      description: 'Вы успешно сменили пароль на сайте. Если это сделали не вы, немедлено смените пароль!'
    },
    verificateSuccess: {
      subject: 'Успешное восстановление доступа к аккаунту',
      title: 'Успешное подтверждение',
      description: 'Вы успешно подтвердили почтовый ящик, добро пожаловать!'
    },
    caption: 'Письмо является уведомлением и создано автоматически, пожалуйста, не отвечайте на него.'
  },
  fields: {
    price: 'Стоимость',
    buyPrice: 'Стоимость покупки',
    sellPrice: 'Стоимость продажи',
    mcPrice: 'Стоимость покупки в MC',
    money: 'Валюта',
    coin: 'Majestic Coin',
    gift: 'Подарок',
    default: 'Стандартный',
    unique: 'Уникальный',
    event: 'Ивент',
    battlePass: 'Battle Pass',
    lootbox: 'Кейс',
    capacityKg: 'Вместимость',
    email: 'Почта',
    password: 'Пароль',
    username: 'Никнейм',
    weightKg: 'кг',
    speed: 'Скорость',
    maxSpeed: 'Максимальная скорость',
    maxSpeedFT: 'Максимальная скорость (FT)',
    accelerationTo100: 'Разгон до 100 км/ч',
    speedSystem: 'км/ч',
    timeSystem: 'сек',
    uploadDescription: 'Нажмите или перетащите изображение в зону',
    placeholder: 'Введите значение в поле',
    imagePlaceholder: 'Введите ссылку на картинку',
    selectPlaceholder: 'Выберите значение из списка',
    videoPlaceholder: 'Введите ссылку на видео',
    soundPlaceholder: 'Введите ссылку на аудио',
    id: 'ID',
    serial: 'Порядковый номер',
    kind: 'Тип',
    category: 'Категория',
    name: 'Название',
    view: 'Вид',
    layer: 'Слой',
    icon: 'Иконка',
    circle: 'Точка',
    polygon: 'Полигон',
    emoji: 'Emoji',
    description: 'Описание',
    gender: 'Гендер',
    value: 'Тип стоимости',
    valueName: 'Дополнительное описание цены',
    visibility: 'Доступность',
    visibilityPrivate: 'Ограниченный доступ',
    visibilityPublic: 'Видимость для всех',
    activityHidden: 'Скрыт из общего доступа',
    activityVisible: 'Доступен всем',
    garageSlots: 'Гаражные слоты',
    maxTenants: 'Количество проживающих',
    gasoline: 'Топливо',
    brand: 'Бренд',
    model: 'Модель',
    trunk: 'Багажник',
    tradable: 'Возможность передачи',
    buyable: 'Возможность покупки',
    upgradesPriceFT: 'Доступные улучшения (FT)',
    upgradesEngine: 'Двигатель',
    upgradesBreaks: 'Тормоза',
    upgradesSuspension: 'Подвеска',
    upgradesTurbo: 'Турбо',
    upgradesTransmission: 'Коробка',
    paintingsMainPrice: 'Основная покраска',
    paintingsSubPrice: 'Дополнительная покраска',
    paintingsOtherPrice: 'Прочая покраска',
    paintingsBrightMetallic: 'Яркий металлик',
    paintingsMetallic: 'Металлик',
    paintingsRichMetallic: 'Насыщенный металлик',
    paintingsDarkMetallic: 'Тёмный металлик',
    paintingsMatte: 'Матовый',
    paintingsMatteMetallic: 'Матовый металл',
    paintingsSatin: 'Сатин',
    paintingsMetal: 'Металл',
    paintingsShadowChrome: 'Теневой хром',
    paintingsPureChrome: 'Чистый хром',
    paintingsPearl: 'Оттенок перламутра',
    paintingsWheels: 'Цвет дисков',
    paintingsDetails: 'Цвет деталей',
    paintingsAdditionalDetails: 'Доп. цвет деталей',
    paintingsLights: 'Свет фар',
    paintingsSmoke: 'Цвет дыма покрышек',
    paintingsNeon: 'Цвет неона',
    total: 'Итого',
    language: 'Язык',
    radius: 'Радиус',
    color: 'Цвет',
    colorPlaceholder: 'Выберите цвет',
    opacity: 'Прозрачность',
    alias: 'Псевдоним',
    aliases: 'Псевдонимы',
    looped: 'Зацикленный',
    battlePassId: 'Battle Pass ID',
    gasolineCapacityLiter: 'л',
    gasolineCapacity: 'Вместимость бака',
    source: 'Источник',
    sources: 'Источники',
    priceDescription: 'Описание стоимости',
    layerName: 'Название слоя',
    layerNamePlaceholder: 'Введите название слоя',
    layerDescription: 'Описание слоя',
    layerDescriptionPlaceholder: 'Введите описание слоя',
    layerColor: 'Цвет слоя',
    layerColorPlaceholder: 'Выберите цвет',
    layerOpacity: 'Прозрачность слоя',
    zIndexOffset: 'Уровень Z-индекса',
    zIndexOffsetPlaceholder: 'Введите значение уровня Z-индекса',
    comment: 'Комментарий'
  },
  vehiclesPage: {
    meta: {
      title: 'Транспорт и автомобили GTA5 RP | Majestic Wiki',
      description:
        'Полный каталог транспорта на сервере Majestic RP в GTA5 RP. Подробные характеристики автомобилей, мотоциклов, вертолетов, самолетов и лодок. Узнайте цены, скорость, маневренность, вместимость багажника и другие параметры. Majestic Motors, Легковые, Грузовые, Вертолеты, Лодки, Мотоциклы, Велосипеды - все для GTA5 RP!',
      custom: {
        title: '{type} | Транспорт GTA5 RP | Majestic Wiki',
        description:
          'Подробный каталог {type} на сервере Majestic RP в GTA5 RP. Характеристики, цены, скорость, маневренность и другие параметры. Полная информация о транспорте для GTA5 RP!'
      }
    },
    default: 'Стандарт',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Кейс',
    unique: 'Уникальный',
    title: 'Таблица транспорта',
    search: 'Поиск транспорта...',
    irl: 'Majestic Motors',
    car: 'Легковые',
    freight: 'Грузовые',
    helicopter: 'Вертолеты',
    plane: 'Самолеты',
    boat: 'Лодки',
    motorbike: 'Мотоциклы',
    bike: 'Велосипеды',
    trailer: 'Прицепы',
    military: 'Военные',
    special: 'Специальные',
    priceDisclaimer: 'Итоговая стоимость может отличаться из-за комиссии в LSC на вашем сервере',
    basicInformation: 'Основная информация',
    additionalInformation: 'Доп. информация',
    compare: 'Сравнить',
    compareLimit: 'Сравнить можно не более 5 ТС',
    pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{транспорт} few{транспорта} many{транспорта} other{транспорта}}'
  },
  vehiclePage: {
    meta: {
      title: 'Транспорт GTA5 RP | Majestic Wiki',
      description:
        'Подробная информация о транспорте на сервере Majestic RP в GTA5 RP. Характеристики, цены, улучшения и покраска. Узнайте о максимальной скорости, разгоне, вместимости багажника, стоимости улучшений и других параметрах каждого транспортного средства.',
      custom: {
        title: '{name} | Транспорт GTA5 RP | Majestic Wiki',
        description:
          'Подробная информация о {name} на сервере Majestic RP в GTA5 RP. Характеристики, цены, улучшения и покраска. Узнайте о максимальной скорости, разгоне, вместимости багажника, стоимости улучшений и других параметрах.'
      }
    },
    backToTable: 'Вернуться к списку',
    labelsUniqueId: 'Уникальный ID',
    labelsPrice: 'Стоимость',
    labelsBuyPrice: 'Стоимость покупки',
    labelsSellPrice: 'Стоимость продажи',
    labelsMaxSpeed: 'Максимальная скорость',
    labelsMaxSpeedFT: 'Максимальная скорость (FT)',
    labelsAccelerationTo100: 'Разгон до 100 км/ч',
    labelsTradable: 'Возможность передачи',
    labelsTrunk: 'Вместимость багажника',
    labelsGasolineCapacity: 'Вместимость бака',
    labelsGasolineType: 'Тип топлива',
    upgradesTitle: 'Улучшения транспорта',
    upgradesDescription: 'Стоимость улучшений до максимального уровня',
    upgradesEngine: 'Двигатель',
    upgradesEngineDescription: 'Раскройте истинный потенциал транспорта',
    upgradesBreaks: 'Тормоза',
    upgradesBreaksDescription: 'Полный контроль скорости на любой дороге',
    upgradesTurbo: 'Турбо',
    upgradesTurboDescription: 'Взрывное ускорение в любой момент',
    upgradesTransmission: 'Коробка',
    upgradesTransmissionDescription: 'Молниеносное переключение всех передач',
    upgradesSuspension: 'Подвеска',
    upgradesSuspensionDescription: 'Идеальный комфорт на нужной высоте',
    upgradesTotal: 'Стоимость улучшений',
    upgradesDisclaimer: 'Итоговая стоимость может отличаться из-за комиссии в LSC на вашем сервере',
    colorsTitle: 'Покраска транспорта',
    colorsDescription: 'Доступные типы и виды покраски со стоимостью',
    paintingsMainPrice: 'Основная покраска',
    paintingsSubPrice: 'Дополнительная покраска',
    paintingsOtherPrice: 'Прочая покраска',
    paintingsBrightMetallic: 'Яркий металлик',
    paintingsMetallic: 'Металлик',
    paintingsRichMetallic: 'Насыщенный металлик',
    paintingsDarkMetallic: 'Тёмный металлик',
    paintingsMatte: 'Матовый',
    paintingsMatteMetallic: 'Матовый металл',
    paintingsSatin: 'Сатин',
    paintingsMetal: 'Металл',
    paintingsShadowChrome: 'Теневой хром',
    paintingsPureChrome: 'Чистый хром',
    paintingsPearl: 'Оттенок перламутра',
    paintingsWheels: 'Цвет дисков',
    paintingsDetails: 'Цвет деталей',
    paintingsAdditionalDetails: 'Доп. цвет деталей',
    paintingsLights: 'Свет фар',
    paintingsSmoke: 'Цвет дыма покрышек',
    paintingsNeon: 'Цвет неона',
    paintingsTotal: 'Финальная стоимость покраски',
    shareLink: 'Поделиться ссылкой на транспорт',
    shareLinkCopied: 'Ссылка на транспорт успешно скопирована',
    addToCompare: 'Добавить в сравнение',
    addedToCompare: 'Транспорт добавлен в сравнение',
    goToCompare: 'Перейти к сравнению',
    removeFromCompare: 'Удалить из сравнения',
    removedFromCompare: 'Транспорт удален из сравнения'
  },
  vehiclesComparePage: {
    meta: {
      title: 'Сравнение транспорта GTA5 RP | Majestic Wiki',
      description:
        'Сравните характеристики и цены различных транспортных средств на сервере Majestic RP в GTA5 RP. Узнайте, какой автомобиль лучше всего подходит для ваших нужд! Сравнение скорости, цены, вместимости и других параметров.'
    },
    name: 'Название',
    type: 'Тип',
    brand: 'Бренд',
    kits: 'Комплектации',
    model: 'Модель',
    trunkCapacity: 'Вместимость багажника',
    maxSpeed: 'Максимальная скорость',
    maxSpeedFT: 'Максимальная скорость (FT)',
    price: 'Стоимость',
    donatePrice: 'Стоимость в Majestic Coin',
    sellPrice: 'Стоимость продажи',
    sellPriceBeforeTax: 'Стоимость продажи (до налога)',
    sellPriceWithDiscount: 'Стоимость продажи (со скидкой)',
    accelerationTo100: 'Время разгона до 100 км/ч',
    gasolineCapacity: 'Вместимость бака',
    gasolineType: 'Тип топлива',
    isTradable: 'Возможность передачи',
    isBuyable: 'Возможность покупки',
    createdAt: 'Дата появления',
    gasolineCapacityUnit: 'л',
    maxSpeedUnit: 'км/ч',
    maxSpeedFTUnit: 'км/ч',
    priceUnit: '₽',
    accelerationTo100Unit: 'сек',
    trunkCapacityUnit: 'кг',
    showOnlyDifferent: 'Показать только отличающиеся',
    showAll: 'Показать все',
    share: 'Поделиться сравнением',
    linkCopied: 'Ссылка на сравнение успешно скопирована',
    openVehicle: 'Открыть страницу транспорта',
    deleteVehicle: 'Удалить транспорт из сравнения',
    noDifferences: 'Нет отличий'
  },
  skinsPage: {
    meta: {
      title: 'Скины оружия GTA5 RP | Majestic Wiki',
      description:
        'Полный каталог скинов оружия на сервере Majestic RP в GTA5 RP. Узнайте о способах получения, стоимости и особенностях каждого скина для различных типов оружия.',
      custom: {
        title: 'Скины на {type} | GTA5 RP | Majestic Wiki',
        description:
          'Каталог скинов для {type} на сервере Majestic RP в GTA5 RP. Подробная информация о способах получения, стоимости и особенностях скинов.'
      }
    },
    title: 'Таблица скинов',
    search: 'Поиск скинов...',
    apPistol: 'Бронебойный пистолет',
    assaultRifle: 'Штурмовая винтовка',
    assaultRifleMk2: 'Штурмовая винтовка Mk2',
    bullpupRifle: 'Винтовка Буллпап',
    carbineRifle: 'Карабинная винтовка',
    carbineRifleMk2: 'Карабинная винтовка Mk2',
    combatPDW: 'Боевой PDW',
    gusenberg: 'Пистолет-пулемет Томпсона',
    heavyPistol: 'Тяжелый пистолет',
    heavyShotgun: 'Тяжелый дробовик',
    heavySniper: 'Тяжелая снайперская винтовка',
    heavySniperMk2: 'Тяжелая снайперская винтовка Mk2',
    lightvest: 'Бронежилет',
    machinePistol: 'Малый ПП',
    marksmanPistol: 'Пистолет Marksman',
    microSMG: 'Micro SMG',
    militaryRifle: 'Военная винтовка',
    revolver: 'Револьвер',
    smgMk2: 'SMG Mk2',
    specialCarbine: 'Специальный карабин',
    vintagePistol: 'Винтажный пистолет',
    combatMgMk2: 'Ручной пулемет Mk2',
    heavyRifle: 'Тяжелая винтовка',
    tacticalRifle: 'Тактическая винтовка',
    tecPistol: 'Тактический SMG',
    noName: 'Скин #{id}',
    battlePass: 'Battle Pass',
    case: 'Кейс',
    unique: 'Уникальный',
    pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{скин} few{скина} many{скинов} other{скинов}}'
  },
  animationsPage: {
    meta: {
      title: 'Анимации GTA5 RP | Majestic Wiki',
      description:
        'Полный каталог анимаций на сервере Majestic RP в GTA5 RP. Все виды анимаций: действия, позы, танцы и эксклюзивные анимации. Узнайте способы получения и стоимость каждой анимации.',
      custom: {
        title: '{type} | Анимации GTA5 RP | Majestic Wiki',
        description:
          'Каталог {type} анимаций на сервере Majestic RP в GTA5 RP. Подробная информация о способах получения, стоимости и использовании анимаций.'
      }
    },
    title: 'Таблица анимаций',
    search: 'Поиск анимаций...',
    default: 'Стандарт',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Кейс',
    unique: 'Уникальный',
    action: 'Действия',
    pose: 'Позы',
    positive: 'Позитивные',
    negative: 'Негативные',
    dances: 'Танцы',
    etc: 'Другое',
    exclusive: 'Эксклюзивные',
    noName: 'Анимация #{id}',
    pagingText: 'Найден{N, plural, one{а} few{о} many{о}} {N} {N, plural, one{анимация} few{анимации} many{анимаций} other{анимаций}}'
  },
  realtyPage: {
    meta: {
      title: 'Недвижимость GTA5 RP | Majestic Wiki',
      description:
        'Полный каталог недвижимости на сервере Majestic RP в GTA5 RP. Дома, квартиры, офисы и склады с подробными характеристиками, ценами и расположением. Узнайте количество жильцов, гаражных мест и другие параметры.',
      custom: {
        title: '{type} | Недвижимость GTA5 RP | Majestic Wiki',
        description:
          'Каталог {type} на сервере Majestic RP в GTA5 RP. Подробная информация о характеристиках, ценах, расположении и особенностях недвижимости.'
      }
    },
    title: 'Таблица недвижимости',
    search: 'Поиск недвижимости...',
    majesticMotors: 'Majestic Motors',
    house: 'Дома',
    apartment: 'Квартиры',
    office: 'Офисы',
    warehouse: 'Склады',
    tenantsFor: '{N} {N, plural, one{жилец} few{жильца} many{жильцов} other{жильцов}}',
    garageSlotsFor: '{N} {N, plural, one{гаражное место} few{гаражных места} many{гаражных мест} other{гаражных мест}}',
    viewOnMap: 'Посмотреть на карте',
    mapPosition: 'Местоположение',
    pagingText:
      'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{недвижимость} few{недвижимости} many{недвижимости} other{недвижимости}}',
    noName: 'Недвижимость #{id}'
  },
  postsPage: {
    meta: {
      title: 'Редактирование поста | Majestic Wiki',
      description:
        'Создание и редактирование постов на Majestic Wiki для GTA5 RP. Публикация актуальной информации о транспорте, недвижимости, бизнесах и других аспектах игры.'
    },
    titlePlaceholder: 'Введите название поста',
    category: 'Категория',
    noCategory: 'Без категории',
    availability: 'Доступность',
    allView: 'Видимость для всех',
    privateView: 'Ограниченный доступ',
    relevance: 'Актуальность',
    relevanceData: 'Актуальная информация',
    outdatedData: 'Устаревшая информация',
    categoryPosition: 'Позиция в категории',
    categoryPositionDescription: 'Например, «50» или «-5»',
    postDeleted: 'Пост удален',
    author: 'Автор статьи',
    editor: 'Редактор статьи',
    contents: 'Содержание статьи',
    readingTime: 'Время чтения',
    readingTimeLength: '{N} {N, plural, one{минута} few{минуты} many{минут} other{минут}}',
    publishDate: 'Публикация от {date}',
    views: '{N} {N, plural, one{просмотр} few{просмотра} many{просмотров} other{просмотров}}',
    youLiked: 'Вы лайкнули',
    youAndPeopleLiked: 'Вы и {likes} лайкнули',
    peopleLiked: '{N} {N, plural, one{лайкнул} few{лайкнули} many{лайкнули} other{лайкнули}}',
    categories: 'Категории',
    posts: 'Посты',
    foundMistake: 'Нашли ошибку в статье?',
    language: 'Язык'
  },
  panelPage: {
    users: {
      title: 'Управление пользователями | Majestic Wiki',
      heading: 'Управление пользователями',
      admin: 'Администратор',
      user: 'Пользователь',
      customUser: 'Пользователь с правами',
      search: 'Начните вводить поисковой запрос...',
      comment: 'Комментарий',
      pagingText:
        'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{пользователь} few{пользователя} many{пользователей} other{пользователей}}'
    },
    posts: {
      title: 'Управление постами | Majestic Wiki',
      heading: 'Управление постами',
      views: '{N} {N, plural, one{просмотр} few{просмотра} many{просмотров} other{просмотров}}',
      likes: '{N} {N, plural, one{лайк} few{лайка} many{лайков} other{лайков}}'
    },
    categories: {
      title: 'Управление категориями | Majestic Wiki',
      heading: 'Управление категориями'
    },
    mapLayers: {
      title: 'Управление слоями карты | Majestic Wiki',
      heading: 'Управление слоями карты'
    },
    logs: {
      title: 'Аудит логов — Majestic Wiki',
      heading: 'Аудит логов',
      search: 'Начните вводить поисковой запрос...',
      biz: 'Бизнес',
      category: 'Категория',
      clothing: 'Одежда',
      file: 'Файл',
      item: 'Предмет',
      mapItem: 'Элемент карты',
      mapLayer: 'Слой карты',
      post: 'Пост',
      realty: 'Недвижимость',
      skin: 'Скин',
      user: 'Пользователь',
      vehicle: 'Транспорт',
      animation: 'Анимация',
      actions: {
        unknown: '{username} совершил неизвестное действие',
        bizCreate: '{username} добавил бизнес {id}',
        bizUpdate: '{username} изменил бизнес {id}',
        bizDelete: '{username} удалил бизнес {id}',
        categoryCreate: '{username} добавил категорию {id}',
        categoryUpdate: '{username} изменил категорию {id}',
        categoryDelete: '{username} удалил категорию {id}',
        clothingCreate: '{username} добавил одежду {id}',
        clothingUpdate: '{username} изменил одежду {id}',
        clothingDelete: '{username} удалил одежду {id}',
        fileCreate: '{username} добавил файл {id}',
        fileUpdate: '{username} изменил файл {id}',
        fileDelete: '{username} удалил файл {id}',
        itemCreate: '{username} добавил предмет {id}',
        itemUpdate: '{username} изменил предмет {id}',
        itemDelete: '{username} удалил предмет {id}',
        mapItemCreate: '{username} добавил элемент на карту {id} на "{layer}"',
        mapItemUpdate: '{username} изменил элемент на карте {id} на "{layer}"',
        mapItemDelete: '{username} удалил элемент на карте {id} на "{layer}"',
        mapLayerCreate: '{username} добавил слой карты {id}',
        mapLayerUpdate: '{username} изменил слой карты {id}',
        mapLayerDelete: '{username} удалил слой карты {id}',
        postCreate: '{username} добавил пост {id}',
        postUpdate: '{username} изменил пост {id}',
        postDelete: '{username} удалил пост {id}',
        realtyCreate: '{username} добавил недвижимость {id}',
        realtyUpdate: '{username} изменил недвижимость {id}',
        realtyDelete: '{username} удалил недвижимость {id}',
        skinCreate: '{username} добавил скин {id}',
        skinUpdate: '{username} изменил скин {id}',
        skinDelete: '{username} удалил скин {id}',
        userCreate: '{username} добавил пользователя {id}',
        userUpdate: '{username} изменил пользователя {id}',
        userDelete: '{username} удалил пользователя {id}',
        vehicleCreate: '{username} добавил транспорт {id}',
        vehicleUpdate: '{username} изменил транспорт {id}',
        vehicleDelete: '{username} удалил транспорт {id}',
        animationCreate: '{username} добавил анимацию {id}',
        animationUpdate: '{username} изменил анимацию {id}',
        animationDelete: '{username} удалил анимацию {id}',
        changelogCreate: '{username} добавил ченджлог {id}',
        changelogUpdate: '{username} изменил ченджлог {id}',
        changelogDelete: '{username} удалил ченджлог {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: 'Настройки',
      users: 'Пользователи',
      categories: 'Категории',
      mapLayers: 'Слои карты',
      posts: 'Посты',
      logs: 'Логи'
    }
  },
  changelogPage: {
    titlePlaceholder: 'Введите название изменения',
    descriptionPlaceholder: 'Введите описание изменения',
    availableAt: 'Дата публикации',
    soon: 'через',
    ago: 'назад',
    fix: 'Исправлено',
    feat: 'Добавлено',
    chore: 'Изменено',
    style: 'Дизайн',
    refactor: 'Переработано'
  },
  changelogsPage: {
    meta: {
      title: 'История изменений | Majestic Wiki',
      description:
        'Полная история обновлений и изменений на сервере Majestic RP. Следите за новыми функциями, улучшениями геймплея, исправлениями багов и балансировкой. Актуальная информация о всех обновлениях проекта.'
    },
    title: 'История изменений',
    search: 'Поиск изменений...'
  },
  homePage: {
    meta: {
      title: 'Majestic Wiki | GTA5 RP Энциклопедия',
      description:
        'Majestic Wiki - полная энциклопедия для игроков GTA5 RP. Информация о транспорте, недвижимости, бизнесах, квестах и других аспектах игры. Актуальные данные о ценах, характеристиках и способах получения.'
    },
    tools: {
      map: {
        title: 'Карта',
        description: 'Карта сервера со всеми локациями'
      },
      vehicles: {
        title: 'Транспорт',
        description: 'Весь доступный транспорт на сервере'
      },
      realty: {
        title: 'Недвижимость',
        description: 'Данные по всем домам и квартирам'
      },
      biz: {
        title: 'Бизнесы',
        description: 'Продвинутый вариант заработка'
      },
      clothesMale: {
        title: 'Мужская одежда',
        description: 'Показ мод для мужчин'
      },
      clothesFemale: {
        title: 'Женская одежда',
        description: 'Показ мод для женщин'
      },
      skins: {
        title: 'Скины',
        description: 'Каталог всех доступных паттернов'
      },
      items: {
        title: 'Предметы',
        description: 'Доступные предметы на сервере'
      },
      servers: {
        title: 'Серверы',
        description: 'Список всех серверов Majestic RP'
      },
      animations: {
        title: 'Анимации',
        description: 'Уникальные анимации на сервере'
      },
      changelogs: {
        title: 'История изменений',
        description: 'История изменений на проекте'
      }
    },
    search: {
      title: 'Общие категории',
      description: 'Здесь Вы можете найти любую информацию о сервере Majestic и его системах'
    },
    categories: {
      collapseList: 'Свернуть список',
      expandList: 'Развернуть список'
    },
    online: {
      players: '{count} онлайн',
      offline: 'Выключен'
    }
  },
  errorPage: {
    title: 'Страница не найдена',
    description: 'Возможно, у вас опечатка в адресе страницы, или её просто не существует :(',
    backToHome: 'Перейти на главную'
  },
  bizPage: {
    meta: {
      title: 'Бизнесы GTA5 RP | Majestic Wiki',
      description:
        'Полный каталог бизнесов на сервере Majestic RP в GTA5 RP. Магазины 24/7, заправки, оружейные магазины, автосалоны и другие предприятия. Узнайте о прибыльности, требованиях и способах приобретения каждого бизнеса.',
      custom: {
        title: '{type} | Бизнесы GTA5 RP | Majestic Wiki',
        description:
          'Подробная информация о {type} на сервере Majestic RP в GTA5 RP. Характеристики, прибыльность, требования и способы приобретения бизнеса.'
      }
    },
    title: 'Таблица бизнесов',
    search: 'Поиск бизнеса...',
    groceryStore: 'Магазин 24/7',
    gasStation: 'Заправка',
    atm: 'Банкомат',
    gunshop: 'Оружейный магазин',
    clothingStore: 'Магазин одежды',
    autoShop: 'Автосалон',
    tattoo: 'Тату-салон',
    lsc: 'Тюнинг салон',
    barbershop: 'Барбершоп',
    carWash: 'Автомойка',
    viewOnMap: 'Посмотреть на карте',
    mapPosition: 'Местоположение',
    pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{бизнес} few{бизнеса} many{бизнесов} other{бизнесов}}',
    noName: 'Бизнес #{id}'
  },
  clothesPage: {
    meta: {
      title: 'Одежда GTA5 RP | Majestic Wiki',
      description:
        'Полный каталог одежды на сервере Majestic RP в GTA5 RP. Мужская и женская одежда, аксессуары, рюкзаки, нашивки, головные уборы и другие предметы гардероба. Узнайте о способах получения и стоимости каждого предмета одежды.',
      custom: {
        title: '{type} | Одежда GTA5 RP | Majestic Wiki',
        description:
          'Каталог {type} на сервере Majestic RP в GTA5 RP. Подробная информация о способах получения, использовании и стоимости предметов одежды.'
      }
    },
    titleMale: 'Таблица мужской одежды',
    titleFemale: 'Таблица женской одежды',
    search: 'Поиск одежды...',
    top: 'Верх',
    legs: 'Низ',
    shoes: 'Обувь',
    watch: 'Часы',
    mask: 'Маски',
    decal: 'Нашивки',
    accessory: 'Аксессуары',
    head: 'Головные уборы',
    bag: 'Рюкзаки',
    glasses: 'Очки',
    ear: 'Уши',
    gloves: 'Перчатки',
    undershirt: 'Майки',
    noName: 'Комплект одежды #{id}',
    drawable: 'Drawable',
    texture: 'Texture',
    male: 'Мужской',
    female: 'Женский',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Кейс',
    unique: 'Уникальный',
    pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{одежда} few{одежды} many{одежды} other{одежды}}'
  },
  itemsPage: {
    meta: {
      title: 'Предметы GTA5 RP | Majestic Wiki',
      description:
        'Полный каталог предметов на сервере Majestic RP в GTA5 RP. Антиквариат, рыба, руда, древесина, ящики, материалы и другие предметы. Узнайте о способах получения, использовании и стоимости каждого предмета.',
      custom: {
        title: '{type} | Предметы GTA5 RP | Majestic Wiki',
        description:
          'Каталог {type} на сервере Majestic RP в GTA5 RP. Подробная информация о способах получения, использовании и стоимости предметов.'
      }
    },
    title: 'Таблица предметов',
    search: 'Поиск предметов...',
    antique: 'Антиквариат',
    fish: 'Рыба',
    ore: 'Руда',
    wood: 'Древесина',
    box: 'Ящики',
    material: 'Материалы',
    event: 'Ивент',
    food: 'Продукты',
    alcohol: 'Алкоголь',
    illegal: 'Нелегальное',
    medical: 'Медицина',
    equipment: 'Оборудование',
    ammunition: 'Амуниция',
    tool: 'Инструменты',
    unique: 'Уникальный',
    vehicle: 'Автозапчасти',
    others: 'Разное',
    default: 'Стандарт',
    battlePass: 'Battle Pass',
    case: 'Кейс',
    unique: 'Уникальный',
    pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{предмет} few{предмета} many{предметов} other{предметов}}',
    noName: 'Предмет #{id}'
  },
  mapPage: {
    meta: {
      title: 'Карта GTA5 RP | Majestic Wiki',
      description:
        'Интерактивная карта сервера Majestic RP в GTA5 RP. Исследуйте локации, находите сокровища, животных, телефоны и мусорки. Детальная карта мира с возможностью добавления меток и зон. Все важные места и точки интереса на одном ресурсе!',
      custom: {
        title: '{type} | Карта GTA5 RP | Majestic Wiki',
        description:
          'Детальная карта {type} на сервере Majestic RP в GTA5 RP. Исследуйте локации, находите важные точки и создавайте собственные метки. Полная информация о расположении объектов на карте!'
      }
    },
    dataLoading: 'Загрузка данных...',
    defaultPoints: 'Стандартные метки',
    animalLayer: 'Карта животных',
    rubbishLayer: 'Карта мусорок',
    telephoneLayer: 'Карта телефонов',
    treasureLayer: 'Карта кладов',
    defaultLayer: 'Карта мира',
    allLayers: 'Слои карты',
    backToHome: 'Вернуть на главную',
    zoomIn: 'Приблизить',
    zoomOut: 'Отдалить',
    point: 'Точка',
    polygon: 'Зона',
    addPoint: 'Добавить точку',
    addPolygon: 'Добавить зону',
    copyCoordinates: 'Скопировать координаты',
    savePointPosition: 'Сохранить позицию',
    editPointPosition: 'Изменить позицию',
    layerNamePlaceholder: 'Название зоны',
    layerDescriptionPlaceholder: 'Описание зоны'
  },
  serversPage: {
    meta: {
      title: 'Онлайн серверов GTA5 RP | Majestic Wiki',
      description:
        'Актуальная статистика онлайн игроков на серверах Majestic RP в GTA5 RP. Текущий онлайн, пиковые значения, очереди и графики активности. Присоединяйтесь к самому популярному ролевому проекту!'
    },
    dataLoading: 'Загрузка данных...',
    title: 'Онлайн серверов',
    online: '{count} онлайн',
    inQueue: '{count} в очереди',
    peakOnline: '{count} пик',
    currentOnline: 'текущий онлайн',
    peakOnline24h: 'пик за сегодня',
    peakOnlineAllTime: 'пик за все время',
    peakOnlineTooltip: '{percent}% относительно прошедших суток',
    techWorks: 'Тех работы',
    forBeginners: 'Для новичков',
    experience: 'Опыт x{multiplier}',
    newServer: 'Новый',
    seasonPassMultiplier: 'Опыт пропуска x{multiplier}',
    combinedView: 'Общий',
    separateView: 'Раздельный'
  },
  research: {
    placeholder: 'Поиск информации на сайте...',
    skin: 'Скин',
    vehicle: 'Транспорт',
    realty: 'Недвижимость',
    biz: 'Бизнес',
    clothing: 'Одежда',
    item: 'Предмет',
    post: 'Пост'
  },
  userModal: {
    login: 'Войти',
    signout: 'Выйти',
    register: 'Регистрация',
    registerEnd: 'Зарегистрироваться',
    restore: 'Восстановление',
    restoreEnd: 'Восстановить',
    avatarDelete: 'Удалить аватар',
    headingUpdate: 'Изменение пользователя #{id}',
    postAdd: 'Публикация постов',
    postUpdate: 'Редактирование постов',
    postDelete: 'Удаление постов',
    vehicleAdd: 'Добавление транспорта',
    vehicleUpdate: 'Редактирование транспорта',
    vehicleDelete: 'Удаление транспорта',
    realtyAdd: 'Добавление недвижимости',
    realtyUpdate: 'Изменение недвижимости',
    realtyDelete: 'Удаление недвижимости',
    bizAdd: 'Добавление бизнеса',
    bizUpdate: 'Изменение бизнеса',
    bizDelete: 'Удаление бизнеса',
    mapItemAdd: 'Добавление элементов на карте',
    mapItemUpdate: 'Изменение элементов на карте',
    mapItemDelete: 'Удаление элементов на карте',
    clothingAdd: 'Добавление одежды',
    clothingUpdate: 'Изменение одежды',
    clothingDelete: 'Удаление одежды',
    skinAdd: 'Добавление скинов',
    skinUpdate: 'Изменение скинов',
    skinDelete: 'Удаление скинов',
    itemAdd: 'Добавление предметов',
    itemUpdate: 'Изменение предметов',
    itemDelete: 'Удаление предметов',
    fileUpload: 'Загрузка изображений',
    viewPrivate: 'Просмотр приватных данных',
    doMagic: 'Доступ к магическим функциям',
    animationAdd: 'Добавление анимаций',
    animationUpdate: 'Изменение анимаций',
    animationDelete: 'Удаление анимаций',
    categoryAdd: 'Добавление категорий',
    categoryUpdate: 'Изменение категорий',
    categoryDelete: 'Удаление категорий',
    mapLayerAdd: 'Добавление слоев карты',
    mapLayerUpdate: 'Изменение слоев карты',
    mapLayerDelete: 'Удаление слоев карты',
    post: 'Пост',
    skin: 'Скин',
    biz: 'Бизнес',
    clothing: 'Одежда',
    item: 'Предмет',
    vehicle: 'Транспорт',
    animation: 'Анимация',
    category: 'Категория',
    realty: 'Недвижимость',
    mapLayer: 'Слой карты',
    changelog: 'Ченджлог'
  },
  restoreModal: {
    title: 'Восстановление аккаунта',
    newPassword: 'Новый пароль',
    newPasswordRepeat: 'Повтор нового пароля',
    passwordConfirm: 'Изменить пароль',
    passwordError: 'Пароли не совпадают'
  },
  bizModal: {
    titleUpdate: 'Изменение бизнеса #{id}',
    titleAdd: 'Добавление нового бизнеса'
  },
  categoryModal: {
    titleUpdate: 'Изменение категории #{id}',
    titleAdd: 'Добавление новой категории'
  },
  mapLayerModal: {
    titleUpdate: 'Изменение слоя карты #{id}',
    titleAdd: 'Добавление нового слоя карты'
  },
  clothingModal: {
    titleUpdate: 'Изменение одежды #{id}',
    titleAdd: 'Добавление новой одежды',
    itemAdd: 'Добавить расцветку',
    itemGenerate: 'Сгенерировать все расцветки'
  },
  itemModal: {
    titleUpdate: 'Изменение предмета #{id}',
    titleAdd: 'Добавление нового предмета'
  },
  realtyModal: {
    titleUpdate: 'Изменение недвижимости #{id}',
    titleAdd: 'Добавление новой недвижимости'
  },
  skinModal: {
    titleUpdate: 'Изменение скина #{id}',
    titleAdd: 'Добавление нового скина'
  },
  vehicleModal: {
    titleUpdate: 'Изменение транспорта #{id}',
    titleAdd: 'Добавление нового транспорта',
    titleDefault: 'Информация о {name}',
    itemAdd: 'Добавить обвес'
  },
  mapItemModal: {
    titleUpdate: 'Изменение элемента карты',
    titleAdd: 'Добавление нового элемента карты',
    titleDelete: 'Удаление элемента карты',
    descriptionDelete: 'Вы точно хотите удалить элемент с карты? Это действие невозможно отменить.'
  },
  animationModal: {
    titleUpdate: 'Изменение анимации #{id}',
    titleAdd: 'Добавление новой анимации'
  }
};
