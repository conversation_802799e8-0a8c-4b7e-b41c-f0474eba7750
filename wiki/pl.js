export default {
  global: {
    all: '<PERSON><PERSON><PERSON><PERSON>',
    back: '<PERSON><PERSON><PERSON><PERSON><PERSON> z powrotem',
    forward: '<PERSON><PERSON><PERSON><PERSON>',
    paging: '<PERSON><PERSON> {page} z {maxPage}',
    close: 'Ak<PERSON>ptuję',
    clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    save: '<PERSON><PERSON><PERSON>',
    delete: '<PERSON>u<PERSON>',
    deleteYes: '<PERSON><PERSON> j<PERSON> pewien usunięcia?',
    cancel: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    publish: 'Opublikować',
    edit: 'Edytuj',
    share: 'Udost<PERSON>pnij',
    add: 'Dodaj.',
    active: 'Aktywny',
    deleted: 'Usunięto',
    blocked: 'Zab<PERSON>kowany',
    learnMore: '<PERSON><PERSON><PERSON><PERSON> więcej',
    panel: 'Panel',
    theme: 'Temat',
    language: '<PERSON><PERSON><PERSON><PERSON>',
    user: 'Użytkownik',
    confirm: 'Potwierd<PERSON>',
    restore: 'Przywr<PERSON>cenie',
    yes: 'Tak',
    no: 'Nie',
    new: 'Nowy',
    preview: 'Podgląd',
    sortPriceDesc: '<PERSON>na malejąco',
    sortPriceAsc: 'Cena rosną<PERSON>',
    sortPriceMinMax: '<PERSON><PERSON><PERSON> cen',
    sortDateDesc: 'W porządku malejącym według daty',
    sortDateAsc: 'W porządku rosnącym według daty',
    noSellPrice: 'Nie do kupienia',
    noBuyPrice: 'Nie na sprzedaż',
    noData: {
      title: 'Nic nie znaleziono',
      description: 'Spróbuj zmienić lub uprościć zapytanie wyszukiwania'
    },
    battlePass: {
      summer: 'Summer Battle Pass',
      winter: 'Zimowa przepustka bojowa'
    },
    case: {
      default: 'Standardowa skrzynka',
      vehicles: 'Obudowa samochodu',
      halloween: 'Etui na Halloween',
      spring: 'Walizka wiosenna',
      autumn: 'Jesienna walizka',
      summer: 'Letnie etui',
      summerExtra: 'Specjalny letni przypadek',
      summerSkins: 'Letni pokrowiec na skórę',
      summerVehicles: 'Letnia walizka transportowa',
      winter: 'Przypadek zimowy',
      winterExtra: 'Specjalny przypadek zimowy',
      winterSkins: 'Etui zimowe ze skórkami',
      winterVehicles: 'Walizka do transportu zimowego',
      toys: 'Etui na zabawki'
    },
    subscription: { premium: 'Subskrypcja Premium' },
    copy: 'Kopia'
  },
  footer: { disclaimer: 'Wszystkie informacje na stronie internetowej służą wyłącznie celom informacyjnym i nie stanowią oferty publicznej.' },
  footer: { disclaimer: 'Wszystkie informacje na stronie internetowej służą wyłącznie celom informacyjnym i nie stanowią oferty publicznej.' },
  editor: {
    placeholder: 'Napiszmy niesamowity post!',
    text: 'Ustęp',
    heading: 'Tytuł',
    warning: 'Uwaga',
    quote: 'Cytat',
    code: 'Kod',
    delimiter: 'Roździelacz',
    link: 'Odniesienie',
    linkPlaceholder: 'Wprowadź łącze',
    marker: 'Marker',
    bold: 'Pogrubienie',
    italic: 'Kursywa',
    inlineCode: 'Monospaced',
    underline: 'Podkreślono',
    copytext: 'Możliwość kopiowania',
    log: 'Logue',
    map: {
      name: 'Mapa',
      params: 'Wprowadź parametry karty',
      caption: 'Wprowadź opis mapy'
    },
    table: {
      name: 'Tabela',
      addColumnLeft: 'Dodaj kolumnę po lewej stronie',
      addColumnRight: 'Dodaj kolumnę po prawej stronie',
      deleteColumn: 'Usuń kolumnę',
      addRowAbove: 'Dodaj linię u góry',
      addRowBelow: 'Dodaj linię na dole',
      deleteRow: 'Usuń ciąg znaków',
      withHeadings: 'Z nagłówkami',
      withoutHeadings: 'Brak nagłówków'
    },
    image: {
      name: 'Obraz',
      withBorder: 'Z ramką',
      stretchImage: 'Rozciągnij obraz',
      withBackground: 'Z tłem',
      selectImage: 'Wybierz obraz',
      uploadError: 'Nie udało się załadować obrazu, spróbuj innego',
      caption: 'Opis obrazu',
      withCaption: 'Z opisem'
    },
    carousel: {
      name: 'Siatka obrazów',
      caption: 'Opis obrazu',
      addImage: 'Wybierz obraz'
    },
    list: {
      name: 'Lista',
      ordered: 'Numerowane',
      unordered: 'Nienumerowane',
      checklist: 'Lista kontrolna'
    },
    header: {
      heading1: 'Nagłówek H1',
      heading2: 'Nagłówek H2',
      heading3: 'Nagłówek H3',
      heading4: 'Nagłówek H4'
    },
    embed: { caption: 'Opis łącza' },
    convertTo: 'Konwertuj na',
    moveUp: 'Przesuń się w górę',
    moveDown: 'Przesuń w dół',
    anchor: 'Kotwica',
    clickToTune: 'Naciśnij, aby skonfigurować',
    dragToMove: 'lub przeciągnij i upuść',
    filter: 'Wyszukiwanie',
    nothingFound: 'Nic nie znaleziono'
  },
  alerts: {
    loginSuccess: 'Pomyślne logowanie',
    loginError: 'Błąd podczas autoryzacji',
    registerSuccess: 'Pomyślna rejestracja na stronie internetowej',
    registerError: 'Błąd rejestracji',
    restoreSuccess: 'Instrukcje odzyskiwania zostały wysłane na twoją skrzynkę pocztową',
    restoreError: 'Błąd podczas przywracania dostępu do konta',
    signoutSuccess: 'Pomyślne wylogowanie',
    postLoadError: 'Błąd podczas przesyłania postu',
    postShareSuccess: 'Link do postu skopiowany do schowka',
    copyCommandSuccess: 'Polecenie zostanie skopiowane do schowka',
    restoreAccountSuccess: 'Odzyskiwanie konta powiodło się',
    restoreAccountError: 'Błąd podczas przywracania dostępu do konta',
    verificateAccountSuccess: 'Skrzynka pocztowa została pomyślnie potwierdzona',
    verificateAccountError: 'Błąd potwierdzenia skrzynki pocztowej',
    dataUpdateSuccess: 'Pomyślna aktualizacja informacji',
    dataUpdateError: 'Błąd podczas aktualizacji informacji',
    dataDeleteSuccess: 'Pomyślne usunięcie informacji',
    dataDeleteError: 'Błąd podczas usuwania informacji',
    dataAddSuccess: 'Skuteczne dodawanie informacji',
    dataAddError: 'Błąd podczas dodawania informacji',
    mapMoveInfo: 'Przenieś punkt do innej pozycji i zapisz',
    mapCoordinatesCopySuccess: 'Współrzędne zostały pomyślnie skopiowane',
    copyTextSuccess: 'Tekst został pomyślnie skopiowany'
  },
  mail: {
    verificate: {
      subject: 'Potwierdzenie adresu e-mail',
      title: 'Potwierdzenie',
      description: 'Aby zweryfikować i potwierdzić uprawnienia do swojego konta, musisz kliknąć poniższy przycisk. Link jest aktywny przez 24 godziny.'
    },
    restore: {
      subject: 'Przywróć dostęp do konta',
      title: 'Przywrócenie',
      description: 'Aby przywrócić dostęp do konta, musisz kliknąć poniższy przycisk. Link jest aktywny przez 24 godziny.'
    },
    restoreSuccess: {
      subject: 'Pomyślne przywrócenie dostępu do konta',
      title: 'Pomyślny powrót do zdrowia',
      description: 'Pomyślnie zmieniłeś hasło w witrynie. Jeśli tego nie zrobiłeś, natychmiast zmień hasło!'
    },
    verificateSuccess: {
      subject: 'Pomyślne przywrócenie dostępu do konta',
      title: 'Pomyślne potwierdzenie',
      description: 'Pomyślnie potwierdziłeś swoją skrzynkę pocztową, witaj!'
    },
    caption: 'Wiadomość e-mail jest powiadomieniem generowanym automatycznie i nie odpowiadaj na nią.'
  },
  fields: {
    price: 'Koszt',
    buyPrice: 'Koszt zakupu',
    sellPrice: 'Koszt sprzedaży',
    mcPrice: 'Koszt zakupu w MC',
    money: 'Waluta',
    coin: 'Majestic Coin',
    gift: 'Prezent',
    default: 'Zwykłe',
    unique: 'Wyjątkowy',
    event: 'Wydarzenie',
    battlePass: 'Battle Pass',
    lootbox: 'Skrzynka',
    capacityKg: 'Pojemność',
    email: 'Poczta',
    password: 'Wymyśl hasło',
    username: 'Pseudonim',
    weightKg: 'kg',
    speed: 'Prędkość',
    maxSpeed: 'Prędkość maksymalna',
    maxSpeedFT: 'Prędkość maksymalna (FT)',
    accelerationTo100: 'Przyspieszenie do 100 km/h',
    speedSystem: 'km / h',
    timeSystem: 'sec',
    uploadDescription: 'Dotknij lub przeciągnij obraz do obszaru',
    placeholder: 'Wprowadź wartość w polu',
    imagePlaceholder: 'Wprowadź link do zdjęcia',
    selectPlaceholder: 'Wybierz wartość z listy',
    videoPlaceholder: 'Wprowadź łącze do filmu',
    soundPlaceholder: 'Wprowadź łącze do dźwięku',
    id: 'ID',
    serial: 'Numer seryjny',
    kind: 'Typ',
    category: 'Kategoria',
    name: 'Nazwa',
    view: 'Zobacz',
    layer: 'Warstwa',
    icon: 'Ikona',
    circle: 'Punkt',
    polygon: 'Wielokąt',
    emoji: 'Emoji',
    description: 'Opis',
    gender: 'Płeć',
    value: 'Typ wartości',
    valueName: 'Dodatkowy opis ceny',
    visibility: 'Dostępność',
    visibilityPrivate: 'Ograniczony dostęp',
    visibilityPublic: 'Widoczność dla wszystkich',
    activityHidden: 'Ukryte przed dostępem publicznym',
    activityVisible: 'Dostępne dla wszystkich',
    garageSlots: 'Gniazda garażowe',
    maxTenants: 'Liczba mieszkańców',
    gasoline: 'Paliwo',
    brand: 'Marka',
    model: 'Model',
    trunk: 'Bagażnik',
    tradable: 'Przenaszalność',
    buyable: 'Opcja zakupu',
    upgradesPriceFT: 'Dostępne ulepszenia (FT)',
    upgradesEngine: 'Silnik',
    upgradesBreaks: 'Hamulec',
    upgradesSuspension: 'Zawieszenie',
    upgradesTurbo: 'Turbo',
    upgradesTransmission: 'Pudełko',
    paintingsMainPrice: 'Malowanie podstawowe',
    paintingsSubPrice: 'Dodatkowe malowanie',
    paintingsOtherPrice: 'Inne malowanie',
    paintingsBrightMetallic: 'Jasny metaliczny',
    paintingsMetallic: 'Metaliczny',
    paintingsRichMetallic: 'Bogaty metaliczny',
    paintingsDarkMetallic: 'Ciemny metaliczny',
    paintingsMatte: 'Matowy',
    paintingsMatteMetallic: 'Matowy metal',
    paintingsSatin: 'Satyna',
    paintingsMetal: 'Metal',
    paintingsShadowChrome: 'Chromowany cień',
    paintingsPureChrome: 'Czysty chrom',
    paintingsPearl: 'Odcień perłowego',
    paintingsWheels: 'Kolor felg',
    paintingsDetails: 'Kolor części',
    paintingsAdditionalDetails: 'Dod. kolor części',
    paintingsLights: 'Światło reflektora',
    paintingsSmoke: 'Kolor dymu z opon',
    paintingsNeon: 'Kolor neonu',
    total: 'Łącznie',
    language: 'Język',
    radius: 'Promień',
    color: 'Kolor',
    colorPlaceholder: 'Wybierz kolor',
    opacity: 'Przejrzystość',
    alias: 'Pseudonim',
    aliases: 'Pseudonimy',
    looped: 'Zapętlony',
    battlePassId: 'Identyfikator przepustki bojowej',
    gasolineCapacityLiter: 'l',
    gasolineCapacity: 'Pojemność zbiornika',
    source: 'Źródło',
    sources: 'Źródła',
    priceDescription: 'Opis kosztów',
    layerName: 'Nazwa warstwy',
    layerNamePlaceholder: 'Wprowadź nazwę warstwy',
    layerDescription: 'Opis warstwy',
    layerDescriptionPlaceholder: 'Wprowadź opis warstwy',
    layerColor: 'Kolor warstwy',
    layerColorPlaceholder: 'Wybierz kolor',
    layerOpacity: 'Przezroczystość warstwy',
    zIndexOffset: 'Poziom indeksu Z',
    zIndexOffsetPlaceholder: 'Wprowadź wartość poziomu indeksu Z',
    comment: 'Powód'
  },
  vehiclesPage: {
    meta: {
      title: 'GTA5 RP Transport i samochody | Majestic Wiki',
      description: 'Pełny katalog pojazdów na serwerze Majestic RP w GTA5 RP. Szczegółowe charakterystyki samochodów, motocykli, helikopterów, samolotów i łodzi. Sprawdź ceny, prędkość, zwrotność, pojemność bagażnika i inne parametry. Majestic Motors, Samochody, Ciężarówki, Helikoptery, Łodzie, Motocykle, Rowery - wszystko dla GTA5 RP!',
      custom: {
        title: '{type} | GTA5 RP Transport | Majestic Wiki',
        description: 'Szczegółowy katalog {type} na serwerze Majestic RP w GTA5 RP. Charakterystyka, ceny, prędkość, zwrotność i inne parametry. Pełna informacja o transporcie dla GTA5 RP!'
      }
    },
    default: 'Standard',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Skrzynka',
    unique: 'Wyjątkowy',
    title: 'Tabela transportowa',
    search: 'Wyszukiwarka transportu...',
    irl: 'Majestic Motors',
    car: 'Samochody',
    freight: 'Ciężarówki',
    helicopter: 'Śmigłowce',
    plane: 'Самолеты',
    boat: 'Łodzie',
    motorbike: 'Motocykle',
    bike: 'Rowery',
    trailer: 'przyczepy',
    military: 'Wojsko',
    special: 'Specjalność',
    priceDisclaimer: 'Ostateczny koszt może się różnić ze względu na prowizję w LSC na twoim serwerze',
    basicInformation: 'Podstawowe informacje',
    additionalInformation: 'Dodatkowe informacje',
    compare: 'Porównaj',
    compareLimit: 'Można porównać maksymalnie 5 TC',
    pagingText: 'Znaleziono{N, plural, few{na temat} many{na temat}} {N} {N, plural, one{transport} few{transport} many{transport} other{transport}}'
  },
  vehiclePage: {
    meta: {
      title: 'GTA5 RP Transport | Majestic Wiki',
      description: 'Szczegółowe informacje o transporcie na serwerze Majestic RP w GTA5 RP. Charakterystyka, ceny, ulepszenia i malowania. Poznaj prędkość maksymalną, przyspieszenie, pojemność bagażnika, koszt ulepszeń i inne parametry każdego pojazdu.',
      custom: {
        title: '{name} | GTA5 RP Transport | Majestic Wiki',
        description: 'Szczegółowe informacje o {name} na serwerze Majestic RP w GTA5 RP. Cechy, ceny, ulepszenia i malowania. Dowiedz się o prędkości maksymalnej, przyspieszeniu, pojemności bagażnika, kosztach ulepszeń i innych parametrach.'
      }
    },
    backToTable: 'Powrót do listy',
    labelsUniqueId: 'Unikalny identyfikator',
    labelsPrice: 'Koszt',
    labelsBuyPrice: 'Koszt zakupu',
    labelsSellPrice: 'Koszt sprzedaży',
    labelsMaxSpeed: 'Prędkość maksymalna',
    labelsMaxSpeedFT: 'Prędkość maksymalna (FT)',
    labelsAccelerationTo100: 'Przyspieszenie do 100 km/h',
    labelsTradable: 'Przenaszalność',
    labelsTrunk: 'Pojemność bagażnika',
    labelsGasolineCapacity: 'Pojemność zbiornika',
    labelsGasolineType: 'Rodzaj paliwa',
    upgradesTitle: 'Usprawnienia transportu',
    upgradesDescription: 'Koszt aktualizacji do maksymalnego poziomu',
    upgradesEngine: 'Silnik',
    upgradesEngineDescription: 'Uwolnij prawdziwy potencjał transportu',
    upgradesBreaks: 'Hamulec',
    upgradesBreaksDescription: 'Pełna kontrola prędkości na każdej drodze',
    upgradesTurbo: 'Turbo',
    upgradesTurboDescription: 'Wybuchowe przyspieszenie w dowolnym momencie',
    upgradesTransmission: 'Pudełko',
    upgradesTransmissionDescription: 'Błyskawiczna zmiana wszystkich biegów',
    upgradesSuspension: 'Zawieszenie',
    upgradesSuspensionDescription: 'Doskonały komfort na odpowiedniej wysokości',
    upgradesTotal: 'Koszt ulepszeń',
    upgradesDisclaimer: 'Ostateczny koszt może się różnić ze względu na prowizję w LSC na twoim serwerze',
    colorsTitle: 'Malowanie pojazdów',
    colorsDescription: 'Dostępne typy i rodzaje malowania wraz z kosztami',
    paintingsMainPrice: 'Malowanie podstawowe',
    paintingsSubPrice: 'Dodatkowe malowanie',
    paintingsOtherPrice: 'Inne malowanie',
    paintingsBrightMetallic: 'Jasny metaliczny',
    paintingsMetallic: 'Metaliczny',
    paintingsRichMetallic: 'Bogaty metaliczny',
    paintingsDarkMetallic: 'Ciemny metaliczny',
    paintingsMatte: 'Matowy',
    paintingsMatteMetallic: 'Matowy metal',
    paintingsSatin: 'Satyna',
    paintingsMetal: 'Metal',
    paintingsShadowChrome: 'Chromowany cień',
    paintingsPureChrome: 'Czysty chrom',
    paintingsPearl: 'Odcień perłowego',
    paintingsWheels: 'Kolor felg',
    paintingsDetails: 'Kolor części',
    paintingsAdditionalDetails: 'Dod. kolor części',
    paintingsLights: 'Światło reflektora',
    paintingsSmoke: 'Kolor dymu z opon',
    paintingsNeon: 'Kolor neonu',
    paintingsTotal: 'Ostateczny koszt malowania',
    shareLink: 'Udostępnij link do transportu',
    shareLinkCopied: 'Połączenie transportowe zostało pomyślnie skopiowane',
    addToCompare: 'Dodaj do porównania',
    addedToCompare: 'Transport został dodany do porównania',
    goToCompare: 'Przejdź do porównania',
    removeFromCompare: 'Usuń z porównania',
    removedFromCompare: 'Transport został usunięty z porównania'
  },
  vehiclesComparePage: {
    meta: {
      title: 'Porównanie transportu GTA5 RP | Majestic Wiki',
      description: 'Porównaj cechy i ceny różnych pojazdów na serwerze Majestic RP w GTA5 RP. Dowiedz się, który pojazd najlepiej odpowiada Twoim potrzebom! Porównaj prędkość, cenę, pojemność i inne parametry.'
    },
    name: 'Nazwa',
    type: 'Typ',
    brand: 'Marka',
    kits: 'Pakiety',
    model: 'Model',
    trunkCapacity: 'Pojemność bagażnika',
    maxSpeed: 'Prędkość maksymalna',
    maxSpeedFT: 'Prędkość maksymalna (FT)',
    price: 'Koszt',
    donatePrice: 'Koszt w Majestic Coin',
    sellPrice: 'Koszt sprzedaży',
    sellPriceBeforeTax: 'Koszt własny sprzedaży (przed opodatkowaniem)',
    sellPriceWithDiscount: 'Koszt sprzedaży (zdyskontowany)',
    accelerationTo100: 'Czas przyspieszania do 100 km/h',
    gasolineCapacity: 'Pojemność zbiornika',
    gasolineType: 'Rodzaj paliwa',
    isTradable: 'Przenaszalność',
    isBuyable: 'Opcja zakupu',
    createdAt: 'Data wystąpienia',
    gasolineCapacityUnit: 'l',
    maxSpeedUnit: 'km / h',
    maxSpeedFTUnit: 'km / h',
    priceUnit: '₽',
    accelerationTo100Unit: 'sec',
    trunkCapacityUnit: 'kg',
    showOnlyDifferent: 'Pokaż tylko te, które się różnią',
    showAll: 'Pokaż wszystkie',
    share: 'Udostępnij porównanie',
    linkCopied: 'Link porównawczy został pomyślnie skopiowany',
    openVehicle: 'Otwórz stronę transportu',
    deleteVehicle: 'Usuń pojazd z porównania',
    noDifferences: 'Nie ma rozróżnienia'
  },
  skinsPage: {
    meta: {
      title: 'Skórki broni RP w GTA5 | Majestic Wiki',
      description: 'Pełny katalog skórek broni na serwerze Majestic RP w GTA5 RP. Dowiedz się, jak zdobyć, ile kosztuje i jakie są funkcje każdej skórki dla różnych rodzajów broni.',
      custom: {
        title: 'Skórki na {type} | GTA5 RP | Majestic Wiki',
        description: 'Katalog skinów dla {type} na serwerze Majestic RP w GTA5 RP. Szczegółowe informacje o sposobie zdobycia, kosztach i właściwościach skinów.'
      }
    },
    title: 'Tabela skórek',
    search: 'Skin Search...',
    apPistol: 'Pistolet przeciwpancerny',
    assaultRifle: 'Karabin szturmowy',
    assaultRifleMk2: 'Karabin szturmowy Mk2',
    bullpupRifle: 'Karabin Bullpup',
    carbineRifle: 'Karabinek',
    carbineRifleMk2: 'Karabin Mk2',
    combatPDW: 'Bojowy PDW',
    gusenberg: 'Pistolet maszynowy Thompson.',
    heavyPistol: 'Ciężki pistolet',
    heavyShotgun: 'Ciężka strzelba',
    heavySniper: 'Ciężki karabin snajperski',
    heavySniperMk2: 'Ciężki karabin snajperski Mk2',
    lightvest: 'Kamizelka kuloodporna',
    machinePistol: 'Mały PP',
    marksmanPistol: 'Pistolet strzelecki',
    microSMG: 'Micro SMG',
    militaryRifle: 'Karabin wojskowy',
    revolver: 'Rewolwer',
    smgMk2: 'SMG Mk2',
    specialCarbine: 'Specjalny Karabin',
    vintagePistol: 'Zabytkowy pistolet',
    combatMgMk2: 'Pistolet maszynowy Mk2',
    heavyRifle: 'Ciężki karabin',
    tacticalRifle: 'Karabin taktyczny',
    tecPistol: 'Pistolet taktyczny',
    noName: 'Skóra #{id}',
    battlePass: 'Battle Pass',
    case: 'Skrzynka',
    unique: 'Wyjątkowy',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{skin} few{skin} many{skins} other{skins}}'
  },
  animationsPage: {
    meta: {
      title: 'Animacje GTA5 RP | Majestic Wiki',
      description: 'Pełny katalog animacji na serwerze Majestic RP w GTA5 RP. Wszystkie rodzaje animacji: akcje, pozy, tańce i animacje na wyłączność. Dowiedz się, jak zdobyć i ile kosztuje każda animacja.',
      custom: {
        title: '{type} | Animacje RP w GTA5 | Majestic Wiki',
        description: 'Katalog {type} animacji na serwerze Majestic RP w GTA5 RP. Szczegółowe informacje jak zdobyć, kosztować i używać animacji.'
      }
    },
    title: 'Tabela animacji',
    search: 'Wyszukiwanie animacji...',
    default: 'Standard',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Skrzynka',
    unique: 'Wyjątkowy',
    action: 'Czynności',
    pose: 'Pozy',
    positive: 'Pozytywne',
    negative: 'Negatywne',
    dances: 'Tańce',
    etc: 'Inne',
    exclusive: 'Ekskluzywne',
    noName: 'Animacja #{id}',
    pagingText: 'Znaleziono{N, plural, one{ą} few{y} many{y} other{}} {N} {N, plural, one{animację} few{animacje} many{animacji} other{animacji}}'
  },
  realtyPage: {
    meta: {
      title: 'GTA5 RP Nieruchomości | Majestic Wiki',
      description: 'Pełny katalog nieruchomości na serwerze Majestic RP w GTA5 RP. Domy, mieszkania, biura i magazyny ze szczegółową charakterystyką, cenami i lokalizacją. Sprawdź liczbę najemców, miejsca garażowe i inne parametry.',
      custom: {
        title: '{type} | GTA5 RP Real Estate | Majestic Wiki',
        description: 'Katalog {type} na serwerze Majestic RP w GTA5 RP. Szczegółowe informacje o charakterystyce, cenach, lokalizacji i cechach nieruchomości.'
      }
    },
    title: 'Tabela właściwości',
    search: 'Wyszukiwanie nieruchomości...',
    majesticMotors: 'Majestic Motors',
    house: 'Domy',
    apartment: 'Mieszkania',
    office: 'Biura',
    warehouse: 'Magazyny',
    tenantsFor: '{N} {N, plural, one{najemca} few{najemca} many{najemca} other{najemca}}',
    garageSlotsFor: '{N} {N, plural, one{miejsca garażowe} few{miejsca garażowe} many{miejsca garażowe} other{miejsca garażowe}}',
    viewOnMap: 'Wyświetl na mapie',
    mapPosition: 'Lokalizacja',
    pagingText: 'Znaleziono{N, plural, few{na temat} many{na temat}} {N} {N, plural, one{nieruchomości} few{nieruchomości} many{nieruchomości} other{nieruchomości}}',
    noName: 'Nieruchomości #{id}'
  },
  postsPage: {
    meta: {
      title: 'Edycja postu | Majestic Wiki',
      description: 'Twórz i edytuj posty na Majestic Wiki dla GTA5 RP. Publikuj aktualne informacje na temat transportu, nieruchomości, firm i innych aspektów gry.'
    },
    titlePlaceholder: 'Wprowadź tytuł postu',
    category: 'Kategoria',
    noCategory: 'Bez kategorii',
    availability: 'Dostępność',
    allView: 'Widoczność dla wszystkich',
    privateView: 'Ograniczony dostęp',
    relevance: 'Znaczenie',
    relevanceData: 'Aktualne informacje',
    outdatedData: 'Nieaktualne informacje',
    categoryPosition: 'Pozycja kategorii',
    categoryPositionDescription: 'Na przykład "50" lub "-5".',
    postDeleted: 'Post usunięty',
    author: 'Autor artykułu',
    editor: 'Redaktor artykułu',
    contents: 'Treść artykułu',
    readingTime: 'Czas czytania',
    readingTimeLength: '{N} {N, plural, one{minuta} few{minuta} many{minuta} other{minuta}}',
    publishDate: 'Publikacja z {date}',
    views: '{N} {N, plural, one{oglądanie} few{oglądanie} many{oglądanie} other{oglądanie}}',
    youLiked: 'Polubiłeś',
    youAndPeopleLiked: 'Ty też lubiłeś {likes}',
    peopleLiked: '{N} {N, plural, one{polubienia} few{polubienia} many{polubienia} other{polubienia}}',
    categories: 'Kategorie',
    posts: 'Posty',
    foundMistake: 'Znalazłeś błąd w artykule?',
    language: 'Język'
  },
  panelPage: {
    users: {
      title: 'Zarządzanie użytkownikami | Majestic Wiki',
      heading: 'Zarządzanie użytkownikami',
      admin: 'Administrator',
      user: 'Użytkownik',
      customUser: 'Użytkownik z uprawnieniami',
      search: 'Zacznij wpisywać wyszukiwane hasło....',
      comment: 'Powód',
      pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{user} few{user} many{users} other{users}}'
    },
    posts: {
      title: 'Zarządzanie postami | Majestic Wiki',
      heading: 'Zarządzanie pocztą',
      views: '{N} {N, plural, one{views} few{views} many{views} other{views}}',
      likes: '{N} {N, plural, one{polubienia} few{polubienia} many{polubienia} other{polubienia}}'
    },
    categories: {
      title: 'Zarządzanie kategoriami | Majestic Wiki',
      heading: 'Zarządzanie kategoriami'
    },
    mapLayers: {
      title: 'Zarządzanie warstwami mapy | Majestic Wiki',
      heading: 'Zarządzanie warstwami mapy'
    },
    logs: {
      title: 'Audyt logów - Majestic Wiki',
      heading: 'Audyt dzienników',
      search: 'Zacznij wpisywać wyszukiwane hasło....',
      biz: 'Biznesy',
      category: 'Kategoria',
      clothing: 'Odzież',
      file: 'Plik',
      item: 'Przedmiot',
      mapItem: 'Element mapy',
      mapLayer: 'Warstwa mapy',
      post: 'Stanowisko',
      realty: 'Nieruchomości',
      skin: 'Skóra',
      user: 'Użytkownik',
      vehicle: 'Pojazdy',
      animation: 'Animacja',
      actions: {
        unknown: '{username} dokonał nieznanego czynu',
        bizCreate: '{username} dodano biznes {id}',
        bizUpdate: '{username} zmieniony biznes {id}',
        bizDelete: '{username} usunięto biznes {id}',
        categoryCreate: '{username} dodano kategorię {id}',
        categoryUpdate: '{username} zmieniono kategorię {id}',
        categoryDelete: '{username} usunięto kategorię {id}',
        clothingCreate: '{username} dodano ubrania {id}',
        clothingUpdate: '{username} zmienił ubrania {id}',
        clothingDelete: '{username} usunięto ubrania {id}',
        fileCreate: '{username} dodano plik {id}',
        fileUpdate: '{username} zmodyfikował plik {id}',
        fileDelete: '{username} usunął plik {id}',
        itemCreate: '{username} dodano pozycję {id}',
        itemUpdate: '{username} zmieniłeś temat {id}',
        itemDelete: '{username} usunięto element {id}',
        mapItemCreate: '{username} dodano element do mapy {id} do "{layer}"',
        mapItemUpdate: '{username} zmieniono pozycję na mapie {id} na "{layer}"',
        mapItemDelete: '{username} usunięto element na mapie {id} do "{layer}"',
        mapLayerCreate: '{username} dodano warstwę mapy {id}',
        mapLayerUpdate: '{username} zmieniono warstwę mapy {id}',
        mapLayerDelete: '{username} usunięto warstwę mapy {id}',
        postCreate: '{username} dodano post {id}',
        postUpdate: '{username} zmodyfikowany post {id}',
        postDelete: '{username} usunięty post {id}',
        realtyCreate: '{username} dodano właściwość {id}',
        realtyUpdate: '{username} zmieniono nieruchomość {id}',
        realtyDelete: '{username} usunięto nieruchomość {id}',
        skinCreate: '{username} dodano skórkę {id}',
        skinUpdate: '{username} zmieniono skórkę {id}',
        skinDelete: '{username} usunięto skórkę {id}',
        userCreate: '{username} dodano użytkownika {id}',
        userUpdate: '{username} zmienił użytkownika {id}',
        userDelete: '{username} usunięty użytkownik {id}',
        vehicleCreate: '{username} dodano transport {id}',
        vehicleUpdate: '{username} zmienił transport {id}',
        vehicleDelete: '{username} usunięto transport {id}',
        animationCreate: '{username} dodano animację {id}',
        animationUpdate: '{username} zmieniono animację {id}',
        animationDelete: '{username} usunięto animację {id}',
        changelogCreate: '{username} dodano dziennik zmian {id}',
        changelogUpdate: '{username} zmieniono dziennik zmian {id}',
        changelogDelete: '{username} usunięto dziennik zmian {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: 'Ustawienia',
      users: 'Użytkownicy',
      categories: 'Kategorie',
      mapLayers: 'Warstwy mapy',
      posts: 'Posty',
      logs: 'Dzienniki'
    }
  },
  changelogPage: {
    titlePlaceholder: 'Wprowadź nazwę zmiany',
    descriptionPlaceholder: 'Wprowadź opis zmiany',
    availableAt: 'Data publikacji',
    soon: 'poprzez',
    ago: 'cofnij',
    fix: 'Poprawiono',
    feat: 'Dodano',
    chore: 'Zmodyfikowany',
    style: 'Projekt',
    refactor: 'Z recyklingu'
  },
  changelogsPage: {
    meta: {
      title: 'Historia zmian | Majestic Wiki',
      description: 'Pełna historia aktualizacji i zmian na serwerze Majestic RP. Wypatruj nowych funkcji, ulepszeń rozgrywki, poprawek błędów i balansu. Aktualne informacje o wszystkich aktualizacjach projektu.'
    },
    title: 'Historia zmian',
    search: 'Znajdowanie zmian...'
  },
  homePage: {
    meta: {
      title: 'Majestic Wiki | Encyklopedia GTA5 RP',
      description: 'Majestic Wiki to kompletna encyklopedia dla graczy GTA5 RP. Znajdziesz tu informacje o transporcie, nieruchomościach, firmach, zadaniach i innych aspektach gry. Aktualne informacje o cenach, funkcjach i sposobach ich uzyskania.'
    },
    tools: {
      map: {
        title: 'Mapa',
        description: 'Mapa serwera ze wszystkimi lokalizacjami'
      },
      vehicles: {
        title: 'Pojazdy',
        description: 'Cały dostępny transport na serwerze'
      },
      realty: {
        title: 'Nieruchomości',
        description: 'Dane dla wszystkich domów i mieszkań'
      },
      biz: {
        title: 'Biznesy',
        description: 'Zaawansowana opcja zarabiania'
      },
      clothesMale: {
        title: 'Odzież męska',
        description: 'Pokaz mody dla mężczyzn'
      },
      clothesFemale: {
        title: 'Odzież damska',
        description: 'Pokaz mody dla kobiet'
      },
      skins: {
        title: 'Skiny',
        description: 'Katalog wszystkich dostępnych wzorów'
      },
      items: {
        title: 'Przedmioty',
        description: 'Elementy dostępne na serwerze'
      },
      servers: {
        title: 'Serwery',
        description: 'Lista wszystkich serwerów Majestic RP'
      },
      animations: {
        title: 'Animacje',
        description: 'Unikalne animacje na serwerze'
      },
      changelogs: {
        title: 'Historia zmian',
        description: 'Historia zmian w projekcie'
      }
    },
    search: {
      title: 'Kategorie ogólne',
      description: 'Tutaj możesz znaleźć wszelkie informacje o serwerze Majestic i jego systemach'
    },
    categories: {
      collapseList: 'Zwiń listę',
      expandList: 'Rozwiń listę'
    },
    online: {
      players: '{count} online',
      offline: 'Wyłączony'
    }
  },
  errorPage: {
    title: 'Nie znaleziono strony',
    description: 'Możesz mieć literówkę w adresie strony lub po prostu ona nie istnieje :(.',
    backToHome: 'Przejdź do strony głównej'
  },
  bizPage: {
    meta: {
      title: 'GTA5 RP Businesses | Majestic Wiki',
      description: 'Pełny katalog biznesów na serwerze Majestic RP w GTA5 RP. Sklepy całodobowe, stacje benzynowe, sklepy z bronią, salony samochodowe i inne biznesy. Dowiedz się o opłacalności, wymaganiach i sposobie zakupu każdego biznesu.',
      custom: {
        title: '{type} | GTA5 RP Businesses | Majestic Wiki',
        description: 'Szczegółowe informacje o {type} na serwerze Majestic RP w GTA5 RP. Charakterystyka, opłacalność, wymagania i jak kupić firmę.'
      }
    },
    title: 'Tabela firm',
    search: 'Wyszukiwanie biznesowe...',
    groceryStore: 'Sklep 24/7',
    gasStation: 'Tankowanie',
    atm: 'Bankomat',
    gunshop: 'Sklep z bronią',
    clothingStore: 'Sklep odzieżowy',
    autoShop: 'Autosalon',
    tattoo: 'Salon tatuażu',
    lsc: 'Tuning salon',
    barbershop: 'Barbershop',
    carWash: 'Myjnia samochodowa',
    viewOnMap: 'Wyświetl na mapie',
    mapPosition: 'Lokalizacja',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{businesses} few{businesses} many{businesses} other{businesses}}',
    noName: 'Biznes #{id}'
  },
  clothesPage: {
    meta: {
      title: 'Odzież GTA5 RP | Majestic Wiki',
      description: 'Pełny katalog odzieży na serwerze Majestic RP w GTA5 RP. Odzież męska i damska, akcesoria, plecaki, naszywki, czapki i inne elementy garderoby. Dowiedz się, jak zdobyć i ile kosztuje każdy element garderoby.',
      custom: {
        title: '{type} | Odzież GTA5 RP | Majestic Wiki',
        description: 'Katalog {type} na serwerze Majestic RP w GTA5 RP. Szczegółowe informacje o tym jak zdobyć, używać i ile kosztują elementy ubioru.'
      }
    },
    titleMale: 'Tabela odzieży męskiej',
    titleFemale: 'Tabela odzieży damskiej',
    search: 'Wyszukiwanie odzieży...',
    top: 'Góra',
    legs: 'Dół',
    shoes: 'Obuwie',
    watch: 'Godzina',
    mask: 'Maski',
    decal: 'Naszywki',
    accessory: 'Akcesoria',
    head: 'Nakrycie głowy',
    bag: 'Plecaki',
    glasses: 'Przygotowane',
    ear: 'Uszy',
    gloves: 'Rękawice',
    undershirt: 'Podkoszulki',
    noName: 'Komplet ubrań #{id}',
    drawable: 'Rysowalny',
    texture: 'Tekstura',
    male: 'Mężczyzna',
    female: 'Kobieta',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Skrzynka',
    unique: 'Wyjątkowy',
    pagingText: 'Znaleziono{N, plural, few{na temat} many{na temat}} {N} {N, plural, one{odzież} few{odzież} many{odzież} other{odzież}}'
  },
  itemsPage: {
    meta: {
      title: 'Przedmioty RP w GTA5 | Majestic Wiki',
      description: 'Pełny katalog przedmiotów na serwerze Majestic RP w GTA5 RP. Antyki, ryby, ruda, drewno, skrzynie, materiały i inne przedmioty. Dowiedz się jak zdobyć, używać i ile kosztują poszczególne przedmioty.',
      custom: {
        title: '{type} | Przedmioty RP w GTA5 | Majestic Wiki',
        description: 'Katalog {type} na serwerze Majestic RP w GTA5 RP. Szczegółowe informacje jak zdobyć, używać i ile kosztują przedmioty.'
      }
    },
    title: 'Tabela tematów',
    search: 'Znajdowanie obiektów...',
    antique: 'Antyki',
    fish: 'Ryby',
    ore: 'Ruda',
    wood: 'Drewno',
    box: 'Pudełka',
    material: 'Materiały',
    event: 'Wydarzenie',
    food: 'Produkty',
    alcohol: 'Alkohol',
    illegal: 'Nielegalny',
    medical: 'Medycyna',
    equipment: 'Sprzęt',
    ammunition: 'Ekwipunek',
    tool: 'Narzędzie',
    unique: 'Wyjątkowy',
    vehicle: 'Części samochodowe ',
    others: 'Różny',
    default: 'Standard',
    battlePass: 'Battle Pass',
    case: 'Skrzynka',
    unique: 'Wyjątkowy',
    pagingText: 'Znaleziono{N, plural, few{about} many{about}} {N} {N, plural, one{item} few{item} many{items} other{items}}',
    noName: 'Temat #{id}'
  },
  mapPage: {
    meta: {
      title: 'Mapa GTA5 RP | Majestic Wiki',
      description: 'Interaktywna mapa serwera Majestic RP w GTA5 RP. Eksploruj lokacje, znajduj skarby, zwierzęta, telefony i śmieci. Szczegółowa mapa świata z możliwością dodawania tagów i stref. Wszystkie ważne miejsca i punkty zainteresowania w jednym zasobie!',
      custom: {
        title: '{type} | Mapa RP GTA5 | Majestic Wiki',
        description: 'Szczegółowa mapa {type} na serwerze Majestic RP w GTA5 RP. Eksploruj lokacje, odnajduj ważne punkty i twórz własne znaczniki. Pełna informacja o położeniu obiektów na mapie!'
      }
    },
    dataLoading: 'Ładowanie danych...',
    defaultPoints: 'Standardowe znaczniki',
    animalLayer: 'Mapa zwierząt',
    rubbishLayer: 'Mapa pojemników na śmieci',
    telephoneLayer: 'Mapa telefonu',
    treasureLayer: 'Mapa skarbów',
    defaultLayer: 'Mapa świata',
    allLayers: 'Warstwy mapy',
    backToHome: 'Powrót do strony głównej',
    zoomIn: 'Podejście',
    zoomOut: 'Pociągnij do tyłu',
    point: 'Punkt',
    polygon: 'Strefa',
    addPoint: 'Dodaj punkt',
    addPolygon: 'Dodaj strefę',
    copyCoordinates: 'Kopiuj współrzędne',
    savePointPosition: 'Zapisz pozycję',
    editPointPosition: 'Zmień pozycję',
    layerNamePlaceholder: 'Nazwa strefy',
    layerDescriptionPlaceholder: 'Opis strefy'
  },
  serversPage: {
    meta: {
      title: 'Serwery RP online GTA5 | Majestic Wiki',
      description: 'Aktualne statystyki graczy online na serwerach Majestic RP w GTA5 RP. Aktualne online, szczyty, kolejki i wykresy aktywności. Dołącz do najpopularniejszego projektu fabularnego!'
    },
    dataLoading: 'Ładowanie danych...',
    title: 'Serwery online',
    online: '{count} online',
    inQueue: '{count} w kolejce',
    peakOnline: '{count} szczyt',
    currentOnline: 'aktualny online',
    peakOnline24h: 'szczyt dzisiaj',
    peakOnlineAllTime: 'szczyt wszech czasów',
    peakOnlineTooltip: '{percent}% w stosunku do poprzedniego dnia',
    techWorks: 'Prace techniczne',
    forBeginners: 'Dla początkujących',
    experience: '{multiplier}Doświadczenie x',
    newServer: 'Nowy',
    seasonPassMultiplier: '{multiplier}Pomiń doświadczenie x',
    combinedView: 'Ogólny',
    separateView: 'Oddzielnie'
  },
  research: {
    placeholder: 'Szukaj informacji na stronie internetowej...',
    skin: 'Skóra',
    vehicle: 'Pojazdy',
    realty: 'Nieruchomości',
    biz: 'Biznesy',
    clothing: 'Odzież',
    item: 'Przedmiot',
    post: 'Stanowisko'
  },
  userModal: {
    login: 'Wejdź',
    signout: 'Wyjść',
    register: 'Rejestracja',
    registerEnd: 'Zarejestrować się',
    restore: 'Przywrócenie',
    restoreEnd: 'Przywrócić',
    avatarDelete: 'Usuń awatar',
    headingUpdate: 'Modyfikowanie użytkownika #{id}',
    postAdd: 'Publikowanie postów',
    postUpdate: 'Edytowanie postów',
    postDelete: 'Usuwanie postów',
    vehicleAdd: 'Dodawanie transportu',
    vehicleUpdate: 'Edycja transportu',
    vehicleDelete: 'Usunięcie transportu',
    realtyAdd: 'Dodawanie właściwości',
    realtyUpdate: 'Zmiana na rynku nieruchomości',
    realtyDelete: 'Usunięcie nieruchomości',
    bizAdd: 'Dodawanie firmy',
    bizUpdate: 'Zmiana biznesowa',
    bizDelete: 'Usunięcie przedsiębiorstwa',
    mapItemAdd: 'Dodawanie elementów do mapy',
    mapItemUpdate: 'Zmiana elementów na mapie',
    mapItemDelete: 'Usuwanie elementów na mapie',
    clothingAdd: 'Dodawanie ubrań',
    clothingUpdate: 'Zmiana odzieży',
    clothingDelete: 'Usuwanie odzieży',
    skinAdd: 'Dodawanie skórek',
    skinUpdate: 'Zmiana skórek',
    skinDelete: 'Usuwanie skórek',
    itemAdd: 'Dodawanie elementów',
    itemUpdate: 'Zmiana obiektów',
    itemDelete: 'Usuwanie przedmiotów',
    fileUpload: 'Przesyłanie obrazów',
    viewPrivate: 'Wyświetl prywatne dane',
    doMagic: 'Dostęp do funkcji magicznych',
    animationAdd: 'Dodawanie animacji',
    animationUpdate: 'Zmiana animacji',
    animationDelete: 'Usuwanie animacji',
    categoryAdd: 'Dodawanie kategorii',
    categoryUpdate: 'Zmiana kategorii',
    categoryDelete: 'Usuwanie kategorii',
    mapLayerAdd: 'Dodawanie warstw mapy',
    mapLayerUpdate: 'Zmiana warstw mapy',
    mapLayerDelete: 'Usuwanie warstw mapy',
    post: 'Stanowisko',
    skin: 'Skóra',
    biz: 'Biznesy',
    clothing: 'Odzież',
    item: 'Przedmiot',
    vehicle: 'Pojazdy',
    animation: 'Animacja',
    category: 'Kategoria',
    realty: 'Nieruchomości',
    mapLayer: 'Warstwa mapy',
    changelog: 'Dziennik zmian'
  },
  restoreModal: {
    title: 'Przywracanie konta',
    newPassword: 'Nowe hasło',
    newPasswordRepeat: 'Powtórz nowe hasło',
    passwordConfirm: 'Zmień hasło',
    passwordError: 'Hasła się nie zgadzają'
  },
  bizModal: {
    titleUpdate: 'Zmiana biznesu #{id}',
    titleAdd: 'Dodawanie nowej firmy'
  },
  categoryModal: {
    titleUpdate: 'Zmiana kategorii #{id}',
    titleAdd: 'Dodawanie nowej kategorii'
  },
  mapLayerModal: {
    titleUpdate: 'Zmiana warstwy mapy #{id}',
    titleAdd: 'Dodawanie nowej warstwy mapy'
  },
  clothingModal: {
    titleUpdate: 'Zmiana odzieży #{id}',
    titleAdd: 'Dodawanie nowych ubrań',
    itemAdd: 'Dodaj kolor',
    itemGenerate: 'Wygeneruj wszystkie kolory'
  },
  itemModal: {
    titleUpdate: 'Zmiana tematu #{id}',
    titleAdd: 'Dodawanie nowego elementu'
  },
  realtyModal: {
    titleUpdate: 'Zmiana nieruchomości #{id}',
    titleAdd: 'Dodawanie nowej właściwości'
  },
  skinModal: {
    titleUpdate: 'Zmiana skóry #{id}',
    titleAdd: 'Dodawanie nowej skórki'
  },
  vehicleModal: {
    titleUpdate: 'Zmiana transportu #{id}',
    titleAdd: 'Dodawanie nowego transportu',
    titleDefault: 'Informacje o {name}',
    itemAdd: 'Dodaj osłonę'
  },
  mapItemModal: {
    titleUpdate: 'Zmiana elementu mapy',
    titleAdd: 'Dodawanie nowego elementu mapy',
    titleDelete: 'Usuwanie elementu mapy',
    descriptionDelete: 'Czy na pewno chcesz usunąć przedmiot z mapy? Tej akcji nie można anulować.'
  },
  animationModal: {
    titleUpdate: 'Zmiana animacji #{id}',
    titleAdd: 'Dodawanie nowej animacji'
  }
};