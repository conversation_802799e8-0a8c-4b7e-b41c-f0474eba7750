export default {
  global: {
    all: '全部',
    back: '返回',
    forward: '我们走',
    paging: '{page} of {maxPage}',
    close: '关闭',
    clear: '清晰',
    save: '节省',
    delete: '删除',
    deleteYes: '你确定要拆除吗？',
    cancel: '拒绝',
    publish: '出版',
    edit: '编辑',
    share: '分享',
    add: '添加。',
    active: '活跃',
    deleted: '已删除',
    blocked: '受阻',
    learnMore: '更多信息',
    panel: '小组',
    theme: '主题',
    language: '语言',
    user: '用户',
    confirm: '确认',
    restore: '修复',
    yes: '有',
    no: '不',
    new: '新',
    preview: '预览',
    sortPriceDesc: '按价格降序排列',
    sortPriceAsc: '按价格升序排列',
    sortPriceMinMax: '价格范围',
    sortDateDesc: '按日期降序排列',
    sortDateAsc: '按日期升序排列',
    noSellPrice: '不可购买',
    noBuyPrice: '非卖品',
    noData: {
      title: '未找到',
      description: '尝试更改或简化搜索查询'
    },
    battlePass: {
      summer: '夏季战斗通行证',
      winter: '冬季战斗通行证'
    },
    case: {
      default: '标准箱',
      vehicles: '汽车箱',
      halloween: '万圣节案例',
      spring: '春季公文包',
      autumn: '秋季公文包',
      summer: '夏季案例',
      summerExtra: '夏季特例',
      summerSkins: '夏季皮套',
      summerVehicles: '夏季运输箱',
      winter: '冬季案例',
      winterExtra: '冬季特例',
      winterSkins: '带皮肤的冬季便携箱',
      winterVehicles: '冬季运输箱',
      toys: '玩具盒'
    },
    subscription: { premium: '高级订阅' },
    copy: '复制'
  },
  footer: { disclaimer: '网站上的所有信息仅供参考，并不构成公开要约。' },
  footer: { disclaimer: '网站上的所有信息仅供参考，并不构成公开要约。' },
  editor: {
    placeholder: '让我们写一篇精彩的文章吧！',
    text: '段落',
    heading: '标题',
    warning: '备注',
    quote: '报价',
    code: '代码',
    delimiter: '分离器',
    link: '参考资料',
    linkPlaceholder: '输入链接',
    marker: '标记',
    bold: '粗体',
    italic: '斜体',
    inlineCode: '单倍行距',
    underline: '强调',
    copytext: '可复制',
    log: '罗格',
    map: {
      name: '地图',
      params: '输入卡参数',
      caption: '输入地图的描述'
    },
    table: {
      name: '表格',
      addColumnLeft: '在左侧添加一栏',
      addColumnRight: '在右侧添加栏',
      deleteColumn: '删除栏目',
      addRowAbove: '在顶部添加一行',
      addRowBelow: '在底部添加一行',
      deleteRow: '删除字符串',
      withHeadings: '头条新闻',
      withoutHeadings: '无头条新闻'
    },
    image: {
      name: '图片',
      withBorder: '带框架',
      stretchImage: '拉伸图片',
      withBackground: '背景',
      selectImage: '选择图像',
      uploadError: '加载图像失败，请尝试其他图像',
      caption: '图片说明',
      withCaption: '附带说明'
    },
    carousel: {
      name: '图像网格',
      caption: '图片说明',
      addImage: '选择图像'
    },
    list: {
      name: '列表',
      ordered: '编号',
      unordered: '无编号',
      checklist: '清单'
    },
    header: {
      heading1: '标题 H1',
      heading2: '标题 H2',
      heading3: '标题 H3',
      heading4: '页眉 H4'
    },
    embed: { caption: '链接说明' },
    convertTo: '转换为',
    moveUp: '向上移动',
    moveDown: '下移',
    anchor: '锚',
    clickToTune: '按下 进行配置',
    dragToMove: '或拖放',
    filter: '搜索',
    nothingFound: '未找到'
  },
  alerts: {
    loginSuccess: '登录成功',
    loginError: '授权时出错',
    registerSuccess: '在网站上成功注册',
    registerError: '注册错误',
    restoreSuccess: '恢复说明已发送到您的邮箱',
    restoreError: '恢复账户访问权限时出错',
    signoutSuccess: '成功注销',
    postLoadError: '上传帖子时出错',
    postShareSuccess: '复制到剪贴板的帖子链接',
    copyCommandSuccess: '命令被复制到剪贴板',
    restoreAccountSuccess: '账户恢复成功',
    restoreAccountError: '恢复账户访问权限时出错',
    verificateAccountSuccess: '邮箱成功确认',
    verificateAccountError: '邮箱确认错误',
    dataUpdateSuccess: '成功更新信息',
    dataUpdateError: '更新信息时出错',
    dataDeleteSuccess: '成功删除信息',
    dataDeleteError: '删除信息时出错',
    dataAddSuccess: '成功添加信息',
    dataAddError: '添加信息时出错',
    mapMoveInfo: '将点移动到另一个位置并保存',
    mapCoordinatesCopySuccess: '坐标复制成功',
    copyTextSuccess: '成功复制文本'
  },
  mail: {
    verificate: {
      subject: '确认电子邮件地址',
      title: '确认',
      description: '要验证和确认您的账户权限，请点击下面的按钮。该链接的有效期为 24 小时。'
    },
    restore: {
      subject: '恢复账户访问权限',
      title: '修复',
      description: '要恢复账户访问权限，您需要点击下面的按钮。该链接的有效期为 24 小时。'
    },
    restoreSuccess: {
      subject: '成功恢复账户访问权限',
      title: '成功恢复',
      description: '您已成功更改了网站密码。如果没有，请立即更改密码！'
    },
    verificateSuccess: {
      subject: '成功恢复账户访问权限',
      title: '成功确认',
      description: '您已成功确认邮箱，欢迎使用！'
    },
    caption: '该邮件为自动生成的通知，请勿回复。'
  },
  fields: {
    price: '费用',
    buyPrice: '购买成本',
    sellPrice: '销售成本',
    mcPrice: '按 MC 计算的购买成本',
    money: '货币',
    coin: 'Majestic Coin',
    gift: '礼物',
    default: '标准',
    unique: '独特性',
    event: '活动',
    battlePass: '战斗通行证',
    lootbox: '案例',
    capacityKg: '容量',
    email: '邮件',
    password: '密码',
    username: '昵称',
    weightKg: '公斤',
    speed: '速度',
    maxSpeed: '最大速度',
    maxSpeedFT: '最大速度（英尺）',
    accelerationTo100: '加速至 100 公里/小时',
    speedSystem: '公里/小时',
    timeSystem: '秒钟',
    uploadDescription: '触摸或拖动图像到某个区域',
    placeholder: '在字段中输入数值',
    imagePlaceholder: '输入图片链接',
    selectPlaceholder: '从列表中选择一个值',
    videoPlaceholder: '输入视频链接',
    soundPlaceholder: '输入音频链接',
    id: '身份证',
    serial: '序列号',
    kind: '类型',
    category: '类别',
    name: '标题',
    view: '查看',
    layer: '层数',
    icon: '图标',
    circle: '点',
    polygon: '多边形',
    emoji: '表情符号',
    description: '说明',
    gender: '性别',
    value: '价值类型',
    valueName: '附加价格说明',
    visibility: '无障碍环境',
    visibilityPrivate: '限制进入',
    visibilityPublic: '人人可见',
    activityHidden: '不向公众开放',
    activityVisible: '向所有人提供',
    garageSlots: '车库插槽',
    maxTenants: '居民人数',
    gasoline: '燃料',
    brand: '品牌',
    model: '模型',
    trunk: '后备箱',
    tradable: '可转让性',
    buyable: '购买选择',
    upgradesPriceFT: '可用的改进（FT）',
    upgradesEngine: '引擎',
    upgradesBreaks: '刹车',
    upgradesSuspension: '悬架',
    upgradesTurbo: '涡轮',
    upgradesTransmission: '箱',
    paintingsMainPrice: '基本绘画',
    paintingsSubPrice: '附加油漆',
    paintingsOtherPrice: '其他绘画',
    paintingsBrightMetallic: '明亮的金属色',
    paintingsMetallic: '金属',
    paintingsRichMetallic: '丰富的金属质感',
    paintingsDarkMetallic: '深色金属',
    paintingsMatte: '亚光',
    paintingsMatteMetallic: '拉丝金属',
    paintingsSatin: '纱卡',
    paintingsMetal: '金属',
    paintingsShadowChrome: '阴影铬',
    paintingsPureChrome: '纯铬',
    paintingsPearl: '珠光色调',
    paintingsWheels: '轮圈颜色',
    paintingsDetails: '部件颜色',
    paintingsAdditionalDetails: '部件的其他颜色',
    paintingsLights: '头灯照明',
    paintingsSmoke: '轮胎烟雾的颜色',
    paintingsNeon: '霓虹灯的颜色',
    total: '总计',
    language: '语言',
    radius: '半径',
    color: '颜色',
    colorPlaceholder: '选择颜色',
    opacity: '透明度',
    alias: '化名',
    aliases: '别名',
    looped: '循环',
    battlePassId: '战斗通行证 ID',
    gasolineCapacityLiter: 'л',
    gasolineCapacity: '油箱容量',
    source: '资料来源',
    sources: '资料来源',
    priceDescription: '成本说明',
    layerName: '图层名称',
    layerNamePlaceholder: '输入图层名称',
    layerDescription: '层描述',
    layerDescriptionPlaceholder: '输入图层描述',
    layerColor: '图层颜色',
    layerColorPlaceholder: '选择颜色',
    layerOpacity: '图层透明度',
    zIndexOffset: 'Z 索引级别',
    zIndexOffsetPlaceholder: '输入 Z 索引级别的值',
    comment: '评论'
  },
  vehiclesPage: {
    meta: {
      title: 'GTA5 RP 运输和汽车 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 服务器上的全部车辆目录。汽车、摩托车、直升机、飞机和船只的详细特性。了解价格、速度、机动性、行李箱容量和其他参数。Majestic Motors、Cars、Trucks、Helicopters、Boat、Motorcycles、Bikes - 全部适用于 GTA5 RP！',
      custom: {
        title: '{type} | GTA5 RP Transport | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 服务器上的详细目录 {type} 。特性、价格、速度、机动性和其他参数。GTA5 RP 运输工具的全部信息！'
      }
    },
    default: '标准',
    donate: 'Majestic Coin',
    battlePass: '战斗通行证',
    case: '案例',
    unique: '独特性',
    title: '运输表',
    search: '运输搜索器...',
    irl: 'Majestic Motors',
    car: '汽车',
    freight: '货车',
    helicopter: '直升机',
    plane: 'Самолеты',
    boat: '船只',
    motorbike: '摩托车',
    bike: '自行车',
    trailer: '预告片',
    military: '军事',
    special: '专业',
    priceDisclaimer: '最终成本可能会因服务器上的 LSC 佣金而有所不同',
    basicInformation: '基本信息',
    additionalInformation: '其他信息',
    compare: '比较',
    compareLimit: '最多可比较 5 个 TC',
    pagingText: '发现{N, plural, few{关于} many{关于}} {N} {N, plural, one{运输} few{运输} many{运输} other{运输}}'
  },
  vehiclePage: {
    meta: {
      title: 'GTA5 RP Transport | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 服务器上有关交通工具的详细信息。特点、价格、改进和喷漆。了解每辆车的最高速度、加速度、行李箱容量、改进成本和其他参数。',
      custom: {
        title: '{name} | GTA5 RP Transport | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 服务器上有关 {name} 的详细信息。功能、价格、改进和涂装。了解最高速度、加速度、启动容量、改进成本和其他参数。'
      }
    },
    backToTable: '返回列表',
    labelsUniqueId: '唯一 ID',
    labelsPrice: '费用',
    labelsBuyPrice: '购买成本',
    labelsSellPrice: '销售成本',
    labelsMaxSpeed: '最大速度',
    labelsMaxSpeedFT: '最大速度（英尺）',
    labelsAccelerationTo100: '加速至 100 公里/小时',
    labelsTradable: '可转让性',
    labelsTrunk: '靴子容量',
    labelsGasolineCapacity: '油箱容量',
    labelsGasolineType: '燃料类型',
    upgradesTitle: '改善交通',
    upgradesDescription: '升级到最高级别的费用',
    upgradesEngine: '引擎',
    upgradesEngineDescription: '释放交通运输的真正潜力',
    upgradesBreaks: '刹车',
    upgradesBreaksDescription: '在任何道路上都能完全控制车速',
    upgradesTurbo: '涡轮',
    upgradesTurboDescription: '随时爆发的加速度',
    upgradesTransmission: '箱',
    upgradesTransmissionDescription: '所有档位均可快速换挡',
    upgradesSuspension: '悬架',
    upgradesSuspensionDescription: '高度适宜，完美舒适',
    upgradesTotal: '改进费用',
    upgradesDisclaimer: '最终成本可能会因服务器上的 LSC 佣金而有所不同',
    colorsTitle: '车辆喷漆',
    colorsDescription: '现有油漆类型和种类及成本',
    paintingsMainPrice: '基本绘画',
    paintingsSubPrice: '附加油漆',
    paintingsOtherPrice: '其他绘画',
    paintingsBrightMetallic: '明亮的金属色',
    paintingsMetallic: '金属',
    paintingsRichMetallic: '丰富的金属质感',
    paintingsDarkMetallic: '深色金属',
    paintingsMatte: '亚光',
    paintingsMatteMetallic: '拉丝金属',
    paintingsSatin: '纱卡',
    paintingsMetal: '金属',
    paintingsShadowChrome: '阴影铬',
    paintingsPureChrome: '纯铬',
    paintingsPearl: '珠光色调',
    paintingsWheels: '轮圈颜色',
    paintingsDetails: '部件颜色',
    paintingsAdditionalDetails: '部件的其他颜色',
    paintingsLights: '头灯照明',
    paintingsSmoke: '轮胎烟雾的颜色',
    paintingsNeon: '霓虹灯的颜色',
    paintingsTotal: '最终油漆费用',
    shareLink: '分享运输链接',
    shareLinkCopied: '运输链路已成功复制',
    addToCompare: '添加到比较',
    addedToCompare: '运输已加入比较',
    goToCompare: '转到比较',
    removeFromCompare: '从比较中移除',
    removedFromCompare: '运输已从比较中删除'
  },
  vehiclesComparePage: {
    meta: {
      title: 'GTA5 RP 运输对比 | Majestic Wiki',
      description: '比较《GTA5》RP 中 Majestic RP 服务器上不同车辆的功能和价格。找出最适合您需求的车辆！比较速度、价格、容量和其他参数。'
    },
    name: '标题',
    type: '类型',
    brand: '品牌',
    kits: '套餐',
    model: '模型',
    trunkCapacity: '靴子容量',
    maxSpeed: '最大速度',
    maxSpeedFT: '最大速度（英尺）',
    price: '费用',
    donatePrice: 'Majestic Coin 的成本',
    sellPrice: '销售成本',
    sellPriceBeforeTax: '销售成本（税前）',
    sellPriceWithDiscount: '销售成本（贴现）',
    accelerationTo100: '加速到 100 km/h 所需的时间',
    gasolineCapacity: '油箱容量',
    gasolineType: '燃料类型',
    isTradable: '可转让性',
    isBuyable: '购买选择',
    createdAt: '出庭日期',
    gasolineCapacityUnit: 'л',
    maxSpeedUnit: '公里/小时',
    maxSpeedFTUnit: '公里/小时',
    priceUnit: '₽',
    accelerationTo100Unit: '秒钟',
    trunkCapacityUnit: '公斤',
    showOnlyDifferent: '只显示不同的',
    showAll: '显示全部',
    share: '分享比较',
    linkCopied: '比较链接已成功复制',
    openVehicle: '打开运输页面',
    deleteVehicle: '从比较中移除车辆',
    noDifferences: '没有区别'
  },
  skinsPage: {
    meta: {
      title: 'GTA5 RP 武器皮肤 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 服务器上武器皮肤的完整目录。了解不同类型武器皮肤的获取方式、成本和特性。',
      custom: {
        title: '皮肤 {type} | GTA5 RP | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 服务器 {type} 的皮肤目录。详细介绍了皮肤的获取方式、成本和功能。'
      }
    },
    title: '皮肤表',
    search: '皮肤搜索...',
    apPistol: '穿甲手枪',
    assaultRifle: '突击式步枪',
    assaultRifleMk2: 'Mk2 突击步枪',
    bullpupRifle: '小斗步枪',
    carbineRifle: '卡宾枪步枪',
    carbineRifleMk2: 'Mk2卡宾枪步枪',
    combatPDW: '战斗PDW',
    gusenberg: '汤姆逊冲锋枪',
    heavyPistol: '重型手枪',
    heavyShotgun: '重型霰弹枪',
    heavySniper: '重型狙击步枪',
    heavySniperMk2: '重型狙击步枪Mk2',
    lightvest: '防弹背心',
    machinePistol: '小 PP',
    marksmanPistol: '神枪手枪',
    microSMG: 'Micro SMG',
    militaryRifle: '军用步枪',
    revolver: '左轮手枪',
    smgMk2: 'SMG Mk2',
    specialCarbine: '特种卡宾枪',
    vintagePistol: '老式手枪',
    combatMgMk2: 'Mk2 冲锋枪',
    heavyRifle: '重型步枪',
    tacticalRifle: '战术步枪',
    tecPistol: '战术 SMG',
    noName: '皮肤 #{id}',
    battlePass: '战斗通行证',
    case: '案例',
    unique: '独特性',
    pagingText: '找到{N, plural, few{关于} many{关于}} {N} {N, plural, one{皮肤} few{皮肤} many{皮肤} other{皮肤}}'
  },
  animationsPage: {
    meta: {
      title: 'GTA5 RP 动画 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 服务器上的全部动画目录。所有类型的动画：动作、姿势、舞蹈和专属动画。了解每个动画的获取方式和成本。',
      custom: {
        title: '{type} | GTA5 RP 动画 | Majestic Wiki',
        description: '{type} GTA5 RP 中 Majestic RP 服务器上的动画目录。关于如何获取、花费和使用动画的详细信息。'
      }
    },
    title: '动画表',
    search: '动画搜索...',
    default: '标准',
    donate: 'Majestic Coin',
    battlePass: '战斗通行证',
    case: '案例',
    unique: '独特性',
    action: '行动',
    pose: '姿势',
    positive: '积极的',
    negative: '底片',
    dances: '舞蹈',
    etc: '其他',
    exclusive: '独家',
    noName: '动画 #{id}',
    pagingText: '找到{N, plural, one{个} few{个} many{个} other{个}} {N} {N, plural, one{动画} few{动画} many{动画} other{动画}}'
  },
  realtyPage: {
    meta: {
      title: 'GTA5 RP Real Estate | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 服务器上房地产的完整目录。房屋、公寓、办公室和仓库的详细特征、价格和位置。查看租户数量、车库空间和其他参数。',
      custom: {
        title: '{type} | GTA5 RP Real Estate | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 服务器上的目录 {type} 。详细介绍了房产的特点、价格、位置和功能。'
      }
    },
    title: '属性表',
    search: '物业搜索...',
    majesticMotors: 'Majestic Motors',
    house: '在家里',
    apartment: '公寓',
    office: '办事处',
    warehouse: '仓库',
    tenantsFor: '{N} {N, plural, one{租户} few{租户} many{租户} other{租户}}',
    garageSlotsFor: '{N} {N, plural, one{车库空间} few{车库空间} many{车库空间} other{车库空间}}',
    viewOnMap: '在地图上查看',
    mapPosition: '地点',
    pagingText: '发现{N, plural, few{关于} many{关于}} {N} {N, plural, one{房地产} few{房地产} many{房地产} other{房地产}}',
    noName: '房地产 #{id}'
  },
  postsPage: {
    meta: {
      title: '编辑帖子 | Majestic Wiki',
      description: '在《GTA5》RP 的 Majestic Wiki 上创建和编辑帖子。发布有关交通、财产、商业和游戏其他方面的最新信息。'
    },
    titlePlaceholder: '输入帖子标题',
    category: '类别',
    noCategory: '未分类',
    availability: '无障碍环境',
    allView: '人人可见',
    privateView: '限制进入',
    relevance: '相关性',
    relevanceData: '最新信息',
    outdatedData: '过时信息',
    categoryPosition: '类别 位置',
    categoryPositionDescription: '例如，"50 "或"-5"。',
    postDeleted: '删除帖子',
    author: '文章作者',
    editor: '文章编辑',
    contents: '文章内容',
    readingTime: '阅读时间',
    readingTimeLength: '{N} {N, plural, one{分钟} few{分钟} many{分钟} other{分钟}}',
    publishDate: '出版物来自 {date}',
    views: '{N} {N, plural, one{观看} few{观看} many{观看} other{观看}}',
    youLiked: '您喜欢',
    youAndPeopleLiked: '你也喜欢 {likes}',
    peopleLiked: '{N} {N, plural, one{likes} few{likes} many{likes} other{likes}}',
    categories: '类别',
    posts: '职位',
    foundMistake: '发现文章中有错误？',
    language: '语言'
  },
  panelPage: {
    users: {
      title: '用户管理',
      heading: '用户管理',
      admin: '管理人',
      user: '用户',
      customUser: '有权限的用户',
      search: '开始输入您的搜索查询....',
      comment: '评论',
      pagingText: '找到{N, plural, few{关于} many{关于}} {N} {N, plural, one{用户} few{用户} many{用户} other{用户}}'
    },
    posts: {
      title: '帖子管理 | Majestic Wiki',
      heading: '员额管理',
      views: '{N} {N, plural, one{观看} few{观看} many{观看} other{观看}}',
      likes: '{N} {N, plural, one{likes} few{likes} many{likes} other{likes}}'
    },
    categories: {
      title: '类别管理',
      heading: '类别管理'
    },
    mapLayers: {
      title: '管理地图图层 | Majestic Wiki',
      heading: '管理地图图层'
    },
    logs: {
      title: '日志审计 - Majestic Wiki',
      heading: '日志审计',
      search: '开始输入您的搜索查询....',
      biz: '商业',
      category: '类别',
      clothing: '服装',
      file: '文件',
      item: '物品',
      mapItem: '地图元素',
      mapLayer: '地图层',
      post: '职位',
      realty: '房地产',
      skin: '皮肤',
      user: '用户',
      vehicle: '运输',
      animation: '动画',
      actions: {
        unknown: '{username} 做了一件不为人知的事',
        bizCreate: '{username} 添加业务 {id}',
        bizUpdate: '{username} 改变业务 {id}',
        bizDelete: '{username} 删除业务 {id}',
        categoryCreate: '{username} 添加了类别 {id}',
        categoryUpdate: '{username} 更改类别 {id}',
        categoryDelete: '{username} 删除类别 {id}',
        clothingCreate: '{username} 添加衣服 {id}',
        clothingUpdate: '{username} 更衣 {id}',
        clothingDelete: '{username} 删除衣服 {id}',
        fileCreate: '{username} 添加了一个文件 {id}',
        fileUpdate: '{username} 修改了文件 {id}',
        fileDelete: '{username} 删除文件 {id}',
        itemCreate: '{username} 添加了项目 {id}',
        itemUpdate: '{username} {id}',
        itemDelete: '{username} 删除项目 {id}',
        mapItemCreate: '{username} 将地图 {id} 中的一个项目添加为 "{layer}"',
        mapItemUpdate: '{username} 将地图上的项目 {id} 更改为 "{layer}"',
        mapItemDelete: '{username} 将地图 {id} 上的一个项目删除为 "{layer}"',
        mapLayerCreate: '{username} 添加了地图图层 {id}',
        mapLayerUpdate: '{username} 更改地图图层 {id}',
        mapLayerDelete: '{username} 删除地图层 {id}',
        postCreate: '{username} 添加了一个帖子 {id}',
        postUpdate: '{username} 修改后的帖子 {id}',
        postDelete: '{username} 删除的帖子 {id}',
        realtyCreate: '{username} 添加了属性 {id}',
        realtyUpdate: '{username} 改变房地产 {id}',
        realtyDelete: '{username} 已删除 房地产 {id}',
        skinCreate: '{username} 添加了皮肤 {id}',
        skinUpdate: '{username} 更改皮肤 {id}',
        skinDelete: '{username} 删除皮肤 {id}',
        userCreate: '{username} 添加用户 {id}',
        userUpdate: '{username} 更改了用户 {id}',
        userDelete: '{username} 删除用户 {id}',
        vehicleCreate: '{username} 新增运输 {id}',
        vehicleUpdate: '{username} 更改了运输 {id}',
        vehicleDelete: '{username} 删除运输 {id}',
        animationCreate: '{username} 添加了动画 {id}',
        animationUpdate: '{username} 更改动画 {id}',
        animationDelete: '{username} 删除动画 {id}',
        changelogCreate: '{username} 添加了更新日志 {id}',
        changelogUpdate: '{username} 更改了更新日志 {id}',
        changelogDelete: '{username} 已删除更新日志 {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: '设置',
      users: '用户',
      categories: '类别',
      mapLayers: '地图图层',
      posts: '职位',
      logs: '日志'
    }
  },
  changelogPage: {
    titlePlaceholder: '输入更改名称',
    descriptionPlaceholder: '输入更改说明',
    availableAt: '出版日期',
    soon: '通过',
    ago: '返回',
    fix: '已更正',
    feat: '已添加',
    chore: '改装',
    style: '设计',
    refactor: '回收'
  },
  changelogsPage: {
    meta: {
      title: '更改历史 | Majestic Wiki',
      description: 'Majestic RP 服务器的完整更新和更改历史。关注新功能、游戏改进、错误修复和平衡。所有项目更新的最新信息。'
    },
    title: '变革历史',
    search: '寻找变化...'
  },
  homePage: {
    meta: {
      title: 'Majestic Wiki | GTA5 RP 百科全书',
      description: 'Majestic Wiki 是《GTA5》RP 玩家的完整百科全书。提供有关交通、房地产、商业、任务和游戏其他方面的信息。有关价格、功能和获取方式的最新信息。'
    },
    tools: {
      map: {
        title: '地图',
        description: '包含所有地点的服务器地图'
      },
      vehicles: {
        title: '运输',
        description: '服务器上的所有可用传输'
      },
      realty: {
        title: '房地产',
        description: '所有住宅和公寓的数据'
      },
      biz: {
        title: '企业',
        description: '高级赚钱选项'
      },
      clothesMale: {
        title: '男士服装',
        description: '男士时装秀'
      },
      clothesFemale: {
        title: '女装',
        description: '妇女时装表演'
      },
      skins: {
        title: '皮肤',
        description: '所有可用图案的目录'
      },
      items: {
        title: '主题',
        description: '服务器上的可用项目'
      },
      servers: {
        title: '服务器',
        description: '所有 Majestic RP 服务器列表'
      },
      animations: {
        title: '动画',
        description: '服务器上的独特动画'
      },
      changelogs: {
        title: '变革历史',
        description: '项目变更历史'
      }
    },
    search: {
      title: '一般类别',
      description: '在这里，您可以找到有关 Majestic 服务器及其系统的任何信息'
    },
    categories: {
      collapseList: '折叠列表',
      expandList: '扩展列表'
    },
    online: {
      players: '{count} 在线',
      offline: '关闭'
    }
  },
  errorPage: {
    title: '页面未找到',
    description: '您可能在页面地址中打错了，或者它根本不存在 :(',
    backToHome: '转至主页'
  },
  bizPage: {
    meta: {
      title: 'GTA5 RP Businesses | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 服务器上的全部商业目录。全天候商店、加油站、枪支商店、汽车经销商和其他业务。了解每项业务的盈利能力、要求和购买方式。',
      custom: {
        title: '{type} | GTA5 RP Businesses | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 服务器上有关 {type} 的详细信息。特点、盈利能力、要求以及如何购买业务。'
      }
    },
    title: '业务一览表',
    search: '商业搜索...',
    groceryStore: '商店24/7',
    gasStation: '加油',
    atm: '自动取款机',
    gunshop: '枪弹铺',
    clothingStore: '服装店',
    autoShop: '汽车经销商',
    tattoo: '纹身店',
    lsc: '改装店',
    barbershop: '理发店',
    carWash: '洗车店',
    viewOnMap: '在地图上查看',
    mapPosition: '地点',
    pagingText: '发现{N, plural, few{关于} many{关于}} {N} {N, plural, one{企业} few{企业} many{企业} other{企业}}',
    noName: '业务 #{id}'
  },
  clothesPage: {
    meta: {
      title: 'GTA5 RP 服装 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 服务器上的全部服装目录。男装、女装、配饰、背包、补丁、帽子和其他衣橱物品。了解如何获得服装以及每件服装的价格。',
      custom: {
        title: '{type} | GTA5 RP 服装 | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 服务器上的目录 {type} 。详细介绍了服装物品的获取方式、使用方法和成本。'
      }
    },
    titleMale: '男士服装图',
    titleFemale: '女装图',
    search: '服装搜索...',
    top: '上装',
    legs: '下装',
    shoes: '鞋',
    watch: '时钟',
    mask: '面具',
    decal: '领章',
    accessory: '配饰',
    head: '帽子',
    bag: '背包',
    glasses: '眼镜',
    ear: '耳朵',
    gloves: '手套',
    undershirt: 'T恤衫',
    noName: '服装套装 #{id}',
    drawable: '可绘制',
    texture: '纹理',
    male: '男',
    female: '女性',
    donate: 'Majestic Coin',
    battlePass: '战斗通行证',
    case: '案例',
    unique: '独特性',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{clothing} few{clothing} many{clothing} other{clothing}}'
  },
  itemsPage: {
    meta: {
      title: 'GTA5 RP 物品 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 服务器上的全部物品目录。古董、鱼、矿石、木材、箱子、材料和其他物品。了解每种物品的获取方式、使用方法和成本。',
      custom: {
        title: '{type} | GTA5 RP道具 | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 服务器上的目录 {type} 。详细介绍了物品的获取方式、使用方法和成本。'
      }
    },
    title: '主题表',
    search: '寻找物体...',
    antique: '古董',
    fish: '鱼',
    ore: '矿石',
    wood: '木材',
    box: '箱子',
    material: '材料',
    event: '活动',
    food: '商品',
    alcohol: '酒',
    illegal: '非法',
    medical: '医疗',
    equipment: '设备',
    ammunition: '装具',
    tool: '工具',
    unique: '独特性',
    vehicle: '汽车零部件',
    others: '其它',
    default: '标准',
    battlePass: '战斗通行证',
    case: '案例',
    unique: '独特性',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{item} few{item} many{items} other{items}}',
    noName: '主题 #{id}'
  },
  mapPage: {
    meta: {
      title: 'GTA5 RP 地图 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 服务器的交互式地图。探索地点，寻找宝藏、动物、电话和垃圾。详细的世界地图，可添加标签和区域。所有重要地点和兴趣点都在一个资源上！',
      custom: {
        title: '{type} | GTA5 RP 地图 | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 服务器上 {type} 的详细地图。探索地点、查找重要点并创建自己的标记。地图上物体位置的完整信息！'
      }
    },
    dataLoading: '数据加载...',
    defaultPoints: '标准标签',
    animalLayer: '动物地图',
    rubbishLayer: '垃圾桶地图',
    telephoneLayer: '电话地图',
    treasureLayer: '藏宝图',
    defaultLayer: '世界地图',
    allLayers: '地图图层',
    backToHome: '返回首页',
    zoomIn: '方法',
    zoomOut: '向后拉',
    point: '点',
    polygon: '区域',
    addPoint: '添加一个点',
    addPolygon: '添加区域',
    copyCoordinates: '复制坐标',
    savePointPosition: '保存位置',
    editPointPosition: '改变位置',
    layerNamePlaceholder: '区域名称',
    layerDescriptionPlaceholder: '区域描述'
  },
  serversPage: {
    meta: {
      title: '在线 GTA5 RP 服务器 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 服务器上在线玩家的当前统计数据。当前在线人数、峰值、队列和活动图表。加入最受欢迎的角色扮演项目！'
    },
    dataLoading: '数据加载...',
    title: '在线服务器',
    online: '{count} 在线',
    inQueue: '{count} 排队',
    peakOnline: '{count} 顶峰',
    currentOnline: '当前在线',
    peakOnline24h: '今天的高峰',
    peakOnlineAllTime: '历史巅峰',
    peakOnlineTooltip: '{percent}相对于前一天的百分比',
    techWorks: '技术工作',
    forBeginners: '初学者',
    experience: '{multiplier}经验 x',
    newServer: '新',
    seasonPassMultiplier: '{multiplier}跳过体验 x',
    combinedView: '一般情况',
    separateView: '分离式'
  },
  research: {
    placeholder: '在网站上搜索信息...',
    skin: '皮肤',
    vehicle: '运输',
    realty: '房地产',
    biz: '商业',
    clothing: '服装',
    item: '物品',
    post: '职位'
  },
  userModal: {
    login: '登录',
    signout: '出',
    register: '注册',
    registerEnd: '注册',
    restore: '修复',
    restoreEnd: '恢复',
    avatarDelete: '删除头像',
    headingUpdate: '修改用户 #{id}',
    postAdd: '发布职位',
    postUpdate: '编辑帖子',
    postDelete: '删除帖子',
    vehicleAdd: '增加运输',
    vehicleUpdate: '编辑运输',
    vehicleDelete: '拆除运输工具',
    realtyAdd: '添加属性',
    realtyUpdate: '房地产变化',
    realtyDelete: '拆除不动产',
    bizAdd: '添加业务',
    bizUpdate: '业务变革',
    bizDelete: '企业搬迁',
    mapItemAdd: '在地图上添加项目',
    mapItemUpdate: '更改地图上的元素',
    mapItemDelete: '删除地图上的项目',
    clothingAdd: '添加衣服',
    clothingUpdate: '换洗衣物',
    clothingDelete: '脱衣',
    skinAdd: '添加皮肤',
    skinUpdate: '更换皮肤',
    skinDelete: '去除表皮',
    itemAdd: '添加项目',
    itemUpdate: '改变对象',
    itemDelete: '移除物体',
    fileUpload: '上传图像',
    viewPrivate: '查看私人数据',
    doMagic: '使用魔法功能',
    animationAdd: '添加动画',
    animationUpdate: '更改动画',
    animationDelete: '删除动画',
    categoryAdd: '添加类别',
    categoryUpdate: '更改类别',
    categoryDelete: '删除类别',
    mapLayerAdd: '添加地图图层',
    mapLayerUpdate: '更改地图图层',
    mapLayerDelete: '删除地图图层',
    post: '职位',
    skin: '皮肤',
    biz: '商业',
    clothing: '服装',
    item: '物品',
    vehicle: '运输',
    animation: '动画',
    category: '类别',
    realty: '房地产',
    mapLayer: '地图层',
    changelog: '更新日志'
  },
  restoreModal: {
    title: '恢复账户',
    newPassword: '新密码',
    newPasswordRepeat: '重复新密码',
    passwordConfirm: '更改密码',
    passwordError: '密码不匹配'
  },
  bizModal: {
    titleUpdate: '改变商业 #{id}',
    titleAdd: '添加新业务'
  },
  categoryModal: {
    titleUpdate: '更改类别 #{id}',
    titleAdd: '添加新类别'
  },
  mapLayerModal: {
    titleUpdate: '#{id}更改地图图层',
    titleAdd: '添加新地图图层'
  },
  clothingModal: {
    titleUpdate: '更衣 #{id}',
    titleAdd: '添加新衣服',
    itemAdd: '添加颜色',
    itemGenerate: '生成所有颜色'
  },
  itemModal: {
    titleUpdate: '转移话题 #{id}',
    titleAdd: '添加新项目'
  },
  realtyModal: {
    titleUpdate: '不断变化的房地产 #{id}',
    titleAdd: '添加新属性'
  },
  skinModal: {
    titleUpdate: '更换皮肤 #{id}',
    titleAdd: '添加新皮肤'
  },
  vehicleModal: {
    titleUpdate: '改变运输 #{id}',
    titleAdd: '添加新的运输工具',
    titleDefault: '有关 {name}的信息',
    itemAdd: '加装防浪板'
  },
  mapItemModal: {
    titleUpdate: '更改地图元素',
    titleAdd: '添加新的地图元素',
    titleDelete: '删除地图项目',
    descriptionDelete: '您确定要从地图上删除该项目吗？此操作不能取消。'
  },
  animationModal: {
    titleUpdate: '更改动画 #{id}',
    titleAdd: '添加新动画'
  }
};