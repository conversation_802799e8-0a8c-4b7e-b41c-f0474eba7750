export default {
  global: {
    all: 'すべて',
    back: 'バック',
    forward: '行こう',
    paging: 'ページ {maxPage}の {page}',
    close: '閉じる',
    clear: 'クリア',
    save: 'セーブ',
    delete: '削除',
    deleteYes: '本当に撤去したのですか？',
    cancel: '拒否',
    publish: '出版',
    edit: '編集',
    share: 'シェア',
    add: '加える。',
    active: 'アクティブ',
    deleted: '削除',
    blocked: 'ブロック',
    learnMore: 'もっと読む',
    panel: 'パネル',
    theme: 'テーマ',
    language: '言語',
    user: 'ユーザー',
    confirm: '認める',
    restore: '修復',
    yes: 'はい',
    no: 'いいえ',
    new: '新しい',
    preview: 'プレビュー',
    sortPriceDesc: '価格の高い順',
    sortPriceAsc: '価格の高い順',
    sortPriceMinMax: '価格帯',
    sortDateDesc: '日付の古い順',
    sortDateAsc: '日付の昇順',
    noSellPrice: '購入不可',
    noBuyPrice: '非売品',
    noData: {
      title: '何も見つからない',
      description: '検索クエリを変更または簡略化してみる'
    },
    battlePass: {
      summer: '夏のバトルパス',
      winter: 'ウィンターバトル・パス'
    },
    case: {
      default: '標準ケース',
      vehicles: 'カーケース',
      halloween: 'ハロウィーン・ケース',
      spring: '春のブリーフケース',
      autumn: '秋のブリーフケース',
      summer: '夏の場合',
      summerExtra: '夏の特別なケース',
      summerSkins: '夏用スキンケース',
      summerVehicles: '夏の輸送ケース',
      winter: 'ウィンターケース',
      winterExtra: '冬の特別なケース',
      winterSkins: 'ウィンターケース（スキン付き',
      winterVehicles: '冬季輸送ケース',
      toys: 'おもちゃケース'
    },
    subscription: { premium: 'プレミアム購読' },
    copy: 'コピー'
  },
  footer: { disclaimer: '本ウェブサイトに掲載されている情報はすべて情報提供のみを目的としたものであり、公募を構成するものではありません。' },
  footer: { disclaimer: '本ウェブサイトに掲載されている情報はすべて情報提供のみを目的としたものであり、公募を構成するものではありません。' },
  editor: {
    placeholder: '素晴らしい記事を書こう！',
    text: 'パラグラフ',
    heading: 'キャプション',
    warning: '注',
    quote: '引用',
    code: 'コード',
    delimiter: 'セパレーター',
    link: '参考',
    linkPlaceholder: 'リンクを入力',
    marker: 'マーカー',
    bold: '太字',
    italic: 'イタリック体',
    inlineCode: 'モノスペース',
    underline: '強調',
    copytext: 'コピー可',
    log: 'ローグ',
    map: {
      name: '地図',
      params: 'カード・パラメーターの入力',
      caption: '地図の説明を入力する'
    },
    table: {
      name: 'テーブル',
      addColumnLeft: '左側に列を追加する',
      addColumnRight: '右側に列を追加',
      deleteColumn: '列の削除',
      addRowAbove: '上部に1行追加',
      addRowBelow: '一番下に行を追加',
      deleteRow: '文字列の削除',
      withHeadings: '見出し',
      withoutHeadings: '見出しなし'
    },
    image: {
      name: '画像',
      withBorder: 'フレーム付き',
      stretchImage: '写真を伸ばす',
      withBackground: '背景',
      selectImage: '画像を選択する',
      uploadError: '画像の読み込みに失敗しました。',
      caption: '画像説明',
      withCaption: '説明付き'
    },
    carousel: {
      name: 'イメージグリッド',
      caption: '画像説明',
      addImage: '画像を選択する'
    },
    list: {
      name: 'リスト',
      ordered: '番号',
      unordered: '番号なし',
      checklist: 'チェックリスト'
    },
    header: {
      heading1: '見出しH1',
      heading2: '見出しH2',
      heading3: '見出しH3',
      heading4: 'ヘッダーH4'
    },
    embed: { caption: 'リンク説明' },
    convertTo: 'に変換する。',
    moveUp: '繰り上げ',
    moveDown: '下に移動',
    anchor: 'アンカー',
    clickToTune: 'を押して設定する',
    dragToMove: 'またはドラッグ＆ドロップ',
    filter: '検索',
    nothingFound: '何も見つからない'
  },
  alerts: {
    loginSuccess: 'ログイン成功',
    loginError: '認証時のエラー',
    registerSuccess: 'ウェブサイトでの登録に成功',
    registerError: '登録エラー',
    restoreSuccess: 'リカバリーの手順がメールボックスに送信されました。',
    restoreError: 'アカウントへのアクセス復元時のエラー',
    signoutSuccess: 'ログアウトに成功',
    postLoadError: '投稿アップロード時のエラー',
    postShareSuccess: 'クリップボードにコピーされた投稿へのリンク',
    copyCommandSuccess: 'コマンドがクリップボードにコピーされる',
    restoreAccountSuccess: 'アカウント復旧に成功',
    restoreAccountError: 'アカウントへのアクセス復元時のエラー',
    verificateAccountSuccess: 'メールボックスの確認に成功',
    verificateAccountError: 'メールボックス確認エラー',
    dataUpdateSuccess: '情報の更新に成功',
    dataUpdateError: '情報更新時のエラー',
    dataDeleteSuccess: '情報の削除に成功',
    dataDeleteError: '情報削除時のエラー',
    dataAddSuccess: '情報追加に成功',
    dataAddError: '情報追加時のエラー',
    mapMoveInfo: 'ポイントを別の位置に移動し、保存する',
    mapCoordinatesCopySuccess: '座標のコピーに成功',
    copyTextSuccess: 'テキストのコピーに成功'
  },
  mail: {
    verificate: {
      subject: 'メールアドレスの確認',
      title: '確認',
      description: 'アカウントの権利を確認し、確認するには、以下のボタンをクリックする必要があります。リンクは24時間有効です。'
    },
    restore: {
      subject: 'アカウントへのアクセスを復元する',
      title: '修復',
      description: 'アカウントへのアクセスを復元するには、以下のボタンをクリックする必要があります。リンクは24時間有効です。'
    },
    restoreSuccess: {
      subject: 'アカウントへのアクセスの復元に成功',
      title: '回復に成功',
      description: 'パスワードの変更が完了しました。まだの方は、すぐにパスワードを変更してください！'
    },
    verificateSuccess: {
      subject: 'アカウントへのアクセスの復元に成功',
      title: '確認成功',
      description: 'メールボックスの確認が完了しました！'
    },
    caption: 'このメールは通知であり、自動的に生成されます。'
  },
  fields: {
    price: 'コスト',
    buyPrice: '購入費用',
    sellPrice: '売上原価',
    mcPrice: '三菱商事での購入費用',
    money: '通貨',
    coin: 'マジェスティック・コイン',
    gift: '贈り物',
    default: 'スタンダード',
    unique: 'ユニーク',
    event: 'イベント',
    battlePass: 'バトルパス',
    lootbox: 'ケース',
    capacityKg: '定員',
    email: 'メール',
    password: 'パスワード',
    username: 'ニックネーム',
    weightKg: 'kg',
    speed: 'スピード',
    maxSpeed: '最高速度',
    maxSpeedFT: '最高速度（FT）',
    accelerationTo100: '100km/h加速',
    speedSystem: 'km/h',
    timeSystem: '秒',
    uploadDescription: '画像をタッチまたはドラッグして領域に移動する',
    placeholder: 'フィールドに値を入力する',
    imagePlaceholder: '写真へのリンクを入力してください。',
    selectPlaceholder: 'リストから値を選択',
    videoPlaceholder: 'ビデオへのリンクを入力してください。',
    soundPlaceholder: 'オーディオへのリンクを入力してください。',
    id: '身分証明書',
    serial: 'シリアル番号',
    kind: 'タイプ',
    category: 'カテゴリー',
    name: 'タイトル',
    view: '表示',
    layer: 'レイヤー',
    icon: 'アイコン',
    circle: 'ポイント',
    polygon: 'ポリゴン',
    emoji: '絵文字',
    description: '説明',
    gender: '性別',
    value: '値の種類',
    valueName: '追加価格',
    visibility: 'アクセシビリティ',
    visibilityPrivate: 'アクセス制限',
    visibilityPublic: 'すべての人に可視性を',
    activityHidden: '一般公開されない',
    activityVisible: 'すべての人が利用可能',
    garageSlots: 'ガレージスロット',
    maxTenants: '居住者数',
    gasoline: '燃料',
    brand: 'ブランド',
    model: 'モデル',
    trunk: 'ブーツ',
    tradable: '譲渡性',
    buyable: '購入オプション',
    upgradesPriceFT: '利用可能な改善（FT）',
    upgradesEngine: 'エンジン',
    upgradesBreaks: 'ブレーキ',
    upgradesSuspension: 'サスペンション',
    upgradesTurbo: 'ターボ',
    upgradesTransmission: 'ボックス',
    paintingsMainPrice: '基本的な塗装',
    paintingsSubPrice: '追加塗装',
    paintingsOtherPrice: 'その他の絵画',
    paintingsBrightMetallic: 'ブライト・メタリック',
    paintingsMetallic: 'メタリック',
    paintingsRichMetallic: 'リッチメタリック',
    paintingsDarkMetallic: 'ダークメタリック',
    paintingsMatte: 'マット',
    paintingsMatteMetallic: 'マットメタル',
    paintingsSatin: 'サテン',
    paintingsMetal: 'メタル',
    paintingsShadowChrome: 'シャドークローム',
    paintingsPureChrome: 'ピュア・クローム',
    paintingsPearl: '真珠光沢のある色合い',
    paintingsWheels: 'ディスクカラー',
    paintingsDetails: '部品の色',
    paintingsAdditionalDetails: '部品の追加カラー',
    paintingsLights: 'ヘッドランプライト',
    paintingsSmoke: 'タイヤスモークの色',
    paintingsNeon: 'ネオンの色',
    total: '合計',
    language: '言語',
    radius: '半径',
    color: 'カラー',
    colorPlaceholder: '色を選ぶ',
    opacity: '透明性',
    alias: 'ペンネーム',
    aliases: '別名',
    looped: 'ループ',
    battlePassId: 'バトルパスID',
    gasolineCapacityLiter: 'л',
    gasolineCapacity: 'タンク容量',
    source: 'ソース',
    sources: '情報源',
    priceDescription: '費用明細',
    layerName: 'レイヤー名',
    layerNamePlaceholder: 'レイヤーの名前を入力します。',
    layerDescription: 'レイヤーの説明',
    layerDescriptionPlaceholder: 'レイヤーの説明を入力します。',
    layerColor: 'レイヤーの色',
    layerColorPlaceholder: '色を選ぶ',
    layerOpacity: 'レイヤーの透明度',
    zIndexOffset: 'Zインデックス・レベル',
    zIndexOffsetPlaceholder: 'Zインデックスレベルの値を入力する。',
    comment: '解説'
  },
  vehiclesPage: {
    meta: {
      title: 'GTA5 RP 交通手段と車｜マジェスティックWiki',
      description: 'GTA5 RPのサーバーマジェスティックRP上の車両の完全なカタログ。車、バイク、ヘリコプター、飛行機、ボートの詳細な特性。価格、速度、操縦性、トランク容量やその他のパラメータを調べる。マジェスティックモータース、車、トラック、ヘリコプター、ボート、オートバイ、バイク - GTA5 RPのためのすべて！',
      custom: {
        title: '{type} | GTA5 RPトランスポート｜マジェスティック・ウィキ',
        description: 'GTA5 RPのサーバーMajestic RPの詳細カタログ {type} 。特徴、価格、速度、操縦性、その他のパラメータ。GTA5 RPの輸送に関する完全な情報！'
      }
    },
    default: 'スタンダード',
    donate: 'マジェスティック・コイン',
    battlePass: 'バトルパス',
    case: 'ケース',
    unique: 'ユニーク',
    title: '輸送テーブル',
    search: 'トランスポート・ファインダー',
    irl: 'マジェスティック・モーターズ',
    car: '自動車',
    freight: 'ロリー',
    helicopter: 'ヘリコプター',
    plane: 'Самолеты',
    boat: 'ボート',
    motorbike: 'バイク',
    bike: '自転車',
    trailer: 'トレーラー',
    military: 'ミリタリー',
    special: 'スペシャリティ',
    priceDisclaimer: 'サーバーのLSCの手数料により、最終的なコストが異なる場合があります。',
    basicInformation: '基本情報',
    additionalInformation: '追加情報',
    compare: '比較する',
    compareLimit: '最大5つのTCを比較することができる',
    pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{транспорт} few{транспорта} many{транспорта} other{транспорта}}'
  },
  vehiclePage: {
    meta: {
      title: 'GTA5 RPトランスポート｜マジェスティックWiki',
      description: 'GTA5 RPのサーバーMajestic RPの輸送に関する詳細情報。特徴、価格、改良、塗装。各車両の最高速度、加速度、トランク容量、改良のコストやその他のパラメータについて学びます。',
      custom: {
        title: '{name} | GTA5 RPトランスポート｜マジェスティック・ウィキ',
        description: 'GTA5 RPのサーバーMajestic RPの {name} についての詳細情報。特徴、価格、改良点、ペイントジョブ。最高速度、加速度、ブート容量、改良費用、その他のパラメータについて調べる。'
      }
    },
    backToTable: 'リストに戻る',
    labelsUniqueId: 'ユニークID',
    labelsPrice: 'コスト',
    labelsBuyPrice: '購入費用',
    labelsSellPrice: '売上原価',
    labelsMaxSpeed: '最高速度',
    labelsMaxSpeedFT: '最高速度（FT）',
    labelsAccelerationTo100: '100km/h加速',
    labelsTradable: '譲渡性',
    labelsTrunk: 'ブート容量',
    labelsGasolineCapacity: 'タンク容量',
    labelsGasolineType: '燃料タイプ',
    upgradesTitle: '交通機関の改善',
    upgradesDescription: '最大レベルへのアップグレード費用',
    upgradesEngine: 'エンジン',
    upgradesEngineDescription: '輸送の真の可能性を引き出す',
    upgradesBreaks: 'ブレーキ',
    upgradesBreaksDescription: 'どんな道でもスピードを自在にコントロール',
    upgradesTurbo: 'ターボ',
    upgradesTurboDescription: 'どんな時でも爆発的な加速',
    upgradesTransmission: 'ボックス',
    upgradesTransmissionDescription: '全ギアを瞬時に変速',
    upgradesSuspension: 'サスペンション',
    upgradesSuspensionDescription: '適切な高さで完璧な快適さ',
    upgradesTotal: '改善費用',
    upgradesDisclaimer: 'サーバーのLSCの手数料により、最終的なコストが異なる場合があります。',
    colorsTitle: '車両塗装',
    colorsDescription: '塗装の種類と費用',
    paintingsMainPrice: '基本的な塗装',
    paintingsSubPrice: '追加塗装',
    paintingsOtherPrice: 'その他の絵画',
    paintingsBrightMetallic: 'ブライト・メタリック',
    paintingsMetallic: 'メタリック',
    paintingsRichMetallic: 'リッチメタリック',
    paintingsDarkMetallic: 'ダークメタリック',
    paintingsMatte: 'マット',
    paintingsMatteMetallic: 'マットメタル',
    paintingsSatin: 'サテン',
    paintingsMetal: 'メタル',
    paintingsShadowChrome: 'シャドークローム',
    paintingsPureChrome: 'ピュア・クローム',
    paintingsPearl: '真珠光沢のある色合い',
    paintingsWheels: 'ディスクカラー',
    paintingsDetails: '部品の色',
    paintingsAdditionalDetails: '部品の追加カラー',
    paintingsLights: 'ヘッドランプライト',
    paintingsSmoke: 'タイヤスモークの色',
    paintingsNeon: 'ネオンの色',
    paintingsTotal: '最終塗装費用',
    shareLink: '交通機関へのリンクを共有する',
    shareLinkCopied: 'トランスポートリンクが正常にコピーされました',
    addToCompare: '比較に加える',
    addedToCompare: '輸送が比較に追加されました',
    goToCompare: '比較する',
    removeFromCompare: '比較対象から外す',
    removedFromCompare: 'トランスポートは比較対象から除外された'
  },
  vehiclesComparePage: {
    meta: {
      title: 'GTA5 RP輸送比較｜マジェスティック・ウィキ',
      description: 'GTA5 RPのマジェスティックRPサーバーで、さまざまな車の機能と価格を比較してください。あなたのニーズに最も適した車両を見つけましょう！スピード、価格、容量、その他のパラメーターを比較してください。'
    },
    name: 'タイトル',
    type: 'タイプ',
    brand: 'ブランド',
    kits: 'パッケージ',
    model: 'モデル',
    trunkCapacity: 'ブート容量',
    maxSpeed: '最高速度',
    maxSpeedFT: '最高速度（FT）',
    price: 'コスト',
    donatePrice: 'マジェスティック・コインのコスト',
    sellPrice: '売上原価',
    sellPriceBeforeTax: '売上原価（税引前）',
    sellPriceWithDiscount: '売却原価（割引）',
    accelerationTo100: '100km/hまでの加速時間',
    gasolineCapacity: 'タンク容量',
    gasolineType: '燃料タイプ',
    isTradable: '譲渡性',
    isBuyable: '購入オプション',
    createdAt: '出演日',
    gasolineCapacityUnit: 'л',
    maxSpeedUnit: 'km/h',
    maxSpeedFTUnit: 'km/h',
    priceUnit: '₽',
    accelerationTo100Unit: '秒',
    trunkCapacityUnit: 'kg',
    showOnlyDifferent: '異なるものだけを表示する',
    showAll: 'すべて表示',
    share: '比較を共有する',
    linkCopied: '比較リンクは正常にコピーされました',
    openVehicle: 'トランスポート・ページを開く',
    deleteVehicle: '比較対象から車両を外す',
    noDifferences: '区別はない'
  },
  skinsPage: {
    meta: {
      title: 'GTA5 RP武器スキン｜マジェスティックWiki',
      description: 'GTA5 RPのサーバーMajestic RPの武器スキンのフルカタログ。武器の種類に応じたスキンの入手方法、コスト、特徴などを紹介しています。',
      custom: {
        title: '{type} のスキン｜GTA5 RP｜マジェスティック Wiki',
        description: 'GTA5 RPのサーバーMajestic RPの {type} のスキンのカタログ。スキンの入手方法、コスト、特徴などの詳細情報。'
      }
    },
    title: 'スキンテーブル',
    search: 'スキンサーチ...',
    apPistol: '徹甲弾ピストル',
    assaultRifle: 'アサルトライフル',
    assaultRifleMk2: 'Mk2アサルトライフル',
    bullpupRifle: 'ブルパップ・ライフル',
    carbineRifle: 'カービン・ライフル',
    carbineRifleMk2: 'Mk2カービン・ライフル',
    combatPDW: 'コンバットPDW',
    gusenberg: 'トンプソン・サブマシンガン。',
    heavyPistol: '重砲',
    heavyShotgun: 'ヘビーショットガン',
    heavySniper: 'ヘビー・スナイパー・ライフル。',
    heavySniperMk2: 'Mk2ヘビー・スナイパー・ライフル',
    lightvest: 'ボディアーマー',
    machinePistol: '小型PP',
    marksmanPistol: 'マークスマンガン',
    microSMG: 'マイクロSMG',
    militaryRifle: '軍用ライフル',
    revolver: 'リボルバー',
    smgMk2: 'SMG Mk2',
    specialCarbine: '専用カラビナ',
    vintagePistol: 'ビンテージ銃',
    combatMgMk2: 'Mk2サブマシンガン',
    heavyRifle: 'ヘビー・ライフル',
    tacticalRifle: 'タクティカル・ライフル',
    tecPistol: 'タクティカルSMG',
    noName: '皮膚 #{id}',
    battlePass: 'バトルパス',
    case: 'ケース',
    unique: 'ユニーク',
    pagingText: '発見{N, plural, few{約} many{約}} {N} {N, plural, one{スキン} few{スキン} many{スキン} other{スキン}}スキン'
  },
  animationsPage: {
    meta: {
      title: 'GTA5 RP アニメーション｜マジェスティック・ウィキ',
      description: 'GTA5 RPのサーバーMajestic RPのアニメーションのフルカタログ。すべての種類のアニメーション：アクション、ポーズ、ダンス、専用アニメーション。各アニメーションの入手方法とコストをご確認ください。',
      custom: {
        title: '{type} | GTA5 RP アニメーション｜マジェスティック・ウィキ',
        description: 'カタログ {type} GTA5 RPのサーバーMajestic RPのアニメーション。アニメーションの入手方法、コスト、使用方法についての詳細情報。'
      }
    },
    title: 'アニメーションテーブル',
    search: 'アニメーション検索...',
    default: 'スタンダード',
    donate: 'マジェスティック・コイン',
    battlePass: 'バトルパス',
    case: 'ケース',
    unique: 'ユニーク',
    action: '行動',
    pose: '姿勢',
    positive: 'ポジティブ',
    negative: 'ネガ',
    dances: 'ダンス',
    etc: 'その他',
    exclusive: '独占',
    noName: 'アニメーション #{id}',
    pagingText: '見つかった{N, plural, one{件} other{件}} {N} {N, plural, one{アニメーション} other{アニメーション}}'
  },
  realtyPage: {
    meta: {
      title: 'GTA5 RP不動産｜マジェスティックWiki',
      description: 'GTA5 RPのサーバーMajestic RP上の不動産の完全なカタログ。住宅、フラット、オフィス、倉庫の詳細な特徴、価格、場所。テナントの数、ガレージスペースやその他のパラメータを検索します。',
      custom: {
        title: '{type} | GTA5 RP 不動産｜マジェスティック・ウィキ',
        description: 'GTA5 RPのサーバーMajestic RPのカタログ {type} 。物件の特徴、価格、場所、特徴などの詳細情報。'
      }
    },
    title: 'プロパティ・テーブル',
    search: '物件検索...',
    majesticMotors: 'マジェスティック・モーターズ',
    house: '自宅で',
    apartment: 'フラット',
    office: '事業所',
    warehouse: '倉庫',
    tenantsFor: '{N} {N, plural, one{жилец} few{жильца} many{жильцов} other{жильцов}}',
    garageSlotsFor: '{N} {N, plural, one{гаражное место} few{гаражных места} many{гаражных мест} other{гаражных мест}}',
    viewOnMap: '地図で見る',
    mapPosition: '所在地',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{real estate} few{real estate} many{real estate} other{real estate}}',
    noName: '不動産 #{id}'
  },
  postsPage: {
    meta: {
      title: '記事の編集｜Majestic Wiki',
      description: 'GTA5 RPのMajestic Wikiで記事を作成・編集。交通、不動産、ビジネスなど、ゲームの最新情報を公開。'
    },
    titlePlaceholder: '記事のタイトルを入力',
    category: 'カテゴリー',
    noCategory: '未分類',
    availability: 'アクセシビリティ',
    allView: 'すべての人に可視性を',
    privateView: 'アクセス制限',
    relevance: '関連性',
    relevanceData: '最新情報',
    outdatedData: '古い情報',
    categoryPosition: 'カテゴリー・ポジション',
    categoryPositionDescription: '例えば "50 "とか"-5 "とか。',
    postDeleted: 'ポスト削除',
    author: '記事の著者',
    editor: '記事編集者',
    contents: '記事内容',
    readingTime: '読書時間',
    readingTimeLength: '{N} {N, plural, one{分} few{分} many{分} other{分}}分',
    publishDate: '{date}からの出版物',
    views: '{N} {N, plural, one{просмотр} few{просмотра} many{просмотров} other{просмотров}}',
    youLiked: 'あなたは',
    youAndPeopleLiked: 'あなたも {likes}。',
    peopleLiked: '{N} {N, plural, one{лайкнул} few{лайкнули} many{лайкнули} other{лайкнули}}',
    categories: 'カテゴリー',
    posts: '投稿',
    foundMistake: '記事に誤りがありましたか？',
    language: '言語'
  },
  panelPage: {
    users: {
      title: 'ユーザー管理｜Majestic Wiki',
      heading: 'ユーザー管理',
      admin: '管理者',
      user: 'ユーザー',
      customUser: '権利を持つユーザー',
      search: '検索クエリを入力し始める......。',
      comment: '解説',
      pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{user} few{user} many{users} other{users}}.'
    },
    posts: {
      title: '投稿管理｜マジェスティック・ウィキ',
      heading: 'ポスト・マネジメント',
      views: '{N} {N, plural, one{просмотр} few{просмотра} many{просмотров} other{просмотров}}',
      likes: '{N} {N, plural, one{лайк} few{лайка} many{лайков} other{лайков}}'
    },
    categories: {
      title: 'カテゴリー管理｜Majestic Wiki',
      heading: 'カテゴリー管理'
    },
    mapLayers: {
      title: 'マップレイヤーの管理｜Majestic Wiki',
      heading: '地図レイヤーの管理'
    },
    logs: {
      title: 'ログ監査 - Majestic Wiki',
      heading: 'ログの監査',
      search: '検索クエリを入力し始める......。',
      biz: 'ビジネス',
      category: 'カテゴリー',
      clothing: '衣類',
      file: 'ファイル',
      item: 'テーマ',
      mapItem: '地図要素',
      mapLayer: '地図レイヤー',
      post: 'ポスト',
      realty: '不動産',
      skin: '皮膚',
      user: 'ユーザー',
      vehicle: '輸送',
      animation: 'アニメーション',
      actions: {
        unknown: '{username} 未知の行為',
        bizCreate: '{username} {id}',
        bizUpdate: '{username} changed business {id}',
        bizDelete: '{username} {id}',
        categoryCreate: '{username} {id}カテゴリーを追加',
        categoryUpdate: '{username} カテゴリー変更 {id}',
        categoryDelete: '{username} 削除されたカテゴリー {id}',
        clothingCreate: '{username} 服を追加 {id}',
        clothingUpdate: '{username} 着替え {id}',
        clothingDelete: '{username} 服を削除 {id}',
        fileCreate: '{username} ファイル追加 {id}',
        fileUpdate: '{username} {id}を修正した。',
        fileDelete: '{username} ファイルを削除した {id}',
        itemCreate: '{username} アイテム追加 {id}',
        itemUpdate: '{username} change the subject {id}',
        itemDelete: '{username} {id}を削除した。',
        mapItemCreate: '{username} マップにアイテムを追加 {id} "{layer}"',
        mapItemUpdate: '{username} 地図上の項目 {id} を "{layer}" に変更。',
        mapItemDelete: '{username} 地図上の項目を削除 {id} "{layer}"',
        mapLayerCreate: '{username} 地図レイヤーの追加 {id}',
        mapLayerUpdate: '{username} 地図レイヤーを変更 {id}',
        mapLayerDelete: '{username} 地図レイヤーを削除 {id}',
        postCreate: '{username} {id}',
        postUpdate: '{username} 修正後 {id}',
        postDelete: '{username} 削除された投稿 {id}',
        realtyCreate: '{username} プロパティ {id}を追加した。',
        realtyUpdate: '{username} 不動産を変えた {id}',
        realtyDelete: '{username} 削除された不動産 {id}',
        skinCreate: '{username} スキン追加 {id}',
        skinUpdate: '{username} {id}',
        skinDelete: '{username} スキン削除 {id}',
        userCreate: '{username} ユーザー追加 {id}',
        userUpdate: '{username} ユーザー {id}を変更',
        userDelete: '{username} 削除ユーザー {id}',
        vehicleCreate: '{username} 輸送手段を追加 {id}',
        vehicleUpdate: '{username} トランスポートを変更 {id}',
        vehicleDelete: '{username} トランスポートを削除 {id}',
        animationCreate: '{username} アニメーション追加 {id}',
        animationUpdate: '{username} アニメーションを変更 {id}',
        animationDelete: '{username} アニメーションを削除 {id}',
        changelogCreate: '{username} 変更履歴を追加 {id}',
        changelogUpdate: '{username} 変更履歴 {id}',
        changelogDelete: '{username} 削除された変更履歴 {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: '設定',
      users: 'ユーザー',
      categories: 'カテゴリー',
      mapLayers: '地図レイヤー',
      posts: '投稿',
      logs: '過去ログ'
    }
  },
  changelogPage: {
    titlePlaceholder: '変更名を入力',
    descriptionPlaceholder: '変更の説明を入力',
    availableAt: '発行日',
    soon: 'を通して',
    ago: 'バックワード',
    fix: '訂正',
    feat: '追加',
    chore: '変形',
    style: 'デザイン',
    refactor: 'リサイクル'
  },
  changelogsPage: {
    meta: {
      title: '変更の歴史｜Majestic Wiki',
      description: 'マジェスティックRPサーバーの更新と変更の完全な履歴。新機能、ゲームプレイの改善、バグ修正、バランス調整などにご注目ください。すべてのプロジェクトのアップデートに関する最新情報。'
    },
    title: '変更の歴史',
    search: '変化を見つける...'
  },
  homePage: {
    meta: {
      title: 'マジェスティック・ウィキ｜GTA5 RP百科事典',
      description: 'Majestic WikiはGTA5 RPプレイヤーのための完全百科事典です。交通機関、不動産、ビジネス、クエスト、その他ゲームに関する情報。価格、機能、入手方法などの最新情報。'
    },
    tools: {
      map: {
        title: '地図',
        description: '全拠点のサーバーマップ'
      },
      vehicles: {
        title: '輸送',
        description: 'サーバー上で利用可能なすべてのトランスポート'
      },
      realty: {
        title: '不動産',
        description: '全戸および全フラットのデータ'
      },
      biz: {
        title: '事業内容',
        description: 'アドバンスド・アーニング・オプション'
      },
      clothesMale: {
        title: '紳士服',
        description: '男性のためのファッションショー'
      },
      clothesFemale: {
        title: '婦人服',
        description: '女性のためのファッションショー'
      },
      skins: {
        title: 'スキンズ',
        description: '全パターンのカタログ'
      },
      items: {
        title: '対象',
        description: 'サーバーで利用可能なアイテム'
      },
      servers: {
        title: 'サーバー',
        description: 'マジェスティックRPの全サーバー一覧'
      },
      animations: {
        title: 'アニメーション',
        description: 'サーバー上のユニークなアニメーション'
      },
      changelogs: {
        title: '変更の歴史',
        description: 'プロジェクトの変更履歴'
      }
    },
    search: {
      title: '一般カテゴリー',
      description: 'ここでは、マジェスティックサーバーとそのシステムに関するあらゆる情報をご覧いただけます。'
    },
    categories: {
      collapseList: '折りたたみリスト',
      expandList: 'リストを拡大する'
    },
    online: {
      players: '{count} オンライン',
      offline: 'オフ'
    }
  },
  errorPage: {
    title: 'ページが見つかりません',
    description: 'ページのアドレスにタイプミスがあるか、存在しないだけかもしれません :(',
    backToHome: 'メインページへ'
  },
  bizPage: {
    meta: {
      title: 'GTA5 RPビジネス｜マジェスティック・ウィキ',
      description: 'GTA5 RPのサーバーMajestic RPのビジネスの完全なカタログ。年中無休のショップ、ガソリンスタンド、ガンショップ、カーディーラー、その他のビジネス。各ビジネスの収益性、条件、購入方法についてご紹介します。',
      custom: {
        title: '{type} | GTA5 RPビジネス｜マジェスティック・ウィキ',
        description: 'GTA5 RPのサーバーMajestic RPの {type} についての詳細情報。特徴、収益性、条件、ビジネスの購入方法など。'
      }
    },
    title: '事業一覧',
    search: 'ビジネスサーチ...',
    groceryStore: '年中無休ショップ',
    gasStation: '燃料補給',
    atm: 'ATM',
    gunshop: 'ガンショップ',
    clothingStore: '衣料品店',
    autoShop: '自動車ディーラー',
    tattoo: 'タトゥーパーラー',
    lsc: 'チューニングサロン',
    barbershop: '理髪店',
    carWash: '洗車',
    viewOnMap: '地図で見る',
    mapPosition: '所在地',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{businesses} few{businesses} many{businesses} other{businesses}}.',
    noName: 'ビジネス #{id}'
  },
  clothesPage: {
    meta: {
      title: 'GTA5 RPウェア｜マジェスティック・ウィキ',
      description: 'GTA5 RPのサーバーMajestic RPの衣類の完全なカタログ。メンズとレディースの服、アクセサリー、バックパック、ワッペン、帽子、その他のワードローブアイテム。服の各アイテムの入手方法と価格を調べる。',
      custom: {
        title: '{type} | GTA5 RPウェア｜マジェスティック・ウィキ',
        description: 'GTA5 RPのサーバーMajestic RPのカタログ {type} 。服飾アイテムの入手方法、使い方、値段などの詳細情報。'
      }
    },
    titleMale: 'メンズ・ウェア・チャート',
    titleFemale: 'レディース・ウェア・チャート',
    search: 'アパレル検索...',
    top: 'トップ',
    legs: 'ボトム',
    shoes: 'フットウェア',
    watch: '時計',
    mask: 'マスク',
    decal: 'パッチ',
    accessory: 'アクセサリー',
    head: '帽子',
    bag: 'バックパック',
    glasses: 'メガネ',
    ear: '耳',
    gloves: 'グローブ',
    undershirt: 'Tシャツ',
    noName: '洋服セット #{id}',
    drawable: '引き出し可能',
    texture: 'テクスチャー',
    male: '男性',
    female: '女性',
    donate: 'マジェスティック・コイン',
    battlePass: 'バトルパス',
    case: 'ケース',
    unique: 'ユニーク',
    pagingText: '発見{N, plural, few{について} many{について}} {N} {N, plural, one{衣類} few{衣類} many{衣類} other{衣類}}について'
  },
  itemsPage: {
    meta: {
      title: 'GTA5 RPアイテム｜マジェスティックWiki',
      description: 'GTA5 RPのサーバーMajestic RPのアイテムの完全カタログ。骨董品、魚、鉱石、木材、木箱、材料、その他のアイテム。各アイテムの入手方法、使用方法、コストについて。',
      custom: {
        title: '{type} | GTA5｜RPアイテム｜マジェスティックWiki',
        description: 'GTA5 RPのサーバーMajestic RPのカタログ {type} 。アイテムの入手方法、使用方法、価格などの詳細情報。'
      }
    },
    title: '被験者表',
    search: 'モノを探す...',
    antique: 'アンティーク',
    fish: '魚',
    ore: '鉱石',
    wood: '木材',
    box: 'ボックス',
    material: '材料',
    event: 'イベント',
    food: '製品紹介',
    alcohol: 'アルコール',
    illegal: '違法',
    medical: '医学',
    equipment: '設備',
    ammunition: '弾薬',
    tool: '楽器',
    unique: 'ユニーク',
    vehicle: '自動車部品',
    others: 'その他',
    default: 'スタンダード',
    battlePass: 'バトルパス',
    case: 'ケース',
    unique: 'ユニーク',
    pagingText: '発見{N, plural, few{について} many{について}} {N} {N, plural, one{項目} few{項目} many{項目} other{項目}}について',
    noName: 'テーマ #{id}'
  },
  mapPage: {
    meta: {
      title: 'GTA5 RPマップ｜マジェスティック・ウィキ',
      description: 'GTA5 RPのマジェスティックRPサーバーのインタラクティブマップ。場所を探索し、宝物、動物、電話、ゴミを見つける。タグやゾーンを追加できる詳細な世界地図。すべての重要な場所と興味のあるポイントを1つのリソースに！',
      custom: {
        title: '{type} | GTA5 RPマップ｜マジェスティック・ウィキ',
        description: 'GTA5 RPのMajestic RPサーバーの {type} の詳細マップ。場所を探索し、重要なポイントを見つけ、あなた自身のマーカーを作成します。マップ上のオブジェクトの位置に関する完全な情報！'
      }
    },
    dataLoading: 'データロード...',
    defaultPoints: '標準タグ',
    animalLayer: '動物マップ',
    rubbishLayer: 'ゴミ箱の地図',
    telephoneLayer: '電話地図',
    treasureLayer: '宝の地図',
    defaultLayer: '世界地図',
    allLayers: '地図レイヤー',
    backToHome: 'ホームへ戻る',
    zoomIn: 'アプローチ',
    zoomOut: '引き戻す',
    point: 'ポイント',
    polygon: 'ゾーン',
    addPoint: 'ポイントを追加する',
    addPolygon: 'ゾーンの追加',
    copyCoordinates: 'コピー座標',
    savePointPosition: 'セーブポジション',
    editPointPosition: 'ポジション変更',
    layerNamePlaceholder: 'ゾーン名',
    layerDescriptionPlaceholder: 'ゾーンの説明'
  },
  serversPage: {
    meta: {
      title: 'オンラインGTA5 RPサーバー｜マジェスティックWiki',
      description: 'GTA5 RPのサーバーMajestic RPのオンラインプレイヤーの現在の統計。現在のオンライン、ピーク、キュー、アクティビティグラフ。最も人気のあるロールプレイングプロジェクトに参加してください！'
    },
    dataLoading: 'データロード...',
    title: 'オンラインサーバー',
    online: '{count} オンライン',
    inQueue: '{count} キューに',
    peakOnline: '{count} ピーク',
    currentOnline: 'カレントオンライン',
    peakOnline24h: 'ピーク・トゥデイ',
    peakOnlineAllTime: 'しじょうさいこう',
    peakOnlineTooltip: '{percent}前日比',
    techWorks: 'テクニカル・ワーク',
    forBeginners: '初心者向け',
    experience: '{multiplier}経験 x',
    newServer: '新しい',
    seasonPassMultiplier: '{multiplier}スキップ体験 x',
    combinedView: '一般',
    separateView: 'セパレート'
  },
  research: {
    placeholder: 'ウェブサイトで情報を検索...',
    skin: '皮膚',
    vehicle: '輸送',
    realty: '不動産',
    biz: 'ビジネス',
    clothing: '衣類',
    item: 'テーマ',
    post: 'ポスト'
  },
  userModal: {
    login: 'ログイン',
    signout: '外に出る',
    register: '参加登録',
    registerEnd: '登録する',
    restore: '修復',
    restoreEnd: 'リストア',
    avatarDelete: 'アバター削除',
    headingUpdate: 'ユーザーの変更 #{id}',
    postAdd: '投稿の公開',
    postUpdate: '投稿の編集',
    postDelete: '投稿の削除',
    vehicleAdd: '輸送手段の追加',
    vehicleUpdate: '輸送の編集',
    vehicleDelete: '輸送撤去',
    realtyAdd: 'プロパティの追加',
    realtyUpdate: '不動産の変化',
    realtyDelete: '不動産の撤去',
    bizAdd: '事業の追加',
    bizUpdate: 'ビジネスの変化',
    bizDelete: '事業撤去',
    mapItemAdd: '地図にアイテムを追加する',
    mapItemUpdate: '地図上の要素を変更する',
    mapItemDelete: '地図上のアイテムの削除',
    clothingAdd: '衣類の追加',
    clothingUpdate: '着替え',
    clothingDelete: '衣服の脱着',
    skinAdd: 'スキンの追加',
    skinUpdate: 'スキンの変更',
    skinDelete: 'スキンの除去',
    itemAdd: 'アイテムの追加',
    itemUpdate: 'オブジェクトの変更',
    itemDelete: '対象物の除去',
    fileUpload: '画像のアップロード',
    viewPrivate: '個人データを見る',
    doMagic: 'マジック機能へのアクセス',
    animationAdd: 'アニメーションの追加',
    animationUpdate: 'アニメーションの変更',
    animationDelete: 'アニメーションの削除',
    categoryAdd: 'カテゴリーの追加',
    categoryUpdate: 'カテゴリーの変更',
    categoryDelete: 'カテゴリーの削除',
    mapLayerAdd: '地図レイヤーの追加',
    mapLayerUpdate: '地図レイヤーの変更',
    mapLayerDelete: 'マップレイヤーの削除',
    post: 'ポスト',
    skin: '皮膚',
    biz: 'ビジネス',
    clothing: '衣類',
    item: 'テーマ',
    vehicle: '輸送',
    animation: 'アニメーション',
    category: 'カテゴリー',
    realty: '不動産',
    mapLayer: '地図レイヤー',
    changelog: '変更履歴'
  },
  restoreModal: {
    title: 'アカウントの復元',
    newPassword: '新しいパスワード',
    newPasswordRepeat: '新しいパスワードを繰り返す',
    passwordConfirm: 'パスワードの変更',
    passwordError: 'パスワードが一致しない'
  },
  bizModal: {
    titleUpdate: 'ビジネスの変化 #{id}',
    titleAdd: '新規事業の追加'
  },
  categoryModal: {
    titleUpdate: 'カテゴリーの変更 #{id}',
    titleAdd: '新しいカテゴリーの追加'
  },
  mapLayerModal: {
    titleUpdate: '#{id}地図レイヤーの変更',
    titleAdd: '新しい地図レイヤーの追加'
  },
  clothingModal: {
    titleUpdate: '着替え #{id}',
    titleAdd: '新しい服の追加',
    itemAdd: '色を加える',
    itemGenerate: 'すべての色を生成する'
  },
  itemModal: {
    titleUpdate: '話題を変える #{id}',
    titleAdd: '新しいアイテムの追加'
  },
  realtyModal: {
    titleUpdate: '変化する不動産 #{id}',
    titleAdd: '新しいプロパティを追加する'
  },
  skinModal: {
    titleUpdate: '皮膚の変更 #{id}',
    titleAdd: '新しいスキンの追加'
  },
  vehicleModal: {
    titleUpdate: '交通機関の変更 #{id}',
    titleAdd: '新しい輸送手段の追加',
    titleDefault: '{name}。',
    itemAdd: 'ドジャーを追加する'
  },
  mapItemModal: {
    titleUpdate: 'マップ要素の変更',
    titleAdd: '新しい地図要素の追加',
    titleDelete: '地図アイテムの削除',
    descriptionDelete: '本当にマップからアイテムを削除しますか？このアクションはキャンセルできません。'
  },
  animationModal: {
    titleUpdate: 'アニメーションの変更 #{id}',
    titleAdd: '新しいアニメーションの追加'
  }
};