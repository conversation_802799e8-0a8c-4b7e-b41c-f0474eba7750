export default {
  global: {
    all: 'Todo',
    back: 'Volver atrás',
    forward: 'Vamos',
    paging: '<PERSON><PERSON><PERSON><PERSON> {page} de {maxPage}',
    close: '<PERSON>rra<PERSON>',
    clear: '<PERSON>rra<PERSON>',
    save: '<PERSON><PERSON>',
    delete: '<PERSON><PERSON>',
    deleteYes: '¿Estás seguro de la eliminación?',
    cancel: '<PERSON><PERSON><PERSON>',
    publish: 'Publicar',
    edit: 'Edit<PERSON>',
    share: 'Comp<PERSON>',
    add: 'Aña<PERSON>',
    active: 'Activo',
    deleted: 'Eliminado',
    blocked: 'Bloqueado',
    learnMore: 'Leer más',
    panel: 'Panel',
    theme: 'Tema',
    language: 'Lengua',
    user: 'Usua<PERSON>',
    confirm: 'Confirmar',
    restore: 'Restauración',
    yes: 'Sí',
    no: 'No hay',
    new: 'Nuevo',
    preview: 'Vista previa',
    sortPriceDesc: 'A la baja del precio',
    sortPriceAsc: 'Por aumento del precio',
    sortPriceMinMax: 'Gama de precios',
    sortDateDesc: 'En orden descendente de fecha',
    sortDateAsc: 'En orden ascendente de fecha',
    noSellPrice: 'No se puede comprar',
    noBuyPrice: 'No se vende',
    noData: {
      title: 'No se ha encontrado nada',
      description: 'Intenta cambiar o simplificar tu consulta de búsqueda'
    },
    battlePass: {
      summer: 'Pase de Batalla de Verano',
      winter: 'Pase de Batalla de Invierno'
    },
    case: {
      default: 'Maletín estándar',
      vehicles: 'Maletín de coche',
      halloween: 'Caso de Halloween',
      spring: 'Maletín Primavera',
      autumn: 'Maletín de otoño',
      summer: 'Caso de verano',
      summerExtra: 'Un caso especial de verano',
      summerSkins: 'Funda de piel de verano',
      summerVehicles: 'Maletín de transporte de verano',
      winter: 'Caso de invierno',
      winterExtra: 'Un caso especial de invierno',
      winterSkins: 'Maletín de invierno con pieles',
      winterVehicles: 'Maletín de transporte de invierno',
      toys: 'Maletín de juguete'
    },
    subscription: { premium: 'Suscripción Premium' },
    copy: 'Copia'
  },
  footer: { disclaimer: 'Toda la información contenida en el sitio web es meramente informativa y no constituye una oferta pública.' },
  footer: { disclaimer: 'Toda la información contenida en el sitio web es meramente informativa y no constituye una oferta pública.' },
  editor: {
    placeholder: '¡Escribamos un post impresionante!',
    text: 'Párrafo',
    heading: 'Título',
    warning: 'Nota',
    quote: 'Cita',
    code: 'Código',
    delimiter: 'Separador',
    link: 'Referencia',
    linkPlaceholder: 'Introduce el enlace',
    marker: 'Marcador',
    bold: 'Negrita',
    italic: 'Cursiva',
    inlineCode: 'Monoespaciado',
    underline: 'Destacado',
    copytext: 'Copiable',
    log: 'Logue',
    map: {
      name: 'Mapa',
      params: 'Introduce los parámetros de la tarjeta',
      caption: 'Introduce una descripción del mapa'
    },
    table: {
      name: 'Tabla',
      addColumnLeft: 'Añade una columna a la izquierda',
      addColumnRight: 'Añadir columna a la derecha',
      deleteColumn: 'Borrar columna',
      addRowAbove: 'Añade una línea en la parte superior',
      addRowBelow: 'Añade una línea en la parte inferior',
      deleteRow: 'Borrar cadena',
      withHeadings: 'Con los titulares',
      withoutHeadings: 'Sin titulares'
    },
    image: {
      name: 'Imagen',
      withBorder: 'Con marco',
      stretchImage: 'Estira la imagen',
      withBackground: 'Con el fondo',
      selectImage: 'Selecciona una imagen',
      uploadError: 'No se ha podido cargar la imagen, prueba con otra',
      caption: 'Descripción de la imagen',
      withCaption: 'Con una descripción'
    },
    carousel: {
      name: 'Rejilla de imágenes',
      caption: 'Descripción de la imagen',
      addImage: 'Selecciona una imagen'
    },
    list: {
      name: 'Lista',
      ordered: 'Numerado',
      unordered: 'Sin numerar',
      checklist: 'Lista de control'
    },
    header: {
      heading1: 'Rúbrica H1',
      heading2: 'Rúbrica H2',
      heading3: 'Rúbrica H3',
      heading4: 'Cabecera H4'
    },
    embed: { caption: 'Descripción del enlace' },
    convertTo: 'Convertir a',
    moveUp: 'Sube',
    moveDown: 'Baja',
    anchor: 'Ancla',
    clickToTune: 'Pulsa para configurar',
    dragToMove: 'o arrastrar y soltar',
    filter: 'Buscar',
    nothingFound: 'No se ha encontrado nada'
  },
  alerts: {
    loginSuccess: 'Inicio de sesión con éxito',
    loginError: 'Error durante la autorización',
    registerSuccess: 'Inscripción satisfactoria en el sitio web',
    registerError: 'Error de registro',
    restoreSuccess: 'Se han enviado instrucciones de recuperación a tu buzón de correo',
    restoreError: 'Error al restaurar el acceso a la cuenta',
    signoutSuccess: 'Cierre de sesión con éxito',
    postLoadError: 'Error al subir una entrada',
    postShareSuccess: 'Enlace al post copiado en el portapapeles',
    copyCommandSuccess: 'La orden se copia en el portapapeles',
    restoreAccountSuccess: 'La recuperación de la cuenta se ha realizado correctamente',
    restoreAccountError: 'Error al restaurar el acceso a la cuenta',
    verificateAccountSuccess: 'Buzón confirmado correctamente',
    verificateAccountError: 'Error de confirmación de buzón',
    dataUpdateSuccess: 'Actualización correcta de la información',
    dataUpdateError: 'Error al actualizar la información',
    dataDeleteSuccess: 'Eliminación correcta de la información',
    dataDeleteError: 'Error al borrar información',
    dataAddSuccess: 'Añadir información con éxito',
    dataAddError: 'Error al añadir información',
    mapMoveInfo: 'Mueve el punto a otra posición y guarda',
    mapCoordinatesCopySuccess: 'Coordenadas copiadas correctamente',
    copyTextSuccess: 'Texto copiado correctamente'
  },
  mail: {
    verificate: {
      subject: 'Confirmación de la dirección de correo electrónico',
      title: 'Confirmación',
      description: 'Para verificar y confirmar los derechos de tu cuenta, tienes que hacer clic en el botón de abajo. El enlace estará activo durante 24 horas.'
    },
    restore: {
      subject: 'Restablecer el acceso a la cuenta',
      title: 'Restauración',
      description: 'Para restablecer el acceso a tu cuenta, tienes que hacer clic en el botón que aparece a continuación. El enlace estará activo durante 24 horas.'
    },
    restoreSuccess: {
      subject: 'Restablecer con éxito el acceso a la cuenta',
      title: 'Recuperación con éxito',
      description: 'Has cambiado correctamente tu contraseña en el sitio. Si no lo has hecho, ¡cambia tu contraseña inmediatamente!'
    },
    verificateSuccess: {
      subject: 'Restablecer con éxito el acceso a la cuenta',
      title: 'Confirmación satisfactoria',
      description: 'Has confirmado correctamente tu buzón, ¡bienvenido!'
    },
    caption: 'El correo electrónico es una notificación y se genera automáticamente, por favor, no respondas a él.'
  },
  fields: {
    price: 'Costo',
    buyPrice: 'Coste de compra',
    sellPrice: 'Coste de venta',
    mcPrice: 'Coste de compra en MC',
    money: 'Moneda',
    coin: 'Moneda Majestic',
    gift: 'Un regalo',
    default: 'Estándar',
    unique: 'Único',
    event: 'Evento',
    battlePass: 'Pase de Batalla',
    lootbox: 'Caso',
    capacityKg: 'Capacidad',
    email: 'Correo',
    password: 'Contraseña',
    username: 'Apodo',
    weightKg: 'kg',
    speed: 'Velocidad',
    maxSpeed: 'Velocidad máxima',
    maxSpeedFT: 'Velocidad máxima (FT)',
    accelerationTo100: 'Aceleración a 100 km/h',
    speedSystem: 'km/h',
    timeSystem: 'segundos',
    uploadDescription: 'Toca o arrastra una imagen a un área',
    placeholder: 'Introduce un valor en el campo',
    imagePlaceholder: 'Introduce un enlace a la imagen',
    selectPlaceholder: 'Selecciona un valor de la lista',
    videoPlaceholder: 'Introduce un enlace al vídeo',
    soundPlaceholder: 'Introduce un enlace al audio',
    id: 'ID',
    serial: 'Número de serie',
    kind: 'Tipo',
    category: 'Categoría',
    name: 'Nombre',
    view: 'Ver',
    layer: 'Capa',
    icon: 'Icono',
    circle: 'Punto',
    polygon: 'Polígono',
    emoji: 'Emoji',
    description: 'Descripción',
    gender: 'Género',
    value: 'Tipo de valor',
    valueName: 'Descripción del precio adicional',
    visibility: 'Disponibilidad',
    visibilityPrivate: 'Acceso restringido',
    visibilityPublic: 'Visibilidad para todos',
    activityHidden: 'Oculto al acceso público',
    activityVisible: 'Disponible para todos',
    garageSlots: 'Ranuras de garaje',
    maxTenants: 'Número de residentes',
    gasoline: 'Combustible',
    brand: 'Marca',
    model: 'Modelo',
    trunk: 'Maletero',
    tradable: 'Transferibilidad',
    buyable: 'Opción de compra',
    upgradesPriceFT: 'Mejoras disponibles (FT)',
    upgradesEngine: 'Motor',
    upgradesBreaks: 'Frenos',
    upgradesSuspension: 'Suspensión',
    upgradesTurbo: 'Turbo',
    upgradesTransmission: 'Caja',
    paintingsMainPrice: 'Pintura básica',
    paintingsSubPrice: 'Pintura adicional',
    paintingsOtherPrice: 'Otra pintura',
    paintingsBrightMetallic: 'Metalizado brillante',
    paintingsMetallic: 'Metálico',
    paintingsRichMetallic: 'Rico metálico',
    paintingsDarkMetallic: 'Oscuro metálico',
    paintingsMatte: 'Mate',
    paintingsMatteMetallic: 'Metal cepillado',
    paintingsSatin: 'Satén',
    paintingsMetal: 'Metal',
    paintingsShadowChrome: 'Sombra cromada',
    paintingsPureChrome: 'Cromo puro',
    paintingsPearl: 'Tono nacarado',
    paintingsWheels: 'Color de los discos',
    paintingsDetails: 'Color de las piezas',
    paintingsAdditionalDetails: 'Color adicional de las piezas',
    paintingsLights: 'Luz del faro',
    paintingsSmoke: 'El color del humo de los neumáticos',
    paintingsNeon: 'Color de neón',
    total: 'Total',
    language: 'Lengua',
    radius: 'Radio',
    color: 'Color',
    colorPlaceholder: 'Elige un color',
    opacity: 'Transparencia',
    alias: 'Seudónimo',
    aliases: 'Alias',
    looped: 'En bucle',
    battlePassId: 'ID del Pase de Batalla',
    gasolineCapacityLiter: 'л',
    gasolineCapacity: 'Capacidad del depósito',
    source: 'Fuente',
    sources: 'Fuentes',
    priceDescription: 'Descripción de costes',
    layerName: 'Nombre de la capa',
    layerNamePlaceholder: 'Introduce un nombre para la capa',
    layerDescription: 'Descripción de la capa',
    layerDescriptionPlaceholder: 'Introduce una descripción para la capa',
    layerColor: 'Color de la capa',
    layerColorPlaceholder: 'Elige un color',
    layerOpacity: 'Transparencia de las capas',
    zIndexOffset: 'Nivel de índice Z',
    zIndexOffsetPlaceholder: 'Introduce el valor del nivel de índice Z',
    comment: 'Comentario'
  },
  vehiclesPage: {
    meta: {
      title: 'GTA5 RP Transporte y Coches | Majestic Wiki',
      description: 'Catálogo completo de vehículos del servidor Majestic RP en GTA5 RP. Características detalladas de coches, motos, helicópteros, aviones y barcos. Averigua precios, velocidad, maniobrabilidad, capacidad del maletero y otros parámetros. Majestic Motors, Coches, Camiones, Helicópteros, Barcos, Motos, Bicicletas: ¡todo para GTA5 RP!',
      custom: {
        title: '{type} | GTA5 RP Transporte | Majestic Wiki',
        description: 'Catálogo detallado {type} en el servidor Majestic RP en GTA5 RP. Características, precios, velocidad, maniobrabilidad y otros parámetros. ¡Información completa sobre el transporte para GTA5 RP!'
      }
    },
    default: 'Estándar',
    donate: 'Moneda Majestic',
    battlePass: 'Pase de Batalla',
    case: 'Caso',
    unique: 'Único',
    title: 'Mesa de transporte',
    search: 'Buscador de transporte...',
    irl: 'Majestic Motors',
    car: 'Coches',
    freight: 'Camiones',
    helicopter: 'Helicópteros',
    plane: 'Самолеты',
    boat: 'Barcos',
    motorbike: 'Motos',
    bike: 'Bicicletas',
    trailer: 'remolques',
    military: 'Militar',
    special: 'Especialidad',
    priceDisclaimer: 'El coste final puede variar debido a una comisión en el LSC de tu servidor',
    basicInformation: 'Información básica',
    additionalInformation: 'Información adicional',
    compare: 'Compara',
    compareLimit: 'Se pueden comparar un máximo de 5 TC',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{transporte} few{transporte} many{transporte} other{transporte}}'
  },
  vehiclePage: {
    meta: {
      title: 'GTA5 RP Transporte | Majestic Wiki',
      description: 'Información detallada sobre el transporte en el servidor Majestic RP en GTA5 RP. Características, precios, mejoras y pintura. Infórmate sobre la velocidad máxima, la aceleración, la capacidad del maletero, el coste de las mejoras y otros parámetros de cada vehículo.',
      custom: {
        title: '{name} | GTA5 RP Transporte | Majestic Wiki',
        description: 'Información detallada sobre {name} en el servidor Majestic RP en GTA5 RP. Características, precios, mejoras y trabajos de pintura. Infórmate sobre la velocidad máxima, la aceleración, la capacidad de arranque, el coste de las mejoras y otros parámetros.'
      }
    },
    backToTable: 'Volver a la lista',
    labelsUniqueId: 'ID único',
    labelsPrice: 'Costo',
    labelsBuyPrice: 'Coste de compra',
    labelsSellPrice: 'Coste de venta',
    labelsMaxSpeed: 'Velocidad máxima',
    labelsMaxSpeedFT: 'Velocidad máxima (FT)',
    labelsAccelerationTo100: 'Aceleración a 100 km/h',
    labelsTradable: 'Transferibilidad',
    labelsTrunk: 'Capacidad del maletero',
    labelsGasolineCapacity: 'Capacidad del depósito',
    labelsGasolineType: 'Tipo de combustible',
    upgradesTitle: 'Mejoras en el transporte',
    upgradesDescription: 'Coste de las mejoras al nivel máximo',
    upgradesEngine: 'Motor',
    upgradesEngineDescription: 'Libera el verdadero potencial del transporte',
    upgradesBreaks: 'Frenos',
    upgradesBreaksDescription: 'Control total de la velocidad en cualquier carretera',
    upgradesTurbo: 'Turbo',
    upgradesTurboDescription: 'Aceleración explosiva en cualquier momento',
    upgradesTransmission: 'Caja',
    upgradesTransmissionDescription: 'Cambio rapidísimo de todas las marchas',
    upgradesSuspension: 'Suspensión',
    upgradesSuspensionDescription: 'Comodidad perfecta a la altura adecuada',
    upgradesTotal: 'Coste de las mejoras',
    upgradesDisclaimer: 'El coste final puede variar debido a una comisión en el LSC de tu servidor',
    colorsTitle: 'Pintura de vehículos',
    colorsDescription: 'Tipos disponibles y tipos de pintura con coste',
    paintingsMainPrice: 'Pintura básica',
    paintingsSubPrice: 'Pintura adicional',
    paintingsOtherPrice: 'Otra pintura',
    paintingsBrightMetallic: 'Metalizado brillante',
    paintingsMetallic: 'Metálico',
    paintingsRichMetallic: 'Rico metálico',
    paintingsDarkMetallic: 'Oscuro metálico',
    paintingsMatte: 'Mate',
    paintingsMatteMetallic: 'Metal cepillado',
    paintingsSatin: 'Satén',
    paintingsMetal: 'Metal',
    paintingsShadowChrome: 'Sombra cromada',
    paintingsPureChrome: 'Cromo puro',
    paintingsPearl: 'Tono nacarado',
    paintingsWheels: 'Color de los discos',
    paintingsDetails: 'Color de las piezas',
    paintingsAdditionalDetails: 'Color adicional de las piezas',
    paintingsLights: 'Luz del faro',
    paintingsSmoke: 'El color del humo de los neumáticos',
    paintingsNeon: 'Color de neón',
    paintingsTotal: 'Coste final de pintura',
    shareLink: 'Comparte el enlace al transporte',
    shareLinkCopied: 'El enlace de transporte se ha copiado correctamente',
    addToCompare: 'Añadir a la comparación',
    addedToCompare: 'Se agregó el producto Transporte a la comparación',
    goToCompare: 'Ir a comparar',
    removeFromCompare: 'Eliminar de la comparación',
    removedFromCompare: 'Se ha eliminado el transporte de la comparación'
  },
  vehiclesComparePage: {
    meta: {
      title: 'GTA5 RP Comparación de transportes | Majestic Wiki',
      description: 'Compara las características y los precios de los distintos vehículos en el servidor Majestic RP de GTA5 RP. ¡Averigua qué vehículo se adapta mejor a tus necesidades! Compara velocidad, precio, capacidad y otros parámetros.'
    },
    name: 'Nombre',
    type: 'Tipo',
    brand: 'Marca',
    kits: 'Paquetes',
    model: 'Modelo',
    trunkCapacity: 'Capacidad del maletero',
    maxSpeed: 'Velocidad máxima',
    maxSpeedFT: 'Velocidad máxima (FT)',
    price: 'Costo',
    donatePrice: 'Coste en Majestic Coin',
    sellPrice: 'Coste de venta',
    sellPriceBeforeTax: 'Coste de venta (antes de impuestos)',
    sellPriceWithDiscount: 'Coste de venta (descontado)',
    accelerationTo100: 'Tiempo de aceleración hasta 100 km/h',
    gasolineCapacity: 'Capacidad del depósito',
    gasolineType: 'Tipo de combustible',
    isTradable: 'Transferibilidad',
    isBuyable: 'Opción de compra',
    createdAt: 'Fecha de comparecencia',
    gasolineCapacityUnit: 'л',
    maxSpeedUnit: 'km/h',
    maxSpeedFTUnit: 'km/h',
    priceUnit: '₽',
    accelerationTo100Unit: 'segundos',
    trunkCapacityUnit: 'kg',
    showOnlyDifferent: 'Mostrar sólo los que son diferentes',
    showAll: 'Ver todo',
    share: 'Comparte una comparación',
    linkCopied: 'El enlace de comparación se ha copiado correctamente',
    openVehicle: 'Abre la página de transporte',
    deleteVehicle: 'Eliminar un vehículo de la comparación',
    noDifferences: 'No hay distinción'
  },
  skinsPage: {
    meta: {
      title: 'GTA5 RP Weapon Skins | Majestic Wiki',
      description: 'Catálogo completo de skins de armas en el servidor Majestic RP en GTA5 RP. Infórmate sobre cómo conseguir, coste y características de cada skin para distintos tipos de armas.',
      custom: {
        title: 'Skins en {type} | GTA5 RP | Majestic Wiki',
        description: 'Catálogo de skins para {type} en el servidor Majestic RP en GTA5 RP. Información detallada sobre cómo conseguir, coste y características de las skins.'
      }
    },
    title: 'Tabla de piel',
    search: 'Buscar piel...',
    apPistol: 'Pistola perforadora',
    assaultRifle: 'Rifle de asalto',
    assaultRifleMk2: 'Rifle de asalto Mk2',
    bullpupRifle: 'Rifle Bullpup',
    carbineRifle: 'Rifle de carabina',
    carbineRifleMk2: 'Rifle de carabina Mk2',
    combatPDW: 'PDW de combate',
    gusenberg: 'Subfusil Thompson.',
    heavyPistol: 'Pistola pesada',
    heavyShotgun: 'Escopeta pesada',
    heavySniper: 'Rifle de francotirador pesado',
    heavySniperMk2: 'Rifle de francotirador pesado Mk2',
    lightvest: 'Chaleco antibalas',
    machinePistol: 'PP pequeño',
    marksmanPistol: 'Pistola de tirador',
    microSMG: 'Micro SMG',
    militaryRifle: 'Rifle militar',
    revolver: 'Revólver',
    smgMk2: 'SMG Mk2',
    specialCarbine: 'Carabina especial',
    vintagePistol: 'Pistola vintage',
    combatMgMk2: 'Subfusil Mk2',
    heavyRifle: 'Fusil pesado',
    tacticalRifle: 'Rifle táctico',
    tecPistol: 'SMG táctico',
    noName: 'Piel #{id}',
    battlePass: 'Pase de Batalla',
    case: 'Caso',
    unique: 'Único',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{piel} few{piel} many{pieles} other{pieles}}'
  },
  animationsPage: {
    meta: {
      title: 'Animaciones RP de GTA5 | Majestic Wiki',
      description: 'Catálogo completo de animaciones del servidor Majestic RP en GTA5 RP. Todo tipo de animaciones: acciones, poses, bailes y animaciones exclusivas. Descubre cómo conseguir y el coste de cada animación.',
      custom: {
        title: '{type} | Animaciones RP de GTA5 | Majestic Wiki',
        description: 'Catálogo {type} animaciones en el servidor Majestic RP en GTA5 RP. Información detallada sobre cómo conseguir, costear y utilizar las animaciones.'
      }
    },
    title: 'Tabla de animación',
    search: 'Búsqueda de animación...',
    default: 'Estándar',
    donate: 'Moneda Majestic',
    battlePass: 'Pase de Batalla',
    case: 'Caso',
    unique: 'Único',
    action: 'Acciones',
    pose: 'Poses',
    positive: 'Positivos',
    negative: 'Negativos',
    dances: 'Bailes',
    etc: 'Otro',
    exclusive: 'Exclusivos',
    noName: 'Animación #{id}',
    pagingText: 'Se encontró{N, plural, one{ una} other{ } } {N} {N, plural, one{ animación} other{ animaciones}}'
  },
  realtyPage: {
    meta: {
      title: 'GTA5 RP Inmobiliaria | Majestic Wiki',
      description: 'Catálogo completo de bienes inmuebles en el servidor Majestic RP de GTA5 RP. Casas, pisos, oficinas y almacenes con características detalladas, precios y ubicación. Averigua el número de inquilinos, plazas de garaje y otros parámetros.',
      custom: {
        title: '{type} | GTA5 RP Real Estate | Majestic Wiki',
        description: 'Catálogo {type} en el servidor Majestic RP en GTA5 RP. Información detallada sobre las características, precios, ubicación y prestaciones del inmueble.'
      }
    },
    title: 'Tabla de propiedades',
    search: 'Búsqueda de propiedades...',
    majesticMotors: 'Majestic Motors',
    house: 'Casa',
    apartment: 'Pisos',
    office: 'Oficinas',
    warehouse: 'Almacenes',
    tenantsFor: '{N} {N, plural, one{arrendatario} few{arrendatario} many{arrendatarios} other{arrendatarios}}',
    garageSlotsFor: '{N} {N, plural, one{plazas de garaje} few{plazas de garaje} many{plazas de garaje} other{plazas de garaje}}',
    viewOnMap: 'Ver en el mapa',
    mapPosition: 'Ubicación',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{inmobiliario} few{inmobiliario} many{inmobiliario} other{inmobiliario}}',
    noName: 'Inmobiliaria #{id}'
  },
  postsPage: {
    meta: {
      title: 'Editar un post | Majestic Wiki',
      description: 'Crea y edita entradas en la Wiki Majestic para GTA5 RP. Publicar información actualizada sobre transporte, propiedades, negocios y otros aspectos del juego.'
    },
    titlePlaceholder: 'Introduce el título del post',
    category: 'Categoría',
    noCategory: 'Sin categoría',
    availability: 'Disponibilidad',
    allView: 'Visibilidad para todos',
    privateView: 'Acceso restringido',
    relevance: 'Relevancia',
    relevanceData: 'Información actualizada',
    outdatedData: 'Información obsoleta',
    categoryPosition: 'Categoría',
    categoryPositionDescription: 'Por ejemplo, "50" o "-5".',
    postDeleted: 'Puesto eliminado',
    author: 'Autor del artículo',
    editor: 'Editor del artículo',
    contents: 'Contenido del artículo',
    readingTime: 'Tiempo de lectura',
    readingTimeLength: '{N} {N, plural, one{minuto} few{minutos} many{minutos} other{minutos}}',
    publishDate: 'Publicación de {date}',
    views: '{N} {N, plural, one{visualización} few{visualización} many{visualización} other{visualización}}',
    youLiked: 'Te ha gustado',
    youAndPeopleLiked: 'A ti también te gustaba {likes}',
    peopleLiked: '{N} {N, plural, one{me gusta} few{me gusta} many{me gusta} other{me gusta}}',
    categories: 'Categorías',
    posts: 'Puestos',
    foundMistake: '¿Has encontrado un error en el artículo?',
    language: 'Lengua'
  },
  panelPage: {
    users: {
      title: 'Gestión de usuarios | Majestic Wiki',
      heading: 'Gestión de usuarios',
      admin: 'Administrador',
      user: 'Usuario',
      customUser: 'Usuario con derechos',
      search: 'Empieza a escribir tu consulta de búsqueda....',
      comment: 'Comentario',
      pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{usuario} few{usuario} many{usuarios} other{usuarios}}'
    },
    posts: {
      title: 'Gestión de entradas | Majestic Wiki',
      heading: 'Gestión de puestos',
      views: '{N} {N, plural, one{vistas} few{vistas} many{vistas} other{vistas}}',
      likes: '{N} {N, plural, one{me gusta} few{me gusta} many{me gusta} other{me gusta}}'
    },
    categories: {
      title: 'Gestión de Categorías | Majestic Wiki',
      heading: 'Gestión de categorías'
    },
    mapLayers: {
      title: 'Gestión de capas de mapas | Majestic Wiki',
      heading: 'Gestionar las capas del mapa'
    },
    logs: {
      title: 'Auditoría de registros - Majestic Wiki',
      heading: 'Auditoría de registros',
      search: 'Empieza a escribir tu consulta de búsqueda....',
      biz: 'Negocio',
      category: 'Categoría',
      clothing: 'Ropa',
      file: 'Archivo',
      item: 'Objeto',
      mapItem: 'Elemento del mapa',
      mapLayer: 'Capa de mapa',
      post: 'El puesto',
      realty: 'Inmueble',
      skin: 'Piel',
      user: 'Usuario',
      vehicle: 'Transporte',
      animation: 'Animación',
      actions: {
        unknown: '{username} realizó un acto desconocido',
        bizCreate: '{username} añade una empresa {id}',
        bizUpdate: '{username} negocio cambiado {id}',
        bizDelete: '{username} suprimido el negocio {id}',
        categoryCreate: '{username} añadida la categoría {id}',
        categoryUpdate: '{username} cambió la categoría {id}',
        categoryDelete: '{username} categoría eliminada {id}',
        clothingCreate: '{username} ropa añadida {id}',
        clothingUpdate: '{username} me cambié de ropa {id}',
        clothingDelete: '{username} borrada la ropa {id}',
        fileCreate: '{username} añadido un archivo {id}',
        fileUpdate: '{username} modificado el archivo {id}',
        fileDelete: '{username} borrado el archivo {id}',
        itemCreate: '{username} añadido el elemento {id}',
        itemUpdate: '{username} cambió de tema {id}',
        itemDelete: '{username} eliminado el elemento {id}',
        mapItemCreate: '{username} añadido un elemento al mapa {id} a "{layer}"',
        mapItemUpdate: '{username} cambiado el elemento del mapa {id} por "{layer}"',
        mapItemDelete: '{username} borrar un elemento del mapa {id} a "{layer}"',
        mapLayerCreate: '{username} añadido una capa de mapa {id}',
        mapLayerUpdate: '{username} cambiado la capa del mapa {id}',
        mapLayerDelete: '{username} borrado de la capa del mapa {id}',
        postCreate: '{username} añadido un post {id}',
        postUpdate: '{username} puesto modificado {id}',
        postDelete: '{username} post borrado {id}',
        realtyCreate: '{username} añadida la propiedad {id}',
        realtyUpdate: '{username} cambió la inmobiliaria {id}',
        realtyDelete: '{username} inmuebles borrados {id}',
        skinCreate: '{username} añadido la piel {id}',
        skinUpdate: '{username} cambió la piel {id}',
        skinDelete: '{username} borrado la piel {id}',
        userCreate: '{username} usuario añadido {id}',
        userUpdate: '{username} cambió el usuario {id}',
        userDelete: '{username} usuario eliminado {id}',
        vehicleCreate: '{username} transporte añadido {id}',
        vehicleUpdate: '{username} cambió el transporte {id}',
        vehicleDelete: '{username} suprimido el transporte {id}',
        animationCreate: '{username} añadida animación {id}',
        animationUpdate: '{username} cambió la animación {id}',
        animationDelete: '{username} suprimida la animación {id}',
        changelogCreate: '{username} añadido registro de cambios {id}',
        changelogUpdate: '{username} cambiado el registro de cambios {id}',
        changelogDelete: '{username} borrado registro de cambios {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: 'Ajustes',
      users: 'Usuarios',
      categories: 'Categorías',
      mapLayers: 'Capas del mapa',
      posts: 'Puestos',
      logs: 'Registros'
    }
  },
  changelogPage: {
    titlePlaceholder: 'Introduce el nombre del cambio',
    descriptionPlaceholder: 'Introduce una descripción del cambio',
    availableAt: 'Fecha de publicación',
    soon: 'a través de',
    ago: 'volver atrás',
    fix: 'Corregido',
    feat: 'Añadido',
    chore: 'Modificado',
    style: 'Diseño',
    refactor: 'Reciclado'
  },
  changelogsPage: {
    meta: {
      title: 'Historia de los cambios | Majestic Wiki',
      description: 'Historial completo de actualizaciones y cambios del servidor RP Majestic. Mantente atento a las nuevas funciones, mejoras en la jugabilidad, correcciones de errores y equilibrios. Información actualizada sobre todas las actualizaciones del proyecto.'
    },
    title: 'Historia de los cambios',
    search: 'Encontrar el cambio...'
  },
  homePage: {
    meta: {
      title: 'Majestic Wiki | Enciclopedia RP de GTA5',
      description: 'Majestic Wiki es una enciclopedia completa para jugadores de GTA5 RP. Información sobre transporte, bienes inmuebles, negocios, misiones y otros aspectos del juego. Información actualizada sobre precios, características y cómo obtenerlas.'
    },
    tools: {
      map: {
        title: 'Mapa',
        description: 'Mapa del servidor con todas las ubicaciones'
      },
      vehicles: {
        title: 'Transporte',
        description: 'Todos los transportes disponibles en el servidor'
      },
      realty: {
        title: 'Inmueble',
        description: 'Datos de todas las casas y pisos'
      },
      biz: {
        title: 'Negocios',
        description: 'Opción de ganancia avanzada'
      },
      clothesMale: {
        title: 'Ropa de hombre',
        description: 'Un desfile de moda masculina'
      },
      clothesFemale: {
        title: 'Ropa de mujer',
        description: 'Desfile de moda femenina'
      },
      skins: {
        title: 'Aspectos',
        description: 'Catálogo de todos los modelos disponibles'
      },
      items: {
        title: 'Objetos',
        description: 'Elementos disponibles en el servidor'
      },
      servers: {
        title: 'Servidores',
        description: 'Lista de todos los servidores de Majestic RP'
      },
      animations: {
        title: 'Animaciones',
        description: 'Animaciones únicas en el servidor'
      },
      changelogs: {
        title: 'Historia de los cambios',
        description: 'Historial de cambios en el proyecto'
      }
    },
    search: {
      title: 'Categorías generales',
      description: 'Aquí puedes encontrar cualquier información sobre el servidor Majestic y sus sistemas'
    },
    categories: {
      collapseList: 'Colapsar lista',
      expandList: 'Ampliar la lista'
    },
    online: {
      players: '{count} en línea',
      offline: 'Apagado'
    }
  },
  errorPage: {
    title: 'Página no encontrada',
    description: 'Puede que tengas un error tipográfico en la dirección de la página, o simplemente no existe :(',
    backToHome: 'Ir a la página principal'
  },
  bizPage: {
    meta: {
      title: 'GTA5 RP Negocios | Majestic Wiki',
      description: 'Catálogo completo de comercios del servidor Majestic RP en GTA5 RP. Tiendas 24/7, gasolineras, armerías, concesionarios de coches y otros negocios. Infórmate sobre la rentabilidad, los requisitos y cómo comprar cada negocio.',
      custom: {
        title: '{type} | GTA5 RP Negocios | Majestic Wiki',
        description: 'Información detallada sobre {type} en el servidor Majestic RP en GTA5 RP. Características, rentabilidad, requisitos y cómo comprar un negocio.'
      }
    },
    title: 'Cuadro de empresas',
    search: 'Búsqueda de empresas...',
    groceryStore: 'Tienda 24/7',
    gasStation: 'Gasolinera',
    atm: 'Cajero automático',
    gunshop: 'Armería',
    clothingStore: 'Tienda de ropa',
    autoShop: 'Salón del automóvil',
    tattoo: 'Salón de tatuajes',
    lsc: 'Salón de tuneo',
    barbershop: 'Barbería',
    carWash: 'Lavado de coches',
    viewOnMap: 'Ver en el mapa',
    mapPosition: 'Ubicación',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{negocios} few{negocios} many{negocios} other{negocios}}',
    noName: 'Empresa #{id}'
  },
  clothesPage: {
    meta: {
      title: 'Ropa RP GTA5 | Majestic Wiki',
      description: 'Catálogo completo de ropa del servidor Majestic RP en GTA5 RP. Ropa de hombre y mujer, accesorios, mochilas, parches, sombreros y otros artículos de vestuario. Descubre cómo conseguir y cuánto cuesta cada prenda.',
      custom: {
        title: '{type} | Ropa RP de GTA5 | Majestic Wiki',
        description: 'Catálogo {type} en el servidor Majestic RP en GTA5 RP. Información detallada sobre cómo conseguir, usar y el coste de los artículos de ropa.'
      }
    },
    titleMale: 'Tabla de ropa de hombre',
    titleFemale: 'Tabla de ropa de mujer',
    search: 'Búsqueda de ropa...',
    top: 'Parte superior',
    legs: 'Parte inferior',
    shoes: 'Calzado',
    watch: 'Horas',
    mask: 'Máscaras',
    decal: 'Galones',
    accessory: 'Accesorios',
    head: 'Sombreros',
    bag: 'Mochilas',
    glasses: 'Puntos',
    ear: 'Orejas',
    gloves: 'Guantes',
    undershirt: 'Camisetas',
    noName: 'Conjunto de ropa #{id}',
    drawable: 'Dibujable',
    texture: 'Textura',
    male: 'Hombre',
    female: 'Mujer',
    donate: 'Moneda Majestic',
    battlePass: 'Pase de Batalla',
    case: 'Caso',
    unique: 'Único',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{ropa} few{ropa} many{ropa} other{ropa}}'
  },
  itemsPage: {
    meta: {
      title: 'Objetos RP de GTA5 | Majestic Wiki',
      description: 'Catálogo completo de objetos del servidor Majestic RP en GTA5 RP. Antigüedades, pescado, mineral, madera, cajas, materiales y otros objetos. Infórmate sobre cómo obtener, usar y el coste de cada objeto.',
      custom: {
        title: '{type} | Objetos RP de GTA5 | Majestic Wiki',
        description: 'Catálogo {type} en el servidor Majestic RP en GTA5 RP. Información detallada sobre cómo conseguir, utilizar y coste de los objetos.'
      }
    },
    title: 'Tabla de materias',
    search: 'Encontrar objetos...',
    antique: 'Antigüedades',
    fish: 'Pescado',
    ore: 'Mineral',
    wood: 'Madera',
    box: 'Cajas',
    material: 'Materiales',
    event: 'Evento',
    food: 'Productos',
    alcohol: 'Alcohol',
    illegal: 'Ilegal',
    medical: 'Medicina',
    equipment: 'Equipo',
    ammunition: 'Munición',
    tool: 'Herramientas',
    unique: 'Único',
    vehicle: 'Piezas de vehículos',
    others: 'Varios',
    default: 'Estándar',
    battlePass: 'Pase de Batalla',
    case: 'Caso',
    unique: 'Único',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{artículo} few{artículo} many{artículos} other{artículos}}',
    noName: 'Asunto #{id}'
  },
  mapPage: {
    meta: {
      title: 'GTA5 RP Map | Majestic Wiki',
      description: 'Mapa interactivo del servidor Majestic RP en GTA5 RP. Explora lugares, encuentra tesoros, animales, teléfonos y basura. Mapa detallado del mundo con la posibilidad de añadir etiquetas y zonas. ¡Todos los lugares y puntos de interés importantes en un solo recurso!',
      custom: {
        title: '{type} | Mapa RP de GTA5 | Majestic Wiki',
        description: 'Un mapa detallado de {type} en el servidor Majestic RP de GTA5 RP. Explora lugares, encuentra puntos importantes y crea tus propios marcadores. ¡Información completa sobre la ubicación de los objetos en el mapa!'
      }
    },
    dataLoading: 'Carga de datos...',
    defaultPoints: 'Etiquetas estándar',
    animalLayer: 'Mapa de animales',
    rubbishLayer: 'Un mapa de los cubos de basura',
    telephoneLayer: 'Mapa telefónico',
    treasureLayer: 'Mapa del tesoro',
    defaultLayer: 'Mapa del mundo',
    allLayers: 'Capas del mapa',
    backToHome: 'Volver al inicio',
    zoomIn: 'Acércate a',
    zoomOut: 'Tira hacia atrás',
    point: 'Punto',
    polygon: 'Zona',
    addPoint: 'Añade un punto',
    addPolygon: 'Añadir una zona',
    copyCoordinates: 'Copiar coordenadas',
    savePointPosition: 'Guardar posición',
    editPointPosition: 'Cambiar posición',
    layerNamePlaceholder: 'Nombre de la zona',
    layerDescriptionPlaceholder: 'Descripción de la zona'
  },
  serversPage: {
    meta: {
      title: 'Servidores RP online de GTA5 | Majestic Wiki',
      description: 'Estadísticas actuales de jugadores en línea en los servidores Majestic RP en GTA5 RP. Online actual, picos, colas y gráficos de actividad. ¡Únete al proyecto de rol más popular!'
    },
    dataLoading: 'Carga de datos...',
    title: 'Servidores en línea',
    online: '{count} en línea',
    inQueue: '{count} en la cola',
    peakOnline: '{count} pico',
    currentOnline: 'actual en línea',
    peakOnline24h: 'pico hoy',
    peakOnlineAllTime: 'máximo histórico',
    peakOnlineTooltip: '{percent}% respecto al día anterior',
    techWorks: 'Trabajo técnico',
    forBeginners: 'Para principiantes',
    experience: '{multiplier}Experiencia x',
    newServer: 'Nuevo',
    seasonPassMultiplier: '{multiplier}Saltar experiencia x',
    combinedView: 'General',
    separateView: 'Separar'
  },
  research: {
    placeholder: 'Busca información en la web...',
    skin: 'Piel',
    vehicle: 'Transporte',
    realty: 'Inmueble',
    biz: 'Negocio',
    clothing: 'Ropa',
    item: 'Objeto',
    post: 'El puesto'
  },
  userModal: {
    login: 'Entrar',
    signout: 'Salir',
    register: 'Registrarse',
    registerEnd: 'Registrarse',
    restore: 'Restauración',
    restoreEnd: 'Recuperar',
    avatarDelete: 'Borrar avatar',
    headingUpdate: 'Modificar el usuario #{id}',
    postAdd: 'Publicar puestos',
    postUpdate: 'Editar puestos',
    postDelete: 'Borrar mensajes',
    vehicleAdd: 'Añadir transporte',
    vehicleUpdate: 'Editar el transporte',
    vehicleDelete: 'Eliminación del transporte',
    realtyAdd: 'Añadir una propiedad',
    realtyUpdate: 'Cambio inmobiliario',
    realtyDelete: 'Eliminación de bienes inmuebles',
    bizAdd: 'Añadir una empresa',
    bizUpdate: 'Cambio empresarial',
    bizDelete: 'Supresión de una empresa',
    mapItemAdd: 'Añadir elementos al mapa',
    mapItemUpdate: 'Cambiar elementos del mapa',
    mapItemDelete: 'Borrar elementos del mapa',
    clothingAdd: 'Añadir ropa',
    clothingUpdate: 'Cambio de ropa',
    clothingDelete: 'Quitarse la ropa',
    skinAdd: 'Añadir pieles',
    skinUpdate: 'Cambio de pieles',
    skinDelete: 'Quitar pieles',
    itemAdd: 'Añadir elementos',
    itemUpdate: 'Objetos cambiantes',
    itemDelete: 'Retirada de objetos',
    fileUpload: 'Subir imágenes',
    viewPrivate: 'Ver datos privados',
    doMagic: 'Acceso a funciones mágicas',
    animationAdd: 'Añadir animaciones',
    animationUpdate: 'Cambio de animaciones',
    animationDelete: 'Borrar animaciones',
    categoryAdd: 'Añadir categorías',
    categoryUpdate: 'Cambio de categorías',
    categoryDelete: 'Borrar categorías',
    mapLayerAdd: 'Añadir capas de mapa',
    mapLayerUpdate: 'Cambiar las capas del mapa',
    mapLayerDelete: 'Borrar capas del mapa',
    post: 'El puesto',
    skin: 'Piel',
    biz: 'Negocio',
    clothing: 'Ropa',
    item: 'Objeto',
    vehicle: 'Transporte',
    animation: 'Animación',
    category: 'Categoría',
    realty: 'Inmueble',
    mapLayer: 'Capa de mapa',
    changelog: 'Registro de cambios'
  },
  restoreModal: {
    title: 'Restaurar cuenta',
    newPassword: 'Nueva contraseña',
    newPasswordRepeat: 'Repite la nueva contraseña',
    passwordConfirm: 'Cambiar contraseña',
    passwordError: 'Las contraseñas no coinciden'
  },
  bizModal: {
    titleUpdate: 'Cambiar de empresa #{id}',
    titleAdd: 'Añadir una nueva empresa'
  },
  categoryModal: {
    titleUpdate: 'Cambiar la categoría #{id}',
    titleAdd: 'Añadir una nueva categoría'
  },
  mapLayerModal: {
    titleUpdate: '#{id}Cambiar la capa del mapa',
    titleAdd: 'Añadir una nueva capa de mapa'
  },
  clothingModal: {
    titleUpdate: 'Cambio de ropa #{id}',
    titleAdd: 'Añadir ropa nueva',
    itemAdd: 'Añadir color',
    itemGenerate: 'Generar todos los colores'
  },
  itemModal: {
    titleUpdate: 'Cambiando de tema #{id}',
    titleAdd: 'Añadir un nuevo elemento'
  },
  realtyModal: {
    titleUpdate: 'Cambio inmobiliario #{id}',
    titleAdd: 'Añadir una nueva propiedad'
  },
  skinModal: {
    titleUpdate: 'Cambiar la piel #{id}',
    titleAdd: 'Añadir una nueva piel'
  },
  vehicleModal: {
    titleUpdate: 'Cambiar el transporte #{id}',
    titleAdd: 'Añadir nuevo transporte',
    titleDefault: 'Información sobre {name}',
    itemAdd: 'Añade un deflector'
  },
  mapItemModal: {
    titleUpdate: 'Modificar un elemento del mapa',
    titleAdd: 'Añadir un nuevo elemento de mapa',
    titleDelete: 'Borrar un elemento del mapa',
    descriptionDelete: '¿Estás seguro de que quieres eliminar el objeto del mapa? Esta acción no se puede cancelar.'
  },
  animationModal: {
    titleUpdate: 'Cambiar la animación #{id}',
    titleAdd: 'Añadir una nueva animación'
  }
};