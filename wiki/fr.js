export default {
  global: {
    all: 'Tous',
    back: 'Retour',
    forward: 'Allons-y',
    paging: 'Page {page} of {maxPage}',
    close: '<PERSON><PERSON><PERSON>',
    clear: '<PERSON>',
    save: 'Sauvegarde',
    delete: 'Supprimer',
    deleteYes: 'Es-tu sûr de l\'enlèvement ?',
    cancel: 'Rejet<PERSON>',
    publish: 'Pub<PERSON>',
    edit: 'É<PERSON>er',
    share: 'Partager',
    add: 'Ajoute.',
    active: 'Actif',
    deleted: 'Supprimé',
    blocked: 'Bloqué',
    learnMore: 'En savoir plus',
    panel: 'Panneau',
    theme: 'Thème',
    language: 'Langue',
    user: 'Utilisateur',
    confirm: 'Accusé de réception',
    restore: 'Restauration',
    yes: 'Oui',
    no: 'Non',
    new: 'Nouveau',
    preview: 'Aperçu',
    sortPriceDesc: 'Par ordre décroissant de prix',
    sortPriceAsc: 'Par ordre croissant de prix',
    sortPriceMinMax: 'Gamme de prix',
    sortDateDesc: 'Par ordre décroissant de date',
    sortDateAsc: 'Par ordre croissant de date',
    noSellPrice: 'Pas à l\'achat',
    noBuyPrice: 'Pas à vendre',
    noData: {
      title: 'Rien n\'a été trouvé',
      description: 'Essaie de modifier ou de simplifier ta requête de recherche'
    },
    battlePass: {
      summer: 'Passeport de combat pour l\'été',
      winter: 'Passeport de combat d\'hiver'
    },
    case: {
      default: 'Boîtier standard',
      vehicles: 'Mallette de voiture',
      halloween: 'Mallette d\'Halloween',
      spring: 'Porte-documents de printemps',
      autumn: 'Porte-documents d\'automne',
      summer: 'Cas d\'été',
      summerExtra: 'Un cas particulier en été',
      summerSkins: 'Cas de peau d\'été',
      summerVehicles: 'Mallette de transport pour l\'été',
      winter: 'Cas d\'hiver',
      winterExtra: 'Un cas particulier en hiver',
      winterSkins: 'Mallette d\'hiver avec peaux',
      winterVehicles: 'Mallette de transport pour l\'hiver',
      toys: 'Coffre à jouets'
    },
    subscription: { premium: 'Abonnement Premium' },
    copy: 'Copie'
  },
  footer: { disclaimer: 'Toutes les informations figurant sur le site Internet sont données à titre indicatif et ne constituent pas une offre publique.' },
  footer: { disclaimer: 'Toutes les informations figurant sur le site Internet sont données à titre indicatif et ne constituent pas une offre publique.' },
  editor: {
    placeholder: 'Écrivons un article génial !',
    text: 'Paragraphe',
    heading: 'Légende',
    warning: 'Note',
    quote: 'Citation',
    code: 'Code',
    delimiter: 'Séparateur',
    link: 'Référence',
    linkPlaceholder: 'Saisis le lien',
    marker: 'Marqueur',
    bold: 'Gras',
    italic: 'Italique',
    inlineCode: 'Monospaced',
    underline: 'Souligné',
    copytext: 'Copiable',
    log: 'Logue',
    map: {
      name: 'Carte',
      params: 'Saisis les paramètres de la carte',
      caption: 'Saisis une description de la carte'
    },
    table: {
      name: 'Tableau',
      addColumnLeft: 'Ajoute une colonne à gauche',
      addColumnRight: 'Ajoute une colonne à droite',
      deleteColumn: 'Supprimer la colonne',
      addRowAbove: 'Ajoute une ligne en haut',
      addRowBelow: 'Ajoute une ligne en bas',
      deleteRow: 'Supprimer la chaîne de caractères',
      withHeadings: 'Avec les gros titres',
      withoutHeadings: 'Pas de titres'
    },
    image: {
      name: 'Image',
      withBorder: 'Avec cadre',
      stretchImage: 'Étire l\'image',
      withBackground: 'Avec l\'arrière-plan',
      selectImage: 'Sélectionne une image',
      uploadError: 'Le chargement de l\'image a échoué, essaie-en une autre.',
      caption: 'Description de l\'image',
      withCaption: 'Avec une description'
    },
    carousel: {
      name: 'Grille d\'images',
      caption: 'Description de l\'image',
      addImage: 'Sélectionne une image'
    },
    list: {
      name: 'Liste',
      ordered: 'Numéroté',
      unordered: 'Non numéroté',
      checklist: 'Liste de contrôle'
    },
    header: {
      heading1: 'Titre H1',
      heading2: 'Titre H2',
      heading3: 'Titre H3',
      heading4: 'En-tête H4'
    },
    embed: { caption: 'Lien Description' },
    convertTo: 'Convertir en',
    moveUp: 'Avance',
    moveDown: 'Descends',
    anchor: 'Ancre',
    clickToTune: 'Appuie sur pour configurer',
    dragToMove: 'ou glisser-déposer',
    filter: 'Recherche',
    nothingFound: 'Rien n\'a été trouvé'
  },
  alerts: {
    loginSuccess: 'Connexion réussie',
    loginError: 'Erreur lors de l\'autorisation',
    registerSuccess: 'Inscription réussie sur le site web',
    registerError: 'Erreur d\'enregistrement',
    restoreSuccess: 'Les instructions de récupération ont été envoyées à ta boîte aux lettres',
    restoreError: 'Erreur lors de la restauration de l\'accès au compte',
    signoutSuccess: 'Déconnexion réussie',
    postLoadError: 'Erreur lors du téléchargement d\'un message',
    postShareSuccess: 'Lien vers l\'article copié dans le presse-papiers',
    copyCommandSuccess: 'La commande est copiée dans le presse-papiers',
    restoreAccountSuccess: 'La récupération du compte a réussi',
    restoreAccountError: 'Erreur lors de la restauration de l\'accès au compte',
    verificateAccountSuccess: 'Boîte aux lettres confirmée avec succès',
    verificateAccountError: 'Erreur de confirmation de la boîte aux lettres',
    dataUpdateSuccess: 'Mise à jour réussie des informations',
    dataUpdateError: 'Erreur lors de la mise à jour des informations',
    dataDeleteSuccess: 'Suppression réussie des informations',
    dataDeleteError: 'Erreur lors de la suppression d\'informations',
    dataAddSuccess: 'Ajout réussi d\'informations',
    dataAddError: 'Erreur lors de l\'ajout d\'informations',
    mapMoveInfo: 'Déplace le point à une autre position et enregistre',
    mapCoordinatesCopySuccess: 'Les coordonnées ont été copiées avec succès',
    copyTextSuccess: 'Texte copié avec succès'
  },
  mail: {
    verificate: {
      subject: 'Confirmation de l\'adresse e-mail',
      title: 'Confirmation',
      description: 'Pour vérifier et confirmer les droits de ton compte, tu dois cliquer sur le bouton ci-dessous. Le lien est actif pendant 24 heures.'
    },
    restore: {
      subject: 'Rétablis l\'accès au compte',
      title: 'Restauration',
      description: 'Pour rétablir l\'accès à ton compte, tu dois cliquer sur le bouton ci-dessous. Le lien est actif pendant 24 heures.'
    },
    restoreSuccess: {
      subject: 'Restauration réussie de l\'accès au compte',
      title: 'Récupération réussie',
      description: 'Tu as bien changé ton mot de passe sur le site. Si ce n\'est pas le cas, change ton mot de passe immédiatement !'
    },
    verificateSuccess: {
      subject: 'Restauration réussie de l\'accès au compte',
      title: 'Confirmation réussie',
      description: 'Tu as confirmé avec succès ta boîte aux lettres, bienvenue !'
    },
    caption: 'L\'email est une notification et est généré automatiquement, merci de ne pas y répondre.'
  },
  fields: {
    price: 'Coût',
    buyPrice: 'Coût d\'achat',
    sellPrice: 'Coût de la vente',
    mcPrice: 'Coût d\'achat en MC',
    money: 'Monnaie',
    coin: 'Majestic Coin',
    gift: 'Un cadeau',
    default: 'Standard',
    unique: 'Unique',
    event: 'Événement',
    battlePass: 'Battle Pass',
    lootbox: 'Cas',
    capacityKg: 'Capacité',
    email: 'Courrier',
    password: 'Mot de passe',
    username: 'Surnom',
    weightKg: 'kg',
    speed: 'La vitesse',
    maxSpeed: 'Vitesse maximale',
    maxSpeedFT: 'Vitesse maximale (FT)',
    accelerationTo100: 'Accélération jusqu\'à 100 km/h',
    speedSystem: 'km/h',
    timeSystem: 'sec',
    uploadDescription: 'Touche ou fais glisser une image sur une zone',
    placeholder: 'Saisis une valeur dans le champ',
    imagePlaceholder: 'Saisis un lien vers l\'image',
    selectPlaceholder: 'Sélectionne une valeur dans la liste',
    videoPlaceholder: 'Saisis un lien vers la vidéo',
    soundPlaceholder: 'Saisis un lien vers l\'audio',
    id: 'ID',
    serial: 'Numéro de série',
    kind: 'Type',
    category: 'Catégorie',
    name: 'Titre',
    view: 'Voir',
    layer: 'Couche',
    icon: 'Icône',
    circle: 'Point',
    polygon: 'Polygone',
    emoji: 'Emoji',
    description: 'Description',
    gender: 'Genre',
    value: 'Type de valeur',
    valueName: 'Description du prix supplémentaire',
    visibility: 'Accessibilité',
    visibilityPrivate: 'Accès restreint',
    visibilityPublic: 'Visibilité pour tous',
    activityHidden: 'Caché de l\'accès du public',
    activityVisible: 'Disponible pour tous',
    garageSlots: 'Fentes de garage',
    maxTenants: 'Nombre de résidents',
    gasoline: 'Carburant',
    brand: 'Marque',
    model: 'Modèle',
    trunk: 'Botte',
    tradable: 'Transférabilité',
    buyable: 'Option d\'achat',
    upgradesPriceFT: 'Améliorations disponibles (FT)',
    upgradesEngine: 'Moteur',
    upgradesBreaks: 'Freins',
    upgradesSuspension: 'Suspension',
    upgradesTurbo: 'Turbo',
    upgradesTransmission: 'Boîte',
    paintingsMainPrice: 'Peinture de base',
    paintingsSubPrice: 'Peinture supplémentaire',
    paintingsOtherPrice: 'Autre peinture',
    paintingsBrightMetallic: 'Métallique brillant',
    paintingsMetallic: 'Métallique',
    paintingsRichMetallic: 'Riche en métal',
    paintingsDarkMetallic: 'Métallique foncé',
    paintingsMatte: 'Mat',
    paintingsMatteMetallic: 'Métal mat',
    paintingsSatin: 'Satin',
    paintingsMetal: 'Métal',
    paintingsShadowChrome: 'Ombre chromée',
    paintingsPureChrome: 'Chrome pur',
    paintingsPearl: 'Teinte nacrée',
    paintingsWheels: 'Couleur du disque',
    paintingsDetails: 'Couleur des pièces',
    paintingsAdditionalDetails: 'Couleur supplémentaire des pièces',
    paintingsLights: 'Lampe frontale',
    paintingsSmoke: 'La couleur de la fumée des pneus',
    paintingsNeon: 'Couleur du néon',
    total: 'Total',
    language: 'Langue',
    radius: 'Rayon',
    color: 'Couleur',
    colorPlaceholder: 'Choisis une couleur',
    opacity: 'Transparence',
    alias: 'Pseudonyme',
    aliases: 'Alias',
    looped: 'En boucle',
    battlePassId: 'ID du Battle Pass',
    gasolineCapacityLiter: 'л',
    gasolineCapacity: 'Capacité du réservoir',
    source: 'Source',
    sources: 'Sources',
    priceDescription: 'Description des coûts',
    layerName: 'Nom de la couche',
    layerNamePlaceholder: 'Saisis un nom pour la couche',
    layerDescription: 'Description de la couche',
    layerDescriptionPlaceholder: 'Saisis une description pour la couche',
    layerColor: 'Couleur du calque',
    layerColorPlaceholder: 'Choisis une couleur',
    layerOpacity: 'Transparence des couches',
    zIndexOffset: 'Niveau Z-index',
    zIndexOffsetPlaceholder: 'Saisis la valeur du niveau d\'indice Z',
    comment: 'Commentaire'
  },
  vehiclesPage: {
    meta: {
      title: 'GTA5 RP Transport et voitures | Majestic Wiki',
      description: 'Catalogue complet des véhicules sur le serveur Majestic RP dans GTA5 RP. Caractéristiques détaillées des voitures, motos, hélicoptères, avions et bateaux. Découvre les prix, la vitesse, la maniabilité, la capacité du coffre et d\'autres paramètres. Majestic Motors, voitures, camions, hélicoptères, bateaux, motos, vélos - tout pour GTA5 RP !',
      custom: {
        title: '{type} | GTA5 RP Transport | Majestic Wiki',
        description: 'Catalogue détaillé {type} sur le serveur Majestic RP dans GTA5 RP. Caractéristiques, prix, vitesse, maniabilité et autres paramètres. Toutes les informations sur les transports pour GTA5 RP !'
      }
    },
    default: 'Standard',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Cas',
    unique: 'Unique',
    title: 'Table de transport',
    search: 'Recherche de transport...',
    irl: 'Majestic Motors',
    car: 'Voitures',
    freight: 'Camions',
    helicopter: 'Hélicoptères',
    plane: 'Самолеты',
    boat: 'Bateaux',
    motorbike: 'Motos',
    bike: 'Bicyclettes',
    trailer: 'remorques',
    military: 'Militaire',
    special: 'Spécialité',
    priceDisclaimer: 'Le coût final peut varier en raison d\'une commission dans le LSC sur ton serveur',
    basicInformation: 'Informations de base',
    additionalInformation: 'Informations complémentaires',
    compare: 'Comparer',
    compareLimit: 'Un maximum de 5 CT peuvent être comparés',
    pagingText: 'Trouvé{N, plural, few{à propos de} many{à propos de}} {N} {N, plural, one{transport} few{transport} many{transport} other{transport}}'
  },
  vehiclePage: {
    meta: {
      title: 'GTA5 RP Transport | Majestic Wiki',
      description: 'Informations détaillées sur les transports sur le serveur Majestic RP dans GTA5 RP. Caractéristiques, prix, améliorations et peinture. Découvre la vitesse de pointe, l\'accélération, la capacité de coffre, le coût des améliorations et d\'autres paramètres de chaque véhicule.',
      custom: {
        title: '{name} | GTA5 RP Transport | Majestic Wiki',
        description: 'Informations détaillées sur {name} sur le serveur Majestic RP dans GTA5 RP. Caractéristiques, prix, améliorations et travaux de peinture. Découvre la vitesse de pointe, l\'accélération, la capacité de chargement, le coût des améliorations et d\'autres paramètres.'
      }
    },
    backToTable: 'Retour à la liste',
    labelsUniqueId: 'ID unique',
    labelsPrice: 'Coût',
    labelsBuyPrice: 'Coût d\'achat',
    labelsSellPrice: 'Coût de la vente',
    labelsMaxSpeed: 'Vitesse maximale',
    labelsMaxSpeedFT: 'Vitesse maximale (FT)',
    labelsAccelerationTo100: 'Accélération jusqu\'à 100 km/h',
    labelsTradable: 'Transférabilité',
    labelsTrunk: 'Capacité de la botte',
    labelsGasolineCapacity: 'Capacité du réservoir',
    labelsGasolineType: 'Type de carburant',
    upgradesTitle: 'Amélioration des transports',
    upgradesDescription: 'Coût des mises à niveau jusqu\'au niveau maximum',
    upgradesEngine: 'Moteur',
    upgradesEngineDescription: 'Débloque le véritable potentiel des transports',
    upgradesBreaks: 'Freins',
    upgradesBreaksDescription: 'Contrôle total de la vitesse sur n\'importe quelle route',
    upgradesTurbo: 'Turbo',
    upgradesTurboDescription: 'Accélération explosive à tout moment',
    upgradesTransmission: 'Boîte',
    upgradesTransmissionDescription: 'Passage de tous les rapports à la vitesse de l\'éclair',
    upgradesSuspension: 'Suspension',
    upgradesSuspensionDescription: 'Un confort parfait à la bonne hauteur',
    upgradesTotal: 'Coût des améliorations',
    upgradesDisclaimer: 'Le coût final peut varier en raison d\'une commission dans le LSC sur ton serveur',
    colorsTitle: 'Peinture de véhicules',
    colorsDescription: 'Types disponibles et types de peinture avec coût',
    paintingsMainPrice: 'Peinture de base',
    paintingsSubPrice: 'Peinture supplémentaire',
    paintingsOtherPrice: 'Autre peinture',
    paintingsBrightMetallic: 'Métallique brillant',
    paintingsMetallic: 'Métallique',
    paintingsRichMetallic: 'Riche en métal',
    paintingsDarkMetallic: 'Métallique foncé',
    paintingsMatte: 'Mat',
    paintingsMatteMetallic: 'Métal mat',
    paintingsSatin: 'Satin',
    paintingsMetal: 'Métal',
    paintingsShadowChrome: 'Ombre chromée',
    paintingsPureChrome: 'Chrome pur',
    paintingsPearl: 'Teinte nacrée',
    paintingsWheels: 'Couleur du disque',
    paintingsDetails: 'Couleur des pièces',
    paintingsAdditionalDetails: 'Couleur supplémentaire des pièces',
    paintingsLights: 'Lampe frontale',
    paintingsSmoke: 'La couleur de la fumée des pneus',
    paintingsNeon: 'Couleur du néon',
    paintingsTotal: 'Coût final de la peinture',
    shareLink: 'Partage le lien vers les transports',
    shareLinkCopied: 'Le lien de transport a été copié avec succès',
    addToCompare: 'Ajouter à la comparaison',
    addedToCompare: 'Transport a été ajouté au comparatif',
    goToCompare: 'Comparer',
    removeFromCompare: 'Retirer de la comparaison',
    removedFromCompare: 'Le transport a été supprimé de la comparaison'
  },
  vehiclesComparePage: {
    meta: {
      title: 'GTA5 RP Comparaison des transports | Majestic Wiki',
      description: 'Compare les caractéristiques et les prix de différents véhicules sur le serveur Majestic RP dans GTA5 RP. Découvre le véhicule qui correspond le mieux à tes besoins ! Compare la vitesse, le prix, la capacité et d\'autres paramètres.'
    },
    name: 'Titre',
    type: 'Type',
    brand: 'Marque',
    kits: 'Paquets',
    model: 'Modèle',
    trunkCapacity: 'Capacité de la botte',
    maxSpeed: 'Vitesse maximale',
    maxSpeedFT: 'Vitesse maximale (FT)',
    price: 'Coût',
    donatePrice: 'Coût dans Majestic Coin',
    sellPrice: 'Coût de la vente',
    sellPriceBeforeTax: 'Coût de la vente (avant impôts)',
    sellPriceWithDiscount: 'Coût de la vente (escompté)',
    accelerationTo100: 'Temps d\'accélération jusqu\'à 100 km/h',
    gasolineCapacity: 'Capacité du réservoir',
    gasolineType: 'Type de carburant',
    isTradable: 'Transférabilité',
    isBuyable: 'Option d\'achat',
    createdAt: 'Date de comparution',
    gasolineCapacityUnit: 'л',
    maxSpeedUnit: 'km/h',
    maxSpeedFTUnit: 'km/h',
    priceUnit: '₽',
    accelerationTo100Unit: 'sec',
    trunkCapacityUnit: 'kg',
    showOnlyDifferent: 'Ne montre que ceux qui sont différents',
    showAll: 'Afficher tout',
    share: 'Partager une comparaison',
    linkCopied: 'Le lien de comparaison a été copié avec succès',
    openVehicle: 'Ouvre la page des transports',
    deleteVehicle: 'Retire un véhicule de la comparaison',
    noDifferences: 'Il n\'y a pas de distinction'
  },
  skinsPage: {
    meta: {
      title: 'GTA5 RP Weapon Skins | Majestic Wiki',
      description: 'Catalogue complet des skins d\'armes sur le serveur Majestic RP dans GTA5 RP. Découvre comment obtenir, le coût et les caractéristiques de chaque skin pour différents types d\'armes.',
      custom: {
        title: 'Skins sur {type} | GTA5 RP | Majestic Wiki',
        description: 'Catalogue de skins pour {type} sur le serveur Majestic RP dans GTA5 RP. Informations détaillées sur la façon d\'obtenir, le coût et les caractéristiques des skins.'
      }
    },
    title: 'Tableau des peaux',
    search: 'Recherche de peau...',
    apPistol: 'Pistolet perforant',
    assaultRifle: 'Fusil d\'assaut',
    assaultRifleMk2: 'Fusil d\'assaut Mk2',
    bullpupRifle: 'Fusil Bullpup',
    carbineRifle: 'Carabine',
    carbineRifleMk2: 'Carabine Mk2',
    combatPDW: 'Combat PDW',
    gusenberg: 'Pistolet mitrailleur Thompson.',
    heavyPistol: 'Arme lourde',
    heavyShotgun: 'Fusil de chasse lourd',
    heavySniper: 'Fusil de sniper lourd.',
    heavySniperMk2: 'Fusil de sniper lourd Mk2',
    lightvest: 'Gilet pare-balles',
    machinePistol: 'Petit PP',
    marksmanPistol: 'Pistolet de tir',
    microSMG: 'Micro SMG',
    militaryRifle: 'Fusil militaire',
    revolver: 'Revolver',
    smgMk2: 'SMG Mk2',
    specialCarbine: 'Mousqueton spécial',
    vintagePistol: 'Pistolet d\'époque',
    combatMgMk2: 'Pistolet mitrailleur Mk2',
    heavyRifle: 'Fusil lourd',
    tacticalRifle: 'Fusil tactique',
    tecPistol: 'SMG tactique',
    noName: 'Peau #{id}',
    battlePass: 'Battle Pass',
    case: 'Cas',
    unique: 'Unique',
    pagingText: 'Trouvé{N, plural, few{à propos de} many{à propos de}} {N} {N, plural, one{peau} few{peau} many{peaux} other{peaux}}'
  },
  animationsPage: {
    meta: {
      title: 'GTA5 RP Animations | Majestic Wiki',
      description: 'Catalogue complet des animations sur le serveur Majestic RP dans GTA5 RP. Tous les types d\'animations : actions, poses, danses et animations exclusives. Découvre comment obtenir et le coût de chaque animation.',
      custom: {
        title: '{type} | GTA5 RP Animations | Majestic Wiki',
        description: 'Catalogue {type} animations sur le serveur Majestic RP dans GTA5 RP. Informations détaillées sur l\'obtention, le coût et l\'utilisation des animations.'
      }
    },
    title: 'Tableau d\'animation',
    search: 'Recherche d\'animation...',
    default: 'Standard',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Cas',
    unique: 'Unique',
    action: 'Actions',
    pose: 'Postures',
    positive: 'Positif',
    negative: 'Négatif',
    dances: 'Danser',
    etc: 'Autre',
    exclusive: 'Exclusif',
    noName: 'Animation #{id}',
    pagingText: 'Trouvé{N, plural, one{e} other{}} {N} {N, plural, one{animation} few{animations} many{animations} other{animations}}'
  },
  realtyPage: {
    meta: {
      title: 'GTA5 RP Real Estate | Majestic Wiki',
      description: 'Catalogue complet des biens immobiliers sur le serveur Majestic RP dans GTA5 RP. Maisons, appartements, bureaux et entrepôts avec caractéristiques détaillées, prix et localisation. Découvre le nombre de locataires, les places de garage et d\'autres paramètres.',
      custom: {
        title: '{type} | GTA5 RP Immobilier | Majestic Wiki',
        description: 'Catalogue {type} sur le serveur Majestic RP dans GTA5 RP. Informations détaillées sur les caractéristiques, les prix, l\'emplacement et les fonctionnalités de la propriété.'
      }
    },
    title: 'Tableau des propriétés',
    search: 'Recherche de propriété...',
    majesticMotors: 'Majestic Motors',
    house: 'A la maison',
    apartment: 'Les appartements',
    office: 'Bureaux',
    warehouse: 'Entrepôts',
    tenantsFor: '{N} {N, plural, one{locataire} few{locataire} many{locataires} other{locataires}}',
    garageSlotsFor: '{N} {N, plural, one{espace garage} few{espace garage} many{espace garage} other{espace garage}}',
    viewOnMap: 'Voir sur la carte',
    mapPosition: 'Lieu de travail',
    pagingText: 'Trouvé{N, plural, few{à propos} many{à propos}} {N} {N, plural, one{immobilier} few{immobilier} many{immobilier} other{immobilier}}',
    noName: 'Immobilier #{id}'
  },
  postsPage: {
    meta: {
      title: 'Modifier un article | Majestic Wiki',
      description: 'Créez et éditez des messages sur le Wiki Majestic pour GTA5 RP. Publier des informations actualisées sur les transports, les propriétés, les entreprises et d\'autres aspects du jeu.'
    },
    titlePlaceholder: 'Saisis le titre de l\'article',
    category: 'Catégorie',
    noCategory: 'Sans catégorie',
    availability: 'Accessibilité',
    allView: 'Visibilité pour tous',
    privateView: 'Accès restreint',
    relevance: 'Pertinence',
    relevanceData: 'Informations actualisées',
    outdatedData: 'Informations périmées',
    categoryPosition: 'Position de la catégorie',
    categoryPositionDescription: 'Par exemple, "50" ou "-5".',
    postDeleted: 'Poste supprimé',
    author: 'Auteur de l\'article',
    editor: 'Rédacteur d\'articles',
    contents: 'Contenu de l\'article',
    readingTime: 'Temps de lecture',
    readingTimeLength: '{N} {N, plural, one{minute} few{minutes} many{minutes} other{minutes}}',
    publishDate: 'Publication sur {date}',
    views: '{N} {N, plural, one{visualisation} few{visualisation} many{visualisation} other{visualisation}}',
    youLiked: 'Tu as aimé',
    youAndPeopleLiked: 'Tu as aussi aimé {likes}',
    peopleLiked: '{N} {N, plural, one{aime} few{aime} many{aime} other{aime}}',
    categories: 'Catégories',
    posts: 'Postes',
    foundMistake: 'Tu as trouvé une erreur dans l\'article ?',
    language: 'Langue'
  },
  panelPage: {
    users: {
      title: 'Gestion des utilisateurs | Majestic Wiki',
      heading: 'Gestion des utilisateurs',
      admin: 'Administrateur',
      user: 'Utilisateur',
      customUser: 'Utilisateur avec droits',
      search: 'Commence à taper ta requête de recherche....',
      comment: 'Commentaire',
      pagingText: 'Trouvé{N, plural, few{à propos de} many{à propos de}} {N} {N, plural, one{utilisateur} few{utilisateur} many{utilisateurs} other{utilisateurs}}'
    },
    posts: {
      title: 'Gestion des messages | Majestic Wiki',
      heading: 'Gestion des postes',
      views: '{N} {N, plural, one{vues} few{vues} many{vues} other{vues}}',
      likes: '{N} {N, plural, one{aime} few{aime} many{aime} other{aime}}'
    },
    categories: {
      title: 'Gestion des catégories | Majestic Wiki',
      heading: 'Gestion des catégories'
    },
    mapLayers: {
      title: 'Gestion des couches de cartes | Majestic Wiki',
      heading: 'Gérer les couches de la carte'
    },
    logs: {
      title: 'Audit des logs - Majestic Wiki',
      heading: 'Audit des journaux',
      search: 'Commence à taper ta requête de recherche....',
      biz: 'Entreprise',
      category: 'Catégorie',
      clothing: 'Vêtements',
      file: 'Fichier',
      item: 'Sujet',
      mapItem: 'Élément de carte',
      mapLayer: 'Couche de carte',
      post: 'Le poste',
      realty: 'Immobilier',
      skin: 'Peau',
      user: 'Utilisateur',
      vehicle: 'Transport',
      animation: 'Animation',
      actions: {
        unknown: '{username} a accompli un acte inconnu',
        bizCreate: '{username} a ajouté une entreprise {id}',
        bizUpdate: '{username} a changé d\'activité {id}',
        bizDelete: '{username} a supprimé l\'entreprise {id}',
        categoryCreate: '{username} a ajouté la catégorie {id}',
        categoryUpdate: '{username} a changé la catégorie {id}',
        categoryDelete: '{username} catégorie supprimée {id}',
        clothingCreate: '{username} ajoute des vêtements {id}',
        clothingUpdate: '{username} a changé de vêtements {id}',
        clothingDelete: '{username} a supprimé les vêtements {id}',
        fileCreate: '{username} a ajouté un fichier {id}',
        fileUpdate: '{username} a modifié le fichier {id}',
        fileDelete: '{username} a supprimé le fichier {id}',
        itemCreate: '{username} a ajouté l\'article {id}',
        itemUpdate: '{username} a changé de sujet {id}',
        itemDelete: '{username} a supprimé l\'article {id}',
        mapItemCreate: '{username} a ajouté un élément à la carte {id} à "{layer}"',
        mapItemUpdate: '{username} a changé l\'élément de la carte {id} en "{layer}".',
        mapItemDelete: '{username} a supprimé un élément de la carte {id} au profit de "{layer}"',
        mapLayerCreate: '{username} a ajouté une couche de carte {id}',
        mapLayerUpdate: '{username} a changé la couche de la carte {id}',
        mapLayerDelete: '{username} a supprimé la couche de carte {id}',
        postCreate: '{username} a ajouté un message {id}',
        postUpdate: '{username} poste modifié {id}',
        postDelete: '{username} message supprimé {id}',
        realtyCreate: '{username} a ajouté la propriété {id}',
        realtyUpdate: '{username} a changé l\'immobilier {id}',
        realtyDelete: '{username} supprimé l\'immobilier {id}',
        skinCreate: '{username} a ajouté la peau {id}',
        skinUpdate: '{username} a changé la peau {id}',
        skinDelete: '{username} a supprimé la peau {id}',
        userCreate: '{username} a ajouté l\'utilisateur {id}',
        userUpdate: '{username} a changé l\'utilisateur {id}',
        userDelete: '{username} supprimé l\'utilisateur {id}',
        vehicleCreate: '{username} ajout de transport {id}',
        vehicleUpdate: '{username} a changé le transport {id}',
        vehicleDelete: '{username} a supprimé le transport {id}',
        animationCreate: '{username} ajout de l\'animation {id}',
        animationUpdate: '{username} a changé l\'animation {id}',
        animationDelete: '{username} a supprimé l\'animation {id}',
        changelogCreate: '{username} ajout du changelog {id}',
        changelogUpdate: '{username} a changé le changelog {id}',
        changelogDelete: '{username} supprimé changelog {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: 'Paramètres',
      users: 'Utilisateurs',
      categories: 'Catégories',
      mapLayers: 'Couches de cartes',
      posts: 'Postes',
      logs: 'Journaux'
    }
  },
  changelogPage: {
    titlePlaceholder: 'Saisis le nom du changement',
    descriptionPlaceholder: 'Saisis une description du changement',
    availableAt: 'Date de publication',
    soon: 'à travers',
    ago: 'vers l\'arrière',
    fix: 'Corrigé',
    feat: 'Ajouté',
    chore: 'Modifié',
    style: 'Conception',
    refactor: 'Recyclé'
  },
  changelogsPage: {
    meta: {
      title: 'Historique des changements | Majestic Wiki',
      description: 'Historique complet des mises à jour et des modifications apportées au serveur Majestic RP. Garde un œil sur les nouvelles fonctionnalités, les améliorations du gameplay, les corrections de bugs et l\'équilibrage. Informations actualisées sur toutes les mises à jour du projet.'
    },
    title: 'Historique des changements',
    search: 'Trouver le changement...'
  },
  homePage: {
    meta: {
      title: 'Majestic Wiki | GTA5 RP Encyclopaedia',
      description: 'Majestic Wiki est une encyclopédie complète pour les joueurs de GTA5 RP. Informations sur les transports, l\'immobilier, les entreprises, les quêtes et d\'autres aspects du jeu. Des informations actualisées sur les prix, les caractéristiques et la façon de les obtenir.'
    },
    tools: {
      map: {
        title: 'Carte',
        description: 'Carte du serveur avec tous les emplacements'
      },
      vehicles: {
        title: 'Transport',
        description: 'Tous les transports disponibles sur le serveur'
      },
      realty: {
        title: 'Immobilier',
        description: 'Données pour toutes les maisons et tous les appartements'
      },
      biz: {
        title: 'Entreprises',
        description: 'Option de gain avancé'
      },
      clothesMale: {
        title: 'Vêtements pour hommes',
        description: 'Un défilé de mode pour hommes'
      },
      clothesFemale: {
        title: 'Vêtements pour femmes',
        description: 'Défilé de mode pour femmes'
      },
      skins: {
        title: 'Peaux',
        description: 'Catalogue de tous les modèles disponibles'
      },
      items: {
        title: 'Sujets',
        description: 'Éléments disponibles sur le serveur'
      },
      servers: {
        title: 'Serveurs',
        description: 'Liste de tous les serveurs Majestic RP'
      },
      animations: {
        title: 'Animations',
        description: 'Animations uniques sur le serveur'
      },
      changelogs: {
        title: 'Historique des changements',
        description: 'Historique des changements sur le projet'
      }
    },
    search: {
      title: 'Catégories générales',
      description: 'Tu trouveras ici toutes les informations sur le serveur Majestic et ses systèmes.'
    },
    categories: {
      collapseList: 'Réduire la liste',
      expandList: 'Élargir la liste'
    },
    online: {
      players: '{count} en ligne',
      offline: 'Off'
    }
  },
  errorPage: {
    title: 'Page non trouvée',
    description: 'Tu as peut-être une faute de frappe dans l\'adresse de la page, ou bien elle n\'existe tout simplement pas :(',
    backToHome: 'Aller à la page principale'
  },
  bizPage: {
    meta: {
      title: 'GTA5 RP Businesses | Majestic Wiki',
      description: 'Catalogue complet des commerces sur le serveur Majestic RP dans GTA5 RP. Magasins 24/7, stations-service, armureries, concessionnaires automobiles et autres commerces. Découvre la rentabilité, les exigences et la façon d\'acheter chaque commerce.',
      custom: {
        title: '{type} | GTA5 RP Businesses | Majestic Wiki',
        description: 'Informations détaillées sur {type} sur le serveur Majestic RP dans GTA5 RP. Caractéristiques, rentabilité, exigences et comment acheter une entreprise.'
      }
    },
    title: 'Tableau des entreprises',
    search: 'Recherche d\'entreprises...',
    groceryStore: 'Magasin 24/7',
    gasStation: 'Ravitaillement en carburant',
    atm: 'ATM',
    gunshop: 'Magasin d\'armes',
    clothingStore: 'Magasin de vêtements',
    autoShop: 'Concessionnaire automobile',
    tattoo: 'Salon de tatouage',
    lsc: 'Salon de tuning',
    barbershop: 'salon de coiffure',
    carWash: 'Lavage de voiture',
    viewOnMap: 'Voir sur la carte',
    mapPosition: 'Lieu de travail',
    pagingText: 'Trouvé{N, plural, few{à propos de} many{à propos de}} {N} {N, plural, one{entreprises} few{entreprises} many{entreprises} other{entreprises}}',
    noName: 'Entreprise #{id}'
  },
  clothesPage: {
    meta: {
      title: 'GTA5 RP vêtements | Majestic Wiki',
      description: 'Catalogue complet de vêtements sur le serveur Majestic RP dans GTA5 RP. Vêtements pour hommes et femmes, accessoires, sacs à dos, patchs, chapeaux et autres articles de garde-robe. Découvre comment obtenir et combien coûte chaque vêtement.',
      custom: {
        title: '{type} | GTA5 RP - Vêtements - Majestic Wiki',
        description: 'Catalogue {type} sur le serveur Majestic RP dans GTA5 RP. Informations détaillées sur l\'obtention, l\'utilisation et le coût des articles vestimentaires.'
      }
    },
    titleMale: 'Tableau des vêtements pour hommes',
    titleFemale: 'Tableau des vêtements pour femmes',
    search: 'Recherche de vêtements...',
    top: 'Haut',
    legs: 'Le fond',
    shoes: 'Chaussures',
    watch: 'Horloge',
    mask: 'Masques',
    decal: 'Patch',
    accessory: 'Accessoires',
    head: 'Chapeaux',
    bag: 'Sacs à dos',
    glasses: 'Lunettes',
    ear: 'Oreilles',
    gloves: 'Gants',
    undershirt: 'T-shirts',
    noName: 'Ensemble de vêtements #{id}',
    drawable: 'Dessinable',
    texture: 'Texture',
    male: 'Homme',
    female: 'Femme',
    donate: 'Majestic Coin',
    battlePass: 'Battle Pass',
    case: 'Cas',
    unique: 'Unique',
    pagingText: 'Trouvé{N, plural, few{à propos de} many{à propos de}} {N} {N, plural, one{vêtements} few{vêtements} many{vêtements} other{vêtements}}'
  },
  itemsPage: {
    meta: {
      title: 'GTA5 RP Items | Majestic Wiki',
      description: 'Catalogue complet d\'objets sur le serveur Majestic RP dans GTA5 RP. Antiquités, poissons, minerai, bois, caisses, matériaux et autres objets. Découvre comment obtenir, utiliser et le coût de chaque objet.',
      custom: {
        title: '{type} | GTA5 RP Items | Majestic Wiki',
        description: 'Catalogue {type} sur le serveur Majestic RP dans GTA5 RP. Informations détaillées sur l\'obtention, l\'utilisation et le coût des objets.'
      }
    },
    title: 'Tableau des sujets',
    search: 'Trouver des objets...',
    antique: 'Antiquités',
    fish: 'Poisson',
    ore: 'Minerai',
    wood: 'Bois',
    box: 'Boîtes',
    material: 'Matériaux',
    event: 'Événement',
    food: 'Produits',
    alcohol: 'L\'alcool',
    illegal: 'Illégal',
    medical: 'Médecine',
    equipment: 'Equipement',
    ammunition: 'Munitions',
    tool: 'Instruments',
    unique: 'Unique',
    vehicle: 'Pièces détachées',
    others: 'Divers',
    default: 'Standard',
    battlePass: 'Battle Pass',
    case: 'Cas',
    unique: 'Unique',
    pagingText: 'Trouvé{N, plural, few{à propos de} many{à propos de}} {N} {N, plural, one{article} few{article} many{articles} other{articles}}',
    noName: 'Sujet #{id}'
  },
  mapPage: {
    meta: {
      title: 'GTA5 RP Map | Majestic Wiki',
      description: 'Carte interactive du serveur Majestic RP dans GTA5 RP. Explore les lieux, trouve des trésors, des animaux, des téléphones et des déchets. Carte détaillée du monde avec la possibilité d\'ajouter des tags et des zones. Tous les lieux importants et les points d\'intérêt sur une seule ressource !',
      custom: {
        title: '{type} | GTA5 RP Map | Majestic Wiki',
        description: 'Une carte détaillée de {type} sur le serveur Majestic RP dans GTA5 RP. Explore les lieux, trouve les points importants et crée tes propres marqueurs. Informations complètes sur l\'emplacement des objets sur la carte !'
      }
    },
    dataLoading: 'Chargement des données...',
    defaultPoints: 'Étiquettes standard',
    animalLayer: 'Carte des animaux',
    rubbishLayer: 'Une carte des poubelles',
    telephoneLayer: 'Carte téléphonique',
    treasureLayer: 'Carte au trésor',
    defaultLayer: 'Carte du monde',
    allLayers: 'Couches de cartes',
    backToHome: 'Retour à l\'accueil',
    zoomIn: 'Approche',
    zoomOut: 'Tirez vers l\'arrière',
    point: 'Point',
    polygon: 'Zone',
    addPoint: 'Ajoute un point',
    addPolygon: 'Ajouter une zone',
    copyCoordinates: 'Copier les coordonnées',
    savePointPosition: 'Sauvegarde la position',
    editPointPosition: 'Changer de position',
    layerNamePlaceholder: 'Nom de la zone',
    layerDescriptionPlaceholder: 'Description de la zone'
  },
  serversPage: {
    meta: {
      title: 'Serveurs RP en ligne de GTA5 | Majestic Wiki',
      description: 'Statistiques actuelles des joueurs en ligne sur les serveurs Majestic RP dans GTA5 RP. Courant en ligne, pics, files d\'attente et graphiques d\'activité. Rejoins le projet de jeu de rôle le plus populaire !'
    },
    dataLoading: 'Chargement des données...',
    title: 'Serveurs en ligne',
    online: '{count} en ligne',
    inQueue: '{count} dans la file d\'attente',
    peakOnline: '{count} pic',
    currentOnline: 'actuel en ligne',
    peakOnline24h: 'le sommet aujourd\'hui',
    peakOnlineAllTime: 'pic historique',
    peakOnlineTooltip: '{percent}% par rapport au jour précédent',
    techWorks: 'Travail technique',
    forBeginners: 'Pour les débutants',
    experience: '{multiplier}Expérience x',
    newServer: 'Nouveau',
    seasonPassMultiplier: '{multiplier}Sauter l\'expérience x',
    combinedView: 'Généralités',
    separateView: 'Séparer'
  },
  research: {
    placeholder: 'Cherche des informations sur le site Internet...',
    skin: 'Peau',
    vehicle: 'Transport',
    realty: 'Immobilier',
    biz: 'Entreprise',
    clothing: 'Vêtements',
    item: 'Sujet',
    post: 'Le poste'
  },
  userModal: {
    login: 'S\'inscrire',
    signout: 'Sors',
    register: 'Inscription',
    registerEnd: 'S\'inscrire',
    restore: 'Restauration',
    restoreEnd: 'Restaurer',
    avatarDelete: 'Supprimer l\'avatar',
    headingUpdate: 'Modifier l\'utilisateur #{id}',
    postAdd: 'Publication d\'articles',
    postUpdate: 'Modifier les messages',
    postDelete: 'Supprimer des messages',
    vehicleAdd: 'Ajouter un transport',
    vehicleUpdate: 'Modifier le transport',
    vehicleDelete: 'Suppression du transport',
    realtyAdd: 'Ajouter une propriété',
    realtyUpdate: 'Changements dans l\'immobilier',
    realtyDelete: 'Déménagement de biens immobiliers',
    bizAdd: 'Ajouter une entreprise',
    bizUpdate: 'Changement d\'activité',
    bizDelete: 'Déménagement d\'une entreprise',
    mapItemAdd: 'Ajouter des éléments à la carte',
    mapItemUpdate: 'Changement d\'éléments sur la carte',
    mapItemDelete: 'Suppression d\'éléments sur la carte',
    clothingAdd: 'Ajouter des vêtements',
    clothingUpdate: 'Vêtements de rechange',
    clothingDelete: 'Enlèvement des vêtements',
    skinAdd: 'Ajout d\'habillages',
    skinUpdate: 'Changer d\'apparence',
    skinDelete: 'Enlever les peaux',
    itemAdd: 'Ajouter des éléments',
    itemUpdate: 'Changer d\'objet',
    itemDelete: 'Retrait des objets',
    fileUpload: 'Télécharger des images',
    viewPrivate: 'Voir les données privées',
    doMagic: 'Accès aux fonctions magiques',
    animationAdd: 'Ajouter des animations',
    animationUpdate: 'Modifier les animations',
    animationDelete: 'Suppression des animations',
    categoryAdd: 'Ajouter des catégories',
    categoryUpdate: 'Changer de catégorie',
    categoryDelete: 'Suppression de catégories',
    mapLayerAdd: 'Ajouter des couches de cartes',
    mapLayerUpdate: 'Changer les couches de la carte',
    mapLayerDelete: 'Suppression des couches de la carte',
    post: 'Le poste',
    skin: 'Peau',
    biz: 'Entreprise',
    clothing: 'Vêtements',
    item: 'Sujet',
    vehicle: 'Transport',
    animation: 'Animation',
    category: 'Catégorie',
    realty: 'Immobilier',
    mapLayer: 'Couche de carte',
    changelog: 'Changelog'
  },
  restoreModal: {
    title: 'Restauration de compte',
    newPassword: 'Nouveau mot de passe',
    newPasswordRepeat: 'Répéter le nouveau mot de passe',
    passwordConfirm: 'Modifier le mot de passe',
    passwordError: 'Les mots de passe ne correspondent pas'
  },
  bizModal: {
    titleUpdate: 'Changer l\'entreprise #{id}',
    titleAdd: 'Ajouter une nouvelle entreprise'
  },
  categoryModal: {
    titleUpdate: 'Changer la catégorie #{id}',
    titleAdd: 'Ajouter une nouvelle catégorie'
  },
  mapLayerModal: {
    titleUpdate: '#{id}Changer la couche de la carte',
    titleAdd: 'Ajouter une nouvelle couche de carte'
  },
  clothingModal: {
    titleUpdate: 'Changer de vêtements #{id}',
    titleAdd: 'Ajouter de nouveaux vêtements',
    itemAdd: 'Ajoute de la couleur',
    itemGenerate: 'Génère toutes les couleurs'
  },
  itemModal: {
    titleUpdate: 'Changer de sujet #{id}',
    titleAdd: 'Ajouter un nouvel article'
  },
  realtyModal: {
    titleUpdate: 'L\'immobilier en mutation #{id}',
    titleAdd: 'Ajouter une nouvelle propriété'
  },
  skinModal: {
    titleUpdate: 'Changer la peau #{id}',
    titleAdd: 'Ajouter une nouvelle peau'
  },
  vehicleModal: {
    titleUpdate: 'Changer le transport #{id}',
    titleAdd: 'Ajouter un nouveau transport',
    titleDefault: 'Informations sur {name}',
    itemAdd: 'Ajoute un cagnard'
  },
  mapItemModal: {
    titleUpdate: 'Modifier un élément de la carte',
    titleAdd: 'Ajouter un nouvel élément de carte',
    titleDelete: 'Suppression d\'un élément de la carte',
    descriptionDelete: 'Es-tu sûr de vouloir retirer l\'objet de la carte ? Cette action ne peut pas être annulée.'
  },
  animationModal: {
    titleUpdate: 'Modifier l\'animation #{id}',
    titleAdd: 'Ajouter une nouvelle animation'
  }
};