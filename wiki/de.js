export default {
  global: {
    all: 'Alle',
    back: '<PERSON><PERSON><PERSON>',
    forward: 'weiter',
    paging: 'Seite {page} von {maxPage}',
    close: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    clear: '<PERSON><PERSON><PERSON>',
    save: '<PERSON><PERSON><PERSON><PERSON>',
    delete: '<PERSON><PERSON><PERSON>',
    deleteYes: '<PERSON><PERSON> sicher, dass Sie löschen möchten?',
    cancel: 'Abbrechen',
    publish: 'Veröffentlichen',
    edit: 'bearbeiten',
    share: '<PERSON>ile<PERSON>',
    add: 'Hinz<PERSON>ügen',
    active: 'Aktiv',
    deleted: 'Gelöscht',
    blocked: 'G<PERSON><PERSON><PERSON>',
    learnMore: 'Mehr lesen',
    panel: 'Panel',
    theme: 'Thema',
    language: 'Sprache',
    user: 'Benutzer',
    confirm: 'Bestätigen',
    restore: 'Wiederherstellung',
    yes: 'Ja',
    no: 'Nein',
    new: 'Neu',
    preview: 'Vorschau',
    sortPriceDesc: 'Nach Preis absteigend',
    sortPriceAsc: 'Nach Preis aufsteigend',
    sortPriceMinMax: 'Preisspanne',
    sortDateDesc: 'In absteigender Reihenfolge des Datums',
    sortDateAsc: 'In aufsteigender Reihenfolge des Datums',
    noSellPrice: 'Nicht zu kaufen',
    noBuyPrice: 'Nicht zu verkaufen',
    noData: {
      title: 'Nichts gefunden',
      description: 'Versuchen Sie, die Suchanfrage zu ändern oder zu vereinfachen'
    },
    battlePass: {
      summer: 'Sommer Battle Pass',
      winter: 'Winter Battle Pass'
    },
    case: {
      default: 'Standardkoffer',
      vehicles: 'Auto Koffer',
      halloween: 'Halloween Fall',
      spring: 'Frühling Aktentasche',
      autumn: 'Herbst Aktentasche',
      summer: 'Sommer Fall',
      summerExtra: 'Ein besonderer Sommerfall',
      summerSkins: 'Fall Sommerhaut',
      summerVehicles: 'Sommer-Transportkoffer',
      winter: 'Winterfall',
      winterExtra: 'Ein besonderer Fall für den Winter',
      winterSkins: 'Winterkoffer mit Fellen',
      winterVehicles: 'Transportkoffer für den Winter',
      toys: 'Spielzeugkoffer'
    },
    subscription: { premium: 'Majestic Premium' },
    copy: 'Kopiere'
  },
  footer: { disclaimer: 'Alle Informationen auf der Website dienen nur zu Informationszwecken und stellen kein öffentliches Angebot dar.' },
  footer: { disclaimer: 'Alle Informationen auf der Website dienen nur zu Informationszwecken und stellen kein öffentliches Angebot dar.' },
  editor: {
    placeholder: 'Lass uns einen richtig coolen Post schreiben!',
    text: 'Absatz',
    heading: 'Überschrift',
    warning: 'Hinweis',
    quote: 'Zitat',
    code: 'Code',
    delimiter: 'Trennen',
    link: 'Link',
    linkPlaceholder: 'Geben Sie den Link ein',
    marker: 'Markierung',
    bold: 'Fettgedruckt',
    italic: 'Kursivschrift',
    inlineCode: 'Monospace',
    underline: 'Unterstrichen',
    copytext: 'Kopierbar',
    log: 'Logue',
    map: {
      name: 'Karte',
      params: 'Gib die Kartenparameter ein',
      caption: 'Gib eine Beschreibung der Karte ein'
    },
    table: {
      name: 'Tabelle',
      addColumnLeft: 'Spalte links hinzufügen',
      addColumnRight: 'Spalte rechts hinzufügen',
      deleteColumn: 'Spalte löschen',
      addRowAbove: 'Zeile oben hinzufügen',
      addRowBelow: 'Zeile unten hinzufügen',
      deleteRow: 'Zeile löschen',
      withHeadings: 'Mit Überschriften',
      withoutHeadings: 'Ohne Überschriften'
    },
    image: {
      name: 'Bild',
      withBorder: 'Mit Rahmen',
      stretchImage: 'Bild dehnen',
      withBackground: 'Mit dem Hintergrund',
      selectImage: 'Ein Bild auswählen',
      uploadError: 'Das Bild konnte nicht geladen werden, bitte versuchen Sie es mit einem anderen',
      caption: 'Bildbeschreibung',
      withCaption: 'Mit einer Beschreibung'
    },
    carousel: {
      name: 'Bildgitter',
      caption: 'Bildbeschreibung',
      addImage: 'Wählen Sie ein Bild aus'
    },
    list: {
      name: 'Liste',
      ordered: 'Nummeriert',
      unordered: 'Unnumeriert',
      checklist: 'Checkliste'
    },
    header: {
      heading1: 'Überschrift H1',
      heading2: 'Überschrift H2',
      heading3: 'Überschrift H3',
      heading4: 'Überschrift H4'
    },
    embed: { caption: 'Link Beschreibung' },
    convertTo: 'Konvertieren in',
    moveUp: 'Nach oben verschieben',
    moveDown: 'Nach unten verschieben',
    anchor: 'Anker',
    clickToTune: 'Klicken Sie, um einzustellen',
    dragToMove: 'Oder ziehen Sie es hierhin',
    filter: 'Suche',
    nothingFound: 'Nichts gefunden'
  },
  alerts: {
    loginSuccess: 'Erfolgreiche Anmeldung',
    loginError: 'Fehler bei der Autorisierung',
    registerSuccess: 'Erfolgreiche Registrierung auf der Website',
    registerError: 'Registrierungsfehler',
    restoreSuccess: 'Die Wiederherstellungsanweisungen wurden an Ihre E-Mail-Adresse gesendet',
    restoreError: 'Fehler bei der Wiederherstellung des Zugangs zum Konto',
    signoutSuccess: 'Erfolgreiche Abmeldung',
    postLoadError: 'Fehler beim Hochladen eines Beitrags',
    postShareSuccess: 'Der Link zum Beitrag wurde in die Zwischenablage kopiert',
    copyCommandSuccess: 'Der Befehl wurde in die Zwischenablage kopiert',
    restoreAccountSuccess: 'Die Wiederherstellung des Kontos war erfolgreich',
    restoreAccountError: 'Fehler bei der Wiederherstellung des Zugriffs auf das Konto',
    verificateAccountSuccess: 'Die E-Mail-Adresse wurde erfolgreich bestätigt',
    verificateAccountError: 'Die E-Mail-Adresse wurde erfolgreich bestätigt',
    dataUpdateSuccess: 'Erfolgreiche Aktualisierung der Informationen',
    dataUpdateError: 'Fehler beim Aktualisieren von Informationen',
    dataDeleteSuccess: 'Erfolgreiche Löschung von Informationen',
    dataDeleteError: 'Fehler beim Löschen von Informationen',
    dataAddSuccess: 'Erfolgreiche Hinzufügung von Informationen',
    dataAddError: 'Fehler beim Hinzufügen der Informationen',
    mapMoveInfo: 'Verschieben Sie den Punkt an eine andere Position und speichern Sie',
    mapCoordinatesCopySuccess: 'Koordinaten erfolgreich kopiert',
    copyTextSuccess: 'Text erfolgreich kopiert'
  },
  mail: {
    verificate: {
      subject: 'Bestätigung der E-Mail-Adresse',
      title: 'Bestätigung',
      description: 'Um die Verifizierung abzuschließen und die Rechte an Ihrem Konto zu bestätigen, klicken Sie bitte auf den untenstehenden Button. Der Link ist 24 Stunden lang gültig.'
    },
    restore: {
      subject: 'Wiederherstellung des Zugangs zum Konto',
      title: 'Wiederherstellung',
      description: 'Um den Zugang zu Ihrem Konto wiederherzustellen, klicken Sie bitte auf den untenstehenden Button. Der Link ist 24 Stunden lang gültig.'
    },
    restoreSuccess: {
      subject: 'Erfolgreiche Wiederherstellung des Zugangs zum Konto',
      title: 'Erfolgreiche Wiederherstellung',
      description: 'Sie haben Ihr Passwort auf der Website erfolgreich geändert. Wenn Sie dies nicht waren, ändern Sie Ihr Passwort sofort!'
    },
    verificateSuccess: {
      subject: 'Erfolgreiche Wiederherstellung des Kontozugangs',
      title: 'Erfolgreiche Bestätigung',
      description: 'Sie haben Ihre E-Mail-Adresse erfolgreich bestätigt, willkommen!'
    },
    caption: 'Diese E-Mail ist eine Benachrichtigung und wurde automatisch erstellt. Bitte antworten Sie nicht darauf.'
  },
  fields: {
    price: 'Preis',
    buyPrice: 'Kaufpreis',
    sellPrice: 'Verkaufspreis',
    mcPrice: 'Kosten für den Kauf in MC',
    money: 'Geld',
    coin: 'Majestätische Münze',
    gift: 'Geschenk',
    default: 'Standard',
    unique: 'Einzigartig',
    event: 'Event',
    battlePass: 'Battle Pass',
    lootbox: 'Kiste',
    capacityKg: 'Kapazität',
    email: 'Mail',
    password: 'Passwort',
    username: 'Nickname',
    weightKg: 'kg',
    speed: 'Geschwindigkeit',
    maxSpeed: 'Maximale Geschwindigkeit',
    maxSpeedFT: 'Höchstgeschwindigkeit (FT)',
    accelerationTo100: 'Beschleunigung von 0 auf 100 km/h',
    speedSystem: 'km/h',
    timeSystem: 'Sek.',
    uploadDescription: 'Klicken oder ziehen Sie ein Bild in den Bereich',
    placeholder: 'Geben Sie den Wert in das Feld ein',
    imagePlaceholder: 'Geben Sie den Link zum Bild ein',
    selectPlaceholder: 'Wählen Sie einen Wert aus der Liste',
    videoPlaceholder: 'Gib einen Link zum Video ein',
    soundPlaceholder: 'Gib einen Link zum Audio ein',
    id: 'ID',
    serial: 'Seriennummer',
    kind: 'Typ',
    category: 'Kategorie',
    name: 'Name',
    view: 'Ansicht',
    layer: 'Ebene',
    icon: 'Icon',
    circle: 'Punkt',
    polygon: 'Polygon',
    emoji: 'Emoji',
    description: 'Beschreibung',
    gender: 'Geschlecht',
    value: 'Werttyp',
    valueName: 'Zusätzliche Preisbeschreibung',
    visibility: 'Verfügbarkeit',
    visibilityPrivate: 'Eingeschränkter Zugang',
    visibilityPublic: 'Sichtbarkeit für alle',
    activityHidden: 'Versteckt vor dem öffentlichen Zugang',
    activityVisible: 'Verfügbar für alle',
    garageSlots: 'Garage Steckplätze',
    maxTenants: 'Anzahl der Einwohner',
    gasoline: 'Kraftstoff',
    brand: 'Marke',
    model: 'Modell',
    trunk: 'Kofferraum',
    tradable: 'Übertragbarkeit',
    buyable: 'Kaufoption',
    upgradesPriceFT: 'Verfügbare Verbesserungen (FT)',
    upgradesEngine: 'Motor',
    upgradesBreaks: 'Bremsen',
    upgradesSuspension: 'Radaufhängung',
    upgradesTurbo: 'Turbo',
    upgradesTransmission: 'Box',
    paintingsMainPrice: 'Grundlegende Malerei',
    paintingsSubPrice: 'Zusätzliche Malerei',
    paintingsOtherPrice: 'Andere Malerei',
    paintingsBrightMetallic: 'Helles Metallic',
    paintingsMetallic: 'Metallic',
    paintingsRichMetallic: 'Sattes Metallic',
    paintingsDarkMetallic: 'Dunkles Metallic',
    paintingsMatte: 'Matt',
    paintingsMatteMetallic: 'Mattmetall',
    paintingsSatin: 'Satin',
    paintingsMetal: 'Metall',
    paintingsShadowChrome: 'Schattenchrom',
    paintingsPureChrome: 'Reines Chrom',
    paintingsPearl: 'Perlmutt-Ton',
    paintingsWheels: 'Felgenfarbe',
    paintingsDetails: 'Farbe der Details',
    paintingsAdditionalDetails: 'Zusatzliche Farbe der Details',
    paintingsLights: 'Farbe der Scheinwerfer',
    paintingsSmoke: 'Farbe des Reifenrauchs',
    paintingsNeon: 'Farbe des Neonlichts',
    total: 'Gesamt',
    language: 'Sprache',
    radius: 'Radius',
    color: 'Farbe',
    colorPlaceholder: 'Wählen Sie eine Farbe aus',
    opacity: 'Transparenz',
    alias: 'Pseudonym',
    aliases: 'Aliasnamen',
    looped: 'Geschleift',
    battlePassId: 'Battle Pass ID',
    gasolineCapacityLiter: 'l',
    gasolineCapacity: 'Tankinhalt',
    source: 'Quelle',
    sources: 'Quellen',
    priceDescription: 'Kostenbeschreibung',
    layerName: 'Name der Ebene',
    layerNamePlaceholder: 'Gib einen Namen für die Ebene ein',
    layerDescription: 'Beschreibung der Ebene',
    layerDescriptionPlaceholder: 'Gib eine Beschreibung für die Ebene ein',
    layerColor: 'Farbe der Schicht',
    layerColorPlaceholder: 'Wählen Sie eine Farbe aus',
    layerOpacity: 'Ebenen-Transparenz',
    zIndexOffset: 'Z-Index Ebene',
    zIndexOffsetPlaceholder: 'Gib den Wert der Z-Index-Stufe ein',
    comment: 'Beschreibung'
  },
  vehiclesPage: {
    meta: {
      title: 'GTA5 RP Transport und Autos | Majestic Wiki',
      description: 'Vollständiger Katalog der Fahrzeuge auf dem Server Majestic RP in GTA5 RP. Detaillierte Eigenschaften von Autos, Motorrädern, Hubschraubern, Flugzeugen und Booten. Erfahre Preise, Geschwindigkeit, Manövrierfähigkeit, Kofferraumvolumen und andere Parameter. Majestic Motors, Autos, LKWs, Hubschrauber, Boote, Motorräder, Fahrräder - alle für GTA5 RP!',
      custom: {
        title: '{type} | GTA5 RP Transport | Majestic Wiki',
        description: 'Ausführlicher Katalog {type} auf dem Server Majestic RP in GTA5 RP. Eigenschaften, Preise, Geschwindigkeit, Manövrierfähigkeit und andere Parameter. Alle Informationen zum Transport für GTA5 RP!'
      }
    },
    default: 'Standard',
    donate: 'Majestätische Münze',
    battlePass: 'Battle Pass',
    case: 'Kiste',
    unique: 'Einzigartig',
    title: 'Transport-Tabelle',
    search: 'Fahrzeugsuche...',
    irl: 'Majestic Motors',
    car: 'PKW',
    freight: 'LKW',
    helicopter: 'Hubschrauber',
    plane: 'Flugzeuge',
    boat: 'Boote',
    motorbike: 'Motorräder',
    bike: 'Fahrräder',
    trailer: 'anhänger',
    military: 'Militär',
    special: 'Spezialität',
    priceDisclaimer: 'Der Endpreis kann aufgrund der LSC-Gebühr auf Ihrem Server abweichen',
    basicInformation: 'Hauptinformationen',
    additionalInformation: 'Zusätzliche Informationen',
    compare: 'Vergleiche',
    compareLimit: 'Es können maximal 5 TCs verglichen werden',
    pagingText: 'Gefunden{N, plural, few{über} many{über}} {N} {N, plural, one{Transport} few{Transport} many{Transport} other{Transport}}'
  },
  vehiclePage: {
    meta: {
      title: 'GTA5 RP Transport | Majestic Wiki',
      description: 'Ausführliche Informationen zum Transport auf dem Server Majestic RP in GTA5 RP. Eigenschaften, Preise, Verbesserungen und Lackierung. Erfahre mehr über die Höchstgeschwindigkeit, die Beschleunigung, das Kofferraumvolumen, die Kosten für Verbesserungen und andere Parameter der einzelnen Fahrzeuge.',
      custom: {
        title: '{name} | GTA5 RP Transport | Majestic Wiki',
        description: 'Ausführliche Informationen über {name} auf dem Server Majestic RP in GTA5 RP. Eigenschaften, Preise, Verbesserungen und Lackierungen. Erfahre mehr über Höchstgeschwindigkeit, Beschleunigung, Kofferraumkapazität, Kosten für Verbesserungen und andere Parameter.'
      }
    },
    backToTable: 'Zurück zur Liste',
    labelsUniqueId: 'Eindeutige ID',
    labelsPrice: 'Preis',
    labelsBuyPrice: 'Kaufpreis',
    labelsSellPrice: 'Verkaufspreis',
    labelsMaxSpeed: 'Maximale Geschwindigkeit',
    labelsMaxSpeedFT: 'Höchstgeschwindigkeit (FT)',
    labelsAccelerationTo100: 'Beschleunigung von 0 auf 100 km/h',
    labelsTradable: 'Übertragbarkeit',
    labelsTrunk: 'Kapazität des Stiefels',
    labelsGasolineCapacity: 'Tankinhalt',
    labelsGasolineType: 'Kraftstoffart',
    upgradesTitle: 'Verbesserungen im Verkehr',
    upgradesDescription: 'Kosten für Upgrades auf die maximale Stufe',
    upgradesEngine: 'Motor',
    upgradesEngineDescription: 'Entdecke das wahre Potenzial des Verkehrs',
    upgradesBreaks: 'Bremsen',
    upgradesBreaksDescription: 'Volle Geschwindigkeitskontrolle auf jeder Straße',
    upgradesTurbo: 'Turbo',
    upgradesTurboDescription: 'Explosive Beschleunigung in jedem Moment',
    upgradesTransmission: 'Box',
    upgradesTransmissionDescription: 'Blitzschnelles Schalten aller Gänge',
    upgradesSuspension: 'Radaufhängung',
    upgradesSuspensionDescription: 'Perfekter Komfort in der richtigen Höhe',
    upgradesTotal: 'Kosten für Verbesserungen',
    upgradesDisclaimer: 'Der Endpreis kann aufgrund der LSC-Gebühr auf Ihrem Server abweichen',
    colorsTitle: 'Fahrzeuglackierung',
    colorsDescription: 'Verfügbare Arten und Typen von Anstrichen mit Kosten',
    paintingsMainPrice: 'Grundlegende Malerei',
    paintingsSubPrice: 'Zusätzliche Malerei',
    paintingsOtherPrice: 'Andere Malerei',
    paintingsBrightMetallic: 'Helles Metallic',
    paintingsMetallic: 'Metallic',
    paintingsRichMetallic: 'Sattes Metallic',
    paintingsDarkMetallic: 'Dunkles Metallic',
    paintingsMatte: 'Matt',
    paintingsMatteMetallic: 'Mattmetall',
    paintingsSatin: 'Satin',
    paintingsMetal: 'Metall',
    paintingsShadowChrome: 'Schattenchrom',
    paintingsPureChrome: 'Reines Chrom',
    paintingsPearl: 'Perlmutt-Ton',
    paintingsWheels: 'Felgenfarbe',
    paintingsDetails: 'Farbe der Details',
    paintingsAdditionalDetails: 'Zusatzliche Farbe der Details',
    paintingsLights: 'Farbe der Scheinwerfer',
    paintingsSmoke: 'Farbe des Reifenrauchs',
    paintingsNeon: 'Farbe des Neonlichts',
    paintingsTotal: 'Endgültige Kosten für den Anstrich',
    shareLink: 'Teile den Link zum Transport',
    shareLinkCopied: 'Die Transportverbindung wurde erfolgreich kopiert',
    addToCompare: 'Zum Vergleich hinzufügen',
    addedToCompare: 'Transport wurde zum Vergleich hinzugefügt',
    goToCompare: 'Zum Vergleich gehen',
    removeFromCompare: 'Aus dem Vergleich entfernen',
    removedFromCompare: 'Der Transport wurde aus dem Vergleich herausgenommen'
  },
  vehiclesComparePage: {
    meta: {
      title: 'GTA5 RP Transport Vergleich | Majestic Wiki',
      description: 'Vergleiche die Eigenschaften und Preise der verschiedenen Fahrzeuge auf dem Majestic RP Server in GTA5 RP. Finde heraus, welches Fahrzeug am besten zu deinen Bedürfnissen passt! Vergleiche Geschwindigkeit, Preis, Kapazität und andere Parameter.'
    },
    name: 'Name',
    type: 'Typ',
    brand: 'Marke',
    kits: 'Pakete',
    model: 'Modell',
    trunkCapacity: 'Kapazität des Stiefels',
    maxSpeed: 'Maximale Geschwindigkeit',
    maxSpeedFT: 'Höchstgeschwindigkeit (FT)',
    price: 'Preis',
    donatePrice: 'Kosten in Majestic Coin',
    sellPrice: 'Verkaufspreis',
    sellPriceBeforeTax: 'Kosten des Verkaufs (vor Steuern)',
    sellPriceWithDiscount: 'Verkaufskosten (abgezinst)',
    accelerationTo100: 'Beschleunigungszeit auf 100 km/h',
    gasolineCapacity: 'Tankinhalt',
    gasolineType: 'Kraftstoffart',
    isTradable: 'Übertragbarkeit',
    isBuyable: 'Kaufoption',
    createdAt: 'Datum des Erscheinens',
    gasolineCapacityUnit: 'l',
    maxSpeedUnit: 'km/h',
    maxSpeedFTUnit: 'km/h',
    priceUnit: '₽',
    accelerationTo100Unit: 'Sek.',
    trunkCapacityUnit: 'kg',
    showOnlyDifferent: 'Zeige nur die, die anders sind',
    showAll: 'Alle anzeigen',
    share: 'Einen Vergleich teilen',
    linkCopied: 'Der Vergleichslink wurde erfolgreich kopiert',
    openVehicle: 'Öffne die Transportseite',
    deleteVehicle: 'Ein Fahrzeug aus dem Vergleich entfernen',
    noDifferences: 'Es gibt keinen Unterschied'
  },
  skinsPage: {
    meta: {
      title: 'GTA5 RP-Waffen-Skins | Majestic Wiki',
      description: 'Vollständiger Katalog der Waffenskins auf dem Server Majestic RP in GTA5 RP. Hier erfährst du, wie du die Skins für die verschiedenen Waffentypen bekommst, was sie kosten und welche Eigenschaften sie haben.',
      custom: {
        title: 'Skins auf {type} | GTA5 RP | Majestic Wiki',
        description: 'Katalog der Skins für {type} auf dem Server Majestic RP in GTA5 RP. Detaillierte Informationen darüber, wie man sie bekommt, Kosten und Eigenschaften der Skins.'
      }
    },
    title: 'Skin-Tabelle',
    search: 'Suche nach Skins...',
    apPistol: 'Panzerbrechende Pistole',
    assaultRifle: 'Sturmgewehr',
    assaultRifleMk2: 'Mk2 Sturmgewehr',
    bullpupRifle: 'Gewehr Bullpup',
    carbineRifle: 'Karabinergewehr',
    carbineRifleMk2: 'Karabinergewehr Mk2',
    combatPDW: 'Kampf PDW',
    gusenberg: 'Thompson-Maschinenpistole.',
    heavyPistol: 'Schwere Waffe',
    heavyShotgun: 'Schwere Schrotflinte',
    heavySniper: 'Schweres Scharfschützengewehr',
    heavySniperMk2: 'Schweres Scharfschützengewehr Mk2',
    lightvest: 'Schutzweste',
    machinePistol: 'Kleine Maschinenpistole',
    marksmanPistol: 'Scharfschützengewehr',
    microSMG: 'Micro SMG',
    militaryRifle: 'Militärgewehr',
    revolver: 'Revolver',
    smgMk2: 'SMG Mk2',
    specialCarbine: 'Spezieller Karabiner',
    vintagePistol: 'Vintage Pistole',
    combatMgMk2: 'Mk2-Maschinenpistole',
    heavyRifle: 'Schweres Gewehr',
    tacticalRifle: 'Taktisches Gewehr',
    tecPistol: 'Taktisches SMG',
    noName: 'Skin #{id}',
    battlePass: 'Battle Pass',
    case: 'Kiste',
    unique: 'Einzigartig',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{skin} few{skin} many{skins} other{skins}}'
  },
  animationsPage: {
    meta: {
      title: 'GTA5 RP Animationen | Majestic Wiki',
      description: 'Vollständiger Katalog der Animationen auf dem Server Majestic RP im GTA5 RP. Alle Arten von Animationen: Aktionen, Posen, Tänze und exklusive Animationen. Finde heraus, wie du jede Animation bekommst und wie viel sie kostet.',
      custom: {
        title: '{type} | GTA5 RP Animationen | Majestic Wiki',
        description: 'Katalog {type} Animationen auf dem Server Majestic RP in GTA5 RP. Detaillierte Informationen darüber, wie man Animationen bekommt, kostet und benutzt.'
      }
    },
    title: 'Animationstabelle',
    search: 'Animation Suche...',
    default: 'Standard',
    donate: 'Majestätische Münze',
    battlePass: 'Battle Pass',
    case: 'Kiste',
    unique: 'Einzigartig',
    action: 'Aktionen',
    pose: 'Posen',
    positive: 'Positive',
    negative: 'Negative',
    dances: 'Tanzen',
    etc: 'Sonstiges',
    exclusive: 'Exklusive',
    noName: 'Animation #{id}',
    pagingText: 'Найден{N, plural, one{а} few{о} many{о}} {N} {N, plural, one{анимация} few{анимации} many{анимаций} other{анимаций}}'
  },
  realtyPage: {
    meta: {
      title: 'GTA5 RP Real Estate | Majestic Wiki',
      description: 'Vollständiger Katalog der Immobilien auf dem Server Majestic RP in GTA5 RP. Häuser, Wohnungen, Büros und Lagerhallen mit detaillierten Eigenschaften, Preisen und Lage. Erfahre die Anzahl der Mieter, Garagenplätze und andere Parameter.',
      custom: {
        title: '{type} | GTA5 RP Real Estate | Majestic Wiki',
        description: 'Katalog {type} auf dem Server Majestic RP in GTA5 RP. Detaillierte Informationen über die Merkmale, Preise, Lage und Ausstattung der Immobilie.'
      }
    },
    title: 'Immobilientabelle',
    search: 'Immobiliensuche...',
    majesticMotors: 'Majestic Motors',
    house: 'Häuser',
    apartment: 'Wohnungen',
    office: 'Büroräume',
    warehouse: 'Lagerhäuser',
    tenantsFor: '{N} {N, plural, one{Mieter} few{Mieter} many{Mieter} other{Mieter}}',
    garageSlotsFor: '{N} {N, plural, one{Garagenplätze} few{Garagenplätze} many{Garagenplätze} other{Garagenplätze}}',
    viewOnMap: 'Auf der Karte sehen',
    mapPosition: 'Standort',
    pagingText: 'Found{N, plural, few{about} many{about}} {N} {N, plural, one{real estate} few{real estate} many{real estate} other{real estate}}',
    noName: 'Immobilien #{id}'
  },
  postsPage: {
    meta: {
      title: 'Editieren eines Beitrags | Majestic Wiki',
      description: 'Erstelle und bearbeite Beiträge im Majestic Wiki für GTA5 RP. Veröffentliche aktuelle Informationen über Transport, Immobilien, Geschäfte und andere Aspekte des Spiels.'
    },
    titlePlaceholder: 'Gib den Titel des Beitrags ein',
    category: 'Kategorie',
    noCategory: 'Uncategorised',
    availability: 'Verfügbarkeit',
    allView: 'Sichtbarkeit für alle',
    privateView: 'Eingeschränkter Zugang',
    relevance: 'Relevanz',
    relevanceData: 'Aktuelle Informationen',
    outdatedData: 'Veraltete Informationen',
    categoryPosition: 'Position in der Kategorie',
    categoryPositionDescription: 'Zum Beispiel "50" oder "-5".',
    postDeleted: 'Beitrag entfernt',
    author: 'Autor des Artikels',
    editor: 'Beitrag Editor',
    contents: 'Der Inhalt des Artikels',
    readingTime: 'Lesezeit',
    readingTimeLength: '{N} {N, plural, one{Minute} few{Minuten} many{Minuten} other{Minuten}}',
    publishDate: 'Veröffentlicht am {date}',
    views: '{N} {N, plural, one{View} few{Views} many{Views} other{Views}}',
    youLiked: 'Du hast geliked',
    youAndPeopleLiked: 'Du und {likes} haben geliked',
    peopleLiked: '{N} {N, plural, one{hat geliked} few{haben geliket} many{haben geliket} other{haben geliket}}',
    categories: 'Kategorien',
    posts: 'Beiträge',
    foundMistake: 'Hast du einen Fehler in dem Artikel gefunden?',
    language: 'Sprache'
  },
  panelPage: {
    users: {
      title: 'Benutzerverwaltung | Majestic Wiki',
      heading: 'Benutzerverwaltung',
      admin: 'Administrator',
      user: 'Benutzer',
      customUser: 'Benutzer mit Rechten',
      search: 'Beginnen Sie mit der Eingabe der Suchanfrage...',
      comment: 'Beschreibung',
      pagingText: 'Gefunden{N, plural, few{über} many{über}} {N} {N, plural, one{Benutzer} few{Benutzer} many{Benutzer} other{Benutzer}}'
    },
    posts: {
      title: 'Post Management | Majestic Wiki',
      heading: 'Post Management',
      views: '{N} {N, plural, one{View} few{Views} many{Views} other{Views}}',
      likes: '{N} {N, plural, one{likes} few{likes} many{likes} other{likes}}'
    },
    categories: {
      title: 'Kategorie-Management | Majestic Wiki',
      heading: 'Kategorie Management'
    },
    mapLayers: {
      title: 'Verwalten von Kartenebenen | Majestic Wiki',
      heading: 'Verwalten von Kartenebenen'
    },
    logs: {
      title: 'Log Auditing - Majestic Wiki',
      heading: 'Prüfung von Protokollen',
      search: 'Beginnen Sie mit der Eingabe der Suchanfrage...',
      biz: 'Unternehmen',
      category: 'Kategorie',
      clothing: 'Kleidung',
      file: 'Datei',
      item: 'Gegenstand',
      mapItem: 'Kartenelement',
      mapLayer: 'Kartenebene',
      post: 'Die Post',
      realty: 'Immobillien',
      skin: 'Skin',
      user: 'Benutzer',
      vehicle: 'Fahrzeug',
      animation: 'Animation',
      actions: {
        unknown: '{username} eine unbekannte Tat begangen',
        bizCreate: '{username} ein Geschäft hinzugefügt {id}',
        bizUpdate: '{username} verändertes Geschäft {id}',
        bizDelete: '{username} das Geschäft gelöscht {id}',
        categoryCreate: '{username} die Kategorie {id}hinzugefügt',
        categoryUpdate: '{username} die Kategorie {id}geändert',
        categoryDelete: '{username} gelöschte Kategorie {id}',
        clothingCreate: '{username} Kleidung hinzugefügt {id}',
        clothingUpdate: '{username} Kleidung gewechselt {id}',
        clothingDelete: '{username} die Kleidung gelöscht {id}',
        fileCreate: '{username} eine Datei hinzugefügt {id}',
        fileUpdate: '{username} die Datei {id}geändert',
        fileDelete: '{username} die Datei {id}gelöscht',
        itemCreate: '{username} den Artikel {id}hinzugefügt',
        itemUpdate: '{username} das Thema gewechselt {id}',
        itemDelete: '{username} den Artikel {id}gelöscht',
        mapItemCreate: '{username} einen Gegenstand auf der Karte {id} zu "{layer}" hinzugefügt.',
        mapItemUpdate: '{username} das Element auf der Karte {id} in "{layer}" geändert',
        mapItemDelete: '{username} ein Element auf der Karte {id} zu "{layer}" gelöscht.',
        mapLayerCreate: '{username} eine Kartenebene hinzugefügt {id}',
        mapLayerUpdate: '{username} die Kartenebene geändert {id}',
        mapLayerDelete: '{username} die Kartenebene {id}gelöscht',
        postCreate: '{username} einen Beitrag hinzugefügt {id}',
        postUpdate: '{username} Geänderter Beitrag {id}',
        postDelete: '{username} gelöschter Beitrag {id}',
        realtyCreate: '{username} die Eigenschaft {id}hinzugefügt',
        realtyUpdate: '{username} veränderte Immobilien {id}',
        realtyDelete: '{username} gelöschte Immobilien {id}',
        skinCreate: '{username} den Skin hinzugefügt {id}',
        skinUpdate: '{username} den Skin geändert {id}',
        skinDelete: '{username} den Skin gelöscht {id}',
        userCreate: '{username} Benutzer hinzugefügt {id}',
        userUpdate: '{username} den Benutzer geändert {id}',
        userDelete: '{username} gelöschter Benutzer {id}',
        vehicleCreate: '{username} Transport hinzugefügt {id}',
        vehicleUpdate: '{username} den Transport geändert {id}',
        vehicleDelete: '{username} den Transport gelöscht {id}',
        animationCreate: '{username} Animation hinzugefügt {id}',
        animationUpdate: '{username} die Animation geändert {id}',
        animationDelete: '{username} die Animation gelöscht {id}',
        changelogCreate: '{username} Changelog hinzugefügt {id}',
        changelogUpdate: '{username} den Changelog geändert {id}',
        changelogDelete: '{username} Changelog gelöscht {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: 'Einstellungen',
      users: 'Benutzer',
      categories: 'Kategorien',
      mapLayers: 'Kartenebenen',
      posts: 'Beiträge',
      logs: 'Logs'
    }
  },
  changelogPage: {
    titlePlaceholder: 'Gib den Namen der Änderung ein',
    descriptionPlaceholder: 'Gib eine Beschreibung der Änderung ein',
    availableAt: 'Datum der Veröffentlichung',
    soon: 'über',
    ago: 'ich brauche nichts, danke',
    fix: 'Korrigiert',
    feat: 'Hinzugefügt',
    chore: 'Geändert',
    style: 'Entwurf',
    refactor: 'Recycelt'
  },
  changelogsPage: {
    meta: {
      title: 'Geschichte der Änderungen | Majestic Wiki',
      description: 'Die komplette Geschichte der Updates und Änderungen auf dem Majestic RP Server. Halte Ausschau nach neuen Features, Gameplay-Verbesserungen, Fehlerbehebungen und Balancing. Aktuelle Informationen über alle Projekt-Updates.'
    },
    title: 'Geschichte der Veränderungen',
    search: 'Den Wandel finden...'
  },
  homePage: {
    meta: {
      title: 'Majestic Wiki | GTA5 RP Enzyklopädie',
      description: 'Majestic Wiki ist eine vollständige Enzyklopädie für GTA5-RP-Spieler. Informationen über Transport, Immobilien, Geschäfte, Quests und andere Aspekte des Spiels. Aktuelle Informationen über Preise, Eigenschaften und wie man sie erhält.'
    },
    tools: {
      map: {
        title: 'Karte',
        description: 'Serverkarte mit allen Standorten'
      },
      vehicles: {
        title: 'Fahrzeug',
        description: 'Alle verfügbaren Fahrzeuge auf dem Server'
      },
      realty: {
        title: 'Immobillien',
        description: 'Daten zu allen Häusern und Wohnungen'
      },
      biz: {
        title: 'Unternehmen',
        description: 'Fortgeschrittene Verdienstmöglichkeiten'
      },
      clothesMale: {
        title: 'Männerkleidung',
        description: 'Eine Modenschau für Männer'
      },
      clothesFemale: {
        title: 'Frauenkleidung',
        description: 'Modenschau für Frauen'
      },
      skins: {
        title: 'Skins',
        description: 'Katalog mit allen verfügbaren Mustern'
      },
      items: {
        title: 'Gegenstände',
        description: 'Verfügbare Gegenstände auf dem Server'
      },
      servers: {
        title: 'Server',
        description: 'Liste aller Majestic RP Server'
      },
      animations: {
        title: 'Animationen',
        description: 'Einzigartige Animationen auf dem Server'
      },
      changelogs: {
        title: 'Geschichte der Veränderungen',
        description: 'Historie der Änderungen am Projekt'
      }
    },
    search: {
      title: 'Allgemeine Kategorien',
      description: 'Hier können Sie alle Informationen über den Majestic Server und seine Systeme finden'
    },
    categories: {
      collapseList: 'Liste kollabieren',
      expandList: 'Liste erweitern'
    },
    online: {
      players: '{count} online',
      offline: 'Ausgeschaltet'
    }
  },
  errorPage: {
    title: 'Seite nicht gefunden',
    description: 'Möglicherweise haben Sie einen Tippfehler im Seitenlink, oder die Seite existiert einfach nicht :(',
    backToHome: 'Zur Hauptseite gehen'
  },
  bizPage: {
    meta: {
      title: 'GTA5 RP-Geschäfte | Majestic Wiki',
      description: 'Vollständiger Katalog der Geschäfte auf dem Server Majestic RP in GTA5 RP. 24/7 Shops, Tankstellen, Waffengeschäfte, Autohäuser und andere Geschäfte. Erfahre mehr über die Rentabilität, die Anforderungen und wie du jedes Geschäft kaufen kannst.',
      custom: {
        title: '{type} | GTA5 RP Geschäfte | Majestic Wiki',
        description: 'Ausführliche Informationen über {type} auf dem Server Majestic RP in GTA5 RP. Merkmale, Rentabilität, Anforderungen und wie man ein Geschäft kauft.'
      }
    },
    title: 'Tabelle der Unternehmen',
    search: 'Suche nach Unternehmen...',
    groceryStore: '24/7 Laden',
    gasStation: 'Tankstelle',
    atm: 'Geldautomat',
    gunshop: 'Waffenladen',
    clothingStore: 'Bekleidungsgeschäft',
    autoShop: 'Autohaus',
    tattoo: 'Tattoo-Salon',
    lsc: 'Tuning Salon',
    barbershop: 'Friseur',
    carWash: 'Waschanlage',
    viewOnMap: 'Auf der Karte anzeigen',
    mapPosition: 'Standort',
    pagingText: 'Gefunden{N, plural, few{über} many{über}} {N} {N, plural, one{Unternehmen} few{Unternehmen} many{Unternehmen} other{Unternehmen}}',
    noName: 'Unternehmen #{id}'
  },
  clothesPage: {
    meta: {
      title: 'GTA5 RP-Kleidung | Majestic Wiki',
      description: 'Kompletter Bekleidungskatalog auf dem Server Majestic RP in GTA5 RP. Männer- und Frauenkleidung, Accessoires, Rucksäcke, Aufnäher, Mützen und andere Kleidungsstücke. Finde heraus, wie du sie bekommst und wie viel jedes Kleidungsstück kostet.',
      custom: {
        title: '{type} | GTA5 RP-Kleidung | Majestic Wiki',
        description: 'Katalog {type} auf dem Server Majestic RP in GTA5 RP. Detaillierte Informationen über den Erhalt, die Verwendung und die Kosten von Kleidungsstücken.'
      }
    },
    titleMale: 'Tabelle für Männerkleidung',
    titleFemale: 'Tabelle für Frauenkleidung',
    search: 'Kleidung suchen...',
    top: 'Oberteile',
    legs: 'Hosen',
    shoes: 'Schuhe',
    watch: 'Stunden',
    mask: 'Masken',
    decal: 'Ärmelstreifen',
    accessory: 'Accessoires',
    head: 'Kopfbedeckungen',
    bag: 'Rucksäcke',
    glasses: 'Brillen',
    ear: 'Ohren',
    gloves: 'Handschuhe',
    undershirt: 'Unterhemden',
    noName: 'Kleidung Set #{id}',
    drawable: 'Drawable',
    texture: 'Texture',
    male: 'Männlich',
    female: 'Weiblich',
    donate: 'Majestätische Münze',
    battlePass: 'Battle Pass',
    case: 'Kiste',
    unique: 'Einzigartig',
    pagingText: 'Gefunden{N, plural, few{über} many{über}} {N} {N, plural, one{Kleidung} few{Kleidung} many{Kleidung} other{Kleidung}}'
  },
  itemsPage: {
    meta: {
      title: 'GTA5 RP Items | Majestic Wiki',
      description: 'Vollständiger Katalog von Gegenständen auf dem Server Majestic RP in GTA5 RP. Antiquitäten, Fische, Erze, Holz, Kisten, Materialien und andere Gegenstände. Erfahre, wie du jeden Gegenstand erhältst, wie du ihn verwendest und was er kostet.',
      custom: {
        title: '{type} | GTA5 RP Items | Majestic Wiki',
        description: 'Katalog {type} auf dem Server Majestic RP in GTA5 RP. Detaillierte Informationen über den Erhalt, die Verwendung und die Kosten von Gegenständen.'
      }
    },
    title: 'Tabelle der Gegenstände',
    search: 'Suche nach Gegenständen...',
    antique: 'Antiquitäten',
    fish: 'Fisch',
    ore: 'Erz',
    wood: 'Holz',
    box: 'Boxen',
    material: 'Materialien',
    event: 'Event',
    food: 'Lebensmittel',
    alcohol: 'Alkohol',
    illegal: 'Illegal',
    medical: 'Medizin',
    equipment: 'Ausrüstung',
    ammunition: 'Munition',
    tool: 'Werkzeuge',
    unique: 'Einzigartig',
    vehicle: 'Autoteile',
    others: 'Verschiedenes',
    default: 'Standard',
    battlePass: 'Battle Pass',
    case: 'Kiste',
    unique: 'Einzigartig',
    pagingText: 'Gefunden{N, plural, few{über} many{über}} {N} {N, plural, one{Artikel} few{Artikel} many{Artikel} other{Artikel}}',
    noName: 'Gegenstand #{id}'
  },
  mapPage: {
    meta: {
      title: 'GTA5 RP Karte | Majestic Wiki',
      description: 'Interaktive Karte des Majestic RP Servers in GTA5 RP. Erforsche Orte, finde Schätze, Tiere, Telefone und Müll. Detaillierte Karte der Welt mit der Möglichkeit, Tags und Zonen hinzuzufügen. Alle wichtigen Orte und Points of Interest auf einer Ressource!',
      custom: {
        title: '{type} | GTA5 RP Karte | Majestic Wiki',
        description: 'Eine detaillierte Karte von {type} auf dem Majestic RP Server in GTA5 RP. Erkunde Orte, finde wichtige Punkte und erstelle deine eigenen Markierungen. Vollständige Informationen über die Lage der Objekte auf der Karte!'
      }
    },
    dataLoading: 'Daten laden...',
    defaultPoints: 'Standard-Tags',
    animalLayer: 'Tierkarte',
    rubbishLayer: 'Karte der Mülltonnen',
    telephoneLayer: 'Telefonkarte',
    treasureLayer: 'Schatzkarte',
    defaultLayer: 'Weltkarte',
    allLayers: 'Kartenebenen',
    backToHome: 'Zurück zu Home',
    zoomIn: 'Hineinzoomen',
    zoomOut: 'Herauszoomen',
    point: 'Punkt',
    polygon: 'Zone',
    addPoint: 'Einen Punkt hinzufügen',
    addPolygon: 'Eine Zone hinzufügen',
    copyCoordinates: 'Koordinaten kopieren',
    savePointPosition: 'Position speichern',
    editPointPosition: 'Position ändern',
    layerNamePlaceholder: 'Name der Zone',
    layerDescriptionPlaceholder: 'Zone Beschreibung'
  },
  serversPage: {
    meta: {
      title: 'Online GTA5 RP-Server | Majestic Wiki',
      description: 'Aktuelle Statistiken der Online-Spieler auf den Servern Majestic RP in GTA5 RP. Aktuell online, Spitzenwerte, Warteschlangen und Aktivitätsgraphen. Mach mit beim beliebtesten Rollenspielprojekt!'
    },
    dataLoading: 'Daten laden...',
    title: 'Online Server',
    online: '{count} online',
    inQueue: '{count} in der Warteschlange',
    peakOnline: '{count} peak',
    currentOnline: 'aktuell online',
    peakOnline24h: 'Gipfel heute',
    peakOnlineAllTime: 'Allzeithoch',
    peakOnlineTooltip: '{percent}% relativ zum Vortag',
    techWorks: 'Technische Arbeit',
    forBeginners: 'Für Anfänger',
    experience: '{multiplier}Erfahrung x',
    newServer: 'Neu',
    seasonPassMultiplier: '{multiplier}Erfahrung überspringen x',
    combinedView: 'Allgemein',
    separateView: 'Separate'
  },
  research: {
    placeholder: 'Suche nach Informationen auf der Website...',
    skin: 'Skin',
    vehicle: 'Fahrzeug',
    realty: 'Immobillien',
    biz: 'Unternehmen',
    clothing: 'Kleidung',
    item: 'Gegenstand',
    post: 'Die Post'
  },
  userModal: {
    login: 'Betreten',
    signout: 'Abmelden',
    register: 'Registrieren',
    registerEnd: 'Registrieren',
    restore: 'Wiederherstellung',
    restoreEnd: 'Wiederherstellen',
    avatarDelete: 'Avatar löschen',
    headingUpdate: 'Änderung des Benutzers #{id}',
    postAdd: 'Beitrag veröffentlichen',
    postUpdate: 'Beiträge bearbeiten',
    postDelete: 'Löschen von Beiträgen',
    vehicleAdd: 'Fahrzeug hinzufügen',
    vehicleUpdate: 'Fahrzeug bearbeiten',
    vehicleDelete: 'Fahrzeug löschen',
    realtyAdd: 'Immobilie hinzufügen',
    realtyUpdate: 'Immobilien ändern',
    realtyDelete: 'Immobilie löschen',
    bizAdd: 'Hinzufügen eines Unternehmens',
    bizUpdate: 'Unternehmen ändern',
    bizDelete: 'Unternehmen löschen',
    mapItemAdd: 'Hinzufügen von Elementen zur Karte',
    mapItemUpdate: 'Ändern von Elementen auf der Karte',
    mapItemDelete: 'Löschen von Elementen auf der Karte',
    clothingAdd: 'Hinzufügen von Kleidung',
    clothingUpdate: 'Kleidung ändern',
    clothingDelete: 'Kleidung löschen',
    skinAdd: 'Skin hinzufügen',
    skinUpdate: 'Skin ändern',
    skinDelete: 'Skin löschen',
    itemAdd: 'Gegenstände hinzufügen',
    itemUpdate: 'Gegenstände ändern',
    itemDelete: 'Entfernung von Gegenständen',
    fileUpload: 'Bilder hochladen',
    viewPrivate: 'Ansicht privater Daten',
    doMagic: 'Zugang zu magischen Funktionen',
    animationAdd: 'Hinzufügen von Animationen',
    animationUpdate: 'Ändern von Animationen',
    animationDelete: 'Löschen von Animationen',
    categoryAdd: 'Hinzufügen von Kategorien',
    categoryUpdate: 'Wechselnde Kategorien',
    categoryDelete: 'Löschen von Kategorien',
    mapLayerAdd: 'Hinzufügen von Kartenebenen',
    mapLayerUpdate: 'Ändern von Kartenebenen',
    mapLayerDelete: 'Löschen von Kartenebenen',
    post: 'Die Post',
    skin: 'Skin',
    biz: 'Unternehmen',
    clothing: 'Kleidung',
    item: 'Gegenstand',
    vehicle: 'Fahrzeug',
    animation: 'Animation',
    category: 'Kategorie',
    realty: 'Immobillien',
    mapLayer: 'Kartenebene',
    changelog: 'Changelog'
  },
  restoreModal: {
    title: 'Konto wiederherstellen',
    newPassword: 'Neues Passwort',
    newPasswordRepeat: 'Neues Passwort wiederholen',
    passwordConfirm: 'Passwort ändern',
    passwordError: 'Die Passwörter stimmen nicht überein'
  },
  bizModal: {
    titleUpdate: 'Änderung des Unternehmens #{id}',
    titleAdd: 'Hinzufügen eines neuen Unternehmens'
  },
  categoryModal: {
    titleUpdate: 'Ändern der Kategorie #{id}',
    titleAdd: 'Hinzufügen einer neuen Kategorie'
  },
  mapLayerModal: {
    titleUpdate: '#{id}Ändern der Kartenebene',
    titleAdd: 'Hinzufügen einer neuen Kartenebene'
  },
  clothingModal: {
    titleUpdate: 'Änderung der Kleidung #{id}',
    titleAdd: 'Hinzufügen neuer Kleidung',
    itemAdd: 'Farbe hinzufügen',
    itemGenerate: 'Alle Farben generieren'
  },
  itemModal: {
    titleUpdate: 'Änderung des Gegenstands #{id}',
    titleAdd: 'Hinzufügen eines neuen Gegenstands'
  },
  realtyModal: {
    titleUpdate: 'Änderung der Immobilie #{id}',
    titleAdd: 'Hinzufügen einer neuen Immobilie'
  },
  skinModal: {
    titleUpdate: 'Änderung des Skins #{id}',
    titleAdd: 'Hinzufügen eines neuen Skins'
  },
  vehicleModal: {
    titleUpdate: 'Änderung des Fahrzeugs #{id}',
    titleAdd: 'Hinzufügen eines neuen Fahrzeugs',
    titleDefault: 'Informationen über {name}',
    itemAdd: 'Body-Kit hinzufügen'
  },
  mapItemModal: {
    titleUpdate: 'Ändern eines Kartenelements',
    titleAdd: 'Hinzufügen eines neuen Kartenelements',
    titleDelete: 'Löschen eines Kartenelements',
    descriptionDelete: 'Bist du sicher, dass du den Gegenstand von der Karte entfernen willst? Diese Aktion kann nicht abgebrochen werden.'
  },
  animationModal: {
    titleUpdate: 'Ändern der Animation #{id}',
    titleAdd: 'Hinzufügen einer neuen Animation'
  }
};