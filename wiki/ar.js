export default {
  global: {
    all: 'جميعها',
    back: 'رجوع',
    forward: 'هيا بنا نذهب',
    paging: 'الصفحة {page} من {maxPage}',
    close: 'إغلاق',
    clear: 'واضح',
    save: 'الحفظ',
    delete: 'حذف',
    deleteYes: 'هل أنت متأكد من الإزالة؟',
    cancel: 'رفض',
    publish: 'النشر',
    edit: 'تحرير',
    share: 'شارك',
    add: 'إضافة.',
    active: 'نشط',
    deleted: 'محذوف',
    blocked: 'محجوب',
    learnMore: 'اقرأ المزيد',
    panel: 'اللوحة',
    theme: 'الموضوع',
    language: 'اللغة',
    user: 'المستخدم',
    confirm: 'الإقرار',
    restore: 'الترميم',
    yes: 'نعم',
    no: 'لا يوجد',
    new: 'جديد',
    preview: 'معاينة',
    sortPriceDesc: 'بالترتيب التنازلي للسعر',
    sortPriceAsc: 'بالترتيب التصاعدي للسعر',
    sortPriceMinMax: 'نطاق السعر',
    sortDateDesc: 'بالترتيب التنازلي للتاريخ',
    sortDateAsc: 'بترتيب تصاعدي حسب التاريخ',
    noSellPrice: 'ليس للشراء',
    noBuyPrice: 'غير معروضة للبيع',
    noData: {
      title: 'لم يتم العثور على شيء',
      description: 'حاول تغيير استعلام البحث أو تبسيطه'
    },
    battlePass: {
      summer: 'تذكرة المعركة الصيفية',
      winter: 'تذكرة المعركة الشتوية'
    },
    case: {
      default: 'الحالة القياسية',
      vehicles: 'حالة السيارة',
      halloween: 'حالة الهالوين',
      spring: 'حقيبة الربيع',
      autumn: 'حقيبة الخريف',
      summer: 'حالة الصيف',
      summerExtra: 'حالة صيفية خاصة',
      summerSkins: 'حالة الجلد الصيفية',
      summerVehicles: 'حالة النقل الصيفي',
      winter: 'حالة الشتاء',
      winterExtra: 'حالة خاصة بالشتاء',
      winterSkins: 'حالة الشتاء مع الجلود',
      winterVehicles: 'حالة النقل الشتوي',
      toys: 'علبة لعبة'
    },
    subscription: { premium: 'اشتراك مميز' },
    copy: 'نسخ'
  },
  footer: { disclaimer: 'جميع المعلومات على الموقع الإلكتروني هي لأغراض إعلامية فقط ولا تشكل عرضاً عاماً.' },
  footer: { disclaimer: 'جميع المعلومات على الموقع الإلكتروني هي لأغراض إعلامية فقط ولا تشكل عرضاً عاماً.' },
  editor: {
    placeholder: 'لنكتب منشوراً رائعاً!',
    text: 'الفقرة',
    heading: 'التسمية التوضيحية',
    warning: 'ملاحظة',
    quote: 'اقتباس',
    code: 'الكود',
    delimiter: 'الفاصل',
    link: 'المرجع',
    linkPlaceholder: 'أدخل الرابط',
    marker: 'العلامة',
    bold: 'جريئة',
    italic: 'الخط المائل',
    inlineCode: 'أحادية المسافة',
    underline: 'التأكيد على',
    copytext: 'قابل للنسخ',
    log: 'لوجو',
    map: {
      name: 'الخريطة',
      params: 'أدخل معلمات البطاقة',
      caption: 'أدخل وصفاً للخريطة'
    },
    table: {
      name: 'الجدول',
      addColumnLeft: 'إضافة عمود على اليسار',
      addColumnRight: 'إضافة عمود على اليمين',
      deleteColumn: 'حذف العمود',
      addRowAbove: 'إضافة سطر في الأعلى',
      addRowBelow: 'إضافة سطر في الأسفل',
      deleteRow: 'حذف السلسلة',
      withHeadings: 'مع العناوين الرئيسية',
      withoutHeadings: 'لا توجد عناوين'
    },
    image: {
      name: 'الصورة',
      withBorder: 'مع الإطار',
      stretchImage: 'مدد الصورة',
      withBackground: 'مع الخلفية',
      selectImage: 'اختر صورة',
      uploadError: 'فشل تحميل الصورة، جرب صورة أخرى',
      caption: 'وصف الصورة',
      withCaption: 'مع وصف'
    },
    carousel: {
      name: 'شبكة الصور',
      caption: 'وصف الصورة',
      addImage: 'اختر صورة'
    },
    list: {
      name: 'القائمة',
      ordered: 'مرقمة',
      unordered: 'غير مرقمة',
      checklist: 'قائمة المراجعة'
    },
    header: {
      heading1: 'العنوان H1',
      heading2: 'العنوان H2',
      heading3: 'العنوان H3',
      heading4: 'الرأس H4'
    },
    embed: { caption: 'وصف الرابط' },
    convertTo: 'التحويل إلى',
    moveUp: 'تحرك للأعلى',
    moveDown: 'تحرك لأسفل',
    anchor: 'مرساة',
    clickToTune: 'اضغط للتهيئة',
    dragToMove: 'أو السحب والإفلات',
    filter: 'بحث',
    nothingFound: 'لم يتم العثور على شيء'
  },
  alerts: {
    loginSuccess: 'تسجيل دخول ناجح',
    loginError: 'خطأ أثناء التفويض',
    registerSuccess: 'التسجيل الناجح على الموقع الإلكتروني',
    registerError: 'خطأ في التسجيل',
    restoreSuccess: 'تم إرسال تعليمات الاسترداد إلى صندوق البريد الخاص بك',
    restoreError: 'خطأ عند استعادة الوصول إلى الحساب',
    signoutSuccess: 'تم تسجيل الخروج بنجاح',
    postLoadError: 'خطأ عند تحميل منشور',
    postShareSuccess: 'رابط المنشور المنسوخ إلى الحافظة',
    copyCommandSuccess: 'يتم نسخ الأمر إلى الحافظة',
    restoreAccountSuccess: 'تم استرداد الحساب بنجاح',
    restoreAccountError: 'خطأ عند استعادة الوصول إلى الحساب',
    verificateAccountSuccess: 'تم تأكيد صندوق البريد بنجاح',
    verificateAccountError: 'خطأ في تأكيد صندوق البريد',
    dataUpdateSuccess: 'التحديث الناجح للمعلومات',
    dataUpdateError: 'خطأ عند تحديث المعلومات',
    dataDeleteSuccess: 'الحذف الناجح للمعلومات',
    dataDeleteError: 'خطأ عند حذف المعلومات',
    dataAddSuccess: 'الإضافة الناجحة للمعلومات',
    dataAddError: 'خطأ عند إضافة معلومات',
    mapMoveInfo: 'نقل النقطة إلى موضع آخر وحفظها',
    mapCoordinatesCopySuccess: 'تم نسخ الإحداثيات بنجاح',
    copyTextSuccess: 'تم نسخ النص بنجاح'
  },
  mail: {
    verificate: {
      subject: 'تأكيد عنوان البريد الإلكتروني',
      title: 'التأكيد',
      description: 'للتحقق من حقوق حسابك وتأكيدها، عليك النقر على الزر أدناه. الرابط نشط لمدة 24 ساعة.'
    },
    restore: {
      subject: 'استعادة الوصول إلى الحساب',
      title: 'الترميم',
      description: 'لاستعادة الوصول إلى حسابك، تحتاج إلى النقر على الزر أدناه. الرابط نشط لمدة 24 ساعة.'
    },
    restoreSuccess: {
      subject: 'استعادة الوصول إلى الحساب بنجاح',
      title: 'التعافي الناجح',
      description: 'لقد قمت بتغيير كلمة المرور الخاصة بك على الموقع بنجاح. إذا لم تقم بذلك، يرجى تغيير كلمة المرور الخاصة بك على الفور!'
    },
    verificateSuccess: {
      subject: 'استعادة الوصول إلى الحساب بنجاح',
      title: 'تأكيد ناجح',
      description: 'لقد قمت بتأكيد صندوق بريدك بنجاح، مرحباً بك!'
    },
    caption: 'البريد الإلكتروني عبارة عن إشعار ويتم إنشاؤه تلقائيًا، يُرجى عدم الرد عليه.'
  },
  fields: {
    price: 'التكلفة',
    buyPrice: 'تكلفة الشراء',
    sellPrice: 'تكلفة البيع',
    mcPrice: 'تكلفة الشراء في MC',
    money: 'العملة',
    coin: 'ماجيستيك كوين',
    gift: 'هدية',
    default: 'قياسي',
    unique: 'فريد من نوعه',
    event: 'الحدث',
    battlePass: 'تذكرة المعركة',
    lootbox: 'الحالة',
    capacityKg: 'القدرة الاستيعابية',
    email: 'البريد',
    password: 'كلمة المرور',
    username: 'الاسم المستعار',
    weightKg: 'كجم',
    speed: 'السرعة',
    maxSpeed: 'السرعة القصوى',
    maxSpeedFT: 'السرعة القصوى (FT)',
    accelerationTo100: 'التسارع حتى 100 كم/ساعة',
    speedSystem: 'كم/ساعة',
    timeSystem: 'ثانية',
    uploadDescription: 'المس أو اسحب صورة إلى منطقة ما',
    placeholder: 'أدخل قيمة في الحقل',
    imagePlaceholder: 'أدخل رابطاً للصورة',
    selectPlaceholder: 'حدد قيمة من القائمة',
    videoPlaceholder: 'أدخل رابط الفيديو',
    soundPlaceholder: 'أدخل رابطاً للصوت',
    id: 'بطاقة الهوية',
    serial: 'الرقم التسلسلي',
    kind: 'النوع',
    category: 'الفئة',
    name: 'العنوان',
    view: 'عرض',
    layer: 'الطبقة',
    icon: 'أيقونة',
    circle: 'نقطة',
    polygon: 'مضلع',
    emoji: 'الرموز التعبيرية',
    description: 'الوصف',
    gender: 'الجنس',
    value: 'نوع القيمة',
    valueName: 'وصف السعر الإضافي',
    visibility: 'إمكانية الوصول',
    visibilityPrivate: 'وصول مقيد',
    visibilityPublic: 'الرؤية للجميع',
    activityHidden: 'مخفي عن أعين العامة',
    activityVisible: 'متاح للجميع',
    garageSlots: 'فتحات المرآب',
    maxTenants: 'عدد السكان',
    gasoline: 'الوقود',
    brand: 'العلامة التجارية',
    model: 'الطراز',
    trunk: 'الحذاء',
    tradable: 'قابلية التحويل',
    buyable: 'خيار الشراء',
    upgradesPriceFT: 'التحسينات المتاحة (FT)',
    upgradesEngine: 'المحرك',
    upgradesBreaks: 'المكابح',
    upgradesSuspension: 'التعليق',
    upgradesTurbo: 'تيربو',
    upgradesTransmission: 'الصندوق',
    paintingsMainPrice: 'الطلاء الأساسي',
    paintingsSubPrice: 'طلاء إضافي',
    paintingsOtherPrice: 'اللوحات الأخرى',
    paintingsBrightMetallic: 'معدني لامع',
    paintingsMetallic: 'معدنية',
    paintingsRichMetallic: 'معدني غني بالمعادن',
    paintingsDarkMetallic: 'معدني داكن اللون',
    paintingsMatte: 'ماتي',
    paintingsMatteMetallic: 'معدن غير لامع',
    paintingsSatin: 'ساتين',
    paintingsMetal: 'معدن',
    paintingsShadowChrome: 'ظل الكروم',
    paintingsPureChrome: 'كروم نقي',
    paintingsPearl: 'ظل لؤلؤي اللون',
    paintingsWheels: 'لون القرص',
    paintingsDetails: 'لون الأجزاء',
    paintingsAdditionalDetails: 'لون إضافي للأجزاء',
    paintingsLights: 'ضوء المصباح الأمامي',
    paintingsSmoke: 'لون دخان الإطارات',
    paintingsNeon: 'لون النيون',
    total: 'الإجمالي',
    language: 'اللغة',
    radius: 'نصف القطر',
    color: 'اللون',
    colorPlaceholder: 'اختر لوناً',
    opacity: 'الشفافية',
    alias: 'اسم مستعار',
    aliases: 'الأسماء المستعارة',
    looped: 'حلقي',
    battlePassId: 'معرّف بطاقة المعركة',
    gasolineCapacityLiter: 'л',
    gasolineCapacity: 'سعة الخزان',
    source: 'المصدر',
    sources: 'المصادر',
    priceDescription: 'وصف التكلفة',
    layerName: 'اسم الطبقة',
    layerNamePlaceholder: 'أدخل اسماً للطبقة',
    layerDescription: 'وصف الطبقة',
    layerDescriptionPlaceholder: 'أدخل وصفاً للطبقة',
    layerColor: 'لون الطبقة',
    layerColorPlaceholder: 'اختر لوناً',
    layerOpacity: 'شفافية الطبقة',
    zIndexOffset: 'مستوى مؤشر Z',
    zIndexOffsetPlaceholder: 'أدخل قيمة مستوى الفهرس Z-index',
    comment: 'التعليق'
  },
  vehiclesPage: {
    meta: {
      title: 'وسائل النقل والسيارات في GTA5 RP RTA5 | ويكي المهيبة',
      description: 'كتالوج كامل للمركبات على الخادم Majestic RP في GTA5 RP. الخصائص التفصيلية للسيارات والدراجات النارية والمروحيات والطائرات والقوارب. تعرف على الأسعار والسرعة والقدرة على المناورة وسعة صندوق الأمتعة وغيرها من المعايير. ماجستيك موتورز، سيارات، شاحنات، شاحنات، مروحيات، قوارب، دراجات نارية، دراجات - كل ذلك في GTA5 RP!',
      custom: {
        title: '{type} | GTA5 RP Transport | ويكي ماجستيك',
        description: 'كتالوج مفصل {type} على الخادم Majestic RP في GTA5 RP. الخصائص والأسعار والسرعة والقدرة على المناورة وغيرها من المعلمات. معلومات كاملة عن النقل في GTA5 RP!'
      }
    },
    default: 'قياسي',
    donate: 'ماجيستيك كوين',
    battlePass: 'تذكرة المعركة',
    case: 'الحالة',
    unique: 'فريد من نوعه',
    title: 'جدول النقل',
    search: 'أداة البحث عن وسائل النقل...',
    irl: 'ماجستيك موتورز',
    car: 'السيارات',
    freight: 'الشاحنات',
    helicopter: 'طائرات الهليكوبتر',
    plane: 'Самолеты',
    boat: 'القوارب',
    motorbike: 'الدراجات النارية',
    bike: 'الدراجات الهوائية',
    trailer: 'المقطورات',
    military: 'العسكرية',
    special: 'التخصص',
    priceDisclaimer: 'قد تختلف التكلفة النهائية بسبب العمولة في LSC على الخادم الخاص بك',
    basicInformation: 'معلومات أساسية',
    additionalInformation: 'معلومات إضافية',
    compare: 'قارن',
    compareLimit: 'يمكن مقارنة 5 من مراكز التعاون التقني كحد أقصى',
    pagingText: 'وجد{N, plural, few{حول} many{حول ~ حول}} {N} ~ {N, plural, one{النقل} few{النقل} many{النقل} other{النقل}}~'
  },
  vehiclePage: {
    meta: {
      title: 'GTA5 RP Transport | ويكي ماجستيك ويكي',
      description: 'معلومات مفصلة عن النقل على الخادم Majestic RP في GTA5 RP. الخصائص والأسعار والتحسينات والطلاء. تعرف على السرعة القصوى، والتسارع، وسعة صندوق الأمتعة، وتكلفة التحسينات وغيرها من المعلمات لكل مركبة.',
      custom: {
        title: '{name} | GTA5 RP Transport | ويكي ماجستيك',
        description: 'معلومات مفصلة عن {name} على الخادم Majestic RP في GTA5 RP. الميزات والأسعار والتحسينات ووظائف الطلاء. تعرّف على السرعة القصوى والتسارع وسعة التمهيد وتكلفة التحسينات وغيرها من المعلمات.'
      }
    },
    backToTable: 'العودة إلى القائمة',
    labelsUniqueId: 'المعرف الفريد',
    labelsPrice: 'التكلفة',
    labelsBuyPrice: 'تكلفة الشراء',
    labelsSellPrice: 'تكلفة البيع',
    labelsMaxSpeed: 'السرعة القصوى',
    labelsMaxSpeedFT: 'السرعة القصوى (FT)',
    labelsAccelerationTo100: 'التسارع حتى 100 كم/ساعة',
    labelsTradable: 'قابلية التحويل',
    labelsTrunk: 'سعة التمهيد',
    labelsGasolineCapacity: 'سعة الخزان',
    labelsGasolineType: 'نوع الوقود',
    upgradesTitle: 'تحسينات النقل',
    upgradesDescription: 'تكلفة الترقيات إلى المستوى الأقصى',
    upgradesEngine: 'المحرك',
    upgradesEngineDescription: 'أطلق العنان للإمكانات الحقيقية للنقل',
    upgradesBreaks: 'المكابح',
    upgradesBreaksDescription: 'تحكم كامل في السرعة على أي طريق',
    upgradesTurbo: 'تيربو',
    upgradesTurboDescription: 'تسارع انفجاري في أي لحظة',
    upgradesTransmission: 'الصندوق',
    upgradesTransmissionDescription: 'تبديل سريع للغاية لجميع التروس',
    upgradesSuspension: 'التعليق',
    upgradesSuspensionDescription: 'راحة مثالية بالارتفاع مناسب',
    upgradesTotal: 'تكلفة التحسينات',
    upgradesDisclaimer: 'قد تختلف التكلفة النهائية بسبب العمولة في LSC على الخادم الخاص بك',
    colorsTitle: 'طلاء المركبات',
    colorsDescription: 'أنواع وأنواع الطلاء المتاحة مع التكلفة',
    paintingsMainPrice: 'الطلاء الأساسي',
    paintingsSubPrice: 'طلاء إضافي',
    paintingsOtherPrice: 'اللوحات الأخرى',
    paintingsBrightMetallic: 'معدني لامع',
    paintingsMetallic: 'معدنية',
    paintingsRichMetallic: 'معدني غني بالمعادن',
    paintingsDarkMetallic: 'معدني داكن اللون',
    paintingsMatte: 'ماتي',
    paintingsMatteMetallic: 'معدن غير لامع',
    paintingsSatin: 'ساتين',
    paintingsMetal: 'معدن',
    paintingsShadowChrome: 'ظل الكروم',
    paintingsPureChrome: 'كروم نقي',
    paintingsPearl: 'ظل لؤلؤي اللون',
    paintingsWheels: 'لون القرص',
    paintingsDetails: 'لون الأجزاء',
    paintingsAdditionalDetails: 'لون إضافي للأجزاء',
    paintingsLights: 'ضوء المصباح الأمامي',
    paintingsSmoke: 'لون دخان الإطارات',
    paintingsNeon: 'لون النيون',
    paintingsTotal: 'تكلفة الطلاء النهائي',
    shareLink: 'مشاركة رابط النقل',
    shareLinkCopied: 'تم نسخ رابط النقل بنجاح',
    addToCompare: 'إضافة إلى المقارنة',
    addedToCompare: 'تمت إضافة النقل إلى المقارنة',
    goToCompare: 'انتقل إلى المقارنة',
    removeFromCompare: 'إزالة من المقارنة',
    removedFromCompare: 'تمت إزالة النقل من المقارنة'
  },
  vehiclesComparePage: {
    meta: {
      title: 'مقارنة بين وسائل نقل GTA5 RP | ويكي المهيبة',
      description: 'قارن بين ميزات وأسعار المركبات المختلفة على خادم Majestic RP في GTA5 RP. اكتشف السيارة التي تناسب احتياجاتك بشكل أفضل! قارن بين السرعة والسعر والسعة وغيرها من المعايير.'
    },
    name: 'العنوان',
    type: 'النوع',
    brand: 'العلامة التجارية',
    kits: 'الحزم',
    model: 'الطراز',
    trunkCapacity: 'سعة التمهيد',
    maxSpeed: 'السرعة القصوى',
    maxSpeedFT: 'السرعة القصوى (FT)',
    price: 'التكلفة',
    donatePrice: 'التكلفة في ماجستيك كوين',
    sellPrice: 'تكلفة البيع',
    sellPriceBeforeTax: 'تكلفة البيع (قبل الضريبة)',
    sellPriceWithDiscount: 'تكلفة البيع (مخفضة)',
    accelerationTo100: 'زمن التسارع إلى 100 كم/ساعة',
    gasolineCapacity: 'سعة الخزان',
    gasolineType: 'نوع الوقود',
    isTradable: 'قابلية التحويل',
    isBuyable: 'خيار الشراء',
    createdAt: 'تاريخ الظهور',
    gasolineCapacityUnit: 'л',
    maxSpeedUnit: 'كم/ساعة',
    maxSpeedFTUnit: 'كم/ساعة',
    priceUnit: '₽',
    accelerationTo100Unit: 'ثانية',
    trunkCapacityUnit: 'كجم',
    showOnlyDifferent: 'اعرض فقط ما هو مختلف',
    showAll: 'عرض الكل',
    share: 'مشاركة مقارنة',
    linkCopied: 'تم نسخ رابط المقارنة بنجاح',
    openVehicle: 'افتح صفحة النقل',
    deleteVehicle: 'إزالة مركبة من المقارنة',
    noDifferences: 'لا يوجد تمييز'
  },
  skinsPage: {
    meta: {
      title: 'هيئات أسلحة GTA5 RP | ويكي المهيبة',
      description: 'كتالوج كامل لجلود الأسلحة على الخادم Majestic RP في GTA5 RP. تعرف على كيفية الحصول على كل مظهر وتكلفته وميزات كل مظهر لأنواع مختلفة من الأسلحة.',
      custom: {
        title: 'الجلود في {type} | GTA5 RP | GTA5 RP | ماجستيك ويكي',
        description: 'كتالوج الجلود {type} على الخادم Majestic RP في GTA5 RP. معلومات مفصلة عن كيفية الحصول على الجلود وتكلفتها وميزاتها.'
      }
    },
    title: 'جدول الجلد',
    search: 'البحث عن البشرة...',
    apPistol: 'مسدس خارق للدروع',
    assaultRifle: 'بندقية هجومية',
    assaultRifleMk2: 'بندقية هجومية Mk2',
    bullpupRifle: 'بندقية من طراز Bullpup',
    carbineRifle: 'بندقية كاربين',
    carbineRifleMk2: 'بندقية كاربين Mk2',
    combatPDW: 'مسدس PDW القتالي',
    gusenberg: 'رشاش قصير من طراز طومسون',
    heavyPistol: 'مدفع ثقيل',
    heavyShotgun: 'بندقية ثقيلة',
    heavySniper: 'بندقية قنص ثقيلة.',
    heavySniperMk2: 'بندقية قنص ثقيلة Mk2',
    lightvest: 'دروع واقية للجسم',
    machinePistol: 'باء صغيرة',
    marksmanPistol: 'مسدس الرامي',
    microSMG: 'جهاز إس إم جي الصغير',
    militaryRifle: 'بندقية عسكرية',
    revolver: 'المسدس',
    smgMk2: 'SMG Mk2',
    specialCarbine: 'حلقة تسلق خاصة',
    vintagePistol: 'مسدس عتيق',
    combatMgMk2: 'رشاش من طراز Mk2',
    heavyRifle: 'بندقية ثقيلة',
    tacticalRifle: 'بندقية تكتيكية',
    tecPistol: 'مسدس الرشاش التكتيكي',
    noName: 'الجلد #{id}',
    battlePass: 'تذكرة المعركة',
    case: 'الحالة',
    unique: 'فريد من نوعه',
    pagingText: 'وجدت{N, plural, few{حول} many{حول ~ حول}} {N} ~ {N, plural, one{~ الجلد} few{الجلد} many{الجلد} other{الجلد}}'
  },
  animationsPage: {
    meta: {
      title: 'رسوم GTA5 RP المتحركة | ويكي المهيبة',
      description: 'كتالوج كامل للرسوم المتحركة على الخادم Majestic RP في GTA5 RP. جميع أنواع الرسوم المتحركة: الحركات والوضعيات والرقصات والرسوم المتحركة الحصرية. تعرف على كيفية الحصول عليها وتكلفة كل رسم متحرك.',
      custom: {
        title: '{type} | رسوم GTA5 RP المتحركة | ويكي المهيبة',
        description: 'كتالوج {type} الرسوم المتحركة على الخادم Majestic RP في GTA5 RP. معلومات مفصلة عن كيفية الحصول على الرسوم المتحركة وتكلفتها واستخدامها.'
      }
    },
    title: 'جدول الرسوم المتحركة',
    search: 'بحث عن الرسوم المتحركة...',
    default: 'قياسي',
    donate: 'ماجيستيك كوين',
    battlePass: 'تذكرة المعركة',
    case: 'الحالة',
    unique: 'فريد من نوعه',
    action: 'الإجراءات',
    pose: 'الوضعيات',
    positive: 'موجب',
    negative: 'السلبيات',
    dances: 'الرقص',
    etc: 'أخرى',
    exclusive: 'حصرياً',
    noName: 'الرسوم المتحركة #{id}',
    pagingText: 'Найден{N, plural, one{а} few{о} many{о}} {N} {N, plural, one{анимация} few{анимации} many{анимаций} other{анимаций}}'
  },
  realtyPage: {
    meta: {
      title: 'عقارات GTA5 RP العقارية | ويكي ماجستيك',
      description: 'كتالوج كامل للعقارات على الخادم Majestic RP في GTA5 RP. المنازل والشقق والمكاتب والمستودعات مع الخصائص التفصيلية والأسعار والموقع. تعرف على عدد المستأجرين ومساحات المرآب وغيرها من المعايير.',
      custom: {
        title: '{type} | عقارات GTA5 RP العقارية | ويكي ماجستيك',
        description: 'كتالوج {type} على الخادم Majestic RP في GTA5 RP. معلومات مفصلة عن خصائص العقار وأسعاره وموقعه ومميزاته.'
      }
    },
    title: 'جدول الممتلكات',
    search: 'البحث عن عقار...',
    majesticMotors: 'ماجستيك موتورز',
    house: 'في المنزل',
    apartment: 'الشقق',
    office: 'المكاتب',
    warehouse: 'المستودعات',
    tenantsFor: '{N} {N, plural, one{المستأجر} few{المستأجر ~ المستأجر} many{المستأجرين} other{المستأجرين}}',
    garageSlotsFor: '{N} {N, plural, one{مساحة المرآب} few{مساحات المرآب} many{مساحات المرآب} other{مساحات المرآب}}',
    viewOnMap: 'عرض على الخريطة',
    mapPosition: 'الموقع',
    pagingText: 'وجدت{N, plural, few{حول} many{حول ~ حول}} {N} {N, plural, one{~ عقارات} few{عقارات} many{عقارات} other{عقارات}}~',
    noName: 'عقارات #{id}'
  },
  postsPage: {
    meta: {
      title: 'تحرير منشور | ماجستيك ويكي',
      description: 'إنشاء وتحرير منشورات على ويكي ماجستيك لـ GTA5 RP. نشر معلومات محدثة عن النقل والممتلكات والأعمال التجارية وغيرها من جوانب اللعبة.'
    },
    titlePlaceholder: 'أدخل عنوان المنشور',
    category: 'الفئة',
    noCategory: 'غير مصنف',
    availability: 'إمكانية الوصول',
    allView: 'الرؤية للجميع',
    privateView: 'وصول مقيد',
    relevance: 'الصلة بالموضوع',
    relevanceData: 'معلومات محدثة',
    outdatedData: 'معلومات قديمة',
    categoryPosition: 'منصب الفئة',
    categoryPositionDescription: 'على سبيل المثال، "50" أو "-5".',
    postDeleted: 'تم حذف المشاركة',
    author: 'كاتب المقال',
    editor: 'محرر المقالات',
    contents: 'محتوى المقال',
    readingTime: 'وقت القراءة',
    readingTimeLength: '{N} {N, plural, one{دقيقة} few{دقائق ~ دقائق} many{دقائق} other{دقائق}}',
    publishDate: 'منشور من {date}',
    views: '{N} {N, plural, one{عرض} few{عرض ~ ~ عرض} many{عرض} other{عرض}}',
    youLiked: 'لقد أعجبتك',
    youAndPeopleLiked: 'أعجبك {likes} أيضاً',
    peopleLiked: '{N} {N, plural, one{أعجبني} few{أعجبني ~ أعجبني} many{أعجبني} other{أعجبني}}',
    categories: 'الفئات',
    posts: 'المنشورات',
    foundMistake: 'هل وجدت خطأ في المقال؟',
    language: 'اللغة'
  },
  panelPage: {
    users: {
      title: 'إدارة المستخدم | ماجستيك ويكي',
      heading: 'إدارة المستخدم',
      admin: 'المدير',
      user: 'المستخدم',
      customUser: 'المستخدم صاحب الحقوق',
      search: 'ابدأ بكتابة استعلام البحث الخاص بك....',
      comment: 'التعليق',
      pagingText: 'تم العثور على{N, plural, few{حول} many{حول ~ حول}} {N} ~ {N, plural, one{مستخدم} few{مستخدم} many{مستخدمين} other{مستخدمين}}'
    },
    posts: {
      title: 'إدارة البريد | ماجستيك ويكي',
      heading: 'إدارة الوظائف',
      views: '{N} {N, plural, one{عرض} few{عرض ~ ~ عرض} many{عرض} other{عرض}}',
      likes: '{N} {N, plural, one{أعجبني} few{أعجبني ~ أعجبني} many{أعجبني} other{أعجبني}}'
    },
    categories: {
      title: 'إدارة الفئات | ماجستيك ويكي',
      heading: 'إدارة الفئات'
    },
    mapLayers: {
      title: 'إدارة طبقات الخريطة | ماجستيك ويكي',
      heading: 'إدارة طبقات الخريطة'
    },
    logs: {
      title: 'تدقيق السجل - ماجستيك ويكي',
      heading: 'تدقيق السجلات',
      search: 'ابدأ بكتابة استعلام البحث الخاص بك....',
      biz: 'الأعمال التجارية',
      category: 'الفئة',
      clothing: 'الملابس',
      file: 'ملف',
      item: 'الموضوع',
      mapItem: 'عنصر الخريطة',
      mapLayer: 'طبقة الخريطة',
      post: 'المنشور',
      realty: 'العقارات',
      skin: 'الجلد',
      user: 'المستخدم',
      vehicle: 'النقل والمواصلات',
      animation: 'الرسوم المتحركة',
      actions: {
        unknown: '{username} قام بعمل غير معروف',
        bizCreate: '{username} إضافة عمل {id}',
        bizUpdate: '{username} تغيير العمل {id}',
        bizDelete: '{username} حذف العمل {id}',
        categoryCreate: '{username} إضافة الفئة {id}',
        categoryUpdate: '{username} تغيير الفئة {id}',
        categoryDelete: '{username} الفئة المحذوفة {id}',
        clothingCreate: '{username} الملابس المضافة {id}',
        clothingUpdate: '{username} تغيير الملابس {id}',
        clothingDelete: '{username} حذف الملابس {id}',
        fileCreate: '{username} إضافة ملف {id}',
        fileUpdate: '{username} تعديل الملف {id}',
        fileDelete: '{username} حذف الملف {id}',
        itemCreate: '{username} إضافة العنصر {id}',
        itemUpdate: '{username} تغيير الموضوع {id}',
        itemDelete: '{username} حذف العنصر {id}',
        mapItemCreate: '{username} تمت إضافة عنصر إلى الخريطة {id} إلى "{layer}"',
        mapItemUpdate: '{username} تغيير العنصر على الخريطة {id} إلى "{layer}"',
        mapItemDelete: '{username} حذف عنصر على الخريطة {id} إلى "{layer}"',
        mapLayerCreate: '{username} إضافة طبقة خريطة {id}',
        mapLayerUpdate: '{username} تغيير طبقة الخريطة {id}',
        mapLayerDelete: '{username} حذف طبقة الخريطة {id}',
        postCreate: '{username} إضافة منشور {id}',
        postUpdate: '{username} المنشور المعدل {id}',
        postDelete: '{username} تم حذف المنشور المحذوف {id}',
        realtyCreate: '{username} إضافة الخاصية {id}',
        realtyUpdate: '{username} العقارات المتغيرة {id}',
        realtyDelete: '{username} العقارات المحذوفة {id}',
        skinCreate: '{username} إضافة الجلد {id}',
        skinUpdate: '{username} تغيير الجلد {id}',
        skinDelete: '{username} حذف الجلد {id}',
        userCreate: '{username} إضافة المستخدم {id}',
        userUpdate: '{username} تغيير المستخدم {id}',
        userDelete: '{username} حذف المستخدم {id}',
        vehicleCreate: '{username} النقل المضافة {id}',
        vehicleUpdate: '{username} تغيير النقل {id}',
        vehicleDelete: '{username} حذف النقل {id}',
        animationCreate: '{username} إضافة رسوم متحركة {id}',
        animationUpdate: '{username} تغيير الرسوم المتحركة {id}',
        animationDelete: '{username} حذف الرسوم المتحركة {id}',
        changelogCreate: '{username} سجل التغييرات المضافة {id}',
        changelogUpdate: '{username} تغيير سجل التغييرات {id}',
        changelogDelete: '{username} سجل التغييرات المحذوف {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: 'الإعدادات',
      users: 'المستخدمون',
      categories: 'الفئات',
      mapLayers: 'طبقات الخريطة',
      posts: 'المنشورات',
      logs: 'السجلات'
    }
  },
  changelogPage: {
    titlePlaceholder: 'أدخل اسم التغيير',
    descriptionPlaceholder: 'أدخل وصفاً للتغيير',
    availableAt: 'تاريخ النشر',
    soon: 'من خلال',
    ago: 'إلى الوراء',
    fix: 'تم التصحيح',
    feat: 'تمت الإضافة',
    chore: 'تم التعديل',
    style: 'التصميم',
    refactor: 'معاد تدويره'
  },
  changelogsPage: {
    meta: {
      title: 'تاريخ التغييرات | ماجستيك ويكي',
      description: 'التاريخ الكامل للتحديثات والتغييرات التي طرأت على خادم Majestic RP. ترقب الميزات الجديدة وتحسينات اللعب وإصلاحات الأخطاء والموازنة. معلومات محدثة عن جميع تحديثات المشروع.'
    },
    title: 'تاريخ التغييرات',
    search: 'إيجاد التغيير...'
  },
  homePage: {
    meta: {
      title: 'الويكي المهيب | موسوعة GTA5 RP موسوعة GTA5 RP',
      description: 'ويكي ماجستيك هي موسوعة كاملة للاعبي GTA5 RP. معلومات عن النقل والعقارات والشركات والمهام وغيرها من جوانب اللعبة. معلومات محدثة عن الأسعار والميزات وكيفية الحصول عليها.'
    },
    tools: {
      map: {
        title: 'الخريطة',
        description: 'خريطة الخادم مع جميع المواقع'
      },
      vehicles: {
        title: 'النقل والمواصلات',
        description: 'جميع وسائل النقل المتاحة على الخادم'
      },
      realty: {
        title: 'العقارات',
        description: 'بيانات جميع المنازل والشقق السكنية'
      },
      biz: {
        title: 'الشركات',
        description: 'خيار الكسب المتقدم'
      },
      clothesMale: {
        title: 'ملابس رجالية',
        description: 'عرض أزياء للرجال'
      },
      clothesFemale: {
        title: 'ملابس نسائية',
        description: 'عرض أزياء للنساء'
      },
      skins: {
        title: 'الجلود',
        description: 'كتالوج بجميع الأنماط المتاحة'
      },
      items: {
        title: 'الموضوعات',
        description: 'العناصر المتوفرة على الخادم'
      },
      servers: {
        title: 'الخوادم',
        description: 'قائمة بجميع خوادم RP Majestic RP'
      },
      animations: {
        title: 'الرسوم المتحركة',
        description: 'رسوم متحركة فريدة على الخادم'
      },
      changelogs: {
        title: 'تاريخ التغييرات',
        description: 'تاريخ التغييرات في المشروع'
      }
    },
    search: {
      title: 'الفئات العامة',
      description: 'هنا يمكنك العثور على أي معلومات حول الخادم Majestic وأنظمته'
    },
    categories: {
      collapseList: 'طي القائمة',
      expandList: 'توسيع القائمة'
    },
    online: {
      players: '{count} عبر الإنترنت',
      offline: 'إيقاف التشغيل'
    }
  },
  errorPage: {
    title: 'الصفحة غير موجودة',
    description: 'قد يكون لديك خطأ مطبعي في عنوان الصفحة، أو أنها غير موجودة :((',
    backToHome: 'الانتقال إلى الصفحة الرئيسية'
  },
  bizPage: {
    meta: {
      title: 'أعمال RP GTA5 RTA5 | ويكي المهيبة',
      description: 'كتالوج كامل للأعمال التجارية على الخادم Majestic RP في GTA5 RP. متاجر 24/7، ومحطات بنزين، ومتاجر أسلحة، وتجار سيارات وغيرها من الأعمال التجارية. تعرف على الربحية والمتطلبات وكيفية شراء كل نشاط تجاري.',
      custom: {
        title: '{type} | أعمال RP GTA5 RTA5 | ويكي المهيبة',
        description: 'معلومات مفصلة حول {type} على الخادم Majestic RP في GTA5 RP. الخصائص والربحية والمتطلبات وكيفية شراء الأعمال التجارية.'
      }
    },
    title: 'جدول الأعمال',
    search: 'البحث عن الأعمال...',
    groceryStore: 'تسوق 24/7',
    gasStation: 'إعادة التزويد بالوقود',
    atm: 'أجهزة الصراف الآلي',
    gunshop: 'متجر الأسلحة',
    clothingStore: 'متجر الملابس',
    autoShop: 'وكالة سيارات',
    tattoo: 'صالون الوشم',
    lsc: 'ضبط الصالون',
    barbershop: 'صالون الحلاقة',
    carWash: 'غسيل السيارات',
    viewOnMap: 'عرض على الخريطة',
    mapPosition: 'الموقع',
    pagingText: 'وجدت{N, plural, few{حول} many{حول ~ حول}} {N} {N, plural, one{~ الأعمال} few{الأعمال} many{الأعمال} other{الأعمال}}الأعمال',
    noName: 'الأعمال #{id}'
  },
  clothesPage: {
    meta: {
      title: 'ملابس GTA5 RP GTA5 | ويكي ماجستيك',
      description: 'كتالوج كامل للملابس على الخادم Majestic RP في GTA5 RP. الملابس الرجالية والنسائية والإكسسوارات وحقائب الظهر والرقع والقبعات وغيرها من الملابس. تعرف على كيفية الحصول على كل قطعة من الملابس ومقدار تكلفة كل قطعة من الملابس.',
      custom: {
        title: '{type} |ملابس GTA5 RP | ويكي ماجستيك',
        description: 'الكتالوج {type} على الخادم Majestic RP في GTA5 RP. معلومات مفصلة عن كيفية الحصول على الملابس واستخدامها وتكلفتها.'
      }
    },
    titleMale: 'مخطط الملابس الرجالية',
    titleFemale: 'مخطط الملابس النسائية',
    search: 'البحث عن الملابس...',
    top: 'أعلى',
    legs: 'القاع',
    shoes: 'الأحذية',
    watch: 'الساعة',
    mask: 'الأقنعة',
    decal: 'الرقعة',
    accessory: 'الإكسسوارات',
    head: 'القبعات',
    bag: 'حقائب الظهر',
    glasses: 'النظارات',
    ear: 'الأذنين',
    gloves: 'القفازات',
    undershirt: 'تي شيرتات',
    noName: 'طقم ملابس #{id}',
    drawable: 'قابل للرسم',
    texture: 'الملمس',
    male: 'الذكور',
    female: 'أنثى',
    donate: 'ماجيستيك كوين',
    battlePass: 'تذكرة المعركة',
    case: 'الحالة',
    unique: 'فريد من نوعه',
    pagingText: 'وجدت{N, plural, few{حول} many{حول ~ حول}} {N} ~ {N, plural, one{الملابس} few{الملابس} many{الملابس} other{الملابس}}'
  },
  itemsPage: {
    meta: {
      title: 'عناصر GTA5 RP | ويكي ماجستيك ويكي',
      description: 'كتالوج كامل للعناصر على الخادم Majestic RP في GTA5 RP. تحف وأسماك وخامات وخامات وخشب وصناديق ومواد وعناصر أخرى. تعرف على كيفية الحصول على كل عنصر واستخدامه وتكلفته.',
      custom: {
        title: '{type} | عناصر GTA5 RP | ويكي المهيبة',
        description: 'كتالوج {type} على الخادم Majestic RP في GTA5 RP. معلومات مفصلة عن كيفية الحصول على العناصر واستخدامها وتكلفتها.'
      }
    },
    title: 'جدول الموضوعات',
    search: 'العثور على الأشياء...',
    antique: 'التحف',
    fish: 'السمك',
    ore: 'الركاز',
    wood: 'خشب',
    box: 'الصناديق',
    material: 'المواد',
    event: 'الحدث',
    food: 'المنتجات',
    alcohol: 'الكحول',
    illegal: 'غير قانوني',
    medical: 'الطب',
    equipment: 'المعدات',
    ammunition: 'الذخيرة',
    tool: 'الأدوات',
    unique: 'فريدة من نوعها',
    vehicle: 'قطع غيار السيارات',
    others: 'متفرقات',
    default: 'قياسي',
    battlePass: 'تذكرة المعركة',
    case: 'الحالة',
    unique: 'فريد من نوعه',
    pagingText: 'وجدت{N, plural, few{حول} many{حول ~ حول}} {N} {N, plural, one{~ البند} few{البند} many{} other{البنود}}~',
    noName: 'الموضوع #{id}'
  },
  mapPage: {
    meta: {
      title: 'خريطة GTA5 RP | ويكي ماجستيك ويكي',
      description: 'خريطة تفاعلية لخادم Majestic RP في GTA5 RP. استكشاف المواقع والعثور على الكنوز والحيوانات والهواتف والقمامة. خريطة مفصلة للعالم مع إمكانية إضافة علامات ومناطق. جميع الأماكن والنقاط المهمة في مورد واحد!',
      custom: {
        title: '{type} | خريطة GTA5 RP | ويكي المهيبة',
        description: 'خريطة مفصلة لـ {type} على خادم Majestic RP في GTA5 RP. استكشف المواقع واعثر على النقاط المهمة وأنشئ علاماتك الخاصة. معلومات كاملة حول موقع الأشياء على الخريطة!'
      }
    },
    dataLoading: 'تحميل البيانات...',
    defaultPoints: 'العلامات القياسية',
    animalLayer: 'خريطة الحيوان',
    rubbishLayer: 'خريطة لصناديق القمامة',
    telephoneLayer: 'خريطة الهاتف',
    treasureLayer: 'خريطة الكنز',
    defaultLayer: 'خريطة العالم',
    allLayers: 'طبقات الخريطة',
    backToHome: 'العودة إلى الصفحة الرئيسية',
    zoomIn: 'النهج',
    zoomOut: 'اسحب للخلف',
    point: 'نقطة',
    polygon: 'المنطقة',
    addPoint: 'إضافة نقطة',
    addPolygon: 'إضافة منطقة',
    copyCoordinates: 'نسخ الإحداثيات',
    savePointPosition: 'موضع الحفظ',
    editPointPosition: 'تغيير الوضع',
    layerNamePlaceholder: 'اسم المنطقة',
    layerDescriptionPlaceholder: 'وصف المنطقة'
  },
  serversPage: {
    meta: {
      title: 'خوادم GTA5 RP على الإنترنت | ويكي ماجيستيك',
      description: 'الإحصائيات الحالية للاعبين عبر الإنترنت على الخوادم Majestic RP في GTA5 RP. الإنترنت الحالي، والذروات، وطوابير الانتظار والرسوم البيانية للنشاط. انضم إلى مشروع لعب الأدوار الأكثر شعبية!'
    },
    dataLoading: 'تحميل البيانات...',
    title: 'الخوادم عبر الإنترنت',
    online: '{count} عبر الإنترنت',
    inQueue: '{count} في قائمة الانتظار',
    peakOnline: '{count} الذروة',
    currentOnline: 'الحالي على الإنترنت',
    peakOnline24h: 'الذروة اليوم',
    peakOnlineAllTime: 'الذروة على الإطلاق',
    peakOnlineTooltip: '{percent}النسبة المئوية بالنسبة لليوم السابق',
    techWorks: 'العمل الفني',
    forBeginners: 'للمبتدئين',
    experience: '{multiplier}الخبرة x',
    newServer: 'جديد',
    seasonPassMultiplier: '{multiplier}تجربة التخطي x',
    combinedView: 'جنرال لواء',
    separateView: 'منفصلة'
  },
  research: {
    placeholder: 'ابحث عن معلومات على الموقع الإلكتروني...',
    skin: 'الجلد',
    vehicle: 'النقل والمواصلات',
    realty: 'العقارات',
    biz: 'الأعمال التجارية',
    clothing: 'الملابس',
    item: 'الموضوع',
    post: 'المنشور'
  },
  userModal: {
    login: 'تسجيل الدخول',
    signout: 'اخرج',
    register: 'التسجيل',
    registerEnd: 'التسجيل',
    restore: 'الترميم',
    restoreEnd: 'الاستعادة',
    avatarDelete: 'حذف الصورة الرمزية',
    headingUpdate: 'تعديل المستخدم #{id}',
    postAdd: 'نشر المنشورات',
    postUpdate: 'تحرير المنشورات',
    postDelete: 'حذف المنشورات',
    vehicleAdd: 'إضافة النقل',
    vehicleUpdate: 'تحرير النقل',
    vehicleDelete: 'إزالة النقل',
    realtyAdd: 'إضافة خاصية',
    realtyUpdate: 'التغييرات العقارية',
    realtyDelete: 'إزالة العقارات',
    bizAdd: 'إضافة نشاط تجاري',
    bizUpdate: 'تغيير العمل',
    bizDelete: 'إزالة عمل تجاري',
    mapItemAdd: 'إضافة عناصر إلى الخريطة',
    mapItemUpdate: 'تغيير العناصر على الخريطة',
    mapItemDelete: 'حذف العناصر الموجودة على الخريطة',
    clothingAdd: 'إضافة الملابس',
    clothingUpdate: 'تغيير الملابس',
    clothingDelete: 'إزالة الملابس',
    skinAdd: 'إضافة الجلود',
    skinUpdate: 'تغيير الجلود',
    skinDelete: 'إزالة الجلود',
    itemAdd: 'إضافة عناصر',
    itemUpdate: 'تغيير الكائنات',
    itemDelete: 'إزالة الأجسام',
    fileUpload: 'تحميل الصور',
    viewPrivate: 'عرض البيانات الخاصة',
    doMagic: 'الوصول إلى الوظائف السحرية',
    animationAdd: 'إضافة رسوم متحركة',
    animationUpdate: 'تغيير الرسوم المتحركة',
    animationDelete: 'حذف الرسوم المتحركة',
    categoryAdd: 'إضافة الفئات',
    categoryUpdate: 'تغيير الفئات المتغيرة',
    categoryDelete: 'حذف الفئات',
    mapLayerAdd: 'إضافة طبقات الخريطة',
    mapLayerUpdate: 'تغيير طبقات الخريطة',
    mapLayerDelete: 'حذف طبقات الخريطة',
    post: 'المنشور',
    skin: 'الجلد',
    biz: 'الأعمال التجارية',
    clothing: 'الملابس',
    item: 'الموضوع',
    vehicle: 'النقل والمواصلات',
    animation: 'الرسوم المتحركة',
    category: 'الفئة',
    realty: 'العقارات',
    mapLayer: 'طبقة الخريطة',
    changelog: 'سجل التغييرات'
  },
  restoreModal: {
    title: 'استعادة الحساب',
    newPassword: 'كلمة مرور جديدة',
    newPasswordRepeat: 'كرر كلمة المرور الجديدة',
    passwordConfirm: 'تغيير كلمة المرور',
    passwordError: 'كلمات المرور غير متطابقة'
  },
  bizModal: {
    titleUpdate: 'تغيير العمل #{id}',
    titleAdd: 'إضافة نشاط تجاري جديد'
  },
  categoryModal: {
    titleUpdate: 'تغيير الفئة #{id}',
    titleAdd: 'إضافة فئة جديدة'
  },
  mapLayerModal: {
    titleUpdate: '#{id}تغيير طبقة الخريطة',
    titleAdd: 'إضافة طبقة خريطة جديدة'
  },
  clothingModal: {
    titleUpdate: 'تغيير الملابس #{id}',
    titleAdd: 'إضافة ملابس جديدة',
    itemAdd: 'إضافة لون',
    itemGenerate: 'توليد كل الألوان'
  },
  itemModal: {
    titleUpdate: 'تغيير الموضوع #{id}',
    titleAdd: 'إضافة عنصر جديد'
  },
  realtyModal: {
    titleUpdate: 'العقارات المتغيرة #{id}',
    titleAdd: 'إضافة خاصية جديدة'
  },
  skinModal: {
    titleUpdate: 'تغيير الجلد #{id}',
    titleAdd: 'إضافة مظهر جديد'
  },
  vehicleModal: {
    titleUpdate: 'تغيير النقل #{id}',
    titleAdd: 'إضافة وسيلة نقل جديدة',
    titleDefault: 'معلومات حول {name}',
    itemAdd: 'إضافة مراوغة'
  },
  mapItemModal: {
    titleUpdate: 'تغيير عنصر الخريطة',
    titleAdd: 'إضافة عنصر خريطة جديد',
    titleDelete: 'حذف عنصر الخريطة',
    descriptionDelete: 'هل أنت متأكد أنك تريد إزالة العنصر من الخريطة؟ لا يمكن إلغاء هذا الإجراء.'
  },
  animationModal: {
    titleUpdate: 'تغيير الرسوم المتحركة #{id}',
    titleAdd: 'إضافة رسم متحرك جديد'
  }
};