export default {
  global: {
    all: 'Todos',
    back: 'Voltar',
    forward: 'Vamos lá',
    paging: '<PERSON><PERSON><PERSON><PERSON> {page} de {maxPage}',
    close: 'Ok',
    clear: '<PERSON><PERSON>',
    save: '<PERSON><PERSON>',
    delete: 'Remover',
    deleteYes: 'Você tem certeza sobre a remoção?',
    cancel: 'Cancelar',
    publish: 'Publicar',
    edit: 'Editar',
    share: 'Compartilhar',
    add: 'Adicionar.',
    active: 'Ativo',
    deleted: 'Excluído',
    blocked: 'Bloqueado',
    learnMore: '<PERSON><PERSON> mais',
    panel: 'Painel',
    theme: 'Assunto',
    language: 'Idioma',
    user: 'Usu<PERSON><PERSON>',
    confirm: 'Confirmar',
    restore: 'Restauração',
    yes: 'Sim',
    no: 'Não',
    new: 'Novo',
    preview: 'Prévia',
    sortPriceDesc: 'Preço decrescente',
    sortPriceAsc: 'Preço crescente',
    sortPriceMinMax: 'Faixa de preço',
    sortDateDesc: 'Em ordem decrescente de data',
    sortDateAsc: 'Em ordem crescente de data',
    noSellPrice: 'Não para compra',
    noBuyPrice: 'Não está à venda',
    noData: {
      title: 'Nada encontrado',
      description: 'Tente alterar ou simplificar sua consulta de pesquisa'
    },
    battlePass: {
      summer: 'Passe de batalha de verão',
      winter: 'Passe de batalha de inverno'
    },
    case: {
      default: 'Estojo padrão',
      vehicles: 'Estojo para carro',
      halloween: 'Caso de Halloween',
      spring: 'Pasta Spring',
      autumn: 'Pasta de outono',
      summer: 'Caso de verão',
      summerExtra: 'Um caso especial de verão',
      summerSkins: 'Estojo de pele para o verão',
      summerVehicles: 'Maleta de transporte de verão',
      winter: 'Caso de inverno',
      winterExtra: 'Um caso especial de inverno',
      winterSkins: 'Estojo de inverno com peles',
      winterVehicles: 'Maleta de transporte para o inverno',
      toys: 'Caixa de brinquedos'
    },
    subscription: { premium: 'Assinatura Premium' },
    copy: 'Cópia'
  },
  footer: { disclaimer: 'Todas as informações contidas no site são apenas para fins informativos e não constituem uma oferta pública.' },
  footer: { disclaimer: 'Todas as informações contidas no site são apenas para fins informativos e não constituem uma oferta pública.' },
  editor: {
    placeholder: 'Vamos escrever uma postagem incrível!',
    text: 'Parágrafo',
    heading: 'Cabeçalho',
    warning: 'Observação',
    quote: 'Citação',
    code: 'Código',
    delimiter: 'Separador',
    link: 'Referência',
    linkPlaceholder: 'Digite o link',
    marker: 'Marcador',
    bold: 'Negrito',
    italic: 'Itálico',
    inlineCode: 'Monoespaçado',
    underline: 'Enfatizado',
    copytext: 'Copiável',
    log: 'Logue',
    map: {
      name: 'Mapa',
      params: 'Insira os parâmetros do cartão',
      caption: 'Insira uma descrição do mapa'
    },
    table: {
      name: 'Tabela',
      addColumnLeft: 'Adicionar uma coluna à esquerda',
      addColumnRight: 'Adicionar coluna à direita',
      deleteColumn: 'Excluir coluna',
      addRowAbove: 'Adicione uma linha na parte superior',
      addRowBelow: 'Adicione uma linha na parte inferior',
      deleteRow: 'Excluir string',
      withHeadings: 'Com as manchetes',
      withoutHeadings: 'Sem manchetes'
    },
    image: {
      name: 'Imagem',
      withBorder: 'Com moldura',
      stretchImage: 'Esticar a imagem',
      withBackground: 'Com o plano de fundo',
      selectImage: 'Selecione uma imagem',
      uploadError: 'Falha ao carregar a imagem, tente outra',
      caption: 'Descrição da imagem',
      withCaption: 'Com uma descrição'
    },
    carousel: {
      name: 'Grade de imagens',
      caption: 'Descrição da imagem',
      addImage: 'Selecione uma imagem'
    },
    list: {
      name: 'Lista',
      ordered: 'Numerado',
      unordered: 'Não numerado',
      checklist: 'Lista de verificação'
    },
    header: {
      heading1: 'Título H1',
      heading2: 'Título H2',
      heading3: 'Título H3',
      heading4: 'Cabeçalho H4'
    },
    embed: { caption: 'Descrição do link' },
    convertTo: 'Converter para',
    moveUp: 'Subir',
    moveDown: 'Mover para baixo',
    anchor: 'Âncora',
    clickToTune: 'Pressione para configurar',
    dragToMove: 'ou arrastar e soltar',
    filter: 'Pesquisa',
    nothingFound: 'Nada encontrado'
  },
  alerts: {
    loginSuccess: 'Login bem-sucedido',
    loginError: 'Erro durante a autorização',
    registerSuccess: 'Registro bem-sucedido no site',
    registerError: 'Erro de registro',
    restoreSuccess: 'As instruções de recuperação foram enviadas para sua caixa de correio',
    restoreError: 'Erro ao restaurar o acesso à conta',
    signoutSuccess: 'Logout bem-sucedido',
    postLoadError: 'Erro ao fazer upload de uma postagem',
    postShareSuccess: 'Link para postagem copiado para a área de transferência',
    copyCommandSuccess: 'O comando é copiado para a área de transferência',
    restoreAccountSuccess: 'A recuperação da conta foi bem-sucedida',
    restoreAccountError: 'Erro ao restaurar o acesso à conta',
    verificateAccountSuccess: 'Caixa de correio confirmada com sucesso',
    verificateAccountError: 'Erro de confirmação da caixa de correio',
    dataUpdateSuccess: 'Atualização bem-sucedida das informações',
    dataUpdateError: 'Erro ao atualizar informações',
    dataDeleteSuccess: 'Exclusão bem-sucedida de informações',
    dataDeleteError: 'Erro ao excluir informações',
    dataAddSuccess: 'Adição bem-sucedida de informações',
    dataAddError: 'Erro ao adicionar informações',
    mapMoveInfo: 'Mova o ponto para outra posição e salve',
    mapCoordinatesCopySuccess: 'Coordenadas copiadas com sucesso',
    copyTextSuccess: 'Texto copiado com sucesso'
  },
  mail: {
    verificate: {
      subject: 'Confirmação do endereço de e-mail',
      title: 'Confirmação',
      description: 'Para verificar e confirmar os direitos da sua conta, você precisa clicar no botão abaixo. O link fica ativo por 24 horas.'
    },
    restore: {
      subject: 'Restaurar o acesso à conta',
      title: 'Restauração',
      description: 'Para restaurar o acesso à sua conta, você precisa clicar no botão abaixo. O link ficará ativo por 24 horas.'
    },
    restoreSuccess: {
      subject: 'Restaurar com êxito o acesso à conta',
      title: 'Recuperação bem-sucedida',
      description: 'Você alterou sua senha no site com sucesso. Se você não conseguiu, altere sua senha imediatamente!'
    },
    verificateSuccess: {
      subject: 'Restaurar com êxito o acesso à conta',
      title: 'Confirmação bem-sucedida',
      description: 'Você confirmou sua caixa de correio com sucesso, seja bem-vindo!'
    },
    caption: 'O e-mail é uma notificação e é gerado automaticamente, portanto, não responda a ele.'
  },
  fields: {
    price: 'Custo',
    buyPrice: 'Custo da compra',
    sellPrice: 'Custo de venda',
    mcPrice: 'Custo de compra em MC',
    money: 'Moeda',
    coin: 'Moeda Majestic',
    gift: 'Presente',
    default: 'Padrão',
    unique: 'Único',
    event: 'Evento',
    battlePass: 'Passe de Batalha',
    lootbox: 'Estojo',
    capacityKg: 'Capacidades',
    email: 'Correio eletrônico',
    password: 'Senha',
    username: 'Apelido',
    weightKg: 'kg',
    speed: 'Velocidade',
    maxSpeed: 'Velocidade máxima',
    maxSpeedFT: 'Velocidade máxima (FT)',
    accelerationTo100: 'Aceleração até 100 km/h',
    speedSystem: 'km/h',
    timeSystem: 'seg.',
    uploadDescription: 'Toque ou arraste uma imagem para uma área',
    placeholder: 'Digite um valor no campo',
    imagePlaceholder: 'Insira um link para a imagem',
    selectPlaceholder: 'Selecione um valor na lista',
    videoPlaceholder: 'Insira um link para o vídeo',
    soundPlaceholder: 'Insira um link para o áudio',
    id: 'ID',
    serial: 'Número de série',
    kind: 'Tipo',
    category: 'Categoria',
    name: 'Título',
    view: 'Ver',
    layer: 'Camada',
    icon: 'Ícone',
    circle: 'Ponto',
    polygon: 'Polígono',
    emoji: 'Emoji',
    description: 'Descrição',
    gender: 'Gênero',
    value: 'Tipo de valor',
    valueName: 'Descrição adicional do preço',
    visibility: 'Disponibilidade',
    visibilityPrivate: 'Acesso restrito',
    visibilityPublic: 'Visibilidade para todos',
    activityHidden: 'Escondido do acesso público',
    activityVisible: 'Disponível para todos',
    garageSlots: 'Vagas de garagem',
    maxTenants: 'Número de residentes',
    gasoline: 'Combustível',
    brand: 'Marca',
    model: 'Modelo',
    trunk: 'Porta-malas',
    tradable: 'Transferibilidade',
    buyable: 'Opção de compra',
    upgradesPriceFT: 'Melhorias disponíveis (FT)',
    upgradesEngine: 'Motor',
    upgradesBreaks: 'Freios',
    upgradesSuspension: 'Dispositivo de controle',
    upgradesTurbo: 'Turbo',
    upgradesTransmission: 'Caixa',
    paintingsMainPrice: 'Pintura básica',
    paintingsSubPrice: 'Pintura adicional',
    paintingsOtherPrice: 'Outra pintura',
    paintingsBrightMetallic: 'Metálico brilhante',
    paintingsMetallic: 'Metálica',
    paintingsRichMetallic: 'Metálico rico',
    paintingsDarkMetallic: 'Metálico escuro',
    paintingsMatte: 'Fosco',
    paintingsMatteMetallic: 'Metal escovado',
    paintingsSatin: 'Cetim',
    paintingsMetal: 'Metal',
    paintingsShadowChrome: 'Cor cromada',
    paintingsPureChrome: 'Cromo puro',
    paintingsPearl: 'Tonalidade perolada',
    paintingsWheels: 'Cor da roda',
    paintingsDetails: 'Cor das peças',
    paintingsAdditionalDetails: 'Cor adicional das peças',
    paintingsLights: 'Luz do farol',
    paintingsSmoke: 'A cor da fumaça do pneu',
    paintingsNeon: 'Cor de neon',
    total: 'Total',
    language: 'Idioma',
    radius: 'Raio',
    color: 'Cor',
    colorPlaceholder: 'Escolha uma cor',
    opacity: 'Transparência',
    alias: 'Pseudônimo',
    aliases: 'Apelidos',
    looped: 'Em loop',
    battlePassId: 'ID do passe de batalha',
    gasolineCapacityLiter: 'l',
    gasolineCapacity: 'Capacidade do tanque',
    source: 'Fonte',
    sources: 'Fontes',
    priceDescription: 'Descrição do custo',
    layerName: 'Nome da camada',
    layerNamePlaceholder: 'Digite um nome para a camada',
    layerDescription: 'Descrição da camada',
    layerDescriptionPlaceholder: 'Insira uma descrição para a camada',
    layerColor: 'Cor da camada',
    layerColorPlaceholder: 'Escolha uma cor',
    layerOpacity: 'Transparência da camada',
    zIndexOffset: 'Nível de índice Z',
    zIndexOffsetPlaceholder: 'Digite o valor do nível de índice Z',
    comment: 'Comentário'
  },
  vehiclesPage: {
    meta: {
      title: 'Transporte e carros do GTA5 RP | Majestic Wiki',
      description: 'Catálogo completo de veículos no servidor Majestic RP no GTA5 RP. Características detalhadas de carros, motocicletas, helicópteros, aviões e barcos. Descubra os preços, a velocidade, a capacidade de manobra, a capacidade do porta-malas e outros parâmetros. Majestic Motors, Carros, Caminhões, Helicópteros, Barcos, Motocicletas, Bicicletas - tudo para o GTA5 RP!',
      custom: {
        title: '{type} | Transporte RP do GTA5 | Majestic Wiki',
        description: 'Catálogo detalhado {type} no servidor Majestic RP no GTA5 RP. Características, preços, velocidade, manobrabilidade e outros parâmetros. Informações completas sobre o transporte para o GTA5 RP!'
      }
    },
    default: 'Padrão',
    donate: 'Moeda Majestic',
    battlePass: 'Passe de Batalha',
    case: 'Estojo',
    unique: 'Único',
    title: 'Tabela de transporte',
    search: 'Localizador de transporte...',
    irl: 'Motores Majestic',
    car: 'Carros de passageiros',
    freight: 'Caminhões',
    helicopter: 'Helicópteros',
    plane: 'Самолеты',
    boat: 'Barcos',
    motorbike: 'Motocicletas',
    bike: 'Bicicletas',
    trailer: 'reboques',
    military: 'Militar',
    special: 'Especialidade',
    priceDisclaimer: 'O custo final pode variar devido a uma comissão no LSC em seu servidor',
    basicInformation: 'Informações básicas',
    additionalInformation: 'Informações adicionais',
    compare: 'Compare',
    compareLimit: 'Um máximo de 5 TCs pode ser comparado',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{transporte} few{transporte} many{transporte} other{transporte}}'
  },
  vehiclePage: {
    meta: {
      title: 'Transporte RP do GTA5 | Majestic Wiki',
      description: 'Informações detalhadas sobre o transporte no servidor Majestic RP em GTA5 RP. Características, preços, melhorias e pintura. Saiba mais sobre a velocidade máxima, a aceleração, a capacidade do porta-malas, o custo das melhorias e outros parâmetros de cada veículo.',
      custom: {
        title: '{name} | Transporte RP do GTA5 | Majestic Wiki',
        description: 'Informações detalhadas sobre {name} no servidor Majestic RP em GTA5 RP. Características, preços, melhorias e pinturas. Saiba mais sobre velocidade máxima, aceleração, capacidade de inicialização, custo de melhorias e outros parâmetros.'
      }
    },
    backToTable: 'Voltar à lista',
    labelsUniqueId: 'ID exclusivo',
    labelsPrice: 'Custo',
    labelsBuyPrice: 'Custo da compra',
    labelsSellPrice: 'Custo de venda',
    labelsMaxSpeed: 'Velocidade máxima',
    labelsMaxSpeedFT: 'Velocidade máxima (FT)',
    labelsAccelerationTo100: 'Aceleração até 100 km/h',
    labelsTradable: 'Transferibilidade',
    labelsTrunk: 'Capacidade de inicialização',
    labelsGasolineCapacity: 'Capacidade do tanque',
    labelsGasolineType: 'Tipo de combustível',
    upgradesTitle: 'Melhorias no transporte',
    upgradesDescription: 'Custo dos upgrades para o nível máximo',
    upgradesEngine: 'Motor',
    upgradesEngineDescription: 'Desbloqueie o verdadeiro potencial do transporte',
    upgradesBreaks: 'Freios',
    upgradesBreaksDescription: 'Controle total de velocidade em qualquer estrada',
    upgradesTurbo: 'Turbo',
    upgradesTurboDescription: 'Aceleração explosiva a qualquer momento',
    upgradesTransmission: 'Caixa',
    upgradesTransmissionDescription: 'Mudança de marcha extremamente rápida em todas as marchas',
    upgradesSuspension: 'Dispositivo de controle',
    upgradesSuspensionDescription: 'Conforto perfeito na altura certa',
    upgradesTotal: 'Custo das melhorias',
    upgradesDisclaimer: 'O custo final pode variar devido a uma comissão no LSC em seu servidor',
    colorsTitle: 'Pintura de veículos',
    colorsDescription: 'Tipos disponíveis e tipos de pintura com custo',
    paintingsMainPrice: 'Pintura básica',
    paintingsSubPrice: 'Pintura adicional',
    paintingsOtherPrice: 'Outra pintura',
    paintingsBrightMetallic: 'Metálico brilhante',
    paintingsMetallic: 'Metálica',
    paintingsRichMetallic: 'Metálico rico',
    paintingsDarkMetallic: 'Metálico escuro',
    paintingsMatte: 'Fosco',
    paintingsMatteMetallic: 'Metal escovado',
    paintingsSatin: 'Cetim',
    paintingsMetal: 'Metal',
    paintingsShadowChrome: 'Cor cromada',
    paintingsPureChrome: 'Cromo puro',
    paintingsPearl: 'Tonalidade perolada',
    paintingsWheels: 'Cor da roda',
    paintingsDetails: 'Cor das peças',
    paintingsAdditionalDetails: 'Cor adicional das peças',
    paintingsLights: 'Luz do farol',
    paintingsSmoke: 'A cor da fumaça do pneu',
    paintingsNeon: 'Cor de neon',
    paintingsTotal: 'Custo final da pintura',
    shareLink: 'Compartilhe o link para o transporte',
    shareLinkCopied: 'O link de transporte foi copiado com sucesso',
    addToCompare: 'Adicionar à comparação',
    addedToCompare: 'Transport foi adicionado à comparação',
    goToCompare: 'Ir para comparar',
    removeFromCompare: 'Remover da comparação',
    removedFromCompare: 'O transporte foi removido da comparação'
  },
  vehiclesComparePage: {
    meta: {
      title: 'Comparação de transportes RP do GTA5 | Majestic Wiki',
      description: 'Compare as características e os preços de diferentes veículos no servidor Majestic RP no GTA5 RP. Descubra qual veículo atende melhor às suas necessidades! Compare velocidade, preço, capacidade e outros parâmetros.'
    },
    name: 'Título',
    type: 'Tipo',
    brand: 'Marca',
    kits: 'Pacotes',
    model: 'Modelo',
    trunkCapacity: 'Capacidade de inicialização',
    maxSpeed: 'Velocidade máxima',
    maxSpeedFT: 'Velocidade máxima (FT)',
    price: 'Custo',
    donatePrice: 'Custo em Majestic Coin',
    sellPrice: 'Custo de venda',
    sellPriceBeforeTax: 'Custo de venda (antes dos impostos)',
    sellPriceWithDiscount: 'Custo de venda (descontado)',
    accelerationTo100: 'Tempo de aceleração até 100 km/h',
    gasolineCapacity: 'Capacidade do tanque',
    gasolineType: 'Tipo de combustível',
    isTradable: 'Transferibilidade',
    isBuyable: 'Opção de compra',
    createdAt: 'Data de comparecimento',
    gasolineCapacityUnit: 'l',
    maxSpeedUnit: 'km/h',
    maxSpeedFTUnit: 'km/h',
    priceUnit: '₽',
    accelerationTo100Unit: 'seg.',
    trunkCapacityUnit: 'kg',
    showOnlyDifferent: 'Mostre apenas os que são diferentes',
    showAll: 'Mostrar tudo',
    share: 'Compartilhe uma comparação',
    linkCopied: 'O link de comparação foi copiado com sucesso',
    openVehicle: 'Abra a página de transporte',
    deleteVehicle: 'Remover um veículo da comparação',
    noDifferences: 'Não há distinção'
  },
  skinsPage: {
    meta: {
      title: 'GTA5 RP Weapon Skins | Majestic Wiki',
      description: 'Catálogo completo de skins de armas no servidor Majestic RP em GTA5 RP. Saiba como obter, o custo e as características de cada skin para diferentes tipos de armas.',
      custom: {
        title: 'Skins em {type} | GTA5 RP | Majestic Wiki',
        description: 'Catálogo de skins para {type} no servidor Majestic RP em GTA5 RP. Informações detalhadas sobre como obter, custo e recursos das skins.'
      }
    },
    title: 'Tabela de pele',
    search: 'Pesquisa de pele...',
    apPistol: 'Pistola de perfuração de armadura',
    assaultRifle: 'Fuzil de assalto',
    assaultRifleMk2: 'Fuzil de assalto Mk2',
    bullpupRifle: 'Rifle Bullpup',
    carbineRifle: 'Fuzil de carabina',
    carbineRifleMk2: 'Fuzil de carabina Mk2',
    combatPDW: 'PDW de combate',
    gusenberg: 'Submetralhadora Thompson.',
    heavyPistol: 'Pistola Pesada',
    heavyShotgun: 'Escopeta pesada',
    heavySniper: 'Rifle de atirador pesado.',
    heavySniperMk2: 'Rifle de atirador pesado Mk2',
    lightvest: 'Colete blindado',
    machinePistol: 'PP pequeno',
    marksmanPistol: 'Arma de atirador',
    microSMG: 'Micro SMG',
    militaryRifle: 'Rifle militar',
    revolver: 'Revólver',
    smgMk2: 'SMG Mk2',
    specialCarbine: 'Mosquetão especial',
    vintagePistol: 'Arma vintage',
    combatMgMk2: 'Submetralhadora Mk2',
    heavyRifle: 'Rifle pesado',
    tacticalRifle: 'Rifle tático',
    tecPistol: 'SMG tática',
    noName: 'Pele #{id}',
    battlePass: 'Passe de Batalha',
    case: 'Estojo',
    unique: 'Único',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{pele} few{pele} many{peles} other{peles}}'
  },
  animationsPage: {
    meta: {
      title: 'Animações de RP do GTA5 | Majestic Wiki',
      description: 'Catálogo completo de animações do servidor Majestic RP no GTA5 RP. Todos os tipos de animações: ações, poses, danças e animações exclusivas. Saiba como você pode obter e o custo de cada animação.',
      custom: {
        title: '{type} | GTA5 RP Animations | Majestic Wiki',
        description: 'Catálogo {type} animações no servidor Majestic RP no GTA5 RP. Informações detalhadas sobre como você pode obter, pagar e usar as animações.'
      }
    },
    title: 'Tabela de animação',
    search: 'Pesquisa de animação...',
    default: 'Padrão',
    donate: 'Moeda Majestic',
    battlePass: 'Passe de Batalha',
    case: 'Estojo',
    unique: 'Único',
    action: 'Ações',
    pose: 'Poses',
    positive: 'Positivo',
    negative: 'Negativo',
    dances: 'Danças',
    etc: 'Outro',
    exclusive: 'Exclusivo',
    noName: 'Animação #{id}',
    pagingText: 'Encontrado{N, plural, one{a} other{}} {N} {N, plural, one{animação} few{animações} other{animações}}'
  },
  realtyPage: {
    meta: {
      title: 'GTA5 RP Real Estate | Majestic Wiki',
      description: 'Catálogo completo de imóveis no servidor Majestic RP no GTA5 RP. Casas, apartamentos, escritórios e armazéns com características detalhadas, preços e localização. Descubra o número de inquilinos, vagas de garagem e outros parâmetros.',
      custom: {
        title: '{type} | GTA5 RP Real Estate | Majestic Wiki',
        description: 'Catálogo {type} no servidor Majestic RP no GTA5 RP. Informações detalhadas sobre as características, os preços, a localização e os recursos da propriedade.'
      }
    },
    title: 'Tabela de propriedades',
    search: 'Pesquisa de propriedades...',
    majesticMotors: 'Motores Majestic',
    house: 'Casa',
    apartment: 'Apartamentos',
    office: 'Escritórios',
    warehouse: 'Armazéns',
    tenantsFor: '{N} {N, plural, one{inquilino} few{inquilino} many{inquilinos} other{inquilinos}}',
    garageSlotsFor: '{N} {N, plural, one{vaga de garagem} few{vagas de garagem} many{vagas de garagem} other{vagas de garagem}}',
    viewOnMap: 'Ver no mapa',
    mapPosition: 'Localização',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{imóveis} few{imóveis} many{imóveis} other{imóveis}}',
    noName: 'Imóveis #{id}'
  },
  postsPage: {
    meta: {
      title: 'Editando uma postagem | Majestic Wiki',
      description: 'Você pode criar e editar postagens no Majestic Wiki para o GTA5 RP. Você pode publicar informações atualizadas sobre transporte, propriedades, negócios e outros aspectos do jogo.'
    },
    titlePlaceholder: 'Digite o título da postagem',
    category: 'Categoria',
    noCategory: 'Não categorizado',
    availability: 'Disponibilidade',
    allView: 'Visibilidade para todos',
    privateView: 'Acesso restrito',
    relevance: 'Relevância',
    relevanceData: 'Informações atualizadas',
    outdatedData: 'Informações desatualizadas',
    categoryPosition: 'Posição na categoria',
    categoryPositionDescription: 'Por exemplo, "50" ou "-5".',
    postDeleted: 'Postar excluído',
    author: 'Autor do artigo',
    editor: 'Editor de artigos',
    contents: 'Conteúdo do artigo',
    readingTime: 'Tempo de leitura',
    readingTimeLength: '{N} {N, plural, one{minuto} few{minutos} many{minutos} other{minutos}}',
    publishDate: 'Publicação do site {date}',
    views: '{N} {N, plural, one{visualização} few{visualização} many{visualização} other{visualização}}',
    youLiked: 'Você gostou de',
    youAndPeopleLiked: 'Você também gostou do {likes}',
    peopleLiked: '{N} {N, plural, one{curtidas} few{curtidas} many{curtidas} other{curtidas}}',
    categories: 'Categorias',
    posts: 'Publicações',
    foundMistake: 'Você encontrou um erro no artigo?',
    language: 'Idioma'
  },
  panelPage: {
    users: {
      title: 'Gerenciamento de usuários | Majestic Wiki',
      heading: 'Gerenciamento de usuários',
      admin: 'Admin',
      user: 'Usuário',
      customUser: 'Usuário com direitos',
      search: 'Comece a digitar sua consulta de pesquisa....',
      comment: 'Comentário',
      pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{usuário} few{usuário} many{usuários} other{usuários}}'
    },
    posts: {
      title: 'Gerenciamento de postagens | Majestic Wiki',
      heading: 'Gerenciamento de postagens',
      views: '{N} {N, plural, one{visualização} few{visualização} many{visualização} other{visualização}}',
      likes: '{N} {N, plural, one{curtidas} few{curtidas} many{curtidas} other{curtidas}}'
    },
    categories: {
      title: 'Gerenciamento de categorias | Majestic Wiki',
      heading: 'Gerenciamento de categorias'
    },
    mapLayers: {
      title: 'Gerenciando camadas de mapas | Majestic Wiki',
      heading: 'Gerenciar camadas de mapas'
    },
    logs: {
      title: 'Auditoria de registro - Majestic Wiki',
      heading: 'Auditoria de registros',
      search: 'Comece a digitar sua consulta de pesquisa....',
      biz: 'Negócio',
      category: 'Categoria',
      clothing: 'Roupas',
      file: 'Arquivo',
      item: 'Item',
      mapItem: 'Elemento do mapa',
      mapLayer: 'Camada de mapa',
      post: 'A postagem',
      realty: 'Imóveis',
      skin: 'Skin',
      user: 'Usuário',
      vehicle: 'Veículos',
      animation: 'Animação',
      actions: {
        unknown: '{username} realizou um ato desconhecido',
        bizCreate: '{username} Você adicionou uma empresa {id}',
        bizUpdate: '{username} negócios alterados {id}',
        bizDelete: '{username} Excluiu o negócio {id}',
        categoryCreate: '{username} Adicionada a categoria {id}',
        categoryUpdate: '{username} Você alterou a categoria {id}',
        categoryDelete: '{username} Categoria excluída {id}',
        clothingCreate: '{username} roupas adicionadas {id}',
        clothingUpdate: '{username} Você trocou de roupa {id}',
        clothingDelete: '{username} Excluiu as roupas {id}',
        fileCreate: '{username} Você adicionou um arquivo {id}',
        fileUpdate: '{username} Você modificou o arquivo {id}',
        fileDelete: '{username} Excluiu o arquivo {id}',
        itemCreate: '{username} Você adicionou o item {id}',
        itemUpdate: '{username} você mudou de assunto {id}',
        itemDelete: '{username} Excluiu o item {id}',
        mapItemCreate: '{username} Você adicionou um item ao mapa {id} para "{layer}"',
        mapItemUpdate: '{username} Você alterou o item no mapa {id} para "{layer}"',
        mapItemDelete: '{username} Excluiu um item do mapa {id} para "{layer}"',
        mapLayerCreate: '{username} Adicionada uma camada de mapa {id}',
        mapLayerUpdate: '{username} Você alterou a camada do mapa {id}',
        mapLayerDelete: '{username} Excluiu a camada do mapa {id}',
        postCreate: '{username} Você adicionou uma postagem {id}',
        postUpdate: '{username} Postagem modificada {id}',
        postDelete: '{username} Postagem excluída {id}',
        realtyCreate: '{username} Adicionada a propriedade {id}',
        realtyUpdate: '{username} imóveis alterados {id}',
        realtyDelete: '{username} imóveis excluídos {id}',
        skinCreate: '{username} Você adicionou a pele {id}',
        skinUpdate: '{username} Você mudou a pele {id}',
        skinDelete: '{username} Excluiu a pele {id}',
        userCreate: '{username} Adicionado o usuário {id}',
        userUpdate: '{username} Alterou o usuário {id}',
        userDelete: '{username} Usuário excluído {id}',
        vehicleCreate: '{username} Transporte adicionado {id}',
        vehicleUpdate: '{username} Você alterou o transporte {id}',
        vehicleDelete: '{username} Excluiu o transporte {id}',
        animationCreate: '{username} Adicionada animação {id}',
        animationUpdate: '{username} Você alterou a animação {id}',
        animationDelete: '{username} Excluiu a animação {id}',
        changelogCreate: '{username} Adicionado changelog {id}',
        changelogUpdate: '{username} Você alterou o changelog {id}',
        changelogDelete: '{username} changelog excluído {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: 'Configuirações',
      users: 'Usuários',
      categories: 'Categorias',
      mapLayers: 'Camadas de mapas',
      posts: 'Publicações',
      logs: 'Registros'
    }
  },
  changelogPage: {
    titlePlaceholder: 'Digite o nome da alteração',
    descriptionPlaceholder: 'Insira uma descrição da alteração',
    availableAt: 'Data de publicação',
    soon: 'através de',
    ago: 'voltar',
    fix: 'Corrigido',
    feat: 'Adicionado',
    chore: 'Modificado',
    style: 'Design',
    refactor: 'Reciclado'
  },
  changelogsPage: {
    meta: {
      title: 'Histórico de alterações | Majestic Wiki',
      description: 'Histórico completo de atualizações e alterações no servidor Majestic RP. Fique de olho nos novos recursos, melhorias de jogabilidade, correções de bugs e balanceamento. Informações atualizadas sobre todas as atualizações do projeto.'
    },
    title: 'Histórico de mudanças',
    search: 'Encontrando a mudança...'
  },
  homePage: {
    meta: {
      title: 'Majestic Wiki | Enciclopédia de RP do GTA5',
      description: 'O Majestic Wiki é uma enciclopédia completa para os jogadores de GTA5 RP. Informações sobre transporte, imóveis, negócios, missões e outros aspectos do jogo. Informações atualizadas sobre preços, recursos e como obtê-los.'
    },
    tools: {
      map: {
        title: 'Mapa',
        description: 'Mapa do servidor com todos os locais'
      },
      vehicles: {
        title: 'Veículos',
        description: 'Todos os transportes disponíveis no servidor'
      },
      realty: {
        title: 'Imóveis',
        description: 'Dados para todas as casas e apartamentos'
      },
      biz: {
        title: 'Negócios',
        description: 'Opção de ganho avançado'
      },
      clothesMale: {
        title: 'Roupas masculinas',
        description: 'Um desfile de moda para homens'
      },
      clothesFemale: {
        title: 'Roupas femininas',
        description: 'Desfile de moda para mulheres'
      },
      skins: {
        title: 'Skin',
        description: 'Catálogo de todos os padrões disponíveis'
      },
      items: {
        title: 'Itens',
        description: 'Itens disponíveis no servidor'
      },
      servers: {
        title: 'Servidores',
        description: 'Lista de todos os servidores do Majestic RP'
      },
      animations: {
        title: 'Animações',
        description: 'Animações exclusivas no servidor'
      },
      changelogs: {
        title: 'Histórico de mudanças',
        description: 'Histórico de mudanças no projeto'
      }
    },
    search: {
      title: 'Categorias gerais',
      description: 'Aqui você pode encontrar qualquer informação sobre o servidor Majestic e seus sistemas'
    },
    categories: {
      collapseList: 'Recolher lista',
      expandList: 'Expandir lista'
    },
    online: {
      players: '{count} on-line',
      offline: 'Desligado'
    }
  },
  errorPage: {
    title: 'Página não encontrada',
    description: 'Você pode ter um erro de digitação no endereço da página ou ela simplesmente não existe :(',
    backToHome: 'Ir para a página principal'
  },
  bizPage: {
    meta: {
      title: 'Negócios de RP do GTA5 | Majestic Wiki',
      description: 'Catálogo completo de negócios no servidor Majestic RP no GTA5 RP. Lojas 24/7, postos de gasolina, lojas de armas, concessionárias de carros e outros negócios. Saiba mais sobre a lucratividade, os requisitos e como comprar cada negócio.',
      custom: {
        title: '{type} | GTA5 RP Businesses | Majestic Wiki',
        description: 'Informações detalhadas sobre {type} no servidor Majestic RP em GTA5 RP. Características, lucratividade, requisitos e como você pode comprar uma empresa.'
      }
    },
    title: 'Tabela de negócios',
    search: 'Pesquisa de negócios...',
    groceryStore: 'Loja 24/7',
    gasStation: 'Posto de gasolina',
    atm: 'ATM',
    gunshop: 'Loja de armas',
    clothingStore: 'Loja de roupas',
    autoShop: 'Concessionária',
    tattoo: 'Salão de tatuagem',
    lsc: 'Salão de customização',
    barbershop: 'Barbearia',
    carWash: 'Lavagem de carro',
    viewOnMap: 'Ver no mapa',
    mapPosition: 'Localização',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{negócios} few{negócios} many{negócios} other{negócios}}',
    noName: 'Negócios #{id}'
  },
  clothesPage: {
    meta: {
      title: 'Roupas de RP do GTA5 | Majestic Wiki',
      description: 'Catálogo completo de roupas do servidor Majestic RP no GTA5 RP. Roupas masculinas e femininas, acessórios, mochilas, patches, chapéus e outros itens do guarda-roupa. Saiba como obter e quanto custa cada peça de roupa.',
      custom: {
        title: '{type} | Roupas de RP do GTA5 | Majestic Wiki',
        description: 'Catálogo {type} no servidor Majestic RP em GTA5 RP. Informações detalhadas sobre como obter, usar e o custo dos itens de vestuário.'
      }
    },
    titleMale: 'Tabela de roupas masculinas',
    titleFemale: 'Tabela de roupas femininas',
    search: 'Pesquisa de vestuário...',
    top: 'Topo',
    legs: 'Inferior',
    shoes: 'Sapatos',
    watch: 'Horas',
    mask: 'Máscaras',
    decal: 'Listras',
    accessory: 'Acessórios',
    head: 'Chapéus',
    bag: 'Mochilas',
    glasses: 'Óculos',
    ear: 'Orelhas',
    gloves: 'Luvas',
    undershirt: 'Camisetas',
    noName: 'Conjunto de roupas #{id}',
    drawable: 'Desenhável',
    texture: 'Textura',
    male: 'Masculino',
    female: 'Feminino',
    donate: 'Moeda Majestic',
    battlePass: 'Passe de Batalha',
    case: 'Estojo',
    unique: 'Único',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{vestuário} few{vestuário} many{vestuário} other{vestuário}}'
  },
  itemsPage: {
    meta: {
      title: 'Itens RP do GTA5 | Majestic Wiki',
      description: 'Catálogo completo de itens do servidor Majestic RP no GTA5 RP. Antiguidades, peixes, minério, madeira, caixas, materiais e outros itens. Saiba como obter, usar e o custo de cada item.',
      custom: {
        title: '{type} | GTA5 RP Items | Majestic Wiki',
        description: 'Catálogo {type} no servidor Majestic RP em GTA5 RP. Informações detalhadas sobre como obter, usar e o custo dos itens.'
      }
    },
    title: 'Tabela de assuntos',
    search: 'Encontrar objetos...',
    antique: 'Antiguidades',
    fish: 'Peixes',
    ore: 'Minério',
    wood: 'Madeira',
    box: 'Caixas',
    material: 'Materiais',
    event: 'Evento',
    food: 'Produtos',
    alcohol: 'Álcool',
    illegal: 'Ilegal',
    medical: 'Medicina',
    equipment: 'Equipamento',
    ammunition: 'Munição',
    tool: 'Instrumentos',
    unique: 'Único',
    vehicle: 'Peças de automóveis',
    others: 'Diversos',
    default: 'Padrão',
    battlePass: 'Passe de Batalha',
    case: 'Estojo',
    unique: 'Único',
    pagingText: 'Encontrado{N, plural, few{sobre} many{sobre}} {N} {N, plural, one{item} few{item} many{itens} other{itens}}',
    noName: 'Assunto #{id}'
  },
  mapPage: {
    meta: {
      title: 'Mapa RP do GTA5 | Majestic Wiki',
      description: 'Mapa interativo do servidor Majestic RP no GTA5 RP. Explore locais, encontre tesouros, animais, telefones e lixo. Mapa detalhado do mundo com a possibilidade de você adicionar tags e zonas. Todos os locais e pontos de interesse importantes em um único recurso!',
      custom: {
        title: '{type} | Mapa RP do GTA5 | Majestic Wiki',
        description: 'Um mapa detalhado de {type} no servidor Majestic RP no GTA5 RP. Explore locais, encontre pontos importantes e crie seus próprios marcadores. Você tem todas as informações sobre a localização dos objetos no mapa!'
      }
    },
    dataLoading: 'Carregamento de dados...',
    defaultPoints: 'Etiquetas padrão',
    animalLayer: 'Mapa de animais',
    rubbishLayer: 'Um mapa das latas de lixo',
    telephoneLayer: 'Mapa telefônico',
    treasureLayer: 'Mapa do tesouro',
    defaultLayer: 'Mapa do mundo',
    allLayers: 'Camadas de mapas',
    backToHome: 'Voltar para a página inicial',
    zoomIn: 'Abordagem',
    zoomOut: 'Puxar para trás',
    point: 'Ponto',
    polygon: 'Zona',
    addPoint: 'Adicionar um ponto',
    addPolygon: 'Adicionar uma zona',
    copyCoordinates: 'Copiar coordenadas',
    savePointPosition: 'Salvar posição',
    editPointPosition: 'Mudar de posição',
    layerNamePlaceholder: 'Nome da zona',
    layerDescriptionPlaceholder: 'Descrição da zona'
  },
  serversPage: {
    meta: {
      title: 'Servidores RP online do GTA5 | Majestic Wiki',
      description: 'Estatísticas atuais dos jogadores on-line nos servidores Majestic RP em GTA5 RP. Gráficos atuais de online, picos, filas e atividade. Junte-se ao projeto de RPG mais popular!'
    },
    dataLoading: 'Carregamento de dados...',
    title: 'Servidores on-line',
    online: '{count} on-line',
    inQueue: '{count} na fila',
    peakOnline: '{count} pico',
    currentOnline: 'online atual',
    peakOnline24h: 'pico hoje',
    peakOnlineAllTime: 'pico de todos os tempos',
    peakOnlineTooltip: '{percent}% em relação ao dia anterior',
    techWorks: 'Trabalho técnico',
    forBeginners: 'Para iniciantes',
    experience: '{multiplier}Experiência x',
    newServer: 'Novo',
    seasonPassMultiplier: '{multiplier}Pular experiência x',
    combinedView: 'Geral',
    separateView: 'Separado'
  },
  research: {
    placeholder: 'Você pode pesquisar informações no site...',
    skin: 'Skin',
    vehicle: 'Veículos',
    realty: 'Imóveis',
    biz: 'Negócio',
    clothing: 'Roupas',
    item: 'Item',
    post: 'A postagem'
  },
  userModal: {
    login: 'Iniciar sessão',
    signout: 'Sair',
    register: 'Cadastre-se',
    registerEnd: 'Cadastre-se',
    restore: 'Restauração',
    restoreEnd: 'Restaurar',
    avatarDelete: 'Excluir avatar',
    headingUpdate: 'Modificando o usuário #{id}',
    postAdd: 'Publicação de posts',
    postUpdate: 'Edição de postagens',
    postDelete: 'Exclusão de postagens',
    vehicleAdd: 'Adição de transporte',
    vehicleUpdate: 'Edição de transporte',
    vehicleDelete: 'Remoção de transporte',
    realtyAdd: 'Adicionar uma propriedade',
    realtyUpdate: 'Mudança no setor imobiliário',
    realtyDelete: 'Remoção de bens imóveis',
    bizAdd: 'Adicionar uma empresa',
    bizUpdate: 'Mudança nos negócios',
    bizDelete: 'Remoção de uma empresa',
    mapItemAdd: 'Adicionando itens ao mapa',
    mapItemUpdate: 'Alterar elementos no mapa',
    mapItemDelete: 'Exclusão de itens no mapa',
    clothingAdd: 'Adicionando roupas',
    clothingUpdate: 'Troca de roupas',
    clothingDelete: 'Remoção de roupas',
    skinAdd: 'Adicionando skins',
    skinUpdate: 'Alteração de skins',
    skinDelete: 'Remoção de peles',
    itemAdd: 'Adicionar itens',
    itemUpdate: 'Objetos de modificação',
    itemDelete: 'Remoção de objetos',
    fileUpload: 'Carregamento de imagens',
    viewPrivate: 'Exibir dados privados',
    doMagic: 'Acesso a funções mágicas',
    animationAdd: 'Adição de animações',
    animationUpdate: 'Alteração de animações',
    animationDelete: 'Exclusão de animações',
    categoryAdd: 'Adicionar categorias',
    categoryUpdate: 'Mudança de categorias',
    categoryDelete: 'Exclusão de categorias',
    mapLayerAdd: 'Adição de camadas de mapa',
    mapLayerUpdate: 'Alterar as camadas do mapa',
    mapLayerDelete: 'Exclusão de camadas do mapa',
    post: 'A postagem',
    skin: 'Skin',
    biz: 'Negócio',
    clothing: 'Roupas',
    item: 'Item',
    vehicle: 'Veículos',
    animation: 'Animação',
    category: 'Categoria',
    realty: 'Imóveis',
    mapLayer: 'Camada de mapa',
    changelog: 'Registro de alterações'
  },
  restoreModal: {
    title: 'Restauração de conta',
    newPassword: 'Nova Senha',
    newPasswordRepeat: 'Repetir a nova senha',
    passwordConfirm: 'Alterar senha',
    passwordError: 'As senhas não correspondem'
  },
  bizModal: {
    titleUpdate: 'Mudança nos negócios #{id}',
    titleAdd: 'Adicionar um novo negócio'
  },
  categoryModal: {
    titleUpdate: 'Alterando a categoria #{id}',
    titleAdd: 'Adicionar uma nova categoria'
  },
  mapLayerModal: {
    titleUpdate: '#{id}Alterar a camada do mapa',
    titleAdd: 'Adicionando uma nova camada de mapa'
  },
  clothingModal: {
    titleUpdate: 'Troca de roupas #{id}',
    titleAdd: 'Adicionar roupas novas',
    itemAdd: 'Adicionar cor',
    itemGenerate: 'Gerar todas as cores'
  },
  itemModal: {
    titleUpdate: 'Mudando de assunto #{id}',
    titleAdd: 'Adicionando um novo item'
  },
  realtyModal: {
    titleUpdate: 'Mudança no setor imobiliário #{id}',
    titleAdd: 'Adição de uma nova propriedade'
  },
  skinModal: {
    titleUpdate: 'Alterando a pele #{id}',
    titleAdd: 'Adição de uma nova pele'
  },
  vehicleModal: {
    titleUpdate: 'Mudança no transporte #{id}',
    titleAdd: 'Adição de novo transporte',
    titleDefault: 'Informações sobre {name}',
    itemAdd: 'Adicionar um guarda-sol'
  },
  mapItemModal: {
    titleUpdate: 'Alterar um elemento do mapa',
    titleAdd: 'Adicionando um novo elemento de mapa',
    titleDelete: 'Exclusão de um item do mapa',
    descriptionDelete: 'Você tem certeza de que deseja remover o item do mapa? Essa ação não pode ser cancelada.'
  },
  animationModal: {
    titleUpdate: 'Alterando a animação #{id}',
    titleAdd: 'Adição de uma nova animação'
  }
};