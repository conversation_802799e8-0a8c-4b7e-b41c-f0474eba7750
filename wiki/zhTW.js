export default {
  global: {
    all: '全部',
    back: '返回',
    forward: '下一步',
    paging: '第頁 {page} ，共頁 {maxPage}',
    close: '關閉',
    clear: '清除',
    save: '保存',
    delete: '刪除',
    deleteYes: '您確定要刪除嗎？',
    cancel: '解僱',
    publish: '發佈',
    edit: '更改',
    share: '分享',
    add: '添加',
    active: '有效',
    deleted: '已刪除',
    blocked: '已屏蔽',
    learnMore: '了解更多',
    panel: '面板',
    theme: '主旨',
    language: '語言',
    user: '用戶',
    confirm: '確認',
    restore: '恢復',
    yes: '是',
    no: '無',
    new: '新款',
    preview: '預覽',
    sortPriceDesc: '依價格遞減',
    sortPriceAsc: '依價格遞增',
    sortPriceMinMax: '價格範圍',
    sortDateDesc: '依日期降序排列',
    sortDateAsc: '依日期升序排列',
    noSellPrice: '不可購買',
    noBuyPrice: '非賣品',
    noData: {
      title: '什麼都沒找到',
      description: '請嘗試變更或簡化搜尋內容'
    },
    battlePass: {
      summer: '夏季戰鬥通行證',
      winter: '冬季戰爭通行證'
    },
    case: {
      default: '標準外殼',
      vehicles: '車殼',
      halloween: '萬聖節案例',
      spring: '春季公事包',
      autumn: '秋季公事包',
      summer: '夏季案例',
      summerExtra: '夏季特例',
      summerSkins: '夏日皮套',
      summerVehicles: '夏季運輸箱',
      winter: '冬季案例',
      winterExtra: '冬季特例',
      winterSkins: '冬季皮套',
      winterVehicles: '冬季運輸箱',
      toys: '玩具盒'
    },
    subscription: { premium: '高級會籍' },
    copy: '複製'
  },
  footer: { disclaimer: '本網站上的所有資訊僅供參考，並不構成公開要約。' },
  footer: { disclaimer: '本網站上的所有資訊僅供參考，並不構成公開要約。' },
  editor: {
    placeholder: '一起來寫一篇精彩的文章吧！',
    text: '段落',
    heading: '標題',
    warning: '注意',
    quote: '引用',
    code: '代碼',
    delimiter: '分隔符',
    link: '參考',
    linkPlaceholder: '輸入連結',
    marker: '標記',
    bold: '粗體',
    italic: '斜體',
    inlineCode: '等間距',
    underline: '下劃線',
    copytext: '可複製',
    log: '羅格',
    map: {
      name: '地圖',
      params: '輸入卡片參數',
      caption: '輸入地圖描述'
    },
    table: {
      name: '表',
      addColumnLeft: '在左側新增一欄',
      addColumnRight: '在右側新增一欄',
      deleteColumn: '刪除欄',
      addRowAbove: '在頂部新增一行',
      addRowBelow: '添加行底部',
      deleteRow: '刪除行',
      withHeadings: '有標題',
      withoutHeadings: '無標題'
    },
    image: {
      name: '圖片',
      withBorder: '有框架',
      stretchImage: '拉伸圖片',
      withBackground: '有背景',
      selectImage: '選擇一張圖片',
      uploadError: '無法上傳圖片，請嘗試另一個',
      caption: '圖片描述',
      withCaption: '附帶說明'
    },
    carousel: {
      name: '圖片網格',
      caption: '圖片描述',
      addImage: '選擇一張圖片'
    },
    list: {
      name: '列表',
      ordered: '已編號',
      unordered: '無編號',
      checklist: '清單'
    },
    header: {
      heading1: '標題H1',
      heading2: '標題H2',
      heading3: '標題H3',
      heading4: '標題H4'
    },
    embed: { caption: '連結說明' },
    convertTo: '轉換為',
    moveUp: '上移',
    moveDown: '下移',
    anchor: '錨點',
    clickToTune: '按一下即可自訂',
    dragToMove: '或拖放',
    filter: '搜索',
    nothingFound: '什麼都沒找到'
  },
  alerts: {
    loginSuccess: '登入成功',
    loginError: '授權錯誤',
    registerSuccess: '在網站上成功註冊',
    registerError: '註冊時發生錯誤',
    restoreSuccess: '復原指示已傳送至電子郵件',
    restoreError: '恢復帳戶存取權限時出錯',
    signoutSuccess: '已成功登出',
    postLoadError: '載入文章時發生錯誤',
    postShareSuccess: '文章連結已複製到剪貼簿',
    copyCommandSuccess: '命令已複製到剪貼簿',
    restoreAccountSuccess: '您的帳戶已成功還原',
    restoreAccountError: '恢復帳戶存取權限時出錯',
    verificateAccountSuccess: '信箱已成功驗證',
    verificateAccountError: '信箱驗證錯誤',
    dataUpdateSuccess: '已成功更新信息',
    dataUpdateError: '更新資訊時發生錯誤',
    dataDeleteSuccess: '成功刪除資訊',
    dataDeleteError: '刪除資訊時發生錯誤',
    dataAddSuccess: '已成功添加信息',
    dataAddError: '新增資訊時發生錯誤',
    mapMoveInfo: '將點移至其他位置並儲存',
    mapCoordinatesCopySuccess: '坐標已成功複製',
    copyTextSuccess: '成功複製文字'
  },
  mail: {
    verificate: {
      subject: '確認電子郵件地址',
      title: '確認',
      description: '你需要點擊下方按鈕以完成驗證並驗證你的帳戶。連結有效期為24小時。'
    },
    restore: {
      subject: '恢復帳戶存取權限',
      title: '恢復',
      description: '如要重新取得帳戶存取權限，請點擊下方按鈕。連結有效期為24小時。'
    },
    restoreSuccess: {
      subject: '已成功重新取得帳戶存取權限',
      title: '恢復成功',
      description: '您已成功更改網站上的密碼。如果您沒有這樣做，請立即更改密碼！'
    },
    verificateSuccess: {
      subject: '已成功重新取得帳戶存取權限',
      title: '確認成功',
      description: '您已成功驗證您的信箱，歡迎！'
    },
    caption: '電子郵件是自動產生的通知，請勿回覆。'
  },
  fields: {
    price: '成本',
    buyPrice: '購買價格',
    sellPrice: '銷售成本',
    mcPrice: 'MC 購買成本',
    money: '幣種',
    coin: '雄偉硬幣',
    gift: '禮物',
    default: '標準',
    unique: '獨特',
    event: '事件',
    battlePass: '戰鬥通行證',
    lootbox: '案例',
    capacityKg: '容量',
    email: '郵件',
    password: '密碼',
    username: '朋友打電話給我',
    weightKg: '公斤',
    speed: '速度',
    maxSpeed: '最高速度',
    maxSpeedFT: '最高速度（英尺）',
    accelerationTo100: '加速到100公裏/小時',
    speedSystem: '公裏/小時',
    timeSystem: 'sec',
    uploadDescription: '按一下影像或將影像拖曳到該區域',
    placeholder: '在中輸入值',
    imagePlaceholder: '輸入圖片連結',
    selectPlaceholder: '從清單中選擇一個值',
    videoPlaceholder: '輸入影片連結',
    soundPlaceholder: '輸入音訊的連結',
    id: 'ID',
    serial: '序號',
    kind: '類型',
    category: '類別',
    name: '名稱',
    view: '檢視',
    layer: '層數',
    icon: '圖示',
    circle: '點',
    polygon: '多邊形',
    emoji: '表情符號',
    description: '描述',
    gender: '性別',
    value: '費用類型',
    valueName: '額外價格說明',
    visibility: '可訂狀態',
    visibilityPrivate: '存取受限',
    visibilityPublic: '對所有人可見',
    activityHidden: '對共享隱藏',
    activityVisible: '所有人都可以使用',
    garageSlots: '車庫插槽',
    maxTenants: '居民人數',
    gasoline: '燃料',
    brand: '品牌',
    model: '模型',
    trunk: '行李箱',
    tradable: '可轉讓性',
    buyable: '購買選項',
    upgradesPriceFT: '可用的改進 (FT)',
    upgradesEngine: '馬達',
    upgradesBreaks: '煞車',
    upgradesSuspension: '暫時停用',
    upgradesTurbo: 'Turbo',
    upgradesTransmission: 'Box',
    paintingsMainPrice: '基本繪畫',
    paintingsSubPrice: '額外繪畫',
    paintingsOtherPrice: '其他繪畫',
    paintingsBrightMetallic: '明亮的金屬色',
    paintingsMetallic: '金屬',
    paintingsRichMetallic: '豐富的金屬感',
    paintingsDarkMetallic: '深色金屬',
    paintingsMatte: '霧面',
    paintingsMatteMetallic: '拉絲金屬',
    paintingsSatin: '緞面',
    paintingsMetal: '金屬',
    paintingsShadowChrome: '陰影鉻',
    paintingsPureChrome: '純鉻',
    paintingsPearl: '珠光色調',
    paintingsWheels: '光碟顏色',
    paintingsDetails: '零件顏色',
    paintingsAdditionalDetails: '零件的附加顏色',
    paintingsLights: '頭燈照明',
    paintingsSmoke: '輪胎煙霧的顏色',
    paintingsNeon: '霓虹燈的顏色',
    total: '總計',
    language: '語言',
    radius: '半徑',
    color: '顏色',
    colorPlaceholder: '選擇顏色',
    opacity: '透明度',
    alias: '化名',
    aliases: '別名',
    looped: '循環',
    battlePassId: '戰鬥通行證 ID',
    gasolineCapacityLiter: 'л',
    gasolineCapacity: '油箱容量',
    source: '來源',
    sources: '來源',
    priceDescription: '成本說明',
    layerName: '圖層名稱',
    layerNamePlaceholder: '輸入圖層的名稱',
    layerDescription: '層描述',
    layerDescriptionPlaceholder: '輸入圖層的描述',
    layerColor: '圖層顏色',
    layerColorPlaceholder: '選擇顏色',
    layerOpacity: '圖層透明度',
    zIndexOffset: 'Z 索引層級',
    zIndexOffsetPlaceholder: '輸入 Z-index 層級的值',
    comment: '評論'
  },
  vehiclesPage: {
    meta: {
      title: 'GTA5 RP 運輸與汽車 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 伺服器上的完整車輛目錄。汽車、機車、直昇機、飛機和船隻的詳細特性。找出價格、速度、機動性、行李箱容量和其他參數。Majestic Motors、汽車、卡車、直昇機、飛船、機車、腳踏車 - 全部適用於 GTA5 RP！',
      custom: {
        title: '{type} | GTA5 RP Transport | Majestic Wiki',
        description: '詳細目錄 {type} 在 GTA5 RP 的伺服器 Majestic RP 上。特性、價格、速度、機動性和其他參數。GTA5 RP 的完整運輸資訊！'
      }
    },
    default: '標準',
    donate: '雄偉硬幣',
    battlePass: '戰鬥通行證',
    case: '案例',
    unique: '獨特',
    title: '傳輸表',
    search: '搜尋運輸工具...',
    irl: 'Majestic Motors',
    car: '乘用車',
    freight: '貨物',
    helicopter: '直升機',
    plane: 'Самолеты',
    boat: '船',
    motorbike: '摩託車',
    bike: '腳踏車',
    trailer: '拖車',
    military: '軍事',
    special: '專長',
    priceDisclaimer: '總費用可能會因伺服器上的LSC費用而有所不同',
    basicInformation: '基本信息',
    additionalInformation: '其他信息',
    compare: '比較',
    compareLimit: '最多可比較 5 個 TC',
    pagingText: '已找到{N, plural, few{~ о} many{~ о}} {N} {N, plural, one{~交通工具} few{~交通工具} many{} other{交通工具}}'
  },
  vehiclePage: {
    meta: {
      title: 'GTA5 RP Transport | Majestic Wiki',
      description: '有關 GTA5 RP 中 Majestic RP 伺服器上交通工具的詳細資訊。特性、價格、改良和塗裝。瞭解每種交通工具的最高速度、加速度、行李箱容量、改良費用及其他參數。',
      custom: {
        title: '{name} | GTA5 RP Transport | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 伺服器上 {name} 的詳細資訊。功能、價格、改進和塗裝。瞭解最高速度、加速度、開機容量、改進成本和其他參數。'
      }
    },
    backToTable: '回到清單',
    labelsUniqueId: '唯一 ID',
    labelsPrice: '成本',
    labelsBuyPrice: '購買價格',
    labelsSellPrice: '銷售成本',
    labelsMaxSpeed: '最高速度',
    labelsMaxSpeedFT: '最高速度（英尺）',
    labelsAccelerationTo100: '加速到100公裏/小時',
    labelsTradable: '可轉讓性',
    labelsTrunk: '開機容量',
    labelsGasolineCapacity: '油箱容量',
    labelsGasolineType: '燃料類型',
    upgradesTitle: '交通改善',
    upgradesDescription: '升級到最高等級的成本',
    upgradesEngine: '馬達',
    upgradesEngineDescription: '發掘運輸的真正潛力',
    upgradesBreaks: '煞車',
    upgradesBreaksDescription: '在任何道路上都能完全控制車速',
    upgradesTurbo: 'Turbo',
    upgradesTurboDescription: '隨時爆炸的加速度',
    upgradesTransmission: 'Box',
    upgradesTransmissionDescription: '所有檔位的換檔速度快如閃電',
    upgradesSuspension: '暫時停用',
    upgradesSuspensionDescription: '完美舒適的高度',
    upgradesTotal: '改善成本',
    upgradesDisclaimer: '總費用可能會因伺服器上的LSC費用而有所不同',
    colorsTitle: '車輛噴漆',
    colorsDescription: '可用的塗裝類型及成本',
    paintingsMainPrice: '基本繪畫',
    paintingsSubPrice: '額外繪畫',
    paintingsOtherPrice: '其他繪畫',
    paintingsBrightMetallic: '明亮的金屬色',
    paintingsMetallic: '金屬',
    paintingsRichMetallic: '豐富的金屬感',
    paintingsDarkMetallic: '深色金屬',
    paintingsMatte: '霧面',
    paintingsMatteMetallic: '拉絲金屬',
    paintingsSatin: '緞面',
    paintingsMetal: '金屬',
    paintingsShadowChrome: '陰影鉻',
    paintingsPureChrome: '純鉻',
    paintingsPearl: '珠光色調',
    paintingsWheels: '光碟顏色',
    paintingsDetails: '零件顏色',
    paintingsAdditionalDetails: '零件的附加顏色',
    paintingsLights: '頭燈照明',
    paintingsSmoke: '輪胎煙霧的顏色',
    paintingsNeon: '霓虹燈的顏色',
    paintingsTotal: '最終噴漆成本',
    shareLink: '分享運輸連結',
    shareLinkCopied: '傳輸連結已成功複製',
    addToCompare: '加入比較',
    addedToCompare: '運輸 已加入比較',
    goToCompare: '前往比較',
    removeFromCompare: '從比較中移除',
    removedFromCompare: '運輸已從比較中移除'
  },
  vehiclesComparePage: {
    meta: {
      title: 'GTA5 RP Transport Comparison | Majestic Wiki',
      description: '比較 GTA5 RP 中 Majestic RP 伺服器上不同車輛的功能和價格。找出最適合您需求的車輛！比較速度、價格、容量和其他參數。'
    },
    name: '名稱',
    type: '類型',
    brand: '品牌',
    kits: '套裝',
    model: '模型',
    trunkCapacity: '開機容量',
    maxSpeed: '最高速度',
    maxSpeedFT: '最高速度（英尺）',
    price: '成本',
    donatePrice: 'Majestic Coin 的成本',
    sellPrice: '銷售成本',
    sellPriceBeforeTax: '銷售成本（稅前）',
    sellPriceWithDiscount: '銷售成本（貼現）',
    accelerationTo100: '加速至 100 km/h 的時間',
    gasolineCapacity: '油箱容量',
    gasolineType: '燃料類型',
    isTradable: '可轉讓性',
    isBuyable: '購買選項',
    createdAt: '出庭日期',
    gasolineCapacityUnit: 'л',
    maxSpeedUnit: '公裏/小時',
    maxSpeedFTUnit: '公裏/小時',
    priceUnit: '₽',
    accelerationTo100Unit: 'sec',
    trunkCapacityUnit: '公斤',
    showOnlyDifferent: '只顯示不同的',
    showAll: '顯示全部',
    share: '分享比較',
    linkCopied: '已成功複製比較連結',
    openVehicle: '開啟傳輸頁面',
    deleteVehicle: '從比較中移除車輛',
    noDifferences: '沒有區別'
  },
  skinsPage: {
    meta: {
      title: 'GTA5 RP 武器面板 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 伺服器上武器皮膚的完整目錄。瞭解不同類型武器的每種皮膚的獲得方式、成本和功能。',
      custom: {
        title: 'Skins at {type} | GTA5 RP | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 伺服器上 {type} 的面板目錄。詳細資訊關於如何獲得，成本和功能的皮膚。'
      }
    },
    title: '皮膚表',
    search: '搜尋外觀...',
    apPistol: '穿甲手槍',
    assaultRifle: '突擊步槍',
    assaultRifleMk2: 'Mk2突擊步槍',
    bullpupRifle: 'Bullpup Rifle',
    carbineRifle: '登山步槍',
    carbineRifleMk2: 'Mk2卡賓槍',
    combatPDW: '戰鬥PDW',
    gusenberg: 'Thompson衝鋒槍',
    heavyPistol: '重型手槍',
    heavyShotgun: '重型霰彈槍',
    heavySniper: '重型狙擊步槍',
    heavySniperMk2: 'Mk2重型狙擊步槍',
    lightvest: '防彈背心',
    machinePistol: '小PP',
    marksmanPistol: '神射手槍',
    microSMG: '微型衝鋒槍',
    militaryRifle: '軍用步槍',
    revolver: '左輪手槍',
    smgMk2: 'SMG Mk2',
    specialCarbine: '特種卡賓槍',
    vintagePistol: '復古手槍',
    combatMgMk2: 'Mk2輕機槍',
    heavyRifle: '重型步槍',
    tacticalRifle: '戰術步槍',
    tecPistol: '戰術衝鋒槍',
    noName: '皮膚 #{id}',
    battlePass: '戰鬥通行證',
    case: '案例',
    unique: '獨特',
    pagingText: '找到{N, plural, few{о ~ ~ о} many{~}} 皮膚 {N} {N, plural, one{皮膚} few{皮膚} many{~皮膚} other{皮膚}}'
  },
  animationsPage: {
    meta: {
      title: 'GTA5 RP 動畫 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 伺服器上的完整動畫目錄。所有類型的動畫：動作、姿勢、舞蹈和專屬動畫。瞭解如何獲得以及每個動畫的費用。',
      custom: {
        title: '{type} | GTA5 RP 動畫 | Majestic Wiki',
        description: '{type} GTA5 RP 中 Majestic RP 伺服器上的動畫目錄。有關如何獲得、成本和使用動畫的詳細資訊。'
      }
    },
    title: '動畫表',
    search: '動畫搜尋...',
    default: '標準',
    donate: '雄偉硬幣',
    battlePass: '戰鬥通行證',
    case: '案例',
    unique: '獨特',
    action: '行動',
    pose: '姿勢',
    positive: '正面',
    negative: '負面',
    dances: '跳舞',
    etc: '其他',
    exclusive: '獨家',
    noName: '動畫 #{id}',
    pagingText: 'Найден{N, plural, one{а} few{о} many{о}} {N} {N, plural, one{анимация} few{анимации} many{анимаций} other{анимаций}}'
  },
  realtyPage: {
    meta: {
      title: 'GTA5 RP Real Estate | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 伺服器上房地產的完整目錄。房屋、公寓、辦公室和倉庫的詳細特性、價格和位置。找出租戶數量、車庫空間和其他參數。',
      custom: {
        title: '{type} | GTA5 RP Real Estate | Majestic Wiki',
        description: '目錄 {type} 在 GTA5 RP 中的 Majestic RP 伺服器上。有關物業特性、價格、位置和功能的詳細資訊。'
      }
    },
    title: '屬性表',
    search: '搜尋旅居...',
    majesticMotors: 'Majestic Motors',
    house: '在家',
    apartment: '公寓',
    office: '辦公室',
    warehouse: '倉庫',
    tenantsFor: '{N} {N, plural, one{租用戶} few{租用戶} many{租用戶} other{租用戶}}',
    garageSlotsFor: '{N} {N, plural, one{車庫空間} few{車庫空間} many{車庫空間} other{車庫空間}}',
    viewOnMap: '在地圖上查看',
    mapPosition: '位置',
    pagingText: '發現{N, plural, few{關於~ ~關於} many{}} ~ {N} ~房地產 {N, plural, one{} few{房地產} many{房地產} other{房地產}}',
    noName: '房地產 #{id}'
  },
  postsPage: {
    meta: {
      title: '編輯文章 | Majestic Wiki',
      description: '在 Majestic Wiki 上為《GTA5》RP 創建和編輯文章。發佈有關交通、財產、商業和遊戲其他方面的最新資訊。'
    },
    titlePlaceholder: '輸入文章標題',
    category: '類別',
    noCategory: '未分類',
    availability: '可訂狀態',
    allView: '對所有人可見',
    privateView: '存取受限',
    relevance: '相關性',
    relevanceData: '最新資訊',
    outdatedData: '資訊已過期',
    categoryPosition: '類別位置',
    categoryPositionDescription: '例如，「50」或「-5」',
    postDeleted: '文章已刪除',
    author: '文章作者',
    editor: '文章編輯',
    contents: '文章內容',
    readingTime: '閱讀時間',
    readingTimeLength: '{N} {N, plural, one{MINUTE ~ ~ MINUTE} few{~ MINUTE} many{~ MINUTE} other{~ MINUTE}}',
    publishDate: '出版物來源： {date}',
    views: '{N} {N, plural, one{VIEW ~ ~ VIEW} few{~ VIEW} many{~ VIEW} other{~ VIEW}}',
    youLiked: '您已點讚',
    youAndPeopleLiked: '您也點 {likes} 讚了',
    peopleLiked: '{N} {N, plural, one{讚~ ~讚} few{讚} many{讚~ ~讚} other{~讚}}',
    categories: '類別',
    posts: '帖子',
    foundMistake: '發現文章中有錯誤？',
    language: '語言'
  },
  panelPage: {
    users: {
      title: '使用者管理',
      heading: '使用者管理',
      admin: '系統管理員',
      user: '使用者',
      customUser: '具有權限的使用者',
      search: '開始輸入搜尋查詢...',
      comment: '評論',
      pagingText: '已找到{N, plural, few{о} many{о ~}} {N} {N, plural, one{用戶} few{用戶} many{用戶} other{用戶}}'
    },
    posts: {
      title: '貼文管理 | Majestic Wiki',
      heading: '職位管理',
      views: '{N} {N, plural, one{VIEW ~ ~ VIEW} few{~ VIEW} many{~ VIEW} other{~ VIEW}}',
      likes: '{N} {N, plural, one{讚~ ~讚} few{讚} many{讚~ ~讚} other{~讚}}'
    },
    categories: {
      title: '類別管理',
      heading: '分類管理'
    },
    mapLayers: {
      title: '管理地圖圖層 | Majestic Wiki',
      heading: '管理地圖圖層'
    },
    logs: {
      title: '日誌稽核 - Majestic Wiki',
      heading: '日誌稽核',
      search: '開始輸入搜尋查詢...',
      biz: '商務',
      category: '類別',
      clothing: '服裝',
      file: '檔案',
      item: '主旨',
      mapItem: '地圖元素',
      mapLayer: '地圖圖層',
      post: '文章',
      realty: '房地產',
      skin: '皮膚',
      user: '用戶',
      vehicle: '交通',
      animation: '動畫',
      actions: {
        unknown: '{username} 行不明之事',
        bizCreate: '{username} 新增商業 {id}',
        bizUpdate: '{username} 已變更的業務 {id}',
        bizDelete: '{username} 刪除企業 {id}',
        categoryCreate: '{username} 新增類別 {id}',
        categoryUpdate: '{username} 變更類別 {id}',
        categoryDelete: '{username} 刪除類別 {id}',
        clothingCreate: '{username} 添加衣服 {id}',
        clothingUpdate: '{username} 更換衣服 {id}',
        clothingDelete: '{username} 刪除衣服 {id}',
        fileCreate: '{username} 新增檔案 {id}',
        fileUpdate: '{username} 修改檔案 {id}',
        fileDelete: '{username} 刪除檔案 {id}',
        itemCreate: '{username} 新增項目 {id}',
        itemUpdate: '{username} 更改主題 {id}',
        itemDelete: '{username} 刪除項目 {id}',
        mapItemCreate: '{username} 在地圖 {id} 中加入一個項目至 "{layer}"',
        mapItemUpdate: '{username} 將地圖上的項目 {id} 改為 "{layer}"',
        mapItemDelete: '{username} 刪除地圖上的項目 {id} 至 "{layer}"',
        mapLayerCreate: '{username} 新增地圖圖層 {id}',
        mapLayerUpdate: '{username} 變更地圖圖層 {id}',
        mapLayerDelete: '{username} 刪除地圖圖層 {id}',
        postCreate: '{username} 新增一個職位 {id}',
        postUpdate: '{username} 修改後 {id}',
        postDelete: '{username} 已刪除的張貼 {id}',
        realtyCreate: '{username} 新增屬性 {id}',
        realtyUpdate: '{username} 變更的房地產 {id}',
        realtyDelete: '{username} 刪除房地產 {id}',
        skinCreate: '{username} 添加了皮膚 {id}',
        skinUpdate: '{username} 變更皮膚 {id}',
        skinDelete: '{username} 刪除皮膚 {id}',
        userCreate: '{username} 新增使用者 {id}',
        userUpdate: '{username} 變更使用者 {id}',
        userDelete: '{username} 已刪除使用者 {id}',
        vehicleCreate: '{username} 新增運輸 {id}',
        vehicleUpdate: '{username} 變更運輸 {id}',
        vehicleDelete: '{username} 刪除運輸 {id}',
        animationCreate: '{username} 新增動畫 {id}',
        animationUpdate: '{username} 變更動畫 {id}',
        animationDelete: '{username} 刪除動畫 {id}',
        changelogCreate: '{username} 新增變更日誌 {id}',
        changelogUpdate: '{username} 更改了變更日誌 {id}',
        changelogDelete: '{username} 已刪除變更記錄 {id}'
      },
      pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{лог} few{лога} many{логов} other{логов}}'
    },
    menu: {
      settings: '設定',
      users: '用戶',
      categories: '類別',
      mapLayers: '地圖圖層',
      posts: '帖子',
      logs: '日誌'
    }
  },
  changelogPage: {
    titlePlaceholder: '輸入變更的名稱',
    descriptionPlaceholder: '輸入變更的描述',
    availableAt: '出版日期',
    soon: '通过',
    ago: '向後',
    fix: '已更正',
    feat: '已添加',
    chore: '已修改',
    style: '設計',
    refactor: '回收'
  },
  changelogsPage: {
    meta: {
      title: '變更歷史 | Majestic Wiki',
      description: 'Majestic RP 伺服器的完整更新與變更歷史。密切注意新功能、遊戲改進、錯誤修正與平衡。所有專案更新的最新資訊。'
    },
    title: '變更歷史',
    search: '尋找改變...'
  },
  homePage: {
    meta: {
      title: 'Majestic Wiki | GTA5 RP 百科全書',
      description: 'Majestic Wiki 是《GTA5》RP 玩家的完整百科全書。有關交通、房地產、商業、任務和遊戲其他方面的資訊。最新的價格、功能與取得方式資訊。'
    },
    tools: {
      map: {
        title: '地圖',
        description: '包含所有位置的伺服器地圖'
      },
      vehicles: {
        title: '交通',
        description: '伺服器上的所有可用傳輸'
      },
      realty: {
        title: '房地產',
        description: '所有獨棟房屋和公寓的數據'
      },
      biz: {
        title: '企業',
        description: '進階行程費用選項'
      },
      clothesMale: {
        title: '男裝',
        description: '男裝時裝秀'
      },
      clothesFemale: {
        title: '女裝',
        description: '女裝時裝秀'
      },
      skins: {
        title: '皮膚',
        description: '所有可用模式的目錄'
      },
      items: {
        title: '項目',
        description: '伺服器上可用的項目'
      },
      servers: {
        title: '伺服器',
        description: '所有 Majestic RP 伺服器清單'
      },
      animations: {
        title: '動畫',
        description: '伺服器上的獨特動畫'
      },
      changelogs: {
        title: '變更歷史',
        description: '專案變更的歷史'
      }
    },
    search: {
      title: '一般類別',
      description: '您可以在這裡找到有關Majestic伺服器及其系統的任何資訊'
    },
    categories: {
      collapseList: '收起清單',
      expandList: '展開清單'
    },
    online: {
      players: '{count} online',
      offline: '關閉'
    }
  },
  errorPage: {
    title: '找不到頁面',
    description: '頁面位址可能有錯字，或根本不存在: (',
    backToHome: '前往首頁'
  },
  bizPage: {
    meta: {
      title: 'GTA5 RP Businesses | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 伺服器上的完整企業目錄。全天候商店、加油站、槍店、汽車經銷商和其他生意。瞭解每項生意的獲利能力、要求以及購買方式。',
      custom: {
        title: '{type} | GTA5 RP Businesses | Majestic Wiki',
        description: '有關 {type} 在 GTA5 RP 中 Majestic RP 伺服器上的詳細資訊。特性、獲利能力、要求以及如何購買業務。'
      }
    },
    title: '商務桌',
    search: '搜尋企業…',
    groceryStore: '24小時全天候選購',
    gasStation: '加油',
    atm: '自動櫃員機',
    gunshop: '槍械商店',
    clothingStore: '服飾店',
    autoShop: '汽車經銷商',
    tattoo: '紋身店',
    lsc: '調音沙龍',
    barbershop: '理髮店',
    carWash: '洗車',
    viewOnMap: '在地圖上查看',
    mapPosition: '位置',
    pagingText: '找到{N, plural, few{about} many{~ about ~}} {N} {N, plural, one{business ~} few{business} many{企業} other{企業}}',
    noName: '商務 #{id}'
  },
  clothesPage: {
    meta: {
      title: 'GTA5 RP 服裝 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 伺服器上的完整服裝目錄。男女服裝、配件、背包、補丁、帽子和其他衣櫃物品。瞭解如何獲得以及每件衣服的價格。',
      custom: {
        title: '{type} | GTA5 RP 服裝 | Majestic Wiki',
        description: '目錄 {type} 在 GTA5 RP 中的 Majestic RP 伺服器上。詳細介紹服裝物品的取得方式、使用方法與成本。'
      }
    },
    titleMale: '男服桌',
    titleFemale: '女裝桌',
    search: '正在搜尋衣服...',
    top: '上',
    legs: '底部',
    shoes: '鞋',
    watch: '小時',
    mask: '口罩',
    decal: '修補程式',
    accessory: '配件',
    head: '帽子',
    bag: '背包',
    glasses: '眼鏡',
    ear: '耳',
    gloves: '手套',
    undershirt: 'T恤',
    noName: '服飾套裝 #{id}',
    drawable: '可繪製',
    texture: '紋理',
    male: '男',
    female: '女性',
    donate: '雄偉硬幣',
    battlePass: '戰鬥通行證',
    case: '案例',
    unique: '獨特',
    pagingText: '找到~ ~ about{N, plural, few{~ about} many{~ ~ ~衣服}} {N} ~衣服 {N, plural, one{~衣服} few{~衣服} many{~衣服} other{衣服}}'
  },
  itemsPage: {
    meta: {
      title: 'GTA5 RP 項目 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 伺服器上物品的完整目錄。古董、魚、礦石、木材、箱子、材料和其他物品。瞭解各物品的取得方式、使用方法與成本。',
      custom: {
        title: '{type} | GTA5 RP 項目 | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 伺服器上的目錄 {type} 。詳細介紹物品的取得方法、使用方式與成本。'
      }
    },
    title: '項目表',
    search: '正在搜尋項目...',
    antique: '古董',
    fish: '魚',
    ore: '礦石',
    wood: '木材',
    box: '盒子',
    material: '材料',
    event: '事件',
    food: '產品',
    alcohol: '酒類飲品',
    illegal: '非法',
    medical: '醫學',
    equipment: '設備',
    ammunition: '彈藥',
    tool: '工具',
    unique: '獨特',
    vehicle: '汽車零件',
    others: '其他',
    default: '標準',
    battlePass: '戰鬥通行證',
    case: '案例',
    unique: '獨特',
    pagingText: 'Найден{N, plural, few{о} many{о}} {N} {N, plural, one{предмет} few{предмета} many{предметов} other{предметов}}',
    noName: '主旨 #{id}'
  },
  mapPage: {
    meta: {
      title: 'GTA5 RP 地圖 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 伺服器的互動地圖。探索地點、尋找寶物、動物、電話和垃圾。詳細的世界地圖，可加入標籤和區域。所有重要地點和興趣點都在一個資源上！',
      custom: {
        title: '{type} | GTA5 RP Map | Majestic Wiki',
        description: 'GTA5 RP 中 Majestic RP 伺服器上 {type} 的詳細地圖。探索地點、尋找重要點並建立您自己的標記。地圖上物件位置的完整資訊！'
      }
    },
    dataLoading: '正在載入資料...',
    defaultPoints: '標準標籤',
    animalLayer: '動物地圖',
    rubbishLayer: '垃圾桶地圖',
    telephoneLayer: '電話地圖',
    treasureLayer: '藏寶圖',
    defaultLayer: '世界地圖',
    allLayers: '地圖圖層',
    backToHome: '返回首頁',
    zoomIn: '放大',
    zoomOut: '刪除',
    point: '點',
    polygon: '區域',
    addPoint: '新增一個點',
    addPolygon: '新增區域',
    copyCoordinates: '複製座標',
    savePointPosition: '儲存位置',
    editPointPosition: '變更位置',
    layerNamePlaceholder: '區域名稱',
    layerDescriptionPlaceholder: '區域描述'
  },
  serversPage: {
    meta: {
      title: '線上 GTA5 RP 伺服器 | Majestic Wiki',
      description: 'GTA5 RP 中 Majestic RP 伺服器上線上玩家的目前統計資料。目前線上、峰值、佇列和活動圖表。加入最受歡迎的角色扮演專案！'
    },
    dataLoading: '正在載入資料...',
    title: '線上伺服器',
    online: '{count} 線上',
    inQueue: '{count} 在佇列中',
    peakOnline: '{count} 峰值',
    currentOnline: '目前線上',
    peakOnline24h: '今日峰值',
    peakOnlineAllTime: '歷史高峰',
    peakOnlineTooltip: '{percent}相對於前一天的百分比',
    techWorks: '技術工作',
    forBeginners: '適合初學者',
    experience: '{multiplier}經驗 x',
    newServer: '新款',
    seasonPassMultiplier: '{multiplier}跳過經驗 x',
    combinedView: '一般',
    separateView: '分離式'
  },
  research: {
    placeholder: '正在搜尋網站上的資訊...',
    skin: '皮膚',
    vehicle: '交通',
    realty: '房地產',
    biz: '商務',
    clothing: '服裝',
    item: '主旨',
    post: '文章'
  },
  userModal: {
    login: '登錄',
    signout: '退出',
    register: '注冊',
    registerEnd: '注冊',
    restore: '恢復',
    restoreEnd: '還原',
    avatarDelete: '刪除頭像',
    headingUpdate: '編輯用戶 #{id}',
    postAdd: '發佈',
    postUpdate: '編輯文章',
    postDelete: '刪除文章',
    vehicleAdd: '新增交通服務',
    vehicleUpdate: '編輯交通服務',
    vehicleDelete: '移除運輸工具',
    realtyAdd: '新增旅居',
    realtyUpdate: '不動產變動',
    realtyDelete: '搬遷旅居',
    bizAdd: '新增企業',
    bizUpdate: '業務變更',
    bizDelete: '刪除業務',
    mapItemAdd: '將項目加入地圖',
    mapItemUpdate: '變更地圖上的元素',
    mapItemDelete: '刪除地圖上的項目',
    clothingAdd: '新增衣服',
    clothingUpdate: '換衣服',
    clothingDelete: '脫衣服',
    skinAdd: '新增外觀',
    skinUpdate: '修改外觀',
    skinDelete: '移除外觀',
    itemAdd: '新增餐點',
    itemUpdate: '變更項目',
    itemDelete: '刪除項目',
    fileUpload: '正在上傳圖片',
    viewPrivate: '查看隱私數據',
    doMagic: '存取魔法功能',
    animationAdd: '新增動畫',
    animationUpdate: '變更動畫',
    animationDelete: '刪除動畫',
    categoryAdd: '新增類別',
    categoryUpdate: '變更類別',
    categoryDelete: '刪除類別',
    mapLayerAdd: '新增地圖圖層',
    mapLayerUpdate: '變更地圖圖層',
    mapLayerDelete: '刪除地圖圖層',
    post: '文章',
    skin: '皮膚',
    biz: '商務',
    clothing: '服裝',
    item: '主旨',
    vehicle: '交通',
    animation: '動畫',
    category: '類別',
    realty: '房地產',
    mapLayer: '地圖圖層',
    changelog: '變更日誌'
  },
  restoreModal: {
    title: '恢復帳戶',
    newPassword: '新密碼',
    newPasswordRepeat: '重複新密碼',
    passwordConfirm: '更改密碼',
    passwordError: '密碼不相符'
  },
  bizModal: {
    titleUpdate: '業務變更 #{id}',
    titleAdd: '新增商家'
  },
  categoryModal: {
    titleUpdate: '類別變更 #{id}',
    titleAdd: '新增類別'
  },
  mapLayerModal: {
    titleUpdate: '#{id}變更地圖圖層',
    titleAdd: '新增地圖圖層'
  },
  clothingModal: {
    titleUpdate: '換衣服 #{id}',
    titleAdd: '添加新衣服',
    itemAdd: '添加着色',
    itemGenerate: '產生所有顏色'
  },
  itemModal: {
    titleUpdate: '變更主題 #{id}',
    titleAdd: '新增餐點'
  },
  realtyModal: {
    titleUpdate: '不動產變動 #{id}',
    titleAdd: '新增旅居'
  },
  skinModal: {
    titleUpdate: '皮膚變化 #{id}',
    titleAdd: '新增皮膚'
  },
  vehicleModal: {
    titleUpdate: '交通工具變更 #{id}',
    titleAdd: '新增車輛',
    titleDefault: '相關資訊 {name}',
    itemAdd: '新增車身套件'
  },
  mapItemModal: {
    titleUpdate: '變更地圖元素',
    titleAdd: '新增地圖元素',
    titleDelete: '刪除地圖項目',
    descriptionDelete: '您確定要從地圖上移除物品嗎？此動作無法取消。'
  },
  animationModal: {
    titleUpdate: '變更動畫 #{id}',
    titleAdd: '新增動畫'
  }
};