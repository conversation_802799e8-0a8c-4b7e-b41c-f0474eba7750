JWT_SECRET=super-secret

GATEWAY_HOST=127.0.0.1
GATEWAY_PORT=8080
GATEWAY_METRICS_PORT=8180
GATEWAY_SWAGGER_ENABLED=true

SOCKET_HOST=127.0.0.1
SOCKET_PORT=8081
SOCKET_METRICS_PORT=8181

UPLOADS_HOST=127.0.0.1
UPLOADS_PORT=8082
UPLOADS_METRICS_PORT=8182

REPORTS_HOST=127.0.0.1
REPORTS_PORT=8083
REPORTS_METRICS_PORT=8183

COMMON_HOST=127.0.0.1
COMMON_PORT=8084
COMMON_METRICS_PORT=8184

CAPTCHA_SITE_KEY=
CAPTCHA_SECRET_KEY=
CAPTCHA_DISABLED=true

ADMIN_HOST=127.0.0.1
ADMIN_PORT=8085
ADMIN_METRICS_PORT=8185

PAYMENTS_HOST=127.0.0.1
PAYMENTS_PORT=8086
PAYMENTS_METRICS_PORT=8186

BOTS_INTERNAL_HOST=127.0.0.1
BOTS_INTERNAL_PORT=8087
BOTS_INTERNAL_METRICS_PORT=8187

GAME_FEATURES_HOST=127.0.0.1
GAME_FEATURES_PORT=8088
GAME_FEATURES_METRICS_PORT=8188

OUTER_REDIS_HOST=127.0.0.1
OUTER_REDIS_PORT=6379
OUTER_REDIS_PASSWORD=

METRICS_DISABLED=true

LOGGER_LEVEL=debug

MAIL_HOST=smtp.elasticemail.com
MAIL_PORT=2525
MAIL_USER=
MAIL_PASSWORD=
MAIL_SECURE=false
# for production use:
# <AUTHOR> <EMAIL>'
MAIL_FROM='your_email_here'
MAIL_ASSETS_DIR=libs/mailer/assets

SENTRY_DSN=<sentry-dsn>

TWITCH_CLIENT_ID=
TWITCH_SECRET=

INFISICAL_URL=https://infisical.majestic-files.com
INFISICAL_CLIENT_ID=
INFISICAL_SECRET=
INFISICAL_PROJECT_ID=
INFISICAL_ENV=local
INFISICAL_DUMP_PATH=.
INFISICAL_DEV_PATH=.
INFISICAL_TOKEN=
