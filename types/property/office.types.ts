import { CHARACTER_PROPERTY_TABLE_NAME } from '../global/main-menu/character/property';

// not full list
/*
    import offices from '@serverConfig/families/offices';

    const officeId = global.families[familyId].officeId;
    const office = global.offices[officeId];

    return {
        ...offices[office.buildingId],
        ...office
    };
*/
export interface IFamilyOffice {
    id: number,
    buildingId: number,
    garageDecor: null | number,
    familyId: number | null,
    cash: number,
    price: number,
    posX: number,
    posY: number,
    posZ: number,
    officeInterior: number,
    officeMoneySafe: number,
    officeGunLocker: number,
    officeWardrobe: number,
    officeWarehouse: number,
    heliport: number,
    garage: number,
    // paidUntil: null,
    // auctionId: number,
    tableName: CHARACTER_PROPERTY_TABLE_NAME.OFFICE
}       