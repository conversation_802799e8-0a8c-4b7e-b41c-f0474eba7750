import { CHARACTER_PROPERTY_TABLE_NAME } from '../global/main-menu/character/property';

// not full list
/*
    import family_warehouses from '@serverConfig/families/family_warehouses';

    const warehouseId = global.families[familyId].warehouseId;
    const warehouse = global.warehouses[warehouseId];
    const warehouseType = warehouses.exteriors[warehouse.buildingId].type;

    return {
        ...warehouses.exteriors[warehouse.buildingId],
        ...warehouses.interiors[warehouseType],
        ...warehouse
    };
*/
export interface IFamilyWarehouse {
    id: number,
    buildingId: number,
    familyId: null | number,
    // paidUntil: string | null,
    type: string,
    price: number,
    posX: number,
    posY: number,
    posZ: number,
    capacity: number;
    auctionId: number,
    tableName: CHARACTER_PROPERTY_TABLE_NAME.WAREHOUSE
}

