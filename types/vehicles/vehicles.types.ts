export interface IBasicVehicleData {
    id: number,
    model: string,
    number: string | null,
    mileage: number,
    startPrice: number | null,
    args: string | null,
}

export interface IBasicVehicleDataWithBasicTuning extends IBasicVehicleData {
    engine: number | null,
    brakes: number | null,
    transmission: number | null,
    suspension: number | null,
    turbo: number | null,

    gpsTracker: number | null,
    bikeSafetyBelt: number | null,
    doorTazer: number | null,
    signaling: number | null,
}