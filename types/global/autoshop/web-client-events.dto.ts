// import { 
//   IW2CCarShowroomBuy, 
//   IW2CCarShowroomBuyModal,
//   IW2CCarShowroomOpenCase, 
//   IW2CCarShowroomSelectCar, 
//   IW2CCarShowroomSelectSets, 
//   IW2CCarShowroomSetColor, 
//   IW2CCarShowroomSetSets, 
//   IW2CCarShowroomToggleBar, 
//   IW2CPanelMenuDestroyed 
// } from "./web-client-events.types";

export type W2C_AUTOSHOP_PAYLOAD = {
  ['W2C:CarShowroom:Ready']: void;
  ['W2C:CarShowroom:Destroyed']: void;
  ['W2C:CarShowroom:SelectCar']: { model: string, isArrow?: boolean };
  ['W2C:CarShowroom:PreviewReady']: void;
  ['W2C:CarShowroom:PreviewDestroyed']: void;
  ['W2C:CarShowroom:PreviewTogglePartState']: { partId: string };
  ['W2C:CarShowroom:PreviewSetViewState']: { state: boolean };
  ['W2C:CarShowroom:SidebarMainReady']: void;
  ['W2C:CarShowroom:SidebarMainDestroyed']: void;
  ['W2C:CarShowroom:SetColor']: { colorId: number };
  ['W2C:CarShowroom:OpenCase']: { type: string; from: string; }
  ['W2C:PanelMenu:Destroyed']: { from: string };
  ['W2C:CarShowroom:TestDrive']: void;
  ['W2C:CarShowroom:KitsReady']: void;
  ['W2C:CarShowroom:KitsDestroyed']: void;
  ['W2C:CarShowroom:SelectKit']: { kitId: number };
  ['W2C:CarShowroom:SetKit']: { kitId: number };
  ['W2C:CarShowroom:Purchase']: { method: string, discount?: string}
};
