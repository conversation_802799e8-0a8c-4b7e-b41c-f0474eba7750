export interface IW2CCarShowroomSelectCar {
  model: string;
}

export interface IW2CCarShowroomSetColor {
  colorId: number;
}

export interface IW2CCarShowroomToggleBar {
  state: string;
}

export interface IW2CCarShowroomBuy {
  method: string;
  discount: number;
}

export interface IW2CCarShowroomBuyModal {
  method: string;
  discount: number;
}

export interface IW2CCarShowroomOpenCase {
  type: string;
  from: string;
}

export interface IW2CPanelMenuDestroyed {
  from: string;
}

export interface IW2CCarShowroomSelectSets {
  setId: number;
}

export interface IW2CCarShowroomSetSets {
  setId: number;
}
