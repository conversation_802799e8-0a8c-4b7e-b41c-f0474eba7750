export type WalletsData = {
  cash: number;
  bank?: number;
  coins: number;
};

export type VehicleParamsData = {
  maxSpeed: number;
  trunkCapacity: number;
}

export type VehicleParamsDataExtended = VehicleParamsData & {
  fuelCap?: number;
  fuelType?: string;
  maxAcceleration?: number;
}

export type VehicleData = {
  hasRL: boolean;
  brand: string;
  brandRL: string;
  title: string;
  titleRL: string;
  model: string;
  class: string;
  price: number;
  discount: number;
  inPass: boolean;
  params: VehicleParamsData;
  cases: CaseData[];
}

export type VehicleKitsData = {
  activeId: number;
  list: VehicleKitData[];
}

export type CaseData = { 
  name: string;
  isActive: boolean;
}

export type VehicleKitData = {
  id: number;
  title: string;
  img: string;
  price: number;
  isBasic: boolean;
  inInventory: boolean;
  isDonate: boolean;
  discount: number;
  inPass: boolean;
  cases: CaseData[];
}

export type VehicleDataExtended = VehicleData & {
  params: VehicleParamsDataExtended;
  hasKits: boolean;
  kitData: VehicleKitData;
  colorId: number;
  cases: CaseData[];
}

export type CouponData = {
  id: string;
  value: number;
}

export type StoreData = {
  id: number,
  wallets: WalletsData;
  vehicles: VehicleData[];
  coupons: CouponData[];
  cases: string[];
};

export type PreviewPartsData = {
  light?: boolean;
  doors?: boolean;
  hood?: boolean;
  trunk?: boolean;
}

export type PreviewData = {
  isExterior: boolean;
  parts: PreviewPartsData;
}