export enum CHARACTER_STATS_KEYS {
    REGISTRATION = 'registration',
    LEVEL = 'level',
    IN_GAME_TIME = 'in_game_time',
    ARRESTS = 'arrests',
    CRIMINAL_RECORDS = 'criminal_records',
    KILLS = 'kills',
    BANS = 'bans',
    MONEY_GAINED = 'money_gained',
    MONEY_SPENT = 'money_spent',
    MONEY_CASINO_WIN = 'money_casino_win',
    FAMILY_CONTRACTS = 'family_contracts',
    WORLD_QUESTS = 'world_quests',
    ACHIEVEMENTS = 'achievements',
    PROMO = 'promo',
    // HONOR = 'honor'
};

export enum CHARACTER_STATS_KEYS_PRIORITY {
    REGISTRATION = 100,
    LEVEL = 200,
    IN_GAME_TIME = 300,
    ARRESTS = 400,
    CRIMINAL_RECORDS = 500,
    KILLS = 600,
    BANS = 700,
    MONEY_GAINED = 800,
    MONEY_SPENT = 900,
    MONEY_CASINO_WIN = 1000,
    FAMILY_CONTRACTS = 1100,
    WORLD_QUESTS = 1200,
    ACHIEVEMENTS = 1300,
    PROMO = 1400
    // HONOR = 1500
};

export enum CHARACTER_STATS_TYPE_KEYS {
    BUTTON = 'button',
    SELECTOR = 'selector',
    SIMPLE = 'simple',
    SIMPLE_EDITED = 'simple_edited',
};

export interface ICharacterBaseStats {
    type: CHARACTER_STATS_TYPE_KEYS;
    priority: number;
    mainText: string;
};

export interface ICharacterStatsButton extends ICharacterBaseStats {
    type: CHARACTER_STATS_TYPE_KEYS.BUTTON;
};

export interface ICharacterStatsSimple extends ICharacterBaseStats {
    type: CHARACTER_STATS_TYPE_KEYS.SIMPLE;
    info?: {
        leftText?: string;
        rightText?: string;
    };
};

export interface ICharacterStatsSimpleEdited extends ICharacterBaseStats {
    type: CHARACTER_STATS_TYPE_KEYS.SIMPLE_EDITED;
    divider?: boolean;
    info: {
        leftSide?: {
            icon?: string;
            text: string;
        };
        rightSide?: {
            icon?: string;
            text: string;
        };
    };
};

export interface ICharacterStatsSelector extends ICharacterBaseStats {
    type: CHARACTER_STATS_TYPE_KEYS.SELECTOR;
    info: Array<{ key: string, mainText: string }>,
};

export type ICharacterStats =
    | ICharacterStatsButton
    | ICharacterStatsSimple
    | ICharacterStatsSimpleEdited
    | ICharacterStatsSelector;

export type ICharacterStatsData = Record<CHARACTER_STATS_KEYS, ICharacterStats>;

export interface IRequestBansTable {
    offset: number;
};

export interface ICharacterStatsTableRow {
    id: number;
    // type?: string;
    punisherName: string;
    reason: string;
    period: number;
    startDate: string;
    endDate: string;
};
export type ICharacterStatsTable = Array<ICharacterStatsTableRow>;

export interface ICharacterStatsTableSetData {
    info: {
        value1: number,
        value2?: number
    };

    rows: ICharacterStatsTable;
};