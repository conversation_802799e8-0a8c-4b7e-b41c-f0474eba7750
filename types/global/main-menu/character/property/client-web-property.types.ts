export enum CHARACTER_PROPERTY_UPGRADES_NAME {
    GUN_SAFE = 'gun_safe',
    SAFE = 'safe',
    HELIPAD = 'helipad',
    PANTRY = 'pantry', // кладовая
    FRIDGE = 'fridge',
    STORAGE = 'storage', // склад
    WORKSHOP = 'workshop',
    WARDROBE = 'wardrobe',
    GARAGE_ONE = 'garage_one',
    GARAGE_TWO = 'garage_two',
    GARAGE_THREE = 'garage_three'
}

export const CHARACTER_PROPERTY_UPGRADES_PRIORITY = {
    [CHARACTER_PROPERTY_UPGRADES_NAME.GUN_SAFE]: 100,
    [CHARACTER_PROPERTY_UPGRADES_NAME.HELIPAD]: 200,
    [CHARACTER_PROPERTY_UPGRADES_NAME.WARDROBE]: 300,
    [CHARACTER_PROPERTY_UPGRADES_NAME.SAFE]: 400,
    [CHARACTER_PROPERTY_UPGRADES_NAME.PANTRY]: 500,
    [CHARACTER_PROPERTY_UPGRADES_NAME.STORAGE]: 600,
    [CHARACTER_PROPERTY_UPGRADES_NAME.WORKSHOP]: 700,
    [CHARACTER_PROPERTY_UPGRADES_NAME.FRIDGE]: 800,
    [CHARACTER_PROPERTY_UPGRADES_NAME.GARAGE_ONE]: 900,
    [CHARACTER_PROPERTY_UPGRADES_NAME.GARAGE_TWO]: 1000,
    [CHARACTER_PROPERTY_UPGRADES_NAME.GARAGE_THREE]: 1100
} as const;

export enum CHARACTER_PROPERTY_SPECS_NAME {
    CAPACITY = 'capacity',
    GARAGE_PLACE = 'garage_place',
}

type HouseUpgrades = {
    [CHARACTER_PROPERTY_UPGRADES_NAME.WORKSHOP]: boolean;
};

type OfficeUpgrades = {
    [CHARACTER_PROPERTY_UPGRADES_NAME.GUN_SAFE]: boolean;
    [CHARACTER_PROPERTY_UPGRADES_NAME.SAFE]: boolean;
    [CHARACTER_PROPERTY_UPGRADES_NAME.HELIPAD]: boolean;
    [CHARACTER_PROPERTY_UPGRADES_NAME.PANTRY]: boolean;
    [CHARACTER_PROPERTY_UPGRADES_NAME.WARDROBE]: boolean;
    [CHARACTER_PROPERTY_UPGRADES_NAME.GARAGE_ONE]: boolean;
    [CHARACTER_PROPERTY_UPGRADES_NAME.GARAGE_TWO]: boolean;
    [CHARACTER_PROPERTY_UPGRADES_NAME.GARAGE_THREE]: boolean;
};

type ApartmentSpecs = {
    [CHARACTER_PROPERTY_SPECS_NAME.CAPACITY]: number;
    [CHARACTER_PROPERTY_SPECS_NAME.GARAGE_PLACE]: number;
};

type HouseSpecs = {
    [CHARACTER_PROPERTY_SPECS_NAME.CAPACITY]: number;
    [CHARACTER_PROPERTY_SPECS_NAME.GARAGE_PLACE]: number;
};

type WarehouseSpecs = {
    [CHARACTER_PROPERTY_SPECS_NAME.CAPACITY]: number;
};

type OfficeSpecs = {
    [CHARACTER_PROPERTY_SPECS_NAME.GARAGE_PLACE]: number;
};

export enum CHARACTER_PROPERTY_ICONS_TYPE {
    WEIGHT = 'weight',
    VEHICLE_ROOF = 'vehicle_roof',
    WRENCH = 'wrenches',
}

export enum CHARACTER_PROPERTY_TYPE {
    GUEST = 'guest',
    RENT = 'rent',
    FAMILY_RENT = 'family_rent',
    FAMILY_OWN = 'family_own',
    OWN = 'own',
}

export enum CHARACTER_PROPERTY_TABLE_NAME {
    AMMO_SHOP = 'ammo_shops',
    AUTO_SHOP = 'auto_shops',
    BARBER_SHOP = 'barber_shops',
    CARWASH_SHOP = 'carwash_shops',
    CLOTHES_SHOP = 'clothes_shops',
    FUEL_STATION = 'fuel_stations',
    ITEM_SHOP = 'item_shops',
    TATTOO_SHOP = 'tattoo_shops',
    TUNING_SHOP = 'tuning_shops',
    MOBILE_SHOP = 'mobile_shops',
    HOUSE = 'houses',
    APARTMENT = 'apartments',
    ATM = 'atm',
    WAREHOUSE = 'warehouses',
    OFFICE = 'offices',
    BIKERS_CUSTOMS = 'bikers_customs'
}

export enum CHARACTER_PROPERTY_RENT_TYPE {
    HOUR = 'hour',
    DAY = 'day'
}

interface IBaseCharacterPropertyData<T extends CHARACTER_PROPERTY_TABLE_NAME> {
    id: number;
    tableName: T;
    propertyType: CHARACTER_PROPERTY_TYPE;
    rentType?: CHARACTER_PROPERTY_RENT_TYPE;
    coords: { x: number, y: number, z: number };
    // name: string;
    price: number;
    zoneName: string;
    streetName: string;
    specs?: {};
    upgrades?: {};
}

export interface IApartmentPropertyData extends IBaseCharacterPropertyData<CHARACTER_PROPERTY_TABLE_NAME.APARTMENT> {
    tableName: CHARACTER_PROPERTY_TABLE_NAME.APARTMENT;
    buildingId: number;
    specs: ApartmentSpecs;
}

export interface IHousePropertyData extends IBaseCharacterPropertyData<CHARACTER_PROPERTY_TABLE_NAME.HOUSE> {
    tableName: CHARACTER_PROPERTY_TABLE_NAME.HOUSE;
    upgrades: HouseUpgrades;
    specs?: HouseSpecs;
}

export interface IWarehousePropertyData extends IBaseCharacterPropertyData<CHARACTER_PROPERTY_TABLE_NAME.WAREHOUSE> {
    tableName: CHARACTER_PROPERTY_TABLE_NAME.WAREHOUSE;
    buildingId: number;
    specs?: WarehouseSpecs;
    additionalType: string;
}

export interface IOfficePropertyData extends IBaseCharacterPropertyData<CHARACTER_PROPERTY_TABLE_NAME.OFFICE> {
    tableName: CHARACTER_PROPERTY_TABLE_NAME.OFFICE;
    buildingId: number;
    upgrades: OfficeUpgrades;
    specs?: OfficeSpecs;
}

export type IOtherPropertyData = IBaseCharacterPropertyData<
    Exclude<
        CHARACTER_PROPERTY_TABLE_NAME,
        | CHARACTER_PROPERTY_TABLE_NAME.APARTMENT
        | CHARACTER_PROPERTY_TABLE_NAME.HOUSE
        | CHARACTER_PROPERTY_TABLE_NAME.WAREHOUSE
        | CHARACTER_PROPERTY_TABLE_NAME.OFFICE
    >
>;

export type ICharacterPropertyData =
    | IApartmentPropertyData
    | IHousePropertyData
    | IWarehousePropertyData
    | IOfficePropertyData
    | IOtherPropertyData;