import { VehicleEnhancementsList, VehicleBasicTuningInfoList } from './index'

export type VehicleEnhancements = Array<{ id: (typeof VehicleEnhancementsList)[number], status: boolean }>;
export type VehicleTuningInfo = Array<{ id: (typeof VehicleBasicTuningInfoList)[number] | 'maxSpeed', status: number, installable?: boolean }>;

export interface ICharacterVehicle {
    id: number;
    // maxSpeed: number;
    title: string;
    price: number;
    utilizePrice: number;
    mileage: number;
    numberPlate: string;
    fuelType: string;
    enhancements: VehicleEnhancements;
    tuningInfo: VehicleTuningInfo;
    model: string;
}

export type ICharacterVehicles = Record<number, ICharacterVehicle>;
