export enum CHARACTER_GENERAL_KEYS {
    MONEY = "money",
    AGE = "age",
    PROPERTY = "property",
    JOB = "job",
    MARRIED = "married",
    FRACTION = "fraction",
    FAMILY = "family",
    PREMSUBSCRIPTION = "premSubscription",
    PAYDAY = "payday"
};

export const CHARACTER_GENERAL_KEYS_PRIORITY = {
    [CHARACTER_GENERAL_KEYS.MONEY]: 100,
    [CHARACTER_GENERAL_KEYS.AGE]: 200,
    [CHARACTER_GENERAL_KEYS.PROPERTY]: 300,
    [CHARACTER_GENERAL_KEYS.JOB]: 400,
    [CHARACTER_GENERAL_KEYS.MARRIED]: 500,
    [CHARACTER_GENERAL_KEYS.FRACTION]: 600,
    [CHARACTER_GENERAL_KEYS.FAMILY]: 700,
    [CHARACTER_GENERAL_KEYS.PREMSUBSCRIPTION]: 800,
    [CHARACTER_GENERAL_KEYS.PAYDAY]: 900
} as const;

export type ICharacterGeneral = {
    mainText: string;
    priority: number;
    leftText?: string;
    rightText?: string;
}

export type ICharacterGeneralData = Record<CHARACTER_GENERAL_KEYS, ICharacterGeneral>;