export type Subtab = {
    order: number;
    labels?: string[];
};

export type Banner = {
    theme: string;
    timestamp: number;
};

export type SubtabRecords = Record<string, Subtab>;

export type Tab = {
    order: number;
    labels?: string[];
    banner?: Banner;
    subtabs?: SubtabRecords;
};

export type TabRecords = Record<string, Tab>;

export type Data = {
    login: string;
    adminLevel: number;
    wallets: Record<string, number>;
    tabs: TabRecords;
};