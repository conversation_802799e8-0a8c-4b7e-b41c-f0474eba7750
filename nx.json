{"$schema": "./node_modules/nx/schemas/nx-schema.json", "targetDefaults": {"build": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"], "configurations": {"production": {"generatePackageJson": true, "generateLockfile": true}}}, "lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.js"]}, "test": {"cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"]}, "e2e": {"cache": true}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js"], "sharedGlobals": []}}