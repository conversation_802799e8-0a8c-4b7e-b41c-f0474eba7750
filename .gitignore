# See http://help.github.com/ignore-files/ for more about ignoring files.

**/prisma-client-backend/src/generated
**/prisma-client-game/src/generated

# compiled output
dist
runtime
tmp
/out-tsc

# dependencies
node_modules
node_modules_docker

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

.nx/cache


# config
config.json

# env for local services
.env
.env.*
!.env.example

infisical-dump.json

output
dynamic-gitlab-ci.yml
