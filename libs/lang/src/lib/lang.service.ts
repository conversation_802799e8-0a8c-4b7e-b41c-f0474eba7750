import { Injectable, OnModuleInit } from '@nestjs/common'
import i18next, { Resource, ResourceLanguage } from 'i18next'
import { languagesConf } from '@i18n/game'

@Injectable()
export class LangService implements OnModuleInit {
  t = i18next.t

  onModuleInit() {
    i18next.init({
      lng: 'ru',
      resources: Object.entries(languagesConf).reduce(
        (acc, [lang, translation]: [lang: string, translation: ResourceLanguage]) => {
          acc[lang] = {
            translation,
          }
          return acc
        },
        {} as Resource,
      ),
    })
  }
}
