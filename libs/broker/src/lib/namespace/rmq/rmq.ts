import { ChannelModel, Channel, ConsumeMessage, Options } from 'amqplib'
import { NameSpace, NameSpaceSubscribeCb } from '../'

export class RabbitMQNameSpace<Req, Res> implements NameSpace<Req, Res> {
  private channel?: Channel
  private connection: ChannelModel
  private readonly queueName: string
  private readonly exchangeName: string
  private consumerTag: string | null = null
  private readonly exchangeOptions: Options.AssertExchange
  private readonly queueOptions: Options.AssertQueue

  constructor(
    connection: ChannelModel,
    exchangeName: string,
    queueName: string,
    options: {
      exchangeOptions?: Options.AssertExchange
      queueOptions?: Options.AssertQueue
    } = {},
  ) {
    this.connection = connection
    this.exchangeName = exchangeName
    this.queueName = queueName
    this.exchangeOptions = options.exchangeOptions || { durable: true }
    this.queueOptions = options.queueOptions || { durable: true }
  }

  async init(): Promise<void> {
    if (!this.channel) {
      try {
        this.channel = await this.connection.createChannel()
        await this.channel.assertExchange(this.exchangeName, 'topic', this.exchangeOptions)
      } catch (error) {
        throw new Error(`Failed to initialize channel: ${error}`)
      }
    }
  }

  async request(data: Req): Promise<Res> {
    if (!this.channel) await this.init()

    return new Promise((resolve, reject) => {
      const correlationId = Math.random().toString() + Date.now().toString()
      const replyQueue = `reply-${correlationId}`
      const timeout = setTimeout(() => reject(new Error('Request timed out')), 10000)

      try {
        this.channel
          .assertQueue(replyQueue, { exclusive: true, autoDelete: true })
          .then(() => {
            this.channel.consume(
              replyQueue,
              (msg: ConsumeMessage) => {
                if (msg && msg.properties.correlationId === correlationId) {
                  clearTimeout(timeout)
                  resolve(JSON.parse(msg.content.toString()) as Res)
                  this.channel.deleteQueue(replyQueue)
                }
              },
              { noAck: true },
            )

            this.channel.publish(
              this.exchangeName,
              this.queueName,
              Buffer.from(JSON.stringify(data)),
              {
                correlationId,
                replyTo: replyQueue,
                persistent: true,
              },
            )
          })
          .catch(error => {
            clearTimeout(timeout)
            reject(new Error(`Failed to assert reply queue: ${error}`))
          })
      } catch (error) {
        clearTimeout(timeout)
        reject(new Error(`Request failed: ${error}`))
      }
    })
  }

  async publish(data: Req): Promise<void> {
    if (!this.channel) await this.init()
    try {
      this.channel.publish(this.exchangeName, this.queueName, Buffer.from(JSON.stringify(data)), {
        persistent: true,
      })
    } catch (error) {
      throw new Error(`Failed to publish message: ${error}`)
    }
  }

  async subscribeGroup(groupName: string, cb: NameSpaceSubscribeCb<Req, Res>): Promise<void> {
    if (!this.channel) await this.init()
    if (this.consumerTag) throw new Error('Already subscribed')

    const queueName = `${this.queueName}.${groupName}`
    try {
      await this.channel.assertQueue(queueName, this.queueOptions)
      await this.channel.bindQueue(queueName, this.exchangeName, this.queueName)

      this.consumerTag = (
        await this.channel.consume(queueName, async (msg: ConsumeMessage) => {
          if (msg) {
            try {
              const data = JSON.parse(msg.content.toString()) as Req
              const result = await cb(data)
              this.channel.ack(msg)

              if (msg.properties.replyTo) {
                this.channel.sendToQueue(
                  msg.properties.replyTo,
                  Buffer.from(JSON.stringify(result)),
                  { correlationId: msg.properties.correlationId },
                )
              }
            } catch (error) {
              this.channel.nack(msg)
            }
          }
        })
      ).consumerTag
    } catch (error) {
      throw new Error(`Failed to subscribe to group: ${error}`)
    }
  }

  async subscribe(cb: NameSpaceSubscribeCb<Req, Res>): Promise<void> {
    if (!this.channel) await this.init()
    if (this.consumerTag) throw new Error('Already subscribed')

    try {
      await this.channel.assertQueue(this.queueName, this.queueOptions)
      await this.channel.bindQueue(this.queueName, this.exchangeName, this.queueName)

      this.consumerTag = (
        await this.channel.consume(this.queueName, async (msg: ConsumeMessage) => {
          if (msg) {
            try {
              const data = JSON.parse(msg.content.toString()) as Req
              const result = await cb(data)
              this.channel.ack(msg)

              if (msg.properties.replyTo) {
                this.channel.sendToQueue(
                  msg.properties.replyTo,
                  Buffer.from(JSON.stringify(result)),
                  { correlationId: msg.properties.correlationId },
                )
              }
            } catch (error) {
              this.channel.nack(msg)
            }
          }
        })
      ).consumerTag
    } catch (error) {
      throw new Error(`Failed to subscribe: ${error}`)
    }
  }

  async unsubscribe(): Promise<void> {
    if (this.channel && this.consumerTag) {
      try {
        await this.channel.cancel(this.consumerTag)
        this.consumerTag = null
        await this.channel.close()
      } catch (error) {
        throw new Error(`Failed to unsubscribe: ${error}`)
      }
    }
  }
}
