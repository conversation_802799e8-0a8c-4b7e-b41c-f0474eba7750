import { SetMetadata } from '@nestjs/common'
import { BrokerN<PERSON>space<PERSON>ey } from '@shared_config/broker/namespaces'

export const BROKER_SUBSCRIBE_GROUP = Symbol()
export interface IBrokerSubscribeGroupMeta {
  nameSpace: BrokerNamespaceKey
  groupName: string
}

export const BrokerSubscribeGroupHandler = (nameSpace: BrokerNamespaceKey, groupName: string) => {
  return SetMetadata(BROKER_SUBSCRIBE_GROUP, { nameSpace, groupName })
}
