import { Injectable, OnModuleInit, Logger, OnApplicationShutdown } from '@nestjs/common'

import { BrokerNamespaceKey } from '@shared_config/broker/namespaces'
import { DiscoveryService } from '@golevelup/nestjs-discovery'
import {
  BROKER_SUBSCRIBE_HANDLER,
  BROKER_SUBSCRIBE_GROUP,
  IBrokerSubscribeGroupMeta,
} from '../decorators'
import { RabbitMQNameSpace } from '../namespace/rmq'
import { IBroker } from '@shared_config/broker'

type subscribeCb = (...args: any) => any | Promise<any>

@Injectable()
export class RmqBroker implements IBroker, OnModuleInit, OnApplicationShutdown {
  private readonly logger = new Logger(RmqBroker.name)

  namespaces: IBroker['namespaces']
  private collectedSubscribers = new Map<string, subscribeCb[]>()
  private collectedGroupSubscribers = new Map<string, subscribeCb>()

  constructor(private readonly discoveryService: DiscoveryService) {}

  async onModuleInit() {
    await this.collectSubscribers()
    await this.collectGroupSubscribers()

    await this.init()
  }

  async onApplicationShutdown() {
    return
  }

  private async collectSubscribers() {
    const discoveredHandlers =
      await this.discoveryService.controllerMethodsWithMetaAtKey<BrokerNamespaceKey>(
        BROKER_SUBSCRIBE_HANDLER,
      )

    for (const { meta: topic, discoveredMethod } of discoveredHandlers) {
      if (this.collectedSubscribers.has(topic)) {
        this.logger.debug(`Subscription to topic ${topic} already exists, skipping`)

        this.collectedSubscribers
          .get(topic)!
          .push(discoveredMethod.handler.bind(discoveredMethod.parentClass))

        continue
      }

      this.collectedSubscribers.set(topic, [
        discoveredMethod.handler.bind(discoveredMethod.parentClass),
      ])
    }
  }

  private async collectGroupSubscribers() {
    const discoveredHandlers =
      await this.discoveryService.controllerMethodsWithMetaAtKey<IBrokerSubscribeGroupMeta>(
        BROKER_SUBSCRIBE_GROUP,
      )

    for (const { meta, discoveredMethod } of discoveredHandlers) {
      const groupId = `${meta.nameSpace}-${meta.groupName}`

      if (this.collectedGroupSubscribers.has(groupId)) {
        this.logger.debug(`Group subscription ${groupId} already exists, skipping`)
        continue
      }

      this.collectedGroupSubscribers.set(
        groupId,
        discoveredMethod.handler.bind(discoveredMethod.parentClass),
      )
    }
  }

  private async init() {
    this.namespaces = {
      [BrokerNamespaceKey.UsersRegister]: new RabbitMQNameSpace(
        this.connection,
        'users',
        'register',
      ),
    }
  }
}
