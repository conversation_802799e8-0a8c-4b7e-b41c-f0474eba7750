import { DynamicModule, Provider } from '@nestjs/common'
import { PrometheusModule } from '@willsoto/nestjs-prometheus'
import { MetricsController } from './metrics.controller'
import { MetricsModuleOptions } from './metrics.models'

export class MetricsModule {
  static forRoot(options: MetricsModuleOptions): DynamicModule {
    const promModule = PrometheusModule.register({
      path: '/metrics',
      defaultMetrics: { enabled: true },
      defaultLabels: options.labels,
      controller: MetricsController,
    })
    return {
      module: promModule.module,
      global: promModule.global,
      providers: [
        ...(promModule.providers || []),
        ...options.customProviders,
        { provide: 'OPTIONS', useValue: options },
      ],
      controllers: promModule.controllers,
      exports: [...(promModule.exports || []), ...options.customProviders],
    }
  }

  static forAppRoot(appName: string, customProviders: Provider[] = []) {
    return this.forRoot({
      labels: {
        app: appName,
        appHost: process.env['HOSTNAME'] ?? 'unknown',
      },
      customProviders,
    })
  }
}
