import { PrismaService } from '@majestic-backend/database'
import { Controller, Get, Inject, Optional, Res, UseGuards } from '@nestjs/common'
import { PrometheusController } from '@willsoto/nestjs-prometheus'
import { Response } from 'express'
import { MetricsGuard } from './metrics.guard'
import { MetricsModuleOptions } from './metrics.models'

@Controller()
export class MetricsController extends PrometheusController {
  constructor(
    @Optional() private readonly database: PrismaService,
    @Inject('OPTIONS') private readonly options: MetricsModuleOptions,
  ) {
    super()
  }
  @Get()
  @UseGuards(MetricsGuard)
  override async index(@Res({ passthrough: true }) response: Response) {
    const serviceName = this.options.labels['app'] || 'unknown'

    const dbMetrics = await this.database?.getAllMetrics(serviceName).catch(() => '')
    const defaultMetrics = await super.index(response).catch(() => '')
    return defaultMetrics + dbMetrics
  }
}
