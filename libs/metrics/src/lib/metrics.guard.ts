import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common'
import { CommonConfig } from '@majestic-backend/config'

@Injectable()
export class MetricsGuard implements CanActivate {
  constructor(private commonConfig: CommonConfig) {}
  async canActivate(context: ExecutionContext) {
    const apiKey = this.commonConfig.METRICS_API_KEY
    if (!apiKey) {
      return true
    }
    const req = context.switchToHttp().getRequest()
    return this.extractApiKeyFromHeader(req) === apiKey
  }

  private extractApiKeyFromHeader(req: any): string | undefined {
    return req.headers['x-metrics-api-key']
  }
}
