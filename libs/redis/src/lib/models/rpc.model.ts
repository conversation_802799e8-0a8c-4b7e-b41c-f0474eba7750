export interface IRpcRequest<T = unknown> {
  requestId: string
  payload: T
}

export interface IRpcResponse<T extends Record<string, unknown> = Record<string, unknown>> {
  requestId: string
  status: boolean
  payload: RpcSuccessPayload<T> | RpcErrorPayload<T>
}

type RpcSuccessPayload<T extends Record<string, unknown>> = T

type RpcErrorPayload<T extends Record<string, unknown>> = { message: string } & T
