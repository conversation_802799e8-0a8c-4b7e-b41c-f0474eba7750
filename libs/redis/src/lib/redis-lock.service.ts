import { Injectable, Logger } from '@nestjs/common'
import { RedisService } from './redis.service'

@Injectable()
export class RedisLockService {
  private logger = new Logger(RedisLockService.name)

  constructor(private readonly redisService: RedisService) {}

  async acquireLock(key: string, ttl = 10): Promise<boolean> {
    try {
      const lockAcquired = await this.redisService.set(key, 'locked', {
        NX: true,
        EX: ttl,
      })
      // this.logger.debug(`acquireLock: ${lockAcquired}`)

      if (lockAcquired) return true
    } catch (error) {
      this.logger.debug(`Error acquiring lock for key ${key}:`, error)

      return false
    }

    // this.logger.debug(`Key: ${key} is already locked`)
    return false
  }

  async releaseLock(key: string): Promise<boolean> {
    try {
      const lockReleased = await this.redisService.del(key)
      // this.logger.debug(`releaseLock: ${lockReleased}`)

      return lockReleased === 1
    } catch (error) {
      this.logger.debug(`Error releasing lock for key ${key}:`, error)

      return false
    }
  }
}
