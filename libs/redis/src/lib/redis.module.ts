import { Module } from '@nestjs/common'
import { RedisService } from './redis.service'
import { RedisLockService } from './redis-lock.service'
import { RedisListenerService } from './redis-listener.service'
import { IRedisModuleOptions } from './models/options.model'
import { REDIS_MODULE_OPTIONS_KEY } from './constants'
import { RedisRpcService } from './redis-rpc.service'
import { RedisEventsService } from './redis-events.service'
import { DiscoveryModule } from '@golevelup/nestjs-discovery'

@Module({})
export class RedisModule {
  static forRoot(options?: IRedisModuleOptions) {
    return {
      module: RedisModule,
      global: options?.global,
      imports: [DiscoveryModule],
      providers: [
        RedisService,
        RedisLockService,
        RedisListenerService,
        RedisRpcService,
        RedisEventsService,
        { provide: REDIS_MODULE_OPTIONS_KEY, useValue: options },
      ],
      exports: [
        RedisService,
        RedisLockService,
        RedisListenerService,
        RedisRpcService,
        RedisEventsService,
      ],
    }
  }
}
