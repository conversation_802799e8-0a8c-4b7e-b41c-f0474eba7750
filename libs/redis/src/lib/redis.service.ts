import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { createClient as redisClientClient, SetOptions } from 'redis'
import {
  IBroadcastSocketPayload,
  IPublishSocketPayload,
  SOCKET_CHANNEL,
  SOCKET_MESSAGE,
} from '@majestic-backend/types'
import { RedisConfig } from '@majestic-backend/config'

export type RedisClient = ReturnType<typeof redisClientClient>

@Injectable()
export class RedisService implements OnModuleInit {
  private logger = new Logger('Redis')
  private client: RedisClient | null = null
  constructor(private readonly redisConfig: RedisConfig) {}

  async onModuleInit() {
    await this.connect()
  }

  checkClientIsReady() {
    if (!this.client?.isReady) {
      this.logger.error('Error get key. No connection')
      throw Error('Error get key. No connection')
    }
  }

  public createClient() {
    return redisClientClient({
      socket: {
        host: this.redisConfig.HOST,
        port: this.redisConfig.PORT,
      },
      password: this.redisConfig.PASSWORD,
    }).on('error', err => this.logger.error(`Redis create client error`, err))
  }

  async connect() {
    this.client = await this.createClient()
      .on('error', err => this.logger.error(`Redis connect error`, err))
      .connect()
    this.logger.log('Successfully created client for publish')
  }

  publish(channel: string, payload: object | string) {
    this.checkClientIsReady()
    let parsed: Record<string, string> | string | null = null

    try {
      parsed = typeof payload === 'object' ? JSON.stringify(payload) : payload
    } catch (e) {
      this.logger.error(`Error on publish. Wrong payload format`, e)
      throw Error('Error on publish. Wrong payload format ' + payload)
    }

    return this.client?.publish(channel, parsed)
  }

  async publishToSockets<T extends SOCKET_MESSAGE>(
    data: Omit<IPublishSocketPayload<T>, 'propagationMode'>,
  ) {
    const receivers = Array.isArray(data.receivers) ? data.receivers : [data.receivers]
    for (const receiver of receivers) {
      this.publish(SOCKET_CHANNEL, {
        ...data,
        receivers: receiver,
        propagationMode: 'toProvided',
      })
    }
  }

  async broadcastToAllSockets<T extends SOCKET_MESSAGE>(
    data: Omit<IBroadcastSocketPayload<T>, 'propagationMode'>,
  ) {
    this.publish(SOCKET_CHANNEL, { ...data, propagationMode: 'broadcast' })
  }

  async get(key: string) {
    this.checkClientIsReady()

    return this.client?.get(key)
  }

  async exists(key: string) {
    this.checkClientIsReady()

    return this.client?.exists(key)
  }

  async hGet(key: string, field: string) {
    this.checkClientIsReady()

    return this.client?.hGet(key, field)
  }

  async set(key: string, payload: string | Buffer | number, options?: SetOptions) {
    this.checkClientIsReady()

    return this.client?.set(key, payload, options)
  }

  async del(key: string) {
    this.checkClientIsReady()

    return this.client?.del(key)
  }

  async keys(pattern: string) {
    this.checkClientIsReady()

    return this.client?.keys(pattern)
  }

  async hExists(key: string, field: string) {
    this.checkClientIsReady()

    return this.client?.hExists(key, field)
  }

  async hSet(key: string, field: string, value: number | string | Buffer) {
    this.checkClientIsReady()

    return this.client?.hSet(key, field, value)
  }

  async hDel(key: string, field: string) {
    this.checkClientIsReady()

    return this.client?.hDel(key, field)
  }

  async incr(key: string) {
    this.checkClientIsReady()

    return this.client?.incr(key)
  }

  async expire(key: string, ttl: number) {
    this.checkClientIsReady()

    return this.client?.expire(key, ttl)
  }

  async append(key: string, payload: string) {
    this.checkClientIsReady()

    return this.client?.append(key, payload)
  }

  async subscribe(key: string, cb: (args: string) => unknown) {
    this.checkClientIsReady()

    return this.client?.subscribe(key, cb)
  }
}
