import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { RedisClient, RedisService } from './redis.service'
import { IEvents } from './models/event.model'

@Injectable()
export class RedisListenerService implements OnModuleInit {
  private logger = new Logger(RedisListenerService.name)
  private _events: IEvents = {}
  private _initialEvents: IEvents = {}

  private redisClient: RedisClient | null = null

  constructor(private readonly redisService: RedisService) {}

  onModuleInit() {
    this.initRedisClient().then(() => {
      this.addEvents(this._initialEvents).then(() => (this._initialEvents = {}))
    })
  }

  private async initRedisClient() {
    this.redisClient = await this.redisService.createClient().connect()
    this.logger.log('Redis client initialized')
  }

  public getEvents() {
    return this._events
  }

  public async addEvents(events: IEvents) {
    if (!Object.keys(events).length) return

    if (!this.redisClient) {
      this._initialEvents = { ...this._initialEvents, ...events }
      return
    }

    const newEventsKeys = Object.keys(events).filter(key => !this._events[key])
    for (const key of newEventsKeys) {
      this._events[key] = events[key]
      await this.redisClient?.subscribe(key, data => this.onMessage(key, data))
    }
    this.logger.debug(`[addEvents] Events: ${newEventsKeys.join(', ')} successfully added`)
  }

  public removeEvents(...eventsKeys: string[]) {
    for (const eventKey of eventsKeys) {
      if (!this._events[eventKey]) {
        continue
      }

      this.redisClient?.unsubscribe(eventKey, data => this.onMessage(eventKey, data))
      delete this._events[eventKey]
    }
    this.logger.debug(`[removeEvents] Events ${eventsKeys.join(', ')} successfully removed`)
  }

  public async stop() {
    if (!this.redisClient) {
      return
    }

    await this.redisClient.disconnect()
    this.redisClient = null
    this._events = {}

    this.logger.log('[stop] Redis listener stopped')
  }

  private async onMessage(key: keyof IEvents, data: string) {
    this.logger.log(`[onMessage] Got message: ${key}`)

    let parsed = null
    try {
      parsed = JSON.parse(data)
      await this._events[key](parsed)
    } catch (e) {
      this.logger.error(`[onMessage] Error on message parsing. Channel: ${key}`, e)
    }
  }
}
