import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { RedisListenerService } from './redis-listener.service'
import { IRedisModuleOptions } from './models/options.model'
import { REDIS_MODULE_OPTIONS_KEY } from './constants'
import { IEvents } from './models/event.model'
import { DiscoveryService } from '@golevelup/nestjs-discovery'
import { RPC_HANDLER } from './decorators'
import { IRpcRequest } from './models/rpc.model'
import { RedisRpcService } from './redis-rpc.service'

@Injectable()
export class RedisEventsService implements OnModuleInit {
  private logger = new Logger(RedisEventsService.name)

  constructor(
    @Inject(REDIS_MODULE_OPTIONS_KEY) private readonly redisModuleOptions: IRedisModuleOptions,
    private readonly redisListenerService: RedisListenerService,
    private readonly redisRpcService: RedisRpcService,
    private readonly discoveryService: DiscoveryService,
  ) {}

  async onModuleInit() {
    if (!this.shouldInit()) return
    await this.initRpcHandlers()
  }

  private async initRpcHandlers() {
    const discoveredHandlers = await this.discoveryService.controllerMethodsWithMetaAtKey<string>(
      RPC_HANDLER,
    )
    const events: IEvents = {}
    discoveredHandlers.forEach(discoveredHandler => {
      const { meta, discoveredMethod } = discoveredHandler

      const wrapper = async (event: IRpcRequest<unknown>) => {
        try {
          const res = await discoveredMethod.handler.bind(discoveredMethod.parentClass.instance)(
            event.payload,
          )
          await this.redisRpcService.answerOnRpcRequest(event.requestId, res, true)
        } catch (e) {
          await this.redisRpcService.answerOnRpcRequest(
            event.requestId,
            { message: JSON.stringify(e) },
            false,
          )
        }
      }

      events[meta] = wrapper
    })

    if (Object.keys(events).length) {
      this.redisListenerService.addEvents(events)
    }
  }

  public async addEvents(events: IEvents) {
    if (!this.shouldInit()) {
      this.logger.verbose(`[addEvents] Events: ${Object.keys(events).join(', ')} cannot be added`)
      return
    }

    await this.redisListenerService.addEvents(events)
  }

  private shouldInit() {
    if (this.redisModuleOptions?.onlyMasterReplicaEvents) {
      return process.env['REPLICA'] === '1'
    }
    return true
  }
}
