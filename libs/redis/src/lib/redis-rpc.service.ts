import { Injectable, Logger } from '@nestjs/common'
import { RedisService } from './redis.service'
import { v4 as uuidv4 } from 'uuid'
import { RedisListenerService } from './redis-listener.service'
import { IRpcRequest, IRpcResponse } from './models/rpc.model'

interface IMakeRpcRequestOptions {
  timeout?: number
}

@Injectable()
export class RedisRpcService {
  private logger = new Logger(RedisRpcService.name)
  private readonly rpcPrefix = 'rpc'

  constructor(
    private readonly redisService: RedisService,
    private readonly redisListenerService: RedisListenerService,
  ) {}

  public makeRpcRequest<T extends IRpcRequest['payload'], Y extends IRpcResponse['payload']>(
    data: { channel: string; payload: T },
    options: IMakeRpcRequestOptions = { timeout: 5000 },
  ): Promise<IRpcResponse<Y>> {
    const rpcId = uuidv4()
    const rpcChannel = this.genRpcChannel(rpcId)

    return new Promise((res, rej) => {
      const timeout = setTimeout(() => {
        this.redisListenerService.removeEvents(rpcChannel)
        rej('Rpc timeout')
      }, options.timeout)

      const subscribeCallback = (data: unknown) => {
        this.redisListenerService.removeEvents(rpcChannel)
        clearTimeout(timeout)
        res(data as IRpcResponse<Y>)
      }

      try {
        this.redisListenerService
          .addEvents({
            [rpcChannel]: subscribeCallback,
          })
          .then(() =>
            this.redisService.publish(data.channel, {
              requestId: rpcId,
              payload: data.payload,
            }),
          )
      } catch (e) {
        this.redisListenerService.removeEvents(rpcChannel)
        clearTimeout(timeout)
        this.logger.error(`[makeRpcRequest] Error during rpc request on channel ${data.channel}`)
        rej('Error during rpc request')
      }
    })
  }

  public answerOnRpcRequest<T extends IRpcResponse['payload']>(
    rpcId: string,
    payload: T,
    status = false,
  ) {
    return this.redisService.publish(this.genRpcChannel(rpcId), {
      requestId: rpcId,
      status,
      payload,
    } satisfies IRpcResponse<T>)
  }

  private genRpcChannel(id: string) {
    return this.rpcPrefix + ':' + id
  }

  public async processRpcRequest<
    T extends IRpcRequest = IRpcRequest<unknown>,
    Y extends Record<string, any> = object,
  >(event: T, cb: (ev: T['payload']) => Promise<Y>, returnResponse = false) {
    try {
      const res = await cb(event.payload)
      await this.answerOnRpcRequest(event.requestId, returnResponse ? res : {})
    } catch (e) {
      await this.answerOnRpcRequest(event.requestId, { message: JSON.stringify(e) }, false)
    }
  }
}
