import { Injectable, Logger } from '@nestjs/common'
import { S3 } from '@aws-sdk/client-s3'
import { S3Config } from '@majestic-backend/config'

@Injectable()
export class S3Service {
  private readonly logger = new Logger(S3Service.name)
  private client: S3 | null = null
  private readonly bucket: string
  private readonly pathPrefix: string = ''

  constructor(private readonly s3Config: S3Config) {
    if (!this.s3Config) {
      throw new Error('S3 config is not provided')
    }

    const [bucket, ...pathPrefixParts] = this.s3Config.BUCKET.split('/')
    this.bucket = bucket
    if (pathPrefixParts.length > 0) {
      this.pathPrefix = pathPrefixParts.join('/') + '/'
    }

    this.client = new S3({
      endpoint: this.s3Config.ENDPOINT,
      region: this.s3Config.REGION,
      credentials: {
        accessKeyId: this.s3Config.ACCESS_KEY_ID || '',
        secretAccessKey: this.s3Config.SECRET_ACCESS_KEY || '',
      },
    })
  }

  pushFile({
    path,
    payload,
    contentType,
  }: {
    path: string
    payload: Buffer
    contentType: string | undefined
  }) {
    return this.client?.putObject({
      Key: `${this.pathPrefix}${path}`,
      Body: payload,
      ContentType: contentType,
      Bucket: this.bucket,
    })
  }

  copyFile({ from, to }: { from: string; to: string }) {
    return this.client?.copyObject({
      CopySource: encodeURI(`${this.bucket}/${this.pathPrefix}${from}`),
      Key: `${this.pathPrefix}${to}`,
      Bucket: this.bucket,
    })
  }

  deleteFile({ path }: { path: string }) {
    return this.client?.deleteObject({
      Key: `${this.pathPrefix}${path}`,
      Bucket: this.bucket,
    })
  }

  getFileUrl(subpath: string) {
    // return this.configService.get('s3.publicUrl') + '/' + subpath
    return '/' + subpath
  }
}
