import { GitlabJobEvent } from './events/gitlab-job.event'

export const STATUS_MESSAGES: Record<
  GitlabJobEvent['data']['build_status'],
  {
    message: string
    finished?: boolean
  }
> = {
  created: {
    message: '✨ Создано',
  },
  pending: {
    message: '⌛ Ожидание',
  },
  running: {
    message: '⚙️ Выполняется',
  },
  success: {
    message: '✅ Выполнено',
    finished: true,
  },
  failed: {
    message: '⚠️ Ошибка',
    finished: true,
  },
  warning: {
    message: '⚠️ Ошибка',
    finished: true,
  },
  canceled: {
    message: '❌ Отменено',
    finished: true,
  },
}

export const GITLAB_CONFIG_TOKEN = Symbol('GITLAB_CONFIG_TOKEN')
