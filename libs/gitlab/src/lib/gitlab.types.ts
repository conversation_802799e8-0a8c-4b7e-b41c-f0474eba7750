// thanks @ccckblaze and @excaliburhan for base for this
// https://github.com/ccckblaze/gitlab-webhook-events/blob/master/index.d.ts

export interface PushEvent {
  after: string
  before: string
  checkout_sha: string
  commits: Commit[]
  object_kind: string
  project_id: number
  project: Project
  ref: string
  repository: Repository
  total_commits_count: number
  user_avatar: string
  user_email: string
  user_id: number
  user_name: string
  user_username: string
}

export interface TagPushEvent {
  after: string
  before: string
  checkout_sha: string
  commits: Commit[]
  object_kind: string
  project_id: number
  project: Project
  ref: string
  repository: Repository
  total_commits_count: number
  user_avatar: string
  user_id: number
  user_name: string
}

export interface IssueEvent {
  assignee: User
  assignees: User[]
  changes: Changes
  labels: Label[]
  object_attributes: IssueAttributes
  object_kind: string
  project: Project
  repository: Repository
  user: User
}

export interface IssueAttributes {
  action: 'open' | 'close' | 'reopen' | 'update'
  assignee_ids: number[]
  assignees: User[]
  author_id: number
  branch_name: string
  confidential: boolean
  created_at: string
  description: string
  discussion_locked: boolean
  due_date: Date | null
  duplicated_to_id: number | null
  id: number
  iid: number
  labels: Label[]
  milestone_id: number
  moved_to_id: number | null
  position: number
  project_id: number
  severity: string
  state: string
  title: string
  updated_at: string
  url: string
}

export interface NoteEvent {
  commit?: Commit
  issue?: Issue
  merge_request?: MergeRequest
  snippet?: Snippet

  object_attributes: NoteAttributes
  object_kind: string
  project_id: number
  project: Project
  repository: Repository
  user: User
}

export interface NoteAttributes {
  attachment: any
  author_id: number
  commit_id: string
  created_at: string
  id: number
  line_code: string
  note: string
  noteable_id: number
  noteable_type: 'Commit' | 'MergeRequest' | 'Issue' | 'Snippet'
  project_id: number
  st_diff: StDiff
  system: boolean
  updated_at: string
  url: string
}

export interface MergeRequestEvent {
  object_kind: string
  event_type: string
  user: User
  project: Project
  repository: Repository
  object_attributes: MergeRequestAttributes
  labels: Label[]
  changes: Changes
  assignees: User[]
  reviewers: User[]
}

export interface MergeRequestAttributes {
  id: number
  iid: number
  target_branch: string
  source_branch: string
  source_project_id: number
  author_id: number
  assignee_ids: number[]
  assignee_id: number
  reviewer_ids: number[]
  title: string
  created_at: string
  updated_at: string
  last_edited_at: string
  last_edited_by_id: number
  milestone_id: null
  state_id: number
  state: string
  blocking_discussions_resolved: boolean
  work_in_progress: boolean
  draft: boolean
  first_contribution: boolean
  merge_status: string
  target_project_id: number
  description: string
  prepared_at: string
  total_time_spent: number
  time_change: number
  human_total_time_spent: string
  human_time_change: string
  human_time_estimate: string
  url: string
  source: Project
  target: Project
  last_commit: LastCommit
  labels: Labels
  action:
    | 'open'
    | 'close'
    | 'reopen'
    | 'update'
    | 'approved'
    | 'unapproved'
    | 'approval'
    | 'unapproval'
    | 'merge'
  detailed_merge_status: string
}

export interface WikiPageEvent {
  object_attributes: WikiPageAttributes
  object_kind: string
  project: Project
  user: User
  wiki: Wiki
}

export interface WikiPageAttributes {
  action: string
  content: string
  format: string
  message: string
  slug: string
  title: string
  url: string
}

export interface PipelineEvent {
  builds: Build[]
  commit: Commit
  object_attributes: PipelineAttributes
  object_kind: string
  project: Project
  user: User
}

export interface PipelineAttributes {
  before_sha: string
  created_at: string
  duration: number
  finished_at: string
  id: number
  ref: string
  sha: string
  stages: string[]
  status: 'pending' | 'running' | 'success' | 'failed'
  tag: boolean
}

export interface BuildEvent {
  before_sha: string
  build_id: number
  build_name: string
  build_stage: string
  build_status: 'created' | 'pending' | 'running' | 'success' | 'failed' | 'warning' | 'canceled'
  build_created_at: string
  build_started_at: string
  build_finished_at: string
  build_duration: number
  build_queued_duration: number
  build_allow_failure: boolean
  build_failure_reason: string
  commit: {
    id: number
    sha: string
    message: string
    author_name: string
    author_email: string
    author_url: string
    status: string
    duration: number
    started_at: string
    finished_at: string
  }
  environment?: {
    name: string
    action: 'start' | 'prepare' | 'stop' | 'verify' | 'access'
  }
  object_kind: string
  pipeline_id: number
  project_id: number
  project_name: string
  ref: string
  repository: Repository
  runner: Runner
  sha: string
  tag: boolean
  user: User
}

export interface Runner {
  id: number
  description: string
  runner_type: string
  active: boolean
  is_shared: boolean
  tags: string[]
}

export interface StDiff {
  a_mode: string
  b_mode: string
  deleted_file: boolean
  diff: string
  new_file: boolean
  new_path: string
  old_path: string
  renamed_file: boolean
}

export interface Issue {
  assignee_id: number
  assignee_ids: number[]
  author_id: number
  branch_name: string
  created_at: string
  description: string
  id: number
  iid: number
  milestone_id: number
  position: number
  project_id: number
  state: string
  title: string
  updated_at: string
}

export interface Snippet {
  author_id: number
  content: string
  created_at: string
  expires_at: string
  file_name: string
  id: number
  project_id: number
  title: string
  type: string
  updated_at: string
  visibility_level: number
}

export interface Commit {
  added?: string[]
  author: Author
  id: string
  message: string
  modified?: string[]
  removed?: string[]
  timestamp: string
  title: string
  url: string
}

export interface Build {
  artifacts_file: ArtifactsFile
  created_at: string
  finished_at?: string
  id: number
  manual: boolean
  name: string
  runner: any
  stage: string
  started_at?: string
  status: string
  user: User
  when: string
}

export interface ArtifactsFile {
  filename: string
  size: number
}

export interface Label {
  color: string
  created_at: string
  description: string
  group_id: number
  id: number
  project_id: number
  template: boolean
  title: string
  type: string
  updated_at: string
}

export interface UpdatedById {
  previous: number | null
  current: number
}

export interface Draft {
  previous: boolean
  current: boolean
}

export interface UpdatedAt {
  previous: string
  current: string
}

export interface Changes {
  updated_by_id: UpdatedById
  draft: Draft
  updated_at: UpdatedAt
  labels: Labels
  last_edited_at: LastEditedAt
  last_edited_by_id: LastEditedById
}

export interface LastEditedAt {
  previous: string | null
  current: string
}

export interface LastEditedById {
  previous: number | null
  current: number
}

export interface Labels {
  current: Label[]
  previous: Label[]
}

export interface MergeRequest {
  assignee_id: number
  assignee: User
  author_id: number
  created_at: string
  description: string
  id: number
  iid: number
  last_commit: LastCommit
  merge_status: string
  milestone_id: number
  position: number
  source_branch: string
  source_project_id: number
  source: Project
  state: string
  target_branch: string
  target_project_id: number
  target: Project
  title: string
  updated_at: string
  work_in_progress: boolean
}

export interface User {
  id?: number
  avatar_url: string
  name: string
  username: string
  email?: string
}

export interface LastCommit {
  id: string
  message: string
  title: string
  timestamp: string
  url: string
  author: Author
}

export interface Author {
  email: string
  name: string
}

export interface Project {
  avatar_url: string
  default_branch: string
  description: string
  git_http_url: string
  git_ssh_url: string
  homepage: string
  http_url: string
  id?: number
  name: string
  namespace: string
  path_with_namespace: string
  ssh_url: string
  url: string
  visibility_level: number
  web_url: string
  ci_config_path?: string
}

export interface Repository {
  description: string
  homepage: string
  name: string
  url: string
}

export interface Wiki {
  default_branch: string
  git_http_url: string
  git_ssh_url: string
  path_with_namespace: string
  web_url: string
}

export interface EventData<T> {
  event: string
  host: string
  path: string
  payload: T
  protocol: string
  url: string
}

export interface Option {
  path: string
  secret: string
}

export interface SystemEvent {
  created_at: string
  updated_at: string
  event_name: string
  [key: string]: any
}

export interface DeploymentEvent {
  commit_title: string
  commit_url: string
  deployable_id: number
  deployable_url: string
  deployment_id: number
  environment_external_url: string
  environment_slug: string
  environment_tier: string
  environment: string
  project: Project
  ref: string
  short_sha: string
  status_changed_at: string
  status: 'pending' | 'running' | 'success' | 'failed'
  user_url: string
  user: User
}
