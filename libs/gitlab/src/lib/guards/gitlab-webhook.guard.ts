import { CanActivate, ExecutionContext, Inject, Injectable } from '@nestjs/common'
import { Request } from 'express'
import { GitlabInstance } from '@majestic-backend/config'
import { GITLAB_CONFIG_TOKEN } from '../gitlab-feature.constants'

@Injectable()
export class GitLabWebhookGuard implements CanActivate {
  constructor(@Inject(GITLAB_CONFIG_TOKEN) private readonly config: GitlabInstance) {}

  public async canActivate(ctx: ExecutionContext): Promise<boolean> {
    const req = ctx.switchToHttp().getRequest<Request>()

    const webhookSecret = this.config.WEBHOOK_SECRET
    if (webhookSecret && req.header('X-GitLab-Token') !== webhookSecret) {
      return false
    }

    return true
  }
}
