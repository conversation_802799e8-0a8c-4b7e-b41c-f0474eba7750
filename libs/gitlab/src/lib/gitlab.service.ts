import { Gitlab as GitlabClient } from '@gitbeaker/core'
import { Gitlab } from '@gitbeaker/rest'
import { Inject, Injectable } from '@nestjs/common'
import { EventBus } from '@nestjs/cqrs'

import { GitlabDeploymentEvent } from './events/gitlab-deployment.event'
import { GitlabJobEvent } from './events/gitlab-job.event'
import { BuildEvent, DeploymentEvent, MergeRequestEvent } from './gitlab.types'
import { GitlabInstance } from '@majestic-backend/config'
import { GITLAB_CONFIG_TOKEN } from './gitlab-feature.constants'
import { GitlabMergeRequestEvent } from './events/gitlab-merge-request.event'

@Injectable()
export class GitLabService {
  private readonly gitlab: GitlabClient

  constructor(
    @Inject(GITLAB_CONFIG_TOKEN) config: GitlabInstance,
    private readonly eventBus: EventBus,
  ) {
    this.gitlab = new Gitlab({
      host: config.HOST,
      token: config.TOKEN,
    })
  }

  public async getProjectOpenedMRs(projectId: string | number) {
    const mrs = await this.gitlab.MergeRequests.all({
      projectId,
      state: 'opened',
    })
    return mrs
  }

  public async getProject(projectId: string | number) {
    return this.gitlab.Projects.show(projectId)
  }

  public async getProjectMr(projectId: string | number, mrIid: number) {
    return this.gitlab.MergeRequests.show(projectId, mrIid)
  }

  public async getProjectMrApprovalConfig(projectId: string | number, mrIid: number) {
    return this.gitlab.MergeRequestApprovals.showConfiguration(projectId, {
      mergerequestIId: mrIid,
    })
  }

  public async getPipelineJobs(projectId: string | number, pipelineId: number) {
    return this.gitlab.Jobs.all(projectId, {
      pipelineId,
    })
  }

  public async getPipelineJob(projectId: string | number, jobId: number) {
    return this.gitlab.Jobs.show(projectId, jobId)
  }

  public async runJob(projectId: string | number, jobId: number) {
    return this.gitlab.Jobs.play(projectId, jobId)
  }

  public async retryJob(projectId: string | number, jobId: number) {
    return this.gitlab.Jobs.retry(projectId, jobId)
  }

  public async handleJobWebhook(dto: BuildEvent) {
    this.eventBus.publish(new GitlabJobEvent(dto))
  }

  public async handleDeploymentWebhook(dto: DeploymentEvent) {
    this.eventBus.publish(new GitlabDeploymentEvent(dto))
  }

  public async handleMergeRequestWebhook(dto: MergeRequestEvent) {
    this.eventBus.publish(new GitlabMergeRequestEvent(dto))
  }

  public async getLatestPipeline(projectId: string | number, branch = 'master') {
    return this.gitlab.Pipelines.show(projectId, 'latest', {
      ref: branch,
    })
  }
}
