{"name": "libs/gitlab", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/gitlab/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/gitlab/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/gitlab/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "tags": []}