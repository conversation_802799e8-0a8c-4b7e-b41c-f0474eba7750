export enum Methods {
  BITCOIN = 'bitcoin',
  BOLETO = 'boleto',
  CARD_BRAZIL = 'card_brazil',
  CARD_RU3 = 'card_ru3',
  CARD_WORLD1 = 'card_world1',
  CARDS_EU = 'cards_eu',
  CRYPTO = 'crypto',
  DIRECT_BANKING = 'direct_banking',
  ERC20 = 'erc20',
  ETHEREUM = 'ethereum',
  PIX = 'pix',
  QIWI = 'qiwi',
  SBP = 'sbp',
  STEAM = 'steam',
  TRC20 = 'trc20',
  YOOMONEY = 'yoomoney',
  ERIP = 'erip',
}

export enum OtherMethods {
  CARD = 'card',
  CARD_RU1 = 'card_ru1',
  CARD_RU2 = 'card_ru2',
  CARD_RU4 = 'card_ru4',
  CARD_UA = 'card_ua',
  CARD_WORLD2 = 'card_world2',
  CARD_WORLD3 = 'card_world3',
  CARDS_PGS = 'cards_pgs',
  CHECK = 'check',
  PAY = 'pay',
  DOLYAME = 'dolyame',
  YANDEXPAY = 'yandexpay',
}
