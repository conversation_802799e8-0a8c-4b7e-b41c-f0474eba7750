import {
  CLIENT_MESSAGES_TYPES,
  MINI_GAMES_MESSAGES_TYPES,
  PIXEL_BATTLE_MESSAGES_TYPES,
  REPORT_MESSAGES_TYPES,
  SERVER_MESSAGES_TYPES,
} from './socketMessages'

export interface REPORT_PAYLOAD {
  [REPORT_MESSAGES_TYPES.ADD_NEW_REPORT]: {
    id: number
    characterId?: number
    text: string
    type: string
    status: string
    date?: string | Date
    mark?: number | boolean
    familyLogoType?: string
    exceptionId?: number
    selected?: number
    lastMessage: string
    lastMessageType: string
    author?: string
  }
  [REPORT_MESSAGES_TYPES.UPDATE_REPORT_ADMIN]: {
    reportId: number
    newAdmin?: number
  }
  [REPORT_MESSAGES_TYPES.REMOVE_REPORT_FROM_LIST]: {
    reportId: number
    characterId?: number
  }
  [REPORT_MESSAGES_TYPES.ADD_NEW_MESSAGE]: {
    id: number
    reportId: number
    characterId?: number
    message: string
    socialClub?: string
    nickname?: string
    date?: string | Date
    type: string
    author?: string
    assets: string[] | string
    noPush: boolean
  }
  [REPORT_MESSAGES_TYPES.SHOW_NOTIFY]: {
    reportId?: number
    type: string
  }
  [REPORT_MESSAGES_TYPES.UPDATE_REPORT_STATUS]: {
    reportId: number
    status: string
  }
  [REPORT_MESSAGES_TYPES.UPDATE_MODERATION_STATUS]: {
    reportId: number
    type: string
    status: boolean
  }
  [REPORT_MESSAGES_TYPES.ADD_NEW_MODERATION_REPORT]: {
    id?: number
    reportId?: number
    type: string
    date: string | Date
    author?: string
    title?: string
    text?: string
    orgId?: number
    companyName?: string
    img?: string
    factionId?: number
  }
}

export interface SERVER_PAYLOAD {
  [SERVER_MESSAGES_TYPES.PLAYER_JOIN]: {
    accountId: number
    remoteId: number
  }
  [SERVER_MESSAGES_TYPES.PLAYER_QUIT]: {
    accountId: number
  }
  [SERVER_MESSAGES_TYPES.MINI_GAMES_UPDATE_USER_DONATE]: {
    serverId: string
    accountId: number
    userId: number
    betAmount: number
    donateType: string
  }
}

export interface PIXEL_BATTLE_PAYLOAD {
  [PIXEL_BATTLE_MESSAGES_TYPES.UPDATE_IMAGE]: {
    x: number
    y: number
    colorIdx: number
    serverId: string
    staticId: number
    count: number
    name: string
  }
  [PIXEL_BATTLE_MESSAGES_TYPES.CLEAR_ALL]: {
    clear: boolean
  }
}

export interface CLIENT_PAYLOAD {
  [CLIENT_MESSAGES_TYPES.MAIN_SHOW_NOTIFY]: {
    text: string
    messageType: string
    duration: number
  }
  [CLIENT_MESSAGES_TYPES.DONATE_UPDATE_COINS]: {
    donate: number
  }
  [CLIENT_MESSAGES_TYPES.NOTIFICATION_ERROR]: {
    volume: number
  }
}

export interface MINI_GAMES_PAYLOAD {
  [MINI_GAMES_MESSAGES_TYPES.CRASH_START_TIMER]: {
    state: object
    currentGame: object
  }
  [MINI_GAMES_MESSAGES_TYPES.CRASH_START_GAME]: {
    state: object
  }
  [MINI_GAMES_MESSAGES_TYPES.CRASH_CRASH]: {
    crashX: number
  }
  [MINI_GAMES_MESSAGES_TYPES.CRASH_BET]: {
    login: string
    gender: boolean
    accountId: number
    serverId: string
    serverName: string
    betAmount: number
  }
  [MINI_GAMES_MESSAGES_TYPES.CRASH_TAKE]: {
    accountId: number
    serverId: string
    serverName: string
    exit: number
    exitTime: string
  }
  [MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_GAME]: {
    state: object
    currentGame?: object
    crashLastGame?: object
  }
  [MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_BET]: object
  [MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_HISTORY_LIST]: {
    data: object
  }
  [MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_HISTORY_ITEM]: {
    data: object
  }
  [MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_AUTO_TAKE]: {
    autoTakeX: number
  }
  [MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_LUCKERS_RESULT]: {
    data: object
  }
  [MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_GAME]: {
    state: object
    currentGame?: object
    luck?: object
  }
  [MINI_GAMES_MESSAGES_TYPES.JACKPOT_START_TIMER]: {
    state: object
    currentGame?: object
    luck?: object
  }
  [MINI_GAMES_MESSAGES_TYPES.JACKPOT_START_GAME]: {
    state: string
    currentGame: string
    winner: string
  }
  [MINI_GAMES_MESSAGES_TYPES.JACKPOT_END_GAME]: object
  [MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_HISTORY_LIST]: {
    data: object
  }
  [MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_HISTORY_ITEM]: {
    data: object
  }
  [MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_BET]: {
    login: string
    gender: boolean
    accountId: number
    serverId: string
    serverName: string
    amount: number
  }
  [MINI_GAMES_MESSAGES_TYPES.JACKPOT_BET]: {
    player: object
  }
  [MINI_GAMES_MESSAGES_TYPES.WHEEL_START_TIMER]: {
    state: object
    currentGame?: string
  }
  [MINI_GAMES_MESSAGES_TYPES.WHEEL_START_WHEEL]: {
    state: object
  }
  [MINI_GAMES_MESSAGES_TYPES.WHEEL_BET]: {
    currentBet: object
    player: object
  }
  [MINI_GAMES_MESSAGES_TYPES.WHEEL_UPDATE_GAME]: {
    state: object
    currentGame: object
    history: object
  }
  [MINI_GAMES_MESSAGES_TYPES.WHEEL_UPDATE_BET]: {
    accountId: number
    color: string
    amount: number
    serverId: string
    serverName: string
  }
  [MINI_GAMES_MESSAGES_TYPES.WHEEL_UPDATE_HISTORY_LIST]: {
    data: object
  }
  [MINI_GAMES_MESSAGES_TYPES.WHEEL_UPDATE_HISTORY_ITEM]: {
    data: object
  }
  [MINI_GAMES_MESSAGES_TYPES.WHEEL_SET_WINNER]: {
    totalRotate: number
    winner: string
  }
}

export type SOCKET_PAYLOAD = REPORT_PAYLOAD &
  SERVER_PAYLOAD &
  PIXEL_BATTLE_PAYLOAD &
  CLIENT_PAYLOAD &
  MINI_GAMES_PAYLOAD
