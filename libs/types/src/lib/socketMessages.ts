export enum REPORT_MESSAGES_TYPES {
  ADD_NEW_REPORT = 'reports.addNewReport',
  UPDATE_REPORT_ADMIN = 'reports.chat.updateReportAdmin',
  REMOVE_REPORT_FROM_LIST = 'reports.chat.removeReportFromList',
  ADD_NEW_MESSAGE = 'reports.chat.addNewMessage',
  SHOW_NOTIFY = 'reports.chat.showNotify',
  UPDATE_REPORT_STATUS = 'reports.chat.updateReportStatus',
  UPDATE_MODERATION_STATUS = 'reports.updateModerationStatus',
  ADD_NEW_MODERATION_REPORT = 'reports.addNewModerationReport',
}

export enum MINI_GAMES_MESSAGES_TYPES {
  CRASH_START_TIMER = 'minigamesF2.crash.startTimer',
  CRASH_START_GAME = 'minigamesF2.crash.startGame',
  CRASH_CRASH = 'minigamesF2.crash.crash',
  CRASH_BET = 'minigamesF2.crash.bet',
  CRASH_TAKE = 'minigamesF2.crash.take',
  CRASH_UPDATE_GAME = 'minigamesF2.crash.updateGame',
  CRASH_UPDATE_BET = 'minigamesF2.crash.updateBet',
  CRASH_UPDATE_HISTORY_LIST = 'minigamesF2.crash.updateHistoryList',
  CRASH_UPDATE_HISTORY_ITEM = 'minigamesF2.crash.updateHistoryItem',
  CRASH_UPDATE_AUTO_TAKE = 'minigamesF2.crash.updateAutoTake',
  JACKPOT_UPDATE_LUCKERS_RESULT = 'minigamesF2.jackpot.updateLuckers',
  JACKPOT_UPDATE_GAME = 'minigamesF2.jackpot.updateGame',
  JACKPOT_START_TIMER = 'minigamesF2.jackpot.startTimer',
  JACKPOT_START_GAME = 'minigamesF2.jackpot.startGame',
  JACKPOT_END_GAME = 'minigamesF2.jackpot.endGame',
  JACKPOT_UPDATE_HISTORY_LIST = 'minigamesF2.jackpot.updateHistoryList',
  JACKPOT_UPDATE_HISTORY_ITEM = 'minigamesF2.jackpot.updateHistoryItem',
  JACKPOT_UPDATE_BET = 'minigamesF2.jackpot.updateBet',
  JACKPOT_BET = 'minigamesF2.jackpot.bet',
  WHEEL_START_TIMER = 'minigamesF2.wheel.startTimer',
  WHEEL_START_WHEEL = 'minigamesF2.wheel.startWheel',
  WHEEL_BET = 'minigamesF2.wheel.bet',
  WHEEL_UPDATE_GAME = 'minigamesF2.wheel.updateGame',
  WHEEL_UPDATE_BET = 'minigamesF2.wheel.updateBet',
  WHEEL_UPDATE_HISTORY_LIST = 'minigamesF2.wheel.updateHistoryList',
  WHEEL_UPDATE_HISTORY_ITEM = 'minigamesF2.wheel.updateHistoryItem',
  WHEEL_SET_WINNER = 'minigamesF2.wheel.setWinner',
}

export enum CLIENT_MESSAGES_TYPES {
  MAIN_SHOW_NOTIFY = 'main.showNotify',
  DONATE_UPDATE_COINS = 'panelMenu.donate.updateCoins',
  NOTIFICATION_ERROR = 'notification.error',
}

export enum SERVER_MESSAGES_TYPES {
  PLAYER_JOIN = 'server.playerJoin',
  PLAYER_QUIT = 'server.playerQuit',
  MINI_GAMES_UPDATE_USER_DONATE = 'minigames.updateUserDonate',
}

export enum PIXEL_BATTLE_MESSAGES_TYPES {
  UPDATE_IMAGE = 'pixelBattle.imageUpdate',
  CLEAR_ALL = 'pixelBattle.clearAll',
}

export type SOCKET_MESSAGE =
  | REPORT_MESSAGES_TYPES
  | SERVER_MESSAGES_TYPES
  | PIXEL_BATTLE_MESSAGES_TYPES
  | CLIENT_MESSAGES_TYPES
  | MINI_GAMES_MESSAGES_TYPES
