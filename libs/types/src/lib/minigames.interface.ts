import { mini_games_name } from '@majestic-backend/prisma-client-backend'
import { WheelColors } from './mini-game-wheel.enum'

export interface ICreateMiniGamesLog {
  userId: number
  accountId: number
  game: MiniGamesNames
  type: MiniGamesDonateType
  amount: number
  gameId: number
  args: Record<string, any>
  serverId: string
}

export interface IMiniGamePlayer {
  minigameName: mini_games_name
  userId: number
  accountId: number
  serverId: string
  login: string
  socialClub: string
  donate: number
  betAmount: number
  gameId: number | null
  inCurrentGame: boolean
  args: IPlayerArgs
  isActive: boolean
  gotReward: boolean
  gender: boolean
  isBetInProgress: boolean
}

export interface IPlayerArgs {
  exit?: number | null
  crashAutoTakeX?: number | null
  exitTime?: Date | null
  ticket?: string | null
  dateBet?: Date | null
  wheelBets?: {
    color?: WheelColors | null
    betAmount?: number | null
  }[]
}

export enum MiniGamesNames {
  CRASH = 'crash',
  JACKPOT = 'jackpot',
  WHEEL = 'wheel',
}

export enum MiniGamesDonateType {
  BET = 'bet',
  WIN = 'win',
}
