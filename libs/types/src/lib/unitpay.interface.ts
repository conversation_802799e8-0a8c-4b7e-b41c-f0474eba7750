import { UnitpayPayment } from '@majestic-backend/prisma-client-game'

export type IUnitpayCreatePaymentPayload = Omit<UnitpayPayment, 'id'>

export interface IUnitpayAcceptPaymentPayload {
  unitpayId: string
  serverId: string
  id: number
  profit?: number
  payAmount?: number
  sum?: number
  additional?: number
  method?: string
}

export interface IUnitpayUpdatePaymentPayload {
  status: number
  profit: string | null
  dateComplete: Date
  payAmount?: number
  sum?: number
  additional?: number
  method?: string
}

export interface IUnitpayFindPaymentByPayload {
  where: {
    unitpayId: string
    status?: number
  }
}
