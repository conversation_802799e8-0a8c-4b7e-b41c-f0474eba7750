export interface ITinkoffResponse {
  /** required, string <= 20 characters */
  TerminalKey: string
  /** required, number <= 20 characters */
  Amount: number
  /** required, string <= 36 characters */
  OrderId: string
  /** required, boolean */
  Success: boolean
  /** required, string <= 20 characters */
  Status: string
  /** required, string <= 20 characters */
  PaymentId: string
  /** required, string <= 20 characters */
  ErrorCode: string
  /** string <uri> <= 100 characters */
  PaymentURL?: string
  /** string <= 255 characters */
  Message?: string
  /** string */
  Details?: string
}

export interface ITinkoffQrResponse {
  TerminalKey: string
  Data: string
  RequestKey: string
  ErrorCode: string
  Success: boolean
  Message?: string
}

export interface ITinkoffPaymentParams {
  Password?: string
  TerminalKey?: string
  Amount?: number
  OrderId?: string
  Description?: string
  Recurrent?: string
  CustomerKey?: string
  Source?: string
  DATA?: {
    serverId: string
    login: string
    subscriptionDays?: number
    isAutoPayment?: boolean
    QR?: boolean
  }
  Receipt?: {
    Items: [
      {
        Name: string
        Price: number
        Quantity: number
        Amount: number
        PaymentMethod: string
        PaymentObject: string
        Tax: string
        MeasurementUnit: string
      },
    ]
    FfdVersion: number
    Email: string
    Taxation: string
  }
  Token?: string
  Success?: boolean
  Status?: string
  Data?: Record<string, any>
}

export interface ITinkoffChargeParams {
  TerminalKey: string
  PaymentId: string
  RebillId: string
  Token: string
}

export enum TinkoffQrDataTypes {
  PAYLOAD = 'PAYLOAD',
  IMAGE = 'IMAGE',
}

export interface ITinkoffAddAccountQrParams {
  TerminalKey: string
  Description: string
  DataType?: TinkoffQrDataTypes
  Data?: Record<string, any>
  RedirectDueDate?: Date
  Token?: string
}

export interface ITinkoffChargeQrParams {
  TerminalKey: string
  PaymentId: string
  AccountToken: string
  Token: string
}
