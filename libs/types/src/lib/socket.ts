import { SOCKET_MESSAGE } from './socketMessages'
import { SOCKET_PAYLOAD } from './socketPayloads'

export interface IPublishSocketPayload<T extends SOCKET_MESSAGE>
  extends Omit<IBroadcastSocketPayload<T>, 'propagationMode'> {
  propagationMode: 'toProvided'
  receivers: number | number[]
  serverId: string
}

export interface IBroadcastSocketPayload<T extends SOCKET_MESSAGE> {
  propagationMode: 'broadcast'
  payload: SOCKET_PAYLOAD[T]
  type: T
}
