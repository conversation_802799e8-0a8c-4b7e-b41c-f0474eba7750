export interface CreatePaypalRequestData {
  intent: string;
  redirect_urls: {
    return_url: string;
    cancel_url: string;
  };
  payer: {
    payment_method: string;
  };
  transactions: Array<{
    custom: string;
    amount: {
      total: string;
      currency: string;
    };
    description: string;
  }>;
}

export interface CreatePaypalPaymentData {
  links?: { rel: string; href: string; }[];
  id?: string;
}

export interface ExecutePaypalPaymentData {
  transactions?: Array<Record<string, any>>;
  id?: string;
}