import { Currencies } from './currencies.enum'

export interface IMultihubPayload {
  service_id: number
  method: MultihubMethod
  params: {
    payment: {
      description: string
      identifiers: {
        c_id: number
      }
      amount: {
        value: number
        currency: Currencies
      }
      payer: {
        customer_account: {
          id: string
        }
        email: string
        person: {
          first_name: string
          documents: [
            {
              // idk what is it. TODO: Enum or string literal?
              type: 'BR_CPF'
              number: string
            },
          ]
        }
      }
    }
  }
}

export interface IMultihubResponse {
  result: {
    payment?: {
      identifiers?: {
        h_id: number | string
      }
      status?: {
        status?: MultihubPaymentStatus
      }
      redirect?: {
        to?: string
      }
    }
  }
}

export enum MultihubMethod {
  PaymentIn = 'payment.in',
  PaymentUpdate = 'payment.update',
}

export enum MultihubPaymentStatus {
  AwaitRedirect = 'await_redirect',
  Success = 'success',
}
