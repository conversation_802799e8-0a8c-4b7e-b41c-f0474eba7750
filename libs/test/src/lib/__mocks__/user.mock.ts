import { users_registerFrom } from '@majestic-backend/prisma-client-game'

export const mockUser = {
  id: 123,
  login: 'test',
  email: null,
  acceptEmail: true,
  password: null,
  isConfirm: false,
  media: false,
  createdAt: null,
  donate: null,
  halloweenCrystals: 0,
  createdSocialClub: null,
  createdRGSCID: null,
  lastSocialClub: null,
  lastRGSCID: null,
  createdIP: null,
  lastIP: null,
  createdHWID: null,
  lastHWID: null,
  lastVisit: null,
  invitedBy: null,
  inviteCode: null,
  lastAccount: null,
  maxAccounts: 2,
  authToken: null,
  online: false,
  coinsTimer: 0,
  rulesAccepted: false,
  twoFactorSecret: null,
  telegram: null,
  telegramSecret: null,
  registerFrom: users_registerFrom.game,
  registerCode: null,
  registrationPageName: null,
  registrationUtmSource: null,
  registrationUtmMedium: null,
  registrationUtmCampaign: null,
  launcherAuth: false,
  cooldowns: null,
  pixelBattleTime: null,
  pixelBattleCount: 0,
  subscriptionStart: null,
  subscriptionEnd: null,
  subscriptionPaymentData: null,
  subscriptionDays: null,
  rebillId: null,
  accountTokenQr: null,
  subscriptionActive: null,
  subscriptionRenewalsNumber: null,
  exitData: null,
}

export const mockGetAccount = {
  serverModel: new Map<number, any>([[1, { name: 'TEST0', status: 'active' }]]),
  login: 'test',
  source: 'site',
  args: {
    staticId: '123',
    season: 'Summer season',
  },
}

export const mockAccountsEmptyList = []
