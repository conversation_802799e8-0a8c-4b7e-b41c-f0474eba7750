import { Region } from '@majestic-backend/config'
import { Currencies } from '@majestic-backend/types'

export const mockRegion = 'RU'

export const mockRegionConfig: Region = {
  BASE_URL: 'http://localhost/',
  CURRENCY: Currencies.RUB,
  PAYPAL_CLIENT_ID: 'testPaypalClientId',
  PAYPAL_SECRET: 'testPaypalSecret',
  PAYPAL_MIN_AMOUNT: 10,
  PAYPAL_MODE: 'sandbox',
  COINS_AMOUNT: 100,
  DONATE_MULTIPLIER: 1,
  PGS_STORE_ID: '10',
  PGS_API_KEY: 'apiKey',
  PGS_MIN_AMOUNT: 50,
}
