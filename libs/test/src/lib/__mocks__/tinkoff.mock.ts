import {
  PaymentsCancelRecurrentTinkoff,
  PaymentsChargeTinkoff,
  PaymentsCheckTinkoff,
  PaymentsCreateTinkoff,
  PaymentsRecurrentTinkoff,
} from '@majestic-backend/contracts'
import { unitpay_payments_source } from '@majestic-backend/prisma-client-game'
import { Methods, SubscriptionIds } from '@majestic-backend/types'

export const mockTinkoffConfig = {
  TERMINAL_KEY: 'tinkTerminalKey',
  PASSWORD: 'tinkPassword',
  SBP_PERCENT: 1,
  SBP_MAXIMUM: 2,
  DOLYAME_PERCENT: 3,
  DOLYAME_MAXIMUM: 4,
  CARDS_PERCENT: 5,
  CARDS_MINIMUM: 6,
  MIN_AMOUNT: 50,
}

export const mockCreateTinkoffBody: PaymentsCreateTinkoff.Request = {
  serverId: 'TEST0',
  login: 'test',
  amount: 1500,
  source: unitpay_payments_source.site,
  email: '<EMAIL>',
  method: Methods.PIX,
  args: {
    subId: 'start2',
  },
  firstName: 'pixName',
  document: 'pixDocument',
}

export const mockCreateTinkoffArgs = {
  args: {
    subId: 'start2',
  },
}

export const mockCheckTinkoffBody: PaymentsCheckTinkoff.Request = {
  params: {
    // Success: 'Success',
    Success: true,
    Status: 'CONFIRMED',
    TerminalKey: 'tinkTerminalKey',
    Token: '897d667ab51266548332bf67ce39cbda71745a1637804ee57c2212b3e379a2e7',
    OrderId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
    Amount: 100,
    Pan: '+7 (952) ***-**-67',
    Data: {
      Source: 'Bnpl',
      serverId: 'TEST0',
    },
  },
}

export const mockUnitpayPaymentTinkoff = {
  id: 123,
  unitpayId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
  account: 'test',
  payAmount: 1500,
  sum: 150000,
  additional: 15,
  profit: null,
  dateCreate: new Date(),
  dateComplete: null,
  status: 0,
  multiplier: 1,
  comment: 'Tinkoff',
  args: '{"firstName":"pixName","document":"pixDocument","staticId":"123","season":"Summer season"}',
  source: unitpay_payments_source.site,
  method: 'pix',
  shop: null,
  currency: 'RUB',
}

export const { id: mockUnitpayPaymentTinkoffId, ...mockUnitpayPaymentTinkoffWithoutId } =
  mockUnitpayPaymentTinkoff

export const mockAxiosReturn = {
  data: {
    Success: true,
    PaymentURL: 'http://tinkoff-approval',
    PaymentId: '12345',
  },
}

export const mockTinkoffFindPaymentByPayload = {
  where: {
    unitpayId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
    // status: 0,
  },
}

export const mockTinkoffAcceptUnitpayPayload = {
  serverId: 'TEST0',
  id: 123,
  unitpayId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
  profit: 0.97,
  payAmount: 1,
  sum: 1,
  additional: 0,
  method: 'Bnpl',
}

export const mockCreateRecurrentTinkoff: PaymentsRecurrentTinkoff.Request = {
  serverId: 'TEST0',
  login: 'test',
  source: unitpay_payments_source.premiumSubscription,
  method: Methods.CARD_RU3,
  args: {
    subId: SubscriptionIds.START,
  },
}

export const mockCancelRecurrentTinkoff: PaymentsCancelRecurrentTinkoff.Request = {
  serverId: 'TEST0',
  login: 'test',
}

export const mockUpdateUser = {
  data: {
    rebillId: null,
    accountTokenQr: null,
  },
  where: { userId: 123 },
}

export const mockChargeTinkoff: PaymentsChargeTinkoff.Request = {
  serverId: 'TEST0',
  login: 'test',
  amount: 190,
}

export const mockPremiumSubscriptionConfig = {
  start: {
    days: 7,
    cost: {
      ru: 190,
      eu: 5,
    },
  },
}

export const mockSubscriptionPayment = {
  id: 123,
  unitpayId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
  account: 'test',
  payAmount: 190,
  sum: 190,
  additional: 0,
  profit: null,
  dateCreate: new Date(),
  dateComplete: null,
  status: 0,
  multiplier: 1,
  comment: 'Tinkoff',
  args: null,
  source: unitpay_payments_source.premiumSubscription,
  method: 'card_ru3',
  shop: null,
  currency: 'RUB',
}

export const mockSubscriptionData = {
  userId: 123,
  login: 'test',
  start: null,
  end: null,
  days: null,
  renewalsNumber: null,
  hasActive: null,
  paymentData: null,
  rebillId: '12345',
  accountTokenQr: null,
  active: null,
  paymentType: null,
}

export const { id: mockSubscriptionPaymentId, ...mockSubscriptionPaymentWithoutId } =
  mockSubscriptionPayment

export const mockSendEndNotification = 'TEST0'
