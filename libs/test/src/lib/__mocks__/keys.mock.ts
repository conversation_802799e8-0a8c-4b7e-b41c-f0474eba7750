import { PaymentsBuyKeys } from '@majestic-backend/contracts'
import { Currencies, Methods } from '@majestic-backend/types'

export const mockBuyKeysBody: PaymentsBuyKeys.Request = {
  email: '<EMAIL>',
  method: Methods.CRYPTO,
  mediaReferer: null,
  referer: null,
  ip: '***************',
  confirmationCode: '022882',
}

export const mockCheckKeysBody = {
  status: 'succeeded',
  paymentId: '3d6816dd-e0fc-413b-8eff-8cf15c31b12b',
  sysPaymentId: 12345,
  storeId: 1237728132,
  sign: 'cd4f7c9ec766f8bd9fe8941932f82165db7c70ca07a47098d6676680d2b2e628',
  userAmountMultiplier: 2,
  amount: 100.0,
  incomeAmount: 10.22,
  description: 'Ключ активации для игры Grand Theft Auto 5 на https://majestic-rp.ru',
  additionalInformation: 'gta key',
  currency: Currencies.RUB,
}

export const mockKeyData = {
  id: 10,
  serial: 'serialKeyValue',
  cost: '100',
  createdDate: null,
  frozenUntil: null,
  frozenUuid: '',
  buyDate: null,
}

export const mockKeyPayment = {
  id: '07d45440-77e3-4bfe-b80e-be6006eaadc6',
  keyId: 10,
  email: '<EMAIL>',
  ip: '***************',
  dateCreate: null,
  dateComplete: null,
  method: 'erc20',
  provider: 'pgs',
  profit: null,
  mediaReferer: null,
  referer: null,
}

export const mockPaymentData = {
  profit: '123',
  orderId: '12345',
  amount: 100,
}

export const mockOrder = {
  id: '07d45440-77e3-4bfe-b80e-be6006eaadc6',
  keyId: 10,
  email: '<EMAIL>',
  ip: '***************',
  dateCreate: new Date('2024-08-19T11:23:15.000Z'),
  dateComplete: null,
  method: 'erc20',
  provider: 'pgs',
  profit: null,
  mediaReferer: 'site',
  referer: 'refff',
}

export const mockVerifiedEmail = {
  id: 7,
  email: '<EMAIL>',
  code: '022882',
  isVerified: false,
  createdAt: null,
  completedAt: null,
}
