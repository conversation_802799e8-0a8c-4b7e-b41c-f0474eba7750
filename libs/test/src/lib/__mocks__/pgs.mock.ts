import { PaymentsCheckPgs, PaymentsCreatePgs } from '@majestic-backend/contracts'
import { unitpay_payments_source } from '@majestic-backend/prisma-client-game'
import { Currencies, Methods } from '@majestic-backend/types'

export const mockCreatePgsBody: PaymentsCreatePgs.Request = {
  serverId: 'TEST0',
  amount: 1500,
  source: unitpay_payments_source.site,
  login: 'test',
  method: Methods.PIX,
  args: {
    subId: 'start2',
  },
}

export const mockCreatePgsArgs = {
  args: {
    subId: 'start2',
  },
}

export const mockCheckPgsBody: PaymentsCheckPgs.Request = {
  shop: 'ru',
  params: {
    status: 'succeeded',
    amount: 1500,
    incomeAmount: 100,
    sign: 'ca82f8a1c4be4f10bf5fa8d0da5b1bd6404355dd4488d3b740c97ee6abc43874',
    additionalInformation: 'test.___TEST0',
    paymentId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
    sysPaymentId: 123,
    storeId: 123,
    description: 'test - Бронзовый сезонный пропуск Summer season. Server: TEST0.',
    currency: Currencies.RUB,
  },
}
