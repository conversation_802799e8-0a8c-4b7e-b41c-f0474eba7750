import { unitpay_payments_source } from '@majestic-backend/prisma-client-game'
import { mockUser } from './user.mock'
import { mockRegionConfig } from './region.mock'

export const mockCreatePaypalBody = {
  serverId: 'TEST',
  login: 'test',
  amount: 12345,
  source: unitpay_payments_source.site,
  args: {
    subId: 'start2',
  },
}

export const mockCreatePaypalArgs = {
  args: {
    subId: 'start2',
  },
}

export const mockCheckPaypalBody = {
  paymentId: 'PAYID-12345',
  payerId: 'AA5AA5AA2LVLV',
  serverId: 'TEST',
}

export const mockPaypalConfig = {
  mode: 'sandbox',
  client_id: 'testPaypalClientId',
  client_secret: 'testPaypalSecret',
}

export const mockPaypalRequestData = {
  intent: 'sale',
  redirect_urls: {
    return_url: `${mockRegionConfig.BASE_URL}?paypal-check&serverId=TEST`,
    cancel_url: `${mockRegionConfig.BASE_URL}?paypal-cancel`,
  },
  payer: {
    payment_method: 'paypal',
  },
  transactions: [
    {
      custom: JSON.stringify({
        login: mockUser.login,
        serverId: 'TEST',
      }),
      amount: {
        total: `12345`,
        currency: mockRegionConfig.CURRENCY,
      },
      description: 'test - 1236969 MC. Server: TEST',
    },
  ],
}

export const mockCreatePaypalPaymentData = {
  links: [
    {
      rel: 'approval_url',
      href: 'https://api.sandbox.paypal.com/approval',
    },
    {
      rel: 'self',
      href: 'https://api.sandbox.paypal.com/self',
    },
  ],
  id: 'PAYID-12345',
}

export const mockExecutePaypalPaymentData = {
  id: 'PAYID-M2ORZHA01L917982H8339932',
  transactions: [
    {
      custom: '{"login":"test","serverId":"TEST"}',
    },
  ],
}
