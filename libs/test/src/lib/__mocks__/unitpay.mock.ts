import { unitpay_payments_source } from '@majestic-backend/prisma-client-game'
import { mockCheckPaypalBody } from './paypal.mock'

export const mockUnitpayPaymentPayload = {
  id: 123,
  unitpayId: 'PAYID-12345',
  account: 'test',
  payAmount: 12345,
  sum: 1234500,
  additional: 2469,
  profit: null,
  dateCreate: new Date(),
  dateComplete: null,
  status: 0,
  multiplier: 1,
  comment: 'PayPal',
  args: null,
  source: unitpay_payments_source.site,
  method: 'PayPal',
  shop: null,
  currency: 'RUB',
}

export const { id: mockUnitpayPaymentPayloadId, ...mockUnitpayPaymentPayloadWithoutId } =
  mockUnitpayPaymentPayload

export const mockUnitpayPaymentWithArgs = {
  id: 123,
  unitpayId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
  account: 'test',
  payAmount: 1500,
  sum: 150000,
  additional: 15,
  profit: null,
  dateCreate: new Date(),
  dateComplete: null,
  status: 0,
  multiplier: 1,
  comment: 'PGS',
  args: '{"firstName":"pixName","document":"pixDocument","staticId":"123","season":"Summer season"}',
  source: unitpay_payments_source.site,
  method: 'pix',
  shop: null,
  currency: 'RUB',
}

export const { id: mockUnitpayPaymentWithArgsId, ...mockUnitpayPaymentWithArgsWithoutId } =
  mockUnitpayPaymentWithArgs

export const mockUnitpayFindPaymentByPayload = {
  where: {
    unitpayId: 'PAYID-M2ORZHA01L917982H8339932',
    // status: 0,
  },
}

export const mockPgsUnitpayFindPaymentByPayload = {
  where: {
    unitpayId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
    // status: 0,
  },
}

export const mockAcceptUnitpayPayload = {
  serverId: mockCheckPaypalBody.serverId,
  id: 123,
  unitpayId: 'PAYID-M2ORZHA01L917982H8339932',
}

export const mockPgsAcceptUnitpayPayload = {
  serverId: 'TEST0',
  id: 123,
  unitpayId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
  profit: 100,
  payAmount: 3000,
  sum: 300000,
  additional: 150,
}

export const mockCheckUnitpayQuery = {
  method: 'check',
  params: {
    account: 'test.___TEST0.___site',
    signature: 'c78077c12a83b9b60606d6cc380e558ada5c968028e991437d009cd98b474f26',
    unitpayId: '9c65e1b2-06a6-443d-9e91-e3f80d9e4111',
    orderSum: '100',
    paymentType: 'pix',
    profit: 200,
  },
}

export const mockUnitpayPaymentUnitpay = {
  id: 123,
  unitpayId: '9c65e1b2-06a6-443d-9e91-e3f80d9e4111',
  account: 'test',
  payAmount: 1500,
  sum: 150000,
  additional: 15,
  profit: null,
  dateCreate: new Date(),
  dateComplete: null,
  status: 0,
  multiplier: 1,
  comment: 'Unitpay',
  args: null,
  source: unitpay_payments_source.site,
  method: 'pix',
  shop: null,
  currency: 'RUB',
}

export const mockUnitpayFindPaymentByCheck = {
  where: {
    unitpayId: '9c65e1b2-06a6-443d-9e91-e3f80d9e4111',
  },
}

export const mockUnitpayFindPaymentByPay = {
  where: {
    unitpayId: 'ee820da2-3c41-4259-aedd-43369df20c42',
    status: 0,
  },
}

export const mockUnitpayAcceptUnitpayPayload = {
  serverId: 'TEST0',
  id: 123,
  unitpayId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
  profit: 0.97,
  payAmount: 1,
  sum: 1,
  additional: 0,
  method: 'Bnpl',
}

export const mockUnitpayPayment = {
  id: 123,
  unitpayId: '9c65e1b2-06a6-443d-9e91-e3f80d9e4111',
  account: 'test',
  payAmount: 100,
  sum: 10000,
  additional: 0,
  profit: null,
  dateCreate: new Date(),
  dateComplete: null,
  status: 0,
  multiplier: 1,
  comment: 'Unitpay',
  args: null,
  source: 'site',
  method: 'pix',
  shop: null,
  currency: 'RUB',
}

export const { id: mockUnitpayPaymentId, ...mockUnitpayPaymentWithoutId } = mockUnitpayPayment
