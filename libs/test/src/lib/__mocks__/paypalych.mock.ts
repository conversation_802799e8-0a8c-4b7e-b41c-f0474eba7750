import { unitpay_payments_source } from '@majestic-backend/prisma-client-game'
import { PaymentStatus } from '@majestic-backend/types'

export const mockPaypalychConfig = {
  AUTH_TOKEN: 'authToken',
  SHOP_ID: '12345',
  MIN_AMOUNT: '100',
}

export const mockCreatePaypalychBody = {
  serverId: 'TEST0',
  login: 'test',
  amount: 1500,
  source: unitpay_payments_source.site,
  method: 'pix',
  args: {
    subId: 'start2',
  },
}

export const mockCreatePaypalychArgs = {
  args: {
    subId: 'start2',
  },
}

export const mockCheckPaypalychBody = {
  params: {
    Status: PaymentStatus.SUCCESS,
    OutSum: 100,
    InvId: '123',
    SignatureValue: '74634135A98AA152D80825DCEFD11E0D',
    TrsId: '***********',
    Commission: 55,
    custom: 'test.___TEST0',
  },
}

export const mockAxiosPaypalychReturn = {
  data: {
    bill_id: '********',
    link_page_url: 'http://paypalych-approval',
  },
}

export const mockUnitpayPaymentPaypalych = {
  id: 123,
  unitpayId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
  account: 'test',
  payAmount: 1500,
  sum: 150000,
  additional: 15,
  profit: null,
  dateCreate: new Date(),
  dateComplete: null,
  status: 0,
  multiplier: 1,
  comment: 'PayPalych',
  args: '{"firstName":"pixName","document":"pixDocument","staticId":"123","season":"Summer season"}',
  source: unitpay_payments_source.site,
  method: 'pix',
  shop: null,
  currency: 'RUB',
}

export const { id: mockUnitpayPaymentPaypalychId, ...mockUnitpayPaymentPaypalychWithoutId } =
  mockUnitpayPaymentPaypalych

export const mockPaypalychFindPaymentByPayload = {
  where: {
    unitpayId: '***********',
    // status: 0,
  },
}

export const mockPaypalychAcceptUnitpayPayload = {
  serverId: 'TEST0',
  id: 123,
  unitpayId: '***********',
  profit: 45,
}
