import { unitpay_payments_source } from '@majestic-backend/prisma-client-game'
import { Currencies, MultihubMethod, MultihubPaymentStatus } from '@majestic-backend/types'

export const mockCreateMultihubBody = {
  serverId: 'TEST',
  login: 'test',
  amount: 1500,
  source: unitpay_payments_source.site,
  email: '<EMAIL>',
  firstName: 'TestName',
  document: 'testDocument',
  args: {
    subId: 'start2',
  },
}

export const mockMultihubPayment = {
  id: 1,
  unitpayId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
  account: 'test',
  payAmount: 1500,
  sum: 150000,
  additional: 0,
  comment: 'Multihub',
  status: 0,
  multiplier: 1,
  source: unitpay_payments_source.site,
  method: 'PIX',
  currency: Currencies.BRL,
  args: '{"firstName":"TestName","document":"testDocument"}',
  dateCreate: new Date(),
  dateComplete: null,
  shop: null,
  profit: null,
}

export const mockMultihubResponse = {
  result: {
    payment: {
      identifiers: {
        h_id: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
      },
      status: {
        status: MultihubPaymentStatus.AwaitRedirect,
      },
      redirect: {
        to: 'https://example.com/redirect',
      },
    },
  },
}

export const mockCheckMultihubBody = {
  hash: '1cf0e6654921a30c8463df4401d5cfecbf05c53430d72d42673784577f2245c95ea43b3285258102f512ae165cc6dd383d83c61c8654aa1f96fda279679ed3c1',
  params: {
    payment: {
      identifiers: {
        h_id: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
      },
      status: {
        status: MultihubPaymentStatus.Success,
        final: true,
      },
      payer: {
        customer_account: {
          id: 'test.___TEST',
        },
      },
      amount: {
        value: 150000,
      },
    },
    method: MultihubMethod.PaymentUpdate,
  },
}

export const mockMultihubAcceptUnitpayPayload = {
  serverId: 'TEST0',
  id: 123,
  unitpayId: '01e6d021-0aba-46c4-86a8-bde0d8b8cb9c',
  profit: 100,
  payAmount: 3000,
  sum: 300000,
  additional: 150,
}
