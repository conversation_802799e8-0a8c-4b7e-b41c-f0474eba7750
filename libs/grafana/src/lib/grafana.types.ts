export interface Alert {
  /** Current status of the alert, firing or resolved */
  status: string
  /** Labels that are part of this alert, map of string keys to string values */
  labels?: Record<string, string>
  /** Annotations that are part of this alert, map of string keys to string values */
  annotations?: {
    /** Summary of the alert */
    summary?: string
    /** Description of the alert */
    description?: string
    /** Runbook URL */
    runbook_url?: string
  }
  /** Start time of the alert */
  startsAt: string
  /** End time of the alert, default value when not resolved is 0001-01-01T00:00:00Z */
  endsAt: string
  /** Values that triggered the current status */
  values?: Record<string, string>
  /** URL of the alert rule in the Grafana UI */
  generatorURL: string
  /** The labels fingerprint, alarms with the same labels will have the same fingerprint */
  fingerprint: string
  /** URL to silence the alert rule in the Grafana UI */
  silenceURL: string
  /** URL of a screenshot of a panel assigned to the rule that created this notification */
  imageURL?: string
}

export interface WebhookPayload {
  /** Name of the webhook */
  receiver: string
  /** Current status of the alert, firing or resolved */
  status: string
  /** ID of the organization related to the payload */
  orgId: number
  /** Alerts that are triggering */
  alerts: Alert[]
  /** Labels that are used for grouping, map of string keys to string values */
  groupLabels: Record<string, string>
  /** Labels that all alarms have in common, map of string keys to string values */
  commonLabels: Record<string, string>
  /** Annotations that all alarms have in common, map of string keys to string values */
  commonAnnotations: Record<string, string>
  /** External URL to the Grafana instance sending this webhook */
  externalURL: string
  /** Version of the payload */
  version: string
  /** Key that is used for grouping */
  groupKey: string
  /** Number of alerts that were truncated */
  truncatedAlerts: number
  /** Will be deprecated soon */
  title: string
  /** Will be deprecated soon */
  state: string
  /** Will be deprecated soon */
  message: string
}
