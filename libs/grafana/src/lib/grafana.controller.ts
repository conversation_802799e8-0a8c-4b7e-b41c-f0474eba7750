import { createHash } from 'crypto'

import { Controller, Post, Req, UseGuards } from '@nestjs/common'
import { EventBus } from '@nestjs/cqrs'
import { Request } from 'express'

import { GrafanaAlertFiredEvent } from './events/grafana-alert-fired.event'
import { GrafanaAlertResolvedEvent } from './events/grafana-alert-resolved.event'
import { WebhookPayload } from './grafana.types'
import { GrafanaWebhookGuard } from './guards/grafana-webhook.guard'

@Controller('grafana')
export class GrafanaContoller {
  constructor(private readonly eventBus: EventBus) {}

  @Post('webhook')
  @UseGuards(GrafanaWebhookGuard)
  public handleWebhook(@Req() req: Request) {
    const payload = req.body as WebhookPayload
    payload.alerts.forEach(async alert => {
      const uniqueAlertId = createHash('md5')
        .update(alert.startsAt + alert.fingerprint)
        .digest('hex')

      switch (alert.status) {
        case 'firing':
          this.eventBus.publish(new GrafanaAlertFiredEvent(uniqueAlertId, alert))
          break
        case 'resolved':
          this.eventBus.publish(new GrafanaAlertResolvedEvent(uniqueAlertId, alert))
          break
        default:
          break
      }
    })

    return 'thanks!'
  }
}
