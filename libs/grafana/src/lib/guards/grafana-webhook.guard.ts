import { CanActivate, ExecutionContext, Inject, Injectable } from '@nestjs/common'
import { Request } from 'express'

import { GrafanaInstance } from '@majestic-backend/config'
import { GRAFANA_CONFIG_TOKEN } from '../grafana.constants'

@Injectable()
export class GrafanaWebhookGuard implements CanActivate {
  constructor(@Inject(GRAFANA_CONFIG_TOKEN) private readonly config: GrafanaInstance) {}

  public async canActivate(ctx: ExecutionContext): Promise<boolean> {
    const req = ctx.switchToHttp().getRequest<Request>()
    const webhookSecret = this.config.WEBHOOK_SECRET
    if (webhookSecret && req.header('Authorization') !== `Bearer ${webhookSecret}`) {
      return false
    }

    return true
  }
}
