{"name": "prisma-client-backend", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/prisma-client-backend/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/prisma-client-backend/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/prisma-client-backend/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "tags": []}