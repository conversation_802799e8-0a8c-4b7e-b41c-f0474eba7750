import { IsDefined, IsNotEmpty, <PERSON>String, ValidateNested } from 'class-validator'
import { InfisicalVar } from '../decorators/infisical-var.decorator'
import { Type } from 'class-transformer'
import { MailLang } from './models'
import { ALLOWED_LANGS } from '@shared_config/mail'

export class MailgunInstance {
  @IsString()
  @IsNotEmpty()
  public API_KEY!: string

  @IsString()
  @IsNotEmpty()
  public USERNAME!: string
}

export class MailganerInstance {
  @IsString()
  @IsNotEmpty()
  API_KEY!: string

  @IsString()
  @IsNotEmpty()
  API_BASE!: string
}

export class MailConfig {
  @InfisicalVar('/mail/mailgun/*', 'mail.mailgun')
  @IsDefined()
  @ValidateNested()
  @Type(() => MailgunInstance)
  mailgun!: MailgunInstance

  @InfisicalVar('/mail/mailganer/*', 'mail.mailganer')
  @IsDefined()
  @ValidateNested()
  @Type(() => MailganerInstance)
  mailganer!: MailganerInstance

  @InfisicalVar('/mail/langs/*', 'mail.langs')
  @ValidateNested({ each: true })
  @Type(() => MailLang)
  langs!: Map<ALLOWED_LANGS, MailLang>
}
