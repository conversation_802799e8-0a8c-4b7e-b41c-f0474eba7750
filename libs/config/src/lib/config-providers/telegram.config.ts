import { Type } from 'class-transformer'

import { InfisicalVar } from '../decorators/infisical-var.decorator'
import { TelegramInstance, TelegramTimeTracking } from './models'

export class TelegramConfig {
  @InfisicalVar('/telegram/internal/*', 'telegram.internal')
  @Type(() => TelegramInstance)
  internal!: TelegramInstance

  @InfisicalVar('/telegram/public/*', 'telegram.public')
  @Type(() => TelegramInstance)
  public!: TelegramInstance

  @InfisicalVar('/telegram/timeTracking/*', 'telegram.timeTracking')
  @Type(() => TelegramTimeTracking)
  timeTracking!: TelegramTimeTracking
}
