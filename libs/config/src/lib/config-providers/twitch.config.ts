import { IsString } from 'class-validator'
import { InfisicalVar } from '../decorators/infisical-var.decorator'

export class TwitchConfig {
  @InfisicalVar('/twitch/CLIENT_ID', 'twitch.CLIENT_ID')
  @IsString()
  CLIENT_ID!: string

  @InfisicalVar('/twitch/GAME_ID', 'twitch.GAME_ID')
  @IsString()
  GAME_ID!: string

  @InfisicalVar('/twitch/SECRET', 'twitch.SECRET')
  @IsString()
  SECRET!: string
}
