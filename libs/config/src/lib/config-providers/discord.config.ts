import { Type } from 'class-transformer'
import { InfisicalVar } from '../decorators/infisical-var.decorator'
import { DiscordInstance } from './models'

export class DiscordConfig {
  @InfisicalVar('/discord/internal/*', 'discord.internal')
  @Type(() => DiscordInstance)
  internal!: DiscordInstance

  @InfisicalVar('/discord/public-bugs/*', 'discord.publicBugs')
  @Type(() => DiscordInstance)
  publicBugs!: DiscordInstance
}
