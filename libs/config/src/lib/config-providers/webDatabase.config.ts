import { IsN<PERSON>ber, IsString } from 'class-validator'
import { InfisicalVar } from '../decorators/infisical-var.decorator'

export class WebDatabaseConfig {
  @InfisicalVar('/webDatabase/HOST', 'webDatabase.HOST')
  @IsString()
  HOST!: string

  @InfisicalVar('/webDatabase/PORT', 'webDatabase.PORT')
  @IsNumber()
  PORT!: number

  @InfisicalVar('/webDatabase/USER', 'webDatabase.USER')
  @IsString()
  USER!: string

  @InfisicalVar('/webDatabase/PASSWORD', 'webDatabase.PASSWORD')
  @IsString()
  PASSWORD!: string

  @InfisicalVar('/webDatabase/DATABASE', 'webDatabase.DATABASE')
  @IsString()
  DATABASE!: string
}
