import { InfisicalVar } from '../decorators/infisical-var.decorator'
import { Type } from 'class-transformer'
import {
  MultihubPayment,
  PaypalychPayment,
  PgsKeysPayment,
  TinkoffPayment,
  UnitpayPayment,
} from './models'

export class PaymentsConfig {
  @InfisicalVar('/payments/tinkoff/*', 'payments.tinkoff')
  @Type(() => TinkoffPayment)
  tinkoff!: TinkoffPayment

  @InfisicalVar('/payments/unitpay/*', 'payments.unitpay')
  @Type(() => UnitpayPayment)
  unitpay!: UnitpayPayment

  @InfisicalVar('/payments/pgsKeys/*', 'payments.pgsKeys')
  @Type(() => PgsKeysPayment)
  pgsKeys!: PgsKeysPayment

  @InfisicalVar('/payments/paypalych/*', 'payments.paypalych')
  @Type(() => PaypalychPayment)
  paypalych!: PaypalychPayment

  @InfisicalVar('/payments/multihub/*', 'payments.multihub')
  @Type(() => MultihubPayment)
  multihub!: MultihubPayment
}
