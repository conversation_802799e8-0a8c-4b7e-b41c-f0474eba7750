import { Transform, Type } from 'class-transformer'
import { IsString, IsN<PERSON>ber, IsBoolean, IsOptional } from 'class-validator'
// eslint-disable-next-line @nx/enforce-module-boundaries
import { toBoolean } from '@majestic-backend/contracts'

export class MySqlConfig {
  @IsString()
  HOST!: string

  @IsNumber()
  PORT!: number

  @IsString()
  USER!: string

  @IsString()
  PASSWORD!: string

  @IsString()
  DATABASE!: string
}

export class GameServerConfig {
  @IsString()
  NAME!: string

  @IsString()
  REGION!: string

  @Type(() => MySqlConfig)
  mysql!: MySqlConfig
}
