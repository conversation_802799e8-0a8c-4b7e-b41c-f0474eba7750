import { Transform } from 'class-transformer'
import { <PERSON>A<PERSON>y, IsNotEmpty, IsString, IsUrl } from 'class-validator'
import { transformArray } from '../../variable-types/array'

export class GitlabInstance {
  @IsUrl()
  public HOST!: string

  @IsString()
  @IsNotEmpty()
  public TOKEN!: string

  @IsString()
  @IsNotEmpty()
  public WEBHOOK_SECRET!: string

  @IsArray()
  @IsString({ each: true })
  @Transform(transformArray())
  public PROJECT_IDS: string[] = []

  @IsArray()
  @IsString({ each: true })
  @Transform(transformArray())
  public BRANCH_NAMES: string[] = ['master', 'develop']

  @IsArray()
  @IsString({ each: true })
  @Transform(transformArray())
  public JOB_IGNORE_PATTERNS: string[] = []
}
