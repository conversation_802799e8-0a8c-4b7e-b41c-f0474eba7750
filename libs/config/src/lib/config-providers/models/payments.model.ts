// eslint-disable-next-line @nx/enforce-module-boundaries
import { toBoolean } from '@majestic-backend/contracts'
import { Transform } from 'class-transformer'
import { IsBoolean, IsNumber, IsString } from 'class-validator'

export class TinkoffPayment {
  @IsString()
  TERMINAL_KEY!: string
  @IsString()
  PASSWORD!: string
  @IsNumber()
  SBP_PERCENT!: number
  @IsNumber()
  SBP_MAXIMUM!: number
  @IsNumber()
  DOLYAME_PERCENT!: number
  @IsNumber()
  DOLYAME_MAXIMUM!: number
  @IsNumber()
  CARDS_PERCENT!: number
  @IsNumber()
  CARDS_MINIMUM!: number
  @IsNumber()
  MIN_AMOUNT!: number
}

export class UnitpayPayment {
  @IsString()
  PUBLIC_KEY!: string
  @IsString()
  SECRET_KEY!: string
  @IsString()
  PROJECT_ID!: string
}

export class PaypalychPayment {
  @IsString()
  AUTH_TOKEN!: string
  @IsString()
  SHOP_ID!: string
  @IsNumber()
  MIN_AMOUNT!: number
}

export class PgsKeysPayment {
  @IsString()
  STORE_ID!: string
  @IsString()
  API_KEY!: string
  @IsNumber()
  PRICE!: number
  @IsBoolean()
  @Transform(toBoolean)
  DISABLED!: boolean
}

export class MultihubPayment {
  @IsString()
  URL!: string
  @IsString()
  APP_ID!: string
  @IsString()
  SECRET_KEY!: string
}
