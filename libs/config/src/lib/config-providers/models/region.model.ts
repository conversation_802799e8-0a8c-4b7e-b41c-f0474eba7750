import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator'
import { Currencies } from '@majestic-backend/types'

export class Region {
  @IsString()
  BASE_URL!: string

  @IsEnum(Currencies)
  CURRENCY!: Currencies

  @IsString()
  PAYPAL_CLIENT_ID!: string

  @IsString()
  PAYPAL_SECRET!: string

  @IsNumber()
  PAYPAL_MIN_AMOUNT!: number

  @IsString()
  PAYPAL_MODE!: string

  @IsNumber()
  COINS_AMOUNT!: number

  @IsNumber()
  DONATE_MULTIPLIER!: number

  @IsString()
  PGS_STORE_ID!: string

  @IsString()
  PGS_API_KEY!: string

  @IsNumber()
  PGS_MIN_AMOUNT!: number
}
