import { Type } from 'class-transformer'
import { InfisicalVar } from '../../decorators/infisical-var.decorator'
import { TelegramToken } from './telegram-token.model'

export class TelegramTimeTracking {
  @InfisicalVar('/telegram/management/*', 'telegram.management')
  @Type(() => TelegramToken)
  management!: TelegramToken

  @InfisicalVar('/telegram/personal/*', 'telegram.personal')
  @Type(() => TelegramToken)
  personal!: TelegramToken
}
