import { IsNumber, IsString } from 'class-validator'
import { EnvVar } from '../../decorators/env-var.decorator'
import { Service } from '../models'

export class BotsInternalServiceConfig implements Service {
  @EnvVar('BOTS_INTERNAL_HOST')
  @IsString()
  host!: string

  @EnvVar('BOTS_INTERNAL_PORT')
  @IsNumber()
  port!: number

  @EnvVar('BOTS_INTERNAL_METRICS_PORT')
  @IsNumber()
  metricsPort!: number
}
