import { IsNumber, IsString } from 'class-validator'
import { EnvVar } from '../../decorators/env-var.decorator'
import { Service } from '../models'

export class GameFeaturesServiceConfig implements Service {
  @EnvVar('GAME_FEATURES_HOST')
  @IsString()
  host!: string

  @EnvVar('GAME_FEATURES_PORT')
  @IsNumber()
  port!: number

  @EnvVar('GAME_FEATURES_METRICS_PORT')
  @IsNumber()
  metricsPort!: number
}
