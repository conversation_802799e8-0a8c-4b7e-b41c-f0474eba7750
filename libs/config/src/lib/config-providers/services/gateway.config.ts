import { IsBoolean, IsN<PERSON>ber, IsString } from 'class-validator'
import { EnvVar } from '../../decorators/env-var.decorator'
import { Service } from '../models'

export class GatewayServiceConfig implements Service {
  @EnvVar('GATEWAY_HOST')
  @IsString()
  host!: string

  @EnvVar('GATEWAY_PORT')
  @IsNumber()
  port!: number

  @EnvVar('GATEWAY_METRICS_PORT')
  @IsNumber()
  metricsPort!: number

  @EnvVar('GATEWAY_SWAGGER_ENABLED')
  @IsBoolean()
  swaggerEnabled = false
}
