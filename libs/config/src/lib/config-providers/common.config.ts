import { IsBoolean, IsOptional, IsString } from 'class-validator'
import { EnvVar } from '../decorators/env-var.decorator'
import { InfisicalVar } from '../decorators/infisical-var.decorator'
import { Transform } from 'class-transformer'
// eslint-disable-next-line @nx/enforce-module-boundaries
import { toBoolean } from '@majestic-backend/contracts'

export class CommonConfig {
  @EnvVar('METRICS_DISABLED')
  @IsBoolean()
  METRICS_DISABLED = false

  @InfisicalVar('/JWT_SECRET', 'JWT_SECRET')
  @IsString()
  JWT_SECRET!: string

  @InfisicalVar('/METRICS_API_KEY', 'METRICS_API_KEY')
  @IsString()
  @IsOptional()
  METRICS_API_KEY?: string

  @EnvVar('LOGGER_LEVEL')
  @IsString()
  LOGGER_LEVEL = 'verbose'

  @EnvVar('PB_ACTIVE_ACTIONS_DISABLED')
  @IsBoolean()
  @Transform(toBoolean)
  PB_ACTIVE_ACTIONS_DISABLED = true
}
