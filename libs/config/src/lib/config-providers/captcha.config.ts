import { IsBoolean, IsString } from 'class-validator'
import { InfisicalVar } from '../decorators/infisical-var.decorator'
import { Transform } from 'class-transformer'
// eslint-disable-next-line @nx/enforce-module-boundaries
import { toBoolean } from '@majestic-backend/contracts'

const DEFAULT_CF_CAPTCHA_ENDPOINT = 'https://challenges.cloudflare.com/turnstile/v0/siteverify'

export class CaptchaConfig {
  @InfisicalVar('/captcha/CF_CAPTCHA_ENDPOINT', 'captcha.CF_CAPTCHA_ENDPOINT')
  @IsString()
  CF_CAPTCHA_ENDPOINT = DEFAULT_CF_CAPTCHA_ENDPOINT

  @InfisicalVar('/captcha/CF_CAPTCHA_SECRET_KEY', 'captcha.CF_CAPTCHA_SECRET_KEY')
  @IsString()
  CF_CAPTCHA_SECRET_KEY!: string

  @InfisicalVar('/captcha/DISABLED', 'captcha.DISABLED')
  @IsBoolean()
  @Transform(toBoolean)
  DISABLED!: boolean
}
