import { IsString } from 'class-validator'
import { InfisicalVar } from '../decorators/infisical-var.decorator'

export class UploadPathsConfig {
  @InfisicalVar('/uploadPaths/CAMERA', 'uploadPaths.CAMERA')
  @IsString()
  CAMERA!: string

  @InfisicalVar('/uploadPaths/FAMILY_LOGOS', 'uploadPaths.FAMILY_LOGOS')
  @IsString()
  FAMILY_LOGOS!: string

  @InfisicalVar('/uploadPaths/MARKETPLACE', 'uploadPaths.MARKETPLACE')
  @IsString()
  MARKETPLACE!: string

  @InfisicalVar('/uploadPaths/REPORTS', 'uploadPaths.REPORTS')
  @IsString()
  REPORTS!: string
}
