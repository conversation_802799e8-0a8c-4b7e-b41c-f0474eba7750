import { IsString, IsNotEmpty } from 'class-validator'
import { InfisicalVar } from '../decorators/infisical-var.decorator'

export class YandexTrackerConfig {
  @InfisicalVar('/yandexTracker/O_AUTH', 'yandexTracker.O_AUTH')
  @IsString()
  @IsNotEmpty()
  public O_AUTH!: string

  @InfisicalVar('/yandexTracker/X_CLOUD_ORG_ID', 'yandexTracker.X_CLOUD_ORG_ID')
  @IsString()
  @IsNotEmpty()
  public X_CLOUD_ORG_ID!: string
}
