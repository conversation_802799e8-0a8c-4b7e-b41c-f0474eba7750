import { IsString } from 'class-validator'
import { InfisicalVar } from '../decorators/infisical-var.decorator'

export class S3Config {
  @InfisicalVar('/s3/ENDPOINT', 's3.ENDPOINT')
  @IsString()
  ENDPOINT!: string

  @InfisicalVar('/s3/ACCESS_KEY_ID', 's3.ACCESS_KEY_ID')
  @IsString()
  ACCESS_KEY_ID!: string

  @InfisicalVar('/s3/BUCKET', 's3.BUCKET')
  @IsString()
  BUCKET!: string

  @InfisicalVar('/s3/PUBLIC_URL', 's3.PUBLIC_URL')
  @IsString()
  PUBLIC_URL!: string

  @InfisicalVar('/s3/REGION', 's3.REGION')
  @IsString()
  REGION!: string

  @InfisicalVar('/s3/SECRET_ACCESS_KEY', 's3.SECRET_ACCESS_KEY')
  @IsString()
  SECRET_ACCESS_KEY!: string
}
