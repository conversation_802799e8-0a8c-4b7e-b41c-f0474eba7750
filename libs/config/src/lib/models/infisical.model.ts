export interface IInfisicalSecret {
  id: string
  _id: string
  workspace: string
  environment: string
  version: number
  type: string
  secretKey: string
  secretValue: string
  secretComment: string
  secretReminderNote: string
  secretReminderRepeatDays: number
  skipMultilineEncoding: boolean
  metadata: string
  createdAt: string
  updatedAt: string
  secretPath: string
  tags: ITag[]
}

interface ITag {
  id: string
  slug: string
  color: string
  name: string
}

export interface IInfisicalGetSecretsResponse {
  secrets?: IInfisicalSecret[]
  imports?: Record<string, unknown>[]
}

export interface IInfisicalLoginResponse {
  accessToken: string
  expiresIn: number
  accessTokenMaxTTL: number
  tokenType: string
}
