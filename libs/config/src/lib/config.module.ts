import { DynamicModule, Global, Logger, <PERSON>dule, ValueProvider } from '@nestjs/common'
import { Constructor } from '@nestjs/common/utils/merge-with-values.util'
import { IMetadata } from './models/metadata.model'
import { EnvConfigModule } from './value-providers/env/env-config.module'
import { InfisicalConfigModule } from './value-providers/infisical/infisical-config.module'
import { validateSync, ValidationError } from 'class-validator'

@Global()
@Module({})
export class ConfigModule {
  private static readonly sharedProviders: ValueProvider[] = []
  private static readonly logger = new Logger(ConfigModule.name)

  public static forRoot(): DynamicModule {
    return {
      module: ConfigModule,
      providers: this.sharedProviders,
      exports: this.sharedProviders,
    }
  }

  public static async init(configClasses: Constructor<any>[]) {
    configClasses.forEach(configClass => {
      this.sharedProviders.push({
        provide: configClass,
        useValue: new configClass(),
      })
    })
    EnvConfigModule.init()
    await InfisicalConfigModule.init()
    if (process.env['VALIDATE_CONFIG'] === 'false') return
    this.validate()
  }

  public static getProvidersMetadata(key: symbol): [Constructor<any>, IMetadata[]][] {
    return this.sharedProviders.map(provider => {
      const configClass = Object.getPrototypeOf(provider.useValue)
      return [configClass.constructor, Reflect.getMetadata(key, configClass.constructor)]
    })
  }

  public static patchProvider(configClass: Constructor<any>, transformed: Record<string, any>) {
    const found = this.sharedProviders.find(el => el.provide === configClass)
    if (!found) return

    for (const [key, value] of Object.entries(transformed)) {
      found.useValue[key] = value
    }
  }

  public static validate() {
    this.sharedProviders.forEach(configClass => {
      const errors = validateSync(configClass.useValue)
      if (errors.length > 0) {
        const configName = Object.getPrototypeOf(configClass.useValue).constructor.name
        this.logger.error(`Error validating configuration ${configName}:`, this.niceErrors(errors))
        process.exit(1)
      }
    })
  }

  private static niceErrors(errors: ValidationError[]): string {
    return JSON.stringify(
      errors.reduce((acc, e) => {
        acc[e.property] = Object.values(e.constraints || {})
        return acc
      }, {} as Record<string, string[]>),
      null,
      2,
    )
  }
}
