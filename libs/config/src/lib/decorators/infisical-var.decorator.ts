import { IMetadata } from '../models/metadata.model'

export const PROP_INFISICAL_NAMES = Symbol('PROP_INFISICAL_NAMES')

export const InfisicalVar = (name: string, local?: string): PropertyDecorator => {
  return (target, propertyKey) => {
    const current: IMetadata[] = Reflect.getMetadata(PROP_INFISICAL_NAMES, target.constructor) || []
    // toString fix types (exclude symbol)
    current.push({ propertyKey: propertyKey.toString(), name, local })
    Reflect.defineMetadata(PROP_INFISICAL_NAMES, current, target.constructor)
  }
}
