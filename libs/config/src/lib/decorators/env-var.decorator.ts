import { IMetadata } from '../models/metadata.model'

export const PROP_ENV_NAMES = Symbol('PROP_ENV_NAMES')

export const EnvVar = (name: string): PropertyDecorator => {
  return (target, propertyKey) => {
    const current: IMetadata[] = Reflect.getMetadata(PROP_ENV_NAMES, target.constructor) || []
    // toString fix types (exclude symbol)
    current.push({ propertyKey: propertyKey.toString(), name })
    Reflect.defineMetadata(PROP_ENV_NAMES, current, target.constructor)
  }
}
