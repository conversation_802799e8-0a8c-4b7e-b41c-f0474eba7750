import { config } from 'dotenv'
import { ConfigModule } from '../../config.module'
import { PROP_ENV_NAMES } from '../../decorators/env-var.decorator'
import { Constructor, IMetadata } from '../../models'
import { plainToClass } from 'class-transformer'

config()

export class EnvConfigModule {
  public static init() {
    ConfigModule.getProvidersMetadata(PROP_ENV_NAMES).forEach(([configClass, meta]) => {
      this.applyClass(configClass, meta)
    })
  }

  private static applyClass(configClass: Constructor<any>, meta: IMetadata[] = []): void {
    const plain: Record<string, unknown> = meta.reduce<Record<string, unknown>>(
      (acc, { propertyKey, name }) => {
        acc[propertyKey] = process.env[name]
        return acc
      },
      {},
    )

    const transformed = plainToClass(configClass, plain, {
      enableImplicitConversion: true,
      exposeUnsetFields: false,
    })

    ConfigModule.patchProvider(configClass, transformed)
  }
}
