import axios from 'axios'
import {
  IInfisicalGetSecretsResponse,
  IInfisicalLoginResponse,
  IInfisicalSecret,
} from '../../models'
import * as path from 'path'
import { readFileSync, writeFileSync } from 'fs'

export const getConfig = async () => {
  const siteUrl = process.env['INFISICAL_URL']
  const clientId = process.env['INFISICAL_CLIENT_ID']
  const clientSecret = process.env['INFISICAL_SECRET']
  const clientToken = process.env['INFISICAL_TOKEN']

  const environment = process.env['INFISICAL_ENV'] || ''

  if (environment === 'local') return []

  const hasCredentials = (clientId && clientSecret) || clientToken
  if (!siteUrl || !hasCredentials) {
    throw new Error('Infisical creds not provided in .env file')
  }

  const projectId = process.env['INFISICAL_PROJECT_ID']

  if (!projectId) {
    throw new Error('Infisical project id not provided in .env files')
  }

  let secrets: IInfisicalSecret[] = []

  try {
    const token = await getToken(siteUrl, clientId, clientSecret)
    secrets =
      (await fetchSecrets({
        url: siteUrl,
        token,
        projectId,
        environment,
      })) || []
  } catch (e) {
    console.error(`Error on fetch infisical secrets ${e}`)
    throw new Error(`Error on fetch infisical secrets`)
  }

  return secrets
}

const login = async ({
  url,
  clientId,
  clientSecret,
}: Record<string, string>): Promise<IInfisicalLoginResponse> => {
  const res = await axios.request<IInfisicalLoginResponse>({
    url: url + '/api/v1/auth/universal-auth/login',
    method: 'POST',
    data: {
      clientId,
      clientSecret,
    },
  })
  return res.data
}

const fetchSecrets = async ({ url, token, projectId, environment }: Record<string, string>) => {
  const dumpPath = path.resolve(process.env['INFISICAL_DUMP_PATH'] ?? '.', './infisical-dump.json')

  let secrets: IInfisicalGetSecretsResponse['secrets']

  try {
    secrets = (
      await axios.request<IInfisicalGetSecretsResponse>({
        url: url + '/api/v3/secrets/raw',
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: {
          workspaceId: projectId,
          environment,
          recursive: true,
        },
      })
    ).data.secrets

    writeFileSync(dumpPath, JSON.stringify(secrets), 'utf8')
  } catch (e) {
    console.warn(`Error fetching infisical secrets, falling back to local dump: ${e}`)
    try {
      secrets = JSON.parse(readFileSync(dumpPath, 'utf8'))
    } catch (e) {
      console.error(`Error loading local secrets dump ${e}`)
      throw new Error('Cannot load local secrets dump')
    }
  }

  return secrets
}

const getToken = async (siteUrl: string, clientId?: string, clientSecret?: string) => {
  if (process.env['INFISICAL_TOKEN']) {
    return process.env['INFISICAL_TOKEN']
  } else if (clientId && clientSecret) {
    const { accessToken: token } = await login({
      url: siteUrl,
      clientId,
      clientSecret,
    })

    return token
  }

  throw new Error('Cannot get infisical token')
}
