import { Logger } from '@nestjs/common'
import { ConfigModule } from '../../config.module'
import { PROP_INFISICAL_NAMES } from '../../decorators/infisical-var.decorator'
import { Constructor, IInfisicalSecret, IMetadata } from '../../models'
import { getConfig } from './infisical-api.utils'
import { get, isNil, merge } from 'lodash'
import { plainToClass } from 'class-transformer'
import { readFileSync } from 'fs'
import * as path from 'path'

export class InfisicalConfigModule {
  private static logger = new Logger(InfisicalConfigModule.name)
  private static secrets: IInfisicalSecret[] = []
  private static localSecrets: Record<string, any> | null = null

  public static async init() {
    if (process.env['INFISICAL_ENV'] === 'local') {
      this.parseLocal()
    }

    await this.applyConfig()

    ConfigModule.getProvidersMetadata(PROP_INFISICAL_NAMES).forEach(([configClass, meta]) => {
      this.applyInfisicalVars(configClass, meta)
    })
  }

  private static async applyConfig() {
    this.secrets = await getConfig()
  }

  private static applyInfisicalVars(configClass: Constructor<any>, meta: IMetadata[] = []): void {
    const plain: Record<string, unknown> = meta.reduce<Record<string, unknown>>(
      (acc, { propertyKey, name, local }) => {
        let value = this.getSecret(name)

        if (isNil(value) && local) {
          if (!this.localSecrets) {
            return acc
          }
          value = get(this.localSecrets, local)
        }
        acc[propertyKey] = value
        return acc
      },
      {},
    )

    const transformed = plainToClass(configClass, plain, {
      enableImplicitConversion: true,
      exposeUnsetFields: false,
    })

    ConfigModule.patchProvider(configClass, transformed)
  }

  private static parseLocal() {
    const devPath = path.resolve(process.env['INFISICAL_DEV_PATH'] ?? '.', './config.json')
    try {
      this.localSecrets = JSON.parse(readFileSync(devPath, 'utf8'))
    } catch (e) {
      this.logger.error(`Cannot parse config.json, ${e}`)
    }
  }

  public static getSecret(pattern: string) {
    let search = pattern

    if (search[0] === '/') {
      search = search.slice(1, search.length)
    }

    if (pattern.at(-1) === '*') {
      const splitted = search.split('/')
      splitted.pop()
      search = splitted.join('/')

      return this.getSecretsInFolder(search)
    }

    return this.getPlainSecret(search)
  }

  private static getPlainSecret(pattern: string) {
    const path = pattern.split('/')
    const key = path.pop()
    return this.secrets.find(
      secret => secret.secretPath === '/' + path.join('/') && secret.secretKey === key,
    )?.secretValue
  }

  private static getSecretsInFolder(pattern: string) {
    const secrets = this.secrets
      .filter(el => el.secretPath?.startsWith('/' + pattern))
      .reduce((acc, cur) => {
        const curBranch = cur.secretPath
          .replace('/' + pattern, '')
          .split('/')
          .reduceRight<Record<string, unknown>>((branch, path, idx, arr) => {
            // if secret in root folder
            if (arr.length === 1) {
              branch[cur.secretKey] = cur.secretValue
              return branch
            }
            // start from there. Leaf of folder
            if (!Object.keys(branch).length) {
              return { [path]: { [cur.secretKey]: cur.secretValue } }
            }
            if (!path) {
              return branch
            }

            // else go up
            return { [path]: branch }
          }, {})
        return merge(curBranch, acc)
      }, {})

    return Object.keys(secrets).length ? secrets : null
  }
}
