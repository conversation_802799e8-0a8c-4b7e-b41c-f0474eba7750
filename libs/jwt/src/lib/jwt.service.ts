import { Injectable } from '@nestjs/common'
import { JwtService as JWT, JwtVerifyOptions } from '@nestjs/jwt'
import { jwtPayload } from '../models'
import { CommonConfig } from '@majestic-backend/config'

@Injectable()
export class JwtService {
  constructor(private readonly jwtService: JWT, private readonly commonConfig: CommonConfig) {}

  public async signAsync(payload: jwtPayload) {
    return this.jwtService.signAsync(payload, { secret: this.commonConfig.JWT_SECRET })
  }

  public async verifyAsync<T extends object>(token: string, options: JwtVerifyOptions): Promise<T> {
    return this.jwtService.verifyAsync<T>(token, {
      secret: this.commonConfig.JWT_SECRET,
      ...options,
    })
  }
}
