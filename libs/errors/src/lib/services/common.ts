import { HttpStatus } from '@nestjs/common'
import { IErrorPayload } from '../errors'

// Temproary all errors here

export enum Errors {
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  BAD_REQUEST = 'BAD_REQUEST',
  MISSING_PARAMS = 'MISSING_PARAMS',
  SERVER_NOT_FOUND = 'SERVER_NOT_FOUND',
  FAMILY_NOT_FOUND = 'FAMILY_NOT_FOUND',
  EMAIL_IS_INVALID = 'EMAIL_IS_INVALID',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  ACCOUNTS_NOT_FOUND = 'ACCOUNTS_NOT_FOUND',
  ACCOUNT_NOT_FOUND = 'ACCOUNT_NOT_FOUND',
  EMAIL_NOT_EXISTS = 'EMAIL_NOT_EXISTS',
  ACCOUNT_ALREADY_TRY_RECOVER = 'ACCOUNT_ALREADY_TRY_RECOVER',
  USER_ALREADY_TRY_CREATING = 'USER_ALREADY_TRY_CREATING',
  GIFT_ALREADY_GIVEN = 'GIFT_ALREADY_GIVEN',
  LOGIN_ALREADY_USED = 'LOGIN_ALREADY_USED',
  PASSWORD_NOT_EXISTS = 'PASSWORD_NOT_EXISTS',
  EMAIL_ALREADY_USED = 'EMAIL_ALREADY_USED',
  EMAIL_ALREADY_CONFIRMED = 'EMAIL_ALREADY_CONFIRMED',
  RECOVERYCODE_OUT_OF_DATE = 'RECOVERYCODE_OUT_OF_DATE',
  CAPTCHA_NOT_VERIFERED = 'CAPTCHA_NOT_VERIFERED',
  FAMILY_LOGO_TOKEN_EXPIRED = 'FAMILY_LOGO_TOKEN_EXPIRED',
  SECRETKEY_NOT_EXISTS = 'SECRETKEY_NOT_EXISTS',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  FAMILY_LOGO_EXTENSION_NOT_EQUALS = 'FAMILY_LOGO_EXTENSION_NOT_EQUALS',
  UNAUTHORIZED = 'UNAUTHORIZED',
  NOT_FOUND = 'NOT_FOUND',
  METHOD_NOT_ALLOWED = 'METHOD_NOT_ALLOWED',
  REPORT_NOT_FOUND = 'REPORT_NOT_FOUND',
  REPORT_NOT_CREATED = 'REPORT_NOT_CREATED',
  REPORT_IS_CLOSED = 'REPORT_IS_CLOSED',
  NO_ACCESS = 'NO_ACCESS',
  MESSAGE_TOO_LARGE = 'MESSAGE_TOO_LARGE',
  APPROVAL_URL_NOT_FOUND = 'APPROVAL_URL_NOT_FOUND',
  PAYPAL_TRANSACTION_NOT_FOUND = 'PAYPAL_TRANSACTION_NOT_FOUND',
  PAYMENT_NOT_FOUND = 'PAYMENT_NOT_FOUND',
  PAYMENT_TOO_SMALL = 'PAYMENT_TOO_SMALL',
  PAYMENT_ERROR_SIGNATURE = 'PAYMENT_ERROR_SIGNATURE',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  WAIT_LOADING_PROCESS = 'WAIT_LOADING_PROCESS',
  REPORT_ALREADY_USED = 'REPORT_ALREADY_USED',
  PROMOCODE_ALREADY_USED = 'PROMOCODE_ALREADY_USED',
  PAYMENT_ALREADY_EXISTS = 'PAYMENT_ALREADY_EXISTS',
  REGION_NOT_FOUND = 'REGION_NOT_FOUND',
  RULES_NOT_FOUND = 'RULES_NOT_FOUND',
  NEED_2FA = 'NEED_2FA',
  TOO_MANY_REQUESTS = 'TOO_MANY_REQUESTS',
  PAYMENT_TOO_LARGE = 'PAYMENT_TOO_LARGE',
  ERROR_PARSE_BODY = 'ERROR_PARSE_BODY',
  NOT_FOUND_DATA = 'NOT_FOUND_DATA',
  NOT_TOO_FAST = 'NOT_TOO_FAST',
  NO_AVAILABLE_KEYS = 'NO_AVAILABLE_KEYS',
  CONFIRMATION_CODE_NOT_SEND = 'CONFIRMATION_CODE_NOT_SEND',
  SUBSCRIPTION_ID_NOT_FOUND = 'SUBSCRIPTION_ID_NOT_FOUND',
  KEY_ALREADY_FROZEN = 'KEY_ALREADY_FROZEN',
}

export const ErrorsPayload = {
  [Errors.UNKNOWN_ERROR]: {
    code: 1101,
    message: 'Unknown error',
    status: HttpStatus.FORBIDDEN,
  },
  [Errors.ERROR_PARSE_BODY]: {
    code: '0001',
    message: 'Error Parse Body',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.BAD_REQUEST]: {
    code: 1000,
    message: 'Bad Request',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.MISSING_PARAMS]: {
    code: 1001,
    message: 'Missing params',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.SERVER_NOT_FOUND]: {
    code: 1002,
    message: 'Server not found',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.EMAIL_IS_INVALID]: {
    code: 1003,
    message: 'Email is invalid',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.FAMILY_NOT_FOUND]: {
    code: 1004,
    message: 'Family not found',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.PAYMENT_TOO_SMALL]: {
    code: 1005,
    message: 'Payment too small',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.REPORT_NOT_CREATED]: {
    code: 1006,
    message: 'Report Not Created',
    status: HttpStatus.INTERNAL_SERVER_ERROR,
  },
  [Errors.USER_NOT_FOUND]: {
    code: 1101,
    message: 'User not found',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.APPROVAL_URL_NOT_FOUND]: {
    code: 1102,
    message: 'Approval url not found',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.PAYPAL_TRANSACTION_NOT_FOUND]: {
    code: 1103,
    message: 'PayPal transaction not found',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.PAYMENT_NOT_FOUND]: {
    code: 1104,
    message: 'Payment not found',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.ACCOUNTS_NOT_FOUND]: {
    code: 1105,
    message: 'Accounts not found',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.ACCOUNT_NOT_FOUND]: {
    code: 1106,
    message: 'Account not found',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.PAYMENT_ALREADY_EXISTS]: {
    code: 1107,
    message: 'Payment already exists',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.RECOVERYCODE_OUT_OF_DATE]: {
    code: 1200,
    message: 'RecoveryCode out of date',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.CAPTCHA_NOT_VERIFERED]: {
    code: 1201,
    message: 'Captcha not verifered',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.FAMILY_LOGO_TOKEN_EXPIRED]: {
    code: 1202,
    message: 'Family logo token out of date',
    status: HttpStatus.BAD_REQUEST,
  },

  [Errors.SECRETKEY_NOT_EXISTS]: {
    code: 1300,
    message: 'SecretKey does not meet the conditions',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.EMAIL_NOT_EXISTS]: {
    code: 1301,
    message: 'Email does not meet the conditions',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.ACCOUNT_ALREADY_TRY_RECOVER]: {
    code: 1302,
    message: 'User already trying recover password',
    status: HttpStatus.FORBIDDEN,
  },
  [Errors.PASSWORD_NOT_EXISTS]: {
    code: 1303,
    message: 'Password does not meet the conditions',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.EMAIL_ALREADY_USED]: {
    code: 1304,
    message: 'Email already used',
    status: HttpStatus.FORBIDDEN,
  },
  [Errors.FILE_TOO_LARGE]: {
    code: 1305,
    message: 'File too large',
    status: HttpStatus.PAYLOAD_TOO_LARGE,
  },
  [Errors.FAMILY_LOGO_EXTENSION_NOT_EQUALS]: {
    code: 1306,
    message: 'Family logo extension not same',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.USER_ALREADY_TRY_CREATING]: {
    code: 1307,
    message: 'User already trying create',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.WAIT_LOADING_PROCESS]: {
    code: 1308,
    message: 'Report already in process',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.EMAIL_ALREADY_CONFIRMED]: {
    code: 1309,
    message: 'Email already confirmed',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.LOGIN_ALREADY_USED]: {
    code: 1310,
    message: 'Login already used',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.PROMOCODE_ALREADY_USED]: {
    code: 1311,
    message: 'Promocode already used',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.REPORT_ALREADY_USED]: {
    code: 1312,
    message: 'The report is already being answered',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.REPORT_IS_CLOSED]: {
    code: 1313,
    message: 'Report already closed',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.MESSAGE_TOO_LARGE]: {
    code: 1314,
    message: 'Message too large',
    status: HttpStatus.PAYLOAD_TOO_LARGE,
  },
  [Errors.TOO_MANY_REQUESTS]: {
    code: 1315,
    message: 'Too Many Requests',
    status: HttpStatus.TOO_MANY_REQUESTS,
  },
  [Errors.NOT_TOO_FAST]: {
    code: 1316,
    message: 'Not too fast',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.GIFT_ALREADY_GIVEN]: {
    code: 1317,
    message: 'Gift already given',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.UNAUTHORIZED]: {
    code: 1400,
    message: 'Unauthorized',
    status: HttpStatus.UNAUTHORIZED,
  },
  [Errors.PAYMENT_ERROR_SIGNATURE]: {
    code: 1500,
    message: 'Error payment signature',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.PAYMENT_FAILED]: {
    code: 1501,
    message: 'Payment failed',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.NO_ACCESS]: {
    code: 1600,
    message: 'No Access',
    status: HttpStatus.METHOD_NOT_ALLOWED,
  },
  [Errors.NEED_2FA]: {
    code: 1601,
    message: 'Need 2FA Auth',
    status: HttpStatus.BAD_REQUEST,
  },
  [Errors.NOT_FOUND]: {
    code: 1700,
    message: 'Not Found',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.REPORT_NOT_FOUND]: {
    code: 1701,
    message: 'Report Not Found',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.NOT_FOUND_DATA]: {
    code: 1702,
    message: 'Not found data',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.METHOD_NOT_ALLOWED]: {
    code: 1800,
    message: 'Method Not Allowed',
    status: HttpStatus.METHOD_NOT_ALLOWED,
  },
  [Errors.PAYMENT_TOO_LARGE]: {
    code: 1007,
    message: 'Payment too large',
    status: HttpStatus.PAYLOAD_TOO_LARGE,
  },
  [Errors.NO_AVAILABLE_KEYS]: {
    code: 2001,
    message: 'No available keys',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.CONFIRMATION_CODE_NOT_SEND]: {
    code: 2002,
    message: 'Confirmation code was not send',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.SUBSCRIPTION_ID_NOT_FOUND]: {
    code: 2003,
    message: 'Subscription Id in args Not Found',
    status: HttpStatus.NOT_FOUND,
  },
  [Errors.KEY_ALREADY_FROZEN]: {
    code: 2004,
    message: 'Activation key is already frozen',
    status: HttpStatus.CONFLICT,
  },
} as Record<string, IErrorPayload>
