import { Controller, UnauthorizedException } from '@nestjs/common'
import { PrismaService, IAccount, GameModelEnum } from '@majestic-backend/database'
import { jwtPayload } from '@majestic-backend/jwt'
import { maxBy } from 'lodash'
import { IUser } from '@majestic-backend/types'

@Controller()
export class BaseController {
  constructor(protected readonly databaseService: PrismaService) {}

  // Copypaste from admin controller
  protected async getUserData(
    payload: (jwtPayload & { token: string; ip: string }) | null,
  ): Promise<IUser | null> {
    if (!payload) {
      return null
    }
    const serverId = payload.serverId || 'TEST'

    const userModel = this.databaseService.getGameModel(serverId, GameModelEnum.UserModel)
    const accountModel = this.databaseService.getGameModel(serverId, GameModelEnum.AccountModel)
    const tokenModel = this.databaseService.getGameModel(serverId, GameModelEnum.TokenModel)

    const tokenData = await tokenModel.findToken(payload.token)

    if (!tokenData || !tokenData.accountId) {
      throw new UnauthorizedException()
    }

    const [userData, accountsData] = await Promise.all([
      userModel.getUser({ where: { id: tokenData.accountId } }),
      accountModel.getAccounts({ where: { userId: tokenData.accountId } }),
    ])

    if (!userData) {
      throw new UnauthorizedException()
    }

    let isAdmin = false
    let accountIdAdmin = 0
    let levelAdmin = 0

    if (accountsData) {
      isAdmin = accountsData.some(account => (account.admin ?? 0) >= 1)
      if (isAdmin) {
        const account = maxBy(accountsData, (a: IAccount) => a.admin)
        if (account) {
          accountIdAdmin = account.id
          levelAdmin = account.admin ?? levelAdmin
        }
      }
    }

    try {
      await tokenModel.updateActivity(tokenData.id, payload.ip)
    } catch (e) {
      throw new UnauthorizedException()
    }

    return {
      user: userData,
      accounts: accountsData,
      isAdmin,
      accountIdAdmin,
      levelAdmin,
      ip: payload.ip,
      serverId,
      token: payload.token,
      device: tokenData,
    }
  }
}
