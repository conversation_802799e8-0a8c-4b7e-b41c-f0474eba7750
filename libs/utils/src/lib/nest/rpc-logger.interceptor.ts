import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common'
import { Observable } from 'rxjs'
import { tap } from 'rxjs/operators'

@Injectable()
export class RpcLoggerInterceptor implements NestInterceptor {
  private readonly logger = new Logger(RpcLoggerInterceptor.name)

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    if (context.getType() !== 'rpc') {
      return next.handle()
    }

    const className = context.getClass().name
    const methodName = context.getHandler().name
    const controller = `[${className}][${methodName}]`

    const startTime = Date.now()

    return next.handle().pipe(
      tap(() => {
        const executionDurationMs = Date.now() - startTime
        this.logger.log(
          `Processed message for controller ${controller} with execution duration ${executionDurationMs} ms`,
        )
      }),
    )
  }
}
