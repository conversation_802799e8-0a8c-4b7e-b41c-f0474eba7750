import { ExceptionFilter, Catch, Logger } from '@nestjs/common'
import { throwError } from 'rxjs'
import { common } from '@majestic-backend/errors'
import { RpcException } from '@nestjs/microservices'

@Catch()
export class ServiceExceptionsFilter implements ExceptionFilter {
  private logger = new Logger(ServiceExceptionsFilter.name)
  private blacklist: common.Errors[] | string[] = []

  serviceName: string

  constructor(serviceName: string, blacklist: common.Errors[] | string[] = []) {
    this.serviceName = serviceName
    this.blacklist = blacklist
  }

  private shouldLogError(exception: Error): boolean {
    if (this.blacklist?.some(x => x === exception.message)) {
      return false
    }

    return true
  }

  catch(exception: Error) {
    if (this.shouldLogError(exception)) {
      this.logger.error(`${JSON.stringify(exception)}\n${exception.toString()}\n${exception.stack}`)
    }

    const errorMsg = {
      service: this.serviceName,
      error: exception?.message,
    }

    if (exception.message === 'Bad Request Exception') {
      errorMsg.error = common.Errors.MISSING_PARAMS
    }

    return throwError(() => new RpcException(errorMsg))
  }
}
