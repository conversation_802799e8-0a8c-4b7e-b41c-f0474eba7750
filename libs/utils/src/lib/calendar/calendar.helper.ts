import { Logger } from '@nestjs/common'
import axios from 'axios'
import moment from 'moment'

export const weekdays = new Map<number, string>([
  [0, 'MONDAY'],
  [1, 'TUESDAY'],
  [2, 'WEDNESDAY'],
  [3, 'THURSDAY'],
  [4, 'FRIDAY'],
  [5, 'SATURDAY'],
  [6, 'SUNDAY'],
])

export class CalendarHelper {
  private static logger = new Logger(CalendarHelper.name)

  public static getWeekDatesFrom(startWeek: Date): Date[] {
    const startOfWeek = moment(startWeek).startOf('week').add(1, 'days')
    const weekDates: Date[] = []

    for (let i = 0; i < 7; i++) {
      weekDates.push(startOfWeek.clone().add(i, 'days').toDate())
    }

    return weekDates
  }

  public static getDatesFrom(start: Date, end: Date): Date[] {
    const startPeriod = moment(start).startOf('day')
    const endPeriod = moment(end).endOf('day')
    const diff = endPeriod.diff(startPeriod, 'days') + 1
    const weekDates: Date[] = []

    for (let i = 0; i < diff; i++) {
      weekDates.push(startPeriod.clone().add(i, 'days').toDate())
    }

    return weekDates
  }

  public static getMonthDatesFrom(startMonth: Date): Date[] {
    const startOfMonth = moment(startMonth).startOf('month')
    const endOfMonth = moment(startMonth).endOf('month')
    const monthDates: Date[] = []

    for (
      let currentDay = startOfMonth;
      currentDay.isBefore(endOfMonth);
      currentDay.add(1, 'days')
    ) {
      monthDates.push(currentDay.toDate())
    }

    return monthDates
  }

  public static async getMonthWorkingDays(date: string) {
    const [year, month] = date.split('-')

    const params = {
      year: year,
      month: month,
      cc: 'ru',
    }

    try {
      const response = await axios.get(`https://isdayoff.ru/api/getdata`, {
        params,
        responseType: 'text',
      })
      const days = response.data
      this.logger.debug(`Days type in a ${month} ${year}: ${days}`)

      return days
    } catch (err) {
      this.logger.error(`Get month working days error`, err)
      throw err
    }
  }
}
