export const randomString = (length: number, useNumbers = true): string => {
  const words = `${
    useNumbers ? '0123456789' : ''
  }qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM`

  let result = ''

  for (let i = 0; i < length; ++i) {
    const position = Math.floor(Math.random() * words.length - 1)
    result = result + words.substring(position, position + 1)
  }

  return result
}

export const getExtFromMime = (mime: string): string | null => {
  const arr = mime.split('/')
  if (arr.length !== 2) {
    return null
  }
  return arr[1]
}

export const generateRandomConfirmationCode = (length: number) => {
  let code = ''
  const digits = '0123456789'

  for (let i = 0; i < length; i++) {
    code += digits[Math.floor(Math.random() * digits.length)]
  }

  return code
}
