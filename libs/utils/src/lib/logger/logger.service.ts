import { Injectable, LoggerService as LoggerServiceInterface } from '@nestjs/common'
import { createLogger, format, transports, Logger } from 'winston'
import { addColors } from 'winston'

const customLevels = {
  levels: {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3,
    verbose: 4,
  },
  colors: {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    debug: 'blue',
    verbose: 'magenta',
  },
}

addColors(customLevels.colors)

@Injectable()
export class LoggerService implements LoggerServiceInterface {
  private readonly logger: Logger

  constructor() {
    this.logger = createLogger({
      levels: customLevels.levels,
      level: 'debug',
      format: format.combine(
        format.timestamp({ format: 'DD-MM-YYYY HH:mm:ss' }),
        format.printf(({ level, message, context, timestamp }) => {
          const colors = format.colorize().colorize
          const paddedLevel = level.toUpperCase().padEnd(7)

          return `${timestamp}  ${colors(level, paddedLevel)}  ${
            context ? `[${context}]` : ''
          } ${colors(level, String(message))}`
        }),
      ),
      transports: [new transports.Console()],
    })
  }

  setLevel(level: string) {
    this.logger.level = level
  }

  private formatContext(context?: string): string {
    return context || 'Application'
  }

  log(message: any, context?: string) {
    this.logger.info(message, { context: this.formatContext(context) })
  }

  error(message: any, error: unknown, context?: string) {
    if (error instanceof Error) {
      this.logger.error(`${message}. ${error.name}: ${error.message} - ${error.stack}`, {
        context: this.formatContext(context),
      })
    } else {
      this.logger.error(message, {
        context: this.formatContext(context),
      })
    }
  }

  warn(message: any, context?: string) {
    this.logger.warn(message, { context: this.formatContext(context) })
  }

  debug(message: any, context?: string) {
    this.logger.debug(message, { context: this.formatContext(context) })
  }

  verbose(message: any, context?: string) {
    this.logger.verbose(message, { context: this.formatContext(context) })
  }
}
