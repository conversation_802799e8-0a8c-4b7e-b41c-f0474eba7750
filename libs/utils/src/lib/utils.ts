export const sqlBufferToJson = (buffer: Buffer) => JSON.parse(buffer.toString())

export const roundNumber = (number: number, digits: number) => {
  const multiple = Math.pow(10, digits)
  return Math.round(number * multiple) / multiple
}

export const getDayDateLimits = (dateFrom: Date, dateTo: Date) => {
  if (!dateFrom || !dateTo) {
    return null
  }

  return { start: dateFrom, end: dateTo }
}

export const countMoneyToDollar = (amount: number, full = false, isCent = true) => {
  if (!amount) return full ? '0,00' : '0'

  amount = isCent ? amount / 100 : amount
  const currency = new Intl.NumberFormat('en-EN', { style: 'currency', currency: 'USD' }).format(
    amount,
  )

  const result = full ? currency : `${currency.substring(0, currency.length - 3)}`
  return result.replace(/,/g, ' ').replace(/\./g, ',').replace('$', '')
}

export const joaat = (key: string) => {
  const keyLowered = key.toLowerCase()
  let hash = 0

  for (let i = 0; i < keyLowered.length; i++) {
    hash += keyLowered.charCodeAt(i)
    hash += hash << 10
    hash ^= hash >>> 6
  }

  hash += hash << 3
  hash ^= hash >>> 11
  hash += hash << 15

  return hash >>> 0
}

export const dateFromHash = (objectId: string) =>
  new Date(parseInt(objectId.substring(0, 8), 16) * 1000)

export const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const escapeMarkdown = (text: string) => text.replace(/[.*_+?^${}()|[\\]/g, '\\$&')
