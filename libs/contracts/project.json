{"name": "contracts", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/contracts/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/contracts/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/contracts/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "tags": []}