import { Transform, Type } from 'class-transformer'
import { Session } from '..'
import { IsNumber, IsOptional, Min } from 'class-validator'
import { toNumber } from '../../../transforms/index'

export namespace AdminGetFamilies {
  export const topic = 'admin.getFamilies.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsNumber()
    @Transform(toNumber)
    @Min(0)
    offset?: number

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    familyId?: number
  }
}
