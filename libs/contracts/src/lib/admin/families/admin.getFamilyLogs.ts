import { Transform, Type } from 'class-transformer'
import { Session } from '..'
import { IsDate, IsNumber, IsOptional, IsString, Min } from 'class-validator'
import { toDate, toNumber } from '../../../transforms/index'

export namespace AdminGetFamiliesLogs {
  export const topic = 'admin.getFamilyLogs.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsNumber()
    @Transform(toNumber)
    @Min(0)
    offset?: number

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    familyId?: number

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    accountId?: number

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    actionBy?: number

    @IsString()
    @IsOptional()
    type?: string

    @Transform(toDate)
    @IsDate()
    @IsOptional()
    dateFrom?: Date

    @Transform(toDate)
    @IsDate()
    @IsOptional()
    dateTo?: Date
  }
}
