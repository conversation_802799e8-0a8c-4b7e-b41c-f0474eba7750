import { Type } from 'class-transformer'
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  Matches,
  ValidateNested,
} from 'class-validator'

export namespace Register {
  // TODO: изменить когда перенесем из admin в отдельный app
  export const topic = 'admin.register.query'

  export class QueryData {
    @IsString()
    @IsOptional()
    utm_source?: string

    @IsString()
    @IsOptional()
    utm_medium?: string

    @IsString()
    @IsOptional()
    utm_campaign?: string
  }

  export class Request {
    @IsString()
    @IsNotEmpty()
    serverId!: string

    @IsString()
    @Length(4, 25)
    @Matches(/^[0-9a-zA-Z]+$/, {
      message: 'Only Latin letters and numbers!',
    })
    login!: string

    @IsEmail()
    @Length(5, 40)
    email!: string

    @IsString()
    @Length(6, 32)
    password!: string

    @IsString()
    @IsNotEmpty()
    pageName!: string

    @ValidateNested()
    @IsOptional()
    @Type(() => QueryData)
    queryData?: QueryData

    @IsString()
    @IsNotEmpty()
    validationKey!: string

    @IsString()
    @IsOptional()
    invitedBy?: string | null

    @IsString()
    @IsNotEmpty()
    ip!: string
  }
}
