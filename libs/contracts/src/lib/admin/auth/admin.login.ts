import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator'

export namespace AdminLogin {
  export const topic = 'admin.login.query'

  export class Request {
    @IsString()
    serverId!: string

    @IsString()
    @MinLength(3)
    @MaxLength(20)
    login!: string

    @IsString()
    @MinLength(3)
    @MaxLength(30)
    password!: string

    @IsString()
    @IsIP()
    @IsOptional()
    ip!: string

    @IsString()
    code!: string
  }
}
