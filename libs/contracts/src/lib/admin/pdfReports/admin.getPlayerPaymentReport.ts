import { Type } from 'class-transformer'
import { Session } from '..'
import { IsNotEmpty, IsString } from 'class-validator'

export namespace AdminGetPlayerPaymentReport {
  export const topic = 'admin.getPlayerPaymentReport.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsString()
    @IsNotEmpty()
    serverId!: string

    @IsString()
    @IsNotEmpty()
    lang!: 'ru' | 'en'

    @IsString()
    @IsNotEmpty()
    unitpayId!: string
  }
}
