import { Transform, Type } from 'class-transformer'
import { Session } from '..'
import { toNumber } from '../../../transforms/index'
import { IsNumber, IsOptional } from 'class-validator'

export namespace AdminGetBetboom {
  export const topic = 'admin.getBetboom.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(value => (value ? toNumber(value) : undefined))
    @IsNumber()
    @IsOptional()
    id?: number
  }
}
