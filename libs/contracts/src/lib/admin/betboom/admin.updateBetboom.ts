import { Type } from 'class-transformer'
import { Session } from '..'
import { toNumber } from '../../../transforms/index'
import { IsNotEmpty, IsNumber, IsString } from 'class-validator'

export namespace AdminUpdateBetboom {
  export const topic = 'admin.updateBetboom.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsNumber()
    id!: number

    @IsString()
    @IsNotEmpty()
    score!: string
  }
}
