import { Transform, Type } from 'class-transformer'
import { IsDate, IsN<PERSON>ber, IsOptional, IsString, Min } from 'class-validator'
import { toDate, toNumber } from '../../../transforms'
import { Session } from '..'

export namespace AdminGetItemLogs {
  export const topic = 'admin.getItemLogs.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsNumber()
    @Transform(toNumber)
    @Min(0)
    page!: number

    @IsNumber()
    @Transform(toNumber)
    @Min(0)
    offset!: number

    @IsOptional()
    @Transform(toNumber)
    @IsNumber()
    accountId?: number

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    itemSqlId?: number

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    itemId?: number

    @IsString()
    @IsOptional()
    org?: string

    @Transform(toDate)
    @IsDate()
    @IsOptional()
    dateFrom?: Date

    @Transform(toDate)
    @IsDate()
    @IsOptional()
    dateTo?: Date
  }
}
