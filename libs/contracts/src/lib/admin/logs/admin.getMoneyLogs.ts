import { Transform, Type } from 'class-transformer'
import { IsDate, IsNumber, IsOptional, Min } from 'class-validator'
import { toDate, toNumber } from '../../../transforms'
import { Session } from '..'

export namespace AdminGetMoneyLogs {
  export const topic = 'admin.getMoneyLogs.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsNumber()
    @Transform(toNumber)
    @Min(0)
    page!: number

    @IsNumber()
    @Transform(toNumber)
    @Min(0)
    offset!: number

    @IsOptional()
    @Transform(toNumber)
    @IsNumber()
    accountId?: number

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    bankId?: number

    @Transform(toDate)
    @IsDate()
    @IsOptional()
    dateFrom?: Date

    @Transform(toDate)
    @IsDate()
    @IsOptional()
    dateTo?: Date
  }
}
