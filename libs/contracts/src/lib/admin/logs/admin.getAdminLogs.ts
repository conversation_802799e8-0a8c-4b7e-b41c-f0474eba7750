import { toDate, toNumber } from '../../../transforms'
import { Transform, Type } from 'class-transformer'
import { IsDate, IsNumber, IsOptional, Min } from 'class-validator'
import { Session } from '..'

export namespace AdminGetAdminLogs {
  export const topic = 'admin.getAdminLogs.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsNumber()
    @Transform(toNumber)
    @Min(0)
    page!: number

    @IsNumber()
    @Transform(toNumber)
    @Min(0)
    offset!: number

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    admin?: number

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    static?: number

    @Transform(toDate)
    @IsDate()
    @IsOptional()
    dateFrom?: Date

    @Transform(toDate)
    @IsDate()
    @IsOptional()
    dateTo?: Date
  }
}
