import { toDate, toNumber } from '../../../transforms'
import { Transform, Type } from 'class-transformer'
import { IsDate, IsNumber, IsOptional, IsString, Min } from 'class-validator'
import { Session } from '..'

export namespace AdminGetDeathLogs {
  export const topic = 'admin.getDeathLogs.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsNumber()
    @Transform(toNumber)
    @Min(0)
    page!: number

    @IsNumber()
    @Transform(toNumber)
    @Min(0)
    offset!: number

    @IsString()
    @IsOptional()
    killerLogin?: string

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    killerAccountId?: number

    @IsString()
    @IsOptional()
    weapon?: string

    @IsString()
    @IsOptional()
    killedLogin?: string

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    killedAccountId?: number

    @Transform(toDate)
    @IsDate()
    @IsOptional()
    dateFrom?: Date

    @Transform(toDate)
    @IsDate()
    @IsOptional()
    dateTo?: Date
  }
}
