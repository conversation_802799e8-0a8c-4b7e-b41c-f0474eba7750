import { Transform, Type } from 'class-transformer'
import { Session } from '..'
import { toNumber } from '../../../transforms'
import { IsNumber } from 'class-validator'

export namespace AdminGetOrganisationInfo {
  export const topic = 'admin.getOrganisationInfo.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    orgId!: number
  }
}
