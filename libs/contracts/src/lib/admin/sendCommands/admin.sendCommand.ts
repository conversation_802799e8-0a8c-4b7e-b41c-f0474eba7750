import { Type } from 'class-transformer'
import { Session } from '..'
import { IsNotEmpty, IsNumber, IsString } from 'class-validator'

export namespace AdminSendCommand {
  export const topic = 'admin.sendCommand.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsNumber()
    accountId!: number

    @IsString()
    @IsNotEmpty()
    command!: string

    @IsString()
    @IsNotEmpty()
    login!: string

    @IsString()
    @IsNotEmpty()
    server!: string
  }
}
