import { Transform, Type } from 'class-transformer'
import { Session } from '..'
import { toDate, toNumber } from '../../../transforms/index'
import {
  IsDate,
  IsNumber,
  IsOptional,
  IsString,
  Validate,
  isNumber,
  isString,
} from 'class-validator'

export namespace AdminGetSendCommandsLog {
  export const topic = 'admin.getSendCommandsLog.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    @IsOptional()
    offset?: number

    @IsString()
    @IsOptional()
    serverId?: string

    @Transform(({ value }) => (!isNaN(value) ? Number(value) : value))
    @Validate((val: unknown) => isNumber(val) || isString(val))
    @IsOptional()
    admin?: number | string

    @IsString()
    @IsOptional()
    command?: string

    @IsString()
    @IsOptional()
    args?: string

    @Transform(toDate)
    @IsDate()
    @IsOptional()
    date?: Date
  }
}
