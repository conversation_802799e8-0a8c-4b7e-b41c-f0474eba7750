import { Type, Transform } from 'class-transformer'
import { Session } from '..'
import { IsNumber, Min } from 'class-validator'
import { toNumber } from '../../../transforms'

export namespace AdminGetUser {
  export const topic = 'admin.getUser.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @Min(0)
    @IsNumber()
    accountId!: number
  }
}
