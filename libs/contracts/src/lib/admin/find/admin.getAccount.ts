import { Type, Transform } from 'class-transformer'
import { Session } from '..'
import { Validate, isNumber, isString } from 'class-validator'

export namespace AdminGetAccount {
  export const topic = 'admin.getAccount.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(({ value }) => (!isNaN(value) ? Number(value) : value))
    @Validate((val: unknown) => isNumber(val) || isString(val))
    param!: number | string
  }
}
