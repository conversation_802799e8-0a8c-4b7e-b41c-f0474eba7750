import { Type } from 'class-transformer'
import { Session } from '..'
import { IsString, Validate, isNumber } from 'class-validator'

export namespace AdminUpdateFaq {
  export const topic = 'admin.updateFaq.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Validate(({ value }: { value: unknown }) => isNumber(value) || value === null)
    id!: number | null
    @IsString()
    name!: string
    @IsString()
    text!: string
  }
}
