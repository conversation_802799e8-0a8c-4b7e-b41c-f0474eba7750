import { Type } from 'class-transformer'
import { Session } from '..'
import { IsNotEmpty, IsString } from 'class-validator'

export namespace AdminCreateAdminWhitelist {
  export const topic = 'admin.createAdminWhitelist.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsString()
    @IsNotEmpty()
    socialClub!: string

    @IsNotEmpty()
    @IsString()
    comment!: string
  }
}
