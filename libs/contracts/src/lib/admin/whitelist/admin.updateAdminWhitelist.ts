import { Transform, Type } from 'class-transformer'
import { Session } from '..'
import { IsNotEmpty, IsNumber, IsString } from 'class-validator'
import { toNumber } from '../../../transforms/index'

export namespace AdminUpdateAdminWhitelist {
  export const topic = 'admin.updateAdminWhitelist.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    id!: number

    @IsString()
    @IsNotEmpty()
    socialClub!: string

    @IsNotEmpty()
    @IsString()
    comment!: string
  }
}
