import { Transform, Type } from 'class-transformer'
import { Session } from '..'
import { IsNumber, IsString } from 'class-validator'
import { toNumber } from '../../../transforms/index'

export namespace AdminDeleteAdminWhitelist {
  export const topic = 'admin.deleteAdminWhitelist.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    id!: number

    @IsString()
    socialClub!: string
  }
}
