import { Type } from 'class-transformer'
import { Session } from '..'
import { IsNotEmpty, IsNumber, IsString } from 'class-validator'

export namespace AdminUpdateWhitelist {
  export const topic = 'admin.updateWhitelist.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsNumber()
    id!: number

    @IsNotEmpty()
    @IsString()
    socialClub!: string

    @IsNotEmpty()
    @IsString()
    comment!: string
  }
}
