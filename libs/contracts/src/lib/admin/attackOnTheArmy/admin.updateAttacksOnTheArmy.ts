import { Transform, Type } from 'class-transformer'
import { Session } from '..'
import { IsNumber, IsString } from 'class-validator'
import { toNumber } from '../../../transforms/index'

class Payload {
  @Transform(toNumber)
  @IsNumber()
  rest!: number
  @IsString()
  typeColor!: string
  @Transform(toNumber)
  @IsNumber()
  maxMats!: number
}

export namespace AdminUpdateAttacksOnTheArmy {
  export const topic = 'admin.updateAttacksOnTheArmy.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsNumber()
    id!: number
    @IsNumber()
    isFortZancudo!: number

    @Type(() => Payload)
    payload!: Payload
  }
}
