import { Type } from 'class-transformer'
import { Session } from '..'
import { IsBoolean, IsString } from 'class-validator'

class Payload {
  @IsString()
  days!: string
  @IsString()
  time!: string
}

export namespace AdminUpdateAttackOnTheArmyTime {
  export const topic = 'admin.updateAttackOnTheArmyTime.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsBoolean()
    isFortZancudo!: boolean

    @Type(() => Payload)
    payload!: Payload
  }
}
