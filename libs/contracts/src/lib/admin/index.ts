import { IsN<PERSON>ber, IsOptional, IsString } from 'class-validator'

export class Session {
  @IsNumber()
  id!: number

  @IsString()
  login!: string

  @IsString()
  @IsOptional()
  serverId?: string

  @IsString()
  token!: string

  @IsString()
  ip!: string
}

export * from './user/admin.getUserInfo'

export * from './auth/admin.login'
export * from './auth/admin.logout'
export * from './auth/register'
export * from './auth/confirm-email'
export * from './auth/user.login'

export * from './rights/admin.getRights'
export * from './rights/admin.updateRights'

export * from './find/admin.getAccount'
export * from './find/admin.getAdminList'
export * from './find/admin.getUser'

export * from './reports/admin.getActiveReports'

export * from './organisations/admin.getOrganisationsList'
export * from './organisations/admin.getOrganisationInfo'

export * from './logs/admin.getAdminLogs'
export * from './logs/admin.getFractionLogs'
export * from './logs/admin.getItemLogs'
export * from './logs/admin.getItemTradeLogs'
export * from './logs/admin.getMoneyLogs'
export * from './logs/admin.getDeathLogs'

export * from './attackOnTheArmy/admin.getAttackOnTheArmyResetTime'
export * from './attackOnTheArmy/admin.getAttackOnTheArmyTime'
export * from './attackOnTheArmy/admin.getAttacksOnTheArmy'
export * from './attackOnTheArmy/admin.updateAttackOnTheArmyResetTime'
export * from './attackOnTheArmy/admin.updateAttackOnTheArmyTime'
export * from './attackOnTheArmy/admin.updateAttacksOnTheArmy'

export * from './plantWeedTime/admin.getPlantWeedTime'
export * from './plantWeedTime/admin.updatePlantWeedTime'

export * from './families/admin.getFamilies'
export * from './families/admin.getFamilyLogs'
export * from './families/admin.getFamilyPlayers'

export * from './rules/admin.getRules'
export * from './rules/admin.updateRules'
export * from './rules/admin.deleteRule'

export * from './faq/admin.getFaq'
export * from './faq/admin.updateFaq'
export * from './faq/admin.deleteFaq'

export * from './whitelist/admin.getWhitelist'
export * from './whitelist/admin.createWhitelist'
export * from './whitelist/admin.updateWhitelist'
export * from './whitelist/admin.deleteWhitelist'
export * from './whitelist/admin.getAdminWhitelist'
export * from './whitelist/admin.createAdminWhitelist'
export * from './whitelist/admin.updateAdminWhitelist'
export * from './whitelist/admin.deleteAdminWhitelist'

export * from './betboom/admin.getBetboom'
export * from './betboom/admin.updateBetboom'

export * from './pdfReports/admin.getPlayerPaymentReport'

export * from './sendCommands/admin.getSendCommandsLog'
export * from './sendCommands/admin.sendCommand'

export * from './twitch'

export * from './minigames/minigames.getStatuses'
export * from './minigames/minigames.updateStatus'
