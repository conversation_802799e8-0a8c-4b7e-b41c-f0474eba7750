import { Type } from 'class-transformer'
import { Session } from '..'
import { IsBoolean, IsIn } from 'class-validator'
import { mini_games_name } from '@majestic-backend/prisma-client-backend'

export namespace MinigamesUpdateStatus {
  export const topic = 'minigames.updateStatus.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsIn(Object.values(mini_games_name))
    minigameName!: mini_games_name

    @IsBoolean()
    isActive!: boolean
  }
}
