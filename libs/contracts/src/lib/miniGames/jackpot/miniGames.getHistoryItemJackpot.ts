import { Transform, Type } from 'class-transformer'
import { Session } from '../../../models/session'
import { toNumber } from '../../../transforms'
import { IsNumber } from 'class-validator'

export namespace MiniGamesGetHistoryItemJackpot {
  export const topic = 'minigames.getHistoryItemJackpot.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @IsNumber()
    gameId!: number
  }
}
