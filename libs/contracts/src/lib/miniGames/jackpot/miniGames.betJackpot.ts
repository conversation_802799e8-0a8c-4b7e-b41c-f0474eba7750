import { Transform, Type } from 'class-transformer'
import { Session } from '../../../models/session'
import { toNumber } from '../../../transforms'
import { IsNumber, IsPositive } from 'class-validator'

export namespace MiniGamesBetJackpot {
  export const topic = 'minigames.betJackpot.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @Transform(({ value }) => parseInt(value))
    @IsNumber()
    @IsPositive()
    betAmount!: number
  }
}
