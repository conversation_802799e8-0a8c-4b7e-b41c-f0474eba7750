import { Transform, Type } from 'class-transformer'
import { Session } from '../../../models/session'
import { toNumber } from '../../../transforms'
import { IsIn, IsNumber, IsPositive, IsString } from 'class-validator'
import { WheelColors } from '@majestic-backend/types'

export namespace MiniGamesBetWheel {
  export const topic = 'minigames.betWheel.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @Transform(({ value }) => parseInt(value))
    @IsNumber()
    @IsPositive()
    betAmount!: number

    @IsString()
    @IsIn(Object.values(WheelColors))
    color!: WheelColors
  }
}
