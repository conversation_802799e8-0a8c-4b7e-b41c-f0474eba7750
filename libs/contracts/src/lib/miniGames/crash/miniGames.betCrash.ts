import { Transform, Type } from 'class-transformer'
import { Session } from '../../../models/session'
import { toNumber } from '../../../transforms'
import { IsNumber, IsOptional, IsPositive } from 'class-validator'

export namespace MiniGamesBetCrash {
  export const topic = 'minigames.betCrash.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @Transform(({ value }) => parseInt(value))
    @IsNumber()
    @IsPositive()
    betAmount!: number

    @Transform(({ value }) => parseFloat(value))
    @IsNumber()
    @IsOptional()
    autoTakeX?: number
  }
}
