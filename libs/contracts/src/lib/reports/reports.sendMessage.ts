import { Transform, Type } from 'class-transformer'
import { Session } from '../../models/session'
import { IsArray, IsNumber, IsOptional, IsString } from 'class-validator'
import { toArray, toNumber } from '../../transforms'
import { ISerializedFile } from '@majestic-backend/types'

export namespace ReportsSendMessage {
  export const topic = 'reports.sendMessage.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    reportId!: number

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @IsString()
    socialClub!: string

    @IsString()
    message!: string

    @Transform(toArray)
    @IsOptional()
    @IsArray()
    files?: ISerializedFile[]

    @Transform(toArray)
    @IsOptional()
    @IsString({ each: true })
    imagesUrls?: string[]
  }
}
