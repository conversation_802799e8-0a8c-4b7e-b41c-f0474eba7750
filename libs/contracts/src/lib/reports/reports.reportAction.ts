import { Transform, Type } from 'class-transformer'
import { Session } from '../../models/session'
import { IsNumber, IsString } from 'class-validator'
import { toNumber } from '../../transforms'

export namespace ReportsReportAction {
  export const topic = 'reports.reportAction.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    reportId!: number

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @IsString()
    socialClub!: string

    @IsString()
    message!: string
  }
}
