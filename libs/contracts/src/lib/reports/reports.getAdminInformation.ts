import { Transform, Type } from 'class-transformer'
import { Session } from '../../models/session'
import { IsNumber } from 'class-validator'
import { toNumber } from '../../transforms'

export namespace ReportsGetAdminInformation {
  export const topic = 'reports.getAdminInformation.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number
  }
}
