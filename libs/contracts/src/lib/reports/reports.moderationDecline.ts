import { Transform, Type } from 'class-transformer'
import { Session } from '../../models/session'
import { toNumber } from '../../transforms'
import { IsNumber } from 'class-validator'

export namespace ReportsModerationDecline {
  export const topic = 'reports.moderationDecline.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @Transform(toNumber)
    @IsNumber()
    reportId!: number
  }
}
