import { Transform, Type } from 'class-transformer'
import { Session } from '../../models/session'
import { IsNumber, IsString } from 'class-validator'
import { toNumber } from '../../transforms'

export namespace ReportsCreateReport {
  export const topic = 'reports.createReport.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @IsString()
    socialClub!: string

    @IsString()
    text!: string

    @IsString()
    type!: 'default' | 'billboard' | 'family_logo' | 'gov_news'
  }
}
