import { Transform, Type } from 'class-transformer'
import { Session } from '../../models/session'
import { IsNumber } from 'class-validator'
import { toNumber } from '../../transforms'

export namespace ReportsGetReport {
  export const topic = 'reports.getReport.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @Transform(toNumber)
    @IsNumber()
    reportId!: number
  }
}
