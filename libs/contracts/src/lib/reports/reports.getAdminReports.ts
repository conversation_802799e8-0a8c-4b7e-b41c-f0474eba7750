import { Transform, Type } from 'class-transformer'
import { Session } from '../../models/session'
import { IsNumber } from 'class-validator'
import { toNumber } from '../../transforms'

export namespace ReportsGetAdminReports {
  export const topic = 'reports.getAdminReports.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number
  }
}
