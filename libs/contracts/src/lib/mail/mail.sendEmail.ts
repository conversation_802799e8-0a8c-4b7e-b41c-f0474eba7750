import { IsEmail, <PERSON><PERSON><PERSON>, <PERSON>O<PERSON>, <PERSON>Optional, IsString } from 'class-validator'
import { IMailTemplateVariables, MAIL_TEMPLATES, ALLOWED_LANGS } from '@shared_config/mail'

export namespace MailSendEmail {
  export const topic = 'mail.sendEmail.query'

  export class Request<T extends MAIL_TEMPLATES = MAIL_TEMPLATES> {
    @IsEnum(MAIL_TEMPLATES)
    template!: T

    @IsOptional()
    @IsEmail()
    from?: string

    @IsObject()
    variables!: IMailTemplateVariables[T]

    @IsEmail()
    to!: string

    @IsEnum(ALLOWED_LANGS)
    lang!: ALLOWED_LANGS

    @IsString()
    @IsOptional()
    subject?: string
  }
}
