import { IsObject, IsOptional, IsString } from 'class-validator'
import { PaymentCreateRequest } from '../../../models'

export namespace PaymentsCreateMultihub {
  export const topic = 'payments.createMultihub.query'

  export class Request extends PaymentCreateRequest {
    @IsString()
    email!: string

    @IsString()
    firstName!: string

    @IsString()
    document!: string

    @IsObject()
    @IsOptional()
    args?: Record<string, any> | undefined
  }
}
