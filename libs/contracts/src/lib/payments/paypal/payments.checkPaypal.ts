import { Expose } from 'class-transformer'
import { IsOptional, IsString } from 'class-validator'

export namespace PaymentsCheckPaypal {
  export const topic = 'payments.checkPaypal.query'

  export class Request {
    @IsString()
    paymentId!: string

    @IsString()
    @Expose({ name: 'PayerID' }) 
    payerId!: string

    @IsString()
    serverId!: string

    @IsString()
    @IsOptional()
    token?: string
  }
}
