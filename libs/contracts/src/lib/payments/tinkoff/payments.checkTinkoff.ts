import { Type } from 'class-transformer'
import { IsDefined, ValidateNested } from 'class-validator'
import { CheckTinkoffParamsRequest } from '../../../models'

export namespace PaymentsCheckTinkoff {
  export const topic = 'payments.checkTinkoff.query'

  export class Request {
    @IsDefined()
    @ValidateNested()
    @Type(() => CheckTinkoffParamsRequest)
    params!: CheckTinkoffParamsRequest
  }
}
