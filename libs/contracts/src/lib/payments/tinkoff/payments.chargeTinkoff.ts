import { Transform } from 'class-transformer'
import { IsNotEmpty, IsOptional, IsString, Min } from 'class-validator'

export namespace PaymentsChargeTinkoff {
  export const topic = 'payments.chargeTinkoff.query'

  export class Request {
    @IsString()
    @IsNotEmpty()
    serverId!: string

    @IsString()
    @IsOptional()
    login?: string

    @Transform(({ value }) => parseInt(value))
    @Min(0)
    amount!: number

    @IsString()
    @IsOptional()
    method?: string
  }
}
