import { IsIn, IsObject, IsOptional, IsString } from 'class-validator'
import { PaymentCreateRequest } from '../../../models'
import { Methods, OtherMethods } from '@majestic-backend/types'

export namespace PaymentsCreateTinkoff {
  export const topic = 'payments.createTinkoff.query'

  export class Request extends PaymentCreateRequest {
    @IsString()
    @IsOptional()
    email?: string

    @IsString()
    @IsIn([...Object.values(Methods), ...Object.values(OtherMethods)])
    method!: Methods | OtherMethods

    @IsObject()
    @IsOptional()
    args?: Record<string, any>

    @IsString()
    @IsOptional()
    firstName?: string

    @IsString()
    @IsOptional()
    document?: string
  }
}
