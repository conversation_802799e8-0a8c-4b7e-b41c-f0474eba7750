import { IsIn, <PERSON>NotEmpty, IsObject, IsOptional, IsString } from 'class-validator'
import { Methods, OtherMethods } from '@majestic-backend/types'
import { unitpay_payments_source } from '@majestic-backend/prisma-client-game'

export namespace PaymentsRecurrentTinkoff {
  export const topic = 'payments.recurrentTinkoff.query'

  export class Request {
    @IsString()
    @IsNotEmpty()
    serverId!: string

    @IsString()
    @IsNotEmpty()
    login!: string

    source: unitpay_payments_source = unitpay_payments_source.premiumSubscription

    @IsString()
    @IsIn([...Object.values(Methods), ...Object.values(OtherMethods)])
    method!: Methods | OtherMethods

    @IsObject()
    @IsOptional()
    args?: Record<string, any>
  }
}
