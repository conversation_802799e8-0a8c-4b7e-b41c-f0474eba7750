import { IsDefined, IsIn, IsString, ValidateNested } from 'class-validator'
import { CheckPgsParamsRequest } from '../../../models'
import { Type } from 'class-transformer'

export namespace PaymentsCheckPgs {
  export const topic = 'payments.checkPgs.query'

  export class Request {
    @IsString()
    @IsIn(['ru', 'pl', 'de', 'br'])
    shop!: string

    @IsDefined()
    @ValidateNested()
    @Type(() => CheckPgsParamsRequest)
    params!: CheckPgsParamsRequest
  }
}
