import { IsIn, IsObject, IsString } from 'class-validator'
import { PaymentCreateRequest } from '../../../models'
import { Methods, OtherMethods } from '@majestic-backend/types'

export namespace PaymentsCreateBrazil {
  export const topic = 'payments.createBrazil.query'

  export class Request extends PaymentCreateRequest {
    @IsString()
    email!: string

    @IsString()
    @IsIn([...Object.values(Methods), ...Object.values(OtherMethods)])
    method!: Methods | OtherMethods

    @IsObject()
    args!: Record<string, any>

    @IsString()
    firstName!: string

    @IsString()
    document!: string
  }
}
