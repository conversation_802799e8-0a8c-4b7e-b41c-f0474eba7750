import { Type } from 'class-transformer'
import { IsDefined, ValidateNested } from 'class-validator'
import { CheckPaypalychParamsRequest } from '../../../models'

export namespace PaymentsCheckPaypalych {
  export const topic = 'payments.checkPaypalych.query'

  export class Request {
    @IsDefined()
    @ValidateNested()
    @Type(() => CheckPaypalychParamsRequest)
    params!: CheckPaypalychParamsRequest
  }
}
