import { Methods, OtherMethods } from '@majestic-backend/types'
import { IsEmail, IsIn, IsNotEmpty, IsOptional, IsString } from 'class-validator'

export namespace PaymentsBuyKeys {
  export const topic = 'payments.buyKeys.query'

  export class Request {
    @IsString()
    @IsNotEmpty()
    @IsEmail()
    email!: string

    @IsString()
    @IsIn([...Object.values(Methods), ...Object.values(OtherMethods)])
    method!: Methods | OtherMethods

    @IsString()
    @IsOptional()
    mediaReferer?: string | null

    @IsString()
    @IsOptional()
    referer?: string | null

    @IsString()
    ip!: string

    @IsString()
    @IsOptional()
    confirmationCode!: string
  }
}
