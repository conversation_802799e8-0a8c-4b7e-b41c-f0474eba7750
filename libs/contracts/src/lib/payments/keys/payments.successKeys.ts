import { Transform } from 'class-transformer'
import { IsString, IsNumber } from 'class-validator'

export namespace PaymentsSuccessKeys {
  export const topic = 'payments.successKeys.query'

  export class Request {
    @IsString()
    status!: string

    @IsString()
    paymentId!: string

    @IsNumber()
    @Transform(({ value }) => parseInt(value))
    sysPaymentId!: number

    @IsString()
    description!: string

    @IsString()
    sign!: string
  }
}
