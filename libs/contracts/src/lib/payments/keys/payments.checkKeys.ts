import { Currencies } from '@majestic-backend/types'
import { Transform } from 'class-transformer'
import { IsBoolean, IsIn, IsNumber, IsOptional, IsString, Min } from 'class-validator'

export namespace PaymentsCheckKeys {
  export const topic = 'payments.checkKeys.query'

  export class Request {
    @IsString()
    status!: string

    @IsString()
    paymentId!: string

    @IsNumber()
    sysPaymentId!: number

    @IsNumber()
    storeId!: number

    @Transform(({ value }) => parseFloat(parseFloat(value).toFixed(2)))
    @Min(0)
    amount!: number

    @Transform(({ value }) => parseFloat(parseFloat(value).toFixed(2)))
    @Min(0)
    incomeAmount!: number

    @IsString()
    description!: string

    @IsString()
    @IsIn(Object.values(Currencies))
    currency!: Currencies

    @IsString()
    additionalInformation!: string

    @IsString()
    sign!: string

    @IsBoolean()
    @IsOptional()
    amountChanged?: boolean

    @Transform(({ value }) => parseFloat(parseFloat(value).toFixed(2)))
    @IsNumber()
    @IsOptional()
    userAmountMultiplier?: number
  }
}
