import { Transform, Type } from 'class-transformer'
import { Session } from '../../models/session'
import { IsArray, IsNumber } from 'class-validator'
import { toNumber } from '../../transforms'
import { ISerializedFile } from '@majestic-backend/types'

export namespace UploadsUploadMarketplaceImages {
  export const topic = 'uploads.uploadMarketplaceImages.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @IsArray()
    files!: ISerializedFile[]
  }
}
