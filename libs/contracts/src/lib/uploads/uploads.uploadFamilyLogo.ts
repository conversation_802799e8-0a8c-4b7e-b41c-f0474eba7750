import { Transform, Type } from 'class-transformer'
import { Session } from '../../models/session'
import { IsNumber, IsString } from 'class-validator'
import { oneFile, toNumber } from '../../transforms'
import { ISerializedFile } from '@majestic-backend/types'

export namespace UploadsUploadFamilyLogo {
  export const topic = 'uploads.uploadFamilyLogo.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @IsString()
    logoToken!: string

    @Transform(oneFile)
    logo!: ISerializedFile
  }
}
