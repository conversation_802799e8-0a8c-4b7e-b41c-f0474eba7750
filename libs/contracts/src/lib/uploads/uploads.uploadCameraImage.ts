import { Transform, Type } from 'class-transformer'
import { Session } from '../../models/session'
import { IsNumber, IsString } from 'class-validator'
import { toNumber } from '../../transforms'

export namespace UploadsUploadCameraImage {
  export const topic = 'uploads.uploadCameraImage.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @IsString()
    dataURL!: string
  }
}
