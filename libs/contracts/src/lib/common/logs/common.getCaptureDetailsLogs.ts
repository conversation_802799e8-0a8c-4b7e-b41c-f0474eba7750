import { Transform, Type } from 'class-transformer'
import { Session } from '../../../models/session'
import { IsNumber, IsString } from 'class-validator'
import { toNumber } from '../../../transforms'

export namespace CommonGetCaptureDetailsLogs {
  export const topic = 'common.getCaptureDetailsLogs.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsString()
    serverId!: string

    @Transform(toNumber)
    @IsNumber()
    captureId!: number

    @Transform(toNumber)
    @IsNumber()
    family!: number
  }
}
