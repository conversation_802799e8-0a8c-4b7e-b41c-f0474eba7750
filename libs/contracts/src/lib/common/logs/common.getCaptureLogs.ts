import { Transform, Type } from 'class-transformer'
import { Session } from '../../../models/session'
import { IsNumber, IsString } from 'class-validator'
import { toNumber } from '../../../transforms'

export namespace CommonGetCaptureLogs {
  export const topic = 'common.getCaptureLogs.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @IsString()
    serverId!: string

    @Transform(toNumber)
    @IsNumber()
    factionId!: number

    @Transform(toNumber)
    @IsNumber()
    family!: number
  }
}
