import { Transform, Type } from 'class-transformer'
import { IsNumber } from 'class-validator'
import { toNumber } from '../../../transforms'
import { Session } from '../../../models/session'

export namespace CommonSetPixelColor {
  export const topic = 'common.setPixelColor.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number

    @IsNumber()
    x!: number

    @IsNumber()
    y!: number

    @IsNumber()
    colorIdx!: number
  }
}
