import { Transform, Type } from 'class-transformer'
import { IsNumber } from 'class-validator'
import { toNumber } from '../../../transforms'
import { Session } from '../../../models/session'

export namespace CommonClosePixelBattle {
  export const topic = 'common.closePixelBattle.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number
  }
}
