import { Transform, Type } from 'class-transformer'
import { IsNumber } from 'class-validator'
import { toNumber } from '../../../transforms'
import { Session } from '../../../models/session'

export namespace CommonOpenPixelBattle {
  export const topic = 'common.openPixelBattle.query'

  export class Request {
    @Type(() => Session)
    session!: Session

    @Transform(toNumber)
    @IsNumber()
    characterId!: number
  }
}
