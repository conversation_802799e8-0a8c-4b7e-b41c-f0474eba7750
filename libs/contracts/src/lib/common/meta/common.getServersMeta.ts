import { Transform } from 'class-transformer'
import { IsBoolean, IsOptional, IsString } from 'class-validator'
import { toBoolean } from '../../../transforms'

export namespace CommonGetServersMeta {
  export const topic = 'common.getServersMeta.query'

  export class Request {
    @IsString()
    @IsOptional()
    serverId?: string

    @IsString()
    @IsOptional()
    region?: string

    @IsBoolean()
    @IsOptional()
    @Transform(toBoolean)
    isTest?: boolean
  }
}
