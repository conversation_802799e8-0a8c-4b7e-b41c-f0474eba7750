import { Transform } from 'class-transformer'
import { IsDate, IsNotEmpty } from 'class-validator'
import { toDate } from '../../../transforms/index'

export namespace CommonGetUniqueUsers {
  export const topic = 'common.getUniqueUsers.query'

  export class Request {
    @Transform(toDate)
    @IsNotEmpty()
    @IsDate()
    startDate?: Date

    @Transform(toDate)
    @IsNotEmpty()
    @IsDate()
    endDate?: Date
  }
}
