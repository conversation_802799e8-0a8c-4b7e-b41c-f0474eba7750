import { unitpay_payments_source } from '@majestic-backend/prisma-client-game'
import { Transform } from 'class-transformer'
import { IsNotEmpty, IsOptional, IsString, Min } from 'class-validator'

export class PaymentCreateRequest {
  @IsString()
  @IsNotEmpty()
  serverId!: string

  @IsString()
  @IsOptional()
  login?: string

  @Transform(({ value }) => parseInt(value))
  @Min(0)
  amount!: number

  source: unitpay_payments_source = unitpay_payments_source.site
}
