import { Transform } from 'class-transformer'
import { IsBoolean, IsObject, IsOptional, IsString, Min } from 'class-validator'

export class CheckTinkoffParamsRequest {
  @IsBoolean()
  @IsOptional()
  Success?: boolean

  @IsString()
  Status!: string

  @IsString()
  @IsOptional()
  TerminalKey?: string

  @IsString()
  @IsOptional()
  Token?: string

  @IsString()
  @IsOptional()
  OrderId?: string

  @IsString()
  @IsOptional()
  RequestKey?: string

  @IsString()
  Pan!: string

  @Transform(({ value }) => value.toString())
  @IsOptional()
  RebillId?: string

  @IsString()
  @IsOptional()
  AccountToken?: string

  @Transform(({ value }) => parseInt(value))
  @Min(0)
  @IsOptional()
  Amount?: number

  @IsObject()
  Data!: Record<string, any>

  @IsString()
  @IsOptional()
  Message?: string

  @IsString()
  @IsOptional()
  ErrorCode?: string
}
