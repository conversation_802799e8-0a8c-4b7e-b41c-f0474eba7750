import { PaymentStatus } from '@majestic-backend/types'
import { Transform } from 'class-transformer'
import { IsEnum, IsNumber, IsString, Min } from 'class-validator'
import { toNumber } from '../transforms'

export class CheckPaypalychParamsRequest {
  @IsEnum(PaymentStatus)
  Status!: PaymentStatus

  @Transform(toNumber)
  @IsNumber()
  @Min(0)
  OutSum!: number

  @IsString()
  InvId!: string

  @IsString()
  SignatureValue!: string

  @IsString()
  TrsId!: string

  @Transform(toNumber)
  @IsNumber()
  @Min(0)
  Commission!: number

  @IsString()
  custom!: string
}
