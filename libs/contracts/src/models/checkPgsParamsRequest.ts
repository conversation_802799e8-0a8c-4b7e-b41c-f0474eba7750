import { Currencies } from '@majestic-backend/types'
import { Transform } from 'class-transformer'
import { IsIn, IsN<PERSON>ber, IsOptional, IsString, Min } from 'class-validator'
import { toNumber } from '../transforms'

export class CheckPgsParamsRequest {
  @IsString()
  status!: string

  @Transform(toNumber)
  @IsNumber()
  @Min(0)
  amount!: number

  @Transform(toNumber)
  @IsNumber()
  @Min(0)
  incomeAmount!: number

  @IsString()
  sign!: string

  @IsString()
  additionalInformation!: string

  @IsString()
  paymentId!: string

  @IsNumber()
  sysPaymentId!: number

  @Transform(toNumber)
  @IsNumber()
  @IsOptional()
  userAmountMultiplier?: number

  @IsNumber()
  storeId!: number

  @IsString()
  description!: string

  @IsString()
  @IsIn(Object.values(Currencies))
  currency!: Currencies
}
