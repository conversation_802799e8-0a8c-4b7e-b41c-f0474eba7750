import { ISerializedFile } from '@majestic-backend/types'
import moment from 'moment-timezone'

export const toNumber = ({ value }: { value: unknown }) => {
  if (value === '') {
    return
  }
  return Number(value)
}

export const toDate = ({ value }: { value: unknown }) => {
  if (value === '') {
    return
  }

  return moment(value as string).toDate()
}

export const toArray = ({ value }: { value: unknown }) => {
  if (!value) {
    return value
  }

  if (!Array.isArray(value)) {
    return [value]
  }

  return value
}

export const oneFile = ({ value }: { value: ISerializedFile[] }) => {
  if (!Array.isArray(value) || !value.length) {
    return
  }
  return value[0]
}

export const toBoolean = ({ value }: { value: unknown }) => {
  if (typeof value === 'boolean') {
    return value
  }

  if (typeof value === 'string') {
    return value === 'true' || value === '1'
  }

  if (typeof value === 'number') {
    return value > 0
  }

  return false
}
