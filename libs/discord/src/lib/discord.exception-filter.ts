import { Catch, ExceptionFilter } from '@nestjs/common'
import { Interaction } from 'discord.js'
import { NecordArgumentsHost } from 'necord'

import { DiscordException } from './discord.exception'

@Catch(DiscordException)
export class DiscordExceptionFilter implements ExceptionFilter<DiscordException> {
  public async catch(exception: DiscordException, host: NecordArgumentsHost) {
    const [interaction] = host.getArgByIndex<[Interaction]>(0)
    if (!interaction.isRepliable()) {
      return
    }

    const text = `⚠️ Ошибка: ${exception.message}`
    if (interaction.deferred) {
      await interaction.editReply(text)
    } else if (interaction.replied) {
      await interaction.followUp(text)
    } else {
      await interaction.reply(text)
    }
  }
}
