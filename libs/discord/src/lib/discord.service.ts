import { Injectable } from '@nestjs/common'
import { Client, Message, PartialTextBasedChannelFields } from 'discord.js'

@Injectable()
export class DiscordService {
  constructor(private readonly client: Client) {}

  public async healthcheck() {
    return {
      ok: this.client.isReady(),
    }
  }

  public async sendMessage(
    channelId: string,
    message: Parameters<PartialTextBasedChannelFields['send']>[0],
  ) {
    const channel = await this.getChannel(channelId)
    if (!channel.isSendable()) {
      throw new Error(`Channel ${channel.name} is not sendable`)
    }
    return channel.send(message)
  }

  public async getMessage(channelId: string, messageId: string) {
    const channel = await this.getChannel(channelId)

    const message = channel.messages.fetch(messageId)
    if (!message) {
      throw new Error(`Message with ID ${messageId} not found`)
    }

    return message
  }

  public async editMessage(
    channelId: string,
    messageId: string,
    message: Parameters<Message['edit']>[0],
  ) {
    const messageToEdit = await this.getMessage(channelId, messageId)
    return messageToEdit.edit(message)
  }

  private async getChannel(channelId: string) {
    const channel = await this.client.channels.fetch(channelId)
    if (!channel) {
      throw new Error(`Channel with ID ${channelId} not found`)
    }

    if (!channel.isTextBased()) {
      throw new Error(`Channel with ID ${channelId} is not a text channel`)
    }

    return channel
  }
}
