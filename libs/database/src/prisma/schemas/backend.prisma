generator client {
  provider        = "prisma-client-js"
  output          = "./prisma-client-backend/src/generated"
  previewFeatures = ["metrics"]
}

generator local {
  provider        = "prisma-client-js"
  output          = "../../../../prisma-client-backend/src/generated"
  previewFeatures = ["metrics"]
}

generator json {
  provider = "prisma-json-types-generator"
}

datasource db {
  provider = "mysql"
  url      = "mysql://root@database:3306/majestic_rp"
}

// checked
model RegisterCodeSlug {
  id           Int      @id @default(autoincrement())
  slug         String?  @db.VarChar(255)
  serverId     String?  @db.VarChar(6)
  registerCode String?  @db.VarChar(255)
  createdAt    DateTime @db.Date
  updatedAt    DateTime @db.Date

  @@map("RegisterCodeSlugs")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model anticheat {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  server     Int?      @db.SmallInt
  code       Int?      @db.UnsignedSmallInt
  socialClub String?   @db.VarChar(16)
  hwid       String?   @db.VarChar(128)
  date       DateTime? @db.DateTime(0)

  @@index([code], map: "code")
  @@index([hwid], map: "hwid")
  @@index([server], map: "server")
  @@index([socialClub], map: "socialClub")
}

model betboom_logos {
  id  Int     @id
  src String? @db.VarChar(150)
}

// checked
model BkFixture {
  id             Int                @id @default(autoincrement()) @db.UnsignedInt
  fixtureId      Int?               @unique(map: "fixtureId") @db.UnsignedInt
  sport          bk_fixtures_sport? @default(football)
  fixtureDate    DateTime?          @db.DateTime(0)
  timestamp      Int?
  leagueName     String?            @db.VarChar(50)
  leagueCountry  String?            @db.VarChar(50)
  flag           String?            @db.VarChar(60)
  team1          String?            @db.VarChar(50)
  team2          String?            @db.VarChar(50)
  halftimeScore1 String?            @db.VarChar(50)
  halftimeScore2 String?            @db.VarChar(50)
  fulltimeScore1 String?            @db.VarChar(50)
  fulltimeScore2 String?            @db.VarChar(50)
  winHome        String?            @default("1.0") @db.VarChar(5)
  winDraw        String?            @default("1.0") @db.VarChar(5)
  winAway        String?            @default("1.0") @db.VarChar(5)
  half1Home      String?            @default("1.0") @db.VarChar(5)
  half1Draw      String?            @default("1.0") @db.VarChar(5)
  half1Away      String?            @default("1.0") @db.VarChar(5)
  half2Home      String?            @default("1.0") @db.VarChar(5)
  half2Draw      String?            @default("1.0") @db.VarChar(5)
  half2Away      String?            @default("1.0") @db.VarChar(5)
  tm05           String?            @default("1.0") @db.VarChar(5)
  tb05           String?            @default("1.0") @db.VarChar(5)
  tm15           String?            @default("1.0") @db.VarChar(5)
  tb15           String?            @default("1.0") @db.VarChar(5)
  tm25           String?            @default("1.0") @db.VarChar(5)
  tb25           String?            @default("1.0") @db.VarChar(5)
  tm35           String?            @default("1.0") @db.VarChar(5)
  tb35           String?            @default("1.0") @db.VarChar(5)
  bothYes        String?            @default("1.0") @db.VarChar(5)
  bothNo         String?            @default("1.0") @db.VarChar(5)
  bothYes1       String?            @default("1.0") @db.VarChar(5)
  bothNo1        String?            @default("1.0") @db.VarChar(5)
  bothYes2       String?            @default("1.0") @db.VarChar(5)
  bothNo2        String?            @default("1.0") @db.VarChar(5)
  updatedAt      DateTime?          @db.DateTime(0)

  @@index([fixtureDate], map: "fixtureDate")
  @@index([fulltimeScore1], map: "fulltimeScore1")
  @@index([fulltimeScore2], map: "fulltimeScore2")
  @@index([halftimeScore1], map: "halftimeScore1")
  @@index([halftimeScore2], map: "halftimeScore2")
  @@index([leagueCountry], map: "leagueCountry")
  @@index([leagueName], map: "leagueName")
  @@index([sport], map: "sport")
  @@index([timestamp], map: "timestamp")
  @@index([updatedAt], map: "updatedAt")
  @@map("bk_fixtures")
}

// checked
model BkFixtureBetboom {
  id            Int       @id
  timestamp     Int?
  category      String?   @db.VarChar(50)
  categoryRu    String?   @db.VarChar(50)
  subCategory   String?   @db.VarChar(50)
  subCategoryRu String?   @db.VarChar(50)
  tournament    String?   @db.VarChar(100)
  tournamentRu  String?   @db.VarChar(100)
  homeId        Int?
  home          String?   @db.VarChar(100)
  homeRu        String?   @db.VarChar(100)
  awayId        Int?
  away          String?   @db.VarChar(100)
  awayRu        String?   @db.VarChar(100)
  odds          String?   @db.LongText
  score         String?   @db.LongText
  updatedAt     DateTime? @db.DateTime(0)

  @@index([awayId], map: "awayId")
  @@index([categoryRu], map: "categoryRu")
  @@index([homeId], map: "homeId")
  @@index([category], map: "sport")
  @@index([subCategory], map: "subCategory")
  @@index([subCategoryRu], map: "subCategoryRu")
  @@index([timestamp], map: "timestamp")
  @@index([tournament], map: "tournament")
  @@index([tournamentRu], map: "tournamentRu")
  @@index([updatedAt], map: "updatedAt")
  @@map("bk_fixtures_betboom")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model bonuscodes {
  id            Int       @id @default(autoincrement())
  code          String?   @unique(map: "code") @db.VarChar(25)
  used          Int?      @default(0) @db.UnsignedSmallInt
  maxUsed       Int?      @db.UnsignedSmallInt
  maxLevel      Boolean?
  regBefore     DateTime? @db.DateTime(0)
  regAfter      DateTime? @db.DateTime(0)
  server        String?   @db.VarChar(6)
  serverExclude String?   @db.VarChar(6)
  region        String?   @default("RU") @db.VarChar(4)
  start         DateTime? @db.DateTime(0)
  end           DateTime? @db.DateTime(0)
  money         Int?      @db.UnsignedMediumInt
  seasonPassExp Int?      @db.UnsignedMediumInt
  coins         Int?      @db.UnsignedMediumInt
  vipLevel      Int?      @db.UnsignedTinyInt
  vipDays       Int?      @db.UnsignedTinyInt
  gunzone3      Int?      @db.UnsignedTinyInt
  kinguin       Boolean?
  createdAt     DateTime? @db.DateTime(0)

  @@index([createdAt], map: "createdAt")
  @@index([end], map: "end")
  @@index([region], map: "region")
  @@index([server], map: "server")
  @@index([start], map: "start")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model bonuscodes_used {
  server    String?   @db.VarChar(6)
  accountId Int?      @db.UnsignedMediumInt
  userId    Int?      @db.UnsignedMediumInt
  code      String?   @db.VarChar(25)
  date      DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([code], map: "code")
  @@index([date], map: "date")
  @@index([server], map: "server")
  @@index([userId], map: "userId")
  @@ignore
}

model CrashBets {
  id         Int      @id @default(autoincrement())
  userId     Int
  accountId  Int
  bet        Int
  exitX      Float?   @db.Float
  gameId     Int
  dateBet    DateTime @default(now()) @db.DateTime(0)
  socialClub String?  @db.VarChar(16)
  serverId   String?  @db.VarChar(24)
  login      String?  @db.VarChar(20)
  gender     Boolean?

  @@index([dateBet], map: "dateBet")
  @@index([gameId], map: "gameId")
  @@index([userId], map: "userId")
  @@map("crash_bets")
}

model CrashGames {
  id           Int      @id @default(autoincrement())
  playersCount Int
  crashedAt    Float    @db.Float
  bank         Int
  won          Int
  lost         Int
  dateGame     DateTime @default(now()) @db.DateTime(0)

  @@index([dateGame], map: "dateGame")
  @@map("crash_games")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model dictionary_animations {
  id    Int?
  title String? @db.VarChar(20)

  @@index([id], map: "id")
  @@index([title], map: "title")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model dictionary_clothes {
  component Int?    @db.UnsignedTinyInt
  drawable  Int?    @db.UnsignedSmallInt
  texture   Int?    @db.UnsignedTinyInt
  isProp    Int?    @db.UnsignedTinyInt
  gender    Int?    @db.UnsignedTinyInt
  title     String? @db.VarChar(150)

  @@index([component], map: "component")
  @@index([drawable], map: "drawable")
  @@index([gender], map: "gender")
  @@index([isProp], map: "isProp")
  @@index([texture], map: "texture")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model dictionary_vehicles {
  model String? @db.VarChar(20)
  title String? @db.VarChar(50)

  @@index([model], map: "model")
  @@index([title], map: "title")
  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model Faq {
  id     Int       @id @default(autoincrement())
  server String?   @db.VarChar(3)
  text   String?   @db.LongText
  name   String?   @db.VarChar(50)
  author String?   @db.VarChar(50)
  date   DateTime? @db.DateTime(0)

  @@map("faq")
}

model GtaKeys {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  serial      String    @unique(map: "serial") @db.VarChar(24)
  cost        String?   @db.VarChar(10)
  createdDate DateTime? @db.DateTime(0)
  frozenUntil DateTime? @db.DateTime(0)
  frozenUuid  String?   @db.VarChar(50)
  buyDate     DateTime? @db.DateTime(0)

  @@index([frozenUntil], map: "frozenUntil")
  @@index([frozenUuid], map: "frozenUuid")
  @@index([buyDate], map: "buyDate")
  @@map("gta_keys")
}

model MiniGamesF2Log {
  id        Int      @id @default(autoincrement())
  userId    Int      @default(0)
  accountId Int      @default(0)
  game      String   @default("0") @db.VarChar(10)
  type      String   @default("") @db.VarChar(100)
  gameId    Int?     @default(0)
  amount    Int      @default(0)
  args      String?  @db.LongText
  date      DateTime @db.DateTime(0)
  serverId  String?  @db.VarChar(24)

  @@index([accountId], map: "accountId")
  @@index([date], map: "date")
  @@index([game], map: "game")
  @@index([gameId], map: "gameId")
  @@index([type], map: "type")
  @@index([userId], map: "userId")
  @@map("minigames_f2_log")
}

model JackpotBets {
  id         Int       @id @default(autoincrement())
  userId     Int?
  accountId  Int?
  amount     Int?
  ticket     String?   @db.VarChar(10)
  gameId     Int?
  dateBet    DateTime? @default(now()) @db.DateTime(0)
  socialClub String?   @db.VarChar(16)
  login      String?   @db.VarChar(20)
  serverId   String?   @db.VarChar(24)
  gender     Boolean?

  @@index([dateBet], map: "dateBet")
  @@index([gameId], map: "gameId")
  @@index([userId], map: "userId")
  @@map("jackpot_bets")
}

model JackpotGames {
  id               Int      @id @default(autoincrement())
  playersCount     Int
  bank             Int
  winnerId         Int      @default(0)
  winnerChance     Float    @db.Float
  winnerPosition   Float    @default(0) @db.Float
  dateGame         DateTime @default(now()) @db.DateTime(0)
  winnerLogin      String?  @db.VarChar(20)
  winnerSocialClub String?  @db.VarChar(16)
  winnerCardIndex  Int?     @db.UnsignedInt
  winnerAccountId  Int?
  winnerServerId   String?  @db.VarChar(24)
  winnerGender     Boolean?
  winnerAmount     Int?
  winnerTicket     String?

  @@index([dateGame], map: "dateGame")
  @@index([winnerId], map: "winnerId")
  @@map("jackpot_games")
}

model KeysPayments {
  id           String    @id @db.VarChar(40)
  keyId        Int?      @db.UnsignedInt
  email        String    @db.VarChar(40)
  ip           String?   @db.VarChar(40)
  dateCreate   DateTime? @db.DateTime(0)
  dateComplete DateTime? @db.DateTime(0)
  method       String?   @db.VarChar(15)
  provider     String?   @db.VarChar(15)
  profit       String?   @db.VarChar(10)
  mediaReferer String?   @db.VarChar(20)
  referer      String?   @db.VarChar(255)

  @@index([keyId], map: "keyId")
  @@index([email], map: "email")
  @@index([dateComplete], map: "dateComplete")
  @@index([method], map: "method")
  @@index([provider], map: "provider")
  @@index([mediaReferer], map: "mediaReferer")
  @@index([referer], map: "referer")
  @@map("keys_payments")
}

model logs_users {
  id       Int    @id @default(autoincrement())
  password String @default("0") @db.VarChar(50)
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model masterlist {
  server_id    Int       @default(0) @db.UnsignedSmallInt
  serverRegion String?   @unique(map: "serverRegion") @db.VarChar(6)
  online       Int?      @default(0) @db.UnsignedMediumInt
  date         DateTime? @db.DateTime(0)
  dateUNIX     BigInt?   @default(dbgenerated("(unix_timestamp())"))

  @@ignore
}

model mc_keys {
  keyId     String    @id @default("") @db.VarChar(30)
  amount    Int       @default(0) @db.UnsignedSmallInt
  price     String    @default("0") @db.VarChar(10)
  region    String    @default("RU") @db.VarChar(3)
  userId    Int?      @db.UnsignedMediumInt
  accountId Int?      @db.UnsignedMediumInt
  server    String?   @db.VarChar(6)
  createdAt DateTime? @db.DateTime(0)
  usedAt    DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([amount], map: "amount")
  @@index([createdAt], map: "createdAt")
  @@index([price], map: "price")
  @@index([region], map: "region")
  @@index([server], map: "server")
  @@index([usedAt], map: "usedAt")
  @@index([userId], map: "userId")
}

model mc_keys_bonus {
  keyId     String    @id @default("") @db.VarChar(30)
  type      Int       @default(0) @db.UnsignedTinyInt
  region    String    @default("RU") @db.VarChar(3)
  userId    Int?      @db.UnsignedMediumInt
  accountId Int?      @db.UnsignedMediumInt
  server    String?   @db.VarChar(5)
  createdAt DateTime? @db.DateTime(0)
  usedAt    DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([region], map: "region")
  @@index([server], map: "server")
  @@index([type], map: "type")
  @@index([usedAt], map: "usedAt")
  @@index([userId], map: "userId")
}

model PixelBattle {
  id         Int       @id
  serverId   String?   @db.VarChar(24)
  staticId   Int?
  name       String?   @db.VarChar(64)
  updateDate DateTime? @db.DateTime(0)
  isAdmin    Int?      @db.UnsignedTinyInt

  @@map("pixel_battle")
}

model PixelBattleSnapshot {
  id        Int       @id @default(autoincrement())
  image     Bytes     @db.Blob
  createdAt DateTime? @default(now()) @db.DateTime(0)

  @@map("pixel_battle_snapshot")
}

// checked
model RightsAdminSite {
  id         Int     @id @default(autoincrement())
  adminLevel Int?    @unique @db.TinyInt
  rights     String? @db.Text

  @@map("rights_admin_site")
}

// checked
model Rule {
  id     Int       @id @default(autoincrement())
  server String?   @db.VarChar(3)
  type   String?   @db.VarChar(15)
  text   String?   @db.LongText
  name   String?   @db.VarChar(50)
  author String?   @db.VarChar(50)
  date   DateTime? @db.DateTime(0)

  @@map("rules")
}

model SendCommandLog {
  id        Int                      @id @default(autoincrement())
  server    String?                  @db.VarChar(10)
  login     String?                  @db.VarChar(20)
  accountId Int?
  command   String?                  @db.VarChar(15)
  args      Bytes?                   @db.Blob
  status    send_command_log_status?
  date      DateTime?                @db.DateTime(0)

  @@map("send_command_log")
}

model ServerOnline {
  id            Int    @id @default(autoincrement())
  serverId      String @db.Char(8)
  players       Int    @default(0) @db.UnsignedSmallInt
  queuedPlayers Int    @default(0) @db.UnsignedSmallInt
  date          BigInt @default(0) @db.UnsignedBigInt

  @@index([serverId], map: "serverId")
  @@map("server_online")
}

model Server {
  id                   String           @unique(map: "id") @db.VarChar(8)
  benefitId            String?          @unique(map: "benefitId") @db.VarChar(16)
  name                 String           @unique(map: "name") @db.VarChar(16)
  branch               servers_branch   @default(release)
  ip                   String           @db.VarChar(64)
  region               servers_region   @default(global)
  country              String           @db.VarChar(4)
  extras               String?          @db.LongText
  isTest               Boolean?         @default(false)
  techWorks            Boolean?         @default(false)
  queue                Int?             @default(1900) @db.UnsignedSmallInt
  queueType            queue_type       @default(selectPerson)
  queueHard            Int?             @default(5000) @db.UnsignedSmallInt
  emailVerify          Boolean?         @default(true)
  seasonPassMultiplier String?          @default("1.0") @db.VarChar(4)
  isRegOpened          servers_reg_open @default(yes)
  afkAuthKick          Int?             @default(0) @db.UnsignedTinyInt
  slowAltQueue         Int?             @default(0) @db.UnsignedTinyInt
  maxPlayersHardLimit  Int?             @default(3000) @db.UnsignedSmallInt
  money_log_lastId     Int?             @default(0) @db.UnsignedInt
  items_logs_lastId    Int?             @default(0) @db.UnsignedInt
  connectConfig        String?          @db.Text
  lastPlayersQueue     Int?             @db.UnsignedSmallInt
  lastPlayersOnline    Int?             @db.UnsignedSmallInt
  lastPlayersUpdatedAt DateTime?        @db.DateTime(0)

  @@map("servers")
}

model TwitchStreamer {
  id             Int    @id @default(autoincrement())
  twitchNickname String @unique(map: "twitchNickname") @db.VarChar(20)

  @@map("twitch_streamers")
}

model VerifiedEmails {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  email       String    @db.VarChar(40)
  code        String?   @db.VarChar(40)
  isVerified  Boolean?  @default(false)
  createdAt   DateTime? @db.DateTime(0)
  completedAt DateTime? @db.DateTime(0)

  @@unique([email], map: "email")
  @@index([code], map: "code")
  @@map("verified_emails")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model server_settings {
  serverRegion String?  @unique(map: "serverRegion") @db.VarChar(6)
  techWorks    Boolean? @default(false)

  @@ignore
}

model RequestsLog {
  id       Int       @id @default(autoincrement()) @db.UnsignedInt
  method   String    @db.VarChar(6)
  route    String    @db.VarChar(50)
  serverId String    @db.VarChar(7)
  userId   Int       @db.UnsignedInt
  args     Bytes?    @db.Blob
  status   Boolean
  date     DateTime? @default(now()) @db.DateTime(0)

  @@index([date], map: "date")
  @@index([route], map: "route")
  @@index([serverId], map: "serverId")
  @@index([userId], map: "userId")
  @@map("requests_log")
}

model TelegramDashboardUsers {
  id               Int      @id @default(autoincrement()) @db.UnsignedInt
  telegramId       BigInt?  @db.UnsignedBigInt
  yandexTrackerId  BigInt   @db.UnsignedBigInt
  telegramUsername String   @db.VarChar(40)
  email            String   @db.VarChar(40)
  createdAt        DateTime @default(now()) @db.DateTime(0)
  args             String?  @db.LongText
  isReportNeed     Boolean? @default(true)
  isAdmin          Boolean? @default(false)
  dailyMsgId       Int?     @db.UnsignedInt
  weeklyMsgId      Int?     @db.UnsignedInt
  tgChatId         Int?     @db.UnsignedInt

  @@unique([telegramId], map: "telegramId")
  @@unique([yandexTrackerId], map: "yandexTrackerId")
  @@unique([email], map: "email")
  @@map("telegram_dashboard_users")
}

model WheelBets {
  id         Int      @id @default(autoincrement())
  userId     Int
  accountId  Int
  amount     Int
  color      String   @db.VarChar(18)
  gameId     Int
  dateBet    DateTime @default(now()) @db.DateTime(0)
  socialClub String?  @db.VarChar(16)
  serverId   String?  @db.VarChar(24)
  login      String?  @db.VarChar(20)
  gender     Boolean?

  @@index([dateBet], map: "dateBet")
  @@index([gameId], map: "gameId")
  @@index([userId], map: "userId")
  @@map("wheel_bets")
}

model WheelGames {
  id           Int      @id @default(autoincrement())
  playersCount Int
  white        Int
  red          Int
  green        Int
  yellow       Int
  bank         Int
  colorWin     String   @db.VarChar(18)
  won          Int
  lost         Int
  dateGame     DateTime @default(now()) @db.DateTime(0)

  @@index([dateGame], map: "dateGame")
  @@map("wheel_games")
}

model MiniGamesPlayers {
  minigameName    mini_games_name
  userId          Int             @db.UnsignedMediumInt
  accountId       Int             @db.UnsignedMediumInt
  serverId        String          @db.VarChar(24)
  login           String          @db.VarChar(20)
  socialClub      String?         @db.VarChar(16)
  donate          Int?            @default(0) @db.UnsignedInt
  betAmount       Int             @default(0) @db.UnsignedMediumInt
  gameId          Int?            @db.UnsignedMediumInt
  inCurrentGame   Boolean         @default(false)
  /// [PlayerArgs]
  args            Json
  gotReward       Boolean         @default(false)
  isActive        Boolean         @default(false)
  gender          Boolean?
  isBetInProgress Boolean?        @default(false)

  @@id([minigameName, userId])
  @@index([minigameName], map: "minigameName")
  @@index([userId], map: "userId")
  @@index([accountId], map: "accountId")
  @@index([serverId], map: "serverId")
  @@index([gameId], map: "gameId")
  @@map("minigames_players")
}

model MiniGamesGames {
  id               Int             @id
  minigameName     mini_games_name
  gameId           Int             @db.UnsignedMediumInt
  isActive         Boolean         @default(true)
  isTimerActive    Boolean         @default(false)
  timerTimeStart   DateTime?       @db.DateTime()
  isGameActive     Boolean         @default(false)
  gameTimeStart    DateTime?       @db.DateTime()
  args             String          @db.LongText
  isGameInProgress Boolean         @default(false)

  @@index([minigameName], map: "minigameName")
  @@index([gameId], map: "gameId")
  @@map("minigames_games")
}

model PaymentWebhookLogs {
  id        Int               @id @default(autoincrement())
  paymentId String            @db.VarChar(40)
  provider  payment_providers
  status    String?           @db.VarChar(24)
  payload   Json
  createdAt DateTime          @default(now()) @db.DateTime(0)

  @@index([paymentId], map: "paymentId")
  @@index([provider], map: "provider")
  @@index([status], map: "status")
  @@map("payment_webhook_logs")
}

model ApiKey {
  id          Int    @id @default(autoincrement())
  hash        String @unique(map: "hash") @db.VarChar(32)
  permissions String @db.Text
  keyPart     String @unique(map: "keyPart") @db.VarChar(5)

  @@map("api_keys")
}

enum bk_fixtures_sport {
  football
  dota2
  csgo
}

enum servers_region {
  ru
  eu
  global
}

enum servers_branch {
  release
  rc
  dev
}

enum servers_reg_open {
  yes
  test
  no
}

enum queue_type {
  selectPerson
  authorize
}

enum send_command_log_status {
  error
  pending
  success
}

enum mini_games_name {
  crash
  jackpot
  wheel
}

enum payment_providers {
  tinkoff
  pgs
  paypalych
  unitpay
  paypal
  multihub
}
