/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `RegisterCodeSlugs`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `RegisterCodeSlugs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `slug` varchar(255) DEFAULT NULL,
  `serverId` varchar(8) DEFAULT NULL,
  `registerCode` varchar(255) DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `anticheat`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `anticheat` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `server` smallint(6) DEFAULT NULL,
  `code` smallint(6) unsigned DEFAULT NULL COMMENT 'Код который сработал на античит',
  `socialClub` varchar(16) DEFAULT NULL,
  `hwid` varchar(128) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `code` (`code`),
  KEY `socialClub` (`socialClub`),
  KEY `hwid` (`hwid`),
  KEY `server` (`server`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Здесь собираются данные о читерах всех серверов';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `banlist`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `banlist` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `serverId` varchar(8) DEFAULT NULL,
  `socialClub` varchar(16) DEFAULT NULL,
  `rgscId` int(10) unsigned DEFAULT NULL,
  `hwid` varchar(128) DEFAULT NULL,
  `ip` varchar(16) DEFAULT NULL,
  `banStart` datetime DEFAULT NULL,
  `banEnd` datetime DEFAULT NULL,
  `banReason` varchar(512) DEFAULT NULL,
  `banBy` mediumint(7) unsigned DEFAULT NULL,
  `unbanBy` mediumint(7) unsigned DEFAULT NULL,
  `unbanReason` varchar(512) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ip` (`ip`) USING BTREE,
  KEY `banStart` (`banStart`) USING BTREE,
  KEY `banEnd` (`banEnd`) USING BTREE,
  KEY `banReason` (`banReason`) USING BTREE,
  KEY `banBy` (`banBy`) USING BTREE,
  KEY `unbanBy` (`unbanBy`) USING BTREE,
  KEY `hwid` (`hwid`) USING BTREE,
  KEY `userId` (`userId`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `socialClub` (`socialClub`) USING BTREE,
  KEY `unbanReason` (`unbanReason`) USING BTREE,
  KEY `rgscId` (`rgscId`) USING BTREE,
  KEY `serverId` (`serverId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Банлист';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `betboom_logos`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `betboom_logos` (
  `id` int(11) NOT NULL,
  `src` varchar(150) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bk_fixtures`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bk_fixtures` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `fixtureId` int(10) unsigned DEFAULT NULL,
  `sport` enum('football','dota2','csgo') DEFAULT 'football',
  `fixtureDate` datetime DEFAULT NULL,
  `timestamp` int(11) DEFAULT NULL,
  `leagueName` varchar(50) DEFAULT NULL,
  `leagueCountry` varchar(50) DEFAULT NULL,
  `flag` varchar(60) DEFAULT NULL,
  `team1` varchar(50) DEFAULT NULL,
  `team2` varchar(50) DEFAULT NULL,
  `halftimeScore1` varchar(50) DEFAULT NULL,
  `halftimeScore2` varchar(50) DEFAULT NULL,
  `fulltimeScore1` varchar(50) DEFAULT NULL,
  `fulltimeScore2` varchar(50) DEFAULT NULL,
  `winHome` varchar(5) DEFAULT '1.0',
  `winDraw` varchar(5) DEFAULT '1.0',
  `winAway` varchar(5) DEFAULT '1.0',
  `half1Home` varchar(5) DEFAULT '1.0',
  `half1Draw` varchar(5) DEFAULT '1.0',
  `half1Away` varchar(5) DEFAULT '1.0',
  `half2Home` varchar(5) DEFAULT '1.0',
  `half2Draw` varchar(5) DEFAULT '1.0',
  `half2Away` varchar(5) DEFAULT '1.0',
  `tm05` varchar(5) DEFAULT '1.0',
  `tb05` varchar(5) DEFAULT '1.0',
  `tm15` varchar(5) DEFAULT '1.0',
  `tb15` varchar(5) DEFAULT '1.0',
  `tm25` varchar(5) DEFAULT '1.0',
  `tb25` varchar(5) DEFAULT '1.0',
  `tm35` varchar(5) DEFAULT '1.0',
  `tb35` varchar(5) DEFAULT '1.0',
  `bothYes` varchar(5) DEFAULT '1.0',
  `bothNo` varchar(5) DEFAULT '1.0',
  `bothYes1` varchar(5) DEFAULT '1.0',
  `bothNo1` varchar(5) DEFAULT '1.0',
  `bothYes2` varchar(5) DEFAULT '1.0',
  `bothNo2` varchar(5) DEFAULT '1.0',
  `updatedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fixtureId` (`fixtureId`),
  KEY `leagueName` (`leagueName`),
  KEY `fixtureDate` (`fixtureDate`),
  KEY `leagueCountry` (`leagueCountry`),
  KEY `updatedAt` (`updatedAt`),
  KEY `halftimeScore1` (`halftimeScore1`),
  KEY `halftimeScore2` (`halftimeScore2`),
  KEY `fulltimeScore1` (`fulltimeScore1`),
  KEY `fulltimeScore2` (`fulltimeScore2`),
  KEY `sport` (`sport`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bk_fixtures_betboom`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bk_fixtures_betboom` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `timestamp` int(11) unsigned DEFAULT NULL,
  `category` varchar(50) DEFAULT NULL,
  `categoryRu` varchar(50) DEFAULT NULL,
  `subCategory` varchar(50) DEFAULT NULL,
  `subCategoryRu` varchar(50) DEFAULT NULL,
  `tournament` varchar(100) DEFAULT NULL,
  `tournamentRu` varchar(100) DEFAULT NULL,
  `homeId` int(10) unsigned DEFAULT NULL,
  `home` varchar(100) DEFAULT NULL,
  `homeRu` varchar(100) DEFAULT NULL,
  `awayId` int(10) unsigned DEFAULT NULL,
  `away` varchar(100) DEFAULT NULL,
  `awayRu` varchar(100) DEFAULT NULL,
  `odds` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `score` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `updatedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `updatedAt` (`updatedAt`) USING BTREE,
  KEY `timestamp` (`timestamp`) USING BTREE,
  KEY `sport` (`category`) USING BTREE,
  KEY `tournament` (`tournament`),
  KEY `subCategory` (`subCategory`),
  KEY `homeId` (`homeId`),
  KEY `awayId` (`awayId`),
  KEY `categoryRu` (`categoryRu`),
  KEY `subCategoryRu` (`subCategoryRu`),
  KEY `tournamentRu` (`tournamentRu`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bk_fixtures_betboom_copy`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bk_fixtures_betboom_copy` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `timestamp` int(11) unsigned DEFAULT NULL,
  `category` varchar(50) DEFAULT NULL,
  `categoryRu` varchar(50) DEFAULT NULL,
  `subCategory` varchar(50) DEFAULT NULL,
  `subCategoryRu` varchar(50) DEFAULT NULL,
  `tournament` varchar(100) DEFAULT NULL,
  `tournamentRu` varchar(100) DEFAULT NULL,
  `homeId` int(10) unsigned DEFAULT NULL,
  `home` varchar(100) DEFAULT NULL,
  `homeRu` varchar(100) DEFAULT NULL,
  `awayId` int(10) unsigned DEFAULT NULL,
  `away` varchar(100) DEFAULT NULL,
  `awayRu` varchar(100) DEFAULT NULL,
  `odds` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `score` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `updatedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `updatedAt` (`updatedAt`) USING BTREE,
  KEY `timestamp` (`timestamp`) USING BTREE,
  KEY `sport` (`category`) USING BTREE,
  KEY `tournament` (`tournament`) USING BTREE,
  KEY `subCategory` (`subCategory`) USING BTREE,
  KEY `homeId` (`homeId`) USING BTREE,
  KEY `awayId` (`awayId`) USING BTREE,
  KEY `categoryRu` (`categoryRu`) USING BTREE,
  KEY `subCategoryRu` (`subCategoryRu`) USING BTREE,
  KEY `tournamentRu` (`tournamentRu`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bonuscodes`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bonuscodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(25) DEFAULT NULL,
  `used` smallint(5) unsigned DEFAULT 0,
  `maxUsed` smallint(5) unsigned DEFAULT NULL,
  `maxLevel` tinyint(1) unsigned DEFAULT NULL,
  `regBefore` datetime DEFAULT NULL,
  `regAfter` datetime DEFAULT NULL,
  `server` varchar(6) DEFAULT NULL COMMENT 'UNUSED',
  `serverExclude` varchar(6) DEFAULT NULL,
  `region` varchar(4) DEFAULT 'RU',
  `start` datetime DEFAULT NULL,
  `end` datetime DEFAULT NULL,
  `money` mediumint(8) unsigned DEFAULT NULL,
  `seasonPassExp` mediumint(8) unsigned DEFAULT NULL,
  `coins` mediumint(8) unsigned DEFAULT NULL,
  `vipLevel` tinyint(3) unsigned DEFAULT NULL,
  `vipDays` tinyint(3) unsigned DEFAULT NULL,
  `gunzone3` tinyint(3) unsigned DEFAULT NULL,
  `kinguin` tinyint(1) unsigned DEFAULT NULL,
  `createdAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `start` (`start`),
  KEY `end` (`end`),
  KEY `createdAt` (`createdAt`),
  KEY `server` (`server`),
  KEY `region` (`region`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `bonuscodes_used`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bonuscodes_used` (
  `server` varchar(6) DEFAULT NULL,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `userId` mediumint(8) unsigned DEFAULT NULL,
  `code` varchar(25) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  KEY `server` (`server`),
  KEY `accountId` (`accountId`),
  KEY `code` (`code`),
  KEY `userId` (`userId`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `captures_playlog`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `captures_playlog` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `server` varchar(50) DEFAULT NULL,
  `socialClub` varchar(16) DEFAULT NULL,
  `hwid` varchar(128) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `isFamily` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `socialClub` (`socialClub`) USING BTREE,
  KEY `hwid` (`hwid`) USING BTREE,
  KEY `server` (`server`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

CREATE TABLE `crash_bets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `accountId` int(11) NOT NULL,
  `bet` int(11) NOT NULL,
  `exitX` float DEFAULT NULL COMMENT 'X вывода игрока',
  `gameId` int(11) NOT NULL,
  `dateBet` datetime NOT NULL DEFAULT current_timestamp(),
  `socialClub` varchar(16) DEFAULT NULL,
  `serverId` varchar(24) DEFAULT NULL,
  `login` varchar(20) DEFAULT NULL,
  `gender` tinyint(1) UNSIGNED DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `gameId` (`gameId`),
  KEY `dateBet` (`dateBet`),
  KEY `userId` (`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE `crash_games` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playersCount` int(11) NOT NULL,
  `crashed_at` float NOT NULL,
  `bank` int(11) NOT NULL,
  `won` int(11) NOT NULL,
  `lost` int(11) NOT NULL,
  `dateGame` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `dateGame` (`dateGame`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE `minigames_f2_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL DEFAULT 0,
  `accountId` int(11) NOT NULL DEFAULT 0,
  `game` varchar(10) NOT NULL DEFAULT '0',
  `type` varchar(100) NOT NULL DEFAULT '',
  `gameId` int(11) DEFAULT 0,
  `amount` int(11) NOT NULL DEFAULT 0,
  `args` longtext DEFAULT NULL,
  `date` datetime NOT NULL,
  `serverId` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userId` (`userId`),
  KEY `gameId` (`gameId`),
  KEY `accountId` (`accountId`),
  KEY `game` (`game`),
  KEY `type` (`type`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Table structure for table `dictionary_animations`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dictionary_animations` (
  `id` int(11) DEFAULT NULL,
  `title` varchar(20) DEFAULT NULL,
  KEY `id` (`id`),
  KEY `title` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dictionary_clothes`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dictionary_clothes` (
  `component` tinyint(3) unsigned DEFAULT NULL,
  `drawable` smallint(5) unsigned DEFAULT NULL,
  `texture` tinyint(3) unsigned DEFAULT NULL,
  `isProp` tinyint(3) unsigned DEFAULT NULL,
  `gender` tinyint(3) unsigned DEFAULT NULL,
  `title` varchar(150) DEFAULT NULL,
  KEY `component` (`component`),
  KEY `drawable` (`drawable`),
  KEY `texture` (`texture`),
  KEY `isProp` (`isProp`),
  KEY `gender` (`gender`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dictionary_vehicles`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dictionary_vehicles` (
  `model` varchar(20) DEFAULT NULL,
  `title` varchar(50) DEFAULT NULL,
  KEY `model` (`model`),
  KEY `title` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `faq`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `faq` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server` varchar(3) DEFAULT NULL,
  `text` longtext DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `author` varchar(50) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='FAQ игровых серверов';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `gta_keys`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gta_keys` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `serial` varchar(24) NOT NULL,
  `cost` varchar(10) DEFAULT NULL,
  `createdDate` datetime DEFAULT NULL,
  `frozenUntil` datetime DEFAULT NULL,
  `frozenUuid` varchar(50) DEFAULT NULL,
  `buyDate` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `serial` (`serial`),
  KEY `buyDate` (`buyDate`),
  KEY `frozenUntil` (`frozenUntil`),
  KEY `frozenUuid` (`frozenUuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

CREATE TABLE `jackpot_bets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) DEFAULT NULL,
  `accountId` int(11) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `ticket` varchar(10) DEFAULT NULL,
  `gameId` int(11) DEFAULT NULL,
  `dateBet` datetime DEFAULT current_timestamp(),
  `socialClub` varchar(16) DEFAULT NULL,
  `login` varchar(20) DEFAULT NULL,
  `serverId` varchar(24) DEFAULT NULL,
  `gender` tinyint(1) UNSIGNED DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userId` (`userId`),
  KEY `gameId` (`gameId`),
  KEY `dateBet` (`dateBet`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE `jackpot_games` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playersCount` int(11) NOT NULL,
  `bank` int(11) NOT NULL,
  `winnerId` int(11) NOT NULL DEFAULT 0,
  `winnerChance` float NOT NULL,
  `winnerPosition` float NOT NULL DEFAULT 0,
  `dateGame` datetime NOT NULL DEFAULT current_timestamp(),
  `winnerLogin` varchar(20) DEFAULT NULL,
  `winnerSocialClub` varchar(16) DEFAULT NULL,
  `winnerCardIndex` int(10) DEFAULT NULL,
  `winnerServerId` varchar(24) DEFAULT NULL,
  `winderGender` tinyint(1) UNSIGNED DEFAULT NULL,
  `winnerAccountId` int(11) DEFAULT NULL,
  `winnerAmount` int(11) DEFAULT NULL,
  `winnerTicket` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `winnerId` (`winnerId`),
  KEY `dateGame` (`dateGame`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Table structure for table `keys_payments`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `keys_payments` (
  `id` varchar(40) NOT NULL,
  `keyId` int(10) unsigned DEFAULT NULL,
  `email` varchar(40) NOT NULL,
  `ip` varchar(40) DEFAULT NULL,
  `dateCreate` datetime DEFAULT NULL,
  `dateComplete` datetime DEFAULT NULL,
  `method` varchar(15) DEFAULT NULL,
  `provider` varchar(15) DEFAULT NULL,
  `profit` varchar(10) DEFAULT NULL,
  `mediaReferer` varchar(20) DEFAULT NULL,
  `referer` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `method` (`method`),
  KEY `dateComplete` (`dateComplete`),
  KEY `provider` (`provider`),
  KEY `email` (`email`),
  KEY `keyId` (`keyId`),
  KEY `mediaReferer` (`mediaReferer`),
  KEY `referer` (`referer`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `logs_users`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `logs_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `password` varchar(50) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `masterlist`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `masterlist` (
  `server_id` smallint(5) unsigned NOT NULL DEFAULT 0,
  `serverRegion` varchar(6) DEFAULT NULL,
  `online` mediumint(8) unsigned DEFAULT 0,
  `date` datetime DEFAULT NULL,
  `dateUNIX` bigint(20) DEFAULT unix_timestamp(),
  UNIQUE KEY `serverRegion` (`serverRegion`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Текущий онлайн сервера';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mc_keys`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mc_keys` (
  `keyId` varchar(30) NOT NULL DEFAULT '',
  `amount` mediumint(8) unsigned NOT NULL DEFAULT 0,
  `price` varchar(10) NOT NULL DEFAULT '0',
  `region` varchar(3) NOT NULL DEFAULT 'RU',
  `userId` mediumint(8) unsigned DEFAULT NULL,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `server` varchar(6) DEFAULT NULL,
  `createdAt` datetime DEFAULT NULL,
  `usedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`keyId`),
  KEY `userId` (`userId`),
  KEY `accountId` (`accountId`),
  KEY `server` (`server`),
  KEY `amount` (`amount`),
  KEY `usedAt` (`usedAt`),
  KEY `price` (`price`),
  KEY `createdAt` (`createdAt`),
  KEY `region` (`region`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mc_keys_bonus`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mc_keys_bonus` (
  `keyId` varchar(30) NOT NULL DEFAULT '',
  `type` tinyint(3) unsigned NOT NULL DEFAULT 0,
  `region` varchar(3) NOT NULL DEFAULT 'RU',
  `userId` mediumint(8) unsigned DEFAULT NULL,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `server` varchar(5) DEFAULT NULL,
  `createdAt` datetime DEFAULT NULL,
  `usedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`keyId`) USING BTREE,
  KEY `region` (`region`),
  KEY `userId` (`userId`),
  KEY `accountId` (`accountId`),
  KEY `server` (`server`),
  KEY `usedAt` (`usedAt`),
  KEY `type` (`type`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pixel_battle`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pixel_battle` (
  `id` int(11) NOT NULL,
  `serverId` varchar(24) DEFAULT NULL,
  `staticId` int(11) DEFAULT NULL,
  `name` varchar(64) DEFAULT NULL,
  `updateDate` datetime DEFAULT NULL,
  `isAdmin` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

CREATE TABLE `pixel_battle_snapshot` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `createdAt` datetime NOT NULL DEFAULT current_timestamp(),
    `image` blob NOT NULL DEFAULT '',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

--
-- Table structure for table `requests_log`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `requests_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `method` varchar(6) NOT NULL,
  `route` varchar(50) NOT NULL,
  `serverId` varchar(7) NOT NULL,
  `userId` int(10) unsigned NOT NULL,
  `args` blob DEFAULT NULL,
  `status` tinyint(1) unsigned NOT NULL,
  `date` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userId` (`userId`) USING BTREE,
  KEY `serverId` (`serverId`) USING BTREE,
  KEY `route` (`route`) USING BTREE,
  KEY `date` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `rights_admin_site`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rights_admin_site` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `adminLevel` tinyint(4) DEFAULT NULL,
  `rights` text DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Права доступов админ сайта';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `rules`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server` varchar(3) DEFAULT NULL,
  `type` varchar(15) DEFAULT NULL,
  `text` longtext DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `author` varchar(50) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Правила игровых серверов';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `season_pass_data`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `season_pass_data` (
  `resetTasksTime` int(10) unsigned DEFAULT NULL,
  `server` varchar(6) DEFAULT NULL,
  UNIQUE KEY `server` (`server`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `send_command_log`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `send_command_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server` varchar(10) DEFAULT NULL,
  `login` varchar(20) DEFAULT NULL,
  `accountId` int(11) DEFAULT NULL,
  `command` varchar(15) DEFAULT NULL,
  `args` blob DEFAULT NULL,
  `status` enum('error','pending','success') DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `server_online`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `server_online` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `serverId` char(8) NOT NULL,
  `players` smallint(5) unsigned NOT NULL DEFAULT 0,
  `queuedPlayers` smallint(5) unsigned NOT NULL DEFAULT 0,
  `date` bigint(20) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `serverId` (`serverId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `servers`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `servers` (
  `id` varchar(8) NOT NULL COMMENT 'e.g RU4',
  `benefitId` varchar(16) DEFAULT NULL,
  `name` varchar(16) NOT NULL COMMENT 'e.g Las Vegas',
  `branch` enum('release','rc','dev') NOT NULL DEFAULT 'release',
  `ip` varchar(64) NOT NULL,
  `region` enum('ru','eu','global') NOT NULL DEFAULT 'global',
  `country` varchar(4) NOT NULL,
  `extras` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '{gift: 1, experience 2}' CHECK (json_valid(`extras`)),
  `isTest` tinyint(1) DEFAULT 0,
  `techWorks` tinyint(1) unsigned DEFAULT 0,
  `queue` smallint(5) unsigned DEFAULT 1900,
  `queueHard` smallint(5) unsigned DEFAULT 5000 COMMENT 'Лимит после которого очередь идет через altv',
  `emailVerify` tinyint(1) unsigned DEFAULT 1 COMMENT 'При входе из нового места высылать на емейл проверочный код',
  `isRegOpened` enum('yes','test','no') DEFAULT 'yes' COMMENT 'Отображать сервер на сайте',
  `playerActionAnalytics` TINYINT(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Включение аналитики бизнес действий игрока',
  `connectConfig` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `lastPlayersQueue` smallint unsigned DEFAULT NULL,
  `lastPlayersOnline` smallint unsigned DEFAULT NULL,
  `lastPlayersUpdatedAt` datetime DEFAULT NULL,
  UNIQUE KEY `id` (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `benefitId` (`benefitId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='List of servers for masterlist';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `twitch_streamers`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `twitch_streamers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `twitchNickname` varchar(20) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `verified_emails`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `verified_emails` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(40) NOT NULL,
  `code` varchar(40) DEFAULT NULL,
  `isVerified` tinyint(1) DEFAULT 0,
  `createdAt` datetime DEFAULT NULL,
  `completedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) ,
  UNIQUE KEY `email` (`email`),
  KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `wheel_bets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `accountId` int(11) NOT NULL,
  `amount` int(11) NOT NULL,
  `color` varchar(18) NOT NULL,
  `gameId` int(11) NOT NULL,
  `dateBet` datetime NOT NULL DEFAULT current_timestamp(),
  `socialClub` varchar(16) DEFAULT NULL,
  `serverId` varchar(24) DEFAULT NULL,
  `login` varchar(20) DEFAULT NULL,
  `gender` tinyint(1) UNSIGNED DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userId` (`userId`),
  KEY `gameId` (`gameId`),
  KEY `dateBet` (`dateBet`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;


CREATE TABLE `wheel_games` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playersCount` int(11) NOT NULL,
  `white` int(11) NOT NULL,
  `red` int(11) NOT NULL,
  `green` int(11) NOT NULL,
  `yellow` int(11) NOT NULL,
  `bank` int(11) NOT NULL,
  `colorWin` varchar(18) NOT NULL,
  `won` int(11) NOT NULL,
  `lost` int(11) NOT NULL,
  `dateGame` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) USING BTREE,
  KEY `dateGame` (`dateGame`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

CREATE TABLE `minigames_players` (
  `minigameName` enum('crash','jackpot','wheel'),
  `userId` mediumint(8) unsigned NOT NULL,
  `accountId` mediumint(8) unsigned NOT NULL,
  `serverId` varchar(24) NOT NULL,
  `login` varchar(20) NOT NULL,
  `socialClub` varchar(16),
  `donate` int(10) unsigned NOT NULL,
  `betAmount` smallint(5) unsigned DEFAULT 0,
  `gameId`  mediumint(8) unsigned DEFAULT NULL,
  `inCurrentGame` TINYINT(1) DEFAULT 0,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `gotReward` TINYINT(1) UNSIGNED DEFAULT 0,
  `isActive` TINYINT(1) UNSIGNED DEFAULT 0,
  `gender` tinyint(1) UNSIGNED DEFAULT NULL,
  `isBetInProgress` tinyint(1) UNSIGNED DEFAULT 0,
  PRIMARY KEY (`minigameName`, `userId`),
  KEY `accountId` (`accountId`),
  KEY `serverId` (`serverId`),
  KEY `gameId` (`gameId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Table structure for table `telegram_dashboard_users`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `telegram_dashboard_users` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `telegramId` bigint(20),
  `yandexTrackerId` bigint(20) NOT NULL,
  `telegramUsername` varchar(40) NOT NULL,
  `email` varchar(40) NOT NULL,
  `createdAt` datetime NOT NULL DEFAULT current_timestamp(),
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`args`)),
  `isReportNeed` TINYINT(1) UNSIGNED DEFAULT 1,
  `isAdmin` TINYINT(1) UNSIGNED DEFAULT 0,
  `dailyMsgId` int(10) unsigned DEFAULT NULL,
  `weeklyMsgId` int(10) unsigned DEFAULT NULL,
  `tgChatId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `telegramId` (`telegramId`),
  UNIQUE KEY `yandexTrackerId` (`yandexTrackerId`),
  UNIQUE KEY `email` (`email`)
  UNIQUE KEY `telegramUsername` (`telegramUsername`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

CREATE TABLE `payment_webhook_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `paymentId` varchar(40) NOT NULL,
  `provider` enum('tinkoff','pgs','paypalych','unitpay','paypal','multihub') NOT NULL,
  `status` varchar(24) DEFAULT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `createdAt` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) ,
  KEY `paymentId` (`paymentId`),
  KEY `provider` (`provider`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping routines for database 'majestic_rp'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

CREATE TABLE `api_keys` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `hash` varchar(32) NOT NULL UNIQUE,
  `permissions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `keyPart` varchar(5) NOT NULL UNIQUE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
