/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


CREATE TABLE `_change_clothes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `isProp` tinyint(2) DEFAULT 0,
  `gender` tinyint(2) DEFAULT 0,
  `component` tinyint(2) DEFAULT 0,
  `oldDrawable` smallint(6) DEFAULT 0,
  `oldTexture` tinyint(4) DEFAULT -1,
  `newDrawable` smallint(6) DEFAULT 0,
  `newTexture` tinyint(4) DEFAULT -1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `accounts` (
  `id` mediumint(7) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `login` varchar(20) DEFAULT NULL,
  `admin` int(11) DEFAULT 0,
  `tester` tinyint(1) unsigned DEFAULT 0,
  `level` tinyint(3) unsigned DEFAULT 1,
  `exp` smallint(4) unsigned DEFAULT 0,
  `gender` tinyint(1) unsigned DEFAULT NULL,
  `nationality` tinyint(1) unsigned DEFAULT 0,
  `age` tinyint(2) unsigned DEFAULT 18,
  `hp` tinyint(2) unsigned DEFAULT 100,
  `cash` bigint(12) unsigned DEFAULT 350000,
  `chips` bigint(12) unsigned DEFAULT 0 COMMENT 'Фишки в казино',
  `casinoWin` bigint(12) unsigned DEFAULT 0,
  `job` tinyint(2) unsigned DEFAULT 0,
  `member` tinyint(2) unsigned DEFAULT 0,
  `leader` tinyint(2) unsigned DEFAULT 0,
  `fractionRankId` mediumint(8) unsigned DEFAULT NULL,
  `family` mediumint(8) unsigned DEFAULT 0,
  `familyRankId` mediumint(8) unsigned DEFAULT NULL,
  `fwarns` tinyint(2) unsigned DEFAULT 0,
  `famwarns` tinyint(2) unsigned DEFAULT 0,
  `createdAt` datetime DEFAULT NULL,
  `lastVisit` datetime DEFAULT NULL,
  `online` tinyint(3) unsigned DEFAULT 0,
  `dayOnline` mediumint(5) unsigned DEFAULT 0,
  `weekOnline` mediumint(6) unsigned DEFAULT 0,
  `monthOnline` mediumint(7) unsigned DEFAULT 0,
  `totalOnline` int(10) unsigned DEFAULT 0,
  `contestOnline` mediumint(7) unsigned DEFAULT NULL,
  `createdIP` varchar(16) DEFAULT NULL,
  `lastIP` varchar(16) DEFAULT NULL,
  `vip` tinyint(2) DEFAULT NULL,
  `vipDate` datetime DEFAULT NULL,
  `married` mediumint(7) unsigned DEFAULT NULL,
  `jailTotalTime` mediumint(8) unsigned DEFAULT 0,
  `wantedLevel` tinyint(1) unsigned DEFAULT 0,
  `payday` tinyint(2) unsigned DEFAULT 60,
  `crimes` smallint(5) unsigned DEFAULT 0,
  `arrests` smallint(5) unsigned DEFAULT 0,
  `criminalRecords` smallint(5) unsigned DEFAULT 0,
  `kills` smallint(5) unsigned DEFAULT 0,
  `deaths` smallint(5) unsigned DEFAULT 0,
  `warns` tinyint(1) unsigned DEFAULT 0,
  `warnStats` smallint(5) unsigned DEFAULT 0,
  `muted` smallint(5) unsigned DEFAULT 0,
  `hunger` tinyint(3) DEFAULT 50,
  `water` tinyint(3) DEFAULT 50,
  `marketplaceRating` float DEFAULT 0,
  `cooldowns` blob DEFAULT NULL,
  `buffs` blob DEFAULT NULL,
  `achievements` blob DEFAULT NULL,
  `rentCar` blob DEFAULT NULL,
  `tempJobInfo` blob DEFAULT NULL,
  `tempMoney` blob DEFAULT NULL,
  `lic` blob DEFAULT NULL,
  `customization` blob DEFAULT NULL,
  `settings` blob DEFAULT NULL,
  `bindings` blob DEFAULT NULL,
  `tattoos` blob DEFAULT NULL,
  `availableTattoos` blob DEFAULT NULL,
  `equipedSkins` blob DEFAULT NULL,
  `statTracks` blob DEFAULT NULL,
  `exitPosition` blob DEFAULT NULL,
  `fastSlots` blob DEFAULT NULL,
  `jail` blob DEFAULT NULL,
  `marketplaceFavorites` blob DEFAULT NULL,
  `houseId` smallint(4) unsigned DEFAULT NULL,
  `rentHouseId` smallint(4) unsigned DEFAULT NULL,
  `apartmentId` smallint(4) unsigned DEFAULT NULL,
  `rentApartmentId` smallint(4) unsigned DEFAULT NULL,
  `biz` varchar(20) DEFAULT NULL,
  `infected` tinyint(1) unsigned DEFAULT 0,
  `carSlots` tinyint(1) unsigned DEFAULT 0,
  `contractsData` blob DEFAULT '{}',
  `weaponsBlock` int(11) DEFAULT NULL,
  `halloweenQrCodes` blob DEFAULT '[]',
  `xmasPropsSearch` blob DEFAULT '[]',
  PRIMARY KEY (`id`),
  UNIQUE KEY `login` (`login`),
  KEY `userId` (`userId`),
  KEY `gender` (`gender`),
  KEY `createdAt` (`createdAt`),
  KEY `lastVisit` (`lastVisit`),
  KEY `online` (`online`),
  KEY `admin` (`admin`),
  KEY `leader` (`leader`),
  KEY `member` (`member`),
  KEY `cash` (`cash`),
  KEY `dayOnline` (`dayOnline`),
  KEY `monthOnline` (`monthOnline`),
  KEY `totalOnline` (`totalOnline`),
  KEY `createdIP` (`createdIP`),
  KEY `lastIP` (`lastIP`),
  KEY `level` (`level`),
  KEY `nationality` (`nationality`),
  KEY `age` (`age`),
  KEY `married` (`married`),
  KEY `vip` (`vip`),
  KEY `job` (`job`),
  KEY `houseId` (`houseId`),
  KEY `biz` (`biz`),
  KEY `rentHouseId` (`rentHouseId`),
  KEY `apartmentId` (`apartmentId`),
  KEY `rentApartmentId` (`rentApartmentId`),
  KEY `family` (`family`),
  KEY `weekOnline` (`weekOnline`),
  KEY `online2023` (`contestOnline`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Игровые персонажи';



CREATE TABLE `admin_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `admin` mediumint(7) unsigned DEFAULT NULL,
  `type` varchar(20) DEFAULT NULL,
  `static` mediumint(7) unsigned DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `comment` text DEFAULT NULL,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `date` (`date`),
  KEY `admin` (`admin`),
  KEY `type` (`type`),
  KEY `static` (`static`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `admin_whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `socialClub` varchar(16) NOT NULL DEFAULT '',
  `hwid` varchar(128) DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `socialClub` (`socialClub`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `ammo_shops` (
  `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(24) DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `price` int(11) unsigned DEFAULT 5000000,
  `cash` bigint(12) unsigned DEFAULT 0,
  `bank` bigint(12) unsigned DEFAULT 0,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `pickupX` varchar(10) DEFAULT NULL,
  `pickupY` varchar(10) DEFAULT NULL,
  `pickupZ` varchar(10) DEFAULT NULL,
  `warehouse` blob DEFAULT NULL,
  `prices` blob DEFAULT NULL,
  `discounts` blob DEFAULT NULL,
  `mafiaId` tinyint(2) unsigned DEFAULT 0,
  `mafiaCash` bigint(12) unsigned DEFAULT 0,
  `defaultMafiaId` tinyint(2) unsigned DEFAULT 0,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

LOCK TABLES `ammo_shops` WRITE;
/*!40000 ALTER TABLE `ammo_shops` DISABLE KEYS */;

INSERT INTO `ammo_shops` (`id`, `title`, `accountId`, `price`, `cash`, `bank`, `posX`, `posY`, `posZ`, `pickupX`, `pickupY`, `pickupZ`, `warehouse`, `prices`, `discounts`, `mafiaId`, `mafiaCash`, `defaultMafiaId`, `paidUntil`, `auctionId`, `marketplaceId`)
VALUES
	(1,'Магазин оружия',NULL,*********,*********,1647570,'814.0482','-2147.4714','29.4867','809.8859','-2157.2243','29.6190',NULL,NULL,NULL,16,0,13,'2023-03-23 22:51:00',2182,NULL),
	(2,'Магазин оружия',NULL,*********,*********,0,'841.5540','-1021.7889','27.5359','841.9891','-1033.7945','28.1948',NULL,NULL,NULL,16,0,13,'2023-07-06 22:15:57',2665,NULL),
	(3,'Магазин оружия',NULL,*********,*********,********,'15.4746','-1115.3193','29.7911','22.4782','-1107.1005','29.7970',NULL,NULL,NULL,16,0,13,'2023-06-30 20:43:17',2663,NULL),
	(4,'Магазин оружия',NULL,*********,********,0,'-662.0864','-945.1248','21.7855','-661.8967','-935.0447','21.8292',NULL,NULL,NULL,16,0,13,'2023-07-06 22:15:09',2666,NULL),
	(5,'Магазин оружия',NULL,*********,70450000,0,'-1315.5439','-392.0623','36.5710','-1305.7907','-394.5947','36.6957',NULL,NULL,NULL,16,0,13,'2023-07-21 15:38:23',NULL,NULL),
	(6,'Магазин оружия',NULL,*********,161248770,30118650,'242.7175','-47.0414','69.8965','252.0478','-50.4024','69.9410',NULL,NULL,NULL,16,0,13,'2023-03-25 21:31:01',2338,NULL),
	(7,'Магазин оружия',NULL,90000000,124299930,119465900,'2567.3947','304.1345','108.6078','2567.4719','294.0990','108.7349',NULL,NULL,NULL,16,0,13,'2023-06-30 20:43:11',2664,NULL),
	(8,'Магазин оружия',NULL,*********,47150010,1673300,'-3162.6728','1084.0687','20.8477','-3171.9289','1088.0700','20.8387',NULL,NULL,NULL,16,0,13,'2023-03-23 21:31:01',2176,NULL),
	(9,'Магазин оружия',NULL,1********,51130030,863350,'-1113.7747','2688.7436','18.5914','-1117.5477','2698.8393','18.5541',NULL,NULL,NULL,16,0,13,'2023-03-27 22:10:01',2640,NULL),
	(10,'Магазин оружия',NULL,*********,*********,********,'1700.9964','3753.3823','34.3556','1693.5729','3760.1066','34.7053',NULL,NULL,NULL,16,0,13,'2024-01-30 13:21:53',2710,NULL),
	(11,'Магазин оружия',NULL,********,********,7907700,'-325.7704','6073.9804','31.2351','-330.1637','6084.0493','31.4547',NULL,NULL,NULL,16,0,13,'2023-03-23 22:10:01',2184,NULL);

/*!40000 ALTER TABLE `ammo_shops` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `animations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(8) unsigned NOT NULL,
  `type` enum('circle','favourite') NOT NULL DEFAULT 'favourite',
  `animPath` varchar(50) DEFAULT NULL COMMENT 'Какой path используется для анимок',
  `animIndex` smallint(5) unsigned DEFAULT NULL COMMENT 'Какой index используется для анимок',
  `soundPath` varchar(50) DEFAULT NULL COMMENT 'Какой path используется для звуков',
  `circleIndex` tinyint(3) unsigned DEFAULT NULL COMMENT 'Индексация анимки',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `circleIndex` (`circleIndex`) USING BTREE,
  KEY `type` (`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Анимации из кругового меню и избранные';



CREATE TABLE `animations_purchased` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` int(10) unsigned NOT NULL,
  `userId` int(11) DEFAULT NULL,
  `animationId` smallint(5) unsigned NOT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `userId` (`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `anticheat` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `socialClub` varchar(16) DEFAULT NULL,
  `cheatCode` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `socialClub` (`socialClub`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `anticheat_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `hwid` varchar(128) DEFAULT NULL,
  `socialClub` varchar(16) DEFAULT NULL,
  `code` smallint(5) unsigned DEFAULT NULL,
  `cheatPath` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `hwid` (`hwid`),
  KEY `socialClub` (`socialClub`),
  KEY `code` (`code`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;



CREATE TABLE `anticheat_triggers` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned NOT NULL,
  `triggerId` int(11) NOT NULL,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `triggerId` (`triggerId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Триггеры античита на игроков';



CREATE TABLE `apartments` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `buildingId` smallint(5) unsigned NOT NULL DEFAULT 1,
  `price` int(10) unsigned DEFAULT 1000000,
  `interior` tinyint(1) unsigned DEFAULT 1,
  `garage` tinyint(1) unsigned DEFAULT 0,
  `cash` bigint(12) unsigned DEFAULT 0,
  `rentPrice` int(10) unsigned DEFAULT 0,
  `maxRent` tinyint(1) unsigned DEFAULT 1,
  `locked` tinyint(1) unsigned DEFAULT 0,
  `paidUntil` datetime DEFAULT '2020-05-01 00:00:00',
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`),
  KEY `marketplaceId` (`marketplaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Квартиры';

LOCK TABLES `apartments` WRITE;
/*!40000 ALTER TABLE `apartments` DISABLE KEYS */;

INSERT INTO `apartments` (`id`, `accountId`, `buildingId`, `price`, `interior`, `garage`, `cash`, `rentPrice`, `maxRent`, `locked`, `paidUntil`, `auctionId`, `marketplaceId`)
VALUES
	(1,NULL,1,*********,5,10,0,0,3,0,'2024-04-08 14:12:10',2737,NULL),
	(2,NULL,1,*********,5,10,0,0,3,0,'2023-07-18 20:53:07',2677,NULL),
	(3,NULL,1,*********,5,10,0,0,3,0,'2023-07-18 17:09:23',NULL,NULL),
	(4,NULL,1,*********,5,10,0,0,3,0,'2023-03-09 23:17:44',1125,NULL),
	(5,NULL,1,*********,5,10,0,0,3,0,'2024-03-26 18:53:12',2728,NULL),
	(6,NULL,1,*********,4,10,0,0,3,0,'2023-03-01 22:01:11',488,NULL),
	(7,NULL,1,*********,4,10,0,0,3,0,'2023-03-26 03:01:00',2379,NULL),
	(8,NULL,1,*********,4,10,0,0,3,0,'2023-03-20 21:07:04',2022,NULL),
	(9,NULL,1,*********,4,10,0,0,3,0,'2023-03-09 12:23:49',1053,NULL),
	(10,NULL,1,*********,4,10,0,0,3,0,'2023-03-25 20:27:30',2331,NULL),
	(11,NULL,1,*********,8,10,0,0,3,0,'2023-03-26 21:21:00',2483,NULL),
	(12,NULL,1,*********,8,10,0,0,3,0,'2023-03-27 17:52:31',2578,NULL),
	(13,NULL,1,*********,8,10,0,0,3,0,'2023-03-26 20:54:02',2474,NULL),
	(14,NULL,1,*********,8,10,0,0,3,0,'2023-03-26 03:32:56',2380,NULL),
	(15,NULL,1,*********,8,10,0,0,3,0,'2023-03-11 01:22:08',1254,NULL),
	(16,NULL,1,*********,1,10,0,0,3,0,'2023-03-26 20:25:44',2476,NULL),
	(17,NULL,1,*********,1,10,0,0,3,0,'2023-03-26 21:00:01',2486,NULL),
	(18,NULL,1,*********,1,10,0,0,3,0,'2023-03-20 02:56:01',1975,NULL),
	(19,NULL,1,*********,1,10,0,0,3,0,'2023-03-23 20:29:29',2173,NULL),
	(20,NULL,1,*********,1,10,0,0,3,0,'2023-03-16 02:28:05',1771,NULL),
	(21,NULL,1,*********,6,10,0,0,3,0,'2023-03-27 12:49:44',2542,NULL),
	(22,NULL,1,*********,6,10,0,0,3,0,'2023-03-26 12:51:52',2405,NULL),
	(23,NULL,1,*********,6,10,0,0,3,0,'2023-03-24 15:47:45',2223,NULL),
	(24,NULL,1,*********,6,10,0,0,3,0,'2023-03-23 14:05:19',2142,NULL),
	(25,NULL,1,*********,6,10,0,0,3,0,'2023-03-08 19:48:57',1000,NULL),
	(26,NULL,1,1********,7,10,0,0,3,0,'2023-03-06 05:01:00',753,NULL),
	(27,NULL,1,1********,7,10,0,0,3,0,'2023-03-27 03:07:16',2518,NULL),
	(28,NULL,1,1********,7,10,0,0,3,0,'2023-03-04 12:35:47',612,NULL),
	(29,NULL,1,1********,7,10,0,0,3,0,'2023-03-09 14:09:00',1059,NULL),
	(30,NULL,1,1********,7,10,0,0,3,0,'2023-03-06 00:51:50',737,NULL),
	(31,NULL,1,1********,2,10,0,0,3,0,'2023-03-22 23:10:17',2115,NULL),
	(32,NULL,1,1********,2,10,0,0,3,0,'2023-03-24 03:26:34',2200,NULL),
	(33,NULL,1,1********,2,10,0,0,3,0,'2023-03-07 18:21:57',881,NULL),
	(34,NULL,1,1********,2,10,0,0,3,0,'2023-03-25 23:32:09',2355,NULL),
	(35,NULL,1,1********,2,10,0,0,3,0,'2023-03-11 18:59:03',1310,NULL),
	(36,NULL,1,*********,3,10,0,0,3,0,'2023-03-27 19:15:24',2597,NULL),
	(37,NULL,1,*********,3,10,0,0,3,0,'2023-03-26 19:08:55',2457,NULL),
	(38,NULL,1,*********,3,10,0,0,3,0,'2023-03-09 03:01:00',1041,NULL),
	(39,NULL,1,*********,3,10,0,0,3,0,'2023-03-23 20:47:25',2175,NULL),
	(40,NULL,1,*********,3,10,0,0,3,0,'2023-03-07 19:40:25',886,NULL),
	(41,NULL,1,********,15,6,0,0,2,0,'2023-03-08 00:01:00',916,NULL),
	(42,NULL,1,********,15,6,0,0,2,0,'2023-03-22 21:14:10',2106,NULL),
	(43,NULL,1,********,15,6,0,0,2,0,'2023-03-26 15:11:56',2427,NULL),
	(44,NULL,1,********,15,6,0,0,2,0,'2023-03-25 10:15:30',2290,NULL),
	(45,NULL,1,********,15,6,0,0,2,0,'2023-03-08 18:49:07',989,NULL),
	(46,NULL,1,********,15,6,0,0,2,0,'2023-03-02 01:56:41',494,NULL),
	(47,NULL,1,********,15,6,0,0,2,0,'2023-03-19 11:39:44',1931,NULL),
	(48,NULL,1,********,15,6,0,0,2,0,'2023-02-28 12:03:39',447,NULL),
	(49,NULL,1,********,15,6,0,0,2,0,'2023-03-09 13:12:39',1055,NULL),
	(50,NULL,1,********,15,6,0,0,2,0,'2023-03-04 20:23:01',641,NULL),
	(51,NULL,1,********,15,6,0,0,2,0,'2023-03-12 18:18:28',1448,NULL),
	(52,NULL,1,********,15,6,0,0,2,0,'2023-03-26 15:00:15',2429,NULL),
	(53,NULL,1,********,15,6,0,0,2,0,'2024-04-08 14:10:04',2738,NULL),
	(54,NULL,1,********,15,6,0,0,2,0,'2023-03-15 01:39:48',1728,NULL),
	(55,NULL,1,********,15,6,0,0,2,0,'2023-03-13 20:25:50',1626,NULL),
	(56,NULL,1,********,15,6,0,0,2,0,'2023-03-25 15:01:07',2303,NULL),
	(57,NULL,1,********,15,6,0,0,2,0,'2023-03-26 22:27:00',2496,NULL),
	(58,NULL,1,********,15,6,0,0,2,0,'2023-03-18 00:46:32',1864,NULL),
	(59,NULL,1,********,15,6,0,0,2,0,'2023-03-27 17:22:00',2581,NULL),
	(60,NULL,1,********,15,6,0,0,2,0,'2023-03-12 21:16:44',1479,NULL),
	(61,NULL,1,********,15,6,0,0,2,0,'2023-03-13 19:15:32',1618,NULL),
	(62,NULL,1,********,15,6,0,0,2,0,'2023-03-21 18:44:36',2057,NULL),
	(63,NULL,1,********,15,6,0,0,2,0,'2023-03-10 19:15:37',1206,NULL),
	(64,NULL,1,********,15,6,0,0,2,0,'2023-03-12 02:54:35',1369,NULL),
	(65,NULL,1,********,15,6,0,0,2,0,'2023-03-21 06:43:18',2037,NULL),
	(66,NULL,1,********,15,6,0,0,2,0,'2023-03-20 05:54:49',1981,NULL),
	(67,NULL,1,********,15,6,0,0,2,0,'2023-03-27 18:21:00',2584,NULL),
	(68,NULL,1,********,15,6,0,0,2,0,'2023-03-25 19:23:12',2325,NULL),
	(69,NULL,1,********,15,6,0,0,2,0,'2023-03-12 22:32:13',1488,NULL),
	(70,NULL,1,********,15,6,0,0,2,0,'2023-03-25 01:18:48',2273,NULL),
	(71,NULL,1,********,15,6,0,0,2,0,'2023-07-17 15:20:29',2674,NULL),
	(72,NULL,2,70000000,10,6,0,0,2,0,'2023-03-11 17:41:28',1301,NULL),
	(73,NULL,2,70000000,10,6,0,0,2,0,'2023-03-13 18:43:09',1608,NULL),
	(74,NULL,2,70000000,10,6,0,0,2,0,'2023-03-06 18:52:16',792,NULL),
	(75,NULL,2,70000000,10,6,0,0,2,0,'2023-03-09 17:40:45',1084,NULL),
	(76,NULL,2,70000000,10,6,0,0,2,0,'2023-03-21 02:17:38',2034,NULL),
	(77,NULL,2,70000000,10,6,0,0,2,0,'2023-03-08 08:00:01',938,NULL),
	(78,NULL,2,70000000,10,6,0,0,2,0,'2023-03-16 12:48:58',1780,NULL),
	(79,NULL,2,70000000,10,6,0,0,2,0,'2023-03-26 13:15:51',2412,NULL),
	(80,NULL,2,70000000,10,6,0,0,2,0,'2023-03-10 02:13:39',1143,NULL),
	(81,NULL,2,70000000,10,6,0,0,2,0,'2023-03-26 14:12:53',2417,NULL),
	(82,NULL,2,70000000,10,6,0,0,2,0,'2023-03-26 13:17:00',2414,NULL),
	(83,NULL,2,70000000,10,6,0,0,2,0,'2023-03-18 18:34:03',1900,NULL),
	(84,NULL,2,70000000,10,6,0,0,2,0,'2023-03-15 12:36:32',1740,NULL),
	(85,NULL,2,70000000,10,6,0,0,2,0,'2023-03-25 21:10:13',2344,NULL),
	(86,NULL,2,70000000,10,6,0,0,2,0,'2023-03-21 13:20:00',2042,NULL),
	(87,NULL,2,70000000,10,6,0,0,2,0,'2023-03-22 21:44:35',2108,NULL),
	(88,NULL,2,70000000,10,6,0,0,2,0,'2023-03-27 23:47:53',2657,NULL),
	(89,NULL,2,70000000,10,6,0,0,2,0,'2023-03-14 10:36:35',1697,NULL),
	(90,NULL,2,70000000,10,6,0,0,2,0,'2023-03-04 20:01:00',645,NULL),
	(91,NULL,2,70000000,10,6,0,0,2,0,'2023-03-04 22:31:15',652,NULL),
	(92,NULL,2,70000000,10,6,0,0,2,0,'2023-03-22 22:32:26',2111,NULL),
	(93,NULL,2,70000000,10,6,0,0,2,0,'2023-03-27 15:08:01',2559,NULL),
	(94,NULL,2,70000000,10,6,0,0,2,0,'2023-03-10 19:01:00',1208,NULL),
	(95,NULL,2,70000000,10,6,0,0,2,0,'2023-03-13 21:35:52',1640,NULL),
	(96,NULL,2,70000000,10,6,0,0,2,0,'2023-03-10 19:32:02',1211,NULL),
	(97,NULL,2,70000000,10,6,0,0,2,0,'2023-03-16 21:14:57',1804,NULL),
	(98,NULL,2,70000000,10,6,0,0,2,0,'2023-03-26 05:05:07',2384,NULL),
	(99,NULL,2,70000000,10,6,0,0,2,0,'2023-03-23 16:52:35',2151,NULL),
	(100,NULL,2,70000000,10,6,0,0,2,0,'2023-03-25 23:24:42',2356,NULL),
	(101,NULL,2,70000000,10,6,0,0,2,0,'2023-03-10 01:01:00',1137,NULL),
	(102,NULL,2,65000000,9,6,0,0,2,0,'2023-03-05 21:38:40',720,NULL),
	(103,NULL,2,65000000,9,6,0,0,2,0,'2023-03-07 17:26:42',877,NULL),
	(104,NULL,2,65000000,9,6,0,0,2,0,'2023-03-03 23:01:00',586,NULL),
	(105,NULL,2,65000000,9,6,0,0,2,0,'2023-03-05 00:44:18',659,NULL),
	(106,NULL,2,65000000,9,6,0,0,2,0,'2023-03-08 00:16:00',919,NULL),
	(107,NULL,2,65000000,9,6,0,0,2,0,'2023-03-12 01:47:44',1356,NULL),
	(108,NULL,2,65000000,9,6,0,0,2,0,'2023-03-18 02:13:03',1867,NULL),
	(109,NULL,2,65000000,9,6,0,0,2,0,'2023-03-10 12:04:41',1169,NULL),
	(110,NULL,2,65000000,9,6,0,0,2,0,'2023-03-04 00:57:48',589,NULL),
	(111,NULL,2,65000000,9,6,0,0,2,0,'2023-03-26 05:53:38',2386,NULL),
	(112,NULL,2,65000000,9,6,0,0,2,0,'2023-03-26 21:01:00',2489,NULL),
	(113,NULL,2,65000000,9,6,0,0,2,0,'2023-03-19 20:36:16',1954,NULL),
	(114,NULL,2,65000000,9,6,0,0,2,0,'2023-03-14 02:59:04',1685,NULL),
	(115,NULL,2,65000000,9,6,0,0,2,0,'2023-03-21 01:33:44',2031,NULL),
	(116,NULL,2,65000000,9,6,0,0,2,0,'2023-03-24 09:44:42',2205,NULL),
	(117,NULL,2,65000000,9,6,0,0,2,0,'2023-03-20 03:16:51',1979,NULL),
	(118,NULL,2,65000000,9,6,0,0,2,0,'2023-03-26 10:06:37',2399,NULL),
	(119,NULL,2,65000000,9,6,0,0,2,0,'2023-03-07 12:11:00',855,NULL),
	(120,NULL,2,65000000,9,6,0,0,2,0,'2023-03-19 21:09:01',1961,NULL),
	(121,NULL,2,65000000,9,6,0,0,2,0,'2023-03-10 18:01:01',1201,NULL),
	(122,NULL,2,65000000,9,6,0,0,2,0,'2023-03-05 19:01:00',708,NULL),
	(123,NULL,2,65000000,9,6,0,0,2,0,'2023-03-03 23:20:47',587,NULL),
	(124,NULL,2,65000000,9,6,0,0,2,0,'2023-03-05 11:16:53',674,NULL),
	(125,NULL,2,65000000,9,6,0,0,2,0,'2023-03-05 09:01:00',671,NULL),
	(126,NULL,2,65000000,9,6,0,0,2,0,'2023-03-27 13:01:01',2546,NULL),
	(127,NULL,2,65000000,9,6,0,0,2,0,'2023-03-18 06:36:29',1872,NULL),
	(128,NULL,2,65000000,9,6,0,0,2,0,'2023-03-18 21:09:05',1913,NULL),
	(129,NULL,2,65000000,9,6,0,0,2,0,'2023-03-24 22:19:50',2258,NULL),
	(130,NULL,2,65000000,9,6,0,0,2,0,'2023-03-27 15:28:21',2560,NULL),
	(131,NULL,2,65000000,9,6,0,0,2,0,'2023-03-26 21:14:18',2490,NULL),
	(132,NULL,3,70000000,12,6,0,0,2,0,'2023-03-25 23:04:53',2359,NULL),
	(133,NULL,3,70000000,12,6,0,0,2,0,'2023-03-12 04:01:00',1376,NULL),
	(134,NULL,3,70000000,12,6,0,0,2,0,'2023-03-09 22:41:46',1115,NULL),
	(135,NULL,3,70000000,12,6,0,0,2,0,'2023-03-21 21:02:00',2064,NULL),
	(136,NULL,3,70000000,12,6,0,0,2,0,'2023-03-08 01:01:01',923,NULL),
	(137,NULL,3,70000000,12,6,0,0,2,0,'2023-03-08 19:55:54',1002,NULL),
	(138,NULL,3,70000000,12,6,0,0,2,0,'2023-03-03 02:01:01',536,NULL),
	(139,NULL,3,70000000,12,6,0,0,2,0,'2023-03-27 13:49:11',2549,NULL),
	(140,NULL,3,70000000,12,6,0,0,2,0,'2023-03-05 20:32:38',713,NULL),
	(141,NULL,3,70000000,12,6,0,0,2,0,'2023-03-27 22:47:52',2647,NULL),
	(142,NULL,3,70000000,12,6,0,0,2,0,'2023-03-22 04:00:01',2075,NULL),
	(143,NULL,3,70000000,12,6,0,0,2,0,'2023-03-24 03:05:21',2201,NULL),
	(144,NULL,3,70000000,12,6,0,0,2,0,'2023-03-03 14:11:42',548,NULL),
	(145,NULL,3,70000000,12,6,0,0,2,0,'2023-03-02 18:53:40',513,NULL),
	(146,NULL,3,70000000,12,6,0,0,2,0,'2023-03-20 20:01:00',2016,NULL),
	(147,NULL,3,70000000,12,6,0,0,2,0,'2023-03-10 20:53:43',1214,NULL),
	(148,NULL,3,70000000,12,6,0,0,2,0,'2023-03-09 20:42:10',1103,NULL),
	(149,NULL,3,70000000,12,6,0,0,2,0,'2023-03-10 04:01:00',1150,NULL),
	(150,NULL,3,70000000,12,6,0,0,2,0,'2023-03-17 17:01:00',1840,NULL),
	(151,NULL,3,70000000,12,6,0,0,2,0,'2023-03-27 19:05:45',2598,NULL),
	(152,NULL,3,70000000,12,6,0,0,2,0,'2023-03-02 03:05:00',497,NULL),
	(153,NULL,3,70000000,12,6,0,0,2,0,'2023-03-16 23:24:07',1810,NULL),
	(154,NULL,3,70000000,12,6,0,0,2,0,'2023-03-04 22:15:48',653,NULL),
	(155,NULL,3,70000000,12,6,0,0,2,0,'2023-03-02 16:14:44',505,NULL),
	(156,NULL,3,70000000,12,6,0,0,2,0,'2023-03-19 11:39:00',1933,NULL),
	(157,NULL,3,70000000,12,6,0,0,2,0,'2023-03-08 22:11:10',1018,NULL),
	(158,NULL,3,70000000,12,6,0,0,2,0,'2023-03-04 23:21:00',656,NULL),
	(159,NULL,3,70000000,12,6,0,0,2,0,'2023-03-10 21:11:00',1224,NULL),
	(160,NULL,3,70000000,12,6,0,0,2,0,'2023-03-27 19:15:12',2601,NULL),
	(161,NULL,3,70000000,12,6,0,0,2,0,'2023-03-22 17:38:56',2099,NULL),
	(162,NULL,3,65000000,11,6,0,0,2,0,'2023-03-24 22:51:10',2261,NULL),
	(163,NULL,3,65000000,11,6,0,0,2,0,'2023-03-13 12:24:38',1558,NULL),
	(164,NULL,3,65000000,11,6,0,0,2,0,'2023-03-11 01:01:18',1255,NULL),
	(165,NULL,3,65000000,11,6,0,0,2,0,'2023-03-17 13:04:16',1824,NULL),
	(166,NULL,3,65000000,11,6,0,0,2,0,'2023-03-13 21:03:42',1642,NULL),
	(167,NULL,3,65000000,11,6,0,0,2,0,'2023-03-08 06:38:35',935,NULL),
	(168,NULL,3,65000000,11,6,0,0,2,0,'2023-03-03 05:17:22',538,NULL),
	(169,NULL,3,65000000,11,6,0,0,2,0,'2023-03-06 08:44:39',759,NULL),
	(170,NULL,3,65000000,11,6,0,0,2,0,'2023-03-14 08:43:11',1691,NULL),
	(171,NULL,3,65000000,11,6,0,0,2,0,'2023-03-24 16:09:45',2228,NULL),
	(172,NULL,3,65000000,11,6,0,0,2,0,'2023-03-27 12:59:32',2545,NULL),
	(173,NULL,3,65000000,11,6,0,0,2,0,'2023-03-09 19:01:00',1099,NULL),
	(174,NULL,3,65000000,11,6,0,0,2,0,'2023-03-20 13:11:01',1992,NULL),
	(175,NULL,3,65000000,11,6,0,0,2,0,'2023-03-17 17:52:58',1841,NULL),
	(176,NULL,3,65000000,11,6,0,0,2,0,'2023-03-24 17:01:00',2233,NULL),
	(177,NULL,3,65000000,11,6,0,0,2,0,'2023-03-26 16:42:58',2436,NULL),
	(178,NULL,3,65000000,11,6,0,0,2,0,'2023-03-27 17:11:00',2582,NULL),
	(179,NULL,3,65000000,11,6,0,0,2,0,'2023-03-05 15:33:55',685,NULL),
	(180,NULL,3,65000000,11,6,0,0,2,0,'2023-03-17 15:13:40',1830,NULL),
	(181,NULL,3,65000000,11,6,0,0,2,0,'2023-03-16 18:46:01',1794,NULL),
	(182,NULL,3,65000000,11,6,0,0,2,0,'2023-03-06 01:31:46',743,NULL),
	(183,NULL,3,65000000,11,6,0,0,2,0,'2023-03-13 22:04:34',1661,NULL),
	(184,NULL,3,65000000,11,6,0,0,2,0,'2023-03-08 11:10:42',946,NULL),
	(185,NULL,3,65000000,11,6,0,0,2,0,'2023-03-20 01:29:04',1971,NULL),
	(186,NULL,3,65000000,11,6,0,0,2,0,'2023-03-27 22:01:00',2650,NULL),
	(187,NULL,3,65000000,11,6,0,0,2,0,'2023-03-24 22:07:44',2260,NULL),
	(188,NULL,3,65000000,11,6,0,0,2,0,'2023-03-02 16:02:18',508,NULL),
	(189,NULL,3,65000000,11,6,0,0,2,0,'2023-03-13 16:02:26',1591,NULL),
	(190,NULL,3,65000000,11,6,0,0,2,0,'2023-03-26 19:07:45',2460,NULL),
	(191,NULL,3,65000000,11,6,0,0,2,0,'2023-03-01 14:56:50',450,NULL),
	(192,NULL,4,********,13,6,0,0,2,0,'2023-03-12 18:51:58',1450,NULL),
	(193,NULL,4,********,13,6,0,0,2,0,'2023-03-22 02:01:00',2073,NULL),
	(194,NULL,4,********,13,6,0,0,2,0,'2023-03-07 04:11:00',846,NULL),
	(195,NULL,4,********,13,6,0,0,2,0,'2023-03-02 17:22:02',510,NULL),
	(196,NULL,4,********,13,6,0,0,2,0,'2023-03-27 07:15:00',2532,NULL),
	(197,NULL,4,********,13,6,0,0,2,0,'2023-03-14 00:31:00',1683,NULL),
	(198,NULL,4,********,13,6,0,0,2,0,'2023-03-20 19:59:13',2010,NULL),
	(199,NULL,4,********,13,6,0,0,2,0,'2023-03-23 19:59:19',2169,NULL),
	(200,NULL,4,********,13,6,0,0,2,0,'2023-03-27 21:10:31',2628,NULL),
	(201,NULL,4,********,13,6,0,0,2,0,'2023-03-26 00:12:32',2363,NULL),
	(202,NULL,4,********,13,6,0,0,2,0,'2023-03-06 01:16:32',745,NULL),
	(203,NULL,4,********,13,6,0,0,2,0,'2023-03-07 00:55:12',834,NULL),
	(204,NULL,4,********,13,6,0,0,2,0,'2023-03-17 15:28:44',1832,NULL),
	(205,NULL,4,********,13,6,0,0,2,0,'2023-03-23 12:09:52',2136,NULL),
	(206,NULL,4,********,13,6,0,0,2,0,'2023-03-12 02:45:17',1371,NULL),
	(207,NULL,4,********,13,6,0,0,2,0,'2023-03-13 20:45:51',1630,NULL),
	(208,NULL,4,********,13,6,0,0,2,0,'2023-03-22 13:47:57',2087,NULL),
	(209,NULL,4,********,13,6,0,0,2,0,'2023-03-13 18:54:11',1611,NULL),
	(210,NULL,4,********,13,6,0,0,2,0,'2023-03-26 03:01:00',2381,NULL),
	(211,NULL,4,********,13,6,0,0,2,0,'2023-03-10 15:01:00',1179,NULL),
	(212,NULL,4,********,13,6,0,0,2,0,'2023-03-24 02:52:38',2198,NULL),
	(213,NULL,4,********,13,6,0,0,2,0,'2023-03-27 20:35:32',2613,NULL),
	(214,NULL,4,********,13,6,0,0,2,0,'2023-03-27 20:53:33',2617,NULL),
	(215,NULL,4,********,13,6,0,0,2,0,'2023-03-07 22:01:00',908,NULL),
	(216,NULL,4,********,13,6,0,0,2,0,'2023-03-18 03:16:07',1869,NULL),
	(217,NULL,4,********,13,6,0,0,2,0,'2023-03-13 21:51:24',1645,NULL),
	(218,NULL,4,********,13,6,0,0,2,0,'2023-03-09 10:57:30',1049,NULL),
	(219,NULL,4,********,13,6,0,0,2,0,'2023-03-15 13:26:07',1743,NULL),
	(220,NULL,4,********,13,6,0,0,2,0,'2023-03-12 17:34:33',1434,NULL),
	(221,NULL,4,********,13,6,0,0,2,0,'2023-03-05 20:12:41',715,NULL),
	(222,NULL,4,********,13,6,0,0,2,0,'2023-03-24 19:53:37',2241,NULL),
	(223,NULL,4,********,13,6,0,0,2,0,'2023-03-26 19:39:15',2465,NULL),
	(224,NULL,4,********,13,6,0,0,2,0,'2023-03-19 13:49:21',1936,NULL),
	(225,NULL,4,********,13,6,0,0,2,0,'2023-03-16 15:08:40',1785,NULL),
	(226,NULL,4,********,13,6,0,0,2,0,'2023-03-27 15:30:06',2561,NULL),
	(227,NULL,4,********,13,6,0,0,2,0,'2023-03-17 15:30:17',1834,NULL),
	(228,NULL,4,********,13,6,0,0,2,0,'2023-03-04 19:50:00',636,NULL),
	(229,NULL,4,********,13,6,0,0,2,0,'2023-03-18 15:01:00',1884,NULL),
	(230,NULL,4,********,13,6,0,0,2,0,'2023-03-17 02:01:00',1816,NULL),
	(231,NULL,4,********,13,6,0,0,2,0,'2023-03-13 02:15:00',1518,NULL),
	(232,NULL,4,********,13,6,0,0,2,0,'2023-03-17 14:26:44',1825,NULL),
	(233,NULL,4,********,13,6,0,0,2,0,'2023-03-10 07:02:53',1157,NULL),
	(234,NULL,4,********,13,6,0,0,2,0,'2023-03-07 16:20:34',872,NULL),
	(235,NULL,4,********,13,6,0,0,2,0,'2023-03-05 19:49:34',711,NULL),
	(236,NULL,4,********,13,6,0,0,2,0,'2023-03-05 16:19:32',690,NULL),
	(237,NULL,4,********,13,6,0,0,2,0,'2023-03-26 17:01:00',2448,NULL),
	(238,NULL,4,********,13,6,0,0,2,0,'2023-03-11 17:39:23',1303,NULL),
	(239,NULL,4,********,13,6,0,0,2,0,'2023-03-15 17:30:17',1748,NULL),
	(240,NULL,4,********,13,6,0,0,2,0,'2023-03-10 13:50:58',1173,NULL),
	(241,NULL,4,********,13,6,0,0,2,0,'2023-03-10 17:32:56',1190,NULL),
	(242,NULL,4,********,13,6,0,0,2,0,'2023-03-06 22:18:30',824,NULL),
	(243,NULL,4,********,13,6,0,0,2,0,'2023-03-10 17:27:25',1192,NULL),
	(244,NULL,4,********,13,6,0,0,2,0,'2023-03-26 01:06:30',2371,NULL),
	(245,NULL,4,********,13,6,0,0,2,0,'2023-03-11 16:50:24',1291,NULL),
	(246,NULL,4,********,13,6,0,0,2,0,'2023-03-09 01:01:01',1029,NULL),
	(247,NULL,4,********,13,6,0,0,2,0,'2023-03-08 01:01:01',925,NULL),
	(248,NULL,4,********,13,6,0,0,2,0,'2023-03-22 00:06:27',2068,NULL),
	(249,NULL,4,********,13,6,0,0,2,0,'2023-03-05 15:18:00',688,NULL),
	(250,NULL,4,********,13,6,0,0,2,0,'2023-03-22 11:10:32',2082,NULL),
	(251,NULL,4,********,13,6,0,0,2,0,'2023-03-13 13:57:57',1563,NULL),
	(252,NULL,5,********,14,6,0,0,2,0,'2023-03-07 18:00:03',884,NULL),
	(253,NULL,5,********,14,6,0,0,2,0,'2023-03-26 01:58:49',2373,NULL),
	(254,NULL,5,********,14,6,0,0,2,0,'2023-03-15 22:31:47',1764,NULL),
	(255,NULL,5,********,14,6,0,0,2,0,'2023-03-10 20:25:00',1216,NULL),
	(256,NULL,5,********,14,6,0,0,2,0,'2023-03-10 02:11:00',1145,NULL),
	(257,NULL,5,********,14,6,0,0,2,0,'2023-03-06 02:56:47',748,NULL),
	(258,NULL,5,********,14,6,0,0,2,0,'2023-03-26 17:48:57',2451,NULL),
	(259,NULL,5,********,14,6,0,0,2,0,'2023-03-23 15:41:20',2146,NULL),
	(260,NULL,5,********,14,6,0,0,2,0,'2023-03-12 15:22:43',1411,NULL),
	(261,NULL,5,********,14,6,0,0,2,0,'2023-03-01 09:16:55',451,NULL),
	(262,NULL,5,********,14,6,0,0,2,0,'2023-03-13 07:25:00',1529,NULL),
	(263,NULL,5,********,14,6,0,0,2,0,'2023-03-27 14:54:59',2554,NULL),
	(264,NULL,5,********,14,6,0,0,2,0,'2023-03-25 03:59:32',2278,NULL),
	(265,NULL,5,********,14,6,0,0,2,0,'2023-03-21 20:10:47',2061,NULL),
	(266,NULL,5,********,14,6,0,0,2,0,'2023-03-26 11:54:12',2400,NULL),
	(267,NULL,5,********,14,6,0,0,2,0,'2023-03-13 05:21:00',1527,NULL),
	(268,NULL,5,********,14,6,0,0,2,0,'2023-03-10 17:18:40',1196,NULL),
	(269,NULL,5,********,14,6,0,0,2,0,'2023-03-07 08:04:01',850,NULL),
	(270,NULL,5,********,14,6,0,0,2,0,'2023-03-21 13:01:00',2043,NULL),
	(271,NULL,5,********,14,6,0,0,2,0,'2023-03-25 02:53:55',2276,NULL),
	(272,NULL,5,********,14,6,0,0,2,0,'2023-03-16 13:07:49',1781,NULL),
	(273,NULL,5,********,14,6,0,0,2,0,'2023-03-17 18:08:15',1842,NULL),
	(274,NULL,5,********,14,6,0,0,2,0,'2023-03-12 16:31:05',1422,NULL),
	(275,NULL,5,********,14,6,0,0,2,0,'2023-02-28 14:52:19',453,NULL),
	(276,NULL,5,********,14,6,0,0,2,0,'2023-03-27 20:25:53',2618,NULL),
	(277,NULL,5,********,14,6,0,0,2,0,'2023-03-23 21:29:43',2177,NULL),
	(278,NULL,5,********,14,6,0,0,2,0,'2023-03-08 22:28:20',1020,NULL),
	(279,NULL,5,********,14,6,0,0,2,0,'2023-03-10 23:18:02',1240,NULL),
	(280,NULL,5,********,14,6,0,0,2,0,'2023-03-19 19:11:00',1951,NULL),
	(281,NULL,5,********,14,6,0,0,2,0,'2023-03-24 00:21:00',2193,NULL),
	(282,NULL,5,********,14,6,0,0,2,0,'2023-03-19 12:42:33',1934,NULL),
	(283,NULL,5,********,14,6,0,0,2,0,'2023-03-10 00:29:39',1131,NULL),
	(284,NULL,5,********,14,6,0,0,2,0,'2023-03-21 05:20:37',2036,NULL),
	(285,NULL,5,********,14,6,0,0,2,0,'2023-03-11 21:00:01',1332,NULL),
	(286,NULL,5,********,14,6,0,0,2,0,'2023-03-17 21:01:00',1851,NULL),
	(287,NULL,5,********,14,6,0,0,2,0,'2023-03-13 15:32:24',1581,NULL),
	(288,NULL,5,********,14,6,0,0,2,0,'2023-03-26 14:53:58',2419,NULL),
	(289,NULL,5,********,14,6,0,0,2,0,'2023-03-07 22:40:08',910,NULL),
	(290,NULL,5,********,14,6,0,0,2,0,'2023-03-23 23:29:02',2188,NULL),
	(291,NULL,5,********,14,6,0,0,2,0,'2023-03-10 17:48:26',1197,NULL),
	(292,NULL,5,********,14,6,0,0,2,0,'2023-03-27 16:27:09',2566,NULL),
	(293,NULL,5,********,14,6,0,0,2,0,'2023-03-11 22:15:55',1336,NULL),
	(294,NULL,5,********,14,6,0,0,2,0,'2023-03-23 22:20:07',2186,NULL),
	(295,NULL,5,********,14,6,0,0,2,0,'2023-03-07 16:27:10',874,NULL),
	(296,NULL,5,********,14,6,0,0,2,0,'2023-03-20 22:36:09',2025,NULL),
	(297,NULL,5,********,14,6,0,0,2,0,'2023-03-27 19:06:56',2602,NULL),
	(298,NULL,5,********,14,6,0,0,2,0,'2023-03-15 04:38:38',1734,NULL),
	(299,NULL,5,********,14,6,0,0,2,0,'2023-03-26 15:10:00',2430,NULL),
	(300,NULL,5,********,14,6,0,0,2,0,'2023-03-17 11:20:32',1822,NULL),
	(301,NULL,5,********,14,6,0,0,2,0,'2023-03-17 23:28:48',1861,NULL),
	(302,NULL,5,********,14,6,0,0,2,0,'2023-03-03 01:01:01',532,NULL),
	(303,NULL,5,********,14,6,0,0,2,0,'2023-03-20 18:01:00',2009,NULL),
	(304,NULL,5,********,14,6,0,0,2,0,'2023-03-12 10:44:32',1388,NULL),
	(305,NULL,5,********,14,6,0,0,2,0,'2023-03-18 00:24:24',1865,NULL),
	(306,NULL,5,********,14,6,0,0,2,0,'2023-03-05 22:51:54',726,NULL),
	(307,NULL,5,********,14,6,0,0,2,0,'2023-03-10 16:44:11',1187,NULL),
	(308,NULL,5,********,14,6,0,0,2,0,'2023-03-23 01:01:01',2121,NULL),
	(309,NULL,5,********,14,6,0,0,2,0,'2023-02-28 12:38:22',455,NULL),
	(310,NULL,5,********,14,6,0,0,2,0,'2023-03-26 23:22:59',2499,NULL),
	(311,NULL,5,********,14,6,0,0,2,0,'2023-03-08 02:14:37',927,NULL),
	(312,NULL,6,********,18,6,0,0,2,0,'2023-03-13 21:16:00',1648,NULL),
	(313,NULL,6,********,18,6,0,0,2,0,'2023-03-04 14:41:18',618,NULL),
	(314,NULL,6,********,18,6,0,0,2,0,'2023-03-27 19:57:56',2604,NULL),
	(315,NULL,6,********,18,6,0,0,2,0,'2023-03-27 13:54:53',2551,NULL),
	(316,NULL,6,********,18,6,0,0,2,0,'2023-03-11 19:23:03',1319,NULL),
	(317,NULL,6,********,18,6,0,0,2,0,'2023-03-03 20:51:00',573,NULL),
	(318,NULL,6,********,18,6,0,0,2,0,'2023-03-27 23:13:00',2658,NULL),
	(319,NULL,6,********,18,6,0,0,2,0,'2023-03-26 16:49:00',2442,NULL),
	(320,NULL,6,********,18,6,0,0,2,0,'2023-03-12 02:32:53',1372,NULL),
	(321,NULL,6,********,18,6,0,0,2,0,'2023-03-27 04:38:47',2524,NULL),
	(322,NULL,6,********,18,6,0,0,2,0,'2023-03-13 17:01:00',1602,NULL),
	(323,NULL,6,********,18,6,0,0,2,0,'2023-03-14 05:01:01',1689,NULL),
	(324,NULL,6,********,18,6,0,0,2,0,'2023-03-13 23:26:48',1672,NULL),
	(325,NULL,6,********,18,6,0,0,2,0,'2023-03-12 13:18:00',1401,NULL),
	(326,NULL,6,********,18,6,0,0,2,0,'2023-03-07 02:08:00',842,NULL),
	(327,NULL,6,********,18,6,0,0,2,0,'2023-03-01 13:51:36',457,NULL),
	(328,NULL,6,********,18,6,0,0,2,0,'2023-03-08 02:24:34',930,NULL),
	(329,NULL,6,********,18,6,0,0,2,0,'2023-03-11 00:01:00',1248,NULL),
	(330,NULL,6,********,18,6,0,0,2,0,'2023-03-25 17:15:36',2311,NULL),
	(331,NULL,6,********,18,6,0,0,2,0,'2023-03-26 07:27:45',2392,NULL),
	(332,NULL,6,********,18,6,0,0,2,0,'2023-03-16 15:09:06',1786,NULL),
	(333,NULL,6,********,18,6,0,0,2,0,'2023-03-17 20:22:54',1845,NULL),
	(334,NULL,6,********,18,6,0,0,2,0,'2023-03-13 16:37:30',1594,NULL),
	(335,NULL,6,********,18,6,0,0,2,0,'2023-03-24 19:01:10',2243,NULL),
	(336,NULL,6,********,18,6,0,0,2,0,'2023-03-26 00:21:00',2365,NULL),
	(337,NULL,6,********,18,6,0,0,2,0,'2023-03-20 08:14:38',1985,NULL),
	(338,NULL,6,********,18,6,0,0,2,0,'2023-03-13 01:17:17',1512,NULL),
	(339,NULL,6,********,18,6,0,0,2,0,'2023-03-12 04:11:00',1377,NULL),
	(340,NULL,6,********,18,6,0,0,2,0,'2023-03-27 05:25:32',2526,NULL),
	(341,NULL,6,********,18,6,0,0,2,0,'2023-03-21 13:56:23',2045,NULL),
	(342,NULL,6,********,18,6,0,0,2,0,'2023-03-26 23:16:00',2501,NULL),
	(343,NULL,6,********,18,6,0,0,2,0,'2023-03-25 19:06:46',2327,NULL),
	(344,NULL,6,********,18,6,0,0,2,0,'2023-03-25 01:33:51',2274,NULL),
	(345,NULL,6,********,18,6,0,0,2,0,'2023-03-27 00:57:49',2508,NULL),
	(346,NULL,6,********,18,6,0,0,2,0,'2023-03-11 15:27:45',1283,NULL),
	(347,NULL,6,********,18,6,0,0,2,0,'2023-03-09 15:53:50',1068,NULL),
	(348,NULL,6,********,18,6,0,0,2,0,'2023-03-23 01:01:00',2124,NULL),
	(349,NULL,6,********,18,6,0,0,2,0,'2023-03-10 02:43:00',1147,NULL),
	(350,NULL,6,********,18,6,0,0,2,0,'2023-03-26 21:32:14',2493,NULL),
	(351,NULL,6,********,18,6,0,0,2,0,'2023-03-11 00:56:06',1251,NULL),
	(352,NULL,6,********,18,6,0,0,2,0,'2023-03-06 01:45:55',746,NULL),
	(353,NULL,6,********,18,6,0,0,2,0,'2023-03-16 21:59:34',1805,NULL),
	(354,NULL,6,********,18,6,0,0,2,0,'2023-03-19 19:52:13',1952,NULL),
	(355,NULL,6,********,18,6,0,0,2,0,'2023-03-24 18:01:00',2237,NULL),
	(356,NULL,6,********,18,6,0,0,2,0,'2023-03-23 22:56:51',2187,NULL),
	(357,NULL,6,********,18,6,0,0,2,0,'2023-03-25 02:12:18',2277,NULL),
	(358,NULL,6,********,18,6,0,0,2,0,'2023-03-06 01:34:47',747,NULL),
	(359,NULL,6,********,18,6,0,0,2,0,'2023-03-27 00:49:00',2509,NULL),
	(360,NULL,6,********,18,6,0,0,2,0,'2023-03-18 16:02:00',1890,NULL),
	(361,NULL,6,********,18,6,0,0,2,0,'2023-03-27 00:11:00',2510,NULL),
	(362,NULL,6,********,18,6,0,0,2,0,'2023-03-05 22:38:51',729,NULL),
	(363,NULL,6,********,18,6,0,0,2,0,'2023-03-10 00:43:17',1134,NULL),
	(364,NULL,6,********,18,6,0,0,2,0,'2023-03-24 01:01:00',2196,NULL),
	(365,NULL,6,********,18,6,0,0,2,0,'2023-03-10 04:13:04',1152,NULL),
	(366,NULL,6,********,18,6,0,0,2,0,'2023-03-05 14:41:29',680,NULL),
	(367,NULL,6,********,18,6,0,0,2,0,'2023-03-10 09:41:37',1161,NULL),
	(368,NULL,6,********,18,6,0,0,2,0,'2023-03-15 06:55:50',1737,NULL),
	(369,NULL,6,********,18,6,0,0,2,0,'2023-03-10 21:49:46',1227,NULL),
	(370,NULL,6,********,18,6,0,0,2,0,'2023-03-27 23:01:00',2661,NULL),
	(371,NULL,6,********,18,6,0,0,2,0,'2023-03-24 00:38:15',2195,NULL),
	(372,NULL,7,********,19,3,0,0,3,0,'2023-03-14 23:11:02',1723,NULL),
	(373,NULL,7,********,19,3,0,0,3,0,'2023-03-27 18:01:01',2587,NULL),
	(374,NULL,7,********,19,3,0,0,3,0,'2023-03-22 22:19:54',2113,NULL),
	(375,NULL,7,********,19,3,0,0,3,0,'2023-03-09 01:37:11',1031,NULL),
	(376,NULL,7,********,19,3,0,0,3,0,'2023-03-25 00:28:26',2267,NULL),
	(377,NULL,7,********,19,3,0,0,3,0,'2023-03-21 18:11:00',2059,NULL),
	(378,NULL,7,********,19,3,0,0,3,0,'2023-03-07 14:01:01',863,NULL),
	(379,NULL,7,********,19,3,0,0,3,0,'2023-03-10 15:02:35',1182,NULL),
	(380,NULL,7,********,19,3,0,0,3,0,'2023-03-24 17:01:00',2235,NULL),
	(381,NULL,7,********,19,3,0,0,3,0,'2023-03-13 13:00:01',1567,NULL),
	(382,NULL,7,********,19,3,0,0,3,0,'2023-03-27 10:13:35',2538,NULL),
	(383,NULL,7,********,19,3,0,0,3,0,'2023-03-08 22:01:00',1022,NULL),
	(384,NULL,7,********,19,3,0,0,3,0,'2023-03-26 00:01:00',2368,NULL),
	(385,NULL,7,********,19,3,0,0,3,0,'2023-03-19 18:11:01',1948,NULL),
	(386,NULL,7,********,19,3,0,0,3,0,'2023-03-27 18:50:00',2589,NULL),
	(387,NULL,7,********,19,3,0,0,3,0,'2023-03-12 18:03:40',1453,NULL),
	(388,NULL,7,********,19,3,0,0,3,0,'2023-03-26 04:01:00',2382,NULL),
	(389,NULL,7,********,19,3,0,0,3,0,'2023-03-11 06:53:21',1263,NULL),
	(390,NULL,7,********,19,3,0,0,3,0,'2023-03-27 11:01:01',2540,NULL),
	(391,NULL,7,********,19,3,0,0,3,0,'2023-03-08 11:01:00',948,NULL),
	(392,NULL,7,********,19,3,0,0,3,0,'2023-03-18 15:31:59',1887,NULL),
	(393,NULL,7,********,19,3,0,0,3,0,'2023-03-04 14:59:08',619,NULL),
	(394,NULL,7,********,19,3,0,0,3,0,'2023-03-23 17:45:57',2158,NULL),
	(395,NULL,7,********,19,3,0,0,3,0,'2023-03-15 18:11:00',1752,NULL),
	(396,NULL,7,********,19,3,0,0,3,0,'2023-03-15 18:01:00',1753,NULL),
	(397,NULL,7,********,19,3,0,0,3,0,'2023-03-25 18:48:33',2320,NULL),
	(398,NULL,7,********,19,3,0,0,3,0,'2023-03-12 19:34:19',1461,NULL),
	(399,NULL,7,********,19,3,0,0,3,0,'2023-03-24 22:01:05',2263,NULL),
	(400,NULL,7,********,19,3,0,0,3,0,'2023-03-24 20:01:01',2247,NULL),
	(401,NULL,7,********,19,3,0,0,3,0,'2023-03-09 16:01:00',1077,NULL),
	(402,NULL,7,********,19,3,0,0,3,0,'2023-03-24 20:04:29',2250,NULL),
	(403,NULL,7,********,19,3,0,0,3,0,'2023-10-22 15:30:20',2708,NULL),
	(404,NULL,7,********,19,3,0,0,3,0,'2023-03-24 15:01:00',2224,NULL),
	(405,NULL,7,********,19,3,0,0,3,0,'2023-03-05 20:12:45',717,NULL),
	(406,NULL,7,********,19,3,0,0,3,0,'2023-03-13 23:47:00',1674,NULL),
	(407,NULL,7,********,19,3,0,0,3,0,'2023-03-24 18:53:17',2239,NULL),
	(408,NULL,7,********,19,3,0,0,3,0,'2023-03-08 05:40:59',933,NULL),
	(409,NULL,7,********,19,3,0,0,3,0,'2023-03-23 05:41:01',2127,NULL),
	(410,NULL,7,********,19,3,0,0,3,0,'2023-03-06 14:29:39',770,NULL),
	(411,NULL,7,********,19,3,0,0,3,0,'2023-03-14 05:01:01',1690,NULL),
	(412,NULL,7,********,19,3,0,0,3,0,'2023-03-08 20:01:00',1008,NULL),
	(413,NULL,7,********,19,3,0,0,3,0,'2023-03-07 00:01:00',836,NULL),
	(414,NULL,7,********,19,3,0,0,3,0,'2023-03-03 01:01:00',534,NULL),
	(415,NULL,7,********,19,3,0,0,3,0,'2023-03-16 00:21:00',1769,NULL),
	(416,NULL,7,********,19,3,0,0,3,0,'2023-03-13 00:30:00',1501,NULL),
	(417,NULL,7,********,19,3,0,0,3,0,'2023-03-15 03:21:13',1732,NULL),
	(418,NULL,7,********,19,3,0,0,3,0,'2023-03-11 05:49:09',1261,NULL),
	(419,NULL,7,********,19,3,0,0,3,0,'2023-03-09 10:00:01',1051,NULL),
	(420,NULL,7,********,19,3,0,0,3,0,'2023-03-08 17:27:03',986,NULL),
	(421,NULL,7,********,19,3,0,0,3,0,'2023-03-27 06:01:00',2530,NULL),
	(422,NULL,7,********,19,3,0,0,3,0,'2023-03-08 16:42:20',982,NULL),
	(423,NULL,7,********,19,3,0,0,3,0,'2023-03-11 23:32:00',1341,NULL),
	(424,NULL,7,********,19,3,0,0,3,0,'2023-03-17 20:32:28',1847,NULL),
	(425,NULL,7,********,19,3,0,0,3,0,'2023-03-04 12:33:47',613,NULL),
	(426,NULL,7,********,19,3,0,0,3,0,'2023-03-20 14:39:13',1995,NULL),
	(427,NULL,7,********,19,3,0,0,3,0,'2023-03-12 16:01:00',1425,NULL),
	(428,NULL,7,********,19,3,0,0,3,0,'2023-03-08 12:38:05',949,NULL),
	(429,NULL,7,********,19,3,0,0,3,0,'2023-03-04 16:35:42',623,NULL),
	(430,NULL,7,********,19,3,0,0,3,0,'2023-03-24 20:29:46',2251,NULL),
	(431,NULL,7,********,19,3,0,0,3,0,'2023-03-11 16:05:53',1294,NULL),
	(432,NULL,7,********,19,3,0,0,3,0,'2023-03-07 13:11:00',861,NULL),
	(433,NULL,7,********,19,3,0,0,3,0,'2023-03-23 16:57:20',2152,NULL),
	(434,NULL,7,********,19,3,0,0,3,0,'2023-03-22 14:40:00',2091,NULL),
	(435,NULL,7,********,19,3,0,0,3,0,'2023-03-09 15:01:00',1070,NULL),
	(436,NULL,7,********,19,3,0,0,3,0,'2023-03-20 14:49:30',1998,NULL),
	(437,NULL,7,********,19,3,0,0,3,0,'2023-03-26 19:29:00',2466,NULL),
	(438,NULL,7,********,19,3,0,0,3,0,'2023-03-26 14:52:45',2421,NULL),
	(439,NULL,7,********,19,3,0,0,3,0,'2023-02-11 14:53:49',4078,NULL),
	(440,NULL,7,********,19,3,0,0,3,0,'2023-03-11 18:01:00',1312,NULL),
	(441,NULL,7,********,19,3,0,0,3,0,'2023-03-08 23:33:17',1026,NULL),
	(442,NULL,7,********,19,3,0,0,3,0,'2023-03-17 00:48:51',1813,NULL),
	(443,NULL,7,********,19,3,0,0,3,0,'2023-03-23 00:04:02',2117,NULL),
	(444,NULL,7,********,19,3,0,0,3,0,'2023-03-25 23:01:00',2361,NULL),
	(445,NULL,7,********,19,3,0,0,3,0,'2023-03-26 14:11:00',2423,NULL),
	(446,NULL,7,********,19,3,0,0,3,0,'2023-03-06 23:01:00',831,NULL),
	(447,NULL,7,********,19,3,0,0,3,0,'2023-03-27 13:01:00',2552,NULL),
	(448,NULL,7,********,19,3,0,0,3,0,'2023-03-08 14:23:14',953,NULL),
	(449,NULL,7,********,19,3,0,0,3,0,'2023-03-12 01:17:41',1360,NULL),
	(450,NULL,7,********,19,3,0,0,3,0,'2023-03-22 08:09:33',2077,NULL),
	(451,NULL,7,********,19,3,0,0,3,0,'2023-03-14 21:02:00',1720,NULL);

/*!40000 ALTER TABLE `apartments` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `arena_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staticId` int(11) DEFAULT NULL,
  `sessionId` varchar(12) DEFAULT NULL,
  `lobbyId` varchar(12) DEFAULT NULL,
  `action` varchar(45) DEFAULT NULL,
  `args` blob DEFAULT NULL,
  `date` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `staticId` (`staticId`),
  KEY `sessionId` (`sessionId`),
  KEY `lobbyId` (`lobbyId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Areana player action history log';



CREATE TABLE `arena_matches_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sessionId` varchar(12) DEFAULT NULL,
  `lobbyId` varchar(12) DEFAULT NULL,
  `creator` int(11) DEFAULT NULL,
  `leader` int(11) DEFAULT NULL,
  `gamemode` varchar(45) DEFAULT NULL,
  `bank` bigint(20) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `createDate` datetime DEFAULT NULL,
  `startDate` datetime DEFAULT NULL,
  `endDate` datetime DEFAULT NULL,
  `lobbyData` blob DEFAULT NULL,
  `gameData` blob DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `sessionId` (`sessionId`),
  KEY `lobbyId` (`lobbyId`),
  KEY `creator` (`creator`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Arena matches log';



CREATE TABLE `arrest_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `time` mediumint(8) unsigned DEFAULT NULL,
  `wantedLevel` tinyint(1) unsigned DEFAULT NULL,
  `arrestBy` mediumint(7) unsigned DEFAULT NULL,
  `employeeOrg` tinyint(2) unsigned DEFAULT NULL,
  `employeeRank` int(10) unsigned DEFAULT NULL,
  `intruderOrg` tinyint(2) unsigned DEFAULT NULL,
  `intruderFamily` mediumint(8) unsigned DEFAULT NULL,
  `comment` varchar(50) DEFAULT NULL,
  `isCriminalRecord` tinyint(1) DEFAULT 0,
  `pledge` int(8) DEFAULT 0,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `arrestBy` (`arrestBy`),
  KEY `accountId` (`accountId`),
  KEY `date` (`date`),
  KEY `isCriminalRecord` (`isCriminalRecord`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `atm` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `price` int(10) unsigned DEFAULT *********,
  `profit` bigint(12) unsigned NOT NULL DEFAULT 0,
  `condition` smallint(5) unsigned NOT NULL DEFAULT 0,
  `cashInPercent` varchar(5) NOT NULL DEFAULT '1.0',
  `cashOutPercent` varchar(5) NOT NULL DEFAULT '1.0',
  `mobilePercent` varchar(5) NOT NULL DEFAULT '1.0',
  `materials` smallint(5) unsigned NOT NULL DEFAULT 1000,
  `cash` bigint(20) unsigned NOT NULL DEFAULT 2500000,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `userId` (`accountId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`),
  KEY `marketplaceId` (`marketplaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Банкоматы';



CREATE TABLE `attackonthearmy` (
  `id` tinyint(2) NOT NULL,
  `isFortZancudo` tinyint(1) NOT NULL,
  `rest` smallint(6) NOT NULL DEFAULT 0,
  `maxMats` smallint(6) NOT NULL DEFAULT 0,
  `typeColor` varchar(8) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

LOCK TABLES `attackonthearmy` WRITE;
/*!40000 ALTER TABLE `attackonthearmy` DISABLE KEYS */;

INSERT INTO `attackonthearmy` (`id`, `isFortZancudo`, `rest`, `maxMats`, `typeColor`)
VALUES
	(0,1,4000,400,'blue'),
	(1,1,2000,200,'blue'),
	(2,1,2000,200,'green'),
	(3,1,4000,400,'green'),
	(4,1,4000,400,'red'),
	(0,0,4000,200,'blue'),
	(1,0,4500,225,'blue'),
	(2,0,6000,300,'green'),
	(3,0,4500,225,'green'),
	(4,0,5500,275,'red'),
	(5,0,6500,325,'red');

/*!40000 ALTER TABLE `attackonthearmy` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `attackonthearmy_resettime` (
  `hours` blob DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

LOCK TABLES `attackonthearmy_resettime` WRITE;
/*!40000 ALTER TABLE `attackonthearmy_resettime` DISABLE KEYS */;

INSERT INTO `attackonthearmy_resettime` (`hours`)
VALUES
	(X'5B5D');

/*!40000 ALTER TABLE `attackonthearmy_resettime` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `attackonthearmytime` (
  `isFortZancudo` tinyint(1) NOT NULL,
  `days` varchar(16) NOT NULL DEFAULT '[]',
  `time` varchar(65) NOT NULL DEFAULT '[]'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

LOCK TABLES `attackonthearmytime` WRITE;
/*!40000 ALTER TABLE `attackonthearmytime` DISABLE KEYS */;

INSERT INTO `attackonthearmytime` (`isFortZancudo`, `days`, `time`)
VALUES
	(1,'[2,4,6,1,3,5,7]','[15,16,17,18,19,20,\"0\",2,1,3,4,5,6,7,8,9,10,11,12,13,14,21,22,23]'),
	(0,'[1,3,5,2,4,7,6]','[24,18,19,20,21,22,17,16,15,14,13,12,11,10,9,23,8,7,6,5,4,3]');

/*!40000 ALTER TABLE `attackonthearmytime` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `auto_shops` (
  `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(50) DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `price` int(11) unsigned DEFAULT 5000000,
  `cash` bigint(12) unsigned DEFAULT 0,
  `bank` bigint(12) unsigned DEFAULT 0,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `pickupX` varchar(10) DEFAULT NULL,
  `pickupY` varchar(10) DEFAULT NULL,
  `pickupZ` varchar(10) DEFAULT NULL,
  `camPosX` varchar(10) DEFAULT NULL,
  `camPosY` varchar(10) DEFAULT NULL,
  `camPosZ` varchar(10) DEFAULT NULL,
  `vehPosX` varchar(10) DEFAULT NULL,
  `vehPosY` varchar(10) DEFAULT NULL,
  `vehPosZ` varchar(10) DEFAULT NULL,
  `exitPosX` varchar(10) DEFAULT NULL,
  `exitPosY` varchar(10) DEFAULT NULL,
  `exitPosZ` varchar(10) DEFAULT NULL,
  `warehouse` blob DEFAULT NULL,
  `prices` blob DEFAULT NULL,
  `mafiaId` tinyint(2) unsigned DEFAULT 0,
  `mafiaCash` bigint(12) unsigned DEFAULT 0,
  `defaultMafiaId` tinyint(2) unsigned DEFAULT 0,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `title` (`title`),
  KEY `accountId` (`accountId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`),
  KEY `marketplaceId` (`marketplaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Автосалоны';

LOCK TABLES `auto_shops` WRITE;
/*!40000 ALTER TABLE `auto_shops` DISABLE KEYS */;

INSERT INTO `auto_shops` (`id`, `title`, `accountId`, `price`, `cash`, `bank`, `posX`, `posY`, `posZ`, `pickupX`, `pickupY`, `pickupZ`, `camPosX`, `camPosY`, `camPosZ`, `vehPosX`, `vehPosY`, `vehPosZ`, `exitPosX`, `exitPosY`, `exitPosZ`, `warehouse`, `prices`, `mafiaId`, `mafiaCash`, `defaultMafiaId`, `paidUntil`, `auctionId`, `marketplaceId`)
VALUES
	(1,'Premium Deluxe Motorsport',NULL,*********,48,3910066,'-42.4470','-1108.125','26.4371','-43.2257','-1104.3775','26.4223','-54.5176','-1097.8811','27.2223','-44.4430','-1097.0700','25.7921','-39.0841','-1110.2082','26.43834',NULL,NULL,16,0,13,'2023-03-14 18:31:00',1710,NULL),
	(2,'Albany Motors',NULL,*********,6765038,96,'-43.8232','-1672.3395','29.4916','-41.4668','-1674.7248','29.4426','205.6479','-995.3928','-99.0000','200.0722','-1000.3183','-99.7206','-42.3902','-1675.7521','29.4149',NULL,NULL,16,0,13,'2023-03-19 18:01:00',1947,NULL),
	(3,'Sanders Motorcycles',NULL,*********,********,********,'284.2200','-1148.0629','29.2917','286.8426','-1148.0617','29.2919','205.6479','-995.3928','-99.0000','200.0722','-1000.3183','-99.7206','286.9196','-1145.1135','29.2922',NULL,NULL,16,0,13,'2023-03-25 18:01:01',2316,NULL),
	(4,'Benefactor',NULL,*********,10340000,39090000,'-65.7318','62.2005','71.9430','-69.1555','63.4111','71.89088','205.6479','-995.3928','-99.0000','200.0722','-1000.3183','-99.7206','-70.5683','61.5631','71.8878',NULL,NULL,16,0,13,'2023-03-25 19:46:00',2324,NULL),
	(5,'Vapid',NULL,*********,2300000,8673500,'-175.1157','-1158.6243','23.8134','-177.2259','-1158.6226','23.8136','205.6479','-995.3928','-99.0000','200.0722','-1000.3183','-99.7206','-177.1693','-1156.9174','23.1135',NULL,NULL,16,0,13,'2023-03-26 18:51:00',2452,NULL),
	(6,'Luxury Autos',NULL,*********,0,0,'-804.03253','-222.0241','37.2394','-803.2722','-224.0497','37.2249','205.6479','-995.3928','-99.0000','200.0722','-1000.3183','-99.7206','-805.6228','-225.2220','37.2264',NULL,NULL,16,0,13,'2023-03-21 14:02:51',2049,NULL),
	(7,'JoBuilt Commercials',NULL,*********,102984000,681700,'-911.0419','-2037.5035','9.4049','-915.3103','-2038.3728','9.4049','-902.2913','-2039.0397','9.2991','-899.7716','-2053.3850','9.3844','-912.7484','-2040.7684','9.4049',NULL,NULL,16,0,13,'2023-03-15 18:11:00',1751,NULL),
	(8,'Majestic Motors',NULL,*********,0,0,'-477.1994','-94.4940','39.0122','-474.1665','-95.2992','39.0186','205.6479','-995.3928','-99.0000','200.0722','-1000.3183','-99.7206','-474.6557','-99.1739','38.8256',NULL,NULL,16,0,13,NULL,NULL,NULL),
	(9,'Dock',NULL,*********,0,0,'-704.518','-1395.006','5.150','-704.19','-1398.45','5.49',' -855.066','-1431.86','4.388','-854.515','-1418.560','0.390','-705.31','-1399.69','5.15',NULL,NULL,16,0,13,NULL,NULL,NULL),
	(10,'Elitas',NULL,*********,0,0,'-895.36','-2400.60','14.02','-893.328','-2402.089','14.02','-1652.351','-3129.026','16.362','-1652.521','-3142.891','14.6','-894.22','-2403.53','14.0',NULL,NULL,16,0,13,NULL,NULL,NULL);

/*!40000 ALTER TABLE `auto_shops` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `bank` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `pin` varchar(4) DEFAULT NULL,
  `amount` bigint(12) unsigned NOT NULL DEFAULT 0,
  `tariff` enum('Standart','Premium','VIP') NOT NULL DEFAULT 'Standart',
  `cashback` int(10) unsigned NOT NULL DEFAULT 0,
  `monthTransfers` bigint(20) unsigned NOT NULL DEFAULT 0,
  `monthOuts` bigint(20) unsigned NOT NULL DEFAULT 0,
  `estimatedDate` datetime DEFAULT NULL,
  `blocked` tinyint(1) unsigned DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `amount` (`amount`),
  KEY `accountId` (`accountId`),
  KEY `tariff` (`tariff`),
  KEY `estimatedDate` (`estimatedDate`),
  KEY `blocked` (`blocked`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Банковские счета';



CREATE TABLE `banlist` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `socialClub` varchar(16) DEFAULT NULL,
  `rgscId` int(10) unsigned DEFAULT NULL,
  `hwid` varchar(128) DEFAULT NULL,
  `ip` varchar(16) DEFAULT NULL,
  `cheatPath` varchar(128) DEFAULT NULL,
  `banStart` datetime DEFAULT NULL,
  `banEnd` datetime DEFAULT NULL,
  `banReason` varchar(128) DEFAULT NULL,
  `banBy` mediumint(7) unsigned DEFAULT NULL,
  `unbanBy` mediumint(7) unsigned DEFAULT NULL,
  `unbanReason` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ip` (`ip`),
  KEY `banStart` (`banStart`),
  KEY `banEnd` (`banEnd`),
  KEY `banReason` (`banReason`),
  KEY `banBy` (`banBy`),
  KEY `unbanBy` (`unbanBy`),
  KEY `hwid` (`hwid`),
  KEY `userId` (`userId`),
  KEY `accountId` (`accountId`),
  KEY `socialClub` (`socialClub`),
  KEY `unbanReason` (`unbanReason`),
  KEY `rgscId` (`rgscId`),
  KEY `cheatPath` (`cheatPath`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Банлист';



CREATE TABLE `barber_shops` (
  `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(24) DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `price` int(11) unsigned DEFAULT 5000000,
  `cash` bigint(12) unsigned DEFAULT 0,
  `bank` bigint(12) unsigned DEFAULT 0,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `materials` mediumint(7) unsigned DEFAULT 5000,
  `prices` blob DEFAULT NULL,
  `discounts` blob DEFAULT NULL,
  `mafiaId` tinyint(2) unsigned DEFAULT 0,
  `mafiaCash` bigint(12) unsigned DEFAULT 0,
  `defaultMafiaId` tinyint(2) unsigned DEFAULT 0,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `title` (`title`),
  KEY `accountId` (`accountId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`),
  KEY `marketplaceId` (`marketplaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Барбершопы';

LOCK TABLES `barber_shops` WRITE;
/*!40000 ALTER TABLE `barber_shops` DISABLE KEYS */;

INSERT INTO `barber_shops` (`id`, `title`, `accountId`, `price`, `cash`, `bank`, `posX`, `posY`, `posZ`, `materials`, `prices`, `discounts`, `mafiaId`, `mafiaCash`, `defaultMafiaId`, `paidUntil`, `auctionId`, `marketplaceId`)
VALUES
	(1,'Hair On Hawick',NULL,********,********,********,'-33.5109','-144.5895','57.1084',3766,NULL,NULL,16,0,13,'2023-03-09 19:31:01',1096,NULL),
	(2,'Herr Kutz',NULL,********,********,6242692,'-285.2977','6230.8906','31.4904',3919,NULL,NULL,16,0,13,'2023-03-14 19:01:00',1712,NULL),
	(3,'Herr Kutz',NULL,********,235281,470497,'1205.0393','-473.9316','66.1026',4500,NULL,NULL,16,0,13,'2023-03-15 20:11:01',1759,NULL),
	(4,'Herr Kutz',NULL,********,262580,2081,'134.5068','-1714.9979','29.287',8075,NULL,NULL,18,0,13,'2023-03-27 19:11:00',2593,NULL),
	(5,'O Sheas',NULL,********,356130,556917,'1932.1311','3722.397','32.8213',1947,NULL,NULL,15,0,13,'2023-03-20 21:09:00',2018,NULL),
	(6,'Bob Mule',NULL,*********,********,********,'-823.6847','-190.5127','37.6022',1765,NULL,NULL,16,0,13,'2023-03-08 19:01:00',998,NULL),
	(7,'Herr Kutz',NULL,********,20647692,12015859,'-1289.4718','-1114.0133','6.7197',1760,NULL,NULL,16,0,13,'2023-03-18 19:21:00',1901,NULL);

/*!40000 ALTER TABLE `barber_shops` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `biker_zones_reset` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reset_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf16 COLLATE=utf16_general_ci;



CREATE TABLE `biz_items` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(20) DEFAULT NULL,
  `bizId` tinyint(3) unsigned NOT NULL DEFAULT 0,
  `itemId` varchar(30) NOT NULL DEFAULT '0',
  `count` int(10) unsigned DEFAULT NULL,
  `price` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni` (`type`,`bizId`,`itemId`),
  KEY `type` (`type`),
  KEY `bizId` (`bizId`),
  KEY `itemId` (`itemId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;



CREATE TABLE `bk_bets` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `amount` mediumint(8) unsigned DEFAULT NULL,
  `coefficient` varchar(10) DEFAULT NULL,
  `lines` tinyint(2) unsigned DEFAULT NULL,
  `status` enum('new','win','lose','return','error','cancel') DEFAULT 'new',
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `date` (`date`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `bk_bets_old` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `fixtureId` int(10) unsigned DEFAULT NULL,
  `oddType` varchar(50) DEFAULT NULL,
  `amount` bigint(20) unsigned DEFAULT 0,
  `status` enum('new','done','return','error') DEFAULT 'new',
  `date` datetime DEFAULT NULL,
  KEY `id` (`id`),
  KEY `userId` (`userId`),
  KEY `fixtureId` (`fixtureId`),
  KEY `oddType` (`oddType`),
  KEY `accountId` (`accountId`),
  KEY `date` (`date`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `bk_events` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` int(10) unsigned DEFAULT NULL,
  `betId` int(10) unsigned DEFAULT 0,
  `fixtureId` int(10) unsigned DEFAULT 0,
  `eventId` mediumint(8) unsigned DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `paramId` varchar(10) DEFAULT NULL,
  `coefficient` varchar(10) DEFAULT NULL,
  KEY `id` (`id`),
  KEY `fixtureId` (`fixtureId`),
  KEY `betId` (`betId`),
  KEY `accountId` (`accountId`),
  KEY `eventId` (`eventId`),
  KEY `paramId` (`paramId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `bk_ratings` (
  `position` tinyint(2) unsigned DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `value` bigint(20) DEFAULT NULL,
  `period` enum('day','month','total') DEFAULT NULL,
  `type` enum('bet','profit') DEFAULT NULL,
  KEY `accountId` (`accountId`),
  KEY `period` (`period`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `bk_stats` (
  `period` enum('day','month','total') DEFAULT NULL,
  `betCount` mediumint(8) unsigned DEFAULT NULL,
  `betAmount` bigint(20) unsigned DEFAULT NULL,
  `winCount` mediumint(8) unsigned DEFAULT NULL,
  `winAmount` bigint(20) unsigned DEFAULT NULL,
  KEY `period` (`period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `burger_leaderboard` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` int(11) DEFAULT NULL,
  `amount` mediumint(9) DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci COMMENT='Статистика крутых лидеров по созданию бургеров';



CREATE TABLE `businesses_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `income` int(11) NOT NULL DEFAULT 0 COMMENT 'Выручка',
  `netProfit` int(11) NOT NULL DEFAULT 0 COMMENT 'Прибыль',
  `averageCheck` int(11) NOT NULL DEFAULT 0 COMMENT 'Средний чек',
  `salesCount` int(11) NOT NULL DEFAULT 0 COMMENT 'Количество продаж',
  `biz` varchar(50) NOT NULL,
  `date` date NOT NULL DEFAULT curdate(),
  PRIMARY KEY (`id`) USING BTREE,
  KEY `date` (`date`) USING BTREE,
  KEY `biz` (`biz`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Статистика бизнесов';



CREATE TABLE `capture_events_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `captureId` int(11) NOT NULL,
  `type` varchar(15) DEFAULT NULL,
  `fromStaticId` int(11) NOT NULL,
  `toStaticId` int(11) NOT NULL,
  `fromFactionId` int(11) NOT NULL,
  `toFactionId` int(11) NOT NULL,
  `value` bigint(20) NOT NULL DEFAULT 0,
  `createdAt` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `capture_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `captureId` int(11) DEFAULT NULL,
  `defendersId` int(11) DEFAULT 0,
  `attackersId` int(11) NOT NULL DEFAULT 0,
  `gangZoneId` int(11) NOT NULL DEFAULT 0,
  `winner` int(11) NOT NULL DEFAULT 0,
  `startAt` datetime NOT NULL DEFAULT current_timestamp(),
  `endAt` datetime DEFAULT NULL,
  `uniqueDefenders` int(11) NOT NULL DEFAULT 0,
  `uniqueAttackers` int(11) NOT NULL DEFAULT 0,
  `status` varchar(15) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `captures_admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `adminId` int(11) DEFAULT NULL,
  `adminLvl` int(11) NOT NULL DEFAULT 0,
  `adminName` varchar(50) DEFAULT NULL,
  `message` varchar(300) NOT NULL,
  `comment` varchar(300) NOT NULL,
  `translated` int(11) DEFAULT 0,
  `translateData` text DEFAULT '{}',
  `createDate` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `captures_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `adminId` int(11) DEFAULT NULL,
  `message` varchar(300) NOT NULL,
  `translated` int(11) DEFAULT 0,
  `translateData` text DEFAULT '{}',
  `fractionIds` text NOT NULL,
  `type` varchar(50) NOT NULL,
  `createDate` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `captures_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `attackerId` tinyint(4) DEFAULT NULL,
  `defenderId` tinyint(4) DEFAULT NULL,
  `zoneId` tinyint(4) DEFAULT NULL,
  `startTime` bigint(20) DEFAULT NULL,
  `createTime` bigint(20) DEFAULT NULL,
  `peopleCount` tinyint(4) DEFAULT NULL,
  `caliberId` smallint(6) DEFAULT NULL,
  `attackersIds` blob DEFAULT NULL,
  `defendersIds` blob DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `attackerAdditionalTime` int(11) DEFAULT NULL,
  `defenderAdditionalTime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `carwash_shops` (
  `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(20) DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `price` int(11) unsigned DEFAULT 5000000,
  `cash` bigint(12) unsigned DEFAULT 0,
  `bank` bigint(12) unsigned DEFAULT 0,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `pickupX` varchar(10) DEFAULT NULL,
  `pickupY` varchar(10) DEFAULT NULL,
  `pickupZ` varchar(10) DEFAULT NULL,
  `materials` mediumint(7) unsigned DEFAULT 5000,
  `prices` blob DEFAULT NULL,
  `mafiaId` tinyint(2) unsigned DEFAULT 0,
  `mafiaCash` bigint(12) unsigned DEFAULT 0,
  `defaultMafiaId` tinyint(2) unsigned DEFAULT 0,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`),
  KEY `marketplaceId` (`marketplaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC COMMENT='Автомойки';

LOCK TABLES `carwash_shops` WRITE;
/*!40000 ALTER TABLE `carwash_shops` DISABLE KEYS */;

INSERT INTO `carwash_shops` (`id`, `title`, `accountId`, `price`, `cash`, `bank`, `posX`, `posY`, `posZ`, `pickupX`, `pickupY`, `pickupZ`, `materials`, `prices`, `mafiaId`, `mafiaCash`, `defaultMafiaId`, `paidUntil`, `auctionId`, `marketplaceId`)
VALUES
	(1,'Автомойка',NULL,********,975220,1008000,'-2.87','-1396.70','29.25','26.5906','-1392.0261','29.25',6138,NULL,16,0,19,'2023-03-27 20:11:01',2609,NULL),
	(2,'Автомойка',NULL,********,80,655220,'171.14','-1723.15','29.39','167.1034','-1719.4704','29.39',692,NULL,16,0,19,'2023-03-18 20:11:01',1908,NULL),
	(3,'Автомойка',NULL,********,5342400,3948000,'-74.85','6420.68','31.49','-74.5693','6427.8715','31.49',943,NULL,16,0,19,'2023-03-15 20:31:00',1760,NULL),
	(4,'Автомойка',NULL,********,9123300,2620800,'-697.30','-941.60','19.18','-699.6325','-932.7043','19.21',5863,NULL,16,0,19,'2023-03-27 20:01:00',2612,NULL),
	(5,'Автомойка',NULL,********,0,0,'1360.86','3603.75','34.95','1362.5385','3592.1274','34.95',3019,NULL,16,0,19,'2023-03-13 20:21:00',1624,NULL);

/*!40000 ALTER TABLE `carwash_shops` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `cases_config` (
  `type` varchar(12) NOT NULL,
  `rest` smallint(6) DEFAULT NULL,
  `until` datetime DEFAULT NULL,
  PRIMARY KEY (`type`),
  KEY `until` (`until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `cases_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `caseType` varchar(20) DEFAULT NULL,
  `rouletteItemId` varchar(36) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `caseType` (`caseType`),
  KEY `date` (`date`),
  KEY `rouletteItemId` (`rouletteItemId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `chips_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `gameType` varchar(50) DEFAULT NULL,
  `gameId` int(11) DEFAULT NULL,
  `tableId` int(11) DEFAULT NULL,
  `amount` bigint(20) DEFAULT NULL,
  `prev` bigint(20) unsigned DEFAULT NULL,
  `comment` varchar(128) DEFAULT NULL,
  `args` longtext DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `amount` (`amount`) USING BTREE,
  KEY `date` (`date`) USING BTREE,
  KEY `comment` (`comment`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Логи фишек в казино';



CREATE TABLE `clothes_shops` (
  `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT,
  `class` varchar(24) DEFAULT NULL,
  `title` varchar(24) DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `price` int(11) unsigned DEFAULT 5000000,
  `cash` bigint(12) unsigned DEFAULT 0,
  `bank` bigint(12) unsigned DEFAULT 0,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `playerPosX` varchar(10) DEFAULT NULL,
  `playerPosY` varchar(10) DEFAULT NULL,
  `playerPosZ` varchar(10) DEFAULT NULL,
  `playerPosA` varchar(10) DEFAULT NULL,
  `materials` mediumint(7) unsigned DEFAULT 5000,
  `prices` blob DEFAULT NULL,
  `discounts` blob DEFAULT NULL,
  `mafiaId` tinyint(2) unsigned DEFAULT 0,
  `mafiaCash` bigint(12) unsigned DEFAULT 0,
  `defaultMafiaId` tinyint(2) unsigned DEFAULT 0,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `title` (`title`),
  KEY `accountId` (`accountId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`),
  KEY `marketplaceId` (`marketplaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Магазины одежды';

LOCK TABLES `clothes_shops` WRITE;
/*!40000 ALTER TABLE `clothes_shops` DISABLE KEYS */;

INSERT INTO `clothes_shops` (`id`, `class`, `title`, `accountId`, `price`, `cash`, `bank`, `posX`, `posY`, `posZ`, `playerPosX`, `playerPosY`, `playerPosZ`, `playerPosA`, `materials`, `prices`, `discounts`, `mafiaId`, `mafiaCash`, `defaultMafiaId`, `paidUntil`, `auctionId`, `marketplaceId`)
VALUES
	(1,'suburban','Suburban',NULL,2********,********,********,'130.5779','-209.4527','54.5420','123.2184','-228.7560','54.5578','334',10256,NULL,NULL,16,0,14,'2023-03-26 19:11:00',2455,NULL),
	(2,'ponsonbys','Ponsonbys',NULL,3********,********,********,'-153.6073','-303.5395','38.9224','-167.4005','-300.4701','39.7333','7.1648',5836,NULL,NULL,18,0,14,'2023-03-26 19:11:01',2456,NULL),
	(3,'binco','Binco',NULL,*********,2580375,75,'-819.7582','-1080.6221','11.1324','-830.1859','-1073.1818','11.3281','240.8204',4338,NULL,NULL,15,0,14,'2023-03-26 20:11:00',2469,NULL),
	(4,'checkout','Discount Store',NULL,********,73,66,'-1097.3843','2703.8452','18.9750','-1109.5808','2709.5590','19.1078','252.0027',845,NULL,NULL,15,0,14,'2023-03-26 19:31:00',2458,NULL),
	(5,'ponsonbys','Ponsonbys',NULL,3********,5410420,44,'-718.1774','-155.5674','36.9882','-705.6834','-151.6842','37.4151','245.4587',10000,NULL,NULL,15,0,14,'2023-03-26 19:01:00',2461,NULL),
	(6,'ponsonbys','Ponsonbys',NULL,*********,5965889,164787,'-1454.8022','-229.6992','49.2438','-1447.6970','-241.0979','49.8191','167.4881',5344,NULL,NULL,16,0,14,'2023-03-25 20:01:01',2330,NULL),
	(7,'checkout','Discount Store',NULL,********,53765386,35798560,'1687.0902','4818.5429','41.9924','1697.6358','4830.0439','42.0631','130.6059',3098,NULL,NULL,15,0,14,'2023-03-15 19:31:00',1754,NULL),
	(8,'checkout','Discount Store',NULL,1********,68,56,'-2.5296','6515.8125','31.4851','12.9483','6513.5659','31.8778','73.0982',2133,NULL,NULL,18,0,14,'2023-03-03 19:40:01',567,NULL),
	(9,'suburban','Suburban',NULL,1********,8418000,4703100,'616.0192','2747.9257','42.0880','614.2117','2768.6911','42.0881','176.5335',56,NULL,NULL,18,0,14,'2023-03-08 19:21:00',999,NULL),
	(10,'suburban','Suburban',NULL,*********,23,7194219,'-1204.9185','-777.4840','17.3322','-1187.8194','-764.9670','17.3201','126.3152',7667,NULL,NULL,18,0,14,'2023-03-26 19:31:00',2463,NULL),
	(11,'checkout','Discount Store',NULL,1********,11968201,101766700,'82.6167','-1389.6644','29.4195','71.0900','-1399.4686','29.3761','300.7005',6421,NULL,NULL,16,0,14,'2023-03-15 19:31:00',1755,NULL),
	(12,'checkout','Discount Store',NULL,********,2122881,8820691,'1195.6112','2702.5705','38.1577','1189.9077','2714.6071','38.2226','213.5054',4016,NULL,NULL,16,0,14,'2023-03-27 19:01:00',2594,NULL),
	(13,'suburban','Suburban',NULL,*********,19354211,42918221,'-3164.8398','1057.9606','20.8582','-3173.6848','1038.4816','20.8632','332.6203',131,NULL,NULL,16,0,14,'2023-03-23 19:21:00',2167,NULL),
	(14,'binco','Binco',NULL,1********,67586700,28622800,'417.6985','-809.3787','29.3592','429.5391','-800.0798','29.4911','126.9840',66916,NULL,NULL,16,0,14,'2023-03-06 19:01:00',802,NULL),
	(15,'leopold','Leopolds',NULL,*********0,0,0,'-723.83','-372.1594','34.3216','-724.93','-381.22','34.82','256.09',5000,NULL,NULL,16,0,14,NULL,NULL,NULL);

/*!40000 ALTER TABLE `clothes_shops` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `containers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(1) NOT NULL DEFAULT 0,
  `rare` varchar(50) NOT NULL,
  `bet` bigint(20) NOT NULL DEFAULT 0,
  `members` text NOT NULL,
  `prize` text DEFAULT NULL,
  `posX` varchar(50) NOT NULL,
  `posY` varchar(50) NOT NULL,
  `posZ` varchar(50) NOT NULL,
  `timeWhenOpen` int(11) NOT NULL DEFAULT 0,
  `dateWhenOpen` timestamp NULL DEFAULT NULL,
  KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `craftings` (
  `id` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '',
  `type` tinyint(2) DEFAULT 0,
  `owner` int(11) DEFAULT 0,
  `time` datetime DEFAULT NULL,
  `data` varchar(85) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '{}'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `crash_bets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `accountId` int(11) NOT NULL,
  `bet` int(11) NOT NULL,
  `exitX` float DEFAULT NULL COMMENT 'X вывода игрока',
  `gameId` int(11) NOT NULL,
  `dateBet` datetime NOT NULL DEFAULT current_timestamp(),
  `socialClub` varchar(16) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `gameId` (`gameId`),
  KEY `dateBet` (`dateBet`),
  KEY `userId` (`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `crash_games` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playersCount` int(11) NOT NULL,
  `crashed_at` float NOT NULL,
  `bank` int(11) NOT NULL,
  `won` int(11) NOT NULL,
  `lost` int(11) NOT NULL,
  `dateGame` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `dateGame` (`dateGame`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `criminal_record_logs` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `arrestId` int(11) DEFAULT NULL,
  `accountId` int(10) NOT NULL,
  `recordBy` int(10) DEFAULT NULL,
  `reason` varchar(50) NOT NULL,
  `endDate` datetime NOT NULL,
  `date` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `endDate` (`endDate`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `custom_promocodes` (
  `id` int(10) unsigned zerofill NOT NULL AUTO_INCREMENT,
  `code` varchar(6) DEFAULT NULL,
  `used` smallint(5) unsigned zerofill DEFAULT 00000,
  `limit` smallint(5) unsigned zerofill DEFAULT 00000,
  `endDate` datetime DEFAULT NULL,
  `donate` smallint(5) unsigned zerofill DEFAULT 00000,
  `money` smallint(5) unsigned zerofill DEFAULT 00000,
  PRIMARY KEY (`id`),
  KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `custom_tuning` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `vehicle` mediumint(8) unsigned DEFAULT NULL,
  `modType` tinyint(3) unsigned DEFAULT NULL,
  `modIndex` varchar(7) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `vehicleId` (`vehicle`) USING BTREE,
  KEY `modType` (`modType`),
  KEY `modIndex` (`modIndex`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `custom_wheels` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(8) unsigned DEFAULT NULL,
  `wheelId` smallint(5) unsigned DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `usedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `wheelId` (`wheelId`),
  KEY `date` (`date`),
  KEY `status` (`status`),
  KEY `usedAt` (`usedAt`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `death_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `killerLogin` varchar(20) DEFAULT NULL,
  `killerAccountId` mediumint(7) DEFAULT NULL,
  `weapon` varchar(50) DEFAULT NULL,
  `killedLogin` varchar(20) DEFAULT NULL,
  `killedAccountId` mediumint(7) DEFAULT NULL,
  `date` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `killerAccountId` (`killerAccountId`),
  KEY `killerLogin` (`killerLogin`),
  KEY `killedLogin` (`killedLogin`),
  KEY `killedAccountId` (`killedAccountId`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `deposits` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `tariffId` tinyint(1) unsigned DEFAULT NULL,
  `amount` bigint(20) unsigned DEFAULT 0,
  `profit` bigint(20) unsigned DEFAULT 0,
  `hours` smallint(5) unsigned DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `closedDate` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `date` (`date`),
  KEY `closedDate` (`closedDate`),
  KEY `tariffId` (`tariffId`),
  KEY `accountId` (`accountId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Банковские вклады';



CREATE TABLE `donate_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `amount` mediumint(7) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `comment` varchar(100) DEFAULT NULL,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`args`)),
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `date` (`date`),
  KEY `userId` (`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Лог использования доната';



CREATE TABLE `drift_score` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `zoneId` smallint(6) DEFAULT NULL,
  `vehicleModel` varchar(50) DEFAULT NULL,
  `score` int(11) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `zoneId` (`zoneId`) USING BTREE,
  KEY `date` (`date`) USING BTREE,
  KEY `vehicleModel` (`vehicleModel`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Счёт дрифт очков игроков';



CREATE TABLE `election_candidates` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `electionId` smallint(5) unsigned DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `description` varchar(128) DEFAULT NULL,
  `slogan` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq` (`electionId`,`accountId`),
  KEY `electionId` (`electionId`),
  KEY `accountId` (`accountId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `election_votes` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `electionId` mediumint(7) unsigned DEFAULT NULL,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `selected` mediumint(7) unsigned DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `electionId` (`electionId`),
  KEY `selected` (`selected`),
  KEY `date` (`date`),
  KEY `startedBy` (`userId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `elections` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `startedBy` mediumint(7) unsigned DEFAULT NULL,
  `createdAt` datetime DEFAULT NULL,
  `startAt` date DEFAULT NULL,
  `winner` mediumint(7) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `startedBy` (`startedBy`),
  KEY `winner` (`winner`),
  KEY `date` (`createdAt`) USING BTREE,
  KEY `startAt` (`startAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `enterprises` (
  `id` tinyint(4) NOT NULL AUTO_INCREMENT,
  `owner` smallint(6) NOT NULL DEFAULT 0,
  `position` varchar(50) NOT NULL DEFAULT '{"x":0,"y":0,"z":0}',
  `posA` float NOT NULL DEFAULT 0,
  `garage` varchar(50) NOT NULL DEFAULT '{"x":0,"y":0,"z":0}',
  `garagePosA` float NOT NULL DEFAULT 0,
  `type` tinyint(4) NOT NULL DEFAULT 0,
  `level` tinyint(4) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

LOCK TABLES `enterprises` WRITE;
/*!40000 ALTER TABLE `enterprises` DISABLE KEYS */;

INSERT INTO `enterprises` (`id`, `owner`, `position`, `posA`, `garage`, `garagePosA`, `type`, `level`)
VALUES
	(1,0,'{\"x\":1709.9620,\"y\":4728.3110,\"z\":42.1497}',105.82,'{\"x\":1712.6490,\"y\":4744.1631,\"z\":41.9737}',42.2503,0,0),
	(2,0,'{\"x\":2555.2126,\"y\":4651.4502,\"z\":34.0768}',111.893,'{\"x\":2553.8838,\"y\":4668.8608,\"z\":33.9813}',21.1483,2,0),
	(3,0,'{\"x\":-1100.8918,\"y\":4940.6831,\"z\":218.3541}',-104.146,'{\"x\":-1097.8308,\"y\":4947.9277,\"z\":218.3542}',-111.451,0,0),
	(4,0,'{\"x\":-561.8518,\"y\":294.8293,\"z\":87.4935}',-92.7213,'{\"x\":-565.4676,\"y\":302.3437,\"z\":83.1531}',-85.0781,0,0),
	(5,0,'{\"x\":152.3294,\"y\":2280.5461,\"z\":93.9818}',163.419,'{\"x\":162.6980,\"y\":2285.7896,\"z\":94.1134}',-132.599,0,0),
	(6,0,'{\"x\":405.5786,\"y\":6526.1802,\"z\":27.6967}',90.5106,'{\"x\":407.8865,\"y\":6497.8662,\"z\":27.7653}',177.508,0,0),
	(7,0,'{\"x\":1231.3147,\"y\":-1083.1877,\"z\":38.5253}',127.581,'{\"x\":1241.1219,\"y\":-1096.1194,\"z\":38.5253}',98.2168,0,0),
	(8,0,'{\"x\":136.9572,\"y\":-2472.8376,\"z\":6.0000}',-126.199,'{\"x\":137.9599,\"y\":-2500.6528,\"z\":6.0000}',-155.833,0,0),
	(9,0,'{\"x\":-268.6432,\"y\":-2437.8896,\"z\":6.0006}',-125.071,'{\"x\":-238.0452,\"y\":-2444.8235,\"z\":6.0014}',-87.92,0,0),
	(10,0,'{\"x\":-1807.2939,\"y\":-2810.3860,\"z\":13.9443}',152.952,'{\"x\":-1818.2614,\"y\":-2804.7783,\"z\":13.9443}',147.978,0,0),
	(11,0,'{\"x\":-2022.7131,\"y\":-255.2632,\"z\":23.4201}',60.4704,'{\"x\":-2035.4735,\"y\":-274.5135,\"z\":23.3855}',57.0899,0,0),
	(12,0,'{\"x\":-1218.8948,\"y\":-1051.7219,\"z\":8.4172}',-73.1147,'{\"x\":-1215.5382,\"y\":-1060.5486,\"z\":8.4286}',-79.5433,0,0),
	(13,0,'{\"x\":-830.4528,\"y\":-1255.8617,\"z\":6.5840}',140.621,'{\"x\":-827.1891,\"y\":-1261.9983,\"z\":5.0004}',136.857,0,0),
	(14,0,'{\"x\":160.1463,\"y\":293.4671,\"z\":110.8549}',4.0741,'{\"x\":138.3110,\"y\":273.8765,\"z\":109.9740}',75.4933,1,1),
	(15,0,'{\"x\":1545.9164,\"y\":2166.4785,\"z\":78.7288}',88.6312,'{\"x\":1552.0259,\"y\":2190.8062,\"z\":78.8481}',-4.7494,1,1);

/*!40000 ALTER TABLE `enterprises` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `facility` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL DEFAULT '',
  `posX` varchar(10) NOT NULL DEFAULT '',
  `posY` varchar(10) NOT NULL DEFAULT '',
  `posZ` varchar(10) NOT NULL DEFAULT '',
  `payment` smallint(5) unsigned NOT NULL DEFAULT 0,
  `fractionId` smallint(6) unsigned NOT NULL DEFAULT 0,
  `defaultFractionId` smallint(6) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Предприятия. На данный момент нужно для байкеров';

LOCK TABLES `facility` WRITE;
/*!40000 ALTER TABLE `facility` DISABLE KEYS */;

INSERT INTO `facility` (`id`, `name`, `posX`, `posY`, `posZ`, `payment`, `fractionId`, `defaultFractionId`)
VALUES
	(1,'Завод цементный','287.21','2843.88','44.22',2500,23,18),
	(2,'Завод Union','2890.48','4391.56','49.86',5000,24,17),
	(3,'Нефтезавод','2695.40','1613.98','24.02',7500,23,23),
	(4,'Завод малый нефтезавод','-130.14','-2240.32','7.51',3750,23,22),
	(5,'Завод малый нефтезавод','167.83','-2222.45','6.75',3750,23,20),
	(6,'Ферма 1','839.23','2176.35','51.76',375,23,17),
	(7,'Ферма 2','1552.12','2190.17','78.31',500,23,20),
	(8,'Ферма 3','2564.23','4680.49','33.61',1000,23,22),
	(9,'Ферма 4','2416.21','4993.48','45.72',2500,23,18),
	(10,'Ферма 5','2242.95','5153.56','57.15',1500,23,22),
	(11,'Ферма 6','1963.63','4638.38','40.19',2000,23,18),
	(12,'Нефтяная вышка 1','604.38','2858.73','39.46',300,23,17),
	(13,'Нефтяная вышка 2','655.38','2918.08','41.57',300,23,17),
	(14,'Нефтяная вышка 3','689.46','2890.20','49.50',300,23,17),
	(15,'Нефтяная вышка 4','658.16','3016.11','43.06',300,23,17),
	(16,'Нефтяная вышка 5','594.58','3014.39','41.35',300,23,17),
	(17,'Нефтяная вышка 6','575.12','2928.94','40.27',300,23,17),
	(18,'Нефтяная вышка 7','540.29','2870.99','42.61',300,23,17),
	(19,'Нефтяная вышка 8','499.88','2952.60','41.90',300,23,17),
	(20,'Нефтяная вышка 9','1170.21','-2116.26','42.76',300,24,17),
	(21,'Нефтяная вышка 10','1204.02','-2190.43','40.94',300,24,18),
	(22,'Нефтяная вышка 11','1266.22','-2341.44','50.35',300,24,18),
	(23,'Нефтяная вышка 12','1215.32','-2436.70','44.00',300,24,18),
	(24,'Нефтяная вышка 13','1226.15','-2454.15','44.00',300,24,20),
	(25,'Нефтяная вышка 14','1370.12','-2278.24','61.00',300,24,20),
	(26,'Нефтяная вышка 15','1420.38','-2306.76','66.37',300,24,20),
	(27,'Нефтяная вышка 16','1435.54','-2301.64','66.53',300,24,20),
	(28,'Нефтяная вышка 17','1450.98','-2265.69','66.02',300,24,20),
	(29,'Нефтяная вышка 18','1428.16','-2101.66','55.00',300,24,20),
	(30,'Нефтяная вышка 19','1434.21','-2092.39','54.36',300,24,20),
	(31,'Нефтяная вышка 20','1518.69','-2055.88','76.82',300,23,20),
	(32,'Нефтяная вышка 21','1528.95','-2169.14','77.14',300,23,20),
	(33,'Нефтяная вышка 22','1555.40','-1852.34','91.97',300,24,20),
	(34,'Нефтяная вышка 23','1583.63','-1780.31','87.69',300,24,20),
	(35,'Нефтяная вышка 24','1567.94','-1586.13','90.41',300,24,20),
	(36,'Нефтяная вышка 25','1675.06','-1450.67','111.57',300,24,20),
	(37,'Нефтяная вышка 26','1691.06','-1427.02','111.96',300,24,22),
	(38,'Нефтяная вышка 27','1797.74','-1350.78','99.03',300,24,22),
	(39,'Нефтяная вышка 28','1771.32','-1322.03','94.15',300,22,22),
	(40,'Нефтяная вышка 29','1836.08','-1200.25','92.00',300,23,22),
	(41,'Нефтяная вышка 30','1833.66','-1172.85','90.99',300,22,22),
	(42,'Нефтяная вышка 31','1866.64','-1132.51','85.42',300,23,22),
	(43,'Нефтяная вышка 32','1874.76','-1036.15','78.62',300,18,23),
	(44,'Нефтяная вышка 33','1891.08','-1019.59','78.03',300,18,23),
	(45,'Нефтяная вышка 34','2569.56','2719.96','42.45',300,18,23);

/*!40000 ALTER TABLE `facility` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `families` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(32) DEFAULT NULL,
  `tag` varchar(4) DEFAULT NULL,
  `mainColor` varchar(10) DEFAULT NULL,
  `theme` enum('black','white') DEFAULT 'black',
  `backgroundColor` varchar(10) DEFAULT NULL,
  `blipColor` varchar(10) DEFAULT NULL,
  `logo` varchar(100) DEFAULT NULL,
  `leaderId` mediumint(7) unsigned DEFAULT NULL,
  `money` bigint(12) unsigned DEFAULT 0,
  `createdAt` datetime DEFAULT NULL,
  `createdBy` mediumint(9) DEFAULT NULL,
  `reputation` mediumint(9) DEFAULT NULL,
  `exp` mediumint(8) unsigned DEFAULT NULL,
  `contractsData` blob DEFAULT NULL,
  `contractsCompleted` blob DEFAULT '{}',
  `cooldowns` blob DEFAULT NULL,
  `rentProperty` blob DEFAULT NULL,
  `logoUploaded` tinyint(1) DEFAULT 0,
  `logoStatus` tinyint(1) DEFAULT 0,
  `logoReportId` int(11) DEFAULT NULL,
  `logoType` enum('png','jpg','jpeg') DEFAULT NULL,
  `logoToken` varchar(16) DEFAULT NULL,
  `titleStatus` tinyint(1) DEFAULT 0,
  `titleReportId` int(11) DEFAULT NULL,
  `lastUpdated` datetime DEFAULT NULL,
  `removedBy` mediumint(8) unsigned DEFAULT NULL,
  `removedDate` datetime DEFAULT NULL,
  `block` datetime DEFAULT NULL,
  `capturesBlock` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `title` (`title`),
  UNIQUE KEY `tag` (`tag`),
  KEY `leaderId` (`leaderId`) USING BTREE,
  KEY `createdAt` (`createdAt`) USING BTREE,
  KEY `createdBy` (`createdBy`) USING BTREE,
  KEY `removedBy` (`removedBy`),
  KEY `removedDate` (`removedDate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC COMMENT='Фракции, Лидеры';



CREATE TABLE `family_blacklist` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `familyId` mediumint(7) NOT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `actionBy` mediumint(7) unsigned DEFAULT NULL,
  `reason` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `date` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `actionBy` (`actionBy`),
  KEY `familyId` (`familyId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Черный список семей';



CREATE TABLE `family_craft_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `familyId` smallint(5) unsigned DEFAULT NULL,
  `itemId` smallint(5) unsigned DEFAULT NULL,
  `value` tinyint(1) unsigned DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `Индекс 4` (`familyId`,`itemId`),
  KEY `familyId` (`familyId`),
  KEY `itemId` (`itemId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `family_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `familyId` smallint(5) unsigned DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `actionBy` mediumint(7) unsigned DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `reason` varchar(50) DEFAULT NULL,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`args`)),
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `date` (`date`) USING BTREE,
  KEY `actionBy` (`actionBy`) USING BTREE,
  KEY `familyId` (`familyId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC COMMENT='Лог вступления, увольнения и т.д. по фракциях';



CREATE TABLE `family_money_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `familyId` smallint(5) unsigned DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `toAccountId` mediumint(7) unsigned DEFAULT NULL,
  `amount` bigint(20) DEFAULT 0,
  `prev` bigint(20) DEFAULT 0,
  `comment` varchar(128) DEFAULT NULL,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`args`)),
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `date` (`date`) USING BTREE,
  KEY `comment` (`comment`) USING BTREE,
  KEY `amount` (`amount`) USING BTREE,
  KEY `fractionId` (`familyId`) USING BTREE,
  KEY `toAccountId` (`toAccountId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `family_perks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `familyId` int(11) NOT NULL,
  `perk` varchar(50) NOT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `familyId_perk` (`familyId`,`perk`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Прокаченные улучшения семей';



CREATE TABLE `family_rank_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `familyId` smallint(5) unsigned DEFAULT NULL,
  `rankId` mediumint(8) unsigned DEFAULT NULL,
  `key` varchar(20) DEFAULT NULL,
  `value` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uni` (`familyId`,`rankId`,`key`) USING BTREE,
  KEY `key` (`key`) USING BTREE,
  KEY `fractionId` (`familyId`) USING BTREE,
  KEY `rankId` (`rankId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `family_ranks` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `familyId` smallint(5) unsigned DEFAULT NULL,
  `rank` tinyint(3) unsigned DEFAULT NULL,
  `name` varchar(24) DEFAULT NULL,
  KEY `id` (`id`) USING BTREE,
  KEY `rank` (`rank`) USING BTREE,
  KEY `fractionId` (`familyId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `family_vehicle_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `familyId` smallint(5) unsigned DEFAULT NULL,
  `vehicleId` mediumint(8) unsigned DEFAULT NULL,
  `value` tinyint(1) unsigned DEFAULT 1,
  `percent` tinyint(1) unsigned DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `Индекс 4` (`familyId`,`vehicleId`) USING BTREE,
  KEY `fractionId` (`familyId`) USING BTREE,
  KEY `vehicle` (`vehicleId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `family_wars_admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `adminId` int(11) DEFAULT NULL,
  `adminLvl` int(11) NOT NULL DEFAULT 0,
  `adminName` varchar(50) DEFAULT NULL,
  `message` varchar(300) NOT NULL,
  `comment` varchar(300) NOT NULL,
  `translated` int(11) DEFAULT 0,
  `translateData` text DEFAULT '{}',
  `createDate` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `family_wars_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) DEFAULT NULL,
  `fractionId` int(11) DEFAULT NULL,
  `attacksCount` int(11) DEFAULT 3,
  `defencesCount` int(11) DEFAULT 3,
  `lastAttackTime` int(11) DEFAULT NULL,
  `lastDefenceTime` int(11) DEFAULT NULL,
  `freezeAttacks` tinyint(4) DEFAULT 0,
  `maxAttacks` int(11) DEFAULT 2,
  `maxDefences` int(11) DEFAULT 2,
  `addDfenceLog` blob DEFAULT NULL,
  `addAttackLog` blob DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `fractionId` (`fractionId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `family_wars_events_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `captureId` int(11) NOT NULL,
  `type` varchar(15) DEFAULT NULL,
  `fromStaticId` int(11) NOT NULL,
  `toStaticId` int(11) NOT NULL,
  `fromFactionId` int(11) NOT NULL,
  `toFactionId` int(11) NOT NULL,
  `value` bigint(20) NOT NULL DEFAULT 0,
  `createdAt` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `family_wars_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `captureId` int(11) DEFAULT NULL,
  `defendersId` int(11) DEFAULT 0,
  `attackersId` int(11) NOT NULL DEFAULT 0,
  `gangZoneId` int(11) NOT NULL DEFAULT 0,
  `winner` int(11) NOT NULL DEFAULT 0,
  `startAt` datetime NOT NULL DEFAULT current_timestamp(),
  `endAt` datetime DEFAULT NULL,
  `uniqueDefenders` int(11) NOT NULL DEFAULT 0,
  `uniqueAttackers` int(11) NOT NULL DEFAULT 0,
  `status` varchar(15) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `family_wars_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `adminId` int(11) DEFAULT NULL,
  `message` varchar(300) NOT NULL,
  `translated` int(11) DEFAULT 0,
  `translateData` text DEFAULT '{}',
  `fractionIds` text NOT NULL,
  `type` varchar(50) NOT NULL,
  `createDate` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `family_wars_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `attackerId` int(8) DEFAULT NULL,
  `defenderId` int(8) DEFAULT NULL,
  `zoneId` int(8) unsigned DEFAULT NULL,
  `startTime` bigint(20) DEFAULT NULL,
  `createTime` bigint(20) DEFAULT NULL,
  `peopleCount` tinyint(4) DEFAULT NULL,
  `caliberId` smallint(6) DEFAULT NULL,
  `attackersIds` blob DEFAULT NULL,
  `defendersIds` blob DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `attackerAdditionalTime` int(11) DEFAULT NULL,
  `defenderAdditionalTime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `family_wars_zones` (
  `id` int(5) unsigned NOT NULL AUTO_INCREMENT,
  `zoneId` int(5) unsigned NOT NULL,
  `fractionId` int(8) unsigned DEFAULT NULL,
  `level` tinyint(5) unsigned DEFAULT NULL,
  `controllStartTime` bigint(20) unsigned DEFAULT NULL,
  `debuffZoneId` int(5) unsigned DEFAULT NULL,
  `debuffEndTime` bigint(20) unsigned DEFAULT NULL,
  `effectCooldown` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `zoneId` (`zoneId`) USING BTREE,
  KEY `familyId` (`fractionId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

LOCK TABLES `family_wars_zones` WRITE;
/*!40000 ALTER TABLE `family_wars_zones` DISABLE KEYS */;

INSERT INTO `family_wars_zones` (`id`, `zoneId`, `fractionId`, `level`, `controllStartTime`, `debuffZoneId`, `debuffEndTime`, `effectCooldown`)
VALUES
	(1,2743,NULL,0,1689278339841,NULL,NULL,NULL),
	(2,2659,NULL,0,1689278339862,NULL,NULL,NULL),
	(3,2742,NULL,0,1689278339886,NULL,NULL,NULL),
	(4,3637,NULL,0,1690394402765,NULL,NULL,NULL),
	(5,3777,NULL,0,1689269813052,NULL,NULL,NULL),
	(6,2991,NULL,0,1690394402507,NULL,NULL,NULL),
	(7,4789,NULL,0,1690394400838,NULL,NULL,NULL),
	(8,4706,NULL,0,1690394400860,NULL,NULL,NULL),
	(9,4622,NULL,0,1690394400884,NULL,NULL,NULL),
	(10,4538,NULL,0,1690394400908,NULL,NULL,NULL),
	(11,4707,NULL,0,1690394400933,NULL,NULL,NULL),
	(12,4539,NULL,0,1690394400956,NULL,NULL,NULL),
	(13,4623,NULL,0,1690394400983,NULL,NULL,NULL),
	(14,4708,NULL,0,1690394401010,NULL,NULL,NULL),
	(15,4624,NULL,0,1690394401036,NULL,NULL,NULL),
	(16,4540,NULL,0,1690394401063,NULL,NULL,NULL),
	(17,4456,NULL,0,1690394401090,NULL,NULL,NULL),
	(18,4455,NULL,0,1690394401116,NULL,NULL,NULL),
	(19,4454,NULL,0,1690394401144,NULL,NULL,NULL),
	(20,4370,NULL,0,1690394401167,NULL,NULL,NULL),
	(21,4371,NULL,0,1690394401194,NULL,NULL,NULL),
	(22,4372,NULL,0,1690394401220,NULL,NULL,NULL),
	(23,4709,NULL,0,1690394401247,NULL,NULL,NULL),
	(24,4625,NULL,0,1690394401274,NULL,NULL,NULL),
	(25,4541,NULL,0,1690394401300,NULL,NULL,NULL),
	(26,4457,NULL,0,1690394401327,NULL,NULL,NULL),
	(27,4373,NULL,0,1690394401351,NULL,NULL,NULL),
	(28,3725,NULL,0,1690394401378,NULL,NULL,NULL),
	(29,3641,NULL,0,1714316401789,NULL,NULL,NULL),
	(30,1176,NULL,0,1689274525752,NULL,NULL,NULL),
	(31,1175,NULL,0,1689274519589,NULL,NULL,NULL),
	(32,1509,NULL,0,1690394402711,NULL,NULL,1689348354766),
	(33,2008,NULL,0,1690394402789,1509,1689348354766,NULL),
	(34,1924,NULL,0,1690394401536,NULL,NULL,NULL),
	(35,1923,NULL,0,1690394401563,NULL,NULL,NULL),
	(36,1676,NULL,0,1690394401589,NULL,NULL,NULL),
	(37,1677,NULL,0,1690394401616,NULL,NULL,NULL),
	(38,1760,NULL,0,1690394401643,NULL,NULL,NULL),
	(39,1759,NULL,0,1690394401669,NULL,NULL,NULL),
	(40,1758,NULL,0,1690394401696,NULL,NULL,NULL),
	(41,1757,NULL,0,1690394401720,NULL,NULL,NULL),
	(42,1841,NULL,0,1690394401747,NULL,NULL,NULL),
	(43,1925,NULL,0,1690394401774,NULL,NULL,NULL),
	(44,1922,NULL,0,1690394401800,NULL,NULL,NULL),
	(45,2006,NULL,0,1690394401827,NULL,NULL,NULL),
	(46,2090,NULL,0,1690394401851,NULL,NULL,NULL),
	(47,2009,NULL,0,1690394401875,NULL,NULL,NULL),
	(48,1594,NULL,0,1690394401902,NULL,NULL,NULL),
	(49,1678,NULL,0,1690394401928,NULL,NULL,NULL),
	(50,1510,NULL,0,1690394401955,NULL,NULL,NULL),
	(51,1511,NULL,0,1690394401979,NULL,NULL,NULL),
	(52,1595,NULL,0,1690394402005,NULL,NULL,NULL),
	(53,1512,NULL,0,1690394402032,NULL,NULL,NULL),
	(54,1596,NULL,0,1690394402059,NULL,NULL,NULL),
	(55,217,NULL,0,1714316401484,NULL,NULL,NULL),
	(56,218,NULL,0,1714316401703,NULL,NULL,NULL),
	(57,219,NULL,0,1714316401540,NULL,NULL,NULL),
	(58,220,NULL,0,1714316401406,NULL,NULL,NULL),
	(59,638,NULL,0,1690394402557,NULL,NULL,NULL),
	(60,2306,NULL,0,1714316401792,NULL,NULL,NULL),
	(61,2051,NULL,0,1690394402661,NULL,NULL,NULL),
	(62,2303,NULL,0,1689274838267,NULL,NULL,NULL),
	(63,2474,NULL,0,1714316401545,NULL,NULL,NULL),
	(64,3064,NULL,4,1714316401381,NULL,NULL,NULL),
	(65,3818,NULL,0,1690394402607,NULL,NULL,NULL),
	(66,1824,NULL,4,1714316401376,NULL,NULL,NULL),
	(67,876,NULL,0,1690398001058,371,1689376995504,NULL),
	(68,791,NULL,0,1690394402085,NULL,NULL,NULL),
	(69,874,NULL,0,1690394402112,NULL,NULL,NULL),
	(70,790,NULL,0,1690394402136,NULL,NULL,NULL),
	(71,958,NULL,0,1690394402162,NULL,NULL,NULL),
	(72,1042,NULL,0,1690394402189,NULL,NULL,NULL),
	(73,792,NULL,0,1690394402216,NULL,NULL,NULL),
	(74,1043,NULL,0,1690394402242,NULL,NULL,NULL),
	(75,1044,NULL,0,1690394402269,NULL,NULL,NULL),
	(76,706,NULL,0,1690394402293,NULL,NULL,NULL),
	(77,622,NULL,0,1690394402320,NULL,NULL,NULL),
	(78,538,NULL,0,1690394402346,NULL,NULL,NULL),
	(79,371,NULL,0,1690394402738,NULL,NULL,1689520995504),
	(80,286,NULL,0,1690394402373,NULL,NULL,NULL),
	(81,202,NULL,0,1690394402400,NULL,NULL,NULL),
	(82,118,NULL,0,1690394402427,NULL,NULL,NULL),
	(83,34,NULL,0,1690394402453,NULL,NULL,NULL),
	(84,287,NULL,0,1690394402480,NULL,NULL,NULL),
	(85,1718,NULL,0,1690394402634,NULL,NULL,NULL),
	(86,2973,NULL,0,1714316401801,NULL,NULL,NULL),
	(87,3729,NULL,0,1689276752394,NULL,NULL,NULL),
	(88,868,NULL,0,1689375310212,NULL,NULL,NULL),
	(89,1228,NULL,0,1689426030247,NULL,NULL,NULL),
	(90,1233,NULL,0,1710727201009,NULL,NULL,NULL),
	(91,3652,NULL,0,1689375310237,NULL,NULL,NULL),
	(92,4311,NULL,0,1689375310159,NULL,NULL,NULL),
	(93,3848,NULL,0,1689375310027,NULL,NULL,NULL),
	(94,2327,NULL,0,1714316401359,NULL,NULL,NULL),
	(95,1527,NULL,0,1689375310052,NULL,NULL,NULL),
	(96,1530,NULL,0,1689375310078,NULL,NULL,NULL),
	(97,1950,NULL,0,1690394401404,NULL,NULL,NULL),
	(98,2937,NULL,0,1690408801557,NULL,NULL,NULL),
	(99,3021,NULL,0,1690408800940,NULL,NULL,NULL),
	(100,3020,NULL,0,1690408800958,NULL,NULL,NULL),
	(101,2936,NULL,0,1690408800984,NULL,NULL,NULL),
	(102,3104,NULL,0,1690408801009,NULL,NULL,NULL),
	(103,3105,NULL,0,1690408801036,NULL,NULL,NULL),
	(104,3188,NULL,0,1690408801063,NULL,NULL,NULL),
	(105,3189,NULL,0,1690408801090,NULL,NULL,NULL),
	(106,3187,NULL,0,1690408801116,NULL,NULL,NULL),
	(107,3103,NULL,0,1690408801143,NULL,NULL,NULL),
	(108,3019,NULL,0,1690408801167,NULL,NULL,NULL),
	(109,2935,NULL,0,1690408801194,NULL,NULL,NULL),
	(110,3186,NULL,0,1690408801221,NULL,NULL,NULL),
	(111,3102,NULL,0,1690408801244,NULL,NULL,NULL),
	(112,3018,NULL,0,1690408801271,NULL,NULL,NULL),
	(113,2934,NULL,0,1690408801297,NULL,NULL,NULL),
	(114,3273,NULL,0,1690408801325,NULL,NULL,NULL),
	(115,3272,NULL,0,1690408801352,NULL,NULL,NULL),
	(116,3271,NULL,0,1690408801379,NULL,NULL,NULL),
	(117,3270,NULL,0,1690408801403,NULL,NULL,NULL),
	(118,3354,NULL,0,1690408801430,NULL,NULL,NULL),
	(119,3355,NULL,0,1690408801453,NULL,NULL,NULL),
	(120,3356,NULL,0,1690408801480,NULL,NULL,NULL),
	(121,3357,NULL,0,1690408801503,NULL,NULL,NULL),
	(122,168,NULL,0,1690408801530,NULL,NULL,NULL),
	(123,1614,NULL,0,1689375310105,NULL,NULL,NULL),
	(124,2260,NULL,0,1689370704489,NULL,NULL,NULL),
	(125,3016,NULL,0,1690416000828,NULL,NULL,NULL),
	(126,3760,NULL,0,1690416000855,NULL,NULL,NULL),
	(127,3312,NULL,0,1690416000730,NULL,NULL,NULL),
	(128,2473,NULL,3,1709661600021,NULL,NULL,NULL),
	(129,1004,NULL,0,1712764800387,NULL,NULL,NULL),
	(130,1377,NULL,0,1690416000753,NULL,NULL,NULL),
	(131,3378,NULL,0,1690416000802,NULL,NULL,NULL),
	(132,900,NULL,0,1689375310264,NULL,NULL,NULL),
	(133,2084,NULL,0,1714316401760,NULL,NULL,NULL),
	(134,2255,NULL,0,1714316401765,NULL,NULL,NULL),
	(135,2171,NULL,0,1714316401610,NULL,NULL,NULL),
	(136,2087,NULL,0,1714316401410,NULL,NULL,NULL),
	(137,2086,NULL,0,1690502400572,NULL,NULL,NULL),
	(138,2170,NULL,0,1714316401722,NULL,NULL,NULL),
	(139,2085,NULL,0,1690502400593,NULL,NULL,NULL),
	(140,2252,NULL,0,1714316401549,NULL,NULL,NULL),
	(141,2168,NULL,0,1714316401414,NULL,NULL,NULL),
	(142,2254,NULL,0,1714316401517,NULL,NULL,NULL),
	(143,2433,NULL,0,1690502400620,NULL,NULL,NULL),
	(144,2258,NULL,0,1690502400992,NULL,NULL,NULL),
	(145,2256,NULL,0,1690502400644,NULL,NULL,NULL),
	(146,2760,NULL,0,1690502400969,NULL,NULL,NULL),
	(147,2586,NULL,0,1714316401589,NULL,NULL,NULL),
	(148,2928,NULL,0,1690502400666,NULL,NULL,NULL),
	(149,2670,NULL,0,1714316401613,NULL,NULL,NULL),
	(150,2587,NULL,0,1690502400691,NULL,NULL,NULL),
	(151,2671,NULL,0,1690502400715,NULL,NULL,NULL),
	(152,64,NULL,0,1698415200211,NULL,NULL,NULL),
	(153,148,NULL,0,1698415200233,NULL,NULL,NULL),
	(155,1848,NULL,0,1710727200901,NULL,NULL,NULL),
	(156,1932,NULL,0,1710727200907,NULL,NULL,NULL),
	(157,1847,NULL,0,1710727200912,NULL,NULL,NULL),
	(158,1931,NULL,0,1710727200919,NULL,NULL,NULL),
	(159,2015,NULL,0,1710727200924,NULL,NULL,NULL),
	(160,2016,NULL,0,1710727200929,NULL,NULL,NULL),
	(161,1762,NULL,0,1710727200934,NULL,NULL,NULL),
	(162,1846,NULL,0,1710727200939,NULL,NULL,NULL),
	(163,1930,NULL,0,1710727200944,NULL,NULL,NULL),
	(164,2014,NULL,0,1710727200949,NULL,NULL,NULL),
	(165,2098,NULL,0,1710727200954,NULL,NULL,NULL),
	(166,1845,NULL,0,1710727200959,NULL,NULL,NULL),
	(167,1929,NULL,0,1710727200964,NULL,NULL,NULL),
	(168,2013,NULL,0,1710727200969,NULL,NULL,NULL),
	(169,3097,NULL,0,1710727200974,NULL,NULL,NULL),
	(170,3181,NULL,0,1710727200979,NULL,NULL,NULL),
	(171,3180,NULL,0,1710727200984,NULL,NULL,NULL),
	(172,3179,NULL,0,1710727200989,NULL,NULL,NULL),
	(173,3013,NULL,0,1710727200994,NULL,NULL,NULL),
	(174,3178,NULL,0,1710727200999,NULL,NULL,NULL),
	(175,3094,NULL,0,1710727201004,NULL,NULL,NULL),
	(176,3012,NULL,3,1709665200377,NULL,NULL,NULL),
	(177,2471,NULL,0,1709661600015,NULL,NULL,NULL),
	(178,2240,NULL,0,1714316401810,NULL,NULL,NULL),
	(179,1736,NULL,0,1714316401814,NULL,NULL,NULL),
	(180,3067,NULL,0,1712764800392,NULL,NULL,NULL),
	(181,2226,NULL,0,1712764800396,NULL,NULL,NULL),
	(182,968,NULL,0,1712764800378,NULL,NULL,NULL),
	(183,3679,NULL,0,1711044238423,NULL,NULL,NULL),
	(184,3678,NULL,0,1711044240830,NULL,NULL,NULL),
	(185,3680,NULL,0,1711044216155,NULL,NULL,NULL),
	(186,3595,NULL,0,1711044203871,NULL,NULL,NULL),
	(187,3594,NULL,0,1711044208168,NULL,NULL,NULL),
	(188,3510,NULL,0,1711044206253,NULL,NULL,NULL),
	(189,3511,NULL,0,1711044201624,NULL,NULL,NULL),
	(190,3596,NULL,0,1711044211954,NULL,NULL,NULL),
	(191,3512,NULL,0,1711044210011,NULL,NULL,NULL),
	(192,4100,NULL,0,1711044228239,NULL,NULL,NULL),
	(193,4268,NULL,0,1711044224615,NULL,NULL,NULL),
	(194,4184,NULL,0,1711044226441,NULL,NULL,NULL),
	(195,4183,NULL,0,1711044220782,NULL,NULL,NULL),
	(196,4099,NULL,0,1711044218901,NULL,NULL,NULL),
	(197,4267,NULL,0,1711044222645,NULL,NULL,NULL),
	(198,2242,NULL,0,1714316401488,NULL,NULL,NULL),
	(199,2159,NULL,0,1714316401552,NULL,NULL,NULL),
	(200,2075,NULL,0,1714316401520,NULL,NULL,NULL),
	(201,1991,NULL,0,1714316401592,NULL,NULL,NULL),
	(202,1992,NULL,0,1714316401555,NULL,NULL,NULL),
	(203,1906,NULL,0,1714316401727,NULL,NULL,NULL),
	(204,1739,NULL,0,1714316401558,NULL,NULL,NULL),
	(205,1655,NULL,0,1714316401524,NULL,NULL,NULL),
	(206,1656,NULL,0,1714316401731,NULL,NULL,NULL),
	(207,2073,NULL,0,1714316401735,NULL,NULL,NULL),
	(208,2415,NULL,0,1714316401562,NULL,NULL,NULL),
	(209,2333,NULL,0,1714316401392,NULL,NULL,NULL),
	(210,2334,NULL,0,1714316401738,NULL,NULL,NULL),
	(211,2335,NULL,0,1714316401617,NULL,NULL,NULL),
	(212,2166,NULL,0,1714316401491,NULL,NULL,NULL),
	(213,2082,NULL,0,1714316401742,NULL,NULL,NULL),
	(214,2083,NULL,0,1714316401745,NULL,NULL,NULL),
	(215,2253,NULL,0,1714316401620,NULL,NULL,NULL),
	(216,2000,NULL,0,1714316401596,NULL,NULL,NULL),
	(217,2001,NULL,0,1714316401624,NULL,NULL,NULL),
	(218,1831,NULL,0,1714316401630,NULL,NULL,NULL),
	(219,1829,NULL,0,1714316401494,NULL,NULL,NULL),
	(220,1745,NULL,0,1714316401417,NULL,NULL,NULL),
	(221,1746,NULL,0,1714316401770,NULL,NULL,NULL),
	(222,1661,NULL,0,1714316401633,NULL,NULL,NULL),
	(223,1662,NULL,0,1714316401565,NULL,NULL,NULL),
	(224,1496,NULL,0,1714316401707,NULL,NULL,NULL),
	(225,1748,NULL,0,1714316401748,NULL,NULL,NULL),
	(226,1749,NULL,0,1714316401636,NULL,NULL,NULL),
	(227,1410,NULL,0,1714316401527,NULL,NULL,NULL),
	(228,1409,NULL,0,1714316401420,NULL,NULL,NULL),
	(229,1407,NULL,0,1714316401568,NULL,NULL,NULL),
	(230,1405,NULL,0,1714316401497,NULL,NULL,NULL),
	(231,1741,NULL,0,1714316401571,NULL,NULL,NULL),
	(232,1321,NULL,0,1714316401424,NULL,NULL,NULL),
	(233,1320,NULL,0,1714316401639,NULL,NULL,NULL),
	(234,1319,NULL,0,1714316401575,NULL,NULL,NULL),
	(235,2669,NULL,0,1714316401501,NULL,NULL,NULL),
	(236,2837,NULL,0,1714316401427,NULL,NULL,NULL),
	(237,2924,NULL,0,1714316401642,NULL,NULL,NULL),
	(238,2833,NULL,0,1714316401430,NULL,NULL,NULL),
	(239,2918,NULL,0,1714316401530,NULL,NULL,NULL),
	(240,2832,NULL,0,1714316401646,NULL,NULL,NULL),
	(241,2662,NULL,4,1714316401369,NULL,NULL,NULL),
	(242,2576,NULL,0,1714316401649,NULL,NULL,NULL),
	(243,2408,NULL,0,1714316401434,NULL,NULL,NULL),
	(244,2325,NULL,0,1714316401711,NULL,NULL,NULL),
	(245,1785,NULL,0,1714316401403,NULL,NULL,NULL),
	(246,1779,NULL,0,1714316401387,NULL,NULL,NULL),
	(247,1861,NULL,0,1714316401715,NULL,NULL,NULL),
	(248,1944,NULL,0,1714316401533,NULL,NULL,NULL),
	(249,2028,NULL,0,1714316401653,NULL,NULL,NULL),
	(250,2112,NULL,0,1714316401775,NULL,NULL,NULL),
	(251,1696,NULL,0,1714316401795,NULL,NULL,NULL),
	(252,1864,NULL,0,1714316400341,NULL,NULL,NULL),
	(253,1865,NULL,0,1714316400358,NULL,NULL,NULL),
	(254,1866,NULL,0,1714316400365,NULL,NULL,NULL),
	(255,1867,NULL,0,1714316400370,NULL,NULL,NULL),
	(256,1783,NULL,0,1714316400373,NULL,NULL,NULL),
	(257,1951,NULL,0,1714316400375,NULL,NULL,NULL),
	(258,1953,NULL,0,1714316400378,NULL,NULL,NULL),
	(259,1952,NULL,0,1714316400380,NULL,NULL,NULL),
	(260,1954,NULL,0,1714316400383,NULL,NULL,NULL),
	(261,1870,NULL,0,1714316400385,NULL,NULL,NULL),
	(262,1786,NULL,0,1714316400387,NULL,NULL,NULL),
	(263,1702,NULL,0,1714316400390,NULL,NULL,NULL),
	(264,1701,NULL,0,1714316400394,NULL,NULL,NULL),
	(265,1780,NULL,0,1714316400397,NULL,NULL,NULL),
	(266,1947,NULL,0,1714316401504,NULL,NULL,NULL),
	(267,1948,NULL,0,1714316400399,NULL,NULL,NULL),
	(268,1695,NULL,0,1714316400402,NULL,NULL,NULL),
	(269,2111,NULL,0,1714316401579,NULL,NULL,NULL),
	(270,2110,NULL,0,1714316401599,NULL,NULL,NULL),
	(271,2109,NULL,0,1714316400405,NULL,NULL,NULL),
	(272,2108,NULL,0,1714316400408,NULL,NULL,NULL),
	(273,2195,NULL,0,1714316401656,NULL,NULL,NULL),
	(274,2194,NULL,0,1714316401437,NULL,NULL,NULL),
	(275,2113,NULL,0,1714316401440,NULL,NULL,NULL),
	(276,1777,NULL,0,1714316400412,NULL,NULL,NULL),
	(277,1693,NULL,0,1714316400415,NULL,NULL,NULL),
	(278,1694,NULL,0,1714316400418,NULL,NULL,NULL),
	(279,2024,NULL,0,1714316400422,NULL,NULL,NULL),
	(280,2023,NULL,0,1714316400426,NULL,NULL,NULL),
	(281,2022,NULL,0,1714316400430,NULL,NULL,NULL),
	(282,2021,NULL,0,1714316400433,NULL,NULL,NULL),
	(283,2020,NULL,0,1714316400437,NULL,NULL,NULL),
	(284,2019,NULL,0,1714316400440,NULL,NULL,NULL),
	(285,2018,NULL,0,1714316400444,NULL,NULL,NULL),
	(286,2017,NULL,0,1714316400447,NULL,NULL,NULL),
	(287,2278,NULL,0,1714316400451,NULL,NULL,NULL),
	(288,2362,NULL,0,1714316400454,NULL,NULL,NULL),
	(289,2530,NULL,0,1714316400457,NULL,NULL,NULL),
	(290,2446,NULL,0,1714316400461,NULL,NULL,NULL),
	(291,2614,NULL,0,1714316400464,NULL,NULL,NULL),
	(292,2698,NULL,0,1714316400468,NULL,NULL,NULL),
	(293,2699,NULL,0,1714316400474,NULL,NULL,NULL),
	(294,2700,NULL,0,1714316400478,NULL,NULL,NULL),
	(295,2784,NULL,0,1714316400480,NULL,NULL,NULL),
	(296,2868,NULL,0,1714316400483,NULL,NULL,NULL),
	(297,2952,NULL,0,1714316400486,NULL,NULL,NULL),
	(298,2953,NULL,0,1714316401804,NULL,NULL,NULL),
	(299,3036,NULL,0,1714316401659,NULL,NULL,NULL),
	(300,3120,NULL,0,1714316401444,NULL,NULL,NULL),
	(301,3288,NULL,0,1714316400489,NULL,NULL,NULL),
	(302,3204,NULL,0,1714316400492,NULL,NULL,NULL),
	(303,3289,NULL,0,1714316400495,NULL,NULL,NULL),
	(304,3290,NULL,0,1714316400498,NULL,NULL,NULL),
	(305,3291,NULL,0,1714316400501,NULL,NULL,NULL),
	(306,3293,NULL,0,1714316400504,NULL,NULL,NULL),
	(307,3292,NULL,0,1714316400507,NULL,NULL,NULL),
	(308,3294,NULL,0,1714316400511,NULL,NULL,NULL),
	(309,3295,NULL,0,1714316400514,NULL,NULL,NULL),
	(310,3296,NULL,0,1714316400518,NULL,NULL,NULL),
	(311,3297,NULL,0,1714316400525,NULL,NULL,NULL),
	(312,3298,NULL,0,1714316400528,NULL,NULL,NULL),
	(313,3299,NULL,0,1714316400531,NULL,NULL,NULL),
	(314,3215,NULL,0,1714316401447,NULL,NULL,NULL),
	(315,3131,NULL,0,1714316401663,NULL,NULL,NULL),
	(316,3132,NULL,0,1714316401583,NULL,NULL,NULL),
	(317,3379,NULL,0,1714316400534,NULL,NULL,NULL),
	(318,3380,NULL,0,1714316400537,NULL,NULL,NULL),
	(319,3464,NULL,0,1714316400539,NULL,NULL,NULL),
	(320,3463,NULL,0,1714316400542,NULL,NULL,NULL),
	(321,3547,NULL,0,1714316400544,NULL,NULL,NULL),
	(322,3548,NULL,0,1714316400548,NULL,NULL,NULL),
	(323,3549,NULL,0,1714316400550,NULL,NULL,NULL),
	(324,3550,NULL,0,1714316400553,NULL,NULL,NULL),
	(325,3551,NULL,0,1714316400555,NULL,NULL,NULL),
	(326,3546,NULL,0,1714316400557,NULL,NULL,NULL),
	(327,3545,NULL,0,1714316400560,NULL,NULL,NULL),
	(328,3544,NULL,0,1714316400562,NULL,NULL,NULL),
	(329,3460,NULL,0,1714316400565,NULL,NULL,NULL),
	(330,3376,NULL,0,1714316400567,NULL,NULL,NULL),
	(331,3552,NULL,0,1714316400569,NULL,NULL,NULL),
	(332,3469,NULL,0,1714316400572,NULL,NULL,NULL),
	(333,3385,NULL,0,1714316400574,NULL,NULL,NULL),
	(334,3553,NULL,0,1714316400577,NULL,NULL,NULL),
	(335,3554,NULL,0,1714316400579,NULL,NULL,NULL),
	(336,3555,NULL,0,1714316400582,NULL,NULL,NULL),
	(337,3556,NULL,0,1714316400585,NULL,NULL,NULL),
	(338,3557,NULL,0,1714316400587,NULL,NULL,NULL),
	(339,3559,NULL,0,1714316400590,NULL,NULL,NULL),
	(340,3558,NULL,0,1714316400593,NULL,NULL,NULL),
	(341,3475,NULL,0,1714316400596,NULL,NULL,NULL),
	(342,3391,NULL,0,1714316401666,NULL,NULL,NULL),
	(343,3307,NULL,0,1714316401451,NULL,NULL,NULL),
	(344,3223,NULL,0,1714316401718,NULL,NULL,NULL),
	(345,3139,NULL,0,1714316401507,NULL,NULL,NULL),
	(346,2972,NULL,0,1714316401454,NULL,NULL,NULL),
	(347,2971,NULL,0,1714316400599,NULL,NULL,NULL),
	(348,3055,NULL,0,1714316400603,NULL,NULL,NULL),
	(349,2888,NULL,0,1714316401779,NULL,NULL,NULL),
	(350,3392,NULL,0,1714316401536,NULL,NULL,NULL),
	(351,3142,NULL,0,1714316401670,NULL,NULL,NULL),
	(352,3226,NULL,0,1714316400608,NULL,NULL,NULL),
	(353,3310,NULL,0,1714316400611,NULL,NULL,NULL),
	(354,3394,NULL,0,1714316400614,NULL,NULL,NULL),
	(355,3393,NULL,0,1714316400617,NULL,NULL,NULL),
	(356,3478,NULL,0,1714316400620,NULL,NULL,NULL),
	(357,3562,NULL,0,1714316400623,NULL,NULL,NULL),
	(358,3646,NULL,0,1714316400627,NULL,NULL,NULL),
	(359,3730,NULL,0,1714316401457,NULL,NULL,NULL),
	(360,3731,NULL,0,1714316401673,NULL,NULL,NULL),
	(361,3058,NULL,0,1714316400631,NULL,NULL,NULL),
	(362,2974,NULL,0,1714316400634,NULL,NULL,NULL),
	(363,2975,NULL,0,1714316400638,NULL,NULL,NULL),
	(364,2976,NULL,0,1714316400640,NULL,NULL,NULL),
	(365,2977,NULL,0,1714316400643,NULL,NULL,NULL),
	(366,2978,NULL,0,1714316400647,NULL,NULL,NULL),
	(367,2979,NULL,0,1714316400650,NULL,NULL,NULL),
	(368,2980,NULL,0,1714316400653,NULL,NULL,NULL),
	(369,2981,NULL,0,1714316400657,NULL,NULL,NULL),
	(370,2895,NULL,0,1714316401676,NULL,NULL,NULL),
	(371,2811,NULL,0,1714316401603,NULL,NULL,NULL),
	(372,2727,NULL,0,1714316401680,NULL,NULL,NULL),
	(373,2894,NULL,0,1714316401586,NULL,NULL,NULL),
	(374,2810,NULL,0,1714316401461,NULL,NULL,NULL),
	(375,1337,NULL,0,1714316401683,NULL,NULL,NULL),
	(376,1587,NULL,0,1714316401752,NULL,NULL,NULL),
	(377,1669,NULL,0,1714316401755,NULL,NULL,NULL),
	(378,1500,NULL,0,1714316401606,NULL,NULL,NULL),
	(379,2677,NULL,0,1714316401511,NULL,NULL,NULL),
	(380,901,NULL,0,1714316401687,NULL,NULL,NULL),
	(381,307,NULL,0,1714316401467,NULL,NULL,NULL),
	(382,305,NULL,0,1714316401471,NULL,NULL,NULL),
	(383,1061,NULL,0,1714316401690,NULL,NULL,NULL),
	(384,1060,NULL,0,1714316401474,NULL,NULL,NULL),
	(385,3668,NULL,0,1714316401514,NULL,NULL,NULL),
	(386,3667,NULL,0,1714316401693,NULL,NULL,NULL),
	(387,3666,NULL,0,1714316401477,NULL,NULL,NULL),
	(388,2475,NULL,0,1714316401481,NULL,NULL,NULL),
	(389,2308,NULL,0,1714316401696,NULL,NULL,NULL),
	(390,2138,NULL,0,1714316401699,NULL,NULL,NULL),
	(391,2643,NULL,0,1714316400660,NULL,NULL,NULL),
	(392,2559,NULL,0,1714316400663,NULL,NULL,NULL),
	(393,2391,NULL,0,1714316400666,NULL,NULL,NULL),
	(394,2307,NULL,0,1714316400670,NULL,NULL,NULL),
	(395,2223,NULL,0,1714316400674,NULL,NULL,NULL),
	(396,2139,NULL,0,1714316400677,NULL,NULL,NULL),
	(397,306,NULL,0,1714316400680,NULL,NULL,NULL),
	(398,221,NULL,0,1714316400686,NULL,NULL,NULL),
	(399,137,NULL,0,1714316400689,NULL,NULL,NULL),
	(400,53,NULL,0,1714316400692,NULL,NULL,NULL),
	(401,985,NULL,0,1714316400695,NULL,NULL,NULL),
	(402,1069,NULL,0,1714316400699,NULL,NULL,NULL),
	(403,1153,NULL,0,1714316400702,NULL,NULL,NULL),
	(404,1237,NULL,0,1714316400705,NULL,NULL,NULL),
	(405,1145,NULL,0,1714316400709,NULL,NULL,NULL),
	(406,1229,NULL,0,1714316400712,NULL,NULL,NULL),
	(407,1313,NULL,0,1714316400716,NULL,NULL,NULL),
	(408,1397,NULL,0,1714316400719,NULL,NULL,NULL),
	(409,1481,NULL,0,1714316400723,NULL,NULL,NULL),
	(410,1565,NULL,0,1714316400727,NULL,NULL,NULL),
	(411,1649,NULL,0,1714316400731,NULL,NULL,NULL),
	(412,1650,NULL,0,1714316400734,NULL,NULL,NULL),
	(413,1651,NULL,0,1714316400737,NULL,NULL,NULL),
	(414,1652,NULL,0,1714316400741,NULL,NULL,NULL),
	(415,1653,NULL,0,1714316400744,NULL,NULL,NULL),
	(416,1654,NULL,0,1714316400747,NULL,NULL,NULL),
	(417,1737,NULL,0,1714316400752,NULL,NULL,NULL),
	(418,1821,NULL,0,1714316400755,NULL,NULL,NULL),
	(419,1905,NULL,0,1714316400759,NULL,NULL,NULL),
	(420,1904,NULL,0,1714316400765,NULL,NULL,NULL),
	(421,1903,NULL,0,1714316400768,NULL,NULL,NULL),
	(422,1902,NULL,0,1714316400772,NULL,NULL,NULL),
	(423,1818,NULL,0,1714316400776,NULL,NULL,NULL),
	(424,1734,NULL,0,1714316400779,NULL,NULL,NULL),
	(425,1989,NULL,0,1714316400783,NULL,NULL,NULL),
	(426,2157,NULL,0,1714316400786,NULL,NULL,NULL),
	(427,2241,NULL,0,1714316400789,NULL,NULL,NULL),
	(428,2409,NULL,0,1714316400793,NULL,NULL,NULL),
	(429,2156,NULL,0,1714316400796,NULL,NULL,NULL),
	(430,2492,NULL,0,1714316400800,NULL,NULL,NULL),
	(431,2407,NULL,0,1714316400804,NULL,NULL,NULL),
	(432,2406,NULL,0,1714316400807,NULL,NULL,NULL),
	(433,2243,NULL,0,1714316400810,NULL,NULL,NULL),
	(434,2244,NULL,0,1714316400814,NULL,NULL,NULL),
	(435,2328,NULL,0,1714316400818,NULL,NULL,NULL),
	(436,2412,NULL,0,1714316400821,NULL,NULL,NULL),
	(437,2496,NULL,0,1714316400825,NULL,NULL,NULL),
	(438,2495,NULL,0,1714316400829,NULL,NULL,NULL),
	(439,2494,NULL,0,1714316400832,NULL,NULL,NULL),
	(440,2493,NULL,0,1714316400835,NULL,NULL,NULL),
	(441,2660,NULL,0,1714316400838,NULL,NULL,NULL),
	(442,2577,NULL,0,1714316400842,NULL,NULL,NULL),
	(443,2578,NULL,0,1714316400849,NULL,NULL,NULL),
	(444,2579,NULL,0,1714316400853,NULL,NULL,NULL),
	(445,2744,NULL,0,1714316400883,NULL,NULL,NULL),
	(446,2828,NULL,0,1714316400886,NULL,NULL,NULL),
	(447,2829,NULL,0,1714316400889,NULL,NULL,NULL),
	(448,2830,NULL,0,1714316400892,NULL,NULL,NULL),
	(449,2831,NULL,0,1714316400894,NULL,NULL,NULL),
	(450,4098,NULL,0,1711044230601,NULL,NULL,NULL),
	(451,4182,NULL,0,1711044233431,NULL,NULL,NULL),
	(452,4350,NULL,0,1711044235688,NULL,NULL,NULL),
	(453,3764,NULL,0,1711044244266,NULL,NULL,NULL),
	(454,2834,NULL,0,1714316400897,NULL,NULL,NULL),
	(455,2835,NULL,0,1714316400900,NULL,NULL,NULL),
	(456,2836,NULL,0,1714316400903,NULL,NULL,NULL),
	(457,2753,NULL,0,1714316400906,NULL,NULL,NULL),
	(458,2502,NULL,0,1714316400909,NULL,NULL,NULL),
	(459,2418,NULL,0,1714316400912,NULL,NULL,NULL),
	(460,2501,NULL,0,1714316400915,NULL,NULL,NULL),
	(461,2500,NULL,0,1714316400918,NULL,NULL,NULL),
	(462,2499,NULL,0,1714316400921,NULL,NULL,NULL),
	(463,2250,NULL,0,1714316400924,NULL,NULL,NULL),
	(464,2251,NULL,0,1714316400927,NULL,NULL,NULL),
	(465,2840,NULL,0,1714316400930,NULL,NULL,NULL),
	(466,2756,NULL,0,1714316400933,NULL,NULL,NULL),
	(467,2755,NULL,0,1714316400936,NULL,NULL,NULL),
	(468,2754,NULL,0,1714316400939,NULL,NULL,NULL),
	(469,2676,NULL,0,1714316400942,NULL,NULL,NULL),
	(470,2675,NULL,0,1714316400946,NULL,NULL,NULL),
	(471,2674,NULL,0,1714316400952,NULL,NULL,NULL),
	(472,2673,NULL,0,1714316400955,NULL,NULL,NULL),
	(473,2757,NULL,0,1714316400958,NULL,NULL,NULL),
	(474,1916,NULL,0,1714316400962,NULL,NULL,NULL),
	(475,1832,NULL,0,1714316400966,NULL,NULL,NULL),
	(476,1747,NULL,0,1714316400969,NULL,NULL,NULL),
	(477,1495,NULL,0,1714316400973,NULL,NULL,NULL),
	(478,1411,NULL,0,1714316400977,NULL,NULL,NULL),
	(479,1406,NULL,0,1714316400980,NULL,NULL,NULL),
	(480,1408,NULL,0,1714316400983,NULL,NULL,NULL),
	(481,1740,NULL,0,1714316400987,NULL,NULL,NULL),
	(482,1825,NULL,0,1714316400990,NULL,NULL,NULL),
	(483,1909,NULL,0,1714316400993,NULL,NULL,NULL),
	(484,1993,NULL,0,1714316400996,NULL,NULL,NULL),
	(485,1738,NULL,0,1714316401000,NULL,NULL,NULL),
	(486,1822,NULL,0,1714316401004,NULL,NULL,NULL),
	(487,2074,NULL,0,1714316401007,NULL,NULL,NULL),
	(488,2158,NULL,0,1714316401010,NULL,NULL,NULL),
	(489,1990,NULL,0,1714316401013,NULL,NULL,NULL),
	(490,1750,NULL,0,1714316401017,NULL,NULL,NULL),
	(491,1751,NULL,0,1714316401020,NULL,NULL,NULL),
	(492,1752,NULL,0,1714316401024,NULL,NULL,NULL),
	(493,1668,NULL,0,1714316401027,NULL,NULL,NULL),
	(494,1584,NULL,0,1714316401033,NULL,NULL,NULL),
	(495,1670,NULL,0,1714316401037,NULL,NULL,NULL),
	(496,1671,NULL,0,1714316401040,NULL,NULL,NULL),
	(497,1503,NULL,0,1714316401044,NULL,NULL,NULL),
	(498,1419,NULL,0,1714316401048,NULL,NULL,NULL),
	(499,1335,NULL,0,1714316401051,NULL,NULL,NULL),
	(500,1336,NULL,0,1714316401054,NULL,NULL,NULL),
	(501,1173,NULL,4,1714316401362,NULL,NULL,NULL),
	(502,1339,NULL,0,1714316401057,NULL,NULL,NULL),
	(503,1338,NULL,0,1714316401061,NULL,NULL,NULL),
	(504,1255,NULL,0,1714316401064,NULL,NULL,NULL),
	(505,1171,NULL,0,1714316401066,NULL,NULL,NULL),
	(506,1340,NULL,0,1714316401069,NULL,NULL,NULL),
	(507,1341,NULL,0,1714316401071,NULL,NULL,NULL),
	(508,1342,NULL,0,1714316401077,NULL,NULL,NULL),
	(509,1343,NULL,0,1714316401080,NULL,NULL,NULL),
	(510,1344,NULL,0,1714316401083,NULL,NULL,NULL),
	(511,1258,NULL,0,1714316401086,NULL,NULL,NULL),
	(512,1089,NULL,0,1714316401088,NULL,NULL,NULL),
	(513,1090,NULL,0,1714316401091,NULL,NULL,NULL),
	(514,1174,NULL,0,1714316401094,NULL,NULL,NULL),
	(515,1489,NULL,0,1714316401097,NULL,NULL,NULL),
	(516,1573,NULL,0,1714316401100,NULL,NULL,NULL),
	(517,1657,NULL,0,1714316401103,NULL,NULL,NULL),
	(518,2497,NULL,0,1714316401106,NULL,NULL,NULL),
	(519,2498,NULL,0,1714316401108,NULL,NULL,NULL),
	(520,2331,NULL,0,1714316401112,NULL,NULL,NULL),
	(521,2247,NULL,0,1714316401115,NULL,NULL,NULL),
	(522,2248,NULL,0,1714316401118,NULL,NULL,NULL),
	(523,2249,NULL,0,1714316401121,NULL,NULL,NULL),
	(524,2245,NULL,0,1714316401124,NULL,NULL,NULL),
	(525,2246,NULL,0,1714316401127,NULL,NULL,NULL),
	(526,2160,NULL,0,1714316401130,NULL,NULL,NULL),
	(527,2077,NULL,0,1714316401134,NULL,NULL,NULL),
	(528,2162,NULL,0,1714316401137,NULL,NULL,NULL),
	(529,2164,NULL,0,1714316401140,NULL,NULL,NULL),
	(530,2081,NULL,0,1714316401144,NULL,NULL,NULL),
	(531,1996,NULL,0,1714316401147,NULL,NULL,NULL),
	(532,1828,NULL,0,1714316401150,NULL,NULL,NULL),
	(533,1660,NULL,0,1714316401154,NULL,NULL,NULL),
	(534,1658,NULL,0,1714316401157,NULL,NULL,NULL),
	(535,1826,NULL,0,1714316401160,NULL,NULL,NULL),
	(536,1994,NULL,0,1714316401163,NULL,NULL,NULL),
	(537,1490,NULL,0,1714316401167,NULL,NULL,NULL),
	(538,1574,NULL,0,1714316401171,NULL,NULL,NULL),
	(539,1911,NULL,0,1714316401174,NULL,NULL,NULL),
	(540,2079,NULL,0,1714316401177,NULL,NULL,NULL),
	(541,1743,NULL,0,1714316401181,NULL,NULL,NULL),
	(542,1999,NULL,0,1714316401184,NULL,NULL,NULL),
	(543,1915,NULL,0,1714316401189,NULL,NULL,NULL),
	(544,1830,NULL,0,1714316401192,NULL,NULL,NULL),
	(545,1995,NULL,0,1714316401208,NULL,NULL,NULL),
	(546,1827,NULL,0,1714316401211,NULL,NULL,NULL),
	(547,2392,NULL,0,1714316401214,NULL,NULL,NULL),
	(548,2393,NULL,0,1714316401218,NULL,NULL,NULL),
	(549,2394,NULL,0,1714316401221,NULL,NULL,NULL),
	(550,2395,NULL,0,1714316401225,NULL,NULL,NULL),
	(551,2396,NULL,0,1714316401228,NULL,NULL,NULL),
	(552,2397,NULL,0,1714316401231,NULL,NULL,NULL),
	(553,2398,NULL,0,1714316401234,NULL,NULL,NULL),
	(554,2484,NULL,0,1714316401237,NULL,NULL,NULL),
	(555,2400,NULL,0,1714316401240,NULL,NULL,NULL),
	(556,2399,NULL,0,1714316401244,NULL,NULL,NULL),
	(557,2403,NULL,0,1714316401247,NULL,NULL,NULL),
	(558,2404,NULL,0,1714316401250,NULL,NULL,NULL),
	(559,2405,NULL,0,1714316401253,NULL,NULL,NULL),
	(560,2487,NULL,0,1714316401256,NULL,NULL,NULL),
	(561,2571,NULL,0,1714316401259,NULL,NULL,NULL),
	(562,2739,NULL,0,1714316401263,NULL,NULL,NULL),
	(563,2655,NULL,0,1714316401266,NULL,NULL,NULL),
	(564,2738,NULL,0,1714316401269,NULL,NULL,NULL),
	(565,2737,NULL,0,1714316401272,NULL,NULL,NULL),
	(566,2736,NULL,0,1714316401276,NULL,NULL,NULL),
	(567,2652,NULL,0,1714316401281,NULL,NULL,NULL),
	(568,2568,NULL,0,1714316401284,NULL,NULL,NULL),
	(569,2570,NULL,4,1714316401396,NULL,NULL,NULL),
	(570,2954,NULL,0,1714316401287,NULL,NULL,NULL),
	(571,2701,NULL,0,1714316401291,NULL,NULL,NULL),
	(572,2702,NULL,0,1714316401294,NULL,NULL,NULL),
	(573,2703,NULL,0,1714316401298,NULL,NULL,NULL),
	(574,2787,NULL,0,1714316401301,NULL,NULL,NULL),
	(575,2871,NULL,0,1714316401304,NULL,NULL,NULL),
	(576,2955,NULL,0,1714316401308,NULL,NULL,NULL),
	(577,2114,NULL,0,1714316401312,NULL,NULL,NULL),
	(578,2115,NULL,0,1714316401317,NULL,NULL,NULL),
	(579,2031,NULL,0,1714316401320,NULL,NULL,NULL),
	(580,3065,NULL,0,1714316401323,NULL,NULL,NULL),
	(581,3149,NULL,0,1714316401336,NULL,NULL,NULL),
	(582,3233,NULL,0,1714316401339,NULL,NULL,NULL),
	(583,3062,NULL,0,1714316401342,NULL,NULL,NULL),
	(584,3146,NULL,0,1714316401345,NULL,NULL,NULL),
	(585,3230,NULL,0,1714316401348,NULL,NULL,NULL),
	(586,3231,NULL,0,1714316401352,NULL,NULL,NULL),
	(587,3232,NULL,0,1714316401355,NULL,NULL,NULL),
	(588,2786,NULL,4,1714316401783,NULL,NULL,NULL),
	(589,2664,NULL,0,1712764800382,NULL,NULL,NULL),
	(590,2580,NULL,0,1712764800337,NULL,NULL,NULL),
	(591,2665,NULL,0,1712764800368,NULL,NULL,NULL),
	(592,2749,NULL,0,1712764800373,NULL,NULL,NULL),
	(593,3669,NULL,0,1713708000692,NULL,NULL,NULL);

/*!40000 ALTER TABLE `family_wars_zones` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `forbes_families` (
  `id` int(11) DEFAULT NULL,
  `familyId` mediumint(8) DEFAULT NULL,
  `name` varchar(24) DEFAULT NULL,
  `tag` varchar(4) DEFAULT NULL,
  `static` int(11) DEFAULT NULL,
  `username` varchar(50) DEFAULT NULL,
  `amountMembers` int(11) DEFAULT NULL,
  `wealth` bigint(16) DEFAULT NULL,
  `logo` varchar(150) DEFAULT NULL,
  `prevId` int(11) DEFAULT NULL,
  UNIQUE KEY `id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Список Форбс семей';



CREATE TABLE `forbes_players` (
  `id` int(11) DEFAULT NULL,
  `username` varchar(50) DEFAULT NULL,
  `static` int(11) DEFAULT NULL,
  `level` int(11) DEFAULT NULL,
  `biz` varchar(50) DEFAULT NULL,
  `family` varchar(50) DEFAULT NULL,
  `wealth` bigint(16) DEFAULT NULL,
  `socialClub` varchar(16) DEFAULT NULL,
  `prevId` int(11) DEFAULT NULL,
  UNIQUE KEY `id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Список форбс игроков';



CREATE TABLE `forbes_year_wealth` (
  `type` varchar(6) DEFAULT NULL,
  `wealth` blob DEFAULT NULL,
  UNIQUE KEY `type` (`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Денежное состояние по месяцам ТОП-1';

LOCK TABLES `forbes_year_wealth` WRITE;
/*!40000 ALTER TABLE `forbes_year_wealth` DISABLE KEYS */;

INSERT INTO `forbes_year_wealth` (`type`, `wealth`)
VALUES
	('player',X'5B302C302C302C302C302C302C302C302C302C302C302C305D'),
	('family',X'5B302C302C302C302C302C302C35363638393030302C302C302C302C302C305D');

/*!40000 ALTER TABLE `forbes_year_wealth` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `fraction_blacklist` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `fractionId` mediumint(7) NOT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `actionBy` mediumint(7) unsigned DEFAULT NULL,
  `reason` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `date` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `actionBy` (`actionBy`),
  KEY `fractionId` (`fractionId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Черный список фракции';



CREATE TABLE `fraction_craft_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fractionId` smallint(5) unsigned DEFAULT NULL,
  `itemId` smallint(5) unsigned DEFAULT NULL,
  `value` tinyint(1) unsigned DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `Индекс 4` (`fractionId`,`itemId`),
  KEY `fractionId` (`fractionId`),
  KEY `itemId` (`itemId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `fraction_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `fractionId` smallint(5) unsigned DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `actionBy` mediumint(7) unsigned DEFAULT NULL,
  `test` mediumint(7) unsigned DEFAULT NULL,
  `type` varchar(100) DEFAULT NULL,
  `reason` varchar(150) DEFAULT NULL,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`args`)),
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `type` (`type`),
  KEY `date` (`date`),
  KEY `actionBy` (`actionBy`),
  KEY `fractionId` (`fractionId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Лог вступления, увольнения и т.д. по фракциях';



CREATE TABLE `fraction_money_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `fractionId` smallint(5) unsigned DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `toFractionid` smallint(5) unsigned DEFAULT NULL,
  `toAccountId` mediumint(7) unsigned DEFAULT NULL,
  `amount` bigint(20) DEFAULT 0,
  `comment` varchar(128) DEFAULT NULL,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`args`)),
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fractionId` (`fractionId`),
  KEY `accountId` (`accountId`),
  KEY `date` (`date`),
  KEY `comment` (`comment`),
  KEY `amount` (`amount`),
  KEY `toFractionid` (`toFractionid`),
  KEY `toAccountId` (`toAccountId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `fraction_player_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fractionId` smallint(5) unsigned DEFAULT NULL,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `rank` tinyint(3) unsigned DEFAULT NULL,
  `key` varchar(20) DEFAULT NULL,
  `value` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `key` (`key`) USING BTREE,
  KEY `fractionId` (`fractionId`),
  KEY `accountId` (`accountId`),
  KEY `rank` (`rank`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `fraction_rank_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fractionId` smallint(5) unsigned DEFAULT NULL,
  `rankId` mediumint(8) unsigned DEFAULT NULL,
  `key` varchar(20) DEFAULT NULL,
  `value` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni` (`fractionId`,`rankId`,`key`) USING BTREE,
  KEY `fractionId` (`fractionId`),
  KEY `key` (`key`),
  KEY `rank` (`rankId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `fraction_ranks` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `fractionId` smallint(5) unsigned DEFAULT NULL,
  `rank` tinyint(3) unsigned DEFAULT NULL,
  `name` varchar(24) DEFAULT NULL,
  KEY `id` (`id`),
  KEY `rank` (`rank`),
  KEY `fractionId` (`fractionId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `fraction_vehicle_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fractionId` smallint(5) unsigned DEFAULT NULL,
  `vehicle` varchar(24) DEFAULT NULL,
  `value` tinyint(1) unsigned DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `Индекс 4` (`fractionId`,`vehicle`),
  KEY `fractionId` (`fractionId`) USING BTREE,
  KEY `vehicle` (`vehicle`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC;



CREATE TABLE `fractions` (
  `id` tinyint(2) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(32) DEFAULT NULL,
  `leaderId` mediumint(7) unsigned DEFAULT NULL,
  `lockEnter` tinyint(1) unsigned DEFAULT 0,
  `money` bigint(12) unsigned DEFAULT 0,
  `awardCount` tinyint(3) unsigned DEFAULT 0,
  `awardAmount` bigint(20) unsigned DEFAULT 0,
  `financingAmount` bigint(20) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `leaderId` (`leaderId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Фракции, Лидеры';

LOCK TABLES `fractions` WRITE;
/*!40000 ALTER TABLE `fractions` DISABLE KEYS */;

INSERT INTO `fractions` (`id`, `title`, `leaderId`, `lockEnter`, `money`, `awardCount`, `awardAmount`, `financingAmount`)
VALUES
	(1,'Police Department',NULL,0,739286027973,0,0,0),
	(2,'Emergency Medical Services',NULL,0,104459800,0,0,0),
	(3,'Sheriff Department',NULL,0,105197593,0,0,0),
	(4,'San-Andreas National Guard',NULL,0,840011257480,0,0,0),
	(5,'Government',NULL,0,23759775708,0,0,0),
	(6,'Weazel News',NULL,0,**********,0,0,0),
	(7,'FIB',NULL,0,150354228863,0,0,0),
	(8,'The Ballas Gang',NULL,0,**********,0,0,0),
	(9,'Los Santos Vagos',NULL,0,**********,0,0,0),
	(10,'The Families',NULL,0,712370246,0,0,0),
	(11,'Bloods',NULL,0,16576189368,0,0,0),
	(12,'Marabunta Grande',NULL,0,12740960265,0,0,0);

/*!40000 ALTER TABLE `fractions` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `fractions_captures_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) DEFAULT NULL,
  `fractionId` int(11) DEFAULT NULL,
  `attacksCount` int(11) DEFAULT 3,
  `defencesCount` int(11) DEFAULT 3,
  `lastAttackTime` int(11) DEFAULT NULL,
  `lastDefenceTime` int(11) DEFAULT NULL,
  `freezeAttacks` tinyint(4) DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `friends` (
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `friendId` mediumint(7) unsigned DEFAULT NULL,
  KEY `accountId` (`accountId`),
  KEY `friendId` (`friendId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `fuel_stations` (
  `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(24) DEFAULT 'Заправочная станция',
  `type` varchar(10) DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `price` int(11) unsigned DEFAULT 5000000,
  `cash` bigint(12) unsigned DEFAULT 0,
  `bank` bigint(12) unsigned DEFAULT 0,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `warehouse` blob DEFAULT NULL,
  `prices` blob DEFAULT NULL,
  `discounts` blob DEFAULT NULL,
  `mafiaId` tinyint(2) unsigned DEFAULT 0,
  `mafiaCash` bigint(12) unsigned DEFAULT 0,
  `defaultMafiaId` tinyint(2) unsigned DEFAULT 0,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `title` (`title`),
  KEY `accountId` (`accountId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`),
  KEY `marketplaceId` (`marketplaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Бизнесы - Заправки';

LOCK TABLES `fuel_stations` WRITE;
/*!40000 ALTER TABLE `fuel_stations` DISABLE KEYS */;

INSERT INTO `fuel_stations` (`id`, `title`, `type`, `accountId`, `price`, `cash`, `bank`, `posX`, `posY`, `posZ`, `warehouse`, `prices`, `discounts`, `mafiaId`, `mafiaCash`, `defaultMafiaId`, `paidUntil`, `auctionId`, `marketplaceId`)
VALUES
	(1,'Заправка Xero','xero',NULL,*********,3581079,1231147,'-2072.9578','-324.9384','13.316',NULL,NULL,NULL,14,0,14,'2023-03-13 21:21:00',1636,NULL),
	(2,'Заправка Globe','globe',NULL,1********,********,4772438,'-341.9143','-1477.6022','30.4054',NULL,NULL,NULL,18,0,14,'2023-03-08 21:51:00',1012,NULL),
	(3,'Заправка Ron','ron',NULL,*********,658943,317458,'820.8806','-1040.4833','26.7508',NULL,NULL,NULL,16,0,14,'2023-03-22 22:10:01',2110,NULL),
	(4,'Заправка Ron','ron',NULL,*********,2002530,883744,'1209.1766','-1389.0835','35.3769',NULL,NULL,NULL,16,0,19,'2023-03-27 22:01:00',2641,NULL),
	(5,'Заправка Globe','globe',NULL,*********,4499520,1506210,'647.1927','269.5044','103.2455',NULL,NULL,NULL,16,0,19,'2023-03-25 21:11:00',2339,NULL),
	(6,'Заправка Xero','xero',NULL,*********,4132581,2284811,'289.2104','-1269.3579','29.4408',NULL,NULL,NULL,15,0,15,'2023-03-27 21:41:00',2623,NULL),
	(7,'Заправка LTD','ltd',NULL,1********,3145816,6408160,'-51.2797','-1759.0415','29.4396',NULL,NULL,NULL,16,0,15,'2023-03-25 21:01:00',2342,NULL),
	(8,'Заправка Ron','ron',NULL,1********,6770400,3035657,'163.7717','-1556.8768','29.2618',NULL,NULL,NULL,18,0,15,'2023-03-27 22:01:01',2644,NULL),
	(9,'Заправка LTD','ltd',NULL,90000000,3769697,876290,'-707.9532','-917.074','19.2143',NULL,NULL,NULL,16,0,15,'2023-03-13 21:01:00',1638,NULL),
	(10,'Заправка Ron','ron',NULL,********,2942538,2118390,'2559.6035','373.4013','108.6211',NULL,NULL,NULL,16,0,15,'2023-03-19 21:31:01',1959,NULL),
	(11,'Заправка Ron','ron',NULL,********,13283440,16374202,'-2543.6704','2316.0598','33.2161',NULL,NULL,NULL,16,0,15,'2023-03-27 21:21:00',2625,NULL),
	(12,'Заправка Ron','ron',NULL,60000000,5684110,2756689,'157.8363','6640.4907','31.5933',NULL,NULL,NULL,17,0,15,'2023-03-22 21:41:01',2104,NULL),
	(13,'Заправка Xero','xero',NULL,********,103344104,54926226,'-93.2996','6410.4971','31.6405',NULL,NULL,NULL,15,0,15,'2023-03-19 21:11:00',1960,NULL),
	(14,'Заправка Globe','globe',NULL,********,7598805,4105728,'1706.1910','6425.5400','32.7687',NULL,NULL,NULL,15,0,15,'2023-03-13 22:56:00',1658,NULL),
	(15,'Заправка Xero','xero',NULL,********,11220509,5513346,'2673.7749','3266.8940','55.2405',NULL,NULL,NULL,16,0,15,'2023-03-26 21:31:01',2480,NULL),
	(16,'Заправка LTD','ltd',NULL,********,12565556,5760972,'-1816.7769','791.5074','138.1084',NULL,NULL,NULL,18,0,15,'2023-03-27 21:41:01',2627,NULL),
	(17,'Заправка Xero','xero',NULL,********,1883525,74437,'1997.4506','3779.8166','32.1808',NULL,NULL,NULL,18,0,15,'2023-03-12 21:51:01',1477,NULL),
	(18,'Заправка Xero','xero',NULL,********,19074365,7862355,'-531.2606','-1220.1709','18.455',NULL,NULL,NULL,18,0,15,'2023-03-27 21:41:01',2631,NULL),
	(19,'Заправка LTD','ltd',NULL,********,379679,74180,'1168.3','-324.5508','69.2875',NULL,NULL,NULL,15,0,15,'2023-03-19 21:41:01',1962,NULL),
	(20,'Заправка Globe','globe',NULL,********,9236110,3199391,'265.9714','2598.4072','44.8230',NULL,NULL,NULL,18,0,15,'2023-03-27 21:51:01',2634,NULL),
	(21,'Заправка Ron','ron',NULL,********,30561911,11085287,'-1426.8911','-270.6272','46.2645',NULL,NULL,NULL,16,0,15,'2023-03-22 21:11:00',2105,NULL),
	(22,'Заправка LTD','ltd',NULL,60000000,2456314,905674,'46.4730','2789.1875','57.8782',NULL,NULL,NULL,15,0,15,'2023-03-20 21:21:00',2019,NULL),
	(23,'Заправка LTD','ltd',NULL,********,1231565,75,'1039.3331','2664.2248','39.5510',NULL,NULL,NULL,16,0,15,'2023-03-20 21:31:02',2021,NULL),
	(24,'Заправка LTD','ltd',NULL,********,5433916,14931806,'1200.8302','2655.9650','37.8518',NULL,NULL,NULL,16,0,15,'2023-03-27 21:51:01',2636,NULL),
	(25,'Заправка LTD','ltd',NULL,********,960,598942,'-1315.53','-2658.46','13.9',NULL,NULL,NULL,15,0,15,'2023-03-13 22:01:01',1659,NULL),
	(26,'Заправка LTD','ltd',NULL,********,55619220,30883944,'1694.0895','4923.6953','42.2317',NULL,NULL,NULL,15,0,15,'2023-03-25 22:10:01',2350,NULL),
	(27,'Заправка Xero','xero',NULL,60000000,10177871,5144620,'1776.6943','3327.5664','41.4332',NULL,NULL,NULL,16,0,15,'2023-03-03 21:21:01',576,NULL),
	(28,'Заправка Xero','xero',NULL,********,60,60,'-790.29','-1460.08','5.00',NULL,NULL,NULL,15,0,15,'2023-03-27 21:21:01',2638,NULL);

/*!40000 ALTER TABLE `fuel_stations` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `gang_zones` (
  `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT,
  `fractionId` mediumint(7) unsigned DEFAULT NULL,
  `defaultFractionId` mediumint(7) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`fractionId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC COMMENT='Бизнесы - Заправки';

LOCK TABLES `gang_zones` WRITE;
/*!40000 ALTER TABLE `gang_zones` DISABLE KEYS */;

INSERT INTO `gang_zones` (`id`, `fractionId`, `defaultFractionId`)
VALUES
	(1,10,10),
	(2,11,10),
	(3,11,10),
	(4,11,10),
	(5,11,10),
	(6,8,10),
	(7,8,8),
	(8,11,10),
	(9,11,10),
	(10,8,10),
	(11,8,8),
	(12,8,8),
	(13,8,8),
	(14,11,10),
	(15,11,10),
	(16,11,10),
	(17,8,10),
	(18,8,8),
	(19,8,8),
	(20,8,8),
	(21,11,11),
	(22,11,11),
	(23,11,11),
	(24,11,10),
	(25,11,8),
	(26,11,8),
	(27,11,8),
	(28,11,11),
	(29,11,11),
	(30,11,11),
	(31,11,8),
	(32,11,8),
	(33,11,8),
	(34,11,8),
	(35,11,11),
	(36,11,11),
	(37,9,11),
	(38,9,11),
	(39,9,12),
	(40,9,12),
	(41,9,12),
	(42,9,12),
	(43,11,11),
	(44,11,11),
	(45,11,11),
	(46,9,9),
	(47,9,9),
	(48,12,12),
	(49,9,12),
	(50,12,12),
	(51,11,11),
	(52,12,9),
	(53,12,9),
	(54,12,12),
	(55,9,12),
	(56,12,12),
	(57,12,12),
	(58,12,9),
	(59,12,9),
	(60,12,9),
	(61,12,9),
	(62,12,9),
	(63,12,12),
	(64,12,12),
	(65,12,9),
	(66,12,9),
	(67,12,9),
	(68,12,9),
	(69,12,9),
	(70,12,12);

/*!40000 ALTER TABLE `gang_zones` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `gang_zones_reset` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reset_time` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Тут будет храниться дата для обнуления территорий';



CREATE TABLE `garages` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `type` enum('houses','apartments','offices') DEFAULT NULL,
  `keyId` smallint(5) unsigned DEFAULT NULL,
  `slot` mediumint(8) unsigned DEFAULT NULL,
  `vehicleId` mediumint(8) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `keyId` (`keyId`),
  KEY `vehicleId` (`vehicleId`),
  KEY `slot` (`slot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `gps_favourites` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `title` varchar(64) DEFAULT NULL,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `icon` varchar(20) DEFAULT NULL,
  `args` blob DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `userId` (`accountId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='GPS - вкладка Избранное';



CREATE TABLE `gps_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `title` varchar(64) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `icon` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `userId` (`accountId`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='GPS - история поездок';



CREATE TABLE `houses` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `posA` varchar(10) DEFAULT NULL,
  `garagePosX` varchar(10) DEFAULT NULL,
  `garagePosY` varchar(10) DEFAULT NULL,
  `garagePosZ` varchar(10) DEFAULT NULL,
  `garagePosA` varchar(10) DEFAULT NULL,
  `garage` tinyint(1) unsigned DEFAULT 0,
  `price` int(10) unsigned DEFAULT 15000,
  `interior` tinyint(1) unsigned DEFAULT 1,
  `rentPrice` int(10) unsigned DEFAULT 0,
  `cash` bigint(12) unsigned DEFAULT 0,
  `maxRent` tinyint(1) unsigned DEFAULT 1,
  `locked` tinyint(1) unsigned DEFAULT 0,
  `paidUntil` datetime DEFAULT '2020-02-28 00:00:00',
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  `familyId` int(10) unsigned DEFAULT NULL,
  `workShop` tinyint(2) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`),
  KEY `familyId` (`familyId`),
  KEY `marketplaceId` (`marketplaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Дома';

LOCK TABLES `houses` WRITE;
/*!40000 ALTER TABLE `houses` DISABLE KEYS */;

INSERT INTO `houses` (`id`, `accountId`, `posX`, `posY`, `posZ`, `posA`, `garagePosX`, `garagePosY`, `garagePosZ`, `garagePosA`, `garage`, `price`, `interior`, `rentPrice`, `cash`, `maxRent`, `locked`, `paidUntil`, `auctionId`, `marketplaceId`, `familyId`, `workShop`)
VALUES
	(1,NULL,'-152.0464','910.7946','235.6556','43.4028','-137.5365','902.2675','235.6608','327.0327',10,*********,9,0,0,11,0,'2024-05-06 14:18:01',2813,NULL,NULL,0),
	(2,NULL,'-85.8929','834.6198','235.92','99.7739','-104.4109','822.4041','235.7252','192.4892',10,*********,9,0,0,11,0,'2024-04-08 14:27:34',2739,NULL,NULL,0),
	(3,NULL,'1386.063','-593.2559','74.4855','54.3401','1378.6844','-596.4883','74.3379','51.2953',9,*********,7,0,0,6,0,'2024-04-22 14:45:25',2742,NULL,NULL,0),
	(4,NULL,'1367.324','-606.2888','74.7109','0.9276','1360.1154','-601.5080','74.0284','357.7449',6,*********,7,0,0,6,0,'2023-07-17 15:14:06',2675,NULL,NULL,1),
	(5,NULL,'1341.5111','-597.5344','74.7009','240.6981','1349.7545','-602.6109','74.3615','332.0924',6,*********,7,0,0,6,0,'2023-03-08 11:06:21',945,NULL,NULL,0),
	(6,NULL,'114.1565','-1960.8862','21.3345','27.6159','107.6622','-1950.2639','20.6783','95.8446',3,24000000,3,0,0,3,0,'2023-03-26 16:57:42',2437,NULL,NULL,0),
	(7,NULL,'1388.5627','-569.6313','74.4965','114.8122','1387.9106','-577.6074','74.3389','107.7927',6,*********,7,0,0,6,0,'2023-07-20 20:11:52',2678,NULL,NULL,0),
	(8,NULL,'1372.8579','-555.6636','74.6857','129.12','1363.6899','-551.4374','74.338','154.7747',6,*********,7,0,0,6,0,'2023-03-05 17:58:25',696,NULL,NULL,0),
	(9,NULL,'126.5953','-1929.6879','21.3824','158.0823','119.0351','-1931.5891','20.7472','113.4781',3,24000000,3,0,0,3,0,'2023-03-25 18:13:19',2319,NULL,NULL,0),
	(10,NULL,'1348.1196','-547.2548','73.8916','156.4224','1352.5153','-554.3884','74.1189','159.7113',6,*********,7,0,0,6,0,'2023-03-23 17:13:34',2156,NULL,NULL,0),
	(11,NULL,'117.9942','-1920.8809','21.3234','234.4395','110.1837','-1923.0333','20.7512','159.0224',3,24000000,3,0,0,3,0,'2023-03-26 19:11:00',2459,NULL,NULL,0),
	(12,NULL,'1323.5656','-582.7784','73.2464','336.2701','1319.3092','-575.3039','72.9915','339.0827',6,*********,7,0,0,6,0,'2023-03-13 16:49:04',1592,NULL,NULL,0),
	(13,NULL,'1328.3461','-535.9858','72.4408','69.7421','1319.3113','-531.8129','72.122','159.3832',6,*********,7,0,0,6,0,'2023-03-23 21:01:01',2178,NULL,NULL,0),
	(14,NULL,'100.9493','-1912.3552','21.4074','334.2883','85.5573','-1918.351','20.7362','45.2649',3,24000000,3,0,0,3,0,'2023-03-04 18:34:00',628,NULL,NULL,0),
	(15,NULL,'76.6485','-1948.0797','21.1742','82.5576','90.9589','-1938.1443','20.6112','33.0986',3,24000000,3,0,0,3,0,'2023-03-12 20:24:00',1470,NULL,NULL,0),
	(16,NULL,'72.3524','-1938.9054','21.3693','310.5303','80.188','-1932.0044','20.7269','315.888',3,24000000,3,0,0,3,0,'2023-03-02 18:21:00',514,NULL,NULL,0),
	(17,NULL,'-1022.8669','-998.3174','2.1502','208.7648','-1019.6232','-1002.7051','2.14','121.8968',5,70000000,6,0,0,5,0,'2023-07-22 00:21:30',2695,NULL,NULL,0),
	(18,NULL,'-991.4778','-1104.0614','2.1503','209.5118','-987.3697','-1108.4015','2.0692','116.5401',5,70000000,6,0,0,5,0,'2023-03-05 01:58:44',660,NULL,NULL,0),
	(19,NULL,'56.6947','-1922.5812','21.9114','148.3792','68.6552','-1921.9182','21.3022','328.1071',3,24000000,3,0,0,3,0,'2023-03-27 19:04:00',2596,NULL,NULL,0),
	(20,NULL,'104.0723','-1885.2677','24.3189','163.9103','105.8314','-1879.4481','23.9434','325.0186',3,24000000,3,0,0,3,0,'2023-03-09 14:01:12',1058,NULL,NULL,0),
	(21,NULL,'-1031.1862','-1108.8894','2.1586','300.9727','-1022.3387','-1101.7753','1.9311','216.0892',5,70000000,6,0,0,5,0,'2023-03-19 20:17:00',1955,NULL,NULL,0),
	(22,NULL,'114.968','-1887.748','23.9282','243.1204','128.0937','-1882.8384','23.5563','337.5752',3,24000000,3,0,0,3,0,'2023-03-02 19:48:45',516,NULL,NULL,0),
	(23,NULL,'-1040.2369','-1136.5552','2.1586','206.7121','-1044.324','-1140.356','2.1586','299.2773',5,70000000,6,0,0,5,0,'2023-03-09 14:40:34',1057,NULL,NULL,0),
	(24,NULL,'130.5751','-1853.4272','25.2348','330.2709','125.906','-1858.6503','24.7629','150.3047',3,24000000,3,0,0,3,0,'2023-03-05 19:33:49',709,NULL,NULL,0),
	(25,NULL,'-1073.3293','-1152.2698','2.1586','297.5138','-1075.9806','-1160.0791','2.1227','227.2603',5,70000000,6,0,0,5,0,'2023-03-09 22:07:21',1114,NULL,NULL,0),
	(26,NULL,'149.9001','-1864.9763','24.5913','330.8828','138.3655','-1869.1425','24.1505','148.5963',3,24000000,3,0,0,3,0,'2023-03-26 21:01:01',2485,NULL,NULL,0),
	(27,NULL,'128.2791','-1896.9188','23.6743','69.2374','139.9414','-1892.6436','23.4553','333.1904',3,24000000,3,0,0,3,0,'2023-03-12 20:19:45',1472,NULL,NULL,0),
	(28,NULL,'-951.9924','-1078.5471','2.1503','206.4493','-954.3337','-1082.9735','2.1503','214.4156',4,********,5,0,0,4,0,'2023-03-03 20:27:31',572,NULL,NULL,0),
	(29,NULL,'148.7709','-1904.2098','23.5316','153.8482','158.3931','-1898.1626','22.9788','337.3851',3,24000000,3,0,0,3,0,'2023-03-26 21:31:00',2487,NULL,NULL,0),
	(30,NULL,'-1031.6124','-902.3537','3.7456','28.8734','-1035.998','-901.393','4.1045','24.0859',4,60000000,5,0,0,4,0,'2023-03-05 18:57:39',702,NULL,NULL,0),
	(31,NULL,'171.2072','-1871.5337','24.4002','286.5889','161.021','-1871.7736','23.994','153.7979',3,24000000,3,0,0,3,0,'2023-03-12 19:11:01',1462,NULL,NULL,0),
	(32,NULL,'179.1555','-1924.3051','21.371','347.0651','173.6154','-1933.9276','21.0079','229.0952',3,24000000,3,0,0,3,0,'2023-03-07 19:45:04',887,NULL,NULL,0),
	(33,NULL,'165.3677','-1945.1526','20.2354','54.6199','165.1544','-1955.9294','19.27','239.2967',3,24000000,3,0,0,3,0,'2023-03-01 12:57:53',446,NULL,NULL,0),
	(34,NULL,'1301.2201','-573.8497','71.7323','345.3206','1295.6377','-567.9176','71.2338','347.315',6,*********,7,0,0,6,0,'2023-03-07 15:51:32',867,NULL,NULL,0),
	(35,NULL,'1303.0149','-527.7838','71.4608','161.3246','1307.776','-536.349','71.2453','163.4239',6,*********,7,0,0,6,0,'2023-03-04 17:01:01',625,NULL,NULL,0),
	(36,NULL,'39.9421','-1911.5074','21.9535','263.9573','47.657','-1913.7754','21.6524','320.047',3,24000000,3,0,0,3,0,'2023-03-05 18:01:00',704,NULL,NULL,0),
	(37,NULL,'23.8755','-1895.9709','22.8198','320.4272','32.5452','-1891.3693','22.2815','318.2853',3,24000000,3,0,0,3,0,'2023-02-28 20:01:00',448,NULL,NULL,0),
	(38,NULL,'-1024.6655','-1139.6099','2.7453','214.0829','-1022.9113','-1134.8339','2.1586','308.2296',4,40000000,5,0,0,4,0,'2023-03-13 20:53:30',1625,NULL,NULL,0),
	(39,NULL,'960.3709','-669.726','58.4498','300.0067','944.2744','-669.9854','58.0115','296.5893',5,70000000,6,0,0,5,0,'2023-03-25 17:36:59',2312,NULL,NULL,0),
	(40,NULL,'5.1242','-1883.7825','23.6974','293.491','17.841','-1880.2064','23.0787','318.7702',3,24000000,3,0,0,3,0,'2023-03-17 00:31:00',1814,NULL,NULL,0),
	(41,NULL,'980.0538','-627.5611','59.2358','37.1943','976.8289','-617.431','58.8397','126.7015',5,70000000,6,0,0,5,0,'2023-03-13 15:09:32',1580,NULL,NULL,0),
	(42,NULL,'29.4663','-1854.8469','24.0689','139.3086','21.3172','-1855.7842','23.666','140.0639',3,24000000,3,0,0,3,0,'2023-03-10 01:01:00',1138,NULL,NULL,0),
	(43,NULL,'-1034.8771','-1146.6821','2.1586','209.7786','-1033.3168','-1139.0802','2.061','300.6919',5,70000000,6,0,0,5,0,'2023-03-08 14:23:13',954,NULL,NULL,0),
	(44,NULL,'20.2228','-1844.6838','24.6016','89.2307','9.3628','-1845.7893','24.306','139.7845',3,24000000,3,0,0,3,0,'2023-03-24 20:44:01',2248,NULL,NULL,0),
	(45,NULL,'-4.8615','-1871.3643','24.151','16.3243','5.2941','-1872.671','23.6798','317.1531',3,24000000,3,0,0,3,0,'2023-07-17 15:21:50',NULL,NULL,NULL,0),
	(46,NULL,'-20.4753','-1858.5784','25.4088','322.9297','-21.7345','-1850.8411','25.1243','321.6078',3,24000000,3,0,0,3,0,'2023-03-07 20:10:45',895,NULL,NULL,0),
	(47,NULL,'903.1898','-615.6802','58.4533','230.9174','915.6451','-627.541','58.0489','315.2322',5,70000000,6,0,0,5,0,'2023-03-09 19:47:51',1097,NULL,NULL,0),
	(48,NULL,'-33.6048','-1847.3326','26.1936','287.2243','-26.093','-1849.0361','25.633','320.831',3,24000000,3,0,0,3,0,'2023-03-25 20:00:05',2332,NULL,NULL,0),
	(49,NULL,'919.8378','-569.8464','58.3664','206.0519','927.8863','-569.3062','57.9515','208.0653',5,70000000,6,0,0,5,0,'2023-03-06 20:52:54',808,NULL,NULL,0),
	(50,NULL,'906.3921','-489.7422','59.4362','202.1171','913.9554','-488.8559','59.0373','205.4896',5,70000000,6,0,0,5,0,'2023-03-04 17:42:34',626,NULL,NULL,0),
	(51,NULL,'-903.4279','-1005.5701','2.1503','215.8427','-891.1459','-1006.3107','2.1503','300.6359',5,70000000,6,0,0,5,0,'2023-03-11 02:28:09',1256,NULL,NULL,0),
	(52,NULL,'-42.3289','-1792.683','27.8282','138.3519','-50.4708','-1799.8115','27.0697','140.5602',3,24000000,3,0,0,3,0,'2023-03-10 03:39:00',1148,NULL,NULL,0),
	(53,NULL,'-900.4017','-982.1433','2.1631','122.1807','-896.4171','-985.2632','2.163','204.6773',5,70000000,6,0,0,5,0,'2023-03-10 19:54:32',1205,NULL,NULL,0),
	(54,NULL,'987.6021','-433.227','63.8941','214.2929','991.7338','-435.7924','63.7776','278.8598',5,70000000,6,0,0,5,0,'2023-03-11 18:57:09',1308,NULL,NULL,0),
	(55,NULL,'-908.3758','-976.5734','2.1503','20.6977','-907.6843','-968.728','2.1643','28.9726',5,70000000,6,0,0,5,0,'2023-03-13 16:38:40',1596,NULL,NULL,0),
	(56,NULL,'967.3543','-451.9412','62.7896','213.914','974.0131','-452.0376','62.4028','214.5548',5,70000000,6,0,0,5,0,'2023-03-08 14:23:14',955,NULL,NULL,0),
	(57,NULL,'-50.7462','-1783.6718','28.3007','134.1754','-61.062','-1788.4126','27.7703','139.9298',3,24000000,3,0,0,3,0,'2023-03-24 20:02:44',2249,NULL,NULL,0),
	(58,NULL,'944.2144','-463.2993','61.3992','122.7755','940.4809','-476.3038','61.0739','217.7923',5,70000000,6,0,0,5,0,'2023-03-08 17:58:08',987,NULL,NULL,0),
	(59,NULL,'-927.2094','-949.2139','2.7454','119.9563','-921.0709','-950.8322','2.1629','290.3298',4,40000000,5,0,0,4,0,'2023-03-01 21:11:00',486,NULL,NULL,0),
	(60,NULL,'921.9461','-478.1846','61.0836','204.9411','933.162','-480.1048','60.678','204.8065',5,70000000,6,0,0,5,0,'2023-03-12 18:02:52',1447,NULL,NULL,0),
	(61,NULL,'-934.6152','-939.1234','2.1453','124.0191','-924.894','-938.5521','2.2609','24.6931',5,70000000,6,0,0,5,0,'2023-03-23 18:35:26',2161,NULL,NULL,0),
	(62,NULL,'-142.2882','-1697.8727','30.7661','139.1458','-144.4607','-1700.7456','30.7661','138.4901',2,12000000,2,0,0,2,0,'2023-03-13 22:06:49',1662,NULL,NULL,0),
	(63,NULL,'850.5263','-532.6409','57.9255','266.3626','845.4687','-541.9337','57.3257','263.4',5,70000000,6,0,0,5,0,'2023-03-13 19:08:07',1616,NULL,NULL,0),
	(64,NULL,'-947.1422','-927.8777','2.1453','114.694','-935.0565','-919.1341','2.2526','212.0047',4,40000000,5,0,0,4,0,'2023-03-12 23:36:15',1495,NULL,NULL,0),
	(65,NULL,'-148.4376','-1687.8955','32.8724','136.1878','-144.6988','-1703.5109','30.6781','141.2361',2,12000000,2,0,0,2,0,'2023-03-27 14:08:47',2553,NULL,NULL,0),
	(66,NULL,'-147.3206','-1688.9093','32.8724','137.6743','-142.3407','-1703.4709','30.7515','141.5114',2,12000000,2,0,0,2,0,'2023-03-02 16:01:00',506,NULL,NULL,0),
	(67,NULL,'844.016','-562.8437','57.9926','191.0697','846.6251','-567.0192','57.7079','281.3755',5,70000000,6,0,0,5,0,'2023-03-07 23:27:22',912,NULL,NULL,0),
	(68,NULL,'-141.9402','-1693.3285','36.1672','46.487','-172.8868','-1690.8763','32.521','31.5834',2,12000000,2,0,0,2,0,'2023-03-26 14:01:00',2418,NULL,NULL,0),
	(69,NULL,'-948.0199','-910.3821','2.7432','119.2718','-942.3563','-911.7031','2.1634','302.7278',4,40000000,5,0,0,4,0,'2023-03-19 17:22:09',1945,NULL,NULL,0),
	(70,NULL,'-147.3112','-1688.9001','36.1674','48.6378','-172.7314','-1691.2057','32.5003','40.3717',2,12000000,2,0,0,2,0,'2023-03-19 06:01:00',1928,NULL,NULL,0),
	(71,NULL,'-148.6738','-1687.6938','36.1673','48.3289','-170.5383','-1693.6907','32.2385','40.6288',2,12000000,2,0,0,2,0,'2023-03-03 19:00:01',569,NULL,NULL,0),
	(72,NULL,'-950.9517','-905.6882','2.7455','118.3189','-949.9382','-900.0685','2.1631','299.451',4,40000000,5,0,0,4,0,'2023-03-14 20:01:00',1715,NULL,NULL,0),
	(73,NULL,'886.9648','-608.1042','58.4451','318.3133','873.386','-599.4219','58.206','314.7115',5,70000000,6,0,0,5,0,'2023-03-06 19:55:54',803,NULL,NULL,0),
	(74,NULL,'-158.0316','-1679.6837','36.9664','137.5808','-166.7908','-1696.9277','31.8046','50.5915',2,12000000,2,0,0,2,0,'2023-03-20 01:49:55',1972,NULL,NULL,0),
	(75,NULL,'-157.41','-1680.314','33.243','226.4633','-162.9161','-1699.8885','31.3699','50.432',2,12000000,2,0,0,2,0,'2023-03-26 11:47:41',2401,NULL,NULL,0),
	(76,NULL,'928.8768','-639.4834','58.2424','320.7945','915.349','-642.1692','57.8632','318.1186',5,70000000,6,0,0,5,0,'2023-03-05 21:59:30',719,NULL,NULL,0),
	(77,NULL,'-124.3608','-1670.8173','32.5644','46.9932','-140.583','-1640.5337','32.4867','320.5603',2,12000000,2,0,0,2,0,'2023-03-27 23:01:00',2654,NULL,NULL,0),
	(78,NULL,'1100.8629','-411.3454','67.5551','87.1092','1098.4015','-418.5659','67.1492','82.1861',5,70000000,6,0,0,5,0,'2023-03-13 21:30:25',1639,NULL,NULL,0),
	(79,NULL,'-131.5183','-1665.5115','32.5644','314.7647','-136.7916','-1635.8831','32.3873','320.7831',2,12000000,2,0,0,2,0,'2023-03-13 21:07:41',1641,NULL,NULL,0),
	(80,NULL,'-138.3581','-1659.0841','33.0539','233.5512','-133.7135','-1632.113','32.3293','320.9016',2,12000000,2,0,0,2,0,'2023-03-08 21:15:58',1015,NULL,NULL,0),
	(81,NULL,'-128.7325','-1647.5454','33.0997','225.8944','-154.0042','-1658.2015','32.8359','320.8782',2,12000000,2,0,0,2,0,'2023-03-08 14:23:14',956,NULL,NULL,0),
	(82,NULL,'-121.7115','-1653.8805','32.5643','140.8294','-151.3259','-1654.8785','32.7391','321.1176',2,12000000,2,0,0,2,0,'2023-03-17 19:00:01',1843,NULL,NULL,0),
	(83,NULL,'-114.7086','-1659.2036','32.5643','51.0358','-148.6621','-1651.5819','32.7211','320.772',2,12000000,2,0,0,2,0,'2023-03-01 22:01:00',489,NULL,NULL,0),
	(84,NULL,'-114.7647','-1659.3073','35.7143','49.9968','-150.5017','-1645.9518','32.9654','322.6541',2,12000000,2,0,0,2,0,'2023-03-17 17:01:00',1839,NULL,NULL,0),
	(85,NULL,'-121.6855','-1653.5525','35.7142','228.6126','-147.6143','-1642.1852','32.8919','322.7357',2,12000000,2,0,0,2,0,'2023-03-07 00:04:07',835,NULL,NULL,0),
	(86,NULL,'-128.6709','-1647.7063','36.5142','225.2344','-145.0255','-1638.7582','32.8088','322.7026',2,12000000,2,0,0,2,0,'2023-03-26 15:01:00',2426,NULL,NULL,0),
	(87,NULL,'-124.467','-1670.7806','35.7142','51.1443','-150.2464','-1649.0881','32.6315','325.2046',2,12000000,2,0,0,2,0,'2023-03-26 16:44:11',2440,NULL,NULL,0),
	(88,NULL,'-986.5051','-866.6891','2.3971','218.5782','-967.7697','-892.6041','2.1504','297.5049',5,70000000,6,0,0,5,0,'2023-03-07 20:30:02',894,NULL,NULL,0),
	(89,NULL,'-131.1503','-1665.0358','35.7142','226.5326','-147.4705','-1645.7915','32.5429','319.6349',2,12000000,2,0,0,2,0,'2023-03-06 18:01:00',793,NULL,NULL,0),
	(90,NULL,'-138.1529','-1659.2524','36.5142','229.2462','-143.3669','-1640.6376','32.4696','321.9753',2,12000000,2,0,0,2,0,'2023-03-20 15:25:07',1999,NULL,NULL,0),
	(91,NULL,'1251.1696','-515.5313','69.3491','259.3348','1244.0924','-522.0449','68.9709','254.8395',4,40000000,5,0,0,4,0,'2023-03-04 02:11:46',597,NULL,NULL,0),
	(92,NULL,'-89.9501','-1629.6124','31.5056','49.9589','-100.8716','-1595.4169','31.6455','317.5005',2,12000000,2,0,0,2,0,'2023-03-22 14:54:33',2090,NULL,NULL,0),
	(93,NULL,'-983.9443','-889.6932','2.1507','37.8375','-982.8189','-902.6968','2.1502','299.581',5,70000000,6,0,0,5,0,'2023-03-07 01:38:35',839,NULL,NULL,0),
	(94,NULL,'-84.3459','-1622.609','31.4772','46.4284','-104.898','-1599.7413','31.6925','311.8928',2,12000000,2,0,0,2,0,'2023-03-17 15:59:00',1829,NULL,NULL,0),
	(95,NULL,'1251.6582','-494.1757','69.9069','257.7682','1259.0968','-493.306','69.429','258.8165',4,40000000,5,0,0,4,0,'2023-03-13 10:41:30',1547,NULL,NULL,0),
	(96,NULL,'-80.4276','-1608.1439','31.4809','137.6015','-110.2994','-1605.88','31.7961','321.3768',2,12000000,2,0,0,2,0,'2023-03-09 22:00:00',1110,NULL,NULL,0),
	(97,NULL,'1259.8003','-480.0237','70.1888','305.0875','1275.9701','-475.7546','69.0926','261.5526',4,40000000,5,0,0,4,0,'2023-03-09 20:18:39',1102,NULL,NULL,0),
	(98,NULL,'-88.0691','-1602.0416','32.3119','147.4742','-113.3337','-1609.5521','31.8686','318.9642',2,12000000,2,0,0,2,0,'2023-03-13 21:11:01',1644,NULL,NULL,0),
	(99,NULL,'1266.0754','-458.0272','70.5169','273.9505','1265.2665','-442.8158','69.7074','286.9738',4,40000000,5,0,0,4,0,'2023-03-11 17:54:52',1302,NULL,NULL,0),
	(100,NULL,'-92.9501','-1607.6516','32.312','225.0742','-116.8584','-1613.6281','31.9653','318.9744',2,12000000,2,0,0,2,0,'2023-03-09 17:43:15',1085,NULL,NULL,0),
	(101,NULL,'-97.3647','-1612.7712','32.3123','227.2271','-124.2832','-1621.6189','32.1133','314.3302',2,12000000,2,0,0,2,0,'2023-03-05 17:38:27',697,NULL,NULL,0),
	(102,NULL,'-109.2772','-1628.8698','32.9076','231.007','-121.574','-1618.9705','32.0661','314.3657',2,12000000,2,0,0,2,0,'2023-03-09 15:01:00',1069,NULL,NULL,0),
	(103,NULL,'-105.245','-1632.2646','32.9069','321.6196','-118.855','-1616.3099','32.0212','314.3769',2,12000000,2,0,0,2,0,'2023-03-18 15:01:00',1885,NULL,NULL,0),
	(104,NULL,'1262.6602','-429.7372','70.0237','297.8442','1261.9683','-418.9819','69.3239','302.3687',4,40000000,5,0,0,4,0,'2023-03-12 18:42:58',1451,NULL,NULL,0),
	(105,NULL,'-97.4632','-1638.7212','32.1032','61.2615','-127.6597','-1626.3882','32.1692','319.7119',2,12000000,2,0,0,2,0,'2023-03-13 14:34:23',1571,NULL,NULL,0),
	(106,NULL,'1241.5074','-566.1822','69.6574','313.3071','1244.8351','-578.1862','69.3009','269.8551',4,40000000,5,0,0,4,0,'2023-03-12 16:19:44',1420,NULL,NULL,0),
	(107,NULL,'-997.7548','-904.5525','2.7463','214.8674','-990.4349','-905.9818','1.8398','297.6133',4,40000000,5,0,0,4,0,'2023-03-13 18:06:15',1607,NULL,NULL,0),
	(108,NULL,'1240.6503','-601.74','69.7828','273.0435','1244.9413','-586.2738','69.2449','274.4929',4,40000000,5,0,0,4,0,'2023-03-09 18:47:16',1088,NULL,NULL,0),
	(109,NULL,'1250.9254','-621.0698','69.5721','207.853','1256.4199','-623.7449','69.3867','296.4092',4,40000000,5,0,0,4,0,'2023-03-06 17:45:02',786,NULL,NULL,0),
	(110,NULL,'-109.3729','-1628.7324','36.289','229.5429','-121.5207','-1618.687','32.0574','316.5338',2,12000000,2,0,0,2,0,'2023-03-08 19:00:28',1001,NULL,NULL,0),
	(111,NULL,'-1022.7531','-896.6229','5.4167','211.8786','-1029.0264','-884.9956','6.1081','137.1432',4,40000000,5,0,0,4,0,'2023-03-25 18:24:24',2321,NULL,NULL,0),
	(112,NULL,'1265.6271','-648.5999','68.1206','29.493','1274.3246','-657.3949','67.7009','293.1889',4,40000000,5,0,0,4,0,'2023-03-07 19:36:22',885,NULL,NULL,0),
	(113,NULL,'-105.1463','-1632.3375','36.2891','318.0463','-118.934','-1615.9227','32.0084','317.4318',2,12000000,2,0,0,2,0,'2023-03-11 17:52:42',1304,NULL,NULL,0),
	(114,NULL,'1270.9139','-683.4235','66.0316','7.8088','1273.5446','-672.6988','65.89','279.0504',4,40000000,5,0,0,4,0,'2023-03-09 13:00:45',1054,NULL,NULL,0),
	(115,NULL,'1265.0026','-702.9789','64.7107','242.2585','1263.0758','-715.7524','64.5096','237.5412',4,40000000,5,0,0,4,0,'2023-03-09 18:59:21',1090,NULL,NULL,0),
	(116,NULL,'-97.3842','-1638.9167','35.4891','50.7723','-116.3606','-1613.1093','31.9534','317.4565',2,12000000,2,0,0,2,0,'2023-03-13 17:48:42',1603,NULL,NULL,0),
	(117,NULL,'1229.3162','-725.3845','60.798','96.822','1224.8009','-727.5608','60.5186','168.6363',4,40000000,5,0,0,4,0,'2023-03-03 11:08:31',541,NULL,NULL,0),
	(118,NULL,'-89.9471','-1629.7899','34.6892','316.2532','-113.7772','-1610.3413','31.8863','318.6698',2,12000000,2,0,0,2,0,'2023-03-09 15:49:42',1071,NULL,NULL,0),
	(119,NULL,'-84.1506','-1622.589','34.6892','315.7006','-109.8016','-1605.8373','31.8007','318.563',2,12000000,2,0,0,2,0,'2023-03-08 14:23:14',957,NULL,NULL,0),
	(120,NULL,'-1043.2672','-924.0018','3.1542','34.8307','-1053.2815','-905.5229','4.3349','31.9414',4,40000000,5,0,0,4,0,'2023-03-09 20:51:32',1104,NULL,NULL,0),
	(121,NULL,'1222.8844','-696.9201','60.8048','103.5711','1223.1478','-704.1805','60.7056','94.8702',4,40000000,5,0,0,4,0,'2023-03-09 20:13:21',1105,NULL,NULL,0),
	(122,NULL,'-80.4552','-1608.2076','34.6892','140.7888','-105.8493','-1601.2931','31.7234','319.2815',2,12000000,2,0,0,2,0,'2023-03-12 21:26:59',1480,NULL,NULL,0),
	(123,NULL,'1221.4619','-669.1152','63.4931','11.3965','1216.8778','-665.0511','62.8526','101.7589',4,40000000,5,0,0,4,0,'2023-03-12 00:14:03',1351,NULL,NULL,0),
	(124,NULL,'-88.2592','-1601.7572','35.4892','138.9861','-114.0859','-1604.9651','31.7709','321.4233',2,12000000,2,0,0,2,0,'2023-03-04 18:23:00',629,NULL,NULL,0),
	(125,NULL,'-1053.6791','-932.8621','3.3553','32.4283','-1071.5925','-909.2912','4.0673','29.9198',5,70000000,6,0,0,5,0,'2023-03-07 17:15:31',876,NULL,NULL,0),
	(126,NULL,'-93.2358','-1607.5955','35.4892','135.935','-110.3644','-1600.5156','31.7019','317.4808',2,12000000,2,0,0,2,0,'2023-03-17 08:00:01',1820,NULL,NULL,0),
	(127,NULL,'1207.1371','-620.2952','66.4386','94.3643','1200.2914','-613.1498','65.11','93.3502',4,40000000,5,0,0,4,0,'2023-03-23 18:10:28',2162,NULL,NULL,0),
	(128,NULL,'-97.3904','-1612.7646','35.4892','229.5444','-107.165','-1597.0273','31.5886','317.3114',2,12000000,2,0,0,2,0,'2023-03-07 21:01:01',902,NULL,NULL,0),
	(129,NULL,'1203.6226','-598.4901','68.0636','187.8151','1187.4805','-596.8417','63.8762','92.6995',4,40000000,5,0,0,4,0,'2023-03-12 01:08:15',1355,NULL,NULL,0),
	(130,NULL,'1200.8029','-575.7305','69.1391','135.0897','1185.677','-572.1866','64.2593','84.1218',4,40000000,5,0,0,4,0,'2023-03-23 21:11:41',2179,NULL,NULL,0),
	(131,NULL,'-123.458','-1590.9598','34.2078','49.9631','-155.7355','-1575.6831','34.6482','319.7062',2,12000000,2,0,0,2,0,'2023-03-09 23:56:11',1124,NULL,NULL,0),
	(132,NULL,'-119.1161','-1585.8352','34.213','47.1784','-157.6141','-1577.8947','34.6971','319.7383',2,12000000,2,0,0,2,0,'2023-03-09 16:01:00',1078,NULL,NULL,0),
	(133,NULL,'1204.496','-557.6772','69.6152','88.9206','1187.2609','-552.7089','64.5692','83.901',4,40000000,5,0,0,4,0,'2023-03-26 07:38:45',2391,NULL,NULL,0),
	(134,NULL,'-1075.3108','-939.3853','2.3641','310.6555','-1082.178','-919.5505','3.5162','28.9726',5,70000000,6,0,0,5,0,'2023-03-27 20:14:34',2615,NULL,NULL,0),
	(135,NULL,'-114.4234','-1579.7813','34.1771','133.2859','-161.27','-1582.5299','34.5991','319.4132',2,12000000,2,0,0,2,0,'2023-03-10 00:11:18',1132,NULL,NULL,0),
	(136,NULL,'-120.4382','-1575.0682','34.177','138.7853','-163.6335','-1585.2993','34.5335','319.6381',2,12000000,2,0,0,2,0,'2023-03-17 23:30:39',1862,NULL,NULL,0),
	(137,NULL,'997.0467','-729.5097','57.8157','311.7083','1007.0082','-731.1564','57.6477','305.5302',4,40000000,5,0,0,4,0,'2023-03-12 21:06:11',1481,NULL,NULL,0),
	(138,NULL,'-134.0595','-1580.6754','34.208','228.1612','-168.1739','-1590.3901','34.399','320.1716',2,12000000,2,0,0,2,0,'2023-03-25 13:40:15',2296,NULL,NULL,0),
	(139,NULL,'979.3708','-716.1243','58.2207','314.4303','981.1342','-710.0607','57.6479','311.2588',4,40000000,5,0,0,4,0,'2023-07-21 01:23:20',2686,NULL,NULL,0),
	(140,NULL,'-139.83','-1587.5508','34.2436','232.9046','-171.9153','-1595.4813','34.0856','321.9171',2,12000000,2,0,0,2,0,'2023-03-04 02:01:00',598,NULL,NULL,0),
	(141,NULL,'-1896.6708','642.4205','130.2091','135.9843','-1887.7003','626.3052','130.0002','134.5266',6,*********,7,0,0,6,0,'2023-03-06 19:38:39',805,NULL,NULL,0),
	(142,NULL,'970.9269','-701.2238','58.482','351.3844','972.9837','-685.4952','57.6511','301.5824',4,40000000,5,0,0,4,0,'2023-03-13 00:25:47',1500,NULL,NULL,0),
	(143,NULL,'-1090.8519','-926.1335','3.14','219.0174','-1097.8588','-942.2922','2.508','121.8035',5,70000000,6,0,0,5,0,'2023-03-03 23:17:19',585,NULL,NULL,0),
	(144,NULL,'-147.1518','-1596.5273','34.8313','244.3962','-145.3122','-1563.0251','34.4904','319.4927',2,12000000,2,0,0,2,0,'2023-03-19 23:47:00',1969,NULL,NULL,0),
	(145,NULL,'-140.2912','-1599.0747','34.8314','330.7219','-147.261','-1565.2791','34.536','320.1545',2,12000000,2,0,0,2,0,'2023-03-27 03:01:00',2519,NULL,NULL,0),
	(146,NULL,'943.3671','-653.4927','58.4287','217.4483','949.3033','-654.8498','58.0145','313.7453',4,40000000,5,0,0,4,0,'2023-03-25 11:32:08',2292,NULL,NULL,0),
	(147,NULL,'964.1224','-596.0174','59.9027','73.777','954.3714','-597.5352','59.3738','27.5118',4,40000000,5,0,0,4,0,'2023-03-12 17:33:21',1435,NULL,NULL,0),
	(148,NULL,'-140.3176','-1599.3763','38.2126','339.9452','-175.7347','-1602.359','33.8636','330.3998',2,12000000,2,0,0,2,0,'2023-03-12 11:58:15',1391,NULL,NULL,0),
	(149,NULL,'-147.3195','-1596.4386','38.2126','252.1736','-174.5709','-1599.7426','33.9812','326.9828',2,12000000,2,0,0,2,0,'2023-02-28 03:19:44',452,NULL,NULL,0),
	(150,NULL,'976.3979','-580.303','59.85','30.1569','982.5983','-573.1727','59.2693','32.9129',4,40000000,5,0,0,4,0,'2023-03-03 20:16:49',574,NULL,NULL,0),
	(151,NULL,'-1084.8074','-951.9262','2.3618','303.374','-1074.4498','-944.9846','2.246','31.9136',4,40000000,5,0,0,4,0,'2023-03-10 19:06:25',1207,NULL,NULL,0),
	(152,NULL,'-139.8047','-1587.8428','37.4078','235.1863','-139.9629','-1556.9832','34.2689','319.9401',2,12000000,2,0,0,2,0,'2023-03-10 00:01:00',1133,NULL,NULL,0),
	(153,NULL,'1010.0154','-572.4716','60.5944','263.734','1007.3239','-562.6497','60.1994','260.9386',4,40000000,5,0,0,4,0,'2023-03-27 18:09:00',2585,NULL,NULL,0),
	(154,NULL,'-133.5215','-1581.0183','37.4078','234.6172','-138.0584','-1554.7042','34.2055','320.5873',2,12000000,2,0,0,2,0,'2023-03-27 01:22:15',2511,NULL,NULL,0),
	(155,NULL,'-1973.9591','630.5648','122.5363','152.3146','-1971.519','622.0645','122.1059','249.3875',6,140000000,7,0,0,6,0,'2023-03-09 17:46:10',1086,NULL,NULL,0),
	(156,NULL,'999.9662','-594.0184','59.6386','262.2705','1007.3897','-590.0768','59.083','239.1945',4,40000000,5,0,0,4,0,'2023-03-07 03:36:05',843,NULL,NULL,0),
	(157,NULL,'-120.0933','-1574.535','37.4078','146.6543','-159.1187','-1579.8051','34.6682','319.5745',2,12000000,2,0,0,2,0,'2023-03-09 15:31:14',1072,NULL,NULL,0),
	(158,NULL,'-114.3614','-1579.6503','37.4078','131.3127','-157.3233','-1577.645','34.6895','321.8318',2,12000000,2,0,0,2,0,'2023-03-01 12:37:41',454,NULL,NULL,0),
	(159,NULL,'965.2347','-542.1299','59.7252','215.6514','957.0117','-550.2749','59.3787','210.9335',4,40000000,5,0,0,4,0,'2023-03-17 14:19:52',1826,NULL,NULL,0),
	(160,NULL,'-1929.3414','595.5038','122.2849','117.9316','-1941.8444','582.2574','118.8885','12.1476',6,*********,7,0,0,6,0,'2023-03-06 20:14:25',810,NULL,NULL,0),
	(161,NULL,'988.0684','-526.0836','60.6906','206.4693','981.3253','-531.9482','60.1182','210.9631',4,40000000,5,0,0,4,0,'2023-03-03 17:38:34',562,NULL,NULL,0),
	(162,NULL,'-118.9749','-1586.0112','37.4078','49.4805','-153.6633','-1573.0786','34.6512','316.0693',2,12000000,2,0,0,2,0,'2023-03-21 01:00:01',2032,NULL,NULL,0),
	(163,NULL,'1006.1956','-511.0823','60.834','116.5419','1003.069','-514.6964','60.6945','206.7112',4,40000000,5,0,0,4,0,'2023-03-10 10:37:45',1163,NULL,NULL,0),
	(164,NULL,'-123.4','-1590.9305','37.4078','51.6362','-152.2397','-1571.5917','34.6461','317.7942',2,12000000,2,0,0,2,0,'2023-03-12 02:40:05',1368,NULL,NULL,0),
	(165,NULL,'-1995.5327','590.9583','117.9034','270.2781','-1979.6024','597.3099','118.3434','197.1527',6,*********,7,0,0,6,0,'2023-03-13 03:07:51',1520,NULL,NULL,0),
	(166,NULL,'1046.1517','-498.0698','64.2759','346.6255','1054.3191','-489.0228','63.7781','253.3142',4,40000000,5,0,0,4,0,'2023-03-12 17:19:38',1437,NULL,NULL,0),
	(167,NULL,'-1122.0994','-1046.6571','2.1504','348.1984','-1120.9932','-1053.3323','2.1504','212.634',4,40000000,5,0,0,4,0,'2023-03-02 20:43:44',518,NULL,NULL,0),
	(168,NULL,'1051.3417','-470.5766','64.103','258.6787','1055.423','-483.0474','63.8515','256.7507',4,40000000,5,0,0,4,0,'2023-03-13 22:22:32',1660,NULL,NULL,0),
	(169,NULL,'-1114.3933','-1068.9495','2.1504','304.8832','-1117.5094','-1064.1294','2.0839','298.4116',5,70000000,6,0,0,5,0,'2023-03-11 21:56:07',1331,NULL,NULL,0),
	(170,NULL,'1056.193','-448.8963','66.2575','349.8491','1062.2065','-445.9099','65.8712','258.3083',4,40000000,5,0,0,4,0,'2023-03-21 18:23:53',2058,NULL,NULL,0),
	(171,NULL,'-161.5047','-1638.323','37.2459','309.5528','-178.2297','-1637.3246','33.1635','357.7476',2,12000000,2,0,0,2,0,'2023-03-08 19:46:48',1003,NULL,NULL,0),
	(172,NULL,'-1104.1716','-1059.9304','2.7368','203.0286','-1102.6262','-1054.4602','2.1151','299.3148',5,70000000,6,0,0,5,0,'2023-03-12 20:36:48',1469,NULL,NULL,0),
	(173,NULL,'-160.1266','-1636.3174','37.2459','135.2811','-178.3758','-1641.0839','33.1766','354.733',2,12000000,2,0,0,2,0,'2023-03-22 00:54:46',2069,NULL,NULL,0),
	(174,NULL,'1014.4208','-468.8991','64.4925','38.0859','1020.4066','-462.0524','63.9036','37.2697',4,40000000,5,0,0,4,0,'2023-03-13 15:53:48',1583,NULL,NULL,0),
	(175,NULL,'-160.0187','-1636.5389','34.0289','133.9052','-178.2836','-1654.1821','33.1633','357.3214',2,12000000,2,0,0,2,0,'2023-03-22 19:01:00',2102,NULL,NULL,0),
	(176,NULL,'-1108.3795','-1041.4851','2.1504','25.9363','-1104.3505','-1054.3461','2.0904','114.0284',4,40000000,5,0,0,4,0,'2023-03-11 23:15:09',1340,NULL,NULL,0),
	(177,NULL,'-161.0315','-1638.3984','34.0289','317.896','-178.2172','-1650.5443','33.1562','0.9395',2,12000000,2,0,0,2,0,'2023-03-12 15:39:03',1410,NULL,NULL,0),
	(178,NULL,'970.1419','-502.3255','62.1409','69.7039','962.0923','-503.2549','61.6161','28.6647',4,40000000,5,0,0,4,0,'2023-03-10 17:41:46',1191,NULL,NULL,0),
	(179,NULL,'-151.1008','-1622.6052','33.6507','230.5283','-178.3065','-1631.4236','33.1633','358.0313',2,12000000,2,0,0,2,0,'2023-03-06 13:36:13',766,NULL,NULL,0),
	(180,NULL,'-150.4364','-1625.3038','33.6568','44.3836','-178.2019','-1628.4829','33.1886','358.6078',2,12000000,2,0,0,2,0,'2023-03-11 05:01:00',1260,NULL,NULL,0),
	(181,NULL,'946.0265','-518.7576','60.6255','304.8596','947.7743','-512.0696','60.2176','31.7307',4,40000000,5,0,0,4,0,'2023-03-06 18:12:54',795,NULL,NULL,0),
	(182,NULL,'-145.5554','-1614.7726','36.0485','157.1427','-178.666','-1613.3329','33.4811','348.6432',2,12000000,2,0,0,2,0,'2023-03-20 19:23:31',2011,NULL,NULL,0),
	(183,NULL,'-145.2253','-1618.2654','36.0485','134.2432','-178.1588','-1625.4783','33.2222','0.0225',2,12000000,2,0,0,2,0,'2023-03-15 15:00:01',1745,NULL,NULL,0),
	(184,NULL,'924.146','-525.6006','59.789','28.2007','915.7144','-512.0983','58.4132','291.0672',4,40000000,5,0,0,4,0,'2023-03-05 14:52:43',679,NULL,NULL,0),
	(185,NULL,'-1874.2236','201.3724','84.2945','128.4942','-1880.0205','190.314','84.0065','122.8227',8,1********,8,0,0,8,0,'2023-03-10 01:32:02',1136,NULL,NULL,0),
	(186,NULL,'-152.3778','-1624.0793','36.8483','232.0668','-178.0659','-1610.4451','33.5431','348.8087',2,12000000,2,0,0,2,0,'2023-03-12 23:35:49',1497,NULL,NULL,0),
	(187,NULL,'-150.56','-1625.5549','36.8483','46.9258','-177.4774','-1607.5491','33.6517','347.0938',2,12000000,2,0,0,2,0,'2023-03-23 13:06:00',2137,NULL,NULL,0),
	(188,NULL,'892.8539','-540.6891','58.5066','110.651','889.434','-552.3759','58.046','114.8847',4,40000000,5,0,0,4,0,'2023-03-13 23:07:48',1670,NULL,NULL,0),
	(189,NULL,'1010.5056','-423.3053','65.351','309.341','1014.6636','-422.6634','64.9936','216.9165',4,40000000,5,0,0,4,0,'2023-03-06 17:54:04',787,NULL,NULL,0),
	(190,NULL,'1028.8646','-408.5177','66.1497','219.371','1023.7728','-420.4489','65.6887','-145.6913',4,40000000,5,0,0,4,0,'2023-03-13 20:25:30',1628,NULL,NULL,0),
	(191,NULL,'-986.2771','-1121.943','4.5455','307.0023','-980.1684','-1112.9128','2.1503','39.1003',4,40000000,5,0,0,4,0,'2023-03-11 23:22:40',1344,NULL,NULL,0),
	(192,NULL,'1099.476','-438.4126','67.7906','356.2547','1099.6909','-429.4959','67.3921','78.8626',4,40000000,5,0,0,4,0,'2023-03-11 23:12:22',1345,NULL,NULL,0),
	(193,NULL,'1098.6439','-464.683','67.3194','152.3525','1089.9124','-471.1474','65.6858','76.8589',4,40000000,5,0,0,4,0,'2023-03-12 18:02:05',1454,NULL,NULL,0),
	(194,NULL,'-978.3635','-1107.9327','2.1503','212.7828','-974.4001','-1103.9805','2.1426','119.2924',4,40000000,5,0,0,4,0,'2023-02-26 20:44:00',560,NULL,NULL,0),
	(195,NULL,'-1923.2826','298.2639','89.287','102.2982','-1923.262','281.3324','89.0732','92.5907',6,120000000,7,0,0,6,0,'2023-03-06 20:40:53',812,NULL,NULL,0),
	(196,NULL,'1090.1803','-484.3709','65.6604','81.5765','1084.5889','-494.2799','64.1026','71.5756',4,40000000,5,0,0,4,0,'2023-03-09 18:23:45',1092,NULL,NULL,0),
	(197,NULL,'-216.636','-1673.863','34.4633','349.7795','-188.5082','-1677.9486','33.4702','189.5945',2,12000000,2,0,0,2,0,'2023-03-11 20:01:00',1325,NULL,NULL,0),
	(198,NULL,'-224.2986','-1673.9979','34.4633','352.3703','-188.5932','-1675.5493','33.4792','181.9616',2,12000000,2,0,0,2,0,'2023-03-20 19:11:01',2012,NULL,NULL,0),
	(199,NULL,'-224.3835','-1666.2434','34.4632','285.7612','-188.6253','-1673.3187','33.4945','187.588',2,12000000,2,0,0,2,0,'2023-03-27 13:00:01',2547,NULL,NULL,0),
	(200,NULL,'1114.1235','-391.1917','68.9482','61.0985','1104.1772','-398.6758','67.7651','75.7028',4,40000000,5,0,0,4,0,'2023-03-12 08:38:53',1385,NULL,NULL,0),
	(201,NULL,'-212.6884','-1667.968','34.4632','84.6096','-188.2679','-1653.8572','33.487','178.5491',2,12000000,2,0,0,2,0,'2023-03-03 16:11:01',553,NULL,NULL,0),
	(202,NULL,'-982.5734','-1083.546','2.5452','124.0797','-977.9745','-1102.9695','2.0698','122.4449',4,40000000,5,0,0,4,0,'2023-03-13 19:34:53',1617,NULL,NULL,0),
	(203,NULL,'-212.7262','-1660.6564','34.4632','83.8291','-188.1574','-1659.1782','33.4919','177.0749',2,12000000,2,0,0,2,0,'2023-02-28 01:11:00',456,NULL,NULL,0),
	(204,NULL,'1060.7302','-378.4889','68.2312','228.0563','1055.6201','-385.9508','67.8535','219.1667',4,40000000,5,0,0,4,0,'2023-03-08 00:57:40',915,NULL,NULL,0),
	(205,NULL,'-216.8252','-1649.481','34.4631','182.8875','-188.34','-1665.0997','33.4928','177.9097',2,12000000,2,0,0,2,0,'2023-03-13 17:01:00',1604,NULL,NULL,0),
	(206,NULL,'-224.6568','-1648.896','35.0738','259.39','-188.5766','-1671.2151','33.4211','182.2528',2,12000000,2,0,0,2,0,'2023-03-10 17:01:01',1193,NULL,NULL,0),
	(207,NULL,'-1906.6849','252.2846','86.2511','112.4467','-1902.2421','238.6993','86.2511','29.3449',6,120000000,7,0,0,6,0,'2023-03-10 17:20:59',1189,NULL,NULL,0),
	(208,NULL,'952.5356','-252.3122','67.9646','60.9483','946.3075','-253.7267','67.5338','148.3908',4,40000000,5,0,0,4,0,'2023-03-02 17:27:34',511,NULL,NULL,0),
	(209,NULL,'920.965','-238.3118','70.1781','151.349','910.2189','-258.477','68.8847','354.1123',4,40000000,5,0,0,4,0,'2023-03-08 02:18:41',926,NULL,NULL,0),
	(210,NULL,'-212.7394','-1668.0759','37.6369','355.242','-188.447','-1643.8446','33.4752','178.7422',2,12000000,2,0,0,2,0,'2023-03-27 17:00:01',2577,NULL,NULL,0),
	(211,NULL,'-982.9273','-1066.5642','2.1503','215.3344','-964.6437','-1092.3448','2.1503','119.0173',4,40000000,5,0,0,4,0,'2023-03-08 14:23:14',958,NULL,NULL,0),
	(212,NULL,'-212.6058','-1660.7518','37.6369','354.8315','-188.6364','-1651.6139','33.4783','181.8371',2,12000000,2,0,0,2,0,'2023-03-11 18:11:00',1309,NULL,NULL,0),
	(213,NULL,'880.0966','-205.4165','71.9764','153.5506','870.1542','-205.9992','70.7576','149.7041',4,40000000,5,0,0,4,0,'2023-03-02 04:27:18',499,NULL,NULL,0),
	(214,NULL,'-1931.531','362.5318','93.785','86.2073','-1937.7357','359.8768','93.5817','159.8512',6,130000000,7,0,0,6,0,'2023-03-23 17:28:38',2157,NULL,NULL,0),
	(215,NULL,'-216.5389','-1648.9915','37.6369','87.1715','-188.4096','-1656.0385','33.4855','181.095',2,12000000,2,0,0,2,0,'2023-03-05 23:19:25',732,NULL,NULL,0),
	(216,NULL,'-960.0506','-1109.4828','2.1503','208.358','-966.1313','-1096.0161','2.0749','297.1445',4,40000000,5,0,0,4,0,'2023-03-10 19:58:15',1209,NULL,NULL,0),
	(217,NULL,'-224.0305','-1648.8889','38.445','175.4611','-188.6207','-1647.4835','33.4787','182.0858',2,12000000,2,0,0,2,0,'2023-03-23 14:26:14',2143,NULL,NULL,0),
	(218,NULL,'-1995.3107','300.5135','91.9646','197.4657','-1994.8262','294.9405','91.7649','258.3781',6,120000000,7,0,0,6,0,'2023-07-21 23:03:57',2694,NULL,NULL,0),
	(219,NULL,'-224.4549','-1653.9912','37.6368','177.2222','-188.5612','-1649.7488','33.4776','182.0495',2,12000000,2,0,0,2,0,'2023-03-18 07:03:13',1873,NULL,NULL,0),
	(220,NULL,'-938.9641','-1088.2172','2.1503','316.2053','-934.7432','-1080.0948','2.1264','121.521',4,40000000,5,0,0,4,0,'2023-03-13 18:39:26',1610,NULL,NULL,0),
	(221,NULL,'-942.952','-1076.0013','2.746','25.6771','-937.4942','-1076.5865','2.1503','119.2728',4,40000000,5,0,0,4,0,'2023-03-18 19:00:42',1902,NULL,NULL,0),
	(222,NULL,'-2008.5015','367.4568','94.8143','273.2598','-1995.4697','376.5396','94.4835','273.1282',6,*********,7,0,0,6,0,'2023-03-10 20:34:45',1212,NULL,NULL,0),
	(223,NULL,'-224.2453','-1666.1887','37.6368','260.8617','-187.9924','-1661.9489','33.4972','180.8523',2,12000000,2,0,0,2,0,'2023-03-10 15:17:09',1180,NULL,NULL,0),
	(224,NULL,'-224.2883','-1673.9856','37.6367','355.3079','-188.5009','-1667.087','33.4879','213.1147',2,12000000,2,0,0,2,0,'2023-03-26 23:50:48',2498,NULL,NULL,0),
	(225,NULL,'-884.4226','-1072.0806','2.1629','209.5204','-875.0212','-1072.7969','2.1579','26.8548',5,70000000,6,0,0,5,0,'2023-03-06 18:48:30',791,NULL,NULL,0),
	(226,NULL,'-1940.5404','387.1802','96.5071','179.2581','-1943.0911','385.4055','96.5072','93.6423',6,1********,7,0,0,6,0,'2023-03-10 11:07:32',1164,NULL,NULL,0),
	(227,NULL,'-864.111','-1101.1191','6.4456','328.4328','-864.2051','-1090.3193','2.1629','298.5025',5,70000000,6,0,0,5,0,'2023-03-12 05:37:39',1380,NULL,NULL,0),
	(228,NULL,'840.6862','-182.1505','74.388','60.2099','843.665','-191.1752','72.6674','150.5869',5,70000000,6,0,0,5,0,'2023-03-09 18:29:05',1089,NULL,NULL,0),
	(229,NULL,'930.7275','-245.2144','69.0027','240.3773','938.6992','-251.8429','68.2499','149.4385',5,70000000,6,0,0,5,0,'2023-03-07 16:42:56',871,NULL,NULL,0),
	(230,NULL,'-2010.7251','445.1838','103.016','281.0795','-2008.4952','455.2644','102.6479','287.7013',6,140000000,7,0,0,6,0,'2023-03-09 01:54:32',1028,NULL,NULL,0),
	(231,NULL,'-1943.0714','449.5715','102.928','99.0615','-1944.7922','462.0122','102.0639','93.449',8,170000000,8,0,0,8,0,'2023-03-27 17:43:22',2579,NULL,NULL,0),
	(232,NULL,'-2972.5894','599.1134','24.4377','65.2422','-2981.5496','612.5305','20.1563','102.9833',5,70000000,6,0,0,5,0,'2023-03-10 17:49:46',1195,NULL,NULL,0),
	(233,NULL,'-2014.4069','499.736','107.1717','253.8161','-2010.6342','484.3473','106.8726','245.7442',6,*********,7,0,0,6,0,'2023-03-13 03:37:52',1522,NULL,NULL,0),
	(234,NULL,'-2972.637','642.529','25.9921','102.0798','-2982.5342','654.3382','25.0396','101.5467',5,70000000,6,0,0,5,0,'2023-03-12 01:33:35',1358,NULL,NULL,0),
	(235,NULL,'-2994.5337','682.5778','25.0415','107.8431','-2999.4421','689.4464','25.2028','109.3297',5,70000000,6,0,0,5,0,'2023-03-09 02:16:24',1038,NULL,NULL,0),
	(236,NULL,'-1938.0004','551.3197','114.8285','69.2093','-1939.9111','522.0144','109.237','164.4523',6,*********,7,0,0,6,0,'2023-07-18 16:27:35',2676,NULL,NULL,0),
	(237,NULL,'-1046.1072','-1159.5641','2.1586','213.0107','-1052.5452','-1147.415','2.081','296.1341',4,40000000,5,0,0,4,0,'2023-03-20 19:34:10',NULL,NULL,NULL,0),
	(238,NULL,'-1063.6793','-1160.0521','2.5533','220.9566','-1061.5009','-1154.4403','2.1109','303.037',5,70000000,6,0,0,5,0,'2023-03-11 15:44:52',1282,NULL,NULL,0),
	(239,NULL,'-1068.1991','-1163.1373','2.7454','121.3413','-1073.0808','-1162.1425','2.1393','304.4489',4,40000000,5,0,0,4,0,'2023-03-09 22:55:10',1116,NULL,NULL,0),
	(240,NULL,'-2992.8362','707.4138','28.5064','203.0209','-2993.3079','705.457','28.4963','109.796',5,70000000,6,0,0,5,0,'2023-03-07 22:25:41',907,NULL,NULL,0),
	(241,NULL,'-1960.8716','212.0966','86.8029','294.484','-1953.7959','212.8545','85.9621','-153.2773',6,*********,7,0,0,6,0,'2023-07-21 00:36:33',2683,NULL,NULL,0),
	(242,NULL,'-3017.4229','746.8547','27.7784','111.2884','-3016.9226','740.5969','27.5876','105.4841',5,70000000,6,0,0,5,0,'2023-03-05 17:18:30',698,NULL,NULL,0),
	(243,NULL,'-1931.588','162.8192','84.6527','321.0513','-1935.839','182.9631','84.6137','305.5367',6,*********,7,0,0,6,0,'2023-03-12 18:33:47',1446,NULL,NULL,0),
	(244,NULL,'-3101.561','743.8139','21.2848','227.1302','-3095.7954','744.9185','20.9827','315.4494',4,40000000,5,0,0,4,0,'2023-03-26 23:06:19',2500,NULL,NULL,0),
	(245,NULL,'-1114.0746','-1193.4863','2.3567','217.2974','-1119.5544','-1209.6007','2.4882','123.9898',3,24000000,3,0,0,3,0,'2023-03-11 19:08:09',1320,NULL,NULL,0),
	(246,NULL,'-3107.4082','718.9597','20.6411','283.3373','-3101.1445','716.1567','20.4499','286.8258',4,40000000,5,0,0,4,0,'2023-03-13 01:22:03',1511,NULL,NULL,0),
	(247,NULL,'-1113.4484','-1195.5391','6.6834','207.7278','-1128.8607','-1210.517','2.3954','21.6864',3,24000000,3,0,0,3,0,'2023-03-25 00:59:43',2268,NULL,NULL,0),
	(248,NULL,'-1898.5762','132.7117','81.9847','307.1946','-1887.8605','122.4961','81.6946','306.0521',6,120000000,7,0,0,6,0,'2023-03-10 05:54:24',1153,NULL,NULL,0),
	(249,NULL,'-1111.4369','-1194.3821','6.6834','205.242','-1117.4207','-1213.168','2.5044','115.7983',3,24000000,3,0,0,3,0,'2023-03-16 02:11:00',1772,NULL,NULL,0),
	(250,NULL,'-3077.8884','658.9224','11.6677','309.6596','-3068.9478','659.1933','10.5869','305.2334',4,40000000,5,0,0,4,0,'2023-03-03 22:57:36',579,NULL,NULL,0),
	(251,NULL,'-3029.679','568.7326','7.8236','282.1086','-3026.571','569.9184','7.5938','4.1621',4,40000000,5,0,0,4,0,'2023-03-01 15:34:45',458,NULL,NULL,0),
	(252,NULL,'-3036.7915','559.1981','7.5077','274.9749','-3033.1709','555.7678','7.5077','276.0128',4,40000000,5,0,0,4,0,'2023-03-27 20:21:48',2616,NULL,NULL,0),
	(253,NULL,'-1119.3732','-1198.5302','6.6836','126.2963','-1127.2307','-1208.9425','2.4605','41.1353',3,24000000,3,0,0,3,0,'2023-03-06 00:00:01',738,NULL,NULL,0),
	(254,NULL,'-216.4415','-1674.0072','37.6367','266.9176','-188.5266','-1667.8767','33.4865','178.9979',2,12000000,2,0,0,2,0,'2023-03-23 23:45:11',2189,NULL,NULL,0),
	(255,NULL,'-1126.0912','-1171.9667','2.3576','286.0549','-1140.6315','-1178.5778','2.4088','204.8163',4,40000000,5,0,0,4,0,'2023-03-12 22:30:48',1487,NULL,NULL,0),
	(256,NULL,'-3036.647','544.8748','7.5077','276.2467','-3032.4146','548.6179','7.5077','274.816',4,40000000,5,0,0,4,0,'2023-03-13 23:25:44',1671,NULL,NULL,0),
	(257,NULL,'-222.6388','-1617.5449','34.8692','272.3876','-186.9138','-1602.1462','34.1059','163.1281',2,12000000,2,0,0,2,0,'2023-03-06 21:12:20',819,NULL,NULL,0),
	(258,NULL,'-1128.9012','-1162.502','6.495','122.2469','-1143.3722','-1164.373','2.7022','228.1704',3,24000000,3,0,0,3,0,'2023-03-13 03:21:00',1521,NULL,NULL,0),
	(259,NULL,'-212.6631','-1617.559','34.8693','2.0473','-186.2083','-1600.2789','34.1674','163.3687',2,12000000,2,0,0,2,0,'2023-03-04 03:11:00',604,NULL,NULL,0),
	(260,NULL,'-3031.6267','524.8612','7.4184','265.0164','-3029.4646','520.2408','7.3458','265.3359',4,40000000,5,0,0,4,0,'2023-03-08 15:11:28',974,NULL,NULL,0),
	(261,NULL,'-210.6305','-1606.8314','34.8693','75.9478','-185.1602','-1597.5392','34.2718','157.9127',2,12000000,2,0,0,2,0,'2023-03-27 18:24:46',2586,NULL,NULL,0),
	(262,NULL,'-1135.8441','-1153.4009','2.7453','296.3188','-1149.1185','-1158.6743','2.711','204.309',4,40000000,5,0,0,4,0,'2023-03-11 23:56:38',1346,NULL,NULL,0),
	(263,NULL,'-3039.1003','492.7247','6.7727','260.6179','-3031.3831','498.0293','6.8103','272.6134',4,40000000,5,0,0,4,0,'2023-03-09 22:47:15',1117,NULL,NULL,0),
	(264,NULL,'-222.5592','-1601.1836','34.8755','264.2951','-187.9351','-1606.5439','33.9614','171.0474',2,12000000,2,0,0,2,0,'2023-03-18 03:48:19',1870,NULL,NULL,0),
	(265,NULL,'-1142.7609','-1144.483','2.8442','296.9323','-1163.1191','-1137.7677','2.7656','200.6481',4,40000000,5,0,0,4,0,'2023-03-06 16:10:48',778,NULL,NULL,0),
	(266,NULL,'-222.7926','-1585.8132','34.8693','269.5929','-188.4785','-1610.2782','33.831','171.1871',2,12000000,2,0,0,2,0,'2023-03-12 01:01:00',1357,NULL,NULL,0),
	(267,NULL,'-219.0043','-1580.2444','34.8693','222.0047','-188.5022','-1612.6989','33.7592','178.3087',2,12000000,2,0,0,2,0,'2023-03-27 07:31:22',2533,NULL,NULL,0),
	(268,NULL,'-215.8855','-1576.6071','34.8693','141.7043','-188.5815','-1615.6464','33.6905','178.3297',2,12000000,2,0,0,2,0,'2023-03-13 11:20:28',1551,NULL,NULL,0),
	(269,NULL,'-206.3391','-1585.535','34.8693','146.7229','-189.0847','-1619.3745','33.6133','179.1917',2,10000000,2,0,0,2,0,'2023-03-26 06:36:42',2388,NULL,NULL,0),
	(270,NULL,'-1145.6108','-1127.5614','6.5031','123.1361','-1161.3746','-1136.9236','2.567','21.181',4,40000000,5,0,0,4,0,'2023-03-04 07:46:49',608,NULL,NULL,0),
	(271,NULL,'-209.3915','-1600.3669','34.8693','74.3547','-189.0553','-1623.9613','33.4472','179.6638',2,12000000,2,0,0,2,0,'2023-03-08 14:23:14',959,NULL,NULL,0),
	(272,NULL,'-206.3096','-1585.632','38.0545','153.9031','-186.5809','-1603.1469','34.092','165.5195',2,12000000,2,0,0,2,0,'2023-03-15 21:45:42',1761,NULL,NULL,0),
	(273,NULL,'-216.0563','-1576.9753','38.0545','130.7613','-187.4465','-1605.5453','34.0007','162.2871',2,12000000,2,0,0,2,0,'2023-03-25 16:04:52',2308,NULL,NULL,0),
	(274,NULL,'-219.0794','-1580.3528','38.0545','141.9939','-188.0367','-1607.3802','33.9332','161.9938',2,12000000,2,0,0,2,0,'2023-03-26 08:01:00',2395,NULL,NULL,0),
	(275,NULL,'-222.3834','-1585.9266','38.0545','176.8007','-188.3555','-1610.0469','33.8421','182.0986',2,12000000,2,0,0,2,0,'2023-03-05 21:01:04',721,NULL,NULL,0),
	(276,NULL,'-222.4832','-1600.8993','38.0545','271.5864','-188.6225','-1613.0063','33.749','177.8738',2,12000000,2,0,0,2,0,'2023-03-25 11:07:34',2293,NULL,NULL,0),
	(277,NULL,'-1667.1943','-441.4268','40.3557','228.0196','-1668.0028','-453.8558','39.1458','234.1266',4,30000000,4,0,0,4,0,'2023-03-15 17:53:34',1750,NULL,NULL,0),
	(278,NULL,'-167.907','-1534.8154','35.0997','109.9067','-153.653','-1556.7738','34.9074','319.8398',2,12000000,2,0,0,2,0,'2023-03-12 19:05:11',1463,NULL,NULL,0),
	(279,NULL,'-3093.4048','349.1912','7.5385','255.8512','-3088.281','340.6702','7.3947','252.9476',5,70000000,6,0,0,5,0,'2023-03-10 23:48:23',1239,NULL,NULL,0),
	(280,NULL,'-174.763','-1528.9661','34.3539','137.0881','-156.0277','-1559.6379','35.0085','315.6699',2,12000000,2,0,0,2,0,'2023-03-19 18:14:58',1949,NULL,NULL,0),
	(281,NULL,'-179.5931','-1534.6643','34.3539','137.9986','-163.108','-1567.7592','35.087','317.3635',2,12000000,2,0,0,2,0,'2023-03-12 16:17:41',1423,NULL,NULL,0),
	(282,NULL,'-1041.6974','-1025.7964','2.7476','215.3203','-1040.0819','-1019.1033','2.1089','27.2471',4,40000000,5,0,0,4,0,'2023-03-13 16:49:52',1597,NULL,NULL,0),
	(283,NULL,'-184.1071','-1539.7728','34.3578','138.2254','-164.704','-1569.8718','35.0942','325.3368',2,12000000,2,0,0,2,0,'2023-03-08 02:01:00',928,NULL,NULL,0),
	(284,NULL,'-1642.2972','-412.4517','42.0742','226.0109','-1632.9564','-419.6428','39.7007','234.1886',4,40000000,5,0,0,4,0,'2023-03-05 16:31:29',689,NULL,NULL,0),
	(285,NULL,'-3110.5457','335.3713','7.4933','346.2256','-3090.4507','321.5652','7.5321','243.8219',4,40000000,5,0,0,4,0,'2023-03-11 19:49:53',1318,NULL,NULL,0),
	(286,NULL,'-173.9188','-1547.1488','35.1273','49.9562','-151.9309','-1554.601','34.7813','317.0777',2,12000000,2,0,0,2,0,'2023-03-03 16:38:09',554,NULL,NULL,0),
	(287,NULL,'-1054.2075','-1000.272','6.4105','299.1542','-1040.4326','-1005.6758','2.1502','211.1621',3,24000000,3,0,0,3,0,'2023-03-23 06:01:00',2128,NULL,NULL,0),
	(288,NULL,'-3115.7495','294.4087','8.9721','346.4982','-3101.0056','289.3408','8.9658','248.507',4,40000000,5,0,0,4,0,'2023-03-13 00:00:17',1503,NULL,NULL,0),
	(289,NULL,'-1055.3335','-998.5372','6.4105','315.2338','-1037.0364','-1003.7653','2.1502','205.2766',3,24000000,3,0,0,3,0,'2023-03-17 06:57:32',1818,NULL,NULL,0),
	(290,NULL,'-179.7635','-1553.8417','35.1263','62.3681','-149.4247','-1551.8961','34.5951','317.0652',2,12000000,2,0,0,2,0,'2023-03-26 05:01:00',2385,NULL,NULL,0),
	(291,NULL,'-1051.3635','-1006.3163','6.4105','224.3325','-1041.7338','-1014.8128','2.1311','118.9907',3,24000000,3,0,0,3,0,'2023-03-27 02:28:17',2515,NULL,NULL,0),
	(292,NULL,'-187.3043','-1562.9165','35.7553','25.3814','-171.5086','-1578.4241','34.9229','321.9053',2,12000000,2,0,0,2,0,'2023-03-13 17:59:00',1606,NULL,NULL,0),
	(293,NULL,'-1622.3597','-380.0007','43.7157','233.2807','-1609.0554','-382.4759','43.182','230.7338',5,70000000,6,0,0,5,0,'2023-03-06 20:26:11',809,NULL,NULL,0),
	(294,NULL,'-3105.283','246.6414','12.496','291.4431','-3100.5012','244.0251','12.3816','284.8669',4,40000000,5,0,0,4,0,'2023-03-07 23:41:45',913,NULL,NULL,0),
	(295,NULL,'-191.6162','-1559.2428','34.9544','336.7346','-169.6398','-1576.1819','35.0794','320.98',2,12000000,2,0,0,2,0,'2023-03-16 18:01:00',1795,NULL,NULL,0),
	(296,NULL,'-195.657','-1556.0166','34.9555','237.1243','-167.0406','-1572.7928','35.149','322.0204',2,12000000,2,0,0,2,0,'2023-02-28 17:01:00',459,NULL,NULL,0),
	(297,NULL,'-195.9278','-1555.8993','38.3349','231.3653','-210.9534','-1547.9722','33.967','328.3771',2,12000000,2,0,0,2,0,'2023-03-26 11:48:51',2402,NULL,NULL,0),
	(298,NULL,'-1596.7203','-352.6822','45.9764','225.5661','-1603.9518','-364.0579','45.6347','233.1396',5,70000000,6,0,0,5,0,'2023-03-11 17:44:53',1300,NULL,NULL,0),
	(299,NULL,'-3089.2156','221.2726','14.1197','320.2252','-3081.9636','222.5051','14.0123','319.4089',5,70000000,6,0,0,5,0,'2023-03-08 18:59:52',990,NULL,NULL,0),
	(300,NULL,'-191.6335','-1559.3451','38.3349','229.081','-209.8374','-1546.4336','33.9243','315.7496',2,12000000,2,0,0,2,0,'2023-03-02 03:11:00',496,NULL,NULL,0),
	(301,NULL,'-1027.6991','-969.2007','2.5178','206.7635','-1008.1144','-994.0574','2.1503','127.6731',4,40000000,5,0,0,4,0,'2023-03-13 18:21:47',1609,NULL,NULL,0),
	(302,NULL,'-3088.5205','392.1202','11.4484','251.463','-3074.1101','392.2552','6.9685','252.7507',4,40000000,5,0,0,4,0,'2023-03-05 15:22:02',683,NULL,NULL,0),
	(303,NULL,'-1018.6863','-963.9681','2.3447','216.8849','-1001.0295','-990.0522','2.1503','119.4567',4,40000000,5,0,0,4,0,'2023-03-06 17:23:34',789,NULL,NULL,0),
	(304,NULL,'-187.5432','-1562.7533','39.1314','327.9485','-205.8056','-1541.9871','33.8537','317.9164',2,12000000,2,0,0,2,0,'2023-03-15 23:01:00',1766,NULL,NULL,0),
	(305,NULL,'-179.7158','-1553.5667','38.331','316.476','-200.6396','-1537.8323','33.8757','318.5541',2,12000000,2,0,0,2,0,'2023-03-09 14:28:59',1060,NULL,NULL,0),
	(306,NULL,'-174.0326','-1546.959','38.3343','319.5291','-196.5184','-1532.7472','33.7606','320.8594',2,12000000,2,0,0,2,0,'2023-03-12 13:13:09',1400,NULL,NULL,0),
	(307,NULL,'-978.5165','-990.7141','4.5453','126.7881','-985.092','-985.1624','1.9933','118.1445',4,40000000,5,0,0,4,0,'2023-03-16 17:55:34',1791,NULL,NULL,0),
	(308,NULL,'-3108.6372','303.7279','8.381','163.6874','-3096.8665','301.4583','8.3702','247.4347',3,24000000,3,0,0,3,0,'2023-03-21 22:15:17',2066,NULL,NULL,0),
	(309,NULL,'-3106.0984','312.0107','8.381','343.0725','-3097.2576','306.7125','8.3442','249.8503',3,24000000,3,0,0,3,0,'2023-03-16 04:01:00',1775,NULL,NULL,0),
	(310,NULL,'-995.4106','-967.1844','2.5454','122.4614','-991.4839','-987.8939','2.0572','118.75',4,40000000,5,0,0,4,0,'2023-03-07 20:13:22',897,NULL,NULL,0),
	(311,NULL,'-119.939','-1478.3029','33.8227','314.8309','-129.4952','-1501.8757','33.9836','47.3362',2,12000000,2,0,0,2,0,'2023-03-26 15:58:49',2428,NULL,NULL,0),
	(312,NULL,'-125.5217','-1473.4883','33.8227','317.3885','-132.8028','-1499.3954','33.974','47.404',2,12000000,2,0,0,2,0,'2023-03-18 17:29:02',1896,NULL,NULL,0),
	(313,NULL,'-3071.1897','442.4608','6.358','157.4761','-3057.4929','440.754','6.3617','240.7684',3,24000000,3,0,0,3,0,'2023-03-08 14:23:12',960,NULL,NULL,0),
	(314,NULL,'-131.7122','-1463.3362','33.8226','268.6251','-136.8876','-1495.8497','33.8086','50.3376',2,12000000,2,0,0,2,0,'2023-03-06 16:07:31',782,NULL,NULL,0),
	(315,NULL,'-3059.5208','453.4732','6.355','334.9681','-3053.8635','445.993','6.3617','239.8656',3,24000000,3,0,0,3,0,'2023-03-15 00:51:00',1727,NULL,NULL,0),
	(316,NULL,'-126.785','-1456.8119','34.4532','142.7183','-141.17','-1492.1208','33.5578','51.2139',2,12000000,2,0,0,2,0,'2023-03-10 02:41:44',1144,NULL,NULL,0),
	(317,NULL,'-123.3768','-1460.5919','33.8226','146.9753','-147.269','-1486.7979','33.1862','49.9621',2,12000000,2,0,0,2,0,'2023-03-08 14:23:15',961,NULL,NULL,0),
	(318,NULL,'-1566.0432','-280.2659','48.2757','130.8563','-1563.4597','-291.1825','48.2757','226.6746',2,12000000,2,0,0,2,0,'2023-03-03 14:31:26',547,NULL,NULL,0),
	(319,NULL,'-113.7357','-1468.4011','33.8226','143.9669','-142.8652','-1490.5593','33.4421','49.7623',2,12000000,2,0,0,2,0,'2023-03-27 16:01:00',2565,NULL,NULL,0),
	(320,NULL,'-107.8836','-1473.1407','33.8227','53.5189','-144.8845','-1489.1826','33.316','49.77',2,12000000,2,0,0,2,0,'2023-03-09 16:21:38',1079,NULL,NULL,0),
	(321,NULL,'-1560.8854','-285.4178','48.2757','164.1731','-1575.5709','-282.0447','48.2757','42.3689',2,12000000,2,0,0,2,0,'2023-03-13 15:01:00',1582,NULL,NULL,0),
	(322,NULL,'-112.8292','-1479.1506','33.8227','48.1052','-151.7217','-1483.7826','32.9669','48.3046',2,12000000,2,0,0,2,0,'2023-03-10 20:01:41',1213,NULL,NULL,0),
	(323,NULL,'-3094.231','364.0904','7.1191','168.2422','-3081.1501','364.3557','7.1262','243.0646',3,24000000,3,0,0,3,0,'2023-03-06 10:50:22',762,NULL,NULL,0),
	(324,NULL,'-1555.2852','-290.2369','48.2705','153.7009','-1568.295','-290.7539','48.2757','314.3164',2,12000000,2,0,0,2,0,'2023-03-01 15:23:01',460,NULL,NULL,0),
	(325,NULL,'-3091.3464','379.1871','7.112','253.5781','-3078.7622','370.9857','7.1266','249.614',4,30000000,4,0,0,4,0,'2023-03-04 21:11:00',647,NULL,NULL,0),
	(326,NULL,'-119.1681','-1486.1954','36.9821','319.5974','-152.6077','-1482.1901','32.8929','49.8371',2,12000000,2,0,0,2,0,'2023-03-07 13:40:55',860,NULL,NULL,0),
	(327,NULL,'-1068.4637','-1049.4242','6.4117','32.5838','-1075.5546','-1047.7583','2.1503','31.1518',3,24000000,3,0,0,3,0,'2023-03-13 20:47:40',1627,NULL,NULL,0),
	(328,NULL,'-1564.2214','-300.1179','48.2288','315.196','-1560.3844','-299.3736','48.187','317.1241',2,12000000,2,0,0,2,0,'2023-03-22 00:09:51',2070,NULL,NULL,0),
	(329,NULL,'-112.843','-1479.2025','36.9921','320.3627','-137.5441','-1495.1226','33.7604','54.147',2,12000000,2,0,0,2,0,'2023-03-08 20:39:25',1009,NULL,NULL,0),
	(330,NULL,'-1065.5459','-1055.3837','6.4117','137.6595','-1079.0414','-1049.6285','2.1502','26.8418',3,24000000,3,0,0,3,0,'2023-03-23 13:02:24',2138,NULL,NULL,0),
	(331,NULL,'-1569.3567','-294.8801','48.2757','314.1493','-1565.934','-294.3238','48.2757','319.2129',2,12000000,2,0,0,2,0,'2023-03-18 15:01:00',1886,NULL,NULL,0),
	(332,NULL,'-1064.4285','-1057.1993','6.4117','152.248','-1082.7809','-1051.4443','2.1499','34.6102',3,24000000,3,0,0,3,0,'2023-03-06 05:01:00',754,NULL,NULL,0),
	(333,NULL,'-107.9942','-1473.1381','36.9922','34.9582','-133.7381','-1497.7244','33.904','51.1581',2,12000000,2,0,0,2,0,'2023-03-13 01:11:00',1513,NULL,NULL,0),
	(334,NULL,'-113.7377','-1468.1121','36.9922','227.5369','-101.6731','-1460.4137','33.2966','231.5682',2,12000000,2,0,0,2,0,'2023-03-15 12:23:24',1741,NULL,NULL,0),
	(335,NULL,'-1574.613','-289.9327','48.2757','314.7883','-1571.8053','-288.6744','48.2757','314.5661',2,12000000,2,0,0,2,0,'2023-03-04 15:29:38',621,NULL,NULL,0),
	(336,NULL,'-1063.5645','-1054.9344','2.1504','116.9303','-1075.8517','-1040.7134','2.0935','297.7942',3,24000000,3,0,0,3,0,'2023-03-14 22:54:31',1722,NULL,NULL,0),
	(337,NULL,'-3225.4709','911.1339','13.9933','47.505','-3216.9294','915.7986','13.9905','311.1489',4,30000000,4,0,0,4,0,'2023-03-10 11:36:41',1167,NULL,NULL,0),
	(338,NULL,'-1582.1602','-278.0979','48.2757','289.0002','-1574.2159','-286.1111','48.2757','319.7517',2,12000000,2,0,0,2,0,'2023-03-11 17:46:00',1305,NULL,NULL,0),
	(339,NULL,'-123.2339','-1460.2797','36.9921','258.3376','-134.6667','-1497.1198','33.8784','48.6964',2,12000000,2,0,0,2,0,'2023-03-09 13:01:01',1056,NULL,NULL,0),
	(340,NULL,'-3228.6892','927.4359','13.9698','298.5481','-3224.1431','924.9175','13.9653','301.7268',4,30000000,4,0,0,4,0,'2023-03-11 20:13:17',1326,NULL,NULL,0),
	(341,NULL,'-1056.3444','-1000.8738','2.1502','305.2672','-1044.0879','-1008.305','2.1502','210.1426',3,24000000,3,0,0,3,0,'2023-03-26 23:33:10',2502,NULL,NULL,0),
	(342,NULL,'-127.2342','-1457.1598','37.7919','228.7461','-146.5968','-1487.3368','33.2216','46.5532',2,12000000,2,0,0,2,0,'2023-03-07 12:01:00',856,NULL,NULL,0),
	(343,NULL,'-3232.5137','934.8056','13.7984','303.4619','-3231.5281','940.0709','13.7467','292.1804',4,30000000,4,0,0,4,0,'2023-03-12 15:18:14',1409,NULL,NULL,0),
	(344,NULL,'-1583.4789','-265.866','48.2757','262.0226','-1579.6555','-263.8281','48.2757','254.599',2,12000000,2,0,0,2,0,'2023-03-21 01:11:00',2033,NULL,NULL,0),
	(345,NULL,'-132.1653','-1462.897','36.9921','226.6409','-148.7484','-1485.3545','33.1088','49.5492',2,12000000,2,0,0,2,0,'2023-03-08 18:01:00',992,NULL,NULL,0),
	(346,NULL,'-3238.1245','952.6241','13.2447','286.0939','-3234.9121','948.9904','13.2104','278.0953',4,30000000,4,0,0,4,0,'2023-03-11 23:47:21',1347,NULL,NULL,0),
	(347,NULL,'-138.0891','-1470.4569','36.9921','290.881','-135.4394','-1496.7806','33.8626','50.9659',2,12000000,2,0,0,2,0,'2023-03-07 19:01:00',889,NULL,NULL,0),
	(348,NULL,'-3251.1455','1027.2557','11.7577','265.0333','-3237.6997','1034.234','11.6903','261.0059',4,30000000,4,0,0,4,0,'2023-03-09 23:35:29',1123,NULL,NULL,0),
	(349,NULL,'-1560.5591','-274.5117','48.2782','312.8','-1565.6794','-253.0929','48.273','232.4559',2,12000000,2,0,0,2,0,'2023-02-27 10:20:00',561,NULL,NULL,0),
	(350,NULL,'-1022.3514','-1022.8619','2.1504','116.8969','-1027.9535','-1009.7682','2.0667','127.9043',4,40000000,5,0,0,4,0,'2023-03-11 23:34:23',1348,NULL,NULL,0),
	(351,NULL,'-1555.4816','-279.3084','48.2785','325.0374','-1563.6581','-250.2975','48.2757','236.7297',2,12000000,2,0,0,2,0,'2023-03-10 17:29:00',1198,NULL,NULL,0),
	(352,NULL,'-125.8695','-1473.616','36.9921','315.9075','-132.3308','-1499.5189','33.971','50.6294',2,12000000,2,0,0,2,0,'2023-03-02 16:22:07',507,NULL,NULL,0),
	(353,NULL,'-1008.6076','-1015.1933','2.1503','194.6676','-1009.3874','-1009.1893','2.1503','31.0425',4,40000000,5,0,0,4,0,'2023-03-11 01:15:34',1253,NULL,NULL,0),
	(354,NULL,'-1550.1188','-283.9798','48.2793','315.6961','-1561.6362','-246.9419','48.2757','238.2937',2,12000000,2,0,0,2,0,'2023-03-18 19:32:44',1903,NULL,NULL,0),
	(355,NULL,'-3254.6611','1063.9487','11.1462','355.3656','-3228.8164','1049.1298','11.4124','174.0248',4,30000000,4,0,0,4,0,'2023-03-10 05:10:22',1155,NULL,NULL,0),
	(356,NULL,'-119.9023','-1477.9921','36.9921','227.7505','-130.5368','-1501.0072','34.0049','51.9811',2,12000000,2,0,0,2,0,'2023-03-13 15:11:00',1584,NULL,NULL,0),
	(357,NULL,'-1542.8364','-278.4257','48.2797','51.9807','-1558.9951','-244.1173','48.2757','233.7044',2,12000000,2,0,0,2,0,'2023-03-26 19:52:03',2462,NULL,NULL,0),
	(358,NULL,'-3232.8059','1068.0326','11.0333','257.3163','-3227.3098','1071.1006','11.0099','167.9045',4,30000000,4,0,0,4,0,'2023-03-13 20:10:43',1632,NULL,NULL,0),
	(359,NULL,'-1541.381','-276.5347','48.2795','51.2937','-1556.3101','-240.8845','48.2757','230.001',2,12000000,2,0,0,2,0,'2023-03-19 20:13:00',1956,NULL,NULL,0),
	(360,NULL,'-1538.0316','-272.3603','48.2793','61.9908','-1554.5857','-238.0251','48.2757','232.3676',2,12000000,2,0,0,2,0,'2023-03-05 23:35:00',734,NULL,NULL,0),
	(361,NULL,'-1536.6207','-270.4216','48.2789','54.5881','-1551.4541','-234.824','48.2757','320.2491',2,12000000,2,0,0,2,0,'2023-03-25 21:52:12',2343,NULL,NULL,0),
	(362,NULL,'-3229.083','1100.8214','10.5787','163.4531','-3219.8152','1105.5791','10.4493','251.175',4,30000000,4,0,0,4,0,'2023-03-27 23:37:49',2656,NULL,NULL,0),
	(363,NULL,'-997.5431','-1012.3993','2.1503','300.4812','-996.7053','-1000.5535','2.1503','23.9613',4,40000000,5,0,0,4,0,'2023-03-13 13:35:45',1562,NULL,NULL,0),
	(364,NULL,'-1533.5046','-260.3735','48.275','134.2068','-1565.7019','-253.0171','48.2728','222.8396',2,12000000,2,0,0,2,0,'2023-03-12 22:01:00',1489,NULL,NULL,0),
	(365,NULL,'-3205.3423','1152.0829','9.6623','249.9391','-3201.6169','1154.3087','9.6543','251.1546',4,30000000,4,0,0,4,0,'2023-03-06 16:31:48',779,NULL,NULL,0),
	(366,NULL,'-1538.516','-254.9772','48.2764','137.1111','-1564.0977','-250.3432','48.2757','237.0351',2,12000000,2,0,0,2,0,'2023-03-10 13:00:01',1172,NULL,NULL,0),
	(367,NULL,'-1543.4063','-249.2309','48.2806','134.6664','-1561.6538','-247.0158','48.2757','237.325',2,12000000,2,0,0,2,0,'2023-03-24 14:01:00',2219,NULL,NULL,0),
	(368,NULL,'-3200.043','1165.411','9.6543','250.9785','-3198.3259','1159.9585','9.6543','250.3459',4,30000000,4,0,0,4,0,'2023-03-12 15:54:31',1412,NULL,NULL,0),
	(369,NULL,'-64.2318','-1449.606','32.5249','194.4255','-67.8477','-1460.6882','32.0536','203.2054',2,12000000,2,0,0,2,0,'2023-03-20 14:01:01',1996,NULL,NULL,0),
	(370,NULL,'-3195.1392','1179.5599','9.6595','261.9077','-3192.8235','1176.5004','9.4192','252.8713',4,30000000,4,0,0,4,0,'2024-03-20 18:05:38',2726,NULL,NULL,0),
	(371,NULL,'-1707.0325','-453.6093','42.6492','137.0955','-1699.4181','-442.1019','41.3143','229.392',2,12000000,2,0,0,2,0,'2023-03-10 11:01:00',1165,NULL,NULL,0),
	(372,NULL,'-3187.0276','1273.8016','12.662','255.2688','-3186.115','1268.3685','12.5798','257.7726',4,30000000,4,0,0,4,0,'2023-03-10 05:39:51',1156,NULL,NULL,0),
	(373,NULL,'-1698.4498','-460.7906','41.6493','134.8524','-1697.1796','-443.8733','41.2782','228.2536',2,12000000,2,0,0,2,0,'2023-03-27 18:21:48',2588,NULL,NULL,0),
	(374,NULL,'-3190.8608','1297.6343','19.0674','244.2233','-3176.55','1296.5852','14.4973','249.3042',4,30000000,4,0,0,4,0,'2023-03-23 20:01:29',2174,NULL,NULL,0),
	(375,NULL,'-1693.2004','-464.8523','41.6494','136.2543','-1695.1268','-445.6342','41.2807','228.6992',2,12000000,2,0,0,2,0,'2023-03-13 12:09:06',1557,NULL,NULL,0),
	(376,NULL,'-46.2542','-1445.9731','32.4296','193.9709','-52.8474','-1457.0121','32.021','188.3206',2,12000000,2,0,0,2,0,'2023-03-10 01:51:26',1139,NULL,NULL,0),
	(377,NULL,'-1103.4601','-1014.2712','2.3452','26.835','-1085.4299','-1040.8698','2.1432','291.9905',4,40000000,5,0,0,4,0,'2023-03-10 14:16:51',1175,NULL,NULL,0),
	(378,NULL,'-1700.1146','-474.6399','41.6493','49.8116','-1692.8311','-447.3873','41.0693','229.3612',2,12000000,2,0,0,2,0,'2023-03-13 03:12:27',1523,NULL,NULL,0),
	(379,NULL,'-32.9701','-1446.432','31.8914','35.8047','-38.1395','-1451.4269','31.5031','182.4314',2,12000000,2,0,0,2,0,'2023-03-09 19:11:00',1098,NULL,NULL,0),
	(380,NULL,'-1704.647','-480.229','41.6494','49.8174','-1690.6207','-449.1217','40.9238','229.7385',2,12000000,2,0,0,2,0,'2023-03-13 13:59:00',1564,NULL,NULL,0),
	(381,NULL,'-1112.4673','-1019.6036','2.3965','309.5814','-1095.5938','-1045.9108','2.1519','306.6037',4,40000000,5,0,0,4,0,'2023-03-11 22:01:14',1337,NULL,NULL,0),
	(382,NULL,'-3228.74','1092.5809','10.7727','251.2633','-3226.5916','1090.2568','10.709','251.2106',2,12000000,2,0,0,2,0,'2023-03-03 00:01:00',527,NULL,NULL,0),
	(383,NULL,'-3231.9619','1081.6378','10.8081','248.0665','-3227.3655','1086.9879','10.7477','250.221',2,12000000,2,0,0,2,0,'2023-03-26 18:52:16',2453,NULL,NULL,0),
	(384,NULL,'-1709.5334','-480.7418','41.6495','318.688','-1687.8011','-451.2578','40.6852','231.1038',2,12000000,2,0,0,2,0,'2023-03-09 14:01:00',1062,NULL,NULL,0),
	(385,NULL,'-1.9323','-1442.9808','30.7654','176.9012','0.4582','-1452.9492','30.4825','177.032',2,12000000,2,0,0,2,0,'2023-03-12 01:57:46',1359,NULL,NULL,0),
	(386,NULL,'-1712.2906','-477.2358','41.6494','270.8659','-1685.0231','-453.5077','40.4484','230.8325',2,12000000,2,0,0,2,0,'2023-03-24 19:01:00',2242,NULL,NULL,0),
	(387,NULL,'-1133.9803','-1050.1625','2.1504','104.4876','-1134.9368','-1061.7084','2.1504','210.5564',4,40000000,5,0,0,4,0,'2023-03-06 18:14:01',796,NULL,NULL,0),
	(388,NULL,'-1713.204','-470.4612','41.6493','267.3094','-1682.0563','-455.9218','40.1024','230.8798',2,12000000,2,0,0,2,0,'2023-03-26 20:01:00',2473,NULL,NULL,0),
	(389,NULL,'16.2741','-1444.3','30.9486','153.6059','7.5776','-1452.303','30.5437','167.8606',2,12000000,2,0,0,2,0,'2023-03-12 17:46:57',1438,NULL,NULL,0),
	(390,NULL,'-3213.6504','1136.6368','9.8954','154.2333','-3205.5','1135.595','9.8974','246.663',3,24000000,3,0,0,3,0,'2023-03-09 01:20:05',1030,NULL,NULL,0),
	(391,NULL,'-1713.9252','-463.5973','41.6493','280.4041','-1679.7917','-457.7623','39.7483','230.8942',2,12000000,2,0,0,2,0,'2023-03-15 06:17:36',1738,NULL,NULL,0),
	(392,NULL,'-3209.8164','1144.7427','9.8954','334.2037','-3203.509','1138.8425','9.8974','243.5643',3,24000000,3,0,0,3,0,'2023-03-02 03:38:34',498,NULL,NULL,0),
	(393,NULL,'-3200.9966','1194.5717','9.5446','192.1978','-3189.3677','1198.113','9.4358','269.7257',3,24000000,3,0,0,3,0,'2023-03-23 22:02:00',2185,NULL,NULL,0),
	(394,NULL,'-1122.8767','-1089.5736','2.5452','307.3312','-1129.2555','-1070.812','2.0786','298.3073',4,40000000,5,0,0,4,0,'2023-03-15 19:54:54',1756,NULL,NULL,0),
	(395,NULL,'-3193.5703','1208.5847','9.4252','352.6583','-3188.8376','1203.5953','9.5044','256.9625',3,24000000,3,0,0,3,0,'2023-03-25 04:48:00',2279,NULL,NULL,0),
	(396,NULL,'-1493.5317','-668.2256','29.0251','313.5858','-1485.8508','-664.2515','28.9432','220.7899',2,12000000,2,0,0,2,0,'2023-03-12 11:11:00',1393,NULL,NULL,0),
	(397,NULL,'-1498.0016','-664.6667','29.0252','311.3906','-1485.8508','-664.2515','28.9432','220.7899',2,12000000,2,0,0,2,0,'2023-03-24 16:01:00',2229,NULL,NULL,0),
	(398,NULL,'-1495.2446','-661.8238','29.0252','216.5613','-1485.8508','-664.2515','28.9432','220.7899',2,12000000,2,0,0,2,0,'2023-03-08 15:35:05',975,NULL,NULL,0),
	(399,NULL,'-3194.1099','1229.8138','10.0483','337.8593','-3186.8025','1226.2662','10.0585','262.7932',3,24000000,3,0,0,3,0,'2023-03-17 22:55:00',1856,NULL,NULL,0),
	(400,NULL,'-1490.6141','-658.643','29.0252','209.3643','-1485.8508','-664.2515','28.9432','220.7899',2,12000000,2,0,0,2,0,'2023-03-18 23:01:00',1915,NULL,NULL,0),
	(401,NULL,'-3195.1428','1221.1305','10.0483','170.6273','-3186.3921','1222.8319','9.9888','262.6489',3,24000000,3,0,0,3,0,'2023-03-22 21:42:00',2107,NULL,NULL,0),
	(402,NULL,'-1486.4927','-655.6899','29.5828','210.5252','-1483.1161','-662.5709','28.9432','217.2553',2,12000000,2,0,0,2,0,'2023-03-05 03:01:00',665,NULL,NULL,0),
	(403,NULL,'-1177.35','-1073.3761','5.9064','124.4576','-1189.8281','-1082.0337','2.1504','202.8669',3,24000000,3,0,0,3,0,'2023-03-06 20:11:55',811,NULL,NULL,0),
	(404,NULL,'-1482.1277','-652.3491','29.5829','222.6926','-1483.1161','-662.5709','28.9432','217.2553',2,12000000,2,0,0,2,0,'2023-03-05 13:15:10',677,NULL,NULL,0),
	(405,NULL,'-1478.1029','-649.4438','29.5829','212.6873','-1483.1161','-662.5709','28.9432','217.2553',2,12000000,2,0,0,2,0,'2023-03-04 21:31:16',648,NULL,NULL,0),
	(406,NULL,'-1183.005','-1064.699','2.1504','310.8515','-1188.3411','-1065.2518','2.1504','118.9063',4,40000000,5,0,0,4,0,'2023-03-07 22:01:27',909,NULL,NULL,0),
	(407,NULL,'-1473.5881','-646.1855','29.5829','212.4292','-1480.1462','-660.518','28.9432','214.7939',2,12000000,2,0,0,2,0,'2023-03-13 14:29:19',1573,NULL,NULL,0),
	(408,NULL,'-1190.9401','-1054.881','2.1504','330.8883','-1199.0095','-1057.2651','2.1504','194.879',4,40000000,5,0,0,4,0,'2023-03-11 19:55:02',1321,NULL,NULL,0),
	(409,NULL,'-1469.5851','-643.1229','29.5829','209.3033','-1480.1462','-660.518','28.9432','214.7939',2,12000000,2,0,0,2,0,'2023-03-16 04:01:00',1776,NULL,NULL,0),
	(410,NULL,'-1195.5161','-1036.0941','2.1677','292.7418','-1199.9657','-1043.1522','2.1504','123.9324',4,40000000,5,0,0,4,0,'2023-03-09 00:05:01',1027,NULL,NULL,0),
	(411,NULL,'-1465.0316','-639.7174','29.5829','212.8706','-1480.1462','-660.518','28.9432','214.7939',2,12000000,2,0,0,2,0,'2023-03-09 06:59:27',1045,NULL,NULL,0),
	(412,NULL,'-1461.3156','-640.791','29.583','125.1017','-1477.3881','-658.647','28.9432','227.3596',2,12000000,2,0,0,2,0,'2023-03-12 16:15:08',1426,NULL,NULL,0),
	(413,NULL,'-1188.5665','-1041.6488','2.1503','23.3346','-1203.8434','-1046.171','2.1333','190.3835',4,40000000,5,0,0,4,0,'2023-03-09 01:03:50',1032,NULL,NULL,0),
	(414,NULL,'-1452.53','-653.2378','29.583','122.2531','-1477.3408','-658.6163','28.9432','229.8778',2,12000000,2,0,0,2,0,'2023-03-26 23:01:00',2503,NULL,NULL,0),
	(415,NULL,'-1200.9105','-1031.8917','2.1504','260.0685','-1223.8051','-1013.7783','2.6025','31.5669',4,40000000,5,0,0,4,0,'2023-03-21 00:17:32',2028,NULL,NULL,0),
	(416,NULL,'-1454.5432','-655.7895','29.5829','34.8918','-1470.8124','-653.6478','29.5023','222.0049',2,12000000,2,0,0,2,0,'2023-03-13 13:05:00',1565,NULL,NULL,0),
	(417,NULL,'-1901.428','-586.2206','11.8722','139.533','-1888.781','-571.3063','11.806','323.2532',5,70000000,6,0,0,5,0,'2023-03-14 19:51:39',1713,NULL,NULL,0),
	(418,NULL,'-1458.9706','-659.1722','29.5829','40.7961','-1470.8124','-653.6478','29.5023','222.0049',2,12000000,2,0,0,2,0,'2023-03-14 19:11:00',1714,NULL,NULL,0),
	(419,NULL,'-1204.0474','-1021.6663','5.9451','115.3392','-1221.0161','-1017.2808','2.0764','36.1359',4,40000000,5,0,0,4,0,'2023-03-10 20:59:10',1215,NULL,NULL,0),
	(420,NULL,'-1463.0487','-661.9934','29.5829','38.0317','-1467.7745','-651.5094','29.5023','221.8174',2,12000000,2,0,0,2,0,'2023-03-25 14:25:42',2298,NULL,NULL,0),
	(421,NULL,'-1467.6338','-665.2516','29.5829','36.4466','-1467.7745','-651.5094','29.5023','221.8174',2,12000000,2,0,0,2,0,'2023-03-11 13:46:19',1277,NULL,NULL,0),
	(422,NULL,'-1471.7367','-668.0972','29.583','33.4594','-1465.2401','-649.5527','29.5023','218.0148',2,12000000,2,0,0,2,0,'2023-03-18 19:05:45',1904,NULL,NULL,0),
	(423,NULL,'-1166.785','-1104.5858','6.5319','155.4038','-1170.2281','-1093.6707','2.1151','117.2465',3,24000000,3,0,0,3,0,'2023-03-24 23:13:09',2257,NULL,NULL,0),
	(424,NULL,'-1872.9991','-596.8651','11.8546','318.5463','-1863.7607','-588.4429','11.6157','317.156',4,40000000,5,0,0,4,0,'2023-03-04 20:56:56',642,NULL,NULL,0),
	(425,NULL,'-1461.5281','-640.9709','33.3814','125.7256','-1477.3881','-658.647','28.9432','227.3596',2,12000000,2,0,0,2,0,'2023-03-06 13:11:00',767,NULL,NULL,0),
	(426,NULL,'-1160.848','-1101.9171','6.5313','234.1544','-1167.0305','-1115.8451','2.2861','115.0957',3,24000000,3,0,0,3,0,'2023-03-24 23:37:55',2259,NULL,NULL,0),
	(427,NULL,'-1158.968','-1100.7178','6.5313','232.1952','-1164.9104','-1119.3483','2.2864','120.7369',3,24000000,3,0,0,3,0,'2023-03-21 02:08:46',2035,NULL,NULL,0),
	(428,NULL,'-1464.9902','-639.8389','33.3814','213.4403','-1480.1462','-660.518','28.9432','214.7939',2,12000000,2,0,0,2,0,'2023-03-26 10:29:36',2398,NULL,NULL,0),
	(429,NULL,'-1161.4918','-1099.5637','2.201','206.1772','-1174.7909','-1113.0371','2.5593','28.3222',3,24000000,3,0,0,3,0,'2023-03-19 22:46:36',1965,NULL,NULL,0),
	(430,NULL,'-1884.9659','-599.9328','11.8994','140.672','-1871.6125','-584.4772','11.8128','314.714',4,40000000,5,0,0,4,0,'2023-03-12 16:22:40',1424,NULL,NULL,0),
	(431,NULL,'-1143.3815','-1122.4841','2.6339','23.5935','-1159.5337','-1126.7153','2.4154','117.4037',4,40000000,5,0,0,4,0,'2023-03-03 20:14:33',575,NULL,NULL,0),
	(432,NULL,'-1883.1583','-578.8513','11.818','316.1265','-1879.6329','-578.9316','11.7838','325.9653',4,40000000,5,0,0,4,0,'2023-03-23 18:10:01',2163,NULL,NULL,0),
	(433,NULL,'198.5088','-1725.9489','29.6637','254.1483','209.8232','-1730.2629','29.1251','202.7012',2,12000000,2,0,0,2,0,'2023-03-11 06:31:09',1264,NULL,NULL,0),
	(434,NULL,'-1898.4297','-572.3928','11.8464','229.7269','-1900.9792','-561.4347','11.8074','318.5277',4,40000000,5,0,0,4,0,'2023-03-13 01:06:43',1514,NULL,NULL,0),
	(435,NULL,'-1469.573','-643.0704','33.3814','218.501','-1480.1462','-660.518','28.9432','214.7939',2,12000000,2,0,0,2,0,'2023-03-24 20:42:49',2252,NULL,NULL,0),
	(436,NULL,'-1473.5402','-645.9382','33.3814','224.0829','-1480.1462','-660.518','28.9432','214.7939',2,12000000,2,0,0,2,0,'2023-03-22 16:01:00',2094,NULL,NULL,0),
	(437,NULL,'-1917.8033','-558.8466','11.8462','47.6407','-1907.9712','-552.2803','11.8221','317.7913',4,40000000,5,0,0,4,0,'2023-03-09 07:55:45',1046,NULL,NULL,0),
	(438,NULL,'216.5561','-1717.3856','29.6769','248.6066','214.7784','-1728.533','29.0364','213.3669',2,12000000,2,0,0,2,0,'2023-03-20 07:00:01',1984,NULL,NULL,0),
	(439,NULL,'-1478.2417','-649.2731','33.3814','207.5396','-1483.1161','-662.5709','28.9432','217.2553',2,12000000,2,0,0,2,0,'2023-03-26 13:01:00',2413,NULL,NULL,0),
	(440,NULL,'-1918.5382','-542.4659','11.8273','317.9545','-1914.0356','-544.5868','11.787','327.1786',4,40000000,5,0,0,4,0,'2023-03-12 22:43:55',1490,NULL,NULL,0),
	(441,NULL,'-1482.1998','-652.0613','33.3814','218.0122','-1483.1161','-662.5709','28.9432','217.2553',2,12000000,2,0,0,2,0,'2023-03-08 14:23:13',962,NULL,NULL,0),
	(442,NULL,'223.0028','-1703.2102','29.7051','240.3483','242.7247','-1699.9531','29.0922','228.0332',2,12000000,2,0,0,2,0,'2023-03-11 00:39:41',1249,NULL,NULL,0),
	(443,NULL,'-1486.8433','-655.6215','33.3814','213.0022','-1483.1161','-662.5709','28.9432','217.2553',2,12000000,2,0,0,2,0,'2023-02-28 12:06:14',461,NULL,NULL,0),
	(444,NULL,'249.5327','-1730.6456','29.6688','92.5136','245.1158','-1729.2284','29.2627','50.9329',2,12000000,2,0,0,2,0,'2023-03-13 00:01:00',1502,NULL,NULL,0),
	(445,NULL,'-1490.7457','-658.407','33.3814','215.4189','-1485.8508','-664.2515','28.9432','220.7899',2,12000000,2,0,0,2,0,'2023-03-27 22:01:00',2648,NULL,NULL,0),
	(446,NULL,'-1107.3768','-1223.0526','2.5621','19.37','-1110.891','-1230.9489','2.5926','115.8438',4,40000000,5,0,0,4,0,'2023-03-09 22:43:06',1119,NULL,NULL,0),
	(447,NULL,'-1495.2979','-661.789','33.3814','218.7475','-1483.3975','-662.361','28.9432','217.238',2,12000000,2,0,0,2,0,'2023-03-18 21:09:54',1912,NULL,NULL,0),
	(448,NULL,'256.5598','-1723.049','29.6541','68.0017','258.9216','-1713.7733','29.2734','48.7141',2,12000000,2,0,0,2,0,'2023-03-22 18:12:53',2100,NULL,NULL,0),
	(449,NULL,'-1497.9026','-664.5891','33.3814','317.9617','-1485.8508','-664.2515','28.9432','220.7899',2,12000000,2,0,0,2,0,'2023-03-10 22:00:01',1234,NULL,NULL,0),
	(450,NULL,'-1927.756','-551.7272','11.8413','235.7971','-1920.5276','-537.6904','11.8084','226.6198',4,40000000,5,0,0,4,0,'2023-03-09 21:56:11',1111,NULL,NULL,0),
	(451,NULL,'-1103.7648','-1222.4187','2.7355','238.1363','-1114.4279','-1233.1342','2.4538','27.9685',4,40000000,5,0,0,4,0,'2023-03-26 00:43:52',2364,NULL,NULL,0),
	(452,NULL,'-1493.5792','-668.2093','33.3814','323.6843','-1485.8508','-664.2515','28.9432','220.7899',2,12000000,2,0,0,2,0,'2023-03-07 15:01:00',868,NULL,NULL,0),
	(453,NULL,'-1100.4131','-1231.7266','2.9672','111.3916','-1106.6156','-1228.0596','2.5903','122.2327',4,40000000,5,0,0,4,0,'2023-03-12 17:56:12',1432,NULL,NULL,0),
	(454,NULL,'269.3178','-1712.9716','29.6688','58.3747','268.9958','-1703.6707','29.3','45.9229',2,12000000,2,0,0,2,0,'2023-03-25 22:06:42',2351,NULL,NULL,0),
	(455,NULL,'-1489.7568','-671.2241','33.3814','320.8242','-1485.8508','-664.2515','28.9432','220.7899',2,12000000,2,0,0,2,0,'2023-03-27 19:51:12',2599,NULL,NULL,0),
	(456,NULL,'-1945.7454','-544.7634','11.8637','48.5521','-1933.2197','-534.9605','11.8221','314.7793',4,40000000,5,0,0,4,0,'2023-03-08 16:38:14',981,NULL,NULL,0),
	(457,NULL,'281.5422','-1694.3627','29.6476','117.9413','271.6195','-1693.9474','29.2328','43.2316',2,12000000,2,0,0,2,0,'2023-03-12 12:11:59',1398,NULL,NULL,0),
	(458,NULL,'-1458.1505','-645.6074','33.3814','129.8046','-1477.3881','-658.647','28.9432','227.3596',2,12000000,2,0,0,2,0,'2023-03-03 00:11:01',528,NULL,NULL,0),
	(459,NULL,'-986.2031','-1199.1428','6.0464','110.152','-980.0664','-1200.882','4.824','301.237',4,40000000,5,0,0,4,0,'2023-03-07 00:24:56',833,NULL,NULL,0),
	(460,NULL,'-1455.84','-648.5526','33.3814','121.0616','-1477.3881','-658.647','28.9432','227.3596',2,12000000,2,0,0,2,0,'2023-03-03 00:11:00',529,NULL,NULL,0),
	(461,NULL,'-1452.481','-653.291','33.3814','116.5776','-1477.3881','-658.647','28.9432','227.3596',2,12000000,2,0,0,2,0,'2023-03-05 12:10:24',675,NULL,NULL,0),
	(462,NULL,'-1454.438','-655.7143','33.3814','34.5098','-1470.8124','-653.6478','29.5023','222.0049',2,12000000,2,0,0,2,0,'2023-03-07 15:38:50',870,NULL,NULL,0),
	(463,NULL,'-1947.0256','-543.9947','11.8646','231.4067','-1938.8492','-529.0272','11.8257','317.0118',4,40000000,5,0,0,4,0,'2023-03-09 18:15:24',1094,NULL,NULL,0),
	(464,NULL,'-1459.0305','-659.2303','33.3814','40.0176','-1470.8124','-653.6478','29.5023','222.0049',2,12000000,2,0,0,2,0,'2023-03-25 18:01:01',2322,NULL,NULL,0),
	(465,NULL,'-1462.9613','-662.1528','33.3814','31.9658','-1467.7745','-651.5094','29.5023','221.8174',2,12000000,2,0,0,2,0,'2023-03-19 15:01:39',1939,NULL,NULL,0),
	(466,NULL,'-1467.5963','-665.4373','33.3814','36.4373','-1467.7745','-651.5094','29.5023','221.8174',2,12000000,2,0,0,2,0,'2023-03-12 21:21:00',1482,NULL,NULL,0),
	(467,NULL,'-1471.6488','-668.3546','33.3814','35.9955','-1465.2401','-649.5527','29.5023','218.0148',2,12000000,2,0,0,2,0,'2023-03-17 20:01:01',1846,NULL,NULL,0),
	(468,NULL,'-1958.1937','-538.3716','11.8994','140.5968','-1940.2953','-519.9602','11.8476','226.2196',4,40000000,5,0,0,4,0,'2023-03-13 17:06:23',1601,NULL,NULL,0),
	(469,NULL,'-1063.6394','-1133.447','2.1586','188.7604','-1064.2202','-1145.4189','2.1586','204.2705',4,40000000,5,0,0,4,0,'2023-02-28 18:25:30',445,NULL,NULL,0),
	(470,NULL,'332.9426','-1741.7673','29.7305','153.4689','332.6749','-1753.4268','29.2789','229.8017',2,12000000,2,0,0,2,0,'2023-03-04 19:07:00',635,NULL,NULL,0),
	(471,NULL,'-1476.0626','-671.6097','33.3814','26.3402','-1465.2401','-649.5527','29.5023','218.0148',2,12000000,2,0,0,2,0,'2023-03-07 18:24:52',882,NULL,NULL,0),
	(472,NULL,'321.2732','-1759.8866','29.6379','247.9052','322.993','-1772.3815','28.6829','228.5582',2,12000000,2,0,0,2,0,'2023-03-05 18:07:15',705,NULL,NULL,0),
	(473,NULL,'304.9803','-1775.952','29.1137','146.4223','312.5211','-1784.0979','28.2307','227.571',2,12000000,2,0,0,2,0,'2023-03-13 02:01:00',1519,NULL,NULL,0),
	(474,NULL,'-1182.6705','-243.9638','37.9465','42.7133','-1141.1388','-224.9247','37.947','103.783',2,12000000,2,0,0,2,0,'2023-03-20 06:34:43',1982,NULL,NULL,0),
	(475,NULL,'300.2802','-1784.5082','28.4387','179.4337','297.6417','-1791.5353','27.9518','227.3833',2,12000000,2,0,0,2,0,'2023-03-16 14:55:21',1783,NULL,NULL,0),
	(476,NULL,'-1188.5012','-249.7319','37.9466','48.3059','-1142.8501','-220.3135','37.9425','93.6853',2,12000000,2,0,0,2,0,'2023-03-16 23:14:14',1811,NULL,NULL,0),
	(477,NULL,'-1194.4531','-250.8701','37.9467','331.3111','-1142.0979','-215.5662','37.9405','73.9485',2,12000000,2,0,0,2,0,'2023-03-26 12:14:28',2406,NULL,NULL,0),
	(478,NULL,'289.194','-1792.5254','28.0891','178.0346','292.7086','-1805.8656','27.3183','229.594',2,12000000,2,0,0,2,0,'2023-03-27 13:04:01',2548,NULL,NULL,0),
	(479,NULL,'-1201.84','-247.1181','37.9468','331.3337','-1140.7997','-211.114','37.9343','67.4354',2,12000000,2,0,0,2,0,'2023-03-12 07:35:47',1382,NULL,NULL,0),
	(480,NULL,'-1957.7428','-528.2425','12.1812','227.9602','-1953.7209','-515.9561','11.8861','319.1413',4,40000000,5,0,0,4,0,'2023-03-07 22:32:11',911,NULL,NULL,0),
	(481,NULL,'-1207.297','-240.2914','37.9462','240.9522','-1139.4338','-206.8898','37.9402','54.5567',2,12000000,2,0,0,2,0,'2023-03-12 11:38:03',1394,NULL,NULL,0),
	(483,NULL,'-1203.4786','-233.0207','37.9462','242.3991','-1136.229','-202.9693','37.9486','86.1681',2,12000000,2,0,0,2,0,'2023-03-26 01:01:00',2370,NULL,NULL,0),
	(485,NULL,'-1969.4047','-516.374','11.8332','322.9495','-1958.8018','-506.8903','11.849','320.1847',4,40000000,5,0,0,4,0,'2023-03-13 21:02:00',1647,NULL,NULL,0),
	(486,NULL,'-1199.4943','-225.7808','37.9461','243.6835','-1153.8937','-227.3794','37.8987','303.1873',2,12000000,2,0,0,2,0,'2023-03-18 02:30:51',1868,NULL,NULL,0),
	(488,NULL,'-1195.8446','-218.5307','37.9448','235.1477','-1151.6539','-230.5897','37.8607','299.8673',2,12000000,2,0,0,2,0,'2023-03-26 15:56:24',2431,NULL,NULL,0),
	(490,NULL,'500.4657','-1697.4211','29.7892','142.1268','498.1386','-1703.184','29.3719','242.4393',2,12000000,2,0,0,2,0,'2023-03-26 06:29:58',2389,NULL,NULL,0),
	(491,NULL,'1390.8802','-1508.0247','58.4358','198.8306','1372.35','-1520.1335','57.4346','206.1108',3,24000000,3,0,0,3,0,'2023-03-20 20:12:15',2015,NULL,NULL,0),
	(492,NULL,'-1977.7975','-509.2207','11.8501','47.4503','-1967.14','-500.8219','11.8463','322.3083',4,40000000,5,0,0,4,0,'2023-03-07 20:09:58',898,NULL,NULL,0),
	(493,NULL,'1382.1311','-1544.7052','57.1072','126.6992','1395.2656','-1533.7542','57.5552','42.1203',3,24000000,3,0,0,3,0,'2023-03-09 01:41:08',1033,NULL,NULL,0),
	(494,NULL,'-1838.8066','-629.5676','11.2466','230.7994','-1832.1577','-614.3844','11.3039','322.0616',4,40000000,5,0,0,4,0,'2023-03-12 02:08:01',1367,NULL,NULL,0),
	(495,NULL,'-1836.3677','-631.7726','10.7544','47.6578','-1826.1327','-624.3085','11.0974','318.0832',4,40000000,5,0,0,4,0,'2023-03-08 20:13:51',1007,NULL,NULL,0),
	(496,NULL,'1360.3701','-1555.6465','56.3428','11.8585','1347.3236','-1550.934','53.6903','31.265',3,24000000,3,0,0,3,0,'2023-03-09 18:57:22',1091,NULL,NULL,0),
	(497,NULL,'-1820.3965','-643.1282','10.9397','320.8297','-1812.2819','-636.7973','10.9383','321.1216',4,40000000,5,0,0,4,0,'2023-03-04 02:08:00',600,NULL,NULL,0),
	(498,NULL,'508.6619','193.6603','104.745','340.4025','461.7277','222.9471','103.0996','71.7603',3,24000000,3,0,0,3,0,'2023-03-25 04:15:46',2280,NULL,NULL,0),
	(499,NULL,'515.0726','191.3802','104.745','339.1792','464.0929','227.181','103.1983','64.3061',3,24000000,3,0,0,3,0,'2023-03-13 20:41:00',1629,NULL,NULL,0),
	(500,NULL,'520.4444','192.1165','104.7448','72.9124','465.4723','230.7409','103.2098','67.5596',3,24000000,3,0,0,3,0,'2023-03-21 13:15:00',2041,NULL,NULL,0),
	(501,NULL,'522.7115','199.3331','104.7441','76.6031','467.1445','234.6707','103.2097','67.3366',3,24000000,3,0,0,3,0,'2023-03-27 05:29:05',2527,NULL,NULL,0),
	(502,NULL,'525.8591','207.2053','104.744','70.6974','468.5031','238.8675','103.2097','67.437',3,24000000,3,0,0,3,0,'2023-03-12 19:19:12',1464,NULL,NULL,0),
	(503,NULL,'528.0786','213.3346','104.7443','72.557','469.8116','242.4178','103.2097','65.5907',3,24000000,3,0,0,3,0,'2023-03-13 19:38:37',1619,NULL,NULL,0),
	(504,NULL,'531.3887','222.3391','104.7448','65.5278','471.3484','246.3239','103.2097','64.983',3,24000000,3,0,0,3,0,'2023-03-19 22:22:44',1966,NULL,NULL,0),
	(505,NULL,'-1834.0164','-641.8574','11.4781','50.2767','-1846.5232','-611.4526','11.2765','48.9128',4,40000000,5,0,0,4,0,'2023-03-16 22:04:57',1808,NULL,NULL,0),
	(506,NULL,'527.4933','225.9925','104.7447','165.5836','472.8978','250.1692','103.2098','68.8292',3,24000000,3,0,0,3,0,'2023-03-19 01:23:28',1918,NULL,NULL,0),
	(507,NULL,'518.973','229.1067','104.7441','162.4141','474.1863','254.1718','103.2107','67.9354',3,24000000,3,0,0,3,0,'2023-03-08 14:23:14',963,NULL,NULL,0),
	(508,NULL,'511.3411','231.7122','104.744','160.4075','452.3723','241.3749','103.2099','248.4369',3,24000000,3,0,0,3,0,'2023-03-10 21:52:53',1225,NULL,NULL,0),
	(509,NULL,'1338.1237','-1524.689','54.5808','354.8086','1335.4949','-1531.368','53.025','182.9361',3,24000000,3,0,0,3,0,'2023-03-23 01:46:24',2122,NULL,NULL,0),
	(510,NULL,'-1814.0099','-656.7489','10.8867','232.0169','-1806.5258','-642.7075','10.8528','316.5479',4,40000000,5,0,0,4,0,'2023-03-04 22:33:20',650,NULL,NULL,0),
	(511,NULL,'504.9176','234.0235','104.7443','160.178','444.8389','257.1973','103.2097','73.0058',3,24000000,3,0,0,3,0,'2023-03-01 01:29:51',462,NULL,NULL,0),
	(512,NULL,'1327.2577','-1552.9434','54.0516','235.5141','1336.5585','-1548.65','52.796','43.6711',3,24000000,3,0,0,3,0,'2023-03-11 22:11:01',1338,NULL,NULL,0),
	(513,NULL,'496.5794','237.1928','104.7448','177.1037','446.4672','260.9194','103.2094','68.1924',3,24000000,3,0,0,3,0,'2023-03-25 17:41:40',2313,NULL,NULL,0),
	(514,NULL,'-1803.7306','-662.0573','10.726','232.972','-1795.2043','-644.1794','10.9092','319.8341',4,40000000,5,0,0,4,0,'2023-03-06 23:49:46',830,NULL,NULL,0),
	(515,NULL,'490.1195','227.0734','104.7444','251.807','448.1932','264.8579','103.2089','62.3206',3,24000000,3,0,0,3,0,'2023-03-09 01:31:00',1034,NULL,NULL,0),
	(516,NULL,'1341.8052','-1578.2371','54.4431','32.5591','1351.1886','-1574.4052','54.0475','210.5957',3,24000000,3,0,0,3,0,'2023-03-05 02:11:00',662,NULL,NULL,0),
	(517,NULL,'487.6601','220.6577','104.7441','254.0887','448.7574','268.716','103.1985','76.5814',3,24000000,3,0,0,3,0,'2023-03-04 01:01:00',593,NULL,NULL,0),
	(518,NULL,'-1788.1166','-671.8292','10.652','41.3283','-1778.2756','-667.5182','10.3836','319.1521',4,40000000,5,0,0,4,0,'2023-03-10 01:04:37',1140,NULL,NULL,0),
	(519,NULL,'484.9405','213.1883','104.7441','254.2092','475.6156','258.2021','103.1828','66.1629',3,24000000,3,0,0,3,0,'2023-03-26 04:01:00',2383,NULL,NULL,0),
	(520,NULL,'482.172','205.9052','104.7447','253.066','458.3452','261.005','103.2031','247.0837',3,24000000,3,0,0,3,0,'2023-03-24 13:22:02',2214,NULL,NULL,0),
	(521,NULL,'486.0155','201.7351','104.7449','343.8163','456.8563','257.0117','103.2091','251.2424',3,24000000,3,0,0,3,0,'2023-03-06 00:08:10',739,NULL,NULL,0),
	(522,NULL,'-1765.9751','-681.5443','10.3377','316.726','-1766.5548','-678.5007','10.2162','320.2707',4,40000000,5,0,0,4,0,'2023-03-06 18:23:46',799,NULL,NULL,0),
	(523,NULL,'508.5573','193.4509','108.3096','338.5722','455.3438','253.1956','103.2091','246.425',3,24000000,3,0,0,3,0,'2023-03-04 01:01:00',594,NULL,NULL,0),
	(524,NULL,'514.8126','191.1907','108.3095','342.4349','453.3905','249.8029','103.2092','243.151',3,24000000,3,0,0,3,0,'2023-03-27 23:11:00',2659,NULL,NULL,0),
	(525,NULL,'520.2746','192.213','108.3095','68.0271','452.6069','245.5995','103.2094','243.9401',3,24000000,3,0,0,3,0,'2023-03-03 22:11:12',580,NULL,NULL,0),
	(526,NULL,'1297.2532','-1618.1267','54.4444','14.2257','1271.114','-1607.8757','53.9327','31.6389',3,24000000,3,0,0,3,0,'2023-03-05 22:00:01',727,NULL,NULL,0),
	(527,NULL,'522.9062','199.2037','108.3095','79.0709','451.1425','241.7053','103.21','248.4491',3,24000000,3,0,0,3,0,'2023-03-24 23:25:00',2262,NULL,NULL,0),
	(528,NULL,'525.8261','206.7555','108.3095','75.4468','459.2336','260.7025','103.2028','247.3201',3,24000000,3,0,0,3,0,'2023-03-10 23:37:27',1241,NULL,NULL,0),
	(529,NULL,'1276.593','-1629.2305','54.5383','27.9305','1251.9463','-1618.3798','53.4394','29.7597',3,24000000,3,0,0,3,0,'2023-03-25 08:01:00',2284,NULL,NULL,0),
	(530,NULL,'528.0844','213.214','108.3095','75.4563','472.7266','254.9137','103.2091','69.5804',3,24000000,3,0,0,3,0,'2023-03-24 00:23:00',2194,NULL,NULL,0),
	(531,NULL,'-1777.2893','-701.6964','10.5058','140.153','-1757.3944','-686.7313','10.0757','325.395',4,40000000,5,0,0,4,0,'2023-03-07 19:51:14',890,NULL,NULL,0),
	(532,NULL,'531.1744','221.8128','108.3095','79.7008','470.7516','251.0743','103.2097','71.9898',3,24000000,3,0,0,3,0,'2023-03-12 01:11:00',1362,NULL,NULL,0),
	(533,NULL,'527.4899','226.1008','108.3095','173.8001','469.586','246.9803','103.2098','69.2241',3,24000000,3,0,0,3,0,'2023-03-09 01:04:36',1035,NULL,NULL,0),
	(534,NULL,'518.879','229.0544','108.3095','173.0853','467.9949','243.0721','103.2098','67.6318',3,24000000,3,0,0,3,0,'2023-03-21 23:21:00',2067,NULL,NULL,0),
	(535,NULL,'-1753.9785','-701.1611','10.2763','241.6915','-1751.4404','-692.7176','10.092','319.4109',4,40000000,5,0,0,4,0,'2023-03-10 21:50:48',1228,NULL,NULL,0),
	(536,NULL,'1245.1823','-1626.5292','53.2823','214.5427','1235.5038','-1628.5095','51.7702','35.6229',3,24000000,3,0,0,3,0,'2023-03-08 15:00:01',977,NULL,NULL,0),
	(537,NULL,'511.3554','231.8331','108.3096','158.7208','466.9578','239.2884','103.2098','64.4443',3,24000000,3,0,0,3,0,'2023-03-19 02:01:00',1924,NULL,NULL,0),
	(538,NULL,'504.9356','234.2761','108.3096','159.9834','465.5448','235.265','103.2097','66.045',3,24000000,3,0,0,3,0,'2023-03-21 00:12:52',2029,NULL,NULL,0),
	(539,NULL,'1214.3315','-1644.144','48.646','216.1544','1160.3176','-1645.3802','36.9196','206.2617',3,24000000,3,0,0,3,0,'2023-03-16 18:01:00',1796,NULL,NULL,0),
	(540,NULL,'496.5931','237.2924','108.3096','158.422','464.1612','231.0183','103.2097','65.7895',3,24000000,3,0,0,3,0,'2023-03-19 01:39:00',1919,NULL,NULL,0),
	(541,NULL,'489.8251','227.0278','108.3095','254.399','455.769','252.9001','103.209','248.3238',3,24000000,3,0,0,3,0,'2023-03-23 23:21:32',2190,NULL,NULL,0),
	(542,NULL,'-1754.614','-708.5925','10.4005','51.7962','-1742.838','-700.992','10.1286','317.6871',4,40000000,5,0,0,4,0,'2023-03-13 14:59:28',1570,NULL,NULL,0),
	(543,NULL,'443.0641','-1707.1528','29.7088','50.3392','439.9204','-1697.1178','29.2876','43.2032',3,24000000,3,0,0,3,0,'2023-03-16 21:35:11',1803,NULL,NULL,0),
	(544,NULL,'487.6493','220.7412','108.3096','247.662','457.3959','256.8723','103.209','253.9904',3,24000000,3,0,0,3,0,'2023-03-16 00:21:00',1768,NULL,NULL,0),
	(545,NULL,'430.7922','-1725.3188','29.6014','48.5416','429.3607','-1715.2109','29.2797','53.0719',3,24000000,3,0,0,3,0,'2023-03-14 02:28:20',1686,NULL,NULL,0),
	(546,NULL,'-1793.5577','-664.0182','10.6047','137.3559','-1784.2423','-660.2767','10.4254','321.9949',5,70000000,6,0,0,5,0,'2023-03-27 21:31:38',2629,NULL,NULL,0),
	(547,NULL,'1193.418','-1656.233','43.0265','212.8215','1166.9631','-1643.6151','36.9195','171.8653',3,24000000,3,0,0,3,0,'2023-03-04 19:11:01',637,NULL,NULL,0),
	(548,NULL,'484.6671','213.0117','108.3095','249.754','441.8255','245.2014','103.2097','69.5111',3,24000000,3,0,0,3,0,'2023-02-26 02:51:00',558,NULL,NULL,0),
	(549,NULL,'417.8514','-1735.6765','29.6077','52.8519','421.5553','-1727.4211','29.247','52.5145',3,24000000,3,0,0,3,0,'2023-03-03 22:08:00',582,NULL,NULL,0),
	(550,NULL,'482.1146','205.868','108.3095','252.249','442.3411','249.12','103.2098','64.0178',3,24000000,3,0,0,3,0,'2023-03-17 22:01:00',1857,NULL,NULL,0),
	(551,NULL,'405.3243','-1751.1361','29.7103','91.1226','398.4536','-1752.7587','29.2844','53.8471',3,24000000,3,0,0,3,0,'2023-03-17 02:11:00',1817,NULL,NULL,0),
	(552,NULL,'485.7722','201.864','108.3095','343.4385','443.945','253.0304','103.2098','64.4141',3,24000000,3,0,0,3,0,'2023-03-27 05:46:14',2528,NULL,NULL,0),
	(553,NULL,'1230.6918','-1590.9109','53.7661','30.3312','1227.2941','-1604.0106','51.825','212.0692',3,24000000,3,0,0,3,0,'2023-03-13 16:11:00',1593,NULL,NULL,0),
	(554,NULL,'1210.5753','-1607.0452','50.7265','31.1693','1156.1354','-1662.3844','36.6141','309.9535',3,24000000,3,0,0,3,0,'2023-03-27 04:01:00',2525,NULL,NULL,0),
	(555,NULL,'472.8029','-1775.1627','29.0693','264.181','481.5819','-1777.8126','28.544','286.5646',3,24000000,3,0,0,3,0,'2023-03-19 01:18:08',1920,NULL,NULL,0),
	(556,NULL,'475.5191','-1757.7671','28.8903','165.4382','487.9109','-1759.3395','28.4048','167.8781',3,24000000,3,0,0,3,0,'2023-03-12 01:01:01',1363,NULL,NULL,0),
	(557,NULL,'480.1582','-1737.3656','29.151','267.4313','474.886','-1744.3083','28.9276','247.5229',3,24000000,3,0,0,3,0,'2023-03-07 01:32:46',840,NULL,NULL,0),
	(558,NULL,'1193.3396','-1622.4459','45.2215','340.0552','1151.7274','-1654.1385','36.5194','302.3977',3,24000000,3,0,0,3,0,'2023-03-14 17:47:50',1708,NULL,NULL,0),
	(559,NULL,'490.1013','-1714.2158','29.7067','196.3661','483.1335','-1719.4556','29.3932','244.4244',3,24000000,3,0,0,3,0,'2023-03-19 15:11:00',1940,NULL,NULL,0),
	(560,NULL,'808.6375','-163.8412','75.8799','147.806','798.7432','-170.103','73.1593','153.7246',4,40000000,5,0,0,4,0,'2023-03-13 00:28:23',1506,NULL,NULL,0),
	(561,NULL,'513.4972','-1780.8143','28.9139','108.9858','501.1108','-1780.3596','28.3452','21.4251',3,24000000,3,0,0,3,0,'2023-03-04 02:03:17',601,NULL,NULL,0),
	(562,NULL,'511.8914','-1790.6536','28.9194','107.6315','500.9977','-1790.7242','28.3412','354.0749',3,24000000,3,0,0,3,0,'2023-03-05 16:04:09',691,NULL,NULL,0),
	(563,NULL,'500.6441','-1812.8673','28.8912','344.7553','498.058','-1803.0222','28.4554','53.7278',3,24000000,3,0,0,3,0,'2023-03-03 07:01:00',540,NULL,NULL,0),
	(564,NULL,'798.6943','-158.9022','74.8924','243.687','788.8','-165.9907','73.9722','150.358',4,40000000,5,0,0,4,0,'2023-03-06 18:45:58',797,NULL,NULL,0),
	(565,NULL,'495.5017','-1822.4055','28.8697','29.0832','485.5867','-1813.4005','28.1683','133.7739',3,24000000,3,0,0,3,0,'2023-03-07 12:37:12',857,NULL,NULL,0),
	(566,NULL,'1365.4249','-1721.4661','65.6341','201.4212','1380.1193','-1733.3611','65.6228','206.305',3,24000000,3,0,0,3,0,'2023-03-13 16:52:14',1595,NULL,NULL,0),
	(567,NULL,'366.5985','-1802.7092','29.0923','292.6439','364.6935','-1810.188','29.0749','354.9373',2,24000000,2,0,0,2,0,'2023-03-14 12:25:26',1699,NULL,NULL,0),
	(568,NULL,'1355.139','-1690.6849','60.4912','78.6431','1365.2538','-1699.9956','61.4236','279.0869',3,24000000,3,0,0,3,0,'2023-03-13 06:11:00',1528,NULL,NULL,0),
	(569,NULL,'371.9035','-1791.2413','29.0955','53.6185','358.4529','-1798.6642','28.9482','46.2735',2,12000000,2,0,0,2,0,'2023-03-27 11:24:42',2541,NULL,NULL,0),
	(570,NULL,'378.5695','-1812.689','29.0704','215.4758','367.6487','-1812.3252','29.0797','353.4586',2,12000000,2,0,0,2,0,'2023-03-08 09:41:30',940,NULL,NULL,0),
	(571,NULL,'380.2867','-1813.7405','29.0586','184.6274','370.6282','-1814.8254','29.0906','356.3379',2,12000000,2,0,0,2,0,'2023-03-12 11:02:51',1395,NULL,NULL,0),
	(572,NULL,'405.7017','-1795.4358','29.0627','268.6849','376.1643','-1819.4565','29.168','1.9887',2,12000000,2,0,0,2,0,'2023-03-11 20:51:00',1327,NULL,NULL,0),
	(573,NULL,'398.6465','-1789.0822','29.151','3.5274','373.2428','-1817.1691','29.1184','356.8849',2,12000000,2,0,0,2,0,'2023-03-25 11:10:03',2294,NULL,NULL,0),
	(574,NULL,'1358.093','-1752.423','64.4505','330.4522','1352.1039','-1770.0957','60.4185','286.6161',3,24000000,3,0,0,3,0,'2023-03-26 13:54:00',2415,NULL,NULL,0),
	(575,NULL,'348.6089','-1819.8928','28.8941','354.0893','348.2211','-1809.5492','28.49','45.4306',2,12000000,2,0,0,2,0,'2023-03-18 17:24:12',1897,NULL,NULL,0),
	(576,NULL,'-1804.915','436.9059','128.7076','1.3672','-1790.0292','456.8884','128.3082','280.0943',10,190000000,9,0,0,11,0,'2023-03-11 05:15:18',1259,NULL,NULL,0),
	(577,NULL,'339.0614','-1829.3761','28.3375','315.9059','338.1526','-1820.299','27.9636','52.6331',3,24000000,3,0,0,3,0,'2023-03-23 14:21:00',2144,NULL,NULL,0),
	(578,NULL,'328.7314','-1845.359','27.7481','45.3509','334.9373','-1835.1892','27.3409','45.8517',3,24000000,3,0,0,3,0,'2023-03-09 22:01:00',1118,NULL,NULL,0),
	(579,NULL,'1314.5448','-1733.0536','54.7001','160.432','1331.1068','-1733.8293','56.1041','24.1758',3,24000000,3,0,0,3,0,'2023-03-11 18:01:00',1313,NULL,NULL,0),
	(580,NULL,'320.0322','-1853.8741','27.5109','48.2985','311.3523','-1848.6674','26.8574','49.3966',3,24000000,3,0,0,3,0,'2023-03-26 06:21:00',2390,NULL,NULL,0),
	(581,NULL,'1316.843','-1698.8396','58.2291','13.6377','1325.1304','-1710.4797','55.5533','108.9069',3,24000000,3,0,0,3,0,'2023-03-17 20:27:47',1848,NULL,NULL,0),
	(583,NULL,'-1808.0549','333.4468','89.5672','29.1539','-1791.9073','350.7678','88.5605','30.6979',10,190000000,9,0,0,11,0,'2023-03-05 16:36:17',692,NULL,NULL,0),
	(584,NULL,'282.4488','-1898.9119','27.2676','41.9431','268.5916','-1906.9142','26.4531','48.8018',3,24000000,3,0,0,3,0,'2023-03-06 00:57:03',740,NULL,NULL,0),
	(586,NULL,'1289.1904','-1710.5566','55.4751','24.0655','1304.9788','-1712.0887','54.7328','202.5771',4,40000000,5,0,0,4,0,'2023-03-12 18:45:38',1449,NULL,NULL,0),
	(587,NULL,'269.9961','-1917.0303','26.1803','75.4845','259.0668','-1909.7571','25.7594','43.7538',3,24000000,3,0,0,3,0,'2023-03-22 06:01:00',2076,NULL,NULL,0),
	(588,NULL,'1295.0668','-1739.8184','54.2718','123.548','1299.9237','-1736.3605','53.8792','20.3446',3,24000000,3,0,0,3,0,'2023-02-26 02:48:00',559,NULL,NULL,0),
	(589,NULL,'257.4116','-1927.3215','25.4448','145.2907','255.9458','-1915.4995','25.485','48.7471',3,24000000,3,0,0,3,0,'2023-03-13 00:44:20',1504,NULL,NULL,0),
	(591,NULL,'1274.7963','-1721.1521','54.6808','35.7355','1283.9355','-1728.7738','52.8171','296.903',3,24000000,3,0,0,3,0,'2023-03-27 01:59:40',2512,NULL,NULL,0),
	(592,NULL,'250.2999','-1934.782','24.7017','29.5805','241.5546','-1944.1367','23.3914','46.1765',3,24000000,3,0,0,3,0,'2023-03-08 14:01:00',964,NULL,NULL,0),
	(593,NULL,'1250.7329','-1734.5935','52.032','337.2113','1260.2124','-1741.7555','49.4529','117.0725',3,24000000,3,0,0,3,0,'2023-03-13 21:37:10',1650,NULL,NULL,0),
	(594,NULL,'237.3034','-2046.0732','18.38','211.2527','248.3195','-2044.6094','17.9529','229.3572',3,24000000,3,0,0,3,0,'2023-03-15 19:36:35',1757,NULL,NULL,0),
	(595,NULL,'1259.0775','-1761.7644','49.6582','199.9567','1218.3656','-1770.2809','40.3831','0.4331',3,24000000,3,0,0,3,0,'2023-03-12 19:22:26',1465,NULL,NULL,0),
	(596,NULL,'251.5727','-2029.8403','18.3171','313.399','254.8195','-2041.1405','18.0573','230.2165',3,24000000,3,0,0,3,0,'2023-03-07 16:10:58',873,NULL,NULL,0),
	(597,NULL,'-1540.0043','420.8811','110.014','4.386','-1547.8851','427.7195','109.3767','274.1074',8,120000000,8,0,0,8,0,'2023-03-09 20:16:37',1107,NULL,NULL,0),
	(598,NULL,'-1496.0713','437.1772','112.4979','46.6915','-1507.8534','429.7676','111.065','36.7028',8,120000000,8,0,0,8,0,'2023-03-13 22:07:13',1664,NULL,NULL,0),
	(599,NULL,'256.7492','-2023.8763','19.2671','258.8076','260.8178','-2024.0857','18.7962','232.0519',3,24000000,3,0,0,3,0,'2023-03-17 22:37:23',1858,NULL,NULL,0),
	(600,NULL,'-1453.9159','512.4569','117.7964','9.7313','-1471.8813','513.3936','117.8594','22.3169',10,190000000,9,0,0,11,0,'2023-03-12 21:29:54',1483,NULL,NULL,0),
	(601,NULL,'291.4155','-1981.3623','21.6006','125.2842','291.8874','-1989.8541','20.9184','226.539',3,24000000,3,0,0,3,0,'2023-03-23 19:45:02',2170,NULL,NULL,0),
	(602,NULL,'-1500.3779','522.8556','118.2721','207.3287','-1488.4999','527.4473','118.2722','206.8321',10,190000000,9,0,0,11,0,'2023-03-12 18:18:37',1452,NULL,NULL,0),
	(603,NULL,'296.1164','-1972.4387','22.9001','239.7466','306.2479','-1976.9973','22.5634','229.6148',3,24000000,3,0,0,3,0,'2023-03-21 12:00:01',2039,NULL,NULL,0),
	(604,NULL,'-1452.6587','545.4988','120.7994','253.2624','-1452.5425','534.3252','119.2581','233.9914',10,190000000,9,0,0,11,0,'2023-03-04 12:24:23',611,NULL,NULL,0),
	(605,NULL,'312.403','-1956.3823','24.6167','218.3107','312.3802','-1967.6045','23.534','228.2813',3,24000000,3,0,0,3,0,'2023-03-19 22:53:59',1967,NULL,NULL,0),
	(606,NULL,'324.3784','-1938.0313','25.019','272.5146','322.3192','-1948.535','24.6224','229.6382',3,24000000,3,0,0,3,0,'2023-03-10 22:25:06',1236,NULL,NULL,0),
	(607,NULL,'-1413.5717','462.3755','109.2085','344.8448','-1418.5442','469.1751','109.1254','307.6707',10,190000000,9,0,0,11,0,'2023-03-13 23:56:23',1673,NULL,NULL,0),
	(608,NULL,'368.4411','-1896.7832','25.1785','134.9118','355.4568','-1895.6995','24.8007','229.2141',3,24000000,3,0,0,3,0,'2023-02-28 23:56:04',463,NULL,NULL,0),
	(609,NULL,'385.6051','-1881.9504','26.0316','141.0728','386.3905','-1892.5582','25.1478','225.0603',3,24000000,3,0,0,3,0,'2023-03-17 21:02:48',1852,NULL,NULL,0),
	(610,NULL,'399.619','-1864.9965','26.7164','226.1353','399.2693','-1874.5826','26.2143','223.0436',3,24000000,3,0,0,3,0,'2023-03-13 12:31:29',1559,NULL,NULL,0),
	(611,NULL,'-1405.7675','526.6989','123.8312','91.3836','-1413.9843','538.5656','122.245','105.5141',8,120000000,8,0,0,8,0,'2023-03-06 18:07:50',801,NULL,NULL,0),
	(612,NULL,'413.0167','-1856.2316','27.3232','257.7304','408.29','-1862.7742','26.822','224.0902',3,24000000,3,0,0,3,0,'2023-03-05 09:01:00',672,NULL,NULL,0),
	(613,NULL,'427.5612','-1842.0808','28.4634','188.156','434.0923','-1840.5786','27.9944','222.9404',3,24000000,3,0,0,3,0,'2023-03-17 12:39:00',1823,NULL,NULL,0),
	(614,NULL,'-1405.1117','561.7726','125.4062','176.945','-1412.0652','559.0421','124.4126','112.3438',10,190000000,9,0,0,11,0,'2023-03-04 15:11:44',620,NULL,NULL,0),
	(615,NULL,'440.2127','-1830.7476','28.3619','163.9071','437.5145','-1837.6005','27.9575','225.7009',3,24000000,3,0,0,3,0,'2023-03-27 14:05:54',2555,NULL,NULL,0),
	(616,NULL,'-1367.0004','610.7366','133.8969','287.3824','-1361.9296','605.0306','133.8927','282.8403',6,*********,7,0,0,6,0,'2023-03-02 20:19:54',520,NULL,NULL,0),
	(617,NULL,'279.5453','-2043.3412','19.7676','47.1004','272.7898','-2065.1357','17.1502','105.1643',2,12000000,2,0,0,2,0,'2023-10-22 11:48:12',2707,NULL,NULL,0),
	(618,NULL,'280.8388','-2041.9299','19.7676','47.5177','275.0708','-2060.9211','17.348','113.8594',2,12000000,2,0,0,2,0,'2023-03-23 04:21:00',2126,NULL,NULL,0),
	(619,NULL,'-1337.4286','606.1729','134.3798','89.3779','-1345.5089','610.4756','133.8132','103.776',8,120000000,8,0,0,8,0,'2023-03-03 17:55:31',563,NULL,NULL,0),
	(620,NULL,'286.8369','-2034.6178','19.7676','46.8932','275.8403','-2063.3123','17.2707','113.9164',2,12000000,2,0,0,2,0,'2023-03-25 08:36:41',2285,NULL,NULL,0),
	(621,NULL,'289.9351','-2030.9755','19.7676','47.8738','306.2061','-2023.2333','20.3241','320.0475',2,12000000,2,0,0,2,0,'2023-03-06 20:43:01',813,NULL,NULL,0),
	(622,NULL,'-1277.7163','629.8738','143.1859','130.3118','-1288.1239','625.7677','138.798','39.622',8,120000000,8,0,0,8,0,'2023-03-25 23:45:28',2354,NULL,NULL,0),
	(623,NULL,'298.1737','-2034.2389','19.8357','313.3674','306.3574','-2022.9996','20.3283','317.6077',2,12000000,2,0,0,2,0,'2023-03-14 23:14:00',1724,NULL,NULL,0),
	(624,NULL,'-1291.7094','650.119','141.5014','194.7226','-1283.7164','647.6692','139.5867','203.9508',10,190000000,9,0,0,11,0,'2023-03-13 23:32:34',1676,NULL,NULL,0),
	(625,NULL,'306.3219','-2045.2118','20.9119','51.5847','310.9976','-2070.1707','18.159','138.0215',2,12000000,2,0,0,2,0,'2023-03-08 22:03:39',1019,NULL,NULL,0),
	(626,NULL,'-1248.5688','643.0555','142.6969','303.1725','-1235.0509','654.6402','142.4437','122.4124',10,190000000,9,0,0,11,0,'2023-03-07 17:38:15',878,NULL,NULL,0),
	(627,NULL,'293.7218','-2044.4242','19.64','230.73','310.1809','-2027.7518','20.4913','322.7662',2,12000000,2,0,0,2,0,'2023-03-12 03:48:37',1373,NULL,NULL,0),
	(628,NULL,'291.1787','-2047.6046','19.646','210.9712','307.8579','-2026.0151','20.3916','320.0516',2,12000000,2,0,0,2,0,'2023-03-14 12:21:10',1700,NULL,NULL,0),
	(629,NULL,'286.4622','-2052.7563','19.6465','230.4494','272.2841','-2063.5337','17.1957','137.4293',2,12000000,2,0,0,2,0,'2023-03-15 12:01:00',1742,NULL,NULL,0),
	(630,NULL,'313.8687','-2040.4116','20.9364','318.8972','314.9084','-2031.9919','20.5506','322.3708',2,12000000,2,0,0,2,0,'2023-03-16 18:20:31',1797,NULL,NULL,0),
	(631,NULL,'317.3258','-2043.1497','20.9364','317.0497','317.184','-2033.6611','20.5747','322.8572',2,12000000,2,0,0,2,0,'2023-03-09 14:20:36',1063,NULL,NULL,0),
	(632,NULL,'329.4083','-225.1054','54.2218','172.9325','333.1435','-216.5568','54.0863','71.3699',2,12000000,2,0,0,2,0,'2023-03-03 00:00:01',530,NULL,NULL,0),
	(633,NULL,'324.6705','-2049.4407','20.9364','319.592','319.6137','-2035.6224','20.5937','320.991',2,12000000,2,0,0,2,0,'2023-03-26 07:01:00',2393,NULL,NULL,0),
	(634,NULL,'325.8911','-2050.6311','20.9364','318.7785','321.9171','-2037.4885','20.6081','322.2098',2,12000000,2,0,0,2,0,'2023-03-03 14:00:01',549,NULL,NULL,0),
	(635,NULL,'331.4235','-225.8675','54.2218','154.3019','334.2241','-213.5514','54.0863','70.379',2,12000000,2,0,0,2,0,'2023-03-09 04:00:01',1042,NULL,NULL,0),
	(636,NULL,'-1241.3948','674.2898','142.8121','177.6873','-1245.2367','665.8963','142.7422','206.0591',8,120000000,8,0,0,8,0,'2023-03-12 18:57:23',1456,NULL,NULL,0),
	(637,NULL,'333.2015','-2056.7937','20.9364','319.0417','323.9636','-2039.6115','20.6478','322.8344',2,12000000,2,0,0,2,0,'2023-03-20 14:57:40',1997,NULL,NULL,0),
	(638,NULL,'334.616','-2058.0591','20.9364','319.0266','326.0769','-2041.338','20.7049','321.2455',2,12000000,2,0,0,2,0,'2023-03-19 01:01:01',1921,NULL,NULL,0),
	(639,NULL,'342.024','-2064.0254','20.9364','316.3396','328.4843','-2043.4926','20.7439','319.2541',2,12000000,2,0,0,2,0,'2023-03-04 01:11:00',595,NULL,NULL,0),
	(640,NULL,'346.0336','-2067.2075','20.9364','316.5704','331.0735','-2045.463','20.758','324.8433',2,12000000,2,0,0,2,0,'2023-03-25 20:03:54',2334,NULL,NULL,0),
	(641,NULL,'356.3937','-2074.6292','21.7444','344.4276','324.4681','-2083.0186','17.944','133.8752',2,12000000,2,0,0,2,0,'2023-03-04 18:08:59',630,NULL,NULL,0),
	(642,NULL,'357.6246','-2072.9727','21.7445','45.7698','327.7926','-2084.6858','17.96','122.7338',2,12000000,2,0,0,2,0,'2023-03-13 18:19:03',1612,NULL,NULL,0),
	(643,NULL,'365.1563','-2064.5149','21.7445','48.5584','331.9995','-2044.3215','20.8285','320.7716',2,12000000,2,0,0,2,0,'2023-03-13 19:01:00',1620,NULL,NULL,0),
	(644,NULL,'-1218.809','665.371','144.534','45.0302','-1224.6554','663.4688','143.4969','49.2206',6,*********,7,0,0,6,0,'2023-03-05 00:19:02',658,NULL,NULL,0),
	(645,NULL,'371.1009','-2057.1843','21.7445','58.9834','412.6902','-2065.9226','21.4648','136.8618',2,12000000,2,0,0,2,0,'2023-03-14 17:01:00',1709,NULL,NULL,0),
	(646,NULL,'-333.1718','56.8861','54.4298','176.8751','-371.5293','39.7463','51.0116','88.8983',2,12000000,2,0,0,2,0,'2023-03-11 02:30:05',1257,NULL,NULL,0),
	(647,NULL,'372.3043','-2055.9119','21.7445','76.7078','409.6215','-2064.3818','21.4374','136.5653',2,12000000,2,0,0,2,0,'2023-03-17 16:01:00',1837,NULL,NULL,0),
	(648,NULL,'-344.7236','57.3255','54.4349','176.8713','-371.5293','39.7463','51.0116','88.8983',2,12000000,2,0,0,2,0,'2023-03-06 10:01:00',763,NULL,NULL,0),
	(649,NULL,'-1196.772','693.2865','147.4262','63.6219','-1200.4751','691.6716','147.1015','143.2459',6,*********,7,0,0,6,0,'2023-03-05 16:07:23',694,NULL,NULL,0),
	(650,NULL,'337.0171','-224.7285','54.2218','257.366','335.5223','-210.0818','54.0863','70.1843',2,12000000,2,0,0,2,0,'2023-02-28 18:21:00',464,NULL,NULL,0),
	(651,NULL,'-350.5703','57.3585','54.4354','178.8072','-371.5293','39.7463','51.0116','88.8983',2,12000000,2,0,0,2,0,'2023-03-21 16:26:00',2054,NULL,NULL,0),
	(652,NULL,'342.2751','-2075.448','20.9387','219.9406','384.3729','-2086.7236','19.382','321.7039',2,12000000,2,0,0,2,0,'2023-03-04 20:11:00',644,NULL,NULL,0),
	(653,NULL,'338.9269','-219.4115','54.2218','252.8125','336.533','-207.1217','54.0863','69.4719',2,12000000,2,0,0,2,0,'2023-03-10 18:06:12',1202,NULL,NULL,0),
	(654,NULL,'-362.3371','57.4886','54.4298','183.3329','-371.5293','39.7463','51.0116','88.8983',2,12000000,2,0,0,2,0,'2023-03-26 11:01:00',2403,NULL,NULL,0),
	(655,NULL,'340.8438','-214.8175','54.2218','249.7315','334.2292','-213.5236','54.0863','76.2163',2,12000000,2,0,0,2,0,'2023-03-07 14:01:00',864,NULL,NULL,0),
	(656,NULL,'332.3175','-2070.7781','20.9364','138.1274','282.1079','-2078.1172','16.9563','108.3579',2,12000000,2,0,0,2,0,'2023-03-08 18:43:15',993,NULL,NULL,0),
	(657,NULL,'-1165.5922','726.9581','155.6068','320.4288','-1156.4292','744.2078','155.4923','57.7906',10,190000000,9,0,0,11,0,'2023-03-26 00:58:19',2366,NULL,NULL,0),
	(658,NULL,'342.7961','-209.5633','54.2218','266.7559','335.5123','-210.0375','54.0863','68.6153',2,12000000,2,0,0,2,0,'2023-03-24 13:33:46',2215,NULL,NULL,0),
	(659,NULL,'-1130.5234','784.2872','163.8875','239.8712','-1121.5776','789.5601','163.186','209.8472',10,190000000,9,0,0,11,0,'2023-03-06 16:16:42',783,NULL,NULL,0),
	(660,NULL,'-1117.8301','761.6365','164.2887','25.7081','-1115.4147','770.9163','163.1625','22.4231',10,190000000,9,0,0,11,0,'2023-03-13 00:01:07',1507,NULL,NULL,0),
	(661,NULL,'-371.2529','23.235','47.8589','353.63','-357.6991','26.6826','47.7073','80.057',2,12000000,2,0,0,2,0,'2023-03-07 20:06:39',899,NULL,NULL,0),
	(662,NULL,'323.9329','-2063.7148','20.7005','141.8369','280.7316','-2075.5156','16.987','111.6632',2,12000000,2,0,0,2,0,'2023-03-07 19:33:53',891,NULL,NULL,0),
	(663,NULL,'-360.3818','21.2874','47.859','3.6218','-357.1808','29.7623','47.7823','76.6831',2,12000000,2,0,0,2,0,'2023-03-06 15:33:02',774,NULL,NULL,0),
	(664,NULL,'-1065.1403','772.5166','170.0034','6.2654','-1048.0437','768.5334','167.4159','4.4167',10,*********,9,0,0,11,0,'2023-02-28 22:04:32',449,NULL,NULL,0),
	(665,NULL,'320.9705','-2061.032','20.7132','141.4234','279.4864','-2073.0366','17.0285','114.5567',2,12000000,2,0,0,2,0,'2023-03-12 16:13:06',1427,NULL,NULL,0),
	(666,NULL,'-345.1328','18.0949','47.859','350.1248','-356.5264','33.0541','47.8066','81.0188',2,12000000,2,0,0,2,0,'2023-03-26 21:00:01',2488,NULL,NULL,0),
	(667,NULL,'315.4112','-2056.8813','20.7059','137.5307','278.6069','-2070.0059','17.1189','110.3732',2,12000000,2,0,0,2,0,'2023-03-11 18:40:51',1314,NULL,NULL,0),
	(668,NULL,'-1100.5668','797.4705','167.2561','193.0249','-1107.5195','793.3618','164.9755','189.21',10,190000000,9,0,0,11,0,'2023-03-23 00:09:14',2118,NULL,NULL,0),
	(669,NULL,'-339.1544','21.5097','47.859','74.6645','-355.6962','37.7015','47.9777','79.3038',2,12000000,2,0,0,2,0,'2023-03-14 09:01:00',1693,NULL,NULL,0),
	(670,NULL,'312.4148','-2054.415','20.9387','139.8526','277.5088','-2067.061','17.2002','112.9467',2,12000000,2,0,0,2,0,'2023-03-08 05:01:00',934,NULL,NULL,0),
	(671,NULL,'-336.6662','30.9811','47.859','81.9378','-357.5314','26.6096','47.7088','85.1963',2,12000000,2,0,0,2,0,'2023-03-27 23:20:38',2660,NULL,NULL,0),
	(672,NULL,'-1067.6122','795.4904','166.9604','184.4785','-1040.4579','790.5826','167.6759','273.0997',10,252000000,9,0,0,11,0,'2023-03-06 03:06:00',750,NULL,NULL,0),
	(673,NULL,'306.0676','-2045.1836','20.9122','44.8457','312.9974','-2029.2426','20.5213','315.9983',2,12000000,2,0,0,2,0,'2023-03-07 05:01:00',847,NULL,NULL,0),
	(674,NULL,'295.1959','-2066.9619','17.6554','330.9476','285.8069','-2089.4685','16.8745','109.1888',2,12000000,2,0,0,2,0,'2023-03-06 14:23:36',771,NULL,NULL,0),
	(675,NULL,'301.8592','-2076.2263','17.6671','313.023','284.8359','-2086.5891','16.874','111.1853',2,12000000,2,0,0,2,0,'2023-03-13 15:54:10',1586,NULL,NULL,0),
	(676,NULL,'344.5641','-204.7667','54.2218','219.0209','328.1387','-204.0181','54.0863','154.6647',2,12000000,2,0,0,2,0,'2023-03-23 14:01:00',2145,NULL,NULL,0),
	(677,NULL,'-205.7571','-7.7997','56.6229','71.4023','-227.1055','9.4251','53.1988','74.5906',2,12000000,2,0,0,2,0,'2023-03-21 13:04:01',2044,NULL,NULL,0),
	(678,NULL,'303.1867','-2079.7483','17.6466','266.9496','273.9609','-2067.8142','17.0763','112.3667',2,12000000,2,0,0,2,0,'2023-03-08 14:23:12',965,NULL,NULL,0),
	(679,NULL,'-998.4254','768.7338','171.5827','288.4628','-997.4091','787.8671','171.9697','336.2294',10,190000000,9,0,0,11,0,'2023-03-04 02:35:11',599,NULL,NULL,0),
	(680,NULL,'346.6896','-199.715','54.2218','235.1003','327.5255','-204.7023','54.0863','156.6701',2,12000000,2,0,0,2,0,'2023-03-08 13:32:04',950,NULL,NULL,0),
	(681,NULL,'305.2699','-2086.573','17.709','291.259','274.9691','-2070.6431','16.9963','108.5563',2,12000000,2,0,0,2,0,'2023-03-26 02:50:00',2375,NULL,NULL,0),
	(682,NULL,'-205.6726','-7.6892','60.6271','62.9328','-227.8867','6.7025','52.6588','83.0284',2,12000000,2,0,0,2,0,'2024-02-25 16:30:08',2711,NULL,NULL,0),
	(683,NULL,'306.4424','-2097.4187','17.5325','244.6033','276.7362','-2073.5457','16.932','106.0486',2,12000000,2,0,0,2,0,'2023-03-17 15:01:00',1831,NULL,NULL,0),
	(684,NULL,'321.3458','-197.1023','54.2265','345.6826','315.8396','-209.8029','54.0863','250.4133',2,12000000,2,0,0,2,0,'2023-03-10 19:11:00',1210,NULL,NULL,0),
	(685,NULL,'319.2693','-196.3382','54.2265','353.4518','315.8396','-209.8029','54.0863','250.4133',2,12000000,2,0,0,2,0,'2023-03-22 22:44:33',2112,NULL,NULL,0),
	(686,NULL,'-972.4813','752.5317','176.3806','48.6787','-977.6982','775.6428','174.315','44.0389',6,128000000,7,0,0,6,0,'2023-03-10 15:00:35',1178,NULL,NULL,0),
	(687,NULL,'-999.348','816.6772','173.0497','225.0553','-1020.1548','806.7084','171.5921','209.0075',10,240000000,9,0,0,11,0,'2023-03-01 00:14:39',465,NULL,NULL,0),
	(688,NULL,'-161.3569','-4.3288','62.4628','245.9308','-135.3167','-31.4772','58.1023','342.0704',2,12000000,2,0,0,2,0,'2023-03-26 22:23:02',2497,NULL,NULL,0),
	(689,NULL,'-962.749','814.1035','177.5652','183.2988','-955.5913','800.7719','177.8684','180.9756',10,240000000,9,0,0,11,0,'2023-03-01 22:53:18',490,NULL,NULL,0),
	(690,NULL,'-161.3382','-4.3546','66.4664','249.2859','-132.4317','-32.3604','58.1426','340.9166',2,12000000,2,0,0,2,0,'2023-03-10 23:16:27',1242,NULL,NULL,0),
	(691,NULL,'-931.8298','808.8797','184.7809','186.4967','-918.2894','802.111','184.2851','184.9927',10,260000000,9,0,0,11,0,'2023-03-11 18:32:58',1315,NULL,NULL,0),
	(692,NULL,'-912.1781','777.2812','187.0105','5.759','-904.7955','783.8187','185.9843','10.3917',10,190000000,9,0,0,11,0,'2023-03-04 02:49:10',602,NULL,NULL,0),
	(693,NULL,'-867.3561','785.1149','191.9343','7.1629','-851.2596','797.6559','192.8008','4.1165',10,190000000,9,0,0,11,0,'2023-03-10 20:25:02',1218,NULL,NULL,0),
	(694,NULL,'-116.6046','-37.6709','62.1959','69.1411','-81.9144','-50.3223','61.3798','263.2612',2,12000000,2,0,0,2,0,'2023-03-08 19:01:00',1004,NULL,NULL,0),
	(695,NULL,'-824.0881','806.1424','202.7845','24.7879','-811.4155','807.8079','202.1255','39.3172',10,190000000,9,0,0,11,0,'2023-03-27 21:42:00',2633,NULL,NULL,0),
	(696,NULL,'297.0407','-2097.8762','17.6636','110.74','283.6361','-2083.6904','16.8497','107.8606',2,12000000,2,0,0,2,0,'2023-03-05 13:58:12',678,NULL,NULL,0),
	(697,NULL,'-747.1846','808.2939','215.0303','286.6609','-747.0891','816.2007','213.4009','0.5464',10,240000000,9,0,0,11,0,'2023-03-08 22:01:52',1017,NULL,NULL,0),
	(698,NULL,'295.5574','-2093.095','17.6636','107.1196','282.297','-2080.5928','16.8796','113.6697',2,12000000,2,0,0,2,0,'2023-03-15 06:08:32',1739,NULL,NULL,0),
	(699,NULL,'315.7711','-194.944','54.2264','23.0037','315.8396','-209.8029','54.0863','250.4133',2,12000000,2,0,0,2,0,'2023-03-24 18:01:00',2240,NULL,NULL,0),
	(700,NULL,'-102.0338','-31.9015','66.444','248.8214','-80.8196','-45.4709','62.0376','255.4348',2,12000000,2,0,0,2,0,'2023-03-26 00:01:00',2367,NULL,NULL,0),
	(701,NULL,'313.5865','-198.2674','54.2218','67.763','316.7726','-206.5068','54.0863','251.3333',2,12000000,2,0,0,2,0,'2023-03-11 12:24:36',1271,NULL,NULL,0),
	(702,NULL,'-102.1918','-32.0182','70.4476','256.1612','-81.3897','-47.8929','61.7084','166.9988',2,12000000,2,0,0,2,0,'2023-03-11 11:11:00',1268,NULL,NULL,0),
	(703,NULL,'311.5977','-203.5435','54.2218','72.4725','316.7717','-206.5056','54.0863','251.3333',2,12000000,2,0,0,2,0,'2023-03-07 12:24:38',858,NULL,NULL,0),
	(704,NULL,'309.5493','-208.0879','54.2218','126.5811','316.7717','-206.5056','54.0863','251.3333',2,12000000,2,0,0,2,0,'2023-03-13 13:30:00',1568,NULL,NULL,0),
	(705,NULL,'293.2458','-2088.1223','17.6636','108.2927','282.077','-2077.7834','16.9606','106.276',2,12000000,2,0,0,2,0,'2023-03-13 04:23:32',1525,NULL,NULL,0),
	(706,NULL,'307.6783','-213.3504','54.2218','38.9365','318.2441','-203.2691','54.0863','250.044',2,12000000,2,0,0,2,0,'2023-03-20 12:46:45',1990,NULL,NULL,0),
	(707,NULL,'292.9721','-2086.0417','17.6636','113.7822','280.3493','-2075.2437','16.9803','108.9784',2,12000000,2,0,0,2,0,'2023-03-11 18:12:01',1316,NULL,NULL,0),
	(708,NULL,'-658.4691','886.3705','229.3042','9.0283','-672.6192','912.5283','230.569','251.8997',10,400000000,9,0,0,11,0,'2023-03-07 18:02:38',883,NULL,NULL,0),
	(709,NULL,'307.3977','-216.3595','54.2218','158.0775','318.2452','-203.2707','54.0863','250.044',2,12000000,2,0,0,2,0,'2023-03-18 05:00:34',1871,NULL,NULL,0),
	(710,NULL,'289.5951','-2077.2817','17.6635','27.4453','279.3016','-2072.6489','17.0361','114.107',2,12000000,2,0,0,2,0,'2023-03-15 01:01:00',1729,NULL,NULL,0),
	(711,NULL,'311.0788','-218.0017','54.2218','224.1916','318.2441','-203.2691','54.0863','250.044',2,12000000,2,0,0,2,0,'2023-03-09 19:11:00',1100,NULL,NULL,0),
	(712,NULL,'312.7962','-218.5172','54.2218','195.8352','319.6584','-200.0249','54.0863','251.0414',2,12000000,2,0,0,2,0,'2023-03-12 17:43:54',1440,NULL,NULL,0),
	(713,NULL,'-597.1252','851.7394','211.4441','58.1654','-610.5597','866.4179','213.6109','323.2936',10,190000000,9,0,0,11,0,'2023-03-27 02:17:45',2516,NULL,NULL,0),
	(714,NULL,'288.0949','-2072.4749','17.6636','120.1771','278.3006','-2069.4492','17.1316','109.0074',2,12000000,2,0,0,2,0,'2023-03-09 14:10:08',1064,NULL,NULL,0),
	(715,NULL,'-27.7742','-60.7749','63.7097','251.8956','-49.0086','-44.3778','63.4585','71.5069',2,12000000,2,0,0,2,0,'2023-03-09 20:01:00',1106,NULL,NULL,0),
	(716,NULL,'-536.7422','818.162','197.6993','339.7851','-552.3572','831.0078','197.8668','338.9924',8,120000000,8,0,0,8,0,'2023-03-13 22:03:04',1666,NULL,NULL,0),
	(717,NULL,'329.5367','-224.9414','58.0193','163.7968','333.1115','-216.6641','54.0863','68.007',2,12000000,2,0,0,2,0,'2023-03-12 18:01:00',1455,NULL,NULL,0),
	(718,NULL,'-25.3621','-53.0844','67.5921','243.62','-49.3638','-47.2719','62.9525','69.2124',2,12000000,2,0,0,2,0,'2023-03-16 21:01:00',1806,NULL,NULL,0),
	(719,NULL,'331.4465','-225.7652','58.0193','159.5683','334.4457','-213.5732','54.0863','73.9666',2,12000000,2,0,0,2,0,'2023-03-25 23:01:00',2357,NULL,NULL,0),
	(720,NULL,'-494.3022','796.1111','184.3439','71.1795','-481.2998','801.7681','181.5536','209.2086',8,120000000,8,0,0,8,0,'2023-03-04 20:52:13',640,NULL,NULL,0),
	(721,NULL,'335.0713','-227.0651','58.0192','168.3573','335.2756','-210.0347','54.0863','73.2054',2,12000000,2,0,0,2,0,'2023-03-21 17:01:00',2055,NULL,NULL,0),
	(722,NULL,'-27.8024','-60.7876','67.5922','243.2857','-50.1193','-50.3207','62.5496','76.5818',2,12000000,2,0,0,2,0,'2023-03-04 22:01:00',651,NULL,NULL,0),
	(723,NULL,'336.7273','-224.663','58.0192','254.1133','336.4688','-207.0544','54.0863','72.2128',2,12000000,2,0,0,2,0,'2023-03-24 13:01:00',2216,NULL,NULL,0),
	(724,NULL,'338.8309','-219.4724','58.0192','278.0132','336.4688','-207.0544','54.0863','72.2128',2,12000000,2,0,0,2,0,'2023-03-23 13:37:52',2139,NULL,NULL,0),
	(725,NULL,'319.9323','-2100.687','18.2441','29.5875','281.0413','-2084.4324','16.6919','107.3861',2,12000000,2,0,0,2,0,'2023-03-12 16:20:38',1428,NULL,NULL,0),
	(726,NULL,'340.7166','-214.8354','58.0192','184.7086','335.4509','-210.3342','54.0863','16.0428',2,12000000,2,0,0,2,0,'2023-03-25 18:01:00',2323,NULL,NULL,0),
	(727,NULL,'-495.4698','738.6959','163.031','327.8881','-492.9702','744.7925','162.8358','246.2669',8,120000000,8,0,0,8,0,'2023-03-07 04:48:59',845,NULL,NULL,0),
	(728,NULL,'-18.4449','-68.5798','61.3754','73.2212','7.1395','-71.4742','61.0497','251.3198',2,12000000,2,0,0,2,0,'2023-03-20 15:11:00',2000,NULL,NULL,0),
	(729,NULL,'321.7628','-2099.7092','18.2441','27.3354','279.5382','-2081.5076','16.7196','110.4703',2,12000000,2,0,0,2,0,'2023-03-27 17:01:00',2580,NULL,NULL,0),
	(730,NULL,'342.6737','-209.5785','58.0192','321.4666','334.5255','-213.6368','54.0863','75.0802',2,12000000,2,0,0,2,0,'2023-03-19 18:01:00',1950,NULL,NULL,0),
	(731,NULL,'344.6246','-204.9667','58.0192','282.5273','333.2927','-216.6203','54.0863','75.9489',2,12000000,2,0,0,2,0,'2023-03-09 16:55:04',1080,NULL,NULL,0),
	(732,NULL,'330.0689','-2095.1558','18.2441','28.4279','278.5896','-2078.6597','16.79','105.1434',2,12000000,2,0,0,2,0,'2023-03-18 19:01:00',1905,NULL,NULL,0),
	(733,NULL,'-16.3021','-60.8872','61.375','76.4416','8.217','-67.6159','61.5967','245.2787',2,12000000,2,0,0,2,0,'2023-03-21 21:01:01',2065,NULL,NULL,0),
	(734,NULL,'346.5658','-199.6852','58.0192','293.9498','333.2927','-216.6203','54.0863','75.9489',2,12000000,2,0,0,2,0,'2023-03-17 22:15:13',1859,NULL,NULL,0),
	(735,NULL,'334.183','-2092.6587','18.2441','21.048','277.9365','-2076.1084','16.8706','103.5727',2,12000000,2,0,0,2,0,'2023-03-18 20:01:01',1910,NULL,NULL,0),
	(736,NULL,'-533.4236','709.22','153.1053','188.6252','-520.239','711.3892','152.4778','134.7285',10,190000000,9,0,0,11,0,'2023-03-18 20:31:16',1911,NULL,NULL,0),
	(737,NULL,'340.9049','-2098.4905','18.205','329.9841','285.7553','-2093.6814','16.7732','39.1624',2,12000000,2,0,0,2,0,'2023-03-13 19:54:26',1621,NULL,NULL,0),
	(738,NULL,'312.8047','-218.7287','58.0193','199.8613','319.6584','-200.0249','54.0863','251.0414',2,12000000,2,0,0,2,0,'2023-03-27 03:01:00',2520,NULL,NULL,0),
	(739,NULL,'332.8176','-2106.3711','18.1223','207.4024','284.9759','-2088.1077','16.8528','106.3871',2,12000000,2,0,0,2,0,'2023-03-01 02:01:36',466,NULL,NULL,0),
	(740,NULL,'310.9754','-217.8728','58.0193','130.857','319.6584','-200.0249','54.0863','251.0414',2,12000000,2,0,0,2,0,'2023-03-08 19:01:00',1005,NULL,NULL,0),
	(741,NULL,'329.7352','-2108.7996','17.9112','183.2193','284.1095','-2085.0569','16.8446','108.4534',2,12000000,2,0,0,2,0,'2023-03-25 21:01:00',2345,NULL,NULL,0),
	(742,NULL,'307.2246','-216.622','58.0192','157.0969','319.6584','-200.0249','54.0863','251.0414',2,12000000,2,0,0,2,0,'2023-03-25 08:15:56',2286,NULL,NULL,0),
	(743,NULL,'307.6566','-213.4657','58.0192','64.8032','318.2298','-203.245','54.0863','247.8739',2,12000000,2,0,0,2,0,'2023-03-12 18:44:52',1457,NULL,NULL,0),
	(744,NULL,'323.4988','-2112.053','18.1222','177.2981','283.0347','-2082.0996','16.8699','109.4861',2,12000000,2,0,0,2,0,'2023-03-07 19:18:01',892,NULL,NULL,0),
	(745,NULL,'309.7067','-208.0399','58.0192','84.1543','317.0778','-206.3788','54.0863','251.1377',2,12000000,2,0,0,2,0,'2023-03-26 20:01:01',2475,NULL,NULL,0),
	(746,NULL,'311.4772','-203.4715','58.0192','84.502','315.9577','-209.7285','54.0863','247.6703',2,12000000,2,0,0,2,0,'2023-03-27 20:11:00',2619,NULL,NULL,0),
	(747,NULL,'-499.1531','683.1296','151.8558','176.5014','-520.8369','697.4777','150.1429','135.4292',10,190000000,9,0,0,11,0,'2023-03-13 21:18:50',1643,NULL,NULL,0),
	(748,NULL,'43.8582','-37.2685','73.6835','347.7773','55.1228','-46.4893','69.3944','160.4589',2,12000000,2,0,0,2,0,'2023-03-12 20:20:07',1473,NULL,NULL,0),
	(749,NULL,'313.5057','-198.2058','58.0192','57.0063','316.8969','-206.5139','54.0863','266.2639',2,12000000,2,0,0,2,0,'2023-03-27 19:45:44',2600,NULL,NULL,0),
	(750,NULL,'315.6965','-194.823','58.0192','340.038','317.8426','-203.1583','54.0863','246.9209',2,12000000,2,0,0,2,0,'2023-03-18 12:38:41',1878,NULL,NULL,0),
	(751,NULL,'46.8817','-31.0497','73.6835','62.7355','51.3308','-45.2911','69.3944','162.0317',2,12000000,2,0,0,2,0,'2023-03-14 12:50:52',1701,NULL,NULL,0),
	(752,NULL,'319.2837','-196.3221','58.0192','195.904','319.5679','-200.2126','54.0863','249.049',2,12000000,2,0,0,2,0,'2023-03-01 17:01:00',467,NULL,NULL,0),
	(753,NULL,'47.3944','-28.8673','73.6835','70.3046','47.1718','-43.5119','69.3944','163',2,12000000,2,0,0,2,0,'2023-03-09 15:53:59',1073,NULL,NULL,0),
	(754,NULL,'-564.3574','684.5029','146.4136','166.2827','-559.6379','686.0991','145.4154','171.9937',10,190000000,9,0,0,11,0,'2023-03-05 18:04:37',701,NULL,NULL,0),
	(755,NULL,'321.3155','-197.0958','58.0192','152.3756','319.5674','-200.2128','54.0863','249.049',2,12000000,2,0,0,2,0,'2023-03-13 07:01:00',1530,NULL,NULL,0),
	(756,NULL,'364.2309','-2045.8269','22.3543','136.9425','339.0148','-2036.9512','21.4063','52.2761',2,12000000,2,0,0,2,0,'2023-03-02 22:01:00',522,NULL,NULL,0),
	(757,NULL,'44.8835','-29.895','69.3947','67.3495','55.3058','-46.2841','69.3944','152.3721',2,12000000,2,0,0,2,0,'2023-03-26 00:01:00',2369,NULL,NULL,0),
	(758,NULL,'360.5333','-2042.5897','22.3543','152.8426','334.8733','-2041.7708','21.0413','53.929',2,12000000,2,0,0,2,0,'2023-03-26 02:11:00',2376,NULL,NULL,0),
	(759,NULL,'353.1452','-2036.8309','22.3543','124.4434','336.9648','-2039.505','21.2054','49.5516',2,12000000,2,0,0,2,0,'2023-03-04 19:11:01',638,NULL,NULL,0),
	(760,NULL,'-559.5154','664.0942','145.4568','342.0409','-555.1391','666.1263','144.8526','339.8875',6,*********,7,0,0,6,0,'2023-07-21 02:59:22',2689,NULL,NULL,1),
	(761,NULL,'351.834','-2035.3584','22.3543','137.9238','335.5082','-2032.8845','21.3546','143.104',2,12000000,2,0,0,2,0,'2023-03-22 21:01:00',2109,NULL,NULL,0),
	(762,NULL,'344.4073','-2029.2074','22.3543','136.4926','333.3747','-2030.6913','21.2939','141.0264',2,12000000,2,0,0,2,0,'2023-03-21 20:26:02',2060,NULL,NULL,0),
	(763,NULL,'343.2765','-2028.0273','22.3543','121.8595','330.6364','-2028.9659','21.1938','146.4212',2,12000000,2,0,0,2,0,'2023-03-20 13:02:01',1993,NULL,NULL,0),
	(764,NULL,'335.9088','-2021.7903','22.3543','136.5071','328.6299','-2026.7434','21.1487','142.0149',2,12000000,2,0,0,2,0,'2023-03-12 04:01:00',1375,NULL,NULL,0),
	(765,NULL,'51.3886','-74.3311','63.6306','250.9737','34.6359','-70.2972','63.1139','63.3135',2,12000000,2,0,0,2,0,'2023-03-27 01:01:00',2513,NULL,NULL,0),
	(766,NULL,'332.2443','-2018.7539','22.3543','135.7653','326.4666','-2024.7831','21.0838','140.4996',2,12000000,2,0,0,2,0,'2023-03-17 14:21:00',1827,NULL,NULL,0),
	(767,NULL,'54.135','-66.7404','67.6308','253.8071','36.0182','-66.5706','63.3348','64.1811',2,12000000,2,0,0,2,0,'2023-03-07 21:08:02',903,NULL,NULL,0),
	(768,NULL,'335.5304','-2010.5791','22.3131','68.0517','37.568','-62.663','63.4778','71.8788',2,12000000,2,0,0,2,0,'2023-03-08 10:11:00',942,NULL,NULL,0),
	(769,NULL,'51.4617','-74.0775','67.6307','248.6425','23.2205','-69.6441','61.8367','339.5616',2,12000000,2,0,0,2,0,'2023-03-03 02:01:00',537,NULL,NULL,0),
	(770,NULL,'330.6212','-2000.0956','24.0461','227.6436','324.1825','-2022.5999','20.9859','144.696',2,12000000,2,0,0,2,0,'2023-03-08 16:01:00',984,NULL,NULL,0),
	(771,NULL,'335.1882','-1994.8641','24.0461','231.4593','321.7723','-2020.91','20.8734','147.3537',2,12000000,2,0,0,2,0,'2023-03-03 16:03:36',555,NULL,NULL,0),
	(772,NULL,'-476.903','647.9921','144.3866','14.5992','-461.3668','641.255','144.1886','50.3627',10,*********,9,0,0,11,0,'2023-03-13 23:26:21',1680,NULL,NULL,0),
	(773,NULL,'337.7101','-1991.9207','24.0633','225.3852','319.3226','-2018.7178','20.7952','142.4193',2,12000000,2,0,0,2,0,'2023-03-21 12:14:57',2040,NULL,NULL,0),
	(774,NULL,'48.1319','-84.4821','61.5644','251.7388','46.3978','-100.773','56.2657','69.3717',2,12000000,2,0,0,2,0,'2023-03-08 03:01:00',932,NULL,NULL,0),
	(775,NULL,'-400.0432','665.0567','163.8301','3.6576','-394.1035','671.0748','163.146','354.0502',10,190000000,9,0,0,11,0,'2023-03-26 21:19:37',2491,NULL,NULL,0),
	(776,NULL,'345.6707','-2015.3088','22.3955','6.1282','356.655','-1970.9094','24.4112','325.024',2,12000000,2,0,0,2,0,'2023-03-23 21:01:01',2180,NULL,NULL,0),
	(777,NULL,'45.1813','-91.8358','65.7638','248.706','42.909','-99.451','56.2631','69.1446',2,12000000,2,0,0,2,0,'2023-03-25 19:13:27',2326,NULL,NULL,0),
	(778,NULL,'-17.529','-296.6828','45.7581','258.8245','-15.7118','-301.7851','45.7149','259.2064',4,40000000,5,0,0,4,0,'2023-03-12 09:02:48',1386,NULL,NULL,0),
	(779,NULL,'353.9447','-2022.234','22.3955','317.6995','354.4167','-1969.3516','24.4248','319.608',2,12000000,2,0,0,2,0,'2023-03-19 03:31:00',1927,NULL,NULL,0),
	(780,NULL,'48.0002','-84.3889','65.7638','246.9825','39.356','-98.0986','56.2837','69.1578',2,12000000,2,0,0,2,0,'2023-03-06 02:48:34',749,NULL,NULL,0),
	(781,NULL,'-353.2462','668.0626','169.0719','168.1167','-344.9566','662.8655','169.5328','170.08',8,120000000,8,0,0,8,0,'2023-03-11 00:19:10',1247,NULL,NULL,0),
	(782,NULL,'356.9121','-2024.9281','22.3955','321.0785','352.1625','-1967.4868','24.4861','315.0138',2,12000000,2,0,0,2,0,'2023-03-05 03:01:00',666,NULL,NULL,0),
	(783,NULL,'362.4434','-2029.3358','22.3955','318.1476','349.7799','-1965.4022','24.5501','322.2295',2,12000000,2,0,0,2,0,'2023-03-08 14:23:12',966,NULL,NULL,0),
	(784,NULL,'45.23','-91.907','69.7635','253.1522','35.9041','-96.7927','56.3175','69.1749',2,12000000,2,0,0,2,0,'2023-03-16 21:31:36',1807,NULL,NULL,0),
	(785,NULL,'-30.3339','-346.9981','46.4517','82.2895','-25.5624','-343.1686','43.2828','246.2982',4,40000000,5,0,0,4,0,'2023-03-22 10:29:38',2079,NULL,NULL,0),
	(786,NULL,'-340.0385','625.8258','171.3567','100.5736','-355.4625','641.1879','171.281','48.3214',10,190000000,9,0,0,11,0,'2023-03-13 00:02:37',1508,NULL,NULL,0),
	(787,NULL,'48.0449','-84.5325','69.763','259.8351','32.3384','-95.4375','56.3779','69.1852',2,12000000,2,0,0,2,0,'2023-03-13 08:48:51',1537,NULL,NULL,0),
	(788,NULL,'365.2999','-2031.7388','22.3955','320.7148','347.4758','-1963.5691','24.4787','316.3322',2,12000000,2,0,0,2,0,'2023-03-06 06:01:00',755,NULL,NULL,0),
	(789,NULL,'-307.8424','643.2344','176.1319','119.4939','-303.1894','631.6929','175.6127','115.4345',10,190000000,9,0,0,11,0,'2023-07-20 22:37:46',2682,NULL,NULL,0),
	(790,NULL,'371.469','-2040.7036','22.1964','246.6466','399.6362','-2005.8301','23.1888','336.3698',2,12000000,2,0,0,2,0,'2023-03-24 11:12:01',2209,NULL,NULL,0),
	(791,NULL,'-293.6407','601.1324','181.5755','329.0647','-274.7437','602.3411','181.7644','351.4229',10,190000000,9,0,0,11,0,'2023-03-03 16:35:51',556,NULL,NULL,0),
	(792,NULL,'-53.2423','-397.5551','38.1281','352.6283','-38.7321','-381.9268','38.6956','247.1291',3,24000000,3,0,0,3,0,'2023-03-06 09:21:00',760,NULL,NULL,0),
	(793,NULL,'-246.1221','621.4682','187.8102','170.3616','-242.3254','609.1959','187.07','128.4884',10,190000000,9,0,0,11,0,'2023-03-06 17:08:10',788,NULL,NULL,0),
	(794,NULL,'-50.8829','-398.6544','38.1281','345.8978','-41.0555','-387.8398','38.6034','254.2617',3,24000000,3,0,0,3,0,'2023-03-02 06:21:00',500,NULL,NULL,0),
	(795,NULL,'58.88','-98.3403','58.2042','74.7505','84.7512','-99.8021','59.3827','240.9303',2,12000000,2,0,0,2,0,'2023-03-09 16:19:57',1081,NULL,NULL,0),
	(796,NULL,'342.6704','-1981.7133','24.1961','307.6683','355.1385','-1973.2952','24.4044','328.1241',2,12000000,2,0,0,2,0,'2023-03-24 23:14:00',2264,NULL,NULL,0),
	(797,NULL,'-232.625','588.19','190.5363','359.7933','-224.0107','591.9487','190.2213','358.2044',10,190000000,9,0,0,11,0,'2023-03-08 18:45:23',988,NULL,NULL,0),
	(798,NULL,'61.4851','-90.848','58.2043','78.1925','83.0726','-103.7946','58.6681','249.7337',2,12000000,2,0,0,2,0,'2023-03-13 19:01:00',1622,NULL,NULL,0),
	(799,NULL,'-189.3757','617.8937','199.6612','183.9867','-196.392','613.6246','196.7248','184.4653',10,2********,9,0,0,11,0,'2023-03-12 22:28:22',1492,NULL,NULL,0),
	(800,NULL,'323.8244','-1990.7739','24.1673','49.1649','346.1493','-1964.9395','24.459','321.2567',2,12000000,2,0,0,2,0,'2023-03-11 07:01:00',1265,NULL,NULL,0),
	(801,NULL,'64.6894','-80.3167','62.508','68.9067','90.8275','-79.9723','62.903','247.3934',2,12000000,2,0,0,2,0,'2023-03-12 16:01:00',1429,NULL,NULL,0),
	(802,NULL,'325.14','-1989.3302','24.1673','50.0096','348.4078','-1966.8556','24.5316','321.0376',2,12000000,2,0,0,2,0,'2023-03-11 21:21:20',1333,NULL,NULL,0),
	(803,NULL,'-115.8225','-372.8718','38.0723','253.7136','-116.6454','-366.772','37.4243','64.927',3,24000000,3,0,0,3,0,'2023-03-22 13:06:00',2088,NULL,NULL,0),
	(804,NULL,'-185.1964','591.3515','197.8231','359.6271','-178.3114','588.6505','197.6282','3.7645',8,120000000,8,0,0,8,0,'2023-03-08 14:45:59',967,NULL,NULL,0),
	(805,NULL,'331.2259','-1981.9063','24.1673','42.8416','350.3327','-1969.2766','24.4673','321.5111',2,12000000,2,0,0,2,0,'2023-03-04 15:52:48',622,NULL,NULL,0),
	(806,NULL,'67.5248','-73.4038','66.7021','70.8163','90.0848','-83.6618','62.0845','244.8125',2,12000000,2,0,0,2,0,'2023-03-07 10:33:28',854,NULL,NULL,0),
	(807,NULL,'334.4345','-1978.4957','24.1673','48.5806','352.7885','-1971.4116','24.4411','321.0883',2,12000000,2,0,0,2,0,'2023-03-13 09:48:54',1541,NULL,NULL,0),
	(808,NULL,'64.9637','-80.4011','66.7021','67.6649','89.1989','-87.2397','61.5727','247.8748',2,12000000,2,0,0,2,0,'2023-03-13 12:34:05',1560,NULL,NULL,0),
	(809,NULL,'362.2772','-1987.0521','24.234','341.0363','358.011','-1974.7705','24.3362','341.7387',2,12000000,2,0,0,2,0,'2023-03-07 03:16:05',844,NULL,NULL,0),
	(810,NULL,'-126.4774','588.5168','204.7106','0.7135','-143.8745','596.4207','203.8329','4.24',10,190000000,9,0,0,11,0,'2023-03-11 12:59:27',1270,NULL,NULL,0),
	(811,NULL,'364.0913','-1987.6619','24.2341','340.0527','361.1939','-1975.3167','24.3025','342.3005',2,12000000,2,0,0,2,0,'2023-03-03 18:01:01',566,NULL,NULL,0),
	(812,NULL,'-83.3246','-281.9974','45.5527','173.4131','-10.2272','-310.5761','45.6275','77.0089',3,24000000,3,0,0,3,0,'2023-03-12 15:11:01',1413,NULL,NULL,0),
	(813,NULL,'-85.7927','-281.5088','45.5527','83.27','-11.5074','-313.8112','45.435','73.1171',3,24000000,3,0,0,3,0,'2023-03-24 16:01:00',2230,NULL,NULL,0),
	(814,NULL,'374.753','-1991.3967','24.235','342.9916','363.8307','-1976.7958','24.2109','340.7159',2,12000000,2,0,0,2,0,'2023-03-23 13:28:00',2140,NULL,NULL,0),
	(815,NULL,'383.7173','-1994.6334','24.235','337.8988','366.5648','-1977.2487','24.1826','344.0041',2,12000000,2,0,0,2,0,'2023-03-26 20:58:55',2477,NULL,NULL,0),
	(816,NULL,'-113.202','985.8761','235.754','108.4454','-130.2447','1004.5735','235.7321','200.0351',10,600000000,9,0,0,11,0,'2024-04-22 20:29:45',NULL,NULL,NULL,0),
	(817,NULL,'385.442','-1995.2053','24.235','338.2209','369.3593','-1978.3201','24.1389','344.4991',2,12000000,2,0,0,2,0,'2023-03-16 18:01:00',1798,NULL,NULL,0),
	(818,NULL,'69.1222','-57.0807','68.8215','162.9147','68.1102','-31.4173','68.812','342.0557',2,12000000,2,0,0,2,0,'2023-03-26 14:16:10',2420,NULL,NULL,0),
	(819,NULL,'356.8559','-1996.6846','24.2455','164.175','370.0089','-1976.6737','24.1847','338.9344',2,12000000,2,0,0,2,0,'2023-03-10 01:12:00',1141,NULL,NULL,0),
	(820,NULL,'61.8366','-54.0651','73.0214','159.2929','71.644','-32.6729','68.812','340.2977',2,12000000,2,0,0,2,0,'2023-03-13 04:01:00',1526,NULL,NULL,0),
	(822,NULL,'363.285','-1999.4233','24.2455','162.3845','367.2586','-1975.3386','24.2545','336.8077',2,12000000,2,0,0,2,0,'2023-03-08 18:11:00',995,NULL,NULL,0),
	(823,NULL,'69.4386','-56.9168','73.0208','161.5781','75.4051','-33.9985','68.812','335.4085',2,12000000,2,0,0,2,0,'2023-03-22 16:22:00',2095,NULL,NULL,0),
	(824,NULL,'173.0417','-280.3541','46.2669','157.2235','160.8051','-313.0168','44.4367','159.0479',3,24000000,3,0,0,3,0,'2023-03-07 07:07:23',849,NULL,NULL,0),
	(825,NULL,'366.96','-2000.8584','24.2455','156.951','364.4594','-1974.6724','24.3062','339.1758',2,12000000,2,0,0,2,0,'2023-03-12 15:01:00',1414,NULL,NULL,0),
	(826,NULL,'61.7696','-54.0419','77.0213','151.3615','78.9703','-35.4106','68.812','331.0302',2,12000000,2,0,0,2,0,'2023-03-24 09:21:35',2206,NULL,NULL,0),
	(827,NULL,'373.4819','-2003.0697','24.2455','162.7698','362.0844','-1973.0631','24.3886','340.0646',2,12000000,2,0,0,2,0,'2023-03-20 03:36:59',1978,NULL,NULL,0),
	(828,NULL,'377.1781','-2004.3752','24.2555','154.7387','359.0527','-1972.1375','24.4106','343.6891',2,12000000,2,0,0,2,0,'2023-03-14 21:00:01',1719,NULL,NULL,0),
	(829,NULL,'69.2171','-56.8812','77.0213','182.8266','78.9703','-35.4106','68.812','331.0302',2,12000000,2,0,0,2,0,'2023-03-02 09:22:02',502,NULL,NULL,0),
	(830,NULL,'180.6062','-283.2395','50.2766','136.0259','164.5511','-314.9454','44.3691','152.1769',2,12000000,2,0,0,2,0,'2023-03-05 16:00:14',693,NULL,NULL,0),
	(831,NULL,'172.9596','-280.3073','50.2762','250.9446','168.2294','-316.0385','44.3121','155.0275',2,12000000,2,0,0,2,0,'2023-03-13 07:19:33',1531,NULL,NULL,0),
	(832,NULL,'75.8885','-61.9793','76.1384','336.6328','96.513','-33.536','68.0035','339.6336',2,12000000,2,0,0,2,0,'2023-03-23 07:20:02',2129,NULL,NULL,0),
	(833,NULL,'180.5827','-283.0873','54.2768','156.9533','168.2294','-316.0385','44.3121','155.0275',2,12000000,2,0,0,2,0,'2023-03-09 10:11:00',1050,NULL,NULL,0),
	(834,NULL,'78.9329','-58.0745','76.1384','74.1089','91.597','-32.0023','68.0776','338.0107',2,12000000,2,0,0,2,0,'2023-03-04 10:01:00',610,NULL,NULL,0),
	(835,NULL,'172.9856','-280.482','54.2764','156.749','165.4115','-314.7982','44.3607','159.351',2,12000000,2,0,0,2,0,'2023-03-06 11:01:00',764,NULL,NULL,0),
	(836,NULL,'79.0339','-58.1164','72.1438','73.4033','86.1507','-38.2276','67.9444','332.567',2,12000000,2,0,0,2,0,'2023-03-23 21:56:17',2181,NULL,NULL,0),
	(837,NULL,'75.9203','-62.1192','72.1438','328.3835','95.4977','-41.9109','67.9444','338.1013',2,12000000,2,0,0,2,0,'2023-03-20 21:01:00',2023,NULL,NULL,0),
	(838,NULL,'79.0417','-58.1315','67.9445','68.1262','94.9513','-29.3563','68.0498','340.1728',2,12000000,2,0,0,2,0,'2023-03-26 16:17:00',2444,NULL,NULL,0),
	(839,NULL,'75.8242','-62.252','67.9445','329.077','95.4978','-41.91','67.9444','338.1013',2,12000000,2,0,0,2,0,'2023-03-07 19:48:43',893,NULL,NULL,0),
	(840,NULL,'228.6851','765.8152','204.9766','57.406','216.8957','757.4146','204.6658','49.0141',10,*********,9,0,0,11,0,'2023-03-26 05:16:15',2387,NULL,NULL,0),
	(841,NULL,'140.3615','-286.7351','50.4496','204.3998','141.526','-306.2559','44.9586','165.8887',2,12000000,2,0,0,2,0,'2023-03-26 01:01:00',2372,NULL,NULL,0),
	(842,NULL,'143.2529','-280.5735','50.4496','274.8151','145.5419','-300.3798','45.6312','160.7766',2,12000000,2,0,0,2,0,'2023-03-20 04:00:01',1980,NULL,NULL,0),
	(843,NULL,'144.001','-278.4695','50.4496','271.7159','142.1153','-298.9852','45.7085','160.4357',2,12000000,2,0,0,2,0,'2023-03-08 14:23:15',968,NULL,NULL,0),
	(844,NULL,'232.0469','672.4432','189.9456','37.8478','225.4701','680.4668','189.408','105.0618',10,190000000,9,0,0,11,0,'2023-03-11 22:21:00',1335,NULL,NULL,0),
	(845,NULL,'363.7791','-2083.0747','21.7546','226.4401','388.4155','-2080.9785','19.8953','324.3852',2,12000000,2,0,0,2,0,'2023-03-26 19:00:01',2464,NULL,NULL,0),
	(846,NULL,'216.1714','620.3635','187.6361','77.3648','209.328','608.7725','186.7382','350.7967',8,160000000,8,0,0,8,0,'2023-03-13 18:19:37',1613,NULL,NULL,0),
	(847,NULL,'368.4965','-2077.8362','21.7668','232.4465','332.4866','-2031.2782','21.2078','142.9949',2,12000000,2,0,0,2,0,'2023-03-08 20:15:23',1010,NULL,NULL,0),
	(848,NULL,'371.0783','-2074.9709','21.7547','232.3667','268.7116','-2073.0779','16.9783','290.2508',2,12000000,2,0,0,2,0,'2023-03-27 21:00:01',2637,NULL,NULL,0),
	(849,NULL,'375.7177','-2069.9272','21.5432','244.0027','270.385','-2076.4504','16.8453','283.427',2,12000000,2,0,0,2,0,'2023-03-14 20:12:28',1716,NULL,NULL,0),
	(850,NULL,'378.0646','-2066.7678','21.7668','222.4627','272.0812','-2080.3167','16.6771','280.7139',2,12000000,2,0,0,2,0,'2023-03-24 02:58:52',2199,NULL,NULL,0),
	(851,NULL,'382.5407','-2061.4558','21.7546','195.381','272.5688','-2082.7366','16.6029','282.6397',2,12000000,2,0,0,2,0,'2023-03-25 20:01:00',2333,NULL,NULL,0),
	(852,NULL,'382.8968','-2037.7286','23.4023','17.0876','394.2591','-2003.3961','23.418','339.5947',2,12000000,2,0,0,2,0,'2023-03-08 02:30:00',929,NULL,NULL,0),
	(853,NULL,'383.649','-2036.0231','23.4025','55.0251','396.8147','-2004.4413','23.3093','336.8006',2,12000000,2,0,0,2,0,'2023-03-27 19:32:09',2603,NULL,NULL,0),
	(854,NULL,'147.4001','-103.1309','62.4947','67.1419','165.5568','-115.6824','62.1223','244.8003',2,12000000,2,0,0,2,0,'2023-03-19 14:11:00',1937,NULL,NULL,0),
	(855,NULL,'113.2315','-277.7189','46.3334','248.1471','122.6254','-298.0303','45.5325','159.7783',3,24000000,3,0,0,3,0,'2023-03-21 13:14:59',2046,NULL,NULL,0),
	(856,NULL,'115.4259','-271.3145','46.3334','326.3239','119.6708','-297.1075','45.6244','159.9785',3,24000000,3,0,0,3,0,'2023-02-28 20:11:04',468,NULL,NULL,0),
	(857,NULL,'388.4647','-2025.7771','23.4031','68.6279','399.3076','-2006.7227','23.1971','339.7163',2,12000000,2,0,0,2,0,'2023-03-18 22:01:00',1914,NULL,NULL,0),
	(858,NULL,'150.8839','555.9993','183.7375','114.0111','148.2445','569.3312','183.8449','284.8941',8,120000000,8,0,0,8,0,'2023-03-13 01:46:37',1515,NULL,NULL,0),
	(859,NULL,'392.5376','-2017.1641','23.4031','61.7782','402.119','-2007.5851','23.1438','333.6329',2,12000000,2,0,0,2,0,'2023-03-22 23:07:45',2114,NULL,NULL,0),
	(860,NULL,'127.1408','-107.9082','60.7161','160.133','120.8825','-98.5435','60.7161','65.2353',2,12000000,2,0,0,2,0,'2023-03-25 20:37:01',2335,NULL,NULL,0),
	(861,NULL,'115.6326','-271.3849','50.5123','337.6774','116.6332','-296.0467','45.7064','162.0491',3,24000000,3,0,0,3,0,'2023-03-17 14:56:27',1828,NULL,NULL,0),
	(862,NULL,'393.3906','-2015.3307','23.4031','64.2201','404.8863','-2009.5061','23.098','341.5075',2,12000000,2,0,0,2,0,'2023-02-28 14:17:11',469,NULL,NULL,0),
	(863,NULL,'113.4658','-277.5307','50.5123','170.6498','113.8864','-295.0299','45.7931','160.029',3,24000000,3,0,0,3,0,'2023-03-08 11:03:28',947,NULL,NULL,0),
	(864,NULL,'115.3991','-271.3172','54.5122','251.4618','110.945','-294.0257','45.8957','155.3741',3,24000000,3,0,0,3,0,'2023-03-23 23:10:53',2191,NULL,NULL,0),
	(865,NULL,'143.5777','-113.0911','62.2967','66.8854','166.5921','-112.9703','62.3404','253.1845',2,12000000,2,0,0,2,0,'2023-03-14 00:01:00',1684,NULL,NULL,0),
	(866,NULL,'119.3228','564.2646','183.9593','7.6711','131.1435','568.8508','183.3276','286.5833',6,160000000,7,0,0,6,0,'2023-03-12 01:01:00',1364,NULL,NULL,0),
	(867,NULL,'113.1561','-277.6755','54.5122','239.8783','107.8918','-292.7841','45.9858','161.1512',3,24000000,3,0,0,3,0,'2023-03-10 22:45:37',1237,NULL,NULL,0),
	(868,NULL,'404.3849','-2018.0646','23.4135','242.4974','394.6096','-2002.2379','23.3706','340.3775',2,12000000,2,0,0,2,0,'2023-03-24 12:01:00',2211,NULL,NULL,0),
	(869,NULL,'84.9674','562.0161','182.7731','4.1605','97.1352','567.5185','182.2879','279.9245',10,*********,9,0,0,11,0,'2023-03-26 11:00:31',2404,NULL,NULL,0),
	(870,NULL,'144.619','-82.9075','64.3045','249.1917','121.7571','-81.1507','64.4107','72.9723',2,12000000,2,0,0,2,0,'2023-03-06 00:34:45',741,NULL,NULL,0),
	(871,NULL,'401.6881','-2024.3954','23.4135','252.2934','407.9487','-2010.7037','23.0907','338.6151',2,12000000,2,0,0,2,0,'2023-03-15 22:01:00',1763,NULL,NULL,0),
	(872,NULL,'45.8109','556.1746','180.0822','22.1087','51.1855','562.4822','180.2911','27.2765',10,276000000,9,0,0,11,0,'2023-03-27 22:45:39',2651,NULL,NULL,0),
	(873,NULL,'399.8923','-2027.9357','23.4135','241.226','397.3905','-2003.2286','23.2899','336.0451',2,12000000,2,0,0,2,0,'2023-03-11 19:28:04',1322,NULL,NULL,0),
	(874,NULL,'141.6113','-90.1953','68.4686','246.3938','120.7085','-84.7154','64.1873','69.8517',2,12000000,2,0,0,2,0,'2023-03-08 14:23:14',969,NULL,NULL,0),
	(875,NULL,'396.7941','-2034.1813','23.4134','246.4406','399.8642','-2005.0687','23.1835','336.458',2,12000000,2,0,0,2,0,'2023-03-17 00:00:01',1815,NULL,NULL,0),
	(876,NULL,'144.3038','-82.616','68.4686','247.9991','121.7571','-81.1507','64.4107','72.9723',2,12000000,2,0,0,2,0,'2023-03-17 20:01:00',1849,NULL,NULL,0),
	(877,NULL,'395.1906','-2037.7122','23.4132','242.8984','405.5743','-2008.092','23.0918','331.2884',2,12000000,2,0,0,2,0,'2023-03-06 01:15:01',744,NULL,NULL,0),
	(878,NULL,'97.7883','-255.962','47.4899','173.3025','86.4346','-285.5416','46.635','156.5744',3,24000000,3,0,0,3,0,'2023-03-26 21:01:00',2492,NULL,NULL,0),
	(879,NULL,'392.2293','-2043.9639','23.3251','246.0934','402.5866','-2006.3112','23.1294','349.0144',2,12000000,2,0,0,2,0,'2023-03-15 21:27:13',1762,NULL,NULL,0),
	(880,NULL,'105.284','-258.8891','51.4993','166.2218','88.5638','-286.4077','46.5732','157.7238',3,24000000,3,0,0,3,0,'2023-03-04 21:21:29',649,NULL,NULL,0),
	(881,NULL,'141.6701','-90.3987','72.4691','248.6139','120.6776','-84.9054','64.1854','73.4266',2,12000000,2,0,0,2,0,'2023-03-08 06:01:00',936,NULL,NULL,0),
	(882,NULL,'97.8974','-255.9456','51.4993','157.5559','90.7636','-287.3763','46.5088','156.8428',3,24000000,3,0,0,3,0,'2023-03-09 22:36:22',1120,NULL,NULL,0),
	(883,NULL,'144.6384','-82.8314','72.4687','249.4801','120.6776','-84.9054','64.1854','73.4266',2,12000000,2,0,0,2,0,'2023-03-16 02:58:21',1773,NULL,NULL,0),
	(884,NULL,'318.7834','552.1442','155.8767','295.674','315.5894','567.7213','154.4243','311.7308',10,256000000,9,0,0,11,0,'2023-03-05 01:17:45',661,NULL,NULL,0),
	(885,NULL,'63.5424','-261.7783','52.3538','172.4261','70.9196','-280.4528','47.0595','161.4991',3,24000000,3,0,0,3,0,'2023-03-24 01:28:25',2197,NULL,NULL,0),
	(886,NULL,'66.2297','-255.8679','52.3538','259.6759','66.1563','-278.9316','47.2068','162.2374',3,24000000,3,0,0,3,0,'2023-03-14 18:23:57',1711,NULL,NULL,0),
	(887,NULL,'67.2434','-253.6671','52.3538','206.2785','70.9196','-280.4528','47.0595','161.4991',3,24000000,3,0,0,3,0,'2023-03-25 01:17:00',2275,NULL,NULL,0),
	(888,NULL,'159.6301','-82.8471','68.5251','72.3972','159.865','-63.8743','68.2514','343.0516',2,12000000,2,0,0,2,0,'2023-03-12 21:00:01',1484,NULL,NULL,0),
	(889,NULL,'315.8503','501.6229','153.1798','196.0464','320.3855','495.3645','152.5367','284.9023',10,276000000,9,0,0,11,0,'2023-03-07 21:00:17',904,NULL,NULL,0),
	(890,NULL,'161.0174','-79.0565','68.5357','88.9705','158.7972','-67.4833','68.0364','341.0331',2,12000000,2,0,0,2,0,'2023-03-18 07:36:04',1874,NULL,NULL,0),
	(891,NULL,'151.6565','-72.6262','67.6748','345.4119','155.1722','-77.6137','68.0694','338.4682',2,12000000,2,0,0,2,0,'2023-03-24 22:01:00',2265,NULL,NULL,0),
	(892,NULL,'8.5383','-243.6218','47.6606','328.5311','22.6224','-245.8852','47.7179','267.2255',3,24000000,3,0,0,3,0,'2023-03-22 12:44:53',2085,NULL,NULL,0),
	(893,NULL,'331.4602','465.3679','151.2447','20.9719','328.1028','483.3717','151.0555','211.6206',10,240000000,9,0,0,11,0,'2023-03-03 19:03:06',568,NULL,NULL,0),
	(894,NULL,'2.2598','-241.1739','47.6606','3.9143','22.6224','-245.8852','47.7179','267.2255',3,24000000,3,0,0,3,0,'2023-03-26 23:01:00',2504,NULL,NULL,0),
	(895,NULL,'151.6514','-72.6212','71.8602','338.3805','156.4521','-74.2252','67.9729','340.495',2,12000000,2,0,0,2,0,'2023-03-11 12:01:00',1272,NULL,NULL,0),
	(896,NULL,'151.641','-72.4982','75.8614','331.9623','157.7177','-70.6635','67.9729','340.3575',2,12000000,2,0,0,2,0,'2023-03-06 18:01:01',798,NULL,NULL,0),
	(897,NULL,'2.2636','-240.9881','51.8606','338.3786','23.5931','-242.4919','47.7841','256.4424',3,24000000,3,0,0,3,0,'2023-03-26 13:07:23',2416,NULL,NULL,0),
	(898,NULL,'8.6322','-243.2262','51.8606','334.6545','23.5931','-242.4919','47.7841','256.4424',3,24000000,3,0,0,3,0,'2023-03-11 20:33:54',1328,NULL,NULL,0),
	(899,NULL,'2.2715','-240.9842','55.8605','332.2029','25.1241','-238.7055','47.8857','261.9274',3,24000000,3,0,0,3,0,'2023-03-13 08:01:00',1538,NULL,NULL,0),
	(900,NULL,'346.2851','440.3614','147.9639','301.9037','359.2561','438.063','145.3378','276.5457',10,*********,9,0,0,11,0,'2023-03-03 00:47:57',526,NULL,NULL,0),
	(901,NULL,'8.748','-243.2805','55.8605','336.3643','25.1241','-238.7055','47.8857','261.9274',3,24000000,3,0,0,3,0,'2023-03-13 20:55:58',1631,NULL,NULL,0),
	(902,NULL,'373.8366','427.8388','145.6842','76.7323','390.9622','432.9167','143.1592','263.8322',10,1********,9,0,0,11,0,'2023-03-07 10:53:17',853,NULL,NULL,0),
	(903,NULL,'202.4345','-149.0765','61.3003','251.9267','203.8204','-158.3291','56.8656','68.5837',2,12000000,2,0,0,2,0,'2023-03-13 11:59:49',1552,NULL,NULL,0),
	(904,NULL,'204.9471','-141.525','65.4789','254.2235','197.2599','-156.7095','56.7915','72.2658',2,12000000,2,0,0,2,0,'2023-03-10 20:39:52',1217,NULL,NULL,0),
	(905,NULL,'202.2541','-148.9687','65.4789','254.0985','190.8472','-154.8239','56.7202','70.5912',2,12000000,2,0,0,2,0,'2023-03-19 20:55:46',1957,NULL,NULL,0),
	(906,NULL,'213.082','-154.4116','58.8253','76.5614','230.6444','-161.4176','58.7581','254.2166',2,12000000,2,0,0,2,0,'2023-03-13 22:11:00',1663,NULL,NULL,0),
	(907,NULL,'215.6402','-146.8419','58.8095','66.3886','230.6444','-161.4176','58.7581','254.2166',2,12000000,2,0,0,2,0,'2023-03-21 17:11:34',2056,NULL,NULL,0),
	(908,NULL,'223.9926','513.8113','140.767','43.9082','202.9093','499.1825','140.8544','114.245',10,190000000,9,0,0,11,0,'2023-03-12 23:03:54',1494,NULL,NULL,0),
	(909,NULL,'-949.328','196.4521','67.3904','344.455','-934.0765','210.9321','67.4645','164.5247',10,*********,9,0,0,11,0,'2023-03-12 17:30:34',1439,NULL,NULL,0),
	(910,NULL,'167.0247','473.8703','142.5132','64.5228','175.3894','483.8354','142.34','354.8428',10,190000000,9,0,0,11,0,'2023-03-07 16:55:07',875,NULL,NULL,0),
	(911,NULL,'231.3582','-130.8053','63.7635','340.3346','237.0013','-144.265','63.7635','246.0347',2,12000000,2,0,0,2,0,'2023-03-21 20:48:43',2062,NULL,NULL,0),
	(912,NULL,'-902.5515','191.5432','69.446','268.1411','-909.0024','191.945','69.4495','161.1826',10,220000000,9,0,0,11,0,'2023-03-06 15:47:15',773,NULL,NULL,0),
	(913,NULL,'107.1022','467.0849','147.562','0.4883','98.9621','476.2593','147.3183','116.8589',10,190000000,9,0,0,11,0,'2023-03-23 17:38:26',2159,NULL,NULL,0),
	(914,NULL,'-913.3533','108.3052','55.5147','265.9215','-919.4876','110.1013','55.3207','58.4514',10,*********,9,0,0,11,0,'2023-03-02 16:51:12',509,NULL,NULL,0),
	(915,NULL,'119.5738','494.2952','147.3429','103.7747','113.8044','496.6778','147.1481','191.1802',6,*********,7,0,0,6,0,'2023-03-16 02:31:49',1774,NULL,NULL,0),
	(916,NULL,'-971.4983','122.1518','57.0486','127.285','-958.804','111.0998','56.5091','324.4017',10,*********,9,0,0,11,0,'2023-03-22 00:35:12',2071,NULL,NULL,0),
	(917,NULL,'80.2516','485.6309','148.2015','206.9221','90.6247','486.0421','147.646','206.21',10,1********,9,0,0,11,0,'2023-03-07 20:45:57',896,NULL,NULL,0),
	(918,NULL,'239.629','-122.4281','70.1046','158.3881','255.3245','-98.5213','70.1036','335.9978',2,12000000,2,0,0,2,0,'2023-03-08 10:01:00',943,NULL,NULL,0),
	(919,NULL,'57.7433','450.0944','147.0315','331.5211','64.557','456.2887','146.8127','24.554',8,240000000,8,0,0,8,0,'2023-03-09 15:23:00',1067,NULL,NULL,0),
	(920,NULL,'234.4113','-107.8246','74.3527','337.366','252.3605','-93.6442','70.0902','339.581',2,12000000,2,0,0,2,0,'2023-03-11 17:05:29',1306,NULL,NULL,0),
	(921,NULL,'43.0015','468.8142','148.0959','175.5446','56.6631','467.965','146.7567','125.7456',10,1********,9,0,0,11,0,'2023-03-09 19:02:38',1101,NULL,NULL,0),
	(922,NULL,'234.4551','-107.8941','78.3569','339.6448','248.2773','-92.3403','70.0817','339.1523',2,12000000,2,0,0,2,0,'2023-03-14 10:01:00',1696,NULL,NULL,0),
	(923,NULL,'-7.7906','468.1522','145.8611','338.7133','1.7147','468.3844','145.8454','50.0146',10,2********,9,0,0,11,0,'2023-03-04 08:54:04',609,NULL,NULL,0),
	(924,NULL,'-998.0468','157.5003','62.2184','19.4285','-992.9895','144.2929','60.6664','275.2001',10,3********,9,0,0,11,0,'2023-03-11 16:33:44',1290,NULL,NULL,0),
	(925,NULL,'465.8019','-1567.5334','32.7923','228.0196','466.4552','-1599.3187','29.273','225.5149',2,12000000,2,0,0,2,0,'2023-03-18 16:01:01',1891,NULL,NULL,0),
	(926,NULL,'471.182','-1561.6881','32.7923','231.6346','479.2152','-1546.2736','29.2826','320.5349',2,12000000,2,0,0,2,0,'2023-03-08 20:08:00',1011,NULL,NULL,0),
	(927,NULL,'203.7522','-98.7469','69.287','163.4144','210.0809','-76.5725','69.1534','343.0458',2,12000000,2,0,0,2,0,'2023-03-07 14:01:00',865,NULL,NULL,0),
	(928,NULL,'-66.7186','490.2028','144.8848','343.0334','-76.0902','495.7027','144.4824','344.3639',10,*********,9,0,0,11,0,'2023-03-13 22:31:40',1668,NULL,NULL,0),
	(929,NULL,'460.8318','-1573.5575','32.7923','227.9219','469.6946','-1595.4547','29.2598','236.9117',2,12000000,2,0,0,2,0,'2023-03-18 00:01:00',1863,NULL,NULL,0),
	(930,NULL,'211.3556','-101.4362','73.2803','162.2241','213.6239','-77.8977','69.2338','339.8655',2,12000000,2,0,0,2,0,'2023-03-06 15:07:52',775,NULL,NULL,0),
	(931,NULL,'455.2361','-1579.8649','32.792','317.7586','472.1904','-1574.5568','29.123','231.8732',2,12000000,2,0,0,2,0,'2023-03-22 16:57:38',2096,NULL,NULL,0),
	(932,NULL,'-1038.0768','222.1794','64.3757','272.1738','-1048.6804','221.5632','63.7666','194.3875',10,220000000,9,0,0,11,0,'2023-03-05 18:41:20',703,NULL,NULL,0),
	(933,NULL,'203.7865','-98.7145','73.2803','160.0818','216.607','-79.1384','69.2987','339.6416',2,12000000,2,0,0,2,0,'2023-03-07 00:22:41',837,NULL,NULL,0),
	(934,NULL,'-109.8787','502.3049','143.4701','345.3637','-123.8032','508.653','143.0783','334.7375',10,260000000,9,0,0,11,0,'2023-03-05 23:03:43',731,NULL,NULL,0),
	(935,NULL,'442.3828','-1569.559','32.7925','318.8716','474.7034','-1571.7695','29.1237','228.8554',2,12000000,2,0,0,2,0,'2023-03-26 16:22:16',2445,NULL,NULL,0),
	(936,NULL,'436.1776','-1564.3385','32.7923','320.1775','476.9508','-1568.8549','29.1179','224.909',2,12000000,2,0,0,2,0,'2023-03-24 10:01:00',2208,NULL,NULL,0),
	(937,NULL,'430.5198','-1559.1777','32.7923','317.8442','479.2935','-1565.7793','29.1048','224.1075',2,12000000,2,0,0,2,0,'2023-03-27 09:02:25',2537,NULL,NULL,0),
	(938,NULL,'-175.0933','502.4738','137.4203','99.8853','-187.7623','502.6544','134.5516','316.4294',10,190000000,9,0,0,11,0,'2023-03-20 16:59:16',2003,NULL,NULL,0),
	(939,NULL,'176.1863','-82.8625','72.7723','159.8056','178.1236','-66.8971','68.4714','339.8197',2,12000000,2,0,0,2,0,'2023-03-15 05:01:01',1735,NULL,NULL,0),
	(940,NULL,'461.0515','-1585.3004','32.7923','320.494','470.0796','-1577.2981','29.1226','229.992',2,12000000,2,0,0,2,0,'2023-03-12 11:22:30',1396,NULL,NULL,0),
	(941,NULL,'-230.3278','488.1982','128.7681','13.7496','-247.8149','496.4516','124.936','284.5287',8,120000000,8,0,0,8,0,'2023-03-04 23:05:56',657,NULL,NULL,0),
	(942,NULL,'176.0889','-82.8701','76.7915','160.1515','172.1456','-64.8822','68.4169','345.0422',2,12000000,2,0,0,2,0,'2023-03-27 03:01:00',2521,NULL,NULL,0),
	(943,NULL,'467.0893','-1590.1014','32.7923','322.9882','467.9859','-1579.9424','29.1228','232.3992',2,12000000,2,0,0,2,0,'2023-03-10 11:01:00',1166,NULL,NULL,0),
	(944,NULL,'-819.64','267.8722','86.396','265.6141','-827.1614','270.8446','86.1961','339.571',8,160000000,8,0,0,8,0,'2023-03-22 11:57:19',2083,NULL,NULL,0),
	(945,NULL,'-355.306','469.9303','112.4984','283.254','-353.5738','475.0649','112.7419','302.2148',8,160000000,8,0,0,8,0,'2023-03-11 20:22:07',1324,NULL,NULL,0),
	(946,NULL,'202.2394','-133.261','63.4943','168.1015','188.322','-118.6501','63.2616','73.077',2,12000000,2,0,0,2,0,'2023-03-27 12:25:37',2543,NULL,NULL,0),
	(947,NULL,'-312.3315','474.6145','111.8239','123.5954','-325.4577','476.8321','110.898','146.8826',10,190000000,9,0,0,11,0,'2023-03-05 23:57:29',733,NULL,NULL,0),
	(948,NULL,'-305.1859','431.7504','110.3091','13.039','-322.8456','437.4939','109.0073','273.2468',10,190000000,9,0,0,11,0,'2023-03-07 21:21:01',901,NULL,NULL,0),
	(949,NULL,'-877.074','306.3526','84.1543','61.8252','-869.3228','319.4985','83.9776','188.0137',10,190000000,9,0,0,11,0,'2023-03-07 07:23:52',848,NULL,NULL,0),
	(950,NULL,'561.7833','-1751.9811','29.28','241.8062','544.8123','-1781.9532','29.107','153.921',2,12000000,2,0,0,2,0,'2023-03-25 00:52:41',2269,NULL,NULL,0),
	(951,NULL,'558.2419','-1759.9634','29.3142','244.8142','561.4905','-1798.7078','29.197','349.1909',2,12000000,2,0,0,2,0,'2023-03-13 20:49:50',1633,NULL,NULL,0),
	(952,NULL,'-881.4745','363.7617','85.3627','228.4151','-889.1975','363.7352','85.0314','348.5832',10,*********,9,0,0,11,0,'2023-03-03 14:51:45',546,NULL,NULL,0),
	(953,NULL,'555.1163','-1766.4452','29.3121','214.3471','564.5312','-1798.7905','29.197','349.0445',2,12000000,2,0,0,2,0,'2023-03-13 09:01:00',1542,NULL,NULL,0),
	(954,NULL,'552.7023','-1771.7134','29.3119','243.6385','558.1195','-1788.6403','29.197','148.0814',2,12000000,2,0,0,2,0,'2023-03-10 05:16:36',1154,NULL,NULL,0),
	(955,NULL,'550.6176','-1775.6542','29.3119','243.3697','555.3069','-1786.9916','29.197','151.1226',2,12000000,2,0,0,2,0,'2023-03-06 22:58:00',825,NULL,NULL,0),
	(956,NULL,'-1026.1656','360.5327','71.3614','74.9783','-1013.192','361.4174','70.8726','147.2419',10,270000000,9,0,0,11,0,'2023-03-12 04:19:57',1374,NULL,NULL,0),
	(957,NULL,'566.8889','-1777.6135','29.356','323.0869','567.4683','-1799.8063','29.1914','347.9369',2,12000000,2,0,0,2,0,'2023-03-08 08:11:00',939,NULL,NULL,0),
	(958,NULL,'-1135.5918','376.029','71.2997','142.8539','-1099.0366','359.5366','68.489','4.1452',10,*********,9,0,0,11,0,'2023-03-23 18:34:25',2164,NULL,NULL,0),
	(959,NULL,'559.4946','-1777.1816','33.4426','337.4955','560.526','-1789.978','29.197','152.9315',2,12000000,2,0,0,2,0,'2023-03-06 07:57:54',756,NULL,NULL,0),
	(960,NULL,'-371.776','408.3084','110.5937','110.4096','-351.6104','425.7326','110.6932','19.7575',8,1********,8,0,0,8,0,'2023-03-10 22:58:27',1233,NULL,NULL,0),
	(961,NULL,'-400.9249','427.5841','112.3413','247.1588','-393.0503','435.1657','112.3401','233.5803',10,*********,9,0,0,11,0,'2023-03-11 13:20:55',1276,NULL,NULL,0),
	(962,NULL,'550.5037','-1770.6432','33.4426','245.7964','558.4705','-1798.5546','29.197','347.6375',2,12000000,2,0,0,2,0,'2023-03-10 01:17:09',1142,NULL,NULL,0),
	(963,NULL,'553.0295','-1765.8448','33.4426','263.3779','555.5564','-1797.7433','29.197','346.8384',2,12000000,2,0,0,2,0,'2023-03-18 14:24:47',1882,NULL,NULL,0),
	(964,NULL,'556.2751','-1759.0734','33.4426','267.0988','552.5118','-1797.0352','29.197','347.37',2,12000000,2,0,0,2,0,'2023-03-04 17:01:00',627,NULL,NULL,0),
	(965,NULL,'559.7905','-1751.2247','33.4426','331.6825','549.4539','-1796.4137','29.197','349.517',2,12000000,2,0,0,2,0,'2023-03-05 02:49:05',663,NULL,NULL,0),
	(966,NULL,'561.6511','-1747.4857','33.4426','149.2836','545.4347','-1794.4518','29.197','320.9901',2,12000000,2,0,0,2,0,'2023-02-28 01:06:41',470,NULL,NULL,0),
	(967,NULL,'352.9074','-142.2424','66.6883','337.7424','349.8814','-136.6123','65.1977','337.8478',3,24000000,3,0,0,3,0,'2023-03-23 00:55:11',2119,NULL,NULL,0),
	(968,NULL,'-386.6754','504.4618','120.4127','151.8723','-399.6963','521.8297','120.9973','354.1553',10,190000000,9,0,0,11,0,'2023-03-11 15:24:53',1284,NULL,NULL,0),
	(969,NULL,'315.2892','-127.8889','69.977','332.2346','333.4919','-129.8245','66.9387','337.3405',3,24000000,3,0,0,3,0,'2023-03-19 14:45:25',1938,NULL,NULL,0),
	(970,NULL,'-378.4128','548.519','123.8508','34.8423','-380.7424','522.5363','120.9393','220.8133',10,190000000,9,0,0,11,0,'2023-03-17 21:01:06',1853,NULL,NULL,0),
	(971,NULL,'-888.0299','42.5144','49.147','59.9366','-872.0724','49.3434','48.7698','183.4866',10,400000000,9,0,0,11,0,'2023-03-10 20:11:06',1219,NULL,NULL,0),
	(972,NULL,'-418.1323','568.7833','125.06','152.5834','-410.8398','555.6603','123.8291','155.3549',8,120000000,8,0,0,8,0,'2023-03-13 22:44:15',1669,NULL,NULL,0),
	(973,NULL,'152.1996','-1823.3153','27.8687','45.8363','139.899','-1829.9614','27.1906','52.2527',3,24000000,3,0,0,3,0,'2023-03-26 12:57:16',2407,NULL,NULL,0),
	(974,NULL,'-841.7908','-25.0812','40.3984','87.7505','-874.814','-52.8573','38.1807','280.6557',10,600000000,9,0,0,11,0,'2023-03-08 23:03:14',1023,NULL,NULL,0),
	(975,NULL,'-102.5896','2.2633','70.2375','59.5353','-70.4535','-1.9211','70.2632','255.6446',3,24000000,3,0,0,3,0,'2023-03-26 14:09:16',2422,NULL,NULL,0),
	(976,NULL,'-425.9307','535.4551','122.2293','335.9531','-441.5762','542.882','121.9293','259.6599',10,190000000,9,0,0,11,0,'2023-03-27 18:59:18',2590,NULL,NULL,0),
	(977,NULL,'-896.5753','-5.0431','43.7992','129.4546','-887.0599','-14.6116','43.0861','302.2086',10,*********,9,0,0,11,0,'2023-03-03 18:27:57',564,NULL,NULL,0),
	(978,NULL,'54.0385','-1873.6655','22.805','136.1075','49.7361','-1879.344','22.1557','140.1773',3,24000000,3,0,0,3,0,'2023-03-06 00:18:00',742,NULL,NULL,0),
	(979,NULL,'-474.7033','585.9107','128.6834','91.1525','-480.4256','596.2355','127.3771','88.4629',8,120000000,8,0,0,8,0,'2023-03-25 14:04:01',2299,NULL,NULL,0),
	(980,NULL,'-459.1343','537.4748','121.4571','357.8159','-470.1071','541.3005','121.0544','354.1869',10,190000000,9,0,0,11,0,'2023-03-11 16:49:39',1296,NULL,NULL,0),
	(981,NULL,'-929.9888','18.9908','48.3257','35.4725','-927.0497','11.9292','47.719','212.1216',10,190000000,9,0,0,11,0,'2023-03-08 00:06:14',918,NULL,NULL,0),
	(982,NULL,'149.3921','-1960.9697','19.4585','192.9839','158.2524','-1968.6165','18.5878','231.7913',3,24000000,3,0,0,3,0,'2023-03-24 09:11:00',2207,NULL,NULL,0),
	(983,NULL,'144.5457','-1970.1589','18.8577','153.5261','151.4168','-1977.2733','18.4055','222.6504',3,24000000,3,0,0,3,0,'2023-03-16 15:09:57',1784,NULL,NULL,0),
	(984,NULL,'-500.6041','552.3195','120.5971','341.4693','-481.6467','547.9523','119.986','341.0641',10,190000000,9,0,0,11,0,'2023-03-12 11:42:18',1390,NULL,NULL,0),
	(985,NULL,'-99.5991','9.6294','74.4375','63.2258','-71.2671','-4.8671','69.9205','256.9528',2,12000000,2,0,0,2,0,'2023-03-12 21:11:00',1485,NULL,NULL,0),
	(986,NULL,'-102.3767','2.1369','74.437','71.9249','-71.8915','-7.6735','69.649','258.8567',2,12000000,2,0,0,2,0,'2023-03-20 23:00:01',2026,NULL,NULL,0),
	(987,NULL,'-99.5457','9.6142','78.4376','72.8154','-74.2621','-7.152','69.6767','253.3658',2,12000000,2,0,0,2,0,'2023-03-13 21:01:00',1652,NULL,NULL,0),
	(988,NULL,'-830.8423','114.9947','55.8299','271.3778','-836.0829','116.0349','55.3691','165.321',10,190000000,9,0,0,11,0,'2023-03-27 18:11:00',2591,NULL,NULL,0),
	(989,NULL,'-102.4346','2.1498','78.4375','68.338','-73.3452','-4.1514','69.8855','257.3181',2,12000000,2,0,0,2,0,'2023-03-08 02:01:00',931,NULL,NULL,0),
	(990,NULL,'-106.9216','-8.6227','66.353','250.378','-76.5986','-28.145','65.7399','256.4282',2,12000000,2,0,0,2,0,'2023-03-10 11:36:00',1168,NULL,NULL,0),
	(991,NULL,'-102.6024','-11.4924','66.353','334.1529','-77.4142','-31.4028','65.3282','256.5291',2,12000000,2,0,0,2,0,'2023-03-08 06:01:00',937,NULL,NULL,0),
	(992,NULL,'37.8704','365.3711','116.0409','217.2654','25.289','367.8045','112.5872','69.5884',10,190000000,9,0,0,11,0,'2023-07-21 00:33:18',2684,NULL,NULL,0),
	(993,NULL,'-106.7613','-8.5681','70.5234','251.0357','-75.8731','-25.2409','66.1814','260.5009',2,12000000,2,0,0,2,0,'2023-03-13 23:39:54',1675,NULL,NULL,0),
	(994,NULL,'-6.6145','409.2224','120.2883','73.8341','15.5801','376.295','112.2763','226.6088',10,190000000,9,0,0,11,0,'2023-03-12 15:08:23',1415,NULL,NULL,0),
	(995,NULL,'-102.7629','-11.7938','70.5173','343.7634','-75.3116','-21.4982','66.6647','260.309',2,12000000,2,0,0,2,0,'2023-03-02 20:43:48',519,NULL,NULL,0),
	(996,NULL,'-106.8348','-8.506','74.5174','258.0366','-78.6278','-31.2027','65.4168','250.7674',2,12000000,2,0,0,2,0,'2023-03-10 09:01:00',1162,NULL,NULL,0),
	(997,NULL,'-102.8176','-11.6157','74.5174','342.1942','-77.0177','-28.0621','65.7471','260.3026',2,12000000,2,0,0,2,0,'2023-02-28 17:01:00',471,NULL,NULL,0),
	(998,NULL,'-72.8903','428.3168','113.0382','165.1176','-97.1469','428.6382','113.0642','110.5884',10,190000000,9,0,0,11,0,'2023-03-03 19:49:09',570,NULL,NULL,0),
	(999,NULL,'-166.5115','424.4572','111.8059','2.0015','-183.3019','420.3306','110.7621','29.6135',10,190000000,9,0,0,11,0,'2023-03-11 18:51:14',1311,NULL,NULL,0),
	(1000,NULL,'-214.1302','399.7929','111.3039','14.2044','-200.9242','409.4861','110.6194','20.9608',10,190000000,9,0,0,11,0,'2023-03-27 00:52:01',2507,NULL,NULL,0),
	(1001,NULL,'-239.3387','381.6404','112.427','119.1885','-261.0926','395.6356','110.0207','69.4734',10,190000000,9,0,0,11,0,'2023-03-13 01:10:26',1516,NULL,NULL,0),
	(1002,NULL,'-297.8477','380.3945','112.0953','196.5071','-305.6866','379.9214','110.3364','19.5637',10,190000000,9,0,0,11,0,'2023-03-10 16:37:04',1186,NULL,NULL,0),
	(1003,NULL,'-328.0616','369.6147','110.006','24.26','-347.5971','369.2399','110.0085','72.0695',8,120000000,8,0,0,8,0,'2023-03-10 17:49:10',1194,NULL,NULL,0),
	(1004,NULL,'-371.8187','343.5206','109.9426','3.1253','-381.1065','346.5048','109.2029','0.2527',6,*********,7,0,0,6,0,'2023-03-13 00:25:28',1509,NULL,NULL,0),
	(1005,NULL,'-409.4153','341.4705','108.9074','269.6047','-398.2541','340.287','108.7178','6.0345',8,120000000,8,0,0,8,0,'2023-03-04 21:53:57',646,NULL,NULL,0),
	(1006,NULL,'-444.2036','343.2942','105.5577','13.8671','-431.2097','343.8975','105.9803','35.0924',8,120000000,8,0,0,8,0,'2023-03-06 19:11:58',806,NULL,NULL,0),
	(1007,NULL,'-469.0812','330.1453','104.7471','249.33','-472.4865','352.9604','103.9909','341.7608',8,120000000,8,0,0,8,0,'2023-03-13 16:31:58',1598,NULL,NULL,0),
	(1008,NULL,'-500.2735','398.4551','98.2671','61.6265','-520.7458','394.3139','93.6495','10.0801',10,190000000,9,0,0,11,0,'2023-03-11 06:50:52',1262,NULL,NULL,0),
	(1009,NULL,'-1150.517','-1473.8325','4.3798','315.924','-1155.6023','-1461.2471','4.3956','122.1475',3,24000000,3,0,0,3,0,'2023-03-02 09:44:41',503,NULL,NULL,0),
	(1010,NULL,'-477.2157','413.117','103.1218','190.3936','-488.545','409.4021','99.1786','144.0926',6,*********,7,0,0,6,0,'2023-03-05 04:54:20',669,NULL,NULL,0),
	(1011,NULL,'-516.7567','433.1836','97.8081','121.6537','-514.2117','424.99','96.98','48.7352',10,190000000,9,0,0,11,0,'2023-03-12 20:16:30',1474,NULL,NULL,0),
	(1012,NULL,'-1135.5665','-1468.5327','4.6182','291.8131','-1149.3375','-1457.194','4.6449','32.6495',3,24000000,3,0,0,3,0,'2023-03-25 14:00:01',2300,NULL,NULL,0),
	(1013,NULL,'-1142.0377','-1461.7129','4.6251','19.854','-1141.1887','-1451.7842','4.7602','33.0491',3,24000000,3,0,0,3,0,'2023-03-25 20:29:57',2336,NULL,NULL,0),
	(1014,NULL,'-1145.8765','-1466.1285','4.5042','143.9892','-1126.9066','-1472.1477','4.5646','299.402',3,24000000,3,0,0,3,0,'2023-03-03 21:21:22',577,NULL,NULL,0),
	(1015,NULL,'-1146.1393','-1466.2258','7.6907','66.1559','-1136.9141','-1479.1709','4.3346','301.5089',3,24000000,3,0,0,3,0,'2023-03-12 12:52:00',1399,NULL,NULL,0),
	(1016,NULL,'-1142.266','-1461.4689','7.6907','343.7669','-1153.4536','-1477.6863','4.258','214.3652',3,24000000,3,0,0,3,0,'2023-03-18 19:20:00',1906,NULL,NULL,0),
	(1017,NULL,'-1136.8508','-1466.3319','7.6907','298.4774','-1157.3756','-1471.0449','4.2348','208.9955',3,24000000,3,0,0,3,0,'2023-03-05 22:01:00',728,NULL,NULL,0),
	(1018,NULL,'-536.9781','477.443','103.1936','55.9896','-538.0217','486.3108','102.9559','46.1138',10,190000000,9,0,0,11,0,'2023-03-13 15:19:42',1585,NULL,NULL,0),
	(1019,NULL,'-526.7046','517.145','112.9419','42.9014','-526.4354','529.2622','111.9251','48.0012',10,190000000,9,0,0,11,0,'2023-03-15 22:16:47',1765,NULL,NULL,0),
	(1020,NULL,'-1130.3851','-1496.3066','4.4288','222.3701','-1134.1466','-1489.7063','4.2752','297.9309',3,24000000,3,0,0,3,0,'2023-03-18 13:14:01',1879,NULL,NULL,0),
	(1021,NULL,'-1118.0652','-1487.7983','4.7178','208.8125','-1123.0308','-1481.9247','4.5156','302.1967',3,24000000,3,0,0,3,0,'2023-03-23 13:47:00',2141,NULL,NULL,0),
	(1022,NULL,'-1109.5022','-1482.0632','4.9174','218.018','-1113.6631','-1475.1635','4.7211','303.7003',3,24000000,3,0,0,3,0,'2023-03-05 17:52:57',699,NULL,NULL,0),
	(1023,NULL,'-1090.7181','-1485.3806','5.1254','33.1859','-1085.4545','-1479.5907','4.9679','28.5196',3,24000000,3,0,0,3,0,'2023-03-16 07:01:00',1777,NULL,NULL,0),
	(1024,NULL,'-655.2107','803.3197','198.9912','358.1062','-661.7798','809.8162','199.6914','8.1596',10,2********,9,0,0,11,0,'2023-03-03 01:57:11',531,NULL,NULL,0),
	(1025,NULL,'-595.6188','780.7098','189.1105','122.0353','-590.3613','752.9849','183.7451','259.4028',10,190000000,9,0,0,11,0,'2023-03-13 18:43:08',1615,NULL,NULL,0),
	(1026,NULL,'-1118.2594','-1439.5306','5.1075','214.5285','-1116.048','-1435.1437','4.973','31.068',2,12000000,2,0,0,2,0,'2023-03-06 07:08:54',757,NULL,NULL,0),
	(1027,NULL,'-599.8705','807.3178','191.3049','7.0889','-595.9039','806.1691','190.903','146.9173',10,1********,9,0,0,11,0,'2023-03-12 23:01:00',1498,NULL,NULL,0),
	(1028,NULL,'-579.6738','733.2093','184.2121','178.7242','-577.15','742.2811','183.8976','95.1516',10,190000000,9,0,0,11,0,'2023-03-04 00:03:45',588,NULL,NULL,0),
	(1029,NULL,'-411.6722','152.8631','65.5272','265.9232','-377.0309','139.5994','65.8649','171.4845',2,12000000,2,0,0,2,0,'2023-03-14 12:01:00',1702,NULL,NULL,0),
	(1030,NULL,'-410.2661','159.5378','65.525','260.6696','-379.85','139.4384','65.7957','183.3381',2,12000000,2,0,0,2,0,'2023-03-10 15:09:03',1181,NULL,NULL,0),
	(1031,NULL,'-664.0059','742.296','174.284','90.7204','-671.5927','753.7074','174.0423','0.537',10,190000000,9,0,0,11,0,'2023-07-20 21:25:53',2681,NULL,NULL,0),
	(1032,NULL,'-383.9084','152.8141','65.5313','105.617','-380.0305','138.5578','65.8219','184.0529',2,12000000,2,0,0,2,0,'2023-03-09 11:17:13',1052,NULL,NULL,0),
	(1033,NULL,'-385.1011','159.6125','65.5313','100.9975','-377.2227','138.5701','65.8955','209.4328',2,12000000,2,0,0,2,0,'2023-03-25 21:01:01',2346,NULL,NULL,0),
	(1034,NULL,'-699.2536','706.3989','157.9861','135.4978','-694.6054','708.3685','156.9682','235.8733',10,190000000,9,0,0,11,0,'2023-03-02 22:37:36',521,NULL,NULL,0),
	(1035,NULL,'-1115.0389','-1577.3828','4.5426','221.8402','-1118.3823','-1571.9667','4.2686','306.4231',3,24000000,3,0,0,3,0,'2023-03-21 20:49:30',2063,NULL,NULL,0),
	(1036,NULL,'-385.0466','159.5621','69.7219','100.4401','-373.9795','138.6176','65.9804','175.7771',2,12000000,2,0,0,2,0,'2023-03-24 19:11:00',2244,NULL,NULL,0),
	(1037,NULL,'-383.9529','152.7802','69.7219','108.182','-371.1377','138.523','66.0589','181.0793',2,12000000,2,0,0,2,0,'2023-03-19 07:34:59',1929,NULL,NULL,0),
	(1038,NULL,'-1119.9521','-1582.855','8.6795','151.5275','-1097.6511','-1574.7958','4.2132','27.0451',3,24000000,3,0,0,3,0,'2023-03-13 21:11:00',1653,NULL,NULL,0),
	(1039,NULL,'-1114.2019','-1579.4885','8.6795','253.1138','-1093.3303','-1581.3146','4.2259','29.0101',3,24000000,3,0,0,3,0,'2023-03-24 22:01:00',2266,NULL,NULL,0),
	(1040,NULL,'-661.9543','679.0214','153.9106','74.2985','-666.6716','669.817','150.3876','72.3854',8,120000000,8,0,0,8,0,'2023-03-08 00:02:46',920,NULL,NULL,0),
	(1041,NULL,'-1112.4071','-1578.2452','8.6795','230.1727','-1126.1294','-1577.4845','4.2555','300.5612',3,24000000,3,0,0,3,0,'2023-03-05 22:01:00',730,NULL,NULL,0),
	(1042,NULL,'-385.2654','159.6656','73.733','105.0323','-367.9381','138.5894','66.1428','182.4139',2,12000000,2,0,0,2,0,'2023-03-25 19:01:01',2328,NULL,NULL,0),
	(1043,NULL,'-383.9458','152.7387','73.7342','108.5967','-379.9345','138.7272','65.8182','181.9222',2,12000000,2,0,0,2,0,'2023-03-09 16:01:00',1082,NULL,NULL,0),
	(1044,NULL,'-700.8699','647.2307','155.3706','349.3399','-711.3416','652.636','155.1754','343.6974',10,260000000,9,0,0,11,0,'2023-03-12 01:11:03',1361,NULL,NULL,0),
	(1045,NULL,'-1105.621','-1596.498','4.6071','195.1913','-1106.4025','-1601.6074','4.6693','122.993',3,24000000,3,0,0,3,0,'2023-03-25 08:01:00',2287,NULL,NULL,0),
	(1046,NULL,'-385.1305','159.6348','77.7441','101.3735','-377.2042','138.8726','65.8855','182.095',2,12000000,2,0,0,2,0,'2023-03-02 22:54:15',523,NULL,NULL,0),
	(1047,NULL,'-668.6146','638.7642','149.5289','88.2307','-675.2905','647.4096','148.6599','175.7926',6,*********,7,0,0,6,0,'2023-03-13 21:14:31',1646,NULL,NULL,0),
	(1048,NULL,'-383.9527','152.801','77.7441','103.6395','-373.836','138.9915','65.9716','179.7294',2,12000000,2,0,0,2,0,'2023-03-09 22:54:51',1121,NULL,NULL,0),
	(1049,NULL,'-1093.3206','-1608.078','8.4588','125.2848','-1106.8827','-1620.3936','4.3984','32.5334',3,24000000,3,0,0,3,0,'2023-03-17 09:21:00',1821,NULL,NULL,0),
	(1050,NULL,'-385.1992','159.5001','81.7431','100.0033','-371.4388','138.759','66.0429','183.2253',2,12000000,2,0,0,2,0,'2023-03-09 23:20:40',1127,NULL,NULL,0),
	(1051,NULL,'-686.0088','596.0842','143.8507','39.6665','-684.1249','602.6992','143.5128','328.663',8,160000000,8,0,0,8,0,'2023-03-05 19:28:56',710,NULL,NULL,0),
	(1052,NULL,'-384.0347','152.8932','81.7474','100.3173','-368.0364','139.021','66.125','185.1345',2,12000000,2,0,0,2,0,'2023-03-18 00:41:28',1866,NULL,NULL,0),
	(1053,NULL,'-704.1782','588.7548','141.9306','0.9306','-723.4044','591.8222','141.7424','271.1702',8,120000000,8,0,0,8,0,'2024-03-16 14:06:34',2721,NULL,NULL,0),
	(1054,NULL,'-411.7178','152.7095','81.743','264.1517','-368.1532','139.2134','66.1155','180.9474',2,12000000,2,0,0,2,0,'2023-03-07 13:50:15',862,NULL,NULL,0),
	(1055,NULL,'-733.0262','593.9696','142.2348','330.5998','-743.2114','602.8768','141.9714','248.1879',8,120000000,8,0,0,8,0,'2023-03-27 14:34:21',2556,NULL,NULL,0),
	(1056,NULL,'-410.7089','159.5203','81.743','269.1403','-371.2642','139.3719','66.0271','175.373',2,12000000,2,0,0,2,0,'2023-02-28 02:05:53',472,NULL,NULL,0),
	(1057,NULL,'-753.259','620.3776','142.8069','289.4772','-754.3898','629.1014','142.6377','204.1015',8,152000000,8,0,0,8,0,'2023-03-06 05:51:52',752,NULL,NULL,0),
	(1058,NULL,'-412.0162','152.7644','77.7441','250.8347','-373.704','139.5985','65.9547','176.2327',2,12000000,2,0,0,2,0,'2023-03-08 14:32:06',970,NULL,NULL,0),
	(1059,NULL,'-1202.9988','-945.2635','8.1149','124.6199','-1181.1793','-957.7766','3.7845','116.0792',2,12000000,2,0,0,2,0,'2023-03-13 09:11:00',1543,NULL,NULL,0),
	(1060,NULL,'-1204.3073','-943.0699','8.1149','348.1887','-1186.2114','-948.5983','4.0611','207.0345',2,12000000,2,0,0,2,0,'2023-03-26 08:52:31',2396,NULL,NULL,0),
	(1061,NULL,'-410.5972','159.4952','77.7441','258.4991','-376.6957','139.409','65.8803','181.4705',2,12000000,2,0,0,2,0,'2023-03-11 16:54:59',1292,NULL,NULL,0),
	(1062,NULL,'-765.4738','650.7014','145.7005','299.2655','-768.18','668.3875','144.833','268.7181',10,220000000,9,0,0,11,0,'2023-03-11 13:16:18',1278,NULL,NULL,0),
	(1063,NULL,'-411.8404','152.7669','73.7343','253.8102','-379.669','139.2322','65.8079','166.4537',2,12000000,2,0,0,2,0,'2023-03-11 11:59:52',1269,NULL,NULL,0),
	(1064,NULL,'-1200.0663','-951.1704','8.1155','32.4818','-1188.6542','-952.6639','4.2651','204.9774',2,12000000,2,0,0,2,0,'2023-03-01 09:01:00',473,NULL,NULL,0),
	(1065,NULL,'-1078.441','-1616.4609','4.4299','104.1719','-1076.4968','-1605.1993','4.2499','32.7022',3,24000000,3,0,0,3,0,'2023-03-15 23:38:00',1767,NULL,NULL,0),
	(1066,NULL,'-410.6496','159.4195','73.733','251.8534','-367.8078','139.8069','66.1085','187.5864',2,12000000,2,0,0,2,0,'2023-03-24 14:01:00',2220,NULL,NULL,0),
	(1067,NULL,'-1205.047','-945.8496','3.7596','302.8606','-1192.8989','-952.812','4.5769','205.4666',2,12000000,2,0,0,2,0,'2023-03-06 09:01:00',761,NULL,NULL,0),
	(1068,NULL,'-819.4688','696.959','148.1096','18.3946','-809.0226','704.5793','147.0742','8.6471',8,156000000,8,0,0,8,0,'2023-03-01 18:19:04',482,NULL,NULL,0),
	(1069,NULL,'-411.7228','152.7711','69.7219','260.5641','-371.3148','139.7985','66.0115','183.6927',2,12000000,2,0,0,2,0,'2023-03-13 22:53:22',1665,NULL,NULL,0),
	(1070,NULL,'-1076.9307','-1620.9299','4.4617','122.8117','-1072.1074','-1611.8037','4.2425','34.1608',3,24000000,3,0,0,3,0,'2023-03-22 16:51:00',2097,NULL,NULL,0),
	(1071,NULL,'-410.5302','159.5691','69.7219','258.163','-373.7068','139.5659','65.9557','174.0607',2,12000000,2,0,0,2,0,'2023-03-06 20:11:00',814,NULL,NULL,0),
	(1072,NULL,'-853.015','695.3831','148.7879','5.3563','-863.655','699.1774','149.0581','328.127',8,120000000,8,0,0,8,0,'2023-03-05 23:37:57',735,NULL,NULL,0),
	(1073,NULL,'-885.2218','699.3317','151.2707','80.564','-892.3902','704.2485','150.2758','268.6148',10,190000000,9,0,0,11,0,'2023-03-10 00:17:28',1130,NULL,NULL,0),
	(1074,NULL,'-908.6979','694.1183','151.4345','358.1235','-910.3391','696.402','151.3906','318.5951',6,120000000,7,0,0,6,0,'2023-03-13 14:07:50',1572,NULL,NULL,0),
	(1075,NULL,'-931.4686','691.4654','153.4667','359.2484','-950.6347','691.5167','153.5838','9.6',8,148000000,8,0,0,8,0,'2023-03-13 15:30:19',1587,NULL,NULL,0),
	(1076,NULL,'-1163.8893','-905.3133','6.5461','28.5881','-1168.0656','-915.6217','2.6746','207.332',3,24000000,3,0,0,3,0,'2023-03-15 19:21:43',1758,NULL,NULL,0),
	(1077,NULL,'-1172.307','-910.2151','6.5466','358.9511','-1171.2087','-917.5428','2.6462','204.179',3,24000000,3,0,0,3,0,'2023-03-05 15:33:54',684,NULL,NULL,0),
	(1078,NULL,'-1180.7649','-914.9395','6.5441','340.9838','-1174.7698','-919.512','2.647','205.3152',3,24000000,3,0,0,3,0,'2023-03-11 15:10:04',1285,NULL,NULL,0),
	(1079,NULL,'-1179.9081','-929.3506','6.6288','231.5439','-1177.972','-921.5095','2.6487','204.4538',3,24000000,3,0,0,3,0,'2023-03-26 12:01:00',2408,NULL,NULL,0),
	(1080,NULL,'-973.8378','684.8488','158.0343','357.2905','-985.7537','693.38','157.5449','268.8174',10,170000000,9,0,0,11,0,'2023-03-02 19:16:12',515,NULL,NULL,0),
	(1081,NULL,'-1154.7357','-931.6505','2.662','207.8528','-1146.0366','-936.7346','2.499','297.3885',3,24000000,3,0,0,3,0,'2023-03-09 18:31:02',1093,NULL,NULL,0),
	(1082,NULL,'-1151.5437','-913.345','6.6288','210.9934','-1186.2814','-927.7484','2.7244','209.6952',3,24000000,3,0,0,3,0,'2023-03-10 13:51:19',1174,NULL,NULL,0),
	(1083,NULL,'-1033.2712','685.9954','161.3027','279.3794','-1022.2961','694.1663','161.2625','347.202',10,240000000,9,0,0,11,0,'2023-03-09 17:01:03',1083,NULL,NULL,0),
	(1084,NULL,'-160.9125','160.8167','81.7019','73.3001','-199.7631','171.9437','70.3286','166.8279',2,12000000,2,0,0,2,0,'2023-03-13 23:42:20',1677,NULL,NULL,0),
	(1085,NULL,'-1111.8701','-902.1886','3.5966','124.3108','-1092.1155','-889.7605','3.587','212.7003',3,24000000,3,0,0,3,0,'2023-03-20 02:30:00',1976,NULL,NULL,0),
	(1086,NULL,'-1019.584','719.1641','163.9961','169.0193','-1009.6989','706.6069','161.3052','244.651',6,*********,7,0,0,6,0,'2023-03-09 21:15:43',1109,NULL,NULL,0),
	(1087,NULL,'-158.2335','168.4396','85.7019','81.9371','-196.0192','172.5781','70.3286','169.7329',2,12000000,2,0,0,2,0,'2023-03-15 01:23:13',1730,NULL,NULL,0),
	(1088,NULL,'-1116.2373','-895.3419','7.7969','113.1614','-1097.1748','-883.076','3.587','209.803',3,24000000,3,0,0,3,0,'2023-03-19 20:32:10',1958,NULL,NULL,0),
	(1089,NULL,'-160.9151','161.0665','85.7064','87.0892','-188.8768','171.2727','70.3285','169.6838',2,12000000,2,0,0,2,0,'2023-03-21 14:12:55',2050,NULL,NULL,0),
	(1090,NULL,'-1111.6477','-902.2897','7.7962','100.1451','-1081.76','-894.439','3.8426','208.6622',3,24000000,3,0,0,3,0,'2023-03-06 21:11:00',821,NULL,NULL,0),
	(1091,NULL,'-158.1571','168.4615','89.7028','69.2942','-181.4025','171.464','70.3286','169.2374',2,12000000,2,0,0,2,0,'2023-03-25 23:09:00',2358,NULL,NULL,0),
	(1092,NULL,'-1116.506','-895.8543','11.7967','184.8267','-1084.6311','-896.1116','3.6585','207.0239',3,24000000,3,0,0,3,0,'2023-03-26 15:03:47',2432,NULL,NULL,0),
	(1093,NULL,'-160.839','161.0108','89.7021','64.2104','-144.3911','198.2057','90.2627','336.1019',2,12000000,2,0,0,2,0,'2023-03-04 13:40:37',615,NULL,NULL,0),
	(1094,NULL,'-1111.7578','-902.1364','11.7967','142.7789','-1088.6844','-897.6645','3.4876','208.7092',3,24000000,3,0,0,3,0,'2023-03-25 20:09:15',2337,NULL,NULL,0),
	(1095,NULL,'-1065.1351','727.0017','165.4747','20.86','-1057.9344','734.73','165.4497','318.9737',10,260000000,9,0,0,11,0,'2023-03-13 21:42:44',1649,NULL,NULL,0),
	(1096,NULL,'-143.4162','175.0476','85.427','172.1166','-136.3537','187.5667','85.4269','165.252',2,12000000,2,0,0,2,0,'2023-03-25 00:00:01',2270,NULL,NULL,0),
	(1097,NULL,'-150.0434','177.0907','85.427','154.1005','-134.9454','181.4667','85.4269','247.9178',2,12000000,2,0,0,2,0,'2023-03-25 10:01:00',2291,NULL,NULL,0),
	(1098,NULL,'-595.4332','530.0988','107.7553','199.9075','-587.5198','529.5636','107.9503','215.7645',10,190000000,9,0,0,11,0,'2023-03-05 14:11:26',681,NULL,NULL,0),
	(1099,NULL,'-150.1184','177.2883','89.6275','160.8074','-139.6124','177.4673','85.4269','340.5728',2,12000000,2,0,0,2,0,'2023-03-16 18:05:41',1800,NULL,NULL,0),
	(1100,NULL,'-143.4385','174.8787','89.6275','154.6028','-137.7935','182.9742','85.4269','252.3255',2,12000000,2,0,0,2,0,'2023-03-09 22:12:02',1122,NULL,NULL,0),
	(1101,NULL,'-622.8307','489.1819','108.8585','189.9182','-615.012','496.9521','107.2223','96.0663',10,190000000,9,0,0,11,0,'2023-03-13 11:19:39',1553,NULL,NULL,0),
	(1102,NULL,'-1071.3047','-1636.2896','8.1942','126.2846','-1060.2715','-1626.2378','4.2524','31.0971',3,24000000,3,0,0,3,0,'2023-03-12 23:01:00',1499,NULL,NULL,0),
	(1103,NULL,'-640.7816','519.871','109.6877','1.7369','-629.1912','518.4951','109.6431','187.4019',10,190000000,9,0,0,11,0,'2023-03-05 20:01:57',712,NULL,NULL,0),
	(1104,NULL,'-149.8659','177.3276','93.6266','164.6774','-142.4897','189.8112','85.4271','157.6059',2,12000000,2,0,0,2,0,'2023-03-10 23:42:21',1243,NULL,NULL,0),
	(1105,NULL,'-143.5795','174.8457','93.6266','156.8154','-146.6694','181.3757','85.427','334.0776',2,12000000,2,0,0,2,0,'2023-03-11 23:42:20',1342,NULL,NULL,0),
	(1106,NULL,'-580.426','492.0137','108.9029','2.6179','-575.1019','496.6248','106.5654','10.5963',10,190000000,9,0,0,11,0,'2023-03-10 22:46:56',1235,NULL,NULL,0),
	(1107,NULL,'-1062.8126','-1641.4069','4.4918','130.2591','-1055.7542','-1632.396','4.2409','33.4514',3,24000000,3,0,0,3,0,'2023-03-19 15:21:00',1941,NULL,NULL,0),
	(1108,NULL,'-1058.2378','-1643.2401','4.4886','138.0033','-1051.6418','-1637.1864','4.239','34.3029',3,24000000,3,0,0,3,0,'2023-03-23 11:01:00',2133,NULL,NULL,0),
	(1109,NULL,'-678.8541','511.563','113.526','16.1519','-686.5026','500.3305','110.0286','199.4207',10,190000000,9,0,0,11,0,'2023-03-07 23:53:50',914,NULL,NULL,0),
	(1110,NULL,'-667.296','471.9393','114.1365','211.1146','-657.0237','490.3589','109.7506','80.6944',10,190000000,9,0,0,11,0,'2023-03-13 20:17:35',1634,NULL,NULL,0),
	(1111,NULL,'-1058.5789','-1657.3352','4.6726','230.2062','-1066.8195','-1670.3136','4.4755','129.6875',3,24000000,3,0,0,3,0,'2023-03-23 08:55:23',2130,NULL,NULL,0),
	(1112,NULL,'-1059.9724','-1658.5115','4.6734','193.9701','-1069.4514','-1667.2164','4.4215','127.0311',3,24000000,3,0,0,3,0,'2023-03-08 10:07:04',944,NULL,NULL,0),
	(1113,NULL,'-721.4459','490.1532','109.3857','29.1137','-715.7144','498.0403','109.2792','200.2877',10,190000000,9,0,0,11,0,'2023-03-10 23:01:00',1244,NULL,NULL,0),
	(1114,NULL,'-718.0082','448.9746','106.9091','207.8692','-737.1627','443.5228','106.8726','15.7888',10,276000000,9,0,0,11,0,'2023-03-03 12:30:39',543,NULL,NULL,0),
	(1115,NULL,'-824.7921','422.3874','92.1246','187.0779','-806.3015','425.6456','91.5889','1.7418',10,190000000,9,0,0,11,0,'2023-03-13 13:11:00',1566,NULL,NULL,0),
	(1116,NULL,'-1088.0791','-1671.8175','4.7019','117.906','-1077.7192','-1662.4417','4.3984','40.947',3,24000000,3,0,0,3,0,'2023-03-10 07:51:15',1158,NULL,NULL,0),
	(1117,NULL,'-833.6567','456.2606','89.8989','187.7253','-845.2178','459.4408','87.7529','99.3895',10,190000000,9,0,0,11,0,'2023-03-04 17:47:43',624,NULL,NULL,0),
	(1118,NULL,'-1097.6328','-1673.0544','8.394','306.5721','-1108.5389','-1678.0234','4.3067','31.1009',3,24000000,3,0,0,3,0,'2023-03-27 08:01:00',2535,NULL,NULL,0),
	(1119,NULL,'-866.7553','457.4533','88.2811','193.7318','-862.405','463.1274','87.9372','286.5307',10,190000000,9,0,0,11,0,'2023-03-12 17:28:13',1441,NULL,NULL,0),
	(1120,NULL,'-848.7642','508.8681','90.817','14.7561','-849.4534','519.8787','90.6223','107.2304',10,190000000,9,0,0,11,0,'2023-03-05 15:13:16',686,NULL,NULL,0),
	(1121,NULL,'-884.2226','518.0415','92.4429','141.8451','-871.1093','499.7058','89.8652','280.1563',10,190000000,9,0,0,11,0,'2023-03-06 04:47:58',751,NULL,NULL,0),
	(1122,NULL,'-873.7291','562.5408','96.6194','291.8327','-875.0189','538.7363','91.5218','42.1413',10,190000000,9,0,0,11,0,'2023-03-13 17:55:36',1605,NULL,NULL,0),
	(1123,NULL,'-924.8205','561.3975','100.1576','334.1952','-933.5544','569.5724','99.9971','264.3895',8,120000000,8,0,0,8,0,'2023-03-06 22:40:30',823,NULL,NULL,0),
	(1124,NULL,'-904.6517','587.6768','101.1885','334.718','-912.008','587.7833','101.0085','150.9041',10,190000000,9,0,0,11,0,'2023-03-03 16:33:06',552,NULL,NULL,0),
	(1125,NULL,'-1043.697','-1579.8551','5.0406','203.8893','-1047.4387','-1566.9518','4.8421','300.0732',4,40000000,5,0,0,4,0,'2023-03-16 07:34:04',1778,NULL,NULL,0),
	(1126,NULL,'-1048.6144','-1580.6975','4.9288','117.4865','-1054.907','-1569.4171','4.7379','126.3116',4,40000000,5,0,0,4,0,'2023-03-27 07:49:50',2534,NULL,NULL,0),
	(1127,NULL,'-958.1769','606.6917','106.1773','342.7448','-946.822','592.7411','101.0049','171.3305',10,190000000,9,0,0,11,0,'2023-03-08 14:23:12',971,NULL,NULL,0),
	(1128,NULL,'-1039.0413','-1610.0081','4.9927','160.4853','-1045.6478','-1604.3843','4.8218','28.6645',4,40000000,5,0,0,4,0,'2023-03-12 18:56:54',1458,NULL,NULL,0),
	(1129,NULL,'-155.0092','214.2844','94.8084','263.805','-133.6478','195.5677','89.9809','339.046',2,12000000,2,0,0,2,0,'2023-03-18 09:51:18',1876,NULL,NULL,0),
	(1130,NULL,'-148.8304','215.3726','94.8084','184.2031','-128.462','194.5257','89.9286','339.7079',2,12000000,2,0,0,2,0,'2023-03-13 09:00:47',1544,NULL,NULL,0),
	(1131,NULL,'-947.8383','567.7795','101.5051','161.8061','-948.3154','572.6789','101.2742','343.3092',10,190000000,9,0,0,11,0,'2023-03-02 23:30:12',525,NULL,NULL,0),
	(1132,NULL,'-140.6032','215.3851','94.8084','183.8045','-129.1622','209.9223','93.0625','175.4095',2,12000000,2,0,0,2,0,'2023-03-07 21:04:04',905,NULL,NULL,0),
	(1133,NULL,'-1056.8947','-1587.5083','4.61','20.4554','-1048.1118','-1600.0568','4.5672','120.6218',4,40000000,5,0,0,4,0,'2023-03-08 18:41:18',991,NULL,NULL,0),
	(1134,NULL,'-974.3755','582.0687','102.9218','168.2426','-985.3796','586.5939','102.3012','25.3413',10,190000000,9,0,0,11,0,'2023-03-01 23:14:55',492,NULL,NULL,0),
	(1135,NULL,'-1022.6743','587.031','103.4291','179.334','-1039.2712','593.3267','103.0841','99.3731',10,190000000,9,0,0,11,0,'2023-03-27 19:42:37',2605,NULL,NULL,0),
	(1136,NULL,'-1027.4187','-1575.432','5.1362','137.8071','-1021.455','-1570.4526','5.004','31.2438',4,40000000,5,0,0,4,0,'2023-03-12 07:29:19',1383,NULL,NULL,0),
	(1137,NULL,'-1057.3466','-1551.4122','4.9051','309.4315','-1067.5392','-1537.2301','4.7481','300.0094',4,40000000,5,0,0,4,0,'2023-03-07 08:10:24',851,NULL,NULL,0),
	(1138,NULL,'-1090.3741','548.295','103.6333','30.8614','-1105.7667','551.4064','102.6051','45.7942',8,120000000,8,0,0,8,0,'2023-03-09 23:03:50',1126,NULL,NULL,0),
	(1140,NULL,'-1107.4688','593.9648','104.4547','31.5137','-1092.0575','594.8257','103.0646','209.973',10,190000000,9,0,0,11,0,'2023-03-13 02:44:49',1517,NULL,NULL,0),
	(1141,NULL,'-1063.4966','-1558.5217','4.9228','219.173','-1065.207','-1566.4199','4.5587','208.8342',4,40000000,5,0,0,4,0,'2023-03-03 22:28:26',581,NULL,NULL,0),
	(1142,NULL,'-1125.4209','548.7285','102.5725','104.0377','-1136.153','552.8647','101.777','104.616',10,190000000,9,0,0,11,0,'2023-03-09 14:30:02',1061,NULL,NULL,0),
	(1143,NULL,'-133.491','215.3964','98.3293','179.0649','-137.4455','210.5103','93.099','172.4992',2,12000000,2,0,0,2,0,'2023-03-15 15:01:00',1746,NULL,NULL,0),
	(1144,NULL,'-1072.8778','-1562.0504','4.6917','322.682','-140.4398','210.2472','93.0322','173.7033',4,40000000,5,0,0,4,0,'2023-03-12 14:03:05',1404,NULL,NULL,0),
	(1145,NULL,'-145.7643','215.3928','98.3293','185.149','-143.0214','210.3876','93.0333','175.3541',2,12000000,2,0,0,2,0,'2023-03-14 09:29:27',1694,NULL,NULL,0),
	(1146,NULL,'-154.8775','214.4005','98.3293','274.7601','-146.1412','210.7775','93.062','176.7889',2,12000000,2,0,0,2,0,'2023-03-27 16:32:51',2569,NULL,NULL,0),
	(1147,NULL,'-1167.0336','568.5051','101.8274','188.4925','-1158.2246','567.3328','101.8325','199.1547',10,190000000,9,0,0,11,0,'2023-03-04 06:59:09',606,NULL,NULL,0),
	(1148,NULL,'-1084.6315','-1559.0444','4.5828','219.8872','-1068.9541','-1577.8154','4.4144','122.0771',2,12000000,2,0,0,2,0,'2023-03-11 14:46:40',1281,NULL,NULL,0),
	(1149,NULL,'-1146.5128','545.8907','101.9076','7.6113','-1158.858','547.2081','100.7647','90.7395',8,120000000,8,0,0,8,0,'2023-03-08 15:12:04',976,NULL,NULL,0),
	(1150,NULL,'35.3946','6662.437','32.1904','171.0593','21.8814','6658.0322','31.5121','197.2546',3,24000000,3,0,0,3,0,'2023-03-13 11:43:35',1554,NULL,NULL,0),
	(1151,NULL,'-9.445','6653.9346','31.4982','204.6573','-15.5877','6645.686','31.1098','201.7748',3,24000000,3,0,0,3,0,'2023-03-19 07:24:36',1930,NULL,NULL,0),
	(1152,NULL,'-1193.0375','563.7784','100.3395','3.2624','-1208.8666','556.5947','99.0708','188.6988',8,120000000,8,0,0,8,0,'2023-03-05 23:25:04',736,NULL,NULL,0),
	(1153,NULL,'20.2899','114.2878','83.2733','259.0909','33.0639','101.4684','79.0241','245.2008',2,12000000,2,0,0,2,0,'2023-03-05 03:01:01',667,NULL,NULL,0),
	(1154,NULL,'1.4572','6612.9956','32.036','26.817','-6.0016','6618.647','31.4545','38.423',3,24000000,3,0,0,3,0,'2023-03-13 13:15:57',1569,NULL,NULL,0),
	(1155,NULL,'20.3668','114.2626','87.2773','246.703','31.2218','97.5308','79.0241','246.9623',2,12000000,2,0,0,2,0,'2023-03-18 19:20:21',1907,NULL,NULL,0),
	(1156,NULL,'-41.1924','6637.1958','31.0875','230.2521','-52.3246','6622.8936','29.9463','231.4705',3,24000000,3,0,0,3,0,'2023-03-25 08:15:50',2288,NULL,NULL,0),
	(1157,NULL,'-27.0206','6597.8374','31.8607','38.5788','-20.7141','6612.6177','30.6468','42.4864',4,30000000,4,0,0,4,0,'2023-03-12 20:47:03',1475,NULL,NULL,0),
	(1158,NULL,'-44.6961','6582.3721','32.1755','48.5134','-39.9273','6592.9116','30.4805','43.147',3,24000000,3,0,0,3,0,'2023-03-08 13:23:36',951,NULL,NULL,0),
	(1159,NULL,'-968.2709','436.7636','80.5714','77.4987','-966.9207','449.2283','79.8089','198.7785',10,*********,9,0,0,11,0,'2023-03-11 21:44:41',1334,NULL,NULL,0),
	(1160,NULL,'-130.7815','6551.8662','29.8726','225.0088','-120.027','6559.1299','29.5204','250.3732',4,30000000,4,0,0,4,0,'2023-03-06 19:54:08',807,NULL,NULL,0),
	(1161,NULL,'107.0172','54.6379','77.7694','241.7017','117.5759','37.8656','73.5203','246.1677',2,12000000,2,0,0,2,0,'2023-03-11 17:55:32',1307,NULL,NULL,0),
	(1162,NULL,'-950.0371','465.0859','80.8005','114.0892','-953.8721','450.6316','79.6236','198.8367',10,*********,9,0,0,11,0,'2023-03-26 17:01:00',2449,NULL,NULL,0),
	(1163,NULL,'-105.4434','6529.0908','30.1669','33.3599','-106.6607','6536.5278','29.8092','50.8385',3,24000000,3,0,0,3,0,'2023-03-13 07:42:14',1532,NULL,NULL,0),
	(1164,NULL,'107.0064','54.6918','81.7733','257.4505','118.7183','42.2175','73.5201','245.916',2,12000000,2,0,0,2,0,'2023-03-27 20:01:00',2620,NULL,NULL,0),
	(1165,NULL,'-987.2952','487.5713','82.4577','188.2457','-993.9273','489.7239','82.2662','4.6927',10,190000000,9,0,0,11,0,'2023-03-11 14:32:16',1279,NULL,NULL,0),
	(1166,NULL,'92.4261','49.3415','73.5209','84.6455','124.5718','39.0295','73.1198','243.3031',2,12000000,2,0,0,2,0,'2023-03-23 09:01:48',2132,NULL,NULL,0),
	(1167,NULL,'-967.5191','509.9838','81.867','325.2339','-978.9023','516.177','81.4714','148.051',10,190000000,9,0,0,11,0,'2023-03-06 22:28:28',827,NULL,NULL,0),
	(1168,NULL,'-214.4174','6444.7656','31.3135','317.1866','-223.6983','6435.1182','31.1974','228.4389',3,24000000,3,0,0,3,0,'2023-03-27 14:53:10',2557,NULL,NULL,0),
	(1169,NULL,'-1007.1015','513.1249','79.5954','11.346','-1010.3118','509.6143','79.5334','187.8291',8,120000000,8,0,0,8,0,'2023-03-04 20:41:45',643,NULL,NULL,0),
	(1170,NULL,'-237.7047','6423.4072','31.2573','202.8689','-232.5183','6419.8125','31.2391','217.1843',3,24000000,3,0,0,3,0,'2023-03-02 08:01:00',501,NULL,NULL,0),
	(1171,NULL,'-1009.3206','479.6136','79.4146','152.2832','-1013.5232','489.0871','79.2695','22.6853',10,190000000,9,0,0,11,0,'2023-03-08 01:39:21',924,NULL,NULL,0),
	(1172,NULL,'-189.1025','6409.8911','32.3121','48.2043','-187.1496','6418.1304','31.8625','45.2426',3,24000000,3,0,0,3,0,'2023-03-03 12:18:09',544,NULL,NULL,0),
	(1173,NULL,'208.2491','74.1979','87.8971','246.8671','188.9444','62.8009','83.5881','70.6044',2,12000000,2,0,0,2,0,'2023-03-13 22:01:00',1667,NULL,NULL,0),
	(1174,NULL,'205.2385','66.9052','92.0956','247.2698','201.5684','58.3754','83.6418','74.4942',2,12000000,2,0,0,2,0,'2023-03-22 19:13:18',2103,NULL,NULL,0),
	(1175,NULL,'-214.0239','6396.2271','33.0851','57.5097','-206.5276','6408.208','31.7524','45.3353',4,30000000,4,0,0,4,0,'2023-03-09 02:23:24',1040,NULL,NULL,0),
	(1176,NULL,'-1040.1315','507.9657','84.3809','27.2291','-1045.4834','500.1119','84.1615','241.5391',10,190000000,9,0,0,11,0,'2023-03-05 21:08:35',722,NULL,NULL,0),
	(1177,NULL,'207.9847','74.4214','92.0956','248.2272','198.791','59.3402','83.6133','70.5734',2,12000000,2,0,0,2,0,'2023-03-10 22:57:05',1238,NULL,NULL,0),
	(1178,NULL,'-1062.4446','475.6979','81.3205','233.0243','-1081.123','458.3591','77.2857','147.6838',10,190000000,9,0,0,11,0,'2023-03-12 20:58:53',1476,NULL,NULL,0),
	(1179,NULL,'-227.3793','6377.6963','31.7592','45.7015','-219.671','6383.0103','31.6053','219.5231',3,24000000,3,0,0,3,0,'2023-03-24 13:17:09',2217,NULL,NULL,0),
	(1180,NULL,'-1350.0948','-1161.5623','4.5079','273.8015','-1319.1196','-1141.369','4.4989','92.2958',3,28000000,3,0,0,3,0,'2023-03-26 07:17:19',2394,NULL,NULL,0),
	(1181,NULL,'205.3667','67.0165','96.0958','247.3346','196.0384','60.3101','83.6029','70.5833',2,12000000,2,0,0,2,0,'2023-03-16 16:34:13',1790,NULL,NULL,0),
	(1182,NULL,'208.0128','74.4911','96.0958','249.781','193.2716','61.2776','83.5951','70.59',2,12000000,2,0,0,2,0,'2023-03-07 08:25:49',852,NULL,NULL,0),
	(1183,NULL,'-1052.0354','432.0811','77.0636','191.0618','-1066.5062','437.409','73.8639','93.7805',10,190000000,9,0,0,11,0,'2023-03-10 13:56:58',1171,NULL,NULL,0),
	(1184,NULL,'-248.1096','6370.3496','31.8567','49.3798','-253.7638','6358.7598','31.4809','218.7457',3,24000000,3,0,0,3,0,'2023-03-27 10:30:03',2539,NULL,NULL,0),
	(1185,NULL,'-1094.7303','427.2546','75.88','260.5089','-1086.5465','436.6052','74.7494','255.2263',10,*********,9,0,0,11,0,'2023-03-12 23:51:59',1496,NULL,NULL,0),
	(1186,NULL,'-1336.8796','-1169.887','4.5649','185.0925','-1319.3174','-1148.406','4.4989','92.528',3,28000000,3,0,0,3,0,'2023-03-08 09:34:07',941,NULL,NULL,0),
	(1187,NULL,'-272.3778','6400.5337','31.5049','206.527','-262.6557','6404.1357','30.9839','207.736',3,24000000,3,0,0,3,0,'2023-03-22 12:42:12',2086,NULL,NULL,0),
	(1188,NULL,'233.3636','75.947','87.9242','344.0878','190.1742','64.4325','83.6537','68.6899',2,12000000,2,0,0,2,0,'2023-03-10 04:01:00',1149,NULL,NULL,0),
	(1189,NULL,'-1122.6587','486.1181','82.3557','166.1481','-1113.4625','472.1998','81.4906','184.2331',10,190000000,9,0,0,11,0,'2023-03-08 18:28:40',994,NULL,NULL,0),
	(1190,NULL,'221.5713','80.2171','87.9243','337.0596','190.9789','63.411','83.6367','69.4354',2,12000000,2,0,0,2,0,'2023-02-27 21:51:59',592,NULL,NULL,0),
	(1191,NULL,'-280.8668','6351.2148','32.6008','47.7555','-268.3754','6355.4956','32.4902','40.6152',3,24000000,3,0,0,3,0,'2023-03-01 20:36:19',485,NULL,NULL,0),
	(1192,NULL,'-1335.8116','-1146.9395','6.7314','200.611','-1319.2682','-1144.6814','4.4989','85.5826',3,28000000,3,0,0,3,0,'2023-03-13 14:57:28',1575,NULL,NULL,0),
	(1193,NULL,'-302.3871','6327.502','32.8867','34.6515','-293.8593','6337.8608','32.3588','40.6078',4,30000000,4,0,0,4,0,'2023-03-09 21:17:12',1112,NULL,NULL,0),
	(1194,NULL,'-1158.9475','481.7323','86.0937','181.5164','-1164.9189','476.9456','85.9448','187.9852',10,190000000,9,0,0,11,0,'2023-03-04 13:25:37',614,NULL,NULL,0),
	(1195,NULL,'-1174.5612','440.2901','86.8498','84.9291','-1179.5731','456.4695','86.6694','77.9493',10,190000000,9,0,0,11,0,'2023-03-10 18:55:06',1203,NULL,NULL,0),
	(1196,NULL,'-332.9577','6302.6855','33.0881','42.7522','-314.7238','6311.2935','32.3784','47.5291',4,30000000,4,0,0,4,0,'2023-03-01 22:19:49',491,NULL,NULL,0),
	(1197,NULL,'-1217.5792','506.3559','95.667','169.7787','-1238.2124','486.6354','93.2452','130.0642',10,240000000,9,0,0,11,0,'2023-03-06 17:08:42',790,NULL,NULL,0),
	(1198,NULL,'-359.7039','6334.4658','29.8488','220.6658','-353.5922','6334.7476','29.8336','222.8906',4,30000000,4,0,0,4,0,'2023-03-13 23:23:15',1681,NULL,NULL,0),
	(1199,NULL,'-1215.7633','458.1234','92.0638','1.0345','-1230.5682','460.954','91.8503','5.8087',10,190000000,9,0,0,11,0,'2023-03-12 17:14:28',1442,NULL,NULL,0),
	(1200,NULL,'-406.9588','6313.6714','28.9424','224.9565','-392.761','6307.2178','29.3563','213.7945',3,24000000,3,0,0,3,0,'2023-03-27 02:11:00',2517,NULL,NULL,0),
	(1201,NULL,'-1277.4456','497.0443','97.8904','83.8346','-1268.5499','505.4285','97.08','187.5705',10,*********,9,0,0,11,0,'2023-03-04 00:08:16',590,NULL,NULL,0),
	(1202,NULL,'175.116','45.6404','84.2174','158.1841','211.8845','53.6843','83.8393','250.6909',2,12000000,2,0,0,2,0,'2023-03-12 17:33:04',1444,NULL,NULL,0),
	(1203,NULL,'191.5477','39.6226','84.2175','149.1737','214.8039','52.6695','83.9342','250.6855',2,12000000,2,0,0,2,1,'2023-03-07 00:01:01',557,NULL,NULL,0),
	(1204,NULL,'-1258.7526','446.9314','94.7356','309.2313','-1271.9254','451.6828','94.9752','15.4402',6,*********,7,0,0,6,0,'2023-03-10 21:31:48',1223,NULL,NULL,0),
	(1205,NULL,'-1308.1178','449.322','100.9697','359.2708','-1323.2847','450.641','99.7217','3.6354',10,190000000,9,0,0,11,0,'2023-03-05 10:48:55',673,NULL,NULL,0),
	(1206,NULL,'191.0712','39.7568','87.8226','159.3277','218.2513','51.4694','83.9661','250.702',2,12000000,2,0,0,2,0,'2023-03-16 01:01:01',1770,NULL,NULL,0),
	(1207,NULL,'188.7945','40.6416','87.8226','161.0778','221.8974','50.1958','84.0003','250.7167',2,12000000,2,0,0,2,0,'2023-03-12 04:01:00',1378,NULL,NULL,0),
	(1208,NULL,'-1371.5769','444.0168','105.8571','89.2367','-1377.0767','452.9545','104.8878','87.5113',10,190000000,9,0,0,11,0,'2023-03-25 21:58:43',2347,NULL,NULL,0),
	(1209,NULL,'178.9946','44.2347','87.8226','160.9239','224.6222','49.206','84.0331','249.8671',2,12000000,2,0,0,2,0,'2023-03-25 13:05:00',2297,NULL,NULL,0),
	(1210,NULL,'176.6273','45.0192','87.8226','164.9684','226.8414','48.3984','84.0566','249.8345',2,12000000,2,0,0,2,0,'2023-03-18 10:17:03',1877,NULL,NULL,0),
	(1211,NULL,'-359.8615','6260.7896','31.8907','307.9531','-359.2047','6272.3081','30.9385','37.409',3,24000000,3,0,0,3,0,'2023-03-13 07:56:21',1533,NULL,NULL,0),
	(1212,NULL,'-1342.9836','481.3802','102.7619','91.4823','-1362.3578','492.3861','103.8928','131.2094',10,190000000,9,0,0,11,0,'2023-03-12 14:26:46',1406,NULL,NULL,0),
	(1213,NULL,'-379.8386','6253.374','31.8512','327.8764','-388.7706','6272.4478','30.1763','44.7921',3,24000000,3,0,0,3,0,'2023-03-18 13:31:00',1880,NULL,NULL,0),
	(1214,NULL,'-437.8095','6272.728','30.0683','247.1562','-427.4735','6260.9136','30.4738','253.8989',4,30000000,4,0,0,4,0,'2023-03-11 23:24:49',1349,NULL,NULL,0),
	(1215,NULL,'284.0957','47.3584','92.6615','244.4061','273.8589','26.5703','84.088','255.3651',2,12000000,2,0,0,2,0,'2023-03-23 11:01:00',2134,NULL,NULL,0),
	(1216,NULL,'284.0721','47.384','96.682','252.4992','276.9408','25.4779','84.088','250.1809',2,12000000,2,0,0,2,0,'2023-03-08 16:41:34',985,NULL,NULL,0),
	(1217,NULL,'-467.7836','6206.3794','29.5528','288.0009','-431.4351','6208.9907','30.0043','285.8423',3,24000000,3,0,0,3,0,'2023-03-06 07:46:24',758,NULL,NULL,0),
	(1218,NULL,'-1048.1283','312.8678','67.0052','16.7036','-1061.9976','302.6625','65.9473','351.6316',10,*********,9,0,0,11,0,'2023-03-03 22:12:58',583,NULL,NULL,0),
	(1219,NULL,'-374.2407','6190.5391','31.7295','257.4325','-377.5029','6183.2866','31.4906','220.5182',3,24000000,3,0,0,3,0,'2023-03-14 03:40:58',1688,NULL,NULL,0),
	(1220,NULL,'-356.4911','6207.251','31.8422','221.6134','-364.9036','6196.52','31.4911','223.5472',3,24000000,3,0,0,3,0,'2023-03-20 23:15:35',2027,NULL,NULL,0),
	(1221,NULL,'-1116.9288','304.1063','66.5213','349.6218','-1104.155','286.1755','64.1478','194.5689',10,*********,9,0,0,11,0,'2023-03-05 18:01:04',706,NULL,NULL,0),
	(1222,NULL,'-347.2663','6224.6714','31.6897','159.5861','-354.9248','6222.4814','31.489','218.2236',3,24000000,3,0,0,3,0,'2023-03-23 12:01:00',2135,NULL,NULL,0),
	(1223,NULL,'64.6136','-254.536','48.1882','62.7562','65.9495','-278.3778','47.2247','160.0947',2,12000000,2,0,0,2,0,'2023-03-23 16:54:51',2150,NULL,NULL,0),
	(1224,NULL,'-1629.6913','36.7324','62.9361','334.2402','-1614.5339','18.7576','62.1779','332.6539',10,*********,9,0,0,11,0,'2023-03-12 00:51:48',1350,NULL,NULL,0),
	(1225,NULL,'-1570.6821','22.5551','59.5539','353.9431','-1554.8358','22.1783','58.5948','349.1482',10,*********,9,0,0,11,0,'2023-03-02 11:04:05',504,NULL,NULL,0),
	(1226,NULL,'-1515.4299','24.0913','56.8207','352.2455','-1505.4889','29.3077','55.8867','20.126',10,*********,9,0,0,11,0,'2023-03-22 18:31:08',2101,NULL,NULL,0),
	(1227,NULL,'-1467.4169','35.1248','54.5448','350.8539','-1491.1897','20.7067','54.7196','347.308',10,*********,9,0,0,11,0,'2023-03-26 18:30:38',2454,NULL,NULL,0),
	(1228,NULL,'-150.203','6422.5796','31.9159','135.5785','-179.0169','6439.4219','31.5125','43.3339',2,12000000,2,0,0,2,0,'2023-03-27 19:01:01',2606,NULL,NULL,0),
	(1229,NULL,'-150.1788','6416.5415','31.9159','28.8089','-176.5319','6441.5317','31.5123','43.6527',2,12000000,2,0,0,2,0,'2023-03-10 17:05:46',1199,NULL,NULL,0),
	(1230,NULL,'-157.3266','6409.4307','31.9159','46.2985','-173.911','6443.7441','31.5127','44.9098',2,12000000,2,0,0,2,0,'2023-03-25 22:50:54',2352,NULL,NULL,0),
	(1231,NULL,'5.8768','-9.2165','70.1161','331.6771','10.8361','-3.6401','70.1818','339.5509',2,12000000,2,0,0,2,0,'2023-03-10 02:04:06',1146,NULL,NULL,0),
	(1232,NULL,'-1464.8757','-34.0925','55.0505','310.8304','-1462.7554','-26.7204','54.6468','231.7169',10,400000000,9,0,0,11,0,'2023-03-10 18:53:28',1200,NULL,NULL,0),
	(1233,NULL,'17.7608','-13.5212','70.1161','341.1551','17.5411','-6.2048','70.1398','337.42',2,12000000,2,0,0,2,0,'2023-03-10 23:57:52',1245,NULL,NULL,0),
	(1234,NULL,'-160.1821','6432.1484','31.9159','126.1144','-182.2839','6437.6968','31.5147','41.6053',2,12000000,2,0,0,2,0,'2023-03-08 22:56:36',1021,NULL,NULL,0),
	(1235,NULL,'-167.2446','6439.5801','31.9159','155.4715','-184.6599','6436.1084','31.5189','26.407',2,12000000,2,0,0,2,0,'2023-03-21 13:01:00',2047,NULL,NULL,0),
	(1236,NULL,'-1549.2555','-90.6271','54.9291','356.7656','-1573.6158','-85.0085','54.1345','269.1862',10,400000000,9,0,0,11,0,'2023-03-09 18:52:49',1095,NULL,NULL,0),
	(1237,NULL,'-14.0229','-11.5922','71.1452','166.3592','-0.4557','2.3876','70.997','343.4883',2,12000000,2,0,0,2,0,'2023-03-13 23:40:02',1679,NULL,NULL,0),
	(1238,NULL,'-9.9547','-14.5554','71.1452','72.2364','-4.3915','3.5684','71.0815','337.6964',2,12000000,2,0,0,2,0,'2023-02-28 00:01:00',474,NULL,NULL,0),
	(1239,NULL,'-9.9205','-14.5483','75.3448','74.0817','-7.7944','5.6362','71.1458','343.5023',2,12000000,2,0,0,2,0,'2023-03-09 09:12:23',1048,NULL,NULL,0),
	(1240,NULL,'-14.1937','-11.5496','75.3448','159.6563','-11.9142','6.6947','71.2962','337.3296',2,12000000,2,0,0,2,0,'2023-03-10 15:00:01',1183,NULL,NULL,0),
	(1241,NULL,'-1579.8997','-34.1517','57.5651','85.9548','-1573.993','-59.643','56.4917','267.9881',10,*********,9,0,0,11,0,'2023-03-23 18:27:04',2165,NULL,NULL,0),
	(1242,NULL,'-9.9641','-14.6486','79.3456','74.1509','0.5066','6.6191','70.8425','342.429',2,12000000,2,0,0,2,0,'2023-03-26 09:01:00',2397,NULL,NULL,0),
	(1243,NULL,'-13.9879','-11.6145','79.3459','166.5445','-2.7495','8.1252','70.938','339.53',2,12000000,2,0,0,2,0,'2023-03-06 20:11:00',816,NULL,NULL,0),
	(1244,NULL,'-111.3813','6322.7427','31.5761','130.4192','-76.6187','6346.3599','31.4904','40.3617',2,12000000,2,0,0,2,0,'2023-03-25 23:01:43',2360,NULL,NULL,0),
	(1245,NULL,'-114.6459','6326.0278','31.5761','129.7811','-79.639','6343.8818','31.4904','38.8564',2,12000000,2,0,0,2,0,'2023-03-27 19:38:20',2607,NULL,NULL,0),
	(1246,NULL,'-120.0819','6326.9414','31.5759','222.5196','-82.351','6341.2627','31.4904','44.4039',2,12000000,2,0,0,2,0,'2023-02-27 22:11:01',441,NULL,NULL,0),
	(1247,NULL,'-120.0711','6326.9224','35.501','46.1671','-85.3145','6338.5225','31.4904','38.702',2,12000000,2,0,0,2,0,'2023-03-26 19:40:24',2467,NULL,NULL,0),
	(1248,NULL,'-114.5378','6325.9126','35.501','323.7438','-80.4333','6333.9058','31.4904','225.1134',2,12000000,2,0,0,2,0,'2023-03-08 15:01:00',978,NULL,NULL,0),
	(1249,NULL,'-111.4239','6322.625','35.501','313.6762','-77.6799','6336.6172','31.4904','222.7332',2,12000000,2,0,0,2,0,'2023-03-12 11:57:47',1397,NULL,NULL,0),
	(1250,NULL,'-103.375','6330.8701','31.5762','307.3661','-74.8003','6339.3452','31.4904','220.6783',2,12000000,2,0,0,2,0,'2023-03-26 12:01:01',2409,NULL,NULL,0),
	(1251,NULL,'-105.9975','6333.9639','31.5762','42.4222','-72.374','6342.0625','31.4904','223.4426',2,12000000,2,0,0,2,0,'2023-03-05 02:10:02',664,NULL,NULL,0),
	(1252,NULL,'-107.41','6339.5649','31.5758','17.9095','-58.5658','6344.792','31.4904','129.1513',2,12000000,2,0,0,2,0,'2023-03-06 16:01:00',784,NULL,NULL,0),
	(1253,NULL,'-102.2325','6344.9648','31.5759','206.8242','-61.0044','6347.4575','31.4904','129.7894',2,12000000,2,0,0,2,0,'2023-03-06 22:04:00',826,NULL,NULL,0),
	(1254,NULL,'-98.5814','6348.2617','31.5759','314.8152','-63.776','6350.5093','31.4904','129.621',2,12000000,2,0,0,2,0,'2023-03-16 11:01:00',1779,NULL,NULL,0),
	(1255,NULL,'-93.2651','6353.4058','31.5759','315.0385','-66.6455','6352.894','31.4904','130.6881',2,12000000,2,0,0,2,0,'2023-03-13 10:14:55',1548,NULL,NULL,0),
	(1256,NULL,'-90.0588','6356.5176','31.5759','312.1344','-69.8172','6355.5542','31.4904','132.4916',2,12000000,2,0,0,2,0,'2023-03-26 20:47:28',2478,NULL,NULL,0),
	(1257,NULL,'-84.4363','6361.9707','31.5759','313.1142','-72.4967','6358.2554','31.4904','130.5359',2,12000000,2,0,0,2,0,'2023-03-10 08:01:00',1159,NULL,NULL,0),
	(1258,NULL,'-84.4037','6362.4404','35.5007','132.4734','-82.506','6358.6997','31.4904','222.5607',2,12000000,2,0,0,2,0,'2023-03-24 12:01:00',2212,NULL,NULL,0),
	(1259,NULL,'-89.8874','6357.0142','35.5008','131.8628','-85.35','6355.6719','31.4904','219.9023',2,12000000,2,0,0,2,0,'2023-03-14 20:40:48',1717,NULL,NULL,0),
	(1260,NULL,'-93.3749','6353.6069','35.5008','131.7562','-87.9123','6352.9194','31.4904','221.5836',2,12000000,2,0,0,2,0,'2023-02-28 17:42:37',475,NULL,NULL,0),
	(1261,NULL,'-98.438','6348.4668','35.5008','131.61','-90.2824','6349.873','31.4904','222.8674',2,12000000,2,0,0,2,0,'2023-03-13 14:21:00',1576,NULL,NULL,0),
	(1262,NULL,'-102.0982','6344.7363','35.5008','131.7356','-93.1582','6347.4775','31.4904','223.8407',2,12000000,2,0,0,2,0,'2023-03-04 18:01:02',631,NULL,NULL,0),
	(1263,NULL,'-106.9699','6339.8203','35.5008','143.7729','-95.7424','6344.9653','31.4904','221.3358',2,12000000,2,0,0,2,0,'2023-03-22 15:01:00',2092,NULL,NULL,0),
	(1264,NULL,'-106.4295','6334.3765','35.5008','222.1811','-98.4652','6342.2695','31.4903','221.4738',2,12000000,2,0,0,2,0,'2023-03-13 10:18:13',1549,NULL,NULL,0),
	(1265,NULL,'-103.2932','6331.333','35.5008','219.2196','-101.1761','6339.27','31.4903','217.3613',2,12000000,2,0,0,2,0,'2023-03-10 15:01:01',1184,NULL,NULL,0),
	(1266,NULL,'283.9858','31.0848','88.6064','162.8991','289.0803','21.0367','84.0178','254.5408',2,12000000,2,0,0,2,0,'2023-02-28 19:16:24',476,NULL,NULL,0),
	(1267,NULL,'280.177','32.4328','88.6065','166.5958','282.661','22.8968','84.0879','256.3813',2,12000000,2,0,0,2,0,'2023-03-17 19:16:23',1844,NULL,NULL,0),
	(1268,NULL,'-784.5378','459.4044','100.3887','212.1969','-767.9518','468.1138','100.193','220.7415',10,190000000,9,0,0,11,0,'2023-03-09 02:43:10',1039,NULL,NULL,0),
	(1269,NULL,'-762.3425','431.4338','100.197','17.1308','-757.6121','440.2279','99.5746','24.1226',10,190000000,9,0,0,11,0,'2023-03-06 20:52:10',815,NULL,NULL,0),
	(1270,NULL,'-606.1867','672.8726','151.6033','171.9035','-614.7795','677.8716','149.7889','344.4008',10,190000000,9,0,0,11,0,'2023-03-10 21:27:09',1226,NULL,NULL,0),
	(1271,NULL,'-615.3899','398.6178','101.6267','3.5018','-628.6036','401.1028','101.1519','8.3524',8,120000000,8,0,0,8,0,'2023-03-04 00:08:16',591,NULL,NULL,0),
	(1272,NULL,'-595.7758','393.8508','101.8825','6.9909','-604.5828','399.7122','101.5317','10.7573',10,190000000,9,0,0,11,0,'2023-03-13 21:33:46',1651,NULL,NULL,0),
	(1273,NULL,'-561.2505','402.9799','101.8052','355.9471','-577.3595','403.3368','100.6627','27.4789',10,190000000,9,0,0,11,0,'2023-03-05 21:51:34',723,NULL,NULL,0),
	(1274,NULL,'-451.2953','395.4768','104.7741','91.8684','-455.5469','388.6426','104.132','0.5168',10,228000000,9,0,0,11,0,'2023-03-08 01:57:24',922,NULL,NULL,0),
	(1275,NULL,'-520.2995','594.1897','120.8366','279.8192','-511.9806','577.4787','120.4303','284.5052',10,190000000,9,0,0,11,0,'2023-03-12 22:45:18',1493,NULL,NULL,0),
	(1276,NULL,'-522.6998','628.403','137.9738','286.8549','-525.1332','645.8777','138.1496','321.608',10,190000000,9,0,0,11,0,'2023-03-05 21:30:47',724,NULL,NULL,0),
	(1277,NULL,'-445.9045','686.1106','153.1167','116.9284','-465.2367','674.8038','148.1496','163.7178',10,190000000,9,0,0,11,0,'2023-03-10 20:30:37',1220,NULL,NULL,0),
	(1278,NULL,'-1970.2428','246.1826','87.8121','291.3885','-1975.7981','260.235','87.2193','286.1584',6,120000000,7,0,0,6,0,'2023-03-12 19:14:53',1460,NULL,NULL,0),
	(1279,NULL,'-1364.2168','569.9929','134.9728','251.1968','-1357.8176','578.9533','131.4482','255.2341',6,*********,7,0,0,6,0,'2023-03-06 21:12:23',820,NULL,NULL,0),
	(1280,NULL,'-1346.4213','560.775','130.5316','50.2676','-1359.5302','552.6583','129.9014','39.824',10,190000000,9,0,0,11,0,'2023-03-08 21:13:43',1014,NULL,NULL,0),
	(1281,NULL,'-349.1395','514.7145','120.6471','317.6275','-360.8094','514.3142','119.6157','132.6497',10,190000000,9,0,0,11,0,'2023-03-27 20:55:18',2621,NULL,NULL,0),
	(1282,NULL,'-554.5039','541.1632','110.7071','171.3288','-542.5255','544.9501','110.5262','207.9477',8,120000000,8,0,0,8,0,'2023-03-12 11:06:50',1392,NULL,NULL,0),
	(1285,NULL,'1889.3942','3927.8403','33.1978','284.4956','1891.2753','3941.2156','32.8132','262.2326',2,10000000,1,0,0,2,0,'2023-03-10 21:29:06',1229,NULL,NULL,0),
	(1287,NULL,'1879.788','3920.4402','33.1919','102.6614','1870.7207','3914.9575','32.9324','11.502',2,10000000,1,0,0,2,0,'2023-03-18 13:49:04',1881,NULL,NULL,0),
	(1288,NULL,'1894.2708','3895.5745','33.1732','200.102','1886.6071','3884.521','32.9694','110.8943',2,10000000,1,0,0,2,0,'2023-03-26 02:01:00',2377,NULL,NULL,0),
	(1289,NULL,'1895.4988','3873.3975','32.5014','206.1067','1888.9302','3868.0828','32.5843','117.4969',2,10000000,1,0,0,2,0,'2023-03-20 06:01:49',1983,NULL,NULL,0),
	(1290,NULL,'1902.7087','3866.0254','33.0682','204.8123','1907.5624','3859.2471','32.3387','114.3312',2,10000000,1,0,0,2,0,'2023-03-06 21:20:43',822,NULL,NULL,0),
	(1292,NULL,'1435.7498','3657.3425','34.3429','290.4832','1446.017','3662.105','34.145','233.0513',2,12000000,2,0,0,2,0,'2023-03-20 21:06:52',2024,NULL,NULL,0),
	(1293,NULL,'1433.8958','3628.1626','35.7461','195.8017','1439.2899','3612.8477','34.8571','96.1432',2,10000000,1,0,0,2,0,'2023-03-19 16:01:01',1943,NULL,NULL,0),
	(1294,NULL,'1430.6259','3671.9607','34.8307','16.3828','1419.8107','3686.9363','33.7779','287.4329',2,10000000,1,0,0,2,0,'2023-03-11 20:01:00',1329,NULL,NULL,0),
	(1295,NULL,'1500.6753','3694.7063','35.2077','133.3218','1490.058','3706.217','34.3983','28.2522',2,10000000,1,0,0,2,0,'2023-03-04 02:58:22',603,NULL,NULL,0),
	(1296,NULL,'1406.707','3655.8147','34.2115','112.295','1398.2853','3669.1448','33.8924','18.5312',2,10000000,1,0,0,2,0,'2023-03-11 18:01:00',1317,NULL,NULL,0),
	(1297,NULL,'1389.9519','3661.9316','34.9249','19.8204','1381.9537','3673.4512','33.5506','107.8273',2,10000000,1,0,0,2,0,'2023-03-09 17:40:44',1087,NULL,NULL,0),
	(1298,NULL,'1532.1804','3723.4106','34.6937','20.9863','1543.3641','3726.4226','34.6686','275.9389',2,10000000,1,0,0,2,0,'2023-03-20 19:30:27',2014,NULL,NULL,0),
	(1299,NULL,'1643.0477','3727.6987','35.0671','305.382','1643.7897','3720.9451','34.3239','305.7739',2,10000000,1,0,0,2,0,'2023-03-11 12:01:01',1273,NULL,NULL,0),
	(1300,NULL,'1662.5942','3820.6453','35.4698','303.3907','1662.2006','3812.811','34.8473','303.4168',2,10000000,1,0,0,2,0,'2023-03-14 16:01:00',1707,NULL,NULL,0),
	(1301,NULL,'1639.6082','3731.6326','35.0671','308.0019','1631.2797','3734.9158','34.5189','304.2414',2,10000000,1,0,0,2,0,'2023-03-26 12:01:00',2410,NULL,NULL,0),
	(1302,NULL,'1690.5538','3867.2698','34.9063','41.7098','1691.8635','3872.7029','34.8175','25.1847',2,10000000,1,0,0,2,0,'2023-03-19 15:01:00',1942,NULL,NULL,0),
	(1303,NULL,'1737.3645','3899.0488','35.559','21.0479','1732.752','3906.8008','34.8479','310.728',2,10000000,1,0,0,2,0,'2023-03-10 16:15:25',1188,NULL,NULL,0),
	(1304,NULL,'1748.3251','3783.249','34.8349','124.1053','1755.6466','3770.4421','33.8318','117.9196',2,10000000,1,0,0,2,0,'2023-03-25 00:01:00',2271,NULL,NULL,0),
	(1305,NULL,'1781.1888','3912.0198','34.9096','22.0059','1775.2614','3928.2861','34.4037','303.3514',2,10000000,1,0,0,2,0,'2023-03-08 19:49:36',1006,NULL,NULL,0),
	(1306,NULL,'1745.6868','3788.0034','34.8349','118.4848','1733.3918','3784.0012','34.383','212.5517',2,10000000,1,0,0,2,0,'2023-03-14 10:07:51',1698,NULL,NULL,0),
	(1307,NULL,'1802.781','3914.4304','37.058','27.1588','1803.2704','3933.4229','33.7599','311.2685',2,10000000,1,0,0,2,0,'2023-03-10 14:18:45',1176,NULL,NULL,0),
	(1308,NULL,'1925.2534','3824.1589','32.44','205.0325','1911.8422','3806.8374','32.3192','27.4944',2,10000000,1,0,0,2,0,'2023-03-24 06:36:39',2202,NULL,NULL,0),
	(1309,NULL,'1842.23','3927.3984','33.0871','184.3414','1847.8024','3931.2019','32.9397','196.9895',2,10000000,1,0,0,2,0,'2023-03-12 18:01:00',1459,NULL,NULL,0),
	(1310,NULL,'1846.374','3914.5989','33.4611','276.1928','1840.0083','3920.1462','33.1733','278.1877',2,10000000,1,0,0,2,0,'2023-03-12 15:55:43',1416,NULL,NULL,0),
	(1311,NULL,'3311.49','5176.1333','19.6146','233.9095','3327.4966','5152.9966','18.2979','123.2255',8,120000000,8,0,0,8,0,'2023-03-10 23:58:31',1246,NULL,NULL,0),
	(1312,NULL,'1831.865','3868.3145','34.2974','109.6365','1823.9302','3873.7839','33.7351','321.7188',2,10000000,1,0,0,2,0,'2023-03-17 22:21:00',1860,NULL,NULL,0),
	(1313,NULL,'-355.9933','94.7439','66.2417','265.8657','-362.9342','114.167','66.1477','5.3531',2,12000000,2,0,0,2,0,'2023-03-27 03:01:01',2522,NULL,NULL,0),
	(1314,NULL,'-357.7814','91.123','70.5203','270.9654','-366.9152','114.2277','66.0376','0.256',2,12000000,2,0,0,2,0,'2023-03-11 19:02:35',1323,NULL,NULL,0),
	(1315,NULL,'-358.0302','95.3438','70.5203','283.3808','-371.1275','114.0775','65.9267','2.6828',2,12000000,2,0,0,2,0,'2023-03-22 00:49:00',2072,NULL,NULL,0),
	(1316,NULL,'1932.6464','3804.8005','32.9099','114.7939','1955.2455','3791.5325','32.2925','117.1864',2,10000000,1,0,0,2,0,'2023-03-19 02:46:08',1925,NULL,NULL,0),
	(1317,NULL,'1919.2914','3913.2007','33.4416','229.6852','1931.1038','3925.4719','32.3529','269.7653',2,10000000,1,0,0,2,0,'2023-03-21 15:01:00',2052,NULL,NULL,0),
	(1318,NULL,'1936.7546','3891.3782','32.7418','209.2455','1946.3158','3881.3931','32.2469','117.2303',2,10000000,1,0,0,2,0,'2023-03-06 19:01:45',804,NULL,NULL,0),
	(1319,NULL,'-431.8118','84.4687','68.5114','357.8744','-431.5733','107.4522','64.6261','359.6096',2,12000000,2,0,0,2,0,'2023-03-21 11:11:00',2038,NULL,NULL,0),
	(1320,NULL,'-431.6883','84.1419','72.5154','357.9869','-428.114','107.4193','64.6681','4.3245',2,12000000,2,0,0,2,0,'2024-04-08 14:10:06',2736,NULL,NULL,0),
	(1321,NULL,'-333.0305','85.8095','71.218','268.499','-348.314','113.6759','66.56','0.3645',2,12000000,2,0,0,2,0,'2023-03-12 22:00:01',1491,NULL,NULL,0),
	(1322,NULL,'-333.0209','88.3354','71.218','270.6118','-344.6167','113.4123','66.6676','358.5056',2,12000000,2,0,0,2,0,'2023-03-26 21:11:00',2494,NULL,NULL,0),
	(1323,NULL,'-333.1979','98.7654','71.2181','281.2564','-348.3645','113.3464','66.5672','4.4599',2,12000000,2,0,0,2,0,'2023-03-20 20:21:00',2017,NULL,NULL,0),
	(1324,NULL,'-333.2113','101.2766','71.2181','266.293','-344.614','113.4327','66.6673','358.6194',2,12000000,2,0,0,2,0,'2023-03-22 03:06:10',2074,NULL,NULL,0),
	(1325,NULL,'-333.0763','103.0106','67.6173','267.355','-348.5482','113.1815','66.5663','2.3596',2,12000000,2,0,0,2,0,'2023-03-25 00:01:01',2272,NULL,NULL,0),
	(1326,NULL,'-333.0811','85.3358','67.6188','270.9964','-344.4918','113.5859','66.6668','354.1031',2,12000000,2,0,0,2,0,'2023-03-09 14:01:19',1065,NULL,NULL,0),
	(1327,NULL,'-313.6118','84.0261','67.6166','175.4551','-322.08','113.7661','67.2656','1.1695',2,12000000,2,0,0,2,0,'2023-03-27 20:54:08',2622,NULL,NULL,0),
	(1328,NULL,'1899.6211','3781.7295','32.8769','291.7705','1905.0994','3784.1296','32.8107','231.957',2,10000000,2,0,0,2,0,'2023-03-23 23:01:01',2192,NULL,NULL,0),
	(1329,NULL,'-321.6621','84.1233','71.663','175.7154','-318.8367','113.6135','67.3594','356.9845',2,12000000,2,0,0,2,0,'2023-03-21 01:00:00',2030,NULL,NULL,0),
	(1330,NULL,'15.6367','3688.2239','39.5776','200.2191','21.4259','3685.7207','39.7182','283.3421',2,10000000,1,0,0,2,0,'2023-03-20 02:01:00',1977,NULL,NULL,0),
	(1331,NULL,'-313.6252','84.1417','71.6629','186.7765','-315.1716','113.4782','67.4673','0.2164',2,12000000,2,0,0,2,0,'2023-03-11 22:29:39',1339,NULL,NULL,0),
	(1332,NULL,'-321.7752','84.1197','75.7913','178.036','-311.7186','113.4452','67.5646','1.558',2,12000000,2,0,0,2,0,'2023-03-13 10:01:00',1550,NULL,NULL,0),
	(1333,NULL,'8.557','3686.0063','39.7277','197.668','9.4397','3681.8408','39.6653','285.4758',2,10000000,1,0,0,2,0,'2023-03-13 19:15:08',1623,NULL,NULL,0),
	(1334,NULL,'-313.7143','84.0447','75.6531','180.1292','-311.7186','113.4452','67.5646','1.558',2,12000000,2,0,0,2,0,'2023-03-18 08:50:13',1875,NULL,NULL,0),
	(1335,NULL,'-302.9635','85.3489','68.5681','85.9193','-303.7646','113.1356','67.7957','1.9042',2,12000000,2,0,0,2,0,'2023-03-16 22:25:31',1809,NULL,NULL,0),
	(1336,NULL,'-304.5983','80.5709','68.6172','358.0293','-300.4986','112.8693','67.8931','358.9088',2,12000000,2,0,0,2,0,'2023-03-13 21:01:00',1655,NULL,NULL,0),
	(1337,NULL,'-304.5064','80.2051','72.6721','0.7359','-297.4618','113.0026','67.9768','2.8429',2,12000000,2,0,0,2,0,'2023-03-03 01:05:48',533,NULL,NULL,0),
	(1338,NULL,'-302.908','85.0404','72.6622','82.3058','-293.9529','112.923','68.0787','10.1651',2,12000000,2,0,0,2,0,'2023-03-27 06:01:00',2531,NULL,NULL,0),
	(1339,NULL,'-304.6969','80.356','76.6621','2.1044','-290.6712','112.8733','68.1736','11.6918',2,12000000,2,0,0,2,0,'2023-03-21 14:28:26',2051,NULL,NULL,0),
	(1340,NULL,'-302.886','85.2384','76.6621','87.1554','-287.3698','112.6548','68.266','2.8301',2,12000000,2,0,0,2,0,'2023-03-11 12:01:00',1274,NULL,NULL,0),
	(1341,NULL,'26.1078','3739.8271','40.2263','134.7887','28.6661','3722.0964','39.668','140.5997',2,10000000,1,0,0,2,0,'2023-03-03 21:01:00',578,NULL,NULL,0),
	(1342,NULL,'-263.8978','98.8692','69.2867','274.7811','-279.9911','113.1985','68.4578','358.9424',2,12000000,2,0,0,2,0,'2023-03-12 02:50:20',1370,NULL,NULL,0),
	(1343,NULL,'-263.9875','92.0662','69.2756','272.3702','-276.656','112.9067','68.5601','6.2852',2,12000000,2,0,0,2,0,'2023-03-14 12:01:00',1703,NULL,NULL,0),
	(1344,NULL,'-263.92','91.962','73.5633','267.3854','-273.2408','113.1446','68.6509','356.7692',2,12000000,2,0,0,2,0,'2023-03-07 00:01:00',838,NULL,NULL,0),
	(1345,NULL,'-263.9969','98.9814','73.5727','264.9308','-269.886','112.8332','68.7542','357.2075',2,12000000,2,0,0,2,0,'2023-03-10 21:14:40',1230,NULL,NULL,0),
	(1346,NULL,'30.5271','3735.3386','40.6282','141.4816','35.8283','3728.1589','39.6857','291.1333',2,10000000,1,0,0,2,0,'2023-03-25 17:58:40',2314,NULL,NULL,0),
	(1347,NULL,'-263.8501','92.1385','77.5631','268.0675','-266.0235','112.4109','68.8748','1.1017',2,12000000,2,0,0,2,0,'2023-03-07 01:13:01',841,NULL,NULL,0),
	(1348,NULL,'-263.882','98.9454','77.5632','261.7427','-262.6435','112.4726','68.9693','9.5579',2,12000000,2,0,0,2,0,'2023-03-19 02:01:00',1926,NULL,NULL,0),
	(1349,NULL,'1881.3712','3811.123','32.7788','300.3469','1893.5209','3812.7632','32.3264','235.4599',4,30000000,4,0,0,4,0,'2023-03-09 20:58:48',1108,NULL,NULL,0),
	(1350,NULL,'52.2646','3740.9678','39.6015','194.7135','48.7358','3735.394','39.6037','296.9378',2,10000000,1,0,0,2,0,'2023-03-18 17:05:13',1898,NULL,NULL,0),
	(1351,NULL,'-175.8285','-9.6089','58.2147','86.726','-137.9585','-30.2363','58.0634','341.497',2,12000000,2,0,0,2,0,'2023-03-07 14:55:59',866,NULL,NULL,0),
	(1352,NULL,'1842.5898','3777.5317','33.1544','113.4989','1834.9371','3777.7073','33.4424','69.9723',2,10000000,1,0,0,2,0,'2023-03-03 13:14:39',545,NULL,NULL,0),
	(1353,NULL,'78.0067','3757.8362','39.7549','101.29','82.2573','3760.8018','39.7532','173.2362',2,10000000,1,0,0,2,0,'2023-03-16 17:11:00',1793,NULL,NULL,0),
	(1354,NULL,'41.5613','3705.9097','40.2623','336.0341','32.3043','3712.7207','39.5097','163.5071',2,10000000,1,0,0,2,0,'2023-03-25 19:01:00',2329,NULL,NULL,0),
	(1355,NULL,'95.4703','3750.1519','40.7199','252.7679','104.1417','3747.7295','39.7525','141.2072',2,10000000,1,0,0,2,0,'2023-03-25 21:01:01',2348,NULL,NULL,0),
	(1356,NULL,'48.041','3702.5137','40.2634','332.812','32.4528','3698.4775','39.4013','206.6161',2,10000000,1,0,0,2,0,'2023-03-13 21:01:00',1656,NULL,NULL,0),
	(1357,NULL,'104.3382','3727.8933','39.6696','121.2844','111.9486','3720.6523','39.6255','115.3581',2,10000000,1,0,0,2,0,'2023-03-13 11:01:39',1555,NULL,NULL,0),
	(1358,NULL,'97.7357','3682.6318','39.7311','1.8692','95.9645','3688.4377','39.5719','78.3498',2,10000000,1,0,0,2,0,'2023-03-14 02:01:00',1687,NULL,NULL,0),
	(1359,NULL,'101.5701','3652.5803','40.2038','268.0145','108.2134','3653.8062','39.736','168.1277',2,10000000,1,0,0,2,1,'2023-03-12 02:28:00',442,NULL,NULL,0),
	(1360,NULL,'1826.7791','3729.1753','33.9619','295.759','1830.8541','3723.041','33.3053','246.7294',4,30000000,4,0,0,4,0,'2023-03-06 23:43:04',832,NULL,NULL,0),
	(1361,NULL,'1831.3215','3738.0098','33.9619','344.4368','1835.9828','3745.9954','33.0974','224.682',4,30000000,4,0,0,4,0,'2023-03-13 08:13:46',1539,NULL,NULL,0),
	(1362,NULL,'67.1897','3693.783','39.941','60.2589','67.2826','3700.0266','39.755','143.6691',2,10000000,1,0,0,2,0,'2023-03-12 00:03:00',1353,NULL,NULL,0),
	(1363,NULL,'1776.8997','3738.0527','34.6554','123.261','1775.8049','3723.23','34.1668','21.851',2,10000000,1,0,0,2,0,'2023-03-20 11:01:00',1988,NULL,NULL,0),
	(1364,NULL,'84.0477','3718.5549','39.7751','55.5747','93.9117','3727.24','39.5582','232.9522',2,10000000,1,0,0,2,0,'2023-03-09 23:14:00',1128,NULL,NULL,0),
	(1365,NULL,'78.8959','3733.4773','39.6173','315.0554','80.7343','3736.5752','39.7671','230.8282',2,10000000,1,0,0,2,0,'2023-03-13 21:08:54',1657,NULL,NULL,0),
	(1366,NULL,'-23.6659','-23.5544','73.2455','334.2289','-45.3025','-24.674','67.7215','72.5126',2,12000000,2,0,0,2,0,'2023-03-07 17:01:01',879,NULL,NULL,0),
	(1367,NULL,'-21.6236','-24.379','73.2455','334.4019','-44.2914','-21.108','68.4318','71.1989',2,12000000,2,0,0,2,0,'2023-03-13 14:01:01',1578,NULL,NULL,0),
	(1368,NULL,'-22.311','-21.0301','68.9974','337.09','-45.4902','-23.6736','67.9007','76.3734',2,12000000,2,0,0,2,0,'2023-03-03 22:01:00',584,NULL,NULL,0),
	(1369,NULL,'1728.976','3850.749','34.7316','211.7942','1734.484','3839.8159','34.8361','136.7189',3,24000000,3,0,0,3,0,'2023-03-19 11:01:00',1932,NULL,NULL,0),
	(1370,NULL,'22.1502','3672.2964','39.755','62.111','22.7663','3677.873','39.7438','302.702',2,10000000,1,0,0,2,0,'2023-03-16 18:22:23',1801,NULL,NULL,0),
	(1371,NULL,'240.8718','3107.6775','42.4872','90.0344','242.7781','3130.4453','42.2063','94.9587',4,30000000,4,0,0,4,0,'2023-03-06 20:12:04',817,NULL,NULL,0),
	(1372,NULL,'192.0082','3082.0254','43.4728','273.2764','211.9282','3090.0728','42.241','181.2703',3,24000000,3,0,0,3,0,'2023-03-06 18:37:51',800,NULL,NULL,0),
	(1373,NULL,'3688.5684','4562.9565','25.1831','271.4843','3694.7219','4565.8218','25.1589','183.8495',4,30000000,4,0,0,4,0,'2023-03-12 17:11:11',1443,NULL,NULL,0),
	(1374,NULL,'1725.19','4642.30','43.87','108.1337','1725.1962','4630.9482','43.2787','117.7247',3,24000000,3,0,0,3,0,'2023-03-12 10:21:00',1389,NULL,NULL,0),
	(1375,NULL,'3725.3074','4525.0269','22.4705','176.1726','3724.2896','4518.4814','21.0362','78.0481',4,30000000,4,0,0,4,0,'2023-03-05 15:49:25',687,NULL,NULL,0),
	(1376,NULL,'195.561','3030.8669','43.8867','275.0703','193.2425','3037.5339','43.879','272.9834',3,24000000,3,0,0,3,0,'2023-03-14 09:39:01',1695,NULL,NULL,0),
	(1378,NULL,'1674.4197','4658.0918','43.3712','270.3921','1682.4946','4662.3062','43.3709','270.9404',3,24000000,3,0,0,3,0,'2023-03-06 16:19:03',785,NULL,NULL,0),
	(1379,NULL,'3808.1685','4477.8525','5.9927','207.3947','3805.7698','4464.8335','4.5743','101.3501',4,30000000,4,0,0,4,0,'2023-03-11 16:07:23',1295,NULL,NULL,0),
	(1380,NULL,'1683.709','4689.2905','43.0653','264.0132','1683.1985','4681.1147','43.0553','266.746',3,24000000,3,0,0,3,0,'2023-03-23 15:36:18',2147,NULL,NULL,0),
	(1381,NULL,'361.034','2976.5369','41.1828','118.5569','360.4009','2960.9868','40.9502','266.9531',2,12000000,2,0,0,2,0,'2023-03-03 19:04:05',571,NULL,NULL,0),
	(1382,NULL,'411.8156','2965.6414','41.8881','46.6902','401.9001','2963.8984','40.9861','119.7737',2,10000000,1,0,0,2,0,'2023-03-20 15:41:28',2001,NULL,NULL,0),
	(1383,NULL,'1718.5118','4677.2095','43.6558','83.9269','1711.0406','4668.8125','43.0779','89.3572',2,12000000,2,0,0,2,0,'2023-03-07 12:09:40',859,NULL,NULL,0),
	(1384,NULL,'1664.6077','4739.7329','41.9989','287.0609','1674.2107','4753.6597','41.9045','214.7075',2,12000000,2,0,0,2,0,'2023-03-13 11:24:19',1556,NULL,NULL,0),
	(1385,NULL,'1662.4501','4775.936','42.0076','270.7286','1666.5834','4768.9033','41.9534','281.2903',2,12000000,2,0,0,2,0,'2023-10-20 11:01:56',2705,NULL,NULL,0),
	(1386,NULL,'435.974','2997.0525','41.2832','22.6393','429.6207','3002.2456','40.2861','112.5618',2,10000000,1,0,0,2,0,'2023-03-10 04:46:00',1151,NULL,NULL,0),
	(1387,NULL,'429.071','2993.9856','40.741','21.9795','418.4922','2992.3279','40.6986','161.5425',2,10000000,1,0,0,2,0,'2023-03-27 14:01:00',2558,NULL,NULL,0),
	(1388,NULL,'1428.5934','4378.3242','44.2996','49.167','1416.6451','4374.5654','43.6759','76.4425',2,12000000,2,0,0,2,0,'2023-03-12 19:01:01',1466,NULL,NULL,0),
	(1389,NULL,'341.2211','2615.969','44.6585','28.2521','336.1321','2620.1848','44.4938','24.7983',2,12000000,2,0,0,2,0,'2023-03-05 03:05:08',668,NULL,NULL,0),
	(1390,NULL,'346.5511','2618.9666','44.6883','28.2655','342.3614','2621.5527','44.5103','33.6441',2,12000000,2,0,0,2,0,'2023-03-24 14:07:30',2221,NULL,NULL,0),
	(1391,NULL,'353.7674','2620.6348','44.6628','31.7776','349.2094','2624.2124','44.4999','30.5562',2,12000000,2,0,0,2,0,'2023-03-08 21:15:57',1016,NULL,NULL,0),
	(1392,NULL,'1381.803','4379.5869','44.0723','187.4408','1390.074','4378.8711','43.4417','267.3883',2,10000000,1,0,0,2,0,'2023-03-12 17:11:00',1445,NULL,NULL,0),
	(1393,NULL,'359.0389','2623.7961','44.6823','34.0696','354.6594','2626.7798','44.4975','29.6947',2,12000000,2,0,0,2,0,'2023-03-27 18:25:38',2592,NULL,NULL,0),
	(1394,NULL,'366.5642','2625.4204','44.663','30.3841','361.8796','2628.0962','44.4977','32.6711',2,12000000,2,0,0,2,0,'2023-02-28 17:46:00',477,NULL,NULL,0),
	(1395,NULL,'371.6034','2629.0818','44.6563','28.06','366.9149','2631.6504','44.4982','37.0599',2,12000000,2,0,0,2,0,'2023-03-23 01:54:34',2123,NULL,NULL,0),
	(1396,NULL,'379.0262','2630.334','44.6542','30.6512','374.7432','2633.0105','44.4984','30.415',2,12000000,2,0,0,2,0,'2023-03-10 14:10:53',1177,NULL,NULL,0),
	(1397,NULL,'1366.3141','4358.9224','44.5','345.0685','1367.5621','4366.2739','44.3341','296.6481',2,12000000,2,0,0,2,0,'2023-03-01 10:39:27',478,NULL,NULL,0),
	(1398,NULL,'384.3782','2633.4702','44.6733','31.4875','380.1972','2635.8882','44.4974','26.7699',2,12000000,2,0,0,2,0,'2023-03-01 15:50:41',479,NULL,NULL,0),
	(1399,NULL,'391.8993','2634.9331','44.6621','17.1158','386.7001','2638.6421','44.4965','37.9965',2,12000000,2,0,0,2,0,'2023-03-12 19:17:01',1467,NULL,NULL,0),
	(1400,NULL,'397.29','2637.9641','44.6896','29.7419','391.9941','2642.0276','44.492','29.388',2,12000000,2,0,0,2,0,'2023-03-26 17:06:00',2450,NULL,NULL,0),
	(1401,NULL,'1339.0001','4359.6064','44.3665','307.2632','1341.77','4367.3008','44.3203','290.0457',2,10000000,1,0,0,2,0,'2023-03-11 12:44:06',1275,NULL,NULL,0),
	(1402,NULL,'749.3994','4184.4775','41.0879','62.7432','758.4295','4178.207','40.7092','356.9857',3,24000000,3,0,0,3,0,'2023-03-04 06:36:41',607,NULL,NULL,0),
	(1403,NULL,'383.6045','-2006.9247','24.2459','331.225','372.0554','-1978.528','24.102','345.0047',2,12000000,2,0,0,2,0,'2023-03-11 04:01:01',1258,NULL,NULL,0),
	(1404,NULL,'347.5646','2565.3005','43.5196','119.8864','335.7999','2555.0957','44.1129','30.9982',2,10000000,1,0,0,2,0,'2023-03-03 11:01:01',542,NULL,NULL,0),
	(1405,NULL,'366.574','2571.2993','44.2128','119.4201','357.5462','2572.5322','43.5196','30.1479',2,10000000,1,0,0,2,0,'2023-03-20 00:59:27',1970,NULL,NULL,0),
	(1406,NULL,'382.2805','2576.3657','44.1725','107.2484','376.6523','2576.0918','43.5196','22.3319',2,10000000,1,0,0,2,0,'2023-03-04 18:11:00',632,NULL,NULL,0),
	(1407,NULL,'403.0505','2584.2026','43.5196','99.1248','397.0769','2583.9119','43.5196','41.7365',2,10000000,1,0,0,2,0,'2023-03-27 05:01:00',2529,NULL,NULL,0),
	(1408,NULL,'280.0771','-1994.0607','20.8038','52.4671','289.8247','-1994.4746','20.5658','233.7582',3,24000000,3,0,0,3,0,'2023-03-26 19:34:42',2468,NULL,NULL,0),
	(1409,NULL,'-481.2322','6265.4248','13.4157','161.0973','-489.4633','6263.4897','12.4825','63.596',2,12000000,2,0,0,2,0,'2023-03-25 06:46:47',2281,NULL,NULL,0),
	(1410,NULL,'471.078','2608.561','44.4773','5.5655','468.8515','2616.6189','43.2483','281.9876',4,30000000,4,0,0,4,0,'2023-03-12 09:05:57',1387,NULL,NULL,0),
	(1411,NULL,'499.0975','2606.4158','43.6996','96.3383','495.8253','2623.1863','42.7204','275.5282',3,24000000,3,0,0,3,0,'2023-03-26 14:07:42',2424,NULL,NULL,0),
	(1412,NULL,'563.8483','2598.6394','43.4567','108.7581','555.4934','2601.7603','42.8137','8.0888',2,10000000,1,0,0,2,0,'2023-03-02 19:01:00',517,NULL,NULL,0),
	(1413,NULL,'-453.907','6337.7163','12.7936','38.3069','-447.0759','6349.2622','12.6388','28.1',2,10000000,1,0,0,2,0,'2023-03-14 08:01:00',1692,NULL,NULL,0),
	(1414,NULL,'1585.988','2906.7915','57.794','133.4186','1596.8245','2904.5518','57.0199','226.8772',2,10000000,1,0,0,2,0,'2023-03-13 07:01:00',1534,NULL,NULL,0),
	(1415,NULL,'980.3909','2667.335','40.0612','356.5015','993.4389','2672.8779','40.0612','357.921',3,24000000,3,0,0,3,0,'2023-03-27 12:48:19',2544,NULL,NULL,0),
	(1416,NULL,'1106.6802','2652.8315','38.1409','267.8651','1112.3495','2657.7859','37.9951','267.4952',2,12000000,2,0,0,2,0,'2023-03-10 18:11:00',1204,NULL,NULL,0),
	(1417,NULL,'1106.714','2648.9431','38.1408','265.8438','1112.1002','2654.2449','37.9963','265.1447',2,12000000,2,0,0,2,0,'2023-03-08 00:18:55',917,NULL,NULL,0),
	(1418,NULL,'847.4246','2863.7725','58.4854','146.8824','857.3676','2849.9829','57.5731','243.3782',2,10000000,1,0,0,2,0,'2023-03-04 04:49:39',605,NULL,NULL,0),
	(1419,NULL,'1107.1747','2642.1648','38.144','357.2386','1111.7776','2647.7715','37.9961','355.5118',2,12000000,2,0,0,2,0,'2023-03-13 00:19:53',1505,NULL,NULL,0),
	(1420,NULL,'1114.8098','2642.6594','38.1438','358.3687','1116.7388','2648.2839','38.0008','357.3228',2,12000000,2,0,0,2,0,'2023-03-14 23:01:00',1725,NULL,NULL,0),
	(1421,NULL,'866.8182','2876.0615','56.9323','226.7866','870.7205','2870.7493','56.9068','197.6422',2,10000000,1,0,0,2,0,'2023-03-09 04:41:00',1043,NULL,NULL,0),
	(1422,NULL,'1121.506','2642.541','38.1438','356.5789','1116.8561','2647.041','37.9963','359.1334',2,12000000,2,0,0,2,0,'2023-03-26 23:09:16',2505,NULL,NULL,0),
	(1423,NULL,'2167.8318','3331.4219','46.4751','25.4685','2179.0632','3339.0647','45.7253','29.6048',2,10000000,1,0,0,2,0,'2023-03-01 14:17:14',480,NULL,NULL,0),
	(1424,NULL,'1125.3379','2642.2197','38.144','359.2425','1120.5925','2647.2534','37.9965','358.9847',2,12000000,2,0,0,2,0,'2023-03-24 15:32:00',2222,NULL,NULL,0),
	(1425,NULL,'890.0007','2854.9573','57.0004','113.4833','883.7421','2845.6812','56.6665','155.975',2,10000000,1,0,0,2,0,'2023-03-20 08:45:52',1986,NULL,NULL,0),
	(1426,NULL,'1132.8416','2642.4326','38.1439','354.9593','1124.0502','2647.3457','37.9965','356.5146',2,12000000,2,0,0,2,0,'2023-03-20 01:52:12',1973,NULL,NULL,0),
	(1427,NULL,'2175.1702','3322.187','46.467','209.9897','2184.3074','3327.4248','45.8786','204.5728',2,10000000,1,0,0,2,0,'2023-02-28 22:43:51',481,NULL,NULL,0),
	(1428,NULL,'1136.2831','2642.3093','38.1439','2.0269','1127.801','2646.8145','37.9965','359.184',2,12000000,2,0,0,2,0,'2023-03-14 23:22:33',1726,NULL,NULL,0),
	(1429,NULL,'1141.1813','2642.1504','38.144','356.5539','1131.3914','2646.7273','37.9965','358.3255',2,12000000,2,0,0,2,0,'2023-03-24 12:51:05',2213,NULL,NULL,0),
	(1430,NULL,'2201.0154','3318.1313','46.8062','301.2216','2210.5537','3308.6138','46.2435','290.3922',2,10000000,1,0,0,2,0,'2023-03-13 14:25:52',1579,NULL,NULL,0),
	(1431,NULL,'1141.6362','2643.6106','38.1432','87.5991','1135.161','2646.5935','37.9965','358.3645',2,12000000,2,0,0,2,0,'2023-03-09 04:01:00',1044,NULL,NULL,0),
	(1432,NULL,'1141.443','2651.1648','38.1409','87.3588','1138.3641','2647.0818','37.9965','359.5716',2,12000000,2,0,0,2,0,'2023-03-19 21:56:51',1963,NULL,NULL,0),
	(1433,NULL,'1141.7499','2654.5217','38.15','89.4048','1137.7997','2654.9507','38.0028','356.8237',2,12000000,2,0,0,2,0,'2023-03-26 02:01:01',2378,NULL,NULL,0),
	(1434,NULL,'2166.6011','3379.8755','46.4345','239.561','2181.9409','3360.8567','45.3789','300.7558',2,10000000,1,0,0,2,0,'2023-03-27 23:11:47',2662,NULL,NULL,0),
	(1435,NULL,'2178.488','3497.1182','45.43','35.9803','2170.8994','3504.0212','45.5127','51.5555',2,10000000,1,0,0,2,0,'2023-03-25 23:24:50',2362,NULL,NULL,0),
	(1436,NULL,'1257.8262','2739.5','38.7188','148.8627','1259.9487','2728.9441','38.4851','196.2807',2,10000000,1,0,0,2,0,'2023-03-20 13:01:00',1994,NULL,NULL,0),
	(1437,NULL,'2156.6155','3385.7751','45.4867','59.0744','2169.4651','3362.4172','45.4771','301.0451',2,10000000,1,0,0,2,0,'2023-03-09 23:01:00',1129,NULL,NULL,0),
	(1438,NULL,'2419.4934','4020.1904','36.8359','243.8301','2422.2856','4012.5122','36.696','239.5581',4,30000000,4,0,0,4,0,'2023-03-13 23:33:11',1682,NULL,NULL,0),
	(1439,NULL,'2486.9783','3727.0815','43.9217','36.6663','2485.021','3738.1719','42.6124','81.4293',2,10000000,1,0,0,2,0,'2023-03-09 14:01:00',1066,NULL,NULL,0),
	(1440,NULL,'2163.0911','3374.8733','46.3258','239.884','2181.6201','3369.3931','45.4644','301.5387',2,10000000,1,0,0,2,0,'2023-03-27 19:01:00',2608,NULL,NULL,0),
	(1441,NULL,'2659.9932','3291.8635','55.624','56.5035','2652.0295','3289.698','55.2136','139.0976',2,10000000,1,0,0,2,0,'2023-03-16 13:39:06',1782,NULL,NULL,0),
	(1442,NULL,'2567.0771','4273.7368','41.9891','320.5953','2578.1721','4278.4155','41.8912','238.7411',3,24000000,3,0,0,3,0,'2023-03-27 01:01:00',2514,NULL,NULL,0),
	(1443,NULL,'2164.3169','3395.7146','45.4482','324.9629','2177.4568','3405.9368','45.6034','196.0692',2,10000000,1,0,0,2,0,'2023-03-25 14:01:00',2301,NULL,NULL,0),
	(1444,NULL,'2632.8701','3258.6038','55.4634','317.2036','2640.8667','3256.2422','55.2429','292.4999',2,12000000,2,0,0,2,0,'2023-03-24 13:04:01',2218,NULL,NULL,0),
	(1445,NULL,'2728.4629','4280.5562','48.9612','93.0176','2719.1621','4278.041','47.255','1.1369',2,10000000,1,0,0,2,0,'2023-03-26 14:33:26',2425,NULL,NULL,0),
	(1446,NULL,'2194.844','3306.7039','46.3336','120.2954','2189.1199','3303.8911','46.3046','25.6776',2,10000000,1,0,0,2,0,'2023-03-12 15:11:32',1417,NULL,NULL,0),
	(1447,NULL,'2728.3083','4286.9683','48.9614','96.4305','2723.9585','4292.5225','48.0202','108.7609',2,10000000,1,0,0,2,0,'2023-03-12 13:04:00',1402,NULL,NULL,0),
	(1448,NULL,'2618.8904','3275.0122','55.7381','236.4934','2613.2507','3267.7437','55.2264','256.8912',2,12000000,2,0,0,2,0,'2023-03-22 10:37:35',2080,NULL,NULL,0),
	(1449,NULL,'2633.6089','3290.804','55.7284','147.946','2627.3572','3290.031','55.328','199.9838',2,12000000,2,0,0,2,0,'2023-03-20 12:01:00',1991,NULL,NULL,0),
	(1450,NULL,'2153.04','3360.051','45.4434','193.66','2173.9934','3356.0139','45.4566','299.4597',2,10000000,1,0,0,2,0,'2023-03-23 00:46:31',2120,NULL,NULL,0),
	(1451,NULL,'2727.8762','4142.1211','44.288','77.194','2720.4258','4140.0059','43.9707','40.2869',2,12000000,2,0,0,2,0,'2023-03-05 20:11:00',714,NULL,NULL,0),
	(1452,NULL,'2638.9565','4246.8159','44.7797','35.8853','2645.9128','4253.6909','44.7812','306.4946',2,12000000,2,0,0,2,0,'2023-03-27 16:01:00',2574,NULL,NULL,0),
	(1453,NULL,'2588.5889','3168.2861','51.1673','320.6655','2597.8521','3167.2446','51.0053','286.4146',2,12000000,2,0,0,2,0,'2023-03-11 08:01:00',1267,NULL,NULL,0),
	(1454,NULL,'188.7957','11.1106','73.2244','336.9291','170.0612','20.1449','73.2244','74.377',2,12000000,2,0,0,2,0,'2023-03-13 03:58:51',1524,NULL,NULL,0),
	(1455,NULL,'2190.375','3493.0195','46.453','217.8307','2172.7239','3511.8794','45.2392','60.0173',2,10000000,1,0,0,2,0,'2023-03-20 16:53:06',2004,NULL,NULL,0),
	(1456,NULL,'188.7643','11.2392','77.41','338.9712','170.6845','23.9319','73.2244','69.1121',2,12000000,2,0,0,2,0,'2023-03-08 14:23:12',972,NULL,NULL,0),
	(1457,NULL,'188.8046','11.281','81.4111','338.5276','172.0785','27.3724','73.2244','64.5404',2,12000000,2,0,0,2,0,'2023-03-24 20:56:15',2253,NULL,NULL,0),
	(1458,NULL,'1966.5255','4634.7456','41.1412','38.0032','1954.8069','4650.4082','40.7275','246.5608',2,12000000,2,0,0,2,0,'2023-03-26 12:01:00',2411,NULL,NULL,0),
	(1459,NULL,'201.7688','2441.9209','60.4619','267.6709','210.0589','2434.2539','59.7088','326.5072',2,12000000,2,0,0,2,0,'2023-03-20 16:21:00',2005,NULL,NULL,0),
	(1460,NULL,'-46.2616','1946.417','190.3556','201.0485','-58.8083','1956.6393','190.1862','88.5811',3,24000000,3,0,0,3,0,'2023-03-24 06:22:30',2203,NULL,NULL,0),
	(1461,NULL,'710.8305','4185.5518','41.0826','87.5382','717.1163','4172.9106','40.7092','291.5398',3,24000000,3,0,0,3,0,'2023-03-15 01:11:00',1731,NULL,NULL,0),
	(1462,NULL,'208.0639','-1895.3169','24.814','316.8987','198.0096','-1899.0653','24.1716','135.571',3,24000000,3,0,0,3,0,'2023-03-24 06:46:22',2204,NULL,NULL,0),
	(1463,NULL,'192.2905','-1883.4611','25.0568','341.6277','179.5343','-1889.2997','23.879','155.519',3,24000000,3,0,0,3,0,'2023-03-25 22:13:12',2353,NULL,NULL,0),
	(1464,NULL,'722.356','2331.4565','51.7503','31.1803','697.5035','2342.0168','50.3329','124.027',3,24000000,3,0,0,3,0,'2023-03-13 07:03:16',1535,NULL,NULL,0),
	(1465,NULL,'790.2799','4183.8833','41.3448','266.5252','769.872','4189.2139','40.7092','80.1379',3,24000000,3,0,0,3,0,'2023-03-27 03:53:29',2523,NULL,NULL,0),
	(1466,NULL,'1509.9283','6326.54','24.6071','59.16','1526.4967','6343.1934','24.0709','84.8163',4,30000000,4,0,0,4,0,'2023-03-02 22:10:32',524,NULL,NULL,0),
	(1467,NULL,'1538.7366','6322.6665','24.3976','0.8873','1529.785','6332.4253','24.3215','56.9864',2,10000000,1,0,0,2,0,'2023-03-23 18:13:05',2166,NULL,NULL,0),
	(1468,NULL,'-709.7062','5768.9302','17.511','333.9833','-715.0303','5768.2847','17.4363','64.0536',2,12000000,2,0,0,2,0,'2023-03-19 01:31:00',1922,NULL,NULL,0),
	(1469,NULL,'-705.8634','5766.9185','17.511','335.0024','-713.5472','5771.6025','17.46','63.972',2,12000000,2,0,0,2,0,'2023-03-27 21:09:00',2639,NULL,NULL,0),
	(1470,NULL,'-701.9571','5765.1538','17.511','330.0553','-706.8348','5785.1055','17.331','155.8433',2,12000000,2,0,0,2,0,'2023-03-26 21:01:00',2495,NULL,NULL,0),
	(1471,NULL,'-697.9976','5763.2559','17.511','339.0613','-703.6043','5783.4688','17.3309','153.5438',2,12000000,2,0,0,2,0,'2023-03-12 13:24:00',1403,NULL,NULL,0),
	(1472,NULL,'-694.1574','5761.4268','17.511','336.3574','-700.4813','5781.7207','17.331','151.0916',2,12000000,2,0,0,2,0,'2023-03-09 01:19:57',1036,NULL,NULL,0),
	(1473,NULL,'-690.1559','5759.6816','17.511','338.3337','-697.5753','5780.23','17.3309','159.1022',2,12000000,2,0,0,2,0,'2023-03-11 23:15:00',1343,NULL,NULL,0),
	(1474,NULL,'-687.6166','5759.1113','17.511','65.8951','-675.8403','5775.8506','17.3309','67.7971',2,12000000,2,0,0,2,0,'2023-03-06 12:20:55',765,NULL,NULL,0),
	(1475,NULL,'254.296','24.7','83.9517','341.0634','239.2284','16.6186','83.9158','71.1867',2,12000000,2,0,0,2,0,'2023-03-20 09:43:48',1987,NULL,NULL,0),
	(1476,NULL,'-685.8004','5763.0371','17.511','66.0701','-673.9059','5779.1431','17.3309','63.5964',2,12000000,2,0,0,2,0,'2023-03-12 01:36:00',1365,NULL,NULL,0),
	(1477,NULL,'260.7851','22.5413','83.958','335.7883','240.2704','20.0071','83.896','72.6532',2,12000000,2,0,0,2,0,'2023-03-13 18:18:33',1614,NULL,NULL,0),
	(1478,NULL,'260.8548','22.3637','88.1273','345.4839','241.2565','23.4225','83.8804','61.9896',2,12000000,2,0,0,2,0,'2023-03-14 15:39:00',1705,NULL,NULL,0),
	(1479,NULL,'254.4342','24.8809','88.1273','338.2025','242.3133','26.8815','83.8921','71.5789',2,12000000,2,0,0,2,0,'2023-03-26 23:15:17',2506,NULL,NULL,0),
	(1480,NULL,'-684.1047','5767.0215','17.511','66.2544','-672.8009','5782.1362','17.3309','73.133',2,12000000,2,0,0,2,0,'2023-03-19 01:01:00',1923,NULL,NULL,0),
	(1481,NULL,'260.9355','22.6719','92.1272','336.1231','243.4877','30.0902','83.9674','66.7542',2,12000000,2,0,0,2,0,'2023-03-05 17:22:54',700,NULL,NULL,0),
	(1482,NULL,'254.3873','24.9687','92.1272','343.1782','239.2752','28.0915','83.6393','155.2016',2,12000000,2,0,0,2,0,'2023-03-18 16:01:00',1892,NULL,NULL,0),
	(1483,NULL,'-682.2159','5770.9346','17.511','65.5621','-671.3405','5784.9414','17.3753','59.9496',2,12000000,2,0,0,2,0,'2023-03-25 12:21:00',2295,NULL,NULL,0),
	(1484,NULL,'141.3845','-279.1047','46.3035','66.8726','149.3555','-301.7237','45.5837','158.972',2,12000000,2,0,0,2,0,'2023-03-05 12:56:38',676,NULL,NULL,0),
	(1485,NULL,'105.392','-258.6361','55.4995','347.515','93.3189','-288.4659','46.4317','159.2469',2,12000000,2,0,0,2,0,'2023-03-14 21:01:00',1721,NULL,NULL,0),
	(1486,NULL,'97.8401','-256.0013','55.4995','334.0895','96.462','-289.6291','46.3365','159.8835',2,12000000,2,0,0,2,0,'2023-03-17 21:14:06',1854,NULL,NULL,0),
	(1487,NULL,'2356.9028','2519.1699','47.5684','316.1369','2360.4236','2511.9988','46.6677','310.3564',2,10000000,1,0,0,2,0,'2023-03-16 23:32:50',1812,NULL,NULL,0),
	(1488,NULL,'2352.5994','2523.5671','47.6894','314.8992','2355.5669','2527.0605','46.5317','225.7271',2,10000000,1,0,0,2,0,'2023-03-15 14:01:01',1744,NULL,NULL,0),
	(1489,NULL,'2354.9353','2540.9092','47.6222','190.7793','2341.3049','2537.9446','46.6677','220.9012',2,10000000,1,0,0,2,0,'2023-03-10 21:15:07',1231,NULL,NULL,0),
	(1490,NULL,'2359.6323','2541.5723','47.696','186.9883','2371.7246','2539.408','46.6665','174.0652',2,10000000,1,0,0,2,0,'2023-03-06 14:01:00',772,NULL,NULL,0),
	(1491,NULL,'2333.1423','2524.2559','46.571','257.3012','2325.3054','2526.6362','46.6677','148.9505',2,10000000,1,0,0,2,0,'2023-03-11 16:01:00',1297,NULL,NULL,0),
	(1492,NULL,'2320.5259','2535.7073','47.6269','264.5013','2309.8284','2531.928','46.5661','169.5134',2,10000000,1,0,0,2,0,'2023-03-24 11:31:23',2210,NULL,NULL,0),
	(1493,NULL,'2314.1519','2549.6917','47.6059','222.2424','2305.4221','2546.9285','46.6635','208.4955',2,10000000,1,0,0,2,0,'2023-03-10 20:09:03',1221,NULL,NULL,0),
	(1494,NULL,'2318.9619','2553.4734','47.6905','217.2622','2322.2827','2549.1719','46.5176','123.1293',2,10000000,1,0,0,2,0,'2023-03-10 13:00:00',1170,NULL,NULL,0),
	(1495,NULL,'2326.9487','2573.8608','46.6677','64.7804','2332.613','2579.0273','46.6677','337.8226',2,10000000,1,0,0,2,0,'2023-03-15 05:43:39',1736,NULL,NULL,0),
	(1496,NULL,'2334.1216','2589.054','47.5818','183.1052','2320.6917','2589.3547','46.6477','357.4917',2,10000000,1,0,0,2,0,'2023-03-13 09:59:57',1545,NULL,NULL,0),
	(1497,NULL,'2336.178','2566.3374','47.6361','244.7873','2331.0662','2559.4834','46.7','55.9092',2,10000000,1,0,0,2,0,'2023-03-17 21:20:32',1855,NULL,NULL,0),
	(1498,NULL,'2338.3623','2570.5413','47.7231','246.2774','2340.5051','2580.6987','46.6059','74.2386',2,10000000,1,0,0,2,0,'2023-03-20 01:30:00',1974,NULL,NULL,0),
	(1499,NULL,'2337.585','2605.2166','47.2094','140.2662','2338.709','2598.5898','46.6462','65.5773',2,10000000,1,0,0,2,0,'2023-03-11 16:06:00',1298,NULL,NULL,0),
	(1500,NULL,'2331.7102','2610.0376','46.6666','145.9629','2325.3599','2616.6667','46.5566','338.0264',2,10000000,1,0,0,2,0,'2023-03-22 23:32:47',2116,NULL,NULL,0),
	(1501,NULL,'2357.5491','2609.4258','47.1517','150.917','2350.7148','2608.5483','46.5176','46.5147',2,10000000,1,0,0,2,0,'2023-03-25 15:01:01',2700,NULL,NULL,0),
	(1502,NULL,'2355.9751','2564.5303','47.0887','31.3938','2357.3633','2571.1484','46.6677','284.3588',2,10000000,1,0,0,2,0,'2023-03-17 15:11:00',1833,NULL,NULL,0),
	(1503,NULL,'2363.0586','2556.3755','47.1585','210.3218','2377.2979','2558.7283','46.6616','169.1404',2,10000000,1,0,0,2,0,'2023-03-06 22:01:00',828,NULL,NULL,0),
	(1504,NULL,'2356.8425','2552.5601','46.6677','213.8303','2379.6868','2549.9695','46.5547','166.9303',2,10000000,1,0,0,2,0,'2023-03-25 14:31:40',2302,NULL,NULL,0),
	(1505,NULL,'240.8204','-1687.8551','29.6993','61.4729','251.7447','-1684.0721','29.2549','232.8206',3,24000000,3,0,0,3,0,'2023-03-12 14:30:00',1405,NULL,NULL,0),
	(1506,NULL,'252.5545','-1671.198','29.6632','339.3679','253.8001','-1681.6014','29.2538','228.1999',3,24000000,3,0,0,3,0,'2023-03-08 14:23:15',973,NULL,NULL,0),
	(1507,NULL,'-1828.0453','311.7927','89.7121','281.5959','-1857.3422','324.8459','88.7025','17.5428',10,190000000,9,0,0,11,0,'2024-03-12 04:01:04',2719,NULL,NULL,0),
	(1508,NULL,'-1733.3344','379.6263','89.7252','29.9954','-1754.3469','363.7047','89.5704','113.7943',10,240000000,9,0,0,11,0,'2023-07-22 02:13:34',2696,NULL,NULL,3),
	(1509,NULL,'-1672.9535','386.1495','89.3483','349.2713','-1665.1594','388.819','89.2904','353.5006',8,120000000,8,0,0,8,0,'2023-03-19 12:36:00',1935,NULL,NULL,0),
	(1510,NULL,'-1294.283','454.7356','97.5025','9.0821','-1298.2058','456.1331','97.4313','277.2794',6,*********,7,0,0,6,0,'2023-03-01 22:54:06',487,NULL,NULL,0),
	(1511,NULL,'-907.5232','545.301','100.2049','316.3438','-910.4327','554.2827','96.221','315.7313',8,120000000,8,0,0,8,0,'2023-03-13 14:15:27',1577,NULL,NULL,0),
	(1512,NULL,'-1070.134','-1653.4098','4.4202','125.646','-1076.842','-1660.2936','4.3984','32.4052',3,24000000,3,0,0,3,0,'2023-03-09 07:51:50',1047,NULL,NULL,0),
	(1513,NULL,'-1075.8683','-1645.5126','4.5012','127.5502','-1082.4385','-1652.5928','4.3984','31.6162',3,24000000,3,0,0,3,0,'2023-03-13 09:01:00',1546,NULL,NULL,0),
	(1514,NULL,'-1098.6992','-1678.9772','4.3617','127.681','-1102.6904','-1685.0226','4.334','30.2523',3,24000000,3,0,0,3,0,'2023-03-25 06:14:47',2283,NULL,NULL,0),
	(1515,NULL,'-1083.0337','-1631.4795','4.7394','121.7822','-1096.3466','-1636.5823','4.3984','129.3479',3,24000000,3,0,0,3,0,'2023-03-18 16:49:58',1893,NULL,NULL,0),
	(1516,NULL,'-1089.0859','-1623.0448','4.7327','106.9372','-1107.8978','-1632.9938','4.616','303.6356',3,24000000,3,0,0,3,0,'2023-03-24 20:40:15',2254,NULL,NULL,0),
	(1517,NULL,'-1122.5631','-1558.2833','4.8078','213.8724','-1120.8538','-1561.6516','4.2415','297.947',3,24000000,3,0,0,3,0,'2023-03-19 00:01:00',1916,NULL,NULL,0),
	(1518,NULL,'-1155.2806','-1544.1168','4.4359','209.9919','-1147.197','-1546.1072','4.2923','35.9515',3,24000000,3,0,0,3,0,'2023-03-17 07:44:20',1819,NULL,NULL,0),
	(1519,NULL,'-1149.3793','-1522.5579','10.6281','234.8465','-1159.8335','-1514.3708','4.1607','304.0706',4,40000000,5,0,0,4,0,'2023-03-12 14:28:17',2720,NULL,NULL,0),
	(1520,NULL,'-1132.4152','-1455.9747','4.8713','305.1379','-1135.4398','-1439.6257','4.8626','302.3357',3,24000000,3,0,0,3,0,'2023-03-15 03:22:01',1733,NULL,NULL,0),
	(1521,NULL,'-1246.8749','-1358.9673','7.8204','199.8042','-1241.1138','-1353.5161','3.8691','16.5946',3,24000000,3,0,0,3,0,'2023-03-05 05:53:40',670,NULL,NULL,0),
	(1522,NULL,'-1256.8521','-1359.7671','4.0467','21.948','-1273.0585','-1363.7756','4.3024','289.9916',3,24000000,3,0,0,3,0,'2023-03-25 06:35:20',2282,NULL,NULL,0),
	(1523,NULL,'-1255.4899','-1330.8781','4.0807','287.4506','-1258.5255','-1318.8339','4.0197','285.8929',3,24000000,3,0,0,3,0,'2023-03-12 06:01:00',1381,NULL,NULL,0),
	(1524,NULL,'-1242.8778','-1301.1874','3.9092','109.2391','-1243.3584','-1309.9695','3.9293','108.3758',4,40000000,5,0,0,4,0,'2023-03-13 21:55:59',1654,NULL,NULL,0),
	(1525,NULL,'-1268.4242','-1295.6606','4.0039','291.0463','-1264.3365','-1291.6422','3.823','15.0952',3,24000000,3,0,0,3,0,'2023-03-04 13:25:20',616,NULL,NULL,0),
	(1526,NULL,'-1271.7513','-1295.3326','8.2859','198.621','-1261.7085','-1298.6119','3.7973','15.9197',3,24000000,3,0,0,3,0,'2023-03-25 09:01:00',2289,NULL,NULL,0),
	(1527,NULL,'-1270.8573','-1297.8062','8.2859','200.8049','-1258.2554','-1307.1638','3.8752','16.375',3,24000000,3,0,0,3,0,'2023-03-12 07:51:02',1384,NULL,NULL,0),
	(1528,NULL,'-1269.0388','-1302.4492','8.2859','17.7969','-1276.7892','-1282.8949','3.8444','107.3456',3,24000000,3,0,0,3,0,'2023-03-23 01:40:23',2125,NULL,NULL,0),
	(1529,NULL,'-14.9544','6557.8799','33.2404','140.636','-11.7589','6563.9966','31.9582','222.5292',3,24000000,3,0,0,3,0,'2023-03-25 16:29:15',2309,NULL,NULL,0),
	(1530,NULL,'862.0818','-509.4694','57.3289','224.9559','858.1945','-521.2081','57.2972','225.9324',4,40000000,5,0,0,4,0,'2023-03-07 19:51:28',888,NULL,NULL,0),
	(1531,NULL,'878.6122','-498.3378','58.0906','226.3968','875.1674','-506.9129','57.4914','228.0744',3,30000000,3,0,0,3,0,'2023-03-27 16:57:35',2575,NULL,NULL,0),
	(1532,NULL,'1315.7947','-1527.0962','51.8076','202.9105','1325.6808','-1540.046','51.0292','99.5663',3,24000000,3,0,0,3,0,'2023-03-14 12:33:04',1704,NULL,NULL,0),
	(1533,NULL,'2482.2864','3722.4729','43.9216','36.13','2474.4983','3733.394','42.362','24.2483',2,10000000,1,0,0,2,0,'2023-03-27 13:01:00',2550,NULL,NULL,0),
	(1534,NULL,'-263.6418','2196.3064','130.3986','238.7477','-269.9621','2194.6763','129.8326','239.2089',3,24000000,3,0,0,3,0,'2023-03-10 21:22:07',1232,NULL,NULL,0),
	(1535,NULL,'-287.5375','2535.6101','75.6903','269.856','-283.6801','2540.4829','74.5928','357.2087',3,24000000,3,0,0,3,0,'2023-03-19 21:11:00',1964,NULL,NULL,0),
	(1536,NULL,'1401.0829','2169.885','97.843','264.7625','1389.1704','2157.6792','97.4083','264.3038',3,24000000,3,0,0,3,0,'2023-03-11 07:51:16',1266,NULL,NULL,0),
	(1537,NULL,'1535.4194','2232.0686','77.699','87.1933','1519.9001','2235.4419','75.1759','178.9149',3,24000000,3,0,0,3,0,'2023-03-04 22:02:03',654,NULL,NULL,0),
	(1538,NULL,'843.0165','2112.2805','52.8196','74.1295','835.0029','2122.7769','52.2931','14.9046',2,12000000,2,0,0,2,0,'2023-03-24 17:07:30',2234,NULL,NULL,0),
	(1539,NULL,'845.706','2122.9978','52.6429','89.0816','837.7805','2129.72','52.2976','41.6699',2,12000000,2,0,0,2,0,'2023-03-22 13:07:48',2089,NULL,NULL,0),
	(1540,NULL,'845.9963','2136.5203','52.6413','83.578','841.6512','2144.8879','52.2932','75.0734',2,12000000,2,0,0,2,0,'2023-03-25 17:07:51',2315,NULL,NULL,0),
	(1541,NULL,'1740.88','4648.93','43.65','133.91','1725.106','4631.0786','43.2979','116.6552',4,30000000,4,0,0,4,0,'2023-03-07 15:30:55',869,NULL,NULL,0),
	(1542,NULL,'-1344.62','49.11','55.24','97.82','-1386.9568','29.1951','53.6043','129.0993',10,********0,8,0,0,11,0,'2023-03-12 00:49:01',1352,NULL,NULL,0),
	(1543,NULL,'-1345.26','54.26','55.24','89','-1389.1117','33.1755','53.5881','130.5316',10,********0,8,0,0,11,0,'2023-03-13 15:07:10',1588,NULL,NULL,0),
	(1544,NULL,'-1345.57','57.12','55.24','90.5','-1389.5724','38.6593','53.5978','130.3369',10,********0,8,0,0,11,0,'2023-03-08 18:27:22',996,NULL,NULL,0),
	(1545,NULL,'-1345.77','59.80','55.24','92.2','-1388.4382','45.3278','53.6208','129.1222',10,********0,8,0,0,11,0,'2023-03-23 08:42:55',2131,NULL,NULL,0),
	(1546,NULL,'-1346.35','64.59','55.24','84.64','-1391.2419','48.7692','53.6146','129.1336',10,********0,8,0,0,11,0,'2023-03-05 20:47:01',716,NULL,NULL,0),
	(1547,NULL,'-1346.69','68.12','55.24','87.89','-1391.3405','53.9165','53.5845','131.1104',10,********0,8,0,0,11,0,'2023-03-13 00:06:45',1510,NULL,NULL,0),
	(1548,NULL,'-817.3197','178.0555','72.2276','111.00','-823.9133','182.1482','71.2507','129.11',10,********0,10,0,0,15,0,'2024-04-04 10:07:35',2732,NULL,NULL,0),
	(1549,NULL,'-2587.78','1910.97','167.49','183.96','-2577.5493','1929.0432','167.4535','243.6475',10,*********,9,0,0,11,0,'2023-03-09 16:46:17',1076,NULL,NULL,0),
	(1550,NULL,'-2797.86','1431.49','100.92','142.41','-2767.4006','1438.9862','101.0022','315.9661',8,160000000,8,0,0,8,0,'2023-03-08 18:34:07',997,NULL,NULL,0),
	(1551,NULL,'31.8023','6596.0615','32.4705','54.1534','32.47','6607.79','32.45','223.01',4,24000000,3,0,0,3,0,'2023-03-13 08:35:33',1540,NULL,NULL,0),
	(1552,NULL,'8.45','539.85','176.02','-15.68','11.85','547.37','175.43','90',10,600000000,9,0,0,10,0,'2023-03-20 11:48:17',1989,NULL,NULL,0),
	(1553,NULL,'-1189.886','291.790','69.8925','-158.2562','-1198.986','268.823','68.8425','-77.9703',10,********,9,0,0,10,0,'2023-03-08 00:54:04',921,NULL,NULL,0),
	(1554,NULL,'-181.447','961.551','237.736','-55.198','-171.836','966.854','237.394','126.171',15,9********,9,0,0,15,0,'2024-04-08 14:20:33',2735,NULL,NULL,0);

/*!40000 ALTER TABLE `houses` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `item_shops` (
  `id` tinyint(3) NOT NULL AUTO_INCREMENT,
  `title` varchar(30) DEFAULT NULL,
  `type` varchar(24) DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `price` int(11) unsigned DEFAULT 5000000,
  `cash` bigint(12) unsigned DEFAULT 0,
  `bank` bigint(12) unsigned DEFAULT 0,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `pickupX` varchar(10) DEFAULT NULL,
  `pickupY` varchar(10) DEFAULT NULL,
  `pickupZ` varchar(10) DEFAULT NULL,
  `warehouse` blob DEFAULT NULL,
  `prices` blob DEFAULT NULL,
  `discounts` blob DEFAULT NULL,
  `mafiaId` tinyint(2) unsigned DEFAULT 0,
  `mafiaCash` bigint(12) unsigned DEFAULT 0,
  `defaultMafiaId` tinyint(2) unsigned DEFAULT 0,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `title` (`title`),
  KEY `accountId` (`accountId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`),
  KEY `marketplaceId` (`marketplaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Магазины 24/7';

LOCK TABLES `item_shops` WRITE;
/*!40000 ALTER TABLE `item_shops` DISABLE KEYS */;

INSERT INTO `item_shops` (`id`, `title`, `type`, `accountId`, `price`, `cash`, `bank`, `posX`, `posY`, `posZ`, `pickupX`, `pickupY`, `pickupZ`, `warehouse`, `prices`, `discounts`, `mafiaId`, `mafiaCash`, `defaultMafiaId`, `paidUntil`, `auctionId`, `marketplaceId`)
VALUES
	(1,'Магазин 24/7','24_7',NULL,*********,1341825,83054,'26.8882','-1349.6554','29.3256','25.7361','-1345.6055','27.997',NULL,NULL,NULL,16,0,15,'2023-03-23 20:51:01',2171,NULL),
	(2,'Магазин Robs Liquor','rebs',NULL,1********,********,********,'-1229.1556','-903.3273','12.1907','-1222.1929','-906.7051','10.8264',NULL,NULL,NULL,16,0,15,'2023-03-26 21:11:00',2481,NULL),
	(3,'Магазин Robs Liquor','rebs',NULL,*********,********,********,'-1489.8356','-385.5327','40.0354','-1487.7504','-378.592','38.6634',NULL,NULL,NULL,15,0,15,'2023-03-19 20:51:01',1953,NULL),
	(4,'Магазин 24/7','24_7',NULL,2********,5948520,5217350,'378.9351','321.9019','103.386','374.2085','327.7418','102.0664',NULL,NULL,NULL,16,0,15,'2023-03-24 21:01:00',2256,NULL),
	(5,'Магазин Robs Liquor','rebs',NULL,1********,212437,149948,'1142.6647','-982.6805','46.105','1135.7496','-982.9034','44.9159',NULL,NULL,NULL,16,0,15,'2023-03-23 22:01:00',2183,NULL),
	(6,'Магазин 24/7','24_7',NULL,********,1502560,1482940,'2559.6611','382.0683','108.6211','2555.4023','382.1656','107.1229',NULL,NULL,NULL,15,0,16,'2023-03-13 21:21:00',1637,NULL),
	(7,'Магазин 24/7','24_7',NULL,1********,8861294,5157951,'2681.2268','3278.9653','55.4055','2677.0898','3281.3977','53.7411',NULL,NULL,NULL,18,0,16,'2023-03-27 21:01:00',2624,NULL),
	(8,'Магазин 24/7','24_7',NULL,*********,9578155,5698636,'1968.0093','3740.6785','32.3524','1960.3589','3742.0676','30.8438',NULL,NULL,NULL,16,0,16,'2023-03-27 21:01:00',2626,NULL),
	(9,'Магазин Convenience Store','rebs',NULL,********,10028696,5524788,'1396.9596','3600.1138','35.0131','1392.1064','3604.8218','33.4809',NULL,NULL,NULL,14,0,16,'2023-03-27 21:01:00',2630,NULL),
	(10,'Магазин Convenience Store','rebs',NULL,********,6068617,4193217,'1163.8822','2702.9683','38.181','1165.1644','2709.3638','36.6577',NULL,NULL,NULL,15,0,16,'2023-03-05 21:21:00',718,NULL),
	(11,'Магазин 24/7','24_7',NULL,********,63250,89,'547.4521','2674.1047','42.1804','548.0414','2669.3279','40.6565',NULL,NULL,NULL,18,0,16,'2023-03-27 21:11:00',2632,NULL),
	(12,'Магазин LTD','24_7',NULL,********,37728478,44523713,'1700.1747','4931.7671','42.0781','1699.4768','4923.4922','40.5637',NULL,NULL,NULL,16,0,16,'2023-03-01 20:41:00',484,NULL),
	(13,'Магазин 24/7','24_7',NULL,*********,2138437,596873,'1727.6106','6412.4111','35.0006','1729.7832','6416.2344','33.5372',NULL,NULL,NULL,18,0,16,'2023-03-27 21:21:00',2635,NULL),
	(14,'Магазин 24/7','24_7',NULL,********,2207910,1193808,'-3238.9338','1002.3544','12.4976','-3244.0615','1001.4288','11.3307',NULL,NULL,NULL,16,0,16,'2023-03-24 20:41:00',2245,NULL),
	(15,'Магазин 24/7','24_7',NULL,********,2619851,1997972,'-3037.4377','587.5711','7.8125','-3041.0774','585.2083','6.4089',NULL,NULL,NULL,16,0,16,'2023-03-12 20:52:00',1468,NULL),
	(16,'Магазин Robs Liquor','rebs',NULL,*********,73340754,28260133,'-2973.9624','392.8493','15.0384','-2967.834','391.6593','13.5433',NULL,NULL,NULL,16,0,16,'2023-03-11 21:11:00',1330,NULL),
	(17,'Магазин LTD','24_7',NULL,*********,2005655,566642,'-1819.5641','789.5856','138.1398','-1820.4854','792.6774','137.1107',NULL,NULL,NULL,14,0,16,'2023-03-25 21:01:01',2340,NULL),
	(18,'Магазин LTD','24_7',NULL,1********,833170,336201,'-702.8369','-917.4727','19.2140','-707.4088','-913.9871','18.2155',NULL,NULL,NULL,16,0,16,'2023-03-27 20:51:01',2610,NULL),
	(19,'Магазин LTD','24_7',NULL,1********,554391,360059,'1163.12304','-326.8310','69.2298','1163.4523','-323.0612','68.2051',NULL,NULL,NULL,15,0,16,'2023-03-26 21:01:01',2482,NULL),
	(20,'Магазин LTD','24_7',NULL,*********,52127203,22690956,'-48.3493','-1761.3674','29.4396','47.8701','-1757.2685','29.4210',NULL,NULL,NULL,16,0,16,'2023-03-24 20:51:01',2246,NULL),
	(21,'Магазин 24/7','24_7',NULL,*********,2689143,3051446,'166.05','6632.20','31.53','162.60','6641.87','31.70',NULL,NULL,NULL,16,0,16,'2023-03-18 20:41:00',1909,NULL),
	(22,'Магазин Robs Liquor','rebs',NULL,120000000,23196093,12571296,'-153.32','6324.95','31.58','-160.20','6322.52','31.58',NULL,NULL,NULL,18,0,19,'2023-03-26 21:21:00',2484,NULL);

/*!40000 ALTER TABLE `item_shops` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `items` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `itemId` smallint(5) unsigned DEFAULT NULL,
  `owner` int(10) unsigned DEFAULT NULL,
  `slot` smallint(4) unsigned DEFAULT NULL,
  `position` enum('player','trunk','ftrunk','glove','fglove','house','drop','item','clothes','garbage','warehouse','family_warehouse','apartment','fridge','afridge','wallet','roulette','bag','marketplace','modifications','warehouseJobs','warehouseJobs_Oil','warehouseJobs_Drug','warehouseJobs_Fish','warehouseJobs_Mushrm','warehouseJobs_Lumberjack','warehouseJobs_Miner','orgStash','orgCompany','office_gunlocker','office_wardrobe','warehouse_mansion','vehicle_numberplate') DEFAULT 'drop',
  `count` int(11) unsigned DEFAULT 0,
  `condition` smallint(5) unsigned DEFAULT 0,
  `pX` varchar(10) DEFAULT NULL,
  `pY` varchar(10) DEFAULT NULL,
  `pZ` varchar(10) DEFAULT NULL,
  `rX` varchar(10) DEFAULT NULL,
  `rY` varchar(10) DEFAULT NULL,
  `rZ` varchar(10) DEFAULT NULL,
  `dimension` int(10) DEFAULT NULL,
  `info` blob DEFAULT NULL,
  `component` tinyint(3) unsigned DEFAULT NULL,
  `drawable` smallint(5) unsigned DEFAULT NULL,
  `texture` smallint(5) unsigned DEFAULT NULL,
  `isProp` tinyint(1) unsigned DEFAULT NULL,
  `gender` tinyint(1) unsigned DEFAULT NULL,
  `isTurn` tinyint(1) DEFAULT 0,
  `numberplate` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `itemId` (`itemId`),
  KEY `owner` (`owner`),
  KEY `position` (`position`),
  KEY `numberplate` (`numberplate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Предметы инвентаря';



CREATE TABLE `items_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `itemSqlId` int(10) unsigned DEFAULT NULL,
  `itemId` smallint(5) unsigned DEFAULT NULL,
  `comment` varchar(64) DEFAULT NULL,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`args`)),
  `org` varchar(20) DEFAULT NULL,
  `amount` int(10) unsigned DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `source` enum('money_log','donate_log','fraction','quest','admin') DEFAULT NULL,
  `transactionId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `itemSqlId` (`itemSqlId`),
  KEY `comment` (`comment`),
  KEY `date` (`date`),
  KEY `accountId` (`accountId`),
  KEY `itemId` (`itemId`),
  KEY `org` (`org`),
  KEY `transactionId` (`transactionId`),
  KEY `source` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `items_trade_logs` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `player1` mediumint(7) unsigned DEFAULT NULL,
  `player2` mediumint(7) unsigned DEFAULT NULL,
  `amount1` bigint(20) unsigned DEFAULT NULL,
  `amount2` bigint(20) unsigned DEFAULT NULL,
  `items1` varchar(60) DEFAULT NULL,
  `items2` varchar(60) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `player1` (`player1`),
  KEY `player2` (`player2`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `jackpot_bets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) DEFAULT NULL,
  `accountId` int(11) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `ticket` varchar(10) DEFAULT NULL,
  `gameId` int(11) DEFAULT NULL,
  `dateBet` datetime DEFAULT current_timestamp(),
  `socialClub` varchar(16) DEFAULT NULL,
  `login` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userId` (`userId`),
  KEY `gameId` (`gameId`),
  KEY `dateBet` (`dateBet`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `jackpot_games` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playersCount` int(11) NOT NULL,
  `bank` int(11) NOT NULL,
  `winnerId` int(11) NOT NULL DEFAULT 0,
  `winnerChance` float NOT NULL,
  `winnerPosition` float NOT NULL DEFAULT 0,
  `dateGame` datetime NOT NULL DEFAULT current_timestamp(),
  `winnerLogin` varchar(20) DEFAULT NULL,
  `winnerSocialClub` varchar(16) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `winnerId` (`winnerId`),
  KEY `dateGame` (`dateGame`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `key_binds` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `type` enum('vehicle','warehouse','apartment') DEFAULT NULL,
  `key` int(10) unsigned DEFAULT NULL,
  `itemSqlId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `key` (`key`),
  KEY `itemSqlId` (`itemSqlId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `mafia_zones_reset` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reset_time` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC COMMENT='Тут будет храниться дата для обнуления территорий';



CREATE TABLE `marketplace_ads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) NOT NULL DEFAULT 0,
  `nickname` varchar(20) NOT NULL DEFAULT '',
  `socialClub` varchar(16) NOT NULL DEFAULT '',
  `type` varchar(20) NOT NULL DEFAULT '',
  `price` bigint(20) unsigned NOT NULL DEFAULT 0,
  `favorites` int(11) NOT NULL DEFAULT 0,
  `views` int(11) NOT NULL DEFAULT 0,
  `comment` varchar(250) NOT NULL DEFAULT '',
  `phoneNumber` varchar(8) DEFAULT NULL,
  `args` blob DEFAULT NULL,
  `images` longtext DEFAULT NULL,
  `buyerId` mediumint(7) DEFAULT NULL,
  `date` datetime NOT NULL,
  `endDate` datetime NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `endDate` (`endDate`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `marketplace_auction` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` int(11) DEFAULT NULL,
  `type` varchar(10) NOT NULL,
  `keyId` int(11) NOT NULL,
  `bidStart` bigint(20) unsigned NOT NULL DEFAULT 0,
  `bidStep` bigint(20) unsigned NOT NULL DEFAULT 0,
  `currentBet` bigint(20) unsigned DEFAULT NULL,
  `participants` smallint(6) NOT NULL DEFAULT 0,
  `favorites` smallint(6) NOT NULL DEFAULT 0,
  `views` smallint(6) NOT NULL DEFAULT 0,
  `startDate` datetime NOT NULL,
  `endDate` datetime NOT NULL,
  `finalPrice` bigint(20) unsigned DEFAULT NULL,
  `winnerId` int(11) DEFAULT NULL,
  `warehouseId` int(11) DEFAULT NULL,
  `args` blob DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `endDate` (`endDate`) USING BTREE,
  KEY `winnerId` (`winnerId`) USING BTREE,
  KEY `warehouseId` (`warehouseId`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `keyId` (`keyId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `marketplace_auction_bets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `auctionId` int(11) DEFAULT NULL,
  `accountId` int(11) DEFAULT NULL,
  `type` varchar(15) DEFAULT NULL,
  `time` datetime DEFAULT NULL,
  `bid` bigint(20) unsigned DEFAULT NULL,
  `active` tinyint(1) DEFAULT 0,
  `params` blob DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `auctionId` (`auctionId`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `type` (`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `marketplace_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` int(11) NOT NULL,
  `type` varchar(50) DEFAULT NULL,
  `tableName` varchar(50) DEFAULT NULL,
  `cost` bigint(20) unsigned DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `args` blob DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='История покупок на маркетплейсе';



CREATE TABLE `marketplace_trading` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) DEFAULT NULL,
  `name` varchar(20) DEFAULT NULL,
  `amount` bigint(20) unsigned NOT NULL DEFAULT 0,
  `condition` int(11) NOT NULL DEFAULT 0,
  `price` bigint(20) unsigned NOT NULL DEFAULT 0,
  `itemId` int(11) NOT NULL,
  `type` varchar(100) NOT NULL,
  `buyerIds` mediumblob DEFAULT NULL,
  `itemSqlIds` mediumblob DEFAULT NULL,
  `endDate` datetime NOT NULL,
  `date` datetime NOT NULL,
  `args` mediumblob NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `endDate` (`endDate`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Торговая площадка';



CREATE TABLE `marketplace_trading_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tradingId` int(11) DEFAULT NULL,
  `itemId` int(11) NOT NULL,
  `accountFrom` int(11) DEFAULT NULL,
  `accountTo` int(11) NOT NULL,
  `amount` bigint(20) unsigned NOT NULL DEFAULT 0,
  `component` smallint(5) unsigned DEFAULT NULL,
  `drawable` smallint(5) unsigned DEFAULT NULL,
  `texture` smallint(5) unsigned DEFAULT NULL,
  `gender` tinyint(1) unsigned DEFAULT NULL,
  `isProp` tinyint(1) unsigned DEFAULT NULL,
  `itemSqlIds` blob DEFAULT NULL,
  `price` bigint(20) unsigned NOT NULL DEFAULT 0,
  `date` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `date` (`date`) USING BTREE,
  KEY `accountFrom` (`accountFrom`) USING BTREE,
  KEY `accountTo` (`accountTo`) USING BTREE,
  KEY `itemId` (`itemId`) USING BTREE,
  KEY `component` (`component`),
  KEY `drawable` (`drawable`),
  KEY `texture` (`texture`),
  KEY `gender` (`gender`),
  KEY `isProp` (`isProp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `marketplace_warehouse` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` int(11) DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `endDate` datetime NOT NULL,
  `args` mediumblob NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `endDate` (`endDate`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `mediaSounds_purchased` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` int(10) unsigned NOT NULL,
  `userId` int(11) DEFAULT NULL,
  `soundId` smallint(5) unsigned NOT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId_userId` (`accountId`,`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `minigames_f2_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL DEFAULT 0,
  `accountId` int(11) NOT NULL DEFAULT 0,
  `game` varchar(10) NOT NULL DEFAULT '0',
  `type` varchar(100) NOT NULL DEFAULT '',
  `gameId` int(11) DEFAULT 0,
  `amount` int(11) NOT NULL DEFAULT 0,
  `args` longtext DEFAULT NULL,
  `date` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userId` (`userId`),
  KEY `gameId` (`gameId`),
  KEY `accountId` (`accountId`),
  KEY `game` (`game`),
  KEY `type` (`type`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `minigames_ranking` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `soloElo` smallint(5) unsigned DEFAULT NULL,
  `partyElo` smallint(5) unsigned DEFAULT NULL,
  `previousSoloElo` smallint(5) unsigned DEFAULT NULL,
  `previousPartyElo` smallint(5) unsigned DEFAULT NULL,
  `updateDate` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `money_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `bankId` int(10) unsigned DEFAULT NULL,
  `amount` bigint(12) DEFAULT NULL,
  `prev` bigint(12) unsigned DEFAULT NULL,
  `comment` varchar(128) DEFAULT NULL,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`args`)),
  `date` datetime DEFAULT NULL,
  `biz` varchar(20) DEFAULT NULL,
  `taxPercent` varchar(4) DEFAULT NULL,
  `mafiaPercent` tinyint(2) unsigned DEFAULT NULL,
  `unitPrice` bigint(12) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `bankId` (`bankId`) USING BTREE,
  KEY `date` (`date`) USING BTREE,
  KEY `comment` (`comment`) USING BTREE,
  KEY `amount` (`amount`) USING BTREE,
  KEY `biz` (`biz`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Логи денег\r\n\r\n999 999 999.99';



CREATE TABLE `multiaccounts` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `login` varchar(20) DEFAULT NULL,
  `ip` varchar(15) DEFAULT NULL,
  `socialClub` varchar(16) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `userId` (`userId`),
  KEY `ip` (`ip`),
  KEY `socialClub` (`socialClub`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `mysql_migrations_347ertt3e` (
  `timestamp` varchar(254) NOT NULL,
  UNIQUE KEY `timestamp` (`timestamp`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

LOCK TABLES `mysql_migrations_347ertt3e` WRITE;
/*!40000 ALTER TABLE `mysql_migrations_347ertt3e` DISABLE KEYS */;

INSERT INTO `mysql_migrations_347ertt3e` (`timestamp`)
VALUES
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('1648042394737'),
	('1648641693798'),
	('1649410924546'),
	('1649529413639'),
	('1649603731864'),
	('1649619687533'),
	('1649670859651'),
	('1649681819453'),
	('1649682732450'),
	('1649708820365'),
	('1649866686869'),
	('1649918782005'),
	('1650367489000'),
	('1650627290670'),
	('1650738130839'),
	('1650812795139'),
	('1650813592160'),
	('1651412645722'),
	('1651416949960'),
	('1651509087015'),
	('1651839545930'),
	('1651914676478'),
	('1652196518947'),
	('1652975369596'),
	('1652977952012'),
	('1652978404101'),
	('1652980299866'),
	('1654075486783'),
	('1654171819509'),
	('1654366771222'),
	('1654619152956'),
	('1654807197015'),
	('1654977968229'),
	('1655102511224'),
	('1655102571338'),
	('1655229941731'),
	('1655318197478'),
	('1655372980721'),
	('1655482272736'),
	('1656245132321'),
	('1656331094312'),
	('1656517258244'),
	('1658058556003'),
	('1658513127753'),
	('1658728535763'),
	('1658949248587'),
	('1659031154205'),
	('1659031383327'),
	('1659031610286'),
	('1659126559984'),
	('1659168364620'),
	('1659298340361'),
	('1659354456831'),
	('1662024636124'),
	('1663322174754'),
	('1665082298149'),
	('1665082783290'),
	('1665596926206'),
	('1665598902962'),
	('1665601029424'),
	('1665784456178'),
	('1665784524197'),
	('1665914121817'),
	('1665947684243'),
	('1666042768878'),
	('1666079287115'),
	('1666081542326'),
	('1666369281934'),
	('1666583745796'),
	('1666900302028'),
	('1666950446743'),
	('1667218660607'),
	('1667218812984'),
	('1667240004807'),
	('1667499908528'),
	('1667500197298'),
	('1667507929992'),
	('1667508452289'),
	('1667649834363'),
	('1667674588419'),
	('1667755285698'),
	('1668967057097'),
	('1669031132841'),
	('1669284084234'),
	('1669294849428'),
	('1669658548005'),
	('1670007658164'),
	('1670598603424'),
	('1670936617422'),
	('1671026601579'),
	('1671100766928'),
	('1671367445572'),
	('1671465002875'),
	('1671465835283'),
	('1671479627863'),
	('1671561053851'),
	('1671747855165'),
	('1671819286963'),
	('1671825330700'),
	('1671825925861'),
	('1671900756202'),
	('1672049791116'),
	('1672098295335'),
	('1672170460423'),
	('1672170636505'),
	('1672175474959'),
	('1672176386583'),
	('1672259082805'),
	('1672263901933'),
	('1672321601825'),
	('1673005043589'),
	('1673357514799'),
	('1673486882931'),
	('1673637276013'),
	('1673637413324'),
	('1674226732532'),
	('1674578344904'),
	('1674648504977'),
	('1674687058268'),
	('1674773590371'),
	('1675206644153'),
	('1675521092237'),
	('1676109675533'),
	('1676192602071'),
	('1676581723898'),
	('1676582169971'),
	('1676727132023'),
	('1676761796765'),
	('1676823329495'),
	('1676829650333'),
	('1677056398043'),
	('1677079621932'),
	('1677153593311'),
	('1677438832145'),
	('1677533427399'),
	('1677615026662'),
	('1677618108342'),
	('1678272476871'),
	('1678272476872'),
	('1678576336387'),
	('1678822189106'),
	('1679054108988'),
	('1679055344211'),
	('1679087391816'),
	('1679087681252'),
	('1679087773396'),
	('1679087831088'),
	('1679087893995'),
	('1679087927065'),
	('1679088047449'),
	('1679146215365'),
	('1679338791453'),
	('1679675300185'),
	('1679817958453'),
	('1679830370889'),
	('1680007197353'),
	('1680527390448'),
	('1680540203969'),
	('1680708467414'),
	('1681152840907'),
	('1681297754395'),
	('1681405797906'),
	('1681477342842'),
	('1681477725919'),
	('1682025022036'),
	('1682025030573'),
	('1682166221403'),
	('1682202464574'),
	('1682619396470'),
	('1682626797788'),
	('1682684497806'),
	('1683273891157'),
	('1683277654993'),
	('1683364414325'),
	('1683650106165'),
	('1683961607532'),
	('1683961614366'),
	('1684175591413'),
	('1684414557482'),
	('1684482811937'),
	('1684599882623'),
	('1684606717636'),
	('1684707355834'),
	('1684770025509'),
	('1685103887572'),
	('1685618405760'),
	('1685694666984'),
	('1685780768945'),
	('1685790987178'),
	('1685887709134'),
	('1686001341643'),
	('1686232122144'),
	('1686234030071'),
	('1686322351510'),
	('1686576097820'),
	('1686914907635'),
	('1687214669876'),
	('1687678520505'),
	('1687693949191'),
	('1687860395737'),
	('1688489292722'),
	('1688685714026'),
	('1689254640065'),
	('1689451646530'),
	('1689947730318'),
	('1689956815529'),
	('1689956815629'),
	('1689956816529'),
	('1690204056388'),
	('1690286449711'),
	('1690449452818'),
	('1690550803522'),
	('1690713575827'),
	('1691528002691'),
	('1691927760964'),
	('1692175336752'),
	('1692178905694'),
	('1692286716917'),
	('1692537702546'),
	('1692873679055'),
	('1693281943447'),
	('1694111914273'),
	('1694174958719'),
	('1694185352641'),
	('1694523579227'),
	('1694611758736'),
	('1694828779711'),
	('1694915203739'),
	('1694915464072'),
	('1694918558692'),
	('1694980384789'),
	('1695168662638'),
	('1695291568347'),
	('1695395284915'),
	('1696608443946'),
	('1697198953767'),
	('1697215587210'),
	('1697459820287'),
	('1697489870324'),
	('1697579616403'),
	('1697668706148'),
	('1697714224051'),
	('1697800152744'),
	('1697898963983'),
	('1698111433876'),
	('1698154985826'),
	('1698595448734'),
	('1698685003074'),
	('1698792804937'),
	('1698857488218'),
	('1698946466695'),
	('1699000264289'),
	('1699002760773'),
	('1699010927964'),
	('1699041576518'),
	('1699128824431'),
	('1699310628114'),
	('1699390073155'),
	('1699459963639'),
	('1699879533353'),
	('1699880372676'),
	('1699880962132'),
	('1699881300398'),
	('1699881855395'),
	('1699890928021'),
	('1700493875626'),
	('1700500696684'),
	('1700582965198'),
	('1700586730237'),
	('1700602789994'),
	('1700711909528'),
	('1700755294938'),
	('1700777184032'),
	('1700813112852'),
	('1700842756540'),
	('1701285007553'),
	('1701360562823'),
	('1701364137969'),
	('1701872649370'),
	('1702234404017'),
	('1702490816694'),
	('1702511390864'),
	('1702801061025'),
	('1702803534310'),
	('1702881386915'),
	('1703091967791'),
	('1703677631954'),
	('1703856401663'),
	('1704536499794'),
	('1704555288732'),
	('1704935836926'),
	('1705584912393'),
	('1705934853328'),
	('1706138444205'),
	('1707133605433'),
	('1707163721210'),
	('1707566917146'),
	('1707586879516'),
	('1707753447057'),
	('1707812428883'),
	('1707843720352'),
	('1708081749849'),
	('1708786724174'),
	('1708956910310'),
	('1709060956246'),
	('1709757196687'),
	('1709830563243'),
	('1709837766230'),
	('1709879297022'),
	('1709901432804'),
	('1709911376912'),
	('1709915932173'),
	('1709999061176'),
	('1710107886501'),
	('1710347577984'),
	('1710348438255'),
	('1710432366559'),
	('1710434596852'),
	('1710436138531'),
	('1710530983551'),
	('1710716031757'),
	('1710945950766'),
	('1711315735089'),
	('1711355808703'),
	('1711369054043'),
	('1711460262652'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************'),
	('*************');

/*!40000 ALTER TABLE `mysql_migrations_347ertt3e` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `name_logs` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `oldName` varchar(24) DEFAULT NULL,
  `newName` varchar(24) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `admin` mediumint(7) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `oldName` (`oldName`),
  KEY `newName` (`newName`),
  KEY `date` (`date`),
  KEY `admin` (`admin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `news_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `creatorId` int(11) DEFAULT NULL,
  `reportId` int(11) DEFAULT NULL,
  `factionId` int(11) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `status` tinyint(1) DEFAULT NULL,
  `createdAt` datetime DEFAULT NULL,
  `updatedAt` datetime DEFAULT NULL,
  `deployAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `offices` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `buildingId` tinyint(3) unsigned NOT NULL DEFAULT 0,
  `familyId` smallint(5) unsigned DEFAULT NULL,
  `cash` bigint(12) unsigned DEFAULT 0,
  `officeInterior` tinyint(1) unsigned DEFAULT 0,
  `officeMoneySafe` tinyint(1) unsigned DEFAULT 0,
  `officeGunLocker` tinyint(1) unsigned DEFAULT 0,
  `officeWardrobe` tinyint(1) unsigned DEFAULT 0,
  `heliport` tinyint(1) unsigned DEFAULT 0,
  `garageDecor` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`garageDecor`)),
  `garage` tinyint(3) unsigned DEFAULT NULL,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `familyId` (`familyId`) USING BTREE,
  KEY `paidUntil` (`paidUntil`) USING BTREE,
  KEY `buildingId` (`buildingId`),
  KEY `auctionId` (`auctionId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC COMMENT='Семейные офисы';

LOCK TABLES `offices` WRITE;
/*!40000 ALTER TABLE `offices` DISABLE KEYS */;

INSERT INTO `offices` (`id`, `buildingId`, `familyId`, `cash`, `officeInterior`, `officeMoneySafe`, `officeGunLocker`, `officeWardrobe`, `heliport`, `garageDecor`, `garage`, `paidUntil`, `auctionId`)
VALUES
	(1,1,NULL,0,0,0,0,0,0,NULL,0,'2024-03-13 18:57:18',2718),
	(2,1,NULL,0,0,0,0,0,0,NULL,0,'2024-03-16 14:14:17',2722),
	(3,1,NULL,0,0,0,0,0,0,NULL,0,'2023-07-21 22:26:33',2692),
	(4,1,NULL,0,0,0,0,0,0,NULL,0,'2023-07-21 23:50:36',2671),
	(5,1,NULL,0,0,0,0,0,0,NULL,0,'2023-10-21 16:11:46',2706),
	(6,1,NULL,0,0,0,0,0,0,NULL,0,'2024-03-09 11:11:40',2717),
	(7,1,NULL,0,0,0,0,0,0,NULL,0,'2024-03-12 20:27:29',2716),
	(8,1,NULL,0,0,0,0,0,0,NULL,0,'2024-04-04 18:12:17',2734),
	(9,1,NULL,0,0,0,0,0,0,NULL,0,'2024-04-22 17:04:11',2743),
	(10,1,NULL,0,0,0,0,0,0,NULL,0,'2024-04-08 15:24:12',2740),
	(11,1,NULL,0,0,0,0,0,0,NULL,0,'2024-05-01 18:53:21',2809),
	(12,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(13,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(14,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(15,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(16,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(17,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(18,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(19,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(20,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(21,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(22,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(23,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(24,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(25,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(26,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(27,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(28,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(29,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(30,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(31,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(32,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(33,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(34,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(35,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(36,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(37,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(38,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(39,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(40,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(41,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(42,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(43,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(44,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(45,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(46,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(47,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(48,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(49,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(50,1,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(51,2,NULL,0,0,0,0,0,0,NULL,0,'2023-07-20 21:51:01',2680),
	(52,2,NULL,0,0,0,0,0,0,NULL,0,'2023-07-21 15:44:52',2691),
	(53,2,NULL,0,0,0,0,0,0,NULL,0,'2024-02-28 19:06:54',2712),
	(54,2,NULL,0,0,0,0,0,0,NULL,0,'2024-03-02 19:07:28',2714),
	(55,2,NULL,0,0,0,0,0,0,NULL,0,'2024-03-16 14:28:19',2723),
	(56,2,NULL,0,0,0,0,0,0,NULL,0,'2024-04-15 16:33:24',2741),
	(57,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(58,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(59,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(60,2,NULL,0,0,0,0,0,0,NULL,0,'2023-07-21 01:03:26',2672),
	(61,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(62,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(63,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(64,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(65,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(66,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(67,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(68,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(69,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(70,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(71,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(72,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(73,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(74,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(75,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(76,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(77,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(78,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(79,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(80,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(81,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(82,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(83,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(84,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(85,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(86,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(87,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(88,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(89,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(90,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(91,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(92,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(93,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(94,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(95,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(96,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(97,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(98,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(99,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(100,2,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(101,3,NULL,0,0,0,0,0,0,NULL,0,'2023-07-20 20:24:14',2679),
	(102,3,NULL,0,0,0,0,0,0,NULL,0,'2023-07-21 00:22:29',2685),
	(103,3,NULL,0,0,0,0,0,0,NULL,0,'2024-02-28 21:01:26',2713),
	(104,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(105,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(106,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(107,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(108,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(109,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(110,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(111,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(112,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(113,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(114,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(115,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(116,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(117,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(118,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(119,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(120,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(121,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(122,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(123,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(124,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(125,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(126,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(127,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(128,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(129,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(130,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(131,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(132,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(133,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(134,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(135,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(136,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(137,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(138,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(139,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(140,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(141,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(142,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(143,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(144,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(145,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(146,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(147,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(148,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(149,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(150,3,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(151,4,NULL,0,0,0,0,0,0,NULL,0,'2023-07-21 01:26:02',2687),
	(152,4,NULL,0,0,0,0,0,0,NULL,0,'2023-07-21 01:55:13',2668),
	(153,4,NULL,0,0,0,0,0,0,NULL,0,'2023-10-20 09:37:47',2703),
	(154,4,NULL,0,0,0,0,0,0,NULL,0,'2024-03-18 16:05:13',2725),
	(155,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(156,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(157,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(158,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(159,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(160,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(161,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(162,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(163,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(164,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(165,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(166,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(167,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(168,4,NULL,0,8,1,1,1,1,X'7B226C69676874696E67223A362C227369676E73223A392C22696E746572696F7273223A347D',30,'2023-07-21 01:11:19',NULL),
	(169,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(170,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(171,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(172,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(173,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(174,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(175,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(176,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(177,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(178,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(179,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(180,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(181,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(182,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(183,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(184,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(185,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(186,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(187,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(188,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(189,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(190,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(191,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(192,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(193,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(194,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(195,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(196,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(197,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(198,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(199,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL),
	(200,4,NULL,0,0,0,0,0,0,NULL,NULL,NULL,NULL);

/*!40000 ALTER TABLE `offices` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `online` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `players` smallint(5) unsigned NOT NULL DEFAULT 0,
  `date` bigint(20) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `online_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `date` date DEFAULT NULL,
  `maxOnline` smallint(5) unsigned DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `date` (`date`),
  KEY `maxOnline` (`maxOnline`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `phone_contacts` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `number` varchar(8) DEFAULT NULL,
  `name` varchar(24) DEFAULT NULL,
  `blocked` tinyint(1) DEFAULT 0,
  `ringtone` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `number` (`number`),
  KEY `name` (`name`),
  KEY `accountId` (`accountId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Контакты в телефоне';



CREATE TABLE `phone_favourite` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` int(11) NOT NULL,
  `phoneNumber` varchar(8) NOT NULL DEFAULT '',
  `socialClub` varchar(16) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `phoneNumber` (`phoneNumber`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `phone_history` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) DEFAULT NULL,
  `callId` int(10) DEFAULT NULL,
  `accountFrom` mediumint(7) unsigned DEFAULT NULL,
  `numberFrom` varchar(8) DEFAULT NULL,
  `accountTo` mediumint(7) unsigned DEFAULT NULL,
  `numberTo` varchar(8) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `status` enum('offline','missed','success','waiting') DEFAULT NULL,
  `statusView` int(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `date` (`date`),
  KEY `accountFrom` (`accountFrom`),
  KEY `accountTo` (`accountTo`),
  KEY `status` (`status`),
  KEY `accountId` (`accountId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='История звонков в телефоне';



CREATE TABLE `phone_sms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountFrom` mediumint(7) DEFAULT NULL,
  `numberFrom` varchar(24) DEFAULT NULL,
  `accountTo` mediumint(7) DEFAULT NULL,
  `numberTo` varchar(24) DEFAULT NULL,
  `text` varchar(512) DEFAULT NULL,
  `photos` blob DEFAULT NULL,
  `status` tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '0 - sent, 1 - delivered, 2 - read',
  `deleteBy` enum('none','sender','recipient') CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT 'none',
  `date` datetime DEFAULT NULL,
  `statusDate` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `date` (`date`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `deleteBy` (`deleteBy`) USING BTREE,
  KEY `accountFrom` (`accountFrom`) USING BTREE,
  KEY `accountTo` (`accountTo`) USING BTREE,
  KEY `numberFrom` (`numberFrom`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='СМС в телефоне';



CREATE TABLE `phones` (
  `id` mediumint(9) NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `number` varchar(8) DEFAULT NULL,
  `brightness` tinyint(4) DEFAULT 100,
  `volume` tinyint(4) DEFAULT 50,
  `wallpaper` tinyint(4) DEFAULT 0,
  `sms_ringtone` tinyint(4) DEFAULT 0,
  `call_ringtone` tinyint(4) DEFAULT 0,
  `dnd` tinyint(1) DEFAULT 0,
  `balance` int(11) DEFAULT 1500000,
  PRIMARY KEY (`id`),
  UNIQUE KEY `number` (`number`),
  UNIQUE KEY `accountId` (`accountId`),
  KEY `balance` (`balance`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Мобильные телефоны';



CREATE TABLE `plant_weed_time` (
  `hours` blob DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

LOCK TABLES `plant_weed_time` WRITE;
/*!40000 ALTER TABLE `plant_weed_time` DISABLE KEYS */;

INSERT INTO `plant_weed_time` (`hours`)
VALUES
	(X'5B32312C32322C32332C32305D');

/*!40000 ALTER TABLE `plant_weed_time` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `promocodes` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `invitedBy` mediumint(7) unsigned DEFAULT NULL,
  `media` tinyint(1) unsigned DEFAULT 0,
  `date` datetime DEFAULT NULL,
  `confirm` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `invitedBy` (`invitedBy`),
  KEY `date` (`date`),
  KEY `confirm` (`confirm`),
  KEY `media` (`media`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `property_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `tableName` varchar(30) DEFAULT NULL,
  `keyId` mediumint(8) unsigned DEFAULT NULL,
  `cost` bigint(20) unsigned DEFAULT NULL,
  `accountId2` mediumint(7) unsigned DEFAULT NULL,
  `comment` varchar(100) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `tableName` (`tableName`),
  KEY `key` (`keyId`),
  KEY `date` (`date`),
  KEY `accountId2` (`accountId2`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `quests` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `questId` smallint(5) unsigned DEFAULT NULL,
  `stageId` smallint(5) unsigned DEFAULT NULL,
  `progress` mediumint(8) unsigned DEFAULT NULL,
  `state` enum('active','success','failed','cancel') DEFAULT 'active',
  `date` datetime DEFAULT NULL,
  `cooldown` datetime DEFAULT NULL,
  `hasUse` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `ready` (`state`),
  KEY `questId` (`questId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Статусы квестов у игроков';



CREATE TABLE `quizzes` (
  `id` mediumint(7) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `quizId` mediumint(7) NOT NULL,
  `answer` varchar(50) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `quizId` (`quizId`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Система опроса игроков';



CREATE TABLE `report_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reportId` int(11) DEFAULT NULL,
  `characterId` int(11) DEFAULT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `type` enum('answer','question','notification') DEFAULT NULL,
  `assets` mediumtext DEFAULT '[]',
  `nickname` varchar(50) DEFAULT NULL,
  `socialClub` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `reportId` (`reportId`),
  KEY `characterId` (`characterId`),
  KEY `date` (`date`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `characterId` mediumint(7) DEFAULT NULL,
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `type` enum('default','billboard','family_logo','gov_news') DEFAULT NULL,
  `status` enum('new','closed','reopened','billboard','billboard_accepted','billboard_declined','family_logo','family_logo_accepted','family_logo_declined','gov_news','gov_news_accepted','gov_news_declined') DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `mark` tinyint(1) DEFAULT NULL,
  `admin` mediumint(7) DEFAULT NULL,
  `author` varchar(40) DEFAULT NULL,
  `familyLogoType` enum('png','jpg','jpeg') DEFAULT NULL,
  `exceptionId` int(11) DEFAULT NULL,
  `selected` int(11) DEFAULT NULL,
  `firstAnswerAt` datetime DEFAULT NULL,
  `lastAnswerAt` datetime DEFAULT NULL,
  `lastQuestionAt` datetime DEFAULT NULL,
  `closedAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `characterId` (`characterId`),
  KEY `type` (`type`),
  KEY `status` (`status`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `roulette_items` (
  `id` varchar(36) NOT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `value` varchar(35) DEFAULT NULL,
  `price` mediumint(8) unsigned DEFAULT NULL,
  `type` varchar(20) DEFAULT NULL,
  `color` varchar(10) DEFAULT NULL,
  `seasonPassId` tinyint(3) unsigned DEFAULT NULL,
  `clothesId` int(10) unsigned DEFAULT NULL,
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`args`)),
  `date` datetime DEFAULT NULL,
  `status` enum('active','sold','taken') DEFAULT 'active',
  `actionDate` datetime DEFAULT NULL,
  `caseId` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `userId` (`userId`),
  KEY `value` (`value`),
  KEY `price` (`price`),
  KEY `type` (`type`),
  KEY `color` (`color`),
  KEY `date` (`date`),
  KEY `status` (`status`),
  KEY `actionDate` (`actionDate`),
  KEY `seasonPassId` (`seasonPassId`),
  KEY `clothesId` (`clothesId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `season_pass` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` int(11) unsigned DEFAULT NULL,
  `userId` int(11) unsigned DEFAULT NULL,
  `level` smallint(5) unsigned NOT NULL DEFAULT 0,
  `xp` mediumint(9) unsigned NOT NULL DEFAULT 0,
  `tasks` mediumint(9) unsigned NOT NULL DEFAULT 0,
  `premium` tinyint(3) unsigned NOT NULL DEFAULT 0,
  `dateStart` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `userId` (`userId`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `premium` (`premium`),
  KEY `dateStart` (`dateStart`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `season_pass_data` (
  `resetTasksTime` int(10) unsigned DEFAULT NULL,
  `server` varchar(6) DEFAULT NULL,
  UNIQUE KEY `server` (`server`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `season_pass_exp_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(8) unsigned DEFAULT NULL,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `amount` mediumint(8) unsigned DEFAULT NULL,
  `comment` varchar(30) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `accountId` (`accountId`),
  KEY `comment` (`comment`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Логи получения опыта батлпаса';



CREATE TABLE `season_pass_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` mediumint(9) NOT NULL DEFAULT 0,
  `awardIndex` smallint(6) NOT NULL DEFAULT 0,
  `awardSubIndex` smallint(6) NOT NULL DEFAULT 0 COMMENT 'У приза может быть несколько предметов',
  `variantIndex` tinyint(4) DEFAULT NULL,
  `levelType` enum('free','premium') NOT NULL DEFAULT 'free',
  `claimType` enum('xp','take') DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userId` (`userId`) USING BTREE,
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Предметы полученные игроком в сезоннике';



CREATE TABLE `season_pass_last` (
  `userId` int(10) unsigned NOT NULL,
  `level` tinyint(3) unsigned DEFAULT NULL,
  PRIMARY KEY (`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `season_pass_payday` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(8) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `season_pass_tasks` (
  `id` varchar(36) NOT NULL DEFAULT '',
  `userId` mediumint(9) NOT NULL DEFAULT 0,
  `taskId` smallint(6) NOT NULL,
  `progress` mediumint(9) NOT NULL DEFAULT 0,
  `isComplete` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userId` (`userId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `send_command_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server` varchar(10) DEFAULT NULL,
  `login` varchar(20) DEFAULT NULL,
  `accountId` int(11) DEFAULT NULL,
  `command` varchar(15) DEFAULT NULL,
  `args` blob DEFAULT NULL,
  `status` enum('error','pending','success') DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `server_settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `keyName` varchar(50) DEFAULT NULL,
  `value` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `key` (`keyName`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

LOCK TABLES `server_settings` WRITE;
/*!40000 ALTER TABLE `server_settings` DISABLE KEYS */;

INSERT INTO `server_settings` (`id`, `keyName`, `value`)
VALUES
	(6,'snowManCurrentStage',0),
	(7,'snowManCurrentProgress',0);

/*!40000 ALTER TABLE `server_settings` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `session_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `enterDate` datetime DEFAULT NULL,
  `exitDate` datetime DEFAULT NULL,
  `exitReason` varchar(40) DEFAULT NULL,
  `hwid` varchar(128) DEFAULT NULL,
  `socialClub` varchar(16) DEFAULT NULL,
  `rgscId` int(10) unsigned DEFAULT NULL,
  `ip` varchar(15) DEFAULT NULL,
  `security` enum('2fa','telegram','email','2faSuccess','telegramSuccess','emailSuccess') DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `enterDate` (`enterDate`) USING BTREE,
  KEY `exitDate` (`exitDate`) USING BTREE,
  KEY `exitReason` (`exitReason`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `hwid` (`hwid`) USING BTREE,
  KEY `socialClub` (`socialClub`) USING BTREE,
  KEY `ip` (`ip`) USING BTREE,
  KEY `rgscId` (`rgscId`),
  KEY `userId` (`userId`),
  KEY `security` (`security`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Логи посещений сервера';



CREATE TABLE `skills` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `key` varchar(20) DEFAULT NULL,
  `value` mediumint(8) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni` (`accountId`,`key`),
  KEY `accountId` (`accountId`),
  KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;



CREATE TABLE `specials` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `comment` varchar(30) DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `givepromo` tinyint(1) unsigned DEFAULT 0,
  `f5` tinyint(1) unsigned DEFAULT 0,
  `setname` tinyint(1) unsigned DEFAULT 0,
  `veh` tinyint(1) unsigned DEFAULT 0,
  `skin` tinyint(1) unsigned DEFAULT 0,
  `gtp` tinyint(1) unsigned DEFAULT 0,
  `mtp` tinyint(1) unsigned DEFAULT 0,
  `aveh` tinyint(1) unsigned DEFAULT 0,
  `mute` tinyint(1) unsigned DEFAULT 0,
  `ajail` tinyint(1) unsigned DEFAULT 0,
  `delveh` tinyint(1) unsigned DEFAULT 0,
  `tp` tinyint(1) unsigned DEFAULT 0,
  `gh` tinyint(1) unsigned DEFAULT 0,
  `kick` tinyint(1) unsigned DEFAULT 0,
  `setvip` tinyint(1) unsigned DEFAULT 0,
  `gm` tinyint(1) unsigned DEFAULT 0,
  `givecar` tinyint(1) unsigned DEFAULT 0,
  `givedonate` tinyint(1) unsigned DEFAULT 0,
  `givemoney` tinyint(1) unsigned DEFAULT 0,
  `giveskill` tinyint(1) unsigned DEFAULT 0,
  `setemail` tinyint(1) unsigned DEFAULT 0,
  `givematerials` tinyint(1) unsigned DEFAULT 0,
  `setmod` tinyint(1) unsigned DEFAULT 0,
  `reset2fa` tinyint(1) unsigned DEFAULT 0,
  `transfervehicle` tinyint(1) unsigned DEFAULT 0,
  `hp` tinyint(1) DEFAULT 0,
  `rescue` tinyint(1) DEFAULT 0,
  `ban` tinyint(1) DEFAULT 0,
  `unban` tinyint(1) DEFAULT 0,
  `toggleachat` tinyint(1) DEFAULT 0,
  `esp` tinyint(1) DEFAULT 0,
  `clear` tinyint(1) DEFAULT 0,
  `resetbplevel` tinyint(1) unsigned DEFAULT 0,
  `resetbpitems` tinyint(1) unsigned DEFAULT 0,
  `advPartner` tinyint(1) unsigned DEFAULT 0,
  `delitem` tinyint(1) unsigned DEFAULT 0,
  `givecarmod` tinyint(1) unsigned DEFAULT 0,
  `localtime` tinyint(1) unsigned DEFAULT 0,
  `localweather` tinyint(1) unsigned DEFAULT 0,
  `sendCommand` tinyint(1) unsigned DEFAULT 0,
  `giveitem` tinyint(1) unsigned DEFAULT 0,
  `setfamexp` tinyint(1) unsigned DEFAULT 0,
  `setfamrep` tinyint(1) unsigned DEFAULT 0,
  `setfamilyleader` tinyint(1) unsigned DEFAULT 0,
  `giveexp` tinyint(1) unsigned DEFAULT 0,
  `givechips` tinyint(1) unsigned DEFAULT 0,
  `goPropertyToPlayer` tinyint(1) unsigned DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `accountId` (`accountId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

LOCK TABLES `specials` WRITE;
/*!40000 ALTER TABLE `specials` DISABLE KEYS */;

INSERT INTO `specials` (`id`, `comment`, `accountId`, `givepromo`, `f5`, `setname`, `veh`, `skin`, `gtp`, `mtp`, `aveh`, `mute`, `ajail`, `delveh`, `tp`, `gh`, `kick`, `setvip`, `gm`, `givecar`, `givedonate`, `givemoney`, `giveskill`, `setemail`, `givematerials`, `setmod`, `reset2fa`, `transfervehicle`, `hp`, `rescue`, `ban`, `unban`, `toggleachat`, `esp`, `clear`, `resetbplevel`, `resetbpitems`, `advPartner`, `delitem`, `givecarmod`, `localtime`, `localweather`, `sendCommand`, `giveitem`, `setfamexp`, `setfamrep`, `setfamilyleader`, `giveexp`, `givechips`, `goPropertyToPlayer`)
VALUES
	(1,NULL,17,1,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0),
	(2,NULL,18,1,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0),
	(3,'appolo',33,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,1,0,0,0,0,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,1,0,0),
	(4,'horror',7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0),
	(5,'yaveter',271,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,0,0,0,1,0,1,1,1,0,0,0,0,0,0,0,0,0,0,1,0,0),
	(6,'roy',18919,1,0,1,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,1,1,1,0,0,0,0,0,0,1,1,1,0,0,0,0,0,0,0,0,0,0,1,0,0),
	(7,'shesh',8715,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,0,0,0,0,0,1,1,1,0,0,0,0,0,0,0,0,0,0,1,0,0),
	(8,'baxter',17732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,0,0,0,0,0,1,1,1,0,0,0,0,0,0,0,0,0,0,1,0,0),
	(9,'condagar media',4310,0,1,0,1,1,0,0,1,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0),
	(10,'sadovsky',88,0,1,0,1,1,0,0,1,0,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0),
	(11,'Fostin',8480,0,1,0,1,1,0,0,1,0,0,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0),
	(12,'Olegha',2896,0,1,0,1,1,0,0,1,0,0,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0),
	(13,'Leganor',23,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0),
	(14,'Skewer',29240,1,1,0,1,1,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,0,0,0,0,1,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0),
	(19,'PR kakoyto',14101,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);

/*!40000 ALTER TABLE `specials` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `supply_materials` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `fractionId` smallint(5) unsigned DEFAULT NULL,
  `type` enum('green','red','blue') DEFAULT NULL,
  `position` tinyint(3) unsigned DEFAULT 0,
  `amount` mediumint(8) unsigned DEFAULT 0,
  `rest` mediumint(8) unsigned DEFAULT 0,
  `finishedBy` mediumint(8) DEFAULT NULL,
  `deliveryDate` datetime DEFAULT NULL,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fractionId` (`fractionId`),
  KEY `date` (`date`),
  KEY `type` (`type`),
  KEY `deliveryDate` (`deliveryDate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;


CREATE TABLE `subscriptions` (
  `userId` mediumint(7) NULL,
  `login` varchar(20) DEFAULT NULL,
  `start` datetime NULL,
  `end` datetime NULL,
  `days` smallint(5) NULL DEFAULT NULL,
  `renewalsNumber` smallint(4) NULL DEFAULT NULL,
  `hasActive` tinyint(1) NULL DEFAULT NULL,
  `paymentData` varchar(20) NULL DEFAULT NULL,
  `rebillId` varchar(40) NULL DEFAULT NULL,
  `accountTokenQr` varchar(40) NULL DEFAULT NULL,
  `active` blob NULL,
  UNIQUE KEY `userId` (`userId`),
  UNIQUE KEY `login` (`login`),
  KEY `end` (`end`) USING BTREE,
  KEY `rebillId` (`rebillId`) USING BTREE,
  KEY `accountTokenQr` (`accountTokenQr`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;


CREATE TABLE `tasks` (
  `autoId` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL DEFAULT 0,
  `type` tinyint(2) NOT NULL DEFAULT 0,
  `name` varchar(36) DEFAULT NULL,
  `value` bigint(12) NOT NULL DEFAULT 0,
  UNIQUE KEY `autoId` (`autoId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `tattoo_shops` (
  `id` tinyint(3) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(24) DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `price` int(11) unsigned DEFAULT 5000000,
  `cash` bigint(12) unsigned DEFAULT 0,
  `bank` bigint(12) unsigned DEFAULT 0,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `playerPosX` varchar(10) DEFAULT NULL,
  `playerPosY` varchar(10) DEFAULT NULL,
  `playerPosZ` varchar(10) DEFAULT NULL,
  `playerPosA` varchar(10) DEFAULT NULL,
  `materials` mediumint(7) unsigned DEFAULT 5000,
  `prices` blob DEFAULT NULL,
  `discounts` blob DEFAULT NULL,
  `mafiaId` tinyint(2) unsigned DEFAULT 0,
  `mafiaCash` bigint(12) unsigned DEFAULT 0,
  `defaultMafiaId` tinyint(2) unsigned DEFAULT 0,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `title` (`title`),
  KEY `accountId` (`accountId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`),
  KEY `marketplaceId` (`marketplaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Тату салоны';

LOCK TABLES `tattoo_shops` WRITE;
/*!40000 ALTER TABLE `tattoo_shops` DISABLE KEYS */;

INSERT INTO `tattoo_shops` (`id`, `title`, `accountId`, `price`, `cash`, `bank`, `posX`, `posY`, `posZ`, `playerPosX`, `playerPosY`, `playerPosZ`, `playerPosA`, `materials`, `prices`, `discounts`, `mafiaId`, `mafiaCash`, `defaultMafiaId`, `paidUntil`, `auctionId`, `marketplaceId`)
VALUES
	(1,'Тату салон',NULL,*********,*********,*********,'318.7301','178.7768','103.5809','323.2530','180.231','103.5864','70',75101,NULL,NULL,16,0,19,'2023-03-27 22:41:00',2642,NULL),
	(2,'Тату салон',NULL,*********,*********,********,'1322.7602','-1648.4533','52.1455','1322.0476','-1652.8013','52.2752','305.74',20296,NULL,NULL,18,0,19,'2023-03-12 22:41:00',1486,NULL),
	(3,'Тату салон',NULL,1********,10,4068045,'-1154.3427','-1422.7294','4.7926','-1154.7287','-1426.2296','4.9544','300.89',13697,NULL,NULL,16,0,19,'2023-03-27 23:11:00',2652,NULL),
	(4,'Тату салон',NULL,********,95,70,'-3168.1313','1072.2990','20.8487','-3169.6552','1076.5991','20.8291','158.57',1872,NULL,NULL,16,0,19,'2023-03-27 22:21:00',2645,NULL),
	(5,'Тату салон',NULL,1********,*********,*********,'1859.5184','3746.8579','33.0653','1864.3260','3747.4721','33.0318','25.28',62496,NULL,NULL,15,0,19,'2023-03-09 22:21:01',1113,NULL),
	(6,'Тату салон',NULL,*********,3012980,4214280,'-290.3337','6201.6772','31.4675','-293.6884','6199.8945','31.4874','221.21',9267,NULL,NULL,16,0,19,'2023-03-27 22:10:01',2649,NULL);

/*!40000 ALTER TABLE `tattoo_shops` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `taxes` (
  `category` varchar(20) DEFAULT NULL,
  `tax` varchar(4) DEFAULT '2.0',
  `monthProfit` bigint(20) DEFAULT 0,
  `monthTaxes` bigint(20) DEFAULT 0,
  UNIQUE KEY `category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

LOCK TABLES `taxes` WRITE;
/*!40000 ALTER TABLE `taxes` DISABLE KEYS */;

INSERT INTO `taxes` (`category`, `tax`, `monthProfit`, `monthTaxes`)
VALUES
	('ammo_shops','10.0',8800,880),
	('auto_shops','6.0',37500,2250),
	('barber_shops','2.0',2160,43),
	('carwash_shops','4.0',24850,994),
	('clothes_shops','8.5',2388,203),
	('fuel_stations','7.0',392,27),
	('item_shops','6.5',5000,325),
	('tattoo_shops','10.0',67200,6720),
	('tuning_shops','12.0',815244,97829),
	('atm','2.0',0,0),
	('billboards','2.0',0,0);

/*!40000 ALTER TABLE `taxes` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `team_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `userId` mediumint(7) unsigned DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `event` varchar(10) DEFAULT NULL,
  `param` tinyint(1) unsigned DEFAULT NULL,
  `reason` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `userId` (`userId`),
  KEY `date` (`date`),
  KEY `event` (`event`),
  KEY `param` (`param`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='История участия в команде Majestic (админ, хелпер)';



CREATE TABLE `tickets_logs` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `amount` int(10) unsigned DEFAULT 0,
  `ticketBy` mediumint(7) unsigned DEFAULT NULL,
  `employeeOrg` tinyint(2) unsigned DEFAULT NULL,
  `employeeRank` tinyint(2) unsigned DEFAULT NULL,
  `comment` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `active` tinyint(1) unsigned DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `wantedBy` (`ticketBy`) USING BTREE,
  KEY `amount` (`amount`),
  KEY `date` (`date`),
  KEY `active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci ROW_FORMAT=DYNAMIC COMMENT='Логи штрафов';



CREATE TABLE `timers` (
  `id` varchar(36) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `ms` mediumint(9) DEFAULT NULL,
  `player` mediumint(7) DEFAULT NULL,
  `start` datetime DEFAULT NULL,
  `end` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `name` (`name`),
  KEY `ms` (`ms`),
  KEY `player` (`player`),
  KEY `start` (`start`),
  KEY `end` (`end`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



CREATE TABLE `tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` int(11) DEFAULT NULL,
  `characterId` int(11) DEFAULT NULL,
  `ip` varchar(50) DEFAULT NULL,
  `lastIp` varchar(50) DEFAULT NULL,
  `type` varchar(15) DEFAULT 'website',
  `token` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tokens_token` (`token`),
  KEY `tokens_type_account_id_ip` (`type`,`accountId`,`ip`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;



CREATE TABLE `tuning` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vehicle` mediumint(8) unsigned DEFAULT NULL,
  `modType` tinyint(3) unsigned DEFAULT NULL,
  `modIndex` varchar(7) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `vehicle` (`vehicle`),
  KEY `modType` (`modType`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Детали тюнинга транспорта';



CREATE TABLE `tuning_shops` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(24) DEFAULT NULL,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `price` int(11) unsigned DEFAULT 5000000,
  `cash` bigint(12) unsigned DEFAULT 0,
  `bank` bigint(12) unsigned DEFAULT 0,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `materials` mediumint(7) unsigned DEFAULT 5000,
  `prices` blob DEFAULT NULL,
  `mafiaId` tinyint(2) unsigned DEFAULT 0,
  `mafiaCash` bigint(12) unsigned DEFAULT 0,
  `defaultMafiaId` tinyint(2) unsigned DEFAULT 0,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `accountId` (`accountId`),
  KEY `title` (`title`),
  KEY `paidUntil` (`paidUntil`),
  KEY `auctionId` (`auctionId`),
  KEY `marketplaceId` (`marketplaceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

LOCK TABLES `tuning_shops` WRITE;
/*!40000 ALTER TABLE `tuning_shops` DISABLE KEYS */;

INSERT INTO `tuning_shops` (`id`, `title`, `accountId`, `price`, `cash`, `bank`, `posX`, `posY`, `posZ`, `materials`, `prices`, `mafiaId`, `mafiaCash`, `defaultMafiaId`, `paidUntil`, `auctionId`, `marketplaceId`)
VALUES
	(1,'Los Santos Customs',NULL,2********,********,********,'-1142.6247','-1993.5196','13.1644',62886,NULL,16,0,19,'2024-03-29 20:33:17',2731,NULL),
	(2,'Los Santos Customs',NULL,*********,9831885,52,'722.7023','-1084.0733','22.1912',99360,NULL,16,0,19,'2023-03-26 20:31:00',2470,NULL),
	(3,'Los Santos Customs',NULL,3********,9,76,'-209.1629','-1309.8439','31.2952',53858,NULL,16,0,19,'2024-05-06 18:05:16',2814,NULL),
	(4,'Los Santos Customs',NULL,3********,********,*********,'1178.4713','2646.3388','37.7924',33259,NULL,16,0,19,'2023-03-07 21:31:00',900,NULL),
	(5,'Beeker’s Garage',NULL,2********,********,********,'112.2193','6619.6796','31.8171',84439,NULL,16,0,19,'2023-03-26 20:01:01',2471,NULL),
	(6,'Los Santos Customs',NULL,********0,*********,*********,'-358.1653','-137.4266','39.4306',52278,NULL,16,0,19,'2024-03-24 11:00:13',2727,NULL);

/*!40000 ALTER TABLE `tuning_shops` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `two_factor_codes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `userId` int(7) unsigned DEFAULT NULL,
  `code` varchar(8) DEFAULT NULL,
  `activated` tinyint(1) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `code` (`code`) USING BTREE,
  KEY `accountId` (`userId`) USING BTREE,
  KEY `activated` (`activated`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `unitpay_payments` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `unitpayId` varchar(40) DEFAULT NULL,
  `account` varchar(25) DEFAULT NULL,
  `payAmount` float DEFAULT NULL,
  `sum` float DEFAULT NULL,
  `additional` mediumint(8) unsigned DEFAULT 0,
  `profit` varchar(10) DEFAULT NULL,
  `dateCreate` datetime DEFAULT NULL,
  `dateComplete` datetime DEFAULT NULL,
  `dateReceive` datetime DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `multiplier` tinyint(1) unsigned NOT NULL DEFAULT 2,
  `comment` varchar(50) NOT NULL DEFAULT 'Unitpay',
  `args` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`args`)),
  `source` enum('site','game','seasonPassBronze','seasonPassSilver','seasonPassGold','seasonPassPlatinum','seasonPassPremium','seasonPassKing', 'seasonPassExp', 'starterBronze', 'starterGold', 'starterPlatinum', 'premiumSubscription') DEFAULT 'site',
  `method` varchar(15) DEFAULT NULL,
  `shop` varchar(20) DEFAULT NULL,
  `currency` varchar(4) DEFAULT 'RUB',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unitpayId` (`unitpayId`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `account` (`account`) USING BTREE,
  KEY `dateComplete` (`dateComplete`) USING BTREE,
  KEY `sum` (`sum`) USING BTREE,
  KEY `comment` (`comment`) USING BTREE,
  KEY `source` (`source`),
  KEY `profit` (`profit`),
  KEY `shop` (`shop`),
  KEY `payAmount` (`payAmount`),
  KEY `currency` (`currency`),
  KEY `method` (`method`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `user_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(7) NOT NULL,
  `newValue` varchar(128) DEFAULT NULL,
  `oldValue` varchar(128) DEFAULT NULL,
  `comment` varchar(64) DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userId` (`userId`) USING BTREE,
  KEY `date` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Логи действий аккаунта';



CREATE TABLE `users` (
  `id` mediumint(7) unsigned NOT NULL AUTO_INCREMENT,
  `login` varchar(20) DEFAULT NULL,
  `email` varchar(40) DEFAULT NULL,
  `acceptEmail` tinyint(1) unsigned DEFAULT 1,
  `password` varchar(60) DEFAULT NULL,
  `isConfirm` tinyint(1) unsigned DEFAULT 0,
  `media` tinyint(1) unsigned DEFAULT 0,
  `createdAt` datetime DEFAULT NULL,
  `donate` int(10) unsigned DEFAULT NULL,
  `springCoins` mediumint(6) unsigned DEFAULT 0,
  `halloweenCrystals` mediumint(6) unsigned DEFAULT 0,
  `createdSocialClub` varchar(16) DEFAULT NULL,
  `createdRGSCID` int(10) unsigned DEFAULT NULL,
  `lastSocialClub` varchar(16) DEFAULT NULL,
  `lastRGSCID` int(10) unsigned DEFAULT NULL,
  `createdIP` varchar(45) DEFAULT NULL,
  `lastIP` varchar(45) DEFAULT NULL,
  `createdHWID` varchar(128) DEFAULT NULL,
  `lastHWID` varchar(128) DEFAULT NULL,
  `lastVisit` datetime DEFAULT NULL,
  `invitedBy` mediumint(7) unsigned DEFAULT NULL,
  `inviteCode` varchar(11) DEFAULT NULL,
  `lastAccount` mediumint(7) unsigned DEFAULT NULL,
  `maxAccounts` tinyint(1) unsigned DEFAULT 2,
  `authToken` varchar(200) DEFAULT NULL,
  `online` tinyint(1) unsigned DEFAULT 0,
  `coinsTimer` smallint(3) unsigned DEFAULT 0,
  `rulesAccepted` tinyint(1) unsigned DEFAULT 0,
  `twoFactorSecret` varchar(32) DEFAULT NULL,
  `telegram` bigint(20) DEFAULT NULL,
  `telegramSecret` varchar(6) DEFAULT NULL,
  `registerFrom` enum('game','website','launcher') DEFAULT 'game',
  `registerCode` varchar(32) DEFAULT NULL,
  `registrationPageName` varchar(30) DEFAULT NULL,
  `registrationUtmSource` text DEFAULT NULL,
  `registrationUtmMedium` text DEFAULT NULL,
  `registrationUtmCampaign` text DEFAULT NULL,
  `launcherAuth` tinyint(1) DEFAULT 0,
  `exitData` blob DEFAULT NULL,
  `changePasswordDate` datetime DEFAULT NULL,
  `cooldowns` blob DEFAULT NULL,
  `pixelBattleTime` datetime DEFAULT NULL,
  `pixelBattleCount` smallint(6) DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `email` (`email`) USING BTREE,
  UNIQUE KEY `login` (`login`) USING BTREE,
  KEY `createdAt` (`createdAt`) USING BTREE,
  KEY `donate` (`donate`) USING BTREE,
  KEY `password` (`password`) USING BTREE,
  KEY `createdIP` (`createdIP`) USING BTREE,
  KEY `lastIP` (`lastIP`) USING BTREE,
  KEY `createdHWID` (`createdHWID`) USING BTREE,
  KEY `lastHWID` (`lastHWID`) USING BTREE,
  KEY `lastVisit` (`lastVisit`) USING BTREE,
  KEY `invitedBy` (`invitedBy`) USING BTREE,
  KEY `inviteCode` (`inviteCode`) USING BTREE,
  KEY `createdSocialClub` (`createdSocialClub`) USING BTREE,
  KEY `lastSocialClub` (`lastSocialClub`) USING BTREE,
  KEY `authToken` (`authToken`) USING BTREE,
  KEY `isConfirm` (`isConfirm`) USING BTREE,
  KEY `media` (`media`) USING BTREE,
  KEY `online` (`online`) USING BTREE,
  KEY `acceptEmail` (`acceptEmail`) USING BTREE,
  KEY `rulesAccepted` (`rulesAccepted`) USING BTREE,
  KEY `twoFactorSecret` (`twoFactorSecret`) USING BTREE,
  KEY `createdRGSCID` (`createdRGSCID`),
  KEY `lastRGSCID` (`lastRGSCID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Пользователи (socialClub)';



CREATE TABLE `vehicle_fines` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `vehId` int(11) DEFAULT NULL,
  `vehModel` varchar(50) NOT NULL DEFAULT '',
  `vehNumberplate` varchar(50) NOT NULL DEFAULT '',
  `comment` longtext DEFAULT NULL,
  `amount` int(11) NOT NULL,
  `date` datetime DEFAULT NULL,
  `byPlayer` int(11) DEFAULT NULL,
  `status` int(1) DEFAULT NULL,
  `org` int(11) DEFAULT NULL,
  `url` longtext DEFAULT NULL,
  `byCamera` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='veh fines';



CREATE TABLE `vehicle_sets` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `vehicle` int(10) unsigned DEFAULT NULL,
  `setId` tinyint(3) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `vehicle` (`vehicle`),
  KEY `setId` (`setId`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

LOCK TABLES `vehicle_sets` WRITE;
/*!40000 ALTER TABLE `vehicle_sets` DISABLE KEYS */;

INSERT INTO `vehicle_sets` (`id`, `vehicle`, `setId`)
VALUES
	(1,3166,4),
	(2,15410,4),
	(3,5429,4),
	(4,5429,7),
	(5,24500,11),
	(6,29271,4),
	(7,30771,4),
	(8,62728,4),
	(9,62311,4),
	(10,63938,4),
	(11,72475,4),
	(12,76017,4),
	(13,74577,4),
	(14,76761,4),
	(15,80036,4),
	(16,81877,4),
	(17,80843,4),
	(18,86805,4),
	(19,80857,4),
	(20,80857,7),
	(21,89500,4),
	(22,96454,4),
	(23,99818,10),
	(24,99818,11),
	(25,85443,4),
	(26,107886,4),
	(27,110164,4),
	(28,110483,4),
	(29,112973,4),
	(30,115825,4),
	(31,116918,8),
	(32,119340,4),
	(33,113347,4),
	(34,77926,4),
	(35,122819,9),
	(36,123704,4),
	(37,123589,4),
	(38,128060,4),
	(39,128752,4),
	(40,130728,4),
	(41,130872,4),
	(42,130872,5),
	(43,131492,8),
	(44,136073,4),
	(45,136124,4),
	(46,136179,4),
	(47,123933,4),
	(48,137481,4),
	(49,137481,6),
	(50,86680,5),
	(51,140227,7),
	(52,142596,9),
	(53,140877,4),
	(54,142171,4),
	(55,144768,4),
	(56,123461,5),
	(57,145572,4),
	(58,147358,4),
	(59,147313,4),
	(60,99101,4),
	(61,84548,4),
	(62,145277,4),
	(63,152953,4),
	(64,153695,4),
	(65,155646,4),
	(66,123754,4),
	(67,136338,4),
	(68,158777,4),
	(69,131494,4),
	(70,15931,4),
	(71,163066,4),
	(72,163066,7),
	(73,163661,4),
	(74,154062,4),
	(75,92445,4),
	(76,30432,4),
	(77,152901,4),
	(78,108037,4),
	(79,170975,4),
	(80,172146,4),
	(81,168217,4),
	(82,175423,5),
	(83,145122,4),
	(84,94436,4),
	(85,179683,6),
	(86,123896,5),
	(87,180681,4),
	(88,88717,4),
	(89,181912,4),
	(90,182279,4),
	(91,182963,4),
	(92,183071,7),
	(93,182910,5),
	(94,184140,4),
	(95,185774,6),
	(96,185845,4),
	(97,173250,4),
	(98,162614,5),
	(99,151030,4),
	(100,189384,5),
	(101,189395,4),
	(102,189594,5),
	(103,190497,4),
	(104,192356,4),
	(105,147257,4),
	(106,51376,4),
	(107,191418,4),
	(108,152391,4),
	(109,108787,4),
	(110,197945,5),
	(111,187698,4),
	(112,200232,4),
	(113,202650,6),
	(114,204237,4),
	(115,208860,4),
	(116,209053,3),
	(117,209736,10),
	(118,209441,5),
	(119,156196,4),
	(120,136738,4),
	(121,96057,4),
	(122,209602,4),
	(123,212325,4),
	(124,213464,4),
	(125,145850,4),
	(126,214132,6),
	(127,214169,4),
	(128,214745,4),
	(129,215906,6),
	(130,218770,4),
	(131,219556,4),
	(132,132007,4),
	(133,4797,4),
	(134,226406,9),
	(135,226613,5),
	(136,226762,4),
	(137,138730,4),
	(138,123256,4),
	(139,230821,9),
	(140,207288,4),
	(141,232723,11),
	(142,233012,4),
	(143,233741,4),
	(144,122244,4),
	(145,235321,4),
	(146,236013,8),
	(147,235526,4),
	(148,114892,4),
	(149,238518,4),
	(150,238514,9),
	(151,239832,9),
	(152,241393,4),
	(153,244089,5),
	(154,246261,4),
	(155,247003,4),
	(156,249225,5),
	(157,174467,9),
	(158,249205,4),
	(159,250386,4),
	(160,250842,6),
	(161,251836,4),
	(162,253820,4),
	(163,221077,4),
	(164,218807,4),
	(165,199111,4),
	(166,257681,4),
	(167,258148,9),
	(168,257643,4),
	(169,259404,4),
	(170,212776,4),
	(171,237014,4),
	(172,260413,4),
	(173,260543,4),
	(174,260582,11),
	(175,262251,8),
	(176,119400,3),
	(177,264471,4),
	(178,173123,6),
	(179,66433,4),
	(180,269369,4),
	(181,269552,5),
	(182,188981,4),
	(183,271888,4),
	(184,267377,4),
	(185,277176,4),
	(186,258745,3),
	(187,254488,3),
	(188,279057,4),
	(189,220574,4),
	(190,279161,4),
	(191,279345,9),
	(192,279367,4),
	(193,281139,3),
	(194,281226,4),
	(195,236307,4),
	(196,148204,8),
	(197,115760,4),
	(198,284005,3),
	(199,232843,4),
	(200,287360,8),
	(201,287401,11),
	(202,258675,4),
	(203,227202,4),
	(204,291040,4),
	(205,191145,4),
	(206,294912,4),
	(207,131447,4),
	(208,297326,3),
	(209,297832,4),
	(210,300052,4),
	(211,152783,4),
	(212,233738,4),
	(213,106491,4),
	(214,294104,4),
	(215,236762,4),
	(216,138531,4),
	(217,304378,4),
	(218,71422,4),
	(219,306849,4),
	(220,307480,4),
	(221,309387,4),
	(222,266641,4),
	(223,209635,4),
	(224,310772,6),
	(225,138980,4),
	(226,137598,4),
	(227,312806,4),
	(228,313303,6),
	(229,306773,4),
	(230,313866,5),
	(231,301153,4),
	(232,145805,4),
	(233,124876,4),
	(234,319764,6),
	(235,320178,4),
	(236,320593,6),
	(237,321211,4),
	(238,224392,4),
	(239,323558,5),
	(240,325468,5),
	(241,326318,4),
	(242,329453,4),
	(243,239246,4),
	(244,333039,4),
	(245,333101,10),
	(246,336138,4),
	(247,337174,3),
	(248,300582,4),
	(249,148484,4),
	(250,342232,9),
	(251,342261,4),
	(252,169849,10),
	(253,345664,4),
	(254,346366,11),
	(255,308108,5),
	(256,263229,5),
	(257,120577,4),
	(258,209297,5),
	(259,349853,6),
	(260,91550,4),
	(261,351829,5),
	(262,352548,5),
	(263,353703,4),
	(264,354601,4),
	(265,145546,4),
	(266,355095,4),
	(267,154049,4),
	(268,131276,4),
	(269,336214,4),
	(270,220502,5),
	(271,358739,6),
	(272,359097,3),
	(273,279902,4),
	(274,355154,5),
	(275,322518,4),
	(276,367739,4),
	(277,111986,4),
	(278,369148,10),
	(279,73284,4),
	(280,205972,4),
	(281,371293,3),
	(282,361059,6),
	(283,371927,4),
	(284,372452,4),
	(285,372587,4),
	(286,126432,7),
	(287,265571,4),
	(288,373585,4),
	(289,343773,4),
	(290,378040,4),
	(291,378689,4),
	(292,182332,6),
	(293,370464,8),
	(294,370464,9),
	(295,258742,4),
	(296,338738,4),
	(297,127979,5),
	(298,136411,4),
	(299,367644,4),
	(300,388913,5),
	(301,114812,4),
	(302,366553,4),
	(303,389252,3),
	(304,166093,5),
	(305,392082,6),
	(306,392471,4),
	(307,132722,6),
	(308,393304,5),
	(309,393489,5),
	(310,380083,4),
	(311,395176,7),
	(312,254709,4),
	(313,397403,9),
	(314,344470,4),
	(315,381007,4),
	(316,397650,10),
	(317,297014,9),
	(318,220303,7),
	(319,398084,4),
	(320,398085,7),
	(321,398969,5),
	(322,400536,10),
	(323,400555,4),
	(324,370998,4),
	(325,400944,6),
	(326,132002,9),
	(327,133583,4),
	(328,402687,4),
	(329,403017,5),
	(330,404810,7),
	(331,279702,10),
	(332,406183,9),
	(333,91131,4),
	(334,406689,4),
	(335,387204,10),
	(336,85931,4),
	(337,115653,4),
	(338,408978,4),
	(339,409205,4),
	(340,411562,4),
	(341,268721,6),
	(342,391363,3),
	(343,412653,4),
	(344,413417,9),
	(345,413450,9),
	(346,329296,4),
	(347,329295,4),
	(348,414893,11),
	(349,317216,4),
	(350,318162,10),
	(351,79529,4),
	(352,419115,9),
	(353,344251,4),
	(354,419477,2),
	(355,419478,2),
	(356,419479,5),
	(357,419483,1),
	(358,419483,2),
	(359,419543,2),
	(360,419490,9),
	(361,419853,5);

/*!40000 ALTER TABLE `vehicle_sets` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `vehicles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `owner` mediumint(8) unsigned DEFAULT NULL,
  `model` varchar(24) DEFAULT NULL,
  `number` varchar(50) DEFAULT NULL,
  `posX` varchar(10) DEFAULT NULL,
  `posY` varchar(10) DEFAULT NULL,
  `posZ` varchar(10) DEFAULT NULL,
  `rotX` varchar(10) DEFAULT NULL,
  `rotY` varchar(10) DEFAULT NULL,
  `rotZ` varchar(10) DEFAULT NULL,
  `park` int(10) unsigned DEFAULT NULL,
  `seasonPassId` tinyint(3) unsigned DEFAULT NULL,
  `locked` tinyint(1) unsigned NOT NULL DEFAULT 0,
  `gas` mediumint(8) unsigned NOT NULL DEFAULT 10000,
  `mileage` bigint(12) unsigned NOT NULL DEFAULT 0,
  `dirt` smallint(3) unsigned NOT NULL DEFAULT 0,
  `color1` tinyint(3) unsigned NOT NULL DEFAULT 0,
  `color2` tinyint(3) unsigned NOT NULL DEFAULT 0,
  `color3` tinyint(3) unsigned NOT NULL DEFAULT 0,
  `health` smallint(4) unsigned NOT NULL DEFAULT 1000,
  `brokenLock` tinyint(1) unsigned NOT NULL DEFAULT 0,
  `wearEngine` mediumint(6) unsigned NOT NULL DEFAULT 0,
  `wearSuspension` mediumint(6) unsigned NOT NULL DEFAULT 0,
  `wearCooling` mediumint(6) unsigned NOT NULL DEFAULT 0,
  `wearBrakes` mediumint(6) unsigned NOT NULL DEFAULT 0,
  `wearTransmission` mediumint(6) unsigned NOT NULL DEFAULT 0,
  `wearFuel` mediumint(6) unsigned NOT NULL DEFAULT 0,
  `wearExhaust` mediumint(6) unsigned NOT NULL DEFAULT 0,
  `wearWheels` mediumint(6) unsigned NOT NULL DEFAULT 0,
  `wearBattery` mediumint(6) unsigned NOT NULL DEFAULT 0,
  `wearOil` mediumint(6) unsigned NOT NULL DEFAULT 0,
  `endTime` datetime DEFAULT NULL,
  `date` datetime DEFAULT NULL,
  `rentPlaceId` tinyint(2) unsigned DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  `marketplaceId` int(10) unsigned DEFAULT NULL,
  `familyId` int(10) unsigned DEFAULT NULL,
  `rentBy` mediumint(7) unsigned DEFAULT NULL,
  `rentUntil` datetime DEFAULT NULL,
  `impound` blob DEFAULT NULL,
  `familyCarriage` tinyint(1) unsigned DEFAULT NULL,
  `registerItemId` int(11) DEFAULT NULL,
  `startPrice` int(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `owner` (`owner`,`model`,`number`,`park`),
  KEY `date` (`date`),
  KEY `endTime` (`endTime`),
  KEY `auctionId` (`auctionId`),
  KEY `familyId` (`familyId`),
  KEY `rentBy` (`rentBy`),
  KEY `rentUntil` (`rentUntil`),
  KEY `seasonPassId` (`seasonPassId`),
  KEY `marketplaceId` (`marketplaceId`),
  KEY `registerItemId` (`registerItemId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Транспорт';



CREATE TABLE `walkietalkie` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(8) unsigned DEFAULT NULL,
  `enabled` tinyint(1) unsigned DEFAULT 0,
  `frequency` mediumint(6) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Информация о рации игроков';



CREATE TABLE `wanted_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `accountId` mediumint(7) unsigned DEFAULT NULL,
  `level` tinyint(1) unsigned DEFAULT NULL,
  `wantedBy` mediumint(7) unsigned DEFAULT NULL,
  `employeeOrg` tinyint(2) unsigned DEFAULT NULL,
  `employeeRank` tinyint(2) unsigned DEFAULT NULL,
  `comment` varchar(50) DEFAULT NULL,
  `startAt` datetime DEFAULT NULL,
  `endAt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `level` (`level`),
  KEY `endAt` (`endAt`),
  KEY `startAt` (`startAt`),
  KEY `wantedBy` (`wantedBy`),
  KEY `accountId` (`accountId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `warehouses` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `buildingId` tinyint(3) unsigned NOT NULL DEFAULT 0,
  `familyId` smallint(5) unsigned DEFAULT NULL,
  `paidUntil` datetime DEFAULT NULL,
  `auctionId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `familyId` (`familyId`),
  KEY `paidUntil` (`paidUntil`),
  KEY `buildingId` (`buildingId`),
  KEY `auctionId` (`auctionId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Семейные склады';

LOCK TABLES `warehouses` WRITE;
/*!40000 ALTER TABLE `warehouses` DISABLE KEYS */;

INSERT INTO `warehouses` (`id`, `buildingId`, `familyId`, `paidUntil`, `auctionId`)
VALUES
	(1,1,NULL,'2024-04-04 10:23:17',2733),
	(2,1,NULL,'2024-03-16 15:15:43',2724),
	(3,1,NULL,NULL,NULL),
	(4,1,NULL,NULL,NULL),
	(5,1,NULL,NULL,NULL),
	(6,1,NULL,NULL,NULL),
	(7,1,NULL,NULL,NULL),
	(8,1,NULL,NULL,NULL),
	(9,1,NULL,NULL,NULL),
	(10,1,NULL,NULL,NULL),
	(11,1,NULL,NULL,NULL),
	(12,1,NULL,NULL,NULL),
	(13,1,NULL,NULL,NULL),
	(14,1,NULL,NULL,NULL),
	(15,1,NULL,NULL,NULL),
	(16,1,NULL,NULL,NULL),
	(17,1,NULL,NULL,NULL),
	(18,1,NULL,NULL,NULL),
	(19,1,NULL,NULL,NULL),
	(20,1,NULL,NULL,NULL),
	(21,2,NULL,NULL,NULL),
	(22,2,NULL,NULL,NULL),
	(23,2,NULL,NULL,NULL),
	(24,2,NULL,NULL,NULL),
	(25,2,NULL,NULL,NULL),
	(26,2,NULL,NULL,NULL),
	(27,2,NULL,NULL,NULL),
	(28,2,NULL,NULL,NULL),
	(29,2,NULL,NULL,NULL),
	(30,2,NULL,NULL,NULL),
	(31,2,NULL,NULL,NULL),
	(32,2,NULL,NULL,NULL),
	(33,2,NULL,NULL,NULL),
	(34,2,NULL,NULL,NULL),
	(35,2,NULL,NULL,NULL),
	(36,2,NULL,NULL,NULL),
	(37,2,NULL,NULL,NULL),
	(38,2,NULL,NULL,NULL),
	(39,2,NULL,NULL,NULL),
	(40,2,NULL,NULL,NULL),
	(41,3,NULL,'2023-07-21 02:07:59',2688),
	(42,3,NULL,'2023-07-21 12:46:11',2690),
	(43,3,NULL,'2023-07-22 12:09:28',2697),
	(44,3,NULL,NULL,NULL),
	(45,3,NULL,NULL,NULL),
	(46,3,NULL,NULL,NULL),
	(47,3,NULL,NULL,NULL),
	(48,3,NULL,NULL,NULL),
	(49,3,NULL,NULL,NULL),
	(50,3,NULL,NULL,NULL),
	(51,3,NULL,NULL,NULL),
	(52,3,NULL,NULL,NULL),
	(53,3,NULL,NULL,NULL),
	(54,3,NULL,NULL,NULL),
	(55,3,NULL,NULL,NULL),
	(56,3,NULL,NULL,NULL),
	(57,3,NULL,NULL,NULL),
	(58,3,NULL,NULL,NULL),
	(59,3,NULL,NULL,NULL),
	(60,3,NULL,NULL,NULL),
	(61,4,NULL,NULL,NULL),
	(62,4,NULL,NULL,NULL),
	(63,4,NULL,NULL,NULL),
	(64,4,NULL,NULL,NULL),
	(65,4,NULL,NULL,NULL),
	(66,4,NULL,NULL,NULL),
	(67,4,NULL,NULL,NULL),
	(68,4,NULL,NULL,NULL),
	(69,4,NULL,NULL,NULL),
	(70,4,NULL,NULL,NULL),
	(71,4,NULL,NULL,NULL),
	(72,4,NULL,NULL,NULL),
	(73,4,NULL,NULL,NULL),
	(74,4,NULL,NULL,NULL),
	(75,4,NULL,NULL,NULL),
	(76,4,NULL,NULL,NULL),
	(77,4,NULL,NULL,NULL),
	(78,4,NULL,NULL,NULL),
	(79,4,NULL,NULL,NULL),
	(80,4,NULL,NULL,NULL),
	(81,5,NULL,NULL,NULL),
	(82,5,NULL,NULL,NULL),
	(83,5,NULL,NULL,NULL),
	(84,5,NULL,NULL,NULL),
	(85,5,NULL,NULL,NULL),
	(86,5,NULL,NULL,NULL),
	(87,5,NULL,NULL,NULL),
	(88,5,NULL,NULL,NULL),
	(89,5,NULL,NULL,NULL),
	(90,5,NULL,NULL,NULL),
	(91,5,NULL,NULL,NULL),
	(92,5,NULL,NULL,NULL),
	(93,5,NULL,NULL,NULL),
	(94,5,NULL,NULL,NULL),
	(95,5,NULL,NULL,NULL),
	(96,5,NULL,NULL,NULL),
	(97,5,NULL,NULL,NULL),
	(98,5,NULL,NULL,NULL),
	(99,5,NULL,NULL,NULL),
	(100,5,NULL,NULL,NULL),
	(101,6,NULL,'2023-07-21 23:32:54',NULL),
	(102,6,NULL,NULL,NULL),
	(103,6,NULL,NULL,NULL),
	(104,6,NULL,NULL,NULL),
	(105,6,NULL,NULL,NULL),
	(106,6,NULL,NULL,NULL),
	(107,6,NULL,NULL,NULL),
	(108,6,NULL,NULL,NULL),
	(109,6,NULL,NULL,NULL),
	(110,6,NULL,NULL,NULL),
	(111,6,NULL,NULL,NULL),
	(112,6,NULL,NULL,NULL),
	(113,6,NULL,NULL,NULL),
	(114,6,NULL,NULL,NULL),
	(115,6,NULL,NULL,NULL),
	(116,6,NULL,NULL,NULL),
	(117,6,NULL,NULL,NULL),
	(118,6,NULL,NULL,NULL),
	(119,6,NULL,NULL,NULL),
	(120,6,NULL,NULL,NULL),
	(121,7,NULL,'2023-07-21 23:31:26',NULL),
	(122,7,NULL,NULL,NULL),
	(123,7,NULL,NULL,NULL),
	(124,7,NULL,NULL,NULL),
	(125,7,NULL,NULL,NULL),
	(126,7,NULL,NULL,NULL),
	(127,7,NULL,NULL,NULL),
	(128,7,NULL,NULL,NULL),
	(129,7,NULL,NULL,NULL),
	(130,7,NULL,NULL,NULL),
	(131,7,NULL,NULL,NULL),
	(132,7,NULL,NULL,NULL),
	(133,7,NULL,NULL,NULL),
	(134,7,NULL,NULL,NULL),
	(135,7,NULL,NULL,NULL),
	(136,7,NULL,'2023-07-21 22:58:04',NULL),
	(137,7,NULL,NULL,NULL),
	(138,7,NULL,NULL,NULL),
	(139,7,NULL,NULL,NULL),
	(140,7,NULL,NULL,NULL),
	(141,8,NULL,NULL,NULL),
	(142,8,NULL,NULL,NULL),
	(143,8,NULL,NULL,NULL),
	(144,8,NULL,NULL,NULL),
	(145,8,NULL,NULL,NULL),
	(146,8,NULL,NULL,NULL),
	(147,8,NULL,NULL,NULL),
	(148,8,NULL,NULL,NULL),
	(149,8,NULL,NULL,NULL),
	(150,8,NULL,'2023-07-21 00:44:38',2673),
	(151,8,NULL,NULL,NULL),
	(152,8,NULL,NULL,NULL),
	(153,8,NULL,NULL,NULL),
	(154,8,NULL,NULL,NULL),
	(155,8,NULL,NULL,NULL),
	(156,8,NULL,NULL,NULL),
	(157,8,NULL,NULL,NULL),
	(158,8,NULL,NULL,NULL),
	(159,8,NULL,NULL,NULL),
	(160,8,NULL,NULL,NULL),
	(161,9,NULL,NULL,NULL),
	(162,9,NULL,NULL,NULL),
	(163,9,NULL,NULL,NULL),
	(164,9,NULL,NULL,NULL),
	(165,9,NULL,NULL,NULL),
	(166,9,NULL,NULL,NULL),
	(167,9,NULL,NULL,NULL),
	(168,9,NULL,NULL,NULL),
	(169,9,NULL,NULL,NULL),
	(170,9,NULL,NULL,NULL),
	(171,9,NULL,NULL,NULL),
	(172,9,NULL,NULL,NULL),
	(173,9,NULL,NULL,NULL),
	(174,9,NULL,NULL,NULL),
	(175,9,NULL,NULL,NULL),
	(176,9,NULL,NULL,NULL),
	(177,9,NULL,NULL,NULL),
	(178,9,NULL,NULL,NULL),
	(179,9,NULL,NULL,NULL),
	(180,9,NULL,NULL,NULL);

/*!40000 ALTER TABLE `warehouses` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `wheel_bets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(11) NOT NULL,
  `accountId` int(11) NOT NULL,
  `amount` int(11) NOT NULL,
  `color` varchar(18) NOT NULL,
  `gameId` int(11) NOT NULL,
  `dateBet` datetime NOT NULL DEFAULT current_timestamp(),
  `socialClub` varchar(16) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userId` (`userId`),
  KEY `gameId` (`gameId`),
  KEY `dateBet` (`dateBet`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `wheel_games` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playersCount` int(11) NOT NULL,
  `white` int(11) NOT NULL,
  `red` int(11) NOT NULL,
  `green` int(11) NOT NULL,
  `yellow` int(11) NOT NULL,
  `bank` int(11) NOT NULL,
  `colorWin` varchar(18) NOT NULL,
  `won` int(11) NOT NULL,
  `lost` int(11) NOT NULL,
  `dateGame` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`) USING BTREE,
  KEY `dateGame` (`dateGame`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `whitelist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `socialClub` varchar(16) NOT NULL DEFAULT '',
  `comment` varchar(50) DEFAULT NULL,
  `donater` tinyint(1) unsigned DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `socialClub` (`socialClub`),
  KEY `donater` (`donater`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `world_cameras` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `zone` varchar(50) DEFAULT NULL,
  `destroyed` tinyint(1) DEFAULT NULL,
  `finesAmount` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `zone` (`zone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

LOCK TABLES `world_cameras` WRITE;
/*!40000 ALTER TABLE `world_cameras` DISABLE KEYS */;

INSERT INTO `world_cameras` (`id`, `zone`, `destroyed`, `finesAmount`)
VALUES
	(2,'group_2',0,0),
	(3,'group_4',0,0),
	(4,'group_5',0,0),
	(5,'group_12',0,0),
	(6,'group_14',0,0),
	(7,'group_15',0,0),
	(8,'group_17',0,0),
	(9,'group_19',0,0),
	(10,'group_22',0,0),
	(11,'group_23',0,0),
	(12,'group_24',0,0),
	(13,'group_25',0,0),
	(14,'group_28',0,0),
	(15,'group_32',0,0),
	(16,'group_34',0,0),
	(17,'group_38',0,0),
	(18,'group_39',0,0),
	(19,'group_40',0,0),
	(20,'group_42',0,0),
	(21,'group_43',0,375),
	(22,'group_1',1,0),
	(23,'group_3',1,0),
	(24,'group_6',1,0),
	(25,'group_7',0,0),
	(26,'group_8',0,0),
	(27,'group_9',1,0),
	(28,'group_10',1,0),
	(29,'group_11',1,0),
	(30,'group_13',1,0),
	(31,'group_16',1,0),
	(32,'group_18',1,0),
	(33,'group_20',1,0),
	(34,'group_21',1,0),
	(35,'group_26',1,0),
	(36,'group_27',0,0),
	(37,'group_29',1,0),
	(38,'group_30',1,0),
	(39,'group_31',1,0),
	(40,'group_33',1,0),
	(41,'group_35',1,0),
	(42,'group_36',1,0),
	(43,'group_37',1,0),
	(44,'group_41',1,0),
	(45,'group_44',1,0);

/*!40000 ALTER TABLE `world_cameras` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `world_quests` (
  `id` smallint(6) unsigned NOT NULL AUTO_INCREMENT,
  `questId` smallint(6) DEFAULT NULL,
  `progress` int(10) unsigned DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `questId` (`questId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Мировые квесты, где игроки всесте их выполняют чтобы выполнить его';

LOCK TABLES `world_quests` WRITE;
/*!40000 ALTER TABLE `world_quests` DISABLE KEYS */;

INSERT INTO `world_quests` (`id`, `questId`, `progress`)
VALUES
	(1,6,0),
	(2,7,0),
	(3,3,0);

/*!40000 ALTER TABLE `world_quests` ENABLE KEYS */;
UNLOCK TABLES;


CREATE TABLE `world_quests_players` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` int(11) DEFAULT NULL,
  `worldQuestId` mediumint(9) DEFAULT NULL,
  `progress` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `accountId` (`accountId`) USING BTREE,
  KEY `worldQuestId` (`worldQuestId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;



CREATE TABLE `xmas_claimed_presents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userId` int(10) unsigned NOT NULL,
  `presentId` tinyint(3) unsigned NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `userId` (`userId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci COMMENT='Подарки которые игрок забрал';




/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
