generator client {
  provider        = "prisma-client-js"
  output          = "./prisma-client-game/src/generated"
  previewFeatures = ["metrics"]
}

generator local {
  provider        = "prisma-client-js"
  output          = "../../../../prisma-client-game/src/generated"
  previewFeatures = ["metrics"]
}

datasource db {
  provider = "mysql"
  url      = "mysql://root@database:3306/majestic"
}

model change_clothes {
  id          Int  @id @default(autoincrement())
  isProp      Int? @default(0) @db.TinyInt
  gender      Int? @default(0) @db.TinyInt
  component   Int? @default(0) @db.TinyInt
  oldDrawable Int? @default(0) @db.SmallInt
  oldTexture  Int? @default(-1) @db.TinyInt
  newDrawable Int? @default(0) @db.SmallInt
  newTexture  Int? @default(-1) @db.TinyInt

  @@map("_change_clothes")
}

// autogenerated (primsa db pull)
//

// checked
model User {
  id                      Int                 @id @default(autoincrement()) @db.UnsignedMediumInt
  login                   String?             @unique(map: "login") @db.VarChar(20)
  email                   String?             @unique(map: "email") @db.VarChar(40)
  acceptEmail             Boolean?            @default(true)
  password                String?             @db.VarChar(60)
  isConfirm               Boolean?            @default(false)
  media                   Boolean?            @default(false)
  createdAt               DateTime?           @db.Date
  donate                  Int?                @default(0) @db.UnsignedMediumInt
  halloweenCrystals       Int?                @default(0) @db.UnsignedMediumInt
  createdSocialClub       String?             @db.VarChar(16)
  createdRGSCID           Int?                @db.UnsignedInt
  lastSocialClub          String?             @db.VarChar(16)
  lastRGSCID              Int?                @db.UnsignedInt
  createdIP               String?             @db.VarChar(45)
  lastIP                  String?             @db.VarChar(16)
  createdHWID             String?             @db.VarChar(128)
  lastHWID                String?             @db.VarChar(128)
  lastVisit               DateTime?           @db.DateTime(0)
  invitedBy               Int?                @db.UnsignedMediumInt
  inviteCode              String?             @db.VarChar(11)
  lastAccount             Int?                @db.UnsignedMediumInt
  maxAccounts             Int?                @default(2) @db.UnsignedTinyInt
  authToken               String?             @db.VarChar(200)
  online                  Boolean?            @default(false)
  coinsTimer              Int?                @default(0) @db.UnsignedSmallInt
  rulesAccepted           Boolean?            @default(false)
  twoFactorSecret         String?             @db.VarChar(32)
  telegram                BigInt?
  telegramSecret          String?             @db.VarChar(6)
  registerFrom            users_registerFrom? @default(game)
  registerCode            String?             @db.VarChar(32)
  registrationPageName    String?             @db.VarChar(30)
  registrationUtmSource   String?             @db.Text
  registrationUtmMedium   String?             @db.Text
  registrationUtmCampaign String?             @db.Text
  launcherAuth            Boolean?            @default(false)
  cooldowns               Bytes?              @db.Blob
  pixelBattleTime         DateTime?           @db.DateTime(0)
  pixelBattleCount        Int?                @default(0) @db.SmallInt
  exitData                Bytes?              @db.Blob

  @@index([acceptEmail], map: "acceptEmail")
  @@index([authToken], map: "authToken")
  @@index([createdAt], map: "createdAt")
  @@index([createdHWID], map: "createdHWID")
  @@index([createdIP], map: "createdIP")
  @@index([createdRGSCID], map: "createdRGSCID")
  @@index([createdSocialClub], map: "createdSocialClub")
  @@index([donate], map: "donate")
  @@index([inviteCode], map: "inviteCode")
  @@index([invitedBy], map: "invitedBy")
  @@index([isConfirm], map: "isConfirm")
  @@index([lastHWID], map: "lastHWID")
  @@index([lastIP], map: "lastIP")
  @@index([lastRGSCID], map: "lastRGSCID")
  @@index([lastSocialClub], map: "lastSocialClub")
  @@index([lastVisit], map: "lastVisit")
  @@index([media], map: "media")
  @@index([online], map: "online")
  @@index([password], map: "password")
  @@index([rulesAccepted], map: "rulesAccepted")
  @@index([twoFactorSecret], map: "twoFactorSecret")
  @@map("users")
}

model Subscriptions {
  userId         Int       @unique(map: "userId") @db.MediumInt
  login          String?   @unique(map: "login") @db.VarChar(20)
  start          DateTime? @db.Date
  end            DateTime? @db.Date
  days           Int?      @db.UnsignedSmallInt
  renewalsNumber Int?      @db.UnsignedSmallInt
  hasActive      Int?      @db.TinyInt
  paymentData    String?   @db.VarChar(20)
  rebillId       String?   @db.VarChar(40)
  accountTokenQr String?   @db.VarChar(40)
  active         Bytes?    @db.Blob
  paymentType    String?   @db.VarChar(20)

  @@index([end], map: "end")
  @@index([rebillId], map: "rebillId")
  @@index([accountTokenQr], map: "accountTokenQr")
  @@map("subscriptions")
}

// checked
model AdminLog {
  id      Int       @id @default(autoincrement()) @db.UnsignedInt
  admin   Int?      @db.UnsignedMediumInt
  type    String?   @db.VarChar(20)
  static  Int?      @db.UnsignedMediumInt
  amount  Int?
  comment String?   @db.Text
  args    String?   @db.LongText
  date    DateTime? @db.DateTime(0)

  @@index([admin], map: "admin")
  @@index([date], map: "date")
  @@index([static], map: "static")
  @@index([type], map: "type")
  @@map("admin_log")
}

model ammo_shops {
  id             Int       @id @default(autoincrement()) @db.UnsignedTinyInt
  title          String?   @db.VarChar(24)
  accountId      Int?      @db.UnsignedMediumInt
  price          Int?      @default(5000000) @db.UnsignedInt
  cash           BigInt?   @default(0) @db.UnsignedBigInt
  bank           BigInt?   @default(0) @db.UnsignedBigInt
  posX           String?   @db.VarChar(10)
  posY           String?   @db.VarChar(10)
  posZ           String?   @db.VarChar(10)
  pickupX        String?   @db.VarChar(10)
  pickupY        String?   @db.VarChar(10)
  pickupZ        String?   @db.VarChar(10)
  warehouse      Bytes?    @db.Blob
  prices         Bytes?    @db.Blob
  discounts      Bytes?    @db.Blob
  mafiaId        Int?      @default(0) @db.UnsignedTinyInt
  mafiaCash      BigInt?   @default(0) @db.UnsignedBigInt
  defaultMafiaId Int?      @default(0) @db.UnsignedTinyInt
  paidUntil      DateTime? @db.DateTime(0)
  auctionId      Int?      @db.UnsignedInt
  marketplaceId  Int?      @db.UnsignedInt

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([paidUntil], map: "paidUntil")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model animations {
  id          Int             @id @default(autoincrement()) @db.UnsignedInt
  accountId   Int             @db.UnsignedMediumInt
  type        animations_type @default(favourite)
  animPath    String?         @db.VarChar(50)
  animIndex   Int?            @db.UnsignedSmallInt
  soundPath   String?         @db.VarChar(50)
  circleIndex Int?            @db.UnsignedTinyInt

  @@index([accountId], map: "accountId")
  @@index([circleIndex], map: "circleIndex")
  @@index([type], map: "type")
}

model animations_purchased {
  id          Int       @id @default(autoincrement())
  accountId   Int       @db.UnsignedInt
  userId      Int?
  animationId Int       @db.UnsignedSmallInt
  date        DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([userId], map: "userId")
}

model anticheat {
  id         Int     @id @default(autoincrement()) @db.UnsignedInt
  userId     Int?    @db.UnsignedMediumInt
  socialClub String? @db.VarChar(16)
  cheatCode  Int?    @db.UnsignedInt

  @@index([socialClub], map: "socialClub")
  @@index([userId], map: "userId")
}

model anticheat_log {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  hwid       String?   @db.VarChar(128)
  socialClub String?   @db.VarChar(16)
  code       Int?      @db.UnsignedSmallInt
  cheatPath  String?   @db.VarChar(512)
  date       DateTime? @db.DateTime(0)

  @@index([code], map: "code")
  @@index([date], map: "date")
  @@index([hwid], map: "hwid")
  @@index([socialClub], map: "socialClub")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model apartments {
  id            Int       @id @default(autoincrement()) @db.UnsignedSmallInt
  accountId     Int?      @db.UnsignedMediumInt
  buildingId    Int       @default(1) @db.UnsignedSmallInt
  price         Int?      @default(1000000) @db.UnsignedInt
  interior      Boolean?  @default(true)
  garage        Boolean?  @default(false)
  cash          BigInt?   @default(0) @db.UnsignedBigInt
  rentPrice     Int?      @default(0) @db.UnsignedInt
  maxRent       Boolean?  @default(true)
  locked        Boolean?  @default(false)
  paidUntil     DateTime? @default(dbgenerated("('2020-05-01 00:00:00')")) @db.DateTime(0)
  auctionId     Int?      @db.UnsignedInt
  marketplaceId Int?      @db.UnsignedInt

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([paidUntil], map: "paidUntil")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model arena_history {
  id        Int      @id @default(autoincrement())
  staticId  Int?
  sessionId String?  @db.VarChar(12)
  lobbyId   String?  @db.VarChar(12)
  action    String?  @db.VarChar(45)
  args      Bytes?   @db.Blob
  date      DateTime @db.DateTime(0)

  @@index([lobbyId], map: "lobbyId")
  @@index([sessionId], map: "sessionId")
  @@index([staticId], map: "staticId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model arena_matches_log {
  id         Int       @id @default(autoincrement())
  sessionId  String?   @db.VarChar(12)
  lobbyId    String?   @db.VarChar(12)
  creator    Int?
  leader     Int?
  gamemode   String?   @db.VarChar(45)
  bank       BigInt?
  status     String?   @db.VarChar(20)
  createDate DateTime? @db.DateTime(0)
  startDate  DateTime? @db.DateTime(0)
  endDate    DateTime? @db.DateTime(0)
  lobbyData  Bytes?    @db.Blob
  gameData   Bytes?    @db.Blob

  @@index([creator], map: "creator")
  @@index([lobbyId], map: "lobbyId")
  @@index([sessionId], map: "sessionId")
}

model arrest_log {
  id               Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId        Int?      @db.UnsignedMediumInt
  time             Int?      @db.UnsignedMediumInt
  wantedLevel      Boolean?
  arrestBy         Int?      @db.UnsignedMediumInt
  employeeOrg      Int?      @db.UnsignedTinyInt
  employeeRank     Int?      @db.UnsignedInt
  intruderOrg      Int?      @db.UnsignedTinyInt
  intruderFamily   Int?      @db.UnsignedMediumInt
  comment          String?   @db.VarChar(50)
  isCriminalRecord Boolean?  @default(false)
  pledge           Int?      @default(0)
  date             DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([arrestBy], map: "arrestBy")
  @@index([date], map: "date")
  @@index([isCriminalRecord], map: "isCriminalRecord")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model atm {
  id             Int       @id @default(autoincrement()) @db.UnsignedSmallInt
  accountId      Int?      @db.UnsignedMediumInt
  price          Int?      @default(*********) @db.UnsignedInt
  profit         BigInt    @default(0) @db.UnsignedBigInt
  condition      Int       @default(0) @db.UnsignedSmallInt
  cashInPercent  String    @default("1.0") @db.VarChar(5)
  cashOutPercent String    @default("1.0") @db.VarChar(5)
  mobilePercent  String    @default("1.0") @db.VarChar(5)
  materials      Int       @default(1000) @db.UnsignedSmallInt
  cash           BigInt    @default(2500000) @db.UnsignedBigInt
  paidUntil      DateTime? @db.DateTime(0)
  auctionId      Int?      @db.UnsignedInt
  marketplaceId  Int?      @db.UnsignedInt

  @@index([auctionId], map: "auctionId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([paidUntil], map: "paidUntil")
  @@index([accountId], map: "userId")
}

// checked
model AttackOnTheArmy {
  id            Int    @db.TinyInt
  isFortZancudo Int    @db.TinyInt
  rest          Int    @default(0) @db.SmallInt
  maxMats       Int    @default(0) @db.SmallInt
  typeColor     String @default("") @db.VarChar(8)

  @@unique(fields: [id, isFortZancudo], name: "attackOnTheArmyIdentifier")
  @@map("attackonthearmy")
}

// checked
model AttackOnTheArmyResetTime {
  hours Bytes @unique(length: 100) @db.Blob

  @@map("attackonthearmy_resettime")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model AttackOnTheArmyTime {
  isFortZancudo Int    @db.TinyInt
  days          String @db.VarChar(16)
  time          String @db.VarChar(65)

  @@unique([isFortZancudo])
  @@map("attackonthearmytime")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model auto_shops {
  id             Int       @id @default(autoincrement()) @db.UnsignedTinyInt
  title          String?   @db.VarChar(50)
  accountId      Int?      @db.UnsignedMediumInt
  price          Int?      @default(5000000) @db.UnsignedInt
  cash           BigInt?   @default(0) @db.UnsignedBigInt
  bank           BigInt?   @default(0) @db.UnsignedBigInt
  posX           String?   @db.VarChar(10)
  posY           String?   @db.VarChar(10)
  posZ           String?   @db.VarChar(10)
  pickupX        String?   @db.VarChar(10)
  pickupY        String?   @db.VarChar(10)
  pickupZ        String?   @db.VarChar(10)
  camPosX        String?   @db.VarChar(10)
  camPosY        String?   @db.VarChar(10)
  camPosZ        String?   @db.VarChar(10)
  vehPosX        String?   @db.VarChar(10)
  vehPosY        String?   @db.VarChar(10)
  vehPosZ        String?   @db.VarChar(10)
  exitPosX       String?   @db.VarChar(10)
  exitPosY       String?   @db.VarChar(10)
  exitPosZ       String?   @db.VarChar(10)
  warehouse      Bytes?    @db.Blob
  prices         Bytes?    @db.Blob
  mafiaId        Int?      @default(0) @db.UnsignedTinyInt
  mafiaCash      BigInt?   @default(0) @db.UnsignedBigInt
  defaultMafiaId Int?      @default(0) @db.UnsignedTinyInt
  paidUntil      DateTime? @db.DateTime(0)
  auctionId      Int?      @db.UnsignedInt
  marketplaceId  Int?      @db.UnsignedInt

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([paidUntil], map: "paidUntil")
  @@index([title], map: "title")
}

// checked
model Bank {
  id             Int         @id @default(autoincrement())
  accountId      Int?        @db.UnsignedMediumInt
  pin            String?     @db.VarChar(4)
  amount         BigInt      @default(0) @db.UnsignedBigInt
  tariff         bank_tariff @default(Standart)
  cashback       Int         @default(0) @db.UnsignedInt
  monthTransfers BigInt      @default(0) @db.UnsignedBigInt
  monthOuts      BigInt      @default(0) @db.UnsignedBigInt
  estimatedDate  DateTime?   @db.DateTime(0)
  blocked        Boolean?    @default(false)

  @@index([accountId], map: "accountId")
  @@index([amount], map: "amount")
  @@index([blocked], map: "blocked")
  @@index([estimatedDate], map: "estimatedDate")
  @@index([tariff], map: "tariff")
  @@map("bank")
}

// checked
model Banlist {
  id          Int       @id @default(autoincrement()) @db.UnsignedMediumInt
  userId      Int?      @db.UnsignedMediumInt
  accountId   Int?      @db.UnsignedMediumInt
  socialClub  String?   @db.VarChar(16)
  rgscId      Int?      @db.UnsignedInt
  hwid        String?   @db.VarChar(128)
  ip          String?   @db.VarChar(16)
  cheatPath   String?   @db.VarChar(128)
  banStart    DateTime? @db.DateTime(0)
  banEnd      DateTime? @db.DateTime(0)
  banReason   String?   @db.VarChar(128)
  banBy       Int?      @db.UnsignedMediumInt
  unbanBy     Int?      @db.UnsignedMediumInt
  unbanReason String?   @db.VarChar(128)

  @@index([accountId], map: "accountId")
  @@index([banBy], map: "banBy")
  @@index([banEnd], map: "banEnd")
  @@index([banReason], map: "banReason")
  @@index([banStart], map: "banStart")
  @@index([cheatPath], map: "cheatPath")
  @@index([hwid], map: "hwid")
  @@index([ip], map: "ip")
  @@index([rgscId], map: "rgscId")
  @@index([socialClub], map: "socialClub")
  @@index([unbanBy], map: "unbanBy")
  @@index([unbanReason], map: "unbanReason")
  @@index([userId], map: "userId")
  @@map("banlist")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model barber_shops {
  id             Int       @id @default(autoincrement()) @db.UnsignedTinyInt
  title          String?   @db.VarChar(24)
  accountId      Int?      @db.UnsignedMediumInt
  price          Int?      @default(5000000) @db.UnsignedInt
  cash           BigInt?   @default(0) @db.UnsignedBigInt
  bank           BigInt?   @default(0) @db.UnsignedBigInt
  posX           String?   @db.VarChar(10)
  posY           String?   @db.VarChar(10)
  posZ           String?   @db.VarChar(10)
  materials      Int?      @default(5000) @db.UnsignedMediumInt
  prices         Bytes?    @db.Blob
  discounts      Bytes?    @db.Blob
  mafiaId        Int?      @default(0) @db.UnsignedTinyInt
  mafiaCash      BigInt?   @default(0) @db.UnsignedBigInt
  defaultMafiaId Int?      @default(0) @db.UnsignedTinyInt
  paidUntil      DateTime? @db.DateTime(0)
  auctionId      Int?      @db.UnsignedInt
  marketplaceId  Int?      @db.UnsignedInt

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([paidUntil], map: "paidUntil")
  @@index([title], map: "title")
}

model biker_zones_reset {
  id         Int  @id @default(autoincrement())
  reset_time Int?
}

model biz_items {
  id     Int     @id @default(autoincrement()) @db.UnsignedInt
  type   String? @db.VarChar(20)
  bizId  Int     @default(0) @db.UnsignedTinyInt
  itemId String  @default("0") @db.VarChar(30)
  count  Int?    @db.UnsignedInt
  price  Int?    @db.UnsignedInt

  @@unique([type, bizId, itemId], map: "uni")
  @@index([bizId], map: "bizId")
  @@index([itemId], map: "itemId")
  @@index([type], map: "type")
}

model bk_bets {
  id          Int             @id @default(autoincrement()) @db.UnsignedInt
  accountId   Int?            @db.UnsignedMediumInt
  amount      Int?            @db.UnsignedMediumInt
  coefficient String?         @db.VarChar(10)
  lines       Int?            @db.UnsignedTinyInt
  status      bk_bets_status? @default(new)
  date        DateTime?       @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([date], map: "date")
  @@index([status], map: "status")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model bk_bets_old {
  id        Int                 @default(autoincrement()) @db.UnsignedInt
  userId    Int?                @db.UnsignedMediumInt
  accountId Int?                @db.UnsignedMediumInt
  fixtureId Int?                @db.UnsignedInt
  oddType   String?             @db.VarChar(50)
  amount    BigInt?             @default(0) @db.UnsignedBigInt
  status    bk_bets_old_status? @default(new)
  date      DateTime?           @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([date], map: "date")
  @@index([fixtureId], map: "fixtureId")
  @@index([id], map: "id")
  @@index([oddType], map: "oddType")
  @@index([status], map: "status")
  @@index([userId], map: "userId")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model bk_events {
  id          Int     @default(autoincrement()) @db.UnsignedInt
  accountId   Int?    @db.UnsignedInt
  betId       Int?    @default(0) @db.UnsignedInt
  fixtureId   Int?    @default(0) @db.UnsignedInt
  eventId     Int?    @db.UnsignedMediumInt
  type        String? @db.VarChar(50)
  paramId     String? @db.VarChar(10)
  coefficient String? @db.VarChar(10)

  @@index([accountId], map: "accountId")
  @@index([betId], map: "betId")
  @@index([eventId], map: "eventId")
  @@index([fixtureId], map: "fixtureId")
  @@index([id], map: "id")
  @@index([paramId], map: "paramId")
  @@ignore
}

model bk_fixtures_betboom {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  timestamp     Int?      @db.UnsignedInt
  category      String?   @db.VarChar(50)
  categoryRu    String?   @db.VarChar(50)
  subCategory   String?   @db.VarChar(50)
  subCategoryRu String?   @db.VarChar(50)
  tournament    String?   @db.VarChar(100)
  tournamentRu  String?   @db.VarChar(100)
  homeId        Int?      @db.UnsignedInt
  home          String?   @db.VarChar(100)
  homeRu        String?   @db.VarChar(100)
  awayId        Int?      @db.UnsignedInt
  away          String?   @db.VarChar(100)
  awayRu        String?   @db.VarChar(100)
  odds          String?   @db.LongText
  score         String?   @db.LongText
  updatedAt     DateTime? @db.DateTime(0)

  @@index([awayId], map: "awayId")
  @@index([categoryRu], map: "categoryRu")
  @@index([homeId], map: "homeId")
  @@index([category], map: "sport")
  @@index([subCategory], map: "subCategory")
  @@index([subCategoryRu], map: "subCategoryRu")
  @@index([timestamp], map: "timestamp")
  @@index([tournament], map: "tournament")
  @@index([tournamentRu], map: "tournamentRu")
  @@index([updatedAt], map: "updatedAt")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model bk_ratings {
  position  Int?               @db.UnsignedTinyInt
  accountId Int?               @db.UnsignedMediumInt
  value     BigInt?
  period    bk_ratings_period?
  type      bk_ratings_type?

  @@index([accountId], map: "accountId")
  @@index([period], map: "period")
  @@index([type], map: "type")
  @@ignore
}

// / The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model bk_stats {
  period    bk_stats_period?
  betCount  Int?             @db.UnsignedMediumInt
  betAmount BigInt?          @db.UnsignedBigInt
  winCount  Int?             @db.UnsignedMediumInt
  winAmount BigInt?          @db.UnsignedBigInt

  @@index([period], map: "period")
  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model burger_leaderboard {
  id        Int  @id @default(autoincrement())
  accountId Int?
  amount    Int? @default(0) @db.MediumInt

  @@index([accountId], map: "accountId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model businesses_statistics {
  id           Int      @id @default(autoincrement())
  income       Int      @default(0)
  netProfit    Int      @default(0)
  averageCheck Int      @default(0)
  salesCount   Int      @default(0)
  biz          String   @db.VarChar(50)
  date         DateTime @default(dbgenerated("(curdate())")) @db.Date

  @@index([biz], map: "biz")
  @@index([date], map: "date")
}

model CaptureEventsLog {
  id            Int      @id @default(autoincrement())
  captureId     Int
  type          String?  @db.VarChar(15)
  fromStaticId  Int
  toStaticId    Int
  fromFactionId Int
  toFactionId   Int
  value         Int
  createdAt     DateTime @default(now()) @db.DateTime(0)

  @@map("capture_events_logs")
}

model CaptureLog {
  id              Int       @id @default(autoincrement())
  captureId       Int?
  defendersId     Int?      @default(0)
  attackersId     Int       @default(0)
  gangZoneId      Int       @default(0)
  winner          Int       @default(0)
  startAt         DateTime  @default(now()) @db.DateTime(0)
  endAt           DateTime? @db.DateTime(0)
  uniqueDefenders Int       @default(0)
  uniqueAttackers Int       @default(0)
  status          String?   @db.VarChar(15)

  @@map("capture_logs")
}

model captures_admin_logs {
  id            Int       @id @default(autoincrement())
  adminId       Int?
  adminLvl      Int       @default(0)
  adminName     String?   @db.VarChar(50)
  message       String    @db.VarChar(300)
  comment       String    @db.VarChar(300)
  translated    Int?      @default(0)
  translateData String?   @default("{}") @db.Text
  createDate    DateTime? @default(now()) @db.DateTime(0)
}

model captures_messages {
  id            Int       @id @default(autoincrement())
  adminId       Int?
  message       String    @db.VarChar(300)
  translated    Int?      @default(0)
  translateData String?   @default("{}") @db.Text
  fractionIds   String    @db.Text
  type          String    @db.VarChar(50)
  createDate    DateTime? @default(now()) @db.DateTime(0)
}

model CapturesQueue {
  id                     Int     @id @default(autoincrement())
  attackerId             Int?    @db.TinyInt
  defenderId             Int?    @db.TinyInt
  zoneId                 Int?    @db.TinyInt
  startTime              BigInt?
  createTime             BigInt?
  peopleCount            Int?    @db.TinyInt
  caliberId              Int?    @db.SmallInt
  attackersIds           Bytes?  @db.Blob
  defendersIds           Bytes?  @db.Blob
  status                 String? @db.VarChar(50)
  attackerAdditionalTime Int?
  defenderAdditionalTime Int?

  @@map("captures_queue")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model carwash_shops {
  id             Int       @id @default(autoincrement()) @db.UnsignedTinyInt
  title          String?   @db.VarChar(20)
  accountId      Int?      @db.UnsignedMediumInt
  price          Int?      @default(5000000) @db.UnsignedInt
  cash           BigInt?   @default(0) @db.UnsignedBigInt
  bank           BigInt?   @default(0) @db.UnsignedBigInt
  posX           String?   @db.VarChar(10)
  posY           String?   @db.VarChar(10)
  posZ           String?   @db.VarChar(10)
  pickupX        String?   @db.VarChar(10)
  pickupY        String?   @db.VarChar(10)
  pickupZ        String?   @db.VarChar(10)
  materials      Int?      @default(5000) @db.UnsignedMediumInt
  prices         Bytes?    @db.Blob
  mafiaId        Int?      @default(0) @db.UnsignedTinyInt
  mafiaCash      BigInt?   @default(0) @db.UnsignedBigInt
  defaultMafiaId Int?      @default(0) @db.UnsignedTinyInt
  paidUntil      DateTime? @db.DateTime(0)
  auctionId      Int?      @db.UnsignedInt
  marketplaceId  Int?      @db.UnsignedInt

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([paidUntil], map: "paidUntil")
}

model cases_config {
  type  String    @id @db.VarChar(12)
  rest  Int?      @db.SmallInt
  until DateTime? @db.DateTime(0)

  @@index([until], map: "until")
}

model cases_log {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId      Int?      @db.UnsignedMediumInt
  caseType       String?   @db.VarChar(20)
  rouletteItemId String?   @db.VarChar(36)
  date           DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([caseType], map: "caseType")
  @@index([date], map: "date")
  @@index([rouletteItemId], map: "rouletteItemId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model chips_log {
  id        BigInt    @id @default(autoincrement())
  accountId Int?      @db.UnsignedMediumInt
  gameType  String?   @db.VarChar(50)
  gameId    Int?
  tableId   Int?
  amount    BigInt?
  prev      BigInt?   @db.UnsignedBigInt
  comment   String?   @db.VarChar(128)
  args      String?   @db.LongText
  date      DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([amount], map: "amount")
  @@index([comment], map: "comment")
  @@index([date], map: "date")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model clothes_shops {
  id             Int       @id @default(autoincrement()) @db.UnsignedTinyInt
  class          String?   @db.VarChar(24)
  title          String?   @db.VarChar(24)
  accountId      Int?      @db.UnsignedMediumInt
  price          Int?      @default(5000000) @db.UnsignedInt
  cash           BigInt?   @default(0) @db.UnsignedBigInt
  bank           BigInt?   @default(0) @db.UnsignedBigInt
  posX           String?   @db.VarChar(10)
  posY           String?   @db.VarChar(10)
  posZ           String?   @db.VarChar(10)
  playerPosX     String?   @db.VarChar(10)
  playerPosY     String?   @db.VarChar(10)
  playerPosZ     String?   @db.VarChar(10)
  playerPosA     String?   @db.VarChar(10)
  materials      Int?      @default(5000) @db.UnsignedMediumInt
  prices         Bytes?    @db.Blob
  discounts      Bytes?    @db.Blob
  mafiaId        Int?      @default(0) @db.UnsignedTinyInt
  mafiaCash      BigInt?   @default(0) @db.UnsignedBigInt
  defaultMafiaId Int?      @default(0) @db.UnsignedTinyInt
  paidUntil      DateTime? @db.DateTime(0)
  auctionId      Int?      @db.UnsignedInt
  marketplaceId  Int?      @db.UnsignedInt

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([paidUntil], map: "paidUntil")
  @@index([title], map: "title")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model containers {
  id           Int     @default(autoincrement())
  type         Int     @default(0)
  rare         String  @db.VarChar(50)
  bet          Int     @default(0)
  members      String  @db.Text
  prize        String? @db.Text
  posX         String  @db.VarChar(50)
  posY         String  @db.VarChar(50)
  posZ         String  @db.VarChar(50)
  timeWhenOpen Int

  @@index([id], map: "id")
  @@ignore
}

// / The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model craftings {
  id    String?   @default("") @db.VarChar(36)
  type  Int?      @default(0) @db.TinyInt
  owner Int?      @default(0)
  time  DateTime? @db.DateTime(0)
  data  String?   @default("{}") @db.VarChar(85)

  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model crash_bets {
  id         Int      @id @default(autoincrement())
  userId     Int
  accountId  Int
  bet        Int
  exitX      Float?   @db.Float
  gameId     Int
  dateBet    DateTime @default(now()) @db.DateTime(0)
  socialClub String?  @db.VarChar(16)

  @@index([dateBet], map: "dateBet")
  @@index([gameId], map: "gameId")
  @@index([userId], map: "userId")
}

model crash_games {
  id           Int      @id @default(autoincrement())
  playersCount Int
  crashed_at   Float    @db.Float
  bank         Int
  won          Int
  lost         Int
  dateGame     DateTime @default(now()) @db.DateTime(0)

  @@index([dateGame], map: "dateGame")
}

model criminal_record_logs {
  id        Int      @id @default(autoincrement()) @db.UnsignedInt
  arrestId  Int?
  accountId Int
  recordBy  Int?
  reason    String   @db.VarChar(50)
  endDate   DateTime @db.DateTime(0)
  date      DateTime @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([endDate], map: "endDate")
}

model custom_promocodes {
  id      Int       @id @default(autoincrement())
  code    String?   @db.VarChar(6)
  used    Int?      @default(0) @db.SmallInt
  limit   Int?      @default(0) @db.SmallInt
  endDate DateTime? @db.DateTime(0)
  donate  Int?      @default(0) @db.SmallInt
  money   Int?      @default(0) @db.SmallInt

  @@index([code], map: "code")
}

model custom_tuning {
  id       Int     @id @default(autoincrement()) @db.UnsignedInt
  vehicle  Int?    @db.UnsignedMediumInt
  modType  Int?    @db.UnsignedTinyInt
  modIndex String? @db.VarChar(7)

  @@index([modIndex], map: "modIndex")
  @@index([modType], map: "modType")
  @@index([vehicle], map: "vehicleId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model deposits {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId  Int?      @db.UnsignedMediumInt
  tariffId   Boolean?
  amount     BigInt?   @default(0) @db.UnsignedBigInt
  profit     BigInt?   @default(0) @db.UnsignedBigInt
  hours      Int?      @db.UnsignedSmallInt
  date       DateTime? @db.DateTime(0)
  closedDate DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([closedDate], map: "closedDate")
  @@index([date], map: "date")
  @@index([tariffId], map: "tariffId")
}

// checked
model DonateLog {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  userId    Int?      @db.UnsignedMediumInt
  accountId Int?      @db.UnsignedMediumInt
  amount    Int?      @db.MediumInt
  date      DateTime? @db.DateTime(0)
  comment   String?   @db.VarChar(100)
  args      String?   @db.LongText

  @@index([accountId], map: "accountId")
  @@index([date], map: "date")
  @@index([userId], map: "userId")
  @@map("donate_log")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model drift_score {
  id           Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId    Int?      @db.UnsignedMediumInt
  zoneId       Int?      @db.SmallInt
  vehicleModel String?   @db.VarChar(50)
  score        Int?
  date         DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([date], map: "date")
  @@index([vehicleModel], map: "vehicleModel")
  @@index([zoneId], map: "zoneId")
}

model election_candidates {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  electionId  Int?    @db.UnsignedSmallInt
  accountId   Int?    @db.UnsignedMediumInt
  description String? @db.VarChar(128)
  slogan      String? @db.VarChar(128)

  @@unique([electionId, accountId], map: "uniq")
  @@index([accountId], map: "accountId")
  @@index([electionId], map: "electionId")
}

model election_votes {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  electionId Int?      @db.UnsignedMediumInt
  userId     Int?      @db.UnsignedMediumInt
  selected   Int?      @db.UnsignedMediumInt
  date       DateTime? @db.DateTime(0)

  @@index([date], map: "date")
  @@index([electionId], map: "electionId")
  @@index([selected], map: "selected")
  @@index([userId], map: "startedBy")
}

model elections {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  startedBy Int?      @db.UnsignedMediumInt
  createdAt DateTime? @db.DateTime(0)
  startAt   DateTime? @db.Date
  winner    Int?      @db.UnsignedMediumInt

  @@index([createdAt], map: "date")
  @@index([startAt], map: "startAt")
  @@index([startedBy], map: "startedBy")
  @@index([winner], map: "winner")
}

model enterprises {
  id         Int    @id @default(autoincrement()) @db.TinyInt
  owner      Int    @default(0) @db.SmallInt
  position   String @default("{\"x\":0,\"y\":0,\"z\":0}") @db.VarChar(50)
  posA       Float  @default(0) @db.Float
  garage     String @default("{\"x\":0,\"y\":0,\"z\":0}") @db.VarChar(50)
  garagePosA Float  @default(0) @db.Float
  type       Int    @default(0) @db.TinyInt
  level      Int    @default(0) @db.TinyInt
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model facility {
  id                Int    @id @default(autoincrement())
  name              String @default("") @db.VarChar(50)
  posX              String @default("") @db.VarChar(10)
  posY              String @default("") @db.VarChar(10)
  posZ              String @default("") @db.VarChar(10)
  payment           Int    @default(0) @db.UnsignedSmallInt
  fractionId        Int    @default(0) @db.UnsignedSmallInt
  defaultFractionId Int    @default(0) @db.UnsignedSmallInt
}

// checked
model Family {
  id                 Int                @id @default(autoincrement()) @db.UnsignedMediumInt
  title              String?            @unique(map: "title") @db.VarChar(32)
  tag                String?            @unique(map: "tag") @db.VarChar(4)
  mainColor          String?            @db.VarChar(10)
  theme              families_theme?    @default(black)
  backgroundColor    String?            @db.VarChar(10)
  blipColor          String?            @db.VarChar(10)
  logo               String?            @db.VarChar(100)
  leaderId           Int?               @db.UnsignedMediumInt
  money              BigInt?            @default(0) @db.UnsignedBigInt
  createdAt          DateTime?          @db.DateTime(0)
  createdBy          Int?               @db.MediumInt
  reputation         Int?               @db.MediumInt
  exp                Int?               @db.UnsignedMediumInt
  contractsData      Bytes?             @db.Blob
  contractsCompleted Bytes?             @default(dbgenerated("('{}')")) @db.Blob
  cooldowns          Bytes?             @db.Blob
  rentProperty       Bytes?             @db.Blob
  logoUploaded       Boolean?           @default(false)
  logoStatus         Int?               @db.TinyInt
  logoReportId       Int?
  logoType           families_logoType?
  logoToken          String?            @db.VarChar(16)
  titleStatus        Boolean?           @default(false)
  titleReportId      Int?
  lastUpdated        DateTime?          @db.DateTime(0)
  removedBy          Int?               @db.UnsignedMediumInt
  removedDate        DateTime?          @db.DateTime(0)
  block              DateTime?          @db.DateTime(0)
  capturesBlock      DateTime?          @db.DateTime(0)

  @@index([createdAt], map: "createdAt")
  @@index([createdBy], map: "createdBy")
  @@index([leaderId], map: "leaderId")
  @@index([removedBy], map: "removedBy")
  @@index([removedDate], map: "removedDate")
  @@map("families")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model family_blacklist {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  familyId  Int       @db.MediumInt
  accountId Int?      @db.UnsignedMediumInt
  actionBy  Int?      @db.UnsignedMediumInt
  reason    String?   @db.VarChar(150)
  date      DateTime? @default(now()) @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([actionBy], map: "actionBy")
  @@index([familyId], map: "familyId")
}

model family_craft_settings {
  id       Int      @id @default(autoincrement())
  familyId Int?     @db.UnsignedSmallInt
  itemId   Int?     @db.UnsignedSmallInt
  value    Boolean? @default(true)

  @@unique([familyId, itemId], map: "Индекс 4")
  @@index([familyId], map: "familyId")
  @@index([itemId], map: "itemId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model FamilyLog {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  familyId  Int?      @db.UnsignedSmallInt
  accountId Int?      @db.UnsignedMediumInt
  actionBy  Int?      @db.UnsignedMediumInt
  type      String?   @db.VarChar(50)
  reason    String?   @db.VarChar(50)
  args      String?   @db.LongText
  date      DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([actionBy], map: "actionBy")
  @@index([date], map: "date")
  @@index([familyId], map: "familyId")
  @@index([type], map: "type")
  @@map("family_log")
}

model family_money_log {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  familyId    Int?      @db.UnsignedSmallInt
  accountId   Int?      @db.UnsignedMediumInt
  toAccountId Int?      @db.UnsignedMediumInt
  amount      BigInt?   @default(0)
  prev        BigInt?   @default(0)
  comment     String?   @db.VarChar(128)
  args        String?   @db.LongText
  date        DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([amount], map: "amount")
  @@index([comment], map: "comment")
  @@index([date], map: "date")
  @@index([familyId], map: "fractionId")
  @@index([toAccountId], map: "toAccountId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model family_perks {
  id       Int       @id @default(autoincrement())
  familyId Int
  perk     String    @db.VarChar(50)
  date     DateTime? @db.DateTime(0)

  @@unique([familyId, perk], map: "familyId_perk")
}

model family_rank_settings {
  id       Int      @id @default(autoincrement())
  familyId Int?     @db.UnsignedSmallInt
  rankId   Int?     @db.UnsignedMediumInt
  key      String?  @db.VarChar(20)
  value    Boolean?

  @@unique([familyId, rankId, key], map: "uni")
  @@index([familyId], map: "fractionId")
  @@index([key], map: "key")
  @@index([rankId], map: "rankId")
}

// / The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model family_ranks {
  id       Int     @default(autoincrement()) @db.UnsignedInt
  familyId Int?    @db.UnsignedSmallInt
  rank     Int?    @db.UnsignedTinyInt
  name     String? @db.VarChar(24)

  @@index([familyId], map: "fractionId")
  @@index([id], map: "id")
  @@index([rank], map: "rank")
  @@ignore
}

model family_vehicle_settings {
  id        Int      @id @default(autoincrement())
  familyId  Int?     @db.UnsignedSmallInt
  vehicleId Int?     @db.UnsignedMediumInt
  value     Boolean? @default(true)
  percent   Boolean? @default(false)

  @@unique([familyId, vehicleId], map: "Индекс 4")
  @@index([familyId], map: "fractionId")
  @@index([vehicleId], map: "vehicle")
}

model family_wars_admin_logs {
  id            Int       @id @default(autoincrement())
  adminId       Int?
  adminLvl      Int       @default(0)
  adminName     String?   @db.VarChar(50)
  message       String    @db.VarChar(300)
  comment       String    @db.VarChar(300)
  translated    Int?      @default(0)
  translateData String?   @default("{}") @db.Text
  createDate    DateTime? @default(now()) @db.DateTime(0)
}

model family_wars_data {
  id              Int     @id @default(autoincrement())
  type            String? @db.VarChar(50)
  fractionId      Int?
  attacksCount    Int?    @default(3)
  defencesCount   Int?    @default(3)
  lastAttackTime  Int?
  lastDefenceTime Int?
  freezeAttacks   Int?    @default(0) @db.TinyInt
  maxAttacks      Int?    @default(2)
  maxDefences     Int?    @default(2)
  addDfenceLog    Bytes?  @db.Blob
  addAttackLog    Bytes?  @db.Blob

  @@index([fractionId], map: "fractionId")
}

model FamilyWarsEventsLog {
  id            Int      @id @default(autoincrement())
  captureId     Int
  type          String?  @db.VarChar(15)
  fromStaticId  Int
  toStaticId    Int
  fromFactionId Int
  toFactionId   Int
  value         Int
  createdAt     DateTime @default(now()) @db.DateTime(0)

  @@map("family_wars_events_logs")
}

model FamilyWarsLog {
  id              Int       @id @default(autoincrement())
  captureId       Int?
  defendersId     Int?      @default(0)
  attackersId     Int       @default(0)
  gangZoneId      Int       @default(0)
  winner          Int       @default(0)
  startAt         DateTime  @default(now()) @db.DateTime(0)
  endAt           DateTime? @db.DateTime(0)
  uniqueDefenders Int       @default(0)
  uniqueAttackers Int       @default(0)
  status          String?   @db.VarChar(15)

  @@map("family_wars_logs")
}

model family_wars_messages {
  id            Int       @id @default(autoincrement())
  adminId       Int?
  message       String    @db.VarChar(300)
  translated    Int?      @default(0)
  translateData String?   @default("{}") @db.Text
  fractionIds   String    @db.Text
  type          String    @db.VarChar(50)
  createDate    DateTime? @default(now()) @db.DateTime(0)
}

model FamilyWarsQueue {
  id                     Int     @id @default(autoincrement())
  attackerId             Int?
  defenderId             Int?
  zoneId                 Int?    @db.UnsignedInt
  startTime              BigInt?
  createTime             BigInt?
  peopleCount            Int?    @db.TinyInt
  caliberId              Int?    @db.SmallInt
  attackersIds           Bytes?  @db.Blob
  defendersIds           Bytes?  @db.Blob
  status                 String? @db.VarChar(50)
  attackerAdditionalTime Int?
  defenderAdditionalTime Int?

  @@map("family_wars_queue")
}

model family_wars_zones {
  id                Int     @id @default(autoincrement()) @db.UnsignedInt
  zoneId            Int     @db.UnsignedInt
  fractionId        Int?    @db.UnsignedInt
  level             Int?    @db.UnsignedTinyInt
  controllStartTime BigInt? @db.UnsignedBigInt
  debuffZoneId      Int?    @db.UnsignedInt
  debuffEndTime     BigInt? @db.UnsignedBigInt
  effectCooldown    BigInt? @db.UnsignedBigInt

  @@index([fractionId], map: "familyId")
  @@index([zoneId], map: "zoneId")
}

// / The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
// / This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model forbes_families {
  id            Int?    @unique(map: "id")
  familyId      Int?    @db.MediumInt
  name          String? @db.VarChar(24)
  tag           String? @db.VarChar(4)
  static        Int?
  username      String? @db.VarChar(50)
  amountMembers Int?
  wealth        BigInt?
  logo          String? @db.VarChar(150)
  prevId        Int?

  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model forbes_players {
  id         Int?    @unique(map: "id")
  username   String? @db.VarChar(50)
  static     Int?
  level      Int?
  biz        String? @db.VarChar(50)
  family     String? @db.VarChar(50)
  wealth     BigInt?
  socialClub String? @db.VarChar(16)
  prevId     Int?

  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model forbes_yearWealth {
  type   String? @unique(map: "type") @db.VarChar(6)
  wealth Bytes?  @db.Blob

  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model fraction_blacklist {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  fractionId Int       @db.MediumInt
  accountId  Int?      @db.UnsignedMediumInt
  actionBy   Int?      @db.UnsignedMediumInt
  reason     String?   @db.VarChar(150)
  date       DateTime? @default(now()) @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([actionBy], map: "actionBy")
  @@index([fractionId], map: "fractionId")
}

model fraction_craft_settings {
  id         Int      @id @default(autoincrement())
  fractionId Int?     @db.UnsignedSmallInt
  itemId     Int?     @db.UnsignedSmallInt
  value      Boolean? @default(true)

  @@unique([fractionId, itemId], map: "Индекс 4")
  @@index([fractionId], map: "fractionId")
  @@index([itemId], map: "itemId")
}

// checked
model FractionLog {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  fractionId Int?      @db.UnsignedSmallInt
  accountId  Int?      @db.UnsignedMediumInt
  actionBy   Int?      @db.UnsignedMediumInt
  test       Int?      @db.UnsignedMediumInt
  type       String?   @db.VarChar(100)
  reason     String?   @db.VarChar(150)
  args       String?   @db.LongText
  date       DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([actionBy], map: "actionBy")
  @@index([date], map: "date")
  @@index([fractionId], map: "fractionId")
  @@index([type], map: "type")
  @@map("fraction_log")
}

model fraction_money_log {
  id           Int       @id @default(autoincrement()) @db.UnsignedInt
  fractionId   Int?      @db.UnsignedSmallInt
  accountId    Int?      @db.UnsignedMediumInt
  toFractionid Int?      @db.UnsignedSmallInt
  toAccountId  Int?      @db.UnsignedMediumInt
  amount       BigInt?   @default(0)
  comment      String?   @db.VarChar(128)
  args         String?   @db.LongText
  date         DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([amount], map: "amount")
  @@index([comment], map: "comment")
  @@index([date], map: "date")
  @@index([fractionId], map: "fractionId")
  @@index([toAccountId], map: "toAccountId")
  @@index([toFractionid], map: "toFractionid")
}

model fraction_player_settings {
  id         Int      @id @default(autoincrement())
  fractionId Int?     @db.UnsignedSmallInt
  accountId  Int?     @db.UnsignedMediumInt
  rank       Int?     @db.UnsignedTinyInt
  key        String?  @db.VarChar(20)
  value      Boolean?

  @@index([accountId], map: "accountId")
  @@index([fractionId], map: "fractionId")
  @@index([key], map: "key")
  @@index([rank], map: "rank")
}

model fraction_rank_settings {
  id         Int      @id @default(autoincrement())
  fractionId Int?     @db.UnsignedSmallInt
  rankId     Int?     @db.UnsignedMediumInt
  key        String?  @db.VarChar(20)
  value      Boolean?

  @@unique([fractionId, rankId, key], map: "uni")
  @@index([fractionId], map: "fractionId")
  @@index([key], map: "key")
  @@index([rankId], map: "rank")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model fraction_ranks {
  id         Int     @default(autoincrement()) @db.UnsignedInt
  fractionId Int?    @db.UnsignedSmallInt
  rank       Int?    @db.UnsignedTinyInt
  name       String? @db.VarChar(24)

  @@index([fractionId], map: "fractionId")
  @@index([id], map: "id")
  @@index([rank], map: "rank")
  @@ignore
}

model fraction_vehicle_settings {
  id         Int      @id @default(autoincrement())
  fractionId Int?     @db.UnsignedSmallInt
  vehicle    String?  @db.VarChar(24)
  value      Boolean? @default(true)

  @@unique([fractionId, vehicle], map: "Индекс 4")
  @@index([fractionId], map: "fractionId")
  @@index([vehicle], map: "vehicle")
}

// checked
model Fraction {
  id              Int              @id @default(autoincrement()) @db.UnsignedTinyInt
  title           String?          @db.VarChar(32)
  leaderId        Int?             @db.UnsignedMediumInt
  lockEnter       Boolean?         @default(false)
  money           BigInt?          @default(0) @db.UnsignedBigInt
  awardCount      Int?             @default(0) @db.UnsignedTinyInt
  awardAmount     BigInt?          @default(0) @db.UnsignedBigInt
  financingAmount BigInt?          @default(0)
  backgroundColor String?          @db.VarChar(10)
  logo            String?          @db.VarChar(200)
  mainColor       String?          @db.VarChar(10)
  tag             String?          @db.VarChar(4)
  theme           fractions_theme? @default(black)

  @@index([leaderId], map: "leaderId")
  @@map("fractions")
}

model fractions_captures_data {
  id              Int     @id @default(autoincrement())
  type            String? @db.VarChar(50)
  fractionId      Int?
  attacksCount    Int?    @default(3)
  defencesCount   Int?    @default(3)
  lastAttackTime  Int?
  lastDefenceTime Int?
  freezeAttacks   Int?    @default(0) @db.TinyInt
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model fractions_old {
  id           Int      @id @default(autoincrement()) @db.UnsignedTinyInt
  fractionName String?  @db.VarChar(32)
  leaderId     Int?     @db.UnsignedMediumInt
  lockEnter    Boolean? @default(false)
  money        BigInt?  @default(0) @db.UnsignedBigInt
  materials    Int?     @default(0) @db.UnsignedInt
  settings     Bytes?   @db.Blob

  @@index([leaderId], map: "leaderId")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model friends {
  accountId Int? @db.UnsignedMediumInt
  friendId  Int? @db.UnsignedMediumInt

  @@index([accountId], map: "accountId")
  @@index([friendId], map: "friendId")
  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model fuel_stations {
  id             Int       @id @default(autoincrement()) @db.UnsignedTinyInt
  title          String?   @default("Заправочная станция") @db.VarChar(24)
  type           String?   @db.VarChar(10)
  accountId      Int?      @db.UnsignedMediumInt
  price          Int?      @default(5000000) @db.UnsignedInt
  cash           BigInt?   @default(0) @db.UnsignedBigInt
  bank           BigInt?   @default(0) @db.UnsignedBigInt
  posX           String?   @db.VarChar(10)
  posY           String?   @db.VarChar(10)
  posZ           String?   @db.VarChar(10)
  warehouse      Bytes?    @db.Blob
  prices         Bytes?    @db.Blob
  discounts      Bytes?    @db.Blob
  mafiaId        Int?      @default(0) @db.UnsignedTinyInt
  mafiaCash      BigInt?   @default(0) @db.UnsignedBigInt
  defaultMafiaId Int?      @default(0) @db.UnsignedTinyInt
  paidUntil      DateTime? @db.DateTime(0)
  auctionId      Int?      @db.UnsignedInt
  marketplaceId  Int?      @db.UnsignedInt

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([paidUntil], map: "paidUntil")
  @@index([title], map: "title")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gang_zones {
  id                Int  @id @default(autoincrement()) @db.UnsignedTinyInt
  fractionId        Int? @db.UnsignedMediumInt
  defaultFractionId Int? @db.UnsignedMediumInt

  @@index([fractionId], map: "accountId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gang_zones_reset {
  id         Int @id @default(autoincrement())
  reset_time Int
}

model garages {
  id        Int           @id @default(autoincrement()) @db.UnsignedInt
  type      garages_type?
  keyId     Int?          @db.UnsignedSmallInt
  slot      Int?          @db.UnsignedSmallInt
  vehicleId Int?          @db.UnsignedMediumInt

  @@index([keyId], map: "keyId")
  @@index([slot], map: "slot")
  @@index([type], map: "type")
  @@index([vehicleId], map: "vehicleId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gps_favourites {
  id        Int     @id @default(autoincrement()) @db.UnsignedInt
  accountId Int?    @db.UnsignedMediumInt
  title     String? @db.VarChar(64)
  posX      String? @db.VarChar(10)
  posY      String? @db.VarChar(10)
  posZ      String? @db.VarChar(10)
  icon      String? @db.VarChar(20)
  args      Bytes?  @db.Blob

  @@index([accountId], map: "userId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model gps_log {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId Int?      @db.UnsignedMediumInt
  title     String?   @db.VarChar(64)
  date      DateTime? @db.DateTime(0)
  posX      String?   @db.VarChar(10)
  posY      String?   @db.VarChar(10)
  posZ      String?   @db.VarChar(10)
  icon      String?   @db.VarChar(20)

  @@index([date], map: "date")
  @@index([accountId], map: "userId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model houses {
  id            Int       @id @default(autoincrement()) @db.UnsignedSmallInt
  accountId     Int?      @db.UnsignedMediumInt
  posX          String?   @db.VarChar(10)
  posY          String?   @db.VarChar(10)
  posZ          String?   @db.VarChar(10)
  posA          String?   @db.VarChar(10)
  garagePosX    String?   @db.VarChar(10)
  garagePosY    String?   @db.VarChar(10)
  garagePosZ    String?   @db.VarChar(10)
  garagePosA    String?   @db.VarChar(10)
  garage        Boolean?  @default(false)
  price         Int?      @default(15000) @db.UnsignedInt
  interior      Boolean?  @default(true)
  rentPrice     Int?      @default(0) @db.UnsignedInt
  cash          BigInt?   @default(0) @db.UnsignedBigInt
  maxRent       Boolean?  @default(true)
  locked        Boolean?  @default(false)
  paidUntil     DateTime? @default(dbgenerated("('2020-02-28 00:00:00')")) @db.DateTime(0)
  auctionId     Int?      @db.UnsignedInt
  marketplaceId Int?      @db.UnsignedInt
  familyId      Int?      @db.UnsignedInt
  workShop      Int?      @default(0) @db.TinyInt

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([familyId], map: "familyId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([paidUntil], map: "paidUntil")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model item_shops {
  id             Int       @id @default(autoincrement()) @db.TinyInt
  title          String?   @db.VarChar(30)
  type           String?   @db.VarChar(24)
  accountId      Int?      @db.UnsignedMediumInt
  price          Int?      @default(5000000) @db.UnsignedInt
  cash           BigInt?   @default(0) @db.UnsignedBigInt
  bank           BigInt?   @default(0) @db.UnsignedBigInt
  posX           String?   @db.VarChar(10)
  posY           String?   @db.VarChar(10)
  posZ           String?   @db.VarChar(10)
  pickupX        String?   @db.VarChar(10)
  pickupY        String?   @db.VarChar(10)
  pickupZ        String?   @db.VarChar(10)
  warehouse      Bytes?    @db.Blob
  prices         Bytes?    @db.Blob
  discounts      Bytes?    @db.Blob
  mafiaId        Int?      @default(0) @db.UnsignedTinyInt
  mafiaCash      BigInt?   @default(0) @db.UnsignedBigInt
  defaultMafiaId Int?      @default(0) @db.UnsignedTinyInt
  paidUntil      DateTime? @db.DateTime(0)
  auctionId      Int?      @db.UnsignedInt
  marketplaceId  Int?      @db.UnsignedInt

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([paidUntil], map: "paidUntil")
  @@index([title], map: "title")
}

// checked
model Item {
  id          Int             @id @default(autoincrement()) @db.UnsignedInt
  itemId      Int?            @db.UnsignedSmallInt
  owner       Int?            @db.UnsignedInt
  slot        Int?            @db.UnsignedSmallInt
  position    items_position? @default(drop)
  count       Int?            @default(0) @db.UnsignedInt
  condition   Int?            @default(0) @db.UnsignedSmallInt
  pX          String?         @db.VarChar(10)
  pY          String?         @db.VarChar(10)
  pZ          String?         @db.VarChar(10)
  rX          String?         @db.VarChar(10)
  rY          String?         @db.VarChar(10)
  rZ          String?         @db.VarChar(10)
  dimension   Int?            @db.UnsignedInt
  info        Bytes?          @db.Blob
  component   Int?            @db.UnsignedTinyInt
  drawable    Int?            @db.UnsignedSmallInt
  texture     Int?            @db.UnsignedSmallInt
  isProp      Boolean?
  gender      Boolean?
  isTurn      Boolean?        @default(false)
  numberplate String?         @db.VarChar(10)

  @@index([itemId], map: "itemId")
  @@index([owner], map: "owner")
  @@index([position], map: "position")
  @@map("items")
}

model ItemLog {
  id            BigInt             @id @default(autoincrement()) @db.UnsignedBigInt
  accountId     Int?               @db.UnsignedMediumInt
  itemSqlId     Int?               @db.UnsignedInt
  itemId        Int?               @db.UnsignedSmallInt
  comment       String?            @db.VarChar(64)
  args          String?            @db.LongText
  org           String?            @db.VarChar(20)
  amount        Int?               @db.UnsignedInt
  date          DateTime?          @db.DateTime(0)
  source        items_logs_source?
  transactionId Int?               @db.UnsignedInt

  @@index([accountId], map: "accountId")
  @@index([comment], map: "comment")
  @@index([date], map: "date")
  @@index([itemId], map: "itemId")
  @@index([itemSqlId], map: "itemSqlId")
  @@index([org], map: "org")
  @@index([source], map: "source")
  @@index([transactionId], map: "transactionId")
  @@map("items_logs")
}

// checked
model ItemTradeLog {
  id      Int       @id @default(autoincrement()) @db.UnsignedInt
  player1 Int?      @db.UnsignedMediumInt
  player2 Int?      @db.UnsignedMediumInt
  amount1 BigInt?   @db.UnsignedBigInt
  amount2 BigInt?   @db.UnsignedBigInt
  items1  String?   @db.VarChar(60)
  items2  String?   @db.VarChar(60)
  date    DateTime? @db.DateTime(0)

  @@index([date], map: "date")
  @@index([player1], map: "player1")
  @@index([player2], map: "player2")
  @@map("items_trade_logs")
}

model jackpot_bets {
  id         Int       @id @default(autoincrement())
  userId     Int?
  accountId  Int?
  amount     Int?
  ticket     String?   @db.VarChar(10)
  gameId     Int?
  dateBet    DateTime? @default(now()) @db.DateTime(0)
  socialClub String?   @db.VarChar(16)
  login      String?   @db.VarChar(20)

  @@index([dateBet], map: "dateBet")
  @@index([gameId], map: "gameId")
  @@index([userId], map: "userId")
}

model jackpot_games {
  id               Int      @id @default(autoincrement())
  playersCount     Int
  bank             Int
  winnerId         Int      @default(0)
  winnerChance     Float    @db.Float
  winnerPosition   Float    @default(0) @db.Float
  dateGame         DateTime @default(now()) @db.DateTime(0)
  winnerLogin      String?  @db.VarChar(20)
  winnerSocialClub String?  @db.VarChar(16)

  @@index([dateGame], map: "dateGame")
  @@index([winnerId], map: "winnerId")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model job_points {
  id Int?

  @@ignore
}

model key_binds {
  id        Int             @id @default(autoincrement()) @db.UnsignedInt
  type      key_binds_type?
  key       Int?            @db.UnsignedInt
  itemSqlId Int?            @db.UnsignedInt

  @@index([itemSqlId], map: "itemSqlId")
  @@index([key], map: "key")
  @@index([type], map: "type")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model mafia_zones_reset {
  id         Int @id @default(autoincrement())
  reset_time Int
}

model marketplace_ads {
  id          Int      @id @default(autoincrement())
  accountId   Int      @default(0) @db.MediumInt
  nickname    String   @default("") @db.VarChar(20)
  socialClub  String   @default("") @db.VarChar(16)
  type        String   @default("") @db.VarChar(20)
  price       BigInt   @default(0) @db.UnsignedBigInt
  favorites   Int      @default(0)
  views       Int      @default(0)
  comment     String   @default("") @db.VarChar(250)
  phoneNumber String?  @db.VarChar(8)
  args        Bytes?   @db.Blob
  images      String?  @db.LongText
  buyerId     Int?     @db.MediumInt
  date        DateTime @db.DateTime(0)
  endDate     DateTime @db.DateTime(0)
  status      Boolean  @default(false)

  @@index([accountId], map: "accountId")
  @@index([endDate], map: "endDate")
  @@index([status], map: "status")
}

model marketplace_auction {
  id           Int      @id @default(autoincrement())
  accountId    Int?
  type         String   @db.VarChar(10)
  keyId        Int
  bidStart     BigInt   @default(0) @db.UnsignedBigInt
  bidStep      BigInt   @default(0) @db.UnsignedBigInt
  currentBet   BigInt?  @db.UnsignedBigInt
  participants Int      @default(0) @db.SmallInt
  favorites    Int      @default(0) @db.SmallInt
  views        Int      @default(0) @db.SmallInt
  startDate    DateTime @db.DateTime(0)
  endDate      DateTime @db.DateTime(0)
  finalPrice   BigInt?  @db.UnsignedBigInt
  winnerId     Int?
  warehouseId  Int?
  args         Bytes?   @db.Blob
  status       Boolean? @default(true)

  @@index([accountId], map: "accountId")
  @@index([endDate], map: "endDate")
  @@index([keyId], map: "keyId")
  @@index([status], map: "status")
  @@index([type], map: "type")
  @@index([warehouseId], map: "warehouseId")
  @@index([winnerId], map: "winnerId")
}

model marketplace_auction_bets {
  id        Int       @id @default(autoincrement())
  auctionId Int?
  accountId Int?
  type      String?   @db.VarChar(15)
  time      DateTime? @db.DateTime(0)
  bid       BigInt?   @db.UnsignedBigInt
  active    Boolean?  @default(false)
  params    Bytes?    @db.Blob

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([type], map: "type")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model marketplace_history {
  id        Int       @id @default(autoincrement())
  accountId Int
  type      String?   @db.VarChar(50)
  tableName String?   @db.VarChar(50)
  cost      BigInt?   @db.UnsignedBigInt
  date      DateTime? @db.DateTime(0)
  args      Bytes?    @db.Blob

  @@index([accountId], map: "accountId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model marketplace_trading {
  id         Int      @id @default(autoincrement())
  accountId  Int?     @db.MediumInt
  name       String?  @db.VarChar(20)
  amount     BigInt   @default(0) @db.UnsignedBigInt
  condition  Int      @default(0)
  price      BigInt   @default(0) @db.UnsignedBigInt
  itemId     Int
  type       String   @db.VarChar(100)
  buyerIds   Bytes?   @db.MediumBlob
  itemSqlIds Bytes?   @db.MediumBlob
  endDate    DateTime @db.DateTime(0)
  date       DateTime @db.DateTime(0)
  args       Bytes    @db.MediumBlob
  status     Boolean  @default(true)

  @@index([accountId], map: "accountId")
  @@index([endDate], map: "endDate")
  @@index([status], map: "status")
}

model marketplace_trading_log {
  id          Int      @id @default(autoincrement())
  tradingId   Int?
  itemId      Int
  accountFrom Int?
  accountTo   Int
  amount      BigInt   @default(0) @db.UnsignedBigInt
  component   Int?     @db.UnsignedSmallInt
  drawable    Int?     @db.UnsignedSmallInt
  texture     Int?     @db.UnsignedSmallInt
  gender      Boolean?
  isProp      Boolean?
  numberplate String?  @db.VarChar(10)
  itemSqlIds  Bytes?   @db.Blob
  price       BigInt   @default(0) @db.UnsignedBigInt
  date        DateTime @db.DateTime(0)

  @@index([accountFrom], map: "accountFrom")
  @@index([accountTo], map: "accountTo")
  @@index([date], map: "date")
  @@index([itemId], map: "itemId")
}

model marketplace_warehouse {
  id        Int      @id @default(autoincrement())
  accountId Int?
  type      String?  @db.VarChar(50)
  endDate   DateTime @db.DateTime(0)
  args      Bytes    @db.MediumBlob

  @@index([accountId], map: "accountId")
  @@index([endDate], map: "endDate")
}

model mediaSounds_purchased {
  id        Int       @id @default(autoincrement())
  accountId Int       @db.UnsignedInt
  userId    Int?
  soundId   Int       @db.UnsignedSmallInt
  date      DateTime? @db.DateTime(0)

  @@index([accountId, userId], map: "accountId_userId")
}

// checked
model MoneyLog {
  id           BigInt    @id @default(autoincrement())
  accountId    Int?      @db.UnsignedMediumInt
  bankId       Int?      @db.UnsignedInt
  amount       BigInt?
  prev         BigInt?   @db.UnsignedBigInt
  comment      String?   @db.VarChar(128)
  args         String?   @db.LongText
  date         DateTime? @db.DateTime(0)
  biz          String?   @db.VarChar(20)
  taxPercent   String?   @db.VarChar(4)
  mafiaPercent Int?      @db.UnsignedTinyInt
  unitPrice    BigInt?

  @@index([accountId], map: "accountId")
  @@index([amount], map: "amount")
  @@index([bankId], map: "bankId")
  @@index([biz], map: "biz")
  @@index([comment], map: "comment")
  @@index([date], map: "date")
  @@map("money_log")
}

model multiaccounts {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId  Int?      @db.UnsignedMediumInt
  userId     Int?      @db.UnsignedMediumInt
  login      String?   @db.VarChar(20)
  ip         String?   @db.VarChar(15)
  socialClub String?   @db.VarChar(16)
  date       DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([date], map: "date")
  @@index([ip], map: "ip")
  @@index([socialClub], map: "socialClub")
  @@index([userId], map: "userId")
}

model mysql_migrations_347ertt3e {
  timestamp String @unique(map: "timestamp") @db.VarChar(254)
}

// checked
model NameLog {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId Int?      @db.UnsignedMediumInt
  oldName   String?   @db.VarChar(24)
  newName   String?   @db.VarChar(24)
  date      DateTime? @db.DateTime(0)
  admin     Int?      @db.UnsignedMediumInt

  @@index([accountId], map: "accountId")
  @@index([admin], map: "admin")
  @@index([date], map: "date")
  @@index([newName], map: "newName")
  @@index([oldName], map: "oldName")
  @@map("name_logs")
}

model NewsQueue {
  id        Int       @id @default(autoincrement())
  creatorId Int?
  reportId  Int?
  factionId Int?
  message   String?   @db.Text
  status    Boolean?
  createdAt DateTime? @db.DateTime(0)
  updatedAt DateTime? @db.DateTime(0)
  deployAt  DateTime? @db.DateTime(0)

  @@map("news_queue")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model offices {
  id              Int       @id @default(autoincrement()) @db.UnsignedSmallInt
  buildingId      Int       @default(0) @db.UnsignedTinyInt
  familyId        Int?      @db.UnsignedSmallInt
  cash            BigInt?   @default(0) @db.UnsignedBigInt
  officeInterior  Boolean?  @default(false)
  officeMoneySafe Boolean?  @default(false)
  officeGunLocker Boolean?  @default(false)
  officeWardrobe  Boolean?  @default(false)
  officeWarehouse Boolean?  @default(false)
  heliport        Boolean?  @default(false)
  garageDecor     String?   @db.LongText
  garage          Int?      @db.UnsignedTinyInt
  paidUntil       DateTime? @db.DateTime(0)
  auctionId       Int?      @db.UnsignedInt

  @@index([auctionId], map: "auctionId")
  @@index([buildingId], map: "buildingId")
  @@index([familyId], map: "familyId")
  @@index([paidUntil], map: "paidUntil")
}

model online {
  id      BigInt @id @default(autoincrement()) @db.UnsignedBigInt
  players Int    @default(0) @db.UnsignedSmallInt
  date    BigInt @default(0) @db.UnsignedBigInt

  @@index([date], map: "date")
}

model online_log {
  id        BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  date      DateTime? @unique(map: "date") @db.Date
  maxOnline Int?      @default(0) @db.UnsignedSmallInt

  @@index([maxOnline], map: "maxOnline")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model phone_contacts {
  id        Int      @id @default(autoincrement()) @db.UnsignedMediumInt
  accountId Int?     @db.UnsignedMediumInt
  number    String?  @db.VarChar(8)
  name      String?  @db.VarChar(24)
  blocked   Boolean? @default(false)
  ringtone  Boolean? @default(false)

  @@index([accountId], map: "accountId")
  @@index([name], map: "name")
  @@index([number], map: "number")
}

model phone_favourite {
  id          Int     @id @default(autoincrement())
  callId      Int?
  accountId   Int?    @db.MediumInt
  phoneNumber String  @default("") @db.VarChar(8)
  socialClub  String? @db.VarChar(16)

  @@index([accountId], map: "accountId")
  @@index([phoneNumber], map: "phoneNumber")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model phone_history {
  id          Int                   @id @default(autoincrement()) @db.UnsignedInt
  accountFrom Int?                  @db.UnsignedMediumInt
  numberFrom  String?               @db.VarChar(8)
  accountTo   Int?                  @db.UnsignedMediumInt
  numberTo    String?               @db.VarChar(8)
  date        DateTime?             @db.DateTime(0)
  status      phone_history_status?
  statusView  Int?                  @default(0)

  @@index([accountFrom], map: "accountFrom")
  @@index([accountTo], map: "accountTo")
  @@index([date], map: "date")
  @@index([status], map: "status")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model phone_sms {
  id          Int                @id @default(autoincrement())
  accountFrom Int?               @db.MediumInt
  numberFrom  String?            @db.VarChar(24)
  accountTo   Int?               @db.MediumInt
  numberTo    String?            @db.VarChar(24)
  text        String?            @db.VarChar(512)
  photos      Bytes?             @db.Blob
  status      Boolean            @default(false)
  deleteBy    phone_sms_deleteBy @default(none)
  date        DateTime?          @db.DateTime(0)
  statusDate  DateTime?          @db.DateTime(0)

  @@index([accountFrom], map: "accountFrom")
  @@index([accountTo], map: "accountTo")
  @@index([date], map: "date")
  @@index([deleteBy], map: "deleteBy")
  @@index([numberFrom], map: "numberFrom")
  @@index([status], map: "status")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model phones {
  id            Int      @id @default(autoincrement()) @db.MediumInt
  accountId     Int?     @unique(map: "accountId") @db.UnsignedMediumInt
  number        String?  @unique(map: "number") @db.VarChar(8)
  brightness    Int?     @default(100) @db.TinyInt
  volume        Int?     @default(50) @db.TinyInt
  wallpaper     Int?     @default(0) @db.TinyInt
  sms_ringtone  Int?     @default(0) @db.TinyInt
  call_ringtone Int?     @default(0) @db.TinyInt
  dnd           Boolean? @default(false)
  balance       Int?     @default(1500000)

  @@index([balance], map: "balance")
}

// checked
model PlantWeedTime {
  hours Bytes @unique(length: 100) @db.Blob

  @@map("plant_weed_time")
}

model Promocode {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  userId    Int?      @db.UnsignedMediumInt
  invitedBy Int?      @db.UnsignedMediumInt
  media     Boolean?  @default(false)
  date      DateTime? @db.Date
  confirm   Boolean?  @default(false)

  @@index([confirm], map: "confirm")
  @@index([date], map: "date")
  @@index([invitedBy], map: "invitedBy")
  @@index([media], map: "media")
  @@index([userId], map: "userId")
  @@map("promocodes")
}

model property_log {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId  Int?      @db.UnsignedMediumInt
  tableName  String?   @db.VarChar(30)
  keyId      Int?      @db.UnsignedMediumInt
  cost       BigInt?   @default(0) @db.UnsignedBigInt
  accountId2 Int?      @db.UnsignedMediumInt
  comment    String?   @db.VarChar(100)
  date       DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([accountId2], map: "accountId2")
  @@index([date], map: "date")
  @@index([keyId], map: "key")
  @@index([tableName], map: "tableName")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model quests {
  id        Int           @id @default(autoincrement()) @db.UnsignedInt
  accountId Int?          @db.UnsignedMediumInt
  questId   Int?          @db.UnsignedSmallInt
  stageId   Int?          @db.UnsignedSmallInt
  progress  Int?          @db.UnsignedMediumInt
  state     quests_state? @default(active)
  date      DateTime?     @db.DateTime(0)
  cooldown  DateTime?     @db.DateTime(0)
  hasUse    Boolean?      @default(false)

  @@index([accountId], map: "accountId")
  @@index([questId], map: "questId")
  @@index([state], map: "ready")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model quizzes {
  id     Int       @id @default(autoincrement()) @db.UnsignedMediumInt
  userId Int?      @db.UnsignedMediumInt
  quizId Int       @db.MediumInt
  answer String?   @db.VarChar(50)
  date   DateTime? @db.DateTime(0)

  @@index([date], map: "date")
  @@index([quizId], map: "quizId")
  @@index([userId], map: "userId")
}

model ReportMessage {
  id          Int                   @id @default(autoincrement())
  reportId    Int?
  characterId Int?
  message     String?               @db.Text
  date        DateTime?             @db.DateTime(0)
  type        report_messages_type?
  assets      String?               @default("[]") @db.MediumText
  nickname    String?               @db.VarChar(50)
  socialClub  String?               @db.VarChar(50)

  @@map("report_messages")
}

// checked
model Report {
  id             Int                     @id @default(autoincrement())
  characterId    Int?                    @db.MediumInt
  text           String?                 @db.Text
  type           reports_type?
  status         reports_status?         @default(new)
  date           DateTime?               @db.Date
  mark           Boolean?
  admin          Int?                    @db.MediumInt
  author         String?                 @db.VarChar(40)
  familyLogoType reports_familyLogoType?
  exceptionId    Int?
  selected       Int?
  firstAnswerAt  DateTime?               @db.Date
  lastAnswerAt   DateTime?               @db.Date
  lastQuestionAt DateTime?               @db.Date
  closedAt       DateTime?               @db.Date

  @@index([characterId], map: "characterId")
  @@index([date], map: "date")
  @@index([status], map: "status")
  @@index([type], map: "type")
  @@map("reports")
}

model roulette_items {
  id           String                 @id @db.VarChar(36)
  accountId    Int?                   @db.UnsignedMediumInt
  userId       Int?                   @db.UnsignedMediumInt
  value        String?                @db.VarChar(35)
  price        Int?                   @db.UnsignedMediumInt
  type         String?                @db.VarChar(20)
  color        String?                @db.VarChar(10)
  seasonPassId Int?                   @db.UnsignedTinyInt
  clothesId    Int?                   @db.UnsignedInt
  args         String?                @db.LongText
  date         DateTime?              @db.DateTime(0)
  status       roulette_items_status? @default(active)
  actionDate   DateTime?              @db.DateTime(0)
  caseId       String?                @db.VarChar(50)

  @@index([accountId], map: "accountId")
  @@index([actionDate], map: "actionDate")
  @@index([clothesId], map: "clothesId")
  @@index([color], map: "color")
  @@index([date], map: "date")
  @@index([price], map: "price")
  @@index([seasonPassId], map: "seasonPassId")
  @@index([status], map: "status")
  @@index([type], map: "type")
  @@index([userId], map: "userId")
  @@index([value], map: "value")
}

model season_pass {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId Int?      @db.UnsignedInt
  userId    Int?      @unique(map: "userId") @db.UnsignedInt
  level     Int       @default(0) @db.UnsignedSmallInt
  xp        Int       @default(0) @db.UnsignedMediumInt
  tasks     Int       @default(0) @db.UnsignedMediumInt
  rating    Int       @default(0) @db.UnsignedMediumInt
  premium   Int       @default(0) @db.UnsignedTinyInt
  dateStart DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([dateStart], map: "dateStart")
  @@index([premium], map: "premium")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model season_pass_exp_log {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  userId    Int?      @db.UnsignedMediumInt
  accountId Int?      @db.UnsignedMediumInt
  amount    Int?      @db.UnsignedMediumInt
  comment   String?   @db.VarChar(30)
  date      DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([comment], map: "comment")
  @@index([date], map: "date")
  @@index([userId], map: "userId")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model season_pass_items {
  id            Int                          @id @default(autoincrement())
  userId        Int                          @default(0) @db.MediumInt
  awardIndex    Int                          @default(0) @db.SmallInt
  awardSubIndex Int                          @default(0) @db.SmallInt
  variantIndex  Int?                         @db.TinyInt
  levelType     season_pass_items_levelType  @default(free)
  claimType     season_pass_items_claimType?
  date          DateTime?                    @db.DateTime(0)

  @@index([date], map: "date")
  @@index([userId], map: "userId")
}

model season_pass_last {
  userId Int  @id @db.UnsignedInt
  level  Int? @db.UnsignedTinyInt
}

model season_pass_payday {
  id     Int  @id @default(autoincrement()) @db.UnsignedInt
  userId Int? @db.UnsignedMediumInt

  @@index([userId], map: "userId")
}

model season_pass_tasks {
  id         String   @id @default("") @db.VarChar(36)
  userId     Int      @default(0) @db.MediumInt
  taskId     Int      @db.SmallInt
  progress   Int      @default(0) @db.MediumInt
  isComplete Boolean? @default(false)

  @@index([userId], map: "userId")
}

model SendCommandLog {
  id        Int                      @id @default(autoincrement())
  server    String?                  @db.VarChar(10)
  login     String?                  @db.VarChar(20)
  accountId Int?
  command   String?                  @db.VarChar(15)
  args      Bytes?                   @db.Blob
  status    send_command_log_status?
  date      DateTime?                @db.DateTime(0)

  @@map("send_command_log")
}

model server_settings {
  id      Int     @id @default(autoincrement()) @db.UnsignedInt
  keyName String? @db.VarChar(50)
  value   Int?

  @@index([keyName], map: "key")
}

// checked
model SessionLog {
  id         Int                   @id @default(autoincrement())
  userId     Int?                  @db.UnsignedMediumInt
  accountId  Int?                  @db.UnsignedMediumInt
  enterDate  DateTime?             @db.DateTime(0)
  exitDate   DateTime?             @db.DateTime(0)
  exitReason String?               @db.VarChar(40)
  hwid       String?               @db.VarChar(128)
  socialClub String?               @db.VarChar(16)
  rgscId     Int?                  @db.UnsignedInt
  ip         String?               @db.VarChar(15)
  security   session_log_security?

  @@index([accountId], map: "accountId")
  @@index([enterDate], map: "enterDate")
  @@index([exitDate], map: "exitDate")
  @@index([exitReason], map: "exitReason")
  @@index([hwid], map: "hwid")
  @@index([ip], map: "ip")
  @@index([rgscId], map: "rgscId")
  @@index([socialClub], map: "socialClub")
  @@map("session_log")
}

model skills {
  id        Int     @id @default(autoincrement()) @db.UnsignedInt
  accountId Int?    @db.UnsignedMediumInt
  key       String? @db.VarChar(20)
  value     Int?    @db.UnsignedMediumInt

  @@unique([accountId, key], map: "uni")
  @@index([accountId], map: "accountId")
  @@index([key], map: "key")
}

// TODO: need to optimize
model Special {
  id              Int      @id @default(autoincrement()) @db.UnsignedSmallInt
  comment         String?  @db.VarChar(30)
  accountId       Int?     @unique(map: "accountId") @db.UnsignedMediumInt
  givepromo       Boolean? @default(false)
  f5              Boolean? @default(false)
  setname         Boolean? @default(false)
  veh             Boolean? @default(false)
  skin            Boolean? @default(false)
  gtp             Boolean? @default(false)
  mtp             Boolean? @default(false)
  aveh            Boolean? @default(false)
  mute            Boolean? @default(false)
  ajail           Boolean? @default(false)
  delveh          Boolean? @default(false)
  tp              Boolean? @default(false)
  gh              Boolean? @default(false)
  kick            Boolean? @default(false)
  setvip          Boolean? @default(false)
  gm              Boolean? @default(false)
  givecar         Boolean? @default(false)
  givedonate      Boolean? @default(false)
  givemoney       Boolean? @default(false)
  giveskill       Boolean? @default(false)
  setemail        Boolean? @default(false)
  givematerials   Boolean? @default(false)
  setmod          Boolean? @default(false)
  reset2fa        Boolean? @default(false)
  transfervehicle Boolean? @default(false)
  hp              Boolean? @default(false)
  rescue          Boolean? @default(false)
  ban             Boolean? @default(false)
  unban           Boolean? @default(false)
  toggleachat     Boolean? @default(false)
  esp             Boolean? @default(false)
  clear           Boolean? @default(false)
  advPartner      Boolean? @default(false)
  delitem         Boolean? @default(false)
  givecarmod      Boolean? @default(false)
  localtime       Boolean? @default(false)
  localweather    Boolean? @default(false)
  sendCommand     Int?     @db.TinyInt
  giveitem        Boolean? @default(false)
  setfamexp       Boolean? @default(false)
  setfamrep       Boolean? @default(false)
  setfamilyleader Boolean? @default(false)

  @@map("specials")
}

model supply_materials {
  id           Int                    @id @default(autoincrement()) @db.UnsignedInt
  fractionId   Int?                   @db.UnsignedSmallInt
  type         supply_materials_type?
  position     Int?                   @default(0) @db.UnsignedTinyInt
  amount       Int?                   @default(0) @db.UnsignedMediumInt
  rest         Int?                   @default(0) @db.UnsignedMediumInt
  finishedBy   Int?                   @db.MediumInt
  deliveryDate DateTime?              @db.DateTime(0)
  accountId    Int?                   @db.UnsignedMediumInt
  date         DateTime?              @db.DateTime(0)

  @@index([date], map: "date")
  @@index([deliveryDate], map: "deliveryDate")
  @@index([fractionId], map: "fractionId")
  @@index([type], map: "type")
}

model tasks {
  autoId Int     @unique(map: "autoId") @default(autoincrement())
  userId Int     @default(0)
  type   Int     @default(0) @db.TinyInt
  name   String? @db.VarChar(36)
  value  BigInt  @default(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model tattoo_shops {
  id             Int       @id @default(autoincrement()) @db.UnsignedTinyInt
  title          String?   @db.VarChar(24)
  accountId      Int?      @db.UnsignedMediumInt
  price          Int?      @default(5000000) @db.UnsignedInt
  cash           BigInt?   @default(0) @db.UnsignedBigInt
  bank           BigInt?   @default(0) @db.UnsignedBigInt
  posX           String?   @db.VarChar(10)
  posY           String?   @db.VarChar(10)
  posZ           String?   @db.VarChar(10)
  playerPosX     String?   @db.VarChar(10)
  playerPosY     String?   @db.VarChar(10)
  playerPosZ     String?   @db.VarChar(10)
  playerPosA     String?   @db.VarChar(10)
  materials      Int?      @default(5000) @db.UnsignedMediumInt
  prices         Bytes?    @db.Blob
  discounts      Bytes?    @db.Blob
  mafiaId        Int?      @default(0) @db.UnsignedTinyInt
  mafiaCash      BigInt?   @default(0) @db.UnsignedBigInt
  defaultMafiaId Int?      @default(0) @db.UnsignedTinyInt
  paidUntil      DateTime? @db.DateTime(0)
  auctionId      Int?      @db.UnsignedInt
  marketplaceId  Int?      @db.UnsignedInt

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([paidUntil], map: "paidUntil")
  @@index([title], map: "title")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model taxes {
  category    String? @unique(map: "category") @db.VarChar(20)
  tax         String? @default("2.0") @db.VarChar(4)
  monthProfit BigInt? @default(0)
  monthTaxes  BigInt? @default(0)

  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model team_log {
  id     Int       @id @default(autoincrement()) @db.UnsignedInt
  userId Int?      @db.UnsignedMediumInt
  date   DateTime? @db.DateTime(0)
  event  String?   @db.VarChar(10)
  param  Boolean?
  reason String?   @db.VarChar(24)

  @@index([date], map: "date")
  @@index([event], map: "event")
  @@index([param], map: "param")
  @@index([userId], map: "userId")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model tickets {
  id Int?

  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model tickets_logs {
  id           Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId    Int?      @db.UnsignedMediumInt
  amount       Int?      @default(0) @db.UnsignedInt
  ticketBy     Int?      @db.UnsignedMediumInt
  employeeOrg  Int?      @db.UnsignedTinyInt
  employeeRank Int?      @db.UnsignedTinyInt
  comment      String?   @db.VarChar(50)
  date         DateTime? @db.DateTime(0)
  active       Boolean?  @default(true)

  @@index([accountId], map: "accountId")
  @@index([active], map: "active")
  @@index([amount], map: "amount")
  @@index([date], map: "date")
  @@index([ticketBy], map: "wantedBy")
}

model timers {
  id     String    @id @db.VarChar(36)
  name   String?   @db.VarChar(50)
  ms     Int?      @db.MediumInt
  player Int?      @db.MediumInt
  start  DateTime? @db.DateTime(0)
  end    DateTime? @db.DateTime(0)

  @@index([end], map: "end")
  @@index([ms], map: "ms")
  @@index([name], map: "name")
  @@index([player], map: "player")
  @@index([start], map: "start")
}

model Token {
  id          Int      @id @default(autoincrement())
  accountId   Int?
  characterId Int?
  ip          String?  @db.VarChar(50)
  lastIp      String?  @db.VarChar(50)
  type        String?  @default("website") @db.VarChar(15)
  token       String   @unique(map: "tokens_token") @db.VarChar(512)
  createdAt   DateTime @db.Date
  updatedAt   DateTime @db.Date

  @@index([type, accountId, ip], map: "tokens_type_account_id_ip")
  @@map("tokens")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model tuning {
  id       Int     @id @default(autoincrement())
  vehicle  Int?    @db.UnsignedMediumInt
  modType  Int?    @db.UnsignedTinyInt
  modIndex String? @db.VarChar(7)

  @@index([modType], map: "modType")
  @@index([vehicle], map: "vehicle")
}

model tuning_shops {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  title          String?   @db.VarChar(24)
  accountId      Int?      @db.UnsignedMediumInt
  price          Int?      @default(5000000) @db.UnsignedInt
  cash           BigInt?   @default(0) @db.UnsignedBigInt
  bank           BigInt?   @default(0) @db.UnsignedBigInt
  posX           String?   @db.VarChar(10)
  posY           String?   @db.VarChar(10)
  posZ           String?   @db.VarChar(10)
  materials      Int?      @default(5000) @db.UnsignedMediumInt
  prices         Bytes?    @db.Blob
  mafiaId        Int?      @default(0) @db.UnsignedTinyInt
  mafiaCash      BigInt?   @default(0) @db.UnsignedBigInt
  defaultMafiaId Int?      @default(0) @db.UnsignedTinyInt
  paidUntil      DateTime? @db.DateTime(0)
  auctionId      Int?      @db.UnsignedInt
  marketplaceId  Int?      @db.UnsignedInt

  @@index([accountId], map: "accountId")
  @@index([auctionId], map: "auctionId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([paidUntil], map: "paidUntil")
  @@index([title], map: "title")
}

model two_factor_codes {
  id        BigInt  @id @default(autoincrement()) @db.UnsignedBigInt
  userId    Int?    @db.UnsignedInt
  code      String? @db.VarChar(8)
  activated Boolean @default(false)

  @@index([userId], map: "accountId")
  @@index([activated], map: "activated")
  @@index([code], map: "code")
}

model UnitpayPayment {
  id           Int                      @id @default(autoincrement())
  unitpayId    String?                  @unique(map: "unitpayId") @db.VarChar(50)
  account      String?                  @db.VarChar(25)
  payAmount    Float?                   @db.Float
  sum          Float?                   @db.Float
  additional   Int?                     @default(0) @db.UnsignedMediumInt
  profit       String?                  @db.VarChar(10)
  dateCreate   DateTime?                @db.DateTime(0)
  dateComplete DateTime?                @db.DateTime(0)
  status       Int                      @db.UnsignedTinyInt
  multiplier   Int                      @default(2) @db.UnsignedTinyInt
  comment      String                   @default("Unitpay") @db.VarChar(50)
  args         String?                  @db.LongText
  source       unitpay_payments_source? @default(site)
  method       String?                  @db.VarChar(15)
  shop         String?                  @db.VarChar(20)
  currency     String?                  @default("RUB") @db.VarChar(4)

  @@index([account], map: "account")
  @@index([comment], map: "comment")
  @@index([currency], map: "currency")
  @@index([dateComplete], map: "dateComplete")
  @@index([payAmount], map: "payAmount")
  @@index([profit], map: "profit")
  @@index([method], map: "method")
  @@index([shop], map: "shop")
  @@index([source], map: "source")
  @@index([status], map: "status")
  @@index([sum], map: "sum")
  @@map("unitpay_payments")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model user_logs {
  id       Int       @id @default(autoincrement())
  userId   Int
  newValue String?   @db.VarChar(128)
  oldValue String?   @db.VarChar(128)
  comment  String?   @db.VarChar(64)
  date     DateTime? @db.DateTime(0)

  @@index([date], map: "date")
  @@index([userId], map: "userId")
}

// checked
model Account {
  id                   Int       @id @default(autoincrement()) @db.UnsignedMediumInt
  userId               Int?      @db.UnsignedMediumInt
  login                String?   @unique(map: "login") @db.VarChar(20)
  admin                Int?      @default(0)
  tester               Boolean?  @default(false)
  level                Int?      @default(1) @db.UnsignedTinyInt
  exp                  Int?      @default(0) @db.UnsignedSmallInt
  gender               Boolean?
  nationality          Boolean?  @default(false)
  age                  Int?      @default(18) @db.UnsignedTinyInt
  hp                   Int?      @default(100) @db.UnsignedTinyInt
  cash                 BigInt?   @default(350000) @db.UnsignedBigInt
  chips                BigInt?   @default(0) @db.UnsignedBigInt
  casinoWin            BigInt?   @default(0) @db.UnsignedBigInt
  job                  Int?      @default(0) @db.UnsignedTinyInt
  member               Int?      @default(0) @db.UnsignedTinyInt
  leader               Int?      @default(0) @db.UnsignedTinyInt
  fractionRankId       Int?      @db.UnsignedMediumInt
  family               Int?      @default(0) @db.UnsignedMediumInt
  familyRankId         Int?      @db.UnsignedMediumInt
  fwarns               Int?      @default(0) @db.UnsignedTinyInt
  famwarns             Int?      @default(0) @db.UnsignedTinyInt
  createdAt            DateTime? @db.DateTime(0)
  lastVisit            DateTime? @db.DateTime(0)
  online               Int?      @default(0) @db.UnsignedTinyInt
  dayOnline            Int?      @default(0) @db.UnsignedMediumInt
  weekOnline           Int?      @default(0) @db.UnsignedMediumInt
  monthOnline          Int?      @default(0) @db.UnsignedMediumInt
  totalOnline          Int?      @default(0) @db.UnsignedInt
  contestOnline        Int?      @db.UnsignedMediumInt
  createdIP            String?   @db.VarChar(16)
  lastIP               String?   @db.VarChar(16)
  vip                  Int?      @db.TinyInt
  vipDate              DateTime? @db.DateTime(0)
  married              Int?      @db.UnsignedMediumInt
  jailTotalTime        Int?      @default(0) @db.UnsignedMediumInt
  wantedLevel          Boolean?  @default(false)
  payday               Int?      @default(60) @db.UnsignedTinyInt
  crimes               Int?      @default(0) @db.UnsignedSmallInt
  arrests              Int?      @default(0) @db.UnsignedSmallInt
  criminalRecords      Int?      @default(0) @db.UnsignedSmallInt
  kills                Int?      @default(0) @db.UnsignedSmallInt
  deaths               Int?      @default(0) @db.UnsignedSmallInt
  warns                Boolean?  @default(false)
  warnStats            Int?      @default(0) @db.UnsignedSmallInt
  muted                Int?      @default(0) @db.UnsignedSmallInt
  hunger               Int?      @default(50) @db.TinyInt
  water                Int?      @default(50) @db.TinyInt
  marketplaceRating    Float?    @default(0) @db.Float
  cooldowns            Bytes?    @db.Blob
  buffs                Bytes?    @db.Blob
  achievements         Bytes?    @db.Blob
  rentCar              Bytes?    @db.Blob
  tempJobInfo          Bytes?    @db.Blob
  tempMoney            Bytes?    @db.Blob
  lic                  Bytes?    @db.Blob
  customization        Bytes?    @db.Blob
  settings             Bytes?    @db.Blob
  bindings             Bytes?    @db.Blob
  tattoos              Bytes?    @db.Blob
  availableTattoos     Bytes?    @db.Blob
  equipedSkins         Bytes?    @db.Blob
  statTracks           Bytes?    @db.Blob
  exitPosition         Bytes?    @db.Blob
  fastSlots            Bytes?    @db.Blob
  jail                 Bytes?    @db.Blob
  marketplaceFavorites Bytes?    @db.Blob
  houseId              Int?      @db.UnsignedSmallInt
  rentHouseId          Int?      @db.UnsignedSmallInt
  apartmentId          Int?      @db.UnsignedSmallInt
  rentApartmentId      Int?      @db.UnsignedSmallInt
  biz                  String?   @db.VarChar(20)
  infected             Boolean?  @default(false)
  carSlots             Boolean?  @default(false)
  contractsData        Bytes?    @default(dbgenerated("('{}')")) @db.Blob
  weaponsBlock         Int?
  halloweenQrCodes     Bytes?    @default(dbgenerated("('[]')")) @db.Blob
  xmasPropsSearch      Bytes?    @default(dbgenerated("('[]')")) @db.Blob

  @@index([admin], map: "admin")
  @@index([age], map: "age")
  @@index([apartmentId], map: "apartmentId")
  @@index([biz], map: "biz")
  @@index([cash], map: "cash")
  @@index([chips], map: "chips")
  @@index([createdAt], map: "createdAt")
  @@index([createdIP], map: "createdIP")
  @@index([dayOnline], map: "dayOnline")
  @@index([family], map: "family")
  @@index([gender], map: "gender")
  @@index([houseId], map: "houseId")
  @@index([job], map: "job")
  @@index([lastIP], map: "lastIP")
  @@index([lastVisit], map: "lastVisit")
  @@index([leader], map: "leader")
  @@index([level], map: "level")
  @@index([married], map: "married")
  @@index([member], map: "member")
  @@index([monthOnline], map: "monthOnline")
  @@index([nationality], map: "nationality")
  @@index([online], map: "online")
  @@index([contestOnline], map: "contestOnline")
  @@index([rentApartmentId], map: "rentApartmentId")
  @@index([rentHouseId], map: "rentHouseId")
  @@index([totalOnline], map: "totalOnline")
  @@index([userId], map: "userId")
  @@index([vip], map: "vip")
  @@index([weekOnline], map: "weekOnline")
  @@map("accounts")
}

model vehicle_sets {
  id      Int  @id @default(autoincrement()) @db.UnsignedInt
  vehicle Int? @db.UnsignedInt
  setId   Int? @db.UnsignedTinyInt

  @@index([setId], map: "setId")
  @@index([vehicle], map: "vehicle")
}

// checked
model Vehicle {
  id               Int       @id @default(autoincrement())
  owner            Int?      @db.UnsignedMediumInt
  model            String?   @db.VarChar(24)
  number           String?   @default("MAJESTIC") @db.VarChar(50)
  posX             String?   @db.VarChar(10)
  posY             String?   @db.VarChar(10)
  posZ             String?   @db.VarChar(10)
  rotX             String?   @db.VarChar(10)
  rotY             String?   @db.VarChar(10)
  rotZ             String?   @db.VarChar(10)
  park             Int?      @db.UnsignedInt
  seasonPassId     Int?      @db.UnsignedTinyInt
  locked           Boolean   @default(false)
  gas              Int       @default(10000) @db.UnsignedMediumInt
  mileage          BigInt    @default(0) @db.UnsignedBigInt
  dirt             Int       @default(0) @db.UnsignedSmallInt
  color1           Int       @default(0) @db.UnsignedTinyInt
  color2           Int       @default(0) @db.UnsignedTinyInt
  color3           Int       @default(0) @db.UnsignedTinyInt
  health           Int       @default(1000) @db.UnsignedSmallInt
  brokenLock       Boolean   @default(false)
  wearEngine       Int       @default(0) @db.UnsignedMediumInt
  wearSuspension   Int       @default(0) @db.UnsignedMediumInt
  wearCooling      Int       @default(0) @db.UnsignedMediumInt
  wearBrakes       Int       @default(0) @db.UnsignedMediumInt
  wearTransmission Int       @default(0) @db.UnsignedMediumInt
  wearFuel         Int       @default(0) @db.UnsignedMediumInt
  wearExhaust      Int       @default(0) @db.UnsignedMediumInt
  wearWheels       Int       @default(0) @db.UnsignedMediumInt
  wearBattery      Int       @default(0) @db.UnsignedMediumInt
  wearOil          Int       @default(0) @db.UnsignedMediumInt
  endTime          DateTime? @db.DateTime(0)
  date             DateTime? @db.DateTime(0)
  rentPlaceId      Int?      @db.UnsignedTinyInt
  auctionId        Int?      @db.UnsignedInt
  marketplaceId    Int?      @db.UnsignedInt
  familyId         Int?      @db.UnsignedInt
  rentBy           Int?      @db.UnsignedMediumInt
  rentUntil        DateTime? @db.DateTime(0)
  impound          Bytes?    @db.Blob
  familyCarriage   Boolean?
  registerItemId   Int?
  startPrice       Int?      @default(0)
  args             String?   @db.LongText

  @@index([auctionId], map: "auctionId")
  @@index([date], map: "date")
  @@index([endTime], map: "endTime")
  @@index([familyId], map: "familyId")
  @@index([marketplaceId], map: "marketplaceId")
  @@index([owner, model, number, park], map: "owner")
  @@index([rentBy], map: "rentBy")
  @@index([rentUntil], map: "rentUntil")
  @@index([seasonPassId], map: "seasonPassId")
  @@map("vehicles")
}

model walkietalkie {
  id        Int      @id @default(autoincrement())
  accountId Int?     @db.UnsignedMediumInt
  enabled   Boolean? @default(false)
  frequency Int?     @db.UnsignedMediumInt

  @@index([accountId], map: "accountId")
}

model wanted_log {
  id           Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId    Int?      @db.UnsignedMediumInt
  level        Boolean?
  wantedBy     Int?      @db.UnsignedMediumInt
  employeeOrg  Int?      @db.UnsignedTinyInt
  employeeRank Int?      @db.UnsignedTinyInt
  comment      String?   @db.VarChar(50)
  startAt      DateTime? @db.DateTime(0)
  endAt        DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([endAt], map: "endAt")
  @@index([level], map: "level")
  @@index([startAt], map: "startAt")
  @@index([wantedBy], map: "wantedBy")
}

model warehouses {
  id         Int       @id @default(autoincrement()) @db.UnsignedSmallInt
  buildingId Int       @default(0) @db.UnsignedTinyInt
  familyId   Int?      @db.UnsignedSmallInt
  paidUntil  DateTime? @db.DateTime(0)
  auctionId  Int?      @db.UnsignedInt

  @@index([auctionId], map: "auctionId")
  @@index([buildingId], map: "buildingId")
  @@index([familyId], map: "familyId")
  @@index([paidUntil], map: "paidUntil")
}

model wheel_bets {
  id         Int      @id @default(autoincrement())
  userId     Int
  accountId  Int
  amount     Int
  color      String   @db.VarChar(18)
  gameId     Int
  dateBet    DateTime @default(now()) @db.DateTime(0)
  socialClub String?  @db.VarChar(16)

  @@index([dateBet], map: "dateBet")
  @@index([gameId], map: "gameId")
  @@index([userId], map: "userId")
}

model wheel_games {
  id           Int      @id @default(autoincrement())
  playersCount Int
  white        Int
  red          Int
  green        Int
  yellow       Int
  bank         Int
  colorWin     String   @db.VarChar(18)
  won          Int
  lost         Int
  dateGame     DateTime @default(now()) @db.DateTime(0)

  @@index([dateGame], map: "dateGame")
}

model WhiteList {
  id         Int     @id @default(autoincrement())
  socialClub String  @unique @default("") @db.VarChar(16)
  comment    String? @db.VarChar(50)
  donater    Int?    @default(0) @db.TinyInt

  @@index([donater], map: "donater")
  @@index([socialClub], map: "socialClub")
  @@map("whitelist")
}

model AdminWhiteList {
  id         Int     @id @default(autoincrement())
  socialClub String  @unique @default("") @db.VarChar(16)
  hwid       String? @default("") @db.VarChar(128)

  @@index([socialClub], map: "socialClub")
  @@map("admin_whitelist")
}

model world_quests {
  id       Int  @id @default(autoincrement())
  questId  Int? @db.SmallInt
  progress Int? @default(0) @db.UnsignedInt

  @@index([questId], map: "questId")
}

model world_quests_players {
  id           Int  @id @default(autoincrement())
  accountId    Int?
  worldQuestId Int? @db.MediumInt
  progress     Int  @default(0)

  @@index([accountId], map: "accountId")
  @@index([worldQuestId], map: "worldQuestId")
}

model xmas_claimed_presents {
  id        Int @id @default(autoincrement())
  userId    Int @db.UnsignedInt
  presentId Int @db.UnsignedTinyInt

  @@index([userId], map: "userId")
}

model anticheat_triggers {
  id        BigInt    @id @default(autoincrement())
  accountId Int       @db.UnsignedMediumInt
  triggerId Int
  args      String?   @db.LongText
  date      DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([triggerId], map: "triggerId")
}

model buffs {
  id        Int       @id @default(autoincrement())
  accountId Int?
  buff      String    @default("") @db.VarChar(50)
  endTime   DateTime? @db.DateTime(0)
  dispelled Int?      @db.UnsignedTinyInt
  stack     Int?
  duration  BigInt?

  @@index([accountId], map: "accountId")
  @@index([buff], map: "buff")
}

model custom_wheels {
  id      Int       @id @default(autoincrement()) @db.UnsignedInt
  userId  Int?      @db.UnsignedMediumInt
  wheelId Int?      @db.UnsignedSmallInt
  status  String?   @db.VarChar(10)
  date    DateTime? @db.DateTime(0)
  usedAt  DateTime? @db.DateTime(0)

  @@index([date], map: "date")
  @@index([status], map: "status")
  @@index([usedAt], map: "usedAt")
  @@index([userId], map: "userId")
  @@index([wheelId], map: "wheelId")
}

model forbes_year_wealth {
  type   String @unique(map: "type") @db.VarChar(6)
  wealth Bytes? @db.Blob
}

model minigames_f2_log {
  id        Int      @id @default(autoincrement())
  userId    Int      @default(0)
  accountId Int      @default(0)
  game      String   @default("0") @db.VarChar(10)
  type      String   @default("") @db.VarChar(100)
  gameId    Int?     @default(0)
  amount    Int      @default(0)
  args      String?  @db.LongText
  date      DateTime @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([date], map: "date")
  @@index([game], map: "game")
  @@index([gameId], map: "gameId")
  @@index([type], map: "type")
  @@index([userId], map: "userId")
}

model season_pass_4 {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  accountId Int?      @db.UnsignedInt
  userId    Int?      @unique(map: "userId") @db.UnsignedInt
  level     Int       @default(0) @db.UnsignedSmallInt
  xp        Int       @default(0) @db.UnsignedMediumInt
  tasks     Int       @default(0) @db.UnsignedMediumInt
  premium   Int       @default(0) @db.UnsignedTinyInt
  dateStart DateTime? @db.DateTime(0)

  @@index([accountId], map: "accountId")
  @@index([dateStart], map: "dateStart")
  @@index([premium], map: "premium")
}

model university {
  id        Int                @id @default(autoincrement())
  accountId Int
  faculty   university_faculty
  status    university_status  @default(progress)
  stage     Int                @default(0)
  startDate DateTime           @db.DateTime(0)
  endDate   DateTime?          @db.DateTime(0)
}

model vehicle_fines {
  id             Int       @id @default(autoincrement())
  userId         Int
  vehId          Int?
  vehModel       String    @default("") @db.VarChar(50)
  vehNumberplate String    @default("") @db.VarChar(50)
  comment        String?   @db.LongText
  amount         Int
  date           DateTime? @db.DateTime(0)
  byPlayer       Int?
  status         Int?
  org            Int?
  url            String?   @db.LongText
  byCamera       Int?      @db.TinyInt
}

model wardrobe_kits {
  id         Int      @id @default(autoincrement())
  name       String?  @db.Text
  accountId  Int?
  fractionId Int?     @db.TinyInt
  items      Bytes?   @default(dbgenerated("('[]')")) @db.TinyBlob
  rankId     Int?
  gender     Boolean? @db.Bit(1)

  @@index([accountId], map: "accountId")
  @@index([fractionId], map: "fraction")
}

model wardrobe_kits_favorites {
  id        Int  @unique(map: "id")
  accountId Int? @db.MediumInt
  state     Int? @db.TinyInt

  @@index([accountId], map: "accountId")
}

model world_cameras {
  id          Int      @id @default(autoincrement())
  zone        String?  @unique(map: "zone") @db.VarChar(50)
  destroyed   Boolean?
  finesAmount Int?
}

model DeathLog {
  id              Int      @id @default(autoincrement())
  killerLogin     String?  @db.VarChar(20)
  killerAccountId Int?     @db.MediumInt
  weapon          String?  @db.VarChar(50)
  killedLogin     String?  @db.VarChar(20)
  killedAccountId Int?     @db.MediumInt
  date            DateTime @default(now()) @db.DateTime(0)

  @@index([date], map: "date")
  @@index([killedAccountId], map: "killedAccountId")
  @@index([killedLogin], map: "killedLogin")
  @@index([killerAccountId], map: "killerAccountId")
  @@index([killerLogin], map: "killerLogin")
  @@map("death_log")
}

enum university_faculty {
  medicine
  engineering
  cooking
  physycal
  jurisprudence
  languages_spanish  @map("languages-spanish")
  languages_japanese @map("languages-japanese")
  languages_russian  @map("languages-russian")
  languages_italian  @map("languages-italian")
}

enum university_status {
  progress
  completed
}

enum items_position {
  player
  trunk
  ftrunk
  glove
  fglove
  house
  drop
  item
  clothes
  garbage
  warehouse
  family_warehouse
  apartment
  fridge
  afridge
  wallet
  roulette
  bag
  marketplace
  modifications
  warehouseJobs
  warehouseJobs_Oil
  warehouseJobs_Drug
  warehouseJobs_Fish
  warehouseJobs_Mushrm
  warehouseJobs_Lumberjack
  warehouseJobs_Miner
  orgStash
  orgCompany
  office_gunlocker
  office_wardrobe
  warehouse_mansion
  vehicle_numberplate
  keychain
  office_warehouse
  university_book_storage
}

enum season_pass_items_claimType {
  xp
  take
}

enum session_log_security {
  fa              @map("2fa")
  telegram
  email
  faSuccess       @map("2faSuccess")
  telegramSuccess
  emailSuccess
}

enum bk_stats_period {
  day
  month
  total
}

enum key_binds_type {
  vehicle
  warehouse
  apartment
}

enum garages_type {
  houses
  apartments
  offices
}

enum animations_type {
  circle
  favourite
}

enum supply_materials_type {
  green
  red
  blue
}

enum bk_ratings_period {
  day
  month
  total
}

enum reports_type {
  default
  billboard
  family_logo
  gov_news
}

enum bank_tariff {
  Standart
  Premium
  VIP
}

enum bk_ratings_type {
  bet
  profit
}

enum reports_status {
  new
  closed
  reopened
  billboard
  billboard_accepted
  billboard_declined
  family_logo
  family_logo_accepted
  family_logo_declined
  gov_news
  gov_news_accepted
  gov_news_declined
}

enum families_theme {
  black
  white
}

enum fractions_theme {
  black
  white
}

enum quests_state {
  active
  success
  failed
  cancel
}

enum season_pass_items_levelType {
  free
  premium
}

enum bk_bets_status {
  new
  win
  lose
  return
  error
}

enum report_messages_type {
  answer
  question
  notification
}

enum send_command_log_status {
  error
  pending
  success
}

enum phone_history_status {
  offline
  missed
  success
  waiting
}

enum bk_bets_old_status {
  new
  done
  return
  error
}

enum phone_sms_deleteBy {
  none
  sender
  recipient
}

enum reports_familyLogoType {
  png
  jpg
  jpeg
}

enum items_logs_source {
  money_log
  donate_log
  fraction
  quest
  admin
}

enum roulette_items_status {
  active
  sold
  taken
}

enum unitpay_payments_source {
  site
  game
  seasonPassBronze
  seasonPassSilver
  seasonPassGold
  seasonPassPlatinum
  seasonPassPremium
  seasonPassKing
  seasonPassExp
  starterBronze
  starterGold
  starterPlatinum
  premiumSubscription
  premiumSubscriptionRenew
}

enum families_logoType {
  png
  jpg
  jpeg
}

enum users_registerFrom {
  game
  website
  launcher
  de2
}
