import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import {
  PrismaClient as GameClient,
  Prisma as GamePrisma,
} from '@majestic-backend/prisma-client-game'
import {
  PrismaClient as BackendClient,
  <PERSON>rism<PERSON> as BackendPrisma,
} from '@majestic-backend/prisma-client-backend'
import { BackendModelEnum, backendModels } from './models/backend.models'
import { GameModelEnum, gameModels } from './models/game.models'
import { common } from '@majestic-backend/errors'
import { GameServersConfig, MySqlConfig, WebDatabaseConfig } from '@majestic-backend/config'

@Injectable()
export class PrismaService implements OnModuleInit {
  private CONNECTION_LIMIT = 100
  private CONNECTION_TIMEOUT = 100000
  private POOL_TIMEOUT = 30

  private _gameModelMaps = new Map<string, Map<number, any>>()
  private _gameInstances = new Map<string, GameClient>()
  private _backendModels = new Map<number, any>()
  private _backendInstance: BackendClient | null = null
  private logger = new Logger(PrismaService.name)

  constructor(
    private readonly gameServersConfig: GameServersConfig,
    private readonly webDatabaseConfig: WebDatabaseConfig,
  ) {}

  async onModuleInit() {
    await this._connectGameClients()
    await this._connectBackendClient()
  }

  private _dataSourcesGame(database: MySqlConfig): GamePrisma.PrismaClientOptions {
    return {
      datasources: {
        db: {
          url: `mysql://${database.USER}:${database.PASSWORD}@${database.HOST}:${database.PORT}/${database.DATABASE}?connection_limit=${this.CONNECTION_LIMIT}&connect_timeout=${this.CONNECTION_TIMEOUT}&pool_timeout=${this.POOL_TIMEOUT}`,
        },
      },
    }
  }

  public async getAllMetrics(serviceName: string) {
    const promises = []
    for (const [key, gameInstance] of this._gameInstances) {
      promises.push(
        this.getPrismaMetrics({ instanceName: key, instance: gameInstance, serviceName }),
      )
    }
    if (this._backendInstance) {
      promises.push(
        this.getPrismaMetrics({
          instanceName: 'web',
          instance: this._backendInstance,
          serviceName,
        }),
      )
    }
    const results = await Promise.allSettled(promises)

    return results
      .filter(res => res.status === 'fulfilled')
      .map(res => (res as PromiseFulfilledResult<string>).value)
      .reduce((acc, cur) => {
        acc += cur
        return acc
      }, '')
  }

  private async getPrismaMetrics({
    serviceName,
    instanceName,
    instance,
  }: {
    serviceName: string
    instanceName: string
    instance: GameClient | BackendClient
  }) {
    let metrics = ''
    try {
      metrics = await instance.$metrics.prometheus({
        globalLabels: { instance: instanceName, app: serviceName },
      })
    } catch (e) {
      this.logger.error(`Fail to load metrics on ${instanceName}`, e)
    }

    return metrics
  }

  private _dataSourcesBackend(database: WebDatabaseConfig): BackendPrisma.PrismaClientOptions {
    return {
      datasources: {
        db: {
          url: `mysql://${database.USER}:${database.PASSWORD}@${database.HOST}:${database.PORT}/${database.DATABASE}?connection_limit=${this.CONNECTION_LIMIT}&connect_timeout=${this.CONNECTION_TIMEOUT}&pool_timeout=${this.POOL_TIMEOUT}`,
        },
      },
    }
  }

  private async _connectGameClients() {
    const servers = this.gameServersConfig.gameServers

    for (const [server, conf] of servers) {
      const gameClient = new GameClient(this._dataSourcesGame(conf.mysql))

      await gameClient.$connect()
      this.logger.log(`Connected to game server ${server}`)

      const models = new Map<number, any>()

      for (const modelKey of Object.keys(gameModels)) {
        models.set(
          Number(modelKey),
          new gameModels[modelKey as unknown as GameModelEnum](gameClient),
        )
      }

      this._gameModelMaps.set(server, models)
      this._gameInstances.set(server, gameClient)
    }
  }

  private async _connectBackendClient() {
    const dataBase = this.webDatabaseConfig

    if (!dataBase) {
      throw new Error('No backend db config')
    }

    const connection = new BackendClient(this._dataSourcesBackend(dataBase))

    await connection.$connect()
    this.logger.log('Connected to backend server')

    const models = new Map<number, any>()

    for (const modelKey of Object.keys(backendModels)) {
      models.set(
        Number(modelKey),
        new backendModels[modelKey as unknown as keyof typeof backendModels](connection),
      )
    }

    this._backendModels = models
    this._backendInstance = connection
  }

  public getGameModel<T extends GameModelEnum>(
    serverId: string,
    modelName: T,
  ): InstanceType<(typeof gameModels)[T]> {
    const models = this._gameModelMaps.get(serverId)
    if (!models) {
      throw new Error(common.Errors.SERVER_NOT_FOUND)
    }

    const model = models.get(modelName)
    if (!model) {
      throw new Error('No model found')
    }

    return model
  }

  public getGameInstance(serverId: string) {
    const gameInstance = this._gameInstances.get(serverId)
    if (!gameInstance) {
      throw new Error(common.Errors.SERVER_NOT_FOUND)
    }

    return gameInstance
  }

  public getBackendModel<T extends BackendModelEnum>(
    modelName: T,
  ): InstanceType<(typeof backendModels)[T]> {
    const model = this._backendModels.get(modelName)

    if (!model) {
      throw new Error('No model found')
    }

    return model
  }

  public getBackendInstance() {
    return this._backendInstance
  }
}
