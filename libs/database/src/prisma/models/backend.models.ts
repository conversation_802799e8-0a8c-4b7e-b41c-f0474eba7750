import {
  GtaKeysModel,
  KeysPaymentsModel,
  VerifiedEmailsModel,
  ServerModel,
  ServerOnlineModel,
  TwitchStreamerModel,
  TelegramDashboardUsersModel,
  CrashGamesModel,
  CrashBetsModel,
  JackpotGamesModel,
  JackpotBetsModel,
  WheelBetsModel,
  WheelGamesModel,
  MiniGamesPlayersModel,
  MiniGamesGamesModel,
  PaymentWebhookLogsModel,
  ApiKeyModel,
} from './backend'
import { BkFixtureModel } from './backend/bkFixture.model'
import { BkFixtureBetboomModel } from './backend/bkFixtureBetboom.model'
import { FaqModel } from './backend/faq.model'
import { MiniGamesF2LogModel } from './backend/miniGamesF2Log.model'
import { RegisterCodeSlug } from './backend/registerCodeSlug.model'
import { RequestsLogModel } from './backend/requestsLog.model'
import { RightsAdminSiteModel } from './backend/rightsAdminSite.model'
import { RuleModel } from './backend/rule.model'
import { SendCommandLogModel } from './backend/sendCommandLog.model'
import { PixelBattleModel, PixelBattleSnapshotModel } from './backend'
export { IServerExtras } from './backend/server.model'

export enum BackendModelEnum {
  RegisterCodeSlug = 1,
  RightsAdminSiteModel,
  RuleModel,
  FaqModel,
  BkFixtureModel,
  BkFixtureBetboomModel,
  RequestsLogModel,
  SendCommandLogModel,
  GtaKeysModel,
  KeysPaymentsModel,
  TwitchStreamerModel,
  ServerModel,
  ServerOnlineModel,
  VerifiedEmailsModel,
  PixelBattleModel,
  PixelBattleSnapshotModel,
  TelegramDashboardUsersModel,
  CrashGamesModel,
  CrashBetsModel,
  JackpotGamesModel,
  JackpotBetsModel,
  WheelGamesModel,
  WheelBetsModel,
  MiniGamesF2LogModel,
  MiniGamesPlayersModel,
  MiniGamesGamesModel,
  PaymentWebhookLogsModel,
  ApiKeyModel,
}

export const backendModels = {
  [BackendModelEnum.RegisterCodeSlug]: RegisterCodeSlug,
  [BackendModelEnum.RightsAdminSiteModel]: RightsAdminSiteModel,
  [BackendModelEnum.RuleModel]: RuleModel,
  [BackendModelEnum.FaqModel]: FaqModel,
  [BackendModelEnum.BkFixtureModel]: BkFixtureModel,
  [BackendModelEnum.BkFixtureBetboomModel]: BkFixtureBetboomModel,
  [BackendModelEnum.RequestsLogModel]: RequestsLogModel,
  [BackendModelEnum.SendCommandLogModel]: SendCommandLogModel,
  [BackendModelEnum.GtaKeysModel]: GtaKeysModel,
  [BackendModelEnum.KeysPaymentsModel]: KeysPaymentsModel,
  [BackendModelEnum.TwitchStreamerModel]: TwitchStreamerModel,
  [BackendModelEnum.ServerModel]: ServerModel,
  [BackendModelEnum.ServerOnlineModel]: ServerOnlineModel,
  [BackendModelEnum.VerifiedEmailsModel]: VerifiedEmailsModel,
  [BackendModelEnum.PixelBattleModel]: PixelBattleModel,
  [BackendModelEnum.PixelBattleSnapshotModel]: PixelBattleSnapshotModel,
  [BackendModelEnum.TelegramDashboardUsersModel]: TelegramDashboardUsersModel,
  [BackendModelEnum.CrashGamesModel]: CrashGamesModel,
  [BackendModelEnum.CrashBetsModel]: CrashBetsModel,
  [BackendModelEnum.JackpotGamesModel]: JackpotGamesModel,
  [BackendModelEnum.JackpotBetsModel]: JackpotBetsModel,
  [BackendModelEnum.WheelGamesModel]: WheelGamesModel,
  [BackendModelEnum.WheelBetsModel]: WheelBetsModel,
  [BackendModelEnum.MiniGamesF2LogModel]: MiniGamesF2LogModel,
  [BackendModelEnum.MiniGamesPlayersModel]: MiniGamesPlayersModel,
  [BackendModelEnum.MiniGamesGamesModel]: MiniGamesGamesModel,
  [BackendModelEnum.PaymentWebhookLogsModel]: PaymentWebhookLogsModel,
  [BackendModelEnum.ApiKeyModel]: ApiKeyModel,
}
