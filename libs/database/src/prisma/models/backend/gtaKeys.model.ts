import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import GtaKeysFindFirstArgs = Prisma.GtaKeysFindFirstArgs
import GtaKeysUpdateManyArgs = Prisma.GtaKeysUpdateManyArgs
import GtaKeysUpdateInput = Prisma.GtaKeysUpdateInput
import { fixDatesInWrite, fixDatesInReadNullable, fixDatesInFindArgs } from '../../../utils'

export class GtaKeysModel {
  private dateFields: Array<keyof GtaKeysUpdateInput> = ['buyDate', 'createdDate', 'frozenUntil']

  constructor(private backendClient: BackendClient) {}

  public async getGtaKey(payload: GtaKeysFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.gtaKeys.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async updateGtaKey(payload: GtaKeysUpdateManyArgs) {
    return this.backendClient.gtaKeys.updateMany({
      ...fixDatesInFindArgs(payload, this.dateFields),
      data: fixDatesInWrite(payload.data, this.dateFields),
    })
  }
}
