import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import MiniGamesGamesUpdateInput = Prisma.MiniGamesGamesUpdateInput
import MiniGamesGamesFindManyArgs = Prisma.MiniGamesGamesFindManyArgs
import MiniGamesGamesFindFirstArgs = Prisma.MiniGamesGamesFindFirstArgs
import MiniGamesGamesCreateInput = Prisma.MiniGamesGamesCreateInput
import MiniGamesGamesUpdateArgs = Prisma.MiniGamesGamesUpdateArgs

import {
  fixDatesInFindArgs,
  fixDatesInRead,
  fixDatesInReadMany,
  fixDatesInReadNullable,
  fixDatesInWrite,
} from '../../../utils'

export class MiniGamesGamesModel {
  private dateFields: Array<keyof MiniGamesGamesUpdateInput> = []

  constructor(private backendClient: BackendClient) {}

  public async getMiniGamesGames(payload: MiniGamesGamesFindManyArgs) {
    return fixDatesInReadMany(
      await this.backendClient.miniGamesGames.findMany(
        fixDatesInFindArgs(payload, this.dateFields),
      ),
      this.dateFields,
    )
  }

  public async getMiniGamesGame(payload: MiniGamesGamesFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.miniGamesGames.findFirst(
        fixDatesInFindArgs(payload, this.dateFields),
      ),
      this.dateFields,
    )
  }

  public async createMiniGamesGame(data: MiniGamesGamesCreateInput) {
    return this.backendClient.miniGamesGames.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }

  public async updateMiniGamesGame(payload: MiniGamesGamesUpdateArgs) {
    return fixDatesInRead(
      await this.backendClient.miniGamesGames.update({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }
}
