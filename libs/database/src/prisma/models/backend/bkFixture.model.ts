import { Prisma, PrismaClient as BackendClient } from '@majestic-backend/prisma-client-backend'
import BkFixtureUpdateArgs = Prisma.BkFixtureUpdateArgs
import BkFixtureFindManyArgs = Prisma.BkFixtureFindManyArgs

export class BkFixtureModel {
  constructor(private backendClient: BackendClient) {}

  public updateBkFixture(payload: BkFixtureUpdateArgs) {
    return this.backendClient.bkFixture.update(payload)
  }

  public getBkFixtures(payload?: BkFixtureFindManyArgs) {
    return this.backendClient.bkFixture.findMany(payload)
  }
}
