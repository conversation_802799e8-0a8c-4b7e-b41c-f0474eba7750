import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import PaymentWebhookLogsCreateInput = Prisma.PaymentWebhookLogsCreateInput
import { fixDatesInRead, fixDatesInWrite } from '../../../utils'

export class PaymentWebhookLogsModel {
  private dateFields: Array<keyof PaymentWebhookLogsCreateInput> = ['createdAt']

  constructor(private backendClient: BackendClient) {}

  public async create(data: PaymentWebhookLogsCreateInput) {
    return fixDatesInRead(
      await this.backendClient.paymentWebhookLogs.create({
        data: fixDatesInWrite(data, this.dateFields),
      }),
      this.dateFields,
    )
  }
}
