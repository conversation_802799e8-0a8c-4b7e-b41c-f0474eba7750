import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import CrashGamesUpdateInput = Prisma.CrashGamesUpdateInput
import CrashGamesFindFirstArgs = Prisma.CrashGamesFindFirstArgs
import CrashGamesFindManyArgs = Prisma.CrashGamesFindManyArgs
import CrashGamesCreateInput = Prisma.CrashGamesCreateInput

import {
  fixDatesInReadNullable,
  fixDatesInFindArgs,
  fixDatesInWrite,
  fixDatesInReadMany,
} from '../../../utils'

export class CrashGamesModel {
  private dateFields: Array<keyof CrashGamesUpdateInput> = ['dateGame']

  constructor(private backendClient: BackendClient) {}

  public async getCrashGame(payload: CrashGamesFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.crashGames.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getCrashGames(payload: CrashGamesFindManyArgs) {
    return fixDatesInReadMany(
      await this.backendClient.crashGames.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async createCrashGame(payload: CrashGamesCreateInput) {
    return this.backendClient.crashGames.create({
      data: fixDatesInWrite(payload, this.dateFields),
    })
  }
}
