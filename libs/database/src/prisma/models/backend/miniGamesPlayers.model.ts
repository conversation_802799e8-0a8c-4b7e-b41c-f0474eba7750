import {
  PrismaClient as BackendClient,
  mini_games_name,
  Prisma,
} from '@majestic-backend/prisma-client-backend'
import MiniGamesPlayersUpdateInput = Prisma.MiniGamesPlayersUpdateInput
import MiniGamesPlayersFindManyArgs = Prisma.MiniGamesPlayersFindManyArgs
import MiniGamesPlayersFindFirstArgs = Prisma.MiniGamesPlayersFindFirstArgs
import MiniGamesPlayersCreateInput = Prisma.MiniGamesPlayersCreateInput
import MiniGamesPlayersUpdateArgs = Prisma.MiniGamesPlayersUpdateArgs
import MiniGamesPlayersUpdateManyArgs = Prisma.MiniGamesPlayersUpdateManyArgs

import {
  fixDatesInFindArgs,
  fixDatesInRead,
  fixDatesInReadMany,
  fixDatesInReadNullable,
  fixDatesInWrite,
} from '../../../utils'

export class MiniGamesPlayersModel {
  private dateFields: Array<keyof MiniGamesPlayersUpdateInput> = []

  constructor(private backendClient: BackendClient) {}

  public async getMiniGamesPlayers(payload: MiniGamesPlayersFindManyArgs) {
    return fixDatesInReadMany(
      await this.backendClient.miniGamesPlayers.findMany(
        fixDatesInFindArgs(payload, this.dateFields),
      ),
      this.dateFields,
    )
  }

  public async getMiniGamesPlayer(payload: MiniGamesPlayersFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.miniGamesPlayers.findFirst(
        fixDatesInFindArgs(payload, this.dateFields),
      ),
      this.dateFields,
    )
  }

  public async createMiniGamesPlayer(data: MiniGamesPlayersCreateInput) {
    return this.backendClient.miniGamesPlayers.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }

  public async updateMiniGamesPlayer(payload: MiniGamesPlayersUpdateArgs) {
    return this.backendClient.miniGamesPlayers.update({
      ...payload,
      data: payload.data,
    })
  }

  public async updateManyMiniGamesPlayers(payload: MiniGamesPlayersUpdateManyArgs) {
    return this.backendClient.miniGamesPlayers.updateMany({
      ...payload,
      data: payload.data,
    })
  }

  public async removeMiniGamesPlayer(userId: number, minigameName: mini_games_name) {
    return fixDatesInRead(
      await this.backendClient.miniGamesPlayers.delete({
        where: {
          minigameName_userId: {
            minigameName,
            userId,
          },
        },
      }),
      this.dateFields,
    )
  }

  public async removeNotInGamePlayer(userId: number, minigameName: mini_games_name) {
    return fixDatesInRead(
      await this.backendClient.miniGamesPlayers.delete({
        where: {
          minigameName_userId: {
            minigameName,
            userId,
          },
          inCurrentGame: false,
        },
      }),
      this.dateFields,
    )
  }
}
