import {
  PrismaClient as BackendClient,
  mini_games_name,
  Prisma,
} from '@majestic-backend/prisma-client-backend'
import CrashBetsUpdateInput = Prisma.CrashBetsUpdateInput
import CrashBetsFindManyArgs = Prisma.CrashBetsFindManyArgs
import CrashBetsFindFirstArgs = Prisma.CrashBetsFindFirstArgs
import CrashBetsCreateInput = Prisma.CrashBetsCreateInput
import CrashBetsUpdateArgs = Prisma.CrashBetsUpdateArgs
import CrashBetsDeleteManyArgs = Prisma.CrashBetsDeleteManyArgs
import MiniGamesF2LogUpdateInput = Prisma.MiniGamesF2LogUpdateInput

import {
  fixDatesInFindArgs,
  fixDatesInRead,
  fixDatesInReadMany,
  fixDatesInReadNullable,
  fixDatesInWrite,
} from '../../../utils'
import { IMiniGamePlayer, MiniGamesDonateType } from '@majestic-backend/types'
import moment from 'moment'

export class CrashBetsModel {
  private dateFields: Array<keyof CrashBetsUpdateInput> = ['dateBet']
  private dateFieldsF2Log: Array<keyof MiniGamesF2LogUpdateInput> = ['date']

  constructor(private backendClient: BackendClient) {}

  public async getCrashBets(payload: CrashBetsFindManyArgs) {
    return fixDatesInReadMany(
      await this.backendClient.crashBets.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getCrashBet(payload: CrashBetsFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.crashBets.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async createCrashBet(data: CrashBetsCreateInput) {
    return this.backendClient.crashBets.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }

  public async updateCrashBet(payload: CrashBetsUpdateArgs) {
    return fixDatesInRead(
      await this.backendClient.crashBets.update({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }

  public async deleteCrashBets(payload: CrashBetsDeleteManyArgs) {
    return this.backendClient.crashBets.deleteMany(payload)
  }

  public async transactionCreateCrashBet(
    minigameName: mini_games_name,
    betAmount: number,
    player: IMiniGamePlayer,
    gameId: number,
  ) {
    return this.backendClient.$transaction(async tx => {
      await tx.miniGamesF2Log.create({
        data: fixDatesInWrite(
          {
            userId: player.userId,
            accountId: player.accountId,
            game: minigameName,
            type: MiniGamesDonateType.BET,
            amount: betAmount,
            gameId,
            serverId: player.serverId,
            args: JSON.stringify({ betAmount }),
            date: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
          this.dateFieldsF2Log,
        ),
      })

      await tx.crashBets.create({
        data: fixDatesInWrite(
          {
            userId: player.userId,
            accountId: player.accountId,
            bet: betAmount,
            exitX: null,
            gameId,
            dateBet: moment().format('YYYY-MM-DD HH:mm:ss'),
            socialClub: player.socialClub,
            serverId: player.serverId,
            login: player.login,
            gender: player.gender,
          },
          this.dateFields,
        ),
      })

      const state = await tx.miniGamesGames.findFirst({
        where: {
          minigameName,
        },
      })

      if (state?.isGameActive) {
        throw new Error(
          `${minigameName} game already started. Refund bet (create bet). AccountId: ${player.accountId}. GameId: ${player.gameId}`,
        )
      }

      return true
    })
  }
}
