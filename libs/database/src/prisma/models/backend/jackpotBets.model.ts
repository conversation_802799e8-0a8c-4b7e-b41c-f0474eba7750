import {
  PrismaClient as BackendClient,
  mini_games_name,
  Prisma,
} from '@majestic-backend/prisma-client-backend'
import JackpotBetsUpdateInput = Prisma.JackpotBetsUpdateInput
import JackpotBetsFindManyArgs = Prisma.JackpotBetsFindManyArgs
import JackpotBetsFindFirstArgs = Prisma.JackpotBetsFindFirstArgs
import JackpotBetsCreateInput = Prisma.JackpotBetsCreateInput
import JackpotBetsUpdateArgs = Prisma.JackpotBetsUpdateArgs
import JackpotBetsDeleteManyArgs = Prisma.JackpotBetsDeleteManyArgs
import MiniGamesF2LogUpdateInput = Prisma.MiniGamesF2LogUpdateInput

import {
  fixDatesInFindArgs,
  fixDatesInRead,
  fixDatesInReadMany,
  fixDatesInReadNullable,
  fixDatesInWrite,
} from '../../../utils'
import moment from 'moment'
import { IMiniGamePlayer, MiniGamesDonateType } from '@majestic-backend/types'

export class JackpotBetsModel {
  private dateFields: Array<keyof JackpotBetsUpdateInput> = ['dateBet']
  private dateFieldsF2Log: Array<keyof MiniGamesF2LogUpdateInput> = ['date']

  constructor(private backendClient: BackendClient) {}

  public async getJackpotBets(payload: JackpotBetsFindManyArgs) {
    return fixDatesInReadMany(
      await this.backendClient.jackpotBets.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getJackpotBet(payload: JackpotBetsFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.jackpotBets.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async createJackpotBet(data: JackpotBetsCreateInput) {
    return this.backendClient.jackpotBets.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }

  public async updateJackpotBet(payload: JackpotBetsUpdateArgs) {
    return fixDatesInRead(
      await this.backendClient.jackpotBets.update({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }

  public async deleteJackpotBets(payload: JackpotBetsDeleteManyArgs) {
    return this.backendClient.jackpotBets.deleteMany(payload)
  }

  public async transactionCreateJackpotBet(
    minigameName: mini_games_name,
    betAmount: number,
    player: IMiniGamePlayer,
    ticket: string,
    gameId: number,
  ) {
    return this.backendClient.$transaction(async tx => {
      await tx.miniGamesF2Log.create({
        data: fixDatesInWrite(
          {
            userId: player.userId,
            accountId: player.accountId,
            game: minigameName,
            type: MiniGamesDonateType.BET,
            amount: betAmount,
            gameId,
            serverId: player.serverId,
            args: JSON.stringify(player),
            date: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
          this.dateFieldsF2Log,
        ),
      })

      await tx.jackpotBets.create({
        data: fixDatesInWrite(
          {
            userId: player.userId,
            accountId: player.accountId,
            socialClub: player.socialClub,
            login: player.login,
            amount: betAmount,
            ticket,
            gameId,
            serverId: player.serverId,
            gender: player.gender,
            dateBet: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
          this.dateFields,
        ),
      })

      const state = await tx.miniGamesGames.findFirst({
        where: {
          minigameName,
        },
      })

      if (state?.isGameActive) {
        throw new Error(
          `${minigameName} game already started. Refund bet (create bet). AccountId: ${player.accountId}. GameId: ${player.gameId}`,
        )
      }

      return true
    })
  }

  public async transactionUpdateJackpotBet(
    minigameName: mini_games_name,
    betAmount: number,
    newBet: number,
    player: IMiniGamePlayer,
  ) {
    return this.backendClient.$transaction(async tx => {
      await tx.miniGamesF2Log.create({
        data: fixDatesInWrite(
          {
            userId: player.userId,
            accountId: player.accountId,
            game: minigameName,
            type: MiniGamesDonateType.BET,
            amount: betAmount,
            gameId: player.gameId,
            serverId: player.serverId,
            args: JSON.stringify(player),
            date: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
          this.dateFieldsF2Log,
        ),
      })

      const { count } = await tx.jackpotBets.updateMany({
        where: {
          gameId: player.gameId,
          userId: player.userId,
          accountId: player.accountId,
        },
        data: {
          amount: newBet,
        },
      })
      if (count === 0) {
        throw new Error(
          `Bet not found. Refund bet. AccountId: ${player.accountId}. GameId: ${player.gameId}`,
        )
      }

      const state = await tx.miniGamesGames.findFirst({
        where: {
          minigameName,
        },
      })

      if (state?.isGameActive) {
        throw new Error(
          `${minigameName} game already started. Refund bet (update bet). AccountId: ${player.accountId}. GameId: ${player.gameId}`,
        )
      }

      return true
    })
  }
}
