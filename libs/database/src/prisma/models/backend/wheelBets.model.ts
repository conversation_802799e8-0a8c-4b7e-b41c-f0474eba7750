import {
  PrismaClient as BackendClient,
  mini_games_name,
  Prisma,
} from '@majestic-backend/prisma-client-backend'
import WheelBetsUpdateInput = Prisma.WheelBetsUpdateInput
import WheelBetsFindFirstArgs = Prisma.WheelBetsFindFirstArgs
import WheelBetsFindManyArgs = Prisma.WheelBetsFindManyArgs
import WheelBetsCreateInput = Prisma.WheelBetsCreateInput
import WheelBetsUpdateArgs = Prisma.WheelBetsUpdateArgs
import WheelBetsDeleteManyArgs = Prisma.WheelBetsDeleteManyArgs
import MiniGamesF2LogUpdateInput = Prisma.MiniGamesF2LogUpdateInput

import {
  fixDatesInReadNullable,
  fixDatesInFindArgs,
  fixDatesInWrite,
  fixDatesInRead,
  fixDatesInReadMany,
} from '../../../utils'
import moment from 'moment'
import { IMiniGamePlayer, MiniGamesDonateType } from '@majestic-backend/types'

export class WheelBetsModel {
  private dateFields: Array<keyof WheelBetsUpdateInput> = ['dateBet']
  private dateFieldsF2Log: Array<keyof MiniGamesF2LogUpdateInput> = ['date']

  constructor(private backendClient: BackendClient) {}

  public async getWheelBet(payload: WheelBetsFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.wheelBets.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getWheelBets(payload: WheelBetsFindManyArgs) {
    return fixDatesInReadMany(
      await this.backendClient.wheelBets.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async createWheelBet(data: WheelBetsCreateInput) {
    return this.backendClient.wheelBets.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }

  public async updateWheelBet(payload: WheelBetsUpdateArgs) {
    return fixDatesInRead(
      await this.backendClient.wheelBets.update({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }

  public async deleteWheelBets(payload: WheelBetsDeleteManyArgs) {
    return this.backendClient.wheelBets.deleteMany(payload)
  }

  public async transactionCreateWheelBet(
    minigameName: mini_games_name,
    betAmount: number,
    player: IMiniGamePlayer,
    color: string,
    gameId: number,
  ) {
    return this.backendClient.$transaction(async tx => {
      await tx.miniGamesF2Log.create({
        data: fixDatesInWrite(
          {
            userId: player.userId,
            accountId: player.accountId,
            game: minigameName,
            type: MiniGamesDonateType.BET,
            amount: betAmount,
            gameId,
            serverId: player.serverId,
            args: JSON.stringify({
              amount: betAmount,
              color,
            }),
            date: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
          this.dateFieldsF2Log,
        ),
      })

      await tx.wheelBets.create({
        data: fixDatesInWrite(
          {
            userId: player.userId,
            accountId: player.accountId,
            amount: betAmount,
            color,
            gameId,
            dateBet: moment().format('YYYY-MM-DD HH:mm:ss'),
            socialClub: player.socialClub,
            serverId: player.serverId,
            login: player.login,
            gender: player.gender,
          },
          this.dateFields,
        ),
      })

      const state = await tx.miniGamesGames.findFirst({
        where: {
          minigameName,
        },
      })

      if (state?.isGameActive) {
        throw new Error(
          `${minigameName} game already started. Refund bet (create bet). AccountId: ${player.accountId}. GameId: ${player.gameId}`,
        )
      }

      return true
    })
  }

  public async transactionUpdateWheelBet(
    minigameName: mini_games_name,
    betAmount: number,
    newBet: number,
    player: IMiniGamePlayer,
    gameId: number,
    color: string,
  ) {
    return this.backendClient.$transaction(async tx => {
      await tx.miniGamesF2Log.create({
        data: fixDatesInWrite(
          {
            userId: player.userId,
            accountId: player.accountId,
            game: minigameName,
            type: MiniGamesDonateType.BET,
            amount: betAmount,
            gameId,
            serverId: player.serverId,
            args: JSON.stringify({
              amount: betAmount,
              color,
            }),
            date: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
          this.dateFieldsF2Log,
        ),
      })

      const { count } = await tx.wheelBets.updateMany({
        where: {
          gameId,
          userId: player.userId,
          accountId: player.accountId,
          color,
        },
        data: {
          amount: newBet,
        },
      })
      if (count === 0) {
        throw new Error(
          `Bet not found. Refund bet. AccountId: ${player.accountId}. GameId: ${gameId}`,
        )
      }

      const state = await tx.miniGamesGames.findFirst({
        where: {
          minigameName,
        },
      })

      if (state?.isGameActive) {
        throw new Error(
          `${minigameName} game already started. Refund bet (update bet). AccountId: ${player.accountId}. GameId: ${gameId}`,
        )
      }

      return true
    })
  }
}
