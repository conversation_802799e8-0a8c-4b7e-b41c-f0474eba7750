import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import FaqUpsertArgs = Prisma.FaqUpsertArgs
import FaqFindManyArgs = Prisma.FaqFindManyArgs
import FaqDeleteArgs = Prisma.FaqDeleteArgs

export class FaqModel {
  constructor(private backendClient: BackendClient) {}

  public updateFaq(payload: FaqUpsertArgs) {
    return this.backendClient.faq.upsert(payload)
  }

  public getFaq(payload?: FaqFindManyArgs) {
    return this.backendClient.faq.findMany(payload)
  }
  public deleteFaq(payload: FaqDeleteArgs) {
    return this.backendClient.faq.delete(payload)
  }
}
