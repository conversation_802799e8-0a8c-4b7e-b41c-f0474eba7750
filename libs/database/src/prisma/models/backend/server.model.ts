import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import ServerFindManyArgs = Prisma.ServerFindManyArgs
import ServerUpdateArgs = Prisma.ServerUpdateArgs
import ServerUpdateInput = Prisma.ServerUpdateInput
import ServerFindFirstArgs = Prisma.ServerFindFirstArgs
import { fixDatesInReadMany, fixDatesInWrite } from 'libs/database/src/utils'

export interface IServerExtras {
  forBeginners?: boolean
  advantages?: Record<string, any>
}

export class ServerModel {
  constructor(private backendClient: BackendClient) {}

  public dateFields: Array<keyof ServerUpdateInput> = ['lastPlayersUpdatedAt']

  public async getServers(payload?: ServerFindManyArgs) {
    return fixDatesInReadMany(await this.backendClient.server.findMany(payload), this.dateFields)
  }

  public getServer(payload?: ServerFindFirstArgs) {
    return this.backendClient.server.findFirst(payload)
  }

  public updateServer(payload: ServerUpdateArgs) {
    return this.backendClient.server.update(payload)
  }

  public async getServerConnectConfig(serverId: string): Promise<string | null> {
    const res = await this.backendClient.server.findFirst({
      where: { id: serverId },
      select: { connectConfig: true },
    })
    return res?.connectConfig || null
  }

  public async setServerConnectConfig(serverId: string, connectConfig: string): Promise<void> {
    await this.backendClient.server.update({
      where: { id: serverId },
      data: { connectConfig },
    })
  }

  public async getServerExtras(serverId: string): Promise<IServerExtras | null> {
    const dbServer = await this.backendClient.server.findFirst({ where: { id: serverId } })
    let extras = null

    if (!dbServer?.extras) {
      return extras
    }

    try {
      extras = JSON.parse(dbServer.extras)
    } catch {
      // could be
    }

    return extras
  }

  public async updateServerOnline(
    serverId: string,
    {
      online,
      queue,
    }: {
      online: number
      queue: number
    },
  ): Promise<void> {
    await this.backendClient.server.update({
      where: { id: serverId.toLowerCase() },
      data: fixDatesInWrite(
        {
          lastPlayersQueue: queue,
          lastPlayersOnline: online,
          lastPlayersUpdatedAt: new Date(),
        },
        this.dateFields as any,
      ),
    })
  }
}
