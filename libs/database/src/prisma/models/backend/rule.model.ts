import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import RuleUpsertArgs = Prisma.RuleUpsertArgs
import RuleFindManyArgs = Prisma.RuleFindManyArgs
import RuleDeleteArgs = Prisma.RuleDeleteArgs

export class RuleModel {
  constructor(private backendClient: BackendClient) {}

  public updateRule(payload: RuleUpsertArgs) {
    return this.backendClient.rule.upsert(payload)
  }

  public getRules(payload?: RuleFindManyArgs) {
    return this.backendClient.rule.findMany(payload)
  }
  public deleteRule(payload: RuleDeleteArgs) {
    return this.backendClient.rule.delete(payload)
  }
}
