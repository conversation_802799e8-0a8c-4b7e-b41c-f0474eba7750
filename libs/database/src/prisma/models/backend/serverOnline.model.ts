import {
  PrismaClient as BackendClient,
  ServerOn<PERSON>,
  Prisma,
} from '@majestic-backend/prisma-client-backend'
import ServerOnlineCreateInput = Prisma.ServerOnlineCreateInput

export class ServerOnlineModel {
  constructor(private backendClient: BackendClient) {}

  public async getServersOnline(): Promise<ServerOnline[]> {
    return this.backendClient
      .$queryRaw`SELECT * from server_online WHERE id IN (SELECT MAX(id) FROM server_online GROUP BY serverId)`
  }

  public async createServerOnline(data: ServerOnlineCreateInput) {
    return this.backendClient.serverOnline.create({
      data,
    })
  }
}
