import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import KeysPaymentsCreateInput = Prisma.KeysPaymentsCreateInput
import KeysPaymentsFindFirstArgs = Prisma.KeysPaymentsFindFirstArgs
import KeysPaymentsUpdateArgs = Prisma.KeysPaymentsUpdateArgs
import {
  fixDatesInFindArgs,
  fixDatesInRead,
  fixDatesInReadNullable,
  fixDatesInWrite,
} from '../../../utils'

export class KeysPaymentsModel {
  private dateFields: Array<keyof KeysPaymentsCreateInput> = ['dateComplete', 'dateCreate']

  constructor(private backendClient: BackendClient) {}

  public async createKeyPayment(data: KeysPaymentsCreateInput) {
    return this.backendClient.keysPayments.create({ data: fixDatesInWrite(data, this.dateFields) })
  }

  public async getKeyPayment(payload: KeysPaymentsFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.keysPayments.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async updateKeyPayment(payload: KeysPaymentsUpdateArgs) {
    return fixDatesInRead(
      await this.backendClient.keysPayments.update({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }
}
