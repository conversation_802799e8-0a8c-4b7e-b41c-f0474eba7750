import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import VerifiedEmailsCreateInput = Prisma.VerifiedEmailsCreateInput
import VerifiedEmailsFindFirstArgs = Prisma.VerifiedEmailsFindFirstArgs
import VerifiedEmailsUpdateArgs = Prisma.VerifiedEmailsUpdateArgs
import {
  fixDatesInFindArgs,
  fixDatesInRead,
  fixDatesInReadNullable,
  fixDatesInWrite,
} from '../../../utils'

export class VerifiedEmailsModel {
  private dateFields: Array<keyof VerifiedEmailsCreateInput> = ['createdAt', 'completedAt']

  constructor(private backendClient: BackendClient) {}

  public async createVerifiedEmail(data: VerifiedEmailsCreateInput) {
    return this.backendClient.verifiedEmails.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }

  public async getVerifiedEmail(payload: VerifiedEmailsFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.verifiedEmails.findFirst(
        fixDatesInFindArgs(payload, this.dateFields),
      ),
      this.dateFields,
    )
  }

  public async updateVerifiedEmail(payload: VerifiedEmailsUpdateArgs) {
    return fixDatesInRead(
      await this.backendClient.verifiedEmails.update({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }
}
