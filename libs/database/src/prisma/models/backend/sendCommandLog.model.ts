import {
  PrismaClient as BackendClient,
  <PERSON>rism<PERSON>,
  SendCommandLog,
} from '@majestic-backend/prisma-client-backend'
import moment from 'moment-timezone'

import SendCommandLogFindFirstArgs = Prisma.SendCommandLogFindFirstArgs
import SendCommandLogFindManyArgs = Prisma.SendCommandLogFindManyArgs
import SendCommandLogCreateInput = Prisma.SendCommandLogCreateInput
import SendCommandLogUpdateInput = Prisma.SendCommandLogUpdateInput
import {
  fixDatesInFindArgs,
  fixDatesInRead,
  fixDatesInReadMany,
  fixDatesInReadNullable,
  fixDatesInWrite,
} from '../../../utils/date'

export class SendCommandLogModel {
  private dateFields: Array<keyof SendCommandLogUpdateInput> = ['date']

  constructor(private backendClient: BackendClient) {}

  public async getSendCommandLogs(payload: SendCommandLogFindManyArgs) {
    return fixDatesInReadMany(
      await this.backendClient.sendCommandLog.findMany(
        fixDatesInFindArgs(payload, this.dateFields),
      ),
      this.dateFields,
    )
  }

  // ugly thing, but needed cause of args binary type
  public async getAdminSendCommandLogs(payload: {
    server?: string
    admin?: string | number
    command?: string
    args?: string
    date?: Date
    limit: number
    offset?: number
  }): Promise<SendCommandLog[]> {
    const values = []

    const where = []

    if (payload?.server) {
      where.push(`server = ?`)
      values.push(payload.server)
    }

    if (payload?.admin) {
      where.push('login = ? OR accountId = ?')
      values.push(payload.admin, payload.admin)
    }

    if (payload?.command) {
      where.push(`command = ?`)
      values.push(payload.command)
    }

    if (payload?.args) {
      where.push('args LIKE ?')
      values.push(
        '%' +
          payload.args
            .split(' ')
            .map(el => `${el}%`)
            .join(''),
      )
    }

    if (payload?.date) {
      const dateLimits = fixDatesInWrite(
        {
          from: moment(payload.date).toDate(),
          to: moment(payload.date).add(1, 'days').toDate(),
        },
        ['from', 'to'],
      )

      where.push('date >= ? AND date <= ?')
      values.push(
        moment(dateLimits.from).format('YYYY-MM-DD'),
        moment(dateLimits.to).add(1, 'days').format('YYYY-MM-DD'),
      )
    }

    const queryArr = ['SELECT * FROM send_command_log']

    if (where.length) {
      queryArr.push(' WHERE ')
      queryArr.push(where.join(' AND '))
    }

    queryArr.push(' ORDER BY id DESC LIMIT ?')
    values.push(payload.limit)

    return fixDatesInReadMany(
      await this.backendClient.$queryRawUnsafe(queryArr.join(''), ...values),
      this.dateFields,
    )
  }

  public async getSendCommandLog(payload: SendCommandLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.sendCommandLog.findFirst(
        fixDatesInFindArgs(payload, this.dateFields),
      ),
      this.dateFields,
    )
  }
  public async createSendCommandLog(data: SendCommandLogCreateInput) {
    const row = await this.backendClient.sendCommandLog.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
    return fixDatesInRead(row, this.dateFields)
  }
}
