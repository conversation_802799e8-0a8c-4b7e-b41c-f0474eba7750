import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import TwitchStreamerFindManyArgs = Prisma.TwitchStreamerFindManyArgs
import TwitchStreamerFindFirstArgs = Prisma.TwitchStreamerFindFirstArgs
import TwitchStreamerCreateArgs = Prisma.TwitchStreamerCreateArgs
import TwitchStreamerDeleteArgs = Prisma.TwitchStreamerDeleteArgs

export class TwitchStreamerModel {
  constructor(private backendClient: BackendClient) {}

  public getStreamers(payload?: TwitchStreamerFindManyArgs) {
    return this.backendClient.twitchStreamer.findMany(payload)
  }

  public getStreamer(payload: TwitchStreamerFindFirstArgs) {
    return this.backendClient.twitchStreamer.findFirst(payload)
  }

  public createStreamer(payload: TwitchStreamerCreateArgs) {
    return this.backendClient.twitchStreamer.create(payload)
  }

  public deleteStreamer(payload: TwitchStreamerDeleteArgs) {
    return this.backendClient.twitchStreamer.delete(payload)
  }
}
