import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import MiniGamesF2LogUpdateInput = Prisma.MiniGamesF2LogUpdateInput
import MiniGamesF2LogCreateInput = Prisma.MiniGamesF2LogCreateInput
import { fixDatesInWrite } from '../../../utils/date'

export class MiniGamesF2LogModel {
  private dateFields: Array<keyof MiniGamesF2LogUpdateInput> = ['date']

  constructor(private backendClient: BackendClient) {}

  public async createMiniGamesF2Log(data: MiniGamesF2LogCreateInput) {
    return this.backendClient.miniGamesF2Log.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }
}
