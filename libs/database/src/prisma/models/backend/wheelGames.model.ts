import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import WheelGamesUpdateInput = Prisma.WheelGamesUpdateInput
import WheelGamesFindFirstArgs = Prisma.WheelGamesFindFirstArgs
import WheelGamesFindManyArgs = Prisma.WheelGamesFindManyArgs
import WheelGamesCreateInput = Prisma.WheelGamesCreateInput

import {
  fixDatesInReadNullable,
  fixDatesInFindArgs,
  fixDatesInReadMany,
  fixDatesInWrite,
} from '../../../utils'

export class WheelGamesModel {
  private dateFields: Array<keyof WheelGamesUpdateInput> = ['dateGame']

  constructor(private backendClient: BackendClient) {}

  public async getWheelGame(payload: WheelGamesFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.wheelGames.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getWheelGames(payload: WheelGamesFindManyArgs) {
    return fixDatesInReadMany(
      await this.backendClient.wheelGames.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async createWheelGame(payload: WheelGamesCreateInput) {
    return this.backendClient.wheelGames.create({
      data: fixDatesInWrite(payload, this.dateFields),
    })
  }
}
