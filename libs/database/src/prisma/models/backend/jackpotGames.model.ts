import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import JackpotGamesUpdateInput = Prisma.JackpotGamesUpdateInput
import JackpotGamesFindFirstArgs = Prisma.JackpotGamesFindFirstArgs
import JackpotGamesCreateInput = Prisma.JackpotGamesCreateInput
import JackpotGamesFindManyArgs = Prisma.JackpotGamesFindManyArgs

import {
  fixDatesInReadNullable,
  fixDatesInFindArgs,
  fixDatesInWrite,
  fixDatesInReadMany,
} from '../../../utils'

export class JackpotGamesModel {
  private dateFields: Array<keyof JackpotGamesUpdateInput> = ['dateGame']

  constructor(private backendClient: BackendClient) {}

  public async getJackpotGame(payload: JackpotGamesFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.jackpotGames.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getJackpotGames(payload: JackpotGamesFindManyArgs) {
    return fixDatesInReadMany(
      await this.backendClient.jackpotGames.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async createJackpotGame(payload: JackpotGamesCreateInput) {
    return this.backendClient.jackpotGames.create({
      data: fixDatesInWrite(payload, this.dateFields),
    })
  }
}
