import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import RightsAdminSiteUpsertArgs = Prisma.RightsAdminSiteUpsertArgs
import RightsAdminSiteFindManyArgs = Prisma.RightsAdminSiteFindManyArgs

export class RightsAdminSiteModel {
  constructor(private backendClient: BackendClient) {}

  public updateRightsAdminSite(payload: RightsAdminSiteUpsertArgs) {
    return this.backendClient.rightsAdminSite.upsert(payload)
  }

  public getRightsAdminSite(payload?: RightsAdminSiteFindManyArgs) {
    return this.backendClient.rightsAdminSite.findMany(payload)
  }

  //   public create(data: any): Promise<RightsAdminSiteModel> {
  //     return this.backendClient.rightsAdminSite.create({
  //       data,
  //     })
  //   }

  //   public async get() {
  //     const rightsData = await this.backendClient.rightsAdminSite.findMany()

  //     if (!rightsData) {
  //       throw new HttpException('ErrorNames.BAD_REQUEST', HttpStatus.BAD_REQUEST)
  //     }

  //     return rightsData.map(item => {
  //       return {
  //         adminLevel: item.adminLevel,
  //         rights: item.rights.split('|'),
  //       }
  //     })
  //   }

  public async update(adminLevel: number, rights: string) {
    return this.backendClient.rightsAdminSite.update({
      where: {
        adminLevel,
      },
      data: {
        rights,
      },
    })
  }
}
