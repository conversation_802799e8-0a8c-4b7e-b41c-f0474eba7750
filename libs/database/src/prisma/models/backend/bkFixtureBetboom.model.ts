import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import BkFixtureBetboomUpdateArgs = Prisma.BkFixtureBetboomUpdateArgs
import BkFixtureBetboomFindManyArgs = Prisma.BkFixtureBetboomFindManyArgs

export class BkFixtureBetboomModel {
  constructor(private backendClient: BackendClient) {}

  public updateBkFixtureBetboom(payload: BkFixtureBetboomUpdateArgs) {
    return this.backendClient.bkFixtureBetboom.update(payload)
  }

  public getBkFixtureBetboom(payload?: BkFixtureBetboomFindManyArgs) {
    return this.backendClient.bkFixtureBetboom.findMany(payload)
  }
}
