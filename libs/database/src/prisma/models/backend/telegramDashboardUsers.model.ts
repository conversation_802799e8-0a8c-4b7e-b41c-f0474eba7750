import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import TelegramDashboardUsersCreateInput = Prisma.TelegramDashboardUsersCreateInput
import TelegramDashboardUsersFindFirstArgs = Prisma.TelegramDashboardUsersFindFirstArgs
import TelegramDashboardUsersUpdateArgs = Prisma.TelegramDashboardUsersUpdateArgs
import TelegramDashboardUsersDeleteArgs = Prisma.TelegramDashboardUsersDeleteArgs
import TelegramDashboardUsersFindManyArgs = Prisma.TelegramDashboardUsersFindManyArgs
import {
  fixDatesInFindArgs,
  fixDatesInRead,
  fixDatesInReadNullable,
  fixDatesInWrite,
} from '../../../utils'

export class TelegramDashboardUsersModel {
  private dateFields: Array<keyof TelegramDashboardUsersCreateInput> = ['createdAt']

  constructor(private backendClient: BackendClient) {}

  public async createTelegramDashboardUser(data: TelegramDashboardUsersCreateInput) {
    return this.backendClient.telegramDashboardUsers.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }

  public async getTelegramDashboardUser(payload: TelegramDashboardUsersFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.telegramDashboardUsers.findFirst(
        fixDatesInFindArgs(payload, this.dateFields),
      ),
      this.dateFields,
    )
  }

  public async getTelegramDashboardUsers(payload?: TelegramDashboardUsersFindManyArgs) {
    return this.backendClient.telegramDashboardUsers.findMany(payload)
  }

  public async updateTelegramDashboardUser(payload: TelegramDashboardUsersUpdateArgs) {
    return fixDatesInRead(
      await this.backendClient.telegramDashboardUsers.update({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }

  public async deleteTelegramDashboardUser(data: TelegramDashboardUsersDeleteArgs) {
    return this.backendClient.telegramDashboardUsers.delete(data)
  }
}
