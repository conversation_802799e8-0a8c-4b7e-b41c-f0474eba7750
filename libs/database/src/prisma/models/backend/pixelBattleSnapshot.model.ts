import { PrismaClient as BackendClient, Prisma } from '@majestic-backend/prisma-client-backend'
import PixelBattleSnapshotCreateArgs = Prisma.PixelBattleSnapshotCreateArgs
import PixelBattleSnapshotUpdateInput = Prisma.PixelBattleSnapshotUpdateInput
import { fixDatesInRead, fixDatesInWrite } from '../../../utils/date'

export class PixelBattleSnapshotModel {
  constructor(private backendClient: BackendClient) {}

  private dateFields: Array<keyof PixelBattleSnapshotUpdateInput> = ['createdAt']

  public async create(payload: PixelBattleSnapshotCreateArgs) {
    return fixDatesInRead(
      await this.backendClient.pixelBattleSnapshot.create({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }
}
