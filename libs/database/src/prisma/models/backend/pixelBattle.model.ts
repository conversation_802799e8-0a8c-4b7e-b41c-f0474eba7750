import {
  PrismaClient as BackendClient,
  Prisma,
  PixelBattle,
} from '@majestic-backend/prisma-client-backend'
import PixelBattleCreateInput = Prisma.PixelBattleCreateArgs
import PixelBattleUpdateArgs = Prisma.PixelBattleUpdateArgs
import PixelBattleDeleteArgs = Prisma.PixelBattleDeleteArgs
import PixelBattleFindFirstArgs = Prisma.PixelBattleFindFirstArgs
import PixelBattleCountArgs = Prisma.PixelBattleCountArgs
import PixelBattleUpdateInput = Prisma.PixelBattleUpdateInput
import { fixDatesInReadNullable, fixDatesInRead, fixDatesInWrite } from '../../../utils/date'

export class PixelBattleModel {
  constructor(private backendClient: BackendClient) {}

  private dateFields: Array<keyof PixelBattleUpdateInput> = ['updateDate']

  public async create(payload: PixelBattleCreateInput) {
    return fixDatesInRead(
      await this.backendClient.pixelBattle.create({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }

  public async update(payload: PixelBattleUpdateArgs) {
    return fixDatesInRead(
      await this.backendClient.pixelBattle.update({
        ...payload,
        data: fixDatesInRead(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }

  public del(data: PixelBattleDeleteArgs) {
    return this.backendClient.pixelBattle.delete(data)
  }

  public delAll() {
    return this.backendClient.pixelBattle.deleteMany()
  }

  public async get(data: PixelBattleFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.backendClient.pixelBattle.findFirst(data),
      this.dateFields,
    )
  }

  public getTop(): Promise<
    {
      serverId: PixelBattle['serverId']
      staticId: PixelBattle['staticId']
      pbCount: number
    }[]
  > {
    return this.backendClient.$queryRaw<
      {
        serverId: PixelBattle['serverId']
        staticId: PixelBattle['staticId']
        pbCount: bigint
      }[]
    >`SELECT serverId, staticId, name, COUNT(*) AS pbCount FROM
        pixel_battle WHERE isAdmin = 0 GROUP BY serverId, staticId ORDER BY pbCount DESC LIMIT 25;`.then(
      res =>
        (res || []).map(el => ({
          ...el,
          pbCount: Number(el.pbCount),
        })),
    )
  }

  public count(data: PixelBattleCountArgs) {
    return this.backendClient.pixelBattle.count(data)
  }
}
