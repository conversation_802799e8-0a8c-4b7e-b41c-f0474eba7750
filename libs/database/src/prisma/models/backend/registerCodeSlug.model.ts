import {
  PrismaClient as BackendClient,
  RegisterCodeSlug as RegisterCodeSlugModel,
  Prisma,
} from '@majestic-backend/prisma-client-backend'

export class RegisterCodeSlug {
  constructor(private backendClient: BackendClient) {}

  public create(data: Prisma.RegisterCodeSlugCreateInput): Promise<RegisterCodeSlugModel> {
    return this.backendClient.registerCodeSlug.create({
      data,
    })
  }

  getByIdAndSlug(id: number, slug: string): Promise<RegisterCodeSlugModel | null> {
    return this.backendClient.registerCodeSlug.findFirst({ where: { id, slug } })
  }

  remove(id: number): Promise<RegisterCodeSlugModel> {
    return this.backendClient.registerCodeSlug.delete({ where: { id } })
  }
}
