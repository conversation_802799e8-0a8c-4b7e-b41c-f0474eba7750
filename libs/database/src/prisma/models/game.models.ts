import { UserModel } from './game/user.model'
import { AccountModel } from './game/account.model'
import { ItemLogModel } from './game/itemLog.model'
import { ItemTradeLogModel } from './game/itemTradeLog.model'
import { PromoCodeModel } from './game/promoCode.model'
import { ReportModel } from './game/report.model'
import { TokenModel } from './game/token.model'
import { FractionModel } from './game/fraction.model'
import { ItemModel } from './game/item.model'
import { AdminLogModel } from './game/adminLog.model'
import { FractionLogModel } from './game/fractionLog.model'
import { MoneyLogModel } from './game/moneyLog.model'
import { DeathLogModel } from './game/deathLog.model'
import { AttackOnTheArmyResetTimeModel } from './game/attackOnTheArmyResetTime.model'
import { AttackOnTheArmyModel } from './game/attackOnTheArmy.model'
import { AttackOnTheArmyTimeModel } from './game/attackOnTheArmyTime.model'
import { PlantWeedTimeModel } from './game/plantWeedTime.model'
import { FamilyModel } from './game/family.model'
import { FamilyLogModel } from './game/familyLog.model'
import { BankModel } from './game/bank.model'
import { NameLogModel } from './game/nameLog.model'
import { SessionLogModel } from './game/sessionLog.model'
import { BanlistModel } from './game/banlist.model'
import { VehicleModel } from './game/vehicle.model'
import { WhiteListModel } from './game/whiteList.model'
import { UnitpayPaymentModel } from './game/unitpayPayments.model'
import { DonateLogModel } from './game/donateLog.model'
import { SpecialModel } from './game/special.model'
import { ReportMessageModel } from './game/reportMessage.model'
import { NewsQueueModel } from './game/newsQueue.model'
import { CaptureLogModel } from './game/captureLog.model'
import { FamilyWarsLogModel } from './game/familyWarsLog.model'
import { CaptureEventsLogModel } from './game/captureEventsLog.model'
import { CapturesQueueModel } from './game/capturesQueue.model'
import { FamilyWarsEventsLogModel } from './game/familyWarsEventsLog.model'
import { FamilyWarsQueueModel } from './game/familyWarsQueue.model'
import { AdminWhiteListModel } from './game/adminWhiteList.model'
import { SubscriptionModel } from './game/subscription.model'
import { PixelBattleModel } from './game/pixelBattle.model'

export { IVehicle } from './game/vehicle.model'
export { IAccount } from './game/account.model'

export enum GameModelEnum {
  UserModel = 1,
  AccountModel,
  FamilyModel,
  NewsQueueModel,
  PromoCodeModel,
  ReportModel,
  ReportMessageModel,
  TokenModel,
  UnitpayPaymentModel,
  FractionModel,
  ItemModel,
  ItemLogModel,
  AdminLogModel,
  FractionLogModel,
  ItemTradeLogModel,
  MoneyLogModel,
  DeathLogModel,
  AttackOnTheArmyResetTimeModel,
  AttackOnTheArmyModel,
  AttackOnTheArmyTimeModel,
  PlantWeedTimeModel,
  FamilyLogModel,
  BankModel,
  NameLogModel,
  SessionLogModel,
  BanlistModel,
  VehicleModel,
  WhiteListModel,
  DonateLogModel,
  SpecialModel,
  CaptureLogModel,
  CaptureEventsLogModel,
  CapturesQueueModel,
  FamilyWarsLogModel,
  FamilyWarsEventsLogModel,
  FamilyWarsQueueModel,
  AdminWhiteListModel,
  SubscriptionModel,
  PixelBattleModel,
}

export const gameModels = {
  [GameModelEnum.UserModel]: UserModel,
  [GameModelEnum.AccountModel]: AccountModel,
  [GameModelEnum.PromoCodeModel]: PromoCodeModel,
  [GameModelEnum.ReportModel]: ReportModel,
  [GameModelEnum.TokenModel]: TokenModel,
  [GameModelEnum.FractionModel]: FractionModel,
  [GameModelEnum.ItemModel]: ItemModel,
  [GameModelEnum.ItemLogModel]: ItemLogModel,
  [GameModelEnum.AdminLogModel]: AdminLogModel,
  [GameModelEnum.FractionLogModel]: FractionLogModel,
  [GameModelEnum.ItemTradeLogModel]: ItemTradeLogModel,
  [GameModelEnum.MoneyLogModel]: MoneyLogModel,
  [GameModelEnum.DeathLogModel]: DeathLogModel,
  [GameModelEnum.AttackOnTheArmyResetTimeModel]: AttackOnTheArmyResetTimeModel,
  [GameModelEnum.AttackOnTheArmyModel]: AttackOnTheArmyModel,
  [GameModelEnum.AttackOnTheArmyTimeModel]: AttackOnTheArmyTimeModel,
  [GameModelEnum.PlantWeedTimeModel]: PlantWeedTimeModel,
  [GameModelEnum.FamilyModel]: FamilyModel,
  [GameModelEnum.FamilyLogModel]: FamilyLogModel,
  [GameModelEnum.BankModel]: BankModel,
  [GameModelEnum.NameLogModel]: NameLogModel,
  [GameModelEnum.SessionLogModel]: SessionLogModel,
  [GameModelEnum.BanlistModel]: BanlistModel,
  [GameModelEnum.VehicleModel]: VehicleModel,
  [GameModelEnum.WhiteListModel]: WhiteListModel,
  [GameModelEnum.UnitpayPaymentModel]: UnitpayPaymentModel,
  [GameModelEnum.DonateLogModel]: DonateLogModel,
  [GameModelEnum.SpecialModel]: SpecialModel,
  [GameModelEnum.ReportMessageModel]: ReportMessageModel,
  [GameModelEnum.NewsQueueModel]: NewsQueueModel,
  [GameModelEnum.CaptureLogModel]: CaptureLogModel,
  [GameModelEnum.CaptureEventsLogModel]: CaptureEventsLogModel,
  [GameModelEnum.CapturesQueueModel]: CapturesQueueModel,
  [GameModelEnum.FamilyWarsLogModel]: FamilyWarsLogModel,
  [GameModelEnum.FamilyWarsEventsLogModel]: FamilyWarsEventsLogModel,
  [GameModelEnum.FamilyWarsQueueModel]: FamilyWarsQueueModel,
  [GameModelEnum.AdminWhiteListModel]: AdminWhiteListModel,
  [GameModelEnum.SubscriptionModel]: SubscriptionModel,
  [GameModelEnum.PixelBattleModel]: PixelBattleModel,
} as const
