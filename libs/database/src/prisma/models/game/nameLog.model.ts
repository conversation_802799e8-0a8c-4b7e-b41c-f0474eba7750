import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import NameLogUpdateInput = Prisma.NameLogUpdateInput
import NameLogFindFirstArgs = Prisma.NameLogFindFirstArgs
import NameLogFindManyArgs = Prisma.NameLogFindManyArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class NameLogModel {
  private dateFields: Array<keyof NameLogUpdateInput> = ['date']

  constructor(private gameClient: GameClient) {}

  public async getNameLogs(payload: NameLogFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.nameLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getNameLog(payload: NameLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.nameLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
