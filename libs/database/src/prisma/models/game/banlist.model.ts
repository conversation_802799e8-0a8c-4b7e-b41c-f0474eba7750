import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import BanlistUpdateInput = Prisma.BanlistUpdateInput
import BanlistFindFirstArgs = Prisma.BanlistFindFirstArgs
import BanlistFindManyArgs = Prisma.BanlistFindManyArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class BanlistModel {
  private dateFields: Array<keyof BanlistUpdateInput> = ['banStart', 'banEnd']

  constructor(private gameClient: GameClient) {}

  public async getBanlist(payload: BanlistFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.banlist.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getBanlistEl(payload: BanlistFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.banlist.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
