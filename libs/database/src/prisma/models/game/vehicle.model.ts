import { PrismaClient as GameClient, Prisma, Vehicle } from '@majestic-backend/prisma-client-game'

import VehicleUpdateInput = Prisma.VehicleUpdateInput
import VehicleFindFirstArgs = Prisma.VehicleFindFirstArgs
import VehicleFindManyArgs = Prisma.VehicleFindManyArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export interface IVehicle extends Omit<Vehicle, 'mileage'> {
  mileage: number
}

export class VehicleModel {
  private dateFields: Array<keyof VehicleUpdateInput> = ['date', 'endTime', 'rentUntil']

  constructor(private gameClient: GameClient) {}

  public async getVehicles(payload: VehicleFindManyArgs): Promise<IVehicle[]> {
    const res = fixDatesInReadMany(
      await this.gameClient.vehicle.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
    return res.map(el => ({
      ...el,
      mileage: el.mileage ? Number(el.mileage) : 0,
    }))
  }

  public async getVehicle(payload: VehicleFindFirstArgs): Promise<IVehicle> {
    const row = fixDatesInReadNullable(
      await this.gameClient.vehicle.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )

    return {
      ...row,
      mileage: row?.mileage ? Number(row.mileage) : 0,
      // temp
    } as IVehicle
  }
}
