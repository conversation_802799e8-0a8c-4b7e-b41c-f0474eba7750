import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import FractionLogUpdateInput = Prisma.FractionLogUpdateInput
import FractionLogFindManyArgs = Prisma.FractionLogFindManyArgs
import FractionLogFindFirstArgs = Prisma.FractionLogFindFirstArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class FractionLogModel {
  private dateFields: Array<keyof FractionLogUpdateInput> = ['date']

  constructor(private gameClient: GameClient) {}

  public async getFractionLogs(payload: FractionLogFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.fractionLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getFractionLog(payload: FractionLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.fractionLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
