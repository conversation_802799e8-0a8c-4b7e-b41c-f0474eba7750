import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import AdminLogUpdateInput = Prisma.AdminLogUpdateInput
import AdminLogFindManyArgs = Prisma.AdminLogFindManyArgs
import AdminLogFindFirstArgs = Prisma.AdminLogFindFirstArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class AdminLogModel {
  private dateFields: Array<keyof AdminLogUpdateInput> = ['date']

  constructor(private gameClient: GameClient) {}

  public async getAdminLogs(payload: AdminLogFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.adminLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getAdminLog(payload: AdminLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.adminLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
