import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import FamilyLogUpdateInput = Prisma.FamilyLogUpdateInput
import FamilyLogFindManyArgs = Prisma.FamilyLogFindManyArgs
import FamilyLogFindFirstArgs = Prisma.FamilyLogFindFirstArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class FamilyLogModel {
  private dateFields: Array<keyof FamilyLogUpdateInput> = ['date']

  constructor(private gameClient: GameClient) {}

  public async getFamilyLogs(payload: FamilyLogFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.familyLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getFamilyLog(payload: FamilyLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.familyLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
