import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import BankUpdateInput = Prisma.BankUpdateInput
import BankFindFirstArgs = Prisma.BankFindFirstArgs
import BankFindManyArgs = Prisma.BankFindManyArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class BankModel {
  private dateFields: Array<keyof BankUpdateInput> = ['estimatedDate']

  constructor(private gameClient: GameClient) {}

  public async getBank(payload: BankFindFirstArgs) {
    const res = fixDatesInReadNullable(
      await this.gameClient.bank.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
    if (!res) {
      return null
    }

    return {
      ...res,
      amount: res?.amount ? Number(res.amount) : 0,
    }
  }

  public async getBanks(payload: BankFindManyArgs) {
    const res = fixDatesInReadMany(await this.gameClient.bank.findMany(payload), this.dateFields)
    return res.map(el => ({
      ...el,
      amount: el.amount ? Number(el.amount) : 0,
    }))
  }
}
