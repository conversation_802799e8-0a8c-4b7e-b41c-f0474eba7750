import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import AttackOnTheArmyFindManyArgs = Prisma.AttackOnTheArmyFindManyArgs
import AttackOnTheArmyUpsertArgs = Prisma.AttackOnTheArmyUpsertArgs

export class AttackOnTheArmyModel {
  constructor(private gameClient: GameClient) {}

  public getAttacksOnTheArmy(payload?: AttackOnTheArmyFindManyArgs) {
    return this.gameClient.attackOnTheArmy.findMany(payload)
  }
  public updateAttacksOnTheArmy(payload: AttackOnTheArmyUpsertArgs) {
    return this.gameClient.attackOnTheArmy.upsert(payload)
  }
}
