import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import CaptureLogUpdateInput = Prisma.CaptureLogUpdateInput
import CaptureLogFindManyArgs = Prisma.CaptureLogFindManyArgs
import CaptureLogFindFirstArgs = Prisma.CaptureLogFindFirstArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class CaptureLogModel {
  private dateFields: Array<keyof CaptureLogUpdateInput> = ['startAt', 'endAt']

  constructor(private gameClient: GameClient) {}

  public async getCaptureLogs(payload: CaptureLogFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.captureLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getCaptureLog(payload: CaptureLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.captureLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
