import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import {
  fixDatesInRead,
  fixDatesInReadMany,
  fixDatesInReadNullable,
  fixDatesInWrite,
  fixDatesInFindArgs,
} from '../../../utils/date'
import UnitpayPaymentUpdateInput = Prisma.UnitpayPaymentUpdateInput
import UnitpayPaymentFindFirstArgs = Prisma.UnitpayPaymentFindFirstArgs
import UnitpayPaymentFindManyArgs = Prisma.UnitpayPaymentFindManyArgs
import UnitpayPaymentCreateInput = Prisma.UnitpayPaymentCreateInput
import UnitpayPaymentUpdateArgs = Prisma.UnitpayPaymentUpdateArgs
import UnitpayPaymentDeleteArgs = Prisma.UnitpayPaymentDeleteArgs

export class UnitpayPaymentModel {
  private dateFields: Array<keyof UnitpayPaymentUpdateInput> = ['dateComplete', 'dateCreate']

  constructor(private gameClient: GameClient) {}

  public async createUnitpayPayment(data: UnitpayPaymentCreateInput) {
    return this.gameClient.unitpayPayment.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }

  public async updateUnitpayPayments(payload: UnitpayPaymentUpdateArgs) {
    return fixDatesInRead(
      await this.gameClient.unitpayPayment.update({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }

  public async getUnitpayPayments(payload: UnitpayPaymentFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.unitpayPayment.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getUnitpayPayment(payload: UnitpayPaymentFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.unitpayPayment.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async deleteUnitpayPayment(data: UnitpayPaymentDeleteArgs) {
    return this.gameClient.unitpayPayment.delete(data)
  }
}
