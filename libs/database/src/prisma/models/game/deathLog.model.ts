import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import DeathLogUpdateInput = Prisma.DeathLogUpdateInput
import DeathLogFindManyArgs = Prisma.DeathLogFindManyArgs
import DeathLogFindFirstArgs = Prisma.DeathLogFindFirstArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class DeathLogModel {
  private dateFields: Array<keyof DeathLogUpdateInput> = ['date']

  constructor(private gameClient: GameClient) {}

  public async getDeathLogs(payload: DeathLogFindManyArgs) {
    const res = fixDatesInReadMany(
      await this.gameClient.deathLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
    return res.map(el => ({
      ...el,
      id: Number(el.id),
      killerAccountId: el.killerAccountId ? Number(el.killerAccountId) : undefined,
      killedAccountId: el.killedAccountId ? Number(el.killedAccountId) : undefined,
    }))
  }

  public async getDeathLog(payload: DeathLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.deathLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
