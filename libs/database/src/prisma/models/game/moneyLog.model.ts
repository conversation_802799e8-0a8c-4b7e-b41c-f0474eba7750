import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import MoneyLogUpdateInput = Prisma.MoneyLogUpdateInput
import MoneyLogFindManyArgs = Prisma.MoneyLogFindManyArgs
import MoneyLogFindFirstArgs = Prisma.MoneyLogFindFirstArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class MoneyLogModel {
  private dateFields: Array<keyof MoneyLogUpdateInput> = ['date']

  constructor(private gameClient: GameClient) {}

  public async getMoneyLogs(payload: MoneyLogFindManyArgs) {
    const res = fixDatesInReadMany(
      await this.gameClient.moneyLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
    return res.map(el => ({
      ...el,
      id: Number(el.id),
      amount: el.amount ? Number(el.amount) : undefined,
      prev: el.prev ? Number(el.prev) : undefined,
      unitPrice: el.unitPrice ? Number(el.unitPrice) : undefined,
    }))
  }

  public async getMoneyLog(payload: MoneyLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.moneyLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
