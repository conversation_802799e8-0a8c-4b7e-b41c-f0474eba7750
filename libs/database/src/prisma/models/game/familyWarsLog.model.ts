import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import FamilyWarsLogUpdateInput = Prisma.FamilyWarsLogUpdateInput
import FamilyWarsLogFindManyArgs = Prisma.FamilyWarsLogFindManyArgs
import FamilyWarsLogFindFirstArgs = Prisma.FamilyWarsLogFindFirstArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class FamilyWarsLogModel {
  private dateFields: Array<keyof FamilyWarsLogUpdateInput> = ['startAt', 'endAt']

  constructor(private gameClient: GameClient) {}

  public async getFamilyWarsLogs(payload: FamilyWarsLogFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.familyWarsLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getFamilyWarsLog(payload: FamilyWarsLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.familyWarsLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
