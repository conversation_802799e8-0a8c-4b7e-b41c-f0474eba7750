import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import PlantWeedTimeFindFirstArgs = Prisma.PlantWeedTimeFindFirstArgs
import PlantWeedTimeUpsertArgs = Prisma.PlantWeedTimeUpsertArgs

export class PlantWeedTimeModel {
  constructor(private gameClient: GameClient) {}

  public getPlantWeedTime(payload?: PlantWeedTimeFindFirstArgs) {
    return this.gameClient.plantWeedTime.findFirst(payload)
  }

  public updatePlantWeedTime(payload: PlantWeedTimeUpsertArgs) {
    return this.gameClient.plantWeedTime.upsert(payload)
  }
}
