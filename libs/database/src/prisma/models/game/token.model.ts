import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import { fixDatesInRead, fixDatesInReadNullable, fixDatesInWrite } from '../../../utils/date'

import TokenUpdateInput = Prisma.TokenUpdateInput

type tokenTypes = 'website'

export class TokenModel {
  private dateFields: Array<keyof TokenUpdateInput> = ['updatedAt', 'createdAt']

  constructor(private gameClient: GameClient) {}

  public async findToken(token: string) {
    return fixDatesInReadNullable(
      await this.gameClient.token.findFirst({
        where: {
          token,
        },
      }),
      this.dateFields,
    )
  }

  public createToken(userId: number, type: tokenTypes, token: string, ip: string) {
    return this.gameClient.token.create({
      data: fixDatesInWrite(
        {
          accountId: userId,
          characterId: 0,
          lastIp: '',
          type,
          token,
          ip,
          updatedAt: new Date(),
          createdAt: new Date(),
        },
        this.dateFields,
      ),
    })
  }

  public async removeToken(token: string) {
    return fixDatesInRead(
      await this.gameClient.token.delete({
        where: {
          token,
        },
      }),
      this.dateFields,
    )
  }

  public async updateActivity(id: any, ip: any) {
    return fixDatesInRead(
      await this.gameClient.token.update({
        where: {
          id,
        },
        data: fixDatesInWrite(
          {
            updatedAt: new Date(),
            lastIp: ip,
          },
          this.dateFields as any,
        ),
      }),
      this.dateFields,
    )
  }
}
