import { PrismaClient as GameClient, Prisma, User } from '@majestic-backend/prisma-client-game'
import * as bcrypt from 'bcrypt'
import UserCreateInput = Prisma.UserCreateInput
import UserUpdateInput = Prisma.UserUpdateInput
import UserFindFirstArgs = Prisma.UserFindFirstArgs
import {
  fixDatesInReadNullable,
  fixDatesInRead,
  fixDatesInWrite,
  fixDatesInFindArgs,
  fixDatesInReadMany,
} from '../../../utils/date'

export class UserModel {
  private dateFields: Array<keyof UserUpdateInput> = ['createdAt', 'lastVisit', 'pixelBattleTime']

  constructor(private gameClient: GameClient) {}

  public async createUser(data: UserCreateInput): Promise<User> {
    const row = await this.gameClient.user.create({
      data: fixDatesInWrite(
        {
          ...data,
          password: await bcrypt.hash(data.password as string, 5),
          acceptEmail: true,
          isConfirm: false,
        },
        this.dateFields,
      ),
    })

    return fixDatesInRead(row, this.dateFields)
  }

  public async getUser(payload: UserFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.user.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async findExistsUsers({
    login,
    email,
    createdIP,
    promocode,
  }: {
    login: string
    email: string
    createdIP: string
    promocode?: string
  }): Promise<User[]> {
    const whereObject: Prisma.UserWhereInput[] = [{ login }, { email }, { createdIP }]

    if (typeof promocode === 'string' && promocode.length) {
      whereObject.push({
        inviteCode: {
          contains: promocode.toLowerCase(),
        },
      })
    }

    const resultExistsUsers = await this.gameClient.user.findMany({
      where: {
        OR: whereObject,
      },
    })

    return fixDatesInReadMany(resultExistsUsers, this.dateFields)
  }

  public async findUsersByIds(userIds: number[]) {
    const usersByLogins = await this.gameClient.user.findMany({
      where: {
        id: { in: userIds },
      },
    })

    return fixDatesInReadMany(usersByLogins, this.dateFields)
  }

  public async getUserByInviteCode(inviteCode: string) {
    return fixDatesInReadNullable(
      await this.gameClient.user.findFirst({
        where: {
          inviteCode: inviteCode.toLowerCase(),
        },
      }),
      this.dateFields,
    )
  }

  public async updateInviteCode(id: number, promoCode: string) {
    return fixDatesInRead(
      await this.gameClient.user.update({
        where: {
          id,
        },
        data: {
          inviteCode: promoCode,
        },
      }),
      this.dateFields,
    )
  }

  public async getUserByRegisterCode(registerCode: string) {
    return fixDatesInReadNullable(
      await this.gameClient.user.findFirst({
        where: {
          registerCode,
        },
      }),
      this.dateFields,
    )
  }

  public async confirmEmailById(id: number) {
    return fixDatesInRead(
      await this.gameClient.user.update({
        where: {
          id,
        },
        data: {
          isConfirm: true,
        },
      }),
      this.dateFields,
    )
  }

  public async updateDonate(id: number, donate: number) {
    return fixDatesInRead(
      await this.gameClient.user.update({
        where: {
          id,
        },
        data: {
          donate,
        },
      }),
      this.dateFields,
    )
  }
}
