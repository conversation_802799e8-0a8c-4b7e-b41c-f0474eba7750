import { PrismaClient as GameClient, Prisma, Promocode } from '@majestic-backend/prisma-client-game'
import PromocodeCreateInput = Prisma.PromocodeCreateInput
import PromocodeUpdateInput = Prisma.PromocodeUpdateInput
import { fixDatesInWrite } from '../../../utils/date'

export class PromoCodeModel {
  private dateFields: Array<keyof PromocodeUpdateInput> = ['date']

  constructor(private gameClient: GameClient) {}

  public createPromoCode(data: PromocodeCreateInput): Promise<Promocode> {
    return this.gameClient.promocode.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }
}
