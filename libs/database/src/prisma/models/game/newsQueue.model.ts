import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import NewsQueueUpdateInput = Prisma.NewsQueueUpdateInput
import NewsQueueFindManyArgs = Prisma.NewsQueueFindManyArgs
import NewsQueueFindFirstArgs = Prisma.NewsQueueFindFirstArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class NewsQueueModel {
  private dateFields: Array<keyof NewsQueueUpdateInput> = ['createdAt', 'deployAt', 'updatedAt']

  constructor(private gameClient: GameClient) {}

  public async getNewsQueue(payload: NewsQueueFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.newsQueue.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getNewsQueueEl(payload: NewsQueueFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.newsQueue.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
