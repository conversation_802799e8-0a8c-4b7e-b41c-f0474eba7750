import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import CaptureEventsLogUpdateInput = Prisma.CaptureEventsLogUpdateInput
import CaptureEventsLogFindManyArgs = Prisma.CaptureEventsLogFindManyArgs
import { fixDatesInFindArgs, fixDatesInReadMany } from '../../../utils/date'

export class CaptureEventsLogModel {
  private dateFields: Array<keyof CaptureEventsLogUpdateInput> = ['createdAt']

  constructor(private gameClient: GameClient) {}

  public async getCaptureEventsLogs(payload: CaptureEventsLogFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.captureEventsLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
