import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import ItemLogUpdateInput = Prisma.ItemLogUpdateInput
import ItemLogFindManyArgs = Prisma.ItemLogFindManyArgs
import ItemLogFindFirstArgs = Prisma.ItemLogFindFirstArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class ItemLogModel {
  private dateFields: Array<keyof ItemLogUpdateInput> = ['date']

  constructor(private gameClient: GameClient) {}

  public async getItemLogs(payload: ItemLogFindManyArgs) {
    const res = fixDatesInReadMany(
      await this.gameClient.itemLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )

    return res.map(el => ({ ...el, id: Number(el.id) }))
  }

  public async getItemLog(payload: ItemLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.itemLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
