import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import SessionLogUpdateInput = Prisma.SessionLogUpdateInput
import SessionLogFindFirstArgs = Prisma.SessionLogFindFirstArgs
import SessionLogFindManyArgs = Prisma.SessionLogFindManyArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class SessionLogModel {
  private dateFields: Array<keyof SessionLogUpdateInput> = ['enterDate', 'exitDate']

  constructor(private gameClient: GameClient) {}

  public async getSessionLogs(payload: SessionLogFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.sessionLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getSessionLog(payload: SessionLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.sessionLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
