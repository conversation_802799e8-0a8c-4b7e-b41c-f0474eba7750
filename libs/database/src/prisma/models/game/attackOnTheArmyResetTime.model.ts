import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import AttackOnTheArmyResetTimeFindFirstArgs = Prisma.AttackOnTheArmyResetTimeFindFirstArgs
import AttackOnTheArmyResetTimeUpsertArgs = Prisma.AttackOnTheArmyResetTimeUpsertArgs

export class AttackOnTheArmyResetTimeModel {
  constructor(private gameClient: GameClient) {}

  public getAttackOnTheArmyResetTime(payload?: AttackOnTheArmyResetTimeFindFirstArgs) {
    return this.gameClient.attackOnTheArmyResetTime.findFirst(payload)
  }
  public updateAttackOnTheArmyResetTime(payload: AttackOnTheArmyResetTimeUpsertArgs) {
    return this.gameClient.attackOnTheArmyResetTime.upsert(payload)
  }
}
