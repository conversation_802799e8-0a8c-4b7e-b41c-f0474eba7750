import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import WhiteListFindManyArgs = Prisma.WhiteListFindManyArgs
import WhiteListFindFirstArgs = Prisma.WhiteListFindFirstArgs
import WhiteListCreateArgs = Prisma.WhiteListCreateArgs
import WhiteListUpdateArgs = Prisma.WhiteListUpdateArgs
import WhiteListDeleteArgs = Prisma.WhiteListDeleteArgs

export class WhiteListModel {
  constructor(private gameClient: GameClient) {}

  public getWhiteList(payload?: WhiteListFindManyArgs) {
    return this.gameClient.whiteList.findMany(payload)
  }
  public getWhiteListEl(payload?: WhiteListFindFirstArgs) {
    return this.gameClient.whiteList.findFirst(payload)
  }
  public createWhiteList(payload: WhiteListCreateArgs) {
    return this.gameClient.whiteList.create(payload)
  }
  public updateWhiteList(payload: WhiteListUpdateArgs) {
    return this.gameClient.whiteList.update(payload)
  }
  public deleteWhiteListEl(payload: WhiteListDeleteArgs) {
    return this.gameClient.whiteList.delete(payload)
  }
}
