import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import AttackOnTheArmyTimeFindManyArgs = Prisma.AttackOnTheArmyTimeFindManyArgs
import AttackOnTheArmyTimeUpsertArgs = Prisma.AttackOnTheArmyTimeUpsertArgs

export class AttackOnTheArmyTimeModel {
  constructor(private gameClient: GameClient) {}

  public getAttacksOnTheArmyTime(payload?: AttackOnTheArmyTimeFindManyArgs) {
    return this.gameClient.attackOnTheArmyTime.findMany(payload)
  }

  public updateAttacksOnTheArmyTime(payload: AttackOnTheArmyTimeUpsertArgs) {
    return this.gameClient.attackOnTheArmyTime.upsert(payload)
  }
}
