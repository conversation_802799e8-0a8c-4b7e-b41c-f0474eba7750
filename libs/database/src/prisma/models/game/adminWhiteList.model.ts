import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import AdminWhiteListFindManyArgs = Prisma.AdminWhiteListFindManyArgs
import AdminWhiteListCreateArgs = Prisma.AdminWhiteListCreateArgs
import AdminWhiteListUpdateArgs = Prisma.AdminWhiteListUpdateArgs
import AdminWhiteListDeleteArgs = Prisma.AdminWhiteListDeleteArgs

export class AdminWhiteListModel {
  constructor(private gameClient: GameClient) {}

  public getAdminWhiteList(payload?: AdminWhiteListFindManyArgs) {
    return this.gameClient.adminWhiteList.findMany(payload)
  }
  public createAdminWhiteList(payload: AdminWhiteListCreateArgs) {
    return this.gameClient.adminWhiteList.create(payload)
  }
  public updateAdminWhiteList(payload: AdminWhiteListUpdateArgs) {
    return this.gameClient.adminWhiteList.update(payload)
  }
  public deleteAdminWhiteListEl(payload: AdminWhiteListDeleteArgs) {
    return this.gameClient.adminWhiteList.delete(payload)
  }
}
