import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import ItemTradeLogUpdateInput = Prisma.ItemTradeLogUpdateInput
import ItemTradeLogFindManyArgs = Prisma.ItemTradeLogFindManyArgs
import ItemTradeLogFindFirstArgs = Prisma.ItemTradeLogFindFirstArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export class ItemTradeLogModel {
  private dateFields: Array<keyof ItemTradeLogUpdateInput> = ['date']

  constructor(private gameClient: GameClient) {}

  public async getTradeItemLogs(payload: ItemTradeLogFindManyArgs) {
    const res = fixDatesInReadMany(
      await this.gameClient.itemTradeLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
    return res.map(el => ({
      ...el,
      amount1: el.amount1 ? Number(el.amount1) : undefined,
      amount2: el.amount2 ? Number(el.amount2) : undefined,
    }))
  }

  public async getTradeItemLog(payload: ItemTradeLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.itemTradeLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }
}
