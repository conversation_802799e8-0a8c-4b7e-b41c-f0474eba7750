import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import ReportCreateInput = Prisma.ReportCreateInput
import ReportUpdateInput = Prisma.ReportUpdateInput
import ReportFindFirstArgs = Prisma.ReportFindFirstArgs
import ReportFindManyArgs = Prisma.ReportFindManyArgs
import ReportUpdateArgs = Prisma.ReportUpdateArgs
import ReportCountArgs = Prisma.ReportCountArgs
import {
  fixDatesInRead,
  fixDatesInReadMany,
  fixDatesInReadNullable,
  fixDatesInWrite,
} from '../../../utils/date'

export class ReportModel {
  public dateFields: Array<keyof ReportUpdateInput> = [
    'date',
    'firstAnswerAt',
    'lastAnswerAt',
    'lastQuestionAt',
    'closedAt',
  ]

  constructor(private gameClient: GameClient) {}

  public async getReportsListByAccountId(accountId: string | number) {
    const rows = await this.gameClient.$queryRaw`
    SELECT
        reports.*,
        report_messages.message  AS lastMessage,
        report_messages.type     AS lastMessageType,
        report_messages.nickname AS lastMessageNickName
      FROM
        reports
      LEFT JOIN
        report_messages
      ON
        report_messages.reportId = reports.id AND
        report_messages.id = (
          SELECT
            MAX(report_messages.id)
          FROM
            report_messages
          WHERE
            report_messages.reportId = reports.id AND
            report_messages.message != '' AND
            (
              report_messages.type = 'answer' OR
              report_messages.type = 'question'
            )
        )
      WHERE
        reports.characterId = ${accountId} AND
        reports.type = 'default'
      GROUP BY
        reports.id
      ORDER BY
        reports.id DESC
    `

    return fixDatesInReadMany(rows as ReportCreateInput[], this.dateFields) as unknown[]
  }

  async getAdminReportsByAccountId(accountId: string | number) {
    const rows = await this.gameClient.$queryRaw`
    SELECT
          reports.*,
          report_messages.message  AS lastMessage,
          report_messages.type     AS lastMessageType,
          report_messages.nickname AS lastMessageNickName
        FROM
          reports
        LEFT JOIN
          report_messages
        ON
          report_messages.reportId = reports.id AND
          report_messages.id = (
            SELECT
              MAX(report_messages.id)
            FROM
              report_messages
            WHERE
              report_messages.reportId = reports.id AND
              report_messages.message != '' AND
              (
                report_messages.type = 'answer' OR
                report_messages.type = 'question'
              )
          )
        WHERE
          reports.type = 'default' AND
          reports.status != 'closed' AND
          reports.characterId != ${accountId} AND
          (
            reports.exceptionId IS NULL OR
            reports.exceptionId = '' OR
            reports.exceptionId != ${accountId}
          ) AND
          (
            reports.admin IS NULL OR
            reports.admin = '' OR
            reports.admin = ${accountId}
          ) AND
          (
            reports.selected IS NULL OR
            reports.selected = '' OR
            reports.selected = ${accountId}
          )
        GROUP BY
          reports.id
    `

    return fixDatesInReadMany(rows as ReportCreateInput[], this.dateFields) as unknown[]
  }

  public createReport(data: ReportCreateInput) {
    return this.gameClient.report.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }

  public async getReport(data: ReportFindFirstArgs) {
    return fixDatesInReadNullable(await this.gameClient.report.findFirst(data), this.dateFields)
  }

  public async getReports(data: ReportFindManyArgs) {
    return fixDatesInReadMany(await this.gameClient.report.findMany(data), this.dateFields)
  }

  public async updateReport(data: ReportUpdateArgs) {
    return fixDatesInRead(
      await this.gameClient.report.update({
        ...data,
        data: fixDatesInWrite(data.data, this.dateFields),
      }),
      this.dateFields,
    )
  }

  public getActiveReportsCount(): Promise<number> {
    return this.gameClient.report.count({
      where: {
        type: 'default',
        status: {
          not: 'closed',
        },
      },
    })
  }

  public getReportsCount(data: ReportCountArgs) {
    return this.gameClient.report.count(data)
  }
}
