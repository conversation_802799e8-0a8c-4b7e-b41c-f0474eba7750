import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import ReportMessageCreateInput = Prisma.ReportMessageCreateInput
import ReportMessageUpdateInput = Prisma.ReportMessageUpdateInput
import ReportMessageFindManyArgs = Prisma.ReportMessageFindManyArgs
import ReportMessageFindFirstArgs = Prisma.ReportMessageFindFirstArgs
import { fixDatesInRead, fixDatesInWrite } from '../../../utils/date'

export class ReportMessageModel {
  public dateFields: Array<keyof ReportMessageUpdateInput> = ['date']

  constructor(private gameClient: GameClient) {}

  public createReportMessage(data: ReportMessageCreateInput) {
    return this.gameClient.reportMessage.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }

  public async getReportMessages(data: ReportMessageFindManyArgs) {
    const rows = await this.gameClient.reportMessage.findMany(data)
    return rows.map(row => fixDatesInRead(row, this.dateFields))
  }

  public async getReportMessage(data: ReportMessageFindFirstArgs) {
    const row = await this.gameClient.reportMessage.findFirst(data)
    if (!row) return row

    return fixDatesInRead(row, this.dateFields)
  }
}
