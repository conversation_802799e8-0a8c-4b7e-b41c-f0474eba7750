import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import SpecialFindFirstArgs = Prisma.SpecialFindFirstArgs
import SpecialFindManyArgs = Prisma.SpecialFindManyArgs

export class SpecialModel {
  constructor(private gameClient: GameClient) {}

  public getSpecials(payload: SpecialFindManyArgs) {
    return this.gameClient.special.findMany(payload)
  }

  public getSpecial(payload: SpecialFindFirstArgs) {
    return this.gameClient.special.findFirst(payload)
  }
}
