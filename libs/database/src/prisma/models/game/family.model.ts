import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'

import FamilyUpdateInput = Prisma.FamilyUpdateInput
import FamilyFindManyArgs = Prisma.FamilyFindManyArgs
import FamilyFindFirstArgs = Prisma.FamilyFindFirstArgs
import FamilyUpdateArgs = Prisma.FamilyUpdateArgs
import {
  fixDatesInReadMany,
  fixDatesInReadNullable,
  fixDatesInRead,
  fixDatesInWrite,
  fixDatesInFindArgs,
} from '../../../utils/date'

export class FamilyModel {
  private dateFields: Array<keyof FamilyUpdateInput> = [
    'createdAt',
    'lastUpdated',
    'removedDate',
    'block',
    'capturesBlock',
  ]

  constructor(private gameClient: GameClient) {}

  async getFamilies(payload: FamilyFindManyArgs) {
    const rows = fixDatesInReadMany(
      await this.gameClient.family.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
    return rows.map(el => ({
      ...el,
      money: el?.money ? Number(el.money) || 0 : undefined,
    }))
  }

  async getFamily(payload: FamilyFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.family.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  async updateFamily(payload: FamilyUpdateArgs) {
    return fixDatesInRead(
      await this.gameClient.family.update({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }
}
