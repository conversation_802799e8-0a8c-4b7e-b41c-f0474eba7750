import {
  PrismaClient as GameClient,
  Prisma,
  Subscriptions,
} from '@majestic-backend/prisma-client-game'
import SubscriptionsUpdateInput = Prisma.SubscriptionsUpdateInput
import SubscriptionsUpdateArgs = Prisma.SubscriptionsUpdateArgs
import SubscriptionsFindManyArgs = Prisma.SubscriptionsFindManyArgs
import SubscriptionsFindFirstArgs = Prisma.SubscriptionsFindFirstArgs
import SubscriptionsCreateInput = Prisma.SubscriptionsCreateInput
import {
  fixDatesInRead,
  fixDatesInWrite,
  fixDatesInFindArgs,
  fixDatesInReadMany,
  fixDatesInReadNullable,
} from '../../../utils/date'

export class SubscriptionModel {
  private dateFields: Array<keyof SubscriptionsUpdateInput> = ['start', 'end']

  constructor(private gameClient: GameClient) {}

  public async createSubscription(data: SubscriptionsCreateInput): Promise<Subscriptions> {
    const row = await this.gameClient.subscriptions.create({
      data: fixDatesInWrite(data, this.dateFields),
    })

    return fixDatesInRead(row, this.dateFields)
  }

  public async getSubscription(payload: SubscriptionsFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.subscriptions.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async findNeedPaySubscriptions(date: Date) {
    const payload: SubscriptionsFindManyArgs = {
      where: {
        end: {
          lte: date,
        },
        OR: [
          {
            rebillId: {
              not: null,
            },
          },
          {
            accountTokenQr: {
              not: null,
            },
          },
        ],
      },
    }

    return fixDatesInReadMany(
      await this.gameClient.subscriptions.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async findSubscriptionsByEndDate(endDayStart: Date, endDayEnd: Date) {
    const payload: SubscriptionsFindManyArgs = {
      where: {
        end: {
          gte: endDayStart,
          lt: endDayEnd,
        },
      },
    }

    return fixDatesInReadMany(
      await this.gameClient.subscriptions.findMany(payload),
      this.dateFields,
    )
  }

  public async updateSubscriptionData(payload: SubscriptionsUpdateArgs) {
    return fixDatesInRead(
      await this.gameClient.subscriptions.update({
        ...payload,
        data: fixDatesInWrite(payload.data, this.dateFields),
      }),
      this.dateFields,
    )
  }
}
