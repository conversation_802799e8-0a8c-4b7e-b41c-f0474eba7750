import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import UserUpdateInput = Prisma.UserUpdateInput
import { fixDatesInRead, fixDatesInWrite } from '../../../utils/date'

export class PixelBattleModel {
  private dateFields: Array<keyof UserUpdateInput> = ['createdAt', 'lastVisit', 'pixelBattleTime']

  constructor(private gameClient: GameClient) {}

  public async decrementPixelBattleCount(id: number) {
    const user = await this.gameClient.user.findFirst({
      where: { id },
      select: { pixelBattleCount: true },
    })

    if (!user) {
      return
    }

    return fixDatesInRead(
      await this.gameClient.user.update({
        where: {
          id,
        },
        data: {
          pixelBattleCount: user.pixelBattleCount ? user.pixelBattleCount - 1 : 0,
        },
      }),
      this.dateFields,
    )
  }

  public async updatePixelBattleCooldown(id: number, time: string) {
    return fixDatesInRead(
      await this.gameClient.user.update({
        where: {
          id,
        },
        data: fixDatesInWrite(
          {
            pixelBattleTime: time,
          },
          ['pixelBattleTime'],
        ),
      }),
      this.dateFields,
    )
  }
}
