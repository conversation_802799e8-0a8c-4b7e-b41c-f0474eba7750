import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import FractionFindManyArgs = Prisma.FractionFindManyArgs
import FractionFindFirstArgs = Prisma.FractionFindFirstArgs

export class FractionModel {
  constructor(private gameClient: GameClient) {}

  // very ugly thing
  public getOrganisationsList() {
    return this.gameClient.$queryRaw`
        SELECT
          fractions.title AS name,
          fractions.id AS id,
          fractions.awardCount AS bonus_count,
          COUNT(DISTINCT IF(a2.online = 1, a2.id, NULL)) AS org_online,
          a1.login AS leader_name,
          a1.dayOnline AS leader_online
        FROM
          fractions
        LEFT JOIN accounts AS a1 ON a1.id = fractions.leaderId
        LEFT JOIN accounts AS a2 ON a2.member = fractions.id
        GROUP BY fractions.id
        ORDER BY fractions.id ASC
        `.then(res => {
      if (Array.isArray(res)) {
        return res.map(el => ({ ...el, org_online: Number(el.org_online) || 0 }))
      }
      return null
    })
  }

  public getOrganisation(payload: FractionFindFirstArgs) {
    return this.gameClient.fraction.findFirst(payload).then(res => ({
      ...res,
      money: res?.money ? Number(res.money) || 0 : undefined,
    }))
  }
  public getOrganisations(payload: FractionFindManyArgs) {
    return this.gameClient.fraction.findMany(payload).then(res =>
      res.map(el => ({
        ...el,
        money: el?.money ? Number(el.money) || 0 : undefined,
      })),
    )
  }
}
