import { PrismaClient as GameClient, Prisma, Account } from '@majestic-backend/prisma-client-game'
import AccountUpdateInput = Prisma.AccountUpdateInput
import AccountFindFirstArgs = Prisma.AccountFindFirstArgs
import AccountFindManyArgs = Prisma.AccountFindManyArgs
import { fixDatesInFindArgs, fixDatesInReadMany, fixDatesInReadNullable } from '../../../utils/date'

export interface IAccount extends Omit<Account, 'cash'> {
  cash: number
}

export class AccountModel {
  private dateFields: Array<keyof AccountUpdateInput> = ['createdAt', 'lastVisit']

  constructor(private gameClient: GameClient) {}

  public async getAccount(payload: AccountFindFirstArgs): Promise<IAccount | null> {
    const row = fixDatesInReadNullable(
      await this.gameClient.account.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
    if (!row) {
      return null
    }

    return {
      ...row,
      cash: row?.cash ? Number(row.cash) : undefined,
    } as IAccount
  }

  public async getAccounts(payload: AccountFindManyArgs): Promise<IAccount[]> {
    const rows = fixDatesInReadMany(
      await this.gameClient.account.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )

    return rows.map(el => ({
      ...el,
      cash: el.cash ? Number(el.cash) : undefined,
    })) as IAccount[]
  }

  public async getAccountByUserId(userId: number): Promise<Account[]> {
    return fixDatesInReadMany(
      await this.gameClient.account.findMany({
        where: {
          userId,
        },
      }),
      this.dateFields,
    )
  }

  public async getAdminsIds(where: AccountFindManyArgs['where'] = {}) {
    const adminsData = await this.gameClient.account.findMany({
      where: {
        admin: {
          gt: 0,
        },
        ...where,
      },
      select: {
        id: true,
      },
    })

    return [...new Set(adminsData.map(adminData => adminData.id))]
  }

  public async getOnlineIds() {
    const chars = await this.gameClient.account.findMany({
      where: { online: { gt: 0 } },
      select: { id: true, online: true },
    })

    return chars.reduce((acc, cur) => {
      acc[cur.id] = cur.online
      return acc
    }, {} as Record<string, unknown>)
  }
}
