import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import DonateLogUpdateInput = Prisma.DonateLogUpdateInput
import DonateLogFindManyArgs = Prisma.DonateLogFindManyArgs
import DonateLogFindFirstArgs = Prisma.DonateLogFindFirstArgs
import DonateLogCreateInput = Prisma.DonateLogCreateInput
import {
  fixDatesInFindArgs,
  fixDatesInReadMany,
  fixDatesInReadNullable,
  fixDatesInWrite,
} from '../../../utils/date'

export class DonateLogModel {
  private dateFields: Array<keyof DonateLogUpdateInput> = ['date']

  constructor(private gameClient: GameClient) {}

  public async getDonateLogs(payload: DonateLogFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.donateLog.findMany(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async getDonateLog(payload: DonateLogFindFirstArgs) {
    return fixDatesInReadNullable(
      await this.gameClient.donateLog.findFirst(fixDatesInFindArgs(payload, this.dateFields)),
      this.dateFields,
    )
  }

  public async createDonateLog(data: DonateLogCreateInput) {
    return this.gameClient.donateLog.create({
      data: fixDatesInWrite(data, this.dateFields),
    })
  }
}
