import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import ItemFindFirstArgs = Prisma.ItemFindFirstArgs
import ItemFindManyArgs = Prisma.ItemFindManyArgs

export class ItemModel {
  constructor(private gameClient: GameClient) {}

  public getItems(payload: ItemFindManyArgs) {
    return this.gameClient.item.findMany(payload)
  }

  public getItem(payload: ItemFindFirstArgs) {
    return this.gameClient.item.findFirst(payload)
  }
}
