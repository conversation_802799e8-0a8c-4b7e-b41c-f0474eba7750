import { PrismaClient as GameClient, Prisma } from '@majestic-backend/prisma-client-game'
import FamilyWarsEventsLogUpdateInput = Prisma.FamilyWarsEventsLogUpdateInput
import FamilyWarsEventsLogFindManyArgs = Prisma.FamilyWarsEventsLogFindManyArgs
import { fixDatesInFindArgs, fixDatesInReadMany } from '../../../utils/date'

export class FamilyWarsEventsLogModel {
  private dateFields: Array<keyof FamilyWarsEventsLogUpdateInput> = ['createdAt']

  constructor(private gameClient: GameClient) {}

  public async getFamilyWarsEventsLogs(payload: FamilyWarsEventsLogFindManyArgs) {
    return fixDatesInReadMany(
      await this.gameClient.familyWarsEventsLog.findMany(
        fixDatesInFindArgs(payload, this.dateFields),
      ),
      this.dateFields,
    )
  }
}
