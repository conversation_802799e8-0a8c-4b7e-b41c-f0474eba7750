// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`fixDatesInFindArgs should fix dates in find args with array fields 1`] = `
{
  "where": {
    "date": {
      "in": [
        2022-01-01T03:00:00.000Z,
        2022-01-02T03:00:00.000Z,
      ],
    },
  },
}
`;

exports[`fixDatesInFindArgs should fix dates in find args with plain fields 1`] = `
{
  "where": {
    "date": {
      "gte": 2022-01-01T03:00:00.000Z,
      "lte": 2022-01-02T03:00:00.000Z,
    },
  },
}
`;

exports[`fixDatesInFindArgs should not throw if field value is not provided 1`] = `
{
  "where": {},
}
`;

exports[`fixDatesInFindArgs should not throw if where is not provided 1`] = `{}`;

exports[`fixDatesInRead should fix dates in read payload 1`] = `
{
  "date": 2021-12-31T21:00:00.000Z,
  "otherProp": "someValue",
}
`;

exports[`fixDatesInReadMany should fix dates in read payload array 1`] = `
[
  {
    "date": 2021-12-31T21:00:00.000Z,
    "otherProp": "someValue",
  },
  {
    "date": 2021-12-31T21:00:00.000Z,
    "otherProp": "otherValue",
  },
]
`;

exports[`fixDatesInReadNullable should fix dates in read nullable payload 1`] = `
{
  "date": 2021-12-31T21:00:00.000Z,
  "otherProp": "someValue",
}
`;

exports[`fixDatesInWrite should fix dates in write payload 1`] = `
{
  "date": 2022-01-01T03:00:00.000Z,
  "otherProp": "someValue",
}
`;
