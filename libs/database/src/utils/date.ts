import { Prisma } from '@majestic-backend/prisma-client-backend'
// TODO: Temp fix, need to use config service, but it is async
const DATABASE_TIME_OFFSET = 10800000

type FilterKeys = keyof Prisma.DateTimeNullableFilter
const findArgsPlainFields: FilterKeys[] = ['equals', 'lt', 'lte', 'gt', 'gte', 'not']
const findArgsArrayFields: FilterKeys[] = ['in', 'notIn']

const fixField = (value: unknown, change: number): Date | unknown => {
  if (!value) return value

  if (value instanceof Date) {
    return new Date(value.valueOf() + change)
  } else if (typeof value === 'number' && !Number.isNaN(value) && Number.isFinite(value)) {
    return new Date(value + change)
  } else if (typeof value === 'string') {
    return new Date(new Date(value).valueOf() + change)
  }

  return value
}

export const fixDatesInRead = <T extends object, K extends keyof T>(payload: T, fields: K[]): T => {
  for (const field of fields) {
    try {
      const newValue = fixField(payload[field], -DATABASE_TIME_OFFSET)
      payload[field] = newValue as T[K]
    } catch (e: any) {
      throw new Error(`Could not fix date in field ${field.toString()}: ${e.toString()}`)
    }
  }

  return payload
}

export const fixDatesInReadMany = <T extends object, K extends keyof T>(
  payload: T[],
  fields: K[],
): T[] => {
  return payload.map(row => fixDatesInRead(row, fields))
}

export const fixDatesInReadNullable = <T extends object, K extends keyof T>(
  payload: T | null,
  fields: K[],
): T | null => {
  return payload ? fixDatesInRead(payload, fields) : payload
}

export const fixDatesInWrite = <T extends object, K extends keyof T>(
  payload: T,
  fields: K[],
): T => {
  for (const field of fields) {
    try {
      const newValue = fixField(payload[field], DATABASE_TIME_OFFSET)
      payload[field] = newValue as T[K]
    } catch (e: any) {
      throw new Error(`Could not fix date in field ${field.toString()}: ${e.toString()}`)
    }
  }

  return payload
}

/**
 * Handle prisma find values like `{where: {date: {gte: 'date1', lte: 'date2'}}}`
 */
export const fixDatesInFindArgs = <T extends object, K extends keyof T>(
  payload: { where?: T },
  fields: K[],
) => {
  if (!payload.where) return payload

  for (const field of fields) {
    try {
      const fieldValue = payload.where[field]
      if (!fieldValue) continue

      if (typeof fieldValue === 'object' && !((fieldValue as object) instanceof Date)) {
        // FIXME: typescript gods, need some help with `any` below
        const prismaFilter = fieldValue as any

        for (const childField of findArgsPlainFields) {
          if (!prismaFilter[childField]) continue
          prismaFilter[childField] = fixField(prismaFilter[childField], DATABASE_TIME_OFFSET)
        }

        for (const childField of findArgsArrayFields) {
          if (!prismaFilter[childField]) continue
          for (let i = 0; i < prismaFilter[childField].length; i++) {
            prismaFilter[childField][i] = fixField(
              prismaFilter[childField][i],
              DATABASE_TIME_OFFSET,
            )
          }
        }
      } else {
        fixDatesInWrite(payload.where, fields)
      }
    } catch (e: any) {
      throw new Error(`Could not fix date in field ${field.toString()}: ${e.toString()}`)
    }
  }

  return payload
}
