import {
  fixDatesInRead,
  fixDatesInWrite,
  fixDatesInReadMany,
  fixDatesInReadNullable,
  fixDatesInFindArgs,
} from './date'

jest.mock('@majestic-backend/config', () => ({
  default: () => ({
    db: {
      timeOffset: 3 * 60 * 60 * 1000,
    },
  }),
}))

const getPayloads = () => [
  {
    date: new Date('2022-01-01T00:00:00Z'),
    otherProp: 'someValue',
  },
  {
    date: new Date('2022-01-01T00:00:00Z'),
    otherProp: 'otherValue',
  },
]

describe('fixDatesInRead', () => {
  it('should fix dates in read payload', () => {
    const result = fixDatesInRead(getPayloads()[0], ['date'])
    expect(result).toMatchSnapshot()
  })
})

describe('fixDatesInWrite', () => {
  it('should fix dates in write payload', () => {
    const result = fixDatesInWrite(getPayloads()[0], ['date'])
    expect(result).toMatchSnapshot()
  })
})

describe('fixDatesInReadMany', () => {
  it('should fix dates in read payload array', () => {
    const result = fixDatesInReadMany(getPayloads(), ['date'])
    expect(result).toMatchSnapshot()
  })
})

describe('fixDatesInReadNullable', () => {
  it('should fix dates in read nullable payload', () => {
    const result = fixDatesInReadNullable(getPayloads()[0], ['date'])
    expect(result).toMatchSnapshot()
  })

  it('should return null if payload is null', () => {
    const result = fixDatesInReadNullable(null, ['date'])
    expect(result).toBeNull()
  })
})

describe('fixDatesInFindArgs', () => {
  it('should fix dates in find args with plain fields', () => {
    const payload = {
      where: {
        date: {
          gte: '2022-01-01T00:00:00Z',
          lte: '2022-01-02T00:00:00Z',
        },
      },
    }

    const result = fixDatesInFindArgs(payload, ['date'])
    expect(result).toMatchSnapshot()
  })

  it('should fix dates in find args with array fields', () => {
    const payload = {
      where: {
        date: {
          in: ['2022-01-01T00:00:00Z', '2022-01-02T00:00:00Z'],
        },
      },
    }

    const result = fixDatesInFindArgs(payload, ['date'])
    expect(result).toMatchSnapshot()
  })

  it('should not throw if where is not provided', () => {
    const payload = {}

    const result = fixDatesInFindArgs(payload, ['date'] as any)
    expect(result).toMatchSnapshot()
  })

  it('should not throw if field value is not provided', () => {
    const payload = {
      where: {},
    }

    const result = fixDatesInFindArgs(payload, ['date'] as any)
    expect(result).toMatchSnapshot()
  })
})
