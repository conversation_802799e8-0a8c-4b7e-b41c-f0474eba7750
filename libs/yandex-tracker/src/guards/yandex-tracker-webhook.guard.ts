import { CanActivate, ExecutionContext, Inject, Injectable } from '@nestjs/common'
import { Request } from 'express'
import { YandexTrackerConfig } from '@majestic-backend/config'
import { YANDEX_TRACKER_CONFIG_TOKEN } from '../lib/yandex-tracker-feature.constants'

@Injectable()
export class YandexTrackerWebhookGuard implements CanActivate {
  constructor(@Inject(YANDEX_TRACKER_CONFIG_TOKEN) private readonly config: YandexTrackerConfig) {}

  public async canActivate(ctx: ExecutionContext): Promise<boolean> {
    const req = ctx.switchToHttp().getRequest<Request>()

    const webhookSecret = this.config.O_AUTH
    if (webhookSecret && req.header('Authorization') !== `Bearer ${webhookSecret}`) {
      return false
    }

    return true
  }
}
