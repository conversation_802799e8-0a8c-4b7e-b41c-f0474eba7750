export enum IssueStatus {
  READY_FOR_TEST = 'Можно тестировать',
  IN_REVIEW = 'Ревью',
  CLOSED = 'Закрыт',
  IN_PROGRESS = 'В работе',
  OPEN = 'Открыт',
  NEED_INFO = 'Требуется информация',
  TESTING = 'Тестируется',
  TESTED = 'Протестировано',
  RESOLVED = 'Решен',
  RC = 'Готово к релизу',
  NEED_ACCEPTANCE = 'Ждем подтверждения',
  CANCELLED = 'Отменено',
  CONFIRMED = 'Подтверждён',
  READY_TO_GO = 'Готово к работе',
  GD_REVIEW = 'ГД-ревью',
}

export enum IssueFields {
  FIXED_IN_VERSIONS = 'Исправлено в версиях',
}
