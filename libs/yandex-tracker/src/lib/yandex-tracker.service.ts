import { Injectable, Logger } from '@nestjs/common'
import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { YandexTrackerConfig } from '@majestic-backend/config'
import {
  GetUserIssues,
  GetWorklogArgs,
  IQueueVersion,
  IIssueLink,
  Issues,
  IUpdateFixedInVersions,
  RemoteLink,
  Transition,
  Worklog,
  YandexTrackerIssue,
} from '../interfaces'

@Injectable()
export class YandexTrackerService {
  public axios: AxiosInstance
  private logger = new Logger(YandexTrackerService.name)

  constructor(private readonly config: YandexTrackerConfig) {
    this.axios = axios.create({
      headers: {
        Authorization: `OAuth ${this.config.O_AUTH}`,
        'X-Org-ID': this.config.X_CLOUD_ORG_ID,
      },
      baseURL: 'https://api.tracker.yandex.net/v2/',
    })
  }

  async getIssueByKey(key: string): Promise<YandexTrackerIssue> {
    this.logger.verbose(`Got into getIssuesByKey. Key: ${key}`)
    try {
      const response = await this.axios.get(`issues/${key}`)

      return response.data
    } catch (err) {
      this.logger.error(`Get issue by key error`, err)
      throw err
    }
  }

  async getIssueRemoteLinksByKey(key: string): Promise<RemoteLink[]> {
    this.logger.verbose(`Got into getIssueRemoteLinksByKey. Key: ${key}`)
    try {
      const response = await this.axios.get(`issues/${key}/remotelinks`)

      return response.data
    } catch (err) {
      this.logger.error(`Get issue by key error`, err)
      throw err
    }
  }

  async getIssueTransitionsByKey(key: string): Promise<Transition[]> {
    this.logger.verbose(`Got into getIssueTransitionsByKey. Key: ${key}`)
    try {
      const response = await this.axios.get(`issues/${key}/transitions`)

      return response.data
    } catch (err) {
      this.logger.error(`Get issue by key error`, err)
      throw err
    }
  }

  async issueUpdateStatus(key: string, transitionId: string, comment: string) {
    this.logger.verbose(`Got into issueUpdateStatus. Key: ${key}`)
    try {
      const response = await this.axios.post(`issues/${key}/transitions/${transitionId}/_execute`, {
        comment,
      })

      return response.data
    } catch (err) {
      this.logger.error(`Get issue by key error`, err)
      throw err
    }
  }

  async getWorklogs(args: GetWorklogArgs) {
    this.logger.verbose('Got into getWorklogs')
    try {
      const data: Worklog[] = []
      let response: AxiosResponse<Worklog[], any>
      let page = 0

      do {
        page++
        response = await this.axios.post<Worklog[]>(
          `worklog/_search?perPage=100&page=${page}`,
          args,
        )
        data.push(...response.data)
      } while (Number(response.headers['x-total-pages']) > page)

      return data
    } catch (err) {
      this.logger.error(`Get worklogs error`, err)
      throw err
    }
  }

  async getUserIssues(args: GetUserIssues) {
    this.logger.verbose('Got into getUserIssues')
    try {
      const data: Issues[] = []
      let response: AxiosResponse<Issues[], any>
      let page = 0

      do {
        page++
        response = await this.axios.post(`issues/_search?perPage=100&page=${page}`, args)
        data.push(...response.data)
      } while (Number(response.headers['x-total-pages']) > page)

      return data
    } catch (err) {
      this.logger.error(`Get issues error`, err)
      throw err
    }
  }

  async getUserByLogin(login: string) {
    this.logger.verbose('Got into getUserByLogin')
    try {
      const response = await this.axios.get(`users/${login}`)

      return response.data
    } catch (err) {
      this.logger.error(`Get user by login error`, err)
      throw err
    }
  }

  async getUsers() {
    this.logger.verbose('Got into getUsers')
    try {
      const response = await this.axios.get(`users?perPage=200`)

      return response.data
    } catch (err) {
      this.logger.error(`Get users error`, err)
      throw err
    }
  }

  async getIssueChangelog(key: string) {
    this.logger.verbose(`Got into getIssueChangelog. Key: ${key}`)
    try {
      const data = []
      let response: any
      let page = 0

      do {
        page++
        response = await this.axios.get(`issues/${key}/changelog?perPage=200`)
        data.push(...response.data)
      } while (Number(response.headers['x-total-pages']) > page)

      return data
    } catch (err) {
      this.logger.error(`Get issue changelog error`, err)
      throw err
    }
  }

  async getIssueLinksByKey(key: string): Promise<IIssueLink[]> {
    this.logger.verbose(`Got into getIssueLinksByKey. Key: ${key}`)
    try {
      const response = await this.axios.get(`issues/${key}/links`)

      return response.data
    } catch (err) {
      this.logger.error(`Get issue links by key error`, err)
      throw err
    }
  }

  async updateIssueFieldsByKey(key: string, payload: IUpdateFixedInVersions) {
    this.logger.verbose(`Got into updateIssueFieldsByKey. Key: ${key}`)
    try {
      const response = await this.axios.patch(`issues/${key}`, payload)

      return response.data
    } catch (err) {
      this.logger.error(`Update issue fields by key error`, err)
      throw err
    }
  }

  async getQueueVersions(queue: string): Promise<IQueueVersion[]> {
    this.logger.verbose(`Got into getQueueVersions. Queue: ${queue}`)
    try {
      const response = await this.axios.get(`queues/${queue}/versions`)

      return response.data
    } catch (err) {
      this.logger.error(`Get queue version error`, err)
      throw err
    }
  }

  async createQueueVersion(queue: string, name: string): Promise<IQueueVersion> {
    this.logger.verbose(`Got into createQueueVersion. Queue: ${queue}. Name: ${name}`)
    try {
      const response = await this.axios.post(`versions`, {
        queue,
        name,
      })

      return response.data
    } catch (err) {
      this.logger.error(`Create queue version error`, err)
      throw err
    }
  }
}
