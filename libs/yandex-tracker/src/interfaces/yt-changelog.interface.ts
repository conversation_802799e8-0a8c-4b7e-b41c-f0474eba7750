export interface IChangelog {
  id: string
  self: string
  issue: Issue
  updatedAt: string
  updatedBy: UpdatedBy
  type: string
  transport: string
  fields: RootField[]
  executedTriggers: ExecutedTrigger[]
}

export interface ExecutedTrigger {
  trigger: Field
  success: boolean
  message: string
}

export interface RootField {
  field: Field
  from?: UpdatedBy
  to?: UpdatedBy
}

export interface Field {
  self: string
  id: string
  display: string
}

export interface UpdatedBy {
  self: string
  id: string
  display: string
  cloudUid: string
  passportUid: number
}

export interface Issue {
  self: string
  id: string
  key: string
  display: string
}
