export interface IIssueLink {
  self: string
  id: number
  type: IType
  direction: string
  object: IObject
  createdBy: IDate
  updatedBy: IDate
  createdAt: string
  updatedAt: string
  assignee: IDate
  status: IObject
}

export interface IDate {
  self: string
  id: string
  display: string
}

export interface IObject {
  self: string
  id: string
  key: string
  display: string
}

export interface IType {
  self: string
  id: string
  inward: string
  outward: string
}

export interface IUpdateFixedInVersions {
  fixedInVersions?: string
  fixVersions?: any
}
