interface User {
  self: string
  id: string
  display: string
  cloudUid?: string
  passportUid?: number
}

interface Application {
  self: string
  id: string
  type: string
  name: string
}

interface Status {
  self: string
  id: string
  key: string
  display: string
}

interface IssueType {
  self: string
  id: string
  key: string
  display: string
}

interface Priority {
  self: string
  id: string
  key: string
  display: string
}

interface Queue {
  self: string
  id: string
  key: string
  display: string
}

interface Board {
  id: number
}

export interface YandexTrackerIssue {
  self: string
  id: string
  key: string
  version: number
  lastCommentUpdatedAt: string
  summary: string
  statusStartTime: string
  updatedBy: User
  statusType: Status
  boards: Board[]
  type: IssueType
  priority: Priority
  previousStatusLastAssignee: User
  createdAt: string
  followers: User[]
  createdBy: User
  commentWithoutExternalMessageCount: number
  votes: number
  commentWithExternalMessageCount: number
  assignee: User
  queue: Queue
  updatedAt: string
  status: Status
  previousStatus: Status
  favorite: boolean
}

interface LinkType {
  self: string
  id: string
  inward: string
  outward: string
}

interface RemoteObject {
  self: string
  id: string
  key: string
  application: Application
}

export interface RemoteLink {
  self: string
  id: number
  type: LinkType
  direction: string
  object: RemoteObject
  createdBy: User
  updatedBy: User
  createdAt: string
  updatedAt: string
}

export interface Transition {
  id: string
  self: string
  display: string
  to: {
    self: string
    id: string
    key: string
    display: string
  }
}

export interface GetWorklogArgs {
  createdBy?: string
  createdAt?: {
    from: Date
    to: Date
  }
  start?: {
    from: Date
    to: Date
  }
}

export interface GetUserIssues {
  filter: {
    assignee: string
  }
}

export interface WorklogUser {
  // Адрес ресурса API, который содержит информацию о пользователе
  self: string
  // Идентификатор пользователя.
  id: string
  // Отображаемое имя пользователя.
  display: string
  // Идентификатор пользователя в Yandex Cloud
  cloudUid: string
  // Идентификатор Yandex Passport
  passportUid: number
}

export interface WorklogIssue {
  // Адрес ресурса API, который содержит информацию о задаче.
  self: string
  // Идентификатор задачи.
  id: string
  // Ключ задачи.
  key: string
  // 	Отображаемое название задачи.
  display: string
}

export interface Worklog {
  // 	Адрес ресурса API, который содержит запись о затраченном времени.
  self: string
  // Идентификатор записи о затраченном времени.
  id: number
  // Версия записи. Каждое изменение записи увеличивает номер версии.
  version: number
  issue: WorklogIssue
  // Текст комментария к записи. Комментарий сохранится в Отчёте по затратам времени.
  comment?: string
  // Объект с информацией об авторе записи.
  createdBy: WorklogUser
  // Объект с информацией об авторе последнего изменения записи.
  updatedBy: WorklogUser
  // Дата и время создания записи в формате: YYYY-MM-DDThh:mm:ss.sss±hhmm
  createdAt: string
  // Дата и время обновления записи в формате: YYYY-MM-DDThh:mm:ss.sss±hhmm
  updatedAt: string
  // Дата и время начала работы над задачей в формате: YYYY-MM-DDThh:mm:ss.sss±hhmm
  start: string
  // Затраченное время в формате PnYnMnDTnHnMnS, PnW в соответствии с ISO 8601.
  duration: string
}

export interface Issues {
  self: string
  id: number
  key: string
  summary: string
  deadline: string
  start: string
  statusType: {
    id: string
    display: string
    key: string
  }
}

export interface IWebhookDateUpdate {
  key: string
  name: string
  startDate: string
  deadline: string
  assignee: string
}

export interface IYandexTrackerUser {
  self: string
  uid: number
  login: string
  trackerUid: number
  passportUid: number
  cloudUid: string
  firstName: string
  lastName: string
  display: string
  email: string
  external: boolean
  hasLicense: boolean
  dismissed: boolean
  useNewFilters: boolean
  disableNotifications: boolean
  firstLoginDate: string
  lastLoginDate: string
  welcomeMailSent: boolean
  sources: string[]
}

export interface IWebhookUpdateFixedInVersion {
  key: string
  name: string
}

export interface IQueueVersion {
  self: string
  id: number
  version: number
  queue: {
    self: string
    id: string
    key: string
    display: string
  }
  name: string
  released: boolean
  archived: boolean
}
