/*
 * flag: 0 - Анимация закончится самостоятельно
 * flag: 1 - Анимация будет повторяться
 * flag: 48 - Выше пояса, анимация закончится самостоятельно
 * flag: 49 - Выше пояса, анимация будет повторяться
 *
 * looped: true - Зациклить анимацию
 * looped: false - Анимация проиграется только 1 раз
 */

if (!globalThis.mp) {
	globalThis.mp = {
		Vector3: class {
			constructor() {}
		},
		game: {
			joaat: (key) => {
				const keyLowered = key.toLowerCase();
				let hash = 0;

				for (let i = 0; i < keyLowered.length; i++) {
					hash += keyLowered.charCodeAt(i);
					hash += hash << 10;
					hash ^= hash >>> 6;
				}

				hash += hash << 3;
				hash ^= hash >>> 11;
				hash += hash << 15;

				return hash >>> 0;
			},
		},
	};
}

module.exports = [
	{
		title: 'Действия',
		path: 'action',
		items: [
			{ title: 'animations.list.handsUp', animData: ['random@mugging3', 'handsup_standing_base'], flag: 49, looped: true },
			{ title: 'animations.list.handsUp1', animData: ['anim@move_hostages@male', 'male_idle'], flag: 1, looped: true },
			{ title: 'animations.list.salute', animData: ['anim@mp_player_intincarsalutestd@ds@', 'idle_a'], flag: 49, looped: true },
			{ title: 'animations.list.salute1', animData: ['mp_player_int_uppersalute', 'mp_player_int_salute'], flag: 49, looped: true },
			{ title: 'animations.list.natMorPeh', animData: ['mp_player_intsalute', 'mp_player_int_salute'], flag: 1, looped: false },
			{ title: 'animations.list.search', animData: ['anim@gangops@morgue@table@', 'player_search'], flag: 49, looped: true },
			{ title: 'animations.list.whistle', animData: ['taxi_hail', 'fp_hail_taxi'], flag: 48, looped: false },
			{ title: 'animations.list.warmHands', animData: ['amb@world_human_stand_fire@male@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.waiting', animData: ['move_m@drunk@transitions', 'slightly_to_idle'], flag: 1, looped: true },
			{ title: 'animations.list.press', animData: ['amb@world_human_sit_ups@male@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.searchOnTheGround', animData: ['amb@world_human_bum_wash@male@high@idle_a', 'idle_a'], flag: 1, looped: true },
			{ title: 'animations.list.flapHends', animData: ['random@car_thief@victimpoints_ig_3', 'arms_waving'], flag: 1, looped: true },
			{ title: 'animations.list.inspect', animData: ['amb@medic@standing@kneel@enter', 'enter'], flag: 1, looped: true },
			{ title: 'animations.list.inspGround', animData: ['amb@code_human_police_investigate@idle_b', 'idle_f'], flag: 1, looped: true },
			{ title: 'animations.list.pant', animData: ['timetable@reunited@ig_2', 'jimmy_base'], flag: 1, looped: true },
			{ title: 'animations.list.pushUp', animData: ['amb@world_human_push_ups@male@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.kick', animData: ['anim@mp_freemode_return@f@fail', 'fail_a'], flag: 1, looped: true },
			{ title: 'animations.list.expect', animData: ['amb@code_human_police_investigate@idle_a', 'idle_a'], flag: 1, looped: true },
			{ title: 'animations.list.shrug', animData: ['gestures@m@standing@casual', 'gesture_what_soft'], flag: 0, looped: false },
			{
				title: 'animations.list.showTwoFingers',
				animData: ['amb@code_human_in_car_mp_actions@v_sign@bodhi@rps@base', 'idle_a'],
				flag: 49,
				looped: true,
			},
			{ title: 'animations.list.pointFinger', animData: ['gestures@m@standing@casual', 'gesture_point'], flag: 48, looped: true },
			{ title: 'animations.list.putInMouth', animData: ['mp_player_int_uppersmoke', 'mp_player_int_smoke_enter'], flag: 1, looped: true },
			{ title: 'animations.list.prayer', animData: ['pro_mcs_7_concat-0', 'cs_priest_dual-0'], flag: 48, looped: true },
			{ title: 'animations.list.drum', animData: ['amb@code_human_in_car_mp_actions@dance@bodhi@ps@base', 'idle_a_fp'], flag: 1, looped: true },
			{ title: 'animations.list.rubPalms', animData: ['amb@world_human_cop_idles@female@idle_b', 'idle_d'], flag: 1, looped: true },
			{ title: 'animations.list.rubPalms1', animData: ['move_action@p_m_one@unarmed@idle@variations', 'idle_a'], flag: 1, looped: true },
			{ title: 'animations.list.rubNeck', animData: ['amb@world_human_cop_idles@female@idle_a', 'idle_c'], flag: 1, looped: true },
			{ title: 'animations.list.rubAss', animData: ['anim@heists@team_respawn@respawn_02', 'heist_spawn_02_ped_d'], flag: 0, looped: false },
			{ title: 'animations.list.wash', animData: ['mp_safehouseshower@male@', 'male_shower_idle_d'], flag: 1, looped: true },
			{ title: 'animations.list.nervLookAround', animData: ['amb@world_human_guard_patrol@male@idle_a', 'idle_b'], flag: 1, looped: true },
			{ title: 'animations.list.pickInHands', animData: ['amb@prop_human_movie_studio_light@idle_a', 'idle_a'], flag: 1, looped: true },
			{ title: 'animations.list.waveFinger', animData: ['gestures@m@standing@casual', 'gesture_i_will'], flag: 48, looped: false },
			{ title: 'animations.list.tackleBelly', animData: ['rcmpaparazzo1', 'idle'], flag: 1, looped: true },
			{ title: 'animations.list.tackleHeart', animData: ['rcmfanatic1out_of_breath', 'p_zero_tired_01'], flag: 49, looped: true },
			{ title: 'animations.list.tss', animData: ['anim@mp_player_intcelebrationfemale@shush', 'shush'], flag: 0, looped: false },
			{
				title: 'animations.list.lyingBlow',
				animData: ['anim@heists@ornate_bank@hostages@hit', 'player_melee_long_pistol_a'],
				flag: 0,
				looped: false,
			},
			{ title: 'animations.list.washFace', animData: ['missfam2_washing_face', 'michael_washing_face'], flag: 1, looped: true },
			{ title: 'animations.list.transmitOnRadio', animData: ['random@arrests', 'generic_radio_chatter'], flag: 49, looped: true },
			{ title: 'animations.list.wrtPaper', animData: ['amb@world_human_clipboard@male@idle_b', 'idle_d'], flag: 49, looped: true },
			{ title: 'animations.list.easyDance1', animData: ['amb@world_human_strip_watch_stand@male_a@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.easyDance2', animData: ['amb@world_human_strip_watch_stand@male_a@idle_a', 'idle_c'], flag: 1, looped: true },
			{ title: 'animations.list.easyDance3', animData: ['amb@world_human_strip_watch_stand@male_b@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.easyDance4', animData: ['amb@world_human_strip_watch_stand@male_c@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.wrtNotebook', animData: ['amb@medic@standing@timeofdeath@base', 'base'], flag: 1, looped: false },
			{ title: 'animations.list.washFace1', animData: ['amb@world_human_bum_wash@male@high@idle_a', 'idle_a'], flag: 0, looped: false },
			{ title: 'animations.list.drinkCoffee', animData: ['amb@world_human_leaning@female@coffee@base', 'base'], flag: 1, looped: false },
			{ title: 'animations.list.copInThrong', animData: ['amb@code_human_police_crowd_control@idle_a', 'idle_a'], flag: 1, looped: true },
			{ title: 'animations.list.layBum', animData: ['amb@lo_res_idles@', 'world_human_bum_slumped_left_lo_res_base'], flag: 1, looped: true },
			{ title: 'animations.list.lean', animData: ['amb@prop_human_bum_shopping_cart@male@idle_a', 'idle_a'], flag: 1, looped: true },
		],
	},

	{
		title: 'Позы',
		path: 'pose',
		items: [
			{
				title: 'animations.list.sitOnChair1',
				animData: ['anim@amb@business@cfm@cfm_machine_no_work@', 'transition_sleep_operator'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.sitOnChair2',
				animData: ['switch@michael@tv_w_kids', '001520_02_mics3_14_tv_w_kids_idle_mic'],
				flag: 1,
				looped: true,
			},
			{ title: 'animations.list.sitOnChair3', animData: ['mp_army_contact', 'positive_a'], flag: 1, looped: true },
			{ title: 'animations.list.sitOnChair4', animData: ['random@robbery', 'sit_down_idle_01'], flag: 1, looped: true },
			{
				title: 'animations.list.sitOnChair5',
				animData: ['anim@amb@nightclub@peds@', 'anim_heists_heist_safehouse_intro_phone_couch_female'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.sitOnChair6',
				animData: ['anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@', 'transition_sleep_lazyworker'],
				flag: 1,
				looped: true,
			},
			{ title: 'animations.list.sitOnSofa', animData: ['timetable@maid@couch@', 'base'], flag: 1, looped: true },
			{
				title: 'animations.list.expanding',
				animData: ['amb@code_human_in_car_mp_actions@v_sign@std@rds@base', 'enter'],
				flag: 0,
				looped: false,
			},
			{ title: 'animations.list.handsToSide', animData: ['missfam5_yoga', 'c1_pose'], flag: 1, looped: true },
			{ title: 'animations.list.handsFeetToSide', animData: ['missfam5_yoga', 'a2_pose'], flag: 1, looped: true },
			{ title: 'animations.list.handsInSides', animData: ['amb@code_human_police_investigate@base', 'base'], flag: 49, looped: true },
			{ title: 'animations.list.handsInSides1', animData: ['amb@world_human_cop_idles@female@base', 'base'], flag: 49, looped: true },
			{ title: 'animations.list.foldHandsLookAround', animData: ['mp_corona@single_team', 'single_team_intro_boss'], flag: 1, looped: true },
			{ title: 'animations.list.foldHands', animData: ['mp_corona@single_team', 'single_team_loop_boss'], flag: 1, looped: true },
			{
				title: 'animations.list.handsOverHead',
				animData: ['anim@heists@ornate_bank@hostages@cashier_b@', 'flinch_loop_underfire'],
				flag: 1,
				looped: true,
			},
			{ title: 'animations.list.handsOverBack', animData: ['anim@miss@low@fin@vagos@', 'idle_ped06'], flag: 49, looped: true },
			{ title: 'animations.list.volchok', animData: ['random@peyote@fish', 'wakeup_loop'], flag: 1, looped: true },
			{ title: 'animations.list.runInPlace', animData: ['amb@world_human_jog_standing@male@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.runInPlaceWaggle', animData: ['amb@world_human_jog_standing@female@idle_a', 'idle_a'], flag: 1, looped: true },
			{
				title: 'animations.list.lieOnSide',
				animData: ['amb@world_human_bum_slumped@male@laying_on_right_side@base', 'base'],
				flag: 1,
				looped: true,
			},
			{ title: 'animations.list.lieOnBelly', animData: ['amb@world_human_sunbathe@male@front@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.lieOnBack1', animData: ['missfbi1', 'cpr_pumpchest_idle'], flag: 1, looped: true },
			{ title: 'animations.list.lieOnBack2', animData: ['amb@world_human_sunbathe@male@back@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.lieOnBack3', animData: ['amb@world_human_sunbathe@male@back@idle_a', 'idle_a'], flag: 1, looped: true },
			{ title: 'animations.list.yoga', animData: ['missfam5_yoga', 'a2_to_a3'], flag: 1, looped: true },
			{ title: 'animations.list.posSecurity', animData: ['missfbi4mcs_2', 'loop_sec_b'], flag: 1, looped: true },
			{ title: 'animations.list.showBiceps', animData: ['amb@world_human_muscle_flex@arms_in_front@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.sit1', animData: ['anim@miss@low@fin@lamar@', 'idle'], flag: 2, looped: true },
			{ title: 'animations.list.sit3', animData: ['amb@medic@standing@tendtodead@enter', 'enter'], flag: 2, looped: true },
			{ title: 'animations.list.sit2', animData: ['amb@medic@standing@kneel@base', 'base'], flag: 2, looped: true },
			{ title: 'animations.list.cryMercy', animData: ['amb@code_human_cower@male@react_cowering', 'base_front'], flag: 1, looped: true },
			{ title: 'animations.list.warmUp', animData: ['timetable@tracy@ig_5@idle_b', 'idle_d'], flag: 1, looped: true },
			{ title: 'animations.list.foldHands2', animData: ['rcmme_amanda1', 'stand_loop_cop'], flag: 49, looped: true },
			{ title: 'animations.list.arnold', animData: ['amb@world_human_muscle_flex@arms_in_front@idle_a', 'idle_b'], flag: 0, looped: false },
			{
				title: 'animations.list.standWall1',
				animData: ['amb@world_human_leaning@female@wall@back@holding_elbow@base', 'base'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.standWall2',
				animData: ['amb@world_human_leaning@female@wall@back@holding_elbow@idle_a', 'idle_a'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.standWall3',
				animData: ['amb@world_human_leaning@male@wall@back@hands_together@base', 'base'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.standWall4',
				animData: ['amb@world_human_leaning@male@wall@back@legs_crossed@base', 'base'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.standWall5',
				animData: ['amb@world_human_leaning@male@wall@back@foot_up@react_shock', 'front'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.standWall6',
				animData: ['amb@world_human_leaning@male@wall@back@hands_together@idle_b', 'idle_e'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.standWall7',
				animData: ['amb@world_human_leaning@male@wall@back@legs_crossed@idle_a', 'idle_c'],
				flag: 1,
				looped: true,
			},
			{ title: 'animations.list.standWall8', animData: ['amb@world_human_leaning@male@wall@back@smoking@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.chillGaz1', animData: ['amb@world_human_picnic@male@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.chillGaz2', animData: ['amb@world_human_picnic@male@idle_a', 'idle_a'], flag: 1, looped: true },
			{ title: 'animations.list.chillGaz3', animData: ['rcm_barry3', 'barry_3_sit_loop'], flag: 1, looped: true },
			{ title: 'animations.list.sunbathe', animData: ['amb@world_human_sunbathe@female@back@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.betterHand', animData: ['amb@world_human_muscle_flex@arms_in_front@idle_a', 'idle_c'], flag: 49, looped: true },
			{ title: 'animations.list.waveHand', animData: ['amb@world_human_car_park_attendant@male@base', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.meditation', animData: ['rcmcollect_paperleadinout@', 'meditiate_idle'], flag: 1, looped: true },
			{ title: 'animations.list.chesatAss', animData: ['mp_player_int_upperarse_pick', 'mp_player_int_arse_pick'], flag: 49, looped: true },
			{ title: 'animations.list.posSecurity2', animData: ['rcmepsilonism8', 'base_carrier'], flag: 1, looped: true },
		],
	},

	{
		title: 'Позитивные',
		path: 'positive',
		items: [
			{ title: 'animations.list.fingUp', animData: ['anim@mp_player_intincarthumbs_upbodhi@ds@', 'enter_fp'], flag: 0, looped: false },
			{ title: 'animations.list.jumpHappy1', animData: ['amb@world_human_cheering@female_a', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.jumpHappy2', animData: ['missmic_4premiere', 'movie_prem_01_f_a'], flag: 1, looped: true },
			{ title: 'animations.list.jumpHappy3', animData: ['anim@mp_player_intcelebrationfemale@freakout', 'freakout'], flag: 0, looped: false },
			{ title: 'animations.list.gordSelf1', animData: ['mini@dartsoutro', 'darts_outro_03_guy2'], flag: 0, looped: false },
			{ title: 'animations.list.gordSelf2', animData: ['mini@dartsoutro', 'darts_outro_01_guy1'], flag: 0, looped: false },
			{ title: 'animations.list.uspokoit', animData: ['amb@code_human_police_crowd_control@idle_a', 'idle_c'], flag: 0, looped: false },
			{ title: 'animations.list.applause1', animData: ['missmic_4premiere', 'movie_prem_02_f_a'], flag: 49, looped: true },
			{ title: 'animations.list.applause2', animData: ['amb@world_human_cheering@male_e', 'base'], flag: 49, looped: true },
			{ title: 'animations.list.applause3', animData: ['amb@world_human_cheering@male_a', 'base'], flag: 1, looped: true },
			{ title: 'animations.list.airkiss', animData: ['anim@mp_player_intcelebrationfemale@blow_kiss', 'blow_kiss'], flag: 49, looped: true },
			{ title: 'animations.list.kissPlayer', animData: ['mp_ped_interaction', 'kisses_guy_b'], flag: 0, looped: false },
			{ title: 'animations.list.bro', animData: ['anim@mp_player_intcelebrationmale@bro_love', 'bro_love'], flag: 49, looped: true },
			{ title: 'animations.list.fingersUp1', animData: ['anim@mp_player_intcelebrationfemale@thumbs_up', 'thumbs_up'], flag: 0, looped: false },
			{ title: 'animations.list.fingersUp2', animData: ['anim@mp_player_intupperthumbs_up', 'idle_a_fp'], flag: 1, looped: true },
			{ title: 'animations.list.jeeEst', animData: ['anim@mp_point', '1st_person_high_blocked'], flag: 1, looped: true },
		],
	},

	{
		title: 'Негативные',
		path: 'negative',
		items: [
			{ title: 'animations.list.pickInNose', animData: ['anim@mp_player_intcelebrationfemale@nose_pick', 'nose_pick'], flag: 1, looped: true },
			{ title: 'animations.list.refusal', animData: ['gestures@m@standing@casual', 'gesture_nod_no_hard'], flag: 48, looped: false },
			{ title: 'animations.list.skipHand', animData: ['taxi_hail', 'forget_it'], flag: 0, looped: false },
			{
				title: 'animations.list.myatKulaki',
				animData: ['anim@mp_player_intcelebrationfemale@knuckle_crunch', 'knuckle_crunch'],
				flag: 49,
				looped: false,
			},
			{ title: 'animations.list.upset', animData: ['mini@dartsoutro', 'darts_outro_03_guy1'], flag: 0, looped: false },
			{ title: 'animations.list.krytitVisok', animData: ['anim@mp_parachute_outro@female@lose', 'lose_loop'], flag: 0, looped: false },
			{ title: 'animations.list.stupid', animData: ['anim@mp_player_intcelebrationfemale@you_loco', 'you_loco'], flag: 0, looped: false },
			{ title: 'animations.list.handFace', animData: ['anim@mp_player_intcelebrationfemale@face_palm', 'face_palm'], flag: 49, looped: false },
			{
				title: 'animations.list.shootingGun1',
				animData: ['amb@world_human_superhero@male@space_pistol@idle_a', 'idle_b'],
				flag: 0,
				looped: false,
			},
			{
				title: 'animations.list.shootingGun2',
				animData: ['anim@deathmatch_intros@2hcombat_mgmale', 'intro_male_mg_c'],
				flag: 0,
				looped: false,
			},
			{
				title: 'animations.list.baseball',
				animData: ['anim@deathmatch_intros@melee@2h', 'intro_male_melee_2h_b_gclub'],
				flag: 0,
				looped: false,
			},
			{ title: 'animations.list.stand', animData: ['anim@deathmatch_intros@unarmed', 'intro_male_unarmed_b'], flag: 0, looped: false },
			{ title: 'animations.list.rage', animData: ['oddjobs@towingangryidle_a', 'idle_c'], flag: 1, looped: true },
			{ title: 'animations.list.showFuck', animData: ['anim@mp_player_intupperfinger', 'idle_a_fp'], flag: 49, looped: true },
			{ title: 'animations.list.facepalm', animData: ['anim@mp_player_intcelebrationmale@face_palm', 'face_palm'], flag: 49, looped: true },
			{ title: 'animations.list.badger', animData: ['anim@mp_player_intcelebrationmale@cry_baby', 'cry_baby'], flag: 0, looped: false },
			{ title: 'animations.list.badger1', animData: ['anim@mp_player_intcelebrationfemale@jazz_hands', 'jazz_hands'], flag: 0, looped: false },
			{
				title: 'animations.list.badger2',
				animData: ['anim@mp_player_intcelebrationfemale@thumb_on_ears', 'thumb_on_ears'],
				flag: 0,
				looped: false,
			},
			{
				title: 'animations.list.badger3',
				animData: ['anim@mp_player_intcelebrationmale@thumb_on_ears', 'thumb_on_ears'],
				flag: 0,
				looped: false,
			},
			{ title: 'animations.list.ugrozhat', animData: ['anim@mp_player_intcelebrationmale@cut_throat', 'cut_throat'], flag: 1, looped: true },
			{ title: 'animations.list.krytitVisok2', animData: ['anim@mp_player_intcelebrationmale@you_loco', 'you_loco'], flag: 1, looped: true },
			{ title: 'animations.list.crazy', animData: ['anim@mp_player_intcelebrationmale@freakout', 'freakout'], flag: 1, looped: true },
			{ title: 'animations.list.stinks', animData: ['anim@mp_player_intcelebrationmale@stinker', 'stinker'], flag: 1, looped: true },
			{ title: 'animations.list.whatTheHeck', animData: ['amb@code_human_police_investigate@idle_a', 'idle_c'], flag: 0, looped: false },
		],
	},

	{
		title: 'Танцы',
		path: 'dances',
		items: [
			{ title: 'animations.list.dj', animData: ['anim@mp_player_intcelebrationfemale@dj', 'dj'], flag: 1, looped: true },
			{ title: 'animations.list.easyDance15', animData: ['misscarsteal4@toilet', 'desperate_toilet_idle_a'], flag: 1, looped: true },
			{
				title: 'animations.list.lesgin',
				animData: ['special_ped@mountain_dancer@monologue_2@monologue_2a', 'mnt_dnc_angel'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.hardBass',
				animData: ['anim@amb@casino@mini@dance@dance_solo@female@var_b@', 'high_center'],
				flag: 1,
				looped: true,
			},
			{ title: 'animations.list.twerk', animData: ['switch@trevor@mocks_lapdance', '001443_01_trvs_28_idle_stripper'], flag: 1, looped: true },
			{ title: 'animations.list.clap', animData: ['anim@amb@nightclub@djs@black_madonna@', 'dance_a_loop_blamadon'], flag: 1, looped: true },
			{
				title: 'animations.list.dance1',
				animData: ['anim@amb@casino@mini@dance@dance_solo@female@var_a@', 'high_center'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance2',
				animData: ['anim@amb@casino@mini@dance@dance_solo@female@var_a@', 'high_center_up'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance3',
				animData: ['anim@amb@casino@mini@dance@dance_solo@female@var_a@', 'med_center_up'],
				flag: 1,
				looped: true,
			},
			{ title: 'animations.list.dance4', animData: ['anim@amb@casino@mini@dance@dance_solo@female@var_a@', 'med_left'], flag: 1, looped: true },
			{
				title: 'animations.list.dance5',
				animData: ['anim@amb@nightclub@dancers@crowddance_facedj@', 'hi_dance_facedj_09_v1_female^1'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance6',
				animData: ['anim@amb@nightclub@dancers@crowddance_facedj@', 'hi_dance_facedj_09_v2_female^1'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance7',
				animData: ['anim@amb@nightclub@dancers@crowddance_facedj@', 'hi_dance_facedj_09_v2_female^3'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance8',
				animData: ['anim@amb@nightclub@dancers@crowddance_facedj@', 'hi_dance_facedj_09_v2_male^2'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance9',
				animData: ['anim@amb@nightclub@dancers@crowddance_facedj@', 'hi_dance_facedj_09_v2_male^4'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance10',
				animData: ['anim@amb@nightclub@dancers@crowddance_facedj@', 'hi_dance_facedj_11_v1_female^3'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance11',
				animData: ['anim@amb@nightclub@dancers@crowddance_facedj@', 'hi_dance_facedj_11_v2_female^3'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance12',
				animData: ['anim@amb@nightclub@dancers@crowddance_facedj@', 'hi_dance_facedj_11_v2_male^5'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance13',
				animData: ['anim@amb@nightclub@dancers@crowddance_facedj@', 'hi_dance_facedj_15_v1_male^1'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance14',
				animData: ['anim@amb@nightclub@dancers@crowddance_facedj@', 'hi_dance_facedj_15_v2_female^3'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance15',
				animData: ['anim@amb@nightclub@dancers@crowddance_facedj@', 'hi_dance_facedj_17_v2_female^3'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance16',
				animData: ['anim@amb@nightclub@dancers@crowddance_groups@', 'hi_dance_crowd_09_v1_female^1'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance17',
				animData: ['anim@amb@nightclub@dancers@crowddance_groups@', 'hi_dance_crowd_09_v1_female^3'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance18',
				animData: ['anim@amb@nightclub@dancers@crowddance_groups@', 'hi_dance_crowd_09_v2_female^1'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance19',
				animData: ['anim@amb@nightclub@dancers@crowddance_groups@', 'hi_dance_crowd_09_v2_female^3'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance20',
				animData: ['anim@amb@nightclub@dancers@crowddance_groups@', 'hi_dance_crowd_11_v1_male^3'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance21',
				animData: ['anim@amb@nightclub@dancers@crowddance_groups@', 'hi_dance_crowd_15_v2_male^4'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance22',
				animData: ['anim@amb@nightclub@dancers@crowddance_groups@', 'hi_dance_crowd_17_v2_female^2'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance23',
				animData: ['anim@amb@nightclub@lazlow@hi_dancefloor@', 'crowddance_hi_11_handup_laz'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance24',
				animData: ['anim@amb@nightclub@lazlow@hi_podium@', 'danceidle_hi_15_crazyrobot_laz'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance25',
				animData: ['anim@amb@nightclub@lazlow@hi_podium@', 'danceidle_hi_17_smackthat_laz'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance26',
				animData: ['anim@amb@nightclub@lazlow@hi_podium@', 'danceidle_hi_17_spiderman_laz'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance27',
				animData: ['anim@amb@nightclub@lazlow@hi_podium@', 'danceidle_mi_17_crotchgrab_laz'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance28',
				animData: ['anim@amb@nightclub@lazlow@hi_railing@', 'ambclub_09_mi_hi_bellydancer_laz'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance29',
				animData: ['anim@amb@nightclub@mini@dance@dance_solo@female@var_a@', 'high_center'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance30',
				animData: ['anim@amb@nightclub@mini@dance@dance_solo@female@var_a@', 'high_center_down'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance31',
				animData: ['anim@amb@nightclub@mini@dance@dance_solo@female@var_a@', 'low_center_up'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance32',
				animData: ['anim@amb@nightclub@mini@dance@dance_solo@female@var_b@', 'high_center_up'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance33',
				animData: ['anim@amb@nightclub@mini@dance@dance_solo@male@var_b@', 'high_center_down'],
				flag: 1,
				looped: true,
			},
			{ title: 'animations.list.dance34', animData: ['misschinese2_crystalmazemcs1_cs', 'dance_loop_tao'], flag: 1, looped: true },
			{ title: 'animations.list.dance35', animData: ['misschinese2_crystalmazemcs1_ig', 'dance_loop_tao'], flag: 1, looped: true },
			{ title: 'animations.list.dance36', animData: ['move_clown@p_m_two_idles@', 'fidget_short_dance'], flag: 1, looped: true },
			{ title: 'animations.list.dance37', animData: ['rcmnigel1bnmt_1b', 'dance_loop_tyler'], flag: 1, looped: true },
			{ title: 'animations.list.dance38', animData: ['missfbi3_sniping', 'dance_m_default'], flag: 1, looped: true },
			{
				title: 'animations.list.dance39',
				animData: ['anim@amb@nightclub@mini@dance@dance_solo@female@var_b@', 'med_center'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.dance40',
				animData: ['anim@amb@casino@mini@dance@dance_solo@female@var_b@', 'med_center'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.stripDance1',
				animData: ['anim@amb@nightclub@peds@', 'mini_strip_club_lap_dance_ld_girl_a_song_a_p1'],
				flag: 1,
				looped: true,
			},
			{
				title: 'animations.list.stripDance2',
				animData: ['mini@strip_club@lap_dance@ld_girl_a_song_a_p2', 'ld_girl_a_song_a_p2_f'],
				flag: 1,
				looped: true,
			},
			{ title: 'animations.list.stripDance3', animData: ['mp_am_stripper', 'lap_dance_girl'], flag: 1, looped: true },
			{ title: 'animations.list.stripDance4', animData: ['mini@strip_club@private_dance@part2', 'priv_dance_p2'], flag: 1, looped: true },
			{ title: 'animations.list.stripDance5', animData: ['mini@strip_club@private_dance@part3', 'priv_dance_p3'], flag: 1, looped: true },
			{
				title: 'animations.list.stripDance6',
				animData: ['oddjobs@assassinate@multi@yachttarget@lapdance', 'yacht_ld_f'],
				flag: 1,
				looped: true,
			},
			{ title: 'animations.list.stripDance7', animData: ['mp_safehouse', 'lap_dance_girl'], flag: 1, looped: true },
			{ title: 'animations.list.stripDance8', animData: ['mini@strip_club@lap_dance_2g@ld_2g_p2', 'ld_2g_p2_s1'], flag: 1, looped: true },
			{ title: 'animations.list.stripDance9', animData: ['amb@world_human_prostitute@cokehead@idle_a', 'idle_a'], flag: 1, looped: true },
			{ title: 'animations.list.stripDance10', animData: ['amb@world_human_prostitute@cokehead@idle_a', 'idle_b'], flag: 1, looped: true },
			{ title: 'animations.list.stripDance11', animData: ['amb@world_human_prostitute@cokehead@idle_a', 'idle_c'], flag: 1, looped: true },
			{ title: 'animations.list.stripDance12', animData: ['mini@strip_club@lap_dance_2g@ld_2g_p1', 'ld_2g_p1_s1'], flag: 1, looped: true },
			{ title: 'animations.list.stripDance13', animData: ['mini@strip_club@lap_dance_2g@ld_2g_p1', 'ld_2g_p1_s2'], flag: 1, looped: true },
		],
	},

	{
		title: 'Другое',
		path: 'etc',
		items: [
			{
				title: 'animations.list.guitarist',
				animData: ['anim@mp_player_intcelebrationfemale@air_guitar', 'air_guitar'],
				flag: 0,
				looped: false,
			},
			{ title: 'animations.list.kachatMyshtsy', animData: ['anim@mp_player_intcelebrationmale@peace', 'peace'], flag: 0, looped: false },
			{
				title: 'animations.list.chicken',
				animData: ['anim@mp_player_intcelebrationfemale@chicken_taunt', 'chicken_taunt'],
				flag: 0,
				looped: false,
			},
			{ title: 'animations.list.lomka', animData: ['creatures@rottweiler@melee@', 'victim_takedown_from_front'], flag: 0, looped: false },
			{ title: 'animations.list.rock', animData: ['amb@code_human_in_car_mp_actions@rock@bodhi@rps@base', 'idle_a'], flag: 48, looped: true },
			{ title: 'animations.list.rock2', animData: ['amb@code_human_in_car_mp_actions@rock@bodhi@rps@base', 'idle_a'], flag: 48, looped: true },
			{
				title: 'animations.list.waveHands',
				animData: ['random@gang_intimidation@', '001445_01_gangintimidation_1_female_wave_loop'],
				flag: 49,
				looped: true,
			},
			{ title: 'animations.list.karate', animData: ['anim@mp_player_intcelebrationmale@karate_chops', 'karate_chops'], flag: 1, looped: true },
			// {title: 'animations.list.sexMale1',       		animData: ['misscarsteal2pimpsex', 'pimpsex_punter'],                                               				flag: 1,    looped: true},
			// {title: 'animations.list.sexMale2',       		animData: ['misscarsteal2pimpsex', 'shagloop_pimp'],                                                				flag: 1,    looped: true},
			// {title: 'animations.list.sexMale3',       		animData: ['rcmpaparazzo_2', 'shag_loop_a'],                                                        				flag: 1,    looped: true},
			// {title: 'animations.list.sexFemale1',       		animData: ['misscarsteal2pimpsex', 'pimpsex_hooker'],                                               				flag: 1,    looped: true},
			// {title: 'animations.list.sexFemale2',       		animData: ['misscarsteal2pimpsex', 'shagloop_hooker'],                                              				flag: 1,    looped: true},
			// {title: 'animations.list.sexFemale3',       		animData: ['rcmpaparazzo_2', 'shag_loop_poppy'],                                                    				flag: 1,    looped: true},
			// {title: 'animations.list.pullHands',       		animData: ['mp_player_int_upperwank', 'mp_player_int_wank_01'],                                     				flag: 49,   looped: true},
			{ title: 'animations.list.trup', animData: ['anim@melee@machete@streamed_core@', 'victim_front_takedown'], flag: 1, looped: false },
			{
				title: 'animations.list.photographer',
				animData: ['anim@mp_player_intcelebrationfemale@photography', 'photography'],
				flag: 0,
				looped: false,
			},
			// {title: "Sheeeeesh",            					animData: ["custom@sheeeeesh", "sheeeeesh"],                                                        				flag: 50,   looped: true},
			// {title: "Рисовать граффити 1", 					animData: ["amb@world_human_maid_clean@idle_a", "idle_a"], 															flag: 1, 	looped: true},
			// {title: "Рисовать граффити 2", 					animData: ["amb@world_human_picnic@female@enter", "enter"], 														flag: 1, 	looped: true},
		],
	},

	{
		title: 'Эксклюзивные',
		path: 'exclusive',
		items: [
			{
				id: 0,
				title: 'animations.list.BlindingLights',
				animData: ['majestic_animations', 'blinding_lights'],
				flag: 0,
				looped: false,
				music: 'blinding_lights',
			},
			{
				id: 1,
				title: 'animations.list.BoogieDown',
				animData: ['majestic_animations', 'boogie_down'],
				flag: 1,
				looped: true,
				music: 'boogie_down',
			},
			{
				id: 2,
				title: 'animations.list.KneeSlapper',
				animData: ['majestic_animations', 'cowboy_dance'],
				flag: 1,
				looped: true,
				music: 'knee_slapper',
			},
			{
				id: 3,
				title: 'animations.list.Crossbounce',
				animData: ['majestic_animations', 'crossbounce'],
				flag: 1,
				looped: true,
				music: 'crossbounce',
			},
			{
				id: 4,
				title: 'animations.list.DiscoFever',
				animData: ['majestic_animations', 'disco_dance'],
				flag: 1,
				looped: true,
				music: 'disco_fever',
			},
			{
				id: 5,
				title: 'animations.list.DontStartNow',
				animData: ['majestic_animations', 'dont_start_now'],
				flag: 1,
				looped: true,
				music: 'dont_start_now',
			},
			{ id: 6, title: 'animations.list.Floss', animData: ['majestic_animations', 'floss_dance'], flag: 1, looped: true, music: 'floss' },
			{ id: 7, title: 'animations.list.Fresh', animData: ['majestic_animations', 'fresh'], flag: 1, looped: true, music: 'fresh' },
			{
				id: 8,
				title: 'animations.list.GangnamStyle',
				animData: ['majestic_animations', 'gangnam_style'],
				flag: 33,
				looped: true,
				music: 'gangnam_style',
			},
			{ id: 9, title: 'animations.list.HeartRizon', animData: ['majestic_animations', 'i_heart_you'], flag: 0, looped: false },
			{
				id: 10,
				title: 'animations.list.JabbaSwitchway',
				animData: ['majestic_animations', 'jabba_switchway'],
				flag: 1,
				looped: true,
				music: 'jabba_switchway',
			},
			{ id: 11, title: 'animations.list.TheMacarena', animData: ['majestic_animations', 'macarena'], flag: 1, looped: true, music: 'macarena' },
			{
				id: 12,
				title: 'animations.list.LastForever',
				animData: ['majestic_animations', 'last_forever'],
				flag: 1,
				looped: true,
				music: 'last_forever',
				pairAnims: [{ animData: ['majestic_animations_7', 'last_forever_2'], flag: 1, looped: true }],
			},
			{
				id: 13,
				title: 'animations.list.RideThePony',
				animData: ['majestic_animations', 'ridethepony_v2'],
				flag: 1,
				looped: true,
				music: 'ride_the_pony',
			},
			{ id: 14, title: 'animations.list.Rollie', animData: ['majestic_animations', 'rollie'], flag: 1, looped: true, music: 'rollie' },
			{ id: 15, title: 'animations.list.SaySo', animData: ['majestic_animations', 'say_so'], flag: 1, looped: true, music: 'say_so' },
			{
				id: 16,
				title: 'animations.list.SignatureShuffle',
				animData: ['majestic_animations', 'shuffle2'],
				flag: 1,
				looped: true,
				music: 'signature_shuffle',
			},
			{
				id: 17,
				title: 'animations.list.SquatKick',
				animData: ['majestic_animations', 'squat_kick'],
				flag: 1,
				looped: true,
				music: 'squat_kick',
			},
			{
				id: 18,
				title: 'animations.list.StepItUp',
				animData: ['majestic_animations', 'step_it_up'],
				flag: 0,
				looped: false,
				music: 'step_it_up',
			},
			{ id: 19, title: 'animations.list.TheFlow', animData: ['majestic_animations', 'the_flow'], flag: 1, looped: true, music: 'the_flow' },
			{
				id: 20,
				title: 'animations.list.TheRenegade',
				animData: ['majestic_animations_2', 'renegade'],
				flag: 1,
				looped: true,
				music: 'the_renegade',
			},
			{ id: 21, title: 'animations.list.Stuck', animData: ['majestic_animations_2', 'stuck'], flag: 1, looped: true, music: 'stuck' },
			{
				id: 22,
				title: 'animations.list.PumpUpTheJam',
				animData: ['majestic_animations_2', 'pump_up'],
				flag: 1,
				looped: true,
				music: 'pump_up',
			},
			{ id: 23, title: 'animations.list.Socks', animData: ['majestic_animations_2', 'socks'], flag: 1, looped: true, music: 'socks' },
			{
				id: 24,
				title: 'animations.list.MyWorld',
				animData: ['majestic_animations_2', 'my_world'],
				flag: 1,
				looped: true,
				music: 'my_world',
				pairAnims: [{ animData: ['majestic_animations_7', 'my_world_2'], flag: 1, looped: true }],
			},
			{ id: 25, title: 'animations.list.WakeUp', animData: ['majestic_animations_2', 'wake_up'], flag: 1, looped: true, music: 'wake_up' },
			{ id: 26, title: 'animations.list.OndaOnda', animData: ['majestic_animations_2', 'onda'], flag: 1, looped: true, music: 'onda' },
			{ id: 27, title: 'animations.list.GetGriddy', animData: ['majestic_animations_2', 'gridy'], flag: 33, looped: true, music: 'get_griddy' },
			{ id: 28, title: 'animations.list.HitIt', animData: ['majestic_animations_2', 'hit_it'], flag: 1, looped: true, music: 'hit_it' },
			{
				id: 29,
				title: 'animations.list.LeaveTheDoorOpen',
				animData: ['majestic_animations_2', 'leave_door_open'],
				flag: 1,
				looped: true,
				music: 'leave_door_open',
			},
			{
				id: 30,
				title: 'animations.list.ChickenWingIt',
				animData: ['majestic_animations_2', 'chicken_wing'],
				flag: 1,
				looped: true,
				music: 'chicken_wing',
			},
			{ id: 31, title: 'animations.list.Savage', animData: ['majestic_animations_2', 'savage'], flag: 1, looped: true, music: 'savage' },
			{
				id: 32,
				title: 'animations.list.ElectroSwing',
				animData: ['majestic_animations_2', 'electro_swing'],
				flag: 1,
				looped: true,
				music: 'electro_swing',
			},
			{
				id: 33,
				title: 'animations.list.Sprinkler',
				animData: ['majestic_animations_2', 'sprinkler'],
				flag: 1,
				looped: true,
				music: 'sprinkler',
			},
			{ id: 34, title: 'animations.list.Smeeze', animData: ['majestic_animations_3', 'smeeze'], flag: 1, looped: true, music: 'smeeze' },
			{ id: 35, title: 'animations.list.GoMufasa', animData: ['majestic_animations_3', 'mufasa'], flag: 33, looped: true, music: 'mufasa' },
			{
				id: 36,
				title: 'animations.list.HeyNow',
				animData: ['majestic_animations_3', 'hey_now'],
				flag: 1,
				looped: true,
				music: 'hey_now',
				pairAnims: [{ animData: ['majestic_animations_7', 'hey_now_2'], flag: 1, looped: true }],
			},
			{ id: 37, title: 'animations.list.BuildUp', animData: ['majestic_animations_3', 'build_up'], flag: 1, looped: true, music: 'build_up' },
			{
				id: 38,
				title: 'animations.list.TakeTheL',
				animData: ['majestic_animations_3', 'take_the_l'],
				flag: 1,
				looped: true,
				music: 'take_the_l',
			},
			{ id: 39, title: 'animations.list.Breakdown', animData: ['majestic_animations', 'hip_hop'], flag: 1, looped: true, music: 'breakdown' },
			{
				id: 40,
				title: 'animations.list.IAintAfraid',
				animData: ['majestic_animations_3', 'i_aint_afraid'],
				flag: 1,
				looped: true,
				music: 'i_aint_afraid',
			},
			{ id: 41, title: 'animations.list.GetGone', animData: ['majestic_animations_3', 'get_gone'], flag: 1, looped: true, music: 'get_gone' },
			{
				id: 42,
				title: 'animations.list.MaximumBounce',
				animData: ['majestic_animations_3', 'maximum_bounce'],
				flag: 1,
				looped: true,
				music: 'maximum_bounce',
			},
			{
				id: 43,
				title: 'animations.list.ILikeToMoveIt',
				animData: ['majestic_animations_3', 'like_to_move'],
				flag: 33,
				looped: true,
				music: 'like_to_move',
			},
			{
				id: 44,
				title: 'animations.list.LeiltElomr',
				animData: ['majestic_animations_3', 'leilt_elomr'],
				flag: 1,
				looped: true,
				music: 'leilt_elomr',
			},
			{ id: 45, title: 'animations.list.Tidy', animData: ['majestic_animations_3', 'tidy'], flag: 1, looped: true, music: 'tidy' },
			{
				id: 46,
				title: 'animations.list.BhangraBoogie',
				animData: ['majestic_animations_3', 'bhangra_boogie'],
				flag: 1,
				looped: true,
				music: 'bhangra_boogie',
			},
			{ id: 47, title: 'animations.list.OutWest', animData: ['majestic_animations_3', 'out_west'], flag: 1, looped: true, music: 'out_west' },
			{
				id: 48,
				title: 'animations.list.ToosieSlide',
				animData: ['majestic_animations_3', 'toosie_slide'],
				flag: 1,
				looped: true,
				music: 'toosie_slide',
			},
			{ id: 49, title: 'animations.list.PullUp', animData: ['majestic_animations_3', 'pull_up'], flag: 1, looped: true, music: 'pull_up' },
			{
				id: 50,
				title: 'animations.list.TheCraneKick',
				animData: ['majestic_animations_3', 'the_crane_kick'],
				flag: 0,
				looped: false,
				music: 'the_crane_kick',
			},
			{
				id: 51,
				title: 'animations.list.BillyBounce',
				animData: ['majestic_animations_3', 'billy_bounce'],
				flag: 1,
				looped: true,
				music: 'billy_bounce',
			},
			{
				id: 52,
				title: 'animations.list.ElectroShuffle',
				animData: ['majestic_animations_3', 'electro_shuffle'],
				flag: 1,
				looped: true,
				music: 'electro_shuffle',
			},
			{
				id: 53,
				title: 'animations.list.WorkItOut',
				animData: ['majestic_animations_4', 'work_it_out'],
				flag: 1,
				looped: true,
				music: 'work_it_out',
			},
			{ id: 54, title: 'animations.list.Zany', animData: ['majestic_animations_2', 'zany'], flag: 1, looped: true, music: 'zany' },
			{
				id: 55,
				title: 'animations.list.SmoothMoves',
				animData: ['majestic_animations_3', 'smooth_moves'],
				flag: 1,
				looped: true,
				music: 'smooth_moves',
			},
			{
				id: 56,
				title: 'animations.list.Vivacious',
				animData: ['majestic_animations_4', 'vivacious'],
				flag: 1,
				looped: true,
				music: 'vivacious',
			},
			{ id: 57, title: 'animations.list.Hula', animData: ['majestic_animations_4', 'hula'], flag: 1, looped: true, music: 'hula' },
			{
				id: 58,
				title: 'animations.list.TrueHeart',
				animData: ['majestic_animations_4', 'true_heart'],
				flag: 1,
				looped: true,
				music: 'true_heart',
			},
			{
				id: 59,
				title: 'animations.list.Reanimated',
				animData: ['majestic_animations_4', 'reanimated'],
				flag: 1,
				looped: true,
				music: 'reanimated',
			},
			{
				id: 60,
				title: 'animations.list.InDaParty',
				animData: ['majestic_animations_4', 'in_da_party'],
				flag: 33,
				looped: true,
				music: 'in_da_party',
			},
			{
				id: 61,
				title: 'animations.list.BimBamBoom',
				animData: ['majestic_animations_4', 'bim_bam_boom'],
				flag: 1,
				looped: true,
				music: 'bim_bam_boom',
			},
			{
				id: 62,
				title: 'animations.list.WannaSeeMe',
				animData: ['majestic_animations_4', 'wanna_see_me'],
				flag: 1,
				looped: true,
				music: 'wanna_see_me',
			},
			{
				id: 63,
				title: 'animations.list.DynamicShuffle',
				animData: ['majestic_animations_4', 'dynamic_shuffle'],
				flag: 1,
				looped: true,
				music: 'dynamic_shuffle',
			},
			{
				id: 64,
				title: 'animations.list.NeverGonna',
				animData: ['majestic_animations_4', 'never_gonna'],
				flag: 1,
				looped: true,
				music: 'never_gonna',
			},
			{
				id: 65,
				title: 'animations.list.FrightFunk',
				animData: ['majestic_animations_4', 'fright_funk'],
				flag: 1,
				looped: true,
				music: 'fright_funk',
			},
			{
				id: 66,
				title: 'animations.list.Jitterbug',
				animData: ['majestic_animations_4', 'jitterbug'],
				flag: 1,
				looped: true,
				music: 'jitterbug',
			},
			{
				id: 67,
				title: 'animations.list.Infectious',
				animData: ['majestic_animations_4', 'infectious'],
				flag: 1,
				looped: true,
				music: 'infectious',
			},
			{
				id: 68,
				title: 'animations.list.WhereIsMatt',
				animData: ['majestic_animations_4', 'where_is_matt'],
				flag: 1,
				looped: true,
				music: 'where_is_matt',
			},
			{
				id: 69,
				title: 'animations.list.SavorTheW',
				animData: ['majestic_animations_4', 'savor_the_w'],
				flag: 1,
				looped: true,
				music: 'savor_the_w',
			},
			{
				id: 70,
				title: 'animations.list.DanceTherapy',
				animData: ['majestic_animations_5', 'dance_therapy'],
				flag: 1,
				looped: true,
				music: 'dance_therapy',
			},
			{
				id: 71,
				title: 'animations.list.Intensity',
				animData: ['majestic_animations_5', 'intensity'],
				flag: 1,
				looped: true,
				music: 'intensity',
			},
			{
				id: 72,
				title: 'animations.list.RushinAround',
				animData: ['majestic_animations_5', 'rushin_around'],
				flag: 1,
				looped: true,
				music: 'rushin_around',
			},
			{
				id: 73,
				title: 'animations.list.AdvancedMath',
				animData: ['majestic_animations_5', 'advanced_math'],
				flag: 1,
				looped: true,
				music: 'advanced_math',
			},
			{
				id: 74,
				title: 'animations.list.BoldStance',
				animData: ['majestic_animations_5', 'bold_stance'],
				flag: 1,
				looped: true,
				music: 'bold_stance',
			},
			{ id: 75, title: 'animations.list.Freemix', animData: ['majestic_animations_5', 'freemix'], flag: 1, looped: true, music: 'freemix' },
			{
				id: 76,
				title: 'animations.list.Extraterrestrial',
				animData: ['majestic_animations_5', 'extraterrestrial'],
				flag: 1,
				looped: true,
				music: 'extraterrestrial',
			},
			{ id: 77, title: 'animations.list.Crabby', animData: ['majestic_animations_5', 'crabby'], flag: 1, looped: true, music: 'crabby' },
			{ id: 78, title: 'animations.list.Lavish', animData: ['majestic_animations_5', 'lavish'], flag: 1, looped: true, music: 'lavish' },
			{
				id: 79,
				title: 'animations.list.MimeTime',
				animData: ['majestic_animations_5', 'mime_time'],
				flag: 1,
				looped: true,
				music: 'mime_time',
			},
			{ id: 80, title: 'animations.list.TaiChi', animData: ['majestic_animations_5', 'tai_chi'], flag: 1, looped: true, music: 'tai_chi' },
			{
				id: 81,
				title: 'animations.list.LilBounce',
				animData: ['majestic_animations_props', 'hydraulics_player'],
				flag: 33,
				looped: true,
				music: 'lil_bounce',
				propList: [
					{
						prop: 'hydraulics_vehicle',
						propAnimData: ['majestic_animations_props', 'hydraulics_vehicle'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{ id: 82, title: 'animations.list.Daydream', animData: ['majestic_animations_5', 'daydream'], flag: 1, looped: true, music: 'daydream' },
			{ id: 83, title: 'animations.list.WorkIt', animData: ['majestic_animations_5', 'work_it'], flag: 1, looped: true, music: 'work_it' },
			{ id: 84, title: 'animations.list.Slick', animData: ['majestic_animations_5', 'slick'], flag: 1, looped: true, music: 'slick' },
			{
				id: 85,
				title: 'animations.list.Bombastic',
				animData: ['majestic_animations_5', 'bombastic'],
				flag: 1,
				looped: true,
				music: 'bombastic',
			},
			{
				id: 86,
				title: 'animations.list.ItsaVibe',
				animData: ['majestic_animations_5', 'its_a_vibe'],
				flag: 1,
				looped: true,
				music: 'its_a_vibe',
			},
			{
				id: 87,
				title: 'animations.list.WuTangIsForever',
				animData: ['majestic_animations_5', 'wutang_is_forever'],
				flag: 1,
				looped: true,
				music: 'wutang_is_forever',
			},
			{
				id: 88,
				title: 'animations.list.RootinTootin',
				animData: ['majestic_animations_5', 'rootin_tootin'],
				flag: 0,
				looped: false,
				music: 'rootin_tootin',
			},
			{
				id: 89,
				title: 'animations.list.Triumphant',
				animData: ['majestic_animations_5', 'triumphant'],
				flag: 1,
				looped: true,
				music: 'triumphant',
			},
			{
				id: 90,
				title: 'animations.list.LilDiplodoculus',
				animData: ['majestic_animations_props', 'alfredo_player'],
				flag: 33,
				looped: true,
				music: 'lil_diplodoculus',
				propList: [
					{
						prop: 'alfredo_gadget',
						propAnimData: ['majestic_animations_props', 'alfredo_gadget'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 91,
				title: 'animations.list.Frolic',
				animData: ['majestic_animations_props', 'layers_player'],
				flag: 1,
				looped: true,
				music: 'frolic',
				propList: [
					{
						prop: 'layers_gadget',
						propAnimData: ['majestic_animations_props', 'layers_gadget_left'],
						propAttachData: { bone: 18905, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'layers_gadget',
						propAnimData: ['majestic_animations_props', 'layers_gadget_right'],
						propAttachData: { bone: 57005, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 92,
				title: 'animations.list.PhoneItIn',
				animData: ['majestic_animations_props', 'epic_sax'],
				flag: 1,
				looped: true,
				music: 'phone_it_in',
				propList: [
					{
						prop: 'epic_sax',
						propAttachData: { bone: 18905, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 93,
				title: 'animations.list.LlamaBell',
				animData: ['majestic_animations_props', 'llama_cowbell'],
				flag: 1,
				looped: true,
				music: 'llama_bell',
				propList: [
					{
						prop: 'llama_cowbell',
						propAttachData: { bone: 18905, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'llama_stick',
						propAttachData: { bone: 57005, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 94,
				title: 'animations.list.MajesticCoinFlip',
				animData: ['majestic_animations_props', 'majestic_flipped'],
				flag: 0,
				looped: false,
				music: 'majestic_coinflip',
				propList: [
					{
						prop: 'majestic_coin',
						propAnimData: ['majestic_animations_props', 'majestic_coin'],
						propAttachData: { bone: 57005, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 95,
				title: 'animations.list.LilFloaticorn',
				animData: ['majestic_animations_props', 'llama_float_player'],
				flag: 33,
				looped: true,
				music: 'lil_floaticorn',
				propList: [
					{
						prop: 'llama_float',
						propAnimData: ['majestic_animations_props', 'llama_float'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 96,
				title: 'animations.list.Glowsticks',
				animData: ['majestic_animations_props', 'glowstick_dance'],
				flag: 1,
				looped: true,
				music: 'glowsticks',
				propList: [
					{
						prop: 'glowstick_prop',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'glowstick_prop',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 97,
				title: 'animations.list.ShakeItUp',
				animData: ['majestic_animations_props', 'shake_dance'],
				flag: 1,
				looped: true,
				music: 'shake_it_up',
				propList: [
					{
						prop: 'shake_prop',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'shake_prop',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 98,
				title: 'animations.list.IslandVibes',
				animData: ['majestic_animations_props', 'ukulele_dance'],
				flag: 1,
				looped: true,
				music: 'island_vibes',
				propList: [
					{
						prop: 'ukulele_prop',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 99,
				title: 'animations.list.SnareSolo',
				animData: ['majestic_animations_props', 'snare_solo_player'],
				flag: 1,
				looped: true,
				music: 'snare_solo',
				propList: [
					{
						prop: 'snare_solo',
						propAnimData: ['majestic_animations_props', 'snare_solo'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 100,
				title: 'animations.list.RockOut',
				animData: ['majestic_animations_props', 'rock_out_player'],
				flag: 1,
				looped: true,
				music: 'rock_out',
				propList: [
					{
						prop: 'guitar_walk',
						propAnimData: ['majestic_animations_props', 'rock_out'],
						propAttachData: { bone: 18905, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 101,
				title: 'animations.list.LilOctane',
				animData: ['majestic_animations_props', 'rhyme_lock_player'],
				flag: 33,
				looped: true,
				music: 'lil_octane',
				propList: [
					{
						prop: 'rhyme_lock',
						propAnimData: ['majestic_animations_props', 'rhyme_lock'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 102,
				title: 'animations.list.Unicycle',
				animData: ['majestic_animations_props', 'unicycle_gadget_player'],
				flag: 33,
				looped: true,
				music: 'unicycle',
				propList: [
					{
						prop: 'unicycle_gadget',
						propAnimData: ['majestic_animations_props', 'unicycle_gadget'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 103,
				title: 'animations.list.BannerWaves',
				animData: ['majestic_animations_props', 'banner_flag_player'],
				flag: 1,
				looped: true,
				propList: [
					{
						prop: 'banner_flag_prop',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'prop_flag_majestic',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 1.65), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 104,
				title: 'animations.list.BloominBouquet',
				animData: ['majestic_animations_props', 'bouquet_hat_player'],
				flag: 0,
				looped: false,
				music: 'bloomin_bouquet',
				propList: [
					{
						prop: 'bouquet_hat',
						propAnimData: ['majestic_animations_props', 'bouquet_hat'],
						propAttachData: { bone: 57005, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'bouquet_main',
						propAnimData: ['majestic_animations_props', 'bouquet_hat'],
						propAttachData: { bone: 18905, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 105,
				title: 'animations.list.LilMonster',
				animData: ['majestic_animations_props', 'car_lifted_player'],
				flag: 33,
				looped: true,
				music: 'lil_monster',
				propList: [
					{
						prop: 'car_lifted',
						propAnimData: ['majestic_animations_props', 'car_lifted'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 106,
				title: 'animations.list.GuitarWalk',
				animData: ['majestic_animations_props', 'guitar_walk_player'],
				flag: 1,
				looped: true,
				music: 'guitar_walk',
				propList: [
					{
						prop: 'guitar_walk',
						propAnimData: ['majestic_animations_props', 'guitar_walk'],
						propAttachData: { bone: 18905, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 107,
				title: 'animations.list.BestMates',
				animData: ['majestic_animations_6', 'best_mates'],
				flag: 1,
				looped: true,
				music: 'best_mates',
			},
			{
				id: 108,
				title: 'animations.list.OnYourMarks',
				animData: ['majestic_animations_6', 'on_your_marks'],
				flag: 0,
				looped: false,
				music: 'on_your_marks',
			},
			{
				id: 109,
				title: 'animations.list.LaidBackShuffle',
				animData: ['majestic_animations_6', 'laid_back_shuffle'],
				flag: 1,
				looped: true,
				music: 'laid_back_shuffle',
			},
			{
				id: 110,
				title: 'animations.list.ThePolloDance',
				animData: ['majestic_animations_6', 'pollo_dance'],
				flag: 1,
				looped: true,
				music: 'pollo_dance',
			},
			{ id: 111, title: 'animations.list.Scenario', animData: ['majestic_animations_6', 'scenario'], flag: 1, looped: true, music: 'scenario' },
			{
				id: 112,
				title: 'animations.list.BuckleUp',
				animData: ['majestic_animations_6', 'buckle_up'],
				flag: 1,
				looped: true,
				music: 'buckle_up',
			},
			{
				id: 113,
				title: 'animations.list.ItsComplicated',
				animData: ['majestic_animations_6', 'its_complicated'],
				flag: 1,
				looped: true,
				music: 'its_complicated',
			},
			{
				id: 114,
				title: 'animations.list.FreedomWheels',
				animData: ['majestic_animations_6', 'freedom_wheels'],
				flag: 1,
				looped: true,
				music: 'freedom_wheels',
			},
			{
				id: 115,
				title: 'animations.list.EverybodyLovesMe',
				animData: ['majestic_animations_6', 'everybody_loves_me'],
				flag: 1,
				looped: true,
				music: 'everybody_loves_me',
			},
			{
				id: 116,
				title: 'animations.list.Pirouette',
				animData: ['majestic_animations_6', 'pirouette'],
				flag: 1,
				looped: true,
				music: 'pirouette',
			},
			{
				id: 117,
				title: 'animations.list.LazerBlast',
				animData: ['majestic_animations_6', 'lazer_blast'],
				flag: 1,
				looped: true,
				music: 'lazer_blast',
			},
			{ id: 118, title: 'animations.list.Poki', animData: ['majestic_animations_6', 'poki'], flag: 1, looped: true, music: 'poki' },
			{ id: 119, title: 'animations.list.Leapin', animData: ['majestic_animations_6', 'leapin'], flag: 1, looped: true, music: 'leapin' },
			{
				id: 120,
				title: 'animations.list.WellRounded',
				animData: ['majestic_animations_6', 'well_rounded'],
				flag: 1,
				looped: true,
				music: 'well_rounded',
			},
			{ id: 121, title: 'animations.list.Flux', animData: ['majestic_animations_6', 'flux'], flag: 1, looped: true, music: 'flux' },
			{
				id: 122,
				title: 'animations.list.Whirlwind',
				animData: ['majestic_animations_6', 'whirlwind'],
				flag: 1,
				looped: true,
				music: 'whirlwind',
			},
			{ id: 123, title: 'animations.list.Jamboree', animData: ['majestic_animations_6', 'jamboree'], flag: 1, looped: true, music: 'jamboree' },
			{
				id: 124,
				title: 'animations.list.SlapHappy',
				animData: ['majestic_animations_6', 'slap_happy'],
				flag: 1,
				looped: true,
				music: 'slap_happy',
			},
			{
				id: 125,
				title: 'animations.list.DreamFeet',
				animData: ['majestic_animations_6', 'dream_feet'],
				flag: 1,
				looped: true,
				music: 'dream_feet',
			},
			{
				id: 126,
				title: 'animations.list.Switchstep',
				animData: ['majestic_animations_6', 'switchstep'],
				flag: 1,
				looped: true,
				music: 'switchstep',
			},
			{ id: 127, title: 'animations.list.Glitter', animData: ['majestic_animations_6', 'glitter'], flag: 1, looped: true, music: 'glitter' },
			{
				id: 128,
				title: 'animations.list.SugarRush',
				animData: ['majestic_animations_6', 'sugar_rush'],
				flag: 1,
				looped: true,
				music: 'sugar_rush',
			},
			{ id: 129, title: 'animations.list.Twist', animData: ['majestic_animations_6', 'twist'], flag: 1, looped: true, music: 'twist' },
			{ id: 130, title: 'animations.list.Howl', animData: ['majestic_animations_6', 'howl'], flag: 0, looped: false, music: 'howl' },
			{
				id: 131,
				title: 'animations.list.CrazyFeet',
				animData: ['majestic_animations_6', 'crazy_feet'],
				flag: 1,
				looped: true,
				music: 'crazy_feet',
			},
			{
				id: 132,
				title: 'animations.list.HotMarat',
				animData: ['majestic_animations_6', 'hot_marat'],
				flag: 1,
				looped: true,
				music: 'hot_marat',
			},
			{
				id: 133,
				title: 'animations.list.ShowStopper',
				animData: ['majestic_animations_6', 'show_stopper'],
				flag: 1,
				looped: true,
				music: 'show_stopper',
			},
			{ id: 134, title: 'animations.list.Boneless', animData: ['majestic_animations_6', 'boneless'], flag: 1, looped: true, music: 'boneless' },
			{ id: 135, title: 'animations.list.PopLock', animData: ['majestic_animations_6', 'pop_lock'], flag: 1, looped: true, music: 'pop_lock' },
			{ id: 136, title: 'animations.list.Steady', animData: ['majestic_animations_6', 'steady'], flag: 1, looped: true, music: 'steady' },
			{ id: 137, title: 'animations.list.Shimmer', animData: ['majestic_animations_6', 'shimmer'], flag: 1, looped: true, music: 'shimmer' },
			{ id: 138, title: 'animations.list.Springy', animData: ['majestic_animations_6', 'springy'], flag: 1, looped: true, music: 'springy' },
			{
				id: 139,
				title: 'animations.list.FreeFlow',
				animData: ['majestic_animations_6', 'free_flow'],
				flag: 1,
				looped: true,
				music: 'free_flow',
			},
			{ id: 140, title: 'animations.list.Conga', animData: ['majestic_animations_6', 'conga'], flag: 1, looped: true, music: 'conga' },
			{ id: 141, title: 'animations.list.DeepEnd', animData: ['majestic_animations_6', 'deep_end'], flag: 1, looped: true, music: 'deep_end' },
			{
				id: 142,
				title: 'animations.list.Pumpernickel',
				animData: ['majestic_animations_6', 'pumpernickel'],
				flag: 1,
				looped: true,
				music: 'pumpernickel',
			},
			{
				id: 143,
				title: 'animations.list.Jubilation',
				animData: ['majestic_animations_6', 'jubilation'],
				flag: 1,
				looped: true,
				music: 'jubilation',
			},
			{
				id: 144,
				title: 'animations.list.Jaywalking',
				animData: ['majestic_animations_6', 'jaywalking'],
				flag: 1,
				looped: true,
				music: 'jaywalking',
			},
			{
				id: 145,
				title: 'animations.list.PeaceOut',
				animData: ['majestic_animations_6', 'peace_out'],
				flag: 0,
				looped: false,
				music: 'peace_out',
			},
			{ id: 146, title: 'animations.list.Hype', animData: ['majestic_animations_6', 'hype'], flag: 1, looped: true, music: 'hype' },
			{
				id: 147,
				title: 'animations.list.OrangeJustice',
				animData: ['majestic_animations_6', 'orange_justice'],
				flag: 1,
				looped: true,
				music: 'orange_justice',
			},
			{ id: 148, title: 'animations.list.SwipeIt', animData: ['majestic_animations_6', 'swipe_it'], flag: 1, looped: true, music: 'swipe_it' },
			{
				id: 149,
				title: 'animations.list.JumpAround',
				animData: ['majestic_animations_7', 'jump_around'],
				flag: 33,
				looped: true,
				music: 'jump_around',
			},
			{
				id: 150,
				title: 'animations.list.MonsterMash',
				animData: ['majestic_animations_7', 'monster_mash'],
				flag: 1,
				looped: true,
				music: 'monster_mash',
			},
			{
				id: 151,
				title: 'animations.list.FeelTheFlow',
				animData: ['majestic_animations_7', 'feel_the_flow'],
				flag: 1,
				looped: true,
				music: 'feel_the_flow',
			},
			{ id: 152, title: 'animations.list.Copines', animData: ['majestic_animations_7', 'copines'], flag: 1, looped: true, music: 'copines' },
			{ id: 153, title: 'animations.list.JiggleJiggle', animData: ['majestic_animations_7', 'jiggle'], flag: 1, looped: true, music: 'jiggle' },
			{
				id: 154,
				title: 'animations.list.ForgetMeNot',
				animData: ['majestic_animations_7', 'forget_me'],
				flag: 1,
				looped: true,
				music: 'forget_me_not',
			},
			{
				id: 155,
				title: 'animations.list.DrippinFlavor',
				animData: ['majestic_animations_7', 'chilled'],
				flag: 1,
				looped: true,
				music: 'drippin_flavor',
			},
			{
				id: 156,
				title: 'animations.list.Distraction',
				animData: ['majestic_animations_7', 'distraction'],
				flag: 1,
				looped: true,
				music: 'distraction',
			},
			{
				id: 157,
				title: 'animations.list.UCantCMe',
				animData: ['majestic_animations_7', 'ucan_cme'],
				flag: 0,
				looped: false,
				music: 'ucan_cme',
			},
			{
				id: 158,
				title: 'animations.list.TacoTime',
				animData: ['majestic_animations_props', 'taco_time'],
				flag: 1,
				looped: true,
				music: 'taco_time',
				propList: [
					{
						prop: 'prop_taco_01',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'prop_taco_01',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 159,
				title: 'animations.list.Snowshaker',
				animData: ['majestic_animations_props_2', 'snowglobe'],
				flag: 0,
				looped: false,
				music: 'snowglobe',
				propList: [
					{
						prop: 'mj_snowglobe_prop',
						propAnimData: ['majestic_animations_props_2', 'snowglobe_prop'],
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_snowflake',
						propAnimData: ['majestic_animations_props_2', 'snowflake'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 160,
				title: 'animations.list.RideAlong',
				animData: ['majestic_animations_props_2', 'mj_sleigh_player'],
				flag: 33,
				looped: true,
				music: 'ride_along',
				propList: [
					{
						prop: 'mj_sleigh_dlc',
						propAnimData: ['majestic_animations_props_2', 'mj_sleigh_dlc'],
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_sleigh',
						propAnimData: ['majestic_animations_props_2', 'mj_sleigh'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 161,
				title: 'animations.list.SingAlong',
				animData: ['majestic_animations_props_2', 'sing_along_player'],
				flag: 1,
				looped: true,
				music: 'sing_along_1',
				propList: [
					{
						prop: 'mj_sing_along',
						propAnimData: ['majestic_animations_props_2', 'sing_along_prop'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_snowflake',
						propAnimData: ['majestic_animations_props_2', 'snowflake_2'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
				pairAnims: [
					{
						/* offsetPos: {x: 0, y: 1.5, z: 0, r: 180},       */ animData: ['majestic_animations_props_2', 'sing_along_player'],
						flag: 1,
						looped: true,
						music: 'sing_along_2',
						propList: [
							{
								prop: 'mj_sing_along',
								propAnimData: ['majestic_animations_props_2', 'sing_along_prop'],
								propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
							{
								prop: 'mj_snowflake',
								propAnimData: ['majestic_animations_props_2', 'snowflake_2'],
								propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						/* offsetPos: {x: -0.75, y: 0.755, z: 0, r: 90},  */ animData: ['majestic_animations_props_2', 'sing_along_player'],
						flag: 1,
						looped: true,
						music: 'sing_along_3',
						propList: [
							{
								prop: 'mj_sing_along',
								propAnimData: ['majestic_animations_props_2', 'sing_along_prop'],
								propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
							{
								prop: 'mj_snowflake',
								propAnimData: ['majestic_animations_props_2', 'snowflake_2'],
								propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						/* offsetPos: {x: 0.75, y: 0.75, z: 0, r: -90},   */ animData: ['majestic_animations_props_2', 'sing_along_player'],
						flag: 1,
						looped: true,
						music: 'sing_along_4',
						propList: [
							{
								prop: 'mj_sing_along',
								propAnimData: ['majestic_animations_props_2', 'sing_along_prop'],
								propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
							{
								prop: 'mj_snowflake',
								propAnimData: ['majestic_animations_props_2', 'snowflake_2'],
								propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
				],
			}, // Сделать чтобы можно было в 4м вместе петь
			{
				id: 162,
				title: 'animations.list.Unwrapped',
				animData: ['majestic_animations_props_2', 'unwrapped_player'],
				flag: 0,
				looped: false,
				music: 'unwrapped',
				propList: [
					{
						prop: 'mj_unwrapped_prop',
						propAnimData: ['majestic_animations_props_2', 'unwrapped_prop'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 163,
				title: 'animations.list.DoubleUp',
				animData: ['majestic_animations_7', 'double_up'],
				flag: 1,
				looped: true,
				music: 'double_up',
				pairAnims: [{ animData: ['majestic_animations_7', 'double_up'], flag: 1, looped: true }],
			},
			{
				id: 164,
				title: 'animations.list.Sway',
				animData: ['majestic_animations_7', 'sway_1'],
				flag: 1,
				looped: true,
				music: 'sway',
				pairAnims: [{ animData: ['majestic_animations_7', 'sway_2'], flag: 1, looped: true }],
			},
			{
				id: 165,
				title: 'animations.list.ItsDynamite',
				animData: ['majestic_animations_7', 'its_dynamite'],
				flag: 1,
				looped: true,
				music: 'its_dynamite',
				pairAnims: [{ animData: ['majestic_animations_7', 'its_dynamite'], flag: 1, looped: true }],
			},
			{
				id: 166,
				title: 'animations.list.MaYaHi',
				animData: ['majestic_animations_props_2', 'mayahi_player'],
				flag: 1,
				looped: true,
				music: 'mayahi',
				propList: [
					{
						prop: 'mj_mayahi_prop',
						propAnimData: ['majestic_animations_props_2', 'mayahi_prop'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 167,
				title: 'animations.list.JugBand',
				animData: ['majestic_animations_props_2', 'jug'],
				flag: 1,
				looped: true,
				music: 'jug_1',
				propList: [
					{
						prop: 'mj_jug',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
				pairAnims: [
					{
						animData: ['majestic_animations_props_2', 'fiddle_player'],
						flag: 1,
						looped: true,
						music: 'jug_2',
						propList: [
							{
								prop: 'mj_fiddle',
								propAnimData: ['majestic_animations_props_2', 'fiddle_prop'],
								propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_2', 'banjo_player'],
						flag: 1,
						looped: true,
						music: 'jug_3',
						propList: [
							{
								prop: 'mj_banjo',
								propAnimData: ['majestic_animations_props_2', 'banjo_prop'],
								propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_2', 'guitar_player'],
						flag: 1,
						looped: true,
						music: 'jug_4',
						propList: [
							{
								prop: 'mj_guitar',
								propAnimData: ['majestic_animations_props_2', 'guitar_prop'],
								propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
				],
			},
			{
				id: 168,
				title: 'animations.list.GetSchwifty',
				animData: ['majestic_animations_props_2', 'get_swifty_1'],
				flag: 1,
				looped: true,
				music: 'get_swifty',
				propList: [
					{
						prop: 'mj_get_swifty_micro',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
				pairAnims: [
					{
						animData: ['majestic_animations_props_2', 'get_swifty_2'],
						flag: 1,
						looped: true,
						propList: [
							{
								prop: 'mj_get_swifty_tambourine',
								propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
				],
			},
			{
				id: 169,
				title: 'animations.list.ShantyForASquad',
				animData: ['majestic_animations_props_2', 'shanty'],
				flag: 33,
				looped: true,
				music: 'shanty_1',
				propList: [
					{
						prop: 'mj_shanty',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
				pairAnims: [
					{
						animData: ['majestic_animations_props_2', 'shanty'],
						flag: 33,
						looped: true,
						music: 'shanty_2',
						propList: [
							{
								prop: 'mj_shanty',
								propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_2', 'shanty'],
						flag: 33,
						looped: true,
						music: 'shanty_3',
						propList: [
							{
								prop: 'mj_shanty',
								propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_2', 'shanty'],
						flag: 33,
						looped: true,
						music: 'shanty_4',
						propList: [
							{
								prop: 'mj_shanty',
								propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
				],
			},
			{
				id: 170,
				title: 'animations.list.LilPrancer',
				animData: ['majestic_animations_props_2', 'prancer_player'],
				flag: 33,
				looped: true,
				music: 'prancer',
				propList: [
					{
						prop: 'mj_prancer',
						propAnimData: ['majestic_animations_props_2', 'prancer_prop'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_prancer_left',
						propAnimData: ['majestic_animations_props_2', 'prancer_prop_left'],
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_prancer_right',
						propAnimData: ['majestic_animations_props_2', 'prancer_prop_right'],
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 171,
				title: 'animations.list.SlalomStyle',
				animData: ['majestic_animations_props_2', 'slalom_player'],
				flag: 33,
				looped: true,
				music: 'slalom',
				allowSprint: true,
				propList: [
					{
						prop: 'mj_slalom',
						propAnimData: ['majestic_animations_props_2', 'slalom_prop_left'],
						propAttachData: { bone: 2108, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_slalom',
						propAnimData: ['majestic_animations_props_2', 'slalom_prop_right'],
						propAttachData: { bone: 20781, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 172,
				title: 'animations.list.BounceWitIt',
				animData: ['majestic_animations_7', 'bounce_wit_it'],
				flag: 1,
				looped: true,
				music: 'bounce_wit_it',
			},
			{
				id: 173,
				title: 'animations.list.DanceMonkey',
				animData: ['majestic_animations_7', 'dance_monkey'],
				flag: 1,
				looped: true,
				music: 'dance_monkey',
			},
			{
				id: 174,
				title: 'animations.list.SideShuffle',
				animData: ['majestic_animations_8', 'side_shuffle'],
				flag: 1,
				looped: true,
				music: 'side_shuffle',
			},
			{ id: 175, title: 'animations.list.Flapper', animData: ['majestic_animations_8', 'flapper'], flag: 1, looped: true, music: 'flapper' },
			{ id: 176, title: 'animations.list.Vibin', animData: ['majestic_animations_8', 'vibin'], flag: 1, looped: true, music: 'vibin' },
			{
				id: 177,
				title: 'animations.list.TheRobot',
				animData: ['majestic_animations_8', 'the_robot'],
				flag: 1,
				looped: true,
				music: 'the_robot',
			},
			{
				id: 178,
				title: 'animations.list.GrooveJam',
				animData: ['majestic_animations_8', 'groove_jam'],
				flag: 1,
				looped: true,
				music: 'groove_jam',
			},
			{ id: 179, title: 'animations.list.Flamenco', animData: ['majestic_animations_8', 'flamenco'], flag: 1, looped: true, music: 'flamenco' },
			{
				id: 180,
				title: 'animations.list.TheRickDance',
				animData: ['majestic_animations_8', 'rick_dance'],
				flag: 1,
				looped: true,
				music: 'rick_dance',
			},
			{
				id: 181,
				title: 'animations.list.Crackdown',
				animData: ['majestic_animations_8', 'crackdown'],
				flag: 1,
				looped: true,
				music: 'crackdown',
			},
			{
				id: 182,
				title: 'animations.list.PrimoMoves',
				animData: ['majestic_animations_8', 'primo_moves'],
				flag: 1,
				looped: true,
				music: 'primo_moves',
			},
			{ id: 183, title: 'animations.list.Balletic', animData: ['majestic_animations_8', 'balletic'], flag: 1, looped: true, music: 'balletic' },
			{
				id: 184,
				title: 'animations.list.InfiniteDab',
				animData: ['majestic_animations_8', 'infinite_dab'],
				flag: 1,
				looped: true,
				music: 'infinite_dab',
			},
			{
				id: 185,
				title: 'animations.list.HandSignals',
				animData: ['majestic_animations_8', 'hand_signals'],
				flag: 0,
				looped: false,
				music: 'hand_signals',
			},
			{
				id: 186,
				title: 'animations.list.FancyFeet',
				animData: ['majestic_animations_8', 'fancy_feet'],
				flag: 1,
				looped: true,
				music: 'fancy_feet',
			},
			{
				id: 187,
				title: 'animations.list.CleanGroove',
				animData: ['majestic_animations_8', 'clean_groove'],
				flag: 1,
				looped: true,
				music: 'clean_groove',
			},
			{
				id: 188,
				title: 'animations.list.OldSchool',
				animData: ['majestic_animations_8', 'old_school'],
				flag: 1,
				looped: true,
				music: 'old_school',
			},
			{
				id: 189,
				title: 'animations.list.Intdroducing',
				animData: ['majestic_animations_8', 'introducing'],
				flag: 1,
				looped: true,
				music: 'introducing',
			},
			{
				id: 190,
				title: 'animations.list.SkaStraTerrestrial',
				animData: ['majestic_animations_8', 'terrestrial'],
				flag: 1,
				looped: true,
				music: 'terrestrial',
			},
			{
				id: 191,
				title: 'animations.list.YoureAwesome',
				animData: ['majestic_animations_8', 'youre_awesome'],
				flag: 0,
				looped: false,
				music: 'youre_awesome',
			},
			{
				id: 192,
				title: 'animations.list.CluckStrut',
				animData: ['majestic_animations_8', 'cluck_strut'],
				flag: 1,
				looped: true,
				music: 'cluck_strut',
			},
			{
				id: 193,
				title: 'animations.list.Slitherin',
				animData: ['majestic_animations_8', 'slitherin'],
				flag: 1,
				looped: true,
				music: 'slitherin',
			},
			{
				id: 194,
				title: 'animations.list.ItsGoTime',
				animData: ['majestic_animations_8', 'its_go_time'],
				flag: 1,
				looped: true,
				music: 'its_go_time',
			},
			{
				id: 195,
				title: 'animations.list.GetFunky',
				animData: ['majestic_animations_8', 'get_funky'],
				flag: 1,
				looped: true,
				music: 'get_funky',
			},
			{
				id: 196,
				title: 'animations.list.NanaNana',
				animData: ['majestic_animations_8', 'nana_nana'],
				flag: 1,
				looped: true,
				music: 'nana_nana',
			},
			{
				id: 197,
				title: 'animations.list.SideHustle',
				animData: ['majestic_animations_8', 'side_hustle'],
				flag: 1,
				looped: true,
				music: 'side_hustle',
			},
			{ id: 198, title: 'animations.list.Droop', animData: ['majestic_animations_8', 'droop'], flag: 1, looped: true, music: 'droop' },
			{
				id: 199,
				title: 'animations.list.MashedPotato',
				animData: ['majestic_animations_8', 'mashed_potato'],
				flag: 1,
				looped: true,
				music: 'mashed_potato',
			},
			{ id: 200, title: 'animations.list.Verve', animData: ['majestic_animations_8', 'verve'], flag: 1, looped: true, music: 'verve' },
			{ id: 201, title: 'animations.list.Gloss', animData: ['majestic_animations_8', 'gloss'], flag: 1, looped: true, music: 'gloss' },
			{ id: 202, title: 'animations.list.MyIdol', animData: ['majestic_animations_8', 'my_idol'], flag: 1, looped: true, music: 'my_idol' },
			{
				id: 203,
				title: 'animations.list.PawsClaws',
				animData: ['majestic_animations_9', 'paws_claws'],
				flag: 1,
				looped: true,
				music: 'paws_claws',
			},
			{
				id: 204,
				title: 'animations.list.RunningMan',
				animData: ['majestic_animations_9', 'running_man'],
				flag: 1,
				looped: true,
				music: 'running_man',
			},
			{
				id: 205,
				title: 'animations.list.LivingLarge',
				animData: ['majestic_animations_9', 'living_large'],
				flag: 1,
				looped: true,
				music: 'living_large',
			},
			{
				id: 206,
				title: 'animations.list.Hootenanny',
				animData: ['majestic_animations_9', 'hootenanny'],
				flag: 1,
				looped: true,
				music: 'hootenanny',
			},
			{
				id: 207,
				title: 'animations.list.DirtbikeChallenge',
				animData: ['majestic_animations_9', 'dirtbike_challenge'],
				flag: 1,
				looped: true,
				music: 'dirtbike_challenge',
			},
			{
				id: 208,
				title: 'animations.list.LunarParty',
				animData: ['majestic_animations_9', 'lunar_party'],
				flag: 1,
				looped: true,
				music: 'lunar_party',
			},
			{ id: 209, title: 'animations.list.TheLook', animData: ['majestic_animations_9', 'the_look'], flag: 1, looped: true, music: 'the_look' },
			{ id: 210, title: 'animations.list.Revel', animData: ['majestic_animations_9', 'revel'], flag: 1, looped: true, music: 'revel' },
			{
				id: 211,
				title: 'animations.list.ImDiamond',
				animData: ['majestic_animations_9', 'im_diamond'],
				flag: 1,
				looped: true,
				music: 'im_diamond',
			},
			{
				id: 212,
				title: 'animations.list.Hitchhiker',
				animData: ['majestic_animations_9', 'hitchhiker'],
				flag: 1,
				looped: true,
				music: 'hitchhiker',
			},
			{
				id: 213,
				title: 'animations.list.Waterworks',
				animData: ['majestic_animations_9', 'waterworks'],
				flag: 1,
				looped: true,
				music: 'waterworks',
			},
			{
				id: 214,
				title: 'animations.list.PickItUp',
				animData: ['majestic_animations_9', 'pick_it_up'],
				flag: 1,
				looped: true,
				music: 'pick_it_up',
			},
			{
				id: 215,
				title: 'animations.list.CaliforniaGurls',
				animData: ['majestic_animations_9', 'california_gurls'],
				flag: 1,
				looped: true,
				music: 'california_gurls',
			},
			{
				id: 216,
				title: 'animations.list.BBoomBBoom',
				animData: ['majestic_animations_9', 'bboom_bboom'],
				flag: 1,
				looped: true,
				music: 'bboom_bboom',
			},
			{
				id: 217,
				title: 'animations.list.HangLooseCelebration',
				animData: ['majestic_animations_10', 'hang_loose_celebration'],
				flag: 1,
				looped: true,
				music: 'hang_loose_celebration',
			},
			{ id: 218, title: 'animations.list.Tootsee', animData: ['majestic_animations_10', 'tootsee'], flag: 1, looped: true, music: 'tootsee' },
			{
				id: 219,
				title: 'animations.list.TheDanceLaroi',
				animData: ['majestic_animations_10', 'the_dance_laroi'],
				flag: 1,
				looped: true,
				music: 'the_dance_laroi',
			},
			{
				id: 220,
				title: 'animations.list.DanceOff',
				animData: ['majestic_animations_10', 'dance_off'],
				flag: 1,
				looped: true,
				music: 'dance_off',
			},
			{
				id: 221,
				title: 'animations.list.FishyFlourish',
				animData: ['majestic_animations_10', 'fishy_flourish'],
				flag: 1,
				looped: true,
				music: 'fishy_flourish',
			},
			{
				id: 222,
				title: 'animations.list.Freestylin',
				animData: ['majestic_animations_10', 'freestylin'],
				flag: 1,
				looped: true,
				music: 'freestylin',
			},
			{ id: 223, title: 'animations.list.Glyphic', animData: ['majestic_animations_10', 'glyphic'], flag: 1, looped: true, music: 'glyphic' },
			{
				id: 224,
				title: 'animations.list.Fandangle',
				animData: ['majestic_animations_10', 'fandalangle'],
				flag: 1,
				looped: true,
				music: 'fandalangle',
			},
			{
				id: 225,
				title: 'animations.list.MarshWalk',
				animData: ['majestic_animations_10', 'marsh_walk'],
				flag: 1,
				looped: true,
				music: 'marsh_walk',
			},
			{
				id: 226,
				title: 'animations.list.LazyShuffle',
				animData: ['majestic_animations_10', 'lazy_shuffle'],
				flag: 1,
				looped: true,
				music: 'lazy_shuffle',
			},
			{
				id: 227,
				title: 'animations.list.Backstroke',
				animData: ['majestic_animations_10', 'backstroke'],
				flag: 1,
				looped: true,
				music: 'backstroke',
			},
			{
				id: 228,
				title: 'animations.list.CrissCross',
				animData: ['majestic_animations_10', 'criss_cross'],
				flag: 1,
				looped: true,
				music: 'criss_cross',
			},
			{
				id: 229,
				title: 'animations.list.PartyHips',
				animData: ['majestic_animations_10', 'party_hips'],
				flag: 1,
				looped: true,
				music: 'party_hips',
			},
			{
				id: 230,
				title: 'animations.list.LlamaConga',
				animData: ['majestic_animations_10', 'llama_conga'],
				flag: 1,
				looped: true,
				music: 'llama_conga',
			},
			{
				id: 231,
				title: 'animations.list.JumpingJacks',
				animData: ['majestic_animations_10', 'jumping_jacks'],
				flag: 1,
				looped: true,
				music: 'jumping_jacks',
			},
			{ id: 232, title: 'animations.list.Shout', animData: ['majestic_animations_10', 'shout'], flag: 1, looped: true, music: 'shout' },
			{ id: 233, title: 'animations.list.Yay', animData: ['majestic_animations_10', 'yay'], flag: 1, looped: true, music: 'yay' },
			{ id: 234, title: 'animations.list.Forever', animData: ['majestic_animations_9', 'forever'], flag: 1, looped: true, music: 'forever' },
			{
				id: 235,
				title: 'animations.list.TheMagicBomb',
				animData: ['majestic_animations_10', 'the_magic_bomb'],
				flag: 1,
				looped: true,
				music: 'the_magic_bomb',
			},
			{
				id: 236,
				title: 'animations.list.RollNRock',
				animData: ['majestic_animations_10', 'roll_n_rock'],
				flag: 1,
				looped: true,
				music: 'roll_n_rock',
			},
			{ id: 237, title: 'animations.list.WarmUp', animData: ['majestic_animations_11', 'warm_up'], flag: 1, looped: true, music: 'warm_up' },
			{
				id: 238,
				title: 'animations.list.GunslingerSmokeshow',
				animData: ['majestic_animations_11', 'gungslinger_smokeshow'],
				flag: 1,
				looped: true,
				music: 'gunslinger_smokeshow',
			},
			{
				id: 239,
				title: 'animations.list.SweetShot',
				animData: ['majestic_animations_11', 'sweet_shot'],
				flag: 1,
				looped: true,
				music: 'sweet_shot',
			},
			{
				id: 240,
				title: 'animations.list.VibrantVibin',
				animData: ['majestic_animations_11', 'vibrant_vibin'],
				flag: 1,
				looped: true,
				music: 'vibrant_vibin',
			},
			{
				id: 241,
				title: 'animations.list.KoiDance',
				animData: ['majestic_animations_11', 'koi_dance'],
				flag: 1,
				looped: true,
				music: 'koi_dance',
			},
			{
				id: 242,
				title: 'animations.list.TheQuickStyle',
				animData: ['majestic_animations_11', 'quick_style'],
				flag: 1,
				looped: true,
				music: 'quick_style',
			},
			{
				id: 243,
				title: 'animations.list.MadeYouLook',
				animData: ['majestic_animations_11', 'made_you_look'],
				flag: 1,
				looped: true,
				music: 'made_you_look',
			},
			{ id: 244, title: 'animations.list.AskMe', animData: ['majestic_animations_11', 'ask_me'], flag: 1, looped: true, music: 'ask_me' },
			{
				id: 245,
				title: 'animations.list.Komarovo',
				animData: ['majestic_animations_props_3', 'atomic_synth_player'],
				flag: 1,
				looped: true,
				music: 'atomic_synth',
				propList: [
					{
						prop: 'mj_atomic_synth',
						propAnimData: ['majestic_animations_props_3', 'atomic_synth_prop'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 246,
				title: 'animations.list.Sleds',
				animData: ['majestic_animations_props_2', 'sled_player'],
				flag: 33,
				looped: true,
				music: 'sleds',
				propList: [
					{
						prop: 'mj_sleigh_custom',
						propAnimData: ['majestic_animations_props_2', 'sled_prop'],
						propAttachData: { bone: 0, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 247,
				title: 'animations.list.RingItOn',
				animData: ['majestic_animations_props_3', 'ring_it_on'],
				flag: 1,
				looped: true,
				music: 'ring_it_on',
				propList: [
					{
						prop: 'mj_bell_ring_prop',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_bell_ring_prop',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 248,
				title: 'animations.list.Boomin',
				animData: ['majestic_animations_props_3', 'boombox_player'],
				flag: 1,
				looped: true,
				music: 'boomin',
				propList: [
					{
						prop: 'prop_boombox_01',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 249,
				title: 'animations.list.BootsNCats',
				animData: ['majestic_animations_props_3', 'boots_n_cats_player'],
				flag: 1,
				looped: true,
				music: 'boots_and_cats',
				propList: [
					{
						prop: 'mj_microphone_prop',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 250,
				title: 'animations.list.Rage',
				animData: ['majestic_animations_props_3', 'mic_stand_player'],
				flag: 1,
				looped: true,
				music: 'rage',
				propList: [
					{
						prop: 'mj_mic_stand',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 251,
				title: 'animations.list.IDeclare',
				animData: ['majestic_animations_props_3', 'declare'],
				flag: 49,
				looped: true,
				music: 'declare',
				propList: [
					{
						prop: 'mj_declare_prop',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 252,
				title: 'animations.list.RocketRodeo',
				animData: ['majestic_animations_props_3', 'rocket_rodeo_player'],
				flag: 33,
				looped: true,
				music: 'rocket_rodeo',
				allowSprint: true,
				propList: [
					{
						prop: 'mj_rocket_rodeo',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 253,
				title: 'animations.list.DrumMajor',
				animData: ['majestic_animations_props_3', 'drum_major_player'],
				flag: 1,
				looped: true,
				music: 'drum_major',
				propList: [
					{
						prop: 'mj_drum_major_stick',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 254,
				title: 'animations.list.PumpItUp',
				animData: ['majestic_animations_props_3', 'pump_it_up'],
				flag: 1,
				looped: true,
				music: 'pump_it_up',
				propList: [
					{
						prop: 'mj_pump_mask',
						propAttachData: { bone: 12844, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 255,
				title: 'animations.list.CheerUp',
				animData: ['majestic_animations_props_3', 'cheer_up'],
				flag: 1,
				looped: true,
				music: 'cheer_up',
				propList: [
					{
						prop: 'mj_cheer_up_prop',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_cheer_up_prop',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 256,
				title: 'animations.list.EmpressFanDance',
				animData: ['majestic_animations_props_3', 'empress_fan_dance'],
				flag: 1,
				looped: true,
				music: 'empress_fan_dance',
				propList: [
					{
						prop: 'mj_declare_prop',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 257,
				title: 'animations.list.ManeraMir',
				animData: ['majestic_animations_dances_1', 'manera'],
				flag: 1,
				looped: true,
				music: 'manera_mir',
			},
			{
				id: 258,
				title: 'animations.list.AirShredder',
				animData: ['majestic_animations_11', 'air_shredder'],
				flag: 1,
				looped: true,
				music: 'air_shredder',
			},
			{
				id: 259,
				title: 'animations.list.CrazyBoy',
				animData: ['majestic_animations_11', 'crazy_boy'],
				flag: 1,
				looped: true,
				music: 'crazy_boy',
			},
			{ id: 260, title: 'animations.list.Fishin', animData: ['majestic_animations_11', 'fishin'], flag: 33, looped: true, music: 'fishin' },
			{
				id: 261,
				title: 'animations.list.NinjaStyle',
				animData: ['majestic_animations_11', 'ninja_style'],
				flag: 1,
				looped: true,
				music: 'ninja_style',
			},
			{ id: 262, title: 'animations.list.TheWorm', animData: ['majestic_animations_11', 'the_worm'], flag: 1, looped: true, music: 'the_worm' },
			{ id: 263, title: 'animations.list.Wiggle', animData: ['majestic_animations_11', 'wiggle'], flag: 1, looped: true, music: 'wiggle' },
			{
				id: 264,
				title: 'animations.list.StarPower',
				animData: ['majestic_animations_11', 'star_power'],
				flag: 1,
				looped: true,
				music: 'star_power',
			},
			{
				id: 265,
				title: 'animations.list.Rambunctious',
				animData: ['majestic_animations_12', 'rambunctious'],
				flag: 1,
				looped: true,
				music: 'rambunctious',
			},
			{ id: 266, title: 'animations.list.Rawr', animData: ['majestic_animations_12', 'rawr'], flag: 0, looped: true, music: 'rawr' },
			{
				id: 267,
				title: 'animations.list.FastFeet',
				animData: ['majestic_animations_12', 'fast_feet'],
				flag: 1,
				looped: true,
				music: 'fast_feet',
			},
			{
				id: 268,
				title: 'animations.list.Capoeira',
				animData: ['majestic_animations_12', 'capoeira'],
				flag: 1,
				looped: true,
				music: 'capoeira',
			},
			{ id: 269, title: 'animations.list.Bobbin', animData: ['majestic_animations_12', 'bobbin'], flag: 1, looped: true, music: 'bobbin' },
			{
				id: 270,
				title: 'animations.list.Overdrive',
				animData: ['majestic_animations_12', 'overdrive'],
				flag: 1,
				looped: true,
				music: 'overdrive',
			},
			{
				id: 271,
				title: 'animations.list.Fanciful',
				animData: ['majestic_animations_12', 'fanciful'],
				flag: 33,
				looped: true,
				music: 'fanciful',
			},
			{
				id: 272,
				title: 'animations.list.BunnyHop',
				animData: ['majestic_animations_12', 'bunny_hop'],
				flag: 33,
				looped: true,
				music: 'bunny_hop',
			},
			{ id: 273, title: 'animations.list.NoSweat', animData: ['majestic_animations_12', 'no_sweat'], flag: 1, looped: true, music: 'no_sweat' },
			{
				id: 274,
				title: 'animations.list.WindmillFloss',
				animData: ['majestic_animations_12', 'windmill_floss'],
				flag: 1,
				looped: true,
				music: 'windmill_floss',
			},
			{
				id: 275,
				title: 'animations.list.SwoleCat',
				animData: ['majestic_animations_12', 'swole_cat'],
				flag: 1,
				looped: true,
				music: 'swole_cat',
			},
			{
				id: 276,
				title: 'animations.list.HeadBanger',
				animData: ['majestic_animations_12', 'head_banger'],
				flag: 1,
				looped: true,
				music: 'head_banger',
			},
			{
				id: 277,
				title: 'animations.list.GetLoose',
				animData: ['majestic_animations_12', 'get_loose'],
				flag: 1,
				looped: true,
				music: 'get_loose',
			},
			{ id: 278, title: 'animations.list.Bully', animData: ['majestic_animations_12', 'bully'], flag: 1, looped: true, music: 'bully' },
			{
				id: 279,
				title: 'animations.list.BringItAround',
				animData: ['majestic_animations_12', 'bring_it_around'],
				flag: 1,
				looped: true,
				music: 'bring_it_around',
			},
			{
				id: 280,
				title: 'animations.list.SquareUp',
				animData: ['majestic_animations_12', 'square_up'],
				flag: 1,
				looped: true,
				music: 'square_up',
			},
			{
				id: 281,
				title: 'animations.list.WithoutYou',
				animData: ['majestic_animations_12', 'without_you'],
				flag: 1,
				looped: true,
				music: 'without_you',
			},
			{
				id: 282,
				title: 'animations.list.RunItDown',
				animData: ['majestic_animations_12', 'run_it_down'],
				flag: 1,
				looped: true,
				music: 'run_it_down',
			},
			{ id: 283, title: 'animations.list.Goated', animData: ['majestic_animations_12', 'goated'], flag: 1, looped: true, music: 'goated' },
			{
				id: 284,
				title: 'animations.list.CelebrateMe',
				animData: ['majestic_animations_12', 'celebrate_me'],
				flag: 1,
				looped: true,
				music: 'celebrate_me',
			},
			{
				id: 285,
				title: 'animations.list.PayItOff',
				animData: ['majestic_animations_12', 'pay_it_off'],
				flag: 1,
				looped: true,
				music: 'pay_it_off',
			},
			{
				id: 286,
				title: 'animations.list.FastFlex',
				animData: ['majestic_animations_12', 'fast_flex'],
				flag: 1,
				looped: true,
				music: 'fast_flex',
			},
			{
				id: 287,
				title: 'animations.list.GetOutOfYourMind',
				animData: ['majestic_animations_12', 'get_out_of_your_mind'],
				flag: 1,
				looped: true,
				music: 'get_out_of_your_mind',
			},
			{
				id: 288,
				title: 'animations.list.LitDance',
				animData: ['majestic_animations_dances_1', 'lit_dance'],
				flag: 1,
				looped: true,
				music: 'lit_dance',
			},
			{
				id: 289,
				title: 'animations.list.TakeTheElf',
				animData: ['majestic_animations_props_7', 'take_the_elf'],
				flag: 1,
				looped: true,
				music: 'take_the_elf',
				propList: [
					{
						prop: 'mj_nw_bell',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 290,
				title: 'animations.list.SnowDay',
				animData: ['majestic_animations_props_7', 'snowman_player'],
				flag: 0,
				looped: false,
				music: 'snowman',
				propList: [
					{
						prop: 'mj_snowman_prop',
						propAnimData: ['majestic_animations_props_7', 'snowman_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 291,
				title: 'animations.list.ChoiceKnit',
				animData: ['majestic_animations_props_6', 'choice_knit_player'],
				flag: 0,
				looped: false,
				music: 'choice_knit',
				propList: [
					{
						prop: 'mj_choice_knit',
						propAnimData: ['majestic_animations_props_6', 'choice_knit_prop'],
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 292,
				title: 'animations.list.ShaolinSip',
				animData: ['majestic_animations_props_6', 'shaolin_sip'],
				flag: 1,
				looped: true,
				music: 'shaolin_sip',
				propList: [
					{
						prop: 'mj_shaolin_sip',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 293,
				title: 'animations.list.LilTreat',
				animData: ['majestic_animations_props_6', 'treat_player'],
				flag: 33,
				looped: true,
				music: 'treat',
				propList: [
					{
						prop: 'mj_treat',
						propAnimData: ['majestic_animations_props_6', 'treat_prop'],
						propAttachData: { bone: 35502, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 294,
				title: 'animations.list.Sparkler',
				animData: ['majestic_animations_props_6', 'sparkler'],
				flag: 49,
				looped: true,
				music: 'sparkler',
				propList: [
					{
						prop: 'mj_sparkler',
						propAnimData: ['majestic_animations_props_6', 'sparkler_prop'],
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 295,
				title: 'animations.list.TelekenetikCookies',
				animData: ['majestic_animations_props_6', 'telekinetic_player'],
				flag: 0,
				looped: false,
				music: 'telekinetic',
				propList: [
					{
						prop: 'mj_telekinetic',
						propAnimData: ['majestic_animations_props_6', 'telekinetic_prop'],
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 296,
				title: 'animations.list.TangerineJuggling',
				animData: ['majestic_animations_props_6', 'tangerine_player'],
				flag: 49,
				looped: true,
				music: 'tangerine',
				propList: [
					{
						prop: 'mj_tangerine',
						propAnimData: ['majestic_animations_props_6', 'tangerine_prop'],
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 297,
				title: 'animations.list.HeartAttach',
				animData: ['majestic_animations_props_7', 'heart_attach_player'],
				flag: 0,
				looped: false,
				music: 'heart_attach',
				propList: [
					{
						prop: 'mj_heart_attach',
						propAnimData: ['majestic_animations_props_7', 'heart_attach_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 298,
				title: 'animations.list.OmgILoveIt',
				animData: ['majestic_animations_props_7', 'omg_love_player'],
				flag: 1,
				looped: true,
				music: 'omg_i_love_it',
				propList: [
					{
						prop: 'mj_omg_love',
						propAnimData: ['majestic_animations_props_7', 'omg_love_prop'],
						propAttachData: { bone: 31086, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 299,
				title: 'animations.list.PlanetaryVibe',
				animData: ['majestic_animations_13', 'planetary_vibe'],
				flag: 1,
				looped: true,
				music: 'planetary_vibe',
			},
			{
				id: 300,
				title: 'animations.list.PumpMeUp',
				animData: ['majestic_animations_13', 'pump_me_up'],
				flag: 1,
				looped: true,
				music: 'pump_me_up',
			},
			{
				id: 301,
				title: 'animations.list.Headbanger2',
				animData: ['majestic_animations_13', 'headbanger_2'],
				flag: 1,
				looped: true,
				music: 'headbanger_2',
			},
			{
				id: 302,
				title: 'animations.list.CultureFestival',
				animData: ['majestic_animations_13', 'culture_festival'],
				flag: 1,
				looped: true,
				music: 'culture_festival_dance',
			},
			{
				id: 303,
				title: 'animations.list.BustAMove',
				animData: ['majestic_animations_13', 'bust_a_move_1'],
				flag: 1,
				looped: true,
				music: 'bust_a_move',
				pairAnims: [{ animData: ['majestic_animations_13', 'bust_a_move_2'], flag: 1, looped: true }],
			},
			{
				id: 304,
				title: 'animations.list.BoysALiar',
				animData: ['majestic_animations_13', 'boys_a_liar'],
				flag: 1,
				looped: true,
				music: 'boys_a_liar',
			},
			{
				id: 305,
				title: 'animations.list.Bizcochito',
				animData: ['majestic_animations_13', 'bizcochito'],
				flag: 1,
				looped: true,
				music: 'bizcochito',
			},
			{
				id: 306,
				title: 'animations.list.NightOut',
				animData: ['majestic_animations_13', 'night_out'],
				flag: 1,
				looped: true,
				music: 'night_out',
			},
			{
				id: 307,
				title: 'animations.list.StartItUp',
				animData: ['majestic_animations_13', 'start_it_up'],
				flag: 1,
				looped: true,
				music: 'start_it_up',
			},
			{ id: 308, title: 'animations.list.WindUp', animData: ['majestic_animations_13', 'wind_up'], flag: 1, looped: true, music: 'wind_up' },
			{ id: 309, title: 'animations.list.Starlit', animData: ['majestic_animations_13', 'starlit'], flag: 1, looped: true, music: 'starlit' },
			{
				id: 310,
				title: 'animations.list.DomDomYesYes',
				animData: ['majestic_animations_dances_1', 'dom_yes'],
				flag: 1,
				looped: true,
				music: 'dom_yes',
			},
			{
				id: 311,
				title: 'animations.list.IJustWannaDance',
				animData: ['majestic_animations_dances_1', 'wanna_dance'],
				flag: 1,
				looped: true,
				music: 'wanna_dance',
			},
			{
				id: 312,
				title: 'animations.list.CalledShot',
				animData: ['majestic_animations_props_4', 'called_shot'],
				flag: 1,
				looped: true,
				music: 'called_shot',
				propList: [
					{
						prop: 'p_cs_bbbat_01',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 313,
				title: 'animations.list.WitchWay',
				animData: ['majestic_animations_props_4', 'witch_way_player'],
				flag: 33,
				looped: true,
				music: 'witch_way',
				propList: [
					{
						prop: 'mj_broom',
						propAnimData: ['majestic_animations_props_4', 'witch_way_prop'],
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'pump1_q',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 314,
				title: 'animations.list.Cardistry',
				animData: ['majestic_animations_props_4', 'cardistry_player'],
				flag: 1,
				looped: true,
				music: 'cardistry',
				propList: [
					{
						prop: 'mj_cardistry',
						propAnimData: ['majestic_animations_props_4', 'cardistry_prop'],
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 315,
				title: 'animations.list.TargetTraining',
				animData: ['majestic_animations_props_4', 'target_training'],
				flag: 1,
				looped: true,
				music: 'targer_training',
				propList: [
					{
						prop: 'mj_boxing_glove_left',
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_boxing_glove_right',
						propAttachData: { bone: 60309, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_boxing_target',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_boxing_target',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 316,
				title: 'animations.list.KeepEmCrispy',
				animData: ['majestic_animations_props_4', 'crispy'],
				flag: 1,
				looped: true,
				music: 'keep_em_crispy',
				propList: [
					{
						prop: 'mj_crispy_brush',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_crispy_sneakers',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 317,
				title: 'animations.list.SproutOfTune',
				animData: ['majestic_animations_props_4', 'sprout_of_tune_player'],
				flag: 1,
				looped: true,
				music: 'sprout_of_tune',
				propList: [
					{
						prop: 'mj_sprout',
						propAnimData: ['majestic_animations_props_4', 'sprout_of_tune_prop'],
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 318,
				title: 'animations.list.ClickClickFlash',
				animData: ['majestic_animations_props_4', 'click_click_flash'],
				flag: 1,
				looped: true,
				music: 'click_click_flash',
				propList: [
					{
						prop: 'ch_prop_ch_phone_ing_01a',
						propAttachData: { bone: 6286, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 319,
				title: 'animations.list.PonyUp',
				animData: ['majestic_animations_props_4', 'pony_up'],
				flag: 1,
				looped: true,
				music: 'pony_up',
				propList: [
					{
						prop: 'h4_prop_battle_hobby_horse',
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 320,
				title: 'animations.list.KissKiss',
				animData: ['majestic_animations_props_8', 'kiss_kiss'],
				flag: 0,
				looped: false,
				music: 'kiss_kiss',
				propList: [
					{
						prop: 'mj_kiss_hearts',
						propAnimData: ['majestic_animations_props_8', 'kiss_kiss_prop'],
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 321,
				title: 'animations.list.HeartSign',
				animData: ['majestic_animations_props_8', 'heart_sign'],
				flag: 49,
				looped: true,
				music: 'heart_sign',
				propList: [
					{
						prop: 'mj_heart_sign',
						propAnimData: ['majestic_animations_props_8', 'heart_sign_prop'],
						propAttachData: { bone: 36029, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 322,
				title: 'animations.list.Ambitious',
				animData: ['majestic_animations_14', 'ambitious'],
				flag: 1,
				looped: true,
				music: 'ambitious',
			},
			{ id: 323, title: 'animations.list.BadGuy', animData: ['majestic_animations_14', 'bad_guy'], flag: 1, looped: true, music: 'bad_guy' },
			{
				id: 324,
				title: 'animations.list.BoneyBounce',
				animData: ['majestic_animations_14', 'boney_bounce'],
				flag: 1,
				looped: true,
				music: 'boney_bounce',
			},
			{
				id: 325,
				title: 'animations.list.BoodUpGroove',
				animData: ['majestic_animations_14', 'bood_up_groove'],
				flag: 1,
				looped: true,
				music: 'bood_up_groove',
			},
			{
				id: 326,
				title: 'animations.list.Carefree',
				animData: ['majestic_animations_14', 'carefree'],
				flag: 1,
				looped: true,
				music: 'carefree',
			},
			{ id: 327, title: 'animations.list.Classy', animData: ['majestic_animations_14', 'classy'], flag: 1, looped: true, music: 'classy' },
			{
				id: 328,
				title: 'animations.list.DancinDomino',
				animData: ['majestic_animations_14', 'dancin_domino'],
				flag: 1,
				looped: true,
				music: 'dancin_domino',
			},
			{
				id: 329,
				title: 'animations.list.EvilPlan',
				animData: ['majestic_animations_14', 'evil_plan'],
				flag: 1,
				looped: true,
				music: 'evil_plan',
			},
			{
				id: 330,
				title: 'animations.list.GoWithTheFlow',
				animData: ['majestic_animations_14', 'go_with_the_flow'],
				flag: 1,
				looped: true,
				music: 'go_with_the_flow',
			},
			{ id: 331, title: 'animations.list.Hooray', animData: ['majestic_animations_14', 'hooray'], flag: 1, looped: true, music: 'hooray' },
			{
				id: 332,
				title: 'animations.list.JubiSlide',
				animData: ['majestic_animations_14', 'jubi_slide'],
				flag: 33,
				looped: true,
				music: 'jubi_slide',
				allowSprint: true,
			},
			{
				id: 333,
				title: 'animations.list.MakeSomeWaves',
				animData: ['majestic_animations_14', 'make_some_waves'],
				flag: 1,
				looped: true,
				music: 'make_some_waves',
			},
			{ id: 334, title: 'animations.list.NoCure', animData: ['majestic_animations_14', 'no_cure'], flag: 1, looped: true, music: 'no_cure' },
			{
				id: 335,
				title: 'animations.list.PopularVibe',
				animData: ['majestic_animations_14', 'popular_vibe'],
				flag: 1,
				looped: true,
				music: 'popular_vibe',
			},
			{
				id: 336,
				title: 'animations.list.RainCheck',
				animData: ['majestic_animations_14', 'rain_check'],
				flag: 1,
				looped: true,
				music: 'rain_check',
			},
			{
				id: 337,
				title: 'animations.list.RealSlimShady',
				animData: ['majestic_animations_14', 'real_slim_shady'],
				flag: 1,
				looped: true,
				music: 'real_slim_shady',
			},
			{
				id: 338,
				title: 'animations.list.Rebellious',
				animData: ['majestic_animations_14', 'rebellious'],
				flag: 1,
				looped: true,
				music: 'rebellious',
			},
			{ id: 339, title: 'animations.list.ShowYa', animData: ['majestic_animations_14', 'show_ya'], flag: 1, looped: true, music: 'show_ya' },
			{
				id: 340,
				title: 'animations.list.SocialClimber',
				animData: ['majestic_animations_14', 'social_climber'],
				flag: 1,
				looped: true,
				music: 'social_climber',
			},
			{
				id: 341,
				title: 'animations.list.SwagShuffle',
				animData: ['majestic_animations_14', 'swag_shuffle'],
				flag: 1,
				looped: true,
				music: 'swag_shuffle',
			},
			{
				id: 342,
				title: 'animations.list.TheSquabble',
				animData: ['majestic_animations_14', 'the_squabble'],
				flag: 1,
				looped: true,
				music: 'the_squabble',
			},
			{
				id: 343,
				title: 'animations.list.ToTheBeat',
				animData: ['majestic_animations_14', 'to_the_beat'],
				flag: 1,
				looped: true,
				music: 'to_the_beat',
			},
			{
				id: 344,
				title: 'animations.list.YouAWinner',
				animData: ['majestic_animations_14', 'you_a_winner'],
				flag: 1,
				looped: true,
				music: 'you_a_winner',
			},
			{
				id: 345,
				title: 'animations.list.YouShouldSeeMeInACrown',
				animData: ['majestic_animations_14', 'you_should_see_me_in_a_crown'],
				flag: 1,
				looped: true,
				music: 'you_should_see_me_in_a_crown',
			},
			{
				id: 346,
				title: 'animations.list.JustWannaRock',
				animData: ['majestic_animations_dances_1', 'just_wanna_rock'],
				flag: 1,
				looped: true,
				music: 'wanna_rock',
			},
			{
				id: 347,
				title: 'animations.list.9mmGoBang',
				animData: ['majestic_animations_dances_1', '9mm_go_bang'],
				flag: 1,
				looped: true,
				music: '9mm_go_bang',
			},
			{
				id: 348,
				title: 'animations.list.Expressionism',
				animData: ['majestic_animations_props_10', 'expressionism_player'],
				flag: 1,
				looped: true,
				music: 'expressionism',
				propList: [
					{
						prop: 'mj_expressionism',
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_expressionism',
						propAttachData: { bone: 60309, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 349,
				title: 'animations.list.SqueezieDoesIt',
				animData: ['majestic_animations_props_10', 'squeezie_does_it_player'],
				flag: 33,
				looped: true,
				music: 'squeezie',
				propList: [
					{
						prop: 'mj_squeezie_does_it',
						propAnimData: ['majestic_animations_props_10', 'squeezie_does_it_prop'],
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 350,
				title: 'animations.list.LilSupercar',
				animData: ['majestic_animations_props_9', 'supercar_player'],
				flag: 33,
				looped: true,
				music: 'lil_supercar',
				propList: [
					{
						prop: 'mj_supercar',
						propAnimData: ['majestic_animations_props_9', 'supercar_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 351,
				title: 'animations.list.Squirrelly',
				animData: ['majestic_animations_props_10', 'squirrelly_player'],
				flag: 0,
				looped: false,
				music: 'squirelly',
				propList: [
					{
						prop: 'mj_squirrelly',
						propAnimData: ['majestic_animations_props_10', 'squirrelly_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 352,
				title: 'animations.list.ZoidbergScuttle',
				animData: ['majestic_animations_props_10', 'zoidberg_scuttle_player'],
				flag: 33,
				looped: true,
				music: 'zoidberg_scuttle',
				propList: [
					{
						prop: 'mj_zoidberg_scuttle_right',
						propAnimData: ['majestic_animations_props_10', 'zoidberg_scuttle_right'],
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_zoidberg_scuttle_left',
						propAnimData: ['majestic_animations_props_10', 'zoidberg_scuttle_left'],
						propAttachData: { bone: 60309, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 353,
				title: 'animations.list.GGWP',
				animData: ['majestic_animations_props_11', 'ggwp_player'],
				flag: 0,
				looped: false,
				music: 'ggwp',
				propList: [
					{
						prop: 'mj_ggwp',
						propAnimData: ['majestic_animations_props_11', 'ggwp_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 354,
				title: 'animations.list.Baller',
				animData: ['majestic_animations_props_10', 'baller'],
				flag: 1,
				looped: true,
				music: 'baller',
				propList: [
					{
						prop: 'mj_baller',
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 355,
				title: 'animations.list.LookingToTheSky',
				animData: ['majestic_animations_custom_4', 'alpha_warrior_loop'],
				flag: 1,
				looped: true,
			},
			{
				id: 356,
				title: 'animations.list.LiveFromHaddonfield',
				animData: ['majestic_animations_props_12', 'live_from_haddonfield_player'],
				flag: 49,
				looped: true,
				music: 'live_from_haddonfield',
				propList: [
					{
						prop: 'mj_haddonfield_piano',
						propAnimData: ['majestic_animations_props_12', 'live_from_haddonfield_prop'],
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_haddonfield_knife',
						propAttachData: { bone: 60309, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 357,
				title: 'animations.list.ItIsATrick',
				animData: ['majestic_animations_props_12', 'troops_player'],
				flag: 0,
				looped: false,
				music: 'troops_trick',
				propList: [
					{
						prop: 'mj_troops',
						propAnimData: ['majestic_animations_props_12', 'troops_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 358,
				title: 'animations.list.MiracleTrickshot',
				animData: ['majestic_animations_props_11', 'miracle_trickshot'],
				flag: 0,
				looped: false,
				music: 'miracle_trickshot',
				propList: [
					{
						prop: 'w_am_baseball',
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'w_me_bat',
						propAttachData: { bone: 60309, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 359,
				title: 'animations.list.ReaperShowtime',
				animData: ['majestic_animations_props_11', 'reaper_showtime'],
				flag: 1,
				looped: true,
				music: 'reaper_showtime',
				propList: [
					{
						prop: 'mj_reaper_showtime',
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 360,
				title: 'animations.list.Pickin',
				animData: ['majestic_animations_props_12', 'pickin'],
				flag: 1,
				looped: true,
				music: 'pickin',
				propList: [
					{
						prop: 'mj_banjo',
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_chair_rot',
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 361,
				title: 'animations.list.PilaTricycle',
				animData: ['majestic_animations_props_12', 'billy_tricycle_player'],
				flag: 33,
				looped: true,
				music: 'billy_tricycle',
				propList: [
					{
						prop: 'mj_billy_tricycle',
						propAnimData: ['majestic_animations_props_12', 'billy_tricycle_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 362,
				title: 'animations.list.PrisonerMugshotM',
				animData: ['majestic_animations_props_12', 'prisoner_mugshot_player'],
				flag: 0,
				looped: false,
				music: 'prisoner_mugshot',
				propList: [
					{
						prop: 'mj_prisoner_mugshot',
						propAnimData: ['majestic_animations_props_12', 'prisoner_mugshot_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_prisoner_table_male',
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 363,
				title: 'animations.list.PrisonerMugshotF',
				animData: ['majestic_animations_props_12', 'prisoner_mugshot_player'],
				flag: 0,
				looped: false,
				music: 'prisoner_mugshot',
				propList: [
					{
						prop: 'mj_prisoner_mugshot',
						propAnimData: ['majestic_animations_props_12', 'prisoner_mugshot_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_prisoner_table_female',
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 364,
				title: 'animations.list.SamuraiStrike',
				animData: ['majestic_animations_props_12', 'samurai_strike'],
				flag: 0,
				looped: false,
				music: 'samurai_strike',
				propList: [
					{
						prop: 'mj_samurai_katana',
						propAnimData: ['majestic_animations_props_12', 'samurai_strike_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 365,
				title: 'animations.list.KiotoFan',
				animData: ['majestic_animations_props_12', 'kioto_fan'],
				flag: 33,
				looped: true,
				music: 'kioto_fan',
				propList: [
					{
						prop: 'mj_kioto_fan',
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 366,
				title: 'animations.list.RonaldGreeting',
				animData: ['majestic_animations_17', 'ronald_greeting'],
				flag: 1,
				looped: true,
				music: 'ronald_greeting',
			},
			{
				id: 367,
				title: 'animations.list.PickMeUp',
				animData: ['majestic_animations_props_11', 'pick_me_up_player'],
				flag: 33,
				looped: true,
				music: 'pick_me_up',
				propList: [
					{
						prop: 'mj_pick_bear',
						propAnimData: ['majestic_animations_props_11', 'pick_me_up_prop'],
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 368,
				title: 'animations.list.IceMoves',
				animData: ['majestic_animations_props_10', 'ice_moves_player'],
				flag: 0,
				looped: false,
				music: 'ice_moves',
				propList: [
					{
						prop: 'mj_ice_moves',
						propAnimData: ['majestic_animations_props_10', 'ice_moves_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 369,
				title: 'animations.list.Snowflakey',
				animData: ['majestic_animations_props_8', 'snowflakey_player'],
				flag: 0,
				looped: false,
				music: 'snowflakey',
				propList: [
					{
						prop: 'mj_snowflakey',
						propAnimData: ['majestic_animations_props_8', 'snowflakey_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 370,
				title: 'animations.list.TeaTime',
				animData: ['majestic_animations_props_8', 'tea_time'],
				flag: 33,
				looped: true,
				music: 'tea_time',
				propList: [
					{
						prop: 'mj_tea_time_cup',
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_tea_time_plate',
						propAttachData: { bone: 60309, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 371,
				title: 'animations.list.ChuggaChugga',
				animData: ['majestic_animations_props_13', 'chugga_train_player'],
				flag: 33,
				looped: true,
				music: 'chugga_chugga_train',
				propList: [
					{
						prop: 'mj_chugga_train',
						propAnimData: ['majestic_animations_props_13', 'chugga_train_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
				pairAnims: [
					{
						animData: ['majestic_animations_props_13', 'chugga_passenger_player'],
						flag: 33,
						looped: true,
						propList: [
							{
								prop: 'mj_chugga_passenger',
								propAnimData: ['majestic_animations_props_13', 'chugga_passenger_prop'],
								propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_13', 'chugga_passenger_player'],
						flag: 33,
						looped: true,
						propList: [
							{
								prop: 'mj_chugga_passenger',
								propAnimData: ['majestic_animations_props_13', 'chugga_passenger_prop'],
								propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_13', 'chugga_passenger_player'],
						flag: 33,
						looped: true,
						propList: [
							{
								prop: 'mj_chugga_passenger',
								propAnimData: ['majestic_animations_props_13', 'chugga_passenger_prop'],
								propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_13', 'chugga_passenger_player'],
						flag: 33,
						looped: true,
						propList: [
							{
								prop: 'mj_chugga_passenger',
								propAnimData: ['majestic_animations_props_13', 'chugga_passenger_prop'],
								propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_13', 'chugga_passenger_player'],
						flag: 33,
						looped: true,
						propList: [
							{
								prop: 'mj_chugga_passenger',
								propAnimData: ['majestic_animations_props_13', 'chugga_passenger_prop'],
								propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_13', 'chugga_passenger_player'],
						flag: 33,
						looped: true,
						propList: [
							{
								prop: 'mj_chugga_passenger',
								propAnimData: ['majestic_animations_props_13', 'chugga_passenger_prop'],
								propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_13', 'chugga_passenger_player'],
						flag: 33,
						looped: true,
						propList: [
							{
								prop: 'mj_chugga_passenger',
								propAnimData: ['majestic_animations_props_13', 'chugga_passenger_prop'],
								propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_13', 'chugga_passenger_player'],
						flag: 33,
						looped: true,
						propList: [
							{
								prop: 'mj_chugga_passenger',
								propAnimData: ['majestic_animations_props_13', 'chugga_passenger_prop'],
								propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
					{
						animData: ['majestic_animations_props_13', 'chugga_passenger_player'],
						flag: 33,
						looped: true,
						propList: [
							{
								prop: 'mj_chugga_passenger',
								propAnimData: ['majestic_animations_props_13', 'chugga_passenger_prop'],
								propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
				],
			},
			{
				id: 372,
				title: 'animations.list.MoneyBlastin',
				animData: ['majestic_animations_props_13', 'money_blastin_player'],
				flag: 0,
				looped: false,
				music: 'money_blastin',
				propList: [
					{
						prop: 'mj_gun_money',
						propAnimData: ['majestic_animations_props_13', 'money_blastin_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 373,
				title: 'animations.list.Airboard',
				animData: ['majestic_animations_props_13', 'snow_airboard'],
				flag: 33,
				looped: true,
				music: 'snow_airboard',
				allowSprint: true,
				propList: [
					{
						prop: 'mj_snow_airboard',
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 374,
				title: 'animations.list.ToysFlip',
				animData: ['majestic_animations_props_13', 'toys_flip_player'],
				flag: 49,
				looped: true,
				music: 'toys_flip',
				propList: [
					{
						prop: 'mj_flip_toys',
						propAnimData: ['majestic_animations_props_13', 'toys_flip_prop'],
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 375,
				title: 'animations.list.DeerCadabra',
				animData: ['majestic_animations_props_13', 'deer_cadabra_player'],
				flag: 0,
				looped: false,
				music: 'deer_cadabra',
				propList: [
					{
						prop: 'mj_deer_stick',
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_deer_cadabra',
						propAnimData: ['majestic_animations_props_13', 'deer_cadabra_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
					{
						prop: 'mj_deer_particles',
						propAnimData: ['majestic_animations_props_13', 'deer_cadabra_particles'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 376,
				title: 'animations.list.CloudSwing',
				animData: ['majestic_animations_props_13', 'cloud_swing_player'],
				flag: 1,
				looped: true,
				music: 'cloud_swing',
				propList: [
					{
						prop: 'mj_cloud_swing',
						propAnimData: ['mj_cloud_swing_anim', 'cloud_swing_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 377,
				title: 'animations.list.Boing',
				animData: ['majestic_animations_props_12', 'boing_player'],
				flag: 33,
				looped: true,
				music: 'boing',
				propList: [
					{
						prop: 'mj_boing',
						propAnimData: ['majestic_animations_props_12', 'boing_prop'],
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 378,
				title: 'animations.list.ByeByeBye',
				animData: ['majestic_animations_17', 'bye_bye_bye'],
				flag: 1,
				looped: true,
				music: 'bye_bye_bye',
				pairAnims: [{ animData: ['majestic_animations_17', 'bye_bye_bye'], flag: 1, looped: true }],
			},
			{
				id: 379,
				title: 'animations.list.Caffeinated',
				animData: ['majestic_animations_17', 'caffeinated'],
				flag: 1,
				looped: true,
				music: 'caffeinated',
			},
			{
				id: 380,
				title: 'animations.list.Desirable',
				animData: ['majestic_animations_17', 'desirable'],
				flag: 1,
				looped: true,
				music: 'desirable',
			},
			{
				id: 381,
				title: 'animations.list.WhatYouWant',
				animData: ['majestic_animations_17', 'what_you_want'],
				flag: 1,
				looped: true,
				music: 'what_you_want',
			},
			{
				id: 382,
				title: 'animations.list.Entranced',
				animData: ['majestic_animations_15', 'entranced'],
				flag: 1,
				looped: true,
				music: 'entranced',
			},
			{
				id: 383,
				title: 'animations.list.CompanyJig',
				animData: ['majestic_animations_15', 'company_jig'],
				flag: 1,
				looped: true,
				music: 'company_jig',
			},
			{
				id: 384,
				title: 'animations.list.HeartbreakShuffle',
				animData: ['majestic_animations_15', 'heartbreak_shuffle_1'],
				flag: 1,
				looped: true,
				music: 'heartbreak_shuffle',
				pairAnims: [{ animData: ['majestic_animations_15', 'heartbreak_shuffle_2'], flag: 1, looped: true }],
			},
			{
				id: 385,
				title: 'animations.list.PointAndStrut',
				animData: ['majestic_animations_15', 'point_and_strut'],
				flag: 1,
				looped: true,
				music: 'point_and_strut',
			},
			{
				id: 386,
				title: 'animations.list.Breakneck',
				animData: ['majestic_animations_16', 'breakneck'],
				flag: 1,
				looped: true,
				music: 'breakneck',
			},
			{ id: 387, title: 'animations.list.NoTears', animData: ['majestic_animations_16', 'no_tears'], flag: 1, looped: true, music: 'no_tears' },
			{ id: 388, title: 'animations.list.LofiHeadbang', animData: ['majestic_animations_15', 'lofi_headbang'], flag: 1,   looped: true,  music: 'lofi_headbang'},
			{ id: 389, title: 'animations.list.BriteMoves', animData: ['majestic_animations_15', 'brite_moves'], flag: 1,   looped: true,  music: 'brite_moves'},
			{ id: 390, title: 'animations.list.WaddleAway', animData: ['majestic_animations_17', 'waddle_away'], flag: 33,   looped: true,  music: 'waddle_away'},
			{
				id: 391,
				title: 'animations.list.GiftDrop',
				animData: ['majestic_animations_props_14', 'gift_drop_player'],
				flag: 1,
				looped: true,
				music: 'gift_drop_christmas',
				propList: [
					{
						prop: 'mj_gift_drop',
						propAnimData: ['majestic_animations_props_14', 'gift_drop_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 392,
				title: 'animations.list.SantaLaughing',
				animData: ['majestic_animations_props_14', 'santa_xoxo_player'],
				flag: 1,
				looped: true,
				music: 'santa_hohoho',
				propList: [
					{
						prop: 'mj_santa_xoxo',
						propAnimData: ['majestic_animations_props_14', 'santa_xoxo_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 393,
				title: 'animations.list.XOXO',
				animData: ['majestic_animations_props_14', 'xoxo_female_player'],
				flag: 0,
				looped: false,
				music: 'xoxo_female_kiss',
				propList: [
					{
						prop: 'mj_xoxo_female',
						propAnimData: ['majestic_animations_props_14', 'xoxo_female_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 394,
				title: 'animations.list.DonkeyFlight',
				animData: ['majestic_animations_props_14', 'shrek_donkey_player'],
				flag: 33,
				looped: true,
				music: 'shrek_donkey',
				allowSprint: true,
				propList: [
					{
						prop: 'mj_shrek_donkey',
						propAnimData: ['majestic_animations_props_14', 'shrek_donkey_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{ id: 395, title: 'animations.list.AlaskaPuffer', animData: ['majestic_animations_dances_2', 'alaska_puffer'], flag: 1, looped: true, music: 'alaska_puffer'},
			{
				id: 396,
				title: 'animations.list.NyaArigato',
				animData: ['majestic_animations_props_15', 'nya_arigato_player'],
				flag: 1,
				looped: true,
				music: 'nya_arigato',
				propList: [
					{
						prop: 'mj_nya_arigato',
						propAnimData: ['majestic_animations_props_15', 'nya_arigato_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
				pairAnims: [
					{
						animData: ['majestic_animations_props_15', 'nya_arigato_player'],
						flag: 1,
						looped: true,
						propList: [
							{
								prop: 'mj_nya_arigato',
								propAnimData: ['majestic_animations_props_15', 'nya_arigato_prop'],
								propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
							},
						],
					},
				],
			},
			{ id: 397, title: 'animations.list.Shine', animData: ['majestic_animations_dances_3', 'shine'], flag: 1, looped: true, music: 'shine_majestic'},
			{ id: 398, title: 'animations.list.ExumShuffle', animData: ['majestic_animations_dances_3', 'exum_shuffle'], flag: 1, looped: true, music: 'exum_shuffle'},
			{ id: 399, title: 'animations.list.LemonMelonCoockie', animData: ['majestic_animations_dances_3', 'lemon_melon_coockie'], flag: 1, looped: true, music: 'lemon_coockie'},
			{ id: 400, title: 'animations.list.SweetBumbleBee', animData: ['majestic_animations_dances_3', 'sweet_bumble_bee'], flag: 1, looped: true, music: 'sweet_bumble_bee'},
			{ id: 401, title: 'animations.list.SurfinBird', animData: ['majestic_animations_15', 'surfin_bird'], flag: 1, looped: true, music: 'surfin_bird'},			
			{ id: 402, title: 'animations.list.DeepExplorer', animData: ['majestic_animations_17', 'deep_explorer'], flag: 1, looped: true, music: 'deep_explorer'},
			{ id: 403, title: 'animations.list.LucidDreams', animData: ['majestic_animations_17', 'lucid_dreams'], flag: 1, looped: true, music: 'lucid_dreams'},
			{ id: 404, title: 'animations.list.Smitten', animData: ['majestic_animations_17', 'smitted'], flag: 1, looped: true, music: 'smitten'},
			{
				id: 405,
				title: 'animations.list.Commited',
				animData: ['majestic_animations_17', 'commited_player_one'],
				flag: 1,
				looped: true,
				music: 'committed',
				pairAnims: [{ animData: ['majestic_animations_17', 'commited_player_two'], flag: 1, looped: true }],
			},
			{
				id: 406,
				title: 'animations.list.RibbonDancer',
				animData: ['majestic_animations_props_8', 'ribbon_dance_player'],
				flag: 1,
				looped: true,
				music: 'ribbon_dance',
				propList: [
					{
						prop: 'mj_ribbon_dance',
						propAnimData: ['majestic_animations_props_8', 'ribbon_dance_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 407,
				title: 'animations.list.AmazingCube',
				animData: ['majestic_animations_props_15', 'amazing_cube_player'],
				flag: 49,
				looped: true,
				music: 'amazing_cube',
				propList: [
					{
						prop: 'mj_amazing_cube',
						propAnimData: ['majestic_animations_props_15', 'amazing_cube_prop'],
						propAttachData: { bone: 28422, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 408,
				title: 'animations.list.ASMRKeys',
				animData: ['majestic_animations_props_15', 'asmr_keys'],
				flag: 1,
				looped: true,
				music: 'asmr_keys',
				propList: [
					{
						prop: 'mj_keyboard_rgb',
						propAttachData: { bone: 60309, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 409,
				title: 'animations.list.CupidArrow',
				animData: ['majestic_animations_props_15', 'cupid_arrow_player'],
				flag: 1,
				looped: true,
				music: 'cupid_arrow',
				propList: [
					{
						prop: 'mj_cupid_arrow',
						propAnimData: ['majestic_animations_props_15', 'cupid_arrow_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 410,
				title: 'animations.list.MikuLive',
				animData: ['majestic_animations_props_15', 'miku_live_player'],
				flag: 1,
				looped: true,
				music: 'miku_live',
				propList: [
					{
						prop: 'mj_miku_live',
						propAnimData: ['majestic_animations_props_15', 'miku_live_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
			{
				id: 411,
				title: 'animations.list.Accolades',
				animData: ['majestic_animations_props_15', 'accolades_player'],
				flag: 1,
				looped: true,
				music: 'accolades',
				propList: [
					{
						prop: 'mj_accolades',
						propAnimData: ['majestic_animations_props_15', 'accolades_prop'],
						propAttachData: { bone: 56604, offsetPos: new mp.Vector3(0.0, 0.0, 0.0), offsetRot: new mp.Vector3(0.0, 0.0, 0.0) },
					},
				],
			},
		],
	},

	// {
	//     title: 'Избранное',
	//     path: 'favourite',
	//     items: []
	// },

	// {
	//     title: 'Круговое меню',
	//     path: 'circle-menu',
	//     items: []
	// },
];
