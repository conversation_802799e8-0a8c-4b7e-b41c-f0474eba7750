import { awardEnum } from '@sharedConfig/tasks';

export const SEASON_EVENTS_TYPES = Object.freeze({
	WINTER_SEASON: 'winter_season',
	SPRING_SEASON: 'spring_season',
	SUMMER_SEASON: 'summer_season',
	AUTUMN_SEASON: 'autumn_season',
	
	WINTER: 'winter',
	VALENTINE: 'valentine'
});

export const seasonEventsConfig = Object.freeze({
	[SEASON_EVENTS_TYPES.WINTER_SEASON]: {
		name: 'winter_season',
		startYearOffset: -1,
		start: '12-01 00:00:00',
		end: '03-01 00:00:00',
		activities: [
			{
				name: 'SeasonalActivity',
				controller: 'SeasonalActivity',
				config: {},
			},
		]
	},

	[SEASON_EVENTS_TYPES.SPRING_SEASON]: {
		name: 'spring_season',
		start: '03-01 00:00:00',
		end: '06-01 00:00:00',
		activities: [
			{
				name: 'CasesStatesActivity',
				controller: 'CasesStatesActivity',
				config: {
					cases: [
						{
							id: 14,
							active: true,
							allowedToBuy: true,
							seasonal: true,
						}
					]
				},
			},
			{
				name: 'SeasonalActivity',
				controller: 'SeasonalActivity',
				config: {},
			},
		]
	},

	[SEASON_EVENTS_TYPES.WINTER]: {
		name: 'winter',
		start: '2025-01-28 6:00:00',
		end: '2025-02-10 6:00:00',
		startForTest: '2025-01-03 00:00:00',
		withYear: true,
		activities: [
			{
				id: 12,
				name: 'PromoScreen',
				controller: 'PromoScreen',
				config: {
					screens: [
						{
							modal: 'eventModalWinter2024',
							cooldown: 999 * 24 * 60 * 60 * 1000,
							category: 'battlePass',
							subCategory: 'winterFairyTale',
							check: 'isSeasonPassActive',
						},
					],
				},
			},
			{
				id: 8,
				name: 'PanelMenuCategories',
				controller: 'PanelMenu',
				config: {
					categories: {
						battlePass: {
							list: ['winterFairyTale', 'lostLetter'],
							priority: 101,
						},
					},
				},
			},
			{
				name: 'Rifts',
				controller: 'Rifts',
				config: {
					insideTimecycle: 'NeutralColorCodeLight',
					timecycleTransition: 1.4,
					innerRadius: 5,
					snowDriftRadius: 2,
					timeToDestroyAfterCollect: 0.09, // В минутах
					timeToDestroy: 10, // В минутах
					spawnEveryMinutes: 60, // В минутах

					expReward: 150,
					maxRifts: 20,

					riftProps: [
						{ prop: 'mj_nw23_mini_cloud', positionOffset: { x: 0, y: 0, z: 7 } },
						{ prop: 'mj_nw23_mini_particles', positionOffset: { x: 0, y: 0, z: 7 } },
						{ prop: 'mj_nw23_mini_particles_2', positionOffset: { x: 0, y: 0, z: 7 } },
						{ prop: 'mj_prop_snowfall_01', positionOffset: { x: 0, y: 0, z: -1 }, instantDestroy: true },
					],
					centers: [
						{
							x: 1232.545,
							y: -2932.2593,
							z: 12.143,
						},
						{
							x: 1289.8945,
							y: -3174.1978,
							z: 5.8923,
						},
						{
							x: 828.1714,
							y: -3194.5979,
							z: 14.4857,
						},
						{
							x: 797.4725,
							y: -2979.9297,
							z: 17.7715,
						},
						{
							x: 676.4967,
							y: -2736.1582,
							z: 6.0103,
						},
						{
							x: 616.1802,
							y: -2849.1956,
							z: 2.5897,
						},
						{
							x: 557.8813,
							y: -2631.6924,
							z: 6.1619,
						},
						{
							x: 347.1297,
							y: -2540.0703,
							z: 5.7238,
						},
						{
							x: 362.822,
							y: -2758.3779,
							z: 14.1992,
						},
						{
							x: 267.3099,
							y: -2993.5913,
							z: 5.7238,
						},
						{
							x: 155.4594,
							y: -2798.6902,
							z: 5.9934,
						},
						{
							x: 194.189,
							y: -2515.7935,
							z: 5.9766,
						},
						{
							x: 128.2022,
							y: -2418.3823,
							z: 13.8623,
						},
						{
							x: -64.8132,
							y: -2413.7407,
							z: 8.8074,
						},
						{
							x: -38.8615,
							y: -2532.4614,
							z: 5.9934,
						},
						{
							x: -263.1956,
							y: -2388.6726,
							z: 5.9934,
						},
						{
							x: -330.422,
							y: -2565.4812,
							z: 6.0103,
						},
						{
							x: -438.9363,
							y: -2643.9692,
							z: 8.7568,
						},
						{
							x: -544.655,
							y: -2803.9253,
							z: 5.9934,
						},
						{
							x: 680.5846,
							y: -2451.8374,
							z: 26.4323,
						},
						{
							x: 716.0308,
							y: -2254.8132,
							z: 29.2292,
						},
						{
							x: 776.0835,
							y: -2540.2153,
							z: 20.0461,
						},
						{
							x: 1061.723,
							y: -2446.7473,
							z: 28.993,
						},
						{
							x: 970.9846,
							y: -2263.9385,
							z: 39.811,
						},
						{
							x: 1016.8615,
							y: -2032.6549,
							z: 30.83,
						},
						{
							x: 1104.4879,
							y: -2129.8945,
							z: 31.352,
						},
						{
							x: 1235.2483,
							y: -2557.6484,
							z: 39.103,
						},
						{
							x: 1540.4967,
							y: -2502.2769,
							z: 59.575,
						},
						{
							x: 1483.556,
							y: -1971.7318,
							z: 70.865,
						},
						{
							x: 1328.1362,
							y: -2067.7847,
							z: 51.875,
						},
						{
							x: 1372.5626,
							y: -1863.4022,
							z: 57.098,
						},
						{
							x: 1009.556,
							y: -1863.4813,
							z: 30.88,
						},
						{
							x: 893.5912,
							y: -1833.2836,
							z: 30.611,
						},
						{
							x: 758.0571,
							y: -1909.1208,
							z: 35.1267,
						},
						{
							x: 789.4286,
							y: -1609.5692,
							z: 31.1838,
						},
						{
							x: 874.2593,
							y: -1550.6769,
							z: 30.4425,
						},
						{
							x: 969.7846,
							y: -1458.9363,
							z: 31.3356,
						},
						{
							x: 765.7451,
							y: -1488.3429,
							z: 20.1304,
						},
						{
							x: 1063.2527,
							y: -1907.2351,
							z: 31.015,
						},
						{
							x: 1399.5297,
							y: -1612.5099,
							z: 57.318,
						},
						{
							x: 1790.8879,
							y: -1277.6967,
							z: 96.005,
						},
						{
							x: 1948.2329,
							y: -975.4418,
							z: 78.4982,
						},
						{
							x: 1217.2748,
							y: -1228.734,
							z: 35.43,
						},
						{
							x: 1129.4374,
							y: -1255.1208,
							z: 20.787,
						},
						{
							x: 1107.5472,
							y: -1405.2131,
							z: 29.381,
						},
						{
							x: 1201.978,
							y: -1073.5648,
							z: 40.805,
						},
						{
							x: 1089.6395,
							y: -890.8483,
							z: 47.4271,
						},
						{
							x: 1038.6066,
							y: -784.9187,
							z: 58.0088,
						},
						{
							x: 1084.8528,
							y: -694.2593,
							z: 57.9919,
						},
						{
							x: 1348.2858,
							y: -747.0198,
							z: 67.3099,
						},
						{
							x: 1321.9648,
							y: -448.589,
							z: 69.0791,
						},
						{
							x: 1229.1296,
							y: -436.0747,
							z: 67.6974,
						},
						{
							x: 1159.3319,
							y: -386.9011,
							z: 72.8535,
						},
						{
							x: 1135.0417,
							y: -296.8352,
							z: 68.7927,
						},
						{
							x: 992.611,
							y: -486.989,
							z: 61.5809,
						},
						{
							x: 1029.4154,
							y: -262.9055,
							z: 58.1436,
						},
						{
							x: 1054.866,
							y: -175.7538,
							z: 69.8374,
						},
						{
							x: 899.2352,
							y: -288.1978,
							z: 65.6417,
						},
						{
							x: 743.7231,
							y: -233.8154,
							z: 66.0967,
						},
						{
							x: 773.1693,
							y: -116.6242,
							z: 75.8359,
						},
						{
							x: 1090.2726,
							y: -105.5473,
							z: 81.9861,
						},
						{
							x: 1173.5341,
							y: 277.5692,
							z: 81.8513,
						},
						{
							x: 707.8549,
							y: 143.156,
							z: 80.7393,
						},
						{
							x: 736.4835,
							y: 213.8506,
							z: 87.0242,
						},
						{
							x: 375.2176,
							y: 235.4505,
							z: 112.8887,
						},
						{
							x: 344.3736,
							y: 334.7341,
							z: 104.9524,
						},
						{
							x: 182.9538,
							y: 386.3868,
							z: 108.5582,
						},
						{
							x: 90.5011,
							y: 303.8637,
							z: 109.9905,
						},
						{
							x: 288.989,
							y: 203.3934,
							z: 104.3627,
						},
						{
							x: 466.9714,
							y: -63.2703,
							z: 88.844,
						},
						{
							x: 313.6088,
							y: 61.1736,
							z: 99.8806,
						},
						{
							x: 67.0418,
							y: 175.0154,
							z: 112.9729,
						},
						{
							x: 36.8835,
							y: 129.3626,
							z: 104.1099,
						},
						{
							x: -1.7143,
							y: 284.9407,
							z: 112.9055,
						},
						{
							x: -210.0132,
							y: 307.7275,
							z: 96.9319,
						},
						{
							x: 287.0242,
							y: -200.2813,
							z: 61.5472,
						},
						{
							x: -32.9407,
							y: -198.4615,
							z: 52.3473,
						},
						{
							x: -105.4813,
							y: -428.2549,
							z: 36.104,
						},
						{
							x: -525.7978,
							y: -35.4593,
							z: 44.5121,
						},
						{
							x: -329.6044,
							y: -228.8703,
							z: 42.4227,
						},
						{
							x: -474.1451,
							y: 30.0264,
							z: 61.1428,
						},
						{
							x: -581.578,
							y: 331.0022,
							z: 84.8169,
						},
						{
							x: -775.8593,
							y: 368.1626,
							z: 87.8499,
						},
						{
							x: -1362.9626,
							y: -17.9868,
							z: 53.3751,
						},
						{
							x: -1233.7847,
							y: -43.9912,
							z: 45.8938,
						},
						{
							x: -1327.3319,
							y: -99.8374,
							z: 49.5333,
						},
						{
							x: -1388.9407,
							y: -172.9978,
							z: 47.376,
						},
						{
							x: -1551.5341,
							y: -301.5297,
							z: 48,
						},
						{
							x: -1448.7957,
							y: -364.0088,
							z: 43.534,
						},
						{
							x: -1550.7561,
							y: -417.7978,
							z: 41.984,
						},
						{
							x: -1550.0571,
							y: -462.7253,
							z: 47.359,
						},
						{
							x: -1380.3165,
							y: -456.7912,
							z: 34.469,
						},
						{
							x: -1574.6241,
							y: -598.8264,
							z: 33.964,
						},
						{
							x: -1464.3824,
							y: -583.3318,
							z: 30.964,
						},
						{
							x: -1413.2571,
							y: -710.9011,
							z: 34.469,
						},
						{
							x: -1668.7517,
							y: -598.3912,
							z: 33.863,
						},
						{
							x: -1778.255,
							y: -504.3297,
							z: 38.783,
						},
						{
							x: -1784.6373,
							y: -358.0352,
							z: 45.034,
						},
						{
							x: -1911.7186,
							y: -295.5165,
							z: 49.011,
						},
						{
							x: -1951.701,
							y: -339.6264,
							z: 46.011,
						},
						{
							x: -2116.5759,
							y: -428.3736,
							z: 8.0322,
						},
						{
							x: -1971.3759,
							y: -562.3912,
							z: 11.452,
						},
						{
							x: -1733.2087,
							y: -728.0571,
							z: 10.408,
						},
						{
							x: -1639.5824,
							y: -814.9319,
							z: 10.155,
						},
						{
							x: -1493.6703,
							y: -1093.6615,
							z: 2.353,
						},
						{
							x: -1398.8967,
							y: -1175.3407,
							z: 3.398,
						},
						{
							x: -1428.5275,
							y: -1423.8989,
							z: 3.331,
						},
						{
							x: -1383.5868,
							y: -1490.0176,
							z: 4.375,
						},
						{
							x: -1304.6769,
							y: -1598.3209,
							z: 4.224,
						},
						{
							x: -1248.4615,
							y: -1424.0176,
							z: 4.308,
						},
						{
							x: -1211.0505,
							y: -1595.9868,
							z: 4.173,
						},
						{
							x: -1078.0615,
							y: -1708.2594,
							z: 4.46,
						},
						{
							x: -958.0483,
							y: -1581.3363,
							z: 4.9993,
						},
						{
							x: -1025.0505,
							y: -1522.2329,
							z: 5.572,
						},
						{
							x: -975.2703,
							y: -1469.9736,
							z: 4.9993,
						},
						{
							x: -1066.0483,
							y: -1440.7913,
							z: 5.403,
						},
						{
							x: -1016.967,
							y: -1324.5758,
							z: 5.825,
						},
						{
							x: -812.3077,
							y: -1402.0879,
							z: 4.9993,
						},
						{
							x: -685.3978,
							y: -1329.9956,
							z: 5.0835,
						},
						{
							x: -770.6638,
							y: -1515.2043,
							z: 5.3195,
						},
						{
							x: -568.6022,
							y: -1142.4396,
							z: 22.169,
						},
						{
							x: -588.8703,
							y: -1287.7186,
							z: 13.71,
						},
						{
							x: -621.244,
							y: -1419.9956,
							z: 12.312,
						},
						{
							x: -630.5406,
							y: -1583.6967,
							z: 26.567,
						},
						{
							x: -863.1165,
							y: -1688.3209,
							z: 4.359,
						},
						{
							x: -1065.4286,
							y: -1870.0088,
							z: 6.97,
						},
						{
							x: -1175.8418,
							y: -1983.178,
							z: 13.37,
						},
						{
							x: -1320.6989,
							y: -2116.2197,
							z: 14.21,
						},
						{
							x: -748.4308,
							y: -2812.5627,
							z: 13.929,
						},
						{
							x: -695.2088,
							y: -2527.0417,
							z: 13.929,
						},
						{
							x: -689.9341,
							y: -2403.5342,
							z: 13.929,
						},
						{
							x: -782.4,
							y: -2376.4219,
							z: 14.553,
						},
						{
							x: -641.3143,
							y: -2213.3142,
							z: 5.9766,
						},
						{
							x: -790.2198,
							y: -2291.5254,
							z: 13.238,
						},
						{
							x: -872.9275,
							y: -2352.1978,
							z: 13.929,
						},
						{
							x: -979.9648,
							y: -2370.9495,
							z: 13.912,
						},
						{
							x: 1232.545,
							y: -2932.2593,
							z: 12.143,
						},
						{
							x: 1289.8945,
							y: -3174.1978,
							z: 5.8923,
						},
						{
							x: 828.1714,
							y: -3194.5979,
							z: 14.4857,
						},
						{
							x: 797.4725,
							y: -2979.9297,
							z: 17.7715,
						},
						{
							x: 676.4967,
							y: -2736.1582,
							z: 6.0103,
						},
						{
							x: 616.1802,
							y: -2849.1956,
							z: 2.5897,
						},
						{
							x: 557.8813,
							y: -2631.6924,
							z: 6.1619,
						},
						{
							x: 347.1297,
							y: -2540.0703,
							z: 5.7238,
						},
						{
							x: 362.822,
							y: -2758.3779,
							z: 14.1992,
						},
						{
							x: 267.3099,
							y: -2993.5913,
							z: 5.7238,
						},
						{
							x: 155.4594,
							y: -2798.6902,
							z: 5.9934,
						},
						{
							x: 194.189,
							y: -2515.7935,
							z: 5.9766,
						},
						{
							x: 128.2022,
							y: -2418.3823,
							z: 13.8623,
						},
						{
							x: -64.8132,
							y: -2413.7407,
							z: 8.8074,
						},
						{
							x: -38.8615,
							y: -2532.4614,
							z: 5.9934,
						},
						{
							x: -263.1956,
							y: -2388.6726,
							z: 5.9934,
						},
						{
							x: -330.422,
							y: -2565.4812,
							z: 6.0103,
						},
						{
							x: -438.9363,
							y: -2643.9692,
							z: 8.7568,
						},
						{
							x: -544.655,
							y: -2803.9253,
							z: 5.9934,
						},
						{
							x: 680.5846,
							y: -2451.8374,
							z: 26.4323,
						},
						{
							x: 716.0308,
							y: -2254.8132,
							z: 29.2292,
						},
						{
							x: 776.0835,
							y: -2540.2153,
							z: 20.0461,
						},
						{
							x: 1061.723,
							y: -2446.7473,
							z: 28.993,
						},
						{
							x: 970.9846,
							y: -2263.9385,
							z: 39.811,
						},
						{
							x: 1016.8615,
							y: -2032.6549,
							z: 30.83,
						},
						{
							x: 1104.4879,
							y: -2129.8945,
							z: 31.352,
						},
						{
							x: 1235.2483,
							y: -2557.6484,
							z: 39.103,
						},
						{
							x: 1540.4967,
							y: -2502.2769,
							z: 59.575,
						},
						{
							x: 1483.556,
							y: -1971.7318,
							z: 70.865,
						},
						{
							x: 1328.1362,
							y: -2067.7847,
							z: 51.875,
						},
						{
							x: 1372.5626,
							y: -1863.4022,
							z: 57.098,
						},
						{
							x: 1009.556,
							y: -1863.4813,
							z: 30.88,
						},
						{
							x: 893.5912,
							y: -1833.2836,
							z: 30.611,
						},
						{
							x: 758.0571,
							y: -1909.1208,
							z: 35.1267,
						},
						{
							x: 789.4286,
							y: -1609.5692,
							z: 31.1838,
						},
						{
							x: 874.2593,
							y: -1550.6769,
							z: 30.4425,
						},
						{
							x: 969.7846,
							y: -1458.9363,
							z: 31.3356,
						},
						{
							x: 765.7451,
							y: -1488.3429,
							z: 20.1304,
						},
						{
							x: 1063.2527,
							y: -1907.2351,
							z: 31.015,
						},
						{
							x: 1399.5297,
							y: -1612.5099,
							z: 57.318,
						},
						{
							x: 1790.8879,
							y: -1277.6967,
							z: 96.005,
						},
						{
							x: 1948.2329,
							y: -975.4418,
							z: 78.4982,
						},
						{
							x: 1217.2748,
							y: -1228.734,
							z: 35.43,
						},
						{
							x: 1129.4374,
							y: -1255.1208,
							z: 20.787,
						},
						{
							x: 1107.5472,
							y: -1405.2131,
							z: 29.381,
						},
						{
							x: 1201.978,
							y: -1073.5648,
							z: 40.805,
						},
						{
							x: 1089.6395,
							y: -890.8483,
							z: 47.4271,
						},
						{
							x: 1038.6066,
							y: -784.9187,
							z: 58.0088,
						},
						{
							x: 1084.8528,
							y: -694.2593,
							z: 57.9919,
						},
						{
							x: 1348.2858,
							y: -747.0198,
							z: 67.3099,
						},
						{
							x: 1321.9648,
							y: -448.589,
							z: 69.0791,
						},
						{
							x: 1229.1296,
							y: -436.0747,
							z: 67.6974,
						},
						{
							x: 1159.3319,
							y: -386.9011,
							z: 72.8535,
						},
						{
							x: 1135.0417,
							y: -296.8352,
							z: 68.7927,
						},
						{
							x: 992.611,
							y: -486.989,
							z: 61.5809,
						},
						{
							x: 1029.4154,
							y: -262.9055,
							z: 58.1436,
						},
						{
							x: 1054.866,
							y: -175.7538,
							z: 69.8374,
						},
						{
							x: 899.2352,
							y: -288.1978,
							z: 65.6417,
						},
						{
							x: 743.7231,
							y: -233.8154,
							z: 66.0967,
						},
						{
							x: 773.1693,
							y: -116.6242,
							z: 75.8359,
						},
						{
							x: 1090.2726,
							y: -105.5473,
							z: 81.9861,
						},
						{
							x: 1173.5341,
							y: 277.5692,
							z: 81.8513,
						},
						{
							x: 707.8549,
							y: 143.156,
							z: 80.7393,
						},
						{
							x: 736.4835,
							y: 213.8506,
							z: 87.0242,
						},
						{
							x: 375.2176,
							y: 235.4505,
							z: 112.8887,
						},
						{
							x: 344.3736,
							y: 334.7341,
							z: 104.9524,
						},
						{
							x: 182.9538,
							y: 386.3868,
							z: 108.5582,
						},
						{
							x: 90.5011,
							y: 303.8637,
							z: 109.9905,
						},
						{
							x: 288.989,
							y: 203.3934,
							z: 104.3627,
						},
						{
							x: 466.9714,
							y: -63.2703,
							z: 88.844,
						},
						{
							x: 313.6088,
							y: 61.1736,
							z: 99.8806,
						},
						{
							x: 67.0418,
							y: 175.0154,
							z: 112.9729,
						},
						{
							x: 36.8835,
							y: 129.3626,
							z: 104.1099,
						},
						{
							x: -1.7143,
							y: 284.9407,
							z: 112.9055,
						},
						{
							x: -210.0132,
							y: 307.7275,
							z: 96.9319,
						},
						{
							x: 287.0242,
							y: -200.2813,
							z: 61.5472,
						},
						{
							x: -32.9407,
							y: -198.4615,
							z: 52.3473,
						},
						{
							x: -105.4813,
							y: -428.2549,
							z: 36.104,
						},
						{
							x: -525.7978,
							y: -35.4593,
							z: 44.5121,
						},
						{
							x: -329.6044,
							y: -228.8703,
							z: 42.4227,
						},
						{
							x: -474.1451,
							y: 30.0264,
							z: 61.1428,
						},
						{
							x: -581.578,
							y: 331.0022,
							z: 84.8169,
						},
						{
							x: -775.8593,
							y: 368.1626,
							z: 87.8499,
						},
						{
							x: -1362.9626,
							y: -17.9868,
							z: 53.3751,
						},
						{
							x: -1233.7847,
							y: -43.9912,
							z: 45.8938,
						},
						{
							x: -1327.3319,
							y: -99.8374,
							z: 49.5333,
						},
						{
							x: -1388.9407,
							y: -172.9978,
							z: 47.376,
						},
						{
							x: -1551.5341,
							y: -301.5297,
							z: 48,
						},
						{
							x: -1448.7957,
							y: -364.0088,
							z: 43.534,
						},
						{
							x: -1550.7561,
							y: -417.7978,
							z: 41.984,
						},
						{
							x: -1550.0571,
							y: -462.7253,
							z: 47.359,
						},
						{
							x: -1380.3165,
							y: -456.7912,
							z: 34.469,
						},
						{
							x: -1574.6241,
							y: -598.8264,
							z: 33.964,
						},
						{
							x: -1464.3824,
							y: -583.3318,
							z: 30.964,
						},
						{
							x: -1413.2571,
							y: -710.9011,
							z: 34.469,
						},
						{
							x: -1668.7517,
							y: -598.3912,
							z: 33.863,
						},
						{
							x: -1778.255,
							y: -504.3297,
							z: 38.783,
						},
						{
							x: -1784.6373,
							y: -358.0352,
							z: 45.034,
						},
						{
							x: -1911.7186,
							y: -295.5165,
							z: 49.011,
						},
						{
							x: -1951.701,
							y: -339.6264,
							z: 46.011,
						},
						{
							x: -2116.5759,
							y: -428.3736,
							z: 8.0322,
						},
						{
							x: -1971.3759,
							y: -562.3912,
							z: 11.452,
						},
						{
							x: -1733.2087,
							y: -728.0571,
							z: 10.408,
						},
						{
							x: -1639.5824,
							y: -814.9319,
							z: 10.155,
						},
						{
							x: -1493.6703,
							y: -1093.6615,
							z: 2.353,
						},
						{
							x: -1398.8967,
							y: -1175.3407,
							z: 3.398,
						},
						{
							x: -1428.5275,
							y: -1423.8989,
							z: 3.331,
						},
						{
							x: -1383.5868,
							y: -1490.0176,
							z: 4.375,
						},
						{
							x: -1304.6769,
							y: -1598.3209,
							z: 4.224,
						},
						{
							x: -1248.4615,
							y: -1424.0176,
							z: 4.308,
						},
						{
							x: -1211.0505,
							y: -1595.9868,
							z: 4.173,
						},
						{
							x: -1078.0615,
							y: -1708.2594,
							z: 4.46,
						},
						{
							x: -958.0483,
							y: -1581.3363,
							z: 4.9993,
						},
						{
							x: -1025.0505,
							y: -1522.2329,
							z: 5.572,
						},
						{
							x: -975.2703,
							y: -1469.9736,
							z: 4.9993,
						},
						{
							x: -1066.0483,
							y: -1440.7913,
							z: 5.403,
						},
						{
							x: -1016.967,
							y: -1324.5758,
							z: 5.825,
						},
						{
							x: -812.3077,
							y: -1402.0879,
							z: 4.9993,
						},
						{
							x: -685.3978,
							y: -1329.9956,
							z: 5.0835,
						},
						{
							x: -770.6638,
							y: -1515.2043,
							z: 5.3195,
						},
						{
							x: -568.6022,
							y: -1142.4396,
							z: 22.169,
						},
						{
							x: -588.8703,
							y: -1287.7186,
							z: 13.71,
						},
						{
							x: -621.244,
							y: -1419.9956,
							z: 12.312,
						},
						{
							x: -630.5406,
							y: -1583.6967,
							z: 26.567,
						},
						{
							x: -863.1165,
							y: -1688.3209,
							z: 4.359,
						},
						{
							x: -1065.4286,
							y: -1870.0088,
							z: 6.97,
						},
						{
							x: -1175.8418,
							y: -1983.178,
							z: 13.37,
						},
						{
							x: -1320.6989,
							y: -2116.2197,
							z: 14.21,
						},
						{
							x: -748.4308,
							y: -2812.5627,
							z: 13.929,
						},
						{
							x: -695.2088,
							y: -2527.0417,
							z: 13.929,
						},
						{
							x: -689.9341,
							y: -2403.5342,
							z: 13.929,
						},
						{
							x: -782.4,
							y: -2376.4219,
							z: 14.553,
						},
						{
							x: -641.3143,
							y: -2213.3142,
							z: 5.9766,
						},
						{
							x: -790.2198,
							y: -2291.5254,
							z: 13.238,
						},
						{
							x: -872.9275,
							y: -2352.1978,
							z: 13.929,
						},
						{
							x: -979.9648,
							y: -2370.9495,
							z: 13.912,
						},

						{
							x: -856.5758,
							y: -2215.6616,
							z: 7.5267,
						},
						{
							x: -986.1099,
							y: -2058.356,
							z: 9.3971,
						},
						{
							x: -1065.0593,
							y: -1947.1648,
							z: 13.15,
						},
						{
							x: -783.2703,
							y: -2001.2043,
							z: 9.0601,
						},
						{
							x: -549.3759,
							y: -2221.49,
							z: 6.381,
						},
						{
							x: -400.8791,
							y: -2185.0813,
							z: 10.138,
						},
						{
							x: -331.2791,
							y: -2204.7825,
							z: 8.3693,
						},
						{
							x: -183.2571,
							y: -2223.1384,
							z: 8.5883,
						},
						{
							x: -26.6901,
							y: -2101.3713,
							z: 16.693,
						},
						{
							x: -249.4154,
							y: -1880.4528,
							z: 27.729,
						},
						{
							x: -469.2,
							y: -1818.1055,
							z: 20.416,
						},
						{
							x: -635.4857,
							y: -1780.9583,
							z: 24.123,
						},
						{
							x: -633.2835,
							y: -1663.9385,
							z: 25.96,
						},
						{
							x: -468.0791,
							y: -1313.4725,
							z: 25.623,
						},
						{
							x: -111.9165,
							y: -1045.9648,
							z: 27.257,
						},
						{
							x: 44.822,
							y: -1056.2902,
							z: 38.1428,
						},
						{
							x: 176.3077,
							y: -1151.433,
							z: 29.2968,
						},
						{
							x: 401.7099,
							y: -1156.4044,
							z: 29.2799,
						},
						{
							x: 512.2549,
							y: -639.3626,
							z: 24.7303,
						},
						{
							x: 302.822,
							y: -694.0747,
							z: 29.2968,
						},
						{
							x: 196.3912,
							y: -937.0154,
							z: 30.6783,
						},
						{
							x: 250.8264,
							y: -757.8066,
							z: 34.6212,
						},
						{
							x: 204.356,
							y: -718.6418,
							z: 47.0564,
						},
						{
							x: 187.4901,
							y: -440.8615,
							z: 41.6476,
						},
						{
							x: 148.1407,
							y: -359.2879,
							z: 43.2483,
						},
						{
							x: -353.3802,
							y: -1064.3209,
							z: 30.526,
						},
						{
							x: -306.4615,
							y: -1168.9846,
							z: 23.298,
						},
						{
							x: -261.5868,
							y: -610.6418,
							z: 33.4923,
						},
						{
							x: -340.8,
							y: -685.9385,
							z: 32.8352,
						},
						{
							x: -479.367,
							y: -610.8923,
							z: 31.167,
						},
						{
							x: -489.2308,
							y: -735.3494,
							z: 33.2058,
						},
						{
							x: -615.5604,
							y: -681.2571,
							z: 36.2726,
						},
						{
							x: -729.5736,
							y: -744.633,
							z: 35.5817,
						},
						{
							x: -710.6241,
							y: -872.2286,
							z: 23.433,
						},
						{
							x: -584.4132,
							y: -887.1956,
							z: 31.2175,
						},
						{
							x: -696.633,
							y: -983.7363,
							z: 20.3832,
						},
						{
							x: -833.8945,
							y: -924.4879,
							z: 16.3392,
						},
						{
							x: -754.6418,
							y: -1070.8088,
							z: 11.874,
						},
						{
							x: -644.3077,
							y: -1163.1165,
							z: 9.4308,
						},
						{
							x: -982.9846,
							y: -845.3406,
							z: 15.0923,
						},
						{
							x: -1241.4989,
							y: -960.9363,
							z: 3.1458,
						},
						{
							x: -1321.5428,
							y: -963.8505,
							z: 7.5604,
						},
						{
							x: -2332.5891,
							y: 262.8923,
							z: 169.588,
						},
						{
							x: -2457.5737,
							y: -299.5253,
							z: 3.5502,
						},
						{
							x: -3077.301,
							y: 145.3055,
							z: 10.6945,
						},
						{
							x: -3051.2175,
							y: 291.7319,
							z: 20.1809,
						},
						{
							x: -3136.5232,
							y: 298.0616,
							z: 2.337,
						},
						{
							x: -3018.8176,
							y: 389.9604,
							z: 14.6205,
						},
						{
							x: -2939.2483,
							y: 479.5385,
							z: 15.2271,
						},
						{
							x: -3064.3384,
							y: 585.1649,
							z: 1.9832,
						},
						{
							x: -3064.8792,
							y: 712.5626,
							z: 23.0454,
						},
						{
							x: -3074.6506,
							y: 807.6396,
							z: 19.3722,
						},
						{
							x: -3209.7231,
							y: 886.9055,
							z: 5.0161,
						},
						{
							x: -3192.145,
							y: 992.6901,
							z: 19.1194,
						},
						{
							x: -3144.9363,
							y: 1186.6681,
							z: 20.13,
						},
						{
							x: -3223.1604,
							y: 1253.3011,
							z: 3.4659,
						},
						{
							x: -3131.7759,
							y: 1353.9033,
							z: 20.484,
						},
						{
							x: -3044.4263,
							y: 1481.5912,
							z: 28.386,
						},
						{
							x: -3000.8572,
							y: 1390.866,
							z: 41.428,
						},
						{
							x: -2908.9187,
							y: 1590.2241,
							z: 29.465,
						},
						{
							x: -2984.3999,
							y: 1648.0352,
							z: 30.762,
						},
						{
							x: -3079.4768,
							y: 1794.6461,
							z: 32.632,
						},
						{
							x: -2999.2219,
							y: 1911.0461,
							z: 28.538,
						},
						{
							x: -2998.4175,
							y: 2126.0044,
							z: 39.996,
						},
						{
							x: -2821.8989,
							y: 2249.1033,
							z: 29.751,
						},
						{
							x: -2816.8088,
							y: 2323.9385,
							z: 2.6066,
						},
						{
							x: -2450.8748,
							y: 2253.6133,
							z: 56.071,
						},
						{
							x: -2441.1165,
							y: 2372.3999,
							z: 30.83,
						},
						{
							x: -2229.2571,
							y: 2424.7385,
							z: 10.323,
						},
						{
							x: -2256.2769,
							y: 2536.8132,
							z: 4.1904,
						},
						{
							x: -1923.4154,
							y: 2480.3867,
							z: 3.8367,
						},
						{
							x: -1784.8748,
							y: 2435.7759,
							z: 30.762,
						},
						{
							x: -1604.3209,
							y: 2438.3208,
							z: 26.196,
						},
						{
							x: -1466.3473,
							y: 2519.4197,
							z: 28.285,
						},
						{
							x: -1407.7979,
							y: 2515.2,
							z: 33.003,
						},
						{
							x: -1401.9561,
							y: 2225.7759,
							z: 28.706,
						},
						{
							x: -1190.8352,
							y: 2262.0396,
							z: 78.296,
						},
						{
							x: -1017.2176,
							y: 2767.3845,
							z: 24.629,
						},
						{
							x: -782.2022,
							y: 2797.4241,
							z: 23.5509,
						},
						{
							x: -611.6176,
							y: 2918.4658,
							z: 15.0249,
						},
						{
							x: -615.9824,
							y: 2800.9451,
							z: 37.5531,
						},
						{
							x: -732.2242,
							y: 2608.6814,
							z: 60.1655,
						},
						{
							x: -485.6703,
							y: 2710.9451,
							z: 45.759,
						},
						{
							x: -348,
							y: 2705.855,
							z: 72.4659,
						},
						{
							x: -128.0967,
							y: 2800.0483,
							z: 53.0886,
						},
						{
							x: -165.2703,
							y: 2994.0527,
							z: 31.1165,
						},
						{
							x: 206.0967,
							y: 2504.479,
							z: 54.959,
						},
						{
							x: 164.756,
							y: 3055.4504,
							z: 43.0798,
						},
						{
							x: 148.6681,
							y: 3269.5386,
							z: 41.3275,
						},
						{
							x: 289.7538,
							y: 3109.4504,
							z: 41.9341,
						},
						{
							x: 317.5648,
							y: 3022.7078,
							z: 41.6981,
						},
						{
							x: 335.5516,
							y: 2695.9648,
							z: 56.1384,
						},
						{
							x: 611.6572,
							y: 2499.4417,
							z: 64.715,
						},
						{
							x: 610.8,
							y: 2218.6023,
							z: 60.351,
						},
						{
							x: 912.5538,
							y: 2583.4812,
							z: 62.6593,
						},
						{
							x: 1051.0154,
							y: 2729.9736,
							z: 38.2946,
						},
						{
							x: 1128.1846,
							y: 2556.6462,
							z: 58.9692,
						},
						{
							x: 1238.7296,
							y: 2601.9033,
							z: 40.4344,
						},
						{
							x: 1348.7869,
							y: 2738.5188,
							z: 52.2631,
						},
						{
							x: 1379.4198,
							y: 2642.7693,
							z: 47.4608,
						},
						{
							x: 1223.8682,
							y: 2880.0132,
							z: 47.2924,
						},
						{
							x: 1755.8506,
							y: 2829.8506,
							z: 45.3883,
						},
						{
							x: 1787.2616,
							y: 3063.2571,
							z: 62.1201,
						},
						{
							x: 2065.688,
							y: 2900.1099,
							z: 47.3934,
						},
						{
							x: 160.9451,
							y: 3275.3801,
							z: 40.1816,
						},
						{
							x: 306.5934,
							y: 3490.5232,
							z: 35.8345,
						},
						{
							x: 486,
							y: 3529.2263,
							z: 32.5992,
						},
						{
							x: 716.9407,
							y: 3554.8352,
							z: 32.5656,
						},
						{
							x: 859.9648,
							y: 3650.1494,
							z: 33.071,
						},
						{
							x: 1067.855,
							y: 3493.2922,
							z: 31.2175,
						},
						{
							x: 1273.411,
							y: 3589.0945,
							z: 32.734,
						},
						{
							x: 1548.6725,
							y: 3446.2417,
							z: 37.1487,
						},
						{
							x: 1115.5253,
							y: 3648.3296,
							z: 33.0037,
						},
						{
							x: 1366.6681,
							y: 3711.6792,
							z: 32.8689,
						},
						{
							x: 1707.6923,
							y: 3925.0154,
							z: 34.2843,
						},
						{
							x: 1821.2307,
							y: 3822.7517,
							z: 33.4249,
						},
						{
							x: 1973.2616,
							y: 3956.334,
							z: 32.0264,
						},
						{
							x: 2082.6199,
							y: 3793.7803,
							z: 32.0938,
						},
						{
							x: 2303.9736,
							y: 3890.3208,
							z: 34.7054,
						},
						{
							x: 2400.8704,
							y: 4075.5957,
							z: 34.082,
						},
						{
							x: 2433.1648,
							y: 4342.4307,
							z: 35.7671,
						},
						{
							x: 2447.8418,
							y: 4553.6968,
							z: 35.9861,
						},
						{
							x: 2496.4482,
							y: 4292.8481,
							z: 39.1875,
						},
						{
							x: 2574.7253,
							y: 4109.8682,
							z: 39.9963,
						},
						{
							x: 2441.7891,
							y: 3726.2769,
							z: 50.6622,
						},
						{
							x: 2203.2263,
							y: 3675.0461,
							z: 36.7781,
						},
						{
							x: 1626.4352,
							y: 3786.1187,
							z: 34.6718,
						},
						{
							x: 1443.9824,
							y: 3555.1252,
							z: 34.7729,
						},
						{
							x: 1614.1582,
							y: 3533.5781,
							z: 35.9019,
						},
						{
							x: 1971.0066,
							y: 3636.8967,
							z: 34.7898,
						},
						{
							x: 2025.7318,
							y: 3506.479,
							z: 40.9063,
						},
						{
							x: 2021.9341,
							y: 3328.4175,
							z: 45.7927,
						},
						{
							x: 2214.6858,
							y: 3267.9956,
							z: 48.219,
						},
						{
							x: 2622.3691,
							y: 4208.8086,
							z: 43.063,
						},
						{
							x: 3000.2769,
							y: 4406.9404,
							z: 69.4667,
						},
						{
							x: 2896.9846,
							y: 4825.9385,
							z: 62.3561,
						},
						{
							x: 2721.7847,
							y: 4996.312,
							z: 32.3129,
						},
						{
							x: 3374.2285,
							y: 4950.4087,
							z: 32.6498,
						},
						{
							x: 3490.5496,
							y: 4700.0835,
							z: 51.6732,
						},
						{
							x: 3842.8352,
							y: 4404.2637,
							z: 3.6849,
						},
						{
							x: 3838.9714,
							y: 4244.4922,
							z: 6.7686,
						},
						{
							x: 3933.9165,
							y: 4018.51,
							z: 3.1121,
						},
						{
							x: 3910.2725,
							y: 3452.2285,
							z: 5.589,
						},
						{
							x: 3831.4946,
							y: 3279.4548,
							z: 19.7429,
						},
						{
							x: 3344.8352,
							y: 2687.6045,
							z: 13.6095,
						},
						{
							x: 3320.4263,
							y: 2490.4087,
							z: 2.5223,
						},
						{
							x: 2680.7473,
							y: 5344.2329,
							z: 64.3949,
						},
						{
							x: 2514.9495,
							y: 5650.3384,
							z: 45.0513,
						},
						{
							x: 1875.033,
							y: 6279.7188,
							z: 57.4528,
						},
						{
							x: 1853.0374,
							y: 6401.6177,
							z: 46.2307,
						},
						{
							x: 1603.0286,
							y: 6616.5229,
							z: 16.0359,
						},
						{
							x: 1525.3583,
							y: 6625.2925,
							z: 2.4718,
						},
						{
							x: 1448.3737,
							y: 6507.4551,
							z: 20.0293,
						},
						{
							x: 1406.3605,
							y: 6453.9561,
							z: 20.0461,
						},
						{
							x: -136.8923,
							y: 6684.6328,
							z: 1.4608,
						},
						{
							x: -272.4264,
							y: 6631.4243,
							z: 7.4425,
						},
						{
							x: -292.2462,
							y: 6469.1079,
							z: 12.7839,
						},
						{
							x: -551.1693,
							y: 6353.0903,
							z: 3.4491,
						},
						{
							x: -601.7538,
							y: 6145.7935,
							z: 5.8081,
						},
						{
							x: -817.7011,
							y: 5967.0859,
							z: 20.063,
						},
						{
							x: -852.0659,
							y: 5903.3672,
							z: 3.1121,
						},
						{
							x: -843.8637,
							y: 5662.7339,
							z: 18.6813,
						},
						{
							x: -916.8264,
							y: 5553.6528,
							z: 6.4989,
						},
						{
							x: -1117.266,
							y: 5393.8286,
							z: 6.6,
						},
						{
							x: -1279.7142,
							y: 5352.501,
							z: 3.5839,
						},
						{
							x: -1441.2924,
							y: 5409.1255,
							z: 24.646,
						},
						{
							x: -1300.9583,
							y: 5272.6416,
							z: 57.604,
						},
						{
							x: -1434.844,
							y: 5110.1802,
							z: 62.625,
						},
						{
							x: -1675.7935,
							y: 5046.501,
							z: 34.301,
						},
						{
							x: -960.9231,
							y: 5348.1099,
							z: 65.8777,
						},
						{
							x: -697.8593,
							y: 5465.6309,
							z: 45.6915,
						},
						{
							x: -703.8726,
							y: 5613.5342,
							z: 29.0608,
						},
						{
							x: -529.0286,
							y: 5613.8901,
							z: 56.2565,
						},
						{
							x: -382.2593,
							y: 5832,
							z: 49.2975,
						},
						{
							x: -346.1275,
							y: 5956.3647,
							z: 43.9224,
						},
						{
							x: -63.5868,
							y: 6161.6836,
							z: 30.4257,
						},
						{
							x: -46.6286,
							y: 6269.7363,
							z: 40.266,
						},
						{
							x: -185.6835,
							y: 6154.1011,
							z: 48.8425,
						},
						{
							x: -436.6154,
							y: 6237.9165,
							z: 29.7179,
						},
						{
							x: -413.5648,
							y: 6177.2441,
							z: 31.4703,
						},
						{
							x: -141.7582,
							y: 6354.8701,
							z: 31.4703,
						},
						{
							x: -14.4396,
							y: 6457.2397,
							z: 31.3861,
						},
						{
							x: 165.7055,
							y: 6449.0112,
							z: 31.3019,
						},
						{
							x: 302.7692,
							y: 6444.5933,
							z: 32.2792,
						},
						{
							x: 302.7165,
							y: 6623.4199,
							z: 29.1956,
						},
						{
							x: 104.9538,
							y: 6710.7559,
							z: 41.1589,
						},
						{
							x: -238.0484,
							y: 6263.6309,
							z: 31.4703,
						},
						{
							x: -314.5055,
							y: 6374.9404,
							z: 30.3077,
						},
						{
							x: -53.1165,
							y: 6622.3779,
							z: 29.9033,
						},
						{
							x: 132.2505,
							y: 6667.187,
							z: 32.0432,
						},
						{
							x: -197.3143,
							y: 3658.4834,
							z: 51.7238,
						},
						{
							x: -120.7648,
							y: 3794.0967,
							z: 32.3802,
						},
						{
							x: -186.7121,
							y: 3984.6199,
							z: 31.723,
						},
						{
							x: -105.8769,
							y: 4338.4878,
							z: 64.8162,
						},
						{
							x: 152.2681,
							y: 4347.2178,
							z: 55.8184,
						},
						{
							x: 396.633,
							y: 4364.123,
							z: 63.5187,
						},
						{
							x: 560.9934,
							y: 4181.0112,
							z: 38.1765,
						},
						{
							x: 721.2923,
							y: 4288.958,
							z: 66.5011,
						},
						{
							x: 841.3055,
							y: 4209.1914,
							z: 59.4916,
						},
						{
							x: 806.3604,
							y: 4506.5405,
							z: 51.0498,
						},
						{
							x: 1046.3209,
							y: 4270.457,
							z: 37.452,
						},
						{
							x: 1144.3649,
							y: 4482.936,
							z: 77.285,
						},
						{
							x: 1256.2153,
							y: 4371.2837,
							z: 48.1348,
						},
						{
							x: 1373.9604,
							y: 4436.0967,
							z: 65.3217,
						},
						{
							x: 1670.4132,
							y: 4612.0615,
							z: 45.877,
						},
						{
							x: 1909.3451,
							y: 4546.5361,
							z: 35.5648,
						},
						{
							x: 2081.6309,
							y: 4594.0747,
							z: 34.4359,
						},
						{
							x: 1777.6879,
							y: 4751.6045,
							z: 40.822,
						},
						{
							x: 1867.3715,
							y: 4948.7075,
							z: 50.6454,
						},
						{
							x: 2559.9956,
							y: 3427.3054,
							z: 68.186,
						},
						{
							x: 2924.9934,
							y: 3378.2241,
							z: 86.03,
						},
						{
							x: 2450.6243,
							y: 3281.6177,
							z: 51.1003,
						},
						{
							x: 2097.2043,
							y: 3185.5254,
							z: 43.3832,
						},
						{
							x: 1676.9275,
							y: 2335.5562,
							z: 76.5436,
						},
						{
							x: 1532.9011,
							y: 2384.2021,
							z: 47.7642,
						},
						{
							x: 1469.2351,
							y: 2599.688,
							z: 51.2014,
						},
						{
							x: 1428.9363,
							y: 2355.3098,
							z: 73.7296,
						},
						{
							x: 1392.0923,
							y: 2191.345,
							z: 97.7576,
						},
						{
							x: 1399.2263,
							y: 2119.2395,
							z: 105.002,
						},
						{
							x: 1418.7428,
							y: 2022.1055,
							z: 120.723,
						},
						{
							x: 1608.4088,
							y: 1875.4681,
							z: 101.7,
						},
						{
							x: 1402.3517,
							y: 1584.2241,
							z: 106.553,
						},
						{
							x: 1466.6901,
							y: 1327.1604,
							z: 114.135,
						},
						{
							x: 1333.0549,
							y: 941.011,
							z: 123.0154,
						},
						{
							x: 1194.6857,
							y: 1134.7781,
							z: 165.038,
						},
						{
							x: 1213.1472,
							y: 1817.2087,
							z: 78.7847,
						},
						{
							x: 949.4637,
							y: 2058.989,
							z: 61.2102,
						},
						{
							x: 270.9231,
							y: 2057.6045,
							z: 122.9648,
						},
						{
							x: 0.5275,
							y: 1897.1736,
							z: 210.8367,
						},
						{
							x: 324.567,
							y: 1712.7429,
							z: 245.1091,
						},
						{
							x: 151.0022,
							y: 1433.578,
							z: 260.8975,
						},
						{
							x: 176.2549,
							y: 1046.2682,
							z: 245.918,
						},
						{
							x: -44.7824,
							y: 1265.7362,
							z: 299.0959,
						},
						{
							x: -388.6286,
							y: 1291.7406,
							z: 344.017,
						},
						{
							x: -506.9275,
							y: 1186.3121,
							z: 324.775,
						},
						{
							x: -810.4747,
							y: 1047.8242,
							z: 266.12,
						},
						{
							x: -1160.7561,
							y: 933.4154,
							z: 197.761,
						},
						{
							x: -1402.9187,
							y: 740.4528,
							z: 183,
						},
						{
							x: -1625.433,
							y: 899.0901,
							z: 176.058,
						},
						{
							x: -1767.4813,
							y: 611.9077,
							z: 180.237,
						},
						{
							x: -2241.9561,
							y: 1041.4813,
							z: 207.36,
						},
						{
							x: -2443.5693,
							y: 1024.4308,
							z: 192.31,
						},
						{
							x: -2684.5056,
							y: 1278.3824,
							z: 145.91,
						},
						{
							x: -2607.0593,
							y: 1675.6747,
							z: 139.32,
						},
						{
							x: -2346.5671,
							y: 1870.3649,
							z: 183.43,
						},
						{
							x: -1993.1208,
							y: 1881.6527,
							z: 204.75,
						},
					],
				},
			},
			{
				id: 9,
				name: 'Event Peds',
				controller: 'EventPedsActivity',
				config: {
					peds: [
						// Убрали отсюда, включили по дефолту, чтобы все NPC из этого конфига спавнились только в Янктоне
						// Потом нужно будет доработать эту механику
						// {
						// 	pedId: 164,
						// 	start: '2025-20-01',
						// 	end: '2025-02-06',
						// },
						{
							// Аренда авто Yankton
							pedId: 287,
							start: '2025-01-28 6:00:00', // год - месяц - день
							end: '2025-02-11 6:00:00',
							inYankton: true,
						},
						{
							// Аренда авто Yankton
							pedId: 290,
							start: '2025-01-28 6:00:00',
							end: '2025-02-11 6:00:00',
							inYankton: true,
						},
						{
							pedId: 390,
							activity: 'MazeBankMinigames',
							start: '2025-01-28 6:00:00',
							end: '2025-02-11 6:00:00',
							inYankton: true,
						},
						{
							pedId: 391,
							activity: 'LostVehicles',
							start: '2025-01-30 10:00:00',
							end: '2025-02-11 6:00:00',
							inYankton: true,
						},
						{
							pedId: 392,
							activity: 'HitElfs',
							start: '2025-02-01 10:00:00',
							end: '2025-02-11 6:00:00',
							inYankton: true,
						},
						{
							pedId: 393,
							activity: 'MatchSame',
							start: '2025-02-03 10:00:00',
							end: '2025-02-11 6:00:00',
							inYankton: true,
						},
						{
							pedId: 394,
							activity: 'GrinchRabbits',
							start: '2025-02-04 10:00:00',
							end: '2025-02-11 6:00:00',
							inYankton: true,
						},
						{
							pedId: 395,
							activity: 'MatchTree',
							start: '2025-02-07 10:00:00',
							end: '2025-02-11 6:00:00',
							inYankton: true,
						},
						{
							pedId: 396,
							activity: 'DeliveryGifts',
							start: '2025-02-09 10:00:00',
							end: '2025-02-11 6:00:00',
							inYankton: true,
						},
					],
				},
			},
			{
				id: 10,
				name: 'Event Ipls',
				controller: 'EventIpls',
				config: {
					start: '2025-01-28',
					end: '2025-02-11',
					IPLs: [
						'MJ_XM_Apartaments1',
						'MJ_XM_Apartaments2',
						'MJ_XM_Apartaments3',
						'MJ_XM_Apartaments4',
						'MJ_XM_Apartaments5',
						'MJ_XM_Apartaments6',
						'MJ_XM_Army_Checkpoint1_01',
						'MJ_XM_Army_Checkpoint1_02',
						'MJ_XM_Army_Checkpoint2_01',
						'MJ_XM_Army_Checkpoint2_02',
						'MJ_XM_Auction',
						'MJ_XM_Automarket',
						'MJ_XM_Autoschool',
						'MJ_XM_Autoshop_1',
						'MJ_XM_Autoshop_2',
						'MJ_XM_Autoshop_3',
						'MJ_XM_Autoshop_4',
						'MJ_XM_Autoshop_5',
						'MJ_XM_Autoshop_6',
						'MJ_XM_Autoshop_7',
						'MJ_XM_Bank_Blaine',
						'MJ_XM_Bank_Fleeca1',
						'MJ_XM_Bank_Fleeca2',
						'MJ_XM_Bank_Fleeca3',
						'MJ_XM_Bank_Fleeca4',
						'MJ_XM_Bank_Fleeca5',
						'MJ_XM_Bank_Fleeca6',
						'MJ_XM_Bank_PacificStandart',
						'MJ_XM_BarPalleto',
						'MJ_XM_Barber_1',
						'MJ_XM_Barber_2',
						'MJ_XM_Barber_3',
						'MJ_XM_Barber_4',
						'MJ_XM_Barber_5',
						'MJ_XM_Barber_6',
						'MJ_XM_Barber_7',
						'MJ_XM_BeanMachine',
						'MJ_XM_Biker_AOD_01',
						'MJ_XM_Biker_AOD_02',
						'MJ_XM_Biker_TheLost_01',
						'MJ_XM_Biker_TheLost_02',
						'MJ_XM_Bookmaker',
						'MJ_XM_Business_Motosalon',
						'MJ_XM_Bussines_CarWash',
						'MJ_XM_Casino_01',
						'MJ_XM_Casino_02',
						'MJ_XM_Church1',
						'MJ_XM_Church2',
						'MJ_XM_Church3',
						'MJ_XM_CityHall_01',
						'MJ_XM_CityHall_02',
						'MJ_XM_CityHall_03',
						'MJ_XM_Cshop_1',
						'MJ_XM_Cshop_10',
						'MJ_XM_Cshop_11',
						'MJ_XM_Cshop_12',
						'MJ_XM_Cshop_13',
						'MJ_XM_Cshop_14',
						'MJ_XM_Cshop_2',
						'MJ_XM_Cshop_3',
						'MJ_XM_Cshop_4',
						'MJ_XM_Cshop_5',
						'MJ_XM_Cshop_6',
						'MJ_XM_Cshop_7',
						'MJ_XM_Cshop_8',
						'MJ_XM_Cshop_9',
						'MJ_XM_DV',
						'MJ_XM_DinnerChilliad',
						'MJ_XM_EMSSheriff_Sandy_01',
						'MJ_XM_EMSSheriff_Sandy_02',
						'MJ_XM_EMSSheriff_Sandy_03',
						'MJ_XM_EMS_Paleto_01',
						'MJ_XM_EMS_Paleto_02',
						'MJ_XM_EMS_PillboxHill',
						'MJ_XM_EventPed_LosSantos',
						'MJ_XM_FIB_01',
						'MJ_XM_FIB_02',
						'MJ_XM_Flightschool',
						'MJ_XM_FunicularDown',
						'MJ_XM_FunicularUp',
						'MJ_XM_Gas_1',
						'MJ_XM_Gas_10',
						'MJ_XM_Gas_11',
						'MJ_XM_Gas_12',
						'MJ_XM_Gas_13',
						'MJ_XM_Gas_14',
						'MJ_XM_Gas_15',
						'MJ_XM_Gas_16',
						'MJ_XM_Gas_17',
						'MJ_XM_Gas_18',
						'MJ_XM_Gas_19',
						'MJ_XM_Gas_2',
						'MJ_XM_Gas_20',
						'MJ_XM_Gas_21',
						'MJ_XM_Gas_22',
						'MJ_XM_Gas_23',
						'MJ_XM_Gas_24',
						'MJ_XM_Gas_25',
						'MJ_XM_Gas_26',
						'MJ_XM_Gas_27',
						'MJ_XM_Gas_28',
						'MJ_XM_Gas_3',
						'MJ_XM_Gas_4',
						'MJ_XM_Gas_5',
						'MJ_XM_Gas_6',
						'MJ_XM_Gas_7',
						'MJ_XM_Gas_8',
						'MJ_XM_Gas_9',
						'MJ_XM_Ghetto_Ballas',
						'MJ_XM_Ghetto_Bloods_01',
						'MJ_XM_Ghetto_Bloods_02',
						'MJ_XM_Ghetto_Marabunta_01',
						'MJ_XM_Ghetto_Marabunta_02',
						'MJ_XM_Ghetto_TheFamilies_02',
						'MJ_XM_Ghetto_Vagos',
						'MJ_XM_GolfClub_02',
						'MJ_XM_GolfClub_03',
						'MJ_XM_Gunshop_1',
						'MJ_XM_Gunshop_10',
						'MJ_XM_Gunshop_11',
						'MJ_XM_Gunshop_2',
						'MJ_XM_Gunshop_3',
						'MJ_XM_Gunshop_4',
						'MJ_XM_Gunshop_5',
						'MJ_XM_Gunshop_6',
						'MJ_XM_Gunshop_7',
						'MJ_XM_Gunshop_8',
						'MJ_XM_Gunshop_9',
						'MJ_XM_Gym',
						'MJ_XM_Hookies',
						'MJ_XM_HumanLabs_Enter',
						'MJ_XM_LSC1',
						'MJ_XM_LSC2',
						'MJ_XM_LSC3',
						'MJ_XM_LSC4',
						'MJ_XM_LSC5',
						'MJ_XM_LSC6',
						'MJ_XM_LSPD_MissonRow_01',
						'MJ_XM_LSPD_MissonRow_02',
						'MJ_XM_LegionSquare_02',
						'MJ_XM_LegionSquare_03',
						'MJ_XM_Leopolds',
						'MJ_XM_Mafia_Armenian_02',
						'MJ_XM_Mafia_Armenian_03',
						'MJ_XM_Mafia_CosaNostra_02',
						'MJ_XM_Mafia_CosaNostra_03',
						'MJ_XM_Mafia_Mexican',
						'MJ_XM_Mafia_Russian_02',
						'MJ_XM_Mafia_Russian_03',
						'MJ_XM_Mafia_Yakuza_02',
						'MJ_XM_Mafia_Yakuza_03',
						'MJ_XM_Makret_LosSantos',
						'MJ_XM_Market_PaletoBay',
						'MJ_XM_MazeArena',
						'MJ_XM_MazeBankTower_Top',
						'MJ_XM_Mega_Mall',
						'MJ_XM_Observatory',
						'MJ_XM_Port_Enter_1',
						'MJ_XM_Post_Enter_2',
						'MJ_XM_Prison_Inside',
						'MJ_XM_Prison_Outside_01',
						'MJ_XM_Prison_Outside_02',
						'MJ_XM_Rest_Arcade',
						'MJ_XM_Rest_BahamaMamasWest',
						'MJ_XM_Rest_Cinema1',
						'MJ_XM_Rest_Cinema2',
						'MJ_XM_Rest_Cinema2true',
						'MJ_XM_Rest_Cinema3',
						'MJ_XM_Rest_SplitSidesWest',
						'MJ_XM_Rest_Tequilala',
						'MJ_XM_Rest_ThePalace',
						'MJ_XM_Rest_VanillaUnicorn',
						'MJ_XM_Scene_SisyphusTheater',
						'MJ_XM_Scene_WinewoodBowl',
						'MJ_XM_Service_1',
						'MJ_XM_Service_2',
						'MJ_XM_Service_4',
						'MJ_XM_Service_5',
						'MJ_XM_Service_6',
						'MJ_XM_Sheriff_Paleto_01',
						'MJ_XM_Sheriff_Paleto_02',
						'MJ_XM_Spawn_Airport',
						'MJ_XM_Spawn_Chumash',
						'MJ_XM_Spawn_LSVokzal',
						'MJ_XM_Spawn_PBHotel',
						'MJ_XM_Spawn_SandyShores',
						'MJ_XM_Spawn_VespucciCanals',
						'MJ_XM_Start_01',
						'MJ_XM_Start_02',
						'MJ_XM_Start_03',
						'MJ_XM_Store_1',
						'MJ_XM_Store_10',
						'MJ_XM_Store_11',
						'MJ_XM_Store_13',
						'MJ_XM_Store_14',
						'MJ_XM_Store_15',
						'MJ_XM_Store_16',
						'MJ_XM_Store_2',
						'MJ_XM_Store_21',
						'MJ_XM_Store_22',
						'MJ_XM_Store_3',
						'MJ_XM_Store_4',
						'MJ_XM_Store_5',
						'MJ_XM_Store_6',
						'MJ_XM_Store_7',
						'MJ_XM_Store_8',
						'MJ_XM_Store_9',
						'MJ_XM_Tattoo_1',
						'MJ_XM_Tattoo_2',
						'MJ_XM_Tattoo_3',
						'MJ_XM_Tattoo_4',
						'MJ_XM_Tattoo_5',
						'MJ_XM_Tattoo_6',
						'MJ_XM_Tunnel_Enter',
						'MJ_XM_Tunnel_Exit',
						'MJ_XM_Univercity',
						'MJ_XM_WeazelNews_01',
						'MJ_XM_WeazelNews_02',
						'MJ_XM_Work_1',
						'MJ_XM_Work_10',
						'MJ_XM_Work_11',
						'MJ_XM_Work_2',
						'MJ_XM_Work_3',
						'MJ_XM_Work_4',
						'MJ_XM_Work_5',
						'MJ_XM_Work_6',
						'MJ_XM_Work_7',
						'MJ_XM_Work_8',
						'MJ_XM_Work_9',
						'mj_xm_yankton_2',
					],
				},
			},
			{
				id: 12,
				name: 'Search Props',
				controller: 'SearchProps',
				config: {
					start: '2025-01-28',
					end: '2025-02-11',

					searchXpAward: 150,

					propsAmount: 40,
					QR_CODE_MODEL: 'mj_ny_paper_prop',
					// QR_CODE_MODEL: 'xm_prop_x17_note_paper_01a',

					SETTINGS_RECEIVED_PRESENTS_KEY: 'qrCodes_presents',
					SETTINGS_PRESENTS_FOUND: 'qrCodes_presents_found',

					REWARD_CHANGE_DATE: '2025-02-06 10:00:00',

					QR_PARTS_ACTIVE: [
						'2025-01-27 06:00:00', // 1
						'2025-01-27 06:00:00', // 2
						'2025-01-29 10:00:00', // 3 квест
						'2025-01-29 10:00:00', // 4
						'2025-01-30 10:00:00', // 5 квест
						'2025-01-31 10:00:00', // 6
						'2025-01-31 10:00:00', // 7
						'2025-02-01 10:00:00', // 8 квест
						'2025-02-02 10:00:00', // 9
						'2025-02-03 10:00:00', // 10
						'2025-02-03 10:00:00', // 11
						'2025-02-04 10:00:00', // 12 квест
					],

					PRESENTS_INTERVAL: 100,

					COOLDOWN_NAME: 'qrCodeNYAward2025',

					PART_STATUSES: {
						open: 'open',
						notFound: 'notFound',
						opensQuest: 'opensQuest',
						notAvailable: 'notAvailable',
					},

					PRESENT_STATUSES: {
						available: 'available',
						notReceived: 'notReceived',
						received: 'received',
					},

					DEFAULT_QR_PARTS: [
						'notAvailable',
						'notAvailable',
						'opensQuest',
						'notAvailable',
						'opensQuest',
						'notAvailable',
						'notAvailable',
						'notAvailable',
						'opensQuest',
						'notAvailable',
						'opensQuest',
						'notAvailable',
					],

					alreadyHaveThisQrCodeNumTrans: 'fun.seasonEvents.alreadyHaveThisQrCodeNum',
					notAllQrCodeParts: 'fun.seasonEvents.notAllQrCodeParts',
					collectPropTrans: 'fun.seasonEvents.collectPropTrans',

					presents: [
						[
							{ type: 'case', value: { case: 'winterExtra2025' } },
							{ type: 'case', value: { case: 'winterExtra2025' } },
						],
						[{ type: 'xp', value: { amount: 5000 } }],
					],

					quests: [
						{
							name: 'magicRifts',
							maxCounter: 2,
							counter: 0,
							icon: 'snow',
							openDate: '2025-01-29 10:00:00',
							part: 3,
						},
						{
							name: 'mazeBankArena',
							maxCounter: 6,
							counter: 0,
							icon: 'arena',
							openDate: '2025-01-30 10:00:00',
							part: 5,
						},
						{
							name: 'searchPropPart',
							maxCounter: 2,
							counter: 0,
							icon: 'paper',
							openDate: '2025-02-01 10:00:00',
							part: 9,
						},
						{
							name: 'eventTask',
							maxCounter: 8,
							counter: 0,
							icon: 'other',
							openDate: '2025-02-04 10:00:00',
							part: 11,
						},
					],

					positions: [
						{ x: 434.723633, y: 6452.163, z: 28.6767864, rX: 0.0, rY: 0.0, rZ: -0.4226183, rW: 0.9063078 },
						{ x: -2323.8418, y: 392.8179, z: 174.837326, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071068 },
						{ x: -1188.88245, y: -1581.1593, z: 4.********, rX: 0.0, rY: 0.0, rZ: -0.8870108, rW: 0.4617486 },
						{ x: -1107.853, y: -1695.08679, z: 4.********, rX: 0.0, rY: 0.0, rZ: -0.953717, rW: -0.3007058 },
						{ x: -1673.36035, y: -988.7576, z: 7.********, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -32.9352722, y: -8.044654, z: 71.04828, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: -256.3811, y: -279.9747, z: 30.9797478, rX: 0.0, rY: 0.0, rZ: -0.08715576, rW: 0.9961947 },
						{ x: -676.8913, y: -180.8081, z: 38.08366, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: 0.2588191 },
						{ x: -776.710632, y: -30.2762127, z: 39.6185074, rX: 0.0, rY: 0.0, rZ: 0.8660254, rW: -0.5 },
						{ x: -1319.77148, y: 2518.57642, z: 21.54101, rX: 0.0, rY: 0.0, rZ: 0.8433914, rW: 0.5372996 },
						{ x: -452.514221, y: 2975.05249, z: 24.8053169, rX: 0.0, rY: 0.0, rZ: 0.08715574, rW: 0.9961947 },
						{ x: 1696.95508, y: 3460.66064, z: 37.2609329, rX: 0.0, rY: 0.0, rZ: 0.9537169, rW: 0.3007058 },
						{ x: 1532.16943, y: 1729.757, z: 109.990761, rX: 0.0, rY: 0.0, rZ: -0.08715582, rW: 0.9961947 },
						{ x: -1108.09351, y: -1571.65564, z: 4.7165947, rX: 0.0, rY: 0.0, rZ: -0.953717, rW: -0.3007057 },
						{ x: -1203.38208, y: -1397.58423, z: 4.40756464, rX: 0.0, rY: 0.0, rZ: -0.3007058, rW: 0.9537169 },
						{ x: -2214.49072, y: -367.780579, z: 13.4041662, rX: 0.0, rY: 0.0, rZ: 0.9238795, rW: 0.3826835 },
						{ x: -3148.019, y: 1047.63928, z: 21.0049725, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: -2521.672, y: 2313.99243, z: 33.31047, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361942 },
						{ x: -2189.60571, y: 4268.6543, z: 49.1107063, rX: 0.0, rY: 0.0, rZ: -0.5000001, rW: 0.8660254 },
						{ x: -1492.47, y: 4971.386, z: 64.2401657, rX: 0.0, rY: 0.0, rZ: -0.04361942, rW: 0.9990482 },
						{ x: -925.007141, y: 5436.647, z: 37.7224731, rX: 0.0, rY: 0.0, rZ: 0.7071068, rW: -0.7071068 },
						{ x: -741.703, y: 5558.89258, z: 36.7604, rX: 0.0, rY: 0.0, rZ: 0.7071068, rW: -0.7071067 },
						{ x: -677.76355, y: 5843.679, z: 17.4348774, rX: 0.0, rY: 0.0, rZ: -0.9238796, rW: -0.3826834 },
						{ x: -414.048248, y: 6073.501, z: 32.0240555, rX: 0.0, rY: 0.0, rZ: 0.3826834, rW: 0.9238796 },
						{ x: -93.23722, y: 6461.68359, z: 32.0570946, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.3420201 },
						{ x: 2245.8457, y: 5153.05371, z: 57.8349457, rX: 0.0, rY: 0.0, rZ: -0.4617487, rW: 0.8870109 },
						{ x: 2305.23584, y: 4879.43555, z: 42.07759, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 2483.86084, y: 4108.58, z: 38.1812477, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: 1556.175, y: 855.5145, z: 78.0434341, rX: 0.0, rY: 0.0, rZ: 0.2164396, rW: 0.9762961 },
						{ x: 1032.4397, y: 177.045074, z: 81.0035, rX: 0.0, rY: 0.0, rZ: 0.258819, rW: 0.9659258 },
						{ x: 526.6032, y: -146.371567, z: 58.4270935, rX: 0.0, rY: 0.0, rZ: -0.04361942, rW: 0.9990482 },
						{ x: 90.26876, y: -553.5962, z: 32.30098, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: -497.835419, y: -1224.007, z: 22.1873665, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: 0.1305262 },
						{ x: -703.8773, y: -1700.39417, z: 26.9494743, rX: 0.0, rY: 0.0, rZ: 0.9990483, rW: 0.0436194 },
						{ x: 155.370224, y: -3012.70532, z: 6.2545557, rX: 0.0, rY: 0.0, rZ: 0.9990483, rW: 0.0436194 },
						{ x: 774.4767, y: -2467.90649, z: 21.2601318, rX: 0.0, rY: 0.0, rZ: -0.5372997, rW: -0.8433915 },
						{ x: 1184.01978, y: -1407.285, z: 35.4308243, rX: 0.0, rY: 0.0, rZ: 0.7071068, rW: -0.7071068 },
						{ x: 1181.84143, y: -1104.71631, z: 39.6426735, rX: 0.0, rY: 0.0, rZ: 0.5000001, rW: 0.8660253 },
						{ x: 1173.57288, y: -303.1131, z: 69.150444, rX: 0.0, rY: 0.0, rZ: 0.9961948, rW: 0.08715575 },
						{ x: -2721.35425, y: -31.91865, z: 15.94738, rX: 0.0, rY: 0.0, rZ: 0.4226182, rW: 0.9063078 },
						{ x: -2867.66772, y: -13.0166473, z: 7.70699263, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305262 },
						{ x: -3057.042, y: 174.804581, z: 12.0765085, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.0436194 },
						{ x: -2974.10059, y: 378.039, z: 15.69227, rX: 0.0, rY: 0.0, rZ: 0.6755903, rW: -0.7372773 },
						{ x: -2950.912, y: 486.8485, z: 16.11395, rX: 0.0, rY: 0.0, rZ: 0.7372773, rW: 0.6755902 },
						{ x: -3070.64648, y: 783.403259, z: 22.05804, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: -0.4226183 },
						{ x: -3061.49658, y: 509.222015, z: 2.830052, rX: 0.0, rY: 0.0, rZ: 0.5000001, rW: 0.8660253 },
						{ x: -3152.49536, y: 836.5855, z: 3.587579, rX: 0.0, rY: 0.0, rZ: 0.2588191, rW: -0.9659258 },
						{ x: -3261.05444, y: 954.227966, z: 8.704958, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361938 },
						{ x: -3424.187, y: 964.8513, z: 8.738775, rX: 0.0, rY: 0.0, rZ: 0.0, rW: 1.0 },
						{ x: -3146.65527, y: 1140.32166, z: 21.1493263, rX: 0.0, rY: 0.0, rZ: 0.8433915, rW: 0.5372996 },
						{ x: -3183.497, y: 1033.98865, z: 21.2230244, rX: 0.0, rY: 0.0, rZ: 0.2164397, rW: 0.976296 },
						{ x: -3220.34521, y: 1163.64136, z: 7.24712, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: -3102.417, y: 1343.04663, z: 20.7321739, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.258819 },
						{ x: -3067.93237, y: 1783.44824, z: 34.5527039, rX: 0.0, rY: 0.0, rZ: 0.9961948, rW: 0.08715575 },
						{ x: -2817.40015, y: 2182.55566, z: 30.4822674, rX: 0.0, rY: 0.0, rZ: 0.08715577, rW: -0.9961947 },
						{ x: -2565.67114, y: 2310.88281, z: 33.61904, rX: 0.0, rY: 0.0, rZ: -0.04361939, rW: 0.9990482 },
						{ x: -2407.62817, y: 2291.63672, z: 33.4228172, rX: 0.0, rY: 0.0, rZ: 0.8660254, rW: -0.5 },
						{ x: -2210.562, y: 2324.18628, z: 32.63018, rX: 0.0, rY: 0.0, rZ: 0.7372774, rW: -0.6755902 },
						{ x: -1838.20435, y: 2313.342, z: 67.20255, rX: 0.0, rY: 0.0, rZ: 0.0, rW: 1.0 },
						{ x: -1919.06775, y: 2082.92847, z: 138.358, rX: 0.0, rY: 0.0, rZ: -0.04361936, rW: -0.9990483 },
						{ x: -1686.97668, y: 2214.71338, z: 89.32261, rX: 0.0, rY: 0.0, rZ: 0.7372773, rW: 0.6755902 },
						{ x: -1779.82886, y: 1868.34583, z: 151.335037, rX: 0.0, rY: 0.0, rZ: 0.1736483, rW: -0.9848078 },
						{ x: -1916.93555, y: 1782.5293, z: 173.009369, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: -0.5735765 },
						{ x: -2538.63354, y: 1852.99719, z: 166.148438, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: 0.1305262 },
						{ x: -2646.049, y: 1627.37244, z: 129.154953, rX: 0.0, rY: 0.0, rZ: -0.9762959, rW: 0.2164397 },
						{ x: -2747.9436, y: 1364.73035, z: 83.9040451, rX: 0.0, rY: 0.0, rZ: 0.2164396, rW: 0.9762961 },
						{ x: -1936.57373, y: 2352.43115, z: 33.543705, rX: 0.0, rY: 0.0, rZ: 0.9238795, rW: 0.3826834 },
						{ x: -1548.025, y: 2191.097, z: 55.7588158, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: -1499.9917, y: 1525.74146, z: 115.547318, rX: 0.0, rY: 0.0, rZ: -0.9914449, rW: 0.1305263 },
						{ x: -1583.45654, y: 1352.14954, z: 131.113342, rX: 0.0, rY: 0.0, rZ: -0.3007058, rW: 0.953717 },
						{ x: -1703.19019, y: 915.847046, z: 151.59082, rX: 0.0, rY: 0.0, rZ: 0.9961947, rW: -0.08715574 },
						{ x: -1828.29651, y: 782.642, z: 138.6666, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: -0.4226183 },
						{ x: -1997.27625, y: 550.7053, z: 110.537277, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: -0.819152 },
						{ x: -1861.28064, y: 167.429276, z: 80.89983, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: -0.258819 },
						{ x: -1619.92725, y: -59.1532822, z: 59.2360077, rX: 0.0, rY: 0.0, rZ: 0.3007058, rW: -0.953717 },
						{ x: -1663.95081, y: -278.108643, z: 52.1611977, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: 0.4617486 },
						{ x: -1793.03162, y: -367.463654, z: 45.3376961, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: 0.4617486 },
						{ x: -2046.20435, y: -136.965179, z: 27.5364437, rX: 0.0, rY: 0.0, rZ: 0.953717, rW: -0.3007058 },
						{ x: -2068.78735, y: -312.93454, z: 13.61253, rX: 0.0, rY: 0.0, rZ: 0.7372773, rW: 0.6755903 },
						{ x: -1995.542, y: -503.320618, z: 12.1369534, rX: -0.08312191, rY: 0.02620824, rZ: 0.9500878, rW: -0.2995615 },
						{ x: -1827.43091, y: -659.417236, z: 10.8849993, rX: 0.0, rY: 0.0, rZ: 0.4226183, rW: -0.9063078 },
						{ x: -1614.24146, y: -824.209351, z: 10.3247147, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226183 },
						{ x: -1805.86731, y: -1205.65344, z: 14.4651871, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: 0.3420202 },
						{ x: -1640.63, y: -1022.53442, z: 13.4686079, rX: 0.0, rY: 0.0, rZ: 0.4226183, rW: -0.9063078 },
						{ x: -1559.47144, y: -905.6311, z: 10.39202, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.3420202 },
						{ x: -1437.13025, y: -1041.95313, z: 4.77280331, rX: 0.0, rY: 0.0, rZ: 0.9063079, rW: 0.4226183 },
						{ x: -1244.01868, y: -1534.33008, z: 4.720996, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087615 },
						{ x: -1358.48474, y: -1479.36023, z: 5.317178, rX: 0.03460559, rY: -0.0265538, rZ: -0.608182, rW: 0.7925983 },
						{ x: 98.55298, y: 6325.44971, z: 32.1592941, rX: 0.0, rY: 0.0, rZ: 0.2164396, rW: -0.9762961 },
						{ x: 96.23271, y: 6620.27, z: 32.4049835, rX: 0.0, rY: 0.0, rZ: 0.3826835, rW: 0.9238796 },
						{ x: 230.158035, y: 6636.39063, z: 29.9962578, rX: 0.0, rY: 0.0, rZ: -0.6755902, rW: 0.7372773 },
						{ x: -244.090164, y: 6066.52734, z: 33.098793, rX: 0.0, rY: 0.0, rZ: -0.3826835, rW: 0.9238796 },
						{ x: 756.9327, y: 6459.59668, z: 31.865118, rX: 0.0, rY: 0.0, rZ: 0.8660254, rW: 0.5000001 },
						{ x: 1299.58777, y: 6452.88, z: 23.6981354, rX: 0.0, rY: 0.0, rZ: -0.0436193, rW: -0.9990482 },
						{ x: 1474.17859, y: 6383.99756, z: 24.0064335, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305261 },
						{ x: 1985.222, y: 6200.70361, z: 43.1876945, rX: 0.0, rY: 0.0, rZ: 0.5000001, rW: -0.8660253 },
						{ x: 2232.13965, y: 5622.571, z: 55.3097572, rX: 0.0, rY: 0.0, rZ: 0.6087615, rW: 0.7933533 },
						{ x: 2515.21558, y: 5082.755, z: 44.51625, rX: 0.0, rY: 0.0, rZ: -0.08715563, rW: -0.9961947 },
						{ x: 2315.67261, y: 5218.948, z: 60.758, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.3420201 },
						{ x: 1959.4425, y: 5186.71826, z: 48.54001, rX: 0.0, rY: 0.0, rZ: 0.6755902, rW: 0.7372774 },
						{ x: 1987.59155, y: 5027.26855, z: 41.33807, rX: 0.0, rY: 0.0, rZ: -0.08715563, rW: -0.9961947 },
						{ x: 2257.87573, y: 4958.393, z: 41.99598, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.3420201 },
						{ x: 2328.01831, y: 4852.793, z: 42.3830528, rX: 0.0, rY: 0.0, rZ: 0.9238796, rW: -0.3826833 },
						{ x: 2439.11377, y: 4999.563, z: 46.3035545, rX: -0.04258544, rY: 0.009440961, rZ: 0.9753668, rW: -0.2162336 },
						{ x: 2582.86914, y: 4684.15039, z: 34.54019, rX: 0.0, rY: 0.0, rZ: -0.9238794, rW: -0.3826835 },
						{ x: 2680.87183, y: 4858.247, z: 32.8625145, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226183 },
						{ x: 1698.90918, y: 4918.274, z: 42.24503, rX: 0.0, rY: 0.0, rZ: 0.5000001, rW: -0.8660253 },
						{ x: 1698.77344, y: 4619.442, z: 43.513382, rX: 0.0, rY: 0.0, rZ: 0.4226183, rW: 0.9063079 },
						{ x: 1922.2605, y: 4633.81543, z: 40.98387, rX: 0.0, rY: 0.0, rZ: -0.04361929, rW: -0.9990483 },
						{ x: 2150.024, y: 4773.476, z: 41.5576553, rX: 0.0, rY: 0.0, rZ: 0.8433915, rW: -0.5372995 },
						{ x: 2428.81128, y: 4626.90332, z: 32.5857964, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.3420201 },
						{ x: 2822.55444, y: 4994.20654, z: 32.6306534, rX: 0.0, rY: 0.0, rZ: -0.9659259, rW: -0.2588192 },
						{ x: 3249.214, y: 5150.278, z: 20.436182, rX: 0.04160055, rY: 0.01311661, rZ: -0.9528093, rW: -0.3004197 },
						{ x: 3496.508, y: 4599.862, z: 55.6354866, rX: 0.0, rY: 0.0, rZ: -0.9848078, rW: -0.1736483 },
						{ x: 3831.91553, y: 4440.232, z: 2.68311, rX: 0.0, rY: 0.0, rZ: -0.9961948, rW: -0.08715582 },
						{ x: 2873.43652, y: 4866.606, z: 62.80431, rX: 0.0, rY: 0.0, rZ: -0.9659259, rW: -0.2588192 },
						{ x: 2874.85767, y: 4504.896, z: 48.4338226, rX: 0.0, rY: 0.0, rZ: -0.7660444, rW: -0.6427877 },
						{ x: 3051.77075, y: 4388.149, z: 62.9917374, rX: 0.0, rY: 0.0, rZ: -0.04361933, rW: -0.9990482 },
						{ x: 2921.12622, y: 4258.13672, z: 51.60552, rX: 0.0, rY: 0.0, rZ: 0.3420203, rW: -0.9396926 },
						{ x: 2985.50781, y: 3867.29761, z: 51.5637665, rX: 0.0, rY: 0.0, rZ: -0.9961947, rW: 0.08715562 },
						{ x: 2980.80249, y: 3474.308, z: 71.86616, rX: 0.0, rY: 0.0, rZ: 0.6427878, rW: -0.7660443 },
						{ x: 3256.93384, y: 3586.97974, z: 59.4979362, rX: 0.0, rY: 0.0, rZ: 0.2588192, rW: -0.9659259 },
						{ x: 3428.721, y: 3772.409, z: 31.060236, rX: 0.0, rY: 0.0, rZ: -0.8191522, rW: 0.5735763 },
						{ x: 3600.81177, y: 3734.6272, z: 36.6120148, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: 0.04361925 },
						{ x: 3448.1875, y: 3661.13354, z: 42.93014, rX: 0.0, rY: 0.0, rZ: -0.08715563, rW: -0.9961947 },
						{ x: 3640.199, y: 3744.03076, z: 29.0079651, rX: 0.0, rY: 0.0, rZ: -0.9659259, rW: 0.2588189 },
						{ x: 2710.39233, y: 3450.257, z: 56.92966, rX: 0.0, rY: 0.0, rZ: -0.1736481, rW: -0.9848078 },
						{ x: 2664.62646, y: 3274.53638, z: 55.8205643, rX: 0.0, rY: 0.0, rZ: 0.2164395, rW: 0.9762961 },
						{ x: 2629.07837, y: 2933.39063, z: 40.8739967, rX: 0.0, rY: 0.0, rZ: -0.8660254, rW: -0.5000001 },
						{ x: 2711.17969, y: 2777.94287, z: 38.24864, rX: 0.0, rY: 0.0, rZ: -0.9659259, rW: -0.2588192 },
						{ x: 2951.68262, y: 2743.023, z: 43.7455177, rX: 0.0, rY: 0.0, rZ: -0.7933535, rW: 0.6087613 },
						{ x: 2565.24927, y: 2727.55859, z: 43.8338661, rX: 0.0, rY: 0.0, rZ: -0.7372774, rW: 0.6755901 },
						{ x: 2527.93677, y: 2640.34155, z: 38.56493, rX: 0.0, rY: 0.0, rZ: -0.04361929, rW: -0.9990482 },
						{ x: 2565.74536, y: 2180.96973, z: 19.5490456, rX: 0.0, rY: 0.0, rZ: -0.8660255, rW: 0.4999999 },
						{ x: 2560.04321, y: 1651.5177, z: 23.37764, rX: 0.0, rY: 0.0, rZ: -0.9848077, rW: -0.1736483 },
						{ x: 2780.7688, y: 1422.98, z: 25.492384, rX: 0.0, rY: 0.0, rZ: -0.1305261, rW: -0.9914449 },
						{ x: 2721.73975, y: 1648.9873, z: 24.8310146, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: -0.04361951 },
						{ x: 2675.83521, y: 1510.87878, z: 24.7541256, rX: 0.0, rY: 0.0, rZ: 0.08715577, rW: -0.9961947 },
						{ x: 2656.45923, y: 1352.827, z: 24.6159115, rX: 0.0, rY: 0.0, rZ: 0.5735766, rW: -0.819152 },
						{ x: 2430.69775, y: 1524.38318, z: 35.4616547, rX: 0.0, rY: 0.0, rZ: 0.3420203, rW: -0.9396926 },
						{ x: 2393.90552, y: 1272.29163, z: 63.1211281, rX: 0.0, rY: 0.0, rZ: 0.1305262, rW: -0.9914448 },
						{ x: 2202.12842, y: 1651.2561, z: 84.52494, rX: 0.0, rY: 0.0, rZ: -0.258819, rW: -0.9659259 },
						{ x: 2170.18164, y: 1934.48792, z: 99.50322, rX: 0.0, rY: 0.0, rZ: -0.9961947, rW: -0.08715581 },
						{ x: 2096.73584, y: 2496.278, z: 91.4104462, rX: 0.0, rY: 0.0, rZ: -0.7660444, rW: -0.6427877 },
						{ x: 2344.71631, y: 1176.649, z: 60.5282974, rX: 0.0, rY: 0.0, rZ: -0.3420204, rW: 0.9396926 },
						{ x: 2663.67847, y: 880.1694, z: 76.03493, rX: 0.03573092, rY: -0.02501905, rZ: -0.8183725, rW: 0.5730304 },
						{ x: 2655.79443, y: 552.577759, z: 96.17319, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361945 },
						{ x: 2599.55151, y: 275.9484, z: 100.211708, rX: 0.0, rY: 0.0, rZ: -0.9961947, rW: 0.08715562 },
						{ x: 2589.36133, y: 484.388336, z: 108.822708, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: -0.04361951 },
						{ x: 2578.42944, y: 283.2924, z: 108.80632, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: -0.04361951 },
						{ x: 2428.1438, y: -298.436432, z: 81.9652557, rX: 0.0, rY: 0.0, rZ: 0.04361939, rW: -0.9990483 },
						{ x: 2561.81616, y: -599.5981, z: 65.68105, rX: 0.0, rY: 0.0, rZ: -0.08715576, rW: 0.9961948 },
						{ x: 2576.19629, y: -296.2477, z: 93.60965, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071069 },
						{ x: 2305.60449, y: -709.587463, z: 65.36703, rX: 0.0, rY: 0.0, rZ: -0.9848077, rW: -0.1736483 },
						{ x: 2048.29321, y: -872.6935, z: 81.25552, rX: 0.0, rY: 0.0, rZ: -0.9914449, rW: -0.1305263 },
						{ x: 2280.89575, y: -544.9126, z: 96.93907, rX: 0.0, rY: 0.0, rZ: -0.8433915, rW: 0.5372995 },
						{ x: 1848.71167, y: -759.284668, z: 82.56997, rX: 0.0, rY: 0.0, rZ: -0.7933533, rW: -0.6087615 },
						{ x: 1352.55237, y: -1072.53, z: 51.24622, rX: 0.0, rY: 0.0, rZ: -0.4999999, rW: -0.8660255 },
						{ x: 1355.95789, y: -949.133545, z: 57.1160622, rX: 0.0, rY: 0.0, rZ: -0.9659259, rW: 0.2588189 },
						{ x: 1922.43811, y: -974.777039, z: 79.1381454, rX: 0.0, rY: 0.0, rZ: -0.5000002, rW: 0.8660254 },
						{ x: 1785.13879, y: -1325.93567, z: 96.56259, rX: 0.0, rY: 0.0, rZ: -0.1736483, rW: 0.9848078 },
						{ x: 1674.38782, y: -1456.12183, z: 112.401329, rX: -0.01513443, rY: -0.08583164, rZ: -0.1729873, rW: -0.9810603 },
						{ x: 1759.797, y: -1588.9845, z: 118.40313, rX: 0.0, rY: 0.0, rZ: -0.6427876, rW: -0.7660445 },
						{ x: 1680.34717, y: -1681.00989, z: 112.835793, rX: 0.0, rY: 0.0, rZ: -0.6427875, rW: -0.7660445 },
						{ x: 1655.361, y: -1890.58411, z: 109.543312, rX: 0.0, rY: 0.0, rZ: -0.5735766, rW: 0.819152 },
						{ x: 1600.04822, y: -1956.9397, z: 100.066, rX: 0.0, rY: 0.0, rZ: -0.8433915, rW: 0.5372995 },
						{ x: 1436.97107, y: -1656.61267, z: 63.9223976, rX: 0.0, rY: 0.0, rZ: -0.5372996, rW: -0.8433915 },
						{ x: 1510.64734, y: -2233.21484, z: 78.4046555, rX: 0.0, rY: 0.0, rZ: -0.2588192, rW: 0.9659259 },
						{ x: 1504.34583, y: -2514.24072, z: 56.74315, rX: 0.0, rY: 0.0, rZ: -0.6087613, rW: -0.7933534 },
						{ x: 1208.88806, y: -2582.01758, z: 39.36306, rX: 0.0, rY: 0.0, rZ: 0.04361945, rW: -0.9990482 },
						{ x: 1661.2395, y: -54.4865723, z: 168.664856, rX: 0.0, rY: 0.0, rZ: -0.7372773, rW: -0.6755902 },
						{ x: 1662.54944, y: 37.24646, z: 172.288467, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361948 },
						{ x: 1904.78845, y: 589.2137, z: 178.61441, rX: 0.0, rY: 0.0, rZ: -0.258819, rW: -0.9659259 },
						{ x: 1813.549, y: 1267.75244, z: 144.625748, rX: 0.0, rY: 0.0, rZ: -0.7933534, rW: -0.6087615 },
						{ x: 198.130875, y: 3347.567, z: 41.4020348, rX: 0.0, rY: 0.0, rZ: -0.3007059, rW: 0.953717 },
						{ x: 352.144562, y: 3396.99731, z: 37.0512276, rX: 0.0, rY: 0.0, rZ: -0.7933534, rW: 0.6087614 },
						{ x: 463.0757, y: 3554.45264, z: 34.1551743, rX: 0.0, rY: 0.0, rZ: -0.6427877, rW: 0.7660444 },
						{ x: 661.45636, y: 3506.9104, z: 34.84691, rX: 0.0, rY: 0.0, rZ: -0.7372774, rW: 0.6755902 },
						{ x: 901.0046, y: 3578.419, z: 34.0465126, rX: 0.0, rY: 0.0, rZ: -0.04361948, rW: 0.9990482 },
						{ x: 1126.909, y: 3551.24048, z: 35.9792252, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: 1382.20166, y: 3622.61621, z: 35.5382, rX: 0.0, rY: 0.0, rZ: 0.5735763, rW: 0.8191521 },
						{ x: 1517.79138, y: 3789.72437, z: 34.20528, rX: 0.0, rY: 0.0, rZ: 0.4226182, rW: 0.9063078 },
						{ x: 1723.078, y: 3885.618, z: 35.8127823, rX: -0.0434534, rY: 0.003801686, rZ: 0.9952466, rW: 0.08707293 },
						{ x: 2014.12024, y: 3934.55029, z: 32.57488, rX: 0.0, rY: 0.0, rZ: 0.953717, rW: -0.3007057 },
						{ x: 1773.058, y: 3825.1377, z: 35.0975952, rX: 0.0, rY: 0.0, rZ: 0.8660255, rW: -0.4999998 },
						{ x: 1662.47913, y: 3745.65625, z: 35.4730263, rX: 0.0, rY: 0.0, rZ: -0.3007059, rW: 0.9537169 },
						{ x: 1614.54456, y: 3566.03052, z: 36.05922, rX: 0.0, rY: 0.0, rZ: 0.8660256, rW: -0.4999998 },
						{ x: 1925.64258, y: 3730.57422, z: 33.2956276, rX: 0.0, rY: 0.0, rZ: 0.4999999, rW: 0.8660255 },
						{ x: 2701.502, y: 4313.019, z: 46.44176, rX: 0.0, rY: 0.0, rZ: -0.3826836, rW: 0.9238794 },
						{ x: 2659.146, y: 3929.281, z: 42.76023, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: -0.7071068 },
						{ x: 233.884476, y: 3220.06763, z: 43.95565, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305261 },
						{ x: 334.181244, y: 2842.76465, z: 44.2275276, rX: 0.0, rY: 0.0, rZ: 0.4617485, rW: 0.8870109 },
						{ x: 176.425461, y: 2705.13965, z: 43.15375, rX: 0.0, rY: 0.0, rZ: -0.1736482, rW: 0.9848077 },
						{ x: 221.398422, y: 2582.693, z: 46.6484451, rX: 0.0, rY: 0.0, rZ: -0.7660445, rW: 0.6427875 },
						{ x: 401.5314, y: 2631.092, z: 45.14226, rX: 0.0, rY: 0.0, rZ: 0.8660255, rW: -0.4999998 },
						{ x: 579.1829, y: 2660.13965, z: 42.57167, rX: 0.0, rY: 0.0, rZ: -0.7660445, rW: 0.6427876 },
						{ x: 634.290466, y: 2779.25269, z: 43.16571, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361963 },
						{ x: 1046.85913, y: 2653.93213, z: 40.052845, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: 1217.11487, y: 2741.44971, z: 38.602108, rX: 0.0, rY: 0.0, rZ: -0.6755901, rW: -0.7372774 },
						{ x: 1570.47327, y: 2848.42456, z: 47.7999878, rX: 0.0, rY: 0.0, rZ: 0.8660255, rW: -0.4999998 },
						{ x: 2114.86182, y: 2922.30347, z: 48.6325836, rX: -0.04295671, rY: -0.007574433, rZ: 0.9838704, rW: 0.1734831 },
						{ x: 1879.942, y: 2727.022, z: 46.354557, rX: 0.0, rY: 0.0, rZ: 0.9762959, rW: 0.2164398 },
						{ x: 1832.97913, y: 2538.61548, z: 46.7055321, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: 185.009842, y: 2445.954, z: 59.81278, rX: 0.0, rY: 0.0, rZ: -0.04361922, rW: -0.9990482 },
						{ x: -47.1714325, y: 2194.75757, z: 127.930916, rX: 0.0, rY: 0.0, rZ: 0.7660443, rW: 0.6427878 },
						{ x: -130.721832, y: 1921.00049, z: 197.950882, rX: 0.0, rY: 0.0, rZ: 0.7071066, rW: 0.7071069 },
						{ x: 206.5313, y: 1253.41418, z: 226.373047, rX: 0.0, rY: 0.0, rZ: 0.67559, rW: 0.7372775 },
						{ x: 243.578323, y: 1101.83875, z: 220.526779, rX: 0.0, rY: 0.0, rZ: 0.7933534, rW: -0.6087613 },
						{ x: -152.7598, y: 1442.47461, z: 293.404755, rX: 0.0, rY: 0.0, rZ: 0.3420198, rW: 0.9396928 },
						{ x: -178.973129, y: 1305.90076, z: 305.7422, rX: 0.0, rY: 0.0, rZ: 0.1305264, rW: -0.9914448 },
						{ x: -418.0484, y: 1151.69336, z: 326.9244, rX: 0.0, rY: 0.0, rZ: 0.300706, rW: -0.9537169 },
						{ x: -365.1335, y: 1264.53455, z: 330.763, rX: 0.0, rY: 0.0, rZ: 0.9396926, rW: 0.3420204 },
						{ x: -264.119263, y: 1568.56848, z: 337.270264, rX: -0.16043, rY: -0.06645231, rZ: 0.9098437, rW: 0.3768699 },
						{ x: -443.56604, y: 1599.15979, z: 358.64505, rX: 0.0, rY: 0.0, rZ: -0.3007056, rW: -0.9537171 },
						{ x: 2187.26831, y: 3721.866, z: 35.03279, rX: 0.0, rY: 0.0, rZ: 0.8660255, rW: -0.4999998 },
						{ x: 2454.90845, y: 3850.68457, z: 39.3872948, rX: 0.0, rY: 0.0, rZ: 0.8433916, rW: -0.5372995 },
						{ x: 2530.0625, y: 4113.103, z: 38.96177, rX: 0.0, rY: 0.0, rZ: 0.9762961, rW: -0.2164395 },
						{ x: 1440.202, y: 4372.11768, z: 44.59105, rX: 0.0, rY: 0.0, rZ: 0.9238796, rW: -0.3826832 },
						{ x: 1298.93188, y: 4343.1626, z: 42.33395, rX: 0.0, rY: 0.0, rZ: -0.5735766, rW: 0.819152 },
						{ x: 920.1052, y: 4432.8125, z: 50.703125, rX: 0.0, rY: 0.0, rZ: -0.1305261, rW: -0.9914449 },
						{ x: 819.2839, y: 4218.982, z: 51.8644676, rX: 0.0, rY: 0.0, rZ: -0.2164395, rW: -0.9762961 },
						{ x: 715.1674, y: 4100.56445, z: 36.19841, rX: 0.0, rY: 0.0, rZ: -0.04361924, rW: -0.9990482 },
						{ x: 355.500519, y: 4425.29053, z: 64.33219, rX: 0.007574437, rY: -0.04295672, rZ: -0.1734831, rW: 0.9838704 },
						{ x: -63.15244, y: 4572.53564, z: 123.643967, rX: 0.0, rY: 0.0, rZ: -0.9537171, rW: 0.3007056 },
						{ x: -259.960663, y: 4727.93652, z: 137.279663, rX: 0.0, rY: 0.0, rZ: -0.9063078, rW: -0.4226184 },
						{ x: -604.127, y: 5353.20752, z: 70.9449844, rX: 0.0, rY: 0.0, rZ: -0.04361952, rW: 0.9990482 },
						{ x: -464.718536, y: 5360.97656, z: 81.50229, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: 0.04361924 },
						{ x: -840.9944, y: 5398.959, z: 35.229496, rX: 0.0, rY: 0.0, rZ: -0.976296, rW: -0.2164398 },
						{ x: -1057.66382, y: 4916.08057, z: 212.120239, rX: 0.0, rY: 0.0, rZ: -0.7660446, rW: 0.6427875 },
						{ x: -1162.29663, y: 4914.904, z: 221.902649, rX: 0.0, rY: 0.0, rZ: -0.9396926, rW: -0.3420204 },
						{ x: -1356.091, y: 4845.601, z: 138.453415, rX: 0.0, rY: 0.0, rZ: -0.9659259, rW: 0.2588189 },
						{ x: -1605.29431, y: 5201.60742, z: 4.71082449, rX: 0.0, rY: 0.0, rZ: 0.5372995, rW: 0.8433916 },
						{ x: -1983.66382, y: 4517.334, z: 20.32726, rX: 0.0, rY: 0.0, rZ: -0.9396926, rW: -0.3420204 },
						{ x: -1418.67639, y: 4309.57373, z: 4.255692, rX: 0.0, rY: 0.0, rZ: -0.9396927, rW: 0.34202 },
						{ x: -775.656433, y: 4410.616, z: 17.3340588, rX: 0.0, rY: 0.0, rZ: -0.5735766, rW: 0.819152 },
						{ x: -496.9129, y: 4337.16553, z: 70.25102, rX: 0.0, rY: 0.0, rZ: 0.6087613, rW: 0.7933534 },
						{ x: -204.819473, y: 3651.378, z: 52.68403, rX: 0.0, rY: 0.0, rZ: 0.4999999, rW: 0.8660254 },
						{ x: 34.3609657, y: 3616.67529, z: 40.44073, rX: 0.0, rY: 0.0, rZ: 0.7660443, rW: 0.6427878 },
						{ x: 103.430428, y: 3767.05835, z: 39.2970619, rX: 0.0, rY: 0.0, rZ: -0.8660254, rW: 0.4999999 },
						{ x: 111.869896, y: 3371.16479, z: 35.2115326, rX: 0.0, rY: 0.0, rZ: -0.5372998, rW: 0.8433914 },
						{ x: -1461.90271, y: 2666.55542, z: 4.617156, rX: 0.01843436, rY: -0.03953259, rZ: -0.4222162, rW: 0.9054451 },
						{ x: -1664.253, y: 3049.33765, z: 32.6294136, rX: 0.0, rY: 0.0, rZ: -0.9914448, rW: -0.1305264 },
						{ x: -2622.98486, y: 2915.384, z: 6.74508333, rX: 0.0, rY: 0.0, rZ: -0.6087615, rW: 0.7933533 },
						{ x: -2227.33545, y: 3481.279, z: 30.968092, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: -0.04361946 },
						{ x: -2639.34375, y: 3281.397, z: 33.8205681, rX: 0.0, rY: 0.0, rZ: -0.8660254, rW: -0.5000001 },
						{ x: -2388.35547, y: 2946.11328, z: 33.2692337, rX: 0.0, rY: 0.0, rZ: -0.9063079, rW: 0.4226181 },
						{ x: -1918.92224, y: 2822.06885, z: 33.38858, rX: 0.0, rY: 0.0, rZ: 0.6755901, rW: 0.7372775 },
						{ x: -1772.675, y: 3140.94263, z: 33.7140923, rX: 0.0, rY: 0.0, rZ: 0.2588188, rW: 0.9659259 },
						{ x: -1954.74585, y: 3363.266, z: 33.6418571, rX: 0.0, rY: 0.0, rZ: -0.8433914, rW: -0.5372998 },
						{ x: 1004.29504, y: 487.0019, z: 99.05994, rX: 0.0, rY: 0.0, rZ: -0.04361925, rW: -0.9990482 },
						{ x: 1417.41577, y: 1110.07129, z: 114.9594, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: 0.04361924 },
						{ x: 1496.09155, y: 1125.45349, z: 115.0828, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: -0.04361954 },
						{ x: 1298.3717, y: 1451.46436, z: 100.301285, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: 0.04361924 },
						{ x: 1220.84656, y: 1905.27759, z: 78.587616, rX: 0.04098882, rY: 0.01491871, rZ: -0.9387982, rW: -0.3416948 },
						{ x: 1440.564, y: 1992.78015, z: 120.95388, rX: 0.0, rY: 0.0, rZ: -0.8433914, rW: -0.5372998 },
						{ x: 1557.10315, y: 2177.915, z: 79.28021, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: -0.04361946 },
						{ x: 1151.11609, y: 2388.46558, z: 58.5763, rX: 0.0, rY: 0.0, rZ: 0.4617485, rW: 0.8870109 },
						{ x: 861.750061, y: 2424.91528, z: 54.5541, rX: 0.0, rY: 0.0, rZ: 0.08715583, rW: -0.9961947 },
						{ x: 1055.51648, y: 2248.38574, z: 45.9386063, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: -0.04361954 },
						{ x: 800.7728, y: 2175.56348, z: 52.6614647, rX: 0.0, rY: 0.0, rZ: -0.8433914, rW: -0.5372998 },
						{ x: 1278.70935, y: -3258.28979, z: 6.43372059, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: 0.04361919 },
						{ x: 1094.61, y: -3312.10083, z: 6.703167, rX: 0.0, rY: 0.0, rZ: -0.04361924, rW: -0.9990482 },
						{ x: 750.952332, y: -3198.849, z: 6.49632454, rX: 0.0, rY: 0.0, rZ: 0.04361954, rW: -0.9990482 },
						{ x: 1051.73682, y: -3035.10547, z: 6.416258, rX: 0.0, rY: 0.0, rZ: -0.6755904, rW: 0.7372772 },
						{ x: 1220.63037, y: -3034.67554, z: 6.252434, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: 0.04361921 },
						{ x: 798.107666, y: -2982.24438, z: 6.18942547, rX: 0.0, rY: 0.0, rZ: 0.04361954, rW: -0.9990482 },
						{ x: 706.049744, y: -2658.71069, z: 6.68339729, rX: 0.0, rY: 0.0, rZ: -0.9961947, rW: -0.08715597 },
						{ x: 580.065247, y: -2646.73242, z: 6.747767, rX: 0.0, rY: 0.0, rZ: 0.04361966, rW: -0.9990482 },
						{ x: 460.794128, y: -2422.15259, z: 6.1220417, rX: 0.0, rY: 0.0, rZ: 0.1305265, rW: -0.9914449 },
						{ x: 453.959778, y: -2734.6582, z: 6.62111235, rX: 0.0, rY: 0.0, rZ: 0.04361954, rW: -0.9990482 },
						{ x: 577.070862, y: -2854.144, z: 6.35568, rX: 0.0, rY: 0.0, rZ: 0.4226184, rW: -0.9063077 },
						{ x: 482.540771, y: -2992.88159, z: 6.555678, rX: 0.0, rY: 0.0, rZ: -0.04361919, rW: -0.9990482 },
						{ x: 678.0788, y: -3015.70361, z: 6.90745068, rX: 0.0, rY: 0.0, rZ: -0.7071069, rW: 0.7071066 },
						{ x: 445.618, y: -3100.41968, z: 6.204502, rX: 0.0, rY: 0.0, rZ: 0.7372774, rW: -0.6755901 },
						{ x: 509.9943, y: -3271.85742, z: 7.09535646, rX: 0.0, rY: 0.0, rZ: -0.7071069, rW: 0.7071066 },
						{ x: 294.6091, y: -3310.6167, z: 5.87296343, rX: 0.0, rY: 0.0, rZ: -0.7071066, rW: -0.7071069 },
						{ x: 195.329849, y: -3182.166, z: 6.47201, rX: 0.0, rY: 0.0, rZ: 0.04361954, rW: -0.9990482 },
						{ x: 118.982414, y: -3029.60278, z: 6.642234, rX: 0.0, rY: 0.0, rZ: 0.04361929, rW: 0.9990482 },
						{ x: 182.526321, y: -2874.03149, z: 6.685324, rX: 0.0, rY: 0.0, rZ: 0.04361959, rW: -0.9990482 },
						{ x: 312.702362, y: -2951.30151, z: 6.49867, rX: 0.0, rY: 0.0, rZ: -0.7071066, rW: -0.7071069 },
						{ x: 241.721954, y: -2728.57935, z: 6.73860025, rX: 0.0, rY: 0.0, rZ: 0.5000002, rW: -0.8660253 },
						{ x: 376.40213, y: -2573.95166, z: 6.76372957, rX: 0.0, rY: 0.0, rZ: 0.976296, rW: 0.2164398 },
						{ x: 158.29747, y: -2654.6355, z: 6.414049, rX: 0.0, rY: 0.0, rZ: 0.04361963, rW: -0.9990482 },
						{ x: 19.60272, y: -2747.029, z: 6.564461, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071066 },
						{ x: -106.27729, y: -2709.64453, z: 6.421339, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071066 },
						{ x: -227.383881, y: -2668.06836, z: 6.68949461, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071066 },
						{ x: -320.2388, y: -2719.62646, z: 6.03385258, rX: 0.0, rY: 0.0, rZ: 0.9238796, rW: -0.3826832 },
						{ x: -393.026764, y: -2760.95532, z: 7.18465376, rX: 0.0, rY: 0.0, rZ: -0.3826832, rW: -0.9238796 },
						{ x: -564.1838, y: -2826.85742, z: 6.99924755, rX: 0.0, rY: 0.0, rZ: 0.8433916, rW: -0.5372995 },
						{ x: -396.110077, y: -2676.38135, z: 6.79955673, rX: 0.0, rY: 0.0, rZ: 0.9238797, rW: -0.3826832 },
						{ x: -477.6708, y: -2764.52173, z: 6.92456, rX: 0.0, rY: 0.0, rZ: 0.4226185, rW: -0.9063078 },
						{ x: -433.300781, y: -2434.444, z: 7.028283, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226185 },
						{ x: -211.163, y: -2596.37842, z: 6.41997576, rX: -0.003395292, rY: 0.0008284737, rZ: 0.7062288, rW: -0.707975 },
						{ x: -277.527039, y: -2423.802, z: 7.151018, rX: 0.0, rY: 0.0, rZ: 0.2588188, rW: 0.9659259 },
						{ x: -160.196182, y: -2464.25269, z: 6.617047, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226185 },
						{ x: -115.459732, y: -2515.04785, z: 6.7970686, rX: 0.0, rY: 0.0, rZ: 0.953717, rW: -0.3007056 },
						{ x: 98.70442, y: -2494.65161, z: 6.79517365, rX: 0.0, rY: 0.0, rZ: -0.3007055, rW: -0.9537171 },
						{ x: 110.089371, y: -2574.35645, z: 7.30684233, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361968 },
						{ x: -23.2688942, y: -2475.88452, z: 6.1661005, rX: 0.0, rY: 0.0, rZ: 0.8660253, rW: 0.5000002 },
						{ x: 200.848145, y: -2462.912, z: 6.587246, rX: 0.0, rY: 0.0, rZ: -0.7071066, rW: -0.7071071 },
						{ x: 326.6069, y: -2431.3938, z: 6.173349, rX: 0.0, rY: 0.0, rZ: -0.1736479, rW: -0.9848078 },
						{ x: 581.4188, y: -2289.72583, z: 6.77543068, rX: 0.0, rY: 0.0, rZ: -0.7372772, rW: -0.6755905 },
						{ x: 587.158264, y: -2070.13135, z: 16.4251175, rX: 0.0, rY: 0.0, rZ: -0.04361915, rW: -0.9990482 },
						{ x: 398.209839, y: -2227.66553, z: 6.391653, rX: 0.0, rY: 0.0, rZ: 0.9990483, rW: -0.04361922 },
						{ x: 217.592621, y: -2152.67944, z: 11.4454689, rX: 0.0, rY: 0.0, rZ: 0.8433915, rW: -0.5372994 },
						{ x: 119.716278, y: -2104.43921, z: 12.3520775, rX: 0.0, rY: 0.0, rZ: 0.8191522, rW: -0.5735763 },
						{ x: 142.976135, y: -2265.22827, z: 6.09747934, rX: 0.0, rY: 0.0, rZ: -0.7071066, rW: -0.7071071 },
						{ x: 122.897652, y: -2179.31738, z: 6.42339134, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071066 },
						{ x: 186.7533, y: -2078.07422, z: 18.3742523, rX: 0.0, rY: 0.0, rZ: 0.9762961, rW: -0.2164394 },
						{ x: 17.8378925, y: -2049.35376, z: 9.784195, rX: 0.0, rY: 0.0, rZ: 0.9990483, rW: -0.04361922 },
						{ x: 5.317737, y: -2154.10571, z: 10.8898411, rX: 0.0, rY: 0.0, rZ: 0.8660256, rW: -0.4999998 },
						{ x: -57.2406921, y: -2255.35669, z: 8.231559, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071066 },
						{ x: -147.155518, y: -2233.53662, z: 8.197321, rX: 0.0, rY: 0.0, rZ: 0.04361963, rW: -0.9990482 },
						{ x: -238.391464, y: -2211.507, z: 11.4131136, rX: 0.0, rY: 0.0, rZ: 0.7372775, rW: -0.67559 },
						{ x: -301.51474, y: -2197.12378, z: 11.0565968, rX: 0.0, rY: 0.0, rZ: 0.953717, rW: -0.3007056 },
						{ x: -435.8952, y: -2187.03955, z: 10.6510582, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071066 },
						{ x: -513.3651, y: -2224.45972, z: 7.188898, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.34202 },
						{ x: -585.3672, y: -2305.713, z: 14.2623291, rX: 0.0, rY: 0.0, rZ: -0.9063077, rW: -0.4226185 },
						{ x: -601.0978, y: -2371.71558, z: 14.1488285, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: -0.2588189 },
						{ x: -697.467041, y: -2445.781, z: 14.3792439, rX: 0.0, rY: 0.0, rZ: 0.5000002, rW: -0.8660253 },
						{ x: -741.631165, y: -2530.35425, z: 14.6386843, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: -0.2588189 },
						{ x: -788.362854, y: -2627.65479, z: 14.4085245, rX: 0.0, rY: 0.0, rZ: 0.5000002, rW: -0.8660253 },
						{ x: -808.930237, y: -2853.88257, z: 14.77165, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: -0.2588189 },
						{ x: -851.146, y: -2925.151, z: 14.3253622, rX: 0.0, rY: 0.0, rZ: 0.5000002, rW: -0.8660253 },
						{ x: -1004.57544, y: -3034.169, z: 15.1286631, rX: 0.0, rY: 0.0, rZ: 0.3826837, rW: -0.9238795 },
						{ x: -910.983948, y: -3020.10449, z: 14.6591482, rX: 0.0, rY: 0.0, rZ: -0.2588188, rW: -0.965926 },
						{ x: -899.0315, y: -2784.54468, z: 14.82109, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: -0.2588189 },
						{ x: -1132.8949, y: -3508.69482, z: 14.4798279, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: -0.2588188 },
						{ x: -1248.94263, y: -3411.56543, z: 14.9579887, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.3420199 },
						{ x: -1355.15076, y: -3318.36816, z: 14.8552771, rX: 0.0, rY: 0.0, rZ: 0.8660253, rW: 0.5000002 },
						{ x: -1443.332, y: -3296.71216, z: 14.658287, rX: 0.0, rY: 0.0, rZ: 0.5000002, rW: -0.8660253 },
						{ x: -1549.50244, y: -3233.65015, z: 14.7702951, rX: 0.0, rY: 0.0, rZ: -0.8660253, rW: -0.5000003 },
						{ x: -1698.65491, y: -3169.22021, z: 14.9875383, rX: 0.0, rY: 0.0, rZ: 0.5000002, rW: -0.8660253 },
						{ x: -1964.96448, y: -3036.03467, z: 14.6965885, rX: 0.0, rY: 0.0, rZ: 0.5372999, rW: -0.8433914 },
						{ x: -1799.44531, y: -2756.10278, z: 14.6019192, rX: 0.0, rY: 0.0, rZ: 0.8660253, rW: 0.5000002 },
						{ x: -1156.1106, y: -2790.19434, z: 15.3921385, rX: 0.0, rY: 0.0, rZ: 0.2588188, rW: 0.9659259 },
						{ x: -1054.65247, y: -2738.96484, z: 15.48978, rX: 0.0, rY: 0.0, rZ: 0.9762961, rW: -0.2164393 },
						{ x: -1177.49329, y: -2752.57275, z: 14.85997, rX: 0.0, rY: 0.0, rZ: 0.965926, rW: -0.2588188 },
						{ x: -1242.49658, y: -2681.52051, z: 14.9266577, rX: 0.0, rY: 0.0, rZ: -0.5000002, rW: 0.8660253 },
						{ x: -1320.2334, y: -2623.60962, z: 14.61232, rX: 0.0, rY: 0.0, rZ: 0.6427875, rW: 0.7660447 },
						{ x: -1120.28943, y: -2587.20386, z: 14.6600666, rX: 0.0, rY: 0.0, rZ: 0.2588188, rW: 0.9659259 },
						{ x: -1197.38379, y: -2445.23975, z: 15.1078634, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: -0.2588188 },
						{ x: -1289.98657, y: -2322.87915, z: 14.9166613, rX: 0.0, rY: 0.0, rZ: 0.2588189, rW: 0.9659259 },
						{ x: -964.62085, y: -2612.54321, z: 14.3582888, rX: 0.0, rY: 0.0, rZ: 0.8660253, rW: 0.5000002 },
						{ x: -881.683533, y: -2441.268, z: 14.2817488, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361916 },
						{ x: -834.092041, y: -2342.27759, z: 15.0349178, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: -905.3336, y: -2290.797, z: 7.627888, rX: 0.0, rY: 0.0, rZ: -0.4226185, rW: 0.9063078 },
						{ x: -720.209, y: -2374.45654, z: 15.5595169, rX: 0.0, rY: 0.0, rZ: 0.67559, rW: 0.7372775 },
						{ x: -987.147156, y: -2288.96948, z: 9.461778, rX: 0.0, rY: 0.0, rZ: 0.9238797, rW: -0.3826832 },
						{ x: -1083.49487, y: -2258.73828, z: 14.8872385, rX: 0.0, rY: 0.0, rZ: -0.3826837, rW: 0.9238794 },
						{ x: -1196.458, y: -2121.19482, z: 13.7351675, rX: 0.0, rY: 0.0, rZ: 0.3826832, rW: 0.9238796 },
						{ x: -1112.01416, y: -2044.684, z: 13.9891148, rX: 0.0, rY: 0.0, rZ: -0.9238796, rW: 0.3826833 },
						{ x: -1132.65393, y: -1962.10962, z: 13.6789713, rX: 0.0, rY: 0.0, rZ: 0.7071066, rW: 0.7071069 },
						{ x: -1054.9281, y: -1953.51184, z: 14.0257025, rX: 0.0, rY: 0.0, rZ: 0.3826832, rW: 0.9238796 },
						{ x: -942.0071, y: -2033.95959, z: 10.155488, rX: 0.0, rY: 0.0, rZ: 0.9238794, rW: 0.3826837 },
						{ x: -865.35, y: -2137.17041, z: 9.925451, rX: 0.0, rY: 0.0, rZ: -0.3420204, rW: 0.9396926 },
						{ x: -761.2575, y: -2177.62, z: 6.514573, rX: 0.0, rY: 0.0, rZ: 0.9396926, rW: 0.3420204 },
						{ x: -695.306458, y: -2241.58325, z: 5.415643, rX: 0.0, rY: 0.0, rZ: 0.7071066, rW: 0.7071069 },
						{ x: -593.135254, y: -2083.771, z: 6.27447844, rX: 0.0, rY: 0.0, rZ: 0.3826832, rW: 0.9238796 },
						{ x: -738.4192, y: -2074.623, z: 9.206342, rX: 0.0, rY: 0.0, rZ: 0.3007056, rW: 0.9537171 },
						{ x: -693.9266, y: -1970.86023, z: 8.347302, rX: 0.0, rY: 0.0, rZ: -0.3826836, rW: 0.9238795 },
						{ x: 806.4479, y: -2535.56763, z: 22.513855, rX: 0.0, rY: 0.0, rZ: 0.6755905, rW: -0.7372772 },
						{ x: 1043.54724, y: -2510.60522, z: 28.7997551, rX: 0.0, rY: 0.0, rZ: 0.7372772, rW: 0.6755904 },
						{ x: 1082.98877, y: -2415.135, z: 30.5867214, rX: 0.0, rY: 0.0, rZ: 0.7372772, rW: 0.6755903 },
						{ x: 1077.21863, y: -2335.97, z: 30.9739265, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 1132.67273, y: -2283.44141, z: 31.1674175, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 1073.20166, y: -2208.73584, z: 31.8266716, rX: 0.0, rY: 0.0, rZ: 0.7372772, rW: 0.6755904 },
						{ x: 1129.3855, y: -2121.24976, z: 32.6836281, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 1137.66992, y: -2052.2688, z: 31.5774231, rX: 0.0, rY: 0.0, rZ: 0.6087617, rW: -0.7933532 },
						{ x: 1031.85071, y: -1990.84741, z: 31.4792175, rX: 0.0, rY: 0.0, rZ: 0.6755901, rW: 0.7372775 },
						{ x: 1107.71765, y: -1970.04919, z: 31.81056, rX: 0.0, rY: 0.0, rZ: 0.8870107, rW: 0.4617488 },
						{ x: 1053.792, y: -1897.35413, z: 31.6781845, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361963 },
						{ x: 1011.43231, y: -1925.00317, z: 31.59255, rX: 0.0, rY: 0.0, rZ: -0.04361957, rW: 0.9990482 },
						{ x: 1002.30945, y: -2011.57886, z: 32.263092, rX: 0.0, rY: 0.0, rZ: -0.5372995, rW: -0.8433915 },
						{ x: 1051.355, y: -1819.54053, z: 37.84845, rX: 0.0, rY: 0.0, rZ: 0.1736484, rW: -0.9848077 },
						{ x: 916.842651, y: -1786.34314, z: 31.2704144, rX: 0.0, rY: 0.0, rZ: 0.7372772, rW: 0.6755903 },
						{ x: 904.7772, y: -1887.746, z: 31.7941742, rX: 0.0, rY: 0.0, rZ: 0.6755905, rW: -0.7372772 },
						{ x: 905.9035, y: -1977.10645, z: 31.2802849, rX: 0.0, rY: 0.0, rZ: -0.7071066, rW: -0.7071069 },
						{ x: 922.1506, y: -2061.11133, z: 31.2596779, rX: 0.0, rY: 0.0, rZ: 0.9238796, rW: -0.3826832 },
						{ x: 784.7846, y: -2395.9375, z: 22.8739, rX: 0.0, rY: 0.0, rZ: 0.04361907, rW: 0.9990482 },
						{ x: 817.965942, y: -2369.46387, z: 30.1591835, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 836.6814, y: -2307.41113, z: 30.9716187, rX: 0.0, rY: 0.0, rZ: 0.7660447, rW: -0.6427875 },
						{ x: 874.2606, y: -2379.14746, z: 28.77531, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 875.155151, y: -2255.98877, z: 31.4176731, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 890.2178, y: -2205.2168, z: 31.21147, rX: 0.0, rY: 0.0, rZ: 0.707107, rW: -0.7071066 },
						{ x: 903.1101, y: -2134.5188, z: 31.1088524, rX: 0.0, rY: 0.0, rZ: 0.6755905, rW: -0.7372772 },
						{ x: 841.3128, y: -2116.12939, z: 30.350174, rX: 0.0, rY: 0.0, rZ: 0.6755905, rW: -0.7372772 },
						{ x: 805.9882, y: -2020.37488, z: 29.8926182, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 844.7803, y: -1924.31482, z: 30.5328979, rX: 0.0, rY: 0.0, rZ: 0.6755904, rW: -0.7372772 },
						{ x: 888.4115, y: -1804.15857, z: 31.2961, rX: 0.0, rY: 0.0, rZ: 0.9396926, rW: 0.3420204 },
						{ x: 850.795532, y: -1869.02759, z: 29.39954, rX: 0.0, rY: 0.0, rZ: 0.3007054, rW: 0.9537171 },
						{ x: 733.6086, y: -1875.43335, z: 30.3616238, rX: 0.0, rY: 0.0, rZ: 0.7071066, rW: 0.7071069 },
						{ x: 739.942261, y: -2007.05981, z: 29.9867039, rX: 0.0, rY: 0.0, rZ: 0.6755905, rW: -0.7372772 },
						{ x: 1047.34985, y: -1720.43091, z: 36.15153, rX: 0.0, rY: 0.0, rZ: 0.6427874, rW: 0.7660446 },
						{ x: 1011.86029, y: -1657.26416, z: 30.9078388, rX: 0.0, rY: 0.0, rZ: 0.7372772, rW: 0.6755904 },
						{ x: 980.9151, y: -1708.681, z: 31.8813629, rX: 0.0, rY: 0.0, rZ: 0.04361913, rW: 0.9990482 },
						{ x: 904.514954, y: -1722.14307, z: 32.9266319, rX: 0.0, rY: 0.0, rZ: 0.6755904, rW: -0.7372772 },
						{ x: 896.0752, y: -1661.91382, z: 31.1080265, rX: 0.0, rY: 0.0, rZ: 0.7071066, rW: 0.7071069 },
						{ x: 905.104431, y: -1605.08191, z: 32.06734, rX: 0.0, rY: 0.0, rZ: 0.7071066, rW: 0.7071069 },
						{ x: 981.566956, y: -1573.8877, z: 31.4559841, rX: 0.0, rY: 0.0, rZ: 0.707107, rW: -0.7071066 },
						{ x: 1000.06586, y: -1475.49829, z: 31.74118, rX: 0.0, rY: 0.0, rZ: 0.707107, rW: -0.7071066 },
						{ x: 909.679749, y: -1490.25659, z: 30.9376888, rX: 0.0, rY: 0.0, rZ: -0.0436191, rW: -0.9990482 },
						{ x: 941.1095, y: -1571.75244, z: 31.1326771, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361963 },
						{ x: 1032.06543, y: -2150.15039, z: 31.8308849, rX: 0.0, rY: 0.0, rZ: 0.7660443, rW: 0.6427878 },
						{ x: 1014.14862, y: -2251.18823, z: 33.02717, rX: 0.0, rY: 0.0, rZ: -0.04361898, rW: -0.9990483 },
						{ x: 988.429749, y: -2438.84351, z: 29.0639687, rX: 0.0, rY: 0.0, rZ: -0.7372772, rW: -0.6755904 },
						{ x: 948.962, y: -2374.517, z: 31.3391323, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 933.7537, y: -2255.46045, z: 31.3442173, rX: 0.0, rY: 0.0, rZ: -0.04361957, rW: 0.9990482 },
						{ x: 972.9925, y: -2189.538, z: 31.0459652, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 959.3805, y: -2108.19019, z: 30.7443542, rX: 0.0, rY: 0.0, rZ: 0.04361918, rW: 0.9990482 },
						{ x: 1103.37036, y: -781.9714, z: 58.71069, rX: 0.0, rY: 0.0, rZ: 0.707107, rW: -0.7071066 },
						{ x: 984.8766, y: -644.4112, z: 57.91419, rX: 0.0, rY: 0.0, rZ: 0.0436197, rW: -0.9990482 },
						{ x: 950.8815, y: -473.085876, z: 61.8501244, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588193 },
						{ x: 1056.18921, y: -598.1732, z: 57.6733055, rX: 0.0, rY: 0.0, rZ: 0.7660446, rW: -0.6427875 },
						{ x: 1192.15076, y: -594.7183, z: 64.50637, rX: 0.0, rY: 0.0, rZ: 0.08715601, rW: -0.9961947 },
						{ x: 1328.90686, y: -691.434448, z: 66.2836456, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087616 },
						{ x: 1273.41907, y: -470.017883, z: 69.40331, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361963 },
						{ x: 1255.361, y: -351.617554, z: 69.73384, rX: 0.0, rY: 0.0, rZ: -0.9914449, rW: 0.1305261 },
						{ x: 1144.16052, y: -294.3983, z: 69.7540741, rX: 0.0, rY: 0.0, rZ: 0.04361971, rW: -0.9990482 },
						{ x: 998.9333, y: -307.3743, z: 58.50267, rX: 0.0, rY: 0.0, rZ: 0.9537171, rW: -0.3007056 },
						{ x: 904.0733, y: -148.735077, z: 76.72533, rX: 0.0, rY: 0.0, rZ: 0.2588188, rW: 0.9659259 },
						{ x: 836.996033, y: -251.99353, z: 65.69637, rX: 0.0, rY: 0.0, rZ: 0.5735762, rW: 0.8191522 },
						{ x: 696.427, y: -307.316925, z: 59.408905, rX: 0.0, rY: 0.0, rZ: 0.1305265, rW: -0.9914448 },
						{ x: 808.4, y: -119.761383, z: 80.20946, rX: 0.0, rY: 0.0, rZ: 0.5000003, rW: -0.8660253 },
						{ x: 1042.50659, y: -140.574692, z: 74.6370544, rX: 0.0, rY: 0.0, rZ: 0.9063077, rW: 0.4226185 },
						{ x: 1109.4458, y: 81.45134, z: 81.3490753, rX: 0.0, rY: 0.0, rZ: 0.7071066, rW: 0.7071069 },
						{ x: 1052.39526, y: 208.9513, z: 81.5832443, rX: 0.0, rY: 0.0, rZ: 0.3007056, rW: 0.9537171 },
						{ x: 994.826233, y: 72.5134, z: 81.76701, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: -0.2588188 },
						{ x: 1121.66858, y: -988.324646, z: 46.89239, rX: 0.0, rY: 0.0, rZ: 0.7372775, rW: -0.6755901 },
						{ x: 1230.984, y: -1133.541, z: 38.7650871, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588193 },
						{ x: 1191.92273, y: -1226.53748, z: 35.9600029, rX: 0.0, rY: 0.0, rZ: 0.8191522, rW: -0.5735762 },
						{ x: 1136.282, y: -1335.50623, z: 35.0637474, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361963 },
						{ x: 1110.72571, y: -1418.52063, z: 30.1462231, rX: 0.0, rY: 0.0, rZ: -0.7071066, rW: -0.7071069 },
						{ x: 1224.522, y: -1497.30579, z: 35.5387726, rX: 0.0, rY: 0.0, rZ: -0.7071066, rW: -0.7071069 },
						{ x: 1260.04688, y: -1562.48145, z: 54.92435, rX: 0.0, rY: 0.0, rZ: 0.8870109, rW: -0.4617484 },
						{ x: 1323.92041, y: -1662.61536, z: 52.07173, rX: 0.0, rY: 0.0, rZ: 0.9063079, rW: -0.4226181 },
						{ x: 1181.87854, y: -1767.64636, z: 38.9495773, rX: 0.0, rY: 0.0, rZ: 0.3420204, rW: -0.9396926 },
						{ x: 1381.40527, y: -2210.5415, z: 61.5379181, rX: 0.0, rY: 0.0, rZ: 0.5735762, rW: 0.8191522 },
						{ x: 1420.07019, y: -2041.22937, z: 52.72826, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 1489.89636, y: -1880.93726, z: 72.78334, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588193 },
						{ x: 1454.31921, y: -1667.89844, z: 66.53502, rX: 0.0, rY: 0.0, rZ: 0.976296, rW: 0.2164399 },
						{ x: 755.641, y: -559.871, z: 34.3682251, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 711.672546, y: -603.234131, z: 27.4960423, rX: 0.0, rY: 0.0, rZ: -0.04361966, rW: 0.9990482 },
						{ x: 746.4934, y: -674.7074, z: 28.6593361, rX: 0.0, rY: 0.0, rZ: -0.7071066, rW: -0.7071069 },
						{ x: 738.3809, y: -739.3617, z: 27.2197876, rX: 0.0, rY: 0.0, rZ: -0.0436196, rW: 0.9990482 },
						{ x: 732.9416, y: -836.0478, z: 25.5948238, rX: 0.0, rY: 0.0, rZ: 0.04361922, rW: 0.9990482 },
						{ x: 693.4392, y: -716.513855, z: 26.4506512, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071066 },
						{ x: 809.8009, y: -756.5287, z: 26.7676373, rX: 0.0, rY: 0.0, rZ: -0.04361957, rW: 0.9990482 },
						{ x: 825.6982, y: -810.9634, z: 26.58807, rX: 0.0, rY: 0.0, rZ: -0.9762961, rW: 0.2164395 },
						{ x: 695.942261, y: -876.5314, z: 24.5207138, rX: 0.0, rY: 0.0, rZ: 0.7071066, rW: 0.7071069 },
						{ x: 676.708862, y: -996.105164, z: 22.8799133, rX: 0.0, rY: 0.0, rZ: 0.04361918, rW: 0.9990482 },
						{ x: 845.8903, y: -991.975, z: 28.9085789, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361915 },
						{ x: 948.256165, y: -905.249451, z: 43.9540825, rX: 0.0, rY: 0.0, rZ: 0.7372775, rW: -0.67559 },
						{ x: 834.6859, y: -869.262634, z: 25.6598721, rX: 0.0, rY: 0.0, rZ: -0.08715586, rW: 0.9961947 },
						{ x: 849.314, y: -965.0451, z: 26.9148121, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071066 },
						{ x: 765.9794, y: -1035.976, z: 20.6208363, rX: 0.0, rY: 0.0, rZ: -0.04361967, rW: 0.9990482 },
						{ x: 723.1322, y: -1110.06934, z: 22.96755, rX: 0.0, rY: 0.0, rZ: 0.8191519, rW: 0.5735766 },
						{ x: 650.552734, y: -1114.53882, z: 22.87358, rX: 0.0, rY: 0.0, rZ: -0.04361966, rW: 0.9990482 },
						{ x: 746.6997, y: -1154.02991, z: 24.612051, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071066 },
						{ x: 847.4891, y: -1123.64258, z: 23.6089783, rX: 0.0, rY: 0.0, rZ: 0.3420199, rW: 0.9396927 },
						{ x: 888.361755, y: -1153.96143, z: 25.1413288, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071066 },
						{ x: 922.549561, y: -1134.95715, z: 26.220993, rX: 0.0, rY: 0.0, rZ: 0.7071066, rW: 0.7071069 },
						{ x: 1002.61792, y: -1176.81982, z: 26.0849743, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071066 },
						{ x: 835.227356, y: -1217.4856, z: 26.44271, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361963 },
						{ x: 939.4695, y: -1230.4292, z: 26.2198811, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588193 },
						{ x: 1014.64069, y: -1235.98975, z: 25.8202381, rX: 0.0, rY: 0.0, rZ: -0.9848078, rW: 0.1736481 },
						{ x: 829.279358, y: -1058.42859, z: 28.52329, rX: 0.0, rY: 0.0, rZ: 0.0436192, rW: 0.9990482 },
						{ x: 897.0922, y: -1045.56335, z: 33.1093, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: 962.8051, y: -1034.24512, z: 41.4997673, rX: 0.0, rY: 0.0, rZ: -0.7372774, rW: 0.6755901 },
						{ x: 849.131165, y: -1380.55774, z: 26.71156, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: 0.04361927 },
						{ x: 844.4443, y: -1270.83728, z: 26.58218, rX: 0.0, rY: 0.0, rZ: -0.9848078, rW: 0.1736481 },
						{ x: 738.097961, y: -1399.1322, z: 28.1986275, rX: 0.0, rY: 0.0, rZ: -0.7071069, rW: 0.7071066 },
						{ x: 766.309937, y: -1324.39514, z: 26.9814587, rX: 0.0, rY: 0.0, rZ: -0.9990482, rW: 0.04361927 },
						{ x: 716.868958, y: -1455.61487, z: 22.2548389, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: -170.278046, y: -2128.81226, z: 21.4704056, rX: 0.0, rY: 0.0, rZ: -0.8191521, rW: 0.5735763 },
						{ x: -72.1934738, y: -2003.07458, z: 18.7737484, rX: 0.0, rY: 0.0, rZ: -0.7372773, rW: -0.6755902 },
						{ x: -400.66507, y: -1883.73376, z: 22.0418, rX: 0.0, rY: 0.0, rZ: -0.9396926, rW: -0.3420203 },
						{ x: -267.585541, y: -1929.24512, z: 30.2330589, rX: 0.0, rY: 0.0, rZ: -0.4226183, rW: 0.9063078 },
						{ x: -364.19574, y: -2023.55054, z: 30.3729687, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -616.6318, y: -1787.74927, z: 24.1499386, rX: 0.0, rY: 0.0, rZ: -0.8870109, rW: 0.4617485 },
						{ x: -549.3996, y: -1812.4176, z: 22.9445724, rX: 0.0, rY: 0.0, rZ: -0.5000001, rW: 0.8660253 },
						{ x: -604.5695, y: -1734.412, z: 24.0460968, rX: 0.0, rY: 0.0, rZ: 0.3007057, rW: 0.953717 },
						{ x: -665.755859, y: -1714.36426, z: 25.6528358, rX: 0.0, rY: 0.0, rZ: -0.7372774, rW: 0.6755902 },
						{ x: -413.622528, y: -1687.86548, z: 19.50976, rX: 0.0, rY: 0.0, rZ: -0.173648, rW: -0.9848078 },
						{ x: -548.8068, y: -1601.90308, z: 19.8165417, rX: 0.0, rY: 0.0, rZ: -0.7372773, rW: -0.6755903 },
						{ x: -607.4151, y: -1635.08508, z: 26.4140549, rX: 0.0, rY: 0.0, rZ: -0.6755903, rW: 0.7372773 },
						{ x: -586.157837, y: -1680.97656, z: 20.3295536, rX: 0.0, rY: 0.0, rZ: -0.976296, rW: 0.2164395 },
						{ x: -766.566467, y: -1520.14282, z: 6.02134943, rX: 0.0, rY: 0.0, rZ: -0.2164397, rW: 0.9762961 },
						{ x: -694.1374, y: -1385.8446, z: 5.732015, rX: 0.0, rY: 0.0, rZ: -0.9063078, rW: -0.4226183 },
						{ x: -729.82605, y: -1382.09583, z: 2.138416, rX: 0.0, rY: 0.0, rZ: 0.3826834, rW: 0.9238796 },
						{ x: -715.0933, y: -1265.91919, z: 6.00697, rX: 0.0, rY: 0.0, rZ: -0.9396926, rW: 0.3420201 },
						{ x: -864.985, y: -1263.53467, z: 5.955953, rX: 0.0, rY: 0.0, rZ: -0.7660444, rW: -0.6427877 },
						{ x: -730.6662, y: -1226.82, z: 11.4093933, rX: 0.0, rY: 0.0, rZ: -0.9396927, rW: 0.3420201 },
						{ x: -801.619751, y: -1168.16028, z: 9.483009, rX: 0.0, rY: 0.0, rZ: -0.9063078, rW: -0.4226183 },
						{ x: -917.1051, y: -1244.94641, z: 1.78872573, rX: 0.0, rY: 0.0, rZ: 0.258819, rW: 0.9659259 },
						{ x: -949.7826, y: -1312.06909, z: 5.36830235, rX: 0.0, rY: 0.0, rZ: -0.9848078, rW: 0.1736481 },
						{ x: -984.290344, y: -1327.98767, z: 6.00530863, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: -0.9848078 },
						{ x: -1016.45465, y: -1357.37463, z: 5.764801, rX: 0.0, rY: 0.0, rZ: -0.7933533, rW: -0.6087615 },
						{ x: -1067.70032, y: -1459.14392, z: 5.70552349, rX: 0.0, rY: 0.0, rZ: 0.7372772, rW: 0.6755903 },
						{ x: -983.5349, y: -1436.07178, z: 5.22503138, rX: 0.0, rY: 0.0, rZ: -0.9848078, rW: -0.1736482 },
						{ x: -955.3582, y: -1393.21777, z: 2.04876733, rX: 0.0, rY: 0.0, rZ: -0.7660445, rW: 0.6427876 },
						{ x: -919.550354, y: -1411.33728, z: 7.81820869, rX: 0.0, rY: 0.0, rZ: -0.9848077, rW: -0.1736482 },
						{ x: -990.362549, y: -1472.10974, z: 5.24788857, rX: 0.0, rY: 0.0, rZ: -0.3007059, rW: 0.953717 },
						{ x: -1045.6593, y: -1496.54626, z: 4.88724947, rX: 0.0, rY: 0.0, rZ: -0.3007059, rW: 0.953717 },
						{ x: -1013.70325, y: -1515.61707, z: 7.11685848, rX: 0.0, rY: 0.0, rZ: 0.4617485, rW: 0.887011 },
						{ x: -935.8041, y: -1514.09033, z: 5.62747049, rX: 0.0, rY: 0.0, rZ: 0.6087614, rW: 0.7933534 },
						{ x: -862.1693, y: -1493.63586, z: 1.83670747, rX: 0.0, rY: 0.0, rZ: 0.1736483, rW: -0.9848077 },
						{ x: -966.1997, y: -1563.57434, z: 5.580393, rX: 0.0, rY: 0.0, rZ: -0.9848077, rW: -0.1736483 },
						{ x: -1897.72034, y: -311.469269, z: 50.36181, rX: 0.0, rY: 0.0, rZ: -0.9537171, rW: 0.3007057 },
						{ x: -1853.797, y: -455.441681, z: 46.3196678, rX: 0.0, rY: 0.0, rZ: 0.258819, rW: 0.9659258 },
						{ x: -2015.34363, y: -248.07074, z: 24.7961044, rX: 0.0, rY: 0.0, rZ: -0.8660254, rW: -0.5000001 },
						{ x: -1755.12585, y: -526.5194, z: 39.109726, rX: 0.0, rY: 0.0, rZ: 0.08715556, rW: 0.9961948 },
						{ x: -1726.355, y: -157.477432, z: 59.7674, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: -1627.65771, y: -225.414948, z: 55.029705, rX: 0.0, rY: 0.0, rZ: 0.2164395, rW: 0.976296 },
						{ x: 759.1969, y: 208.879532, z: 86.40172, rX: 0.0, rY: 0.0, rZ: -0.9659259, rW: 0.258819 },
						{ x: 665.583435, y: 227.337982, z: 94.8672, rX: 0.0, rY: 0.0, rZ: -0.8191521, rW: -0.5735765 },
						{ x: 650.9389, y: 249.103729, z: 102.780525, rX: 0.0, rY: 0.0, rZ: -0.9659259, rW: 0.2588189 },
						{ x: 368.619263, y: 325.8863, z: 104.223694, rX: 0.0, rY: 0.0, rZ: 0.08715566, rW: 0.9961947 },
						{ x: 332.538055, y: 361.738159, z: 107.15802, rX: 0.0, rY: 0.0, rZ: -0.7933533, rW: -0.6087615 },
						{ x: 257.580383, y: 372.43045, z: 105.998055, rX: 0.0, rY: 0.0, rZ: -0.819152, rW: -0.5735765 },
						{ x: 169.587448, y: 383.951935, z: 110.363083, rX: 0.0, rY: 0.0, rZ: -0.9961947, rW: -0.08715583 },
						{ x: -90.28387, y: 398.737244, z: 112.811188, rX: 0.0, rY: 0.0, rZ: 0.5372996, rW: -0.8433914 },
						{ x: -94.85435, y: 359.598724, z: 113.520836, rX: 0.0, rY: 0.0, rZ: -0.5372997, rW: 0.8433914 },
						{ x: -6.97434759, y: 293.648315, z: 111.026833, rX: 0.0, rY: 0.0, rZ: -0.5372997, rW: 0.8433914 },
						{ x: 369.509552, y: -2107.075, z: 17.1296959, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: 400.945068, y: -2065.26855, z: 21.6916733, rX: 0.0, rY: 0.0, rZ: 0.5, rW: -0.8660254 },
						{ x: 291.8308, y: -2029.844, z: 20.2931767, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 390.218323, y: -1997.75684, z: 24.4075661, rX: 0.0, rY: 0.0, rZ: -0.819152, rW: -0.5735765 },
						{ x: 383.8379, y: -2058.74634, z: 22.2468815, rX: 0.0, rY: 0.0, rZ: -0.9537171, rW: 0.3007057 },
						{ x: 292.026672, y: -2092.70166, z: 17.49602, rX: 0.0, rY: 0.0, rZ: -0.2588191, rW: 0.9659259 },
						{ x: 272.852783, y: -2000.41345, z: 20.336813, rX: 0.0, rY: 0.0, rZ: -0.4617487, rW: 0.8870108 },
						{ x: 330.198059, y: -1932.74878, z: 25.1616859, rX: -0.02343668, rY: -0.03678822, rZ: -0.8425887, rW: -0.5367882 },
						{ x: 266.602417, y: -1942.72754, z: 24.925703, rX: 0.0, rY: 0.0, rZ: -0.8870108, rW: -0.4617487 },
						{ x: 191.7761, y: -2021.13, z: 18.87285, rX: 0.0, rY: 0.0, rZ: 0.7660444, rW: 0.6427877 },
						{ x: 115.895424, y: -2017.77185, z: 19.10785, rX: 0.0, rY: 0.0, rZ: 0.5735764, rW: -0.8191521 },
						{ x: 162.120972, y: -1924.05176, z: 21.7770042, rX: 0.0, rY: 0.0, rZ: -0.9537171, rW: 0.3007057 },
						{ x: 111.404625, y: -1879.0752, z: 24.2106285, rX: 0.0, rY: 0.0, rZ: 0.2164396, rW: 0.976296 },
						{ x: 116.820267, y: -1953.80591, z: 21.1250858, rX: 0.0, rY: 0.0, rZ: 0.4617485, rW: 0.8870109 },
						{ x: -3.543656, y: -1877.216, z: 24.3960285, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.34202 },
						{ x: 35.9953423, y: -1827.81738, z: 25.1590748, rX: 0.0, rY: 0.0, rZ: 0.9238794, rW: 0.3826835 },
						{ x: -54.652256, y: -1858.89246, z: 26.5707855, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: 0.4617487 },
						{ x: -6.13946724, y: -1813.7135, z: 26.4870853, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -41.4350243, y: -1754.68774, z: 30.3331738, rX: 0.0, rY: 0.0, rZ: 0.953717, rW: -0.3007056 },
						{ x: -133.801208, y: -1785.7627, z: 30.2550316, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 72.96853, y: -1722.96753, z: 30.0432034, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226184 },
						{ x: 218.774734, y: -1866.80347, z: 27.33597, rX: 0.0, rY: 0.0, rZ: -0.4617487, rW: 0.8870108 },
						{ x: 158.5027, y: -1870.396, z: 24.3026371, rX: 0.0, rY: 0.0, rZ: 0.9762961, rW: -0.2164395 },
						{ x: -15.2676353, y: -1681.83521, z: 30.354311, rX: 0.0, rY: 0.0, rZ: 0.5372995, rW: 0.8433915 },
						{ x: -187.337463, y: -1701.08545, z: 33.4565773, rX: 0.0, rY: 0.0, rZ: 0.9396926, rW: 0.3420203 },
						{ x: -233.95192, y: -1665.96338, z: 34.28538, rX: 0.0, rY: 0.0, rZ: -0.04361948, rW: 0.9990482 },
						{ x: -193.591644, y: -1578.6311, z: 35.05968, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: 0.4617487 },
						{ x: -179.74025, y: -1519.8512, z: 34.15904, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -155.217178, y: -1628.59863, z: 34.1391258, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226184 },
						{ x: -109.865585, y: -1589.47729, z: 32.57548, rX: 0.0, rY: 0.0, rZ: -0.9396927, rW: 0.3420201 },
						{ x: -114.523155, y: -1643.93372, z: 32.61312, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: 0.4617487 },
						{ x: -163.773636, y: -1675.84741, z: 33.81379, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -268.5322, y: -1561.20081, z: 32.1446342, rX: 0.0, rY: 0.0, rZ: 0.08715565, rW: 0.9961947 },
						{ x: -187.325592, y: -1509.83167, z: 32.8621979, rX: 0.0, rY: 0.0, rZ: 0.3826833, rW: 0.9238796 },
						{ x: -353.66272, y: -1491.205, z: 30.76054, rX: 0.0, rY: 0.0, rZ: 0.04361933, rW: 0.9990482 },
						{ x: -312.104065, y: -1394.54248, z: 31.5634422, rX: 0.0, rY: 0.0, rZ: 0.7372773, rW: 0.6755903 },
						{ x: -314.16922, y: -1321.428, z: 31.9435654, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: -323.361237, y: -1263.94275, z: 31.9956684, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: -233.653091, y: -1374.20068, z: 31.9937325, rX: 0.0, rY: 0.0, rZ: 0.4999999, rW: 0.8660254 },
						{ x: -131.689392, y: -1330.94824, z: 30.4233017, rX: 0.0, rY: 0.0, rZ: 0.9990483, rW: 0.04361943 },
						{ x: -187.007309, y: -1323.5553, z: 31.9393768, rX: 0.0, rY: 0.0, rZ: -0.9238796, rW: 0.3826834 },
						{ x: -226.739487, y: -1258.41357, z: 31.9051952, rX: 0.0, rY: 0.0, rZ: 0.7372773, rW: 0.6755903 },
						{ x: -219.224838, y: -1341.98853, z: 31.9162846, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: -151.133728, y: -1441.19263, z: 32.16217, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.34202 },
						{ x: -81.02677, y: -1392.84827, z: 29.7773342, rX: 0.0, rY: 0.0, rZ: 0.9990483, rW: 0.04361958 },
						{ x: -104.829361, y: -1463.1554, z: 34.10978, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226184 },
						{ x: 8.380992, y: -1402.13672, z: 29.7175846, rX: 0.0, rY: 0.0, rZ: -0.7372774, rW: 0.6755902 },
						{ x: 82.24087, y: -1432.77148, z: 30.0349636, rX: 0.0, rY: 0.0, rZ: -0.9238795, rW: 0.3826834 },
						{ x: 49.4521027, y: -1455.1405, z: 30.1951, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -27.7137661, y: -1447.09583, z: 31.3339367, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361919 },
						{ x: -81.1779251, y: -1526.61584, z: 34.82678, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -33.9550362, y: -1514.74988, z: 31.5801353, rX: 0.0, rY: 0.0, rZ: 0.953717, rW: -0.3007056 },
						{ x: 0.9862404, y: -1504.85925, z: 30.2800217, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -45.8419533, y: -1269.01917, z: 29.8463764, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361933 },
						{ x: -59.0903168, y: -1346.45483, z: 29.6417274, rX: 0.0, rY: 0.0, rZ: -0.7372774, rW: 0.6755902 },
						{ x: 19.5346432, y: -1343.121, z: 29.78557, rX: 0.0, rY: 0.0, rZ: 0.04361933, rW: 0.9990482 },
						{ x: -6.96952724, y: -1285.54236, z: 30.3462276, rX: 0.0, rY: 0.0, rZ: -0.04361952, rW: 0.9990482 },
						{ x: 35.34735, y: -1315.90759, z: 30.1550636, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: 93.86401, y: -1285.14465, z: 29.9116058, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: 0.2588192 },
						{ x: 166.5147, y: -1252.05872, z: 30.60749, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: 191.089722, y: -1301.349, z: 30.3464584, rX: 0.0, rY: 0.0, rZ: 0.976296, rW: -0.2164395 },
						{ x: 138.43573, y: -1349.11169, z: 30.1897545, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 233.479645, y: -1379.80481, z: 31.7103252, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 299.712616, y: -1232.57166, z: 29.7877769, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: 339.2046, y: -1280.26367, z: 33.1628342, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 394.775269, y: -1278.89148, z: 34.1052666, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 437.95816, y: -1316.63928, z: 31.6179, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 485.819183, y: -1299.88232, z: 30.13238, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361948 },
						{ x: 517.612549, y: -1406.59851, z: 30.07306, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361948 },
						{ x: 398.945984, y: -1348.43884, z: 31.6264229, rX: 0.0, rY: 0.0, rZ: -0.4226184, rW: 0.9063078 },
						{ x: 316.062561, y: -1453.88708, z: 30.5974522, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226184 },
						{ x: 404.436157, y: -1415.88257, z: 30.253767, rX: 0.0, rY: 0.0, rZ: -0.9238796, rW: 0.3826835 },
						{ x: 446.271057, y: -1489.17712, z: 30.1476383, rX: 0.0, rY: 0.0, rZ: -0.1736483, rW: 0.9848077 },
						{ x: 378.814362, y: -1510.13367, z: 29.82816, rX: 0.0, rY: 0.0, rZ: 0.2588191, rW: -0.9659258 },
						{ x: 504.061249, y: -1492.61072, z: 29.8745346, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: 446.379883, y: -1573.28357, z: 29.8126259, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.34202 },
						{ x: 527.720459, y: -1654.01208, z: 29.7338867, rX: 0.0, rY: 0.0, rZ: -0.4226184, rW: 0.9063078 },
						{ x: 561.8026, y: -1563.682, z: 29.8810539, rX: 0.0, rY: 0.0, rZ: 0.9238794, rW: 0.3826835 },
						{ x: 144.540359, y: -1464.58044, z: 30.2505856, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226184 },
						{ x: 186.862015, y: -1444.49524, z: 29.9464226, rX: 0.0, rY: 0.0, rZ: 0.8660253, rW: 0.5000001 },
						{ x: 183.586426, y: -1514.82544, z: 30.1474361, rX: 0.0, rY: 0.0, rZ: 0.3826833, rW: 0.9238796 },
						{ x: 221.258179, y: -1534.43958, z: 30.24439, rX: 0.0, rY: 0.0, rZ: 0.9537169, rW: 0.3007059 },
						{ x: 114.707214, y: -1525.18616, z: 30.5500965, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.34202 },
						{ x: 249.738663, y: -1593.38367, z: 32.03954, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226184 },
						{ x: 324.262451, y: -1665.54126, z: 27.827879, rX: 0.0, rY: 0.0, rZ: -0.4226184, rW: 0.9063078 },
						{ x: 401.909241, y: -1654.498, z: 30.0322418, rX: 0.0, rY: 0.0, rZ: -0.4617487, rW: 0.8870109 },
						{ x: 351.68924, y: -1584.45288, z: 30.5526657, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 475.110138, y: -1683.58789, z: 30.3739719, rX: 0.0, rY: 0.0, rZ: -0.4226184, rW: 0.9063078 },
						{ x: 453.014374, y: -1783.73047, z: 29.09156, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 523.546448, y: -1751.597, z: 29.6247063, rX: 0.0, rY: 0.0, rZ: -0.976296, rW: 0.2164395 },
						{ x: 564.6528, y: -1784.86426, z: 29.90354, rX: 0.0, rY: 0.0, rZ: 0.976296, rW: -0.2164395 },
						{ x: 531.778259, y: -1863.85376, z: 26.18033, rX: 0.0, rY: 0.0, rZ: 0.4617485, rW: 0.8870109 },
						{ x: 513.1364, y: -1841.49719, z: 28.2384377, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.34202 },
						{ x: 599.2595, y: -1886.50439, z: 25.6332283, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305261 },
						{ x: 511.1793, y: -1919.961, z: 25.6056881, rX: 0.0, rY: 0.0, rZ: 0.4617485, rW: 0.8870109 },
						{ x: 483.217316, y: -2019.84778, z: 24.6301956, rX: 0.0, rY: 0.0, rZ: 0.9238796, rW: -0.3826833 },
						{ x: 480.4824, y: -1874.13062, z: 27.5201817, rX: 0.0, rY: 0.0, rZ: 0.5372995, rW: 0.8433915 },
						{ x: 419.8684, y: -1903.59119, z: 26.6244316, rX: 0.0, rY: 0.0, rZ: 0.9238794, rW: 0.3826835 },
						{ x: 281.128418, y: -1804.243, z: 27.9015636, rX: 0.0, rY: 0.0, rZ: 0.9238794, rW: 0.3826835 },
						{ x: 309.5839, y: -1713.15332, z: 29.7361546, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.34202 },
						{ x: 248.480408, y: -1723.6228, z: 29.7082748, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 232.2006, y: -1750.92249, z: 29.5924911, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.34202 },
						{ x: 270.2776, y: -1791.27869, z: 28.1456337, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226184 },
						{ x: 168.0444, y: -1710.85181, z: 29.9217014, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: 0.4617487 },
						{ x: 209.764053, y: -1687.79016, z: 30.283823, rX: 0.0, rY: 0.0, rZ: -0.3007059, rW: 0.9537169 },
						{ x: 126.012657, y: -1695.2854, z: 30.0462036, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: 174.077957, y: -1641.36218, z: 29.5358143, rX: 0.0, rY: 0.0, rZ: -0.9063078, rW: 0.4226182 },
						{ x: 230.58905, y: -1662.47412, z: 29.9474525, rX: 0.0, rY: 0.0, rZ: 0.3007057, rW: 0.9537171 },
						{ x: 108.655861, y: -1572.42261, z: 30.61917, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: 0.4617487 },
						{ x: 21.7598228, y: -1601.61194, z: 29.9278278, rX: 0.0, rY: 0.0, rZ: 0.8870109, rW: 0.4617487 },
						{ x: 467.446838, y: -1170.59351, z: 29.8309059, rX: 0.0, rY: 0.0, rZ: 0.7071066, rW: 0.7071069 },
						{ x: 350.7822, y: -1169.75122, z: 30.2301712, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: 244.965012, y: -1173.75708, z: 29.8993435, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.0436195 },
						{ x: 458.208923, y: -1060.92639, z: 29.44031, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: 418.452759, y: -1088.40088, z: 30.7532883, rX: 0.0, rY: 0.0, rZ: 0.04361933, rW: 0.9990482 },
						{ x: 344.54483, y: -1073.15845, z: 30.2615833, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: 334.343781, y: -1119.11194, z: 29.6473751, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: 253.536041, y: -1112.80664, z: 29.8558464, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361948 },
						{ x: 265.242584, y: -1069.5127, z: 30.047348, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: 276.9133, y: -1002.21887, z: 29.8918781, rX: 0.0, rY: 0.0, rZ: 0.7660444, rW: 0.6427877 },
						{ x: 350.7144, y: -992.2689, z: 30.0586376, rX: 0.0, rY: 0.0, rZ: -0.04361942, rW: 0.9990482 },
						{ x: 291.389435, y: -964.306763, z: 29.9340668, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: 482.984528, y: -979.4547, z: 28.6515121, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361929 },
						{ x: 515.0211, y: -1074.66418, z: 29.3254414, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361948 },
						{ x: 489.113129, y: -918.256042, z: 26.62971, rX: 0.0, rY: 0.0, rZ: 0.7372773, rW: 0.6755903 },
						{ x: 483.486267, y: -851.602844, z: 26.24178, rX: 0.0, rY: 0.0, rZ: -0.04361919, rW: -0.9990482 },
						{ x: 415.749573, y: -908.9135, z: 29.99618, rX: 0.0, rY: 0.0, rZ: 0.04361948, rW: -0.9990482 },
						{ x: 449.01416, y: -919.66, z: 29.0651951, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361929 },
						{ x: 377.826569, y: -899.2762, z: 29.762928, rX: 0.0, rY: 0.0, rZ: 0.7372774, rW: -0.6755902 },
						{ x: 298.690643, y: -890.665833, z: 29.78742, rX: 0.0, rY: 0.0, rZ: 0.8433914, rW: 0.5372997 },
						{ x: 344.211853, y: -940.8512, z: 29.881958, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071067 },
						{ x: 490.875916, y: -782.4332, z: 25.3178825, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071067 },
						{ x: 462.4214, y: -690.7651, z: 28.0906773, rX: 0.0, rY: 0.0, rZ: -0.04361919, rW: -0.9990482 },
						{ x: 417.726929, y: -777.5198, z: 29.88057, rX: 0.0, rY: 0.0, rZ: 8.940695, rW: -1.0 },
						{ x: 451.404541, y: -797.186951, z: 27.9870586, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: 0.4617487 },
						{ x: 363.617554, y: -708.6519, z: 29.6289, rX: 0.0, rY: 0.0, rZ: 0.6087615, rW: -0.7933533 },
						{ x: 323.626526, y: -833.9415, z: 29.7363682, rX: 0.0, rY: 0.0, rZ: 0.7071068, rW: -0.7071067 },
						{ x: 372.5521, y: -813.4112, z: 30.2153, rX: 0.0, rY: 0.0, rZ: -2.980232, rW: 1.0 },
						{ x: 391.578156, y: -733.298, z: 29.7795, rX: 0.0, rY: 0.0, rZ: -8.940697, rW: 1.0 },
						{ x: 544.80365, y: -649.310059, z: 25.1239719, rX: 0.0, rY: 0.0, rZ: 0.04361928, rW: 0.9990483 },
						{ x: 488.324982, y: -630.135, z: 25.34883, rX: 0.0, rY: 0.0, rZ: 0.6755903, rW: -0.7372773 },
						{ x: 547.8334, y: -572.0928, z: 25.3210087, rX: 0.0, rY: 0.0, rZ: -0.7372773, rW: -0.6755903 },
						{ x: 494.365479, y: -564.5299, z: 24.95349, rX: 0.0, rY: 0.0, rZ: 0.7071068, rW: 0.7071068 },
						{ x: 518.08606, y: -469.25647, z: 24.6607132, rX: 0.0, rY: 0.0, rZ: 0.8191521, rW: -0.5735764 },
						{ x: 467.299316, y: -521.8764, z: 25.8674583, rX: 0.0, rY: 0.0, rZ: 0.976296, rW: 0.2164397 },
						{ x: 458.2548, y: -568.405151, z: 29.0684528, rX: 0.0, rY: 0.0, rZ: 0.08715578, rW: 0.9961947 },
						{ x: 436.407227, y: -660.438354, z: 29.3612766, rX: 0.0, rY: 0.0, rZ: 0.6755903, rW: -0.7372773 },
						{ x: 410.047058, y: -537.6771, z: 29.33523, rX: 0.0, rY: 0.0, rZ: 0.7372774, rW: -0.6755902 },
						{ x: 310.710754, y: -551.9735, z: 29.177887, rX: 0.0, rY: 0.0, rZ: 1.0, rW: 9.685753 },
						{ x: 308.059235, y: -621.384644, z: 35.6747169, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305261 },
						{ x: 315.430145, y: -686.423035, z: 29.9008713, rX: 0.0, rY: 0.0, rZ: 0.6087615, rW: -0.7933533 },
						{ x: 245.623459, y: -817.5965, z: 30.5881233, rX: 0.0, rY: 0.0, rZ: -0.173648, rW: -0.9848078 },
						{ x: 236.1623, y: -755.007751, z: 35.5373573, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087615 },
						{ x: 267.623169, y: -646.44165, z: 42.3254662, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: 296.8513, y: -600.084656, z: 43.8198166, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: -0.819152 },
						{ x: 180.926208, y: -873.3333, z: 31.5711956, rX: 0.0, rY: 0.0, rZ: 0.5000001, rW: -0.8660254 },
						{ x: 188.837173, y: -997.4923, z: 29.8197346, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: -0.819152 },
						{ x: 105.44397, y: -1087.635, z: 29.8261642, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: 188.233582, y: -1099.09167, z: 29.5670052, rX: 0.0, rY: 0.0, rZ: 1.564622, rW: -1.0 },
						{ x: 87.66919, y: -1114.23669, z: 29.7603436, rX: 0.0, rY: 0.0, rZ: -0.3007059, rW: 0.9537169 },
						{ x: 130.821243, y: -1153.97888, z: 29.82042, rX: 0.0, rY: 0.0, rZ: 1.0, rW: 9.685753 },
						{ x: 62.5839, y: -1042.0249, z: 30.05026, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: 34.9965134, y: -1035.72, z: 30.0807323, rX: 0.0, rY: 0.0, rZ: -0.2164395, rW: -0.976296 },
						{ x: -20.2502232, y: -1005.04895, z: 29.7438354, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: -41.3188, y: -1085.60815, z: 27.1659031, rX: 0.0, rY: 0.0, rZ: 0.173648, rW: 0.9848078 },
						{ x: -9.038147, y: -1106.13269, z: 29.6010113, rX: 0.0, rY: 0.0, rZ: 0.6087615, rW: -0.7933533 },
						{ x: -72.35981, y: -1172.9696, z: 26.7664433, rX: 0.0, rY: 0.0, rZ: 0.9961947, rW: 0.08715583 },
						{ x: 119.202232, y: -895.3094, z: 31.6857567, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: 91.94348, y: -821.155151, z: 31.91384, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: 68.93992, y: -876.6797, z: 31.2650566, rX: 0.0, rY: 0.0, rZ: -0.8433914, rW: -0.5372998 },
						{ x: 66.91036, y: -957.858948, z: 29.9749851, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: -0.819152 },
						{ x: 161.363647, y: -769.3839, z: 32.59149, rX: 0.0, rY: 0.0, rZ: -0.976296, rW: 0.2164396 },
						{ x: 211.311111, y: -622.057068, z: 41.59209, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: 108.429382, y: -631.42, z: 45.03692, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: 88.5395355, y: -715.066, z: 44.268734, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: 64.2561646, y: -735.3928, z: 31.94976, rX: 0.0, rY: 0.0, rZ: 0.976296, rW: 0.2164397 },
						{ x: 117.361473, y: -600.657532, z: 32.44918, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: 189.119675, y: -605.949951, z: 30.07472, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: 195.349518, y: -561.5338, z: 44.6560974, rX: 0.0, rY: 0.0, rZ: 0.7660444, rW: 0.6427876 },
						{ x: -194.6948, y: -1178.13647, z: 23.5172443, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071067 },
						{ x: -201.333481, y: -1122.45789, z: 23.806673, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071067 },
						{ x: -75.50492, y: -1008.12427, z: 29.7229881, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: -125.958, y: -940.408569, z: 30.0424976, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: -185.1862, y: -994.075256, z: 30.0954037, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: -248.902679, y: -1120.085, z: 23.6016865, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: -0.819152 },
						{ x: -107.771065, y: -893.698242, z: 29.5003281, rX: 0.0, rY: 0.0, rZ: -0.7372773, rW: -0.6755903 },
						{ x: -134.02655, y: -817.8994, z: 32.1562576, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: -40.52286, y: -776.176331, z: 34.00166, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: -17.64754, y: -845.0703, z: 31.3182774, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: -2.66546488, y: -731.531433, z: 33.014595, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: -77.227684, y: -654.8728, z: 36.7197266, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: -13.2756567, y: -559.794, z: 38.2795868, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071067 },
						{ x: 18.7424431, y: -610.065552, z: 32.6873245, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305261 },
						{ x: 41.63031, y: -687.5575, z: 31.9105129, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: 11.7060337, y: -709.7262, z: 46.1409836, rX: 0.0, rY: 0.0, rZ: 0.2588192, rW: -0.9659258 },
						{ x: 11.26533, y: -664.2564, z: 48.533638, rX: 0.0, rY: 0.0, rZ: -0.9848077, rW: 0.1736482 },
						{ x: -81.42151, y: -703.28656, z: 45.1336937, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: -0.819152 },
						{ x: -279.861969, y: -1058.85522, z: 26.4101257, rX: 0.0, rY: 0.0, rZ: -8.195639, rW: 1.0 },
						{ x: -342.711853, y: -1063.4231, z: 23.2843342, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: -0.819152 },
						{ x: -279.960144, y: -1034.69055, z: 30.866396, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: -252.645248, y: -970.569641, z: 31.455183, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: -0.819152 },
						{ x: -276.597015, y: -933.5164, z: 31.5934334, rX: 0.0, rY: 0.0, rZ: -0.1736481, rW: -0.9848078 },
						{ x: -378.761841, y: -859.921143, z: 32.4869347, rX: 0.0, rY: 0.0, rZ: 0.7071068, rW: 0.7071068 },
						{ x: -259.73526, y: -902.437134, z: 32.80279, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: -199.465149, y: -825.246643, z: 31.0978642, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: -204.548187, y: -716.1375, z: 34.79805, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087615 },
						{ x: -245.0786, y: -777.62085, z: 33.41464, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087615 },
						{ x: -260.705658, y: -842.0654, z: 31.68026, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: -148.070816, y: -670.4441, z: 35.300415, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: -0.819152 },
						{ x: -211.044922, y: -597.8194, z: 35.2133026, rX: 0.0, rY: 0.0, rZ: 0.8433914, rW: 0.5372998 },
						{ x: -314.408142, y: -819.402832, z: 33.15524, rX: 0.0, rY: 0.0, rZ: 0.8191521, rW: -0.5735764 },
						{ x: -272.582947, y: -772.0582, z: 32.7623444, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305261 },
						{ x: -263.0135, y: -702.3209, z: 34.7948761, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: 0.1305263 },
						{ x: -324.371582, y: -712.765747, z: 33.4171638, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087615 },
						{ x: -369.724762, y: -640.2465, z: 31.9286919, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071067 },
						{ x: -275.119141, y: -610.0836, z: 33.5962753, rX: 0.0, rY: 0.0, rZ: -0.7071067, rW: -0.7071068 },
						{ x: -551.5058, y: -1273.11377, z: 26.565836, rX: 0.0, rY: 0.0, rZ: 0.8433914, rW: 0.5372997 },
						{ x: -544.6403, y: -1229.32336, z: 18.771925, rX: 0.0, rY: 0.0, rZ: 0.2164395, rW: 0.9762961 },
						{ x: -646.882, y: -1223.02832, z: 11.6893873, rX: 0.0, rY: 0.0, rZ: 0.9396926, rW: 0.3420202 },
						{ x: -568.486145, y: -1071.14429, z: 23.18539, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071067 },
						{ x: -618.59375, y: -1132.30981, z: 22.98145, rX: 0.0, rY: 0.0, rZ: -2.235174, rW: 1.0 },
						{ x: -596.839661, y: -980.767151, z: 22.8396721, rX: 0.0, rY: 0.0, rZ: -0.7071067, rW: -0.7071068 },
						{ x: -604.7451, y: -1042.06555, z: 22.3274517, rX: 0.0, rY: 0.0, rZ: -0.7071067, rW: -0.7071068 },
						{ x: -670.0037, y: -1107.05688, z: 14.8611069, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: -0.819152 },
						{ x: -716.353638, y: -1123.94617, z: 11.433733, rX: 0.0, rY: 0.0, rZ: 0.9537169, rW: 0.3007059 },
						{ x: -683.8634, y: -1166.1239, z: 11.5683336, rX: 0.0, rY: 0.0, rZ: 0.8870109, rW: -0.4617485 },
						{ x: -643.579163, y: -1145.8197, z: 9.727322, rX: 0.0, rY: 0.0, rZ: -0.9848078, rW: 0.1736482 },
						{ x: -522.855, y: -876.9365, z: 25.6801662, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071067 },
						{ x: -579.129944, y: -894.5239, z: 26.7585335, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361929 },
						{ x: -518.9071, y: -858.12915, z: 30.93474, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361948 },
						{ x: -445.3047, y: -794.5055, z: 31.3638763, rX: 0.0, rY: 0.0, rZ: 1.0, rW: 9.685755 },
						{ x: -500.651245, y: -726.050354, z: 34.0377274, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361948 },
						{ x: -464.4131, y: -679.440857, z: 33.26673, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: -529.957458, y: -688.637634, z: 34.1119347, rX: 0.0, rY: 0.0, rZ: 0.6755902, rW: 0.7372774 },
						{ x: -502.9499, y: -628.676941, z: 31.06241, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361929 },
						{ x: -588.7533, y: -612.9906, z: 35.3366928, rX: 0.0, rY: 0.0, rZ: 1.639128, rW: -1.0 },
						{ x: -580.055847, y: -676.5074, z: 33.0108, rX: 0.0, rY: 0.0, rZ: 0.7372773, rW: 0.6755903 },
						{ x: -566.3743, y: -726.863647, z: 33.4808273, rX: 0.0, rY: 0.0, rZ: 1.0, rW: 9.685755 },
						{ x: -595.5672, y: -751.4092, z: 29.6689968, rX: 0.0, rY: 0.0, rZ: 1.0, rW: 9.685755 },
						{ x: -595.5696, y: -784.9122, z: 26.0467663, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: -528.4102, y: -795.9894, z: 31.2057152, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361929 },
						{ x: -673.0593, y: -572.6279, z: 32.2807465, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361929 },
						{ x: -674.1472, y: -631.5375, z: 31.849617, rX: 0.0, rY: 0.0, rZ: 0.6755903, rW: -0.7372773 },
						{ x: -698.5912, y: -589.142334, z: 32.0492554, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.3420201 },
						{ x: -732.385132, y: -581.7073, z: 30.3377113, rX: 0.0, rY: 0.0, rZ: 0.6755902, rW: 0.7372774 },
						{ x: -726.5063, y: -681.2467, z: 30.6256428, rX: 0.0, rY: 0.0, rZ: -0.9238796, rW: 0.3826834 },
						{ x: -730.831848, y: -723.146851, z: 29.5425529, rX: 0.0, rY: 0.0, rZ: 0.7372774, rW: -0.6755902 },
						{ x: -665.547058, y: -708.6102, z: 27.1407967, rX: 0.0, rY: 0.0, rZ: 1.0, rW: 9.685755 },
						{ x: -658.455933, y: -812.1237, z: 25.212801, rX: 0.0, rY: 0.0, rZ: -0.9396926, rW: 0.3420202 },
						{ x: -732.9513, y: -800.9504, z: 23.9183636, rX: 0.0, rY: 0.0, rZ: 1.639128, rW: -1.0 },
						{ x: -724.391541, y: -742.0221, z: 27.0511055, rX: 0.0, rY: 0.0, rZ: 1.192093, rW: -1.0 },
						{ x: -728.3717, y: -881.8367, z: 23.2523346, rX: 0.0, rY: 0.0, rZ: -2.980232, rW: 1.0 },
						{ x: -696.910339, y: -925.1296, z: 19.6823235, rX: 0.0, rY: 0.0, rZ: 1.639128, rW: -1.0 },
						{ x: -680.122559, y: -875.82196, z: 25.0780315, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071067 },
						{ x: -716.1521, y: -875.132935, z: 24.27925, rX: 0.0, rY: 0.0, rZ: 1.0, rW: 9.685755 },
						{ x: -684.448, y: -987.4605, z: 20.9300632, rX: 0.0, rY: 0.0, rZ: 0.4617485, rW: 0.8870109 },
						{ x: -735.931, y: -1026.88525, z: 13.6164455, rX: 0.0, rY: 0.0, rZ: 0.2588192, rW: -0.9659258 },
						{ x: -774.3992, y: -1009.75549, z: 13.3657284, rX: 0.0, rY: 0.0, rZ: 0.7933534, rW: -0.6087614 },
						{ x: -785.493652, y: -1049.24927, z: 12.8654432, rX: 0.0, rY: 0.0, rZ: 0.8433915, rW: -0.5372995 },
						{ x: -811.2227, y: -981.942444, z: 14.6305246, rX: 0.0, rY: 0.0, rZ: -0.3007059, rW: 0.9537169 },
						{ x: -763.9011, y: -887.7066, z: 21.4980125, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071067 },
						{ x: -812.5653, y: -859.3555, z: 21.3618984, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: -883.569946, y: -854.4606, z: 19.3700848, rX: 0.0, rY: 0.0, rZ: 0.7933534, rW: -0.6087614 },
						{ x: -773.6692, y: -726.7733, z: 28.3133049, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: -799.1722, y: -685.0796, z: 29.8593, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: -808.9149, y: -732.6848, z: 28.6037178, rX: 0.0, rY: 0.0, rZ: -0.04361939, rW: 0.9990482 },
						{ x: -799.82135, y: -800.047058, z: 21.4327755, rX: -0.07391417, rY: 0.03249338, rZ: -0.01066711, rW: 0.9966781 },
						{ x: -951.3548, y: -710.783569, z: 20.5152149, rX: 0.0, rY: 0.0, rZ: -0.7071067, rW: -0.7071068 },
						{ x: -1021.324, y: -778.6041, z: 16.673275, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: -0.258819 },
						{ x: -992.051331, y: -723.864136, z: 21.3640938, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361929 },
						{ x: -786.5605, y: -600.3654, z: 30.84773, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087615 },
						{ x: -1060.2417, y: -829.1559, z: 19.6508369, rX: 0.0, rY: 0.0, rZ: 0.9537169, rW: 0.3007059 },
						{ x: -1094.69983, y: -894.0122, z: 4.076605, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588191 },
						{ x: -1174.77539, y: -898.4749, z: 14.0413857, rX: 0.0, rY: 0.0, rZ: 0.8870109, rW: -0.4617485 },
						{ x: -1203.14514, y: -959.284363, z: 2.68165159, rX: 0.0, rY: 0.0, rZ: 0.2588192, rW: -0.9659258 },
						{ x: -1146.2998, y: -924.254761, z: 3.23556352, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588191 },
						{ x: -824.3637, y: -1066.87781, z: 11.9876013, rX: 0.0, rY: 0.0, rZ: 0.4999999, rW: 0.8660255 },
						{ x: -1217.43311, y: -1058.01086, z: 8.969485, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: 0.1305263 },
						{ x: -1212.68115, y: -1103.39587, z: 7.603066, rX: 0.0, rY: 0.0, rZ: 0.8191521, rW: -0.5735763 },
						{ x: -1182.31909, y: -1133.363, z: 6.09802961, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: 0.8191521 },
						{ x: -1176.38953, y: -1174.27991, z: 6.276095, rX: 0.0, rY: 0.0, rZ: 0.8191521, rW: -0.5735763 },
						{ x: -1155.41809, y: -1248.30554, z: 7.262793, rX: 0.0, rY: 0.0, rZ: 0.5372995, rW: 0.8433915 },
						{ x: -1096.12854, y: -1257.491, z: 6.01268435, rX: 0.0, rY: 0.0, rZ: 0.4999999, rW: 0.8660255 },
						{ x: -993.312, y: -1202.22925, z: 6.01692724, rX: 0.0, rY: 0.0, rZ: 0.4999999, rW: 0.8660255 },
						{ x: -1033.33765, y: -1232.97656, z: 6.17382, rX: 0.0, rY: 0.0, rZ: 0.2588192, rW: -0.9659258 },
						{ x: -917.6722, y: -1165.58057, z: 5.42874432, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588191 },
						{ x: -888.0779, y: -1159.33044, z: 5.899598, rX: 0.0, rY: 0.0, rZ: 0.8660254, rW: -0.4999999 },
						{ x: -836.9821, y: -1118.50513, z: 7.474372, rX: 0.0, rY: 0.0, rZ: 0.2588192, rW: -0.9659258 },
						{ x: -1210.2533, y: -724.8137, z: 22.460247, rX: 0.0, rY: 0.0, rZ: 0.9537169, rW: 0.3007059 },
						{ x: -1281.09509, y: -683.5511, z: 25.8151035, rX: 0.0, rY: 0.0, rZ: 0.3826835, rW: -0.9238795 },
						{ x: -1352.354, y: -630.990356, z: 28.2736244, rX: 0.0, rY: 0.0, rZ: 0.9537169, rW: 0.3007059 },
						{ x: -1355.67725, y: -572.1854, z: 30.4461784, rX: 0.0, rY: 0.0, rZ: 0.3007059, rW: -0.9537169 },
						{ x: -1305.30945, y: -612.0139, z: 28.18868, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: -0.4226182 },
						{ x: -1291.01672, y: -559.012634, z: 32.2166138, rX: 0.0, rY: 0.0, rZ: 0.9396926, rW: 0.3420202 },
						{ x: -1187.4071, y: -668.023254, z: 22.94118, rX: -0.009264118, rY: -0.01742909, rZ: 0.9428059, rW: 0.3327575 },
						{ x: -1225.83765, y: -853.942261, z: 13.555357, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: -0.4226182 },
						{ x: -1258.67224, y: -820.761353, z: 17.3804569, rX: 0.0, rY: 0.0, rZ: -0.9238796, rW: 0.3826834 },
						{ x: -1336.423, y: -764.3341, z: 20.7896938, rX: 0.0, rY: 0.0, rZ: 0.4226182, rW: 0.9063078 },
						{ x: -1390.5011, y: -655.0254, z: 29.02509, rX: 0.0, rY: 0.0, rZ: 0.4226182, rW: 0.9063079 },
						{ x: -1472.74646, y: -637.894165, z: 31.2942886, rX: 0.0, rY: 0.0, rZ: 0.3826833, rW: 0.9238796 },
						{ x: -1459.46082, y: -688.386353, z: 26.4729443, rX: 0.0, rY: 0.0, rZ: 0.9238795, rW: 0.3826835 },
						{ x: -1015.56439, y: -508.403625, z: 37.6058235, rX: 0.0, rY: 0.0, rZ: 0.2588192, rW: -0.9659258 },
						{ x: -963.136169, y: -511.496979, z: 37.56222, rX: 0.0, rY: 0.0, rZ: 0.2588192, rW: -0.9659258 },
						{ x: -1001.21088, y: -467.222748, z: 37.9883423, rX: 0.0, rY: 0.0, rZ: 0.5372995, rW: 0.8433915 },
						{ x: -1005.41437, y: -390.9631, z: 38.2476463, rX: 0.0, rY: 0.0, rZ: 0.5372995, rW: 0.8433915 },
						{ x: -1039.78955, y: -430.323822, z: 39.80654, rX: 0.0, rY: 0.0, rZ: -0.2588191, rW: 0.9659258 },
						{ x: -1136.25488, y: -427.0835, z: 36.4379959, rX: 0.0, rY: 0.0, rZ: 0.6755902, rW: 0.7372774 },
						{ x: -1058.31213, y: -303.857361, z: 38.55186, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.04361929 },
						{ x: -1155.06335, y: -387.158264, z: 36.6541939, rX: 0.0, rY: 0.0, rZ: 0.7372774, rW: -0.6755902 },
						{ x: -1171.78259, y: -323.142273, z: 37.8166542, rX: 0.0, rY: 0.0, rZ: 0.6755903, rW: -0.7372773 },
						{ x: -1248.36365, y: -403.785583, z: 35.5411034, rX: 0.0, rY: 0.0, rZ: 0.2164397, rW: -0.9762959 },
						{ x: -1065.20215, y: -234.887863, z: 38.62492, rX: 0.0, rY: 0.0, rZ: -0.5372996, rW: -0.8433915 },
						{ x: -1301.92224, y: -445.293579, z: 35.128624, rX: 0.0, rY: 0.0, rZ: 0.3007059, rW: -0.9537169 },
						{ x: -1289.56116, y: -428.016, z: 34.8861237, rX: 0.0, rY: 0.0, rZ: 0.9537169, rW: 0.3007059 },
						{ x: -1622.58948, y: -508.518066, z: 35.73135, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226184 },
						{ x: -1669.37439, y: -526.1242, z: 37.01295, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -1550.858, y: -467.027344, z: 36.4659233, rX: 0.0, rY: 0.0, rZ: 0.6087615, rW: -0.7933533 },
						{ x: -1513.39246, y: -411.83075, z: 36.18908, rX: 0.0, rY: 0.0, rZ: 0.4226184, rW: -0.9063077 },
						{ x: -1567.455, y: -371.136, z: 46.4238548, rX: 0.0, rY: 0.0, rZ: 0.3420202, rW: 0.9396927 },
						{ x: -1571.89282, y: -423.756256, z: 38.6400223, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226184 },
						{ x: -1595.33862, y: -461.204529, z: 38.0731, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -1570.44055, y: -278.562927, z: 49.1045151, rX: 0.0, rY: 0.0, rZ: -0.3826835, rW: 0.9238796 },
						{ x: -1538.76306, y: -240.1935, z: 52.48968, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: 0.3420202 },
						{ x: -1394.8905, y: -342.764343, z: 39.60602, rX: 0.0, rY: 0.0, rZ: 0.4617485, rW: 0.8870109 },
						{ x: -1379.415, y: -289.731262, z: 43.9101372, rX: 0.0, rY: 0.0, rZ: 0.3826833, rW: 0.9238796 },
						{ x: -1344.79675, y: -312.007416, z: 39.4390144, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588192 },
						{ x: -1423.10742, y: -250.472656, z: 47.2468529, rX: 0.0, rY: 0.0, rZ: 0.9396926, rW: 0.3420203 },
						{ x: -1450.12378, y: -223.79451, z: 49.277935, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -1489.66467, y: -147.487747, z: 53.2675056, rX: 0.0, rY: 0.0, rZ: 0.4226182, rW: 0.9063078 },
						{ x: -1503.83435, y: -188.531647, z: 51.5346375, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: -0.4226182 },
						{ x: -1476.56287, y: -323.102661, z: 45.5913, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: -0.2588189 },
						{ x: -1469.878, y: -347.189056, z: 44.8176155, rX: 0.0, rY: 0.0, rZ: -0.3420202, rW: 0.9396927 },
						{ x: -1430.8656, y: -392.715271, z: 36.7680168, rX: 0.0, rY: 0.0, rZ: -0.2588191, rW: 0.9659258 },
						{ x: -1461.21191, y: -378.19873, z: 39.5212936, rX: 0.0, rY: 0.0, rZ: 0.9238794, rW: 0.3826835 },
						{ x: -1515.99683, y: -308.463226, z: 48.6372375, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226184 },
						{ x: -1530.52979, y: -341.202332, z: 46.2013969, rX: 0.0, rY: 0.0, rZ: -0.9238796, rW: 0.3826835 },
						{ x: -1424.41931, y: -98.51427, z: 52.5269928, rX: 0.0, rY: 0.0, rZ: -0.5372996, rW: -0.8433914 },
						{ x: -1416.37085, y: -142.598648, z: 49.453743, rX: 0.0, rY: 0.0, rZ: -0.1736483, rW: 0.9848077 },
						{ x: -1348.24475, y: -216.1784, z: 44.4028931, rX: 0.0, rY: 0.0, rZ: 0.4617485, rW: 0.8870109 },
						{ x: -1299.81946, y: -285.32666, z: 39.761013, rX: 0.0, rY: 0.0, rZ: -0.5372996, rW: -0.8433914 },
						{ x: -1239.03357, y: -258.8584, z: 39.362896, rX: 0.0, rY: 0.0, rZ: -0.5372996, rW: -0.8433914 },
						{ x: -1160.90039, y: -229.220444, z: 38.40408, rX: 0.0, rY: 0.0, rZ: 0.9396926, rW: 0.3420202 },
						{ x: -1146.56348, y: -165.838165, z: 40.67059, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: -1230.53, y: -123.168655, z: 42.2439156, rX: 0.0, rY: 0.0, rZ: 0.9396927, rW: -0.34202 },
						{ x: -1287.44336, y: -101.24971, z: 47.51248, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -1372.81287, y: -128.174744, z: 50.5742722, rX: 0.0, rY: 0.0, rZ: 0.08715568, rW: 0.9961947 },
						{ x: -1359.31067, y: -213.328339, z: 43.9987, rX: 0.0, rY: 0.0, rZ: -0.8870108, rW: 0.4617485 },
						{ x: -1244.85254, y: -262.01297, z: 39.6248055, rX: 0.0, rY: 0.0, rZ: 0.5372996, rW: 0.8433915 },
						{ x: -1298.82556, y: -27.7254429, z: 49.81213, rX: 0.0, rY: 0.0, rZ: -0.3420202, rW: 0.9396927 },
						{ x: -1374.91125, y: 139.80838, z: 56.584198, rX: 0.0, rY: 0.0, rZ: -0.6755902, rW: -0.7372774 },
						{ x: -958.7649, y: -189.3709, z: 38.26118, rX: 0.0, rY: 0.0, rZ: -0.2588192, rW: 0.9659258 },
						{ x: -889.1984, y: -144.789322, z: 38.15064, rX: 0.0, rY: 0.0, rZ: -0.8433915, rW: 0.5372995 },
						{ x: -800.9279, y: -95.04651, z: 37.72116, rX: 0.0, rY: 0.0, rZ: -0.5372996, rW: -0.8433914 },
						{ x: -731.7893, y: -52.016037, z: 38.12591, rX: 0.0, rY: 0.0, rZ: -0.5372996, rW: -0.8433914 },
						{ x: -823.064941, y: -433.5218, z: 36.9881668, rX: 0.0, rY: 0.0, rZ: -0.8660254, rW: 0.5 },
						{ x: -915.206055, y: -430.9568, z: 38.751915, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087614 },
						{ x: -790.3487, y: -393.714966, z: 36.4388237, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: -772.1783, y: -349.66983, z: 37.3750534, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: -871.5748, y: -308.768616, z: 40.01807, rX: 0.0, rY: 0.0, rZ: 0.04361931, rW: 0.9990483 },
						{ x: -935.6235, y: -372.89444, z: 39.01363, rX: 0.0, rY: 0.0, rZ: 0.5, rW: 0.8660255 },
						{ x: -989.549866, y: -284.134979, z: 38.7131424, rX: 0.0, rY: 0.0, rZ: -0.2588192, rW: 0.9659258 },
						{ x: -937.315369, y: -287.153381, z: 39.6504021, rX: 0.0, rY: 0.0, rZ: 0.9914448, rW: 0.1305262 },
						{ x: -977.5387, y: -246.070724, z: 38.8317947, rX: 0.0, rY: 0.0, rZ: 0.8660253, rW: 0.5000001 },
						{ x: -894.8879, y: -235.512009, z: 40.81809, rX: 0.0, rY: 0.0, rZ: 0.258819, rW: 0.9659258 },
						{ x: -830.959961, y: -246.826111, z: 38.26764, rX: 0.0, rY: 0.0, rZ: 0.9762959, rW: 0.2164398 },
						{ x: -880.0945, y: -196.3706, z: 38.9347267, rX: 0.0, rY: 0.0, rZ: -0.2588191, rW: 0.9659259 },
						{ x: -911.04364, y: -222.021545, z: 40.4932556, rX: 0.0, rY: 0.0, rZ: -0.8191521, rW: 0.5735764 },
						{ x: -772.2904, y: -241.770569, z: 37.7726021, rX: 0.0, rY: 0.0, rZ: 0.9848077, rW: 0.1736482 },
						{ x: -778.6894, y: -137.511337, z: 38.60318, rX: 0.0, rY: 0.0, rZ: 0.953717, rW: 0.3007059 },
						{ x: -737.0656, y: -229.980576, z: 38.0477371, rX: 0.0, rY: 0.0, rZ: -0.8191521, rW: 0.5735764 },
						{ x: -746.022034, y: -314.642822, z: 37.0071831, rX: 0.0, rY: 0.0, rZ: -0.9848078, rW: 0.1736481 },
						{ x: -667.8181, y: -327.240662, z: 35.8024178, rX: 0.0, rY: 0.0, rZ: -0.8870109, rW: 0.4617486 },
						{ x: -738.2168, y: -283.282867, z: 37.59667, rX: 0.0, rY: 0.0, rZ: 0.9848077, rW: 0.1736482 },
						{ x: -674.9737, y: -432.369751, z: 35.3277969, rX: 0.0, rY: 0.0, rZ: -0.7372774, rW: 0.6755902 },
						{ x: -554.5241, y: -457.6069, z: 35.109005, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: -0.7071068 },
						{ x: -567.682739, y: -394.6208, z: 36.0413361, rX: 0.0, rY: 0.0, rZ: -5.960465, rW: 1.0 },
						{ x: -471.7725, y: -448.181671, z: 35.1701965, rX: 0.0, rY: 0.0, rZ: -0.6427877, rW: 0.7660444 },
						{ x: -424.63974, y: -454.8137, z: 32.45936, rX: 0.0, rY: 0.0, rZ: -0.6427877, rW: 0.7660444 },
						{ x: -369.240143, y: -421.320221, z: 31.9587574, rX: 0.0, rY: 0.0, rZ: 0.04361931, rW: 0.9990483 },
						{ x: -309.1476, y: -433.995667, z: 32.6182327, rX: 0.0, rY: 0.0, rZ: 0.04361931, rW: 0.9990483 },
						{ x: -396.8891, y: -317.473, z: 35.0901031, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -463.527649, y: -278.163666, z: 36.4838371, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: -0.819152 },
						{ x: -462.3399, y: -352.9083, z: 35.081913, rX: 0.0, rY: 0.0, rZ: 0.7660444, rW: 0.6427876 },
						{ x: -516.133362, y: -328.363281, z: 35.9411278, rX: 0.0, rY: 0.0, rZ: 0.5, rW: 0.8660255 },
						{ x: -595.9726, y: -351.075531, z: 35.43, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: -555.6537, y: -326.130768, z: 35.89861, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588191 },
						{ x: -324.676147, y: -252.664734, z: 34.7584457, rX: 0.0, rY: 0.0, rZ: -0.4617487, rW: 0.8870109 },
						{ x: -330.488281, y: -216.1015, z: 38.46976, rX: 0.0, rY: 0.0, rZ: 0.9063078, rW: 0.4226184 },
						{ x: -365.9394, y: -239.461548, z: 36.77864, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -306.0974, y: -122.739479, z: 46.6395454, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: -316.8359, y: -55.5233955, z: 49.771225, rX: 0.0, rY: 0.0, rZ: 0.8433915, rW: -0.5372996 },
						{ x: -404.919434, y: -75.88335, z: 44.1783524, rX: 0.0, rY: 0.0, rZ: 0.8433915, rW: 0.5372997 },
						{ x: -453.863617, y: -17.9908752, z: 46.6141052, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: -506.929871, y: -42.648716, z: 44.98215, rX: 0.0, rY: 0.0, rZ: -0.6755903, rW: 0.7372773 },
						{ x: -453.087952, y: -84.01386, z: 40.9795151, rX: 0.0, rY: 0.0, rZ: -0.9063079, rW: 0.4226182 },
						{ x: -421.74115, y: -187.940918, z: 37.2875252, rX: 0.0, rY: 0.0, rZ: -0.8660254, rW: 0.4999999 },
						{ x: -405.106964, y: -120.502686, z: 39.01878, rX: 0.0, rY: 0.0, rZ: -0.8660255, rW: 0.4999999 },
						{ x: -554.7338, y: -171.323822, z: 39.49048, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588191 },
						{ x: -589.1496, y: -210.496292, z: 37.8774529, rX: 0.0, rY: 0.0, rZ: -0.2588191, rW: 0.9659258 },
						{ x: -631.7592, y: -116.278679, z: 38.7788124, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: -623.372253, y: -83.29034, z: 40.4512329, rX: 0.0, rY: 0.0, rZ: 0.04361931, rW: 0.9990483 },
						{ x: -536.85675, y: -119.912888, z: 39.55161, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588191 },
						{ x: -236.735855, y: -311.469025, z: 30.82689, rX: 0.0, rY: 0.0, rZ: -5.960465, rW: 1.0 },
						{ x: -152.4593, y: -295.425323, z: 40.2111435, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087614 },
						{ x: -143.006973, y: -108.495674, z: 55.844738, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: -259.144745, y: -156.5413, z: 41.88424, rX: 0.0, rY: 0.0, rZ: -0.04361942, rW: 0.9990482 },
						{ x: -216.854156, y: -221.241318, z: 50.31132, rX: 0.0, rY: 0.0, rZ: -7.823109, rW: 1.0 },
						{ x: -56.6937637, y: -399.589874, z: 37.6048, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.819152 },
						{ x: -23.9298077, y: -353.263855, z: 40.7859344, rX: 0.0, rY: 0.0, rZ: -0.976296, rW: 0.2164396 },
						{ x: -21.4716549, y: -290.0191, z: 46.6717033, rX: 0.0, rY: 0.0, rZ: 0.7372773, rW: 0.6755903 },
						{ x: -116.20916, y: -318.253723, z: 39.0951157, rX: 0.0, rY: 0.0, rZ: 0.7660444, rW: 0.6427877 },
						{ x: 2.77147365, y: -434.490265, z: 40.35132, rX: 0.0, rY: 0.0, rZ: 0.2164395, rW: 0.976296 },
						{ x: 142.823364, y: -377.512238, z: 43.91876, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: 208.488861, y: -439.4046, z: 43.3564339, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.819152 },
						{ x: 270.2699, y: -430.841675, z: 45.80669, rX: 0.0, rY: 0.0, rZ: 0.9762961, rW: -0.2164395 },
						{ x: 228.990677, y: -394.118256, z: 46.8292351, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: 408.9281, y: -341.388977, z: 47.6521339, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305261 },
						{ x: 323.538147, y: -280.406219, z: 54.8284874, rX: 0.0, rY: 0.0, rZ: -0.9848078, rW: 0.1736482 },
						{ x: 270.9545, y: -313.5815, z: 45.9342728, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.819152 },
						{ x: 220.471054, y: -299.0189, z: 46.63125, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: 254.201462, y: -256.402771, z: 54.943058, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: 172.415878, y: -225.015411, z: 55.22098, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305261 },
						{ x: 100.624275, y: -225.986038, z: 55.1794167, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: 61.733448, y: -269.699768, z: 48.4145775, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.8191521 },
						{ x: 192.375259, y: -278.487579, z: 48.150856, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.819152 },
						{ x: -24.7332726, y: -149.285278, z: 57.67246, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: -52.9730949, y: -181.559235, z: 53.0322762, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.819152 },
						{ x: -26.7217, y: -226.8632, z: 46.68627, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: 29.63954, y: -205.512085, z: 53.65649, rX: 0.0, rY: 0.0, rZ: -0.5372997, rW: 0.8433915 },
						{ x: 431.390625, y: -160.793915, z: 64.4252548, rX: 0.0, rY: 0.0, rZ: 0.7660444, rW: 0.6427876 },
						{ x: 412.994324, y: -216.6626, z: 60.2891159, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087614 },
						{ x: 396.28653, y: -261.739868, z: 54.1227341, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.819152 },
						{ x: 542.809631, y: -192.0523, z: 54.8516579, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: 549.9629, y: -151.979813, z: 57.4160461, rX: 0.0, rY: 0.0, rZ: 0.7071067, rW: 0.7071068 },
						{ x: 346.413666, y: -138.336472, z: 65.54229, rX: 0.0, rY: 0.0, rZ: 0.9961947, rW: -0.08715566 },
						{ x: -1682.49463, y: -461.8739, z: 40.4032059, rX: 0.0, rY: 0.0, rZ: 0.3420201, rW: 0.9396927 },
						{ x: -1723.5592, y: -473.1043, z: 41.9733543, rX: 0.0, rY: 0.0, rZ: -0.04361945, rW: 0.9990482 },
						{ x: 291.939423, y: -140.853027, z: 67.40537, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: 293.893982, y: -223.067673, z: 54.07645, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.8191521 },
						{ x: 352.718079, y: -187.366165, z: 58.9070053, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.8191521 },
						{ x: 236.3705, y: -105.258453, z: 70.53342, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: 207.219315, y: -103.772743, z: 69.08835, rX: 0.0, rY: 0.0, rZ: -0.6087615, rW: 0.7933533 },
						{ x: 163.186569, y: -108.86145, z: 62.82386, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.8191521 },
						{ x: 109.4564, y: -143.327209, z: 55.1846046, rX: 0.0, rY: 0.0, rZ: 0.1305262, rW: 0.9914449 },
						{ x: 123.319374, y: -83.65541, z: 64.59239, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: 98.27587, y: -43.2955856, z: 68.5528946, rX: 0.0, rY: 0.0, rZ: -0.1736481, rW: -0.9848078 },
						{ x: 57.25096, y: -54.7903328, z: 70.14021, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: 31.9484158, y: -101.731918, z: 56.68652, rX: 0.0, rY: 0.0, rZ: -0.2164396, rW: -0.9762961 },
						{ x: 73.06808, y: -122.152092, z: 56.7981949, rX: 0.0, rY: 0.0, rZ: 0.9961947, rW: -0.08715569 },
						{ x: 89.8113556, y: -74.70733, z: 64.33334, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305262 },
						{ x: 12.0495262, y: -9.89769, z: 70.67147, rX: 0.0, rY: 0.0, rZ: -0.1736481, rW: -0.9848078 },
						{ x: -39.6598053, y: -56.380188, z: 64.13078, rX: 0.0, rY: 0.0, rZ: -0.1736481, rW: -0.9848078 },
						{ x: -25.1200027, y: -93.96099, z: 57.8581657, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: 9.161605, y: -55.145546, z: 64.06787, rX: 0.0, rY: 0.0, rZ: -0.6087615, rW: 0.7933533 },
						{ x: 469.257751, y: -112.343864, z: 63.3929138, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087615 },
						{ x: 537.5536, y: -26.7965717, z: 71.8843155, rX: 0.0, rY: 0.0, rZ: -0.2588191, rW: 0.9659258 },
						{ x: 645.742065, y: 11.1197767, z: 84.00666, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.8191521 },
						{ x: 510.203, y: -30.0237465, z: 78.8036652, rX: 0.0, rY: 0.0, rZ: -0.5000001, rW: 0.8660253 },
						{ x: 386.1396, y: -73.31602, z: 68.8502655, rX: 0.0, rY: 0.0, rZ: -0.5735764, rW: 0.8191521 },
						{ x: 454.9278, y: 26.08652, z: 88.2968445, rX: 0.0, rY: 0.0, rZ: -0.5, rW: 0.8660254 },
						{ x: 434.8806, y: 86.41314, z: 100.115845, rX: 0.0, rY: 0.0, rZ: 0.1305262, rW: 0.9914449 },
						{ x: 362.649048, y: 75.03793, z: 98.54539, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.819152 },
						{ x: 324.387329, y: -6.450156, z: 80.69588, rX: 0.0, rY: 0.0, rZ: 0.1736481, rW: 0.9848078 },
						{ x: 332.051758, y: -71.08855, z: 72.97588, rX: 0.0, rY: 0.0, rZ: -0.6087614, rW: 0.7933533 },
						{ x: 276.1758, y: -46.4351921, z: 72.45081, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: 249.703857, y: 6.8865, z: 81.0618439, rX: 0.0, rY: 0.0, rZ: -0.6087614, rW: 0.7933534 },
						{ x: 311.580444, y: 49.64996, z: 90.94118, rX: 0.0, rY: 0.0, rZ: 0.5735765, rW: -0.8191521 },
						{ x: 319.592316, y: 98.65021, z: 100.178192, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: 272.304352, y: 141.099518, z: 104.6248, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: 201.709564, y: 146.386887, z: 103.586227, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: 182.097885, y: 68.96266, z: 84.17769, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.8191521 },
						{ x: 187.8981, y: -28.3770828, z: 68.9552155, rX: 0.0, rY: 0.0, rZ: 0.1305262, rW: 0.9914449 },
						{ x: 228.015335, y: -22.9024067, z: 69.97341, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.8191521 },
						{ x: 282.5329, y: -26.2230415, z: 73.99451, rX: 0.0, rY: 0.0, rZ: -0.6087614, rW: 0.7933534 },
						{ x: 239.099243, y: 51.56797, z: 85.78695, rX: 0.0, rY: 0.0, rZ: 0.1305262, rW: 0.9914449 },
						{ x: 141.249512, y: 193.22049, z: 107.236984, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: 32.6184044, y: 211.682556, z: 107.926407, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: 7.03374958, y: 136.354431, z: 90.0190659, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.8191521 },
						{ x: 22.93339, y: 34.65419, z: 71.3973, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736482 },
						{ x: 78.86111, y: 13.8313541, z: 69.47989, rX: 0.0, rY: 0.0, rZ: 0.1305262, rW: 0.9914449 },
						{ x: 145.0576, y: 92.61837, z: 85.3285, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.819152 },
						{ x: 164.861938, y: 150.398041, z: 102.474884, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: -86.72481, y: 83.28213, z: 72.23193, rX: 0.0, rY: 0.0, rZ: 0.9659259, rW: -0.258819 },
						{ x: -34.1446762, y: 168.624542, z: 95.4635, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305262 },
						{ x: -16.3054428, y: 218.9784, z: 107.547585, rX: 0.0, rY: 0.0, rZ: -0.6427876, rW: 0.7660444 },
						{ x: -48.0184174, y: 241.975525, z: 105.638405, rX: 0.0, rY: 0.0, rZ: 0.08715572, rW: 0.9961947 },
						{ x: -82.07224, y: 215.471909, z: 97.0585861, rX: 0.0, rY: 0.0, rZ: 0.7071068, rW: -0.7071068 },
						{ x: -62.5200233, y: 182.313034, z: 88.0437241, rX: 0.0, rY: 0.0, rZ: 0.4617486, rW: 0.8870109 },
						{ x: -104.557167, y: 25.3674011, z: 72.52167, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.819152 },
						{ x: -212.1078, y: 54.70783, z: 64.67192, rX: 0.0, rY: 0.0, rZ: 0.1305262, rW: 0.9914449 },
						{ x: -224.791931, y: -34.84906, z: 50.42882, rX: 0.0, rY: 0.0, rZ: -0.6087614, rW: 0.7933534 },
						{ x: -159.069016, y: -32.84286, z: 53.55317, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: -85.09534, y: -64.87466, z: 58.6566162, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087615 },
						{ x: -64.6954041, y: 9.27843, z: 72.72515, rX: 0.0, rY: 0.0, rZ: -0.5372996, rW: 0.8433914 },
						{ x: -153.301971, y: -17.3005486, z: 58.9285851, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305261 },
						{ x: 683.5077, y: 78.59007, z: 84.06861, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071067 },
						{ x: 661.783569, y: 153.293076, z: 94.2241745, rX: 0.0, rY: 0.0, rZ: -0.1736481, rW: -0.9848078 },
						{ x: 563.9551, y: 171.268265, z: 101.019188, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: 592.0453, y: 91.20165, z: 95.28675, rX: 0.0, rY: 0.0, rZ: 0.2164396, rW: 0.9762961 },
						{ x: 523.5706, y: 163.305237, z: 99.62536, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: 420.1445, y: 221.513336, z: 104.1016, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: 393.103241, y: 179.311539, z: 104.198723, rX: 0.0, rY: 0.0, rZ: -0.6087614, rW: 0.7933534 },
						{ x: 412.687653, y: 146.663788, z: 103.638657, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: 485.263947, y: 118.727966, z: 97.93628, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.8191521 },
						{ x: 352.194855, y: 281.2411, z: 103.879982, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: 265.64386, y: 311.080261, z: 106.1286, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: 235.18721, y: 233.197281, z: 106.167755, rX: 0.0, rY: 0.0, rZ: 0.2164396, rW: 0.9762961 },
						{ x: 288.05307, y: 193.582565, z: 105.082954, rX: 0.0, rY: 0.0, rZ: 0.7933533, rW: 0.6087615 },
						{ x: 370.270416, y: 207.818, z: 103.492813, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: -0.1736481 },
						{ x: 202.182327, y: 337.349976, z: 106.138542, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: 115.722206, y: 334.793274, z: 112.604637, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: 142.212555, y: 301.933075, z: 110.987564, rX: 0.0, rY: 0.0, rZ: 0.6427878, rW: -0.7660444 },
						{ x: 97.86, y: 269.556366, z: 111.096359, rX: 0.0, rY: 0.0, rZ: 0.2164396, rW: 0.9762961 },
						{ x: 142.081879, y: 241.287659, z: 108.019531, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.819152 },
						{ x: 212.021423, y: 284.398743, z: 106.270317, rX: 0.0, rY: 0.0, rZ: 0.819152, rW: 0.5735765 },
						{ x: -120.86969, y: 223.4269, z: 95.9337845, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: 0.04361939 },
						{ x: -142.087112, y: 149.809357, z: 77.7077255, rX: 0.0, rY: 0.0, rZ: -0.5735765, rW: 0.8191521 },
						{ x: -181.619781, y: 129.304, z: 70.4183044, rX: 0.0, rY: 0.0, rZ: 0.1736482, rW: 0.9848078 },
						{ x: -209.3437, y: 176.68187, z: 77.06942, rX: 0.0, rY: 0.0, rZ: 0.04361942, rW: 0.9990482 },
						{ x: -187.496231, y: 202.890656, z: 86.9779, rX: 0.0, rY: 0.0, rZ: 0.7071068, rW: 0.7071068 },
						{ x: -154.0528, y: 229.303177, z: 95.58511, rX: 0.0, rY: 0.0, rZ: 0.7071068, rW: 0.7071068 },
						{ x: -277.481445, y: 196.629288, z: 86.14093, rX: 0.0, rY: 0.0, rZ: 0.6755902, rW: 0.7372774 },
						{ x: -284.215, y: 144.18866, z: 68.8949, rX: 0.0, rY: 0.0, rZ: -0.7372774, rW: 0.6755902 },
						{ x: -344.499054, y: 144.138687, z: 66.9010849, rX: 0.0, rY: 0.0, rZ: 0.7071069, rW: -0.7071067 },
						{ x: -381.7185, y: 143.21965, z: 66.03697, rX: 0.0, rY: 0.0, rZ: 0.6427876, rW: 0.7660446 },
						{ x: -527.629333, y: 156.132935, z: 64.8767853, rX: 0.0, rY: 0.0, rZ: 0.04361936, rW: 0.9990483 },
						{ x: -486.367584, y: 219.38533, z: 84.27648, rX: 0.0, rY: 0.0, rZ: 1.0, rW: 2.980232 },
						{ x: -395.2453, y: 194.4489, z: 83.50271, rX: 0.0, rY: 0.0, rZ: -0.7372773, rW: 0.6755903 },
						{ x: -320.533966, y: 220.026031, z: 88.32983, rX: 0.0, rY: 0.0, rZ: -0.1305262, rW: 0.9914449 },
						{ x: -516.8017, y: 102.6603, z: 63.42016, rX: 0.0, rY: 0.0, rZ: 0.04361936, rW: 0.9990483 },
						{ x: -535.458, y: 35.13279, z: 45.15244, rX: 0.0, rY: 0.0, rZ: 0.6755903, rW: -0.7372773 },
						{ x: -437.722351, y: 23.1058674, z: 46.48053, rX: 0.0, rY: 0.0, rZ: -0.6755903, rW: 0.7372773 },
						{ x: -404.491547, y: 64.45683, z: 56.98584, rX: 0.0, rY: 0.0, rZ: 0.8433915, rW: -0.5372996 },
						{ x: -450.1102, y: 95.59917, z: 63.5868378, rX: 0.0, rY: 0.0, rZ: 0.7372773, rW: 0.6755902 },
						{ x: -632.914063, y: 97.2095642, z: 54.823925, rX: 0.0, rY: 0.0, rZ: -0.7071067, rW: 0.7071068 },
						{ x: -599.839966, y: 32.772583, z: 43.96676, rX: 0.0, rY: 0.0, rZ: 1.0, rW: 2.980232 },
						{ x: -562.2386, y: 88.1070557, z: 59.3295, rX: 0.0, rY: 0.0, rZ: 1.0, rW: 2.980232 },
						{ x: -601.075439, y: 114.153839, z: 60.2217941, rX: 0.0, rY: 0.0, rZ: 0.7372773, rW: 0.6755902 },
						{ x: -723.424561, y: 36.0265236, z: 43.62177, rX: 0.0, rY: 0.0, rZ: 0.8191521, rW: -0.5735764 },
						{ x: -720.118042, y: 92.09983, z: 56.0964775, rX: 0.0, rY: 0.0, rZ: 0.5735764, rW: 0.8191521 },
						{ x: -675.5224, y: 81.26258, z: 51.692585, rX: 0.0, rY: 0.0, rZ: 0.976296, rW: 0.2164397 },
						{ x: -636.1123, y: 209.521866, z: 74.80183, rX: 0.0, rY: 0.0, rZ: -0.7071067, rW: 0.7071068 },
						{ x: -569.941956, y: 167.659241, z: 67.11883, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.0436193 },
						{ x: -574.604553, y: 238.547684, z: 83.02462, rX: 0.0, rY: 0.0, rZ: 0.7660444, rW: 0.6427876 },
						{ x: -575.954651, y: 311.023315, z: 84.9325943, rX: 0.0, rY: 0.0, rZ: 0.7372773, rW: 0.6755902 },
						{ x: -621.5537, y: 289.926331, z: 82.2603455, rX: 0.0, rY: 0.0, rZ: 0.04361936, rW: 0.9990482 },
						{ x: -702.5879, y: 317.91394, z: 83.63501, rX: 0.0, rY: 0.0, rZ: 0.9990482, rW: -0.0436193 },
						{ x: -782.7463, y: 352.634949, z: 88.520195, rX: 0.0, rY: 0.0, rZ: 0.7071068, rW: 0.7071068 },
						{ x: -779.0236, y: 270.198059, z: 85.94544, rX: 0.0, rY: 0.0, rZ: 0.6427876, rW: 0.7660445 },
						{ x: -738.3455, y: 248.331253, z: 77.41453, rX: 0.0, rY: 0.0, rZ: -0.2164397, rW: 0.9762961 },
						{ x: -726.9572, y: 209.347626, z: 77.04972, rX: 0.0, rY: 0.0, rZ: 0.5372996, rW: 0.8433915 },
						{ x: -735.2343, y: 119.426582, z: 56.00074, rX: 0.0, rY: 0.0, rZ: -0.04361939, rW: 0.9990482 },
						{ x: -661.713745, y: 166.192612, z: 60.037735, rX: 0.0, rY: 0.0, rZ: -7.450581, rW: 1.0 },
						{ x: -1383.92029, y: 245.516464, z: 59.6976662, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: 0.1305262 },
						{ x: 280.3982, y: 862.848145, z: 196.989761, rX: 0.0, rY: 0.0, rZ: -0.9238796, rW: -0.3826834 },
						{ x: 16.06232, y: 636.243835, z: 207.701691, rX: 0.0, rY: 0.0, rZ: 0.7660446, rW: -0.6427876 },
						{ x: -393.489136, y: 876.4639, z: 230.992218, rX: 0.0, rY: 0.0, rZ: 0.8433915, rW: -0.5372996 },
					],
				},
			},
			{
				id: 11,
				name: 'Yankton',
				controller: 'YanktonTeleport',
				config: {
					start: '2025-01-28',
					end: '2025-02-11',
				},
			},
			{
				id: 1,
				name: 'WinterMinigames',
				controller: 'MazeBankMinigames',
				start: '2025-01-28 06:00:00',
				end: '2025-02-11 06:00:00',

				coolDown: 6,

				rewards: [
					{
						rewardId: 49,
						type: awardEnum.seasonPassProgress,
						amount: 400,
						hasOneReceipt: true,
					},
					{
						rewardId: 50,
						type: awardEnum.seasonPassProgress,
						amount: 400,
						hasOneReceipt: true,
					},
					{
						rewardId: 51,
						type: awardEnum.seasonPassProgress,
						amount: 400,
						hasOneReceipt: true,
					},
					{
						rewardId: 52,
						type: awardEnum.seasonPassProgress,
						amount: 400,
						hasOneReceipt: true,
					},
					{
						rewardId: 53,
						type: awardEnum.seasonPassProgress,
						amount: 200,
					},
					{
						rewardId: 54,
						type: awardEnum.seasonPassProgress,
						amount: 200,
					},
					{
						rewardId: 55,
						type: awardEnum.seasonPassProgress,
						amount: 200,
					},
					{
						rewardId: 56,
						type: awardEnum.seasonPassProgress,
						amount: 200,
					},
				],

				config: {
					minigames: ['Taran', 'sws', 'RocketDm', 'PropHunt'],
				},
			},
			{
				id: 2,
				name: 'LostVehicles',
				controller: 'LostVehicles',
				start: '2025-01-30 10:00:00',
				end: '2025-02-11 10:00:00',
				coolDown: 6,

				rewards: [
					{
						rewardId: 1,
						type: awardEnum.seasonPassProgress,
						amount: 500,
						hasOneReceipt: true,
					},
					{
						rewardId: 2,
						type: awardEnum.seasonPassProgress,
						amount: 500,
						hasOneReceipt: true,
					},
					{
						rewardId: 3,
						type: awardEnum.seasonPassProgress,
						amount: 500,
						hasOneReceipt: true,
					},
					{
						rewardId: 4,
						type: awardEnum.seasonPassProgress,
						amount: 500,
						hasOneReceipt: true,
					},
					{
						rewardId: 5,
						type: awardEnum.seasonPassProgress,
						amount: 250,
					},
					{
						rewardId: 6,
						type: awardEnum.seasonPassProgress,
						amount: 250,
					},
					{
						rewardId: 7,
						type: awardEnum.seasonPassProgress,
						amount: 250,
					},
					{
						rewardId: 8,
						type: awardEnum.seasonPassProgress,
						amount: 250,
					},
				],
				config: {
					// КД при провале задания в МС
					failCooldown: 5 * 60 * 1000,
					// Модели потерянных авто(выбираются из массива рандомно)
					vehiclesModels: ['rancherxl2', 'asea2', 'emperor3', 'sadler2', 'burrito5', 'policeold1'],
					// Позиции спавна авто(выбираются из массива рандомно)
					vehiclesSpawnPositions: [
						{ x: 3525.6528, y: -4682.7954, z: 114.0007, rotation: 125.8172 },
						{ x: 3581.5386, y: -4666.2856, z: 114.1018, rotation: 144.4322 },
						{ x: 3693.0857, y: -4732.2988, z: 111.7598, rotation: 80.4757 },
						{ x: 3252.5144, y: -4698.4087, z: 112.9561, rotation: 110.4835 },
						{ x: 3709.833, y: -4852.4702, z: 108.3392, rotation: 151.7812 },
						{ x: 4492.7339, y: -5047.0947, z: 111.4901, rotation: 68.6727 },
						{ x: 4922.3999, y: -5060.334, z: 94.9773, rotation: -170.9252 },
						{ x: 5631.02, y: -5154.0, z: 80.2168, rotation: -37.1952 },
						{ x: 5422.0483, y: -5204.7427, z: 81.2784, rotation: 93.5433 },
						{ x: 5241.4551, y: -5179.042, z: 81.8008, rotation: -70.051 },
						{ x: 4761.1387, y: -5129.3936, z: 106.8901, rotation: -101.1798 },
						{ x: 4396.3779, y: -5223.9692, z: 111.5912, rotation: 117.8653 },
						{ x: 4067.1428, y: -5178.0527, z: 107.6146, rotation: 174.2706 },
						{ x: 3866.0835, y: -5030.2681, z: 111.7598, rotation: -56.776 },
						{ x: 3458.4131, y: -4890.145, z: 111.9619, rotation: -169.147 },
					],

					// Количество машин, которые нужно будет вывезти. Диапазон от минимального к максимальному
					vehiclesSpawnAmount: [2, 3],

					// Модель тягача, которую спавним игроку
					towtruckModel: 'towtruck',

					// Куда телепортируем игрока при старте задания
					teleportToStart: {
						position: new mp.Vector3(3319.1208, -4913.5518, 111.2543),
						rotation: -8.5039,
					},

					// Время затемнения экрана
					fadeTime: 1000,

					// Где спавним игрока после окончания задания
					teleportToEnd: {
						position: new mp.Vector3(3313.4241, -4915.6353, 111.3217),
						rotation: -25.5118,
					},

					// Время на выполнение задания в МС
					duration: 20 * 60 * 1000,

					vehiclesBlip: {
						id: 225,
						optionals: {
							color: 3,
							name: 'fun.seasonEvents.lostVehicles.blips.lostVehicle',
						},
					},

					garageBlip: {
						id: 524,
						position: new mp.Vector3(3334, -4900, 111),
						optionals: {
							color: 5,
							name: 'fun.seasonEvents.lostVehicles.blips.garage',
							alpha: 0,
						},
					},

					garageColshape: {
						position: new mp.Vector3(3334, -4900, 111),
						radius: 5,
					},

					vehiclesMarker: {
						id: 2,
						scale: 1.1,
						optionals: {
							color: [101, 185, 231, 255],
							visible: true,
							rotation: new mp.Vector3(0, 22, 0),
						},
					},

					garageMarker: {
						id: 1,
						position: new mp.Vector3(3334, -4900, 110),
						scale: 5,
						optionals: {
							color: [101, 185, 231, 255],
							visible: false,
						},
					},

					startHint: {
						title: 'fun.seasonEvents.lostVehicles.startHint.title',
						text: 'fun.seasonEvents.lostVehicles.startHint.text',
						showTime: 15000,
					},

					IPLs: [
						'plg_01',
						'prologue01',
						'prologue01_lod',
						'prologue01c',
						'prologue01c_lod',
						'prologue01d',
						'prologue01d_lod',
						'prologue01e',
						'prologue01e_lod',
						'prologue01f',
						'prologue01f_lod',
						'prologue01g',
						'prologue01h',
						'prologue01h_lod',
						'prologue01i',
						'prologue01i_lod',
						'prologue01j',
						'prologue01j_lod',
						'prologue01k',
						'prologue01k_lod',
						'prologue01z',
						'prologue01z_lod',
						'plg_02',
						'prologue02',
						'prologue02_lod',
						'plg_03',
						'prologue03',
						'prologue03_lod',
						'prologue03b',
						'prologue03b_lod',
						'prologue03_grv_dug',
						'prologue03_grv_dug_lod',
						'prologue_grv_torch',
						'plg_04',
						'prologue04',
						'prologue04_lod',
						'prologue04b',
						'prologue04b_lod',
						'prologue04_cover',
						'des_protree_end',
						'des_protree_start',
						'des_protree_start_lod',
						'plg_05',
						'prologue05',
						'prologue05_lod',
						'prologue05b',
						'prologue05b_lod',
						'plg_06',
						'prologue06',
						'prologue06_lod',
						'prologue06b',
						'prologue06b_lod',
						'prologue06_int',
						'prologue06_int_lod',
						'prologue06_pannel',
						'prologue06_pannel_lod',
						'prologue_m2_door',
						'prologue_m2_door_lod',
						'plg_occl_00',
						'prologue_occl',
						'plg_rd',
						'prologuerd',
						'prologuerdb',
						'prologuerd_lod',
						'MJ_XM_Yankton',
						'MJ_XM_Yankton_XmasTree',
						'mj_xm_tree_to_yankton',
					],
				},
			},
			{
				id: 3,
				name: 'HitElfs',
				controller: 'HitElfs',
				start: '2025-02-01 10:00:00',
				end: '2025-02-11 10:00:00',
				coolDown: 6,

				rewards: [
					{
						rewardId: 9,
						type: awardEnum.seasonPassProgress,
						amount: 300,
						hasOneReceipt: true,
					},
					{
						rewardId: 10,
						type: awardEnum.seasonPassProgress,
						amount: 300,
						hasOneReceipt: true,
					},
					{
						rewardId: 11,
						type: awardEnum.seasonPassProgress,
						amount: 300,
						hasOneReceipt: true,
					},
					{
						rewardId: 12,
						type: awardEnum.seasonPassProgress,
						amount: 300,
						hasOneReceipt: true,
					},
					{
						rewardId: 13,
						type: awardEnum.seasonPassProgress,
						amount: 150,
					},
					{
						rewardId: 14,
						type: awardEnum.seasonPassProgress,
						amount: 150,
					},
					{
						rewardId: 15,
						type: awardEnum.seasonPassProgress,
						amount: 150,
					},
					{
						rewardId: 16,
						type: awardEnum.seasonPassProgress,
						amount: 150,
					},
				],
				config: {
					// КД при провале задания в ms
					failCooldown: 5 * 60 * 1000,

					// Позиции ельфов
					spawnPositions: [
						{ x: 3517.5034, y: -4689.2178, z: 117.9436, r: -51.0236 },
						{ x: 3527.5254, y: -4690.6284, z: 114.3378, r: 150.2362 },
						{ x: 3522.7913, y: -4682.2417, z: 114.1187, r: 17.0079 },
						{ x: 3521.2878, y: -4676.835, z: 114.641, r: 99.2126 },
						{ x: 3509.8945, y: -4688.334, z: 114.1692, r: -17.0079 },
						{ x: 3551.1692, y: -4698.6592, z: 114.2872, r: -2.8346 },
						{ x: 3570.3296, y: -4711.0684, z: 115.5846, r: -19.8425 },
						{ x: 3575.9868, y: -4696.0615, z: 118.4828, r: 73.7008 },
						{ x: 3567.9297, y: -4684.5098, z: 115.6857, r: 34.0157 },
						{ x: 3586.4966, y: -4679.9209, z: 114.5736, r: -48.189 },
						{ x: 3574.6287, y: -4666.1011, z: 114.4894, r: -2.8346 },
						{ x: 3565.7803, y: -4664.8745, z: 114.9949, r: 51.0236 },
						{ x: 3557.9868, y: -4663.4771, z: 115.0623, r: -2.8346 },
						{ x: 3542.6638, y: -4641.7847, z: 114.6915, r: 161.5748 },
						{ x: 3515.4856, y: -4654.6152, z: 114.641, r: -68.0315 },
						{ x: 3483.1121, y: -4662.7515, z: 115.7194, r: -25.5118 },
						{ x: 3536.2681, y: -4664.1099, z: 119.7971, r: 31.1811 },
						{ x: 3542.6375, y: -4639.1211, z: 119.8645, r: -107.7165 },
						{ x: 3514.7737, y: -4642.022, z: 114.186, r: 59.5276 },
						{ x: 3552.554, y: -4689.8374, z: 114.4557, r: 170.0787 },
						{ x: 3555.7979, y: -4679.5386, z: 114.3378, r: -113.3858 },
					],

					// Модели персонажей эльфов
					elfModels: ['elffirst', 'elfsecond', 'elfthird'],

					// Сколько эльфов спавнить
					spawnRandomCount: [6, 8],

					// Куда телепортируем игрока при старте задания
					// teleportTo: {
					//     position: new mp.Vector3(3517.5034, -4689.2178, 117.9436, -51.0236),
					//     rotation: 133.2283
					// },

					// Время затемнения экрана
					fadeTime: 1000,

					// Где спавним игрока после окончания задания
					// spawnAfterEnd: {
					//     position: new mp.Vector3(3187.8857, -4789.2397, 111.9956),
					//     rotation: -36.8504
					// },

					// Время на выполнение задания в ms
					duration: 10 * 60 * 1000,

					// elfBlip: {
					//     id: 225,
					//     optionals: {
					//         color: 3,
					//         name: 'blips.hitElf',
					//     },
					// },

					IPLs: [
						'plg_01',
						'prologue01',
						'prologue01_lod',
						'prologue01c',
						'prologue01c_lod',
						'prologue01d',
						'prologue01d_lod',
						'prologue01e',
						'prologue01e_lod',
						'prologue01f',
						'prologue01f_lod',
						'prologue01g',
						'prologue01h',
						'prologue01h_lod',
						'prologue01i',
						'prologue01i_lod',
						'prologue01j',
						'prologue01j_lod',
						'prologue01k',
						'prologue01k_lod',
						'prologue01z',
						'prologue01z_lod',
						'plg_02',
						'prologue02',
						'prologue02_lod',
						'plg_03',
						'prologue03',
						'prologue03_lod',
						'prologue03b',
						'prologue03b_lod',
						'prologue03_grv_dug',
						'prologue03_grv_dug_lod',
						'prologue_grv_torch',
						'plg_04',
						'prologue04',
						'prologue04_lod',
						'prologue04b',
						'prologue04b_lod',
						'prologue04_cover',
						'des_protree_end',
						'des_protree_start',
						'des_protree_start_lod',
						'plg_05',
						'prologue05',
						'prologue05_lod',
						'prologue05b',
						'prologue05b_lod',
						'plg_06',
						'prologue06',
						'prologue06_lod',
						'prologue06b',
						'prologue06b_lod',
						'prologue06_int',
						'prologue06_int_lod',
						'prologue06_pannel',
						'prologue06_pannel_lod',
						'prologue_m2_door',
						'prologue_m2_door_lod',
						'plg_occl_00',
						'prologue_occl',
						'plg_rd',
						'prologuerd',
						'prologuerdb',
						'prologuerd_lod',
						'MJ_XM_Yankton',
						'MJ_XM_Yankton_XmasTree',
						'mj_xm_tree_to_yankton',
					],
				},
			},
			{
				id: 4,
				name: 'Match same',
				controller: 'MatchSame',
				start: '2025-02-03 10:00:00',
				end: '2025-02-11 10:00:00',

				coolDown: 6,

				rewards: [
					{
						rewardId: 17,
						type: awardEnum.seasonPassProgress,
						amount: 200,
						hasOneReceipt: true,
					},
					{
						rewardId: 18,
						type: awardEnum.seasonPassProgress,
						amount: 200,
						hasOneReceipt: true,
					},
					{
						rewardId: 19,
						type: awardEnum.seasonPassProgress,
						amount: 200,
						hasOneReceipt: true,
					},
					{
						rewardId: 20,
						type: awardEnum.seasonPassProgress,
						amount: 200,
						hasOneReceipt: true,
					},
					{
						rewardId: 21,
						type: awardEnum.seasonPassProgress,
						amount: 100,
					},
					{
						rewardId: 22,
						type: awardEnum.seasonPassProgress,
						amount: 100,
					},
					{
						rewardId: 23,
						type: awardEnum.seasonPassProgress,
						amount: 100,
					},
					{
						rewardId: 24,
						type: awardEnum.seasonPassProgress,
						amount: 100,
					},
				],
				config: {
					failCooldown: 5 * 60 * 1000,

					teleportToStart: {
						position: { x: 1393.82, y: 1147.14, z: 114.33 },
						rotation: 176.84,
					},

					teleportToEnd: {
						position: { x: 4476.5801, y: -5085.0068, z: 111.3553 },
						rotation: -51.0236,
					},
				},
			},
			{
				id: 5,
				name: 'GrinchRabbits',
				controller: 'GrinchRabbits',
				start: '2025-02-05 10:00:00',
				end: '2025-02-11 10:00:00',

				coolDown: 6,

				rewards: [
					{
						rewardId: 33,
						type: awardEnum.seasonPassProgress,
						amount: 400,
						hasOneReceipt: true,
					},
					{
						rewardId: 34,
						type: awardEnum.seasonPassProgress,
						amount: 400,
						hasOneReceipt: true,
					},
					{
						rewardId: 35,
						type: awardEnum.seasonPassProgress,
						amount: 400,
						hasOneReceipt: true,
					},
					{
						rewardId: 36,
						type: awardEnum.seasonPassProgress,
						amount: 400,
						hasOneReceipt: true,
					},
					{
						rewardId: 37,
						type: awardEnum.seasonPassProgress,
						amount: 200,
					},
					{
						rewardId: 38,
						type: awardEnum.seasonPassProgress,
						amount: 200,
					},
					{
						rewardId: 39,
						type: awardEnum.seasonPassProgress,
						amount: 200,
					},
					{
						rewardId: 40,
						type: awardEnum.seasonPassProgress,
						amount: 200,
					},
				],
				config: {
					// КД при провале задания в МС
					failCooldown: 5 * 60 * 1000,
					// Скин, который выдаем игроку при старте задания
					playerSkin: 'a_c_husky',
					// Название педа кролика
					rabbitPed: 'A_C_Rabbit_01',

					healedRabbitPed: ['elffirst', 'elfsecond', 'elfthird'],

					// Позиции спавна кроликов(выбираются из массива рандомно)
					rabbitsPositions: [
						{
							position: new mp.Vector3(-1719.6791, 4699.9385, 34.6212),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1734.5011, 4689.1914, 28.1509),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1743.5209, 4677.165, 22.0513),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1731.2571, 4664.1758, 22.0513),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1712.967, 4663.8066, 22.7084),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1715.6044, 4679.9473, 28.9934),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1698.4088, 4670.0439, 22.9949),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1686.3165, 4684.1934, 25.0168),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1679.9341, 4700.5186, 27.1567),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						//{
						//	position: new mp.Vector3(-1647.4286, 4540.1011, 40.5861),
						//	heading: 10,
						//	wanderRadius: 5,
						//	minimalLength: 2,
						//	timeBetweenWalks: 3,
						//},
						{
							position: new mp.Vector3(-1637.1296, 4555.5298, 43.1641),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1625.9341, 4567.1738, 43.3326),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1622.3605, 4583.8813, 43.4169),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1641.9165, 4581.5342, 42.7935),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1654.6285, 4564.0747, 42.1194),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1663.9385, 4579.6616, 42.8777),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1678.1274, 4597.2266, 48.9773),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1659.811, 4601.1958, 47.3934),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1643.6439, 4609.9121, 44.8828),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1627.2395, 4605.6792, 43.2653),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1637.855, 4595.5122, 43.063),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1619.2483, 4619.8682, 44.7142),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1603.7539, 4626.6724, 46.197),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1596.844, 4640.6636, 48.7076),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1582.4703, 4652.334, 46.9216),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1572.3165, 4664.2153, 45.7759),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1557.7054, 4678.8657, 45.5568),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1574.9802, 4680.6855, 46.1633),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1591.0286, 4667.7363, 45.2366),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1613.9868, 4640.1099, 45.5736),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1601.8154, 4657.5298, 46.6857),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1630.9846, 4628.98, 39.9626),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1647.4418, 4625.7891, 41.4623),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1663.1472, 4613.3804, 46.4667),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1631.1956, 4646.0439, 37.2161),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1648.7208, 4643.5518, 33.2902),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},

						{
							position: new mp.Vector3(-1617.4945, 4659.4551, 41.5128),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1603.0813, 4673.1035, 41.9509),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1589.0637, 4685.6177, 44.1245),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1565.2351, 4692.8438, 49.7186),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1583.4725, 4700.8481, 45.9612),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1604.4396, 4691.3276, 39.929),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1621.266, 4675.5693, 35.2616),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1641.455, 4657.4507, 30.0718),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1663.4901, 4654.2988, 24.7979),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1683.9824, 4648.8394, 22.7252),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1702.1538, 4642.8657, 21.6974),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1721.1165, 4646.479, 20.9561),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1737.5868, 4648.7603, 21.0066),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1663.1077, 4674.8701, 29.8191),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1650.0527, 4686.1714, 35.8176),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1634.7693, 4693.5298, 39.6256),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1616.1758, 4699.0552, 39.3392),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1605.6527, 4713.231, 42.8608),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1582.0615, 4715.0244, 50.1904),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1566.6989, 4707.6924, 49.3649),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1635.389, 4727.71, 53.2402),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1621.0549, 4736.2813, 52.3473),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1635.0198, 4747.2266, 52.5831),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
						{
							position: new mp.Vector3(-1611.6132, 4754.9536, 52.5326),
							heading: 10,
							wanderRadius: 5,
							minimalLength: 2,
							timeBetweenWalks: 3,
						},
					],

					timeToDestroyHealedRabbit: 10000,

					// Конфигурация маркера над головой кроликов
					pedMarker: {
						id: 31,
						color: [255, 165, 0, 255],
						minScale: 0.2,
						maxScale: 0.5,
						height: 0.3,
						range: 50,
						speedReducer: 0.1,
					},

					// Скрин эффект входа в режим "Чутье"
					screenEffectIn: 'FocusIn', //FocusIn
					// Скрин эффект выхода из режима "Чутье"
					screenEffectOut: 'FocusOut',

					// Таймцикл входа в режим "Чутье"
					timecycleName: 'prologue', //spectator4
					// Плавность перехода в таймцикл
					timecycleTransition: 1.4,

					clockTime: {
						hours: 19,
						minutes: 0,
						seconds: 0,
					},

					weather: 'xmas',

					// TODO: Добавить как появится кастомная анимка
					// barkAnimDict: '',
					// barkAnimName: '',
					// barkEffect: '',

					// Расстояние от игрока до педа, при котором можно снять чары
					infectDispellRadius: 5,

					// Суммарное количество кроликов. Диапазон от минимального к максимальному
					rabbitsSpawnAmount: 59,

					// Количество зачарованных кроликов. Диапазон от минимального к максимальному
					infectedRabbitsAmount: [4, 6],

					// Бафф чутья
					instinctBuff: 'Instinct',

					// Куда телепортируем игрока при старте задания
					teleportToStart: {
						position: new mp.Vector3(-1598.7032, 4643.4858, 48.792),
						rotation: -8.5039,
					},

					// Время затемнения экрана
					fadeTime: 1000,

					// Где спавним игрока после окончания задания
					teleportToEnd: {
						position: new mp.Vector3(3903.9165, -5059.7671, 110.9678),
						rotation: 22.6772,
					},

					findZoneBlip: {
						id: 9,
						position: new mp.Vector3(-1654.2858, 4655.5386, 26.6176),
						color: 3,
						radius: 110,
						alpha: 100,
					},

					// Время на выполнение задания в МС
					duration: 20 * 60 * 1000,

					// За сколько МС до конца таймера пойдет blizzard
					timeToBlizzard: 20000,

					// Подсказка при старте задания
					startHint: {
						title: 'fun.seasonEvents.grinchRabbits.startHint.title',
						text: 'fun.seasonEvents.grinchRabbits.startHint.text',
						showTime: 15000,
					},

					IPLs: [
						'plg_01',
						'prologue01',
						'prologue01_lod',
						'prologue01c',
						'prologue01c_lod',
						'prologue01d',
						'prologue01d_lod',
						'prologue01e',
						'prologue01e_lod',
						'prologue01f',
						'prologue01f_lod',
						'prologue01g',
						'prologue01h',
						'prologue01h_lod',
						'prologue01i',
						'prologue01i_lod',
						'prologue01j',
						'prologue01j_lod',
						'prologue01k',
						'prologue01k_lod',
						'prologue01z',
						'prologue01z_lod',
						'plg_02',
						'prologue02',
						'prologue02_lod',
						'plg_03',
						'prologue03',
						'prologue03_lod',
						'prologue03b',
						'prologue03b_lod',
						'prologue03_grv_dug',
						'prologue03_grv_dug_lod',
						'prologue_grv_torch',
						'plg_04',
						'prologue04',
						'prologue04_lod',
						'prologue04b',
						'prologue04b_lod',
						'prologue04_cover',
						'des_protree_end',
						'des_protree_start',
						'des_protree_start_lod',
						'plg_05',
						'prologue05',
						'prologue05_lod',
						'prologue05b',
						'prologue05b_lod',
						'plg_06',
						'prologue06',
						'prologue06_lod',
						'prologue06b',
						'prologue06b_lod',
						'prologue06_int',
						'prologue06_int_lod',
						'prologue06_pannel',
						'prologue06_pannel_lod',
						'prologue_m2_door',
						'prologue_m2_door_lod',
						'plg_occl_00',
						'prologue_occl',
						'plg_rd',
						'prologuerd',
						'prologuerdb',
						'prologuerd_lod',
						'MJ_XM_Yankton',
						'MJ_XM_Yankton_XmasTree',
						'mj_xm_tree_to_yankton',
					],
				},
			},
			{
				id: 6,
				name: 'Match tree',
				controller: 'MatchTree',
				start: '2025-02-07 10:00:00',
				end: '2025-02-11 10:00:00',

				coolDown: 6,

				rewards: [
					{
						rewardId: 25,
						type: awardEnum.seasonPassProgress,
						amount: 200,
						hasOneReceipt: true,
					},
					{
						rewardId: 26,
						type: awardEnum.seasonPassProgress,
						amount: 200,
						hasOneReceipt: true,
					},
					{
						rewardId: 27,
						type: awardEnum.seasonPassProgress,
						amount: 200,
						hasOneReceipt: true,
					},
					{
						rewardId: 28,
						type: awardEnum.seasonPassProgress,
						amount: 200,
						hasOneReceipt: true,
					},
					{
						rewardId: 29,
						type: awardEnum.seasonPassProgress,
						amount: 100,
					},
					{
						rewardId: 30,
						type: awardEnum.seasonPassProgress,
						amount: 100,
					},
					{
						rewardId: 32,
						type: awardEnum.seasonPassProgress,
						amount: 100,
					},
					{
						rewardId: 57,
						type: awardEnum.seasonPassProgress,
						amount: 100,
					},
				],
				config: {
					failCooldown: 5 * 60 * 1000,

					teleportToStart: {
						position: { x: 1393.82, y: 1147.14, z: 114.33 },
						rotation: 176.84,
					},

					teleportToEnd: {
						position: { x: 5421.231, y: -5189.1694, z: 79.7113 },
						rotation: -42.5197,
					},
				},
			},
			{
				id: 7,
				name: 'DeliveryGifts',
				controller: 'DeliveryGifts',
				start: '2025-02-09 10:00:00',
				end: '2025-02-11 10:00:00',

				coolDown: 6,

				rewards: [
					{
						rewardId: 41,
						type: awardEnum.seasonPassProgress,
						amount: 600,
						hasOneReceipt: true,
					},
					{
						rewardId: 42,
						type: awardEnum.seasonPassProgress,
						amount: 600,
						hasOneReceipt: true,
					},
					{
						rewardId: 43,
						type: awardEnum.seasonPassProgress,
						amount: 600,
						hasOneReceipt: true,
					},
					{
						rewardId: 44,
						type: awardEnum.seasonPassProgress,
						amount: 600,
						hasOneReceipt: true,
					},
					{
						rewardId: 45,
						type: awardEnum.seasonPassProgress,
						amount: 300,
					},
					{
						rewardId: 46,
						type: awardEnum.seasonPassProgress,
						amount: 300,
					},
					{
						rewardId: 47,
						type: awardEnum.seasonPassProgress,
						amount: 300,
					},
					{
						rewardId: 48,
						type: awardEnum.seasonPassProgress,
						amount: 300,
					},
				],
				config: {
					failCooldown: 5 * 60 * 1000,

					vehicleSpawn: {
						position: { x: 1586.1758, y: 3186.0396, z: 40.5187 },
						rotation: 133.2283,
					},

					teleportToEnd: {
						position: { x: 3262.7473, y: -4696.0483, z: 112.9392 },
						rotation: 25.5118,
					},

					vehName: 'sled',

					// Время на выполнение задания в МС
					duration: 20 * 60 * 1000,

					countGifts: { min: 14, max: 18 },
					giftsObjects: [
						'mj_box_big_gift_1',
						'mj_box_big_gift_2',
						'mj_box_big_gift_3',
						'mj_box_gift_1',
						'mj_box_gift_2',
						'mj_box_gift_3',
						'mj_box_gift_4',
						'mj_box_present_1',
						'mj_box_present_2',
						'mj_box_present_3',
						'mj_box_present_4',
						'mj_xm_box1',
						'mj_xm_box2',
						'mj_xm_box3',
						'mj_xm_box4',
						'mj_xm_box5',
						'mj_xm_box6',
						'mj_xm_box7',
						'mj_xm_box8',
						'mj_xm_box9',
						'mj_xm_box10',
					],
					giftsPositions: [
						new mp.Vector3(351.7529, -586.9473, 74.1656, 30.1666),
						new mp.Vector3(282.2487, -646.6302, 113.0718, 66.9906),
						// new mp.Vector3(379.4942, -710.9388, 85.6123, 78.8509),
						// new mp.Vector3(351.2910, -785.5867, 66.8951, 142.1398),
						new mp.Vector3(324.6512, -824.4116, 67.1004, 149.594),
						new mp.Vector3(449.2886, -981.1358, 43.6916, -119.5816),
						new mp.Vector3(376.7583, -1017.0287, 57.5294, 12.8175),
						new mp.Vector3(337.6772, -968.4169, 79.5033, 52.9303),
						// new mp.Vector3(312.7150, -967.9891, 79.5033, 98.3200),
						new mp.Vector3(263.9351, -1012.5244, 61.6314, 167.327),
						new mp.Vector3(294.235, -1021.4094, 86.7461, -86.2297),
						new mp.Vector3(476.6415, -1106.4172, 43.0756, 23.9959),
						new mp.Vector3(329.3622, -1081.0345, 60.7671, 151.3873),
						new mp.Vector3(282.4183, -1074.9779, 83.0099, 165.1841),
						new mp.Vector3(160.2096, -1095.7039, 49.1559, 81.7959),
						new mp.Vector3(181.6564, -1055.4473, 71.7392, -100.4391),
						new mp.Vector3(107.2432, -1101.7278, 63.2914, 178.1215),
						new mp.Vector3(130.1855, -1036.861, 57.7968, -21.3251),
						new mp.Vector3(63.5435, -1019.4118, 79.7767, -167.7903),
						new mp.Vector3(88.8158, -926.3344, 85.2874, 74.1812),
						// new mp.Vector3(105.6793, -872.4768, 137.9538, -147.6794),
						new mp.Vector3(24.9879, -922.636, 123.0777, -23.594),
						new mp.Vector3(297.381, -913.4276, 56.4607, -85.7886),
						new mp.Vector3(304.0446, -890.0873, 57.947, 93.1232),
						new mp.Vector3(255.7568, -815.2079, 75.7008, 100.8582),
						new mp.Vector3(287.0995, -755.7597, 67.1133, 69.1392),
						new mp.Vector3(376.6877, -802.089, 53.4357, 105.8429),
						new mp.Vector3(377.92, -711.0505, 85.6122, 71.6774),
						new mp.Vector3(352.2224, -785.754, 66.8951, 14.7598),
						new mp.Vector3(325.0421, -824.1259, 67.1049, 86.6775),
						new mp.Vector3(266.446, -723.6356, 81.4146, -141.3884),
						new mp.Vector3(196.111, -578.1254, 125.3095, 116.8265),
						new mp.Vector3(120.1827, -848.6998, 73.5856, 66.9162),
						new mp.Vector3(60.3589, -827.794, 96.9327, 60.0521),
						// new mp.Vector3(-29.8065, -589.2479, 106.3203, 97.5694),
						new mp.Vector3(-286.9294, -618.434, 50.3329, 132.4568),
						new mp.Vector3(-222.3442, -837.7054, 125.2569, 41.8091),
						new mp.Vector3(-272.9711, -955.8354, 144.5951, -116.3329),
						new mp.Vector3(-303.5636, -819.4432, 95.7936, 12.9263),
						new mp.Vector3(-279.4161, -733.6305, 123.9974, 39.4199),
						new mp.Vector3(-273.4905, -702.8331, 113.5263, 23.4745),
						new mp.Vector3(-143.9878, -593.2236, 211.775, 136.5936),
						new mp.Vector3(-2026.861, 491.3717, 112.2896, 152.0462),
						new mp.Vector3(-2019.5669, 383.4688, 99.5877, 76.92),
						new mp.Vector3(-1997.3937, 306.8608, 101.2703, 61.1408),
						new mp.Vector3(-1923.0452, 592.644, 130.2059, -14.4496),
						new mp.Vector3(-1884.2625, 641.9255, 134.7915, 93.6446),
						new mp.Vector3(-1840.3568, 308.3001, 99.0692, 35.329),
						new mp.Vector3(-1797.7174, 334.7874, 97.3573, -67.8492),
						new mp.Vector3(-1730.5574, 368.4311, 94.326, -110.781),
						new mp.Vector3(-1681.7679, 380.4369, 92.966, -104.7821),
						new mp.Vector3(-1558.9521, -98.8732, 67.1663, -21.5428),
						new mp.Vector3(-1472.1045, -36.798, 66.0893, -113.6458),
						new mp.Vector3(251.6841, 234.3408, 151.6328, -154.7326),
						new mp.Vector3(191.6868, 256.3356, 141.4779, 131.5973),
						new mp.Vector3(365.5605, -343.2909, 61.0184, -7.4308),
						// new mp.Vector3(305.5814, -302.6540, 69.4283, -5.6088),
						new mp.Vector3(243.5945, -332.6262, 63.8996, 68.211),
						// new mp.Vector3(260.8519, -284.1694, 68.8301, 83.8872),
						new mp.Vector3(342.7618, -220.3002, 61.5657, -35.1219),
						new mp.Vector3(315.3697, -189.2969, 61.5755, -121.6843),
						new mp.Vector3(170.1019, -300.9206, 57.5686, 32.0689),
						new mp.Vector3(150.9037, -284.1348, 53.6182, -16.7701),
						new mp.Vector3(123.5518, -276.9907, 57.8122, -6.5313),
						new mp.Vector3(94.2553, -276.0257, 58.7805, 37.025),
						new mp.Vector3(73.8732, -258.5418, 55.5663, -51.8694),
						new mp.Vector3(261.3597, -286.5146, 68.8291, -98.3306),
						new mp.Vector3(305.9828, -299.6204, 69.4212, -7.9408),
						new mp.Vector3(351.6459, -167.0888, 79.4308, 75.3157),
						new mp.Vector3(310.5395, -151.7124, 81.0424, 94.4869),
						new mp.Vector3(320.0299, -131.4487, 88.3153, 166.7942),
						// new mp.Vector3(242.2612, -105.6558, 82.1039, 150.6081),
						new mp.Vector3(211.7565, -85.9919, 80.5794, 103.8032),
						new mp.Vector3(227.1079, -158.3881, 70.2699, -9.7112),
						new mp.Vector3(190.4866, -142.0006, 72.7782, 162.7032),
						new mp.Vector3(238.5661, -109.1933, 82.1038, -100.3302),
						// new mp.Vector3(212.8625, -85.9526, 80.5464, 72.5827),
						new mp.Vector3(158.8321, -112.859, 73.7942, 51.0051),
						new mp.Vector3(165.856, -84.212, 80.5229, 10.3824),
						new mp.Vector3(139.8123, -64.6189, 79.1743, -132.0778),
						new mp.Vector3(-60.871, -299.4587, 63.0673, -132.0778),
						new mp.Vector3(-73.0125, -374.7498, 55.6473, 152.814),
						new mp.Vector3(-17.0885, -98.1673, 64.7706, -44.3064),
						new mp.Vector3(-46.3989, -84.9289, 63.0147, 35.3863),
						new mp.Vector3(-5.1858, -68.9902, 72.9657, -156.354),
						new mp.Vector3(-40.6223, -55.8731, 75.009, 7.5405),
						new mp.Vector3(5.3528, -42.7599, 78.636, -23.1184),
						new mp.Vector3(3.3389, -28.3242, 77.6559, -131.5106),
						new mp.Vector3(-26.6703, -1.8453, 83.6451, 8.9214),
						new mp.Vector3(0.4151, 45.355, 93.3898, -0.0168),
						new mp.Vector3(11.7272, 71.2666, 96.7726, -7.0928),
						new mp.Vector3(110.2379, 42.7341, 85.5279, 50.0941),
						new mp.Vector3(113.5269, 83.2121, 96.1406, 114.993),
						new mp.Vector3(180.6418, -14.55, 85.7403, -10.1696),
						new mp.Vector3(180.4211, 20.0932, 84.7085, -108.1454),
						new mp.Vector3(212.2579, 21.5274, 86.7363, -88.8252),
						new mp.Vector3(258.1005, 15.0295, 95.4742, -95.2595),
						new mp.Vector3(339.5029, -41.1344, 153.2948, -114.6771),
						new mp.Vector3(579.9454, 12.0623, 103.2336, 18.1861),
						new mp.Vector3(393.3183, -66.5041, 124.3762, -85.1869),
						new mp.Vector3(402.0065, -27.0023, 160.3927, -37.6544),
						// new mp.Vector3(419.6626, 1.0572, 160.3941, -15.9049),
						new mp.Vector3(308.6817, 113.8034, 145.1687, 50.9364),
						// new mp.Vector3(259.4440, 14.3304, 95.4275, 122.0691),
						new mp.Vector3(283.5482, 38.2947, 100.4156, -113.4968),
						new mp.Vector3(218.9535, 61.5925, 95.4697, -117.6507),
						new mp.Vector3(234.156, 154.239, 137.5441, -146.7684),
						new mp.Vector3(595.1276, 195.6122, 137.2022, 153.0947),
						new mp.Vector3(512.9113, 141.2982, 131.6082, 113.4862),
						new mp.Vector3(345.8286, 212.8997, 132.685, -176.6195),
						new mp.Vector3(282.6554, 294.2026, 132.0898, -174.173),
						new mp.Vector3(251.6841, 234.3408, 151.6328, -154.7326),
						new mp.Vector3(191.6868, 256.3356, 141.4779, 131.5973),
						new mp.Vector3(-607.7393, 653.3902, 156.6163),
						new mp.Vector3(-568.6826, 657.0331, 149.8367),
						new mp.Vector3(-528.7988, 632.5131, 142.5483),
						new mp.Vector3(-531.0101, 588.0478, 125.5113),
						new mp.Vector3(-463.9005, 592.9275, 135.3657),
						new mp.Vector3(-509.3992, 691.679, 162.3587),
						new mp.Vector3(-462.8227, 692.8663, 162.4462),
						new mp.Vector3(-492.5283, 642.0424, 151.438),
						new mp.Vector3(-413.1397, 651.0041, 168.4735),
						new mp.Vector3(-345.3322, 674.7711, 181.9308),
						new mp.Vector3(-299.5803, 647.6185, 183.569),
						new mp.Vector3(-335.0901, 618.0254, 176.9696),
						new mp.Vector3(-291.6559, 591.5505, 185.1101),
						new mp.Vector3(-250.0228, 627.6408, 196.8457),
						new mp.Vector3(-237.5591, 581.1381, 194.4278),
						new mp.Vector3(-189.0149, 582.8132, 201.7298),
						new mp.Vector3(-182.0937, 633.8212, 208.5327),
						new mp.Vector3(-124.9637, 582.6579, 211.0669),
						new mp.Vector3(-77.5576, 1001.1489, 245.5125),
						new mp.Vector3(-59.9357, 835.3923, 239.9392),
						new mp.Vector3(-157.2218, 903.126, 242.2001),
						new mp.Vector3(-178.5985, 998.491, 240.1224),
						new mp.Vector3(234.3144, 763.1038, 208.9595),
						new mp.Vector3(230.6692, 660.2177, 193.5784),
						new mp.Vector3(228.8086, 613.3177, 194.6829),
						new mp.Vector3(175.7758, 565.3531, 186.5623),
						new mp.Vector3(123.5637, 556.4991, 188.2996),
						new mp.Vector3(83.3474, 555.796, 186.8861),
						new mp.Vector3(47.0413, 550.6799, 183.7),
						new mp.Vector3(-10.2273, 526.6704, 178.6139),
						new mp.Vector3(213.317, 495.7011, 144.9301),
						new mp.Vector3(170.5582, 469.0821, 147.172),
						new mp.Vector3(116.2038, 498.0083, 159.1239),
						new mp.Vector3(108.3248, 460.4032, 156.3636),
						new mp.Vector3(80.905, 491.8127, 157.2971),
						new mp.Vector3(48.0647, 478.0686, 161.6713),
						new mp.Vector3(45.2024, 446.9972, 150.6711),
						new mp.Vector3(-77.9652, 487.307, 151.5805),
						new mp.Vector3(-116.3066, 499.1686, 148.8625),
						new mp.Vector3(-177.1839, 494.9514, 141.7611),
						new mp.Vector3(-216.1954, 485.4664, 132.8434),
						new mp.Vector3(-297.033, 481.4624, 120.676),
						new mp.Vector3(-340.8448, 519.929, 124.5307),
						new mp.Vector3(-365.9675, 548.2215, 132.1381),
						new mp.Vector3(-408.0566, 573.0857, 132.49),
						new mp.Vector3(-499.3397, 540.4916, 124.1211),
						new mp.Vector3(-458.316, 529.8276, 129.7753),
						new mp.Vector3(-422.651, 523.0762, 126.5171),
						new mp.Vector3(-395.6163, 506.0445, 124.9943),
						new mp.Vector3(-358.8382, 457.1553, 125.467),
						new mp.Vector3(-410.725, 441.7651, 121.0006),
						new mp.Vector3(-445.7578, 392.1712, 112.4458),
						new mp.Vector3(-521.7825, 505.0926, 116.8674),
						new mp.Vector3(-530.7734, 476.4922, 111.2716),
						new mp.Vector3(-1551.7648, 416.6752, 114.1011),
						new mp.Vector3(-1488.3514, 438.5387, 117.4928),
						new mp.Vector3(-1461.108, 547.2063, 131.4577),
						new mp.Vector3(-1419.9858, 452.0644, 120.2817),
						new mp.Vector3(-1396.1025, 535.3153, 130.1946),
						new mp.Vector3(-1399.6674, 570.3518, 132.1261),
						new mp.Vector3(-1381.8466, 601.602, 141.085),
						new mp.Vector3(-1349.6259, 547.1422, 136.1111),
						new mp.Vector3(-1318.6775, 594.1528, 139.0154),
						new mp.Vector3(-1296.6447, 661.6966, 154.149),
						new mp.Vector3(-1277.0621, 622.7214, 146.9178),
						new mp.Vector3(-1241.3722, 638.8926, 147.1647),
						new mp.Vector3(-1251.7161, 674.8834, 153.6794),
						new mp.Vector3(-1208.2832, 659.8054, 148.9737),
						new mp.Vector3(-1186.4282, 683.9667, 156.0255),
						new mp.Vector3(-1151.4205, 723.3915, 159.4184),
						new mp.Vector3(-1117.2843, 747.4203, 168.6086),
						new mp.Vector3(-1135.5692, 792.5849, 176.732),
						new mp.Vector3(-1097.1184, 811.7963, 176.1109),
						new mp.Vector3(-1064.8671, 808.2537, 176.5585),
						new mp.Vector3(-1057.8181, 721.239, 168.8781),
						new mp.Vector3(-1021.9619, 679.1362, 165.5686),
						new mp.Vector3(-969.42, 676.0119, 165.664),
						new mp.Vector3(-936.0541, 679.644, 156.9927),
						new mp.Vector3(-912.362, 686.1888, 158.7936),
						new mp.Vector3(-881.8281, 682.5862, 156.2819),
						new mp.Vector3(-855.0757, 683.1184, 156.0546),
						new mp.Vector3(-812.251, 683.3345, 154.3124),
						new mp.Vector3(-778.1248, 656.0776, 151.0762),
						new mp.Vector3(-759.8382, 617.3212, 148.4924),
						new mp.Vector3(-745.2983, 579.6795, 149.5746),
						new mp.Vector3(-708.5769, 579.3409, 146.6684),
						new mp.Vector3(-670.5497, 586.7675, 148.4588),
						new mp.Vector3(-652.7961, 637.0605, 153.4016),
						new mp.Vector3(-703.4756, 642.1743, 162.8226),
						new mp.Vector3(-710.6687, 705.07, 170.0311),
						new mp.Vector3(-1500.5225, 532.1298, 124.9246),
						new mp.Vector3(-1004.9516, 819.915, 181.4358),
						new mp.Vector3(-1004.0009, 773.1719, 181.4164),
						new mp.Vector3(-961.6809, 745.985, 182.256),
						new mp.Vector3(-961.3988, 823.202, 186.3888),
						new mp.Vector3(-926.9666, 818.6385, 192.2165),
						new mp.Vector3(-911.7355, 767.3713, 190.7719),
						new mp.Vector3(-860.6265, 778.2876, 196.5111),
						new mp.Vector3(-826.4108, 799.8041, 209.0388),
						new mp.Vector3(-765.7726, 802.7439, 220.1402),
						new mp.Vector3(-644.8154, 866.4247, 233.765),
						new mp.Vector3(-606.3671, 849.9863, 215.9211),
						new mp.Vector3(-587.5919, 812.713, 205.4294),
						new mp.Vector3(-552.4383, 812.2053, 201.6881),
						new mp.Vector3(-489.318, 790.7706, 189.3236),
						new mp.Vector3(-499.2827, 744.1985, 174.7746),
						new mp.Vector3(-526.6348, 721.2883, 164.1579),
						new mp.Vector3(-552.8687, 696.2126, 160.6117),
						new mp.Vector3(-651.9386, 798.0287, 206.4794),
						new mp.Vector3(-602.8433, 775.7736, 194.8736),
						new mp.Vector3(-557.0255, 757.341, 190.0283),
						new mp.Vector3(-584.499, 725.2458, 187.7393),
						new mp.Vector3(-665.6356, 732.5856, 178.5371),
						new mp.Vector3(-939.1218, 466.6939, 97.9751),
						new mp.Vector3(-965.3634, 523.8985, 89.2707),
						new mp.Vector3(-993.6372, 525.2609, 91.6182),
						new mp.Vector3(-1042.8068, 520.963, 88.3296),
						new mp.Vector3(-1069.6432, 477.1584, 89.3108),
						new mp.Vector3(-1112.3893, 490.6759, 89.8236),
						new mp.Vector3(-1153.6946, 491.5437, 95.0367),
						new mp.Vector3(-1204.9966, 503.6065, 103.8199),
						new mp.Vector3(-1033.4442, 369.2632, 81.1012),
						new mp.Vector3(-1042.7271, 426.589, 83.8853),
						new mp.Vector3(-1019.923, 472.1809, 90.0974),
						new mp.Vector3(-984.6059, 482.2344, 93.066),
						new mp.Vector3(-976.3895, 443.408, 86.779),
						new mp.Vector3(-1875.604, -315.4442, 93.6635),
						new mp.Vector3(-1826.8995, -352.4193, 93.6769),
						new mp.Vector3(-1811.319, -400.9113, 63.375),
						new mp.Vector3(-1782.0033, -410.1709, 58.2346),
						new mp.Vector3(-1708.3726, -489.2326, 48.4064),
						new mp.Vector3(-1698.2179, -454.3617, 51.7249),
						new mp.Vector3(-1694.082, -429.3941, 51.9474),
						new mp.Vector3(-1673.9573, -405.7938, 53.4569),
						new mp.Vector3(-1655.4886, -378.0928, 57.5446),
						new mp.Vector3(-1599.9666, -345.3733, 55.1209),
						new mp.Vector3(-1625.6697, -372.1654, 51.6745),
						new mp.Vector3(-1650.3287, -408.3926, 50.9278),
						new mp.Vector3(-1669.2902, -435.9105, 48.6693),
						new mp.Vector3(-1654.1903, -526.4089, 75.0575),
						new mp.Vector3(-1601.3754, -449.6175, 53.9512),
						// new mp.Vector3(-1595.2629, -423.7146, 56.9518),
						new mp.Vector3(-1572.8357, -400.2521, 62.5447),
						new mp.Vector3(-1521.0497, -340.4952, 58.7076),
						new mp.Vector3(-1571.8013, -296.8785, 56.409),
						new mp.Vector3(-1586.0803, -273.3266, 59.1668),
						new mp.Vector3(-1533.442, -271.4286, 59.4118),
						new mp.Vector3(-1263.0667, -145.6117, 60.5574),
						new mp.Vector3(-1191.9078, -183.1342, 85.9425),
						new mp.Vector3(-1580.8363, -568.8616, 116.324),
						new mp.Vector3(-1529.1914, -528.3634, 72.2929),
						new mp.Vector3(-1513.672, -598.5295, 70.5956),
						new mp.Vector3(-1450.3329, -547.4945, 85.06),
						new mp.Vector3(-1394.8492, -476.9814, 91.2508),
						new mp.Vector3(-1298.9735, -438.6287, 107.476),
						new mp.Vector3(-1233.4132, -344.9642, 75.3911),
						new mp.Vector3(-1168.4254, -364.5367, 57.8268),
						new mp.Vector3(-1100.4883, -334.8086, 54.7675),
						new mp.Vector3(-906.2007, -220.3805, 87.3486),
						new mp.Vector3(-913.1404, -378.7976, 137.9032),
						new mp.Vector3(-895.0247, -446.4342, 171.814),
						new mp.Vector3(-1009.5797, -757.2078, 81.7485),
						new mp.Vector3(-817.7799, -716.3731, 124.2329),
						new mp.Vector3(-833.6382, -588.8061, 96.1979),
						new mp.Vector3(-804.0961, -613.4009, 96.1979),
						new mp.Vector3(-765.6896, -620.9877, 96.1979),
						new mp.Vector3(-773.4332, -776.5296, 84.8984),
						new mp.Vector3(-599.5079, -715.6461, 131.0403),
						new mp.Vector3(-464.2205, -692.1797, 81.1851),
						new mp.Vector3(-992.7933, -1303.0752, 50.6265),
						new mp.Vector3(-888.6415, -1281.4218, 50.6191),
						new mp.Vector3(-801.0314, -1190.5311, 51.2364),
						new mp.Vector3(-666.9355, -1134.2083, 48.4908),

						{ x: -1088.2549, y: -1675.3319, z: 14.1824 },
						{ x: -1055.9868, y: -1659.6527, z: 13.896 },
						{ x: -1073.4725, y: -1635.1516, z: 11.4191 },
						{ x: -1086.0659, y: -1618.7076, z: 14.1992 },
						{ x: -1110.9363, y: -1586.8879, z: 11.7561 },
						{ x: -1133.4594, y: -1540.9583, z: 15.5978 },
						{ x: -1152.6198, y: -1518.4747, z: 13.6769 },
						{ x: -1169.3407, y: -1548.1714, z: 19.4901 },
						{ x: -1030.299, y: -1605.7583, z: 12.5648 },
						{ x: -1058.5187, y: -1589.011, z: 13.7274 },
						{ x: -1061.6703, y: -1555.6219, z: 16.2043 },
						{ x: -1077.5736, y: -1510.6681, z: 14.9407 },
						{ x: -1092.2373, y: -1525.5692, z: 12.5311 },
						{ x: -1105.8726, y: -1492.3781, z: 13.8286 },
						{ x: -1149.4418, y: -1460.9539, z: 14.6879 },
						{ x: -1137.7715, y: -1422.712, z: 14.5363 },
						{ x: -1188.7781, y: -1404.5143, z: 17.8052 },
						{ x: -1158.844, y: -1380.6989, z: 18.8162 },
						{ x: -1214.9011, y: -1283.2219, z: 21.6805 },
						{ x: -1276.9583, y: -1301.0901, z: 11.5033 },
						{ x: -1289.9077, y: -1250.4, z: 15.1091 },
						{ x: -1338.6198, y: -1185.1384, z: 15.8337 },
						{ x: -1338.2638, y: -1133.3275, z: 21.9165 },
						{ x: -1366.734, y: -1055.0242, z: 17.1143 },
						{ x: -1290.5143, y: -1018.8527, z: 26.8535 },
						{ x: -1409.9077, y: -993.9033, z: 19.3722 },
						{ x: -1397.5516, y: -871.2923, z: 26.1626 },
						{ x: -1368.2109, y: -900.8307, z: 19.8777 },
						{ x: -1313.3934, y: -938.5846, z: 19.389 },
						{ x: -1100.3868, y: -1234.6549, z: 14.4015 },
						{ x: -1113.5472, y: -1204.2858, z: 9.9026 },
						{ x: -1142.9143, y: -1141.5165, z: 12.7333 },
						{ x: -1159.8198, y: -1110.3956, z: 9.751 },
						{ x: -1183.0286, y: -1061.9209, z: 11.5201 },
						{ x: -1202.0308, y: -1029.1516, z: 11.4528 },
						{ x: -1172.6241, y: -994.1011, z: 12.7333 },
						{ x: -1141.7935, y: -1049.7627, z: 12.1436 },
						{ x: -1116.6725, y: -1097.2087, z: 6.6505 },
						{ x: -1083.9297, y: -1148.6241, z: 13.0366 },
						{ x: -1036.5494, y: -1166.3341, z: 10.6102 },
						{ x: -1048.1802, y: -1130.8484, z: 11.7561 },
						{ x: -1093.0022, y: -1072.4308, z: 11.5033 },
						{ x: -1139.8286, y: -973.0022, z: 8.285 },
						{ x: -1084.7737, y: -1018.4308, z: 12.1099 },
						{ x: -1035.0461, y: -1116.0396, z: 12.9524 },
						{ x: -944.1363, y: -1101.877, z: 12.093 },
						{ x: -999.4418, y: -1091.4066, z: 13.0872 },
						{ x: -1002.0659, y: -1024.4967, z: 11.7729 },
						{ x: -1042.9451, y: -997.9385, z: 9.5992 },
						{ x: -1043.855, y: -919.5824, z: 10.189 },
						{ x: -1094.6505, y: -931.4637, z: 13.896 },
						{ x: -1153.7935, y: -980.0439, z: 11.2 },
						{ x: -1165.3187, y: -921.1912, z: 18.1589 },
						{ x: -1106.6373, y: -894.0264, z: 18.0242 },
						{ x: -965.0637, y: -909.1912, z: 11.9583 },
						{ x: -935.4462, y: -960.1846, z: 11.9751 },
						{ x: -900.3956, y: -1012.655, z: 11.8403 },
						{ x: -866.5846, y: -1095.8638, z: 9.5319 },
						{ x: 328.2198, y: -2104.3779, z: 24.6293 },
						{ x: 369.5341, y: -2067.033, z: 28.1846 },
						{ x: 393.2176, y: -2028.3956, z: 29.8696 },
						{ x: 329.011, y: -1992.4615, z: 30.5941 },
						{ x: 286.4176, y: -2040.3956, z: 26.0784 },
						{ x: 325.8462, y: -2057.5254, z: 27.3927 },
						{ x: 231.9824, y: -2043.6527, z: 21.5626 },
						{ x: 253.1209, y: -2019.9824, z: 23.2814 },
						{ x: 280.6813, y: -1986.5011, z: 23.1296 },
						{ x: 285.6132, y: -1976.7032, z: 24.7979 },
						{ x: 307.978, y: -1951.6879, z: 28.5721 },
						{ x: 198.5275, y: -2006.3209, z: 21.748 },
						{ x: 215.3802, y: -1982.0571, z: 24.7135 },
						{ x: 259.2923, y: -1931.4066, z: 28.6395 },
						{ x: 283.1341, y: -1903.5033, z: 30.8469 },
						{ x: 362.6637, y: -1891.3055, z: 28.37 },
						{ x: 382.7077, y: -1878.2902, z: 29.8696 },
						{ x: 411.2176, y: -1851.8506, z: 30.5267 },
						{ x: 424.7868, y: -1837.7935, z: 33.0374 },
						{ x: 355.7538, y: -1824.2505, z: 32.0938 },
						{ x: 332.6637, y: -1850.2814, z: 30.7964 },
						{ x: 499.0417, y: -1823.855, z: 32.0769 },
						{ x: 517.6747, y: -1791.5736, z: 32.8857 },
						{ x: 550.5231, y: -1766.7032, z: 37.0981 },
						{ x: 466.8659, y: -1772.9143, z: 33.2227 },
						{ x: 473.3143, y: -1732.2329, z: 32.3466 },
						{ x: 447.7978, y: -1711.8594, z: 34.2 },
						{ x: 424.2066, y: -1740.1055, z: 32.8015 },
						{ x: 467.2088, y: -1554.1846, z: 39.8279 },
						{ x: 329.4066, y: -1738.1274, z: 32.9363 },
						{ x: 317.0901, y: -1754.7032, z: 33.3912 },
						{ x: 303.2571, y: -1769.1165, z: 33.2733 },
						{ x: 284.5187, y: -1787.2483, z: 32.6161 },
						{ x: 286.0879, y: -1694.3473, z: 33.1553 },
						{ x: 273.1385, y: -1717.3715, z: 34.2336 },
						{ x: 253.9516, y: -1733.6571, z: 33.5934 },
						{ x: 154.8528, y: -1826.9275, z: 31.6219 },
						{ x: 135.4813, y: -1848.3429, z: 29.3473 },
						{ x: 154.5099, y: -1859.9077, z: 28.2014 },
						{ x: 196.6286, y: -1879.701, z: 29.0945 },
						{ x: 213.9297, y: -1891.9121, z: 28.7576 },
						{ x: 174.0132, y: -1921.0549, z: 24.5787 },

						{ x: 159.8506, y: -1938.6725, z: 23.989 },
						{ x: 143.9736, y: -1956.6329, z: 23.7532 },
						{ x: 116.756, y: -1967.3671, z: 25.3539 },
						{ x: 80.0835, y: -1965.455, z: 24.3429 },
						{ x: 71.9209, y: -1957.3451, z: 24.3934 },
						{ x: 67.2132, y: -1940.3737, z: 25.3202 },
						{ x: 51.956, y: -1929.6132, z: 25.7076 },
						{ x: 35.6308, y: -1918.6945, z: 25.1685 },
						{ x: 20.4527, y: -1899.6659, z: 26.7693 },
						{ x: 2.189, y: -1888.7605, z: 28.2351 },
						{ x: -9.7582, y: -1877.723, z: 27.3422 },
						{ x: -24.5538, y: -1863.8638, z: 30.0886 },
						{ x: -37.5429, y: -1854.1978, z: 29.3978 },
						{ x: 22.8132, y: -1842.2638, z: 27.7971 },
						{ x: 35.1956, y: -1849.7803, z: 28.7406 },
						{ x: 51.4813, y: -1859.7098, z: 26.3143 },
						{ x: 61.0022, y: -1869.8638, z: 26.4491 },
						{ x: 86.1626, y: -1901.3275, z: 25.3876 },
						{ x: 101.8154, y: -1910.0044, z: 24.7979 },
						{ x: 119.011, y: -1918.0747, z: 25.9604 },
						{ x: 130.0747, y: -1925.3934, z: 24.5787 },
						{ x: 143.7626, y: -1953.0198, z: 23.0286 },
						{ x: 175.0813, y: -1923.2439, z: 24.5787 },
						{ x: 144.4483, y: -1908.8176, z: 27.915 },
						{ x: 112.2857, y: -1894.2197, z: 27.1399 },
						{ x: 133.0813, y: -1850.9143, z: 28.8586 },
						{ x: 155.2615, y: -1860.1055, z: 28.1003 },
						{ x: 169.6879, y: -1866.3956, z: 27.578 },
						{ x: 194.2022, y: -1880.4528, z: 28.6058 },
						{ x: 211.3055, y: -1892.189, z: 29.5326 },
						{ x: 196.5495, y: -1719.1912, z: 32.8689 },
						{ x: 213.6, y: -1711.0154, z: 33.8967 },
						{ x: 221.7495, y: -1696.7737, z: 33.8462 },
						{ x: 237.7714, y: -1681.833, z: 33.1216 },
						{ x: 246.4879, y: -1666.5363, z: 32.8689 },
						{ x: 207.7055, y: -1645.556, z: 39.9795 },
						{ x: 170.1231, y: -1678.8792, z: 33.6439 },
						{ x: 245.433, y: -1371.9429, z: 48.6908 },
						{ x: 317.4725, y: -1269.2307, z: 42.4733 },
						{ x: -140.5978, y: -1688.5582, z: 40.973 },
						{ x: -126.9494, y: -1674.3165, z: 40.164 },
						{ x: -114.4615, y: -1653.2175, z: 40.333 },
						{ x: -100.9978, y: -1611.8506, z: 39.929 },
						{ x: -115.7538, y: -1569.9165, z: 41.883 },
						{ x: -145.9648, y: -1621.7935, z: 42.844 },
						{ x: -214.6813, y: -1675.2395, z: 41.378 },
						{ x: -225.2176, y: -1600.6681, z: 41.917 },
						{ x: -173.4857, y: -1554.4088, z: 43.282 },
						{ x: -111.5736, y: -1483.2395, z: 41.529 },
						{ x: 3300.9099, y: 5176.9316, z: 31.167 },
						{ x: 1971.2043, y: 3817.9912, z: 36.4916 },
						{ x: 1940.9143, y: 3803.1824, z: 35.649 },
						{ x: 1921.7407, y: 3827.8418, z: 36.1714 },
						{ x: 1928.545, y: 3895.6353, z: 36.2051 },
						{ x: 1915.1868, y: 3913.8989, z: 36.8623 },
						{ x: 1886.8616, y: 3921.6528, z: 37.6542 },
						{ x: 1892.0967, y: 3897.7451, z: 37.0308 },
						{ x: 1838.3209, y: 3912.8572, z: 36.9634 },
						{ x: 1810.3517, y: 3913.6353, z: 41.6307 },
						{ x: 1782.3956, y: 3909.0461, z: 39.7942 },
						{ x: 1834.3385, y: 3868.2856, z: 37.7722 },
						{ x: 1853.0505, y: 3862.5759, z: 36.9128 },
						{ x: 1738.4176, y: 3892.5232, z: 39.238 },
						{ x: 1723.0681, y: 3857.2878, z: 38.8843 },
						{ x: 1656.8572, y: 3819.0066, z: 39.2212 },
						{ x: 1538.6505, y: 3720.0659, z: 38.5978 },
						{ x: 1502.0439, y: 3699.5078, z: 39.0359 },
						{ x: 1430.9802, y: 3669.1648, z: 39.693 },
						{ x: 1431.1252, y: 3652.312, z: 41.7318 },
						{ x: 1437.0593, y: 3636.2241, z: 38.4799 },
						{ x: 1388.7957, y: 3654.4087, z: 38.5641 },
						{ x: 1397.578, y: 3639.6396, z: 37.9912 },
						{ x: 1394.8088, y: 3608.1626, z: 42.608 },
						{ x: 1369.8066, y: 3645.8506, z: 37.9238 },
						{ x: 1824.6857, y: 3734.0308, z: 37.0813 },
						{ x: 1519.1077, y: 6321.9692, z: 29.3641 },
						{ x: 62.2418, y: 6654.2637, z: 37.6542 },
						{ x: 36.0396, y: 6665.3936, z: 36.7781 },
						{ x: -14.0571, y: 6658.7734, z: 37.0308 },
						{ x: -44.6637, y: 6644.5317, z: 36.0029 },
						{ x: -128.9934, y: 6565.1738, z: 34.1157 },
						{ x: -216.6725, y: 6445.3188, z: 35.3964 },
						{ x: -247.5824, y: 6419.6309, z: 35.9019 },
						{ x: -276.4352, y: 6403.688, z: 35.7164 },
						{ x: -371.8813, y: 6351.5869, z: 37.6879 },
						{ x: -410.611, y: 6327.1387, z: 34.6549 },
						{ x: -443.3407, y: 6265.4507, z: 37.7047 },
						{ x: -464.3209, y: 6201.9297, z: 37.8564 },
						{ x: -380.2022, y: 6193.0552, z: 35.1267 },
						{ x: -359.8681, y: 6208.5361, z: 36.2051 },
						{ x: -347.8418, y: 6227.8154, z: 35.5985 },
						{ x: -380.1758, y: 6249.0728, z: 36.2219 },
						{ x: -366.1319, y: 6260.8613, z: 35.6996 },
						{ x: -329.8945, y: 6291.5078, z: 41.1758 },
						{ x: -298.5626, y: 6323.4067, z: 37.0308 },
						{ x: -275.1297, y: 6347.4727, z: 35.9692 },
						{ x: -246.8308, y: 6361.1606, z: 35.329 },
						{ x: -223.9648, y: 6371.5913, z: 35.1772 },
						{ x: -207.1253, y: 6390.1846, z: 38.5135 },
						{ x: -186.0791, y: 6405.7979, z: 36.104 },
						{ x: -18.6857, y: 6547.9385, z: 39.0527 },
						{ x: 2.2154, y: 6572.1494, z: 37.2836 },
						{ x: 28.7868, y: 6597.8638, z: 36.6263 },
						{ x: 6.1055, y: 6612.2505, z: 36.2557 },
						{ x: -21.6396, y: 6593.1958, z: 37.0476 },
						{ x: -41.6571, y: 6577.042, z: 36.1545 },
					],

					// Время затемнения экрана
					fadeTime: 1000,
				},
			},
			// {
			// 	name: 'DeliveryGifts',
			// 	controller: 'DeliveryGifts',
			// 	start: '2024-12-09',
			// 	end: '2025-01-15',
			// 	config: {
			// 		failCooldown: 5 * 60 * 1000,
			// 		teleportToEnd: {
			// 			position: { x: 123, y: 123, z: 123 },
			// 			rotation: 0,
			// 		},
			// 	},
			// },
			// {
			// 	name: 'WinterMinigames',
			// 	controller: 'MazeBankMinigames',
			// 	start: '2025-20-01',
			// 	end: '2025-02-06',
			// 	config: {
			// 		minigames: ['Taran', 'sws', 'RocketDm', 'PropHunt'],
			// 	},
			// },
			{
				name: 'PanelMenuRewards',
				controller: 'PanelMenuRewards',
				start: '2025-01-28',
				end: '2025-02-11',
				// start: ['27/01/2025 10:00:00', 'MM/DD/YYYY HH:mm:ss'],
				// end: ['11/02/2025 06:00:00', 'MM/DD/YYYY HH:mm:ss'],
				config: {
					quest: {
						LostVehicles: [
							{
								rewardId: 1,
								type: awardEnum.seasonPassProgress,
								amount: 500,
								hasOneReceipt: true,
							},
							{
								rewardId: 2,
								type: awardEnum.seasonPassProgress,
								amount: 500,
								hasOneReceipt: true,
							},
							{
								rewardId: 3,
								type: awardEnum.seasonPassProgress,
								amount: 500,
								hasOneReceipt: true,
							},
							{
								rewardId: 4,
								type: awardEnum.seasonPassProgress,
								amount: 500,
								hasOneReceipt: true,
							},
							{
								rewardId: 5,
								type: awardEnum.seasonPassProgress,
								amount: 250,
							},
							{
								rewardId: 6,
								type: awardEnum.seasonPassProgress,
								amount: 250,
							},
							{
								rewardId: 7,
								type: awardEnum.seasonPassProgress,
								amount: 250,
							},
							{
								rewardId: 8,
								type: awardEnum.seasonPassProgress,
								amount: 250,
							},
						],
					},
				},
			},
		],
	},
	[SEASON_EVENTS_TYPES.VALENTINE]: {
		name: 'valentine',
		start: '2025-02-13 6:00:00',
		end: '2025-02-17 06:00:00',
		startForTest: '2025-02-05 00:00:00',
		withYear: true,
		activities: [
			{
				name: 'PromoScreen',
				controller: 'PromoScreen',
				config: {
					screens: [
						{
							modal: 'eventModalValentine2025',
							cooldown: 20 * 60 * 60 * 1000,
							action: 'setGps',
							args: {
								x: -320.7297,
								y: 6172.6021,
								z: 32.3129,
								title: 'blips.valentineMarket'
							}
						},
					],
				},
			},
			{
				name: 'Event Peds',
				controller: 'EventPedsActivity',
				config: {
					peds: [
						{
							// Bot on market
							pedId: 404,
							start: '2025-02-05 6:00:00', // год - месяц - день
							end: '2025-02-17 6:00:00',
						},
					],
				},
			},
			{
				name: 'GiftValentine',
				controller: 'GiftValentineActivity',
				config: {
					pajamaColors: [0, 1, 2, 3, 4, 5, 6]
				}
			},
		]
	}
});
