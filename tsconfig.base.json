{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "removeComments": true, "allowSyntheticDefaultImports": true, "noImplicitAny": false, "strictNullChecks": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2022", "module": "esnext", "lib": ["es2022", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "allowJs": true, "paths": {"@i18n/game": ["i18n/game/index.js"], "@majestic-backend/broker": ["libs/broker/src/index.ts"], "@majestic-backend/config": ["libs/config/src/index.ts"], "@majestic-backend/contracts": ["libs/contracts/src/index.ts"], "@majestic-backend/database": ["libs/database/src/index.ts"], "@majestic-backend/discord": ["libs/discord/src/index.ts"], "@majestic-backend/errors": ["libs/errors/src/index.ts"], "@majestic-backend/gitlab": ["libs/gitlab/src/index.ts"], "@majestic-backend/grafana": ["libs/grafana/src/index.ts"], "@majestic-backend/jwt": ["libs/jwt/src/index.ts"], "@majestic-backend/lang": ["libs/lang/src/index.ts"], "@majestic-backend/metrics": ["libs/metrics/src/index.ts"], "@majestic-backend/prisma-client-backend": ["libs/prisma-client-backend/src/index.ts"], "@majestic-backend/prisma-client-game": ["libs/prisma-client-game/src/index.ts"], "@majestic-backend/redis": ["libs/redis/src/index.ts"], "@majestic-backend/s3": ["libs/s3/src/index.ts"], "@majestic-backend/test": ["libs/test/src/index.ts"], "@majestic-backend/types": ["libs/types/src/index.ts"], "@majestic-backend/utils": ["libs/utils/src/index.ts"], "@majestic-backend/yandex-tracker": ["libs/yandex-tracker/src/index.ts"], "@shared_config/*": ["shared_config/*"]}}, "exclude": ["node_modules", "tmp"]}