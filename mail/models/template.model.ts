export enum MAIL_TEMPLATES {
  EMAIL_CONFIRMATION = 'emailconfirmation',
  FREEZE_SUBSCRIPTION = 'freezesubscription',
  REGISTER_URL = 'registerurl',
  REGISTER_PROMO = 'registerpromo',
  GTA_KEY = 'gtakey',
  SUBSCRIPTION_END = 'subscriptionend',
  THANKS_FOR_SUBSCRIBING = 'thanksforsubscribing',
}

export enum ALLOWED_LANGS {
  RU = 'RU',
  BR = 'BR',
  PL = 'PL',
  DE = 'DE',
  EN = 'EN',
}

export interface IMailTemplateVariables {
  [MAIL_TEMPLATES.EMAIL_CONFIRMATION]: { serial: string }
  [MAIL_TEMPLATES.FREEZE_SUBSCRIPTION]: { serverName: string; login: string }
  [MAIL_TEMPLATES.SUBSCRIPTION_END]: { serverName: string; login: string }
  [MAIL_TEMPLATES.REGISTER_URL]: { registerUrl: string }
  [MAIL_TEMPLATES.GTA_KEY]: { serial: string }
  [MAIL_TEMPLATES.REGISTER_PROMO]: Record<string, never>
  [MAIL_TEMPLATES.THANKS_FOR_SUBSCRIBING]: Record<string, never>
}
