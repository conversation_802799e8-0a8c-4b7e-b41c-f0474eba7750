import { IMailTemplateVariables, MAIL_TEMPLATES, ALLOWED_LANGS } from '../models'

export interface ISendEmailDTO<
  T extends MAIL_TEMPLATES = MAIL_TEMPLATES,
  Y extends IMailTemplateVariables[T] = IMailTemplateVariables[T],
> {
  template: T
  variables: Y
  to: string
  // Email отправителя. Если не указан - берется из региона
  from?: string
  lang: ALLOWED_LANGS
  // Тема письма. Если не указана - берется из шаблона
  subject?: string
}
