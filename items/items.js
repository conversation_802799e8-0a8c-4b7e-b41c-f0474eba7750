/**
 * Доступные параметры на каждый предмет.
 * @param  {Number}     id                      ИД предмета (Обязательный)
 * @param  {String}     title                   Название предмета (Обязательный).
 * @param  {String}     model                   Моделька предмета (Обязательный).
 * @param  {Number}     width                   Ширина предмета (Обязательный).
 * @param  {Number}     height                  Высота предмета (Обязательный).
 * @param  {String}     tag                     Категория (Обязательный).
 * @param  {String}     description             Описание (Обязательный).
 * @param  {Boolean}    usable                  Можно ли использовать предмет.
 * @param  {Boolean}    takeable                Можно ли достать предмет.
 * @param  {Boolean}    dropOnDeath             Выпадает ли при смерти.
 * @param  {Boolean}    dropOnQuit              Выпадает ли при выходе.
 * @param  {Boolean}    deathSafe               Выпадает ли из быстрых/активных слотов.
 * @param  {Boolean}    canDrag                 Можно ли выкидывать/перетаскивать вещи куда-то кроме инвентаря.
 * @param  {Boolean}    canDragCop              Можно ли выкидывать/перетаскивать вещи куда-то кроме инвентаря ГОС. органам.
 * @param  {Boolean}    canPickupToBag          Можно ли поднимать с земли в рюкзак
 * @param  {Boolean}    canSeize                Можно ли изымать гос. органам предмет.
 * @param  {Boolean}    hasSerial               Создается ли серийный номер при крафте.
 * @param  {Number}     caliber                 ID предмета патронов.
 * @param  {Number}     materials               Стоимость в материалах.
 * @param  {Number}     armour                  По сути это состояние предмета. Обычно измеряется от 0 до 100.
 * @param  {String}     hash                    Хэш если это оружие.
 * @param  {Number}     limit                   Макс лимит износа предмета.
 * @param  {Number}     shopCount               Кол-во выдаваемых предметов при покупке.
 * @param  {Number}     barPrice                Стоимость предмета в баре.
 * @param  {Number}     weight                  Масса предмета, измеряется в граммах.
 * @param  {Number}     stack                   Максимальное количество предметов в одной клетки
 * @param  {Boolean}    noStack                 Для предметов где стак не является количеством, банит при попытке разделить предмет экзекьютом.
 * @param  {Boolean}    forWallet               Можно ли класть предмет в кошелёк
 * @param  {Boolean}    forKeychain             Можно ли класть предмет в ключницу
 * @param  {Boolean}    fastSlot                Можно ли класть предмет в быстрый слот
 * @param  {Boolean}    isMelee                 Является ли оружие ближним боем.
 * @param  {Number}     supplyBoxId             ИД предмета который находится вне ящика
 * @param  {Object}     animateItem             При использовании предмета вызывается анимация в цикле с указанными данными
 * @param  {Boolean}    isTool                  Если предмет это оружие, но используется как инструмент (топоры, парашюты и тд)
 * @param {Array<Number>} fractionsCantSell     Фракции, которые не могут продать данный предмет скупщику на рынке
 * @param  {Boolean}    canUtilize              Предмет можно утилизировать будучи в гос фракции
 * @param  {Number}     evidenceReward          Награда за утилизацию предмета в пакет для улик
 * @param  {Boolean}    canToggle               Добавляет предмету кнопки включить/выключить через info.active
 * @param  {Number}     grindSlots              Слоты для точильных камней, добавляет возможность заточить инструмент.
 * @param  {Boolean}    demorganSafe            Предотвращяет удаление при посадке в деморган.
 * @param  {Boolean}    demorganRemove          Удаляет предмет при посадке в деморган.
 * @param  {Boolean}    isSupplyBox             Не позволяет перенести в багажник тс, если это не матовозка.
 * @param  {Array<string>} tags                 Массив тэгов предмета, которые влияют на его поведения в системах
 *
 * @param  {Object}     food                    Добавляет предмету свойства еды.
 * @param  {Number}     food.hunger             Добавление сытости персонаджа.
 * @param  {Number}     food.water              Добавление жажды персонажа.
 * @param  {Number}     food.hp                 Добавление жизней персонажа.
 *
 * @param  {Object}     stats                    Добавляет предмету свойства еды.
 * @param  {Number}     stats.hunger             Добавление сытости персонаджа.
 * @param  {Number}     stats.water              Добавление жажды персонажа.
 * @param  {Number}     stats.hp                 Добавление жизней персонажа.
 * @param  {Number}     stats.poisonChance       Шанс отравления едой
 */
export default [
    {
        id: 0,
        title: 'items[0].title'
    },
    {
        // ID карта
        id: 1,
        title: 'items[1].title',
        model: 'prop_passport_01',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        description: 'items[1].description',
        canDrag: false,
        usable: true,
        canDragWallet: false,
        forWallet: true,
    },
    {
        // Свертки наличных
        id: 2,
        title: 'items[2].title',
        model: 'bkr_prop_bkr_cash_roll_01',
        weight: 250,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.misc',
        description: 'items[2].description'
    },
    {
        // Аккумулятор
        id: 3,
        title: 'items[3].title',
        model: 'bkr_prop_meth_lithium',
        weight: 10000,
        width: 2,
        height: 2,
        tag: 'items.tags.autoParts',
        description: 'items[3].description'
    },
    {
        // Перекись водорода
        id: 4,
        title: 'items[4].title',
        model: 'bkr_prop_meth_sodium',
        weight: 500,
        width: 1,
        height: 2,
        stack: 1,
        tag: 'items.tags.medicine',
        description: 'items[4].description'
    },
    {
        // Денежная компенсация
        id: 5,
        title: 'items[5].title',
        model: 'bkr_prop_money_wrapped_01',
        weight: 500,
        width: 1,
        height: 1,
        cashOut: true,
        tag: 'items.tags.misc',
        description: 'items[5].description'
    },
    {
        // Сварочный аппарат
        id: 6,
        title: 'items[6].title',
        model: 'v_ind_cm_weldmachine',
        weight: 5700,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[6].description'
    },
    {
        // Напильник
        id: 7,
        title: 'items[7].title',
        model: 'gr_prop_gr_rasp_01',
        weight: 50,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[7].description'
    },
    {
        // Отвертка
        id: 8,
        title: 'items[8].title',
        model: 'gr_prop_gr_sdriver_01',
        weight: 60,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        shopCount: 0,
        description: 'items[8].description'
    },
    {
        // Скотч
        id: 9,
        title: 'items[9].title',
        model: 'gr_prop_gr_tape_01',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[9].description',
        buffs: [
            {
                name: 'TapedMouth'
            }
        ]
    },
    {
        // Набор инструментов
        id: 10,
        title: 'items[10].title',
        model: 'gr_prop_gr_tool_box_02a',
        weight: 400,
        width: 2,
        height: 2,
        tag: 'items.tags.tools',
        description: 'items[10].description'
    },
    {
        // Мачете
        id: 11,
        title: 'items[11].title',
        model: 'w_me_machette_lr',
        weight: 700,
        width: 1,
        height: 3,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: true,
        takeable: true,
        isTool: true,
        disableDropOnCapt: true,
        hash: 'weapon_machete',
        isMelee: true,
        description: 'items[11].description',
        coins: 100
    },
    {
        // Пассатижи
        id: 12,
        title: 'items[12].title',
        model: 'prop_pliers_01',
        weight: 350,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[12].description'
    },
    {
        // Банка таблеток
        id: 13,
        title: 'items[13].title',
        model: 'prop_cs_pills',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.medicine',
        description: 'items[13].description'
    },
    {
        // Навесной замок
        id: 14,
        title: 'items[14].title',
        model: 'prop_cs_padlock',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[14].description'
    },
    {
        // Рация
        id: 15,
        title: 'items[15].title',
        model: 'prop_cs_hand_radio',
        weight: 300,
        width: 1,
        height: 2,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[15].description'
    },
    {
        // Сироп от кашля
        id: 16,
        title: 'items[16].title',
        model: 'prop_cs_script_bottle_01',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.medicine',
        description: 'items[16].description'
    },
    {
        // Бутылка таблеток
        id: 17,
        title: 'items[17].title',
        model: 'prop_cs_script_bottle',
        weight: 300,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.medicine',
        description: 'items[17].description'
    },
    {
        // Фондовый рынок #1
        id: 18,
        title: 'items[18].title',
        model: 'prop_cs_stock_book',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.books',
        description: 'items[18].description'
    },
    {
        // Веревка
        id: 19,
        title: 'items[19].title',
        model: 'prop_devin_rope_01',
        weight: 350,
        width: 2,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[19].description',
        buffs: [
            {
                name: 'TiedLegs'
            }
        ]
    },
    {
        // Мятая Футболка
        id: 20,
        title: 'items[20].title',
        model: 'prop_cs_tshirt_ball_01',
        weight: 250,
        width: 1,
        height: 1,
        tag: 'items.tags.personals',
        description: 'items[20].description'
    },
    {
        // Посадочный совок
        id: 21,
        title: 'items[21].title',
        model: 'prop_cs_trowel',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[21].description'
    },
    {
        // Защитные наушники
        id: 22,
        title: 'items[22].title',
        model: 'prop_ear_defenders_01',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[22].description'
    },
    {
        // Аппарат ЭГК
        id: 23,
        title: 'items[23].title',
        model: 'prop_ecg_01',
        weight: 3000,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[23].description'
    },
    {
        // Пачка сигарет
        id: 24,
        title: 'items[24].title',
        model: 'p_fag_packet_01_s',
        weight: 20,
        width: 1,
        height: 1,
        takeable: true,
        fastSlot: true,
        tag: 'items.tags.products',
        shopCount: 12,
        stack: 12,
        description: 'items[24].description',
        stats: {
            hp: -2,
            poisonChance: 1
        },
        useItem: {
            overrideEffect: 'UseItemCig',

            animationProp: 'cigarette',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke',

            usingSound: 'cigarette',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            useFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_smoke',
                boneIndex: 20279,
                timeout: 10000
            },

            processFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_exhale_mouth',
                boneIndex: 20279,
                timeout: 10000
            },

            processDelay: 3000,

            seasonPassTriggers: ['task_use_cig'],

            duration: 28000
        }
    },
    {
        // Удостоверение FIB
        id: 25,
        title: 'items[25].title',
        model: 'prop_fib_badge',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        usable: true,
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[25].description'
    },
    {
        // Моторное масло
        id: 26,
        title: 'items[26].title',
        model: 'prop_oiltub_03',
        weight: 4300,
        width: 2,
        height: 2,
        tag: 'items.tags.consumables',
        description: 'items[26].description'
    },
    {
        // Дверной замок
        id: 27,
        title: 'items[27].title',
        model: 'p_car_keys_01',
        weight: 700,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[27].description'
    },
    {
        // Кожаный портфель
        id: 28,
        title: 'items[28].title',
        model: 'prop_ld_case_01_s',
        weight: 300,
        width: 2,
        height: 2,
        tag: 'items.tags.personals',
        takeable: true,
        canSeize: false,
        limit: 1,
        description: 'items[28].description'
    },
    {
        // Пачка сигар Estancia
        id: 29,
        title: 'items[29].title',
        model: 'p_cigar_pack_02_s',
        weight: 25,
        width: 1,
        height: 1,
        takeable: true,
        tag: 'items.tags.products',
        shopCount: 6,
        stack: 6,
        barPrice: 30000,
        description: 'items[29].description',
        useItem: {
            overrideEffect: 'UseItemCig',

            animationProp: 'cigarette',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke',

            usingSound: 'cigarette',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            useFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_smoke',
                boneIndex: 20279,
                timeout: 10000
            },

            processFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_exhale_mouth',
                boneIndex: 20279,
                timeout: 10000
            },

            processDelay: 3000,

            seasonPassTriggers: ['task_use_cig'],

            duration: 28000
        }
    },
    {
        // Удочка
        id: 30,
        title: 'items[30].title',
        model: 'prop_fishing_rod_01',
        weight: 350,
        width: 4,
        height: 2,
        tag: 'items.tags.tools',
        description: 'items[30].description'
    },
    {
        // Удостоверение Government
        id: 31,
        title: 'items[31].title',
        model: 'prop_fib_badge',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        usable: true,
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[31].description'
    },
    {
        // Скотч
        id: 32,
        title: 'items[32].title',
        model: 'prop_gaffer_tape',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.consumables',
        description: 'items[32].description',
        buffs: [
            {
                name: 'TapedMouth'
            }
        ]
    },
    {
        // Медицинские материалы
        id: 33,
        title: 'items[33].title',
        model: 'mj_prop_addon_medbox',
        weight: 100,
        width: 5,
        height: 3,
        usable: true,
        tag: 'items.tags.materials',
        materialColor: 'red',
        materialPrice: 50,
        stack: 500,
        description: 'items[33].description'
    },
    {
        // Кобура
        id: 34,
        title: 'items[34].title',
        model: 'prop_holster_01',
        weight: 250,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[34].description'
    },
    {
        // Лом
        id: 35,
        title: 'items[35].title',
        model: 'w_me_crowbar',
        weight: 3500,
        width: 1,
        height: 4,
        caliber: true,
        takeable: true,
        tag: 'items.tags.personals',
        hash: 'weapon_crowbar',
        isMelee: true,
        disableDropOnCapt: true,
        description: 'items[35].description',
        coins: 50
    },
    {
        // Алкотестер
        id: 36,
        title: 'items[36].title',
        model: 'ng_proc_inhaler01a',
        weight: 200,
        width: 1,
        height: 2,
        tag: 'items.tags.facilities',
        materials: 5,
        description: 'items[36].description'
    },
    {
        // 9x19mm
        id: 37,
        title: 'items[37].title',
        model: 'tor_ammo_06_9x19mm',
        boxItemId: 476,
        materialType: 'green',
        materials: 1,
        weight: 6,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        canSeize: true,
        clip: 96,
        description: 'items[37].description',
        stack: 96
    },
    {
        // 12ga Buckshots
        id: 38,
        title: 'items[38].title',
        model: 'tor_ammo_07_12gaBuckshots',
        boxItemId: 477,
        materialType: 'green',
        materials: 1,
        weight: 40,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        canSeize: true,
        clip: 48,
        description: 'items[38].description',
        stack: 96
    },
    {
        // 7.62x39mm
        id: 39,
        title: 'items[39].title',
        model: 'tor_ammo_04_7_62x39mm',
        boxItemId: 483,
        materialType: 'green',
        materials: 7,
        weight: 10,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        canSeize: true,
        clip: 240,
        description: 'items[39].description',
        stack: 240
    },
    {
        // Маска
        id: 40,
        title: 'items[40].title',
        model: 'q_box_m',
        weight: 200,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.clothesAndAccessories',
        description: 'masks'
    },
    {
        // Низ
        id: 41,
        title: 'items[41].title',
        model: 'q_box_pt',
        weight: 600,
        width: 2,
        height: 2,
        tag: 'items.tags.clothesAndAccessories',
        description: 'legs'
    },
    {
        // Обувь
        id: 42,
        title: 'items[42].title',
        model: 'q_box_sh',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'shoes'
    },
    {
        // Аксессуары
        id: 43,
        title: 'items[43].title',
        model: 'prop_acs',
        weight: 50,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'accessories'
    },
    {
        // Майка
        id: 44,
        title: 'items[44].title',
        model: 'q_box_cl',
        weight: 220,
        width: 2,
        height: 2,
        tag: 'items.tags.clothesAndAccessories',
        description: 'undershirts'
    },
    {
        // Верхняя одежда
        id: 45,
        title: 'items[45].title',
        model: 'q_box_cur',
        weight: 800,
        width: 2,
        height: 2,
        tag: 'items.tags.clothesAndAccessories',
        description: 'tops'
    },
    {
        // Головной убор
        id: 46,
        title: 'items[46].title',
        model: 'q_box_hat',
        weight: 300,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'head'
    },
    {
        // Очки
        id: 47,
        title: 'items[47].title',
        model: 'prop_glass_q',
        weight: 30,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'glasses'
    },
    {
        // Серьги
        id: 48,
        title: 'items[48].title',
        model: 'prop_earr',
        weight: 20,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'ears'
    },
    {
        // Наручные часы
        id: 49,
        title: 'items[49].title',
        model: 'prop_watch_q',
        weight: 40,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'watches'
    },
    {
        // Браслет
        id: 50,
        title: 'items[50].title',
        model: 'prop_brac',
        weight: 30,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'bracelets'
    },
    {
        // Нашивки
        id: 51,
        title: 'items[51].title',
        model: 'prop_brac',
        weight: 10,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'decals'
    },
    {
        // Бронежилет
        id: 52,
        title: 'items[52].title',
        model: null,
        weight: 5000,
        width: 2,
        height: 2,
        tag: 'items.tags.clothesAndAccessories',
        description: 'armor',
        canUtilize: true
    },
    {
        // Рюкзак
        id: 53,
        title: 'items[53].title',
        model: 'vw_prop_vw_backpack_01a',
        weight: 600,
        width: 2,
        height: 2,
        tag: 'items.tags.clothesAndAccessories',
        description: 'bags',
        bagConfig: {
            41: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            45: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2000: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2001: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2002: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2003: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2004: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2005: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2006: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2007: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2009: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2010: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2011: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2012: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2013: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2014: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2015: {
                capacity: [25000, 5000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2016: {
                capacity: [10000, 25000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2017: {
                capacity: [20000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2018: {
                capacity: [20000, 20000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2019: {
                capacity: [10000, 20000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2020: {
                capacity: [7500, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2021: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2022: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2023: {
                capacity: [15000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2024: {
                capacity: [10000, 12000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2025: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2045: {
                capacity: [0, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2046: {
                capacity: [0, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2047: {
                capacity: [0, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2049: {
                capacity: [0, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2050: {
                capacity: [0, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2051: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2052: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2053: {
                capacity: [7500, 12000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2054: {
                capacity: [15000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2055: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2056: {
                capacity: [10000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2057: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2058: {
                capacity: [12000, 12000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2059: {
                capacity: [10000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2060: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2061: {
                capacity: [7500, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2062: {
                capacity: [12000, 12000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2063: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2064: {
                capacity: [15000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2065: {
                capacity: [12000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2066: {
                capacity: [10000, 12000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2067: {
                capacity: [12000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2068: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2069: {
                capacity: [15000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2070: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2071: {
                capacity: [10000, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2072: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2073: {
                capacity: [7500, 12000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2074: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2075: {
                capacity: [12000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2076: {
                capacity: [7500, 12000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2077: {
                capacity: [7500, 12000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2078: {
                capacity: [12000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2079: {
                capacity: [12000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2080: {
                capacity: [7500, 12000],  // Рюкзак дракона
                columns: [10, 10],
                rows: [30, 30]
            },
            2081: {
                capacity: [7500, 12000],  // Рюкзак зайца
                columns: [10, 10],
                rows: [30, 30]
            },
            2082: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2083: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2084: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2085: {
                capacity: [7500, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2086: {
                capacity: [15000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2087: {
                capacity: [15000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2088: {
                capacity: [15000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2089: {
                capacity: [12000, 7500],  // мужской узнать вес у женщин
                columns: [10, 10],
                rows: [30, 30]
            },
            2090: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2091: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2092: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2093: {
                capacity: [15000, 12000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2094: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2095: {
                capacity: [22000, 15000], // LV NY2024
                columns: [10, 10],
                rows: [30, 30]
            },
            2096: {
                capacity: [10000, 15000], // HOUSE NY2024
                columns: [10, 10],
                rows: [30, 30]
            },
            2097: {
                capacity: [22000, 15000], // GOYARD NY2024
                columns: [10, 10],
                rows: [30, 30]
            },
            2098: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2099: {
                capacity: [15000, 22000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2100: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2101: {
                capacity: [15000, 22000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2102: {
                capacity: [12000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2103: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2104: {
                capacity: [25000, 7500],
                columns: [10, 10],
                rows: [30, 30]
            },
            2105: {
                capacity: [25000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2106: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2107: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2108: {
                capacity: [15000, 0],
                columns: [10, 10],
                rows: [30, 30]
            },
            2109: {
                capacity: [15000, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2110: {
                capacity: [15000, 25000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2111: {
                capacity: [10000, 25000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2112: {
                capacity: [0, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2113: {
                capacity: [0, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2114: {
                capacity: [0, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2115: {
                capacity: [0, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2116: {
                capacity: [0, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2117: {
                capacity: [0, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2118: {
                capacity: [0, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2119: {
                capacity: [0, 15000],
                columns: [10, 10],
                rows: [30, 30]
            },
            2120: {
                capacity: [0, 10000],
                columns: [10, 10],
                rows: [30, 30]
            },
        }
    },
    {
        // Перчатки
        id: 54,
        title: 'items[54].title',
        model: 'prop_gloves',
        weight: 20,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'gloves'
    },
    {
        // Аккумулятор
        id: 55,
        title: 'items[55].title',
        model: 'prop_battery_01',
        weight: 9000,
        width: 1,
        height: 1,
        tag: 'items.tags.autoParts',
        description: 'items[55].description'
    },
    {
        // Аккумулятор
        id: 56,
        title: 'items[56].title',
        model: 'prop_battery_02',
        weight: 9000,
        width: 1,
        height: 1,
        tag: 'items.tags.autoParts',
        description: 'items[56].description'
    },
    {
        // Легкий бронежилет
        id: 57,
        title: 'items[57].title',
        model: 'prop_armour_pickup',
        hasSerial: true,
        weight: 2000,
        width: 2,
        height: 3,
        usable: true,
        canSeize: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        description: 'items[57].description',
        canUtilize: true
    },
    {
        // Бонг
        id: 58,
        title: 'items[58].title',
        model: 'prop_bong_01',
        weight: 700,
        width: 1,
        height: 2,
        canSeize: true,
        materials: 15,
        tag: 'items.tags.facilities',
        description: 'items[58].description'
    },
    {
        // Зажигалка
        id: 59,
        title: 'items[59].title',
        model: 'ng_proc_ciglight01a',
        weight: 12,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[59].description'
    },
    {
        // Технические материалы
        id: 60,
        title: 'items[60].title',
        model: 'mj_prop_addon_otherpbox',
        weight: 100,
        width: 5,
        height: 3,
        usable: true,
        tag: 'items.tags.materials',
        materialColor: 'blue',
        materialPrice: 25,
        stack: 500,
        description: 'items[60].description'
    },
    {
        // Сигареты RedWood
        id: 61,
        title: 'items[61].title',
        model: 'ng_proc_cigpak01c',
        weight: 19,
        width: 1,
        height: 1,
        tag: 'items.tags.rubbish',
        description: 'items[61].description',
        stats: {
            hp: -2,
            poisonChance: 1
        },
        useItem: {
            overrideEffect: 'UseItemCig',

            animationProp: 'cigarette',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke',

            usingSound: 'cigarette',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            useFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_smoke',
                boneIndex: 20279,
                timeout: 10000
            },

            processFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_exhale_mouth',
                boneIndex: 20279,
                timeout: 10000
            },

            processDelay: 3000,

            seasonPassTriggers: ['task_use_cig'],

            duration: 28000
        }
    },
    {
        // Стакан Кофе
        id: 62,
        title: 'items[62].title',
        model: 'ng_proc_coffee_01a',
        weight: 600,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        barPrice: 30000,
        description: 'items[62].description',
        buffs: [
            {
                name: 'ResetStamina'
            }
        ],
        stats: {
            // СТАТЫ ЕДЫ
            water: 25,
        },
        useItem: {
            animationProp: 'coffee',
            animationDict: 'amb@world_human_drinking@coffee@male@idle_a',
            animationName: 'idle_a',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'coffee',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,

            isAlcohol: false,
        }
    },
    {
        // Стакан Кофе
        id: 63,
        title: 'items[63].title',
        model: 'ng_proc_coffee_02a',
        weight: 600,
        width: 1,
        height: 1,
        tag: 'items.tags.rubbish',
        description: 'items[63].description'
    },
    {
        // Банка с лекарством
        id: 64,
        title: 'items[64].title',
        model: 'ng_proc_drug01a002',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.medicine',
        description: 'items[64].description'
    },
    {
        // Волтметр
        id: 65,
        title: 'items[65].title',
        model: 'prop_voltmeter_01',
        weight: 500,
        width: 2,
        height: 2,
        tag: 'items.tags.facilities',
        description: 'items[65].description'
    },
    {
        // Ключ от автомобиля
        id: 66,
        title: 'items[66].title',
        model: 'prop_cuff_keys_01',
        weight: 250,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        forWallet: true,
        forKeychain: true,
        canDragWallet: false,
        description: 'items[66].description',
        canRename: true
    },
    {
        // Планшет iPad
        id: 67,
        title: 'items[67].title',
        model: 'prop_cs_tablet',
        weight: 700,
        width: 3,
        height: 2,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[67].description'
    },
    {
        // Корм для скота
        id: 68,
        title: 'items[68].title',
        model: 'prop_feed_sack_01',
        weight: 25000,
        width: 3,
        height: 3,
        tag: 'items.tags.agriculture',
        description: 'items[68].description'
    },
    {
        // Генератор 1 уровня
        id: 69,
        title: 'items[69].title',
        model: 'prop_generator_01a',
        weight: 10000,
        width: 4,
        height: 3,
        tag: 'items.tags.facilities',
        description: 'items[69].description'
    },
    {
        // Генератор 2 уровня
        id: 70,
        title: 'items[70].title',
        model: 'prop_generator_01a',
        weight: 15000,
        width: 4,
        height: 3,
        tag: 'items.tags.facilities',
        description: 'items[70].description'
    },
    {
        // Генератор 3 уровня
        id: 71,
        title: 'items[71].title',
        model: 'prop_generator_01a',
        weight: 20000,
        width: 4,
        height: 3,
        tag: 'items.tags.facilities',
        description: 'items[71].description'
    },
    {
        // Электростанция 150кВт
        id: 72,
        title: 'items[72].title',
        model: 'prop_generator_04',
        weight: 2640000,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[72].description'
    },
    {
        // Газовый баллон S
        id: 73,
        title: 'items[73].title',
        model: 'prop_gascyl_01a',
        weight: 5250,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[73].description'
    },
    {
        // Газовый баллон M
        id: 74,
        title: 'items[74].title',
        model: 'prop_gascyl_02b',
        weight: 14250,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[74].description'
    },
    {
        // Газовый баллон L
        id: 75,
        title: 'items[75].title',
        model: 'prop_gascyl_04a',
        weight: 26250,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[75].description'
    },
    {
        // Бумбокс
        id: 76,
        title: 'items[76].title',
        model: 'prop_ghettoblast_01',
        weight: 5300,
        width: 2,
        height: 2,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[76].description'
    },
    {
        // Канистра
        id: 77,
        title: 'items[77].title',
        model: 'w_am_jerrycan',
        weight: 1000,
        width: 3,
        height: 3,
        takeable: true,
        fastSlot: true,
        deathSafe: false,
        isTool: true,
        tag: 'items.tags.autoParts',
        shopCount: 10,
        description: 'items[77].description'
    },
    {
        // Аптечка
        id: 78,
        title: 'items[78].title',
        model: 'prop_ld_health_pack',
        weight: 350,
        width: 2,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.medicine',
        description: 'items[78].description',
        // usedData: {
        //     defaultAnimActionData: null,
        //     defaultAttachment: 'medkit',
        //     //usedAnimActionData = null,

        //     intervalTime: 1000,
        //     timeAmount: 5,
        //     timePercent: 10
        // },
        stats: {
            hp: 50
        },
        useItem: {
            overrideEffect: 'UseItemMedkit',

            animationProp: 'medkit',
            animationDict: 'majestic_animations_custom',
            animationName: 'healthpack',

            crawlingAnimationProp: 'medkit',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_healthpack',

            usingSound: 'medkit_use',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            seasonPassTriggers: ['task_medkit_item'],

            meMessage: 'chat.events.useMedicalKit',

            duration: 4500
        }
    },
    {
        // Баллончик с краской
        id: 79,
        title: 'items[79].title',
        model: 'prop_paint_spray01a',
        weight: 380,
        width: 1,
        height: 1,
        takeable: true,
        tag: 'items.tags.consumables',
        description: 'items[79].description'
    },
    {
        // Валик
        id: 80,
        title: 'items[80].title',
        model: 'prop_paint_roller',
        weight: 170,
        width: 1,
        height: 1,
        tag: 'Инструмент',
        description: 'items[80].description'
    },
    {
        // Банка с краской S
        id: 81,
        title: 'items[81].title',
        model: 'prop_paints_can01',
        weight: 1000,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[81].description'
    },
    {
        // Банка с краской M
        id: 82,
        title: 'items[82].title',
        model: 'prop_paints_can05',
        weight: 2000,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[82].description'
    },
    {
        // Банка с краской L
        id: 83,
        title: 'items[83].title',
        model: 'prop_paints_can07',
        weight: 3000,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[83].description'
    },
    {
        // Фотоаппарат
        id: 84,
        title: 'items[84].title',
        model: 'prop_pap_camera_01',
        weight: 515,
        width: 1,
        height: 2,
        tag: 'items.tags.facilities',
        description: 'items[84].description'
    },
    {
        // Бутылка Рома
        id: 85,
        title: 'items[85].title',
        model: 'prop_rum_bottle',
        weight: 900,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        description: 'items[85].description',
    },
    {
        // Палатка
        id: 86,
        title: 'items[86].title',
        model: 'prop_skid_tent_01',
        weight: 1200,
        takeable: true,
        width: 3,
        height: 2,
        tag: 'items.tags.personals',
        description: 'items[86].description'
    },
    {
        // Палатка утепленная
        id: 87,
        title: 'items[87].title',
        model: 'prop_skid_tent_02',
        weight: 3600,
        width: 1,
        height: 1,
        tag: 'items.tags.personals',
        description: 'items[87].description'
    },
    {
        // Палатка из ткани
        id: 88,
        title: 'items[88].title',
        model: 'prop_skid_tent_cloth',
        weight: 2500,
        width: 1,
        height: 1,
        tag: 'items.tags.personals',
        description: 'items[88].description'
    },
    {
        // Аптечка Желтая
        id: 89,
        title: 'items[89].title',
        model: 'prop_stat_pack_01',
        weight: 300,
        width: 2,
        height: 1,
        usable: true,
        fastSlot: true,
        stack: 1,
        tag: 'items.tags.medicine',
        description: 'items[89].description',
        stats: {
            hp: 75
        },
        useItem: {
            overrideEffect: 'UseItemMedkit',

            animationProp: 'medkit2',
            animationDict: 'majestic_animations_custom',
            animationName: 'healthpack',

            crawlingAnimationProp: 'medkit2',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_healthpack',

            usingSound: 'medkit_use',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            seasonPassTriggers: ['task_medkit_item'],

            meMessage: 'chat.events.useMedicalKit',

            duration: 3000
        }
    },
    {
        // Карта охранника
        id: 90,
        title: 'items[90].title',
        model: 'prop_cs_credit_card',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[90].description'
    },
    {
        // Текила
        id: 91,
        title: 'items[91].title',
        model: 'prop_tequila_bottle',
        weight: 900,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        description: 'items[91].description',
        stats: {
            water: 15,
        },
        buffs: [
            {
                name: 'Drunk',
                duration: 300 * 1000
            }
        ],
        useItem: {
            animationProp: 'tequila_bottle',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'tequila_bottle',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
            questTriggers: ['drink_alcohol'],
            seasonPassTriggers: ['task_drink_alcohol']
        }
    },
    {
        // Болгарка
        id: 92,
        title: 'items[92].title',
        model: 'prop_tool_consaw',
        weight: 3500,
        materials: 2000,
        materialType: 'blue',
        width: 3,
        height: 2,
        dropOnDeath: true,
        canSeize: true,
        tag: 'items.tags.tools',
        description: 'items[92].description'
    },
    {
        // Дрель-шуруповёрт
        id: 93,
        title: 'items[93].title',
        model: 'prop_tool_drill',
        weight: 1300,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.tools',
        description: 'items[93].description'
    },
    {
        // Топор пожарного
        id: 94,
        title: 'items[94].title',
        model: 'prop_tool_fireaxe',
        weight: 1800,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[94].description'
    },
    {
        // Молоток
        id: 95,
        title: 'items[95].title',
        model: 'w_me_hammer',
        weight: 300,
        width: 1,
        height: 3,
        caliber: true,
        takeable: true,
        tag: 'items.tags.tools',
        hash: 'weapon_hammer',
        isMelee: true,
        isTool: true,
        disableDropOnCapt: true,
        description: 'items[95].description',
        coins: 50
    },
    {
        // Гвоздомет
        id: 96,
        title: 'items[96].title',
        model: 'prop_tool_nailgun',
        weight: 1300,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[96].description',
        coins: 100
    },
    {
        // Кирка
        id: 97,
        title: 'items[97].title',
        model: 'prop_tool_pickaxe',
        weight: 1500,
        limit: 2500,
        width: 2,
        height: 4,
        caliber: true,
        takeable: true,
        isTool: true,
        tag: 'items.tags.tools',
        description: 'items[97].description'
    },
    {
        // Грабли
        id: 98,
        title: 'items[98].title',
        model: 'prop_tool_rake',
        weight: 1500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[98].description'
    },
    {
        // Лопата
        id: 99,
        title: 'items[99].title',
        model: 'prop_tool_shovel2',
        weight: 1500,
        width: 3,
        height: 1,
        takeable: true,
        isTool: true,
        fastSlot: true,
        deathSafe: false,
        tag: 'items.tags.tools',
        description: 'items[99].description'
    },
    {
        // Мотыга
        id: 100,
        title: 'items[100].title',
        model: 'prop_tool_shovel5',
        weight: 700,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[100].description'
    },
    {
        // Кувалда
        id: 101,
        title: 'items[101].title',
        model: 'prop_tool_sledgeham',
        weight: 2000,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[101].description'
    },
    {
        // Газовый ключ
        id: 102,
        title: 'items[102].title',
        model: 'prop_tool_wrench',
        weight: 100,
        width: 1,
        height: 4,
        caliber: true,
        takeable: true,
        disableDropOnCapt: true,
        tag: 'items.tags.tools',
        hash: 'weapon_wrench',
        isMelee: true,
        description: 'items[102].description',
        coins: 50
    },
    {
        // Поисковый фонарь
        id: 103,
        title: 'items[103].title',
        model: 'prop_tool_torch',
        weight: 600,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[103].description'
    },
    {
        // Телефон Nokia
        id: 104,
        title: 'items[104].title',
        model: 'prop_v_m_phone_01',
        weight: 150,
        width: 1,
        height: 2,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[104].description'
    },
    {
        // Водка Absolut
        id: 105,
        title: 'items[105].title',
        model: 'prop_vodka_bottle',
        weight: 550,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        description: 'items[105].description'
    },
    {
        // Термос
        id: 106,
        title: 'items[106].title',
        model: 'sm_prop_smug_flask',
        weight: 1150,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[106].description'
    },
    {
        // Моноблок iMac
        id: 107,
        title: 'items[107].title',
        model: 'sm_prop_smug_monitor_01',
        weight: 9500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[107].description'
    },
    {
        // Macbook
        id: 108,
        title: 'items[108].title',
        model: 'p_laptop_02_s',
        weight: 2000,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[108].description'
    },
    {
        // Дорожная сумка
        id: 109,
        title: 'items[109].title',
        model: 'p_ld_heist_bag_s_pro2_s',
        weight: 1000,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[109].description'
    },
    {
        // Дорожная сумка
        id: 110,
        title: 'items[110].title',
        model: 'p_ld_heist_bag_s_pro_o',
        weight: 1000,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[110].description'
    },
    {
        // Пакетик с *Вещество*
        id: 111,
        title: 'items[111].title',
        model: 'p_meth_bag_01_s',
        weight: 10,
        width: 1,
        height: 1,
        tag: 'items.tags.personals',
        description: 'items[111].description'
    },
    {
        // Наручники
        id: 112,
        title: 'items[112].title',
        model: 'prop_cs_cuffs_01',
        boxItemId: 565,
        materialType: 'blue',
        materials: 2,
        weight: 350,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[112].description',
        buffs: [
            {
                name: 'Cuffed'
            },
        ]
    },
    {
        // Рюкзак
        id: 113,
        title: 'items[113].title',
        model: 'p_michael_backpack_s',
        weight: 750,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[113].description'
    },
    {
        // Папка с документами
        id: 114,
        title: 'items[114].title',
        model: 'prop_cs_documents_01',
        weight: 350,
        width: 2,
        height: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        tag: 'items.tags.documents',
        description: 'items[114].description'
    },
    {
        // Сигара
        id: 115,
        title: 'items[115].title',
        model: 'prop_sh_cigar_01',
        weight: 18,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.products',
        description: 'items[115].description'
    },
    {
        // Полицейское радио
        id: 116,
        title: 'items[116].title',
        model: 'prop_police_radio_main',
        weight: 435,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[116].description'
    },
    {
        // Джоинт
        id: 117,
        title: 'items[117].title',
        model: 'prop_sh_joint_01',
        weight: 1,
        width: 1,
        height: 1,
        tag: 'items.tags.products',
        description: 'items[117].description'
    },
    {
        // Швейная машина
        id: 118,
        title: 'items[118].title',
        model: 'prop_sewing_machine',
        weight: 7500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[118].description'
    },
    {
        // Ткань для шитья
        id: 119,
        title: 'items[119].title',
        model: 'prop_sewing_fabric',
        weight: 1000,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[119].description'
    },
    {
        // Оружейная дробь
        id: 120,
        title: 'items[120].title',
        model: 'prop_sgun_casing',
        weight: 11,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[120].description'
    },
    {
        // Пиво Pißwasser
        id: 121,
        title: 'items[121].title',
        model: 'prop_sh_beer_pissh_01',
        weight: 500,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        description: 'items[121].description',
        stats: {
            hunger: 10,
            water: 20,
            hp: -5,
            poisonChance: 15

        },
        buffs: [
            {
                name: 'Drunk',
                duration: 300 * 1000
            }
        ],
        useItem: {
            animationProp: 'beer_bottle',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'beer_bottle',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
            questTriggers: ['drink_alcohol'],
            seasonPassTriggers: ['task_drink_alcohol'],
        }
    },
    {
        // Запасное колесо
        id: 122,
        title: 'items[122].title',
        model: 'prop_stockade_wheel',
        weight: 8000,
        width: 1,
        height: 1,
        tag: 'items.tags.autoParts',
        description: 'items[122].description'
    },
    {
        // Спущенное колесо
        id: 123,
        title: 'items[123].title',
        model: 'prop_stockade_wheel_flat',
        weight: 8000,
        width: 1,
        height: 1,
        tag: 'items.tags.autoParts',
        description: 'items[123].description'
    },
    {
        // Противовирусная вакцина
        id: 124,
        title: 'items[124].title',
        model: 'prop_syringe_01',
        boxItemId: 502,
        materialType: 'red',
        materials: 1,
        weight: 4,
        width: 1,
        height: 2,
        usable: true,
        tag: 'items.tags.medicine',
        description: 'items[124].description',
        buffs: [
            {
                name: 'HealCovid'
            }
        ]
    },
    {
        // Gruppe Sechs
        id: 125,
        title: 'items[125].title',
        model: 'prop_casey_sec_id',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        description: 'items[125].description'
    },
    {
        // Баночка с Марихуаной
        id: 126,
        title: 'items[126].title',
        model: 'prop_weed_bottle',
        weight: 10,
        width: 1,
        height: 1,
        tag: 'items.tags.drugs',
        description: 'items[126].description'
    },
    {
        // Сварочная маска
        id: 127,
        title: 'items[127].title',
        model: 'prop_welding_mask_01',
        weight: 1500,
        width: 1,
        height: 1,
        tag: 'items.tags.personals',
        description: 'items[127].description'
    },
    {
        // Сварочный аппарат
        id: 128,
        title: 'items[128].title',
        model: 'prop_weld_torch',
        weight: 5500,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.facilities',
        description: 'items[128].description'
    },
    {
        // Разбитая бутылка
        id: 129,
        title: 'items[129].title',
        model: 'prop_w_me_bottle',
        weight: 250,
        width: 1,
        height: 2,
        caliber: true,
        takeable: true,
        tag: 'items.tags.personals',
        hash: 'weapon_bottle',
        isMelee: true,
        isTool: true,
        disableDropOnCapt: true,
        description: 'items[129].description',
        coins: 50
    },
    {
        // Кинжал
        id: 130,
        title: 'items[130].title',
        model: 'w_me_dagger',
        weight: 600,
        width: 1,
        height: 3,
        caliber: true,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        isTool: true,
        disableDropOnCapt: true,
        hash: 'weapon_dagger',
        isMelee: true,
        description: 'items[130].description',
        coins: 100
    },
    {
        // Топорик
        id: 131,
        title: 'items[131].title',
        model: 'prop_w_me_hatchet',
        weight: 1200,
        width: 2,
        height: 4,
        caliber: true,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        isTool: true,
        limit: 2000,
        hash: 'weapon_hatchet',
        isMelee: true,
        disableDropOnCapt: true,
        description: 'items[131].description',
        coins: 100
    },
    {
        // Складной нож
        id: 132,
        title: 'items[132].title',
        model: 'w_me_switchblade',
        weight: 170,
        width: 1,
        height: 2,
        caliber: true,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        isTool: true,
        disableDropOnCapt: true,
        hash: 'weapon_switchblade',
        isMelee: true,
        description: 'items[132].description',
        coins: 100
    },
    {
        // Боевой топор
        id: 133,
        title: 'items[133].title',
        model: 'w_me_battleaxe',
        weight: 1500,
        width: 2,
        height: 4,
        caliber: true,
        takeable: true,
        isTool: true,
        materials: 5,
        disableDropOnCapt: true,
        hash: 'weapon_battleaxe',
        isMelee: true,
        tag: 'items.tags.ammunition',
        description: 'items[133].description',
        coins: 100
    },
    {
        // Самокруточная бумага
        id: 134,
        title: 'items[134].title',
        model: 'p_cs_papers_01',
        weight: 15,
        width: 1,
        height: 1,
        tag: 'items.tags.products',
        description: 'items[134].description'
    },
    {
        // Микрофон
        id: 135,
        title: 'items[135].title',
        model: 'p_ing_microphonel_01',
        weight: 100,
        width: 1,
        height: 1,
        isTool: true,
        tag: 'items.tags.facilities',
        takeable: true,
        description: 'items[135].description'
    },
    {
        // Гамбургер
        id: 136,
        title: 'items[136].title',
        model: 'prop_cs_burger_01',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        barPrice: 34000,
        description: 'items[136].description',
        stats: {
            hunger: 45,
            water: -15,
            poisonChance: 15
        },
        useItem: {
            animationProp: 'burger',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'burger',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000,
            questTriggers: ['eat_food']
        }
    },
    {
        // Удостоверение Humane
        id: 137,
        title: 'items[137].title',
        model: 'p_ld_id_card_002',
        weight: 50,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        description: 'items[137].description'
    },
    {
        // Удостоверение Sunset
        id: 138,
        title: 'items[138].title',
        model: 'p_ld_id_card_01',
        weight: 50,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        description: 'items[138].description'
    },
    {
        // Гитара
        id: 139,
        title: 'items[139].title',
        model: 'prop_acc_guitar_01',
        weight: 5000,
        width: 2,
        height: 4,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[139].description'
    },
    {
        // Дорожный конус
        id: 140,
        title: 'items[140].title',
        model: 'prop_cone_float_1',
        weight: 400,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[140].description'
    },
    {
        // Диаммонийфосфат
        id: 141,
        title: 'items[141].title',
        model: 'prop_cs_fertilizer',
        weight: 1000,
        width: 1,
        height: 1,
        tag: 'items.tags.ingredients',
        description: 'items[141].description'
    },
    {
        // Кресло инвалида
        id: 142,
        title: 'items[142].title',
        model: 'prop_wheelchair_01',
        weight: 19000,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[142].description'
    },
    {
        // Сумка с дрелью
        id: 143,
        title: 'items[143].title',
        model: 'p_ld_heist_bag_s',
        weight: 2500,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[143].description'
    },
    {
        // Буровая установка
        id: 144,
        title: 'items[144].title',
        model: 'p_oil_pjack_01_amo',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[144].description'
    },
    {
        // Буровая установка
        id: 145,
        title: 'items[145].title',
        model: 'p_oil_pjack_03_amo',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[145].description'
    },
    {
        // Полицейские шипы
        id: 146,
        title: 'items[146].title',
        model: 'p_ld_stinger_s',
        weight: 23000,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[146].description'
    },
    {
        // Чемодан с деньгами
        id: 147,
        title: 'items[147].title',
        model: 'prop_cash_case_02',
        weight: 30000,
        width: 2,
        height: 2,
        dropOnDeath: true,
        dropOnQuit: true,
        tag: 'items.tags.misc',
        description: 'items[147].description'
    },
    {
        // Конверт с деньгами
        id: 148,
        title: 'items[148].title',
        model: 'prop_cash_envelope_01',
        weight: 50,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[148].description'
    },
    {
        // Полицейский щит
        id: 149,
        title: 'items[149].title',
        model: 'prop_riot_shield',
        weight: 29500,
        width: 1,
        height: 1,
        tag: 'items.tags.personals',
        description: 'items[149].description'
    },
    {
        // Банка с *вещество*
        id: 150,
        title: 'items[150].title',
        model: 'v_ind_cs_chemcan',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.ingredients',
        description: 'items[150].description'
    },
    {
        // Газовый балон
        id: 151,
        title: 'items[151].title',
        model: 'v_ind_cs_gascanister',
        weight: 6000,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[151].description'
    },
    {
        // Канистра с *вещество*
        id: 152,
        title: 'items[152].title',
        model: 'v_ind_cs_jerrycan01',
        weight: 3900,
        width: 1,
        height: 1,
        tag: 'items.tags.autoParts',
        description: 'items[152].description'
    },
    {
        // Канистра с дизелем
        id: 153,
        title: 'items[153].title',
        model: 'v_ind_cs_jerrycan03',
        weight: 3900,
        width: 1,
        height: 1,
        tag: 'items.tags.autoParts',
        description: 'items[153].description'
    },
    {
        // Электропила
        id: 154,
        title: 'items[154].title',
        model: 'v_ind_cs_powersaw',
        weight: 4100,
        width: 1,
        height: 1,
        tag: 'Инструмент',
        description: 'items[154].description'
    },
    {
        // Шило
        id: 155,
        title: 'items[155].title',
        model: 'v_ind_cs_screwdrivr3',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'Инструмент',
        description: 'items[155].description'
    },
    {
        // Перекись водорода L
        id: 156,
        title: 'items[156].title',
        model: 'v_ind_meatwash',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.ingredients',
        description: 'items[156].description'
    },
    {
        // Кросовки
        id: 157,
        title: 'items[157].title',
        model: 'v_res_fa_trainer01r',
        weight: 600,
        width: 1,
        height: 1,
        tag: 'items.tags.personals',
        description: 'items[157].description'
    },
    {
        // Зеленая старая книга
        id: 158,
        title: 'items[158].title',
        model: 'v_ret_ta_book2',
        weight: 50,
        width: 1,
        height: 1,
        tag: 'items.tags.books',
        stack: 20,
        description: 'items[158].description'
    },
    {
        // Книга
        id: 159,
        title: 'items[159].title',
        model: 'v_res_investbook08',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.books',
        takeable: true,
        description: 'items[159].description'
    },
    {
        // Банка пива Blarney
        id: 160,
        title: 'items[160].title',
        model: 'v_res_tt_can01',
        weight: 750,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        description: 'items[160].description'
    },
    {
        // Банка пива Logger
        id: 161,
        title: 'items[161].title',
        model: 'v_res_tt_can02',
        weight: 750,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        description: 'items[161].description'
    },
    {
        // Банка Sprunk
        id: 162,
        title: 'items[162].title',
        model: 'v_res_tt_can01',
        weight: 350,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.products',
        description: 'items[162].description'
    },
    {
        // Банка пива Blarney
        id: 163,
        title: 'items[163].title',
        model: 'v_res_tt_cancrsh01',
        weight: 750,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.rubbish',
        description: 'items[163].description'
    },
    {
        // Банка пива Logger
        id: 164,
        title: 'items[164].title',
        model: 'v_res_tt_cancrsh02',
        weight: 750,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.rubbish',
        description: 'items[164].description'
    },
    {
        // Молоко
        id: 165,
        title: 'items[165].title',
        model: 'prop_cs_milk_01',
        weight: 500,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[165].description',
        buffs: [
            {
                name: 'DispellDebuffs'
            }
        ],
        stats: {
            hunger: 3.5,
            water: 20,
            poisonChance: 5,
        },
        useItem: {
            animationProp: 'milk',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'milk',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000
        }
    },
    {
        // Овсяные хлопья
        id: 166,
        title: 'items[166].title',
        model: 'v_res_tt_cereal01',
        weight: 500,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[166].description'
    },
    {
        // Овсяные хлопья
        id: 167,
        title: 'items[167].title',
        model: 'v_res_tt_cereal02',
        weight: 500,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[167].description'
    },
    {
        // Азитромицин
        id: 168,
        title: 'items[168].title',
        model: 'mj_az_tabl',
        boxItemId: 505,
        materialType: 'red',
        materials: 10,
        weight: 5,
        width: 1,
        height: 1,
        canSeize: true,
        stack: 24,
        tag: 'items.tags.medicine',
        description: 'items[168].description',
        buffs: [
            {
                name: 'HealCold'
            }
        ]
    },
    {
        // Активированный уголь
        id: 169,
        title: 'items[169].title',
        model: 'mj_acchar_tabl',
        boxItemId: 507,
        materialType: 'red',
        materials: 10,
        weight: 5,
        width: 1,
        height: 1,
        canSeize: true,
        stack: 24,
        tag: 'items.tags.medicine',
        description: 'items[169].description',
        buffs: [
            {
                name: 'HealPoisoning'
            }
        ]
    },
    {
        // Кодеиновые таблетки
        id: 170,
        title: 'items[170].title',
        model: 'bkr_prop_coke_painkiller_01a',
        boxItemId: 503,
        materialType: 'red',
        materials: 1,
        weight: 5,
        width: 2,
        height: 1,
        canSeize: true,
        stack: 24,
        tag: 'items.tags.medicine',
        description: 'items[170].description',
        stats: {
            hp: 100
        },
    },
    {
        // .50 BMG
        id: 171,
        title: 'items[171].title',
        model: 'tor_ammo_08_50BMG',
        weight: 50,
        width: 2,
        height: 1,
        tag: 'items.tags.ammunition',
        canSeize: true,
        clip: 48,
        description: 'items[171].description',
        stack: 48
    },
    {
        // 5.56x45mm
        id: 172,
        title: 'items[172].title',
        model: 'tor_ammo_03_5_56x45mm',
        boxItemId: 482,
        materialType: 'green',
        materials: 6,
        weight: 12,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        canSeize: true,
        clip: 240,
        description: 'items[172].description',
        stack: 240
    },
    {
        // Кассовый аппарат
        id: 173,
        title: 'items[173].title',
        model: 'v_ret_gc_cashreg',
        weight: 4500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[173].description'
    },
    {
        // Паста для волос
        id: 174,
        title: 'items[174].title',
        model: 'v_ret_hd_prod1_',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[174].description'
    },
    {
        // Шампунь Жумайсынба
        id: 175,
        title: 'items[175].title',
        model: 'prop_toilet_shamp_01',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[175].description'
    },
    {
        // Лабораторные весы
        id: 176,
        title: 'items[176].title',
        model: 'v_ret_ml_scale',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[176].description'
    },
    {
        // Лейка
        id: 177,
        title: 'items[177].title',
        model: 'prop_wateringcan',
        weight: 500,
        width: 2,
        height: 2,
        noStack: true,
        takeable: true,
        fastSlot: true,
        isTool: true,
        tag: 'items.tags.tools',
        description: 'items[177].description'
    },
    {
        // Стол изготовл. нарк.
        id: 178,
        title: 'items[178].title',
        model: 'v_ret_ml_tableb',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[178].description'
    },
    {
        // Стол изготовл. нарк.
        id: 179,
        title: 'items[179].title',
        model: 'v_ret_ml_tablec',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[179].description'
    },
    {
        // Сумка для ноутбука
        id: 180,
        title: 'items[180].title',
        model: 'v_ret_ps_bag_01',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[180].description'
    },
    {
        // Большая аптечка
        id: 181,
        title: 'items[181].title',
        model: 'v_ret_ta_firstaid',
        weight: 500,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.medicine',
        description: 'items[181].description',
        stats: {
            hp: 100
        },
        useItem: {
            overrideEffect: 'UseItemMedkit',

            animationProp: 'medkit',
            animationDict: 'majestic_animations_custom',
            animationName: 'healthpack',

            crawlingAnimationProp: 'medkit',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_healthpack',

            usingSound: 'medkit_use',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            seasonPassTriggers: ['task_medkit_item'],

            meMessage: 'chat.events.useMedicalKit',

            duration: 4500
        }
    },
    {
        // Медицинские перчатки
        id: 182,
        title: 'items[182].title',
        model: 'v_ret_ta_gloves',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[182].description'
    },
    {
        // Оружейный верстак
        id: 183,
        title: 'items[183].title',
        model: 'gr_prop_gr_bench_01a',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[183].description'
    },
    {
        // Оружейный верстак
        id: 184,
        title: 'items[184].title',
        model: 'gr_prop_gr_bench_01b',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[184].description'
    },
    {
        // Оружейный верстак
        id: 185,
        title: 'items[185].title',
        model: 'gr_prop_gr_bench_02a',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[185].description'
    },
    {
        // Оружейный верстак
        id: 186,
        title: 'items[186].title',
        model: 'gr_prop_gr_bench_02b',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[186].description'
    },
    {
        // Рабочий стол
        id: 187,
        title: 'items[187].title',
        model: 'gr_prop_gr_bench_03a',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[187].description'
    },
    {
        // Рабочий стол
        id: 188,
        title: 'items[188].title',
        model: 'gr_prop_gr_bench_03b',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[188].description'
    },
    {
        // Малый рабочий стол
        id: 189,
        title: 'items[189].title',
        model: 'gr_prop_gr_bench_04a',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[189].description'
    },
    {
        // Малый рабочий стол
        id: 190,
        title: 'items[190].title',
        model: 'gr_prop_gr_bench_04b',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[190].description'
    },
    {
        // Сверлильный станок
        id: 191,
        title: 'items[191].title',
        model: 'gr_prop_gr_speeddrill_01a',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[191].description'
    },
    {
        // Сверлильный станок
        id: 192,
        title: 'items[192].title',
        model: 'gr_prop_gr_speeddrill_01b',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[192].description'
    },
    {
        // Ящик инструментов XS
        id: 193,
        title: 'items[193].title',
        model: 'gr_prop_gr_tool_chest_01a',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[193].description'
    },
    {
        // Ящик инструментов S
        id: 194,
        title: 'items[194].title',
        model: 'gr_prop_gr_tool_draw_01a',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[194].description'
    },
    {
        // Ящик инструментов M
        id: 195,
        title: 'items[195].title',
        model: 'gr_prop_gr_tool_draw_01b',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[195].description'
    },
    {
        // Ящик инструментов L
        id: 196,
        title: 'items[196].title',
        model: 'gr_prop_gr_tool_draw_01d',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[196].description'
    },
    {
        // Тиски
        id: 197,
        title: 'items[197].title',
        model: 'gr_prop_gr_vice_01a',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[197].description'
    },
    {
        // Флешка
        id: 198,
        title: 'items[198].title',
        model: 'hei_prop_hst_usb_drive',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[198].description'
    },
    {
        // Синий банкомат
        id: 199,
        title: 'items[199].title',
        model: 'prop_atm_02',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[199].description'
    },
    {
        // Красный банкомат
        id: 200,
        title: 'items[200].title',
        model: 'prop_atm_03',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[200].description'
    },
    {
        // Килька
        id: 201,
        title: 'items[201].title',
        model: null,
        weight: 1,
        width: 1,
        height: 1,
        tag: 'items.tags.fish',
        // chance: 70,
        minWeight: 20,
        maxWeight: 100,
        description: 'items[201].description',
        stack: 10000
    },
    {
        // Кастет
        id: 202,
        title: 'items[202].title',
        model: 'w_me_knuckle',
        weight: 400,
        width: 1,
        height: 1,
        caliber: true,
        takeable: true,
        materials: 10,
        hash: 'weapon_knuckle',
        isMelee: true,
        disableDropOnCapt: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[202].description',
        coins: 150
    },
    {
        // Филе мяса
        id: 203,
        title: 'items[203].title',
        model: 'p_brain_chunk_s',
        weight: 500,
        width: 1,
        height: 2,
        tag: 'items.tags.products',
        description: 'items[203].description'
    },
    {
        // Автомобильный трос
        id: 204,
        title: 'items[204].title',
        model: 'p_csh_strap_01_s',
        weight: 500,
        width: 2,
        height: 1,
        tag: 'items.tags.autoParts',
        description: 'items[204].description'
    },
    {
        // Шлем для мотоцикла
        id: 205,
        title: 'items[205].title',
        model: 'ba_prop_battle_sports_helmet',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.autoParts',
        description: 'items[205].description'
    },
    {
        // Банка eCola
        id: 206,
        title: 'items[206].title',
        model: 'prop_ecola_can',
        weight: 330,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[206].description',
        barPrice: 40000,
        stats: {
            water: 40,
        },
        useItem: {
            animationProp: 'ecola',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'ecola',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,

            questTriggers: ['eat_food']
        },
    },
    {
        // Баскетбольный мяч
        id: 207,
        title: 'items[207].title',
        model: 'prop_bskball_01',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[207].description'
    },
    {
        // Бейсбольная бита
        id: 208,
        title: 'items[208].title',
        model: 'w_me_bat',
        weight: 1400,
        width: 1,
        height: 4,
        caliber: true,
        takeable: true,
        hash: 'weapon_bat',
        isMelee: true,
        isTool: true,
        disableDropOnCapt: true,
        tag: 'items.tags.misc',
        description: 'items[208].description',
        coins: 100
    },
    {
        // Коктейль молотова
        id: 209,
        title: 'items[209].title',
        model: 'w_ex_molotov',
        weight: 750,
        width: 1,
        height: 2,
        caliber: true,
        takeable: true,
        hash: 'weapon_molotov',
        isMelee: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[209].description'
    },
    {
        // Пакет с таблетками
        id: 210,
        title: 'items[210].title',
        model: 'hei_prop_pill_bag_01',
        weight: 500,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.medicine',
        description: 'items[210].description'
    },
    {
        // Шоколад Meteorite
        id: 211,
        title: 'items[211].title',
        model: 'prop_choc_meto',
        weight: 50,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[211].description',
        stats: {
            hunger: 40,
            water: -10,
            hp: +5
        },
        buffs: [
            {
                name: 'ResetStamina'
            }
        ],
        useItem: {
            animationProp: 'choc_meto',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'choc_meto',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Шоколад EgoChaser
        id: 212,
        title: 'items[212].title',
        model: 'prop_choc_ego',
        weight: 50,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[212].description',
        stats: {
            hunger: 30,
            water: -10,
            hp: +5
        },
        useItem: {
            animationProp: 'choc_ego',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'choc_ego',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Банковская карта
        id: 213,
        title: 'items[213].title',
        model: 'prop_cs_credit_card',
        weight: 4,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[213].description'
    },
    {
        // Ps And Qs
        id: 214,
        title: 'items[214].title',
        model: 'prop_candy_pqs',
        weight: 500,
        width: 2,
        height: 2,
        tag: 'items.tags.products',
        description: 'items[214].description'
    },
    {
        // Удостоверение Sheriff Department
        id: 215,
        title: 'items[215].title',
        model: 'prop_fib_badge',
        weight: 4,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        usable: true,
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[215].description'
    },
    {
        // Полицейская дубинка
        id: 216,
        title: 'items[216].title',
        model: 'w_me_nightstick',
        boxItemId: 566,
        materialType: 'blue',
        materials: 5,
        weight: 825,
        hash: 'weapon_nightstick',
        isMelee: true,
        width: 1,
        height: 3,
        takeable: true,
        caliber: true,
        isTool: true,
        disableDropOnCapt: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[216].description'
    },
    {
        // Фонарик
        id: 217,
        title: 'items[217].title',
        model: 'p_cs_police_torch_s',
        weight: 350,
        hash: 'weapon_flashlight',
        isMelee: true,
        width: 2,
        height: 1,
        takeable: true,
        isTool: true,
        materials: 5,
        tag: 'items.tags.tools',
        canSeize: true,
        disableDropOnCapt: true,
        caliber: true,
        description: 'items[217].description'
    },
    {
        // Воск для волос
        id: 218,
        title: 'items[218].title',
        model: 'v_ret_hd_prod3_',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[218].description'
    },
    {
        // Гель
        id: 219,
        title: 'items[219].title',
        model: 'v_ret_hd_prod4_',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[219].description'
    },
    {
        // Крем для лица
        id: 220,
        title: 'items[220].title',
        model: 'v_ret_hd_prod5_',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[220].description'
    },
    {
        // Поднос с наркотиком
        id: 221,
        title: 'items[221].title',
        model: 'v_ret_ml_meth',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[221].description'
    },
    {
        // Форель
        id: 222,
        title: 'items[222].title',
        model: 'ribka_tor1',
        weight: 1,
        width: 2,
        height: 1,
        // boxItemId: 714,
        tag: 'items.tags.fish',
        // chance: 50,
        minWeight: 300,
        maxWeight: 500,
        description: 'items[222].description',
        stack: 10000,
        canSeize: true
    },
    {
        // Сардина
        id: 223,
        title: 'items[223].title',
        model: null,
        weight: 1,
        width: 1,
        height: 1,
        tag: 'items.tags.fish',
        // chance: 80,
        minWeight: 1200,
        maxWeight: 4200,
        description: 'items[223].description',
        stack: 10000,
        canSeize: true
    },
    {
        // Камбала
        id: 224,
        title: 'items[224].title',
        model: 'ribka_tor3',
        weight: 1,
        width: 2,
        height: 1,
        // boxItemId: 715,
        tag: 'items.tags.fish',
        // chance: 50,
        minWeight: 375,
        maxWeight: 580,
        description: 'items[224].description',
        stack: 10000,
        canSeize: true
    },
    {
        // Тунец
        id: 225,
        title: 'items[225].title',
        model: 'ribka_tor2',
        weight: 1,
        width: 2,
        height: 1,
        // boxItemId: 716,
        tag: 'items.tags.fish',
        // chance: 10,
        minWeight: 520,
        maxWeight: 630,
        description: 'items[225].description',
        stack: 10000,
        canSeize: true
    },
    {
        // Магнитофон
        id: 226,
        title: 'items[226].title',
        model: 'prop_boombox_01',
        weight: 3000,
        width: 1,
        height: 1,
        takeable: true,
        tag: 'items.tags.misc',
        description: 'items[226].description'
    },
    {
        // Банка кукурузы
        id: 227,
        title: 'items[227].title',
        model: 'v_ret_247_swtcorn2',
        weight: 340,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[227].description',
        stats: {
            hunger: 25,
            water: 10,
            poisonChance: 15
        },
        useItem: {
            animationProp: 'sweet_corn',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'sweet_corn',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000
        }
    },
    {
        // Сумка санитара
        id: 228,
        title: 'items[228].title',
        model: 'xm_prop_x17_bag_med_01a',
        weight: 1200,
        width: 3,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.medicine',
        description: 'items[228].description'
    },
    {
        // Медицинский контейнер
        id: 229,
        title: 'items[229].title',
        model: 'sm_prop_smug_crate_s_medical',
        weight: 1200,
        width: 3,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.medicine',
        description: 'items[229].description'
    },
    {
        // Ноутбук для взлома
        id: 230,
        title: 'items[230].title',
        model: 'xm_prop_x17_laptop_lester_01',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.facilities',
        description: 'items[230].description'
    },
    {
        // Ретранслятор
        id: 231,
        title: 'items[231].title',
        model: 'vw_prop_vw_ex_pe_01a',
        weight: 500,
        width: 1,
        height: 2,
        tag: 'items.tags.facilities',
        description: 'items[231].description'
    },
    {
        // Кейс с винтовкой
        id: 232,
        title: 'items[232].title',
        model: 'p_gcase_s',
        weight: 500,
        width: 5,
        height: 3,
        tag: 'items.tags.misc',
        description: 'items[232].description'
    },
    {
        // Кабельная стяжка
        id: 233,
        title: 'items[233].title',
        model: 'hei_prop_zip_tie_positioned',
        weight: 1,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[233].description',
        buffs: [
            {
                name: 'Tied'
            }
        ]
    },
    {
        // Pacific Standart
        id: 234,
        title: 'items[234].title',
        model: 'hei_prop_hei_id_bank',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        description: 'items[234].description'
    },
    {
        // Ударная дрель
        id: 235,
        title: 'items[235].title',
        model: 'hei_prop_heist_drill',
        weight: 500,
        width: 4,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[235].description'
    },
    {
        // Оружейный магазин R
        id: 236,
        title: 'items[236].title',
        model: 'w_ar_assaultrifle_mag1',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[236].description',
        modificationSlot: 3
    },
    {
        // Пустая пачка сигарет
        id: 237,
        title: 'items[237].title',
        model: 'p_fag_packet_01_s',
        weight: 2,
        width: 1,
        height: 1,
        tag: 'items.tags.products',
        description: 'items[237].description'
    },
    {
        // Гвозди
        id: 238,
        title: 'items[238].title',
        model: 'prop_cs_nail_file',
        weight: 50,
        width: 1,
        height: 1,
        tag: 'items.tags.consumables',
        description: 'items[238].description'
    },
    {
        // iPhone 11
        id: 239,
        title: 'items[239].title',
        model: 'p_cs_cam_phone',
        weight: 180,
        width: 1,
        height: 2,
        takeable: true,
        canDrag: false,
        tag: 'items.tags.facilities',
        description: 'items[239].description'
    },
    {
        // Бинт медицинский
        id: 240,
        title: 'items[240].title',
        model: 'bandage_shell',
        weight: 20,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.medicine',
        shopCount: 20,
        stack: 20,
        description: 'items[240].description',
        stats: {
            hp: 25
        },
        useItem: {
            overrideEffect: 'UseItemMedkit',

            animationProp: 'bandage',
            animationDict: 'oddjobs@bailbond_hobotwitchy',
            animationName: 'base',

            crawlingAnimationProp: 'bandage',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_bandage',

            usingSound: 'pills',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            seasonPassTriggers: ['task_medkit_item'],

            meMessage: 'chat.events.useBandage',

            duration: 2000
        }
    },
    {
        // Штурмовая винтовка
        id: 241,
        title: 'items[241].title',
        model: 'w_ar_assaultrifle',
        boxItemId: 559,
        materialType: 'green',
        materials: 15,
        weight: 2500,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 39,
        hash: 'weapon_assaultrifle',
        isMelee: false,
        limit: 7500,
        description: 'items[241].description'
    },
    {
        // Бандана
        id: 242,
        title: 'items[242].title',
        model: 'prop_cs_bandana',
        weight: 20,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.accessories',
        description: 'items[242].description'
    },
    {
        // Штурмовой пистолет
        id: 243,
        title: 'items[243].title',
        model: 'w_pi_pistol50',
        boxItemId: 498,
        materialType: 'green',
        materials: 15,
        weight: 1000,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 37,
        hash: 'weapon_pistol50',
        isMelee: false,
        limit: 750,
        description: 'items[243].description'
    },
    {
        // Тазер
        id: 244,
        title: 'items[244].title',
        model: 'w_pi_stungun',
        boxItemId: 567,
        materialType: 'blue',
        materials: 5,
        weight: 220,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        hash: 'weapon_stungun_mp',
        isMelee: false,
        limit: 50,
        caliber: true,
        description: 'items[244].description',
    },
    {
        // Короткий пистолет
        id: 245,
        title: 'items[245].title',
        model: 'w_pi_sns_pistolmk2',
        weight: 500,
        width: 1,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 37,
        hash: 'weapon_snspistol_mk2',
        isMelee: false,
        limit: 750,
        description: 'items[245].description'
    },
    {
        // Охотничий нож
        id: 246,
        title: 'items[246].title',
        model: 'w_me_knife_01',
        weight: 180,
        width: 1,
        height: 2,
        caliber: true,
        takeable: true,
        materials: 5,
        isTool: true,
        tag: 'items.tags.ammunition',
        hash: 'weapon_knife',
        isMelee: true,
        disableDropOnCapt: true,
        description: 'items[246].description'
    },
    {
        // Короткий дробовик
        id: 247,
        title: 'items[247].title',
        model: 'w_sg_sawnoff',
        weight: 2000,
        width: 3,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 38,
        hash: 'weapon_sawnoffshotgun',
        isMelee: false,
        limit: 400,
        materialType: 'green',
        materials: 10,
        boxItemId: 827,
        description: 'items[247].description'
    },
    {
        // Улучшенная винтовка
        id: 248,
        title: 'items[248].title',
        model: 'w_ar_advancedrifle',
        boxItemId: 486,
        materialType: 'green',
        materials: 25,
        weight: 3200,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 172,
        hash: 'weapon_advancedrifle',
        isMelee: false,
        limit: 7500,
        description: 'items[248].description'
    },
    {
        // Пистолет Mk2
        id: 249,
        title: 'items[249].title',
        model: 'w_pi_pistolmk2',
        weight: 600,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 37,
        hash: 'weapon_pistol_mk2',
        isMelee: false,
        limit: 750,
        description: 'items[249].description'
    },
    {
        // Помповый дробовик
        id: 250,
        title: 'items[250].title',
        model: 'w_sg_pumpshotgun',
        weight: 3500,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 38,
        limit: 400,
        hash: 'weapon_pumpshotgun',
        isMelee: false,
        description: 'items[250].description'
    },
    {
        // Пустой мешок
        id: 251,
        title: 'items[251].title',
        model: 'prop_ld_rub_binbag_01',
        weight: 80,
        width: 2,
        height: 2,
        tag: 'items.tags.rubbish',
        description: 'items[251].description'
    },
    {
        // Эпинефрин
        id: 252,
        title: 'items[252].title',
        model: 'adrenalin_shell',
        boxItemId: 504,
        materialType: 'red',
        materials: 10,
        weight: 150,
        width: 1,
        height: 2,
        canSeize: true,
        hasSerial: true,
        tag: 'items.tags.medicine',
        description: 'items[252].description'
    },
    {
        // 12ga Rifled
        id: 253,
        title: 'items[253].title',
        model: 'tor_ammo_02_12gaRifled',
        boxItemId: 478,
        materialType: 'green',
        materials: 4,
        weight: 35,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        canSeize: true,
        clip: 48,
        description: 'items[253].description',
        stack: 48
    },
    {
        // .45ACP
        id: 254,
        title: 'items[254].title',
        model: 'tor_ammo_05_045ACP',
        boxItemId: 479,
        materialType: 'green',
        materials: 5,
        weight: 9,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        canSeize: true,
        clip: 240,
        description: 'items[254].description',
        stack: 240
    },
    {
        // Бронебойный пистолет
        id: 255,
        title: 'items[255].title',
        model: 'w_pi_appistol',
        weight: 800,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 37,
        hash: 'weapon_appistol',
        isMelee: false,
        limit: 3000,
        description: 'items[255].description',
        coins: 150
    },
    {
        // Помповый дробовик Mk2
        id: 256,
        title: 'items[256].title',
        model: 'w_sg_pumpshotgunmk2',
        boxItemId: 484,
        materialType: 'green',
        materials: 20,
        weight: 3600,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 38,
        hash: 'weapon_pumpshotgun_mk2',
        isMelee: false,
        limit: 400,
        description: 'items[256].description'
    },
    {
        // Штурмовой дробовик
        id: 257,
        title: 'items[257].title',
        model: 'w_sg_assaultshotgun',
        weight: 3100,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 38,
        hash: 'weapon_assaultshotgun',
        isMelee: false,
        limit: 1000,
        materials: 100,
        description: 'items[257].description'
    },
    {
        // Двуствольный дробовик
        id: 258,
        title: 'items[258].title',
        model: 'w_sg_doublebarrel',
        weight: 1800,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 38,
        hash: 'weapon_dbshotgun',
        isMelee: false,
        limit: 200,
        materials: 120,
        description: 'items[258].description'
    },
    {
        // Тяжелый дробовик
        id: 259,
        title: 'items[259].title',
        model: 'w_sg_heavyshotgun',
        boxItemId: 493,
        materialType: 'green',
        materials: 40,
        weight: 3340,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 253,
        hash: 'weapon_heavyshotgun',
        isMelee: false,
        limit: 1000,
        description: 'items[259].description'
    },
    {
        // Тяжелый пистолет
        id: 260,
        title: 'items[260].title',
        model: 'w_pi_heavypistol',
        materialType: 'green',
        materials: 15,
        weight: 800,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 37,
        hash: 'weapon_heavypistol',
        isMelee: false,
        limit: 750,
        boxItemId: 825,
        description: 'items[260].description'
    },
    {
        // Винтажный пистолет
        id: 261,
        title: 'items[261].title',
        model: 'w_pi_vintage_pistol',
        weight: 500,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 37,
        hash: 'weapon_vintagepistol',
        isMelee: false,
        limit: 750,
        description: 'items[261].description'
    },
    {
        // Пистолет Marksman
        id: 262,
        title: 'items[262].title',
        model: 'w_pi_singleshot',
        weight: 2000,
        width: 3,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 282,
        hash: 'weapon_marksmanpistol',
        isMelee: false,
        limit: 1000,
        description: 'items[262].description',
        coins: 100
    },
    {
        // Револьвер
        id: 263,
        title: 'items[263].title',
        model: 'w_pi_revolver',
        boxItemId: 560,
        materialType: 'green',
        materials: 30,
        weight: 2000,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 282,
        hash: 'weapon_revolver',
        isMelee: false,
        limit: 750,
        description: 'items[263].description'
    },
    {
        // Револьвер Mk2
        id: 264,
        title: 'items[264].title',
        model: 'w_pi_revolvermk2',
        weight: 2500,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 282,
        hash: 'weapon_revolver_mk2',
        isMelee: false,
        limit: 2500,
        description: 'items[264].description',
        coins: 150
    },
    {
        // Старый револьвер
        id: 265,
        title: 'items[265].title',
        model: 'w_pi_wep1_gun',
        weight: 1500,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 254,
        materials: 25,
        hash: 'weapon_doubleaction',
        isMelee: false,
        limit: 2500,
        description: 'items[265].description'
    },
    {
        // SMG
        id: 266,
        title: 'items[266].title',
        model: 'w_sb_smg',
        weight: 2500,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 254,
        materials: 20,
        hash: 'weapon_smg',
        isMelee: false,
        limit: 15000,
        description: 'items[266].description'
    },
    {
        // SMG Mk2
        id: 267,
        title: 'items[267].title',
        model: 'w_sb_smgmk2',
        boxItemId: 499,
        materialType: 'green',
        materials: 20,
        weight: 1600,
        width: 3,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 254,
        hash: 'weapon_smg_mk2',
        isMelee: false,
        limit: 1600,
        description: 'items[267].description'
    },
    {
        // Боевой PDW
        id: 268,
        title: 'items[268].title',
        model: 'w_sb_pdw',
        boxItemId: 497,
        materialType: 'green',
        materials: 40,
        weight: 2000,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 254,
        hash: 'weapon_combatpdw',
        isMelee: false,
        limit: 3000,
        description: 'items[268].description'
    },
    {
        // Малый ПП
        id: 269,
        title: 'items[269].title',
        model: 'w_sb_compactsmg',
        weight: 1000,
        width: 3,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 37,
        hash: 'weapon_machinepistol',
        isMelee: false,
        limit: 3000,
        description: 'items[269].description'
    },
    {
        // Мини SMG
        id: 270,
        title: 'items[270].title',
        model: 'w_sb_minismg',
        boxItemId: 561,
        materialType: 'green',
        materials: 25,
        weight: 1900,
        width: 3,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 37,
        hash: 'weapon_minismg',
        isMelee: false,
        limit: 3000,
        description: 'items[270].description'
    },
    {
        // Штурмовая винтовка Mk2
        id: 271,
        title: 'items[271].title',
        model: 'w_ar_assaultriflemk2',
        boxItemId: 487,
        materialType: 'green',
        materials: 30,
        weight: 2600,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 39,
        hash: 'weapon_assaultrifle_mk2',
        isMelee: false,
        limit: 7500,
        description: 'items[271].description'
    },
    {
        // Специальный карабин Mk2
        id: 272,
        title: 'items[272].title',
        model: 'w_ar_specialcarbinemk2',
        boxItemId: 500,
        materialType: 'green',
        materials: 40,
        weight: 2300,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 172,
        hash: 'weapon_specialcarbine_mk2',
        isMelee: false,
        limit: 7500,
        description: 'items[272].description'
    },
    {
        // Специальный карабин
        id: 273,
        title: 'items[273].title',
        model: 'w_ar_specialcarbine',
        weight: 2700,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 172,
        hash: 'weapon_specialcarbine',
        isMelee: false,
        limit: 7500,
        description: 'items[273].description'
    },
    {
        // Карабинная винтовка Mk2
        id: 274,
        title: 'items[274].title',
        model: 'w_ar_carbineriflemk2',
        boxItemId: 492,
        materialType: 'green',
        materials: 40,
        weight: 2300,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 172,
        hash: 'weapon_carbinerifle_mk2',
        isMelee: false,
        limit: 7500,
        description: 'items[274].description'
    },
    {
        // Карабинная винтовка
        id: 275,
        title: 'items[275].title',
        model: 'w_ar_carbinerifle',
        boxItemId: 491,
        materialType: 'green',
        materials: 20,
        weight: 2600,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 172,
        hash: 'weapon_carbinerifle',
        isMelee: false,
        limit: 7500,
        description: 'items[275].description'
    },
    {
        // Винтовка Буллпап
        id: 276,
        title: 'items[276].title',
        model: 'w_ar_bullpuprifle',
        weight: 2700,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 172,
        hash: 'weapon_bullpuprifle',
        isMelee: false,
        limit: 7500,
        description: 'items[276].description'
    },
    {
        // Винтовка Буллпап Mk2
        id: 277,
        title: 'items[277].title',
        model: 'w_ar_bullpupriflemk2',
        boxItemId: 490,
        materialType: 'green',
        materials: 40,
        weight: 2300,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 172,
        hash: 'weapon_bullpuprifle_mk2',
        isMelee: false,
        limit: 7500,
        description: 'items[277].description'
    },
    {
        // Компактная винтовка
        id: 278,
        title: 'items[278].title',
        model: 'w_ar_assaultrifle_smg',
        weight: 1800,
        width: 3,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 39,
        materials: 100,
        hash: 'weapon_compactrifle',
        isMelee: false,
        limit: 7500,
        description: 'items[278].description'
    },
    {
        // Тяжелая снайперская винтовка
        id: 279,
        title: 'items[279].title',
        model: 'w_sr_heavysniper',
        boxItemId: 557,
        materialType: 'green',
        materials: 10000,
        weight: 4500,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 171,
        hash: 'weapon_heavysniper',
        isMelee: false,
        limit: 400,
        description: 'items[279].description'
    },
    {
        // Гранатомет
        id: 280,
        title: 'items[280].title',
        model: 'w_lr_grenadelauncher',
        weight: 10000,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        hash: 'weapon_grenadelauncher',
        isMelee: false,
        description: 'items[280].description'
    },
    {
        // Дефибриллятор
        id: 281,
        title: 'items[281].title',
        model: 'prop_ecg_01',
        boxItemId: 506,
        materialType: 'red',
        materials: 400,
        weight: 500,
        width: 2,
        height: 2,
        hasSerial: true,
        canSeize: true,
        maxCondition: 50,
        tag: 'items.tags.facilities',
        description: 'items[281].description'
    },
    {
        // .357 Magnum
        id: 282,
        title: 'items[282].title',
        model: 'tor_ammo_01_357Magnum',
        boxItemId: 481,
        materialType: 'green',
        materials: 2,
        weight: 35,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        canSeize: true,
        clip: 96,
        description: 'items[282].description',
        stack: 96
    },
    {
        // Карта с лицензиями
        id: 283,
        title: 'items[283].title',
        model: 'bkr_prop_fakeid_singledriverl',
        weight: 150,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[283].description',
        usable: true
    },
    {
        // Механический эмбоссер
        id: 284,
        title: 'items[284].title',
        model: 'bkr_prop_fakeid_embosser',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.facilities',
        description: 'items[284].description'
    },
    {
        // Ламинатор
        id: 285,
        title: 'items[285].title',
        model: 'bkr_prop_fakeid_laminator',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.facilities',
        description: 'items[285].description'
    },
    {
        // Пачка наличных
        id: 286,
        title: 'items[286].title',
        model: 'bkr_prop_moneypack_01a',
        weight: 0.01,
        width: 2,
        height: 1,
        stack: 150000,
        dropOnDeath: true,
        dropOnQuit: true,
        canSeize: true,
        //canDrag: false,
        canDragCop: false,
        canPickupToBag: false,
        tag: 'items.tags.quests',
        description: 'items[286].description'
    },
    {
        // Кейс с оружием
        id: 287,
        title: 'items[287].title',
        model: 'hei_prop_heist_thermite_case',
        weight: 8500,
        width: 4,
        height: 3,
        takeable: true,
        tag: 'items.tags.quests',
        description: 'items[287].description'
    },
    {
        // Фальшивые паспорта
        id: 288,
        title: 'items[288].title',
        model: 'bkr_prop_fakeid_bundlepassports',
        weight: 30,
        width: 2,
        height: 2,
        usable: true,
        tag: 'items.tags.quests',
        description: 'items[288].description'
    },
    {
        // Закладка с наркотиком
        id: 289,
        title: 'items[289].title',
        model: 'prop_meth_bag_01',
        weight: 20,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.quests',
        description: 'items[289].description'
    },
    {
        // Блок с веществом: Green
        id: 290,
        title: 'items[290].title',
        model: 'prop_weed_block_01',
        weight: 500,
        width: 3,
        height: 2,
        usable: true,
        canDragCop: false,
        canSeize: true,
        tag: 'items.tags.drugs',
        description: 'items[290].description'
    },
    {
        // Блок с веществом: White
        id: 291,
        title: 'items[291].title',
        model: 'bkr_prop_coke_cutblock_01',
        weight: 500,
        width: 3,
        height: 1,
        usable: true,
        canDragCop: false,
        canSeize: true,
        tag: 'items.tags.drugs',
        description: 'items[291].description'
    },
    {
        // Испанское вино Кава
        id: 292,
        title: 'items[292].title',
        model: 'prop_cava',
        weight: 750,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        barPrice: 50000,
        description: 'items[292].description',
        stats: {
            hunger: 10,
            water: 20
        },
        buffs: [
            {
                name: 'Drunk',
                duration: 300 * 1000
            }
        ],
        useItem: {
            animationProp: 'cava_bottle',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'cava_bottle',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
            questTriggers: ['drink_alcohol'],
            seasonPassTriggers: ['task_drink_alcohol']
        }
    },
    {
        // Пиво Stzopen
        id: 293,
        title: 'items[293].title',
        model: 'prop_beer_stzopen',
        weight: 500,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        barPrice: 20000,
        description: 'items[293].description',
        stats: {
            hunger: 15,
            water: 25,
            poisonChance: 1,
        },
        buffs: [
            {
                name: 'Drunk',
                duration: 300 * 1000
            }
        ],
        useItem: {
            animationProp: 'stzopen_bottle',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'stzopen_bottle',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
            questTriggers: ['drink_alcohol'],
            seasonPassTriggers: ['task_drink_alcohol']
        }
    },
    {
        // Коньяк Bourgeois
        id: 294,
        title: 'items[294].title',
        model: 'prop_bottle_cognac',
        weight: 750,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        barPrice: 60000,
        description: 'items[294].description',
        stats: {
            water: 30
        },
        buffs: [
            {
                name: 'Drunk',
                duration: 300 * 1000
            }
        ],
        useItem: {
            animationProp: 'cognac_bottle',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'cognac_bottle',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
            questTriggers: ['drink_alcohol'],
            seasonPassTriggers: ['task_drink_alcohol']
        }
    },
    {
        // Премиальная водка Nogo
        id: 295,
        title: 'items[295].title',
        model: 'prop_vodka_bottle',
        weight: 750,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        barPrice: 100000,
        description: 'items[295].description',
        stats: {
            water: 15
        },
        buffs: [
            {
                name: 'Drunk',
                duration: 300 * 1000
            }
        ],
        useItem: {
            animationProp: 'vodka_bottle',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'vodka_bottle',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
            questTriggers: ['drink_alcohol'],
            seasonPassTriggers: ['task_drink_alcohol']
        }
    },
    {
        // Вино Rockford Hills Resort
        id: 296,
        title: 'items[296].title',
        model: 'prop_wine_bot_02',
        weight: 750,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        barPrice: 150000,
        description: 'items[296].description',
        stats: {
            hunger: 10,
            water: 35,
        },
        buffs: [
            {
                name: 'Drunk',
                duration: 300 * 1000
            }
        ],
        useItem: {
            animationProp: 'wine_bottle',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'wine_bottle',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
            questTriggers: ['drink_alcohol'],
            seasonPassTriggers: ['task_drink_alcohol']
        }
    },
    {
        // Пакетик с Green
        id: 297,
        title: 'items[297].title',
        model: 'p_meth_bag_01_s',
        weight: 1,
        width: 1,
        height: 1,
        fastSlot: true,
        boxItemId: [747, 748, 749],
        usable: true,
        canDragCop: false,
        canSeize: true,
        tag: 'items.tags.drugs',
        description: 'items[297].description',
        stack: 20,
        stats: {
            water: -5,
        },
        buffs: [
            {
                name: 'Green',
                duration: 300 * 1000
            }
        ],
        useItem: {
            overrideEffect: 'UseItemBong',

            animationProp: null,
            animationDict: 'majestic_crawl_crouch',
            animationName: 'bong_idle',

            crawlingAnimationProp: null,
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'bong_crawl',
            crawlingAnimationFlag: 32,

            crouchAnimationProp: null,
            crouchAnimationDict: 'majestic_crawl_crouch',
            crouchAnimationName: 'bong_crouch',

            props: ['bong', 'lighter'],

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usedSound: null,

            seasonPassTriggers: ['task_use_cig'],

            duration: 10000
        }
    },
    {
        // Пакетик с Blue
        id: 298,
        title: 'items[298].title',
        model: 'p_meth_bag_01_s',
        weight: 1,
        width: 1,
        height: 1,
        boxItemId: [750, 751, 752],
        usable: true,
        canDragCop: false,
        canSeize: true,
        fastSlot: true,
        tag: 'items.tags.drugs',
        description: 'items[298].description',
        stack: 20,
        stats: {
            water: -5,
        },
        buffs: [
            {
                name: 'Blue',
                duration: 300 * 1000
            }
        ],
        useItem: {
            overrideEffect: 'UseItemBong',

            animationProp: null,
            animationDict: 'majestic_crawl_crouch',
            animationName: 'bong_idle',

            crawlingAnimationProp: null,
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'bong_crawl',
            crawlingAnimationFlag: 32,

            crouchAnimationProp: null,
            crouchAnimationDict: 'majestic_crawl_crouch',
            crouchAnimationName: 'bong_crouch',

            props: ['bong', 'lighter'],

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usedSound: null,

            seasonPassTriggers: ['task_use_cig'],

            duration: 10000
        }
    },
    {
        // Пакетик с White
        id: 299,
        title: 'items[299].title',
        model: 'p_meth_bag_01_s',
        weight: 1,
        width: 1,
        height: 1,
        boxItemId: [753, 754, 755],
        usable: true,
        canDragCop: false,
        canSeize: true,
        fastSlot: true,
        tag: 'items.tags.drugs',
        description: 'items[299].description',
        stack: 20,
        stats: {
            water: -5,
        },
        buffs: [
            {
                name: 'White',
                duration: 300 * 1000
            }
        ],
        useItem: {
            overrideEffect: 'UseItemBong',

            animationProp: null,
            animationDict: 'majestic_crawl_crouch',
            animationName: 'bong_idle',

            crawlingAnimationProp: null,
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'bong_crawl',
            crawlingAnimationFlag: 32,

            crouchAnimationProp: null,
            crouchAnimationDict: 'majestic_crawl_crouch',
            crouchAnimationName: 'bong_crouch',

            props: ['bong', 'lighter'],

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usedSound: null,

            seasonPassTriggers: ['task_use_cig'],

            duration: 10000
        }
    },
    {
        // Клад с веществом: Green
        id: 300,
        title: 'items[300].title',
        model: 'bkr_prop_Green_bigbag_03a',
        weight: 500,
        width: 3,
        height: 3,
        usable: true,
        canDragCop: false,
        canSeize: true,
        tag: 'items.tags.quests',
        description: 'items[300].description'
    },
    {
        // Клад с веществом: Blue
        id: 301,
        title: 'items[301].title',
        model: 'prop_weed_block_01',
        weight: 500,
        width: 3,
        height: 3,
        usable: true,
        canDragCop: false,
        canSeize: true,
        tag: 'items.tags.quests',
        description: 'items[301].description'
    },
    {
        // Клад с веществом: White
        id: 302,
        title: 'items[302].title',
        model: 'ba_prop_battle_drug_package_02',
        weight: 500,
        width: 3,
        height: 3,
        usable: true,
        canDragCop: false,
        canSeize: true,
        tag: 'items.tags.quests',
        description: 'items[302].description'
    },
    {
        // Пакет с веществом: Blue
        id: 303,
        title: 'items[303].title',
        model: 'bkr_prop_meth_smallbag_01a',
        weight: 500,
        width: 3,
        height: 3,
        usable: true,
        canDragCop: false,
        canSeize: true,
        tag: 'items.tags.drugs',
        description: 'items[303].description'
    },
    {
        // Оружейные материалы
        id: 304,
        title: 'items[304].title',
        model: 'mj_prop_addon_weapbox',
        weight: 100,
        width: 5,
        height: 3,
        usable: true,
        tag: 'items.tags.materials',
        materialColor: 'green',
        materialPrice: 100,
        stack: 500,
        description: 'items[304].description'
    },
    {
        // Набор прекурсоров
        id: 305,
        title: 'items[305].title',
        model: 'ba_dlc_ba_int2_meth_eqp',
        weight: 500,
        width: 5,
        height: 3,
        usable: true,
        canDragCop: false,
        canSeize: true,
        tag: 'items.tags.materials',
        description: 'items[305].description'
    },
    {
        // Удостоверение Army
        id: 306,
        title: 'items[306].title',
        model: 'prop_fib_badge',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        usable: true,
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[306].description'
    },
    {
        // Удостоверение Police
        id: 307,
        title: 'items[307].title',
        model: 'prop_fib_badge',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        usable: true,
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[307].description'
    },
    {
        // Банковская карта Standart
        id: 308,
        title: 'items[308].title',
        model: 'prop_cs_credit_card',
        weight: 5,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[308].description',
        tariff: 'Standart'
    },
    {
        // Банковская карта Premium
        id: 309,
        title: 'items[309].title',
        model: 'prop_cs_credit_card',
        weight: 5,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[309].description',
        tariff: 'Premium'
    },
    {
        // Банковская карта VIP
        id: 310,
        title: 'items[310].title',
        model: 'prop_cs_credit_card',
        weight: 5,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[310].description',
        tariff: 'VIP'
    },
    {
        // Противогаз
        id: 311,
        title: 'items[311].title',
        model: 'prop_player_gasmask',
        weight: 500,
        width: 2,
        height: 2,
        tag: 'items.tags.equipment',
        description: ''
    },
    {
        // Фишки из Казино
        id: 312,
        title: 'items[312].title',
        model: '',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.gameItems',
        description: 'items[312].description'
    },
    {
        // Игральные кости
        id: 313,
        title: 'items[313].title',
        model: '',
        weight: 10,
        width: 1,
        height: 1,
        tag: 'items.tags.gameItems',
        description: 'items[313].description'
    },
    {
        // Дымовой гранатомет
        id: 314,
        title: 'items[314].title',
        model: 'w_lr_grenadelauncher',
        weight: 500,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        materials: 9999,
        hash: 'weapon_grenadelauncher_smoke',
        isMelee: false,
        description: 'items[314].description'
    },
    {
        // Тяжелый бронежилет
        id: 315,
        title: 'items[315].title',
        model: 'prop_bodyarmour_02',
        hasSerial: true,
        weight: 4000,
        width: 3,
        height: 3,
        usable: true,
        materialType: 'blue',
        materials: 30,
        armour: 100,
        tag: 'items.tags.havyArmor',
        canSeize: true,
        description: 'items[315].description',
        canUtilize: true
    },
    {
        // Наживка
        id: 316,
        title: 'items[316].title',
        model: 'prop_drug_package_02',
        weight: 30,
        width: 1,
        height: 1,
        shopCount: 30,
        stack: 30,
        tag: 'items.tags.misc',
        description: 'items[316].description',
    },
    {
        // Лотерейный билет
        id: 317,
        title: 'items[317].title',
        model: '',
        weight: 5,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[317].description'
    },
    {
        // Самогон Лимоновка
        id: 318,
        title: 'items[318].title',
        model: 'p_cs_bottle_01',
        weight: 1100,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        description: 'items[318].description',
        materials: 1,
        stats: {
            water: 15,
        },
        buffs: [
            {
                name: 'Drunk',
                duration: 300 * 1000
            }
        ],
        useItem: {
            animationProp: 'moonshine_bottle',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            crawlingAnimationProp: 'moonshine_bottle',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
            questTriggers: ['drink_alcohol'],
            seasonPassTriggers: ['task_drink_alcohol']
        }
    },
    {
        // Видеокамера
        id: 319,
        title: 'items[319].title',
        model: 'prop_v_cam_01',
        weight: 3000,
        width: 3,
        height: 2,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[319].description'
    },
    {
        // Микрофон
        id: 320,
        title: 'items[320].title',
        model: 'p_ing_microphonel_01',
        weight: 300,
        width: 1,
        height: 2,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[320].description'
    },
    {
        // Медицинская справка
        id: 321,
        title: 'items[321].title',
        model: '',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[321].description',
        usable: true
    },
    {
        // Заготовка под ключ
        id: 322,
        title: 'items[322].title',
        model: 'prop_cuff_keys_01',
        weight: 300,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[322].description'
    },
    {
        // Пачка Синь
        id: 323,
        title: 'items[323].title',
        model: 'ng_proc_cigpak01c',
        weight: 20,
        width: 1,
        height: 1,
        takeable: true,
        fastSlot: true,
        tag: 'items.tags.products',
        shopCount: 7,
        stack: 7,
        description: 'items[323].description',
        materials: 1,
        stats: {
            hp: -1
        },
        useItem: {
            overrideEffect: 'UseItemCig',

            animationProp: 'cigarette',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke',

            usingSound: 'cigarette',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            useFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_smoke',
                boneIndex: 20279,
                timeout: 10000
            },

            processFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_exhale_mouth',
                boneIndex: 20279,
                timeout: 10000
            },

            processDelay: 3000,

            seasonPassTriggers: ['task_use_cig'],

            duration: 28000
        }
    },
    {
        // Удостоверение EMS
        id: 324,
        title: 'items[324].title',
        model: 'prop_fib_badge',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        usable: true,
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[324].description'
    },
    {
        // Удостоверение Weazel News
        id: 325,
        title: 'items[325].title',
        model: 'prop_fib_badge',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        usable: true,
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[325].description'
    },
    {
        // Пистолет-пулемёт Томпсона
        id: 326,
        title: 'items[326].title',
        model: 'w_sb_gusenberg',
        boxItemId: 501,
        materialType: 'green',
        materials: 50,
        weight: 4400,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 254,
        hash: 'weapon_gusenberg',
        isMelee: false,
        limit: 7500,
        description: 'items[326].description'
    },
    {
        // Клюшка для гольфа
        id: 327,
        title: 'items[327].title',
        model: 'w_me_gclub',
        weight: 1000,
        width: 1,
        height: 4,
        tag: 'items.tags.ammunition',
        caliber: true,
        takeable: true,
        hash: 'weapon_golfclub',
        isMelee: true,
        isTool: true,
        disableDropOnCapt: true,
        description: 'items[327].description'
    },
    {
        // Тяжелая снайперская винтовка Mk2
        id: 328,
        title: 'items[328].title',
        model: 'w_sr_heavysnipermk2',
        boxItemId: 558,
        materialType: 'green',
        materials: 20000,
        weight: 8000,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 171,
        limit: 500,
        hash: 'weapon_heavysniper_mk2',
        isMelee: false,
        description: 'items[328].description'
    },
    {
        // Снайперская винтовка
        id: 329,
        title: 'items[329].title',
        model: 'w_sr_sniperrifle',
        boxItemId: 553,
        materialType: 'green',
        materials: 3000,
        weight: 6500,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 171,
        hash: 'weapon_sniperrifle',
        isMelee: false,
        limit: 750,
        description: 'items[329].description'
    },
    {
        // Боевой дробовик
        id: 330,
        title: 'items[330].title',
        model: 'w_sg_sweeper',
        weight: 3000,
        width: 3,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 38,
        materials: 100,
        limit: 800,
        hash: 'weapon_autoshotgun',
        isMelee: false,
        description: 'items[330].description'
    },
    {
        // Пулемёт Калашникова
        id: 331,
        title: 'items[331].title',
        model: 'w_mg_mg',
        boxItemId: 495,
        materialType: 'green',
        materials: 350,
        weight: 7500,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 39,
        hash: 'weapon_mg',
        isMelee: false,
        limit: 10000,
        description: 'items[331].description'
    },
    {
        // Ручной пулемет
        id: 332,
        title: 'items[332].title',
        model: 'w_mg_combatmg',
        boxItemId: 563,
        materialType: 'green',
        materials: 3000,
        weight: 6100,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 172,
        hash: 'weapon_combatmg',
        isMelee: false,
        limit: 10000,
        description: 'items[332].description'
    },
    {
        // Ручной пулемет Мk2
        id: 333,
        title: 'items[333].title',
        model: 'w_mg_combatmgmk2',
        boxItemId: 564,
        materialType: 'green',
        materials: 2500,
        weight: 7200,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 172,
        hash: 'weapon_combatmg_mk2',
        isMelee: false,
        limit: 10000,
        description: 'items[333].description'
    },
    {
        // ИРП Армии США
        id: 334,
        title: 'items[334].title',
        model: 'mj_mre_pack',
        boxItemId: 556,
        materialType: 'blue',
        materials: 5,
        weight: 600,
        width: 2,
        height: 2,
        usable: true,
        fastSlot: true,
        stack: 1,
        tag: 'items.tags.food',
        description: 'items[334].description',
        stats: {
            hunger: 100,
            water: 100
        },
        useItem: {
            animationProp: 'choc_ego',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            crawlingAnimationProp: 'choc_ego',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Вейп
        id: 335,
        title: 'items[335].title',
        model: 'ba_prop_battle_vape_01',
        weight: 40,
        width: 2,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.misc',
        materials: 10,
        description: 'items[335].description',
        useItem: {
            overrideEffect: 'UseItemVape',

            removeOnFinish: false,

            animationProp: 'vape',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',
            animationFlag: 50,

            usingSound: 'vape',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            processFx: {
                fxLib: 'core',
                fxName: 'exp_grd_bzgas_smoke',
                boneIndex: 20279,
                timeout: 5000
            },

            processDelay: 3500,

            duration: 10000
        }
    },
    {
        // Кальян
        id: 336,
        title: 'items[336].title',
        model: 'prop_hookah',
        weight: 3400,
        width: 3,
        height: 4,
        usable: true,
        tag: 'items.tags.misc',
        materials: 10,
        description: 'items[336].description'
    },
    {
        // Винтовка Marksman Mk2
        id: 337,
        title: 'items[337].title',
        model: 'w_sr_marksmanrifle',
        boxItemId: 494,
        materialType: 'green',
        materials: 4000,
        weight: 3700,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 39,
        hash: 'weapon_marksmanrifle_mk2',
        isMelee: false,
        limit: 1000,
        description: 'items[337].description'
    },
    {
        // Пончики
        id: 338,
        title: 'items[338].title',
        model: 'prop_amb_donut',
        weight: 40,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[338].description',
        shopCount: 3,
        stack: 3,
        barPrice: 12000,
        stats: {
            hunger: 20,
            water: -20
        },
        useItem: {
            animationProp: 'donut',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'donut',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Блинчики с сиропом
        id: 339,
        title: 'items[339].title',
        model: 'ng_proc_food_burg02a',
        weight: 250,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[339].description',
        barPrice: 11000,
        stats: {
            hunger: 15,
            water: -10
        },
        useItem: {
            animationProp: 'pancake',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'pancake',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 5000,
        }
    },
    {
        // Молочный коктейль
        id: 340,
        title: 'items[340].title',
        model: 'prop_cocktail',
        weight: 250,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[340].description',
        barPrice: 18000,
        stats: {
            hunger: +5,
            water: +50
        },
        useItem: {
            animationProp: 'cocktail',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'cocktail',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000
        }
    },
    {
        // Тост Пит-стоп
        id: 341,
        title: 'items[341].title',
        model: 'prop_food_bs_chips',
        weight: 250,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[341].description',
        barPrice: 15000,
        stats: {
            hunger: 20,
            water: -10
        },
        useItem: {
            animationProp: 'tost',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'tost',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Шаурма
        id: 342,
        title: 'items[342].title',
        model: 'prop_food_burg2',
        weight: 350,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[342].description',
        barPrice: 24000,
        stats: {
            hunger: 45,
            water: -10,
            poisonChance: 5
        },
        useItem: {
            animationProp: 'shaurma',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'shaurma',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000,
        }
    },
    {
        // Салат Цезарь
        id: 343,
        title: 'items[343].title',
        model: 'ng_proc_food_burg02a',
        weight: 200,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[343].description',
        barPrice: 18000,
        stats: {
            hunger: 25,
            water: 10
        },
        useItem: {
            animationProp: 'cesar',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'cesar',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Стейк Grand steak
        id: 344,
        title: 'items[344].title',
        model: 'prop_cs_steak',
        weight: 250,
        width: 2,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[344].description',
        barPrice: 32000,
        stats: {
            hunger: 75,
            water: -10,
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Вишневый пирог
        id: 345,
        title: 'items[345].title',
        model: 'ng_proc_food_chips01c',
        weight: 300,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[345].description',
        barPrice: 14000,
        stats: {
            hunger: 25,
            water: -10,
        },
        useItem: {
            animationProp: 'cherrypie',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'cherrypie',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Тяжелый бронежилет
        id: 346,
        title: 'items[346].title',
        model: 'prop_bodyarmour_02',
        boxItemId: 550,
        hasSerial: true,
        weight: 4000,
        width: 3,
        height: 3,
        usable: true,
        materials: 30,
        materialType: 'blue',
        armour: 100,
        tag: 'items.tags.havyArmor',
        canSeize: true,
        description: 'items[346].description',
        canUtilize: true
    },
    {
        // Тяжелый бронежилет
        id: 347,
        title: 'items[347].title',
        model: 'prop_bodyarmour_02',
        boxItemId: 550,
        hasSerial: true,
        weight: 4000,
        width: 3,
        height: 3,
        usable: true,
        materials: 30,
        materialType: 'blue',
        armour: 100,
        tag: 'items.tags.havyArmor',
        canSeize: true,
        canUtilize: true,
        description: 'items[347].description'
    },
    {
        // Аквамарин
        id: 348,
        title: 'items[348].title',
        model: 'ribka_tor4',
        weight: 1,
        width: 2,
        height: 1,
        // boxItemId: 720,
        tag: 'items.tags.fish',
        // chance: 50,
        minWeight: 615,
        maxWeight: 840,
        description: 'items[348].description',
        stack: 10000,
        canSeize: true
    },
    {
        // Золотая рыбка
        id: 349,
        title: 'items[349].title',
        model: 'ribka_tor5',
        weight: 1,
        width: 2,
        height: 1,
        // boxItemId: 721,
        tag: 'items.tags.fish',
        // chance: 50,
        minWeight: 460,
        maxWeight: 700,
        description: 'items[349].description',
        stack: 10000,
        canSeize: true
    },
    {
        // Красный солдат
        id: 350,
        title: 'items[350].title',
        model: 'ribka_tor6',
        weight: 1,
        width: 2,
        height: 1,
        // boxItemId: 717,
        tag: 'items.tags.fish',
        // chance: 50,
        minWeight: 550,
        maxWeight: 825,
        description: 'items[350].description',
        stack: 10000,
        canSeize: true
    },
    {
        // Карась
        id: 351,
        title: 'items[351].title',
        model: 'ribka_tor7',
        weight: 1,
        width: 2,
        height: 1,
        // boxItemId: 718,
        tag: 'items.tags.fish',
        // chance: 50,
        minWeight: 685,
        maxWeight: 960,
        description: 'items[351].description',
        stack: 10000,
        canSeize: true
    },
    {
        // Легкий бронежилет
        id: 352,
        title: 'items[352].title',
        model: 'prop_armour_pickup',
        boxItemId: 549,
        hasSerial: true,
        weight: 2000,
        width: 2,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[352].description',
        canUtilize: true
    },
    {
        // Легкий бронежилет
        id: 353,
        title: 'items[353].title',
        model: 'prop_armour_pickup',
        boxItemId: 549,
        hasSerial: true,
        weight: 2000,
        width: 2,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[353].description',
        canUtilize: true
    },
    {
        // Легкий бронежилет
        id: 354,
        title: 'items[354].title',
        model: 'prop_armour_pickup',
        boxItemId: 549,
        hasSerial: true,
        weight: 2000,
        width: 2,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[354].description',
        canUtilize: true
    },
    {
        // Легкий бронежилет
        id: 355,
        title: 'items[355].title',
        model: 'prop_armour_pickup',
        boxItemId: 549,
        hasSerial: true,
        weight: 2000,
        width: 2,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[355].description',
        canUtilize: true
    },
    {
        // Легкий бронежилет
        id: 356,
        title: 'items[356].title',
        model: 'prop_armour_pickup',
        boxItemId: 549,
        hasSerial: true,
        weight: 2000,
        width: 2,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[356].description',
        canUtilize: true
    },
    {
        // Легкий бронежилет
        id: 357,
        title: 'items[357].title',
        model: 'prop_armour_pickup',
        boxItemId: 549,
        hasSerial: true,
        weight: 2000,
        width: 2,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[357].description',
        canUtilize: true
    },
    {
        // Легкий бронежилет
        id: 358,
        title: 'items[358].title',
        model: 'prop_armour_pickup',
        boxItemId: 549,
        hasSerial: true,
        weight: 2000,
        width: 2,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[358].description',
        canUtilize: true
    },
    {
        // Легкий бронежилет
        id: 359,
        title: 'items[359].title',
        model: 'prop_armour_pickup',
        boxItemId: 549,
        hasSerial: true,
        weight: 2000,
        width: 2,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[359].description',
        canUtilize: true
    },
    {
        // Легкий бронежилет
        id: 360,
        title: 'items[360].title',
        model: 'prop_armour_pickup',
        boxItemId: 549,
        hasSerial: true,
        weight: 2000,
        width: 2,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[360].description',
        canUtilize: true
    },
    {
        // Легкий бронежилет
        id: 361,
        title: 'items[361].title',
        model: 'prop_armour_pickup',
        boxItemId: 549,
        hasSerial: true,
        weight: 2000,
        width: 2,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[361].description',
        canUtilize: true
    },
    {
        // Роза
        id: 362,
        title: 'items[362].title',
        model: 'prop_single_rose',
        weight: 70,
        width: 1,
        height: 2,
        takeable: true,
        tag: 'items.tags.misc',
        description: 'items[362].description'
    },
    {
        // Зонт
        id: 363,
        title: 'items[363].title',
        model: 'p_amb_brolly_01',
        weight: 500,
        width: 1,
        height: 3,
        takeable: true,
        isTool: true,
        deathSafe: true,
        tag: 'items.tags.misc',
        description: 'items[363].description'
    },
    {
        // Мишка
        id: 364,
        title: 'items[364].title',
        model: 'v_ilev_mr_rasberryclean',
        weight: 250,
        width: 1,
        height: 1,
        takeable: true,
        tag: 'items.tags.misc',
        description: 'items[364].description'
    },
    {
        // Парашют
        id: 365,
        title: 'items[365].title',
        model: 'p_parachute_s',
        weight: 10000,
        width: 2,
        height: 3,
        caliber: true,
        materials: 75,
        tag: 'items.tags.ammunition',
        canSeize: true,
        hash: 'gadget_parachute',
        isMelee: true,
        disableDropOnCapt: true,
        description: 'items[365].description',
        isTool: true,
        demorganSafe: true
    },
    {
        // Сумка с дрелью
        id: 366,
        title: 'items[366].title',
        model: 'p_ld_heist_bag_s',
        weight: 500,
        width: 5,
        height: 3,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[366].description'
    },
    {
        // Сумка с дрелью
        id: 367,
        title: 'items[367].title',
        model: 'p_ld_heist_bag_s',
        weight: 500,
        width: 5,
        height: 3,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[367].description'
    },
    {
        // Сумка с дрелью
        id: 368,
        title: 'items[368].title',
        model: 'p_ld_heist_bag_s',
        weight: 500,
        width: 5,
        height: 3,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[368].description'
    },
    {
        // Сумка с дрелью
        id: 369,
        title: 'items[369].title',
        model: 'p_ld_heist_bag_s',
        weight: 500,
        width: 5,
        height: 3,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[369].description'
    },
    {
        // Сумка с дрелью
        id: 370,
        title: 'items[370].title',
        model: 'p_ld_heist_bag_s',
        weight: 500,
        width: 5,
        height: 3,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[370].description'
    },
    {
        // Сумка с дрелью
        id: 371,
        title: 'items[371].title',
        model: 'p_ld_heist_bag_s',
        weight: 500,
        width: 5,
        height: 3,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[371].description'
    },
    {
        // Сумка с дрелью
        id: 372,
        title: 'items[372].title',
        model: 'p_ld_heist_bag_s',
        weight: 500,
        width: 5,
        height: 3,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[372].description'
    },
    {
        // Сумка с дрелью
        id: 373,
        title: 'items[373].title',
        model: 'p_ld_heist_bag_s',
        weight: 500,
        width: 5,
        height: 3,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[373].description'
    },
    {
        // Сумка с дрелью
        id: 374,
        title: 'items[374].title',
        model: 'p_ld_heist_bag_s',
        weight: 500,
        width: 5,
        height: 3,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[374].description'
    },
    {
        // Сумка с дрелью
        id: 375,
        title: 'items[375].title',
        model: 'p_ld_heist_bag_s',
        weight: 500,
        width: 5,
        height: 3,
        tag: 'items.tags.clothesAndAccessories',
        description: 'items[375].description'
    },
    {
        // Барбекю
        id: 376,
        title: 'items[376].title',
        model: 'prop_bbq_5',
        weight: 4500,
        usable: true,
        width: 2,
        height: 3,
        tag: 'items.tags.facilities',
        description: 'items[376].description'
    },
    {
        // Жаренная рыба
        id: 377,
        title: 'items[377].title',
        model: 'ribka_tor1',
        weight: 900,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        tag: 'items.tags.food',
        description: 'items[377].description',
        stats: {
            hunger: 75,
            water: -20,
        },
        useItem: {
            animationProp: 'choc_ego',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'choc_ego',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Апельсин
        id: 378,
        title: 'items[378].title',
        model: 'prop_orange',
        weight: 130,
        boxItemId: 702,
        usable: true,
        width: 1,
        height: 1,
        fastSlot: true,
        tag: 'items.tags.food',
        tags: ['vegetable'],
        description: 'items[378].description',
        stats: {
            hunger: 5,
            water: 5,
        },
        useItem: {
            animationProp: 'orange',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'orange',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Картошка
        id: 379,
        title: 'items[379].title',
        model: 'prop_potato',
        weight: 150,
        boxItemId: 705,
        usable: true,
        width: 1,
        height: 1,
        fastSlot: true,
        tag: 'items.tags.food',
        tags: ['vegetable'],
        description: 'items[379].description',
        stats: {
            hunger: 25,
            water: 0,
            poisonChance: 5
        },
        useItem: {
            animationProp: 'potatoe',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'potatoe',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Семена картофеля
        id: 380,
        title: 'items[380].title',
        model: 'prop_drug_package_02',
        weight: 20,
        usable: true,
        width: 1,
        height: 1,
        shopCount: 10,
        stack: 10,
        fastSlot: true,
        tag: 'items.tags.agriculture',
        description: 'items[380].description'
    },
    {
        // Капуста
        id: 381,
        title: 'items[381].title',
        model: 'prop_veg_crop_03_ca',
        weight: 550,
        boxItemId: 704,
        usable: true,
        width: 1,
        height: 1,
        fastSlot: true,
        tag: 'items.tags.food',
        tags: ['vegetable'],
        description: 'items[381].description',
        stats: {
            hunger: 5,
            water: 5,
        },
        useItem: {
            animationProp: 'cabbage',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'cabbage',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Семена капусты
        id: 382,
        title: 'items[382].title',
        model: 'prop_drug_package_02',
        weight: 20,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        shopCount: 10,
        stack: 10,
        tag: 'items.tags.agriculture',
        description: 'items[382].description'
    },
    {
        // Тыква
        id: 383,
        title: 'items[383].title',
        model: 'prop_veg_crop_03_pump',
        weight: 800,
        boxItemId: 707,
        usable: true,
        width: 2,
        height: 2,
        fastSlot: true,
        tag: 'items.tags.food',
        tags: ['vegetable'],
        description: 'items[383].description',
        stats: {
            hunger: 35,
            water: 10,
        },
        useItem: {
            animationProp: 'cabbage',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'cabbage',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Семена тыквы
        id: 384,
        title: 'items[384].title',
        model: 'prop_drug_package_02',
        weight: 20,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        shopCount: 10,
        stack: 10,
        tag: 'items.tags.agriculture',
        description: 'items[384].description'
    },
    {
        // Кукуруза
        id: 385,
        title: 'items[385].title',
        model: 'prop_corn',
        weight: 200,
        boxItemId: 706,
        usable: true,
        width: 1,
        height: 1,
        fastSlot: true,
        tag: 'items.tags.food',
        tags: ['vegetable'],
        description: 'items[385].description',
        stats: {
            hunger: 15,
            water: 5,
        },
        useItem: {
            animationProp: 'corn',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'corn',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Семена кукурузы
        id: 386,
        title: 'items[386].title',
        model: 'prop_drug_package_02',
        weight: 20,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        shopCount: 10,
        stack: 10,
        tag: 'items.tags.agriculture',
        description: 'items[386].description'
    },
    {
        // Пшеница
        id: 387,
        title: 'items[387].title',
        model: 'prop_psheno',
        weight: 90,
        boxItemId: 703,
        usable: true,
        width: 1,
        height: 2,
        fastSlot: true,
        tag: 'items.tags.products',
        tags: ['vegetable'],
        description: 'items[387].description',
        stats: {
            hunger: 15,
            water: -15,
            poisonChance: 5
        },
        useItem: {
            animationProp: 'wheat',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'wheat',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Семена пшеницы
        id: 388,
        title: 'items[388].title',
        model: 'prop_drug_package_02',
        weight: 20,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        shopCount: 10,
        stack: 10,
        tag: 'items.tags.agriculture',
        description: 'items[388].description'
    },
    {
        // Банан
        id: 389,
        title: 'items[389].title',
        model: 'prop_banan',
        weight: 130,
        boxItemId: 708,
        usable: true,
        width: 1,
        height: 1,
        fastSlot: true,
        tag: 'items.tags.food',
        tags: ['vegetable'],
        description: 'items[389].description',
        stats: {
            hunger: 20,
            water: -5
        },
        useItem: {
            animationProp: 'banana',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'banana',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Семена банана
        id: 390,
        title: 'items[390].title',
        model: 'prop_drug_package_02',
        weight: 20,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        shopCount: 10,
        stack: 10,
        tag: 'items.tags.agriculture',
        description: 'items[390].description',
    },
    {
        // Оранжевый карп
        id: 391,
        title: 'items[391].title',
        model: 'ribka_tor1',
        weight: 1,
        width: 2,
        height: 1,
        // boxItemId: 719,
        tag: 'items.tags.fish',
        // chance: 10,
        minWeight: 820,
        maxWeight: 1000,
        description: 'items[391].description',
        stack: 10000
    },
    {
        // Сорванный куст
        id: 392,
        title: 'items[392].title',
        model: 'bkr_prop_weed_bud_02b',
        weight: 100,
        width: 1,
        height: 1,
        boxItemId: [817, 818, 819],
        canDragCop: false,
        canSeize: true,
        tag: 'items.tags.drugs',
        stack: 6,
        description: 'items[392].description'
    },
    {
        // Семена Green
        id: 393,
        title: 'items[393].title',
        model: 'prop_drug_package_02',
        weight: 20,
        usable: true,
        canSeize: true,
        fastSlot: false,
        canDragCop: false,
        boxItemId: [983],
        width: 1,
        height: 1,
        stack: 2,
        tag: 'items.tags.drugs',
        description: 'items[393].description'
    },
    {
        // Коробка с фейерверками 1
        id: 394,
        title: 'items[394].title',
        model: 'ind_prop_firework_01',
        weight: 300,
        usable: true,
        width: 1,
        height: 2,
        tag: 'items.tags.misc',
        description: 'items[394].description'
    },
    {
        // Коробка с фейерверками 2
        id: 395,
        title: 'items[395].title',
        model: 'ind_prop_firework_04',
        weight: 500,
        usable: true,
        width: 1,
        height: 2,
        tag: 'items.tags.misc',
        description: 'items[395].description'
    },
    {
        // Коробка с фейерверками 3
        id: 396,
        title: 'items[396].title',
        model: 'ind_prop_firework_02',
        weight: 800,
        usable: true,
        width: 1,
        height: 2,
        tag: 'items.tags.misc',
        description: 'items[396].description'
    },
    {
        // Коробка с фейерверками 4
        id: 397,
        title: 'items[397].title',
        model: 'ind_prop_firework_03',
        weight: 1000,
        usable: true,
        width: 2,
        height: 2,
        tag: 'items.tags.misc',
        description: 'items[397].description'
    },
    {
        // Шампиньон обыкновенный
        id: 398,
        title: 'items[398].title',
        model: 'prop_mush6',
        weight: 30,
        width: 1,
        height: 1,
        boxItemId: 722,
        tag: 'items.tags.food',
        tags: ['mushroom'],
        description: 'items[398].description',
        usable: true,
        stats: {
            hunger: 5
        },
        useItem: {
            animationProp: 'mush6',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'mush6',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Вёшенка обыкновенная
        id: 399,
        title: 'items[399].title',
        model: 'prop_mush4',
        weight: 90,
        width: 1,
        height: 1,
        boxItemId: 723,
        tag: 'items.tags.food',
        tags: ['mushroom'],
        description: 'items[399].description',
        usable: true,
        stats: {
            hunger: 10
        },
        useItem: {
            animationProp: 'mush4',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke_enter',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'mush4',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }
    },
    {
        // Гипсизигус шахматный
        id: 400,
        title: 'items[400].title',
        model: 'prop_mush1',
        weight: 70,
        width: 1,
        height: 1,
        boxItemId: 739,
        tag: 'items.tags.food',
        tags: ['mushroom'],
        description: 'items[400].description',
        usable: true,
        stats: {
            hunger: 15
        },
        useItem: {
            animationProp: 'mush1',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke_enter',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'mush1',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }
    },
    {
        // Мухомор
        id: 401,
        title: 'items[401].title',
        model: 'prop_mush3',
        weight: 110,
        width: 1,
        height: 1,
        boxItemId: 740,
        tag: 'items.tags.food',
        tags: ['mushroom'],
        description: 'items[401].description',
        usable: true,
        stats: {
            hunger: 20,
            poisonChance: 35
        },
        buffs: [
            {
                name: 'Agaric',
                duration: 300 * 1000
            }
        ],
        useItem: {
            animationProp: 'agaric',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke_enter',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'agaric',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }
    },
    {
        // Подболотник
        id: 402,
        title: 'items[402].title',
        model: 'prop_mush2',
        weight: 50,
        width: 1,
        height: 1,
        boxItemId: 741,
        tag: 'items.tags.food',
        tags: ['mushroom'],
        description: 'items[402].description',
        usable: true,
        stats: {
            hunger: 25
        },
        useItem: {
            animationProp: 'mush2',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke_enter',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'mush2',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }
    },
    {
        // Подберезовик
        id: 403,
        title: 'items[403].title',
        model: 'prop_mush5',
        weight: 70,
        width: 1,
        height: 1,
        boxItemId: 742,
        tag: 'items.tags.food',
        tags: ['mushroom'],
        description: 'items[403].description',
        usable: true,
        stats: {
            hunger: 30
        },
        useItem: {
            animationProp: 'mush5',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke_enter',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'mush5',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }
    },
    {
        // Иррет 715
        id: 404,
        title: 'items[404].title',
        model: 'ba_prop_battle_vape_01',
        weight: 500,
        usable: true,
        width: 1,
        height: 2,
        tag: 'items.tags.misc',
        description: 'items[404].description',
        buffs: [
            {
                name: 'ViolaBloodDrunk'
            }
        ],
        stats: {
            hunger: -10,
            water: -10,
            hp: +50
        },
        useItem: {
            overrideEffect: 'UseItemVape',

            animationProp: 'vape',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',
            animationFlag: 50,

            usingSound: 'vape',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            processFx: {
                fxLib: 'core',
                fxName: 'exp_grd_bzgas_smoke',
                boneIndex: 20279,
                timeout: 5000
            },

            processDelay: 3500,

            duration: 6000
        }
    },
    {
        // Assault SMG
        id: 405,
        title: 'items[405].title',
        model: 'w_sb_assaultsmg',
        boxItemId: 488,
        materialType: 'green',
        materials: 40,
        weight: 900,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 254,
        hash: 'weapon_assaultsmg',
        isMelee: false,
        limit: 7500,
        description: 'items[405].description'
    },
    {
        // Корзинка для сладостей
        id: 406,
        title: 'items[406].title',
        model: 'pump_hand_q',
        weight: 200,
        width: 2,
        height: 2,
        takeable: true,
        fastSlot: false,
        tag: 'items.tags.misc',
        description: 'items[406].description'
    },
    {
        // Шоколадная плитка
        id: 407,
        title: 'items[407].title',
        model: 'choc_q',
        weight: 10,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        stack: 50,
        tag: 'items.tags.food',
        description: 'items[407].description',
        stats: {
            hunger: 30,
            water: -10,
            hp: 5
        },
        useItem: {
            animationProp: 'choc_ego',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'choc_ego',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Карамельные конфеты
        id: 408,
        title: 'items[408].title',
        model: 'candy2_qnx',
        weight: 10,
        width: 1,
        height: 1,
        stack: 25,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        description: 'items[408].description',
        stats: {
            hunger: 10,
            water: -25,
            hp: 5
        },
        useItem: {
            animationProp: 'choc_ego',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'choc_ego',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Конфета KitKat
        id: 409,
        title: 'items[409].title',
        model: 'candy1_qnx',
        weight: 10,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        stack: 10,
        tag: 'items.tags.food',
        description: 'items[409].description',
        stats: {
            hunger: 10,
            water: -25,
            hp: 5
        },
        useItem: {
            animationProp: 'choc_ego',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'choc_ego',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Весёлая тыква джека
        id: 410,
        title: 'items[410].title',
        model: 'pump2_q',
        weight: 100,
        width: 2,
        height: 2,
        stack: 5,
        tag: 'items.tags.misc',
        description: 'items[410].description'
    },
    {
        // Злая тыква джека
        id: 411,
        title: 'items[411].title',
        model: 'pump1_q',
        weight: 70,
        width: 2,
        height: 2,
        stack: 5,
        tag: 'items.tags.misc',
        description: 'items[411].description'
    },
    {
        // Череп
        id: 412,
        title: 'items[412].title',
        model: 'sklt_q',
        weight: 170,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[412].description'
    },
    {
        // Бинокль
        id: 413,
        title: 'items[413].title',
        model: 'prop_binoc_01',
        weight: 600,
        takeable: true,
        fastSlot: true,
        isTool: true,
        deathSafe: true,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[413].description'
    },
    {
        // Турбо декодер
        id: 414,
        title: 'items[414].title',
        model: 'lr_prop_carkey_fob',
        weight: 270,
        canSeize: true,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.facilities',
        description: 'items[414].description',
        limit: 4
    },
    {
        // Программатор ЭБУ
        id: 415,
        title: 'items[415].title',
        model: 'lr_prop_carkey_fob',
        weight: 650,
        canSeize: true,
        width: 2,
        height: 1,
        stack: 1,
        tag: 'items.tags.facilities',
        description: 'items[415].description',
        limit: 20
    },
    {
        // Кабель I
        id: 416,
        title: 'items[416].title',
        model: 'prop_ld_cable_tie_01',
        weight: 100,
        canSeize: true,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[416].description'
    },
    {
        // Кабель II
        id: 417,
        title: 'items[417].title',
        model: 'prop_ld_cable_tie_01',
        weight: 100,
        canSeize: true,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[417].description'
    },
    {
        // Кабель III
        id: 418,
        title: 'items[418].title',
        model: 'prop_ld_cable_tie_01',
        weight: 100,
        canSeize: true,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[418].description'
    },
    {
        // Кабель IV
        id: 419,
        title: 'items[419].title',
        model: 'prop_ld_cable_tie_01',
        weight: 100,
        canSeize: true,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[419].description'
    },
    {
        // Кабель V
        id: 420,
        title: 'items[420].title',
        model: 'prop_ld_cable_tie_01',
        weight: 100,
        canSeize: true,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[420].description'
    },
    {
        // Кабель VI
        id: 421,
        title: 'items[421].title',
        model: 'prop_ld_cable_tie_01',
        weight: 100,
        canSeize: true,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[421].description'
    },
    {
        // Кабель VII
        id: 422,
        title: 'items[422].title',
        model: 'prop_ld_cable_tie_01',
        weight: 100,
        canSeize: true,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[422].description'
    },
    {
        // Кабель VIII
        id: 423,
        title: 'items[423].title',
        model: 'prop_ld_cable_tie_01',
        weight: 100,
        canSeize: true,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[423].description'
    },
    {
        // Карамельная трость
        id: 424,
        title: 'items[424].title',
        model: 'mj_xm_candy',
        weight: 30,
        usable: true,
        width: 1,
        height: 1,
        stack: 100,
        tag: 'items.tags.misc',
        description: 'items[424].description'
    },
    {
        // Шар новогодний
        id: 425,
        title: 'items[425].title',
        model: 'mj_xm_candy',
        weight: 20,
        width: 1,
        height: 1,
        stack: 50,
        tag: 'items.tags.misc',
        description: 'items[425].description',
    },
    {
        // Гирлянда
        id: 426,
        title: 'items[426].title',
        model: 'mj_xm_candy',
        weight: 500,
        width: 2,
        height: 2,
        stack: 10,
        tag: 'items.tags.misc',
        description: 'items[426].description'
    },
    {
        // Маленький подарок
        id: 427,
        title: 'items[427].title',
        model: 'mj_xm_box5',
        weight: 500,
        usable: true,
        canDrag: false,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.misc',
        description: 'items[427].description'
    },
    {
        // Средний подарок
        id: 428,
        title: 'items[428].title',
        model: 'mj_xm_box8',
        weight: 500,
        usable: true,
        canDrag: false,
        width: 2,
        height: 1,
        stack: 1,
        tag: 'items.tags.misc',
        description: 'items[428].description'
    },
    {
        // Большой подарок
        id: 429,
        title: 'items[429].title',
        model: 'mj_xm_box7',
        weight: 500,
        usable: true,
        canDrag: false,
        width: 2,
        height: 2,
        stack: 1,
        tag: 'items.tags.misc',
        description: 'items[429].description'
    },
    {
        // Военно-морской револьвер
        id: 430,
        title: 'items[430].title',
        model: 'w_pi_wep2_gun',
        weight: 1300,
        width: 3,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 282,
        hash: 'weapon_navyrevolver',
        isMelee: false,
        limit: 2000,
        description: 'items[430].description',
        coins: 150
    },
    {
        // Пистолет Перико
        id: 431,
        title: 'items[431].title',
        model: 'w_pi_singleshoth4',
        weight: 550,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 282,
        hash: 'weapon_gadgetpistol',
        isMelee: false,
        limit: 750,
        coins: 150,
        description: 'items[431].description'
    },
    {
        // Боевой дробовик II
        id: 432,
        title: 'items[432].title',
        model: 'w_sg_pumpshotgunh4',
        boxItemId: 554,
        materialType: 'green',
        materials: 80,
        weight: 3300,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 38,
        hash: 'weapon_combatshotgun',
        isMelee: false,
        limit: 400,
        description: 'items[432].description'
    },
    {
        // Военная винтовка
        id: 433,
        title: 'items[433].title',
        model: 'w_ar_bullpuprifleh4',
        boxItemId: 496,
        materialType: 'green',
        materials: 30,
        weight: 2900,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 172,
        hash: 'weapon_militaryrifle',
        isMelee: false,
        limit: 7500,
        description: 'items[433].description'
    },
    {
        // Золотой гриб
        id: 434,
        title: 'items[434].title',
        model: 'prop_mush_gold',
        weight: 200,
        width: 1,
        height: 1,
        coins: 25,
        tag: 'items.tags.food',
        tags: ['mushroom'],
        description: 'items[434].description',
        usable: true,
        stats: {
            hunger: 35,
            hp: 50
        },
        useItem: {
            animationProp: 'mushGold',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke_enter',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'mushGold',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }

    },
    {
        // Grand Cola
        id: 435,
        title: 'items[435].title',
        model: 'v_res_tt_can01',
        weight: 330,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.food',
        barPrice: 27000,
        description: 'items[435].description',
        stats: {
            hunger: -20,
            water: -20,
            hp: 50
        },
        useItem: {
            animationProp: 'ecola',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'ecola',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
        }
    },
    {
        // Зажигалка “Grand Club”
        id: 436,
        title: 'items[436].title',
        model: 'lux_prop_lighter_luxe',
        weight: 10,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[436].description'
    },
    {
        // Viola Blood
        id: 437,
        title: 'items[437].title',
        model: 'prop_drug_bottle',
        weight: 500,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.drugs',
        description: 'items[437].description',
        stats: {
            hunger: -20,
            water: -20,
            hp: 50
        },
        buffs: [
            {
                name: 'ViolaBloodDrunk',
                duration: 180000,
            }
        ],
        useItem: {
            animationProp: 'ecola',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'ecola',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
        }
    },
    {
        // Текила “Grand Club”
        id: 438,
        title: 'items[438].title',
        model: 'prop_tequila_bottle',
        weight: 550,
        width: 1,
        height: 2,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.alcohol',
        materials: 5,
        description: 'items[438].description',
        stats: {
            hunger: -10,
            water: 20,
            hp: 7
        },
        buffs: [
            {
                name: 'Drunk',
                duration: 300 * 1000
            }
        ],
        useItem: {
            animationProp: 'wine_bottle',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'wine_bottle',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,

            seasonPassTriggers: ['task_drink_alcohol']
        }
    },
    {
        // Играбельная гитара
        id: 439,
        title: 'items[439].title',
        model: 'prop_acc_guitar_01',
        weight: 700,
        width: 2,
        height: 4,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[439].description'
    },
    {
        // Играбельный барабан
        id: 440,
        title: 'items[440].title',
        model: 'prop_bongos_01',
        weight: 500,
        width: 2,
        height: 2,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[440].description'
    },
    {
        // План банка Fleeca
        id: 441,
        title: 'items[441].title',
        model: 'h4_prop_h4_files_paper_01a',
        weight: 200,
        width: 2,
        height: 2,
        takeable: true,
        canSeize: true,
        tag: 'items.tags.documents',
        description: 'items[441].description'
    },
    {
        // Кабель электронных дверей
        id: 442,
        title: 'items[442].title',
        model: 'prop_ld_cable_tie_01',
        weight: 100,
        width: 1,
        height: 1,
        canSeize: true,
        tag: 'items.tags.facilities',
        description: 'items[442].description'
    },
    {
        // Дрель 1500w
        id: 443,
        title: 'items[443].title',
        model: 'ch_prop_heist_drill_bag_01a',
        boxItemId: 562,
        materialType: 'blue',
        materials: 2000,
        weight: 8000,
        width: 4,
        height: 1,
        takeable: true,
        dropOnDeath: true,
        canSeize: true,
        tag: 'items.tags.facilities',
        description: 'items[443].description'
    },
    {
        // Ключ карта Fleeca
        id: 444,
        title: 'items[444].title',
        model: 'h4_prop_h4_securitycard_01a',
        weight: 10,
        width: 1,
        height: 1,
        takeable: true,
        canSeize: true,
        tag: 'items.tags.facilities',
        description: 'items[444].description'
    },
    {
        // Термитная установка
        id: 445,
        title: 'items[445].title',
        model: 'hei_prop_heist_thermite',
        weight: 1000,
        width: 1,
        height: 2,
        takeable: true,
        canSeize: true,
        tag: 'items.tags.facilities',
        description: 'items[445].description'
    },
    {
        // Золотой слиток
        id: 446,
        title: 'items[446].title',
        model: 'hei_prop_heist_gold_bar',
        weight: 1500,
        width: 2,
        height: 2,
        coins: 25,
        takeable: true,
        dropOnDeath: true,
        dropOnQuit: true,
        canSeize: true,
        canDrag: false,
        canDragCop: false,
        tag: 'items.tags.jewelry',
        description: 'items[446].description'
    },
    {
        // Череп с бриллиантами
        id: 447,
        title: 'items[447].title',
        model: 'vw_prop_casino_art_skull_03a',
        weight: 500,
        width: 1,
        height: 1,
        takeable: true,
        dropOnDeath: true,
        dropOnQuit: true,
        canSeize: true,
        canDrag: false,
        canDragCop: false,
        tag: 'items.tags.jewelry',
        description: 'items[447].description'
    },
    {
        // Статуэтка пумы
        id: 448,
        title: 'items[448].title',
        model: 'vw_prop_casino_art_panther_01c',
        weight: 350,
        width: 2,
        height: 3,
        takeable: true,
        dropOnDeath: true,
        dropOnQuit: true,
        canSeize: true,
        canDrag: false,
        canDragCop: false,
        tag: 'items.tags.jewelry',
        description: 'items[448].description'
    },
    {
        // Древняя тарелка
        id: 449,
        title: 'items[449].title',
        model: 'apa_mp_h_acc_dec_plate_01',
        weight: 170,
        width: 2,
        height: 2,
        takeable: true,
        dropOnDeath: true,
        dropOnQuit: true,
        canSeize: true,
        canDrag: false,
        canDragCop: false,
        tag: 'items.tags.jewelry',
        description: 'items[449].description'
    },
    {
        // Коллекция швейцарских часов
        id: 450,
        title: 'items[450].title',
        model: 'ex_office_swag_jewelwatch2',
        weight: 600,
        width: 4,
        height: 3,
        takeable: true,
        dropOnDeath: true,
        dropOnQuit: true,
        canSeize: true,
        canDrag: false,
        canDragCop: false,
        tag: 'items.tags.jewelry',
        description: 'items[450].description'
    },
    {
        // Полицейский дрон
        id: 451,
        title: 'items[451].title',
        model: 'ch_prop_casino_drone_02a',
        boxItemId: 555,
        materialType: 'blue',
        materials: 2000,
        weight: 1200,
        width: 2,
        height: 2,
        takeable: true,
        hasSerial: true,
        canSeize: true,
        tag: 'items.tags.facilities',
        description: 'items[451].description'
    },
    {
        // Гражданский дрон
        id: 452,
        title: 'items[452].title',
        model: 'xs_prop_arena_drone_02',
        weight: 1000,
        width: 2,
        height: 2,
        takeable: true,
        hasSerial: true,
        tag: 'items.tags.facilities',
        materials: 7000,
        description: 'items[452].description'
    },
    {
        // Тяжелый бронежилет Sheriff
        id: 453,
        title: 'items[453].title',
        model: 'prop_armour_pickup',
        boxItemId: 550,
        hasSerial: true,
        weight: 4000,
        width: 3,
        height: 3,
        usable: true,
        materials: 30,
        materialType: 'blue',
        armour: 100,
        tag: 'items.tags.havyArmor',
        canSeize: true,
        description: 'items[453].description',
        canUtilize: true
    },
    {
        // Тяжелый бронежилет LSPD
        id: 454,
        title: 'items[454].title',
        model: 'prop_armour_pickup',
        boxItemId: 550,
        hasSerial: true,
        weight: 4000,
        width: 3,
        height: 3,
        usable: true,
        materials: 30,
        materialType: 'blue',
        armour: 100,
        tag: 'items.tags.havyArmor',
        canSeize: true,
        description: 'items[454].description',
        canUtilize: true
    },
    {
        // Тяжелый бронежилет FIB
        id: 455,
        title: 'items[455].title',
        model: 'prop_armour_pickup',
        boxItemId: 550,
        hasSerial: true,
        weight: 4000,
        width: 3,
        height: 3,
        usable: true,
        materials: 30,
        materialType: 'blue',
        armour: 100,
        tag: 'items.tags.havyArmor',
        canSeize: true,
        description: 'items[455].description',
        canUtilize: true
    },
    {
        // Легкая бронеразгрузка Sheriff
        id: 456,
        title: 'items[456].title',
        model: 'prop_armour_pickup',
        boxItemId: 551,
        hasSerial: true,
        weight: 2000,
        width: 3,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[456].description',
        canUtilize: true
    },
    {
        // Легкая бронеразгрузка LSPD
        id: 457,
        title: 'items[457].title',
        model: 'prop_armour_pickup',
        boxItemId: 551,
        hasSerial: true,
        weight: 2000,
        width: 3,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[457].description',
        canUtilize: true
    },
    {
        // Легкая бронеразгрузка FIB
        id: 458,
        title: 'items[458].title',
        model: 'prop_armour_pickup',
        boxItemId: 551,
        hasSerial: true,
        weight: 2000,
        width: 3,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[458].description',
        canUtilize: true
    },
    {
        // Легкая бронеразгрузка Sheriff
        id: 459,
        title: 'items[459].title',
        model: 'prop_armour_pickup',
        boxItemId: 551,
        hasSerial: true,
        weight: 2000,
        width: 3,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[459].description',
        canUtilize: true
    },
    {
        // Легкая бронеразгрузка LSPD
        id: 460,
        title: 'items[460].title',
        model: 'prop_armour_pickup',
        boxItemId: 551,
        hasSerial: true,
        weight: 2000,
        width: 3,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[460].description',
        canUtilize: true
    },
    {
        // Легкая бронеразгрузка FIB
        id: 461,
        title: 'items[461].title',
        model: 'prop_armour_pickup',
        hasSerial: true,
        weight: 2000,
        width: 3,
        height: 3,
        usable: true,
        materials: 15,
        materialType: 'blue',
        armour: 50,
        tag: 'items.tags.ammunition',
        canSeize: true,
        description: 'items[461].description',
        canUtilize: true
    },
    {
        // Тяжелая бронеразгрузка FIB
        id: 462,
        title: 'items[462].title',
        model: 'prop_bodyarmour_02',
        boxItemId: 552,
        hasSerial: true,
        weight: 4000,
        width: 3,
        height: 3,
        usable: true,
        materials: 30,
        materialType: 'blue',
        armour: 100,
        tag: 'items.tags.havyArmor',
        canSeize: true,
        description: 'items[462].description',
        canUtilize: true
    },
    {
        // Тяжелая бронеразгрузка LSPD
        id: 463,
        title: 'items[463].title',
        model: 'prop_bodyarmour_02',
        boxItemId: 552,
        hasSerial: true,
        weight: 4000,
        width: 3,
        height: 3,
        usable: true,
        materials: 30,
        materialType: 'blue',
        armour: 100,
        tag: 'items.tags.havyArmor',
        canSeize: true,
        description: 'items[463].description',
        canUtilize: true
    },
    {
        // Тяжелая бронеразгрузка SANG
        id: 464,
        title: 'items[464].title',
        model: 'prop_bodyarmour_02',
        boxItemId: 552,
        hasSerial: true,
        weight: 4000,
        width: 3,
        height: 3,
        usable: true,
        materials: 30,
        materialType: 'blue',
        armour: 100,
        tag: 'items.tags.havyArmor',
        canSeize: true,
        description: 'items[464].description',
        canUtilize: true
    },
    {
        // Тяжелая бронеразгрузка Sheriff
        id: 465,
        title: 'items[465].title',
        model: 'prop_bodyarmour_02',
        boxItemId: 552,
        hasSerial: true,
        weight: 4000,
        width: 3,
        height: 3,
        usable: true,
        materials: 30,
        materialType: 'blue',
        armour: 100,
        tag: 'items.tags.havyArmor',
        canSeize: true,
        description: 'items[465].description',
        canUtilize: true
    },
    {
        // Сосновое бревно
        id: 466,
        title: 'items[466].title',
        model: 'mj_wood_pile_qnx',
        weight: 860,
        boxItemId: 698,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[466].description'
    },
    {
        // Дубовое бревно
        id: 467,
        title: 'items[467].title',
        model: 'mj_wood_pile_qnx',
        weight: 1020,
        boxItemId: 699,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[467].description'
    },
    {
        // Березовое бревно
        id: 468,
        title: 'items[468].title',
        model: 'mj_wood_pile_qnx',
        weight: 870,
        boxItemId: 700,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[468].description'
    },
    {
        // Кленовое бревно
        id: 469,
        title: 'items[469].title',
        model: 'mj_wood_pile_qnx',
        weight: 860,
        boxItemId: 701,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[469].description'
    },
    {
        // Золотая шишка
        id: 470,
        title: 'items[470].title',
        model: 'v_res_tre_banana',
        weight: 90,
        width: 1,
        height: 1,
        coins: 25,
        tag: 'items.tags.misc',
        description: 'items[470].description'
    },
    {
        // Резиновая курица
        id: 471,
        title: 'items[471].title',
        model: 'vw_prop_toy_sculpture_01a',
        weight: 200,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        tag: 'items.tags.misc',
        description: 'items[471].description'
    },
    {
        // Микро SMG
        id: 472,
        title: 'items[472].title',
        model: 'w_sb_microsmg',
        weight: 1400,
        width: 3,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 254,
        materialType: 'green',
        materials: 20,
        hash: 'weapon_microsmg',
        isMelee: false,
        limit: 3000,
        boxItemId: 826,
        description: 'items[472].description'
    },
    {
        // Кошелёк
        id: 473,
        title: 'items[473].title',
        model: 'prop_ld_wallet_pickup',
        weight: 300,
        width: 2,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[473].description',
        canSeize: false
    },
    {
        // Автомобильный номер
        id: 474,
        title: 'items[474].title',
        model: 'p_num_plate_01',
        weight: 500,
        width: 2,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[474].description',
        canPickupToBag: false
    },
    {
        // Симкарта
        id: 475,
        title: 'items[475].title',
        model: 'mj_qnx_simcard',
        weight: 10,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[475].description'
    },
    {
        // Ящик с боеприпасами 9x19mm
        id: 476,
        title: 'items[476].title',
        model: 'mj_ammo9x19_box',
        weight: 500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[476].description',
        isSupplyBox: true
    },
    {
        // Ящик с боеприпасами 12ga Buckshots
        id: 477,
        title: 'items[477].title',
        model: 'mj_ammo12gaB_box',
        weight: 500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[477].description',
        isSupplyBox: true
    },
    {
        // Ящик с боеприпасами 12ga Rifled
        id: 478,
        title: 'items[478].title',
        model: 'mj_ammo12gaR_box',
        weight: 500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[478].description',
        isSupplyBox: true
    },
    {
        // Ящик с боеприпасами .045 ACP
        id: 479,
        title: 'items[479].title',
        model: 'mj_ammo45acp_box',
        weight: 500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[479].description',
        isSupplyBox: true
    },
    {
        // Ящик с боеприпасами .50 BMG
        id: 480,
        title: 'items[480].title',
        model: 'mj_ammo50bmg_box',
        weight: 500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[480].description',
        isSupplyBox: true
    },
    {
        // Ящик с боеприпасами .357 Magnum
        id: 481,
        title: 'items[481].title',
        model: 'mj_ammo357_box',
        weight: 500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[481].description',
        isSupplyBox: true
    },
    {
        // Ящик с боеприпасами 5.56x45mm
        id: 482,
        title: 'items[482].title',
        model: 'mj_ammo556_box',
        weight: 500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[482].description',
        isSupplyBox: true
    },
    {
        // Ящик с боеприпасами 7.62x39mm
        id: 483,
        title: 'items[483].title',
        model: 'mj_ammo762_box',
        weight: 500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[483].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Помповый дробовик Mk2»
        id: 484,
        title: 'items[484].title',
        model: 'mj_12garifl_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[484].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием
        id: 485,
        title: 'items[485].title',
        model: 'mj_45acp_box',
        weight: 20000,
        width: 4,
        height: 4,
        tag: 'items.tags.box',
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[485].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Улучшенная винтовка»
        id: 486,
        title: 'items[486].title',
        model: 'mj_advrifle_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[486].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Штурмовая винтовка Mk2»
        id: 487,
        title: 'items[487].title',
        model: 'mj_ak47_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[487].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Assault SMG»
        id: 488,
        title: 'items[488].title',
        model: 'mj_assaultsmg_box',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[488].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием
        id: 489,
        title: 'items[489].title',
        model: 'mj_automriflemk2_box',
        weight: 20000,
        width: 4,
        height: 4,
        tag: 'items.tags.box',
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[489].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Винтовка Буллпап Mk2»
        id: 490,
        title: 'items[490].title',
        model: 'mj_bullpupmk2_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[490].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Карабинная винтовка»
        id: 491,
        title: 'items[491].title',
        model: 'mj_carriffle556_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[491].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Карабинная винтовка Mk2»
        id: 492,
        title: 'items[492].title',
        model: 'mj_carriflemk2_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[492].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Тяжелый дробовик»
        id: 493,
        title: 'items[493].title',
        model: 'mj_heavy_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[493].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Винтовка Marksman Mk2»
        id: 494,
        title: 'items[494].title',
        model: 'mj_marksmanmk2_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[494].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Пулемёт Калашникова»
        id: 495,
        title: 'items[495].title',
        model: 'mj_mgk_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[495].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Военная винтовка»
        id: 496,
        title: 'items[496].title',
        model: 'mj_military_rifle_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[496].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Боевой PDW»
        id: 497,
        title: 'items[497].title',
        model: 'mj_pdw_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[497].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Штурмовой пистолет»
        id: 498,
        title: 'items[498].title',
        model: 'mj_pistol50_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[498].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «SMG Mk2»
        id: 499,
        title: 'items[499].title',
        model: 'mj_smgmk2_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[499].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Специальный карабин Mk2»
        id: 500,
        title: 'items[500].title',
        model: 'mj_speccarmk2_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[500].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Пистолет-пулемёт Томпсона»
        id: 501,
        title: 'items[501].title',
        model: 'mj_thomp_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[501].description',
        isSupplyBox: true
    },
    {
        // Ящик с вакциной
        id: 502,
        title: 'items[502].title',
        model: 'mj_vaction_box',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[502].description',
        isSupplyBox: true
    },
    {
        // Ящик с Кодеиновыми таблетками
        id: 503,
        title: 'items[503].title',
        model: 'mj_codein_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[503].description',
        isSupplyBox: true
    },
    {
        // Ящик с Эпинефрином
        id: 504,
        title: 'items[504].title',
        model: 'mj_epinif_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[504].description',
        isSupplyBox: true
    },
    {
        // Ящик с Азитромицином
        id: 505,
        title: 'items[505].title',
        model: 'mj_az_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[505].description',
        isSupplyBox: true
    },
    {
        // Ящик с дефибриляторами
        id: 506,
        title: 'items[506].title',
        model: 'mj_box_defir',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[506].description',
        isSupplyBox: true
    },
    {
        // Ящик с Активированным углём
        id: 507,
        title: 'items[507].title',
        model: 'mj_acchar_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[507].description',
        isSupplyBox: true
    },
    {
        // Ключ от кладовки
        id: 508,
        title: 'items[508].title',
        model: 'prop_cuff_keys_01',
        weight: 14,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        forWallet: true,
        canDragWallet: false,
        forKeychain: true,
        description: 'items[508].description'
    },
    {
        // Ацетон
        id: 509,
        title: 'items[509].title',
        model: 'bkr_prop_meth_acetone',
        weight: 1400,
        width: 2,
        height: 2,
        tag: 'items.tags.drugMaterials',
        canDrag: true,
        canDragCop: true,
        description: 'items[509].description'
    },
    {
        // Пропиленгликоль
        id: 510,
        title: 'items[510].title',
        model: 'bkr_prop_meth_ammonia',
        weight: 1600,
        width: 2,
        height: 2,
        tag: 'items.tags.drugMaterials',
        canDrag: true,
        canDragCop: true,
        description: 'items[510].description'
    },
    {
        // Контейнер с Blue
        id: 511,
        title: 'items[511].title',
        model: 'bkr_prop_meth_bigbag_01a',
        weight: 10000,
        width: 3,
        height: 3,
        // tag: 'items.tags.box',
        canHandItem: true,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[511].description'
    },
    {
        // Висмутовая кислота
        id: 512,
        title: 'items[512].title',
        model: 'bkr_prop_meth_hcacid',
        weight: 1300,
        width: 3,
        height: 3,
        tag: 'items.tags.drugMaterials',
        canDrag: true,
        canDragCop: true,
        description: 'items[512].description'
    },
    {
        // Ортофосфорная кислота
        id: 513,
        title: 'items[513].title',
        model: 'mj_box_addon_mini_barrel_phosphoric',
        weight: 1700,
        width: 4,
        height: 5,
        tag: 'items.tags.drugMaterials',
        canDrag: true,
        canDragCop: true,
        description: 'items[513].description'
    },
    {
        // Коробка Псевдоэфедрина
        id: 514,
        title: 'items[514].title',
        model: 'bkr_prop_meth_pseudoephedrine',
        weight: 1200,
        width: 3,
        height: 3,
        tag: 'items.tags.drugMaterials',
        canDrag: true,
        canDragCop: true,
        description: 'items[514].description'
    },
    {
        // Бензонат натрия
        id: 515,
        title: 'items[515].title',
        model: 'bkr_prop_meth_sacid',
        weight: 750,
        width: 2,
        height: 2,
        tag: 'items.tags.drugMaterials',
        canDrag: true,
        canDragCop: true,
        description: 'items[515].description'
    },
    {
        // Пероксид водорода
        id: 516,
        title: 'items[516].title',
        model: 'bkr_prop_meth_sodium',
        weight: 450,
        width: 1,
        height: 2,
        tag: 'items.tags.drugMaterials',
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[516].description'
    },
    {
        // Толуол
        id: 517,
        title: 'items[517].title',
        model: 'bkr_prop_meth_toulene',
        weight: 800,
        width: 2,
        height: 2,
        tag: 'items.tags.drugMaterials',
        canDrag: true,
        canDragCop: true,
        description: 'items[517].description'
    },
    {
        // Коробка сигар «Vintage»
        id: 518,
        title: 'items[518].title',
        model: 'ex_prop_exec_cigar_01',
        weight: 500,
        width: 2,
        height: 2,
        tag: 'items.tags.grandClub',
        usable: true,
        materials: 5,
        shopCount: 12,
        stack: 12,
        description: 'items[518].description',
        buffs: [
            {
                name: 'Drunk',
                duration: 5000
            }
        ],
        stats: {
            hp: -1
        },
        useItem: {
            overrideEffect: 'UseItemCig',

            animationProp: 'cigarette',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke',

            usingSound: 'cigarette',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            useFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_smoke',
                boneIndex: 20279,
                timeout: 10000
            },

            processFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_exhale_mouth',
                boneIndex: 20279,
                timeout: 10000
            },

            processDelay: 3000,

            seasonPassTriggers: ['task_use_cig'],

            duration: 28000,
        }
    },
    {
        // Grand Coffee
        id: 519,
        title: 'items[519].title',
        model: 'prop_fib_coffee',
        weight: 300,
        width: 1,
        height: 2,
        tag: 'items.tags.food',
        usable: true,
        barPrice: 23000,
        // food: {
        //     hunger: 5,
        //     water: 35,
        //     hp: 0
        // },
        description: 'items[519].description',
        stats: {
            hunger: 6.5,
            water: 20,
        },
        useItem: {
            animationProp: 'coffee',
            animationDict: 'amb@world_human_drinking@coffee@male@idle_a',
            animationName: 'idle_a',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'coffee',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',
        }
    },
    {
        // Мороженое «Свежесть Лета»
        id: 520,
        title: 'items[520].title',
        model: 'prop_food_coffee',
        weight: 300,
        width: 1,
        height: 2,
        tag: 'items.tags.food',
        usable: true,
        barPrice: 10000,
        // food: {
        //     hunger: 10,
        //     water: 15,
        //     hp: 5
        // },
        description: 'items[520].description',
        stats: {
            hunger: 10,
            water: 15,
            hp: 5
        },
        useItem: {
            animationProp: 'cocktail',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'cocktail',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000
        }
    },
    {
        // FABULA
        id: 521,
        title: 'items[521].title',
        model: 'prop_pool_ball_01',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.grandClub',
        usable: true,
        canSeize: true,
        description: 'items[521].description',
        buffs: [
            {
                name: 'ViolaBloodDrunk'
            }
        ],
        stats: {
            hunger: -10,
            water: -10,
            hp: +50
        },
        useItem: {
            overrideEffect: 'UseItemVape',

            animationProp: 'vape',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',
            animationFlag: 50,

            usingSound: 'vape',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            processFx: {
                fxLib: 'core',
                fxName: 'exp_grd_bzgas_smoke',
                boneIndex: 20279,
                timeout: 5000
            },

            processDelay: 3500,

            duration: 6000
        }
    },
    {
        // D1E HARD
        id: 522,
        title: 'items[522].title',
        model: 'w_am_flare',
        weight: 400,
        width: 1,
        height: 2,
        tag: 'items.tags.grandClub',
        usable: true,
        canSeize: true,
        description: 'items[522].description',
        buffs: [
            {
                name: 'ViolaBloodDrunk'
            }
        ],
        stats: {
            hunger: -10,
            water: -10,
            hp: +50
        },
        useItem: {
            overrideEffect: 'UseItemVape',

            animationProp: 'vape',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',
            animationFlag: 50,

            usingSound: 'vape',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            processFx: {
                fxLib: 'core',
                fxName: 'exp_grd_bzgas_smoke',
                boneIndex: 20279,
                timeout: 5000
            },

            processDelay: 3500,

            duration: 6000
        }
    },
    {
        // PaNa CEA
        id: 523,
        title: 'items[523].title',
        model: 'w_am_flare',
        weight: 400,
        width: 1,
        height: 2,
        tag: 'items.tags.grandClub',
        usable: true,
        canSeize: true,
        description: 'items[523].description',
        buffs: [
            {
                name: 'ViolaBloodDrunk'
            }
        ],
        stats: {
            hunger: -10,
            water: -10,
            hp: +50
        },
        useItem: {
            overrideEffect: 'UseItemVape',

            animationProp: 'vape',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',
            animationFlag: 50,

            usingSound: 'vape',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            processFx: {
                fxLib: 'core',
                fxName: 'exp_grd_bzgas_smoke',
                boneIndex: 20279,
                timeout: 5000
            },

            processDelay: 3500,

            duration: 6000
        }
    },
    {
        // Neuro Biotin X
        id: 524,
        title: 'items[524].title',
        model: 'w_am_flare',
        weight: 400,
        width: 1,
        height: 2,
        tag: 'items.tags.grandClub',
        usable: true,
        canSeize: true,
        description: 'items[524].description',
        buffs: [
            {
                name: 'ViolaBloodDrunk'
            }
        ],
        stats: {
            hunger: -10,
            water: -10,
            hp: +50
        },
        useItem: {
            overrideEffect: 'UseItemVape',

            animationProp: 'vape',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',
            animationFlag: 50,

            usingSound: 'vape',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            processFx: {
                fxLib: 'core',
                fxName: 'exp_grd_bzgas_smoke',
                boneIndex: 20279,
                timeout: 5000
            },

            processDelay: 3500,

            duration: 6000
        }
    },
    {
        // BIOLINK
        id: 525,
        title: 'items[525].title',
        model: 'prop_energy_drink',
        weight: 400,
        width: 1,
        height: 2,
        tag: 'items.tags.food',
        usable: true,
        description: 'items[525].description',
        stats: {
            hunger: 100,
            water: 100,
        },
        buffs: [
            {
                name: 'Biolink',
                duration: 5 * 60 * 60 * 1000
            }

        ],
        useItem: {
            animationProp: 'ecola',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'ecola',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000
        }
    },
    {
        // Винтажный Бинокль
        id: 526,
        title: 'items[526].title',
        model: 'prop_binoc_01',
        weight: 1000,
        width: 2,
        height: 1,
        tag: 'items.tags.jewelry',
        takeable: true,
        description: 'items[526].description'
    },
    {
        // Виски «TOXIC»
        id: 527,
        title: 'items[527].title',
        model: 'p_cs_bottle_01',
        weight: 900,
        width: 1,
        height: 2,
        tag: 'items.tags.food',
        usable: true,
        shopCount: 3,
        stack: 1,
        stats: {
            hunger: -15,
            water: 50,
            hp: -80
        },
        description: 'items[527].description',
        useItem: {
            animationProp: 'bottle1',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'bottle1',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,

            questTriggers: ['drink_alcohol'],
            seasonPassTriggers: ['task_drink_alcohol']
        }
    },
    {
        // Сигареты «Camel»
        id: 528,
        title: 'items[528].title',
        model: 'prop_cigar_pack_01',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        usable: true,
        description: 'items[528].description',
        stats: {
            hp: +5
        },
        useItem: {
            overrideEffect: 'UseItemCig',

            animationProp: 'cigarette',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke',

            usingSound: 'cigarette',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            useFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_smoke',
                boneIndex: 20279,
                timeout: 10000
            },

            processFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_exhale_mouth',
                boneIndex: 20279,
                timeout: 10000
            },

            processDelay: 3000,

            seasonPassTriggers: ['task_use_cig'],

            duration: 28000
        }
    },
    {
        // Old Cola
        id: 529,
        title: 'items[529].title',
        model: 'ng_proc_spraycan01b',
        weight: 350,
        width: 1,
        height: 2,
        tag: 'items.tags.food',
        usable: true,
        // food: {
        //     hunger: 0,
        //     water: 50,
        //     hp: -10
        // },
        description: 'items[529].description',
        stats: {
            hunger: 75,
            water: 25,
            hp: 10
        },
        useItem: {
            animationProp: 'ecola',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'ecola',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
        }
    },
    {
        // Старинная Аптечка
        id: 530,
        title: 'items[530].title',
        model: 'hei_prop_heist_deposit_box',
        weight: 750,
        width: 2,
        height: 1,
        stack: 10,
        tag: 'items.tags.medicine',
        usable: true,
        description: 'items[530].description',
        stats: {
            hp: 50
        },
        useItem: {
            overrideEffect: 'UseItemMedkit',

            animationProp: 'medkit',
            animationDict: 'majestic_animations_custom',
            animationName: 'healthpack',

            crawlingAnimationProp: 'medkit',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_healthpack',

            usingSound: 'medkit_use',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            seasonPassTriggers: ['task_medkit_item'],

            meMessage: 'chat.events.useMedicalKit',

            duration: 4500
        }
    },
    {
        // Старинный Ланч-Бокс
        id: 531,
        title: 'items[531].title',
        model: 'prop_food_napkin_02',
        weight: 2000,
        width: 2,
        height: 2,
        tag: 'items.tags.food',
        usable: true,
        shopCount: 2,
        stack: 2,
        stats: {
            hunger: 75,
            water: 25,
            hp: 10
        },
        description: 'items[531].description',
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Lite Beer
        id: 532,
        title: 'items[532].title',
        model: 'ng_proc_spraycan01b',
        weight: 350,
        width: 1,
        height: 2,
        tag: 'items.tags.food',
        usable: true,
        stack: 10,
        // food: {
        //     hunger: 0,
        //     water: 25,
        //     hp: 0
        // },
        description: 'items[532].description',
        stats: {
            water: 25,
        },
        useItem: {
            animationProp: 'ecola',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'ecola',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000,
        }
    },
    {
        // Бабочка в Банке
        id: 533,
        title: 'items[533].title',
        model: 'v_res_foodjarc',
        weight: 1000,
        width: 2,
        height: 2,
        stack: 10,
        tag: 'items.tags.jewelry',
        description: 'items[533].description'
    },
    {
        // Древний Манускрипт
        id: 534,
        title: 'items[534].title',
        model: 'v_res_fa_book01',
        weight: 3000,
        width: 2,
        height: 2,
        stack: 10,
        tag: 'items.tags.jewelry',
        description: 'items[534].description'
    },
    {
        // Монета Хаоса
        id: 535,
        title: 'items[535].title',
        model: 'hei_heist_acc_box_trinket_02',
        weight: 50,
        width: 1,
        height: 1,
        tag: 'items.tags.grandClub',
        stack: 50,
        forWallet: true,
        canDragWallet: false,
        description: 'items[535].description'
    },
    {
        // Сломанный Блок Памяти
        id: 536,
        title: 'items[536].title',
        model: 'prop_ld_hdd_01',
        weight: 250,
        width: 1,
        height: 1,
        stack: 10,
        tag: 'items.tags.facilities',
        description: 'items[536].description'
    },
    {
        // Китайское Молоко
        id: 537,
        title: 'items[537].title',
        model: 'prop_cs_milk_01',
        weight: 300,
        width: 1,
        height: 1,
        tag: 'items.tags.food',
        stack: 10,
        usable: true,
        // food: {
        //     hunger: 5,
        //     water: 25,
        //     hp: 0
        // },
        description: 'items[537].description',
        stats: {
            hunger: -15,
            water: -15,
            hp: 50
        },
        useItem: {
            animationProp: 'milk',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'milk',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000
        }
    },
    {
        // Неприличный Вуайеризм
        id: 538,
        title: 'items[538].title',
        model: 'prop_bottle_cognac',
        weight: 1000,
        width: 1,
        height: 2,
        tag: 'items.tags.grandClub',
        usable: true,
        shopCount: 3,
        stack: 3,
        stats: {
            hunger: 10,
            water: 25,
            hp: +15
        },
        useItem: {
            animationProp: 'vodka_bottle',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'vodka_bottle',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000
        },
        description: 'items[538].description'
    },
    {
        // Сигареты «Grave»
        id: 539,
        title: 'items[539].title',
        model: 'p_cigar_pack_02_s',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        usable: true,
        fastSlot: true,
        shopCount: 20,
        stack: 20,
        description: 'items[539].description',
        stats: {
            hp: -1
        },
        useItem: {
            overrideEffect: 'UseItemCig',

            animationProp: 'cigarette',
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke',

            usingSound: 'cigarette',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            useFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_smoke',
                boneIndex: 20279,
                timeout: 10000
            },

            processFx: {
                fxLib: 'cut_bigscr',
                fxName: 'cs_bigscr_cig_exhale_mouth',
                boneIndex: 20279,
                timeout: 10000
            },

            processDelay: 3000,

            seasonPassTriggers: ['task_use_cig'],

            duration: 28000
        }
    },
    {
        // Чип с криптовалютой
        id: 540,
        title: 'items[540].title',
        model: 'hei_prop_hei_id_bio',
        weight: 120,
        width: 2,
        height: 1,
        tag: 'items.tags.facilities',
        stack: 100,
        forWallet: true,
        canDragWallet: false,
        description: 'items[540].description'
    },
    {
        // Сверх прочный контейнер
        id: 541,
        title: 'items[541].title',
        model: 'ex_prop_adv_case',
        weight: 15000,
        width: 4,
        height: 3,
        tag: 'items.tags.facilities',
        usable: true,
        description: 'items[541].description'
    },
    {
        // Секретные документы
        id: 542,
        title: 'items[542].title',
        model: 'prop_cs_cashenvelope',
        weight: 100,
        width: 2,
        height: 2,
        tag: 'items.tags.documents',
        usable: true,
        description: 'items[542].description'
    },
    {
        // Хранилище данных - 16 ТБ
        id: 543,
        title: 'items[543].title',
        model: 'prop_ld_hdd_01',
        weight: 200,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        usable: true,
        description: 'items[543].description'
    },
    {
        // Букет красно-белых калл
        id: 544,
        title: 'items[544].title',
        model: 'mj_qnx_flowers_01',
        weight: 200,
        width: 1,
        height: 1,
        tag: 'items.tags.plant',
        takeable: true,
        description: 'items[544].description'
    },
    {
        // Букет желто-белых калл
        id: 545,
        title: 'items[545].title',
        model: 'mj_qnx_flowers_02',
        weight: 200,
        width: 1,
        height: 1,
        tag: 'items.tags.plant',
        takeable: true,
        description: 'items[545].description'
    },
    {
        // Букет оранжево-желтых калл
        id: 546,
        title: 'items[546].title',
        model: 'mj_qnx_flowers_03',
        weight: 200,
        width: 1,
        height: 1,
        tag: 'items.tags.plant',
        takeable: true,
        description: 'items[546].description'
    },
    {
        // Букет белых роз
        id: 547,
        title: 'items[547].title',
        model: 'mj_qnx_flowers_04',
        weight: 200,
        width: 1,
        height: 1,
        tag: 'items.tags.plant',
        takeable: true,
        description: 'items[547].description'
    },
    {
        // Букет розовых роз
        id: 548,
        title: 'items[548].title',
        model: 'mj_qnx_flowers_05',
        weight: 200,
        width: 1,
        height: 1,
        tag: 'items.tags.plant',
        takeable: true,
        description: 'items[548].description'
    },
    {
        // Ящик с «Легкий бронежилет»
        id: 549,
        title: 'items[549].title',
        model: 'mj_light_body_armor',
        weight: 2000,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[549].description',
        armorBox: true,
        isSupplyBox: true
    },
    {
        // Ящик с «Тяжелый бронежилет»
        id: 550,
        title: 'items[550].title',
        model: 'mj_heavy_body_armor',
        weight: 4000,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[550].description',
        armorBox: true,
        isSupplyBox: true
    },
    {
        // Ящик с «Легкая бронеразгрузка»
        id: 551,
        title: 'items[551].title',
        model: 'mj_light_unloading',
        weight: 2000,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[551].description',
        armorBox: true,
        isSupplyBox: true
    },
    {
        // Ящик с «Тяжелая бронеразгрузка»
        id: 552,
        title: 'items[552].title',
        model: 'mj_heavy_unloading',
        weight: 4000,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[552].description',
        armorBox: true,
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Снайперская винтовка»
        id: 553,
        title: 'items[553].title',
        model: 'mj_sniper_rifle_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[553].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Боевой дробовик II»
        id: 554,
        title: 'items[554].title',
        model: 'mj_pump_box_shotgunmk2',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[554].description',
        isSupplyBox: true
    },
    {
        // Ящик с полицейским дроном
        id: 555,
        title: 'items[555].title',
        model: 'mj_police_drons',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[555].description',
        isSupplyBox: true
    },
    {
        // Ящик с ИРП Армии США
        id: 556,
        title: 'items[556].title',
        model: 'mj_mre_pack_box',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[556].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Тяжелая снайперская винтовка»
        id: 557,
        title: 'items[557].title',
        model: 'mj_heavy_sniper_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[557].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Тяжелая снайперская винтовка Mk2»
        id: 558,
        title: 'items[558].title',
        model: 'mj_heavy_sniper_mk2_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[558].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Штурмовая винтовка»
        id: 559,
        title: 'items[559].title',
        model: 'mj_assault_box_rifle',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[559].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Револьвер»
        id: 560,
        title: 'items[560].title',
        model: 'mj_revolver_box_pistol',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[560].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Мини SMG»
        id: 561,
        title: 'items[561].title',
        model: 'mj_mini_box_smg',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[561].description',
        isSupplyBox: true
    },
    {
        // Ящик с Дрель 1500w
        id: 562,
        title: 'items[562].title',
        model: 'mj_drills_box',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[562].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Ручной пулемет»
        id: 563,
        title: 'items[563].title',
        model: 'mj_combat_mg_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[563].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Ручной пулемет Mk2»
        id: 564,
        title: 'items[564].title',
        model: 'mj_combat_mg_mk2_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[564].description',
        isSupplyBox: true
    },
    {
        // Ящик с наручниками
        id: 565,
        title: 'items[565].title',
        model: 'mj_cuffs_box',
        weight: 500,
        width: 2,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[565].description',
        isSupplyBox: true
    },
    {
        // Ящик с полицейскими дубинками
        id: 566,
        title: 'items[566].title',
        model: 'mj_batons_box',
        weight: 500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[566].description',
        isSupplyBox: true
    },
    {
        // Ящик с тазерами
        id: 567,
        title: 'items[567].title',
        model: 'mj_tasers_box',
        weight: 500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[567].description',
        isSupplyBox: true
    },
    {
        // Сертификат о военной службе
        id: 568,
        title: 'items[568].title',
        model: '',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.documents',
        canDrag: false,
        forWallet: true,
        canDragWallet: false,
        description: 'items[568].description',
        usable: true
    },
    {
        // Черви
        id: 569,
        title: 'items[569].title',
        model: 'mj_whorms_red',
        weight: 10,
        width: 1,
        height: 1,
        stack: 50,
        tag: 'items.tags.misc',
        description: 'items[569].description',
        usable: true,
        stats: {
            hunger: 2,
            poisonChance: 95
        },
        useItem: {
            animationProp: null,
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke_enter',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: null,
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_pills',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }
    },
    {
        // Ящик с «Просроченный стимулятор»
        id: 570,
        title: 'items[570].title',
        model: 'mj_qnx_old_enject_box',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[570].description',
        isSupplyBox: true
    },
    {
        // Просроченный стимулятор
        id: 571,
        title: 'items[571].title',
        model: 'mj_qnx_old_enject',
        boxItemId: 570,
        materialType: 'red',
        materials: 35,
        weight: 150,
        width: 1,
        height: 2,
        usable: true,
        canSeize: true,
        hasSerial: true,
        tag: 'items.tags.medicine',
        description: 'items[571].description'
    },
    {
        // Ящик с «Списанный дефибриллятор»
        id: 572,
        title: 'items[572].title',
        model: 'mj_qnx_old_defib_box',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[572].description',
        isSupplyBox: true
    },
    {
        // Списанный дефибриллятор
        id: 573,
        title: 'items[573].title',
        model: 'mj_qnx_old_defib',
        boxItemId: 572,
        materialType: 'red',
        materials: 800,
        weight: 500,
        width: 2,
        height: 2,
        hasSerial: true,
        canSeize: true,
        maxCondition: 50,
        tag: 'items.tags.facilities',
        description: 'items[573].description'
    },
    {
        // Железная руда
        id: 574,
        title: 'items[574].title',
        model: 'mj_metall_ore',
        weight: 50,
        boxItemId: 709,
        width: 1,
        height: 1,
        stack: 50,
        tag: 'items.tags.misc',
        description: 'items[574].description'
    },
    {
        // Серебрянная руда
        id: 575,
        title: 'items[575].title',
        model: 'mj_silver_ore',
        weight: 240,
        boxItemId: 710,
        width: 1,
        height: 1,
        stack: 50,
        tag: 'items.tags.misc',
        description: 'items[575].description'
    },
    {
        // Медная руда
        id: 576,
        title: 'items[576].title',
        model: 'mj_copper_ore',
        weight: 300,
        boxItemId: 711,
        width: 1,
        height: 1,
        stack: 50,
        tag: 'items.tags.misc',
        description: 'items[576].description'
    },
    {
        // Оловянная руда
        id: 577,
        title: 'items[577].title',
        model: 'mj_tin_ore',
        weight: 240,
        boxItemId: 712,
        width: 1,
        height: 1,
        stack: 50,
        tag: 'items.tags.misc',
        description: 'items[577].description'
    },
    {
        // Золотая руда
        id: 578,
        title: 'items[578].title',
        model: 'mj_gold_ore',
        weight: 360,
        boxItemId: 713,
        width: 1,
        height: 1,
        stack: 50,
        tag: 'items.tags.misc',
        description: 'items[578].description'
    },
    {
        // Металлоискатель
        id: 579,
        title: 'items[579].title',
        model: 'w_am_metaldetector',
        weight: 1000,
        width: 3,
        height: 2,
        takeable: true,
        isTool: true,
        deathSafe: false,
        fastSlot: true,
        tag: 'items.tags.tools',
        description: 'items[579].description'
    },

    {
        // Ящик с запчастями
        id: 580,
        title: 'items[580].title',
        model: 'gr_prop_gr_rsply_crate03a',
        weight: 7500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        // stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[580].description'
    },

    {
        // Ящик с запчастями
        id: 581,
        title: 'items[581].title',
        model: 'mj_mail_box_wood',
        weight: 7500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        // stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[581].description'
    },

    {
        // Ящик с запчастями
        id: 582,
        title: 'items[582].title',
        model: 'mj_mail_box_paper',
        weight: 7500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        // stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[582].description'
    },

    {
        // Ящик с электроников
        id: 583,
        title: 'items[583].title',
        model: 'mj_mail_box_elect',
        weight: 5000,
        width: 3,
        height: 2,
        //tag: 'items.tags.box',
        // stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[583].description',
        contractBox: true
    },

    {
        // Ящик с запчастями
        id: 584,
        title: 'items[584].title',
        model: 'mj_mail_box_cart',
        weight: 7500,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        // stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[584].description'
    },
    {
        // Дымарь
        id: 585,
        title: 'items[585].title',
        model: 'prop_tool_drill',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        description: 'items[585].description'
    },
    {
        // Пасека
        id: 586,
        title: 'items[586].title',
        model: 'v_serv_abox_g3',
        weight: 5000,
        width: 2,
        height: 2,
        tag: 'items.tags.misc',
        description: 'items[586].description'
    },
    {
        // Пчелиная матка
        id: 587,
        title: 'items[587].title',
        model: 'prop_amb_beer_bottle',
        weight: 100,
        width: 1,
        height: 1,
        tag: 'items.tags.food',
        description: 'items[587].description'
    },
    {
        // Трутень
        id: 588,
        title: 'items[588].title',
        model: 'prop_amb_beer_bottle',
        weight: 90,
        width: 1,
        height: 1,
        tag: 'items.tags.food',
        description: 'items[588].description'
    },
    {
        // Медовые соты
        id: 589,
        title: 'items[589].title',
        model: 'prop_amb_beer_bottle',
        weight: 500,
        width: 1,
        height: 1,
        stack: 5,
        tag: 'items.tags.food',
        description: 'items[589].description'
    },

    {
        // GLOCK P80
        id: 590,
        title: 'items[590].title',
        model: 'w_pi_glockp80',
        boxItemId: 598,
        weight: 750,
        width: 1,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 254,
        materials: 20,
        hash: 'weapon_glockp80',
        isMelee: false,
        limit: 15000,
        description: 'items[590].description'
    },

    {
        // Snow Ball
        id: 591,
        title: 'items[591].title',
        model: 'w_ex_snowball',
        weight: 100,
        width: 1,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 591,
        materials: 20,
        hash: 'weapon_snowball',
        isMelee: true, // Нужно чтоб обойти ограничение на 1 лвл
        disableDropOnCapt: true,
        stack: 5,
        limit: 15000,
        description: 'items[591].description'
    },

    {
        // Dragy
        id: 592,
        title: 'items[592].title',
        model: 'mj_qnx_dragy',
        weight: 380,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[592].description'
    },
    {
        // Рождественская ёлка
        id: 593,
        title: 'items[593].title',
        model: 'mj_wood_pile_qnx',
        weight: 860,
        width: 1,
        height: 2,
        tag: 'items.tags.misc',
        description: 'items[593].description'
    },
    {
        // Танцевальная бомба
        id: 594,
        title: 'items[594].title',
        model: 'w_am_baseball',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
        stack: 3,
        tag: 'items.tags.ammunition',
        description: 'items[594].description'
    },
    {
        // Тяжелая винтовка
        id: 595,
        title: 'items[595].title',
        model: 'w_ar_heavyrifleh',
        boxItemId: 596,
        materialType: 'green',
        materials: 50,
        weight: 3100,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 39,
        hash: 'weapon_heavyrifle',
        isMelee: false,
        limit: 7500,
        description: 'items[595].description'
    },
    {
        // Ящик с оружием «Тяжёлая винтовка»
        id: 596,
        title: 'items[596].title',
        model: 'mj_advrifle_box',
        weight: 500,
        width: 4,
        height: 2,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[596].description',
        isSupplyBox: true
    },
    {
        // Машинка для бритья
        id: 597,
        title: 'items[597].title',
        model: 'v_serv_bs_razor',
        weight: 150,
        width: 1,
        height: 2,
        tag: 'items.tags.tools',
        description: 'items[597].description'
    },
    {
        // Ящик с оружием «GLOCK P80»
        id: 598,
        title: 'items[598].title',
        model: 'mj_pistol50_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[598].description',
        isSupplyBox: true
    },
    {
        // Сырое мясо кабана
        id: 599,
        title: 'items[599].title',
        model: 'v_ind_meatboxsml',
        weight: 500,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        stack: 3,
        tag: 'items.tags.food',
        description: 'items[599].description',
        stats: {
            hunger: 30,
            water: -15,
            poisonChance: 45
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Сырое мясо оленины
        id: 600,
        title: 'items[600].title',
        model: 'v_ind_meatboxsml',
        weight: 500,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        stack: 3,
        tag: 'items.tags.food',
        description: 'items[600].description',
        stats: {
            hunger: 35,
            water: -15,
            poisonChance: 20
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Сырое мясо кролика
        id: 601,
        title: 'items[601].title',
        model: 'v_ind_meatboxsml',
        weight: 200,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        stack: 3,
        tag: 'items.tags.food',
        description: 'items[601].description',
        stats: {
            hunger: 20,
            water: -20,
            poisonChance: 70
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Сырое мясо свинины
        id: 602,
        title: 'items[602].title',
        model: 'v_ind_meatboxsml',
        weight: 400,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        stack: 3,
        tag: 'items.tags.food',
        description: 'items[602].description',
        stats: {
            hunger: 40,
            water: -10,
            poisonChance: 90
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Сырое мясо говядины
        id: 603,
        title: 'items[603].title',
        model: 'v_ind_meatboxsml',
        weight: 500,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        stack: 3,
        tag: 'items.tags.food',
        description: 'items[603].description',
        stats: {
            hunger: 45,
            water: -10,
            poisonChance: 85
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Сырое мясо курицы
        id: 604,
        title: 'items[604].title',
        model: 'v_ind_meatboxsml',
        weight: 300,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        stack: 3,
        tag: 'items.tags.food',
        description: 'items[604].description',
        stats: {
            hunger: 25,
            water: -20,
            poisonChance: 95
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Жареное мясо кабана
        id: 605,
        title: 'items[605].title',
        model: 'v_ind_meatboxsml',
        weight: 250,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        tag: 'items.tags.food',
        description: 'items[605].description',
        stats: {
            hunger: 60,
            water: -20
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Жареное мясо оленины
        id: 606,
        title: 'items[606].title',
        model: 'v_ind_meatboxsml',
        weight: 250,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        tag: 'items.tags.food',
        description: 'items[606].description',
        stats: {
            hunger: 70,
            water: -20
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Жареное мясо кролика
        id: 607,
        title: 'items[607].title',
        model: 'v_ind_meatboxsml',
        weight: 100,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        tag: 'items.tags.food',
        description: 'items[607].description',
        stats: {
            hunger: 40,
            water: -25,
            poisonChance: 5
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Жареное мясо свинины
        id: 608,
        title: 'items[608].title',
        model: 'v_ind_meatboxsml',
        weight: 200,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        tag: 'items.tags.food',
        description: 'items[608].description',
        stats: {
            hunger: 80,
            water: -15,
            poisonChance: 3
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Жареное мясо говядины
        id: 609,
        title: 'items[609].title',
        model: 'v_ind_meatboxsml',
        weight: 250,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        tag: 'items.tags.food',
        description: 'items[609].description',
        stats: {
            hunger: 90,
            water: -15,
            poisonChance: 1
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Жареное мясо курицы
        id: 610,
        title: 'items[610].title',
        model: 'v_ind_meatboxsml',
        weight: 150,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        tag: 'items.tags.food',
        description: 'items[610].description',
        stats: {
            hunger: 50,
            water: -25,
            poisonChance: 5
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Синий маячёк
        id: 611,
        title: 'items[611].title',
        model: 'mj_air_lamp_blue',
        weight: 50,
        width: 1,
        height: 1,
        stack: 1,
        takeable: true,
        canSeize: true,
        tag: 'items.tags.facilities',
        description: 'items[611].description',
        usable: true
    },
    {
        // Зелёный маячёк
        id: 612,
        title: 'items[612].title',
        model: 'mj_air_lamp_green',
        weight: 50,
        width: 1,
        height: 1,
        stack: 1,
        takeable: true,
        canSeize: true,
        tag: 'items.tags.facilities',
        description: 'items[612].description',
        usable: true
    },
    {
        // Красный маячёк
        id: 613,
        title: 'items[613].title',
        model: 'mj_air_lamp_red',
        weight: 50,
        width: 1,
        height: 1,
        stack: 1,
        takeable: true,
        canSeize: true,
        tag: 'items.tags.facilities',
        description: 'items[613].description',
        usable: true
    },
    {
        // Кабель радио сигналов
        id: 614,
        title: 'items[614].title',
        model: 'lr_prop_carkey_fob',
        weight: 650,
        canSeize: true,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[614].description'
    },
    {
        // Граната
        id: 615,
        title: 'items[615].title',
        model: 'w_ex_grenadefrag',
        weight: 400,
        width: 1,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 615,
        materials: 20000,
        hash: 'weapon_grenade',
        stack: 10,
        limit: 100,
        description: 'items[615].description'
    },
    {
        // Слезоточивый газ
        id: 616,
        title: 'items[616].title',
        model: 'w_ex_grenadesmoke',
        weight: 600,
        width: 1,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 616,
        materials: 10000,
        hash: 'weapon_smokegrenade',
        stack: 10,
        limit: 100,
        description: 'items[616].description'
    },
    {
        // Коктейль Молотова
        id: 617,
        title: 'items[617].title',
        model: 'w_ex_molotov',
        weight: 500,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 617,
        materials: 10000,
        hash: 'weapon_molotov',
        stack: 10,
        limit: 100,
        description: 'items[617].description'
    },
    {
        // Самодельная бомба
        id: 618,
        title: 'items[618].title',
        model: 'w_ex_pipebomb',
        weight: 1000,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 618,
        materials: 10000,
        hash: 'weapon_pipebomb',
        stack: 10,
        limit: 100,
        description: 'items[618].description'
    },
    {
        // Миниган
        id: 619,
        title: 'items[619].title',
        model: 'prop_minigun_01',
        weight: 10000,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 39,
        materials: 50000,
        hash: 'weapon_minigun',
        limit: 10000,
        description: 'items[619].description'
    },
    {
        // Ордер на обыск транспорта
        id: 620,
        title: 'items[620].title',
        model: 'prop_passport_01',
        weight: 100,
        width: 2,
        height: 2,
        tag: 'items.tags.documents',
        description: 'items[620].description',
        canDrag: false,
        usable: true,
        canDragWallet: false,
        forWallet: true
    },
    {
        // Синяя старая книга
        id: 621,
        title: 'items[621].title',
        model: 'xm_prop_x17_book_bogdan',
        weight: 50,
        width: 1,
        height: 1,
        tag: 'items.tags.books',
        stack: 20,
        description: 'items[621].description'
    },
    {
        // Красная старая книга
        id: 622,
        title: 'items[622].title',
        model: 'v_res_fa_book04',
        weight: 50,
        width: 1,
        height: 1,
        tag: 'items.tags.books',
        stack: 20,
        description: 'items[622].description'
    },
    {
        // Кристаллизованная жемчужина инопланетянина
        id: 623,
        title: 'items[623].title',
        model: 'v_res_fa_crystal01',
        weight: 250,
        width: 1,
        height: 1,
        stack: 5,
        tag: 'items.tags.misc',
        description: 'items[623].description'
    },
    {
        // Призрачная рыба
        id: 624,
        title: 'items[624].title',
        model: 'ribka_tor5',
        weight: 500,
        width: 2,
        height: 1,
        tag: 'items.tags.misc',
        chance: 1,
        description: 'items[624].description',
        stack: 5
    },
    {
        // Адреналин
        id: 625,
        title: 'items[625].title',
        model: 'adrenalin_shell',
        boxItemId: 626,
        materialType: 'red',
        materials: 10,
        weight: 150,
        width: 1,
        height: 2,
        stack: 1,
        hasSerial: true,
        tag: 'items.tags.medicine',
        description: 'items[625].description'
    },
    {
        // Ящик с Адреналином
        id: 626,
        title: 'items[626].title',
        model: 'mj_epinif_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[626].description',
        isSupplyBox: true
    },
    {
        // Дефибриллятор Mk2
        id: 627,
        title: 'items[627].title',
        model: 'prop_ecg_01',
        boxItemId: 628,
        materialType: 'red',
        materials: 400,
        weight: 500,
        width: 2,
        height: 2,
        hasSerial: true,
        canSeize: true,
        maxCondition: 50,
        tag: 'items.tags.facilities',
        description: 'items[627].description'
    },
    {
        // Ящик с дефибриляторами Mk2
        id: 628,
        title: 'items[628].title',
        model: 'mj_box_defir',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[628].description'
    },
    {
        // Тактическая винтовка
        id: 629,
        title: 'items[629].title',
        model: 'w_ar_carbinerifle_reh',
        weight: 2500,
        width: 4,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 172,
        materialType: 'green',
        materials: 40,
        hash: 'weapon_tacticalrifle',
        isMelee: false,
        limit: 7500,
        boxItemId: 823,
        description: 'items[629].description'
    },
    {
        // Прецизионная винтовка
        id: 630,
        title: 'items[630].title',
        model: 'w_sr_precisionrifle_reh',
        weight: 4100,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 171,
        materials: 400,
        hash: 'weapon_precisionrifle',
        limit: 400,
        boxItemId: 824,
        description: 'items[630].description'
    },
    {
        // Строительные материалы 1 уровня
        id: 631,
        title: 'items[631].title',
        description: 'items[631].description',
        tag: 'items.tags.consumables',
        model: 'mj_box_upgr_wood_box',
        weight: 1400,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Строительные материалы 2 уровня
        id: 632,
        title: 'items[632].title',
        description: 'items[632].description',
        tag: 'items.tags.consumables',
        model: 'mj_box_upgr_wood_box',
        weight: 1500,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Строительные материалы 3 уровня
        id: 633,
        title: 'items[633].title',
        description: 'items[633].description',
        tag: 'items.tags.consumables',
        model: 'mj_box_upgr_wood_box',
        weight: 1600,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Строительное оборудование 1 уровня
        id: 634,
        title: 'items[634].title',
        description: 'items[634].description',
        tag: 'items.tags.consumables',
        model: 'prop_tool_box_04',
        weight: 1000,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Строительное оборудование 2 уровня
        id: 635,
        title: 'items[635].title',
        description: 'items[635].description',
        tag: 'items.tags.consumables',
        model: 'prop_tool_box_04',
        weight: 1100,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Строительное оборудование 3 уровня
        id: 636,
        title: 'items[636].title',
        description: 'items[636].description',
        tag: 'items.tags.consumables',
        model: 'prop_tool_box_04',
        weight: 1200,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Ткань 1 уровня
        id: 637,
        title: 'items[637].title',
        description: 'items[637].description',
        tag: 'items.tags.consumables',
        model: 'mj_box_upgr_cloth',
        weight: 100,
        width: 2,
        height: 2,
        stack: 20
    },
    {
        // Ткань 2 уровня
        id: 638,
        title: 'items[638].title',
        description: 'items[638].description',
        tag: 'items.tags.consumables',
        model: 'mj_tin_ore',
        weight: 100,
        width: 2,
        height: 2,
        stack: 20
    },
    {
        // Ткань 3 уровня
        id: 639,
        title: 'items[639].title',
        description: 'items[639].description',
        tag: 'items.tags.consumables',
        model: 'mj_tin_ore',
        weight: 100,
        width: 2,
        height: 2,
        stack: 20
    },

    {
        // Нитки 1 уровня
        id: 640,
        title: 'items[640].title',
        description: 'items[640].description',
        tag: 'items.tags.consumables',
        model: 'mj_thread_roll',
        weight: 5,
        width: 1,
        height: 2,
        stack: 20
    },
    {
        // Нитки 2 уровня
        id: 641,
        title: 'items[641].title',
        description: 'items[641].description',
        tag: 'items.tags.consumables',
        model: 'mj_thread_roll',
        weight: 5,
        width: 1,
        height: 2,
        stack: 20
    },
    {
        // Нитки 3 уровня
        id: 642,
        title: 'items[642].title',
        description: 'items[642].description',
        tag: 'items.tags.consumables',
        model: 'mj_thread_roll',
        weight: 5,
        width: 1,
        height: 2,
        stack: 20
    },

    {
        // Электронное оборудование 1 уровня
        id: 643,
        title: 'items[643].title',
        description: 'items[643].description',
        tag: 'items.tags.facilities',
        model: 'mj_box_addon_electric',
        weight: 600,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Электронное оборудование 2 уровня
        id: 644,
        title: 'items[644].title',
        description: 'items[644].description',
        tag: 'items.tags.facilities',
        model: 'mj_box_addon_electric',
        weight: 700,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Электронное оборудование 3 уровня
        id: 645,
        title: 'items[645].title',
        description: 'items[645].description',
        tag: 'items.tags.facilities',
        model: 'mj_box_addon_electric',
        weight: 800,
        width: 2,
        height: 2,
        stack: 1
    },

    {
        // Микроэлектроника 1 уровня
        id: 646,
        title: 'items[646].title',
        description: 'items[646].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 50,
        width: 1,
        height: 1,
        stack: 20
    },
    {
        // Микроэлектроника 2 уровня
        id: 647,
        title: 'items[647].title',
        description: 'items[647].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 50,
        width: 1,
        height: 1,
        stack: 20
    },
    {
        // Микроэлектроника 3 уровня
        id: 648,
        title: 'items[648].title',
        description: 'items[648].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 50,
        width: 1,
        height: 1,
        stack: 20
    },
    {
        // Обработанная древесина
        id: 649,
        title: 'items[649].title',
        description: 'items[649].description',
        tag: 'items.tags.misc',
        model: 'mj_tin_ore',
        weight: 200,
        width: 3,
        height: 1,
        stack: 5
    },
    {
        // Сталь
        id: 650,
        title: 'items[650].title',
        description: 'items[650].description',
        tag: 'items.tags.misc',
        model: 'mj_tin_ore',
        weight: 300,
        width: 2,
        height: 2,
        stack: 5
    },
    {
        // Марганцевая руда
        id: 651,
        title: 'items[651].title',
        description: 'items[651].description',
        tag: 'items.tags.misc',
        model: 'mj_tin_ore',
        weight: 15,
        boxItemId: 743,
        width: 1,
        height: 1,
        stack: 50
    },
    {
        // Кремниевая руда
        id: 652,
        title: 'items[652].title',
        description: 'items[652].description',
        tag: 'items.tags.misc',
        model: 'mj_tin_ore',
        weight: 24,
        boxItemId: 744,
        width: 1,
        height: 1,
        stack: 50
    },
    {
        // Хромовая руда
        id: 653,
        title: 'items[653].title',
        description: 'items[653].description',
        tag: 'items.tags.misc',
        model: 'mj_tin_ore',
        weight: 36,
        boxItemId: 745,
        width: 1,
        height: 1,
        stack: 50
    },
    {
        // Никелевая руда
        id: 654,
        title: 'items[654].title',
        description: 'items[654].description',
        tag: 'items.tags.misc',
        model: 'mj_tin_ore',
        weight: 42,
        boxItemId: 746,
        width: 1,
        height: 1,
        stack: 50
    },

    {
        // П.С.С.И 1 уровня
        id: 655,
        title: 'items[655].title',
        description: 'items[655].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 3000,
        width: 3,
        height: 2,
        stack: 1
    },
    {
        // П.С.С.И 2 уровня
        id: 656,
        title: 'items[656].title',
        description: 'items[656].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 3500,
        width: 3,
        height: 2,
        stack: 1
    },
    {
        // П.С.С.И 3 уровня
        id: 657,
        title: 'items[657].title',
        description: 'items[657].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 4000,
        width: 3,
        height: 2,
        stack: 1
    },
    {
        // GPS трекер 1 уровня
        id: 658,
        title: 'items[658].title',
        description: 'items[658].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 10,
        width: 1,
        height: 1,
        stack: 1
    },
    {
        // GPS трекер 2 уровня
        id: 659,
        title: 'items[659].title',
        description: 'items[659].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 10,
        width: 1,
        height: 1,
        stack: 1
    },
    {
        // GPS трекер 3 уровня
        id: 660,
        title: 'items[660].title',
        description: 'items[660].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 10,
        width: 1,
        height: 1,
        stack: 1
    },

    {
        // Шифратор связи 1 уровня
        id: 661,
        title: 'items[661].title',
        description: 'items[661].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 1500,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Шифратор связи 2 уровня
        id: 662,
        title: 'items[662].title',
        description: 'items[662].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 1800,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Шифратор связи 3 уровня
        id: 663,
        title: 'items[663].title',
        description: 'items[663].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 2100,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Поддельная жилетка уборщика мусора
        id: 664,
        title: 'items[664].title',
        description: 'items[664].description',
        tag: 'items.tags.personals',
        model: 'mj_tin_ore',
        weight: 800,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Блок со всроенным ПО
        id: 665,
        title: 'items[665].title',
        description: 'items[665].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 750,
        width: 2,
        height: 1,
        stack: 1
    },
    {
        // Поддельная рубашка почтальона
        id: 666,
        title: 'items[666].title',
        description: 'items[666].description',
        tag: 'items.tags.personals',
        model: 'mj_tin_ore',
        weight: 800,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Валкодер
        id: 667,
        title: 'items[667].title',
        description: 'items[667].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 900,
        width: 1,
        height: 1,
        stack: 1
    },
    {
        // Поддельная рубашка инкассатора
        id: 668,
        title: 'items[668].title',
        description: 'items[668].description',
        tag: 'items.tags.personals',
        model: 'mj_tin_ore',
        weight: 800,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Стальная кирка
        id: 669,
        title: 'items[669].title',
        description: 'items[669].description',
        tag: 'items.tags.tools',
        model: 'mj_prop_tool_pickaxe',
        weight: 1700,
        width: 2,
        height: 4,
        stack: 1,
        caliber: true,
        takeable: true,
        disableDropOnCapt: true,
        grindSlots: 5,
        isTool: true,
        deathSafe: true,
        demorganSafe: true,
        buffs: [
            {
                name: 'ExtractionQuarryman'
            }
        ]
    },
    {
        // Стальной топор
        id: 670,
        title: 'items[670].title',
        description: 'items[670].description',
        model: 'w_me_majhatchet',
        weight: 1300,
        width: 2,
        height: 4,
        stack: 1,
        caliber: true,
        takeable: true,
        tag: 'items.tags.ammunition',
        hash: 'weapon_majhatchet',
        disableDropOnCapt: true,
        isMelee: true,
        dropOnDeath: false,
        deathSafe: true,
        demorganSafe: true,
        isTool: true,
        grindSlots: 5,
        buffs: [
            {
                name: 'ExtractionLumberjack'
            }
        ]
    },
    {
        // Стальной нож охотника
        id: 671,
        title: 'items[671].title',
        description: 'items[671].description',
        tag: 'items.tags.tools',
        model: 'w_me_majdagger',
        hash: 'WEAPON_MAJDAGGER',
        weight: 200,
        width: 1,
        height: 2,
        stack: 1,
        disableDropOnCapt: true,
        deathSafe: true,
        demorganSafe: true,
        isTool: true,
        canSeize: false,
        buffs: [
            {
                name: 'ExtractionHunter'
            }
        ]
    },
    {
        // Стальной нож грибника
        id: 672,
        title: 'items[672].title',
        description: 'items[672].description',
        model: 'w_me_majswitchblade',
        weight: 180,
        width: 1,
        height: 2,
        stack: 1,
        takeable: true,
        caliber: true,
        materials: 5,
        tag: 'items.tags.ammunition',
        hash: 'WEAPON_MAJSWITCHBLADE',
        disableDropOnCapt: true,
        isMelee: true,
        deathSafe: true,
        demorganSafe: true,
        isTool: true,
        canSeize: false,
        grindSlots: 5,
        buffs: [
            {
                name: 'ExtractionMushroomer'
            }
        ]
    },
    {
        // Стальная лейка фермера
        id: 673,
        title: 'items[673].title',
        model: 'mj_prop_wateringcan',
        weight: 600,
        width: 2,
        height: 2,
        noStack: true,
        takeable: true,
        fastSlot: true,
        disableDropOnCapt: true,
        tag: 'items.tags.tools',
        description: 'items[673].description',
        deathSafe: true,
        demorganSafe: true,
        buffs: [
            {
                name: 'ExtractionFarmer'
            }
        ]
    },
    {
        // Поддельные штаны уборщика мусора
        id: 674,
        title: 'items[674].title',
        description: 'items[674].description',
        tag: 'items.tags.personals',
        model: 'mj_tin_ore',
        weight: 600,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Серверная система 1 уровня
        id: 675,
        title: 'items[675].title',
        description: 'items[675].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 2900,
        width: 3,
        height: 2,
        stack: 1
    },
    {
        // Серверная система 2 уровня
        id: 676,
        title: 'items[676].title',
        description: 'items[676].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 3200,
        width: 3,
        height: 2,
        stack: 1
    },
    {
        // Серверная система 3 уровня
        id: 677,
        title: 'items[677].title',
        description: 'items[677].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 3500,
        width: 3,
        height: 2,
        stack: 1
    },
    {
        // Бумага 1 уровня
        id: 678,
        title: 'items[678].title',
        description: 'items[678].description',
        tag: 'items.tags.documents',
        model: 'prop_a4_pile_01',
        weight: 5,
        width: 1,
        height: 1,
        stack: 10
    },
    {
        // Бумага 2 уровня
        id: 679,
        title: 'items[679].title',
        description: 'items[679].description',
        tag: 'items.tags.documents',
        model: 'prop_a4_pile_01',
        weight: 10,
        width: 1,
        height: 1,
        stack: 10
    },
    {
        // Бумага 3 уровня
        id: 680,
        title: 'items[680].title',
        description: 'items[680].description',
        tag: 'items.tags.documents',
        model: 'prop_a4_pile_01',
        weight: 15,
        width: 1,
        height: 1,
        stack: 10
    },
    {
        // Краска для печати 1 уровня
        id: 681,
        title: 'items[681].title',
        description: 'items[681].description',
        tag: 'items.tags.misc',
        model: 'prop_paints_can02',
        weight: 450,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Краска для печати 2 уровня
        id: 682,
        title: 'items[682].title',
        description: 'items[682].description',
        tag: 'items.tags.misc',
        model: 'prop_paints_can02',
        weight: 500,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Краска для печати 3 уровня
        id: 683,
        title: 'items[683].title',
        description: 'items[683].description',
        tag: 'items.tags.misc',
        model: 'prop_paints_can02',
        weight: 550,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Поддельная подарочная карта
        id: 684,
        title: 'items[684].title',
        description: 'items[684].description',
        tag: 'items.tags.documents',
        model: 'hei_prop_hei_id_bio',
        weight: 30,
        width: 1,
        height: 1,
        stack: 1,
        canSeize: true
    },
    {
        // Поддельная лицензия на оружие
        id: 685,
        title: 'items[685].title',
        description: 'items[685].description',
        tag: 'items.tags.documents',
        model: 'hei_prop_hei_id_bank',
        coins: 100,
        weight: 20,
        width: 1,
        height: 1,
        stack: 1,
        usable: true,
        canSeize: true
    },
    {
        // Поддельная доверенность
        id: 686,
        title: 'items[686].title',
        description: 'items[686].description',
        tag: 'items.tags.documents',
        model: 'bkr_prop_fakeid_openpassport',
        weight: 500,
        width: 2,
        height: 2,
        stack: 1,
        canSeize: true
    },
    {
        // Поддельный военный билет
        id: 687,
        title: 'items[687].title',
        description: 'items[687].description',
        tag: 'items.tags.documents',
        model: 'prop_passport_01',
        weight: 500,
        width: 1,
        height: 1,
        stack: 1,
        usable: true,
        canSeize: true,
        coins: 150
    },
    {
        // Фальшивые деньги
        id: 688,
        title: 'items[688].title',
        description: 'items[688].description',
        tag: 'items.tags.misc',
        model: 'hei_prop_heist_cash_pile',
        stack: 150000,
        weight: 0.01,
        width: 1,
        height: 1,
        canSeize: true,
    },
    {
        // Воздушный фильтр 1 уровня
        id: 689,
        title: 'items[689].title',
        description: 'items[689].description',
        tag: 'items.tags.autoParts',
        model: 'mj_airfilter',
        weight: 400,
        width: 1,
        height: 1,
        stack: 1
    },
    {
        // Воздушный фильтр 2 уровня
        id: 690,
        title: 'items[690].title',
        description: 'items[690].description',
        tag: 'items.tags.autoParts',
        model: 'mj_airfilter',
        weight: 420,
        width: 1,
        height: 1,
        stack: 1
    },
    {
        // Воздушный фильтр 3 уровня
        id: 691,
        title: 'items[691].title',
        description: 'items[691].description',
        tag: 'items.tags.autoParts',
        model: 'mj_airfilter',
        weight: 440,
        width: 1,
        height: 1,
        stack: 1
    },
    {
        // Удобрения 1 уровня
        id: 692,
        title: 'items[692].title',
        description: 'items[692].description',
        tag: 'items.tags.agriculture',
        model: 'mj_pack_fertilize',
        weight: 310,
        width: 2,
        height: 2,
        stack: 3
    },
    {
        // Удобрения 2 уровня
        id: 693,
        title: 'items[693].title',
        description: 'items[693].description',
        tag: 'items.tags.agriculture',
        model: 'mj_pack_fertilize',
        weight: 330,
        width: 2,
        height: 2,
        stack: 3
    },
    {
        // Удобрения 3 уровня
        id: 694,
        title: 'items[694].title',
        description: 'items[694].description',
        tag: 'items.tags.agriculture',
        model: 'mj_pack_fertilize',
        weight: 350,
        width: 2,
        height: 2,
        stack: 3
    },
    {
        // Поддельные штаны почтальона
        id: 695,
        title: 'items[695].title',
        description: 'items[695].description',
        tag: 'items.tags.personals',
        model: 'mj_tin_ore',
        weight: 600,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Поддельные штаны инкассатора
        id: 696,
        title: 'items[696].title',
        description: 'items[696].description',
        tag: 'items.tags.personals',
        model: 'mj_tin_ore',
        weight: 600,
        width: 2,
        height: 2,
        stack: 1
    },
    {
        // Шифровальный ключ
        id: 697,
        title: 'items[697].title',
        description: 'items[697].description',
        tag: 'items.tags.facilities',
        model: 'mj_tin_ore',
        weight: 60,
        width: 1,
        height: 1,
        stack: 1
    },
    {
        // Ящик с сосновыми брёвнами
        id: 698,
        title: 'items[698].title',
        model: 'mj_box_addon_log',
        weight: 860,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 100,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[698].description'
    },
    {
        // Ящик с дубовыми брёвнами
        id: 699,
        title: 'items[699].title',
        model: 'mj_box_addon_log',
        weight: 1020,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 100,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[699].description'
    },
    {
        // Ящик с берёзовыми брёвнами
        id: 700,
        title: 'items[700].title',
        model: 'mj_box_addon_log',
        weight: 870,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 100,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[700].description'
    },
    {
        // Ящик с кленовыми брёвнами
        id: 701,
        title: 'items[701].title',
        model: 'mj_box_addon_log',
        weight: 860,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 100,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[701].description'
    },
    {
        // Ящик с апельсинами
        id: 702,
        title: 'items[702].title',
        model: 'mj_box_addon_orange',
        weight: 130,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 600,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[702].description'
    },
    {
        // Ящик с пшеницей
        id: 703,
        title: 'items[703].title',
        model: 'mj_box_addon_wheat',
        weight: 90,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 600,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[703].description'
    },
    {
        // Ящик с капустой
        id: 704,
        title: 'items[704].title',
        model: 'mj_box_addon_cabbage',
        weight: 550,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 400,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[704].description'
    },
    {
        // Ящик с картошкой
        id: 705,
        title: 'items[705].title',
        model: 'mj_box_addon_potato',
        weight: 150,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 450,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[705].description'
    },
    {
        // Ящик с кукурузой
        id: 706,
        title: 'items[706].title',
        model: 'mj_box_addon_corn',
        weight: 200,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 400,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[706].description'
    },
    {
        // Ящик с тыквами
        id: 707,
        title: 'items[707].title',
        model: 'mj_box_addon_pumpkin',
        weight: 800,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 100,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[707].description'
    },
    {
        // Ящик с бананами
        id: 708,
        title: 'items[708].title',
        model: 'mj_box_addon_banana',
        weight: 130,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[708].description'
    },
    {
        // Ящик с железной рудой
        id: 709,
        title: 'items[709].title',
        model: 'mj_box_addon_ore',
        weight: 50,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[709].description'
    },
    {
        // Ящик с серебряной рудой
        id: 710,
        title: 'items[710].title',
        model: 'mj_box_addon_ore',
        weight: 240,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[710].description'
    },
    {
        // Ящик с медной рудой
        id: 711,
        title: 'items[711].title',
        model: 'mj_box_addon_ore',
        weight: 300,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[711].description'
    },
    {
        // Ящик с оловянной рудой
        id: 712,
        title: 'items[712].title',
        model: 'mj_box_addon_ore',
        weight: 240,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[712].description'
    },
    {
        // Ящик с золотой рудой
        id: 713,
        title: 'items[713].title',
        model: 'mj_box_addon_ore',
        weight: 360,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[713].description'
    },
    {
        // Ящик с Вобла
        id: 714,
        title: 'items[714].title',
        model: 'mj_box_addon_fish',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 30000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[714].description'
    },
    {
        // Ящик с Коричневый сом
        id: 715,
        title: 'items[715].title',
        model: 'mj_box_addon_fish',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 30000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[715].description'
    },
    {
        // Ящик с Речной окунь
        id: 716,
        title: 'items[716].title',
        model: 'mj_box_addon_fish',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 30000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[716].description'
    },
    {
        // Ящик с Зеркальный карп
        id: 717,
        title: 'items[717].title',
        model: 'mj_box_addon_fish',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 30000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[717].description'
    },
    {
        // Ящик с Голавль
        id: 718,
        title: 'items[718].title',
        model: 'mj_box_addon_fish',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 30000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[718].description'
    },
    {
        // Ящик с Полосатый лаврак
        id: 719,
        title: 'items[719].title',
        model: 'mj_box_addon_fish',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 30000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[719].description'
    },
    {
        // Ящик с Стальноголовый лосось
        id: 720,
        title: 'items[720].title',
        model: 'mj_box_addon_fish',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 30000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[720].description'
    },
    {
        // Ящик с Красный горбыль
        id: 721,
        title: 'items[721].title',
        model: 'mj_box_addon_fish',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 30000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[721].description'
    },
    {
        // Ящик с шампиньонами
        id: 722,
        title: 'items[722].title',
        model: 'mj_box_addon_mashroom',
        weight: 30,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 5000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[722].description'
    },
    {
        // Ящик с вёшенками
        id: 723,
        title: 'items[723].title',
        model: 'mj_box_addon_mashroom',
        weight: 90,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[723].description'
    },
    {
        // Улучшенный металлоискатель
        id: 724,
        title: 'items[724].title',
        model: 'w_am_metaldetector',
        weight: 1000,
        width: 3,
        height: 2,
        takeable: true,
        fastSlot: true,
        deathSafe: true,
        tag: 'items.tags.facilities',
        description: 'items[724].description'
    },
    {
        // Ремонтный комплект для оружия
        id: 725,
        title: 'items[725].title',
        model: 'prop_box_guncase_01a',
        weight: 2500,
        width: 2,
        height: 2,
        usable: true,
        tag: 'items.tags.tools',
        description: 'items[725].description'
    },
    {
        // Экспериментальная пилюля "Имморталитикс"
        id: 726,
        title: 'items[726].title',
        model: 'mj_v_club_vu_pills',
        weight: 50,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.medicine',
        description: 'items[726].description',
        buffs: [
            {
                name: 'HealCovid'
            },
            {
                name: 'HealCold'
            },
            {
                name: 'HealPoisoning'
            }
        ],
        useItem: {
            animationProp: null,
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke_enter',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: null,
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_pills',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }
    },
    {
        // Большой ремонтный набор
        id: 727,
        title: 'items[727].title',
        model: 'v_ind_cs_toolbox2',
        weight: 300,
        width: 3,
        height: 2,
        stack: 25,
        tag: 'items.tags.tools',
        description: 'items[727].description',
    },
    {
        // Биодобавка 1 уровня
        id: 728,
        title: 'items[728].title',
        model: 'p_syringe_01_s',
        weight: 300,
        width: 1,
        height: 2,
        usable: true,
        stack: 1,
        tag: 'items.tags.medicine',
        description: 'items[728].description',
        buffs: [
            {
                name: 'Bioadditive',
                duration: 30 * 60 * 1000
            }
        ],
        useItem: {
            animationProp: 'bioadditive',
            animationDict: 'majestic_animations_custom',
            animationName: 'syringe',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usedSound: 'syringe',

            duration: 2500
        }
    },
    {
        // Биодобавка 2 уровня
        id: 729,
        title: 'items[729].title',
        model: 'p_syringe_01_s',
        weight: 300,
        width: 1,
        height: 2,
        usable: true,
        stack: 1,
        tag: 'items.tags.medicine',
        description: 'items[729].description',
        buffs: [
            {
                name: 'Bioadditive',
                duration: 60 * 60 * 1000
            }
        ],
        useItem: {
            animationProp: 'bioadditive',
            animationDict: 'majestic_animations_custom',
            animationName: 'syringe',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usedSound: 'syringe',

            duration: 2500
        }
    },
    {
        // Биодобавка 3 уровня
        id: 730,
        title: 'items[730].title',
        model: 'p_syringe_01_s',
        weight: 300,
        width: 1,
        height: 2,
        usable: true,
        stack: 1,
        tag: 'items.tags.medicine',
        description: 'items[730].description',
        buffs: [
            {
                name: 'Bioadditive',
                duration: 2 * 60 * 60 * 1000
            }
        ],
        useItem: {
            animationProp: 'bioadditive',
            animationDict: 'majestic_animations_custom',
            animationName: 'syringe',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usedSound: 'syringe',

            duration: 2500
        }
    },
    {
        // Капсулы восстановления
        id: 731,
        title: 'items[731].title',
        model: 'prop_cs_pills',
        weight: 50,
        width: 1,
        height: 1,
        usable: true,
        stack: 20,
        fastSlot: true,
        tag: 'items.tags.medicine',
        description: 'items[731].description',
        buffs: [
            {
                name: 'HealCapsule',
                duration: 300 * 1000
            }
        ],
        useItem: {
            animationProp: null,
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke_enter',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: null,
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_pills',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }
    },
    {
        // Протеиновый батончик
        id: 732,
        title: 'items[732].title',
        model: 'prop_choc_meto',
        weight: 50,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.food',
        description: 'items[732].description',
        stats: {
            hunger: 25,
            water: -10
        },
        buffs: [
            {
                name: 'ProteinBar',
                duration: 30 * 60 * 1000
            }
        ],
        useItem: {
            animationProp: 'choc_meto',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'choc_meto',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Стайлинг для волос
        id: 733,
        title: 'items[733].title',
        model: 'prop_cs_pills',
        weight: 500,
        width: 1,
        height: 1,
        usable: true,
        stack: 10,
        tag: 'items.tags.consumables',
        description: 'items[733].description',
        buffs: [
            {
                name: 'Styling',
                duration: 3 * 60 * 60 * 1000
            }
        ],
        useItem: {
            animationProp: null,
            animationDict: 'majestic_animations_custom',
            animationName: 'styling',
            animationFlag: 16,

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usingSound: 'styling',

            duration: 4000
        }
    },
    {
        // Благодарственное письмо губернатора
        id: 734,
        title: 'items[734].title',
        model: 'prop_cash_envelope_01',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.documents',
        description: 'items[734].description',
        buffs: [
            {
                name: 'GovernorAppreciation',
                duration: 5 * 60 * 60 * 1000
            }
        ],
    },
    {
        // Испорченный бургер
        id: 735,
        title: 'items[735].title',
        model: 'prop_cs_burger_01',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
        tag: 'items.tags.food',
        description: 'items[735].description',
        // food: {
        //     hunger: -100,
        //     water: -100,
        //     hp: 0,
        // },
        stats: {
            hunger: -100,
            water: -100,
            poisonChance: 100,
        },
        useItem: {
            animationProp: 'burger', // animation prop
            animationDict: 'mp_player_inteat@burger', // animation dict
            animationName: 'mp_player_int_eat_burger', // animation name

            crawlingAnimationProp: 'burger',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Пицца
        id: 736,
        title: 'items[736].title',
        model: 'prop_pizza_box_01',
        weight: 500,
        width: 2,
        height: 2,
        stack: 8,
        usable: true,
        tag: 'items.tags.food',
        description: 'items[736].description',
        // food: {
        //     hunger: 50,
        //     water: 0,
        //     hp: 0,
        // },
        stats: {
            hunger: 50,
            water: -15
        },
        useItem: {
            animationProp: 'pizza',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            crawlingAnimationProp: 'pizza',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Консервированные бобы
        id: 737,
        title: 'items[737].title',
        model: 'mj_v_res_fa_tincorn',
        weight: 500,
        width: 1,
        height: 2,
        usable: true,
        tag: 'items.tags.food',
        description: 'items[737].description',
        stats: {
            hunger: 70,
            water: 35
        },
        useItem: {
            animationProp: 'pea',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'pea',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000
        }
    },
    {
        // Непредсказуемый коктейль Реднека
        id: 738,
        title: 'items[738].title',
        model: 'p_cs_bottle_01',
        weight: 1000,
        width: 1,
        height: 2,
        usable: true,
        tag: 'items.tags.alcohol',
        description: 'items[738].description',
        useItem: {
            animationProp: 'bottle1',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop_bottle',

            crawlingAnimationProp: 'bottle1',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000
        }
    },
    {
        // Ящик с гипсизигусами
        id: 739,
        title: 'items[739].title',
        model: 'mj_box_addon_mashroom',
        weight: 70,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[739].description'
    },
    {
        // Ящик с мухоморами
        id: 740,
        title: 'items[740].title',
        model: 'mj_box_addon_mashroom',
        weight: 110,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[740].description'
    },
    {
        // Ящик с подболотниками
        id: 741,
        title: 'items[741].title',
        model: 'mj_box_addon_mashroom',
        weight: 50,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[741].description'
    },
    {
        // Ящик с подберёзовиками
        id: 742,
        title: 'items[742].title',
        model: 'mj_box_addon_mashroom',
        weight: 70,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[742].description'
    },
    {
        // Ящик с марганцевой рудой
        id: 743,
        title: 'items[743].title',
        model: 'mj_box_addon_ore',
        weight: 15,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 2000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[743].description'
    },
    {
        // Ящик с кремниевой рудой
        id: 744,
        title: 'items[744].title',
        model: 'mj_box_addon_ore',
        weight: 24,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 2000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[744].description'
    },
    {
        // Ящик с хромовой рудой
        id: 745,
        title: 'items[745].title',
        model: 'mj_box_addon_ore',
        weight: 36,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[745].description'
    },
    {
        // Ящик с никелиевой рудой
        id: 746,
        title: 'items[746].title',
        model: 'mj_box_addon_ore',
        weight: 42,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[746].description'
    },
    {
        // Пакет с Green
        id: 747,
        title: 'items[747].title',
        model: 'mbkr_prop_weed_smallbag_01a',
        weight: 1,
        width: 2,
        height: 2,
        tag: 'items.tags.box',
        stack: 400,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[747].description'
    },
    {
        // Блок с Green
        id: 748,
        title: 'items[748].title',
        model: 'bkr_prop_weed_bigbag_03a',
        weight: 1,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 2000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[748].description'
    },
    {
        // Контейнер с Green
        id: 749,
        title: 'items[749].title',
        model: 'ex_prop_adv_case_sm_02',
        weight: 1,
        width: 4,
        height: 3,
        // tag: 'items.tags.box',
        canHandItem: true,
        stack: 10000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[749].description'
    },
    {
        // Небольшой пакеn с Blue
        id: 750,
        title: 'items[750].title',
        model: 'bkr_prop_meth_smallbag_01a',
        weight: 1,
        width: 1,
        height: 2,
        // tag: 'items.tags.box',
        canHandItem: true,
        stack: 100,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[750].description'
    },
    {
        // Пакеn с Blue
        id: 751,
        title: 'items[751].title',
        model: 'ebkr_prop_meth_smallbag_01a',
        weight: 1,
        width: 2,
        height: 2,
        // tag: 'items.tags.box',
        canHandItem: true,
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[751].description'
    },
    {
        // Контейнер с Blue
        id: 752,
        title: 'items[752].title',
        model: 'ex_prop_adv_case_sm_02',
        weight: 1,
        width: 4,
        height: 3,
        // tag: 'items.tags.box',
        canHandItem: true,
        stack: 2500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[752].description'
    },
    {
        // Пакетик с White
        id: 753,
        title: 'items[753].title',
        model: 'hei_prop_pill_bag_01',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 100,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[753].description'
    },
    {
        // Небольшой пакет с White
        id: 754,
        title: 'items[754].title',
        model: 'hei_prop_pill_bag_01',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 250,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[754].description'
    },
    {
        // Пакет с White
        id: 755,
        title: 'items[755].title',
        model: 'hei_prop_pill_bag_01',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 625,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[755].description'
    },
    {
        // Скейт
        id: 756,
        title: 'items[756].title',
        model: 'mj_skate',
        weight: 2000,
        width: 3,
        height: 1,
        takeable: true,
        animateItem: {
            flag: 32 + 1,
            playerAnimDict: 'majestic_animations_props_custom',
            playerAnimName: 'skate_player',
            propSet: 'skateboard'
        },
        tag: 'items.tags.misc',
        description: 'items[756].description'
    },
    {
        // Ролики
        id: 757,
        title: 'items[757].title',
        model: 'mj_rollers',
        weight: 2000,
        width: 2,
        height: 2,
        takeable: true,
        animateItem: {
            flag: 32 + 1,
            playerAnimDict: 'majestic_animations_props_custom',
            playerAnimName: 'rollers',
            propSet: 'rollers'
        },
        tag: 'items.tags.misc',
        description: 'items[757].description'
    },
    {
        // Night Vision прицел
        id: 758,
        title: 'items[758].title',
        model: 'w_at_scope_nv',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        hasSerial: true,
        canSeize: true,
        description: 'items[758].description',
        modificationSlot: 4
    },
    {
        // Тепловизионный прицел
        id: 759,
        title: 'items[759].title',
        model: 'w_at_scope_nv',
        weight: 500,
        width: 1,
        height: 1,
        tag: 'items.tags.ammunition',
        hasSerial: true,
        canSeize: true,
        description: 'items[759].description',
        modificationSlot: 4
    },
    {
        // Увеличенный магазин
        id: 760,
        title: 'items[760].title',
        model: 'w_ar_carbinerifle_mag2',
        boxItemId: 766,
        materialType: 'green',
        materials: 10,
        weight: 700,
        width: 1,
        height: 1,
        limit: 5000,
        tag: 'items.tags.ammunition',
        hasSerial: true,
        canSeize: true,
        description: 'items[760].description',
        modificationSlot: 1
    },
    {
        // Рукоятка
        id: 761,
        title: 'items[761].title',
        model: 'w_at_ar_afgrip',
        materialType: 'green',
        materials: 10,
        boxItemId: 767,
        weight: 500,
        width: 1,
        height: 1,
        limit: 6000,
        tag: 'items.tags.ammunition',
        hasSerial: true,
        canSeize: true,
        description: 'items[761].description',
        modificationSlot: 3
    },
    {
        // Голографический прицел
        id: 762,
        title: 'items[762].title',
        model: 'w_at_sights_1',
        materialType: 'green',
        materials: 4,
        boxItemId: 768,
        weight: 300,
        width: 1,
        height: 1,
        limit: 6500,
        tag: 'items.tags.ammunition',
        hasSerial: true,
        canSeize: true,
        description: 'items[762].description',
        modificationSlot: 2
    },
    {
        // Оптический прицел
        id: 763,
        title: 'items[763].title',
        model: 'w_at_scope_macro',
        materialType: 'green',
        materials: 4,
        boxItemId: 769,
        weight: 300,
        width: 1,
        height: 1,
        limit: 6500,
        tag: 'items.tags.ammunition',
        hasSerial: true,
        canSeize: true,
        description: 'items[763].description',
        modificationSlot: 2
    },
    {
        // Глушитель
        id: 764,
        title: 'items[764].title',
        model: 'w_at_ar_supp',
        materialType: 'green',
        materials: 6,
        boxItemId: 770,
        weight: 200,
        width: 1,
        height: 1,
        limit: 3000,
        tag: 'items.tags.ammunition',
        hasSerial: true,
        canSeize: true,
        description: 'items[764].description',
        modificationSlot: 5
    },
    {
        // Фонарик
        id: 765,
        title: 'items[765].title',
        model: 'w_at_ar_flsh',
        boxItemId: 771,
        materialType: 'green',
        materials: 2,
        weight: 200,
        width: 1,
        height: 1,
        limit: 7000,
        tag: 'items.tags.ammunition',
        hasSerial: true,
        canSeize: true,
        description: 'items[765].description',
        modificationSlot: 4
    },
    {
        // Ящик с модулем «Увеличенный магазин»
        id: 766,
        title: 'items[766].title',
        model: 'mj_box_magazines',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[766].description'
    },
    {
        // Ящик с модулем «Рукоять»
        id: 767,
        title: 'items[767].title',
        model: 'mj_box_handles',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[767].description'
    },
    {
        // Ящик с модулем «Голографический прицел»
        id: 768,
        title: 'items[768].title',
        model: 'mj_box_holo_sight',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[768].description'
    },
    {
        // Ящик с модулем «Обычный прицел»
        id: 769,
        title: 'items[769].title',
        model: 'mj_box_optical_sight',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[769].description'
    },
    {
        // Ящик с модулем «Глушитель»
        id: 770,
        title: 'items[770].title',
        model: 'mj_box_silencers',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[770].description'
    },
    {
        // Ящик с модулем «Фонарик»
        id: 771,
        title: 'items[771].title',
        model: 'mj_box_flashlights',
        weight: 500,
        width: 3,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[771].description'
    },
    {
        // Неизвестная коробка
        id: 772,
        title: 'items[772].title',
        model: 'v_serv_abox_04',
        // tag: 'items.tags.box',
        weight: 3000,
        width: 3,
        height: 2,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[772].description',
        contractBox: true
    },
    {
        // Ящик с химикатами
        id: 773,
        title: 'items[773].title',
        model: 'mj_prop_tool_box_05',
        weight: 7000,
        width: 3,
        height: 2,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[773].description',
        contractBox: true
    },
    {
        // Ящик со взрывчатыми вещестами
        id: 774,
        title: 'items[774].title',
        model: 'mj_hei_prop_heist_thermite_case',
        //tag: 'items.tags.box',
        weight: 9000,
        width: 3,
        height: 2,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[774].description',
        contractBox: true
    },
    {
        // Посылка
        id: 775,
        title: 'items[775].title',
        model: 'mj_prop_cs_box_clothes',
        //tag: 'items.tags.box',
        weight: 4000,
        width: 3,
        height: 2,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[775].description',
        contractBox: true
    },
    {
        // Корм
        id: 776,
        title: 'items[776].title',
        model: 'mj_prop_feed_sack_01',
        tag: 'items.tags.agriculture',
        weight: 6000,
        width: 3,
        height: 2,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[776].description'
    },
    {
        // Ящик с запчастями
        id: 777,
        title: 'items[777].title',
        model: 'mj_gr_prop_gr_rsply_crate03a',
        //tag: 'items.tags.box',
        weight: 8000,
        width: 3,
        height: 2,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[777].description',
        contractBox: true
    },
    {
        // Кейс с электроникой
        id: 778,
        title: 'items[778].title',
        model: 'mj_bkr_prop_biker_case_shut',
        //tag: 'items.tags.box',
        weight: 10000,
        width: 3,
        height: 2,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        description: 'items[778].description',
        contractBox: true
    },
    {
        // Контрабанда
        id: 779,
        title: 'items[779].title',
        model: 'mj_prop_mp_drug_package',
        tag: 'items.tags.drugs',
        weight: 3000,
        width: 3,
        height: 2,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        canSeize: true,
        description: 'items[779].description'
    },
    {
        // Контрабанда
        id: 780,
        title: 'items[780].title',
        model: 'mj_prop_mp_drug_pack_blue',
        tag: 'items.tags.drugs',
        weight: 4500,
        width: 3,
        height: 2,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        canSeize: true,
        description: 'items[780].description'
    },
    {
        // Контрабанда
        id: 781,
        title: 'items[781].title',
        model: 'mj_prop_mp_drug_pack_red',
        tag: 'items.tags.drugs',
        weight: 6000,
        width: 3,
        height: 2,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canHandItem: true,
        canSeize: true,
        description: 'items[781].description'
    },
    {
        // XSS - устройство 1 уровня
        id: 782,
        title: 'items[782].title',
        description: 'items[782].description',
        tag: 'items.tags.facilities',
        model: 'hei_prop_hst_usb_drive',
        canPickupToBag: false,
        weight: 80,
        width: 1,
        height: 1,
        // usable: true
    },
    {
        // XSS - устройство 2 уровня
        id: 783,
        title: 'items[783].title',
        description: 'items[783].description',
        tag: 'items.tags.facilities',
        model: 'hei_prop_hst_usb_drive',
        canPickupToBag: false,
        weight: 100,
        width: 1,
        height: 1,
        // usable: true
    },
    {
        // XSS - устройство 3 уровня
        id: 784,
        title: 'items[784].title',
        description: 'items[784].description',
        tag: 'items.tags.facilities',
        model: 'hei_prop_hst_usb_drive',
        canPickupToBag: false,
        weight: 120,
        width: 1,
        height: 1,
        // usable: true
    },
    {
        // Форма EMS
        id: 785,
        title: 'items[785].title',
        description: 'items[785].description',
        tag: 'items.tags.personals',
        model: 'prop_overalls_01',
        weight: 1500,
        width: 2,
        height: 2,
    },
    {
        // Форма Sheriff Deparment
        id: 786,
        title: 'items[786].title',
        description: 'items[786].description',
        tag: 'items.tags.personals',
        model: 'prop_overalls_01',
        weight: 1650,
        width: 2,
        height: 2,
    },
    {
        // Форма LSPD
        id: 787,
        title: 'items[787].title',
        description: 'items[787].description',
        tag: 'items.tags.personalsc',
        model: 'prop_overalls_01',
        weight: 1800,
        width: 2,
        height: 2,
    },
    {
        // Форма Федеральной Тюрьмы
        id: 788,
        title: 'items[788].title',
        description: 'items[788].description',
        tag: 'items.tags.personals',
        model: 'prop_overalls_01',
        weight: 1950,
        width: 2,
        height: 2,
    },
    {
        // Форма FIB
        id: 789,
        title: 'items[789].title',
        description: 'items[789].description',
        tag: 'items.tags.personals',
        model: 'prop_overalls_01',
        weight: 2100,
        width: 2,
        height: 2,
    },
    {
        // Форма SANG
        id: 790,
        title: 'items[790].title',
        description: 'items[790].description',
        tag: 'items.tags.personals',
        model: 'prop_overalls_01',
        weight: 2250,
        width: 2,
        height: 2,
    },
    {
        // Глушилка GPS сигнала
        id: 791,
        title: 'items[791].title',
        description: 'items[791].description',
        tag: 'items.tags.facilities',
        model: 'hei_prop_heist_transponder',
        weight: 450,
        coins: 100,
        width: 1,
        height: 1,
        buffs: [
            {
                name: 'Jammergps',
                duration: 30 * 60 * 1000,
            }
        ]
    },
    {
        // Флешка с компроматом
        id: 792,
        title: 'items[792].title',
        description: 'items[792].description',
        tag: 'items.tags.facilities',
        model: 'hei_prop_heist_transponder',
        weight: 90,
        width: 1,
        height: 1,
        stack: 1,
        coins: 150
    },
    {
        // Пластиковая статуэтка
        id: 793,
        title: 'items[793].title',
        description: 'items[793].description',
        tag: 'items.tags.jewelry',
        model: 'ex_prop_exec_award_plastic',
        weight: 200,
        width: 1,
        height: 2,
        stack: 1
    },
    {
        // Бронзовая статуэтка
        id: 794,
        title: 'items[794].title',
        description: 'items[794].description',
        tag: 'items.tags.jewelry',
        model: 'ex_prop_exec_award_bronze',
        weight: 400,
        width: 1,
        height: 2,
        stack: 1
    },
    {
        // Серебряная статуэтка
        id: 795,
        title: 'items[795].title',
        description: 'items[795].description',
        tag: 'items.tags.jewelryc',
        model: 'ex_prop_exec_award_silver',
        weight: 600,
        width: 1,
        height: 2,
        stack: 1
    },
    {
        // Золотая статуэтка
        id: 796,
        title: 'items[796].title',
        description: 'items[796].description',
        tag: 'items.tags.jewelry',
        model: 'ex_prop_exec_award_gold',
        weight: 800,
        width: 1,
        height: 2,
        stack: 1
    },
    {
        // Алмазная статуэтка
        id: 797,
        title: 'items[797].title',
        description: 'items[797].description',
        tag: 'items.tags.jewelry',
        model: 'ex_prop_exec_award_diamond',
        weight: 1000,
        width: 1,
        height: 2,
        stack: 1
    },
    {
        // Медный провод
        id: 798,
        title: 'items[798].title',
        model: 'hei_prop_zip_tie_positioned',
        weight: 10,
        width: 1,
        height: 1,
        stack: 10,
        tag: 'items.tags.facilities',
        description: 'items[798].description'
    },
    {
        // Блок управления
        id: 799,
        title: 'items[799].title',
        model: 'hei_prop_heist_transponder',
        weight: 350,
        width: 1,
        height: 1,
        tag: 'items.tags.facilities',
        description: 'items[799].description'
    },
    {
        // Карта сетевого интерфейса
        id: 800,
        title: 'items[800].title',
        model: 'hei_prop_heist_transponder',
        weight: 110,
        width: 2,
        height: 1,
        stack: 4,
        tag: 'items.tags.facilities',
        description: 'items[800].description'
    },
    {
        // Батарейка
        id: 801,
        title: 'items[801].title',
        model: 'lr_prop_carkey_fob',
        weight: 30,
        width: 1,
        height: 1,
        stack: 10,
        tag: 'items.tags.facilities',
        description: 'items[801].description'
    },
    {
        // Пластик
        id: 802,
        title: 'items[802].title',
        model: 'prop_ld_hdd_01',
        weight: 150,
        width: 1,
        height: 1,
        stack: 10,
        tag: 'items.tags.consumables',
        description: 'items[802].description'
    },
    {
        // Стеклянная колба
        id: 803,
        title: 'items[803].title',
        model: 'prop_drug_bottle',
        weight: 250,
        width: 1,
        height: 2,
        stack: 1,
        tag: 'items.tags.consumables',
        description: 'items[803].description'
    },
    {
        // Порошок TOXIC
        id: 804,
        title: 'items[804].title',
        model: 'p_meth_bag_01_s',
        weight: 25,
        width: 1,
        height: 1,
        stack: 5,
        tag: 'items.tags.drugs',
        description: 'items[804].description'
    },
    {
        // Металлолом
        id: 805,
        title: 'items[805].title',
        model: 'prop_ld_hdd_01',
        weight: 540,
        width: 2,
        height: 2,
        stack: 10,
        tag: 'items.tags.consumables',
        description: 'items[805].description'
    },
    {
        // Ржавый механизм
        id: 806,
        title: 'items[806].title',
        model: 'lr_prop_carkey_fob',
        weight: 210,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.consumables',
        description: 'items[806].description'
    },
    {
        // Коробка
        id: 807,
        title: 'items[807].title',
        model: 'prop_cs_cardbox_01',
        weight: 200,
        width: 2,
        height: 2,
        tag: 'items.tags.consumables',
        description: 'items[807].description'
    },
    {
        // Трансцендентный Ассинезатор
        id: 808,
        title: 'items[808].title',
        model: 'w_am_digiscanner',
        weight: 1000,
        width: 1,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: false,
        caliber: true,
        hash: 'WEAPON_DIGISCANNER',
        limit: 7000,
        isMelee: true,
        canDrag: false,
        description: 'items[808].description',
        demorganSafe: true,
    },
    {
        // Симб. модуль
        id: 809,
        title: 'items[809].title',
        model: 'sky_fanar',
        weight: 350,
        width: 1,
        height: 2,
        stack: 1,
        tag: 'items.tags.consumables',
        description: 'items[809].description'
    },
    {
        // Темные частицы
        id: 810,
        title: 'items[810].title',
        model: 'sky_clyc',
        weight: 5,
        width: 1,
        height: 1,
        stack: 10,
        tag: 'items.tags.consumables',
        description: 'items[810].description'
    },
    {
        // Свисток
        id: 811,
        title: 'items[811].title',
        model: 'mj_swistle',
        weight: 500,
        width: 1,
        height: 1,
        usable: true,
        fastSlot: true,
        deathSafe: true,
        tag: 'items.tags.tools',
        description: 'items[811].description'
    },
    {
        // Лыжи
        id: 812,
        title: 'items[812].title',
        model: 'mj_ski',
        weight: 1500,
        width: 1,
        height: 3,
        takeable: true,
        animateItem: {
            flag: 32 + 1,
            playerAnimDict: 'majestic_animations_props_custom',
            playerAnimName: 'skis_walk',
            propSet: 'ski'
        },
        tag: 'items.tags.equipment',
        description: 'items[812].description'
    },
    {
        // Коньки
        id: 813,
        title: 'items[813].title',
        model: 'mj_ice_skates',
        weight: 1500,
        width: 2,
        height: 2,
        takeable: true,
        animateItem: {
            flag: 32 + 1,
            playerAnimDict: 'majestic_animations_props_custom',
            playerAnimName: 'ice_skates',
            propSet: 'skates'
        },
        tag: 'items.tags.equipment',
        description: 'items[813].description'
    },
    {
        // Сани
        id: 814,
        title: 'items[814].title',
        model: 'mj_sleigh_custom',
        weight: 3000,
        width: 4,
        height: 4,
        takeable: true,
        animateItem: {
            flag: 32 + 1,
            playerAnimDict: 'majestic_animations_props_2',
            playerAnimName: 'sled_player',
            propSet: 'sled'
        },
        tag: 'items.tags.equipment',
        description: 'items[814].description'
    },
    {
        // Волшебная чёрная конфета
        id: 815,
        title: 'items[815].title',
        model: 'prop_ld_cable_tie_01',
        weight: 200,
        width: 1,
        height: 1,
        stack: 1,
        usable: true,
        tag: 'items.tags.food',
        description: 'items[815].description',
        buffs: [
            {
                name: 'MagicCandyBlack',
                duration: 10 * 60 * 1000
            }
        ],
        useItem: {
            animationProp: null, // animation prop
            animationDict: 'mp_player_int_uppersmoke', // animation dict
            animationName: 'mp_player_int_smoke_enter', // animation name

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }
    },
    {
        // Волшебная белая конфета
        id: 816,
        title: 'items[816].title',
        model: 'prop_ld_cable_tie_01',
        weight: 1500,
        width: 1,
        height: 1,
        stack: 1,
        usable: true,
        tag: 'items.tags.food',
        description: 'items[816].description',
        buffs: [
            {
                name: 'MagicCandyWhite',
                duration: 10 * 60 * 1000
            }
        ],
        useItem: {
            animationProp: null, // animation prop
            animationDict: 'mp_player_int_uppersmoke', // animation dict
            animationName: 'mp_player_int_smoke_enter', // animation name

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }
    },
    {
        // Пакет с сорванными кустами
        id: 817,
        title: 'items[817].title',
        model: 'ex_prop_adv_case_sm_02',
        weight: 1,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 50,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[817].description'
    },
    {
        // Пакет с сорванными кустами
        id: 818,
        title: 'items[818].title',
        model: 'bkr_prop_weed_bigbag_03a',
        weight: 1,
        width: 3,
        height: 2,
        tag: 'items.tags.box',
        stack: 200,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[818].description'
    },
    {
        // Контейнер с сорванными кустами
        id: 819,
        title: 'items[819].title',
        model: 'ex_prop_adv_case_sm_02',
        weight: 1,
        width: 4,
        height: 3,
        // tag: 'items.tags.box',
        canHandItem: true,
        stack: 1000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[819].description'
    },
    {
        // Тактический SMG
        id: 820,
        title: 'items[820].title',
        model: 'W_PI_PistolSMG_M31',
        boxItemId: 821,
        materialType: 'green',
        materials: 20,
        weight: 1600,
        width: 3,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 254,
        hash: 'weapon_tecpistol',
        isMelee: false,
        limit: 3000,
        description: 'items[820].description'
    },
    {
        // Ящик с оружием «Тактический SMG»
        id: 821,
        title: 'items[821].title',
        model: 'mj_tactical_smg_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[821].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Тяжёлая винтовка»
        id: 822,
        title: 'items[822].title',
        model: 'mj_heavy_rifle_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[822].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Тактическая винтовка»
        id: 823,
        title: 'items[823].title',
        model: 'mj_tactical_rifle_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[823].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Прецизионная винтовка»
        id: 824,
        title: 'items[824].title',
        model: 'mj_gr_prop_gr_rsply_crate03a',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[824].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Тяжелый пистолет»
        id: 825,
        title: 'items[825].title',
        model: 'mj_heavy_pistol_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[825].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Микро SMG»
        id: 826,
        title: 'items[826].title',
        model: 'mj_micro_smg_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[826].description',
        isSupplyBox: true
    },
    {
        // Ящик с оружием «Короткий дробовик»
        id: 827,
        title: 'items[827].title',
        model: 'mj_mini_shotgun_box',
        weight: 500,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 1,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[827].description',
        isSupplyBox: true
    },
    {
        // Валентинка
        id: 828,
        title: 'items[828].title',
        description: 'items[828].description',
        model: 'hei_heist_acc_box_trinket_02',
        weight: 50,
        width: 1,
        height: 1,
        canDrag: false,
        stack: 1,
        tag: 'items.tags.misc',
    },
    {
        // Жетон парка
        id: 829,
        title: 'items[829].title',
        description: 'items[829].description',
        model: 'hei_heist_acc_box_trinket_02',
        weight: 100,
        width: 1,
        height: 1,
        canDrag: false,
        stack: 1,
        tag: 'items.tags.misc',
    },
    {
        // Валюта дня валентина
        id: 830,
        title: 'items[830].title',
        description: 'items[830].description',
        model: 'hei_heist_acc_box_trinket_02',
        weight: 10,
        width: 1,
        height: 1,
        canDrag: false,
        stack: 100,
        tag: 'items.tags.misc',
    },
    {
        // Ангельский компас
        id: 831,
        title: 'items[831].title',
        description: 'items[831].description',
        model: 'hei_heist_acc_box_trinket_02',
        weight: 100,
        width: 1,
        height: 1,
        canDrag: true,
        usable: true,
        stack: 1,
        tag: 'items.tags.misc',
    },

    {
        // Кусачки
        id: 832,
        title: 'items[832].title',
        model: 'gr_prop_gr_pliers_01',
        weight: 160,
        width: 1,
        limit: 1250,
        height: 1,
        tag: 'items.tags.tools',
        shopCount: 0,
        description: 'items[832].description',
        canSeize: true,
        canPickupToBag: false
    },
    {
        // Камера
        id: 833,
        title: 'items[833].title',
        model: 'ch_prop_ch_cctv_cam_01a',
        weight: 5000,
        width: 2,
        height: 2,
        tag: 'items.tags.tools',
        shopCount: 0,
        description: 'items[833].description',
        canSeize: true,
        materialType: 'blue',
        materials: 100,
        boxItemId: 836,
        fractionsCantSell: [1, 3, 4, 5, 7]
    },
    {
        // Радар
        id: 834,
        title: 'items[834].title',
        model: 'w_pi_dmvradarmaj',
        hash: 'WEAPON_DMVRADARMAJ',
        weight: 700,
        width: 2,
        height: 2,
        tag: 'items.tags.tools',
        description: 'items[834].description',
        takeable: true,
        canSeize: true,
        isMelee: true,
        caliber: true,
        dropOnDeath: true,
        limit: 7000,
        materialType: 'blue',
        materials: 150,
        boxItemId: 837,
        fractionsCantSell: [1, 3, 4, 5, 7]
    },
    {
        // Радар-детектор
        id: 835,
        title: 'items[835].title',
        model: 'w_am_hackdevice_m32',
        weight: 1000,
        width: 1,
        height: 1,
        tag: 'items.tags.tools',
        shopCount: 0,
        description: 'items[835].description',
        canSeize: true,
    },
    {
        // Ящик с камерами
        id: 836,
        title: 'items[836].title',
        model: 'gr_prop_gr_rsply_crate03a',
        weight: 130,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[836].description',
        isSupplyBox: true
    },
    {
        // Ящик с Радарами
        id: 837,
        title: 'items[837].title',
        model: 'gr_prop_gr_rsply_crate03a',
        weight: 130,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 500,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[837].description',
        isSupplyBox: true
    },
    {
        // Поврежденная камера
        id: 838,
        title: 'items[838].title',
        model: 'ch_prop_ch_cctv_cam_01a',
        weight: 5000,
        width: 2,
        height: 2,
        tag: 'items.tags.tools',
        shopCount: 0,
        description: 'items[838].description',
        dropOnDeath: true,
        dropOnQuit: true,
        canSeize: true,
    },
    {
        // Профессиональное удилище
        id: 839,
        title: 'items[839].title',
        model: 'maj_fish_rod_3',
        weight: 350,
        width: 4,
        height: 1,
        limit: 1750, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[839].description',
        fishing: {
            type: 'rod', // spinning
            level: 5,
            maxFishWeight: 2800,
            maxReelLevel: 5,
            price: 50000,
            multipliers: {
                sliderSpeed: -0.2 // Как быстро бегает бегунок на забросе
            }
        }
    },
    {
        // Базовый спиннинг
        id: 840,
        title: 'items[840].title',
        model: 'maj_fish_rod_v2_1',
        weight: 750,
        width: 4,
        height: 1,
        limit: 1000, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[840].description',
        fishing: {
            type: 'spinning', // rod
            level: 3,
            maxFishWeight: 2350,
            maxReelLevel: 4,
            price: 15000,
            multipliers: {
                sliderSpeed: -0.12 // Как быстро бегает бегунок на забросе
            }
        }
    },
    {
        // Cпиннинг любителя
        id: 841,
        title: 'items[841].title',
        model: 'maj_fish_rod_v2_2',
        weight: 700,
        width: 4,
        height: 1,
        limit: 1250, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[841].description',
        fishing: {
            type: 'spinning', // rod
            level: 4,
            maxFishWeight: 2700,
            maxReelLevel: 5,
            price: 25000,
            multipliers: {
                sliderSpeed: -0.17 // Как быстро бегает бегунок на забросе
            }
        }
    },
    {
        // Улучшенный спиннинг
        id: 842,
        title: 'items[842].title',
        model: 'maj_fish_rod_v2_3',
        weight: 650,
        width: 4,
        height: 1,
        limit: 1500, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[842].description',
        fishing: {
            type: 'spinning', // rod
            level: 5,
            maxFishWeight: 3100,
            maxReelLevel: 6,
            price: 57000,
            discount: 10,
            multipliers: {
                sliderSpeed: -0.22 // Как быстро бегает бегунок на забросе
            }
        }
    },
    {
        // Усиленный спиннинг
        id: 843,
        title: 'items[843].title',
        model: 'maj_fish_rod_v2_4',
        weight: 600,
        width: 4,
        height: 1,
        limit: 1750, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[843].description',
        fishing: {
            type: 'spinning', // rod
            level: 6,
            maxFishWeight: 3500,
            maxReelLevel: 7,
            price: 85000,
            multipliers: {
                sliderSpeed: -0.25 // Как быстро бегает бегунок на забросе
            }
        }
    },
    {
        // Сверхпрочный спиннинг
        id: 844,
        title: 'items[844].title',
        model: 'maj_fish_rod_v2_5',
        weight: 550,
        width: 4,
        height: 1,
        limit: 2250, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[844].description',
        fishing: {
            type: 'spinning', // rod
            level: 7,
            maxFishWeight: 3800,
            maxReelLevel: 8,
            price: 125000,
            multipliers: {
                sliderSpeed: -0.3 // Как быстро бегает бегунок на забросе
            }
        }
    },
    {
        // Коллекционный спиннинг
        id: 845,
        title: 'items[845].title',
        model: 'maj_fish_rod_v2_7',
        weight: 500,
        width: 4,
        height: 1,
        limit: 3000, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[845].description',
        fishing: {
            type: 'spinning', // rod
            level: 8,
            maxFishWeight: 3900,
            maxReelLevel: 8,
            price: 250000,
            multipliers: {
                sliderSpeed: -0.35 // Как быстро бегает бегунок на забросе
            }
        }
    },
    {
        // Профессиональный спиннинг
        id: 846,
        title: 'items[846].title',
        model: 'maj_fish_rod_v2_6',
        weight: 350,
        width: 4,
        height: 1,
        limit: 3500, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[846].description',
        fishing: {
            type: 'spinning', // rod
            level: 9,
            maxFishWeight: 7000,
            maxReelLevel: 9,
            price: 350000,
            multipliers: {
                sliderSpeed: -0.4 // Как быстро бегает бегунок на забросе
            }
        }
    },
    {
        // Кукуруза (наживка)
        id: 847,
        title: 'items[847].title',
        model: 'prop_corn',
        weight: 3,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[847].description',
        fishing: {
            for: 'rod',
            level: 1,
            price: 500,
            efficiency: [2, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: -0.05
            }
        }
    },
    {
        // Тесто
        id: 848,
        title: 'items[848].title',
        model: 'maj_fish_bait_2',
        weight: 5,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[848].description',
        fishing: {
            for: 'rod',
            level: 1,
            price: 400,
            efficiency: [1, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: 0
            }
        }
    },
    {
        // Хлеб
        id: 849,
        title: 'items[849].title',
        model: 'maj_fish_bait_2',
        weight: 4,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[849].description',
        fishing: {
            for: 'rod',
            level: 1,
            price: 400,
            efficiency: [1, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: 0,
                trash: -0.3, // множитель вылова мусора
            }
        }
    },
    {
        // Сверчки
        id: 850,
        title: 'items[850].title',
        model: 'maj_fish_bait_2',
        weight: 2,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[850].description',
        fishing: {
            for: 'rod',
            level: 2,
            price: 800,
            efficiency: [2, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: -0.05,
                trash: -0.3, // множитель вылова мусора
            }
        }
    },
    {
        // Мотыль
        id: 851,
        title: 'items[851].title',
        model: 'maj_fish_bait_2',
        weight: 3,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[851].description',
        fishing: {
            for: 'rod',
            level: 2,
            price: 900,
            discount: 15,
            efficiency: [1, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: 0,
                trophy: 0.5, // множитель вылова трофейной рыбы
            }
        }
    },
    {
        // Опарыши
        id: 852,
        title: 'items[852].title',
        model: 'maj_fish_bait_2',
        weight: 2,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[852].description',
        fishing: {
            for: 'rod',
            level: 2,
            price: 1200,
            discount: 10,
            efficiency: [3, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: -0.1,
                components: 0.5, // множитель вылова компонентов
            }
        }
    },
    {
        // Куски рыбы
        id: 853,
        title: 'items[853].title',
        model: 'maj_fish_bait_2',
        weight: 7,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[853].description',
        fishing: {
            for: 'rod',
            level: 3,
            efficiency: [3, 5],
            multipliers: {
                waitBite: -0.1,
                trophy: 0.75, // множитель вылова трофейной рыбы
            }
        }
    },
    {
        // Лягушка
        id: 854,
        title: 'items[854].title',
        model: 'maj_fish_bait_2',
        weight: 10,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[854].description',
        fishing: {
            for: 'rod',
            level: 3,
            price: 1600,
            efficiency: [2, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: -0.05,
                trash: -0.4, // множитель вылова мусора
            }
        }
    },
    {
        // Речной рак
        id: 855,
        title: 'items[855].title',
        model: 'maj_fish_bait_2',
        weight: 12,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[855].description',
        fishing: {
            for: 'rod',
            level: 4,
            price: 2100,
            efficiency: [1, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: 0,
                components: 0.75, // множитель вылова компонентов
            }
        }
    },
    {
        // Креветки
        id: 856,
        title: 'items[856].title',
        model: 'maj_fish_bait_2',
        weight: 6,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[856].description',
        fishing: {
            for: 'rod',
            level: 4,
            price: 2300,
            efficiency: [3, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: -0.1
            }
        }
    },
    {
        // Почки буйвола
        id: 857,
        title: 'items[857].title',
        model: 'maj_fish_bait_2',
        weight: 15,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[857].description',
        fishing: {
            for: 'rod',
            level: 4,
            efficiency: [4, 5],
            multipliers: {
                waitBite: -0.15,
                trophy: 0.5, // множитель вылова трофейной рыбы
            }
        }
    },
    {
        // Живец
        id: 858,
        title: 'items[858].title',
        model: 'maj_fish_bait_2',
        weight: 50,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[858].description',
        fishing: {
            for: 'rod',
            level: 4,
            price: 2500,
            discount: 15,
            efficiency: [2, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: -0.05,
                trash: -0.3, // множитель вылова мусора
            }
        }
    },
    {
        // Мясо лобстера
        id: 859,
        title: 'items[859].title',
        model: 'maj_fish_bait_2',
        weight: 40,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[859].description',
        fishing: {
            for: 'rod',
            level: 5,
            efficiency: [5, 5],
            multipliers: {
                waitBite: -0.2,
                trophy: 0.5, // множитель вылова трофейной рыбы
            }
        }
    },
    {
        // Бойлы
        id: 860,
        title: 'items[860].title',
        model: 'maj_fish_bait_2',
        weight: 30,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[860].description',
        fishing: {
            for: 'rod',
            level: 5,
            price: 2950,
            discount: 10,
            efficiency: [4, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: -0.15,
                trash: -0.5, // множитель вылова мусора
                components: 0.5, // множитель вылова компонентов
            }
        }
    },
    {
        // Пеллетсы
        id: 861,
        title: 'items[861].title',
        model: 'maj_fish_bait_2',
        weight: 25,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[861].description',
        fishing: {
            for: 'rod',
            level: 5,
            efficiency: [5, 5],
            multipliers: {
                waitBite: -0.2,
                trash: -0.5, // множитель вылова мусора
                components: 1, // множитель вылова компонентов
            }
        }
    },
    {
        // Вобла
        id: 862,
        title: 'items[862].title',
        model: 'ribka_tor6',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 714,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[862].description',
        stack: 10000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Краснопёрка
        id: 863,
        title: 'items[863].title',
        model: 'ribka_tor6',
        weight: 1,
        width: 1,
        height: 1,
        boxItemId: 714,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[863].description',
        stack: 10000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Лещ
        id: 864,
        title: 'items[864].title',
        model: 'ribka_tor5',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 714,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[864].description',
        stack: 10000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Плотва
        id: 865,
        title: 'items[865].title',
        model: 'ribka_tor6',
        weight: 1,
        width: 1,
        height: 1,
        boxItemId: 714,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[865].description',
        stack: 10000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Коричневый Сом
        id: 866,
        title: 'items[866].title',
        model: 'ribka_tor3',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 715,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[866].description',
        stack: 10000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Серебряный карась
        id: 867,
        title: 'items[867].title',
        model: 'ribka_tor5',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 715,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[867].description',
        stack: 10000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Речной Окунь
        id: 868,
        title: 'items[868].title',
        model: 'ribka_tor7',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 716,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[868].description',
        stack: 10000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Обыкновенная Щука
        id: 869,
        title: 'items[869].title',
        model: 'ribka_tor1',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 716,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[869].description',
        stack: 10000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Радужная Форель
        id: 870,
        title: 'items[870].title',
        model: 'ribka_tor2',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 716,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[870].description',
        stack: 10000,
        fishing: {
            level: 1,
        }
    },
    {
        // Зеркальный карп
        id: 871,
        title: 'items[871].title',
        model: 'ribka_tor7',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 717,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[871].description',
        stack: 10000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Сом обыкновенный
        id: 872,
        title: 'items[872].title',
        model: 'ribka_tor3',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 717,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[872].description',
        stack: 10000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Токсичный окунь
        id: 873,
        title: 'items[873].title',
        model: 'ribka_tor1',
        weight: 1,
        width: 2,
        height: 1,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[873].description',
        stack: 10000,
        canSeize: true,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Древняя Гинерия
        id: 874,
        title: 'items[874].title',
        model: 'ribka_tor1',
        weight: 1,
        width: 2,
        height: 1,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[874].description',
        stack: 10000,
        canSeize: true,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Рыба призрак
        id: 875,
        title: 'items[875].title',
        model: 'ribka_tor1',
        weight: 1,
        width: 2,
        height: 1,
        tag: 'items.tags.fish',
        description: 'items[875].description',
        stack: 10000,
        canSeize: true,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Судак обыкновенный
        id: 876,
        title: 'items[876].title',
        model: 'ribka_tor7',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 717,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[876].description',
        stack: 20000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Голавль
        id: 877,
        title: 'items[877].title',
        model: 'ribka_tor7',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 718,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[877].description',
        stack: 20000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Сазан
        id: 878,
        title: 'items[878].title',
        model: 'ribka_tor7',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 718,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[878].description',
        stack: 20000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Стерлядь
        id: 879,
        title: 'items[879].title',
        model: 'ribka_tor2',
        weight: 1,
        width: 3,
        height: 1,
        boxItemId: 718,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[879].description',
        stack: 20000,
        canSeize: true,
        fishing: {
            level: 1,
            seizeAward: 0.35, // награда в $ за изъятие (за грамм)
            multipliers: {

            }
        }
    },
    {
        // Жерех
        id: 880,
        title: 'items[880].title',
        model: 'ribka_tor7',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 718,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[880].description',
        stack: 20000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Полосатый Лаврак
        id: 881,
        title: 'items[881].title',
        model: 'ribka_tor1',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 719,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[881].description',
        stack: 20000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Снук Обыкновенный
        id: 882,
        title: 'items[882].title',
        model: 'ribka_tor1',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 719,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[882].description',
        stack: 20000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Альбула
        id: 883,
        title: 'items[883].title',
        model: 'ribka_tor2',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 719,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[883].description',
        stack: 20000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Прибрежный Басс
        id: 884,
        title: 'items[884].title',
        model: 'ribka_tor7',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 719,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[884].description',
        stack: 20000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Стальноголовый Лосось
        id: 885,
        title: 'items[885].title',
        model: 'ribka_tor1',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 720,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[885].description',
        stack: 20000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Круглый Трахинот
        id: 886,
        title: 'items[886].title',
        model: 'ribka_tor4',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 720,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[886].description',
        stack: 25000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Тёмный Горбыль
        id: 887,
        title: 'items[887].title',
        model: 'ribka_tor2',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 720,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[887].description',
        stack: 25000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Барракуда
        id: 888,
        title: 'items[888].title',
        model: 'ribka_tor1',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 720,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[888].description',
        stack: 25000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Красный Горбыль
        id: 889,
        title: 'items[889].title',
        model: 'ribka_tor4',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 721,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[889].description',
        stack: 25000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Тарпон
        id: 890,
        title: 'items[890].title',
        model: 'ribka_tor1',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 721,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[890].description',
        stack: 25000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Марлин
        id: 891,
        title: 'items[891].title',
        model: 'ribka_tor4',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 721,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[891].description',
        stack: 25000,
        fishing: {
            level: 1,
            multipliers: {

            }
        }
    },
    {
        // Рустер
        id: 892,
        title: 'items[892].title',
        model: 'ribka_tor4',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 975,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[892].description',
        stack: 25000,
        canSeize: true,
        fishing: {
            level: 1,
            seizeAward: 0.35, // награда в $ за изъятие (за грамм)
            multipliers: {

            }
        }
    },
    {
        // Сериола
        id: 893,
        title: 'items[893].title',
        model: 'ribka_tor4',
        weight: 1,
        width: 2,
        height: 1,
        boxItemId: 976,
        tag: 'items.tags.fish',
        tags: ['fish'],
        description: 'items[893].description',
        stack: 25000,
        canSeize: true,
        fishing: {
            level: 1,
            seizeAward: 0.3, // награда в $ за изъятие (за грамм)
            multipliers: {

            }
        }
    },
    {
        // Катушка S-1
        id: 894,
        title: 'items[894].title',
        model: 'maj_fish_reel_2',
        weight: 160,
        width: 1,
        height: 1,
        stack: 1,
        limit: 150, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[894].description',
        fishing: {
            //for: 'rod',
            level: 1,
            reelLevel: 1,
            price: 500,
            frictionPower: 2,
        }
    },
    {
        // Катушка S-2
        id: 895,
        title: 'items[895].title',
        model: 'maj_fish_reel_2',
        weight: 240,
        width: 1,
        height: 1,
        stack: 1,
        limit: 500, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[895].description',
        fishing: {
            //for: 'rod',
            level: 2,
            price: 2500,
            frictionPower: 3,
            reelLevel: 2,
            multipliers: {
                //waitBite: -0.15
                difficulty: -0.05 // Множитель сложности
            }
        }
    },
    {
        // Катушка S-3
        id: 896,
        title: 'items[896].title',
        model: 'maj_fish_reel_2',
        weight: 280,
        width: 1,
        height: 1,
        stack: 1,
        limit: 750, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[896].description',
        fishing: {
            //for: 'rod',
            level: 2,
            price: 10000,
            discount: 15,
            frictionPower: 3.5,
            reelLevel: 3,
            multipliers: {
                //waitBite: -0.15
                difficulty: -0.1 // Множитель сложности
            }
        }
    },
    {
        // Катушка S-4
        id: 897,
        title: 'items[897].title',
        model: 'maj_fish_reel_2',
        weight: 300,
        width: 1,
        height: 1,
        stack: 1,
        limit: 1000, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[897].description',
        fishing: {
            //for: 'rod',
            level: 4,
            price: 25000,
            frictionPower: 4,
            reelLevel: 4,
            multipliers: {
                //waitBite: -0.15
                difficulty: -0.15 // Множитель сложности
            }
        }
    },
    {
        // Катушка S-5
        id: 898,
        title: 'items[898].title',
        model: 'maj_fish_reel_2',
        weight: 320,
        width: 1,
        height: 1,
        stack: 1,
        limit: 1250, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[898].description',
        fishing: {
            //for: 'rod',
            level: 5,
            price: 40000,
            frictionPower: 4.5,
            reelLevel: 5,
            multipliers: {
                //waitBite: -0.15
                difficulty: -0.2 // Множитель сложности
            }
        }
    },
    {
        // Катушка S-6
        id: 899,
        title: 'items[899].title',
        model: 'maj_fish_reel_1',
        weight: 550,
        width: 1,
        height: 1,
        stack: 1,
        limit: 1500, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[899].description',
        fishing: {
            for: 'spinning',
            level: 6,
            price: 75000,
            discount: 10,
            frictionPower: 6,
            reelLevel: 6,
            multipliers: {
                //waitBite: -0.15
                difficulty: -0.2 // Множитель сложности
            }
        }
    },
    {
        // Катушка S-7
        id: 900,
        title: 'items[900].title',
        model: 'maj_fish_reel_1',
        weight: 600,
        width: 1,
        height: 1,
        stack: 1,
        limit: 1750, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[900].description',
        fishing: {
            for: 'spinning',
            level: 6,
            price: 90000,
            frictionPower: 7,
            reelLevel: 7,
            multipliers: {
                //waitBite: -0.15
                difficulty: -0.25 // Множитель сложности
            }
        }
    },
    {
        // Катушка S-8
        id: 901,
        title: 'items[901].title',
        model: 'maj_fish_reel_1',
        weight: 700,
        width: 1,
        height: 1,
        stack: 1,
        limit: 2000, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[901].description',
        fishing: {
            for: 'spinning',
            level: 8,
            price: 125000,
            frictionPower: 8,
            reelLevel: 8,
            multipliers: {
                //waitBite: -0.15
                difficulty: -0.3 // Множитель сложности
            }
        }
    },
    {
        // Катушка S-9
        id: 902,
        title: 'items[902].title',
        model: 'maj_fish_reel_1',
        weight: 400,
        width: 1,
        height: 1,
        stack: 1,
        limit: 2250, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[902].description',
        fishing: {
            for: 'spinning',
            level: 9,
            price: 175000,
            frictionPower: 10,
            reelLevel: 9,
            multipliers: {
                //waitBite: -0.15
                difficulty: -0.35 // Множитель сложности
            }
        }
    },
    {
        // Монофильная леска 0.12mm
        id: 903,
        title: 'items[903].title',
        model: 'prop_vintage_filmcan',
        weight: 1,
        width: 1,
        height: 1,
        stack: 150,
        tag: 'items.tags.fishingGear',
        description: 'items[903].description',
        fishing: {
            //for: 'rod',
            level: 1,
            price: 250,
            giveAmount: 150,
            visibility: [1, 5], // Видмость лески 4/5
            maxFishWeight: 1000, // max. вес рыбы 1.5kg (в граммах)
            lineBreak: 2, // Шанс обрыва лески/поводка в процентах
            multipliers: {
                waitBite: 0
            }
        }
    },
    {
        // Монофильная леска 0.23mm
        id: 904,
        title: 'items[904].title',
        model: 'prop_vintage_filmcan',
        weight: 1,
        width: 1,
        height: 1,
        stack: 150,
        tag: 'items.tags.fishingGear',
        description: 'items[904].description',
        fishing: {
            //for: 'rod',
            level: 2,
            price: 500,
            giveAmount: 150,
            visibility: [2, 5], // Видмость лески 4/5
            maxFishWeight: 1600, // max. вес рыбы 1.5kg (в граммах)
            lineBreak: 1.75, // Шанс обрыва лески/поводка в процентах
            multipliers: {
                waitBite: 0.03
            }
        }
    },
    {
        // Флюорокарбоновая леска 0.35mm
        id: 905,
        title: 'items[905].title',
        model: 'prop_vintage_filmcan',
        weight: 1,
        width: 1,
        height: 1,
        stack: 150,
        tag: 'items.tags.fishingGear',
        description: 'items[905].description',
        fishing: {
            //for: 'rod',
            level: 3,
            price: 750,
            giveAmount: 150,
            visibility: [3, 5], // Видмость лески 4/5
            maxFishWeight: 2450, // max. вес рыбы 1.5kg (в граммах)
            lineBreak: 1.75, // Шанс обрыва лески/поводка в процентах
            multipliers: {
                waitBite: 0.06
            }
        }
    },
    {
        // Флюорокарбоновая леска 0.45mm
        id: 906,
        title: 'items[906].title',
        model: 'prop_vintage_filmcan',
        weight: 1,
        width: 1,
        height: 1,
        stack: 150,
        tag: 'items.tags.fishingGear',
        description: 'items[906].description',
        fishing: {
            //for: 'rod',
            level: 5,
            price: 1500,
            discount: 15,
            giveAmount: 150,
            visibility: [4, 5], // Видмость лески 4/5
            maxFishWeight: 3100, // max. вес рыбы 1.5kg (в граммах)
            lineBreak: 1.75, // Шанс обрыва лески/поводка в процентах
            multipliers: {
                waitBite: 0.09
            }
        }
    },
    {
        // Флюорокарбоновая леска 0.5mm
        id: 907,
        title: 'items[907].title',
        model: 'prop_vintage_filmcan',
        weight: 1,
        width: 1,
        height: 1,
        stack: 150,
        tag: 'items.tags.fishingGear',
        description: 'items[907].description',
        fishing: {
            //for: 'rod',
            level: 6,
            price: 2000,
            giveAmount: 150,
            visibility: [5, 5], // Видмость лески 4/5
            maxFishWeight: 3700, // max. вес рыбы 1.5kg (в граммах)
            lineBreak: 1.25, // Шанс обрыва лески/поводка в процентах
            multipliers: {
                waitBite: 0.12
            }
        }
    },
    {
        // Плетеный Шнур 0.3mm
        id: 908,
        title: 'items[908].title',
        model: 'prop_vintage_filmcan',
        weight: 1,
        width: 1,
        height: 1,
        stack: 150,
        tag: 'items.tags.fishingGear',
        description: 'items[908].description',
        fishing: {
            //for: 'rod',
            level: 7,
            price: 2500,
            giveAmount: 150,
            visibility: [3, 5], // Видмость лески 4/5
            maxFishWeight: 3900, // max. вес рыбы 1.5kg (в граммах)
            lineBreak: 1, // Шанс обрыва лески/поводка в процентах
            multipliers: {
                waitBite: 0.06
            }
        }
    },
    {
        // Плетеный Шнур 0.35mm
        id: 909,
        title: 'items[909].title',
        model: 'prop_vintage_filmcan',
        weight: 1,
        width: 1,
        height: 1,
        stack: 150,
        tag: 'items.tags.fishingGear',
        description: 'items[909].description',
        fishing: {
            //for: 'rod',
            level: 9,
            price: 4000,
            giveAmount: 150,
            visibility: [2, 5], // Видмость лески 4/5
            maxFishWeight: 7000, // max. вес рыбы 1.5kg (в граммах)
            lineBreak: 0.9, // Шанс обрыва лески/поводка в процентах
            multipliers: {
                waitBite: 0.03
            }
        }
    },
    {
        // Стандартный крючок
        id: 910,
        title: 'items[910].title',
        model: 'v_serv_bs_gel',
        weight: 10,
        width: 1,
        height: 1,
        stack: 12,
        tag: 'items.tags.fishingGear',
        description: 'items[910].description',
        fishing: {
            for: 'rod',
            level: 1,
            price: 300,
            strength: [1, 5],
            visibility: [1, 5], // Заметность 4/5
            giveAmount: 6,
            multipliers: {
                lineBreak: 0.3
            }
        }
    },
    {
        // Продвинутый крючок
        id: 911,
        title: 'items[911].title',
        model: 'v_serv_bs_gel',
        weight: 10,
        width: 1,
        height: 1,
        stack: 12,
        tag: 'items.tags.fishingGear',
        description: 'items[911].description',
        fishing: {
            for: 'rod',
            level: 2,
            price: 750,
            strength: [2, 5],
            visibility: [2, 5], // Видмость лески 4/5
            giveAmount: 6,
            multipliers: {
                waitBite: 0.03,
                lineBreak: 0.2
            }
        }
    },
    {
        // Универсальный крючок
        id: 912,
        title: 'items[912].title',
        model: 'v_serv_bs_gel',
        weight: 10,
        width: 1,
        height: 1,
        stack: 12,
        tag: 'items.tags.fishingGear',
        description: 'items[912].description',
        fishing: {
            for: 'rod',
            level: 3,
            price: 1000,
            strength: [3, 5],
            visibility: [4, 5], // Видмость лески 4/5
            giveAmount: 6,
            multipliers: {
                waitBite: 0.05,
                lineBreak: 0.1
            }
        }
    },
    {
        // Усиленный крючок
        id: 913,
        title: 'items[913].title',
        model: 'v_serv_bs_gel',
        weight: 10,
        width: 1,
        height: 1,
        stack: 12,
        tag: 'items.tags.fishingGear',
        description: 'items[913].description',
        fishing: {
            for: 'rod',
            level: 4,
            price: 1500,
            discount: 10,
            strength: [3, 5],
            visibility: [5, 5], // Видмость лески 4/5
            giveAmount: 6,
            multipliers: {
                waitBite: 0.07,
                lineBreak: 0.1
            }
        }
    },
    {
        // Сверхпрочный крючок
        id: 914,
        title: 'items[914].title',
        model: 'v_serv_bs_gel',
        weight: 10,
        width: 1,
        height: 1,
        stack: 12,
        tag: 'items.tags.fishingGear',
        description: 'items[914].description',
        fishing: {
            for: 'rod',
            level: 5,
            price: 2500,
            strength: [4, 5],
            visibility: [3, 5], // Видмость лески 4/5
            giveAmount: 6,
            multipliers: {
                waitBite: 0.05,
            }
        }
    },
    {
        // Профессиональный крючок
        id: 915,
        title: 'items[915].title',
        model: 'v_serv_bs_gel',
        weight: 10,
        width: 1,
        height: 1,
        stack: 12,
        tag: 'items.tags.fishingGear',
        description: 'items[915].description',
        fishing: {
            for: 'rod',
            level: 6,
            price: 3000,
            discount: 5,
            strength: [5, 5],
            visibility: [2, 5], // Видмость лески 4/5
            giveAmount: 6,
            multipliers: {
                waitBite: 0.03,
                lineBreak: -0.1
            }
        }
    },
    {
        // Поводок Моно
        id: 916,
        title: 'items[916].title',
        model: 'ng_proc_food_chips01c',
        weight: 10,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[916].description',
        fishing: {
            for: 'spinning',
            level: 3,
            price: 250,
            strength: [1, 5],
            visibility: [5, 5], // Видмость лески 4/5
            multipliers: {
                waitBite: 0.1,
                lineBreak: -0.1
            }
        }
    },
    {
        // Поводок Усиленный Моно
        id: 917,
        title: 'items[917].title',
        model: 'ng_proc_food_chips01c',
        weight: 10,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[917].description',
        fishing: {
            for: 'spinning',
            level: 4,
            price: 650,
            strength: [2, 5],
            visibility: [5, 5], // Видмость лески 4/5
            multipliers: {
                waitBite: 0.1,
                lineBreak: -0.15
            }
        }
    },
    {
        // Поводок Прозрачный Моно
        id: 918,
        title: 'items[918].title',
        model: 'ng_proc_food_chips01c',
        weight: 10,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[918].description',
        fishing: {
            for: 'spinning',
            level: 5,
            price: 900,
            discount: 10,
            strength: [3, 5],
            visibility: [4, 5], // Видмость лески 4/5
            multipliers: {
                waitBite: 0.07,
                lineBreak: -0.2
            }
        }
    },
    {
        // Поводок Флюорокарбоновый
        id: 919,
        title: 'items[919].title',
        model: 'ng_proc_food_chips01c',
        weight: 10,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[919].description',
        fishing: {
            for: 'spinning',
            level: 6,
            price: 1500,
            strength: [3, 5],
            visibility: [4, 5], // Видмость лески 4/5
            multipliers: {
                waitBite: 0.07,
                lineBreak: -0.25
            }
        }
    },
    {
        // Поводок Флюорокарбоновый Прозрачный
        id: 920,
        title: 'items[920].title',
        model: 'ng_proc_food_chips01c',
        weight: 10,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[920].description',
        fishing: {
            for: 'spinning',
            level: 7,
            price: 2000,
            strength: [4, 5],
            visibility: [3, 5], // Видмость лески 4/5
            multipliers: {
                waitBite: 0.05,
                lineBreak: -0.3
            }
        }
    },
    {
        // Поводок Титановый
        id: 921,
        title: 'items[921].title',
        model: 'ng_proc_food_chips01c',
        weight: 10,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[921].description',
        fishing: {
            for: 'spinning',
            level: 8,
            price: 4000,
            discount: 15,
            strength: [4, 5],
            visibility: [2, 5], // Видмость лески 4/5
            multipliers: {
                waitBite: 0.03,
                lineBreak: -0.4
            }
        }
    },
    {
        // Поводок Усиленный Титановый
        id: 922,
        title: 'items[922].title',
        model: 'ng_proc_food_chips01c',
        weight: 10,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[922].description',
        fishing: {
            for: 'spinning',
            level: 9,
            price: 6000,
            strength: [5, 5],
            visibility: [1, 5], // Видмость лески 4/5
            multipliers: {
                lineBreak: -0.4
            }
        }
    },
    {
        // Подсак 2 уровня
        id: 923,
        title: 'items[923].title',
        model: 'prop_poolskimmer',
        weight: 300,
        width: 2,
        height: 1,
        stack: 1,
        limit: 500, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[923].description',
        fishing: {
            level: 2,
            netDistance: 5, // Если шкала 100-netDistance то рыба будет поймана
            multipliers: {
                lineBreak: -0.1
            }
        }
    },
    {
        // Подсак 5 уровня
        id: 924,
        title: 'items[924].title',
        model: 'prop_poolskimmer',
        weight: 400,
        width: 2,
        height: 1,
        stack: 1,
        limit: 750, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[924].description',
        fishing: {
            level: 5,
            netDistance: 9, // Если шкала 100-netDistance то рыба будет поймана
            multipliers: {
                lineBreak: -0.15
            }
        }
    },
    {
        // Подсак 7 уровня
        id: 925,
        title: 'items[925].title',
        model: 'prop_poolskimmer',
        weight: 600,
        width: 2,
        height: 1,
        stack: 1,
        limit: 1000, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[925].description',
        fishing: {
            level: 7,
            netDistance: 13, // Если шкала 100-netDistance то рыба будет поймана
            multipliers: {
                lineBreak: -0.20
            }
        }
    },
    {
        // Подсак 9 уровня
        id: 926,
        title: 'items[926].title',
        model: 'prop_poolskimmer',
        weight: 900,
        width: 2,
        height: 1,
        stack: 1,
        limit: 1500, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[926].description',
        fishing: {
            level: 9,
            netDistance: 17, // Если шкала 100-netDistance то рыба будет поймана
            multipliers: {
                lineBreak: -0.25
            }
        }
    },
    {
        // Базовое удилище
        id: 927,
        title: 'items[927].title',
        model: 'maj_fish_rod_1',
        weight: 400,
        width: 3,
        height: 1,
        limit: 150, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[927].description',
        fishing: {
            type: 'rod', // spinning
            level: 1,
            maxFishWeight: 710,
            maxReelLevel: 2,
            price: 750,
        }
    },
    {
        // Продвинутое удилище
        id: 928,
        title: 'items[928].title',
        model: 'maj_fish_rod_4',
        weight: 500,
        width: 3,
        height: 1,
        limit: 1000, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[928].description',
        fishing: {
            type: 'rod', // spinning
            level: 2,
            maxFishWeight: 1700,
            maxReelLevel: 3,
            price: 5000,
            multipliers: {
                sliderSpeed: -0.05 // Как быстро бегает бегунок на забросе
            }
        }
    },
    {
        // Удилище любителя
        id: 929,
        title: 'items[929].title',
        model: 'maj_fish_rod_5',
        weight: 600,
        width: 4,
        height: 1,
        limit: 1250, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[929].description',
        fishing: {
            type: 'rod', // spinning
            level: 3,
            maxFishWeight: 2000,
            maxReelLevel: 3,
            price: 13000,
            multipliers: {
                sliderSpeed: -0.1 // Как быстро бегает бегунок на забросе
            }
        }
    },
    {
        // Улучшенное удилище
        id: 930,
        title: 'items[930].title',
        model: 'maj_fish_rod_2',
        weight: 700,
        width: 4,
        height: 1,
        limit: 1500, // Прочность
        tag: 'items.tags.fishingGear',
        description: 'items[930].description',
        fishing: {
            type: 'rod', // spinning
            level: 4,
            maxFishWeight: 2500,
            maxReelLevel: 4,
            price: 30000,
            discount: 10,
            multipliers: {
                sliderSpeed: -0.15 // Как быстро бегает бегунок на забросе
            }
        }
    },
    {
        // Черви
        id: 931,
        title: 'items[931].title',
        model: 'mj_whorms_red',
        weight: 10,
        width: 1,
        height: 1,
        stack: 20,
        tag: 'items.tags.fishingGear',
        description: 'items[931].description',
        usable: true,
        fishing: {
            for: 'rod',
            level: 1,
            price: 700,
            efficiency: [2, 5],
            giveAmount: 20,
            multipliers: {
                waitBite: 0,
                trophy: 0.5, // множитель вылова трофейной рыбы
            }
        },
        stats: {
            hunger: 2,
            poisonChance: 95
        },
        useItem: {
            animationProp: null,
            animationDict: 'mp_player_int_uppersmoke',
            animationName: 'mp_player_int_smoke_enter',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: null,
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_pills',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 1500
        }
    },
    {
        // Виброхвост
        id: 932,
        title: 'items[932].title',
        model: 'maj_fish_bait_1',
        weight: 30,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[932].description',
        fishing: {
            for: 'spinning',
            level: 3,
            price: 1750,
            efficiency: [1, 5],
            multipliers: {
                waitBite: 0,
                trash: -0.3, // множитель вылова мусора
            }
        }
    },
    {
        // Отвесная блесна
        id: 933,
        title: 'items[933].title',
        model: 'maj_fish_bait_1',
        weight: 60,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[933].description',
        fishing: {
            for: 'spinning',
            level: 3,
            price: 3000,
            discount: 15,
            efficiency: [2, 5],
            multipliers: {
                waitBite: -0.05,
                trophy: 0.5, // множитель вылова трофейной рыбы
            }
        }
    },
    {
        // Двухвостый Твистер
        id: 934,
        title: 'items[934].title',
        model: 'maj_fish_bait_1',
        weight: 30,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[934].description',
        fishing: {
            for: 'spinning',
            level: 4,
            price: 4500,
            efficiency: [2, 5],
            multipliers: {
                waitBite: -0.05,
                trash: -0.3, // множитель вылова мусора
            }
        }
    },
    {
        // Крэнк
        id: 935,
        title: 'items[935].title',
        model: 'maj_fish_bait_1',
        weight: 80,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[935].description',
        fishing: {
            for: 'spinning',
            level: 4,
            price: 3200,
            discount: 5,
            efficiency: [1, 5],
            multipliers: {
                waitBite: 0,
                components: 0.5, // множитель вылова компонентов
            }
        }
    },
    {
        // Поппер-Лягушка
        id: 936,
        title: 'items[936].title',
        model: 'maj_fish_bait_1',
        weight: 60,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[936].description',
        fishing: {
            for: 'spinning',
            level: 4,
            efficiency: [3, 5],
            multipliers: {
                waitBite: -0.1,
                trophy: 0.5, // множитель вылова трофейной рыбы
            }
        }
    },
    {
        // Колеблющаяся блесна
        id: 937,
        title: 'items[937].title',
        model: 'maj_fish_bait_1',
        weight: 60,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[937].description',
        fishing: {
            for: 'spinning',
            level: 5,
            efficiency: [3, 5],
            multipliers: {
                waitBite: -0.1,
                components: 0.5, // множитель вылова компонентов
            }
        }
    },
    {
        // Личинка
        id: 938,
        title: 'items[938].title',
        model: 'maj_fish_bait_1',
        weight: 30,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[938].description',
        fishing: {
            for: 'spinning',
            level: 5,
            price: 6000,
            efficiency: [2, 5],
            multipliers: {
                waitBite: -0.05,
                trash: -0.4, // множитель вылова мусора
            }
        }
    },
    {
        // Средняя блесна
        id: 939,
        title: 'items[939].title',
        model: 'maj_fish_bait_1',
        weight: 80,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[939].description',
        fishing: {
            for: 'spinning',
            level: 6,
            price: 10000,
            discount: 20,
            efficiency: [1, 5],
            multipliers: {
                waitBite: 0,
                trash: -0.4, // множитель вылова мусора
            }
        }
    },
    {
        // Узкая блесна
        id: 940,
        title: 'items[940].title',
        model: 'maj_fish_bait_1',
        weight: 70,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[940].description',
        fishing: {
            for: 'spinning',
            level: 6,
            efficiency: [4, 5],
            multipliers: {
                waitBite: -0.15,
                trophy: 0.75, // множитель вылова трофейной рыбы
            }
        }
    },
    {
        // Джерк
        id: 941,
        title: 'items[941].title',
        model: 'maj_fish_bait_1',
        weight: 70,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[941].description',
        fishing: {
            for: 'spinning',
            level: 6,
            price: 13500,
            discount: 10,
            efficiency: [3, 5],
            multipliers: {
                waitBite: -0.1,
                components: 0.75, // множитель вылова компонентов
            }
        }
    },
    {
        // Топпер
        id: 942,
        title: 'items[942].title',
        model: 'maj_fish_bait_1',
        weight: 90,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[942].description',
        fishing: {
            for: 'spinning',
            level: 7,
            price: 16000,
            efficiency: [3, 5],
            multipliers: {
                waitBite: -0.07,
                trophy: 1, // множитель вылова трофейной рыбы

            }
        }
    },
    {
        // Тритон
        id: 943,
        title: 'items[943].title',
        model: 'maj_fish_bait_1',
        weight: 40,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[943].description',
        fishing: {
            for: 'spinning',
            level: 7,
            efficiency: [4, 5],
            multipliers: {
                waitBite: -0.15,
                components: 0.75, // множитель вылова компонентов
            }
        }
    },
    {
        // Джиговая блесна
        id: 944,
        title: 'items[944].title',
        model: 'maj_fish_bait_1',
        weight: 100,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[944].description',
        fishing: {
            for: 'spinning',
            level: 7,
            price: 14000,
            efficiency: [1, 5],
            multipliers: {
                waitBite: 0,
                trash: -0.5, // множитель вылова мусора
            }
        }
    },
    {
        // Безбородочная Вертушка
        id: 945,
        title: 'items[945].title',
        model: 'maj_fish_bait_1',
        weight: 120,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[945].description',
        fishing: {
            for: 'spinning',
            level: 8,
            price: 22000,
            discount: 10,
            efficiency: [3, 5],
            multipliers: {
                waitBite: -0.1,
                trash: -0.5, // множитель вылова мусора
            }
        }
    },
    {
        // Свимбэйт
        id: 946,
        title: 'items[946].title',
        model: 'maj_fish_bait_1',
        weight: 120,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[946].description',
        fishing: {
            for: 'spinning',
            level: 8,
            efficiency: [5, 5],
            multipliers: {
                waitBite: -0.2,
                trophy: 1, // множитель вылова трофейной рыбы
            }
        }
    },
    {
        // Воблер
        id: 947,
        title: 'items[947].title',
        model: 'maj_fish_bait_1',
        weight: 100,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[947].description',
        fishing: {
            for: 'spinning',
            level: 8,
            price: 17000,
            efficiency: [2, 5],
            multipliers: {
                waitBite: -0.05,
                components: 1, // множитель вылова компонентов
            }
        }
    },
    {
        // Вертушка-пуля
        id: 948,
        title: 'items[948].title',
        model: 'maj_fish_bait_1',
        weight: 120,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[948].description',
        fishing: {
            for: 'spinning',
            level: 9,
            efficiency: [5, 5],
            multipliers: {
                waitBite: -0.2,
                trophy: 1, // множитель вылова трофейной рыбы
                components: 1, // множитель вылова компонентов
            }
        }
    },
    {
        // Жемчуг
        id: 949,
        title: 'items[949].title',
        model: 'p_jewel_necklace01_s',
        tag: 'items.tags.fishingGear',
        weight: 150,
        width: 1,
        height: 1,
        stack: 10,
        description: 'items[949].description',
    },
    {
        // Рыболовная сеть
        id: 950,
        title: 'items[950].title',
        model: 'prop_loose_rag_01',
        tag: 'items.tags.fishingGear',
        weight: 150,
        width: 1,
        height: 1,
        stack: 10,
        description: 'items[950].description',
    },
    {
        // Морская раковина
        id: 951,
        title: 'items[951].title',
        model: 'hei_heist_acc_box_trinket_02',
        tag: 'items.tags.fishingGear',
        weight: 150,
        width: 1,
        height: 1,
        stack: 10,
        description: 'items[951].description',
    },
    {
        // Красный коралл
        id: 952,
        title: 'items[952].title',
        model: 'sf_prop_sf_jewel_01a',
        tag: 'items.tags.fishingGear',
        weight: 150,
        width: 1,
        height: 1,
        stack: 10,
        description: 'items[952].description',
    },
    {
        // Бомба-липучка
        id: 953,
        title: 'items[953].title',
        model: 'w_ex_pe',
        weight: 1000,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 953,
        materials: 10000,
        hash: 'weapon_stickybomb',
        stack: 10,
        limit: 100,
        description: 'items[953].description'
    },
    {
        // Бесконтактная мина
        id: 954,
        title: 'items[954].title',
        model: 'w_ex_proxmine',
        weight: 1000,
        width: 2,
        height: 1,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 954,
        materials: 10000,
        hash: 'weapon_proxmine',
        stack: 10,
        limit: 100,
        description: 'items[954].description'
    },
    {
        // Плоская блесна
        id: 955,
        title: 'items[955].title',
        model: 'maj_fish_bait_1',
        weight: 60,
        width: 1,
        height: 1,
        stack: 1,
        tag: 'items.tags.fishingGear',
        description: 'items[955].description',
        fishing: {
            for: 'spinning',
            level: 5,
            price: 5000,
            discount: 10,
            efficiency: [1, 5],
            multipliers: {
                waitBite: 0,
                trophy: 0.75, // множитель вылова трофейной рыбы
            }
        }
    },
    {
        // Набор самореанимации
        id: 956,
        title: 'items[956].title',
        model: 'v_ret_ta_firstaid',
        weight: 400,
        width: 2,
        height: 2,
        demorganSafe: true,
        canSeize: false,
        stack: 1,
        tag: 'items.tags.misc',
        description: 'items[956].description'
    },
    {
        // Глушилка связи
        id: 957,
        title: 'items[957].title',
        model: 'prop_cs_walkie_talkie',
        weight: 980,
        width: 1,
        height: 2,
        tag: 'items.tags.misc',
        usable: true,
        canToggle: true,
        canSeize: false,
        demorganSafe: true,
        limit: 1080,
        description: 'items[957].description'
    },
    {
        // Улучшенная рация
        id: 958,
        title: 'items[958].title',
        model: 'prop_cs_hand_radio',
        weight: 450,
        width: 1,
        height: 2,
        takeable: true,
        tag: 'items.tags.misc',
        description: 'items[958].description'
    },
    {
        // Точильный камень
        id: 959,
        title: 'items[959].title',
        model: 'prop_rock_5_smash1',
        weight: 920,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[959].description',
        usable: true
    },
    {
        // Энергетик
        id: 960,
        title: 'items[960].title',
        model: 'h4_prop_h4_can_beer_01a',
        weight: 450,
        width: 1,
        height: 2,
        stack: 1,
        tag: 'items.tags.misc',
        description: 'items[960].description',
        usable: true,
        stats: {
            water: 40
        },
        buffs: [
            {
                name: 'EnergyDrink',
                duration: 15 * 60 * 1000
            }
        ],
        useItem: {
            animationProp: 'energyDrink',
            animationDict: 'mp_player_intdrink',
            animationName: 'loop',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'energyDrink',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_drink',

            usedSound: 'drink_used',
            usingSound: 'drink_using',

            duration: 5000
        }
    },
    {
        // GPS трекер
        id: 961,
        title: 'items[961].title',
        model: 'lr_prop_carkey_fob',
        weight: 220,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[961].description'
    },
    {
        // Ремень безопасности для мотоцикла
        id: 962,
        title: 'items[962].title',
        model: 'prop_cs_leg_chain_01',
        weight: 1800,
        width: 2,
        height: 3,
        tag: 'items.tags.misc',
        description: 'items[962].description'
    },
    {
        // Сверхтяжелый бронежилет
        id: 963,
        title: 'items[963].title',
        model: 'prop_bodyarmour_02',
        weight: 6000,
        width: 4,
        height: 3,
        canSeize: true,
        armour: 150,
        tag: 'items.tags.havyArmor',
        description: 'items[963].description',
        buffs: [
            {
                name: 'Overload',
            }
        ],
    },
    {
        // Ключница
        id: 964,
        title: 'items[964].title',
        description: 'items[964].description',
        model: 'prop_ld_wallet_pickup',
        weight: 300,
        width: 1,
        height: 1,
        tag: 'items.tags.misc',
        canSeize: false
    },
    {
        // Указ о налоговых льготах
        id: 965,
        title: 'items[965].title',
        model: 'prop_cash_envelope_01',
        weight: 100,
        width: 2,
        height: 2,
        usable: true,
        tag: 'items.tags.documents',
        description: 'items[965].description',
        buffs: [
            {
                name: 'Taxes',
                duration: 30 * 24 * 60 * 60 * 1000
            }
        ]
    },
    {
        // Смена номера
        id: 966,
        title: 'items[966].title',
        model: 'p_num_plate_01',
        weight: 500,
        width: 2,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[966].description',
        usable: true,
        canDrag: false,
        canDragCop: false,
        canPickupToBag: false
    },
    {
        // Самокат
        id: 967,
        title: 'items[967].title',
        model: 'mj_electric_scooter',
        weight: 2500,
        width: 3,
        height: 3,
        takeable: true,
        animateItem: {
            flag: 32 + 1,
            playerAnimDict: 'majestic_animations_props_custom_2',
            playerAnimName: 'lil_electric_scooter',
            propSet: 'scooter'
        },
        tag: 'items.tags.misc',
        description: 'items[967].description'
    },
    {
        // Пакет для улик
        id: 968,
        title: 'items[968].title',
        description: 'items[968].description',
        model: 'h4_prop_h4_keys_jail_01a',
        weight: 300,
        width: 2,
        height: 2,
        tag: 'items.tags.misc',
    },
    {
        // Бонг Боба Марли
        id: 969,
        title: 'items[58].title',
        model: 'prop_bong_01',
        weight: 700,
        width: 1,
        height: 2,
        canSeize: true,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[58].description',
        buffs: [
            {
                name: 'BongMarley'
            }
        ]
    },
    {
        // Бонг Обычный
        id: 970,
        title: 'items[58].title',
        model: 'prop_bong_01',
        weight: 700,
        width: 1,
        height: 2,
        canSeize: true,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[58].description',
        buffs: [
            {
                name: 'BongCommon'
            }
        ]
    },
    {
        // Бонг Необычный
        id: 971,
        title: 'items[58].title',
        model: 'prop_bong_01',
        weight: 700,
        width: 1,
        height: 2,
        canSeize: true,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[58].description',
        buffs: [
            {
                name: 'BongUncommon'
            }
        ]
    },
    {
        // Бонг Редкий
        id: 972,
        title: 'items[58].title',
        model: 'prop_bong_01',
        weight: 700,
        width: 1,
        height: 2,
        canSeize: true,
        takeable: true,
        tag: 'items.tags.facilities',
        description: 'items[58].description',
        buffs: [
            {
                name: 'BongRare'
            }
        ]
    },
    {
        // Мушкет
        id: 973,
        title: 'items[973].title',
        model: 'w_ar_musket',
        weight: 2100,
        width: 5,
        height: 2,
        takeable: true,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: 38,
        hash: 'weapon_musket',
        isMelee: false,
        limit: 500,
        description: 'items[973].description'
    },
    {
        // Монета
        id: 974,
        title: 'items[974].title',
        model: 'ch_prop_arcade_fortune_coin_01a',
        weight: 7,
        width: 1,
        height: 1,
        coins: 25,
        tag: 'items.tags.misc',
        description: 'items[974].description',
        canSeize: false,
        stack: 1000,
    },
    {
        // Ящик с Рустер
        id: 975,
        title: 'items[975].title',
        model: 'mj_box_addon_fish',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 30000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[975].description'
    },
    {
        // Ящик с Сериола
        id: 976,
        title: 'items[976].title',
        model: 'mj_box_addon_fish',
        weight: 1,
        width: 4,
        height: 3,
        tag: 'items.tags.box',
        stack: 30000,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        description: 'items[976].description'
    },
    {
        // Жареное мясо высокого качества
        id: 977,
        title: 'items[977].title',
        model: 'v_ind_meatboxsml',
        weight: 300,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        stack: 3,
        tag: 'items.tags.food',
        description: 'items[977].description',
        stats: {
            hunger: 70,
            water: 70,
            hp: 10,
            poisonChance: 2
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        }
    },
    {
        // Жареная рыба высокого качества
        id: 978,
        title: 'items[978].title',
        model: 'ribka_tor1',
        weight: 200,
        usable: true,
        fastSlot: true,
        width: 1,
        height: 1,
        tag: 'items.tags.food',
        description: 'items[978].description',
        stats: {
            hunger: 70,
            water: 70,
            hp: 10,
            poisonChance: 2
        },
        useItem: {
            animationProp: 'steak',
            animationDict: 'mp_player_inteat@burger',
            animationName: 'mp_player_int_eat_burger',

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            crawlingAnimationProp: 'steak',
            crawlingAnimationDict: 'majestic_crawl_crouch',
            crawlingAnimationName: 'crawl_eat',

            usedSound: 'eat_used',
            usingSound: 'eat_using',

            duration: 4000
        },
    },
    {
        // Диплом об окончании Университета
        id: 979,
        title: 'items[979].title',
        description: 'items[979].description',
        model: 'prop_passport_01',
        tag: 'items.tags.documents',
        weight: 100,
        width: 1,
        height: 1,
        canDrag: false,
        usable: true,
        canDragWallet: false,
        forWallet: true,
    },
    {
        // Книга желтая
        id: 980,
        title: 'items[980].title',
        description: 'items[980].description',
        model: 'prop_passport_01',
        tag: 'items.tags.documents',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
    },
    {
        // Книга зеленая
        id: 981,
        title: 'items[981].title',
        description: 'items[981].description',
        model: 'prop_passport_01',
        tag: 'items.tags.documents',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
    },
    {
        // Книга красная
        id: 982,
        title: 'items[982].title',
        description: 'items[982].description',
        model: 'prop_passport_01',
        tag: 'items.tags.documents',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
    },
    {
        // Пакетик с семенами Green
        id: 983,
        title: 'items[983].title',
        model: 'p_meth_bag_01_s',
        weight: 1,
        width: 1,
        height: 1,
        tag: 'items.tags.box',
        stack: 100,
        dropOnDeath: true,
        dropOnQuit: true,
        canDrag: true,
        canDragCop: true,
        canSeize: true,
        description: 'items[983].description'
    },
    {
        // Фото местности на хэллоуин
        id: 984,
        title: 'items[984].title',
        description: 'items[984].description',
        model: 'mj_event_photo_1',
        tag: 'items.tags.misc',
        weight: 200,
        width: 1,
        height: 1,
        usable: true,
        canDrag: false,
    },
    {
        // Древний топор
        id: 985,
        title: 'items[985].title',
        model: 'w_me_stonehatchet',
        weight: 1200,
        width: 1,
        height: 3,
        tag: 'items.tags.ammunition',
        canSeize: true,
        caliber: true,
        takeable: true,
        isTool: true,
        hash: 'weapon_stone_hatchet',
        isMelee: true,
        disableDropOnCapt: true,
        description: 'items[985].description',
    },
    {
        // Гражданский дрон-сканер
        id: 986,
        title: 'items[986].title',
        model: 'xs_prop_arena_drone_02',
        weight: 1500,
        width: 2,
        height: 2,
        takeable: true,
        hasSerial: true,
        canSeize: false,
        demorganSafe: true,
        tag: 'items.tags.facilities',
        materials: 7000,
        description: 'items[986].description'
    },
    {
        // Крем-маска для лица
        id: 987,
        title: 'items[987].title',
        model: 'prop_cs_pills',
        weight: 100,
        width: 1,
        height: 1,
        usable: true,
        demorganRemove: true,
        canSeize: true,
        stack: 10,
        tag: 'items.tags.consumables',
        description: 'items[987].description',
        buffs: [
            {
                name: 'HiddenIdentityCream',
                duration: 30 * 60 * 1000
            }
        ],
        useItem: {
            animationProp: null,
            animationDict: 'majestic_animations_custom_4',
            animationName: 'cream_mask',
            animationFlag: 16,

            disableSprint: true,
            disableShoot: true,
            disableJump: true,

            usingSound: 'face_cream',

            duration: 2400,

            notify: {
                text: 'inventory.events.useCreamMask',
                args: ['success']
            }
        },
    },
    {
        // Карманный ингалятор
        id: 988,
        title: 'items[988].title',
        model: 'ng_proc_inhaler01a',
        weight: 120,
        width: 1,
        deathSafe: true,
        demorganSafe: true,
        height: 1,
        canSeize: false,
        disableDropOnCapt: true,
        tag: 'items.tags.facilities',
        description: 'items[988].description',
    },
    {
        // Стальной кастет
        id: 989,
        title: 'items[989].title',
        model: 'w_me_knuckle',
        weight: 200,
        width: 1,
        height: 1,
        caliber: true,
        takeable: true,
        materials: 10,
        hash: 'weapon_knuckle',
        isMelee: true,
        disableDropOnCapt: true,
        tag: 'items.tags.ammunition',
        demorganSafe: true,
        canSeize: false,
        deathSafe: true,
        description: 'items[989].description',
        buffs: [
            {
                name: 'SteelKnuckle'
            }
        ]
    },
    {
        // Воздушный горн
        id: 990,
        title: 'items[990].title',
        model: 'mj_air_horn_props',
        weight: 250,
        width: 1,
        height: 2,
        limit: 50,
        usable: true,
        demorganSafe: true,
        canSeize: false,
        fastSlot: true,
        deathSafe: true,
        tag: 'items.tags.tools',
        description: 'items[990].description'
    },
    {
        // Набор дверных шокеров
        id: 991,
        title: 'items[991].title',
        model: 'lr_prop_carkey_fob',
        weight: 550,
        width: 2,
        height: 1,
        demorganSafe: true,
        canSeize: false,
        tag: 'items.tags.misc',
        description: 'items[991].description',
    },
    {
        // Автосигнализация
        id: 992,
        title: 'items[992].title',
        model: 'lr_prop_carkey_fob',
        weight: 550,
        width: 1,
        height: 2,
        demorganSafe: true,
        canSeize: false,
        height: 1,
        tag: 'items.tags.misc',
        description: 'items[992].description',
    },

    // Валентинки 
    {
        // Валентинка
        id: 993,
        title: 'items[993].title',
        model: 'prop_cash_envelope_01',
        weight: 100,
        width: 1,
        height: 1,
        demorganSafe: true,
        noStack: true,
        usable: true,
        canSeize: false,
        tag: 'items.tags.misc',
        description: 'items[993].description',
        canEditDescription: true,
        canGift: true,
    },
    {
        // Валентинка с напылением из розового золота
        id: 994,
        title: 'items[994].title',
        model: 'prop_cash_envelope_01',
        weight: 100,
        width: 1,
        height: 1,
        demorganSafe: true,
        noStack: true,
        usable: true,
        canSeize: false,
        tag: 'items.tags.misc',
        description: 'items[994].description',
        canEditDescription: true,
        canGift: true,
    },
    {
        // Валентинка с напылением из жёлтого золота
        id: 995,
        title: 'items[995].title',
        model: 'prop_cash_envelope_01',
        weight: 100,
        width: 1,
        height: 1,
        demorganSafe: true,
        noStack: true,
        usable: true,
        canSeize: false,
        disableDropOnCapt: true,
        tag: 'items.tags.misc',
        description: 'items[995].description',
        canEditDescription: true,
        canGift: true,
    },
    {
        // Валентинка с напылением из платины
        id: 996,
        title: 'items[996].title',
        model: 'prop_cash_envelope_01',
        weight: 100,
        width: 1,
        height: 1,
        demorganSafe: true,
        noStack: true,
        usable: true,
        canSeize: false,
        tag: 'items.tags.misc',
        description: 'items[996].description',
        canEditDescription: true,
        canGift: true,
    },
];
