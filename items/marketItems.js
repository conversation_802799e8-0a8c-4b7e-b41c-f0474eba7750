module.exports = {
    // Пиротехник
    1: {
        npcIds: [146, 218, 262],
        resetDuration: 3600000, // 1h
        selling: [
            // Тут несколько разных видов продажи на рандом
            [ // new year only
                { itemId: 394, amount: 100, giveAmount: 0, price: { min: 1000, max: 3000 } }, // Коробка с фейерверками 1
                { itemId: 395, amount: 80, giveAmount: 0, price: { min: 2500, max: 5000 } }, // Коробка с фейерверками 2
                { itemId: 396, amount: 60, giveAmount: 0, price: { min: 2500, max: 5000 } }, // Коробка с фейерверками 3
                { itemId: 397, amount: 40, giveAmount: 0, price: { min: 3000, max: 6000 } }, // Коробка с фейерверками 4
                //{ itemId: 445, amount: 15, giveAmount: 0, price: { min: 15000, max: 25000 } },
            ],
            [
                { itemId: 394, amount: 150, giveAmount: 0, price: { min: 2000, max: 5000 } }, // Коробка с фейерверками 1
                { itemId: 395, amount: 120, giveAmount: 0, price: { min: 4000, max: 8000 } }, // Коробка с фейерверками 2
                { itemId: 396, amount: 90, giveAmount: 0, price: { min: 4000, max: 8000 } }, // Коробка с фейерверками 3
                { itemId: 397, amount: 60, giveAmount: 0, price: { min: 6000, max: 10000 } }, // Коробка с фейерверками 4
                //{ itemId: 445, amount: 20, giveAmount: 0, price: { min: 25000, max: 35000 } },
            ],
        ]
    },
    // Оружейник
    2: {
        npcIds: [142, 216, 260],
        resetDuration: 18000000, // 5h
        selling: [
            [
                { itemId: 413, amount: 100, giveAmount: 0, price: { min: 500, max: 1500 } },
                { itemId: 208, amount: 200, giveAmount: 0, price: { min: 1000, max: 1500 } },
            ]
        ],
        buying: [
            // Тут несколько разных видов продажи на рандом
            [
                { itemId: 250, limit: 35, condition: 400, price: { min: 500, max: 2000 } }, // weapon_pumpshotgun
                { itemId: 256, limit: 35, condition: 400, price: { min: 500, max: 2000 } }, // weapon_pumpshotgun_mk2
                { itemId: 257, limit: 35, condition: 1000, price: { min: 500, max: 2000 } }, // weapon_assaultshotgun
                { itemId: 247, limit: 50, condition: 400, price: { min: 100, max: 1000 } }, // weapon_sawnoffshotgun
                { itemId: 258, limit: 50, condition: 200, price: { min: 100, max: 1000 } }, // weapon_dbshotgun
                { itemId: 259, limit: 35, condition: 1000, price: { min: 500, max: 3000 } }, // weapon_heavyshotgun
                { itemId: 249, limit: 50, condition: 750, price: { min: 100, max: 1000 } }, // weapon_pistol_mk2
                { itemId: 255, limit: 35, condition: 3000, price: { min: 500, max: 3000 } }, // weapon_appistol
            ],
            [
                { itemId: 244, limit: 50, condition: 50, price: { min: 100, max: 1000 } }, // weapon_stungun
                { itemId: 243, limit: 50, condition: 750, price: { min: 100, max: 1000 } }, // weapon_pistol50
                { itemId: 245, limit: 50, condition: 750, price: { min: 100, max: 1000 } }, // weapon_snspistol_mk2
                { itemId: 260, limit: 50, condition: 750, price: { min: 100, max: 1000 } }, // weapon_heavypistol
                { itemId: 261, limit: 50, condition: 750, price: { min: 100, max: 1000 } }, // weapon_vintagepistol
                { itemId: 262, limit: 35, condition: 1000, price: { min: 500, max: 3000 } }, // weapon_marksmanpistol
                { itemId: 263, limit: 35, condition: 750, price: { min: 500, max: 3000 } }, // weapon_revolver
                { itemId: 264, limit: 35, condition: 2500, price: { min: 500, max: 3000 } }, // weapon_revolver_mk2
                { itemId: 265, limit: 50, condition: 2500, price: { min: 100, max: 1000 } }, // weapon_doubleaction
                { itemId: 266, limit: 35, condition: 15000, price: { min: 500, max: 2000 } }, // weapon_smg
            ],
            [
                { itemId: 267, limit: 35, condition: 1600, price: { min: 500, max: 2000 } }, // weapon_smg_mk2
                { itemId: 268, limit: 35, condition: 3000, price: { min: 500, max: 2000 } }, // weapon_combatpdw
                { itemId: 269, limit: 35, condition: 3000, price: { min: 500, max: 2000 } }, // weapon_machinepistol
                { itemId: 270, limit: 35, condition: 3000, price: { min: 500, max: 3000 } }, // weapon_minismg
                { itemId: 241, limit: 35, condition: 7500, price: { min: 500, max: 3000 } }, // weapon_assaultrifle
                { itemId: 271, limit: 35, condition: 7500, price: { min: 500, max: 3000 } }, // weapon_assaultrifle_mk2
                { itemId: 275, limit: 35, condition: 7500, price: { min: 500, max: 3000 } }, // weapon_carbinerifle
                { itemId: 274, limit: 35, condition: 7500, price: { min: 500, max: 3000 } }, // weapon_carbinerifle_mk2
                { itemId: 248, limit: 35, condition: 7500, price: { min: 500, max: 3000 } }, // weapon_advancedrifle
                { itemId: 273, limit: 35, condition: 7500, price: { min: 500, max: 3000 } }, // weapon_specialcarbine
                { itemId: 595, limit: 35, condition: 7500, price: { min: 500, max: 3000 } }, // weapon_heavyrifle
            ],
            [
                { itemId: 272, limit: 35, condition: 7500, price: { min: 500, max: 3000 } }, // weapon_specialcarbine_mk2
                { itemId: 276, limit: 35, condition: 7500, price: { min: 500, max: 3000 } }, // weapon_bullpuprifle
                { itemId: 277, limit: 35, condition: 7500, price: { min: 500, max: 3000 } }, // weapon_bullpuprifle_mk2
                { itemId: 278, limit: 35, condition: 7500, price: { min: 500, max: 3000 } }, // weapon_compactrifle
                { itemId: 331, limit: 10, condition: 10000, price: { min: 2000, max: 10000 } }, // weapon_mg
                { itemId: 332, limit: 10, condition: 10000, price: { min: 2000, max: 10000 } }, // weapon_combatmg
                { itemId: 333, limit: 10, condition: 10000, price: { min: 2000, max: 10000 } }, // weapon_combatmg_mk2
                { itemId: 326, limit: 10, condition: 7500, price: { min: 1000, max: 5000 } }, // weapon_gusenberg
                { itemId: 329, limit: 10, condition: 750, price: { min: 2000, max: 10000 } }, // weapon_sniperrifle
                { itemId: 328, limit: 10, condition: 500, price: { min: 2000, max: 10000 } }, // weapon_heavysniper_mk2
                { itemId: 337, limit: 10, condition: 1000, price: { min: 2000, max: 10000 } }, // weapon_marksmanrifle_mk2
            ],
        ]
    },
    // Грибник
    3: {
        npcIds: [161, 211, 255],
        resetDuration: 7200000, // 2h
        buying: [
            // Тут несколько разных видов продажи на рандом
            [
                { itemId: 398, limit: 1700, sellAll: true, isStarterJob: true, price: { min: 45, max: 90 } },   // 1 Уровень (Шампиньон обыкновенный)
                { itemId: 399, limit: 1180, sellAll: true, isStarterJob: true, price: { min: 51.5, max: 103 } },  // 2 Уровень (Вёшенка обыкновенная)
                { itemId: 400, limit: 940, sellAll: true, isStarterJob: true, price: { min: 53.5, max: 107 } },  // 3 Уровень (Гипсизигус шахматный)
                { itemId: 401, limit: 710, sellAll: true, isStarterJob: true, price: { min: 107.85, max: 215.7 } },  // 4 Уровень (Мухомор)
                { itemId: 402, limit: 560, sellAll: true, isStarterJob: true, price: { min: 112, max: 224 } },  // 5 Уровень (Подболотник)
                { itemId: 403, limit: 300, sellAll: true, isStarterJob: true, price: { min: 147.7, max: 243 } },  // 6 Уровень (Подберезовик)
                { itemId: 434, limit: 100, sellAll: false, isStarterJob: true, price: { min: 3000, max: 6000 } }, // Золотой гриб (любой уровень)
            ],
            [
                { itemId: 398, limit: 2000, sellAll: true, isStarterJob: true, price: { min: 35.3, max: 82.8 } },  // 1 Уровень (Шампиньон обыкновенный)
                { itemId: 399, limit: 1200, sellAll: true, isStarterJob: true, price: { min: 35.6, max: 78.3 } },  // 2 Уровень (Вёшенка обыкновенная)
                { itemId: 400, limit: 1000, sellAll: true, isStarterJob: true, price: { min: 40.3, max: 86.7 } },  // 3 Уровень (Гипсизигус шахматный)
                { itemId: 401, limit: 900, sellAll: true, isStarterJob: true, price: { min: 77.7, max: 191.9 } }, // 4 Уровень (Мухомор)
                { itemId: 402, limit: 800, sellAll: true, isStarterJob: true, price: { min: 91.5, max: 185.9 } }, // 5 Уровень (Подболотник)
                { itemId: 403, limit: 500, sellAll: true, isStarterJob: true, price: { min: 130.56, max: 208.9 } }, // 6 Уровень (Подберезовик)
                { itemId: 434, limit: 100, sellAll: false, isStarterJob: true, price: { min: 2500, max: 5500 } },  // Золотой гриб
            ],
        ]
    },
    // Эколог, скупка мусора
    4: {
        npcIds: [162, 212, 256],
        resetDuration: 7000000, // 2h
        buying: [
            // Тут несколько разных видов продажи на рандом
            [
                { itemId: 8, limit: 20, price: { min: 15, max: 60 } }, // Отвёртка
                { itemId: 9, limit: 50, price: { min: 10, max: 90 } }, // Скотч
                { itemId: 19, limit: 100, price: { min: 25, max: 100 } }, // Веревка
                { itemId: 66, limit: 50, price: { min: 50, max: 400 } }, // Ключ от автомобиля
                { itemId: 227, limit: 100, price: { min: 150, max: 550 } }, // Банка кукурузы
                { itemId: 233, limit: 50, price: { min: 60, max: 150 } }, // Кабельная стяжка
                { itemId: 237, limit: 100, price: { min: 35, max: 100 } }, // Пустая пачка сигарет
                { itemId: 251, limit: 100, price: { min: 50, max: 120 } }, // Пустой мешок

                { itemId: 802, limit: 200, price: { min: 150, max: 400 } }, // Пластик
                { itemId: 803, limit: 30, price: { min: 200, max: 700 } }, // Лабораторная колба
                { itemId: 804, limit: 8, price: { min: 1000, max: 6000 } }, // Порошок "Toxic"
                { itemId: 527, limit: 1, price: { min: 45000, max: 50000 } }, // Виски "Toxic"
                { itemId: 528, limit: 40, price: { min: 750, max: 1200 } }, // Сигареты "Camel"
                { itemId: 529, limit: 50, price: { min: 1000, max: 2000 } }, // Old Cola
                { itemId: 530, limit: 80, price: { min: 250, max: 750 } }, // Старинная аптечка
                { itemId: 531, limit: 100, price: { min: 250, max: 750 } }, // Старинный Ланч-Бокс
                { itemId: 532, limit: 40, price: { min: 250, max: 750 } }, // Lite Beer
                { itemId: 537, limit: 100, price: { min: 200, max: 500 } }, // Китайское молоко
                { itemId: 538, limit: 25, price: { min: 3000, max: 5000 } }, // Неприличный Вуайеризм

                { itemId: 539, limit: 50, price: { min: 300, max: 750 } }, // Сигареты "Grave"
                { itemId: 533, limit: 10, price: { min: 6000, max: 9000 } }, // Бабочка в банке
                { itemId: 534, limit: 5, price: { min: 10000, max: 14000 } }, // Древний манускрипт
                { itemId: 535, limit: 50, price: { min: 750, max: 1000 } }, // Монета Хаоса
                { itemId: 798, limit: 150, price: { min: 100, max: 225 } }, // Медный провод
                { itemId: 801, limit: 50, price: { min: 100, max: 400 } }, // Бракованная батарейка
                { itemId: 800, limit: 16, price: { min: 1000, max: 2000 } }, // Карта сетевого интерфейса
                { itemId: 14, limit: 2, price: { min: 2000, max: 7000 } }, // Старый навесной замок
                { itemId: 536, limit: 25, price: { min: 1250, max: 2000 } }, // Сломанный блок памяти
                { itemId: 540, limit: 150, price: { min: 500, max: 1000 } }, // Чип с криптовалютой
                { itemId: 805, limit: 100, price: { min: 150, max: 450 } }, // Металлолом 
                { itemId: 806, limit: 30, price: { min: 450, max: 700 } }, // Ржавый механизм
                { itemId: 799, limit: 5, price: { min: 1000, max: 4000 } }, // Сломанный блок управления 
                { itemId: 541, limit: 4, price: { min: 10000, max: 24000 } }, // Сверх прочный контейнер
                { itemId: 542, limit: 20, price: { min: 2500, max: 6500 } }, // Секретные документы
                { itemId: 543, limit: 10, price: { min: 4000, max: 9000 } }, // Хранилище данных
                { itemId: 838, limit: 15, sellAll: true, price: { min: 5000, max: 10000 } }, // Повреждённая камера
                { itemId: 834, limit: 15, sellAll: true, price: { min: 5000, max: 10000 } }, // Радар измерения скорости
            ],
        ]
    },
    // Портной, скупка одежды
    5: {
        npcIds: [144, 217, 261],
        resetDuration: 7600000, // 2h
        buying: [
            // Тут несколько разных видов продажи на рандом
            [
                { itemId: 40, limit: 50, price: { min: 150, max: 1000 } }, // Маска
                { itemId: 41, limit: 50, price: { min: 60, max: 300 } }, // Низ
                { itemId: 42, limit: 50, price: { min: 40, max: 250 } }, // Обувь
                { itemId: 43, limit: 50, price: { min: 75, max: 450 } }, // Аксессуары
                { itemId: 44, limit: 50, price: { min: 60, max: 300 } }, // Майка
                { itemId: 45, limit: 50, price: { min: 50, max: 100 } }, // Верхняя одежда
                { itemId: 46, limit: 50, price: { min: 60, max: 150 } }, // Головной убор
                { itemId: 47, limit: 50, price: { min: 60, max: 200 } }, // Очки
                { itemId: 48, limit: 50, price: { min: 65, max: 400 } }, // Серьги
                { itemId: 49, limit: 50, price: { min: 80, max: 500 } }, // Наручные часы
                { itemId: 50, limit: 50, price: { min: 80, max: 500 } }, // Браслет
            ],
            [
                { itemId: 40, limit: 100, price: { min: 150, max: 500 } }, // Маска
                { itemId: 41, limit: 100, price: { min: 60, max: 150 } }, // Низ
                { itemId: 42, limit: 100, price: { min: 40, max: 190 } }, // Обувь
                { itemId: 43, limit: 100, price: { min: 10, max: 25 } }, // Аксессуары
                { itemId: 44, limit: 100, price: { min: 60, max: 150 } }, // Майка
                { itemId: 45, limit: 100, price: { min: 50, max: 100 } }, // Верхняя одежда
                { itemId: 46, limit: 100, price: { min: 60, max: 100 } }, // Головной убор
                { itemId: 47, limit: 100, price: { min: 60, max: 150 } }, // Очки
                { itemId: 48, limit: 100, price: { min: 65, max: 200 } }, // Серьги
                { itemId: 49, limit: 100, price: { min: 80, max: 250 } }, // Наручные часы
                { itemId: 50, limit: 100, price: { min: 80, max: 250 } }, // Браслет
            ],
        ],
        selling: [
            [
                { itemId: 473, amount: 50, giveAmount: 0, price: { min: 6000, max: 8000 } }, // Кошелёк
            ],
            [
                { itemId: 473, amount: 100, giveAmount: 0, price: { min: 8000, max: 10000 } }, // Кошелёк
            ],
        ]
    },
    // Закусочная
    6: {
        npcIds: [166, 214, 258],
        resetDuration: 3600000, // 1h
        selling: [
            // Тут несколько разных видов продажи на рандом
            [
                { itemId: 62, amount: 200, giveAmount: 0, price: { min: 100, max: 200 } }, // Стакан кофе
                { itemId: 121, amount: 200, giveAmount: 0, price: { min: 100, max: 200 } }, // Пиво Pißwasser
                { itemId: 136, amount: 200, giveAmount: 0, price: { min: 100, max: 200 } }, // Гамбургер
                { itemId: 206, amount: 200, giveAmount: 0, price: { min: 150, max: 235 } }, // Банка eCola
                { itemId: 338, amount: 200, giveAmount: 0, price: { min: 150, max: 250 } }, // Пончики
                { itemId: 345, amount: 200, giveAmount: 0, price: { min: 400, max: 600 } }, // Вишневый пирок
            ],
        ]
    },
    // Механик
    7: {
        npcIds: [165, 213, 257],
        resetDuration: 3600000, // 1h
        selling: [
            [
                { itemId: 99, amount: 500, giveAmount: 0, price: { min: 1000, max: 2000 } }, // Лопата
                { itemId: 579, amount: 500, giveAmount: 0, price: { min: 2000, max: 3500 } }, // Металоискатель
                { itemId: 597, amount: 1, giveAmount: 0, price: { min: 15000, max: 25000 } }, // Бритва

                //{ itemId: 414, amount: 50, giveAmount: 0, price: { min: 1000, max: 3000 } }, // Турбо декодер
                //{ itemId: 415, amount: 50, giveAmount: 0, price: { min: 5000, max: 15000 } }, // Прогроматор ЭБУ
                //{ itemId: 442, amount: 10, giveAmount: 0, price: { min: 15000, max: 20000 } }, // Кабель электронных дверей
                // { itemId: 416, amount: 50, giveAmount: 4, price: { min: 1000, max: 3000 } }, // Шнур I
                // { itemId: 417, amount: 50, giveAmount: 4, price: { min: 3000, max: 6000 } }, // Шнур II
                // { itemId: 418, amount: 50, giveAmount: 4, price: { min: 6000, max: 9000 } }, // Шнур III
                // { itemId: 419, amount: 50, giveAmount: 4, price: { min: 9000, max: 12000 } }, // Шнур VI
                // { itemId: 420, amount: 50, giveAmount: 4, price: { min: 12000, max: 15000 } }, // Шнур V
                // { itemId: 421, amount: 50, giveAmount: 4, price: { min: 15000, max: 18000 } }, // Шнур VI
                // { itemId: 422, amount: 50, giveAmount: 4, price: { min: 21000, max: 25000 } }, // Шнур VII
                // { itemId: 832, amount: 5, giveAmount: 1, price: { min: 5000, max: 10_000 } }, // Кусачки
            ],
            [
                { itemId: 99, amount: 500, giveAmount: 0, price: { min: 1000, max: 2000 } }, // Лопата
                { itemId: 579, amount: 500, giveAmount: 0, price: { min: 2000, max: 3500 } }, // Металоискатель
                { itemId: 597, amount: 2, giveAmount: 0, price: { min: 10000, max: 20000 } }, // Бритва

                //{ itemId: 414, amount: 120, giveAmount: 0, price: { min: 2000, max: 5000 } }, // Турбо декодер
                //{ itemId: 415, amount: 120, giveAmount: 0, price: { min: 7000, max: 20000 } }, // Прогроматор ЭБУ
                //{ itemId: 442, amount: 20, giveAmount: 0, price: { min: 25000, max: 35000 } }, // Кабель электронных дверей
                // { itemId: 416, amount: 120, giveAmount: 5, price: { min: 2000, max: 6000 } }, // Шнур I
                // { itemId: 417, amount: 120, giveAmount: 5, price: { min: 5000, max: 8000 } }, // Шнур II
                // { itemId: 418, amount: 120, giveAmount: 5, price: { min: 8000, max: 11000 } }, // Шнур III
                // { itemId: 419, amount: 120, giveAmount: 5, price: { min: 12000, max: 15000 } }, // Шнур VI
                // { itemId: 420, amount: 120, giveAmount: 5, price: { min: 15000, max: 19000 } }, // Шнур V
                // { itemId: 421, amount: 120, giveAmount: 5, price: { min: 20000, max: 24000 } }, // Шнур VI
                // { itemId: 422, amount: 120, giveAmount: 5, price: { min: 26000, max: 35000 } }, // Шнур VII
                // { itemId: 832, amount: 5, giveAmount: 1, price: { min: 5000, max: 10_000 } }, // Кусачки
            ],
        ],
    },
    // Рождественский ивент
    8: {
        npcIds: [164],
        resetDuration: 3600000, // 1h
        trade: [
            [
                {
                    itemId: 425, // Что выдать
                    amount: 4000, // Доступный лимит
                    giveAmount: 1,  // Кол-во выдаваемого
                    items: [ // Требуемые предметы
                        { itemId: 424, amount: 25 }
                    ]
                },
                {
                    itemId: 425,
                    amount: 4000,
                    giveAmount: 1,
                    items: [
                        { itemId: 593, amount: 5 }
                    ]
                },
                {
                    itemId: 426,
                    amount: 1800,
                    giveAmount: 1,
                    items: [
                        { itemId: 425, amount: 10 }
                    ]
                },
                {
                    itemId: 427,
                    amount: 300,
                    giveAmount: 0,
                    items: [
                        { itemId: 426, amount: 1 }
                    ]
                },
                {
                    itemId: 428,
                    amount: 200,
                    giveAmount: 0,
                    items: [
                        { itemId: 426, amount: 3 }
                    ]
                },
                {
                    itemId: 429,
                    amount: 100,
                    giveAmount: 0,
                    items: [
                        { itemId: 426, amount: 20 }
                    ]
                },
            ],
        ]
    },
    // Ювелир
    9: {
        npcIds: [143, 215, 259],
        resetDuration: 3600000, // 1h
        buying: [
            // Это выгодное предложеин
            [
                { itemId: 446, limit: 8, addFractionSafe: 80, price: { min: 30000, max: 45000 } }, // Золотой слиток
                { itemId: 447, limit: 8, addFractionSafe: 80, price: { min: 75000, max: 112500 } }, // Череп с бриллиантами
                { itemId: 448, limit: 16, addFractionSafe: 80, price: { min: 22500, max: 30000 } }, // Статуэтка пумы
                { itemId: 449, limit: 24, addFractionSafe: 80, price: { min: 15000, max: 22500 } }, // Древняя тарелка
                { itemId: 450, limit: 2, addFractionSafe: 80, price: { min: 150000, max: 300000 } }, // Коллекция швейцарских часов
            ],
            // Это менее выгодное предложение для игрока
            [
                { itemId: 446, limit: 16, addFractionSafe: 80, price: { min: 15000, max: 22500 } }, // Золотой слиток
                { itemId: 447, limit: 12, addFractionSafe: 80, price: { min: 37500, max: 56250 } }, // Череп с бриллиантами
                { itemId: 448, limit: 22, addFractionSafe: 80, price: { min: 11250, max: 15000 } }, // Статуэтка пумы
                { itemId: 449, limit: 36, addFractionSafe: 80, price: { min: 7500, max: 11250 } }, // Древняя тарелка
                { itemId: 450, limit: 4, addFractionSafe: 80, price: { min: 75000, max: 112500 } }, // Коллекция швейцарских часов
            ],
            [ 
                { itemId: 446, limit: 24, addFractionSafe: 80, price: { min: 7500, max: 11250 } }, // Золотой слиток
                { itemId: 447, limit: 16, addFractionSafe: 80, price: { min: 18750, max: 28125 } }, // Череп с бриллиантами
                { itemId: 448, limit: 28, addFractionSafe: 80, price: { min: 5625, max: 7500 } }, // Статуэтка пумы
                { itemId: 449, limit: 48, addFractionSafe: 80, price: { min: 3750, max: 5625 } }, // Древняя тарелка
                { itemId: 450, limit: 6, addFractionSafe: 80, price: { min: 37500, max: 56250 } }, // Коллекция швейцарских часов
            ],
        ]
    },
    // Дровосек
    10: {
        npcIds: [199, 219, 263],
        resetDuration: 3600000, // 1h
        buying: [
            [
                { itemId: 466, limit: 2600, sellAll: true, isStarterJob: true, price: { min: 64, max: 107 } }, // 1 уровень (Сосновое бревно)
                { itemId: 467, limit: 1950, sellAll: true, isStarterJob: true, price: { min: 95, max: 158 } }, // 2 уровень (Дубовое бревно)
                { itemId: 468, limit: 1300, sellAll: true, isStarterJob: true, price: { min: 125, max: 209 } }, // 3 уровень (Березовое бревно)
                { itemId: 469, limit: 1300, sellAll: true, isStarterJob: true, price: { min: 156, max: 261 } }, // 4 уровень (Кленовое бревно)
                { itemId: 470, limit: 10, sellAll: false, isStarterJob: true, price: { min: 3000, max: 8000 } }, // Золотая шишка
            ],
        ],
        selling: [
            [
                { itemId: 131, amount: 100, giveAmount: 0, price: { min: 500, max: 3000 } },
            ],
        ]
    },
    // Рыбак
    11: {
        npcIds: [200, 210, 254],
        resetDuration: 1200000, // 20 min
        buying: [
            [
				{ itemId: 863, limit: 125000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.79, max: 0.84 } }, // Краснопёрка
				{ itemId: 865, limit: 125000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.73, max: 0.78 } }, // Плотва 
				{ itemId: 866, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.6, max: 0.64 } }, // Коричневый сом
				{ itemId: 868, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.4, max: 0.44 } }, // Речной окунь
				{ itemId: 870, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.37, max: 0.4 } }, // Радужная форель
				{ itemId: 872, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.32, max: 0.35 } }, // Сом обыкновенный
				{ itemId: 876, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.42, max: 0.45 } }, // Судак обыкновенный 
				{ itemId: 880, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.43, max: 0.46 } }, // Жерех
				{ itemId: 884, limit: 375000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.39, max: 0.42 } }, // Прибрежный бас
				{ itemId: 882, limit: 375000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.38, max: 0.4 } }, // Снук обыкновенный
				{ itemId: 885, limit: 375000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.33, max: 0.35 } }, // Стальноголовый лосось
				{ itemId: 886, limit: 500000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.36, max: 0.39 } }, // Круглый трахинот
				{ itemId: 889, limit: 500000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.38, max: 0.4 } }, // Красный горбыль
				{ itemId: 891, limit: 500000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.42, max: 0.45 } }, // Марлин
				{ itemId: 892, limit: 500000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.52, max: 0.56 } }, // Рустер
				{ itemId: 874, limit: 125000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.85, max: 0.91 } }, // Древняя гинерия

				{ itemId: 864, limit: 125000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.48, max: 0.52 } }, // Лещ
				{ itemId: 862, limit: 125000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.51, max: 0.55 } }, // Вобла
				{ itemId: 867, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.63, max: 0.68 } }, // Серебряный карась 
				{ itemId: 869, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.36, max: 0.39 } }, // Обыкновенная щука
				{ itemId: 871, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.31, max: 0.34 } }, // Зеркальный карп
				{ itemId: 878, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.36, max: 0.39 } }, // Сазан
				{ itemId: 877, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.41, max: 0.44 } }, // Голавль
				{ itemId: 879, limit: 250000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.45, max: 0.48 } }, // Стерлядь
				{ itemId: 883, limit: 375000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.41, max: 0.45 } }, // Альбула
				{ itemId: 881, limit: 375000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.42, max: 0.46 } }, // Полосатый лаврак
				{ itemId: 888, limit: 500000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.4, max: 0.43 } }, // Барракуда
				{ itemId: 887, limit: 500000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.35, max: 0.38 } }, // Тёмный горбыль
				{ itemId: 890, limit: 500000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.37, max: 0.4 } }, // Тарпон
				{ itemId: 893, limit: 500000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.55, max: 0.6 } }, // Сериола
				{ itemId: 873, limit: 125000, sellAll: true, lic: ['fh'], vipMultiplier: true, isStarterJob: true, price: { min: 0.69, max: 0.72 } }, // Токсичный окунь
            ],
        ]
    },
    // Блиц
    // 12: {
    //     npcId: 232,
    //     resetDuration: 43200000, // 12 часов
    //     buying: [
    //         [
    //             {itemId: 533, limit: 10,  sellAll: true, vipMultiplier: false, price: {min: 8000,  max: 10000}},
    //             {itemId: 534, limit: 5,   sellAll: true, vipMultiplier: false, price: {min: 12000, max: 15000}},
    //             {itemId: 535, limit: 50,  sellAll: true, vipMultiplier: false, price: {min: 1000, max: 1500}},
    //             {itemId: 536, limit: 25,  sellAll: true, vipMultiplier: false, price: {min: 1500, max: 2500}},
    //         ],
    //     ],
    //     selling: [
    //         [
    //             {itemId: 527, amount: 10,  giveAmount: 0,  price: {min: 45000, max: 50000}},
    //             {itemId: 528, amount: 20,  giveAmount: 20, price: {min: 15000, max: 25000}},
    //             {itemId: 529, amount: 50,  giveAmount: 0,  price: {min: 1000,  max: 2000}},
    //             {itemId: 530, amount: 50,  giveAmount: 0,  price: {min: 250,   max: 750}},
    //             {itemId: 531, amount: 100, giveAmount: 0,  price: {min: 250,   max: 750}},
    //             {itemId: 532, amount: 25,  giveAmount: 0,  price: {min: 250,   max: 750}},
    //         ],
    //     ],
    // },
    // 13: {
    //     npcId: 233,
    //     resetDuration: 43200000, // 12 часов
    //     buying: [
    //         [
    //             {itemId: 540, limit: 100,  sellAll: true, vipMultiplier: false, price: {min: 500,   max: 1000}},
    //             {itemId: 541, limit: 5,    sellAll: true, vipMultiplier: false, price: {min: 15000, max: 25000}},
    //             {itemId: 542, limit: 20,   sellAll: true, vipMultiplier: false, price: {min: 3500,  max: 7500}},
    //             {itemId: 543, limit: 10,   sellAll: true, vipMultiplier: false, price: {min: 5000,  max: 10000}},
    //         ],
    //     ],
    //     selling: [
    //         [
    //             {itemId: 537, amount: 25,  giveAmount: 0,  price: {min: 200,  max: 500}},
    //             {itemId: 538, amount: 25,  giveAmount: 3,  price: {min: 3000, max: 5000}},
    //             {itemId: 539, amount: 25,  giveAmount: 20, price: {min: 300,  max: 750}},
    //         ],
    //     ],
    // },
    // Хэллоуинский ивент
    14: {
        npcIds: [163],
        resetDuration: 3600000, // 1h
        // trade: [
        //     [
        //         {
        //             itemId: 408, // Что выдать
        //             amount: 3000, // Доступный лимит
        //             giveAmount: 50,  // Кол-во выдаваемого
        //             items: [ // Требуемые предметы
        //                 { itemId: 407, amount: 100 }
        //             ]
        //         },
        //         {
        //             itemId: 409,
        //             amount: 1000,
        //             giveAmount: 2,
        //             items: [ // Требуемые предметы
        //                 { itemId: 408, amount: 50 }
        //             ]
        //         },
        //         {
        //             itemId: 412,
        //             amount: 500,
        //             giveAmount: 1,
        //             items: [ // Требуемые предметы
        //                 { itemId: 409, amount: 8 }
        //             ]
        //         },
        //         {
        //             itemId: 412,
        //             amount: 100,
        //             giveAmount: 1,
        //             items: [ // Требуемые предметы
        //                 { itemId: 410, amount: 20 }
        //             ]
        //         },
        //         {
        //             itemId: 412,
        //             amount: 100,
        //             giveAmount: 1,
        //             items: [ // Требуемые предметы
        //                 { itemId: 411, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 412,
        //             amount: 50,
        //             giveAmount: 1,
        //             items: [ // Требуемые предметы
        //                 { itemId: 623, amount: 12 }
        //             ]
        //         },
        //         {
        //             itemId: 'animations',
        //             amount: 100,

        //             title: 'animations.list.Reanimated',
        //             animId: 59,
        //             animData: [
        //                 'majestic_animations_4',
        //                 'reanimated'
        //             ],
        //             flag: 1,
        //             looped: true,
        //             music: 'reanimated',

        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 30 }
        //             ]
        //         },
        //         {
        //             itemId: 'animations',
        //             amount: 100,

        //             title: 'animations.list.FrightFunk',
        //             animId: 65,
        //             animData: [
        //                 'majestic_animations_4',
        //                 'fright_funk'
        //             ],
        //             flag: 1,
        //             looped: true,
        //             music: 'fright_funk',

        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 25 }
        //             ]
        //         },
        //         {
        //             itemId: 'animations',
        //             amount: 100,

        //             title: 'animations.list.Howl',
        //             animId: 130,
        //             animData: [
        //                 'majestic_animations_6',
        //                 'howl'
        //             ],
        //             flag: 0,
        //             looped: false,
        //             music: 'howl',

        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 20 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 0, drawable: 2011, texture: 0, isProp: 1 },  // Шляпа ведьмы
        //                 1: { component: 0, drawable: 2011, texture: 0, isProp: 1 },
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 8 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2172, texture: 0, isProp: 0 }, // Окровавленная форма заключенного
        //                 1: { component: 11, drawable: 2175, texture: 0, isProp: 0 },
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 2 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2167, texture: 0, isProp: 0 }, // Окровавленная рубашка врача
        //                 1: { component: 11, drawable: 2173, texture: 0, isProp: 0 }, // Окровавленная рубашка медсестры
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 4 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2175, texture: 0, isProp: 0 }, // Кровавый пиджак
        //                 1: { component: 11, drawable: 2166, texture: 0, isProp: 0 }, // Кровавое боди
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 6 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2168, texture: 0, isProp: 0 }, // Фрак темноты
        //                 1: { component: 11, drawable: 2183, texture: 0, isProp: 0 }, // Жилетка темноты
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 8 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2171, texture: 0, isProp: 0 }, //Пальто короля Хеллоуина
        //                 1: { component: 11, drawable: 2178, texture: 0, isProp: 0 }, // Пальто королевы Хеллоуина
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 10 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2098, texture: 0, isProp: 0 }, // Окровавленные штаны врача
        //                 1: { component: 11, drawable: 2188, texture: 0, isProp: 0 }, // Платье чародейки
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 3 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2094, texture: 0, isProp: 0 }, // Кровавые брюки
        //                 1: { component: 7, drawable: 2048, texture: 0, isProp: 0 }, // Чокер Инь и Ян
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 4 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2096, texture: 0, isProp: 0 }, // Брюки темноты
        //                 1: { component: 4, drawable: 2102, texture: 0, isProp: 0 }, // Брюки темноты
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 5 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2097, texture: 0, isProp: 0 }, // Окровавленные штаны заключенного
        //                 1: { component: 4, drawable: 2097, texture: 0, isProp: 0 }, // Окровавленные штаны заключенной
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 1 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2099, texture: 0, isProp: 0 }, // Штаны короля хеллоуина
        //                 1: { component: 7, drawable: 2065, texture: 0, isProp: 0 }, // Крылья Феи
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 10 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 7, drawable: 2059, texture: 1, isProp: 0 }, // Якорь на цепи
        //                 1: { component: 7, drawable: 2065, texture: 1, isProp: 0 }, // Крылья Феи
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 10 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 7, drawable: 2059, texture: 2, isProp: 0 }, // Якорь на цепи
        //                 1: { component: 7, drawable: 2065, texture: 2, isProp: 0 }, // Крылья Феи
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 10 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 6, drawable: 2052, texture: 0, isProp: 0 }, // Обувь короля хеллоуина
        //                 1: { component: 6, drawable: 2050, texture: 0, isProp: 0 }, // Обувь женская ???
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 409, amount: 30 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 6, drawable: 2048, texture: 0, isProp: 0 }, // Кровавые туфли
        //                 1: { component: 6, drawable: 2051, texture: 0, isProp: 0 }, // Кровавые туфли
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 409, amount: 25 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 6, drawable: 2053, texture: 0, isProp: 0 }, // Хеллоуинские кроссовки
        //                 1: { component: 6, drawable: 2055, texture: 0, isProp: 0 }, // Хеллоуинские кроссовки женские
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 409, amount: 20 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 6, drawable: 2049, texture: 0, isProp: 0 }, // Высокие хеллоуинские ботинки
        //                 1: { component: 6, drawable: 2056, texture: 0, isProp: 0 }, // Обувь женская ???
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 409, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 1, drawable: 2093, texture: 0, isProp: 0 }, // Маска злой тыквы
        //                 1: { component: 1, drawable: 2084, texture: 0, isProp: 0 }, // Маска злой тыквы
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 409, amount: 50 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 1, drawable: 2096, texture: 0, isProp: 0 }, // Маска кровавого клоуна
        //                 1: { component: 1, drawable: 2086, texture: 0, isProp: 0 }, // Маска кровавого клоуна
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 409, amount: 35 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 1, drawable: 2094, texture: 0, isProp: 0 }, // Окровавленная медицинская маска
        //                 1: { component: 7, drawable: 2046, texture: 0, isProp: 0 }, // Окровавленная медицинская маска
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 409, amount: 20 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 7, drawable: 2058, texture: 0, isProp: 0 }, // Кровавый галстук
        //                 1: { component: 7, drawable: 2047, texture: 0, isProp: 0 }, // Ожерелья и серьги
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 4 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 0, drawable: 2043, texture: 0, isProp: 1 }, // Ушки кота бегемота
        //                 1: { component: 0, drawable: 2052, texture: 0, isProp: 1 }, // Ушки кота бегемота
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 10 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 0, drawable: 2044, texture: 0, isProp: 1 }, // Тыквы на пружинках
        //                 1: { component: 0, drawable: 2053, texture: 0, isProp: 1 }, // Тыквы на пружинках
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 0, drawable: 2044, texture: 1, isProp: 1 }, // Тыквы на пружинках
        //                 1: { component: 0, drawable: 2053, texture: 1, isProp: 1 }, // Тыквы на пружинках
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2253, texture: 0, isProp: 0 }, // Майка скелета
        //                 1: { component: 4, drawable: 2156, texture: 0, isProp: 0 }, // Джинсы в огне
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 12 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2254, texture: 1, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2156, texture: 1, isProp: 0 }, // Штаны в огне
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 14 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2151, texture: 1, isProp: 0 }, // Штаны скелета Luminoforo
        //                 1: { component: 4, drawable: 2157, texture: 1, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2254, texture: 2, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2156, texture: 2, isProp: 0 }, // Штаны в огне
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 14 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2151, texture: 2, isProp: 0 }, // Штаны скелета Luminoforo
        //                 1: { component: 4, drawable: 2157, texture: 2, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2254, texture: 3, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2156, texture: 3, isProp: 0 }, // Штаны в огне
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 14 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2151, texture: 4, isProp: 0 }, // Штаны скелета Luminoforo
        //                 1: { component: 4, drawable: 2157, texture: 4, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2254, texture: 4, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2156, texture: 4, isProp: 0 }, // Штаны в огне
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 14 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2254, texture: 5, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2156, texture: 5, isProp: 0 }, // Штаны в огне
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 14 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2151, texture: 5, isProp: 0 }, // Штаны скелета Luminoforo
        //                 1: { component: 4, drawable: 2157, texture: 5, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2151, texture: 3, isProp: 0 }, // Штаны скелета Luminoforo
        //                 1: { component: 4, drawable: 2155, texture: 2, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2254, texture: 0, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2157, texture: 0, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2253, texture: 2, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2155, texture: 3, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2253, texture: 1, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2157, texture: 3, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2253, texture: 3, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2157, texture: 6, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2253, texture: 4, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2157, texture: 7, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 11, drawable: 2253, texture: 6, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2157, texture: 8, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2150, texture: 2, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2157, texture: 9, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2150, texture: 1, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2156, texture: 6, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2150, texture: 3, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2156, texture: 7, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2150, texture: 4, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2156, texture: 8, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2150, texture: 6, isProp: 0 }, // Майка скелета Luminoforo
        //                 1: { component: 4, drawable: 2155, texture: 1, isProp: 0 }, // Хеллоуинская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2150, texture: 0, isProp: 0 }, // Штаны скелета
        //                 1: { component: 4, drawable: 2155, texture: 0, isProp: 0 }, // Викторианская юбка
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 10 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 4, drawable: 2151, texture: 0, isProp: 0 }, // Штаны скелета Luminoforo
        //                 1: { component: 2, drawable: 2023, texture: 0, isProp: 1 }, // Серьги приведение
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 15 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 7, drawable: 2059, texture: 0, isProp: 0 }, // Якорь на цепи
        //                 1: { component: 6, drawable: 2054, texture: 0, isProp: 0 }, // Комплект одежды 6-2054
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 1 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2020, texture: 0, isProp: 0 }, // Рюкзак гробик
        //                 1: { component: 5, drawable: 2021, texture: 0, isProp: 0 }, // Рюкзак гробик
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 25 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2020, texture: 1, isProp: 0 }, // Рюкзак гробик
        //                 1: { component: 5, drawable: 2021, texture: 1, isProp: 0 }, // Рюкзак гробик
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 25 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2020, texture: 2, isProp: 0 }, // Рюкзак гробик
        //                 1: { component: 5, drawable: 2021, texture: 2, isProp: 0 }, // Рюкзак гробик
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 25 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2020, texture: 3, isProp: 0 }, // Рюкзак гробик
        //                 1: { component: 5, drawable: 2021, texture: 3, isProp: 0 }, // Рюкзак гробик
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 25 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2020, texture: 4, isProp: 0 }, // Рюкзак гробик
        //                 1: { component: 5, drawable: 2021, texture: 4, isProp: 0 }, // Рюкзак гробик
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 25 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2021, texture: 0, isProp: 0 }, // Сумочка кошка в тыкве
        //                 1: { component: 5, drawable: 2022, texture: 0, isProp: 0 }, // Сумочка кошка в тыкве
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 20 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2021, texture: 1, isProp: 0 }, // Сумочка кошка в тыкве
        //                 1: { component: 5, drawable: 2022, texture: 1, isProp: 0 }, // Сумочка кошка в тыкве
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 20 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2021, texture: 2, isProp: 0 }, // Сумочка кошка в тыкве
        //                 1: { component: 5, drawable: 2022, texture: 2, isProp: 0 }, // Сумочка кошка в тыкве
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 20 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2021, texture: 3, isProp: 0 }, // Сумочка кошка в тыкве
        //                 1: { component: 5, drawable: 2022, texture: 3, isProp: 0 }, // Сумочка кошка в тыкве
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 20 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2021, texture: 4, isProp: 0 }, // Сумочка кошка в тыкве
        //                 1: { component: 5, drawable: 2022, texture: 4, isProp: 0 }, // Сумочка кошка в тыкве
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 20 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2021, texture: 5, isProp: 0 }, // Сумочка кошка в тыкве
        //                 1: { component: 5, drawable: 2022, texture: 5, isProp: 0 }, // Сумочка кошка в тыкве
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 20 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2021, texture: 6, isProp: 0 }, // Сумочка кошка в тыкве
        //                 1: { component: 5, drawable: 2022, texture: 6, isProp: 0 }, // Сумочка кошка в тыкве
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 20 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2022, texture: 0, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //                 1: { component: 5, drawable: 2023, texture: 0, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 30 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2022, texture: 1, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //                 1: { component: 5, drawable: 2023, texture: 1, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 30 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2022, texture: 2, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //                 1: { component: 5, drawable: 2023, texture: 2, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 30 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2022, texture: 3, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //                 1: { component: 5, drawable: 2023, texture: 3, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 30 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2022, texture: 4, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //                 1: { component: 5, drawable: 2023, texture: 4, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 30 }
        //             ]
        //         },
        //         {
        //             itemId: 'clothes',
        //             amount: 100,
        //             giveAmount: 0,

        //             clothesData: {
        //                 0: { component: 5, drawable: 2022, texture: 5, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //                 1: { component: 5, drawable: 2023, texture: 5, isProp: 0 }, // Сумочка кошка в тыкве Luminoforo
        //             },
        //             items: [ // Требуемые предметы
        //                 { itemId: 412, amount: 30 }
        //             ]
        //         },
        //     ],
        // ],

        // buying: [
        //     [
        //         { itemId: 810, limit: 2600, sellAll: true, price: { min: 75, max: 75 } },
        //         { itemId: 809, limit: 1950, sellAll: true, price: { min: 450, max: 450 } },
        //     ]
        // ]
    },
    // Школьный ивент
    15: {
        npcIds: [140],
        resetDuration: 3600000, // 1h
        trade: [
            [
                {
                    itemId: 621,
                    amount: 1000,
                    giveAmount: 0,
                    items: [
                        { itemId: 158, amount: 20 }
                    ]
                },
                {
                    itemId: 622,
                    amount: 1000,
                    giveAmount: 0,
                    items: [
                        { itemId: 621, amount: 10 }
                    ]
                },
                {
                    itemId: 28,
                    amount: 75,
                    giveAmount: 0,
                    items: [
                        { itemId: 621, amount: 5 }
                    ]
                },
                {
                    itemId: 544,
                    amount: 100,
                    giveAmount: 0,
                    items: [
                        { itemId: 158, amount: 20 }
                    ]
                },
                {
                    itemId: 545,
                    amount: 100,
                    giveAmount: 0,
                    items: [
                        { itemId: 158, amount: 25 }
                    ]
                },
                {
                    itemId: 546,
                    amount: 100,
                    giveAmount: 0,
                    items: [
                        { itemId: 158, amount: 40 }
                    ]
                },
                {
                    itemId: 547,
                    amount: 50,
                    giveAmount: 0,
                    items: [
                        { itemId: 158, amount: 40 }
                    ]
                },
                {
                    itemId: 548,
                    amount: 50,
                    giveAmount: 0,
                    items: [
                        { itemId: 158, amount: 50 }
                    ]
                },
                {
                    itemId: 'clothes',
                    amount: 10,
                    giveAmount: 0,

                    clothesData: {
                        0: { component: 11, drawable: 2132, texture: 0, isProp: 0 },
                        1: { component: 11, drawable: 2131, texture: 0, isProp: 0 },
                    },
                    items: [ // Требуемые предметы
                        { itemId: 622, amount: 2 }
                    ]
                },
                {
                    itemId: 'clothes',
                    amount: 10,
                    giveAmount: 0,

                    clothesData: {
                        0: { component: 11, drawable: 2133, texture: 0, isProp: 0 },
                        1: { component: 11, drawable: 2132, texture: 0, isProp: 0 },
                    },
                    items: [ // Требуемые предметы
                        { itemId: 622, amount: 1 }
                    ]
                },
                {
                    itemId: 'clothes',
                    amount: 10,
                    giveAmount: 0,

                    clothesData: {
                        0: { component: 4, drawable: 2064, texture: 0, isProp: 0 },
                        1: { component: 4, drawable: 2065, texture: 0, isProp: 0 },
                    },
                    items: [ // Требуемые предметы
                        { itemId: 622, amount: 1 }
                    ]
                },
                {
                    itemId: 'clothes',
                    amount: 10,
                    giveAmount: 0,

                    clothesData: {
                        0: { component: 7, drawable: 2056, texture: 0, isProp: 0 },
                        1: { component: 7, drawable: 2045, texture: 0, isProp: 0 },
                    },
                    items: [ // Требуемые предметы
                        { itemId: 621, amount: 8 }
                    ]
                },
            ],
        ]
    },
    // Карьерщик
    16: {
        npcIds: [235],
        resetDuration: 18000000, // 5h
        selling: [
            [
                { itemId: 97, amount: 300, giveAmount: 0, price: { min: 2000, max: 5000 } }
            ]
        ]
    },
    // Фермер
    17: {
        npcIds: [264, 269, 270],
        resetDuration: 7200000, // 2h
        buying: [
            // Тут несколько разных видов продажи на рандом
            [
                { itemId: 378, limit: 14400, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 17.00, max: 23.00 } },   // 1 Уровень (Апельсин)
                { itemId: 387, limit: 2300, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 170.00, max: 185.00 } },  // 2 Уровень (Пшеница)
                { itemId: 379, limit: 940, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 205.00, max: 230.00 } },  // 3 Уровень (Картошка)
                { itemId: 381, limit: 710, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 300.00, max: 315.00 } },  // 4 Уровень (Капуста)
                { itemId: 385, limit: 560, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 410.00, max: 435.00 } },  // 5 Уровень (Кукуруза)
                { itemId: 383, limit: 300, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 590.00, max: 610.00 } },  // 6 Уровень (Тыква)
                { itemId: 389, limit: 200, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 800.00, max: 830.00 } }, // 7 уровень (Банан)
            ],
            [
                { itemId: 378, limit: 16000, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 14.00, max: 19.00 } },   // 1 Уровень (Апельсин)
                { itemId: 387, limit: 4000, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 165.00, max: 180.00 } },  // 2 Уровень (Пшеница)
                { itemId: 379, limit: 1000, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 200.00, max: 225.00 } },  // 3 Уровень (Картошка)
                { itemId: 381, limit: 900, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 295.00, max: 310.00 } },  // 4 Уровень (Капуста)
                { itemId: 385, limit: 800, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 400.00, max: 425.00 } },  // 5 Уровень (Кукуруза)
                { itemId: 383, limit: 500, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 580.00, max: 600.00 } },  // 6 Уровень (Тыква)
                { itemId: 389, limit: 400, sellAll: true, vipMultiplier: true, isStarterJob: true, price: { min: 790.00, max: 820.00 } }, // 7 уровень (Банан)
            ],
        ]
    },
    // Дровосек около лесопилки
    18: {
        npcIds: [77],
        resetDuration: 3600000, // 1h
        selling: [
            [
                { itemId: 131, amount: 300, giveAmount: 0, price: { min: 500, max: 1000 } },
            ],
        ]
    },
    // Грибник около лесопилки
    19: {
        npcIds: [201],
        resetDuration: 3600000, // 1h
        selling: [
            [
                { itemId: 246, amount: 300, giveAmount: 0, price: { min: 300, max: 500 } },
            ],
        ]
    },
    // Фермер
    20: {
        npcIds: [133],
        resetDuration: 3600000, // 1h
        selling: [
            [
                { itemId: 177, amount: 300, giveAmount: 0, price: { min: 1000, max: 2000 } },
            ],
        ]
    },
    // Рыбак
    21: {
        npcIds: [125],
        resetDuration: 3600000, // 1h
        selling: [
            [
                { itemId: 927, amount: 300, giveAmount: 0, price: { min: 600, max: 900 } },
				{ itemId: 903, amount: 300, giveAmount: 150, price: { min: 150, max: 350 } },
				{ itemId: 849, amount: 300, giveAmount: 20, price: { min: 300, max: 550 } },
				{ itemId: 848, amount: 300, giveAmount: 20, price: { min: 250, max: 500 } },
				{ itemId: 894, amount: 300, giveAmount: 0, price: { min: 400, max: 750 } },
				{ itemId: 910, amount: 300, giveAmount: 6, price: { min: 200, max: 450 } },
				{ itemId: 847, amount: 300, giveAmount: 20, price: { min: 350, max: 650 } },
				{ itemId: 931, amount: 300, giveAmount: 20, price: { min: 450, max: 750 } },
            ],
        ]
    },

    // День влюбленных
    // 22: {
    //     npcIds: [345],
    //     resetDuration: 3600000, // 1h
    //     trade: [
    //         [
    //             {
    //                 itemId: 831,
    //                 amount: 250,
    //                 giveAmount: 1,
    //                 items: [
    //                     { itemId: 830, amount: 20 }
    //                 ]
    //             },
    //             {
    //                 itemId: 'clothes',
    //                 amount: 50,
    //                 giveAmount: 0,

    //                 clothesData: {
    //                     0: { component: 11, drawable: 2409, texture: 7, isProp: 0 },
    //                 },
    //                 items: [ // Требуемые предметы
    //                     { itemId: 830, amount: 250 }
    //                 ]
    //             },
    //             {
    //                 itemId: 'clothes',
    //                 amount: 50,
    //                 giveAmount: 0,

    //                 clothesData: {
    //                     0: { component: 6, drawable: 2093, texture: 7, isProp: 0 },
    //                 },
    //                 items: [ // Требуемые предметы
    //                     { itemId: 830, amount: 175 }
    //                 ]
    //             },
    //             {
    //                 itemId: 'clothes',
    //                 amount: 50,
    //                 giveAmount: 0,

    //                 clothesData: {
    //                     0: { component: 4, drawable: 2203, texture: 0, isProp: 0 },
    //                 },
    //                 items: [ // Требуемые предметы
    //                     { itemId: 830, amount: 125 }
    //                 ]
    //             },
    //             {
    //                 itemId: 'clothes',
    //                 amount: 50,
    //                 giveAmount: 0,

    //                 clothesData: {
    //                     1: { component: 11, drawable: 2406, texture: 0, isProp: 0 },
    //                 },
    //                 items: [ // Требуемые предметы
    //                     { itemId: 830, amount: 150 }
    //                 ]
    //             },
    //             {
    //                 itemId: 'clothes',
    //                 amount: 50,
    //                 giveAmount: 0,

    //                 clothesData: {
    //                     1: { component: 6, drawable: 2112, texture: 0, isProp: 0 },
    //                 },
    //                 items: [ // Требуемые предметы
    //                     { itemId: 830, amount: 75 }
    //                 ]
    //             },
    //             {
    //                 itemId: 'clothes',
    //                 amount: 50,
    //                 giveAmount: 0,

    //                 clothesData: {
    //                     1: { component: 4, drawable: 2219, texture: 0, isProp: 0 },
    //                 },
    //                 items: [ // Требуемые предметы
    //                     { itemId: 830, amount: 185 }
    //                 ]
    //             },
    //             {
    //                 itemId: 'clothes',
    //                 amount: 50,
    //                 giveAmount: 0,

    //                 clothesData: {
    //                     1: { component: 1, drawable: 2132, texture: 0, isProp: 0 },
    //                 },
    //                 items: [ // Требуемые предметы
    //                     { itemId: 830, amount: 165 }
    //                 ]
    //             },
    //             {
    //                 itemId: 'clothes',
    //                 amount: 50,
    //                 giveAmount: 0,

    //                 clothesData: {
    //                     1: { component: 0, drawable: 2089, texture: 0, isProp: 1 },
    //                 },
    //                 items: [ // Требуемые предметы
    //                     { itemId: 830, amount: 85 }
    //                 ]
    //             },
    //         ],
    //     ]
    // },
    23: {
        npcIds: [404],
        resetDuration: 3600000 * 2, // 2h
        selling: [
            [
                { itemId: 993, amount: 500, giveAmount: 0, price: { min: 29_999, max: 29_999 } },
                { itemId: 994, amount: 400, giveAmount: 0, price: { min: 59_999, max: 59_999 } },
                { itemId: 995, amount: 300, giveAmount: 0, price: { min: 119_999, max: 119_999 } },
                { itemId: 996, amount: 200, giveAmount: 0, price: { min: 239_999, max: 239_999 } },
            ],
        ]
    },
}
