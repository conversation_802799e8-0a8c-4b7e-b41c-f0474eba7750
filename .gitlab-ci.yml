image: node:20-alpine3.20

variables:
  GIT_SUBMODULE_STRATEGY: recursive
  GIT_SUBMODULE_DEPTH: 1
  BUILD_IMAGE_TAG: ${CI_COMMIT_REF_SLUG}
  FALLBACK_IMAGE_TAG: ${CI_MERGE_REQUEST_TARGET_BRANCH_NAME}
  NX_HEAD: ${CI_COMMIT_SHA}

stages:
  - setup
  - ci
  - triggers

.build-ci-job:
  stage: ci
  when: always
  only: [merge_requests, tags, schedules]
  needs: [setup]
  before_script:
    - apk add git
  artifacts:
    expire_in: 1 day
    paths:
      - dynamic-gitlab-ci.yml
  cache:
    - key:
        files: [yarn.lock]
      paths: [node_modules]
      policy: pull
    - key:
        files:
          [
            libs/database/src/prisma/schemas/game.prisma,
            libs/database/src/prisma/schemas/backend.prisma,
          ]
      paths: [libs/prisma-client-backend/src/generated, libs/prisma-client-game/src/generated]
      policy: pull

setup:
  stage: setup
  only: [merge_requests, tags, schedules]
  when: always
  cache:
    - key:
        files: [yarn.lock]
      paths: [node_modules]
      policy: pull-push
    - key:
        files:
          [
            libs/database/src/prisma/schemas/game.prisma,
            libs/database/src/prisma/schemas/backend.prisma,
          ]
      paths: [libs/prisma-client-backend/src/generated, libs/prisma-client-game/src/generated]
      policy: push
  before_script:
    - apk add --no-cache build-base cairo-dev pango-dev jpeg-dev giflib-dev librsvg-dev
  script:
    - yarn --frozen-lockfile
    - yarn prisma:generate

build:ci:affected:
  extends: .build-ci-job
  script:
    - |
      if [ -z "$CI_COMMIT_TAG" ]; then
        export CI_BASE=$CI_MERGE_REQUEST_DIFF_BASE_SHA
      else
        git fetch
        baseTag=$( git tag --sort=-creatordate | sed -n 2p )
        export CI_BASE=$baseTag
      fi
    - node ./deploy/generateDynamicCI.js "$(yarn --silent nx show projects --type app --affected --base=${CI_BASE} --head=${NX_HEAD})"

build:ci:all:
  extends: .build-ci-job
  when: manual
  script:
    - node ./deploy/generateDynamicCI.js "$(yarn --silent nx show projects --type app)"

trigger:services:all:
  only: [merge_requests, tags, schedules]
  needs: [build:ci:all]
  stage: triggers
  trigger:
    include:
      - artifact: dynamic-gitlab-ci.yml
        job: build:ci:all
    strategy: depend

trigger:services:affected:
  only: [merge_requests, tags, schedules]
  needs: [build:ci:affected]
  stage: triggers
  trigger:
    include:
      - artifact: dynamic-gitlab-ci.yml
        job: build:ci:affected
    strategy: depend
