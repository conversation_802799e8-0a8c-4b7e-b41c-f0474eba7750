/**
 * Доступные параметры на награду.
 * @param  {String}     rewardType                      Тип награды. ('item', 'vehicle', 'vehicleSet', 'vehicleMod', 'vehicleDiscount', 'clothesDiscount', 'money', 'clothes', 'coins', 'vip', 'hidden', 'animation')
 * @param  {Object}     rewardData                      Данные награды. Они могут быть разными в зависимости от типа награды.
 * @param  {Number}     destroyXP                       Кол-во XP получаемого при распылении награды.
 * @param  {String}     color                           Цвет награды. ('gray', 'blue', 'purple', 'red', 'gold')
 */

export const seasonPassLevels = [{
    id: 1,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'subscription', rewardData: { days: 3 }, destroyXP: 200, color: 'gray' },
    ]
},
{
    id: 2,
    free: [
        { rewardType: 'item', rewardData: { itemId: 965, count: 0 }, destroyXP: 200, color: 'blue' }, // Указ о налоговых льготах
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 100, color: 'gray' }, // ИРП армии США
        { rewardType: 'vehicleDiscount', rewardData: { amount: 5 }, destroyXP: 200, color: 'gray' }, // Скидка 5% на покупку транспорта
        { rewardType: 'clothesDiscount', rewardData: { amount: 5 }, destroyXP: 200, color: 'gray' }, // Скидка 5% на покупку одежды
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 960, count: 0 }, destroyXP: 200, color: 'blue' }, // Энергетик
    ]
},
{
    id: 3,
    free: [
        { rewardType: 'item', rewardData: { itemId: 816, count: 0 }, destroyXP: 400, color: 'purple' }, // Волшебная белая конфета
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 815, count: 0 }, destroyXP: 400, color: 'purple' }, // Волшебная черная конфета
    ]
},
{
    id: 4,
    free: [
        { rewardType: 'item', rewardData: { itemId: 735, count: 1 }, destroyXP: 200, color: 'blue' }, // Испорченный бургер
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 100, color: 'gray' }, // ИРП армии США
        { rewardType: 'item', rewardData: { itemId: 726, count: 1 }, destroyXP: 400, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс"
        { rewardType: 'item', rewardData: { itemId: 736, count: 8 }, destroyXP: 100, color: 'gray' }, // Пицца
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 732, count: 1 }, destroyXP: 200, color: 'blue' }, // Протеиновый батончик
    ]
},
{
    id: 5,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 10 }, destroyXP: 100, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 200, color: 'blue' }, // Адреналин (= эпинефрин)
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 400, color: 'blue' },
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 100, color: 'gray' }, // ИРП армии США
    ]
},
{
    id: 6,
    free: [
        { rewardType: 'item', rewardData: { itemId: 733, count: 10 }, destroyXP: 1000, color: 'red' }, // Стайлинг ManeMaster

    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 728, count: 3 }, destroyXP: 200, color: 'blue' }, // Биодобавка 1 уровня
    ]
},
{
    id: 7,
    free: [
        { rewardType: 'subscription', rewardData: { days: 3 }, destroyXP: 200, color: 'gray' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 10 }, destroyXP: 100, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 200, color: 'blue' }, // Адреналин (= эпинефрин)
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 400, color: 'blue' },
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 100, color: 'gray' }, // ИРП армии США
    ]
},
{
    id: 8,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
    ]
},
{
    id: 9,
    free: [
        { rewardType: 'item', rewardData: { itemId: 727, count: 25 }, destroyXP: 400, color: 'purple' }, // Большой ремонтный набор для транспорта
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
    ]
},
{
    id: 10,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'vehicle',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'astron' }, destroyXP: 350, color: 'purple' },
                    { rewardType: 'vehicle', rewardData: { model: 'chino' }, destroyXP: 100, color: 'gray' },
                    { rewardType: 'vehicle', rewardData: { model: 'club' }, destroyXP: 100, color: 'gray' },
                    { rewardType: 'vehicle', rewardData: { model: 'rebel' }, destroyXP: 100, color: 'gray' },
                    { rewardType: 'vehicle', rewardData: { model: 'ingot' }, destroyXP: 100, color: 'gray' },
                    { rewardType: 'vehicle', rewardData: { model: 'peyote' }, destroyXP: 100, color: 'gray' },
                ]
            },
            destroyXP: 250,
            color: 'purple'
        }
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 11,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
    ]
},
{
    id: 12,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tecpistol', skinId: 9 }, destroyXP: 600, color: 'purple' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 734, count: 0 }, destroyXP: 200, color: 'blue' }, // Благодарственное письмо губернатора
    ]
},
{
    id: 13,
    free: [
        { rewardType: 'item', rewardData: { itemId: 337, count: 0 }, destroyXP: 400, color: 'purple' }, // Винтовка Marksman Mk2
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 13004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13001, amount: 1 }, },
                ]
            },
            destroyXP: 250,
            color: 'purple'
        }
    ]
},
{
    id: 14,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3389, [1]: 6456 } }, destroyXP: 150, color: 'gray' },
    ]
},
{
    id: 15,
    free: [
        {
            rewardType: 'clothes', destroyXP: 250, color: 'gray',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2484, textures: 6, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2478, textures: 7, isProp: 0 }
                },
            },
        },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 735, count: 1 }, destroyXP: 200, color: 'blue' }, // Испорченный бургер
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 100, color: 'gray' }, // ИРП армии США
        { rewardType: 'item', rewardData: { itemId: 726, count: 1 }, destroyXP: 400, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс"
        { rewardType: 'item', rewardData: { itemId: 736, count: 8 }, destroyXP: 100, color: 'gray' }, // Пицца
    ]
},
{
    id: 16,
    free: [
        { rewardType: 'item', rewardData: { itemId: 429, count: 3 }, destroyXP: 400, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 733, count: 10 }, destroyXP: 200, color: 'blue' }, // Стайлинг для волос
    ],
},
{
    id: 17,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 13001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12004, amount: 1 }, },
                ]
            },
            destroyXP: 250,
            color: 'purple'
        }
    ],
    premium: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
},
{
    id: 18,
    free: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 1 }, destroyXP: 100, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 1 }, destroyXP: 200, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 400, color: 'purple' }, // Большой новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 200, color: 'blue' }, // Адреналин (= эпинефрин)
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
    ],
},
{
    id: 19,
    free: [
        { color: 'gray', destroyXP: 200, rewardData: { id: 369 }, rewardType: 'animation' }, // Snow Flakey
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 1 }, destroyXP: 100, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 100, color: 'gray' }, // ИРП армии США
        { rewardType: 'item', rewardData: { itemId: 89, count: 30 }, destroyXP: 400, color: 'purple' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 956, count: 3 }, destroyXP: 400, color: 'purple' }, // Набор самореанимации
    ],
},
{
    id: 20,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 250, color: 'gray',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 6, drawable: 2130, textures: 7, isProp: 0 },
                    1: { gender: 1, component: 6, drawable: 2153, textures: 7, isProp: 0 }
                },
            },
        },
    ]
},
{
    id: 21,
    free: [
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
    ],
    premium: [
        { euroCoins: 250, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
    ]
},
{
    id: 22,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_gusenberg', skinId: 14 }, destroyXP: 600, color: 'purple' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 2 }, destroyXP: 100, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 2 }, destroyXP: 200, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 2 }, destroyXP: 400, color: 'purple' }, // Большой новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 89, count: 30 }, destroyXP: 400, color: 'purple' }, // Желтая аптечка (хил 75хп)
    ]
},
{
    id: 23,
    free: [
        { rewardType: 'clothesDiscount', rewardData: { amount: 10 }, destroyXP: 400, color: 'blue' }, // Скидка 10% на покупку транспорта
        { rewardType: 'vehicleDiscount', rewardData: { amount: 10 }, destroyXP: 400, color: 'blue' }, // Скидка 10% на покупку одежды
        { rewardType: 'item', rewardData: { itemId: 427, count: 1 }, destroyXP: 100, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 2 }, destroyXP: 200, color: 'blue' }, // Средний новогодний подарок
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 12004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12001, amount: 1 }, },
                ]
            },
            destroyXP: 250,
            color: 'purple'
        }
    ]
},
{
    id: 24,
    free: [
        { rewardType: 'item', rewardData: { itemId: 963, count: 0 }, destroyXP: 200, color: 'blue' }, // Сверхтяжелый бронежилет
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 100, color: 'gray' }, // ИРП армии США
    ]
},
{
    id: 25,
    free: [
        { rewardType: 'item', rewardData: { itemId: 735, count: 1 }, destroyXP: 200, color: 'blue' }, // Испорченный бургер
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 100, color: 'gray' }, // ИРП армии США
        { rewardType: 'item', rewardData: { itemId: 726, count: 1 }, destroyXP: 400, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс"
        { rewardType: 'item', rewardData: { itemId: 736, count: 8 }, destroyXP: 100, color: 'gray' }, // Пицца
    ],
    premium: [
        { color: 'blue', destroyXP: 300, rewardData: { id: 325 }, rewardType: 'animation' }, // Bood Up Groove, prem 24-31 lvl
    ]
},
{
    id: 26,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3390, [1]: 6457 } }, destroyXP: 150, color: 'gray' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tecpistol', skinId: 10 }, destroyXP: 600, color: 'purple' },
    ]
},
{
    id: 27,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 12001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11004, amount: 1 }, },
                ]
            },
            destroyXP: 250,
            color: 'purple'
        }
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 50000 }, destroyXP: 600, color: 'purple' },
    ]
},
{
    id: 28,
    free: [
        { rewardType: 'item', rewardData: { itemId: 960, count: 0 }, destroyXP: 200, color: 'blue' }, // Энергетик
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'orizzsounds' }, destroyXP: 400, color: 'blue' },
    ],
},
{
    id: 29,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
},
{
    id: 30,
    free: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 600, color: 'red' }, // Зима Автомобильный 2025
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 31,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 400, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 5, drawable: 2103, textures: 8, isProp: 0 },
                    1: { gender: 1, component: 5, drawable: 2109, textures: 8, isProp: 0 }
                },
            },
        },
    ]
},
{
    id: 32,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_gusenberg', skinId: 15 }, destroyXP: 600, color: 'purple' },
    ],
    premium: [
        { rewardType: 'subscription', rewardData: { days: 3 }, destroyXP: 200, color: 'gray' },
    ],
},
{
    id: 33,
    free: [
        { rewardType: 'item', rewardData: { itemId: 729, count: 2 }, destroyXP: 400, color: 'purple' }, // Биодобавка 2 уровня
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 11004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11001, amount: 1 }, },
                ]
            },
            destroyXP: 250,
            color: 'purple'
        }
    ]
},
{
    id: 34,
    free: [
        { color: 'gray', destroyXP: 200, rewardData: { id: 374 }, rewardType: 'animation' }, // Toys Flip
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 1 }, destroyXP: 100, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 1 }, destroyXP: 200, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 400, color: 'purple' }, // Большой новогодний подарок
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3392, [1]: 6459 } }, destroyXP: 150, color: 'gray' },
    ]
},
{
    id: 35,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 400, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2250, textures: 6, isProp: 0 },
                    1: { gender: 1, component: 5, drawable: 2102, textures: 10, isProp: 0 }
                },
            },
        },
    ]
},
{
    id: 36,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 134 }, color: 'purple', destroyXP: 750 },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 725, count: 0 }, destroyXP: 1000, color: 'red' }, // Ремонтный комплейкт для оружия
        { rewardType: 'item', rewardData: { itemId: 427, count: 1 }, destroyXP: 100, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 2 }, destroyXP: 200, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 3 }, destroyXP: 400, color: 'purple' }, // Большой новогодний подарок
    ],
},
{
    id: 37,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3397, [1]: 6463 } }, destroyXP: 150, color: 'gray' },
        { rewardType: 'item', rewardData: { itemId: 956, count: 3 }, destroyXP: 400, color: 'purple' }, // Набор самореанимации
        { rewardType: 'item', rewardData: { itemId: 963, count: 0 }, destroyXP: 200, color: 'blue' }, // Сверхтяжелый бронежилет
        { rewardType: 'item', rewardData: { itemId: 731, count: 20 }, destroyXP: 1000, color: 'red' }, // Капсулы восстановления

    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 11001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10004, amount: 1 }, },
                ]
            },
            destroyXP: 250,
            color: 'purple'
        }
    ]
},
{
    id: 38,
    free: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'thisworldisforme' }, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 730, count: 1 }, destroyXP: 1000, color: 'red' }, // Биодобавка 3 уровня
    ]
},
{
    id: 39,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3396, [1]: 6462 } }, destroyXP: 150, color: 'gray' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tecpistol', skinId: 11 }, destroyXP: 600, color: 'purple' },
    ]
},
{
    id: 40,
    free: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 400, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2249, textures: 7, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2484, textures: 9, isProp: 0 }
                },
            },
        },
    ]
},
{
    id: 41,
    free: [
        { color: 'blue', destroyXP: 300, rewardData: { id: 179 }, rewardType: 'animation' }, // Flamenco, free 22-29 lvl
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
    ]
},
{
    id: 42,
    free: [
        { rewardType: 'item', rewardData: { itemId: 956, count: 3 }, destroyXP: 400, color: 'purple' }, // Набор самореанимации
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_gusenberg', skinId: 16 }, destroyXP: 600, color: 'purple' },
    ]
},
{
    id: 43,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 10004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10001, amount: 1 }, },
                ]
            },
            destroyXP: 250,
            color: 'purple'
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 100, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 100, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 3 }, destroyXP: 200, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 3 }, destroyXP: 400, color: 'purple' }, // Большой новогодний подарок
    ]
},
{
    id: 44,
    free: [
        { rewardType: 'item', rewardData: { itemId: 963, count: 0 }, destroyXP: 200, color: 'blue' }, // Сверхтяжелый бронежилет
        { rewardType: 'item', rewardData: { itemId: 963, count: 0 }, destroyXP: 200, color: 'blue' }, // Сверхтяжелый бронежилет
        { rewardType: 'item', rewardData: { itemId: 89, count: 30 }, destroyXP: 400, color: 'purple' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 956, count: 3 }, destroyXP: 400, color: 'purple' }, // Набор самореанимации
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'situation' }, destroyXP: 400, color: 'blue' },
    ]
},
{
    id: 45,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 141 }, color: 'purple', destroyXP: 750 },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 46,
    free: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
    premium: [
        { color: 'blue', destroyXP: 300, rewardData: { id: 368 }, rewardType: 'animation' }, // Ice Moves
    ]
},
{
    id: 47,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 10001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12004, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13001, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13004, amount: 1 }, },
                ]
            },
            destroyXP: 250,
            color: 'purple'
        }
    ],
    premium: [
        { rewardType: 'subscription', rewardData: { days: 7 }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 48,
    free: [
        { rewardType: 'item', rewardData: { itemId: 731, count: 20 }, destroyXP: 1000, color: 'red' }, // Капсулы восстановления
        { rewardType: 'item', rewardData: { itemId: 987, count: 10 }, destroyXP: 400, color: 'purple' }, // Крем-маска
        { rewardType: 'item', rewardData: { itemId: 429, count: 3 }, destroyXP: 400, color: 'purple' }, // Большой новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 3 }, destroyXP: 400, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3395, [1]: 6461 } }, destroyXP: 200, color: 'blue' },
    ]
},
{
    id: 49,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3400, [1]: 6467 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
    ]
},
{
    id: 50,
    free: [
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 400, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 5, drawable: 2095, textures: 7, isProp: 0 },
                    1: { gender: 1, component: 5, drawable: 2099, textures: 7, isProp: 0 }
                },
            },
        },
    ]
},
{
    id: 51,
    free: [
        {
            rewardType: 'clothes', destroyXP: 700, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 1, drawable: 2164, textures: 13, isProp: 0 },
                    1: { gender: 1, component: 1, drawable: 2151, textures: 13, isProp: 0 },
                },
            },
        },
    ],
    premium: [
        { color: 'blue', destroyXP: 300, rewardData: { id: 370 }, rewardType: 'animation' }, // Tea Time
    ],
},
{
    id: 52,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3399, [1]: 6466 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_assaultrifle_mk2', skinId: 21 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 53,
    free: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'getup' }, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 333, count: 0 }, destroyXP: 400, color: 'purple' }, // Ручной пулемет Мk2
    ]
},
{
    id: 54,
    free: [
        { rewardType: 'item', rewardData: { itemId: 992, count: 0 }, destroyXP: 1000, color: 'red' }, // Автосигнализация
    ],
    premium: [
        { color: 'blue', destroyXP: 300, rewardData: { id: 383 }, rewardType: 'animation' }, // Company Jig
    ]
},
{
    id: 55,
    free: [
        { rewardType: 'vehicleSet', rewardData: { model: 'f150', setId: 9 }, destroyXP: 5000, color: 'gold' }, // Обвес Ford F-150 Raptor (Кастомный)
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 56,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Стандартный кейс рулетки
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle_mk2', skinId: 22 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 57,
    free: [
        {
            rewardType: 'clothes', destroyXP: 700, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2252, textures: 4, isProp: 0 },
                    1: { gender: 1, component: 4, drawable: 2272, textures: 7, isProp: 0 },
                },
            },
        },
    ],
    premium: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 15 }, destroyXP: 600, color: 'purple' }, // Скидка 15% на покупку транспорта
        { rewardType: 'clothesDiscount', rewardData: { amount: 15 }, destroyXP: 600, color: 'purple' }, // Скидка 15% на покупку одежды
        { rewardType: 'item', rewardData: { itemId: 428, count: 5 }, destroyXP: 200, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 5 }, destroyXP: 400, color: 'purple' }, // Большой новогодний подарок
    ]
},
{
    id: 58,
    free: [
        { color: 'blue', destroyXP: 300, rewardData: { id: 288 }, rewardType: 'animation' }, // Lit Dance
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 137 }, color: 'purple', destroyXP: 750 },
    ],
},
{
    id: 59,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'takingaway' }, destroyXP: 750, color: 'purple' },
    ],
},
{
    id: 60,
    free: [
        { rewardType: 'vehicle', rewardData: { model: '350z' }, destroyXP: 8000, color: 'gold', customPrice: 500000 }, // Машина Nissan 350Z
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: '350z', setId: 1 }, destroyXP: 1000, color: 'gold' }, // Обвес Nissan 350Z (Кастомный 1)
                    { rewardType: 'vehicleSet', rewardData: { model: '350z', setId: 2 }, destroyXP: 1500, color: 'gold' }, // Обвес Nissan 350Z (Кастомный 2)
                    { rewardType: 'vehicleSet', rewardData: { model: '350z', setId: 3 }, destroyXP: 2000, color: 'gold' }, // Обвес Nissan 350Z (Кастомный 3)
                ]
            }
        }
    ]
},
{
    id: 61,
    free: [
        {
            rewardType: 'clothes', destroyXP: 700, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2251, textures: 3, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2481, textures: 8, isProp: 0 }
                },
            },
        },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 700, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2480, textures: 3, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2479, textures: 10, isProp: 0 }
                },
            },
        },
    ]
},
{
    id: 62,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle_mk2', skinId: 24 }, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3408, [1]: 6475 } }, destroyXP: 200, color: 'blue' },
    ]
},
{
    id: 63,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 600, color: 'purple' }, // Автомобильный
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 600, color: 'red' }, // Автомобильный
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 600, color: 'red' }, // Автомобильный
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 13005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13002, amount: 1 }, },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ]
},
{
    id: 64,
    free: [
        {
            rewardType: 'clothes', destroyXP: 700, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2260, textures: 6, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2487, textures: 12, isProp: 0 },
                },
            },
        },
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'onyourknees' }, destroyXP: 750, color: 'purple' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3398, [1]: 6465 } }, destroyXP: 200, color: 'blue' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3414, [1]: 6481 } }, destroyXP: 200, color: 'blue' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3401, [1]: 6468 } }, destroyXP: 300, color: 'purple' },
    ]
},
{
    id: 65,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_assaultrifle_mk2', skinId: 22 }, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'ct5', setId: 2 }, destroyXP: 5000, color: 'gold' }, // Обвес Cadillac CT-5 Black Wing
    ]
},
{
    id: 66,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        { color: 'blue', destroyXP: 300, rewardData: { id: 62 }, rewardType: 'animation' }, // Wanna See Mee
    ]
},
{
    id: 67,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 13002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12005, amount: 1 }, },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 958, count: 0 }, destroyXP: 400, color: 'purple' }, // Улучшенная рация
    ],
},
{
    id: 68,
    free: [
        { color: 'purple', destroyXP: 500, rewardData: { id: 377 }, rewardType: 'animation' }, // Boing
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 989, count: 0 }, destroyXP: 1000, color: 'red' }, // Стальной кастет
    ]
},
{
    id: 69,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 500, color: 'purple' },
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 500, color: 'purple' },
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 500, color: 'purple' },
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 139 }, color: 'red', destroyXP: 1250 },
    ]
},
{
    id: 70,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'model3' }, destroyXP: 8000, color: 'gold', customPrice: 750000 }, // Машина Tesla Model 3
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'model3', setId: 1 }, destroyXP: 2000, color: 'gold' }, // Обвес Tesla Model 3 Restyling
    ]
},
{
    id: 71,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle_mk2', skinId: 23 }, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 700, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 6, drawable: 2128, textures: 6, isProp: 0 },
                    1: { gender: 1, component: 6, drawable: 2151, textures: 6, isProp: 0 }
                },
            },
        },
    ]
},
{
    id: 72,
    free: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'goodmorning' }, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_assaultrifle_mk2', skinId: 23 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 73,
    free: [
        {
            rewardType: 'clothes', destroyXP: 700, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2152, textures: 8, isProp: 0 },
                    1: { gender: 1, component: 7, drawable: 2130, textures: 8, isProp: 0 },
                },
            },
        },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 12005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12002, amount: 1 }, },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ]
},
{
    id: 74,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3402, [1]: 6469 } }, destroyXP: 300, color: 'purple' },
        { rewardType: 'item', rewardData: { itemId: 627, count: 0 }, destroyXP: 1500, color: 'gold' }, // Дефибриллятор
        { rewardType: 'item', rewardData: { itemId: 959, count: 0 }, destroyXP: 1500, color: 'gold' }, // Точильный камень
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3391, [1]: 6458 } }, destroyXP: 300, color: 'purple' },
    ]
},
{
    id: 75,
    free: [
        { rewardType: 'vehicleSet', rewardData: { model: 's15', setId: 19 }, destroyXP: 5000, color: 'gold' }, // Обвес Nissan Silvia S15 GarageMak
    ],
    premium: [
        { rewardType: 'vehicleDetail', rewardData: { id: 1008, amount: 1 }, destroyXP: 500, color: 'purple' }, // Винил на автомобиль N8
    ]
},
{
    id: 76,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
    ],
    premium: [
        { color: 'blue', destroyXP: 300, rewardData: { id: 381 }, rewardType: 'animation' }, // What You Want
    ],
},
{
    id: 77,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 12002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11005, amount: 1 }, },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 78,
    free: [
        { color: 'purple', destroyXP: 500, rewardData: { id: 376 }, rewardType: 'animation' }, // Cloud Swing
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
    ],
},
{
    id: 79,
    free: [
        { rewardType: 'subscription', rewardData: { days: 14 }, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'sadmeowmeow' }, destroyXP: 750, color: 'purple' },
    ]
},
{
    id: 80,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'ex90' }, destroyXP: 10000, color: 'gold', customPrice: 1500000 }, // Машина Volvo EX90
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_combatmg_mk2', skinId: 9 }, destroyXP: 1500, color: 'gold' },
    ]
},
{
    id: 81,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyshotgun', skinId: 25 }, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 700, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 5, drawable: 2096, textures: 4, isProp: 0 },
                    1: { gender: 1, component: 5, drawable: 2100, textures: 4, isProp: 0 }
                },
            },
        },
    ],
},
{
    id: 82,
    free: [
        {
            rewardType: 'clothes', destroyXP: 700, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2489, textures: 7, isProp: 0 },
                    1: { gender: 1, component: 4, drawable: 2271, textures: 8, isProp: 0 },
                },
            },
        },
    ],
    premium: [
        { color: 'blue', destroyXP: 300, rewardData: { id: 385 }, rewardType: 'animation' }, // Point And Strut
    ],
},
{
    id: 83,
    free: [
        { rewardType: 'item', rewardData: { itemId: 812, count: 0 }, destroyXP: 2500, color: 'red' }, // Лыжи
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 813, count: 0 }, destroyXP: 2500, color: 'red' }, // Коньки
    ]
},
{
    id: 84,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 11005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11002, amount: 1 }, },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3412, [1]: 6479 } }, destroyXP: 300, color: 'purple' },
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'ohIshouldnthave' }, destroyXP: 750, color: 'purple' },
        { rewardType: 'item', rewardData: { itemId: 960, count: 5 }, destroyXP: 400, color: 'purple' }, // Энергетик
        { rewardType: 'item', rewardData: { itemId: 963, count: 0 }, destroyXP: 200, color: 'blue' }, // Сверхтяжелый бронежилет
    ]
},
{
    id: 85,
    free: [
        { rewardType: 'vehicleDetail', rewardData: { id: 1011, amount: 1 }, destroyXP: 1000, color: 'purple' }, // Винил на автомобиль N11
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'sls', setId: 1 }, destroyXP: 5000, color: 'gold' }, // Обвес Mercedes-AMG SLS Black Series
    ]
},
{
    id: 86,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3406, [1]: 6473 } }, destroyXP: 300, color: 'purple' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyshotgun', skinId: 26 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 87,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 11002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10005, amount: 1 }, },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
},
{
    id: 88,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 133 }, color: 'red', destroyXP: 1250 },
    ],
    premium: [
        { color: 'purple', destroyXP: 500, rewardData: { id: 386 }, rewardType: 'animation' }, // Breakneck
    ]
},
{
    id: 89,
    free: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'genius' }, destroyXP: 750, color: 'purple' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3403, [1]: 6470 } }, destroyXP: 300, color: 'purple' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3413, [1]: 6480 } }, destroyXP: 300, color: 'purple' },
        { rewardType: 'item', rewardData: { itemId: 992, count: 0 }, destroyXP: 1000, color: 'red' }, // Автосигнализация
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
    ]
},
{
    id: 90,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'glc' }, destroyXP: 9000, color: 'gold', customPrice: 1250000 }, // Машина Mercedes-Benz GLC C254
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'glc', setId: 2 }, destroyXP: 2000, color: 'gold' }, // Обвес Mercedes-Benz GLC C254 AMG
    ]
},
{
    id: 91,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2253, textures: 5, isProp: 0 },
                    1: { gender: 1, component: 4, drawable: 2270, textures: 8, isProp: 0 }
                },
            },
        },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2486, textures: 5, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2485, textures: 8, isProp: 0 }
                },
            },
        },
    ]
},
{
    id: 92,
    free: [
        { color: 'red', destroyXP: 800, rewardData: { id: 375 }, rewardType: 'animation' }, // Deer Cadabra
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tacticalrifle', skinId: 9 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 93,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2263, textures: 3, isProp: 0 },
                    1: { gender: 1, component: 6, drawable: 2154, textures: 4, isProp: 0 },
                },
            },
        },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 10005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10002, amount: 1 }, },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ],
},
{
    id: 94,
    free: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3394, [1]: 6464 } }, destroyXP: 500, color: 'red' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3411, [1]: 6478 } }, destroyXP: 500, color: 'red' },
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 135 }, color: 'gold', destroyXP: 1500 },
    ]
},
{
    id: 95,
    free: [
        { rewardType: 'vehicleSet', rewardData: { model: 'i8', setId: 12 }, destroyXP: 5000, color: 'gold' }, // Обвес BMW i8 Energy Motorsport
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 452, count: 0 }, destroyXP: 1500, color: 'gold' }, // Гражданский дрон
    ]
},
{
    id: 96,
    free: [
        { color: 'purple', destroyXP: 500, rewardData: { id: 387 }, rewardType: 'animation' }, // No Tears
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 5, drawable: 2097, textures: 10, isProp: 0 },
                    1: { gender: 1, component: 5, drawable: 2101, textures: 10, isProp: 0 }
                },
            },
        },
    ]
},
{
    id: 97,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 10002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12005, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13002, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13005, amount: 1 }, },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'old' }, destroyXP: 1500, color: 'red' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3393, [1]: 6460 } }, destroyXP: 500, color: 'red' },
        { rewardType: 'item', rewardData: { itemId: 961, count: 0 }, destroyXP: 1500, color: 'gold' }, // GPS трекер
    ],
},
{
    id: 98,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 136 }, color: 'gold', destroyXP: 1500 },
    ]
},
{
    id: 99,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyshotgun', skinId: 27 }, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'concertkishlakhere' }, destroyXP: 1500, color: 'red' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3410, [1]: 6477 } }, destroyXP: 500, color: 'red' },
        { rewardType: 'item', rewardData: { itemId: 988, count: 0 }, destroyXP: 1000, color: 'red' }, // Карманный ингалятор
        { rewardType: 'item', rewardData: { itemId: 627, count: 0 }, destroyXP: 1500, color: 'gold' }, // Дефибриллятор Mk2
    ]
},
{
    id: 100,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'cle' }, destroyXP: 11000, color: 'gold', customPrice: 1750000 }, // Машина Mercedes-Benz CLE C236
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'snowmobile' }, destroyXP: 4000, color: 'gold', customPrice: 500000 }, // Снегоход BRP Ski-Doo Sammit Rev-XM
    ]
},
{
    id: 101,
    free: [
        { rewardType: 'subscription', rewardData: { days: 30 }, destroyXP: 1200, color: 'red' },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 102,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2255, textures: 7, isProp: 0 },
                    1: { gender: 1, component: 6, drawable: 2157, textures: 11, isProp: 0 }
                },
            },
        },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2487, textures: 8, isProp: 0 },
                    1: { gender: 1, component: 5, drawable: 2104, textures: 8, isProp: 0 }
                },
            },
        },
    ]
},
{
    id: 103,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 132 }, color: 'red', destroyXP: 1250 },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tacticalrifle', skinId: 10 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 104,
    free: [
        { rewardType: 'vehicleDetail', rewardData: { id: 5003, amount: 1 }, destroyXP: 1500, color: 'red' }, // Номерной знак N3
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'boogotscared' }, destroyXP: 1500, color: 'red' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3405, [1]: 6472 } }, destroyXP: 500, color: 'red' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3416, [1]: 6483 } }, destroyXP: 500, color: 'red' },
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
    ]
},
{
    id: 105,
    free: [
        { rewardType: 'vehicleDetail', rewardData: { id: 1012, amount: 1 }, destroyXP: 1500, color: 'red' }, // Винил на автомобиль N12
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'e63s', setId: 5 }, destroyXP: 5000, color: 'gold' }, // Обвес Mercedes-Benz E-Class W212 Restyling
    ]
},
{
    id: 106,
    free: [
        { color: 'purple', destroyXP: 500, rewardData: { id: 384 }, rewardType: 'animation' }, // Heartbreak Shuffle
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 2000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2029, texture: 2, isProp: 0 }, // Кобура на 2 ноги, белая
                    1: { gender: 1, component: 7, drawable: 2012, texture: 2, isProp: 0 }, // Кобура на 2 ноги, белая
                }
            }
        }
    ]
},
{
    id: 107,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 5, drawable: 2100, textures: 7, isProp: 0 },
                    1: { gender: 1, component: 5, drawable: 2106, textures: 7, isProp: 0 },
                },
            },
        },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2481, textures: 9, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2489, textures: 9, isProp: 0 },
                },
            },
        },
    ]
},
{
    id: 108,
    free: [
        { color: 'red', destroyXP: 800, rewardData: { id: 382 }, rewardType: 'animation' }, // Entranced
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 957, count: 0 }, destroyXP: 1000, color: 'red' }, // Глушилка связи
    ]
},
{
    id: 109,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_combatmg_mk2', skinId: 10 }, destroyXP: 1500, color: 'gold' },
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'teawithsugar' }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 110,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'etron2' }, destroyXP: 12000, color: 'gold', customPrice: 2000000 }, // Машина Audi Q8 E-tron
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'etron2', setId: 1 }, destroyXP: 2000, color: 'gold' }, // обвес Audi Q8 E-tron S
    ]
},
{
    id: 111,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 13006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13003, amount: 1 }, },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyrifle', skinId: 9 }, destroyXP: 1500, color: 'gold' },
    ]
},
{
    id: 112,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2259, textures: 8, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2480, textures: 8, isProp: 0 }
                },
            },
        },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2492, textures: 8, isProp: 0 },
                    1: { gender: 1, component: 6, drawable: 2152, textures: 4, isProp: 0 }
                },
            },
        },
    ]
},
{
    id: 113,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tacticalrifle', skinId: 11 }, destroyXP: 1000, color: 'red' },
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'mutually' }, destroyXP: 1500, color: 'red' },
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'troubletrouble' }, destroyXP: 1500, color: 'red' },
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
    ],
    premium: [
        { rewardType: 'vehicleDetail', rewardData: { id: 1013, amount: 1 }, destroyXP: 2000, color: 'red' }, // Винил на автомобиль N13
    ]
},
{
    id: 114,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2258, textures: 8, isProp: 0 },
                    1: { gender: 1, component: 4, drawable: 2273, textures: 11, isProp: 0 },
                },
            },
        },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2495, textures: 7, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2488, textures: 11, isProp: 0 },
                },
            },
        },
    ]
},
{
    id: 115,
    free: [
        { rewardType: 'vehicleSet', rewardData: { model: 'nsx', setId: 3 }, destroyXP: 5000, color: 'gold' }, // Обвес Honda NSX (Кастомный)
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'cle', setId: 1 }, destroyXP: 5000, color: 'gold' }, // Обвес Mercedes-Benz CLE C253 AMG
    ]
},
{
    id: 116,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        { color: 'red', destroyXP: 800, rewardData: { id: 379 }, rewardType: 'animation' }, // Caffeinated
    ]
},
{
    id: 117,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 13003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12006, amount: 1 }, },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 990, count: 0 }, destroyXP: 1000, color: 'red' }, // Воздушный горн
    ]
},
{
    id: 118,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper_mk2', skinId: 20 }, destroyXP: 1500, color: 'gold' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1400, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 6, drawable: 2129, textures: 4, isProp: 0 },
                    1: { gender: 1, component: 6, drawable: 2155, textures: 4, isProp: 0 },
                },
            },
        },
    ]
},
{
    id: 119,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3415, [1]: 6482 } }, destroyXP: 800, color: 'gold' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3409, [1]: 6476 } }, destroyXP: 800, color: 'gold' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3417, [1]: 6484 } }, destroyXP: 800, color: 'gold' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3418, [1]: 6485 } }, destroyXP: 800, color: 'gold' },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 120,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'roma' }, destroyXP: 13000, color: 'gold', customPrice: 2875000 }, // Машина Ferrari Roma
    ]
},
{
    id: 121,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_combatmg_mk2', skinId: 11 }, destroyXP: 1500, color: 'gold' },
    ],
    premium: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ]
},
{
    id: 122,
    free: [
        { color: 'red', destroyXP: 800, rewardData: { id: 378 }, rewardType: 'animation' }, // Bye Bye Bye
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 123,
    free: [
        { rewardType: 'item', rewardData: { itemId: 961, count: 0 }, destroyXP: 1500, color: 'gold' }, // GPS трекер
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 12006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12003, amount: 1 }, },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ]
},
{
    id: 124,
    free: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'catlaugh' }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3407, [1]: 6474 } }, destroyXP: 800, color: 'gold' },
    ]
},
{
    id: 125,
    free: [
        { rewardType: 'vehicleDetail', rewardData: { id: 1014, amount: 1 }, destroyXP: 2500, color: 'gold' }, // Винил на автомобиль N14
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'chiron19', setId: 12 }, destroyXP: 5000, color: 'gold' }, // Обвес Bugatti Chiron (Кастомный)
    ]
},
{
    id: 126,
    free: [
        {
            rewardType: 'clothes', destroyXP: 2000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2483, textures: 5, isProp: 0 },
                    1: { gender: 1, component: 4, drawable: 2274, textures: 6, isProp: 0 },
                },
            },
        },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyrifle', skinId: 10 }, destroyXP: 1500, color: 'gold' },
    ]
},
{
    id: 127,
    free: [
        { rewardType: 'item', rewardData: { itemId: 966, count: 0 }, destroyXP: 1500, color: 'gold' }, // Пустой номер
    ],
    premium: [
        { color: 'red', destroyXP: 800, rewardData: { id: 367 }, rewardType: 'animation' }, // Pick Me Up
    ]
},
{
    id: 128,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 12006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12003, amount: 1 }, },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 1500, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ]
},
{
    id: 129,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 138 }, color: 'gold', destroyXP: 1500 },
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'legend' }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 130,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'spectre' }, destroyXP: 13000, color: 'gold', customPrice: 3250000 }, // Машина Rolls-Royce Spectre
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 2000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 5, drawable: 2105, textures: 8, isProp: 0 },
                    1: { gender: 1, component: 5, drawable: 2111, textures: 8, isProp: 0 },
                },
            },
        },
    ]
},
{
    id: 131,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper_mk2', skinId: 21 }, destroyXP: 1500, color: 'gold' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 2000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2485, textures: 9, isProp: 0 },
                    1: { gender: 1, component: 4, drawable: 2267, textures: 7, isProp: 0 },
                },
            },
        },
    ]
},
{
    id: 132,
    free: [
        { color: 'red', destroyXP: 800, rewardData: { id: 373 }, rewardType: 'animation' }, // AirBoard
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 133,
    free: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 960, color: 'red' }, // Скидка 20% на покупку транспорта
        { rewardType: 'clothesDiscount', rewardData: { amount: 20 }, destroyXP: 960, color: 'red' }, // Скидка 20% на покупку одежды
        { rewardType: 'item', rewardData: { itemId: 957, count: 0 }, destroyXP: 1000, color: 'red' }, // Глушилка связи
        { rewardType: 'item', rewardData: { itemId: 992, count: 0 }, destroyXP: 1000, color: 'red' }, // Автосигнализация
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 12003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11006, amount: 1 }, },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ]
},
{
    id: 134,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyrifle', skinId: 11 }, destroyXP: 1500, color: 'gold' },
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
    ]
},
{
    id: 135,
    free: [
        { rewardType: 'item', rewardData: { itemId: 991, count: 0 }, destroyXP: 1500, color: 'gold' }, // Набор дверных щокеров
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'veyron', setId: 11 }, destroyXP: 5000, color: 'gold' }, // обвес Bugatti Veyron Hyper Sport
    ]
},
{
    id: 136,
    free: [
        { rewardType: 'vehicleDetail', rewardData: { id: 1016, amount: 1 }, destroyXP: 2400, color: 'gold' }, // Винил на автомобиль N16
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 2000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 3, drawable: 2067, textures: 8, isProp: 0 },
                    1: { gender: 1, component: 3, drawable: 2400, textures: 8, isProp: 0 },
                },
            },
        },
    ]
},
{
    id: 137,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        { color: 'gold', destroyXP: 1200, rewardData: { id: 380 }, rewardType: 'animation' }, // Desirable
    ]
},
{
    id: 138,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 11006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11003, amount: 1 }, },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 724, count: 0 }, destroyXP: 1500, color: 'gold' }, // Улучшенный металоискатель
    ]
},
{
    id: 139,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'vovchik' }, destroyXP: 2400, color: 'gold' },
    ]
},
{
    id: 140,
    free: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 1500, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'temerario' }, destroyXP: 14000, color: 'gold', customPrice: 4250000 }, // Машина Lamborghini Temerario
    ]
},
{
    id: 141,
    free: [
        { color: 'gold', destroyXP: 1200, rewardData: { id: 338 }, rewardType: 'animation' }, // Rebellious
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 986, count: 0 }, destroyXP: 1500, color: 'gold' }, // Дрон-сканер
    ]
},
{
    id: 142,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 140 }, color: 'gold', destroyXP: 1500 },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 962, count: 0 }, destroyXP: 1500, color: 'gold' }, // Система крепления
    ]
},
{
    id: 143,
    free: [
        { rewardType: 'subscription', rewardData: { days: 50 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 10006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10003, amount: 1 }, },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ]
},
{
    id: 144,
    free: [
        { color: 'gold', destroyXP: 1200, rewardData: { id: 371 }, rewardType: 'animation' }, // Chugga Chugga
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3404, [1]: 6471 } }, destroyXP: 800, color: 'gold' },
    ]
},
{
    id: 145,
    free: [
        { rewardType: 'vehicleDetail', rewardData: { id: 5004, amount: 1 }, destroyXP: 1500, color: 'red' }, // Номерной знак N4 Cyber
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'huracan', setId: 25 }, destroyXP: 5000, color: 'gold' }, // Обвес Lamborghini Huracan GT3
    ]
},
{
    id: 146,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper_mk2', skinId: 22 }, destroyXP: 1500, color: 'gold' },
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
    ]
},
{
    id: 147,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 10003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 10006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 11006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 12006, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13003, amount: 1 }, },
                    { rewardType: 'wheels', rewardData: { id: 13006, amount: 1 }, },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 966, count: 0 }, destroyXP: 1500, color: 'gold' }, // Пустой номер
    ]
},
{
    id: 148,
    free: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 25 }, destroyXP: 1280, color: 'gold' }, // Скидка 25% на покупку транспорта
        { rewardType: 'clothesDiscount', rewardData: { amount: 25 }, destroyXP: 1280, color: 'gold' }, // Скидка 25% на покупку одежды
        { rewardType: 'vehicleDiscount', rewardData: { amount: 25 }, destroyXP: 1280, color: 'gold' }, // Скидка 25% на покупку транспорта
        { rewardType: 'clothesDiscount', rewardData: { amount: 25 }, destroyXP: 1280, color: 'gold' }, // Скидка 25% на покупку одежды
    ],
    premium: [
        { color: 'gold', destroyXP: 1200, rewardData: { id: 372 }, rewardType: 'animation' }, // Money Blastin
    ]
},
{
    id: 149,
    free: [
        {
            rewardType: 'clothes', destroyXP: 2000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2151, textures: 16, isProp: 0 },
                    1: { gender: 1, component: 7, drawable: 2129, textures: 16, isProp: 0 },
                },
            },
        },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 2000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 5, drawable: 2104, textures: 6, isProp: 0 },
                    1: { gender: 1, component: 5, drawable: 2110, textures: 6, isProp: 0 },
                },
            },
        },
    ]
},
{
    id: 150,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'tourbillon' }, destroyXP: 15000, color: 'gold', customPrice: 5000000 }, // Машина Bugatti Tourbillon
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'ah6' }, destroyXP: 20000, color: 'gold', customPrice: 5000000 }, // Вертолёт MD Helicopters AH-6 Little Bird
    ]
},
{
    id: 151,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 152,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 153,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 154,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 155,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 156,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 157,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 158,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 159,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 160,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 161,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 162,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 163,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 164,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 165,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 166,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 167,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 168,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 169,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 170,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 171,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 172,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 173,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 174,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 175,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 176,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 177,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 178,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 179,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 180,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 181,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 182,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 183,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 184,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 185,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 186,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 187,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 188,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 189,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 190,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 191,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 192,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 193,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 194,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 195,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 196,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 197,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 198,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 199,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ]
},
{
    id: 200,
    free: [
        {
            euroCoins: 100, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2025' }, destroyXP: 2400, color: 'gold' }, // Зима 2025
                    { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'blue' }, // Автомобильный
                    { rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600, color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'red' }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 1200, color: 'red' }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2025' }, destroyXP: 2400, color: 'gold' }, // Зима Автомобильный 2025
                ]
            }
        },
    ],
    premium: [
        { rewardType: 'subscription', rewardData: { days: 180 }, destroyXP: 2000, color: 'gold' },
    ]
}
];

export const seasonPassXP = {
    // Формула XP_START+(currentLevel*XP_STEP)
    XP_START: 300, // 1й уровень начинается с этого XP
    XP_STEP: 220, // Шаг на каждый уровень. (XP нужен для текущего уровня)+XP_STEP = следующий уровень
}

export const seasonPassgetXPForNextLevel = (currentLevel) => {
    if (currentLevel < 100) {
        return seasonPassXP.XP_START + (currentLevel * seasonPassXP.XP_STEP);
    }

    if (currentLevel >= 100 && currentLevel < 150) {
        return 15000;
    }

    return 20000;
};
