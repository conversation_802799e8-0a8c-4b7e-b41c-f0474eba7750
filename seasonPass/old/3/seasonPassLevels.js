/**
 * Доступные параметры на награду.
* @param  {String}     rewardType                      Тип награды. ('item', 'vehicle', 'vehicleSet', 'vehicleMod', 'vehicleDiscount', 'clothesDiscount', 'money', 'clothes', 'coins', 'vip', 'hidden', 'animation')
 * @param  {Object}     rewardData                      Данные награды. Они могут быть разными в зависимости от типа награды.
 * @param  {Number}     destroyXP                       Кол-во XP получаемого при распылении награды.
 * @param  {String}     color                           Цвет награды. ('gray', 'blue', 'purple', 'red', 'gold')
 */

export const seasonPassLevels = [{
    id: 1,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'items',
			variants: [
                { rewardType: 'item', rewardData: { itemId: 725, count: 0   }, destroyXP: 500, color: 'red'     }, // Ремонтный комплект для оружия
				{ rewardType: 'item', rewardData: { itemId: 89,  count: 20  }, destroyXP: 250, color: 'blue'    }, // Желтая аптечка (хил 75хп)
				{ rewardType: 'item', rewardData: { itemId: 525, count: 0   }, destroyXP: 300, color: 'purple'  }, // BIOLINK
                { rewardType: 'item', rewardData: { itemId: 733, count: 10  }, destroyXP: 500, color: 'red'     }, // Стайлинг ManeMaster
                { rewardType: 'item', rewardData: { itemId: 736, count: 8   }, destroyXP: 100, color: 'gray'    }, // Гавайская пицца
                { rewardType: 'item', rewardData: { itemId: 731, count: 20  }, destroyXP: 500, color: 'red'     }, // (20 штук) Капсулы восстановления
			]
		},  destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 2,
    free: [
        { rewardType: 'item', rewardData: { itemId: 732, count: 0 }, destroyXP: 200, color: 'blue' }, // Протеиновый батончик
        { rewardType: 'item', rewardData: { itemId: 732, count: 0 }, destroyXP: 200, color: 'blue' }, // Протеиновый батончик
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 300, color: 'purple' }, // BIOLINK
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 300, color: 'purple' }, // BIOLINK
        { rewardType: 'item', rewardData: { itemId: 89, count: 10 }, destroyXP: 100, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 100, color: 'gray' }, // Адреналин (= эпинефрин)
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 500, color: 'blue' },

    ],
    premium: [
        { rewardType: 'vip', rewardData: { type: 'Silver', days: 10 }, destroyXP: 500, color: 'blue' },
    ],
},
{
    id: 3,
    free: [
        { rewardType: 'item', rewardData: { itemId: 736, count: 8 }, destroyXP: 100, color: 'gray' }, // Гавайская пицца
        { rewardType: 'item', rewardData: { itemId: 737, count: 0 }, destroyXP: 100, color: 'gray' }, // Консервированные бобы
        { rewardType: 'item', rewardData: { itemId: 736, count: 8 }, destroyXP: 100, color: 'gray' }, // Гавайская пицца
        { rewardType: 'item', rewardData: { itemId: 737, count: 0 }, destroyXP: 100, color: 'gray' }, // Консервированные бобы
    ],
    premium: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 5 }, destroyXP: 250, color: 'gray' },
    ]
},
{
    id: 4,
    free: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 13 // Бронежилет мем Доги так себе
            },
            destroyXP: 250,
            color: 'gray'
        },
    ],
    premium: [
        { rewardType: 'clothesDiscount', rewardData: { amount: 5 }, destroyXP: 250, color: 'gray' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3284, [1]: 6350 } }, destroyXP: 250, color: 'gray' },
        { rewardType: 'item', rewardData: { itemId: 726, count: 0 }, destroyXP: 300, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
        { rewardType: 'item', rewardData: { itemId: 735, count: 0 }, destroyXP: 200, color: 'blue' }, // Странный бургер
    ]
},
{
    id: 5,
    free: [
        { rewardType: 'clothes', destroyXP: 250, color: 'gray', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2164, textures: 8, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2169, textures: 7, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 250, color: 'gray', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2364, textures: 8, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2330, textures: 7, isProp: 0 } } } }
    ]
},
{
    id: 6,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'boor' }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 738, count: 0 }, destroyXP: 300, color: 'purple' }, // Непредсказуемый коктейль Реднека
        { rewardType: 'item', rewardData: { itemId: 732, count: 0 }, destroyXP: 200, color: 'blue' }, // Протеиновый батончик
        { rewardType: 'item', rewardData: { itemId: 733, count: 10 }, destroyXP: 500, color: 'red' }, // Стайлинг ManeMaster
        { rewardType: 'item', rewardData: { itemId: 726, count: 0 }, destroyXP: 300, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
    ]
},
{
    id: 7,
    free: [
        { rewardType: 'item', rewardData: { itemId: 734, count: 0 }, destroyXP: 200, color: 'blue' }, // Благодарственное письмо губернатора
        { rewardType: 'item', rewardData: { itemId: 735, count: 0 }, destroyXP: 200, color: 'blue' }, // Странный бургер
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 300, color: 'purple' }, // BIOLINK
        { rewardType: 'item', rewardData: { itemId: 737, count: 0 }, destroyXP: 100, color: 'gray' }, // Консервированные бобы
    ],
    premium: [
        { euroCoins: 200, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
    ]
},
{
    id: 8,
    free: [
        { color: 'gray', destroyXP: 250, rewardData: { id: 77 }, rewardType: 'animation' },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3285, [1]: 6351 } }, destroyXP: 250, color: 'gray' },
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
        { rewardType: 'item', rewardData: { itemId: 728, count: 0 }, destroyXP: 200, color: 'blue' }, // Биодобавка 1 уровня
        { rewardType: 'item', rewardData: { itemId: 737, count: 0 }, destroyXP: 100, color: 'gray' }, // Консервированные бобы
    ]
},
{
    id: 9,
    free: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
    ]
},
{
    id: 10,
    free: [
        { rewardType: 'clothes', destroyXP: 250, color: 'gray', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2171, texture: 0, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2192, textures: 7, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 250, color: 'gray', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2366, textures: 7, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2360, textures: 7, isProp: 0 } } } }
    ]
},
{
    id: 11,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3288, [1]: 6354 } }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 78, amount: 2 }, destroyXP: 150, color: 'gray' },
    ]
},
{
    id: 12,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 106, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 141, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 176, amount: 2 },  },
			]
		}, destroyXP: 150, color: 'gray' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'armourSkin',
			variants: [
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 23 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Dark Red
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 24 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Gray
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 25 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Purple
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 26 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Red
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 27 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Brown
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 28 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Green
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 29 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Dark Blue
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 30 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Blue
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 31 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Gray 2
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 32 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Purple
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 33 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Black
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 34 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Yellow
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 35 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion White
            ]
        }, destroyXP: 250, color: 'gray' },
    ]
},
{
    id: 13,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 250, color: 'gray' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 113, amount: 2 }, destroyXP: 150, color: 'gray' },
    ]
},
{
    id: 14,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 148, amount: 2 }, destroyXP: 150, color: 'gray' },
    ]
},
{
    id: 15,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 99, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 134, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 169, amount: 2 },  },
			]
		}, destroyXP: 150, color: 'gray' },
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 250, color: 'gray', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2367, textures: 8, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2364, textures: 7, isProp: 0 } } } }
    ]
},
{
    id: 16,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'tahoma' }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 85, amount: 2 }, destroyXP: 150, color: 'gray' }
    ]
},
{
    id: 17,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'armourSkin',
			variants: [
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 2 }, }, // Бронежилет Почта россии
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 3 }, }, // Бронежилет Валдберис
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 4 }, }, // Бронежилет Алиэкспресс
            ]
        }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 120, amount: 2 }, destroyXP: 150, color: 'gray' },
        { rewardType: 'wheels', rewardData: { id: 155, amount: 2 }, destroyXP: 150, color: 'gray' },
        { rewardType: 'item', rewardData: { itemId: 728, count: 0 }, destroyXP: 200, color: 'blue' }, // Биодобавка 1 уровня
    ],
},
{
    id: 18,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 92, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 127, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 162, amount: 2 },  },
			]
		}, destroyXP: 150, color: 'gray' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 727, count: 25 }, destroyXP: 300, color: 'purple' }, // (25 штук) Качественный ремонтный набор
        { rewardType: 'item', rewardData: { itemId: 726, count: 0 }, destroyXP: 300, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
        { rewardType: 'item', rewardData: { itemId: 737, count: 0 }, destroyXP: 100, color: 'gray' }, // Консервированные бобы
    ],
},
{
    id: 19,
    free: [
        { rewardType: 'wheels', rewardData: { id: 92, amount: 2 }, destroyXP: 150, color: 'gray' },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3311, [1]: 6377 } }, destroyXP: 250, color: 'gray' },
    ],
},
{
    id: 20,
    free: [
        { rewardType: 'clothes', destroyXP: 250, color: 'gray', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2368, textures: 6, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2361, textures: 8, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3312, [1]: 6378 } }, destroyXP: 250, color: 'gray' },
    ]
},
{
    id: 21,
    free: [
        { color: 'gray', destroyXP: 250, rewardData: { id: 141 }, rewardType: 'animation' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 127, amount: 2 }, destroyXP: 150, color: 'gray' },
        { rewardType: 'wheels', rewardData: { id: 162, amount: 2 }, destroyXP: 150, color: 'gray' },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 10 }, destroyXP: 500, color: 'blue' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 10 }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 22,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 85, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 120, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 155, amount: 2 },  },
			]
		}, destroyXP: 150, color: 'gray' },
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
    ]
},
{
    id: 23,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'manchez3' }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 99, amount: 2 }, destroyXP: 150, color: 'gray' },
    ]
},
{
    id: 24,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3313, [1]: 6379 } }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3293, [1]: 6359 } }, destroyXP: 250, color: 'gray' },
    ]
},
{
    id: 25,
    free: [
        { rewardType: 'clothes', destroyXP: 500, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2179, textures: 9, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2193, textures: 7, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 500, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2356, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2362, textures: 7, isProp: 0 } } } }
    ]
},
{
    id: 26,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'tulip2' }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 134, amount: 2 }, destroyXP: 150, color: 'gray' },
        { rewardType: 'wheels', rewardData: { id: 169, amount: 2 }, destroyXP: 150, color: 'gray' },
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 300, color: 'purple' }, // BIOLINK
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
    ],
},
{
    id: 27,
    free: [
        { rewardType: 'item', rewardData: { itemId: 729, count: 0 }, destroyXP: 300, color: 'purple' }, // Биодобавка 2 уровня
        { rewardType: 'item', rewardData: { itemId: 736, count: 8 }, destroyXP: 100, color: 'gray' }, // Гавайская пицца
        { rewardType: 'item', rewardData: { itemId: 734, count: 0 }, destroyXP: 200, color: 'blue' }, // Благодарственное письмо губернатора
        { rewardType: 'item', rewardData: { itemId: 735, count: 0 }, destroyXP: 200, color: 'blue' }, // Странный бургер
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 106, amount: 2 }, destroyXP: 150, color: 'gray' },
    ]
},
{
    id: 28,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 78, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 113, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 148, amount: 2 },  },
			]
		}, destroyXP: 150, color: 'gray' },
    ],
    premium: [
        { color: 'gray', destroyXP: 250, rewardData: { id: 158 }, rewardType: 'animation' },
    ]
},
{
    id: 29,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 141, amount: 2 }, destroyXP: 150, color: 'gray' },
        { rewardType: 'wheels', rewardData: { id: 176, amount: 2 }, destroyXP: 150, color: 'gray' },
        { rewardType: 'item', rewardData: { itemId: 734, count: 0 }, destroyXP: 200, color: 'blue' }, // Благодарственное письмо губернатора
    ],
},
{
    id: 30,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'armourSkin',
			variants: [
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 23 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Dark Red
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 24 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Gray
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 25 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Purple
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 26 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Red
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 27 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Brown
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 28 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Green
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 29 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Dark Blue
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 30 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Blue
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 31 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Gray 2
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 32 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Purple
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 33 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Black
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 34 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion Yellow
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 35 },   color: 'gray', destroyXP: 250 }, // Бронежилет Evangelion White
            ]
        }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 500, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2065, textures: 7, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2329, textures: 8, isProp: 0 } } } }
    ]
},
{
    id: 31,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 79, amount: 2 }, destroyXP: 300, color: 'blue' },
    ]
},
{
    id: 32,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 107, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 142, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 177, amount: 2 },  },
			]
		}, destroyXP: 300, color: 'blue' },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3295, [1]: 6361 } }, destroyXP: 250, color: 'gray' },
    ],
},
{
    id: 33,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 114, amount: 2 }, destroyXP: 300, color: 'blue' },
        { rewardType: 'wheels', rewardData: { id: 149, amount: 2 }, destroyXP: 300, color: 'blue' },
        { rewardType: 'item', rewardData: { itemId: 729, count: 0 }, destroyXP: 300, color: 'purple' }, // Биодобавка 2 уровня
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3286, [1]: 6352 } }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 34,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 100, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 135, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 170, amount: 2 },  },
			]
		}, destroyXP: 300, color: 'blue' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'armourSkin',
			variants: [
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 10 }, }, // Бронежилет Аниме прикольное
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 8 }, }, // Бронежилет Аниме так себе
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 9 }, }, // Бронежилет Аниме так себе
            ]
        }, destroyXP: 250, color: 'gray' },
    ]
},
{
    id: 35,
    free: [
        { rewardType: 'clothes', destroyXP: 500, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2340, textures: 7, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2332, textures: 8, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 86, amount: 2 }, destroyXP: 300, color: 'blue' },
    ]
},
{
    id: 36,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'surfer3' }, destroyXP: 1000, color: 'purple' },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3287, [1]: 6353 } }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 37,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3299, [1]: 6365 } }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 121, amount: 2 }, destroyXP: 300, color: 'blue' },
        { rewardType: 'wheels', rewardData: { id: 156, amount: 2 }, destroyXP: 300, color: 'blue' },
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 500, color: 'blue' },
        { rewardType: 'vip', rewardData: { type: 'Silver', days: 10 }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 38,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 93, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 128, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 163, amount: 2 },  },
			]
		}, destroyXP: 300, color: 'blue' },
    ],
    premium: [
        { color: 'blue', destroyXP: 500, rewardData: { id: 98 }, rewardType: 'animation' },
    ]
},
{
    id: 39,
    free: [
        { rewardType: 'item', rewardData: { itemId: 736, count: 8 }, destroyXP: 100, color: 'gray' }, // Гавайская пицца
        { rewardType: 'item', rewardData: { itemId: 730, count: 0 }, destroyXP: 500, color: 'red' }, // Биодобавка 3 уровня
        { rewardType: 'item', rewardData: { itemId: 738, count: 0 }, destroyXP: 300, color: 'purple' }, // Непредсказуемый коктейль Реднека
        { rewardType: 'item', rewardData: { itemId: 734, count: 0 }, destroyXP: 200, color: 'blue' }, // Благодарственное письмо губернатора
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 93, amount: 2 }, destroyXP: 300, color: 'blue' },
    ]
},
{
    id: 40,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicle',
			variants: [
				{ rewardType: 'vehicle', rewardData: { model: 'xc90' },  },
				{ rewardType: 'vehicle', rewardData: { model: 'ae86' },  },
				{ rewardType: 'vehicle', rewardData: { model: 'r300' },  },
			]
		}, destroyXP: 1000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 128, amount: 2 }, destroyXP: 300, color: 'blue' },
        { rewardType: 'wheels', rewardData: { id: 163, amount: 2 }, destroyXP: 300, color: 'blue' },
        { rewardType: 'item', rewardData: { itemId: 729, count: 0 }, destroyXP: 300, color: 'purple' }, // Биодобавка 2 уровня
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023

    ]
},
{
    id: 41,
    free: [
        { rewardType: 'clothes', destroyXP: 500, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2165, textures: 7, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2338, texture: 0, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 500, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2341, textures: 7, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2334, textures: 11, isProp: 0 } } } }
    ]
},
{
    id: 42,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 86, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 121, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 156, amount: 2 },  },
			]
		}, destroyXP: 300, color: 'blue' },
    ],
    premium: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 22 // Бронежилет Off-White
            },
            destroyXP: 250,
            color: 'purple'
        },
    ]
},
{
    id: 43,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3300, [1]: 6366 } }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 100, amount: 2 }, destroyXP: 300, color: 'blue' },
    ]
},
{
    id: 44,
    free: [
        { rewardType: 'variants', destroyXP: 500, color: 'blue',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2169, texture: 0, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2339, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2164, texture: 2, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2329, texture: 6, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2179, texture: 6, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2330, texture: 2, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'variants', destroyXP: 500, color: 'blue',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2348, texture: 0, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2351, texture: 2, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2341, texture: 2, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2343, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2340, texture: 1, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2360, texture: 0, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ]
},
{
    id: 45,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'broadway' }, destroyXP: 1000, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 135, amount: 2 }, destroyXP: 300, color: 'blue' },
        { rewardType: 'wheels', rewardData: { id: 170, amount: 2 }, destroyXP: 300, color: 'blue' },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 15 }, destroyXP: 750, color: 'purple' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 15 }, destroyXP: 750, color: 'purple' },
    ]
},
{
    id: 46,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'xes', setId: 4 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'xes', setId: 5 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'xes', setId: 6 }, },
            ]
		}, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3289, [1]: 6355 } }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 47,
    free: [
        { color: 'blue', destroyXP: 500, rewardData: { id: 255 }, rewardType: 'animation' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 107, amount: 2 }, destroyXP: 300, color: 'blue' },
    ]
},
{
    id: 48,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 79, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 114, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 149, amount: 2 },  },
			]
		}, destroyXP: 300, color: 'blue' },
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 49,
    free: [
        { rewardType: 'variants', destroyXP: 500, color: 'blue',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2175, texture: 0, isProp: 0, gender: 0}, 1: { component: 4, drawable: 2172, texture: 3, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2179, texture: 4, isProp: 0, gender: 0}, 1: { component: 4, drawable: 2182, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2065, texture: 3, isProp: 0, gender: 0}, 1: { component: 4, drawable: 2182, texture: 2, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'variants', destroyXP: 500, color: 'blue',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2339, texture: 7, isProp: 0, gender: 0}, 1: { component: 4, drawable: 2173, texture: 8, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2340, texture: 4, isProp: 0, gender: 0}, 1: { component: 4, drawable: 2172, texture: 2, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2340, texture: 1, isProp: 0, gender: 0}, 1: { component: 4, drawable: 2172, texture: 1, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ]
},
{
    id: 50,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicle',
			variants: [
				{ rewardType: 'vehicle', rewardData: { model: 'm3g81' },  },
				{ rewardType: 'vehicle', rewardData: { model: 'fx50s' },  },
				{ rewardType: 'vehicle', rewardData: { model: 'issi8' },  },
			]
		}, destroyXP: 2000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 142, amount: 2 }, destroyXP: 300, color: 'blue' },
        { rewardType: 'wheels', rewardData: { id: 177, amount: 2 }, destroyXP: 300, color: 'blue' },
        { rewardType: 'item', rewardData: { itemId: 729, count: 0 }, destroyXP: 300, color: 'purple' }, // Биодобавка 2 уровня
        { rewardType: 'item', rewardData: { itemId: 730, count: 2 }, destroyXP: 500, color: 'red' }, // Биодобавка 3 уровня
    ]
},
{
    id: 51,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3301, [1]: 6367 } }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 80, amount: 2 }, destroyXP: 500, color: 'purple' },
    ],
},
{
    id: 52,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 108, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 143, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 178, amount: 2 },  },
			]
		}, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'weaponSkin',
			variants: [
                { rewardType: 'weaponSkin', rewardData: { weaponName: 'WEAPON_CARBINERIFLE',  skinId: 4 },  destroyXP: 500, color: 'blue' }, // Аниме Норм
                { rewardType: 'weaponSkin', rewardData: { weaponName: 'WEAPON_CARBINERIFLE',  skinId: 1 },  destroyXP: 250, color: 'gray' }, // Аниме так себе
                { rewardType: 'weaponSkin', rewardData: { weaponName: 'WEAPON_COMBATPDW',     skinId: 0 },  destroyXP: 250, color: 'gray' }, // Аниме так себе
			]
		}, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 53,
    free: [
        { rewardType: 'item', rewardData: { itemId: 730, count: 2 }, destroyXP: 500, color: 'red' }, // Биодобавка 3 уровня
        { rewardType: 'item', rewardData: { itemId: 727, count: 25 }, destroyXP: 300, color: 'purple' }, // (25 штук) Качественный ремонтный набор
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 300, color: 'purple' }, // BIOLINK
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 300, color: 'purple' }, // BIOLINK

    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 115, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 150, amount: 2 }, destroyXP: 500, color: 'purple' },
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
    ]
},
{
    id: 54,
    free: [
        { rewardType: 'clothes', destroyXP: 500, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2172, texture: 0, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2181, texture: 0, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 337, count: 0 }, destroyXP: 750, color: 'purple' }, // Винтовка Marksman Mk2
    ]
},
{
    id: 55,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 101, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 136, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 171, amount: 2 },  },
			]
		}, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 87, amount: 2 }, destroyXP: 500, color: 'purple' },
    ]
},
{
    id: 56,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'macan', setId: 4 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'macan', setId: 5 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'macan', setId: 6 }, },
            ]
		}, destroyXP: 1500, color: 'gold' },
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
        { rewardType: 'money', rewardData: { amount: 50000 }, destroyXP: 750, color: 'purple' },
        { rewardType: 'vip', rewardData: { type: 'Gold', days: 15 }, destroyXP: 750, color: 'purple' },
        { rewardType: 'coins', rewardData: { amount: 300 }, destroyXP: 300, color: 'purple' },
    ]
},
{
    id: 57,
    free: [
        { color: 'blue', destroyXP: 500, rewardData: { id: 110 }, rewardType: 'animation' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 122, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 157, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'item', rewardData: { itemId: 89, count: 30 }, destroyXP: 250, color: 'purple' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 30 }, destroyXP: 250, color: 'purple' }, // Адреналин (= эпинефрин)
    ]
},
{
    id: 58,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 94, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 129, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 164, amount: 2 },  },
			]
		}, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
    ]
},
{
    id: 59,
    free: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2178, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2175, texture: 0, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2343, textures: 8, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2340, texture: 0, isProp: 0 } } } }
    ]
},
{
    id: 60,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'everon2' }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'slr' }, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 61,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3307, [1]: 6373 } }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 129, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 164, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 94, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 101, amount: 2 }, destroyXP: 500, color: 'purple' },
    ]
},
{
    id: 62,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 87, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 122, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 157, amount: 2 },  },
			]
		}, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 726, count: 0 }, destroyXP: 300, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 300, color: 'purple' }, // BIOLINK
        { rewardType: 'item', rewardData: { itemId: 735, count: 0 }, destroyXP: 200, color: 'blue' }, // Странный бургер
        { rewardType: 'item', rewardData: { itemId: 727, count: 25 }, destroyXP: 300, color: 'purple' }, // (25 штук) Качественный ремонтный набор
    ]
},
{
    id: 63,
    free: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2345, textures: 8, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2178, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2067, textures: 4, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2333, textures: 8, isProp: 0 } } } }
    ]
},
{
    id: 64,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3292, [1]: 6358 } }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'g63', setId: 11 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'g63', setId: 12 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'g63', setId: 13 }, },
            ]
		}, destroyXP: 2000, color: 'gold' },
    ]
},
{
    id: 65,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'powersurge' }, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 136, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 171, amount: 2 }, destroyXP: 500, color: 'purple' },
    ]
},
{
    id: 66,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },

        { euroCoins: 50, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },

        { euroCoins: 50, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },

        { euroCoins: 50, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'weaponSkin',
			variants: [
                { rewardType: 'weaponSkin', rewardData: { weaponName: 'WEAPON_CARBINERIFLE_MK2',  skinId: 0 }, destroyXP: 500, color: 'blue' }, // Цветной микс
                { rewardType: 'weaponSkin', rewardData: { weaponName: 'WEAPON_SMG_MK2',           skinId: 1 }, destroyXP: 500, color: 'blue' }, // Цветной микс
                { rewardType: 'weaponSkin', rewardData: { weaponName: 'WEAPON_REVOLVER',          skinId: 0 }, destroyXP: 500, color: 'blue' }, // Цветной микс
                { rewardType: 'weaponSkin', rewardData: { weaponName: 'WEAPON_HEAVYSHOTGUN',      skinId: 1 }, destroyXP: 500, color: 'blue' }, // Цветной микс
			]
		}, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 67,
    free: [
        { color: 'purple', destroyXP: 750, rewardData: { id: 91 }, rewardType: 'animation' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 108, amount: 2 }, destroyXP: 500, color: 'purple' },
    ],
},
{
    id: 68,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 80, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 115, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 150, amount: 2 },  },
			]
		}, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
        { rewardType: 'wheels', rewardData: { id: 143, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 178, amount: 2 }, destroyXP: 500, color: 'purple' },
    ]
},
{
    id: 69,
    free: [
        { rewardType: 'item', rewardData: { itemId: 736, count: 8 }, destroyXP: 100, color: 'gray' }, // Гавайская пицца
        { rewardType: 'item', rewardData: { itemId: 731, count: 20 }, destroyXP: 500, color: 'red' }, // (20 штук) Капсулы восстановления
        { rewardType: 'item', rewardData: { itemId: 733, count: 10 }, destroyXP: 500, color: 'red' }, // Стайлинг ManeMaster
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
    ]
},
{
    id: 70,
    free: [
        { rewardType: 'wheels', rewardData: { id: 81, amount: 2 }, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'giulia' }, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 71,
    free: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2166, textures: 6, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2171, textures: 7, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2346, textures: 8, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2331, textures: 7, isProp: 0 } } } }
    ]
},
{
    id: 72,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 109, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 144, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 179, amount: 2 },  },
			]
		}, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3296, [1]: 6362 } }, destroyXP: 750, color: 'purple' },
    ]
},
{
    id: 73,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'weaponSkin',
			variants: [
                { rewardType: 'weaponSkin', rewardData: { weaponName: 'WEAPON_COMBATPDW',         skinId: 2 },  color: 'purple',  destroyXP: 750 }, // Золотая калиграфия
                { rewardType: 'weaponSkin', rewardData: { weaponName: 'WEAPON_HEAVYSHOTGUN',      skinId: 2 },  color: 'gray',    destroyXP: 250 }, // Что-то непонятное
                { rewardType: 'weaponSkin', rewardData: { weaponName: 'WEAPON_REVOLVER',          skinId: 2 },  color: 'gray',    destroyXP: 250 }, // Говно
                { rewardType: 'weaponSkin', rewardData: { weaponName: 'WEAPON_REVOLVER',          skinId: 4 },  color: 'purple',  destroyXP: 750 }, // Прикольный
			]
		}, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 116, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 151, amount: 2 }, destroyXP: 500, color: 'purple' },
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
    ]
},
{
    id: 74,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 102, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 137, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 172, amount: 2 },  },
			]
		}, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'gt63s', setId: 7 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'gt63s', setId: 8 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'gt63s', setId: 9 }, },
            ]
		}, destroyXP: 1500, color: 'gold' },
    ]
},
{
    id: 75,
    free: [
        { rewardType: 'variants', destroyXP: 750, color: 'purple',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2170, texture: 0, isProp: 0, gender: 0}, 1: { component: 4, drawable: 2180, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2178, texture: 4, isProp: 0, gender: 0}, 1: { component: 4, drawable: 2185, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2178, texture: 3, isProp: 0, gender: 0}, 1: { component: 4, drawable: 2193, texture: 2, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'variants', destroyXP: 750, color: 'purple',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2351, texture: 0, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2337, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2343, texture: 4, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2351, texture: 1, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2343, texture: 1, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2351, texture: 5, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ]
},
{
    id: 76,
    free: [
        { rewardType: 'wheels', rewardData: { id: 88, amount: 2 }, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'giulia', setId: 4 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'giulia', setId: 5 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'giulia', setId: 6 }, },
            ]
		}, destroyXP: 2000, color: 'gold' },
    ],
},
{
    id: 77,
    free: [
        { rewardType: 'item', rewardData: { itemId: 725, count: 0 }, destroyXP: 500, color: 'red' }, // Ремонтный комплект для оружия
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 123, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 158, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 1200, color: 'red' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 20 }, destroyXP: 1200, color: 'red' },
    ]
},
{
    id: 78,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 95, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 130, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 165, amount: 2 },  },
			]
		}, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 95, amount: 2 }, destroyXP: 500, color: 'purple' },
    ]
},
{
    id: 79,
    free: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2182, textures: 7, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2191, textures: 8, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2365, textures: 7, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2359, textures: 8, isProp: 0 } } } }
    ]
},
{
    id: 80,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'diavel' }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'cls2' }, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 81,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3298, [1]: 6364 } }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 130, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 165, amount: 2 }, destroyXP: 500, color: 'purple' },
        { euroCoins: 200, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
        { euroCoins: 200, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
    ],
},
{
    id: 82,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 88, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 123, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 158, amount: 2 },  },
			]
		}, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'veyron', setId: 8 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'veyron', setId: 9 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'veyron', setId: 10 }, },
            ]
		}, destroyXP: 3000, color: 'gold' },
    ],
},
{
    id: 83,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3294, [1]: 6360 } }, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 102, amount: 2 }, destroyXP: 500, color: 'purple' },
    ]
},
{
    id: 84,
    free: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2018, textures: 10, isProp: 1 }, 1: { gender: 1, component: 4, drawable: 2194, textures: 7, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 0, drawable: 2077, textures: 8, isProp: 1 }, 1: { gender: 1, component: 11, drawable: 2336, textures: 8, isProp: 0 } } } }
    ]
},
{
    id: 85,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'panthere' }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 137, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 172, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'item', rewardData: { itemId: 725, count: 0 }, destroyXP: 500, color: 'red' }, // Ремонтный комплект для оружия
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3297, [1]: 6363 } }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 86,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'armourSkin',
			variants: [
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 18 }, color: 'red',     destroyXP: 1500  }, // Бронежилет Brand collections NASA Blue
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 17 }, color: 'purple',  destroyXP: 750   }, // Бронежилет Brand collections Bape Purple
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 20 }, color: 'blue',    destroyXP: 500   }, // Бронежилет Brand collections Monster Green
            ]
        }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'giulia', setId: 7 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'giulia', setId: 8 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'giulia', setId: 9 }, },
            ]
		}, destroyXP: 2000, color: 'gold' },
    ]
},
{
    id: 87,
    free: [
        { color: 'purple', destroyXP: 750, rewardData: { id: 97 }, rewardType: 'animation' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 109, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3290, [1]: 6356 } }, destroyXP: 1250, color: 'red' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3306, [1]: 6372 } }, destroyXP: 500, color: 'blue' },
    ],
},
{
    id: 88,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 81, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 116, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 151, amount: 2 },  },
			]
		}, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'cls2', setId: 4 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'cls2', setId: 5 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'cls2', setId: 6 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'cls2', setId: 7 }, },
            ]
		}, destroyXP: 2000, color: 'gold' },
    ]
},
{
    id: 89,
    free: [
        { rewardType: 'wheels', rewardData: { id: 82, amount: 2 }, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 144, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 179, amount: 2 }, destroyXP: 500, color: 'purple' },
        { rewardType: 'money', rewardData: { amount: 150000 }, destroyXP: 1500, color: 'red' },
        { rewardType: 'vip', rewardData: { type: 'Platinum', days: 30 }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 90,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'ab2' }, destroyXP: 4000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: '400z' }, destroyXP: 4000, color: 'gold' },
    ]
},
{
    id: 91,
    free: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2177, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2170, textures: 8, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2363, textures: 6, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2328, textures: 8, isProp: 0 } } } }
    ]
},
{
    id: 92,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 110, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 145, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 180, amount: 2 },  },
			]
		}, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 117, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 152, amount: 2 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 93,
    free: [
        { rewardType: 'clothes', destroyXP: 1000, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2173, textures: 8, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2186, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1500, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2354, textures: 8, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2357, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 94,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 103, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 138, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 173, amount: 2 },  },
			]
		}, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: '400z', setId: 3 }, },
                { rewardType: 'vehicleSet', rewardData: { model: '400z', setId: 4 }, },
                { rewardType: 'vehicleSet', rewardData: { model: '400z', setId: 5 }, },
            ]
		}, destroyXP: 2000, color: 'gold' },
    ]
},
{
    id: 95,
    free: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2177, textures: 10, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2076, textures: 4, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1500, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2342, textures: 7, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2078, textures: 5, isProp: 0 } } } }
    ]
},
{
    id: 96,
    free: [
        { rewardType: 'wheels', rewardData: { id: 89, amount: 2 }, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'giulia', setId: 10 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'giulia', setId: 11 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'giulia', setId: 12 }, },
            ]
		}, destroyXP: 2000, color: 'gold' },
    ]
},
{
    id: 97,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 96, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 131, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 166, amount: 2 },  },
			]
		}, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 124, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 159, amount: 2 }, destroyXP: 1000, color: 'red' },
    ],
},
{
    id: 98,
    free: [
        { rewardType: 'clothes', destroyXP: 1000, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2181, textures: 8, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2085, textures: 20, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1500, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2344, textures: 8, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2084, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 99,
    free: [
        { rewardType: 'clothes', destroyXP: 1000, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2183, textures: 7, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2174, texture: 0, isProp: 0 } } } }
    ],
    premium: [
        { color: 'red', destroyXP: 1000, rewardData: { id: 245 }, rewardType: 'animation' },
    ]
},
{
    id: 100,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'hummer3' }, destroyXP: 5000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1500, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2369, textures: 7, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2177, textures: 4, isProp: 0 } } } }
    ]
},
{
    id: 101,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3308, [1]: 6374 } }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 131, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 166, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 25 }, destroyXP: 1750, color: 'gold' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 25 }, destroyXP: 1750, color: 'gold' },
    ]
},
{
    id: 102,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 89, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 124, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 159, amount: 2 },  },
			]
		}, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 103, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 96, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3309, [1]: 6375 } }, destroyXP: 750, color: 'purple' },
        { rewardType: 'item', rewardData: { itemId: 627, count: 0 }, destroyXP: 3000, color: 'gold' }, // Дефибриллятор Mk2
    ]
},
{
    id: 103,
    free: [
        { rewardType: 'item', rewardData: { itemId: 452, count: 0 }, destroyXP: 3000, color: 'gold' }, // Дрон
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 104,
    free: [
        { euroCoins: 100, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },

        { euroCoins: 100, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },

        { euroCoins: 100, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },

        { euroCoins: 100, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: '400z', setId: 6 }, },
                { rewardType: 'vehicleSet', rewardData: { model: '400z', setId: 7 }, },
                { rewardType: 'vehicleSet', rewardData: { model: '400z', setId: 8 }, },
            ]
		}, destroyXP: 2000, color: 'gold' },
    ]
},
{
    id: 105,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'entity3' }, destroyXP: 2000, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 138, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 173, amount: 2 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 106,
    free: [
        { rewardType: 'clothes', destroyXP: 1000, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2019, textures: 10, isProp: 1 }, 1: { gender: 1, component: 4, drawable: 2179, texture: 0, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1500, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2358, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2349, texture: 0, isProp: 0 } } } }
    ]
},
{
    id: 107,
    free: [
        { rewardType: 'variants', destroyXP: 750, color: 'purple',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 1, drawable: 2017, texture: 0, isProp: 1, gender: 0}, 1: { component: 4, drawable: 2184, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2176, texture: 2, isProp: 0, gender: 0}, 1: { component: 4, drawable: 2180, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2176, texture: 6, isProp: 0, gender: 0}, 1: { component: 4, drawable: 2181, texture: 0, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'variants', destroyXP: 1500, color: 'red',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 1, drawable: 2015, texture: 0, isProp: 1, gender: 0}, 1: { component: 11, drawable: 2355, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 1, drawable: 2019, texture: 4, isProp: 1, gender: 0}, 1: { component: 11, drawable: 2354, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 1, drawable: 2018, texture: 1, isProp: 1, gender: 0}, 1: { component: 11, drawable: 2353, texture: 0, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ]
},
{
    id: 108,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 82, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 117, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 152, amount: 2 },  },
			]
		}, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 110, amount: 2 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 109,
    free: [
        { rewardType: 'variants', destroyXP: 1000, color: 'red',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2168, texture: 0, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2350, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2178, texture: 4, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2345, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2178, texture: 3, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2342, texture: 0, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 145, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 180, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'item', rewardData: { itemId: 725, count: 0 }, destroyXP: 500, color: 'red' }, // Ремонтный комплект для оружия
        { rewardType: 'item', rewardData: { itemId: 733, count: 10 }, destroyXP: 500, color: 'red' }, // Стайлинг ManeMaster
    ]
},
{
    id: 110,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'sclass4' }, destroyXP: 6000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'variants', destroyXP: 1500, color: 'red',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2352, texture: 0, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2348, texture: 4, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2345, texture: 4, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2348, texture: 2, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2346, texture: 5, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2346, texture: 0, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ]
},
{
    id: 111,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3310, [1]: 6376 } }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 83, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3303, [1]: 6369 } }, destroyXP: 1250, color: 'red' },
        { rewardType: 'coins', rewardData: { amount: 1000 }, destroyXP: 1000, color: 'red' }
    ]
},
{
    id: 112,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 111, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 146, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 181, amount: 2 },  },
			]
		}, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 627, count: 0 }, destroyXP: 3000, color: 'gold' }, // Дефибриллятор Mk2
    ]
},
{
    id: 113,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'virtue' }, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 118, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 153, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'money', rewardData: { amount: 150000 }, destroyXP: 1500, color: 'red' },
        { rewardType: 'vip', rewardData: { type: 'Platinum', days: 30 }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 114,
    free: [
        { color: 'red', destroyXP: 1500, rewardData: { id: 248 }, rewardType: 'animation' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: '400z', setId: 9 }, },
                { rewardType: 'vehicleSet', rewardData: { model: '400z', setId: 10 }, },
                { rewardType: 'vehicleSet', rewardData: { model: '400z', setId: 11 }, },
            ]
		}, destroyXP: 2000, color: 'gold' },
    ]
},
{
    id: 115,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 104, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 139, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 174, amount: 2 },  },
			]
		}, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 90, amount: 2 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 116,
    free: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 3000, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'panamera17turbo', setId: 6 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'panamera17turbo', setId: 7 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'panamera17turbo', setId: 8 }, },
            ]
		}, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 117,
    free: [
        { rewardType: 'clothes', destroyXP: 1000, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2167, texture: 0, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2190, textures: 5, isProp: 0 } } } }
    ],
    premium: [
        // { rewardType: 'hidden', rewardData: { id: 84, amount: 2 }, destroyXP: 1500, color: 'red' },
        { rewardType: 'clothes', destroyXP: 1500, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2349, texture: 0, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2352, textures: 5, isProp: 0 } } } }
    ]
},
{
    id: 118,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 97, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 132, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 167, amount: 2 },  },
			]
		}, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 125, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 160, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 1200, color: 'red' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 20 }, destroyXP: 1200, color: 'red' },
    ]
},
{
    id: 119,
    free: [
        { rewardType: 'clothes', destroyXP: 3000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2180, textures: 5, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2176, texture: 0, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 5000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2362, textures: 6, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2347, texture: 0, isProp: 0 } } } }
    ]
},
{
    id: 120,
    free: [
        { rewardType: 'wheels', rewardData: { id: 97, amount: 2 }, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'mc20' }, destroyXP: 7000, color: 'gold' },
    ]
},
{
    id: 121,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3302, [1]: 6368 } }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 132, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 167, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'item', rewardData: { itemId: 730, count: 0 }, destroyXP: 500, color: 'red' }, // Биодобавка 3 уровня
        { rewardType: 'item', rewardData: { itemId: 730, count: 0 }, destroyXP: 500, color: 'red' }, // Биодобавка 3 уровня
    ]
},
{
    id: 122,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 90, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 125, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 160, amount: 2 },  },
			]
		}, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 104, amount: 2 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 123,
    free: [
        { rewardType: 'clothes', destroyXP: 1500, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2071, textures: 20, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2083, texture: 0, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1500, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2363, textures: 6, isProp: 0 }, 1: { gender: 1, component: 1, drawable: 2016, texture: 0, isProp: 1 } } } }
    ]
},
{
    id: 124,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'armourSkin',
			variants: [
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 14 }, color: 'gold',    destroyXP: 3000  }, // Бронежилет Brand collections Louis Vuitton Black
                { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 15 }, color: 'red',     destroyXP: 1500  }, // Бронежилет Brand collections Supreme Red
            ]
        }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: '918s', setId: 6 }, },
                { rewardType: 'vehicleSet', rewardData: { model: '918s', setId: 7 }, },
                { rewardType: 'vehicleSet', rewardData: { model: '918s', setId: 8 }, },
            ]
		}, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 125,
    free: [
        { rewardType: 'variants', destroyXP: 1500, color: 'red',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2070, texture: 0, isProp: 0, gender: 0}, 1: { component: 6, drawable: 2080, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2067, texture: 2, isProp: 0, gender: 0}, 1: { component: 6, drawable: 2078, texture: 2, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2068, texture: 2, isProp: 0, gender: 0}, 1: { component: 6, drawable: 2076, texture: 2, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'variants', destroyXP: 5000, color: 'gold',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2069, texture: 0, isProp: 0, gender: 0}, 1: { component: 6, drawable: 2079, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2066, texture: 0, isProp: 0, gender: 0}, 1: { component: 6, drawable: 2075, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2073, texture: 0, isProp: 0, gender: 0}, 1: { component: 6, drawable: 2087, texture: 0, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ]
},
{
    id: 126,
    free: [
        { rewardType: 'wheels', rewardData: { id: 139, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 174, amount: 2 }, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'q8', setId: 16 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'q8', setId: 17 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'q8', setId: 18 }, },
            ]
		}, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 127,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 83, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 118, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 153, amount: 2 },  },
			]
		}, destroyXP: 1000, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 111, amount: 2 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 128,
    free: [
        { rewardType: 'clothes', destroyXP: 5000, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 5, drawable: 2052, textures: 7, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2341, texture: 0, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'variants', destroyXP: 5000, color: 'gold',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2350, texture: 1, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2356, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2068, texture: 4, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2344, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2068, texture: 3, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2344, texture: 1, isProp: 0, gender: 1 } } } }
                ]
            }
        },

        { rewardType: 'variants', destroyXP: 3000, color: 'gold',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2066, texture: 0, isProp: 0, gender: 0}, 1: { component: 6, drawable: 2082, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2178, texture: 4, isProp: 0, gender: 0}, 1: { component: 6, drawable: 2083, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 4, drawable: 2177, texture: 4, isProp: 0, gender: 0}, 1: { component: 6, drawable: 2077, texture: 0, isProp: 0, gender: 1 } } } }
                ]
            }
        },

        { rewardType: 'wheels', rewardData: { id: 146, amount: 2 }, destroyXP: 1000, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 181, amount: 2 }, destroyXP: 1000, color: 'red' },
    ]
},
{
    id: 129,
    free: [
        { color: 'gold', destroyXP: 4500, rewardData: { id: 257 }, rewardType: 'animation' },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3305, [1]: 6371 } }, destroyXP: 5000, color: 'gold' },
    ]
},
{
    id: 130,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'rrphantom2' }, destroyXP: 8000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 84, amount: 2 }, destroyXP: 2000, color: 'gold' },
    ]
},
{
    id: 131,
    free: [
        // { rewardType: 'hidden', rewardData: { id: 84, amount: 2 }, destroyXP: 2000, color: 'red' },
        { rewardType: 'clothes', destroyXP: 1500, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2068, textures: 5, isProp: 0 }, 1: { gender: 1, component: 5, drawable: 2045, textures: 7, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 5000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2347, textures: 6, isProp: 0 }, 1: { gender: 1, component: 5, drawable: 2046, textures: 3, isProp: 0 } } } }
    ]
},
{
    id: 132,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 112, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 147, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 182, amount: 2 },  },
			]
		}, destroyXP: 2000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 5000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2072, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2187, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 133,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3304, [1]: 6370 } }, destroyXP: 3500, color: 'gold' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 119, amount: 2 }, destroyXP: 2000, color: 'gold' },
        { rewardType: 'wheels', rewardData: { id: 154, amount: 2 }, destroyXP: 2000, color: 'gold' },
        { rewardType: 'item', rewardData: { itemId: 733, count: 10 }, destroyXP: 500, color: 'red' }, // Стайлинг ManeMaster
        { rewardType: 'item', rewardData: { itemId: 725, count: 0 }, destroyXP: 500, color: 'red' }, // Ремонтный комплект для оружия
    ]
},
{
    id: 134,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 105, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 140, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 175, amount: 2 },  },
			]
		}, destroyXP: 2000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'sclass3', setId: 8 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'sclass3', setId: 9 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'sclass3', setId: 10 }, },
            ]
		}, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 135,
    free: [
        { rewardType: 'clothes', destroyXP: 3000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 5, drawable: 2051, textures: 12, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2188, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 5000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2350, texture: 0, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2358, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 136,
    free: [
        { rewardType: 'wheels', rewardData: { id: 91, amount: 2 }, destroyXP: 2000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'hummer3', setId: 3 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'hummer3', setId: 4 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'hummer3', setId: 5 }, },
            ]
		}, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 137,
    free: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 3000, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 126, amount: 2 }, destroyXP: 2000, color: 'gold' },
        { rewardType: 'wheels', rewardData: { id: 161, amount: 2 }, destroyXP: 2000, color: 'gold' },
        { rewardType: 'wheels', rewardData: { id: 98, amount: 2 }, destroyXP: 2000, color: 'gold' },
    ]
},
{
    id: 138,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 98, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 133, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 168, amount: 2 },  },
			]
		}, destroyXP: 2000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 5000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2014, texture: 0, isProp: 1 }, 1: { gender: 1, component: 5, drawable: 2047, textures: 12, isProp: 0 } } } }
    ]
},
{
    id: 139,
    free: [
        { rewardType: 'variants', destroyXP: 1500, color: 'red',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2073, texture: 0, isProp: 0, gender: 0}, 1: { component: 1, drawable: 2018, texture: 0, isProp: 1, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2071, texture: 2, isProp: 0, gender: 0}, 1: { component: 1, drawable: 2019, texture: 0, isProp: 1, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 6, drawable: 2071, texture: 4, isProp: 0, gender: 0}, 1: { component: 1, drawable: 2019, texture: 8, isProp: 1, gender: 1 } } } }
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'variants', destroyXP: 5000, color: 'gold',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 1, drawable: 2016, texture: 0, isProp: 1, gender: 0}, 1: { component: 1, drawable: 2017, texture: 0, isProp: 1, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 1, drawable: 2015, texture: 0, isProp: 1, gender: 0}, 1: { component: 1, drawable: 2020, texture: 0, isProp: 1, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 1, drawable: 2017, texture: 0, isProp: 1, gender: 0}, 1: { component: 1, drawable: 2020, texture: 9, isProp: 1, gender: 1 } } } }
                ]
            }
        }
    ]
},
{
    id: 140,
    free: [
        { rewardType: 'clothes', destroyXP: 5000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 5, drawable: 2053, textures: 3, isProp: 0 }, 1: { gender: 1, component: 1, drawable: 2017, texture: 0, isProp: 1 } } } },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'revuelto' }, destroyXP: 9000, color: 'gold' },
    ]
},
{
    id: 141,
    free: [
        { rewardType: 'item', rewardData: { itemId: 757, count: 0 }, destroyXP: 3000, color: 'gold' }, // Ролики
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 133, amount: 2 }, destroyXP: 2000, color: 'gold' },
        { rewardType: 'wheels', rewardData: { id: 168, amount: 2 }, destroyXP: 2000, color: 'gold' },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 25 }, destroyXP: 1750, color: 'gold' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 25 }, destroyXP: 1750, color: 'gold' },
    ]
},
{
    id: 142,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 91, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 126, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 161, amount: 2 },  },
			]
		}, destroyXP: 2000, color: 'gold' },
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 143,
    free: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2076, textures: 21, isProp: 0 }, // Кобура на 2 ноги, золотая
                    1: { gender: 1, component: 7, drawable: 2066, textures: 21, isProp: 0 }, // Кобура на 2 ноги, золотая
                }
            }
        }
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 105, amount: 2 }, destroyXP: 2000, color: 'gold' },
    ]
},
{
    id: 144,
    free: [
        { rewardType: 'wheels', rewardData: { id: 140, amount: 2 }, destroyXP: 2000, color: 'gold' },
        { rewardType: 'wheels', rewardData: { id: 175, amount: 2 }, destroyXP: 2000, color: 'gold' },
        { rewardType: 'money', rewardData: { amount: 300000 }, destroyXP: 3000, color: 'gold' },
        { rewardType: 'vip', rewardData: { type: 'Platinum', days: 90 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'rrphantom2', setId: 4 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'rrphantom2', setId: 5 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'rrphantom2', setId: 6 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'rrphantom2', setId: 7 }, },
            ]
		}, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 145,
    free: [
        { rewardType: 'clothes', destroyXP: 3000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2174, textures: 8, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2081, texture: 0, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 5000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2353, textures: 8, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2183, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 146,
    free: [
        { color: 'gold', destroyXP: 3000, rewardData: { id: 215 }, rewardType: 'animation' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'sclass4', setId: 14 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'sclass4', setId: 15 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'sclass4', setId: 16 }, },
            ]
		}, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 147,
    free: [
        { rewardType: 'item', rewardData: { itemId: 724, count: 0 }, destroyXP: 5000, color: 'gold' }, // Улучшенный металлоискатель
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 756, count: 0 }, destroyXP: 3000, color: 'gold' }, // Скейтборд
    ]
},
{
    id: 148,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 84, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 119, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 154, amount: 2 },  },
			]
		}, destroyXP: 2000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 147, amount: 2 }, destroyXP: 2000, color: 'gold' },
        { rewardType: 'wheels', rewardData: { id: 182, amount: 2 }, destroyXP: 2000, color: 'gold' },
        { rewardType: 'wheels', rewardData: { id: 112, amount: 2 }, destroyXP: 2000, color: 'gold' },
    ]
},
{
    id: 149,
    free: [
        { rewardType: 'clothes', destroyXP: 5000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2016, texture: 0, isProp: 1 }, 1: { gender: 1, component: 6, drawable: 2086, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 5000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2360, texture: 0, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2189, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 150,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'mistral' }, destroyXP: 10000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'ec120' }, destroyXP: 10000, color: 'gold' },
    ]
},


{
    id: 151,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 152,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 153,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 154,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 155,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 156,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 157,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 158,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 159,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 160,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 161,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 162,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 163,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 164,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 165,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 166,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 167,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 168,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 169,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 170,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 171,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 172,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 173,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 174,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 175,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 176,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 177,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 178,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 179,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 180,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 181,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 182,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 183,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 184,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 185,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 186,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 187,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 188,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 189,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 190,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 191,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 192,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 193,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 194,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 195,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 196,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 197,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 198,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 199,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
},
{
    id: 200,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 500, color: 'blue' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 1000, color: 'red' }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Лето 2023
    ]
}
]

export const seasonPassXP = {
    // Формула XP_START+(currentLevel*XP_STEP)
    XP_START: 300, // 1й уровень начинается с этого XP
    XP_STEP: 220, // Шаг на каждый уровень. (XP нужен для текущего уровня)+XP_STEP = следующий уровень
}

export const seasonPassgetXPForNextLevel = (currentLevel) => {
    if (currentLevel < 100) {
        return seasonPassXP.XP_START + (currentLevel * seasonPassXP.XP_STEP);
    }

    if (currentLevel >= 100 && currentLevel < 150) {
        return 10000;
    }

    return 20000;
};
