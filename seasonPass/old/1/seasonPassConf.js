export const seasonPassConfig = {
    SEASON_ID: 1,

    MAX_TASKS_PER_PLAYER: 9, // ДОЛЖНО ДЕЛИТЬСЯ НА 3
    RESET_TASKS_HOUR: 4,
    ACTIVE_DATE_RANGE: {from: '2022-05-24', to: '2022-08-15'},
    ACTIVE_CLAIM_DATE_RANGE: {from: '2022-05-24', to: '2022-09-01'},

    XP_PER_MC_EU: 4, // Сколько XP выдавать за 1 MCoin (EU)
    PRICE_EU_MULTIPLIER: 0.5,

    XP_PER_MC: 2, // Сколько XP выдавать за 1 MCoin
    XP_PER_PAYDAY: 150, // Сколько XP выдавать за 1 PayDay

    PRICE: {
        free: 0,
        premium: 5000,
        king: 30000,
    },

    SHOP_XP: [
        { buyId: 1, type: 'status', price: 5000 }, // Цена генерируется из PREMIUM_PRICE
        { buyId: 2, type: 'experience', price: 50, bonus: 0 }, // Цена генерируется из EXP и XP_PER_MC
        { buyId: 3, type: 'experience', price: 500, bonus: 10 }, // Бонус задается вручную
        { buyId: 4, type: 'experience', price: 2500, bonus: 15 }, // Бонус задается вручную
        { buyId: 5, type: 'experience', price: 10000, bonus: 25 }, // Бонус задается вручную
        { buyId: 6, type: 'experience', price: 30000, bonus: 50 }, // Бонус задается вручную
    ]
}

/**
 * Доступные параметры на задания.
 * @param  {Number}     taskId                          Идентификатор задания. Должно быть уникальным
 * @param  {String}     title                           Название задания i18n
 * @param  {String}     description                     Описание задания i18n
 * @param  {String}     difficulty                      Сложность задания
 * @param  {String}     trigger_type                    Тип триггера. Он задается в коде. Например, 'level_up'
 * @param  {Array}      trigger_value                   Доп триггер. Он задается в коде. Например ID купленного предмета
 * @param  {String}     trigger_amount                  Количество на триггер
 * @param  {String}     xp_reward                       Кол-во опыта за задание
 * @param  {Object}     dateRange                       Время действия задания. Объект с полями:
 * @param  {String}     dateRange.from                  Дата начала действия задания в формате 'DD-MM' или 'DD-MM-YY' или 'DD-MM-n (ЕСЛИ УСТАНОВЛЕН -N в качестве года, ЗНАЧИТ ОН ИСПОЛЬЗУЕТ СЛЕДУЮЩИЙ ГОД от текущего)
 * @param  {Number}     dateRange.to                    Дата окончания действия задания в формате 'DD-MM' или 'DD-MM-YY'
 */

export const seasonPassTasks = [
    {
        // Поздороваться с игроками
        taskId: 1,
        difficulty: 'easy',
        title: 'seasonPass.tasks.greetPlayers.title',
        description: 'seasonPass.tasks.greetPlayers.description',
        xp_reward: 330,
        trigger_type: 'greet_player',
        trigger_amount: 50
    },
    {
        // Принести подарки Эве
        taskId: 2,
        difficulty: 'medium',
        title: 'seasonPass.tasks.bringGifts.title',
        description: 'seasonPass.tasks.bringGifts.description',
        xp_reward: 1000,
        trigger_type: 'xmas_gift',
        trigger_amount: 50,
        dateRanges: [
            {from: '21-12-ce 00:00', to: '21-12-ce 23:59'},
            {from: '01-01-ce 00:00', to: '15-01-ce 23:59'},
        ]
    },
    {
        // Съесть бургеры
        taskId: 3,
        difficulty: 'easy',
        title: 'seasonPass.tasks.eatBurgers.title',
        description: 'seasonPass.tasks.eatBurgers.description',
        xp_reward: 500,
        trigger_type: 'task_eat_item',
        trigger_value: [136],
        trigger_amount: 25,
    },
    {
        // Восстановить хп аптечкой
        taskId: 4,
        difficulty: 'easy',
        title: 'seasonPass.tasks.userMedkits.title',
        description: 'seasonPass.tasks.userMedkits.description',
        xp_reward: 500,
        trigger_type: 'task_medkit_item',
        trigger_value: [78, 530, 240],
        trigger_amount: 20,
    },
    {
        // Установить фейерверк
        taskId: 5,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useFireworks.title',
        description: 'seasonPass.tasks.useFireworks.description',
        xp_reward: 500,
        trigger_type: 'task_use_fireworks',
        trigger_value: [394, 395, 396, 397],
        trigger_amount: 5,
    },
    {
        // Выкурить сигареты
        taskId: 6,
        difficulty: 'easy',
        title: 'seasonPass.tasks.smokeSomething.title',
        description: 'seasonPass.tasks.smokeSomething.description',
        xp_reward: 250,
        trigger_type: 'task_use_cig',
        trigger_amount: 10,
    },
    {
        // Заправить автомобиль
        taskId: 7,
        difficulty: 'easy',
        title: 'seasonPass.tasks.fuelVehicle.title',
        description: 'seasonPass.tasks.fuelVehicle.description',
        xp_reward: 500,
        trigger_type: 'buy_item',
        trigger_value: ['regular', 'plus', 'premium', 'diesel', 'electro'],
        trigger_amount: 50,
    },
    {
        // Купить канистру
        taskId: 8,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyJerrycan.title',
        description: 'seasonPass.tasks.buyJerrycan.description',
        xp_reward: 500,
        trigger_type: 'buy_item',
        trigger_value: [77],
        trigger_amount: 5,
    },
    {
        // Использовать эмоциональное на игроке
        taskId: 9,
        difficulty: 'easy',
        title: 'seasonPass.tasks.emotionToPlayer.title',
        description: 'seasonPass.tasks.emotionToPlayer.description',
        xp_reward: 500,
        trigger_type: 'show_emotional',
        trigger_amount: 10,
    },
    {
        // Обменяться предметами с игроком
        taskId: 10,
        difficulty: 'easy',
        title: 'seasonPass.tasks.tradeItems.title',
        description: 'seasonPass.tasks.tradeItems.description',
        xp_reward: 330,
        trigger_type: 'trade_item',
        trigger_amount: 5,
    },
    {
        // Выйграть в рулетке казино
        taskId: 11,
        difficulty: 'medium',
        title: 'seasonPass.tasks.casinoRoulette.title',
        description: 'seasonPass.tasks.casinoRoulette.description',
        xp_reward: 1000,
        trigger_type: 'win_casino_roulette',
        trigger_amount: 40,
    },
    {
        // Выйграть в блекджек казино
        taskId: 12,
        difficulty: 'medium',
        title: 'seasonPass.tasks.casinoBlackjack.title',
        description: 'seasonPass.tasks.casinoBlackjack.description',
        xp_reward: 1000,
        trigger_type: 'win_casino_blackjack',
        trigger_amount: 15,
    },
    {
        // Выйграть в скачках казино
        taskId: 13,
        difficulty: 'hard',
        title: 'seasonPass.tasks.casinoHorserace.title',
        description: 'seasonPass.tasks.casinoHorserace.description',
        xp_reward: 3000,
        trigger_type: 'win_casino_horserace',
        trigger_amount: 5,
    },
    {
        // Выйграть в слот машинах казино
        taskId: 14,
        difficulty: 'medium',
        title: 'seasonPass.tasks.casinoSlots.title',
        description: 'seasonPass.tasks.casinoSlots.description',
        xp_reward: 1500,
        trigger_type: 'win_casino_slots',
        trigger_amount: 10,
    },
    {
        // Собрать клад
        taskId: 15,
        difficulty: 'medium',
        title: 'seasonPass.tasks.metalDetectorDig.title',
        description: 'seasonPass.tasks.metalDetectorDig.description',
        xp_reward: 1500,
        trigger_type: 'metal_detector_dig',
        trigger_amount: 4,
    },
    {
        // Выполнить рейсы на дальнобойщике
        taskId: 16,
        difficulty: 'medium',
        title: 'seasonPass.tasks.jobTrucker.title',
        description: 'seasonPass.tasks.jobTrucker.description',
        xp_reward: 1000,
        trigger_type: 'job_trucker',
        trigger_amount: 20,
    },
    {
        // Собрать грибы в лесу
        taskId: 17,
        difficulty: 'hard',
        title: 'seasonPass.tasks.jobMushroomer.title',
        description: 'seasonPass.tasks.jobMushroomer.description',
        xp_reward: 3000,
        trigger_type: 'job_mushroomer',
        trigger_amount: 100,
    },
    {
        // Выполнить рейсы на инкассаторе
        taskId: 18,
        difficulty: 'hard',
        title: 'seasonPass.tasks.jobMoneycollector.title',
        description: 'seasonPass.tasks.jobMoneycollector.description',
        xp_reward: 3000,
        trigger_type: 'job_moneycollector',
        trigger_amount: 15,
    },
    {
        // Выловить рыбу несколько раз
        taskId: 19,
        difficulty: 'medium',
        title: 'seasonPass.tasks.jobFishing.title',
        description: 'seasonPass.tasks.jobFishing.description',
        xp_reward: 1500,
        trigger_type: 'job_fisherman',
        trigger_amount: 60,
    },
    {
        // Собрать брёвна в лесу
        taskId: 20,
        difficulty: 'medium',
        title: 'seasonPass.tasks.jobLumberjack.title',
        description: 'seasonPass.tasks.jobLumberjack.description',
        xp_reward: 1000,
        trigger_type: 'job_lumberjack',
        trigger_amount: 50,
    },
    {
        // Выполнить рейсы на почтальоне
        taskId: 21,
        difficulty: 'easy',
        title: 'seasonPass.tasks.jobGopostal.title',
        description: 'seasonPass.tasks.jobGopostal.description',
        xp_reward: 500,
        trigger_type: 'job_gopostal',
        trigger_amount: 15,
    },
    {
        // Выполнить рейсы на мусорщике
        taskId: 22,
        difficulty: 'medium',
        title: 'seasonPass.tasks.jobGargagecollector.title',
        description: 'seasonPass.tasks.jobGargagecollector.description',
        xp_reward: 1000,
        trigger_type: 'job_garbagecollector',
        trigger_amount: 100,
    },
    {
        // Кинуть снежки в игроков
        taskId: 23,
        difficulty: 'easy',
        title: 'seasonPass.tasks.throwSnows.title',
        description: 'seasonPass.tasks.throwSnows.description',
        xp_reward: 500,
        trigger_type: 'throw_snowball',
        trigger_amount: 20,
        dateRanges: [
            {from: '01-12-ce 00:00', to: '21-12-ce 23:59'},
            {from: '01-01-ce 00:00', to: '15-01-ce 23:59'},
        ]
    },
    {
        // Выпить алкоголь несколько раз
        taskId: 24,
        difficulty: 'easy',
        title: 'seasonPass.tasks.drinkAlcohol.title',
        description: 'seasonPass.tasks.drinkAlcohol.description',
        xp_reward: 330,
        trigger_type: 'task_drink_alcohol',
        trigger_amount: 15,
    },
    {
        // Сыграть в GunGame
        taskId: 25,
        difficulty: 'medium',
        title: 'seasonPass.tasks.playMinigames.title',
        description: 'seasonPass.tasks.playMinigames.description',
        xp_reward: 1000,
        trigger_type: 'play_minigames',
        trigger_amount: 5,
    },
    {
        // Использовать качалку
        taskId: 26,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useGym.title',
        description: 'seasonPass.tasks.useGym.description',
        xp_reward: 250,
        trigger_type: 'use_gym',
        trigger_amount: 10,
    },
    {
        // Купить что-то на рынке
        taskId: 27,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyInMarket.title',
        description: 'seasonPass.tasks.buyInMarket.description',
        xp_reward: 330,
        trigger_type: 'market_buy',
        trigger_amount: 10,
    },
    {
        // Продать что-то на рынке
        taskId: 28,
        difficulty: 'easy',
        title: 'seasonPass.tasks.sellInMarket.title',
        description: 'seasonPass.tasks.sellInMarket.description',
        xp_reward: 250,
        trigger_type: 'market_sell',
        trigger_amount: 10,
    },
    {
        // Арендовать автомобиль
        taskId: 29,
        difficulty: 'easy',
        title: 'seasonPass.tasks.rentVehicle.title',
        description: 'seasonPass.tasks.rentVehicle.description',
        xp_reward: 250,
        trigger_type: 'rent_vehicle',
        trigger_amount: 1,
    },
    {
        // Вызвать автомобиль в автосервисе
        taskId: 30,
        difficulty: 'easy',
        title: 'seasonPass.tasks.carService.title',
        description: 'seasonPass.tasks.carService.description',
        xp_reward: 250,
        trigger_type: 'fixcar_use',
        trigger_amount: 2,
    },
    {
        // Набить тату
        taskId: 31,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useTattoo.title',
        description: 'seasonPass.tasks.useTattoo.description',
        xp_reward: 330,
        trigger_type: 'tattoo_apply',
        trigger_amount: 1,
    },
    {
        // Позвонить по телефону
        taskId: 32,
        difficulty: 'easy',
        title: 'seasonPass.tasks.callUsingPhone.title',
        description: 'seasonPass.tasks.callUsingPhone.description',
        xp_reward: 250,
        trigger_type: 'phone_call',
        trigger_amount: 3,
    },
    {
        // Написать СМС
        taskId: 33,
        difficulty: 'easy',
        title: 'seasonPass.tasks.smsUsingPhone.title',
        description: 'seasonPass.tasks.smsUsingPhone.description',
        xp_reward: 250,
        trigger_type: 'phone_sms',
        trigger_amount: 5,
    },
    {
        // Кинотеатр
        taskId: 34,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useCinema.title',
        description: 'seasonPass.tasks.useCinema.description',
        xp_reward: 330,
        trigger_type: 'cinema_set_video',
        trigger_amount: 3,
    },
    {
        // Использовать колесо фортуны в казино
        taskId: 35,
        difficulty: 'easy',
        title: 'seasonPass.tasks.casinoLuckyWheel.title',
        description: 'seasonPass.tasks.casinoLuckyWheel.description',
        xp_reward: 500,
        trigger_type: 'casino_lucky_wheel',
        trigger_amount: 1,
    },
    {
        // Использовать автомойку
        taskId: 36,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useCarWash.title',
        description: 'seasonPass.tasks.useCarWash.description',
        xp_reward: 250,
        trigger_type: 'use_carwash',
        trigger_amount: 1,
    },
    {
        // Выиграть в кости
        taskId: 37,
        difficulty: 'easy',
        title: 'seasonPass.tasks.winDice.title',
        description: 'seasonPass.tasks.winDice.description',
        xp_reward: 500,
        trigger_type: 'dice_win',
        trigger_value: ['win'],
        trigger_amount: 20,
    },
    {
        // Купить что-то в барбершопе
        taskId: 38,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyInBarbershop.title',
        description: 'seasonPass.tasks.buyInBarbershop.description',
        xp_reward: 250,
        trigger_type: 'barbershop_buy',
        trigger_amount: 1,
    },
    {
        // Использовать гриль
        taskId: 39,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useBBQ.title',
        description: 'seasonPass.tasks.useBBQ.description',
        xp_reward: 500,
        trigger_type: 'bbq_use',
        trigger_amount: 3,
    },
    {
        // Заработать на вызовах в такси
        taskId: 40,
        difficulty: 'medium',
        title: 'seasonPass.tasks.workInTaxi.title',
        description: 'seasonPass.tasks.workInTaxi.description',
        xp_reward: 1000,
        trigger_type: 'taxi_work',
        trigger_amount: 50,
    },
    {
        // Собрать любой урожай на ферме
        taskId: 41,
        difficulty: 'medium',
        title: 'seasonPass.tasks.harvestFarmer.title',
        description: 'seasonPass.tasks.harvestFarmer.description',
        xp_reward: 1500,
        trigger_type: 'farm_harvest',
        trigger_amount: 200,
    },
    {
        // Заказать приватный танец в стрип клубе
        taskId: 42,
        difficulty: 'easy',
        title: 'seasonPass.tasks.stripDance.title',
        description: 'seasonPass.tasks.stripDance.description',
        xp_reward: 250,
        trigger_type: 'private_dance_buy',
        trigger_amount: 1,
    },
    {
        // Починить авто на СТО
        taskId: 43,
        difficulty: 'hard',
        title: 'seasonPass.tasks.repairVehicle.title',
        description: 'seasonPass.tasks.repairVehicle.description',
        xp_reward: 3000,
        trigger_type: 'repair_vehicle',
        trigger_amount: 3,
    },
    {
        // Сделать ставку в букмекере
        taskId: 44,
        difficulty: 'easy',
        title: 'seasonPass.tasks.bookmakerBet.title',
        description: 'seasonPass.tasks.bookmakerBet.description',
        xp_reward: 500,
        trigger_type: 'bookmaker_bet',
        trigger_amount: 50000,
    },
    {
        // Собрать дрифт очки
        taskId: 45,
        difficulty: 'hard',
        title: 'seasonPass.tasks.driftPoints.title',
        description: 'seasonPass.tasks.driftPoints.description',
        xp_reward: 3000,
        trigger_type: 'drift_points',
        trigger_amount: 12500,
    },
    {
        // Взять счастливый телефон
        taskId: 46,
        difficulty: 'medium',
        title: 'seasonPass.tasks.luckyPhone.title',
        description: 'seasonPass.tasks.luckyPhone.description',
        xp_reward: 1500,
        trigger_type: 'lucky_phone_pick',
        trigger_amount: 1,
    },
    {
        // Затюнинговать авто
        taskId: 47,
        difficulty: 'medium',
        title: 'seasonPass.tasks.tuningVehicle.title',
        description: 'seasonPass.tasks.tuningVehicle.description',
        xp_reward: 1000,
        trigger_type: 'tuning_vehicle',
        trigger_amount: 4,
    },
    {
        // Поставить бумбокс на землю
        taskId: 48,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useBoombox.title',
        description: 'seasonPass.tasks.useBoombox.description',
        xp_reward: 250,
        trigger_type: 'boombox_use',
        trigger_amount: 1,
    },
    {
        // Взломать зажигание отвёрткой у чужого авто
        taskId: 49,
        difficulty: 'hard',
        title: 'seasonPass.tasks.hijackVehicle.title',
        description: 'seasonPass.tasks.hijackVehicle.description',
        xp_reward: 3000,
        trigger_type: 'hijack_vehicle_screwdriver',
        trigger_amount: 3,
    },
    {
        // Порубить мясо
        taskId: 50,
        difficulty: 'medium',
        title: 'seasonPass.tasks.butcher.title',
        description: 'seasonPass.tasks.butcher.description',
        xp_reward: 1000,
        trigger_type: 'job_butcher',
        trigger_amount: 100,
    },
    {
        // Собрать сырье на шахте
        taskId: 51,
        difficulty: 'medium',
        title: 'seasonPass.tasks.miner.title',
        description: 'seasonPass.tasks.miner.description',
        xp_reward: 1000,
        trigger_type: 'job_mining',
        trigger_amount: 50,
    },
    {
        // Раскрыть парашют
        taskId: 52,
        difficulty: 'hard',
        title: 'seasonPass.tasks.parachuteOpen.title',
        description: 'seasonPass.tasks.parachuteOpen.description',
        xp_reward: 3000,
        trigger_type: 'parachute_open',
        trigger_amount: 10,
    },
    {
        // Сломать оружие
        taskId: 53,
        difficulty: 'hard',
        title: 'seasonPass.tasks.breakWeapon.title',
        description: 'seasonPass.tasks.breakWeapon.description',
        xp_reward: 3000,
        trigger_type: 'weapon_break',
        trigger_amount: 1,
    },
    {
        // Сыграть в армреслинг
        taskId: 54,
        difficulty: 'easy',
        title: 'seasonPass.tasks.armWrestle.title',
        description: 'seasonPass.tasks.armWrestle.description',
        xp_reward: 500,
        trigger_type: 'arm_wrestle_play',
        trigger_amount: 3,
    },
    {
        // Купить отвёртку
        taskId: 55,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyScrewdriver.title',
        description: 'seasonPass.tasks.buyScrewdriver.description',
        xp_reward: 330,
        trigger_type: 'buy_item',
        trigger_value: [8],
        trigger_amount: 5,
    },
    {
        // Купить лотерейный билет
        taskId: 56,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyLottery.title',
        description: 'seasonPass.tasks.buyLottery.description',
        xp_reward: 250,
        trigger_type: 'buy_item',
        trigger_value: [317],
        trigger_amount: 1,
    },
    {
        // Съесть шаурму без отравления
        taskId: 57,
        difficulty: 'easy',
        title: 'seasonPass.tasks.eatShawarma.title',
        description: 'seasonPass.tasks.eatShawarma.description',
        xp_reward: 500,
        trigger_type: 'task_eat_item',
        trigger_value: [342],
        trigger_amount: 3,
    },
    {
        // Толкнуть машину
        taskId: 58,
        difficulty: 'easy',
        title: 'seasonPass.tasks.pushCar.title',
        description: 'seasonPass.tasks.pushCar.description',
        xp_reward: 500,
        trigger_type: 'push_car',
        trigger_amount: 10,
    },
    {
        // Свести тату с тела
        taskId: 59,
        difficulty: 'easy',
        title: 'seasonPass.tasks.removeTattoo.title',
        description: 'seasonPass.tasks.removeTattoo.description',
        xp_reward: 500,
        trigger_type: 'remove_tattoo',
        trigger_amount: 3,
    },
    {
        // Заменить масло
        taskId: 60,
        difficulty: 'easy',
        title: 'seasonPass.tasks.changeOil.title',
        description: 'seasonPass.tasks.changeOil.description',
        xp_reward: 500,
        trigger_type: 'change_oil',
        trigger_amount: 1,
    },
    {
        // Заменить аккумулятор
        taskId: 61,
        difficulty: 'easy',
        title: 'seasonPass.tasks.changeBattery.title',
        description: 'seasonPass.tasks.changeBattery.description',
        xp_reward: 500,
        trigger_type: 'change_battery',
        trigger_amount: 1,
    },
    {
        // Взять на руки девушек
        taskId: 62,
        difficulty: 'easy',
        title: 'seasonPass.tasks.carryFemales.title',
        description: 'seasonPass.tasks.carryFemales.description',
        xp_reward: 500,
        trigger_type: 'carry_female',
        trigger_amount: 5,
    },
    {
        // Взять на руки парней
        taskId: 63,
        difficulty: 'easy',
        title: 'seasonPass.tasks.carryMales.title',
        description: 'seasonPass.tasks.carryMales.description',
        xp_reward: 500,
        trigger_type: 'carry_male',
        trigger_amount: 5,
    },
    {
        // Пополнить счёт телефона
        taskId: 64,
        difficulty: 'easy',
        title: 'seasonPass.tasks.topUpPhone.title',
        description: 'seasonPass.tasks.topUpPhone.description',
        xp_reward: 250,
        trigger_type: 'topup_phone',
        trigger_amount: 500,
    },
    {
        // Вылечиться у NPC-персонажа в больнице
        taskId: 65,
        difficulty: 'easy',
        title: 'seasonPass.tasks.healInHospital.title',
        description: 'seasonPass.tasks.healInHospital.description',
        xp_reward: 500,
        trigger_type: 'heal_in_hospital',
        trigger_amount: 2,
    },
    {
        // Вакцинироваться в больнице у доктора
        taskId: 66,
        difficulty: 'hard',
        title: 'seasonPass.tasks.vaccinate.title',
        description: 'seasonPass.tasks.vaccinate.description',
        xp_reward: 3000,
        trigger_type: 'vaccinate',
        trigger_amount: 1,
    },
    {
        // Покопаться в мусорке
        taskId: 67,
        difficulty: 'medium',
        title: 'seasonPass.tasks.trashSearch.title',
        description: 'seasonPass.tasks.trashSearch.description',
        xp_reward: 1000,
        trigger_type: 'search_trash',
        trigger_amount: 20,
    },
    {
        // Припарковать арендованный автомобиль
        taskId: 68,
        difficulty: 'easy',
        title: 'seasonPass.tasks.parkRentVehicle.title',
        description: 'seasonPass.tasks.parkRentVehicle.description',
        xp_reward: 330,
        trigger_type: 'park_rented_vehicle',
        trigger_amount: 1,
    },
    {
        // Воспользоваться торговыми аппаратами
        taskId: 69,
        difficulty: 'easy',
        title: 'seasonPass.tasks.vendingMachine.title',
        description: 'seasonPass.tasks.vendingMachine.description',
        xp_reward: 500,
        trigger_type: 'vending_machine_use',
        trigger_amount: 10,
    },
    {
        // Прокатиться на колесе обозрения
        taskId: 70,
        difficulty: 'easy',
        title: 'seasonPass.tasks.ferrisWheelRide.title',
        description: 'seasonPass.tasks.ferrisWheelRide.description',
        xp_reward: 500,
        trigger_type: 'ferris_wheel_ride',
        trigger_amount: 4,
    },
    {
        // Прокатиться на американских горках
        taskId: 71,
        difficulty: 'easy',
        title: 'seasonPass.tasks.rollerCoasterRide.title',
        description: 'seasonPass.tasks.rollerCoasterRide.description',
        xp_reward: 500,
        trigger_type: 'roller_coaster_ride',
        trigger_amount: 4,
    },
    // {
    //     // Убить животных и собрать их мясо
    //     taskId: 72,
    //     difficulty: 'medium',
    //     title: 'seasonPass.tasks.huntAnimals.title',
    //     description: 'seasonPass.tasks.huntAnimals.description',
    //     xp_reward: 1500,
    //     trigger_type: 'hunting_animals',
    //     trigger_amount: 10,
    // },
    // {
    //     // Погладить любых животных
    //     taskId: 73,
    //     difficulty: 'medium',
    //     title: 'seasonPass.tasks.petAnimals.title',
    //     description: 'seasonPass.tasks.petAnimals.description',
    //     xp_reward: 1000,
    //     trigger_type: 'pet_animals',
    //     trigger_amount: 10,
    // },
    {
        // Купить кальян
        taskId: 72,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyHookah.title',
        description: 'seasonPass.tasks.buyHookah.description',
        xp_reward: 750,
        trigger_type: 'buy_item',
        trigger_value: [336],
        trigger_amount: 10,
    },
    {
        // Купить вейп
        taskId: 73,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyVape.title',
        description: 'seasonPass.tasks.buyVape.description',
        xp_reward: 750,
        trigger_type: 'buy_item',
        trigger_value: [335],
        trigger_amount: 10,
    },
    {
        // Купить гитару
        taskId: 74,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyGuitar.title',
        description: 'seasonPass.tasks.buyGuitar.description',
        xp_reward: 250,
        trigger_type: 'buy_item',
        trigger_value: [139],
        trigger_amount: 2,
    },
    {
        // Купить что-то в магазине одежды
        taskId: 75,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyInClothesShop.title',
        description: 'seasonPass.tasks.buyInClothesShop.description',
        xp_reward: 330,
        trigger_type: 'clothesshop_buy',
        trigger_amount: 3,
    },
];

export const seasonPassTasksMap = new Map();

for (const taskData of seasonPassTasks) {
  if (!seasonPassTasksMap.has(taskData.trigger_type)) {
    seasonPassTasksMap.set(taskData.trigger_type, []);
  }

  seasonPassTasksMap.get(taskData.trigger_type).push(taskData);

  if (Array.isArray(taskData.trigger_value)) {
    for (const value of taskData.trigger_value) {
      const key = `${taskData.trigger_type}_${value}`;

      if (!seasonPassTasksMap.has(key)) {
        seasonPassTasksMap.set(key, []);
      }

      seasonPassTasksMap.get(key).push(taskData);
    }
  }
}
