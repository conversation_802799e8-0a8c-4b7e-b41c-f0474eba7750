/**
 * Доступные параметры на награду.
 * @param  {String}     rewardType                      Тип награды. ('item', 'vehicle' 'money', 'clothes', 'coins', 'vip', 'hidden')
 * @param  {Object}     rewardData                      Данные награды. Они могут быть разными в зависимости от типа награды.
 * @param  {Number}     destroyXP                       Кол-во XP получаемого при распылении награды.
 * @param  {String}     color                           Цвет награды. ('gray', 'blue', 'purple', 'red', 'gold')
 */
// SEASON PASS РАССЧИТАН на 100 УРОВНЕЙ. НЕ МЕНЬШЕ НЕ БОЛЬШЕ

export const seasonPassLevels = [{
    id: 1,
    free: [
        { rewardType: 'money', rewardData: { amount: 2500 }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 5000 }, destroyXP: 250, color: 'gray' },
        { rewardType: 'coins', rewardData: { amount: 100 }, destroyXP: 100, color: 'blue' },
    ]
},
{
    id: 2,
    free: [
        { rewardType: 'item', rewardData: { itemId: 255, count: 0 }, destroyXP: 250, color: 'gray' }, // Бронебойный пистолет
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 610, count: 0 }, destroyXP: 250, color: 'gray' }, // Жареное мясо курицы
    ]
},
{
    id: 3,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 10000 }, destroyXP: 250, color: 'gray' },
    ]
},
{
    id: 4,
    free: [
        // { rewardType: 'item', rewardData: { itemId: 471, count: 0 }, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'akuma' }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 5,
    free: [
        {
            rewardType: 'clothes', destroyXP: 250, color: 'gray',
            rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2244, texture: 6, isProp: 0, gender: 0 },
                    1: { component: 11, drawable: 2256, texture: 0, isProp: 0, gender: 1 }
                }
            }
        }
    ],
    premium: [

    ]
},
{
    id: 6,
    free: [
        { rewardType: 'item', rewardData: { itemId: 255, count: 0 }, destroyXP: 250, color: 'gray' }, // Бронебойный пистолет
        { rewardType: 'item', rewardData: { itemId: 262, count: 0 }, destroyXP: 250, color: 'gray' }, // Пистолет Marksman
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2245, texture: 2, isProp: 0 },
                    1: { gender: 1, component: 4, drawable: 2151, texture: 0, isProp: 0 }
                }
            }
        }
    ],
},
{
    id: 7,
    free: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_microsmg',
                skinId: 0
            },
            destroyXP: 500,
            color: 'blue'
        }, // Скин случайный
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 334, count: 20 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
    ],
},
{
    id: 8,
    free: [
        { rewardType: 'money', rewardData: { amount: 5000 }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 0
            },
            destroyXP: 250,
            color: 'gray'
        },
    ],
},
{
    id: 9,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [],
},
{
    id: 10,
    free: [],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2246, texture: 17, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2259, texture: 3, isProp: 0 }
                }
            }
        }
    ]
},
{
    id: 11,
    free: [
        // Татуировка случайная 15
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3107,
                    [1]: 6082,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
    premium: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
},
{
    id: 12,
    free: [
        { rewardType: 'item', rewardData: { itemId: 532, count: 1 }, destroyXP: 250, color: 'gray' },
    ],
    premium: [],
},
{
    id: 13,
    free: [
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'bmx' }, destroyXP: 250, color: 'gray' },
    ],
},
{
    id: 14,
    free: [
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
    ],
    premium: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 19
            },
            destroyXP: 500,
            color: 'blue'
        },
    ],
},
{
    id: 15,
    free: [
        {
            rewardType: 'clothes', destroyXP: 250, color: 'gray',
            rewardData: {
                clothesData: {
                    0: { component: 4, drawable: 2143, texture: 12, isProp: 0, gender: 0 },
                    1: { component: 7, drawable: 2061, texture: 0, isProp: 0, gender: 1 }
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 1, drawable: 2010, texture: 0, isProp: 1 },
                    1: { gender: 1, component: 1, drawable: 2010, texture: 1, isProp: 1 }
                }
            }
        }
    ]
},
{
    id: 16,
    free: [
        {
            color: 'blue',
            destroyXP: 500,
            rewardData: {
                id: 29,
                title: 'animations.list.LeaveTheDoorOpen',
                animData: [
                    'majestic_animations_2',
                    'leave_door_open'
                ],
                flag: 1,
                looped: true,
                music: 'leave_door_open'
            },
            rewardType: 'animation',
        }
    ],
    premium: [],
},
{
    id: 17,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'vip', rewardData: { type: 'Silver', days: 10 }, destroyXP: 500, color: 'blue' },
    ],
},
{
    id: 18,
    free: [
        { rewardType: 'money', rewardData: { amount: 5500 }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
    ],
},
{
    id: 19,
    free: [
        // Татуировка случайная 14
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3106,
                    [1]: 6083,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 443, count: 0 }, destroyXP: 750, color: 'purple' }, // Дрель 1500w
    ],
},
{
    id: 20,
    free: [
        { rewardType: 'item', rewardData: { itemId: 337, count: 0 }, destroyXP: 750, color: 'purple' }, // Винтовка Marksman Mk2
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 6, drawable: 2058, texture: 2, isProp: 0 },
                    1: { gender: 1, component: 6, drawable: 2062, texture: 2, isProp: 0 }
                }
            }
        }
    ]
},
{
    id: 21,
    free: [
        { rewardType: 'item', rewardData: { itemId: 431, count: 0 }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_smg_mk2',
                skinId: 0
            },
            destroyXP: 250,
            color: 'gray'
        }, // Скин случайный
    ],
},
{
    id: 22,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        // Татуировка случайная 13
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3103,
                    [1]: 6124,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3086,
                    [1]: 6084,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3104,
                    [1]: 6085,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3105,
                    [1]: 6086,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
},
{
    id: 23,
    free: [
        // Татуировка случайная 12
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3029,
                    [1]: 6087,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3030,
                    [1]: 6088,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3031,
                    [1]: 6089,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3032,
                    [1]: 6090,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
    premium: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
},
{
    id: 24,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { component: 5, drawable: 2017, texture: 0, isProp: 0, gender: 0 },
                    1: { component: 5, drawable: 2017, texture: 0, isProp: 0, gender: 1 }
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2246, texture: 1, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2255, texture: 0, isProp: 0 }
                }
            }
        }
    ]
},
{
    id: 25,
    free: [
        { rewardType: 'wheels', rewardData: { id: 8, amount: 3 }, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 11, amount: 3 }, destroyXP: 750, color: 'red' },
    ],
},
{
    id: 26,
    free: [
        // Татуировка случайная 11
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3100,
                    [1]: 6091,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3101,
                    [1]: 6092,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3079,
                    [1]: 6093,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3080,
                    [1]: 6094,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
    premium: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_revolver',
                skinId: 1
            },
            destroyXP: 250,
            color: 'gray'
        }, // Скин случайный
    ],
},
{
    id: 27,
    free: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 5
            },
            destroyXP: 250,
            color: 'gray'
        },
    ],
    premium: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
},
{
    id: 28,
    free: [
         // Татуировка случайная 10
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3108,
                    [1]: 6099,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3073,
                    [1]: 6100,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3074,
                    [1]: 6101,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3102,
                    [1]: 6102,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
    premium: [
        { rewardType: 'coins', rewardData: { amount: 300 }, destroyXP: 300, color: 'purple' },
    ],
},
{
    id: 29,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 337, count: 0 }, destroyXP: 750, color: 'purple' }, // Винтовка Marksman Mk2
    ],
},
{
    id: 30,
    free: [
        {
            color: 'blue',
            destroyXP: 500,
            rewardData: {
                id: 0,
                title: 'animations.list.BlindingLights',
                animData: [
                    'majestic_animations',
                    'blinding_lights'
                ],
                flag: 0,
                looped: false,
                music: 'blinding_lights'
            },
            rewardType: 'animation',
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2072, texture: 0, isProp: 0 },
                    1: { gender: 1, component: 7, drawable: 2062, texture: 0, isProp: 0 }
                }
            }
        }
    ]
},
{
    id: 31,
    free: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 6
            },
            destroyXP: 250,
            color: 'gray'
        },
    ],
    premium: [
        // Татуировка случайная 9
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3098,
                    [1]: 6103,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3062,
                    [1]: 6104,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3063,
                    [1]: 6105,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3099,
                    [1]: 6106,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
},
{
    id: 32,
    free: [
        { rewardType: 'item', rewardData: { itemId: 130, count: 0 }, destroyXP: 250, color: 'gray' }, // Кинжал
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 750, color: 'purple' },
    ],
},
{
    id: 33,
    free: [
        { rewardType: 'coins', rewardData: { amount: 100 }, destroyXP: 100, color: 'blue' },
    ],
    premium: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_gusenberg',
                skinId: 0
            },
            destroyXP: 500,
            color: 'blue'
        }, // Скин случайный
    ],
},
{
    id: 34,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { component: 0, drawable: 2042, texture: 1, isProp: 1, gender: 0 },
                    1: { component: 0, drawable: 2050, texture: 1, isProp: 1, gender: 1 }
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 1, drawable: 2009, texture: 0, isProp: 1 },
                    1: { gender: 1, component: 1, drawable: 2009, texture: 0, isProp: 1 }
                }
            }
        }
    ]
},
{
    id: 35,
    free: [
        { rewardType: 'wheels', rewardData: { id: 1, amount: 3 }, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 4, amount: 3 }, destroyXP: 750, color: 'red' },
    ],
},
{
    id: 36,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
    ],
},
{
    id: 37,
    free: [
        // Татуировка случайная 8
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3094,
                    [1]: 6108,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3095,
                    [1]: 6109,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3096,
                    [1]: 6107,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3097,
                    [1]: 6110,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
    premium: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
},
{
    id: 38,
    free: [
        { rewardType: 'vip', rewardData: { type: 'Gold', days: 15 }, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 10
            },
            destroyXP: 500,
            color: 'blue'
        },
    ],
},
{
    id: 39,
    free: [],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'f620' }, destroyXP: 500, color: 'blue' },
    ],
},
{
    id: 40,
    free: [
        { rewardType: 'item', rewardData: { itemId: 443, count: 0 }, destroyXP: 750, color: 'purple' }, // Дрель 1500w
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2074, texture: 0, isProp: 0 },
                    1: { gender: 1, component: 7, drawable: 2064, texture: 0, isProp: 0 }
                }
            }
        }
    ]
},
{
    id: 41,
    free: [
         // Татуировка случайная 7
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3035,
                    [1]: 6111,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3038,
                    [1]: 6112,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3093,
                    [1]: 6113,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3047,
                    [1]: 6118,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
    premium: [
        { rewardType: 'vehicleMod', rewardData: { model: 'supragr', setId: 1 }, destroyXP: 3000, color: 'gold' },
    ],
},
{
    id: 42,
    free: [
        { rewardType: 'money', rewardData: { amount: 15000 }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_assaultrifle_mk2',
                skinId: 0
            },
            destroyXP: 500,
            color: 'blue'
        }, // Скин случайный
    ],
},
{
    id: 43,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [],
},
{
    id: 44,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { component: 4, drawable: 2144, texture: 7, isProp: 0, gender: 0 },
                    1: { component: 4, drawable: 2152, texture: 0, isProp: 0, gender: 1 }
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2247, texture: 10, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2257, texture: 0, isProp: 0 }
                }
            }
        }
    ]
},
{
    id: 45,
    free: [
        { rewardType: 'wheels', rewardData: { id: 9, amount: 3 }, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 10, amount: 3 }, destroyXP: 750, color: 'red' },
    ],
},
{
    id: 46,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'WEAPON_HEAVYSHOTGUN',
                skinId: 6
            },
            destroyXP: 750,
            color: 'purple'
        }, // Скин случайный
    ],
},
{
    id: 47,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'daemon2' }, destroyXP: 500, color: 'blue' },
    ],
    premium: [],
},
{
    id: 48,
    free: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 12
            },
            destroyXP: 250,
            color: 'gray'
        },
    ],
    premium: [],
},
{
    id: 49,
    free: [
        { rewardType: 'item', rewardData: { itemId: 132, count: 0 }, destroyXP: 250, color: 'gray' }, // Складной нож
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2145, texture: 6, isProp: 0 },
                    1: { gender: 1, component: 0, drawable: 2051, texture: 0, isProp: 1 }
                }
            }
        }
    ]
},
{
    id: 50,
    free: [
        { rewardType: 'vehicleMod', rewardData: { model: 'i8', setId: 1 }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        {
            color: 'purple',
            destroyXP: 750,
            rewardData: {
                id: 30,
                title: 'animations.list.ChickenWingIt',
                animData: [
                    'majestic_animations_2',
                    'chicken_wing'
                ],
                flag: 1,
                looped: true,
                music: 'chicken_wing'
            },
            rewardType: 'animation',
        }
    ],
},
{
    id: 51,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 573, count: 0 }, destroyXP: 1500, color: 'red' }, // Списанный дефибриллятор
    ],
},
{
    id: 52,
    free: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 1
            },
            destroyXP: 250,
            color: 'gray'
        },
    ],
    premium: [
        // Татуировка случайная 6
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3017,
                    [1]: 6095,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3018,
                    [1]: 6096,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3020,
                    [1]: 6097,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3021,
                    [1]: 6098,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
},
{
    id: 53,
    free: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_carbinerifle',
                skinId: 2
            },
            destroyXP: 750,
            color: 'purple'
        }, // Скин случайный
    ],
    premium: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_carbinerifle',
                skinId: 0
            },
            destroyXP: 500,
            color: 'blue'
        }, // Скин случайный
    ],
},
{
    id: 54,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
            rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2248, texture: 6, isProp: 0, gender: 0 },
                    1: { component: 11, drawable: 2259, texture: 6, isProp: 0, gender: 1 }
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2248, texture: 7, isProp: 0 },
                    1: { gender: 1, component: 7, drawable: 2061, texture: 3, isProp: 0 }
                }
            }
        }
    ]
},
{
    id: 55,
    free: [
        { rewardType: 'wheels', rewardData: { id: 2, amount: 3 }, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 3, amount: 3 }, destroyXP: 750, color: 'red' },
    ]
},
{
    id: 56,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_combatpdw',
                skinId: 1
            },
            destroyXP: 500,
            color: 'blue'
        }, // Скин случайный
    ],
},
{
    id: 57,
    free: [
        { rewardType: 'item', rewardData: { itemId: 573, count: 0 }, destroyXP: 1500, color: 'red' }, // Списанный дефибриллятор
    ],
    premium: [
         // Татуировка случайная 5
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3024,
                    [1]: 6114,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3025,
                    [1]: 6115,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3026,
                    [1]: 6116,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3027,
                    [1]: 6117,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
},
{
    id: 58,
    free: [
        { rewardType: 'money', rewardData: { amount: 15000 }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 50000 }, destroyXP: 1500, color: 'red' },
    ],
},
{
    id: 59,
    free: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'WEAPON_HEAVYSHOTGUN',
                skinId: 8
            },
            destroyXP: 750,
            color: 'purple'
        }, // Скин случайный
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 333, count: 0 }, destroyXP: 750, color: 'purple' }, // Ручной пулемет Мk2
    ],
},
{
    id: 60,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'f40' }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2249, texture: 0, isProp: 0 },
                    1: { gender: 1, component: 11, drawable: 2258, texture: 7, isProp: 0 }
                }
            }
        }
    ],
},
{
    id: 61,
    free: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2001, texture: 0, isProp: 0 }, // Кобура на 1 ногу, темная
                    1: { gender: 1, component: 7, drawable: 2002, texture: 0, isProp: 0 }, // Кобура на 1 ногу, темная
                }
            }
        }
    ],
    premium: [
        // Татуировка случайная 4
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3081,
                    [1]: 6120,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3082,
                    [1]: 6119,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3083,
                    [1]: 6121,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3084,
                    [1]: 6122,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
},
{
    id: 62,
    free: [
        { rewardType: 'money', rewardData: { amount: 15000 }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'moonbeam' }, destroyXP: 750, color: 'purple' },
    ],
},
{
    id: 63,
    free: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'WEAPON_REVOLVER',
                skinId: 5
            },
            destroyXP: 500,
            color: 'blue'
        }, // Скин случайный
    ],
    premium: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_combatpdw',
                skinId: 3
            },
            destroyXP: 750,
            color: 'purple'
        }, // Скин случайный
    ],
},
{
    id: 64,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2246, texture: 9, isProp: 0, gender: 0 },
                    1: { component: 11, drawable: 2255, texture: 6, isProp: 0, gender: 1 },
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_carbinerifle',
                skinId: 3
            },
            destroyXP: 1500,
            color: 'red'
        }, // Скин случайный
    ],
},
{
    id: 65,
    free: [
        { rewardType: 'wheels', rewardData: { id: 12, amount: 3 }, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'dawn' }, destroyXP: 3000, color: 'gold' },
    ],
},
{
    id: 66,
    free: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_militaryrifle',
                skinId: 0
            },
            destroyXP: 500,
            color: 'blue'
        }, // Скин случайный
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 13, amount: 3 }, destroyXP: 750, color: 'red' },
    ],
},
{
    id: 67,
    free: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_carbinerifle_mk2',
                skinId: 1
            },
            destroyXP: 1500,
            color: 'red'
        }, // Скин случайный
    ],
    premium: [
        { rewardType: 'coins', rewardData: { amount: 250 }, destroyXP: 250, color: 'blue' },
    ],
},
{
    id: 68,
    free: [
        { rewardType: 'item', rewardData: { itemId: 333, count: 0 }, destroyXP: 750, color: 'purple' }, // Ручной пулемет Мk2
    ],
    premium: [
        // Татуировка случайная 3
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3012,
                    [1]: 6123,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3013,
                    [1]: 6054,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3015,
                    [1]: 6057,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3016,
                    [1]: 6059,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
},
{
    id: 69,
    free: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_smg_mk2',
                skinId: 2
            },
            destroyXP: 500,
            color: 'blue'
        }, // Скин случайный
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2146, texture: 0, isProp: 0 },
                    1: { gender: 1, component: 1, drawable: 2010, texture: 0, isProp: 1 }
                }
            }
        }
    ]
},
{
    id: 70,
    free: [
        { rewardType: 'vehicleMod', rewardData: { model: 'rs72', setId: 1 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
},
{
    id: 71,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 6, amount: 3 }, destroyXP: 750, color: 'red' },
    ],
},
{
    id: 72,
    free: [
        {
            color: 'purple',
             destroyXP: 750,
             rewardData: {
                 id: 23,
                 title: 'animations.list.Socks',
                 animData: [
                     'majestic_animations_2',
                     'socks'
                 ],
                 flag: 1,
                 looped: true,
                 music: 'socks'
             },
             rewardType: 'animation',
        }
    ],
    premium: [
        // Татуировка случайная 2
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3008,
                    [1]: 6046,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3009,
                    [1]: 6048,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3010,
                    [1]: 6049,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3011,
                    [1]: 6050,
                }
            },
            destroyXP: 350,
            color: 'blue'
        },
    ],
},
{
    id: 73,
    free: [
        // Татуировка случайная 1
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3002,
                    [1]: 6045,
                }
            },
            destroyXP: 500,
            color: 'purple'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3003,
                    [1]: 6061,
                }
            },
            destroyXP: 500,
            color: 'purple'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3005,
                    [1]: 6062,
                }
            },
            destroyXP: 500,
            color: 'purple'
        },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3006,
                    [1]: 6068,
                }
            },
            destroyXP: 500,
            color: 'purple'
        },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2001, texture: 3, isProp: 0 }, // Кобура на 1 ногу, белая
                    1: { gender: 1, component: 7, drawable: 2002, texture: 2, isProp: 0 }, // Кобура на 1 ногу, белая
                }
            }
        }
    ],
},
{
    id: 74,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
            rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2248, texture: 13, isProp: 0, gender: 0 },
                    1: { component: 6, drawable: 2062, texture: 1, isProp: 0, gender: 1 },
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 5, drawable: 2017, texture: 3, isProp: 0 },
                    1: { gender: 1, component: 5, drawable: 2018, texture: 4, isProp: 0 }
                }
            }
        }
    ]
},
{
    id: 75,
    free: [
        { rewardType: 'wheels', rewardData: { id: 5, amount: 3 }, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { rewardType: 'vehicleMod', rewardData: { model: 'gle63', setId: 1 }, destroyXP: 3000, color: 'gold' },
    ],
},
{
    id: 76,
    free: [],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 333, count: 0 }, destroyXP: 750, color: 'purple' }, // Ручной пулемет Мk2
    ],
},
{
    id: 77,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'WEAPON_HEAVYSHOTGUN',
                skinId: 7
            },
            destroyXP: 750,
            color: 'purple'
        }, // Скин случайный
    ],
},
{
    id: 78,
    free: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'weapon_revolver',
                skinId: 3
            },
            destroyXP: 750,
            color: 'purple'
        }, // Скин случайный
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 281, count: 0 }, destroyXP: 3000, color: 'gold' }, // Дефибриллятор
    ],
},
{
    id: 79,
    free: [
        { rewardType: 'item', rewardData: { itemId: 279, count: 0 }, destroyXP: 3000, color: 'gold' }, // Тяжелая снайперская винтовка
    ],
    premium: [
        {
            color: 'red',
             destroyXP: 1500,
             rewardData: {
                 id: 90,
                 title: 'animations.list.LilDiplodoculus',
                 animData: [
                     'majestic_animations_props',
                     'alfredo_player'
                 ],
                 flag: 33,
                 looped: true,
                 music: 'lil_diplodoculus',
             },
             rewardType: 'animation',
        }
    ],
},
{
    id: 80,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'fordgt' }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2072, texture: 2, isProp: 0 },
                    1: { gender: 1, component: 0, drawable: 2051, texture: 2, isProp: 1 }
                }
            }
        }
    ],
},
{
    id: 81,
    free: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 11
            },
            destroyXP: 500,
            color: 'blue'
        },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 3000, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ],
},
{
    id: 82,
    free: [{
        rewardType: 'clothes', destroyXP: 3000, color: 'gold',
        rewardData: {
            clothesData: {
                0: { component: 7, drawable: 2074, texture: 0, isProp: 0, gender: 0 },
                1: { component: 7, drawable: 2064, texture: 0, isProp: 0, gender: 1 },
            }
        }
    }],
    premium: [
        { rewardType: 'coins', rewardData: { amount: 300 }, destroyXP: 300, color: 'purple' },
    ],
},
{
    id: 83,
    free: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2027, texture: 0, isProp: 0 }, // Фотоаппарат на шее
                    1: { gender: 1, component: 7, drawable: 2016, texture: 0, isProp: 0 }, // Фотоаппарат на шее
                }
            }
        }
    ],
    premium: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
},
{
    id: 84,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2249, texture: 1, isProp: 0 },
                    1: { gender: 1, component: 7, drawable: 2062, texture: 2, isProp: 0 }
                }
            }
        }
    ]
},
{
    id: 85,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
            rewardData: {
                clothesData: {
                    0: { component: 4, drawable: 2146, texture: 1, isProp: 0, gender: 0 },
                    1: { component: 7, drawable: 2061, texture: 2, isProp: 0, gender: 1 },
                }
            }
        }
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'sf90' }, destroyXP: 3000, color: 'gold' },
    ],
},
{
    id: 86,
    free: [
        { rewardType: 'money', rewardData: { amount: 100000 }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'WEAPON_HEAVYSHOTGUN',
                skinId: 5
            },
            destroyXP: 750,
            color: 'purple'
        }, // Скин случайный
    ],
},
{
    id: 87,
    free: [
        { rewardType: 'item', rewardData: { itemId: 333, count: 0 }, destroyXP: 750, color: 'purple' }, // Ручной пулемет Мk2
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 75000 }, destroyXP: 750, color: 'purple' },
    ],
},
{
    id: 88,
    free: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'WEAPON_HEAVYSHOTGUN',
                skinId: 3
            },
            destroyXP: 1500,
            color: 'red'
        }, // Скин случайный
    ],
    premium: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 16
            },
            destroyXP: 500,
            color: 'blue'
        },
    ],
},
{
    id: 89,
    free: [
        { rewardType: 'item', rewardData: { itemId: 281, count: 0 }, destroyXP: 3000, color: 'gold' }, // Дефибриллятор
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 6, drawable: 2058, texture: 4, isProp: 0 },
                    1: { gender: 1, component: 6, drawable: 2062, texture: 4, isProp: 0 }
                }
            }
        }
    ]
},
{
    id: 90,
    free: [
        { rewardType: 'vehicleMod', rewardData: { model: '488pista', setId: 1 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 14, amount: 3 }, destroyXP: 750, color: 'red' },
    ],
},
{
    id: 91,
    free: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2029, texture: 1, isProp: 0 }, // Кобура на 2 ноги, темная
                    1: { gender: 1, component: 7, drawable: 2012, texture: 1, isProp: 0 }, // Кобура на 2 ноги, темная
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'armourSkin',
            rewardData: {
                entityName: 'light',
                skinId: 7
            },
            destroyXP: 500,
            color: 'blue'
        },
    ],
},
{
    id: 92,
    free: [
        {
            rewardType: 'weaponSkin',
            rewardData: {
                weaponName: 'WEAPON_HEAVYSHOTGUN',
                skinId: 0
            },
            destroyXP: 3000,
            color: 'gold'
        }, // Скин случайный
    ],
    premium: [
        {
            color: 'gold',
             destroyXP: 3000,
             rewardData: {
                 id: 81,
                 title: 'animations.list.LilBounce',
                 animData: [
                     'majestic_animations_props',
                     'hydraulics_player'
                 ],
                 flag: 33,
                 looped: true,
                 music: 'lil_bounce',
             },
             rewardType: 'animation',
        }
    ],
},
{
    id: 93,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'vip', rewardData: { type: 'Platinum', days: 60 }, destroyXP: 1500, color: 'red' },
    ],
},
{
    id: 94,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 7, amount: 3 }, destroyXP: 750, color: 'red' },
    ],
},
{
    id: 95,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2248, texture: 9, isProp: 0 }, // Спортивный костюм Gucci золотой и черный
                    1: { gender: 1, component: 4, drawable: 2152, texture: 7, isProp: 0 }, // Спортивный костюм Gucci золотой и черный
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2073, texture: 0, isProp: 0 }, // Белые катаны (светящиеся)
                    1: { gender: 1, component: 7, drawable: 2063, texture: 0, isProp: 0 } // Белые катаны (светящиеся)
                }
            }
        }
    ],
},
{
    id: 96,
    free: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 4, drawable: 2145, texture: 8, isProp: 0 }, // Спортивные штаны Gucci золотые
                    1: { gender: 1, component: 11, drawable: 2257, texture: 7, isProp: 0 } // Спортивные штаны Gucci золотые
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2029, texture: 2, isProp: 0 }, // Кобура на 2 ноги, белая
                    1: { gender: 1, component: 7, drawable: 2012, texture: 2, isProp: 0 }, // Кобура на 2 ноги, белая
                }
            }
        }
    ]
},

{
    id: 97,
    free: [
        { rewardType: 'money', rewardData: { amount: 300000 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 279, count: 0 }, destroyXP: 3000, color: 'gold' }, // Тяжелая снайперская винтовка
    ],
},
{
    id: 98,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2074, texture: 1, isProp: 0 }, // Белые катаны
                    1: { gender: 1, component: 7, drawable: 2064, texture: 1, isProp: 0 } // Белые катаны
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 11, drawable: 2249, texture: 3, isProp: 0 }, // Черно-белый костюм с красным галстуком
                    1: { gender: 1, component: 11, drawable: 2259, texture: 7, isProp: 0 } // Черно-белый костюм с красным галстуком
                }
            }
        }
    ]
},
{
    id: 99,
    free: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 3000, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 5, drawable: 2015, texture: 3, isProp: 0 }, // Тактический рюкзак черного цвета
                    1: { gender: 1, component: 5, drawable: 2016, texture: 4, isProp: 0 }, // Тактический рюкзак черного цвета
                }
            }
        }
    ],
},
{
    id: 100,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'centenario' }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'veneno' }, destroyXP: 3000, color: 'gold' },
    ],
}
]

export const seasonPassXP = {
    // Формула XP_START+(currentLevel*XP_STEP)
    XP_START: 300, // 1й уровень начинается с этого XP
    XP_STEP: 220, // Шаг на каждый уровень. (XP нужен для текущего уровня)+XP_STEP = следующий уровень
}

export const seasonPassgetXPForNextLevel = (currentLevel) => seasonPassXP.XP_START + (currentLevel * seasonPassXP.XP_STEP);
