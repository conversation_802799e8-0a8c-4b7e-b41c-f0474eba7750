import activeFractions from '@serverConfig/fractions/activeFractions';

export const seasonPassConfig = {
    SEASON_ID: 5,
    SEASON_TITLE: 'Лето 2024', // Используется только для формирования российских чеков. Не требует перевода

    TRANSFER_DAYS: 60, // Через сколько дней можно будет продавать/передавать одежду/транспорт

    MAX_TASKS_PER_PLAYER: 9, // ДОЛЖНО ДЕЛИТЬСЯ НА 3

    ACTIVE_DATE_RANGE: { from: '2024-05-16', to: '2024-09-01' },
    ACTIVE_CLAIM_DATE_RANGE: { from: '2024-05-16', to: '2024-09-15' },

    XP_PER_MC_EU: 2, // Сколько XP выдавать за 1 MCoin (EU)
    PRICE_EU_MULTIPLIER: 1,

    XP_PER_MC: 2, // Сколько XP выдавать за 1 MCoin
    XP_PER_PAYDAY: 300, // Сколько XP выдавать за 1 PayDay

    MULTIPLIER: { // Удваивать опыт за квесты и бонус за часы
        'RU1': 1,
        'RU2': 1,
        'RU3': 1,
        'RU4': 1,
        'RU5': 1,
        'RU6': 1,
        'RU7': 1,
        'RU8': 1,
        'RU9': 1,
        'RU10': 1,

        'RU11': 1,

        'PL1': 1,
        'DE1': 1,
        'BR1': 1,
    },

    PRICE: {
        starter: {
            coins: 0,
            rubles: 0,

            level: 1
        },

        bronze: {
            coins: 3500,
            rubles: 1390,

            level: 50
        },
        silver: {
            coins: 9500,
            rubles: 3990,

            level: 100
        },
        gold: {
            coins: 23000,
            rubles: 9590,

            level: 150
        }
    },

    SHOP_XP: [
        { buyId: 1, type: 'status' },

        {
            buyId: 'king',
            type: 'king',
            coins: 75000,
            rubles: 31990,
            exp: 300000,

            award: [
                { component: 5, drawable: 2070, texture: 8, isProp: 0 },
                { component: 5, drawable: 2069, texture: 8, isProp: 0 }
            ]
        },

        // Цена генерируется из EXP и XP_PER_MC
        { buyId: 3, type: 'experience', price: 500, bonus: 10, color: 'blue' }, // Бонус задается вручную
        { buyId: 4, type: 'experience', price: 2500, bonus: 15, color: 'purple' }, // Бонус задается вручную
        { buyId: 5, type: 'experience', price: 10000, bonus: 25, color: 'red' }, // Бонус задается вручную
        { buyId: 6, type: 'experience', price: 30000, bonus: 50, color: 'gold' }, // Бонус задается вручную
    ]
}

/**
 * Доступные параметры на задания.
 * @param  {Number}     taskId                          Идентификатор задания. Должно быть уникальным
 * @param  {String}     title                           Название задания i18n
 * @param  {String}     description                     Описание задания i18n
 * @param  {String}     difficulty                      Сложность задания
 * @param  {String}     trigger_type                    Тип триггера. Он задается в коде. Например, 'level_up'
 * @param  {Array}      trigger_value                   Доп триггер. Он задается в коде. Например ID купленного предмета
 * @param  {String}     trigger_amount                  Количество на триггер
 * @param  {Array}      serverRegions                   Регионы в которых будет доступно данное задание (по стандарту все)
 * @param  {Object}     fraction                        Данные для каких игроков давать задания во фракции
 * @param  {Array}      fraction.ids                    ID фракций для которых давать это задание
 * @param  {Array}      fraction.access                 Какие доступы фракций должны быть у игрока
 * @param  {Number}     fraction.rank                   Какой минимальный ранг нужен
 * @param  {String}     xp_reward                       Кол-во опыта за задание
 * @param  {Object}     dateRange                       Время действия задания. Объект с полями:
 * @param  {String}     dateRange.from                  Дата начала действия задания в формате 'DD-MM' или 'DD-MM-YY' или 'DD-MM-n (ЕСЛИ УСТАНОВЛЕН -N в качестве года, ЗНАЧИТ ОН ИСПОЛЬЗУЕТ СЛЕДУЮЩИЙ ГОД от текущего)
 * @param  {Number}     dateRange.to                    Дата окончания действия задания в формате 'DD-MM' или 'DD-MM-YY'
 * @param  {Number}     requiredLevel                   Уровень персонажа, требуемый для выполнения задания
 */

export const seasonPassTasks = [
    {
        // Поздороваться с игроками
        taskId: 1,
        difficulty: 'easy',
        title: 'seasonPass.tasks.greetPlayers.title',
        description: 'seasonPass.tasks.greetPlayers.description',
        xp_reward: 800,
        trigger_type: 'greet_player',
        trigger_amount: 25,
        disabled: true
    },
    // {
    //     // Принести подарки Эве
    //     taskId: 2,
    //     difficulty: 'medium',
    //     title: 'seasonPass.tasks.bringGifts.title',
    //     description: 'seasonPass.tasks.bringGifts.description',
    //     xp_reward: 1200,
    //     trigger_type: 'xmas_gift',
    //     trigger_amount: 50,
    //     dateRanges: [
    //         {from: '21-12-ce 00:00', to: '21-12-ce 23:59'},
    //         {from: '01-01-ce 00:00', to: '15-01-ce 23:59'},
    //     ]
    // },
    {
        // Съесть бургеры
        taskId: 3,
        difficulty: 'easy',
        title: 'seasonPass.tasks.eatBurgers.title',
        description: 'seasonPass.tasks.eatBurgers.description',
        xp_reward: 400,
        trigger_type: 'task_eat_item',
        trigger_value: [136],
        trigger_amount: 20,
    },
    {
        // Восстановить хп аптечкой
        taskId: 4,
        difficulty: 'easy',
        title: 'seasonPass.tasks.userMedkits.title',
        description: 'seasonPass.tasks.userMedkits.description',
        xp_reward: 800,
        trigger_type: 'task_medkit_item',
        trigger_value: [78, 530, 240],
        trigger_amount: 20,
    },
    {
        // Установить фейерверк
        taskId: 5,
        difficulty: 'medium',
        title: 'seasonPass.tasks.useFireworks.title',
        description: 'seasonPass.tasks.useFireworks.description',
        xp_reward: 1200,
        trigger_type: 'task_use_fireworks',
        trigger_value: [394, 395, 396, 397],
        trigger_amount: 5,
        requiredLevel: 2,
    },
    {
        // Выкурить сигареты
        taskId: 6,
        difficulty: 'easy',
        title: 'seasonPass.tasks.smokeSomething.title',
        description: 'seasonPass.tasks.smokeSomething.description',
        xp_reward: 400,
        trigger_type: 'task_use_cig',
        trigger_amount: 10,
    },
    {
        // Заправить автомобиль
        taskId: 7,
        difficulty: 'easy',
        title: 'seasonPass.tasks.fuelVehicle.title',
        description: 'seasonPass.tasks.fuelVehicle.description',
        xp_reward: 800,
        trigger_type: 'buy_item',
        trigger_value: ['regular', 'plus', 'premium', 'diesel', 'electro'],
        trigger_amount: 50,
    },
    {
        // Купить канистру
        taskId: 8,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyJerrycan.title',
        description: 'seasonPass.tasks.buyJerrycan.description',
        xp_reward: 400,
        trigger_type: 'buy_item',
        trigger_value: [77],
        trigger_amount: 2,
    },
    {
        // Использовать эмоциональное на игроке
        taskId: 9,
        difficulty: 'easy',
        title: 'seasonPass.tasks.emotionToPlayer.title',
        description: 'seasonPass.tasks.emotionToPlayer.description',
        xp_reward: 800,
        trigger_type: 'show_emotional',
        trigger_amount: 25,
    },
    {
        // Обменяться предметами с игроком
        taskId: 10,
        difficulty: 'easy',
        title: 'seasonPass.tasks.tradeItems.title',
        description: 'seasonPass.tasks.tradeItems.description',
        xp_reward: 500,
        trigger_type: 'trade_item',
        trigger_amount: 5,
    },
    {
        // Выйграть в рулетке казино
        taskId: 11,
        difficulty: 'medium',
        title: 'seasonPass.tasks.casinoRoulette.title',
        description: 'seasonPass.tasks.casinoRoulette.description',
        xp_reward: 1200,
        trigger_type: 'win_casino_roulette',
        trigger_amount: 20,
    },
    {
        // Выйграть в блекджек казино
        taskId: 12,
        difficulty: 'medium',
        title: 'seasonPass.tasks.casinoBlackjack.title',
        description: 'seasonPass.tasks.casinoBlackjack.description',
        xp_reward: 1800,
        trigger_type: 'win_casino_blackjack',
        trigger_amount: 15,
    },
    {
        // Выйграть в скачках казино
        taskId: 13,
        difficulty: 'hard',
        title: 'seasonPass.tasks.casinoHorserace.title',
        description: 'seasonPass.tasks.casinoHorserace.description',
        xp_reward: 3200,
        trigger_type: 'win_casino_horserace',
        trigger_amount: 5,
    },
    {
        // Выйграть в слот машинах казино
        taskId: 14,
        difficulty: 'medium',
        title: 'seasonPass.tasks.casinoSlots.title',
        description: 'seasonPass.tasks.casinoSlots.description',
        xp_reward: 1800,
        trigger_type: 'win_casino_slots',
        trigger_amount: 15,
    },
    {
        // Собрать клад
        taskId: 15,
        difficulty: 'hard',
        title: 'seasonPass.tasks.metalDetectorDig.title',
        description: 'seasonPass.tasks.metalDetectorDig.description',
        xp_reward: 3200,
        trigger_type: 'metal_detector_dig',
        trigger_amount: 5,
    },
    {
        // Выполнить рейсы на дальнобойщике
        taskId: 16,
        difficulty: 'medium',
        title: 'seasonPass.tasks.jobTrucker.title',
        description: 'seasonPass.tasks.jobTrucker.description',
        xp_reward: 1200,
        trigger_type: 'job_trucker',
        trigger_amount: 15,
    },
    {
        // Собрать грибы в лесу
        taskId: 17,
        difficulty: 'hard',
        title: 'seasonPass.tasks.jobMushroomer.title',
        description: 'seasonPass.tasks.jobMushroomer.description',
        xp_reward: 3200,
        trigger_type: 'job_mushroomer',
        trigger_amount: 90,
    },
    {
        // Выполнить рейсы на инкассаторе
        taskId: 18,
        difficulty: 'hard',
        title: 'seasonPass.tasks.jobMoneycollector.title',
        description: 'seasonPass.tasks.jobMoneycollector.description',
        xp_reward: 3200,
        trigger_type: 'job_moneycollector',
        trigger_amount: 20,
        hasItems: [568]
    },
    {
        // Выловить рыбу несколько раз
        taskId: 19,
        difficulty: 'medium',
        title: 'seasonPass.tasks.jobFishing.title',
        description: 'seasonPass.tasks.jobFishing.description',
        xp_reward: 1800,
        trigger_type: 'job_fisherman',
        trigger_amount: 25,
    },
    {
        // Собрать брёвна в лесу
        taskId: 20,
        difficulty: 'medium',
        title: 'seasonPass.tasks.jobLumberjack.title',
        description: 'seasonPass.tasks.jobLumberjack.description',
        xp_reward: 1200,
        trigger_type: 'job_lumberjack',
        trigger_amount: 35,
    },
    {
        // Выполнить рейсы на почтальоне
        taskId: 21,
        difficulty: 'easy',
        title: 'seasonPass.tasks.jobGopostal.title',
        description: 'seasonPass.tasks.jobGopostal.description',
        xp_reward: 1800,
        trigger_type: 'job_gopostal',
        trigger_amount: 25,
    },
    {
        // Выполнить рейсы на мусорщике
        taskId: 22,
        difficulty: 'medium',
        title: 'seasonPass.tasks.jobGargagecollector.title',
        description: 'seasonPass.tasks.jobGargagecollector.description',
        xp_reward: 1800,
        trigger_type: 'job_garbagecollector',
        trigger_amount: 110,
    },
    {
        // Кинуть снежки в игроков
        taskId: 23,
        difficulty: 'easy',
        title: 'seasonPass.tasks.throwSnows.title',
        description: 'seasonPass.tasks.throwSnows.description',
        xp_reward: 400,
        trigger_type: 'throw_snowball',
        trigger_amount: 10,
        dateRanges: [
            { from: '01-12-ce 00:00', to: '21-12-ce 23:59' },
            { from: '01-01-ce 00:00', to: '15-01-ce 23:59' },
        ]
    },
    {
        // Выпить алкоголь несколько раз
        taskId: 24,
        difficulty: 'easy',
        title: 'seasonPass.tasks.drinkAlcohol.title',
        description: 'seasonPass.tasks.drinkAlcohol.description',
        xp_reward: 500,
        trigger_type: 'task_drink_alcohol',
        trigger_amount: 15,
    },
    {
        // Сыграть в GunGame
        taskId: 25,
        difficulty: 'medium',
        title: 'seasonPass.tasks.playMinigames.title',
        description: 'seasonPass.tasks.playMinigames.description',
        xp_reward: 1800,
        trigger_type: 'play_minigames',
        trigger_amount: 5,
    },
    {
        // Использовать качалку
        taskId: 26,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useGym.title',
        description: 'seasonPass.tasks.useGym.description',
        xp_reward: 500,
        trigger_type: 'use_gym',
        trigger_amount: 20,
    },
    {
        // Купить что-то на рынке
        taskId: 27,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyInMarket.title',
        description: 'seasonPass.tasks.buyInMarket.description',
        xp_reward: 800,
        trigger_type: 'market_buy',
        trigger_amount: 15,
    },
    {
        // Продать что-то на рынке
        taskId: 28,
        difficulty: 'easy',
        title: 'seasonPass.tasks.sellInMarket.title',
        description: 'seasonPass.tasks.sellInMarket.description',
        xp_reward: 400,
        trigger_type: 'market_sell',
        trigger_amount: 15,
    },
    {
        // Арендовать автомобиль
        taskId: 29,
        difficulty: 'easy',
        title: 'seasonPass.tasks.rentVehicle.title',
        description: 'seasonPass.tasks.rentVehicle.description',
        xp_reward: 400,
        trigger_type: 'rent_vehicle',
        trigger_amount: 1,
    },
    {
        // Вызвать автомобиль в автосервисе
        taskId: 30,
        difficulty: 'easy',
        title: 'seasonPass.tasks.carService.title',
        description: 'seasonPass.tasks.carService.description',
        xp_reward: 400,
        trigger_type: 'fixcar_use',
        trigger_amount: 2,
    },
    {
        // Набить тату
        taskId: 31,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useTattoo.title',
        description: 'seasonPass.tasks.useTattoo.description',
        xp_reward: 500,
        trigger_type: 'tattoo_apply',
        trigger_amount: 1,
    },
    {
        // Позвонить по телефону
        taskId: 32,
        difficulty: 'easy',
        title: 'seasonPass.tasks.callUsingPhone.title',
        description: 'seasonPass.tasks.callUsingPhone.description',
        xp_reward: 800,
        trigger_type: 'phone_call',
        trigger_amount: 10,
        disabled: true
    },
    {
        // Написать СМС
        taskId: 33,
        difficulty: 'easy',
        title: 'seasonPass.tasks.smsUsingPhone.title',
        description: 'seasonPass.tasks.smsUsingPhone.description',
        xp_reward: 500,
        trigger_type: 'phone_sms',
        trigger_amount: 10,
        disabled: true
    },
    {
        // Кинотеатр
        taskId: 34,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useCinema.title',
        description: 'seasonPass.tasks.useCinema.description',
        xp_reward: 500,
        trigger_type: 'cinema_set_video',
        trigger_amount: 3,
    },
    {
        // Использовать колесо фортуны в казино
        taskId: 35,
        difficulty: 'easy',
        title: 'seasonPass.tasks.casinoLuckyWheel.title',
        description: 'seasonPass.tasks.casinoLuckyWheel.description',
        xp_reward: 800,
        trigger_type: 'casino_lucky_wheel',
        trigger_amount: 1,
        requiredLevel: 3
    },
    {
        // Использовать автомойку
        taskId: 36,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useCarWash.title',
        description: 'seasonPass.tasks.useCarWash.description',
        xp_reward: 800,
        trigger_type: 'use_carwash',
        trigger_amount: 3,
    },
    {
        // Выиграть в кости
        taskId: 37,
        difficulty: 'easy',
        title: 'seasonPass.tasks.winDice.title',
        description: 'seasonPass.tasks.winDice.description',
        xp_reward: 800,
        trigger_type: 'dice',
        trigger_value: ['win'],
        trigger_amount: 15,
    },
    {
        // Купить что-то в барбершопе
        taskId: 38,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyInBarbershop.title',
        description: 'seasonPass.tasks.buyInBarbershop.description',
        xp_reward: 500,
        trigger_type: 'barbershop_buy',
        trigger_amount: 1,
    },
    {
        // Использовать гриль
        taskId: 39,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useBBQ.title',
        description: 'seasonPass.tasks.useBBQ.description',
        xp_reward: 800,
        trigger_type: 'bbq_use',
        trigger_amount: 5,
    },
    // {
    //     // Заработать на вызовах в такси
    //     taskId: 40,
    //     difficulty: 'medium',
    //     title: 'seasonPass.tasks.workInTaxi.title',
    //     description: 'seasonPass.tasks.workInTaxi.description',
    //     xp_reward: 1800,
    //     trigger_type: 'taxi_work',
    //     trigger_amount: 30,
    // },
    {
        // Собрать любой урожай на ферме
        taskId: 41,
        difficulty: 'medium',
        title: 'seasonPass.tasks.harvestFarmer.title',
        description: 'seasonPass.tasks.harvestFarmer.description',
        xp_reward: 1800,
        trigger_type: 'farm_harvest',
        trigger_amount: 200,
    },
    {
        // Заказать приватный танец в стрип клубе
        taskId: 42,
        difficulty: 'easy',
        title: 'seasonPass.tasks.stripDance.title',
        description: 'seasonPass.tasks.stripDance.description',
        xp_reward: 400,
        trigger_type: 'private_dance_buy',
        trigger_amount: 1,
    },
    {
        // Починить авто на СТО
        taskId: 43,
        difficulty: 'hard',
        title: 'seasonPass.tasks.repairVehicle.title',
        description: 'seasonPass.tasks.repairVehicle.description',
        xp_reward: 3200,
        trigger_type: 'repair_vehicle',
        trigger_amount: 3,
        requiredLevel: 3,
    },
    /*     {
            // Сделать ставку в букмекере
            taskId: 44,
            difficulty: 'easy',
            serverRegions: ['RU'],
            title: 'seasonPass.tasks.bookmakerBet.title',
            description: 'seasonPass.tasks.bookmakerBet.description',
            xp_reward: 800,
            trigger_type: 'bookmaker_bet',
            trigger_amount: 50000,
        }, */
    {
        // Собрать дрифт очки
        taskId: 45,
        difficulty: 'hard',
        title: 'seasonPass.tasks.driftPoints.title',
        description: 'seasonPass.tasks.driftPoints.description',
        xp_reward: 3200,
        trigger_type: 'drift_points',
        trigger_amount: 12500,
    },
    {
        // Взять счастливый телефон
        taskId: 46,
        difficulty: 'medium',
        title: 'seasonPass.tasks.luckyPhone.title',
        description: 'seasonPass.tasks.luckyPhone.description',
        xp_reward: 1800,
        trigger_type: 'lucky_phone_pick',
        trigger_amount: 1,
    },
    {
        // Затюнинговать авто
        taskId: 47,
        difficulty: 'medium',
        title: 'seasonPass.tasks.tuningVehicle.title',
        description: 'seasonPass.tasks.tuningVehicle.description',
        xp_reward: 1200,
        trigger_type: 'tuning_vehicle',
        trigger_amount: 4,
    },
    {
        // Поставить бумбокс на землю
        taskId: 48,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useBoombox.title',
        description: 'seasonPass.tasks.useBoombox.description',
        xp_reward: 400,
        trigger_type: 'boombox_use',
        trigger_amount: 1,
    },
    {
        // Взломать зажигание отвёрткой у чужого авто
        taskId: 49,
        difficulty: 'hard',
        title: 'seasonPass.tasks.hijackVehicle.title',
        description: 'seasonPass.tasks.hijackVehicle.description',
        xp_reward: 3200,
        trigger_type: 'hijack_vehicle_screwdriver',
        trigger_amount: 5,
        fraction: {
            ids: activeFractions.crime
        },
    },
    // {
    //     // Порубить мясо
    //     taskId: 50,
    //     difficulty: 'medium',
    //     title: 'seasonPass.tasks.butcher.title',
    //     description: 'seasonPass.tasks.butcher.description',
    //     xp_reward: 1200,
    //     trigger_type: 'job_butcher',
    //     trigger_amount: 100,
    // },
    {
        // Собрать сырье на шахте
        taskId: 51,
        difficulty: 'medium',
        title: 'seasonPass.tasks.quarryMan.title',
        description: 'seasonPass.tasks.quarryMan.description',
        xp_reward: 1800,
        trigger_type: 'job_quarryman',
        trigger_amount: 80,
    },
    {
        // Раскрыть парашют
        taskId: 52,
        difficulty: 'medium',
        title: 'seasonPass.tasks.parachuteOpen.title',
        description: 'seasonPass.tasks.parachuteOpen.description',
        xp_reward: 1000,
        trigger_type: 'parachute_open',
        trigger_amount: 10,
        requiredLevel: 2,
    },
    {
        // Сломать оружие
        taskId: 53,
        difficulty: 'hard',
        title: 'seasonPass.tasks.breakWeapon.title',
        description: 'seasonPass.tasks.breakWeapon.description',
        xp_reward: 3200,
        trigger_type: 'weapon_break',
        trigger_amount: 1,
        requiredLevel: 2,
    },
    {
        // Сыграть в армреслинг
        taskId: 54,
        difficulty: 'easy',
        title: 'seasonPass.tasks.armWrestle.title',
        description: 'seasonPass.tasks.armWrestle.description',
        xp_reward: 1200,
        trigger_type: 'arm_wrestle_play',
        trigger_amount: 5,
    },
    {
        // Купить отвёртку
        taskId: 55,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyScrewdriver.title',
        description: 'seasonPass.tasks.buyScrewdriver.description',
        xp_reward: 500,
        trigger_type: 'buy_item',
        trigger_value: [8],
        trigger_amount: 5,
    },
    {
        // Купить лотерейный билет
        taskId: 56,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyLottery.title',
        description: 'seasonPass.tasks.buyLottery.description',
        xp_reward: 800,
        trigger_type: 'buy_item',
        trigger_value: [317],
        trigger_amount: 1,
    },
    {
        // Съесть шаурму без отравления
        taskId: 57,
        difficulty: 'easy',
        title: 'seasonPass.tasks.eatShawarma.title',
        description: 'seasonPass.tasks.eatShawarma.description',
        xp_reward: 800,
        trigger_type: 'task_eat_item',
        trigger_value: [342],
        trigger_amount: 3,
    },
    {
        // Толкнуть машину
        taskId: 58,
        difficulty: 'easy',
        title: 'seasonPass.tasks.pushCar.title',
        description: 'seasonPass.tasks.pushCar.description',
        xp_reward: 800,
        trigger_type: 'push_car',
        trigger_amount: 15,
    },
    {
        // Свести тату с тела
        taskId: 59,
        difficulty: 'easy',
        title: 'seasonPass.tasks.removeTattoo.title',
        description: 'seasonPass.tasks.removeTattoo.description',
        xp_reward: 800,
        trigger_type: 'remove_tattoo',
        trigger_amount: 2,
    },
    {
        // Заменить масло
        taskId: 60,
        difficulty: 'easy',
        title: 'seasonPass.tasks.changeOil.title',
        description: 'seasonPass.tasks.changeOil.description',
        xp_reward: 800,
        trigger_type: 'change_oil',
        trigger_amount: 1,
    },
    {
        // Заменить аккумулятор
        taskId: 61,
        difficulty: 'easy',
        title: 'seasonPass.tasks.changeBattery.title',
        description: 'seasonPass.tasks.changeBattery.description',
        xp_reward: 800,
        trigger_type: 'change_battery',
        trigger_amount: 1,
    },
    {
        // Взять на руки девушек
        taskId: 62,
        difficulty: 'easy',
        title: 'seasonPass.tasks.carryFemales.title',
        description: 'seasonPass.tasks.carryFemales.description',
        xp_reward: 800,
        trigger_type: 'carry_female',
        trigger_amount: 5,
    },
    {
        // Взять на руки парней
        taskId: 63,
        difficulty: 'easy',
        title: 'seasonPass.tasks.carryMales.title',
        description: 'seasonPass.tasks.carryMales.description',
        xp_reward: 800,
        trigger_type: 'carry_male',
        trigger_amount: 5,
    },
    {
        // Пополнить счёт телефона
        taskId: 64,
        difficulty: 'easy',
        title: 'seasonPass.tasks.topUpPhone.title',
        description: 'seasonPass.tasks.topUpPhone.description',
        xp_reward: 400,
        trigger_type: 'topup_phone',
        trigger_amount: 500,
        requiredLevel: 3,
    },
    {
        // Вылечиться у NPC-персонажа в больнице
        taskId: 65,
        difficulty: 'easy',
        title: 'seasonPass.tasks.healInHospital.title',
        description: 'seasonPass.tasks.healInHospital.description',
        xp_reward: 800,
        trigger_type: 'heal_in_hospital',
        trigger_amount: 5,
    },
    {
        // Вакцинироваться в больнице у доктора
        taskId: 66,
        difficulty: 'hard',
        title: 'seasonPass.tasks.vaccinate.title',
        description: 'seasonPass.tasks.vaccinate.description',
        xp_reward: 3200,
        trigger_type: 'vaccinate',
        trigger_amount: 1,
        requiredLevel: 4
    },
    {
        // Покопаться в мусорке
        taskId: 67,
        difficulty: 'medium',
        title: 'seasonPass.tasks.trashSearch.title',
        description: 'seasonPass.tasks.trashSearch.description',
        xp_reward: 1200,
        trigger_type: 'search_trash',
        trigger_amount: 30,
    },
    // {
    //     // Припарковать арендованный автомобиль
    //     taskId: 68,
    //     difficulty: 'easy',
    //     title: 'seasonPass.tasks.parkRentVehicle.title',
    //     description: 'seasonPass.tasks.parkRentVehicle.description',
    //     xp_reward: 500,
    //     trigger_type: 'park_rented_vehicle',
    //     trigger_amount: 1,
    // },
    {
        // Воспользоваться торговыми аппаратами
        taskId: 69,
        difficulty: 'easy',
        title: 'seasonPass.tasks.vendingMachine.title',
        description: 'seasonPass.tasks.vendingMachine.description',
        xp_reward: 600,
        trigger_type: 'vending_machine_use',
        trigger_amount: 10,
    },
    {
        // Прокатиться на колесе обозрения
        taskId: 70,
        difficulty: 'easy',
        title: 'seasonPass.tasks.ferrisWheelRide.title',
        description: 'seasonPass.tasks.ferrisWheelRide.description',
        xp_reward: 800,
        trigger_type: 'ferris_wheel_ride',
        trigger_amount: 2,
        disabled: true,
    },
    {
        // Прокатиться на американских горках
        taskId: 71,
        difficulty: 'easy',
        title: 'seasonPass.tasks.rollerCoasterRide.title',
        description: 'seasonPass.tasks.rollerCoasterRide.description',
        xp_reward: 800,
        trigger_type: 'roller_coaster_ride',
        trigger_amount: 2,
        disabled: true,
    },
    // {
    //     // Убить животных и собрать их мясо
    //     taskId: 72,
    //     difficulty: 'medium',
    //     title: 'seasonPass.tasks.huntAnimals.title',
    //     description: 'seasonPass.tasks.huntAnimals.description',
    //     xp_reward: 1800,
    //     trigger_type: 'hunting_animals',
    //     trigger_amount: 10,
    //     requiredLevel: 2
    // },
    // {
    //     // Погладить любых животных
    //     taskId: 73,
    //     difficulty: 'medium',
    //     title: 'seasonPass.tasks.petAnimals.title',
    //     description: 'seasonPass.tasks.petAnimals.description',
    //     xp_reward: 1000,
    //     trigger_type: 'pet_animals',
    //     trigger_amount: 10,
    // },
    {
        // Купить гитару
        taskId: 74,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyGuitar.title',
        description: 'seasonPass.tasks.buyGuitar.description',
        xp_reward: 400,
        trigger_type: 'buy_item',
        trigger_value: [139],
        trigger_amount: 1,
    },
    {
        // Купить что-то в магазине одежды
        taskId: 75,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyInClothesShop.title',
        description: 'seasonPass.tasks.buyInClothesShop.description',
        xp_reward: 800,
        trigger_type: 'clothesshop_buy',
        trigger_amount: 5,
    },
    // {
    //     // Взорвать транспорт
    //     taskId: 76,
    //     difficulty: 'medium',
    //     title: 'seasonPass.tasks.explodeVehicle.title',
    //     description: 'seasonPass.tasks.explodeVehicle.description',
    //     xp_reward: 900,
    //     trigger_type: 'vehicle_explode',
    //     trigger_amount: 3,
    // },
    {
        // Заработать деньги на любой работе
        taskId: 77,
        difficulty: 'hard',
        title: 'seasonPass.tasks.earnSalary.title',
        description: 'seasonPass.tasks.earnSalary.description',
        xp_reward: 3200,
        trigger_type: 'salary_job',
        trigger_amount: 30000,
    },
    {
        // Работать водителем автобуса
        taskId: 78,
        difficulty: 'hard',
        title: 'seasonPass.tasks.busDriver.title',
        description: 'seasonPass.tasks.busDriver.description',
        xp_reward: 3200,
        trigger_type: 'job_busdriver',
        trigger_amount: 50,
    },
    {
        // Использовать Dragy
        taskId: 79,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useDragy.title',
        description: 'seasonPass.tasks.useDragy.description',
        xp_reward: 400,
        trigger_type: 'dragy_use',
        trigger_amount: 1,
    },
    {
        // Получить зарплату в PayDay
        taskId: 80,
        difficulty: 'medium',
        title: 'seasonPass.tasks.getPayDay.title',
        description: 'seasonPass.tasks.getPayDay.description',
        xp_reward: 1800,
        trigger_type: 'payday',
        trigger_amount: 3,
    },
    {
        // Сыграть на пианино
        taskId: 81,
        difficulty: 'easy',
        title: 'seasonPass.tasks.playPiano.title',
        description: 'seasonPass.tasks.playPiano.description',
        xp_reward: 400,
        trigger_type: 'play_piano',
        trigger_amount: 10,
    },
    {
        // Проехать N км на транспорте
        taskId: 82,
        difficulty: 'medium',
        title: 'seasonPass.tasks.mileage.title',
        description: 'seasonPass.tasks.mileage.description',
        xp_reward: 1200,
        trigger_type: 'mileage',
        trigger_amount: 50,
    },
    {
        // Прокатиться на лифте
        taskId: 83,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useElevator.title',
        description: 'seasonPass.tasks.useElevator.description',
        xp_reward: 400,
        trigger_type: 'use_elevator',
        trigger_amount: 5,
    },
    {
        // Выполнить квест у работодателя
        taskId: 84,
        difficulty: 'hard',
        title: 'seasonPass.tasks.completeJobQuest.title',
        description: 'seasonPass.tasks.completeJobQuest.description',
        xp_reward: 3200,
        trigger_type: 'quest_complete',
        trigger_value: [4, 5, 6, 7, 8, 9, 10, 11, 14, 20], // Квесты работ
        trigger_amount: 2,
    },
    {
        // Арендовать лодку
        taskId: 85,
        difficulty: 'easy',
        title: 'seasonPass.tasks.rentBoat.title',
        description: 'seasonPass.tasks.rentBoat.description',
        xp_reward: 800,
        trigger_type: 'rent_vehicle_type',
        trigger_value: ['boat'],
        trigger_amount: 1,
    },
    {
        // Прокатиться в багажнике
        taskId: 86,
        difficulty: 'easy',
        title: 'seasonPass.tasks.rideInTrunk.title',
        description: 'seasonPass.tasks.rideInTrunk.description',
        xp_reward: 400,
        trigger_type: 'travel_distance',
        trigger_value: ['inTrunk'],
        trigger_amount: 1000,
    },
    {
        // Построить снеговика
        taskId: 87,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buildSnowman.title',
        description: 'seasonPass.tasks.buildSnowman.description',
        xp_reward: 800,
        trigger_type: 'snowman_build',
        trigger_amount: 5,
        dateRanges: [
            { from: '01-12-ce 00:00', to: '21-12-ce 23:59' },
            { from: '01-01-ce 00:00', to: '15-01-ce 23:59' },
        ]
    },
    {
        // Сломать снеговика
        taskId: 88,
        difficulty: 'easy',
        title: 'seasonPass.tasks.breakSnowman.title',
        description: 'seasonPass.tasks.breakSnowman.description',
        xp_reward: 800,
        trigger_type: 'snowman_break',
        trigger_amount: 5,
        dateRanges: [
            { from: '01-12-ce 00:00', to: '21-12-ce 23:59' },
            { from: '01-01-ce 00:00', to: '15-01-ce 23:59' },
        ]
    },
    {
        // Прокатиться на руках
        taskId: 89,
        difficulty: 'easy',
        title: 'seasonPass.tasks.rideCarryngs.title',
        description: 'seasonPass.tasks.rideCarryngs.description',
        xp_reward: 500,
        trigger_type: 'travel_distance',
        trigger_value: ['onCarryngs'],
        trigger_amount: 50,
    },
    {
        // Заправить машину с помощью канистры
        taskId: 90,
        difficulty: 'easy',
        title: 'seasonPass.tasks.jerrycanUse.title',
        description: 'seasonPass.tasks.jerrycanUse.description',
        xp_reward: 500,
        trigger_type: 'jerrycan_use',
        trigger_amount: 30,
    },
    {
        // Собрать сырьё на работе карьерщика
        taskId: 91,
        difficulty: 'hard',
        title: 'seasonPass.tasks.mining.title',
        description: 'seasonPass.tasks.mining.description',
        xp_reward: 3200,
        trigger_type: 'job_mining',
        trigger_amount: 80,
    },
    // {
    //     // Собрать сырьё на работе шахтера
    //     taskId: 92,
    //     difficulty: 'hard',
    //     title: 'seasonPass.tasks.mining.title',
    //     description: 'seasonPass.tasks.mining.description',
    //     xp_reward: 3200,
    //     trigger_type: 'job_mining',
    //     trigger_amount: 60,
    // },
    /*     {
            // Перевести деньги на банковскую карту
            taskId: 93,
            difficulty: 'easy',
            title: 'seasonPass.tasks.bankTransferCard.title',
            description: 'seasonPass.tasks.bankTransferCard.description',
            xp_reward: 800,
            trigger_type: 'bank_transfer',
            trigger_amount: 3000,
        }, */
    {
        // Сыграть на гитаре
        taskId: 94,
        difficulty: 'easy',
        title: 'seasonPass.tasks.playGuitar.title',
        description: 'seasonPass.tasks.playGuitar.description',
        xp_reward: 400,
        trigger_type: 'task_play_item',
        trigger_value: ['guitar'],
        trigger_amount: 1,
    },
    {
        // Посмотреть в бинокль
        taskId: 95,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useBinoculars.title',
        description: 'seasonPass.tasks.useBinoculars.description',
        xp_reward: 400,
        trigger_type: 'use_binoculars',
        trigger_amount: 1,
    },
    {
        // Убить иргоков на арене
        taskId: 96,
        difficulty: 'easy',
        title: 'seasonPass.tasks.arenaKill20Players.title',
        description: 'seasonPass.tasks.arenaKill20Players.description',
        xp_reward: 600,
        trigger_type: 'arena_kill20',
        trigger_amount: 20,
    },
    {
        // Используйте ультимейт в режиме дм случайное оружие на арене
        taskId: 97,
        difficulty: 'hard',
        title: 'seasonPass.tasks.arenaUltimate5Times.title',
        description: 'seasonPass.tasks.arenaUltimate5Times.description',
        xp_reward: 3200,
        trigger_type: 'arena_ultimate5Times',
        trigger_amount: 3,
    },
    {
        // Купите снаряжения на 10 тысяч в любом режиме с платным магазином на арене
        taskId: 98,
        difficulty: 'easy',
        title: 'seasonPass.tasks.arenaSpend10kInShop.title',
        description: 'seasonPass.tasks.arenaSpend10kInShop.description',
        xp_reward: 600,
        trigger_type: 'arena_spend10k',
        trigger_amount: 10000,
    },
    {
        // Убейте лидера матча в режиме дм или гг на арене
        taskId: 99,
        difficulty: 'easy',
        title: 'seasonPass.tasks.arenaKillLeader5Times.title',
        description: 'seasonPass.tasks.arenaKillLeader5Times.description',
        xp_reward: 800,
        trigger_type: 'arena_killLeader5Times',
        trigger_amount: 5,
    },
    {
        // Убейте игроков с револьвера на арене
        taskId: 100,
        difficulty: 'easy',
        title: 'seasonPass.tasks.arenaKill20PlayersWithRevolver.title',
        description: 'seasonPass.tasks.arenaKill20PlayersWithRevolver.description',
        xp_reward: 600,
        trigger_type: 'arena_kill20PlayerWithRevolver',
        trigger_amount: 20,
    },
    {
        // Достигните 20 уровня в гонке вооружений на арене
        taskId: 101,
        difficulty: 'hard',
        title: 'seasonPass.tasks.arenaGG20Levels5Times.title',
        description: 'seasonPass.tasks.arenaGG20Levels5Times.description',
        xp_reward: 3200,
        trigger_type: 'arena_gg20Levels5Times',
        trigger_amount: 2,
    },
    // {
    //     // Совершите обгоны в режиме классической гонки на арене
    //     taskId: 102,
    //     difficulty: 'easy',
    //     title: 'seasonPass.tasks.arena20Overtakes.title',
    //     description: 'seasonPass.tasks.arena20Overtakes.description',
    //     xp_reward: 400,
    //     trigger_type: 'arena_overtakes20',
    //     trigger_amount: 20,
    // },
    {
        // Улучшите транспорт в режиме дерби на арене
        taskId: 103,
        difficulty: 'easy',
        title: 'seasonPass.tasks.arenaLvlUp5TimesDerby.title',
        description: 'seasonPass.tasks.arenaLvlUp5TimesDerby.description',
        xp_reward: 800,
        trigger_type: 'arena_derbyLvlup5Times',
        trigger_amount: 5,
        disabled: true,
    },
    {
        // Счастливый квест
        taskId: 104,
        difficulty: 'hard',
        title: 'seasonPass.tasks.luckyQuest.title',
        description: 'seasonPass.tasks.luckyQuest.description',
        xp_reward: 3200,
        trigger_type: 'lucky_quest',
        trigger_amount: 1
    },
    {
        // Купить кальян
        taskId: 105,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyHookah.title',
        description: 'seasonPass.tasks.buyHookah.description',
        xp_reward: 800,
        trigger_type: 'buy_item',
        trigger_value: [336],
        trigger_amount: 2,
    },
    {
        // Купить вейп
        taskId: 106,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyVape.title',
        description: 'seasonPass.tasks.buyVape.description',
        xp_reward: 500,
        trigger_type: 'buy_item',
        trigger_value: [335],
        trigger_amount: 1,
    },
    // {
    //     // Наберать очки в игре "2048" на планшете TODO: доделать на вью
    //     taskId: 107,
    //     difficulty: 'easy',
    //     title: 'seasonPass.tasks.game2048.title',
    //     description: 'seasonPass.tasks.game2048.description',
    //     xp_reward: 500,
    //     trigger_type: 'game2048',
    //     trigger_amount: 3000,
    // },
    {
        // Выставить предмет на продажу используя маркетплейс
        taskId: 108,
        difficulty: 'easy',
        title: 'seasonPass.tasks.sellOnMarketplace.title',
        description: 'seasonPass.tasks.sellOnMarketplace.description',
        xp_reward: 250,
        trigger_type: 'sellOnMarketplace',
        // trigger_value: [335],
        trigger_amount: 3,
    },
    {
        // Купите предмет на марктеплейсе
        taskId: 109,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyOnMarketplace.title',
        description: 'seasonPass.tasks.buyOnMarketplace.description',
        xp_reward: 500,
        trigger_type: 'buyOnMarketplace',
        // trigger_value: [335],
        trigger_amount: 5,
    },
    {
        // Проведите тест драйв транспорта на маркетплейсе
        taskId: 110,
        difficulty: 'easy',
        title: 'seasonPass.tasks.testDriveOnMarketplace.title',
        description: 'seasonPass.tasks.testDriveOnMarketplace.description',
        xp_reward: 400,
        trigger_type: 'testDriveOnMarketplace',
        trigger_amount: 1,
    },
    // {
    //     // Сыграйте в мини игры (через F2)
    //     taskId: 111,
    //     difficulty: 'easy',
    //     serverRegions: ['RU'],
    //     title: 'seasonPass.tasks.playMinigameF2.title',
    //     description: 'seasonPass.tasks.playMinigameF2.description',
    //     xp_reward: 330,
    //     trigger_type: 'playMinigameF2',
    //     trigger_value: ['crash', 'jackpot', 'wheel'],
    //     trigger_amount: 2,
    // },
    {
        // Затянитесь кальяном
        taskId: 112,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useHookah.title',
        description: 'seasonPass.tasks.useHookah.description',
        xp_reward: 330,
        trigger_type: 'useHookah',
        trigger_amount: 5,
    },
    {
        // Проплывите на водном транспорте N км
        taskId: 113,
        difficulty: 'medium',
        title: 'seasonPass.tasks.boatMileage.title',
        description: 'seasonPass.tasks.boatMileage.description',
        xp_reward: 750,
        trigger_type: 'mileage',
        trigger_value: ['boat'],
        trigger_amount: 5,
    },
    /*     {
            // Сломать предмет топор
            taskId: 114,
            difficulty: 'hard',
            title: 'seasonPass.tasks.breakItemAxe.title',
            description: 'seasonPass.tasks.breakItemAxe.description',
            xp_reward: 3200,
            trigger_type: 'item_break',
            trigger_value: [131, 670],
            trigger_amount: 1,
        }, */
    // {
    //     // Сломать предмет кирка
    //     taskId: 115,
    //     difficulty: 'easy',
    //     title: 'seasonPass.tasks.breakItemPickAxe.title',
    //     description: 'seasonPass.tasks.breakItemPickAxe.description',
    //     xp_reward: 3200,
    //     trigger_type: 'item_break',
    //     trigger_value: [97],
    //     trigger_amount: 1,
    // },
    {
        // Сдайте транспорт на свалку
        taskId: 116,
        difficulty: 'medium',
        title: 'seasonPass.tasks.dumpCar.title',
        description: 'seasonPass.tasks.dumpCar.description',
        xp_reward: 1000,
        trigger_type: 'dumpCar',
        trigger_amount: 1,
    },
    {
        // Воспользуйтесь автопилотом
        taskId: 117,
        difficulty: 'medium',
        title: 'seasonPass.tasks.useAutopilot.title',
        description: 'seasonPass.tasks.useAutopilot.description',
        xp_reward: 1200,
        trigger_type: 'useAutopilot',
        trigger_amount: 1,
    },
    {
        // Выкопайте червя
        taskId: 118,
        difficulty: 'medium',
        title: 'seasonPass.tasks.digWorms.title',
        description: 'seasonPass.tasks.digWorms.description',
        xp_reward: 1000,
        trigger_type: 'digWorms',
        trigger_amount: 30,
    },
    // {
    //     // Употребите сырое мясо дичи
    //     taskId: 119,
    //     difficulty: 'medium',
    //     title: 'seasonPass.tasks.eatRawMeat.title',
    //     description: 'seasonPass.tasks.eatRawMeat.description',
    //     xp_reward: 500,
    //     trigger_type: 'task_eat_item',
    //     trigger_value: [599, 600, 601, 602, 603, 604],
    //     trigger_amount: 5,
    // },
    {
        // Смените замок на транспорте
        taskId: 120,
        difficulty: 'easy',
        title: 'seasonPass.tasks.changeVehLock.title',
        description: 'seasonPass.tasks.changeVehLock.description',
        xp_reward: 500,
        trigger_type: 'changeVehLock',
        trigger_amount: 1,
    },
    {
        // Создать группу
        taskId: 121,
        difficulty: 'easy',
        title: 'seasonPass.tasks.createParty.title',
        description: 'seasonPass.tasks.createParty.description',
        xp_reward: 330,
        trigger_type: 'createParty',
        trigger_amount: 3,
    },
    {
        // Вступить в группу
        taskId: 122,
        difficulty: 'easy',
        title: 'seasonPass.tasks.joinParty.title',
        description: 'seasonPass.tasks.joinParty.description',
        xp_reward: 330,
        trigger_type: 'joinParty',
        trigger_amount: 3,
    },
    {
        // Используйте анимацию (через F2)
        taskId: 123,
        difficulty: 'easy',
        title: 'seasonPass.tasks.useAnim.title',
        description: 'seasonPass.tasks.useAnim.description',
        xp_reward: 250,
        trigger_type: 'useAnim',
        trigger_amount: 20,
    },
    {
        // Снимите наличные с банкомата
        taskId: 124,
        difficulty: 'easy',
        title: 'seasonPass.tasks.withdrawFromATM.title',
        description: 'seasonPass.tasks.withdrawFromATM.description',
        xp_reward: 330,
        trigger_type: 'withdrawFromATM',
        trigger_amount: 20000,
    },
    {
        // Доставьте угнанный транспорт
        taskId: 125,
        difficulty: 'hard',
        title: 'seasonPass.tasks.deliveryHijackCar.title',
        description: 'seasonPass.tasks.deliveryHijackCar.description',
        fraction: {
            ids: activeFractions.crime
        },
        xp_reward: 3200,
        trigger_type: 'deliveryHijackCar',
        trigger_amount: 1,
        requiredLevel: 4,
    },
    {
        // Развезите закладки
        taskId: 126,
        difficulty: 'medium',
        title: 'seasonPass.tasks.deliveryDrugs.title',
        description: 'seasonPass.tasks.deliveryDrugs.description',
        fraction: {
            ids: activeFractions.crime
        },
        xp_reward: 1800,
        trigger_type: 'deliveryDrugs',
        trigger_amount: 30,
        requiredLevel: 3,
    },
    {
        // Вакцинируйте игроков
        taskId: 127,
        difficulty: 'medium',
        title: 'seasonPass.tasks.vaccinatePlayers.title',
        description: 'seasonPass.tasks.vaccinatePlayers.description',
        fraction: {
            ids: activeFractions.ems,
            rank: 2
        },
        xp_reward: 1800,
        trigger_type: 'vaccinatePlayers',
        trigger_amount: 6,
        requiredLevel: 4,
    },
    {
        // Вылечите игроков с помощью таблетки
        taskId: 128,
        difficulty: 'medium',
        title: 'seasonPass.tasks.givePills.title',
        description: 'seasonPass.tasks.givePills.description',
        fraction: {
            ids: activeFractions.ems
        },
        xp_reward: 1500,
        trigger_type: 'givePills',
        trigger_amount: 20,
    },
    {
        // Реанимируйте игроков
        taskId: 129,
        difficulty: 'medium',
        title: 'seasonPass.tasks.rescuePlayers.title',
        description: 'seasonPass.tasks.rescuePlayers.description',
        fraction: {
            ids: activeFractions.ems,
            rank: 2
        },
        xp_reward: 1800,
        trigger_type: 'rescuePlayers',
        trigger_amount: 4,
    },
    {
        // Подключите Google Authenticator (через настройки)
        taskId: 130,
        difficulty: 'special', // Выдается отдельно
        title: 'seasonPass.tasks.connect2fa.title',
        description: 'seasonPass.tasks.connect2fa.description',
        xp_reward: 1200,
        trigger_type: 'connect2fa',
        trigger_amount: 1,
    },
    {
        // Арендуйте транспорт на маркетплейсе
        taskId: 131,
        difficulty: 'medium',
        title: 'seasonPass.tasks.marketplaceRentcar.title',
        description: 'seasonPass.tasks.marketplaceRentcar.description',
        xp_reward: 800,
        trigger_type: 'marketplaceRentcar',
        trigger_amount: 1
    },
    {
        // Обыщите тайник
        taskId: 132,
        difficulty: 'hard',
        title: 'seasonPass.tasks.searchStash.title',
        description: 'seasonPass.tasks.searchStash.description',
        xp_reward: 3200,
        trigger_type: 'searchStash',
        trigger_amount: 5,
        requiredLevel: 3,
        fraction: {
            ids: activeFractions.crime
        },
    },
    {
        // Заполните шкалу мирового квеста
        taskId: 133,
        difficulty: 'hard',
        title: 'seasonPass.tasks.worldQuest.title',
        description: 'seasonPass.tasks.worldQuest.description',
        xp_reward: 3200,
        trigger_type: 'worldQuest',
        trigger_amount: 10,
        wqMaxProgress: 89
    },
    /*     {
            // Подтвердите email аккаунта в настройках
            taskId: 134,
            difficulty: 'special',
            title: 'seasonPass.tasks.confirmEmail.title',
            description: 'seasonPass.tasks.confirmEmail.description',
            xp_reward: 800,
            trigger_type: 'confirmEmail',
            trigger_amount: 1
        }, */
    {
        // Потратьте боеприпасы стреляя из оружия
        taskId: 135,
        difficulty: 'medium',
        requiredLevel: 3,
        title: 'seasonPass.tasks.weaponShot.title',
        description: 'seasonPass.tasks.weaponShot.description',
        xp_reward: 1200,
        trigger_type: 'weaponShot',
        trigger_amount: 2000,
    },
    {
        // Избавьтесь от одного уровня розыска
        taskId: 136,
        difficulty: 'medium',
        title: 'seasonPass.tasks.clearWanted.title',
        description: 'seasonPass.tasks.clearWanted.description',
        xp_reward: 1200,
        trigger_type: 'clearWanted',
        trigger_amount: 1,
        fraction: {
            ids: activeFractions.crime
        }
    },
    {
        // Вступите в семью
        taskId: 137,
        difficulty: 'medium',
        title: 'seasonPass.tasks.joinFamily.title',
        description: 'seasonPass.tasks.joinFamily.description',
        xp_reward: 1200,
        trigger_type: 'joinFamily',
        trigger_amount: 1,
        requiredLevel: 3,
        notInFamily: true
    },
    {
        // Выполните ежедневные задания
        taskId: 138,
        difficulty: 'hard',
        title: 'seasonPass.tasks.completeDailyTask.title',
        description: 'seasonPass.tasks.completeDailyTask.description',
        xp_reward: 3200,
        completedDaily: 1,
        trigger_type: 'completeDaily',
        trigger_amount: 2
    },
    /*     {
            // Выполните достижение
            taskId: 139,
            difficulty: 'hard',
            title: 'seasonPass.tasks.completeAchievement.title',
            description: 'seasonPass.tasks.completeAchievement.description',
            xp_reward: 3200,
            trigger_type: 'completeAchievement',
            trigger_amount: 1,
            maxAchievements: 4
        }, */
    {
        // Заработайте на мытье полов в офисе вашей организации
        taskId: 140,
        difficulty: 'hard',
        title: 'seasonPass.tasks.washFloor.title',
        description: 'seasonPass.tasks.washFloor.description',
        xp_reward: 3200,
        trigger_type: 'washFloor',
        trigger_amount: 1000,
        fraction: {
            ids: [1, 2, 3, 4, 6]
        }
    },
    {
        // Получите 5 уровень розыска
        taskId: 141,
        difficulty: 'medium',
        title: 'seasonPass.tasks.getMaxWanted.title',
        description: 'seasonPass.tasks.getMaxWanted.description',
        xp_reward: 800,
        trigger_type: 'getMaxWanted',
        trigger_amount: 1,
        fraction: {
            ids: activeFractions.crime
        }
    },
    {
        // Совершите ограбление бизнеса
        taskId: 142,
        difficulty: 'hard',
        title: 'seasonPass.tasks.robBusiness.title',
        description: 'seasonPass.tasks.robBusiness.description',
        xp_reward: 3200,
        trigger_type: 'robBusiness',
        trigger_amount: 10,
        requiredLevel: 2,
        fraction: {
            ids: activeFractions.crime
        }
    },
    {
        // Заработайте на сдаче нелегальных предметов
        taskId: 143,
        difficulty: 'hard',
        title: 'seasonPass.tasks.sellRobbery.title',
        description: 'seasonPass.tasks.sellRobbery.description',
        xp_reward: 3200,
        trigger_type: 'sellRobbery',
        trigger_amount: 10000,
        requiredLevel: 2,
        fraction: {
            ids: [1, 3, 7]
        }
    },
    {
        // Купите кошелёк на рынке
        taskId: 144,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyWallet.title',
        description: 'seasonPass.tasks.buyWallet.description',
        xp_reward: 400,
        trigger_type: 'buyWallet',
        trigger_amount: 1,
        blacklistItems: [473]

    },
    {
        // Обыщите игроков
        taskId: 145,
        difficulty: 'medium',
        title: 'seasonPass.tasks.searchPlayer.title',
        description: 'seasonPass.tasks.searchPlayer.description',
        xp_reward: 1600,
        trigger_type: 'searchPlayer',
        trigger_amount: 10,
        fraction: {
            ids: [1, 3, 7]
        }
    },
    {
        // Сверьте фоторобот игроков
        taskId: 146,
        difficulty: 'medium',
        title: 'seasonPass.tasks.getIdentikit.title',
        description: 'seasonPass.tasks.getIdentikit.description',
        xp_reward: 800,
        trigger_type: 'getIdentikit',
        trigger_amount: 10,
        fraction: {
            ids: [1, 3, 7]
        }
    },
    {
        // Посмотрите лицензии игроков
        taskId: 147,
        difficulty: 'medium',
        title: 'seasonPass.tasks.checkLic.title',
        description: 'seasonPass.tasks.checkLic.description',
        xp_reward: 1200,
        trigger_type: 'checkLic',
        trigger_amount: 10,
        fraction: {
            ids: [1, 3, 7]
        }
    },
    {
        // Почините автомобили ремкомплектом
        taskId: 148,
        difficulty: 'medium',
        title: 'seasonPass.tasks.repairVehicleWithKit.title',
        description: 'seasonPass.tasks.repairVehicleWithKit.description',
        xp_reward: 800,
        trigger_type: 'repairVehicleWithKit',
        trigger_amount: 10
    },
    {
        // Заправьте автомобили организации на АЗС
        taskId: 149,
        difficulty: 'medium',
        title: 'seasonPass.tasks.fuelFractionVehicle.title',
        description: 'seasonPass.tasks.fuelFractionVehicle.description',
        xp_reward: 1200,
        trigger_type: 'fuelFractionVehicle',
        trigger_amount: 5,
        fraction: {
            ids: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
        }
    },
    {
        // Объявите преступников в розыск
        taskId: 150,
        difficulty: 'medium',
        title: 'seasonPass.tasks.setWanted.title',
        description: 'seasonPass.tasks.setWanted.description',
        xp_reward: 1200,
        trigger_type: 'setWanted',
        trigger_amount: 5,
        fraction: {
            ids: [1, 3, 7],
            rank: 3
        }
    },
    {
        // Арестуйте преступников
        taskId: 151,
        difficulty: 'hard',
        title: 'seasonPass.tasks.arrestPlayer.title',
        description: 'seasonPass.tasks.arrestPlayer.description',
        xp_reward: 3200,
        trigger_type: 'arrestPlayer',
        trigger_amount: 3,
        fraction: {
            ids: [1, 3, 7],
            rank: 3
        }
    },
    {
        // Верните угнанный автомобиль владельцу
        taskId: 152,
        difficulty: 'hard',
        title: 'seasonPass.tasks.returnHijacker.title',
        description: 'seasonPass.tasks.returnHijacker.description',
        xp_reward: 3200,
        trigger_type: 'returnHijackVehicle',
        trigger_amount: 2,
        requiredLevel: 4,
        fraction: {
            ids: [1, 3, 7],
            rank: 3
        }
    },
    {
        // Изымите нелегальные предметы
        taskId: 153,
        difficulty: 'hard',
        title: 'seasonPass.tasks.seizeItems.title',
        description: 'seasonPass.tasks.seizeItems.description',
        xp_reward: 3200,
        trigger_type: 'seizeItems',
        trigger_amount: 10,
        requiredLevel: 3,
        fraction: {
            ids: [1, 3, 7],
            rank: 3
        }
    },
    {
        // Прокачайте навык стрельбы
        taskId: 154,
        difficulty: 'medium',
        title: 'seasonPass.tasks.getShootingAbility.title',
        description: 'seasonPass.tasks.getShootingAbility.description',
        xp_reward: 1000,
        trigger_type: 'getSkill',
        trigger_value: 'shooting_ability',
        trigger_amount: 10,
        maxSkill: 89,
        requiredLevel: 3,
    },
    /*     {
            // Прокачайте навык выносливость
            taskId: 155,
            difficulty: 'hard',
            title: 'seasonPass.tasks.getStamina.title',
            description: 'seasonPass.tasks.getStamina.description',
            xp_reward: 3200,
            trigger_type: 'getSkill',
            trigger_value: 'stamina',
            trigger_amount: 10,
            maxSkill: 89
        }, */
    {
        // Прокачайте навык дыхание
        taskId: 156,
        difficulty: 'hard',
        title: 'seasonPass.tasks.getLungCapacity.title',
        description: 'seasonPass.tasks.getLungCapacity.description',
        xp_reward: 3200,
        trigger_type: 'getSkill',
        trigger_value: 'lung_capacity',
        trigger_amount: 5,
        maxSkill: 94
    },
    {
        // Прокачайте навык сила
        taskId: 157,
        difficulty: 'medium',
        title: 'seasonPass.tasks.getStrength.title',
        description: 'seasonPass.tasks.getStrength.description',
        xp_reward: 1200,
        trigger_type: 'getSkill',
        trigger_value: 'strength',
        trigger_amount: 10,
        maxSkill: 89
    },
    {
        // Прокачайте навык полёт
        taskId: 158,
        difficulty: 'easy',
        title: 'seasonPass.tasks.getFlyingAbility.title',
        description: 'seasonPass.tasks.getFlyingAbility.description',
        xp_reward: 400,
        trigger_type: 'getSkill',
        trigger_value: 'flying_ability',
        trigger_amount: 5,
        requiredLevel: 3,
        maxSkill: 94,
    },
    /*     {
            // Откройте обычный кейс
            taskId: 159,
            difficulty: 'medium',
            title: 'seasonPass.tasks.openDefaultCase.title',
            description: 'seasonPass.tasks.openDefaultCase.description',
            xp_reward: 1200,
            trigger_type: 'openCase',
            trigger_value: 'default',
            trigger_amount: 1,
            minDonate: 1000,
            serverRegions: ['RU']
        }, */
    {
        // Выиграйте фишки в покер
        taskId: 160,
        difficulty: 'medium',
        title: 'seasonPass.tasks.winPoker.title',
        description: 'seasonPass.tasks.winPoker.description',
        xp_reward: 1200,
        trigger_type: 'pokerWin',
        trigger_amount: 5,
        minCash: 500000
    },
    {
        // Прокатитесь на велосипеде
        taskId: 161,
        difficulty: 'medium',
        title: 'seasonPass.tasks.mileageBikes.title',
        description: 'seasonPass.tasks.mileageBikes.description',
        xp_reward: 1000,
        trigger_type: 'mileageBikes',
        trigger_amount: 5
    },
    {
        // Купите номерной знак в Департаменте транспортных средств
        taskId: 162,
        difficulty: 'easy',
        title: 'seasonPass.tasks.buyVehicleNumberplate.title',
        description: 'seasonPass.tasks.buyVehicleNumberplate.description',
        xp_reward: 400,
        trigger_type: 'buyVehicleNumberplate',
        trigger_amount: 5
    },
    {
        // Зарегистрируйте транспорт в Департаменте транспортных средств
        taskId: 163,
        difficulty: 'easy',
        title: 'seasonPass.tasks.registerVehicle.title',
        description: 'seasonPass.tasks.registerVehicle.description',
        xp_reward: 400,
        trigger_type: 'registerVehicle',
        trigger_amount: 1
    },
    {
        // Проехать км на транспорте без номеров
        taskId: 164,
        difficulty: 'medium',
        title: 'seasonPass.tasks.mileageNoNumberplate.title',
        description: 'seasonPass.tasks.mileageNoNumberplate.description',
        xp_reward: 1200,
        trigger_type: 'mileageNoNumberplate',
        trigger_amount: 50,
        fraction: {
            ids: [0, ...activeFractions.crime]
        }
    },
    {
        // Снимите номерной знак с транспорта
        taskId: 165,
        difficulty: 'medium',
        title: 'seasonPass.tasks.removeVehicleNumberplate.title',
        description: 'seasonPass.tasks.removeVehicleNumberplate.description',
        xp_reward: 600,
        trigger_type: 'removeVehicleNumberplate',
        trigger_amount: 10
    },
    {
        // Оплатите штрафы за превышение скорости
        taskId: 166,
        difficulty: 'easy',
        requiredLevel: 3,
        title: 'seasonPass.tasks.paySpeedFine.title',
        description: 'seasonPass.tasks.paySpeedFine.description',
        xp_reward: 600,
        trigger_type: 'paySpeedFine',
        trigger_amount: 3
    },
    {
        // Доставьте эвакуированный транспорт на эвакуаторе
        taskId: 167,
        difficulty: 'medium',
        title: 'seasonPass.tasks.evacuateVehicle.title',
        description: 'seasonPass.tasks.evacuateVehicle.description',
        xp_reward: 1800,
        trigger_type: 'evacuateVehicle',
        trigger_amount: 3,
        requiredLevel: 5,
        fraction: {
            ids: [1, 3],
            rank: 3
        }
    },
    {
        // Сломайте камеру контроля скорости
        taskId: 168,
        difficulty: 'medium',
        title: 'seasonPass.tasks.breakDMVCamera.title',
        description: 'seasonPass.tasks.breakDMVCamera.description',
        xp_reward: 1800,
        requiredLevel: 3,
        trigger_type: 'breakDMVCamera',
        trigger_amount: 1,
        fraction: {
            ids: [...activeFractions.crime],
        }
    },
    ///{
    /// // Установите камеру контроля скорости
    /// taskId: 169,
    /// difficulty: 'easy',
    /// title: 'seasonPass.tasks.installDMVCamera.title',
    /// description: 'seasonPass.tasks.installDMVCamera.description',
    /// xp_reward: 800,
    /// trigger_type: 'installDMVCamera',
    /// trigger_amount: 2,
    /// fraction: {
    ///     ids: [...activeFractions.gov]
    /// }
    ///},
    {
        // Выпишите штрафы за превышение скорости используя камеры контроля скорости
        taskId: 170,
        difficulty: 'medium',
        title: 'seasonPass.tasks.giveSpeedFine.title',
        description: 'seasonPass.tasks.giveSpeedFine.description',
        xp_reward: 1200,
        trigger_type: 'giveSpeedFine',
        trigger_amount: 15,
        requiredLevel: 5,
        fraction: {
            ids: [...activeFractions.police, ...activeFractions.gov, ...activeFractions.fib],
            access: ['dmvCameras']
        }
    },
    {
        // Переоденьтесь в гардеробе организации
        taskId: 171,
        difficulty: 'easy',
        title: 'seasonPass.tasks.changeFractionClosest.title',
        description: 'seasonPass.tasks.changeFractionClosest.description',
        xp_reward: 400,
        trigger_type: 'changeFractionClosest',
        trigger_amount: 10,
        fraction: {
            ids: [...activeFractions.state, ...activeFractions.crime]
        }
    },
    ///{
    ///    // Настройте собственный прицел (F2 - настройки)
    ///    taskId: 172,
    ///    difficulty: 'easy',
    ///    title: 'seasonPass.tasks.changeScope.title',
    ///    description: 'seasonPass.tasks.changeScope.description',
    ///    xp_reward: 400,
    ///    trigger_type: 'changeScope',
    ///    trigger_amount: 1,
    ///},
    {
        // Пролетите используя автомобиль
        taskId: 173,
        difficulty: 'easy',
        title: 'seasonPass.tasks.flyOnVehicle.title',
        description: 'seasonPass.tasks.flyOnVehicle.description',
        xp_reward: 800,
        trigger_type: 'travel_distance',
        trigger_value: ['inVehicleAir'],
        trigger_amount: 1000,
    },
    {
        // Поучаствуйте в захвате воздушного груза
        taskId: 174,
        difficulty: 'medium',
        title: 'seasonPass.tasks.crateFight.title',
        description: 'seasonPass.tasks.crateFight.description',
        xp_reward: 1800,
        trigger_type: 'crateFight',
        trigger_amount: 300,
        fraction: {
            ids: [8, 9, 10, 11, 12],
        }
    },
    {
        // Укажите недействительную частоту рации
        taskId: 175,
        difficulty: 'easy',
        title: 'seasonPass.tasks.walkietalkie.title',
        description: 'seasonPass.tasks.walkietalkie.description',
        xp_reward: 400,
        trigger_type: 'walkietalkie',
        trigger_amount: 1,
    },
    ///{
    ///    // Прокатитесь на такси в качестве пассажира
    ///    taskId: 176,
    ///    difficulty: 'easy',
    ///    title: 'seasonPass.tasks.taxipassenger.title',
    ///    description: 'seasonPass.tasks.taxipassenger.description',
    ///    xp_reward: 800,
    ///    trigger_type: 'taxipassenger',
    ///    trigger_amount: 5000,
    ///},
    {
        // Заплатите комиссию при обмене фишек
        taskId: 177,
        difficulty: 'easy',
        title: 'seasonPass.tasks.chipcommission.title',
        description: 'seasonPass.tasks.chipcommission.description',
        xp_reward: 800,
        requiredLevel: 3,
        trigger_type: 'chipcommission',
        trigger_amount: 10000,
    },
    {
        // Захватите флаг в режиме "Захват флага" на Maze Bank Arena
        taskId: 178,
        difficulty: 'medium',
        title: 'seasonPass.tasks.grabtheflag.title',
        description: 'seasonPass.tasks.grabtheflag.description',
        xp_reward: 1200,
        trigger_type: 'grabtheflag',
        trigger_amount: 5,
    },
    {
        // Выполните спуск по вертикальной лестнице
        taskId: 179,
        difficulty: 'easy',
        title: 'seasonPass.tasks.descendingstairs.title',
        description: 'seasonPass.tasks.descendingstairs.description',
        xp_reward: 600,
        trigger_type: 'travel_distance',
        trigger_value: ['climbingLadder'],
        trigger_amount: 100,
    },
    {
        // Срежьте бронежилет с помощью охотничьего ножа
        taskId: 180,
        difficulty: 'medium',
        title: 'seasonPass.tasks.cutoffBulletproof.title',
        description: 'seasonPass.tasks.cutoffBulletproof.description',
        xp_reward: 1200,
        trigger_type: 'cutoffBulletproof',
        trigger_amount: 1,
        requiredLevel: 3,
        fraction: {
            ids: [8, 9, 10, 11, 12],
        }
    },
    {
        // Оставьте транспорт без топлива
        taskId: 181,
        difficulty: 'easy',
        title: 'seasonPass.tasks.withoutfuel.title',
        description: 'seasonPass.tasks.withoutfuel.description',
        xp_reward: 800,
        trigger_type: 'withoutfuel',
        trigger_amount: 2,
    },
    // {
    //     // Заполните инвентарь весом больше чем 29,9 кг
    //     taskId: 182,
    //     difficulty: 'easy',
    //     title: 'seasonPass.tasks.fillinventory.title',
    //     description: 'seasonPass.tasks.fillinventory.description',
    //     xp_reward: 400,
    //     trigger_type: 'fillinventory',
    //     trigger_amount: 1,
    // },
    {
        // Пролетите используя парашют
        taskId: 183,
        difficulty: 'easy',
        title: 'seasonPass.tasks.flyOnParachuteDist.title',
        description: 'seasonPass.tasks.flyOnParachuteDist.description',
        xp_reward: 800,
        trigger_type: 'travel_distance',
        trigger_value: ['flyOnParachuteDist'],
        requiredLevel: 3,
        trigger_amount: 2000,
    },
];

export const seasonPassIdMap = {};

export const seasonPassTasksMap = new Map();

for (const taskData of seasonPassTasks) {

    seasonPassIdMap[taskData.taskId] = taskData;

    if (!seasonPassTasksMap.has(taskData.trigger_type)) {
        seasonPassTasksMap.set(taskData.trigger_type, []);
    }

    seasonPassTasksMap.get(taskData.trigger_type).push(taskData);

    if (Array.isArray(taskData.trigger_value)) {
        for (const value of taskData.trigger_value) {
            const key = `${taskData.trigger_type}_${value}`;

            if (!seasonPassTasksMap.has(key)) {
                seasonPassTasksMap.set(key, []);
            }

            seasonPassTasksMap.get(key).push(taskData);
        }
    }
}
