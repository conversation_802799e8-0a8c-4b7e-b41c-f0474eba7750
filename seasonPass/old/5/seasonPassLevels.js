/**
 * Доступные параметры на награду.
 * @param  {String}     rewardType                      Тип награды. ('item', 'vehicle', 'vehicleSet', 'vehicleMod', 'vehicleDiscount', 'clothesDiscount', 'money', 'clothes', 'coins', 'vip', 'hidden', 'animation')
 * @param  {Object}     rewardData                      Данные награды. Они могут быть разными в зависимости от типа награды.
 * @param  {Number}     destroyXP                       Кол-во XP получаемого при распылении награды.
 * @param  {String}     color                           Цвет награды. ('gray', 'blue', 'purple', 'red', 'gold')
 */

export const seasonPassLevels = [{
    id: 1,
    free: [
        { rewardType: 'item', rewardData: { itemId: 960,  count: 0  }, destroyXP: 200, color: 'blue' }, // Энергетик
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ]
},
{
    id: 2,
    free: [
		{ rewardType: 'item', rewardData: { itemId: 727, count: 10 }, destroyXP: 160, color: 'blue' }, // (25 штук) Качественный ремонтный набор
		{ rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 400, color: 'blue' }, // Деньги
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 240, color: 'purple' }, // BIOLINK
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 160, color: 'blue' }, // Адреналин (= эпинефрин) 10-30 шт
		{ rewardType: 'item', rewardData: { itemId: 726, count: 0 }, destroyXP: 240, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
		{ rewardType: 'item', rewardData: { itemId: 732, count: 0 }, destroyXP: 160, color: 'blue' }, // Протеиновый батончик
        { rewardType: 'item', rewardData: { itemId: 89,  count: 10  }, destroyXP: 80, color: 'gray' }, // Желтая аптечка (хил 75хп)
    ]
},
{
    id: 3,
    free: [
		{ rewardType: 'item', rewardData: { itemId: 738, count: 0 }, destroyXP: 240, color: 'purple' }, // Непредсказуемый коктейль Реднека
		{ rewardType: 'item', rewardData: { itemId: 734, count: 0 }, destroyXP: 160, color: 'blue' }, // Благодарственное письмо губернатора
		{ rewardType: 'vehicleDiscount', rewardData: { amount: 5 }, destroyXP: 200, color: 'gray' },
		{ rewardType: 'clothesDiscount', rewardData: { amount: 5 }, destroyXP: 200, color: 'gray' },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 252, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 258, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 267, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 273, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 279, amount: 1 },  },
                ]
            },
            destroyXP: 250,
            color: 'blue'
        }
    ]
},
{
    id: 4,
    free: [
		{ rewardType: 'item', rewardData: { itemId: 960,  count: 0  }, destroyXP: 200, color: 'blue'    }, // Энергетик
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 725, count: 0   }, destroyXP: 400, color: 'red' }, // Ремонтный комплект для оружия
    ]
},
{
    id: 5,
    free: [
        { rewardType: 'clothes', destroyXP: 250, color: 'gray', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2210, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2239, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 250, color: 'gray', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2426, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2431, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 6,
    free: [
		{ rewardType: 'item', rewardData: { itemId: 728, count: 0 }, destroyXP: 160, color: 'blue' }, // Биодобавка 1 уровня
		{ rewardType: 'item', rewardData: { itemId: 733, count: 10  }, destroyXP: 150, color: 'blue' }, // Стайлинг ManeMaster
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
    ],
    premium: [
		{ rewardType: 'item', rewardData: { itemId: 728, count: 0 }, destroyXP: 160, color: 'blue' }, // Биодобавка 1 уровня
		{ rewardType: 'item', rewardData: { itemId: 733, count: 10  }, destroyXP: 150, color: 'blue'     }, // Стайлинг ManeMaster
        { rewardType: 'item', rewardData: { itemId: 255, count: 0 }, destroyXP: 250, color: 'gray' }, // Бронебойный пистолет
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
    ]
},
{
    id: 7,
    free: [
		{ rewardType: 'item', rewardData: { itemId: 735, count: 0 }, destroyXP: 160, color: 'blue'   }, // Странный бургер
		{ rewardType: 'item', rewardData: { itemId: 737, count: 0 }, destroyXP: 80, color: 'gray' }, // Консервированные бобы
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
    ],
    premium: [
        { euroCoins: 75, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'donk' }, destroyXP: 320,  color: 'blue' }, // Озвучка 'Удар!'
    ]
},
{
    id: 8,
    free: [
        { rewardType: 'item', rewardData: { itemId: 965, count: 0 }, destroyXP: 600, color: 'purple' }, // Указ о налоговых льготах
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 89,  count: 20  }, destroyXP: 200, color: 'blue' }, // Желтая аптечка (хил 75хп)
    ]
},
{
    id: 9,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3344, [1]: 6410 } }, destroyXP: 350, color: 'blue' },
    ],
    premium: [
		{ rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 400, color: 'blue' }, // Ключ карта Fleeca
		{ rewardType: 'item', rewardData: { itemId: 130, count: 0 }, destroyXP: 200, color: 'gray' }, // Кинжал
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
    ]
},
{
    id: 10,
    free: [
        { rewardType: 'item', rewardData: { itemId: 963,  count: 0  }, destroyXP: 100, color: 'blue' }, // Сверхтяжелый бронежилет
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 500, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2215, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2233, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 11,
    free: [
        { euroCoins: 75, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'sleep' }, destroyXP: 320,  color: 'blue' }, // Озвучка 'Спит...!'
    ],
    premium: [
		{ rewardType: 'item', rewardData: { itemId: 132, count: 0 }, destroyXP: 200, color: 'gray' }, // Складной нож
		{ rewardType: 'item', rewardData: { itemId: 736, count: 8   }, destroyXP: 80, color: 'gray' }, // Гавайская пицца
        { rewardType: 'item', rewardData: { itemId: 262, count: 0 }, destroyXP: 250, color: 'gray' }, // Пистолет Marksman
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
    ]
},
{
    id: 12,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 234, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 240, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 246, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 285, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 291, amount: 1 },  },
                ]
            },
            destroyXP: 100,
            color: 'gray'
        }
    ],
    premium: [
		{ rewardType: 'item', rewardData: { itemId: 965, count: 0 }, destroyXP: 600, color: 'purple' }, // Указ о налоговых льготах
    ]
},
{
    id: 13,
    free: [
		{ rewardType: 'item', rewardData: { itemId: 89,  count: 20  }, destroyXP: 200, color: 'blue'    }, // Желтая аптечка (хил 75хп)
    ],
    premium: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ]
},
{
    id: 14,
    free: [
		{ rewardType: 'item', rewardData: { itemId: 736, count: 8   }, destroyXP: 80, color: 'gray' }, // Гавайская пицца
		{ rewardType: 'item', rewardData: { itemId: 737, count: 0 }, destroyXP: 80, color: 'gray' }, // Консервированные бобы
		{ rewardType: 'item', rewardData: { itemId: 735, count: 0 }, destroyXP: 160, color: 'blue'   }, // Странный бургер
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 727, count: 10 }, destroyXP: 160, color: 'blue' }, // (25 штук) Качественный ремонтный набор
    ]
},
{
    id: 15,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 800, color: 'red' },
    ],
    premium: [
        { rewardType: 'clothesDiscount', rewardData: { amount: 10 }, destroyXP: 400, color: 'blue' },
		{ rewardType: 'vehicleDiscount', rewardData: { amount: 10 }, destroyXP: 400, color: 'blue' },
    ]
},
{
    id: 16,
    free: [
        { rewardType: 'item', rewardData: { itemId: 956, count: 3 }, destroyXP: 600, color: 'purple' }, // Набор самореанимации
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 400, color: 'blue' }, // Деньги
    ],
},
{
    id: 17,
    free: [
        { rewardType: 'item', rewardData: { itemId: 963,  count: 0  }, destroyXP: 100, color: 'blue' }, // Сверхтяжелый бронежилет
    ],
    premium: [
        { euroCoins: 75, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'music' }, destroyXP: 320,  color: 'blue' }, // Озвучка 'Ту-ту-ту-ту-ту-ту!'
    ],
},
{
    id: 18,
    free: [
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 160, color: 'blue' }, // Адреналин (= эпинефрин) 10-30 шт
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3347, [1]: 6413 } }, destroyXP: 350, color: 'blue' },
    ],
},
{
    id: 19,
    free: [
        { rewardType: 'item', rewardData: { itemId: 255, count: 0 }, destroyXP: 250, color: 'gray' }, // Бронебойный пистолет
    ],
    premium: [
        { euroCoins: 200, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Лето 2024
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 800, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 800, color: 'red' }, // Кейс Скины и одежда 2024
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Кейс лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
    ],
},
{
    id: 20,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'victoria', setId: 5 }, destroyXP: 1000, color: 'gold' }, // обвес Ford Crown Victoria TEST2 в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'victoria', setId: 6 }, destroyXP: 1100, color: 'gold' }, // обвес Ford Crown Victoria TEST2 в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'victoria', setId: 7 }, destroyXP: 1200, color: 'gold' }, // обвес Ford Crown Victoria TEST2 в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Кейс Лето 2024
    ]
},
{
    id: 21,
    free: [
        { rewardType: 'clothes', destroyXP: 500, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2105, textures: 10, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2126, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { euroCoins: 200, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Лето 2024
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 800, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 800, color: 'red' }, // Кейс Скины и одежда 2024
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Кейс лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
    ]
},
{
    id: 22,
    free: [
        { rewardType: 'item', rewardData: { itemId: 965, count: 0 }, destroyXP: 600, color: 'purple' }, // Указ о налоговых льготах
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 89,  count: 20  }, destroyXP: 200, color: 'blue' }, // Желтая аптечка (хил 75хп)
    ]
},
{
    id: 23,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 750, color: 'purple' }, // Кейс Лето автомобильный 2024
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 258, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 267, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 273, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 279, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 252, amount: 1 },  },
                ]
            },
            destroyXP: 250,
            color: 'blue'
        }
    ]
},
{
    id: 24,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 750, color: 'purple' }, // Кейс Скины и одежда 2024
    ],
    premium: [
		{ rewardType: 'item', rewardData: { itemId: 956, count: 3 }, destroyXP: 600, color: 'purple' }, // Набор самореанимации
    ]
},
{
    id: 25,
    free: [
		{ euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
    ],
    premium: [
        { euroCoins: 200, rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Лето 2024
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'purple' }, // Кейс лето 2023
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
        { rewardType: 'item', rewardData: { itemId: 728, count: 0 }, destroyXP: 160, color: 'blue' }, // Биодобавка 1 уровня
    ]
},
{
    id: 26,
    free: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 726, count: 0 }, destroyXP: 240, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
        { euroCoins: 200, rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Лето 2024
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'purple' }, // Кейс лето 2023
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
    ]
},
{
    id: 27,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3351, [1]: 6417 } }, destroyXP: 350, color: 'blue' },
    ],
    premium: [
		{ rewardType: 'item', rewardData: { itemId: 443, count: 0 }, destroyXP: 600, color: 'purple' }, // Дрель 1500w
    ]
},
{
    id: 28,
    free: [
        { color: 'gray', destroyXP: 250, rewardData: { id: 185 }, rewardType: 'animation' }, // Hand Signals
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 959, count: 0 }, destroyXP: 1200, color: 'red' }, // Точильный камень
    ],
},
{
    id: 29,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 240, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 246, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 285, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 291, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 234, amount: 1 },  },
                ]
            },
            destroyXP: 100,
            color: 'gray'
        }
    ],
    premium: [
        { euroCoins: 200, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Лето 2024
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 800, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 800, color: 'red' }, // Кейс Скины и одежда 2024
			]
		}, destroyXP: 750, color: 'red' },
    ],
},
{
    id: 30,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'eclipse', setId: 13 }, destroyXP: 1000, color: 'gold' }, // обвес Mitshubishi Eclipse D30 TEST4 в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'eclipse', setId: 14 }, destroyXP: 1100, color: 'gold' }, // обвес Mitshubishi Eclipse D30 TEST4 в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'eclipse', setId: 15 }, destroyXP: 1200, color: 'gold' }, // обвес Mitshubishi Eclipse D30 TEST4 в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'q60s', setId: 13 }, destroyXP: 1000, color: 'gold' }, // обвес Infiniti Q60S TEST4 в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'q60s', setId: 14 }, destroyXP: 1100, color: 'gold' }, // обвес Infiniti Q60S TEST4 в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'q60s', setId: 15 }, destroyXP: 1200, color: 'gold' }, // обвес Infiniti Q60S TEST4 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 31,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 500, color: 'red' }, // Стандартный кейс рулетки
    ],
    premium: [
		{ euroCoins: 75, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'bell' }, destroyXP: 320,  color: 'blue' }, // Озвучка 'Колокол!'
    ]
},
{
    id: 32,
    free: [
        { rewardType: 'item', rewardData: { itemId: 965, count: 0 }, destroyXP: 600, color: 'purple' }, // Указ о налоговых льготах
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 240, color: 'purple' }, // BIOLINK
        { rewardType: 'item', rewardData: { itemId: 734, count: 0 }, destroyXP: 160, color: 'blue' }, // Благодарственное письмо губернатора
		{ rewardType: 'item', rewardData: { itemId: 732, count: 0 }, destroyXP: 160, color: 'blue' }, // Протеиновый батончик
		{ rewardType: 'item', rewardData: { itemId: 960, count: 5 }, destroyXP: 600, color: 'purple' }, // Энергетик
    ],
},
{
    id: 33,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 113 }, color: 'purple', destroyXP: 750 },
    ],
    premium: [
        { euroCoins: 200, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Лето 2024
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 800, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 800, color: 'red' }, // Кейс Скины и одежда 2024
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Кейс лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
    ]
},
{
    id: 34,
    free: [
		{ rewardType: 'item', rewardData: { itemId: 735, count: 0 }, destroyXP: 160, color: 'blue'   }, // Странный бургер
		{ rewardType: 'item', rewardData: { itemId: 737, count: 0 }, destroyXP: 80, color: 'gray' }, // Консервированные бобы
		{ rewardType: 'item', rewardData: { itemId: 736, count: 8   }, destroyXP: 80, color: 'gray' }, // Гавайская пицца
		{ rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 160, color: 'blue' }, // Адреналин (= эпинефрин) 10-30 шт
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3352, [1]: 6418 } }, destroyXP: 350, color: 'blue' },
    ]
},
{
    id: 35,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 267, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 273, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 279, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 252, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 258, amount: 1 },  },
                ]
            },
            destroyXP: 250,
            color: 'blue'
        }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 500, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2427, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2422, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 36,
    free: [
        { rewardType: 'money', rewardData: { amount: 50000 }, destroyXP: 600, color: 'purple' },
    ],
    premium: [
        { color: 'gray', destroyXP: 250, rewardData: { id: 184 }, rewardType: 'animation' }, // Infinite Dab
    ],
},
{
    id: 37,
    free: [
        { euroCoins: 75, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'amogus' }, destroyXP: 600,  color: 'purple' }, // Озвучка 'АМОГУС!'
    ],
    premium: [
        { euroCoins: 200, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Лето 2024
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 800, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 800, color: 'red' }, // Кейс Скины и одежда 2024
			]
		}, destroyXP: 750, color: 'red' },
    ]
},
{
    id: 38,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tecpistol', skinId: 2 }, destroyXP: 1000, color: 'purple' }, // Skin Flowers
    ],
    premium: [
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 600, color: 'red' }, // Кейс Лето 2024
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'purple' }, // Кейс Автомобильный
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 400, color: 'blue' }, // Ключ карта Fleeca
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
    ]
},
{
    id: 39,
    free: [
        { rewardType: 'item', rewardData: { itemId: 337, count: 0 }, destroyXP: 750, color: 'purple' }, // Винтовка Marksman Mk2
    ],
    premium: [
		{ rewardType: 'item', rewardData: { itemId: 733, count: 10  }, destroyXP: 150, color: 'blue' }, // Стайлинг ManeMaster
		{ rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 160, color: 'blue' }, // Адреналин (= эпинефрин) 10-30 шт
        { rewardType: 'item', rewardData: { itemId: 738, count: 0 }, destroyXP: 240, color: 'purple' }, // Непредсказуемый коктейль Реднека
		{ rewardType: 'item', rewardData: { itemId: 729, count: 0 }, destroyXP: 240, color: 'purple' }, // Биодобавка 2 уровня
    ]
},
{
    id: 40,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'brz', setId: 18 }, destroyXP: 1000, color: 'gold' }, // обвес Subaru BRZ Rocket Bunny в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'brz', setId: 19 }, destroyXP: 1100, color: 'gold' }, // обвес Subaru BRZ Rocket Bunny в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'brz', setId: 20 }, destroyXP: 1200, color: 'gold' }, // обвес Subaru BRZ Rocket Bunny в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'carrera', setId: 10 }, destroyXP: 1000, color: 'gold' }, // обвес Porsche Carrera RWB в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'carrera', setId: 11 }, destroyXP: 1100, color: 'gold' }, // обвес Porsche Carrera RWB в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'carrera', setId: 12 }, destroyXP: 1200, color: 'gold' }, // обвес Porsche Carrera RWB в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 41,
    free: [
        { color: 'gray', destroyXP: 250, rewardData: { id: 276 }, rewardType: 'animation' }, // headbanger
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 50000 }, destroyXP: 600, color: 'purple' },
    ]
},
{
    id: 42,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 273, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 279, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 252, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 258, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 267, amount: 1 },  },
                ]
            },
            destroyXP: 250,
            color: 'blue'
        }
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_gusenberg', skinId: 7 }, destroyXP: 1000, color: 'purple' }, // Skin Flowers
    ]
},
{
    id: 43,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3353, [1]: 6419 } }, destroyXP: 350, color: 'blue' },
		{ euroCoins: 75, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'generator' }, destroyXP: 600,  color: 'purple' }, // Озвучка 'Отдаю свою энергию, как генератор!'
        { rewardType: 'item', rewardData: { itemId: 725, count: 0   }, destroyXP: 400, color: 'red' }, // Ремонтный комплект для оружия
		{ rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 240, color: 'purple' }, // BIOLINK
    ],
    premium: [
        { euroCoins: 200, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 600, color: 'red' }, // Кейс Лето 2022
    ]
},
{
    id: 44,
    free: [
        { rewardType: 'item', rewardData: { itemId: 960,  count: 5  }, destroyXP: 600, color: 'purple' }, // Энергетик
    ],
    premium: [
        { color: 'gray', destroyXP: 250, rewardData: { id: 310 }, rewardType: 'animation' }, // doom doom yes yes
    ]
},
{
    id: 45,
    free: [
        { rewardType: 'clothes', destroyXP: 500, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2211, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2235, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 's2000', setId: 6 }, destroyXP: 1000, color: 'gold' }, // обвес Honda S2000 TEST2 в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 's2000', setId: 7 }, destroyXP: 1100, color: 'gold' }, // обвес Honda S2000 TEST2 в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 's2000', setId: 8 }, destroyXP: 1200, color: 'gold' }, // обвес Honda S2000 TEST2 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 46,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3371, [1]: 6437 } }, destroyXP: 350, color: 'blue' },
    ],
    premium: [
        { euroCoins: 200, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Лето 2024
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 800, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 800, color: 'red' }, // Кейс Скины и одежда 2024
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Кейс лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
    ]
},
{
    id: 47,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 246, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 285, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 291, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 234, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 240, amount: 1 },  },
                ]
            },
            destroyXP: 100,
            color: 'gray'
        }
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tecpistol', skinId: 3 }, destroyXP: 1000, color: 'purple' }, // Skin Pirate
    ]
},
{
    id: 48,
    free: [
        { color: 'blue', destroyXP: 500, rewardData: { id: 332 }, rewardType: 'animation' }, // Jubi Slide
    ],
    premium: [
		{ euroCoins: 75, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'paris' }, destroyXP: 600,  color: 'purple' }, // Озвучка 'Я в париже!'
    ]
},
{
    id: 49,
    free: [
        { rewardType: 'item', rewardData: { itemId: 733, count: 10  }, destroyXP: 150, color: 'blue' }, // Стайлинг ManeMaster
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3358, [1]: 6424 } }, destroyXP: 350, color: 'blue' },
    ]
},
{
    id: 50,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'civic2' }, destroyXP: 4000, color: 'gold' }, // Honda Civic FK1
                    { rewardType: 'vehicleMod', rewardData: { model: 'civic2', setId: 1 }, destroyXP: 4250, color: 'gold' }, // Honda Civic FK1 в карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'civic2', setId: 2 }, destroyXP: 4500, color: 'gold' }, // Honda Civic FK1 в кованном карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'civic2', setId: 3 }, destroyXP: 4750, color: 'gold' }, // Honda Civic FK1 Restyling в антихроме
                    { rewardType: 'vehicleMod', rewardData: { model: 'civic2', setId: 4 }, destroyXP: 5000, color: 'gold' }, // Honda Civic FK1 Restyling в кованном карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'civic2', setId: 5 }, destroyXP: 5250, color: 'gold' }, // Honda Civic FK1 Restyling в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 6 }, destroyXP: 1000, color: 'gold' }, // обвес Honda Civic FK1 Type-R в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 7 }, destroyXP: 1100, color: 'gold' }, // обвес Honda Civic FK1 Type-R в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 8 }, destroyXP: 1200, color: 'gold' }, // обвес Honda Civic FK1 Type-R в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 51,
    free: [
		{ rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
    premium: [
        { color: 'blue', destroyXP: 500, rewardData: { id: 166 }, rewardType: 'animation' }, // Ma Ya Hi
    ],
},
{
    id: 52,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_gusenberg', skinId: 8 }, destroyXP: 1000, color: 'purple' }, // Skin Pirate
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2434, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2426, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 53,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3369, [1]: 6435 } }, destroyXP: 350, color: 'blue' },
		{ rewardType: 'item', rewardData: { itemId: 963,  count: 0  }, destroyXP: 100, color: 'blue'    }, // Сверхтяжелый бронежилет
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 500, color: 'purple' }, // Кейс Скины и одежда 2024
    ],
    premium: [
		{ rewardType: 'vehicleDiscount', rewardData: { amount: 15 }, destroyXP: 600, color: 'purple' },
		{ rewardType: 'clothesDiscount', rewardData: { amount: 15 }, destroyXP: 600, color: 'purple' },
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 600, color: 'purple' }, // Кейс Автомобильный
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 600, color: 'purple' }, // Кейс Автомобильный
    ]
},
{
    id: 54,
    free: [
        { color: 'blue', destroyXP: 500, rewardData: { id: 251 }, rewardType: 'animation' }, // I Declare
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 235, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 241, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 247, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 286, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 292, amount: 1 },  },
                ]
            },
            destroyXP: 500,
            color: 'purple'
        }
    ]
},
{
    id: 55,
    free: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2218, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2237, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'i8', setId: 3 }, destroyXP: 1000, color: 'gold' }, // обвес BMW i8 TEST1 в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'i8', setId: 4 }, destroyXP: 1100, color: 'gold' }, // обвес BMW i8 TEST1 в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'i8', setId: 5 }, destroyXP: 1200, color: 'gold' }, // обвес BMW i8 TEST1 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 56,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 114 }, color: 'purple', destroyXP: 750 },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 285, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 291, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 234, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 240, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 246, amount: 1 },  },
                ]
            },
            destroyXP: 100,
            color: 'gray'
        }
    ]
},
{
    id: 57,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3359, [1]: 6425 } }, destroyXP: 350, color: 'blue' },
		{ rewardType: 'item', rewardData: { itemId: 733, count: 10  }, destroyXP: 150, color: 'blue'     }, // Стайлинг ManeMaster
		{ rewardType: 'item', rewardData: { itemId: 727, count: 10 }, destroyXP: 160, color: 'blue' }, // (25 штук) Качественный ремонтный набор
        { rewardType: 'coins', rewardData: { amount: 300 }, destroyXP: 300, color: 'purple' },
    ],
    premium: [
        { euroCoins: 75, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'mood' }, destroyXP: 600,  color: 'purple' }, // Озвучка 'Диктор канала Мастерская настроения!'
    ]
},
{
    id: 58,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'weaponSkin',
                variants: [
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_gusenberg', skinId: 10 }, }, // Skin Bonus
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_gusenberg', skinId: 6  }, }, // Skin Fire
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_gusenberg', skinId: 9  }, }, // Skin Katana
                ]
            },
            destroyXP: 1250,
            color: 'red'
        }
    ],
    premium: [
        { color: 'blue', destroyXP: 500, rewardData: { id: 329 }, rewardType: 'animation' }, // Evil Plan
    ],
},
{
    id: 59,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 259, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 268, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 274, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 280, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 253, amount: 1 },  },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ],
    premium: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
},
{
    id: 60,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'bronco' }, destroyXP: 4500, color: 'gold' }, // Ford Bronco
                    { rewardType: 'vehicleMod', rewardData: { model: 'bronco', setId: 1 }, destroyXP: 4750, color: 'gold' }, // Ford Bronco в карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'bronco', setId: 2 }, destroyXP: 5000, color: 'gold' }, // Ford Bronco в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'bronco', setId: 3 }, destroyXP: 1300, color: 'gold' }, // обвес Ford Bronco Raptor
                    { rewardType: 'vehicleSet', rewardData: { model: 'bronco', setId: 4 }, destroyXP: 1400, color: 'gold' }, // обвес Ford Bronco Raptor в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'bronco', setId: 5 }, destroyXP: 1500, color: 'gold' }, // обвес Ford Bronco Raptor в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 61,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 291, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 234, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 240, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 246, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 285, amount: 1 },  },
                ]
            },
            destroyXP: 100,
            color: 'gray'
        }
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3360, [1]: 6426 } }, destroyXP: 350, color: 'blue' },
    ]
},
{
    id: 62,
    free: [
        { color: 'blue', destroyXP: 500, rewardData: { id: 316 }, rewardType: 'animation' }, // Keep Em Crispy
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'weaponSkin',
                variants: [
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tecpistol', skinId: 5 }, }, // Skin Bonus
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tecpistol', skinId: 1 }, }, // Skin Fire
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tecpistol', skinId: 4 }, }, // Skin Katana
                ]
            },
            destroyXP: 1250,
            color: 'red'
        }
    ]
},
{
    id: 63,
    free: [
        { rewardType: 'variants', destroyXP: 500, color: 'purple',
        rewardData: { type: 'clothes', variants: [
                { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2427, texture: 4, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2422, texture: 6, isProp: 0, gender: 1 } } } },
                { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 5, drawable: 2071, texture: 6, isProp: 0, gender: 0}, 1: { component: 5, drawable: 2071, texture: 4, isProp: 0, gender: 1 } } } },
                { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2434, texture: 8, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2426, texture: 8, isProp: 0, gender: 1 } } } }
            ]
        }
    }
    ],
    premium: [
        { euroCoins: 200, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Лето 2024
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 800, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 800, color: 'red' }, // Кейс Скины и одежда 2024
			]
		}, destroyXP: 750, color: 'red' },
    ]
},
{
    id: 64,
    free: [
        { euroCoins: 200, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Лето 2024
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 800, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 800, color: 'red' }, // Кейс Скины и одежда 2024
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Кейс лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'variants', destroyXP: 500, color: 'purple',
        rewardData: { type: 'clothes', variants: [
                { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 5, drawable: 2071, texture: 5, isProp: 0, gender: 0}, 1: { component: 5, drawable: 2071, texture: 2, isProp: 0, gender: 1 } } } },
                { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 0, drawable: 2089, texture: 5, isProp: 1, gender: 0}, 1: { component: 0, drawable: 2093, texture: 4, isProp: 1, gender: 1 } } } },
                { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2434, texture: 1, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2426, texture: 1, isProp: 0, gender: 1 } } } }
            ]
        }
    }
    ]
},
{
    id: 65,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 279, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 252, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 258, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 267, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 273, amount: 1 },  },
                ]
            },
            destroyXP: 250,
            color: 'blue'
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'f150', setId: 6 }, destroyXP: 1000, color: 'gold' }, // обвес Ford F-150 Raptor TEST2
                    { rewardType: 'vehicleSet', rewardData: { model: 'f150', setId: 7 }, destroyXP: 1250, color: 'gold' }, // обвес Ford F-150 Raptor TEST2 в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'f150', setId: 8 }, destroyXP: 1500, color: 'gold' }, // обвес Ford F-150 Raptor TEST2 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 66,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3366, [1]: 6432 } }, destroyXP: 350, color: 'blue' },
    ],
    premium: [
		{ rewardType: 'item', rewardData: { itemId: 956, count: 3 }, destroyXP: 600, color: 'purple' }, // Набор самореанимации
		{ rewardType: 'item', rewardData: { itemId: 729, count: 0 }, destroyXP: 240, color: 'purple' }, // Биодобавка 2 уровня
		{ rewardType: 'item', rewardData: { itemId: 726, count: 0 }, destroyXP: 240, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
		{ rewardType: 'item', rewardData: { itemId: 89,  count: 20  }, destroyXP: 200, color: 'blue' }, // Желтая аптечка (хил 75хп)
    ]
},
{
    id: 67,
    free: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2220, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2238, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2436, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2428, textures: 10, isProp: 0 } } } }
    ],
},
{
    id: 68,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tacticalrifle', skinId: 2 }, destroyXP: 1250, color: 'red' }, // Skin Flowers
    ],
    premium: [
        { euroCoins: 100, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'snipedflight' }, destroyXP: 1500,  color: 'red' }, // Озвучка 'Меня снайпнули в полёте!'
    ]
},
{
    id: 69,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 253, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 259, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 268, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 274, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 280, amount: 1 },  },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ],
    premium: [
        { color: 'blue', destroyXP: 500, rewardData: { id: 51 }, rewardType: 'animation' }, // Billy Bounce
    ]
},
{
    id: 70,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'd8' }, destroyXP: 5000, color: 'gold' }, // Donkervoort D8 GTO
                    { rewardType: 'vehicleMod', rewardData: { model: 'd8', setId: 1 }, destroyXP: 5250, color: 'gold' }, // Donkervoort D8 GTO в карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'd8', setId: 2 }, destroyXP: 5500, color: 'gold' }, // Donkervoort D8 GTO в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'k5', setId: 7 }, destroyXP: 1250, color: 'gold' }, // обвес KIA K5 Restyling в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'k5', setId: 8 }, destroyXP: 1500, color: 'gold' }, // обвес KIA K5 Restyling в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'k5', setId: 9 }, destroyXP: 1750, color: 'gold' }, // обвес KIA K5 Restyling в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 71,
    free: [
        { rewardType: 'item', rewardData: { itemId: 963,  count: 0  }, destroyXP: 100, color: 'blue' }, // Сверхтяжелый бронежилет
        { rewardType: 'item', rewardData: { itemId: 963,  count: 0  }, destroyXP: 100, color: 'blue' }, // Сверхтяжелый бронежилет
        { rewardType: 'item', rewardData: { itemId: 963,  count: 0  }, destroyXP: 100, color: 'blue' }, // Сверхтяжелый бронежилет
        { rewardType: 'item', rewardData: { itemId: 963,  count: 0  }, destroyXP: 100, color: 'blue' }, // Сверхтяжелый бронежилет
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3368, [1]: 6434 } }, destroyXP: 350, color: 'blue' },
    ]
},
{
    id: 72,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tacticalrifle', skinId: 3 }, destroyXP: 1250, color: 'red' }, // Skin Pirate
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 241, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 247, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 286, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 292, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 235, amount: 1 },  },
                ]
            },
            destroyXP: 500,
            color: 'purple'
        }
    ]
},
{
    id: 73,
    free: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2209, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2232, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2429, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2420, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 74,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3354, [1]: 6420 } }, destroyXP: 350, color: 'purple' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 730, count: 0 }, destroyXP: 400, color: 'red' }, // Биодобавка 3 уровня
        { rewardType: 'item', rewardData: { itemId: 956, count: 3 }, destroyXP: 600, color: 'purple' }, // Набор самореанимации
        { rewardType: 'item', rewardData: { itemId: 733, count: 10  }, destroyXP: 150, color: 'blue' }, // Стайлинг ManeMaster
        { rewardType: 'item', rewardData: { itemId: 727, count: 10 }, destroyXP: 160, color: 'blue' }, // (25 штук) Качественный ремонтный набор
    ]
},
{
    id: 75,
    free: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 9  }, destroyXP: 1000, color: 'gold' }, // обвес Honda Civic FK1 TEST1 в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 10 }, destroyXP: 1100, color: 'gold' }, // обвес Honda Civic FK1 TEST1 в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 11 }, destroyXP: 1200, color: 'gold' }, // обвес Honda Civic FK1 TEST1 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 76,
    free: [
        { color: 'blue', destroyXP: 500, rewardData: { id: 37 }, rewardType: 'animation' }, // Build Up
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 268, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 274, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 280, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 253, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 259, amount: 1 },  },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ],
},
{
    id: 77,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 112 }, color: 'red', destroyXP: 1000 },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 573, count: 0 }, destroyXP: 1200, color: 'red' }, // Списанный дефибриллятор
    ]
},
{
    id: 78,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'weaponSkin',
                variants: [
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tacticalrifle', skinId: 5 }, }, // Skin Bonus
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tacticalrifle', skinId: 1 }, }, // Skin Fire
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_tacticalrifle', skinId: 4 }, }, // Skin Katana
                ]
            },
            destroyXP: 1250,
            color: 'red'
        }
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3355, [1]: 6421 } }, destroyXP: 500, color: 'purple' },
    ],
},
{
    id: 79,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 247, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 286, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 292, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 235, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 241, amount: 1 },  },
                ]
            },
            destroyXP: 500,
            color: 'purple'
        }
    ],
    premium: [
        { color: 'blue', destroyXP: 500, rewardData: { id: 323 }, rewardType: 'animation' }, // Bad Guy
    ]
},
{
    id: 80,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: '5series2' }, destroyXP: 5500, color: 'gold' }, // BMW 5-Series G61
                    { rewardType: 'vehicleMod', rewardData: { model: '5series2', setId: 2 }, destroyXP: 5750, color: 'gold' }, // BMW 5-Series G61 в карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: '5series2', setId: 3 }, destroyXP: 6000, color: 'gold' }, // BMW 5-Series G61 в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: '5series2', setId: 4 }, destroyXP: 1000, color: 'gold' }, // обвес BMW 5-Series G61 M-Pro в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: '5series2', setId: 5 }, destroyXP: 1250, color: 'gold' }, // обвес BMW 5-Series G61 M-Pro в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: '5series2', setId: 6 }, destroyXP: 1500, color: 'gold' }, // обвес BMW 5-Series G61 M-Pro в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 81,
    free: [
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 600, color: 'purple' }, // Кейс Автомобильный
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 274, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 280, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 253, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 259, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 268, amount: 1 },  },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ],
},
{
    id: 82,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_assaultrifle_mk2', skinId: 14 }, destroyXP: 1250, color: 'red' }, // Skin Flowers
    ],
    premium: [
        { euroCoins: 100, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'Yeahbuddy' }, destroyXP: 1500,  color: 'red' }, // Озвучка 'Yeah buddy!'
    ],
},
{
    id: 83,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3356, [1]: 6422 } }, destroyXP: 500, color: 'purple' },
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' }, // Кейс Автомобильный Лето 2024
        { rewardType: 'item', rewardData: { itemId: 963, count: 0  }, destroyXP: 100, color: 'blue' }, // Сверхтяжелый бронежилет
        { rewardType: 'item', rewardData: { itemId: 731, count: 20 }, destroyXP: 400, color: 'red' }, // (20 штук) Капсулы восстановления
        { rewardType: 'coins', rewardData: { amount: 300 }, destroyXP: 300, color: 'purple' },
    ]
},
{
    id: 84,
    free: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2213, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2229, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 750, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2437, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2423, textures: 3, isProp: 0 } } } }
    ]
},
{
    id: 85,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 286, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 292, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 235, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 241, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 247, amount: 1 },  },
                ]
            },
            destroyXP: 500,
            color: 'purple'
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'db11', setId: 9 }, destroyXP: 1000, color: 'gold' }, // обвес Aston Martin DB11 TEST3 в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'db11', setId: 10 }, destroyXP: 1250, color: 'gold' }, // обвес Aston Martin DB11 TEST3 в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'db11', setId: 11 }, destroyXP: 1500, color: 'gold' }, // обвес Aston Martin DB11 TEST3 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 86,
    free: [
        { color: 'purple', destroyXP: 750, rewardData: { id: 346 }, rewardType: 'animation' }, // just wanna rock
    ],
    premium: [
		{ rewardType: 'item', rewardData: { itemId: 443, count: 0 }, destroyXP: 600, color: 'purple' }, // Дрель 1500w
    ]
},
{
    id: 87,
    free: [
        { rewardType: 'item', rewardData: { itemId: 625, count: 20 }, destroyXP: 200, color: 'blue' }, // Адреналин (= эпинефрин) 10-30 шт
        { euroCoins: 200, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Лето 2024
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 800, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 800, color: 'red' }, // Кейс Скины и одежда 2024
                { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 1500, color: 'gold' }, // Кейс лето 2023
                { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 500, color: 'blue' }, // Стандартный кейс рулетки
                { rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 750, color: 'purple' }, // Оружейный лето 2023
                { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'purple' }, // Лето 2022
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 1000, color: 'red' }, // Автомобильный
			]
		}, destroyXP: 750, color: 'purple' },
		{ rewardType: 'item', rewardData: { itemId: 726, count: 0 }, destroyXP: 240, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
        { rewardType: 'coins', rewardData: { amount: 300 }, destroyXP: 300, color: 'purple' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_assaultrifle_mk2', skinId: 15 }, destroyXP: 1250, color: 'red' }, // Skin Pirate
    ],
},
{
    id: 88,
    free: [
        { rewardType: 'variants', destroyXP: 500, color: 'purple',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 0, drawable: 2089, texture: 1, isProp: 1, gender: 0}, 1: { component: 0, drawable: 2093, texture: 1, isProp: 1, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2436, texture: 0, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2423, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2429, texture: 6, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2420, texture: 2, isProp: 0, gender: 1 } } } },
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'variants', destroyXP: 500, color: 'purple',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2430, texture: 0, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2429, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 0, drawable: 2089, texture: 1, isProp: 1, gender: 0}, 1: { component: 0, drawable: 2093, texture: 1, isProp: 1, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 0, drawable: 2089, texture: 1, isProp: 1, gender: 0}, 1: { component: 11, drawable: 2420, texture: 1, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ]
},
{
    id: 89,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3345, [1]: 6411 } }, destroyXP: 750, color: 'red' },
    ],
    premium: [
        { color: 'purple', destroyXP: 750, rewardData: { id: 147 }, rewardType: 'animation' }, // Orange Justice
    ]
},
{
    id: 90,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'lfa' }, destroyXP: 6000, color: 'gold' }, // Lexus LFA
                    { rewardType: 'vehicleMod', rewardData: { model: 'lfa', setId: 1 }, destroyXP: 6250, color: 'gold' }, // Lexus LFA в карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'lfa', setId: 2 }, destroyXP: 6500, color: 'gold' }, // Lexus LFA в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'countach', setId: 3 }, destroyXP: 1000, color: 'gold' }, // обвес Lamborghini Countach TEST1 в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'countach', setId: 4 }, destroyXP: 1250, color: 'gold' }, // обвес Lamborghini Countach TEST1 в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'countach', setId: 5 }, destroyXP: 1500, color: 'gold' }, // обвес Lamborghini Countach TEST1 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 91,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 280, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 253, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 259, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 268, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 274, amount: 1 },  },
                ]
            },
            destroyXP: 750,
            color: 'red'
        }
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3367, [1]: 6433 } }, destroyXP: 500, color: 'purple' },
    ]
},
{
    id: 92,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'weaponSkin',
                variants: [
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_assaultrifle_mk2', skinId: 17 }, }, // Skin Bonus
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_assaultrifle_mk2', skinId: 13 }, }, // Skin Fire
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_assaultrifle_mk2', skinId: 16 }, }, // Skin Katana
                ]
            },
            destroyXP: 1250,
            color: 'red'
        }
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 's15', setId: 22 }, destroyXP: 1500, color: 'red' }, // обвес Nissan Slivia S15 Rocket Bunny
    ]
},
{
    id: 93,
    free: [
        { rewardType: 'item', rewardData: { itemId: 958, count: 0 }, destroyXP: 600, color: 'purple' }, // Улучшенная рация
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Кейс Лето 2024
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' }, // Кейс Лето 2024
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 750, color: 'red' }, // Кейс Скины и одежда 2024
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 811, count: 0 }, destroyXP: 1200, color: 'red' }, // Свисток
    ]
},
{
    id: 94,
    free: [
        { color: 'purple', destroyXP: 750, rewardData: { id: 60 }, rewardType: 'animation' }, // in da party
    ],
    premium: [
        { euroCoins: 100, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'Tylerdisappeared' }, destroyXP: 1500,  color: 'red' }, // Озвучка 'Тайлер исчез...'
    ]
},
{
    id: 95,
    free: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 2400, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 12 }, destroyXP: 1000, color: 'gold' }, // обвес Honda Civic FK1 TEST2 в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 13 }, destroyXP: 1100, color: 'gold' }, // обвес Honda Civic FK1 TEST2 в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 14 }, destroyXP: 1200, color: 'gold' }, // обвес Honda Civic FK1 TEST2 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 96,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyrifle', skinId: 2 }, destroyXP: 1500, color: 'gold' }, // Skin Flowers
    ],
    premium: [
		{ rewardType: 'item', rewardData: { itemId: 452, count: 0 }, destroyXP: 2400, color: 'gold' }, // Дрон
    ]
},
{
    id: 97,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 116 }, color: 'red', destroyXP: 1000 },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 292, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 235, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 241, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 247, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 286, amount: 1 },  },
                ]
            },
            destroyXP: 500,
            color: 'purple'
        }
    ],
},
{
    id: 98,
    free: [
        { rewardType: 'clothes', destroyXP: 1250, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2221, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2243, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1250, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2430, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2434, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 99,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle_mk2', skinId: 15 }, destroyXP: 1250, color: 'red' }, // Skin Flowers
    ],
    premium: [
        { color: 'purple', destroyXP: 750, rewardData: { id: 309 }, rewardType: 'animation' }, // Starlit
    ]
},
{
    id: 100,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'rrs' }, destroyXP: 6500, color: 'gold' }, // Range Rover Sport L461
                    { rewardType: 'vehicleMod', rewardData: { model: 'rrs', setId: 1 }, destroyXP: 6750, color: 'gold' }, // Range Rover Sport L461 в карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'rrs', setId: 2 }, destroyXP: 7000, color: 'gold' }, // Range Rover Sport L461 в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'rrphantom', setId: 12 }, destroyXP: 1000, color: 'gold' }, // Rolls-Royce Phantom VIII Novitec
                    { rewardType: 'vehicleSet', rewardData: { model: 'rrphantom', setId: 13 }, destroyXP: 1250, color: 'gold' }, // Rolls-Royce Phantom VIII Novitec в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'rrphantom', setId: 14 }, destroyXP: 1500, color: 'gold' }, // Rolls-Royce Phantom VIII Novitec в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'rrphantom', setId: 15 }, destroyXP: 1750, color: 'gold' }, // Rolls-Royce Phantom VIII Novitec в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 101,
    free: [
        { rewardType: 'money', rewardData: { amount: 150000 }, destroyXP: 1200, color: 'red' },
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' }, // Кейс Автомобильный Лето 2024
        { euroCoins: 200, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 250, color: 'blue' }, // Кейс Лето 2022
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 400, color: 'purple' }, // Кейс лето 2023
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 959, count: 0 }, destroyXP: 1200, color: 'red' }, // Точильный камень
    ]
},
{
    id: 102,
    free: [
        { rewardType: 'clothes', destroyXP: 1250, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2104, textures: 10, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2124, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1250, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 5, drawable: 2069, textures: 10, isProp: 0 }, 1: { gender: 1, component: 5, drawable: 2068, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 103,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 254, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 260, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 269, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 275, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 281, amount: 1 },  },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle_mk2', skinId: 16 }, destroyXP: 1250, color: 'red' }, // Skin Pirate
    ]
},
{
    id: 104,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3350, [1]: 6416 } }, destroyXP: 750, color: 'red' },
		{ rewardType: 'item', rewardData: { itemId: 963,  count: 0  }, destroyXP: 100, color: 'blue'    }, // Сверхтяжелый бронежилет
        { rewardType: 'item', rewardData: { itemId: 725, count: 0   }, destroyXP: 400, color: 'red' }, // Ремонтный комплект для оружия
        { rewardType: 'item', rewardData: { itemId: 963,  count: 0  }, destroyXP: 100, color: 'blue'    }, // Сверхтяжелый бронежилет
    ],
    premium: [
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 600, color: 'red' },
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 600, color: 'red' },
       // Кейс лето 2023
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 600, color: 'red' },
    ]
},
{
    id: 105,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3346, [1]: 6412 } }, destroyXP: 750, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'emira', setId: 15 }, destroyXP: 1000, color: 'gold' }, // обвес Lotus Emira TEST5
                    { rewardType: 'vehicleSet', rewardData: { model: 'emira', setId: 16 }, destroyXP: 1250, color: 'gold' }, // обвес Lotus Emira TEST5 в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'emira', setId: 17 }, destroyXP: 1500, color: 'gold' }, // обвес Lotus Emira TEST5 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 106,
    free: [
        { rewardType: 'item', rewardData: { itemId: 967, count: 0 }, destroyXP: 2500, color: 'gold' }, // Самокат как предмет инвентаря
    ],
    premium: [
        { color: 'purple', destroyXP: 750, rewardData: { id: 36 }, rewardType: 'animation' }, // Hey Now
    ]
},
{
    id: 107,
    free: [
        { rewardType: 'clothes', destroyXP: 1250, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2212, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2230, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1250, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2428, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2421, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 108,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'weaponSkin',
                variants: [
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle_mk2', skinId: 18 }, }, // Skin Bonus
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle_mk2', skinId: 14 }, }, // Skin Fire
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle_mk2', skinId: 17 }, }, // Skin Katana
                ]
            },
            destroyXP: 1250,
            color: 'red'
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 236, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 242, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 248, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 287, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 293, amount: 1 },  },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ]
},
{
    id: 109,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3357, [1]: 6423 } }, destroyXP: 750, color: 'red' },
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 600, color: 'red' },
       // Кейс лето 2023
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 600, color: 'red' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyrifle', skinId: 3 }, destroyXP: 1500, color: 'gold' }, // Skin Pirate
    ]
},
{
    id: 110,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'air' }, destroyXP: 7000, color: 'gold' }, // Lucid Air
                    { rewardType: 'vehicleMod', rewardData: { model: 'air', setId: 2 }, destroyXP: 7250, color: 'gold' }, // Lucid Air в карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'air', setId: 3 }, destroyXP: 7500, color: 'gold' }, // Lucid Air в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'f1502', setId: 15 }, destroyXP: 1000, color: 'gold' }, // обвес Ford F-150 Raptor TEST5
                    { rewardType: 'vehicleSet', rewardData: { model: 'f1502', setId: 16 }, destroyXP: 1250, color: 'gold' }, // обвес Ford F-150 Raptor TEST5 в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'f1502', setId: 17 }, destroyXP: 1500, color: 'gold' }, // обвес Ford F-150 Raptor TEST5 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 111,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyshotgun', skinId: 18 }, destroyXP: 1250, color: 'red' }, // Skin Flowers
    ],
    premium: [
        { color: 'purple', destroyXP: 750, rewardData: { id: 216 }, rewardType: 'animation' }, // BBoom BBoom
    ]
},
{
    id: 112,
    free: [
        { rewardType: 'clothes', destroyXP: 2000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2224, textures: 10, isProp: 0 }, 1: { gender: 1, component: 1, drawable: 2133, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 2000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2439, textures: 10, isProp: 0 }, 1: { gender: 1, component: 5, drawable: 2067, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 113,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 119 }, color: 'red', destroyXP: 1000 },
    ],
    premium: [
        { euroCoins: 100, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'gojosatoruarrived' }, destroyXP: 1500,  color: 'red' }, // Озвучка 'Прибыл Годжо Сатору!'
    ]
},
{
    id: 114,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 260, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 269, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 275, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 281, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 254, amount: 1 },  },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3361, [1]: 6427 } }, destroyXP: 750, color: 'red' },
		{ rewardType: 'item', rewardData: { itemId: 333, count: 0 }, destroyXP: 600, color: 'purple' }, // Ручной пулемет Мk2
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 600, color: 'red' }, // Кейс Лето 2024
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 600, color: 'red' },
    ]
},
{
    id: 115,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' }, // Кейс Скины и одежда 2024
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        } }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 15 }, destroyXP: 1000, color: 'gold' }, // обвес Honda Civic FK1 TEST3 в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 16 }, destroyXP: 1100, color: 'gold' }, // обвес Honda Civic FK1 TEST3 в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 17 }, destroyXP: 1200, color: 'gold' }, // обвес Honda Civic FK1 TEST3 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 116,
    free: [
		{ rewardType: 'item', rewardData: { itemId: 525, count: 10 }, destroyXP: 240, color: 'purple' }, // BIOLINK
		{ rewardType: 'item', rewardData: { itemId: 956, count: 10 }, destroyXP: 600, color: 'purple' }, // Набор самореанимации
        { rewardType: 'item', rewardData: { itemId: 731, count: 20  }, destroyXP: 400, color: 'red' }, // (20 штук) Капсулы восстановления
        { rewardType: 'item', rewardData: { itemId: 573, count: 0 }, destroyXP: 1200, color: 'red' }, // Списанный дефибриллятор
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 2400, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ]
},
{
    id: 117,
    free: [
        { rewardType: 'clothes', destroyXP: 2000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2146, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2236, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 600, color: 'red' },
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 600, color: 'red' }, // Кейс Скины и одежда 2024
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 600, color: 'red' }, // Кейс Лето 2024
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 600, color: 'red' },
    ]
},
{
    id: 118,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyshotgun', skinId: 19 }, destroyXP: 1250, color: 'red' }, // Skin Pirate
    ],
    premium: [
        { color: 'purple', destroyXP: 750, rewardData: { id: 236 }, rewardType: 'animation' }, // Roll N Rock
    ]
},
{
    id: 119,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3362, [1]: 6428 } }, destroyXP: 750, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 242, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 248, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 287, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 293, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 236, amount: 1 },  },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ]
},
{
    id: 120,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'macan2' }, destroyXP: 7500, color: 'gold' }, // Porsche Macan EV
                    { rewardType: 'vehicleMod', rewardData: { model: 'macan2', setId: 1 }, destroyXP: 7750, color: 'gold' }, // Porsche Macan EV в карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'macan2', setId: 2 }, destroyXP: 8000, color: 'gold' }, // Porsche Macan EV в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'macan2', setId: 3 }, destroyXP: 1000, color: 'gold' }, // обвес Porsche Macan EV Turbo
                    { rewardType: 'vehicleSet', rewardData: { model: 'macan2', setId: 4 }, destroyXP: 1100, color: 'gold' }, // обвес Porsche Macan EV Turbo в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'macan2', setId: 5 }, destroyXP: 1200, color: 'gold' }, // обвес Porsche Macan EV Turbo в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 121,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'weaponSkin',
                variants: [
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyshotgun', skinId: 21 }, }, // Skin Bonus
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyshotgun', skinId: 17 }, }, // Skin Fire
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyshotgun', skinId: 20 }, }, // Skin Katana
                ]
            },
            destroyXP: 1250,
            color: 'red'
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 957, count: 0 }, destroyXP: 1200, color: 'red' }, // Глушилка связи
        { rewardType: 'item', rewardData: { itemId: 958, count: 0 }, destroyXP: 600, color: 'purple' }, // Улучшенная рация
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3363, [1]: 6429 } }, destroyXP: 1000, color: 'gold' },
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 600, color: 'red' }, // Кейс Лето 2024
    ]
},
{
    id: 122,
    free: [
        { rewardType: 'clothes', destroyXP: 2000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2222, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2242, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 2000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2438, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2433, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 123,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 111 }, color: 'gold', destroyXP: 1250 },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3365, [1]: 6431 } }, destroyXP: 750, color: 'red' },
    ]
},
{
    id: 124,
    free: [
        { color: 'purple', destroyXP: 750, rewardData: { id: 235 }, rewardType: 'animation' }, // The Magic Bomb
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 269, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 275, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 281, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 254, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 260, amount: 1 },  },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ]
},
{
    id: 125,
    free: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 2400, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'huracan', setId: 13 }, destroyXP: 1000, color: 'gold' }, // обвес Lamborghini Huracan Mansory в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'huracan', setId: 14 }, destroyXP: 1250, color: 'gold' }, // обвес Lamborghini Huracan Mansory в антихроме в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'huracan', setId: 15 }, destroyXP: 1500, color: 'gold' }, // обвес Lamborghini Huracan Mansory в антихроме в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 126,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'weaponSkin',
                variants: [
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyrifle', skinId: 5 }, }, // Skin Bonus
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyrifle', skinId: 1 }, }, // Skin Fire
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyrifle', skinId: 4 }, }, // Skin Katana
                ]
            },
            destroyXP: 1250,
            color: 'red'
        }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1250, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 5, drawable: 2072, textures: 10, isProp: 0 }, 1: { gender: 1, component: 5, drawable: 2072, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 127,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3370, [1]: 6436 } }, destroyXP: 750, color: 'red' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 961, count: 0 }, destroyXP: 3000, color: 'gold' }, // GPS трекер
    ]
},
{
    id: 128,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_combatmg_mk2', skinId: 2 }, destroyXP: 1500, color: 'gold' }, // Skin Flowers
    ],
    premium: [
        { color: 'red', destroyXP: 1500, rewardData: { id: 169 }, rewardType: 'animation' }, // Shanty For A Squad
    ]
},
{
    id: 129,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 248, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 287, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 293, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 236, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 242, amount: 1 },  },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 287, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 293, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 236, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 242, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 248, amount: 1 },  },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ]
},
{
    id: 130,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'evija' }, destroyXP: 8500, color: 'gold' }, // Lotus Evija
                    { rewardType: 'vehicleMod', rewardData: { model: 'evija', setId: 1 }, destroyXP: 8750, color: 'gold' }, // Lotus Evija в карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'evija', setId: 2 }, destroyXP: 9000, color: 'gold' }, // Lotus Evija в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'cybertruck' }, destroyXP: 5000, color: 'gold' }, // Tesla Cybertruck
                    { rewardType: 'vehicleMod', rewardData: { model: 'cybertruck', setId: 1 }, destroyXP: 5500, color: 'gold' }, // Tesla Cybertruck в карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'cybertruck', setId: 2 }, destroyXP: 6000, color: 'gold' }, // Tesla Cybertruck в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 131,
    free: [
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 600, color: 'red' },
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 600, color: 'red' }, // Кейс Скины и одежда 2024
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 600, color: 'red' }, // Кейс Лето 2024
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 600, color: 'red' },
    ],
    premium: [
        { euroCoins: 100, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'fiftytwo' }, destroyXP: 3000,  color: 'gold' }, // Озвучка '52'
    ]
},
{
    id: 132,
    free: [
        { color: 'red', destroyXP: 1500, rewardData: { id: 167 }, rewardType: 'animation' }, // Jug Band
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_combatmg_mk2', skinId: 3 }, destroyXP: 1500, color: 'gold' }, // Skin Pirate
    ]
},
{
    id: 133,
    free: [
		{ rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 960, color: 'red' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 20 }, destroyXP: 960, color: 'red' },
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerSkins2023' }, destroyXP: 600, color: 'red' },
        { euroCoins: 150, rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 600, color: 'red' }, // Кейс Скины и одежда 2024
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 275, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 281, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 254, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 260, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 269, amount: 1 },  },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ]
},
{
    id: 134,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 115 }, color: 'gold', destroyXP: 1250 },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 956, count: 3 }, destroyXP: 600, color: 'purple' }, // Набор самореанимации
        { rewardType: 'item', rewardData: { itemId: 959, count: 0 }, destroyXP: 1200, color: 'red' }, // Точильный камень
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' }, // Кейс Автомобильный Лето 2024
        { euroCoins: 200, rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' }, // Кейс Скины и одежда 2024
    ]
},
{
    id: 135,
    free: [
        { rewardType: 'item', rewardData: { itemId: 966, count: 0 }, destroyXP: 4000, color: 'gold' }, // Смена номера
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 18 }, destroyXP: 1000, color: 'gold' }, // обвес Honda Civic FK1 TEST4 в антихроме
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 19 }, destroyXP: 1100, color: 'gold' }, // обвес Honda Civic FK1 TEST4 в кованном карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'civic2', setId: 20 }, destroyXP: 1200, color: 'gold' }, // обвес Honda Civic FK1 TEST4 в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 136,
    free: [
        { rewardType: 'clothes', destroyXP: 1000, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2225, texture: 0, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2246, texture: 0, isProp: 0 } } } }
    ],
    premium: [
        { color: 'red', destroyXP: 1500, rewardData: { id: 347 }, rewardType: 'animation' }, // 9mm go bang
    ]
},
{
    id: 137,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'weaponSkin',
                variants: [
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_combatmg_mk2', skinId: 5 }, }, // Skin Bonus
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_combatmg_mk2', skinId: 1 }, }, // Skin Fire
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_combatmg_mk2', skinId: 4 }, }, // Skin Katana
                ]
            },
            destroyXP: 1250,
            color: 'red'
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' }, // Кейс Автомобильный Лето 2024
        { euroCoins: 200, rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' }, // Кейс Скины и одежда 2024
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' }, // Кейс Автомобильный Лето 2024
        { euroCoins: 200, rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' }, // Кейс Скины и одежда 2024
    ]
},
{
    id: 138,
    free: [
        { rewardType: 'clothes', destroyXP: 2000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2103, textures: 10, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2123, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 2000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2148, textures: 10, isProp: 0 }, 1: { gender: 1, component: 1, drawable: 2135, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 139,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 118 }, color: 'gold', destroyXP: 1250 },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3349, [1]: 6415 } }, destroyXP: 1000, color: 'gold' },
    ]
},
{
    id: 140,
    free: [
        { rewardType: 'item', rewardData: { itemId: 724, count: 0 }, destroyXP: 4000, color: 'gold' }, // Улучшенный металлоискатель
    ],
    premium: [
        { euroCoins: 100, onlyRu: true, rewardType: 'mediaSound', rewardData: { soundPath: 'hushboy' }, destroyXP: 3000,  color: 'gold' }, // Озвучка 'Yuuechka - Тише мальчик...'
    ]
},
{
    id: 141,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper_mk2', skinId: 13 }, destroyXP: 1500, color: 'gold' }, // Skin Flowers
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 281, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 254, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 260, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 269, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 275, amount: 1 },  },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ]
},
{
    id: 142,
    free: [
        { color: 'gold', destroyXP: 3000, rewardData: { id: 102 }, rewardType: 'animation' }, // Unicycle
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3364, [1]: 6430 } }, destroyXP: 1000, color: 'gold' },
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3348, [1]: 6414 } }, destroyXP: 1000, color: 'gold' },
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' }, // Кейс Автомобильный Лето 2024
        { euroCoins: 200, rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' }, // Кейс Скины и одежда 2024
    ]
},
{
    id: 143,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper_mk2', skinId: 14 }, destroyXP: 1500, color: 'gold' }, // Skin Pirate
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'weaponSkin',
                variants: [
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper_mk2', skinId: 16 }, }, // Skin Bonus
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper_mk2', skinId: 12 }, }, // Skin Fire
                    { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper_mk2', skinId: 15 }, }, // Skin Katana
                ]
            },
            destroyXP: 1250,
            color: 'red'
        }
    ]
},
{
    id: 144,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 117 }, color: 'gold', destroyXP: 1250 },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 293, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 236, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 242, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 248, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 287, amount: 1 },  },
                ]
            },
            destroyXP: 1000,
            color: 'gold'
        }
    ]
},
{
    id: 145,
    free: [
        {
    rewardType: 'variants',
    rewardData: {
        type: 'items',
        variants: [
            { rewardType: 'vehicle', rewardData: { model: 'amggt2' }, destroyXP: 8000, color: 'gold' }, // Mercedes-AMG GT C192
            { rewardType: 'vehicleMod', rewardData: { model: 'amggt2', setId: 2 }, destroyXP: 8250, color: 'gold' }, // Mercedes-AMG GT C192 в карбоне
            { rewardType: 'vehicleMod', rewardData: { model: 'amggt2', setId: 3 }, destroyXP: 8500, color: 'gold' }, // Mercedes-AMG GT C192 в кованном карбоне
        ]
    }
}
],
premium: [
        {
    rewardType: 'variants',
    rewardData: {
        type: 'items',
        variants: [
            { rewardType: 'vehicleSet', rewardData: { model: 'amggt2', setId: 4 }, destroyXP: 1000, color: 'gold' }, // обвес Mercedes-AMG GT C192 63 в антихроме
            { rewardType: 'vehicleSet', rewardData: { model: 'amggt2', setId: 5 }, destroyXP: 1250, color: 'gold' }, // обвес Mercedes-AMG GT C192 63 в карбоне
            { rewardType: 'vehicleSet', rewardData: { model: 'amggt2', setId: 6 }, destroyXP: 1500, color: 'gold' }, // обвес Mercedes-AMG GT C192 63 в кованном карбоне
        ]
    }
}
]
},
{
    id: 146,
    free: [
        { rewardType: 'item', rewardData: { itemId: 962, count: 0 }, destroyXP: 3000, color: 'gold' }, // Ремень безопасности для мотоцикла
    ],
    premium: [
        { color: 'gold', destroyXP: 3000, rewardData: { id: 101 }, rewardType: 'animation' }, // Lil Octane
    ]
},
{
    id: 147,
    free: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 25 }, destroyXP: 1280, color: 'gold' },
		{ rewardType: 'clothesDiscount', rewardData: { amount: 25 }, destroyXP: 1280, color: 'gold' },
        { rewardType: 'item', rewardData: { itemId: 957, count: 0 }, destroyXP: 1200, color: 'red' }, // Глушилка связи
        { rewardType: 'coins', rewardData: { amount: 1500 }, destroyXP: 750, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'chiron19', setId: 6 }, destroyXP: 1000, color: 'gold' }, // обвес Bugatti Chiron Profilee
                    { rewardType: 'vehicleSet', rewardData: { model: 'chiron19', setId: 7 }, destroyXP: 1250, color: 'gold' }, // обвес Bugatti Chiron Profilee в карбоне
                    { rewardType: 'vehicleSet', rewardData: { model: 'chiron19', setId: 8 }, destroyXP: 1500, color: 'gold' }, // обвес Bugatti Chiron Profilee в кованном карбоне
                ]
            }
        }
    ]
},
{
    id: 148,
    free: [
        { rewardType: 'clothes', destroyXP: 1250, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2216, textures: 10, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2240, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1250, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2433, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2432, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 149,
    free: [
        { rewardType: 'clothes', destroyXP: 2000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 5, drawable: 2068, textures: 10, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2424, textures: 10, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 2000, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 8, drawable: 2140, textures: 10, isProp: 0 }, 1: { gender: 1, component: 8, drawable: 2119, textures: 10, isProp: 0 } } } }
    ]
},
{
    id: 150,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'cc850' }, destroyXP: 9000, color: 'gold' }, // Koenisegg CC850
                    { rewardType: 'vehicleMod', rewardData: { model: 'cc850', setId: 1 }, destroyXP: 9500, color: 'gold' }, // Koenigsegg CC850 в карбоне
                    { rewardType: 'vehicleMod', rewardData: { model: 'cc850', setId: 2 }, destroyXP: 10000, color: 'gold' }, // Koenigsegg CC850 в кованном карбоне
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'r66' }, destroyXP: 10000, color: 'gold' }, // Вертолёт Roninson R66
    ]
},
{
    id: 151,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 152,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 153,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 154,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 155,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 156,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 157,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 158,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 159,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 160,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 161,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 162,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 163,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 164,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 165,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 166,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 167,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 168,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 169,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 170,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 171,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 172,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 173,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 174,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 175,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 176,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 177,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 178,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 179,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 180,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 181,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 182,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 183,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 184,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 185,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 186,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 187,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 188,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 189,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 190,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 191,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 192,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 193,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 194,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 195,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 196,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 197,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 198,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 199,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
{
    id: 200,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'summer2022' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summer2023' }, destroyXP: 750, color: 'red' },
                   // Кейс лето 2023
                    { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                    { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
            variants: [
                { rewardType: 'case', rewardData: { type: 'summer2024' }, destroyXP: 750, color: 'red' },
                { rewardType: 'case', rewardData: { type: 'summerVehicles2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'summerExtra2024' }, destroyXP: 400, color: 'purple' }, // Кейс Скины и одежда 2024
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 300, color: 'purple' },
            ]
        }
    }
    ]
},
];

export const seasonPassXP = {
    // Формула XP_START+(currentLevel*XP_STEP)
    XP_START: 300, // 1й уровень начинается с этого XP
    XP_STEP: 220, // Шаг на каждый уровень. (XP нужен для текущего уровня)+XP_STEP = следующий уровень
}

export const seasonPassgetXPForNextLevel = (currentLevel) => {
    if (currentLevel < 100) {
        return seasonPassXP.XP_START + (currentLevel * seasonPassXP.XP_STEP);
    }

    if (currentLevel >= 100 && currentLevel < 150) {
        return 15000;
    }

    return 20000;
};
