/**
 * Доступные параметры на награду.
 * @param  {String}     rewardType                      Тип награды. ('item', 'vehicle', 'vehicleSet', 'vehicleMod', 'vehicleDiscount', 'clothesDiscount', 'money', 'clothes', 'coins', 'vip', 'hidden', 'animation')
 * @param  {Object}     rewardData                      Данные награды. Они могут быть разными в зависимости от типа награды.
 * @param  {Number}     destroyXP                       Кол-во XP получаемого при распылении награды.
 * @param  {String}     color                           Цвет награды. ('gray', 'blue', 'purple', 'red', 'gold')
 */

export const seasonPassLevels = [{
    id: 1,
    free: [
        { color: 'purple',    destroyXP: 600,  rewardData: { id: 289 }, rewardType: 'animation' }, // Анимация: Take The Elf
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
    ]
},
{
    id: 2,
    free: [
        { rewardType: 'item', rewardData: { itemId: 732, count: 0 }, destroyXP: 160, color: 'blue' }, // Протеиновый батончик
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 240, color: 'purple' }, // BIOLINK
        { rewardType: 'item', rewardData: { itemId: 815, count: 0 }, destroyXP: 240, color: 'purple' }, // Волшебная чёрная конфета
        { rewardType: 'item', rewardData: { itemId: 816, count: 0 }, destroyXP: 240, color: 'purple' }, // Волшебная белая конфета
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'item', rewardData: { itemId: 725, count: 0   }, destroyXP: 400, color: 'red'     }, // Ремонтный комплект для оружия
                    { rewardType: 'item', rewardData: { itemId: 89,  count: 20  }, destroyXP: 200, color: 'blue'    }, // Желтая аптечка (хил 75хп)
                    { rewardType: 'item', rewardData: { itemId: 525, count: 0   }, destroyXP: 240, color: 'purple'  }, // BIOLINK
                    { rewardType: 'item', rewardData: { itemId: 733, count: 10  }, destroyXP: 400, color: 'red'     }, // Стайлинг ManeMaster
                    { rewardType: 'item', rewardData: { itemId: 736, count: 8   }, destroyXP: 80, color: 'gray'    }, // Гавайская пицца
                    { rewardType: 'item', rewardData: { itemId: 731, count: 20  }, destroyXP: 400, color: 'red'     }, // (20 штук) Капсулы восстановления
                ]
            }},
    ]
},
{
    id: 3,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3314, [1]: 6380 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 10 }, destroyXP: 80, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 80, color: 'gray' }, // Адреналин (= эпинефрин)
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 400, color: 'blue' },
    ]
},
{
    id: 4,
    free: [
        { rewardType: 'item', rewardData: { itemId: 736, count: 8 }, destroyXP: 80, color: 'gray' }, // Гавайская пицца
        { rewardType: 'item', rewardData: { itemId: 737, count: 0 }, destroyXP: 80, color: 'gray' }, // Консервированные бобы
        { rewardType: 'item', rewardData: { itemId: 815, count: 0 }, destroyXP: 240, color: 'purple' }, // Волшебная чёрная конфета
        { rewardType: 'item', rewardData: { itemId: 816, count: 0 }, destroyXP: 240, color: 'purple' }, // Волшебная белая конфета
    ],
    premium: [
        { rewardType: 'vip', rewardData: { type: 'Silver', days: 10 }, destroyXP: 400, color: 'blue' },
    ]
},
{
    id: 5,
    free: [
        { rewardType: 'clothes', destroyXP: 200, color: 'gray', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2200, textures: 9, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2209, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 200, color: 'gray', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2398, textures: 9, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2391, textures: 9, isProp: 0 } } } }
    ]
},
{
    id: 6,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3315, [1]: 6381 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 5 }, destroyXP: 200, color: 'gray' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 5 }, destroyXP: 200, color: 'gray' },
    ]
},
{
    id: 7,
    free: [
        { rewardType: 'item', rewardData: { itemId: 726, count: 0 }, destroyXP: 240, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
        { rewardType: 'item', rewardData: { itemId: 735, count: 0 }, destroyXP: 160, color: 'blue'   }, // Странный бургер
        { rewardType: 'item', rewardData: { itemId: 738, count: 0 }, destroyXP: 240, color: 'purple' }, // Непредсказуемый коктейль Реднека
        { rewardType: 'item', rewardData: { itemId: 732, count: 0 }, destroyXP: 160, color: 'blue'   }, // Протеиновый батончик
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 733, count: 10 }, destroyXP: 400, color: 'red' }, // Стайлинг ManeMaster
        { rewardType: 'item', rewardData: { itemId: 726, count: 0  }, destroyXP: 240, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
    ]
},
{
    id: 8,
    free: [
        { rewardType: 'clothes', destroyXP: 200, color: 'gray', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2088, textures: 9, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2097, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        { euroCoins: 150, rewardType: 'mediaSound', rewardData: { soundPath: 'feelSorryKid' }, destroyXP: 200,  color: 'blue' }, // Озвучка 'Ilichili - Ой, жалко-то как пацана'
    ]
},
{
    id: 9,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3316, [1]: 6382 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3285, [1]: 6351 } }, destroyXP: 200, color: 'gray' },
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 400, color: 'blue' }, // Стандартный кейс рулетки
        { rewardType: 'item', rewardData: { itemId: 728, count: 0 }, destroyXP: 160, color: 'blue' }, // Биодобавка 1 уровня
        { rewardType: 'item', rewardData: { itemId: 737, count: 0 }, destroyXP: 80, color: 'gray' }, // Консервированные бобы
    ]
},
{
    id: 10,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'inductor' }, destroyXP: 200, color: 'blue' }, // Велосипед
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_machinepistol', skinId: 7 }, destroyXP: 200, color: 'gray' }
    ]
},
{
    id: 11,
    free: [
        { rewardType: 'item', rewardData: { itemId: 728, count: 0 }, destroyXP: 160, color: 'blue' }, // Биодобавка 1 уровня
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 800, color: 'red' },
    ]
},
{
    id: 12,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3317, [1]: 6383 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 815, count: 3 }, destroyXP: 240, color: 'purple' }, // Волшебная чёрная конфета
    ]
},
{
    id: 13,
    free: [
        { rewardType: 'item', rewardData: { itemId: 816, count: 3 }, destroyXP: 240, color: 'purple' }, // Волшебная белая конфета
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 184, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 187, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 190, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 193, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 196, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 199, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 202, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 205, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 208, amount: 1 },  },
                ]
            },
            destroyXP: 320,
            color: 'purple'
        }
    ]
},
{
    id: 14,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3318, [1]: 6384 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 200, color: 'gray' }, // Стандартный кейс рулетки
    ]
},
{
    id: 15,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 727, count: 25 }, destroyXP: 240, color: 'purple' }, // (25 штук) Качественный ремонтный набор
        { rewardType: 'item', rewardData: { itemId: 726, count: 0  }, destroyXP: 240, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
        { rewardType: 'item', rewardData: { itemId: 737, count: 0  }, destroyXP: 80, color: 'gray'   }, // Консервированные бобы
    ]
},
{
    id: 16,
    free: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'soonBan' }, destroyXP: 320,  color: 'blue' }, // Озвучка 'Mayday - Вы скоро будете забанены!'
    ],
    premium: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 10 }, destroyXP: 400, color: 'blue' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 10 }, destroyXP: 400, color: 'blue' },
    ],
},
{
    id: 17,
    free: [
        { rewardType: 'item', rewardData: { itemId: 729, count: 0 }, destroyXP: 240, color: 'purple' }, // Биодобавка 2 уровня
        { rewardType: 'item', rewardData: { itemId: 736, count: 8 }, destroyXP: 80,  color: 'gray'   }, // Гавайская пицца
        { rewardType: 'item', rewardData: { itemId: 734, count: 0 }, destroyXP: 160, color: 'blue'   }, // Благодарственное письмо губернатора
        { rewardType: 'item', rewardData: { itemId: 735, count: 0 }, destroyXP: 160, color: 'blue'   }, // Странный бургер
    ],
    premium: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
},
{
    id: 18,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 183, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 186, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 189, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 192, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 195, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 198, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 201, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 204, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 207, amount: 1 },  },
                ]
            },
            destroyXP: 160,
            color: 'blue'
        }
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 68 }, color: 'gray', destroyXP: 200 }
    ],
},
{
    id: 19,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3319, [1]: 6385 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            }
        },
    ],
},
{
    id: 20,
    free: [
        { rewardType: 'clothes', destroyXP: 400, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2399, textures: 9, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2394, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_machinepistol', skinId: 6 }, destroyXP: 200, color: 'gray' }
    ]
},
{
    id: 21,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3320, [1]: 6386 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
    ]
},
{
    id: 22,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } },
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 200, color: 'gray' }, // Стандартный кейс рулетки
    ]
},
{
    id: 23,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 187, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 190, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 193, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 196, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 199, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 202, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 205, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 208, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 184, amount: 1 },  },
                ]
            },
            destroyXP: 320,
            color: 'purple'
        }
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ]
},
{
    id: 24,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3321, [1]: 6387 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 815, count: 0 }, destroyXP: 240, color: 'purple' }, // Волшебная чёрная конфета
        { rewardType: 'item', rewardData: { itemId: 816, count: 0 }, destroyXP: 240, color: 'purple' }, // Волшебная белая конфета
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 400, color: 'blue' }, // Ключ карта Fleeca
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'deathlife' }, destroyXP: 250,  color: 'blue' }, // Озвучка 'Elis Grin - Смерть, это всего лишь часть жизни...'

    ]
},
{
    id: 25,
    free: [
        { rewardType: 'item', rewardData: { itemId: 729, count: 0 }, destroyXP: 240, color: 'purple' }, // Биодобавка 2 уровня
    ],
    premium: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } }
    ]
},
{
    id: 26,
    free: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
    premium: [
        { color: 'red',    destroyXP: 1200,  rewardData: { id: 175 }, rewardType: 'animation' }, // Анимация: Flapper
    ]
},
{
    id: 27,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3325, [1]: 6391 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 443, count: 0 }, destroyXP: 600, color: 'purple' }, // Дрель 1500w
    ]
},
{
    id: 28,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 186, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 189, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 192, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 195, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 198, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 201, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 204, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 207, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 183, amount: 1 },  },
                ]
            },
            destroyXP: 160,
            color: 'blue'
        }
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ],
},
{
    id: 29,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } },
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'seeInCourt' }, destroyXP: 200,  color: 'blue' }, // Озвучка 'Condagar - Увидимся в суде!'
    ],
},
{
    id: 30,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3326, [1]: 6392 } }, destroyXP: 200, color: 'blue' },
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 69 }, color: 'gray', destroyXP: 200 }
    ]
},
{
    id: 31,
    free: [
        { rewardType: 'clothes', destroyXP: 400, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2401, textures: 9, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2396, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 400, color: 'blue' },
        { rewardType: 'vip', rewardData: { type: 'Silver', days: 10 }, destroyXP: 400, color: 'blue' },
    ]
},
{
    id: 32,
    free: [
        { color: 'blue',   destroyXP: 400,  rewardData: { id: 247 }, rewardType: 'animation' }, // Анимация: Ring It On
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 130, count: 0 }, destroyXP: 200, color: 'gray' }, // Кинжал
    ],
},
{
    id: 33,
    free: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 1 }, destroyXP: 200, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 1 }, destroyXP: 400, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 600, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 190, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 193, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 196, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 199, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 202, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 205, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 208, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 184, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 187, amount: 1 },  },
                ]
            },
            destroyXP: 320,
            color: 'purple'
        }
    ]
},
{
    id: 34,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3331, [1]: 6397 } }, destroyXP: 400, color: 'purple' },
    ],
    premium: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ]
},
{
    id: 35,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
    ]
},
{
    id: 36,
    free: [
        { rewardType: 'item', rewardData: { itemId: 736, count: 8 }, destroyXP: 80, color: 'gray' }, // Гавайская пицца
        { rewardType: 'item', rewardData: { itemId: 730, count: 0 }, destroyXP: 400, color: 'red' }, // Биодобавка 3 уровня
        { rewardType: 'item', rewardData: { itemId: 738, count: 0 }, destroyXP: 240, color: 'purple' }, // Непредсказуемый коктейль Реднека
        { rewardType: 'item', rewardData: { itemId: 734, count: 0 }, destroyXP: 160, color: 'blue' }, // Благодарственное письмо губернатора
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'realSorryNoLoad' }, destroyXP: 200,  color: 'blue' }, // Озвучка 'Olegha77 - Реально прости, непрогруз'
    ],
},
{
    id: 37,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ]
},
{
    id: 38,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 189, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 192, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 195, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 198, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 201, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 204, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 207, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 183, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 186, amount: 1 },  },
                ]
            },
            destroyXP: 160,
            color: 'blue'
        }
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
    ]
},
{
    id: 39,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3332, [1]: 6398 } }, destroyXP: 400, color: 'purple' },
    ],
    premium: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 15 }, destroyXP: 600, color: 'purple' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 15 }, destroyXP: 600, color: 'purple' },
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'whatWrong' }, destroyXP: 600,  color: 'purple' }, // Озвучка 'SinSet - Ой, а что случилось?'
    ]
},
{
    id: 40,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle', skinId: 12 }, destroyXP: 400, color: 'blue' }
    ]
},
{
    id: 41,
    free: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 10 }, destroyXP: 200, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 200, color: 'gray' }, // Адреналин (= эпинефрин)
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 400, color: 'blue', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2085, textures: 9, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2098, textures: 9, isProp: 0 } } } }
    ]
},
{
    id: 42,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3333, [1]: 6399 } }, destroyXP: 400, color: 'purple' },
        { rewardType: 'item', rewardData: { itemId: 815, count: 0 }, destroyXP: 240, color: 'purple' }, // Волшебная чёрная конфета
        { rewardType: 'item', rewardData: { itemId: 816, count: 0 }, destroyXP: 240, color: 'purple' }, // Волшебная белая конфета
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 132, count: 0 }, destroyXP: 200, color: 'gray' }, // Складной нож
    ]
},
{
    id: 43,
    free: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 193, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 196, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 199, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 202, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 205, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 208, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 184, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 187, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 190, amount: 1 },  },
                ]
            },
            destroyXP: 320,
            color: 'purple'
        }
    ]
},
{
    id: 44,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
    ],
    premium: [
        {euroCoins: 50,  rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
    ]
},
{
    id: 45,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } }
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 92 }, color: 'blue', destroyXP: 400 }
    ]
},
{
    id: 46,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3334, [1]: 6400 } }, destroyXP: 400, color: 'purple' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 573, count: 0 }, destroyXP: 1200, color: 'red' }, // Списанный дефибриллятор
    ]
},
{
    id: 47,
    free: [
        { rewardType: 'item', rewardData: { itemId: 730, count: 2 }, destroyXP: 400, color: 'red' }, // Биодобавка 3 уровня
        { rewardType: 'item', rewardData: { itemId: 727, count: 25 }, destroyXP: 240, color: 'purple' }, // (25 штук) Качественный ремонтный набор
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 240, color: 'purple' }, // BIOLINK
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 240, color: 'purple' }, // BIOLINK
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
    ]
},
{
    id: 48,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 192, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 195, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 198, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 201, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 204, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 207, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 183, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 186, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 189, amount: 1 },  },
                ]
            },
            destroyXP: 160,
            color: 'blue'
        }
    ],
    premium: [
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'kissedYou' }, destroyXP: 600,  color: 'purple' }, // Озвучка 'Wavee - Так бы и зацеловала тебя!'
    ]
},
{
    id: 49,
    free: [
        { rewardType: 'clothes', destroyXP: 600, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2199, textures: 9, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2208, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        { color: 'blue',   destroyXP: 400,  rewardData: { id: 294 }, rewardType: 'animation' }, // Анимация: Sparkler
    ]
},
{
    id: 50,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 600, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2393, textures: 9, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2393, textures: 9, isProp: 0 } } } }
    ]
},
{
    id: 51,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3335, [1]: 6401 } }, destroyXP: 400, color: 'purple' },
    ],
    premium: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
},
{
    id: 52,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
    ]
},
{
    id: 53,
    free: [
        { rewardType: 'item', rewardData: { itemId: 443, count: 0 }, destroyXP: 600, color: 'purple' }, // Дрель 1500w
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 196, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 199, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 202, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 205, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 208, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 184, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 187, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 190, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 193, amount: 1 },  },
                ]
            },
            destroyXP: 320,
            color: 'purple'
        }
    ]
},
{
    id: 54,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3336, [1]: 6402 } }, destroyXP: 400, color: 'purple' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 200, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 2 }, destroyXP: 400, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 600, color: 'purple' }, // Большой новогодний подарок
    ]
},
{
    id: 55,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'vehicleSet',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'aclass', setId: 7 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'aclass', setId: 8 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'aclass', setId: 9 }, },
                ]
            },
            destroyXP: 2400,
            color: 'red'
        }
    ]
},
{
    id: 56,
    free: [
        { rewardType: 'item', rewardData: { itemId: 725, count: 0 }, destroyXP: 400, color: 'red' }, // Ремонтный комплект для оружия
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 600, color: 'purple' }, // Большой новогодний подарок
    ]
},
{
    id: 57,
    free: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 30 }, destroyXP: 200, color: 'purple' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 30 }, destroyXP: 200, color: 'purple' }, // Адреналин (= эпинефрин)
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 50000 }, destroyXP: 600, color: 'purple' },
        { rewardType: 'vip', rewardData: { type: 'Gold', days: 15 }, destroyXP: 600, color: 'purple' },
        { rewardType: 'coins', rewardData: { amount: 300 }, destroyXP: 240, color: 'purple' },
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'disturb' }, destroyXP: 750,  color: 'purple' }, // Озвучка 'Sadovskyy - Василёк, не мешай пожалуйста.'
    ]
},
{
    id: 58,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 195, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 198, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 201, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 204, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 207, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 183, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 186, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 189, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 192, amount: 1 },  },
                ]
            },
            destroyXP: 160,
            color: 'blue'
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 600, color: 'purple' }, // Большой новогодний подарок
    ],
},
{
    id: 59,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3337, [1]: 6403 } }, destroyXP: 400, color: 'purple' },
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 93 }, color: 'blue', destroyXP: 400 }
    ],
},
{
    id: 60,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'm2g42' }, destroyXP: 4800, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'm2g42', setId: 1 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'm2g42', setId: 2 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'm2g42', setId: 3 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'm2g42', setId: 4 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'm2g42', setId: 5 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'variants', destroyXP: 400, color: 'purple',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2397, texture: 0, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2390, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2396, texture: 2, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2392, texture: 2, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2397, texture: 1, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2390, texture: 1, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ]
},
{
    id: 61,
    free: [
        { color: 'blue',   destroyXP: 400,  rewardData: { id: 292 }, rewardType: 'animation' }, // Анимация: Shaolin Sip
    ],
    premium: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3338, [1]: 6404 } }, destroyXP: 400, color: 'purple' },
    ]
},
{
    id: 62,
    free: [
        { rewardType: 'variants', destroyXP: 400, color: 'purple',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2397, texture: 4, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2394, texture: 6, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2401, texture: 6, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2390, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2401, texture: 8, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2394, texture: 8, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 726, count: 0 }, destroyXP: 240, color: 'purple' }, // Экспериментальная пилюля "Имморталитикс
        { rewardType: 'item', rewardData: { itemId: 525, count: 0 }, destroyXP: 240, color: 'purple' }, // BIOLINK
        { rewardType: 'item', rewardData: { itemId: 727, count: 25 }, destroyXP: 240, color: 'purple' }, // (25 штук) Качественный ремонтный набор
        { euroCoins: 100, rewardType: 'mediaSound', rewardData: { soundPath: 'boyterritory' }, destroyXP: 1500,  color: 'red' }, // Озвучка 'Alamantik - Господи мальчик, ты на моей территории!'
    ]
},
{
    id: 63,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3322, [1]: 6388 } }, destroyXP: 440, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 199, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 202, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 205, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 208, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 184, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 187, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 190, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 193, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 196, amount: 1 },  },
                ]
            },
            destroyXP: 320,
            color: 'purple'
        }
    ]
},
{
    id: 64,
    free: [
        { rewardType: 'clothes', destroyXP: 600, color: 'purple', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2090, textures: 9, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2211, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 725, count: 0 }, destroyXP: 400, color: 'red' }, // Ремонтный комплект для оружия
    ]
},
{
    id: 65,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ]
},
{
    id: 66,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_revolver', skinId: 12 }, destroyXP: 600, color: 'purple' }
    ],
    premium: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } }
    ]
},
{
    id: 67,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3323, [1]: 6389 } }, destroyXP: 440, color: 'red' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle', skinId: 13 }, destroyXP: 400, color: 'blue' }
    ],
},
{
    id: 68,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 198, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 201, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 204, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 207, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 183, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 186, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 189, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 192, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 195, amount: 1 },  },
                ]
            },
            destroyXP: 160,
            color: 'blue'
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'm2g42', setId: 6 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'm2g42', setId: 7 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'm2g42', setId: 8 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ]
},
{
    id: 69,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3324, [1]: 6390 } }, destroyXP: 440, color: 'red' },
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
        { rewardType: 'item', rewardData: { itemId: 815, count: 0 }, destroyXP: 240, color: 'purple' }, // Волшебная чёрная конфета
        { rewardType: 'item', rewardData: { itemId: 816, count: 0 }, destroyXP: 240, color: 'purple' }, // Волшебная белая конфета
    ]
},
{
    id: 70,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'mustang3' }, destroyXP: 4800, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'mustang3', setId: 1 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'mustang3', setId: 2 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'mustang3', setId: 3 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ]
},
{
    id: 71,
    free: [
        { euroCoins: 100, rewardType: 'mediaSound', rewardData: { soundPath: 'whatsSayEnjoyGame' }, destroyXP: 320,  color: 'blue' }, // Озвучка 'Olegha77 - Что я могу тебе сказать, приятной игры!'
        { rewardType: 'item', rewardData: { itemId: 731, count: 20 }, destroyXP: 400, color: 'red' }, // (20 штук) Капсулы восстановления
        { rewardType: 'item', rewardData: { itemId: 733, count: 10 }, destroyXP: 400, color: 'red' }, // Стайлинг ManeMaster
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyshotgun', skinId: 12 }, destroyXP: 600, color: 'purple' }
    ]
},
{
    id: 72,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3327, [1]: 6393 } }, destroyXP: 440, color: 'red' },
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
    ]
},
{
    id: 73,
    free: [
        { color: 'blue',   destroyXP: 400,  rewardData: { id: 296 }, rewardType: 'animation' }, // Анимация: Tangerine Juggling
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 202, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 205, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 208, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 184, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 187, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 190, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 193, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 196, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 199, amount: 1 },  },
                ]
            },
            destroyXP: 320,
            color: 'purple'
        }
    ]
},
{
    id: 74,
    free: [
        { rewardType: 'clothes', destroyXP: 1200, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 4, drawable: 2201, textures: 9, isProp: 0 }, 1: { gender: 1, component: 4, drawable: 2210, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ]
},
{
    id: 75,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3328, [1]: 6394 } }, destroyXP: 440, color: 'red' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_revolver', skinId: 11 }, destroyXP: 600, color: 'purple' }
    ]
},
{
    id: 76,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 90 }, color: 'purple', destroyXP: 600 }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1200, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2403, textures: 9, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2395, textures: 9, isProp: 0 } } } }
    ],
},
{
    id: 77,
    free: [
        { rewardType: 'money', rewardData: { amount: 150000 }, destroyXP: 1200, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'vehicleSet',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'taycan', setId: 9 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'taycan', setId: 10 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'taycan', setId: 11 }, },
                ]
            },
            destroyXP: 2400,
            color: 'red'
        }
    ]
},
{
    id: 78,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 201, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 204, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 207, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 183, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 186, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 189, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 192, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 195, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 198, amount: 1 },  },
                ]
            },
            destroyXP: 160,
            color: 'blue'
        }
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ],
},
{
    id: 79,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3329, [1]: 6395 } }, destroyXP: 440, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'm2g42', setId: 9 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'm2g42', setId: 10 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'm2g42', setId: 11 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ]
},
{
    id: 80,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'polestar' }, destroyXP: 4800, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'polestar', setId: 1 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'polestar', setId: 2 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'polestar', setId: 3 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'polestar', setId: 4 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'polestar', setId: 5 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'polestar', setId: 6 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'polestar', setId: 7 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'polestar', setId: 8 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ],
    premium: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 800, color: 'red' }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ]
},
{
    id: 81,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3330, [1]: 6396 } }, destroyXP: 440, color: 'red' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyshotgun', skinId: 11 }, destroyXP: 600, color: 'purple' }
    ],
},
{
    id: 82,
    free: [
        { color: 'purple', destroyXP: 600,  rewardData: { id: 181 }, rewardType: 'animation' }, // Анимация: Crackdown
    ],
    premium: [
        { euroCoins: 100, rewardType: 'mediaSound', rewardData: { soundPath: 'notViolateServerRules' }, destroyXP: 200,  color: 'blue' }, // Озвучка 'FILANT - Молодые люди, правила сервера не нарушать!'
        { euroCoins: 100, rewardType: 'mediaSound', rewardData: { soundPath: 'eat_apples' }, destroyXP: 600,  color: 'purple' }, // Озвучка 'Wavee - А я пока яблочки покушаю'
        { euroCoins: 100, rewardType: 'mediaSound', rewardData: { soundPath: 'Greathaha' }, destroyXP: 1200, color: 'red' }, // Озвучка 'Buster - Классно, ха-ха!'
        { euroCoins: 100, rewardType: 'mediaSound', rewardData: { soundPath: 'guilty' }, destroyXP: 1200, color: 'red' }, // Озвучка 'FrameTamer - Ну виноват, ну чё ты'

    ],
},
{
    id: 83,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 205, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 208, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 184, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 187, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 190, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 193, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 196, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 199, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 202, amount: 1 },  },
                ]
            },
            destroyXP: 320,
            color: 'purple'
        }
    ]
},
{
    id: 84,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3339, [1]: 6405 } }, destroyXP: 440, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'mustang3', setId: 4 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'mustang3', setId: 5 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'mustang3', setId: 6 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ]
},
{
    id: 85,
    free: [
        { rewardType: 'variants', destroyXP: 400, color: 'purple',
            rewardData: { type: 'clothes', variants: [
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2396, texture: 0, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2392, texture: 0, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 5, drawable: 2061, texture: 6, isProp: 0, gender: 0}, 1: { component: 5, drawable: 2056, texture: 2, isProp: 0, gender: 1 } } } },
                    { rewardType: 'clothes', rewardData: { clothesData: { 0: { component: 11, drawable: 2396, texture: 1, isProp: 0, gender: 0}, 1: { component: 11, drawable: 2392, texture: 1, isProp: 0, gender: 1 } } } }
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'vehicleSet',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'sclass3', setId: 14 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'sclass3', setId: 15 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'sclass3', setId: 16 }, },
                ]
            },
            destroyXP: 2400,
            color: 'red'
        }
    ]
},
{
    id: 86,
    free: [
        { rewardType: 'money', rewardData: { amount: 150000 }, destroyXP: 1200, color: 'red' },
        { rewardType: 'vip', rewardData: { type: 'Platinum', days: 30 }, destroyXP: 1200, color: 'red' },
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'coolbutno' }, destroyXP: 750, color: 'purple' }, // Озвучка 'Fletcher - Круто, но нет.'
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_revolver', skinId: 13 }, destroyXP: 600, color: 'purple' }
    ]
},
{
    id: 87,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3340, [1]: 6406 } }, destroyXP: 440, color: 'red' },
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1200, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 5, drawable: 2062, textures: 9, isProp: 0 }, 1: { gender: 1, component: 5, drawable: 2058, textures: 9, isProp: 0 } } } }
    ],
},
{
    id: 88,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 204, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 207, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 183, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 186, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 189, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 192, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 195, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 198, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 201, amount: 1 },  },
                ]
            },
            destroyXP: 160,
            color: 'blue'
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 9 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 10 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 11 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ]
},
{
    id: 89,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3341, [1]: 6407 } }, destroyXP: 440, color: 'red' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavyshotgun', skinId: 13 }, destroyXP: 600, color: 'purple' },
    ]
},
{
    id: 90,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'eclass4' }, destroyXP: 4800, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'eclass4', setId: 1 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'eclass4', setId: 2 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'eclass4', setId: 3 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'm2g42', setId: 12 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'm2g42', setId: 13 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'm2g42', setId: 14 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ]
},
{
    id: 91,
    free: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
    premium: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } }
    ]
},
{
    id: 92,
    free: [
        { rewardType: 'clothes', destroyXP: 1200, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2087, textures: 9, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2100, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        { color: 'purple', destroyXP: 600,  rewardData: { id: 253 }, rewardType: 'animation' }, // Анимация: Drum Major
    ]
},
{
    id: 93,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 70 }, color: 'red', destroyXP: 600 }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 208, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 184, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 187, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 190, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 193, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 196, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 199, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 202, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 205, amount: 1 },  },
                ]
            },
            destroyXP: 320,
            color: 'purple'
        }
    ]
},
{
    id: 94,
    free: [
        { rewardType: 'item', rewardData: { itemId: 333, count: 0 }, destroyXP: 600, color: 'purple' }, // Ручной пулемет Мk2
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1200, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2086, textures: 9, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2099, textures: 9, isProp: 0 } } } }
    ]
},
{
    id: 95,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_assaultrifle_mk2', skinId: 8 }, destroyXP: 800, color: 'red' },
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 91 }, color: 'purple', destroyXP: 400 },
    ]
},
{
    id: 96,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 100, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
        { euroCoins: 100, rewardType: 'mediaSound', rewardData: { soundPath: 'collectTears' }, destroyXP: 600,  color: 'purple' }, // Озвучка 'Mayday - Слёзки собирайте уважаемый'
        { euroCoins: 100, rewardType: 'mediaSound', rewardData: { soundPath: 'nononobrother' }, destroyXP: 600, color: 'purple' }, // Озвучка 'Asiv0y - Оооо всё, не не не брат, не'
    ]
},
{
    id: 97,
    free: [
        { color: 'red',    destroyXP: 1200, rewardData: { id: 290 }, rewardType: 'animation' }, // Анимация: Snow Day
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 627, count: 0 }, destroyXP: 2400, color: 'gold' }, // Дефибриллятор Mk2
    ],
},
{
    id: 98,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 207, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 183, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 186, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 189, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 192, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 195, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 198, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 201, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 204, amount: 1 },  },
                ]
            },
            destroyXP: 160,
            color: 'blue'
        }
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 71 }, color: 'red', destroyXP: 600 }
    ]
},
{
    id: 99,
    free: [
        { rewardType: 'clothes', destroyXP: 1200, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2400, textures: 9, isProp: 0 }, 1: { gender: 1, component: 5, drawable: 2059, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 1200, color: 'red', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2394, textures: 9, isProp: 0 }, 1: { gender: 1, component: 5, drawable: 2057, textures: 9, isProp: 0 } } } }
    ]
},
{
    id: 100,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'ix' }, destroyXP: 4800, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'ix', setId: 1 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'ix', setId: 2 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 12 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 13 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 14 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ]
},
{
    id: 101,
    free: [
        { euroCoins: 150, rewardType: 'mediaSound', rewardData: { soundPath: 'phahahaha' }, destroyXP: 1200, color: 'red' }, // Озвучка 'Macksos - Пха-ха-ха-ха-ха-ха'
        { euroCoins: 150, rewardType: 'mediaSound', rewardData: { soundPath: 'goodwillbad' }, destroyXP: 1200, color: 'red' }, // Озвучка 'Danila_Gorilla - Не хочешь по хорошему, будем по плохому!'
        { euroCoins: 150, rewardType: 'mediaSound', rewardData: { soundPath: 'whatbastard' }, destroyXP: 600, color: 'purple' }, // Озвучка 'Asiv0y - Оооу, что за заварушка?'

    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_assaultrifle_mk2', skinId: 7 }, destroyXP: 800, color: 'red' },
    ]
},
{
    id: 102,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_assaultrifle_mk2', skinId: 9 }, destroyXP: 800, color: 'red' },
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 150000 }, destroyXP: 1200, color: 'red' },
        { rewardType: 'vip', rewardData: { type: 'Platinum', days: 30 }, destroyXP: 1200, color: 'red' },
        { euroCoins: 100, rewardType: 'mediaSound', rewardData: { soundPath: 'donotdo' }, destroyXP: 1500, color: 'red' }, // Озвучка 'Kotovsky - Зачем делать, если можно не делать?'
    ]
},
{
    id: 103,
    free: [
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 211, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 214, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 217, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 220, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 223, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 226, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 229, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 232, amount: 1 },  },
                ]
            },
            destroyXP: 480,
            color: 'red'
        }
    ]
},
{
    id: 104,
    free: [
        { rewardType: 'item', rewardData: { itemId: 452, count: 0 }, destroyXP: 2400, color: 'gold' }, // Дрон
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
                type: 'vehicleSet',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 6 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 7 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 8 }, },
                ]
            }, destroyXP: 1600, color: 'blue' },
    ]
},
{
    id: 105,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3342, [1]: 6408 } }, destroyXP: 440, color: 'gold' },
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 79 }, color: 'red', destroyXP: 600 },
    ]
},
{
    id: 106,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 75 }, color: 'red', destroyXP: 600 }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'vehicleSet',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'm7g70', setId: 7 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'm7g70', setId: 8 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'm7g70', setId: 9 }, },
                ]
            },
            destroyXP: 3200,
            color: 'red'
        }
    ]
},
{
    id: 107,
    free: [
        { rewardType: 'clothes', destroyXP: 2400, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 6, drawable: 2089, textures: 9, isProp: 0 }, 1: { gender: 1, component: 6, drawable: 2101, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 2400, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 5, drawable: 2058, textures: 9, isProp: 0 }, 1: { gender: 1, component: 5, drawable: 2053, textures: 9, isProp: 0 } } } }
    ]
},
{
    id: 108,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 210, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 213, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 216, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 219, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 222, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 225, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 228, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 231, amount: 1 },  },
                ]
            },
            destroyXP: 240,
            color: 'purple'
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 725, count: 0 }, destroyXP: 400, color: 'red' }, // Ремонтный комплект для оружия
        { rewardType: 'item', rewardData: { itemId: 733, count: 10 }, destroyXP: 400, color: 'red' }, // Стайлинг ManeMaster
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ]
},
{
    id: 109,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 88 }, color: 'red', destroyXP: 600 }
    ],
    premium: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } }
    ]
},
{
    id: 110,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'ram2' }, destroyXP: 4800, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'ram2', setId: 1 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'ram2', setId: 2 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'ram2', setId: 3 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ],
    premium: [
        { color: 'red',    destroyXP: 1200, rewardData: { id: 291 }, rewardType: 'animation' }, // Анимация: Choice Knit
    ]
},
{
    id: 111,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 77 }, color: 'red', destroyXP: 600 },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper', skinId: 3 }, destroyXP: 800, color: 'red' },
    ]
},
{
    id: 112,
    free: [
        { rewardType: 'item', rewardData: { itemId: 627, count: 0 }, destroyXP: 2400, color: 'gold' }, // Дефибриллятор Mk2
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 89 }, color: 'red', destroyXP: 600 }
    ]
},
{
    id: 113,
    free: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 2400, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 211, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 214, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 217, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 220, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 223, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 226, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 229, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 232, amount: 1 },  },
                ]
            },
            destroyXP: 480,
            color: 'red'
        }
    ]
},
{
    id: 114,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200,  color: 'red' }, // Зима 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023' }, destroyXP: 600,   color: 'purple' }, // Зима 2023
        { rewardType: 'money', rewardData: { amount: 150000 }, destroyXP: 1200, color: 'red' },
        { rewardType: 'vip', rewardData: { type: 'Platinum', days: 30 }, destroyXP: 1200, color: 'red' },
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 2400, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 5, drawable: 2059, textures: 9, isProp: 0 }, 1: { gender: 1, component: 5, drawable: 2054, textures: 9, isProp: 0 } } } }
    ]
},
{
    id: 115,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'vehicleSet',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 's15', setId: 16 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 's15', setId: 17 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 's15', setId: 18 }, },
                ]
            },
            destroyXP: 2400,
            color: 'red'
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'vehicleSet',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 9 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 10 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 11 }, },
                ]
            },
            destroyXP: 2400,
            color: 'purple'
        }
    ]
},
{
    id: 116,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper', skinId: 2 }, destroyXP: 800, color: 'red' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 724, count: 0 }, destroyXP: 4000, color: 'gold' }, // Улучшенный металлоискатель
    ]
},
{
    id: 117,
    free: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 960, color: 'red' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 20 }, destroyXP: 960, color: 'red' },
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
        { rewardType: 'statTrack', rewardData: { amount: 1 }, destroyXP: 600, color: 'purple' }, // Счётчик Stat-Track для оружий
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 15 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 16 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 17 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 18 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 19 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 20 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ]
},
{
    id: 118,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 210, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 213, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 216, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 219, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 222, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 225, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 228, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 231, amount: 1 },  },
                ]
            },
            destroyXP: 240,
            color: 'purple'
        }
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 73 }, color: 'red', destroyXP: 600 }
    ]
},
{
    id: 119,
    free: [
        { rewardType: 'tattoo', rewardData: { tattoosData: { [0]: 3343, [1]: 6409 } }, destroyXP: 440, color: 'gold' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper', skinId: 4 }, destroyXP: 800, color: 'red' },
    ]
},
{
    id: 120,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'dbx' }, destroyXP: 4800, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'dbx', setId: 1 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'dbx', setId: 2 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'dbx', setId: 3 }, destroyXP: 6400, color: 'gold' },
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 2400, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ]
},
{
    id: 121,
    free: [
        { rewardType: 'clothes', destroyXP: 2400, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2144, textures: 9, isProp: 0 }, 1: { gender: 1, component: 1, drawable: 2130, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } }
    ]
},
{
    id: 122,
    free: [
        { color: 'gold',   destroyXP: 2400, rewardData: { id: 295 }, rewardType: 'animation' }, // Анимация: Telekinetic Cookies
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 2400, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2143, textures: 9, isProp: 0 }, 1: { gender: 1, component: 1, drawable: 2129, textures: 9, isProp: 0 } } } }
    ]
},
{
    id: 123,
    free: [
        { rewardType: 'money', rewardData: { amount: 150000 }, destroyXP: 1200, color: 'red' },
        { rewardType: 'vip', rewardData: { type: 'Platinum', days: 30 }, destroyXP: 1200, color: 'red' },
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200,  color: 'red' }, // Зима 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200,  color: 'red' }, // Зима 2024
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 211, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 214, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 217, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 220, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 223, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 226, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 229, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 232, amount: 1 },  },
                ]
            },
            destroyXP: 480,
            color: 'red'
        }
    ]
},
{
    id: 124,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 210, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 213, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 216, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 219, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 222, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 225, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 228, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 231, amount: 1 },  },
                ]
            },
            destroyXP: 240,
            color: 'purple'
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'ix', setId: 3 }, destroyXP: 5600, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'ix', setId: 4 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'ix', setId: 5 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ]
},
{
    id: 125,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle_mk2', skinId: 9 }, destroyXP: 1000, color: 'gold' },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'vehicleSet',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 12 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 13 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'polestar', setId: 14 }, },
                ]
            },
            destroyXP: 3200,
            color: 'red'
        }
    ]
},
{
    id: 126,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 211, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 214, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 217, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 220, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 223, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 226, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 229, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 232, amount: 1 },  },
                ]
            },
            destroyXP: 480,
            color: 'red'
        }
    ]
},
{
    id: 127,
    free: [
        { rewardType: 'clothes', destroyXP: 2400, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2022, textures: 9, isProp: 1 }, 1: { gender: 1, component: 1, drawable: 2023, textures: 9, isProp: 1 } } } }
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 72 }, color: 'gold', destroyXP: 680 }
    ]
},
{
    id: 128,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 210, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 213, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 216, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 219, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 222, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 225, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 228, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 231, amount: 1 },  },
                ]
            },
            destroyXP: 240,
            color: 'purple'
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'dbx', setId: 4 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'dbx', setId: 5 }, destroyXP: 6000, color: 'gold' },
                    { rewardType: 'vehicleSet', rewardData: { model: 'dbx', setId: 6 }, destroyXP: 6000, color: 'gold' },
                ]
            }
        }
    ]
},
{
    id: 129,
    free: [
        { euroCoins: 50, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
                    { rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
                    { rewardType: 'case', rewardData: { type: 'default'            }, destroyXP: 400,   color: 'blue'   }, // Стандартный кейс рулетки
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
                    { rewardType: 'case', rewardData: { type: 'vehicles'           }, destroyXP: 600,   color: 'purple' },
                ]
            } }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 2400, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2023, textures: 9, isProp: 1 }, 1: { gender: 1, component: 1, drawable: 2024, textures: 9, isProp: 1 } } } }
    ]
},
{
    id: 130,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'purosangue' }, destroyXP: 6400, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'purosangue', setId: 1 }, destroyXP: 6800, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'purosangue', setId: 2 }, destroyXP: 7200, color: 'gold' },
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 25 }, destroyXP: 1280, color: 'gold' },
        { euroCoins: 150, rewardType: 'mediaSound', rewardData: { soundPath: 'notunderstand' }, destroyXP: 3000, color: 'gold' }, // Озвучка 'Kotovsky - Шо происходит, я ничего не понимаю!'
    ]
},
{
    id: 131,
    free: [
        { rewardType: 'clothesDiscount', rewardData: { amount: 25 }, destroyXP: 1280, color: 'gold' },
        { euroCoins: 75, rewardType: 'mediaSound', rewardData: { soundPath: 'communitytoxics' }, destroyXP: 750, color: 'purple' }, // Озвучка 'Fletcher - Кхмм, комъюнити токсиков...'
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper_mk2', skinId: 7 }, destroyXP: 1000, color: 'gold' },
    ]
},
{
    id: 132,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 74 }, color: 'red', destroyXP: 600 }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 2400, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ]
},
{
    id: 133,
    free: [
        {
            euroCoins: 150,
            rewardType: 'variants',
            rewardData: {
                type: 'mediaSound',
                variants: [
                    { rewardType: 'mediaSound', rewardData: { soundPath: 'howMuch' }, destroyXP: 1200,  color: 'red' }, // Озвучка 'SinSet - Скока скока?'
                    { rewardType: 'mediaSound', rewardData: { soundPath: 'ewooomatiz' }, destroyXP: 1200, color: 'red' }, // Озвучка 'Koresh - Ewooo matiz'
                    { rewardType: 'mediaSound', rewardData: { soundPath: 'ohsogood' }, destroyXP: 2400, color: 'gold' }, // Озвучка 'LITVIN - Ой-ой-ой, какая хорошая'
                ]
            },
            destroyXP: 1200,
            color: 'red'
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 211, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 214, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 217, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 220, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 223, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 226, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 229, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 232, amount: 1 },  },
                ]
            },
            destroyXP: 480,
            color: 'red'
        }
    ]
},
{
    id: 134,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 210, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 213, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 216, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 219, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 222, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 225, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 228, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 231, amount: 1 },  },
                ]
            },
            destroyXP: 240,
            color: 'purple'
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 811, count: 0 }, destroyXP: 1200, color: 'red' }, // Свисток
    ]
},
{
    id: 135,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'vehicleSet',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'diablo', setId: 9 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'diablo', setId: 10 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'diablo', setId: 11 }, },
                ]
            },
            destroyXP: 2400,
            color: 'purple'
        }
    ],
    premium: [
        {
            euroCoins: 150,
            rewardType: 'variants',
            rewardData: {
                type: 'mediaSound',
                variants: [
                    { rewardType: 'mediaSound', rewardData: { soundPath: 'wayking' }, destroyXP: 1200, color: 'red' }, // Озвучка 'Alamantik - Дорогу королю!'
                    { rewardType: 'mediaSound', rewardData: { soundPath: 'really' }, destroyXP: 2400, color: 'gold' }, // Озвучка 'Paradeev1ch - Ээ-ээ-ээ, неужели!'
                    { rewardType: 'mediaSound', rewardData: { soundPath: 'waitwaitwait' }, destroyXP: 2400, color: 'gold' }, //Озвучка 'Koresh - Вейт вейт, вейт вейт!'
                ]
            },
            destroyXP: 2400,
            color: 'gold'
        }
    ]
},
{
    id: 136,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle_mk2', skinId: 8 }, destroyXP: 1000, color: 'gold' },
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 211, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 214, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 217, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 220, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 223, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 226, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 229, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 232, amount: 1 },  },
                ]
            },
            destroyXP: 480,
            color: 'red'
        }
    ]
},
{
    id: 137,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 2400, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2076, textures: 21, isProp: 0 }, // Кобура на 2 ноги, золотая
                    1: { gender: 1, component: 7, drawable: 2066, textures: 21, isProp: 0 }, // Кобура на 2 ноги, золотая
                }
            }
        }
    ]
},
{
    id: 138,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 210, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 213, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 216, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 219, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 222, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 225, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 228, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 231, amount: 1 },  },
                ]
            },
            destroyXP: 240,
            color: 'purple'
        }
    ],
    premium: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 78 }, color: 'gold', destroyXP: 680 }
    ]
},
{
    id: 139,
    free: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 960, color: 'red' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 20 }, destroyXP: 960, color: 'red' },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 25 }, destroyXP: 1280, color: 'gold' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 25 }, destroyXP: 1280, color: 'gold' },
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 2400, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2024, textures: 9, isProp: 1 }, 1: { gender: 1, component: 1, drawable: 2025, textures: 9, isProp: 1 } } } }
    ]
},
{
    id: 140,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'lanzador' }, destroyXP: 7200, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'lanzador', setId: 1 }, destroyXP: 7600, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'lanzador', setId: 2 }, destroyXP: 8000, color: 'gold' },
                ]
            }
        }
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'xk' }, destroyXP: 8000, color: 'gold' },
    ]
},
{
    id: 141,
    free: [
        { rewardType: 'item', rewardData: { itemId: 724, count: 0 }, destroyXP: 4000, color: 'gold' }, // Улучшенный металлоискатель
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper_mk2', skinId: 6 }, destroyXP: 1000, color: 'gold' },
    ]
},
{
    id: 142,
    free: [
        { rewardType: 'clothes', destroyXP: 2400, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 11, drawable: 2395, textures: 9, isProp: 0 }, 1: { gender: 1, component: 11, drawable: 2397, textures: 9, isProp: 0 } } } }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'vehicleSet',
                variants: [
                    { rewardType: 'vehicleSet', rewardData: { model: 'xm', setId: 3 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'xm', setId: 4 }, },
                    { rewardType: 'vehicleSet', rewardData: { model: 'xm', setId: 5 }, },
                ]
            },
            destroyXP: 2400,
            color: 'purple'
        }
    ]
},
{
    id: 143,
    free: [
        { color: 'gold',   destroyXP: 2400, rewardData: { id: 293 }, rewardType: 'animation' }, // Анимация: Lil Treat
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 185, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 188, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 191, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 194, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 197, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 200, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 203, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 206, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 209, amount: 1 },  },
                ]
            },
            destroyXP: 800,
            color: 'gold'
        }
    ]
},
{
    id: 144,
    free: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 2400, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ],
    premium: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 960, color: 'red' },
        { rewardType: 'clothesDiscount', rewardData: { amount: 20 }, destroyXP: 960, color: 'red' },
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
    ]
},
{
    id: 145,
    free: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_carbinerifle_mk2', skinId: 10 }, destroyXP: 1000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'weaponSkin', rewardData: { weaponName: 'weapon_heavysniper_mk2', skinId: 8 }, destroyXP: 1000, color: 'gold' },
    ]
},
{
    id: 146,
    free: [
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2024'         }, destroyXP: 1200,  color: 'red'    }, // Зима 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winter2023'         }, destroyXP: 600,   color: 'purple' }, // Зимний кейс 2023
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterExtra2024'    }, destroyXP: 600,   color: 'purple' }, // Новый год 2024
        { euroCoins: 50, rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 2400,  color: 'gold'   }, // Зима Автомобильный 2024
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'wheels',
                variants: [
                    { rewardType: 'wheels', rewardData: { id: 212, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 215, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 218, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 221, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 224, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 227, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 230, amount: 1 },  },
                    { rewardType: 'wheels', rewardData: { id: 233, amount: 1 },  },
                ]
            },
            destroyXP: 800,
            color: 'gold'
        }
    ]
},
{
    id: 147,
    free: [
        { rewardType: 'item', rewardData: { itemId: 812, count: 0 }, destroyXP: 2400, color: 'gold' }, // Лыжи
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 813, count: 0 }, destroyXP: 2400, color: 'gold' }, // Коньки
    ]
},
{
    id: 148,
    free: [
        { rewardType: 'item', rewardData: { itemId: 814, count: 0 }, destroyXP: 2400, color: 'gold' }, // Сани (предположительно)
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 2400, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 0, drawable: 2084, textures: 8, isProp: 1 }, 1: { gender: 1, component: 0, drawable: 2087, textures: 8, isProp: 1 } } } }
    ]
},
{
    id: 149,
    free: [
        { rewardType: 'armourSkin', rewardData: { entityName: 'light', skinId: 76 }, color: 'gold', destroyXP: 680 }
    ],
    premium: [
        { rewardType: 'clothes', destroyXP: 2400, color: 'gold', rewardData: { clothesData: { 0: { gender: 0, component: 1, drawable: 2142, textures: 8, isProp: 0 }, 1: { gender: 1, component: 1, drawable: 2128, textures: 8, isProp: 0 } } } }
    ]
},
{
    id: 150,
    free: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'daytona' }, destroyXP: 8000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'daytona', setId: 1 }, destroyXP: 8800, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'daytona', setId: 2 }, destroyXP: 9600, color: 'gold' },
                ]
            }
        }
    ],
    premium: [
        {
            rewardType: 'variants',
            rewardData: {
                type: 'items',
                variants: [
                    { rewardType: 'vehicle', rewardData: { model: 'xclass2' }, destroyXP: 8000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'xclass2', setId: 1 }, destroyXP: 8000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'xclass2', setId: 2 }, destroyXP: 9600, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'xclass2', setId: 3 }, destroyXP: 10000, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'xclass2', setId: 4 }, destroyXP: 10400, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'xclass2', setId: 5 }, destroyXP: 10800, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'xclass2', setId: 6 }, destroyXP: 11200, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'xclass2', setId: 7 }, destroyXP: 11600, color: 'gold' },
                    { rewardType: 'vehicleMod', rewardData: { model: 'xclass2', setId: 8 }, destroyXP: 12000, color: 'gold' },
                ]
            }
        }
    ]
},

{
    id: 151,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 152,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 153,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 154,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 155,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 156,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 157,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 158,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 159,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 160,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 161,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 162,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 163,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 164,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 165,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 166,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 167,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 168,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 169,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 170,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 171,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 172,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 173,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 174,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 175,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 176,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 177,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 178,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 179,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 180,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 181,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 182,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 183,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 184,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 185,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 186,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 187,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 188,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 189,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 190,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 191,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 192,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 193,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 194,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 195,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 196,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 197,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 198,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 199,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
                type: 'case',
                variants: [
                    { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                    { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                    { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                    { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
                ]
            } }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
},
{
    id: 200,
    free: [
        { euroCoins: 300, rewardType: 'variants', rewardData: {
            type: 'case',
			variants: [
                { rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
                { rewardType: 'case', rewardData: { type: 'winterVehicles2024' }, destroyXP: 400, color: 'blue' },
                { rewardType: 'case', rewardData: { type: 'winterExtra2024' }, destroyXP: 600, color: 'purple' },
                { rewardType: 'case', rewardData: { type: 'vehicles' }, destroyXP: 240, color: 'purple' },
			]
		} }
    ],
    premium: [
        { euroCoins: 300, rewardType: 'case', rewardData: { type: 'winter2024' }, destroyXP: 1200, color: 'gold' },
    ]
}
];

export const seasonPassXP = {
    // Формула XP_START+(currentLevel*XP_STEP)
    XP_START: 300, // 1й уровень начинается с этого XP
    XP_STEP: 220, // Шаг на каждый уровень. (XP нужен для текущего уровня)+XP_STEP = следующий уровень
}

export const seasonPassgetXPForNextLevel = (currentLevel) => {
    if (currentLevel < 100) {
        return seasonPassXP.XP_START + (currentLevel * seasonPassXP.XP_STEP);
    }

    if (currentLevel >= 100 && currentLevel < 150) {
        return 10000;
    }

    return 20000;
};
