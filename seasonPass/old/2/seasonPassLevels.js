/**
 * Доступные параметры на награду.
* @param  {String}     rewardType                      Тип награды. ('item', 'vehicle', 'vehicleSet', 'vehicleMod', 'vehicleDiscount', 'money', 'clothes', 'coins', 'vip', 'hidden')
 * @param  {Object}     rewardData                      Данные награды. Они могут быть разными в зависимости от типа награды.
 * @param  {Number}     destroyXP                       Кол-во XP получаемого при распылении награды.
 * @param  {String}     color                           Цвет награды. ('gray', 'blue', 'purple', 'red', 'gold')
 */
// SEASON PASS РАССЧИТАН на 150 УРОВНЕЙ. НЕ МЕНЬШЕ НЕ БОЛЬШЕ

export const seasonPassLevels = [{
    id: 1,
    free: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 250, color: 'gray' }, // Маленький подарок
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ]
},
{
    id: 2,
    free: [
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
    ],
    premium: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 250, color: 'gray' }, // Стандартный кейс рулетки
    ]
},
{
    id: 3,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 15, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 15, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 15, amount: 6 },  },
			]
		}, destroyXP: 250, color: 'gray' },
    ],
    premium: [

    ]
},
{
    id: 4,
    free: [
        { rewardType: 'vip', rewardData: { type: 'Silver', days: 10 }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 5,
    free: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 5 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 625, count: 6 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ]
},
{
    id: 6,
    free: [

    ],
    premium: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 250, color: 'gray' }, // Стандартный кейс рулетки
    ]
},
{
    id: 7,
    free: [
        { rewardType: 'item', rewardData: { itemId: 262, count: 0 }, destroyXP: 250, color: 'gray' }, // Пистолет Marksman
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 250, color: 'gray' }, // Маленький подарок
    ]
},
{
    id: 8,
    free: [
        { rewardType: 'item', rewardData: { itemId: 625, count: 4 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 625, count: 6 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ]
},
{
    id: 9,
    free: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 10 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 334, count: 20 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
    ]
},
{
    id: 10,
    free: [
        { rewardType: 'case', rewardData: { type: 'default' }, destroyXP: 250, color: 'gray' }, // Стандартный кейс рулетки
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'brioso3' }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 11,
    free: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 428, count: 1 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
    ]
},
{
    id: 12,
    free: [

    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 532, count: 1 }, destroyXP: 250, color: 'gray' },
    ]
},
{
    id: 13,
    free: [
        { rewardType: 'item', rewardData: { itemId: 334, count: 10 }, destroyXP: 250, color: 'gray' }, // ИРП Армии США
    ],
    premium: [

    ]
},
{
    id: 14,
    free: [
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 22, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 22, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 22, amount: 6 },  },
			]
		}, destroyXP: 250, color: 'gray' },
    ]
},
{
    id: 15,
    free: [
        { rewardType: 'item', rewardData: { itemId: 255, count: 0 }, destroyXP: 250, color: 'gray' }, // Бронебойный пистолет
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'ruiner4' }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 16,
    free: [
        {
            color: 'blue',
            destroyXP: 500,
            rewardData: {
                id: 159, // Низкий бесплатный
                title: 'animations.list.Snowshaker',
                animData: [
                    'majestic_animations_props_2',
                    'snowglobe'
                ],
                flag: 0,
                looped: false,
                music: 'snowglobe',
            },
            rewardType: 'animation',
        }
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 29, amount: 3 }, destroyXP: 500, color: 'blue' },
    ],
},
{
    id: 17,
    free: [

    ],
    premium: [
        { rewardType: 'vip', rewardData: { type: 'Silver', days: 10 }, destroyXP: 500, color: 'blue' },
    ],
},
{
    id: 18,
    free: [
        { rewardType: 'money', rewardData: { amount: 15000 }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
    ],
},
{
    id: 19,
    free: [
        { rewardType: 'wheels', rewardData: { id: 36, amount: 3 }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 443, count: 0 }, destroyXP: 750, color: 'purple' }, // Дрель 1500w
    ],
},
{
    id: 20,
    free: [
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'greenwood' }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 21,
    free: [
        { rewardType: 'item', rewardData: { itemId: 431, count: 0 }, destroyXP: 500, color: 'blue' },
    ],
    premium: [

    ]
},
{
    id: 22,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2267, texture: 1, isProp: 0, gender: 0 }, // Худи с рубашкой
                    1: { component: 6,  drawable: 2065, texture: 7, isProp: 0, gender: 1 }  // Высокие ботинки
                }
            }
        }
    ],
    premium: [

    ]
},
{
    id: 23,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 1,  drawable: 2006, texture: 0, isProp: 1, gender: 0 }, // Новогодние очки
                    1: { component: 1,  drawable: 2006, texture: 0, isProp: 1, gender: 1 }  // Новогодние очки
                }
            }
        }
    ],
    premium: [

    ]
},
{
    id: 24,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 4,  drawable: 2153, texture: 0, isProp: 0, gender: 0 }, // Джинсы
                    1: { component: 4,  drawable: 2159, texture: 0, isProp: 0, gender: 1 }  // Джинсы
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 4,  drawable: 2153, texture: 1, isProp: 0, gender: 0 }, // Джинсы
                    1: { component: 4,  drawable: 2159, texture: 1, isProp: 0, gender: 1 }  // Джинсы
                }
            }
        }
    ]
},
{
    id: 25,
    free: [
        { rewardType: 'wheels', rewardData: { id: 43, amount: 3 }, destroyXP: 250, color: 'gray' },
        { rewardType: 'wheels', rewardData: { id: 50, amount: 3 }, destroyXP: 250, color: 'gray' },
        { rewardType: 'wheels', rewardData: { id: 57, amount: 3 }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2267, texture: 0, isProp: 0, gender: 0 }, // Худи с рубашкой
                    1: { component: 6,  drawable: 2065, texture: 0, isProp: 0, gender: 1 }  // Высокие ботинки
                }
            }
        }
    ]
},
{
    id: 26,
    free: [

    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 10 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ]
},
{
    id: 27,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2265, texture: 1, isProp: 0, gender: 0 }, // Худи с крестиком
                    1: { component: 11, drawable: 2271, texture: 1, isProp: 0, gender: 1 }  // Худи с крестиком
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2265, texture: 0, isProp: 0, gender: 0 }, // Худи с крестиком
                    1: { component: 11, drawable: 2271, texture: 0, isProp: 0, gender: 1 }  // Худи с крестиком
                }
            }
        }
    ]
},
{
    id: 28,
    free: [

    ],
    premium: [
        { rewardType: 'coins', rewardData: { amount: 300 }, destroyXP: 300, color: 'purple' },
    ],
},
{
    id: 29,
    free: [
        {
            color: 'blue',
            destroyXP: 500,
            rewardData: {
                id: 52, // Не крутой танец, бесплатный
                title: 'animations.list.ElectroShuffle',
                animData: [
                    'majestic_animations_3',
                    'electro_shuffle'
                ],
                flag: 1,
                looped: true,
                music: 'electro_shuffle'
            },
            rewardType: 'animation',
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 337, count: 0 }, destroyXP: 750, color: 'purple' }, // Винтовка Marksman Mk2
    ],
},
{
    id: 30,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 1,  drawable: 2007, texture: 0, isProp: 1, gender: 0 }, // Новогодние очки снежинка
                    1: { component: 1,  drawable: 2007, texture: 0, isProp: 1, gender: 1 }  // Новогодние очки снежинка
                }
            }
        }
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'sentinel4' }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 31,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2268, texture: 0, isProp: 0, gender: 0 }, // Свитшот Puma
                    1: { component: 6,  drawable: 2066, texture: 1, isProp: 0, gender: 1 }  // Замшевые ботинки
                }
            }
        }
    ],
    premium: [
        {
            color: 'red',
            destroyXP: 1000,
            rewardData: {
                id: 171,
                title: 'animations.list.SlalomStyle',
                animData: ['majestic_animations_props_2', 'slalom_player'],
                flag: 33,
                looped: true,
                music: 'slalom'
            },
            rewardType: 'animation',
        }
    ]
},
{
    id: 32,
    free: [
        { rewardType: 'item', rewardData: { itemId: 130, count: 0 }, destroyXP: 250, color: 'gray' }, // Кинжал
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 500, color: 'blue' },
    ],
},
{
    id: 33,
    free: [
        { rewardType: 'coins', rewardData: { amount: 100 }, destroyXP: 100, color: 'blue' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 0,  drawable: 2046, texture: 5, isProp: 1, gender: 0 }, // Наушники
                    1: { component: 0,  drawable: 2055, texture: 5, isProp: 1, gender: 1 }  // Наушники
                }
            }
        }
    ]
},
{
    id: 34,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 6,  drawable: 2060, texture: 0, isProp: 0, gender: 0 }, // Уги с кроликом
                    1: { component: 6,  drawable: 2067, texture: 0, isProp: 0, gender: 1 }  // Уги с кроликом
                }
            }
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 1 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 1 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ]
},
{
    id: 35,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'weevil2' }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'vigero2' }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 36,
    free: [
        { rewardType: 'wheels', rewardData: { id: 64, amount: 3 }, destroyXP: 250, color: 'gray' },
        { rewardType: 'wheels', rewardData: { id: 71, amount: 3 }, destroyXP: 250, color: 'gray' },
        { rewardType: 'wheels', rewardData: { id: 16, amount: 3 }, destroyXP: 500, color: 'blue' },
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3193,
                    [1]: 6257,
                }
            },
            destroyXP: 250,
            color: 'gray'
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
        { rewardType: 'item', rewardData: { itemId: 444, count: 0 }, destroyXP: 500, color: 'blue' }, // Ключ карта Fleeca
    ],
},
{
    id: 37,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 0,  drawable: 2045, texture: 0, isProp: 1, gender: 0 }, // Шапка
                    1: { component: 0,  drawable: 2054, texture: 0, isProp: 1, gender: 1 }  // Шапка
                }
            }
        }
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'tattoo',
            variants: [
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3194,
                            [1]: 6261,
                        }
                    },
                    destroyXP: 250,
                    color: 'gray'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3195,
                            [1]: 6260,
                        }
                    },
                    destroyXP: 250,
                    color: 'gray'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3198,
                            [1]: 6269,
                        }
                    },
                    destroyXP: 750,
                    color: 'purple'
                }
            ]
        }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 38,
    free: [
        { rewardType: 'vip', rewardData: { type: 'Gold', days: 15 }, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3191,
                    [1]: 6255,
                }
            },
            destroyXP: 250,
            color: 'gray'
        }
    ]
},
{
    id: 39,
    free: [
        {
            color: 'blue',
            destroyXP: 500,
            rewardData: {
                id: 56, // Не крутой танец, бесплатный/премиум низкий лвл
                title: 'animations.list.Vivacious',
                animData: [
                    'majestic_animations_4',
                    'vivacious'
                ],
                flag: 1,
                looped: true,
                music: 'vivacious'
            },
            rewardType: 'animation',
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2259, texture: 4, isProp: 0, gender: 0 }, // Свитер Фред Пери
                    1: { component: 11, drawable: 2270, texture: 0, isProp: 0, gender: 1 }  // Жилет Moncler
                }
            }
        }
    ]
},
{
    id: 40,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'm3e46' }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'm3e46', setId: 4 }, destroyXP: 1000, color: 'purple' },
    ]
},
{
    id: 41,
    free: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 5 }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2268, texture: 6, isProp: 0, gender: 0 }, // Свитшот Puma
                    1: { component: 6,  drawable: 2066, texture: 5, isProp: 0, gender: 1 }  // Замшевые ботинки
                }
            }
        },
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 30, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 30, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 30, amount: 6 },  },
			]
		}, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 42,
    free: [
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 500, color: 'blue' },
        { rewardType: 'item', rewardData: { itemId: 89, count: 10 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'tattoo',
            variants: [
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3203,
                            [1]: 6251,
                        }
                    },
                    destroyXP: 250,
                    color: 'gray'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3204,
                            [1]: 6271,
                        }
                    },
                    destroyXP: 750,
                    color: 'purple'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3205,
                            [1]: 6273,
                        }
                    },
                    destroyXP: 750,
                    color: 'purple'
                }
            ]
        }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 43,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2266, texture: 0, isProp: 0, gender: 0 }, // Худи в духе Burbery
                    1: { component: 11, drawable: 2272, texture: 0, isProp: 0, gender: 1 }  // Худи в духе Burbery
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 0,  drawable: 2042, texture: 0, isProp: 1, gender: 0 }, // Горнолыжные очки на голову
                    1: { component: 0,  drawable: 2050, texture: 0, isProp: 1, gender: 1 }  // Горнолыжные очки на голову
                }
            }
        }
    ]
},
{
    id: 44,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 0,  drawable: 2046, texture: 4, isProp: 1, gender: 0 }, // Наушники
                    1: { component: 0,  drawable: 2055, texture: 4, isProp: 1, gender: 1 }  // Наушники
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2263, texture: 0, isProp: 0, gender: 0 }, // Худи Dolce & Gabbana
                    1: { component: 6,  drawable: 2064, texture: 5, isProp: 0, gender: 1 }  // Глянцевые ботинки
                }
            }
        }
    ]
},
{
    id: 45,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 0,  drawable: 2032, texture: 0, isProp: 1, gender: 0 }, // Рога оленя (Lum)
                    1: { component: 0,  drawable: 2039, texture: 0, isProp: 1, gender: 1 }  // Рога оленя (Lum)
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 0,  drawable: 2045, texture: 9, isProp: 1, gender: 0 }, // Шапка
                    1: { component: 0,  drawable: 2054, texture: 9, isProp: 1, gender: 1 }  // Шапка
                }
            }
        }
    ]
},
{
    id: 46,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2259, texture: 2, isProp: 0, gender: 0 }, // Свитер Фред Пери
                    1: { component: 11, drawable: 2270, texture: 2, isProp: 0, gender: 1 }  // Жилет Moncler
                }
            }
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 2 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ]
},
{
    id: 47,
    free: [
        {
            color: 'blue',
            destroyXP: 500,
            rewardData: {
                id: 54, // Не крутой танец, бесплатный
                title: 'animations.list.Zany',
                animData: [
                    'majestic_animations_2',
                    'zany'
                ],
                flag: 1,
                looped: true,
                music: 'zany'
            },
            rewardType: 'animation',
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 0,  drawable: 2040, texture: 0, isProp: 1, gender: 0 }, // Новогодний монокль
                    1: { component: 0,  drawable: 2047, texture: 0, isProp: 1, gender: 1 }  // Новогодний монокль
                }
            }
        },
        { rewardType: 'wheels', rewardData: { id: 37, amount: 3 }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 48,
    free: [
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3189,
                    [1]: 6252,
                }
            },
            destroyXP: 250,
                        color: 'gray'
        },
        { rewardType: 'item', rewardData: { itemId: 89, count: 10 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 6,  drawable: 2060, texture: 5, isProp: 0, gender: 0 }, // Уги с кроликом
                    1: { component: 6,  drawable: 2067, texture: 5, isProp: 0, gender: 1 }  // Уги с кроликом
                }
            }
        }
    ]
},
{
    id: 49,
    free: [
        { rewardType: 'item', rewardData: { itemId: 132, count: 0 }, destroyXP: 250, color: 'gray' }, // Складной нож
        { rewardType: 'wheels', rewardData: { id: 44, amount: 3 }, destroyXP: 500, color: 'blue' },
        { rewardType: 'wheels', rewardData: { id: 23, amount: 3 }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3190,
                    [1]: 6253,
                }
            },
            destroyXP: 250,
            color: 'gray'
        }
    ]
},
{
    id: 50,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'm4f82' }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'm4f82', setId: 4 }, destroyXP: 1000, color: 'purple' },
    ]
},
{
    id: 51,
    free: [
        { rewardType: 'item', rewardData: { itemId: 443, count: 0 }, destroyXP: 750, color: 'purple' }, // Дрель 1500w
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 573, count: 0 }, destroyXP: 1500, color: 'red' }, // Списанный дефибриллятор
    ],
},
{
    id: 52,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 1,  drawable: 2009, texture: 0, isProp: 1, gender: 0 }, // Горнолыжные очки
                    1: { component: 1,  drawable: 2009, texture: 0, isProp: 1, gender: 1 }  // Горнолыжные очки
                }
            }
        }
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 51, amount: 3 }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 53,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2261, texture: 0, isProp: 0, gender: 0 }, // Тканиевая куртка 2
                    1: { component: 11, drawable: 2274, texture: 0, isProp: 0, gender: 1 }  // Худи Gucci
                }
            }
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 2 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ]
},
{
    id: 54,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 58, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 58, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 58, amount: 6 },  },
			]
		}, destroyXP: 500, color: 'blue' },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 5 }, destroyXP: 250, color: 'gray' },
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'evo10', setId: 5 }, destroyXP: 1000, color: 'purple' },
    ]
},
{
    id: 55,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'rhinehart' }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'draugur' }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 56,
    free: [
        { rewardType: 'wheels', rewardData: { id: 65, amount: 3 }, destroyXP: 500, color: 'blue' },
        { rewardType: 'wheels', rewardData: { id: 72, amount: 3 }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 4,  drawable: 2154, texture: 0, isProp: 0, gender: 0 }, // Джоггеры Jordan
                    1: { component: 4,  drawable: 2162, texture: 0, isProp: 0, gender: 1 }  // Джоггеры The Noth Face
                }
            }
        }
    ]
},
{
    id: 57,
    free: [
        { rewardType: 'item', rewardData: { itemId: 573, count: 0 }, destroyXP: 1500, color: 'red' }, // Списанный дефибриллятор
    ],
    premium: [

    ]
},
{
    id: 58,
    free: [
        { rewardType: 'money', rewardData: { amount: 25000 }, destroyXP: 500, color: 'blue' },
        { rewardType: 'item', rewardData: { itemId: 89, count: 10 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 10 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 50000 }, destroyXP: 750, color: 'purple' },
    ],
},
{
    id: 59,
    free: [

    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 333, count: 0 }, destroyXP: 750, color: 'purple' }, // Ручной пулемет Мk2
    ],
},
{
    id: 60,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'z800' }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        {
            color: 'blue',
            destroyXP: 500,
            rewardData: {
                id: 55, // Средний танец, бесплатный/премиум
                title: 'animations.list.SmoothMoves',
                animData: [
                    'majestic_animations_3',
                    'smooth_moves'
                ],
                flag: 1,
                looped: true,
                music: 'smooth_moves'
            },
            rewardType: 'animation',
        }
    ]
},
{
    id: 61,
    free: [
        {
            color: 'blue',
            destroyXP: 500,
            rewardData: {
                id: 107, // Средний бесплатный
                title: 'animations.list.BestMates',
                animData: [
                    'majestic_animations_6',
                    'best_mates'
                ],
                flag: 1,
                looped: true,
                music: 'best_mates',
            },
            rewardType: 'animation',
        }
    ],
    premium: [

    ]
},
{
    id: 62,
    free: [
        { rewardType: 'money', rewardData: { amount: 50000 }, destroyXP: 750, color: 'purple' },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 10 }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'm4comp', setId: 4 }, destroyXP: 1000, color: 'purple' },
    ]
},
{
    id: 63,
    free: [
        {
            rewardType: 'clothes', destroyXP: 500, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 0,  drawable: 2034, texture: 0, isProp: 1, gender: 0 }, // Рога оленя красные (Lum)
                    1: { component: 0,  drawable: 2041, texture: 0, isProp: 1, gender: 1 }  // Рога оленя красные (Lum)
                }
            }
        }
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 31, amount: 3 }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 64,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2255, texture: 3, isProp: 0, gender: 0 }, // Худи Puma
                    1: { component: 11, drawable: 2266, texture: 8, isProp: 0, gender: 1 }  // Цветной свитер
                }
            }
        }
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 17, amount: 3 }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 65,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2256, texture: 1, isProp: 0, gender: 0 }, // Свитер Stone Island
                    1: { component: 11, drawable: 2267, texture: 0, isProp: 0, gender: 1 }  // Топик + юбка
                }
            }
        }
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 24, amount: 3 }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 66,
    free: [
        { rewardType: 'wheels', rewardData: { id: 52, amount: 3 }, destroyXP: 500, color: 'blue' },
        { rewardType: 'wheels', rewardData: { id: 45, amount: 3 }, destroyXP: 500, color: 'blue' },
        { rewardType: 'wheels', rewardData: { id: 38, amount: 3 }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2262, texture: 18, isProp: 0, gender: 0 }, // Кимоно
                    1: { component: 4,  drawable: 2161, texture: 1, isProp: 0, gender: 1 }  // Юбка Alexander Wang
                }
            }
        }
    ]
},
{
    id: 67,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'blue',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2272, texture: 0, isProp: 0, gender: 0 }, // Ветровка
                    1: { component: 0,  drawable: 2056, texture: 12, isProp: 1, gender: 1 }  // Шапка с котиком
                }
            }
        }
    ],
    premium: [
        { rewardType: 'coins', rewardData: { amount: 250 }, destroyXP: 250, color: 'blue' },
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 59, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 59, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 59, amount: 6 },  },
			]
		}, destroyXP: 500, color: 'blue' },
    ],
},
{
    id: 68,
    free: [
        { rewardType: 'item', rewardData: { itemId: 333, count: 0 }, destroyXP: 750, color: 'purple' }, // Ручной пулемет Мk2
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 1,  drawable: 2008, texture: 0, isProp: 1, gender: 0 }, // Новогодние очки снежинка (Lum)
                    1: { component: 1,  drawable: 2008, texture: 0, isProp: 1, gender: 1 }  // Новогодние очки снежинка (Lum)
                }
            }
        }
    ]
},
{
    id: 69,
    free: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 3 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 3 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [
        {
           color: 'purple',
           destroyXP: 750,
           rewardData: {
               id: 162, // Средний премиум
               title: 'animations.list.Unwrapped',
               animData: [
                   'majestic_animations_props_2',
                   'unwrapped_player'
               ],
               flag: 0,
               looped: false,
               music: 'unwrapped',
           },
           rewardType: 'animation',
        }
    ]
},
{
    id: 70,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'delorean' }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [

    ]
},
{
    id: 71,
    free: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 10 }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2255, texture: 1, isProp: 0, gender: 0 }, // Худи Puma
                    1: { component: 11, drawable: 2266, texture: 4, isProp: 0, gender: 1 }  // Цветной свитер
                }
            }
        }
    ]
},
{
    id: 72,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 6,  drawable: 2061, texture: 8, isProp: 0, gender: 0 }, // Ботинки на лямках
                    1: { component: 6,  drawable: 2069, texture: 8, isProp: 0, gender: 1 }  // Ботинки на лямках
                }
            }
        }
    ],
    premium: [

    ]
},
{
    id: 73,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2271, texture: 0, isProp: 0, gender: 0 }, // Свитшот Dior
                    1: { component: 4,  drawable: 2160, texture: 0, isProp: 0, gender: 1 }  // Джинсы Gucci
                }
            }
        }
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 73, amount: 3 }, destroyXP: 500, color: 'blue' },
        { rewardType: 'wheels', rewardData: { id: 66, amount: 3 }, destroyXP: 500, color: 'blue' },
        { rewardType: 'wheels', rewardData: { id: 18, amount: 3 }, destroyXP: 750, color: 'purple' },
    ]
},
{
    id: 74,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'tenf' }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'rs6', setId: 6 }, destroyXP: 1000, color: 'purple' },
    ]
},
{
    id: 75,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2262, texture: 5, isProp: 0, gender: 0 }, // Кимоно
                    1: { component: 4,  drawable: 2161, texture: 0, isProp: 0, gender: 1 }  // Юбка Alexander Wang
                }
            }
        },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 15 }, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        {
            color: 'purple',
            destroyXP: 750,
            rewardData: {
                id: 53, // Средний танец, премиум
                title: 'animations.list.WorkItOut',
                animData: [
                    'majestic_animations_4',
                    'work_it_out'
                ],
                flag: 1,
                looped: true,
                music: 'work_it_out'
            },
            rewardType: 'animation',
        }
    ]
},
{
    id: 76,
    free: [
        { rewardType: 'item', rewardData: { itemId: 333, count: 0 }, destroyXP: 750, color: 'purple' }, // Ручной пулемет Мk2
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'bentaygast', setId: 4 }, destroyXP: 1000, color: 'purple' },
    ],
},
{
    id: 77,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2269, texture: 5, isProp: 0, gender: 0 }, // Пуховик
                    1: { component: 6,  drawable: 2068, texture: 1, isProp: 0, gender: 1 }  // Меховые ботинки
                }
            }
        }
    ],
    premium: [

    ]
},
{
    id: 78,
    free: [

    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 627, count: 0 }, destroyXP: 3000, color: 'gold' }, // Дефибриллятор Mk2
    ],
},
{
    id: 79,
    free: [
        { rewardType: 'item', rewardData: { itemId: 279, count: 0 }, destroyXP: 3000, color: 'gold' }, // Тяжелая снайперская винтовка
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 32, amount: 3 }, destroyXP: 750, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 25, amount: 3 }, destroyXP: 750, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 39, amount: 3 }, destroyXP: 750, color: 'purple' },
    ]
},
{
    id: 80,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2260, texture: 0, isProp: 0, gender: 0 }, // Тканиевая куртка
                    1: { component: 0,  drawable: 2058, texture: 0, isProp: 1, gender: 1 }  // Панама цветная
                }
            }
        }
    ],
    premium: [
        {
            color: 'purple',
            destroyXP: 750,
            rewardData: {
                id: 75, // Средний танец, премиум/бесплатный
                title: 'animations.list.Freemix',
                animData: [
                    'majestic_animations_5',
                    'freemix'
                ],
                flag: 1,
                looped: true,
                music: 'freemix'
            },
            rewardType: 'animation',
        }
    ]
},
{
    id: 81,
    free: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 3000, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'e63s', setId: 10 }, destroyXP: 2500, color: 'red' },
    ],
},
{
    id: 82,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 0,  drawable: 2041, texture: 0, isProp: 1, gender: 0 }, // Наушники мишки
                    1: { component: 0,  drawable: 2048, texture: 0, isProp: 1, gender: 1 }  // Наушники мишки
                }
            }
        }
    ],
    premium: [
        { rewardType: 'coins', rewardData: { amount: 300 }, destroyXP: 300, color: 'purple' },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 15 }, destroyXP: 750, color: 'purple' },
    ],
},
{
    id: 83,
    free: [
        {
           rewardType: 'tattoo',
           rewardData: {
               tattoosData: {
                   [0]: 3188,
                   [1]: 6262,
               }
           },
           destroyXP: 750,
           color: 'purple'
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2269, texture: 6, isProp: 0, gender: 0 }, // Пуховик
                    1: { component: 6,  drawable: 2068, texture: 7, isProp: 0, gender: 1 }  // Меховые ботинки
                }
            }
        }
    ]
},
{
    id: 84,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 4,  drawable: 2152, texture: 0, isProp: 0, gender: 0 }, // Классические шорты
                    1: { component: 4,  drawable: 2158, texture: 0, isProp: 0, gender: 1 }  // Брюки
                }
            }
        },
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 46, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 46, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 46, amount: 6 },  },
			]
		}, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'm4f82', setId: 5 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'm4f82', setId: 6 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'm4f82', setId: 7 }, },
            ]
		}, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 85,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'tattoo',
            variants: [
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3196,
                            [1]: 6254,
                        }
                    },
                    destroyXP: 250,
                    color: 'gray'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3197,
                            [1]: 6250,
                        }
                    },
                    destroyXP: 250,
                    color: 'gray'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3199,
                            [1]: 6270,
                        }
                    },
                    destroyXP: 750,
                    color: 'purple'
                }
            ]
        }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 6,  drawable: 2061, texture: 12, isProp: 0, gender: 0 }, // Ботинки на лямках
                    1: { component: 6,  drawable: 2069, texture: 12, isProp: 0, gender: 1 }  // Ботинки на лямках
                }
            }
        }
    ]
},
{
    id: 86,
    free: [
        { rewardType: 'money', rewardData: { amount: 100000 }, destroyXP: 1500, color: 'red' },
        {
           rewardType: 'tattoo',
           rewardData: {
               tattoosData: {
                   [0]: 3206,
                   [1]: 6272,
               }
           },
           destroyXP: 750,
           color: 'purple'
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2027, texture: 0, isProp: 0 }, // Фотоаппарат на шее
                    1: { gender: 1, component: 7, drawable: 2016, texture: 0, isProp: 0 }, // Фотоаппарат на шее
                }
            }
        }
    ]
},
{
    id: 87,
    free: [
        { rewardType: 'item', rewardData: { itemId: 333, count: 0 }, destroyXP: 750, color: 'purple' }, // Ручной пулемет Мk2
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 50000 }, destroyXP: 750, color: 'purple' },
        { rewardType: 'item', rewardData: { itemId: 89, count: 30 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 30 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ],
},
{
    id: 88,
    free: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 3 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 3 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2260, texture: 4, isProp: 0, gender: 0 }, // Тканиевая куртка
                    1: { component: 0,  drawable: 2058, texture: 6, isProp: 1, gender: 1 }  // Панама цветная
                }
            }
        }
    ]
},
{
    id: 89,
    free: [
        { rewardType: 'item', rewardData: { itemId: 627, count: 0 }, destroyXP: 3000, color: 'gold' }, // Дефибриллятор
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'm850', setId: 4 }, destroyXP: 1000, color: 'purple' },
    ]
},
{
    id: 90,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 0,  drawable: 2035, texture: 0, isProp: 1, gender: 0 }, // Новогодний Mini колпак
                    1: { component: 0,  drawable: 2042, texture: 0, isProp: 1, gender: 1 }  // Новогодний Mini колпак
                }
            }
        },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 15 }, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 60, amount: 3 }, destroyXP: 750, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 53, amount: 3 }, destroyXP: 750, color: 'purple' },
        { rewardType: 'wheels', rewardData: { id: 67, amount: 3 }, destroyXP: 750, color: 'purple' },
    ]
},
{
    id: 91,
    free: [
        {
            color: 'red',
            destroyXP: 1500,
            rewardData: {
                id: 116, // Топовый бесплатный
                title: 'animations.list.Pirouette',
                animData: [
                    'majestic_animations_6',
                    'pirouette'
                ],
                flag: 1,
                looped: true,
                music: 'pirouette',
            },
            rewardType: 'animation',
        }
    ],
    premium: [
        { rewardType: 'vip', rewardData: { type: 'Platinum', days: 90 }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 92,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
            rewardData: {
                clothesData: {
                    0: { component: 5,  drawable: 2023, texture: 4, isProp: 0, gender: 0 }, // Рюкзак Gucci
                    1: { component: 5,  drawable: 2024, texture: 4, isProp: 0, gender: 1 }  // Рюкзак Gucci
                }
            }
        }
    ],
    premium: [
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3200,
                    [1]: 6274,
                }
            },
            destroyXP: 750,
            color: 'purple'
        }
    ]
},
{
    id: 93,
    free: [
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3187,
                    [1]: 6265,
                }
            },
            destroyXP: 750,
            color: 'purple'
        },
        { rewardType: 'wheels', rewardData: { id: 74, amount: 3 }, destroyXP: 750, color: 'purple' },
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'nisgtr', setId: 9 }, destroyXP: 1000, color: 'purple' },
    ],
},
{
    id: 94,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 19, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 19, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 19, amount: 6 },  },
			]
		}, destroyXP: 1500, color: 'red' },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2258, texture: 1, isProp: 0, gender: 0 }, // Кожанная куртка
                    1: { component: 11, drawable: 2269, texture: 2, isProp: 0, gender: 1 }  // Платье
                }
            }
        }
    ]
},
{
    id: 95,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'tenf2' }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'sm722' }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 96,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'gtr50' }, destroyXP: 4000, color: 'gold' },
        // { rewardType: 'hidden', rewardData: { model: 'gtr50' }, destroyXP: 4000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 33, amount: 3 }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 97,
    free: [
        {
            color: 'red',
            destroyXP: 1500,
            rewardData: {
                id: 69, // Крутой танец, премиум либо бесплатный но высокий уровень
                title: 'animations.list.SavorTheW',
                animData: [
                    'majestic_animations_4',
                    'savor_the_w'
                ],
                flag: 1,
                looped: true,
                music: 'savor_the_w'
            },
            rewardType: 'animation',
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 279, count: 0 }, destroyXP: 3000, color: 'gold' }, // Тяжелая снайперская винтовка
    ],
},
{
    id: 98,
    free: [
        { rewardType: 'money', rewardData: { amount: 300000 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 40, amount: 3 }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 99,
    free: [
        { rewardType: 'item', rewardData: { itemId: 328, count: 0 }, destroyXP: 3000, color: 'gold' }, // Тяжелая снайперская винтовка Mk2
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 30 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 30 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ]
},
{
    id: 100,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'cayenne2' }, destroyXP: 5000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'msprinter', setId: 4 }, destroyXP: 2500, color: 'red' },
    ]
},
{
    id: 101,
    free: [
        { rewardType: 'wheels', rewardData: { id: 26, amount: 3 }, destroyXP: 1500, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 47, amount: 3 }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 102,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2258, texture: 0, isProp: 0, gender: 0 }, // Кожанная куртка
                    1: { component: 11, drawable: 2269, texture: 0, isProp: 0, gender: 1 }  // Платье
                }
            }
        }
    ],
    premium: [
        {
            color: 'red',
            destroyXP: 1500,
            rewardData: {
                id: 160, // Топовый платный
                title: 'animations.list.RideAlong',
                animData: [
                    'majestic_animations_props_2',
                    'mj_sleigh_player'
                ],
                flag: 33,
                looped: true,
                music: 'ride_along',
            },
            rewardType: 'animation',
        }
    ]
},
{
    id: 103,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 7,  drawable: 2063, texture: 0, isProp: 0, gender: 0 }, // Белые бусы
                    1: { component: 7,  drawable: 2051, texture: 0, isProp: 0, gender: 1 }  // Белые бусы
                }
            }
        }
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 54, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 54, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 54, amount: 6 },  },
			]
		}, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 104,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 0,  drawable: 53, texture: 0, isProp: 1, gender: 0 },   // Шлем черный
                    1: { component: 0,  drawable: 52, texture: 0, isProp: 1, gender: 1 }    // Шлем черный
                }
            }
        }
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'taycan', setId: 3 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'taycan', setId: 4 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'taycan', setId: 5 }, },
            ]
		}, destroyXP: 3000, color: 'red' },
    ]
},
{
    id: 105,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'tattoo',
            variants: [
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3209,
                            [1]: 6275,
                        }
                    },
                    destroyXP: 500,
                    color: 'blue'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3210,
                            [1]: 6276,
                        }
                    },
                    destroyXP: 500,
                    color: 'blue'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3211,
                            [1]: 6277,
                        }
                    },
                    destroyXP: 750,
                    color: 'purple'
                }
            ]
        }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        {
            color: 'gold',
            destroyXP: 3000,
            rewardData: {
                id: 161, // Эта анимка премиум только. У нее фишка что она парная, и другие игроки могут вместе петь песни
                title: 'animations.list.SingAlong',
                animData: [
                    'majestic_animations_props_2',
                    'sing_along_player'
                ],
                flag: 1,
                looped: true,
                music: 'sing_along_1',
            },
            rewardType: 'animation',
        }
    ]
},
{
    id: 106,
    free: [
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3186,
                    [1]: 6266,
                }
            },
            destroyXP: 750,
            color: 'purple'
        },
        { rewardType: 'item', rewardData: { itemId: 427, count: 1 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 1 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 1 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'm5comp', setId: 9 }, destroyXP: 2500, color: 'red' },
    ]
},
{
    id: 107,
    free: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
                rewardData: {
                clothesData: {
                    0: { component: 5,  drawable: 2024, texture: 2, isProp: 0, gender: 0 }, // Рюкзак Louis Vuitton
                    1: { component: 5,  drawable: 2025, texture: 2, isProp: 0, gender: 1 }  // Рюкзак Louis Vuitton
                }
            }
        }
    ],
    premium: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 108,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2270, texture: 3, isProp: 0, gender: 0 }, // Жилет The Noth Face
                    1: { component: 6,  drawable: 2070, texture: 1, isProp: 0, gender: 1 }  // Ботинки с ушками
                }
            }
        }
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 300000 }, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 109,
    free: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 2 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 2 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 2 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [

    ]
},
{
    id: 110,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'bacalar' }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [

    ]
},
{
    id: 111,
    free: [
        {
            color: 'gold',
            destroyXP: 1000,
            rewardData: {
                id: 170,
                title: 'animations.list.LilPrancer',
                animData: ['majestic_animations_props_2', 'prancer_player'],
                flag: 33,
                looped: true,
                music: 'prancer'
            },
            rewardType: 'animation',
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2257, texture: 0, isProp: 0, gender: 0 }, // Брендовая рубашка
                    1: { component: 11, drawable: 2268, texture: 0, isProp: 0, gender: 1 }  // Меховая куртка Tomy
                }
            }
        }
    ]
},
{
    id: 112,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 68, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 68, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 68, amount: 6 },  },
			]
		}, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        {
            color: 'red',
            destroyXP: 1500,
            rewardData: {
                id: 49, // Крутой танец, премиум
                title: 'animations.list.PullUp',
                animData: [
                    'majestic_animations_3',
                    'pull_up'
                ],
                flag: 1,
                looped: true,
                music: 'pull_up'
            },
            rewardType: 'animation',
        }
    ]
},
{
    id: 113,
    free: [
        {
            rewardType: 'clothes', destroyXP: 750, color: 'purple',
                rewardData: {
                clothesData: {
                    0: { component: 4,  drawable: 2152, texture: 3, isProp: 0, gender: 0 }, // Классические шорты
                    1: { component: 4,  drawable: 2158, texture: 2, isProp: 0, gender: 1 }  // Брюки
                }
            }
        },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 25 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'e63s', setId: 11 }, destroyXP: 2500, color: 'red' },
    ]
},
{
    id: 114,
    free: [
        { rewardType: 'wheels', rewardData: { id: 75, amount: 3 }, destroyXP: 1500, color: 'red' },
        { rewardType: 'wheels', rewardData: { id: 20, amount: 3 }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 30 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 30 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ]
},
{
    id: 115,
    free: [
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3184,
                    [1]: 6264,
                }
            },
            destroyXP: 750,
            color: 'purple'
        }
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'corsita' }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 116,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 7,  drawable: 2064, texture: 0, isProp: 0, gender: 0 }, // Новогодняя игрушка на плечо
                    1: { component: 7,  drawable: 2052, texture: 0, isProp: 0, gender: 1 }  // Новогодняя игрушка на плечо
                }
            }
        }
    ],
    premium: [

    ]
},
{
    id: 117,
    free: [

    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { component: 5,  drawable: 2023, texture: 3, isProp: 0, gender: 0 }, // Рюкзак Gucci
                    1: { component: 5,  drawable: 2024, texture: 3, isProp: 0, gender: 1 }  // Рюкзак Gucci
                }
            }
        }
    ]
},
{
    id: 118,
    free: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 3 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 3 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [

    ]
},
{
    id: 119,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2073, texture: 2, isProp: 0 }, // Белые катаны
                    1: { gender: 1, component: 7, drawable: 2063, texture: 2, isProp: 0 } // Белые катаны
                }
            }
        }
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'g63', setId: 8 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'g63', setId: 9 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'g63', setId: 10 }, },
            ]
		}, destroyXP: 5000, color: 'gold' },
    ]
},
{
    id: 120,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'fxxk' }, destroyXP: 5000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 121,
    free: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 1,  drawable: 2010, texture: 0, isProp: 1, gender: 0 }, // Очки круглые
                    1: { component: 1,  drawable: 2010, texture: 0, isProp: 1, gender: 1 }  // Очки круглые
                }
            }
        }
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 27, amount: 3 }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 122,
    free: [
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3185,
                    [1]: 6263,
                }
            },
            destroyXP: 750,
            color: 'purple'
        }
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2270, texture: 6, isProp: 0, gender: 0 }, // Жилет The Noth Face
                    1: { component: 6,  drawable: 2070, texture: 6, isProp: 0, gender: 1 }  // Ботинки с ушками
                }
            }
        }
    ]
},
{
    id: 123,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 34, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 34, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 34, amount: 6 },  },
			]
		}, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2264, texture: 0, isProp: 0, gender: 0 }, // Куртка Moncler
                    1: { component: 11, drawable: 2273, texture: 0, isProp: 0, gender: 1 }  // Куртка Moncler
                }
            }
        }
    ]
},
{
    id: 124,
    free: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 30 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 30 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 1500, color: 'red',
                rewardData: {
                clothesData: {
                    0: { component: 11, drawable: 2264, texture: 1, isProp: 0, gender: 0 }, // Куртка Moncler
                    1: { component: 11, drawable: 2273, texture: 1, isProp: 0, gender: 1 }  // Куртка Moncler
                }
            }
        }
    ]
},
{
    id: 125,
    free: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 3 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 3 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'amggt', setId: 4 }, destroyXP: 1000, color: 'purple' },
    ]
},
{
    id: 126,
    free: [
        {
            rewardType: 'tattoo',
            rewardData: {
                tattoosData: {
                    [0]: 3201,
                    [1]: 6267,
                }
            },
            destroyXP: 750,
            color: 'purple'
        },
        { rewardType: 'vehicleDiscount', rewardData: { amount: 20 }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 41, amount: 3 }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 127,
    free: [

    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
                rewardData: {
                clothesData: {
                    0: { component: 6,  drawable: 2003, texture: 0, isProp: 1, gender: 0 }, // Часы Richard Mille
                    1: { component: 6,  drawable: 2003, texture: 0, isProp: 1, gender: 1 }  // Часы Richard Mille
                }
            }
        }
    ]
},
{
    id: 128,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'tattoo',
            variants: [
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3218,
                            [1]: 6284,
                        }
                    },
                    destroyXP: 500,
                    color: 'blue'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3219,
                            [1]: 6285,
                        }
                    },
                    destroyXP: 750,
                    color: 'purple'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3220,
                            [1]: 6286,
                        }
                    },
                    destroyXP: 500,
                    color: 'blue'
                }
            ]
        }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'gt63s', setId: 4 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'gt63s', setId: 5 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'gt63s', setId: 6 }, },
            ]
		}, destroyXP: 5000, color: 'gold' },
    ]
},
{
    id: 129,
    free: [
        { rewardType: 'wheels', rewardData: { id: 48, amount: 3 }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2029, texture: 2, isProp: 0 }, // Кобура на 2 ноги, белая
                    1: { gender: 1, component: 7, drawable: 2012, texture: 2, isProp: 0 }, // Кобура на 2 ноги, белая
                }
            }
        }
    ]
},
{
    id: 130,
    free: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 25 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'aeroboat' }, destroyXP: 5000, color: 'gold' },
    ]
},
{
    id: 131,
    free: [
        { rewardType: 'wheels', rewardData: { id: 55, amount: 3 }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'tattoo',
            variants: [
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3215,
                            [1]: 6281,
                        }
                    },
                    destroyXP: 750,
                    color: 'purple'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3216,
                            [1]: 6282,
                        }
                    },
                    destroyXP: 500,
                    color: 'blue'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3217,
                            [1]: 6283,
                        }
                    },
                    destroyXP: 500,
                    color: 'blue'
                }
            ]
        }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 132,
    free: [
        { rewardType: 'wheels', rewardData: { id: 62, amount: 3 }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        { rewardType: 'money', rewardData: { amount: 300000 }, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 133,
    free: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 25 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'vehicleSet',
            variants: [
                { rewardType: 'vehicleSet', rewardData: { model: 'fxxk', setId: 3 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'fxxk', setId: 4 }, },
                { rewardType: 'vehicleSet', rewardData: { model: 'fxxk', setId: 5 }, },
            ]
		}, destroyXP: 5000, color: 'gold' },
    ]
},
{
    id: 134,
    free: [
        { rewardType: 'wheels', rewardData: { id: 76, amount: 3 }, destroyXP: 1500, color: 'red' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 69, amount: 3 }, destroyXP: 1500, color: 'red' },
    ]
},
{
    id: 135,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'lm87' }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'torero2' }, destroyXP: 500, color: 'blue' },
    ]
},
{
    id: 136,
    free: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 2 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 2 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 2 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [

    ]
},
{
    id: 137,
    free: [

    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
                rewardData: {
                clothesData: {
                    0: { component: 1,  drawable: 2099, texture: 0, isProp: 0, gender: 0 }, // Маска деда мороза
                    1: { component: 0,  drawable: 2051, texture: 0, isProp: 1, gender: 1 }  // Желтый шлем с ушками
                }
            }
        }
    ]
},
{
    id: 138,
    free: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 30 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 30 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 19, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 19, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 19, amount: 6 },  },
			]
		}, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 139,
    free: [
        { rewardType: 'wheels', rewardData: { id: 28, amount: 3 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
                rewardData: {
                clothesData: {
                    0: { component: 5,  drawable: 2024, texture: 0, isProp: 0, gender: 0 }, // Рюкзак Louis Vuitton
                    1: { component: 5,  drawable: 2025, texture: 0, isProp: 0, gender: 1 }  // Рюкзак Louis Vuitton
                }
            }
        }
    ]
},
{
    id: 140,
    free: [
        { rewardType: 'vehicle', rewardData: { model: 'terzo' }, destroyXP: 5000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'vehicleSet', rewardData: { model: 'huracan', setId: 3 }, destroyXP: 5000, color: 'gold' },
    ]
},
{
    id: 141,
    free: [
        { rewardType: 'vehicleDiscount', rewardData: { amount: 25 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 3 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 3 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 3 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ]
},
{
    id: 142,
    free: [
        { rewardType: 'wheels', rewardData: { id: 35, amount: 3 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2076, texture: 0, isProp: 0 }, // Кобура на 2 ноги, золотая
                    1: { gender: 1, component: 7, drawable: 2066, texture: 0, isProp: 0 }, // Кобура на 2 ноги, золотая
                }
            }
        }
    ]
},
{
    id: 143,
    free: [
        { rewardType: 'money', rewardData: { amount: 300000 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 49, amount: 3 }, destroyXP: 3000, color: 'gold' },
        { rewardType: 'wheels', rewardData: { id: 42, amount: 3 }, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 144,
    free: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 2 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 2 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 2 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ],
    premium: [
        { rewardType: 'variants', rewardData: {
            type: 'wheels',
			variants: [
				{ rewardType: 'wheels', rewardData: { id: 56, amount: 2 },  },
				{ rewardType: 'wheels', rewardData: { id: 56, amount: 4 },  },
				{ rewardType: 'wheels', rewardData: { id: 56, amount: 6 },  },
			]
		}, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 145,
    free: [

    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'ec135' }, destroyXP: 5000, color: 'gold' },
    ]
},
{
    id: 146,
    free: [
        { rewardType: 'wheels', rewardData: { id: 63, amount: 3 }, destroyXP: 3000, color: 'gold' },
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 89, count: 30 }, destroyXP: 250, color: 'gray' }, // Желтая аптечка (хил 75хп)
        { rewardType: 'item', rewardData: { itemId: 625, count: 30 }, destroyXP: 250, color: 'gray' }, // Адреналин (= эпинефрин)
    ]
},
{
    id: 147,
    free: [
        { rewardType: 'variants', rewardData: {
            type: 'tattoo',
            variants: [
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3212,
                            [1]: 6278,
                        }
                    },
                    destroyXP: 500,
                    color: 'blue'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3213,
                            [1]: 6279,
                        }
                    },
                    destroyXP: 500,
                    color: 'blue'
                },
                {
                    rewardType: 'tattoo',
                    rewardData: {
                        tattoosData: {
                            [0]: 3214,
                            [1]: 6280,
                        }
                    },
                    destroyXP: 750,
                    color: 'purple'
                }
            ]
        }, destroyXP: 500, color: 'blue' },
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 70, amount: 3 }, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 148,
    free: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 0, drawable: 2047, texture: 0, isProp: 1 }, // Золотые рога
                    1: { gender: 1, component: 0, drawable: 2049, texture: 0, isProp: 1 }, // Золотые рога
                }
            }
        }
    ],
    premium: [
        { rewardType: 'wheels', rewardData: { id: 77, amount: 3 }, destroyXP: 3000, color: 'gold' },
    ]
},
{
    id: 149,
    free: [
        {
            rewardType: 'clothes', destroyXP: 3000, color: 'gold',
            rewardData: {
                clothesData: {
                    0: { gender: 0, component: 7, drawable: 2075, texture: 0, isProp: 0 }, // Крылья
                    1: { gender: 1, component: 7, drawable: 2065, texture: 0, isProp: 0 }, // Крылья
                }
            }
        }
    ],
    premium: [
        { rewardType: 'item', rewardData: { itemId: 427, count: 10 }, destroyXP: 250, color: 'gray' }, // Маленький новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 428, count: 10 }, destroyXP: 500, color: 'blue' }, // Средний новогодний подарок
        { rewardType: 'item', rewardData: { itemId: 429, count: 10 }, destroyXP: 750, color: 'purple' }, // Большой новогодний подарок
    ]
},
{
    id: 150,
    free: [

    ],
    premium: [
        { rewardType: 'vehicle', rewardData: { model: 'l650' }, destroyXP: 5000, color: 'gold' },
    ]
}
]

export const seasonPassXP = {
    // Формула XP_START+(currentLevel*XP_STEP)
    XP_START: 300, // 1й уровень начинается с этого XP
    XP_STEP: 220, // Шаг на каждый уровень. (XP нужен для текущего уровня)+XP_STEP = следующий уровень
}

export const seasonPassgetXPForNextLevel = (currentLevel) => currentLevel >= 100 ? 10000 : seasonPassXP.XP_START + (currentLevel * seasonPassXP.XP_STEP);
