import { seasonPassTasks } from './tasks'

export const seasonPassConfig = {
    SEASON_ID: 6,
    SEASON_TITLE: 'Зима 2025', // Используется только для формирования российских чеков. Не требует перевода

    TRANSFER_DAYS: 90, // Через сколько дней можно будет продавать/передавать одежду/транспорт

    MAX_TASKS_PER_PLAYER: 9, // ДОЛЖНО ДЕЛИТЬСЯ НА 3

    ACTIVE_DATE_RANGE: { from: '2024-11-18 06:00:00', to: '2025-02-28 23:59:59' },
    ACTIVE_CLAIM_DATE_RANGE: { from: '2024-11-18 06:00:00', to: '2025-03-14 23:59:59' },

    SPECIFIC_END_DATE: {
        'RU14': {
            active: '2025-03-31 23:59:59',
            claim: '2025-04-10 23:59:59'
        },

        'DEVELOP0': {
            active: '2025-03-31 23:59:59',
            claim: '2025-04-10 23:59:59'
        },

        'TEST1': {
            active: '2025-03-31 23:59:59',
            claim: '2025-04-10 23:59:59'
        },

        // 'TEST0': {
        //     active: '2024-09-15',
        //     claim: '2024-09-29'
        // },
        // 'TEST6': {
        //     active: '2024-09-31',
        //     claim: '2024-09-31'
        // },
        // 'TEST7': {
        //     active: '2024-09-15',
        //     claim: '2024-09-29'
        // }
    },

    XP_PER_MC_EU: 2, // Сколько XP выдавать за 1 MCoin (EU)
    PRICE_EU_MULTIPLIER: 1,

    XP_PER_MC: 2, // Сколько XP выдавать за 1 MCoin
    XP_PER_PAYDAY: 300, // Сколько XP выдавать за 1 PayDay

    // Перенесено в webdb
    // MULTIPLIER: { // Удваивать опыт за квесты и бонус за часы
    //     'RU1': 1.1,
    //     'RU2': 1.2,
    //     'RU3': 1.1,
    //     'RU4': 1.1,
    //     'RU5': 1.2,
    //     'RU6': 1.2,
    //     'RU7': 1.1,
    //     'RU8': 1,
    //     'RU9': 1,
    //     'RU10': 1,
    //     'RU11': 1,
    //     'RU12': 1,

    //     'RU13': 1,

    //     'PL1': 1,
    //     'DE1': 1,
    // },

    PRICE: {
        starter: {
            coins: 0,
            rubles: 0,

            level: 1
        },

        bronze: {
            coins: 3400,
            rubles: 1290,

            level: 50
        },
        silver: {
            coins: 9000,
            rubles: 3790,

            level: 100
        },
        gold: {
            coins: 22000,
            rubles: 8990,

            level: 150
        }
    },

    SHOP_XP: [
        { buyId: 1, type: 'status' },

        {
            buyId: 'king',
            type: 'king',
            coins: 75000,
            rubles: 31990,
            exp: 300000,

            award: [
                { component: 3, drawable: 2067, texture: 7, isProp: 0 },
                { component: 3, drawable: 2400, texture: 7, isProp: 0 }
            ]
        },

        // Цена генерируется из EXP и XP_PER_MC
        { buyId: 3, type: 'experience', price: 500, bonus: 10, color: 'blue' }, // Бонус задается вручную
        { buyId: 4, type: 'experience', price: 2500, bonus: 15, color: 'purple' }, // Бонус задается вручную
        { buyId: 5, type: 'experience', price: 10000, bonus: 25, color: 'red' }, // Бонус задается вручную
        { buyId: 6, type: 'experience', price: 30000, bonus: 50, color: 'gold' }, // Бонус задается вручную
    ]
}

export const seasonPassIdMap = {};
export const seasonPassTasksMap = new Map();

for (const taskData of seasonPassTasks) {

    seasonPassIdMap[taskData.taskId] = taskData;

    if (!seasonPassTasksMap.has(taskData.trigger_type)) {
        seasonPassTasksMap.set(taskData.trigger_type, []);
    }

    seasonPassTasksMap.get(taskData.trigger_type).push(taskData);

    if (Array.isArray(taskData.trigger_value)) {
        for (const value of taskData.trigger_value) {
            const key = `${taskData.trigger_type}_${value}`;

            if (!seasonPassTasksMap.has(key)) {
                seasonPassTasksMap.set(key, []);
            }

            seasonPassTasksMap.get(key).push(taskData);
        }
    }
}

export const seasonPassGiftItemIds = [
    {
        itemId: 427,
        color: 'gray',
        destroyXP: 250
    },
    {
        itemId: 428,
        color: 'purple',
        destroyXP: 500
    },
    {
        itemId: 429,
        color: 'gold',
        destroyXP: 750
    }
]

export const senderGiftId = 999;
