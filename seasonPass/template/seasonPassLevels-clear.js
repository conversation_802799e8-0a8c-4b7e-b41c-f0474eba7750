/**
 * Доступные параметры на награду.
* @param  {String}     rewardType                      Тип награды. ('item', 'vehicle', 'vehicleSet', 'vehicleMod', 'vehicleDiscount', 'money', 'clothes', 'coins', 'vip', 'hidden')
 * @param  {Object}     rewardData                      Данные награды. Они могут быть разными в зависимости от типа награды.
 * @param  {Number}     destroyXP                       Кол-во XP получаемого при распылении награды.
 * @param  {String}     color                           Цвет награды. ('gray', 'blue', 'purple', 'red', 'gold')
 */
// SEASON PASS РАССЧИТАН на 150 УРОВНЕЙ. НЕ МЕНЬШЕ НЕ БОЛЬШЕ

export const seasonPassLevels = [{
    id: 1,
    free: [

    ],
    premium: [

    ]
},
{
    id: 2,
    free: [

    ],
    premium: [

    ]
},
{
    id: 3,
    free: [

    ],
    premium: [

    ]
},
{
    id: 4,
    free: [

    ],
    premium: [

    ]
},
{
    id: 5,
    free: [

    ],
    premium: [

    ]
},
{
    id: 6,
    free: [

    ],
    premium: [

    ]
},
{
    id: 7,
    free: [

    ],
    premium: [

    ]
},
{
    id: 8,
    free: [

    ],
    premium: [

    ]
},
{
    id: 9,
    free: [

    ],
    premium: [

    ]
},
{
    id: 10,
    free: [

    ],
    premium: [

    ]
},
{
    id: 11,
    free: [

    ],
    premium: [

    ]
},
{
    id: 12,
    free: [

    ],
    premium: [

    ]
},
{
    id: 13,
    free: [

    ],
    premium: [

    ]
},
{
    id: 14,
    free: [

    ],
    premium: [

    ]
},
{
    id: 15,
    free: [

    ],
    premium: [

    ]
},
{
    id: 16,
    free: [

    ],
    premium: [

    ],
},
{
    id: 17,
    free: [

    ],
    premium: [

    ],
},
{
    id: 18,
    free: [

    ],
    premium: [

    ],
},
{
    id: 19,
    free: [

    ],
    premium: [

    ],
},
{
    id: 20,
    free: [

    ],
    premium: [

    ]
},
{
    id: 21,
    free: [

    ],
    premium: [

    ]
},
{
    id: 22,
    free: [

    ],
    premium: [

    ]
},
{
    id: 23,
    free: [

    ],
    premium: [

    ]
},
{
    id: 24,
    free: [

    ],
    premium: [

    ]
},
{
    id: 25,
    free: [

    ],
    premium: [

    ]
},
{
    id: 26,
    free: [

    ],
    premium: [

    ]
},
{
    id: 27,
    free: [

    ],
    premium: [

    ]
},
{
    id: 28,
    free: [

    ],
    premium: [

    ],
},
{
    id: 29,
    free: [

    ],
    premium: [

    ],
},
{
    id: 30,
    free: [

    ],
    premium: [

    ]
},
{
    id: 31,
    free: [

    ],
    premium: [

    ]
},
{
    id: 32,
    free: [

    ],
    premium: [

    ],
},
{
    id: 33,
    free: [

    ],
    premium: [

    ]
},
{
    id: 34,
    free: [

    ],
    premium: [

    ]
},
{
    id: 35,
    free: [

    ],
    premium: [

    ]
},
{
    id: 36,
    free: [

    ],
    premium: [

    ],
},
{
    id: 37,
    free: [

    ],
    premium: [

    ]
},
{
    id: 38,
    free: [

    ],
    premium: [

    ]
},
{
    id: 39,
    free: [

    ],
    premium: [

    ]
},
{
    id: 40,
    free: [

    ],
    premium: [

    ]
},
{
    id: 41,
    free: [

    ],
    premium: [

    ]
},
{
    id: 42,
    free: [

    ],
    premium: [

    ]
},
{
    id: 43,
    free: [

    ],
    premium: [

    ]
},
{
    id: 44,
    free: [

    ],
    premium: [

    ]
},
{
    id: 45,
    free: [

    ],
    premium: [

    ]
},
{
    id: 46,
    free: [

    ],
    premium: [

    ]
},
{
    id: 47,
    free: [

    ],
    premium: [

    ]
},
{
    id: 48,
    free: [

    ],
    premium: [

    ]
},
{
    id: 49,
    free: [

    ],
    premium: [

    ]
},
{
    id: 50,
    free: [

    ],
    premium: [

    ]
},
{
    id: 51,
    free: [

    ],
    premium: [

    ],
},
{
    id: 52,
    free: [

    ],
    premium: [

    ]
},
{
    id: 53,
    free: [

    ],
    premium: [

    ]
},
{
    id: 54,
    free: [

    ],
    premium: [

    ]
},
{
    id: 55,
    free: [

    ],
    premium: [

    ]
},
{
    id: 56,
    free: [

    ],
    premium: [

    ]
},
{
    id: 57,
    free: [

    ],
    premium: [

    ]
},
{
    id: 58,
    free: [

    ],
    premium: [

    ],
},
{
    id: 59,
    free: [

    ],
    premium: [

    ],
},
{
    id: 60,
    free: [

    ],
    premium: [

    ]
},
{
    id: 61,
    free: [

    ],
    premium: [

    ]
},
{
    id: 62,
    free: [

    ],
    premium: [

    ]
},
{
    id: 63,
    free: [

    ],
    premium: [

    ]
},
{
    id: 64,
    free: [

    ],
    premium: [

    ]
},
{
    id: 65,
    free: [

    ],
    premium: [

    ]
},
{
    id: 66,
    free: [

    ],
    premium: [

    ]
},
{
    id: 67,
    free: [

    ],
    premium: [

    ],
},
{
    id: 68,
    free: [

    ],
    premium: [

    ]
},
{
    id: 69,
    free: [

    ],
    premium: [

    ]
},
{
    id: 70,
    free: [

    ],
    premium: [

    ]
},
{
    id: 71,
    free: [

    ],
    premium: [

    ]
},
{
    id: 72,
    free: [

    ],
    premium: [

    ]
},
{
    id: 73,
    free: [

    ],
    premium: [

    ]
},
{
    id: 74,
    free: [

    ],
    premium: [

    ]
},
{
    id: 75,
    free: [

    ],
    premium: [

    ]
},
{
    id: 76,
    free: [

    ],
    premium: [

    ],
},
{
    id: 77,
    free: [

    ],
    premium: [

    ]
},
{
    id: 78,
    free: [

    ],
    premium: [

    ],
},
{
    id: 79,
    free: [

    ],
    premium: [

    ]
},
{
    id: 80,
    free: [

    ],
    premium: [

    ]
},
{
    id: 81,
    free: [

    ],
    premium: [

    ],
},
{
    id: 82,
    free: [

    ],
    premium: [

    ],
},
{
    id: 83,
    free: [

    ],
    premium: [

    ]
},
{
    id: 84,
    free: [

    ],
    premium: [

    ]
},
{
    id: 85,
    free: [

    ],
    premium: [

    ]
},
{
    id: 86,
    free: [

    ],
    premium: [

    ]
},
{
    id: 87,
    free: [

    ],
    premium: [

    ],
},
{
    id: 88,
    free: [

    ],
    premium: [

    ]
},
{
    id: 89,
    free: [

    ],
    premium: [

    ]
},
{
    id: 90,
    free: [

    ],
    premium: [

    ]
},
{
    id: 91,
    free: [

    ],
    premium: [

    ]
},
{
    id: 92,
    free: [

    ],
    premium: [

    ]
},
{
    id: 93,
    free: [

    ],
    premium: [

    ],
},
{
    id: 94,
    free: [

    ],
    premium: [

    ]
},
{
    id: 95,
    free: [

    ],
    premium: [

    ]
},
{
    id: 96,
    free: [

    ],
    premium: [

    ]
},
{
    id: 97,
    free: [

    ],
    premium: [

    ],
},
{
    id: 98,
    free: [

    ],
    premium: [

    ]
},
{
    id: 99,
    free: [

    ],
    premium: [

    ]
},
{
    id: 100,
    free: [

    ],
    premium: [

    ]
},
{
    id: 101,
    free: [

    ],
    premium: [

    ]
},
{
    id: 102,
    free: [

    ],
    premium: [

    ]
},
{
    id: 103,
    free: [

    ],
    premium: [

    ]
},
{
    id: 104,
    free: [

    ],
    premium: [

    ]
},
{
    id: 105,
    free: [

    ],
    premium: [

    ]
},
{
    id: 106,
    free: [

    ],
    premium: [

    ]
},
{
    id: 107,
    free: [

    ],
    premium: [

    ]
},
{
    id: 108,
    free: [

    ],
    premium: [

    ]
},
{
    id: 109,
    free: [

    ],
    premium: [

    ]
},
{
    id: 110,
    free: [

    ],
    premium: [

    ]
},
{
    id: 111,
    free: [

    ],
    premium: [

    ]
},
{
    id: 112,
    free: [

    ],
    premium: [

    ]
},
{
    id: 113,
    free: [

    ],
    premium: [

    ]
},
{
    id: 114,
    free: [

    ],
    premium: [

    ]
},
{
    id: 115,
    free: [

    ],
    premium: [

    ]
},
{
    id: 116,
    free: [

    ],
    premium: [

    ]
},
{
    id: 117,
    free: [

    ],
    premium: [

    ]
},
{
    id: 118,
    free: [

    ],
    premium: [

    ]
},
{
    id: 119,
    free: [

    ],
    premium: [

    ]
},
{
    id: 120,
    free: [

    ],
    premium: [

    ]
},
{
    id: 121,
    free: [

    ],
    premium: [

    ]
},
{
    id: 122,
    free: [

    ],
    premium: [

    ]
},
{
    id: 123,
    free: [

    ],
    premium: [

    ]
},
{
    id: 124,
    free: [

    ],
    premium: [

    ]
},
{
    id: 125,
    free: [

    ],
    premium: [

    ]
},
{
    id: 126,
    free: [

    ],
    premium: [

    ]
},
{
    id: 127,
    free: [

    ],
    premium: [

    ]
},
{
    id: 128,
    free: [

    ],
    premium: [

    ]
},
{
    id: 129,
    free: [

    ],
    premium: [

    ]
},
{
    id: 130,
    free: [

    ],
    premium: [

    ]
},
{
    id: 131,
    free: [

    ],
    premium: [

    ]
},
{
    id: 132,
    free: [

    ],
    premium: [

    ]
},
{
    id: 133,
    free: [

    ],
    premium: [

    ]
},
{
    id: 134,
    free: [

    ],
    premium: [

    ]
},
{
    id: 135,
    free: [

    ],
    premium: [

    ]
},
{
    id: 136,
    free: [

    ],
    premium: [

    ]
},
{
    id: 137,
    free: [

    ],
    premium: [

    ]
},
{
    id: 138,
    free: [

    ],
    premium: [

    ]
},
{
    id: 139,
    free: [

    ],
    premium: [

    ]
},
{
    id: 140,
    free: [

    ],
    premium: [

    ]
},
{
    id: 141,
    free: [

    ],
    premium: [

    ]
},
{
    id: 142,
    free: [

    ],
    premium: [

    ]
},
{
    id: 143,
    free: [

    ],
    premium: [

    ]
},
{
    id: 144,
    free: [

    ],
    premium: [

    ]
},
{
    id: 145,
    free: [

    ],
    premium: [

    ]
},
{
    id: 146,
    free: [

    ],
    premium: [

    ]
},
{
    id: 147,
    free: [

    ],
    premium: [

    ]
},
{
    id: 148,
    free: [

    ],
    premium: [

    ]
},
{
    id: 149,
    free: [

    ],
    premium: [

    ]
},
{
    id: 150,
    free: [

    ],
    premium: [

    ]
}
]

export const seasonPassXP = {
    // Формула XP_START+(currentLevel*XP_STEP)
    XP_START: 300, // 1й уровень начинается с этого XP
    XP_STEP: 220, // Шаг на каждый уровень. (XP нужен для текущего уровня)+XP_STEP = следующий уровень
}

export const seasonPassgetXPForNextLevel = (currentLevel) => currentLevel >= 100 ? 10000 : seasonPassXP.XP_START + (currentLevel * seasonPassXP.XP_STEP);
