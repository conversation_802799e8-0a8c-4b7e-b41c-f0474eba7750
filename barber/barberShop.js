module.exports = {
	scissors: {
		model: 0xB7087FA9
    },

	hairValues: [
        [
            {id: 0, price: 50},
            {id: 1, price: 100},
            {id: 2, price: 300},
            {id: 3, price: 350},
            {id: 4, price: 320},
            {id: 5, price: 180},
            {id: 6, price: 330},
            {id: 7, price: 370},
            {id: 8, price: 410},
            {id: 9, price: 520},
            {id: 10, price: 440},
            {id: 11, price: 460},
            {id: 12, price: 520},
            {id: 13, price: 340},
            {id: 14, price: 620},
            {id: 15, price: 550},
            {id: 16, price: 350},
            {id: 17, price: 470},
            {id: 18, price: 370},
            {id: 19, price: 580},
            {id: 20, price: 390},
            {id: 21, price: 745},
            {id: 22, price: 420},
            {id: 24, price: 430},
            {id: 25, price: 450},
            {id: 26, price: 470},
            {id: 27, price: 430},
            {id: 28, price: 490},
            {id: 29, price: 390},
            {id: 30, price: 650},
            {id: 31, price: 520},
            {id: 32, price: 760},
            {id: 33, price: 780},
            {id: 34, price: 580},
            {id: 35, price: 380},
            {id: 36, price: 350},
            {id: 72, price: 530},
            {id: 73, price: 115},
            {id: 74, price: 325},
            {id: 2000, price: 445},
            {id: 2001, price: 400},
            {id: 2002, price: 660},
            {id: 2003, price: 700},
            {id: 2004, price: 500},
            {id: 2005, price: 375},
            {id: 2006, price: 480},
            {id: 2007, price: 350},
            {id: 2008, price: 210},
            {id: 2009, price: 290},
            {id: 2010, price: 315},
            {id: 2011, price: 280},
            {id: 2012, price: 320},
            {id: 2013, price: 375},
            {id: 2015, price: 500},
            {id: 2016, price: 280},
            {id: 2017, price: 400},
            {id: 2018, price: 350},
            {id: 2019, price: 425},
            {id: 2020, price: 475},
            {id: 2021, price: 380},
            {id: 2022, price: 250},
            {id: 2023, price: 495},
            {id: 2024, price: 500},
            {id: 2025, price: 650},
            {id: 2026, price: 450},
            {id: 2027, price: 515},
            {id: 2028, price: 600},
            {id: 2029, price: 490},
            {id: 2030, price: 290},
            {id: 2031, price: 315},
            {id: 2032, price: 580},
            {id: 2033, price: 700},
            {id: 2034, price: 475},
            {id: 2035, price: 500},
            {id: 2036, price: 350},
            {id: 2037, price: 1000},
            {id: 2038, price: 480},
            {id: 2039, price: 600},
            {id: 2040, price: 500},
            {id: 2041, price: 500},
        ],
        [
            {id: 0, price: 50},
            {id: 1, price: 100},
            {id: 2, price: 300},
            {id: 3, price: 350},
            {id: 4, price: 320},
            {id: 5, price: 180},
            {id: 6, price: 330},
            {id: 7, price: 370},
            {id: 8, price: 410},
            {id: 9, price: 520},
            {id: 10, price: 440},
            {id: 11, price: 460},
            {id: 12, price: 520},
            {id: 13, price: 340},
            {id: 14, price: 620},
            {id: 15, price: 550},
            {id: 16, price: 350},
            {id: 17, price: 470},
            {id: 18, price: 370},
            {id: 19, price: 580},
            {id: 20, price: 390},
            {id: 21, price: 745},
            {id: 22, price: 420},
            {id: 23, price: 430},
            {id: 25, price: 450},
            {id: 26, price: 470},
            {id: 27, price: 430},
            {id: 28, price: 490},
            {id: 29, price: 390},
            {id: 30, price: 650},
            {id: 31, price: 520},
            {id: 32, price: 760},
            {id: 33, price: 780},
            {id: 34, price: 580},
            {id: 35, price: 380},
            {id: 36, price: 350},
            {id: 37, price: 530},
            {id: 38, price: 115},
            {id: 76, price: 325},
            {id: 77, price: 445},
            {id: 2000, price: 400},
            {id: 2001, price: 660},
            {id: 2002, price: 700},
            {id: 2003, price: 500},
            {id: 2004, price: 375},
            {id: 2005, price: 480},
            {id: 2006, price: 350},
            {id: 2007, price: 210},
            {id: 2008, price: 290},
            {id: 2009, price: 315},
            {id: 2011, price: 280},
            {id: 2012, price: 320},
            {id: 2013, price: 375},
            {id: 2014, price: 500},
            {id: 2015, price: 280},
            {id: 2016, price: 400},
            {id: 2017, price: 425},
            {id: 2018, price: 475},
            {id: 2019, price: 380},
            {id: 2020, price: 250},
            {id: 2021, price: 495},
            {id: 2022, price: 500},
            {id: 2023, price: 650},
            {id: 2024, price: 450},
            {id: 2025, price: 515},
            {id: 2026, price: 600},
            {id: 2027, price: 490},
            {id: 2028, price: 290},
            {id: 2039, price: 315},
            {id: 2040, price: 280},
            {id: 2041, price: 320},
            {id: 2042, price: 375},
            {id: 2043, price: 500},
            {id: 2044, price: 280},
            {id: 2045, price: 400},
            {id: 2046, price: 350},
            {id: 2047, price: 425},
            {id: 2048, price: 475},
            {id: 2049, price: 380},
            {id: 2050, price: 250},
            {id: 2051, price: 495},
            {id: 2052, price: 500},
            {id: 2053, price: 650},
            {id: 2054, price: 450},
            {id: 2055, price: 515},
            {id: 2056, price: 600},
            {id: 2057, price: 490},
            {id: 2058, price: 480},
            {id: 2059, price: 350},
            {id: 2060, price: 210},
            {id: 2061, price: 290},
            {id: 2062, price: 315},
            {id: 2063, price: 280},
            {id: 2064, price: 320},
            {id: 2065, price: 280},
            {id: 2066, price: 320},
            {id: 2088, price: 480},
            {id: 2089, price: 870},
            {id: 2090, price: 600},
            {id: 2091, price: 500},
            {id: 2092, price: 750},
            {id: 2093, price: 950},
            {id: 2094, price: 1200},
            {id: 2095, price: 750},
            {id: 2096, price: 1250}
        ]
      ],

	face: [ // overlay 4
        { l: 'NONE', v: 255, price: 50 },

		{ l: 'CC_MKUP_16', v: 16, price: 50 },
		{ l: 'CC_MKUP_17', v: 17, price: 50 },
		{ l: 'CC_MKUP_18', v: 18, price: 50 },
		{ l: 'CC_MKUP_19', v: 19, price: 50 },
		{ l: 'CC_MKUP_20', v: 20, price: 50 },
		{ l: 'CC_MKUP_21', v: 21, price: 50 },
		{ l: 'CC_MKUP_26', v: 26, price: 50 },
		{ l: 'CC_MKUP_27', v: 27, price: 50 },
		{ l: 'CC_MKUP_28', v: 28, price: 50 },
		{ l: 'CC_MKUP_29', v: 29, price: 50 },
		{ l: 'CC_MKUP_30', v: 30, price: 50 },
        { l: 'CC_MKUP_31', v: 31, price: 50 },

        { l: 'CC_MKUP_33', v: 33, price: 50 },

		{ l: 'CC_MKUP_42', v: 42, price: 50 },
		{ l: 'CC_MKUP_43', v: 43, price: 50 },
		{ l: 'CC_MKUP_44', v: 44, price: 50 },
		{ l: 'CC_MKUP_45', v: 45, price: 50 },
		{ l: 'CC_MKUP_46', v: 46, price: 50 },
		{ l: 'CC_MKUP_47', v: 47, price: 50 },
		{ l: 'CC_MKUP_48', v: 48, price: 50 },
		{ l: 'CC_MKUP_49', v: 49, price: 50 },
		{ l: 'CC_MKUP_50', v: 50, price: 50 },
		{ l: 'CC_MKUP_51', v: 51, price: 50 },
		{ l: 'CC_MKUP_52', v: 52, price: 50 },
		{ l: 'CC_MKUP_53', v: 53, price: 50 },
		{ l: 'CC_MKUP_54', v: 54, price: 50 },
		{ l: 'CC_MKUP_55', v: 55, price: 50 },
		{ l: 'CC_MKUP_56', v: 56, price: 50 },
		{ l: 'CC_MKUP_57', v: 57, price: 50 },
		{ l: 'CC_MKUP_58', v: 58, price: 50 },
		{ l: 'CC_MKUP_59', v: 59, price: 50 },
		{ l: 'CC_MKUP_60', v: 60, price: 50 },
		{ l: 'CC_MKUP_61', v: 61, price: 50 },
		{ l: 'CC_MKUP_62', v: 62, price: 50 },
		{ l: 'CC_MKUP_63', v: 63, price: 50 },
		{ l: 'CC_MKUP_64', v: 64, price: 50 },
		{ l: 'CC_MKUP_65', v: 65, price: 50 },
		{ l: 'CC_MKUP_66', v: 66, price: 50 },
		{ l: 'CC_MKUP_67', v: 67, price: 50 },
		{ l: 'CC_MKUP_68', v: 68, price: 50 },
		{ l: 'CC_MKUP_69', v: 69, price: 50 },
		{ l: 'CC_MKUP_70', v: 70, price: 50 },
        { l: 'CC_MKUP_71', v: 71, price: 50 }
    ],

	makeup: [ // overlay 4
        { l: 'NONE', v: 255, price: 50 },

		{ l: 'CC_MKUP_0',  v: 0, price: 50 },
		{ l: 'CC_MKUP_1',  v: 1, price: 50 },
		{ l: 'CC_MKUP_2',  v: 2, price: 50 },
		{ l: 'CC_MKUP_3',  v: 3, price: 50 },
		{ l: 'CC_MKUP_4',  v: 4, price: 50 },
		{ l: 'CC_MKUP_5',  v: 5, price: 50 },
		{ l: 'CC_MKUP_6',  v: 6, price: 50 },
		{ l: 'CC_MKUP_7',  v: 7, price: 50 },
		{ l: 'CC_MKUP_8',  v: 8, price: 50 },
		{ l: 'CC_MKUP_9',  v: 9, price: 50 },
		{ l: 'CC_MKUP_10', v: 10, price: 50 },
		{ l: 'CC_MKUP_11', v: 11, price: 50 },
		{ l: 'CC_MKUP_12', v: 12, price: 50 },
		{ l: 'CC_MKUP_13', v: 13, price: 50 },
		{ l: 'CC_MKUP_14', v: 14, price: 50 },
        { l: 'CC_MKUP_15', v: 15, price: 50 },

        { l: 'CC_MKUP_32', v: 32, price: 50 },

		{ l: 'CC_MKUP_34', v: 34, price: 50 },
		{ l: 'CC_MKUP_35', v: 35, price: 50 },
		{ l: 'CC_MKUP_36', v: 36, price: 50 },
		{ l: 'CC_MKUP_37', v: 37, price: 50 },
		{ l: 'CC_MKUP_38', v: 38, price: 50 },
		{ l: 'CC_MKUP_39', v: 39, price: 50 },
		{ l: 'CC_MKUP_40', v: 40, price: 50 },
		{ l: 'CC_MKUP_41', v: 41, price: 50 }
    ],

    blush: [ // overlay 5
        { l: 'NONE', v: 255, price: 50 },

        { l: 'CC_BLUSH_0',  v: 0, price: 50 },
        { l: 'CC_BLUSH_1',  v: 1, price: 50 },
        { l: 'CC_BLUSH_2',  v: 2, price: 50 },
        { l: 'CC_BLUSH_3',  v: 3, price: 50 },
        { l: 'CC_BLUSH_4',  v: 4, price: 50 },
        { l: 'CC_BLUSH_5',  v: 5, price: 50 },
        { l: 'CC_BLUSH_6',  v: 6, price: 50 },
		{ l: 'CC_BLUSH_7',  v: 7, price: 50 },
		{ l: 'CC_BLUSH_8',  v: 8, price: 50 },
		{ l: 'CC_BLUSH_9',  v: 9, price: 50 },
		{ l: 'CC_BLUSH_10', v: 10, price: 50 },
		{ l: 'CC_BLUSH_11', v: 11, price: 50 },
		{ l: 'CC_BLUSH_12', v: 12, price: 50 },
		{ l: 'CC_BLUSH_13', v: 13, price: 50 },
		{ l: 'CC_BLUSH_14', v: 14, price: 50 },
		{ l: 'CC_BLUSH_15', v: 15, price: 50 },
		{ l: 'CC_BLUSH_16', v: 16, price: 50 },
		{ l: 'CC_BLUSH_17', v: 17, price: 50 },
		{ l: 'CC_BLUSH_18', v: 18, price: 50 },
		{ l: 'CC_BLUSH_19', v: 19, price: 50 },
		{ l: 'CC_BLUSH_20', v: 20, price: 50 },
		{ l: 'CC_BLUSH_21', v: 21, price: 50 },
		{ l: 'CC_BLUSH_22', v: 22, price: 50 },
		{ l: 'CC_BLUSH_23', v: 23, price: 50 },
		{ l: 'CC_BLUSH_24', v: 24, price: 50 },
		{ l: 'CC_BLUSH_25', v: 25, price: 50 },
		{ l: 'CC_BLUSH_26', v: 26, price: 50 },
		{ l: 'CC_BLUSH_27', v: 27, price: 50 },
		{ l: 'CC_BLUSH_28', v: 28, price: 50 },
		{ l: 'CC_BLUSH_29', v: 29, price: 50 },
		{ l: 'CC_BLUSH_30', v: 30, price: 50 },
		{ l: 'CC_BLUSH_31', v: 31, price: 50 },
		{ l: 'CC_BLUSH_32', v: 32, price: 50 }
    ],

	places: [
        { // 0
            id: 6,
			interior: 165377,
			animDict: 'misshair_shop@hair_dressers',
			interaction: {
				origin: new mp.Vector3(-817.9705, -184.5802, 36.56892),
				edge: new mp.Vector3(-812.0129, -181.2539, 38.56892),
				angle: 5.8
			},
			pedModel: 0x418DFF92, // 0x163B875B
			chair: {
				position: new mp.Vector3(-816.22, -182.97, 36.57),
				heading: -238.969
			},
			cam: {
				position: new mp.Vector3(-816.306, -182.914, 37.8927),
				offset: new mp.Vector3(0.586, -1.1006, -0.1042),
				heading: 180
			},
			scissorsPosition: new mp.Vector3(-815.86, -183.75, 36.5689),
			exit: { position: new mp.Vector3(-820.53, -186.87, 37.57), heading: 120 }
		},
        { // 1
            id: 4,
			interior: 198657,
			animDict: 'misshair_shop@barbers',
			interaction: {
				origin: new mp.Vector3(139.6911, -1706.982, 28.29159),
				edge: new mp.Vector3(136.3526, -1710.825, 30.30162),
				angle: 5.4
			},
			pedModel: 0x163B875B,
			chair: {
				position: new mp.Vector3(138.3646, -1709.252, 28.3182),
				heading: -399.97
			},
			cam: {
				position: new mp.Vector3(138.4104, -1709.326, 29.8074),
				offset: new mp.Vector3(1.0109, -0.8391, -0.1795),
				heading: 0
			},
			scissorsPosition: new mp.Vector3(135.0426, -1707.733, 28.2916),
			exit: { position: new mp.Vector3(134.51, -1710.22, 29.29), heading: 135 }
		},
        { // 2
            id: 7,
			interior: 171009,
			animDict: 'misshair_shop@barbers',
			interaction: {
				origin: new mp.Vector3(-1280.183, -1118.613, 5.99159),
				edge: new mp.Vector3(-1285.273, -1118.539, 8.001621),
				angle: 5.4
			},
			pedModel: 0x163B875B,
			chair: {
				position: new mp.Vector3(-1282.774, -1119.063, 6.018199),
				heading: -449.8173
			},
			cam: {
				position: new mp.Vector3(-1282.801, -1119.146, 7.5074),
				offset: new mp.Vector3(1.0109, -0.8391, -0.1795),
				heading: -49.84734
			},
			scissorsPosition: new mp.Vector3(-1283.755, -1115.544, 5.9916),
			exit: { position: new mp.Vector3(-1285.999, -1116.741, 6.990001), heading: 85.15266 }
		},
        { // 3
            id: 5,
			interior: 199937,
			animDict: 'misshair_shop@barbers',
			interaction: {
				origin: new mp.Vector3(1931.628, 3733.236, 31.84159),
				edge: new mp.Vector3(1934.09, 3728.78, 33.85162),
				angle: 5.4
			},
			pedModel: 0x163B875B,
			chair: {
				position: new mp.Vector3(1933.304, 3731.21, 31.8682),
				heading: -330.0692
			},
			cam: {
				position: new mp.Vector3(1933.389, 3731.227, 33.3574),
				offset: new mp.Vector3(1.0109, -0.8391, -0.1795),
				heading: 69.90086
			},
			scissorsPosition: new mp.Vector3(1930.736, 3728.612, 31.8416),
			exit: { position: new mp.Vector3(1932.889, 3727.257, 32.84), heading: 204.9009 }
		},
        { // 4
            id: 3,
			interior: 155905,
			animDict: 'misshair_shop@barbers',
			interaction: {
				origin: new mp.Vector3(1214.553, -474.7919, 65.20159),
				edge: new mp.Vector3(1209.653, -473.4121, 67.21162),
				angle: 5.4
			},
			pedModel: 0x163B875B,
			chair: {
				position: new mp.Vector3(1211.934, -474.5607, 65.2282),
				heading: -464.7142
			},
			cam: {
				position: new mp.Vector3(1211.887, -474.6337, 66.7174),
				offset: new mp.Vector3(1.0109, -0.8391, -0.1795),
				heading: -64.74422
			},
			scissorsPosition: new mp.Vector3(1211.891, -470.9081, 65.2016),
			exit: { position: new mp.Vector3(1209.414, -471.4875, 66.2), heading: 70.25578 }
		},
        { // 5
            id: 1,
			interior: 140545,
			animDict: 'misshair_shop@barbers',
			interaction: {
				origin: new mp.Vector3(-35.2074, -154.372, 56.07159),
				edge: new mp.Vector3(-33.38145, -149.6202, 58.08162),
				angle: 5.4
			},
			pedModel: 0x163B875B,
			chair: {
				position: new mp.Vector3(-34.73554, -151.7856, 56.0982),
				heading: -200.0077
			},
			cam: {
				position: new mp.Vector3(-34.80389, -151.7316, 57.5874),
				offset: new mp.Vector3(1.0109, -0.8391, -0.1795),
				heading: 199.9623
			},
			scissorsPosition: new mp.Vector3(-31.09458, -152.0791, 56.0716),
			exit: { position: new mp.Vector3(-31.44301, -149.5598, 57.07), heading: 334.9623 }
		},
        { // 6
            id: 2,
			interior: 180225,
			animDict: 'misshair_shop@barbers',
			interaction: {
				origin: new mp.Vector3(-277.3488, 6225.342, 30.69159),
				edge: new mp.Vector3(-280.9155, 6228.975, 32.70162),
				angle: 5.4
			},
			pedModel: 0x163B875B,
			chair: {
				position: new mp.Vector3(-279.5066, 6226.844, 30.7182),
				heading: -494.508
			},
			cam: {
				position: new mp.Vector3(-279.5841, 6226.805, 32.2074),
				offset: new mp.Vector3(1.0109, -0.8391, -0.1795),
				heading: -94.53801
			},
			scissorsPosition: new mp.Vector3(-277.7296, 6230.036, 30.6916),
			exit: { position: new mp.Vector3(-280.1666, 6230.764, 31.69), heading: 40.46199 }
		}
    ],

    prices: {
        'facialhair': [
            50, 80, 100, 150, 125, 100, 115, 200, 180, 170, 250, 350, 280, 230, 280,
            300, 260, 310, 130, 100, 220, 240, 190, 170, 180, 205, 260, 320, 280, 260, 260
        ],
        'eyebrows': [
            30, 80, 200, 250, 130, 120, 105, 90, 50, 70, 75, 150, 270, 230, 115, 120, 60, 150,
            290, 170, 180, 190, 285, 155, 310, 160, 170, 220, 40, 200, 320, 340, 90, 210, 170, 200
        ],
        'chesthair': [
            20, 300, 270, 280, 250, 210, 180, 195, 130, 350, 320, 420, 340, 330, 450, 140, 140, 140, 0
        ],
        'eyecolor': [
            300, 300, 300, 300, 300, 300, 500, 300, 650, 650, 650, 650, 300, 300, 500, 850,
            500, 850, 850, 850, 850, 850, 850, 850, 850, 850, 1250, 1250, 1250, 1250, 1250, 1250
        ],
        'blush': [
            30, 70, 70, 70, 70, 70, 250, 70, 150, 150, 90, 90, 90, 90, 90, 90, 90,
            90, 110, 70, 90, 80, 80, 120, 120, 120, 120, 50, 120, 120, 120, 170, 70, 90
        ],
        'face': [
            150, 500, 500, 500, 750, 900, 900, 300, 300, 350, 350, 500, 1100, 750, 1250, 1250, 1100, 1250, 1250, 1100, 1250, 1250,
            1250, 1100, 1100, 1100, 1100, 1100, 900, 1100, 900, 1100, 900, 1200, 1200, 1100, 1100, 1100, 1100, 1100, 1100, 900, 1100, 1100
        ],
        'makeup': [
            30, 70, 70, 70, 90, 70, 90, 110, 110, 70, 150, 150, 90,
            150, 220, 170, 220, 70, 250, 270, 70, 90, 70, 90, 70, 170
        ],
        'lipstick': [
            50, 300, 250, 320, 270, 340, 290, 280, 230, 70, 150
        ]
    }
};
