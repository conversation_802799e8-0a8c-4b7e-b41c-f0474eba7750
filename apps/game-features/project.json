{"name": "game-features", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/game-features/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/game-features", "main": "apps/game-features/src/main.ts", "tsConfig": "apps/game-features/tsconfig.app.json", "assets": ["apps/game-features/src/assets", {"glob": "*.node", "input": "node_modules/@prisma/engines", "output": "."}], "isolatedConfig": true, "webpackConfig": "apps/game-features/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "game-features:build"}, "configurations": {"development": {"buildTarget": "game-features:build:development"}, "production": {"buildTarget": "game-features:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/game-features/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/game-features/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "container": {"executor": "@nx-tools/nx-container:build", "dependsOn": ["build"], "defaultConfiguration": "development", "options": {"engine": "docker", "context": "dist/apps/game-features", "file": "apps/game-features/Dockerfile", "metadata": {"images": ["majestic-backend/game-features"]}, "push": false}, "configurations": {"production": {"push": false}}}}, "tags": []}