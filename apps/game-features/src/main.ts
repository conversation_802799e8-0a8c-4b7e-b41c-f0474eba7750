import { Logger, ValidationPipe } from '@nestjs/common'
import { NestFactory } from '@nestjs/core'

import { AppModule } from './app/app.module'
import { ConfigService } from '@nestjs/config'
import { Transport } from '@nestjs/microservices'
import {
  CommonConfig,
  ConfigModule,
  GameFeaturesServiceConfig,
  GameServersConfig,
  RedisConfig,
  WebDatabaseConfig,
} from '@majestic-backend/config'
import { LoggerService, nest } from '@majestic-backend/utils'

async function bootstrap() {
  await ConfigModule.init([
    GameServersConfig,
    WebDatabaseConfig,
    RedisConfig,
    CommonConfig,
    GameFeaturesServiceConfig,
  ])
  const configService = new ConfigService()
  const port = configService.get('GAME_FEATURES_PORT')
  const app = await NestFactory.create(AppModule)

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  )
  app.useGlobalFilters(new nest.ServiceExceptionsFilter('game-features'))

  app.connectMicroservice(
    {
      transport: Transport.TCP,
      options: {
        host: '0.0.0.0',
        port,
      },
    },
    { inheritAppConfig: true },
  )

  const metricsPort = app.get(GameFeaturesServiceConfig).metricsPort

  await app.startAllMicroservices()

  const logger = new LoggerService()
  logger.setLevel(configService.get('LOGGER_LEVEL'))
  app.useLogger(logger)

  await app.listen(metricsPort)
  Logger.log(
    `🚀 Game features service is running on port ${port}, metrics is running ${metricsPort}`,
  )
}

bootstrap()
