import { PrismaService, GameModelEnum } from '@majestic-backend/database'
import { Injectable } from '@nestjs/common'
import { AppBaseService } from '../app/app.base.service'
import { IUnitpayCreatePaymentPayload } from '@majestic-backend/types'
import { RedisService } from '@majestic-backend/redis'

@Injectable()
export class GameRewardsService extends AppBaseService {
  constructor(
    protected readonly database: PrismaService,
    private readonly redisService: RedisService,
  ) {
    super(database)
  }

  public async createAndAcceptPayment(serverId: string, payload: IUnitpayCreatePaymentPayload) {
    const unitpayPaymentModel = this.database.getGameModel(
      serverId,
      GameModelEnum.UnitpayPaymentModel,
    )

    const payment = await unitpayPaymentModel.createUnitpayPayment(payload)

    this.redisService.publish(`${serverId.toUpperCase()}.donateAcceptPayment`, {
      id: payment.id,
    })
  }
}
