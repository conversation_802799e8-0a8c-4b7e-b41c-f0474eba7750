import { PrismaService, IAccount } from '@majestic-backend/database'
import { common } from '@majestic-backend/errors'
import { nest } from '@majestic-backend/utils'

export class AppBaseService extends nest.BaseService {
  constructor(protected readonly databaseService: PrismaService) {
    super(databaseService)
  }

  protected _checkAccountValid(accounts: IAccount[], target: number) {
    if (!accounts.some(acc => acc.id === target)) {
      throw new Error(common.Errors.ACCOUNTS_NOT_FOUND)
    }
  }
}
