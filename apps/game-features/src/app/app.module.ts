import { Module } from '@nestjs/common'
import { PrismaModule } from '@majestic-backend/database'
import { MiniGamesModule } from '../mini-games/mini-games.module'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Controller, WheelController } from '../mini-games/controllers'
import { MetricsModule } from '@majestic-backend/metrics'
import { ConfigModule } from '@majestic-backend/config'
import { RedisModule } from '@majestic-backend/redis'

@Module({
  imports: [
    // TODO Refactor redis to static module to avoid global option
    RedisModule.forRoot({ global: true }),
    ConfigModule.forRoot(),
    PrismaModule,
    MiniGamesModule,
    {
      ...MetricsModule.forAppRoot('game-features'),
      imports: [PrismaModule],
    },
  ],
  controllers: [Crash<PERSON><PERSON>roller, JackpotController, WheelController],
  providers: [],
})
export class AppModule {}
