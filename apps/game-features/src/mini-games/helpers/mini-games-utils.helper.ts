import moment from 'moment'
import { IMiniGamePlayer, IGroupedRecievers } from '../types'

export function getRandomNumber(a = 1, b = 0, fixed = 4) {
  if (!(a % 1) && !(b % 1)) {
    // если нужно целое число
    const lower = Math.ceil(Math.min(a, b))
    const upper = Math.floor(Math.max(a, b))
    return Math.floor(lower + Math.random() * (upper - lower + 1))
  } else {
    // если нужно вернуть float
    const lower = Math.min(a, b)
    const upper = Math.max(a, b)
    const result = lower + Math.random() * (upper - lower)
    return parseFloat(result.toFixed(fixed))
  }
}

export const sample = (arr: number[]): number => {
  const len = arr == null ? 0 : arr.length
  return len ? arr[Math.floor(Math.random() * len)] : undefined
}

export async function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export function groupPlayersByServers(players: IMiniGamePlayer[]): IGroupedRecievers {
  return players.reduce((acc, player) => {
    const serverId = player.serverId

    if (!acc[serverId]) {
      acc[serverId] = []
    }
    acc[serverId].push(player.accountId)

    return acc
  }, {} as IGroupedRecievers)
}

const keys = ['betAmount', 'args', 'login', 'gender', 'accountId', 'serverId'] as const
export const pickCurrentGamePlayersData = <T extends Record<(typeof keys)[number], any>>(
  obj: T,
  serverName: string,
): Pick<T, (typeof keys)[number]> & { serverName: string } => {
  return {
    ...(Object.fromEntries(
      keys.map(key => [
        key,
        key === 'args'
          ? obj.args.wheelBets
            ? JSON.stringify(obj.args.wheelBets)
            : JSON.stringify(obj[key])
          : obj[key],
      ]),
    ) as Pick<T, (typeof keys)[number]>),
    serverName: serverName.toLowerCase(),
  }
}

const keysCrashBets = ['bet', 'accountId', 'exitX', 'serverId', 'login', 'gender'] as const
export const pickCrashBetsData = <T extends Record<(typeof keysCrashBets)[number], any>>(
  obj: T,
  serverName: string,
): Pick<T, (typeof keysCrashBets)[number]> & { serverName: string } => {
  return {
    ...(Object.fromEntries(keysCrashBets.map(key => [key, obj[key]])) as Pick<
      T,
      (typeof keysCrashBets)[number]
    >),
    serverName: serverName.toLowerCase(),
  }
}

const keysJackpotBets = ['amount', 'accountId', 'ticket', 'serverId', 'login', 'gender'] as const
export const pickJackpotBetsData = <T extends Record<(typeof keysJackpotBets)[number], any>>(
  obj: T,
  serverName: string,
): Pick<T, (typeof keysJackpotBets)[number]> & { serverName: string } => {
  return {
    ...(Object.fromEntries(keysJackpotBets.map(key => [key, obj[key]])) as Pick<
      T,
      (typeof keysJackpotBets)[number]
    >),
    serverName: serverName.toLowerCase(),
  }
}

const keysWheelBets = ['amount', 'accountId', 'color', 'serverId', 'login', 'gender'] as const
export const pickWheelBetsData = <T extends Record<(typeof keysWheelBets)[number], any>>(
  obj: T,
  serverName: string,
): Pick<T, (typeof keysWheelBets)[number]> & { serverName: string } => {
  return {
    ...(Object.fromEntries(keysWheelBets.map(key => [key, obj[key]])) as Pick<
      T,
      (typeof keysWheelBets)[number]
    >),
    serverName: serverName.toLowerCase(),
  }
}

export const isServerRestarting = () => {
  const now = moment().format('HH:mm')

  return now >= '02:58' && now <= '03:05'
}
