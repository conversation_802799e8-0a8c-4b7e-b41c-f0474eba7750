import { Modu<PERSON> } from '@nestjs/common'
import { PrismaModule } from '@majestic-backend/database'
import { RedisLockService } from '@majestic-backend/redis'
import { CrashService, GameService, JackpotService, PlayersService, WheelService } from './services'
import { GameRewardsModule } from '../game-rewards/game-rewards.module'
import { UtilsService } from './services/utils.service'
import { RedisListenerService } from './services/redis-listener.service'
import { ScheduleModule } from '@nestjs/schedule'
import { MinigamesSchedule } from './services/minigames.schedule'

@Module({
  imports: [PrismaModule, GameRewardsModule, ScheduleModule.forRoot()],
  providers: [
    CrashService,
    JackpotService,
    WheelService,
    UtilsService,
    RedisListenerService,
    RedisLockService,
    PlayersService,
    GameService,
    MinigamesSchedule,
  ],
  exports: [CrashService, JackpotService, WheelService],
})
export class MiniGamesModule {}
