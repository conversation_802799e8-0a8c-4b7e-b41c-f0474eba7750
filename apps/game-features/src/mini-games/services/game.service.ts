import { Injectable, Logger } from '@nestjs/common'
import { AppBaseService } from '../../app/app.base.service'
import { BackendModelEnum, PrismaService } from '@majestic-backend/database'
import { mini_games_name } from '@majestic-backend/prisma-client-backend'
import { IMiniGamesGamesUpdatePayload } from '../types'

@Injectable()
export class GameService extends AppBaseService {
  private logger = new Logger(GameService.name)

  constructor(protected readonly databaseService: PrismaService) {
    super(databaseService)
  }

  async getMiniGameStateWithArgs(minigameName: mini_games_name) {
    const minigamesGamesModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesGamesModel,
    )

    const state = await minigamesGamesModel.getMiniGamesGame({
      where: {
        minigameName,
      },
    })
    const stateArgs = JSON.parse(state.args)

    return { state, stateArgs }
  }

  async updateMiniGameState(
    id: number,
    minigameName: mini_games_name,
    data: IMiniGamesGamesUpdatePayload,
  ) {
    const minigamesGamesModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesGamesModel,
    )

    const state = await minigamesGamesModel.updateMiniGamesGame({
      where: {
        id,
        minigameName,
      },
      data,
    })
    const stateArgs = JSON.parse(state.args)

    return { state, stateArgs }
  }

  async setGameIsActive(
    id: number,
    minigameName: mini_games_name,
    data: IMiniGamesGamesUpdatePayload,
  ) {
    const minigamesGamesModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesGamesModel,
    )

    const { stateArgs } = await this.getMiniGameStateWithArgs(minigameName)

    if (!('isPausedDueToRestart' in stateArgs)) {
      this.logger.debug(
        `[startGame] minigame stopped by admin. Not turn on. Minigame: ${minigameName}`,
      )
      return
    }

    this.logger.debug(
      `[startGame] minigame turning on after server restarted. Minigame: ${minigameName}`,
    )
    delete stateArgs.isPausedDueToRestart

    await minigamesGamesModel.updateMiniGamesGame({
      where: {
        id,
        minigameName,
      },
      data: {
        ...data,
        args: JSON.stringify(stateArgs),
      },
    })

    return
  }
}
