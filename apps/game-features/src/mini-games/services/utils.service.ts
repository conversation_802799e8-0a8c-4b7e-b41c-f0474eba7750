import { Injectable, Logger } from '@nestjs/common'
import { AppBaseService } from '../../app/app.base.service'
import { BackendModelEnum, PrismaService, GameModelEnum } from '@majestic-backend/database'
import { RedisService } from '@majestic-backend/redis'
import { CLIENT_MESSAGES_TYPES } from '@majestic-backend/types'
import moment from 'moment'
import { ICreateDonateLog, ICreateMiniGamesLog } from '../types'
import { MiniGamesNames } from '../enum'
import { mini_games_name } from '@majestic-backend/prisma-client-backend'

@Injectable()
export class UtilsService extends AppBaseService {
  private logger = new Logger(UtilsService.name)

  constructor(
    protected readonly databaseService: PrismaService,
    private readonly redisService: RedisService,
  ) {
    super(databaseService)
  }

  async longNotifyTrans(characterId: number, serverId: string, text: string, messageType: string) {
    await this.redisService.publishToSockets({
      receivers: characterId,
      serverId,
      type: CLIENT_MESSAGES_TYPES.MAIN_SHOW_NOTIFY,
      payload: { text, messageType, duration: 5000 },
    })

    await this.redisService.publishToSockets({
      receivers: characterId,
      serverId,
      type: CLIENT_MESSAGES_TYPES.NOTIFICATION_ERROR,
      payload: { volume: 25 },
    })
  }

  async createDonateLog(serverId: string, payload: ICreateDonateLog) {
    const donateLogModel = this.databaseService.getGameModel(serverId, GameModelEnum.DonateLogModel)

    await donateLogModel.createDonateLog({
      ...payload,
      date: moment().format('YYYY-MM-DD HH:mm:ss'),
      args: null,
    })
  }

  async createMinigamesF2Log(payload: ICreateMiniGamesLog) {
    const minigamesF2LogModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesF2LogModel,
    )

    await minigamesF2LogModel.createMiniGamesF2Log({
      ...payload,
      args: payload.args ? JSON.stringify(payload.args) : null,
      date: moment().format('YYYY-MM-DD HH:mm:ss'),
    })
  }

  async isGameHandledElsewhere(minigameName: MiniGamesNames) {
    const lastUpdate = await this.redisService.get(`minigame:${minigameName}:lastUpdate`)
    if (!lastUpdate) return false

    const now = new Date().getTime()

    return now - Number(lastUpdate) < 10000
  }

  async removeRestartedMinigameBets(minigameName: mini_games_name, gameId: number) {
    switch (minigameName) {
      case 'crash':
        const crashBetsModel = this.databaseService.getBackendModel(BackendModelEnum.CrashBetsModel)

        await crashBetsModel.deleteCrashBets({
          where: {
            gameId,
          },
        })
        return
      case 'jackpot':
        const jackpotBetsModel = this.databaseService.getBackendModel(
          BackendModelEnum.JackpotBetsModel,
        )

        await jackpotBetsModel.deleteJackpotBets({
          where: {
            gameId,
          },
        })
        return
      case 'wheel':
        const wheelBetsModel = this.databaseService.getBackendModel(BackendModelEnum.WheelBetsModel)

        await wheelBetsModel.deleteWheelBets({
          where: {
            gameId,
          },
        })
        return
    }
  }
}
