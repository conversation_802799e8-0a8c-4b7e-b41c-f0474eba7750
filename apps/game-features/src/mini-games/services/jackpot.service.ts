import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { AppBaseService } from '../../app/app.base.service'
import { BackendModelEnum, PrismaService } from '@majestic-backend/database'
import { RedisLockService, RedisService } from '@majestic-backend/redis'
import moment from 'moment'
import { IUser, MINI_GAMES_MESSAGES_TYPES, OperationType } from '@majestic-backend/types'
import {
  getRandomNumber,
  isServerRestarting,
  pickCurrentGamePlayersData,
  pickJackpotBetsData,
  sample,
  sleep,
} from '../helpers'
import { UtilsService } from './utils.service'
import {
  MiniGamesAddPlayerJackpot,
  MiniGamesBetJackpot,
  MiniGamesGetDataJackpot,
  MiniGamesGetHistoryItemJackpot,
  MiniGamesGetHistoryListJackpot,
  MiniGamesRemovePlayerJackpot,
} from '@majestic-backend/contracts'
import { MiniGamesNames, MiniGamesDonateType, MiniGamesGameId } from '../enum'
import { IMiniGamesGamesUpdatePayload, IMiniGameState } from '../types'
import { GameService } from './game.service'
import { PlayersService } from './players.service'
import { IJackpotFullBet, IJackpotStateArgs } from '../types/jackpot.interface'
import { mini_games_name } from '@majestic-backend/prisma-client-backend'
import {
  JackpotStateDate,
  MinigamesBetAmounts,
  MinigamesMaxPlayers,
} from '@shared_config/minigames/state'
import { GameServersConfig } from '@majestic-backend/config'

@Injectable()
export class JackpotService extends AppBaseService implements OnModuleInit {
  private logger = new Logger(JackpotService.name)

  private minimumPlayers = 2
  private historyLimit = 20

  private state: IMiniGameState
  private stateArgs: IJackpotStateArgs

  private initLockKey = 'jackpot:init:lock'
  private initLockTTL = 5
  private startGameLockKey = 'jackpot:startGame:lock'
  private startGameLockTTL = 10
  private lastUpdateKey = 'minigame:jackpot:lastUpdate'

  constructor(
    protected readonly databaseService: PrismaService,
    private readonly redisService: RedisService,
    private readonly utilsService: UtilsService,
    private readonly playersService: PlayersService,
    private readonly gameService: GameService,
    private readonly redisLockService: RedisLockService,
    private readonly gameServersConfig: GameServersConfig,
  ) {
    super(databaseService)
  }

  async onModuleInit() {
    const initLockAcquired = await this.redisLockService.acquireLock(this.initLockKey)

    if (initLockAcquired) {
      this.logger.debug('Jackpot init lock acquired. Start init()')

      await this.init()
      await this.redisLockService.releaseLock(this.initLockKey)

      this.logger.debug('Jackpot data initialized')
    } else {
      this.logger.debug('Jackpot init lock not acquired. Waiting for initialization to complete')
      await this.waitForCompleteInit(this.initLockKey)
    }

    this.startGameLoop()
  }

  private async getState() {
    const { state, stateArgs } = await this.gameService.getMiniGameStateWithArgs(
      mini_games_name.jackpot,
    )

    this.state = state
    this.stateArgs = stateArgs
  }

  private async updateState(data: IMiniGamesGamesUpdatePayload) {
    await this.getState()
    const { state, stateArgs } = await this.gameService.updateMiniGameState(
      MiniGamesGameId.JACKPOT,
      mini_games_name.jackpot,
      data,
    )

    this.state = state
    this.stateArgs = stateArgs
  }

  async init() {
    const jackpotGamesModel = this.databaseService.getBackendModel(
      BackendModelEnum.JackpotGamesModel,
    )
    const jackpotBetsModel = this.databaseService.getBackendModel(BackendModelEnum.JackpotBetsModel)
    const jackpotLastGame = await jackpotGamesModel.getJackpotGame({
      orderBy: {
        id: 'desc',
      },
    })

    await this.getState()
    await this.updateState({
      gameId: ((jackpotLastGame && jackpotLastGame.id) || 0) + 1,
      isActive: !this.state.isActive ? false : true,
      isTimerActive: false,
      timerTimeStart: null,
      isGameActive: false,
      gameTimeStart: null,
      args: JSON.stringify({
        ...this.stateArgs,
        winner: null,
        winnerPosition: null,
      }),
    })
    this.logger.debug(`init gameId: ${this.state.gameId}`)

    await this.updateLuckers()

    const lastGame = await jackpotGamesModel.getJackpotGame({
      where: {
        id: this.state.gameId,
      },
    })

    if (lastGame && lastGame.winnerLogin === null) {
      const currentGameBets = await jackpotBetsModel.getJackpotBets({
        where: {
          gameId: this.state.gameId,
        },
      })

      for (const bet of currentGameBets) {
        await this.playersService.updateMiniGamePlayer(bet.userId, mini_games_name.jackpot, {
          betAmount: bet.amount,
          args: {
            ticket: bet.ticket,
            dateBet: bet.dateBet,
          },
        })
      }
    }
  }

  async waitForCompleteInit(initLockKey: string) {
    while (await this.redisService.exists(initLockKey)) {
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    this.logger.debug('Jackpot init completed by another replica')
  }

  async startGameLoop() {
    const tryStartGame = async () => {
      if (await this.utilsService.isGameHandledElsewhere(MiniGamesNames.JACKPOT)) {
        this.logger.debug(`[startGameLoop] Game is handled by other replica. Try start game later`)
        setTimeout(() => {
          tryStartGame()
        }, 5000)
        return
      }

      this.logger.debug(`[startGameLoop] Game is not handled by anyone. Try lock`)
      const lockAcquired = await this.redisLockService.acquireLock(this.startGameLockKey)

      if (!lockAcquired) {
        this.logger.debug(`[startGameLoop] Other replica acquired lock. Try start game later`)
        setTimeout(() => {
          tryStartGame()
        }, 5000)
        return
      }

      await this.redisService.set(this.lastUpdateKey, new Date().getTime())
      const updateActiveJackpot = setInterval(async () => {
        await this.redisService.set(this.lastUpdateKey, new Date().getTime())
      }, 2000)

      try {
        this.logger.debug(`[startGameLoop] Lock acquired`)
        await this.getState()
        if (this.state.isGameInProgress) {
          this.logger.debug(`[startGameLoop] Last game not ended. Refund bets`)
          await this.playersService.refundBets(mini_games_name.jackpot)
          this.logger.debug(`[startGameLoop] Clear current game players`)
          await this.playersService.clearCurrentGamePlayers(mini_games_name.jackpot)
          this.logger.debug(`[startGameLoop] Remove restarted game bets`)
          await this.utilsService.removeRestartedMinigameBets(
            mini_games_name.jackpot,
            this.state.gameId,
          )

          await this.updateState({
            isGameInProgress: false,
          })

          await this.getState()
          const luck = await this.getLuckInfo()
          await this.playersService.sendUpdatesToPlayers(
            mini_games_name.jackpot,
            MINI_GAMES_MESSAGES_TYPES.JACKPOT_START_TIMER,
            {
              state: this.state,
              currentGame: {
                id: this.state.gameId,
                currentGamePlayers: [],
              },
              luck,
            },
          )

          const players = await this.playersService.getMiniGamePlayers(mini_games_name.jackpot)
          for (const player of players) {
            await this.utilsService.longNotifyTrans(
              player.accountId,
              player.serverId,
              'fun.minigamesF2.errors.gameRestarted',
              'error',
            )
          }
        }

        this.logger.debug(`[startGameLoop] Release lock and start game`)
        await this.redisLockService.releaseLock(this.startGameLockKey)
        await this.startGame()
      } finally {
        this.logger.debug(`[startGameLoop] Clear interval and delete lastUpdate`)
        clearInterval(updateActiveJackpot)
        await this.redisService.del(this.lastUpdateKey)
      }

      tryStartGame()
    }

    tryStartGame()
  }

  // async updateLastGameID() {
  //   const jackpotGamesModel = this.databaseService.getBackendModel(
  //     BackendModelEnum.JackpotGamesModel,
  //   )

  //   const jackpotLastGame = await jackpotGamesModel.getJackpotGame({
  //     orderBy: {
  //       id: 'desc',
  //     },
  //   })

  //   await this.getState()
  //   await this.updateState({
  //     gameId: ((jackpotLastGame && jackpotLastGame.id) || 0) + 1,
  //     isActive: !this.state.isActive ? false : true,
  //     isTimerActive: false,
  //     timerTimeStart: null,
  //     isGameActive: false,
  //     gameTimeStart: null,
  //     args: JSON.stringify({
  //       winner: null,
  //       winnerPosition: null,
  //     }),
  //   })
  // }

  async updateLuckers() {
    const luckInfo = await this.getLuckInfo()

    const players = await this.playersService.getMiniGamePlayers(mini_games_name.jackpot)
    if (players.length) {
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.jackpot,
        MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_LUCKERS_RESULT,
        {
          data: luckInfo,
        },
      )
    }
  }

  private async getLuckInfo() {
    const jackpotGamesModel = this.databaseService.getBackendModel(
      BackendModelEnum.JackpotGamesModel,
    )

    const [jackpotLastGame, jackpotLucker, jackpotPlayer] = await Promise.all([
      jackpotGamesModel.getJackpotGame({
        orderBy: {
          id: 'desc',
        },
      }),
      jackpotGamesModel.getJackpotGame({
        where: {
          dateGame: {
            gte: moment().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
          },
        },
        orderBy: {
          winnerChance: 'asc',
        },
      }),
      jackpotGamesModel.getJackpotGame({
        where: {
          dateGame: {
            gte: moment().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
          },
        },
        orderBy: {
          bank: 'desc',
        },
      }),
    ])

    return {
      lastGame: jackpotLastGame
        ? {
            ...jackpotLastGame,
            serverName: this.gameServersConfig.gameServers
              .get(jackpotLastGame.winnerServerId)
              .NAME.toLowerCase(),
          }
        : null,
      lucker: jackpotLucker
        ? {
            ...jackpotLucker,
            serverName: this.gameServersConfig.gameServers
              .get(jackpotLucker.winnerServerId)
              .NAME.toLowerCase(),
          }
        : null,
      jackpot: jackpotPlayer
        ? {
            ...jackpotPlayer,
            serverName: this.gameServersConfig.gameServers
              .get(jackpotPlayer.winnerServerId)
              .NAME.toLowerCase(),
          }
        : null,
    }
  }

  private getArrayBetUsers(bets: IJackpotFullBet[]): number[] {
    const winChances: number[] = []

    bets.forEach(bet => {
      for (let i = 0; i < bet.amount; i++) {
        winChances.push(bet.userId)
      }
    })
    return winChances
  }

  private getSumAllBets(bets: IJackpotFullBet[]): number {
    return bets.reduce((sum, bet) => bet.amount + sum, 0)
  }

  private getWinChangeByUserId(bets: IJackpotFullBet[], userId: number) {
    return (bets.find(bet => bet.userId === userId).amount / this.getSumAllBets(bets)) * 100
  }

  private generateUniqueTicket() {
    return `${getRandomNumber(10, 99)}-${getRandomNumber(100, 999)}`
  }

  private getRandomCardIndex(winChance: number) {
    const countItem = Math.round(winChance / 10)
    let count = 0

    if (countItem <= 1) {
      count += 1
    } else {
      count += countItem
    }

    return getRandomNumber(0, count - 1)
  }

  async startGame() {
    await this.getState()

    if (this.state.isActive && isServerRestarting()) {
      this.logger.debug(`[startGame] Server is restarting. GameId: ${this.state.gameId}`)
      await this.updateState({
        isActive: false,
        args: JSON.stringify({
          ...this.stateArgs,
          isPausedDueToRestart: true,
        }),
      })

      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.jackpot,
        MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_GAME,
        {
          state: { isActive: false },
        },
      )
    }

    if (!this.state.isActive) {
      this.logger.debug(`[startGame] Current game is not active. GameId: ${this.state.gameId}`)
      await sleep(5000)

      return
    }

    let currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.jackpot,
    )
    if (currentGamePlayers.length < this.minimumPlayers) {
      await sleep(1000)

      return
    }
    this.logger.debug(`============ jackpot start game ============`)

    await this.updateState({
      isTimerActive: true,
      timerTimeStart: new Date(),
      isGameInProgress: true,
    })

    let players = await this.playersService.getMiniGamePlayers(mini_games_name.jackpot)
    if (players.length) {
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.jackpot,
        MINI_GAMES_MESSAGES_TYPES.JACKPOT_START_TIMER,
        { state: this.state },
      )
    }

    this.logger.debug(`[startGame] Start timer init time. GameId: ${this.state.gameId}`)
    await sleep(JackpotStateDate.timerInitTime)

    await this.updateState({
      isTimerActive: false,
      isGameActive: true,
      gameTimeStart: new Date(),
    })

    currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.jackpot,
    )

    const jackpotBetsModel = this.databaseService.getBackendModel(BackendModelEnum.JackpotBetsModel)
    const gameBets = await jackpotBetsModel.getJackpotBets({
      where: {
        gameId: this.state.gameId,
      },
    })

    const winnerId = sample(this.getArrayBetUsers(gameBets))
    const winCoins = this.getSumAllBets(gameBets) - Math.trunc(this.getSumAllBets(gameBets) / 100)
    const winChance = this.getWinChangeByUserId(gameBets, winnerId)
    const winnerCardIndex = this.getRandomCardIndex(winChance)
    this.logger.debug(
      `[startGame] WinnerId: ${winnerId}. WinCoins: ${winCoins}. WinChance: ${winChance}. WinnerCardIndex: ${winnerCardIndex}. GameId: ${this.state.gameId}`,
    )

    const winnerPlayer = gameBets.find(bet => bet.userId === winnerId)

    const randomPosition = getRandomNumber(0.1, 0.9)
    this.logger.debug(
      `[startGame] random winnerPosition: ${randomPosition}. GameId: ${this.state.gameId}`,
    )

    await this.updateState({
      args: JSON.stringify({
        winnerPosition: randomPosition,
        winner: {
          ...winnerPlayer,
          betAmount: winnerPlayer.amount,
          args: JSON.stringify({ ticket: winnerPlayer.ticket }),
          winnerPosition: randomPosition,
          winCoins: winCoins,
          winChance: winChance,
          winnerCardIndex,
          serverName: this.gameServersConfig.gameServers
            .get(winnerPlayer.serverId)
            .NAME.toLowerCase(),
        },
      }),
    })

    players = await this.playersService.getMiniGamePlayers(mini_games_name.jackpot)
    if (players.length) {
      const filteredCurrentGamePlayersData = currentGamePlayers.map(item =>
        pickCurrentGamePlayersData(
          item,
          this.gameServersConfig.gameServers.get(item.serverId).NAME,
        ),
      )
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.jackpot,
        MINI_GAMES_MESSAGES_TYPES.JACKPOT_START_GAME,
        {
          state: this.state,
          currentGame: {
            id: this.state.gameId,
            currentGamePlayers: filteredCurrentGamePlayersData,
          },
          winner: this.stateArgs.winner,
        },
      )
    }

    this.logger.debug(`[startGame] Start timer game time. GameId: ${this.state.gameId}`)
    await sleep(JackpotStateDate.gameTime)

    await this.getState()
    currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.jackpot,
    )

    this.logger.debug(
      `[takeJackpot] start increase player donate. AccountId: ${winnerPlayer.accountId}. GameId: ${this.state.gameId}`,
    )
    const serverResponse = await this.playersService.updatePlayerDonate({
      userId: winnerPlayer.userId,
      accountId: winnerPlayer.accountId,
      serverId: winnerPlayer.serverId,
      betAmount: winCoins,
      type: OperationType.INCREASE,
      comment: 'logs.payment_log.minigamesF2',
      args: JSON.stringify({
        type: 'jackpot',
        sessionId: this.state.gameId,
        serverId: winnerPlayer.serverId,
      }),
    })
    // const serverResponse = { data: { success: true } }
    this.logger.debug(
      `[takeJackpot] end increase player donate. Response: ${JSON.stringify(
        serverResponse,
      )}. AccountId: ${winnerPlayer.accountId}. GameId: ${this.state.gameId}`,
    )

    if (!serverResponse || !serverResponse.data.success) {
      this.logger.debug(
        `[takeJackpot] Not got server PRC response. AccountId: ${winnerPlayer.accountId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        winnerPlayer.accountId,
        winnerPlayer.serverId,
        'fun.minigamesF2.errors.serverNotResponse',
        'error',
      )
      return { ok: false }
    }

    this.logger.debug(
      `[startGame] Set player got reward. JackpotGameId: ${this.state.gameId}. AccountId: ${winnerPlayer.accountId}`,
    )
    await this.playersService.updateMiniGamePlayer(winnerPlayer.userId, mini_games_name.jackpot, {
      gotReward: true,
    })
    currentGamePlayers.map(async player => {
      await this.playersService.updateMiniGamePlayer(player.userId, mini_games_name.jackpot, {
        gotReward: true,
      })
    })

    this.logger.debug(`[startGame] Creating minigamesF2 log. GameId: ${this.state.gameId}`)
    await this.utilsService.createMinigamesF2Log({
      userId: winnerPlayer.userId,
      accountId: winnerPlayer.accountId,
      game: MiniGamesNames.JACKPOT,
      type: MiniGamesDonateType.WIN,
      amount: winCoins,
      gameId: this.state.gameId,
      args: this.stateArgs.winner,
      serverId: winnerPlayer.serverId,
    })

    const jackpotGamesModel = this.databaseService.getBackendModel(
      BackendModelEnum.JackpotGamesModel,
    )
    this.logger.debug(`[startGame] Creating jackpot game. GameId: ${this.state.gameId}`)
    const jackpotEndGame = await jackpotGamesModel.createJackpotGame({
      playersCount: currentGamePlayers.length,
      winnerId,
      bank: winCoins,
      winnerPosition: randomPosition,
      winnerChance: parseFloat(winChance.toFixed(2)),
      winnerSocialClub: winnerPlayer.socialClub,
      winnerLogin: winnerPlayer.login,
      winnerCardIndex,
      winnerAccountId: winnerPlayer.accountId,
      winnerGender: winnerPlayer.gender,
      winnerServerId: winnerPlayer.serverId,
      winnerAmount: winnerPlayer.amount,
      winnerTicket: winnerPlayer.ticket,
    })

    this.logger.debug(`[startGame] Start update luckers. GameId: ${this.state.gameId}`)
    this.updateLuckers()
    this.logger.debug(`[startGame] End update luckers. GameId: ${this.state.gameId}`)

    players = await this.playersService.getMiniGamePlayers(mini_games_name.jackpot)
    if (players.length) {
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.jackpot,
        MINI_GAMES_MESSAGES_TYPES.JACKPOT_END_GAME,
        {},
      )
    }

    await this.updateState({
      isGameActive: false,
    })

    this.logger.debug(`[startGame] Start game time end. GameId: ${this.state.gameId}`)
    await sleep(JackpotStateDate.gameTimeEnd)

    await this.updateState({
      args: JSON.stringify({
        winnerPosition: null,
        winner: null,
      }),
    })

    this.logger.debug(`[startGame] Set isGameInProgress to false. GameId: ${this.state.gameId}`)
    await this.updateState({
      isGameInProgress: false,
    })
    this.logger.debug(`[startGame] Clearing current game players. GameId: ${this.state.gameId}`)
    await this.playersService.clearCurrentGamePlayers(mini_games_name.jackpot)

    await sleep(1000)

    this.logger.debug(`[startGame] Ending game.... GameId: ${this.state.gameId}`)

    // await this.updateLastGameID()
    await this.updateState({
      gameId: jackpotEndGame.id + 1,
      isTimerActive: false,
      timerTimeStart: null,
      isGameActive: false,
      gameTimeStart: null,
      args: JSON.stringify({
        winner: null,
        winnerPosition: null,
      }),
    })

    await this.getState()

    currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.jackpot,
    )
    players = await this.playersService.getMiniGamePlayers(mini_games_name.jackpot)
    const filteredCurrentGamePlayersData = currentGamePlayers.map(item =>
      pickCurrentGamePlayersData(item, this.gameServersConfig.gameServers.get(item.serverId).NAME),
    )
    if (players.length) {
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.jackpot,
        MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_GAME,
        {
          state: this.state,
          currentGame: {
            id: this.state.gameId,
            currentGamePlayers: filteredCurrentGamePlayersData,
          },
        },
      )
    }

    this.logger.debug(`[startGame] Removing non active players`)
    await this.playersService.removeNonActivePlayers(mini_games_name.jackpot)

    await sleep(100)
    this.logger.debug(`============ jackpot end game ============`)

    return
  }

  async addPlayer(player: IUser, data: Omit<MiniGamesAddPlayerJackpot.Request, 'session'>) {
    const { state } = await this.gameService.getMiniGameStateWithArgs(mini_games_name.jackpot)
    if (!state.isActive) {
      this.logger.debug(
        `[addPlayer] Trying add player to jackpot when state is not active. AccountId: ${data.characterId}. GameId: ${state.gameId}`,
      )
      await this.redisService.publishToSockets({
        receivers: data.characterId,
        serverId: player.serverId,
        type: MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_GAME,
        payload: {
          state: { isActive: false },
        },
      })

      return 'gameNotActive'
    }

    this.logger.debug(
      `Start ADD player to all jackpot player list. CharacterId: ${data.characterId}. GameId: ${state.gameId}`,
    )

    await this.getState()
    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.jackpot,
    )
    const luck = await this.getLuckInfo()

    const filteredCurrentGamePlayersData = currentGamePlayers.map(item =>
      pickCurrentGamePlayersData(item, this.gameServersConfig.gameServers.get(item.serverId).NAME),
    )
    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_GAME,
      payload: {
        state,
        currentGame: { id: state.gameId, currentGamePlayers: filteredCurrentGamePlayersData },
        luck,
      },
    })

    const players = await this.playersService.getMiniGamePlayers(mini_games_name.jackpot)
    if (
      players.some(
        player =>
          player.accountId === data.characterId && player.minigameName === mini_games_name.jackpot,
      )
    ) {
      await this.playersService.updateMiniGamePlayer(player.user.id, mini_games_name.jackpot, {
        isActive: true,
      })
      this.logger.debug(
        `Player return to game. Set player is active. AccountId: ${data.characterId}. GameId: ${state.gameId}`,
      )
      return 'ok'
    }

    const currentAccount = player.accounts.find(account => account.id === data.characterId)
    await this.playersService.createMiniGamePlayer({
      minigameName: mini_games_name.jackpot,
      userId: player.user.id,
      accountId: data.characterId,
      serverId: player.serverId,
      login: currentAccount.login,
      socialClub: player.user.lastSocialClub,
      args: { ticket: null, dateBet: null },
      isActive: true,
      gender: currentAccount.gender,
    })

    this.logger.debug(
      `Added player to all jackpot player list. CharacterId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    return 'ok'
  }

  async removePlayer(player: IUser, data: Omit<MiniGamesRemovePlayerJackpot.Request, 'session'>) {
    await this.getState()
    const playerInGame = await this.playersService.getMiniGamePlayer(
      mini_games_name.jackpot,
      data.characterId,
    )

    this.logger.debug(
      `Start REMOVE player from all jackpot player list. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    if (!playerInGame) {
      this.logger.debug(
        `Player not found in jackpot player list. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      return 'notFound'
    }

    if (!playerInGame.inCurrentGame && !playerInGame.isBetInProgress) {
      await this.playersService.removeMiniGamePlayer(
        player.user.id,
        data.characterId,
        mini_games_name.jackpot,
      )
      this.logger.debug(
        `Removed player from all jackpot player list. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
    } else {
      await this.playersService.updateMiniGamePlayer(player.user.id, mini_games_name.jackpot, {
        isActive: false,
      })
      this.logger.debug(
        `Set player is non active. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
    }

    return 'ok'
  }

  async getDataJackpot(player: IUser, data: Omit<MiniGamesGetDataJackpot.Request, 'session'>) {
    this._checkAccountValid(player.accounts, data.characterId)

    const { state } = await this.gameService.getMiniGameStateWithArgs(mini_games_name.jackpot)
    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.jackpot,
    )
    const luck = await this.getLuckInfo()

    const filteredCurrentGamePlayersData = currentGamePlayers.map(item =>
      pickCurrentGamePlayersData(item, this.gameServersConfig.gameServers.get(item.serverId).NAME),
    )
    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_GAME,
      payload: {
        state,
        currentGame: { id: state.gameId, currentGamePlayers: filteredCurrentGamePlayersData },
        luck,
      },
    })

    return {
      state,
      currentGame: { id: state.gameId, currentGamePlayers },
      luck,
    }
  }

  async getJackpotHistoryList(
    player: IUser,
    data: Omit<MiniGamesGetHistoryListJackpot.Request, 'session'>,
  ) {
    this._checkAccountValid(player.accounts, data.characterId)

    const jackpotGamesModel = this.databaseService.getBackendModel(
      BackendModelEnum.JackpotGamesModel,
    )
    const jackpotBetsModel = this.databaseService.getBackendModel(BackendModelEnum.JackpotBetsModel)

    const jackpotLastGames = await jackpotGamesModel.getJackpotGames({
      orderBy: {
        id: 'desc',
      },
      take: this.historyLimit,
    })
    const jackpotLastGamesGameIds = jackpotLastGames.map(game => game.id)
    const jackpotLastGamesGameWinnerSC = jackpotLastGames.map(game => game.winnerSocialClub)

    const jackpotLastGamesBets = await jackpotBetsModel.getJackpotBets({
      where: {
        AND: [
          { gameId: { in: jackpotLastGamesGameIds } },
          { socialClub: { in: jackpotLastGamesGameWinnerSC } },
        ],
      },
    })

    const joinGamesAndBets = jackpotLastGames.map(game => {
      const winnerBet = jackpotLastGamesBets.find(
        bet => bet.gameId === game.id && bet.socialClub === game.winnerSocialClub,
      )

      return { ...game, winnerBet }
    })

    const mapGamesAndBets = joinGamesAndBets.map(game => {
      return {
        bank: game.bank,
        id: game.id,
        winnerChance: game.winnerChance,
        winnerLogin: game.winnerLogin,
        winnerSocialClub: game.winnerSocialClub,
        winnerAmount: game.winnerBet.amount,
        winnerTicket: game.winnerBet.ticket,
        winnerGender: game.winnerGender,
        winnerServerName: this.gameServersConfig.gameServers
          .get(game.winnerServerId)
          .NAME.toLowerCase(),
        winnerAccountId: game.winnerAccountId,
      }
    })

    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_HISTORY_LIST,
      payload: { data: mapGamesAndBets },
    })

    return joinGamesAndBets
  }

  async getJackpotHistoryItem(
    player: IUser,
    data: Omit<MiniGamesGetHistoryItemJackpot.Request, 'session'>,
  ) {
    this._checkAccountValid(player.accounts, data.characterId)

    const jackpotGamesModel = this.databaseService.getBackendModel(
      BackendModelEnum.JackpotGamesModel,
    )
    const jackpotBetsModel = this.databaseService.getBackendModel(BackendModelEnum.JackpotBetsModel)

    const game = await jackpotGamesModel.getJackpotGame({
      where: {
        id: data.gameId,
      },
    })
    const bets = await jackpotBetsModel.getJackpotBets({
      where: {
        gameId: data.gameId,
      },
    })

    const filteredBetsData = bets.map(item =>
      pickJackpotBetsData(item, this.gameServersConfig.gameServers.get(item.serverId).NAME),
    )
    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_HISTORY_ITEM,
      payload: {
        data: {
          game: {
            ...game,
            winnerServerName: this.gameServersConfig.gameServers
              .get(game.winnerServerId)
              .NAME.toLowerCase(),
          },
          bets: filteredBetsData,
        },
      },
    })

    return {
      game: {
        ...game,
        winnerServerName: this.gameServersConfig.gameServers
          .get(game.winnerServerId)
          .NAME.toLowerCase(),
      },
      bets: filteredBetsData,
    }
  }

  async betJackpot(user: IUser, data: Omit<MiniGamesBetJackpot.Request, 'session'>) {
    this._checkAccountValid(user.accounts, data.characterId)

    const { characterId, betAmount } = data
    const donate = user.user.donate

    this.logger.debug(
      `[betJackpot] Start jackpot bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )
    this.logger.debug(
      `[betJackpot] player donate: ${donate}. BetAmount: ${betAmount}. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    if (isServerRestarting()) {
      this.logger.debug(
        `[betJackpot] Server is restarting. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.casino.restartCantBet',
        'error',
      )

      return { ok: false }
    }

    await this.getState()

    if (!betAmount || !this.state.isActive) {
      this.logger.debug(
        `[betJackpot] No bet amount or game is not active. AccountId: ${data.characterId}`,
      )
      return { ok: false }
    }

    if (betAmount < MinigamesBetAmounts.jackpot.min) {
      this.logger.debug(
        `[betJackpot] Bet too small. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.betTooSmall',
        'error',
      )

      return { ok: false }
    }

    if (betAmount > MinigamesBetAmounts.jackpot.max) {
      this.logger.debug(
        `[betJackpot] Bet too big. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.betTooBig',
        'error',
      )

      return { ok: false }
    }

    let player = await this.playersService.updateMiniGamePlayer(
      user.user.id,
      mini_games_name.jackpot,
      {
        isBetInProgress: true,
      },
    )

    if (player && MinigamesBetAmounts.jackpot.max - (player.betAmount + betAmount) < 0) {
      this.logger.debug(
        `[betJackpot] Player bet too big. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.betTooBig',
        'error',
      )

      return { ok: false }
    }

    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.jackpot,
    )
    if (currentGamePlayers.length > MinigamesMaxPlayers.jackpot) {
      this.logger.debug(
        `[betJackpot] In current game more than ${MinigamesMaxPlayers.jackpot} players. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.gameIsFulled',
        'error',
      )

      return { ok: false }
    }

    if (this.state.isGameActive || this.stateArgs.winner) {
      this.logger.debug(
        `[betJackpot] Game not stopped. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.gameNotStoped',
        'error',
      )

      return { ok: false }
    }

    if (donate < betAmount) {
      this.logger.debug(
        `[betJackpot] Not enough money. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.notEnoughMoney',
        'error',
      )

      return { ok: false }
    }

    this.logger.debug(
      `[betJackpot] start decrease player donate. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )
    const serverResponse = await this.playersService.updatePlayerDonate({
      userId: player.userId,
      accountId: player.accountId,
      serverId: user.serverId,
      betAmount,
      type: OperationType.DECREASE,
      comment: 'logs.donate_log.minigamesF2JackpotBet',
      args: JSON.stringify({
        game: 'jackpot',
        gameId: this.state.gameId,
      }),
    })
    // const serverResponse = { data: { success: true } }
    this.logger.debug(
      `[betJackpot] end decrease player donate. Response: ${JSON.stringify(
        serverResponse,
      )}. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    if (!serverResponse || !serverResponse.data.success) {
      this.logger.debug(
        `[betJackpot] Not got server PRC response. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.serverNotResponse',
        'error',
      )
      return { ok: false }
    }

    if (this.state.isGameActive || this.stateArgs.winner) {
      this.logger.debug(
        `[betJackpot] Game not stopped. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )

      await this.playersService.refundBet(
        player.serverId,
        player.accountId,
        mini_games_name.jackpot,
        this.state.gameId,
        betAmount,
      )

      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.gameNotStoped',
        'error',
      )

      return { ok: false }
    }

    this.logger.debug(
      `[betJackpot] Creating minigamesF2 log. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    this.redisService.publish(`${user.serverId}.miniGamesTriggerSeasonPassTask`, {
      userId: player.userId,
      accountId: player.accountId,
      triggerType: 'playMinigameF2',
      triggerValue: MiniGamesNames.JACKPOT,
    })

    const jackpotBetsModel = this.databaseService.getBackendModel(BackendModelEnum.JackpotBetsModel)

    await this.getState()
    let players = await this.playersService.getMiniGamePlayers(mini_games_name.jackpot)
    player = await this.playersService.getMiniGamePlayer(mini_games_name.jackpot, characterId)

    if (player.inCurrentGame) {
      this.logger.debug(
        `[betJackpot] Player already in game. Update betAccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      const newBet = (player.betAmount += betAmount)
      this.logger.debug(
        `[betJackpot] new player bet: ${newBet}. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )

      try {
        this.logger.debug(
          `[betJackpot] Creating jackpot bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
        )
        await jackpotBetsModel.transactionUpdateJackpotBet(
          MiniGamesNames.JACKPOT,
          betAmount,
          newBet,
          player,
        )
      } catch (err) {
        this.logger.debug(
          `[betJackpot] Game not stopped, bet not updated. Refund bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
        )

        await this.playersService.refundBet(
          player.serverId,
          player.accountId,
          mini_games_name.jackpot,
          this.state.gameId,
          betAmount,
        )

        await this.utilsService.longNotifyTrans(
          data.characterId,
          user.serverId,
          'fun.minigamesF2.errors.gameNotStoped',
          'error',
        )
        return { ok: false }
      }

      player = await this.playersService.updateMiniGamePlayer(
        user.user.id,
        mini_games_name.jackpot,
        {
          betAmount: newBet,
        },
      )

      if (players.length) {
        await this.playersService.sendUpdatesToPlayers(
          mini_games_name.jackpot,
          MINI_GAMES_MESSAGES_TYPES.JACKPOT_UPDATE_BET,
          {
            login: player.login,
            gender: player.gender,
            accountId: player.accountId,
            serverId: player.serverId,
            serverName: this.gameServersConfig.gameServers.get(player.serverId).NAME.toLowerCase(),
            amount: newBet,
          },
        )
      }
    } else {
      this.logger.debug(
        `[betjackpot] Player not in game. Creating bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      const newPlayerTicket = this.generateUniqueTicket()
      this.logger.debug(
        `[betJackpot] Generated ticket: ${newPlayerTicket}. AccountId: ${player.accountId}. GameId: ${this.state.gameId}`,
      )

      try {
        await jackpotBetsModel.transactionCreateJackpotBet(
          MiniGamesNames.JACKPOT,
          betAmount,
          player,
          newPlayerTicket,
          this.state.gameId,
        )
      } catch (err) {
        this.logger.debug(
          `[betJackpot] Game not stopped, cant create bet. Refund bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
        )

        await this.playersService.refundBet(
          player.serverId,
          player.accountId,
          mini_games_name.jackpot,
          this.state.gameId,
          betAmount,
        )

        await this.utilsService.longNotifyTrans(
          data.characterId,
          user.serverId,
          'fun.minigamesF2.errors.gameNotStoped',
          'error',
        )
        return { ok: false }
      }

      player = await this.playersService.updateMiniGamePlayer(
        player.userId,
        mini_games_name.jackpot,
        {
          gameId: this.state.gameId,
          inCurrentGame: true,
          betAmount,
          args: {
            ticket: newPlayerTicket,
            dateBet: new Date(),
          },
        },
      )

      players = await this.playersService.getMiniGamePlayers(mini_games_name.jackpot)

      const playerData = pickCurrentGamePlayersData(
        player,
        this.gameServersConfig.gameServers.get(player.serverId).NAME,
      )
      if (players.length) {
        await this.playersService.sendUpdatesToPlayers(
          mini_games_name.jackpot,
          MINI_GAMES_MESSAGES_TYPES.JACKPOT_BET,
          { player: playerData },
        )
      }
    }

    this.logger.debug(
      `[betJackpot] Ending jacktop bet -  update isBetInProgress. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )
    await this.playersService.updateMiniGamePlayer(user.user.id, mini_games_name.jackpot, {
      isBetInProgress: false,
    })
    this.logger.debug(
      `[betJackpot] End jacktop bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    return 'ok'
  }
}
