import { Injectable, Logger } from '@nestjs/common'
import { AppBaseService } from '../../app/app.base.service'
import { BackendModelEnum, PrismaService } from '@majestic-backend/database'
import { RedisService } from '@majestic-backend/redis'
import {
  MINI_GAMES_MESSAGES_TYPES,
  OperationType,
  SERVER_MESSAGES_TYPES,
} from '@majestic-backend/types'
import { mini_games_name } from '@majestic-backend/prisma-client-backend'
import {
  IMiniGamePlayer,
  IMiniGamesPlayerCreatePayload,
  IMiniGamesPlayerUpdatePayload,
  IRedisPayload,
  IRefundBet,
  MinigameUpdatePlayerDonate,
} from '../types'
import { groupPlayersByServers } from '../helpers'
import { RedisListenerService } from './redis-listener.service'

@Injectable()
export class PlayersService extends AppBaseService {
  private logger = new Logger(PlayersService.name)

  constructor(
    protected readonly databaseService: PrismaService,
    private readonly redisService: RedisService,
    private readonly redisListenerService: RedisListenerService,
  ) {
    super(databaseService)
  }

  async updatePlayerDonate({
    userId,
    accountId,
    serverId,
    betAmount,
    type,
    args,
    comment,
  }: MinigameUpdatePlayerDonate) {
    this.logger.debug(
      `[updatePlayerDonate] Sending update user donate. ServerId: ${serverId}. UserId: ${userId}`,
    )

    const rpcResponse: IRedisPayload = await this.redisListenerService
      .sendRpcRequest(
        `${serverId}.${SERVER_MESSAGES_TYPES.MINI_GAMES_UPDATE_USER_DONATE}`,
        {
          userId,
          accountId,
          betAmount,
          type,
          args,
          comment,
        },
        10000,
      )
      .catch(() => null)

    return rpcResponse
  }

  async refundBets(miniGameName: mini_games_name) {
    const currentGamePlayersAll = await this.getCurrentMiniGamePlayers(miniGameName)
    const playersNoReward = currentGamePlayersAll.filter(player => !player.gotReward)

    const groupByServerId = (currentGamePlayers: IMiniGamePlayer[]) => {
      return currentGamePlayers.reduce<Record<string, IMiniGamePlayer[]>>((acc, player) => {
        if (!acc[player.serverId]) {
          acc[player.serverId] = []
        }
        acc[player.serverId].push(player)
        return acc
      }, {})
    }
    const playersByServers = groupByServerId(playersNoReward)

    for (const serverId in playersByServers) {
      try {
        this.logger.debug(`[refundBets] Start refunds to players from serverId: ${serverId}`)
        const players = playersByServers[serverId]

        for (const player of players) {
          await this.increasePlayerDonate({
            serverId: player.serverId,
            accountId: player.accountId,
            userId: player.userId,
            betAmount: player.betAmount,
            minigameName: player.minigameName,
            gameId: player.gameId,
          })
        }
      } catch (err) {
        this.logger.error(`[refundBets] Error processing server ${serverId}: ${err}`)
      }
    }

    return
  }

  async refundBet(
    serverId: string,
    accountId: number,
    minigameName: mini_games_name,
    gameId: number,
    betAmount: number,
  ) {
    const player = await this.getMiniGamePlayer(minigameName, accountId)

    if (!player) {
      this.logger.error(
        `[refundBet] Player not found. Minigame: ${minigameName}. ServerId: ${serverId}. AccountId: ${accountId}. GameId: ${gameId}`,
      )
      return
    }

    await this.increasePlayerDonate({
      serverId,
      accountId,
      userId: player.userId,
      betAmount,
      minigameName,
      gameId,
    })
  }

  async increasePlayerDonate(payload: IRefundBet) {
    const { serverId, accountId, userId, betAmount, minigameName, gameId } = payload
    try {
      this.logger.debug(
        `[refundBet] Trying to refund bet. ServerId: ${serverId}. AccountId: ${accountId}.`,
      )
      const serverResponse = await this.updatePlayerDonate({
        userId,
        accountId,
        serverId,
        betAmount,
        type: OperationType.INCREASE,
        comment: 'logs.payment_log.minigamesF2RefundBet',
        args: JSON.stringify({
          type: minigameName,
          gameId,
          betAmount,
        }),
      })
      if (!serverResponse || !serverResponse.data.success) throw new Error()

      this.logger.debug(
        `[refundBet] Refunded ${betAmount}. AccountId: ${accountId}. ServerId: ${serverId}`,
      )
    } catch (err) {
      this.logger.error(
        `[refundBet] Error updating donate. AccountId: ${accountId}. ServerId: ${serverId}`,
      )
    }
  }

  public async sendUpdatesToPlayers(
    miniGameName: mini_games_name,
    type: MINI_GAMES_MESSAGES_TYPES,
    payload: Record<string, any>,
  ) {
    const players = await this.getMiniGamePlayers(miniGameName)
    const groupedRecieversByServers = groupPlayersByServers(players)

    await Promise.all(
      Object.entries(groupedRecieversByServers).map(([serverId, receivers]) =>
        this.redisService.publishToSockets({
          receivers,
          serverId,
          type,
          payload,
        }),
      ),
    )
  }

  async getMiniGamePlayers(minigameName: mini_games_name) {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    return miniGamesPlayersModel.getMiniGamesPlayers({
      where: {
        minigameName,
      },
    })
  }

  async getMiniGamePlayer(minigameName: mini_games_name, accountId: number) {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    return miniGamesPlayersModel.getMiniGamesPlayer({
      where: {
        minigameName,
        accountId,
      },
    })
  }

  async getCurrentMiniGamePlayers(minigameName: mini_games_name) {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    return miniGamesPlayersModel.getMiniGamesPlayers({
      where: {
        minigameName,
        inCurrentGame: true,
      },
    })
  }

  async updateMiniGamePlayer(
    userId: number,
    minigameName: mini_games_name,
    data: IMiniGamesPlayerUpdatePayload,
  ) {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    return miniGamesPlayersModel.updateMiniGamesPlayer({
      where: {
        minigameName_userId: {
          minigameName,
          userId,
        },
      },
      data,
    })
  }

  async removePlayerFromSpectators(serverId: string, accountId: number) {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    return miniGamesPlayersModel.updateManyMiniGamesPlayers({
      where: {
        accountId,
        isActive: true,
        serverId,
      },
      data: {
        isActive: false,
      },
    })
  }

  async updateMiniGamePlayerByArgs(
    userId: number,
    minigameName: mini_games_name,
    data: IMiniGamesPlayerUpdatePayload,
  ) {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    try {
      const player = await miniGamesPlayersModel.updateManyMiniGamesPlayers({
        where: {
          minigameName,
          userId,
          args: {
            path: '$.exit',
            equals: null,
          },
        },
        data,
      })
      return player
    } catch (err) {
      this.logger.debug(`[updateMiniGamePlayerByArgs] Player not found. UserId: ${userId}`)
      return null
    }
  }

  async getMiniGamePlayerByArgs(userId: number, minigameName: mini_games_name) {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    return miniGamesPlayersModel.getMiniGamesPlayer({
      where: {
        minigameName,
        userId,
        args: {
          path: '$.exit',
          equals: null,
        },
      },
    })
  }

  async clearCurrentGamePlayers(minigameName: mini_games_name) {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    const currentPlayers = await miniGamesPlayersModel.getMiniGamesPlayers({
      where: {
        minigameName,
        inCurrentGame: true,
      },
    })

    const clearPlayersInGamePromises = currentPlayers.map(player =>
      miniGamesPlayersModel.updateMiniGamesPlayer({
        where: {
          minigameName_userId: {
            minigameName,
            userId: player.userId,
          },
        },
        data: {
          gameId: null,
          inCurrentGame: false,
          betAmount: 0,
          args: {},
          gotReward: false,
        },
      }),
    )

    await Promise.all(clearPlayersInGamePromises)
  }

  async createMiniGamePlayer(data: IMiniGamesPlayerCreatePayload) {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    return miniGamesPlayersModel.createMiniGamesPlayer(data)
  }

  async removeMiniGamePlayer(userId: number, accountId: number, minigameName: mini_games_name) {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    return miniGamesPlayersModel.removeMiniGamesPlayer(userId, minigameName)
  }

  async removeNonActivePlayers(minigameName: mini_games_name) {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    const nonActivePlayers = await miniGamesPlayersModel.getMiniGamesPlayers({
      where: {
        minigameName,
        isActive: false,
      },
    })

    await Promise.allSettled(
      nonActivePlayers.map(player =>
        this.removeMiniGamePlayer(player.userId, player.accountId, minigameName),
      ),
    )
  }

  async removeMinigamesAllPlayers() {
    const miniGamesPlayersModel = this.databaseService.getBackendModel(
      BackendModelEnum.MiniGamesPlayersModel,
    )

    const players = await miniGamesPlayersModel.getMiniGamesPlayers({
      where: {},
    })

    await Promise.allSettled(
      players.map(player =>
        miniGamesPlayersModel.removeNotInGamePlayer(player.userId, player.minigameName),
      ),
    )
  }
}
