import { Injectable, Logger } from '@nestjs/common'
import { <PERSON>ron } from '@nestjs/schedule'
import { GameService } from './game.service'
import { mini_games_name } from '@majestic-backend/prisma-client-backend'
import { RedisLockService } from '@majestic-backend/redis'
import { PlayersService } from './players.service'

@Injectable()
export class MinigamesSchedule {
  private readonly logger = new Logger(MinigamesSchedule.name)

  constructor(
    private readonly redisLockService: RedisLockService,
    private readonly gameService: GameService,
    private readonly playersService: PlayersService,
  ) {}

  @Cron('10 6 3 * * *')
  async setMinigamesActiveAfterServerRestarted() {
    const initLockKey = 'minigamesCron:setActive:lock'

    const initLockAcquired = await this.redisLockService.acquireLock(initLockKey)

    if (initLockAcquired) {
      this.logger.debug(`Start minigames set isActive after server restarted`)

      await Promise.allSettled([
        this.gameService.setGameIsActive(1, mini_games_name.crash, {
          isActive: true,
        }),
        this.gameService.setGameIsActive(2, mini_games_name.jackpot, {
          isActive: true,
        }),
        this.gameService.setGameIsActive(3, mini_games_name.wheel, {
          isActive: true,
        }),
      ])

      this.logger.debug(`[startGame] Server restarted`)

      await this.redisLockService.releaseLock(initLockKey)
    }
  }

  @Cron('0 0 3 * * *')
  async clearGamesAllPlayers() {
    const initLockKey = 'minigamesCron:clearAllPlayers:lock'

    const initLockAcquired = await this.redisLockService.acquireLock(initLockKey)

    if (initLockAcquired) {
      this.logger.debug(`Start removing all minigames players`)

      await this.playersService.removeMinigamesAllPlayers()

      this.logger.debug(`All players removed`)

      await this.redisLockService.releaseLock(initLockKey)
    }
  }
}
