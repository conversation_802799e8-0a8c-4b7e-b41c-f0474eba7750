import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { AppBaseService } from '../../app/app.base.service'
import { BackendModelEnum, PrismaService } from '@majestic-backend/database'
import { RedisLockService, RedisService } from '@majestic-backend/redis'
import { IUser, MINI_GAMES_MESSAGES_TYPES, OperationType } from '@majestic-backend/types'
import {
  MiniGamesAddPlayerWheel,
  MiniGamesBetWheel,
  MiniGamesGetDataWheel,
  MiniGamesGetHistoryItemWheel,
  MiniGamesGetHistoryListWheel,
  MiniGamesRemovePlayerWheel,
} from '@majestic-backend/contracts'
import { UtilsService } from './utils.service'
import {
  getRandomNumber,
  isServerRestarting,
  pickCurrentGamePlayersData,
  pickWheelBetsData,
  sleep,
} from '../helpers'
import crypto from 'crypto'
import {
  IMiniGamePlayer,
  IMiniGamesGamesUpdatePayload,
  IMiniGameState,
  IWheelStateArgs,
} from '../types'
import { MiniGamesNames, MiniGamesDonateType, MiniGamesGameId } from '../enum'
import { GameService } from './game.service'
import { PlayersService } from './players.service'
import { mini_games_name } from '@majestic-backend/prisma-client-backend'
import {
  MinigamesBetAmounts,
  WheelColorsWithCoef,
  WheelStateData,
} from '@shared_config/minigames/state'
import { GameServersConfig } from '@majestic-backend/config'

@Injectable()
export class WheelService extends AppBaseService implements OnModuleInit {
  private logger = new Logger(WheelService.name)

  private historyLimit = 40
  private validColors = Object.keys(WheelColorsWithCoef)
  private salt = '0x0d1e66e5632279e6234e6234e22334535e56e5e9897q9'

  private wheel = {
    rotateStep: 18,
    wheelColors: [
      WheelColorsWithCoef.red.name,
      WheelColorsWithCoef.white.name,
      WheelColorsWithCoef.green.name,
      WheelColorsWithCoef.white.name,
      WheelColorsWithCoef.red.name,
      WheelColorsWithCoef.white.name,
      WheelColorsWithCoef.red.name,
      WheelColorsWithCoef.white.name,
      WheelColorsWithCoef.green.name,
      WheelColorsWithCoef.white.name,
      WheelColorsWithCoef.yellow.name,
      WheelColorsWithCoef.white.name,
      WheelColorsWithCoef.red.name,
      WheelColorsWithCoef.green.name,
      WheelColorsWithCoef.white.name,
      WheelColorsWithCoef.red.name,
      WheelColorsWithCoef.white.name,
      WheelColorsWithCoef.red.name,
      WheelColorsWithCoef.white.name,
      WheelColorsWithCoef.green.name,
    ],
  }

  private state: IMiniGameState
  private stateArgs: IWheelStateArgs

  private initLockKey = 'wheel:init:lock'
  private initLockTTL = 5
  private startGameLockKey = 'wheel:startGame:lock'
  private startGameLockTTL = 10
  private lastUpdateKey = 'minigame:wheel:lastUpdate'

  constructor(
    protected readonly databaseService: PrismaService,
    private readonly redisService: RedisService,
    private readonly utilsService: UtilsService,
    private readonly playersService: PlayersService,
    private readonly gameService: GameService,
    private readonly redisLockService: RedisLockService,
    private readonly gameServersConfig: GameServersConfig,
  ) {
    super(databaseService)
  }

  async onModuleInit() {
    const initLockAcquired = await this.redisLockService.acquireLock(this.initLockKey)

    if (initLockAcquired) {
      this.logger.debug('Wheel init lock acquired. Start init()')

      await this.init()
      await this.redisLockService.releaseLock(this.initLockKey)

      this.logger.debug('Wheel data initialized')
    } else {
      this.logger.debug('Wheel init lock not acquired. Waiting for initialization to complete')
      await this.waitForCompleteInit(this.initLockKey)
    }

    this.startGameLoop()
  }

  async init() {
    const wheelGamesModel = this.databaseService.getBackendModel(BackendModelEnum.WheelGamesModel)
    const wheelLastGame = await wheelGamesModel.getWheelGame({
      orderBy: {
        id: 'desc',
      },
    })

    await this.getState()
    await this.updateState({
      gameId: (wheelLastGame && wheelLastGame.id) || 0,
      isActive: !this.state.isActive ? false : true,
      isTimerActive: false,
      timerTimeStart: null,
      isGameActive: false,
      gameTimeStart: null,
      args: JSON.stringify({
        ...this.stateArgs,
        winner: null,
        winnerRotation: this.stateArgs.winnerRotation ? this.stateArgs.winnerRotation : 0,
      }),
    })

    this.logger.debug(`init gameId: ${this.state.gameId}`)
  }

  async waitForCompleteInit(initLockKey: string) {
    while (await this.redisService.exists(initLockKey)) {
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    this.logger.debug('Wheel init completed by another replica')
  }

  async startGameLoop() {
    const tryStartGame = async () => {
      if (await this.utilsService.isGameHandledElsewhere(MiniGamesNames.WHEEL)) {
        this.logger.debug(`[startGameLoop] Game is handled by other replica. Try start game later`)
        setTimeout(() => {
          tryStartGame()
        }, 5000)
        return
      }

      this.logger.debug(`[startGameLoop] Game is not handled by anyone. Try lock`)
      const lockAcquired = await this.redisLockService.acquireLock(this.startGameLockKey)

      if (!lockAcquired) {
        this.logger.debug(`[startGameLoop] Other replica acquired lock. Try start game later`)
        setTimeout(() => {
          tryStartGame()
        }, 5000)
        return
      }

      await this.redisService.set(this.lastUpdateKey, new Date().getTime())
      const updateActiveWheel = setInterval(async () => {
        await this.redisService.set(this.lastUpdateKey, new Date().getTime())
      }, 2000)

      try {
        this.logger.debug(`[startGameLoop] Lock acquired`)
        await this.getState()
        if (this.state.isGameInProgress) {
          this.logger.debug(`[startGameLoop] Last game not ended. Refund bets`)
          await this.playersService.refundBets(mini_games_name.wheel)
          this.logger.debug(`[startGameLoop] Clear current game players`)
          await this.playersService.clearCurrentGamePlayers(mini_games_name.wheel)
          this.logger.debug(`[startGameLoop] Remove restarted game bets`)
          await this.utilsService.removeRestartedMinigameBets(
            mini_games_name.wheel,
            this.state.gameId + 1,
          )

          await this.updateState({
            isGameInProgress: false,
          })

          const players = await this.playersService.getMiniGamePlayers(mini_games_name.wheel)
          for (const player of players) {
            await this.utilsService.longNotifyTrans(
              player.accountId,
              player.serverId,
              'fun.minigamesF2.errors.gameRestarted',
              'error',
            )
          }
        }

        this.logger.debug(`[startGameLoop] Release lock and start game`)
        await this.redisLockService.releaseLock(this.startGameLockKey)
        await this.startGame()
      } finally {
        this.logger.debug(`[startGameLoop] Clear interval and delete lastUpdate`)
        clearInterval(updateActiveWheel)
        await this.redisService.del(this.lastUpdateKey)
      }

      tryStartGame()
    }

    tryStartGame()
  }

  private async getState() {
    const { state, stateArgs } = await this.gameService.getMiniGameStateWithArgs(
      mini_games_name.wheel,
    )

    this.state = state
    this.stateArgs = stateArgs
  }

  private async updateState(data: IMiniGamesGamesUpdatePayload) {
    await this.getState()
    const { state, stateArgs } = await this.gameService.updateMiniGameState(
      MiniGamesGameId.WHEEL,
      mini_games_name.wheel,
      data,
    )

    this.state = state
    this.stateArgs = stateArgs
  }

  sortCurrentPlayersByColors(currentGamePlayers: IMiniGamePlayer[]) {
    const sortedPlayers = currentGamePlayers.reduce(
      (acc, player) => {
        const bets = player.args.wheelBets

        bets.forEach(({ color }) => {
          if (acc[color]) {
            acc[color].push(player)
          } else {
            acc[color] = [player]
          }
        })

        return acc
      },
      {
        white: [],
        red: [],
        green: [],
        yellow: [],
      } as Record<string, IMiniGamePlayer[]>,
    )

    return sortedPlayers
  }

  async getSumBetsColor(color: string) {
    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.wheel,
    )
    const sortedPlayers = this.sortCurrentPlayersByColors(currentGamePlayers)

    const sumbets = sortedPlayers[color].reduce((total, player) => {
      const winnerArgs = player.args.wheelBets
      return total + winnerArgs.find(bet => bet.color === color).betAmount
    }, 0)

    return sumbets
  }

  generateHash(seed: string) {
    return crypto.createHash('sha256').update(seed).digest('hex')
  }

  generateRandomColorWin(hash: string) {
    const result = parseInt(hash.slice(0, 52 / 4), 16) % 25
    if (result === 0) {
      return WheelColorsWithCoef.yellow.name
    }

    if (result <= 5) {
      return WheelColorsWithCoef.green.name
    }

    if (result <= 12) {
      return WheelColorsWithCoef.red.name
    }

    return WheelColorsWithCoef.white.name
  }

  async generateRotate() {
    const hash = this.generateHash(crypto.randomBytes(16).toString('hex'))
    const colorWin = this.generateRandomColorWin(hash)

    const indexs = this.wheel.wheelColors
      .map((color, index) => (color === colorWin ? index : null))
      .filter(index => index !== null)
    const winnerRotation =
      indexs[getRandomNumber(0, indexs.length - 1)] * this.wheel.rotateStep +
      getRandomNumber(3, this.wheel.rotateStep - 3)

    await this.getState()
    await this.updateState({
      args: JSON.stringify({
        ...this.stateArgs,
        winnerRotation,
      }),
    })

    return colorWin
  }

  async getPlayersCount() {
    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.wheel,
    )

    return currentGamePlayers.length
  }

  async getTotalBetsSum() {
    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.wheel,
    )

    return currentGamePlayers.reduce((total, player) => total + player.betAmount, 0)
  }

  async createGameResult(colorWin: string, totalGiveCoins: number) {
    const wheelGamesModel = this.databaseService.getBackendModel(BackendModelEnum.WheelGamesModel)

    await wheelGamesModel.createWheelGame({
      playersCount: await this.getPlayersCount(),
      white: await this.getSumBetsColor(WheelColorsWithCoef.white.name),
      red: await this.getSumBetsColor(WheelColorsWithCoef.red.name),
      green: await this.getSumBetsColor(WheelColorsWithCoef.green.name),
      yellow: await this.getSumBetsColor(WheelColorsWithCoef.yellow.name),
      bank: await this.getTotalBetsSum(),
      colorWin: colorWin,
      won: totalGiveCoins,
      lost: (await this.getTotalBetsSum()) - totalGiveCoins,
    })
  }

  async startGame() {
    await this.getState()

    if (this.state.isActive && isServerRestarting()) {
      this.logger.debug(`[startGame] Server is restarting. GameId: ${this.state.gameId}`)
      await this.updateState({
        isActive: false,
        args: JSON.stringify({
          ...this.stateArgs,
          isPausedDueToRestart: true,
        }),
      })

      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.wheel,
        MINI_GAMES_MESSAGES_TYPES.WHEEL_START_TIMER,
        {
          state: { isActive: false },
        },
      )
    }

    if (!this.state.isActive) {
      this.logger.debug(`[startGame] Current game is not active. GameId: ${this.state.gameId}`)
      await sleep(5000)

      return
    }

    let players = await this.playersService.getMiniGamePlayers(mini_games_name.wheel)
    if (!players.length) {
      await sleep(1000)

      return
    }

    this.logger.debug(`************* wheel start game *************`)
    await this.updateState({
      gameId: {
        increment: 1,
      },
      isTimerActive: true,
      timerTimeStart: new Date(),
      isGameInProgress: true,
      isGameActive: false,
      gameTimeStart: null,
      args: JSON.stringify({
        winner: null,
        winnerRotation: this.stateArgs.winnerRotation ? this.stateArgs.winnerRotation : 0,
      }),
    })

    players = await this.playersService.getMiniGamePlayers(mini_games_name.wheel)
    let currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.wheel,
    )

    if (players.length) {
      const filteredCurrentGamePlayersData = currentGamePlayers.map(item =>
        pickCurrentGamePlayersData(
          item,
          this.gameServersConfig.gameServers.get(item.serverId).NAME,
        ),
      )
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.wheel,
        MINI_GAMES_MESSAGES_TYPES.WHEEL_START_TIMER,
        {
          state: this.state,
          currentGame: {
            id: this.state.gameId,
            currentGamePlayers: filteredCurrentGamePlayersData,
          },
        },
      )
    }

    this.logger.debug(`[startGame] Start timer init time. GameId: ${this.state.gameId}`)
    await sleep(WheelStateData.timerInitTime)

    await this.updateState({
      isTimerActive: false,
      isGameActive: true,
      gameTimeStart: new Date(),
    })

    const colorWin = await this.generateRotate()
    this.logger.debug(`[startGame] Generated color win: ${colorWin}. GameId: ${this.state.gameId}`)

    players = await this.playersService.getMiniGamePlayers(mini_games_name.wheel)
    currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(mini_games_name.wheel)
    if (players.length) {
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.wheel,
        MINI_GAMES_MESSAGES_TYPES.WHEEL_START_WHEEL,
        {
          state: this.state,
        },
      )
    }

    this.logger.debug(`[startGame] Start game spin time. GameId: ${this.state.gameId}`)
    await sleep(WheelStateData.gameSpinTime)

    await this.updateState({
      isGameActive: false,
      args: JSON.stringify({
        ...this.stateArgs,
        winner: colorWin,
      }),
    })
    const winColorCoefficient = WheelColorsWithCoef[colorWin].coefficient
    this.logger.debug(
      `[startGame] get winColorCoefficient: ${winColorCoefficient}. GameId: ${this.state.gameId}`,
    )

    this.logger.debug(`[startGame] Creating game result. GameId: ${this.state.gameId}`)
    await this.createGameResult(
      colorWin,
      (await this.getSumBetsColor(colorWin)) * winColorCoefficient,
    )

    players = await this.playersService.getMiniGamePlayers(mini_games_name.wheel)
    if (players.length) {
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.wheel,
        MINI_GAMES_MESSAGES_TYPES.WHEEL_SET_WINNER,
        { totalRotate: this.stateArgs.winnerRotation, winner: colorWin },
      )
    }

    // currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(mini_games_name.wheel)
    // const winPlayers = this.sortCurrentPlayersByColors(currentGamePlayers)[colorWin]
    const wheelBetsModel = this.databaseService.getBackendModel(BackendModelEnum.WheelBetsModel)
    const winBets = await wheelBetsModel.getWheelBets({
      where: {
        gameId: this.state.gameId,
        color: colorWin,
      },
    })

    const updateResults = await Promise.allSettled(
      winBets.map(async bet => {
        try {
          const betAmount = bet.amount
          const winCoins = betAmount * winColorCoefficient

          this.logger.debug(
            `[takeWheel] start increase player donate. AccountId: ${bet.accountId}. GameId: ${this.state.gameId}`,
          )

          const serverResponse = await this.playersService.updatePlayerDonate({
            userId: bet.userId,
            accountId: bet.accountId,
            serverId: bet.serverId,
            betAmount: winCoins,
            type: OperationType.INCREASE,
            comment: 'logs.payment_log.minigamesF2',
            args: JSON.stringify({
              type: 'wheel',
              sessionId: this.state.gameId,
              coefficient: winColorCoefficient,
              serverId: bet.serverId,
            }),
          })
          // const serverResponse = { data: { success: true } }
          this.logger.debug(
            `[takeWheel] end increase player donate. Response: ${JSON.stringify(
              serverResponse,
            )}. AccountId: ${bet.accountId}. GameId: ${this.state.gameId}`,
          )

          if (!serverResponse || !serverResponse.data.success) {
            this.logger.debug(
              `[takeWheel] Not got server PRC response. AccountId: ${bet.accountId}. GameId: ${this.state.gameId}`,
            )
            await this.utilsService.longNotifyTrans(
              bet.accountId,
              bet.serverId,
              'fun.minigamesF2.errors.serverNotResponse',
              'error',
            )
            throw new Error(
              `[takeWheel] Failed to update player donate. AccountId: ${bet.accountId}`,
            )
          }

          this.logger.debug(
            `[startGame] Set player got reward. WheelGameId: ${this.state.gameId}. AccountId: ${bet.accountId}`,
          )
          await this.playersService.updateMiniGamePlayer(bet.userId, mini_games_name.wheel, {
            gotReward: true,
          })

          this.logger.debug(`[startGame] Creating minigamesF2 log. GameId: ${this.state.gameId}`)
          await this.utilsService.createMinigamesF2Log({
            userId: bet.userId,
            accountId: bet.accountId,
            game: MiniGamesNames.WHEEL,
            type: MiniGamesDonateType.WIN,
            amount: winCoins,
            gameId: this.state.gameId,
            args: {
              colorWin,
              winCoins,
              coefficient: winColorCoefficient,
            },
            serverId: bet.serverId,
          })
        } catch (error) {
          this.logger.error(
            `[takeWheel] Error processing player: ${error.message}. AccountId: ${bet.accountId}. ServerId: ${bet.serverId}`,
          )
          throw error
        }
      }),
    )

    updateResults.forEach((result, index) => {
      if (result.status === 'rejected') {
        const player = winBets[index]
        this.logger.error(
          `[takeWheel] Failed to process player: ${result.reason}. AccountId: ${player.accountId}. ServerId: ${player.serverId}`,
        )
      }
    })

    this.logger.debug(`[startGame] Start time end timer. GameId: ${this.state.gameId}`)
    await sleep(WheelStateData.timerTimeEnd)

    await this.getState()

    this.logger.debug(`[startGame] Set isGameInProgress to false. GameId: ${this.state.gameId}`)
    await this.updateState({
      isGameInProgress: false,
    })
    this.logger.debug(`[startGame] Clearing current game players. GameId: ${this.state.gameId}`)
    await this.playersService.clearCurrentGamePlayers(mini_games_name.wheel)
    this.logger.debug(`[startGame] Removing non active players. GameId: ${this.state.gameId}`)
    await this.playersService.removeNonActivePlayers(mini_games_name.wheel)

    await sleep(100)
    this.logger.debug(`************* wheel end game *************`)

    return
  }

  async addPlayer(player: IUser, data: Omit<MiniGamesAddPlayerWheel.Request, 'session'>) {
    const { state } = await this.gameService.getMiniGameStateWithArgs(mini_games_name.wheel)
    if (!state.isActive) {
      this.logger.debug(
        `[addPlayer] Trying add player to wheel when state is not active. AccountId: ${data.characterId}. GameId: ${state.gameId}`,
      )
      await this.redisService.publishToSockets({
        receivers: data.characterId,
        serverId: player.serverId,
        type: MINI_GAMES_MESSAGES_TYPES.WHEEL_START_TIMER,
        payload: {
          state: { isActive: false },
        },
      })

      return 'gameNotActive'
    }

    this.logger.debug(
      `Start ADD player to all wheel player list. AccountId: ${data.characterId}. GameId: ${state.gameId}`,
    )

    await this.getState()
    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.wheel,
    )
    const wheelGamesModel = this.databaseService.getBackendModel(BackendModelEnum.WheelGamesModel)
    const wheelLastGames = await wheelGamesModel.getWheelGames({
      orderBy: {
        id: 'desc',
      },
      take: this.historyLimit,
    })
    const filteredCurrentGamePlayersData = currentGamePlayers.map(item =>
      pickCurrentGamePlayersData(item, this.gameServersConfig.gameServers.get(item.serverId).NAME),
    )
    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.WHEEL_UPDATE_GAME,
      payload: {
        state: state,
        currentGame: { id: state.gameId, currentGamePlayers: filteredCurrentGamePlayersData },
        history: wheelLastGames,
      },
    })

    const players = await this.playersService.getMiniGamePlayers(mini_games_name.wheel)

    if (
      players.some(
        player =>
          player.accountId === data.characterId && player.minigameName === mini_games_name.wheel,
      )
    ) {
      await this.playersService.updateMiniGamePlayer(player.user.id, mini_games_name.wheel, {
        isActive: true,
      })
      this.logger.debug(
        `Player return to game. Set player is active. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      return 'ok'
    }

    const currentAccount = player.accounts.find(account => account.id === data.characterId)
    await this.playersService.createMiniGamePlayer({
      minigameName: mini_games_name.wheel,
      userId: player.user.id,
      accountId: data.characterId,
      serverId: player.serverId,
      login: currentAccount.login,
      socialClub: player.user.lastSocialClub,
      args: { wheelBets: [] },
      isActive: true,
      gender: currentAccount.gender,
    })

    this.logger.debug(
      `Added player to all wheel player list. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    return 'ok'
  }

  async removePlayer(player: IUser, data: Omit<MiniGamesRemovePlayerWheel.Request, 'session'>) {
    await this.getState()
    const playerInGame = await this.playersService.getMiniGamePlayer(
      mini_games_name.wheel,
      data.characterId,
    )

    this.logger.debug(
      `Start REMOVE player from all wheel player list. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    if (!playerInGame) {
      this.logger.debug(
        `Player not found in wheel player list. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      return 'notFound'
    }

    if (!playerInGame.inCurrentGame && !playerInGame.isBetInProgress) {
      await this.playersService.removeMiniGamePlayer(
        player.user.id,
        data.characterId,
        mini_games_name.wheel,
      )
      this.logger.debug(
        `Removed player from all wheel player list. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
    } else {
      await this.playersService.updateMiniGamePlayer(player.user.id, mini_games_name.wheel, {
        isActive: false,
      })
      this.logger.debug(
        `Set player is non active. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
    }

    return 'ok'
  }

  async getDataWheel(player: IUser, data: Omit<MiniGamesGetDataWheel.Request, 'session'>) {
    this._checkAccountValid(player.accounts, data.characterId)

    const wheelGamesModel = this.databaseService.getBackendModel(BackendModelEnum.WheelGamesModel)
    const wheelLastGames = await wheelGamesModel.getWheelGames({
      orderBy: {
        id: 'desc',
      },
      take: this.historyLimit,
    })
    const { state } = await this.gameService.getMiniGameStateWithArgs(mini_games_name.wheel)
    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.wheel,
    )

    const filteredCurrentGamePlayersData = currentGamePlayers.map(item =>
      pickCurrentGamePlayersData(item, this.gameServersConfig.gameServers.get(item.serverId).NAME),
    )
    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.WHEEL_UPDATE_GAME,
      payload: {
        state: state,
        currentGame: { id: state.gameId, currentGamePlayers: filteredCurrentGamePlayersData },
        history: wheelLastGames,
      },
    })

    return {
      state,
      currentGame: { id: state.gameId, currentGamePlayers },
      history: wheelLastGames,
    }
  }

  async getWheelHistoryList(
    player: IUser,
    data: Omit<MiniGamesGetHistoryListWheel.Request, 'session'>,
  ) {
    this._checkAccountValid(player.accounts, data.characterId)

    const wheelGamesModel = this.databaseService.getBackendModel(BackendModelEnum.WheelGamesModel)
    const wheelLastGames = await wheelGamesModel.getWheelGames({
      orderBy: {
        id: 'desc',
      },
      take: this.historyLimit,
    })

    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.WHEEL_UPDATE_HISTORY_LIST,
      payload: { data: wheelLastGames },
    })

    return wheelLastGames
  }

  async getWheelHistoryItem(
    player: IUser,
    data: Omit<MiniGamesGetHistoryItemWheel.Request, 'session'>,
  ) {
    this._checkAccountValid(player.accounts, data.characterId)

    const wheelGamesModel = this.databaseService.getBackendModel(BackendModelEnum.WheelGamesModel)
    const wheelBetsModel = this.databaseService.getBackendModel(BackendModelEnum.WheelBetsModel)

    const historyGame = await wheelGamesModel.getWheelGame({
      where: {
        id: data.gameId,
      },
    })
    const historyGameBets = await wheelBetsModel.getWheelBets({
      where: {
        gameId: data.gameId,
      },
    })

    const bets = {
      white: [],
      red: [],
      green: [],
      yellow: [],
    }

    const filteredBetsData = historyGameBets.map(item =>
      pickWheelBetsData(item, this.gameServersConfig.gameServers.get(item.serverId).NAME),
    )

    filteredBetsData.forEach(row => {
      bets[row.color].push(row)
    })

    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.WHEEL_UPDATE_HISTORY_ITEM,
      payload: {
        data: {
          game: historyGame,
          bets: bets,
        },
      },
    })

    return { game: historyGame, bets: bets }
  }

  async betWheel(user: IUser, data: Omit<MiniGamesBetWheel.Request, 'session'>) {
    this._checkAccountValid(user.accounts, data.characterId)

    const { characterId, betAmount, color } = data
    const donate = user.user.donate

    this.logger.debug(
      `[betWheel] Start wheel bet. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
    )
    this.logger.debug(
      `[betWheel] player donate: ${donate}. Color: ${color}. BetAmount: ${betAmount}. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
    )

    await this.getState()
    if (!betAmount || !this.state.isActive) {
      this.logger.debug(
        `[betWheel] No bet amount or game is not active. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
      )
      return { ok: false }
    }

    if (betAmount < MinigamesBetAmounts.wheel.min) {
      this.logger.debug(
        `[betWheel] Bet too small. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        characterId,
        user.serverId,
        'fun.minigamesF2.errors.betTooSmall',
        'error',
      )

      return { ok: false }
    }

    if (betAmount > MinigamesBetAmounts.wheel.max) {
      this.logger.debug(
        `[betWheel] Bet too big. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        characterId,
        user.serverId,
        'fun.minigamesF2.errors.betTooBig',
        'error',
      )

      return { ok: false }
    }

    if (!this.validColors.includes(color)) {
      this.logger.debug(
        `[betWheel] Color is not valid. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        characterId,
        user.serverId,
        'fun.minigamesF2.errors.noColor',
        'error',
      )

      return { ok: false }
    }

    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.wheel,
    )

    const sortedPlayers = this.sortCurrentPlayersByColors(currentGamePlayers)
    const playerInGame = sortedPlayers[color].find(player => player.userId === user.user.id)

    if (playerInGame && MinigamesBetAmounts.wheel.max - (playerInGame.betAmount + betAmount) < 0) {
      this.logger.debug(
        `[betWheel] Update bet too big. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        characterId,
        user.serverId,
        'fun.minigamesF2.errors.betTooBig',
        'error',
      )

      return { ok: false }
    }

    await this.getState()
    if (this.state.isGameActive || !this.state.isTimerActive) {
      this.logger.debug(
        `[betWheel] Game not stopped. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        characterId,
        user.serverId,
        'fun.minigamesF2.errors.gameNotStoped',
        'error',
      )

      return { ok: false }
    }

    if (donate < betAmount) {
      this.logger.debug(
        `[betWheel] Not enough money. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        characterId,
        user.serverId,
        'fun.minigamesF2.errors.notEnoughMoney',
        'error',
      )

      return { ok: false }
    }

    let player = await this.playersService.updateMiniGamePlayer(
      user.user.id,
      mini_games_name.wheel,
      {
        isBetInProgress: true,
      },
    )

    this.logger.debug(
      `[betWheel] start decrease player donate. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
    )
    const serverResponse = await this.playersService.updatePlayerDonate({
      userId: player.userId,
      accountId: player.accountId,
      serverId: user.serverId,
      betAmount,
      type: OperationType.DECREASE,
      comment: 'logs.donate_log.minigamesF2WheelBet',
      args: JSON.stringify({
        game: 'wheel',
        gameId: this.state.gameId,
      }),
    })
    // const serverResponse = { data: { success: true } }
    this.logger.debug(
      `[betWheel] end decrease player donate. Response: ${JSON.stringify(
        serverResponse,
      )}. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
    )

    if (!serverResponse || !serverResponse.data.success) {
      this.logger.debug(
        `[betWheel] Not got server PRC response. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        characterId,
        user.serverId,
        'fun.minigamesF2.errors.serverNotResponse',
        'error',
      )
      return { ok: false }
    }

    if (this.state.isGameActive || !this.state.isTimerActive) {
      this.logger.debug(
        `[betWheel] Game not stopped. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
      )

      await this.playersService.refundBet(
        player.serverId,
        player.accountId,
        mini_games_name.wheel,
        this.state.gameId,
        betAmount,
      )

      await this.utilsService.longNotifyTrans(
        characterId,
        user.serverId,
        'fun.minigamesF2.errors.gameNotStoped',
        'error',
      )

      return { ok: false }
    }

    this.logger.debug(
      `[betWheel] Creating minigamesF2 log. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
    )

    this.redisService.publish(`${user.serverId}.miniGamesTriggerSeasonPassTask`, {
      userId: player.userId,
      accountId: player.accountId,
      triggerType: 'playMinigameF2',
      triggerValue: MiniGamesNames.WHEEL,
    })

    const wheelBetsModel = this.databaseService.getBackendModel(BackendModelEnum.WheelBetsModel)

    const players = await this.playersService.getMiniGamePlayers(mini_games_name.wheel)
    if (playerInGame) {
      this.logger.debug(
        `[betWheel] Player already in game. Update bet. AccountId: ${player.accountId}. GameId: ${this.state.gameId}`,
      )
      const newBet = (playerInGame.betAmount += betAmount)
      this.logger.debug(`[betWheel] new player bet: ${newBet}. AccountId: ${player.accountId}`)

      const betsArgs = player.args.wheelBets ? player.args.wheelBets : []
      const currentBet = betsArgs.find(bet => bet.color === color)
      currentBet.betAmount += betAmount
      this.logger.debug(
        `[betWheel] new player current color bet: ${currentBet.betAmount}. AccountId: ${player.accountId}. GameId: ${this.state.gameId}`,
      )

      try {
        await wheelBetsModel.transactionUpdateWheelBet(
          MiniGamesNames.WHEEL,
          betAmount,
          currentBet.betAmount,
          player,
          this.state.gameId,
          color,
        )
      } catch (err) {
        this.logger.debug(
          `[betWheel] Game not stopped, bet not updated. Refund bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
        )
        currentBet.betAmount -= betAmount
        this.logger.debug(
          `[betWheel] new player current color bet: ${currentBet.betAmount}. AccountId: ${player.accountId}. GameId: ${this.state.gameId}`,
        )

        await this.playersService.refundBet(
          player.serverId,
          player.accountId,
          mini_games_name.wheel,
          this.state.gameId,
          betAmount,
        )

        await this.utilsService.longNotifyTrans(
          data.characterId,
          user.serverId,
          'fun.minigamesF2.errors.gameNotStoped',
          'error',
        )

        return { ok: false }
      }

      player = await this.playersService.updateMiniGamePlayer(
        playerInGame.userId,
        mini_games_name.wheel,
        {
          betAmount: newBet,
          args: { wheelBets: betsArgs },
        },
      )

      if (players.length) {
        await this.playersService.sendUpdatesToPlayers(
          mini_games_name.wheel,
          MINI_GAMES_MESSAGES_TYPES.WHEEL_UPDATE_BET,
          {
            accountId: player.accountId,
            color,
            amount: currentBet.betAmount,
            serverId: player.serverId,
            serverName: this.gameServersConfig.gameServers.get(player.serverId).NAME.toLowerCase(),
          },
        )
      }
    } else {
      this.logger.debug(
        `[betWheel] Player not in game. Creating. AccountId: ${player.accountId}. GameId: ${this.state.gameId}`,
      )

      try {
        this.logger.debug(
          `[betWheel] Creating wheel bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
        )
        await wheelBetsModel.transactionCreateWheelBet(
          MiniGamesNames.WHEEL,
          betAmount,
          player,
          color,
          this.state.gameId,
        )
      } catch (err) {
        this.logger.debug(
          `[betWheel] Game not stopped, cant create bet. Refund bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
        )

        await this.playersService.refundBet(
          player.serverId,
          player.accountId,
          mini_games_name.wheel,
          this.state.gameId,
          betAmount,
        )

        await this.utilsService.longNotifyTrans(
          data.characterId,
          user.serverId,
          'fun.minigamesF2.errors.gameNotStoped',
          'error',
        )

        return { ok: false }
      }

      const betsArgs = player.args.wheelBets ? player.args.wheelBets : []
      const currentBet = { color, betAmount }
      betsArgs.push(currentBet)

      player = await this.playersService.updateMiniGamePlayer(
        player.userId,
        mini_games_name.wheel,
        {
          gameId: this.state.gameId,
          inCurrentGame: true,
          betAmount: betAmount + (player.betAmount || 0),
          args: { wheelBets: betsArgs },
        },
      )

      const playerData = pickCurrentGamePlayersData(
        player,
        this.gameServersConfig.gameServers.get(player.serverId).NAME,
      )
      if (players.length) {
        await this.playersService.sendUpdatesToPlayers(
          mini_games_name.wheel,
          MINI_GAMES_MESSAGES_TYPES.WHEEL_BET,
          { currentBet, player: playerData },
        )
      }
    }

    this.logger.debug(
      `[betWheel] Ending wheel bet - update isBetInProgress. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
    )
    await this.playersService.updateMiniGamePlayer(user.user.id, mini_games_name.wheel, {
      isBetInProgress: false,
    })
    this.logger.debug(
      `[betWheel] End wheel bet. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
    )

    return 'ok'
  }
}
