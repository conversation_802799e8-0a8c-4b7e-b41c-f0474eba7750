import { forwardRef, Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { PrismaService } from '@majestic-backend/database'
import { RedisClient, RedisService } from '@majestic-backend/redis'
import { logError } from '@majestic-backend/utils'
import { AppBaseService } from '../../app/app.base.service'
import { v4 as uuidv4 } from 'uuid'
import { IRedisPayload, MinigameUpdatePlayerDonate } from '../types'
import { PlayersService } from './players.service'

@Injectable()
export class RedisListenerService extends AppBaseService implements OnModuleInit {
  private logger = new Logger(RedisListenerService.name)
  private eventHandlers: Map<string, (data: IRedisPayload) => void> = new Map()
  private redisClient: RedisClient | null = null
  events = {
    'socket.onTimeout': this.removePlayerFromSpectators.bind(this),
  }

  constructor(
    protected readonly database: PrismaService,
    private readonly redisService: RedisService,
    @Inject(forwardRef(() => PlayersService))
    private readonly playersService: PlayersService,
  ) {
    super(database)
  }

  async onModuleInit() {
    this.logger.debug(`[onModuleInit] replica id: ${process.env['REPLICA']}`)

    try {
      this.redisClient = await this.redisService.createClient().connect()
      for (const event of Object.keys(this.events)) {
        this.redisClient?.subscribe(event, data => this.onMessage(event, data))
      }
      this.logger.log('Success init Redis listener')
    } catch (e) {
      this.logger.error('Cannot init Redis listener')
      logError(this.logger, e)
    }
  }

  private async onMessage(channel: string, data: string) {
    this.logger.verbose(`Got message: ${channel}`)

    let parsed = null
    try {
      parsed = JSON.parse(data)

      await this.events[channel]?.(parsed)
    } catch (e) {
      this.logger.error(`Error on message parsing. Channel: ${channel}`, e)
      logError(this.logger, e)
    }
  }

  async subscribeToResponse(requestId: string, handler: (data: IRedisPayload) => void) {
    const channel = `rpc:response:${requestId}`
    this.eventHandlers.set(requestId, handler)

    this.logger.debug(`[subscribeToResponse] try subscribe to channel: ${channel}`)
    await this.redisClient?.subscribe(channel, message =>
      this.handleRpcResponse(requestId, message),
    )
    this.logger.debug(`[subscribeToResponse] subscribed to channel: ${channel}`)
  }

  private async handleRpcResponse(requestId: string, data: string) {
    this.logger.debug(`[handleRpcResponse] got into rpc response. RequestId: ${requestId}`)
    const handler = this.eventHandlers.get(requestId)

    if (!handler) {
      this.logger.warn(`Received response for unknown requestId: ${requestId}`)
      return
    }

    let parsed: IRedisPayload | undefined

    try {
      parsed = JSON.parse(data) as IRedisPayload
      handler(parsed)
    } catch (e) {
      this.logger.error(`Error parsing message for requestId: ${requestId}`, e)
    } finally {
      this.logger.debug(`[handleRpcResponse] unsibscribe from channel. RequestId: ${requestId}`)
      await this.redisClient?.unsubscribe(`rpc:response:${requestId}`)
      this.eventHandlers.delete(requestId)
    }

    return parsed
  }

  async sendRpcRequest(
    channel: string,
    payload: MinigameUpdatePlayerDonate,
    timeoutMs = 5000,
  ): Promise<IRedisPayload> {
    const requestId = uuidv4()

    return new Promise((resolve, reject) => {
      this.logger.debug(`[sendRpcRequest] start send rpc request. RequestId: ${requestId}`)
      let resolved = false

      const cleanup = () => {
        resolved = true
      }

      this.subscribeToResponse(requestId, data => {
        this.logger.debug(
          `[sendRpcRequest] resolved: ${resolved}. RequestId: ${requestId}. Data: ${JSON.stringify(
            data,
          )}`,
        )
        if (resolved) return
        cleanup()

        this.logger.debug(
          `[sendRpcRequest] Resolve prc promise. RequestId: ${requestId}. Data: ${JSON.stringify(
            data,
          )}`,
        )
        resolve(data)
      })

      if (timeoutMs > 0) {
        this.logger.debug(
          `[sendRpcRequest] start setTimeout. RequestId: ${requestId}. Time: ${timeoutMs}`,
        )
        setTimeout(() => {
          this.logger.debug(
            `[sendRpcRequest] timeout end. RequestId: ${requestId}. Time: ${timeoutMs}`,
          )
          if (!resolved) {
            cleanup()
            reject(new Error(`RPC Timeout (${channel})`))
          }
        }, timeoutMs)
      }
      this.redisService.publish(channel, { ...payload, requestId })
    })
  }

  async removePlayerFromSpectators({
    serverId,
    accountId,
  }: {
    serverId: string
    accountId: number
  }) {
    this.logger.debug(`[removePlayerFromSpectators] ServerId: ${serverId}, AccountId: ${accountId}`)

    await this.playersService.removePlayerFromSpectators(serverId, accountId)
  }
}
