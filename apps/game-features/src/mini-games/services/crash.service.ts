import { BackendModelEnum, PrismaService } from '@majestic-backend/database'
import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { AppBaseService } from '../../app/app.base.service'
import { RedisLockService, RedisService } from '@majestic-backend/redis'
import {
  MiniGamesAddPlayerCrash,
  MiniGamesBetCrash,
  MiniGamesGetDataCrash,
  MiniGamesGetHistoryItemCrash,
  MiniGamesGetHistoryListCrash,
} from '@majestic-backend/contracts'
import { IUser, MINI_GAMES_MESSAGES_TYPES, OperationType } from '@majestic-backend/types'
import crypto from 'crypto'
import {
  getRandomNumber,
  isServerRestarting,
  pickCrashBetsData,
  pickCurrentGamePlayersData,
  sleep,
} from '../helpers'
import { UtilsService } from './utils.service'
import { mini_games_name } from '@majestic-backend/prisma-client-backend'
import {
  ICrashStateArgs,
  IMiniGamesGamesUpdatePayload,
  IMiniGameState,
  IPlayerArgs,
} from '../types'
import { MiniGamesNames, MiniGamesDonateType, MiniGamesGameId } from '../enum'
import { PlayersService } from './players.service'
import { GameService } from './game.service'
import { CrashStateDate, MinigamesBetAmounts } from '@shared_config/minigames/state'
import { GameServersConfig } from '@majestic-backend/config'

@Injectable()
export class CrashService extends AppBaseService implements OnModuleInit {
  private logger = new Logger(CrashService.name)

  private historyLimit = 36
  private autoTakePlayers = []

  private state: IMiniGameState
  private stateArgs: ICrashStateArgs

  private initLockKey = 'crash:init:lock'
  private initLockTTL = 5
  private startGameLockKey = 'crash:startGame:lock'
  private startGameLockTTL = 10
  private lastUpdateKey = 'minigame:crash:lastUpdate'

  constructor(
    protected readonly databaseService: PrismaService,
    private readonly redisService: RedisService,
    private readonly utilsService: UtilsService,
    private readonly redisLockService: RedisLockService,
    private readonly playersService: PlayersService,
    private readonly gameService: GameService,
    private readonly gameServersConfig: GameServersConfig,
  ) {
    super(databaseService)
  }

  async onModuleInit() {
    const initLockAcquired = await this.redisLockService.acquireLock(this.initLockKey)

    if (initLockAcquired) {
      this.logger.debug('Crash init lock acquired. Start init()')

      await this.init()
      await this.redisLockService.releaseLock(this.initLockKey)

      this.logger.debug('Crash data initialized')
    } else {
      this.logger.debug('Crash init lock not acquired. Waiting for initialization to complete')
      await this.waitForCompleteInit(this.initLockKey)
    }

    this.startGameLoop()
  }

  private async getState() {
    const { state, stateArgs } = await this.gameService.getMiniGameStateWithArgs(
      mini_games_name.crash,
    )

    this.state = state
    this.stateArgs = stateArgs
  }

  private async updateState(data: IMiniGamesGamesUpdatePayload) {
    await this.getState()
    const { state, stateArgs } = await this.gameService.updateMiniGameState(
      MiniGamesGameId.CRASH,
      mini_games_name.crash,
      data,
    )

    this.state = state
    this.stateArgs = stateArgs
  }

  async init() {
    const crashGamesModel = this.databaseService.getBackendModel(BackendModelEnum.CrashGamesModel)
    const crashLastGame = await crashGamesModel.getCrashGame({
      orderBy: {
        id: 'desc',
      },
    })

    await this.getState()
    await this.updateState({
      gameId: (crashLastGame && crashLastGame.id) || 0,
      isActive: !this.state.isActive ? false : true,
      isTimerActive: false,
      timerTimeStart: null,
      isGameActive: false,
      gameTimeStart: null,
      args: JSON.stringify({
        ...this.stateArgs,
        endGame: null,
        crashedAt: null,
        gameBalance: 0,
        prevX: null,
        X1Speens: 1,
      }),
    })

    this.logger.debug(`init gameId: ${this.state.gameId}`)
  }

  async waitForCompleteInit(initLockKey: string) {
    while (await this.redisService.exists(initLockKey)) {
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    this.logger.debug('Crash init completed by another replica')
  }

  async startGameLoop() {
    const tryStartGame = async () => {
      if (await this.utilsService.isGameHandledElsewhere(MiniGamesNames.CRASH)) {
        this.logger.debug(`[startGameLoop] Game is handled by other replica. Try start game later`)
        setTimeout(() => {
          tryStartGame()
        }, 5000)
        return
      }

      this.logger.debug(`[startGameLoop] Game is not handled by anyone. Try lock`)
      const lockAcquired = await this.redisLockService.acquireLock(this.startGameLockKey)

      if (!lockAcquired) {
        this.logger.debug(`[startGameLoop] Other replica acquired lock. Try start game later`)
        setTimeout(() => {
          tryStartGame()
        }, 5000)
        return
      }

      await this.redisService.set(this.lastUpdateKey, new Date().getTime())
      const updateActiveCrash = setInterval(async () => {
        await this.redisService.set(this.lastUpdateKey, new Date().getTime())
      }, 2000)

      try {
        this.logger.debug(`[startGameLoop] Lock acquired`)
        await this.getState()
        if (this.state.isGameInProgress) {
          this.logger.debug(`[startGameLoop] Last game not ended. Refund bets`)
          await this.playersService.refundBets(mini_games_name.crash)
          this.logger.debug(`[startGameLoop] Clear current game players`)
          await this.playersService.clearCurrentGamePlayers(mini_games_name.crash)
          const players = await this.playersService.getMiniGamePlayers(mini_games_name.crash)
          this.logger.debug(`[startGameLoop] Remove restarted game bets`)
          await this.utilsService.removeRestartedMinigameBets(
            mini_games_name.crash,
            this.state.gameId + 1,
          )

          await this.updateState({
            isGameInProgress: false,
          })

          for (const player of players) {
            await this.utilsService.longNotifyTrans(
              player.accountId,
              player.serverId,
              'fun.minigamesF2.errors.gameRestarted',
              'error',
            )
          }
        }

        this.logger.debug(`[startGameLoop] Release lock and start game`)
        await this.redisLockService.releaseLock(this.startGameLockKey)
        await this.startGame()
      } finally {
        this.logger.debug(`[startGameLoop] Clear interval and delete lastUpdate`)
        clearInterval(updateActiveCrash)
        await this.redisService.del(this.lastUpdateKey)
      }

      tryStartGame()
    }

    tryStartGame()
  }

  private async getTotalBetsSum(): Promise<number> {
    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.crash,
    )

    return currentGamePlayers.reduce((sum, player) => sum + player.betAmount, 0)
  }

  private async generate_xNew() {
    await this.getState()

    const bias = this.stateArgs.prevX === 1.0 ? 0.25 : 0 // если до этого был 1.00 — увеличиваем шанс 1.00 снова
    const resetChance = Math.min(25, this.stateArgs.X1Speens * 2 + bias * 100) // макс 15%
    const randonNumber = getRandomNumber(0, 100)

    // Check if the operation should execute based on probability (X1Speens * 4)
    if (randonNumber <= resetChance) {
      // Update the state: reset X1Speens to 1 and set prevX to 1.0
      await this.updateState({
        args: JSON.stringify({
          ...this.stateArgs,
          X1Speens: 1,
          prevX: 1.0,
        }),
      })

      return 1.0
    }

    // Define a large number for further calculations
    const e = 2 ** 32
    //const h = Math.floor(Math.getRandomNumber() * 2 ** 32);

    // Generate a random number h in the range [0, 2**32 - 1]
    const h = crypto.randomInt(0xffffffff)
    // Calculate x using the given formula, rounding to two decimal places
    let x = Math.abs(Math.floor((100 * e - h) / (e - h)) / 100)

    // If x exceeds 20, generate a new random value within the range [20.01, 23.05]
    if (x > 20) {
      x = getRandomNumber(20.01, 23.05)
    }

    // Update the state, incrementing X1Speens and storing the new prevX value
    await this.updateState({
      args: JSON.stringify({
        ...this.stateArgs,
        X1Speens: ++this.stateArgs.X1Speens,
        prevX: x,
      }),
    })

    // Return the generated x value
    return x
  }

  private async getCurrentX(): Promise<number> {
    await this.getState()
    const time = new Date().getTime() - this.state.gameTimeStart.getTime()

    return 1.00006 ** time
  }

  async startGame() {
    await this.getState()

    if (this.state.isActive && isServerRestarting()) {
      this.logger.debug(`[startGame] Server is restarting. GameId: ${this.state.gameId}`)
      await this.updateState({
        isActive: false,
        args: JSON.stringify({
          ...this.stateArgs,
          isPausedDueToRestart: true,
        }),
      })

      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.crash,
        MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_GAME,
        {
          state: { isActive: false },
        },
      )
    }

    if (!this.state.isActive) {
      this.logger.debug(`[startGame] Current game is not active. GameId: ${this.state.gameId}`)
      await sleep(5000)
      return
    }

    let players = await this.playersService.getMiniGamePlayers(mini_games_name.crash)
    if (!players.length) {
      await sleep(1000)

      return
    }

    this.logger.debug(`++++++++++++ crash start game ++++++++++++`)

    await this.updateState({
      isTimerActive: true,
      timerTimeStart: new Date(),
      gameId: {
        increment: 1,
      },
      isGameInProgress: true,
    })

    let currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.crash,
    )

    players = await this.playersService.getMiniGamePlayers(mini_games_name.crash)
    if (players.length) {
      const filteredCurrentGamePlayersData = currentGamePlayers.map(item =>
        pickCurrentGamePlayersData(
          item,
          this.gameServersConfig.gameServers.get(item.serverId).NAME,
        ),
      )
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.crash,
        MINI_GAMES_MESSAGES_TYPES.CRASH_START_TIMER,
        {
          state: {
            ...this.state,
            args: JSON.stringify({
              endGame: this.stateArgs.endGame,
              crashedAt: this.stateArgs.crashedAt,
            }),
          },
          currentGame: {
            id: this.state.gameId,
            currentGamePlayers: filteredCurrentGamePlayersData,
          },
        },
      )
    }

    this.logger.debug(`[startGame] Start timer init time. GameId: ${this.state.gameId}`)
    await sleep(CrashStateDate.timerInitTime)

    await this.updateState({
      isTimerActive: false,
      isGameActive: true,
      gameTimeStart: new Date(),
    })

    const generatedX = await this.generate_xNew()
    const crashX = parseFloat(generatedX.toFixed(2))
    this.logger.debug(
      `[startGame] Generate X. generatedX: ${generatedX}. crashX: ${crashX}. GameId: ${this.state.gameId}`,
    )

    players = await this.playersService.getMiniGamePlayers(mini_games_name.crash)
    if (players.length) {
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.crash,
        MINI_GAMES_MESSAGES_TYPES.CRASH_START_GAME,
        {
          state: {
            ...this.state,
            args: JSON.stringify({
              endGame: this.stateArgs.endGame,
              crashedAt: this.stateArgs.crashedAt,
            }),
          },
        },
      )
    }

    // Calculating crashTime: determining how long it takes to reach crashX based on exponential growth with a base of 1.00006.
    const crashTime = Math.log(crashX) / Math.log(1.00006)
    this.logger.debug(`[startGame] generated crashTime: ${crashTime}. GameId: ${this.state.gameId}`)

    currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(mini_games_name.crash)
    currentGamePlayers.forEach(async player => {
      const playerArgs: IPlayerArgs = player.args
      if (!playerArgs.crashAutoTakeX || playerArgs.exit || playerArgs.crashAutoTakeX > crashX)
        return

      await sleep(Math.log(playerArgs.crashAutoTakeX) / Math.log(1.00006))

      this.logger.debug(
        `[startGame] Start auto take player. AccountId: ${player.accountId}. GameId: ${this.state.gameId}`,
      )
      // const updPlayer = await this.playersService.updateMiniGamePlayerByArgs(
      //   player.userId,
      //   mini_games_name.crash,
      //   {
      //     args: {
      //       exit: Number(playerArgs.crashAutoTakeX.toFixed(2)),
      //       exitTime: new Date(),
      //     },
      //   },
      // )
      // const updPlayerArgs = updPlayer.args
      // if (!updPlayer) {
      //   this.logger.debug(
      //     `[startGame] Player already has exit. AccountId: ${player.accountId}. ServerId: ${player.serverId}`,
      //   )
      //   return
      // }

      // await this.playersService.updateMiniGamePlayer(player.userId, mini_games_name.crash, {
      //   args: {
      //     ...updPlayer.args,
      //     crashAutoTakeX: null,
      //   },
      // })

      // this.logger.debug(
      //   `[startGame] Player auto take. AccountId: ${player.accountId}. GameId: ${this.state.gameId}`,
      // )
      await this.takeCrash(player.accountId, player.userId, playerArgs.crashAutoTakeX)
    })

    if (crashTime) {
      this.logger.debug(`[startGame] Start timer crashTime. GameId: ${this.state.gameId}`)
      await sleep(crashTime)
    }

    await this.updateState({
      args: JSON.stringify({
        ...this.stateArgs,
        endGame: new Date(),
        crashedAt: crashX,
      }),
      isGameActive: false,
    })

    players = await this.playersService.getMiniGamePlayers(mini_games_name.crash)
    if (players.length) {
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.crash,
        MINI_GAMES_MESSAGES_TYPES.CRASH_CRASH,
        { crashX },
      )
    }

    const crashGamesModel = this.databaseService.getBackendModel(BackendModelEnum.CrashGamesModel)
    currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(mini_games_name.crash)
    const crashEndGame = await crashGamesModel.createCrashGame({
      playersCount: currentGamePlayers.length,
      crashedAt: crashX,
      bank: await this.getTotalBetsSum(),
      won: currentGamePlayers.reduce((total, player) => {
        const playerArgs: IPlayerArgs = player.args
        if (playerArgs.exit) {
          return total + (player.betAmount * playerArgs.exit - player.betAmount)
        }
        return total
      }, 0),
      lost: currentGamePlayers.reduce((total, player) => {
        const playerArgs: IPlayerArgs = player.args
        if (!playerArgs.exit) {
          return total + player.betAmount
        }
        return total
      }, 0),
    })

    players = await this.playersService.getMiniGamePlayers(mini_games_name.crash)
    if (players.length) {
      await this.getState()
      currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
        mini_games_name.crash,
      )

      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.crash,
        MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_GAME,
        {
          state: {
            ...this.state,
            args: JSON.stringify({
              endGame: this.stateArgs.endGame,
              crashedAt: this.stateArgs.crashedAt,
            }),
          },
          crashLastGame: crashEndGame,
        },
      )
    }

    this.logger.debug(`[startGame] Start timer game time end. GameId: ${this.state.gameId}`)
    await sleep(CrashStateDate.gameTimeEnd)

    await this.updateState({
      args: JSON.stringify({ ...this.stateArgs, endGame: null, crashedAt: null }),
      isGameActive: false,
      gameId: crashEndGame.id,
    })

    this.logger.debug(`[startGame] Set isGameInProgress to false. GameId: ${this.state.gameId}`)
    await this.updateState({
      isGameInProgress: false,
    })
    this.logger.debug(`[startGame] Clearing current game players. GameId: ${this.state.gameId}`)
    await this.playersService.clearCurrentGamePlayers(mini_games_name.crash)
    this.logger.debug(`[startGame] Removing non active players. GameId: ${this.state.gameId}`)
    await this.playersService.removeNonActivePlayers(mini_games_name.crash)
    this.autoTakePlayers = []

    await sleep(100)
    this.logger.debug(`++++++++++++ crash end game ++++++++++++`)

    return
  }

  async addPlayer(player: IUser, data: Omit<MiniGamesAddPlayerCrash.Request, 'session'>) {
    const { state } = await this.gameService.getMiniGameStateWithArgs(mini_games_name.crash)
    if (!state.isActive) {
      this.logger.debug(
        `[addPlayer] Trying add player to crash when state is not active. AccountId: ${data.characterId}. GameId: ${state.gameId}`,
      )
      await this.redisService.publishToSockets({
        receivers: data.characterId,
        serverId: player.serverId,
        type: MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_GAME,
        payload: {
          state: { isActive: false },
        },
      })

      return 'gameNotActive'
    }

    this.logger.debug(
      `Start ADD player to all crash player list. AccountId: ${data.characterId}. GameId: ${state.gameId}`,
    )

    await this.getState()
    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.crash,
    )
    const crashGamesModel = this.databaseService.getBackendModel(BackendModelEnum.CrashGamesModel)
    const crashEndGame = await crashGamesModel.getCrashGame({
      orderBy: {
        id: 'desc',
      },
    })
    const filteredCurrentGamePlayersData = currentGamePlayers.map(item =>
      pickCurrentGamePlayersData(item, this.gameServersConfig.gameServers.get(item.serverId).NAME),
    )
    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_GAME,
      payload: {
        state: {
          ...this.state,
          args: JSON.stringify({
            endGame: this.stateArgs.endGame,
            crashedAt: this.stateArgs.crashedAt,
          }),
        },
        currentGame: { id: this.state.gameId, currentGamePlayers: filteredCurrentGamePlayersData },
        crashLastGame: crashEndGame,
      },
    })

    const players = await this.playersService.getMiniGamePlayers(mini_games_name.crash)
    if (
      players.some(
        player =>
          player.accountId === data.characterId && player.minigameName === mini_games_name.crash,
      )
    ) {
      await this.playersService.updateMiniGamePlayer(player.user.id, mini_games_name.crash, {
        isActive: true,
      })
      this.logger.debug(
        `Player return to game. Set player is active. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      return 'ok'
    }

    const currentAccount = player.accounts.find(account => account.id === data.characterId)
    await this.playersService.createMiniGamePlayer({
      minigameName: mini_games_name.crash,
      userId: player.user.id,
      accountId: data.characterId,
      serverId: player.serverId,
      login: currentAccount.login,
      socialClub: player.user.lastSocialClub,
      args: { exit: null, crashAutoTakeX: null, exitTime: null },
      isActive: true,
      gender: currentAccount.gender,
    })

    this.logger.debug(
      `Added player to all crash player list. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    return 'ok'
  }

  async removePlayer(player: IUser, data: Omit<MiniGamesAddPlayerCrash.Request, 'session'>) {
    await this.getState()
    const playerInGame = await this.playersService.getMiniGamePlayer(
      mini_games_name.crash,
      data.characterId,
    )

    this.logger.debug(
      `Start REMOVE player from all crash player list. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    if (!playerInGame) {
      this.logger.debug(
        `Player not found in crash player list. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      return 'notFound'
    }

    if (!playerInGame.inCurrentGame && !playerInGame.isBetInProgress) {
      await this.playersService.removeMiniGamePlayer(
        player.user.id,
        data.characterId,
        mini_games_name.crash,
      )
      this.logger.debug(
        `Removed player from all crash player list. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
    } else {
      await this.playersService.updateMiniGamePlayer(player.user.id, mini_games_name.crash, {
        isActive: false,
      })
      this.logger.debug(
        `Set player is non active. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
    }

    return 'ok'
  }

  async getDataCrash(player: IUser, data: Omit<MiniGamesGetDataCrash.Request, 'session'>) {
    this._checkAccountValid(player.accounts, data.characterId)

    const { state } = await this.gameService.getMiniGameStateWithArgs(mini_games_name.crash)
    const stateArgs = JSON.parse(state.args)
    const currentGamePlayers = await this.playersService.getCurrentMiniGamePlayers(
      mini_games_name.crash,
    )

    const filteredCurrentGamePlayersData = currentGamePlayers.map(item =>
      pickCurrentGamePlayersData(item, this.gameServersConfig.gameServers.get(item.serverId).NAME),
    )
    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_GAME,
      payload: {
        state: {
          ...this.state,
          args: JSON.stringify({
            endGame: stateArgs.endGame,
            crashedAt: stateArgs.crashedAt,
          }),
        },
        currentGame: { id: state.gameId, currentGamePlayers: filteredCurrentGamePlayersData },
      },
    })

    return {
      state: state,
      currentGame: { id: state.gameId, currentGamePlayers },
    }
  }

  async getCrashHistoryList(
    player: IUser,
    data: Omit<MiniGamesGetHistoryListCrash.Request, 'session'>,
  ) {
    this._checkAccountValid(player.accounts, data.characterId)

    const crashGamesModel = this.databaseService.getBackendModel(BackendModelEnum.CrashGamesModel)
    const crashLastGames = await crashGamesModel.getCrashGames({
      orderBy: {
        id: 'desc',
      },
      take: this.historyLimit,
    })

    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_HISTORY_LIST,
      payload: { data: crashLastGames },
    })

    return crashLastGames
  }

  async getCrashHistoryItem(
    player: IUser,
    data: Omit<MiniGamesGetHistoryItemCrash.Request, 'session'>,
  ) {
    this._checkAccountValid(player.accounts, data.characterId)

    const crashGamesModel = this.databaseService.getBackendModel(BackendModelEnum.CrashGamesModel)
    const crashBetsModel = this.databaseService.getBackendModel(BackendModelEnum.CrashBetsModel)

    const game = await crashGamesModel.getCrashGame({
      where: {
        id: data.gameId,
      },
    })
    const bets = await crashBetsModel.getCrashBets({
      where: {
        gameId: data.gameId,
      },
    })

    const filteredBetsData = bets.map(item =>
      pickCrashBetsData(item, this.gameServersConfig.gameServers.get(item.serverId).NAME),
    )
    await this.redisService.publishToSockets({
      receivers: data.characterId,
      serverId: player.serverId,
      type: MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_HISTORY_ITEM,
      payload: { data: { game, bets: filteredBetsData } },
    })

    return {
      game,
      bets: filteredBetsData,
    }
  }

  async betCrash(user: IUser, data: Omit<MiniGamesBetCrash.Request, 'session'>) {
    this._checkAccountValid(user.accounts, data.characterId)
    this.logger.debug(
      `[betCrash] Start crash bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    const { characterId, betAmount } = data
    const donate = user.user.donate

    if (isServerRestarting()) {
      this.logger.debug(
        `[betCrash] Server is restarting. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.casino.restartCantBet',
        'error',
      )

      return { ok: false }
    }

    await this.getState()

    if (!betAmount || !this.state.isActive) {
      this.logger.debug(
        `[betCrash] No bet amount or game is not active. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      return { ok: false }
    }

    if (this.stateArgs.crashedAt || this.state.isGameActive) {
      this.logger.debug(
        `[betCrash] Current game is not stopped. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.gameNotStoped',
        'error',
      )
      return { ok: false }
    }

    if (betAmount < MinigamesBetAmounts.crash.min) {
      this.logger.debug(
        `[betCrash] Bet too small. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.betTooSmall',
        'error',
      )
      return { ok: false }
    }

    if (betAmount > MinigamesBetAmounts.crash.max) {
      this.logger.debug(
        `[betCrash] Bet too big. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.betTooBig',
        'error',
      )
      return { ok: false }
    }

    if (donate < betAmount) {
      this.logger.debug(
        `[betCrash] Not enough money. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.notEnoughMoney',
        'error',
      )
      return { ok: false }
    }

    let player = await this.playersService.updateMiniGamePlayer(
      user.user.id,
      mini_games_name.crash,
      {
        isBetInProgress: true,
      },
    )

    if (player.inCurrentGame) {
      this.logger.debug(
        `[betCrash] Player already in game. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      return { ok: false }
    }

    this.logger.debug(
      `[betCrash] start decrease player donate. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )
    const serverResponse = await this.playersService.updatePlayerDonate({
      userId: player.userId,
      accountId: player.accountId,
      serverId: user.serverId,
      betAmount,
      type: OperationType.DECREASE,
      comment: 'logs.donate_log.minigamesF2CrashBet',
      args: JSON.stringify({
        game: 'crash',
        gameId: this.state.gameId,
      }),
    })
    // const serverResponse = { data: { success: true } }
    this.logger.debug(
      `[betCrash] end decrease player donate. Response: ${JSON.stringify(
        serverResponse,
      )}. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    if (!serverResponse || !serverResponse.data.success) {
      this.logger.debug(
        `[betCrash] Not got server PRC response. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.serverNotResponse',
        'error',
      )
      return { ok: false }
    }

    if (this.stateArgs.crashedAt || this.state.isGameActive) {
      this.logger.debug(
        `[betCrash] Current game already started. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )

      await this.playersService.refundBet(
        player.serverId,
        player.accountId,
        mini_games_name.crash,
        this.state.gameId,
        betAmount,
      )

      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.gameNotStoped',
        'error',
      )
      return { ok: false }
    }

    const crashBetsModel = this.databaseService.getBackendModel(BackendModelEnum.CrashBetsModel)

    try {
      this.logger.debug(
        `[betCrash] Creating crash bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )
      await crashBetsModel.transactionCreateCrashBet(
        MiniGamesNames.CRASH,
        betAmount,
        player,
        this.state.gameId,
      )
    } catch (err) {
      this.logger.debug(
        `[betCrash] Game not stopped, cant create bet. Refund bet. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
      )

      await this.playersService.refundBet(
        player.serverId,
        player.accountId,
        mini_games_name.crash,
        this.state.gameId,
        betAmount,
      )

      await this.utilsService.longNotifyTrans(
        data.characterId,
        user.serverId,
        'fun.minigamesF2.errors.gameNotStoped',
        'error',
      )

      return { ok: false }
    }

    player = await this.playersService.updateMiniGamePlayer(user.user.id, mini_games_name.crash, {
      args: {
        ...player.args,
        crashAutoTakeX: null,
      },
    })

    this.logger.debug(
      `[betCrash] Creating minigamesF2 log. AccountId: ${data.characterId}. GameId: ${this.state.gameId}`,
    )

    this.redisService.publish(`${user.serverId}.miniGamesTriggerSeasonPassTask`, {
      userId: player.userId,
      accountId: player.accountId,
      triggerType: 'playMinigameF2',
      triggerValue: MiniGamesNames.CRASH,
    })

    await this.updateState({
      args: JSON.stringify({
        ...this.stateArgs,
        gameBalance: this.stateArgs.gameBalance + betAmount,
      }),
    })

    player = await this.playersService.updateMiniGamePlayer(player.userId, mini_games_name.crash, {
      gameId: this.state.gameId,
      inCurrentGame: true,
      betAmount,
      args: {
        ...player.args,
        exit: null,
      },
    })

    const players = await this.playersService.getMiniGamePlayers(mini_games_name.crash)
    if (players.length) {
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.crash,
        MINI_GAMES_MESSAGES_TYPES.CRASH_BET,
        {
          login: player.login,
          gender: player.gender,
          accountId: player.accountId,
          serverId: player.serverId,
          serverName: this.gameServersConfig.gameServers.get(player.serverId).NAME.toLowerCase(),
          betAmount,
        },
      )
    }

    const autoTakeX = data.autoTakeX ? parseFloat(data.autoTakeX.toFixed(2)) : null

    if (autoTakeX && autoTakeX > 1.0) {
      player = await this.playersService.updateMiniGamePlayer(
        player.userId,
        mini_games_name.crash,
        {
          args: {
            ...player.args,
            crashAutoTakeX: autoTakeX,
          },
        },
      )

      await this.redisService.publishToSockets({
        receivers: characterId,
        serverId: user.serverId,
        type: MINI_GAMES_MESSAGES_TYPES.CRASH_UPDATE_AUTO_TAKE,
        payload: { autoTakeX },
      })
    }

    this.logger.debug(
      `[betCrash] Ending crash bet - update isBetInProgress. AccountId: ${characterId}. GameId: ${this.state.gameId}`,
    )
    await this.playersService.updateMiniGamePlayer(player.userId, mini_games_name.crash, {
      isBetInProgress: false,
    })
    this.logger.debug(`[betCrash] End crash bet. AccountId: ${data.characterId}`)

    return 'ok'
  }

  async takeCrash(accountId: number, userId: number, autoTakeXOverride?: number) {
    await this.getState()

    const curX = autoTakeXOverride ? autoTakeXOverride : await this.getCurrentX()
    const exitX = Number(curX.toFixed(2))

    this.logger.debug(
      `[takeCrash] Start crash take. AccountId: ${accountId}. GameId: ${this.state.gameId}`,
    )
    const args = {
      exit: exitX,
      exitTime: new Date(),
    }
    if (autoTakeXOverride) {
      args['crashAutoTakeX'] = null
    }
    const { count } = await this.playersService.updateMiniGamePlayerByArgs(
      userId,
      mini_games_name.crash,
      {
        args,
      },
    )
    this.logger.debug(
      `[takeCrash] Set exit and exitTime. AccountId: ${accountId}. GameId: ${this.state.gameId}`,
    )

    if (count === 0) {
      this.logger.debug(
        `[takeCrash] Player already exited. AccountId: ${accountId}. GameId: ${this.state.gameId}`,
      )
      return { ok: false }
    }

    const player = await this.playersService.getMiniGamePlayer(mini_games_name.crash, accountId)

    if (!player.inCurrentGame) {
      this.logger.debug(
        `[takeCrash] Player not found in current game. AccountId: ${accountId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        player.accountId,
        player.serverId,
        'fun.minigamesF2.errors.playerNotFound',
        'error',
      )
      return { ok: false }
    }

    await this.getState()

    if (this.state.isTimerActive) {
      this.logger.debug(
        `[takeCrash] Current game not stopped. AccountId: ${accountId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        player.accountId,
        player.serverId,
        'fun.minigamesF2.errors.gameNotStoped',
        'error',
      )
      return { ok: false }
    }

    await this.getState()

    if (this.stateArgs.crashedAt || exitX <= 1) {
      this.logger.debug(
        `[takeCrash] Current game not stopped. AccountId: ${accountId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        player.accountId,
        player.serverId,
        'fun.minigamesF2.errors.gameStoped',
        'error',
      )
      return { ok: false }
    }

    let players = await this.playersService.getMiniGamePlayers(mini_games_name.crash)

    const crashBetsModel = this.databaseService.getBackendModel(BackendModelEnum.CrashBetsModel)
    const crashBet = await crashBetsModel.getCrashBet({
      where: {
        userId: player.userId,
        gameId: this.state.gameId,
      },
    })
    this.logger.debug(
      `[takeCrash] Updating crashBet. AccountId: ${accountId}. GameId: ${this.state.gameId}`,
    )
    await crashBetsModel.updateCrashBet({
      where: {
        id: crashBet.id,
      },
      data: {
        exitX,
      },
    })

    const amount = Math.trunc(player.betAmount * exitX)
    this.logger.debug(
      `[takeCrash] Counted amount: ${amount}. AccountId: ${accountId}. GameId: ${this.state.gameId}`,
    )

    await this.getState()

    this.logger.debug(
      `[takeCrash] start increase player donate. AccountId: ${player.accountId}. GameId: ${this.state.gameId}`,
    )
    const serverResponse = await this.playersService.updatePlayerDonate({
      userId: player.userId,
      accountId: player.accountId,
      serverId: player.serverId,
      betAmount: amount,
      type: OperationType.INCREASE,
      comment: 'logs.payment_log.minigamesF2',
      args: JSON.stringify({
        type: 'crash',
        sessionId: this.state.gameId,
        exitX,
        balance: this.stateArgs.gameBalance,
        serverId: player.serverId,
      }),
    })
    // const serverResponse = { data: { success: true } }
    this.logger.debug(
      `[takeCrash] end increase player donate. Response: ${JSON.stringify(
        serverResponse,
      )}. AccountId: ${player.accountId}. GameId: ${this.state.gameId}`,
    )

    if (!serverResponse || !serverResponse.data.success) {
      this.logger.debug(
        `[takeCrash] Not got server PRC response. AccountId: ${player.accountId}. GameId: ${this.state.gameId}`,
      )
      await this.utilsService.longNotifyTrans(
        player.accountId,
        player.serverId,
        'fun.minigamesF2.errors.serverNotResponse',
        'error',
      )
      return { ok: false }
    }

    this.logger.debug(
      `[startGame] Set player got reward. CrashGameId: ${this.state.gameId}. AccountId: ${player.accountId}`,
    )
    await this.playersService.updateMiniGamePlayer(player.userId, mini_games_name.crash, {
      gotReward: true,
    })

    this.logger.debug(`[takeCrash] Creating minigamesF2 log. GameId: ${this.state.gameId}`)
    await this.utilsService.createMinigamesF2Log({
      userId: player.userId,
      accountId: player.accountId,
      game: MiniGamesNames.CRASH,
      type: MiniGamesDonateType.WIN,
      amount,
      gameId: this.state.gameId,
      args: { exitX },
      serverId: player.serverId,
    })

    await this.updateState({
      args: JSON.stringify({
        ...this.stateArgs,
        gameBalance: this.stateArgs.gameBalance - amount,
      }),
    })

    players = await this.playersService.getMiniGamePlayers(mini_games_name.crash)
    if (players.length) {
      await this.playersService.sendUpdatesToPlayers(
        mini_games_name.crash,
        MINI_GAMES_MESSAGES_TYPES.CRASH_TAKE,
        {
          accountId: player.accountId,
          serverId: player.serverId,
          serverName: this.gameServersConfig.gameServers.get(player.serverId).NAME.toLowerCase(),
          exit: player.args.exit,
          exitTime: player.args.exitTime,
        },
      )
    }

    this.autoTakePlayers = this.autoTakePlayers.filter(p => p.accountId !== player.accountId)

    this.logger.debug(
      `[betCrash] End crash take. AccountId: ${accountId}. GameId: ${this.state.gameId}`,
    )

    return 'ok'
  }
}
