import { Controller, UseInterceptors } from '@nestjs/common'
import { PrismaService } from '@majestic-backend/database'
import { MessagePattern } from '@nestjs/microservices'
import { AppBaseController } from '../../app/app.base.controller'
import {
  MiniGamesAddPlayerWheel,
  MiniGamesBetWheel,
  MiniGamesGetDataWheel,
  MiniGamesGetHistoryItemWheel,
  MiniGamesGetHistoryListWheel,
  MiniGamesRemovePlayerWheel,
} from '@majestic-backend/contracts'
import { nest } from '@majestic-backend/utils'
import { WheelService } from '../services'

@Controller()
@UseInterceptors(nest.RpcLoggerInterceptor)
export class WheelController extends AppBaseController {
  constructor(
    protected readonly databaseService: PrismaService,
    private wheelService: WheelService,
  ) {
    super(databaseService)
  }

  @MessagePattern(MiniGamesAddPlayerWheel.topic)
  async addPlayerWheel(data: MiniGamesAddPlayerWheel.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.wheelService.addPlayer(user, payload)
  }

  @MessagePattern(MiniGamesRemovePlayerWheel.topic)
  async removePlayerWheel(data: MiniGamesRemovePlayerWheel.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.wheelService.removePlayer(user, payload)
  }

  @MessagePattern(MiniGamesGetDataWheel.topic)
  async getDataWheel(data: MiniGamesGetDataWheel.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.wheelService.getDataWheel(user, payload)
  }

  @MessagePattern(MiniGamesGetHistoryListWheel.topic)
  async getWheelHistoryList(data: MiniGamesGetHistoryListWheel.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.wheelService.getWheelHistoryList(user, payload)
  }

  @MessagePattern(MiniGamesGetHistoryItemWheel.topic)
  async getWheelHistoryItem(data: MiniGamesGetHistoryItemWheel.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.wheelService.getWheelHistoryItem(user, payload)
  }

  @MessagePattern(MiniGamesBetWheel.topic)
  async betWheel(data: MiniGamesBetWheel.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.wheelService.betWheel(user, payload)
  }
}
