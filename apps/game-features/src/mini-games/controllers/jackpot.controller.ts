import { Controller, UseInterceptors } from '@nestjs/common'
import { PrismaService } from '@majestic-backend/database'
import { MessagePattern } from '@nestjs/microservices'
import { AppBaseController } from '../../app/app.base.controller'
import {
  MiniGamesAddPlayerJackpot,
  MiniGamesBetJackpot,
  MiniGamesGetDataJackpot,
  MiniGamesGetHistoryItemJackpot,
  MiniGamesGetHistoryListJackpot,
  MiniGamesRemovePlayerJackpot,
} from '@majestic-backend/contracts'
import { nest } from '@majestic-backend/utils'
import { JackpotService } from '../services'

@Controller()
@UseInterceptors(nest.RpcLoggerInterceptor)
export class JackpotController extends AppBaseController {
  constructor(
    protected readonly databaseService: PrismaService,
    private jackpotService: JackpotService,
  ) {
    super(databaseService)
  }

  @MessagePattern(MiniGamesAddPlayerJackpot.topic)
  async addPlayerJackpot(data: MiniGamesAddPlayerJackpot.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.jackpotService.addPlayer(user, payload)
  }

  @MessagePattern(MiniGamesRemovePlayerJackpot.topic)
  async removePlayerJackpot(data: MiniGamesRemovePlayerJackpot.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.jackpotService.removePlayer(user, payload)
  }

  @MessagePattern(MiniGamesGetDataJackpot.topic)
  async getDataJackpot(data: MiniGamesGetDataJackpot.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.jackpotService.getDataJackpot(user, payload)
  }

  @MessagePattern(MiniGamesGetHistoryListJackpot.topic)
  async getJackpotHistoryList(data: MiniGamesGetHistoryListJackpot.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.jackpotService.getJackpotHistoryList(user, payload)
  }

  @MessagePattern(MiniGamesGetHistoryItemJackpot.topic)
  async getJackpotHistoryItem(data: MiniGamesGetHistoryItemJackpot.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.jackpotService.getJackpotHistoryItem(user, payload)
  }

  @MessagePattern(MiniGamesBetJackpot.topic)
  async betJackpot(data: MiniGamesBetJackpot.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.jackpotService.betJackpot(user, payload)
  }
}
