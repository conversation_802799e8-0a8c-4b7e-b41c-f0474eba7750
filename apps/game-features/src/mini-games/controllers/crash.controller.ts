import { Controller, UseInterceptors } from '@nestjs/common'
import { PrismaService } from '@majestic-backend/database'
import { MessagePattern } from '@nestjs/microservices'
import { AppBaseController } from '../../app/app.base.controller'
import { CrashService } from '../services/crash.service'
import {
  MiniGamesAddPlayerCrash,
  MiniGamesBetCrash,
  MiniGamesGetDataCrash,
  MiniGamesGetHistoryItemCrash,
  MiniGamesGetHistoryListCrash,
  MiniGamesRemovePlayerCrash,
  MiniGamesTakeCrash,
} from '@majestic-backend/contracts'
import { nest } from '@majestic-backend/utils'

@Controller()
@UseInterceptors(nest.RpcLoggerInterceptor)
export class CrashController extends AppBaseController {
  constructor(
    protected readonly databaseService: PrismaService,
    private crashService: CrashService,
  ) {
    super(databaseService)
  }

  @MessagePattern(MiniGamesAddPlayerCrash.topic)
  async addPlayerCrash(data: MiniGamesAddPlayerCrash.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.crashService.addPlayer(user, payload)
  }

  @MessagePattern(MiniGamesRemovePlayerCrash.topic)
  async removePlayerCrash(data: MiniGamesRemovePlayerCrash.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.crashService.removePlayer(user, payload)
  }

  @MessagePattern(MiniGamesGetDataCrash.topic)
  async getDataCrash(data: MiniGamesGetDataCrash.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.crashService.getDataCrash(user, payload)
  }

  @MessagePattern(MiniGamesGetHistoryListCrash.topic)
  async getCrashHistoryList(data: MiniGamesGetHistoryListCrash.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.crashService.getCrashHistoryList(user, payload)
  }

  @MessagePattern(MiniGamesGetHistoryItemCrash.topic)
  async getCrashHistoryItem(data: MiniGamesGetHistoryItemCrash.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.crashService.getCrashHistoryItem(user, payload)
  }

  @MessagePattern(MiniGamesBetCrash.topic)
  async betCrash(data: MiniGamesBetCrash.Request) {
    const user = await this.getUserData(data.session)
    const { session, ...payload } = data

    return this.crashService.betCrash(user, payload)
  }

  @MessagePattern(MiniGamesTakeCrash.topic)
  async takeCrash(data: MiniGamesTakeCrash.Request) {
    const user = await this.getUserData(data.session)

    return this.crashService.takeCrash(data.characterId, user.user.id)
  }
}
