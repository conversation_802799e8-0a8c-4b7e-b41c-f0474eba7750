import { mini_games_name } from '@majestic-backend/prisma-client-backend'
import { OperationType, WheelColors } from '@majestic-backend/types'

export interface IMiniGameState {
  id: number
  minigameName: mini_games_name
  gameId: number
  isActive: boolean
  isTimerActive: boolean
  timerTimeStart: Date | null
  isGameActive: boolean
  gameTimeStart: Date | null
  args: string
  isGameInProgress: boolean
}

export interface IMiniGamePlayer {
  minigameName: mini_games_name
  userId: number
  accountId: number
  serverId: string
  login: string
  socialClub: string
  donate: number
  betAmount: number
  gameId: number | null
  inCurrentGame: boolean
  args: IPlayerArgs
  isActive: boolean
  gotReward: boolean
  gender: boolean
  isBetInProgress: boolean
}

export interface IMiniGamesGamesUpdatePayload {
  id?: number
  minigameName?: mini_games_name
  gameId?:
    | {
        increment: number
      }
    | number
  isActive?: boolean
  isTimerActive?: boolean
  timerTimeStart?: Date | string
  isGameActive?: boolean
  gameTimeStart?: Date | string
  args?: string
  isGameInProgress?: boolean
}

export interface IMiniGamesPlayerCreatePayload {
  minigameName: mini_games_name
  userId: number
  accountId: number
  serverId: string
  login: string
  socialClub: string
  donate?: number
  betAmount?: number
  gameId?: number | null
  inCurrentGame?: boolean
  args: IPlayerArgs
  isActive?: boolean
  gotReward?: boolean
  gender?: boolean
  isBetInProgress?: boolean
}

export interface IMiniGamesPlayerUpdatePayload {
  minigameName?: mini_games_name
  userId?: number
  accountId?: number
  serverId?: string
  login?: string
  socialClub?: string
  donate?: number
  betAmount?: number
  gameId?: number | null
  inCurrentGame?: boolean
  args?: IPlayerArgs
  isActive?: boolean
  gotReward?: boolean
  isBetInProgress?: boolean
}

export interface MinigameUpdatePlayerDonate {
  userId: number
  accountId: number
  serverId?: string
  betAmount: number
  type: OperationType
  args?: string
  comment: string
}

export interface IPlayerArgs {
  exit?: number | null
  crashAutoTakeX?: number | null
  exitTime?: Date | null
  ticket?: string | null
  dateBet?: Date | null
  wheelBets?: {
    color?: WheelColors | null
    betAmount?: number | null
  }[]
}

export interface IRefundBet {
  serverId: string
  accountId: number
  userId: number
  betAmount: number
  minigameName: mini_games_name
  gameId: number
}
