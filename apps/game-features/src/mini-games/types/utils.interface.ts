import { MiniGamesNames, MiniGamesDonateType } from '../enum'

export interface ICreateDonateLog {
  userId: number
  accountId: number
  amount: number
  comment: string
}

export interface ICreateMiniGamesLog {
  userId: number
  accountId: number
  game: MiniGamesNames
  type: MiniGamesDonateType
  amount: number
  gameId: number
  args: Record<string, any>
  serverId: string
}

export interface IGroupedRecievers {
  [serverId: string]: number[]
}
