export interface IJackpotStateArgs {
  winner: Record<string, any> | null
  winnerPosition: number | null
}

export interface IJackpotPlayerArgs {
  ticket: string | null
}

export interface IJackpotBet {
  userId: number | null
  accountId: number | null
  amount: number | null
  ticket: string | null
  socialClub: string | null
  login: string | null
}

export interface IJackpotFullBet {
  id: number
  userId: number | null
  accountId: number | null
  amount: number | null
  ticket: string | null
  gameId: number | null
  dateBet: Date | null
  socialClub: string | null
  login: string | null
  serverId: string | null
  gender: boolean | null
}
