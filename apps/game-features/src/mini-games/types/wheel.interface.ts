import { IMiniGamePlayer } from './mini-games.interface'
import { WheelColorsWithCoef } from '@shared_config/minigames/state'

export interface ICurrentGame {
  id: number
  betsTypes: Record<string, IBestTypes>
}

export interface IBestTypes {
  coefficient: number
  players: IMiniGamePlayer[]
}

export interface IWheelBet {
  userId: number | null
  accountId: number | null
  color: string | null
  amount: number | null
  gameId: number | null
  socialClub: string | null
}

export interface IWheelStateArgs {
  winner: string | null
  winnerRotation: number | null
}

type WheelColors = keyof typeof WheelColorsWithCoef

export interface IWheelPlayerArgs {
  color: WheelColors | null
}
