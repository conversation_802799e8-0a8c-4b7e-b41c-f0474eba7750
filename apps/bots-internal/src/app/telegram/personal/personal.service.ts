import { BackendModelEnum, PrismaService } from '@majestic-backend/database'
import {
  IChangelog,
  IWebhookDateUpdate,
  IYandexTrackerUser,
  YandexTrackerService,
} from '@majestic-backend/yandex-tracker'
import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import moment from 'moment'
import { DashboardHelper, IDashboardUser, WorkingHours } from '../helpers/dashboard'
import { CalendarHelper } from '@majestic-backend/utils'
import { BotType } from '@majestic-backend/types'
import { RedisService } from '@majestic-backend/redis'
import { InjectBot } from 'nestjs-telegraf'
import { Telegraf } from 'telegraf'

@Injectable()
export class PersonalTelegramBotService implements OnModuleInit {
  private readonly logger = new Logger(PersonalTelegramBotService.name)

  constructor(
    protected readonly databaseService: PrismaService,
    private readonly yandexTrackerService: YandexTrackerService,
    @InjectBot(BotType.PERSONAL_BOT) private readonly telegramService: Telegraf,
    private readonly redisService: RedisService,
  ) {}

  onModuleInit() {
    this.telegramService.telegram.setMyCommands([
      { command: 'start', description: 'Запуск бота' },
      { command: 'daily', description: 'Заполнение отчета по дейли' },
    ])
  }

  async isAllowed(username: string) {
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )
    const dashboardUsers: IDashboardUser[] =
      await telegramDashboardUsersModel.getTelegramDashboardUsers()

    const user = dashboardUsers.find(u => u.telegramUsername === `@${username}`)

    if (!user) {
      return false
    }

    return true
  }

  async saveMsgId(weeklyMsgId: number, dailyMsgId: number, username: string, chatId: number) {
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )
    const user = await telegramDashboardUsersModel.getTelegramDashboardUser({
      where: {
        telegramUsername: `@${username}`,
      },
    })

    await telegramDashboardUsersModel.updateTelegramDashboardUser({
      where: {
        id: user.id,
        telegramUsername: `@${username}`,
      },
      data: {
        dailyMsgId,
        weeklyMsgId,
        tgChatId: chatId,
      },
    })
  }

  async handleStartWeeklyDashboard(username: string, start: string) {
    const startWeek = moment(start).toDate()
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )
    const week = CalendarHelper.getWeekDatesFrom(startWeek)
    const weekEnd = moment(startWeek).add(6, 'days').endOf('day').toISOString()

    try {
      const worklogs = await this.yandexTrackerService.getWorklogs({
        start: { from: startWeek, to: moment(weekEnd).toDate() },
      })

      const user = await telegramDashboardUsersModel.getTelegramDashboardUser({
        where: {
          telegramUsername: `@${username}`,
        },
      })

      const message = DashboardHelper.generatePersonalReport(start, weekEnd, user, worklogs, week)

      return message
    } catch (err) {
      this.logger.error(`Error while getting worker report`, err)

      return `Ошибка при получении личного отчета`
    }
  }

  async handleStartDailyDashboard(username: string, currentDate: string) {
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )
    try {
      const user = await telegramDashboardUsersModel.getTelegramDashboardUser({
        where: {
          telegramUsername: `@${username}`,
        },
      })

      const issues = await this.yandexTrackerService.getUserIssues({
        filter: {
          assignee: `${Number(user.yandexTrackerId)}`,
        },
      })
      const message = DashboardHelper.generateDailyReport(issues, currentDate)

      return message
    } catch (err) {
      this.logger.error(`Error while getting worker report`, err)

      return `Ошибка при получении личного отчета`
    }
  }

  async updateDailyDashboard(payload: IWebhookDateUpdate) {
    this.logger.debug(`Got into updateDailyDashboard. Payload ${JSON.stringify(payload, null, 2)}`)
    const { key } = payload

    const changelog: IChangelog[] = await this.yandexTrackerService.getIssueChangelog(key)

    const filteredChangelog = changelog.filter(
      el => el.fields && el.fields.find(elem => elem.field.id === 'assignee'),
    )
    if (!filteredChangelog.length) {
      this.logger.error(`Assignee did not set to issue: ${key}`)
      return {
        ok: true,
      }
    }

    const assigneeFields = filteredChangelog.at(-1).fields.find(el => el.field.id === 'assignee')
    this.logger.verbose(`Last assignee log: ${JSON.stringify(assigneeFields, null, 2)}`)

    const lastLogAssignees = [assigneeFields.from?.display, assigneeFields.to?.display].filter(
      Boolean,
    )

    const currentDate = moment().toISOString()
    const ytUsers: IYandexTrackerUser[] = await this.yandexTrackerService.getUsers()

    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )

    for (const assignee of lastLogAssignees) {
      const assigneedUser = ytUsers.find(user => user.display === assignee)
      this.logger.debug(`Got assigneed user. Data: ${JSON.stringify(assigneedUser, null, 2)}`)

      if (!assigneedUser) {
        this.logger.error(`Assigned user not found`)
        continue
      }

      const user: IDashboardUser = await telegramDashboardUsersModel.getTelegramDashboardUser({
        where: {
          email: assigneedUser.email,
        },
      })

      if (!user) {
        this.logger.error(`Сотрудник не найден. E-mail: ${assignee}`)
        continue
      }

      setTimeout(async () => {
        const dailyMsg = await this.handleStartDailyDashboard(
          user.telegramUsername.slice(1),
          currentDate,
        )

        try {
          await this.telegramService.telegram.editMessageText(
            user.tgChatId,
            user.dailyMsgId,
            undefined,
            dailyMsg,
            {
              parse_mode: 'Markdown',
            },
          )

          this.logger.debug(`Successfully update daily message. Worker: ${user.telegramUsername}`)
        } catch (err) {
          this.logger.error(`Not update daily message. Worker: ${user.telegramUsername}`, err)
        }
      }, 10000)
    }

    return {
      ok: true,
    }
  }

  restoreFormatting(text: string, entities: any[]) {
    if (!entities || entities.length === 0) return text

    let formattedText = text

    entities.sort((a, b) => b.offset - a.offset)

    for (const entity of entities) {
      if (entity.type === 'text_link') {
        const start = entity.offset
        const end = start + entity.length
        const originalText = text.substring(start, end)
        const link = `<a href="${entity.url}">${originalText}</a>`

        formattedText = formattedText.substring(0, start) + link + formattedText.substring(end)
      }
    }

    return formattedText
  }

  async isWorkerNeedReminderFillTime(startDate: Date, endDate: Date, user: IDashboardUser) {
    try {
      const worklogs = await this.yandexTrackerService.getWorklogs({
        start: { from: startDate, to: endDate },
      })

      const workedHours = DashboardHelper.countWorkedHoursByPeriod(
        startDate,
        endDate,
        Number(user.yandexTrackerId),
        worklogs,
      )

      const weekdayName = moment(startDate).format('dddd').toLowerCase()
      const workingHours = JSON.parse(user.args).workingHours as WorkingHours

      if (workedHours < workingHours[weekdayName]) {
        return true
      }

      return false
    } catch (err) {
      this.logger.error(
        `Error count yesterday working hours. Worker: ${user.telegramUsername}`,
        err,
      )

      return false
    }
  }
}
