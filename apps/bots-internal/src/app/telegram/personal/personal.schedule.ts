import { Injectable, Logger } from '@nestjs/common'
import { <PERSON><PERSON> } from '@nestjs/schedule'
import { BackendModelEnum, PrismaService } from '@majestic-backend/database'
import moment from 'moment'
import { PersonalTelegramBotService } from './personal.service'
import { BotType } from '@majestic-backend/types'
import { IDashboardUser } from '../helpers/dashboard'
import { InjectBot } from 'nestjs-telegraf'
import { Telegraf } from 'telegraf'

@Injectable()
export class PersonalSchedule {
  private readonly logger = new Logger(PersonalSchedule.name)

  constructor(
    protected readonly databaseService: PrismaService,
    @InjectBot(BotType.PERSONAL_BOT) private readonly telegramService: Telegraf,
    private readonly personalTelegramBotService: PersonalTelegramBotService,
  ) {}

  @Cron('0 0 7 * * *')
  async dailyUpdatePersonalMsg() {
    this.logger.debug(`dailyUpdatePersonalMsg cron started`)

    const weekStart = moment().startOf('week').add(1, 'days').toISOString()

    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )
    const users: IDashboardUser[] = await telegramDashboardUsersModel.getTelegramDashboardUsers({
      where: {
        isReportNeed: true,
      },
    })

    for (let i = 0; i < users.length; i++) {
      const weekMsg = await this.personalTelegramBotService.handleStartWeeklyDashboard(
        users[i].telegramUsername.slice(1),
        weekStart,
      )

      try {
        await this.telegramService.telegram.editMessageText(
          users[i].tgChatId,
          users[i].weeklyMsgId,
          undefined,
          weekMsg,
          {
            parse_mode: 'Markdown',
          },
        )
        this.logger.debug(
          `Successfully update weekly message. Worker: ${users[i].telegramUsername}`,
        )
      } catch (err) {
        this.logger.error(`Not update weekly message. Worker: ${users[i].telegramUsername}`, err)
      }
    }
  }

  @Cron('0 0 8 * * *') // 11 утра
  async dailyNotifyUntrackedTime() {
    this.logger.debug(`daily notify untracked time cron started`)

    const dayOfWeek = moment().day()
    if (dayOfWeek == 6 || dayOfWeek == 0) return

    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )
    const users: IDashboardUser[] = await telegramDashboardUsersModel.getTelegramDashboardUsers({
      where: {
        isReportNeed: true,
      },
    })

    const lastWorkingDayStart = moment()
      .subtract(dayOfWeek === 1 ? 3 : 1, 'day')
      .startOf('days')
      .toDate()
    const lastWorkingDayEnd = moment(lastWorkingDayStart).endOf('days').toDate()

    for (let i = 0; i < users.length; i++) {
      const needReminder = await this.personalTelegramBotService.isWorkerNeedReminderFillTime(
        lastWorkingDayStart,
        lastWorkingDayEnd,
        users[i],
      )

      if (needReminder) {
        this.logger.debug(`${users[i].telegramUsername} need track time from the previous day`)
        await this.telegramService.telegram.sendMessage(
          users[i].tgChatId,
          'Не забудь заполнить трекер за вчерашний день!',
          {
            parse_mode: 'Markdown',
          },
        )
      }
    }
  }
}
