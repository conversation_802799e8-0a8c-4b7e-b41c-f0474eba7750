import { Update, Ctx, Start, Action, Command, On } from 'nestjs-telegraf'
import { Context } from 'telegraf'
import { PersonalTelegramBotService } from './personal.service'
import moment from 'moment'
import { Logger } from '@nestjs/common'
import { BotCommands } from './constants'
import { RedisService } from '@majestic-backend/redis'
import { REDIS_KEYS } from '../../telegram/constants'

@Update()
export class PersonalTelegramUpdate {
  private readonly logger = new Logger(PersonalTelegramUpdate.name)
  private chatContexts = new Map<string, any>()

  constructor(
    private readonly personalTelegramBotService: PersonalTelegramBotService,
    private readonly redisService: RedisService,
  ) {}

  @Start()
  async onStart(@Ctx() ctx: Context) {
    if (!(await this.personalTelegramBotService.isAllowed(ctx.message.from.username))) {
      await ctx.reply('У вас нет доступа к боту.')
      return
    }
    this.chatContexts.delete(ctx.chat.id.toString())
    const tgUsername = ctx.message.from.username
    const weekStart = moment().startOf('week').add(1, 'days').toISOString()
    const currentDate = moment().toISOString()

    const weekMsg = await this.personalTelegramBotService.handleStartWeeklyDashboard(
      tgUsername,
      weekStart,
    )
    this.logger.debug(`Successfull generate weekly dashboard. Username: ${tgUsername}`)
    const dailyMsg = await this.personalTelegramBotService.handleStartDailyDashboard(
      tgUsername,
      currentDate,
    )
    this.logger.debug(`Successfull generate daily dashboard. Username: ${tgUsername}`)

    const msgWeekly = await ctx.reply(weekMsg, {
      reply_markup: {
        inline_keyboard: [
          [
            { text: 'Назад', callback_data: `previousWeek*${weekStart}*${tgUsername}` },
            { text: 'Вперед', callback_data: `nextWeek*${weekStart}*${tgUsername}` },
          ],
        ],
      },
      parse_mode: 'Markdown',
    })
    const msgDaily = await ctx.reply(dailyMsg, { parse_mode: 'Markdown' })

    await this.personalTelegramBotService.saveMsgId(
      msgWeekly.message_id,
      msgDaily.message_id,
      tgUsername,
      ctx.chat.id,
    )
  }

  @Action(/previousWeek\*.+/)
  async handlePreviousWeek(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    if ('data' in ctx.callbackQuery) {
      const [, currentWeekStart, tgUsername] = ctx.callbackQuery.data.split('*')
      const previousWeekStart = moment(currentWeekStart).subtract(7, 'days').toISOString()

      const weekMsg = await this.personalTelegramBotService.handleStartWeeklyDashboard(
        tgUsername,
        previousWeekStart,
      )

      await ctx.editMessageText(weekMsg, {
        reply_markup: {
          inline_keyboard: [
            [
              { text: 'Назад', callback_data: `previousWeek*${previousWeekStart}*${tgUsername}` },
              { text: 'Вперед', callback_data: `nextWeek*${previousWeekStart}*${tgUsername}` },
            ],
          ],
        },
        parse_mode: 'Markdown',
      })
    } else {
      this.logger.error('CallbackQuery does not contain data')
      await ctx.reply('Произошла ошибка. Попробуйте снова.')
    }
  }

  @Action(/nextWeek\*.+/)
  async handleNextWeek(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    if ('data' in ctx.callbackQuery) {
      const [, currentWeekStart, tgUsername] = ctx.callbackQuery.data.split('*')
      const nextWeekStart = moment(currentWeekStart).add(7, 'days').toISOString()

      const weekMsg = await this.personalTelegramBotService.handleStartWeeklyDashboard(
        tgUsername,
        nextWeekStart,
      )

      await ctx.editMessageText(weekMsg, {
        reply_markup: {
          inline_keyboard: [
            [
              { text: 'Назад', callback_data: `previousWeek*${nextWeekStart}*${tgUsername}` },
              { text: 'Вперед', callback_data: `nextWeek*${nextWeekStart}*${tgUsername}` },
            ],
          ],
        },
        parse_mode: 'Markdown',
      })
    } else {
      this.logger.error('CallbackQuery does not contain data')
      await ctx.reply('Произошла ошибка. Попробуйте снова.')
    }
  }

  @Command('daily')
  async onDaily(@Ctx() ctx: Context) {
    if (!(await this.personalTelegramBotService.isAllowed(ctx.message.from.username))) {
      await ctx.reply('У вас нет доступа к боту.')
      return
    }

    const chatId = ctx.chat.id.toString()
    this.chatContexts.set(chatId, { step: 'yestardayReport' })

    const previousReport = await this.redisService.get(
      `${REDIS_KEYS.TELEGRAM_DAILY_REPORTER_MSG}:${ctx.message.from.username}`,
    )
    if (previousReport) {
      await ctx.reply(`Ваш сохраненный отчет:\n\n${previousReport}`, { parse_mode: 'HTML' })
    }

    await ctx.reply('Что делал вчера?')
  }

  @On('text')
  async handleText(@Ctx() ctx: Context) {
    if (!(await this.personalTelegramBotService.isAllowed(ctx.message.from.username))) {
      await ctx.reply('У вас нет доступа к боту.')
      return
    }

    const chatId = ctx.chat.id.toString()
    const context = this.chatContexts.get(chatId)

    const message = ctx.message as { text?: string; entities?: any[] }
    const formattedDailyReport = this.personalTelegramBotService.restoreFormatting(
      message.text,
      message.entities,
    )
    const dailyReporter = ctx.message.from.username

    switch (context?.step) {
      case BotCommands.YESTERDAY_REPORT: {
        this.logger.verbose(`Went from yestardayReport`)
        await this.redisService.set(
          `${REDIS_KEYS.TELEGRAM_DAILY_REPORTER_MSG}:${dailyReporter}`,
          `<b>Вчера:</b>\n${formattedDailyReport}`,
        )

        this.chatContexts.set(chatId, { step: 'todayReport' })
        await ctx.reply('Что будешь делать сегодня?')
        break
      }
      case BotCommands.TODAY_REPORT: {
        this.logger.verbose(`Went from todayReport`)
        await this.redisService.append(
          `${REDIS_KEYS.TELEGRAM_DAILY_REPORTER_MSG}:${dailyReporter}`,
          `\n<b>Сегодня:</b>\n${formattedDailyReport}`,
        )

        this.chatContexts.set(chatId, { step: 'blockerReport' })
        await ctx.reply('С какими трудностями столкнулся?')
        break
      }
      case BotCommands.BLOCKER_REPORT: {
        this.logger.verbose(`Went from blockerReport`)
        await this.redisService.append(
          `${REDIS_KEYS.TELEGRAM_DAILY_REPORTER_MSG}:${dailyReporter}`,
          `\n<b>Блокировки:</b>\n${formattedDailyReport}`,
        )
        await this.redisService.append(REDIS_KEYS.TELEGRAM_DAILY_REPORTERS, `@${dailyReporter} `)

        await ctx.reply('Отчет успешно сохранен')
        this.chatContexts.delete(chatId)
        await this.onStart(ctx)
        break
      }
      default:
        await ctx.reply('Неизвестная команда. Начните сценарий заново.')
        break
    }
  }
}
