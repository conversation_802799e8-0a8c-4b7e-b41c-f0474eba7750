import { Issues, Worklog } from '@majestic-backend/yandex-tracker'
import moment from 'moment'
import {
  IEmployeeWorkTimeMonthly,
  IDashboardUser,
  IEmployeeWorkTime,
  WorkingHours,
} from './dashboard.model'
import { weekDaysNames, defaultWorkingHours } from '../../constants'
import { escapeMarkdown } from '@majestic-backend/utils'

type WorkTimeSummary = {
  [date: string]: { [issueKey: string]: { issueName: string; totalMinutes: number } }
}

enum StatusType {
  DONE = 'done',
  CANCELLED = 'cancelled',
}

export class DashboardHelper {
  public static async generateMonthlyDashboard(data: IEmployeeWorkTimeMonthly[]) {
    const monthYear = moment(data[0].date).locale('ru').format('MMMM YYYY')

    const employeeMessages = data.map((employee, index) => {
      const { username, totalWorkTime } = employee
      const totalHours = totalWorkTime.hours + Math.floor(totalWorkTime.minutes / 60)
      const totalMinutes = totalWorkTime.minutes % 60
      const total = totalWorkTime.total

      const totalWorkedHours = totalHours + totalMinutes / 60
      const percentage = ((totalWorkedHours / total) * 100).toFixed(2)
      const percentageIcon = this.percentageFilter(percentage)

      return `${++index}. ${username} - ${percentageIcon} ${totalHours} ч ${totalMinutes} мин / ${total} ч`
    })

    return (
      `${monthYear.charAt(0).toUpperCase() + monthYear.slice(1)}\n` + employeeMessages.join('\n')
    )
  }

  public static calculateWorkTimeByPeriod(
    dashboardUsers: IDashboardUser[],
    worklog: Worklog[],
    period: Date[],
  ) {
    const workTimeByEmployee: { [key: string]: { [date: string]: number } } = dashboardUsers.reduce(
      (acc, employee) => {
        acc[employee.yandexTrackerId.toString()] = period.reduce((dateAcc, date) => {
          dateAcc[moment(date).format('YYYY-MM-DD')] = 0
          return dateAcc
        }, {})
        return acc
      },
      {},
    )

    worklog.forEach(worklog => {
      const employeeId = worklog.createdBy.id
      const duration = moment.duration(worklog.duration)
      const adjustedMinutes = duration.days() * 8 * 60 + duration.hours() * 60 + duration.minutes()

      const worklogDate = moment(worklog.start).format('YYYY-MM-DD')
      const matchingDate = period.find(date => moment(date).format('YYYY-MM-DD') === worklogDate)

      if (matchingDate && employeeId in workTimeByEmployee) {
        workTimeByEmployee[employeeId][moment(matchingDate).format('YYYY-MM-DD')] += adjustedMinutes
      }
    })

    return Object.entries(workTimeByEmployee).map(([employeeId, dailyMinutes]) => {
      const dailyWorkTime = Object.entries(dailyMinutes).map(([date, minutes]) => {
        const hours = Math.floor(minutes / 60)
        const remainingMinutes = minutes % 60
        return {
          date,
          hours,
          minutes: remainingMinutes,
          needWork: 0,
        }
      })

      return {
        employeeId,
        totalWorkTime: dailyWorkTime,
      }
    })
  }

  public static generateWeeklyDashboard(data: IEmployeeWorkTime[]): string[] {
    let message = ''

    for (let i = 0; i < 7; i++) {
      const date = data[0].totalWorkTime[i].date
      const [year, month, day] = date.split('-')

      message += `${weekDaysNames[i]}, ${day} ${new Date(
        Number(year),
        Number(month) - 1,
        Number(day),
      )
        .toLocaleString('ru', { month: 'short' })
        .replace('.', '')}\n`

      data.forEach((employee, index) => {
        const workTime = employee.totalWorkTime[i]
        const hoursWorked = workTime.hours
        const minutesWorked = workTime.minutes

        const totalWorkedHours = hoursWorked + minutesWorked / 60
        const percentage =
          employee.totalWorkTime[i].needWork > 0
            ? ((totalWorkedHours / employee.totalWorkTime[i].needWork) * 100).toFixed(2)
            : 'N/A'
        const percentageIcon = this.percentageFilter(percentage)

        message += `${++index}. ${
          employee.username
        } - ${percentageIcon} ${hoursWorked}ч ${minutesWorked}м / ${
          employee.totalWorkTime[i].needWork
        } ч\n`
      })

      if (i < 6) message += '*****'
    }

    return message.trim().split('*****')
  }

  public static calculateAllWorkTime(
    dashboardUsers: IDashboardUser[],
    worklog: Worklog[],
    startMonth: Date,
  ) {
    const workTimeByEmployee: { [key: string]: { totalTime: number; startMonth: string } } =
      dashboardUsers.reduce((acc, employee) => {
        acc[employee.yandexTrackerId.toString()] = { totalTime: 0, startMonth }
        return acc
      }, {})

    worklog.forEach(worklog => {
      const employeeId = worklog.createdBy.id
      const duration = moment.duration(worklog.duration)
      const adjustedMinutes = duration.days() * 8 * 60 + duration.hours() * 60 + duration.minutes()

      if (employeeId in workTimeByEmployee) {
        workTimeByEmployee[employeeId]['totalTime'] += adjustedMinutes
      }
    })

    return Object.entries(workTimeByEmployee).map(([employeeId, data]) => {
      const hours = Math.floor(data.totalTime / 60)
      const remainingMinutes = data.totalTime % 60

      return {
        employeeId,
        date: startMonth,
        totalWorkTime: {
          hours,
          minutes: remainingMinutes,
        },
      }
    })
  }

  public static expandResultDataByUsername(
    resultTime: IEmployeeWorkTime[] | IEmployeeWorkTimeMonthly[],
    dashboardUsers: IDashboardUser[],
  ) {
    return resultTime.map(employee => {
      const user = dashboardUsers.find(u => u.yandexTrackerId.toString() === employee.employeeId)

      if (user) {
        employee.username = user.telegramUsername
      }

      return employee
    })
  }

  public static expandResultDataByNeedHours(
    data: IDashboardUser[],
    workTimeData: IEmployeeWorkTime[],
  ) {
    const parsedData = data.map(item => ({
      ...item,
      args: JSON.parse(item.args),
    }))

    return workTimeData.map(employee => {
      const matchedEmployee = parsedData.find(
        item => item.yandexTrackerId === BigInt(employee.employeeId),
      )

      if (matchedEmployee) {
        const workingHoursArray = matchedEmployee.args
          ? Object.values(matchedEmployee.args.workingHours)
          : defaultWorkingHours

        employee.totalWorkTime = employee.totalWorkTime.map((day, index) => ({
          ...day,
          needWork: +workingHoursArray[index] || 0,
        }))
      }

      return employee
    })
  }

  static formatDuration(duration: moment.Duration): string {
    const hours = Math.floor(duration.asHours())
    const minutes = duration.minutes()
    return `${hours} ч ${minutes > 0 ? `${minutes} м` : ''}`.trim()
  }

  public static generatePersonalReport(
    startDate: string,
    endDate: string,
    user: IDashboardUser,
    worklogs: Worklog[],
    period: Date[],
  ) {
    const start = moment(startDate).startOf('day')
    const end = moment(endDate).endOf('day')
    const userArgs = JSON.parse(user.args)

    // Инициализируем workTimeSummary со всеми датами из period
    const workTimeSummary: WorkTimeSummary = {}

    period.forEach(date => {
      const formattedDate = moment(date).format('YYYY-MM-DD')
      workTimeSummary[formattedDate] = {}
    })

    const filteredLogs = worklogs.filter(
      log =>
        log.updatedBy.id === `${Number(user.yandexTrackerId)}` &&
        moment(log.start).isBetween(start, end, undefined, '[]'),
    )

    filteredLogs.forEach(log => {
      const date = moment(log.start).format('YYYY-MM-DD')
      const duration = moment.duration(log.duration)
      const adjustedMinutes = duration.days() * 8 * 60 + duration.hours() * 60 + duration.minutes()

      if (!workTimeSummary[date]) {
        workTimeSummary[date] = {}
      }

      const issueKey = log.issue.key
      const issueName = log.issue.display

      if (!workTimeSummary[date][issueKey]) {
        workTimeSummary[date][issueKey] = { issueName, totalMinutes: 0 }
      }

      workTimeSummary[date][issueKey].totalMinutes += adjustedMinutes
    })

    const startPeriod = start.locale('ru').format('D MMM').replace('.', '')
    const endPeriod = end.locale('ru').format('D MMM').replace('.', '')
    let result = `${startPeriod} - ${endPeriod}\n\n`

    for (const date of Object.keys(workTimeSummary).sort()) {
      const dailySummary = workTimeSummary[date]
      const dayMoment = moment(date)
      const dayName = dayMoment.locale('ru').format('dd, D MMM')

      const totalDailyMinutes = Object.values(dailySummary).reduce(
        (sum, issue) => sum + issue.totalMinutes,
        0,
      )

      const totalHours = Math.floor(totalDailyMinutes / 60)
      const totalMinutes = totalDailyMinutes % 60

      const dayOfWeek = dayMoment.locale('en').format('dddd').toLowerCase()

      const dailyWorkingHours = userArgs.workingHours[dayOfWeek] || 0

      const totalWorkedHours = totalHours + totalMinutes / 60
      const percentage =
        dailyWorkingHours > 0 ? ((totalWorkedHours / dailyWorkingHours) * 100).toFixed(2) : 'N/A'
      const percentageIcon = this.percentageFilter(percentage)

      result += `${dayName} - ${percentageIcon} ${totalHours} ч ${
        totalMinutes > 0 ? totalMinutes + ' м' : ''
      } / ${dailyWorkingHours} ч\n`

      const dailySummaryKeys = Object.keys(dailySummary)
      dailySummaryKeys.forEach((issueKey, index) => {
        const { issueName, totalMinutes } = dailySummary[issueKey]
        const issueHours = Math.floor(totalMinutes / 60)
        const issueRemMinutes = totalMinutes % 60

        result += `${
          index + 1
        }. [${issueKey}](https://tracker.yandex.ru/${issueKey}): ${escapeMarkdown(
          issueName,
        )}  -  ${issueHours} ч ${issueRemMinutes > 0 ? issueRemMinutes + ' м' : ''}\n`
      })

      result += '\n'
    }

    return result.trim()
  }

  public static generateDailyReport(tasks: Issues[], currentDate: string) {
    const sortedTasks = this.sortTasksByDeadline(tasks)

    const formatTaskList = (tasks: Issues[]): string => {
      if (!tasks.length) {
        return 'Нет задач'
      }

      const maxTasksToShow = 10
      const visibleTasks = tasks.slice(0, maxTasksToShow)
      const remainingTasksCount = tasks.length - maxTasksToShow

      const taskList = visibleTasks
        .map(
          task =>
            `- [${task.key}](https://tracker.yandex.ru/${task.key}): ${escapeMarkdown(
              task.summary,
            )}`,
        )
        .join('\n')

      const remainingTasksMessage =
        remainingTasksCount > 0 ? `\n(+${remainingTasksCount} других задач)` : ''

      return taskList + remainingTasksMessage
    }

    return `Расписание на ${moment(currentDate)
      .locale('ru')
      .format('D MMMM')}\n\nПросрочено\n${formatTaskList(
      sortedTasks.overdue,
    )}\n\nСегодня\n${formatTaskList(sortedTasks.today)}\n\nДалее\n${formatTaskList(
      sortedTasks.upcoming,
    )}\n\nНе запланировано\n${formatTaskList(sortedTasks.notPlanned)}`
  }

  public static sortTasksByDeadline(tasks: Issues[]) {
    const currentDate = moment().toDate()

    const overdue = []
    const today = []
    const upcoming = []
    const notPlanned = []

    tasks.forEach(task => {
      if (task.statusType.key === StatusType.DONE || task.statusType.key === StatusType.CANCELLED) {
        return
      }
      const deadline = task.deadline ? moment(task.deadline).endOf('day').toDate() : null
      const start = task.start ? moment(task.start).startOf('day').toDate() : null

      if (!start) {
        notPlanned.push(task)
      } else if (start <= currentDate && currentDate <= deadline) {
        today.push(task)
      } else if (deadline && currentDate > deadline) {
        overdue.push(task)
      } else if (currentDate < start) {
        upcoming.push(task)
      }
    })

    return {
      overdue,
      today,
      upcoming,
      notPlanned,
    }
  }

  public static percentageFilter(percent: string) {
    if (percent === 'N/A') return '⚪⚪⚪⚪⚪'

    const percentage = Number(percent)

    if (percentage > 100) return '🔴🔴🔴🔴🔴'
    if (percentage === 100) return '🟢🟢🟢🟢🟢'

    const thresholds = [0, 20, 40, 60, 80, 100]
    const symbols = [
      '⚪⚪⚪⚪⚪',
      '🟡⚪⚪⚪⚪',
      '🟡🟡⚪⚪⚪',
      '🟡🟡🟡⚪⚪',
      '🟡🟡🟡🟡⚪',
      '🟡🟡🟡🟡🟡',
    ]

    for (let i = thresholds.length - 1; i >= 0; i--) {
      if (percentage > thresholds[i]) {
        return symbols[i + 1]
      }
    }

    return symbols[0]
  }

  static calculateMonthlyWorkingHours(daysString: string, workingHours: WorkingHours) {
    const currentYear = moment().year()
    const currentMonth = moment().month()

    const daysInMonth = moment().daysInMonth()

    let totalWorkingHours = 0

    for (let day = 1; day <= daysInMonth; day++) {
      const dayOfWeek = moment({ year: currentYear, month: currentMonth, day }).day()
      const weekdayName = moment().day(dayOfWeek).format('dddd').toLowerCase()

      const isWorkingDay = daysString[day - 1] === '0'

      if (isWorkingDay) {
        totalWorkingHours += workingHours[weekdayName] || 0
      }
    }

    return totalWorkingHours
  }

  public static updateEmployeeWorkTime(
    employees: any[],
    users: IDashboardUser[],
    daysString: string,
  ) {
    return employees.map(employee => {
      const user = users.find(u => u.yandexTrackerId.toString() === employee.employeeId)

      if (user) {
        const workingHours = JSON.parse(user.args).workingHours
        employee.totalWorkTime.total = this.calculateMonthlyWorkingHours(daysString, workingHours)
      }

      return employee
    })
  }

  public static countWorkedHoursByPeriod(
    start: Date,
    end: Date,
    yandexTrackerId: number,
    worklogs: Worklog[],
  ) {
    const filteredLogs: Worklog[] = worklogs.filter(
      log =>
        log.updatedBy.id === `${Number(yandexTrackerId)}` &&
        moment(log.start).isBetween(start, end, undefined, '[]'),
    )

    const totalMinutes = filteredLogs.reduce((acc, log) => {
      const duration = moment.duration(log.duration)
      const adjustedMinutes = duration.days() * 8 * 60 + duration.hours() * 60 + duration.minutes()

      return acc + adjustedMinutes
    }, 0)

    return Math.round((totalMinutes / 60) * 100) / 100
  }
}
