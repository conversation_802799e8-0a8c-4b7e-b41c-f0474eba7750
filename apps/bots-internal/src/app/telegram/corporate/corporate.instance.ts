import { Module } from '@nestjs/common'
import { TelegrafModule } from 'nestjs-telegraf'
import { TelegramConfig } from '@majestic-backend/config'
import { CorporateTelegramBotModule } from './corporate.module'
import { BotType } from '@majestic-backend/types'

@Module({
  imports: [
    {
      imports: [
        TelegrafModule.forRootAsync({
          botName: BotType.CORPORATE_BOT,
          useFactory: (telegramConfig: TelegramConfig) => ({
            token: telegramConfig.timeTracking.management.TOKEN,
            include: [CorporateTelegramBotModule],
          }),
          inject: [TelegramConfig],
        }),
      ],
      module: CorporateTelegramBotModule,
    },
  ],
})
export class CorporateBotInstance {}
