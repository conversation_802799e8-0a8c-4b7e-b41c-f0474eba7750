import { BackendModelEnum, PrismaService } from '@majestic-backend/database'
import { CalendarHelper } from '@majestic-backend/utils'
import { YandexTrackerService } from '@majestic-backend/yandex-tracker'
import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import moment from 'moment'
import {
  DashboardHelper,
  IDashboardUser,
  IEmployeeWorkTime,
  IEmployeeWorkTimeMonthly,
} from '../helpers/dashboard'
import { BotType } from '@majestic-backend/types'
import { InjectBot } from 'nestjs-telegraf'
import { Telegraf } from 'telegraf'

const defaultWorkingHours = {
  workingHours: {
    monday: 6,
    tuesday: 6,
    wednesday: 6,
    thursday: 6,
    friday: 6,
    saturday: 0,
    sunday: 0,
  },
}

@Injectable()
export class CorporateTelegramBotService implements OnModuleInit {
  private readonly logger = new Logger(CorporateTelegramBotService.name)

  constructor(
    protected readonly databaseService: PrismaService,
    private readonly yandexTrackerService: YandexTrackerService,
    @InjectBot(BotType.CORPORATE_BOT) private readonly telegramService: Telegraf,
  ) {}

  onModuleInit() {
    this.telegramService.telegram.setMyCommands([{ command: 'start', description: 'Запуск бота' }])
  }

  async isAllowed(botType: string, username: string) {
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )
    const dashboardUsers: IDashboardUser[] =
      await telegramDashboardUsersModel.getTelegramDashboardUsers()

    const user = dashboardUsers.find(u => u.telegramUsername === `@${username}`)

    if (!user || (botType === 'admin' && !user.isAdmin)) {
      return false
    }

    return true
  }

  async handleWeeklyDashboard(start: string): Promise<string[]> {
    const startWeek = moment(start).toDate()
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )
    const week = CalendarHelper.getWeekDatesFrom(startWeek)
    const endWeek = moment(startWeek).add(6, 'days').endOf('day').toDate()

    const worklogs = await this.yandexTrackerService.getWorklogs({
      createdAt: { from: startWeek, to: endWeek },
    })
    const dashboardUsers: IDashboardUser[] =
      await telegramDashboardUsersModel.getTelegramDashboardUsers()

    const resultTime: IEmployeeWorkTime[] = DashboardHelper.calculateWorkTimeByPeriod(
      dashboardUsers,
      worklogs,
      week,
    )
    const usersWithUsername = DashboardHelper.expandResultDataByUsername(resultTime, dashboardUsers)
    const updatedWorkTimeByEmployee = DashboardHelper.expandResultDataByNeedHours(
      dashboardUsers,
      usersWithUsername,
    )

    const message = DashboardHelper.generateWeeklyDashboard(updatedWorkTimeByEmployee)

    return message
  }

  async handleMounthlyDashboard(startMonth: Date): Promise<string> {
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )
    const endMonth = moment(startMonth).endOf('month').toDate()

    const worklogs = await this.yandexTrackerService.getWorklogs({
      createdAt: { from: startMonth, to: endMonth },
    })
    const dashboardUsers: IDashboardUser[] =
      await telegramDashboardUsersModel.getTelegramDashboardUsers()
    const resultTime: IEmployeeWorkTimeMonthly[] = DashboardHelper.calculateAllWorkTime(
      dashboardUsers,
      worklogs,
      startMonth,
    )

    const updatedWorkTimeByEmployee = DashboardHelper.expandResultDataByUsername(
      resultTime,
      dashboardUsers,
    )
    const numberWorkingDays = await CalendarHelper.getMonthWorkingDays(
      moment(updatedWorkTimeByEmployee[0].date).add(1, 'day').toISOString(),
    )
    const updatedEmployees = DashboardHelper.updateEmployeeWorkTime(
      updatedWorkTimeByEmployee,
      dashboardUsers,
      numberWorkingDays,
    )

    const message = await DashboardHelper.generateMonthlyDashboard(updatedEmployees)

    return message
  }

  async handleWorkerReport(input: string) {
    const [username, start, end] = input.split(' ')

    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )
    const startPeriod = moment(start).toDate()
    const endPeriod = moment(end).toDate()
    const period = CalendarHelper.getDatesFrom(startPeriod, endPeriod)

    try {
      const worklogs = await this.yandexTrackerService.getWorklogs({
        createdAt: {
          from: moment(start).startOf('day').toDate(),
          to: moment(end).endOf('day').toDate(),
        },
      })

      const user = await telegramDashboardUsersModel.getTelegramDashboardUser({
        where: {
          telegramUsername: username,
        },
      })

      if (!user) {
        return `Сотрудник не найден`
      }

      const message = DashboardHelper.generatePersonalReport(start, end, user, worklogs, period)

      return message
    } catch (err) {
      this.logger.error(`Error while getting worker report`, err)

      return `Ошибка при получении отчета сотрудника`
    }
  }

  async handleAddWorker(input: string): Promise<string> {
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )

    const [telegramUsername, email] = input.split(' ')
    const ytLogin = email.split('@')

    try {
      const ytUser = await this.yandexTrackerService.getUserByLogin(ytLogin[0])

      await telegramDashboardUsersModel.createTelegramDashboardUser({
        yandexTrackerId: ytUser.trackerUid,
        telegramUsername,
        email,
        args: JSON.stringify(defaultWorkingHours),
      })

      return `Сотрудник успешно добавлен`
    } catch (err) {
      this.logger.error(`Error while adding worker`, err)

      return `Ошибка при добавлении сотрудника`
    }
  }

  async handleUpdateWorkerInit(input: string) {
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )

    try {
      const user = await telegramDashboardUsersModel.getTelegramDashboardUser({
        where: {
          telegramUsername: input,
        },
      })

      if (!user) {
        return `Сотрудник не найден`
      }

      return `Данные сотрудника:\nYandex Tracker Id: ${user.yandexTrackerId}\nTelegram Username: ${user.telegramUsername}\nE-mail: ${user.email}\n\nВведите обновленные данные:\nYandexTrackerId  @TelegramUsername  E-mail`
    } catch (err) {
      this.logger.error(`Error while adding worker`, err)

      return `Ошибка при обновлении сотрудника`
    }
  }

  async handleUpdateWorker(input: string) {
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )

    const [yandexTrackerId, telegramUsername, email] = input.split(' ')

    try {
      await telegramDashboardUsersModel.updateTelegramDashboardUser({
        where: {
          email,
        },
        data: {
          yandexTrackerId: Number(yandexTrackerId),
          telegramUsername,
          email,
        },
      })

      return `Сотрудник ${telegramUsername} обновлен`
    } catch (err) {
      this.logger.error(`Error while adding worker`, err)

      return `Ошибка при обновлении сотрудника`
    }
  }

  async handleDeleteWorker(input: string) {
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )

    try {
      const user = await telegramDashboardUsersModel.getTelegramDashboardUser({
        where: {
          email: input,
        },
      })

      if (!user) {
        return `Сотрудник не найден`
      }

      await telegramDashboardUsersModel.deleteTelegramDashboardUser({
        where: {
          email: input,
        },
      })

      return `Сотрудник ${input} удален`
    } catch (err) {
      this.logger.error(`Error while adding worker`, err)

      return `Ошибка при удалении сотрудника`
    }
  }

  async handleSetWorkingHours(input: string) {
    const telegramDashboardUsersModel = this.databaseService.getBackendModel(
      BackendModelEnum.TelegramDashboardUsersModel,
    )

    const [telegramUsername, mon = 6, tue = 6, wed = 6, thu = 6, fri = 6, sat = 0, sun = 0] =
      input.split(' ')

    try {
      const user = await telegramDashboardUsersModel.getTelegramDashboardUser({
        where: {
          telegramUsername,
        },
      })

      if (!user) {
        return `Сотрудник не найден`
      }

      await telegramDashboardUsersModel.updateTelegramDashboardUser({
        where: {
          id: user.id,
          telegramUsername,
        },
        data: {
          args: JSON.stringify({
            workingHours: {
              monday: Number(mon),
              tuesday: Number(tue),
              wednesday: Number(wed),
              thursday: Number(thu),
              friday: Number(fri),
              saturday: Number(sat),
              sunday: Number(sun),
            },
          }),
        },
      })

      return `Сотрудник ${telegramUsername} обновлен`
    } catch (err) {
      this.logger.error(`Error while adding worker`, err)

      return `Ошибка при обновлении сотрудника`
    }
  }
}
