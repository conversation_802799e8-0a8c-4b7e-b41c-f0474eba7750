import { Update, Ctx, Start, Action, On } from 'nestjs-telegraf'
import { Context } from 'telegraf'
import { CorporateTelegramBotService } from './corporate.service'
import moment from 'moment'
import { Logger } from '@nestjs/common'
import { BotCommands } from './constants'

@Update()
export class CorporateTelegramUpdate {
  private readonly logger = new Logger(CorporateTelegramUpdate.name)
  private chatContexts = new Map<string, any>()

  constructor(private readonly corporateTelegramBotService: CorporateTelegramBotService) {}

  @Start()
  async onStart(@Ctx() ctx: Context) {
    if (!(await this.corporateTelegramBotService.isAllowed('admin', ctx.message.from.username))) {
      await ctx.reply('У вас нет доступа к боту.')
      return
    }

    const chatId = ctx.chat.id.toString()
    this.chatContexts.delete(chatId)

    await ctx.reply('Привет! Вот кнопки для взаимодействия:', {
      reply_markup: {
        inline_keyboard: [
          [{ text: 'Недельный дешборд', callback_data: 'weeklyDashboard' }],
          [{ text: 'Месячный дешборд', callback_data: 'monthlyDashboard' }],
          [{ text: 'Отчёт по сотруднику', callback_data: 'workerReport' }],
          [{ text: 'Сотрудники', callback_data: 'workers' }],
        ],
      },
    })
  }

  @Action('menu')
  async handleMenu(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    const chatId = ctx.chat.id.toString()
    this.chatContexts.delete(chatId)

    await ctx.editMessageText('Выберите действие:', {
      reply_markup: {
        inline_keyboard: [
          [{ text: 'Недельный дешборд', callback_data: 'weeklyDashboard' }],
          [{ text: 'Месячный дешборд', callback_data: 'monthlyDashboard' }],
          [{ text: 'Отчёт по сотруднику', callback_data: 'workerReport' }],
          [{ text: 'Сотрудники', callback_data: 'workers' }],
        ],
      },
    })
  }

  @Action('weeklyDashboard')
  async handleWeeklyDashboard(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()
    const currentWeekStart = moment().startOf('week').add(1, 'days').toISOString()
    const response = await this.corporateTelegramBotService.handleWeeklyDashboard(currentWeekStart)

    for (let i = 0; i < response.length; i++) {
      await ctx.reply(response[i])
    }

    await ctx.reply('Недельный дешборд', {
      reply_markup: {
        inline_keyboard: [
          [
            { text: 'Назад', callback_data: `previousWeek*${currentWeekStart}` },
            { text: 'Вперед', callback_data: `nextWeek*${currentWeekStart}` },
          ],
          [{ text: 'Меню', callback_data: 'menu' }],
        ],
      },
    })
  }

  @Action(/previousWeek\*.+/)
  async handlePreviousWeek(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    if ('data' in ctx.callbackQuery) {
      const [, currentWeekStart] = ctx.callbackQuery.data.split('*')
      const previousWeekStart = moment(currentWeekStart).subtract(7, 'days').toISOString()

      const response = await this.corporateTelegramBotService.handleWeeklyDashboard(
        previousWeekStart,
      )

      for (let i = 0; i < response.length; i++) {
        await ctx.reply(response[i])
      }

      await ctx.reply('Недельный дешборд', {
        reply_markup: {
          inline_keyboard: [
            [
              { text: 'Назад', callback_data: `previousWeek*${previousWeekStart}` },
              { text: 'Вперед', callback_data: `nextWeek*${previousWeekStart}` },
            ],
            [{ text: 'Меню', callback_data: 'menu' }],
          ],
        },
      })
    } else {
      this.logger.error('CallbackQuery does not contain data')
      await ctx.reply('Произошла ошибка. Попробуйте снова.')
    }
  }

  @Action(/nextWeek\*.+/)
  async handleNextWeek(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    if ('data' in ctx.callbackQuery) {
      const [, currentWeekStart] = ctx.callbackQuery.data.split('*')
      const nextWeekStart = moment(currentWeekStart).add(7, 'days').toISOString()

      const response = await this.corporateTelegramBotService.handleWeeklyDashboard(nextWeekStart)

      for (let i = 0; i < response.length; i++) {
        await ctx.reply(response[i])
      }

      await ctx.reply('Недельный дешборд', {
        reply_markup: {
          inline_keyboard: [
            [
              { text: 'Назад', callback_data: `previousWeek*${nextWeekStart}` },
              { text: 'Вперед', callback_data: `nextWeek*${nextWeekStart}` },
            ],
            [{ text: 'Меню', callback_data: 'menu' }],
          ],
        },
      })
    } else {
      this.logger.error('CallbackQuery does not contain data')
      await ctx.reply('Произошла ошибка. Попробуйте снова.')
    }
  }

  @Action('monthlyDashboard')
  async handleMounthlyDashboard(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    const currentMonthStart = moment().startOf('month').toISOString()
    const response = await this.corporateTelegramBotService.handleMounthlyDashboard(
      new Date(currentMonthStart),
    )

    await ctx.editMessageText(response, {
      reply_markup: {
        inline_keyboard: [
          [
            { text: 'Назад', callback_data: `previousMonth*${currentMonthStart}` },
            { text: 'Вперед', callback_data: `nextMonth*${currentMonthStart}` },
          ],
          [{ text: 'Меню', callback_data: 'menu' }],
        ],
      },
    })
  }

  @Action(/previousMonth\*.+/)
  async handlePreviousMonth(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    if ('data' in ctx.callbackQuery) {
      const [, currentMonthStart] = ctx.callbackQuery.data.split('*')
      const previousMonthStart = moment(currentMonthStart).subtract(1, 'month').toISOString()

      const response = await this.corporateTelegramBotService.handleMounthlyDashboard(
        new Date(previousMonthStart),
      )

      await ctx.editMessageText(response, {
        reply_markup: {
          inline_keyboard: [
            [
              { text: 'Назад', callback_data: `previousMonth*${previousMonthStart}` },
              { text: 'Вперед', callback_data: `nextMonth*${previousMonthStart}` },
            ],
            [{ text: 'Меню', callback_data: 'menu' }],
          ],
        },
      })
    } else {
      this.logger.error('CallbackQuery does not contain data')
      await ctx.reply('Произошла ошибка. Попробуйте снова.')
    }
  }

  @Action(/nextMonth\*.+/)
  async handleNextMonth(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    if ('data' in ctx.callbackQuery) {
      const [, currentMonthStart] = ctx.callbackQuery.data.split('*')
      const nextMonthStart = moment(currentMonthStart).add(1, 'month').toISOString()

      const response = await this.corporateTelegramBotService.handleMounthlyDashboard(
        new Date(nextMonthStart),
      )

      await ctx.editMessageText(response, {
        reply_markup: {
          inline_keyboard: [
            [
              { text: 'Назад', callback_data: `previousMonth*${nextMonthStart}` },
              { text: 'Вперед', callback_data: `nextMonth*${nextMonthStart}` },
            ],
            [{ text: 'Меню', callback_data: 'menu' }],
          ],
        },
      })
    } else {
      this.logger.error('CallbackQuery does not contain data')
      await ctx.reply('Произошла ошибка. Попробуйте снова.')
    }
  }

  @Action('workers')
  async handleWorkers(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    const chatId = ctx.chat.id.toString()
    this.chatContexts.delete(chatId)

    await ctx.editMessageText(`Выберите действие:`, {
      reply_markup: {
        inline_keyboard: [
          [{ text: 'Добавить сотрудника', callback_data: 'addWorker' }],
          [{ text: 'Обновить данные сотрудника', callback_data: 'updateWorker' }],
          [{ text: 'Удалить сотрудника', callback_data: 'deleteWorker' }],
          [{ text: 'Настройка часов сотрудника', callback_data: 'setWorkingHours' }],
          [{ text: 'Меню', callback_data: 'menu' }],
        ],
      },
    })
  }

  @Action('addWorker')
  async handleAddWorker(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    const chatId = ctx.chat.id.toString()
    this.chatContexts.set(chatId, { step: 'addWorker' })

    await ctx.sendMessage(`Введите данные сотрудника:\n@username  E-mail`, {
      reply_markup: {
        inline_keyboard: [[{ text: 'Назад', callback_data: 'workers' }]],
      },
    })
  }

  @Action('updateWorker')
  async handleUpdateWorker(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    const chatId = ctx.chat.id.toString()
    this.chatContexts.set(chatId, { step: 'updateWorkerInit' })

    await ctx.sendMessage(`Введите @username сотрудника:`, {
      reply_markup: {
        inline_keyboard: [[{ text: 'Назад', callback_data: 'workers' }]],
      },
    })
  }

  @Action('deleteWorker')
  async handleDeleteWorker(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    const chatId = ctx.chat.id.toString()
    this.chatContexts.set(chatId, { step: 'deleteWorker' })

    await ctx.sendMessage(`Введите @username сотрудника:`, {
      reply_markup: {
        inline_keyboard: [[{ text: 'Назад', callback_data: 'workers' }]],
      },
    })
  }

  @Action('setWorkingHours')
  async handleSetWorkingHours(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    const chatId = ctx.chat.id.toString()
    this.chatContexts.set(chatId, { step: 'setWorkingHours' })

    await ctx.sendMessage(
      'Введите @username сотрудника и рабочие часы по дням недели:\nПример: @username 6 6 6 6 6 0 0',
      {
        reply_markup: {
          inline_keyboard: [[{ text: 'Назад', callback_data: 'workers' }]],
        },
      },
    )
  }

  @Action('workerReport')
  async handleWorkerReport(@Ctx() ctx: Context) {
    await ctx.answerCbQuery()

    const chatId = ctx.chat.id.toString()
    this.chatContexts.set(chatId, { step: 'workerReport' })

    await ctx.sendMessage(
      `Введите @username сотрудника и промежуток времени\nПример: @username 2025-01-01 2025-01-10`,
      {
        reply_markup: {
          inline_keyboard: [[{ text: 'Назад', callback_data: 'menu' }]],
        },
      },
    )
  }

  @On('text')
  async handleText(@Ctx() ctx: Context) {
    if (!(await this.corporateTelegramBotService.isAllowed('admin', ctx.message.from.username))) {
      await ctx.reply('У вас нет доступа к боту.')
      return
    }
    const chatId = ctx.chat.id.toString()
    const context = this.chatContexts.get(chatId)

    const message = ctx.message as { text?: string }
    const input = message.text

    switch (context?.step) {
      case BotCommands.ADD_WORKER: {
        this.logger.verbose(`Went from addWorker`)
        const addText = await this.corporateTelegramBotService.handleAddWorker(input)
        await ctx.sendMessage(addText, {
          reply_markup: {
            inline_keyboard: [[{ text: 'Назад', callback_data: 'workers' }]],
          },
        })
        break
      }
      case BotCommands.UPDATE_WORKER_INIT: {
        this.logger.verbose(`Went from updateWorkerInit`)
        const updateText = await this.corporateTelegramBotService.handleUpdateWorkerInit(input)
        this.chatContexts.set(chatId, { step: 'updateWorker' })

        await ctx.sendMessage(updateText, {
          reply_markup: {
            inline_keyboard: [[{ text: 'Назад', callback_data: 'workers' }]],
          },
        })
        break
      }
      case BotCommands.UPDATE_WORKER: {
        this.logger.verbose(`Went from updateWorker`)
        const updateText = await this.corporateTelegramBotService.handleUpdateWorker(input)

        await ctx.sendMessage(updateText, {
          reply_markup: {
            inline_keyboard: [[{ text: 'Назад', callback_data: 'workers' }]],
          },
        })
        break
      }
      case BotCommands.DELETE_WORKER: {
        this.logger.verbose(`Went from deleteWorker`)
        const deleteText = await this.corporateTelegramBotService.handleDeleteWorker(input)

        await ctx.sendMessage(deleteText, {
          reply_markup: {
            inline_keyboard: [[{ text: 'Назад', callback_data: 'workers' }]],
          },
        })
        break
      }
      case BotCommands.SET_WORKING_HOURS: {
        this.logger.verbose(`Went from setWorkingHours`)
        const setWorkingHoursText = await this.corporateTelegramBotService.handleSetWorkingHours(
          input,
        )

        await ctx.sendMessage(setWorkingHoursText, {
          reply_markup: {
            inline_keyboard: [[{ text: 'Назад', callback_data: 'workers' }]],
          },
        })
        break
      }
      case BotCommands.WORKER_REPORT: {
        this.logger.verbose(`Went from workerReport`)
        const response = await this.corporateTelegramBotService.handleWorkerReport(input)

        await ctx.sendMessage(response, {
          reply_markup: {
            inline_keyboard: [[{ text: 'Назад', callback_data: 'menu' }]],
          },
          parse_mode: 'Markdown',
        })
        break
      }
      default:
        await ctx.sendMessage('Неизвестная команда. Начните сценарий заново.')
        break
    }
  }
}
