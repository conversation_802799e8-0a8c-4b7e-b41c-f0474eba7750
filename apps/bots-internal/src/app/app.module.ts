import { Module } from '@nestjs/common'
import { GitlabBotsModule } from './gitlab/gitlab-bots.module'
import { GrafanaBotsModule } from './grafana/grafana-bots.module'
import { ConfigModule } from '@majestic-backend/config'
import { CqrsModule } from '@nestjs/cqrs'
import { TelegramBotsModule } from './telegram/telegram.module'
import { YandexTrackerBotModule } from './yandex-tracker/yandex-tracker/yt.module'
import { RedisModule } from '@majestic-backend/redis'

@Module({
  imports: [
    // TODO Refactor redis to static module to avoid global option
    RedisModule.forRoot({ global: true }),
    CqrsModule.forRoot(),
    ConfigModule.forRoot(),
    GitlabBotsModule,
    GrafanaBotsModule,
    TelegramBotsModule,
    YandexTrackerBotModule,
  ],
})
export class AppModule {}
