import { Controller } from '@nestjs/common'
import { MessagePattern } from '@nestjs/microservices'
import { BotsInternalYandexTrackerUpdateFixedInVersion } from '@majestic-backend/contracts'
import { IWebhookUpdateFixedInVersion } from '@majestic-backend/yandex-tracker'
import { YandexTrackerBotService } from '../../yandex-tracker/yandex-tracker/yt.service'

@Controller()
export class YandexTrackerBotController {
  constructor(private readonly ytBotService: YandexTrackerBotService) {}

  @MessagePattern(BotsInternalYandexTrackerUpdateFixedInVersion.topic)
  public webhookUpdateFixedInVersion(data: BotsInternalYandexTrackerUpdateFixedInVersion.Request) {
    const payload = data.dto as IWebhookUpdateFixedInVersion

    return this.ytBotService.updateFixedInVersion(payload)
  }
}
