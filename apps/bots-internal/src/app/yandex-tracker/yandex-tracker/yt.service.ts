import {
  IWebhookUpdateFixedInVersion,
  YandexTrackerService,
} from '@majestic-backend/yandex-tracker'
import { Injectable, Logger } from '@nestjs/common'

@Injectable()
export class YandexTrackerBotService {
  private readonly logger = new Logger(YandexTrackerBotService.name)

  constructor(private readonly yandexTrackerService: YandexTrackerService) {}

  async updateFixedInVersion(payload: IWebhookUpdateFixedInVersion) {
    const { key } = payload

    const issueLinks = await this.yandexTrackerService.getIssueLinksByKey(key)

    if (!issueLinks.length) {
      this.logger.error(`Can't find links from issue. Key: ${key}`)
      return
    }

    for (const link of issueLinks) {
      const issue = await this.yandexTrackerService.getIssueByKey(link.object.key)

      const queueVersions = await this.yandexTrackerService.getQueueVersions(issue.queue.key)

      const queueVersion = queueVersions.find(el => el.name === key)
      if (queueVersion) {
        this.logger.verbose(`Queue version already exist`)

        await this.yandexTrackerService.updateIssueFieldsByKey(link.object.key, {
          fixVersions: [
            {
              self: queueVersion.self,
              id: queueVersion.id,
              display: queueVersion.name,
            },
          ],
        })
      } else {
        this.logger.verbose(`Queue version not found. Creating...`)

        const createdQueueVersion = await this.yandexTrackerService.createQueueVersion(
          issue.queue.key,
          key,
        )

        await this.yandexTrackerService.updateIssueFieldsByKey(link.object.key, {
          fixVersions: [
            {
              self: createdQueueVersion.self,
              id: createdQueueVersion.id,
              display: createdQueueVersion.name,
            },
          ],
        })
      }
    }

    this.logger.verbose(`Successfully update fixed in versions field`)
    return {
      ok: true,
    }
  }
}
