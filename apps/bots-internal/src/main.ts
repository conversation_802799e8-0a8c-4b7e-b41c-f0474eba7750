/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger } from '@nestjs/common'
import { NestFactory } from '@nestjs/core'

import { AppModule } from './app/app.module'
import {
  ConfigModule,
  DiscordConfig,
  TelegramConfig,
  GitLabConfig,
  GrafanaConfig,
  BotsInternalServiceConfig,
  YandexTrackerConfig,
  RedisConfig,
  GameServersConfig,
  WebDatabaseConfig,
} from '@majestic-backend/config'
import { ConfigService } from '@nestjs/config'
import { Transport } from '@nestjs/microservices'
import { DiscordExceptionFilter } from '@majestic-backend/discord'
import { LoggerService } from '@majestic-backend/utils'

async function bootstrap() {
  await ConfigModule.init([
    DiscordConfig,
    TelegramConfig,
    GitLabConfig,
    GrafanaConfig,
    BotsInternalServiceConfig,
    YandexTrackerConfig,
    GameServersConfig,
    WebDatabaseConfig,
    RedisConfig,
  ])
  const configService = new ConfigService()
  const port = configService.get('BOTS_INTERNAL_PORT')
  const app = await NestFactory.create(AppModule)

  app.useGlobalFilters(new DiscordExceptionFilter())
  app.connectMicroservice(
    {
      transport: Transport.TCP,
      options: {
        host: '0.0.0.0',
        port,
      },
    },
    { inheritAppConfig: true },
  )
  const metricsPort = app.get(BotsInternalServiceConfig).metricsPort
  await app.startAllMicroservices()

  const logger = new LoggerService()
  logger.setLevel(configService.get('LOGGER_LEVEL'))
  app.useLogger(logger)

  await app.listen(metricsPort)
  Logger.log(`🚀 Internal bots is running on port ${port}, metrics is running ${metricsPort}`)
}

bootstrap()
