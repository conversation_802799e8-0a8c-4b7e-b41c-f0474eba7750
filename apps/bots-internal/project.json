{"name": "bots-internal", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/bots-internal/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/bots-internal", "main": "apps/bots-internal/src/main.ts", "tsConfig": "apps/bots-internal/tsconfig.app.json", "assets": ["apps/bots-internal/src/assets", {"glob": "*.node", "input": "node_modules/@prisma/engines", "output": "."}], "isolatedConfig": true, "webpackConfig": "apps/bots-internal/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "bots-internal:build"}, "configurations": {"development": {"buildTarget": "bots-internal:build:development"}, "production": {"buildTarget": "bots-internal:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/bots-internal/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/bots-internal/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "container": {"executor": "@nx-tools/nx-container:build", "dependsOn": ["build"], "defaultConfiguration": "development", "options": {"engine": "docker", "context": "dist/apps/bots-internal", "file": "apps/bots-internal/Dockerfile", "metadata": {"images": ["majestic-backend/bots-internal"]}, "push": false}, "configurations": {"production": {"push": false}}}}, "tags": []}