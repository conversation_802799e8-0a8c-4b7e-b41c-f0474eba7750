{"name": "socket-gateway", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/socket-gateway/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/socket-gateway", "main": "apps/socket-gateway/src/main.ts", "tsConfig": "apps/socket-gateway/tsconfig.app.json", "assets": ["apps/socket-gateway/src/assets", {"glob": "*.node", "input": "node_modules/@prisma/engines", "output": "."}], "isolatedConfig": true, "webpackConfig": "apps/socket-gateway/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "socket-gateway:build"}, "configurations": {"development": {"buildTarget": "socket-gateway:build:development"}, "production": {"buildTarget": "socket-gateway:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/socket-gateway/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/socket-gateway/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "container": {"executor": "@nx-tools/nx-container:build", "dependsOn": ["build"], "defaultConfiguration": "development", "options": {"engine": "docker", "context": "dist/apps/socket-gateway", "file": "apps/socket-gateway/Dockerfile", "metadata": {"images": ["majestic-backend/socket-gateway"]}, "push": false}, "configurations": {"production": {"push": false}}}}, "tags": []}