import { NestFactory } from '@nestjs/core'

import { AppModule } from './app/app.module'
import { ConfigService } from '@nestjs/config'
import {
  CommonConfig,
  ConfigModule,
  GameServersConfig,
  RedisConfig,
  SocketServiceConfig,
  WebDatabaseConfig,
} from '@majestic-backend/config'
import { LoggerService } from '@majestic-backend/utils'

async function bootstrap() {
  await ConfigModule.init([
    CommonConfig,
    GameServersConfig,
    WebDatabaseConfig,
    RedisConfig,
    SocketServiceConfig,
  ])
  const configService = new ConfigService()
  const port = configService.get('SOCKET_PORT')
  const app = await NestFactory.create(AppModule)

  const logger = new LoggerService()
  logger.setLevel(configService.get('LOGGER_LEVEL'))
  app.useLogger(logger)

  await app.listen(port)

  logger.log(`🚀 Socket service is running on: ${await app.getUrl()}`)
}

bootstrap()
