import {
  WebSocketGateway,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets'
import { Server } from 'ws'
import { JwtService, jwtPayload } from '@majestic-backend/jwt'
import { RedisService } from '@majestic-backend/redis'
import {
  IBroadcastSocketPayload,
  IPublishSocketPayload,
  SOCKET_CHANNEL,
  SOCKET_MESSAGE,
} from '@majestic-backend/types'
import { PrismaService, GameModelEnum } from '@majestic-backend/database'
import { Logger, OnModuleInit } from '@nestjs/common'
import { CommonConfig } from '@majestic-backend/config'
import { Socket } from 'socket.io'
import { InjectMetric } from '@willsoto/nestjs-prometheus'
import { MetricName } from './metrics/metric-name.enum'
import { Gauge } from 'prom-client'

export const SOCKET_PATH = '/websockets'

interface ISocketClient extends Socket {
  auth?: Partial<jwtPayload> & { characterId: number }
}

@WebSocketGateway({
  path: SOCKET_PATH,
  cors: {
    origin: '*',
    transports: ['websocket'],
  },
})
export class SocketGateway implements OnModuleInit, OnGatewayConnection, OnGatewayDisconnect {
  private logger = new Logger(SocketGateway.name)
  constructor(
    private readonly jwtService: JwtService,
    private readonly redisService: RedisService,
    private readonly databaseService: PrismaService,
    private readonly commonConfig: CommonConfig,
    @InjectMetric(MetricName.SocketTotal) private readonly socketTotalMetric: Gauge,
  ) {}

  private DELAY_AFTER_DISCONNECT = 80 * 1000

  public sockets: Record<string, ISocketClient> = {}

  @WebSocketServer() server: Server

  onModuleInit() {
    this.initSubscribe()
  }

  async handleDisconnect(client: ISocketClient) {
    const serverId = client?.auth?.serverId
    const accountId = client?.auth?.characterId

    this.logger.debug(`Handle disconnect. ServerId: ${serverId}, AccountId: ${accountId}`)

    if (!serverId || !accountId) {
      return
    }
    const socketKey = `${serverId}-${accountId}`

    if (this.sockets[socketKey] === client) {
      delete this.sockets[socketKey]
    }

    await this.redisService.set(`sockets:exitTimeout:${serverId}:${accountId}`, 'true')

    this.socketTotalMetric.set(Object.keys(this.sockets).length)
    setTimeout(() => this.onExit(serverId, accountId), this.DELAY_AFTER_DISCONNECT)
  }

  async handleConnection(client: ISocketClient) {
    const token = client.handshake.query?.token as string
    const accountId = Number(client.handshake.query?.characterId)

    if (!token || !accountId) {
      return client.disconnect(true)
    }

    let payload: jwtPayload | null = null

    try {
      payload = await this.checkAuth(token)

      if (!payload) {
        this.logger.error(
          `[handleConnection] Error on parsing jwt. ServerId: ${payload?.serverId}, AccountId: ${accountId}`,
        )
        throw new Error('Invalid token')
      }
    } catch (e) {
      this.logger.error('[handleConnection] Parse error', e)
      this.logger.error(
        `[handleConnection] Error on socket auth. ServerId: ${payload?.serverId}, AccountId: ${accountId}`,
      )
      return client.disconnect(true)
    }

    client.auth = { ...payload, characterId: accountId }

    setTimeout(() => {
      this.redisService.del(`sockets:exitTimeout:${payload.serverId}:${accountId}`)
    }, 1500)

    const socketKey = `${payload.serverId}-${accountId}`

    if (this.sockets[socketKey]) {
      this.sockets[socketKey].disconnect(true)
      delete this.sockets[socketKey]
    }

    this.sockets[socketKey] = client

    this.socketTotalMetric.set(Object.keys(this.sockets).length)
    this.logger.debug(`Success connection. ServerId: ${payload.serverId}, AccountId: ${accountId}`)
  }

  private async initSubscribe() {
    const client = await this.redisService.createClient().connect()
    client.subscribe(SOCKET_CHANNEL, this.onRedisMessage.bind(this))
  }

  private onRedisMessage(data: string) {
    let jsonData:
      | IBroadcastSocketPayload<SOCKET_MESSAGE>
      | IPublishSocketPayload<SOCKET_MESSAGE>
      | undefined

    try {
      jsonData = JSON.parse(data)
    } catch (e) {
      this.logger.error('[onRedisMessage] Error on parse redis message', e)
    }

    if (jsonData.propagationMode === 'toProvided') {
      const receiverSocket = this.sockets[`${jsonData.serverId}-${jsonData.receivers}`]
      if (!receiverSocket) {
        return
      }

      this.logger.debug(
        `Processed redis message ${jsonData.type} with type ${jsonData.propagationMode}. ServerId: ${jsonData.serverId}, Receivers: ${jsonData.receivers}`,
      )

      receiverSocket.send({ type: jsonData.type, payload: jsonData.payload })
      return
    }

    if (jsonData.propagationMode === 'broadcast') {
      for (const socketInstance of Object.values(this.sockets)) {
        try {
          socketInstance.send({ type: jsonData.type, payload: jsonData.payload })
        } catch (e) {
          continue
        }
      }
    }

    this.logger.debug(
      `Processed redis message ${jsonData.type} with type ${jsonData.propagationMode}`,
    )
  }

  private async onExit(serverId: string, accountId: number) {
    const isExitTimeoutExists = await this.redisService.get(
      `sockets:exitTimeout:${serverId}:${accountId}`,
    )
    if (!isExitTimeoutExists) {
      return
    }
    await this.redisService.del(`sockets:exitTimeout:${serverId}:${accountId}`)

    this.redisService.publish('socket.onTimeout', { serverId, accountId })

    this.logger.debug(
      `[onExit] Socket timeout callback applied. ServerId: ${serverId}, AccountId: ${accountId}`,
    )
  }

  private async checkAuth(token: string) {
    if (!token) {
      this.logger.error('[checkAuth] No token provided')
      throw new Error()
    }
    const payload = await this.jwtService.verifyAsync<jwtPayload>(token, {
      secret: this.commonConfig.JWT_SECRET,
    })

    const tokenModel = this.databaseService.getGameModel(payload.serverId, GameModelEnum.TokenModel)

    const tokenData = await tokenModel.findToken(token)

    if (!payload || !tokenData) {
      this.logger.error('[checkAuth] No jwt payload')
      throw new Error()
    }

    return payload
  }
}
