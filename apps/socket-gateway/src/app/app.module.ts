import { Module } from '@nestjs/common'
import { SocketGateway } from './socket.gateway'
import { JwtModule } from '@majestic-backend/jwt'
import { RedisModule } from '@majestic-backend/redis'
import { PrismaModule } from '@majestic-backend/database'
import { MetricsModule } from '@majestic-backend/metrics'
import { ConfigModule } from '@majestic-backend/config'
import { MetricConfiguration } from './metrics/metric.conf'

@Module({
  imports: [
    ConfigModule.forRoot(),
    JwtModule,
    // TODO Refactor redis to static module to avoid global option
    RedisModule.forRoot({ global: true }),
    PrismaModule,
    {
      ...MetricsModule.forAppRoot('socket', MetricConfiguration),
      imports: [PrismaModule],
    },
  ],
  providers: [SocketGateway],
})
export class AppModule {}
