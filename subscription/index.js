export const SubscriptionType = {
    'F2Calendar': 'f2-calendar', // 1 +
    'BankVipRate': 'bank-vip-rate', // 2 +
    //'CarSlots': 'car-slots', // 3
    'CasinoChips': 'casino-chips', // 4 +
    'LeaveFraction': 'leave-fraction', // 5 +
    'JobSkill': 'job-skill', // 7
    'SkillMultiplier': 'skill-multiplier', // 9 +
    // 'WaterAndHungerMultiplier': 'water-hunger-multiplier', // 12
    'DemorganMultiplier': 'demorgan-multiplier', // 13
    'DepositClose': 'deposit-close', // 14
    'Vehicle': 'vehicle', // 15---
    // 'InventoryOverload': 'inventory-overload', // 16---
    'PositiveEffects': 'positive-effects', // 17 ----
    'AirportCayo': 'airport-cayo', // 18
    'LuckyWheel': 'lucky-wheel', // 19
    'Infected': 'infected', // 20
    'CriminalRecord': 'criminal-record', // 21
    'Bank': 'bank', // 22
    'DeathSpawn': 'death-spawn', // 23
    'JobSalary': 'job-salary', // 24
    'DiscountTuning': 'DiscountTuning',
    'HospitalFullHealth': 'HospitalFullHealth'
}

export const vehicleDiscount = 80; // Скидка на тюнинг и стартовую цену машины считается от 0 до 100
export const buySetDiscount = 15;

export const leaveFractionIds = [6, 8, 9, 10, 11, 12];

export const delayTime = 60 * 60 * 24 * 30;

export const SubscriptionData = [
    {
        id: 1,
		icon: 'fractions',
        key: SubscriptionType.LeaveFraction,
        title: 'subscriptionFeatures.factions.title',
        description: 'subscriptionFeatures.factions.description',
        days: 5,
        value: 1,
    },
    {
        id: 2,
        icon: 'transport',
        key: SubscriptionType.Vehicle,
        title: 'subscriptionFeatures.vehicle.title',
        description: 'galant',
        days: 10,
        model: 'galant',
    },
    {
        id: 3,
        icon: 'transport',
        key: SubscriptionType.Vehicle,
        title: 'subscriptionFeatures.vehicle.title',
        description: 'f1503',
        days: 40,
        model: 'f1503',
    },
    {
        id: 4,
        icon: 'transport',
        key: SubscriptionType.Vehicle,
        title: 'subscriptionFeatures.vehicle.title',
        description: 'p928',
        days: 70,
        model: 'p928',
    },
    {
        id: 5,
        icon: 'transport',
        key: SubscriptionType.Vehicle,
        title: 'subscriptionFeatures.vehicle.title',
        description: 'a42',
        days: 100,
        model: 'a42',
    },
    {
        id: 6,
        icon: 'transport',
        key: SubscriptionType.Vehicle,
        title: 'subscriptionFeatures.vehicle.title',
        description: 'defender2',
        days: 130,
        model: 'defender2',
    },
    {
        id: 7,
        icon: 'transport',
        key: SubscriptionType.Vehicle,
        title: 'subscriptionFeatures.vehicle.title',
        description: 'viper2',
        days: 160,
        model: 'viper2',
    },
    {
        id: 8,
		icon: 'character',
        key: SubscriptionType.JobSkill,
        title: 'subscriptionFeatures.jobSkill.title',
        description: 'subscriptionFeatures.jobSkill.description',
        days: 15,
        value: 1,
		info: '10%',
    },
    {
        id: 9,
		icon: 'character',
        key: SubscriptionType.JobSkill,
        title: 'subscriptionFeatures.jobSkill.title',
        description: 'subscriptionFeatures.jobSkill.description',
        days: 65,
        value: 2,
		info: '20%',
    },
    {
        id: 10,
		icon: 'character',
        key: SubscriptionType.JobSkill,
        title: 'subscriptionFeatures.jobSkill.title',
        description: 'subscriptionFeatures.jobSkill.description',
        days: 175,
        value: 3,
		info: '30%',
    },
    {
        id: 11,
		icon: 'casino',
        key: SubscriptionType.CasinoChips,
        title: 'subscriptionFeatures.casinoChips.title',
        description: 'subscriptionFeatures.casinoChips.description',
        days: 20,
        value: 0.3,
    },
    {
        id: 12,
		icon: 'finance',
        key: SubscriptionType.Bank,
        title: 'subscriptionFeatures.bank.title',
        description: 'subscriptionFeatures.bank.description',
        days: 25,
    },
    {
        id: 13,
		icon: 'character',
        key: SubscriptionType.HospitalFullHealth,
        title: 'subscriptionFeatures.HospitalFullHealth.title',
        description: 'subscriptionFeatures.HospitalFullHealth.description',
        days: 30,
    },
    {
        id: 14,
		icon: 'transport',
        statKey: 'extraCarSlots',
        title: 'subscriptionFeatures.extraCarSlots.title',
        description: 'subscriptionFeatures.extraCarSlots.description',
        days: 40,
        value: 2,
		info: '+2',
    },
    {
        id: 15,
		icon: 'transport',
        statKey: 'extraCarSlots',
        title: 'subscriptionFeatures.extraCarSlots.title',
        description: 'subscriptionFeatures.extraCarSlots.description',
        days: 85,
        value: 2,
		info: '+4',
    },
    {
        id: 16,
		icon: 'transport',
        statKey: 'extraCarSlots',
        title: 'subscriptionFeatures.extraCarSlots.title',
        description: 'subscriptionFeatures.extraCarSlots.description',
        days: 155,
        value: 2,
		info: '+6',
    },
    {
        id: 17,
		icon: 'finance',
        key: SubscriptionType.DepositClose,
        title: 'subscriptionFeatures.depositClose.title',
        description: 'subscriptionFeatures.depositClose.description',
        days: 50,
    },
    //
    {
        id: 18,
		icon: 'character',
        key: SubscriptionType.DemorganMultiplier,
        title: 'subscriptionFeatures.demorganMultiplier.title',
        description: 'subscriptionFeatures.demorganMultiplier.description',
        days: 60,
        value: 10,
		info: '10%',
    },
    {
        id: 19,
		icon: 'character',
        key: SubscriptionType.DemorganMultiplier,
        title: 'subscriptionFeatures.demorganMultiplier.title',
        description: 'subscriptionFeatures.demorganMultiplier.description',
        days: 115,
        value: 20,
		info: '20%',
    },
    {
        id: 20,
		icon: 'character',
        key: SubscriptionType.DemorganMultiplier,
        title: 'subscriptionFeatures.demorganMultiplier.title',
        description: 'subscriptionFeatures.demorganMultiplier.description',
        days: 230,
        value: 40,
		info: '40%',
    },
    {
        id: 21,
		icon: 'character',
        statKey: 'waterAndHungerMultiplier',
        title: 'subscriptionFeatures.waterAndHungerMultiplier.title',
        description: 'subscriptionFeatures.waterAndHungerMultiplier.description',
        days: 70,
        value: 25,
		info: '25%',
    },
    {
        id: 22,
		icon: 'character',
        statKey: 'waterAndHungerMultiplier',
        title: 'subscriptionFeatures.waterAndHungerMultiplier.title',
        description: 'subscriptionFeatures.waterAndHungerMultiplier.description',
        days: 175,
        value: 15,
		info: '40%',
    },
    {
        id: 23,
		icon: 'character',
        statKey: 'waterAndHungerMultiplier',
        title: 'subscriptionFeatures.waterAndHungerMultiplier.title',
        description: 'subscriptionFeatures.waterAndHungerMultiplier.description',
        days: 365,
        value: 35,
		info: '75%',
    },
    {
        id: 24,
		icon: 'finance',
        key: SubscriptionType.AirportCayo,
        title: 'subscriptionFeatures.airportCayo.title',
        description: 'subscriptionFeatures.airportCayo.description',
        days: 80,
    },
    {
        id: 25,
		icon: 'casino',
        key: SubscriptionType.LuckyWheel,
        title: 'subscriptionFeatures.luckyWheel.title',
        description: 'subscriptionFeatures.luckyWheel.description',
        days: 90,
        value: 1, // сколько прибавляем если есть навык
    },
    {
        id: 26,
		icon: 'finance',
        key: SubscriptionType.JobSalary,
        title: 'subscriptionFeatures.jobSalary.title',
        description: 'subscriptionFeatures.jobSalary.description',
        days: 100,
        value: 0.05,
    },
    {
        id: 27,
		icon: 'character',
        key: SubscriptionType.SkillMultiplier,
        title: 'subscriptionFeatures.skillMultiplier.title',
        description: 'subscriptionFeatures.skillMultiplier.description',
        days: 110,
        value: 10,
		info: '10%',
    },
    {
        id: 28,
		icon: 'character',
        key: SubscriptionType.SkillMultiplier,
        title: 'subscriptionFeatures.skillMultiplier.title',
        description: 'subscriptionFeatures.skillMultiplier.description',
        days: 185,
        value: 20,
		info: '20%',
    },
    {
        id: 29,
		icon: 'character',
        key: SubscriptionType.SkillMultiplier,
        title: 'subscriptionFeatures.skillMultiplier.title',
        description: 'subscriptionFeatures.skillMultiplier.description',
        days: 245,
        value: 30,
		info: '30%',
    },
	{
        id: 30,
		icon: 'transport',
        key: SubscriptionType.DiscountTuning,
        title: 'subscriptionFeatures.discountTuning.title',
        description: 'subscriptionFeatures.discountTuning.description',
        days: 120,
        value: 15,
    },
    {
        id: 31,
		icon: 'character',
        key: SubscriptionType.Infected,
        title: 'subscriptionFeatures.infected.title',
        description: 'subscriptionFeatures.infected.description',
        days: 130,
    },
    {
        id: 32,
		icon: 'fractions',
        key: SubscriptionType.CriminalRecord,
        title: 'subscriptionFeatures.criminalRecord.title',
        description: 'subscriptionFeatures.criminalRecord.description',
        days: 140,
    },
    {
        id: 33,
		icon: 'finance',
        key: SubscriptionType.BankVipRate,
        title: 'subscriptionFeatures.bankVipRate.title',
        description: 'subscriptionFeatures.bankVipRate.description',
        days: 150,
        value: 1,
    },
    {
        id: 34,
		icon: 'character',
        key: SubscriptionType.F2Calendar,
        title: 'subscriptionFeatures.f2Calendar.title',
        description: 'subscriptionFeatures.f2Calendar.description',
        days: 160,
        value: 1,
    },
    {
        id: 35,
		icon: 'character',
        statKey: 'buffDurationMult',
        title: 'subscriptionFeatures.buffDuration.title',
        description: 'subscriptionFeatures.buffDuration.description',
        days: 170,
        value: 25,
    },
    {
        id: 36,
		icon: 'character',
        key: SubscriptionType.DeathSpawn,
        title: 'subscriptionFeatures.deathSpawn.title',
        description: 'subscriptionFeatures.deathSpawn.description',
        days: 180,
    },
]

export const subscriptionStarterPacks = {
    bronze: 3,
    gold: 5,
    platinum: 7,
}

export const subscriptionPromoCode = {
    user: 3,
    media: 7
}
