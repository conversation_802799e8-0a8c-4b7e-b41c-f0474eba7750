import male_tops from './male_tops.json';
import female_tops from './female_tops.json';

import male_classes from './male_classes.json';
import female_classes from './female_classes.json';

const tops_config = [{}, {}];

for (let i = 0; i < 2; i++) {
  const classes = i === 0 ? male_classes : female_classes;
  const tops = i === 0 ? male_tops : female_tops;

  const data = tops_config[i];

  for (const topId in tops) {
    data[topId] = [];

    const topsData = tops[topId];

    for (let j = 0; j < topsData.length; j++) {
      const topItem = topsData[j];

      if (typeof topItem === 'string') {
        for (let k = 0; k < classes[topItem].length; k++) {
          const classItem = classes[topItem][k];

          let dataItem = data[topId].find(dataItem => (
            dataItem.u === classItem.u
          ));

          if (!dataItem) {
            dataItem = {
              u: classItem.u,
              t: classItem.t
            };

            data[topId].push(dataItem);
          }

          dataItem.t = classItem.t;
        }
      } else {
        let dataItem = data[topId].find(dataItem => (
          dataItem.u === topItem.u
        ));

        if (!dataItem) {
          dataItem = {
            u: topItem.u,
            t: topItem.t
          };

          data[topId].push(dataItem);
        }

        dataItem.t = topItem.t;
      }
    }
  }
}

export default tops_config;
