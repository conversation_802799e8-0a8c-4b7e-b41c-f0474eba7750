module.exports = {
    shirt: {
        base: 'try_shirt_base',
        try: ['try_shirt_positive_a','try_shirt_positive_b','try_shirt_positive_c','try_shirt_positive_d','try_shirt_neutral_a','try_shirt_neutral_b','try_shirt_neutral_c','try_shirt_neutral_d'],
        checkout: ['check_out_a','check_out_b','check_out_c']
    },
    shoes: {
        base: 'try_shoes_base',
        try: ['try_shoes_positive_a','try_shoes_positive_b','try_shoes_positive_c','try_shoes_positive_d','try_shoes_neutral_a','try_shoes_neutral_b','try_shoes_neutral_c','try_shoes_neutral_d'],
        checkout: ['check_out_a','check_out_b','check_out_c']
    },
    trousers: {
        base: 'try_trousers_base',
        try: ['try_trousers_positive_a','try_trousers_positive_b','try_trousers_positive_c','try_trousers_positive_d','try_trousers_neutral_a','try_trousers_neutral_b','try_trousers_neutral_c','try_trousers_neutral_d'],
        checkout: ['check_out_a','check_out_b','check_out_c']
    },
    glasses: {
        base: 'try_glasses_base',
        try: ['try_glasses_positive_a','try_glasses_positive_b','try_glasses_positive_c','try_glasses_positive_d','try_glasses_neutral_a','try_glasses_neutral_b','try_glasses_neutral_c','try_glasses_neutral_d'],
        checkout: ['check_out_a','check_out_b','check_out_c']
    }
}