module.exports = {
    trucker: [
        [
            {
                c: 11,
                d: 346,
                t: 0
            },
            {
                c: 3,
                d: 11,
                t: 0
            },
            {
                c: 8,
                d: 75,
                t: 0
            },
            {
                c: 4,
                d: 4,
                t: 0
            },
            {
                c: 6,
                d: 4,
                t: 1
            },
            {
                c: 0,
                d: 142,
                t: 0,
                p: true
            }
        ],
        [
            {
                c: 11,
                d: 244,
                t: 0
            },
            {
                c: 8,
                d: 14,
                t: 0
            },
            {
                c: 3,
                d: 9,
                t: 0
            },
            {
                c: 6,
                d: 27,
                t: 0
            },
            {
                c: 4,
                d: 4,
                t: 9
            },
            {
                c: 0,
                d: 141,
                t: 0,
                p: true
            }
        ]
    ],
    builder: [
        [
            {
                c: 11,
                d: 16,
                t: 0
            },
            {
                c: 8,
                d: 59,
                t: 1
            },
            {
                c: 3,
                d: 52,
                t: 0
            },
            {
                c: 4,
                d: 102,
                t: 0
            },
            {
                c: 6,
                d: 27,
                t: 0
            },
            {
                c: 0,
                d: 145,
                t: 0,
                p: true
            }
        ],
        [
            {
                c: 11,
                d: 50,
                t: 0
            },
            {
                c: 8,
                d: 36,
                t: 1
            },
            {
                c: 3,
                d: 62,
                t: 0
            },
            {
                c: 4,
                d: 61,
                t: 9
            },
            {
                c: 6,
                d: 26,
                t: 0
            },
            {
                c: 0,
                d: 144,
                t: 1,
                p: true
            }
        ]
    ],
    busdriver: [
        [
            {
                c: 11,
                d: 322,
                t: 0
            },
            {
                c: 7,
                d: 23,
                t: 11
            },
            {
                c: 4,
                d: 52,
                t: 2
            },
            {
                c: 6,
                d: 20,
                t: 7
            },
            {
                c: 3,
                d: 4,
                t: 0
            },
        ],
        [
            {
                c: 11,
                d: 333,
                t: 0
            },
            {
                c: 7,
                d: 22,
                t: 0
            },
            {
                c: 4,
                d: 150,
                t: 0
            },
            {
                c: 6,
                d: 27,
                t: 0
            },
            {
                c: 3,
                d: 3,
                t: 0
            },
        ]
    ],
    taxi: [
        [
            {
                c: 11,
                d: 21,
                t: 2
            },
            {
                c: 8,
                d: 6,
                t: 0
            },
            {
                c: 3,
                d: 11,
                t: 0
            },
            {
                c: 7,
                d: 27,
                t: 2
            },
            {
                c: 4,
                d: 28,
                t: 0
            },
            {
                c: 6,
                d: 20,
                t: 7
            },
        ],
        [
            {
                c: 11,
                d: 340,
                t: 0
            },
            {
                c: 8,
                d: 39,
                t: 0
            },
            {
                c: 4,
                d: 150,
                t: 0
            },
            {
                c: 6,
                d: 27,
                t: 0
            },
            {
                c: 3,
                d: 3,
                t: 0
            },
            {
                c: 7,
                d: 19,
                t: 0
            },
        ]
    ],
    farmer: [
        [
            {
                c: 11,
                d: 97,
                t: 0
            },
            {
                c: 4,
                d: 90,
                t: 0
            },
            {
                c: 6,
                d: 98,
                t: 0
            },
            {
                c: 3,
                d: 63,
                t: 0
            }
        ],
        [
            {
                c: 11,
                d: 88,
                t: 0
            },
            {
                c: 4,
                d: 93,
                t: 0
            },
            {
                c: 6,
                d: 69,
                t: 0
            },
            {
                c: 3,
                d: 83,
                t: 0
            }
        ]
    ],
    fishing: [
        [
            {
                c: 11,
                d: 247,
                t: 0
            },
            {
                c: 3,
                d: 0,
                t: 0
            },
            {
                c: 8,
                d: 85,
                t: 8
            },
            {
                c: 4,
                d: 12,
                t: 0
            },
            {
                c: 6,
                d: 89,
                t: 0
            },
            {
                c: 0,
                d: 20,
                t: 0,
                p: true
            },
        ],
        [
            {
                c: 11,
                d: 255,
                t: 4
            },
            {
                c: 3,
                d: 0,
                t: 0
            },
            {
                c: 8,
                d: 86,
                t: 0
            },
            {
                c: 6,
                d: 24,
                t: 0
            },
            {
                c: 4,
                d: 137,
                t: 0
            },
            {
                c: 0,
                d: 54,
                t: 1,
                p: true
            },
        ]
    ],
    garbagecollector: [
        [
            {
                c: 11,
                d: 57,
                t: 0
            },
            {
                c: 8,
                d: 59,
                t: 0
            },
            {
                c: 3,
                d: 64,
                t: 0
            },
            {
                c: 4,
                d: 36,
                t: 0
            },
            {
                c: 6,
                d: 27,
                t: 0
            },
        ],
        [
            {
                c: 11,
                d: 50,
                t: 0
            },
            {
                c: 8,
                d: 36,
                t: 0
            },
            {
                c: 3,
                d: 36,
                t: 0
            },
            {
                c: 4,
                d: 35,
                t: 0
            },
            {
                c: 6,
                d: 26,
                t: 0
            },
        ]
    ],
    gopostal: [
        [
            {
                c: 11,
                d: 2013,
                t: 0
            },
            {
                c: 4,
                d: 2004,
                t: 10
            },
            {
                c: 3,
                d: 0,
                t: 0
            },
            {
                c: 6,
                d: 25,
                t: 0
            },
        ],
        [
            {
                c: 11,
                d: 2000,
                t: 6
            },
            {
                c: 4,
                d: 2000,
                t: 5
            },
            {
                c: 3,
                d: 0,
                t: 0
            },
            {
                c: 6,
                d: 32,
                t: 0
            },
        ]
    ],
    moneycollector: [
        [
            {
                c: 11,
                d: 2001,
                t: 11
            },
            {
                c: 5,
                d: 41,
                t: 0
            },
            {
                c: 7,
                d: 2047,
                t: 0
            },
            {
                c: 3,
                d: 1,
                t: 0
            },
            {
                c: 8,
                d: 2012,
                t: 0
            },
            {
                c: 4,
                d: 2002,
                t: 0
            },
            {
                c: 6,
                d: 25,
                t: 0
            },
            {
                c: 10,
                d: 2070,
                t: 0
            },
        ],
        [
            {
                c: 11,
                d: 2001,
                t: 10
            },
            {
                c: 8,
                d: 2000,
                t: 0
            },
            {
                c: 7,
                d: 2035,
                t: 0
            },
            {
                c: 3,
                d: 3,
                t: 0
            },
            {
                c: 6,
                d: 25,
                t: 0
            },
            {
                c: 4,
                d: 2002,
                t: 0
            },
            {
                c: 10,
                d: 2060,
                t: 0
            },
            {
                c: 0,
                d: 2008,
                t: 0,
                p: true
            },
        ]
    ],
    butcher: [
        [
            {
                c: 11,
                d: 67,
                t: 0
            },
            {
                c: 8,
                d: 61,
                t: 0
            },
            {
                c: 3,
                d: 4,
                t: 0
            },
            {
                c: 4,
                d: 40,
                t: 0
            },
            {
                c: 6,
                d: 25,
                t: 0
            },
        ],
        [
            {
                c: 11,
                d: 61,
                t: 0
            },
            {
                c: 8,
                d: 42,
                t: 0
            },
            {
                c: 3,
                d: 3,
                t: 0
            },
            {
                c: 6,
                d: 92,
                t: 0
            },
            {
                c: 4,
                d: 40,
                t: 0
            },
        ]
    ],
    lumberjack: [
        [
            {
                c: 11,
                d: 346,
                t: 19
            },
            {
                c: 3,
                d: 187,
                t: 0
            },
            {
                c: 4,
                d: 0,
                t: 4
            },
            {
                c: 6,
                d: 12,
                t: 0
            },
            {
                c: 8,
                d: 0,
                t: 0
            },
            {
                c: 0,
                d: 120,
                t: 0,
                p: true
            },
        ],
        [
            {
                c: 11,
                d: 364,
                t: 19
            },
            {
                c: 3,
                d: 232,
                t: 0
            },
            {
                c: 4,
                d: 0,
                t: 2
            },
            {
                c: 6,
                d: 68,
                t: 0
            },
            {
                c: 8,
                d: 26,
                t: 0
            },
        ]
    ],
    mushroomer: [
        [
            {
                c: 11,
                d: 97,
                t: 0
            },
            {
                c: 4,
                d: 75,
                t: 0
            },
            {
                c: 6,
                d: 61,
                t: 3
            },
            {
                c: 0,
                d: 94,
                t: 0,
                p: true
            },
        ],
        [
            {
                c: 11,
                d: 54,
                t: 0
            },
            {
                c: 3,
                d: 3,
                t: 0
            },
            {
                c: 8,
                d: 14,
                t: 0
            },
            {
                c: 6,
                d: 74,
                t: 0
            },
            {
                c: 4,
                d: 49,
                t: 0
            },
        ]
    ],
    mining: [
        [
            {
                c: 3,
                d: 63,
                t: 0
            },
            {
                c: 11,
                d: 56,
                t: 0
            },
            {
                c: 8,
                d: 59,
                t: 0
            },
            {
                c: 4,
                d: 43,
                t: 0
            },
            {
                c: 6,
                d: 25,
                t: 0
            },
            {
                c: 0,
                d: 145,
                t: 1,
                p: true
            },
            {
                c: 1,
                d: 15,
                t: 9,
                p: true
            },
        ],
        [
            {
                c: 3,
                d: 49,
                t: 1
            },
            {
                c: 11,
                d: 50,
                t: 0
            },
            {
                c: 8,
                d: 36,
                t: 0
            },
            {
                c: 6,
                d: 52,
                t: 0
            },
            {
                c: 4,
                d: 1,
                t: 0
            },
            {
                c: 0,
                d: 144,
                t: 1,
                p: true
            },
            {
                c: 1,
                d: 9,
                t: 9,
                p: true
            },
        ]
    ],
    firefighter: [
        [
            {
                c: 0,
                d: 137,
                t: 0,
                p: true
            },
            {
                c: 11,
                d: 314,
                t: 0
            },
            {
                c: 4,
                d: 120,
                t: 0
            },
            {
                c: 6,
                d: 71,
                t: 1
            },
            {
                c: 10,
                d: 64,
                t: 0
            },
            {
                c: 8,
                d: 151,
                t: 0
            },
            {
                c: 3,
                d: 109,
                t: 6
            },
        ],
        [
            {
                c: 0,
                d: 136,
                t: 1,
                p: true
            },
            {
                c: 11,
                d: 325,
                t: 0
            },
            {
                c: 4,
                d: 126,
                t: 0
            },
            {
                c: 6,
                d: 74,
                t: 1
            },
            {
                c: 10,
                d: 73,
                t: 0
            },
            {
                c: 8,
                d: 187,
                t: 0
            },
            {
                c: 3,
                d: 117,
                t: 6
            },
        ]
    ]
}
