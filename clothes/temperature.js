export const temperatureClothMale = [
    { component: 4, drawable: 0, minTemp: -20, maxTemp: 35 },
    { component: 4, drawable: 43, minTemp: -25, maxTemp: 30 },
    { component: 4, drawable: 63, minTemp: -20, maxTemp: 35 },
    { component: 4, drawable: 1, minTemp: -20, maxTemp: 35 },
    { component: 4, drawable: 3, minTemp: -5, maxTemp: 35 },
    { component: 4, drawable: 29, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 47, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 8, minTemp: -30, maxTemp: 20 },
    { component: 4, drawable: 9, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 7, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 27, minTemp: -20, maxTemp: 30 },
    { component: 4, drawable: 5, minTemp: -10, maxTemp: 30 },
    { component: 4, drawable: 55, minTemp: -10, maxTemp: 30 },
    { component: 4, drawable: 64, minTemp: -10, maxTemp: 30 },
    { component: 4, drawable: 98, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 15, minTemp: 20, maxTemp: 50 },
    { component: 4, drawable: 100, minTemp: -25, maxTemp: 30 },
    { component: 4, drawable: 14, minTemp: 25, maxTemp: 60 },
    { component: 4, drawable: 18, minTemp: 25, maxTemp: 60 },
    { component: 4, drawable: 42, minTemp: 15, maxTemp: 55 },
    { component: 4, drawable: 62, minTemp: 15, maxTemp: 55 },
    { component: 4, drawable: 21, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 61, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 105, minTemp: -25, maxTemp: 99 },
    { component: 11, drawable: 123, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 83, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 84, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 81, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 128, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 164, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 111, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 139, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 97, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 17, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 36, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 237, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 1, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 16, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 22, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 33, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 34, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 44, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 146, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 273, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 14, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 89, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 238, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 169, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 170, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 125, minTemp: -20, maxTemp: 25 },
    { component: 11, drawable: 6, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 37, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 166, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 157, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 233, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 268, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 3, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 141, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 113, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 7, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 86, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 249, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 110, minTemp: -20, maxTemp: 25 },
    { component: 11, drawable: 138, minTemp: -20, maxTemp: 25 },
    { component: 11, drawable: 151, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 156, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 264, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 116, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 115, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 121, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 171, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 352, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 279, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 281, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 356, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 313, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 323, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 325, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 105, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 299, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 354, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 338, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 127, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 339, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 340, minTemp: 15, maxTemp: 35 },
    { component: 6, drawable: 5, minTemp: 25, maxTemp: 99 },
    { component: 6, drawable: 16, minTemp: 25, maxTemp: 99 },
    { component: 6, drawable: 6, minTemp: 20, maxTemp: 99 },
    { component: 6, drawable: 8, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 46, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 23, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 64, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 7, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 9, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 35, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 79, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 80, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 81, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 82, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 89, minTemp: -30, maxTemp: 30 },
    { component: 4, drawable: 4, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 26, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 82, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 83, minTemp: -20, maxTemp: 40 },
    { component: 4, drawable: 75, minTemp: -20, maxTemp: 35 },
    { component: 4, drawable: 76, minTemp: -5, maxTemp: 35 },
    { component: 4, drawable: 105, minTemp: -15, maxTemp: 35 },
    { component: 4, drawable: 32, minTemp: -1, maxTemp: 40 },
    { component: 4, drawable: 45, minTemp: 5, maxTemp: 35 },
    { component: 4, drawable: 58, minTemp: 5, maxTemp: 35 },
    { component: 4, drawable: 65, minTemp: 5, maxTemp: 35 },
    { component: 4, drawable: 69, minTemp: -20, maxTemp: 30 },
    { component: 4, drawable: 71, minTemp: -20, maxTemp: 30 },
    { component: 4, drawable: 73, minTemp: -20, maxTemp: 30 },
    { component: 4, drawable: 6, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 16, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 54, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 12, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 17, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 132, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 78, minTemp: -10, maxTemp: 35 },
    { component: 4, drawable: 79, minTemp: -10, maxTemp: 35 },
    { component: 4, drawable: 80, minTemp: 10, maxTemp: 50 },
    { component: 4, drawable: 81, minTemp: 10, maxTemp: 50 },
    { component: 4, drawable: 54, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 128, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 131, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 129, minTemp: 15, maxTemp: 60 },
    { component: 11, drawable: 93, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 18, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 8, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 38, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 50, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 51, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 52, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 53, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 71, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 73, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 78, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 80, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 351, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 357, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 92, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 144, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 117, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 107, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 61, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 62, minTemp: -1, maxTemp: 30 },
    { component: 11, drawable: 64, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 118, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 155, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 148, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 152, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 147, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 158, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 165, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 177, minTemp: 15, maxTemp: 30 },
    { component: 11, drawable: 69, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 74, minTemp: -1, maxTemp: 35 },
    { component: 11, drawable: 79, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 106, minTemp: -20, maxTemp: 25 },
    { component: 11, drawable: 134, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 135, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 143, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 149, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 150, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 153, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 154, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 167, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 168, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 185, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 187, minTemp: -15, maxTemp: 35 },
    { component: 11, drawable: 189, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 190, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 191, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 192, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 193, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 194, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 196, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 198, minTemp: -5, maxTemp: 25 },
    { component: 11, drawable: 200, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 205, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 217, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 223, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 224, minTemp: -20, maxTemp: 30 },
    { component: 11, drawable: 225, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 227, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 229, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 230, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 235, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 241, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 244, minTemp: -25, maxTemp: 30 },
    { component: 11, drawable: 245, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 247, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 248, minTemp: -25, maxTemp: 30 },
    { component: 11, drawable: 251, minTemp: -25, maxTemp: 30 },
    { component: 11, drawable: 255, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 261, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 266, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 258, minTemp: -5, maxTemp: 25 },
    { component: 11, drawable: 259, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 262, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 269, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 271, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 288, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 296, minTemp: -25, maxTemp: 30 },
    { component: 11, drawable: 298, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 303, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 305, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 307, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 308, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 309, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 329, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 330, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 359, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 361, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 358, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 332, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 342, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 344, minTemp: -5, maxTemp: 35 },
    { component: 6, drawable: 1, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 4, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 12, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 14, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 22, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 26, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 28, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 29, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 31, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 32, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 42, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 43, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 48, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 49, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 55, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 75, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 76, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 93, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 94, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 57, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 70, minTemp: -35, maxTemp: 30 },
    { component: 6, drawable: 71, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 72, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 73, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 77, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 92, minTemp: -10, maxTemp: 50 },
    { component: 4, drawable: 10, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 13, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 19, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 20, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 22, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 23, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 24, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 25, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 28, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 35, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 37, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 48, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 49, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 50, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 51, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 52, minTemp: -10, maxTemp: 50 },
    { component: 4, drawable: 53, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 60, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 96, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 116, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 118, minTemp: -15, maxTemp: 40 },
    { component: 11, drawable: 11, minTemp: 10, maxTemp: 45 },
    { component: 11, drawable: 21, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 25, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 120, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 45, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 11, minTemp: 10, maxTemp: 45 },
    { component: 11, drawable: 21, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 25, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 120, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 12, minTemp: 10, maxTemp: 45 },
    { component: 11, drawable: 13, minTemp: 10, maxTemp: 45 },
    { component: 11, drawable: 26, minTemp: 10, maxTemp: 45 },
    { component: 11, drawable: 41, minTemp: 10, maxTemp: 45 },
    { component: 11, drawable: 4, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 10, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 19, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 20, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 23, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 24, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 27, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 28, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 29, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 95, minTemp: 10, maxTemp: 45 },
    { component: 11, drawable: 31, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 35, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 46, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 58, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 59, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 99, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 101, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 102, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 103, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 108, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 119, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 145, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 183, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 70, minTemp: -30, maxTemp: 25 },
    { component: 11, drawable: 240, minTemp: -30, maxTemp: 25 },
    { component: 11, drawable: 140, minTemp: -15, maxTemp: 35 },
    { component: 11, drawable: 72, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 76, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 77, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 142, minTemp: -20, maxTemp: 30 },
    { component: 11, drawable: 136, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 290, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 290, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 292, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 294, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 311, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 346, minTemp: 10, maxTemp: 45 },
    { component: 11, drawable: 347, minTemp: 10, maxTemp: 45 },
    { component: 11, drawable: 322, minTemp: 10, maxTemp: 45 },
    { component: 11, drawable: 349, minTemp: 10, maxTemp: 45 },
    { component: 6, drawable: 10, minTemp: -15, maxTemp: 45 },
    { component: 6, drawable: 15, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 18, minTemp: -15, maxTemp: 45 },
    { component: 6, drawable: 19, minTemp: -15, maxTemp: 45 },
    { component: 6, drawable: 20, minTemp: -15, maxTemp: 45 },
    { component: 6, drawable: 21, minTemp: -15, maxTemp: 45 },
    { component: 6, drawable: 30, minTemp: 10, maxTemp: 45 },
    { component: 6, drawable: 36, minTemp: 10, maxTemp: 45 },
    { component: 6, drawable: 37, minTemp: -25, maxTemp: 30 },
    { component: 6, drawable: 38, minTemp: -15, maxTemp: 40 },
    { component: 6, drawable: 40, minTemp: -15, maxTemp: 45 },
    { component: 6, drawable: 44, minTemp: -25, maxTemp: 30 },
    { component: 6, drawable: 45, minTemp: -15, maxTemp: 40 },
    { component: 6, drawable: 50, minTemp: -25, maxTemp: 25 },
    { component: 6, drawable: 51, minTemp: -20, maxTemp: 30 },
    { component: 6, drawable: 52, minTemp: -30, maxTemp: 20 },
    { component: 6, drawable: 56, minTemp: -25, maxTemp: 25 },
    { component: 6, drawable: 88, minTemp: -30, maxTemp: 20 },
    { component: 4, drawable: 86, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 88, minTemp: 10, maxTemp: 65 },
    { component: 4, drawable: 102, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 103, minTemp: 10, maxTemp: 65 },
    { component: 11, drawable: 239, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 219, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 215, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 216, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 220, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 221, minTemp: -20, maxTemp: 25 },
    { component: 11, drawable: 206, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 208, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 212, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 213, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 214, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 324, minTemp: -20, maxTemp: 25 },
    { component: 6, drawable: 53, minTemp: -30, maxTemp: 25 },
    { component: 6, drawable: 54, minTemp: -25, maxTemp: 30 },
    { component: 6, drawable: 59, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 60, minTemp: -30, maxTemp: 25 },
    { component: 6, drawable: 61, minTemp: -25, maxTemp: 30 },
    { component: 6, drawable: 62, minTemp: -30, maxTemp: 25 },
    { component: 6, drawable: 63, minTemp: -10, maxTemp: 50 },
    { component: 4, drawable: 0, minTemp: -20, maxTemp: 35 },
    { component: 4, drawable: 43, minTemp: -25, maxTemp: 30 },
    { component: 4, drawable: 1, minTemp: -20, maxTemp: 35 },
    { component: 4, drawable: 3, minTemp: -2, maxTemp: 35 },
    { component: 4, drawable: 29, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 47, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 8, minTemp: -30, maxTemp: 20 },
    { component: 4, drawable: 9, minTemp: -25, maxTemp: 20 },
    { component: 4, drawable: 7, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 27, minTemp: -20, maxTemp: 30 },
    { component: 4, drawable: 5, minTemp: -10, maxTemp: 30 },
    { component: 4, drawable: 55, minTemp: -10, maxTemp: 30 },
    { component: 4, drawable: 64, minTemp: -10, maxTemp: 30 },
    { component: 4, drawable: 98, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 15, minTemp: 20, maxTemp: 50 },
    { component: 4, drawable: 100, minTemp: -25, maxTemp: 30 },
    { component: 4, drawable: 14, minTemp: 25, maxTemp: 60 },
    { component: 4, drawable: 18, minTemp: 25, maxTemp: 60 },
    { component: 4, drawable: 42, minTemp: 15, maxTemp: 55 },
    { component: 4, drawable: 62, minTemp: 15, maxTemp: 55 },
    { component: 4, drawable: 21, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 61, minTemp: 25, maxTemp: 99 },
    { component: 11, drawable: 9, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 39, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 82, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 123, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 83, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 84, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 81, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 128, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 164, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 111, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 139, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 97, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 17, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 36, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 237, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 1, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 16, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 22, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 33, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 34, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 44, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 146, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 14, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 89, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 169, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 170, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 172, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 173, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 174, minTemp: -20, maxTemp: 30 },
    { component: 11, drawable: 175, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 122, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 161, minTemp: -20, maxTemp: 30 },
    { component: 11, drawable: 163, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 159, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 160, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 162, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 88, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 127, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 6, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 37, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 166, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 157, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 233, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 3, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 141, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 7, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 86, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 171, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 279, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 257, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 281, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 282, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 334, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 335, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 345, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 350, minTemp: 15, maxTemp: 50 },
    { component: 6, drawable: 5, minTemp: 25, maxTemp: 99 },
    { component: 6, drawable: 16, minTemp: 25, maxTemp: 99 },
    { component: 6, drawable: 6, minTemp: 20, maxTemp: 99 },
    { component: 6, drawable: 8, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 23, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 9, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 35, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 65, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 66, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 74, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 78, minTemp: 20, maxTemp: 99 }
]

export const temperatureClothFemale = [
    { component: 4, drawable: 45, minTemp: -20, maxTemp: 30 },
    { component: 4, drawable: 3, minTemp: -20, maxTemp: 30 },
    { component: 4, drawable: 58, minTemp: -10, maxTemp: 30 },
    { component: 4, drawable: 66, minTemp: -10, maxTemp: 30 },
    { component: 4, drawable: 71, minTemp: -25, maxTemp: 30 },
    { component: 4, drawable: 104, minTemp: -25, maxTemp: 30 },
    { component: 4, drawable: 80, minTemp: -10, maxTemp: 35 },
    { component: 4, drawable: 84, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 101, minTemp: -25, maxTemp: 30 },
    { component: 4, drawable: 82, minTemp: 10, maxTemp: 50 },
    { component: 4, drawable: 2, minTemp: 15, maxTemp: 50 },
    { component: 4, drawable: 10, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 14, minTemp: 20, maxTemp: 60 },
    { component: 4, drawable: 16, minTemp: 20, maxTemp: 60 },
    { component: 4, drawable: 78, minTemp: 15, maxTemp: 45 },
    { component: 4, drawable: 17, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 21, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 57, minTemp: 10, maxTemp: 50 },
    { component: 4, drawable: 123, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 137, minTemp: 15, maxTemp: 60 },
    { component: 11, drawable: 0, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 1, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 3, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 10, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 11, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 16, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 17, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 18, minTemp: 25, maxTemp: 99 },
    { component: 11, drawable: 19, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 23, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 31, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 32, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 33, minTemp: 25, maxTemp: 60 },
    { component: 11, drawable: 36, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 37, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 49, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 54, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 55, minTemp: -20, maxTemp: 25 },
    { component: 11, drawable: 73, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 74, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 75, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 76, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 77, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 96, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 97, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 102, minTemp: -20, maxTemp: 30 },
    { component: 11, drawable: 105, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 106, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 107, minTemp: -20, maxTemp: 30 },
    { component: 11, drawable: 108, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 123, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 125, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 126, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 132, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 138, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 141, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 146, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 148, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 152, minTemp: -1, maxTemp: 30 },
    { component: 11, drawable: 154, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 161, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 163, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 166, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 167, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 168, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 169, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 170, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 171, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 172, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 173, minTemp: 20, maxTemp: 55 },
    { component: 11, drawable: 243, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 277, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 247, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 284, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 286, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 292, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 294, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 310, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 372, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 324, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 335, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 337, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 338, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 353, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 120, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 354, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 355, minTemp: 15, maxTemp: 40 },
    { component: 6, drawable: 5, minTemp: 25, maxTemp: 99 },
    { component: 6, drawable: 10, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 16, minTemp: 25, maxTemp: 99 },
    { component: 6, drawable: 30, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 59, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 60, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 67, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 83, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 84, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 85, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 86, minTemp: -30, maxTemp: 30 },
    { component: 4, drawable: 0, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 1, minTemp: -20, maxTemp: 35 },
    { component: 4, drawable: 4, minTemp: 10, maxTemp: 40 },
    { component: 4, drawable: 8, minTemp: 15, maxTemp: 50 },
    { component: 4, drawable: 9, minTemp: 15, maxTemp: 50 },
    { component: 4, drawable: 11, minTemp: 10, maxTemp: 40 },
    { component: 4, drawable: 12, minTemp: 15, maxTemp: 50 },
    { component: 4, drawable: 24, minTemp: 15, maxTemp: 50 },
    { component: 4, drawable: 25, minTemp: 15, maxTemp: 50 },
    { component: 4, drawable: 27, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 28, minTemp: 15, maxTemp: 50 },
    { component: 4, drawable: 31, minTemp: -5, maxTemp: 40 },
    { component: 4, drawable: 43, minTemp: 5, maxTemp: 40 },
    { component: 4, drawable: 106, minTemp: 5, maxTemp: 40 },
    { component: 4, drawable: 44, minTemp: 5, maxTemp: 40 },
    { component: 4, drawable: 112, minTemp: -15, maxTemp: 35 },
    { component: 4, drawable: 47, minTemp: 10, maxTemp: 35 },
    { component: 4, drawable: 51, minTemp: 10, maxTemp: 40 },
    { component: 4, drawable: 54, minTemp: -5, maxTemp: 40 },
    { component: 4, drawable: 138, minTemp: -5, maxTemp: 40 },
    { component: 4, drawable: 55, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 56, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 60, minTemp: 10, maxTemp: 35 },
    { component: 4, drawable: 67, minTemp: 10, maxTemp: 35 },
    { component: 4, drawable: 73, minTemp: -15, maxTemp: 35 },
    { component: 4, drawable: 74, minTemp: -5, maxTemp: 40 },
    { component: 4, drawable: 75, minTemp: -15, maxTemp: 35 },
    { component: 4, drawable: 76, minTemp: -15, maxTemp: 35 },
    { component: 4, drawable: 77, minTemp: -15, maxTemp: 35 },
    { component: 4, drawable: 81, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 83, minTemp: 15, maxTemp: 50 },
    { component: 4, drawable: 139, minTemp: 15, maxTemp: 50 },
    { component: 4, drawable: 85, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 87, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 102, minTemp: -15, maxTemp: 30 },
    { component: 4, drawable: 108, minTemp: 15, maxTemp: 50 },
    { component: 4, drawable: 134, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 135, minTemp: 15, maxTemp: 60 },
    { component: 11, drawable: 2, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 8, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 30, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 35, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 38, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 40, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 44, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 45, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 63, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 67, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 68, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 375, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 377, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 71, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 72, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 78, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 79, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 81, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 378, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 380, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 83, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 84, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 98, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 101, minTemp: 25, maxTemp: 99 },
    { component: 11, drawable: 109, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 110, minTemp: -15, maxTemp: 35 },
    { component: 11, drawable: 117, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 195, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 118, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 119, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 140, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 142, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 145, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 149, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 155, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 162, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 164, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 165, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 179, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 187, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 189, minTemp: -15, maxTemp: 35 },
    { component: 11, drawable: 191, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 192, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 193, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 194, minTemp: -15, maxTemp: 25 },
    { component: 11, drawable: 196, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 198, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 200, minTemp: -5, maxTemp: 25 },
    { component: 11, drawable: 202, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 370, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 207, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 209, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 227, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 147, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 150, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 151, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 233, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 234, minTemp: -20, maxTemp: 30 },
    { component: 11, drawable: 235, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 240, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 245, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 249, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 252, minTemp: -25, maxTemp: 30 },
    { component: 11, drawable: 253, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 255, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 256, minTemp: -25, maxTemp: 30 },
    { component: 11, drawable: 259, minTemp: -25, maxTemp: 30 },
    { component: 11, drawable: 264, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 270, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 275, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 267, minTemp: -5, maxTemp: 25 },
    { component: 11, drawable: 374, minTemp: -5, maxTemp: 25 },
    { component: 11, drawable: 376, minTemp: -5, maxTemp: 25 },
    { component: 11, drawable: 268, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 271, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 278, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 279, minTemp: 25, maxTemp: 99 },
    { component: 11, drawable: 280, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 301, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 307, minTemp: -25, maxTemp: 30 },
    { component: 11, drawable: 309, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 314, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 316, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 318, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 319, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 320, minTemp: -25, maxTemp: 25 },
    { component: 11, drawable: 344, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 345, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 347, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 361, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 363, minTemp: -5, maxTemp: 35 },
    { component: 6, drawable: 1, minTemp: 5, maxTemp: 50 },
    { component: 6, drawable: 2, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 3, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 4, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 11, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 31, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 32, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 33, minTemp: -20, maxTemp: 50 },
    { component: 6, drawable: 47, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 49, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 50, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 58, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 79, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 80, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 96, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 97, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 73, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 74, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 75, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 76, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 77, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 81, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 98, minTemp: -10, maxTemp: 50 },
    { component: 4, drawable: 6, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 7, minTemp: 15, maxTemp: 40 },
    { component: 4, drawable: 18, minTemp: 15, maxTemp: 40 },
    { component: 4, drawable: 19, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 20, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 23, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 26, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 34, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 36, minTemp: 15, maxTemp: 40 },
    { component: 4, drawable: 37, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 41, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 50, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 52, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 53, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 62, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 63, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 64, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 124, minTemp: -15, maxTemp: 40 },
    { component: 4, drawable: 133, minTemp: -15, maxTemp: 40 },
    { component: 11, drawable: 6, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 7, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 9, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 13, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 20, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 21, minTemp: 20, maxTemp: 50 },
    { component: 11, drawable: 22, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 24, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 25, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 26, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 28, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 39, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 51, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 52, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 53, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 57, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 64, minTemp: -25, maxTemp: 30 },
    { component: 11, drawable: 65, minTemp: -30, maxTemp: 25 },
    { component: 11, drawable: 248, minTemp: -30, maxTemp: 25 },
    { component: 11, drawable: 135, minTemp: -20, maxTemp: 30 },
    { component: 11, drawable: 133, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 139, minTemp: -25, maxTemp: 30 },
    { component: 11, drawable: 66, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 69, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 70, minTemp: -25, maxTemp: 30 },
    { component: 11, drawable: 90, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 92, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 94, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 99, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 143, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 111, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 112, minTemp: 20, maxTemp: 50 },
    { component: 11, drawable: 113, minTemp: 20, maxTemp: 50 },
    { component: 11, drawable: 114, minTemp: 20, maxTemp: 50 },
    { component: 11, drawable: 115, minTemp: 20, maxTemp: 50 },
    { component: 11, drawable: 116, minTemp: 20, maxTemp: 50 },
    { component: 11, drawable: 137, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 185, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 273, minTemp: -10, maxTemp: 35 },
    { component: 11, drawable: 283, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 305, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 322, minTemp: 20, maxTemp: 50 },
    { component: 11, drawable: 322, minTemp: 20, maxTemp: 50 },
    { component: 11, drawable: 339, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 359, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 360, minTemp: 5, maxTemp: 35 },
    { component: 11, drawable: 364, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 365, minTemp: 10, maxTemp: 45 },
    { component: 11, drawable: 333, minTemp: -5, maxTemp: 35 },
    { component: 11, drawable: 367, minTemp: -5, maxTemp: 35 },
    { component: 6, drawable: 0, minTemp: 10, maxTemp: 50 },
    { component: 6, drawable: 6, minTemp: 10, maxTemp: 50 },
    { component: 6, drawable: 7, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 8, minTemp: -10, maxTemp: 30 },
    { component: 6, drawable: 9, minTemp: -25, maxTemp: 30 },
    { component: 6, drawable: 13, minTemp: 10, maxTemp: 50 },
    { component: 6, drawable: 14, minTemp: 10, maxTemp: 50 },
    { component: 6, drawable: 15, minTemp: 20, maxTemp: 60 },
    { component: 6, drawable: 18, minTemp: 5, maxTemp: 50 },
    { component: 6, drawable: 19, minTemp: 10, maxTemp: 50 },
    { component: 6, drawable: 20, minTemp: 10, maxTemp: 50 },
    { component: 6, drawable: 21, minTemp: -25, maxTemp: 30 },
    { component: 6, drawable: 22, minTemp: -25, maxTemp: 30 },
    { component: 6, drawable: 23, minTemp: 10, maxTemp: 50 },
    { component: 6, drawable: 29, minTemp: -15, maxTemp: 50 },
    { component: 6, drawable: 37, minTemp: 10, maxTemp: 50 },
    { component: 6, drawable: 38, minTemp: -25, maxTemp: 30 },
    { component: 6, drawable: 39, minTemp: -15, maxTemp: 40 },
    { component: 6, drawable: 41, minTemp: 5, maxTemp: 50 },
    { component: 6, drawable: 42, minTemp: 10, maxTemp: 50 },
    { component: 6, drawable: 43, minTemp: -20, maxTemp: 30 },
    { component: 6, drawable: 44, minTemp: -15, maxTemp: 30 },
    { component: 6, drawable: 45, minTemp: -25, maxTemp: 30 },
    { component: 6, drawable: 46, minTemp: -15, maxTemp: 40 },
    { component: 6, drawable: 51, minTemp: -25, maxTemp: 25 },
    { component: 6, drawable: 52, minTemp: -20, maxTemp: 30 },
    { component: 6, drawable: 53, minTemp: -30, maxTemp: 20 },
    { component: 6, drawable: 56, minTemp: -25, maxTemp: 25 },
    { component: 6, drawable: 55, minTemp: -20, maxTemp: 30 },
    { component: 6, drawable: 92, minTemp: -30, maxTemp: 20 },
    { component: 4, drawable: 30, minTemp: 5, maxTemp: 35 },
    { component: 4, drawable: 89, minTemp: -30, maxTemp: 30 },
    { component: 4, drawable: 91, minTemp: 10, maxTemp: 65 },
    { component: 4, drawable: 109, minTemp: -25, maxTemp: 25 },
    { component: 4, drawable: 110, minTemp: 10, maxTemp: 65 },
    { component: 11, drawable: 43, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 210, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 212, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 216, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 217, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 218, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 219, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 220, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 221, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 222, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 223, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 225, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 226, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 229, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 230, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 231, minTemp: -20, maxTemp: 25 },
    { component: 11, drawable: 336, minTemp: -20, maxTemp: 25 },
    { component: 6, drawable: 54, minTemp: -30, maxTemp: 25 },
    { component: 6, drawable: 55, minTemp: -25, maxTemp: 30 },
    { component: 6, drawable: 62, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 63, minTemp: -30, maxTemp: 25 },
    { component: 6, drawable: 64, minTemp: -25, maxTemp: 30 },
    { component: 6, drawable: 65, minTemp: -30, maxTemp: 25 },
    { component: 6, drawable: 66, minTemp: -10, maxTemp: 50 },
    { component: 4, drawable: 45, minTemp: -20, maxTemp: 30 },
    { component: 4, drawable: 3, minTemp: -20, maxTemp: 30 },
    { component: 4, drawable: 58, minTemp: -10, maxTemp: 30 },
    { component: 4, drawable: 66, minTemp: -10, maxTemp: 30 },
    { component: 4, drawable: 71, minTemp: -25, maxTemp: 30 },
    { component: 4, drawable: 80, minTemp: -10, maxTemp: 35 },
    { component: 4, drawable: 84, minTemp: -10, maxTemp: 40 },
    { component: 4, drawable: 101, minTemp: -25, maxTemp: 30 },
    { component: 4, drawable: 82, minTemp: 10, maxTemp: 50 },
    { component: 4, drawable: 2, minTemp: 15, maxTemp: 50 },
    { component: 4, drawable: 10, minTemp: 15, maxTemp: 60 },
    { component: 4, drawable: 14, minTemp: 20, maxTemp: 60 },
    { component: 4, drawable: 16, minTemp: 20, maxTemp: 60 },
    { component: 4, drawable: 78, minTemp: 15, maxTemp: 45 },
    { component: 4, drawable: 17, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 21, minTemp: 25, maxTemp: 99 },
    { component: 4, drawable: 57, minTemp: 10, maxTemp: 50 },
    { component: 4, drawable: 107, minTemp: 15, maxTemp: 60 },
    { component: 11, drawable: 0, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 1, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 3, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 10, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 11, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 14, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 16, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 18, minTemp: 25, maxTemp: 99 },
    { component: 11, drawable: 19, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 23, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 31, minTemp: 10, maxTemp: 35 },
    { component: 11, drawable: 32, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 33, minTemp: 25, maxTemp: 60 },
    { component: 11, drawable: 36, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 37, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 49, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 73, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 74, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 75, minTemp: 10, maxTemp: 40 },
    { component: 11, drawable: 76, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 105, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 106, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 125, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 126, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 132, minTemp: 15, maxTemp: 45 },
    { component: 11, drawable: 138, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 141, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 148, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 154, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 156, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 157, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 158, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 159, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 160, minTemp: -15, maxTemp: 35 },
    { component: 11, drawable: 161, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 163, minTemp: -15, maxTemp: 35 },
    { component: 11, drawable: 166, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 167, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 168, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 169, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 170, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 171, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 172, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 173, minTemp: 20, maxTemp: 55 },
    { component: 11, drawable: 174, minTemp: -10, maxTemp: 30 },
    { component: 11, drawable: 175, minTemp: 15, maxTemp: 40 },
    { component: 11, drawable: 176, minTemp: -15, maxTemp: 30 },
    { component: 11, drawable: 177, minTemp: 15, maxTemp: 35 },
    { component: 11, drawable: 243, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 247, minTemp: 20, maxTemp: 60 },
    { component: 11, drawable: 266, minTemp: -5, maxTemp: 30 },
    { component: 11, drawable: 286, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 292, minTemp: 5, maxTemp: 30 },
    { component: 11, drawable: 294, minTemp: 10, maxTemp: 30 },
    { component: 11, drawable: 295, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 349, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 350, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 368, minTemp: 15, maxTemp: 50 },
    { component: 11, drawable: 369, minTemp: 15, maxTemp: 50 },
    { component: 6, drawable: 5, minTemp: 25, maxTemp: 99 },
    { component: 6, drawable: 10, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 16, minTemp: 25, maxTemp: 99 },
    { component: 6, drawable: 68, minTemp: -30, maxTemp: 30 },
    { component: 6, drawable: 69, minTemp: -10, maxTemp: 50 },
    { component: 6, drawable: 78, minTemp: -10, maxTemp: 50 }
]
