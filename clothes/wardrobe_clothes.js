/*
===== ITEMS =====
{
    ID: { 
        id: Number, 
        component: Number(optional),
        isProp: Number(optional), 
        drawable: Number, 
        textures: { list: Number[], range: Number[from, to], except: Number[] }
    }
}
=================

=== FRACTIONS ===
{
    ID: {
        categories: {
            CATEGORY: {
                genders: {
                    GENDER: {
                       items: { list: Number[], range: Number[from, to], except: Number[] }
                    }
                }
            }
        }
    }
}
=================

CATEGORIES:

masks
head
tops
undershirts
legs
shoes
accessories
decals
glasses
gloves
bracelets

=================

FRACTIONS:

lspd
ems
dep
sang
gov
news
fib
ballas
vagos
families
bloods
marabunta

=================
*/


module.exports = {

    types: {
        'fraction': 1
    },

    config: {
        maxKits: 30,
        chooseKitTimeout: 1000
    },

    fractionNames: {
        1: 'lspd',
        2: 'ems',
        3: 'dep',
        4: 'sang',
        5: 'gov',
        6: 'news',
        7: 'fib',
        8: 'ballas',
        9: 'vagos',
        10: 'families',
        11: 'bloods',
        12: 'marabunta'
    },

   items: {
        1: { drawable: 36, textures: { list: [0] } },
        2: { drawable: 38, textures: { list: [0] } },
        3: { drawable: 46, textures: { list: [0] } },
        4: { drawable: 73, textures: { list: [0] } },
        5: { drawable: 121, textures: { list: [0] } },
        6: { drawable: 122, textures: { list: [0, 1] } },
        7: { drawable: 166, textures: { list: [2] } },
        8: { drawable: 175, textures: { list: [0] } },
        9: { drawable: 2000, textures: { list: [0] } },
        10: { drawable: 2001, textures: { list: [0] } },
        11: { drawable: 2052, textures: { range: [0, 8] } },
        12: { drawable: 2053, textures: { list: [0] } },
        13: { drawable: 2129, textures: { range: [0, 3] } },
        14: { drawable: 2130, textures: { list: [0, 3] } },
        15: { drawable: 190, textures: { list: [0] } },
        16: { drawable: 2116, textures: { range: [0, 3] } },
        17: { drawable: 2117, textures: { list: [0, 3] } },
        18: { drawable: 2118, textures: { list: [2] } },
        19: { drawable: 0, textures: { list: [1, 5] } },
        20: { drawable: 19, textures: { list: [0] } },
        21: { drawable: 39, textures: { list: [0] } },
        22: { drawable: 58, textures: { list: [2] } },
        23: { drawable: 59, textures: { list: [5, 8] } },
        24: { drawable: 116, textures: { list: [0] } },
        25: { drawable: 117, textures: { list: [0] } },
        26: { drawable: 120, textures: { list: [0] } },
        27: { drawable: 123, textures: { list: [0, 15] } },
        28: { drawable: 124, textures: { list: [0, 15] } },
        29: { drawable: 125, textures: { list: [0, 5, 18] } },
        30: { drawable: 126, textures: { list: [0, 5, 18] } },
        31: { drawable: 141, textures: { list: [0] } },
        32: { drawable: 142, textures: { list: [0] } },
        33: { drawable: 143, textures: { list: [0] } },
        34: { drawable: 147, textures: { list: [0] } },
        35: { drawable: 148, textures: { list: [0] } },
        36: { drawable: 150, textures: { list: [0] } },
        37: { drawable: 2000, textures: { list: [1] } },
        38: { drawable: 2004, textures: { list: [0] } },
        39: { drawable: 2005, textures: { list: [0, 1] } },
        40: { drawable: 2006, textures: { list: [0, 1] } },
        41: { drawable: 2051, textures: { list: [1, 2] } },
        42: { drawable: 2052, textures: { list: [0, 2] } },
        43: { drawable: 2056, textures: { range: [0, 10], except: [1, 2, 3, 4, 5] } },
        44: { drawable: 2057, textures: { list: [0] } },
        45: { drawable: 2058, textures: { range: [0, 10], except: [1, 2, 3, 4, 5] } },
        46: { drawable: 2061, textures: { list: [0, 2, 6, 8] } },
        47: { drawable: 2062, textures: { list: [0, 2, 6, 8] } },
        48: { drawable: 2063, textures: { list: [0, 2, 6, 8] } },
        49: { drawable: 2064, textures: { list: [1] } },
        50: { drawable: 2065, textures: { list: [0, 2, 6, 8] } },
        51: { drawable: 2066, textures: { list: [0, 2, 6, 8] } },
        52: { drawable: 2067, textures: { range: [0, 3] } },
        53: { drawable: 2068, textures: { range: [0, 3] } },
        54: { drawable: 0, textures: { list: [5] } },
        55: { drawable: 45, textures: { list: [0] } },
        56: { drawable: 122, textures: { list: [15] } },
        57: { drawable: 123, textures: { list: [15] } },
        58: { drawable: 124, textures: { list: [18] } },
        59: { drawable: 125, textures: { list: [18] } },
        60: { drawable: 2001, textures: { list: [1] } },
        61: { drawable: 2005, textures: { list: [1] } },
        62: { drawable: 2006, textures: { list: [1] } },
        63: { drawable: 2060, textures: { list: [0, 6] } },
        64: { drawable: 2065, textures: { list: [2, 8] } },
        65: { drawable: 2067, textures: { list: [0, 6] } },
        66: { drawable: 2068, textures: { list: [0] } },
        67: { drawable: 2069, textures: { list: [2, 8] } },
        68: { drawable: 2071, textures: { list: [2, 8] } },
        69: { drawable: 2072, textures: { list: [2, 8] } },
        70: { drawable: 2073, textures: { list: [2, 8] } },
        71: { drawable: 2074, textures: { list: [1] } },
        72: { drawable: 2075, textures: { range: [0, 3] } },
        73: { drawable: 2076, textures: { range: [0, 3] } },
        74: { drawable: 0, textures: { list: [0, 8] } },
        75: { drawable: 13, textures: { list: [0, 2, 3] } },
        76: { drawable: 22, textures: { list: [0, 1] } },
        77: { drawable: 26, textures: { list: [0] } },
        78: { drawable: 47, textures: { list: [0] } },
        79: { drawable: 48, textures: { list: [0] } },
        80: { drawable: 54, textures: { list: [0] } },
        81: { drawable: 55, textures: { list: [0] } },
        82: { drawable: 65, textures: { list: [1, 3] } },
        83: { drawable: 111, textures: { list: [4] } },
        84: { drawable: 139, textures: { list: [4] } },
        85: { drawable: 146, textures: { list: [0, 6, 8] } },
        86: { drawable: 186, textures: { list: [6] } },
        87: { drawable: 241, textures: { list: [1, 2] } },
        88: { drawable: 242, textures: { list: [1, 2] } },
        89: { drawable: 294, textures: { list: [7, 8] } },
        90: { drawable: 295, textures: { list: [7, 8] } },
        91: { drawable: 316, textures: { list: [0, 6] } },
        92: { drawable: 317, textures: { list: [0, 6] } },
        93: { drawable: 318, textures: { list: [0, 6] } },
        94: { drawable: 319, textures: { list: [0, 6] } },
        95: { drawable: 321, textures: { list: [0] } },
        96: { drawable: 322, textures: { list: [0] } },
        97: { drawable: 348, textures: { list: [0, 2, 5, 10] } },
        98: { drawable: 349, textures: { list: [0, 2, 5, 10] } },
        99: { drawable: 351, textures: { list: [0, 2] } },
        100: { drawable: 2000, textures: { list: [0, 2, 3, 16, 17] } },
        101: { drawable: 2001, textures: { list: [3, 7, 8] } },
        102: { drawable: 2002, textures: { list: [3, 7, 8] } },
        103: { drawable: 2003, textures: { list: [0, 1] } },
        104: { drawable: 2004, textures: { list: [0, 1] } },
        105: { drawable: 2007, textures: { list: [0, 2, 13, 14, 15] } },
        106: { drawable: 2008, textures: { list: [0, 3] } },
        107: { drawable: 2009, textures: { list: [0, 4] } },
        108: { drawable: 2010, textures: { list: [0] } },
        109: { drawable: 2011, textures: { list: [0] } },
        110: { drawable: 2016, textures: { list: [0] } },
        111: { drawable: 2032, textures: { list: [1] } },
        112: { drawable: 2033, textures: { list: [1] } },
        113: { drawable: 2097, textures: { list: [0] } },
        114: { drawable: 2101, textures: { range: [0, 5] } },
        115: { drawable: 2102, textures: { range: [0, 5] } },
        116: { drawable: 2104, textures: { list: [0] } },
        117: { drawable: 2105, textures: { list: [0] } },
        118: { drawable: 2106, textures: { list: [0] } },
        119: { drawable: 2107, textures: { list: [0] } },
        120: { drawable: 2110, textures: { list: [0] } },
        121: { drawable: 2284, textures: { range: [0, 7] } },
        122: { drawable: 2286, textures: { list: [0, 1, 2] } },
        123: { drawable: 2287, textures: { list: [2] } },
        124: { drawable: 2288, textures: { list: [0, 1] } },
        125: { drawable: 2290, textures: { range: [0, 3] } },
        126: { drawable: 2291, textures: { range: [0, 6], except: [2] } },
        127: { drawable: 2292, textures: { range: [0, 3] } },
        128: { drawable: 2293, textures: { range: [0, 14], except: [1, 2, 4, 5, 6, 7, 8] } },
        129: { drawable: 2294, textures: { range: [0, 5] } },
        130: { drawable: 2295, textures: { range: [0, 5] } },
        131: { drawable: 2298, textures: { list: [0, 4] } },
        132: { drawable: 2299, textures: { list: [0, 4] } },
        133: { drawable: 2300, textures: { list: [0, 1, 3, 4, 6, 11, 14, 15, 19, 21, 25] } },
        134: { drawable: 2301, textures: { list: [0] } },
        135: { drawable: 2302, textures: { list: [0] } },
        136: { drawable: 2305, textures: { list: [0] } },
        137: { drawable: 2307, textures: { list: [0, 1] } },
        138: { drawable: 2308, textures: { list: [0] } },
        139: { drawable: 2310, textures: { list: [0] } },
        140: { drawable: 2311, textures: { list: [5, 6, 7] } },
        141: { drawable: 2312, textures: { range: [0, 7] } },
        142: { drawable: 2313, textures: { list: [0, 1, 3, 4, 6, 11, 14, 15, 19, 21, 25] } },
        143: { drawable: 2314, textures: { list: [0, 1, 3, 4, 6, 11, 14, 15, 19, 21, 25] } },
        144: { drawable: 2315, textures: { list: [0] } },
        145: { drawable: 7, textures: { list: [0, 1, 8] } },
        146: { drawable: 9, textures: { list: [0, 1] } },
        147: { drawable: 27, textures: { list: [0] } },
        148: { drawable: 57, textures: { range: [0, 7], except: [3, 4, 6] } },
        149: { drawable: 58, textures: { range: [0, 7], except: [3, 4, 6] } },
        150: { drawable: 103, textures: { list: [3, 4] } },
        151: { drawable: 136, textures: { list: [3, 4] } },
        152: { drawable: 188, textures: { list: [0, 6] } },
        153: { drawable: 249, textures: { list: [1, 4] } },
        154: { drawable: 250, textures: { list: [1, 4] } },
        155: { drawable: 305, textures: { list: [0, 4, 7, 8] } },
        156: { drawable: 306, textures: { list: [0, 4, 7, 8] } },
        157: { drawable: 327, textures: { list: [0] } },
        158: { drawable: 328, textures: { list: [0] } },
        159: { drawable: 329, textures: { list: [0] } },
        160: { drawable: 330, textures: { list: [0] } },
        161: { drawable: 332, textures: { list: [0] } },
        162: { drawable: 333, textures: { list: [0] } },
        163: { drawable: 339, textures: { list: [0, 2, 5, 7] } },
        164: { drawable: 340, textures: { list: [0, 2, 5, 7] } },
        165: { drawable: 366, textures: { list: [2, 5] } },
        166: { drawable: 367, textures: { list: [2, 5] } },
        167: { drawable: 2000, textures: { list: [0, 1] } },
        168: { drawable: 2003, textures: { list: [0, 1, 5] } },
        169: { drawable: 2004, textures: { list: [0, 1, 5] } },
        170: { drawable: 2005, textures: { list: [0, 1] } },
        171: { drawable: 2006, textures: { list: [0, 3] } },
        172: { drawable: 2007, textures: { list: [0, 4] } },
        173: { drawable: 2008, textures: { list: [0] } },
        174: { drawable: 2009, textures: { list: [0] } },
        175: { drawable: 2010, textures: { list: [0, 1] } },
        176: { drawable: 2014, textures: { list: [0] } },
        177: { drawable: 2085, textures: { list: [0] } },
        178: { drawable: 2089, textures: { range: [0, 5] } },
        179: { drawable: 2090, textures: { range: [0, 5] } },
        180: { drawable: 2091, textures: { list: [0] } },
        181: { drawable: 2092, textures: { list: [0] } },
        182: { drawable: 2093, textures: { list: [0] } },
        183: { drawable: 2094, textures: { list: [0] } },
        184: { drawable: 2095, textures: { list: [0] } },
        185: { drawable: 2282, textures: { list: [0, 1, 12] } },
        186: { drawable: 2283, textures: { range: [0, 3] } },
        187: { drawable: 2284, textures: { range: [0, 5] } },
        188: { drawable: 2285, textures: { range: [0, 5] } },
        189: { drawable: 2289, textures: { list: [0] } },
        190: { drawable: 2291, textures: { list: [0, 4] } },
        191: { drawable: 2292, textures: { range: [0, 7], except: [1, 2, 3] } },
        192: { drawable: 2293, textures: { range: [0, 7], except: [1, 2, 3] } },
        193: { drawable: 2295, textures: { range: [0, 8], except: [1, 2, 3] } },
        194: { drawable: 2297, textures: { list: [0] } },
        195: { drawable: 2298, textures: { list: [0, 1] } },
        196: { drawable: 2299, textures: { list: [0, 1] } },
        197: { drawable: 2300, textures: { list: [0, 5] } },
        198: { drawable: 2302, textures: { range: [0, 7] } },
        199: { drawable: 2303, textures: { list: [0, 1] } },
        200: { drawable: 2304, textures: { list: [0] } },
        201: { drawable: 2305, textures: { list: [6, 7] } },
        202: { drawable: 2307, textures: { range: [0, 6] } },
        203: { drawable: 2308, textures: { list: [0, 3, 4] } },
        204: { drawable: 2309, textures: { list: [0] } },
        205: { drawable: 10, textures: { list: [0] } },
        206: { drawable: 11, textures: { list: [0] } },
        207: { drawable: 13, textures: { list: [0, 3] } },
        208: { drawable: 21, textures: { list: [0, 4] } },
        209: { drawable: 23, textures: { list: [0, 1] } },
        210: { drawable: 24, textures: { list: [0, 1] } },
        211: { drawable: 25, textures: { list: [0, 4, 9, 12] } },
        212: { drawable: 26, textures: { list: [0, 4, 9, 12] } },
        213: { drawable: 27, textures: { list: [0] } },
        214: { drawable: 28, textures: { list: [0] } },
        215: { drawable: 31, textures: { list: [0, 3] } },
        216: { drawable: 32, textures: { list: [0, 3] } },
        217: { drawable: 58, textures: { list: [0] } },
        218: { drawable: 60, textures: { list: [0] } },
        219: { drawable: 63, textures: { range: [0, 4], except: [1] } },
        220: { drawable: 64, textures: { range: [0, 4], except: [1] } },
        221: { drawable: 69, textures: { list: [0, 1] } },
        222: { drawable: 71, textures: { list: [3, 4] } },
        223: { drawable: 72, textures: { list: [3, 4] } },
        224: { drawable: 74, textures: { list: [3, 4] } },
        225: { drawable: 75, textures: { list: [3, 4] } },
        226: { drawable: 76, textures: { list: [0, 6] } },
        227: { drawable: 77, textures: { list: [0, 6] } },
        228: { drawable: 90, textures: { list: [0] } },
        229: { drawable: 94, textures: { list: [0] } },
        230: { drawable: 95, textures: { list: [0] } },
        231: { drawable: 96, textures: { list: [0] } },
        232: { drawable: 97, textures: { list: [6] } },
        233: { drawable: 115, textures: { list: [0, 1] } },
        234: { drawable: 116, textures: { list: [0, 1] } },
        235: { drawable: 122, textures: { list: [0] } },
        236: { drawable: 129, textures: { list: [0] } },
        237: { drawable: 130, textures: { list: [0] } },
        238: { drawable: 146, textures: { list: [0, 5, 8, 10] } },
        239: { drawable: 147, textures: { list: [0, 5, 8, 10] } },
        240: { drawable: 148, textures: { list: [0, 5, 8, 10] } },
        241: { drawable: 149, textures: { list: [0, 5, 8, 10] } },
        242: { drawable: 150, textures: { list: [0, 1, 10] } },
        243: { drawable: 153, textures: { list: [0] } },
        244: { drawable: 154, textures: { list: [0] } },
        245: { drawable: 155, textures: { list: [0] } },
        246: { drawable: 159, textures: { list: [0, 4] } },
        247: { drawable: 160, textures: { list: [0, 4] } },
        248: { drawable: 161, textures: { list: [0, 4] } },
        249: { drawable: 162, textures: { list: [0, 4] } },
        250: { drawable: 2000, textures: { list: [0, 8, 13] } },
        251: { drawable: 2001, textures: { list: [0] } },
        252: { drawable: 2002, textures: { list: [0, 2] } },
        253: { drawable: 2003, textures: { list: [0] } },
        254: { drawable: 2004, textures: { list: [0] } },
        255: { drawable: 2005, textures: { list: [1, 2] } },
        256: { drawable: 2006, textures: { list: [0] } },
        257: { drawable: 2007, textures: { list: [0] } },
        258: { drawable: 2008, textures: { list: [0] } },
        259: { drawable: 2009, textures: { list: [0, 2] } },
        260: { drawable: 2010, textures: { list: [0] } },
        261: { drawable: 2011, textures: { list: [0] } },
        262: { drawable: 2012, textures: { list: [0] } },
        263: { drawable: 2016, textures: { list: [0] } },
        264: { drawable: 2017, textures: { list: [0] } },
        265: { drawable: 2018, textures: { list: [0] } },
        266: { drawable: 2019, textures: { list: [0] } },
        267: { drawable: 2020, textures: { list: [0] } },
        268: { drawable: 2021, textures: { list: [0] } },
        269: { drawable: 2023, textures: { list: [0] } },
        270: { drawable: 2024, textures: { list: [0] } },
        271: { drawable: 2025, textures: { list: [0] } },
        272: { drawable: 2026, textures: { list: [0] } },
        273: { drawable: 2027, textures: { list: [0] } },
        274: { drawable: 2028, textures: { list: [0] } },
        275: { drawable: 2029, textures: { list: [0] } },
        276: { drawable: 2031, textures: { list: [0] } },
        277: { drawable: 2032, textures: { list: [0] } },
        278: { drawable: 2034, textures: { list: [0, 1] } },
        279: { drawable: 2036, textures: { list: [0] } },
        280: { drawable: 2037, textures: { list: [0] } },
        281: { drawable: 2038, textures: { list: [0] } },
        282: { drawable: 2040, textures: { list: [0] } },
        283: { drawable: 2041, textures: { list: [0] } },
        284: { drawable: 2045, textures: { list: [0] } },
        285: { drawable: 2046, textures: { list: [0] } },
        286: { drawable: 2047, textures: { list: [0, 2] } },
        287: { drawable: 2048, textures: { list: [0, 2, 3] } },
        288: { drawable: 2049, textures: { list: [0, 2] } },
        289: { drawable: 2051, textures: { list: [0] } },
        290: { drawable: 2052, textures: { list: [0] } },
        291: { drawable: 2053, textures: { list: [3, 9, 13, 15] } },
        292: { drawable: 2054, textures: { list: [3, 9, 13, 15] } },
        293: { drawable: 2055, textures: { list: [0, 8, 13] } },
        294: { drawable: 2058, textures: { list: [0] } },
        295: { drawable: 2059, textures: { list: [0] } },
        296: { drawable: 2062, textures: { list: [0, 2] } },
        297: { drawable: 2063, textures: { list: [0, 2] } },
        298: { drawable: 2065, textures: { range: [0, 7], except: [5] } },
        299: { drawable: 2066, textures: { list: [0] } },
        300: { drawable: 2067, textures: { list: [0] } },
        301: { drawable: 2068, textures: { list: [0, 2] } },
        302: { drawable: 2069, textures: { list: [0, 1, 12, 16] } },
        303: { drawable: 2070, textures: { list: [0] } },
        304: { drawable: 2071, textures: { range: [0, 4], except: [2] } },
        305: { drawable: 2072, textures: { range: [0, 4], except: [2] } },
        306: { drawable: 2073, textures: { list: [0] } },
        307: { drawable: 2074, textures: { list: [0] } },
        308: { drawable: 2075, textures: { list: [0] } },
        309: { drawable: 2076, textures: { list: [0] } },
        310: { drawable: 2078, textures: { range: [0, 5] } },
        311: { drawable: 2082, textures: { list: [0, 8, 13] } },
        312: { drawable: 2083, textures: { range: [0, 6], except: [2, 5] } },
        313: { drawable: 2084, textures: { range: [0, 6], except: [2, 5] } },
        314: { drawable: 2085, textures: { list: [0] } },
        315: { drawable: 2092, textures: { list: [0] } },
        316: { drawable: 2093, textures: { list: [0] } },
        317: { drawable: 2094, textures: { list: [0, 1, 2] } },
        318: { drawable: 2096, textures: { list: [0] } },
        319: { drawable: 2097, textures: { list: [0] } },
        320: { drawable: 2099, textures: { range: [0, 9] } },
        321: { drawable: 2101, textures: { list: [0] } },
        322: { drawable: 2102, textures: { list: [0] } },
        323: { drawable: 2104, textures: { list: [0] } },
        324: { drawable: 2108, textures: { list: [2] } },
        325: { drawable: 2109, textures: { list: [0, 1, 2] } },
        326: { drawable: 2110, textures: { list: [2] } },
        327: { drawable: 2119, textures: { range: [0, 9] } },
        328: { drawable: 2124, textures: { range: [0, 4] } },
        329: { drawable: 2126, textures: { range: [0, 9] } },
        330: { drawable: 38, textures: { list: [0, 2] } },
        331: { drawable: 39, textures: { list: [0, 2] } },
        332: { drawable: 40, textures: { list: [0, 2, 5, 8] } },
        333: { drawable: 41, textures: { list: [0, 2, 5, 8] } },
        334: { drawable: 64, textures: { list: [0] } },
        335: { drawable: 67, textures: { list: [0, 3, 4] } },
        336: { drawable: 77, textures: { list: [3, 4] } },
        337: { drawable: 105, textures: { list: [3] } },
        338: { drawable: 160, textures: { list: [0] } },
        339: { drawable: 189, textures: { list: [0] } },
        340: { drawable: 190, textures: { list: [0] } },
        341: { drawable: 191, textures: { list: [0] } },
        342: { drawable: 195, textures: { list: [0, 4] } },
        343: { drawable: 196, textures: { list: [0, 4] } },
        344: { drawable: 197, textures: { list: [0, 4] } },
        345: { drawable: 198, textures: { list: [0, 4] } },
        346: { drawable: 216, textures: { list: [0, 3, 4, 11] } },
        347: { drawable: 217, textures: { list: [0, 3, 4, 11] } },
        348: { drawable: 2000, textures: { list: [0] } },
        349: { drawable: 2002, textures: { list: [0] } },
        350: { drawable: 2003, textures: { list: [0, 1, 3] } },
        351: { drawable: 2004, textures: { list: [1, 2] } },
        352: { drawable: 2005, textures: { list: [0] } },
        353: { drawable: 2008, textures: { range: [0, 4] } },
        354: { drawable: 2013, textures: { range: [0, 9] } },
        355: { drawable: 2015, textures: { list: [0] } },
        356: { drawable: 2022, textures: { list: [0] } },
        357: { drawable: 2030, textures: { list: [0] } },
        358: { drawable: 2033, textures: { list: [0, 1] } },
        359: { drawable: 2035, textures: { list: [0] } },
        360: { drawable: 2039, textures: { list: [0] } },
        361: { drawable: 2043, textures: { list: [0] } },
        362: { drawable: 2044, textures: { list: [1, 2] } },
        363: { drawable: 2047, textures: { list: [0] } },
        364: { drawable: 2048, textures: { list: [0] } },
        365: { drawable: 2050, textures: { list: [0] } },
        366: { drawable: 2051, textures: { list: [0, 1] } },
        367: { drawable: 2053, textures: { list: [0] } },
        368: { drawable: 2054, textures: { list: [0, 2] } },
        369: { drawable: 2055, textures: { range: [0, 7], except: [5] } },
        370: { drawable: 2056, textures: { list: [0, 2, 15, 16] } },
        371: { drawable: 2058, textures: { range: [0, 5] } },
        372: { drawable: 2060, textures: { list: [0] } },
        373: { drawable: 2061, textures: { list: [0] } },
        374: { drawable: 2062, textures: { list: [0] } },
        375: { drawable: 2063, textures: { list: [0] } },
        376: { drawable: 2064, textures: { list: [0] } },
        377: { drawable: 2065, textures: { list: [0] } },
        378: { drawable: 2067, textures: { list: [0, 3] } },
        379: { drawable: 2068, textures: { list: [0] } },
        380: { drawable: 2078, textures: { list: [0] } },
        381: { drawable: 2079, textures: { list: [0, 1, 2] } },
        382: { drawable: 2082, textures: { list: [0] } },
        383: { drawable: 2085, textures: { range: [0, 9] } },
        384: { drawable: 2086, textures: { list: [0] } },
        385: { drawable: 2087, textures: { list: [0] } },
        386: { drawable: 2088, textures: { list: [0] } },
        387: { drawable: 2089, textures: { list: [0] } },
        388: { drawable: 2090, textures: { list: [0] } },
        389: { drawable: 2095, textures: { list: [0, 1, 2] } },
        390: { drawable: 2096, textures: { list: [2] } },
        391: { drawable: 2097, textures: { list: [2] } },
        392: { drawable: 2101, textures: { list: [0, 1] } },
        393: { drawable: 2103, textures: { range: [0, 9] } },
        394: { drawable: 2107, textures: { range: [0, 5] } },
        395: { drawable: 9, textures: { list: [3, 7] } },
        396: { drawable: 10, textures: { list: [0, 2] } },
        397: { drawable: 13, textures: { list: [0, 2] } },
        398: { drawable: 20, textures: { list: [0] } },
        399: { drawable: 24, textures: { list: [0, 2, 5] } },
        400: { drawable: 25, textures: { list: [0, 2, 5] } },
        401: { drawable: 30, textures: { list: [0] } },
        402: { drawable: 31, textures: { list: [0] } },
        403: { drawable: 33, textures: { list: [0] } },
        404: { drawable: 35, textures: { list: [0] } },
        405: { drawable: 37, textures: { list: [2, 3] } },
        406: { drawable: 41, textures: { list: [0] } },
        407: { drawable: 49, textures: { list: [0, 1] } },
        408: { drawable: 50, textures: { list: [1, 2] } },
        409: { drawable: 52, textures: { list: [1, 2] } },
        410: { drawable: 59, textures: { list: [5, 8, 9] } },
        411: { drawable: 84, textures: { list: [0, 6] } },
        412: { drawable: 92, textures: { list: [4, 5, 7, 12] } },
        413: { drawable: 125, textures: { list: [0, 4] } },
        414: { drawable: 126, textures: { list: [0] } },
        415: { drawable: 129, textures: { list: [0, 4] } },
        416: { drawable: 130, textures: { list: [0, 4] } },
        417: { drawable: 141, textures: { list: [0, 3, 5] } },
        418: { drawable: 2000, textures: { list: [0] } },
        419: { drawable: 2002, textures: { list: [1] } },
        420: { drawable: 2003, textures: { list: [1] } },
        421: { drawable: 2004, textures: { range: [2, 9], except: [6, 7, 8] } },
        422: { drawable: 2047, textures: { list: [3] } },
        423: { drawable: 2048, textures: { list: [1] } },
        424: { drawable: 2050, textures: { list: [0] } },
        425: { drawable: 2051, textures: { list: [1] } },
        426: { drawable: 2163, textures: { list: [0] } },
        427: { drawable: 6, textures: { list: [0, 2] } },
        428: { drawable: 7, textures: { list: [0, 2] } },
        429: { drawable: 23, textures: { list: [0, 1] } },
        430: { drawable: 34, textures: { list: [0] } },
        431: { drawable: 37, textures: { list: [0, 2] } },
        432: { drawable: 41, textures: { list: [2, 3] } },
        433: { drawable: 86, textures: { list: [0] } },
        434: { drawable: 133, textures: { list: [0, 2, 3] } },
        435: { drawable: 135, textures: { list: [0, 1, 4] } },
        436: { drawable: 136, textures: { list: [0, 1, 4] } },
        437: { drawable: 2000, textures: { list: [0, 2] } },
        438: { drawable: 2001, textures: { list: [1] } },
        439: { drawable: 2042, textures: { list: [3] } },
        440: { drawable: 2045, textures: { list: [0, 1] } },
        441: { drawable: 2046, textures: { list: [1] } },
        442: { drawable: 2165, textures: { list: [0] } },
        443: { drawable: 10, textures: { list: [0] } },
        444: { drawable: 21, textures: { list: [0, 7, 9] } },
        445: { drawable: 24, textures: { list: [0] } },
        446: { drawable: 25, textures: { list: [0] } },
        447: { drawable: 40, textures: { list: [0, 2, 5, 9, 11] } },
        448: { drawable: 53, textures: { list: [0] } },
        449: { drawable: 60, textures: { list: [0] } },
        450: { drawable: 61, textures: { list: [0] } },
        451: { drawable: 104, textures: { list: [1, 3, 8] } },
        452: { drawable: 0, textures: { list: [0, 3] } },
        453: { drawable: 6, textures: { list: [0] } },
        454: { drawable: 9, textures: { list: [0] } },
        455: { drawable: 13, textures: { list: [0] } },
        456: { drawable: 20, textures: { list: [6] } },
        457: { drawable: 21, textures: { list: [4] } },
        458: { drawable: 29, textures: { list: [0] } },
        459: { drawable: 108, textures: { list: [1, 4, 8] } },
        460: { drawable: 10, textures: { list: [0, 2] } },
        461: { drawable: 11, textures: { list: [2] } },
        462: { drawable: 12, textures: { list: [0, 2] } },
        463: { drawable: 21, textures: { list: [0, 1] } },
        464: { drawable: 22, textures: { list: [0, 2, 3] } },
        465: { drawable: 23, textures: { list: [0, 1] } },
        466: { drawable: 28, textures: { range: [0, 4], except: [1] } },
        467: { drawable: 29, textures: { range: [0, 4], except: [1] } },
        468: { drawable: 30, textures: { list: [0, 2, 3] } },
        469: { drawable: 31, textures: { list: [0, 2, 3] } },
        470: { drawable: 37, textures: { list: [0, 10] } },
        471: { drawable: 38, textures: { list: [0, 9] } },
        472: { drawable: 2000, textures: { list: [0] } },
        473: { drawable: 2001, textures: { list: [0] } },
        474: { drawable: 2002, textures: { list: [0] } },
        475: { drawable: 2003, textures: { list: [0] } },
        476: { drawable: 2004, textures: { list: [2] } },
        477: { drawable: 2005, textures: { list: [0, 2, 3] } },
        478: { drawable: 2006, textures: { list: [0, 2, 14, 15] } },
        479: { drawable: 2007, textures: { list: [0, 2, 13, 15, 16, 17] } },
        480: { drawable: 2009, textures: { list: [0] } },
        481: { drawable: 2012, textures: { range: [0, 9] } },
        482: { drawable: 2013, textures: { list: [0] } },
        483: { drawable: 2016, textures: { list: [0] } },
        484: { drawable: 2018, textures: { list: [0] } },
        485: { drawable: 2022, textures: { list: [0] } },
        486: { drawable: 2023, textures: { list: [0] } },
        487: { drawable: 2028, textures: { list: [0] } },
        488: { drawable: 2029, textures: { list: [0, 1] } },
        489: { drawable: 2046, textures: { list: [0] } },
        490: { drawable: 2047, textures: { list: [0] } },
        491: { drawable: 2048, textures: { list: [0] } },
        492: { drawable: 2049, textures: { list: [0] } },
        493: { drawable: 2052, textures: { list: [0] } },
        494: { drawable: 2053, textures: { list: [0] } },
        495: { drawable: 2054, textures: { list: [0] } },
        496: { drawable: 2077, textures: { list: [0, 1, 9] } },
        497: { drawable: 2086, textures: { list: [2] } },
        498: { drawable: 2087, textures: { list: [0, 1, 2] } },
        499: { drawable: 2088, textures: { list: [2] } },
        500: { drawable: 2097, textures: { range: [0, 9] } },
        501: { drawable: 2102, textures: { range: [0, 5] } },
        502: { drawable: 2104, textures: { range: [0, 9] } },
        503: { drawable: 2106, textures: { list: [0] } },
        504: { drawable: 2107, textures: { list: [0] } },
        505: { drawable: 2108, textures: { list: [0, 1, 2] } },
        506: { drawable: 2113, textures: { range: [0, 9] } },
        507: { drawable: 2115, textures: { list: [0] } },
        508: { drawable: 2116, textures: { list: [0] } },
        509: { drawable: 2118, textures: { list: [0] } },
        510: { drawable: 21, textures: { list: [2] } },
        511: { drawable: 22, textures: { list: [0, 9, 11] } },
        512: { drawable: 23, textures: { list: [0] } },
        513: { drawable: 27, textures: { list: [0, 2] } },
        514: { drawable: 28, textures: { list: [0, 9, 11] } },
        515: { drawable: 86, textures: { list: [0] } },
        516: { drawable: 2004, textures: { list: [0] } },
        517: { drawable: 2005, textures: { list: [0] } },
        518: { drawable: 2006, textures: { list: [0] } },
        519: { drawable: 2007, textures: { list: [0] } },
        520: { drawable: 2012, textures: { list: [0, 1] } },
        521: { drawable: 2017, textures: { list: [0] } },
        522: { drawable: 2034, textures: { list: [0] } },
        523: { drawable: 2035, textures: { list: [0] } },
        524: { drawable: 2037, textures: { list: [0] } },
        525: { drawable: 2038, textures: { list: [0] } },
        526: { drawable: 2039, textures: { list: [0] } },
        527: { drawable: 2040, textures: { list: [0] } },
        528: { drawable: 2041, textures: { list: [0] } },
        529: { drawable: 2042, textures: { list: [0] } },
        530: { drawable: 2070, textures: { list: [0, 1, 2] } },
        531: { drawable: 2071, textures: { list: [2] } },
        532: { drawable: 2072, textures: { list: [2] } },
        533: { drawable: 2076, textures: { list: [0, 1] } },
        534: { drawable: 2078, textures: { range: [0, 9] } },
        535: { drawable: 2082, textures: { range: [0, 5] } },
        536: { drawable: 2083, textures: { list: [0] } },
        537: { drawable: 2084, textures: { list: [0, 1, 2] } },
        538: { drawable: 2087, textures: { list: [0] } },
        539: { drawable: 2090, textures: { range: [0, 9] } },
        540: { drawable: 2091, textures: { list: [0] } },
        541: { drawable: 2092, textures: { list: [0] } },
        542: { drawable: 2093, textures: { list: [0] } },
        543: { drawable: 2094, textures: { list: [0] } },
        544: { drawable: 2095, textures: { list: [0] } },
        545: { drawable: 8, textures: { list: [1, 2, 3] } },
        546: { drawable: 70, textures: { list: [1] } },
        547: { drawable: 2001, textures: { list: [0] } },
        548: { drawable: 2002, textures: { list: [0] } },
        549: { drawable: 2003, textures: { range: [0, 5] } },
        550: { drawable: 2004, textures: { range: [0, 6] } },
        551: { drawable: 2006, textures: { range: [0, 6] } },
        552: { drawable: 2009, textures: { range: [0, 5] } },
        553: { drawable: 2017, textures: { list: [0] } },
        554: { drawable: 2018, textures: { range: [0, 9] } },
        555: { drawable: 2022, textures: { range: [0, 4] } },
        556: { drawable: 2035, textures: { list: [0, 1] } },
        557: { drawable: 2036, textures: { list: [0] } },
        558: { drawable: 2037, textures: { list: [0] } },
        559: { drawable: 2041, textures: { range: [0, 6] } },
        560: { drawable: 2058, textures: { list: [0] } },
        561: { drawable: 2059, textures: { list: [0] } },
        562: { drawable: 2060, textures: { list: [0, 1, 2] } },
        563: { drawable: 2065, textures: { range: [0, 9] } },
        564: { drawable: 2068, textures: { list: [0] } },
        565: { drawable: 2070, textures: { list: [0] } },
        566: { drawable: 2074, textures: { list: [2] } },
        567: { drawable: 2075, textures: { list: [0, 1, 2] } },
        568: { drawable: 2076, textures: { list: [2] } },
        569: { drawable: 2085, textures: { range: [0, 9] } },
        570: { drawable: 2090, textures: { range: [0, 5] } },
        571: { drawable: 2092, textures: { range: [0, 9] } },
        572: { drawable: 7, textures: { list: [1, 2, 3] } },
        573: { drawable: 2005, textures: { list: [0] } },
        574: { drawable: 2006, textures: { range: [0, 5] } },
        575: { drawable: 2007, textures: { range: [0, 6] } },
        576: { drawable: 2012, textures: { range: [0, 5] } },
        577: { drawable: 2019, textures: { list: [0, 1] } },
        578: { drawable: 2020, textures: { range: [0, 8] } },
        579: { drawable: 2024, textures: { range: [0, 4] } },
        580: { drawable: 2038, textures: { list: [0, 1] } },
        581: { drawable: 2039, textures: { list: [0] } },
        582: { drawable: 2040, textures: { list: [0] } },
        583: { drawable: 2044, textures: { range: [0, 6] } },
        584: { drawable: 2045, textures: { range: [0, 6] } },
        585: { drawable: 2060, textures: { list: [0] } },
        586: { drawable: 2061, textures: { list: [0, 1, 2] } },
        587: { drawable: 2064, textures: { list: [0] } },
        588: { drawable: 2067, textures: { range: [0, 9] } },
        589: { drawable: 2071, textures: { list: [0] } },
        590: { drawable: 2072, textures: { list: [0] } },
        591: { drawable: 2077, textures: { list: [0, 1, 2] } },
        592: { drawable: 2078, textures: { list: [2] } },
        593: { drawable: 2079, textures: { list: [2] } },
        594: { drawable: 2083, textures: { list: [0, 1] } },
        595: { drawable: 2089, textures: { range: [0, 5] } },
        596: { drawable: 5, textures: { list: [0] } },
        597: { drawable: 8, textures: { list: [0, 3] } },
        598: { drawable: 23, textures: { list: [5] } },
        599: { drawable: 26, textures: { list: [0] } },
        600: { drawable: 40, textures: { list: [0, 5] } },
        601: { drawable: 41, textures: { list: [0, 5] } },
        602: { drawable: 2000, textures: { list: [1] } },
        603: { drawable: 2001, textures: { list: [0] } },
        604: { drawable: 2002, textures: { list: [0] } },
        605: { drawable: 2011, textures: { list: [0, 1] } },
        606: { drawable: 2012, textures: { list: [0] } },
        607: { drawable: 2013, textures: { list: [0] } },
        608: { drawable: 24, textures: { list: [9] } },
        609: { drawable: 28, textures: { list: [0] } },
        610: { drawable: 42, textures: { list: [0, 5] } },
        611: { drawable: 43, textures: { list: [0, 5] } },
        612: { drawable: 2000, textures: { list: [0] } },
        613: { drawable: 2012, textures: { list: [0, 1] } },
        614: { drawable: 2014, textures: { list: [0] } },
        615: { drawable: 3, textures: { list: [0] } },
        616: { drawable: 6, textures: { list: [0] } },
        617: { drawable: 7, textures: { list: [0, 1] } },
        618: { drawable: 11, textures: { list: [0] } },
        619: { drawable: 175, textures: { list: [0, 2] } },
        620: { drawable: 2053, textures: { list: [0, 1, 2] } },
        621: { drawable: 166, textures: { list: [0] } },
        622: { drawable: 122, textures: { list: [0, 1] } },
        623: { drawable: 2005, textures: { list: [0] } },
        624: { drawable: 2006, textures: { list: [0] } },
        625: { drawable: 2017, textures: { list: [0, 1] } },
        626: { drawable: 2073, textures: { range: [0, 11], except: [7, 8, 10] } },
        627: { drawable: 2074, textures: { range: [0, 7] } },
        628: { drawable: 121, textures: { list: [0, 1] } },
        629: { drawable: 2025, textures: { list: [0, 1] } },
        630: { drawable: 89, textures: { list: [3] } },
        631: { drawable: 95, textures: { list: [1] } },
        632: { drawable: 111, textures: { list: [1, 3] } },
        633: { drawable: 234, textures: { list: [3, 4] } },
        634: { drawable: 249, textures: { list: [0, 1] } },
        635: { drawable: 250, textures: { list: [0, 1] } },
        636: { drawable: 348, textures: { range: [0, 11], except: [7] } },
        637: { drawable: 349, textures: { range: [1, 11], except: [7] } },
        638: { drawable: 2000, textures: { range: [0, 5] } },
        639: { drawable: 2001, textures: { list: [9] } },
        640: { drawable: 2002, textures: { list: [9] } },
        641: { drawable: 2003, textures: { list: [5] } },
        642: { drawable: 2014, textures: { list: [0, 1] } },
        643: { drawable: 2015, textures: { list: [0] } },
        644: { drawable: 2103, textures: { list: [0, 1] } },
        645: { drawable: 2106, textures: { list: [1] } },
        646: { drawable: 2108, textures: { list: [0, 1, 2] } },
        647: { drawable: 2291, textures: { list: [0, 1, 3] } },
        648: { drawable: 2300, textures: { list: [0, 1, 3, 11, 12, 14] } },
        649: { drawable: 2313, textures: { list: [0, 1, 3, 11, 12, 14] } },
        650: { drawable: 2314, textures: { list: [0, 1, 3, 11, 12, 14] } },
        651: { drawable: 2318, textures: { range: [0, 5], except: [2, 4] } },
        652: { drawable: 2319, textures: { range: [1, 7] } },
        653: { drawable: 2320, textures: { range: [1, 7] } },
        654: { drawable: 2321, textures: { range: [0, 5], except: [2] } },
        655: { drawable: 2322, textures: { range: [0, 5], except: [2] } },
        656: { drawable: 2324, textures: { list: [1, 3, 5] } },
        657: { drawable: 2325, textures: { list: [1, 3, 5] } },
        658: { drawable: 2327, textures: { list: [1, 4, 5] } },
        659: { drawable: 2332, textures: { list: [0] } },
        660: { drawable: 2334, textures: { list: [0, 1] } },
        661: { drawable: 2335, textures: { list: [0] } },
        662: { drawable: 2336, textures: { list: [0] } },
        663: { drawable: 2337, textures: { list: [0] } },
        664: { drawable: 2338, textures: { list: [0] } },
        665: { drawable: 7, textures: { list: [0, 1] } },
        666: { drawable: 9, textures: { range: [0, 8], except: [3, 5, 6, 7] } },
        667: { drawable: 14, textures: { list: [1] } },
        668: { drawable: 41, textures: { list: [0] } },
        669: { drawable: 57, textures: { range: [0, 8], except: [1, 4, 6] } },
        670: { drawable: 58, textures: { range: [0, 8], except: [1, 4, 6] } },
        671: { drawable: 59, textures: { list: [2, 3] } },
        672: { drawable: 60, textures: { list: [1, 2, 3] } },
        673: { drawable: 86, textures: { list: [0, 1] } },
        674: { drawable: 90, textures: { list: [0, 2] } },
        675: { drawable: 91, textures: { list: [0, 2] } },
        676: { drawable: 92, textures: { list: [1, 2] } },
        677: { drawable: 93, textures: { list: [1, 2] } },
        678: { drawable: 103, textures: { list: [0, 5] } },
        679: { drawable: 119, textures: { list: [0, 1, 2] } },
        680: { drawable: 136, textures: { list: [0, 5] } },
        681: { drawable: 244, textures: { list: [0, 4] } },
        682: { drawable: 249, textures: { range: [0, 4], except: [3] } },
        683: { drawable: 250, textures: { range: [0, 4], except: [3] } },
        684: { drawable: 257, textures: { list: [0, 1] } },
        685: { drawable: 258, textures: { list: [0, 1] } },
        686: { drawable: 305, textures: { list: [0, 4, 8] } },
        687: { drawable: 306, textures: { list: [0, 4, 8] } },
        688: { drawable: 327, textures: { list: [5] } },
        689: { drawable: 328, textures: { list: [5] } },
        690: { drawable: 329, textures: { list: [5] } },
        691: { drawable: 330, textures: { list: [5] } },
        692: { drawable: 339, textures: { range: [0, 7], except: [1, 4, 6] } },
        693: { drawable: 340, textures: { range: [0, 7], except: [1, 4, 6] } },
        694: { drawable: 357, textures: { list: [0, 6] } },
        695: { drawable: 358, textures: { list: [0, 6] } },
        696: { drawable: 359, textures: { list: [0, 6] } },
        697: { drawable: 360, textures: { list: [0, 6] } },
        698: { drawable: 364, textures: { list: [0, 1, 4] } },
        699: { drawable: 366, textures: { list: [0, 5, 10, 11] } },
        700: { drawable: 367, textures: { list: [0, 5, 10, 11] } },
        701: { drawable: 382, textures: { list: [3] } },
        702: { drawable: 383, textures: { list: [1] } },
        703: { drawable: 2003, textures: { list: [0, 4, 5, 10] } },
        704: { drawable: 2004, textures: { list: [0, 4, 5, 10] } },
        705: { drawable: 2010, textures: { list: [6] } },
        706: { drawable: 2011, textures: { list: [0, 1] } },
        707: { drawable: 2012, textures: { list: [0] } },
        708: { drawable: 2013, textures: { list: [0] } },
        709: { drawable: 2094, textures: { list: [1] } },
        710: { drawable: 2096, textures: { list: [0, 1, 2] } },
        711: { drawable: 2282, textures: { list: [0, 2] } },
        712: { drawable: 2292, textures: { list: [0, 3, 4, 8, 14] } },
        713: { drawable: 2293, textures: { list: [0, 3, 4, 8, 14] } },
        714: { drawable: 2310, textures: { range: [1, 6] } },
        715: { drawable: 2311, textures: { range: [0, 8] } },
        716: { drawable: 2313, textures: { range: [0, 8] } },
        717: { drawable: 2314, textures: { range: [1, 6] } },
        718: { drawable: 2315, textures: { range: [0, 4] } },
        719: { drawable: 2316, textures: { range: [0, 4] } },
        720: { drawable: 2318, textures: { range: [1, 5] } },
        721: { drawable: 2321, textures: { list: [0] } },
        722: { drawable: 2323, textures: { list: [0] } },
        723: { drawable: 2324, textures: { list: [0] } },
        724: { drawable: 2325, textures: { list: [0] } },
        725: { drawable: 2326, textures: { list: [0] } },
        726: { drawable: 4, textures: { list: [0] } },
        727: { drawable: 10, textures: { list: [0, 1, 2, 3, 8, 14] } },
        728: { drawable: 11, textures: { list: [0, 1, 2, 3, 8, 14] } },
        729: { drawable: 13, textures: { range: [0, 3] } },
        730: { drawable: 21, textures: { range: [0, 4], except: [2] } },
        731: { drawable: 25, textures: { range: [0, 12], except: [1, 2, 3, 5, 6] } },
        732: { drawable: 26, textures: { range: [0, 12], except: [1, 2, 3, 5, 6] } },
        733: { drawable: 27, textures: { list: [0, 4] } },
        734: { drawable: 71, textures: { range: [0, 5], except: [1, 2] } },
        735: { drawable: 2004, textures: { list: [0, 1] } },
        736: { drawable: 2013, textures: { range: [0, 3] } },
        737: { drawable: 2048, textures: { list: [0, 2] } },
        738: { drawable: 2065, textures: { list: [5] } },
        739: { drawable: 2069, textures: { list: [1, 3, 7, 10, 13, 16] } },
        740: { drawable: 2083, textures: { list: [0, 1, 11, 12] } },
        741: { drawable: 2084, textures: { list: [0, 1, 11, 12] } },
        742: { drawable: 2088, textures: { range: [0, 5] } },
        743: { drawable: 2090, textures: { list: [0, 1] } },
        744: { drawable: 2091, textures: { list: [0, 1] } },
        745: { drawable: 2093, textures: { list: [4] } },
        746: { drawable: 24, textures: { list: [0] } },
        747: { drawable: 35, textures: { list: [0] } },
        748: { drawable: 37, textures: { list: [0] } },
        749: { drawable: 38, textures: { list: [0, 7] } },
        750: { drawable: 39, textures: { list: [0, 7] } },
        751: { drawable: 40, textures: { list: [0, 2, 3, 8] } },
        752: { drawable: 41, textures: { list: [0, 2, 3, 8] } },
        753: { drawable: 59, textures: { list: [0] } },
        754: { drawable: 64, textures: { list: [0, 1, 2] } },
        755: { drawable: 65, textures: { list: [0] } },
        756: { drawable: 66, textures: { list: [0, 5] } },
        757: { drawable: 67, textures: { list: [0, 5] } },
        758: { drawable: 69, textures: { list: [0] } },
        759: { drawable: 71, textures: { list: [0] } },
        760: { drawable: 72, textures: { list: [0] } },
        761: { drawable: 74, textures: { list: [0] } },
        762: { drawable: 75, textures: { list: [0, 1] } },
        763: { drawable: 76, textures: { list: [0, 5] } },
        764: { drawable: 77, textures: { list: [0, 5] } },
        765: { drawable: 78, textures: { list: [0, 1] } },
        766: { drawable: 80, textures: { list: [0, 1] } },
        767: { drawable: 89, textures: { list: [1, 2] } },
        768: { drawable: 90, textures: { list: [0, 1] } },
        769: { drawable: 91, textures: { list: [1, 2] } },
        770: { drawable: 104, textures: { list: [7, 16] } },
        771: { drawable: 159, textures: { list: [0] } },
        772: { drawable: 162, textures: { list: [0] } },
        773: { drawable: 163, textures: { list: [0] } },
        774: { drawable: 164, textures: { list: [0] } },
        775: { drawable: 165, textures: { list: [0] } },
        776: { drawable: 178, textures: { list: [0, 20] } },
        777: { drawable: 185, textures: { list: [0] } },
        778: { drawable: 193, textures: { list: [0] } },
        779: { drawable: 194, textures: { list: [0] } },
        780: { drawable: 216, textures: { list: [0, 1, 11, 12] } },
        781: { drawable: 217, textures: { list: [0, 1, 11, 12] } },
        782: { drawable: 2003, textures: { list: [0, 2] } },
        783: { drawable: 2016, textures: { list: [0, 1, 2] } },
        784: { drawable: 2055, textures: { list: [5] } },
        785: { drawable: 2056, textures: { list: [1, 5, 7, 13] } },
        786: { drawable: 2067, textures: { list: [4] } },
        787: { drawable: 2076, textures: { list: [0, 1] } },
        788: { drawable: 2077, textures: { list: [0, 1] } },
        789: { drawable: 2082, textures: { list: [4] } },
        790: { drawable: 23, textures: { list: [4] } },
        791: { drawable: 24, textures: { range: [0, 5], except: [1, 4] } },
        792: { drawable: 25, textures: { range: [0, 5], except: [1, 4] } },
        793: { drawable: 28, textures: { list: [0, 3, 8] } },
        794: { drawable: 49, textures: { list: [0, 2, 4] } },
        795: { drawable: 96, textures: { list: [0, 1] } },
        796: { drawable: 2004, textures: { list: [4, 9, 15] } },
        797: { drawable: 2005, textures: { list: [0] } },
        798: { drawable: 2052, textures: { list: [0, 1, 3] } },
        799: { drawable: 2163, textures: { list: [0, 2] } },
        800: { drawable: 6, textures: { list: [0] } },
        801: { drawable: 7, textures: { list: [0] } },
        802: { drawable: 8, textures: { list: [0] } },
        803: { drawable: 23, textures: { list: [0, 3, 9] } },
        804: { drawable: 29, textures: { list: [0] } },
        805: { drawable: 36, textures: { list: [2, 3] } },
        806: { drawable: 37, textures: { list: [0, 5] } },
        807: { drawable: 42, textures: { list: [0] } },
        808: { drawable: 47, textures: { list: [0, 3, 5] } },
        809: { drawable: 50, textures: { list: [0, 2, 4] } },
        810: { drawable: 95, textures: { list: [10] } },
        811: { drawable: 99, textures: { list: [0, 1] } },
        812: { drawable: 111, textures: { list: [0] } },
        813: { drawable: 133, textures: { list: [0, 3, 4, 6, 11, 16, 18] } },
        814: { drawable: 135, textures: { list: [4, 7] } },
        815: { drawable: 136, textures: { list: [4, 7] } },
        816: { drawable: 141, textures: { list: [0, 3] } },
        817: { drawable: 142, textures: { list: [0] } },
        818: { drawable: 148, textures: { list: [0, 3, 5] } },
        819: { drawable: 150, textures: { list: [0, 3] } },
        820: { drawable: 2000, textures: { list: [1, 4] } },
        821: { drawable: 2003, textures: { list: [0] } },
        822: { drawable: 2047, textures: { list: [0, 1, 3] } },
        823: { drawable: 2167, textures: { list: [0, 1] } },
        824: { drawable: 1, textures: { list: [15] } },
        825: { drawable: 7, textures: { list: [0] } },
        826: { drawable: 20, textures: { list: [0, 11] } },
        827: { drawable: 21, textures: { list: [0] } },
        828: { drawable: 42, textures: { list: [0] } },
        829: { drawable: 43, textures: { range: [0, 7], except: [5, 6] } },
        830: { drawable: 51, textures: { list: [0] } },
        831: { drawable: 54, textures: { list: [0] } },
        832: { drawable: 56, textures: { list: [0, 1] } },
        833: { drawable: 2058, textures: { list: [2, 4] } },
        834: { drawable: 0, textures: { list: [0] } },
        835: { drawable: 1, textures: { list: [3, 6, 7] } },
        836: { drawable: 6, textures: { list: [0, 2] } },
        837: { drawable: 13, textures: { list: [0, 3, 10] } },
        838: { drawable: 19, textures: { list: [1, 11] } },
        839: { drawable: 20, textures: { list: [6, 7, 8] } },
        840: { drawable: 29, textures: { list: [1] } },
        841: { drawable: 42, textures: { list: [0, 3, 5, 8, 9] } },
        842: { drawable: 103, textures: { list: [0, 1] } },
        843: { drawable: 2062, textures: { list: [2, 4] } },
        844: { drawable: 21, textures: { list: [0, 1, 8] } },
        845: { drawable: 22, textures: { range: [0, 3] } },
        846: { drawable: 23, textures: { list: [0, 1, 8] } },
        847: { drawable: 37, textures: { list: [0, 1, 9] } },
        848: { drawable: 126, textures: { list: [0] } },
        849: { drawable: 127, textures: { list: [0] } },
        850: { drawable: 2005, textures: { list: [0, 2, 4] } },
        851: { drawable: 2020, textures: { list: [0, 2] } },
        852: { drawable: 2021, textures: { list: [0] } },
        853: { drawable: 2055, textures: { range: [0, 4] } },
        854: { drawable: 2081, textures: { range: [0, 4], except: [1] } },
        855: { drawable: 2082, textures: { range: [0, 8], except: [6] } },
        856: { drawable: 2083, textures: { list: [0, 2] } },
        857: { drawable: 2084, textures: { range: [0, 7], except: [2, 3] } },
        858: { drawable: 2085, textures: { range: [0, 7], except: [1] } },
        859: { drawable: 19, textures: { list: [0] } },
        860: { drawable: 20, textures: { list: [0, 2, 8, 15] } },
        861: { drawable: 21, textures: { list: [0, 2] } },
        862: { drawable: 22, textures: { list: [0, 2, 8, 15] } },
        863: { drawable: 28, textures: { list: [0, 2, 8, 15] } },
        864: { drawable: 96, textures: { list: [0] } },
        865: { drawable: 97, textures: { list: [0] } },
        866: { drawable: 2067, textures: { list: [0, 1, 3] } },
        867: { drawable: 2068, textures: { range: [0, 6], except: [1] } },
        868: { drawable: 2069, textures: { range: [0, 7], except: [2, 3] } },
        869: { drawable: 2087, textures: { list: [4] } },
        870: { drawable: 57, textures: { list: [0] } },
        871: { drawable: 58, textures: { list: [0, 1] } },
        872: { drawable: 2007, textures: { list: [0] } },
        873: { drawable: 2015, textures: { list: [0] } },
        874: { drawable: 2024, textures: { list: [0, 1] } },
        875: { drawable: 2025, textures: { list: [0] } },
        876: { drawable: 2026, textures: { list: [0] } },
        877: { drawable: 2028, textures: { list: [0] } },
        878: { drawable: 2055, textures: { list: [0, 1] } },
        879: { drawable: 2056, textures: { list: [0] } },
        880: { drawable: 2059, textures: { list: [4] } },
        881: { drawable: 65, textures: { list: [0] } },
        882: { drawable: 66, textures: { list: [0, 1] } },
        883: { drawable: 73, textures: { list: [0] } },
        884: { drawable: 2018, textures: { list: [0] } },
        885: { drawable: 2026, textures: { list: [0, 1] } },
        886: { drawable: 2049, textures: { list: [0] } },
        887: { drawable: 2050, textures: { list: [0] } },
        888: { drawable: 2064, textures: { list: [4] } },
        889: { drawable: 5, textures: { range: [0, 6], except: [1, 2, 3] } },
        890: { drawable: 8, textures: { list: [3] } },
        891: { drawable: 26, textures: { list: [0, 2, 4, 10, 20] } },
        892: { drawable: 28, textures: { list: [0, 2, 4] } },
        893: { drawable: 36, textures: { list: [0, 1] } },
        894: { drawable: 39, textures: { list: [0, 1] } },
        895: { drawable: 41, textures: { list: [0, 1] } },
        896: { drawable: 42, textures: { list: [0, 3] } },
        897: { drawable: 43, textures: { list: [0, 3] } },
        898: { drawable: 122, textures: { list: [0] } },
        899: { drawable: 175, textures: { list: [3] } },
        900: { drawable: 2130, textures: { list: [0, 1, 3] } },
        901: { drawable: 2117, textures: { list: [1] } },
        902: { drawable: 0, textures: { list: [2, 5] } },
        903: { drawable: 46, textures: { list: [0] } },
        904: { drawable: 58, textures: { list: [0, 1] } },
        905: { drawable: 59, textures: { list: [7, 8, 9] } },
        906: { drawable: 116, textures: { list: [0, 1] } },
        907: { drawable: 117, textures: { list: [0, 1] } },
        908: { drawable: 123, textures: { list: [0] } },
        909: { drawable: 124, textures: { list: [0] } },
        910: { drawable: 125, textures: { list: [0, 3] } },
        911: { drawable: 126, textures: { list: [3] } },
        912: { drawable: 142, textures: { list: [25] } },
        913: { drawable: 143, textures: { list: [25] } },
        914: { drawable: 147, textures: { list: [2] } },
        915: { drawable: 148, textures: { list: [2] } },
        916: { drawable: 150, textures: { list: [1] } },
        917: { drawable: 2000, textures: { list: [0] } },
        918: { drawable: 2001, textures: { list: [0] } },
        919: { drawable: 2002, textures: { list: [0] } },
        920: { drawable: 2004, textures: { list: [1] } },
        921: { drawable: 2005, textures: { list: [0, 2, 3] } },
        922: { drawable: 2006, textures: { list: [0, 2, 3] } },
        923: { drawable: 2051, textures: { list: [0, 4] } },
        924: { drawable: 2053, textures: { list: [0, 1] } },
        925: { drawable: 2055, textures: { list: [0, 1] } },
        926: { drawable: 2056, textures: { range: [2, 5] } },
        927: { drawable: 2057, textures: { list: [1] } },
        928: { drawable: 2058, textures: { range: [2, 5] } },
        929: { drawable: 2060, textures: { list: [0, 1, 2] } },
        930: { drawable: 2061, textures: { list: [1, 4, 5, 8] } },
        931: { drawable: 2062, textures: { list: [1, 4, 5, 8] } },
        932: { drawable: 2063, textures: { list: [1, 3, 4, 8] } },
        933: { drawable: 2064, textures: { list: [2, 3, 4] } },
        934: { drawable: 2065, textures: { list: [1, 3, 4, 8] } },
        935: { drawable: 2066, textures: { list: [1, 3, 4, 8] } },
        936: { drawable: 2067, textures: { list: [4, 5, 6, 14] } },
        937: { drawable: 2068, textures: { list: [4, 5, 6, 14, 15] } },
        938: { drawable: 0, textures: { list: [0] } },
        939: { drawable: 59, textures: { list: [0, 7] } },
        940: { drawable: 106, textures: { list: [20] } },
        941: { drawable: 112, textures: { list: [1, 3] } },
        942: { drawable: 125, textures: { list: [0] } },
        943: { drawable: 2002, textures: { list: [0, 1] } },
        944: { drawable: 2003, textures: { list: [0] } },
        945: { drawable: 2005, textures: { list: [2, 3] } },
        946: { drawable: 2006, textures: { list: [2, 3] } },
        947: { drawable: 2060, textures: { list: [2, 3, 4] } },
        948: { drawable: 2061, textures: { list: [0, 1] } },
        949: { drawable: 2064, textures: { list: [0, 4] } },
        950: { drawable: 2065, textures: { list: [4, 5] } },
        951: { drawable: 2066, textures: { list: [0, 1] } },
        952: { drawable: 2067, textures: { list: [2, 3, 4] } },
        953: { drawable: 2068, textures: { list: [1] } },
        954: { drawable: 2069, textures: { list: [4, 5, 8] } },
        955: { drawable: 2070, textures: { list: [0, 1, 2] } },
        956: { drawable: 2071, textures: { list: [3, 4, 8] } },
        957: { drawable: 2072, textures: { list: [3, 4, 8] } },
        958: { drawable: 2073, textures: { list: [3, 4, 8] } },
        959: { drawable: 2074, textures: { list: [2, 3, 4] } },
        960: { drawable: 2075, textures: { list: [4, 5, 6, 14, 15] } },
        961: { drawable: 2076, textures: { list: [4, 5, 6, 14, 15] } },
        962: { drawable: 0, textures: { list: [11] } },
        963: { drawable: 13, textures: { list: [0] } },
        964: { drawable: 26, textures: { list: [1, 2] } },
        965: { drawable: 65, textures: { list: [2] } },
        966: { drawable: 111, textures: { list: [2] } },
        967: { drawable: 139, textures: { list: [2] } },
        968: { drawable: 186, textures: { list: [1, 8] } },
        969: { drawable: 228, textures: { list: [0, 8] } },
        970: { drawable: 241, textures: { list: [3] } },
        971: { drawable: 242, textures: { list: [3] } },
        972: { drawable: 294, textures: { list: [5, 6] } },
        973: { drawable: 295, textures: { list: [5, 6] } },
        974: { drawable: 316, textures: { list: [1, 3, 4] } },
        975: { drawable: 317, textures: { list: [1, 3, 4] } },
        976: { drawable: 318, textures: { list: [1, 3, 4] } },
        977: { drawable: 319, textures: { list: [1, 3, 4] } },
        978: { drawable: 348, textures: { list: [4] } },
        979: { drawable: 349, textures: { list: [4] } },
        980: { drawable: 363, textures: { list: [0] } },
        981: { drawable: 364, textures: { list: [0] } },
        982: { drawable: 2000, textures: { list: [5, 18, 19, 20] } },
        983: { drawable: 2001, textures: { range: [0, 6], except: [3] } },
        984: { drawable: 2002, textures: { range: [0, 6], except: [3] } },
        985: { drawable: 2003, textures: { list: [2, 3] } },
        986: { drawable: 2004, textures: { list: [0, 2] } },
        987: { drawable: 2007, textures: { list: [0, 10, 16, 18] } },
        988: { drawable: 2008, textures: { list: [1, 4] } },
        989: { drawable: 2009, textures: { list: [1, 3] } },
        990: { drawable: 2010, textures: { list: [1] } },
        991: { drawable: 2011, textures: { list: [1] } },
        992: { drawable: 2016, textures: { list: [1, 2, 4] } },
        993: { drawable: 2032, textures: { list: [2] } },
        994: { drawable: 2033, textures: { list: [2] } },
        995: { drawable: 2097, textures: { list: [2, 3] } },
        996: { drawable: 2099, textures: { list: [0, 1, 2] } },
        997: { drawable: 2100, textures: { list: [1] } },
        998: { drawable: 2101, textures: { range: [12, 17] } },
        999: { drawable: 2102, textures: { range: [12, 17] } },
        1000: { drawable: 2104, textures: { list: [1, 2] } },
        1001: { drawable: 2105, textures: { list: [1, 2] } },
        1002: { drawable: 2107, textures: { list: [1] } },
        1003: { drawable: 2285, textures: { range: [0, 7] } },
        1004: { drawable: 2286, textures: { list: [3, 4, 5] } },
        1005: { drawable: 2287, textures: { list: [1] } },
        1006: { drawable: 2288, textures: { list: [0, 2] } },
        1007: { drawable: 2291, textures: { list: [0, 2] } },
        1008: { drawable: 2293, textures: { list: [0, 14] } },
        1009: { drawable: 2296, textures: { list: [0, 1, 2] } },
        1010: { drawable: 2297, textures: { list: [0, 1, 2] } },
        1011: { drawable: 2298, textures: { list: [1] } },
        1012: { drawable: 2299, textures: { list: [1] } },
        1013: { drawable: 2300, textures: { list: [0, 2, 5, 7, 16] } },
        1014: { drawable: 2301, textures: { list: [1, 2, 7] } },
        1015: { drawable: 2302, textures: { list: [1, 2] } },
        1016: { drawable: 2303, textures: { list: [3] } },
        1017: { drawable: 2304, textures: { list: [3] } },
        1018: { drawable: 2305, textures: { list: [1, 2, 4] } },
        1019: { drawable: 2306, textures: { list: [0] } },
        1020: { drawable: 2307, textures: { list: [2, 3, 7] } },
        1021: { drawable: 2309, textures: { list: [0, 1] } },
        1022: { drawable: 2311, textures: { range: [0, 3] } },
        1023: { drawable: 2313, textures: { list: [0, 2, 5, 7, 16] } },
        1024: { drawable: 2314, textures: { list: [0, 2, 5, 7, 16] } },
        1025: { drawable: 2315, textures: { list: [1, 2, 7] } },
        1026: { drawable: 2316, textures: { list: [4] } },
        1027: { drawable: 2317, textures: { list: [5] } },
        1028: { drawable: 2329, textures: { range: [0, 3] } },
        1029: { drawable: 7, textures: { list: [0, 2] } },
        1030: { drawable: 9, textures: { list: [0, 1, 3] } },
        1031: { drawable: 27, textures: { list: [0, 2] } },
        1032: { drawable: 57, textures: { list: [0, 7] } },
        1033: { drawable: 58, textures: { list: [0, 6] } },
        1034: { drawable: 103, textures: { list: [2] } },
        1035: { drawable: 136, textures: { list: [2, 7] } },
        1036: { drawable: 188, textures: { list: [1, 8] } },
        1037: { drawable: 238, textures: { list: [0] } },
        1038: { drawable: 249, textures: { list: [3] } },
        1039: { drawable: 250, textures: { list: [3] } },
        1040: { drawable: 305, textures: { list: [0, 5, 6] } },
        1041: { drawable: 306, textures: { list: [0, 5, 6] } },
        1042: { drawable: 327, textures: { list: [1, 3, 4] } },
        1043: { drawable: 328, textures: { list: [1, 3, 4] } },
        1044: { drawable: 329, textures: { list: [1, 3, 4] } },
        1045: { drawable: 330, textures: { list: [1, 3, 4] } },
        1046: { drawable: 339, textures: { list: [0, 6] } },
        1047: { drawable: 340, textures: { list: [0, 6] } },
        1048: { drawable: 366, textures: { list: [1, 4, 6] } },
        1049: { drawable: 367, textures: { list: [1, 4, 6] } },
        1050: { drawable: 382, textures: { list: [0] } },
        1051: { drawable: 383, textures: { list: [0] } },
        1052: { drawable: 2000, textures: { list: [2, 3, 4] } },
        1053: { drawable: 2003, textures: { list: [0, 2] } },
        1054: { drawable: 2005, textures: { list: [0, 2] } },
        1055: { drawable: 2006, textures: { list: [1, 4] } },
        1056: { drawable: 2007, textures: { list: [1, 3] } },
        1057: { drawable: 2008, textures: { list: [1] } },
        1058: { drawable: 2009, textures: { list: [1] } },
        1059: { drawable: 2010, textures: { list: [2, 3] } },
        1060: { drawable: 2014, textures: { list: [1, 2] } },
        1061: { drawable: 2085, textures: { list: [2, 3] } },
        1062: { drawable: 2087, textures: { list: [0, 1, 2] } },
        1063: { drawable: 2088, textures: { list: [1] } },
        1064: { drawable: 2089, textures: { range: [12, 17] } },
        1065: { drawable: 2090, textures: { range: [12, 17] } },
        1066: { drawable: 2092, textures: { list: [1, 2] } },
        1067: { drawable: 2093, textures: { list: [1, 2] } },
        1068: { drawable: 2095, textures: { list: [1] } },
        1069: { drawable: 2282, textures: { list: [0, 11] } },
        1070: { drawable: 2286, textures: { range: [0, 5] } },
        1071: { drawable: 2287, textures: { list: [0, 1, 2] } },
        1072: { drawable: 2288, textures: { list: [0, 1, 2] } },
        1073: { drawable: 2289, textures: { list: [1, 2, 7] } },
        1074: { drawable: 2290, textures: { range: [0, 7] } },
        1075: { drawable: 2291, textures: { list: [1] } },
        1076: { drawable: 2292, textures: { list: [0, 11, 13, 16] } },
        1077: { drawable: 2293, textures: { list: [0, 11, 13, 16] } },
        1078: { drawable: 2294, textures: { list: [0, 1] } },
        1079: { drawable: 2295, textures: { list: [1] } },
        1080: { drawable: 2296, textures: { range: [0, 5] } },
        1081: { drawable: 2297, textures: { list: [1, 2] } },
        1082: { drawable: 2298, textures: { list: [2, 3, 7] } },
        1083: { drawable: 2299, textures: { list: [2, 3, 7] } },
        1084: { drawable: 2300, textures: { list: [1, 2, 4] } },
        1085: { drawable: 2301, textures: { list: [2] } },
        1086: { drawable: 2303, textures: { list: [2, 3, 7] } },
        1087: { drawable: 2305, textures: { range: [0, 3] } },
        1088: { drawable: 2309, textures: { list: [1, 2, 7] } },
        1089: { drawable: 10, textures: { list: [8, 14] } },
        1090: { drawable: 11, textures: { list: [8, 14] } },
        1091: { drawable: 13, textures: { list: [2] } },
        1092: { drawable: 21, textures: { list: [0, 3, 4] } },
        1093: { drawable: 25, textures: { list: [1, 5, 7, 12] } },
        1094: { drawable: 26, textures: { list: [1, 5, 7, 12] } },
        1095: { drawable: 27, textures: { list: [1, 2, 4] } },
        1096: { drawable: 28, textures: { list: [8, 14] } },
        1097: { drawable: 31, textures: { list: [0, 8, 10] } },
        1098: { drawable: 32, textures: { list: [0, 8, 10, 14] } },
        1099: { drawable: 63, textures: { list: [0, 2, 6, 7] } },
        1100: { drawable: 64, textures: { range: [0, 7], except: [1, 4, 5] } },
        1101: { drawable: 69, textures: { list: [0, 4] } },
        1102: { drawable: 71, textures: { list: [2] } },
        1103: { drawable: 72, textures: { list: [2] } },
        1104: { drawable: 74, textures: { list: [2] } },
        1105: { drawable: 75, textures: { list: [2] } },
        1106: { drawable: 76, textures: { list: [0, 4] } },
        1107: { drawable: 77, textures: { list: [0] } },
        1108: { drawable: 96, textures: { list: [8, 10, 14, 16] } },
        1109: { drawable: 97, textures: { list: [0, 8] } },
        1110: { drawable: 115, textures: { list: [0] } },
        1111: { drawable: 116, textures: { list: [0] } },
        1112: { drawable: 131, textures: { list: [0, 8, 10] } },
        1113: { drawable: 146, textures: { list: [0, 4, 6, 8, 10] } },
        1114: { drawable: 147, textures: { list: [0, 4, 6, 8, 10] } },
        1115: { drawable: 148, textures: { list: [0, 4, 6, 8, 10] } },
        1116: { drawable: 149, textures: { list: [0, 4, 6, 8, 10] } },
        1117: { drawable: 150, textures: { list: [0, 13] } },
        1118: { drawable: 159, textures: { list: [0, 5, 6, 8, 10] } },
        1119: { drawable: 160, textures: { list: [0, 5, 6, 8, 10] } },
        1120: { drawable: 161, textures: { list: [0, 5, 6, 8, 10] } },
        1121: { drawable: 162, textures: { list: [0, 5, 6, 8, 10] } },
        1122: { drawable: 2000, textures: { list: [0, 1, 3, 12, 14, 15] } },
        1123: { drawable: 2001, textures: { list: [1] } },
        1124: { drawable: 2004, textures: { list: [1] } },
        1125: { drawable: 2005, textures: { list: [2] } },
        1126: { drawable: 2008, textures: { list: [1] } },
        1127: { drawable: 2009, textures: { list: [0] } },
        1128: { drawable: 2010, textures: { list: [1, 2] } },
        1129: { drawable: 2036, textures: { list: [1] } },
        1130: { drawable: 2037, textures: { list: [1, 4] } },
        1131: { drawable: 2039, textures: { range: [0, 3] } },
        1132: { drawable: 2041, textures: { list: [1] } },
        1133: { drawable: 2045, textures: { list: [1] } },
        1134: { drawable: 2048, textures: { list: [0, 4] } },
        1135: { drawable: 2049, textures: { list: [0] } },
        1136: { drawable: 2050, textures: { list: [0, 1, 2] } },
        1137: { drawable: 2053, textures: { list: [0, 15] } },
        1138: { drawable: 2054, textures: { list: [0, 15] } },
        1139: { drawable: 2055, textures: { list: [0, 1, 3, 7, 12, 14, 15] } },
        1140: { drawable: 2056, textures: { range: [0, 3] } },
        1141: { drawable: 2058, textures: { list: [1] } },
        1142: { drawable: 2065, textures: { range: [0, 13], except: [5, 6, 8, 9, 10, 11] } },
        1143: { drawable: 2066, textures: { list: [0, 1] } },
        1144: { drawable: 2068, textures: { list: [2] } },
        1145: { drawable: 2069, textures: { list: [1, 8, 10, 11, 14, 16] } },
        1146: { drawable: 2071, textures: { list: [0, 2, 5, 7, 17] } },
        1147: { drawable: 2072, textures: { list: [0, 2, 5, 7, 17] } },
        1148: { drawable: 2074, textures: { list: [1, 7] } },
        1149: { drawable: 2075, textures: { list: [1, 7] } },
        1150: { drawable: 2079, textures: { range: [0, 4], except: [2] } },
        1151: { drawable: 2080, textures: { range: [0, 4], except: [2] } },
        1152: { drawable: 2082, textures: { list: [0, 1, 3, 7, 12, 14, 15] } },
        1153: { drawable: 2083, textures: { list: [0, 2, 5, 7] } },
        1154: { drawable: 2084, textures: { list: [0, 2, 5, 7] } },
        1155: { drawable: 2093, textures: { list: [1, 3] } },
        1156: { drawable: 2096, textures: { list: [1] } },
        1157: { drawable: 2097, textures: { list: [1] } },
        1158: { drawable: 2098, textures: { range: [0, 4] } },
        1159: { drawable: 2101, textures: { list: [1] } },
        1160: { drawable: 2108, textures: { list: [3, 4] } },
        1161: { drawable: 2110, textures: { list: [3, 4] } },
        1162: { drawable: 2111, textures: { list: [0, 1, 2] } },
        1163: { drawable: 2116, textures: { range: [0, 10] } },
        1164: { drawable: 2120, textures: { range: [0, 9] } },
        1165: { drawable: 2121, textures: { range: [0, 9] } },
        1166: { drawable: 2125, textures: { list: [0, 1] } },
        1167: { drawable: 38, textures: { list: [0, 8, 10, 11, 14] } },
        1168: { drawable: 39, textures: { list: [0, 8, 10, 11, 14] } },
        1169: { drawable: 40, textures: { list: [0, 1, 6, 8, 9] } },
        1170: { drawable: 41, textures: { list: [0, 1, 6, 8, 9] } },
        1171: { drawable: 57, textures: { list: [0] } },
        1172: { drawable: 64, textures: { list: [0, 4] } },
        1173: { drawable: 66, textures: { list: [2] } },
        1174: { drawable: 67, textures: { list: [2] } },
        1175: { drawable: 76, textures: { list: [2] } },
        1176: { drawable: 77, textures: { list: [2] } },
        1177: { drawable: 105, textures: { list: [8] } },
        1178: { drawable: 152, textures: { list: [0] } },
        1179: { drawable: 195, textures: { list: [0, 6, 8, 9, 10] } },
        1180: { drawable: 196, textures: { list: [0, 6, 8, 9, 10] } },
        1181: { drawable: 197, textures: { list: [0, 6, 8, 9, 10] } },
        1182: { drawable: 198, textures: { list: [0, 6, 8, 9, 10] } },
        1183: { drawable: 216, textures: { list: [0, 2, 5, 7, 9] } },
        1184: { drawable: 217, textures: { list: [0, 2, 5, 7, 9] } },
        1185: { drawable: 2003, textures: { list: [0, 3] } },
        1186: { drawable: 2004, textures: { list: [2] } },
        1187: { drawable: 2006, textures: { list: [1] } },
        1188: { drawable: 2012, textures: { range: [0, 3] } },
        1189: { drawable: 2014, textures: { range: [0, 3] } },
        1190: { drawable: 2035, textures: { list: [1] } },
        1191: { drawable: 2038, textures: { range: [0, 3] } },
        1192: { drawable: 2040, textures: { list: [1] } },
        1193: { drawable: 2044, textures: { list: [1] } },
        1194: { drawable: 2046, textures: { list: [0, 1] } },
        1195: { drawable: 2049, textures: { list: [2] } },
        1196: { drawable: 2051, textures: { list: [1] } },
        1197: { drawable: 2054, textures: { list: [2] } },
        1198: { drawable: 2055, textures: { range: [0, 13], except: [5, 6, 7, 8, 9, 10] } },
        1199: { drawable: 2056, textures: { list: [10] } },
        1200: { drawable: 2061, textures: { list: [1] } },
        1201: { drawable: 2062, textures: { list: [1] } },
        1202: { drawable: 2066, textures: { range: [0, 3] } },
        1203: { drawable: 2082, textures: { list: [1, 3] } },
        1204: { drawable: 2084, textures: { range: [0, 3] } },
        1205: { drawable: 2086, textures: { list: [1] } },
        1206: { drawable: 2087, textures: { list: [1, 4] } },
        1207: { drawable: 2088, textures: { list: [1, 4] } },
        1208: { drawable: 2096, textures: { list: [3, 4] } },
        1209: { drawable: 2097, textures: { list: [3, 4] } },
        1210: { drawable: 2102, textures: { list: [0, 1] } },
        1211: { drawable: 2104, textures: { range: [0, 19] } },
        1212: { drawable: 9, textures: { list: [0, 6] } },
        1213: { drawable: 22, textures: { list: [1, 5] } },
        1214: { drawable: 23, textures: { list: [1, 5] } },
        1215: { drawable: 24, textures: { list: [5, 6] } },
        1216: { drawable: 28, textures: { list: [4, 5] } },
        1217: { drawable: 47, textures: { list: [0, 1] } },
        1218: { drawable: 50, textures: { list: [3] } },
        1219: { drawable: 52, textures: { list: [3] } },
        1220: { drawable: 59, textures: { list: [0, 7, 8] } },
        1221: { drawable: 84, textures: { list: [1, 8] } },
        1222: { drawable: 92, textures: { list: [0, 5, 8] } },
        1223: { drawable: 122, textures: { list: [0] } },
        1224: { drawable: 123, textures: { list: [0] } },
        1225: { drawable: 125, textures: { range: [2, 6], except: [4] } },
        1226: { drawable: 129, textures: { list: [2, 5] } },
        1227: { drawable: 130, textures: { list: [2, 5] } },
        1228: { drawable: 134, textures: { list: [0, 1] } },
        1229: { drawable: 135, textures: { list: [0, 1] } },
        1230: { drawable: 141, textures: { list: [4, 6] } },
        1231: { drawable: 2000, textures: { list: [0, 1, 4, 7] } },
        1232: { drawable: 2001, textures: { range: [0, 8], except: [6, 7] } },
        1233: { drawable: 2002, textures: { list: [0, 3] } },
        1234: { drawable: 2003, textures: { list: [0, 3] } },
        1235: { drawable: 2004, textures: { list: [0, 1, 10, 11, 14] } },
        1236: { drawable: 2047, textures: { list: [0, 2] } },
        1237: { drawable: 2048, textures: { list: [0, 3] } },
        1238: { drawable: 2051, textures: { list: [0] } },
        1239: { drawable: 2163, textures: { list: [1, 3] } },
        1240: { drawable: 23, textures: { list: [0, 5, 10] } },
        1241: { drawable: 36, textures: { list: [1, 2] } },
        1242: { drawable: 37, textures: { list: [0, 6] } },
        1243: { drawable: 41, textures: { list: [1, 2] } },
        1244: { drawable: 49, textures: { list: [1] } },
        1245: { drawable: 128, textures: { list: [0] } },
        1246: { drawable: 129, textures: { list: [0] } },
        1247: { drawable: 133, textures: { list: [0, 3, 8, 24] } },
        1248: { drawable: 135, textures: { list: [2, 5] } },
        1249: { drawable: 136, textures: { list: [2, 5] } },
        1250: { drawable: 2000, textures: { list: [5] } },
        1251: { drawable: 2001, textures: { list: [3, 4] } },
        1252: { drawable: 2002, textures: { list: [3, 4] } },
        1253: { drawable: 2042, textures: { list: [0, 2] } },
        1254: { drawable: 2044, textures: { list: [2] } },
        1255: { drawable: 2046, textures: { list: [0] } },
        1256: { drawable: 2165, textures: { list: [1] } },
        1257: { drawable: 10, textures: { list: [12] } },
        1258: { drawable: 21, textures: { list: [2, 10] } },
        1259: { drawable: 40, textures: { list: [6, 9] } },
        1260: { drawable: 53, textures: { list: [2] } },
        1261: { drawable: 60, textures: { list: [2, 3, 7] } },
        1262: { drawable: 61, textures: { list: [2, 3, 7] } },
        1263: { drawable: 104, textures: { list: [0, 4] } },
        1264: { drawable: 9, textures: { list: [0, 2] } },
        1265: { drawable: 19, textures: { list: [0] } },
        1266: { drawable: 64, textures: { list: [0] } },
        1267: { drawable: 67, textures: { list: [0] } },
        1268: { drawable: 108, textures: { list: [0, 1, 4] } },
        1269: { drawable: 21, textures: { list: [7, 8] } },
        1270: { drawable: 22, textures: { list: [0, 5, 9, 10] } },
        1271: { drawable: 23, textures: { list: [3, 7, 8] } },
        1272: { drawable: 28, textures: { list: [0, 2, 6, 10, 11] } },
        1273: { drawable: 29, textures: { list: [0, 2, 6, 10, 11] } },
        1274: { drawable: 30, textures: { list: [0, 2, 5] } },
        1275: { drawable: 31, textures: { list: [0, 2, 5] } },
        1276: { drawable: 37, textures: { list: [0, 7, 12] } },
        1277: { drawable: 38, textures: { list: [0, 7, 12] } },
        1278: { drawable: 2000, textures: { list: [1] } },
        1279: { drawable: 2002, textures: { list: [0, 1] } },
        1280: { drawable: 2005, textures: { list: [0, 2, 5] } },
        1281: { drawable: 2006, textures: { list: [0, 2, 9, 11, 16] } },
        1282: { drawable: 2007, textures: { list: [0, 2, 9, 14] } },
        1283: { drawable: 2008, textures: { range: [0, 3] } },
        1284: { drawable: 2009, textures: { list: [1, 3] } },
        1285: { drawable: 2011, textures: { range: [0, 3] } },
        1286: { drawable: 2013, textures: { list: [1] } },
        1287: { drawable: 2014, textures: { list: [0] } },
        1288: { drawable: 2015, textures: { list: [0] } },
        1289: { drawable: 2017, textures: { range: [0, 3] } },
        1290: { drawable: 2019, textures: { list: [0] } },
        1291: { drawable: 2028, textures: { list: [1, 2] } },
        1292: { drawable: 2029, textures: { list: [0] } },
        1293: { drawable: 2048, textures: { list: [0, 1] } },
        1294: { drawable: 2050, textures: { list: [0] } },
        1295: { drawable: 2051, textures: { list: [0] } },
        1296: { drawable: 2054, textures: { list: [1] } },
        1297: { drawable: 2077, textures: { list: [2, 3, 4, 10] } },
        1298: { drawable: 2079, textures: { list: [1] } },
        1299: { drawable: 2089, textures: { list: [0, 1, 2] } },
        1300: { drawable: 2094, textures: { range: [0, 10] } },
        1301: { drawable: 2098, textures: { range: [0, 9] } },
        1302: { drawable: 2099, textures: { range: [0, 9] } },
        1303: { drawable: 2103, textures: { list: [0, 1] } },
        1304: { drawable: 2107, textures: { list: [1, 3] } },
        1305: { drawable: 2112, textures: { range: [0, 4] } },
        1306: { drawable: 20, textures: { list: [0, 1, 3, 7, 12] } },
        1307: { drawable: 22, textures: { list: [0, 1, 3, 7, 12] } },
        1308: { drawable: 28, textures: { list: [0, 1, 3, 7, 12] } },
        1309: { drawable: 95, textures: { list: [0] } },
        1310: { drawable: 2002, textures: { list: [1] } },
        1311: { drawable: 2003, textures: { list: [1] } },
        1312: { drawable: 2008, textures: { list: [1] } },
        1313: { drawable: 2012, textures: { list: [0] } },
        1314: { drawable: 2017, textures: { list: [1, 2] } },
        1315: { drawable: 2036, textures: { list: [1] } },
        1316: { drawable: 2042, textures: { list: [1] } },
        1317: { drawable: 2071, textures: { list: [3, 4] } },
        1318: { drawable: 2072, textures: { list: [3, 4] } },
        1319: { drawable: 2077, textures: { list: [0, 1] } },
        1320: { drawable: 2079, textures: { range: [0, 19] } },
        1321: { drawable: 2087, textures: { list: [1, 3] } },
        1322: { drawable: 2089, textures: { range: [0, 3] } },
        1323: { drawable: 2000, textures: { list: [0, 1] } },
        1324: { drawable: 2002, textures: { list: [1] } },
        1325: { drawable: 2003, textures: { range: [6, 12] } },
        1326: { drawable: 2005, textures: { list: [0, 2, 3] } },
        1327: { drawable: 2009, textures: { range: [6, 13] } },
        1328: { drawable: 2012, textures: { list: [0] } },
        1329: { drawable: 2013, textures: { list: [0] } },
        1330: { drawable: 2014, textures: { list: [0] } },
        1331: { drawable: 2019, textures: { range: [0, 7] } },
        1332: { drawable: 2020, textures: { range: [0, 6] } },
        1333: { drawable: 2023, textures: { range: [0, 3] } },
        1334: { drawable: 2031, textures: { range: [0, 8] } },
        1335: { drawable: 2059, textures: { list: [1, 3] } },
        1336: { drawable: 2064, textures: { range: [0, 4] } },
        1337: { drawable: 2082, textures: { range: [0, 10] } },
        1338: { drawable: 2086, textures: { range: [0, 9] } },
        1339: { drawable: 2087, textures: { range: [0, 9] } },
        1340: { drawable: 2091, textures: { list: [0, 1] } },
        1341: { drawable: 2000, textures: { list: [0] } },
        1342: { drawable: 2003, textures: { list: [0] } },
        1343: { drawable: 2004, textures: { list: [0] } },
        1344: { drawable: 2005, textures: { list: [1, 2] } },
        1345: { drawable: 2006, textures: { range: [6, 12] } },
        1346: { drawable: 2012, textures: { range: [6, 13] } },
        1347: { drawable: 2016, textures: { list: [0] } },
        1348: { drawable: 2021, textures: { range: [0, 8] } },
        1349: { drawable: 2022, textures: { range: [0, 7] } },
        1350: { drawable: 2025, textures: { range: [0, 3] } },
        1351: { drawable: 2034, textures: { range: [0, 8] } },
        1352: { drawable: 2064, textures: { list: [0, 3] } },
        1353: { drawable: 2066, textures: { range: [0, 3] } },
        1354: { drawable: 2078, textures: { list: [3, 4] } },
        1355: { drawable: 2079, textures: { list: [3, 4] } },
        1356: { drawable: 2084, textures: { list: [0, 1] } },
        1357: { drawable: 2086, textures: { range: [0, 19] } },
        1358: { drawable: 23, textures: { list: [0, 7] } },
        1359: { drawable: 40, textures: { list: [7, 9] } },
        1360: { drawable: 41, textures: { list: [7, 9] } },
        1361: { drawable: 2000, textures: { list: [4] } },
        1362: { drawable: 11, textures: { list: [3] } },
        1363: { drawable: 28, textures: { list: [0, 11, 22] } },
        1364: { drawable: 42, textures: { list: [0, 7, 9] } },
        1365: { drawable: 43, textures: { list: [0, 7, 9] } },
        1366: { drawable: 130, textures: { range: [0, 12], except: [6] } },
        1367: { drawable: 175, textures: { list: [0, 3] } },
        1368: { drawable: 2130, textures: { list: [3] } },
        1369: { drawable: 38, textures: { list: [0] } },
        1370: { drawable: 103, textures: { list: [0, 3] } },
        1371: { drawable: 106, textures: { list: [0, 3, 4, 8, 18, 20] } },
        1372: { drawable: 107, textures: { list: [3] } },
        1373: { drawable: 114, textures: { list: [0, 17] } },
        1374: { drawable: 118, textures: { list: [0, 1, 13, 23] } },
        1375: { drawable: 119, textures: { list: [0, 1, 13, 23] } },
        1376: { drawable: 149, textures: { list: [0] } },
        1377: { drawable: 2003, textures: { list: [0, 1, 2] } },
        1378: { drawable: 2049, textures: { list: [0, 1] } },
        1379: { drawable: 2054, textures: { list: [0, 1] } },
        1380: { drawable: 37, textures: { list: [0] } },
        1381: { drawable: 38, textures: { list: [1, 3, 4] } },
        1382: { drawable: 102, textures: { range: [0, 17], except: [7] } },
        1383: { drawable: 103, textures: { range: [0, 17], except: [7] } },
        1384: { drawable: 104, textures: { range: [0, 17], except: [7] } },
        1385: { drawable: 105, textures: { range: [0, 17], except: [7] } },
        1386: { drawable: 106, textures: { range: [0, 17], except: [7] } },
        1387: { drawable: 117, textures: { list: [0, 1, 13, 23] } },
        1388: { drawable: 2000, textures: { list: [0, 1, 2] } },
        1389: { drawable: 2062, textures: { list: [0, 1] } },
        1390: { drawable: 2063, textures: { list: [0, 1] } },
        1391: { drawable: 23, textures: { list: [0] } },
        1392: { drawable: 24, textures: { list: [1] } },
        1393: { drawable: 49, textures: { list: [3, 4] } },
        1394: { drawable: 50, textures: { list: [3, 4] } },
        1395: { drawable: 53, textures: { list: [2, 3] } },
        1396: { drawable: 97, textures: { list: [0, 1] } },
        1397: { drawable: 139, textures: { list: [5, 7] } },
        1398: { drawable: 206, textures: { range: [0, 17], except: [7] } },
        1399: { drawable: 207, textures: { range: [0, 17], except: [7] } },
        1400: { drawable: 208, textures: { range: [0, 17], except: [7] } },
        1401: { drawable: 209, textures: { range: [0, 17], except: [7, 15] } },
        1402: { drawable: 210, textures: { range: [0, 17], except: [7, 15] } },
        1403: { drawable: 211, textures: { range: [0, 17], except: [7, 15] } },
        1404: { drawable: 212, textures: { range: [0, 17], except: [7, 15] } },
        1405: { drawable: 215, textures: { range: [0, 17], except: [7] } },
        1406: { drawable: 219, textures: { range: [0, 17], except: [7] } },
        1407: { drawable: 220, textures: { range: [0, 18], except: [7] } },
        1408: { drawable: 221, textures: { range: [0, 24], except: [7, 19, 20, 21, 22, 23] } },
        1409: { drawable: 222, textures: { range: [0, 24], except: [7, 19, 20, 21, 22, 23] } },
        1410: { drawable: 228, textures: { list: [0] } },
        1411: { drawable: 239, textures: { range: [0, 17], except: [7] } },
        1412: { drawable: 336, textures: { list: [0, 1, 7] } },
        1413: { drawable: 337, textures: { list: [0, 1, 7] } },
        1414: { drawable: 2007, textures: { list: [10, 12, 18] } },
        1415: { drawable: 2303, textures: { list: [4] } },
        1416: { drawable: 2304, textures: { list: [4] } },
        1417: { drawable: 2307, textures: { list: [8] } },
        1418: { drawable: 42, textures: { list: [3, 4] } },
        1419: { drawable: 43, textures: { list: [4] } },
        1420: { drawable: 57, textures: { list: [3, 6] } },
        1421: { drawable: 58, textures: { list: [3, 6] } },
        1422: { drawable: 136, textures: { list: [7] } },
        1423: { drawable: 210, textures: { range: [0, 17], except: [7] } },
        1424: { drawable: 211, textures: { range: [0, 17], except: [7] } },
        1425: { drawable: 212, textures: { range: [0, 17], except: [7] } },
        1426: { drawable: 213, textures: { range: [0, 17], except: [7, 15] } },
        1427: { drawable: 214, textures: { range: [0, 17], except: [7, 15] } },
        1428: { drawable: 215, textures: { range: [0, 17], except: [7, 15] } },
        1429: { drawable: 216, textures: { range: [0, 17], except: [7, 15] } },
        1430: { drawable: 221, textures: { range: [0, 17], except: [7, 11] } },
        1431: { drawable: 222, textures: { range: [0, 17], except: [7] } },
        1432: { drawable: 223, textures: { range: [0, 17], except: [7] } },
        1433: { drawable: 224, textures: { range: [0, 17], except: [7] } },
        1434: { drawable: 229, textures: { range: [0, 17], except: [7] } },
        1435: { drawable: 230, textures: { range: [0, 17], except: [7] } },
        1436: { drawable: 231, textures: { range: [0, 17], except: [7] } },
        1437: { drawable: 232, textures: { range: [0, 17], except: [7] } },
        1438: { drawable: 336, textures: { list: [2, 3, 5] } },
        1439: { drawable: 339, textures: { list: [3] } },
        1440: { drawable: 340, textures: { list: [3] } },
        1441: { drawable: 366, textures: { list: [4, 6] } },
        1442: { drawable: 367, textures: { list: [4, 6] } },
        1443: { drawable: 3, textures: { list: [0] } },
        1444: { drawable: 6, textures: { list: [0, 2, 7, 11] } },
        1445: { drawable: 7, textures: { list: [0, 2, 7, 11] } },
        1446: { drawable: 10, textures: { list: [0, 1, 2, 8, 14] } },
        1447: { drawable: 11, textures: { list: [0, 1, 2, 8, 14] } },
        1448: { drawable: 13, textures: { list: [0, 1, 2] } },
        1449: { drawable: 25, textures: { range: [0, 5], except: [2, 3] } },
        1450: { drawable: 26, textures: { range: [0, 5], except: [2, 3] } },
        1451: { drawable: 27, textures: { list: [1, 2] } },
        1452: { drawable: 28, textures: { list: [0, 1, 2, 8, 10, 14] } },
        1453: { drawable: 31, textures: { list: [0, 1, 2, 8, 10, 14] } },
        1454: { drawable: 32, textures: { list: [0, 1, 2, 8, 10, 14] } },
        1455: { drawable: 33, textures: { range: [0, 6], except: [2, 4, 5] } },
        1456: { drawable: 34, textures: { range: [0, 6], except: [2, 4, 5] } },
        1457: { drawable: 35, textures: { range: [0, 6], except: [2, 4, 5] } },
        1458: { drawable: 36, textures: { range: [0, 6], except: [2, 4, 5] } },
        1459: { drawable: 72, textures: { list: [0, 2] } },
        1460: { drawable: 75, textures: { list: [0, 2] } },
        1461: { drawable: 98, textures: { range: [0, 18], except: [7] } },
        1462: { drawable: 99, textures: { range: [0, 17], except: [7] } },
        1463: { drawable: 100, textures: { range: [0, 18], except: [7] } },
        1464: { drawable: 101, textures: { range: [0, 18], except: [7] } },
        1465: { drawable: 102, textures: { range: [0, 18], except: [7] } },
        1466: { drawable: 117, textures: { range: [0, 18], except: [7] } },
        1467: { drawable: 118, textures: { range: [0, 18], except: [7] } },
        1468: { drawable: 119, textures: { range: [0, 18], except: [7] } },
        1469: { drawable: 120, textures: { range: [0, 18], except: [7] } },
        1470: { drawable: 121, textures: { range: [0, 18], except: [7] } },
        1471: { drawable: 2000, textures: { list: [3, 7, 12, 14, 15] } },
        1472: { drawable: 2055, textures: { list: [1, 3, 7, 12, 14, 15] } },
        1473: { drawable: 2065, textures: { list: [15] } },
        1474: { drawable: 2071, textures: { list: [0, 2, 5, 7, 10] } },
        1475: { drawable: 2072, textures: { list: [0, 2, 5, 7, 10] } },
        1476: { drawable: 2082, textures: { list: [1, 3, 7, 12, 14, 15] } },
        1477: { drawable: 2083, textures: { list: [0, 2, 5, 7, 10] } },
        1478: { drawable: 2084, textures: { list: [0, 2, 5, 7, 10] } },
        1479: { drawable: 38, textures: { list: [0, 8, 14] } },
        1480: { drawable: 39, textures: { list: [0, 8, 14] } },
        1481: { drawable: 40, textures: { list: [0, 1, 6, 9] } },
        1482: { drawable: 41, textures: { list: [1, 6, 9] } },
        1483: { drawable: 76, textures: { list: [2, 7] } },
        1484: { drawable: 77, textures: { list: [2, 7] } },
        1485: { drawable: 121, textures: { range: [1, 17], except: [7] } },
        1486: { drawable: 123, textures: { range: [0, 17], except: [7] } },
        1487: { drawable: 128, textures: { range: [0, 17], except: [7] } },
        1488: { drawable: 195, textures: { range: [11, 14] } },
        1489: { drawable: 196, textures: { range: [11, 14] } },
        1490: { drawable: 197, textures: { range: [11, 14] } },
        1491: { drawable: 198, textures: { range: [11, 14] } },
        1492: { drawable: 216, textures: { list: [0, 5, 7] } },
        1493: { drawable: 217, textures: { list: [0, 5, 7] } },
        1494: { drawable: 2016, textures: { list: [3] } },
        1495: { drawable: 2055, textures: { list: [10, 15] } },
        1496: { drawable: 31, textures: { list: [0, 3, 4] } },
        1497: { drawable: 47, textures: { list: [1] } },
        1498: { drawable: 59, textures: { list: [0] } },
        1499: { drawable: 86, textures: { range: [0, 18], except: [7] } },
        1500: { drawable: 87, textures: { range: [0, 18], except: [7] } },
        1501: { drawable: 88, textures: { range: [0, 18], except: [7] } },
        1502: { drawable: 2002, textures: { list: [0] } },
        1503: { drawable: 2004, textures: { list: [0, 10, 11] } },
        1504: { drawable: 30, textures: { range: [0, 4], except: [2] } },
        1505: { drawable: 41, textures: { list: [1] } },
        1506: { drawable: 61, textures: { list: [0, 7] } },
        1507: { drawable: 89, textures: { range: [0, 17], except: [7] } },
        1508: { drawable: 90, textures: { range: [0, 17], except: [7] } },
        1509: { drawable: 91, textures: { range: [0, 17], except: [7] } },
        1510: { drawable: 2042, textures: { list: [0] } },
        1511: { drawable: 10, textures: { list: [0, 12] } },
        1512: { drawable: 20, textures: { list: [0] } },
        1513: { drawable: 35, textures: { list: [0, 1] } },
        1514: { drawable: 59, textures: { range: [0, 18], except: [7] } },
        1515: { drawable: 60, textures: { range: [0, 7], except: [4, 5] } },
        1516: { drawable: 61, textures: { range: [0, 7], except: [4, 5] } },
        1517: { drawable: 62, textures: { range: [0, 7], except: [4, 5] } },
        1518: { drawable: 63, textures: { range: [0, 7], except: [4, 5] } },
        1519: { drawable: 70, textures: { list: [0, 5, 7, 8, 11, 18, 20, 21] } },
        1520: { drawable: 71, textures: { list: [0, 5, 7, 8, 11, 18, 20, 21] } },
        1521: { drawable: 72, textures: { list: [3, 5, 10, 12, 24, 25] } },
        1522: { drawable: 73, textures: { list: [3, 5, 10, 12, 24, 25] } },
        1523: { drawable: 29, textures: { list: [0, 2] } },
        1524: { drawable: 36, textures: { list: [0, 1] } },
        1525: { drawable: 62, textures: { range: [0, 17], except: [7] } },
        1526: { drawable: 73, textures: { list: [0, 1, 5, 7, 8, 11, 20, 21] } },
        1527: { drawable: 74, textures: { list: [0, 1, 5, 7, 8, 11, 20, 21] } },
        1528: { drawable: 75, textures: { range: [0, 12], except: [2, 4, 6, 8, 9, 11] } },
        1529: { drawable: 76, textures: { range: [0, 12], except: [2, 4, 6, 8, 9, 11] } },
        1530: { drawable: 108, textures: { range: [0, 6], except: [2, 4, 5] } },
        1531: { drawable: 78, textures: { list: [0] } },
        1532: { drawable: 2046, textures: { list: [0] } },
        1533: { drawable: 2047, textures: { list: [0] } },
        1534: { drawable: 33, textures: { list: [0] } },
        1535: { drawable: 112, textures: { list: [0] } },
        1536: { drawable: 148, textures: { list: [0] } },
        1537: { drawable: 2001, textures: { list: [0, 1, 2] } },
        1538: { drawable: 2005, textures: { list: [0, 5, 7] } },
        1539: { drawable: 2006, textures: { list: [0, 9, 11] } },
        1540: { drawable: 2014, textures: { list: [0, 1] } },
        1541: { drawable: 2107, textures: { list: [4] } },
        1542: { drawable: 22, textures: { list: [1, 3, 7, 12] } },
        1543: { drawable: 28, textures: { list: [1, 3, 7, 12] } },
        1544: { drawable: 9, textures: { list: [5] } },
        1545: { drawable: 2067, textures: { range: [9, 12] } },
        1546: { drawable: 2068, textures: { range: [9, 12] } },
        1547: { drawable: 2075, textures: { list: [12] } },
        1548: { drawable: 2076, textures: { list: [12] } },
        1549: { drawable: 4, textures: { list: [0, 3] } },
        1550: { drawable: 10, textures: { list: [0, 1] } },
        1551: { drawable: 13, textures: { list: [0, 2] } },
        1552: { drawable: 20, textures: { list: [0] } },
        1553: { drawable: 23, textures: { list: [3] } },
        1554: { drawable: 24, textures: { list: [0, 4, 7, 8] } },
        1555: { drawable: 29, textures: { range: [0, 7], except: [3, 4, 6] } },
        1556: { drawable: 30, textures: { range: [0, 7], except: [3, 4, 6] } },
        1557: { drawable: 31, textures: { range: [0, 7], except: [3, 4, 6] } },
        1558: { drawable: 32, textures: { range: [0, 7], except: [3, 4, 6] } },
        1559: { drawable: 89, textures: { list: [0, 2] } },
        1560: { drawable: 139, textures: { list: [0, 3, 7] } },
        1561: { drawable: 142, textures: { list: [0] } },
        1562: { drawable: 192, textures: { list: [0] } },
        1563: { drawable: 241, textures: { list: [0, 2, 4] } },
        1564: { drawable: 242, textures: { list: [0, 2, 4] } },
        1565: { drawable: 294, textures: { list: [0, 1, 4] } },
        1566: { drawable: 295, textures: { list: [0, 1, 4] } },
        1567: { drawable: 348, textures: { list: [1, 3, 5] } },
        1568: { drawable: 349, textures: { list: [1, 3, 5] } },
        1569: { drawable: 2000, textures: { list: [0, 2] } },
        1570: { drawable: 2004, textures: { list: [0] } },
        1571: { drawable: 2288, textures: { list: [0] } },
        1572: { drawable: 2291, textures: { list: [0, 5] } },
        1573: { drawable: 2302, textures: { list: [4] } },
        1574: { drawable: 2315, textures: { list: [4, 5] } },
        1575: { drawable: 2316, textures: { list: [3] } },
        1576: { drawable: 2317, textures: { list: [2, 4] } },
        1577: { drawable: 6, textures: { list: [0, 1, 4] } },
        1578: { drawable: 14, textures: { list: [0, 3] } },
        1579: { drawable: 24, textures: { list: [0, 3] } },
        1580: { drawable: 27, textures: { list: [0, 1] } },
        1581: { drawable: 57, textures: { range: [0, 8], except: [2, 3, 4, 6] } },
        1582: { drawable: 58, textures: { range: [0, 8], except: [2, 3, 4, 6] } },
        1583: { drawable: 64, textures: { list: [1, 3] } },
        1584: { drawable: 70, textures: { list: [1, 3] } },
        1585: { drawable: 86, textures: { list: [1] } },
        1586: { drawable: 90, textures: { list: [0, 4] } },
        1587: { drawable: 91, textures: { list: [0, 4] } },
        1588: { drawable: 103, textures: { list: [0, 3] } },
        1589: { drawable: 107, textures: { list: [0] } },
        1590: { drawable: 119, textures: { list: [0, 2] } },
        1591: { drawable: 136, textures: { list: [0, 3] } },
        1592: { drawable: 139, textures: { list: [0, 1, 2] } },
        1593: { drawable: 305, textures: { list: [0, 1, 4] } },
        1594: { drawable: 306, textures: { list: [0, 1, 4] } },
        1595: { drawable: 339, textures: { list: [0, 1, 5, 7] } },
        1596: { drawable: 340, textures: { list: [0, 1, 5, 7] } },
        1597: { drawable: 357, textures: { list: [1, 2] } },
        1598: { drawable: 358, textures: { list: [1, 2] } },
        1599: { drawable: 359, textures: { list: [1, 2] } },
        1600: { drawable: 360, textures: { list: [1, 2] } },
        1601: { drawable: 366, textures: { list: [3, 5, 9] } },
        1602: { drawable: 367, textures: { list: [3, 5, 9] } },
        1603: { drawable: 2000, textures: { list: [8] } },
        1604: { drawable: 2001, textures: { list: [11] } },
        1605: { drawable: 2003, textures: { list: [0, 3, 5, 9] } },
        1606: { drawable: 2004, textures: { list: [0, 3, 5, 9] } },
        1607: { drawable: 2282, textures: { list: [0, 3, 9, 10] } },
        1608: { drawable: 2289, textures: { list: [4, 5] } },
        1609: { drawable: 2292, textures: { list: [0, 2, 12] } },
        1610: { drawable: 2293, textures: { list: [0, 2, 12] } },
        1611: { drawable: 2297, textures: { list: [4] } },
        1612: { drawable: 2304, textures: { list: [1] } },
        1613: { drawable: 2305, textures: { list: [5] } },
        1614: { drawable: 2309, textures: { list: [4, 5] } },
        1615: { drawable: 4, textures: { list: [0, 1] } },
        1616: { drawable: 10, textures: { list: [0, 2, 10] } },
        1617: { drawable: 11, textures: { list: [0, 2, 10] } },
        1618: { drawable: 13, textures: { list: [0, 2] } },
        1619: { drawable: 25, textures: { list: [0, 7, 11, 12] } },
        1620: { drawable: 26, textures: { list: [0, 7, 11, 12] } },
        1621: { drawable: 28, textures: { list: [0, 2, 10] } },
        1622: { drawable: 31, textures: { list: [0, 2, 10] } },
        1623: { drawable: 32, textures: { list: [0, 2, 10] } },
        1624: { drawable: 33, textures: { list: [0, 5] } },
        1625: { drawable: 34, textures: { list: [0, 5] } },
        1626: { drawable: 35, textures: { list: [0, 5] } },
        1627: { drawable: 36, textures: { list: [0, 5] } },
        1628: { drawable: 72, textures: { list: [0, 3, 5] } },
        1629: { drawable: 75, textures: { list: [0, 3, 5] } },
        1630: { drawable: 150, textures: { list: [0, 10] } },
        1631: { drawable: 159, textures: { list: [0, 3] } },
        1632: { drawable: 160, textures: { list: [0, 3] } },
        1633: { drawable: 161, textures: { list: [0, 3] } },
        1634: { drawable: 162, textures: { list: [0, 3] } },
        1635: { drawable: 2000, textures: { range: [0, 5], except: [1, 3] } },
        1636: { drawable: 2065, textures: { list: [8] } },
        1637: { drawable: 2069, textures: { list: [1, 10, 16] } },
        1638: { drawable: 2071, textures: { list: [0, 4] } },
        1639: { drawable: 2072, textures: { list: [0, 4] } },
        1640: { drawable: 2077, textures: { list: [1, 2, 3] } },
        1641: { drawable: 2081, textures: { list: [1, 2, 3] } },
        1642: { drawable: 2082, textures: { range: [0, 5], except: [1, 3] } },
        1643: { drawable: 2083, textures: { list: [0, 4, 6] } },
        1644: { drawable: 2084, textures: { list: [0, 4, 6] } },
        1645: { drawable: 2095, textures: { range: [1, 8], except: [4] } },
        1646: { drawable: 2105, textures: { list: [0] } },
        1647: { drawable: 2106, textures: { list: [0] } },
        1648: { drawable: 2107, textures: { list: [0] } },
        1649: { drawable: 2108, textures: { list: [1, 6] } },
        1650: { drawable: 2110, textures: { list: [1, 6] } },
        1651: { drawable: 2113, textures: { range: [0, 4] } },
        1652: { drawable: 2114, textures: { list: [0] } },
        1653: { drawable: 2115, textures: { range: [1, 8], except: [5] } },
        1654: { drawable: 2118, textures: { list: [0, 1] } },
        1655: { drawable: 2123, textures: { list: [1] } },
        1656: { drawable: 26, textures: { list: [0, 1] } },
        1657: { drawable: 40, textures: { list: [0, 2, 8] } },
        1658: { drawable: 41, textures: { list: [0, 2, 8] } },
        1659: { drawable: 66, textures: { list: [0, 3] } },
        1660: { drawable: 67, textures: { list: [0, 3] } },
        1661: { drawable: 76, textures: { list: [0, 3] } },
        1662: { drawable: 77, textures: { list: [0, 3] } },
        1663: { drawable: 101, textures: { list: [0] } },
        1664: { drawable: 195, textures: { list: [0, 3] } },
        1665: { drawable: 196, textures: { list: [0, 3] } },
        1666: { drawable: 197, textures: { list: [0, 3] } },
        1667: { drawable: 198, textures: { list: [0, 3] } },
        1668: { drawable: 216, textures: { list: [0, 4] } },
        1669: { drawable: 217, textures: { list: [0, 4] } },
        1670: { drawable: 2055, textures: { list: [7, 8] } },
        1671: { drawable: 2057, textures: { list: [1, 2] } },
        1672: { drawable: 2059, textures: { list: [1, 2] } },
        1673: { drawable: 2083, textures: { list: [1, 2] } },
        1674: { drawable: 2094, textures: { list: [0] } },
        1675: { drawable: 2096, textures: { list: [1, 6] } },
        1676: { drawable: 2097, textures: { list: [1, 6] } },
        1677: { drawable: 2099, textures: { range: [0, 4] } },
        1678: { drawable: 2106, textures: { list: [1] } },
        1679: { drawable: 10, textures: { list: [0] } },
        1680: { drawable: 13, textures: { list: [0] } },
        1681: { drawable: 24, textures: { list: [0, 5] } },
        1682: { drawable: 25, textures: { list: [0, 5] } },
        1683: { drawable: 129, textures: { list: [1, 4] } },
        1684: { drawable: 130, textures: { list: [1, 4] } },
        1685: { drawable: 2004, textures: { list: [2, 9] } },
        1686: { drawable: 2163, textures: { list: [4] } },
        1687: { drawable: 6, textures: { list: [0, 1] } },
        1688: { drawable: 23, textures: { list: [0] } },
        1689: { drawable: 36, textures: { list: [2] } },
        1690: { drawable: 41, textures: { list: [2] } },
        1691: { drawable: 50, textures: { list: [0] } },
        1692: { drawable: 133, textures: { list: [0, 3] } },
        1693: { drawable: 135, textures: { list: [1, 4] } },
        1694: { drawable: 136, textures: { list: [1, 4] } },
        1695: { drawable: 148, textures: { list: [0, 3] } },
        1696: { drawable: 2000, textures: { list: [0, 4] } },
        1697: { drawable: 1, textures: { list: [13, 14, 15] } },
        1698: { drawable: 4, textures: { list: [1, 2] } },
        1699: { drawable: 20, textures: { list: [0, 3] } },
        1700: { drawable: 42, textures: { list: [0, 1, 2] } },
        1701: { drawable: 104, textures: { range: [0, 4], except: [2] } },
        1702: { drawable: 0, textures: { list: [0, 2] } },
        1703: { drawable: 13, textures: { list: [0, 5] } },
        1704: { drawable: 20, textures: { list: [1, 3] } },
        1705: { drawable: 27, textures: { list: [0] } },
        1706: { drawable: 29, textures: { list: [0, 1] } },
        1707: { drawable: 42, textures: { list: [0, 2, 6] } },
        1708: { drawable: 103, textures: { list: [0] } },
        1709: { drawable: 108, textures: { list: [1, 3] } },
        1710: { drawable: 70, textures: { list: [0] } },
        1711: { drawable: 2061, textures: { range: [1, 8], except: [4] } },
        1712: { drawable: 2073, textures: { list: [0] } },
        1713: { drawable: 2074, textures: { list: [1, 6] } },
        1714: { drawable: 2076, textures: { list: [1, 6] } },
        1715: { drawable: 2079, textures: { range: [0, 4] } },
        1716: { drawable: 2080, textures: { list: [0] } },
        1717: { drawable: 2081, textures: { range: [1, 8], except: [5] } },
        1718: { drawable: 2089, textures: { list: [1] } },
        1719: { drawable: 2065, textures: { range: [1, 7], except: [4] } },
        1720: { drawable: 2074, textures: { list: [0] } },
        1721: { drawable: 2075, textures: { list: [0] } },
        1722: { drawable: 2076, textures: { list: [0] } },
        1723: { drawable: 2078, textures: { list: [1] } },
        1724: { drawable: 2079, textures: { list: [1] } },
        1725: { drawable: 2081, textures: { range: [0, 4] } },
        1726: { drawable: 2088, textures: { list: [1] } },
        1727: { drawable: 21, textures: { list: [8, 9, 11] } },
        1728: { drawable: 22, textures: { list: [0, 10, 11] } },
        1729: { drawable: 24, textures: { list: [0, 2, 11, 12, 14] } },
        1730: { drawable: 25, textures: { list: [0, 2, 11, 12, 14] } },
        1731: { drawable: 26, textures: { list: [0, 2, 11, 12, 14] } },
        1732: { drawable: 27, textures: { list: [0, 2, 11, 12, 14] } },
        1733: { drawable: 28, textures: { list: [0, 2, 11, 12, 14] } },
        1734: { drawable: 29, textures: { list: [0, 2, 11, 12, 14] } },
        1735: { drawable: 115, textures: { list: [0] } },
        1736: { drawable: 125, textures: { list: [0] } },
        1737: { drawable: 2004, textures: { list: [0, 2] } },
        1738: { drawable: 2005, textures: { list: [0, 2] } },
        1739: { drawable: 2077, textures: { list: [7, 8] } },
        1740: { drawable: 2086, textures: { list: [1, 6] } },
        1741: { drawable: 2088, textures: { list: [1, 6] } },
        1742: { drawable: 2091, textures: { range: [0, 4] } },
        1743: { drawable: 2093, textures: { range: [1, 8], except: [5] } },
        1744: { drawable: 2096, textures: { list: [0, 1] } },
        1745: { drawable: 2101, textures: { list: [1] } },
        1746: { drawable: 2109, textures: { range: [1, 8], except: [4] } },
        1747: { drawable: 2119, textures: { list: [0] } },
        1748: { drawable: 2120, textures: { list: [0] } },
        1749: { drawable: 2121, textures: { list: [0] } },
        1750: { drawable: 20, textures: { list: [0, 2] } },
        1751: { drawable: 22, textures: { list: [0, 2] } },
        1752: { drawable: 28, textures: { list: [0, 2] } },
        1753: { drawable: 2071, textures: { list: [1] } },
        1754: { drawable: 2072, textures: { list: [1, 6] } },
        1755: { drawable: 2074, textures: { range: [0, 4] } },
        1756: { drawable: 2081, textures: { list: [1] } },
        1757: { drawable: 2088, textures: { range: [1, 8], except: [4] } },
        1758: { drawable: 2097, textures: { list: [0] } },
        1759: { drawable: 2098, textures: { list: [0] } },
        1760: { drawable: 2099, textures: { list: [0] } },
        1761: { drawable: 2131, textures: { list: [0] } },
        1762: { drawable: 1, textures: { list: [0] } },
        1763: { drawable: 4, textures: { list: [0] } },
        1764: { drawable: 7, textures: { list: [0] } },
        1765: { drawable: 10, textures: { list: [0] } },
        1766: { drawable: 11, textures: { list: [0, 1] } },
        1767: { drawable: 12, textures: { list: [0] } },
        1768: { drawable: 25, textures: { list: [0, 1, 7] } },
        1769: { drawable: 26, textures: { list: [0, 5] } },
        1770: { drawable: 28, textures: { list: [0] } },
        1771: { drawable: 29, textures: { list: [0, 5, 7] } },
        1772: { drawable: 30, textures: { list: [0, 5, 7] } },
        1773: { drawable: 35, textures: { list: [0, 3] } },
        1774: { drawable: 109, textures: { list: [0] } },
        1775: { drawable: 111, textures: { list: [5] } },
        1776: { drawable: 123, textures: { list: [0, 2] } },
        1777: { drawable: 133, textures: { list: [0] } },
        1778: { drawable: 137, textures: { list: [0, 2] } },
        1779: { drawable: 139, textures: { list: [5] } },
        1780: { drawable: 242, textures: { list: [0, 1, 2] } },
        1781: { drawable: 348, textures: { list: [5, 10, 11] } },
        1782: { drawable: 349, textures: { list: [5, 10, 11] } },
        1783: { drawable: 379, textures: { list: [0, 10, 11] } },
        1784: { drawable: 381, textures: { list: [0, 10, 11] } },
        1785: { drawable: 2000, textures: { list: [0] } },
        1786: { drawable: 2313, textures: { list: [0, 1, 12] } },
        1787: { drawable: 2314, textures: { list: [0, 1, 12] } },
        1788: { drawable: 2333, textures: { list: [0] } },
        1789: { drawable: 9, textures: { list: [0, 1, 4] } },
        1790: { drawable: 14, textures: { range: [0, 3] } },
        1791: { drawable: 24, textures: { list: [0, 1, 6] } },
        1792: { drawable: 25, textures: { list: [1, 2, 6] } },
        1793: { drawable: 57, textures: { list: [0, 1, 5, 7] } },
        1794: { drawable: 58, textures: { list: [0, 1, 5, 7] } },
        1795: { drawable: 103, textures: { list: [1, 5] } },
        1796: { drawable: 119, textures: { list: [0] } },
        1797: { drawable: 136, textures: { list: [1, 5] } },
        1798: { drawable: 249, textures: { list: [2, 5] } },
        1799: { drawable: 250, textures: { list: [2, 5] } },
        1800: { drawable: 305, textures: { list: [0, 4, 9] } },
        1801: { drawable: 306, textures: { list: [0, 4, 9] } },
        1802: { drawable: 339, textures: { range: [0, 7], except: [2, 3, 6] } },
        1803: { drawable: 340, textures: { range: [0, 7], except: [2, 3, 6] } },
        1804: { drawable: 357, textures: { list: [0, 3] } },
        1805: { drawable: 358, textures: { list: [0, 3] } },
        1806: { drawable: 359, textures: { list: [0, 3] } },
        1807: { drawable: 360, textures: { list: [0, 3] } },
        1808: { drawable: 2322, textures: { list: [0] } },
        1809: { drawable: 10, textures: { list: [0, 1, 2, 7] } },
        1810: { drawable: 11, textures: { list: [0, 1, 2, 7] } },
        1811: { drawable: 21, textures: { list: [0, 1, 4] } },
        1812: { drawable: 25, textures: { list: [0, 8, 12] } },
        1813: { drawable: 26, textures: { list: [0, 8, 12] } },
        1814: { drawable: 27, textures: { list: [0, 5] } },
        1815: { drawable: 28, textures: { list: [0, 7] } },
        1816: { drawable: 31, textures: { list: [0, 2, 7] } },
        1817: { drawable: 32, textures: { list: [0, 2, 7] } },
        1818: { drawable: 69, textures: { list: [0, 1, 2] } },
        1819: { drawable: 150, textures: { list: [0, 2, 3, 10] } },
        1820: { drawable: 2000, textures: { list: [0, 2] } },
        1821: { drawable: 2010, textures: { list: [4] } },
        1822: { drawable: 2082, textures: { list: [0, 2] } },
        1823: { drawable: 2083, textures: { list: [0, 4, 11, 12] } },
        1824: { drawable: 2084, textures: { list: [0, 4, 11, 12] } },
        1825: { drawable: 2128, textures: { list: [0] } },
        1826: { drawable: 102, textures: { list: [0] } },
        1827: { drawable: 104, textures: { list: [1, 2, 7, 16] } },
        1828: { drawable: 179, textures: { list: [0, 1, 21] } },
        1829: { drawable: 180, textures: { list: [0, 1, 21] } },
        1830: { drawable: 181, textures: { list: [0, 1, 21] } },
        1831: { drawable: 182, textures: { list: [0, 1, 21] } },
        1832: { drawable: 183, textures: { list: [0, 1, 21] } },
        1833: { drawable: 195, textures: { list: [0] } },
        1834: { drawable: 196, textures: { list: [0] } },
        1835: { drawable: 197, textures: { list: [0] } },
        1836: { drawable: 198, textures: { list: [0] } },
        1837: { drawable: 216, textures: { list: [0, 8, 11, 12, 13] } },
        1838: { drawable: 217, textures: { list: [0, 8, 11, 12, 13] } },
        1839: { drawable: 2108, textures: { list: [0] } },
        1840: { drawable: 0, textures: { list: [0] } },
        1841: { drawable: 4, textures: { list: [0] } },
        1842: { drawable: 7, textures: { list: [0, 5] } },
        1843: { drawable: 22, textures: { list: [0, 4, 7] } },
        1844: { drawable: 24, textures: { list: [0, 1, 5] } },
        1845: { drawable: 25, textures: { list: [0, 1, 5] } },
        1846: { drawable: 28, textures: { list: [0, 8] } },
        1847: { drawable: 23, textures: { list: [0, 7, 8, 9] } },
        1848: { drawable: 37, textures: { list: [0, 4, 5] } },
        1849: { drawable: 41, textures: { list: [0, 2] } },
        1850: { drawable: 47, textures: { list: [0, 4, 5] } },
        1851: { drawable: 52, textures: { list: [0, 2] } },
        1852: { drawable: 133, textures: { list: [0, 3, 6, 10, 11, 12, 16] } },
        1853: { drawable: 99, textures: { list: [0, 1, 3, 13] } },
        1854: { drawable: 104, textures: { list: [1, 2, 3] } },
        1855: { drawable: 1, textures: { range: [3, 10], except: [4, 8, 9] } },
        1856: { drawable: 13, textures: { list: [0, 3, 5, 7, 10] } },
        1857: { drawable: 19, textures: { list: [1, 6] } },
        1858: { drawable: 29, textures: { list: [0, 1, 2] } },
        1859: { drawable: 42, textures: { range: [0, 6], except: [1] } },
        1860: { drawable: 10, textures: { list: [0, 1, 2] } },
        1861: { drawable: 12, textures: { list: [0, 1, 2] } },
        1862: { drawable: 22, textures: { list: [0, 1] } },
        1863: { drawable: 38, textures: { list: [0, 2] } },
        1864: { drawable: 2027, textures: { list: [0] } },
        1865: { drawable: 2028, textures: { list: [4] } },
        1866: { drawable: 2122, textures: { list: [0] } },
        1867: { drawable: 20, textures: { list: [0, 5, 13] } },
        1868: { drawable: 21, textures: { list: [0, 1, 2] } },
        1869: { drawable: 22, textures: { list: [0, 5, 13] } },
        1870: { drawable: 23, textures: { list: [0, 1, 2] } },
        1871: { drawable: 26, textures: { list: [0, 5, 13] } },
        1872: { drawable: 28, textures: { list: [0, 5, 13] } },
        1873: { drawable: 2017, textures: { list: [4] } },
        1874: { drawable: 2100, textures: { list: [0] } },
        1875: { drawable: 2094, textures: { list: [0] } },
        1876: { drawable: 2090, textures: { list: [0] } },
        1877: { drawable: 2130, textures: { list: [0, 2] } },
        1878: { drawable: 2117, textures: { list: [2] } },
        1879: { drawable: 0, textures: { list: [5, 6] } },
        1880: { drawable: 39, textures: { list: [2] } },
        1881: { drawable: 58, textures: { list: [0] } },
        1882: { drawable: 120, textures: { list: [1, 2] } },
        1883: { drawable: 123, textures: { list: [2] } },
        1884: { drawable: 124, textures: { list: [2] } },
        1885: { drawable: 125, textures: { list: [2] } },
        1886: { drawable: 126, textures: { list: [2] } },
        1887: { drawable: 142, textures: { list: [1] } },
        1888: { drawable: 143, textures: { list: [1] } },
        1889: { drawable: 2051, textures: { list: [2] } },
        1890: { drawable: 2052, textures: { list: [1] } },
        1891: { drawable: 2056, textures: { list: [9, 10] } },
        1892: { drawable: 2058, textures: { list: [9, 10] } },
        1893: { drawable: 2061, textures: { list: [0, 8] } },
        1894: { drawable: 2062, textures: { list: [0, 8] } },
        1895: { drawable: 2063, textures: { list: [0, 8] } },
        1896: { drawable: 2065, textures: { list: [0, 8] } },
        1897: { drawable: 2066, textures: { list: [0, 8] } },
        1898: { drawable: 2067, textures: { list: [7, 8] } },
        1899: { drawable: 2068, textures: { list: [7, 8] } },
        1900: { drawable: 38, textures: { list: [2] } },
        1901: { drawable: 106, textures: { list: [21] } },
        1902: { drawable: 122, textures: { list: [2] } },
        1903: { drawable: 140, textures: { list: [0] } },
        1904: { drawable: 141, textures: { list: [1] } },
        1905: { drawable: 2001, textures: { list: [2] } },
        1906: { drawable: 2060, textures: { list: [1, 9] } },
        1907: { drawable: 2064, textures: { list: [2] } },
        1908: { drawable: 2067, textures: { list: [1, 9] } },
        1909: { drawable: 2069, textures: { list: [0, 8] } },
        1910: { drawable: 2071, textures: { list: [0, 8] } },
        1911: { drawable: 2072, textures: { list: [0, 8] } },
        1912: { drawable: 2073, textures: { list: [0, 8] } },
        1913: { drawable: 2075, textures: { list: [7, 8] } },
        1914: { drawable: 2076, textures: { list: [7, 8] } },
        1915: { drawable: 0, textures: { list: [0, 1, 4] } },
        1916: { drawable: 13, textures: { list: [0, 1] } },
        1917: { drawable: 21, textures: { list: [0, 2] } },
        1918: { drawable: 22, textures: { list: [0] } },
        1919: { drawable: 25, textures: { list: [1] } },
        1920: { drawable: 65, textures: { list: [3] } },
        1921: { drawable: 66, textures: { list: [3] } },
        1922: { drawable: 111, textures: { list: [0, 5] } },
        1923: { drawable: 139, textures: { list: [0, 5] } },
        1924: { drawable: 146, textures: { list: [0, 1] } },
        1925: { drawable: 186, textures: { list: [7] } },
        1926: { drawable: 241, textures: { list: [0] } },
        1927: { drawable: 242, textures: { list: [0] } },
        1928: { drawable: 294, textures: { range: [0, 4], except: [3] } },
        1929: { drawable: 295, textures: { range: [0, 4], except: [3] } },
        1930: { drawable: 316, textures: { list: [2, 7] } },
        1931: { drawable: 317, textures: { list: [2, 7] } },
        1932: { drawable: 318, textures: { list: [2, 7] } },
        1933: { drawable: 319, textures: { list: [2, 7] } },
        1934: { drawable: 348, textures: { list: [1, 9] } },
        1935: { drawable: 349, textures: { list: [1, 9] } },
        1936: { drawable: 351, textures: { list: [0, 1] } },
        1937: { drawable: 2000, textures: { list: [0, 1, 15, 21] } },
        1938: { drawable: 2003, textures: { list: [4] } },
        1939: { drawable: 2006, textures: { list: [0, 1] } },
        1940: { drawable: 2007, textures: { list: [0, 1, 4] } },
        1941: { drawable: 2008, textures: { list: [2] } },
        1942: { drawable: 2009, textures: { list: [2] } },
        1943: { drawable: 2016, textures: { list: [3] } },
        1944: { drawable: 2032, textures: { list: [0] } },
        1945: { drawable: 2033, textures: { list: [0] } },
        1946: { drawable: 2103, textures: { list: [0] } },
        1947: { drawable: 2291, textures: { list: [0, 4, 6] } },
        1948: { drawable: 2293, textures: { list: [5, 10, 13, 14] } },
        1949: { drawable: 2300, textures: { list: [0, 1, 4, 9, 10, 15] } },
        1950: { drawable: 2301, textures: { list: [3] } },
        1951: { drawable: 2303, textures: { list: [1, 2] } },
        1952: { drawable: 2304, textures: { list: [1, 2] } },
        1953: { drawable: 2307, textures: { list: [5, 6] } },
        1954: { drawable: 2310, textures: { list: [1, 2] } },
        1955: { drawable: 2313, textures: { list: [0, 1, 4, 6, 9, 10, 15, 21] } },
        1956: { drawable: 2314, textures: { list: [0, 1, 4, 6, 9, 10, 15, 21] } },
        1957: { drawable: 2315, textures: { list: [3] } },
        1958: { drawable: 2316, textures: { list: [0, 1] } },
        1959: { drawable: 2317, textures: { list: [0, 1] } },
        1960: { drawable: 57, textures: { list: [0, 1, 5] } },
        1961: { drawable: 58, textures: { list: [0, 1, 5] } },
        1962: { drawable: 103, textures: { list: [0] } },
        1963: { drawable: 119, textures: { list: [2] } },
        1964: { drawable: 136, textures: { list: [0] } },
        1965: { drawable: 188, textures: { list: [7] } },
        1966: { drawable: 249, textures: { list: [0] } },
        1967: { drawable: 250, textures: { list: [0] } },
        1968: { drawable: 305, textures: { range: [0, 4], except: [3] } },
        1969: { drawable: 306, textures: { range: [0, 4], except: [3] } },
        1970: { drawable: 327, textures: { list: [7] } },
        1971: { drawable: 328, textures: { list: [7] } },
        1972: { drawable: 329, textures: { list: [7] } },
        1973: { drawable: 339, textures: { list: [0, 1, 5] } },
        1974: { drawable: 340, textures: { list: [0, 1, 5] } },
        1975: { drawable: 358, textures: { list: [2] } },
        1976: { drawable: 359, textures: { list: [2] } },
        1977: { drawable: 360, textures: { list: [2] } },
        1978: { drawable: 366, textures: { list: [9] } },
        1979: { drawable: 367, textures: { list: [9] } },
        1980: { drawable: 2000, textures: { list: [5] } },
        1981: { drawable: 2003, textures: { list: [3] } },
        1982: { drawable: 2004, textures: { list: [3] } },
        1983: { drawable: 2005, textures: { list: [3] } },
        1984: { drawable: 2006, textures: { list: [2] } },
        1985: { drawable: 2007, textures: { list: [2] } },
        1986: { drawable: 2010, textures: { list: [4] } },
        1987: { drawable: 2014, textures: { list: [3] } },
        1988: { drawable: 2031, textures: { list: [0] } },
        1989: { drawable: 2282, textures: { list: [0, 5, 6] } },
        1990: { drawable: 2289, textures: { list: [3] } },
        1991: { drawable: 2292, textures: { list: [0, 1] } },
        1992: { drawable: 2293, textures: { list: [0, 1] } },
        1993: { drawable: 2298, textures: { list: [5, 6] } },
        1994: { drawable: 2299, textures: { list: [5, 6] } },
        1995: { drawable: 2303, textures: { list: [5, 6] } },
        1996: { drawable: 2304, textures: { list: [3, 4] } },
        1997: { drawable: 2308, textures: { list: [0, 10] } },
        1998: { drawable: 2309, textures: { list: [3] } },
        1999: { drawable: 10, textures: { list: [0, 1, 2] } },
        2000: { drawable: 11, textures: { list: [0, 1, 2] } },
        2001: { drawable: 22, textures: { list: [0, 4] } },
        2002: { drawable: 25, textures: { list: [0, 4, 10, 12] } },
        2003: { drawable: 26, textures: { list: [0, 4, 10, 12] } },
        2004: { drawable: 27, textures: { list: [0, 2] } },
        2005: { drawable: 28, textures: { list: [0, 1, 2] } },
        2006: { drawable: 31, textures: { list: [0, 1, 2] } },
        2007: { drawable: 32, textures: { list: [0, 1, 2] } },
        2008: { drawable: 63, textures: { list: [0, 1, 2] } },
        2009: { drawable: 70, textures: { list: [0] } },
        2010: { drawable: 71, textures: { list: [0, 5] } },
        2011: { drawable: 72, textures: { list: [0, 5] } },
        2012: { drawable: 73, textures: { list: [1] } },
        2013: { drawable: 74, textures: { list: [0, 5] } },
        2014: { drawable: 75, textures: { list: [0, 5] } },
        2015: { drawable: 76, textures: { list: [0, 1] } },
        2016: { drawable: 77, textures: { list: [0, 1] } },
        2017: { drawable: 83, textures: { list: [0, 1, 2, 4, 10] } },
        2018: { drawable: 97, textures: { list: [7] } },
        2019: { drawable: 131, textures: { list: [1, 4] } },
        2020: { drawable: 157, textures: { list: [0] } },
        2021: { drawable: 158, textures: { list: [0] } },
        2022: { drawable: 161, textures: { list: [0] } },
        2023: { drawable: 170, textures: { list: [0] } },
        2024: { drawable: 2008, textures: { list: [2] } },
        2025: { drawable: 2010, textures: { list: [3] } },
        2026: { drawable: 2030, textures: { list: [2] } },
        2027: { drawable: 2033, textures: { list: [0] } },
        2028: { drawable: 2048, textures: { list: [0, 1] } },
        2029: { drawable: 2053, textures: { list: [5, 11, 14, 15] } },
        2030: { drawable: 2054, textures: { list: [5, 11, 14, 15] } },
        2031: { drawable: 2055, textures: { list: [0, 2, 6] } },
        2032: { drawable: 2062, textures: { list: [2] } },
        2033: { drawable: 2063, textures: { list: [2] } },
        2034: { drawable: 2069, textures: { list: [1, 2, 11, 16] } },
        2035: { drawable: 2071, textures: { list: [0, 1, 4, 9, 10, 15, 16] } },
        2036: { drawable: 2072, textures: { list: [0, 1, 4, 9, 10, 15, 16] } },
        2037: { drawable: 2081, textures: { list: [0] } },
        2038: { drawable: 2082, textures: { list: [0, 2, 6] } },
        2039: { drawable: 2083, textures: { list: [0, 4, 10, 15, 16] } },
        2040: { drawable: 2084, textures: { list: [0, 4, 10, 15, 16] } },
        2041: { drawable: 2095, textures: { list: [0, 4] } },
        2042: { drawable: 2100, textures: { list: [3, 5] } },
        2043: { drawable: 2110, textures: { list: [0] } },
        2044: { drawable: 2112, textures: { range: [0, 4] } },
        2045: { drawable: 2115, textures: { list: [0, 5] } },
        2046: { drawable: 2123, textures: { list: [0] } },
        2047: { drawable: 38, textures: { list: [0, 1] } },
        2048: { drawable: 39, textures: { list: [0, 1] } },
        2049: { drawable: 40, textures: { list: [0, 4, 8] } },
        2050: { drawable: 41, textures: { list: [0, 4, 8] } },
        2051: { drawable: 57, textures: { list: [0, 2] } },
        2052: { drawable: 58, textures: { list: [0, 2] } },
        2053: { drawable: 59, textures: { list: [0, 2] } },
        2054: { drawable: 66, textures: { list: [0] } },
        2055: { drawable: 67, textures: { list: [0] } },
        2056: { drawable: 76, textures: { list: [0] } },
        2057: { drawable: 195, textures: { list: [0, 1, 2] } },
        2058: { drawable: 196, textures: { list: [0, 1, 2] } },
        2059: { drawable: 197, textures: { list: [0, 1, 2] } },
        2060: { drawable: 198, textures: { list: [0, 1, 2] } },
        2061: { drawable: 216, textures: { list: [0, 10] } },
        2062: { drawable: 217, textures: { list: [0, 10] } },
        2063: { drawable: 2009, textures: { list: [0, 1] } },
        2064: { drawable: 2011, textures: { list: [0, 1] } },
        2065: { drawable: 2034, textures: { list: [0] } },
        2066: { drawable: 2056, textures: { list: [0, 2] } },
        2067: { drawable: 2057, textures: { list: [0] } },
        2068: { drawable: 2080, textures: { list: [0] } },
        2069: { drawable: 2083, textures: { list: [0] } },
        2070: { drawable: 2084, textures: { list: [4] } },
        2071: { drawable: 9, textures: { list: [1] } },
        2072: { drawable: 22, textures: { list: [0, 4, 7, 8] } },
        2073: { drawable: 23, textures: { list: [0, 4, 7, 8] } },
        2074: { drawable: 28, textures: { range: [0, 10], except: [2, 4, 5, 7, 9] } },
        2075: { drawable: 31, textures: { list: [2] } },
        2076: { drawable: 37, textures: { range: [0, 3] } },
        2077: { drawable: 59, textures: { list: [6, 8] } },
        2078: { drawable: 84, textures: { list: [0] } },
        2079: { drawable: 125, textures: { list: [4] } },
        2080: { drawable: 129, textures: { list: [3, 4] } },
        2081: { drawable: 130, textures: { list: [3, 4] } },
        2082: { drawable: 134, textures: { list: [3] } },
        2083: { drawable: 141, textures: { range: [1, 4] } },
        2084: { drawable: 143, textures: { range: [1, 4] } },
        2085: { drawable: 2002, textures: { list: [2, 3] } },
        2086: { drawable: 2003, textures: { list: [2, 3] } },
        2087: { drawable: 2004, textures: { range: [6, 9] } },
        2088: { drawable: 6, textures: { list: [1] } },
        2089: { drawable: 7, textures: { list: [1] } },
        2090: { drawable: 30, textures: { list: [2] } },
        2091: { drawable: 36, textures: { list: [0, 2] } },
        2092: { drawable: 37, textures: { list: [1] } },
        2093: { drawable: 133, textures: { list: [0, 1, 3] } },
        2094: { drawable: 135, textures: { list: [3] } },
        2095: { drawable: 136, textures: { list: [3] } },
        2096: { drawable: 2001, textures: { list: [2] } },
        2097: { drawable: 2002, textures: { list: [2] } },
        2098: { drawable: 10, textures: { list: [0, 12, 14] } },
        2099: { drawable: 21, textures: { list: [0, 9] } },
        2100: { drawable: 40, textures: { list: [2, 4, 6, 9] } },
        2101: { drawable: 104, textures: { range: [0, 4], except: [3] } },
        2102: { drawable: 0, textures: { list: [0, 1] } },
        2103: { drawable: 20, textures: { list: [3] } },
        2104: { drawable: 42, textures: { list: [2, 9] } },
        2105: { drawable: 63, textures: { list: [0] } },
        2106: { drawable: 108, textures: { list: [1, 2, 4] } },
        2107: { drawable: 23, textures: { list: [10, 11] } },
        2108: { drawable: 28, textures: { list: [0, 1, 2] } },
        2109: { drawable: 29, textures: { list: [0, 1, 2] } },
        2110: { drawable: 30, textures: { list: [0, 1, 2] } },
        2111: { drawable: 31, textures: { list: [0, 1] } },
        2112: { drawable: 37, textures: { list: [0, 2, 6] } },
        2113: { drawable: 38, textures: { list: [0, 2, 6] } },
        2114: { drawable: 2000, textures: { list: [2] } },
        2115: { drawable: 2005, textures: { list: [0, 1] } },
        2116: { drawable: 2006, textures: { list: [0, 1] } },
        2117: { drawable: 2007, textures: { list: [0, 1] } },
        2118: { drawable: 2010, textures: { list: [0, 1] } },
        2119: { drawable: 2028, textures: { list: [3] } },
        2120: { drawable: 2077, textures: { list: [5, 6, 11] } },
        2121: { drawable: 2079, textures: { list: [0] } },
        2122: { drawable: 2086, textures: { list: [0] } },
        2123: { drawable: 2088, textures: { list: [0] } },
        2124: { drawable: 2090, textures: { range: [0, 4] } },
        2125: { drawable: 2093, textures: { list: [0, 5] } },
        2126: { drawable: 2101, textures: { list: [0] } },
        2127: { drawable: 2109, textures: { list: [0, 4] } },
        2128: { drawable: 20, textures: { list: [0, 6] } },
        2129: { drawable: 22, textures: { list: [0, 6] } },
        2130: { drawable: 27, textures: { list: [0, 1, 2] } },
        2131: { drawable: 28, textures: { list: [0, 6] } },
        2132: { drawable: 2003, textures: { list: [2] } },
        2133: { drawable: 2017, textures: { list: [3] } },
        2134: { drawable: 2036, textures: { list: [0] } },
        2135: { drawable: 2071, textures: { list: [0] } },
        2136: { drawable: 2072, textures: { list: [0] } },
        2137: { drawable: 2073, textures: { range: [0, 4] } },
        2138: { drawable: 2081, textures: { list: [0] } },
        2139: { drawable: 2085, textures: { list: [0] } },
        2140: { drawable: 2010, textures: { list: [0] } },
        2141: { drawable: 2011, textures: { list: [0] } },
        2142: { drawable: 2016, textures: { list: [0, 1] } },
        2143: { drawable: 2021, textures: { list: [0] } },
        2144: { drawable: 2061, textures: { list: [0, 4] } },
        2145: { drawable: 2066, textures: { list: [3, 5] } },
        2146: { drawable: 2078, textures: { range: [0, 4] } },
        2147: { drawable: 2081, textures: { list: [0, 5] } },
        2148: { drawable: 2089, textures: { list: [0] } },
        2149: { drawable: 2023, textures: { list: [0] } },
        2150: { drawable: 2062, textures: { list: [0] } },
        2151: { drawable: 2063, textures: { list: [0] } },
        2152: { drawable: 2065, textures: { list: [0] } },
        2153: { drawable: 2078, textures: { list: [0] } },
        2154: { drawable: 2079, textures: { list: [0] } },
        2155: { drawable: 2080, textures: { range: [0, 4] } },
        2156: { drawable: 2088, textures: { list: [0] } },
        2157: { drawable: 25, textures: { list: [6] } },
        2158: { drawable: 42, textures: { list: [1] } },
        2159: { drawable: 43, textures: { list: [1] } },
        2160: { drawable: 3, textures: { list: [9] } },
        2161: { drawable: 7, textures: { list: [9] } },
        2162: { drawable: 17, textures: { list: [3] } },
        2163: { drawable: 126, textures: { list: [13] } },
        2164: { drawable: 128, textures: { list: [2] } },
        2165: { drawable: 2, textures: { list: [14] } },
        2166: { drawable: 121, textures: { list: [16] } },
        2167: { drawable: 125, textures: { list: [2] } },
        2168: { drawable: 247, textures: { list: [11] } },
        2169: { drawable: 3, textures: { list: [9] } },
        2170: { drawable: 5, textures: { list: [9] } },
        2171: { drawable: 15, textures: { list: [8] } },
        2172: { drawable: 0, textures: { list: [5] } },
        2173: { drawable: 12, textures: { list: [15] } },
        2174: { drawable: 66, textures: { list: [7] } },
        2175: { drawable: 22, textures: { list: [4] } },
        2176: { drawable: 3, textures: { list: [6] } },
        2177: { drawable: 3, textures: { list: [8] } },
        2178: { drawable: 7, textures: { list: [8] } },
        2179: { drawable: 17, textures: { list: [1] } },
        2180: { drawable: 126, textures: { list: [12] } },
        2181: { drawable: 128, textures: { list: [1] } },
        2182: { drawable: 2, textures: { list: [13] } },
        2183: { drawable: 121, textures: { list: [15] } },
        2184: { drawable: 125, textures: { list: [1] } },
        2185: { drawable: 247, textures: { list: [5] } },
        2186: { drawable: 3, textures: { list: [8] } },
        2187: { drawable: 5, textures: { list: [8] } },
        2188: { drawable: 42, textures: { list: [5] } },
        2189: { drawable: 0, textures: { list: [12] } },
        2190: { drawable: 12, textures: { list: [12] } },
        2191: { drawable: 66, textures: { list: [6] } },
        2192: { drawable: 22, textures: { list: [3] } },
        2193: { drawable: 3, textures: { list: [9] } },
        2194: { drawable: 3, textures: { list: [6] } },
        2195: { drawable: 7, textures: { list: [6] } },
        2196: { drawable: 17, textures: { list: [5] } },
        2197: { drawable: 126, textures: { list: [9] } },
        2198: { drawable: 128, textures: { list: [0] } },
        2199: { drawable: 125, textures: { list: [0] } },
        2200: { drawable: 247, textures: { list: [14] } },
        2201: { drawable: 3, textures: { list: [6] } },
        2202: { drawable: 5, textures: { list: [6] } },
        2203: { drawable: 42, textures: { list: [6] } },
        2204: { drawable: 0, textures: { list: [7] } },
        2205: { drawable: 66, textures: { list: [9] } },
        2206: { drawable: 22, textures: { list: [1] } },
        2207: { drawable: 3, textures: { list: [5] } },
        2208: { drawable: 3, textures: { list: [5] } },
        2209: { drawable: 7, textures: { list: [5] } },
        2210: { drawable: 36, textures: { list: [2] } },
        2211: { drawable: 126, textures: { list: [10] } },
        2212: { drawable: 128, textures: { list: [4] } },
        2213: { drawable: 121, textures: { list: [12] } },
        2214: { drawable: 125, textures: { list: [4] } },
        2215: { drawable: 247, textures: { list: [7] } },
        2216: { drawable: 3, textures: { list: [5] } },
        2217: { drawable: 5, textures: { list: [5] } },
        2218: { drawable: 42, textures: { list: [4] } },
        2219: { drawable: 0, textures: { list: [9] } },
        2220: { drawable: 66, textures: { list: [4] } },
        2221: { drawable: 26, textures: { list: [3] } },
        2222: { drawable: 3, textures: { list: [2] } },
        2223: { drawable: 3, textures: { list: [3] } },
        2224: { drawable: 7, textures: { list: [3] } },
        2225: { drawable: 17, textures: { list: [0] } },
        2226: { drawable: 126, textures: { list: [5] } },
        2227: { drawable: 128, textures: { list: [5] } },
        2228: { drawable: 121, textures: { list: [6] } },
        2229: { drawable: 125, textures: { list: [5] } },
        2230: { drawable: 247, textures: { list: [21] } },
        2231: { drawable: 3, textures: { list: [3] } },
        2232: { drawable: 5, textures: { list: [3] } },
        2233: { drawable: 42, textures: { list: [3] } },
        2234: { drawable: 0, textures: { list: [10] } },
        2235: { drawable: 12, textures: { list: [10] } },
        2236: { drawable: 66, textures: { list: [5] } },
        2237: { drawable: 22, textures: { list: [0] } },
        2238: { drawable: 3, textures: { list: [11] } },

        // example
        2239: { drawable: 1, textures: { list: [1, 2, 3], range: [5, 7], except: [1, 3, 4] } },
        2240: { drawable: 2, textures: { list: [1], range: [5, 7] } },
        2241: { drawable: 3, textures: { list: [1, 5, 7] } },
        2242: { drawable: 4, textures: { range: [0, 10] } },
        2243: { drawable: 77, textures: { range: [0, 10] } },
    },

    fractions: {
        'lspd': {
            categories: {
                'masks': {
                    genders: {
                        'male': {
                            items: { range:[1,14]}
                        },
                        'female': {
                            items: {range:[1,18],except:[11,12,13,14]}
                        }
                    }
                },
                'head': {
                    genders: {
                        'male': {
                            items: { range:[19,53]}
                        },
                        'female': {
                            items: { list:[20,38,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73]}
                        }
                    }
                },
                'tops': {
                    genders: {
                        'male': {
                            items: { range:[74,144]}
                        },
                        'female': {
                            items: { range:[101,204],except:[103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144]}
                        }
                    }
                },
                'undershirts': {
                    genders: {
                        'male': {
                            items: { range:[205,329]}
                        },
                        'female': {
                            items: { range:[251,394],except:[252,253,254,255,257,258,259,261,262,263,264,265,266,267,268,275,277,278,281,283,284,285,286,287,289,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { range:[395,426]}
                        },
                        'female': {
                            items: { range:[419,442],except:[420,421,422,423,424,425,426]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { range:[443,451]}
                        },
                        'female': {
                            items: { range:[445,459],except:[447,448,449,450,451]}
                        }
                    }
                },
                'accessories': {
                    genders: {
                        'male': {
                            items: { range:[460,509]}
                        },
                        'female': {
                            items: { range:[472,544],except:[476,477,478,479,481,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509]}
                        }
                    }
                },
                'decals': {
                    genders: {
                        'male': {
                            items: { range:[545,571]}
                        },
                        'female': {
                            items: { range:[548,595],except:[549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,570,571]}
                        }
                    }
                },
                'glasses': {
                    genders: {
                        'male': {
                            items: { range:[596,607]}
                        },
                        'female': {
                            items: { range:[603,614],except:[604,605,606]}
                        }
                    }
                },
                'gloves': {
                    genders: {
                        'male': {
                            items: { range:[615,618]}
                        },
                        'female': {
                            items: { list:[615,617,618]}
                        }
                    }
                }
            }
        },
        'ems': {
            categories: {
                'masks': {
                    genders: {
                        'male': {
                            items: { list:[1,2,3,9,13,619,620]}
                        },
                        'female': {
                            items: { list:[1,2,3,9,16,619,620,621]}
                        }
                    }
                },
                'head': {
                    genders: {
                        'male': {
                            items: { list:[20,622,623,624,625,626,627]}
                        },
                        'female': {
                            items: { list:[20,623,624,628,629]}
                        }
                    }
                },
                'tops': {
                    genders: {
                        'male': {
                            items: { list:[104,124,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664]}
                        },
                        'female': {
                            items: { list:[78,141,147,161,162,639,640,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725]}
                        }
                    }
                },
                'undershirts': {
                    genders: {
                        'male': {
                            items: { list:[228,236,243,256,262,263,269,270,272,273,282,290,294,295,315,317,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745]}
                        },
                        'female': {
                            items: { list:[264,339,341,342,343,344,345,348,380,381,387,388,389,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { list:[398,404,422,790,791,792,793,794,795,796,797,798,799]}
                        },
                        'female': {
                            items: { list:[409,432,442,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { list:[443,445,446,824,825,826,827,828,829,830,831,832,833]}
                        },
                        'female': {
                            items: { list:[445,446,834,835,836,837,838,839,840,841,842,843]}
                        }
                    }
                },
                'accessories': {
                    genders: {
                        'male': {
                            items: { list:[471,475,483,498,503,505,508,509,516,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858]}
                        },
                        'female': {
                            items: { list:[465,480,513,515,530,536,537,543,544,859,860,861,862,863,864,865,866,867,868,869]}
                        }
                    }
                },
                'decals': {
                    genders: {
                        'male': {
                            items: { list:[557,558,560,562,564,565,567,870,871,872,873,874,875,876,877,878,879,880]}
                        },
                        'female': {
                            items: { list:[582,585,586,589,590,591,877,881,882,883,884,885,886,887,888]}
                        }
                    }
                },
                'glasses': {
                    genders: {
                        'male': {
                            items: { list:[603,604,605,607,612,889,890,891]}
                        },
                        'female': {
                            items: { list:[612,613,614,892,893,894,895,896,897]}
                        }
                    }
                },
                'gloves': {
                    genders: {
                        'male': {
                            items: { list:[617]}
                        },
                        'female': {
                            items: { list:[617]}
                        }
                    }
                }
            }
        },
        'dep': {
            categories: {
                'masks': {
                    genders: {
                        'male': {
                            items: { list:[1,2,3,4,5,9,10,11,12,13,621,898,899,900]}
                        },
                        'female': {
                            items: { list:[1,2,3,4,5,9,11,12,16,621,898,899,901]}
                        }
                    }
                },
                'head': {
                    genders: {
                        'male': {
                            items: { list:[20,31,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937]}
                        },
                        'female': {
                            items: { list:[20,33,55,58,908,918,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961]}
                        }
                    }
                },
                'tops': {
                    genders: {
                        'male': {
                            items: { list:[76,79,80,95,96,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028]}
                        },
                        'female': {
                            items: { list:[161,162,983,984,986,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088]}
                        }
                    }
                },
                'undershirts': {
                    genders: {
                        'male': {
                            items: { list:[209,210,217,218,235,236,237,243,244,245,253,256,257,261,262,263,264,265,266,267,268,269,270,271,272,273,281,282,285,289,290,295,303,315,317,322,323,325,349,363,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166]}
                        },
                        'female': {
                            items: { list:[251,269,270,271,272,280,307,338,339,341,348,349,352,355,356,360,361,363,364,365,376,377,380,381,387,388,389,1128,1129,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { list:[398,400,401,406,424,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239]}
                        },
                        'female': {
                            items: { list:[440,800,801,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { list:[445,446,1257,1258,1259,1260,1261,1262,1263]}
                        },
                        'female': {
                            items: { list:[445,446,452,455,836,1264,1265,1266,1267,1268]}
                        }
                    }
                },
                'accessories': {
                    genders: {
                        'male': {
                            items: { list:[460,461,462,473,475,483,485,486,489,490,492,493,494,498,505,508,509,516,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305]}
                        },
                        'female': {
                            items: { list:[465,472,480,482,513,515,522,523,524,525,526,527,528,530,536,537,543,544,861,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322]}
                        }
                    }
                },
                'decals': {
                    genders: {
                        'male': {
                            items: { list:[557,558,560,562,564,565,567,591,875,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340]}
                        },
                        'female': {
                            items: { list:[547,553,581,582,585,586,589,590,591,873,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357]}
                        }
                    }
                },
                'glasses': {
                    genders: {
                        'male': {
                            items: { list:[596,597,603,604,605,607,1358,1359,1360,1361]}
                        },
                        'female': {
                            items: { list:[603,608,612,613,614,1362,1363,1364,1365]}
                        }
                    }
                },
                'gloves': {
                    genders: {
                        'male': {
                            items: { range:[615,618]}
                        },
                        'female': {
                            items: { list:[615,617,618]}
                        }
                    }
                }
            }
        },
        'sang': {
            categories: {
                'masks': {
                    genders: {
                        'male': {
                            items: { list:[3,5,9,10,13,621,898,1366,1367,1368]}
                        },
                        'female': {
                            items: { list:[1,2,3,5,9,10,16,621,898,901,1366,1367]}
                        }
                    }
                },
                'head': {
                    genders: {
                        'male': {
                            items: { list:[38,623,624,909,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379]}
                        },
                        'female': {
                            items: { list:[20,904,1374,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390]}
                        }
                    }
                },
                'tops': {
                    genders: {
                        'male': {
                            items: { list:[79,964,1006,1007,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417]}
                        },
                        'female': {
                            items: { list:[668,1031,1038,1039,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442]}
                        }
                    }
                },
                'undershirts': {
                    genders: {
                        'male': {
                            items: { list:[208,263,264,265,268,269,270,290,315,317,322,323,325,726,745,1134,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478]}
                        },
                        'female': {
                            items: { list:[306,338,361,380,381,387,388,389,789,1172,1173,1174,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { list:[401,403,430,821,1223,1224,1226,1227,1496,1497,1498,1499,1500,1501,1502,1503]}
                        },
                        'female': {
                            items: { list:[407,804,1245,1246,1248,1249,1504,1505,1506,1507,1508,1509,1510]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { list:[445,446,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522]}
                        },
                        'female': {
                            items: { list:[446,834,1265,1523,1524,1525,1526,1527,1528,1529,1530]}
                        }
                    }
                },
                'decals': {
                    genders: {
                        'male': {
                            items: { list:[545,557,558,560,562,564,565,567,880,1531,1532,1533]}
                        },
                        'female': {
                            items: { list:[581,582,585,586,589,590,591,888]}
                        }
                    }
                },
                'accessories': {
                    genders: {
                        'male': {
                            items: { list:[474,483,484,485,486,489,490,495,498,503,505,508,509,1534,1535,1536,1537,1538,1539,1540,1541]}
                        },
                        'female': {
                            items: { list:[472,480,522,523,530,536,537,543,544,869,1279,1542,1543]}
                        }
                    }
                },
                'glasses': {
                    genders: {
                        'male': {
                            items: { list:[596,603,604,605,607,612,890]}
                        },
                        'female': {
                            items: { list:[603,608,612,613,614,1362,1544]}
                        }
                    }
                }
            }
        },
        'gov': {
            categories: {
                'masks': {
                    genders: {
                        'male': {
                            items: { list:[3,5,8,9,10,13,621]}
                        },
                        'female': {
                            items: { list:[2,3,5,8,9,10,15,16,621]}
                        }
                    }
                },
                'head': {
                    genders: {
                        'male': {
                            items: { list:[1545,1546]}
                        },
                        'female': {
                            items: { list:[1547,1548]}
                        }
                    }
                },
                'tops': {
                    genders: {
                        'male': {
                            items: { list:[95,96,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576]}
                        },
                        'female': {
                            items: { list:[145,146,161,162,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614]}
                        }
                    }
                },
                'undershirts': {
                    genders: {
                        'male': {
                            items: { list:[208,237,238,239,240,241,245,254,262,263,269,270,273,285,290,315,317,322,323,325,737,745,1101,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655]}
                        },
                        'female': {
                            items: { list:[221,264,269,315,316,330,331,334,338,348,356,380,381,387,388,389,747,789,1178,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { list:[398,419,420,1679,1680,1681,1682,1683,1684,1685,1686]}
                        },
                        'female': {
                            items: { list:[801,806,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { list:[446,1259,1511,1697,1698,1699,1700,1701]}
                        },
                        'female': {
                            items: { list:[445,446,836,1702,1703,1704,1705,1706,1707,1708,1709]}
                        }
                    }
                },
                'decals': {
                    genders: {
                        'male': {
                            items: { list:[557,558,562,564,565,567,589,590,880,1356,1710,1711,1712,1713,1714,1715,1716,1717,1718]}
                        },
                        'female': {
                            items: { list:[581,582,585,586,589,590,591,888,1719,1720,1721,1722,1723,1724,1725,1726]}
                        }
                    }
                },
                'accessories': {
                    genders: {
                        'male': {
                            items: { list:[460,461,462,473,475,483,484,489,490,493,498,503,505,508,509,541,1541,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749]}
                        },
                        'female': {
                            items: { list:[465,472,480,513,515,522,523,524,530,536,537,543,544,861,869,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760]}
                        }
                    }
                },
                'glasses': {
                    genders: {
                        'male': {
                            items: { list:[596,603,604,612,890]}
                        },
                        'female': {
                            items: { list:[603,608,609,612,1362]}
                        }
                    }
                }
            }
        },
        'news': {
            categories: {
                'masks': {
                    genders: {
                        'male': {
                            items: { list:[1,3,5,8,9,13,621,1761]}
                        },
                        'female': {
                            items: { list:[2,3,5,8,9,10,15,16,621]}
                        }
                    }
                },
                'tops': {
                    genders: {
                        'male': {
                            items: { list:[95,96,147,963,1391,1552,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788]}
                        },
                        'female': {
                            items: { list:[665,1577,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808]}
                        }
                    }
                },
                'undershirts': {
                    genders: {
                        'male': {
                            items: { list:[218,238,239,240,241,263,269,285,315,317,322,323,325,387,729,737,745,1628,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825]}
                        },
                        'female': {
                            items: { list:[269,308,356,360,361,380,381,387,388,389,777,778,779,782,789,1171,1821,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { list:[1679,1680,1840,1841,1842,1843,1844,1845,1846]}
                        },
                        'female': {
                            items: { list:[800,801,802,1847,1848,1849,1850,1851,1852]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { list:[443,827,1700,1853,1854]}
                        },
                        'female': {
                            items: { list:[446,836,1702,1705,1855,1856,1857,1858,1859]}
                        }
                    }
                },
                'accessories': {
                    genders: {
                        'male': {
                            items: { list:[461,475,483,498,503,505,508,509,516,1541,1735,1738,1860,1861,1862,1863,1864,1865,1866]}
                        },
                        'female': {
                            items: { list:[480,483,515,530,536,537,543,544,1867,1868,1869,1870,1871,1872,1873,1874]}
                        }
                    }
                },
                'decals': {
                    genders: {
                        'male': {
                            items: { list:[557,558,560,562,564,565,567,880,1875]}
                        },
                        'female': {
                            items: { list:[581,582,585,586,589,590,591,888,1722,1876]}
                        }
                    }
                },
                'glasses': {
                    genders: {
                        'male': {
                            items: { list:[596,604,612,890]}
                        },
                        'female': {
                            items: { list:[608,612,1362]}
                        }
                    }
                }
            }
        },
        'fib': {
            categories: {
                'masks': {
                    genders: {
                        'male': {
                            items: { list:[1,2,3,4,5,8,9,10,12,13,621,898,1877]}
                        },
                        'female': {
                            items: { list:[1,2,3,4,5,8,9,10,12,16,621,898,1878]}
                        }
                    }
                },
                'head': {
                    genders: {
                        'male': {
                            items: { list:[20,24,25,31,34,35,36,38,44,623,624,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899]}
                        },
                        'female': {
                            items: { list:[20,66,623,624,1376,1879,1883,1884,1885,1887,1896,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914]}
                        }
                    }
                },
                'tops': {
                    genders: {
                        'male': {
                            items: { list:[79,80,95,96,170,1570,1571,1774,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959]}
                        },
                        'female': {
                            items: { list:[145,146,668,1580,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998]}
                        }
                    }
                },
                'undershirts': {
                    genders: {
                        'male': {
                            items: { list:[208,209,210,217,218,233,234,235,236,237,238,239,240,241,242,243,244,245,251,253,256,257,261,262,263,264,265,266,267,268,269,270,271,281,282,284,285,289,290,303,315,317,322,323,325,338,349,359,363,745,754,758,771,772,1124,1125,1127,1135,1144,1448,1820,1839,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046]}
                        },
                        'female': {
                            items: { list:[257,269,270,273,277,290,295,318,319,334,338,339,340,341,348,349,356,361,363,364,367,380,381,387,388,389,747,789,1107,1158,1177,1178,1185,1186,1647,2025,2037,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { list:[398,401,406,407,424,1679,1680,1686,1844,1845,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087]}
                        },
                        'female': {
                            items: { list:[406,418,442,804,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { list:[445,446,448,449,450,1699,2098,2099,2100,2101]}
                        },
                        'female': {
                            items: { list:[445,446,453,454,455,458,1266,2102,2103,2104,2105,2106]}
                        }
                    }
                },
                'accessories': {
                    genders: {
                        'male': {
                            items: { list:[461,462,473,474,475,483,484,485,486,489,490,491,492,493,494,495,498,503,505,508,509,516,1287,1292,1294,1295,1541,1860,1862,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127]}
                        },
                        'female': {
                            items: { list:[465,472,473,474,480,482,515,522,523,524,527,530,536,537,543,544,869,1313,1868,2122,2123,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139]}
                        }
                    }
                },
                'decals': {
                    genders: {
                        'male': {
                            items: { list:[557,558,560,562,564,565,567,875,880,1720,1722,2140,2141,2142,2143,2144,2145,2146,2147,2148]}
                        },
                        'female': {
                            items: { list:[581,582,585,586,589,590,591,888,1329,1330,2149,2150,2151,2152,2153,2154,2155,2156]}
                        }
                    }
                },
                'glasses': {
                    genders: {
                        'male': {
                            items: { range:[596,612],except:[602,606,608,609,610,611]}
                        },
                        'female': {
                            items: { list:[603,608,609,612,613,614,1362,2157,2158,2159]}
                        }
                    }
                },
                'gloves': {
                    genders: {
                        'male': {
                            items: { list:[615,617,618]}
                        },
                        'female': {
                            items: { list:[615,617,618]}
                        }
                    }
                }
            }
        },
        'ballas': {
            categories: {
                'tops': {
                    genders: {
                        'male': {
                            items: { range:[2160,2164]}
                        },
                        'female': {
                            items: { range:[2165,2168]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { list:[2169,2170,2171]}
                        },
                        'female': {
                            items: { list:[2172,2173,2174]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { list:[2175]}
                        },
                        'female': {
                            items: { list:[2176]}
                        }
                    }
                }
            }
        },
        'vagos': {
            categories: {
                'tops': {
                    genders: {
                        'male': {
                            items: { range:[2177,2181]}
                        },
                        'female': {
                            items: { range:[2182,2185]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { list:[2186,2187,2188]}
                        },
                        'female': {
                            items: { list:[2189,2190,2191]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { list:[2192]}
                        },
                        'female': {
                            items: { list:[2193]}
                        }
                    }
                }
            }
        },
        'families': {
            categories: {
                'tops': {
                    genders: {
                        'male': {
                            items: { range:[2194,2198]}
                        },
                        'female': {
                            items: { list:[2183,2199,2200]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { list:[2201,2202,2203]}
                        },
                        'female': {
                            items: { list:[2204,2205]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { list:[2206]}
                        },
                        'female': {
                            items: { list:[2207]}
                        }
                    }
                }
            }
        },
        'bloods': {
            categories: {
                'tops': {
                    genders: {
                        'male': {
                            items: { range:[2208,2212]}
                        },
                        'female': {
                            items: { list:[2213,2214,2215]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { list:[2216,2217,2218]}
                        },
                        'female': {
                            items: { list:[2219,2220]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { list:[2221]}
                        },
                        'female': {
                            items: { list:[2222]}
                        }
                    }
                }
            }
        },
        'marabunta': {
            categories: {
                'tops': {
                    genders: {
                        'male': {
                            items: { range:[2223,2227]}
                        },
                        'female': {
                            items: { list:[2228,2229,2230]}
                        }
                    }
                },
                'legs': {
                    genders: {
                        'male': {
                            items: { list:[2231,2232,2233]}
                        },
                        'female': {
                            items: { list:[2234,2235,2236]}
                        }
                    }
                },
                'shoes': {
                    genders: {
                        'male': {
                            items: { list:[2237]}
                        },
                        'female': {
                            items: { list:[2238]}
                        }
                    }
                }
            }
        }
    }
}