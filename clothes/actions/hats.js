module.exports = [
    {
        44: [{ 'capOff': { newDrawable: 45 } }],
        45: [{ 'capOn': { newDrawable: 44 } }],
        66: [{ 'capOn': { newDrawable: 65 } }],
        65: [{ 'capOff': { newDrawable: 66 } }],
        77: [{ 'capOn': { newDrawable: 76 } }],
        76: [{ 'capOff': { newDrawable: 77 } }],
        110: [{ 'capOn': { newDrawable: 109 } }],
        109: [{ 'capOff': { newDrawable: 110 } }],
        131: [{ 'capOn': { newDrawable: 130 } }],
        130: [{ 'capOff': { newDrawable: 131 } }],
        136: [{ 'capOn': { newDrawable: 135 } }],
        135: [{ 'capOff': { newDrawable: 136 } }],
        140: [{ 'capOn': { newDrawable: 139 } }],
        139: [{ 'capOff': { newDrawable: 140 } }],
        143: [{ 'capOn': { newDrawable: 142 } }],
        142: [{ 'capOff': { newDrawable: 143 } }],
        152: [{ 'capOn': { newDrawable: 151 } }],
        151: [{ 'capOff': { newDrawable: 152 } }],
        155: [{ 'capOn': { newDrawable: 154 } }],
        154: [{ 'capOff': { newDrawable: 155 } }],
        157: [{ 'capOn': { newDrawable: 156 } }],
        156: [{ 'capOff': { newDrawable: 157 } }],
        161: [{ 'capOn': { newDrawable: 159 } }],
        159: [{ 'capOff': { newDrawable: 161 } }],
        160: [{ 'capOn': { newDrawable: 158 } }],
        158: [{ 'capOff': { newDrawable: 160 } }],
        163: [{ 'capOn': { newDrawable: 162 } }],
        162: [{ 'capOff': { newDrawable: 163 } }],
        164: [{ 'capOn': { newDrawable: 165 } }],
        165: [{ 'capOff': { newDrawable: 164 } }],
        168: [{ 'capOn': { newDrawable: 166 } }],
        166: [{ 'capOff': { newDrawable: 168 } }],
        171: [{ 'capOn': { newDrawable: 167 } }],
        167: [{ 'capOff': { newDrawable: 171 } }],
        2022: [{ 'capOn': { newDrawable: 2021 } }],
        2021: [{ 'capOff': { newDrawable: 2022 } }],
        2030: [{ 'capOn': { newDrawable: 2029 } }],
        2029: [{ 'capOff': { newDrawable: 2030 } }],
        2068: [{ 'capOn': { newDrawable: 2067 } }],
        2067: [{ 'capOff': { newDrawable: 2068 } }],
        2088: [{'TieOnFace': { newDrawable: 2146, newComponent: 1, isProp: 0 }} ],
        2090: [{ 'VisorOn': { newDrawable: 2091 } }],
        2091: [{ 'VisorOff': { newDrawable: 2090 } }],
    },
    {
        43: [{ 'capOff': { newDrawable: 44 } }],
        44: [{ 'capOn': { newDrawable: 43 } }],
        64: [{ 'capOff': { newDrawable: 65 } }],
        65: [{ 'capOn': { newDrawable: 64 } }],
        75: [{ 'capOff': { newDrawable: 76 } }],
        76: [{ 'capOn': { newDrawable: 75 } }],
        108: [{ 'capOff': { newDrawable: 109 } }],
        109: [{ 'capOn': { newDrawable: 108 } }],
        129: [{ 'capOff': { newDrawable: 130 } }],
        130: [{ 'capOn': { newDrawable: 129 } }],
        134: [{ 'capOff': { newDrawable: 135 } }],
        135: [{ 'capOn': { newDrawable: 134 } }],
        138: [{ 'capOff': { newDrawable: 139 } }],
        139: [{ 'capOn': { newDrawable: 138 } }],
        141: [{ 'capOff': { newDrawable: 142 } }],
        142: [{ 'capOn': { newDrawable: 141 } }],
        150: [{ 'capOff': { newDrawable: 151 } }],
        151: [{ 'capOn': { newDrawable: 150 } }],
        153: [{ 'capOff': { newDrawable: 154 } }],
        154: [{ 'capOn': { newDrawable: 153 } }],
        155: [{ 'capOff': { newDrawable: 156 } }],
        156: [{ 'capOn': { newDrawable: 155 } }],
        157: [{ 'capOff': { newDrawable: 159 } }],
        159: [{ 'capOn': { newDrawable: 157 } }],
        158: [{ 'capOff': { newDrawable: 160 } }],
        160: [{ 'capOn': { newDrawable: 158 } }],
        161: [{ 'capOff': { newDrawable: 162 } }],
        162: [{ 'capOn': { newDrawable: 161 } }],
        164: [{ 'capOff': { newDrawable: 163 } }],
        163: [{ 'capOn': { newDrawable: 164 } }],
        165: [{ 'capOff': { newDrawable: 167 } }],
        167: [{ 'capOn': { newDrawable: 165 } }],
        166: [{ 'capOff': { newDrawable: 170 } }],
        170: [{ 'capOn': { newDrawable: 166 } }],
        2027: [{ 'capOff': { newDrawable: 2090 } }],
        2090: [{ 'capOn': { newDrawable: 2027 } }],
        2035: [{ 'capOff': { newDrawable: 2036 } }],
        2036: [{ 'capOn': { newDrawable: 2035 } }],
        2075: [{ 'capOff': { newDrawable: 2076 } }],
        2076: [{ 'capOn': { newDrawable: 2075 } }],
        2091: [{'TieOnFace': { newDrawable: 2133, newComponent: 1, isProp: 0 }} ],
        2094: [{ 'VisorOn': { newDrawable: 2095 } }],
        2095: [{ 'VisorOff': { newDrawable: 2094 } }],
    }
]
