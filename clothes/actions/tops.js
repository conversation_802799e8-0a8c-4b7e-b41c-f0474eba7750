module.exports = [
    {
        29: [{ 'buttonUp': { newDrawable: 30 } }],
        30: [{ 'unbutton': { newDrawable: 29 } }],
        31: [{ 'buttonUp': { newDrawable: 32 } }],
        32: [{ 'unbutton': { newDrawable: 31 } }],
        43: [{ 'buttonUp': { newDrawable: 42 } }],
        42: [{ 'unbutton': { newDrawable: 43 } }],
        59: [{ 'buttonUp': { newDrawable: 60 } }],
        60: [{ 'unbutton': { newDrawable: 59 } }],
        69: [{ 'hoodOn': { newDrawable: 68 } }],
        68: [{ 'hoodOff': { newDrawable: 69 } }],
        74: [{ 'buttonUp': { newDrawable: 75 } }],
        75: [{ 'unbutton': { newDrawable: 74 } }],
        88: [{ 'buttonUp': { newDrawable: 87 } }],
        87: [{ 'unbutton': { newDrawable: 88 } }],
        93: [{ 'tuck': { newDrawable: 94 } }],
        94: [{ 'untuck': { newDrawable: 93 } }],
        99: [{ 'buttonUp': { newDrawable: 100 } }],
        100: [{ 'unbutton': { newDrawable: 99 } }],
        101: [{ 'buttonUp': { newDrawable: 102 } }],
        102: [{ 'unbutton': { newDrawable: 101 } }],
        103: [{ 'buttonUp': { newDrawable: 104 } }],
        104: [{ 'unbutton': { newDrawable: 103 } }],
        127: [{ 'buttonUp': { newDrawable: 126 } }],
        126: [{ 'unbutton': { newDrawable: 127 } }],
        130: [{ 'buttonUp': { newDrawable: 129 } }],
        129: [{ 'unbutton': { newDrawable: 130 } }],
        131: [{ 'tuck': { newDrawable: 132 } }],
        132: [{ 'untuck': { newDrawable: 131 } }],
        185: [{ 'buttonUp': { newDrawable: 184 } }],
        184: [{ 'unbutton': { newDrawable: 185 } }],
        189: [{ 'buttonUp': { newDrawable: 188 } }],
        188: [{ 'unbutton': { newDrawable: 189 } }],
        206: [{ 'hoodOn': { newDrawable: 207 } }],
        207: [{ 'hoodOff': { newDrawable: 206 } }],
        210: [{ 'hoodOn': { newDrawable: 211 } }, { 'unbutton': { newDrawable: 212 } }],
        211: [{ 'hoodOff': { newDrawable: 210 } }, { 'unbutton': { newDrawable: 212 } }],
        212: [{ 'hoodOn': { newDrawable: 211 } }, { 'buttonUp': { newDrawable: 210 } }],
        217: [{ 'hoodOn': { newDrawable: 218 } }],
        218: [{ 'hoodOff': { newDrawable: 217 } }],
        230: [{ 'buttonUp': { newDrawable: 229 } }],
        229: [{ 'unbutton': { newDrawable: 230 } }],
        233: [{ 'buttonUp': { newDrawable: 232 } }],
        232: [{ 'unbutton': { newDrawable: 233 } }],
        235: [{ 'tuck': { newDrawable: 236 } }],
        236: [{ 'untuck': { newDrawable: 235 } }],
        241: [{ 'tuck': { newDrawable: 242 } }],
        242: [{ 'untuck': { newDrawable: 241 } }],
        251: [{ 'hoodOn': { newDrawable: 253 } }],
        253: [{ 'hoodOff': { newDrawable: 251 } }],
        261: [{ 'buttonUp': { newDrawable: 256 } }],
        256: [{ 'unbutton': { newDrawable: 261 } }],
        262: [{ 'hoodOn': { newDrawable: 263 } }],
        263: [{ 'hoodOff': { newDrawable: 262 } }],
        266: [{ 'buttonUp': { newDrawable: 265 } }],
        265: [{ 'unbutton': { newDrawable: 266 } }],
        268: [{ 'buttonUp': { newDrawable: 267 } }],
        267: [{ 'unbutton': { newDrawable: 268 } }],
        279: [{ 'hoodOn': { newDrawable: 280 } }],
        280: [{ 'hoodOff': { newDrawable: 279 } }],
        292: [{ 'buttonUp': { newDrawable: 293 } }],
        293: [{ 'unbutton': { newDrawable: 292 } }],
        294: [{ 'buttonUp': { newDrawable: 295 } }],
        295: [{ 'unbutton': { newDrawable: 294 } }],
        296: [{ 'hoodOn': { newDrawable: 297 } }],
        297: [{ 'hoodOff': { newDrawable: 296 } }],
        300: [{ 'unbutton': { newDrawable: 301 } }],
        // 301: [{'buttonUp': {newDrawable: 300}} ],
        301: [{ 'hoodOn': { newDrawable: 302 } }, { 'unbutton': { newDrawable: 303 } }],
        302: [{ 'hoodOff': { newDrawable: 301 } }, { 'unbutton': { newDrawable: 303 } }],
        303: [{ 'hoodOn': { newDrawable: 302 } }, { 'buttonUp': { newDrawable: 301 } }],
        305: [{ 'hoodOn': { newDrawable: 306 } }],
        306: [{ 'hoodOff': { newDrawable: 305 } }],
        311: [{ 'buttonUp': { newDrawable: 312 } }],
        312: [{ 'unbutton': { newDrawable: 311 } }],
        315: [{ 'buttonUp': { newDrawable: 314 } }],
        314: [{ 'unbutton': { newDrawable: 315 } }],
        317: [{ 'buttonUp': { newDrawable: 316 } }],
        319: [{ 'buttonUp': { newDrawable: 318 } }],
        318: [{ 'unbutton': { newDrawable: 319 } }],
        321: [{ 'buttonUp': { newDrawable: 322 } }],
        322: [{ 'unbutton': { newDrawable: 321 } }],
        330: [{ 'hoodOn': { newDrawable: 331 } }],
        331: [{ 'hoodOff': { newDrawable: 330 } }],
        340: [{ 'buttonUp': { newDrawable: 341 } }],
        341: [{ 'unbutton': { newDrawable: 340 } }],
        344: [{ 'buttonUp': { newDrawable: 343 } }],
        343: [{ 'unbutton': { newDrawable: 344 } }],
        348: [{ 'buttonUp': { newDrawable: 349 } }],
        349: [{ 'unbutton': { newDrawable: 348 } }],
        355: [{ 'buttonUp': { newDrawable: 354 } }],
        354: [{ 'unbutton': { newDrawable: 355 } }],
        360: [{ 'buttonUp': { newDrawable: 359 } }],
        359: [{ 'unbutton': { newDrawable: 360 } }],
        374: [{ 'hoodOn': { newDrawable: 373 } }],
        373: [{ 'hoodOff': { newDrawable: 374 } }],
        376: [{ 'buttonUp': { newDrawable: 375 } }],
        375: [{ 'unbutton': { newDrawable: 376 } }],
        382: [{ 'tuck': { newDrawable: 383 } }],
        383: [{ 'untuck': { newDrawable: 382 } }],
        384: [{ 'hoodOn': { newDrawable: 385 } }],
        385: [{ 'hoodOff': { newDrawable: 384 } }],
        387: [{ 'buttonUp': { newDrawable: 386 } }],
        386: [{ 'unbutton': { newDrawable: 387 } }],
        390: [{ 'buttonUp': { newDrawable: 388 } }],
        388: [{ 'unbutton': { newDrawable: 390 } }],
        391: [{ 'buttonUp': { newDrawable: 389 } }],
        389: [{ 'unbutton': { newDrawable: 391 } }],
        400: [{ 'hoodOn': { newDrawable: 401 } }],
        401: [{ 'hoodOff': { newDrawable: 400 } }],
        403: [{ 'buttonUp': { newDrawable: 402 } }],
        402: [{ 'unbutton': { newDrawable: 403 } }],
        409: [{ 'buttonUp': { newDrawable: 408 } }],
        408: [{ 'unbutton': { newDrawable: 409 } }],
        2009: [{ 'hoodOn': { newDrawable: 2010 } }, { 'unbutton': { newDrawable: 2011 } }],
        2010: [{ 'hoodOff': { newDrawable: 2009 } }, { 'unbutton': { newDrawable: 2011 } }],
        2011: [{ 'hoodOn': { newDrawable: 2010 } }, { 'buttonUp': { newDrawable: 2009 } }],
        2022: [{ 'buttonUp': { newDrawable: 2021 } }],
        2021: [{ 'unbutton': { newDrawable: 2022 } }],
        2032: [{ 'hoodOn': { newDrawable: 2033 } }],
        2033: [{ 'hoodOff': { newDrawable: 2032 } }],
        2034: [{ 'hoodOn': { newDrawable: 2035 } }],
        2035: [{ 'hoodOff': { newDrawable: 2034 } }],
        2062: [{ 'buttonUp': { newDrawable: 2061 } }],
        2061: [{ 'unbutton': { newDrawable: 2062 } }],
        2064: [{ 'hoodOff': { newDrawable: 2063 } }],
        2063: [{ 'hoodOn': { newDrawable: 2064 } }],
        2239: [{ 'hoodOn': { newDrawable: 2240 } }],
        2240: [{ 'hoodOff': { newDrawable: 2239 } }],
        2313: [{ 'buttonUp': { newDrawable: 2314 } }],
        2314: [{ 'unbutton': { newDrawable: 2313 } }],
        2317: [{ 'tuck': { newDrawable: 2316 } }],
        2316: [{ 'untuck': { newDrawable: 2317 } }],
        2330: [{ 'buttonUp': { newDrawable: 2331 } }],
        2331: [{ 'unbutton': { newDrawable: 2330 } }],  
        // 2337: [{'hoodOff': {newDrawable: 2336}} ],
        2336: [{ 'hoodOn': { newDrawable: 2337 } }],
        2338: [{ 'buttonUp': { newDrawable: 2337 } }, { 'hoodOn': { newDrawable: 2337 } }],
        2337: [{ 'unbutton': { newDrawable: 2338 } }, { 'hoodOff': { newDrawable: 2338 } }],
        2355: [{ 'unbutton': { newDrawable: 2354 } }],
        2354: [{ 'buttonUp': { newDrawable: 2355 } }],
        2360: [{ 'unbutton': { newDrawable: 2361 } }],
        2361: [{ 'buttonUp': { newDrawable: 2360 } }],
        2401: [{ 'unbutton': { newDrawable: 2402 } }],
        2402: [{ 'buttonUp': { newDrawable: 2401 } }],
        2414: [{ 'hoodOff': { newDrawable: 2415 } }],
        2415: [{ 'hoodOn': { newDrawable: 2414 } }],
        2416: [{ 'turnout': { newDrawable: 2417 } }],
        2417: [{ 'turnout': { newDrawable: 2416 } }],
        2418: [{ 'hoodOff': { newDrawable: 2419 } }],
        2419: [{ 'hoodOn': { newDrawable: 2418 } }],
        2441: [{ 'hoodOn': { newDrawable: 2440 } }],
        2440: [{ 'hoodOff': { newDrawable: 2441 } }],
        2444: [{ 'hoodOff': { newDrawable: 2443 } }],
        2443: [{ 'hoodOn': { newDrawable: 2444 } }],
        2447: [{ 'hoodOff': { newDrawable: 2446 } }],
        2446: [{ 'hoodOn': { newDrawable: 2447 } }],
        2450: [{ 'hoodOff': { newDrawable: 2451 } }],
        2451: [{ 'hoodOn': { newDrawable: 2450 } }],
        2455: [{ 'buttonUp': { newDrawable: 2454 } }],
        2454: [{ 'unbutton': { newDrawable: 2455 } }],
        2457: [{ 'turnout': { newDrawable: 2463 } }],
        2463: [{ 'turnout': { newDrawable: 2457 } }],
        2465: [{ 'hoodOff': { newDrawable: 2464 } }],
        2464: [{ 'hoodOn': { newDrawable: 2465 } }],
        2467: [{ 'hoodOff': { newDrawable: 2466 } }],
        2466: [{ 'hoodOn': { newDrawable: 2467 } }],
        2481: [{ 'hoodOff': { newDrawable: 2482 } }],
        2482: [{ 'hoodOn': { newDrawable: 2481 } }],
        2489: [{ 'buttonUp': { newDrawable: 2488 } }],
        2488: [{ 'unbutton': { newDrawable: 2489 } }],
        2498: [{ 'hoodOff': { newDrawable: 2499 } }],
        2499: [{ 'hoodOn': { newDrawable: 2498 } }],
        2513: [{ 'WearOffBalaclava': { newDrawable: 2515 } }, { 'WearOnBalaclavaHood': { newDrawable: 2516 } }],
        2515: [{ 'WearOnBalaclava': { newDrawable: 2513 } }, { 'WearOnBalaclavaHood': { newDrawable: 2516 } }],
        2516: [{ 'hoodOff': { newDrawable: 2513 } }, { 'WearOffBalaclavaHood': { newDrawable: 2515 } }],
    },
    {
        52: [{ 'buttonUp': { newDrawable: 53 } }],
        53: [{ 'unbutton': { newDrawable: 52 } }],
        57: [{ 'buttonUp': { newDrawable: 58 } }],
        58: [{ 'unbutton': { newDrawable: 57 } }],
        62: [{ 'hoodOff': { newDrawable: 63 } },],
        63: [{ 'hoodOn': { newDrawable: 62 } },],
        84: [{ 'tuck': { newDrawable: 85 } }],
        85: [{ 'untuck': { newDrawable: 84 } }],
        90: [{ 'buttonUp': { newDrawable: 91 } }],
        91: [{ 'unbutton': { newDrawable: 90 } }],
        92: [{ 'buttonUp': { newDrawable: 93 } }],
        93: [{ 'unbutton': { newDrawable: 92 } }],
        94: [{ 'buttonUp': { newDrawable: 95 } }],
        95: [{ 'unbutton': { newDrawable: 94 } }],
        117: [{ 'untuck': { newDrawable: 118 } }],
        118: [{ 'tuck': { newDrawable: 117 } }],
        120: [{ 'buttonUp': { newDrawable: 121 } }],
        121: [{ 'unbutton': { newDrawable: 120 } }],
        129: [{ 'untuck': { newDrawable: 128 } }],
        128: [{ 'tuck': { newDrawable: 129 } }],
        187: [{ 'buttonUp': { newDrawable: 186 }  }],
        186: [{ 'unbutton': { newDrawable: 187 } }],
        189: [{ 'hoodOn': { newDrawable: 206 } }],
        206: [{ 'hoodOff': { newDrawable: 189 } }],
        191: [{ 'buttonUp': { newDrawable: 190 } }],
        190: [{ 'unbutton': { newDrawable: 191 } }],
        208: [{ 'untuck': { newDrawable: 209 } }],
        209: [{ 'tuck': { newDrawable: 208 } }],
        202: [{ 'hoodOn': { newDrawable: 205 } }],
        205: [{ 'hoodOff': { newDrawable: 202 } }],
        211: [{ 'hoodOff': { newDrawable: 210 } },],
        210: [{ 'hoodOn': { newDrawable: 211 } },],
        214: [{ 'hoodOn': { newDrawable: 215 } }, { 'unbutton': { newDrawable: 216 } }],
        215: [{ 'hoodOff': { newDrawable: 214 } }, { 'unbutton': { newDrawable: 216 } }],
        216: [{ 'hoodOn': { newDrawable: 215 } }, { 'buttonUp': { newDrawable: 214 } }],
        226: [{ 'untuck': { newDrawable: 225 } }],
        225: [{ 'tuck': { newDrawable: 226 } }],
        228: [{ 'hoodOff': { newDrawable: 227 } },],
        227: [{ 'hoodOn': { newDrawable: 228 } },],
        240: [{ 'buttonUp': { newDrawable: 239 } }],
        239: [{ 'unbutton': { newDrawable: 240 } }],
        243: [{ 'buttonUp': { newDrawable: 242 } }],
        242: [{ 'unbutton': { newDrawable: 243 } }],
        246: [{ 'untuck': { newDrawable: 245 }  }],
        245: [{ 'tuck': { newDrawable: 246 } }],
        250: [{ 'untuck': { newDrawable: 249 } }],
        249: [{ 'tuck': { newDrawable: 250 } }],
        261: [{ 'hoodOff': { newDrawable: 259 } },],
        259: [{ 'hoodOn': { newDrawable: 261 } },],
        265: [{ 'buttonUp': { newDrawable: 270 } }],
        270: [{ 'unbutton': { newDrawable: 265 } }],
        272: [{ 'hoodOff': { newDrawable: 271 }  },],
        271: [{ 'hoodOn': { newDrawable: 272 }  },],
        275: [{ 'buttonUp': { newDrawable: 274 } }],
        274: [{ 'unbutton': { newDrawable: 275 } }],
        277: [{ 'buttonUp': { newDrawable: 276 }  }],
        276: [{ 'unbutton': { newDrawable: 277 } }],
        281: [{ 'untuck': { newDrawable: 280 } }],
        280: [{ 'tuck': { newDrawable: 281 } }],
        293: [{ 'hoodOff': { newDrawable: 292 } },],
        292: [{ 'hoodOn': { newDrawable: 293 } },],
        305: [{ 'buttonUp': { newDrawable: 306 }  }],
        306: [{ 'unbutton': { newDrawable: 305 }  }],
        308: [{ 'hoodOff': { newDrawable: 307 } },],
        307: [{ 'hoodOn': { newDrawable: 308 } },],
        312: [{ 'hoodOn': { newDrawable: 313 } }, { 'unbutton': { newDrawable: 314 } }],
        313: [{ 'hoodOff': { newDrawable: 312 } }, { 'unbutton': { newDrawable: 314 } }],
        314: [{ 'hoodOn': { newDrawable: 313 } }, { 'buttonUp': { newDrawable: 312 } }],
        317: [{ 'hoodOff': { newDrawable: 316 } },],
        316: [{ 'hoodOn': { newDrawable: 317 } },],
        326: [{ 'buttonUp': { newDrawable: 325 } }],
        325: [{ 'unbutton': { newDrawable: 326 } }],
        328: [{ 'buttonUp': { newDrawable: 327 } }],
        327: [{ 'unbutton': { newDrawable: 328 } }],
        330: [{ 'buttonUp': { newDrawable: 329 } }],
        329: [{ 'unbutton': { newDrawable: 330 } }],
        332: [{ 'buttonUp': { newDrawable: 333 }  }],
        333: [{ 'unbutton': { newDrawable: 332 }  }],
        339: [{ 'buttonUp': { newDrawable: 340 } }],
        340: [{ 'unbutton': { newDrawable: 339 } }],
        346: [{ 'hoodOff': { newDrawable: 345 } },],
        345: [{ 'hoodOn': { newDrawable: 346 } },],
        355: [{ 'buttonUp': { newDrawable: 356 } }],
        356: [{ 'unbutton': { newDrawable: 355 } }],
        363: [{ 'buttonUp': { newDrawable: 362 } }],
        362: [{ 'unbutton': { newDrawable: 363 } }],
        366: [{ 'buttonUp': { newDrawable: 367 } }],
        367: [{ 'unbutton': { newDrawable: 366 } }],
        370: [{ 'buttonUp': { newDrawable: 371 } }],
        371: [{ 'unbutton': { newDrawable: 370 } }],
        373: [{ 'buttonUp': { newDrawable: 372 } }],
        372: [{ 'unbutton': { newDrawable: 373 } }],
        379: [{ 'buttonUp': { newDrawable: 378 } }],
        378: [{ 'unbutton': { newDrawable: 379 } }],
        392: [{ 'hoodOn': { newDrawable: 393 }  }],
        393: [{ 'hoodOff': { newDrawable: 392 }  }],
        400: [{ 'tuck': { newDrawable: 401 } }],
        401: [{ 'untuck': { newDrawable: 400 }  }],
        403: [{ 'buttonUp': { newDrawable: 402 } }],
        402: [{ 'unbutton': { newDrawable: 403 } }],
        408: [{ 'hoodOff': { newDrawable: 407 } },],
        407: [{ 'hoodOn': { newDrawable: 408 } },],
        410: [{ 'unbutton': { newDrawable: 412 } }],
        412: [{ 'buttonOn': { newDrawable: 410 } }],
        409: [{ 'unbutton': { newDrawable: 411 } }],
        411: [{ 'buttonUp': { newDrawable: 409 } }],
        414: [{ 'untuck': { newDrawable: 413 } }],
        413: [{ 'tuck': { newDrawable: 414 } }],
        426: [{ 'unbutton': { newDrawable: 427 } }],
        427: [{ 'buttonUp': { newDrawable: 426 } }],
        431: [{ 'untuck': { newDrawable: 430 } }],
        430: [{ 'tuck': { newDrawable: 431 } }],
        436: [{ 'hoodOff': { newDrawable: 439 } },],
        439: [{ 'hoodOn': { newDrawable: 436 } },],
        2004: [{ 'unbutton': { newDrawable: 2003 } }],
        2003: [{ 'buttonUp': { newDrawable: 2004 } }],
        2008: [{ 'hoodOn': { newDrawable: 2007 } }, { 'unbutton': { newDrawable: 2009 } }],
        2007: [{ 'hoodOff': { newDrawable: 2008 } }, { 'unbutton': { newDrawable: 2009 } }],
        2009: [{ 'hoodOn': { newDrawable: 2007 } }, { 'buttonUp': { newDrawable: 2008 } }],
        2024: [{ 'hoodOff': { newDrawable: 2023 } },],
        2023: [{ 'hoodOn': { newDrawable: 2024 }  },],
        2032: [{ 'hoodOff': { newDrawable: 2031 }  },],
        2031: [{ 'hoodOn': { newDrawable: 2032 } },],
        2025: [{ 'buttonUp': { newDrawable: 2034 }  }],
        2034: [{ 'unbutton': { newDrawable: 2025 } }],
        2047: [{ 'buttonUp': { newDrawable: 2046 }  }],
        2046: [{ 'unbutton': { newDrawable: 2047 } }],
        2049: [{ 'hoodOff': { newDrawable: 2048 } },],
        2048: [{ 'hoodOn': { newDrawable: 2049 } },],
        2251: [{ 'hoodOff': { newDrawable: 2250 }  },],
        2250: [{ 'hoodOn': { newDrawable: 2251 } },],
        2293: [{ 'unbutton': { newDrawable: 2292 } }],
        2292: [{ 'buttonUp': { newDrawable: 2293 } }],
        2326: [{ 'buttonUp': { newDrawable: 2325 } }, { 'hoodOn': { newDrawable: 2325 } }],
        2325: [{ 'unbutton': { newDrawable: 2326 } }, { 'hoodOff': { newDrawable: 2326 } }],
        2397: [{ 'sleeveOff': { newDrawable: 2398 } }],
        2398: [{ 'sleeveOn': { newDrawable: 2397 }  }],
        2438: [{ 'hoodOn': { newDrawable: 2437 } }],
        2437: [{ 'hoodOff': { newDrawable: 2438 } }],
        2444: [{ 'PutOnSweater': { newDrawable: 2445 } }],
        2445: [{ 'PutOffSweater': { newDrawable: 2444 }  }],
        2461: [{ 'buttonUp': { newDrawable: 2460 } }],
        2460: [{ 'unbutton': { newDrawable: 2461 } }],
        2469: [{ 'hoodOff': { newDrawable: 2470 } }],
        2470: [{ 'hoodOn': { newDrawable: 2469 } }],
        2489: [{ 'hoodOff': { newDrawable: 2490 } }],
        2490: [{ 'hoodOn': { newDrawable: 2489 } }],
        2491: [{ 'hoodOff': { newDrawable: 2492 } }],
        2492: [{ 'hoodOn': { newDrawable: 2491 } }],
        2500: [{ 'PutOnSweater': { newDrawable: 2501 } }],
        2501: [{ 'PutOffSweater': { newDrawable: 2500 }  }],
    }
]
