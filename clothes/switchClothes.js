export default [
    {
        gender: 0,
        isProp: 0,
        component: 11,
        drawable: 2387,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 1,
        drawable: 2141,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 1,
        drawable: 2140,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 1,
        drawable: 2148,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 1,
        drawable: 2138,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 1,
        drawable: 2137,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 1,
        drawable: 2136,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 1,
        drawable: 2133,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 1,
        drawable: 2132,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 1,
        drawable: 2083,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 1,
        drawable: 2079,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 11,
        drawable: 2384,
        texture: 0,
        update: [
            { isProp: 0, component: 8, drawable: 2129, texture: 0, },
            { isProp: 0, component: 4, drawable: 11, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 4,
        drawable: 2225,
        update: [
            { isProp: 0, component: 3, drawable: 8, texture: 0, },
            { isProp: 0, component: 11, drawable: 15, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 11,
        drawable: 2381,
        texture: 0,
        update: [
            { isProp: 0, component: 6, drawable: 13, texture: 0, },
            { isProp: 0, component: 4, drawable: 11, texture: 0, },
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 11,
        drawable: 2385,
        texture: 0,
        update: [
            { isProp: 0, component: 6, drawable: 13, texture: 0, },
            { isProp: 0, component: 4, drawable: 11, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 8,
        drawable: 2130,
        texture: 0,
        update: [
            { isProp: 0, component: 4, drawable: 11, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 4,
        drawable: 2197,
        texture: 0,
        update: [
            { isProp: 0, component: 11, drawable: 15, texture: 0, },
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
            { isProp: 0, component: 3, drawable: 1, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 4,
        drawable: 2194,
        update: [
            { isProp: 0, component: 11, drawable: 15, texture: 0, },
            { isProp: 0, component: 3, drawable: 1, texture: 0, },
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 4,
        drawable: 2192,
        update: [
            { isProp: 0, component: 11, drawable: 15, texture: 0, },
            { isProp: 0, component: 3, drawable: 1, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 4,
        drawable: 2271,
        update: [
            { isProp: 0, component: 6, drawable: 33, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 1,
        drawable: 2142,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 1,
        drawable: 2143,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 1,
        drawable: 2145,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 0,
        drawable: 2084,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 0,
        drawable: 2085,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 0,
        drawable: 2086,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 0,
        drawable: 2090,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 0,
        drawable: 2091,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 0,
        drawable: 2094,
        update: [
            { isProp: 0, component: 2, drawable: 1, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 0,
        drawable: 2101,
        update: [
            { isProp: 0, component: 2, drawable: 1, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 0,
        drawable: 2099,
        update: [
            { isProp: 0, component: 2, drawable: 1, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 11,
        drawable: 2513,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 11,
        drawable: 2516,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 0,
        drawable: 2098,
        update: [
            { isProp: 0, component: 2, drawable: 1, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 1,
        component: 0,
        drawable: 2095,
        update: [
            { isProp: 0, component: 2, drawable: 1, texture: 0, },
        ]
    },
    {
        gender: 0,
        isProp: 0,
        component: 0,
        drawable: 2162,
        update: [
            { isProp: 0, component: 2, drawable: 1, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 8,
        drawable: 2109,
        update: [
            { isProp: 0, component: 4, drawable: 13, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2202,
        update: [
            { isProp: 0, component: 11, drawable: 2407, texture: 0, },
            { isProp: 0, component: 8, drawable: 14, texture: 0, },
            { isProp: 0, component: 3, drawable: 4, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2204,
        update: [
            { isProp: 0, component: 8, drawable: 14, texture: 0, },
            { isProp: 0, component: 11, drawable: 2407, texture: 0, },
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
            { isProp: 0, component: 3, drawable: 3, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2187,
        update: [
            { isProp: 0, component: 8, drawable: 14, texture: 0, },
            { isProp: 0, component: 11, drawable: 2407, texture: 0, },
            { isProp: 0, component: 3, drawable: 2168, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2231,
        update: [
            { isProp: 0, component: 8, drawable: 14, texture: 0, },
            { isProp: 0, component: 11, drawable: 2407, texture: 0, },
            { isProp: 0, component: 3, drawable: 11, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2290,
        update: [
            { isProp: 0, component: 8, drawable: 14, texture: 0, },
            { isProp: 0, component: 11, drawable: 2407, texture: 0, },
            { isProp: 0, component: 3, drawable: 3, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 11,
        drawable: 2379,
        update: [
            { isProp: 0, component: 8, drawable: 14, texture: 0, },
            { isProp: 0, component: 4, drawable: 13, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 11,
        drawable: 2380,
        update: [
            { isProp: 0, component: 8, drawable: 14, texture: 0, },
            { isProp: 0, component: 4, drawable: 13, texture: 0, },
            { isProp: 0, component: 6, drawable: 34, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2189,
        update: [
            { isProp: 0, component: 11, drawable: 2407, texture: 0, },
            { isProp: 0, component: 3, drawable: 11, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2183,
        update: [
            { isProp: 0, component: 11, drawable: 2407, texture: 0, },
            { isProp: 0, component: 3, drawable: 4, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2220,
        update: [
            { isProp: 0, component: 11, drawable: 2407, texture: 0, },
            { isProp: 0, component: 3, drawable: 15, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2246,
        update: [
            { isProp: 0, component: 11, drawable: 2407, texture: 0, },
            { isProp: 0, component: 3, drawable: 9, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2274,
        update: [
            { isProp: 0, component: 6, drawable: 34, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2271,
        update: [
            { isProp: 0, component: 11, drawable: 2407, texture: 0, },
            { isProp: 0, component: 3, drawable: 3, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 4,
        drawable: 2282,
        update: [
            { isProp: 0, component: 6, drawable: 34, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 1,
        drawable: 2127,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 1,
        drawable: 2126,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 1,
        drawable: 2125,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 1,
        drawable: 2126,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 1,
        drawable: 2123,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 1,
        drawable: 2121,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 1,
        drawable: 2119,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 1,
        drawable: 2135,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 1,
        drawable: 2128,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 1,
        drawable: 2129,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 1,
        component: 0,
        drawable: 2087,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 0,
        component: 1,
        drawable: 2131,
        texture: 0,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 1,
        component: 0,
        drawable: 2094,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 1,
        component: 0,
        drawable: 2095,
        update: [
            { isProp: 0, component: 2, drawable: 0, texture: 0, },
        ]
    },
    {
        gender: 1,
        isProp: 1,
        component: 0,
        drawable: 2098,
        update: [
            { isProp: 0, component: 2, drawable: 1, texture: 0, },
        ]
    },
]
