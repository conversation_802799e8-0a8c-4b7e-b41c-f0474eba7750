module.exports = {
    types: {
        BASS_FX_DX8_CHORUS: 0,
        BASS_FX_DX8_COMPRESSOR: 1,
        BASS_FX_DX8_DISTORTION: 2,
        BASS_FX_DX8_ECHO: 3,
        BASS_FX_DX8_FLANGER: 4,
        BASS_FX_DX8_GARGLE: 5,
        BASS_FX_DX8_I3DL2REVERB: 6,
        BASS_FX_DX8_PARAMEQ: 7,
        BASS_FX_DX8_REVERB: 8,
        BASS_FX_VOLUME: 9,

        BASS_FX_BFX_VOLUME: 100,
        BASS_FX_BFX_PEAKEQ: 101,
        BASS_FX_BFX_DAMP: 102,
        BASS_FX_BFX_AUTOWAH: 103,
        BASS_FX_BFX_PHASER: 104,
        BASS_FX_BFX_PITCHSHIFT: 105,
        BASS_FX_BFX_FREEVERB: 106,

        BASS_FX_BFX_ROTATE: 65536,

        BASS_FX_BFX_BQF: 65555,

        // CUSTOM CATEGORY
        AUDIO_CATEGORY: 200000,
    },
    bqf_types: {
        BASS_BFX_BQF_LOWPASS: 0,
        BASS_BFX_BQF_HIGHPASS: 1,
        BASS_BFX_BQF_BANDPASS: 2,
        // constant 0 dB peak gain
        BASS_BFX_BQF_BANDPASS_Q: 3,
        // constant skirt gain, peak gain = Q
        BASS_BFX_BQF_NOTCH: 4,
        BASS_BFX_BQF_ALLPASS: 5,
        BASS_BFX_BQF_PEAKINGEQ: 6,
        BASS_BFX_BQF_LOWSHELF: 7,
        BASS_BFX_BQF_HIGHSHELF: 8,
    },
    lphase: {
        BASS_DX8_PHASE_NEG_180: 0,
        BASS_DX8_PHASE_NEG_90: 1,
        BASS_DX8_PHASE_ZERO: 2,
        BASS_DX8_PHASE_90: 3,
        BASS_DX8_PHASE_180: 4
    },
    lchannel: {
        BASS_BFX_CHANALL: -1,
    }
}
