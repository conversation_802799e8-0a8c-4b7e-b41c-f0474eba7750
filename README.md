# Majestic i18n

i18n translations

## Информация о переводах

- `clothes` — Переводы игровой одежды
- `game` — Переводы клиентской и серверной части игрового сервера RAGEMP
- `game_interfaces` — Переводы интерфейсов игрового сервера RAGEMP
- `launcher` — Переводы интерфейсов игрового лаунчера
- `web` — Переводы сайта
- `wiki` — Переводы wiki-сайта

## Система переводов

- **СОБЛЮДАЙТЕ СТРУКТУРУ ПЕРЕВОДОВ. ЕСЛИ ПЕРЕВОД СОЗДАЕТСЯ НА СЕРВЕРЕ, ТО ИСПОЛЬЗУЕТСЯ ПАПКА GAME. ЕСЛИ В ДРУГОМ МЕСТЕ, ТО ИСПОЛЬЗУЕТСЯ ПОДХОДЯЩАЯ ПАПКА. ПОЖАЛУЙСТА НЕ ПУТАЙТЕ ПАПКИ, И НЕ ОБРАЗАЙТЕ ИХ**

- У каждого проекта есть своя папка переводов. У сервера `game`, у интерфейсов `game_interfaces` и так далее.
- Если вы условно создаете уведомление на серверной части в папке `/server_src/modules/fun/casino.js` то и перевод должен быть похожей структуры. Например `fun.casino.playerDidThis` в папке `game`. Даже если это уведомление отправляется на сторону интерфейсов, она не должна быть в другой папке. Соблюдается структура
- Ни в коем случае не добавлять свои переводы в другие файлы кроме как ru.js. Система Crowdin сама делает пул из `ru.js` и дополняет файлы.
