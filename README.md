
# Majest<PERSON> Shared Config

Этот репозиторий существует для того, чтобы внешние разработчики, могли быстро добавлять правки в конфиги проекта.

## Как скачать этот репозиторий
- Скачиваем GIT по этой [ссылке](https://git-scm.com/downloads) и устанавливаем
- Далее нам надо настроить сам GIT. Для этого открываем CMD (А лучше `GIT BASH` (поищите в поиске Windows)), копируем и вставляем команды по примеру ниже. Однако не забудьте заполнить данные на свои от Gitlab!
```
git config --global user.name "<PERSON>"
git config --global user.email <EMAIL>
```
- Далее нам надо настроить SSH и привязать гитлаб. Пишем команду `ssh-keygen -t rsa -b 2048` и нажимаем `Enter` на все запросы пока не появится похожая картинка. 
- P.S Если появится запрос мол `/c/Users/<USER>/.ssh/id_rsa already exists. Overwrite (y/n)?`, то пишем `Y` и нажимаем `Enter`
```
+---[RSA 2048]----+
|        ... .+.. |
|       + . +o +  |
|        + = =o . |
|     . o + * ooo.|
|        X X +..o |
|       . o S =o .|
|        o +.Eoo .|
|       . +  ooo. |
|        o ..o+.=+|
+----[SHA256]-----+
```
- Далее после генерации пишем `cat ~/.ssh/id_rsa.pub` и у вас появится примерно похожий ответ: `ssh-rsa AAAAB3NzaC1yc2EAAXXXXx6Ppf6a0GJqullFbv0otF4DRJUUCgrN5fg8kXXXX Jengas@Jengas-PC` (Длина будет больше чем эта, ибо тут в примере удалено много символов)
Копируем всё от `ssh-rsa` до названия вашего ПК
- Далее переходим по этой [ссылке](https://gitlab.com/-/profile/keys) и вставляем содержимое и нажимаем кнопку `Add key`
- Если ошибок нет от GitLab, то теперь начинаем клонировать проект
- Заходите в любую удобную вам папку и скопируйте путь её. (Например `C:\Users\<USER>\Documents\GitProjects`).
- Открываем Git Bash и пишем там `cd "FOLDER_PATH"` (где `FOLDER_PATH` вставляем скопированный путь) и нажимаем Enter. Далее прописывайте `<NAME_EMAIL>:majesticrp/shared_config.git` и нажимайте Enter, чтобы установить репозиторий в эту папку (GitProjects у меня)
- После успешного клонирования, у вас будет проект.

## Как добавлять и заливать правки в конфигах
### Работа с VS CODE (Пропустите этот пункт если он у вас есть, и вы умеете им пользоваться + Имеете NodeJS + ESLint)
- Скачиваем программу по [ссылке](https://code.visualstudio.com/download) и устанавливаем
- В программе нажимаем `File > Add folder to Workspace` и выбираем нашу папку `shared_config`
- Далее сохраняем этот Workspace через `File > Save Workspace As...` (И в будущем будем открывать его для работы с этим репозиторием)
- Если у вас справа снизу появится предложение скачать рекомендуемые расширения - скачиваем. Если же нет, то нужно скачать их самому выбрав иконку пазла слева (Extensions)/`CTRL + SHIFT + X`:
- ESLINT, GitLense, Error Lens, EditorConfig for VS Code
- Далее скачиваем NodeJS LTS версию с [официального сайта](https://nodejs.org/en/) и устанавливаем
- Открываем Git Bash в нашем проекте и пишем `npm install` и нажимаем Enter
- После установки всех зависимостей, вам нужно будет перезапустить VS Code. Оно нужно для того чтобы правильно визуализировать ошибки в конфигах. Дубликаты, скобки не правильные и т.д
- Далее надо убедиться что вы выбрали ветку `develop`. Зайдите в Source Control категорию (см. скриншот)
![Source Control](https://i.imgur.com/eACRWH0.png)
- Далее синхронизируем правки (см. скриншо)
![синхронизируем правки](https://i.imgur.com/LvfBTAt.png)
- После чего редактируем все ваши нужные файлы
- Как только вы закончили редактирование, убедитесь что ваши правки не составляют никаких ошибок, иначе это сломает тестовый сервер
- После чего добавляем файлы в коммит, пишем изменения к этим файлам и пушим изменения (см. GIF)
![Коммитим файлы](https://i.imgur.com/ywFZWWg.gif)
- После успешной заливки, просим разработчиков перезапустить пайплайн тестового сервера для примения конфигов
