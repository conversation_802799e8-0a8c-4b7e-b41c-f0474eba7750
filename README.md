# MajesticBackend

### Полезные ссылки

- Используются фреймворк [NestJS](https://docs.nestjs.com), рекомендуется ознакомиться с документацией
- Для работы с базами данных используется [Prisma](https://www.prisma.io)
- Для развертки сервисов используется [Docker Swarm](https://docs.docker.com/engine/swarm) и [Traefik](https://traefik.io/traefik)

### Зависимости

- NodeJS 20 - например, через [nvm](https://github.com/coreybutler/nvm-windows/releases/latest)
- Docker для запуска инфраструктуры - [Docker Desktop](https://www.docker.com/products/docker-desktop)
- _(опционально)_ На Windows удобно работать через [WSL](https://learn.microsoft.com/en-us/windows/wsl/install)

### Подготовка проекта

1. Клонируем репозиторий на локальную машину
2. Инициализируем git-модули через `git submodule update --init --recursive`
3. Устанавливаем зависимости через `yarn`
4. Копируем файлы конфигов и настраиваем, если необходимо
   - `.env.example` → `.env`
   - `config.json.example` → `config.json`
5. Генерируем клиент БД через `yarn prisma:generate` (надо также выполнять при обновлении схем в `libs/database/prisma`)
6. Запускаем инфраструктуру через `yarn infra:start`
   - Наблюдать за статусом можно через Docker Desktop или через [расширение VS Code](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-docker)
   - Выключить инфраструктуру можно через `yarn infra:stop`
7. Заполняем базу данных из файлов `db-seed-game.sql` и `db-seed-backend.sql` в `libs/database/prisma/seeds`
   - Можно использовать графический клиент вроде [HeidiSQL](https://www.heidisql.com/download.php) или [DBeaver](https://dbeaver.io/download)
   - Надо сначала создать базы данных, указанные в `config.json`, а затем выполнить sql-файлы

### Разработка

Режим локальной разработки (проще, выше производительность):

1. Запускаем инфраструктуру через `yarn infra:start`
2. Запускаем автосборку и перезапуск:
   - `yarn serve:all` - все сервисы
   - `yarn serve api-gateway common reports` - только выбранные сервисы

Режим оркестратора (для разработки инфраструктуры):

1. Собираем базовый образ сервиса через `yarn stack:build-image`
2. Инициализируем кластер Docker Swarm `docker swarm init`
3. Запускаем стек Docker Swarm через `yarn stack:start`
