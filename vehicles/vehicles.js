module.exports = {
    // https://docs.google.com/spreadsheets/d/1fgXoGae1NfAgmgQozN99AaRMdTcT45QnksdOoDAkElo/edit?ts=5ca26bf8#gid=0
    // https://wiki.rage.mp/index.php?title=Vehicles

    // Boats - начало
    'dinghy': { price: 500000, tags: ["boat"], class: 'boat', capacity: 75000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.25, brandLF: 'Nagasaki', titleLF: 'Dinghy', title: 'Nagasaki Dinghy' },
    'dinghy2': { price: 750000, tags: ["boat"], class: 'boat', capacity: 82500, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.25, brandLF: 'Nagasaki', titleLF: 'Dinghy', title: 'Nagasaki Dinghy' },
    'dinghy3': { price: 33600, tags: ["boat"], class: 'null', capacity: 20000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.25, brandLF: 'Nagasaki', titleLF: 'Dinghy', title: 'Nagasaki Dinghy' },
    'dinghy4': { price: 34300, tags: ["boat"], class: 'null', capacity: 20000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.25, brandLF: 'Nagasaki', titleLF: 'Dinghy', title: 'Nagasaki Dinghy' },
    'jetmax': { price: 5000000, tags: ["boat"], class: 'boat', capacity: 200000, fuelType: 'Plus', fuelCap: 40, fuelConsumption: 0.25, brandLF: 'Shitzu', titleLF: 'Jetmax', title: 'Shitzu Jetmax' },
    'marquis': { price: 15000000, tags: ["boat"], class: 'boat', capacity: 750000, fuelType: 'Diesel', fuelCap: 100, fuelConsumption: 0.25, brandLF: 'Dinka', titleLF: 'Marquis', title: 'Dinka Marquis' },
    'seashark': { price: 250000, tags: ["boat"], class: 'boat', capacity: 15000, fuelType: 'Regular', fuelCap: 10, fuelConsumption: 0.1, brandLF: 'Speedophile', titleLF: 'Seashark', title: 'Speedophile Seashark' },
    'seashark2': { price: 250000, tags: ["boat"], class: 'boat', capacity: 15000, fuelType: 'Regular', fuelCap: 10, fuelConsumption: 0.1, brandLF: 'Speedophile', titleLF: 'Seashark', title: 'Speedophile Seashark' },
    'seashark3': { price: 250000, tags: ["boat"], class: 'boat', capacity: 15000, fuelType: 'Regular', fuelCap: 10, fuelConsumption: 0.21, brandLF: 'Speedophile', titleLF: 'Seashark', title: 'Speedophile Seashark' },
    'speeder': { price: 6500000, tags: ["boat"], class: 'boat', capacity: 250000, fuelType: 'Plus', fuelCap: 40, fuelConsumption: 0.25, brandLF: 'Pegassi', titleLF: 'Speeder', title: 'Pegassi Speeder' },
    'speeder2': { price: 6500000, tags: ["boat"], class: 'boat', capacity: 250000, fuelType: 'Plus', fuelCap: 40, fuelConsumption: 0.25, brandLF: 'Pegassi', titleLF: 'Speeder', title: 'Pegassi Speeder' },
    'squalo': { price: 2500000, tags: ["boat"], class: 'boat', capacity: 150000, fuelType: 'Plus', fuelCap: 15, fuelConsumption: 0.1, brandLF: 'Shitzu', titleLF: 'Squalo', title: 'Shitzu Squalo' },
    'submersible': { price: 10000000, tags: ["boat"], class: 'boat', capacity: 1000000, fuelType: 'Diesel', fuelCap: 1050, fuelConsumption: 0.25, brandLF: 'Kraken', titleLF: 'PS-1021', title: 'Submersible' },
    'submersible2': { price: 15000000, tags: ["boat"], class: 'boat', capacity: 1500000, fuelType: 'Diesel', fuelCap: 1050, fuelConsumption: 0.25, brandLF: 'Kraken', titleLF: 'Poseidon', title: 'Kraken' },
    'suntrap': { price: 750000, tags: ["boat"], class: 'boat', capacity: 90000, fuelType: 'Regular', fuelCap: 10, fuelConsumption: 0.1, brandLF: 'Shitzu', titleLF: 'Suntrap', title: 'Shitzu Suntrap' },
    'toro': { price: 5000000, tags: ["boat"], class: 'boat', capacity: 200000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 0.25, brandLF: 'Lampadati', titleLF: 'Toro', title: 'Lampadati Toro' },
    'toro2': { price: 5000000, tags: ["boat"], class: 'boat', capacity: 200000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 0.25, brandLF: 'Lampadati', titleLF: 'Toro', title: 'Lampadati Toro' },
    'tropic': { price: 2500000, tags: ["boat"], class: 'boat', capacity: 150000, fuelType: 'Plus', fuelCap: 40, fuelConsumption: 0.25, brandLF: 'Shitzu', titleLF: 'Tropic', title: 'Shitzu Tropic' },
    'tropic2': { price: 2500000, tags: ["boat"], class: 'boat', capacity: 150000, fuelType: 'Plus', fuelCap: 40, fuelConsumption: 0.25, brandLF: 'Shitzu', titleLF: 'Tropic', title: 'Shitzu Tropic' },
    'tug': { price: 2000000, tags: ["boat"], class: 'boat', capacity: 300000, fuelType: 'Diesel', fuelCap: 1000, fuelConsumption: 1, brandLF: 'Buckingham', titleLF: 'Tug', title: 'Tug' },
    'kosatka': { price: 10000, tags: ["boat"], class: 'null', capacity: 10000000, fuelType: 'Diesel', fuelCap: 90000, fuelConsumption: 100, brandLF: 'RUNE', titleLF: 'Kosatka', title: 'RUNE Kosatka' },
    'longfin': { price: 8000000, tags: ["boat"], class: 'boat', capacity: 300000, fuelType: 'Plus', fuelCap: 90, fuelConsumption: 0.8, brandLF: 'Shitzu', titleLF: 'Longfin', title: 'Shitzu Longfin' },
    'patrolboat': { price: 10000, tags: ["boat"], class: 'null', capacity: 750000, fuelType: 'Diesel', fuelCap: 1000, fuelConsumption: 3, brandLF: 'Buckingham', titleLF: 'Kurtz 31', title: 'Kurtz 31' },
    'avisa': { price: 30000000, tags: ["boat"], class: 'boat', capacity: 500000, fuelType: 'Diesel', fuelCap: 1050, fuelConsumption: 3, brandLF: 'Kraken', titleLF: 'Avisa', title: 'Kraken Avisa' },
    // Boats - конец

    // Commercials - начало
    'benson': { price: 400000, class: 'com', capacity: 210000, fuelType: 'Diesel', fuelCap: 400, fuelConsumption: 4, brandLF: 'Vapid', titleLF: 'Benson', title: 'Vapid Benson' },
    'biff': { price: 46000, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 340, fuelConsumption: 6, brandLF: 'HVY', titleLF: 'Biff', title: 'HVY Biff' },
    'cerberus': { price: 100001, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'MTL', titleLF: 'Cerberus Apocalypse', title: 'MTL Cerberus Apocalypse' },
    'cerberus2': { price: 100001, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'MTL', titleLF: 'Cerberus Fiction', title: 'MTL Cerberus Fiction' },
    'cerberus3': { price: 100001, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'MTL', titleLF: 'Cerberus Nightmare', title: 'MTL Cerberus Nightmare' },
    'hauler': { price: 120000, class: 'null', capacity: 25000, fuelType: 'Diesel', fuelCap: 500, fuelConsumption: 8, brandLF: 'JoBuilt', titleLF: 'Hauler', title: 'JoBuilt Hauler' },
    'hauler2': { price: 100001, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 550, fuelConsumption: 1, brandLF: 'JoBuilt', titleLF: 'Hauler Custom', title: 'JoBuilt Hauler Custom' },
    'mule': { price: 150000, class: 'com', capacity: 151000, fuelType: 'Diesel', fuelCap: 300, fuelConsumption: 4, brandLF: 'Maibatsu', titleLF: 'Mule', title: 'Maibatsu Mule' },
    'mule2': { price: 60000, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 300, fuelConsumption: 4, brandLF: 'Maibatsu', titleLF: 'Mule', title: 'Maibatsu Mule' },
    'mule3': { price: 62000, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 300, fuelConsumption: 4, brandLF: 'Maibatsu', titleLF: 'Mule', title: 'Maibatsu Mule' },
    'mule4': { price: 63000, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 300, fuelConsumption: 4, brandLF: 'Maibatsu', titleLF: 'Mule Custom', title: 'Maibatsu Mule Custom' },
    'packer': { price: 180000, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 900, fuelConsumption: 5.5, brandLF: 'MTL', titleLF: 'Packer', title: 'MTL Packer' },
    'phantom': { price: 150000, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 700, fuelConsumption: 7, brandLF: 'JoBuilt', titleLF: 'Phantom', title: 'JoBuilt Phantom' },
    'phantom2': { price: 100001, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'JoBuilt', titleLF: 'Phantom Wedge', title: 'JoBuilt Phantom Wedge' },
    'phantom3': { price: 220000, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 900, fuelConsumption: 3.5, brandLF: 'JoBuilt', titleLF: 'Phantom Custom', title: 'JoBuilt Phantom Custom' },
    'pounder': { price: 350000, class: 'com', capacity: 220000, fuelType: 'Diesel', fuelCap: 600, fuelConsumption: 4, brandLF: 'MTL', titleLF: 'Pounder', title: 'MTL Pounder' },
    'pounder2': { price: 75000, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 600, fuelConsumption: 4, brandLF: 'MTL', titleLF: 'Pounder Custom', title: 'MTL Pounder Custom' },
    'stockade': { price: 111, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 200, fuelConsumption: 3, brandLF: 'Brute', titleLF: 'Stockade', title: 'Brute Stockade' },
    'stockade3': { price: 100001, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Stockade', title: 'Brute Stockade' },
    'terbyte': { price: 100001, class: 'null', capacity: 5000000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'Terrorbyte', title: 'Benefactor Terrorbyte' },
    // Commercials - конец

    // Compacts - начало
    'asbo': { price: 30000, class: 'low', capacity: 40000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.5, brandLF: 'Maxwell', titleLF: 'Asbo', title: 'Maxwell Asbo' },
    'blista': { price: 36000, class: 'low', capacity: 40000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 0.75, brandLF: 'Dinka', titleLF: 'Blista', title: 'Dinka Blista' },
    'brioso': { price: 50000, class: 'mid', capacity: 35000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.37, brandLF: 'Grotti', titleLF: 'Brioso R/A', title: 'Grotti Brioso R/A' },
    'dilettante': { price: 26000, class: 'low', capacity: 40000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2, brandLF: 'Karin', titleLF: 'Dilettante', title: 'Karin Dilettante' },
    'dilettante2': { price: 100001, class: 'null', capacity: 45000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2, brandLF: 'Karin', titleLF: 'Dilettante', title: 'Karin Dilettante' },
    'issi2': { price: 75000, class: 'low+', capacity: 40000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.4, brandLF: 'Weeny', titleLF: 'Issi', title: 'Weeny Issi' },
    'issi3': { price: 11680, class: 'null', capacity: 20000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 0.83, brandLF: 'Weeny', titleLF: 'Issi Classic', title: 'Weeny Issi Classic' },
    'issi4': { price: 45000, class: 'unique', capacity: 34000, fuelType: 'Diesel', fuelCap: 40, fuelConsumption: 1.4, brandLF: 'Weeny', titleLF: 'Issi Apocalypse', title: 'Weeny Issi Apocalypse' },
    'issi5': { price: 49000, class: 'unique', capacity: 34000, fuelType: 'Diesel', fuelCap: 40, fuelConsumption: 1.4, brandLF: 'Weeny', titleLF: 'Issi Fiction', title: 'Weeny Issi Fiction' },
    'issi6': { price: 45000, class: 'unique', capacity: 34000, fuelType: 'Diesel', fuelCap: 40, fuelConsumption: 1.4, brandLF: 'Weeny', titleLF: 'Issi Nightmare', title: 'Weeny Issi Nightmare' },
    'kanjo': { price: 45000, class: 'low+', capacity: 40000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.5, brandLF: 'Dinka', titleLF: 'Blista Kanjo', title: 'Dinka Blista Kanjo' },
    'panto': { price: 40000, class: 'low+', capacity: 20000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 0.35, brandLF: 'Benefactor', titleLF: 'Panto', title: 'Benefactor Panto' },
    'prairie': { price: 50000, class: 'low+', capacity: 35000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 0.8, brandLF: 'Bollokan', titleLF: 'Prairie', title: 'Bollokan Prairie' },
    'rhapsody': { price: 44000, class: 'low', capacity: 45000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Rhapsody', title: 'Declasse Rhapsody' },
    'club': { price: 44000, class: 'low', capacity: 35000, fuelType: 'Regular', fuelCap: 55, fuelConsumption: 0.75, brandLF: 'BF', titleLF: 'Club', title: 'BF Club' },
    'brioso2': { price: 43000, class: 'low+', capacity: 25000, fuelType: 'Regular', fuelCap: 48, fuelConsumption: 0.75, brandLF: 'Grotti', titleLF: 'Brioso 300', title: 'Grotti Brioso 300' },
    'veto': { price: 37000, class: 'low', capacity: 0, fuelType: 'Regular', fuelCap: 35, fuelConsumption: 0.05, brandLF: 'Dinka', titleLF: 'Veto Classic', title: 'Dinka Veto Classic' },
    'veto2': { price: 43000, class: 'low', capacity: 0, fuelType: 'Regular', fuelCap: 35, fuelConsumption: 0.05, brandLF: 'Dinka', titleLF: 'Veto Modern', title: 'Dinka Veto Modern' },
    'weevil': { price: 47000, class: 'low+', capacity: 30000, fuelType: 'Regular', fuelCap: 55, fuelConsumption: 0.75, brandLF: 'BF', titleLF: 'Weevil', title: 'BF Weevil' },
    'brioso3': { price: 50000, class: 'unique', capacity: 25000, fuelType: 'Regular', fuelCap: 48, fuelConsumption: 0.75, brandLF: 'Grotti', titleLF: 'Brioso 300 Widebody', title: 'Grotti Brioso 300 Widebody' },
    // Compacts - конец

    // Coupes - начало
    'cogcabrio': { price: 110000, class: 'mid+', capacity: 45000, fuelType: 'Plus', fuelCap: 63, fuelConsumption: 1.2, brandLF: 'Enus', titleLF: 'Cognoscenti Cabrio', title: 'Enus Cognoscenti Cabrio' },
    'exemplar': { price: 100000, class: 'prem', capacity: 40000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1, brandLF: 'Dewbauchee', titleLF: 'Exemplar', title: 'Dewbauchee Exemplar' },
    'f620': { price: 125000, class: 'mid+', capacity: 50000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1.5, brandLF: 'Ocelot', titleLF: 'F620', title: 'Ocelot F620' },
    'felon': { price: 80000, class: 'mid+', capacity: 55000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1.2, brandLF: 'Lampadati', titleLF: 'Felon', title: 'Lampadati Felon' },
    'felon2': { price: 85000, class: 'mid+', capacity: 50000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1.2, brandLF: 'Lampadati', titleLF: 'Felon GT', title: 'Lampadati Felon GT' },
    'jackal': { price: 90000, class: 'mid', capacity: 65000, fuelType: 'Plus', fuelCap: 40, fuelConsumption: 1.1, brandLF: 'Ocelot', titleLF: 'Jackal', title: 'Ocelot Jackal' },
    'oracle': { price: 90000, class: 'low+', capacity: 65000, fuelType: 'Regular', fuelCap: 55, fuelConsumption: 1.8, brandLF: 'Ubermacht', titleLF: 'Oracle XS', title: 'Ubermacht Oracle XS' },
    'oracle2': { price: 130000, class: 'mid', capacity: 75000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Ubermacht', titleLF: 'Oracle', title: 'Ubermacht Oracle' },
    'sentinel': { price: 45000, class: 'mid+', capacity: 50000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1, brandLF: 'Ubermacht', titleLF: 'Sentinel XS', title: 'Ubermacht Sentinel XS' },
    'sentinel2': { price: 60000, class: 'mid+', capacity: 45000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1, brandLF: 'Ubermacht', titleLF: 'Sentinel', title: 'Ubermacht Sentinel' },
    'windsor': { price: 250000, class: 'prem', capacity: 65000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.6, brandLF: 'Enus', titleLF: 'Windsor', title: 'Enus Windsor' },
    'windsor2': { price: 350000, class: 'prem', capacity: 50000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Enus', titleLF: 'Windsor Drop', title: 'Enus Windsor Drop' },
    'zion': { price: 35000, class: 'mid+', capacity: 45000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1, brandLF: 'Ubermacht', titleLF: 'Zion', title: 'Ubermacht Zion' },
    'zion2': { price: 70000, class: 'mid+', capacity: 55000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1, brandLF: 'Ubermacht', titleLF: 'Zion Cabrio', title: 'Ubermacht Zion Cabrio' },
    'previon': { price: 80000, class: 'mid', capacity: 50000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1, brandLF: 'Karin', titleLF: 'Previon', title: 'Karin Previon' },
    'kanjosj': { price: 80000, class: 'unique', capacity: 45000, fuelType: 'Regular', fuelCap: 58, fuelConsumption: 1.2, brandLF: 'Dinka', titleLF: 'Kanjo SJ', title: 'Dinka Kanjo SJ' },
    'postlude': { price: 60000, class: 'unique', capacity: 60000, fuelType: 'Regular', fuelCap: 64, fuelConsumption: 1.3, brandLF: 'Dinka', titleLF: 'Postlude', title: 'Dinka Postlude' },
    // Coupes - конец

    // Cycles - начало
    'bmx': { price: 1000, tags: ["bicycle"], class: 'moto', capacity: 0, fuelType: 'mechanical', fuelCap: 2000, fuelConsumption: 0.01, brandLF: 'PED Cycles', titleLF: 'BMX', title: 'BMX' },
    'cruiser': { price: 1000, tags: ["bicycle"], class: 'moto', capacity: 0, fuelType: 'mechanical', fuelCap: 2000, fuelConsumption: 0.01, brandLF: 'PED Cycles', titleLF: 'Cruiser', title: 'Cruiser' },
    'fixter': { price: 2000, tags: ["bicycle"], class: 'moto', capacity: 0, fuelType: 'mechanical', fuelCap: 2000, fuelConsumption: 0.01, brandLF: 'R-Bike', titleLF: 'Fixter', title: 'Fixter' },
    'scorcher': { price: 3000, tags: ["bicycle"], class: 'moto', capacity: 0, fuelType: 'mechanical', fuelCap: 2000, fuelConsumption: 0.01, brandLF: 'PED Cycles', titleLF: 'Scorcher', title: 'Scorcher' },
    'tribike': { price: 4500, tags: ["bicycle"], class: 'moto', capacity: 0, fuelType: 'mechanical', fuelCap: 2000, fuelConsumption: 0.01, brandLF: 'R-Bike', titleLF: 'Whippet', title: 'Whippet Race Bike' },
    'tribike2': { price: 4500, tags: ["bicycle"], class: 'moto', capacity: 0, fuelType: 'mechanical', fuelCap: 2000, fuelConsumption: 0.01, brandLF: 'R-Bike', titleLF: 'Endurex', title: 'Endurex Race Bike' },
    'tribike3': { price: 4500, tags: ["bicycle"], class: 'moto', capacity: 0, fuelType: 'mechanical', fuelCap: 2000, fuelConsumption: 0.01, brandLF: 'R-Bike', titleLF: 'Tri-Cycles', title: 'Tri-Cycles Race Bike' },
    'inductor': { price: 40000, tags: ["bicycle"], class: 'unique', capacity: 0, fuelType: 'mechanical', fuelCap: 2000, fuelConsumption: 0.01, brandLF: 'Coil', titleLF: 'Inductor', title: 'Coil Inductor' },
    'inductor2': { price: 50000, tags: ["bicycle"], class: 'unique', capacity: 0, fuelType: 'mechanical', fuelCap: 2000, fuelConsumption: 0.01, brandLF: 'Coil', titleLF: 'Inductor Junk Energy', title: 'Junk Energy Inductor' },
    // Cycles - конец

    // Helicopters - начало. Исключить для игроков (на первое время).
    'akula': { price: 3750000, tags: ["heli"], class: 'null', capacity: 250000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Akula', title: 'Akula' },
    'annihilator': { price: 4250000, tags: ["heli"], class: 'null', capacity: 250000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Western Company', titleLF: 'Annihilator', title: 'Annihilator' },
    'annihilator2': { price: 4250000, tags: ["heli"], class: 'null', capacity: 250000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Western Company', titleLF: 'Annihilator Stels', title: 'Annihilator Stels' },
    'buzzard': { price: 2750000, tags: ["heli"], class: 'null', capacity: 150000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Nagasaki', titleLF: 'Buzzard Attack Chopper', title: 'Buzzard Attack Chopper' },
    'buzzard2': { price: 50000000, tags: ["heli"], class: 'heli', capacity: 150000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Nagasaki', titleLF: 'Buzzard', title: 'Buzzard' },
    'cargobob': { price: 4830000, tags: ["heli"], class: 'null', capacity: 3500000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Western Company', titleLF: 'Cargobob', title: 'Cargobob' },
    'cargobob2': { price: 4830000, tags: ["heli"], class: 'null', capacity: 3500000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Western Company', titleLF: 'Cargobob', title: 'Cargobob' },
    'cargobob3': { price: 4830000, tags: ["heli"], class: 'null', capacity: 3500000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Western Company', titleLF: 'Cargobob', title: 'Cargobob' },
    'cargobob4': { price: 4830000, tags: ["heli"], class: 'null', capacity: 3500000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Western Company', titleLF: 'Cargobob', title: 'Cargobob' },
    'frogger': { price: 40000000, tags: ["heli"], class: 'heli', capacity: 200000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Maibatsu', titleLF: 'Frogger', title: 'Frogger' },
    'frogger2': { price: 41000000, tags: ["heli"], class: 'null', capacity: 200000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Maibatsu', titleLF: 'Frogger', title: 'Frogger' },
    'havok': { price: 7000000, tags: ["heli"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Nagasaki', titleLF: 'Havok', title: 'Nagasaki Havok' },
    'hunter': { price: 5250000, tags: ["heli"], class: 'null', capacity: 1000000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'FH-1 Hunter', title: 'FH-1 Hunter' },
    'maverick': { price: 25000000, tags: ["heli"], class: 'heli', capacity: 175000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Maverick', title: 'Maverick' },
    'savage': { price: 4370250, tags: ["heli"], class: 'null', capacity: 1000000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Savage', title: 'Savage' },
    'skylift': { price: 1680600, tags: ["heli"], class: 'null', capacity: 1000000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'HVY', titleLF: 'Skylift', title: 'Skylift' },
    'supervolito': { price: 70000000, tags: ["heli"], class: 'heli', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'SuperVolito', title: 'Buckingham SuperVolito' },
    'supervolito2': { price: 80000000, tags: ["heli"], class: 'heli', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'SuperVolito Carbon', title: 'Buckingham SuperVolito Carbon' },
    'swift': { price: 100000000, tags: ["heli"], class: 'heli', capacity: 450000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Swift', title: 'Buckingham Swift' },
    'swift2': { price: 120000000, tags: ["heli"], class: 'heli', capacity: 450000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Swift Deluxe', title: 'Buckingham Swift Deluxe' },
    'valkyrie': { price: 7127350, tags: ["heli"], class: 'null', capacity: 1000000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Valkyrie', title: 'Valkyrie' },
    'valkyrie2': { price: 7129650, tags: ["heli"], class: 'null', capacity: 1000000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Valkyrie MOD.0', title: 'Valkyrie MOD.0' },
    'volatus': { price: 150000000, tags: ["heli"], class: 'heli', capacity: 400000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Volatus', title: 'Buckingham Volatus' },
    'seasparrow2': { price: 40000000, tags: ["heli"], class: 'heli', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'HVY', titleLF: 'Sparrow', title: 'Sparrow' },
    'seasparrow3': { price: 50000000, tags: ["heli"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'HVY', titleLF: 'Sparrow', title: 'Sparrow' },
    'conada': { price: 1000000, tags: ["heli"], class: 'unique', capacity: 250000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Conada', title: 'Buckingham Conada' },
    'conada2': { price: 1000000, tags: ["heli"], class: 'unique', capacity: 250000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Weaponized Conada', title: 'Buckingham Weaponized Conada' },
    // Helicopters - конец. Исключить для игроков (на первое время).

    // Industrial - начало
    'bulldozer': { price: 100001, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 500, fuelConsumption: 5, brandLF: 'HVY', titleLF: 'Dozer', title: 'HVY Dozer' },
    'cutter': { price: 100001, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Cutter', title: 'HVY Cutter' },
    'dump': { price: 100001, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Dump', title: 'HVY Dump' },
    'flatbed': { price: 36000, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 650, fuelConsumption: 5, brandLF: 'MTL', titleLF: 'Flatbed', title: 'MTL Flatbed' },
    'guardian': { price: 750000, class: 'null', capacity: 2000000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Guardian', title: 'Vapid Guardian' },
    'handler': { price: 100001, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Dock Handler', title: 'Dock Handler' },
    'mixer': { price: 48000, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 650, fuelConsumption: 5, brandLF: 'HVY', titleLF: 'Mixer', title: 'HVY Mixer' },
    'mixer2': { price: 49000, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 650, fuelConsumption: 5, brandLF: 'HVY', titleLF: 'Mixer', title: 'HVY Mixer' },
    'rubble': { price: 34000, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 650, fuelConsumption: 5, brandLF: 'JoBuilt', titleLF: 'Rubble', title: 'JoBuilt Rubble' },
    'tiptruck': { price: 34000, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 650, fuelConsumption: 5, brandLF: 'Brute', titleLF: 'Tipper', title: 'Brute Tipper' },
    'tiptruck2': { price: 100001, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Tipper', title: 'Tipper' },
    // Industrial - конец

    // Military - начало. Исключить для игроков.
    'apc': { price: 321000, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 300, fuelConsumption: 4, brandLF: 'HVY', titleLF: 'APC', title: 'HVY APC' },
    'barracks': { price: 94000, class: 'null', capacity: 3000000, fuelType: 'Regular', fuelCap: 150, fuelConsumption: 3, brandLF: 'HVY', titleLF: 'Barracks', title: 'Barracks' },
    'barracks2': { price: 107000, class: 'null', capacity: 3000000, fuelType: 'Regular', fuelCap: 100, fuelConsumption: 2, brandLF: 'HVY', titleLF: 'Barracks Semi', title: 'HVY Barracks Semi' },
    'barracks3': { price: 94000, class: 'null', capacity: 3000000, fuelType: 'Regular', fuelCap: 150, fuelConsumption: 3, brandLF: 'HVY', titleLF: 'Barracks', title: 'HVY Barracks' },
    'barracks5': { price: 94000, class: 'null', capacity: 3000000, fuelType: 'Regular', fuelCap: 150, fuelConsumption: 3, brandLF: 'HVY', titleLF: 'Barracks Flatbed', title: 'HVY Barracks Flatbed' },
    'vetir': { price: 94000, class: 'null', capacity: 850000, fuelType: 'Regular', fuelCap: 150, fuelConsumption: 3, brandLF: 'HVY', titleLF: 'Vetir', title: 'Vetir' },
    'barrage': { price: 1375000, class: 'null', capacity: 150000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Barrage', title: 'Barrage' },
    'chernobog': { price: 7327450, class: 'null', capacity: 10000000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Chernobog', title: 'Chernobog' },
    'crusader': { price: 32400, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1.6, brandLF: 'Canis', titleLF: 'Crusader', title: 'Canis Crusader' },
    'halftrack': { price: 3326150, class: 'null', capacity: 350000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Half-track', title: 'Bravado Half-track' },
    'khanjali': { price: 18023500, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 500, fuelConsumption: 9, brandLF: 'HVY', titleLF: 'TM-02 Khanjali', title: 'TM-02 Khanjali' },
    'rhino': { price: 6340000, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 500, fuelConsumption: 9, brandLF: 'HVY', titleLF: 'TM-01 Rhino', title: 'Rhino Tank' },
    'scarab': { price: 5390300, class: 'null', capacity: 250000, fuelType: 'Regular', fuelCap: 300, fuelConsumption: 5, brandLF: 'HVY', titleLF: 'Scarab Apocalypse', title: 'HVY Scarab Apocalypse' },
    'scarab2': { price: 5408000, class: 'null', capacity: 250000, fuelType: 'Regular', fuelCap: 300, fuelConsumption: 5, brandLF: 'HVY', titleLF: 'Scarab Fiction', title: 'HVY Scarab Fiction' },
    'scarab3': { price: 5410000, class: 'null', capacity: 250000, fuelType: 'Regular', fuelCap: 300, fuelConsumption: 5, brandLF: 'HVY', titleLF: 'Scarab Nightmare', title: 'HVY Scarab Nightmare' },
    'thruster': { price: 3380000, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Mammoth', titleLF: 'Thruster', title: 'Mammoth Thruster' },
    'trailersmall2': { price: 400000, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vom Feuer', titleLF: 'Anti-Aircraft Trailer', title: 'Vom Feuer Anti-Aircraft Trailer' },
    'minitank': { price: 400000, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 100, fuelConsumption: 3, brandLF: 'RC', titleLF: 'Invade and Persuade Tank', title: 'Tank Invade and Persuade' },
    'squaddie': { price: 400000, class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Mammoth', titleLF: 'Squaddie', title: 'Mammoth Squaddie' },
    'squaddie2': { price: 400000, class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Mammoth', titleLF: 'Armed Squaddie', title: 'Mammoth Armed Squaddie' },
    'squaddie3': { price: 400000, class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Mammoth', titleLF: 'Squaddie Classic', title: 'Mammoth Squaddie Classic' },
    'winky': { price: 400000, class: 'null', capacity: 35000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Winky', title: 'Vapid Winky' },
    // Military - конец. Исключить для игроков.

    // Motorcycles - начало
    'akuma': { price: 20000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.6, brandLF: 'Dinka', titleLF: 'Akuma', title: 'Dinka Akuma' },
    'avarus': { price: 15000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 32, fuelConsumption: 0.5, brandLF: 'LCC', titleLF: 'Avarus', title: 'LCC Avarus' },
    'bagger': { price: 12000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 35, fuelConsumption: 0.4, brandLF: 'Western', titleLF: 'Bagger', title: 'Western Bagger' },
    'bati': { price: 30000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Plus', fuelCap: 19, fuelConsumption: 0.5, brandLF: 'Pegassi', titleLF: 'Bati 801', title: 'Pegassi Bati 801' },
    'bati2': { price: 29000, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Plus', fuelCap: 19, fuelConsumption: 0.4, brandLF: 'Pegassi', titleLF: 'Bati 801RR', title: 'Pegassi Bati 801RR' },
    'bf400': { price: 20000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.5, brandLF: 'Nagasaki', titleLF: 'BF400', title: 'Nagasaki BF400' },
    'carbonrs': { price: 29100, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 33, fuelConsumption: 0.5, brandLF: 'Nagasaki', titleLF: 'Carbon RS', title: 'Nagasaki Carbon RS' },
    'chimera': { price: 70000, tags: ["moto"], class: 'unique', capacity: 10000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.4, brandLF: 'Nagasaki', titleLF: 'Chimera', title: 'Nagasaki Chimera' },
    'cliffhanger': { price: 18700, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 39, fuelConsumption: 0.5, brandLF: 'Western', titleLF: 'Cliffhanger', title: 'Western Cliffhanger' },
    'daemon': { price: 120000, tags: ["moto"], class: 'unique', capacity: 0, fuelType: 'Regular', fuelCap: 39, fuelConsumption: 0.6, brandLF: 'Western', titleLF: 'Daemon', title: 'Western Daemon' },
    'daemon2': { price: 18000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 39, fuelConsumption: 0.7, brandLF: 'Western', titleLF: 'Daemon', title: 'Western Daemon' },
    'defiler': { price: 14500, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 21, fuelConsumption: 0.5, brandLF: 'Shitzu', titleLF: 'Defiler', title: 'Shitzu Defiler' },
    'deathbike': { price: 91000, tags: ["moto"], class: 'unique', capacity: 10000, fuelType: 'Regular', fuelCap: 38, fuelConsumption: 1.2, brandLF: 'Western', titleLF: 'Deathbike Apocalypse', title: 'Western Deathbike Apocalypse' },
    'deathbike2': { price: 191000, tags: ["moto"], class: 'unique', capacity: 10000, fuelType: 'Premium', fuelCap: 38, fuelConsumption: 1.8, brandLF: 'Western', titleLF: 'Deathbike Fiction', title: 'Western Deathbike Fiction' },
    'deathbike3': { price: 91000, tags: ["moto"], class: 'unique', capacity: 10000, fuelType: 'Regular', fuelCap: 38, fuelConsumption: 1.2, brandLF: 'Western', titleLF: 'Deathbike Nightmare', title: 'Western Deathbike Nightmare' },
    'diablous': { price: 15300, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Plus', fuelCap: 24, fuelConsumption: 0.5, brandLF: 'Principe', titleLF: 'Diabolus', title: 'Principe Diabolus' },
    'diablous2': { price: 15400, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Plus', fuelCap: 31, fuelConsumption: 0.5, brandLF: 'Principe', titleLF: 'Diabolus Custom', title: 'Principe Diabolus Custom' },
    'double': { price: 28000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Plus', fuelCap: 19, fuelConsumption: 0.4, brandLF: 'Dinka', titleLF: 'Double-T', title: 'Dinka Double-T' },
    'enduro': { price: 5000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.5, brandLF: 'Dinka', titleLF: 'Enduro', title: 'Dinka Enduro' },
    'esskey': { price: 15000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.4, brandLF: 'Pegassi', titleLF: 'Esskey', title: 'Pegassi Esskey' },
    'faggio': { price: 1500, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 17, fuelConsumption: 0.5, brandLF: 'Pegassi', titleLF: 'Faggio Sport', title: 'Pegassi Faggio Sport' },
    'faggio2': { price: 1000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 17, fuelConsumption: 0.4, brandLF: 'Pegassi', titleLF: 'Faggio', title: 'Pegassi Faggio' },
    'faggio3': { price: 3400, tags: ["moto"], class: 'null', capacity: 5000, fuelType: 'Regular', fuelCap: 17, fuelConsumption: 0.5, brandLF: 'Pegassi', titleLF: 'Faggio Mod', title: 'Pegassi Faggio Mod' },
    'fcr': { price: 8400, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 32, fuelConsumption: 0.5, brandLF: 'Pegassi', titleLF: 'FCR 1000', title: 'Pegassi FCR 1000' },
    'fcr2': { price: 8700, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 32, fuelConsumption: 0.6, brandLF: 'Pegassi', titleLF: 'FCR 1000 Custom', title: 'Pegassi FCR 1000 Custom' },
    'gargoyle': { price: 17000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 39, fuelConsumption: 0.6, brandLF: 'Western', titleLF: 'Gargoyle', title: 'Western Gargoyle' },
    'hakuchou': { price: 35000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Premium', fuelCap: 46, fuelConsumption: 0.6, brandLF: 'Shitzu', titleLF: 'Hakuchou', title: 'Shitzu Hakuchou' },
    'hakuchou2': { price: 82000, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 0.4, brandLF: 'Shitzu', titleLF: 'Hakuchou Drag', title: 'Shitzu Hakuchou Drag' },
    'hexer': { price: 22500, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.5, brandLF: 'LCC', titleLF: 'Hexer', title: 'LCC Hexer' },
    'innovation': { price: 23000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.6, brandLF: 'LCC', titleLF: 'Innovation', title: 'LCC Innovation' },
    'lectro': { price: 20000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.6, brandLF: 'Principe', titleLF: 'Lectro', title: 'Principe Lectro' },
    'manchez': { price: 7000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.4, brandLF: 'Maibatsu', titleLF: 'Manchez', title: 'Maibatsu Manchez' },
    'manchez2': { price: 5000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.2, brandLF: 'Maibatsu', titleLF: 'Manchez Scout', title: 'Maibatsu Manchez Scout' },
    'nemesis': { price: 11400, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.6, brandLF: 'Principe', titleLF: 'Nemesis', title: 'Principe Nemesis' },
    'nightblade': { price: 65000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Premium', fuelCap: 40, fuelConsumption: 0.6, brandLF: 'Western', titleLF: 'Nightblade', title: 'Western Nightblade' },
    'oppressor': { price: 100001, tags: ["moto"], class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.6, brandLF: 'Pegassi', titleLF: 'Oppressor', title: 'Pegassi Oppressor' },
    'oppressor2': { price: 100001, tags: ["moto"], class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.6, brandLF: 'Pegassi', titleLF: 'Oppressor Mk II', title: 'Pegassi Oppressor Mk II' },
    'pcj': { price: 12000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.6, brandLF: 'Shitzu', titleLF: 'PCJ 600', title: 'Shitzu PCJ 600' },
    'ratbike': { price: 100001, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.6, brandLF: 'Western', titleLF: 'Rat Bike', title: 'Western Rat Bike' },
    'ruffian': { price: 18000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.5, brandLF: 'Pegassi', titleLF: 'Ruffian', title: 'Pegassi Ruffian' },
    'rrocket': { price: 100001, tags: ["moto"], class: 'null', capacity: 5000, fuelType: 'Regular', fuelCap: 10, fuelConsumption: 0.6, brandLF: 'Western', titleLF: 'Rampant Rocket', title: 'Western Rampant Rocket' },
    'sanchez2': { price: 10000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.6, brandLF: 'Maibatsu', titleLF: 'Sanchez', title: 'Maibatsu Sanchez' },
    'sanctus': { price: 210000, tags: ["moto"], class: 'unique', capacity: 10000, fuelType: 'Premium', fuelCap: 42, fuelConsumption: 2.1, brandLF: 'LCC', titleLF: 'Sanctus', title: 'LCC Sanctus' },
    'shotaro': { price: 111, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.4, brandLF: 'Nagasaki', titleLF: 'Shotaro', title: 'Nagasaki Shotaro' },
    'sovereign': { price: 14000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.6, brandLF: 'Western', titleLF: 'Sovereign', title: 'Western Sovereign' },
    'stryder': { price: 30500, tags: ["moto"], class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.6, brandLF: 'Nagasaki', titleLF: 'Stryder', title: 'Nagasaki Stryder' },
    'thrust': { price: 12200, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Plus', fuelCap: 19, fuelConsumption: 0.6, brandLF: 'Dinka', titleLF: 'Thrust', title: 'Dinka Thrust' },
    'vader': { price: 14200, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 19, fuelConsumption: 0.4, brandLF: 'Shitzu', titleLF: 'Vader', title: 'Shitzu Vader' },
    'vindicator': { price: 9150, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 19, fuelConsumption: 0.6, brandLF: 'Dinka', titleLF: 'Vindicator', title: 'Dinka Vindicator' },
    'vortex': { price: 28200, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 27, fuelConsumption: 0.5, brandLF: 'Pegassi', titleLF: 'Vortex', title: 'Pegassi Vortex' },
    'wolfsbane': { price: 23000, tags: ["moto"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.6, brandLF: 'Western', titleLF: 'Wolfsbane', title: 'Western Wolfsbane' },
    'zombiea': { price: 17500, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.4, brandLF: 'Western', titleLF: 'Zombie Bobber', title: 'Western Zombie Bobber' },
    'zombieb': { price: 19000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.6, brandLF: 'Western', titleLF: 'Zombie Chopper', title: 'Western Zombie Chopper' },
    'manchez3': { price: 45000, tags: ["moto"], class: 'unique', capacity: 0, fuelType: 'Regular', fuelCap: 25, fuelConsumption: 0.8, brandLF: 'Maibatsu', titleLF: 'Manchez Scout C', title: 'Maibatsu Manchez Scout C' },
    'powersurge': { price: 190000, tags: ["moto"], class: 'unique', capacity: 10000, fuelType: 'Electro', fuelCap: 30, fuelConsumption: 0.5, brandLF: 'Western', titleLF: 'Powersurge', title: 'Western Powersurge' },
    // Motorcycles - конец

    // Muscle - начало
    'blade': { price: 50000, class: 'low+', capacity: 55000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 2, brandLF: 'Vapid', titleLF: 'Blade', title: 'Vapid Blade' },
    'buccaneer': { price: 65000, class: 'low+', capacity: 60000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.8, brandLF: 'Albany', titleLF: 'Buccaneer', title: 'Albany Buccaneer' },
    'buccaneer2': { price: 70000, class: 'mid', capacity: 60000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Albany', titleLF: 'Buccaneer Custom', title: 'Albany Buccaneer Custom' },
    'chino': { price: 45000, class: 'low+', capacity: 60000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Chino', title: 'Vapid Chino' },
    'chino2': { price: 73000, class: 'mid', capacity: 60000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Chino Custom', title: 'Vapid Chino Custom' },
    'clique': { price: 60000, class: 'low+', capacity: 60000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1.7, brandLF: 'Vapid', titleLF: 'Clique', title: 'Vapid Clique' },
    'coquette3': { price: 350000, class: 'null', capacity: 50000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Invetero', titleLF: 'Coquette BlackFin', title: 'Invetero Coquette BlackFin' },
    'deviant': { price: 120000, class: 'mid', capacity: 55000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Schyster', titleLF: 'Deviant', title: 'Schyster Deviant' },
    'dominator': { price: 95000, class: 'low+', capacity: 50000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Dominator', title: 'Vapid Dominator' },
    'dominator2': { price: 65000, class: 'null', capacity: 45000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Pisswasser Dominator', title: 'Vapid Pisswasser Dominator' },
    'dominator3': { price: 150000, class: 'mid', capacity: 50000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 1.7, brandLF: 'Vapid', titleLF: 'Dominator GTX', title: 'Vapid Dominator GTX' },
    'dominator4': { price: 111, class: 'null', capacity: 45000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Dominator Apocalypse', title: 'Vapid Dominator Apocalypse' },
    'dominator5': { price: 111, class: 'null', capacity: 45000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Dominator Fiction', title: 'Vapid Dominator Fiction' },
    'dominator6': { price: 111, class: 'null', capacity: 45000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Dominator Nightmare', title: 'Vapid Dominator Nightmare' },
    'dukes': { price: 75000, class: 'low+', capacity: 50000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Imponte', titleLF: 'Dukes', title: 'Imponte Dukes' },
    'dukes2': { price: 111, class: 'null', capacity: 55000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1, brandLF: 'Imponte', titleLF: 'Duke O Death', title: 'Imponte Duke O Death' },
    'dukes3': { price: 85000, class: 'low', capacity: 60000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 2, brandLF: 'Imponte', titleLF: 'Beater Dukes', title: 'Imponte Beater Dukes' },
    'faction': { price: 55000, class: 'low+', capacity: 45000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Willard', titleLF: 'Faction', title: 'Willard Faction' },
    'faction2': { price: 90000, class: 'mid', capacity: 45000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Willard', titleLF: 'Faction Custom', title: 'Willard Faction Custom' },
    'faction3': { price: 500000, class: 'null', capacity: 50000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1, brandLF: 'Willard', titleLF: 'Faction Custom Donk', title: 'Willard Faction Custom Donk' },
    'ellie': { price: 111, class: 'null', capacity: 60000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Ellie', title: 'Vapid Ellie' },
    'gauntlet': { price: 70000, class: 'low+', capacity: 65000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Bravado', titleLF: 'Gauntlet', title: 'Bravado Gauntlet' },
    'gauntlet2': { price: 110000, class: 'unique', capacity: 60000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Bravado', titleLF: 'Redwood Gauntlet', title: 'Bravado Redwood Gauntlet' },
    'gauntlet3': { price: 65000, class: 'low+', capacity: 65000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.6, brandLF: 'Bravado', titleLF: 'Gauntlet Classic', title: 'Bravado Gauntlet Classic' },
    'gauntlet4': { price: 140000, class: 'mid', capacity: 65000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.7, brandLF: 'Bravado', titleLF: 'Gauntlet Hellfire', title: 'Bravado Gauntlet Hellfire' },
    'gauntlet5': { price: 80000, class: 'low+', capacity: 50000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.7, brandLF: 'Bravado', titleLF: 'Gauntlet Classic Custom', title: 'Bravado Gauntlet Classic Custom' },
    'hermes': { price: 46000, class: 'low', capacity: 75000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 2, brandLF: 'Albany', titleLF: 'Hermes', title: 'Albany Hermes' },
    'hotknife': { price: 96000, class: 'null', capacity: 30000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1.6, brandLF: 'Vapid', titleLF: 'Hotknife', title: 'Vapid Hotknife' },
    'hustler': { price: 66000, class: 'null', capacity: 30000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.6, brandLF: 'Vapid', titleLF: 'Hustler', title: 'Vapid Hustler' },
    'impaler': { price: 50000, class: 'low+', capacity: 55000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Impaler', title: 'Declasse Impaler' },
    'impaler2': { price: 82000, class: 'unique', capacity: 65000, fuelType: 'Regular', fuelCap: 83, fuelConsumption: 2.6, brandLF: 'Declasse', titleLF: 'Impaler Apocalypse', title: 'Declasse Impaler Apocalypse' },
    'impaler3': { price: 86000, class: 'unique', capacity: 65000, fuelType: 'Regular', fuelCap: 83, fuelConsumption: 2.6, brandLF: 'Declasse', titleLF: 'Impaler Fiction', title: 'Declasse Impaler Fiction' },
    'impaler4': { price: 82000, class: 'unique', capacity: 65000, fuelType: 'Regular', fuelCap: 83, fuelConsumption: 2.6, brandLF: 'Declasse', titleLF: 'Impaler Nightmare', title: 'Declasse Impaler Nightmare' },
    'imperator': { price: 79000, class: 'unique', capacity: 65000, fuelType: 'Regular', fuelCap: 75, fuelConsumption: 2.3, brandLF: 'Vapid', titleLF: 'Imperator Apocalypse', title: 'Vapid Imperator Apocalypse' },
    'imperator2': { price: 83000, class: 'unique', capacity: 65000, fuelType: 'Regular', fuelCap: 75, fuelConsumption: 2.3, brandLF: 'Vapid', titleLF: 'Imperator Fiction', title: 'Vapid Imperator Fiction' },
    'imperator3': { price: 79000, class: 'unique', capacity: 65000, fuelType: 'Regular', fuelCap: 75, fuelConsumption: 2.3, brandLF: 'Vapid', titleLF: 'Imperator Nightmare', title: 'Vapid Imperator Nightmare' },
    'lurcher': { price: 64000, class: 'unique', capacity: 70000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 2.2, brandLF: 'Albany', titleLF: 'Lurcher', title: 'Albany Lurcher' },
    'moonbeam': { price: 105000, class: 'com', capacity: 73000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 2.3, brandLF: 'Declasse', titleLF: 'Moonbeam', title: 'Declasse Moonbeam' },
    'moonbeam2': { price: 100000, class: 'mid', capacity: 40000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Moonbeam Custom', title: 'Declasse Moonbeam Custom' },
    'nightshade': { price: 120000, class: 'mid', capacity: 65000, fuelType: 'Premium', fuelCap: 60, fuelConsumption: 1.5, brandLF: 'Imponte', titleLF: 'Nightshade', title: 'Imponte Nightshade' },
    'peyote2': { price: 34500, class: 'null', capacity: 55000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Peyote Gasser', title: 'Vapid Peyote Gasser' },
    'phoenix': { price: 55000, class: 'low+', capacity: 50000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1.5, brandLF: 'Imponte', titleLF: 'Phoenix', title: 'Imponte Phoenix' },
    'picador': { price: 45000, class: 'low+', capacity: 70000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1.7, brandLF: 'Cheval', titleLF: 'Picador', title: 'Cheval Picador' },
    'ratloader': { price: 9300, class: 'null', capacity: 10000, fuelType: 'Plus', fuelCap: 28, fuelConsumption: 1.5, brandLF: 'Bravado', titleLF: 'Rat-Loader', title: 'Rat-Loader' },
    'ratloader2': { price: 37000, class: 'null', capacity: 50000, fuelType: 'Plus', fuelCap: 32, fuelConsumption: 1.5, brandLF: 'Bravado', titleLF: 'Rat-Truck', title: 'Bravado Rat-Truck' },
    'ruiner': { price: 100000, class: 'low+', capacity: 65000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1.7, brandLF: 'Imponte', titleLF: 'Ruiner', title: 'Imponte Ruiner' },
    'ruiner2': { price: 100001, class: 'null', capacity: 55000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Imponte', titleLF: 'Ruiner 2000', title: 'Imponte Ruiner 2000' },
    'ruiner3': { price: 100001, class: 'null', capacity: 55000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Imponte', titleLF: 'Ruiner', title: 'Imponte Ruiner' },
    'sabregt': { price: 65000, class: 'low+', capacity: 65000, fuelType: 'Plus', fuelCap: 40, fuelConsumption: 1.8, brandLF: 'Declasse', titleLF: 'Sabre Turbo', title: 'Declasse Sabre Turbo' },
    'sabregt2': { price: 95000, class: 'mid', capacity: 60000, fuelType: 'Plus', fuelCap: 40, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Sabre Turbo Custom', title: 'Declasse Sabre Turbo Custom' },
    'slamvan': { price: 35200, class: 'null', capacity: 80000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.6, brandLF: 'Vapid', titleLF: 'Slamvan', title: 'Vapid Slamvan' },
    'slamvan2': { price: 36100, class: 'null', capacity: 80000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Lost Slamvan', title: 'Vapid Lost Slamvan' },
    'slamvan3': { price: 60000, class: 'mid', capacity: 75000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Slamvan Custom', title: 'Vapid Slamvan Custom' },
    'slamvan4': { price: 94000, class: 'unique', capacity: 100000, fuelType: 'Regular', fuelCap: 80, fuelConsumption: 2.5, brandLF: 'Vapid', titleLF: 'Slamvan Apocalypse', title: 'Vapid Slamvan Apocalypse' },
    'slamvan5': { price: 98000, class: 'unique', capacity: 100000, fuelType: 'Regular', fuelCap: 80, fuelConsumption: 2.5, brandLF: 'Vapid', titleLF: 'Slamvan Fiction', title: 'Vapid Slamvan Fiction' },
    'slamvan6': { price: 94000, class: 'unique', capacity: 0, fuelType: 'Regular', fuelCap: 80, fuelConsumption: 2.5, brandLF: 'Vapid', titleLF: 'Slamvan Nightmare', title: 'Vapid Slamvan Nightmare' },
    'stalion': { price: 55000, class: 'low+', capacity: 60000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Stallion', title: 'Declasse Stallion' },
    'stalion2': { price: 39000, class: 'null', capacity: 30000, fuelType: 'Plus', fuelCap: 52, fuelConsumption: 1.8, brandLF: 'Declasse', titleLF: 'Burger Shot Stallion', title: 'Declasse Burger Shot Stallion' },
    'tampa': { price: 60000, class: 'low+', capacity: 70000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Tampa', title: 'Declasse Tampa' },
    'tampa3': { price: 150000, class: 'unique', capacity: 65000, fuelType: 'Plus', fuelCap: 28, fuelConsumption: 1.4, brandLF: 'Declasse', titleLF: 'Weaponized Tampa', title: 'Declasse Weaponized Tampa' },
    'tulip': { price: 29000, class: 'low', capacity: 60000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.8, brandLF: 'Declasse', titleLF: 'Tulip', title: 'Declasse Tulip' },
    'vamos': { price: 67000, class: 'low+', capacity: 75000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1.6, brandLF: 'Declasse', titleLF: 'Vamos', title: 'Declasse Vamos' },
    'vigero': { price: 100000, class: 'low+', capacity: 65000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Vigero', title: 'Declasse Vigero' },
    'virgo': { price: 39000, class: 'low+', capacity: 60000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.7, brandLF: 'Albany', titleLF: 'Virgo', title: 'Albany Virgo' },
    'virgo2': { price: 65000, class: 'mid', capacity: 55000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.5, brandLF: 'Dundreary ', titleLF: 'Virgo Classic Custom', title: 'Dundreary Virgo Classic Custom' },
    'virgo3': { price: 40000, class: 'low+', capacity: 60000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.5, brandLF: 'Dundreary ', titleLF: 'Virgo Classic', title: 'Dundreary Virgo Classic' },
    'voodoo': { price: 38000, class: 'low', capacity: 55000, fuelType: 'Regular', fuelCap: 55, fuelConsumption: 2, brandLF: 'Declasse', titleLF: 'Voodoo Custom', title: 'Declasse Voodoo Custom' },
    'voodoo2': { price: 16000, class: 'null', capacity: 60000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Voodoo', title: 'Declasse Voodoo' },
    'yosemite': { price: 52000, class: 'null', capacity: 80000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Yosemite', title: 'Declasse Yosemite' },
    'yosemite2': { price: 152000, class: 'null', capacity: 80000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 2.5, brandLF: 'Declasse', titleLF: 'Drift Yosemite', title: 'Declasse Drift Yosemite' },
    'dominator7': { price: 75000, class: 'low+', capacity: 45000, fuelType: 'Plus', fuelCap: 75, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Dominator ASP', title: 'Vapid Dominator ASP' },
    'dominator8': { price: 95000, class: 'low+', capacity: 60000, fuelType: 'Plus', fuelCap: 75, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Dominator GTT', title: 'Vapid Dominator GTT' },
    'greenwood': { price: 95000, class: 'unique', capacity: 70000, fuelType: 'Regular', fuelCap: 81, fuelConsumption: 1.5, brandLF: 'Bravado', titleLF: 'Greenwood', title: 'Bravado Greenwood' },
    'ruiner4': { price: 175000, class: 'unique', capacity: 70000, fuelType: 'Regular', fuelCap: 71, fuelConsumption: 1.7, brandLF: 'Imponte', titleLF: 'Ruiner ZZ-8', title: 'Imponte Ruiner ZZ-8' },
    'vigero2': { price: 200000, class: 'unique', capacity: 70000, fuelType: 'Plus', fuelCap: 78, fuelConsumption: 1.7, brandLF: 'Declasse', titleLF: 'Vigero ZX', title: 'Declasse Vigero ZX' },
    'weevil2': { price: 100000, class: 'unique', capacity: 60000, fuelType: 'Regular', fuelCap: 71, fuelConsumption: 1.7, brandLF: 'BF', titleLF: 'Weevil Custom', title: 'BF Weevil Custom' },
    'tahoma': { price: 35000, class: 'unique', capacity: 55000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1.6, brandLF: 'Declasse', titleLF: 'Tahoma Coupe', title: 'Declasse Tahoma Coupe' },
    'tulip2': { price: 45000, class: 'unique', capacity: 60000, fuelType: 'Regular', fuelCap: 68, fuelConsumption: 1.8, brandLF: 'Declasse', titleLF: 'Tulip M-100', title: 'Declasse Tulip M-100' },
    'broadway': { price: 160000, class: 'unique', capacity: 70000, fuelType: 'Regular', fuelCap: 62, fuelConsumption: 1.6, brandLF: 'Classique', titleLF: 'Broadway', title: 'Classique Broadway' },
    'eudora': { price: 120000, class: 'unique', capacity: 65000, fuelType: 'Regular', fuelCap: 58, fuelConsumption: 1.7, brandLF: 'Willard', titleLF: 'Eudora', title: 'Willard Eudora' },
    'brigham': { price: 150000, class: 'unique', capacity: 100000, fuelType: 'Regular', fuelCap: 80, fuelConsumption: 2.2, brandLF: 'Albany', titleLF: 'Brigham', title: 'Albany Brigham' },
    'buffalo5': { price: 320000, class: 'unique', capacity: 60000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.5, brandLF: 'Bravado', titleLF: 'Buffalo EVX', title: 'Bravado Buffalo EVX' },
    'clique2': { price: 60000, class: 'unique', capacity: 90000, fuelType: 'Regular', fuelCap: 52, fuelConsumption: 2.9, brandLF: 'Vapid', titleLF: 'Clique Wagon', title: 'Vapid Clique Wagon' },
    // Muscle - конец

    // Off-Road - начало
    'bfinjection': { price: 38000, class: 'low', capacity: 35000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1.2, brandLF: 'BF', titleLF: 'Injection', title: 'BF Injection' },
    'bifta': { price: 57000, class: 'null', capacity: 20000, fuelType: 'Regular', fuelCap: 27, fuelConsumption: 1.6, brandLF: 'BF', titleLF: 'Bifta', title: 'BF Bifta' },
    'blazer': { price: 5000, class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.5, brandLF: 'Nagasaki', titleLF: 'Blazer', title: 'Nagasaki Blazer' },
    'blazer2': { price: 6400, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1.5, brandLF: 'Nagasaki', titleLF: 'Blazer Lifeguard', title: 'Nagasaki Blazer Lifeguard' },
    'blazer3': { price: 111, class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Nagasaki', titleLF: 'Hot Rod Blazer', title: 'Nagasaki Hot Rod Blazer' },
    'blazer4': { price: 100001, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Nagasaki', titleLF: 'Street Blazer', title: 'Nagasaki Street Blazer' },
    'blazer5': { price: 100001, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Nagasaki', titleLF: 'Blazer Aqua', title: 'Nagasaki Blazer Aqua' },
    'verus': { price: 10000, class: 'moto', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 0.5, brandLF: 'Dinka', titleLF: 'Verus', title: 'Dinka Verus' },
    'bodhi2': { price: 48600, class: 'null', capacity: 80000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Canis', titleLF: 'Bodhi', title: 'Canis Bodhi' },
    'brawler': { price: 103450, class: 'null', capacity: 50000, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1.6, brandLF: 'Coil', titleLF: 'Brawler', title: 'Coil Brawler' },
    'bruiser': { price: 100001, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'Bruiser Apocalypse', title: 'Benefactor Bruiser Apocalypse' },
    'bruiser2': { price: 100001, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'Bruiser Fiction', title: 'Benefactor Bruiser Fiction' },
    'bruiser3': { price: 100001, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'Bruiser Nightmare', title: 'Benefactor Bruiser Nightmare' },
    'brutus': { price: 180000, class: 'unique', capacity: 110000, fuelType: 'Diesel', fuelCap: 112, fuelConsumption: 2.9, brandLF: 'Declasse', titleLF: 'Brutus Apocalypse', title: 'Declasse Brutus Apocalypse' },
    'brutus2': { price: 180000, class: 'unique', capacity: 110000, fuelType: 'Diesel', fuelCap: 112, fuelConsumption: 2.9, brandLF: 'Declasse', titleLF: 'Brutus Fiction', title: 'Declasse Brutus Fiction' },
    'brutus3': { price: 180000, class: 'unique', capacity: 110000, fuelType: 'Diesel', fuelCap: 112, fuelConsumption: 2.9, brandLF: 'Declasse', titleLF: 'Brutus Nightmare', title: 'Declasse Brutus Nightmare' },
    'caracara': { price: 115000, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 120, fuelConsumption: 3, brandLF: 'Vapid', titleLF: 'Caracara', title: 'Vapid Caracara' },
    'caracara2': { price: 145000, class: 'com', capacity: 95000, fuelType: 'Diesel', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Caracara 4x4', title: 'Vapid Caracara 4x4' },
    'dloader': { price: 13000, class: 'null', capacity: 120000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1.6, brandLF: 'Bravado', titleLF: 'Duneloader', title: 'Bravado Duneloader' },
    'dubsta3': { price: 130000, class: 'null', capacity: 150000, fuelType: 'Premium', fuelCap: 100, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'Dubsta 6x6', title: 'Benefactor Dubsta 6x6' },
    'dune': { price: 36270, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 35, fuelConsumption: 1.8, brandLF: 'BF', titleLF: 'Dune Buggy', title: 'BF Dune Buggy' },
    'dune2': { price: 50000, class: 'unique', capacity: 0, fuelType: 'Regular', fuelCap: 35, fuelConsumption: 1.2, brandLF: 'BF', titleLF: 'Dune UFO', title: 'BF Dune UFO' },
    'dune3': { price: 111, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'BF', titleLF: 'Dune FAV', title: 'BF Dune FAV' },
    'dune4': { price: 100001, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'BF', titleLF: 'Ramp Buggy', title: 'Ramp Buggy' },
    'dune5': { price: 100001, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'BF', titleLF: 'Ramp Buggy', title: 'Ramp Buggy' },
    'everon': { price: 175000, class: 'com', capacity: 95000, fuelType: 'Diesel', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Karin', titleLF: 'Everon', title: 'Karin Everon' },
    'freecrawler': { price: 130000, class: 'mid', capacity: 70000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1, brandLF: 'Canis', titleLF: 'Freecrawler', title: 'Canis Freecrawler' },
    'hellion': { price: 150000, class: 'com', capacity: 90000, fuelType: 'Diesel', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Annis', titleLF: 'Hellion', title: 'Annis Hellion' },
    'insurgent': { price: 100001, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Insurgent Pick-Up', title: 'HVY Insurgent Pick-Up' },
    'insurgent2': { price: 100001, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 100, fuelConsumption: 3, brandLF: 'HVY', titleLF: 'Insurgent', title: 'HVY Insurgent' },
    'insurgent3': { price: 100001, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Insurgent Pick-Up Custom', title: 'HVY Insurgent Pick-Up Custom' },
    'kalahari': { price: 28600, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.5, brandLF: 'Canis', titleLF: 'Kalahari', title: 'Canis Kalahari' },
    'kamacho': { price: 90000, class: 'low+', capacity: 100000, fuelType: 'Regular', fuelCap: 90, fuelConsumption: 2, brandLF: 'Canis', titleLF: 'Kamacho', title: 'Canis Kamacho' },
    'marshall': { price: 100001, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 150, fuelConsumption: 2.5, brandLF: 'Cheval', titleLF: 'Marshall', title: 'Cheval Marshall' },
    'mesa3': { price: 100001, class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1, brandLF: 'Canis', titleLF: 'Mesa', title: 'Canis Mesa' },
    'monster': { price: 100001, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Liberator', title: 'Vapid Liberator' },
    'monster3': { price: 100001, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Sasquatch Apocalypse', title: 'Bravado Sasquatch Apocalypse' },
    'monster4': { price: 100001, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Sasquatch Fiction', title: 'Bravado Sasquatch Fiction' },
    'monster5': { price: 100001, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Sasquatch Nightmare', title: 'Bravado Sasquatch Nightmare' },
    'menacer': { price: 100001, class: 'null', capacity: 400000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Menacer', title: 'HVY Menacer' },
    'nightshark': { price: 100001, class: 'null', capacity: 275000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Nightshark', title: 'HVY Nightshark' },
    'outlaw': { price: 100001, class: 'null', capacity: 20000, fuelType: 'Regular', fuelCap: 55, fuelConsumption: 1, brandLF: 'Nagasaki', titleLF: 'Outlaw', title: 'Nagasaki Outlaw' },
    'rancherxl': { price: 55000, class: 'low+', capacity: 85000, fuelType: 'Regular', fuelCap: 55, fuelConsumption: 0.8, brandLF: 'Declasse', titleLF: 'Rancher XL', title: 'Declasse Rancher XL' },
    'rancherxl2': { price: 100001, class: 'null', capacity: 80000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Rancher XL', title: 'Declasse Rancher XL' },
    'rebel': { price: 39000, class: 'low', capacity: 85000, fuelType: 'Regular', fuelCap: 55, fuelConsumption: 1.6, brandLF: 'Karin', titleLF: 'Rusty Rebel', title: 'Karin Rusty Rebel' },
    'rebel2': { price: 47500, class: 'low+', capacity: 85000, fuelType: 'Regular', fuelCap: 55, fuelConsumption: 1.2, brandLF: 'Karin', titleLF: 'Rebel', title: 'Karin Rebel' },
    'rcbandito': { price: 100001, class: 'null', capacity: 0, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 0.1, brandLF: 'RC', titleLF: 'Bandito', title: 'RC Bandito' },
    'riata': { price: 70000, class: 'mid', capacity: 80000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Riata', title: 'Vapid Riata' },
    'sandking': { price: 140000, class: 'com', capacity: 102000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Sandking XL', title: 'Vapid Sandking XL' },
    'sandking2': { price: 130000, class: 'com', capacity: 98000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Sandking SWB', title: 'Vapid Sandking SWB' },
    'technical': { price: 100001, class: 'null', capacity: 50000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Karin', titleLF: 'Technical', title: 'Karin Technical' },
    'technical2': { price: 100001, class: 'null', capacity: 50000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Karin', titleLF: 'Technical Aqua', title: 'Karin Technical Aqua' },
    'technical3': { price: 100001, class: 'null', capacity: 50000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Karin', titleLF: 'Technical Custom', title: 'Karin Technical Custom' },
    'trophytruck': { price: 100001, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Trophy Truck', title: 'Vapid Trophy Truck' },
    'trophytruck2': { price: 100001, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Desert Raid', title: 'Vapid Desert Raid' },
    'vagrant': { price: 100001, class: 'null', capacity: 20000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Maxwell', titleLF: 'Vagrant', title: 'Maxwell Vagrant' },
    'zhaba': { price: 100001, class: 'null', capacity: 400000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'RUNE', titleLF: 'Zhaba', title: 'RUNE Zhaba' },
    'yosemite3': { price: 85000, class: 'mid', capacity: 75000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Yosemite Rancher', title: 'Declasse Yosemite Rancher' },
    'draugur': { price: 350000, class: 'unique', capacity: 120000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.8, brandLF: 'Declasse', titleLF: 'Draugur', title: 'Declasse Draugur' },
    'boor': { price: 15000, class: 'unique', capacity: 90000, fuelType: 'Diesel', fuelCap: 60, fuelConsumption: 1.8, brandLF: 'Karin', titleLF: 'Boor', title: 'Karin Boor' },
    'l35': { price: 135000, class: 'unique', capacity: 110000, fuelType: 'Regular', fuelCap: 78, fuelConsumption: 2.5, brandLF: 'Declasse', titleLF: 'Walton L35', title: 'Declasse Walton L35' },
    'monstrociti': { price: 185000, class: 'unique', capacity: 105000, fuelType: 'Diesel', fuelCap: 82, fuelConsumption: 2.8, brandLF: 'Maibatsu', titleLF: 'MonstroCiti', title: 'Maibatsu MonstroCiti' },
    'ratel': { price: 100000, class: 'unique', capacity: 40000, fuelType: 'Regular', fuelCap: 52, fuelConsumption: 2.1, brandLF: 'Vapid', titleLF: 'Ratel', title: 'Vapid Ratel' },
    // Off-Road - конец

    // Open Wheel - начало
    'formula': { price: 100001, class: 'null', capacity: 0, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandLF: 'Progen', titleLF: 'PR4', title: 'Progen PR4' },
    'formula2': { price: 100001, class: 'null', capacity: 0, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandLF: 'Ocelot', titleLF: 'R88', title: 'Ocelot R88' },
    'openwheel1': { price: 100001, class: 'null', capacity: 0, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'BR8', title: 'Benefactor BR8' },
    'openwheel2': { price: 100001, class: 'null', capacity: 0, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'DR1', title: 'Declasse DR1' },
    // Open Wheel - конец

    // Planes - начало. Исключить для игроков (на первое время).
    'alphaz1': { price: 5700000, tags: ["plane"], class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Buckingham', titleLF: 'Alpha-Z1', title: 'Buckingham Alpha-Z1' },
    'avenger': { price: 10000000, tags: ["plane"], class: 'null', capacity: 1500000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Mammoth', titleLF: 'Avenger', title: 'Mammoth Avenger' },
    'avenger2': { price: 10000000, tags: ["plane"], class: 'null', capacity: 1500000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Mammoth', titleLF: 'Avenger', title: 'Mammoth Avenger' },
    'avenger3': { price: 10000000, tags: ["plane"], class: 'null', capacity: 1500000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Mammoth', titleLF: 'Avenger', title: 'Mammoth Avenger' },
    'besra': { price: 4490000, tags: ["plane"], class: 'null', capacity: 350000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Besra', title: 'Western Besra' },
    'blimp': { price: 1800000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Blimp Atomic', title: 'Atomic Blimp' },
    'blimp2': { price: 1800000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Blimp Xero', title: 'Xero Blimp' },
    'blimp3': { price: 1800000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Blimp', title: 'Дирижабль' },
    'bombushka': { price: 13100000, tags: ["plane"], class: 'null', capacity: 5000000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'RM-10 Bombushka', title: 'RM-10 Bombushka' },
    'cargoplane': { price: 23000000, tags: ["plane"], class: 'null', capacity: 10000000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Buckingham', titleLF: 'Cargo Plane', title: 'Cargo Plane' },
    'cuban800': { price: 1400000, tags: ["plane"], class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Cuban 800', title: 'Cuban 800' },
    'dodo': { price: 1440000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Mammoth', titleLF: 'Dodo', title: 'Mammoth Dodo' },
    'duster': { price: 1270000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Duster', title: 'Duster' },
    'howard': { price: 4300000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Buckingham', titleLF: 'Howard NX-25', title: 'Buckingham Howard NX-25' },
    'hydra': { price: 5000000, tags: ["plane"], class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Mammoth', titleLF: 'Hydra', title: 'Mammoth Hydra' },
    'jet': { price: 100001, tags: ["plane"], class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Jet', title: 'Jet' },
    'lazer': { price: 4400000, tags: ["plane"], class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Jobuilt', titleLF: 'P-996 LAZER', title: 'P-996 LAZER' },
    'luxor': { price: 3170000, tags: ["plane"], class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Buckingham', titleLF: 'Luxor', title: 'Buckingham Luxor' },
    'luxor2': { price: 3170000, tags: ["plane"], class: 'null', capacity: 1800000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Buckingham', titleLF: 'Luxor Deluxe', title: 'Buckingham Luxor Deluxe' },
    'mammatus': { price: 1160000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Jobuilt', titleLF: 'Mammatus', title: 'Mammatus' },
    'microlight': { price: 490000, tags: ["plane"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Nagasaki', titleLF: 'Ultralight', title: 'Nagasaki Ultralight' },
    'miljet': { price: 3100000, tags: ["plane"], class: 'null', capacity: 700000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Buckingham', titleLF: 'Miljet', title: 'Buckingham Miljet' },
    'mogul': { price: 6470000, tags: ["plane"], class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Mammoth', titleLF: 'Mogul', title: 'Mammoth Mogul' },
    'molotok': { price: 8000000, tags: ["plane"], class: 'null', capacity: 250000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'V-65 Molotok', title: 'V-65 Molotok' },
    'nimbus': { price: 1300000, tags: ["plane"], class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Buckingham', titleLF: 'Nimbus', title: 'Buckingham Nimbus' },
    'nokota': { price: 5000000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'P-45 Nokota', title: 'P-45 Nokota' },
    'pyro': { price: 9000000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Buckingham', titleLF: 'Pyro', title: 'Buckingham Pyro' },
    'rogue': { price: 1964000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Rogue', title: 'Western Rogue' },
    'seabreeze': { price: 3700000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Seabreeze', title: 'Western Seabreeze' },
    'shamal': { price: 6300000, tags: ["plane"], class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Buckingham', titleLF: 'Shamal', title: 'Buckingham Shamal' },
    'starling': { price: 4100000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'LF-22 Starling', title: 'LF-22 Starling' },
    'strikeforce': { price: 9370000, tags: ["plane"], class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'B-11 Strikeforce', title: 'B-11 Strikeforce' },
    'stunt': { price: 1030600, tags: ["plane"], class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Mallard', title: 'Mallard' },
    'titan': { price: 14000000, tags: ["plane"], class: 'null', capacity: 10000000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Titan', title: 'Titan' },
    'tula': { price: 6350000, tags: ["plane"], class: 'null', capacity: 750000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Mammoth', titleLF: 'Tula', title: 'Mammoth Tula' },
    'velum': { price: 930000, tags: ["plane"], class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Jobuilt', titleLF: 'Velum', title: 'Velum' },
    'velum2': { price: 930000, tags: ["plane"], class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Jobuilt', titleLF: 'Velum 5-Seater', title: 'Velum 5-Seater' },
    'vestra': { price: 1270000, tags: ["plane"], class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Buckingham', titleLF: 'Vestra', title: 'Buckingham Vestra' },
    'volatol': { price: 40000000, tags: ["plane"], class: 'null', capacity: 1000000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'Volatol', title: 'Volatol' },
    'alkonost': { price: 40000000, tags: ["plane"], class: 'null', capacity: 5000000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Western Company', titleLF: 'RO-86 Alkonost', title: 'RO-86 Alkonost' },
    'raiju': { price: 50000000, tags: ["plane"], class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 10, brandLF: 'Mammoth', titleLF: 'F-160 Raiju', title: 'Mammoth F-160 Raiju' },
    'sled': { price: 1, tags: ["plane"], class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 0.1, brandLF: 'Jobuilt', titleLF: 'Sled', title: 'Sled' },
    // Planes - конец. Исключить для игроков (на первое время).

    // SUVs - начало
    'baller': { price: 70500, class: 'low+', capacity: 75000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.8, brandLF: 'Gallivanter', titleLF: 'Baller', title: 'Gallivanter Baller' },
    'baller2': { price: 90000, class: 'mid+', capacity: 80000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.4, brandLF: 'Gallivanter', titleLF: 'Baller', title: 'Gallivanter Baller' },
    'baller3': { price: 110000, class: 'mid+', capacity: 90000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1.6, brandLF: 'Gallivanter', titleLF: 'Baller LE', title: 'Gallivanter Baller LE' },
    'baller4': { price: 150000, class: 'mid', capacity: 80000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1.6, brandLF: 'Gallivanter', titleLF: 'Baller LE LWB', title: 'Gallivanter Baller LE LWB' },
    'baller5': { price: 100001, class: 'null', capacity: 70000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1.6, brandLF: 'Gallivanter', titleLF: 'Baller LE (Armored)', title: 'Gallivanter Baller LE (Armored)' },
    'baller6': { price: 100001, class: 'null', capacity: 70000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1.6, brandLF: 'Gallivanter', titleLF: 'Baller LE LWB (Armored)', title: 'Gallivanter Baller LE LWB (Armored)' },
    'bjxl': { price: 75000, class: 'mid', capacity: 65000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Karin', titleLF: 'BeeJay XL', title: 'Karin BeeJay XL' },
    'cavalcade': { price: 86000, class: 'low+', capacity: 85000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 2, brandLF: 'Albany', titleLF: 'Cavalcade', title: 'Albany Cavalcade' },
    'cavalcade2': { price: 110000, class: 'mid', capacity: 80000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.7, brandLF: 'Albany', titleLF: 'Cavalcade', title: 'Albany Cavalcade' },
    'contender': { price: 180000, class: 'com', capacity: 95000, fuelType: 'Diesel', fuelCap: 65, fuelConsumption: 2, brandLF: 'Vapid', titleLF: 'Contender', title: 'Vapid Contender' },
    'dubsta': { price: 110000, class: 'mid+', capacity: 95000, fuelType: 'Diesel', fuelCap: 65, fuelConsumption: 1.6, brandLF: 'Benefactor', titleLF: 'Dubsta', title: 'Benefactor Dubsta' },
    'dubsta2': { price: 180000, class: 'mid+', capacity: 100000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.6, brandLF: 'Benefactor', titleLF: 'Dubsta', title: 'Benefactor Dubsta' },
    'fq2': { price: 90000, class: 'mid', capacity: 60000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Fathom', titleLF: 'FQ 2', title: 'Fathom FQ 2' },
    'granger': { price: 100000, class: 'mid', capacity: 110000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 1.6, brandLF: 'Declasse', titleLF: 'Granger', title: 'Declasse Granger' },
    'gresley': { price: 115000, class: 'mid', capacity: 80000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.6, brandLF: 'Bravado', titleLF: 'Gresley', title: 'Bravado Gresley' },
    'habanero': { price: 17200, class: 'null', capacity: 55000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1.6, brandLF: 'Emperor', titleLF: 'Habanero', title: 'Emperor Habanero' },
    'huntley': { price: 110000, class: 'mid+', capacity: 75000, fuelType: 'Diesel', fuelCap: 65, fuelConsumption: 1.6, brandLF: 'Enus', titleLF: 'Huntley S', title: 'Enus Huntley S' },
    'landstalker': { price: 120000, class: 'mid', capacity: 95000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1.6, brandLF: 'Dundreary ', titleLF: 'Landstalker', title: 'Dundreary Landstalker' },
    'mesa': { price: 97500, class: 'mid', capacity: 60000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1.6, brandLF: 'Canis', titleLF: 'Mesa', title: 'Canis Mesa' },
    'mesa2': { price: 100001, class: 'null', capacity: 55000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1.6, brandLF: 'Canis', titleLF: 'Mesa', title: 'Canis Mesa' },
    'novak': { price: 100000, class: 'prem', capacity: 60000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.6, brandLF: 'Lampadati', titleLF: 'Novak', title: 'Lampadati Novak' },
    'patriot': { price: 88000, class: 'low+', capacity: 100000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 1.8, brandLF: 'Mammoth', titleLF: 'Patriot', title: 'Mammoth Patriot' },
    'patriot2': { price: 109000, class: 'null', capacity: 90000, fuelType: 'Diesel', fuelCap: 65, fuelConsumption: 2.2, brandLF: 'Mammoth', titleLF: 'Patriot Stretch', title: 'Mammoth Patriot Stretch' },
    'radi': { price: 38000, class: 'null', capacity: 55000, fuelType: 'Diesel', fuelCap: 40, fuelConsumption: 1.3, brandLF: 'Vapid', titleLF: 'Radius', title: 'Vapid Radius' },
    'rebla': { price: 250000, class: 'mid+', capacity: 110000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 1.5, brandLF: 'Ubermacht', titleLF: 'Rebla GTS', title: 'Ubermacht Rebla GTS' },
    'rocoto': { price: 70000, class: 'mid', capacity: 60000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.8, brandLF: 'Obey', titleLF: 'Rocoto', title: 'Obey Rocoto' },
    'seminole': { price: 60000, class: 'low+', capacity: 70000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1.7, brandLF: 'Canis', titleLF: 'Seminole', title: 'Canis Seminole' },
    'seminole2': { price: 74000, class: 'low+', capacity: 80000, fuelType: 'Diesel', fuelCap: 60, fuelConsumption: 1.7, brandLF: 'Canis', titleLF: 'Seminole Frontier', title: 'Canis Seminole Frontier' },
    'serrano': { price: 70000, class: 'mid', capacity: 60000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1.9, brandLF: 'Benefactor', titleLF: 'Serrano', title: 'Benefactor Serrano' },
    'toros': { price: 700000, class: 'prem', capacity: 80000, fuelType: 'Premium', fuelCap: 95, fuelConsumption: 2, brandLF: 'Pegassi', titleLF: 'Toros', title: 'Pegassi Toros' },
    'xls': { price: 105000, class: 'mid+', capacity: 90000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.5, brandLF: 'Benefactor', titleLF: 'XLS', title: 'Benefactor XLS' },
    'xls2': { price: 100001, class: 'null', capacity: 75000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 2, brandLF: 'Benefactor', titleLF: 'XLS (Armored)', title: 'Benefactor XLS (Armored)' },
    'landstalker2': { price: 100000, class: 'mid', capacity: 100000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 2, brandLF: 'Dundreary ', titleLF: 'Landstalker XL', title: 'Dundreary Landstalker XL' },
    'issi8': { price: 420000, class: 'unique', capacity: 70000, fuelType: 'Premium', fuelCap: 60, fuelConsumption: 0.9, brandLF: 'Weeny', titleLF: 'Issi Rally', title: 'Weeny Issi Rally' },
    // SUVs - конец

    // Sedans - начало
    'asea': { price: 38000, class: 'low', capacity: 45000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.7, brandLF: 'Declasse', titleLF: 'Asea', title: 'Declasse Asea' },
    'asea2': { price: 100001, class: 'null', capacity: 50000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Asea', title: 'Declasse Asea' },
    'asterope': { price: 44000, class: 'low', capacity: 55000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.7, brandLF: 'Karin', titleLF: 'Asterope', title: 'Karin Asterope' },
    'cog55': { price: 140000, class: 'mid+', capacity: 80000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 0.7, brandLF: 'Enus', titleLF: 'Cognoscenti 55', title: 'Enus Cognoscenti 55' },
    'cog552': { price: 111, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Enus', titleLF: 'Cognoscenti 55 (Armored)', title: 'Enus Cognoscenti 55 (Armored)' },
    'cognoscenti': { price: 165000, class: 'mid+', capacity: 85000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 0.7, brandLF: 'Enus', titleLF: 'Cognoscenti', title: 'Enus Cognoscenti' },
    'cognoscenti2': { price: 111, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Enus', titleLF: 'Cognoscenti (Armored)', title: 'Enus Cognoscenti (Armored)' },
    'emperor': { price: 26000, class: 'low', capacity: 60000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1.5, brandLF: 'Albany', titleLF: 'Emperor', title: 'Albany Emperor' },
    'emperor2': { price: 25000, class: 'low', capacity: 60000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1.5, brandLF: 'Albany', titleLF: 'Emperor', title: 'Albany Emperor' },
    'emperor3': { price: 100001, class: 'null', capacity: 65000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Albany', titleLF: 'Emperor', title: 'Albany Emperor' },
    'fugitive': { price: 82000, class: 'mid', capacity: 60000, fuelType: 'Plus', fuelCap: 40, fuelConsumption: 0.6, brandLF: 'Cheval', titleLF: 'Fugitive', title: 'Cheval Fugitive' },
    'glendale': { price: 39000, class: 'low', capacity: 60000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'Glendale', title: 'Benefactor Glendale' },
    'glendale2': { price: 60000, class: 'low', capacity: 60000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'Glendale Custom', title: 'Benefactor Glendale Custom' },
    'ingot': { price: 40000, class: 'low', capacity: 70000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1, brandLF: 'Vulcar', titleLF: 'Ingot', title: 'Vulcar Ingot' },
    'intruder': { price: 45000, class: 'low', capacity: 60000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1, brandLF: 'Karin', titleLF: 'Intruder', title: 'Karin Intruder' },
    'limo2': { price: 100001, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'Turreted Limo', title: 'Benefactor Turreted Limo' },
    'premier': { price: 35000, class: 'low', capacity: 50000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Premier', title: 'Declasse Premier' },
    'primo': { price: 35000, class: 'low', capacity: 60000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.7, brandLF: 'Albany', titleLF: 'Primo', title: 'Albany Primo' },
    'primo2': { price: 53000, class: 'mid', capacity: 50000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 0.7, brandLF: 'Albany', titleLF: 'Primo Custom', title: 'Albany Primo Custom' },
    'regina': { price: 34000, class: 'low', capacity: 80000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 0.7, brandLF: 'Dundreary ', titleLF: 'Regina', title: 'Dundreary Regina' },
    'romero': { price: 12000, class: 'null', capacity: 50000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.7, brandLF: 'Albany', titleLF: 'Romero Hearse', title: 'Chariot Romero Hearse' },
    'schafter2': { price: 60000, class: 'mid', capacity: 55000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 0.7, brandLF: 'Benefactor', titleLF: 'Schafter', title: 'Benefactor Schafter' },
    'schafter5': { price: 100001, class: 'null', capacity: 60000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'Schafter V12 (Armored)', title: 'Benefactor Schafter V12 (Armored)' },
    'schafter6': { price: 100001, class: 'null', capacity: 60000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'Schafter LWB (Armored)', title: 'Benefactor Schafter LWB (Armored)' },
    'stafford': { price: 250000, class: 'prem', capacity: 75000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.9, brandLF: 'Enus', titleLF: 'Stafford', title: 'Enus Stafford' },
    'stanier': { price: 45000, class: 'low', capacity: 60000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.9, brandLF: 'Vapid', titleLF: 'Stanier', title: 'Vapid Stanier' },
    'stratum': { price: 25000, class: 'low', capacity: 65000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.8, brandLF: 'Zirconium', titleLF: 'Stratum', title: 'Zirconium Stratum' },
    'stretch': { price: 103000, class: 'null', capacity: 65000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 0.8, brandLF: 'Dundreary ', titleLF: 'Stretch', title: 'Dundreary Stretch' },
    'superd': { price: 350000, class: 'prem', capacity: 80000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.2, brandLF: 'Enus', titleLF: 'Super Diamond', title: 'Enus Super Diamond' },
    'surge': { price: 37000, class: 'low', capacity: 50000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 1.5, brandLF: 'Cheval', titleLF: 'Surge', title: 'Cheval Surge' },
    'tailgater': { price: 55000, class: 'mid', capacity: 45000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 0.7, brandLF: 'Obey', titleLF: 'Tailgater', title: 'Obey Tailgater' },
    'warrener': { price: 45000, class: 'low+', capacity: 40000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1.2, brandLF: 'Vulcar', titleLF: 'Warrener', title: 'Vulcar Warrener' },
    'washington': { price: 39000, class: 'low+', capacity: 50000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1.5, brandLF: 'Albany', titleLF: 'Washington', title: 'Albany Washington' },
    'tailgater2': { price: 180000, class: 'mid+', capacity: 65000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandLF: 'Obey', titleLF: 'Tailgater S', title: 'Obey Tailgater S' },
    'warrener2': { price: 55000, class: 'low+', capacity: 55000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1.2, brandLF: 'Vulcar', titleLF: 'Warrener HKR', title: 'Vulcar Warrener HKR' },
    'rhinehart': { price: 55000, class: 'unique', capacity: 90000, fuelType: 'Premium', fuelCap: 68, fuelConsumption: 1.4, brandLF: 'Ubermacht', titleLF: 'Rhinehart', title: 'Ubermacht Rhinehart' },
    // Sedans - конец

    // Service - начало. Исключить для игроков.
    'airbus': { price: 34000, class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Airport Bus', title: 'Airport Bus' },
    'brickade': { price: 2150000, class: 'null', capacity: 1000000, fuelType: 'Diesel', fuelCap: 200, fuelConsumption: 3, brandLF: 'MTL', titleLF: 'Brickade', title: 'MLT Brickade' },
    'bus': { price: 28600, class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Bus', title: 'Bus' },
    'coach': { price: 31200, class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'MTL', titleLF: 'Dashound', title: 'Dashound' },
    'pbus2': { price: 136000, class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Festival Bus', title: 'Party Bus' },
    'rallytruck': { price: 1090000, class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'MTL', titleLF: 'Dune', title: 'MTL Dune' },
    'rentalbus': { price: 26000, class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Rental Shuttle Bus', title: 'Rental Shuttle Bus' },
    'taxi': { price: 10000, class: 'null', capacity: 70000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Taxi', title: 'Taxi' },
    'tourbus': { price: 11000, class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Tourbus', title: 'Tourbus' },
    'trash': { price: 31000, class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Jobuilt', titleLF: 'Trashmaster', title: 'Trashmaster' },
    'trash2': { price: 29000, class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Jobuilt', titleLF: 'Trashmaster', title: 'Trashmaster' },
    'wastelander': { price: 1186000, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'MTL', titleLF: 'Wastelander', title: 'MTL Wastelander' },
    'brickade2': { price: 2500000, class: 'unique', capacity: 1000000, fuelType: 'Diesel', fuelCap: 200, fuelConsumption: 3, brandLF: 'MTL', titleLF: 'Brickade 6x6', title: 'MTL Brickade 6x6' },
    // Service - конец. Исключить для игроков.

    // Sports - начало
    'alpha': { price: 150000, class: 'mid+', capacity: 65000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1, brandLF: 'Albany', titleLF: 'Alpha', title: 'Albany Alpha' },
    'banshee': { price: 145000, class: 'mid+', capacity: 45000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Bravado', titleLF: 'Banshee', title: 'Bravado Banshee' },
    'bestiagts': { price: 200000, class: 'prem', capacity: 65000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Grotti', titleLF: 'Bestia GTS', title: 'Grotti Bestia GTS' },
    'blista2': { price: 55000, class: 'unique', capacity: 30000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.2, brandLF: 'Dinka', titleLF: 'Blista Compact', title: 'Dinka Blista Compact' },
    'blista3': { price: 27000, class: 'null', capacity: 25000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Dinka', titleLF: 'Go Go Monkey Blista', title: 'Dinka Go Go Monkey Blista' },
    'buffalo': { price: 53000, class: 'mid', capacity: 45000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Bravado', titleLF: 'Buffalo', title: 'Bravado Buffalo' },
    'buffalo2': { price: 130000, class: 'mid+', capacity: 55000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.5, brandLF: 'Bravado', titleLF: 'Buffalo S', title: 'Bravado Buffalo S' },
    'buffalo3': { price: 56700, class: 'null', capacity: 50000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.5, brandLF: 'Bravado', titleLF: 'Sprunk Buffalo', title: 'Bravado Sprunk Buffalo' },
    'carbonizzare': { price: 250000, class: 'prem', capacity: 45000, fuelType: 'Premium', fuelCap: 45, fuelConsumption: 1.2, brandLF: 'Grotti', titleLF: 'Carbonizzare', title: 'Grotti Carbonizzare' },
    'comet2': { price: 175000, class: 'mid+', capacity: 30000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Pfister', titleLF: 'Comet', title: 'Pfister Comet' },
    'comet3': { price: 117000, class: 'null', capacity: 25000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandLF: 'Pfister', titleLF: 'Comet Retro Custom', title: 'Pfister Comet Retro Custom' },
    'comet4': { price: 102500, class: 'null', capacity: 50000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandLF: 'Pfister', titleLF: 'Comet Safari', title: 'Pfister Comet Safari' },
    'comet5': { price: 400000, class: 'mid+', capacity: 30000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 0.7, brandLF: 'Pfister', titleLF: 'Comet SR', title: 'Pfister Comet SR' },
    'coquette': { price: 450000, class: 'prem', capacity: 40000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.3, brandLF: 'Invetero', titleLF: 'Coquette', title: 'Invetero Coquette' },
    'deveste': { price: 825000, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 100, fuelConsumption: 1.5, brandLF: 'Principe', titleLF: 'Deveste Eight', title: 'Principe Deveste Eight' },
    'elegy': { price: 130000, class: 'mid', capacity: 45000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.5, brandLF: 'Annis', titleLF: 'Elegy Retro Custom', title: 'Annis Elegy Retro Custom' },
    'elegy2': { price: 200000, class: 'mid+', capacity: 45000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.2, brandLF: 'Annis', titleLF: 'Elegy RH8', title: 'Annis Elegy RH8' },
    'feltzer2': { price: 240000, class: 'mid+', capacity: 40000, fuelType: 'Premium', fuelCap: 55, fuelConsumption: 1.2, brandLF: 'Benefactor', titleLF: 'Feltzer', title: 'Benefactor Feltzer' },
    'flashgt': { price: 120000, class: 'mid', capacity: 0, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandLF: 'Vapid', titleLF: 'Flash GT', title: 'Vapid Flash GT' },
    'furoregt': { price: 250000, class: 'mid+', capacity: 20000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Lampadati', titleLF: 'Furore GT', title: 'Lampadati Furore GT' },
    'fusilade': { price: 65000, class: 'mid', capacity: 55000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Schyster', titleLF: 'Fusilade', title: 'Schyster Fusilade' },
    'futo': { price: 57000, class: 'low', capacity: 30000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1.2, brandLF: 'Karin', titleLF: 'Futo', title: 'Karin Futo' },
    'gb200': { price: 120000, class: 'low+', capacity: 0, fuelType: 'Plus', fuelCap: 40, fuelConsumption: 1.2, brandLF: 'Vapid', titleLF: 'GB200', title: 'Vapid GB200' },
    'hotring': { price: 97000, class: 'null', capacity: 0, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Declasse', titleLF: 'Hotring Sabre', title: 'Declasse Hotring Sabre' },
    'imorgon': { price: 37000, class: 'null', capacity: 50000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.2, brandLF: 'Overflod', titleLF: 'Imorgon', title: 'Overflod Imorgon' },
    'issi7': { price: 37000, class: 'null', capacity: 0, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Weeny', titleLF: 'Issi Sport', title: 'Weeny Issi Sport' },
    'italigto': { price: 750000, class: 'prem', capacity: 30000, fuelType: 'Premium', fuelCap: 86, fuelConsumption: 1.2, brandLF: 'Grotti', titleLF: 'Itali GTO', title: 'Grotti Itali GTO' },
    'jugular': { price: 400000, class: 'prem', capacity: 70000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 0.6, brandLF: 'Ocelot', titleLF: 'Jugular', title: 'Ocelot Jugular' },
    'jester': { price: 350000, class: 'prem', capacity: 30000, fuelType: 'Plus', fuelCap: 68, fuelConsumption: 1.2, brandLF: 'Dinka', titleLF: 'Jester', title: 'Dinka Jester' },
    'jester2': { price: 375000, class: 'prem', capacity: 30000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Dinka', titleLF: 'Jester (Racecar)', title: 'Dinka Jester (Racecar)' },
    'jester3': { price: 190000, class: 'unique', capacity: 60000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Dinka', titleLF: 'Jester Classic', title: 'Dinka Jester Classic' },
    'khamelion': { price: 280000, class: 'mid+', capacity: 45000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.2, brandLF: 'Hijak', titleLF: 'Khamelion', title: 'Hijak Khamelion' },
    'komoda': { price: 350000, class: 'mid+', capacity: 50000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.5, brandLF: 'Lampadati', titleLF: 'Komoda', title: 'Lampadati Komoda' },
    'kuruma': { price: 160000, class: 'null', capacity: 60000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.5, brandLF: 'Karin', titleLF: 'Kuruma', title: 'Karin Kuruma' },
    'kuruma2': { price: 111, class: 'null', capacity: 40000, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 2.5, brandLF: 'Karin', titleLF: 'Kuruma (Armored)', title: 'Karin Kuruma (Armored)' },
    'locust': { price: 250000, class: 'null', capacity: 0, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.2, brandLF: 'Ocelot', titleLF: 'Locust', title: 'Ocelot Locust' },
    'lynx': { price: 240000, class: 'mid+', capacity: 30000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Ocelot', titleLF: 'Lynx', title: 'Ocelot Lynx' },
    'massacro': { price: 250000, class: 'prem', capacity: 40000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Dewbauchee', titleLF: 'Massacro', title: 'Dewbauchee Massacro' },
    'massacro2': { price: 250000, class: 'prem', capacity: 40000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Dewbauchee', titleLF: 'Massacro (Racecar)', title: 'Dewbauchee Massacro (Racecar)' },
    'neo': { price: 500000, class: 'prem', capacity: 30000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Vysser', titleLF: 'Neo', title: 'Vysser Neo' },
    'neon': { price: 400000, class: 'prem', capacity: 55000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 3, brandLF: 'Pfister', titleLF: 'Neon', title: 'Pfister Neon' },
    'ninef': { price: 240000, class: 'mid+', capacity: 20000, fuelType: 'Premium', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Obey', titleLF: '9F', title: 'Obey 9F' },
    'ninef2': { price: 240000, class: 'mid+', capacity: 20000, fuelType: 'Premium', fuelCap: 62, fuelConsumption: 1.2, brandLF: 'Obey', titleLF: '9F Cabrio', title: 'Obey 9F Cabrio' },
    'omnis': { price: 88000, class: 'unique', capacity: 0, fuelType: 'Plus', fuelCap: 62, fuelConsumption: 1.2, brandLF: 'Obey', titleLF: 'Omnis', title: 'Obey Omnis' },
    'paragon': { price: 550000, class: 'prem', capacity: 45000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.7, brandLF: 'Enus', titleLF: 'Paragon R', title: 'Enus Paragon R' },
    'paragon2': { price: 448000, class: 'null', capacity: 30000, fuelType: 'Premium', fuelCap: 62, fuelConsumption: 1.2, brandLF: 'Enus', titleLF: 'Paragon R Armorr', title: 'Enus Paragon R Armorr' },
    'pariah': { price: 500000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandLF: 'Ocelot', titleLF: 'Pariah', title: 'Ocelot Pariah' },
    'penumbra': { price: 41000, class: 'null', capacity: 40000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.2, brandLF: 'Maibatsu', titleLF: 'Penumbra', title: 'Maibatsu Penumbra' },
    'raiden': { price: 230000, class: 'mid+', capacity: 65000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.5, brandLF: 'Coil', titleLF: 'Raiden', title: 'Coil Raiden' },
    'rapidgt': { price: 150000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Dewbauchee', titleLF: 'Rapid GT', title: 'Dewbauchee Rapid GT' },
    'rapidgt2': { price: 150000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Dewbauchee', titleLF: 'Rapid GT', title: 'Dewbauchee Rapid GT' },
    'raptor': { price: 105000, class: 'null', capacity: 20000, fuelType: 'Premium', fuelCap: 45, fuelConsumption: 1.2, brandLF: 'BF', titleLF: 'Raptor', title: 'BF Raptor' },
    'revolter': { price: 300000, class: 'unique', capacity: 70000, fuelType: 'Premium', fuelCap: 60, fuelConsumption: 1.4, brandLF: 'Ubermacht', titleLF: 'Revolter', title: 'Ubermacht Revolter' },
    'ruston': { price: 156000, class: 'null', capacity: 20000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1.2, brandLF: 'Hijak', titleLF: 'Ruston', title: 'Hijak Ruston' },
    'schafter3': { price: 215000, class: 'mid+', capacity: 70000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.5, brandLF: 'Benefactor', titleLF: 'Schafter V12', title: 'Benefactor Schafter V12' },
    'schafter4': { price: 240000, class: 'mid+', capacity: 75000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Benefactor', titleLF: 'Schafter LWB', title: 'Benefactor Schafter LWB' },
    'schlagen': { price: 450000, class: 'prem', capacity: 60000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.2, brandLF: 'Benefactor', titleLF: 'Schlagen GT', title: 'Benefactor Schlagen GT' },
    'schwarzer': { price: 100000, class: 'mid', capacity: 50000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1.2, brandLF: 'Benefactor', titleLF: 'Schwartzer', title: 'Benefactor Schwartzer' },
    'sentinel3': { price: 60000, class: 'low+', capacity: 30000, fuelType: 'Plus', fuelCap: 45, fuelConsumption: 1.2, brandLF: 'Ubermacht', titleLF: 'Sentinel Classic', title: 'Ubermacht Sentinel Classic' },
    'seven70': { price: 400000, class: 'prem', capacity: 30000, fuelType: 'Premium', fuelCap: 57, fuelConsumption: 1.2, brandLF: 'Dewbauchee', titleLF: 'Seven-70', title: 'Dewbauchee Seven-70' },
    'specter': { price: 270000, class: 'mid+', capacity: 30000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Dewbauchee', titleLF: 'Specter', title: 'Dewbauchee Specter' },
    'specter2': { price: 310000, class: 'mid+', capacity: 30000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Dewbauchee', titleLF: 'Specter Custom', title: 'Dewbauchee Specter Custom' },
    'streiter': { price: 67000, class: 'null', capacity: 70000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandLF: 'Benefactor', titleLF: 'Streiter', title: 'Benefactor Streiter' },
    'sugoi': { price: 85000, class: 'mid', capacity: 45000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Dinka', titleLF: 'Sugoi', title: 'Dinka Sugoi' },
    'sultan': { price: 150000, class: 'mid', capacity: 55000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1.2, brandLF: 'Karin', titleLF: 'Sultan', title: 'Karin Sultan' },
    'sultan2': { price: 160000, class: 'unique', capacity: 60000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1.2, brandLF: 'Karin', titleLF: 'Sultan Classic', title: 'Karin Sultan Classic' },
    'surano': { price: 200000, class: 'mid+', capacity: 60000, fuelType: 'Premium', fuelCap: 45, fuelConsumption: 1.2, brandLF: 'Benefactor', titleLF: 'Surano', title: 'Benefactor Surano' },
    'tampa2': { price: 75000, class: 'low+', capacity: 20000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1.7, brandLF: 'Declasse', titleLF: 'Drift Tampa', title: 'Declasse Drift Tampa' },
    'tropos': { price: 170000, class: 'null', capacity: 0, fuelType: 'Premium', fuelCap: 35, fuelConsumption: 1.2, brandLF: 'Lampadati', titleLF: 'Tropos Rallye', title: 'Lampadati Tropos Rallye' },
    'verlierer2': { price: 138650, class: 'null', capacity: 20000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.2, brandLF: 'Bravado', titleLF: 'Verlierer', title: 'Bravado Verlierer' },
    'vstr': { price: 300000, class: 'mid+', capacity: 65000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.7, brandLF: 'Albany', titleLF: 'V-STR', title: 'Albany V-STR' },
    'zr380': { price: 100001, class: 'null', capacity: 0, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1.2, brandLF: 'Annis', titleLF: 'ZR380 Apocalypse', title: 'Annis ZR380 Apocalypse' },
    'zr3802': { price: 100001, class: 'null', capacity: 0, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1.2, brandLF: 'Annis', titleLF: 'ZR380 Fiction', title: 'Annis ZR380 Fiction' },
    'zr3803': { price: 100001, class: 'null', capacity: 0, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1.2, brandLF: 'Annis', titleLF: 'ZR380 Nightmare', title: 'Annis ZR380 Nightmare' },
    'drafter': { price: 300000, class: 'mid+', capacity: 65000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandLF: 'Obey', titleLF: '8F Drafter', title: 'Obey 8F Drafter' },
    'coquette4': { price: 400000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandLF: 'Invetero', titleLF: 'Coquette D10', title: 'Invetero Coquette D10' },
    'penumbra2': { price: 100000, class: 'low+', capacity: 50000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Maibatsu', titleLF: 'Penumbra FF', title: 'Maibatsu Penumbra FF' },
    'manana2': { price: 50000, class: 'low', capacity: 50000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 0.7, brandLF: 'Albany', titleLF: 'Manana Custom', title: 'Albany Manana Custom' },
    'calico': { price: 85000, class: 'low+', capacity: 35000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 0.7, brandLF: 'Karin', titleLF: 'Calico GTF', title: 'Karin Calico GTF' },
    'comet6': { price: 300000, class: 'prem', capacity: 45000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandLF: 'Pfister', titleLF: 'Comet S2', title: 'Pfister Comet S2' },
    'cypher': { price: 280000, class: 'mid+', capacity: 70000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1, brandLF: 'Ubermacht', titleLF: 'Cypher', title: 'Cypher' },
    'euros': { price: 210000, class: 'mid+', capacity: 55000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1, brandLF: 'Annis', titleLF: 'Euros', title: 'Annis Euros' },
    'futo2': { price: 68000, class: 'low', capacity: 20000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1.2, brandLF: 'Karin', titleLF: 'Futo GTX', title: 'Karin Futo GTX' },
    'growler': { price: 250000, class: 'prem', capacity: 50000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandLF: 'Pfister', titleLF: 'Growler', title: 'Pfister Growler' },
    'jester4': { price: 250000, class: 'mid+', capacity: 45000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1, brandLF: 'Dinka', titleLF: 'Jester RR', title: 'Dinka Jester RR' },
    'remus': { price: 80000, class: 'low+', capacity: 45000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1, brandLF: 'Annis', titleLF: 'Remus', title: 'Annis Remus' },
    'rt3000': { price: 90000, class: 'mid', capacity: 25000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Dinka', titleLF: 'RT3000', title: 'Dinka RT3000' },
    'sultan3': { price: 135000, class: 'mid', capacity: 50000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1.2, brandLF: 'Karin', titleLF: 'Sultan RS Classic', title: 'Karin Sultan RS Classic' },
    'vectre': { price: 275000, class: 'mid+', capacity: 65000, fuelType: 'Plus', fuelCap: 75, fuelConsumption: 1.2, brandLF: 'Emperor', titleLF: 'Vectre', title: 'Emperor Vectre' },
    'zr350': { price: 105000, class: 'low+', capacity: 40000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Annis', titleLF: 'ZR350', title: 'Annis ZR350' },
    'corsita': { price: 500000, class: 'unique', capacity: 30000, fuelType: 'Premium', fuelCap: 72, fuelConsumption: 1.5, brandLF: 'Lampadati', titleLF: 'Corsita', title: 'Lampadati Corsita' },
    'omnisegt': { price: 450000, class: 'unique', capacity: 80000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2, brandLF: 'Obey', titleLF: 'Omnis e-GT', title: 'Obey Omnis e-GT' },
    'sm722': { price: 550000, class: 'unique', capacity: 60000, fuelType: 'Premium', fuelCap: 77, fuelConsumption: 1.4, brandLF: 'Benefactor', titleLF: 'SM722', title: 'Benefactor SM722' },
    'tenf': { price: 475000, class: 'unique', capacity: 70000, fuelType: 'Premium', fuelCap: 83, fuelConsumption: 1.5, brandLF: 'Obey', titleLF: '10F', title: 'Obey 10F' },
    'tenf2': { price: 525000, class: 'unique', capacity: 70000, fuelType: 'Premium', fuelCap: 83, fuelConsumption: 1.5, brandLF: 'Obey', titleLF: '10F Widebody', title: 'Obey 10F Widebody' },
    'sentinel4': { price: 90000, class: 'unique', capacity: 30000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1.2, brandLF: 'Ubermacht', titleLF: 'Sentinel Classic Widebody', title: 'Ubermacht Sentinel Classic Widebody' },
    'r300': { price: 600000, class: 'unique', capacity: 45000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.4, brandLF: 'Annis', titleLF: '300R', title: 'Annis 300R' },
    'panthere': { price: 900000, class: 'unique', capacity: 60000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.5, brandLF: 'Toundra', titleLF: 'Panthere', title: 'Toundra Panthere' },
    'everon2': { price: 325000, class: 'unique', capacity: 70000, fuelType: 'Regular', fuelCap: 80, fuelConsumption: 1.6, brandLF: 'Karin', titleLF: 'Hotring Everon', title: 'Karin Hotring Everon' },
    'coureur': { price: 220000, class: 'unique', capacity: 40000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 1.5, brandLF: 'Penaud', titleLF: 'La Coureuse', title: 'Penaud La Coureuse' },
    'gauntlet6': { price: 410000, class: 'unique', capacity: 50000, fuelType: 'Plus', fuelCap: 75, fuelConsumption: 2.5, brandLF: 'Bravado', titleLF: 'Hotring Hellfire', title: 'Bravado Hotring Hellfire' },
    'stingertt': { price: 800000, class: 'unique', capacity: 40000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 2.1, brandLF: 'Grotti', titleLF: 'Itali GTO Stinger TT', title: 'Grotti Itali GTO Stinger TT' },
    // Sports - конец

    // Sports Classic - начало
    'ardent': { price: 100001, class: 'null', capacity: 10000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 0.7, brandLF: 'Ocelot', titleLF: 'Ardent', title: 'Ocelot Ardent' },
    'btype': { price: 101000, class: 'null', capacity: 15000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.5, brandLF: 'Albany', titleLF: 'Roosevelt', title: 'Albany Roosevelt' },
    'btype2': { price: 182000, class: 'unique', capacity: 80000, fuelType: 'Regular', fuelCap: 92, fuelConsumption: 2.8, brandLF: 'Albany', titleLF: 'Fränken Stange', title: 'Albany Fränken Stange' },
    'btype3': { price: 107000, class: 'null', capacity: 15000, fuelType: 'Regular', fuelCap: 42, fuelConsumption: 0.7, brandLF: 'Albany', titleLF: 'Roosevelt Valor', title: 'Albany Roosevelt Valor' },
    'casco': { price: 200000, class: 'null', capacity: 30000, fuelType: 'Regular', fuelCap: 35, fuelConsumption: 0.7, brandLF: 'Lampadati', titleLF: 'Casco', title: 'Lampadati Casco' },
    'cheetah2': { price: 200000, class: 'null', capacity: 40000, fuelType: 'Premium', fuelCap: 60, fuelConsumption: 1, brandLF: 'Grotti', titleLF: 'Cheetah Classic', title: 'Grotti Cheetah Classic' },
    'coquette2': { price: 126250, class: 'null', capacity: 20000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Invetero', titleLF: 'Coquette Classic', title: 'Invetero Coquette Classic' },
    'deluxo': { price: 100001, class: 'null', capacity: 10000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Imponte', titleLF: 'Deluxo', title: 'Imponte Deluxo' },
    'dynasty': { price: 39000, class: 'low', capacity: 45000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 0.8, brandLF: 'Weeny', titleLF: 'Dynasty', title: 'Weeny Dynasty' },
    'fagaloa': { price: 6000, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.8, brandLF: 'Vulcar', titleLF: 'Fagaloa', title: 'Vulcar Fagaloa' },
    'feltzer3': { price: 500000, class: 'null', capacity: 35000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1.2, brandLF: 'Benefactor', titleLF: 'Stirling GT', title: 'Benefactor Stirling GT' },
    'gt500': { price: 123700, class: 'null', capacity: 30000, fuelType: 'Regular', fuelCap: 35, fuelConsumption: 1, brandLF: 'Grotti', titleLF: 'GT500', title: 'Grotti GT500' },
    'infernus2': { price: 130000, class: 'null', capacity: 40000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 0.9, brandLF: 'Pegassi', titleLF: 'Infernus Classic', title: 'Pegassi Infernus Classic' },
    'jb700': { price: 140450, class: 'null', capacity: 20000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.9, brandLF: 'Dewbauchee', titleLF: 'JB 700', title: 'Dewbauchee JB 700' },
    'jb7002': { price: 140450, class: 'null', capacity: 20000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.9, brandLF: 'Dewbauchee', titleLF: 'JB 700W', title: 'Dewbauchee JB 700W' },
    'mamba': { price: 200000, class: 'null', capacity: 35000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1.3, brandLF: 'Declasse', titleLF: 'Mamba', title: 'Declasse Mamba' },
    'manana': { price: 50000, class: 'unique', capacity: 65000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.7, brandLF: 'Albany', titleLF: 'Manana', title: 'Albany Manana' },
    'michelli': { price: 35000, class: 'low', capacity: 30000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.7, brandLF: 'Lampadati', titleLF: 'Michelli GT', title: 'Lampadati Michelli GT' },
    'monroe': { price: 127250, class: 'null', capacity: 35000, fuelType: 'Plus', fuelCap: 40, fuelConsumption: 0.7, brandLF: 'Pegassi', titleLF: 'Monroe', title: 'Pegassi Monroe' },
    'nebula': { price: 40000, class: 'low', capacity: 45000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.7, brandLF: 'Vulcar', titleLF: 'Nebula Turbo', title: 'Vulcar Nebula Turbo' },
    'peyote': { price: 43000, class: 'low', capacity: 60000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Peyote', title: 'Vapid Peyote' },
    'peyote3': { price: 43000, class: 'low', capacity: 60000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Peyote Custom', title: 'Vapid Peyote Custom' },
    'pigalle': { price: 65000, class: 'low+', capacity: 50000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 0.8, brandLF: 'Lampadati', titleLF: 'Pigalle', title: 'Lampadati Pigalle' },
    'rapidgt3': { price: 85000, class: 'low+', capacity: 40000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 0.8, brandLF: 'Dewbauchee', titleLF: 'Rapid GT Classic', title: 'Dewbauchee Rapid GT Classic' },
    'retinue': { price: 40000, class: 'low', capacity: 30000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1.2, brandLF: 'Vapid', titleLF: 'Retinue', title: 'Vapid Retinue' },
    'retinue2': { price: 106400, class: 'null', capacity: 25000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Retinue Mk II', title: 'Vapid Retinue Mk II' },
    'savestra': { price: 85000, class: 'low+', capacity: 35000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1.1, brandLF: 'Annis', titleLF: 'Savestra', title: 'Annis Savestra' },
    'stinger': { price: 85450, class: 'null', capacity: 30000, fuelType: 'Plus', fuelCap: 45, fuelConsumption: 0.8, brandLF: 'Grotti', titleLF: 'Stinger', title: 'Grotti Stinger' },
    'stingergt': { price: 104750, class: 'null', capacity: 30000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1, brandLF: 'Grotti', titleLF: 'Stinger GT', title: 'Grotti Stinger GT' },
    'stromberg': { price: 100001, class: 'null', capacity: 10000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1, brandLF: 'Ocelot', titleLF: 'Stromberg', title: 'Ocelot Stromberg' },
    'swinger': { price: 121700, class: 'null', capacity: 25000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1.5, brandLF: 'Ocelot', titleLF: 'Swinger', title: 'Ocelot Swinger' },
    'torero': { price: 71600, class: 'null', capacity: 10000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 0.7, brandLF: 'Pegassi', titleLF: 'Torero', title: 'Pegassi Torero' },
    'tornado': { price: 34000, class: 'low', capacity: 45000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Tornado', title: 'Declasse Tornado' },
    'tornado2': { price: 38000, class: 'low', capacity: 45000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Tornado', title: 'Declasse Tornado' },
    'tornado3': { price: 32000, class: 'low', capacity: 45000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1.6, brandLF: 'Declasse', titleLF: 'Tornado', title: 'Declasse Tornado' },
    'tornado4': { price: 30000, class: 'low', capacity: 45000, fuelType: 'Regular', fuelCap: 40, fuelConsumption: 1.6, brandLF: 'Declasse', titleLF: 'Tornado', title: 'Declasse Tornado' },
    'tornado5': { price: 27100, class: 'null', capacity: 35000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 0.7, brandLF: 'Declasse', titleLF: 'Tornado Custom', title: 'Declasse Tornado Custom' },
    'tornado6': { price: 68000, class: 'null', capacity: 40000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1.7, brandLF: 'Declasse', titleLF: 'Tornado Rat Rod', title: 'Declasse Tornado Rat Rod' },
    'turismo2': { price: 203000, class: 'null', capacity: 40000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 0.7, brandLF: 'Grotti', titleLF: 'Turismo Classic', title: 'Grotti Turismo Classic' },
    'viseris': { price: 134000, class: 'null', capacity: 10000, fuelType: 'Plus', fuelCap: 62, fuelConsumption: 1, brandLF: 'Lampadati', titleLF: 'Viseris', title: 'Lampadati Viseris' },
    'z190': { price: 134000, class: 'null', capacity: 25000, fuelType: 'Plus', fuelCap: 55, fuelConsumption: 1, brandLF: 'Karin', titleLF: '190z', title: 'Karin 190z' },
    'ztype': { price: 155000, class: 'null', capacity: 5000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1.4, brandLF: 'Truffade', titleLF: 'Z-Type', title: 'Truffade Z-Type' },
    'zion3': { price: 65000, class: 'low+', capacity: 35000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Ubermacht', titleLF: 'Zion Classic', title: 'Ubermacht Zion Classic' },
    'cheburek': { price: 85000, class: 'mid', capacity: 35000, fuelType: 'Regular', fuelCap: 55, fuelConsumption: 0.5, brandLF: 'RUNE', titleLF: 'Cheburek', title: 'RUNE Cheburek' },
    'toreador': { price: 100001, class: 'null', capacity: 15000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Pegassi', titleLF: 'Toreador', title: 'Pegassi Toreador' },
    // Sports Classic - конец

    // Super - начало
    'adder': { price: 450000, class: 'prem', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Truffade', titleLF: 'Adder', title: 'Truffade Adder' },
    'autarch': { price: 600000, class: 'prem', capacity: 15000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Overflod', titleLF: 'Autarch', title: 'Overflod Autarch' },
    'banshee2': { price: 450000, class: 'null', capacity: 45000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Bravado', titleLF: 'Banshee 900R', title: 'Bravado Banshee 900R' },
    'bullet': { price: 250000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'Bullet', title: 'Vapid Bullet' },
    'cheetah': { price: 500000, class: 'prem', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Grotti', titleLF: 'Cheetah', title: 'Grotti Cheetah' },
    'cyclone': { price: 450000, class: 'prem', capacity: 35000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 3, brandLF: 'Coil', titleLF: 'Cyclone', title: 'Coil Cyclone' },
    'entity2': { price: 1000000, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Overflod', titleLF: 'Entity XXR', title: 'Overflod Entity XXR' },
    'entityxf': { price: 337000, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Overflod', titleLF: 'Entity XF', title: 'Overflod Entity XF' },
    'emerus': { price: 600000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Progen', titleLF: 'Emerus', title: 'Progen Emerus' },
    'fmj': { price: 1800000, class: 'null', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Vapid', titleLF: 'FMJ', title: 'Vapid FMJ' },
    'furia': { price: 650000, class: 'prem', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Grotti', titleLF: 'Furia', title: 'Grotti Furia' },
    'gp1': { price: 400000, class: 'prem', capacity: 20000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Progen', titleLF: 'GP1', title: 'Progen GP1' },
    'infernus': { price: 220000, class: 'prem', capacity: 20000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Pegassi', titleLF: 'Infernus', title: 'Pegassi Infernus' },
    'italigtb': { price: 231410, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Progen', titleLF: 'Itali GTB', title: 'Progen Itali GTB' },
    'italigtb2': { price: 241210, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Progen', titleLF: 'Itali GTB Custom', title: 'Progen Itali GTB Custom' },
    'krieger': { price: 1350000, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Benefactor', titleLF: 'Krieger', title: 'Benefactor Krieger' },
    'le7b': { price: 830000, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Annis', titleLF: 'RE-7B', title: 'Annis RE-7B' },
    'nero': { price: 700000, class: 'prem', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Truffade', titleLF: 'Nero', title: 'Truffade Nero' },
    'nero2': { price: 750000, class: 'null', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Truffade', titleLF: 'Nero Custom', title: 'Truffade Nero Custom' },
    'osiris': { price: 450000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Pegassi', titleLF: 'Osiris', title: 'Pegassi Osiris' },
    'penetrator': { price: 326700, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Ocelot', titleLF: 'Penetrator', title: 'Ocelot Penetrator' },
    'pfister811': { price: 500000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Pfister', titleLF: '811', title: 'Pfister 811' },
    'prototipo': { price: 2543000, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Grotti', titleLF: 'X80 Proto', title: 'Grotti X80 Proto' },
    'reaper': { price: 555000, class: 'null', capacity: 35000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Pegassi', titleLF: 'Reaper', title: 'Pegassi Reaper' },
    's80': { price: 237245, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Annis', titleLF: 'S80RR', title: 'Annis S80RR' },
    'sc1': { price: 240000, class: 'null', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Ubermacht', titleLF: 'SC1', title: 'Ubermacht SC1' },
    'scramjet': { price: 100001, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Scramjet', title: 'Declasse Scramjet' },
    'sheava': { price: 243000, class: 'null', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Emperor', titleLF: 'ETR1', title: 'Emperor ETR1' },
    'sultanrs': { price: 350000, class: 'mid', capacity: 60000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Karin', titleLF: 'Sultan RS', title: 'Karin Sultan RS' },
    't20': { price: 555000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Progen', titleLF: 'T20', title: 'Progen T20' },
    'taipan': { price: 1395000, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Cheval', titleLF: 'Taipan', title: 'Cheval Taipan' },
    'tempesta': { price: 500000, class: 'prem', capacity: 35000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Pegassi', titleLF: 'Tempesta', title: 'Pegassi Tempesta' },
    'tezeract': { price: 1000000, class: 'null', capacity: 10000, fuelType: 'Electro', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Pegassi', titleLF: 'Tezeract', title: 'Pegassi Tezeract' },
    'thrax': { price: 1000000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Truffade', titleLF: 'Thrax', title: 'Truffade Thrax' },
    'turismor': { price: 500000, class: 'prem', capacity: 20000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Grotti', titleLF: 'Turismo R', title: 'Grotti Turismo R' },
    'tyrant': { price: 650000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1, brandLF: 'Overflod', titleLF: 'Tyrant', title: 'Overflod Tyrant' },
    'tyrus': { price: 450000, class: 'prem', capacity: 20000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Progen', titleLF: 'Tyrus', title: 'Progen Tyrus' },
    'vacca': { price: 250000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Pegassi', titleLF: 'Vacca', title: 'Pegassi Vacca' },
    'vagner': { price: 247000, class: 'null', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Dewbauchee', titleLF: 'Vagner', title: 'Dewbauchee Vagner' },
    'vigilante': { price: 100001, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Grotti', titleLF: 'Vigilante', title: 'Vigilante' },
    'visione': { price: 650000, class: 'prem', capacity: 15000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Grotti', titleLF: 'Visione', title: 'Grotti Visione' },
    'voltic': { price: 170000, class: 'mid+', capacity: 15000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 3, brandLF: 'Coil', titleLF: 'Voltic', title: 'Coil Voltic' },
    'voltic2': { price: 100001, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Coil', titleLF: 'Rocket Voltic', title: 'Coil Rocket Voltic' },
    'xa21': { price: 500000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Ocelot', titleLF: 'XA-21', title: 'Ocelot XA-21' },
    'zentorno': { price: 1000000, class: 'null', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Pegassi', titleLF: 'Zentorno', title: 'Pegassi Zentorno' },
    'zorrusso': { price: 450000, class: 'prem', capacity: 25000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Pegassi', titleLF: 'Zorrusso', title: 'Pegassi Zorrusso' },
    'tigon': { price: 400000, class: 'prem', capacity: 15000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Lampadati', titleLF: 'Tigon', title: 'Lampadati Tigon' },
    'italirsx': { price: 1000000, class: 'prem', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Grotti', titleLF: 'Itali RSX', title: 'Grotti Itali RSX' },
    'lm87': { price: 1000000, class: 'unique', capacity: 0, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.6, brandLF: 'Benefactor', titleLF: 'LM87', title: 'Benefactor LM87' },
    'torero2': { price: 900000, class: 'unique', capacity: 0, fuelType: 'Premium', fuelCap: 92, fuelConsumption: 1.4, brandLF: 'Pegassi', titleLF: 'Torero XO', title: 'Pegassi Torero XO' },
    'entity3': { price: 1500000, class: 'unique', capacity: 50000, fuelType: 'Premium', fuelCap: 86, fuelConsumption: 1.6, brandLF: 'Overflod', titleLF: 'Entity MT', title: 'Overflod Entity MT' },
    'virtue': { price: 1200000, class: 'unique', capacity: 55000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 1.2, brandLF: 'Ocelot', titleLF: 'Virtue', title: 'Ocelot Virtue' },
    // Super - конец

    // Trailer - начало. Исключить для игроков.
    'armytanker': { price: 9550, class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Jobuilt', titleLF: 'Army Oil Tanker', title: 'Army Trailer' },
    'armytrailer': { price: 9550, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Jobuilt', titleLF: 'Army Trailer', title: 'Army Trailer' },
    'armytrailer2': { price: 9550, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Jobuilt', titleLF: 'Army Trailer Cutter', title: 'Army Trailer' },
    'baletrailer': { price: 5400, class: 'null', capacity: 250000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Bale Trailer', title: 'Baletrailer' },
    'boattrailer': { price: 3400, class: 'null', capacity: 200000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Boat Trailer', title: 'Boat Trailer' },
    'cablecar': { price: 1000, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Cable Car', title: 'Cable Car' },
    'docktrailer': { price: 9670, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Dock Trailer' },
    'freighttrailer': { price: 9670, class: 'null', capacity: 1000000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Freight Trailer' },
    'graintrailer': { price: 8400, class: 'null', capacity: 250000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Grain Trailer' },
    'proptrailer': { price: 8000, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Prop Trailer' },
    'raketrailer': { price: 4250, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Raket Trailer', title: 'Trailer' },
    'tr2': { price: 16000, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Vehicle Trailer', title: 'Trailer' },
    'tr3': { price: 51000, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Marcuise Trailer', title: 'Trailer' },
    'tr4': { price: 100001, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Vehicle Trailer MK2', title: 'Trailer' },
    'trflat': { price: 6700, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Ftal Trailer', title: 'Trailer' },
    'tvtrailer': { price: 9800, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Fame or Shame Trailer', title: 'Trailer' },
    'tanker': { price: 9850, class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'RON Tanker', title: 'Trailer' },
    'tanker2': { price: 8450, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Tanker' },
    'trailerlarge': { price: 8450, class: 'null', capacity: 1000000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Mobile Operations Center', title: 'Mobile Operations Center' },
    'trailerlogs': { price: 3300, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Wood Trailer', title: 'Trailer' },
    'trailersmall': { price: 1200, class: 'null', capacity: 200000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Small Trailer', title: 'Trailer' },
    'trailers': { price: 9300, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Dock Trailer MK2', title: 'Trailer' },
    'trailers2': { price: 92700, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Product Trailer', title: 'Trailer' },
    'trailers3': { price: 92700, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Big Goods Trailer', title: 'Trailer' },
    'trailers4': { price: 92700, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Dock Trailer MK3', title: 'Trailer' },
    // Trailer - конец. Исключить для игроков.

    // Trains - начало. Исключить для игроков.
    'freight': { price: 36000, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Freight Train', title: 'Freight Train' },
    'freightcar': { price: 8000, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Train Trailer', title: 'Freight Train' },
    'freightcont1': { price: 6000, class: 'null', capacity: 10000000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Train Trailer Conteiner', title: 'Freight Train' },
    'freightcont2': { price: 6000, class: 'null', capacity: 10000000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Train Trailer Conteiner MK2', title: 'Freight Train' },
    'freightgrain': { price: 2000, class: 'null', capacity: 10000000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Train Trailer Grain', title: 'Freight Train' },
    'tankercar': { price: 11000, class: 'null', capacity: 10000000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Train Tanker', title: 'Freight Train' },
    // Trains - конец. Исключить для игроков.

    // Utility - начало. Исключить для игроков.
    'airtug': { price: 5400, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Airtug', title: 'Airtug' },
    'caddy': { price: 10200, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1, brandLF: 'Nagasaki', titleLF: 'Caddy', title: 'Caddy' },
    'caddy2': { price: 10200, class: 'null', capacity: 10000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1, brandLF: 'Nagasaki', titleLF: 'Caddy', title: 'Caddy' },
    'caddy3': { price: 5100, class: 'null', capacity: 10000000, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 1, brandLF: 'Nagasaki', titleLF: 'Caddy', title: 'Caddy' },
    'docktug': { price: 61000, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Docktug', title: 'Docktug' },
    'forklift': { price: 21000, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Forklift', title: 'HVY Forklift' },
    'mower': { price: 6000, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Jack Sheepe', titleLF: 'Lawn Mower', title: 'Lawn Mower' },
    'ripley': { price: 27000, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Ripley', title: 'Ripley' },
    'sadler': { price: 110000, class: 'com', capacity: 85000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Sadler', title: 'Vapid Sadler' },
    'sadler2': { price: 22000, class: 'null', capacity: 100000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Sadler', title: 'Vapid Sadler' },
    'scrap': { price: 8400, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Scrap Truck', title: 'Scrap Truck' },
    'towtruck': { price: 13200, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Yankee Towtruck', title: 'Towtruck' },
    'towtruck2': { price: 16400, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'SlamVan Towtruck', title: 'Towtruck' },
    'tractor': { price: 2600, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Stanley', titleLF: 'Tractor', title: 'Tractor' },
    'tractor2': { price: 21000, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Stanley', titleLF: 'Fieldmaster', title: 'Stanley Fieldmaster' },
    'tractor3': { price: 20500, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Stanley', titleLF: 'Fieldmaster', title: 'Stanley Fieldmaster' },
    'utillitruck': { price: 191000, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Stockade Utility Truck', title: 'Utility Truck' },
    'utillitruck2': { price: 17250, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Brute', titleLF: 'Stockade Utility Truck MK2', title: 'Utility Truck' },
    'utillitruck3': { price: 11000, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Contender Utility Truck', title: 'Utility Truck' },
    'slamtruck': { price: 16400, class: 'null', capacity: 10000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Slamtruck', title: 'Vapid Slamtruck' },
    // Utility - конец. Исключить для игроков.

    // Vans - начало
    'bison': { price: 120000, class: 'com', capacity: 87000, fuelType: 'Diesel', fuelCap: 100, fuelConsumption: 2.5, brandLF: 'Bravado', titleLF: 'Bison', title: 'Bravado Bison' },
    'bison2': { price: 40400, class: 'null', capacity: 90000, fuelType: 'Diesel', fuelCap: 100, fuelConsumption: 2.5, brandLF: 'Bravado', titleLF: 'Bison 2', title: 'Bravado Bison 2' },
    'bison3': { price: 40000, class: 'null', capacity: 90000, fuelType: 'Diesel', fuelCap: 100, fuelConsumption: 2.5, brandLF: 'Bravado', titleLF: 'Bison 3', title: 'Bravado Bison 3' },
    'bobcatxl': { price: 100000, class: 'com', capacity: 78000, fuelType: 'Diesel', fuelCap: 100, fuelConsumption: 3.5, brandLF: 'Vapid', titleLF: 'Bobcat XL', title: 'Vapid Bobcat XL' },
    'boxville': { price: 31000, class: 'null', capacity: 200000, fuelType: 'Diesel', fuelCap: 60, fuelConsumption: 0.5, brandLF: 'Brute', titleLF: 'Boxville', title: 'Boxville' },
    'boxville2': { price: 31000, class: 'null', capacity: 200000, fuelType: 'Diesel', fuelCap: 150, fuelConsumption: 2.5, brandLF: 'Brute', titleLF: 'Boxville', title: 'Boxville' },
    'boxville3': { price: 31000, class: 'null', capacity: 200000, fuelType: 'Diesel', fuelCap: 100, fuelConsumption: 0.7, brandLF: 'Brute', titleLF: 'Boxville', title: 'Brute Boxville' },
    'boxville4': { price: 31000, class: 'null', capacity: 200000, fuelType: 'Diesel', fuelCap: 150, fuelConsumption: 2, brandLF: 'Brute', titleLF: 'Boxville', title: 'Brute Boxville' },
    'boxville5': { price: 11000, class: 'null', capacity: 200000, fuelType: 'Diesel', fuelCap: 100, fuelConsumption: 0.7, brandLF: 'Brute', titleLF: 'Armored Boxville', title: 'Armored Boxville' },
    'burrito': { price: 12000, class: 'null', capacity: 100000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 0.7, brandLF: 'Declasse', titleLF: 'Burrito', title: 'Declasse Burrito' },
    'burrito2': { price: 11000, class: 'null', capacity: 100000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 0.7, brandLF: 'Declasse', titleLF: 'Burrito', title: 'Declasse Burrito' },
    'burrito3': { price: 110000, class: 'com', capacity: 86000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 0.7, brandLF: 'Declasse', titleLF: 'Burrito', title: 'Declasse Burrito' },
    'burrito4': { price: 11270, class: 'null', capacity: 100000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 0.7, brandLF: 'Declasse', titleLF: 'Burrito', title: 'Declasse Burrito' },
    'burrito5': { price: 13000, class: 'null', capacity: 100000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 0.7, brandLF: 'Declasse', titleLF: 'Burrito', title: 'Declasse Burrito' },
    'camper': { price: 36000, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 240, fuelConsumption: 0.8, brandLF: 'Brute', titleLF: 'Camper', title: 'Brute Camper' },
    'gburrito': { price: 27380, class: 'null', capacity: 2000000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 0.9, brandLF: 'Declasse', titleLF: 'Gang Burrito', title: 'Declasse Gang Burrito' },
    'gburrito2': { price: 24000, class: 'null', capacity: 2000000, fuelType: 'Diesel', fuelCap: 60, fuelConsumption: 0.9, brandLF: 'Declasse', titleLF: 'Gang Burrito', title: 'Declasse Gang Burrito' },
    'journey': { price: 27000, class: 'low', capacity: 70000, fuelType: 'Diesel', fuelCap: 150, fuelConsumption: 2, brandLF: 'Zirconium', titleLF: 'Journey', title: 'Zirconium Journey' },
    'minivan': { price: 7300, class: 'null', capacity: 90000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 0.4, brandLF: 'Vapid', titleLF: 'Minivan', title: 'Vapid Minivan' },
    'minivan2': { price: 7700, class: 'null', capacity: 45000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 0.4, brandLF: 'Vapid', titleLF: 'Minivan Custom', title: 'Vapid Minivan Custom' },
    'paradise': { price: 8000, class: 'null', capacity: 200000, fuelType: 'Diesel', fuelCap: 60, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Paradise', title: 'Bravado Paradise' },
    'pony': { price: 8000, class: 'null', capacity: 200000, fuelType: 'Diesel', fuelCap: 62, fuelConsumption: 0.9, brandLF: 'Brute', titleLF: 'Pony', title: 'Brute Pony' },
    'pony2': { price: 8000, class: 'null', capacity: 200000, fuelType: 'Diesel', fuelCap: 62, fuelConsumption: 0.9, brandLF: 'Brute', titleLF: 'Pony', title: 'Brute Pony' },
    'rumpo': { price: 8000, class: 'null', capacity: 750000, fuelType: 'Diesel', fuelCap: 62, fuelConsumption: 0.9, brandLF: 'Bravado', titleLF: 'Rumpo', title: 'Bravado Rumpo' },
    'rumpo2': { price: 9000, class: 'null', capacity: 200000, fuelType: 'Diesel', fuelCap: 62, fuelConsumption: 0.9, brandLF: 'Bravado', titleLF: 'Rumpo', title: 'Bravado Rumpo' },
    'rumpo3': { price: 16000, class: 'null', capacity: 150000, fuelType: 'Diesel', fuelCap: 65, fuelConsumption: 1.2, brandLF: 'Bravado', titleLF: 'Rumpo Custom', title: 'Bravado Rumpo Custom' },
    'speedo': { price: 33000, class: 'null', capacity: 2000000, fuelType: 'Diesel', fuelCap: 60, fuelConsumption: 0.9, brandLF: 'Vapid', titleLF: 'Speedo', title: 'Vapid Speedo' },
    'speedo2': { price: 16000, class: 'unique', capacity: 200000, fuelType: 'Diesel', fuelCap: 65, fuelConsumption: 0.9, brandLF: 'Vapid', titleLF: 'Clown Van', title: 'Vapid Clown Van' },
    'surfer': { price: 7000, class: 'null', capacity: 120000, fuelType: 'Diesel', fuelCap: 90, fuelConsumption: 2, brandLF: 'BF', titleLF: 'Surfer', title: 'BF Surfer' },
    'surfer2': { price: 3400, class: 'null', capacity: 120000, fuelType: 'Diesel', fuelCap: 90, fuelConsumption: 2, brandLF: 'BF', titleLF: 'Surfer', title: 'BF Surfer' },
    'taco': { price: 11650, class: 'null', capacity: 200000, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 0.8, brandLF: 'Brute', titleLF: 'Taco Van', title: 'Taco Van' },
    'youga': { price: 100000, class: 'com', capacity: 86000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 0.7, brandLF: 'Bravado', titleLF: 'Youga', title: 'Bravado Youga' },
    'youga2': { price: 80000, class: 'com', capacity: 69000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 0.9, brandLF: 'Bravado', titleLF: 'Youga Classic', title: 'Bravado Youga Classic' },
    'youga3': { price: 105000, class: 'com', capacity: 79000, fuelType: 'Diesel', fuelCap: 80, fuelConsumption: 0.7, brandLF: 'Bravado', titleLF: 'Youga Classic 4x4', title: 'Bravado Youga Classic 4x4' },
    'journey2': { price: 200000, class: 'unique', capacity: 200000, fuelType: 'Diesel', fuelCap: 150, fuelConsumption: 2, brandLF: 'Zirconium', titleLF: 'Journey II', title: 'Zirconium Journey II' },
    'surfer3': { price: 90000, class: 'unique', capacity: 120000, fuelType: 'Diesel', fuelCap: 90, fuelConsumption: 2, brandLF: 'BF', titleLF: 'Surfer Custom', title: 'BF Surfer Custom' },
    'speedo5': { price: 100000, class: 'unique', capacity: 2000000, fuelType: 'Diesel', fuelCap: 60, fuelConsumption: 2, brandLF: 'Vapid', titleLF: 'Ratel', title: 'Vapid Ratel' },
    // Vans - конец

    //Вертолеты Donate - начало
    'buzzard2c': { price: 6250000, tags: ["heli"], class: 'helidonate', capacity: 500000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Nagasaki', titleLF: 'Buzzard S', title: 'Buzzard S' },
    'froggerc': { price: 5000000, tags: ["heli"], class: 'helidonate', capacity: 400000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Maibatsu', titleLF: 'Frogger S', title: 'Frogger S' },
    'maverickc': { price: 3125000, tags: ["heli"], class: 'helidonate', capacity: 250000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Maverick S', title: 'Maverick S' },
    'supervolitoc': { price: 8750000, tags: ["heli"], class: 'helidonate', capacity: 700000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'SuperVolito S', title: 'Buckingham SuperVolito S' },
    'supervolito2c': { price: 10000000, tags: ["heli"], class: 'helidonate', capacity: 800000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'SuperVolito Carbone S', title: 'Buckingham SuperVolito Carbone S' },
    'swiftc': { price: 12500000, tags: ["heli"], class: 'helidonate', capacity: 1000000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Swift S', title: 'Buckingham Swift S' },
    'volatusc': { price: 18750000, tags: ["heli"], class: 'helidonate', capacity: 1500000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'Buckingham', titleLF: 'Volatus S', title: 'Buckingham Volatus S' },
    'sparrowc': { price: 5000000, tags: ["heli"], class: 'helidonate', capacity: 400000, fuelType: 'Regular', fuelCap: 1000, fuelConsumption: 5, brandLF: 'HVY', titleLF: 'Sparrow S', title: 'Sparrow S' },
    //Вертолеты Donate - конец

    // DLC: The Contract - Начало
    'mule5': { price: 100000, class: 'null', capacity: 100000, fuelType: 'Diesel', fuelCap: 100, fuelConsumption: 1, brandLF: 'Maibatsu', titleLF: 'Mule', title: 'Maibatsu Mule' },
    'reever': { price: 150000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1, brandLF: 'Western', titleLF: 'Reever', title: 'Western Reever' },
    'astron': { price: 200000, class: 'mid+', capacity: 10000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1, brandLF: 'Pfister', titleLF: 'Astron', title: 'Pfister Astron' },
    'shinobi': { price: 150000, tags: ["moto"], class: 'moto', capacity: 0, fuelType: 'Premium', fuelCap: 40, fuelConsumption: 1, brandLF: 'Nagasaki', titleLF: 'Shinobi', title: 'Nagasaki Shinobi' },
    'buffalo4': { price: 150000, class: 'mid+', capacity: 50000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Buffalo STX', title: 'Bravado Buffalo STX' },
    'patriot3': { price: 150000, class: 'null', capacity: 40000, fuelType: 'Regular', fuelCap: 90, fuelConsumption: 1, brandLF: 'Mammoth', titleLF: 'Patriot Mil-Spec', title: 'Mammoth Patriot Mil-Spec' },
    'cinquemila': { price: 300000, class: 'mid+', capacity: 50000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1, brandLF: 'Lampadati', titleLF: 'Cinquemila', title: 'Lampadati Cinquemila' },
    'deity': { price: 350000, class: 'prem', capacity: 60000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1, brandLF: 'Enus', titleLF: 'Deity', title: 'Enus Deity' },
    'comet7': { price: 400000, class: 'prem', capacity: 40000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandLF: 'Pfister', titleLF: 'Comet S2 Cabrio', title: 'Pfister Comet S2 Cabrio' },
    'champion': { price: 400000, class: 'prem', capacity: 20000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1, brandLF: 'Dewbauchee', titleLF: 'Champion', title: 'Dewbauchee Champion' },
    'ignus': { price: 450000, class: 'prem', capacity: 20000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1, brandLF: 'Pegassi', titleLF: 'Ignus', title: 'Pegassi Ignus' },
    'zeno': { price: 450000, class: 'prem', capacity: 20000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1, brandLF: 'Overflod', titleLF: 'Zeno', title: 'Overflod Zeno' },
    'baller7': { price: 250000, class: 'mid+', capacity: 65000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1, brandLF: 'Gallivanter', titleLF: 'Baller ST', title: 'Gallivanter Baller ST' },
    'granger2': { price: 150000, class: 'mid+', capacity: 65000, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Granger 3600LX', title: 'Declasse Granger 3600LX' },
    'iwagen': { price: 150000, class: 'mid', capacity: 35000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 1, brandLF: 'Obey', titleLF: 'I-Wagen', title: 'Obey I-Wagen' },
    'jubilee': { price: 400000, class: 'prem', capacity: 65000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1, brandLF: 'Enus', titleLF: 'Jubilee', title: 'Enus Jubilee' },
    'youga4': { price: 50000, class: 'null', capacity: 100000, fuelType: 'Diesel', fuelCap: 100, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Youga Custom', title: 'Vapid Youga Custom' },
    // DLC: The Contract - Конец

    // SeasonPass 5 Battle-Case Lore Friendly - начало
    'turismo3': { price: 300000, class: 'unique', seasonPass: 5, capacity: 40000, fuelType: 'Premium', fuelCap: 74, fuelConsumption: 2.2, brandLF: 'Grotti', titleLF: 'Turismo Omaggio', title: 'Grotti Turismo Omaggio' },
    'cavalcade3': { price: 200000, class: 'unique', seasonPass: 5, capacity: 90000, fuelType: 'Premium', fuelCap: 92, fuelConsumption: 1.9, brandLF: 'Albany', titleLF: 'Cavalcade XL', title: 'Albany Cavalcade XL' },
    'vigero3': { price: 180000, class: 'unique', seasonPass: 5, capacity: 70000, fuelType: 'Premium', fuelCap: 68, fuelConsumption: 2.1, brandLF: 'Declasse', titleLF: 'Vigero ZX Convertible', title: 'Declasse Vigero ZX Convertible' },
    'dominator9': { price: 160000, class: 'unique', seasonPass: 5, capacity: 65000, fuelType: 'Premium', fuelCap: 74, fuelConsumption: 2.1, brandLF: 'Vapid', titleLF: 'Dominator GT', title: 'Vapid Dominator GT' },
    'baller8': { price: 140000, class: 'unique', seasonPass: 5, capacity: 80000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.8, brandLF: 'Gallivanter', titleLF: 'Baller ST-D', title: 'Gallivanter Baller ST-D' },
    'aleutian': { price: 110000, class: 'unique', seasonPass: 5, capacity: 75000, fuelType: 'Plus', fuelCap: 82, fuelConsumption: 1.7, brandLF: 'Vapid', titleLF: 'Aleutian', title: 'Vapid Aleutian' },
    'terminus': { price: 160000, class: 'unique', seasonPass: 5, capacity: 60000, fuelType: 'Plus', fuelCap: 68, fuelConsumption: 1.8, brandLF: 'Canis', titleLF: 'Terminus', title: 'Canis Terminus' },
    'vivanite': { price: 140000, class: 'unique', seasonPass: 5, capacity: 70000, fuelType: 'Plus', fuelCap: 90, fuelConsumption: 1.7, brandLF: 'Karin', titleLF: 'Vivanite', title: 'Karin Vivanite' },
    'dorado': { price: 90000, class: 'unique', seasonPass: 5, capacity: 55000, fuelType: 'Regular', fuelCap: 88, fuelConsumption: 1.9, brandLF: 'Bravado', titleLF: 'Dorado', title: 'Bravado Dorado' },
    'fr36': { price: 70000, class: 'unique', seasonPass: 5, capacity: 45000, fuelType: 'Regular', fuelCap: 58, fuelConsumption: 1.5, brandLF: 'Fathom', titleLF: 'FR36', title: 'Fathom FR36' },
    'impaler5': { price: 50000, class: 'unique', seasonPass: 5, capacity: 45000, fuelType: 'Regular', fuelCap: 64, fuelConsumption: 1.6, brandLF: 'Declasse', titleLF: 'Impaler SZ', title: 'Declasse Impaler SZ' },
    'impaler6': { price: 30000, class: 'unique', seasonPass: 5, capacity: 50000, fuelType: 'Regular', fuelCap: 72, fuelConsumption: 2.3, brandLF: 'Declasse', titleLF: 'Impaler LX', title: 'Declasse Impaler LX' },
    // SeasonPass 5 Battle-Case Lore Friendly - конец

    // SeasonPass 6 Battle-Case Lore Friendly - начало
    'pipistrello': { price: 400000, class: 'unique', seasonPass: 6, capacity: 0, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.2, brandLF: 'Overflod', titleLF: 'Pipistrello', title: 'Overflod Pipistrello' },
    'paragon3': { price: 350000, class: 'unique', seasonPass: 6, capacity: 50000, fuelType: 'Premium', fuelCap: 92, fuelConsumption: 1.9, brandLF: 'Enus', titleLF: 'Paragon S', title: 'Enus Paragon S' },
    'niobe': { price: 300000, class: 'unique', seasonPass: 6, capacity: 30000, fuelType: 'Premium', fuelCap: 68, fuelConsumption: 2.1, brandLF: 'Ubermacht', titleLF: 'Niobe', title: 'Ubermacht Niobe' },
    'castigator': { price: 250000, class: 'unique', seasonPass: 6, capacity: 90000, fuelType: 'Plus', fuelCap: 74, fuelConsumption: 2.1, brandLF: 'Canis', titleLF: 'Castigator', title: 'Canis Castigator' },
    'envisage': { price: 200000, class: 'unique', seasonPass: 6, capacity: 0, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 1.8, brandLF: 'Bollokan', titleLF: 'Envisage', title: 'Bollokan Envisage' },
    'coquette5': { price: 150000, class: 'unique', seasonPass: 6, capacity: 70000, fuelType: 'Plus', fuelCap: 90, fuelConsumption: 1.7, brandLF: 'Invetero', titleLF: 'Coquette D1', title: 'Invetero Coquette D1' },
    'yosemite1500': { price: 120000, class: 'unique', seasonPass: 6, capacity: 120000, fuelType: 'Diesel', fuelCap: 88, fuelConsumption: 1.9, brandLF: 'Declasse', titleLF: 'Yosemite 1500', title: 'Declasse Yosemite 1500' },
    'eurosx32': { price: 100000, class: 'unique', seasonPass: 6, capacity: 70000, fuelType: 'Regular', fuelCap: 58, fuelConsumption: 1.5, brandLF: 'Annis', titleLF: 'Euros X32', title: 'Annis Euros X32' },
    'vorschlaghammer': { price: 80000, class: 'unique', seasonPass: 6, capacity: 60000, fuelType: 'Regular', fuelCap: 64, fuelConsumption: 1.6, brandLF: 'Benefactor', titleLF: 'Vorschlaghammer', title: 'Benefactor Vorschlaghammer' },
    'dominator10': { price: 60000, class: 'unique', seasonPass: 6, capacity: 60000, fuelType: 'Regular', fuelCap: 72, fuelConsumption: 2.3, brandLF: 'Vapid', titleLF: 'Dominator FX', title: 'Vapid Dominator FX' },
    // SeasonPass 6 Battle-Case Lore Friendly - конец

    // Emergency - начало. Исключить для игроков.
    'ambulance': { price: 65000, class: 'null', capacity: 500000, fuelType: 'Regular', fuelCap: 80, fuelConsumption: 1.2, brandLF: 'Brute ', titleLF: 'Burrito AMB', title: 'Ambulance' },
    'fbi': { price: 97000, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Buffalo', title: 'FIB' },
    'fbi2': { price: 41000, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Granger', title: 'FIB' },
    'firetruk': { price: 112000, class: 'null', capacity: 130000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'MLT', titleLF: 'Tarrton FT', title: 'Fire Truck' },
    'lguard': { price: 36000, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Granger', title: 'Lifeguard' },
    'pbus': { price: 71000, class: 'null', capacity: 175000, fuelType: 'Regular', fuelCap: 100, fuelConsumption: 2.5, brandLF: 'Brute ', titleLF: 'Cannon PB', title: 'Prison Bus' },
    'police': { price: 33650, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Stanier', title: 'Police Cruiser' },
    'police2': { price: 120000, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Buffalo', title: 'Police Cruiser' },
    'police3': { price: 68400, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Interceptor', title: 'Police Cruiser' },
    'police4': { price: 47000, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Stanier', title: 'Unmarked Cruiser' },
    'policeb': { price: 35000, class: 'null', capacity: 20000, fuelType: 'Regular', fuelCap: 30, fuelConsumption: 0.5, brandLF: 'WMC', titleLF: 'Wolfsbane', title: 'Police Bike' },
    'policeold1': { price: 63000, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Rancher XL', title: 'Police Rancher' },
    'policeold2': { price: 27000, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Albany', titleLF: 'Esperanto', title: 'Police Roadcruiser' },
    'policet': { price: 107000, class: 'null', capacity: 200000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Burrito', title: 'Police Transporter' },
    'pranger': { price: 86000, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Granger', title: 'Park Ranger' },
    'riot': { price: 475000, class: 'null', capacity: 250000, fuelType: 'Regular', fuelCap: 120, fuelConsumption: 2, brandLF: 'Brute ', titleLF: 'Riot', title: 'Police Riot' },
    'riot2': { price: 500000, class: 'null', capacity: 400000, fuelType: 'Regular', fuelCap: 120, fuelConsumption: 2, brandLF: 'Brute ', titleLF: 'Riot RCV', title: 'RCV' },
    'sheriff': { price: 31400, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Stanier', title: 'Sheriff Cruiser' },
    'sheriff2': { price: 38650, class: 'null', capacity: 100000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Granger', title: 'Sheriff SUV' },
    'predator': { price: 217000, tags: ["boat"], class: 'null', capacity: 150000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 0.1, brandLF: 'Shitzu', titleLF: 'Predator', title: 'Police Predator' },
    'polmav': { price: 375000, tags: ["heli"], class: 'null', capacity: 250000, fuelType: 'Regular', fuelCap: 1500, fuelConsumption: 6, brandLF: 'Buckingham', titleLF: 'Maverick', title: 'Police Maverick' },
    // Emergency - конец. Исключить для игроков.

    // Emergency Mod Pack - начало
    'polbuz': { price: 1000000, tags: ["heli"], class: 'null', capacity: 200000, fuelType: 'Plus', fuelCap: 1000, fuelConsumption: 3.5, brandLF: 'Nagasaki', titleLF: 'Buzzard', title: 'Police Buzzard' },
    'polbuz1': { price: 1000000, tags: ["heli"], class: 'null', capacity: 200000, fuelType: 'Plus', fuelCap: 1000, fuelConsumption: 3.5, brandLF: 'Nagasaki', titleLF: 'Buzzard', title: 'Police Buzzard' },
    'polbuz2': { price: 1000000, tags: ["heli"], class: 'null', capacity: 200000, fuelType: 'Plus', fuelCap: 1000, fuelConsumption: 3.5, brandLF: 'Nagasaki', titleLF: 'Buzzard', title: 'Ambulance Buzzard' },
    'polbuz3': { price: 1000000, tags: ["heli"], class: 'null', capacity: 200000, fuelType: 'Plus', fuelCap: 1000, fuelConsumption: 3.5, brandLF: 'Nagasaki', titleLF: 'Buzzard', title: 'Sheriff LSC Buzzard' },
    'polbuz4': { price: 1000000, tags: ["heli"], class: 'null', capacity: 200000, fuelType: 'Plus', fuelCap: 1000, fuelConsumption: 3.5, brandLF: 'Nagasaki', titleLF: 'Buzzard', title: 'SANG Buzzard' },
    'polbuz5': { price: 1000000, tags: ["heli"], class: 'null', capacity: 200000, fuelType: 'Plus', fuelCap: 1000, fuelConsumption: 3.5, brandLF: 'Nagasaki', titleLF: 'Buzzard', title: 'Weazel News Buzzard' },
    'polbuz6': { price: 1000000, tags: ["heli"], class: 'null', capacity: 200000, fuelType: 'Plus', fuelCap: 1000, fuelConsumption: 3.5, brandLF: 'Nagasaki', titleLF: 'Buzzard', title: 'FIB Buzzard' },
    'bulletems': { price: 300000, class: 'null', capacity: 40000, fuelType: 'Premium', fuelCap: 84, fuelConsumption: 2.2, brandLF: 'Vapid', titleLF: 'Bullet', title: 'Vapid Bullet Highway Patrol' },
    'coquetteems': { price: 300000, class: 'null', capacity: 35000, fuelType: 'Premium', fuelCap: 88, fuelConsumption: 2.2, brandLF: 'Invetero', titleLF: 'Coquette', title: 'Invetero Coquette Highway Patrol' },
    'dominator3ems': { price: 300000, class: 'null', capacity: 60000, fuelType: 'Premium', fuelCap: 98, fuelConsumption: 2.5, brandLF: 'Vapid', titleLF: 'Dominator GTX', title: 'Vapid Dominator GTX Highway Patrol' },
    'vigero2ems': { price: 300000, class: 'null', capacity: 65000, fuelType: 'Premium', fuelCap: 96, fuelConsumption: 2.5, brandLF: 'Declasse', titleLF: 'Vigero ZX', title: 'Declasse Vigero ZX Highway Patrol' },
    'tor_police_buffalo': { price: 300000, class: 'null', capacity: 80000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Buffalo', title: 'Bravado Buffalo Police' },
    'tor_police_contender': { price: 300000, class: 'null', capacity: 250000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Contender', title: 'Vapid Contender Police' },
    'tor_police_cruiser': { price: 300000, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Stanier', title: 'Vapid Cruiser Police' },
    'tor_police_cruiser2': { price: 300000, class: 'null', capacity: 80000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Interceptor', title: 'Vapid Cruiser II Police' },
    'tor_police_insurgent': { price: 300000, class: 'null', capacity: 500000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Insurgent', title: 'HVY Insurgent Police' },
    'tor_police_scout': { price: 300000, class: 'null', capacity: 120000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Scout', title: 'Vapid Scout Police' },
    'tor_police_scout_k9': { price: 300000, class: 'null', capacity: 120000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Scout', title: 'Vapid Scout Police K9' },
    'tor_police_speedo': { price: 300000, class: 'null', capacity: 2000000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Speedo', title: 'Vapid Speedo Police' },
    'tor_police_unmarked': { price: 300000, class: 'null', capacity: 80000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Cruiser', title: 'Vapid Cruiser Unmarked P' },
    'tor_police_speedunit': { price: 300000, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Gauntlet Hellfire', title: 'Bravado Gauntlet Hellfire Police' },
    'tor_sheriff_cruiser': { price: 300000, class: 'null', capacity: 80000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Cruiser', title: 'Cruiser Sheriff' },
    'tor_sheriff_cheval': { price: 300000, class: 'null', capacity: 80000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Cheval', title: 'Bravado Cheval Sheriff' },
    'tor_sheriff_granger': { price: 300000, class: 'null', capacity: 150000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 1, brandLF: 'Declasse', titleLF: 'Granger', title: 'Declasse Granger Sheriff' },
    'tor_sheriff_insurgent': { price: 300000, class: 'null', capacity: 500000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Insurgent', title: 'HVY Insurgent Sheriff' },
    'tor_sheriff_scout': { price: 300000, class: 'null', capacity: 120000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Scout', title: 'Vapid Scout Sheriff' },
    'tor_sheriff_scout_k9': { price: 300000, class: 'null', capacity: 120000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Scout', title: 'Vapid Scout Sheriff K9' },
    'tor_sheriff_speedunit': { price: 300000, class: 'null', capacity: 75000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Gauntlet Hellfire', title: 'Bravado Gauntlet Hellfire Sheriff' },
    'tor_sheriff_unmarked': { price: 300000, class: 'null', capacity: 80000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Cruiser', title: 'Vapid Cruiser Unmarked S' },
    'tor_sang_insurgent': { price: 300000, class: 'null', capacity: 500000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Insurgent', title: 'HVY Insurgent SANG' },
    'tor_fib_insurgent': { price: 300000, class: 'null', capacity: 500000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 1, brandLF: 'HVY', titleLF: 'Insurgent', title: 'HVY Insurgent FIB' },
    'tor_fib_armor1': { price: 300000, class: 'null', capacity: 120000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1, brandLF: 'Benefactor', titleLF: 'XLS', title: 'Benefactor XLS (Armored)' },
    'tor_fib_jugular': { price: 300000, class: 'null', capacity: 100000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1, brandLF: 'Ocelot', titleLF: 'Jugular', title: 'Ocelot Jugular Unmarked' },
    'tor_fib_speedo': { price: 300000, class: 'null', capacity: 300000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Speedo', title: 'Vapid Speedo Unmarked' },
    'tor_fib_neon': { price: 300000, class: 'null', capacity: 80000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 3, brandLF: 'Pfister', titleLF: 'Neon', title: 'Pfister Neon Unmarked' },
    'tor_fib_rumpo': { price: 300000, class: 'null', capacity: 275000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Rumpo', title: 'Bravado Rumpo Unmarked' },
    'tor_fib_novak': { price: 300000, class: 'null', capacity: 110000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Lampadati', titleLF: 'Novak', title: 'Lampadati Novak Unmarked' },
    'tor_fib_caracara': { price: 300000, class: 'null', capacity: 250000, fuelType: 'Diesel', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Caracara', title: 'Vapid Caracara Unmarked' },
    'tor_fib_gresley': { price: 300000, class: 'null', capacity: 120000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Gresley', title: 'Bravado Gresley Unmarked' },
    'tor_unmarked_scout': { price: 300000, class: 'null', capacity: 120000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Scout', title: 'Vapid Scout Unmarked F' },
    'tor_fib_speedunit': { price: 300000, class: 'null', capacity: 75000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1, brandLF: 'Bravado', titleLF: 'Gauntlet Hellfire', title: 'Bravado Gauntlet Hellfire FIB' },
    'tor_fib_speedunit1': { price: 300000, class: 'null', capacity: 75000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Dominator GTX', title: 'Vapid Dominator GTX FIB' },
    'bcat': { price: 300000, class: 'null', capacity: 2000000, fuelType: 'Diesel', fuelCap: 120, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Tarv', title: 'Vapid Tarv' },
    'emsnspeedo': { price: 300000, class: 'null', capacity: 1000000, fuelType: 'Plus', fuelCap: 90, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Speedo N', title: 'Vapid Speedo II EMS' },
    'emsroamer': { price: 300000, class: 'null', capacity: 200000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1, brandLF: 'Dundreary', titleLF: 'Landroamer', title: 'Dundreary Landstalker EMS' },
    'pscoutnew': { price: 300000, class: 'null', capacity: 120000, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Scout', title: 'Vapid Scout II Police' },
    'swatstalker': { price: 300000, class: 'null', capacity: 135000, fuelType: 'Plus', fuelCap: 90, fuelConsumption: 1.2, brandLF: 'Dundreary', titleLF: 'Landstalker XL', title: 'Dundreary Landstalker II Unmarked' },
    // Emergency Mod Pack - конец

    // Vanilla Mods
    'zr380c': { price: 270000, class: 'mid', capacity: 0, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.5, brandLF: 'Annis', titleLF: 'ZR-380 Custom', title: 'Annis ZR-380 Custom' },
    'zr380s': { price: 320000, class: 'mid', capacity: 45000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.5, brandLF: 'Annis', titleLF: 'ZR-380', title: 'Annis ZR-380' },
    'zionks': { price: 140000, class: 'mid', capacity: 45000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.1, brandLF: 'Benefactor', titleLF: 'Zion Classic Krieger Specials', title: 'Krieger Specials Zion Classic' },
    'sigma2': { price: 95000, class: 'low+', capacity: 35000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Karin', titleLF: 'Sigma-Two', title: 'Karin Sigma-Two' },
    'schwartzerc': { price: 65000, class: 'low', capacity: 40000, fuelType: 'Diesel', fuelCap: 60, fuelConsumption: 1.2, brandLF: 'Benefactor', titleLF: 'Schwartzer Classic', title: 'Benefactor Schwartzer Classic' },
    's230': { price: 120000, class: 'low+', capacity: 40000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1.1, brandLF: 'Annis', titleLF: 'S-230', title: 'Annis S-230' },
    'nspeedo': { price: 700000, class: 'com', capacity: 110000, fuelType: 'Diesel', fuelCap: 80, fuelConsumption: 1, brandLF: 'Vapid', titleLF: 'Speedo Express', title: 'Vapid Speedo Express' },
    'gauntlets': { price: 105000, class: 'low+', capacity: 45000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1.4, brandLF: 'Bravado', titleLF: 'Gauntlet S', title: 'Bravado Gauntlet S' },
    'clubgtr': { price: 45000, class: 'low+', capacity: 35000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.3, brandLF: 'BF', titleLF: 'Club GTR', title: 'BF Club GTR' },
    // Vanilla Mods

    // Vanilla Mods 2.0
    'bison4': { price: 190000, class: 'com', capacity: 89000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.5, brandLF: 'Bravado', titleLF: 'Bison 4', title: 'Bravado Bison 4' },
    'yosemiteswb': { price: 165000, class: 'com', capacity: 90000, fuelType: 'Diesel', fuelCap: 75, fuelConsumption: 1.5, brandLF: 'Declasse', titleLF: 'Yosemite SWB', title: 'Declasse Yosemite SWB' },
    'ferocid': { price: 150000, class: 'low+', capacity: 45000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandLF: 'Bravado', titleLF: 'Feroci Custom', title: 'Bravado Feroci Custom' },
    'hellion2': { price: 200000, class: 'com', capacity: 95000, fuelType: 'Diesel', fuelCap: 80, fuelConsumption: 1.5, brandLF: 'Annis', titleLF: 'Hellion XL', title: 'Annis Hellion XL' },
    'jester5': { price: 300000, class: 'mid', capacity: 60000, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1, brandLF: 'Dinka', titleLF: 'Jester (Vanilla)', title: 'Dinka Jester (Vanilla)' },
    'scout': { price: 82000, class: 'mid', capacity: 95000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.3, brandLF: 'Vapid', titleLF: 'Scout', title: 'Vapid Scout' },
    'seraph3': { price: 55000, class: 'low+', capacity: 50000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.3, brandLF: 'Ubermacht', titleLF: 'Seraph XS', title: 'Ubermacht Seraph XS' },
    'skart': { price: 50000, class: 'null', capacity: 0, fuelType: 'Regular', fuelCap: 15, fuelConsumption: 0.2, brandLF: 'Nagasaki', titleLF: 'Shopping Kart', title: 'Nagasaki Shopping Kart' },
    'vincent2': { price: 120000, class: 'low+', capacity: 60000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.3, brandLF: 'Maibatsu', titleLF: 'Vincent Sport', title: 'Maibatsu Vincent Sport' },
    'packer2': { price: 225000, class: 'null', capacity: 50000, fuelType: 'Diesel', fuelCap: 1200, fuelConsumption: 4.5, brandLF: 'MTL', titleLF: 'Semi-Truck', title: 'MTL Semi-Truck' },
    'towtruck3': { price: 150000, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 100, fuelConsumption: 2.3, brandLF: 'MTL', titleLF: 'Tow Truck', title: 'MTL Tow Truck' },
    'pounder3': { price: 370000, class: 'com', capacity: 205000, fuelType: 'Diesel', fuelCap: 1000, fuelConsumption: 3.8, brandLF: 'MTL', titleLF: 'Flatbed', title: 'MTL Flatbed' },
    'flatbed2': { price: 150000, class: 'null', capacity: 0, fuelType: 'Diesel', fuelCap: 500, fuelConsumption: 3, brandLF: 'MTL', titleLF: 'Flatbed', title: 'MTL Flatbed' },
    'jackgpr': { price: 230000, class: 'mid+', capacity: 50000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.1, brandLF: 'Ocelot', titleLF: 'Jackal Custom', title: 'Ocelot Jackal Custom' },
    'pentro': { price: 100000, class: 'low+', capacity: 50000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 0.8, brandLF: 'Maibatsu', titleLF: 'Penumbra Retro', title: 'Maibatsu Penumbra Retro' },
    'pentro2': { price: 100000, class: 'low+', capacity: 50000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 0.8, brandLF: 'Maibatsu', titleLF: 'Penumbra Retro (RHD)', title: 'Maibatsu Penumbra Retro (RHD)' },
    'pentro3': { price: 110000, class: 'low+', capacity: 50000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 0.8, brandLF: 'Maibatsu', titleLF: 'Penumbra Retro Spyder', title: 'Maibatsu Penumbra Retro Spyder' },
    'pentrogpr': { price: 120000, class: 'low+', capacity: 55000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 0.8, brandLF: 'Maibatsu', titleLF: 'Penumbra Retro Custom', title: 'Maibatsu Penumbra Retro Custom' },
    'pentrogpr2': { price: 120000, class: 'low+', capacity: 55000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 0.8, brandLF: 'Maibatsu', titleLF: 'Penumbra Retro S-Custom', title: 'Maibatsu Penumbra Retro S-Custom' },
    // Vanilla Mods 2.0

    // Donate - начало
    'bdivo': { price: 20000000, tags: ["donate"], class: 'unique', capacity: 40000, fuelType: 'Premium', fuelCap: 95, fuelConsumption: 1.6, brandRL: 'Bugatti', titleRL: 'Divo', brandLF: 'Truffade', titleLF: 'Vivo', title: 'Bugatti Divo', safeTitle: 'Truffade Vivo' },
    'chiron19': { price: 15000000, tags: ["donate"], class: 'unique', capacity: 70000, fuelType: 'Premium', fuelCap: 85, fuelConsumption: 1.3, brandRL: 'Bugatti', titleRL: 'Chiron', brandLF: 'Truffade', titleLF: 'Shiron', title: 'Bugatti Chiron', safeTitle: 'Truffade Shiron' },
    'laferrari': { price: 15000000, tags: ["donate"], class: 'unique', capacity: 50000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.5, brandRL: 'Ferrari', titleRL: 'LaFerrari', brandLF: 'Grotti', titleLF: 'LaGrotti', title: 'Ferrari LaFerrari', safeTitle: 'Grotti LaGrotti' },
    'asvj': { price: 10000000, tags: ["donate"], class: 'unique', capacity: 60000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1.1, brandRL: 'Lamborghini', titleRL: 'Aventador SVJ', brandLF: 'Pegassi', titleLF: 'Avendore SVG', title: 'Lamborghini Aventador SVJ', safeTitle: 'Pegassi Avendore SVG' },
    'mp1': { price: 10000000, tags: ["donate"], class: 'unique', capacity: 50000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1.8, brandRL: 'McLaren', titleRL: 'P1', brandLF: 'Progen', titleLF: 'F1', title: 'McLaren P1', safeTitle: 'Progen F1' },
    'cullinan': { price: 10000000, tags: ["donate"], class: 'unique', capacity: 180000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandRL: 'Rolls-Royce', titleRL: 'Cullinan', brandLF: 'Enus', titleLF: 'Callinon', title: 'Rolls-Royce Cullinan', safeTitle: 'Enus Callinon' },
    'g63amg6x6': { price: 10000000, tags: ["donate"], class: 'unique', capacity: 200000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.5, brandRL: 'Mercedes-Benz', titleRL: 'G-Class W463 6x6', brandLF: 'Benefactor', titleLF: 'G-Series M463 6x6', title: 'Mercedes-Benz G-Class W463 6x6', safeTitle: 'Benefactor G-Series M463 6x6' },
    'urus': { price: 9000000, tags: ["donate"], class: 'unique', capacity: 180000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandRL: 'Lamborghini', titleRL: 'Urus', brandLF: 'Pegassi', titleLF: 'Ursus', title: 'Lamborghini Urus', safeTitle: 'Pegassi Ursus' },
    'huracan': { price: 8000000, tags: ["donate"], class: 'unique', capacity: 50000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.6, brandRL: 'Lamborghini', titleRL: 'Huracan', brandLF: 'Pegassi', titleLF: 'Hurricane', title: 'Lamborghini Huracan', safeTitle: 'Pegassi Hurricane' },
    'continental': { price: 7000000, tags: ["donate"], class: 'unique', capacity: 140000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandRL: 'Bentley', titleRL: 'Continental GT', brandLF: 'Enus', titleLF: 'Mainland', title: 'Bentley Continental GT', safeTitle: 'Enus Mainland' },
    'pts21': { price: 7000000, tags: ["donate"], class: 'unique', capacity: 50000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Porsche', titleRL: '911 992-series', brandLF: 'Pfister', titleLF: '919 922-series', title: 'Porsche 911 992-series', safeTitle: 'Pfister 919 922-series' },
    '63gls2': { price: 7000000, tags: ["donate"], class: 'unique', capacity: 150000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandRL: 'Mercedes-Benz', titleRL: 'GLS X167', brandLF: 'Benefactor', titleLF: 'JLS Y167', title: 'Mercedes-Benz GLS X167', safeTitle: 'Benefactor JLS Y167' },
    'ghost': { price: 6500000, tags: ["donate"], class: 'unique', capacity: 160000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandRL: 'Rolls-Royce', titleRL: 'Ghost', brandLF: 'Enus', titleLF: 'Shade', title: 'Rolls-Royce Ghost', safeTitle: 'Enus Shade' },
    'g63': { price: 5500000, tags: ["donate"], class: 'unique', capacity: 170000, fuelType: 'Premium', fuelCap: 104, fuelConsumption: 1.4, brandRL: 'Mercedes-Benz', titleRL: 'G-Class W464', brandLF: 'Benefactor', titleLF: 'G-Series M464', title: 'Mercedes-Benz G-Class W464', safeTitle: 'Benefactor G-Series M464' },
    's63cab': { price: 5500000, tags: ["donate"], class: 'unique', capacity: 60000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1.3, brandRL: 'Mercedes-Benz', titleRL: 'S-Class A217', brandLF: 'Benefactor', titleLF: 'S-Series B217', title: 'Mercedes-Benz S-Class A217', safeTitle: 'Benefactor S-Series B217' },
    'm8gc': { price: 5500000, tags: ["donate"], class: 'unique', capacity: 100000, fuelType: 'Premium', fuelCap: 78, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: 'M8 Gran Coupe', brandLF: 'Ubermacht', titleLF: 'W8 GC', title: 'BMW M8 Gran Coupe', safeTitle: 'Ubermacht W8 GC' },
    'panamera17turbo': { price: 5500000, tags: ["donate"], class: 'unique', capacity: 100000, fuelType: 'Premium', fuelCap: 72, fuelConsumption: 1.2, brandRL: 'Porsche', titleRL: 'Panamera Turbo', brandLF: 'Pfister', titleLF: 'Paramena J2', title: 'Porsche Panamera Turbo', safeTitle: 'Pfister Paramena J2' },
    'e63s': { price: 5000000, tags: ["donate"], class: 'unique', capacity: 100000, fuelType: 'Premium', fuelCap: 68, fuelConsumption: 1.2, brandRL: 'Mercedes-Benz', titleRL: 'E-Class W213', brandLF: 'Benefactor', titleLF: 'E-Series M213', title: 'Mercedes-Benz E-Class W213', safeTitle: 'Benefactor E-Series M213' },
    'm5comp': { price: 5000000, tags: ["donate"], class: 'unique', capacity: 100000, fuelType: 'Premium', fuelCap: 72, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: 'M5 F90 Competition', brandLF: 'Ubermacht', titleLF: 'W5 H90 Contention', title: 'BMW M5 F90 Competition', safeTitle: 'Ubermacht W5 H90 Contention' },
    'ram': { price: 3000000, tags: ["donate"], class: 'unique', capacity: 180000, fuelType: 'Plus', fuelCap: 130, fuelConsumption: 1.9, brandRL: 'Dodge', titleRL: 'RAM 1500', brandLF: 'Bravado', titleLF: 'RUM 1500', title: 'Dodge RAM 1500', safeTitle: 'Bravado RUM 1500' },
    'c300': { price: 5000000, tags: ["donate"], class: 'unique', capacity: 95000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 1.1, brandRL: 'Mercedes-Benz', titleRL: 'C-Class w206', brandLF: 'Benefactor', titleLF: 'C-Series M206', title: 'Mercedes-Benz C-Class w206', safeTitle: 'Benefactor C-Series M206' },
    'amggt': { price: 4500000, tags: ["donate"], class: 'unique', capacity: 50000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1, brandRL: 'Mercedes-AMG', titleRL: 'GT R', brandLF: 'Benefactor', titleLF: '-ASG GS R', title: 'Mercedes-AMG GT R', safeTitle: 'Benefactor-ASG GS R' },
    'cls63s': { price: 4500000, tags: ["donate"], class: 'unique', capacity: 140000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.7, brandRL: 'Mercedes-Benz', titleRL: 'CLS C218', brandLF: 'Benefactor', titleLF: 'ZLS S218', title: 'Mercedes-Benz CLS C218', safeTitle: 'Benefactor ZLS S218' },
    'bentaygast': { price: 4500000, tags: ["donate"], class: 'unique', capacity: 170000, fuelType: 'Premium', fuelCap: 95, fuelConsumption: 1.3, brandRL: 'Bentley', titleRL: 'Bentayga', brandLF: 'Enus', titleLF: 'Benteygo', title: 'Bentley Bentayga', safeTitle: 'Enus Benteygo' },
    '718bs': { price: 3500000, tags: ["donate"], class: 'unique', capacity: 50000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 1.3, brandRL: 'Porsche', titleRL: '718 S Boxter', brandLF: 'Pfister', titleLF: '781 S Cabrio', title: 'Porsche 718 S Boxter', safeTitle: 'Pfister 781 S Cabrio' },
    'brutale': { price: 4500000, tags: ["moto","donate"], class: 'unique', capacity: 0, fuelType: 'Premium', fuelCap: 50, fuelConsumption: 0.6, brandRL: 'MV Agusta', titleRL: 'Brutale', brandLF: 'Pegassi', titleLF: 'Brutora', title: 'MV Agusta Brutale', safeTitle: 'Pegassi Brutora' },
    'velar': { price: 3500000, tags: ["donate"], class: 'unique', capacity: 140000, fuelType: 'Premium', fuelCap: 85, fuelConsumption: 1, brandRL: 'Range Rover', titleRL: 'Velar', brandLF: 'Gallivanter', titleLF: 'Velora', title: 'Range Rover Velar', safeTitle: 'Gallivanter Velora' },
    'v4sp': { price: 3500000, tags: ["moto","donate"], class: 'unique', capacity: 0, fuelType: 'Premium', fuelCap: 45, fuelConsumption: 0.5, brandRL: 'Ducati', titleRL: 'Panigale V4 Speciale', brandLF: 'Principe', titleLF: 'Ranigale V4 Speciale', title: 'Ducati Panigale V4 Speciale', safeTitle: 'Principe Ranigale V4 Speciale' },
    'msprinter': { price: 800000, tags: ["donate"], class: 'donate', capacity: 180000, fuelType: 'Diesel', fuelCap: 90, fuelConsumption: 1.3, brandRL: 'Mercedes-Benz', titleRL: 'Sprinter', brandLF: 'Benefactor', titleLF: 'Runner', title: 'Mercedes-Benz Sprinter', safeTitle: 'Benefactor Runner' },
    'nisgtr': { price: 3000000, tags: ["donate"], class: 'unique', capacity: 70000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1.6, brandRL: 'Nissan', titleRL: 'GT-R R35', brandLF: 'Annis', titleLF: 'GS-R M35', title: 'Nissan GT-R R35', safeTitle: 'Annis GS-R M35' },
    'dsprinter': { price: 700000, tags: ["donate"], class: 'donate', capacity: 180000, fuelType: 'Diesel', fuelCap: 90, fuelConsumption: 1.3, brandRL: 'Dodge', titleRL: 'Sprinter', brandLF: 'Bravado', titleLF: 'Runner', title: 'Dodge Sprinter', safeTitle: 'Bravado Runner' },
    'vclass': { price: 2500000, tags: ["donate"], class: 'unique', capacity: 160000, fuelType: 'Diesel', fuelCap: 90, fuelConsumption: 1.3, brandRL: 'Mercedes-Benz', titleRL: 'V-Class Vito', brandLF: 'Benefactor', titleLF: 'V-Series Veto', title: 'Mercedes-Benz V-Class Vito', safeTitle: 'Benefactor V-Series Veto' },
    'mjc': { price: 900000, tags: ["donate"], class: 'unique', capacity: 100000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 0.5, brandRL: 'MINI', titleRL: 'John Cooper JCW GP', brandLF: 'Weeny', titleLF: 'Copper', title: 'MINI John Cooper JCW GP', safeTitle: 'Weeny Copper' },
    'samara': { price: 700000, tags: ["donate"], class: 'unique', capacity: 75000, fuelType: 'Regular', fuelCap: 55, fuelConsumption: 1, brandRL: 'LADA', titleRL: 'Samara', brandLF: 'RUNE', titleLF: 'V-14', title: 'LADA Samara', safeTitle: 'RUNE V-14' },
    'priora': { price: 800000, tags: ["donate"], class: 'unique', capacity: 50000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.1, brandRL: 'LADA', titleRL: 'Priora', brandLF: 'RUNE', titleLF: 'Prior', title: 'LADA Priora', safeTitle: 'RUNE Prior' },
    'urban': { price: 800000, tags: ["donate"], class: 'unique', capacity: 135000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.1, brandRL: 'LADA', titleRL: 'Niva', brandLF: 'RUNE', titleLF: 'Niwa', title: 'LADA Niva', safeTitle: 'RUNE Niwa' },
    'vesta': { price: 800000, tags: ["donate"], class: 'unique', capacity: 50000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.3, brandRL: 'LADA', titleRL: 'Vesta', brandLF: 'RUNE', titleLF: 'Westa', title: 'LADA Vesta', safeTitle: 'RUNE Westa' },
    'matiz': { price: 700000, tags: ["donate"], class: 'unique', capacity: 60000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 0.8, brandRL: 'Dejawoo', titleRL: 'Matiz', brandLF: 'Weeny', titleLF: 'Maxiz', title: 'Dejawoo Matiz', safeTitle: 'Weeny Maxiz' },
    'vaz2107': { price: 700000, tags: ["donate"], class: 'unique', capacity: 70000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1.4, brandRL: 'VAZ', titleRL: '2107', brandLF: 'RUNE', titleLF: 'V-07', title: 'VAZ 2107', safeTitle: 'RUNE V-07' },
    'vaz2109': { price: 700000, tags: ["donate"], class: 'unique', capacity: 90000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1.7, brandRL: 'VAZ', titleRL: '2109', brandLF: 'RUNE', titleLF: 'V-09', title: 'VAZ 2109', safeTitle: 'RUNE V-09' },
    // 't680':                   { price: 900000 * 4,  tags: ['donate'], class: 'null',   capacity: 0,       fuelType: 'Diesel',  fuelCap: 900, fuelConsumption: 7.0, brandRL: 'Kenworth', titleRL: 'T680',                  brandLF: 'Brute', titleLF: 'T-1000'},            // 1 Ноября 2021
    // 'actros':                 { price: 1200000 * 4, tags: ['donate'], class: 'null',   capacity: 0,       fuelType: 'Diesel',  fuelCap: 800, fuelConsumption: 3.5, brandRL: 'Mercedes-Benz', titleRL: 'Actros',           brandLF: 'Benefactor', titleLF: 'Actor'},         // 1 Ноября 2021
    // Donate - конец

    // Эксклюзив - начало
    'urus58': { price: 3200000, class: 'null', capacity: 180000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.5, brandRL: 'Lamborghini', titleRL: 'Urus Kreed Edition', brandLF: 'Pegassi', titleLF: 'Ursus Kreed Edition', title: 'Lamborghini Urus Kreed Edition', safeTitle: 'Pegassi Ursus Kreed Edition' },
    'hummer2': { price: 6000000, class: 'unique', capacity: 180000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.7, brandRL: 'GMC', titleRL: 'Hummer EV 4x4', brandLF: 'Mammoth', titleLF: 'Hammer EV 4x4', title: 'GMC Hummer EV 4x4', safeTitle: 'Mammoth Hammer EV 4x4' },
    'wraithb': { price: 15200000, class: 'unique', capacity: 200000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.5, brandRL: 'Rolls-Royce', titleRL: 'Wraith', brandLF: 'Enus', titleLF: 'Veil', title: 'Rolls-Royce Wraith', safeTitle: 'Enus Veil' },
    'mustang4': { price: 3000000, class: 'donate', capacity: 0, fuelType: 'Premium', fuelCap: 82, fuelConsumption: 1.6, brandRL: 'Ford', titleRL: 'Mustang Hoonigan', brandLF: 'Vapid', titleLF: 'Steed HG', title: 'Ford Mustang Hoonigan', safeTitle: 'Vapid Steed HG' },
    // Эксклюзив - конец

    // Подписка - начало
    'galant': { price: 600000, class: 'donate', capacity: 60000, fuelType: 'Regular', fuelCap: 60, fuelConsumption: 1.4, brandRL: 'Mitsubishi', titleRL: 'Galant', brandLF: 'Maibatsu', titleLF: 'Shinichi', title: 'Mitsubishi Galant', safeTitle: 'Maibatsu Shinichi' },
    'f1503': { price: 800000, class: 'donate', capacity: 100000, fuelType: 'Diesel', fuelCap: 72, fuelConsumption: 2.2, brandRL: 'Ford', titleRL: 'F-150', brandLF: 'Vapid', titleLF: 'P-150', title: 'Ford F-150', safeTitle: 'Vapid P-150' },
    'p928': { price: 1000000, class: 'donate', capacity: 40000, fuelType: 'Plus', fuelCap: 86, fuelConsumption: 2, brandRL: 'Porsche', titleRL: '928', brandLF: 'Pfister', titleLF: 'Nine-28', title: 'Porsche 928', safeTitle: 'Pfister Nine-28' },
    'a42': { price: 1250000, class: 'donate', capacity: 80000, fuelType: 'Plus', fuelCap: 68, fuelConsumption: 1.5, brandRL: 'Audi', titleRL: '80 B4 Avant', brandLF: 'Obey', titleLF: 'V4 P4', title: 'Audi 80 B4 Avant', safeTitle: 'Obey V4 P4' },
    'defender2': { price: 1500000, class: 'donate', capacity: 110000, fuelType: 'Diesel', fuelCap: 78, fuelConsumption: 2.3, brandRL: 'Land Rover', titleRL: 'Defender', brandLF: 'Gallivanter', titleLF: 'Runaway', title: 'Land Rover Defender', safeTitle: 'Gallivanter Runaway' },
    'viper2': { price: 1750000, class: 'null', capacity: 40000, fuelType: 'Plus', fuelCap: 72, fuelConsumption: 2.1, brandRL: 'Secret', titleRL: 'Vehicle', brandLF: 'Secret', titleLF: 'Vehicle', title: 'Secret', safeTitle: 'Secret' },
    // Подписка - конец

    // Автомобильный кейс - начало
    'bolide': { price: 20000000, class: 'unique', capacity: 0, fuelType: 'Premium', fuelCap: 82, fuelConsumption: 1.8, brandRL: 'Bugatti', titleRL: 'Bolide', brandLF: 'Truffade', titleLF: 'Formula', title: 'Bugatti Bolide', safeTitle: 'Truffade Formula' },
    'essenza': { price: 20000000, class: 'unique', capacity: 0, fuelType: 'Premium', fuelCap: 84, fuelConsumption: 1.8, brandRL: 'Lamborghini', titleRL: 'Essenza SV12', brandLF: 'Pegassi', titleLF: 'Ezensa SV12', title: 'Lamborghini Essenza SV12', safeTitle: 'Pegassi Ezensa SV12' },
    'xclass': { price: 3400000, class: 'unique', capacity: 180000, fuelType: 'Premium', fuelCap: 126, fuelConsumption: 2.2, brandRL: 'Mercedes-Benz', titleRL: 'X-Class', brandLF: 'Benefactor', titleLF: 'X-Series', title: 'Mercedes-Benz X-Class', safeTitle: 'Benefactor X-Series' },
    'gclass4': { price: 10000000, class: 'unique', capacity: 170000, fuelType: 'Premium', fuelCap: 104, fuelConsumption: 1.8, brandRL: 'Mercedes-Benz', titleRL: 'G-Class W464 4x4', brandLF: 'Benefactor', titleLF: 'G-Series M464 4x4', title: 'Mercedes-Benz G-Class W464 4x4', safeTitle: 'Benefactor G-Series M464 4x4' },
    // Автомобильный кейс - конец

    // Осенний кейс 2023 - начало
    'diablo': { price: 7000000, class: 'unique', capacity: 60000, fuelType: 'Plus', fuelCap: 68, fuelConsumption: 1.8, brandRL: 'Lamborghini', titleRL: 'Diablo', brandLF: 'Pegassi', titleLF: 'Devil', title: 'Lamborghini Diablo', safeTitle: 'Pegassi Devil' },
    'hummer': { price: 4500000, class: 'unique', capacity: 160000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.5, brandRL: 'GMC', titleRL: 'Hummer EV', brandLF: 'Mammoth', titleLF: 'Hammer EV', title: 'GMC Hummer EV', safeTitle: 'Mammoth Hammer EV' },
    'missionr': { price: 20000000, class: 'unique', capacity: 20000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 3.5, brandRL: 'Porsche', titleRL: 'Mission R', brandLF: 'Pfister', titleLF: 'Messiah', title: 'Porsche Mission R', safeTitle: 'Pfister Messiah' },
    'xm': { price: 6500000, class: 'unique', capacity: 160000, fuelType: 'Premium', fuelCap: 112, fuelConsumption: 2, brandRL: 'BMW', titleRL: 'XM G09', brandLF: 'Ubermacht', titleLF: 'YW', title: 'BMW XM G09', safeTitle: 'Ubermacht YW' },
    // Осенний кейс 2023 - конец

    // Весенний кейс 2024 - начало
    'senna': { price: 20000000, class: 'unique', capacity: 20000, fuelType: 'Premium', fuelCap: 82, fuelConsumption: 2, brandRL: 'McLaren', titleRL: 'Senna', brandLF: 'Progen', titleLF: 'Ayrton', title: 'McLaren Senna', safeTitle: 'Progen Ayrton' },
    'emira': { price: 16000000, class: 'unique', capacity: 40000, fuelType: 'Premium', fuelCap: 78, fuelConsumption: 1.8, brandRL: 'Lotus', titleRL: 'Emira', brandLF: 'Ocelot', titleLF: 'Remir', title: 'Lotus Emira', safeTitle: 'Ocelot Remir' },
    'f1502': { price: 12000000, class: 'unique', capacity: 250000, fuelType: 'Premium', fuelCap: 130, fuelConsumption: 2.4, brandRL: 'Ford', titleRL: 'F-150 Raptor 6x6', brandLF: 'Vapid', titleLF: 'P-150 Predator 6X6', title: 'Ford F-150 Raptor 6x6', safeTitle: 'Vapid P-150 Predator 6X6' },
    'db11': { price: 8000000, class: 'unique', capacity: 50000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.7, brandRL: 'Aston Martin', titleRL: 'DB11', brandLF: 'Dewbauchee', titleLF: 'DP11', title: 'Aston Martin DB11', safeTitle: 'Dewbauchee DP11' },
    'brz': { price: 4000000, class: 'unique', capacity: 65000, fuelType: 'Plus', fuelCap: 65, fuelConsumption: 1.4, brandRL: 'Subaru', titleRL: 'BRZ', brandLF: 'Karin', titleLF: 'BRS-86', title: 'Subaru BRZ', safeTitle: 'Karin BRS-86' },
    'k5': { price: 2200000, class: 'unique', capacity: 90000, fuelType: 'Plus', fuelCap: 84, fuelConsumption: 1.3, brandRL: 'KIA', titleRL: 'K5', brandLF: 'Bollokan', titleLF: 'K-Five', title: 'KIA K5', safeTitle: 'Bollokan K-Five' },
    'eclipse': { price: 1200000, class: 'unique', capacity: 60000, fuelType: 'Regular', fuelCap: 67, fuelConsumption: 1.2, brandRL: 'Mitsubishi', titleRL: 'Eclipse D30', brandLF: 'Maibatsu', titleLF: 'Sunrise', title: 'Mitsubishi Eclipse D30', safeTitle: 'Maibatsu Sunrise' },
    'victoria': { price: 800000, class: 'unique', capacity: 70000, fuelType: 'Regular', fuelCap: 88, fuelConsumption: 1.4, brandRL: 'Ford', titleRL: 'Crown Victoria', brandLF: 'Vapid', titleLF: 'Coronet V', title: 'Ford Crown Victoria', safeTitle: 'Vapid Coronet V' },
    // Весенний кейс 2024 - конец

    // Зимний кейс 2024 - начало
    'q60s': { price: 2400000, class: 'unique', capacity: 80000, fuelType: 'Plus', fuelCap: 62, fuelConsumption: 1.4, brandRL: 'Infiniti', titleRL: 'Q60S', brandLF: 'Fathom', titleLF: 'Q60C', title: 'Infiniti Q60S', safeTitle: 'Fathom Q60C' },
    's2000': { price: 800000, class: 'unique', capacity: 65000, fuelType: 'Regular', fuelCap: 52, fuelConsumption: 1.2, brandRL: 'Honda', titleRL: 'S2000', brandLF: 'Dinka', titleLF: 'C-2000', title: 'Honda S2000', safeTitle: 'Dinka C-2000' },
    'carrera': { price: 1500000, class: 'unique', capacity: 70000, fuelType: 'Regular', fuelCap: 58, fuelConsumption: 1.8, brandRL: 'Porsche', titleRL: 'Carrera 911', brandLF: 'Pfister', titleLF: 'Carcio', title: 'Porsche Carrera 911', safeTitle: 'Pfister Carcio' },
    // Зимний кейс 2024 - конец

    // Весенний кейс 2025 - начало
    'idr': { price: 20000000, class: 'unique', capacity: 0, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 1.2, brandRL: 'Volkswagen', titleRL: 'ID R', brandLF: 'Burgerfahrzeug', titleLF: 'Norma', title: 'Volkswagen ID R', safeTitle: 'Burgerfahrzeug Norma' },
    'f8': { price: 15000000, class: 'unique', capacity: 60000, fuelType: 'Premium', fuelCap: 88, fuelConsumption: 2, brandRL: 'Ferrari', titleRL: 'F8 Tributo', brandLF: 'Grotti', titleLF: 'P8', title: 'Ferrari F8 Tributo', safeTitle: 'Grotti P8' },
    'a8': { price: 8000000, class: 'unique', capacity: 90000, fuelType: 'Premium', fuelCap: 82, fuelConsumption: 1.8, brandRL: 'Audi', titleRL: 'A8', brandLF: 'Obey', titleLF: 'V8', title: 'Audi A8', safeTitle: 'Obey V8' },
    'durango': { price: 1500000, class: 'unique', capacity: 110000, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 2, brandRL: 'Dodge', titleRL: 'Durango', brandLF: 'Bravado', titleLF: 'Myles', title: 'Dodge Durango', safeTitle: 'Bravado Myles' },
    'gnx': { price: 1100000, class: 'unique', capacity: 70000, fuelType: 'Regular', fuelCap: 86, fuelConsumption: 1.8, brandRL: 'Buick', titleRL: 'GNX', brandLF: 'Willard', titleLF: 'SNX', title: 'Buick GNX', safeTitle: 'Willard SNX' },
    'solaris': { price: 800000, class: 'unique', capacity: 60000, fuelType: 'Regular', fuelCap: 64, fuelConsumption: 1, brandRL: 'Hyundai', titleRL: 'Solaris', brandLF: 'Bollokan', titleLF: 'Lunar', title: 'Hyundai Solaris', safeTitle: 'Bollokan Lunar' },
    // Весенний кейс 2025 - конец

    // Осенний кейс 2024 - начало
    'noire': { price: 20000000, class: 'unique', capacity: 20000, fuelType: 'Premium', fuelCap: 100, fuelConsumption: 2.2, brandRL: 'Bugatti', titleRL: 'La Voiture Noire', brandLF: 'Truffade', titleLF: 'Blanche', title: 'Bugatti La Voiture Noire', safeTitle: 'Truffade Blanche' },
    'valour': { price: 14000000, class: 'unique', capacity: 50000, fuelType: 'Premium', fuelCap: 94, fuelConsumption: 2, brandRL: 'Aston Martin', titleRL: 'Valour', brandLF: 'Dewbauchee', titleLF: 'Malicious', title: 'Aston Martin Valour', safeTitle: 'Dewbauchee Malicious' },
    'batur': { price: 12000000, class: 'unique', capacity: 10000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.9, brandRL: 'Bentley', titleRL: 'Mulliner Batur', brandLF: 'Enus', titleLF: 'Bangli', title: 'Bentley Mulliner Batur', safeTitle: 'Enus Bangli' },
    'sls': { price: 7000000, class: 'unique', capacity: 60000, fuelType: 'Premium', fuelCap: 85, fuelConsumption: 1.8, brandRL: 'Mercedes-AMG', titleRL: 'SLS', brandLF: 'Benefactor', titleLF: '-ASG ZLS', title: 'Mercedes-AMG SLS', safeTitle: 'Benefactor-ASG ZLS' },
    'xts': { price: 6500000, class: 'unique', capacity: 80000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.5, brandRL: 'Cadillac', titleRL: 'XTS Royale', brandLF: 'Albany', titleLF: 'XDS Limo', title: 'Cadillac XTS Royale', safeTitle: 'Cavalcade XDS Limo' },
    'silverado': { price: 6000000, class: 'unique', capacity: 175000, fuelType: 'Premium', fuelCap: 91, fuelConsumption: 2, brandRL: 'Chevrolet', titleRL: 'Silverado T1XX', brandLF: 'Declasse', titleLF: 'Tylemo', title: 'Chevrolet Silverado T1XX', safeTitle: 'Declasse Tylemo' },
    'ct5': { price: 5000000, class: 'unique', capacity: 90000, fuelType: 'Premium', fuelCap: 66, fuelConsumption: 1.6, brandRL: 'Cadillac', titleRL: 'CT5', brandLF: 'Albany', titleLF: 'CD5', title: 'Cadillac CT5', safeTitle: 'Cavalcade CD5' },
    'defender': { price: 4500000, class: 'unique', capacity: 110000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.8, brandRL: 'Land Rover', titleRL: 'Defender', brandLF: 'Gallivanter', titleLF: 'Runaway', title: 'Land Rover Defender L633', safeTitle: 'Gallivanter Runaway 633L' },
    'eclass5': { price: 3000000, class: 'unique', capacity: 85000, fuelType: 'Plus', fuelCap: 59, fuelConsumption: 1.4, brandRL: 'Mercedes-Benz', titleRL: 'E-Class W212', brandLF: 'Benefactor', titleLF: 'E-Series M212', title: 'Mercedes-Benz E-Class W212', safeTitle: 'Benefactor E-Series M212' },
    '5series3': { price: 3000000, class: 'unique', capacity: 90000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.6, brandRL: 'BMW', titleRL: '5-series F10', brandLF: 'Ubermacht', titleLF: '5-Line P10', title: 'BMW 5-series F10', safeTitle: 'Ubermacht 5-Line P10' },
    'cclass2': { price: 2200000, class: 'unique', capacity: 95000, fuelType: 'Regular', fuelCap: 62, fuelConsumption: 1.3, brandRL: 'Mercedes-Benz', titleRL: 'C-Class W203', brandLF: 'Benefactor', titleLF: 'C-Series M203', title: 'Mercedes-Benz C-Class W203', safeTitle: 'Benefactor C-Series M203' },
    'a4': { price: 1400000, class: 'unique', capacity: 105000, fuelType: 'Regular', fuelCap: 62, fuelConsumption: 1.4, brandRL: 'Audi', titleRL: 'A4 B5 Avant', brandLF: 'Obey', titleLF: 'V4 P5', title: 'Audi A4 B5 Avant', safeTitle: 'Obey V4 P5' },
    'astra': { price: 1100000, class: 'unique', capacity: 50000, fuelType: 'Regular', fuelCap: 56, fuelConsumption: 1.2, brandRL: 'Opel', titleRL: 'Astra J GTC', brandLF: 'Bollokan', titleLF: 'Rose GT', title: 'Opel Astra J GTC', safeTitle: 'Bollokan Rose GT' },
    'rio2': { price: 800000, class: 'unique', capacity: 70000, fuelType: 'Regular', fuelCap: 43, fuelConsumption: 1.1, brandRL: 'KIA', titleRL: 'Rio QB', brandLF: 'Bollokan', titleLF: 'Rino QP', title: 'KIA Rio QB', safeTitle: 'Bollokan Rino QP' },
    // Осенний кейс 2023 - конец

    // Хеллуинский ивент 2023 - начало
    'db5': { price: 4000000, class: 'unique', capacity: 90000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 2, brandRL: 'Aston Martin', titleRL: 'DB5', brandLF: 'Dewbauchee', titleLF: 'DP5', title: 'Aston-Martin DB5', safeTitle: 'Dewbauchee DP5' },
    'charger3': { price: 6000000, class: 'unique', capacity: 110000, fuelType: 'Plus', fuelCap: 82, fuelConsumption: 3, brandRL: 'Dodge', titleRL: 'Charger FF', brandLF: 'Bravado', titleLF: 'Changer FF', title: 'Dodge Charger FF', safeTitle: 'Bravado Changer FF' },
    'modelt': { price: 3000000, class: 'unique', capacity: 110000, fuelType: 'Plus', fuelCap: 58, fuelConsumption: 1.5, brandRL: 'Ford', titleRL: 'Model T', brandLF: 'Vapid', titleLF: 'Line T', title: 'Ford Model T', safeTitle: 'Vapid Line T' },
    // Хеллуинский ивент 2023 - конец

    // Хеллуинский ивент 2024 - начало
    'regalia': { price: 13000000, class: 'unique', capacity: 120000, fuelType: 'Premium', fuelCap: 88, fuelConsumption: 2, brandRL: 'Quartz', titleRL: 'Regalia', brandLF: 'Quartz', titleLF: 'Regalia', title: 'Quartz Regalia', safeTitle: 'Quartz Regalia' },
    'turbor': { price: 8000000, class: 'unique', capacity: 60000, fuelType: 'Plus', fuelCap: 82, fuelConsumption: 2.6, brandRL: 'Quadra', titleRL: 'Turbo-R V-Tech', brandLF: 'Quadra', titleLF: 'Turbo-R V-Tech', title: 'Quadra Turbo-R V-Tech', safeTitle: 'Quadra Turbo-R V-Tech' },
    'm12b': { price: 5000000, class: 'unique', capacity: 100000, fuelType: 'Plus', fuelCap: 96, fuelConsumption: 2.4, brandRL: 'AMG', titleRL: 'M12B Warthog', brandLF: 'AMG', titleLF: 'M12B Warthog', title: 'AMG M12B Warthog', safeTitle: 'AMG M12B Warthog' },
    'firebird': { price: 2500000, class: 'unique', capacity: 50000, fuelType: 'Regular', fuelCap: 66, fuelConsumption: 2, brandRL: 'Pontiac', titleRL: 'Firebird KITT', brandLF: 'Imponte', titleLF: 'KITT', title: 'Pontiac Firebird KITT', safeTitle: 'Imponte KITT' },
    'wrangler': { price: 1000000, class: 'unique', capacity: 80000, fuelType: 'Regular', fuelCap: 58, fuelConsumption: 1.8, brandRL: 'Jeep', titleRL: 'Wrangler', brandLF: 'Canis', titleLF: 'JW', title: 'Jeep Wrangler', safeTitle: 'Canis JW' },
    // Хеллуинский ивент 2024 - конец

    // Extra - начало
    'amgone': { price: 20000000, discount: 15, class: 'extra', capacity: 5000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.5, brandRL: 'Mercedes-Benz', titleRL: 'Project One', brandLF: 'Benefactor', titleLF: 'ASG P-One', title: 'Mercedes-Benz Project One', safeTitle: 'Benefactor ASG P-One' },
    'imola': { price: 16000000, discount: 20, class: 'extra', capacity: 15000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Pagani', titleRL: 'Imola', brandLF: 'Pegassi', titleLF: 'Emola', title: 'Pagani Imola', safeTitle: 'Pegassi Emola' },
    '918s': { price: 15000000, discount: 5, class: 'extra', capacity: 30000, fuelType: 'Premium', fuelCap: 86, fuelConsumption: 1.4, brandRL: 'Porsche', titleRL: '918 Spyder', brandLF: 'Pfister', titleLF: '819 Spyder', title: 'Porsche 918 Spyder', safeTitle: 'Pfister 819 Spyder' },
    'jesko20': { price: 14000000, discount: 35, class: 'extra', capacity: 25000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Koenigsegg', titleRL: 'Jesko', brandLF: 'Overflod', titleLF: 'Entity Gesko', title: 'Koenigsegg Jesko', safeTitle: 'Entity Gesko' },
    '720s': { price: 13000000, discount: 20, class: 'extra', capacity: 30000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.4, brandRL: 'McLaren', titleRL: '720s', brandLF: 'Progen', titleLF: '720c', title: 'McLaren 720s', safeTitle: 'Progen 720c' },
    'rrphantom': { price: 12500000, discount: 20, class: 'extra', capacity: 80000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Rolls-Royce', titleRL: 'Phantom VIII', brandLF: 'Enus', titleLF: 'Phantasm VIII', title: 'Rolls-Royce Phantom VIII', safeTitle: 'Enus Phantasm VIII' },
    'countach': { price: 12000000, discount: 55, class: 'extra', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Lamborghini', titleRL: 'Countach', brandLF: 'Pegassi', titleLF: 'Coutron', title: 'Lamborghini Countach', safeTitle: 'Pegassi Coutron' },
    'ff12': { price: 10000000, discount: 50, class: 'extra', capacity: 35000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Ferrari', titleRL: 'F12 Berlinetta', brandLF: 'Grotti', titleLF: 'F21 Verleenetta', title: 'Ferrari F12 Berlinetta', safeTitle: 'Grotti F21 Verleenetta' },
    'veyron': { price: 9000000, discount: 20, class: 'extra', capacity: 45000, fuelType: 'Premium', fuelCap: 82, fuelConsumption: 1.5, brandRL: 'Bugatti', titleRL: 'Veyron', brandLF: 'Truffade', titleLF: 'Weyron', title: 'Bugatti Veyron', safeTitle: 'Truffade Weyron' },
    'gemera': { price: 12000000, discount: 10, class: 'extra', capacity: 40000, fuelType: 'Premium', fuelCap: 75, fuelConsumption: 1.4, brandRL: 'Koenigsegg', titleRL: 'Gemera', brandLF: 'Overflod', titleLF: 'Entity Genera', title: 'Koenigsegg Gemera', safeTitle: 'Entity Genera' },
    'q8': { price: 8500000, discount: 40, class: 'extra', capacity: 160000, fuelType: 'Premium', fuelCap: 98, fuelConsumption: 1.5, brandRL: 'Audi', titleRL: 'Q8', brandLF: 'Obey', titleLF: 'O8', title: 'Audi Q8', safeTitle: 'Obey O8' },
    '488pista': { price: 8000000, discount: 20, class: 'extra', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Ferrari', titleRL: '488 Pista', brandLF: 'Grotti', titleLF: '884 Pizta', title: 'Ferrari 488 Pista', safeTitle: 'Grotti 884 Pizta' },
    'sclass3': { price: 7500000, discount: 25, class: 'extra', capacity: 95000, fuelType: 'Premium', fuelCap: 82, fuelConsumption: 1.4, brandRL: 'Mercedes-Benz', titleRL: 'S-Class W223', brandLF: 'Benefactor', titleLF: 'S-Series M223', title: 'Mercedes-Benz S-Class W223', safeTitle: 'Benefactor S-Series M223' },
    'm7g70': { price: 7500000, discount: 35, class: 'extra', capacity: 110000, fuelType: 'Premium', fuelCap: 78, fuelConsumption: 1.8, brandRL: 'BMW', titleRL: '7-series G70', brandLF: 'Ubermacht', titleLF: '7-line J70', title: 'BMW 7-series G70', safeTitle: 'Ubermacht 7-line J70' },
    'reventon': { price: 7000000, discount: 35, class: 'extra', capacity: 35000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Lamborghini', titleRL: 'Reventon', brandLF: 'Pegassi', titleLF: 'Eventora', title: 'Lamborghini Reventon', safeTitle: 'Pegassi Eventora' },
    'vulcan': { price: 7000000, discount: 40, class: 'extra', capacity: 0, fuelType: 'Premium', fuelCap: 100, fuelConsumption: 1.5, brandRL: 'Aston Martin', titleRL: 'Vulcan', brandLF: 'Dewbauchee', titleLF: 'Vulcore', title: 'Aston-Martin Vulcan', safeTitle: 'Dewbauchee Vulcore' },
    'etron': { price: 7000000, discount: 55, class: 'extra', capacity: 95000, fuelType: 'Electro', fuelCap: 90, fuelConsumption: 1.4, brandRL: 'Audi', titleRL: 'e-tron GT', brandLF: 'Obey', titleLF: 'I-tron GS', title: 'Audi e-tron GT', safeTitle: 'Obey I-tron GS' },
    'taycan': { price: 7000000, discount: 20, class: 'extra', capacity: 80000, fuelType: 'Electro', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Porsche', titleRL: 'Taycan', brandLF: 'Pfister', titleLF: 'Teycan', title: 'Porsche Taycan', safeTitle: 'Pfister Teycan' },
    'x6m2': { price: 6500000, discount: 20, class: 'extra', capacity: 100000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.5, brandRL: 'BMW', titleRL: 'X6-Series G06', brandLF: 'Ubermacht', titleLF: 'X6-Line J06', title: 'BMW X6-Series G06', safeTitle: 'Ubermacht X6-Line J06' },
    'models': { price: 6500000, discount: 70, class: 'extra', capacity: 85000, fuelType: 'Electro', fuelCap: 80, fuelConsumption: 1.2, brandRL: 'Tesla', titleRL: 'Model S', brandLF: 'Coil', titleLF: 'Line S', title: 'Tesla Model S', safeTitle: 'Coil Line S' },
    '5series': { price: 6500000, discount: 50, class: 'extra', capacity: 80000, fuelType: 'Premium', fuelCap: 92, fuelConsumption: 1.7, brandRL: 'BMW', titleRL: '5-series G60', brandLF: 'Ubermacht', titleLF: '5-line J60', title: 'BMW 5-series G60', safeTitle: 'Ubermacht 5-line J60' },
    'eclass3': { price: 6500000, discount: 50, class: 'extra', capacity: 90000, fuelType: 'Premium', fuelCap: 84, fuelConsumption: 1.5, brandRL: 'Mercedes-Benz', titleRL: 'E-Class W214', brandLF: 'Benefactor', titleLF: 'E-Series M214', title: 'Mercedes-Benz E-Class W214', safeTitle: 'Benefactor E-Series M214' },
    'gt63s': { price: 6000000, discount: 20, class: 'extra', capacity: 80000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Mercedes-Benz', titleRL: 'AMG GT 63s', brandLF: 'Benefactor', titleLF: 'ASG GS 4-door', title: 'Mercedes-Benz AMG GT 63s', safeTitle: 'Benefactor ASG GS 4-door' },
    'f150': { price: 6000000, discount: 30, class: 'extra', capacity: 175000, fuelType: 'Plus', fuelCap: 120, fuelConsumption: 2, brandRL: 'Ford', titleRL: 'F-150 Raptor', brandLF: 'Vapid', titleLF: 'P-150 Predator', title: 'Ford F-150 Raptor', safeTitle: 'Vapid P-150 Predator' },
    'rs72': { price: 5750000, discount: 25, class: 'extra', capacity: 85000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Audi', titleRL: 'RS7 II', brandLF: 'Obey', titleLF: 'PS7 S8', title: 'Audi RS7 II', safeTitle: 'Obey PS7 S8' },
    '63gls': { price: 5500000, discount: 50, class: 'extra', capacity: 120000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Mercedes-Benz', titleRL: 'GLS X166', brandLF: 'Benefactor', titleLF: 'JLS Y166', title: 'Mercedes-Benz GLS X166', safeTitle: 'Benefactor JLS Y166' },
    'cayen19': { price: 5500000, discount: 30, class: 'extra', capacity: 100000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Porsche', titleRL: 'Cayenne', brandLF: 'Pfister', titleLF: 'Rayenne', title: 'Porsche Cayenne', safeTitle: 'Pfister Rayenne' },
    'bmwx7': { price: 5500000, discount: 40, class: 'extra', capacity: 120000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: 'X7', brandLF: 'Ubermacht', titleLF: 'Y7', title: 'BMW X7', safeTitle: 'Ubermacht Y7' },
    'viper': { price: 5000000, discount: 70, class: 'extra', capacity: 45000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.7, brandRL: 'Dodge', titleRL: 'Viper SRT', brandLF: 'Bravado', titleLF: 'Wiper SPT', title: 'Dodge Viper VX', safeTitle: 'Bravado Snake XV' },
    's63w222': { price: 5000000, discount: 20, class: 'extra', capacity: 90000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Mercedes-Benz', titleRL: 'S-Class W222', brandLF: 'Benefactor', titleLF: 'S-Series M222', title: 'Mercedes-Benz S-Class W222', safeTitle: 'Benefactor S-Series M222' },
    'bmwm7': { price: 5000000, discount: 70, class: 'extra', capacity: 80000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: '7-series M760Li', brandLF: 'Ubermacht', titleLF: 'N760Li', title: 'BMW 7-series M760Li', safeTitle: 'Ubermacht N760Li' },
    'xes': { price: 4750000, discount: 70, class: 'extra', capacity: 85000, fuelType: 'Plus', fuelCap: 82, fuelConsumption: 1.5, brandRL: 'Jaguar', titleRL: 'XE-S', brandLF: 'Ocelot', titleLF: 'EX-S', title: 'Jaguar XE-S', safeTitle: 'Ocelot EX-S' },
    'gle63': { price: 4500000, discount: 40, class: 'extra', capacity: 100000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Mercedes-Benz', titleRL: 'GLE C292', brandLF: 'Benefactor', titleLF: 'JLE S292', title: 'Mercedes-Benz GLE C292', safeTitle: 'Benefactor JLE S292' },
    'ftype': { price: 4500000, discount: 70, class: 'extra', capacity: 30000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Jaguar', titleRL: 'F-Type R Coupe', brandLF: 'Ocelot', titleLF: 'F-Model R Coupe', title: 'Jaguar F-Type R Coupe', safeTitle: 'Ocelot F-Model R Coupe' },
    'x6m': { price: 4500000, discount: 65, class: 'extra', capacity: 100000, fuelType: 'Premium', fuelCap: 100, fuelConsumption: 1.5, brandRL: 'BMW', titleRL: 'X6 M F86', brandLF: 'Ubermacht', titleLF: 'Y6 W H86', title: 'BMW X6 M F86', safeTitle: 'Ubermacht Y6 W H86' },
    'r820': { price: 4000000, discount: 50, class: 'extra', capacity: 25000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Audi', titleRL: 'R8', brandLF: 'Obey', titleLF: 'P8', title: 'Audi R8', safeTitle: 'Obey P8' },
    'm850': { price: 4000000, discount: 20, class: 'extra', capacity: 70000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: '8-series', brandLF: 'Ubermacht', titleLF: '8-line', title: 'BMW 8-series', safeTitle: 'Ubermacht 8-line' },
    'nsx': { price: 3750000, discount: 50, class: 'extra', capacity: 25000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Honda', titleRL: 'NSX NC1', brandLF: 'Dinka', titleLF: 'NSY NS1', title: 'Honda NSX NC1', safeTitle: 'Dinka NSY NS1' },
    'm4comp': { price: 3750000, discount: 15, class: 'extra', capacity: 75000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'BMW', titleRL: 'M4 G82 Competition', brandLF: 'Ubermacht', titleLF: 'W4 J82 Contention', title: 'BMW M4 G82 Competition', safeTitle: 'Ubermacht W4 J82 Contention' },
    'rs5': { price: 3750000, discount: 60, class: 'extra', capacity: 80000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 1.2, brandRL: 'Audi', titleRL: 'RS5', brandLF: 'Obey', titleLF: 'PS5', title: 'Audi RS5', safeTitle: 'Obey PS5' },
    'jgc': { price: 3750000, discount: 60, class: 'extra', capacity: 125000, fuelType: 'Premium', fuelCap: 67, fuelConsumption: 1.3, brandRL: 'Jeep', titleRL: 'Grand Cherokee', brandLF: 'Canis', titleLF: 'Great Cherokee', title: 'Jeep Grand Cherokee', safeTitle: 'Canis Great Cherokee' },
    'z4b': { price: 3500000, discount: 35, class: 'extra', capacity: 40000, fuelType: 'Premium', fuelCap: 60, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: 'Z4 M40i', brandLF: 'Ubermacht', titleLF: 'S4 N40i', title: 'BMW Z4 M40i', safeTitle: 'Ubermacht S4 N40i' },
    'tt': { price: 3500000, discount: 35, class: 'extra', capacity: 50000, fuelType: 'Premium', fuelCap: 55, fuelConsumption: 1.1, brandRL: 'Audi', titleRL: 'TT S8', brandLF: 'Obey', titleLF: 'DD', title: 'Audi TT S8', safeTitle: 'Obey DD' },
    'gle2': { price: 3500000, discount: 35, class: 'extra', capacity: 130000, fuelType: 'Premium', fuelCap: 110, fuelConsumption: 2, brandRL: 'Mercedes-Benz', titleRL: 'GLE V167', brandLF: 'Benefactor', titleLF: 'JLE B167', title: 'Mercedes-Benz GLE V167', safeTitle: 'Benefactor JLE B167' },
    'x5g05': { price: 3500000, discount: 35, class: 'extra', capacity: 120000, fuelType: 'Premium', fuelCap: 100, fuelConsumption: 1.5, brandRL: 'BMW', titleRL: 'X5 G05', brandLF: 'Ubermacht', titleLF: 'Y5 J05', title: 'BMW X5 G05', safeTitle: 'Ubermacht Y5 J05' },
    'macan': { price: 3500000, discount: 60, class: 'extra', capacity: 115000, fuelType: 'Premium', fuelCap: 110, fuelConsumption: 1.6, brandRL: 'Porsche', titleRL: 'Macan', brandLF: 'Pfister', titleLF: 'Mauer', title: 'Porsche Macan', safeTitle: 'Pfister Mauer' },
    'rs6': { price: 3500000, discount: 20, class: 'extra', capacity: 90000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Audi', titleRL: 'RS6', brandLF: 'Obey', titleLF: 'PS6', title: 'Audi RS6', safeTitle: 'Obey PS6' },
    'lc300': { price: 3500000, discount: 50, class: 'extra', capacity: 95000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Toyota', titleRL: 'Land Cruiser 300', brandLF: 'Karin', titleLF: 'Grand Cruiser 300', title: 'Toyota Land Cruiser 300', safeTitle: 'Karin Grand Cruiser 300' },
    'g55': { price: 4000000, discount: 15, class: 'extra', capacity: 120000, fuelType: 'Plus', fuelCap: 100, fuelConsumption: 1.5, brandRL: 'Mercedes-Benz', titleRL: 'G-Class W463', brandLF: 'Benefactor', titleLF: 'G-series M463', title: 'Mercedes-Benz G-Class W463', safeTitle: 'Benefactor G-series M463' },
    'i8': { price: 3000000, discount: 5, class: 'extra', capacity: 40000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: 'i8', brandLF: 'Ubermacht', titleLF: 'E8', title: 'BMW i8', safeTitle: 'Ubermacht E8' },
    'corvett08': { price: 3000000, discount: 40, class: 'extra', capacity: 60000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Chevrolet', titleRL: 'Corvette C7', brandLF: 'Declasse', titleLF: 'Covetto S7', title: 'Chevrolet Corvette C7', safeTitle: 'Declasse Covetto S7' },
    'ast': { price: 2750000, discount: 50, class: 'extra', capacity: 40000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Aston Martin', titleRL: 'Vanquish', brandLF: 'Dewbauchee', titleLF: 'Wanquish', title: 'Aston-Martin Vanquish', safeTitle: 'Dewbauchee Wanquish' },
    'skyline': { price: 2500000, discount: 25, class: 'extra', capacity: 60000, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Nissan', titleRL: 'Skyline GT-R R34', brandLF: 'Annis', titleLF: 'GS-R M34', title: 'Nissan Skyline GT-R R34', safeTitle: 'Annis GS-R M34' },
    'modelx': { price: 2500000, discount: 30, class: 'extra', capacity: 85000, fuelType: 'Electro', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Tesla', titleRL: 'Model X', brandLF: 'Coil', titleLF: 'Line X', title: 'Tesla Model X', safeTitle: 'Coil Line X' },
    'cesc21': { price: 2500000, discount: 50, class: 'extra', capacity: 120000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Cadillac', titleRL: 'Escalade', brandLF: 'Albany', titleLF: 'Excalibur', title: 'Cadillac Escalade', safeTitle: 'Cavalcade Excalibur' },
    'gsx1000': { price: 2500000, discount: 10, tags: ["moto"], class: 'extra', capacity: 0, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Suzuki', titleRL: 'GSX-R 1000', brandLF: 'Shitzu', titleLF: 'CSX-R 1000', title: 'Suzuki GSX-R 1000', safeTitle: 'Shitzu CSX-R 1000' },
    'charger20': { price: 2500000, discount: 50, class: 'extra', capacity: 80000, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1.5, brandRL: 'Dodge', titleRL: 'Charger SRT', brandLF: 'Bravado', titleLF: 'Changer CRT', title: 'Dodge Charger SRT', safeTitle: 'Bravado Changer CRT' },
    'fx50s': { price: 2300000, discount: 70, class: 'extra', capacity: 75000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Infiniti', titleRL: 'FX50 S', brandLF: 'Fathom', titleLF: 'XF50 S', title: 'Infiniti FX50 S', safeTitle: 'Fathom XF50 S' },
    'lex570': { price: 2300000, discount: 25, class: 'extra', capacity: 105000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Lexus', titleRL: 'LX 570', brandLF: 'Emperor', titleLF: 'MX 570', title: 'Lexus LX 570', safeTitle: 'Emperor MX 570' },
    'rrab': { price: 2300000, discount: 35, class: 'extra', capacity: 120000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Range Rover', titleRL: 'Autobiography L405', brandLF: 'Gallivanter', titleLF: 'AB L405', title: 'Range Rover Autobiography L405', safeTitle: 'Gallivanter AB L405' },
    'evo10': { price: 2200000, discount: 35, class: 'extra', capacity: 50000, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Mitsubishi', titleRL: 'Lancer Evolution X', brandLF: 'Maibatsu', titleLF: 'Lance Revolution X', title: 'Mitsubishi Lancer Evolution X', safeTitle: 'Maibatsu Lance Revolution X' },
    'rs7': { price: 2200000, discount: 20, class: 'extra', capacity: 85000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.3, brandRL: 'Audi', titleRL: 'RS7', brandLF: 'Obey', titleLF: 'PS7', title: 'Audi RS7', safeTitle: 'Obey PS7' },
    'x5me70': { price: 2000000, discount: 40, class: 'extra', capacity: 100000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'BMW', titleRL: 'X5 M E70', brandLF: 'Ubermacht', titleLF: 'Y5 W A70', title: 'BMW X5 M E70', safeTitle: 'Ubermacht Y5 W A70' },
    'lc200': { price: 2000000, discount: 15, class: 'extra', capacity: 110000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Toyota', titleRL: 'Land Cruiser 200', brandLF: 'Karin', titleLF: 'Grand Cruiser 200', title: 'Toyota Land Cruiser 200', safeTitle: 'Karin Grand Cruiser 200' },
    'camaro21': { price: 1800000, discount: 35, class: 'extra', capacity: 80000, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 1.6, brandRL: 'Chevrolet', titleRL: 'Camaro 1LT', brandLF: 'Declasse', titleLF: 'Gamaro 1TL', title: 'Chevrolet Camaro 1LT', safeTitle: 'Declasse Gamaro 1TL' },
    '2020mustang': { price: 1800000, discount: 20, class: 'extra', capacity: 75000, fuelType: 'Plus', fuelCap: 85, fuelConsumption: 1.7, brandRL: 'Ford', titleRL: 'Mustang S550', brandLF: 'Vapid', titleLF: 'Steed C550', title: 'Ford Mustang S550', safeTitle: 'Vapid Steed C550' },
    'bmwg20': { price: 1800000, discount: 5, class: 'extra', capacity: 70000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'BMW', titleRL: '3-series G20', brandLF: 'Ubermacht', titleLF: '3-line J20', title: 'BMW 3-series G20', safeTitle: 'Ubermacht 3-line J20' },
    'kiastinger': { price: 1800000, discount: 60, class: 'extra', capacity: 80000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'KIA', titleRL: 'Stinger', brandLF: 'Bollokan', titleLF: 'Sprinter', title: 'KIA Stinger', safeTitle: 'Bollokan Sprinter' },
    'm5e60': { price: 1650000, discount: 20, class: 'extra', capacity: 80000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'BMW', titleRL: 'M5 E60', brandLF: 'Ubermacht', titleLF: 'W5 A60', title: 'BMW M5 E60', safeTitle: 'Ubermacht W5 A60' },
    'evo9': { price: 1600000, discount: 30, class: 'extra', capacity: 60000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Mitsubishi', titleRL: 'Lancer Evolution IX', brandLF: 'Maibatsu', titleLF: 'Lance Revolution IX', title: 'Mitsubishi Lancer Evolution IX', safeTitle: 'Maibatsu Lance Revolution IX' },
    'touareg': { price: 1500000, discount: 50, class: 'extra', capacity: 95000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Volkswagen', titleRL: 'Touareg', brandLF: 'BF', titleLF: 'Twareg', title: 'Volkswagen Touareg', safeTitle: 'BF Twareg' },
    'tahoe2': { price: 1500000, discount: 35, class: 'extra', capacity: 90000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Chevrolet', titleRL: 'Tahoe', brandLF: 'Declasse', titleLF: 'Takho', title: 'Chevrolet Tahoe', safeTitle: 'Declasse Takho' },
    'supragr': { price: 1500000, discount: 20, class: 'extra', capacity: 50000, fuelType: 'Premium', fuelCap: 65, fuelConsumption: 1.5, brandRL: 'Toyota', titleRL: 'GR Supra', brandLF: 'Karin', titleLF: 'GS Superia', title: 'Toyota GR Supra', safeTitle: 'Karin GS Superia' },
    'rx7': { price: 1475000, discount: 20, class: 'extra', capacity: 40000, fuelType: 'Plus', fuelCap: 75, fuelConsumption: 1.3, brandRL: 'Mazda', titleRL: 'RX-7', brandLF: 'Annis', titleLF: 'RZ-7', title: 'Mazda RX-7', safeTitle: 'Annis RZ-7' },
    '370z': { price: 1450000, discount: 60, class: 'extra', capacity: 55000, fuelType: 'Plus', fuelCap: 75, fuelConsumption: 1.3, brandRL: 'Nissan', titleRL: '370Z', brandLF: 'Annis', titleLF: '370X', title: 'Nissan 370Z', safeTitle: 'Annis 370X' },
    'a80': { price: 1425000, discount: 25, class: 'extra', capacity: 45000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.4, brandRL: 'Toyota', titleRL: 'Supra', brandLF: 'Karin', titleLF: 'Superia', title: 'Toyota Supra', safeTitle: 'Karin Superia' },
    's15': { price: 1400000, discount: 20, class: 'extra', capacity: 50000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.4, brandRL: 'Nissan', titleRL: 'Silvia S15', brandLF: 'Annis', titleLF: 'Slivia', title: 'Nissan Silvia S15', safeTitle: 'Annis Slivia' },
    'mark2': { price: 1300000, discount: 20, class: 'extra', capacity: 65000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.5, brandRL: 'Toyota', titleRL: 'Mark II 100', brandLF: 'Karin', titleLF: 'Marco', title: 'Toyota Mark II 100', safeTitle: 'Karin Marco' },
    'camry70': { price: 1200000, discount: 20, class: 'extra', capacity: 65000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Toyota', titleRL: 'Camry XV70', brandLF: 'Karin', titleLF: 'Camria XW70', title: 'Toyota Camry XV70', safeTitle: 'Karin Camria XW70' },
    'accord': { price: 1200000, discount: 70, class: 'extra', capacity: 65000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Honda', titleRL: 'Accord', brandLF: 'Dinka', titleLF: 'Paccord', title: 'Honda Accord', safeTitle: 'Dinka Paccord' },
    'aclass': { price: 1200000, discount: 15, class: 'extra', capacity: 70000, fuelType: 'Plus', fuelCap: 58, fuelConsumption: 1.1, brandRL: 'Mercedes-Benz', titleRL: 'A-Class V177', brandLF: 'Benefactor', titleLF: 'A-Series P177', title: 'Mercedes-Benz A-Class V177', safeTitle: 'Benefactor A-Series P177' },
    '16challenger': { price: 1200000, discount: 20, class: 'extra', capacity: 70000, fuelType: 'Plus', fuelCap: 90, fuelConsumption: 1.8, brandRL: 'Dodge', titleRL: 'Challenger', brandLF: 'Bravado', titleLF: 'Сontender', title: 'Dodge Challenger', safeTitle: 'Bravado Сontender' },
    'mustang2': { price: 1200000, discount: 35, class: 'extra', capacity: 50000, fuelType: 'Regular', fuelCap: 80, fuelConsumption: 2, brandRL: 'Ford', titleRL: 'Mustang', brandLF: 'Vapid', titleLF: 'Steed', title: 'Ford Mustang', safeTitle: 'Vapid Steed' },
    'camaro2': { price: 1150000, discount: 50, class: 'extra', capacity: 60000, fuelType: 'Regular', fuelCap: 74, fuelConsumption: 1.8, brandRL: 'Chevrolet', titleRL: 'Camaro', brandLF: 'Declasse', titleLF: 'Gamaro', title: 'Chevrolet Camaro', safeTitle: 'Declasse Gamaro' },
    'subwrx': { price: 1000000, discount: 20, class: 'extra', capacity: 45000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Subaru', titleRL: 'Impreza WRX', brandLF: 'Karin', titleLF: 'Imperia MRX', title: 'Subaru Impreza WRX', safeTitle: 'Karin Imperia MRX' },
    'impala': { price: 900000, discount: 25, class: 'extra', capacity: 75000, fuelType: 'Regular', fuelCap: 80, fuelConsumption: 2, brandRL: 'Chevrolet', titleRL: 'Impala', brandLF: 'Declasse', titleLF: 'Implata SN', title: 'Chevrolet Impala', safeTitle: 'Declasse Implata SN' },
    'touareg2': { price: 900000, discount: 35, class: 'extra', capacity: 90000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Volkswagen', titleRL: 'Touareg R50', brandLF: 'BF', titleLF: 'Twareg S50', title: 'Volkswagen Touareg R50', safeTitle: 'BF Twareg S50' },
    'focusrs': { price: 800000, discount: 55, class: 'extra', capacity: 60000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Ford', titleRL: 'Focus RS', brandLF: 'Vapid', titleLF: 'Fox GS', title: 'Ford Focus RS', safeTitle: 'Vapid Fox GS' },
    'golf7r': { price: 800000, discount: 30, class: 'extra', capacity: 60000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.3, brandRL: 'Volkswagen', titleRL: 'Golf 7R', brandLF: 'BF', titleLF: 'Wolf 7S', title: 'Volkswagen Golf 7R', safeTitle: 'BF Wolf 7S' },
    'w210': { price: 800000, discount: 20, class: 'extra', capacity: 65000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.3, brandRL: 'Mercedes-Benz', titleRL: 'E-Class W210', brandLF: 'Benefactor', titleLF: 'E-Series M210', title: 'Mercedes-Benz E-Class W210', safeTitle: 'Benefactor E-Series M210' },
    'bmwe39': { price: 800000, discount: 5, class: 'extra', capacity: 65000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: '5-series E39', brandLF: 'Ubermacht', titleLF: '5-line A39', title: 'BMW 5-series E39', safeTitle: 'Ubermacht 5-line A39' },
    'camry2': { price: 700000, discount: 10, class: 'extra', capacity: 75000, fuelType: 'Regular', fuelCap: 70, fuelConsumption: 1.4, brandRL: 'Toyota', titleRL: 'Camry XV55', brandLF: 'Karin', titleLF: 'Camria XW55', title: 'Toyota Camry XV55', safeTitle: 'Karin Camria XW55' },
    's600': { price: 650000, discount: 10, class: 'extra', capacity: 65000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.3, brandRL: 'Mercedes-Benz', titleRL: 'S-Class W140', brandLF: 'Benefactor', titleLF: 'S-Series M140', title: 'Mercedes-Benz S-Class W140', safeTitle: 'Benefactor S-Series M140' },
    'bmwe38': { price: 650000, discount: 20, class: 'extra', capacity: 60000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: '7-series E38', brandLF: 'Ubermacht', titleLF: '7-line A38', title: 'BMW 7-series E38', safeTitle: 'Ubermacht 7-line A38' },
    'octavia18': { price: 650000, discount: 20, class: 'extra', capacity: 70000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.2, brandRL: 'Skoda', titleRL: 'Octavia RS', brandLF: 'BF', titleLF: 'Olivia GS', title: 'Skoda Octavia RS', safeTitle: 'BF Olivia GS' },
    'rio': { price: 500000, discount: 15, class: 'extra', capacity: 65000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.3, brandRL: 'KIA', titleRL: 'Rio FB', brandLF: 'Bollokan', titleLF: 'Rino FP', title: 'KIA Rio FB', safeTitle: 'Bollokan Rino FP' },
    'ae86': { price: 450000, discount: 5, class: 'extra', capacity: 40000, fuelType: 'Regular', fuelCap: 65, fuelConsumption: 1.3, brandRL: 'Toyota', titleRL: 'Corolla Levin AE86', brandLF: 'Karin', titleLF: 'Korona Lewin', title: 'Toyota Corolla Levin AE86', safeTitle: 'Karin Korona Lewin' },
    // Extra - конец

    // SeasonPass 1 - начало
    'veneno': { price: 20000000, class: 'unique', seasonPass: 1, capacity: 30000, fuelType: 'Premium', fuelCap: 100, fuelConsumption: 1.5, brandRL: 'Lamborghini', titleRL: 'Veneno', brandLF: 'Pegassi', titleLF: 'Verano', title: 'Lamborghini Veneno', safeTitle: 'Pegassi Verano' },
    'fordgt': { price: 10000000, class: 'unique', seasonPass: 1, capacity: 40000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.4, brandRL: 'Ford', titleRL: 'GT', brandLF: 'Vapid', titleLF: 'GS', title: 'Ford GT', safeTitle: 'Vapid GS' },
    'dawn': { price: 12000000, class: 'unique', seasonPass: 1, capacity: 140000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.3, brandRL: 'Rolls-Royce', titleRL: 'Dawn', brandLF: 'Enus', titleLF: 'Veil Cabrio', title: 'Rolls-Royce Dawn', safeTitle: 'Enus Veil Cabrio' },
    'f40': { price: 6000000, class: 'unique', seasonPass: 1, capacity: 80000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.6, brandRL: 'Ferrari', titleRL: 'F40', brandLF: 'Grotti', titleLF: 'P40', title: 'Ferrari F40', safeTitle: 'Grotti P40' },
    'sf90': { price: 14000000, class: 'unique', seasonPass: 1, capacity: 60000, fuelType: 'Premium', fuelCap: 85, fuelConsumption: 1.4, brandRL: 'Ferrari', titleRL: 'SF90', brandLF: 'Grotti', titleLF: 'SP90', title: 'Ferrari SF90', safeTitle: 'Grotti SP90' },
    'centenario': { price: 16000000, class: 'unique', seasonPass: 1, capacity: 60000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.5, brandRL: 'Lamborghini', titleRL: 'Centenario', brandLF: 'Pegassi', titleLF: 'Centerio', title: 'Lamborghini Centenario', safeTitle: 'Pegassi Centerio' },
    // SeasonPass 1 - конец

    // SeasonPass 2 - начало
    'ec135': { price: 20000000, tags: ["heli"], class: 'helidonate', seasonPass: 2, capacity: 1500000, fuelType: 'Premium', fuelCap: 1000, fuelConsumption: 5, brandRL: 'Eurocopter', titleRL: 'EC135 Gendarmerie', brandLF: 'Buckingham', titleLF: 'EK135', title: 'Eurocopter EC135 Gendarmerie', safeTitle: 'Buckingham EK135' },
    'l650': { price: 20000000, tags: ["boat"], class: 'boatdonate', seasonPass: 2, capacity: 2000000, fuelType: 'Diesel', fuelCap: 600, fuelConsumption: 1.6, brandRL: 'Sea Ray', titleRL: 'L650 Fly', brandLF: 'Buckingham', titleLF: 'M650', title: 'Sea Ray L650 Fly', safeTitle: 'Buckingham M650' },
    'aeroboat': { price: 20000000, tags: ["boat"], class: 'boatdonate', seasonPass: 2, capacity: 400000, fuelType: 'Premium', fuelCap: 200, fuelConsumption: 1.1, brandRL: 'Rolls-Royce', titleRL: 'Aeroboat V12', brandLF: 'Enus', titleLF: 'Airboat V12', title: 'Rolls-Royce Aeroboat V12', safeTitle: 'Enus Airboat V12' },
    'terzo': { price: 20000000, class: 'unique', seasonPass: 2, capacity: 10000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2, brandRL: 'Lamborghini', titleRL: 'Terzo Millennio', brandLF: 'Pegassi', titleLF: 'Tenzo', title: 'Lamborghini Terzo Millennio', safeTitle: 'Pegassi Tenzo' },
    'fxxk': { price: 16000000, class: 'unique', seasonPass: 2, capacity: 15000, fuelType: 'Premium', fuelCap: 84, fuelConsumption: 1.4, brandRL: 'Ferrari', titleRL: 'FXX-K', brandLF: 'Grotti', titleLF: 'FSS-K', title: 'Ferrari FXX-K', safeTitle: 'Grotti PXX-K' },
    'bacalar': { price: 12000000, class: 'unique', seasonPass: 2, capacity: 60000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.5, brandRL: 'Bentley', titleRL: 'Mulliner Bacalar', brandLF: 'Enus', titleLF: 'Blanco', title: 'Bentley Mulliner Bacalar', safeTitle: 'Enus Blanco' },
    'cayenne2': { price: 10000000, class: 'unique', seasonPass: 2, capacity: 150000, fuelType: 'Premium', fuelCap: 96, fuelConsumption: 1.5, brandRL: 'Porsche', titleRL: 'Cayenne GT', brandLF: 'Pfister', titleLF: 'Rayenne GS', title: 'Porsche Cayenne GT', safeTitle: 'Pfister Rayenne GS' },
    'gtr50': { price: 8000000, class: 'unique', seasonPass: 2, capacity: 110000, fuelType: 'Premium', fuelCap: 74, fuelConsumption: 1.4, brandRL: 'Nissan', titleRL: 'GT-R50', brandLF: 'Annis', titleLF: 'GS-R50', title: 'Nissan GT-R50', safeTitle: 'Annis GS-R50' },
    'delorean': { price: 6000000, class: 'unique', seasonPass: 2, capacity: 90000, fuelType: 'Plus', fuelCap: 74, fuelConsumption: 1.4, brandLF: 'Imponte', titleLF: 'DMS-12', title: 'Imponte DMC-12', safeTitle: 'Imponte DMS-12' },
    'z800': { price: 5000000, tags: ["moto"], class: 'unique', seasonPass: 2, capacity: 0, fuelType: 'Plus', fuelCap: 51, fuelConsumption: 1.5, brandLF: 'Nagasaki', titleLF: 'S800', title: 'Nagasaki Z800', safeTitle: 'Nagasaki S800' },
    'm4f82': { price: 4000000, class: 'unique', seasonPass: 2, capacity: 90000, fuelType: 'Plus', fuelCap: 68, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: 'M4 F82', brandLF: 'Ubermacht', titleLF: 'W4 H82', title: 'BMW M4 F82', safeTitle: 'Ubermacht W4 H82' },
    'm3e46': { price: 3000000, class: 'unique', seasonPass: 2, capacity: 90000, fuelType: 'Regular', fuelCap: 62, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: 'M3 E46', brandLF: 'Ubermacht', titleLF: 'W3 A46', title: 'BMW M3 E46', safeTitle: 'Ubermacht W3 A46' },
    // SeasonPass 2 - конец

    // SeasonPass 3 - начало
    'ec120': { price: 6600000, tags: ["heli"], class: 'helidonate', seasonPass: 3, capacity: 1500000, fuelType: 'Premium', fuelCap: 800, fuelConsumption: 5, brandRL: 'Eurocopter', titleRL: 'EC120 Colibri', brandLF: 'Buckingham', titleLF: 'EK120', title: 'Eurocopter EC120 Colibri', safeTitle: 'Buckingham EK120' },
    'mistral': { price: 21200000, class: 'unique', seasonPass: 3, capacity: 20000, fuelType: 'Premium', fuelCap: 84, fuelConsumption: 1.8, brandRL: 'Bugatti', titleRL: 'W16 Mistral', brandLF: 'Truffade', titleLF: 'Wistral', title: 'Bugatti W16 Mistral', safeTitle: 'Truffade Wistral' },
    'revuelto': { price: 18400000, class: 'unique', seasonPass: 3, capacity: 30000, fuelType: 'Premium', fuelCap: 78, fuelConsumption: 1.7, brandRL: 'Lamborghini', titleRL: 'Revuelto', brandLF: 'Pegassi', titleLF: 'Rewuelto', title: 'Lamborghini Revuelto', safeTitle: 'Pegassi Rewuelto' },
    'mc20': { price: 17200000, class: 'unique', seasonPass: 3, capacity: 40000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.6, brandRL: 'Maserati', titleRL: 'MC20', brandLF: 'Lampadati', titleLF: 'MS20', title: 'Maserati MC20', safeTitle: 'Lampadati MS20' },
    'sclass4': { price: 16000000, class: 'unique', seasonPass: 3, capacity: 110000, fuelType: 'Premium', fuelCap: 85, fuelConsumption: 1.8, brandRL: 'Mercedes-Benz', titleRL: 'S-Class Z223', brandLF: 'Benefactor', titleLF: 'S-Series M223 LWB', title: 'Mercedes-Benz S-Class Z223', safeTitle: 'Benefactor S-Series M223 LWB' },
    'rrphantom2': { price: 17200000, class: 'unique', seasonPass: 3, capacity: 140000, fuelType: 'Premium', fuelCap: 88, fuelConsumption: 2.2, brandRL: 'Rolls-Royce', titleRL: 'Phantom Limo', brandLF: 'Enus', titleLF: 'Phantasm Limo', title: 'Rolls-Royce Phantom Limo', safeTitle: 'Enus Phantasm Limo' },
    'hummer3': { price: 17200000, class: 'unique', seasonPass: 3, capacity: 250000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.5, brandRL: 'GMC', titleRL: 'Hummer EV', brandLF: 'Mammoth', titleLF: 'Hammer EV 6x6', title: 'GMC Hummer EV', safeTitle: 'Mammoth Hammer EV 6x6' },
    'ab2': { price: 10000000, class: 'unique', seasonPass: 3, capacity: 140000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.7, brandRL: 'Range Rover', titleRL: 'Autobiography L460', brandLF: 'Gallivanter', titleLF: 'AB L460', title: 'Range Rover Autobiography L460', safeTitle: 'Gallivanter AB L460' },
    'diavel': { price: 3560000, tags: ["moto"], class: 'unique', seasonPass: 3, capacity: 0, fuelType: 'Premium', fuelCap: 40, fuelConsumption: 0.8, brandRL: 'Ducati', titleRL: 'Diavel', brandLF: 'Principe', titleLF: 'Diawel', title: 'Ducati Diavel', safeTitle: 'Principe Diawel' },
    'slr': { price: 8000000, class: 'unique', seasonPass: 3, capacity: 30000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.8, brandRL: 'Mercedes-Benz', titleRL: '300 SLR', brandLF: 'Benefactor', titleLF: '300 ZLR', title: 'Mercedes-Benz 300 SLR', safeTitle: 'Benefactor 300 ZLR' },
    'cls2': { price: 6400000, class: 'unique', seasonPass: 3, capacity: 90000, fuelType: 'Premium', fuelCap: 72, fuelConsumption: 1.5, brandRL: 'Mercedes-Benz', titleRL: 'CLS C257', brandLF: 'Benefactor', titleLF: 'ZLS S257', title: 'Mercedes-Benz CLS C257', safeTitle: 'Benefactor ZLS S257' },
    'm3g81': { price: 5000000, class: 'unique', seasonPass: 3, capacity: 120000, fuelType: 'Premium', fuelCap: 78, fuelConsumption: 1.6, brandRL: 'BMW', titleRL: 'M3 G81 Touring', brandLF: 'Ubermacht', titleLF: 'W3 J81 Touring', title: 'BMW M3 G81 Touring', safeTitle: 'Ubermacht W3 J81 Touring' },
    'giulia': { price: 6400000, class: 'unique', seasonPass: 3, capacity: 80000, fuelType: 'Plus', fuelCap: 74, fuelConsumption: 1.5, brandRL: 'Alfa Romeo', titleRL: 'Giulia', brandLF: 'Lampadati', titleLF: 'Jiulia', title: 'Alfa Romeo Giulia', safeTitle: 'Lampadati Jiulia' },
    '400z': { price: 8000000, class: 'unique', seasonPass: 3, capacity: 60000, fuelType: 'Plus', fuelCap: 62, fuelConsumption: 1.2, brandRL: 'Nissan', titleRL: 'Z', brandLF: 'Annis', titleLF: '400X', title: 'Nissan Z', safeTitle: 'Annis 400X' },
    'xc90': { price: 3320000, class: 'unique', seasonPass: 3, capacity: 90000, fuelType: 'Plus', fuelCap: 90, fuelConsumption: 1.6, brandRL: 'Volvo', titleRL: 'XC90', brandLF: 'Vulcar', titleLF: 'CX90', title: 'Volvo XC90', safeTitle: 'Vulcar CX90' },
    // SeasonPass 3 - конец

    // SeasonPass 4 - начало
    'xclass2': { price: 20000000, class: 'unique', seasonPass: 4, capacity: 250000, fuelType: 'Premium', fuelCap: 126, fuelConsumption: 4.5, brandRL: 'Mercedes-Benz', titleRL: 'X-Class BF', brandLF: 'Benefactor', titleLF: 'X-Monster', title: 'Mercedes-Benz X-Class BF', safeTitle: 'Benefactor X-Monster' },
    'daytona': { price: 18400000, class: 'unique', seasonPass: 4, capacity: 30000, fuelType: 'Premium', fuelCap: 102, fuelConsumption: 2.3, brandRL: 'Ferrari', titleRL: 'Daytona SP3', brandLF: 'Grotti', titleLF: 'Timucua', title: 'Ferrari Daytona SP3', safeTitle: 'Grotti Timucua' },
    'xk': { price: 8000000, class: 'unique', seasonPass: 4, capacity: 20000, fuelType: 'Diesel', fuelCap: 85, fuelConsumption: 1.2, brandRL: 'Sea Ray', titleRL: 'XK', brandLF: 'Buckingham', titleLF: 'XK', title: 'Sea Ray XK', safeTitle: 'Buckingham XK' },
    'lanzador': { price: 17200000, class: 'unique', seasonPass: 4, capacity: 150000, fuelType: 'Electro', fuelCap: 110, fuelConsumption: 2.4, brandRL: 'Lamborghini', titleRL: 'Lanzador', brandLF: 'Pegassi', titleLF: 'Brocca', title: 'Lamborghini Lanzador', safeTitle: 'Pegassi Brocca' },
    'purosangue': { price: 16000000, class: 'unique', seasonPass: 4, capacity: 160000, fuelType: 'Premium', fuelCap: 116, fuelConsumption: 2.5, brandRL: 'Ferrari', titleRL: 'Purosangue', brandLF: 'Grotti', titleLF: 'Primogenito', title: 'Ferrari Purosangue', safeTitle: 'Grotti Primogenito' },
    'dbx': { price: 14400000, class: 'unique', seasonPass: 4, capacity: 150000, fuelType: 'Premium', fuelCap: 106, fuelConsumption: 2.6, brandRL: 'Aston Martin', titleRL: 'DBX', brandLF: 'Dewbauchee', titleLF: 'DPX', title: 'Aston-Martin DBX', safeTitle: 'Dewbauchee DPX' },
    'ram2': { price: 12800000, class: 'unique', seasonPass: 4, capacity: 200000, fuelType: 'Diesel', fuelCap: 130, fuelConsumption: 3.2, brandRL: 'Dodge', titleRL: 'RAM TRX', brandLF: 'Bravado', titleLF: 'RUM RTX', title: 'Dodge RAM TRX', safeTitle: 'Bravado RUM RTX' },
    'ix': { price: 11200000, class: 'unique', seasonPass: 4, capacity: 140000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 3, brandRL: 'BMW', titleRL: 'IX', brandLF: 'Ubermacht', titleLF: 'lY', title: 'BMX IX', safeTitle: 'Ubermacht lY' },
    'eclass4': { price: 8800000, class: 'unique', seasonPass: 4, capacity: 130000, fuelType: 'Premium', fuelCap: 88, fuelConsumption: 1.6, brandRL: 'Mercedes-Benz', titleRL: 'E-Class S214 Estate', brandLF: 'Benefactor', titleLF: 'E-Series C214 Estate', title: 'Mercedes-Benz E-Class S214 Estate', safeTitle: 'Benefactor E-Series C214 Estate' },
    'polestar': { price: 7200000, class: 'unique', seasonPass: 4, capacity: 70000, fuelType: 'Premium', fuelCap: 84, fuelConsumption: 2.1, brandRL: 'Volvo', titleRL: 'Polestar 1', brandLF: 'Vulcar', titleLF: 'Solarstar 1', title: 'Volvo Polestar 1', safeTitle: 'Vulcar Solarstar 1' },
    'mustang3': { price: 5600000, class: 'unique', seasonPass: 4, capacity: 100000, fuelType: 'Premium', fuelCap: 85, fuelConsumption: 2, brandRL: 'Ford', titleRL: 'Mustang S650', brandLF: 'Vapid', titleLF: 'Steed C650', title: 'Ford Mustang S650', safeTitle: 'Vapid Steed C650' },
    'm2g42': { price: 4000000, class: 'unique', seasonPass: 4, capacity: 55000, fuelType: 'Plus', fuelCap: 64, fuelConsumption: 1.4, brandRL: 'BMW', titleRL: '2-series G42', brandLF: 'Ubermacht', titleLF: '2-line J42', title: 'BMW 2-series G42', safeTitle: 'Ubermacht 2-line J42' },
    // SeasonPass 4 - конец

    // SeasonPass 4 Battle-Case - начало
    'utopia': { price: 20000000, class: 'unique', seasonPass: 4, capacity: 25000, fuelType: 'Premium', fuelCap: 102, fuelConsumption: 2.6, brandRL: 'Pagani', titleRL: 'Utopia', brandLF: 'Pegassi', titleLF: 'Paradise', title: 'Pagani Utopia', safeTitle: 'Pegassi Paradise' },
    'regera': { price: 16000000, class: 'unique', seasonPass: 4, capacity: 40000, fuelType: 'Premium', fuelCap: 106, fuelConsumption: 2.1, brandRL: 'Koenigsegg', titleRL: 'Regera', brandLF: 'Overflod', titleLF: 'Entity RG-R', title: 'Koenigsegg Regera', safeTitle: 'Entity RG-R' },
    'corvette2': { price: 12000000, class: 'unique', seasonPass: 4, capacity: 50000, fuelType: 'Premium', fuelCap: 94, fuelConsumption: 2.2, brandRL: 'Chevrolet', titleRL: 'Corvette C8', brandLF: 'Declasse', titleLF: 'Covetto S8', title: 'Chevrolet Corvette C8', safeTitle: 'Declasse Covetto S8' },
    'escalade2': { price: 5000000, class: 'unique', seasonPass: 4, capacity: 170000, fuelType: 'Premium', fuelCap: 132, fuelConsumption: 2.8, brandRL: 'Cadillac', titleRL: 'Escalade GM T1XL', brandLF: 'Albany', titleLF: 'Excalibur MG', title: 'Cadillac Escalade GM T1XL', safeTitle: 'Cavalcade Excalibur MG' },
    'aclass2': { price: 1200000, class: 'unique', seasonPass: 4, capacity: 110000, fuelType: 'Plus', fuelCap: 58, fuelConsumption: 1.5, brandRL: 'Mercedes-Benz', titleRL: 'A-Class W177', brandLF: 'Benefactor', titleLF: 'A-Series M177', title: 'Mercedes-Benz A-Class W177', safeTitle: 'Benefactor A-Series M177' },
    'nsx2': { price: 1200000, class: 'unique', seasonPass: 4, capacity: 65000, fuelType: 'Regular', fuelCap: 62, fuelConsumption: 1.6, brandRL: 'Honda', titleRL: 'NSX NA1', brandLF: 'Dinka', titleLF: 'NSY NI1', title: 'Honda NSX NA1', safeTitle: 'Dinka NSY NI1' },
    'gtr32': { price: 1200000, class: 'unique', seasonPass: 4, capacity: 55000, fuelType: 'Regular', fuelCap: 68, fuelConsumption: 1.7, brandRL: 'Nissan', titleRL: 'Skyline GT-R R32', brandLF: 'Annis', titleLF: 'GS-R M32', title: 'Nissan Skyline GT-R R32', safeTitle: 'Annis GS-R M32' },
    'charger2': { price: 980000, class: 'unique', seasonPass: 4, capacity: 90000, fuelType: 'Regular', fuelCap: 92, fuelConsumption: 2.4, brandRL: 'Dodge', titleRL: 'Charger', brandLF: 'Bravado', titleLF: 'Changer', title: 'Dodge Charger', safeTitle: 'Bravado Changer' },
    '190e': { price: 850000, class: 'unique', seasonPass: 4, capacity: 60000, fuelType: 'Regular', fuelCap: 74, fuelConsumption: 1.6, brandRL: 'Mercedes-Benz', titleRL: '190 E', brandLF: 'Benefactor', titleLF: '910 E', title: 'Mercedes-Benz 190 E', safeTitle: 'Benefactor 910 E' },
    'yz450f': { price: 600000, tags: ["moto"], class: 'unique', seasonPass: 4, capacity: 0, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 1.2, brandRL: 'Yamaha', titleRL: 'YZ 450 F', brandLF: 'Shitzu', titleLF: 'ZY 450', title: 'Yamaha YZ 450 F', safeTitle: 'Shitzu ZY 450' },
    // SeasonPass 4 Battle-Case - конец

    // SeasonPass 5 - начало
    'r66': { price: 5000000, tags: ["heli"], class: 'helidonate', seasonPass: 5, capacity: 1600000, fuelType: 'Diesel', fuelCap: 1000, fuelConsumption: 5, brandRL: 'Robinson', titleRL: 'R66', brandLF: 'Buckingham', titleLF: 'FDR-66', title: 'Robinson R66', safeTitle: 'Buckingham FDR-66' },
    'cc850': { price: 20000000, class: 'unique', seasonPass: 5, capacity: 30000, fuelType: 'Premium', fuelCap: 72, fuelConsumption: 2.2, brandRL: 'Koenigsegg', titleRL: 'CC850', brandLF: 'Overflod', titleLF: 'Entity Selip-850', title: 'Koenigsegg CC850', safeTitle: 'Entity Selip-850' },
    'cybertruck': { price: 8000000, class: 'unique', seasonPass: 5, capacity: 210000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 3, brandRL: 'Tesla', titleRL: 'Cybertruck', brandLF: 'Coil', titleLF: 'Alt-Truck', title: 'Tesla Cybertruck', safeTitle: 'Coil Alt-Truck' },
    'evija': { price: 15000000, class: 'unique', seasonPass: 5, capacity: 5000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2, brandRL: 'Lotus', titleRL: 'Evija', brandLF: 'Ocelot', titleLF: 'Aevij', title: 'Lotus Evija', safeTitle: 'Ocelot Aevij' },
    'amggt2': { price: 12000000, class: 'unique', seasonPass: 5, capacity: 60000, fuelType: 'Premium', fuelCap: 70, fuelConsumption: 1.8, brandRL: 'Mercedes-AMG', titleRL: 'GT C192', brandLF: 'Benefactor', titleLF: '-ASG GS S192', title: 'Mercedes-AMG GT C192', safeTitle: 'Benefactor-ASG GS S192' },
    'macan2': { price: 8000000, class: 'unique', seasonPass: 5, capacity: 160000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2, brandRL: 'Porsche', titleRL: 'Macan EV', brandLF: 'Pfister', titleLF: 'Mauer EV', title: 'Porsche Macan EV', safeTitle: 'Pfister Mauer EV' },
    'air': { price: 7000000, class: 'unique', seasonPass: 5, capacity: 90000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 1.7, brandRL: 'Lucid', titleRL: 'Air', brandLF: 'Coil', titleLF: 'Jenks', title: 'Lucid Air', safeTitle: 'Coil Jenks' },
    'rrs': { price: 5500000, class: 'unique', seasonPass: 5, capacity: 140000, fuelType: 'Plus', fuelCap: 90, fuelConsumption: 1.8, brandRL: 'Range Rover', titleRL: 'Sport L461', brandLF: 'Gallivanter', titleLF: 'Gover I461', title: 'Range Rover Sport L461', safeTitle: 'Gallivanter Gover I461' },
    'lfa': { price: 4250000, class: 'unique', seasonPass: 5, capacity: 45000, fuelType: 'Premium', fuelCap: 73, fuelConsumption: 1.8, brandRL: 'Lexus', titleRL: 'LFA', brandLF: 'Emperor', titleLF: 'FLA', title: 'Lexus LFA', safeTitle: 'Emperor FLA' },
    '5series2': { price: 3500000, class: 'unique', seasonPass: 5, capacity: 130000, fuelType: 'Plus', fuelCap: 60, fuelConsumption: 1.8, brandRL: 'BMW', titleRL: '5-Series G61', brandLF: 'Ubermacht', titleLF: '5-line J61', title: 'BMW 5-Series G61', safeTitle: 'Ubermacht 5-line J61' },
    'd8': { price: 3000000, class: 'unique', seasonPass: 5, capacity: 0, fuelType: 'Premium', fuelCap: 48, fuelConsumption: 1.6, brandRL: 'Donkervoort', titleRL: 'D8 GTO', brandLF: 'BF', titleLF: 'T8 GTR', title: 'Donkervoort D8 GTO', safeTitle: 'Burgerfahrzeug T8 GTR' },
    'bronco': { price: 2300000, class: 'unique', seasonPass: 5, capacity: 60000, fuelType: 'Plus', fuelCap: 64, fuelConsumption: 1.6, brandRL: 'Ford', titleRL: 'Bronco U725', brandLF: 'Vapid', titleLF: 'Frey', title: 'Ford Bronco U725', safeTitle: 'Vapid Frey' },
    'civic2': { price: 1200000, class: 'unique', seasonPass: 5, capacity: 70000, fuelType: 'Plus', fuelCap: 50, fuelConsumption: 1.1, brandRL: 'Honda', titleRL: 'Civic FK1', brandLF: 'Dinka', titleLF: 'Hiroshi KF1', title: 'Honda Civic FK1', safeTitle: 'Dinka Hiroshi KF1' },
    // SeasonPass 5 - конец

    // SeasonPass 5 Battle-Case - начало
    'tecnomar': { price: 30000000, tags: ["boat"], class: 'boatdonate', seasonPass: 5, capacity: 1000000, fuelType: 'Diesel', fuelCap: 400, fuelConsumption: 1.5, brandRL: 'Lamborghini', titleRL: 'Tecnomar', brandLF: 'Pegassi', titleLF: 'Viareggio', title: 'Lamborghini Tecnomar', safeTitle: 'Pegassi Viareggio' },
    'speedtail': { price: 20000000, class: 'unique', seasonPass: 5, capacity: 20000, fuelType: 'Premium', fuelCap: 72, fuelConsumption: 2.2, brandRL: 'McLaren', titleRL: 'Speedtail', brandLF: 'Progen', titleLF: 'Alex', title: 'McLaren Speedtail', safeTitle: 'Progen Alex' },
    'invencible': { price: 18000000, class: 'unique', seasonPass: 5, capacity: 0, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 2.1, brandRL: 'Lamborghini', titleRL: 'Invencible', brandLF: 'Pegassi', titleLF: 'Borkert', title: 'Lamborghini Invencible', safeTitle: 'Pegassi Borkert' },
    'j50': { price: 16000000, class: 'unique', seasonPass: 5, capacity: 15000, fuelType: 'Premium', fuelCap: 78, fuelConsumption: 2, brandRL: 'Ferrari', titleRL: 'J50', brandLF: 'Grotti', titleLF: 'Flaman', title: 'Ferrari J50', safeTitle: 'Grotti Flaman' },
    'victoria2': { price: 14000000, class: 'unique', seasonPass: 5, capacity: 60000, fuelType: 'Premium', fuelCap: 72, fuelConsumption: 1.9, brandRL: 'Ford', titleRL: 'Crown Victoria GT', brandLF: 'Vapid', titleLF: 'Coronet V GT', title: 'Ford Crown Victoria GT', safeTitle: 'Vapid Coronet V GT' },
    'huracan2': { price: 12000000, class: 'unique', seasonPass: 5, capacity: 50000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 1.7, brandRL: 'Lamborghini', titleRL: 'Huracan Spyder', brandLF: 'Pegassi', titleLF: 'Hurricane Spyder', title: 'Lamborghini Huracan Spyder', safeTitle: 'Pegassi Hurricane Spyder' },
    'sl': { price: 10000000, class: 'unique', seasonPass: 5, capacity: 60000, fuelType: 'Plus', fuelCap: 70, fuelConsumption: 1.6, brandRL: 'Mercedes-AMG', titleRL: 'SL R232', brandLF: 'Benefactor', titleLF: '-ASG ZL P232', title: 'Mercedes-AMG SL R232', safeTitle: 'Benefactor-ASG ZL P232' },
    'bentayga2': { price: 9000000, class: 'unique', seasonPass: 5, capacity: 160000, fuelType: 'Diesel', fuelCap: 85, fuelConsumption: 2.1, brandRL: 'Bentley', titleRL: 'Bentayga II', brandLF: 'Enus', titleLF: 'Benteygo GT', title: 'Bentley Bentayga II', safeTitle: 'Enus Benteygo GT' },
    'db112': { price: 8000000, class: 'unique', seasonPass: 5, capacity: 50000, fuelType: 'Plus', fuelCap: 78, fuelConsumption: 1.7, brandRL: 'Aston Martin', titleRL: 'DB11 Volante', brandLF: 'Dewbauchee', titleLF: 'DP11 Cabrio', title: 'Aston Martin DB11 Volante', safeTitle: 'Dewbauchee DP11 Cabrio' },
    'lc500': { price: 7000000, class: 'unique', seasonPass: 5, capacity: 75000, fuelType: 'Premium', fuelCap: 82, fuelConsumption: 1.6, brandRL: 'Lexus', titleRL: 'LC 500', brandLF: 'Emperor', titleLF: 'CL 500', title: 'Lexus LC 500', safeTitle: 'Emperor CL 500' },
    'panamera2': { price: 6000000, class: 'unique', seasonPass: 5, capacity: 140000, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 1.6, brandRL: 'Porsche', titleRL: 'Panamera G2 Sport Turismo', brandLF: 'Pfister', titleLF: 'Paramena J2 Touring', title: 'Porsche Panamera G2 Sport Turismo', safeTitle: 'Pfister Paramena J2 Touring' },
    'eqs': { price: 6000000, class: 'unique', seasonPass: 5, capacity: 100000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 1.5, brandRL: 'Mercedes-Benz', titleRL: 'EQS', brandLF: 'Benefactor', titleLF: 'IQS', title: 'Mercedes-Benz EQS', safeTitle: 'Benefactor IQS' },
    'levante': { price: 5500000, class: 'unique', seasonPass: 5, capacity: 140000, fuelType: 'Plus', fuelCap: 80, fuelConsumption: 2.1, brandRL: 'Maserati', titleRL: 'Levante', brandLF: 'Lampadati', titleLF: 'Turin', title: 'Maserati Levante', safeTitle: 'Lampadati Turin' },
    'corvette3': { price: 5000000, class: 'unique', seasonPass: 5, capacity: 100000, fuelType: 'Regular', fuelCap: 62, fuelConsumption: 1.5, brandRL: 'Chevrolet', titleRL: 'Corvette C1', brandLF: 'Declasse', titleLF: 'Covetto S1', title: 'Chevrolet Corvette C1', safeTitle: 'Declasse Covetto S1' },
    'tt2': { price: 3500000, class: 'unique', seasonPass: 5, capacity: 50000, fuelType: 'Plus', fuelCap: 45, fuelConsumption: 1.1, brandRL: 'Audi', titleRL: 'TT 8S Roadster', brandLF: 'Obey', titleLF: 'DD 8C Cabrio', title: 'Audi TT 8S Roadster', safeTitle: 'Obey DD 8C Cabrio' },
    'i82': { price: 3000000, class: 'unique', seasonPass: 5, capacity: 40000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 1.3, brandRL: 'BMW', titleRL: 'i8 Roadster', brandLF: 'Ubermacht', titleLF: 'E8 Roadster', title: 'BMW i8 Roadster', safeTitle: 'Ubermacht E8 Roadster' },
    'countryman': { price: 2000000, class: 'unique', seasonPass: 5, capacity: 110000, fuelType: 'Plus', fuelCap: 51, fuelConsumption: 1.3, brandRL: 'MINI', titleRL: 'Countryman', brandLF: 'Weeny', titleLF: 'Mich', title: 'MINI Countryman', safeTitle: 'Weeny Mich' },
    'hummer5': { price: 1600000, class: 'unique', seasonPass: 5, capacity: 160000, fuelType: 'Diesel', fuelCap: 112, fuelConsumption: 2.4, brandRL: 'GMC', titleRL: 'Hummer H1 4x4', brandLF: 'Mammoth', titleLF: 'Hammer 4x4', title: 'GMC Hummer H1 4x4', safeTitle: 'Mammoth Hammer 4x4' },
    'mx5': { price: 750000, class: 'unique', seasonPass: 5, capacity: 60000, fuelType: 'Regular', fuelCap: 48, fuelConsumption: 1.2, brandRL: 'Mazda', titleRL: 'MX-5', brandLF: 'Annis', titleLF: 'MZ-5', title: 'Mazda MX-5', safeTitle: 'Annis MZ-5' },
    'civic': { price: 600000, class: 'unique', seasonPass: 5, capacity: 45000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 0.8, brandRL: 'Honda', titleRL: 'Civic EK6', brandLF: 'Dinka', titleLF: 'Hiroshi EF1', title: 'Honda Civic EK6', safeTitle: 'Dinka Hiroshi EF1' },
    'golf2': { price: 450000, class: 'unique', seasonPass: 5, capacity: 40000, fuelType: 'Regular', fuelCap: 45, fuelConsumption: 0.9, brandRL: 'Volkswagen', titleRL: 'Golf', brandLF: 'BF', titleLF: 'Wolf', title: 'Volkswagen Golf', safeTitle: 'BF Wolf' },
    'fury': { price: 420000, class: 'unique', seasonPass: 5, capacity: 85000, fuelType: 'Regular', fuelCap: 87, fuelConsumption: 1.8, brandRL: 'Plymouth', titleRL: 'Fury', brandLF: 'Imponte', titleLF: 'Calm', title: 'Plymouth Fury', safeTitle: 'Imponte Calm' },
    'impala2': { price: 340000, class: 'unique', seasonPass: 5, capacity: 95000, fuelType: 'Regular', fuelCap: 76, fuelConsumption: 2.4, brandRL: 'Chevrolet', titleRL: 'Impala', brandLF: 'Declasse', titleLF: 'Implata', title: 'Chevrolet Impala', safeTitle: 'Declasse Implata' },
    // SeasonPass 5 Battle-Case - конец

    // SeasonPass 6 - начало
    'ah6': { price: 20000000, tags: ["heli"], class: 'helidonate', seasonPass: 6, capacity: 1600000, fuelType: 'Diesel', fuelCap: 1000, fuelConsumption: 5, brandRL: 'MD Helicopters', titleRL: 'AH-6 Little Bird', brandLF: 'Buckingham', titleLF: 'HA-6', title: 'MD Helicopters AH-6 Little Bird', safeTitle: 'Buckingham HA-6' },
    'snowmobile': { price: 2000000, class: 'unique', seasonPass: 6, capacity: 0, fuelType: 'Diesel', fuelCap: 50, fuelConsumption: 0.6, brandRL: 'BRP', titleRL: 'Ski-Doo Sammit Rev-XM', brandLF: 'Speedophile', titleLF: 'Snowshark', title: 'BRP Ski-Doo Sammit Rev-XM', safeTitle: 'Speedophile Snowshark' },
    'tourbillon': { price: 20000000, class: 'donate', seasonPass: 6, capacity: 15000, fuelType: 'Premium', fuelCap: 85, fuelConsumption: 2.3, brandRL: 'Bugatti', titleRL: 'Tourbillon', brandLF: 'Truffade', titleLF: 'Heyl', title: 'Bugatti Tourbillon', safeTitle: 'Truffade Heyl' },
    'temerario': { price: 17000000, class: 'unique', seasonPass: 6, capacity: 0, fuelType: 'Premium', fuelCap: 90, fuelConsumption: 2, brandRL: 'Lamborghini', titleRL: 'Temerario', brandLF: 'Pegassi', titleLF: 'Mitja', title: 'Lamborghini Temerario', safeTitle: 'Pegassi Mitja' },
    'spectre': { price: 13000000, class: 'unique', seasonPass: 6, capacity: 80000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.3, brandRL: 'Rolls-Royce', titleRL: 'Spectre', brandLF: 'Enus', titleLF: 'Rosenthal', title: 'Rolls-Royce Spectre', safeTitle: 'Enus Rosenthal' },
    'roma': { price: 11500000, class: 'unique', seasonPass: 6, capacity: 50000, fuelType: 'Premium', fuelCap: 80, fuelConsumption: 2.1, brandRL: 'Ferrari', titleRL: 'Roma', brandLF: 'Grotti', titleLF: 'Manzoni', title: 'Ferrari Roma', safeTitle: 'Grotti Manzoni' },
    'etron2': { price: 8000000, class: 'unique', seasonPass: 6, capacity: 110000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.1, brandRL: 'Audi', titleRL: 'Q8 e-tron', brandLF: 'Obey', titleLF: 'O8 I-tron', title: 'Audi Q8 e-tron', safeTitle: 'Obey O8 I-tron' },
    'cle': { price: 7000000, class: 'unique', seasonPass: 6, capacity: 45000, fuelType: 'Premium', fuelCap: 66, fuelConsumption: 1.7, brandRL: 'Mercedes-Benz', titleRL: 'CLE C236', brandLF: 'Benefactor', titleLF: 'ZLE S236', title: 'Mercedes-Benz CLE C236', safeTitle: 'Benefactor ZLE S236' },
    'ex90': { price: 6000000, class: 'unique', seasonPass: 6, capacity: 100000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2, brandRL: 'Volvo', titleRL: 'EX90', brandLF: 'Vulcar', titleLF: 'XE90', title: 'Volvo EX90', safeTitle: 'Vulcar XE90' },
    'glc': { price: 5000000, class: 'unique', seasonPass: 6, capacity: 90000, fuelType: 'Plus', fuelCap: 62, fuelConsumption: 1.9, brandRL: 'Mercedes-Benz', titleRL: 'GLC C254', brandLF: 'Benefactor', titleLF: 'JLC S254', title: 'Mercedes-Benz GLC C254', safeTitle: 'Benefactor JLC S254' },
    'model3': { price: 3000000, class: 'unique', seasonPass: 6, capacity: 70000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 1.8, brandRL: 'Tesla', titleRL: 'Model 3', brandLF: 'Coil', titleLF: 'Line 3', title: 'Tesla Model 3', safeTitle: 'Coil Line 3' },
    '350z': { price: 2000000, class: 'unique', seasonPass: 6, capacity: 45000, fuelType: 'Plus', fuelCap: 75, fuelConsumption: 1.7, brandRL: 'Nissan', titleRL: '350Z', brandLF: 'Annis', titleLF: '350X', title: 'Nissan 350Z', safeTitle: 'Annis 350X' },
    // SeasonPass 6 - конец

    // SeasonPass 6 Battle-Case - начало
    'nevera': { price: 20000000, class: 'unique', seasonPass: 6, capacity: 25000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.6, brandRL: 'Rimac', titleRL: 'Nevera', brandLF: 'Rimus', titleLF: '4-VR', title: 'Rimac Nevera', safeTitle: 'Rimus 4-VR' },
    'su7': { price: 12000000, class: 'unique', seasonPass: 6, capacity: 80000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2.4, brandRL: 'Xiaomi', titleRL: 'SU7', brandLF: 'Lumi', titleLF: 'US7', title: 'Xiaomi SU7', safeTitle: 'Lumi US7' },
    'testarossa': { price: 10000000, class: 'unique', seasonPass: 6, capacity: 70000, fuelType: 'Premium', fuelCap: 115, fuelConsumption: 2.4, brandRL: 'Ferrari', titleRL: 'Testarossa', brandLF: 'Grotti', titleLF: 'Ottina', title: 'Ferrari Testarossa', safeTitle: 'Grotti Ottina' },
    'sportage': { price: 6000000, class: 'unique', seasonPass: 6, capacity: 115000, fuelType: 'Premium', fuelCap: 54, fuelConsumption: 1.6, brandRL: 'KIA', titleRL: 'Sportage NQ5', brandLF: 'Bollokan', titleLF: 'Turboweek QN5', title: 'KIA Sportage NQ5', safeTitle: 'Bollokan Turboweek QN5' },
    'ev6': { price: 5000000, class: 'unique', seasonPass: 6, capacity: 85000, fuelType: 'Electro', fuelCap: 100, fuelConsumption: 2, brandRL: 'KIA', titleRL: 'EV6', brandLF: 'Bollokan', titleLF: 'VE6', title: 'KIA EV6', safeTitle: 'Bollokan VE6' },
    'f7': { price: 4000000, class: 'unique', seasonPass: 6, capacity: 80000, fuelType: 'Plus', fuelCap: 56, fuelConsumption: 1.7, brandRL: 'Haval', titleRL: 'F7', brandLF: 'Hebei', titleLF: 'V7', title: 'Haval F7', safeTitle: 'Hebei V7' },
    'mazda3': { price: 3000000, class: 'unique', seasonPass: 6, capacity: 55000, fuelType: 'Regular', fuelCap: 51, fuelConsumption: 1.5, brandRL: 'Mazda', titleRL: '3 BM', brandLF: 'Annis', titleLF: '3 MB', title: 'Mazda 3 BM', safeTitle: 'Annis 3 MB' },
    'ml': { price: 2000000, class: 'unique', seasonPass: 6, capacity: 90000, fuelType: 'Plus', fuelCap: 95, fuelConsumption: 1.8, brandRL: 'Mercedes-Benz', titleRL: 'ML', brandLF: 'Benefactor', titleLF: 'LM', title: 'Mercedes-Benz ML', safeTitle: 'Benefactor LM' },
    'rsx': { price: 800000, class: 'unique', seasonPass: 6, capacity: 45000, fuelType: 'Regular', fuelCap: 50, fuelConsumption: 1.2, brandRL: 'Acura', titleRL: 'RSX', brandLF: 'Dinka', titleLF: 'ASX', title: 'Acura RSX', safeTitle: 'Dinka ASX' },
    'barracuda': { price: 600000, class: 'unique', seasonPass: 6, capacity: 35000, fuelType: 'Regular', fuelCap: 68, fuelConsumption: 2.1, brandRL: 'Plymouth', titleRL: 'Barracuda', brandLF: 'Imponte', titleLF: 'Herlitz', title: 'Plymouth Barracuda', safeTitle: 'Imponte Herlitz' },
    // SeasonPass 6 Battle-Case - конец
};
