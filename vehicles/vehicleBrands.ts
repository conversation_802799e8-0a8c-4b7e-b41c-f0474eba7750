export type VehicleBrand = {
  title: string,
  brand: string,
  brandRL?: string;
  tags: string[],
  img?: string,
  active: boolean,
}

export const vehicleBrands: VehicleBrand[] = [
    {
        title: 'Nagasaki',
        brand: 'Nagasaki',
        tags: [ 'moto' ],
        active: false
      },
      {
        title: '<PERSON><PERSON>',
        brand: '<PERSON><PERSON>',
        brandRL: '<PERSON>',
        tags: [ 'moto' ],
        active: false
      },
      {
        title: '<PERSON><PERSON>',
        brand: '<PERSON><PERSON>',
        brandRL: 'Acura',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Speedophile',
        brand: 'Speedophile',
        brandRL: 'BRP',
        tags: [ 'car' ],
        active: false
      },
      {
        title: '<PERSON><PERSON><PERSON><PERSON>',
        brand: '<PERSON><PERSON><PERSON><PERSON>',
        brandRL: '<PERSON><PERSON><PERSON><PERSON>',
        tags: [ 'car' ],
        active: true
      },
      { title: 'Kraken', brand: 'Kraken', tags: [ 'boat' ], active: false },
      {
        title: 'Lampadati',
        brand: 'Lampadati',
        brandRL: 'Maserati',
        tags: [ 'car' ],
        active: false
      },
      {
        title: '<PERSON>',
        brand: '<PERSON>',
        brandRL: 'MD Helicopters',
        tags: [ 'heli' ],
        active: true
      },
      {
        title: 'RUNE',
        brand: 'RUNE',
        brandRL: 'VAZ',
        tags: [ 'donate' ],
        active: false
      },
      {
        title: 'Vapid',
        brand: 'Vapid',
        brandRL: 'Ford',
        tags: [ 'car' ],
        active: true
      },
      { title: 'HVY', brand: 'HVY', tags: [ 'car' ], active: false },
      { title: 'MTL', brand: 'MTL', tags: [ 'car' ], active: false },
      {
        title: 'JoBuilt',
        brand: 'JoBuilt',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Maibatsu',
        brand: 'Maibatsu',
        brandRL: 'Mitsubishi',
        tags: [ 'car' ],
        active: false
      },
      { title: 'Brute', brand: 'Brute', tags: [ 'car' ], active: true },
      {
        title: 'Benefactor',
        brand: 'Benefactor',
        brandRL: 'Mercedes-Benz',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Maxwell',
        brand: 'Maxwell',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Grotti',
        brand: 'Grotti',
        brandRL: 'Ferrari',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Karin',
        brand: 'Karin',
        brandRL: 'Toyota',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Weeny',
        brand: 'Weeny',
        brandRL: 'MINI',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Bollokan',
        brand: 'Bollokan',
        brandRL: 'KIA',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Declasse',
        brand: 'Declasse',
        brandRL: 'Chevrolet',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'BF',
        brand: 'BF',
        brandRL: 'Volkswagen',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Enus',
        brand: 'Enus',
        brandRL: 'Rolls-Royce',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Dewbauchee',
        brand: 'Dewbauchee',
        brandRL: 'Aston Martin',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Ocelot',
        brand: 'Ocelot',
        brandRL: 'Lotus',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Ubermacht',
        brand: 'Ubermacht',
        brandRL: 'BMW',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'PED Cycles',
        brand: 'PED Cycles',
        tags: [ 'bicycle' ],
        active: false
      },
      {
        title: 'R-Bike',
        brand: 'R-Bike',
        tags: [ 'bicycle' ],
        active: false
      },
      {
        title: 'Coil',
        brand: 'Coil',
        brandRL: 'Tesla',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Western Company',
        brand: 'Western Company',
        tags: [ 'plane' ],
        active: false
      },
      {
        title: 'Canis',
        brand: 'Canis',
        brandRL: 'Jeep',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Bravado',
        brand: 'Bravado',
        brandRL: 'Dodge',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Mammoth',
        brand: 'Mammoth',
        brandRL: 'GMC',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Vom Feuer',
        brand: 'Vom Feuer',
        tags: [ 'car' ],
        active: false
      },
      { title: 'RC', brand: 'RC', tags: [ 'car' ], active: false },
      { title: 'LCC', brand: 'LCC', tags: [ 'moto' ], active: false },
      {
        title: 'Western',
        brand: 'Western',
        tags: [ 'moto' ],
        active: false
      },
      {
        title: 'Principe',
        brand: 'Principe',
        brandRL: 'Ducati',
        tags: [ 'moto' ],
        active: false
      },
      {
        title: 'Albany',
        brand: 'Albany',
        brandRL: 'Cadillac',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Invetero',
        brand: 'Invetero',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Schyster',
        brand: 'Schyster',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Imponte',
        brand: 'Imponte',
        brandRL: 'Plymouth',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Willard',
        brand: 'Willard',
        tags: [ 'car' ],
        active: false
      },
      { title: 'Cheval', brand: 'Cheval', tags: [ 'car' ], active: true },
      {
        title: 'Dundreary ',
        brand: 'Dundreary ',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Classique',
        brand: 'Classique',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Annis',
        brand: 'Annis',
        brandRL: 'Mazda',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Progen',
        brand: 'Progen',
        brandRL: 'McLaren',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Jobuilt',
        brand: 'Jobuilt',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Gallivanter',
        brand: 'Gallivanter',
        brandRL: 'Range Rover',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Fathom',
        brand: 'Fathom',
        brandRL: 'Infiniti',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Emperor',
        brand: 'Emperor',
        brandRL: 'Lexus',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Obey',
        brand: 'Obey',
        brandRL: 'Audi',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Vulcar',
        brand: 'Vulcar',
        brandRL: 'Volvo',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Zirconium',
        brand: 'Zirconium',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Pfister',
        brand: 'Pfister',
        brandRL: 'Porsche',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Overflod',
        brand: 'Overflod',
        brandRL: 'Koenigsegg',
        tags: [ 'car' ],
        active: false
      },
      { title: 'Hijak', brand: 'Hijak', tags: [ 'car' ], active: false },
      { title: 'Vysser', brand: 'Vysser', tags: [ 'car' ], active: false },
      {
        title: 'Toundra',
        brand: 'Toundra',
        tags: [ 'car' ],
        active: false
      },
      { title: 'Penaud', brand: 'Penaud', tags: [ 'car' ], active: false },
      {
        title: 'Truffade',
        brand: 'Truffade',
        brandRL: 'Maibatsu',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Jack Sheepe',
        brand: 'Jack Sheepe',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Stanley',
        brand: 'Stanley',
        tags: [ 'car' ],
        active: false
      },
      { title: 'Brute ', brand: 'Brute ', tags: [ 'car' ], active: false },
      { title: 'MLT', brand: 'MLT', tags: [ 'car' ], active: false },
      { title: 'WMC', brand: 'WMC', tags: [ 'car' ], active: false },
      {
        title: 'Dundreary',
        brand: 'Dundreary',
        tags: [ 'car' ],
        active: true
      },
      {
        title: 'Secret',
        brand: 'Secret',
        brandRL: 'Dodge',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Quartz',
        brand: 'Quartz',
        brandRL: 'Quartz',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Quadra',
        brand: 'Quadra',
        brandRL: 'Quadra',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'AMG',
        brand: 'AMG',
        brandRL: 'AMG',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Rimus',
        brand: 'Rimus',
        brandRL: 'Rimac',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Lumi',
        brand: 'Lumi',
        brandRL: 'Xiaomi',
        tags: [ 'car' ],
        active: false
      },
      {
        title: 'Hebei',
        brand: 'Hebei',
        brandRL: 'Haval',
        tags: [ 'car' ],
        active: false
      }
]