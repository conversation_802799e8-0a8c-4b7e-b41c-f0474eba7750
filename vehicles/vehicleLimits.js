if (!globalThis.mp) {
	globalThis.mp = {
		Vector3: class {
			constructor() {}
		},
		game: {
			joaat: (key) => {
				const keyLowered = key.toLowerCase();
				let hash = 0;

				for (let i = 0; i < keyLowered.length; i++) {
					hash += keyLowered.charCodeAt(i);
					hash += hash << 10;
					hash ^= hash >>> 6;
				}

				hash += hash << 3;
				hash ^= hash >>> 11;
				hash += hash << 15;

				return hash >>> 0;
			},
		},
	};
}

const vehicleLimits = [
	// model: Модель авто
	// limit: км/ч
	// Lore-Friendly
	{ model: mp.game.joaat('bulletems'), limit: 335 },
	{ model: mp.game.joaat('coquetteems'), limit: 335 },
	{ model: mp.game.joaat('dominator3ems'), limit: 310 },
	{ model: mp.game.joaat('vigero2ems'), limit: 310 },
	{ model: mp.game.joaat('tor_fib_speedunit1'), limit: 284 },
	{ model: mp.game.joaat('tor_fib_novak'), limit: 284 },
	{ model: mp.game.joaat('tor_fib_jugular'), limit: 284 },
	{ model: mp.game.joaat('tor_fib_speedunit'), limit: 284 },
	{ model: mp.game.joaat('tor_sheriff_speedunit'), limit: 284 },
	{ model: mp.game.joaat('tor_police_speedunit'), limit: 284 },
	{ model: mp.game.joaat('tor_police_unmarked'), limit: 273 },
	{ model: mp.game.joaat('tor_police_buffalo'), limit: 273 },
	{ model: mp.game.joaat('tor_fib_gresley'), limit: 250 },
	{ model: mp.game.joaat('tor_sheriff_cheval'), limit: 268 },
	{ model: mp.game.joaat('tor_sheriff_cruiser'), limit: 270 },
	{ model: mp.game.joaat('tor_police_cruiser'), limit: 270 },
	{ model: mp.game.joaat('tor_sheriff_unmarked'), limit: 270 },
	{ model: mp.game.joaat('tor_police_cruiser2'), limit: 270 },
	{ model: mp.game.joaat('tor_fib_neon'), limit: 286 },
	{ model: mp.game.joaat('tor_unmarked_scout'), limit: 270 },
	{ model: mp.game.joaat('tor_fib_armor1'), limit: 270 },
	{ model: mp.game.joaat('tor_sheriff_scout'), limit: 270 },
	{ model: mp.game.joaat('tor_police_scout'), limit: 270 },
	{ model: mp.game.joaat('tor_police_scout_k9'), limit: 270 },
	{ model: mp.game.joaat('tor_sheriff_scout_k9'), limit: 270 },
	{ model: mp.game.joaat('pscoutnew'), limit: 270 },
	{ model: mp.game.joaat('weevil2'), limit: 190 },
	// Lore-Friendly дальнобои
	{ model: mp.game.joaat('youga2'), limit: 115 },
	{ model: mp.game.joaat('bobcatxl'), limit: 125 },
	{ model: mp.game.joaat('youga'), limit: 120 },
	{ model: mp.game.joaat('moonbeam'), limit: 145 },
	{ model: mp.game.joaat('youga3'), limit: 120 },
	{ model: mp.game.joaat('sadler'), limit: 125 },
	{ model: mp.game.joaat('burrito'), limit: 120 },
	{ model: mp.game.joaat('bison'), limit: 145 },
	{ model: mp.game.joaat('sandking2'), limit: 145 },
	{ model: mp.game.joaat('sandking'), limit: 148 },
	{ model: mp.game.joaat('caracara2'), limit: 135 },
	{ model: mp.game.joaat('mule'), limit: 105 },
	{ model: mp.game.joaat('hellion'), limit: 145 },
	{ model: mp.game.joaat('yosemiteswb'), limit: 160 },
	{ model: mp.game.joaat('everon'), limit: 140 },
	{ model: mp.game.joaat('contender'), limit: 160 },
	{ model: mp.game.joaat('bison2'), limit: 140 },
	{ model: mp.game.joaat('hellion2'), limit: 160 },
	{ model: mp.game.joaat('pounder'), limit: 140 },
	{ model: mp.game.joaat('pounder3'), limit: 135 },
	{ model: mp.game.joaat('benson'), limit: 150 },
	{ model: mp.game.joaat('nspeedo'), limit: 160 },
	// Real Life
	{ model: mp.game.joaat('jesko20'), limit: 315 },
	{ model: mp.game.joaat('rrphantom'), limit: 302 },
	{ model: mp.game.joaat('ff12'), limit: 295 },
	{ model: mp.game.joaat('488pista'), limit: 285 },
	{ model: mp.game.joaat('vulcan'), limit: 286 },
	{ model: mp.game.joaat('taycan'), limit: 274 },
	{ model: mp.game.joaat('gt63s'), limit: 271 },
	{ model: mp.game.joaat('63gls'), limit: 267 },
	{ model: mp.game.joaat('cayen19'), limit: 267 },
	{ model: mp.game.joaat('bmwx7'), limit: 267 },
	{ model: mp.game.joaat('s63w222'), limit: 267 },
	{ model: mp.game.joaat('bmwm7'), limit: 267 },
	{ model: mp.game.joaat('gle63'), limit: 261 },
	{ model: mp.game.joaat('x6m'), limit: 261 },
	{ model: mp.game.joaat('r820'), limit: 262 },
	{ model: mp.game.joaat('m850'), limit: 262 },
	{ model: mp.game.joaat('nsx'), limit: 262 },
	{ model: mp.game.joaat('jgc'), limit: 254 },
	{ model: mp.game.joaat('x5g05'), limit: 254 },
	{ model: mp.game.joaat('rs6'), limit: 255 },
	{ model: mp.game.joaat('i8'), limit: 254 },
	{ model: mp.game.joaat('corvett08'), limit: 255 },
	{ model: mp.game.joaat('ast'), limit: 254 },
	{ model: mp.game.joaat('skyline'), limit: 253 },
	{ model: mp.game.joaat('370z'), limit: 239 },
	{ model: mp.game.joaat('modelx'), limit: 246 },
	{ model: mp.game.joaat('cesc21'), limit: 248 },
	{ model: mp.game.joaat('gsx1000'), limit: 283 },
	{ model: mp.game.joaat('charger20'), limit: 250 },
	{ model: mp.game.joaat('lex570'), limit: 246 },
	{ model: mp.game.joaat('rrab'), limit: 246 },
	{ model: mp.game.joaat('evo10'), limit: 246 },
	{ model: mp.game.joaat('rs7'), limit: 247 },
	{ model: mp.game.joaat('x5me70'), limit: 244 },
	{ model: mp.game.joaat('lc200'), limit: 244 },
	{ model: mp.game.joaat('2020mustang'), limit: 249 },
	{ model: mp.game.joaat('bmwg20'), limit: 241 },
	{ model: mp.game.joaat('kiastinger'), limit: 241 },
	{ model: mp.game.joaat('m5e60'), limit: 238 },
	{ model: mp.game.joaat('evo9'), limit: 239 },
	{ model: mp.game.joaat('touareg'), limit: 234 },
	{ model: mp.game.joaat('tahoe2'), limit: 234 },
	{ model: mp.game.joaat('a80'), limit: 239 },
	{ model: mp.game.joaat('s15'), limit: 239 },
	{ model: mp.game.joaat('rx7'), limit: 239 },
	{ model: mp.game.joaat('supragr'), limit: 239 },
	{ model: mp.game.joaat('mark2'), limit: 230 },
	{ model: mp.game.joaat('camry70'), limit: 229 },
	{ model: mp.game.joaat('accord'), limit: 229 },
	{ model: mp.game.joaat('16challenger'), limit: 237 },
	{ model: mp.game.joaat('subwrx'), limit: 226 },
	{ model: mp.game.joaat('focusrs'), limit: 219 },
	{ model: mp.game.joaat('golf7r'), limit: 222 },
	{ model: mp.game.joaat('w210'), limit: 215 },
	{ model: mp.game.joaat('bmwe39'), limit: 215 },
	{ model: mp.game.joaat('s600'), limit: 207 },
	{ model: mp.game.joaat('bmwe38'), limit: 207 },
	{ model: mp.game.joaat('octavia18'), limit: 207 },
	{ model: mp.game.joaat('ae86'), limit: 195 },
	{ model: mp.game.joaat('chiron19'), limit: 321 },
	{ model: mp.game.joaat('asvj'), limit: 296 },
	{ model: mp.game.joaat('mp1'), limit: 296 },
	{ model: mp.game.joaat('urus'), limit: 286 },
	{ model: mp.game.joaat('cullinan'), limit: 290 },
	{ model: mp.game.joaat('g63amg6x6'), limit: 271 },
	{ model: mp.game.joaat('huracan'), limit: 287 },
	{ model: mp.game.joaat('continental'), limit: 281 },
	{ model: mp.game.joaat('pts21'), limit: 281 },
	{ model: mp.game.joaat('ghost'), limit: 272 },
	{ model: mp.game.joaat('g63'), limit: 271 },
	{ model: mp.game.joaat('panamera17turbo'), limit: 272 },
	{ model: mp.game.joaat('amggt'), limit: 266 },
	{ model: mp.game.joaat('cls63s'), limit: 262 },
	{ model: mp.game.joaat('bentaygast'), limit: 261 },
	{ model: mp.game.joaat('e63s'), limit: 269 },
	{ model: mp.game.joaat('m5comp'), limit: 269 },
	{ model: mp.game.joaat('v4sp'), limit: 286 },
	{ model: mp.game.joaat('nisgtr'), limit: 256 },
	{ model: mp.game.joaat('msprinter'), limit: 196 },
	{ model: mp.game.joaat('vclass'), limit: 221 },
	{ model: mp.game.joaat('priora'), limit: 218 },
	{ model: mp.game.joaat('urban'), limit: 218 },
	{ model: mp.game.joaat('vesta'), limit: 218 },
	{ model: mp.game.joaat('wraithb'), limit: 296 },
	{ model: mp.game.joaat('pscoutnew'), limit: 290 },
	{ model: mp.game.joaat('swatstalker'), limit: 290 },
	{ model: mp.game.joaat('mjc'), limit: 227 },
	{ model: mp.game.joaat('rs72'), limit: 270 },
	{ model: mp.game.joaat('camaro21'), limit: 249 },
	{ model: mp.game.joaat('718bs'), limit: 260 },
	{ model: mp.game.joaat('m4comp'), limit: 261 },
	{ model: mp.game.joaat('imola'), limit: 325 },
	{ model: mp.game.joaat('63gls2'), limit: 274 },
	{ model: mp.game.joaat('ftype'), limit: 265 },
	{ model: mp.game.joaat('rio'), limit: 195 },
	{ model: mp.game.joaat('velar'), limit: 254 },
	{ model: mp.game.joaat('laferrari'), limit: 321 },
	{ model: mp.game.joaat('reventon'), limit: 280 },
	{ model: mp.game.joaat('s63cab'), limit: 275 },
	{ model: mp.game.joaat('z4b'), limit: 262 },
	{ model: mp.game.joaat('viper'), limit: 269 },
	{ model: mp.game.joaat('countach'), limit: 305 },
	{ model: mp.game.joaat('m8gc'), limit: 272 },
	{ model: mp.game.joaat('x6m2'), limit: 272 },
	{ model: mp.game.joaat('f150'), limit: 262 },
	{ model: mp.game.joaat('lc300'), limit: 254 },
	{ model: mp.game.joaat('fx50s'), limit: 246 },
	{ model: mp.game.joaat('impala'), limit: 225 },
	{ model: mp.game.joaat('dsprinter'), limit: 196 },
	{ model: mp.game.joaat('actros'), limit: 201 },
	{ model: mp.game.joaat('t680'), limit: 201 },
	{ model: mp.game.joaat('bdivo'), limit: 335 },
	{ model: mp.game.joaat('centenario'), limit: 325 },
	{ model: mp.game.joaat('samara'), limit: 210 },
	{ model: mp.game.joaat('brutale'), limit: 295 },
	{ model: mp.game.joaat('g55'), limit: 258 },
	{ model: mp.game.joaat('touareg2'), limit: 219 },
	{ model: mp.game.joaat('rs5'), limit: 261 },
	{ model: mp.game.joaat('fordgt'), limit: 290 },
	{ model: mp.game.joaat('gemera'), limit: 302 },
	{ model: mp.game.joaat('veneno'), limit: 337 },
	{ model: mp.game.joaat('models'), limit: 271 },
	{ model: mp.game.joaat('f40'), limit: 274 },
	{ model: mp.game.joaat('sf90'), limit: 315 },
	{ model: mp.game.joaat('720s'), limit: 310 },
	{ model: mp.game.joaat('mustang2'), limit: 235 },
	{ model: mp.game.joaat('ram'), limit: 249 },
	{ model: mp.game.joaat('dawn'), limit: 273 },
	{ model: mp.game.joaat('c300'), limit: 268 },
	{ model: mp.game.joaat('camaro2'), limit: 235 },
	{ model: mp.game.joaat('etron'), limit: 274 },
	{ model: mp.game.joaat('macan'), limit: 254 },
	{ model: mp.game.joaat('xes'), limit: 264 },
	{ model: mp.game.joaat('camry2'), limit: 210 },
	{ model: mp.game.joaat('terzo'), limit: 335 },
	{ model: mp.game.joaat('fxxk'), limit: 325 },
	{ model: mp.game.joaat('bacalar'), limit: 310 },
	{ model: mp.game.joaat('cayenne2'), limit: 275 },
	{ model: mp.game.joaat('delorean'), limit: 271 },
	{ model: mp.game.joaat('z800'), limit: 296 },
	{ model: mp.game.joaat('m4f82'), limit: 257 },
	{ model: mp.game.joaat('m3e46'), limit: 230 },
	{ model: mp.game.joaat('gtr50'), limit: 276 },
	{ model: mp.game.joaat('veyron'), limit: 290 },
	{ model: mp.game.joaat('918s'), limit: 320 },
	{ model: mp.game.joaat('amgone'), limit: 333 },
	{ model: mp.game.joaat('xclass'), limit: 252 },
	{ model: mp.game.joaat('bolide'), limit: 337 },
	{ model: mp.game.joaat('essenza'), limit: 337 },
	{ model: mp.game.joaat('matiz'), limit: 215 },
	{ model: mp.game.joaat('q8'), limit: 281 },
	{ model: mp.game.joaat('sclass3'), limit: 278 },
	{ model: mp.game.joaat('mistral'), limit: 337 },
	{ model: mp.game.joaat('revuelto'), limit: 327 },
	{ model: mp.game.joaat('mc20'), limit: 317 },
	{ model: mp.game.joaat('hummer3'), limit: 271 },
	{ model: mp.game.joaat('rrphantom2'), limit: 276 },
	{ model: mp.game.joaat('sclass4'), limit: 307 },
	{ model: mp.game.joaat('ab2'), limit: 283 },
	{ model: mp.game.joaat('diavel'), limit: 290 },
	{ model: mp.game.joaat('slr'), limit: 267 },
	{ model: mp.game.joaat('400z'), limit: 272 },
	{ model: mp.game.joaat('cls2'), limit: 268 },
	{ model: mp.game.joaat('giulia'), limit: 262 },
	{ model: mp.game.joaat('m3g81'), limit: 258 },
	{ model: mp.game.joaat('xc90'), limit: 247 },
	{ model: mp.game.joaat('diablo'), limit: 274 },
	{ model: mp.game.joaat('hummer'), limit: 261 },
	{ model: mp.game.joaat('hummer2'), limit: 250 },
	{ model: mp.game.joaat('missionr'), limit: 335 },
	{ model: mp.game.joaat('xm'), limit: 271 },
	{ model: mp.game.joaat('db5'), limit: 271 },
	{ model: mp.game.joaat('charger3'), limit: 268 },
	{ model: mp.game.joaat('modelt'), limit: 214 },
	{ model: mp.game.joaat('gle2'), limit: 254 },
	{ model: mp.game.joaat('aclass'), limit: 229 },
	{ model: mp.game.joaat('m7g70'), limit: 278 },
	{ model: mp.game.joaat('190e'), limit: 209 },
	{ model: mp.game.joaat('aclass2'), limit: 229 },
	{ model: mp.game.joaat('charger2'), limit: 233 },
	{ model: mp.game.joaat('corvette2'), limit: 305 },
	{ model: mp.game.joaat('daytona'), limit: 337 },
	{ model: mp.game.joaat('dbx'), limit: 283 },
	{ model: mp.game.joaat('eclass4'), limit: 255 },
	{ model: mp.game.joaat('escalade2'), limit: 267 },
	{ model: mp.game.joaat('gtr32'), limit: 245 },
	{ model: mp.game.joaat('ix'), limit: 274 },
	{ model: mp.game.joaat('lanzador'), limit: 310 },
	{ model: mp.game.joaat('m2g42'), limit: 259 },
	{ model: mp.game.joaat('mustang3'), limit: 272 },
	{ model: mp.game.joaat('nsx2'), limit: 251 },
	{ model: mp.game.joaat('polestar'), limit: 280 },
	{ model: mp.game.joaat('purosangue'), limit: 295 },
	{ model: mp.game.joaat('ram2'), limit: 264 },
	{ model: mp.game.joaat('regera'), limit: 327 },
	{ model: mp.game.joaat('utopia'), limit: 337 },
	{ model: mp.game.joaat('yz450f'), limit: 241 },
	{ model: mp.game.joaat('carrera'), limit: 239 },
	{ model: mp.game.joaat('q60s'), limit: 252 },
	{ model: mp.game.joaat('s2000'), limit: 222 },
	{ model: mp.game.joaat('senna'), limit: 339 },
	{ model: mp.game.joaat('emira'), limit: 325 },
	{ model: mp.game.joaat('f1502'), limit: 272 },
	{ model: mp.game.joaat('db11'), limit: 285 },
	{ model: mp.game.joaat('brz'), limit: 262 },
	{ model: mp.game.joaat('k5'), limit: 247 },
	{ model: mp.game.joaat('eclipse'), limit: 232 },
	{ model: mp.game.joaat('victoria'), limit: 215 },
	{ model: mp.game.joaat('vaz2107'), limit: 210 },
	{ model: mp.game.joaat('5series'), limit: 273 },
	{ model: mp.game.joaat('eclass3'), limit: 273 },
	{ model: mp.game.joaat('tt'), limit: 262 },
	{ model: mp.game.joaat('cc850'), limit: 336 },
	{ model: mp.game.joaat('cybertruck'), limit: 268 },
	{ model: mp.game.joaat('evija'), limit: 319 },
	{ model: mp.game.joaat('amggt2'), limit: 305 },
	{ model: mp.game.joaat('macan2'), limit: 279 },
	{ model: mp.game.joaat('air'), limit: 275 },
	{ model: mp.game.joaat('rrs'), limit: 269 },
	{ model: mp.game.joaat('lfa'), limit: 267 },
	{ model: mp.game.joaat('5series2'), limit: 256 },
	{ model: mp.game.joaat('d8'), limit: 261 },
	{ model: mp.game.joaat('bronco'), limit: 252 },
	{ model: mp.game.joaat('civic2'), limit: 236 },
	{ model: mp.game.joaat('speedtail'), limit: 341 },
	{ model: mp.game.joaat('invencible'), limit: 330 },
	{ model: mp.game.joaat('j50'), limit: 325 },
	{ model: mp.game.joaat('victoria2'), limit: 307 },
	{ model: mp.game.joaat('huracan2'), limit: 305 },
	{ model: mp.game.joaat('sl'), limit: 294 },
	{ model: mp.game.joaat('bentayga2'), limit: 284 },
	{ model: mp.game.joaat('db112'), limit: 284 },
	{ model: mp.game.joaat('lc500'), limit: 279 },
	{ model: mp.game.joaat('panamera2'), limit: 270 },
	{ model: mp.game.joaat('eqs'), limit: 268 },
	{ model: mp.game.joaat('levante'), limit: 267 },
	{ model: mp.game.joaat('corvette3'), limit: 272 },
	{ model: mp.game.joaat('tt2'), limit: 259 },
	{ model: mp.game.joaat('i82'), limit: 254 },
	{ model: mp.game.joaat('countryman'), limit: 244 },
	{ model: mp.game.joaat('hummer5'), limit: 231 },
	{ model: mp.game.joaat('civic'), limit: 217 },
	{ model: mp.game.joaat('mx5'), limit: 208 },
	{ model: mp.game.joaat('golf2'), limit: 197 },
	{ model: mp.game.joaat('fury'), limit: 194 },
	{ model: mp.game.joaat('impala2'), limit: 187 },
	{ model: mp.game.joaat('gclass4'), limit: 266 },
	{ model: mp.game.joaat('noire'), limit: 335 },
	{ model: mp.game.joaat('valour'), limit: 317 },
	{ model: mp.game.joaat('batur'), limit: 303 },
	{ model: mp.game.joaat('sls'), limit: 280 },
	{ model: mp.game.joaat('xts'), limit: 243 },
	{ model: mp.game.joaat('silverado'), limit: 269 },
	{ model: mp.game.joaat('ct5'), limit: 267 },
	{ model: mp.game.joaat('defender'), limit: 257 },
	{ model: mp.game.joaat('eclass5'), limit: 253 },
	{ model: mp.game.joaat('5series3'), limit: 253 },
	{ model: mp.game.joaat('cclass2'), limit: 245 },
	{ model: mp.game.joaat('a4'), limit: 235 },
	{ model: mp.game.joaat('astra'), limit: 232 },
	{ model: mp.game.joaat('rio2'), limit: 222 },
	{ model: mp.game.joaat('regalia'), limit: 305 },
	{ model: mp.game.joaat('turbor'), limit: 288 },
	{ model: mp.game.joaat('m12b'), limit: 262 },
	{ model: mp.game.joaat('firebird'), limit: 256 },
	{ model: mp.game.joaat('wrangler'), limit: 222 },
	{ model: mp.game.joaat('p928'), limit: 229 },
	{ model: mp.game.joaat('f1503'), limit: 212 },
	{ model: mp.game.joaat('galant'), limit: 202 },
	{ model: mp.game.joaat('tourbillon'), limit: 335 },
	{ model: mp.game.joaat('temerario'), limit: 327 },
	{ model: mp.game.joaat('spectre'), limit: 306 },
	{ model: mp.game.joaat('roma'), limit: 302 },
	{ model: mp.game.joaat('etron2'), limit: 277 },
	{ model: mp.game.joaat('cle'), limit: 277 },
	{ model: mp.game.joaat('ex90'), limit: 267 },
	{ model: mp.game.joaat('glc'), limit: 264 },
	{ model: mp.game.joaat('model3'), limit: 251 },
	{ model: mp.game.joaat('350z'), limit: 247 },
	{ model: mp.game.joaat('nevera'), limit: 332 },
	{ model: mp.game.joaat('su7'), limit: 299 },
	{ model: mp.game.joaat('testarossa'), limit: 294 },
	{ model: mp.game.joaat('sportage'), limit: 269 },
	{ model: mp.game.joaat('ev6'), limit: 262 },
	{ model: mp.game.joaat('f7'), limit: 257 },
	{ model: mp.game.joaat('mazda3'), limit: 252 },
	{ model: mp.game.joaat('ml'), limit: 244 },
	{ model: mp.game.joaat('rsx'), limit: 218 },
	{ model: mp.game.joaat('barracuda'), limit: 210 },
	{ model: mp.game.joaat('mustang4'), limit: 225 },
	{ model: mp.game.joaat('a42'), limit: 228 },
	{ model: mp.game.joaat('idr'), limit: 353 },
	{ model: mp.game.joaat('f8'), limit: 319 },
	{ model: mp.game.joaat('a8'), limit: 280 },
	{ model: mp.game.joaat('durango'), limit: 244 },
	{ model: mp.game.joaat('gnx'), limit: 234 },
	{ model: mp.game.joaat('solaris'), limit: 225 },
	{ model: mp.game.joaat('defender2'), limit: 237 },
	{ model: mp.game.joaat('vaz2109'), limit: 207 },
];
export default vehicleLimits;

export const vehicleLimitMap = new Map();
vehicleLimits.forEach((el) => {
	vehicleLimitMap.set(el.model, el.limit);
});
