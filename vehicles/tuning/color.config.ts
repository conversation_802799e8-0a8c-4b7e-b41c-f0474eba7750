export interface IColorMenu {
	action: ColorType;
	title: string;
	eventName: string;
	colorModIdToModType?: {
		[colorModType: number]: number;
	};
	colorMod?: {
		[colorModType: string]: {
			vehPriceRate: number;
			rgbColorPrice?: number;
			items: {
				id: number;
				hex: string;
				price: number;
			}[];
		};
	};
	colorRgb?: {
		vehPriceRate: number;
		rgbColorPrice?: number;
		items: {
			id: number;
			hex: string;
			price: number;
		}[];
	};

	colors?: {
		vehPriceRate: number;
		rgbColorPrice?: number;
		items: {
			colorId: number;
			hex?: string;
			price: number;
		}[];
	};

	// rgbColorPrice?: number;
}

export interface IColor {
	r: number;
	g: number;
	b: number;
}

export interface IColorInfo extends IColor {
	mod: number;
}

export enum ColorType {
	VMT_PAINT1 = 'VMT_PAINT1', // Основной цвет
	VMT_PAINT2 = 'VMT_PAINT2', // Дополнительный цвет
	VMT_PAINT3 = 'VMT_PAINT3', // Цвет Перламутра
	VMT_PAINT4 = 'VMT_PAINT4', // Цвет диска
	VMT_PAINT6 = 'VMT_PAINT6', // Цвет деталей
	VMT_PAINT7 = 'VMT_PAINT7', // Доп. цвет деталей
	VMT_PAINT_LIGHTBAR = 'VMT_PAINT_LIGHTBAR', // Свет фар
	VMT_PAINT_TYRE_SMOKE = 'VMT_PAINT_TYRE_SMOKE', // Цвет дыма покрышек
	VMT_PAINT_NEON = 'VMT_PAINT_NEON', // Цвет неона
}

export enum ColorModType {
	VMT_PAINT_MATERIAL_1 = 1, // Яркий металлик
	VMT_PAINT_MATERIAL_2 = 2, // Металлик
	VMT_PAINT_MATERIAL_3 = 3, // Насыщенный металлик
	VMT_PAINT_MATERIAL_4 = 4, // Тёмный металлик
	VMT_PAINT_MATERIAL_5 = 5, // Матовый
	VMT_PAINT_MATERIAL_6 = 6, // Матовый металл
	VMT_PAINT_MATERIAL_7 = 7, // Сатин
	VMT_PAINT_MATERIAL_8 = 8, // Металл
	VMT_PAINT_MATERIAL_9 = 9, // Теневой хром
	VMT_PAINT_MATERIAL_10 = 10, // Чистый хром
}

export const colorMods: {
	[modType: number]: {
		id: number;
		title: string;
	};
} = {
	[ColorModType.VMT_PAINT_MATERIAL_1]: {
		id: 1,
		title: 'vehicleTuning.colorMods.VMT_PAINT_MATERIAL_1', // Яркий металлик
	},
	[ColorModType.VMT_PAINT_MATERIAL_2]: {
		id: 2,
		title: 'vehicleTuning.colorMods.VMT_PAINT_MATERIAL_2', // Металлик
	},
	[ColorModType.VMT_PAINT_MATERIAL_3]: {
		id: 3,
		title: 'vehicleTuning.colorMods.VMT_PAINT_MATERIAL_3', // Насыщенный металлик
	},
	[ColorModType.VMT_PAINT_MATERIAL_4]: {
		id: 4,
		title: 'vehicleTuning.colorMods.VMT_PAINT_MATERIAL_4', // Тёмный металлик
	},
	[ColorModType.VMT_PAINT_MATERIAL_5]: {
		id: 5,
		title: 'vehicleTuning.colorMods.VMT_PAINT_MATERIAL_5', // Матовый
	},
	[ColorModType.VMT_PAINT_MATERIAL_6]: {
		id: 6,
		title: 'vehicleTuning.colorMods.VMT_PAINT_MATERIAL_6', // Матовый металл
	},
	[ColorModType.VMT_PAINT_MATERIAL_7]: {
		id: 7,
		title: 'vehicleTuning.colorMods.VMT_PAINT_MATERIAL_7', // Сатин
	},
	[ColorModType.VMT_PAINT_MATERIAL_8]: {
		id: 8,
		title: 'vehicleTuning.colorMods.VMT_PAINT_MATERIAL_8', // Металл
	},
	[ColorModType.VMT_PAINT_MATERIAL_9]: {
		id: 9,
		title: 'vehicleTuning.colorMods.VMT_PAINT_MATERIAL_9', // Теневой хром
	},
	[ColorModType.VMT_PAINT_MATERIAL_10]: {
		id: 10,
		title: 'vehicleTuning.colorMods.VMT_PAINT_MATERIAL_10', // Чистый хром
	},
};

export interface IColorsInfo {
	[ColorType.VMT_PAINT1]: IColorInfo; // Основной цвет
	[ColorType.VMT_PAINT2]: IColorInfo; // Дополнительный цвет
}

export interface IColorData {
	colorTypes?: {
		id: number;
		title: string;
	}[];
	colorTypesRGB?: {
		id: number;
		title: string;
	}[];
	colors?: {
		id: number;
		hex: string;
		price: number;
	}[];
	currentColor?: string | number;
	pickerType?: string;
	pickerTypes?: {
		id: string;
		title: string;
	}[];
	rgbColorPrice?: number;
}

export const colorMenu: IColorMenu[] = [
	{
		action: ColorType.VMT_PAINT1,
		title: 'vehicleTuning.colorMenu.VMT_PAINT1',
		eventName: 'client.tuning.setColor',
		colorMod: {
			[ColorModType.VMT_PAINT_MATERIAL_1]: {
				vehPriceRate: 0.4,
				rgbColorPrice: 299,
				items: [
					// Первый ряд
					{ id: 101, hex: `#FFFFFF`, price: 3000 },
					{ id: 102, hex: `#E7E5E4`, price: 3000 },
					{ id: 103, hex: `#E2E8F0`, price: 3000 },
					{ id: 104, hex: `#A5B4FC`, price: 3000 },
					{ id: 105, hex: `#93C5FD`, price: 3000 },
					{ id: 106, hex: `#93C5FD`, price: 3000 },
					{ id: 107, hex: `#5EEAD4`, price: 3000 },
					{ id: 108, hex: `#86EFAC`, price: 3000 },
					{ id: 109, hex: `#BEF264`, price: 3000 },
					{ id: 110, hex: `#FDE047`, price: 3000 },
					{ id: 111, hex: `#FDBA74`, price: 3000 },
					{ id: 112, hex: `#FCA5A5`, price: 3000 },
					{ id: 113, hex: `#FDA4AF`, price: 3000 },
					{ id: 114, hex: `#D8B4FE`, price: 3000 },
					// Второй ряд
					{ id: 115, hex: `#C9C9C9`, price: 3000 },
					{ id: 116, hex: `#A8A29E`, price: 3000 },
					{ id: 117, hex: `#94A3B8`, price: 3000 },
					{ id: 118, hex: `#6366F1`, price: 3000 },
					{ id: 119, hex: `#3B82F6`, price: 3000 },
					{ id: 120, hex: `#06B6D4`, price: 3000 },
					{ id: 121, hex: `#14B8A6`, price: 3000 },
					{ id: 122, hex: `#22C55E`, price: 3000 },
					{ id: 123, hex: `#84CC16`, price: 3000 },
					{ id: 124, hex: `#EAB308`, price: 3000 },
					{ id: 125, hex: `#F97316`, price: 3000 },
					{ id: 126, hex: `#EF4444`, price: 3000 },
					{ id: 127, hex: `#F43F5E`, price: 3000 },
					{ id: 128, hex: `#A855F7`, price: 3000 },
					// Третий ряд
					{ id: 129, hex: `#858585`, price: 3000 },
					{ id: 130, hex: `#57534E`, price: 3000 },
					{ id: 131, hex: `#475569`, price: 3000 },
					{ id: 132, hex: `#4338CA`, price: 3000 },
					{ id: 133, hex: `#1D4ED8`, price: 3000 },
					{ id: 134, hex: `#0E7490`, price: 3000 },
					{ id: 135, hex: `#0F766E`, price: 3000 },
					{ id: 136, hex: `#15803D`, price: 3000 },
					{ id: 137, hex: `#4D7C0F`, price: 3000 },
					{ id: 138, hex: `#A16207`, price: 3000 },
					{ id: 139, hex: `#C2410C`, price: 3000 },
					{ id: 140, hex: `#AB1313`, price: 3000 },
					{ id: 141, hex: `#BE123C`, price: 3000 },
					{ id: 142, hex: `#9333EA`, price: 3000 },
					// Четвертый ряд
					{ id: 143, hex: `#404040`, price: 3000 },
					{ id: 144, hex: `#292524`, price: 3000 },
					{ id: 145, hex: `#1E293B`, price: 3000 },
					{ id: 146, hex: `#312E81`, price: 3000 },
					{ id: 147, hex: `#1E3A8A`, price: 3000 },
					{ id: 148, hex: `#164E63`, price: 3000 },
					{ id: 149, hex: `#134E4A`, price: 3000 },
					{ id: 150, hex: `#14532D`, price: 3000 },
					{ id: 151, hex: `#365314`, price: 3000 },
					{ id: 152, hex: `#713F12`, price: 3000 },
					{ id: 153, hex: `#7C2D12`, price: 3000 },
					{ id: 154, hex: `#8F1313`, price: 3000 },
					{ id: 155, hex: `#881337`, price: 3000 },
					{ id: 156, hex: `#6B21A8`, price: 3000 },
					// Пятый ряд
					{ id: 157, hex: `#000000`, price: 3000 },
					{ id: 158, hex: `#0C0A09`, price: 3000 },
					{ id: 159, hex: `#020617`, price: 3000 },
					{ id: 160, hex: `#1E1B4B`, price: 3000 },
					{ id: 161, hex: `#082F49`, price: 3000 },
					{ id: 162, hex: `#083344`, price: 3000 },
					{ id: 163, hex: `#042F2E`, price: 3000 },
					{ id: 164, hex: `#052E16`, price: 3000 },
					{ id: 165, hex: `#1A2E05`, price: 3000 },
					{ id: 166, hex: `#422006`, price: 3000 },
					{ id: 167, hex: `#431407`, price: 3000 },
					{ id: 168, hex: `#470707`, price: 3000 },
					{ id: 169, hex: `#4C0519`, price: 3000 },
					{ id: 170, hex: `#3B0764`, price: 3000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_2]: {
				vehPriceRate: 0.425,
				rgbColorPrice: 299,
				items: [
					// Первый ряд
					{ id: 201, hex: `#FFFFFF`, price: 3000 },
					{ id: 202, hex: `#E7E5E4`, price: 3000 },
					{ id: 203, hex: `#E2E8F0`, price: 3000 },
					{ id: 204, hex: `#A5B4FC`, price: 3000 },
					{ id: 205, hex: `#93C5FD`, price: 3000 },
					{ id: 206, hex: `#93C5FD`, price: 3000 },
					{ id: 207, hex: `#5EEAD4`, price: 3000 },
					{ id: 208, hex: `#86EFAC`, price: 3000 },
					{ id: 209, hex: `#BEF264`, price: 3000 },
					{ id: 210, hex: `#FDE047`, price: 3000 },
					{ id: 211, hex: `#FDBA74`, price: 3000 },
					{ id: 212, hex: `#FCA5A5`, price: 3000 },
					{ id: 213, hex: `#FDA4AF`, price: 3000 },
					{ id: 214, hex: `#D8B4FE`, price: 3000 },
					// Второй ряд
					{ id: 215, hex: `#C9C9C9`, price: 3000 },
					{ id: 216, hex: `#A8A29E`, price: 3000 },
					{ id: 217, hex: `#94A3B8`, price: 3000 },
					{ id: 218, hex: `#6366F1`, price: 3000 },
					{ id: 219, hex: `#3B82F6`, price: 3000 },
					{ id: 220, hex: `#06B6D4`, price: 3000 },
					{ id: 221, hex: `#14B8A6`, price: 3000 },
					{ id: 222, hex: `#22C55E`, price: 3000 },
					{ id: 223, hex: `#84CC16`, price: 3000 },
					{ id: 224, hex: `#EAB308`, price: 3000 },
					{ id: 225, hex: `#F97316`, price: 3000 },
					{ id: 226, hex: `#EF4444`, price: 3000 },
					{ id: 227, hex: `#F43F5E`, price: 3000 },
					{ id: 228, hex: `#A855F7`, price: 3000 },
					// Третий ряд
					{ id: 229, hex: `#858585`, price: 3000 },
					{ id: 230, hex: `#57534E`, price: 3000 },
					{ id: 231, hex: `#475569`, price: 3000 },
					{ id: 232, hex: `#4338CA`, price: 3000 },
					{ id: 233, hex: `#1D4ED8`, price: 3000 },
					{ id: 234, hex: `#0E7490`, price: 3000 },
					{ id: 235, hex: `#0F766E`, price: 3000 },
					{ id: 236, hex: `#15803D`, price: 3000 },
					{ id: 237, hex: `#4D7C0F`, price: 3000 },
					{ id: 238, hex: `#A16207`, price: 3000 },
					{ id: 239, hex: `#C2410C`, price: 3000 },
					{ id: 240, hex: `#AB1313`, price: 3000 },
					{ id: 241, hex: `#BE123C`, price: 3000 },
					{ id: 242, hex: `#9333EA`, price: 3000 },
					// Четвертый ряд
					{ id: 243, hex: `#404040`, price: 3000 },
					{ id: 244, hex: `#292524`, price: 3000 },
					{ id: 245, hex: `#1E293B`, price: 3000 },
					{ id: 246, hex: `#312E81`, price: 3000 },
					{ id: 247, hex: `#1E3A8A`, price: 3000 },
					{ id: 248, hex: `#164E63`, price: 3000 },
					{ id: 249, hex: `#134E4A`, price: 3000 },
					{ id: 250, hex: `#14532D`, price: 3000 },
					{ id: 251, hex: `#365314`, price: 3000 },
					{ id: 252, hex: `#713F12`, price: 3000 },
					{ id: 253, hex: `#7C2D12`, price: 3000 },
					{ id: 254, hex: `#8F1313`, price: 3000 },
					{ id: 255, hex: `#881337`, price: 3000 },
					{ id: 256, hex: `#6B21A8`, price: 3000 },
					// Пятый ряд
					{ id: 257, hex: `#000000`, price: 3000 },
					{ id: 258, hex: `#0C0A09`, price: 3000 },
					{ id: 259, hex: `#020617`, price: 3000 },
					{ id: 260, hex: `#1E1B4B`, price: 3000 },
					{ id: 261, hex: `#082F49`, price: 3000 },
					{ id: 262, hex: `#083344`, price: 3000 },
					{ id: 263, hex: `#042F2E`, price: 3000 },
					{ id: 264, hex: `#052E16`, price: 3000 },
					{ id: 265, hex: `#1A2E05`, price: 3000 },
					{ id: 266, hex: `#422006`, price: 3000 },
					{ id: 267, hex: `#431407`, price: 3000 },
					{ id: 268, hex: `#470707`, price: 3000 },
					{ id: 269, hex: `#4C0519`, price: 3000 },
					{ id: 270, hex: `#3B0764`, price: 3000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_3]: {
				vehPriceRate: 0.45,
				rgbColorPrice: 299,
				items: [
					// Первый ряд
					{ id: 301, hex: `#FFFFFF`, price: 3000 },
					{ id: 302, hex: `#E7E5E4`, price: 3000 },
					{ id: 303, hex: `#E2E8F0`, price: 3000 },
					{ id: 304, hex: `#A5B4FC`, price: 3000 },
					{ id: 305, hex: `#93C5FD`, price: 3000 },
					{ id: 306, hex: `#93C5FD`, price: 3000 },
					{ id: 307, hex: `#5EEAD4`, price: 3000 },
					{ id: 308, hex: `#86EFAC`, price: 3000 },
					{ id: 309, hex: `#BEF264`, price: 3000 },
					{ id: 310, hex: `#FDE047`, price: 3000 },
					{ id: 311, hex: `#FDBA74`, price: 3000 },
					{ id: 312, hex: `#FCA5A5`, price: 3000 },
					{ id: 313, hex: `#FDA4AF`, price: 3000 },
					{ id: 314, hex: `#D8B4FE`, price: 3000 },
					// Второй ряд
					{ id: 315, hex: `#C9C9C9`, price: 3000 },
					{ id: 316, hex: `#A8A29E`, price: 3000 },
					{ id: 317, hex: `#94A3B8`, price: 3000 },
					{ id: 318, hex: `#6366F1`, price: 3000 },
					{ id: 319, hex: `#3B82F6`, price: 3000 },
					{ id: 320, hex: `#06B6D4`, price: 3000 },
					{ id: 321, hex: `#14B8A6`, price: 3000 },
					{ id: 322, hex: `#22C55E`, price: 3000 },
					{ id: 323, hex: `#84CC16`, price: 3000 },
					{ id: 324, hex: `#EAB308`, price: 3000 },
					{ id: 325, hex: `#F97316`, price: 3000 },
					{ id: 326, hex: `#EF4444`, price: 3000 },
					{ id: 327, hex: `#F43F5E`, price: 3000 },
					{ id: 328, hex: `#A855F7`, price: 3000 },
					// Третий ряд
					{ id: 329, hex: `#858585`, price: 3000 },
					{ id: 330, hex: `#57534E`, price: 3000 },
					{ id: 331, hex: `#475569`, price: 3000 },
					{ id: 332, hex: `#4338CA`, price: 3000 },
					{ id: 333, hex: `#1D4ED8`, price: 3000 },
					{ id: 334, hex: `#0E7490`, price: 3000 },
					{ id: 335, hex: `#0F766E`, price: 3000 },
					{ id: 336, hex: `#15803D`, price: 3000 },
					{ id: 337, hex: `#4D7C0F`, price: 3000 },
					{ id: 338, hex: `#A16207`, price: 3000 },
					{ id: 339, hex: `#C2410C`, price: 3000 },
					{ id: 340, hex: `#AB1313`, price: 3000 },
					{ id: 341, hex: `#BE123C`, price: 3000 },
					{ id: 342, hex: `#9333EA`, price: 3000 },
					// Четвертый ряд
					{ id: 343, hex: `#404040`, price: 3000 },
					{ id: 344, hex: `#292524`, price: 3000 },
					{ id: 345, hex: `#1E293B`, price: 3000 },
					{ id: 346, hex: `#312E81`, price: 3000 },
					{ id: 347, hex: `#1E3A8A`, price: 3000 },
					{ id: 348, hex: `#164E63`, price: 3000 },
					{ id: 349, hex: `#134E4A`, price: 3000 },
					{ id: 350, hex: `#14532D`, price: 3000 },
					{ id: 351, hex: `#365314`, price: 3000 },
					{ id: 352, hex: `#713F12`, price: 3000 },
					{ id: 353, hex: `#7C2D12`, price: 3000 },
					{ id: 354, hex: `#8F1313`, price: 3000 },
					{ id: 355, hex: `#881337`, price: 3000 },
					{ id: 356, hex: `#6B21A8`, price: 3000 },
					// Пятый ряд
					{ id: 357, hex: `#000000`, price: 3000 },
					{ id: 358, hex: `#0C0A09`, price: 3000 },
					{ id: 359, hex: `#020617`, price: 3000 },
					{ id: 360, hex: `#1E1B4B`, price: 3000 },
					{ id: 361, hex: `#082F49`, price: 3000 },
					{ id: 362, hex: `#083344`, price: 3000 },
					{ id: 363, hex: `#042F2E`, price: 3000 },
					{ id: 364, hex: `#052E16`, price: 3000 },
					{ id: 365, hex: `#1A2E05`, price: 3000 },
					{ id: 366, hex: `#422006`, price: 3000 },
					{ id: 367, hex: `#431407`, price: 3000 },
					{ id: 368, hex: `#470707`, price: 3000 },
					{ id: 369, hex: `#4C0519`, price: 3000 },
					{ id: 370, hex: `#3B0764`, price: 3000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_4]: {
				vehPriceRate: 0.475,
				rgbColorPrice: 299,
				items: [
					// Первый ряд
					{ id: 401, hex: `#FFFFFF`, price: 3000 },
					{ id: 402, hex: `#E7E5E4`, price: 3000 },
					{ id: 403, hex: `#E2E8F0`, price: 3000 },
					{ id: 404, hex: `#A5B4FC`, price: 3000 },
					{ id: 405, hex: `#93C5FD`, price: 3000 },
					{ id: 406, hex: `#93C5FD`, price: 3000 },
					{ id: 407, hex: `#5EEAD4`, price: 3000 },
					{ id: 408, hex: `#86EFAC`, price: 3000 },
					{ id: 409, hex: `#BEF264`, price: 3000 },
					{ id: 410, hex: `#FDE047`, price: 3000 },
					{ id: 411, hex: `#FDBA74`, price: 3000 },
					{ id: 412, hex: `#FCA5A5`, price: 3000 },
					{ id: 413, hex: `#FDA4AF`, price: 3000 },
					{ id: 414, hex: `#D8B4FE`, price: 3000 },
					// Второй ряд
					{ id: 415, hex: `#C9C9C9`, price: 3000 },
					{ id: 416, hex: `#A8A29E`, price: 3000 },
					{ id: 417, hex: `#94A3B8`, price: 3000 },
					{ id: 418, hex: `#6366F1`, price: 3000 },
					{ id: 419, hex: `#3B82F6`, price: 3000 },
					{ id: 420, hex: `#06B6D4`, price: 3000 },
					{ id: 421, hex: `#14B8A6`, price: 3000 },
					{ id: 422, hex: `#22C55E`, price: 3000 },
					{ id: 423, hex: `#84CC16`, price: 3000 },
					{ id: 424, hex: `#EAB308`, price: 3000 },
					{ id: 425, hex: `#F97316`, price: 3000 },
					{ id: 426, hex: `#EF4444`, price: 3000 },
					{ id: 427, hex: `#F43F5E`, price: 3000 },
					{ id: 428, hex: `#A855F7`, price: 3000 },
					// Третий ряд
					{ id: 429, hex: `#858585`, price: 3000 },
					{ id: 430, hex: `#57534E`, price: 3000 },
					{ id: 431, hex: `#475569`, price: 3000 },
					{ id: 432, hex: `#4338CA`, price: 3000 },
					{ id: 433, hex: `#1D4ED8`, price: 3000 },
					{ id: 434, hex: `#0E7490`, price: 3000 },
					{ id: 435, hex: `#0F766E`, price: 3000 },
					{ id: 436, hex: `#15803D`, price: 3000 },
					{ id: 437, hex: `#4D7C0F`, price: 3000 },
					{ id: 438, hex: `#A16207`, price: 3000 },
					{ id: 439, hex: `#C2410C`, price: 3000 },
					{ id: 440, hex: `#AB1313`, price: 3000 },
					{ id: 441, hex: `#BE123C`, price: 3000 },
					{ id: 442, hex: `#9333EA`, price: 3000 },
					// Четвертый ряд
					{ id: 443, hex: `#404040`, price: 3000 },
					{ id: 444, hex: `#292524`, price: 3000 },
					{ id: 445, hex: `#1E293B`, price: 3000 },
					{ id: 446, hex: `#312E81`, price: 3000 },
					{ id: 447, hex: `#1E3A8A`, price: 3000 },
					{ id: 448, hex: `#164E63`, price: 3000 },
					{ id: 449, hex: `#134E4A`, price: 3000 },
					{ id: 450, hex: `#14532D`, price: 3000 },
					{ id: 451, hex: `#365314`, price: 3000 },
					{ id: 452, hex: `#713F12`, price: 3000 },
					{ id: 453, hex: `#7C2D12`, price: 3000 },
					{ id: 454, hex: `#8F1313`, price: 3000 },
					{ id: 455, hex: `#881337`, price: 3000 },
					{ id: 456, hex: `#6B21A8`, price: 3000 },
					// Пятый ряд
					{ id: 457, hex: `#000000`, price: 3000 },
					{ id: 458, hex: `#0C0A09`, price: 3000 },
					{ id: 459, hex: `#020617`, price: 3000 },
					{ id: 460, hex: `#1E1B4B`, price: 3000 },
					{ id: 461, hex: `#082F49`, price: 3000 },
					{ id: 462, hex: `#083344`, price: 3000 },
					{ id: 463, hex: `#042F2E`, price: 3000 },
					{ id: 464, hex: `#052E16`, price: 3000 },
					{ id: 465, hex: `#1A2E05`, price: 3000 },
					{ id: 466, hex: `#422006`, price: 3000 },
					{ id: 467, hex: `#431407`, price: 3000 },
					{ id: 468, hex: `#470707`, price: 3000 },
					{ id: 469, hex: `#4C0519`, price: 3000 },
					{ id: 470, hex: `#3B0764`, price: 3000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_5]: {
				vehPriceRate: 0.5,
				rgbColorPrice: 399,
				items: [
					// Первый ряд
					{ id: 501, hex: `#FFFFFF`, price: 10000 },
					{ id: 502, hex: `#E7E5E4`, price: 10000 },
					{ id: 503, hex: `#E2E8F0`, price: 10000 },
					{ id: 504, hex: `#A5B4FC`, price: 10000 },
					{ id: 505, hex: `#93C5FD`, price: 10000 },
					{ id: 506, hex: `#93C5FD`, price: 10000 },
					{ id: 507, hex: `#5EEAD4`, price: 10000 },
					{ id: 508, hex: `#86EFAC`, price: 10000 },
					{ id: 509, hex: `#BEF264`, price: 10000 },
					{ id: 510, hex: `#FDE047`, price: 10000 },
					{ id: 511, hex: `#FDBA74`, price: 10000 },
					{ id: 512, hex: `#FCA5A5`, price: 10000 },
					{ id: 513, hex: `#FDA4AF`, price: 10000 },
					{ id: 514, hex: `#D8B4FE`, price: 10000 },
					// Второй ряд
					{ id: 515, hex: `#C9C9C9`, price: 10000 },
					{ id: 516, hex: `#A8A29E`, price: 10000 },
					{ id: 517, hex: `#94A3B8`, price: 10000 },
					{ id: 518, hex: `#6366F1`, price: 10000 },
					{ id: 519, hex: `#3B82F6`, price: 10000 },
					{ id: 520, hex: `#06B6D4`, price: 10000 },
					{ id: 521, hex: `#14B8A6`, price: 10000 },
					{ id: 522, hex: `#22C55E`, price: 10000 },
					{ id: 523, hex: `#84CC16`, price: 10000 },
					{ id: 524, hex: `#EAB308`, price: 10000 },
					{ id: 525, hex: `#F97316`, price: 10000 },
					{ id: 526, hex: `#EF4444`, price: 10000 },
					{ id: 527, hex: `#F43F5E`, price: 10000 },
					{ id: 528, hex: `#A855F7`, price: 10000 },
					// Третий ряд
					{ id: 529, hex: `#858585`, price: 10000 },
					{ id: 530, hex: `#57534E`, price: 10000 },
					{ id: 531, hex: `#475569`, price: 10000 },
					{ id: 532, hex: `#4338CA`, price: 10000 },
					{ id: 533, hex: `#1D4ED8`, price: 10000 },
					{ id: 534, hex: `#0E7490`, price: 10000 },
					{ id: 535, hex: `#0F766E`, price: 10000 },
					{ id: 536, hex: `#15803D`, price: 10000 },
					{ id: 537, hex: `#4D7C0F`, price: 10000 },
					{ id: 538, hex: `#A16207`, price: 10000 },
					{ id: 539, hex: `#C2410C`, price: 10000 },
					{ id: 540, hex: `#AB1313`, price: 10000 },
					{ id: 541, hex: `#BE123C`, price: 10000 },
					{ id: 542, hex: `#9333EA`, price: 10000 },
					// Четвертый ряд
					{ id: 543, hex: `#404040`, price: 10000 },
					{ id: 544, hex: `#292524`, price: 10000 },
					{ id: 545, hex: `#1E293B`, price: 10000 },
					{ id: 546, hex: `#312E81`, price: 10000 },
					{ id: 547, hex: `#1E3A8A`, price: 10000 },
					{ id: 548, hex: `#164E63`, price: 10000 },
					{ id: 549, hex: `#134E4A`, price: 10000 },
					{ id: 550, hex: `#14532D`, price: 10000 },
					{ id: 551, hex: `#365314`, price: 10000 },
					{ id: 552, hex: `#713F12`, price: 10000 },
					{ id: 553, hex: `#7C2D12`, price: 10000 },
					{ id: 554, hex: `#8F1313`, price: 10000 },
					{ id: 555, hex: `#881337`, price: 10000 },
					{ id: 556, hex: `#6B21A8`, price: 10000 },
					// Пятый ряд
					{ id: 557, hex: `#000000`, price: 10000 },
					{ id: 558, hex: `#0C0A09`, price: 10000 },
					{ id: 559, hex: `#020617`, price: 10000 },
					{ id: 560, hex: `#1E1B4B`, price: 10000 },
					{ id: 561, hex: `#082F49`, price: 10000 },
					{ id: 562, hex: `#083344`, price: 10000 },
					{ id: 563, hex: `#042F2E`, price: 10000 },
					{ id: 564, hex: `#052E16`, price: 10000 },
					{ id: 565, hex: `#1A2E05`, price: 10000 },
					{ id: 566, hex: `#422006`, price: 10000 },
					{ id: 567, hex: `#431407`, price: 10000 },
					{ id: 568, hex: `#470707`, price: 10000 },
					{ id: 569, hex: `#4C0519`, price: 10000 },
					{ id: 570, hex: `#3B0764`, price: 10000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_6]: {
				vehPriceRate: 0.7,
				rgbColorPrice: 499,
				items: [
					// Первый ряд
					{ id: 601, hex: `#FFFFFF`, price: 15000 },
					{ id: 602, hex: `#E7E5E4`, price: 15000 },
					{ id: 603, hex: `#E2E8F0`, price: 15000 },
					{ id: 604, hex: `#A5B4FC`, price: 15000 },
					{ id: 605, hex: `#93C5FD`, price: 15000 },
					{ id: 606, hex: `#93C5FD`, price: 15000 },
					{ id: 607, hex: `#5EEAD4`, price: 15000 },
					{ id: 608, hex: `#86EFAC`, price: 15000 },
					{ id: 609, hex: `#BEF264`, price: 15000 },
					{ id: 610, hex: `#FDE047`, price: 15000 },
					{ id: 611, hex: `#FDBA74`, price: 15000 },
					{ id: 612, hex: `#FCA5A5`, price: 15000 },
					{ id: 613, hex: `#FDA4AF`, price: 15000 },
					{ id: 614, hex: `#D8B4FE`, price: 15000 },
					// Второй ряд
					{ id: 615, hex: `#C9C9C9`, price: 15000 },
					{ id: 616, hex: `#A8A29E`, price: 15000 },
					{ id: 617, hex: `#94A3B8`, price: 15000 },
					{ id: 618, hex: `#6366F1`, price: 15000 },
					{ id: 619, hex: `#3B82F6`, price: 15000 },
					{ id: 620, hex: `#06B6D4`, price: 15000 },
					{ id: 621, hex: `#14B8A6`, price: 15000 },
					{ id: 622, hex: `#22C55E`, price: 15000 },
					{ id: 623, hex: `#84CC16`, price: 15000 },
					{ id: 624, hex: `#EAB308`, price: 15000 },
					{ id: 625, hex: `#F97316`, price: 15000 },
					{ id: 626, hex: `#EF4444`, price: 15000 },
					{ id: 627, hex: `#F43F5E`, price: 15000 },
					{ id: 628, hex: `#A855F7`, price: 15000 },
					// Третий ряд
					{ id: 629, hex: `#858585`, price: 15000 },
					{ id: 630, hex: `#57534E`, price: 15000 },
					{ id: 631, hex: `#475569`, price: 15000 },
					{ id: 632, hex: `#4338CA`, price: 15000 },
					{ id: 633, hex: `#1D4ED8`, price: 15000 },
					{ id: 634, hex: `#0E7490`, price: 15000 },
					{ id: 635, hex: `#0F766E`, price: 15000 },
					{ id: 636, hex: `#15803D`, price: 15000 },
					{ id: 637, hex: `#4D7C0F`, price: 15000 },
					{ id: 638, hex: `#A16207`, price: 15000 },
					{ id: 639, hex: `#C2410C`, price: 15000 },
					{ id: 640, hex: `#AB1313`, price: 15000 },
					{ id: 641, hex: `#BE123C`, price: 15000 },
					{ id: 642, hex: `#9333EA`, price: 15000 },
					// Четвертый ряд
					{ id: 643, hex: `#404040`, price: 15000 },
					{ id: 644, hex: `#292524`, price: 15000 },
					{ id: 645, hex: `#1E293B`, price: 15000 },
					{ id: 646, hex: `#312E81`, price: 15000 },
					{ id: 647, hex: `#1E3A8A`, price: 15000 },
					{ id: 648, hex: `#164E63`, price: 15000 },
					{ id: 649, hex: `#134E4A`, price: 15000 },
					{ id: 650, hex: `#14532D`, price: 15000 },
					{ id: 651, hex: `#365314`, price: 15000 },
					{ id: 652, hex: `#713F12`, price: 15000 },
					{ id: 653, hex: `#7C2D12`, price: 15000 },
					{ id: 654, hex: `#8F1313`, price: 15000 },
					{ id: 655, hex: `#881337`, price: 15000 },
					{ id: 656, hex: `#6B21A8`, price: 15000 },
					// Пятый ряд
					{ id: 657, hex: `#000000`, price: 15000 },
					{ id: 658, hex: `#0C0A09`, price: 15000 },
					{ id: 659, hex: `#020617`, price: 15000 },
					{ id: 660, hex: `#1E1B4B`, price: 15000 },
					{ id: 661, hex: `#082F49`, price: 15000 },
					{ id: 662, hex: `#083344`, price: 15000 },
					{ id: 663, hex: `#042F2E`, price: 15000 },
					{ id: 664, hex: `#052E16`, price: 15000 },
					{ id: 665, hex: `#1A2E05`, price: 15000 },
					{ id: 666, hex: `#422006`, price: 15000 },
					{ id: 667, hex: `#431407`, price: 15000 },
					{ id: 668, hex: `#470707`, price: 15000 },
					{ id: 669, hex: `#4C0519`, price: 15000 },
					{ id: 670, hex: `#3B0764`, price: 15000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_7]: {
				vehPriceRate: 0.9,
				rgbColorPrice: 599,
				items: [
					// Первый ряд
					{ id: 701, hex: `#FFFFFF`, price: 20000 },
					{ id: 702, hex: `#E7E5E4`, price: 20000 },
					{ id: 703, hex: `#E2E8F0`, price: 20000 },
					{ id: 704, hex: `#A5B4FC`, price: 20000 },
					{ id: 705, hex: `#93C5FD`, price: 20000 },
					{ id: 706, hex: `#93C5FD`, price: 20000 },
					{ id: 707, hex: `#5EEAD4`, price: 20000 },
					{ id: 708, hex: `#86EFAC`, price: 20000 },
					{ id: 709, hex: `#BEF264`, price: 20000 },
					{ id: 710, hex: `#FDE047`, price: 20000 },
					{ id: 711, hex: `#FDBA74`, price: 20000 },
					{ id: 712, hex: `#FCA5A5`, price: 20000 },
					{ id: 713, hex: `#FDA4AF`, price: 20000 },
					{ id: 714, hex: `#D8B4FE`, price: 20000 },
					// Второй ряд
					{ id: 715, hex: `#C9C9C9`, price: 20000 },
					{ id: 716, hex: `#A8A29E`, price: 20000 },
					{ id: 717, hex: `#94A3B8`, price: 20000 },
					{ id: 718, hex: `#6366F1`, price: 20000 },
					{ id: 719, hex: `#3B82F6`, price: 20000 },
					{ id: 720, hex: `#06B6D4`, price: 20000 },
					{ id: 721, hex: `#14B8A6`, price: 20000 },
					{ id: 722, hex: `#22C55E`, price: 20000 },
					{ id: 723, hex: `#84CC16`, price: 20000 },
					{ id: 724, hex: `#EAB308`, price: 20000 },
					{ id: 725, hex: `#F97316`, price: 20000 },
					{ id: 726, hex: `#EF4444`, price: 20000 },
					{ id: 727, hex: `#F43F5E`, price: 20000 },
					{ id: 728, hex: `#A855F7`, price: 20000 },
					// Третий ряд
					{ id: 729, hex: `#858585`, price: 20000 },
					{ id: 730, hex: `#57534E`, price: 20000 },
					{ id: 731, hex: `#475569`, price: 20000 },
					{ id: 732, hex: `#4338CA`, price: 20000 },
					{ id: 733, hex: `#1D4ED8`, price: 20000 },
					{ id: 734, hex: `#0E7490`, price: 20000 },
					{ id: 735, hex: `#0F766E`, price: 20000 },
					{ id: 736, hex: `#15803D`, price: 20000 },
					{ id: 737, hex: `#4D7C0F`, price: 20000 },
					{ id: 738, hex: `#A16207`, price: 20000 },
					{ id: 739, hex: `#C2410C`, price: 20000 },
					{ id: 740, hex: `#AB1313`, price: 20000 },
					{ id: 741, hex: `#BE123C`, price: 20000 },
					{ id: 742, hex: `#9333EA`, price: 20000 },
					// Четвертый ряд
					{ id: 743, hex: `#404040`, price: 20000 },
					{ id: 744, hex: `#292524`, price: 20000 },
					{ id: 745, hex: `#1E293B`, price: 20000 },
					{ id: 746, hex: `#312E81`, price: 20000 },
					{ id: 747, hex: `#1E3A8A`, price: 20000 },
					{ id: 748, hex: `#164E63`, price: 20000 },
					{ id: 749, hex: `#134E4A`, price: 20000 },
					{ id: 750, hex: `#14532D`, price: 20000 },
					{ id: 751, hex: `#365314`, price: 20000 },
					{ id: 752, hex: `#713F12`, price: 20000 },
					{ id: 753, hex: `#7C2D12`, price: 20000 },
					{ id: 754, hex: `#8F1313`, price: 20000 },
					{ id: 755, hex: `#881337`, price: 20000 },
					{ id: 756, hex: `#6B21A8`, price: 20000 },
					// Пятый ряд
					{ id: 757, hex: `#000000`, price: 20000 },
					{ id: 758, hex: `#0C0A09`, price: 20000 },
					{ id: 759, hex: `#020617`, price: 20000 },
					{ id: 760, hex: `#1E1B4B`, price: 20000 },
					{ id: 761, hex: `#082F49`, price: 20000 },
					{ id: 762, hex: `#083344`, price: 20000 },
					{ id: 763, hex: `#042F2E`, price: 20000 },
					{ id: 764, hex: `#052E16`, price: 20000 },
					{ id: 765, hex: `#1A2E05`, price: 20000 },
					{ id: 766, hex: `#422006`, price: 20000 },
					{ id: 767, hex: `#431407`, price: 20000 },
					{ id: 768, hex: `#470707`, price: 20000 },
					{ id: 769, hex: `#4C0519`, price: 20000 },
					{ id: 770, hex: `#3B0764`, price: 20000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_8]: {
				vehPriceRate: 1.1,
				rgbColorPrice: 699,
				items: [
					// Первый ряд
					{ id: 801, hex: `#FFFFFF`, price: 30000 },
					{ id: 802, hex: `#E7E5E4`, price: 30000 },
					{ id: 803, hex: `#E2E8F0`, price: 30000 },
					{ id: 804, hex: `#A5B4FC`, price: 30000 },
					{ id: 805, hex: `#93C5FD`, price: 30000 },
					{ id: 806, hex: `#93C5FD`, price: 30000 },
					{ id: 807, hex: `#5EEAD4`, price: 30000 },
					{ id: 808, hex: `#86EFAC`, price: 30000 },
					{ id: 809, hex: `#BEF264`, price: 30000 },
					{ id: 810, hex: `#FDE047`, price: 30000 },
					{ id: 811, hex: `#FDBA74`, price: 30000 },
					{ id: 812, hex: `#FCA5A5`, price: 30000 },
					{ id: 813, hex: `#FDA4AF`, price: 30000 },
					{ id: 814, hex: `#D8B4FE`, price: 30000 },
					// Второй ряд
					{ id: 815, hex: `#C9C9C9`, price: 30000 },
					{ id: 816, hex: `#A8A29E`, price: 30000 },
					{ id: 817, hex: `#94A3B8`, price: 30000 },
					{ id: 818, hex: `#6366F1`, price: 30000 },
					{ id: 819, hex: `#3B82F6`, price: 30000 },
					{ id: 820, hex: `#06B6D4`, price: 30000 },
					{ id: 821, hex: `#14B8A6`, price: 30000 },
					{ id: 822, hex: `#22C55E`, price: 30000 },
					{ id: 823, hex: `#84CC16`, price: 30000 },
					{ id: 824, hex: `#EAB308`, price: 30000 },
					{ id: 825, hex: `#F97316`, price: 30000 },
					{ id: 826, hex: `#EF4444`, price: 30000 },
					{ id: 827, hex: `#F43F5E`, price: 30000 },
					{ id: 828, hex: `#A855F7`, price: 30000 },
					// Третий ряд
					{ id: 829, hex: `#858585`, price: 30000 },
					{ id: 830, hex: `#57534E`, price: 30000 },
					{ id: 831, hex: `#475569`, price: 30000 },
					{ id: 832, hex: `#4338CA`, price: 30000 },
					{ id: 833, hex: `#1D4ED8`, price: 30000 },
					{ id: 834, hex: `#0E7490`, price: 30000 },
					{ id: 835, hex: `#0F766E`, price: 30000 },
					{ id: 836, hex: `#15803D`, price: 30000 },
					{ id: 837, hex: `#4D7C0F`, price: 30000 },
					{ id: 838, hex: `#A16207`, price: 30000 },
					{ id: 839, hex: `#C2410C`, price: 30000 },
					{ id: 840, hex: `#AB1313`, price: 30000 },
					{ id: 841, hex: `#BE123C`, price: 30000 },
					{ id: 842, hex: `#9333EA`, price: 30000 },
					// Четвертый ряд
					{ id: 843, hex: `#404040`, price: 30000 },
					{ id: 844, hex: `#292524`, price: 30000 },
					{ id: 845, hex: `#1E293B`, price: 30000 },
					{ id: 846, hex: `#312E81`, price: 30000 },
					{ id: 847, hex: `#1E3A8A`, price: 30000 },
					{ id: 848, hex: `#164E63`, price: 30000 },
					{ id: 849, hex: `#134E4A`, price: 30000 },
					{ id: 850, hex: `#14532D`, price: 30000 },
					{ id: 851, hex: `#365314`, price: 30000 },
					{ id: 852, hex: `#713F12`, price: 30000 },
					{ id: 853, hex: `#7C2D12`, price: 30000 },
					{ id: 854, hex: `#8F1313`, price: 30000 },
					{ id: 855, hex: `#881337`, price: 30000 },
					{ id: 856, hex: `#6B21A8`, price: 30000 },
					// Пятый ряд
					{ id: 857, hex: `#000000`, price: 30000 },
					{ id: 858, hex: `#0C0A09`, price: 30000 },
					{ id: 859, hex: `#020617`, price: 30000 },
					{ id: 860, hex: `#1E1B4B`, price: 30000 },
					{ id: 861, hex: `#082F49`, price: 30000 },
					{ id: 862, hex: `#083344`, price: 30000 },
					{ id: 863, hex: `#042F2E`, price: 30000 },
					{ id: 864, hex: `#052E16`, price: 30000 },
					{ id: 865, hex: `#1A2E05`, price: 30000 },
					{ id: 866, hex: `#422006`, price: 30000 },
					{ id: 867, hex: `#431407`, price: 30000 },
					{ id: 868, hex: `#470707`, price: 30000 },
					{ id: 869, hex: `#4C0519`, price: 30000 },
					{ id: 870, hex: `#3B0764`, price: 30000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_9]: {
				vehPriceRate: 1.3,
				rgbColorPrice: 849,
				items: [
					// Первый ряд
					{ id: 901, hex: `#FFFFFF`, price: 45000 },
					{ id: 902, hex: `#E7E5E4`, price: 45000 },
					{ id: 903, hex: `#E2E8F0`, price: 45000 },
					{ id: 904, hex: `#A5B4FC`, price: 45000 },
					{ id: 905, hex: `#93C5FD`, price: 45000 },
					{ id: 906, hex: `#93C5FD`, price: 45000 },
					{ id: 907, hex: `#5EEAD4`, price: 45000 },
					{ id: 908, hex: `#86EFAC`, price: 45000 },
					{ id: 909, hex: `#BEF264`, price: 45000 },
					{ id: 910, hex: `#FDE047`, price: 45000 },
					{ id: 911, hex: `#FDBA74`, price: 45000 },
					{ id: 912, hex: `#FCA5A5`, price: 45000 },
					{ id: 913, hex: `#FDA4AF`, price: 45000 },
					{ id: 914, hex: `#D8B4FE`, price: 45000 },
					// Второй ряд
					{ id: 915, hex: `#C9C9C9`, price: 45000 },
					{ id: 916, hex: `#A8A29E`, price: 45000 },
					{ id: 917, hex: `#94A3B8`, price: 45000 },
					{ id: 918, hex: `#6366F1`, price: 45000 },
					{ id: 919, hex: `#3B82F6`, price: 45000 },
					{ id: 920, hex: `#06B6D4`, price: 45000 },
					{ id: 921, hex: `#14B8A6`, price: 45000 },
					{ id: 922, hex: `#22C55E`, price: 45000 },
					{ id: 923, hex: `#84CC16`, price: 45000 },
					{ id: 924, hex: `#EAB308`, price: 45000 },
					{ id: 925, hex: `#F97316`, price: 45000 },
					{ id: 926, hex: `#EF4444`, price: 45000 },
					{ id: 927, hex: `#F43F5E`, price: 45000 },
					{ id: 928, hex: `#A855F7`, price: 45000 },
					// Третий ряд
					{ id: 929, hex: `#858585`, price: 45000 },
					{ id: 930, hex: `#57534E`, price: 45000 },
					{ id: 931, hex: `#475569`, price: 45000 },
					{ id: 932, hex: `#4338CA`, price: 45000 },
					{ id: 933, hex: `#1D4ED8`, price: 45000 },
					{ id: 934, hex: `#0E7490`, price: 45000 },
					{ id: 935, hex: `#0F766E`, price: 45000 },
					{ id: 936, hex: `#15803D`, price: 45000 },
					{ id: 937, hex: `#4D7C0F`, price: 45000 },
					{ id: 938, hex: `#A16207`, price: 45000 },
					{ id: 939, hex: `#C2410C`, price: 45000 },
					{ id: 940, hex: `#AB1313`, price: 45000 },
					{ id: 941, hex: `#BE123C`, price: 45000 },
					{ id: 942, hex: `#9333EA`, price: 45000 },
					// Четвертый ряд
					{ id: 943, hex: `#404040`, price: 45000 },
					{ id: 944, hex: `#292524`, price: 45000 },
					{ id: 945, hex: `#1E293B`, price: 45000 },
					{ id: 946, hex: `#312E81`, price: 45000 },
					{ id: 947, hex: `#1E3A8A`, price: 45000 },
					{ id: 948, hex: `#164E63`, price: 45000 },
					{ id: 949, hex: `#134E4A`, price: 45000 },
					{ id: 950, hex: `#14532D`, price: 45000 },
					{ id: 951, hex: `#365314`, price: 45000 },
					{ id: 952, hex: `#713F12`, price: 45000 },
					{ id: 953, hex: `#7C2D12`, price: 45000 },
					{ id: 954, hex: `#8F1313`, price: 45000 },
					{ id: 955, hex: `#881337`, price: 45000 },
					{ id: 956, hex: `#6B21A8`, price: 45000 },
					// Пятый ряд
					{ id: 957, hex: `#000000`, price: 45000 },
					{ id: 958, hex: `#0C0A09`, price: 45000 },
					{ id: 959, hex: `#020617`, price: 45000 },
					{ id: 960, hex: `#1E1B4B`, price: 45000 },
					{ id: 961, hex: `#082F49`, price: 45000 },
					{ id: 962, hex: `#083344`, price: 45000 },
					{ id: 963, hex: `#042F2E`, price: 45000 },
					{ id: 964, hex: `#052E16`, price: 45000 },
					{ id: 965, hex: `#1A2E05`, price: 45000 },
					{ id: 966, hex: `#422006`, price: 45000 },
					{ id: 967, hex: `#431407`, price: 45000 },
					{ id: 968, hex: `#470707`, price: 45000 },
					{ id: 969, hex: `#4C0519`, price: 45000 },
					{ id: 970, hex: `#3B0764`, price: 45000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_10]: {
				vehPriceRate: 1.5,
				rgbColorPrice: 999,
				items: [
					// Первый ряд
					{ id: 1001, hex: `#FFFFFF`, price: 60000 },
					{ id: 1002, hex: `#E7E5E4`, price: 60000 },
					{ id: 1003, hex: `#E2E8F0`, price: 60000 },
					{ id: 1004, hex: `#A5B4FC`, price: 60000 },
					{ id: 1005, hex: `#93C5FD`, price: 60000 },
					{ id: 1006, hex: `#93C5FD`, price: 60000 },
					{ id: 1007, hex: `#5EEAD4`, price: 60000 },
					{ id: 1008, hex: `#86EFAC`, price: 60000 },
					{ id: 1009, hex: `#BEF264`, price: 60000 },
					{ id: 1010, hex: `#FDE047`, price: 60000 },
					{ id: 1011, hex: `#FDBA74`, price: 60000 },
					{ id: 1012, hex: `#FCA5A5`, price: 60000 },
					{ id: 1013, hex: `#FDA4AF`, price: 60000 },
					{ id: 1014, hex: `#D8B4FE`, price: 60000 },
					// Второй ряд
					{ id: 1015, hex: `#C9C9C9`, price: 60000 },
					{ id: 1016, hex: `#A8A29E`, price: 60000 },
					{ id: 1017, hex: `#94A3B8`, price: 60000 },
					{ id: 1018, hex: `#6366F1`, price: 60000 },
					{ id: 1019, hex: `#3B82F6`, price: 60000 },
					{ id: 1020, hex: `#06B6D4`, price: 60000 },
					{ id: 1021, hex: `#14B8A6`, price: 60000 },
					{ id: 1022, hex: `#22C55E`, price: 60000 },
					{ id: 1023, hex: `#84CC16`, price: 60000 },
					{ id: 1024, hex: `#EAB308`, price: 60000 },
					{ id: 1025, hex: `#F97316`, price: 60000 },
					{ id: 1026, hex: `#EF4444`, price: 60000 },
					{ id: 1027, hex: `#F43F5E`, price: 60000 },
					{ id: 1028, hex: `#A855F7`, price: 60000 },
					// Третий ряд
					{ id: 1029, hex: `#858585`, price: 60000 },
					{ id: 1030, hex: `#57534E`, price: 60000 },
					{ id: 1031, hex: `#475569`, price: 60000 },
					{ id: 1032, hex: `#4338CA`, price: 60000 },
					{ id: 1033, hex: `#1D4ED8`, price: 60000 },
					{ id: 1034, hex: `#0E7490`, price: 60000 },
					{ id: 1035, hex: `#0F766E`, price: 60000 },
					{ id: 1036, hex: `#15803D`, price: 60000 },
					{ id: 1037, hex: `#4D7C0F`, price: 60000 },
					{ id: 1038, hex: `#A16207`, price: 60000 },
					{ id: 1039, hex: `#C2410C`, price: 60000 },
					{ id: 1040, hex: `#AB1313`, price: 60000 },
					{ id: 1041, hex: `#BE123C`, price: 60000 },
					{ id: 1042, hex: `#9333EA`, price: 60000 },
					// Четвертый ряд
					{ id: 1043, hex: `#404040`, price: 60000 },
					{ id: 1044, hex: `#292524`, price: 60000 },
					{ id: 1045, hex: `#1E293B`, price: 60000 },
					{ id: 1046, hex: `#312E81`, price: 60000 },
					{ id: 1047, hex: `#1E3A8A`, price: 60000 },
					{ id: 1048, hex: `#164E63`, price: 60000 },
					{ id: 1049, hex: `#134E4A`, price: 60000 },
					{ id: 1050, hex: `#14532D`, price: 60000 },
					{ id: 1051, hex: `#365314`, price: 60000 },
					{ id: 1052, hex: `#713F12`, price: 60000 },
					{ id: 1053, hex: `#7C2D12`, price: 60000 },
					{ id: 1054, hex: `#8F1313`, price: 60000 },
					{ id: 1055, hex: `#881337`, price: 60000 },
					{ id: 1056, hex: `#6B21A8`, price: 60000 },
					// Пятый ряд
					{ id: 1057, hex: `#000000`, price: 60000 },
					{ id: 1058, hex: `#0C0A09`, price: 60000 },
					{ id: 1059, hex: `#020617`, price: 60000 },
					{ id: 1060, hex: `#1E1B4B`, price: 60000 },
					{ id: 1061, hex: `#082F49`, price: 60000 },
					{ id: 1062, hex: `#083344`, price: 60000 },
					{ id: 1063, hex: `#042F2E`, price: 60000 },
					{ id: 1064, hex: `#052E16`, price: 60000 },
					{ id: 1065, hex: `#1A2E05`, price: 60000 },
					{ id: 1066, hex: `#422006`, price: 60000 },
					{ id: 1067, hex: `#431407`, price: 60000 },
					{ id: 1068, hex: `#470707`, price: 60000 },
					{ id: 1069, hex: `#4C0519`, price: 60000 },
					{ id: 1070, hex: `#3B0764`, price: 60000 },
				],
			},
		},
	},
	{
		action: ColorType.VMT_PAINT2,
		title: 'vehicleTuning.colorMenu.VMT_PAINT2',
		eventName: 'client.tuning.setColor',
		colorMod: {
			[ColorModType.VMT_PAINT_MATERIAL_1]: {
				vehPriceRate: 0.2,
				rgbColorPrice: 149,
				items: [
					// Первый ряд
					{ id: 101, hex: `#FFFFFF`, price: 1500 },
					{ id: 102, hex: `#E7E5E4`, price: 1500 },
					{ id: 103, hex: `#E2E8F0`, price: 1500 },
					{ id: 104, hex: `#A5B4FC`, price: 1500 },
					{ id: 105, hex: `#93C5FD`, price: 1500 },
					{ id: 106, hex: `#93C5FD`, price: 1500 },
					{ id: 107, hex: `#5EEAD4`, price: 1500 },
					{ id: 108, hex: `#86EFAC`, price: 1500 },
					{ id: 109, hex: `#BEF264`, price: 1500 },
					{ id: 110, hex: `#FDE047`, price: 1500 },
					{ id: 111, hex: `#FDBA74`, price: 1500 },
					{ id: 112, hex: `#FCA5A5`, price: 1500 },
					{ id: 113, hex: `#FDA4AF`, price: 1500 },
					{ id: 114, hex: `#D8B4FE`, price: 1500 },
					// Второй ряд
					{ id: 115, hex: `#C9C9C9`, price: 1500 },
					{ id: 116, hex: `#A8A29E`, price: 1500 },
					{ id: 117, hex: `#94A3B8`, price: 1500 },
					{ id: 118, hex: `#6366F1`, price: 1500 },
					{ id: 119, hex: `#3B82F6`, price: 1500 },
					{ id: 120, hex: `#06B6D4`, price: 1500 },
					{ id: 121, hex: `#14B8A6`, price: 1500 },
					{ id: 122, hex: `#22C55E`, price: 1500 },
					{ id: 123, hex: `#84CC16`, price: 1500 },
					{ id: 124, hex: `#EAB308`, price: 1500 },
					{ id: 125, hex: `#F97316`, price: 1500 },
					{ id: 126, hex: `#EF4444`, price: 1500 },
					{ id: 127, hex: `#F43F5E`, price: 1500 },
					{ id: 128, hex: `#A855F7`, price: 1500 },
					// Третий ряд
					{ id: 129, hex: `#858585`, price: 1500 },
					{ id: 130, hex: `#57534E`, price: 1500 },
					{ id: 131, hex: `#475569`, price: 1500 },
					{ id: 132, hex: `#4338CA`, price: 1500 },
					{ id: 133, hex: `#1D4ED8`, price: 1500 },
					{ id: 134, hex: `#0E7490`, price: 1500 },
					{ id: 135, hex: `#0F766E`, price: 1500 },
					{ id: 136, hex: `#15803D`, price: 1500 },
					{ id: 137, hex: `#4D7C0F`, price: 1500 },
					{ id: 138, hex: `#A16207`, price: 1500 },
					{ id: 139, hex: `#C2410C`, price: 1500 },
					{ id: 140, hex: `#AB1313`, price: 1500 },
					{ id: 141, hex: `#BE123C`, price: 1500 },
					{ id: 142, hex: `#9333EA`, price: 1500 },
					// Четвертый ряд
					{ id: 143, hex: `#404040`, price: 1500 },
					{ id: 144, hex: `#292524`, price: 1500 },
					{ id: 145, hex: `#1E293B`, price: 1500 },
					{ id: 146, hex: `#312E81`, price: 1500 },
					{ id: 147, hex: `#1E3A8A`, price: 1500 },
					{ id: 148, hex: `#164E63`, price: 1500 },
					{ id: 149, hex: `#134E4A`, price: 1500 },
					{ id: 150, hex: `#14532D`, price: 1500 },
					{ id: 151, hex: `#365314`, price: 1500 },
					{ id: 152, hex: `#713F12`, price: 1500 },
					{ id: 153, hex: `#7C2D12`, price: 1500 },
					{ id: 154, hex: `#8F1313`, price: 1500 },
					{ id: 155, hex: `#881337`, price: 1500 },
					{ id: 156, hex: `#6B21A8`, price: 1500 },
					// Пятый ряд
					{ id: 157, hex: `#000000`, price: 1500 },
					{ id: 158, hex: `#0C0A09`, price: 1500 },
					{ id: 159, hex: `#020617`, price: 1500 },
					{ id: 160, hex: `#1E1B4B`, price: 1500 },
					{ id: 161, hex: `#082F49`, price: 1500 },
					{ id: 162, hex: `#083344`, price: 1500 },
					{ id: 163, hex: `#042F2E`, price: 1500 },
					{ id: 164, hex: `#052E16`, price: 1500 },
					{ id: 165, hex: `#1A2E05`, price: 1500 },
					{ id: 166, hex: `#422006`, price: 1500 },
					{ id: 167, hex: `#431407`, price: 1500 },
					{ id: 168, hex: `#470707`, price: 1500 },
					{ id: 169, hex: `#4C0519`, price: 1500 },
					{ id: 170, hex: `#3B0764`, price: 1500 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_2]: {
				vehPriceRate: 0.2175,
				rgbColorPrice: 149,
				items: [
					// Первый ряд
					{ id: 201, hex: `#FFFFFF`, price: 1500 },
					{ id: 202, hex: `#E7E5E4`, price: 1500 },
					{ id: 203, hex: `#E2E8F0`, price: 1500 },
					{ id: 204, hex: `#A5B4FC`, price: 1500 },
					{ id: 205, hex: `#93C5FD`, price: 1500 },
					{ id: 206, hex: `#93C5FD`, price: 1500 },
					{ id: 207, hex: `#5EEAD4`, price: 1500 },
					{ id: 208, hex: `#86EFAC`, price: 1500 },
					{ id: 209, hex: `#BEF264`, price: 1500 },
					{ id: 210, hex: `#FDE047`, price: 1500 },
					{ id: 211, hex: `#FDBA74`, price: 1500 },
					{ id: 212, hex: `#FCA5A5`, price: 1500 },
					{ id: 213, hex: `#FDA4AF`, price: 1500 },
					{ id: 214, hex: `#D8B4FE`, price: 1500 },
					// Второй ряд
					{ id: 215, hex: `#C9C9C9`, price: 1500 },
					{ id: 216, hex: `#A8A29E`, price: 1500 },
					{ id: 217, hex: `#94A3B8`, price: 1500 },
					{ id: 218, hex: `#6366F1`, price: 1500 },
					{ id: 219, hex: `#3B82F6`, price: 1500 },
					{ id: 220, hex: `#06B6D4`, price: 1500 },
					{ id: 221, hex: `#14B8A6`, price: 1500 },
					{ id: 222, hex: `#22C55E`, price: 1500 },
					{ id: 223, hex: `#84CC16`, price: 1500 },
					{ id: 224, hex: `#EAB308`, price: 1500 },
					{ id: 225, hex: `#F97316`, price: 1500 },
					{ id: 226, hex: `#EF4444`, price: 1500 },
					{ id: 227, hex: `#F43F5E`, price: 1500 },
					{ id: 228, hex: `#A855F7`, price: 1500 },
					// Третий ряд
					{ id: 229, hex: `#858585`, price: 1500 },
					{ id: 230, hex: `#57534E`, price: 1500 },
					{ id: 231, hex: `#475569`, price: 1500 },
					{ id: 232, hex: `#4338CA`, price: 1500 },
					{ id: 233, hex: `#1D4ED8`, price: 1500 },
					{ id: 234, hex: `#0E7490`, price: 1500 },
					{ id: 235, hex: `#0F766E`, price: 1500 },
					{ id: 236, hex: `#15803D`, price: 1500 },
					{ id: 237, hex: `#4D7C0F`, price: 1500 },
					{ id: 238, hex: `#A16207`, price: 1500 },
					{ id: 239, hex: `#C2410C`, price: 1500 },
					{ id: 240, hex: `#AB1313`, price: 1500 },
					{ id: 241, hex: `#BE123C`, price: 1500 },
					{ id: 242, hex: `#9333EA`, price: 1500 },
					// Четвертый ряд
					{ id: 243, hex: `#404040`, price: 1500 },
					{ id: 244, hex: `#292524`, price: 1500 },
					{ id: 245, hex: `#1E293B`, price: 1500 },
					{ id: 246, hex: `#312E81`, price: 1500 },
					{ id: 247, hex: `#1E3A8A`, price: 1500 },
					{ id: 248, hex: `#164E63`, price: 1500 },
					{ id: 249, hex: `#134E4A`, price: 1500 },
					{ id: 250, hex: `#14532D`, price: 1500 },
					{ id: 251, hex: `#365314`, price: 1500 },
					{ id: 252, hex: `#713F12`, price: 1500 },
					{ id: 253, hex: `#7C2D12`, price: 1500 },
					{ id: 254, hex: `#8F1313`, price: 1500 },
					{ id: 255, hex: `#881337`, price: 1500 },
					{ id: 256, hex: `#6B21A8`, price: 1500 },
					// Пятый ряд
					{ id: 257, hex: `#000000`, price: 1500 },
					{ id: 258, hex: `#0C0A09`, price: 1500 },
					{ id: 259, hex: `#020617`, price: 1500 },
					{ id: 260, hex: `#1E1B4B`, price: 1500 },
					{ id: 261, hex: `#082F49`, price: 1500 },
					{ id: 262, hex: `#083344`, price: 1500 },
					{ id: 263, hex: `#042F2E`, price: 1500 },
					{ id: 264, hex: `#052E16`, price: 1500 },
					{ id: 265, hex: `#1A2E05`, price: 1500 },
					{ id: 266, hex: `#422006`, price: 1500 },
					{ id: 267, hex: `#431407`, price: 1500 },
					{ id: 268, hex: `#470707`, price: 1500 },
					{ id: 269, hex: `#4C0519`, price: 1500 },
					{ id: 270, hex: `#3B0764`, price: 1500 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_3]: {
				vehPriceRate: 0.225,
				rgbColorPrice: 149,
				items: [
					// Первый ряд
					{ id: 301, hex: `#FFFFFF`, price: 1500 },
					{ id: 302, hex: `#E7E5E4`, price: 1500 },
					{ id: 303, hex: `#E2E8F0`, price: 1500 },
					{ id: 304, hex: `#A5B4FC`, price: 1500 },
					{ id: 305, hex: `#93C5FD`, price: 1500 },
					{ id: 306, hex: `#93C5FD`, price: 1500 },
					{ id: 307, hex: `#5EEAD4`, price: 1500 },
					{ id: 308, hex: `#86EFAC`, price: 1500 },
					{ id: 309, hex: `#BEF264`, price: 1500 },
					{ id: 310, hex: `#FDE047`, price: 1500 },
					{ id: 311, hex: `#FDBA74`, price: 1500 },
					{ id: 312, hex: `#FCA5A5`, price: 1500 },
					{ id: 313, hex: `#FDA4AF`, price: 1500 },
					{ id: 314, hex: `#D8B4FE`, price: 1500 },
					// Второй ряд
					{ id: 315, hex: `#C9C9C9`, price: 1500 },
					{ id: 316, hex: `#A8A29E`, price: 1500 },
					{ id: 317, hex: `#94A3B8`, price: 1500 },
					{ id: 318, hex: `#6366F1`, price: 1500 },
					{ id: 319, hex: `#3B82F6`, price: 1500 },
					{ id: 320, hex: `#06B6D4`, price: 1500 },
					{ id: 321, hex: `#14B8A6`, price: 1500 },
					{ id: 322, hex: `#22C55E`, price: 1500 },
					{ id: 323, hex: `#84CC16`, price: 1500 },
					{ id: 324, hex: `#EAB308`, price: 1500 },
					{ id: 325, hex: `#F97316`, price: 1500 },
					{ id: 326, hex: `#EF4444`, price: 1500 },
					{ id: 327, hex: `#F43F5E`, price: 1500 },
					{ id: 328, hex: `#A855F7`, price: 1500 },
					// Третий ряд
					{ id: 329, hex: `#858585`, price: 1500 },
					{ id: 330, hex: `#57534E`, price: 1500 },
					{ id: 331, hex: `#475569`, price: 1500 },
					{ id: 332, hex: `#4338CA`, price: 1500 },
					{ id: 333, hex: `#1D4ED8`, price: 1500 },
					{ id: 334, hex: `#0E7490`, price: 1500 },
					{ id: 335, hex: `#0F766E`, price: 1500 },
					{ id: 336, hex: `#15803D`, price: 1500 },
					{ id: 337, hex: `#4D7C0F`, price: 1500 },
					{ id: 338, hex: `#A16207`, price: 1500 },
					{ id: 339, hex: `#C2410C`, price: 1500 },
					{ id: 340, hex: `#AB1313`, price: 1500 },
					{ id: 341, hex: `#BE123C`, price: 1500 },
					{ id: 342, hex: `#9333EA`, price: 1500 },
					// Четвертый ряд
					{ id: 343, hex: `#404040`, price: 1500 },
					{ id: 344, hex: `#292524`, price: 1500 },
					{ id: 345, hex: `#1E293B`, price: 1500 },
					{ id: 346, hex: `#312E81`, price: 1500 },
					{ id: 347, hex: `#1E3A8A`, price: 1500 },
					{ id: 348, hex: `#164E63`, price: 1500 },
					{ id: 349, hex: `#134E4A`, price: 1500 },
					{ id: 350, hex: `#14532D`, price: 1500 },
					{ id: 351, hex: `#365314`, price: 1500 },
					{ id: 352, hex: `#713F12`, price: 1500 },
					{ id: 353, hex: `#7C2D12`, price: 1500 },
					{ id: 354, hex: `#8F1313`, price: 1500 },
					{ id: 355, hex: `#881337`, price: 1500 },
					{ id: 356, hex: `#6B21A8`, price: 1500 },
					// Пятый ряд
					{ id: 357, hex: `#000000`, price: 1500 },
					{ id: 358, hex: `#0C0A09`, price: 1500 },
					{ id: 359, hex: `#020617`, price: 1500 },
					{ id: 360, hex: `#1E1B4B`, price: 1500 },
					{ id: 361, hex: `#082F49`, price: 1500 },
					{ id: 362, hex: `#083344`, price: 1500 },
					{ id: 363, hex: `#042F2E`, price: 1500 },
					{ id: 364, hex: `#052E16`, price: 1500 },
					{ id: 365, hex: `#1A2E05`, price: 1500 },
					{ id: 366, hex: `#422006`, price: 1500 },
					{ id: 367, hex: `#431407`, price: 1500 },
					{ id: 368, hex: `#470707`, price: 1500 },
					{ id: 369, hex: `#4C0519`, price: 1500 },
					{ id: 370, hex: `#3B0764`, price: 1500 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_4]: {
				vehPriceRate: 0.2375,
				rgbColorPrice: 149,
				items: [
					// Первый ряд
					{ id: 401, hex: `#FFFFFF`, price: 1500 },
					{ id: 402, hex: `#E7E5E4`, price: 1500 },
					{ id: 403, hex: `#E2E8F0`, price: 1500 },
					{ id: 404, hex: `#A5B4FC`, price: 1500 },
					{ id: 405, hex: `#93C5FD`, price: 1500 },
					{ id: 406, hex: `#93C5FD`, price: 1500 },
					{ id: 407, hex: `#5EEAD4`, price: 1500 },
					{ id: 408, hex: `#86EFAC`, price: 1500 },
					{ id: 409, hex: `#BEF264`, price: 1500 },
					{ id: 410, hex: `#FDE047`, price: 1500 },
					{ id: 411, hex: `#FDBA74`, price: 1500 },
					{ id: 412, hex: `#FCA5A5`, price: 1500 },
					{ id: 413, hex: `#FDA4AF`, price: 1500 },
					{ id: 414, hex: `#D8B4FE`, price: 1500 },
					// Второй ряд
					{ id: 415, hex: `#C9C9C9`, price: 1500 },
					{ id: 416, hex: `#A8A29E`, price: 1500 },
					{ id: 417, hex: `#94A3B8`, price: 1500 },
					{ id: 418, hex: `#6366F1`, price: 1500 },
					{ id: 419, hex: `#3B82F6`, price: 1500 },
					{ id: 420, hex: `#06B6D4`, price: 1500 },
					{ id: 421, hex: `#14B8A6`, price: 1500 },
					{ id: 422, hex: `#22C55E`, price: 1500 },
					{ id: 423, hex: `#84CC16`, price: 1500 },
					{ id: 424, hex: `#EAB308`, price: 1500 },
					{ id: 425, hex: `#F97316`, price: 1500 },
					{ id: 426, hex: `#EF4444`, price: 1500 },
					{ id: 427, hex: `#F43F5E`, price: 1500 },
					{ id: 428, hex: `#A855F7`, price: 1500 },
					// Третий ряд
					{ id: 429, hex: `#858585`, price: 1500 },
					{ id: 430, hex: `#57534E`, price: 1500 },
					{ id: 431, hex: `#475569`, price: 1500 },
					{ id: 432, hex: `#4338CA`, price: 1500 },
					{ id: 433, hex: `#1D4ED8`, price: 1500 },
					{ id: 434, hex: `#0E7490`, price: 1500 },
					{ id: 435, hex: `#0F766E`, price: 1500 },
					{ id: 436, hex: `#15803D`, price: 1500 },
					{ id: 437, hex: `#4D7C0F`, price: 1500 },
					{ id: 438, hex: `#A16207`, price: 1500 },
					{ id: 439, hex: `#C2410C`, price: 1500 },
					{ id: 440, hex: `#AB1313`, price: 1500 },
					{ id: 441, hex: `#BE123C`, price: 1500 },
					{ id: 442, hex: `#9333EA`, price: 1500 },
					// Четвертый ряд
					{ id: 443, hex: `#404040`, price: 1500 },
					{ id: 444, hex: `#292524`, price: 1500 },
					{ id: 445, hex: `#1E293B`, price: 1500 },
					{ id: 446, hex: `#312E81`, price: 1500 },
					{ id: 447, hex: `#1E3A8A`, price: 1500 },
					{ id: 448, hex: `#164E63`, price: 1500 },
					{ id: 449, hex: `#134E4A`, price: 1500 },
					{ id: 450, hex: `#14532D`, price: 1500 },
					{ id: 451, hex: `#365314`, price: 1500 },
					{ id: 452, hex: `#713F12`, price: 1500 },
					{ id: 453, hex: `#7C2D12`, price: 1500 },
					{ id: 454, hex: `#8F1313`, price: 1500 },
					{ id: 455, hex: `#881337`, price: 1500 },
					{ id: 456, hex: `#6B21A8`, price: 1500 },
					// Пятый ряд
					{ id: 457, hex: `#000000`, price: 1500 },
					{ id: 458, hex: `#0C0A09`, price: 1500 },
					{ id: 459, hex: `#020617`, price: 1500 },
					{ id: 460, hex: `#1E1B4B`, price: 1500 },
					{ id: 461, hex: `#082F49`, price: 1500 },
					{ id: 462, hex: `#083344`, price: 1500 },
					{ id: 463, hex: `#042F2E`, price: 1500 },
					{ id: 464, hex: `#052E16`, price: 1500 },
					{ id: 465, hex: `#1A2E05`, price: 1500 },
					{ id: 466, hex: `#422006`, price: 1500 },
					{ id: 467, hex: `#431407`, price: 1500 },
					{ id: 468, hex: `#470707`, price: 1500 },
					{ id: 469, hex: `#4C0519`, price: 1500 },
					{ id: 470, hex: `#3B0764`, price: 1500 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_5]: {
				vehPriceRate: 0.25,
				rgbColorPrice: 199,
				items: [
					// Первый ряд
					{ id: 501, hex: `#FFFFFF`, price: 5000 },
					{ id: 502, hex: `#E7E5E4`, price: 5000 },
					{ id: 503, hex: `#E2E8F0`, price: 5000 },
					{ id: 504, hex: `#A5B4FC`, price: 5000 },
					{ id: 505, hex: `#93C5FD`, price: 5000 },
					{ id: 506, hex: `#93C5FD`, price: 5000 },
					{ id: 507, hex: `#5EEAD4`, price: 5000 },
					{ id: 508, hex: `#86EFAC`, price: 5000 },
					{ id: 509, hex: `#BEF264`, price: 5000 },
					{ id: 510, hex: `#FDE047`, price: 5000 },
					{ id: 511, hex: `#FDBA74`, price: 5000 },
					{ id: 512, hex: `#FCA5A5`, price: 5000 },
					{ id: 513, hex: `#FDA4AF`, price: 5000 },
					{ id: 514, hex: `#D8B4FE`, price: 5000 },
					// Второй ряд
					{ id: 515, hex: `#C9C9C9`, price: 5000 },
					{ id: 516, hex: `#A8A29E`, price: 5000 },
					{ id: 517, hex: `#94A3B8`, price: 5000 },
					{ id: 518, hex: `#6366F1`, price: 5000 },
					{ id: 519, hex: `#3B82F6`, price: 5000 },
					{ id: 520, hex: `#06B6D4`, price: 5000 },
					{ id: 521, hex: `#14B8A6`, price: 5000 },
					{ id: 522, hex: `#22C55E`, price: 5000 },
					{ id: 523, hex: `#84CC16`, price: 5000 },
					{ id: 524, hex: `#EAB308`, price: 5000 },
					{ id: 525, hex: `#F97316`, price: 5000 },
					{ id: 526, hex: `#EF4444`, price: 5000 },
					{ id: 527, hex: `#F43F5E`, price: 5000 },
					{ id: 528, hex: `#A855F7`, price: 5000 },
					// Третий ряд
					{ id: 529, hex: `#858585`, price: 5000 },
					{ id: 530, hex: `#57534E`, price: 5000 },
					{ id: 531, hex: `#475569`, price: 5000 },
					{ id: 532, hex: `#4338CA`, price: 5000 },
					{ id: 533, hex: `#1D4ED8`, price: 5000 },
					{ id: 534, hex: `#0E7490`, price: 5000 },
					{ id: 535, hex: `#0F766E`, price: 5000 },
					{ id: 536, hex: `#15803D`, price: 5000 },
					{ id: 537, hex: `#4D7C0F`, price: 5000 },
					{ id: 538, hex: `#A16207`, price: 5000 },
					{ id: 539, hex: `#C2410C`, price: 5000 },
					{ id: 540, hex: `#AB1313`, price: 5000 },
					{ id: 541, hex: `#BE123C`, price: 5000 },
					{ id: 542, hex: `#9333EA`, price: 5000 },
					// Четвертый ряд
					{ id: 543, hex: `#404040`, price: 5000 },
					{ id: 544, hex: `#292524`, price: 5000 },
					{ id: 545, hex: `#1E293B`, price: 5000 },
					{ id: 546, hex: `#312E81`, price: 5000 },
					{ id: 547, hex: `#1E3A8A`, price: 5000 },
					{ id: 548, hex: `#164E63`, price: 5000 },
					{ id: 549, hex: `#134E4A`, price: 5000 },
					{ id: 550, hex: `#14532D`, price: 5000 },
					{ id: 551, hex: `#365314`, price: 5000 },
					{ id: 552, hex: `#713F12`, price: 5000 },
					{ id: 553, hex: `#7C2D12`, price: 5000 },
					{ id: 554, hex: `#8F1313`, price: 5000 },
					{ id: 555, hex: `#881337`, price: 5000 },
					{ id: 556, hex: `#6B21A8`, price: 5000 },
					// Пятый ряд
					{ id: 557, hex: `#000000`, price: 5000 },
					{ id: 558, hex: `#0C0A09`, price: 5000 },
					{ id: 559, hex: `#020617`, price: 5000 },
					{ id: 560, hex: `#1E1B4B`, price: 5000 },
					{ id: 561, hex: `#082F49`, price: 5000 },
					{ id: 562, hex: `#083344`, price: 5000 },
					{ id: 563, hex: `#042F2E`, price: 5000 },
					{ id: 564, hex: `#052E16`, price: 5000 },
					{ id: 565, hex: `#1A2E05`, price: 5000 },
					{ id: 566, hex: `#422006`, price: 5000 },
					{ id: 567, hex: `#431407`, price: 5000 },
					{ id: 568, hex: `#470707`, price: 5000 },
					{ id: 569, hex: `#4C0519`, price: 5000 },
					{ id: 570, hex: `#3B0764`, price: 5000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_6]: {
				vehPriceRate: 0.35,
				rgbColorPrice: 249,
				items: [
					// Первый ряд
					{ id: 601, hex: `#FFFFFF`, price: 7500 },
					{ id: 602, hex: `#E7E5E4`, price: 7500 },
					{ id: 603, hex: `#E2E8F0`, price: 7500 },
					{ id: 604, hex: `#A5B4FC`, price: 7500 },
					{ id: 605, hex: `#93C5FD`, price: 7500 },
					{ id: 606, hex: `#93C5FD`, price: 7500 },
					{ id: 607, hex: `#5EEAD4`, price: 7500 },
					{ id: 608, hex: `#86EFAC`, price: 7500 },
					{ id: 609, hex: `#BEF264`, price: 7500 },
					{ id: 610, hex: `#FDE047`, price: 7500 },
					{ id: 611, hex: `#FDBA74`, price: 7500 },
					{ id: 612, hex: `#FCA5A5`, price: 7500 },
					{ id: 613, hex: `#FDA4AF`, price: 7500 },
					{ id: 614, hex: `#D8B4FE`, price: 7500 },
					// Второй ряд
					{ id: 615, hex: `#C9C9C9`, price: 7500 },
					{ id: 616, hex: `#A8A29E`, price: 7500 },
					{ id: 617, hex: `#94A3B8`, price: 7500 },
					{ id: 618, hex: `#6366F1`, price: 7500 },
					{ id: 619, hex: `#3B82F6`, price: 7500 },
					{ id: 620, hex: `#06B6D4`, price: 7500 },
					{ id: 621, hex: `#14B8A6`, price: 7500 },
					{ id: 622, hex: `#22C55E`, price: 7500 },
					{ id: 623, hex: `#84CC16`, price: 7500 },
					{ id: 624, hex: `#EAB308`, price: 7500 },
					{ id: 625, hex: `#F97316`, price: 7500 },
					{ id: 626, hex: `#EF4444`, price: 7500 },
					{ id: 627, hex: `#F43F5E`, price: 7500 },
					{ id: 628, hex: `#A855F7`, price: 7500 },
					// Третий ряд
					{ id: 629, hex: `#858585`, price: 7500 },
					{ id: 630, hex: `#57534E`, price: 7500 },
					{ id: 631, hex: `#475569`, price: 7500 },
					{ id: 632, hex: `#4338CA`, price: 7500 },
					{ id: 633, hex: `#1D4ED8`, price: 7500 },
					{ id: 634, hex: `#0E7490`, price: 7500 },
					{ id: 635, hex: `#0F766E`, price: 7500 },
					{ id: 636, hex: `#15803D`, price: 7500 },
					{ id: 637, hex: `#4D7C0F`, price: 7500 },
					{ id: 638, hex: `#A16207`, price: 7500 },
					{ id: 639, hex: `#C2410C`, price: 7500 },
					{ id: 640, hex: `#AB1313`, price: 7500 },
					{ id: 641, hex: `#BE123C`, price: 7500 },
					{ id: 642, hex: `#9333EA`, price: 7500 },
					// Четвертый ряд
					{ id: 643, hex: `#404040`, price: 7500 },
					{ id: 644, hex: `#292524`, price: 7500 },
					{ id: 645, hex: `#1E293B`, price: 7500 },
					{ id: 646, hex: `#312E81`, price: 7500 },
					{ id: 647, hex: `#1E3A8A`, price: 7500 },
					{ id: 648, hex: `#164E63`, price: 7500 },
					{ id: 649, hex: `#134E4A`, price: 7500 },
					{ id: 650, hex: `#14532D`, price: 7500 },
					{ id: 651, hex: `#365314`, price: 7500 },
					{ id: 652, hex: `#713F12`, price: 7500 },
					{ id: 653, hex: `#7C2D12`, price: 7500 },
					{ id: 654, hex: `#8F1313`, price: 7500 },
					{ id: 655, hex: `#881337`, price: 7500 },
					{ id: 656, hex: `#6B21A8`, price: 7500 },
					// Пятый ряд
					{ id: 657, hex: `#000000`, price: 7500 },
					{ id: 658, hex: `#0C0A09`, price: 7500 },
					{ id: 659, hex: `#020617`, price: 7500 },
					{ id: 660, hex: `#1E1B4B`, price: 7500 },
					{ id: 661, hex: `#082F49`, price: 7500 },
					{ id: 662, hex: `#083344`, price: 7500 },
					{ id: 663, hex: `#042F2E`, price: 7500 },
					{ id: 664, hex: `#052E16`, price: 7500 },
					{ id: 665, hex: `#1A2E05`, price: 7500 },
					{ id: 666, hex: `#422006`, price: 7500 },
					{ id: 667, hex: `#431407`, price: 7500 },
					{ id: 668, hex: `#470707`, price: 7500 },
					{ id: 669, hex: `#4C0519`, price: 7500 },
					{ id: 670, hex: `#3B0764`, price: 7500 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_7]: {
				vehPriceRate: 0.45,
				rgbColorPrice: 299,
				items: [
					// Первый ряд
					{ id: 701, hex: `#FFFFFF`, price: 10000 },
					{ id: 702, hex: `#E7E5E4`, price: 10000 },
					{ id: 703, hex: `#E2E8F0`, price: 10000 },
					{ id: 704, hex: `#A5B4FC`, price: 10000 },
					{ id: 705, hex: `#93C5FD`, price: 10000 },
					{ id: 706, hex: `#93C5FD`, price: 10000 },
					{ id: 707, hex: `#5EEAD4`, price: 10000 },
					{ id: 708, hex: `#86EFAC`, price: 10000 },
					{ id: 709, hex: `#BEF264`, price: 10000 },
					{ id: 710, hex: `#FDE047`, price: 10000 },
					{ id: 711, hex: `#FDBA74`, price: 10000 },
					{ id: 712, hex: `#FCA5A5`, price: 10000 },
					{ id: 713, hex: `#FDA4AF`, price: 10000 },
					{ id: 714, hex: `#D8B4FE`, price: 10000 },
					// Второй ряд
					{ id: 715, hex: `#C9C9C9`, price: 10000 },
					{ id: 716, hex: `#A8A29E`, price: 10000 },
					{ id: 717, hex: `#94A3B8`, price: 10000 },
					{ id: 718, hex: `#6366F1`, price: 10000 },
					{ id: 719, hex: `#3B82F6`, price: 10000 },
					{ id: 720, hex: `#06B6D4`, price: 10000 },
					{ id: 721, hex: `#14B8A6`, price: 10000 },
					{ id: 722, hex: `#22C55E`, price: 10000 },
					{ id: 723, hex: `#84CC16`, price: 10000 },
					{ id: 724, hex: `#EAB308`, price: 10000 },
					{ id: 725, hex: `#F97316`, price: 10000 },
					{ id: 726, hex: `#EF4444`, price: 10000 },
					{ id: 727, hex: `#F43F5E`, price: 10000 },
					{ id: 728, hex: `#A855F7`, price: 10000 },
					// Третий ряд
					{ id: 729, hex: `#858585`, price: 10000 },
					{ id: 730, hex: `#57534E`, price: 10000 },
					{ id: 731, hex: `#475569`, price: 10000 },
					{ id: 732, hex: `#4338CA`, price: 10000 },
					{ id: 733, hex: `#1D4ED8`, price: 10000 },
					{ id: 734, hex: `#0E7490`, price: 10000 },
					{ id: 735, hex: `#0F766E`, price: 10000 },
					{ id: 736, hex: `#15803D`, price: 10000 },
					{ id: 737, hex: `#4D7C0F`, price: 10000 },
					{ id: 738, hex: `#A16207`, price: 10000 },
					{ id: 739, hex: `#C2410C`, price: 10000 },
					{ id: 740, hex: `#AB1313`, price: 10000 },
					{ id: 741, hex: `#BE123C`, price: 10000 },
					{ id: 742, hex: `#9333EA`, price: 10000 },
					// Четвертый ряд
					{ id: 743, hex: `#404040`, price: 10000 },
					{ id: 744, hex: `#292524`, price: 10000 },
					{ id: 745, hex: `#1E293B`, price: 10000 },
					{ id: 746, hex: `#312E81`, price: 10000 },
					{ id: 747, hex: `#1E3A8A`, price: 10000 },
					{ id: 748, hex: `#164E63`, price: 10000 },
					{ id: 749, hex: `#134E4A`, price: 10000 },
					{ id: 750, hex: `#14532D`, price: 10000 },
					{ id: 751, hex: `#365314`, price: 10000 },
					{ id: 752, hex: `#713F12`, price: 10000 },
					{ id: 753, hex: `#7C2D12`, price: 10000 },
					{ id: 754, hex: `#8F1313`, price: 10000 },
					{ id: 755, hex: `#881337`, price: 10000 },
					{ id: 756, hex: `#6B21A8`, price: 10000 },
					// Пятый ряд
					{ id: 757, hex: `#000000`, price: 10000 },
					{ id: 758, hex: `#0C0A09`, price: 10000 },
					{ id: 759, hex: `#020617`, price: 10000 },
					{ id: 760, hex: `#1E1B4B`, price: 10000 },
					{ id: 761, hex: `#082F49`, price: 10000 },
					{ id: 762, hex: `#083344`, price: 10000 },
					{ id: 763, hex: `#042F2E`, price: 10000 },
					{ id: 764, hex: `#052E16`, price: 10000 },
					{ id: 765, hex: `#1A2E05`, price: 10000 },
					{ id: 766, hex: `#422006`, price: 10000 },
					{ id: 767, hex: `#431407`, price: 10000 },
					{ id: 768, hex: `#470707`, price: 10000 },
					{ id: 769, hex: `#4C0519`, price: 10000 },
					{ id: 770, hex: `#3B0764`, price: 10000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_8]: {
				vehPriceRate: 0.55,
				rgbColorPrice: 349,
				items: [
					// Первый ряд
					{ id: 801, hex: `#FFFFFF`, price: 15000 },
					{ id: 802, hex: `#E7E5E4`, price: 15000 },
					{ id: 803, hex: `#E2E8F0`, price: 15000 },
					{ id: 804, hex: `#A5B4FC`, price: 15000 },
					{ id: 805, hex: `#93C5FD`, price: 15000 },
					{ id: 806, hex: `#93C5FD`, price: 15000 },
					{ id: 807, hex: `#5EEAD4`, price: 15000 },
					{ id: 808, hex: `#86EFAC`, price: 15000 },
					{ id: 809, hex: `#BEF264`, price: 15000 },
					{ id: 810, hex: `#FDE047`, price: 15000 },
					{ id: 811, hex: `#FDBA74`, price: 15000 },
					{ id: 812, hex: `#FCA5A5`, price: 15000 },
					{ id: 813, hex: `#FDA4AF`, price: 15000 },
					{ id: 814, hex: `#D8B4FE`, price: 15000 },
					// Второй ряд
					{ id: 815, hex: `#C9C9C9`, price: 15000 },
					{ id: 816, hex: `#A8A29E`, price: 15000 },
					{ id: 817, hex: `#94A3B8`, price: 15000 },
					{ id: 818, hex: `#6366F1`, price: 15000 },
					{ id: 819, hex: `#3B82F6`, price: 15000 },
					{ id: 820, hex: `#06B6D4`, price: 15000 },
					{ id: 821, hex: `#14B8A6`, price: 15000 },
					{ id: 822, hex: `#22C55E`, price: 15000 },
					{ id: 823, hex: `#84CC16`, price: 15000 },
					{ id: 824, hex: `#EAB308`, price: 15000 },
					{ id: 825, hex: `#F97316`, price: 15000 },
					{ id: 826, hex: `#EF4444`, price: 15000 },
					{ id: 827, hex: `#F43F5E`, price: 15000 },
					{ id: 828, hex: `#A855F7`, price: 15000 },
					// Третий ряд
					{ id: 829, hex: `#858585`, price: 15000 },
					{ id: 830, hex: `#57534E`, price: 15000 },
					{ id: 831, hex: `#475569`, price: 15000 },
					{ id: 832, hex: `#4338CA`, price: 15000 },
					{ id: 833, hex: `#1D4ED8`, price: 15000 },
					{ id: 834, hex: `#0E7490`, price: 15000 },
					{ id: 835, hex: `#0F766E`, price: 15000 },
					{ id: 836, hex: `#15803D`, price: 15000 },
					{ id: 837, hex: `#4D7C0F`, price: 15000 },
					{ id: 838, hex: `#A16207`, price: 15000 },
					{ id: 839, hex: `#C2410C`, price: 15000 },
					{ id: 840, hex: `#AB1313`, price: 15000 },
					{ id: 841, hex: `#BE123C`, price: 15000 },
					{ id: 842, hex: `#9333EA`, price: 15000 },
					// Четвертый ряд
					{ id: 843, hex: `#404040`, price: 15000 },
					{ id: 844, hex: `#292524`, price: 15000 },
					{ id: 845, hex: `#1E293B`, price: 15000 },
					{ id: 846, hex: `#312E81`, price: 15000 },
					{ id: 847, hex: `#1E3A8A`, price: 15000 },
					{ id: 848, hex: `#164E63`, price: 15000 },
					{ id: 849, hex: `#134E4A`, price: 15000 },
					{ id: 850, hex: `#14532D`, price: 15000 },
					{ id: 851, hex: `#365314`, price: 15000 },
					{ id: 852, hex: `#713F12`, price: 15000 },
					{ id: 853, hex: `#7C2D12`, price: 15000 },
					{ id: 854, hex: `#8F1313`, price: 15000 },
					{ id: 855, hex: `#881337`, price: 15000 },
					{ id: 856, hex: `#6B21A8`, price: 15000 },
					// Пятый ряд
					{ id: 857, hex: `#000000`, price: 15000 },
					{ id: 858, hex: `#0C0A09`, price: 15000 },
					{ id: 859, hex: `#020617`, price: 15000 },
					{ id: 860, hex: `#1E1B4B`, price: 15000 },
					{ id: 861, hex: `#082F49`, price: 15000 },
					{ id: 862, hex: `#083344`, price: 15000 },
					{ id: 863, hex: `#042F2E`, price: 15000 },
					{ id: 864, hex: `#052E16`, price: 15000 },
					{ id: 865, hex: `#1A2E05`, price: 15000 },
					{ id: 866, hex: `#422006`, price: 15000 },
					{ id: 867, hex: `#431407`, price: 15000 },
					{ id: 868, hex: `#470707`, price: 15000 },
					{ id: 869, hex: `#4C0519`, price: 15000 },
					{ id: 870, hex: `#3B0764`, price: 15000 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_9]: {
				vehPriceRate: 0.65,
				rgbColorPrice: 399,
				items: [
					// Первый ряд
					{ id: 901, hex: `#FFFFFF`, price: 22500 },
					{ id: 902, hex: `#E7E5E4`, price: 22500 },
					{ id: 903, hex: `#E2E8F0`, price: 22500 },
					{ id: 904, hex: `#A5B4FC`, price: 22500 },
					{ id: 905, hex: `#93C5FD`, price: 22500 },
					{ id: 906, hex: `#93C5FD`, price: 22500 },
					{ id: 907, hex: `#5EEAD4`, price: 22500 },
					{ id: 908, hex: `#86EFAC`, price: 22500 },
					{ id: 909, hex: `#BEF264`, price: 22500 },
					{ id: 910, hex: `#FDE047`, price: 22500 },
					{ id: 911, hex: `#FDBA74`, price: 22500 },
					{ id: 912, hex: `#FCA5A5`, price: 22500 },
					{ id: 913, hex: `#FDA4AF`, price: 22500 },
					{ id: 914, hex: `#D8B4FE`, price: 22500 },
					// Второй ряд
					{ id: 915, hex: `#C9C9C9`, price: 22500 },
					{ id: 916, hex: `#A8A29E`, price: 22500 },
					{ id: 917, hex: `#94A3B8`, price: 22500 },
					{ id: 918, hex: `#6366F1`, price: 22500 },
					{ id: 919, hex: `#3B82F6`, price: 22500 },
					{ id: 920, hex: `#06B6D4`, price: 22500 },
					{ id: 921, hex: `#14B8A6`, price: 22500 },
					{ id: 922, hex: `#22C55E`, price: 22500 },
					{ id: 923, hex: `#84CC16`, price: 22500 },
					{ id: 924, hex: `#EAB308`, price: 22500 },
					{ id: 925, hex: `#F97316`, price: 22500 },
					{ id: 926, hex: `#EF4444`, price: 22500 },
					{ id: 927, hex: `#F43F5E`, price: 22500 },
					{ id: 928, hex: `#A855F7`, price: 22500 },
					// Третий ряд
					{ id: 929, hex: `#858585`, price: 22500 },
					{ id: 930, hex: `#57534E`, price: 22500 },
					{ id: 931, hex: `#475569`, price: 22500 },
					{ id: 932, hex: `#4338CA`, price: 22500 },
					{ id: 933, hex: `#1D4ED8`, price: 22500 },
					{ id: 934, hex: `#0E7490`, price: 22500 },
					{ id: 935, hex: `#0F766E`, price: 22500 },
					{ id: 936, hex: `#15803D`, price: 22500 },
					{ id: 937, hex: `#4D7C0F`, price: 22500 },
					{ id: 938, hex: `#A16207`, price: 22500 },
					{ id: 939, hex: `#C2410C`, price: 22500 },
					{ id: 940, hex: `#AB1313`, price: 22500 },
					{ id: 941, hex: `#BE123C`, price: 22500 },
					{ id: 942, hex: `#9333EA`, price: 22500 },
					// Четвертый ряд
					{ id: 943, hex: `#404040`, price: 22500 },
					{ id: 944, hex: `#292524`, price: 22500 },
					{ id: 945, hex: `#1E293B`, price: 22500 },
					{ id: 946, hex: `#312E81`, price: 22500 },
					{ id: 947, hex: `#1E3A8A`, price: 22500 },
					{ id: 948, hex: `#164E63`, price: 22500 },
					{ id: 949, hex: `#134E4A`, price: 22500 },
					{ id: 950, hex: `#14532D`, price: 22500 },
					{ id: 951, hex: `#365314`, price: 22500 },
					{ id: 952, hex: `#713F12`, price: 22500 },
					{ id: 953, hex: `#7C2D12`, price: 22500 },
					{ id: 954, hex: `#8F1313`, price: 22500 },
					{ id: 955, hex: `#881337`, price: 22500 },
					{ id: 956, hex: `#6B21A8`, price: 22500 },
					// Пятый ряд
					{ id: 957, hex: `#000000`, price: 22500 },
					{ id: 958, hex: `#0C0A09`, price: 22500 },
					{ id: 959, hex: `#020617`, price: 22500 },
					{ id: 960, hex: `#1E1B4B`, price: 22500 },
					{ id: 961, hex: `#082F49`, price: 22500 },
					{ id: 962, hex: `#083344`, price: 22500 },
					{ id: 963, hex: `#042F2E`, price: 22500 },
					{ id: 964, hex: `#052E16`, price: 22500 },
					{ id: 965, hex: `#1A2E05`, price: 22500 },
					{ id: 966, hex: `#422006`, price: 22500 },
					{ id: 967, hex: `#431407`, price: 22500 },
					{ id: 968, hex: `#470707`, price: 22500 },
					{ id: 969, hex: `#4C0519`, price: 22500 },
					{ id: 970, hex: `#3B0764`, price: 22500 },
				],
			},
			[ColorModType.VMT_PAINT_MATERIAL_10]: {
				vehPriceRate: 0.75,
				rgbColorPrice: 499,
				items: [
					// Первый ряд
					{ id: 1001, hex: `#FFFFFF`, price: 30000 },
					{ id: 1002, hex: `#E7E5E4`, price: 30000 },
					{ id: 1003, hex: `#E2E8F0`, price: 30000 },
					{ id: 1004, hex: `#A5B4FC`, price: 30000 },
					{ id: 1005, hex: `#93C5FD`, price: 30000 },
					{ id: 1006, hex: `#93C5FD`, price: 30000 },
					{ id: 1007, hex: `#5EEAD4`, price: 30000 },
					{ id: 1008, hex: `#86EFAC`, price: 30000 },
					{ id: 1009, hex: `#BEF264`, price: 30000 },
					{ id: 1010, hex: `#FDE047`, price: 30000 },
					{ id: 1011, hex: `#FDBA74`, price: 30000 },
					{ id: 1012, hex: `#FCA5A5`, price: 30000 },
					{ id: 1013, hex: `#FDA4AF`, price: 30000 },
					{ id: 1014, hex: `#D8B4FE`, price: 30000 },
					// Второй ряд
					{ id: 1015, hex: `#C9C9C9`, price: 30000 },
					{ id: 1016, hex: `#A8A29E`, price: 30000 },
					{ id: 1017, hex: `#94A3B8`, price: 30000 },
					{ id: 1018, hex: `#6366F1`, price: 30000 },
					{ id: 1019, hex: `#3B82F6`, price: 30000 },
					{ id: 1020, hex: `#06B6D4`, price: 30000 },
					{ id: 1021, hex: `#14B8A6`, price: 30000 },
					{ id: 1022, hex: `#22C55E`, price: 30000 },
					{ id: 1023, hex: `#84CC16`, price: 30000 },
					{ id: 1024, hex: `#EAB308`, price: 30000 },
					{ id: 1025, hex: `#F97316`, price: 30000 },
					{ id: 1026, hex: `#EF4444`, price: 30000 },
					{ id: 1027, hex: `#F43F5E`, price: 30000 },
					{ id: 1028, hex: `#A855F7`, price: 30000 },
					// Третий ряд
					{ id: 1029, hex: `#858585`, price: 30000 },
					{ id: 1030, hex: `#57534E`, price: 30000 },
					{ id: 1031, hex: `#475569`, price: 30000 },
					{ id: 1032, hex: `#4338CA`, price: 30000 },
					{ id: 1033, hex: `#1D4ED8`, price: 30000 },
					{ id: 1034, hex: `#0E7490`, price: 30000 },
					{ id: 1035, hex: `#0F766E`, price: 30000 },
					{ id: 1036, hex: `#15803D`, price: 30000 },
					{ id: 1037, hex: `#4D7C0F`, price: 30000 },
					{ id: 1038, hex: `#A16207`, price: 30000 },
					{ id: 1039, hex: `#C2410C`, price: 30000 },
					{ id: 1040, hex: `#AB1313`, price: 30000 },
					{ id: 1041, hex: `#BE123C`, price: 30000 },
					{ id: 1042, hex: `#9333EA`, price: 30000 },
					// Четвертый ряд
					{ id: 1043, hex: `#404040`, price: 30000 },
					{ id: 1044, hex: `#292524`, price: 30000 },
					{ id: 1045, hex: `#1E293B`, price: 30000 },
					{ id: 1046, hex: `#312E81`, price: 30000 },
					{ id: 1047, hex: `#1E3A8A`, price: 30000 },
					{ id: 1048, hex: `#164E63`, price: 30000 },
					{ id: 1049, hex: `#134E4A`, price: 30000 },
					{ id: 1050, hex: `#14532D`, price: 30000 },
					{ id: 1051, hex: `#365314`, price: 30000 },
					{ id: 1052, hex: `#713F12`, price: 30000 },
					{ id: 1053, hex: `#7C2D12`, price: 30000 },
					{ id: 1054, hex: `#8F1313`, price: 30000 },
					{ id: 1055, hex: `#881337`, price: 30000 },
					{ id: 1056, hex: `#6B21A8`, price: 30000 },
					// Пятый ряд
					{ id: 1057, hex: `#000000`, price: 30000 },
					{ id: 1058, hex: `#0C0A09`, price: 30000 },
					{ id: 1059, hex: `#020617`, price: 30000 },
					{ id: 1060, hex: `#1E1B4B`, price: 30000 },
					{ id: 1061, hex: `#082F49`, price: 30000 },
					{ id: 1062, hex: `#083344`, price: 30000 },
					{ id: 1063, hex: `#042F2E`, price: 30000 },
					{ id: 1064, hex: `#052E16`, price: 30000 },
					{ id: 1065, hex: `#1A2E05`, price: 30000 },
					{ id: 1066, hex: `#422006`, price: 30000 },
					{ id: 1067, hex: `#431407`, price: 30000 },
					{ id: 1068, hex: `#470707`, price: 30000 },
					{ id: 1069, hex: `#4C0519`, price: 30000 },
					{ id: 1070, hex: `#3B0764`, price: 30000 },
				],
			},
		},
	},
	{
		action: ColorType.VMT_PAINT3,
		title: 'vehicleTuning.colorMenu.VMT_PAINT3',
		eventName: 'client.tuning.setToneColor',
		colors: {
			vehPriceRate: 0.1,
			items: [
				{ colorId: 181, price: 1500 },
				{ colorId: 182, price: 1500 },
				{ colorId: 183, price: 1500 },
				{ colorId: 184, price: 1500 },
				{ colorId: 185, price: 1500 },
				{ colorId: 186, price: 1500 },
				{ colorId: 187, price: 1500 },
				{ colorId: 188, price: 1500 },
				{ colorId: 189, price: 1500 },
				{ colorId: 190, price: 1500 },
				{ colorId: 191, price: 1500 },
				{ colorId: 192, price: 1500 },
				{ colorId: 193, price: 1500 },
				{ colorId: 194, price: 1500 },
				{ colorId: 195, price: 1500 },
				{ colorId: 196, price: 1500 },
				{ colorId: 197, price: 1500 },
				{ colorId: 198, price: 1500 },
				{ colorId: 199, price: 1500 },
				{ colorId: 200, price: 1500 },
				{ colorId: 201, price: 1500 },
				{ colorId: 202, price: 1500 },
				{ colorId: 203, price: 1500 },
				{ colorId: 204, price: 1500 },
				{ colorId: 205, price: 1500 },
				{ colorId: 206, price: 1500 },
				{ colorId: 207, price: 1500 },
				{ colorId: 208, price: 1500 },
				{ colorId: 209, price: 1500 },
				{ colorId: 210, price: 1500 },
				{ colorId: 211, price: 1500 },
				{ colorId: 212, price: 1500 },
				{ colorId: 213, price: 1500 },
				{ colorId: 214, price: 1500 },
				{ colorId: 215, price: 1500 },
				{ colorId: 216, price: 1500 },
				{ colorId: 217, price: 1500 },
				{ colorId: 218, price: 1500 },
				{ colorId: 219, price: 1500 },
				{ colorId: 220, price: 1500 },
				{ colorId: 221, price: 1500 },
				{ colorId: 222, price: 1500 },
				{ colorId: 223, price: 1500 },
				{ colorId: 224, price: 1500 },
				{ colorId: 225, price: 1500 },
				{ colorId: 226, price: 1500 },
				{ colorId: 227, price: 1500 },
				{ colorId: 228, price: 1500 },
				{ colorId: 229, price: 1500 },
				{ colorId: 230, price: 1500 },
				{ colorId: 231, price: 1500 },
				{ colorId: 232, price: 1500 },
				{ colorId: 233, price: 1500 },
				{ colorId: 234, price: 1500 },
				{ colorId: 235, price: 1500 },
				{ colorId: 236, price: 1500 },
				{ colorId: 237, price: 1500 },
				{ colorId: 238, price: 1500 },
				{ colorId: 239, price: 1500 },
				{ colorId: 240, price: 1500 },
				{ colorId: 241, price: 1500 },
				{ colorId: 242, price: 1500 },
				{ colorId: 243, price: 1500 },
				{ colorId: 244, price: 1500 },
				{ colorId: 245, price: 1500 },
				{ colorId: 246, price: 1500 },
				{ colorId: 247, price: 1500 },
				{ colorId: 248, price: 1500 },
				{ colorId: 249, price: 1500 },
				{ colorId: 250, price: 1500 },
			],
		},
	},
	{
		action: ColorType.VMT_PAINT4,
		title: 'vehicleTuning.colorMenu.VMT_PAINT4',
		eventName: 'client.tuning.setWheelColor',
		colors: {
			vehPriceRate: 0.25,
			items: [
				{ colorId: 181, price: 3000 },
				{ colorId: 182, price: 3000 },
				{ colorId: 183, price: 3000 },
				{ colorId: 184, price: 3000 },
				{ colorId: 185, price: 3000 },
				{ colorId: 186, price: 3000 },
				{ colorId: 187, price: 3000 },
				{ colorId: 188, price: 3000 },
				{ colorId: 189, price: 3000 },
				{ colorId: 190, price: 3000 },
				{ colorId: 191, price: 3000 },
				{ colorId: 192, price: 3000 },
				{ colorId: 193, price: 3000 },
				{ colorId: 194, price: 3000 },
				{ colorId: 195, price: 3000 },
				{ colorId: 196, price: 3000 },
				{ colorId: 197, price: 3000 },
				{ colorId: 198, price: 3000 },
				{ colorId: 199, price: 3000 },
				{ colorId: 200, price: 3000 },
				{ colorId: 201, price: 3000 },
				{ colorId: 202, price: 3000 },
				{ colorId: 203, price: 3000 },
				{ colorId: 204, price: 3000 },
				{ colorId: 205, price: 3000 },
				{ colorId: 206, price: 3000 },
				{ colorId: 207, price: 3000 },
				{ colorId: 208, price: 3000 },
				{ colorId: 209, price: 3000 },
				{ colorId: 210, price: 3000 },
				{ colorId: 211, price: 3000 },
				{ colorId: 212, price: 3000 },
				{ colorId: 213, price: 3000 },
				{ colorId: 214, price: 3000 },
				{ colorId: 215, price: 3000 },
				{ colorId: 216, price: 3000 },
				{ colorId: 217, price: 3000 },
				{ colorId: 218, price: 3000 },
				{ colorId: 219, price: 3000 },
				{ colorId: 220, price: 3000 },
				{ colorId: 221, price: 3000 },
				{ colorId: 222, price: 3000 },
				{ colorId: 223, price: 3000 },
				{ colorId: 224, price: 3000 },
				{ colorId: 225, price: 3000 },
				{ colorId: 226, price: 3000 },
				{ colorId: 227, price: 3000 },
				{ colorId: 228, price: 3000 },
				{ colorId: 229, price: 3000 },
				{ colorId: 230, price: 3000 },
				{ colorId: 231, price: 3000 },
				{ colorId: 232, price: 3000 },
				{ colorId: 233, price: 3000 },
				{ colorId: 234, price: 3000 },
				{ colorId: 235, price: 3000 },
				{ colorId: 236, price: 3000 },
				{ colorId: 237, price: 3000 },
				{ colorId: 238, price: 3000 },
				{ colorId: 239, price: 3000 },
				{ colorId: 240, price: 3000 },
				{ colorId: 241, price: 3000 },
				{ colorId: 242, price: 3000 },
				{ colorId: 243, price: 3000 },
				{ colorId: 244, price: 3000 },
				{ colorId: 245, price: 3000 },
				{ colorId: 246, price: 3000 },
				{ colorId: 247, price: 3000 },
				{ colorId: 248, price: 3000 },
				{ colorId: 249, price: 3000 },
				{ colorId: 250, price: 3000 },
			],
		},
	},
	{
		action: ColorType.VMT_PAINT6,
		title: 'vehicleTuning.colorMenu.VMT_PAINT6',
		eventName: 'client.tuning.setInteriorColour',
		colors: {
			vehPriceRate: 0.2,
			items: [
				{ colorId: 181, price: 4500 },
				{ colorId: 182, price: 4500 },
				{ colorId: 183, price: 4500 },
				{ colorId: 184, price: 4500 },
				{ colorId: 185, price: 4500 },
				{ colorId: 186, price: 4500 },
				{ colorId: 187, price: 4500 },
				{ colorId: 188, price: 4500 },
				{ colorId: 189, price: 4500 },
				{ colorId: 190, price: 4500 },
				{ colorId: 191, price: 4500 },
				{ colorId: 192, price: 4500 },
				{ colorId: 193, price: 4500 },
				{ colorId: 194, price: 4500 },
				{ colorId: 195, price: 4500 },
				{ colorId: 196, price: 4500 },
				{ colorId: 197, price: 4500 },
				{ colorId: 198, price: 4500 },
				{ colorId: 199, price: 4500 },
				{ colorId: 200, price: 4500 },
				{ colorId: 201, price: 4500 },
				{ colorId: 202, price: 4500 },
				{ colorId: 203, price: 4500 },
				{ colorId: 204, price: 4500 },
				{ colorId: 205, price: 4500 },
				{ colorId: 206, price: 4500 },
				{ colorId: 207, price: 4500 },
				{ colorId: 208, price: 4500 },
				{ colorId: 209, price: 4500 },
				{ colorId: 210, price: 4500 },
				{ colorId: 211, price: 4500 },
				{ colorId: 212, price: 4500 },
				{ colorId: 213, price: 4500 },
				{ colorId: 214, price: 4500 },
				{ colorId: 215, price: 4500 },
				{ colorId: 216, price: 4500 },
				{ colorId: 217, price: 4500 },
				{ colorId: 218, price: 4500 },
				{ colorId: 219, price: 4500 },
				{ colorId: 220, price: 4500 },
				{ colorId: 221, price: 4500 },
				{ colorId: 222, price: 4500 },
				{ colorId: 223, price: 4500 },
				{ colorId: 224, price: 4500 },
				{ colorId: 225, price: 4500 },
				{ colorId: 226, price: 4500 },
				{ colorId: 227, price: 4500 },
				{ colorId: 228, price: 4500 },
				{ colorId: 229, price: 4500 },
				{ colorId: 230, price: 4500 },
				{ colorId: 231, price: 4500 },
				{ colorId: 232, price: 4500 },
				{ colorId: 233, price: 4500 },
				{ colorId: 234, price: 4500 },
				{ colorId: 235, price: 4500 },
				{ colorId: 236, price: 4500 },
				{ colorId: 237, price: 4500 },
				{ colorId: 238, price: 4500 },
				{ colorId: 239, price: 4500 },
				{ colorId: 240, price: 4500 },
				{ colorId: 241, price: 4500 },
				{ colorId: 242, price: 4500 },
				{ colorId: 243, price: 4500 },
				{ colorId: 244, price: 4500 },
				{ colorId: 245, price: 4500 },
				{ colorId: 246, price: 4500 },
				{ colorId: 247, price: 4500 },
				{ colorId: 248, price: 4500 },
				{ colorId: 249, price: 4500 },
				{ colorId: 250, price: 4500 },
			],
		},
	},
	{
		action: ColorType.VMT_PAINT7,
		title: 'vehicleTuning.colorMenu.VMT_PAINT7',
		eventName: 'client.tuning.setDashboardColour',
		colors: {
			vehPriceRate: 0.2,
			items: [
				{ colorId: 181, price: 4500 },
				{ colorId: 182, price: 4500 },
				{ colorId: 183, price: 4500 },
				{ colorId: 184, price: 4500 },
				{ colorId: 185, price: 4500 },
				{ colorId: 186, price: 4500 },
				{ colorId: 187, price: 4500 },
				{ colorId: 188, price: 4500 },
				{ colorId: 189, price: 4500 },
				{ colorId: 190, price: 4500 },
				{ colorId: 191, price: 4500 },
				{ colorId: 192, price: 4500 },
				{ colorId: 193, price: 4500 },
				{ colorId: 194, price: 4500 },
				{ colorId: 195, price: 4500 },
				{ colorId: 196, price: 4500 },
				{ colorId: 197, price: 4500 },
				{ colorId: 198, price: 4500 },
				{ colorId: 199, price: 4500 },
				{ colorId: 200, price: 4500 },
				{ colorId: 201, price: 4500 },
				{ colorId: 202, price: 4500 },
				{ colorId: 203, price: 4500 },
				{ colorId: 204, price: 4500 },
				{ colorId: 205, price: 4500 },
				{ colorId: 206, price: 4500 },
				{ colorId: 207, price: 4500 },
				{ colorId: 208, price: 4500 },
				{ colorId: 209, price: 4500 },
				{ colorId: 210, price: 4500 },
				{ colorId: 211, price: 4500 },
				{ colorId: 212, price: 4500 },
				{ colorId: 213, price: 4500 },
				{ colorId: 214, price: 4500 },
				{ colorId: 215, price: 4500 },
				{ colorId: 216, price: 4500 },
				{ colorId: 217, price: 4500 },
				{ colorId: 218, price: 4500 },
				{ colorId: 219, price: 4500 },
				{ colorId: 220, price: 4500 },
				{ colorId: 221, price: 4500 },
				{ colorId: 222, price: 4500 },
				{ colorId: 223, price: 4500 },
				{ colorId: 224, price: 4500 },
				{ colorId: 225, price: 4500 },
				{ colorId: 226, price: 4500 },
				{ colorId: 227, price: 4500 },
				{ colorId: 228, price: 4500 },
				{ colorId: 229, price: 4500 },
				{ colorId: 230, price: 4500 },
				{ colorId: 231, price: 4500 },
				{ colorId: 232, price: 4500 },
				{ colorId: 233, price: 4500 },
				{ colorId: 234, price: 4500 },
				{ colorId: 235, price: 4500 },
				{ colorId: 236, price: 4500 },
				{ colorId: 237, price: 4500 },
				{ colorId: 238, price: 4500 },
				{ colorId: 239, price: 4500 },
				{ colorId: 240, price: 4500 },
				{ colorId: 241, price: 4500 },
				{ colorId: 242, price: 4500 },
				{ colorId: 243, price: 4500 },
				{ colorId: 244, price: 4500 },
				{ colorId: 245, price: 4500 },
				{ colorId: 246, price: 4500 },
				{ colorId: 247, price: 4500 },
				{ colorId: 248, price: 4500 },
				{ colorId: 249, price: 4500 },
				{ colorId: 250, price: 4500 },
			],
		},
	},
	{
		action: ColorType.VMT_PAINT_LIGHTBAR,
		title: 'vehicleTuning.colorMenu.VMT_PAINT_LIGHTBAR',
		eventName: 'client.tuning.setXenonColor',
		colors: {
			vehPriceRate: 0.5,
			items: [
				{ colorId: 0, hex: `#FFDF75`, price: 2500 },
				{ colorId: 1, hex: `#FFEBAB`, price: 2500 },
				{ colorId: 2, hex: `#FFE864`, price: 2500 },
				{ colorId: 3, hex: `#E8CE34`, price: 2500 },
				{ colorId: 4, hex: `#A3B3FF`, price: 5000 },
				{ colorId: 5, hex: `#6D79B5`, price: 5000 },
				{ colorId: 6, hex: `#4D67EB`, price: 5000 },
				{ colorId: 7, hex: `#6495ED`, price: 5000 },
				{ colorId: 8, hex: `#B5DBFF`, price: 10000 },
				{ colorId: 9, hex: `#B3F1FF`, price: 10000 },
				{ colorId: 10, hex: `#87CEEB`, price: 10000 },
				{ colorId: 11, hex: `#C7DCEC`, price: 10000 },
			],
		},
	},
	{
		action: ColorType.VMT_PAINT_TYRE_SMOKE,
		title: 'vehicleTuning.colorMenu.VMT_PAINT_TYRE_SMOKE',
		eventName: 'client.tuning.setTyreSmokeColor',
		colorRgb: {
			vehPriceRate: 0.2,
			rgbColorPrice: 1499,
			items: [
				{ id: 1, hex: `#FFFFFF`, price: 7000 },
				{ id: 2, hex: `#FAFAFA`, price: 8000 },
				{ id: 3, hex: `#F5F5F5`, price: 9000 },
				{ id: 4, hex: `#E5E5E5`, price: 10000 },
				{ id: 5, hex: `#D4D4D4`, price: 11000 },
				{ id: 6, hex: `#A3A3A3`, price: 12000 },
				{ id: 7, hex: `#737373`, price: 13000 },
				{ id: 8, hex: `#525252`, price: 14000 },
				{ id: 9, hex: `#404040`, price: 15000 },
				{ id: 10, hex: `#262626`, price: 16000 },
				{ id: 11, hex: `#171717`, price: 17000 },
				{ id: 12, hex: `#0A0A0A`, price: 18000 },
				{ id: 13, hex: `#080808`, price: 19000 },
				{ id: 14, hex: `#010101`, price: 20000 },
			],
		},
	},
	{
		action: ColorType.VMT_PAINT_NEON,
		title: 'vehicleTuning.colorMenu.VMT_PAINT_NEON',
		eventName: 'client.tuning.setNeonLightsColour',
		colorRgb: {
			vehPriceRate: 0.5,
			rgbColorPrice: 999,
			items: [
				{ id: 1, hex: `#FFFFFF`, price: 5000 },
				{ id: 2, hex: `#4338CA`, price: 5000 },
				{ id: 3, hex: `#1D4ED8`, price: 5000 },
				{ id: 4, hex: `#0E7490`, price: 5000 },
				{ id: 5, hex: `#0F766E`, price: 5000 },
				{ id: 6, hex: `#15803D`, price: 5000 },
				{ id: 7, hex: `#4D7C0F`, price: 5000 },
				{ id: 8, hex: `#A16207`, price: 5000 },
				{ id: 9, hex: `#B45309`, price: 5000 },
				{ id: 10, hex: `#C2410C`, price: 5000 },
				{ id: 11, hex: `#AB1313`, price: 5000 },
				{ id: 12, hex: `#BE123C`, price: 5000 },
				{ id: 13, hex: `#A21CAF`, price: 5000 },
				{ id: 14, hex: `#9333EA`, price: 5000 },
			],
		},
	},
];

export const vehicleColors: {
	id: number;
	material: number;
	description: string;
	hex: string;
	rgb: string;
	hasClose?: boolean;
}[] = [
	{
		id: 0,
		material: 4,
		description: 'Metallic Black',
		hex: '#080808',
		rgb: '13, 17, 22',
	},
	{
		id: 1,
		material: 4,
		description: 'Metallic Graphite Black',
		hex: '#0F0F0F',
		rgb: '28, 29, 33',
	},
	{
		id: 2,
		material: 2,
		description: 'Metallic Black Steal',
		hex: '#1C1E21',
		rgb: '50, 56, 61',
	},
	{
		id: 3,
		material: 2,
		description: 'Metallic Dark Silver',
		hex: '#292C2E',
		rgb: '69, 75, 79',
	},
	{
		id: 4,
		material: 2,
		description: 'Metallic Silver',
		hex: '#5A5E66',
		rgb: '153, 157, 160',
	},
	{
		id: 5,
		material: 2,
		description: 'Metallic Blue Silver',
		hex: '#777C87',
		rgb: '194, 196, 198',
	},
	{
		id: 6,
		material: 2,
		description: 'Metallic Steel Gray',
		hex: '#515459',
		rgb: '151, 154, 151',
	},
	{
		id: 7,
		material: 2,
		description: 'Metallic Shadow Silver',
		hex: '#323B47',
		rgb: '99, 115, 128',
	},
	{
		id: 8,
		material: 2,
		description: 'Metallic Stone Silver',
		hex: '#333333',
		rgb: '99, 98, 92',
	},
	{
		id: 9,
		material: 2,
		description: 'Metallic Midnight Silver',
		hex: '#1F2226',
		rgb: '60, 63, 71',
	},
	{
		id: 10,
		material: 2,
		description: 'Metallic Gun Metal',
		hex: '#23292E',
		rgb: '68, 78, 84',
	},
	{
		id: 11,
		material: 2,
		description: 'Metallic Anthracite Grey',
		hex: '#121110',
		rgb: '29, 33, 41',
	},
	{
		id: 12,
		material: 5,
		description: 'Matte Black',
		hex: '#050505',
		rgb: '19, 24, 31',
	},
	{
		id: 13,
		material: 5,
		description: 'Matte Gray',
		hex: '#121212',
		rgb: '38, 40, 42',
	},
	{
		id: 14,
		material: 5,
		description: 'Matte Light Grey',
		hex: '#2F3233',
		rgb: '81, 85, 84',
	},
	{
		id: 15,
		material: 3,
		description: 'Util Black',
		hex: '#080808',
		rgb: '21, 25, 33',
		hasClose: true,
	},
	{
		id: 16,
		material: 3,
		description: 'Util Black Poly',
		hex: '#121212',
		rgb: '30, 36, 41',
		hasClose: true,
	},
	{
		id: 17,
		material: 3,
		description: 'Util Dark Silver',
		hex: '#202224',
		rgb: '51, 58, 60',
		hasClose: true,
	},
	{
		id: 18,
		material: 3,
		description: 'Util Silver',
		hex: '#575961',
		rgb: '140, 144, 149',
		hasClose: true,
	},
	{
		id: 19,
		material: 3,
		description: 'Util Gun Metal',
		hex: '#23292E',
		rgb: '57, 67, 77',
		hasClose: true,
	},
	{
		id: 20,
		material: 3,
		description: 'Util Shadow Silver',
		hex: '#323B47',
		rgb: '80, 98, 114',
		hasClose: true,
	},
	{
		id: 21,
		material: 6,
		description: 'Worn Black',
		hex: '#0F1012',
		rgb: '30, 35, 47',
		hasClose: true,
	},
	{
		id: 22,
		material: 6,
		description: 'Worn Graphite',
		hex: '#212121',
		rgb: '54, 58, 63',
		hasClose: true,
	},
	{
		id: 23,
		material: 6,
		description: 'Worn Silver Grey',
		hex: '#5B5D5E',
		rgb: '160, 161, 153',
		hasClose: true,
	},
	{
		id: 24,
		material: 6,
		description: 'Worn Silver',
		hex: '#888A99',
		rgb: '211, 211, 211',
		hasClose: true,
	},
	{
		id: 25,
		material: 6,
		description: 'Worn Blue Silver',
		hex: '#697187',
		rgb: '183, 191, 202',
	},
	{
		id: 26,
		material: 6,
		description: 'Worn Shadow Silver',
		hex: '#3B4654',
		rgb: '119, 135, 148',
		hasClose: true,
	},
	{
		id: 27,
		material: 2,
		description: 'Metallic Red',
		hex: '#690000',
		rgb: '192, 14, 26',
	},
	{
		id: 28,
		material: 2,
		description: 'Metallic Torino Red',
		hex: '#8A0B00',
		rgb: '218, 25, 24',
	},
	{
		id: 29,
		material: 2,
		description: 'Metallic Formula Red',
		hex: '#6B0000',
		rgb: '182, 17, 27',
	},
	{
		id: 30,
		material: 2,
		description: 'Metallic Blaze Red',
		hex: '#611009',
		rgb: '165, 30, 35',
	},
	{
		id: 31,
		material: 2,
		description: 'Metallic Graceful Red',
		hex: '#4A0A0A',
		rgb: '123, 26, 34',
	},
	{
		id: 32,
		material: 2,
		description: 'Metallic Garnet Red',
		hex: '#470E0E',
		rgb: '142, 27, 31',
	},
	{
		id: 33,
		material: 2,
		description: 'Metallic Desert Red',
		hex: '#380C00',
		rgb: '111, 24, 24',
	},
	{
		id: 34,
		material: 2,
		description: 'Metallic Cabernet Red',
		hex: '#26030B',
		rgb: '73, 17, 29',
	},
	{
		id: 35,
		material: 2,
		description: 'Metallic Candy Red',
		hex: '#630012',
		rgb: '182, 15, 37',
	},
	{
		id: 36,
		material: 2,
		description: 'Metallic Sunrise Orange',
		hex: '#802800',
		rgb: '212, 74, 23',
	},
	{
		id: 37,
		material: 2,
		description: 'Metallic Classic Gold',
		hex: '#6E4F2D',
		rgb: '194, 148, 79',
		hasClose: true,
	},
	{
		id: 38,
		material: 2,
		description: 'Metallic Orange',
		hex: '#BD4800',
		rgb: '247, 134, 22',
	},
	{
		id: 39,
		material: 5,
		description: 'Matte Red',
		hex: '#780000',
		rgb: '207, 31, 33',
	},
	{
		id: 40,
		material: 5,
		description: 'Matte Dark Red',
		hex: '#360000',
		rgb: '115, 32, 33',
	},
	{
		id: 41,
		material: 5,
		description: 'Matte Orange',
		hex: '#AB3F00',
		rgb: '242, 125, 32',
	},
	{
		id: 42,
		material: 5,
		description: 'Matte Yellow',
		hex: '#DE7E00',
		rgb: '255, 201, 31',
	},
	{
		id: 43,
		material: 3,
		description: 'Util Red',
		hex: '#520000',
		rgb: '156, 16, 22',
		hasClose: true,
	},
	{
		id: 44,
		material: 3,
		description: 'Util Bright Red',
		hex: '#8C0404',
		rgb: '222, 15, 24',
		hasClose: true,
	},
	{
		id: 45,
		material: 2,
		description: 'Util Garnet Red',
		hex: '#4A1000',
		rgb: '143, 30, 23',
		hasClose: true,
	},
	{
		id: 46,
		material: 6,
		description: 'Worn Red',
		hex: '#592525',
		rgb: '169, 71, 68',
		hasClose: true,
	},
	{
		id: 47,
		material: 6,
		description: 'Worn Golden Red',
		hex: '#754231',
		rgb: '177, 108, 81',
		hasClose: true,
	},
	{
		id: 48,
		material: 6,
		description: 'Worn Dark Red',
		hex: '#210804',
		rgb: '55, 28, 37',
		hasClose: true,
	},
	{
		id: 49,
		material: 2,
		description: 'Metallic Dark Green',
		hex: '#001207',
		rgb: '19, 36, 40',
	},
	{
		id: 50,
		material: 2,
		description: 'Metallic Racing Green',
		hex: '#001A0B',
		rgb: '18, 46, 43',
	},
	{
		id: 51,
		material: 2,
		description: 'Metallic Sea Green',
		hex: '#00211E',
		rgb: '18, 56, 60',
	},
	{
		id: 52,
		material: 2,
		description: 'Metallic Olive Green',
		hex: '#1F261E',
		rgb: '49, 66, 63',
	},
	{
		id: 53,
		material: 2,
		description: 'Metallic Green',
		hex: '#003805',
		rgb: '21, 92, 45',
	},
	{
		id: 54,
		material: 2,
		description: 'Metallic Gasoline Blue Green',
		hex: '#0B4145',
		rgb: '27, 103, 112',
	},
	{
		id: 55,
		material: 5,
		description: 'Matte Lime Green',
		hex: '#418503',
		rgb: '102, 184, 31',
	},
	{
		id: 56,
		material: 3,
		description: 'Util Dark Green',
		hex: '#0F1F15',
		rgb: '34, 56, 62',
		hasClose: true,
	},
	{
		id: 57,
		material: 3,
		description: 'Util Green',
		hex: '#023613',
		rgb: '29, 90, 63',
		hasClose: true,
	},
	{
		id: 58,
		material: 6,
		description: 'Worn Dark Green',
		hex: '#162419',
		rgb: '45, 66, 63',
		hasClose: true,
	},
	{
		id: 59,
		material: 6,
		description: 'Worn Green',
		hex: '#2A3625',
		rgb: '69, 89, 75',
		hasClose: true,
	},
	{
		id: 60,
		material: 6,
		description: 'Worn Sea Wash',
		hex: '#455C56',
		rgb: '101, 134, 127',
		hasClose: true,
	},
	{
		id: 61,
		material: 2,
		description: 'Metallic Midnight Blue',
		hex: '#000D14',
		rgb: '34, 46, 70',
	},
	{
		id: 62,
		material: 2,
		description: 'Metallic Dark Blue',
		hex: '#001029',
		rgb: '35, 49, 85',
	},
	{
		id: 63,
		material: 2,
		description: 'Metallic Saxony Blue',
		hex: '#1C2F4F',
		rgb: '48, 76, 126',
	},
	{
		id: 64,
		material: 2,
		description: 'Metallic Blue',
		hex: '#001B57',
		rgb: '71, 87, 143',
	},
	{
		id: 65,
		material: 2,
		description: 'Metallic Mariner Blue',
		hex: '#3B4E78',
		rgb: '99, 123, 167',
	},
	{
		id: 66,
		material: 2,
		description: 'Metallic Harbor Blue',
		hex: '#272D3B',
		rgb: '57, 71, 98',
	},
	{
		id: 67,
		material: 1,
		description: 'Metallic Diamond Blue',
		hex: '#95B2DB',
		rgb: '214, 231, 241',
	},
	{
		id: 68,
		material: 2,
		description: 'Metallic Surf Blue',
		hex: '#3E627A',
		rgb: '118, 175, 190',
	},
	{
		id: 69,
		material: 2,
		description: 'Metallic Nautical Blue',
		hex: '#1C3140',
		rgb: '52, 94, 114',
	},
	{
		id: 70,
		material: 2,
		description: 'Metallic Bright Blue',
		hex: '#0055C4',
		rgb: '11, 156, 241',
	},
	{
		id: 71,
		material: 2,
		description: 'Metallic Purple Blue',
		hex: '#1A182E',
		rgb: '47, 45, 82',
	},
	{
		id: 72,
		material: 2,
		description: 'Metallic Spinnaker Blue',
		hex: '#161629',
		rgb: '40, 44, 77',
	},
	{
		id: 73,
		material: 2,
		description: 'Metallic Ultra Blue',
		hex: '#0E316D',
		rgb: '35, 84, 161',
	},
	{
		id: 74,
		material: 2,
		description: 'Metallic Bright Blue',
		hex: '#395A83',
		rgb: '110, 163, 198',
	},
	{
		id: 75,
		material: 3,
		description: 'Util Dark Blue',
		hex: '#09142E',
		rgb: '17, 37, 82',
		hasClose: true,
	},
	{
		id: 76,
		material: 3,
		description: 'Util Midnight Blue',
		hex: '#0F1021',
		rgb: '27, 32, 62',
		hasClose: true,
	},
	{
		id: 77,
		material: 3,
		description: 'Util Blue',
		hex: '#152A52',
		rgb: '39, 81, 144',
		hasClose: true,
	},
	{
		id: 78,
		material: 3,
		description: 'Util Sea Foam Blue',
		hex: '#324654',
		rgb: '96, 133, 146',
		hasClose: true,
	},
	{
		id: 79,
		material: 3,
		description: 'Util Lightning Blue',
		hex: '#152563',
		rgb: '36, 70, 168',
		hasClose: true,
	},
	{
		id: 80,
		material: 3,
		description: 'Util Maui Blue Poly',
		hex: '#223BA1',
		rgb: '66, 113, 225',
		hasClose: true,
	},
	{
		id: 81,
		material: 3,
		description: 'Util Bright Blue',
		hex: '#1F1FA1',
		rgb: '59, 57, 224',
		hasClose: true,
	},
	{
		id: 82,
		material: 5,
		description: 'Matte Dark Blue',
		hex: '#030E2E',
		rgb: '31, 40, 82',
	},
	{
		id: 83,
		material: 5,
		description: 'Matte Blue',
		hex: '#0F1E73',
		rgb: '37, 58, 167',
	},
	{
		id: 84,
		material: 5,
		description: 'Matte Midnight Blue',
		hex: '#001C32',
		rgb: '28, 53, 81',
	},
	{
		id: 85,
		material: 6,
		description: 'Worn Dark Blue',
		hex: '#2A3754',
		rgb: '76, 95, 129',
		hasClose: true,
	},
	{
		id: 86,
		material: 6,
		description: 'Worn Blue',
		hex: '#0303C5E',
		rgb: '88, 104, 142',
		hasClose: true,
	},
	{
		id: 87,
		material: 6,
		description: 'Worn Light Blue',
		hex: '#3B6796',
		rgb: '116, 181, 216',
		hasClose: true,
	},
	{
		id: 88,
		material: 2,
		description: 'Metallic Taxi Yellow',
		hex: '#F5890F',
		rgb: '255, 207, 32',
	},
	{
		id: 89,
		material: 2,
		description: 'Metallic Race Yellow',
		hex: '#D9A600',
		rgb: '251, 226, 18',
	},
	{
		id: 90,
		material: 2,
		description: 'Metallic Bronze',
		hex: '#4A341B',
		rgb: '145, 101, 50',
	},
	{
		id: 91,
		material: 2,
		description: 'Metallic Yellow Bird',
		hex: '#A2A827',
		rgb: '224, 225, 61',
	},
	{
		id: 92,
		material: 2,
		description: 'Metallic Lime',
		hex: '#568F00',
		rgb: '152, 210, 35',
	},
	{
		id: 93,
		material: 2,
		description: 'Metallic Champagne',
		hex: '#57514B',
		rgb: '155, 140, 120',
		hasClose: true,
	},
	{
		id: 94,
		material: 2,
		description: 'Metallic Pueblo Beige',
		hex: '#291B06',
		rgb: '80, 50, 24',
	},
	{
		id: 95,
		material: 2,
		description: 'Metallic Dark Ivory',
		hex: '#262117',
		rgb: '71, 63, 43',
	},
	{
		id: 96,
		material: 2,
		description: 'Metallic Choco Brown',
		hex: '#120D07',
		rgb: '34, 27, 25',
	},
	{
		id: 97,
		material: 2,
		description: 'Metallic Golden Brown',
		hex: '#332111',
		rgb: '101, 63, 35',
	},
	{
		id: 98,
		material: 2,
		description: 'Metallic Light Brown',
		hex: '#3D3023',
		rgb: '119, 92, 62',
	},
	{
		id: 99,
		material: 2,
		description: 'Metallic Straw Beige',
		hex: '#5E5343',
		rgb: '172, 153, 117',
	},
	{
		id: 100,
		material: 2,
		description: 'Metallic Moss Brown',
		hex: '#37382B',
		rgb: '108, 107, 75',
	},
	{
		id: 101,
		material: 2,
		description: 'Metallic Biston Brown',
		hex: '#221918',
		rgb: '64, 46, 43',
	},
	{
		id: 102,
		material: 2,
		description: 'Metallic Beechwood',
		hex: '#575036',
		rgb: '164, 150, 95',
	},
	{
		id: 103,
		material: 2,
		description: 'Metallic Dark Beechwood',
		hex: '#F241309',
		rgb: '70, 35, 26',
	},
	{
		id: 104,
		material: 2,
		description: 'Metallic Choco Orange',
		hex: '#3B1700',
		rgb: '117, 43, 25',
	},
	{
		id: 105,
		material: 2,
		description: 'Metallic Beach Sand',
		hex: '#6E6246',
		rgb: '191, 174, 123',
	},
	{
		id: 106,
		material: 2,
		description: 'Metallic Sun Bleeched Sand',
		hex: '#998D73',
		rgb: '223, 213, 178',
	},
	{
		id: 107,
		material: 2,
		description: 'Metallic Cream',
		hex: '#CFC0A5',
		rgb: '247, 237, 213',
	},
	{
		id: 108,
		material: 3,
		description: 'Util Brown',
		hex: '#1F1709',
		rgb: '58, 42, 27',
		hasClose: true,
	},
	{
		id: 109,
		material: 3,
		description: 'Util Medium Brown',
		hex: '#3D311D',
		rgb: '120, 95, 51',
		hasClose: true,
	},
	{
		id: 110,
		material: 3,
		description: 'Util Light Brown',
		hex: '#665847',
		rgb: '181, 160, 121',
		hasClose: true,
	},
	{
		id: 111,
		material: 1,
		description: 'Metallic White',
		hex: '#F0F0F0',
		rgb: '255, 255, 246',
	},
	{
		id: 112,
		material: 2,
		description: 'Metallic Frost White',
		hex: '#B3B9C9',
		rgb: '234, 234, 234',
	},
	{
		id: 113,
		material: 6,
		description: 'Worn Honey Beige',
		hex: '#615F55',
		rgb: '176, 171, 148',
		hasClose: true,
	},
	{
		id: 114,
		material: 6,
		description: 'Worn Brown',
		hex: '#241E1A',
		rgb: '69, 56, 49',
		hasClose: true,
	},
	{
		id: 115,
		material: 6,
		description: 'Worn Dark Brown',
		hex: '#171413',
		rgb: '42, 40, 43',
		hasClose: true,
	},
	{
		id: 116,
		material: 6,
		description: 'Worn Straw Beige',
		hex: '#3B372F',
		rgb: '114, 108, 87',
		hasClose: true,
	},
	{
		id: 117,
		material: 8,
		description: 'Brushed Steel',
		hex: '#3B4045',
		rgb: '106, 116, 124',
	},
	{
		id: 118,
		material: 8,
		description: 'Brushed Black Steel',
		hex: '#1A1E21',
		rgb: '53, 65, 88',
	},
	{
		id: 119,
		material: 8,
		description: 'Brushed Aluminium',
		hex: '#5E646B',
		rgb: '155, 160, 168',
	},
	{
		id: 120,
		material: 10,
		description: 'Chrome',
		hex: '#000000',
		rgb: '88, 112, 161',
	},
	{
		id: 121,
		material: 6,
		description: 'Worn Off White',
		hex: '#B0B0B0',
		rgb: '234, 230, 222',
		hasClose: true,
	},
	{
		id: 122,
		material: 3,
		description: 'Util Off White',
		hex: '#999999',
		rgb: '223, 221, 208',
		hasClose: true,
	},
	{
		id: 123,
		material: 6,
		description: 'Worn Orange',
		hex: '#B56519',
		rgb: '242, 173, 46',
		hasClose: true,
	},
	{
		id: 124,
		material: 6,
		description: 'Worn Light Orange',
		hex: '#C45C33',
		rgb: '249, 164, 88',
		hasClose: true,
	},
	{
		id: 125,
		material: 2,
		description: 'Metallic Securicor Green',
		hex: '#47783C',
		rgb: '131, 197, 102',
		hasClose: true,
	},
	{
		id: 126,
		material: 6,
		description: 'Worn Taxi Yellow',
		hex: '#BA8425',
		rgb: '241, 204, 64',
		hasClose: true,
	},
	{
		id: 127,
		material: 2,
		description: 'Police Car Blue',
		hex: '#2A77A1',
		rgb: '76, 195, 218',
		hasClose: true,
	},
	{
		id: 128,
		material: 5,
		description: 'Matte Green',
		hex: '#243022',
		rgb: '78, 100, 67',
	},
	{
		id: 129,
		material: 5,
		description: 'Matte Brown',
		hex: '#6B5F54',
		rgb: '188, 172, 143',
		hasClose: true,
	},
	{
		id: 130,
		material: 6,
		description: 'Worn Orange',
		hex: '#C96E34',
		rgb: '248, 182, 88',
		hasClose: true,
	},
	{
		id: 131,
		material: 5,
		description: 'Matte White',
		hex: '#D9D9D9',
		rgb: '252, 249, 241',
	},
	{
		id: 132,
		material: 6,
		description: 'Worn White',
		hex: '#F0F0F0',
		rgb: '255, 255, 251',
		hasClose: true,
	},
	{
		id: 133,
		material: 5,
		description: 'Worn Olive Army Green',
		hex: '#3F4228',
		rgb: '129, 132, 76',
		hasClose: true,
	},
	{
		id: 134,
		material: 2,
		description: 'Pure White',
		hex: '#FFFFFF',
		rgb: '255, 255, 255',
		hasClose: true,
	},
	{
		id: 135,
		material: 2,
		description: 'Hot Pink',
		hex: '#B01259',
		rgb: '242, 31, 153',
	},
	{
		id: 136,
		material: 2,
		description: 'Salmon Pink',
		hex: '#F69799',
		rgb: '253, 214, 205',
	},
	{
		id: 137,
		material: 2,
		description: 'Metallic Vermillion Pink',
		hex: '#8F2F55',
		rgb: '223, 88, 145',
	},
	{
		id: 138,
		material: 2,
		description: 'Orange',
		hex: '#C26610',
		rgb: '246, 174, 32',
	},
	{
		id: 139,
		material: 2,
		description: 'Green',
		hex: '#69BD45',
		rgb: '176, 238, 110',
		hasClose: true,
	},
	{
		id: 140,
		material: 2,
		description: 'Blue',
		hex: '#00AEEF',
		rgb: '8, 233, 250',
		hasClose: true,
	},
	{
		id: 141,
		material: 4,
		description: 'Mettalic Black Blue',
		hex: '#000108',
		rgb: '10, 12, 23',
	},
	{
		id: 142,
		material: 4,
		description: 'Metallic Black Purple',
		hex: '#050008',
		rgb: '12, 13, 24',
	},
	{
		id: 143,
		material: 4,
		description: 'Metallic Black Red',
		hex: '#080000',
		rgb: '14, 13, 20',
	},
	{
		id: 144,
		material: 2,
		description: 'Hunter Green',
		hex: '#565751',
		rgb: '159, 158, 138',
		hasClose: true,
	},
	{
		id: 145,
		material: 2,
		description: 'Metallic Purple',
		hex: '#320642',
		rgb: '98, 18, 118',
	},
	{
		id: 146,
		material: 2,
		description: 'Metaillic V Dark Blue',
		hex: '#00080F',
		rgb: '11, 20, 33',
		hasClose: true,
	},
	{
		id: 147,
		material: 4,
		description: 'Modshop Black',
		hex: '#080808',
		rgb: '17, 20, 26',
	},
	{
		id: 148,
		material: 5,
		description: 'Matte Purple',
		hex: '#320642',
		rgb: '107, 31, 123',
	},
	{
		id: 149,
		material: 5,
		description: 'Matte Dark Purple',
		hex: '#050008',
		rgb: '30, 29, 34',
	},
	{
		id: 150,
		material: 2,
		description: 'Metallic Lava Red',
		hex: '#6B0B00',
		rgb: '188, 25, 23',
	},
	{
		id: 151,
		material: 5,
		description: 'Matte Forest Green',
		hex: '#121710',
		rgb: '45, 54, 42',
	},
	{
		id: 152,
		material: 5,
		description: 'Matte Olive Drab',
		hex: '#323325',
		rgb: '105, 103, 72',
	},
	{
		id: 153,
		material: 5,
		description: 'Matte Desert Brown',
		hex: '#3B352D',
		rgb: '122, 108, 85',
	},
	{
		id: 154,
		material: 5,
		description: 'Matte Desert Tan',
		hex: '#706656',
		rgb: '195, 180, 146',
	},
	{
		id: 155,
		material: 5,
		description: 'Matte Foilage Green',
		hex: '#2B302B',
		rgb: '90, 99, 82',
	},
	{
		id: 156,
		material: 4,
		description: 'Default Alloy',
		hex: '#414347',
		rgb: '129, 130, 127',
		hasClose: true,
	},
	{
		id: 157,
		material: 1,
		description: 'Epsilon Blue',
		hex: '#6690B5',
		rgb: '175, 214, 228',
		hasClose: true,
	},
	{
		id: 158,
		material: 9,
		description: 'Pure Gold',
		hex: '#47391B',
		rgb: '122, 100, 64',
	},
	{
		id: 159,
		material: 7,
		description: 'Brushed Gold',
		hex: '#47391B',
		rgb: '127, 106, 72',
	},
	{
		id: 160,
		material: 9,
		description: 'Brushed Gold',
		hex: '#FFD859',
		rgb: '127, 106, 72',
	},
	{
		id: 161,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 162,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 163,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 164,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 165,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 166,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 167,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 168,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 169,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 170,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 171,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 172,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 173,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 174,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 175,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 176,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 177,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 178,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 179,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 180,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 181,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#FFFFFF',
		rgb: '0, 0, 0',
	},
	{
		id: 182,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#E7E5E4',
		rgb: '0, 0, 0',
	},
	{
		id: 183,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#E2E8F0',
		rgb: '0, 0, 0',
	},
	{
		id: 184,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#A5B4FC',
		rgb: '0, 0, 0',
	},
	{
		id: 185,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#93C5FD',
		rgb: '0, 0, 0',
	},
	{
		id: 186,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#67E8F9',
		rgb: '0, 0, 0',
	},
	{
		id: 187,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#5EEAD4',
		rgb: '0, 0, 0',
	},
	{
		id: 188,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#86EFAC',
		rgb: '0, 0, 0',
	},
	{
		id: 189,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#BEF264',
		rgb: '0, 0, 0',
	},
	{
		id: 190,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#FDE047',
		rgb: '0, 0, 0',
	},
	{
		id: 191,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#FDBA74',
		rgb: '0, 0, 0',
	},
	{
		id: 192,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#FCA5A5',
		rgb: '0, 0, 0',
	},
	{
		id: 193,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#FDA4AF',
		rgb: '0, 0, 0',
	},
	{
		id: 194,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#D8B4FE',
		rgb: '0, 0, 0',
	},
	{
		id: 195,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#C9C9C9',
		rgb: '0, 0, 0',
	},
	{
		id: 196,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#A8A29E',
		rgb: '0, 0, 0',
	},
	{
		id: 197,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#94A3B8',
		rgb: '0, 0, 0',
	},
	{
		id: 198,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#6366F1',
		rgb: '0, 0, 0',
	},
	{
		id: 199,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#3B82F6',
		rgb: '0, 0, 0',
	},
	{
		id: 200,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#06B6D4',
		rgb: '0, 0, 0',
	},
	{
		id: 201,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#14B8A6',
		rgb: '0, 0, 0',
	},
	{
		id: 202,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#22C55E',
		rgb: '0, 0, 0',
	},
	{
		id: 203,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#84CC16',
		rgb: '0, 0, 0',
	},
	{
		id: 204,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#EAB308',
		rgb: '0, 0, 0',
	},
	{
		id: 205,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#F97316',
		rgb: '0, 0, 0',
	},
	{
		id: 206,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#EF4444',
		rgb: '0, 0, 0',
	},
	{
		id: 207,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#F43F5E',
		rgb: '0, 0, 0',
	},
	{
		id: 208,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#A855F7',
		rgb: '0, 0, 0',
	},
	{
		id: 209,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#858585',
		rgb: '0, 0, 0',
	},
	{
		id: 210,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#57534E',
		rgb: '0, 0, 0',
	},
	{
		id: 211,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#475569',
		rgb: '0, 0, 0',
	},
	{
		id: 212,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#4338CA',
		rgb: '0, 0, 0',
	},
	{
		id: 213,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#1D4ED8',
		rgb: '0, 0, 0',
	},
	{
		id: 214,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#0E7490',
		rgb: '0, 0, 0',
	},
	{
		id: 215,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#0F766E',
		rgb: '0, 0, 0',
	},
	{
		id: 216,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#15803D',
		rgb: '0, 0, 0',
	},
	{
		id: 217,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#4D7C0F',
		rgb: '0, 0, 0',
	},
	{
		id: 218,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#A16207',
		rgb: '0, 0, 0',
	},
	{
		id: 219,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#C2410C',
		rgb: '0, 0, 0',
	},
	{
		id: 220,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#AB1313',
		rgb: '0, 0, 0',
	},
	{
		id: 221,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#BE123C',
		rgb: '0, 0, 0',
	},
	{
		id: 222,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#9333EA',
		rgb: '0, 0, 0',
	},
	{
		id: 223,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#292524',
		rgb: '0, 0, 0',
	},
	{
		id: 224,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#404040',
		rgb: '0, 0, 0',
	},
	{
		id: 225,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#1E293B',
		rgb: '0, 0, 0',
	},
	{
		id: 226,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#312E81',
		rgb: '0, 0, 0',
	},
	{
		id: 227,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#1E3A8A',
		rgb: '0, 0, 0',
	},
	{
		id: 228,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#164E63',
		rgb: '0, 0, 0',
	},
	{
		id: 229,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#134E4A',
		rgb: '0, 0, 0',
	},
	{
		id: 230,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#14532D',
		rgb: '0, 0, 0',
	},
	{
		id: 231,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#365314',
		rgb: '0, 0, 0',
	},
	{
		id: 232,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#713F12',
		rgb: '0, 0, 0',
	},
	{
		id: 233,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#7C2D12',
		rgb: '0, 0, 0',
	},
	{
		id: 234,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#8F1313',
		rgb: '0, 0, 0',
	},
	{
		id: 235,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#881337',
		rgb: '0, 0, 0',
	},
	{
		id: 236,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#6B21A8',
		rgb: '0, 0, 0',
	},
	{
		id: 237,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 238,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#0C0A09',
		rgb: '0, 0, 0',
	},
	{
		id: 239,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#020617',
		rgb: '0, 0, 0',
	},
	{
		id: 240,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#1E1B4B',
		rgb: '0, 0, 0',
	},
	{
		id: 241,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#082F49',
		rgb: '0, 0, 0',
	},
	{
		id: 242,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#083344',
		rgb: '0, 0, 0',
	},
	{
		id: 243,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#042F2E',
		rgb: '0, 0, 0',
	},
	{
		id: 244,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#052E16',
		rgb: '0, 0, 0',
	},
	{
		id: 245,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#1A2E05',
		rgb: '0, 0, 0',
	},
	{
		id: 246,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#422006',
		rgb: '0, 0, 0',
	},
	{
		id: 247,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#431407',
		rgb: '0, 0, 0',
	},
	{
		id: 248,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#470707',
		rgb: '0, 0, 0',
	},
	{
		id: 249,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#4C0519',
		rgb: '0, 0, 0',
	},
	{
		id: 250,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#3B0764',
		rgb: '0, 0, 0',
	},
	{
		id: 251,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 252,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 253,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 254,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
	{
		id: 255,
		material: 1,
		description: 'MAJESTIC COLOR',
		hex: '#000000',
		rgb: '0, 0, 0',
	},
];
