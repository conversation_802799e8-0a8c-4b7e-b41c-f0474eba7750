export const vehicleMods: {
	[key: string]: number;
} = {
	// drift: -11,
	// autopilot: -10,
	// fHandBrakeForce: -9,
	// fSteeringLock: -8,
	// fTractionCurveMax: -7,
	// fTractionCurveMin: -6,
	// fSuspensionRaise: -5,
	// fAntiRollBarForce: -4,
	wheelType: -3,
	engine: 11,
	brakes: 12,
	transmission: 13,
	horn: 14,
	suspension: 15,
	turbo: 18,
	xenon: 22,
	wheels: 23,
	wheelsBike: 24,
	livery: 48,
	windowTint: 55,

	//
	neonId: 107,
	numberPlateType: 102,
	driftTyre: 103,
	reducedDrift: 104,
	gpsTracker: 105,
	bikeSafetyBelt: 106,
	//
	suspensionFront: 110,
	suspensionRear: 115,
	widthFront: 111,
	widthRear: 112,
	camberFront: 113,
	camberRear: 114,

	// Color
	mainColor: 120,
	mainColorMod: 121,
	extraColor: 122,
	extraColorMod: 123,
	neonColor: 100,
	wheelColor: 101,
	tyreSmokeColor: 124,
	xenonColor: 125,
	interiorColor: 126,
	dashboardColor: 127,
	pearlColor: 128,
	doorTazer: 129,
	signaling: 130,
};

export const basicVehicleMods = [
	'engine',
	'brakes',
	'transmission',
	'suspension',
	'turbo',

	'gpsTracker',
	'bikeSafetyBelt',
	'doorTazer',
	'signaling',
] as const;

export const notInstallMods = [vehicleMods['wheels'], vehicleMods['wheelsBike'], vehicleMods['windowTint'], 50, 51, 52];

export const types: { [key: string]: string } = {
	main: 'main',
	buy: 'type-buy',
	upgrade: 'type-upgrade',
	list: 'type-list',
	additional: 'type-additional',
	wheel: 'type-wheel',
	install: 'type-install',
	lock: 'type-lock',
};

export enum VehicleType {
	Car = 1,
	Bike = 2,
	Heli = 3,
	Plane = 4,
	Boat = 5,
	EmergencyBoat = 6,
}

export const technicalMods: {
	title: string;
	modType?: number;
	type?: string;
	eventName?: string;
	updateCamera?: boolean;
	wheelConfig?: {
		modType: number;
		step: number;
	}[];
	accessType?: VehicleType[];
	blockType?: VehicleType[];
	svg?: string;
	desc?: string;
	doors?: number[];
}[] = [
	{
		title: 'VMT_ENGINE',
		modType: vehicleMods['engine'],
		updateCamera: true,
		doors: [5, 6],
	},
	{
		title: 'VMT_BRAKES',
		modType: vehicleMods['brakes'],
		accessType: [VehicleType.Car, VehicleType.Bike],
		updateCamera: true,
	},
	{
		title: 'VMT_GEARBOX',
		modType: vehicleMods['transmission'],
		accessType: [VehicleType.Car, VehicleType.Bike],
		updateCamera: true,
	},
	// {
	//     title: 'VMT_HORN',
	//     modType: vehicleMods['horn'],
	//     svg: 'img/tuning/details/14.svg?1',
	//     eventName: 'client.tuning.typeSelect',
	//     type: types['main'],
	// },
	// {
	//     title: 'VMT_SUSPENSION',
	//     modType: vehicleMods['suspension'],
	//     svg: 'img/tuning/details/15.svg?1',
	//     updateCamera: true,
	// },
	{
		title: 'VMT_TURBO',
		modType: vehicleMods['turbo'],
		accessType: [VehicleType.Car, VehicleType.Bike],
	},
	{ title: 'VMT_REDUCED_GRIP_TIRES', modType: vehicleMods['driftTyre'], accessType: [VehicleType.Car] }, //
	// {
	// 	title: 'VMT_DRIFT_REDUCTION_SUSPENSION',
	// 	modType: vehicleMods['reducedDrift'],
	// 	accessType: [VehicleType.Car],
	// }, //
	// {
	// 	title: 'VMT_MOD_SUSPENSION',
	// 	wheelConfig: [
	// 		{ modType: vehicleMods['suspensionFront'], step: 0.001 }, // Высота подвески: top - на сколько можно поднять вверх, bottom - на сколько опустить
	// 	],
	// 	accessType: [VehicleType.Car],
	// 	eventName: 'client.tuning.resetTuningMod',
	// },
	{
		title: 'VMT_MOD_FRONT_WHEELS',
		wheelConfig: [
			{ modType: vehicleMods['suspensionFront'], step: 0.001 },
			{ modType: vehicleMods['widthFront'], step: 0.001 }, // Наклон колеса: top - на наклонить верх, bottom - на наклонить вниз,
			// { modType: vehicleMods['camberFront'], step: 0.001 }, // Выдвинуть колеса: inside - на выдвинуть внутрь, bottom - на выдвинуть наружу
		],
		accessType: [VehicleType.Car],
		eventName: 'client.tuning.resetTuningMod',
	},
	{
		title: 'VMT_MOD_REAR_WHEELS',
		wheelConfig: [
			{ modType: vehicleMods['suspensionRear'], step: 0.001 },
			{ modType: vehicleMods['widthRear'], step: 0.001 }, // Наклон колеса: top - на наклонить верх, bottom - на наклонить вниз,
			// { modType: vehicleMods['camberRear'], step: 0.001 }, // Выдвинуть колеса: inside - на выдвинуть внутрь, bottom - на выдвинуть наружу
		],
		accessType: [VehicleType.Car],
		eventName: 'client.tuning.resetTuningMod',
	},
];

export const stylingMods: {
	title: string;
	modType: number;
	count?: number;
	eventName?: string;
	type?: string;
	accessType?: VehicleType[];
	blockType?: VehicleType[];
	svg?: string;
	desc?: string;
	doors?: number[];
}[] = [
	{ title: 'VMT_SPOILER', modType: 0 },
	{ title: 'VMT_SPOILER', modType: 200, svg: 'icons/tuningNew/mods/0.svg' },
	{ title: 'VMT_BUMPER_F', modType: 1 },
	{ title: 'VMT_BUMPER_F', modType: 201, svg: 'icons/tuningNew/mods/1.svg' },
	{ title: 'VMT_BUMPER_R', modType: 2 },
	{ title: 'VMT_BUMPER_R', modType: 202, svg: 'icons/tuningNew/mods/2.svg' },
	{ title: 'VMT_SKIRT', modType: 3 },
	{ title: 'VMT_SKIRT', modType: 203, svg: 'icons/tuningNew/mods/3.svg' },
	{ title: 'VMT_EXHAUST', modType: 4 },
	{ title: 'VMT_EXHAUST', modType: 204, svg: 'icons/tuningNew/mods/4.svg' },
	{ title: 'VMT_CHASSIS', modType: 5 },
	{ title: 'VMT_CHASSIS', modType: 205, svg: 'icons/tuningNew/mods/5.svg' },
	{ title: 'VMT_GRILL', modType: 6 },
	{ title: 'VMT_GRILL', modType: 206, svg: 'icons/tuningNew/mods/6.svg' },
	{ title: 'VMT_BONNET', modType: 7 },
	{ title: 'VMT_BONNET', modType: 207, svg: 'icons/tuningNew/mods/7.svg' },
	{ title: 'VMT_WING_L', modType: 8 },
	{ title: 'VMT_WING_L', modType: 208, svg: 'icons/tuningNew/mods/8.svg' },
	{ title: 'VMT_WING_R', modType: 9 },
	{ title: 'VMT_WING_R', modType: 209, svg: 'icons/tuningNew/mods/9.svg' },
	{ title: 'VMT_ROOF', modType: 10 },
	{ title: 'VMT_ROOF', modType: 210, svg: 'icons/tuningNew/mods/10.svg' },
	// { title: 'VMT_WHEELS_REAR_OR_HYDRAULICS', modType: 24 }, // Заднее колесо для мотоцикла
	// { title: 'VMT_PLTHOLDER', modType: 25 }, // Скрыто в тестовом режиме
	// { title: 'VMT_PLTVANITY', modType: 26 }, // Скрыто в тестовом режиме
	{ title: 'VMT_KNOB', modType: 34 },
	{ title: 'VMT_KNOB', modType: 234, svg: 'icons/tuningNew/mods/34.svg' },
	{ title: 'VMT_PLAQUE', modType: 35 },
	{ title: 'VMT_PLAQUE', modType: 235, svg: 'icons/tuningNew/mods/35.svg' },
	// { title: 'VMT_TYRE_SMOKE', modType: 20 }, // Проверить что это
	{ title: 'VMT_ICE', modType: 36 },
	{ title: 'VMT_ICE', modType: 236, svg: 'icons/tuningNew/mods/36.svg' },
	{ title: 'VMT_TRUNK', modType: 37 },
	{ title: 'VMT_TRUNK', modType: 237, svg: 'icons/tuningNew/mods/37.svg' },
	{ title: 'VMT_HYDRO', modType: 38 },
	{ title: 'VMT_HYDRO', modType: 238, svg: 'icons/tuningNew/mods/38.svg' },
	{ title: 'VMT_ENGINEBAY1', modType: 39 },
	{ title: 'VMT_ENGINEBAY1', modType: 239, svg: 'icons/tuningNew/mods/39.svg' },
	{ title: 'VMT_ENGINEBAY2', modType: 40 },
	{ title: 'VMT_ENGINEBAY2', modType: 240, svg: 'icons/tuningNew/mods/40.svg' },
	{ title: 'VMT_ENGINEBAY3', modType: 41 },
	{ title: 'VMT_ENGINEBAY3', modType: 241, svg: 'icons/tuningNew/mods/41.svg' },
	{ title: 'VMT_CHASSIS2', modType: 42 },
	{ title: 'VMT_CHASSIS2', modType: 242, svg: 'icons/tuningNew/mods/42.svg' },
	{ title: 'VMT_CHASSIS3', modType: 43 },
	{ title: 'VMT_CHASSIS3', modType: 243, svg: 'icons/tuningNew/mods/43.svg' },
	{ title: 'VMT_CHASSIS4', modType: 44 },
	{ title: 'VMT_CHASSIS4', modType: 244, svg: 'icons/tuningNew/mods/44.svg' },
	{ title: 'VMT_CHASSIS5', modType: 45 },
	{ title: 'VMT_CHASSIS5', modType: 245, svg: 'icons/tuningNew/mods/45.svg' },
	{ title: 'VMT_DOOR_L', modType: 46 },
	{ title: 'VMT_DOOR_L', modType: 246, svg: 'icons/tuningNew/mods/46.svg' },
	{ title: 'VMT_DOOR_R', modType: 47 },
	{ title: 'VMT_DOOR_R', modType: 247, svg: 'icons/tuningNew/mods/47.svg' },
	{ title: 'VMT_INTERIOR5', modType: 31 },
	{ title: 'VMT_INTERIOR5', modType: 231, svg: 'icons/tuningNew/mods/31.svg' },
	{ title: 'VMT_INTERIOR4', modType: 30 },
	{ title: 'VMT_INTERIOR4', modType: 230, svg: 'icons/tuningNew/mods/30.svg' },
	{ title: 'VMT_INTERIOR3', modType: 29 },
	{ title: 'VMT_INTERIOR3', modType: 229, svg: 'icons/tuningNew/mods/29.svg' },
	{ title: 'VMT_INTERIOR2', modType: 28 },
	{ title: 'VMT_INTERIOR2', modType: 228, svg: 'icons/tuningNew/mods/28.svg' },
	{ title: 'VMT_INTERIOR1', modType: 27 },
	{ title: 'VMT_INTERIOR1', modType: 227, svg: 'icons/tuningNew/mods/27.svg' },
	{ title: 'VMT_SEATS', modType: 32 },
	{ title: 'VMT_SEATS', modType: 232, svg: 'icons/tuningNew/mods/32.svg' },
	{ title: 'VMT_STEERING', modType: 33 },
	{ title: 'VMT_STEERING', modType: 233, svg: 'icons/tuningNew/mods/33.svg' },
	// { title: 'VMT_LIVERY_MOD', modType: 48 },
	// { title: 'VMT_LIVERY_MOD', modType: 248 },
	{ title: 'VMT_TONER', modType: vehicleMods['windowTint'], accessType: [VehicleType.Car, VehicleType.Bike] },
	{ title: 'VMT_NUMBER_PLATE', modType: vehicleMods['numberPlateType'], accessType: [VehicleType.Car, VehicleType.Bike] },
	{ title: 'VMT_NEON', modType: vehicleMods['neonId'], accessType: [VehicleType.Car] },
	{
		title: 'VMT_HORN',
		modType: vehicleMods['horn'],
		eventName: 'client.tuning.typeSelect',
		type: types['main'],
		accessType: [VehicleType.Car, VehicleType.Bike],
	},
];

export const modTypeToName: { [key: number]: string } = {
	25: 'VMT_PLTHOLDER',
	26: 'VMT_PLTVANITY',
	48: 'VMT_LIVERY_MOD',
};
export const modTypeToDoors: { [key: number]: number[] } = {};

[...technicalMods, ...stylingMods].forEach((item) => {
	if (typeof item.modType !== 'number') {
		return;
	}
	modTypeToName[item.modType] = item.title;
	if (item.doors) {
		modTypeToDoors[item.modType] = item.doors;
	}
});

export const wheelTypes: { title: string; type?: number; seasonPass?: number; limit?: number; isBike?: boolean }[] = [
	{ title: 'Winter 2024', seasonPass: 4 },
	{ title: 'Summer 2023', seasonPass: 3 },
	{ title: 'Winter 2023', seasonPass: 2 },
	{ title: 'Summer 2022', seasonPass: 1 },
	{ title: 'Sport', type: 0, limit: 50 }, // Не знаю почему, но в этой категории с клиента не приходят адекватные цифры. Поэтому такой лимит задаю
	{ title: 'Muscle', type: 1, limit: 35 }, // Лимит из-за колёс сезонного пропуска winter2024
	{ title: 'Lowrider', type: 2 },
	{ title: 'SUV', type: 3 },
	{ title: 'Offroad', type: 4 },
	{ title: 'Tuner', type: 5 },
	{ title: 'Bike Wheels', type: 6, isBike: true },
	{ title: 'High End', type: 7, limit: 40 }, // Лимит из-за колёс сезонного пропуска summer2022, winter2023, summer2023
	{ title: "Benny's Original", type: 8 },
	{ title: "Benny's Bespoke", type: 9 },
	{ title: 'OpenWheel', type: 10 },
	{ title: 'Streets', type: 11 },
	{ title: 'Tracks', type: 12 },
];

export const horns: {
	[key: number]: {
		title: string;
		value: number;
		duration: number;
	};
} = {
	[-1]: {
		// Стандартный клаксон
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_-1',
		value: -1,
		duration: 1000,
	},
	1: {
		// Нота - До
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_16',
		value: 16,
		duration: 1000,
	},
	2: {
		// Нота - Ре
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_17',
		value: 17,
		duration: 1000,
	},
	3: {
		// Нота - Ми
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_18',
		value: 18,
		duration: 1000,
	},
	4: {
		// Нота - Фа
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_19',
		value: 19,
		duration: 1000,
	},
	5: {
		// Нота - Соль
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_20',
		value: 20,
		duration: 1000,
	},
	6: {
		// Нота - Ля
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_21',
		value: 21,
		duration: 1000,
	},
	7: {
		// Нота - Си
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_22',
		value: 22,
		duration: 1000,
	},
	8: {
		// Нота - До (Выс.)
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_23',
		value: 23,
		duration: 1000,
	},
	9: {
		// Спортивный гудок
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_51',
		value: 51,
		duration: 1500,
	},
	10: {
		// Спортивный гудок 2
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_53',
		value: 53,
		duration: 1500,
	},
	11: {
		// Спортивный гудок 3
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_55',
		value: 55,
		duration: 1500,
	},
	12: {
		// Звездный флаг 1
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_28',
		value: 28,
		duration: 3000,
	},
	13: {
		// Звездный флаг 2
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_29',
		value: 29,
		duration: 3000,
	},
	14: {
		// Звездный флаг 3
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_30',
		duration: 3000,
		value: 30,
	},
	15: {
		// Звездный флаг 4
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_31',
		duration: 3000,
		value: 31,
	},
	16: {
		// Музыкальный клаксон 1
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_3',
		value: 3,
		duration: 3500,
	},
	17: {
		// Музыкальный клаксон 2
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_4',
		value: 4,
		duration: 5500,
	},
	18: {
		// Музыкальный клаксон 3
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_5',
		value: 5,
		duration: 4500,
	},
	19: {
		// Музыкальный клаксон 4
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_6',
		value: 6,
		duration: 4500,
	},
	20: {
		// Музыкальный клаксон 5
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_7',
		value: 7,
		duration: 4500,
	},
	21: {
		// Классический клаксон 1
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_9',
		value: 9,
		duration: 5500,
	},
	22: {
		// Классический клаксон 2
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_10',
		value: 10,
		duration: 5500,
	},
	23: {
		// Классический клаксон 3
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_11',
		value: 11,
		duration: 5500,
	},
	24: {
		// Классический клаксон 4
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_12',
		value: 12,
		duration: 4500,
	},
	25: {
		// Классический клаксон 5
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_13',
		value: 13,
		duration: 4500,
	},
	26: {
		// Классический клаксон 6
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_14',
		value: 14,
		duration: 4500,
	},
	27: {
		// Классический клаксон 7
		title: 'vehicleTuning.horns.VMT_HORN_INDEX_15',
		value: 15,
		duration: 4500,
	},
	// 0: { // Клаксон грузовика
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_0',
	// 	duration: 1000,
	// },
	// 1: { // Полицейский клаксон
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_1',
	// 	duration: 1000,
	// },
	// 2: { // Клоунский клаксон
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_2',
	// 	duration: 1000,
	// },
	// 8: { // Печальная труба
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_8',
	// 	duration: 4500,
	// },
	// 24: { // Джаз-клаксон 1
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_24',
	// 	duration: 1500,
	// },
	// 25: { // Джаз-клаксон 2
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_25',
	// 	duration: 1500,
	// },
	// 26: { // Джаз-клаксон 3
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_26',
	// 	duration: 1500,
	// },
	// 27: { // Джаз-клаксон (повтор)
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_27',
	// 	duration: 2500,
	// },
	// 32: { // Классический клаксон 8 (повтор)
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_32',
	// 	duration: 2500,
	// },
	// 33: { // Классический 1 (повтор)
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_33',
	// 	duration: 4500,
	// },
	// 34: { // Классический 2 (повтор)
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_34',
	// 	duration: 4500,
	// },
	// 37: { // Хеллуин 1
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_37',
	// 	duration: 5500,
	// },
	// 39: { // Хеллуин 2
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_39',
	// 	duration: 5500,
	// },
	// 41: { // Сан-Андреас (повтор)
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_41',
	// 	duration: 5500,
	// },
	// 43: { // Либерти-Сити (повтор)
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_43',
	// 	duration: 5500,
	// },
	// 45: { // Праздничный 1 (повтор)
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_45',
	// 	duration: 5500,
	// },
	// 46: { // Праздничный 1
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_46',
	// 	duration: 5500,
	// },
	// 47: { // Праздничный 2 (повтор)
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_47',
	// 	duration: 5500,
	// },
	// 48: { // Праздничный 2
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_48',
	// 	duration: 5500,
	// },
	// 49: { // Праздничный 3 (повтор)
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_49',
	// 	duration: 5500,
	// },
	// 50: { // Праздничный 3
	// 	title: 'vehicleTuning.horns.VMT_HORN_INDEX_50',
	// 	duration: 5500,
	// },
};

export const driftTyres: {
	[key: number]: {
		title: string;
		value: number;
	};
} = {
	[-1]: {
		title: 'Выключено',
		value: -1,
	},
	0: {
		title: 'Включено',
		value: 0,
	},
};

export const reducedDrifts: {
	[key: number]: {
		title: string;
		value: number;
	};
} = {
	[-1]: {
		title: 'Выключено',
		value: -1,
	},
	0: {
		title: 'Включено',
		value: 0,
	},
};

export const turbos: {
	[key: number]: {
		title: string;
		value: number;
	};
} = {
	[-1]: {
		title: 'Выключено',
		value: -1,
	},
	0: {
		title: 'Включено',
		value: 0,
	},
};
export const blockedModLabels: string[] = ['WTD_V_COM_MG', 'WT_V_AKU_MN'];

export const stockLabels = {
	[vehicleMods['engine']]: 'CMOD_ENG_0',
	[vehicleMods['brakes']]: 'CMOD_BRA_0',
	[vehicleMods['transmission']]: 'CMOD_GBX_0',
	[vehicleMods['suspension']]: 'CMOD_SUS_0',
	[vehicleMods['turbo']]: 'turbo',
	[vehicleMods['driftTyre']]: 'driftTyre',
	[vehicleMods['reducedDrift']]: 'reducedDrift',
	[vehicleMods['wheelSuspension']]: 'wheelSuspension',
	[vehicleMods['wheelFront']]: 'wheelFront',
	[vehicleMods['wheelBack']]: 'wheelBack',
};

export const neons: {
	[key: number]: {
		title: string;
		indexes: number[];
	};
} = {
	[-1]: {
		title: 'vehicleTuning.neons.VMT_CUSTOM_NEON_INDEX_0',
		indexes: [],
	},
	1: {
		title: 'vehicleTuning.neons.VMT_CUSTOM_NEON_INDEX_1',
		indexes: [2],
	},
	2: {
		title: 'vehicleTuning.neons.VMT_CUSTOM_NEON_INDEX_2',
		indexes: [3],
	},
	3: {
		title: 'vehicleTuning.neons.VMT_CUSTOM_NEON_INDEX_3',
		indexes: [2, 3],
	},
	4: {
		title: 'vehicleTuning.neons.VMT_CUSTOM_NEON_INDEX_4',
		indexes: [0, 1],
	},
	5: {
		title: 'vehicleTuning.neons.VMT_CUSTOM_NEON_INDEX_5',
		indexes: [0, 1, 2],
	},
	6: {
		title: 'vehicleTuning.neons.VMT_CUSTOM_NEON_INDEX_6',
		indexes: [0, 1, 3],
	},
	7: {
		title: 'vehicleTuning.neons.VMT_CUSTOM_NEON_INDEX_7',
		indexes: [0, 1, 2, 3],
	},
};

export const windowTints: {
	[key: number]: {
		title: string;
		value: number;
	};
} = {
	[-1]: {
		title: 'vehicleTuning.windowTints.VMT_CUSTOM_TINT_INDEX_0',
		value: -1,
	},
	1: {
		title: 'vehicleTuning.windowTints.VMT_CUSTOM_TINT_INDEX_1',
		value: 3,
	},
	2: {
		title: 'vehicleTuning.windowTints.VMT_CUSTOM_TINT_INDEX_2',
		value: 4,
	},
	3: {
		title: 'vehicleTuning.windowTints.VMT_CUSTOM_TINT_INDEX_3',
		value: 5,
	},
	4: {
		title: 'vehicleTuning.windowTints.VMT_CUSTOM_TINT_INDEX_4',
		value: 6,
	},
	5: {
		title: 'vehicleTuning.windowTints.VMT_CUSTOM_TINT_INDEX_5',
		value: 7,
	},
	6: {
		title: 'vehicleTuning.windowTints.VMT_CUSTOM_TINT_INDEX_6',
		value: 8,
	},
	7: {
		title: 'vehicleTuning.windowTints.VMT_CUSTOM_TINT_INDEX_7',
		value: 9,
	},
	8: {
		title: 'vehicleTuning.windowTints.VMT_CUSTOM_TINT_INDEX_8',
		value: 10,
	},
	9: {
		title: 'vehicleTuning.windowTints.VMT_CUSTOM_TINT_INDEX_9',
		value: 11,
	},
	10: {
		title: 'vehicleTuning.windowTints.VMT_CUSTOM_TINT_INDEX_10',
		value: 12,
	},
};

export const numberPlates: {
	[key: number]: {
		title: string;
		value: number;
	};
} = {
	[-1]: {
		// Синий на белом
		title: 'vehicleTuning.plates.VMT_CUSTOM_PLATE_INDEX_0',
		value: -1,
	},
	0: {
		// Чёрный на белом
		title: 'vehicleTuning.plates.VMT_CUSTOM_PLATE_INDEX_1',
		value: 2,
	},
	1: {
		// Жёлтый на синем
		title: 'vehicleTuning.plates.VMT_CUSTOM_PLATE_INDEX_2',
		value: 1,
	},
	2: {
		// Жёлтый на чёрном
		title: 'vehicleTuning.plates.VMT_CUSTOM_PLATE_INDEX_3',
		value: 0,
	},
	29: {
		// Test 1
		title: 'vehicleTuning.plates.VMT_CUSTOM_PLATE_INDEX_1',
		value: 29,
	},
	30: {
		// Test 2
		title: 'vehicleTuning.plates.VMT_CUSTOM_PLATE_INDEX_2',
		value: 30,
	},
	31: {
		// Test 3
		title: 'vehicleTuning.plates.VMT_CUSTOM_PLATE_INDEX_3',
		value: 31,
	},
	32: {
		// Test 4
		title: 'vehicleTuning.plates.VMT_CUSTOM_PLATE_INDEX_4',
		value: 32,
	},
	33: {
		// Test 5
		title: 'vehicleTuning.plates.VMT_CUSTOM_PLATE_INDEX_5',
		value: 33,
	},
	34: {
		// Test 5
		title: 'vehicleTuning.plates.VMT_CUSTOM_PLATE_INDEX_5',
		value: 34,
	},
	35: {
		// Test 5
		title: 'vehicleTuning.plates.VMT_CUSTOM_PLATE_INDEX_5',
		value: 35,
	},
};

export const modsPrice: {
	modType: number;
	category: string;
	tunTitle: string;
	priceInstall?: number;
	//
	priceInfo?: {
		priceStock: number;
		priceInstall: number;
		priceVehicle: number;
		priceRate: number;
	};
	//
	updradeLimit?: number;
	upgradeSystem?: {
		dawngradePrice: number;
		priceVehicle: number;
		priceRate: number;
	};
	//
	items?: {
		modIndex: number;
		priceInstall: number;
		price?: number;
		genDetails?: number;
		seasonPass?: number;
		case?: string;
	}[];
}[] = [
	// Улучшение двигателя
	{
		modType: 11,
		category: 'technical',
		tunTitle: 'VMT_ENGINE',
		updradeLimit: 5, // На сколько будет увеличиваться ограничение скорости за уровень
		upgradeSystem: { dawngradePrice: 2500, priceVehicle: 2, priceRate: 50 },
	},
	// Улучшение тормозов
	{ modType: 12, category: 'technical', tunTitle: 'VMT_BRAKES', upgradeSystem: { dawngradePrice: 2500, priceVehicle: 0.5, priceRate: 50 } },
	// Улучшение коробки передач
	{
		modType: 13,
		category: 'technical',
		tunTitle: 'VMT_GEARBOX',
		updradeLimit: 4, // На сколько будет увеличиваться ограничение скорости за уровень
		upgradeSystem: { dawngradePrice: 2500, priceVehicle: 1, priceRate: 50 },
	},
	// Турбина
	{
		modType: 18,
		category: 'technical',
		tunTitle: 'VMT_TURBO',
		updradeLimit: 3, // На сколько будет увеличиваться ограничение скорости за уровень
		upgradeSystem: { dawngradePrice: 500, priceVehicle: 4, priceRate: 0 },
	},
	// Спойлер
	{ 
		modType: 0, 
		category: 'styling', 
		tunTitle: 'VMT_SPOILER', 
		priceInfo: { priceStock: 1000, priceInstall: 3000, priceVehicle: 0.4, priceRate: 20 } 
	},
	// Спойлер
	{ 
		modType: 200, 
		category: 'styling', 
		tunTitle: 'VMT_SPOILER', 
		priceInstall: 0,
	},
	// Передний бампер
	{
		modType: 1,
		category: 'styling',
		tunTitle: 'VMT_BUMPER_F',
		priceInfo: { priceStock: 1000, priceInstall: 2500, priceVehicle: 0.35, priceRate: 15 },
	},
	// Передний бампер
	{
		modType: 201,
		category: 'styling',
		tunTitle: 'VMT_BUMPER_F',
		priceInstall: 0,
	},
	// Задний бампер
	{
		modType: 2,
		category: 'styling',
		tunTitle: 'VMT_BUMPER_R',
		priceInfo: { priceStock: 1000, priceInstall: 2500, priceVehicle: 0.35, priceRate: 15 },
	},
	// Задний бампер
	{
		modType: 202,
		category: 'styling',
		tunTitle: 'VMT_BUMPER_R',
		priceInstall: 0,
	},
	// Пороги
	{ 
		modType: 3, 
		category: 'styling', 
		tunTitle: 'VMT_SKIRT', 
		priceInfo: { priceStock: 1000, priceInstall: 2000, priceVehicle: 0.3, priceRate: 15 } 
	},
	// Пороги
	{ 
		modType: 203, 
		category: 'styling', 
		tunTitle: 'VMT_SKIRT', 
		priceInstall: 0,
	},
	// Выхлоп
	{
		modType: 4,
		category: 'styling',
		tunTitle: 'VMT_EXHAUST',
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 20 },
	},
	// Выхлоп
	{
		modType: 204,
		category: 'styling',
		tunTitle: 'VMT_EXHAUST',
		priceInstall: 0,
	},
	// Шасси
	{
		modType: 5,
		category: 'styling',
		tunTitle: 'VMT_CHASSIS',
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 10 },
	},
	// Шасси
	{
		modType: 205,
		category: 'styling',
		tunTitle: 'VMT_CHASSIS',
		priceInstall: 0,
	},
	// Решетка
	{ 
		modType: 6, 
		category: 'styling', 
		tunTitle: 'VMT_GRILL', 
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 5 } 
	},
	// Решетка
	{ 
		modType: 206, 
		category: 'styling', 
		tunTitle: 'VMT_GRILL', 
		priceInstall: 0,
	},
	// Капот
	{ 
		modType: 7, 
		category: 'styling', 
		tunTitle: 'VMT_BONNET', 
		priceInfo: { priceStock: 1000, priceInstall: 2500, priceVehicle: 0.35, priceRate: 10 } 
	},
	// Капот
	{ 
		modType: 207, 
		category: 'styling', 
		tunTitle: 'VMT_BONNET', 
		priceInstall: 0,
	},
	// Крылья
	{ 
		modType: 8, 
		category: 'styling', 
		tunTitle: 'VMT_WING_L', 
		priceInfo: { priceStock: 1000, priceInstall: 2000, priceVehicle: 0.3, priceRate: 5 } 
	},
	// Крылья
	{ 
		modType: 208, 
		category: 'styling', 
		tunTitle: 'VMT_WING_L', 
		priceInstall: 0,
	},
	// Дополнительные крылья
	{ 
		modType: 9, 
		category: 'styling', 
		tunTitle: 'VMT_WING_R', 
		priceInfo: { priceStock: 1000, priceInstall: 2000, priceVehicle: 0.3, priceRate: 5 } 
	},
	// Дополнительные крылья
	{ 
		modType: 209, 
		category: 'styling', 
		tunTitle: 'VMT_WING_R', 
		priceInstall: 0,
	},
	// Крыша
	{ 
		modType: 10, 
		category: 'styling', 
		tunTitle: 'VMT_ROOF', 
		priceInfo: { priceStock: 1000, priceInstall: 2500, priceVehicle: 0.35, priceRate: 20 } 
	},
	// Крыша
	{ 
		modType: 210, 
		category: 'styling', 
		tunTitle: 'VMT_ROOF', 
		priceInstall: 0,
	},
	// Заказной номер
	{
		modType: 25,
		category: 'styling',
		tunTitle: 'VMT_PLTHOLDER',
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 5 },
	},
	// Заказной номер
	{
		modType: 26,
		category: 'styling',
		tunTitle: 'VMT_PLTVANITY',
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 5 },
	},
	// Обивка
	{
		modType: 27,
		category: 'styling',
		tunTitle: 'VMT_INTERIOR1',
		priceInfo: { priceStock: 1000, priceInstall: 2000, priceVehicle: 0.3, priceRate: 20 },
	},
	// Обивка
	{
		modType: 227,
		category: 'styling',
		tunTitle: 'VMT_INTERIOR1',
		priceInstall: 0,
	},
	// Украшения
	{
		modType: 28,
		category: 'styling',
		tunTitle: 'VMT_INTERIOR2',
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 15 },
	},
	// Украшения
	{
		modType: 228,
		category: 'styling',
		tunTitle: 'VMT_INTERIOR2',
		priceInstall: 0,
	},
	// Приборная панель
	{
		modType: 29,
		category: 'styling',
		tunTitle: 'VMT_INTERIOR3',
		priceInfo: { priceStock: 1000, priceInstall: 2000, priceVehicle: 0.3, priceRate: 15 },
	},
	// Приборная панель
	{
		modType: 229,
		category: 'styling',
		tunTitle: 'VMT_INTERIOR3',
		priceInstall: 0,
	},
	// Индикаторы
	{
		modType: 30,
		category: 'styling',
		tunTitle: 'VMT_INTERIOR4',
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 15 },
	},
	// Индикаторы
	{
		modType: 230,
		category: 'styling',
		tunTitle: 'VMT_INTERIOR4',
		priceInstall: 0,
	},
	// Дверная обивка
	{
		modType: 31,
		category: 'styling',
		tunTitle: 'VMT_INTERIOR5',
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 15 },
	},
	// Дверная обивка
	{
		modType: 231,
		category: 'styling',
		tunTitle: 'VMT_INTERIOR5',
		priceInstall: 0,
	},
	// Сиденья
	{ 
		modType: 32, 
		category: 'styling', 
		tunTitle: 'VMT_SEATS', 
		priceInfo: { priceStock: 1000, priceInstall: 2500, priceVehicle: 0.35, priceRate: 5 } 
	},
	// Сиденья
	{ 
		modType: 232, 
		category: 'styling', 
		tunTitle: 'VMT_SEATS', 
		priceInstall: 0,
	},
	// Руль
	{
		modType: 33,
		category: 'styling',
		tunTitle: 'VMT_STEERING',
		priceInfo: { priceStock: 1000, priceInstall: 2000, priceVehicle: 0.3, priceRate: 5 },
	},
	// Руль
	{
		modType: 233,
		category: 'styling',
		tunTitle: 'VMT_STEERING',
		priceInstall: 0,
	},
	// Рычаги передач
	{ 
		modType: 34, 
		category: 'styling', 
		tunTitle: 'VMT_KNOB', 
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 5 } 
	},
	// Рычаги передач
	{ 
		modType: 234, 
		category: 'styling', 
		tunTitle: 'VMT_KNOB', 
		priceInstall: 0,
	},
	// Таблички
	{ 
		modType: 35, 
		category: 'styling', 
		tunTitle: 'VMT_PLAQUE', 
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 5 } 
	},
	// Таблички
	{ 
		modType: 235, 
		category: 'styling', 
		tunTitle: 'VMT_PLAQUE', 
		priceInstall: 0,
	},
	// Мультимедийная система
	{ 
		modType: 36, 
		category: 'styling', 
		tunTitle: 'VMT_ICE', 
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 5 } 
	},
	// Мультимедийная система
	{ 
		modType: 236, 
		category: 'styling', 
		tunTitle: 'VMT_ICE', 
		priceInstall: 0,
	},
	// Багажник
	{ 
		modType: 37, 
		category: 'styling', 
		tunTitle: 'VMT_TRUNK', 
		priceInfo: { priceStock: 1000, priceInstall: 2000, priceVehicle: 0.3, priceRate: 15 } 
	},
	// Багажник
	{ 
		modType: 237, 
		category: 'styling', 
		tunTitle: 'VMT_TRUNK', 
		priceInstall: 0,
	},
	// Гидравлика
	{ 
		modType: 38, 
		category: 'styling', 
		tunTitle: 'VMT_HYDRO', 
		priceInfo: { priceStock: 1000, priceInstall: 2000, priceVehicle: 0.3, priceRate: 20 } 
	},
	// Гидравлика
	{ 
		modType: 238, 
		category: 'styling', 
		tunTitle: 'VMT_HYDRO', 
		priceInstall: 0,
	},
	// Блок двигателя
	{
		modType: 39,
		category: 'styling',
		tunTitle: 'VMT_ENGINEBAY1',
		priceInfo: { priceStock: 1000, priceInstall: 3000, priceVehicle: 0.4, priceRate: 20 },
	},
	// Блок двигателя
	{
		modType: 239,
		category: 'styling',
		tunTitle: 'VMT_ENGINEBAY1',
		priceInstall: 0,
	},
	// Воздущные фильтры
	{
		modType: 40,
		category: 'styling',
		tunTitle: 'VMT_ENGINEBAY2',
		priceInfo: { priceStock: 1000, priceInstall: 3000, priceVehicle: 0.4, priceRate: 20 },
	},
	// Воздущные фильтры
	{
		modType: 240,
		category: 'styling',
		tunTitle: 'VMT_ENGINEBAY2',
		priceInstall: 0,
	},
	// Фитинги
	{
		modType: 41,
		category: 'styling',
		tunTitle: 'VMT_ENGINEBAY3',
		priceInfo: { priceStock: 1000, priceInstall: 3000, priceVehicle: 0.4, priceRate: 15 },
	},
	// Фитинги
	{
		modType: 241,
		category: 'styling',
		tunTitle: 'VMT_ENGINEBAY3',
		priceInstall: 0,
	},
	// Подкрылки
	{
		modType: 42,
		category: 'styling',
		tunTitle: 'VMT_CHASSIS2',
		priceInfo: { priceStock: 1000, priceInstall: 2500, priceVehicle: 0.36, priceRate: 15 },
	},
	// Подкрылки
	{
		modType: 242,
		category: 'styling',
		tunTitle: 'VMT_CHASSIS2',
		priceInstall: 0,
	},
	// Антены
	{
		modType: 43,
		category: 'styling',
		tunTitle: 'VMT_CHASSIS3',
		priceInfo: { priceStock: 1000, priceInstall: 2000, priceVehicle: 0.3, priceRate: 15 },
	},
	// Антены
	{
		modType: 243,
		category: 'styling',
		tunTitle: 'VMT_CHASSIS3',
		priceInstall: 0,
	},
	// Отделка
	{
		modType: 44,
		category: 'styling',
		tunTitle: 'VMT_CHASSIS4',
		priceInfo: { priceStock: 1000, priceInstall: 2000, priceVehicle: 0.3, priceRate: 15 },
	},
	// Отделка
	{
		modType: 244,
		category: 'styling',
		tunTitle: 'VMT_CHASSIS4',
		priceInstall: 0,
	},
	// Топливный бак
	{
		modType: 45,
		category: 'styling',
		tunTitle: 'VMT_CHASSIS5',
		priceInfo: { priceStock: 1000, priceInstall: 2000, priceVehicle: 0.3, priceRate: 15 },
	},
	// Топливный бак
	{
		modType: 245,
		category: 'styling',
		tunTitle: 'VMT_CHASSIS5',
		priceInstall: 0,
	},
	// Двери
	{
		modType: 46,
		category: 'styling',
		tunTitle: 'VMT_DOOR_L',
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 20 },
	},
	// Двери
	{
		modType: 246,
		category: 'styling',
		tunTitle: 'VMT_DOOR_L',
		priceInstall: 0,
	},
	// Дополнительные эл. дверей
	{
		modType: 47,
		category: 'styling',
		tunTitle: 'VMT_DOOR_R',
		priceInfo: { priceStock: 1000, priceInstall: 1500, priceVehicle: 0.25, priceRate: 20 },
	},
	// Дополнительные эл. дверей
	{
		modType: 247,
		category: 'styling',
		tunTitle: 'VMT_DOOR_R',
		priceInstall: 0,
	},
	// Винил
	{
		modType: 48,
		category: 'styling',
		tunTitle: 'VMT_LIVERY_MOD',
		priceInfo: { priceStock: 1000, priceInstall: 10000, priceVehicle: 0.5, priceRate: 25 },
	},
	// Гудок
	{
		modType: 14,
		category: 'styling',
		tunTitle: 'VMT_HORN',
		items: [
			{ modIndex: -1, priceInstall: 1000 },
			{ modIndex: 16, priceInstall: 30000 },
			{ modIndex: 17, priceInstall: 30000 },
			{ modIndex: 18, priceInstall: 30000 },
			{ modIndex: 19, priceInstall: 30000 },
			{ modIndex: 20, priceInstall: 30000 },
			{ modIndex: 21, priceInstall: 30000 },
			{ modIndex: 22, priceInstall: 30000 },
			{ modIndex: 23, priceInstall: 30000 },
			{ modIndex: 51, priceInstall: 90000 },
			{ modIndex: 53, priceInstall: 90000 },
			{ modIndex: 55, priceInstall: 90000 },
			{ modIndex: 28, priceInstall: 110000 },
			{ modIndex: 29, priceInstall: 110000 },
			{ modIndex: 30, priceInstall: 130000 },
			{ modIndex: 31, priceInstall: 130000 },
			{ modIndex: 3, priceInstall: 150000 },
			{ modIndex: 4, priceInstall: 150000 },
			{ modIndex: 5, priceInstall: 160000 },
			{ modIndex: 6, priceInstall: 160000 },
			{ modIndex: 7, priceInstall: 170000 },
			{ modIndex: 9, priceInstall: 170000 },
			{ modIndex: 10, priceInstall: 180000 },
			{ modIndex: 11, priceInstall: 180000 },
			{ modIndex: 12, priceInstall: 190000 },
			{ modIndex: 13, priceInstall: 190000 },
			{ modIndex: 14, priceInstall: 200000 },
			{ modIndex: 15, priceInstall: 200000 },
		],
	},
	// Тонировка
	{
		modType: 55,
		category: 'styling',
		tunTitle: 'VMT_TONER',
		items: [
			{ modIndex: -1, priceInstall: 1000 },
			{ modIndex: 3, priceInstall: 10000 },
			{ modIndex: 4, priceInstall: 20000 },
			{ modIndex: 5, priceInstall: 30000 },
			{ modIndex: 6, priceInstall: 40000 },
			{ modIndex: 7, priceInstall: 50000 },
			{ modIndex: 8, priceInstall: 60000 },
			{ modIndex: 9, priceInstall: 70000 },
			{ modIndex: 10, priceInstall: 80000 },
			{ modIndex: 11, priceInstall: 90000 },
			{ modIndex: 12, priceInstall: 100000 },
		],
	},

	//

	// Неон
	{
		modType: 107,
		category: 'styling',
		tunTitle: 'VMT_NEON',
		items: [
			{ modIndex: -1, priceInstall: 1000 },
			{ modIndex: 1, priceInstall: 50000 },
			{ modIndex: 2, priceInstall: 50000 },
			{ modIndex: 3, priceInstall: 100000 },
			{ modIndex: 4, priceInstall: 100000 },
			{ modIndex: 5, priceInstall: 150000 },
			{ modIndex: 6, priceInstall: 150000 },
			{ modIndex: 7, priceInstall: 200000 },
		],
	},
	// Дизайн номерного знака
	{
		modType: 102,
		category: 'styling',
		tunTitle: 'VMT_PLTHOLDER',
		items: [
			{ modIndex: -1, priceInstall: 20000 },
			{ modIndex: 0, priceInstall: 80000 },
			{ modIndex: 1, priceInstall: 60000 },
			{ modIndex: 2, priceInstall: 40000 },
			{ modIndex: 29, priceInstall: 40000, seasonPass: 6, genDetails: 5001 },
			{ modIndex: 30, priceInstall: 40000, seasonPass: 6, genDetails: 5002 },
			{ modIndex: 31, priceInstall: 40000, seasonPass: 6, genDetails: 5003 },
			{ modIndex: 32, priceInstall: 40000, seasonPass: 6, genDetails: 5004 },
			{ modIndex: 33, priceInstall: 40000, case: 'winterExtra2025', genDetails: 5005 },
			{ modIndex: 34, priceInstall: 40000, case: 'spring2025', genDetails: 5006 },
			{ modIndex: 35, priceInstall: 40000, case: 'spring2025', genDetails: 5007 },
			// { modIndex: 4, coin: 1000 },
		],
	},
	// Шины с пониженным сцеплением (Кастомная категория)
	{
		modType: 103,
		category: 'technical',
		tunTitle: 'VMT_REDUCED_GRIP_TIRES', // Сейчас на сервере оно работает
		priceInfo: { priceStock: 1000, priceInstall: 25000, priceVehicle: 0.005, priceRate: 0 },
	},
	// Подвеска для уменьшение дрейфа (Кастомная категория)
	{
		modType: 104,
		category: 'technical',
		tunTitle: 'VMT_DRIFT_REDUCTION_SUSPENSION', // Сейчас на сервере оно работает
		priceInfo: { priceStock: 1000, priceInstall: 50000, priceVehicle: 0.005, priceRate: 0 },
	},
	// Подвеска (Кастомная категория)
	{
		modType: vehicleMods['suspensionFront'],
		category: 'technical',
		tunTitle: 'VMT_MOD_FRONT_SUSPENSION',
		priceInstall: 25000,
		// modSuspension: { min: 0.02, max: -0.01 }, // min - на сколько ниже, max - на сколько выше
	},
	{
		modType: vehicleMods['suspensionRear'],
		category: 'technical',
		tunTitle: 'VMT_MOD_REAR_SUSPENSION',
		priceInstall: 25000,
		// modSuspension: { min: 0.02, max: -0.01 }, // min - на сколько ниже, max - на сколько выше
	},
	// Регулировка передних колёс (Кастомная категория)
	{
		modType: vehicleMods['widthFront'],
		category: 'technical',
		tunTitle: 'VMT_MOD_FRONT_WHEELS',
		priceInstall: 50000,
		// wheelsWidthFront: { min: 0.005, max: -0.005 }, // min - на сколько уменьшить колесо, max - на сколько увеличить колесо
		// wheelsCamberFront: { min: -0.03, max: 0.005 }, // min - на сколько верх колеса внутрь, max - на сколько верх колеса наружу
	},
	// Регулировка передних колёс (Кастомная категория)
	{
		modType: vehicleMods['camberFront'],
		category: 'technical',
		tunTitle: 'VMT_MOD_FRONT_WHEELS',
		priceInstall: 50000,
		// wheelsWidthFront: { min: 0.005, max: -0.005 }, // min - на сколько уменьшить колесо, max - на сколько увеличить колесо
		// wheelsCamberFront: { min: -0.03, max: 0.005 }, // min - на сколько верх колеса внутрь, max - на сколько верх колеса наружу
	},
	// Регулировка задних колёс (Кастомная категория)
	{
		modType: vehicleMods['widthRear'],
		category: 'technical',
		tunTitle: 'VMT_MOD_REAR_WHEELS',
		priceInstall: 50000,
		// wheelsWidthRear: { min: 0.005, max: -0.005 }, // min - на сколько уменьшить колесо, max - на сколько увеличить колесо
		// wheelsCamberRear: { min: -0.03, max: 0.005 }, // min - на сколько верх колеса внутрь, max - на сколько верх колеса наружу
	},
	// Регулировка задних колёс (Кастомная категория)
	{
		modType: vehicleMods['camberRear'],
		category: 'technical',
		tunTitle: 'VMT_MOD_REAR_WHEELS',
		priceInstall: 50000,
		// wheelsWidthRear: { min: 0.005, max: -0.005 }, // min - на сколько уменьшить колесо, max - на сколько увеличить колесо
		// wheelsCamberRear: { min: -0.03, max: 0.005 }, // min - на сколько верх колеса внутрь, max - на сколько верх колеса наружу
	},
];

export const updradeModsSpeedLimitMap = new Map<number, number>();
export const updradeModsSpeedLimit = modsPrice.reduce((res, el) => {
    if (el.updradeLimit != null) {
        res.push({ modType: el.modType, updradeLimit: el.updradeLimit });
        updradeModsSpeedLimitMap.set(el.modType, el.updradeLimit);
    }
    return res;
}, [] as Array<{ modType: number, updradeLimit: number }>);

export const getModValue = (value: number, min: string = '0.1', max: string = '0.3') => {
	return {
		min: (value - Number(min)).toFixed(3),
		max: (value + Number(max)).toFixed(3),
	};
};

export const getWheelSuspension = (value: number) => {
	return getModValue(value, '0.015', '0.020');
};

export const getWheelCamber = () => {
	return {
		min: '-0.120',
		max: '0.000',
	};
};

export const getWheelTrackWidth = (value: number) => {
	return getModValue(value, '0.000', '0.100');
};

export const getWheelValue = (min: number, max: number, perc: number, hasCamber: boolean = false) => {
	const step = (max - min) / 100;

	const value = hasCamber ? max - step * perc : min + step * perc;

	if (min > value) {
		return min;
	}

	if (max < value) {
		return max;
	}

	return Number(value.toFixed(3));
};

export const tuningPrice = {
	repair: {
		price: 1500,
		title: 'repair',
	},

	install: {
		price: 1500,
		title: 'install',
	},

	installSet: {
		price: 50000,
		title: 'installSet',
	},
};

export const tuningTypes: {
	[modType: string]: string;
} = {
	'0': 'VMT_SPOILER',
	'200': 'VMT_SPOILER',
	'1': 'VMT_BUMPER_F',
	'201': 'VMT_BUMPER_F',
	'2': 'VMT_BUMPER_R',
	'202': 'VMT_BUMPER_R',
	'3': 'VMT_SKIRT',
	'203': 'VMT_SKIRT',
	'4': 'VMT_EXHAUST',
	'204': 'VMT_EXHAUST',
	'5': 'VMT_CHASSIS',
	'205': 'VMT_CHASSIS',
	'6': 'VMT_GRILL',
	'206': 'VMT_GRILL',
	'7': 'VMT_BONNET',
	'207': 'VMT_BONNET',
	'8': 'VMT_WING_L',
	'208': 'VMT_WING_L',
	'9': 'VMT_WING_R',
	'209': 'VMT_WING_R',
	'10': 'VMT_ROOF',
	'210': 'VMT_ROOF',
	'11': 'VMT_ENGINE',
	'12': 'VMT_BRAKES',
	'13': 'VMT_GEARBOX',
	'14': 'VMT_HORN',
	'15': 'VMT_SUSPENSION',
	'16': 'VMT_ARMOUR',
	'17': 'VMT_NITROUS',
	'18': 'VMT_TURBO',
	'19': 'VMT_SUBWOOFER',
	'20': 'VMT_TYRE_SMOKE',
	'21': 'VMT_HYDRAULICS',
	'22': 'VMT_XENON_LIGHTS',
	'23': 'VMT_WHEELS',
	'24': 'VMT_WHEELS_REAR_OR_HYDRAULICS',
	'25': 'VMT_PLTHOLDER',
	'26': 'VMT_PLTVANITY',
	'27': 'VMT_INTERIOR1',
	'227': 'VMT_INTERIOR1',
	'28': 'VMT_INTERIOR2',
	'228': 'VMT_INTERIOR2',
	'29': 'VMT_INTERIOR3',
	'229': 'VMT_INTERIOR3',
	'30': 'VMT_INTERIOR4',
	'230': 'VMT_INTERIOR4',
	'31': 'VMT_INTERIOR5',
	'231': 'VMT_INTERIOR5',
	'32': 'VMT_SEATS',
	'232': 'VMT_SEATS',
	'33': 'VMT_STEERING',
	'233': 'VMT_STEERING',
	'34': 'VMT_KNOB',
	'234': 'VMT_KNOB',
	'35': 'VMT_PLAQUE',
	'235': 'VMT_PLAQUE',
	'36': 'VMT_ICE',
	'236': 'VMT_ICE',
	'37': 'VMT_TRUNK',
	'237': 'VMT_TRUNK',
	'38': 'VMT_HYDRO',
	'238': 'VMT_HYDRO',
	'39': 'VMT_ENGINEBAY1',
	'239': 'VMT_ENGINEBAY1',
	'40': 'VMT_ENGINEBAY2',
	'240': 'VMT_ENGINEBAY2',
	'41': 'VMT_ENGINEBAY3',
	'241': 'VMT_ENGINEBAY3',
	'42': 'VMT_CHASSIS2',
	'242': 'VMT_CHASSIS2',
	'43': 'VMT_CHASSIS3',
	'243': 'VMT_CHASSIS3',
	'44': 'VMT_CHASSIS4',
	'244': 'VMT_CHASSIS4',
	'45': 'VMT_CHASSIS5',
	'245': 'VMT_CHASSIS5',
	'46': 'VMT_DOOR_L',
	'246': 'VMT_DOOR_L',
	'47': 'VMT_DOOR_R',
	'247': 'VMT_DOOR_R',
	'48': 'VMT_LIVERY_MOD',
	'49': 'VMT_LIGHTBAR',
	'55': 'VMT_TONER',
	// Доп. функции
	'102': 'VMT_NUMBER_PLATE',
	'103': 'VMT_REDUCED_GRIP_TIRES',
	'104': 'VMT_DRIFT_REDUCTION_SUSPENSION',
	'105': 'VMT_GPS_TRACKER',
	'106': 'VMT_BIKE_BELT',
	'107': 'VMT_NEON',
	'110': 'VMT_MOD_FRONT_SUSPENSION',
	'115': 'VMT_MOD_REAR_SUSPENSION',
	'111': 'VMT_MOD_FRONT_WHEELS',
	'112': 'VMT_MOD_REAR_WHEELS',
	// Покраска
	'120': 'VMT_PAINT1', // Основной цвет
	'122': 'VMT_PAINT2', // Дополнительный цвет
	'128': 'VMT_PAINT3', // Перламутр
	'101': 'VMT_PAINT4', // Окраска диска
	'126': 'VMT_PAINT6', // Цвет интерьера
	'127': 'VMT_PAINT7', // Доп. цвет интерьера
	'100': 'VMT_PAINT_NEON', // Цвет неона
	'124': 'VMT_PAINT_TYRE_SMOKE', // Цвет дыма покрышек
	'125': 'VMT_PAINT_LIGHTBAR', // Цвет ксенона
};

export const garagesData = [
	{
		id: 1,
		locked: false,
		camera: { x: -1154.902, y: -2011.438, z: 13.18, heading: 95.49 },
		driveout: { x: -1150.379, y: -1995.845, z: 12.465, heading: 313.594 },
		drivein: { x: -1150.26, y: -1995.642, z: 12.466, heading: 136.859 },
		outside: { x: -1140.352, y: -1985.89, z: 12.45, heading: 314.406 },
		inside: { x: -1155.077, y: -2006.61, z: 12.465, heading: 162.58 },
	},
	{
		id: 2,
		locked: false,
		camera: { x: 737.09, y: -1085.721, z: 22.169, heading: 114.86 },
		driveout: { x: 725.46, y: -1088.822, z: 21.455, heading: 89.395 },
		drivein: { x: 726.157, y: -1088.768, z: 22.169, heading: 270.288 },
		outside: { x: 716.54, y: -1088.757, z: 21.651, heading: 89.248 },
		inside: { x: 733.69, y: -1088.74, z: 21.733, heading: 270.528 },
	},
	{
		id: 3,
		locked: false,
		camera: { x: -215.518, y: -1329.135, z: 30.89, heading: 329.092 },
		driveout: { x: -205.935, y: -1316.642, z: 30.176, heading: 356.495 },
		drivein: { x: -205.626, y: -1314.99, z: 30.247, heading: 179.395 },
		outside: { x: -205.594, y: -1304.085, z: 30.614, heading: 359.792 },
		inside: { x: -212.368, y: -1325.486, z: 30.176, heading: 141.107 },
	},
	{
		id: 4,
		locked: false,
		camera: { x: 1177.98, y: 2636.059, z: 37.754, heading: 37.082 },
		driveout: { x: 1175.003, y: 2642.175, z: 37.045, heading: 0.759 },
		drivein: { x: 1174.701, y: 2643.764, z: 37.048, heading: 178.119 },
		outside: { x: 1175.565, y: 2652.819, z: 37.941, heading: 351.579 },
		inside: { x: 1175.6075, y: 2640.9545, z: 37.045, heading: 181.19 },
	},
	{
		id: 5,
		locked: false,
		camera: { x: 105.825, y: 6627.562, z: 31.787, heading: 266.692 },
		driveout: { x: 112.326, y: 6625.148, z: 31.073, heading: 224.641 },
		drivein: { x: 112.738, y: 6624.644, z: 31.072, heading: 44.262 },
		outside: { x: 118.493, y: 6618.897, z: 31.13, heading: 224.701 },
		inside: { x: 110.925, y: 6625.1372, z: 31.072, heading: 45.504 },
	},
	{
		id: 6,
		locked: false,
		camera: { x: -330.945, y: -135.471, z: 39.01, heading: 102.213 },
		driveout: { x: -350.376, y: -136.76, z: 38.294, heading: 70.226 },
		drivein: { x: -350.655, y: -136.55, z: 38.295, heading: 249.532 },
		outside: { x: -362.7962, y: -132.4005, z: 38.25239, heading: 71.187133 },
		inside: { x: -337.3863, y: -136.9247, z: 38.5737, heading: 269.455 },
	},
	{
		id: 7,
		locked: false,
		camera: { x: 1201, y: -9112.92, z: 5.54, heading: -135.5 },
		driveout: { x: 1204.53, y: -3110.46, z: 5.542, heading: -9.567 },
		drivein: { x: 1204.53, y: -3110.46, z: 5.542, heading: -9.567 },
		outside: { x: 1204, y: -3103.9, z: 5.74, heading: -180 },
		inside: { x: 1204.4, y: -3117.9, z: 5.24, heading: 9 },
	},
];

export const defaultColor: {
	[modColor: number]: number;
} = {
	0: 1,
	1: 161,
	2: 162,
	3: 163,
	4: 164,
	5: 165,
	6: 166,
	7: 167,
	8: 168,
	9: 169,
	10: 170,
};

export const customTitles: {
	[title: string]: {
		title: string;
		svg: string;
	};
} = {
	VMT_CUSTOM_WING: {
		// Крыло
		title: 'VMT_CUSTOM_WING',
		svg: 'icons/tuningNew/mods/0.svg',
	},
	VMT_CUSTOM_WING_2: {
		// Доп. крыло
		title: 'VMT_CUSTOM_WING_2',
		svg: 'icons/tuningNew/mods/1.svg',
	},
	VMT_CUSTOM_WINGS: {
		// Крылья
		title: 'VMT_CUSTOM_WINGS',
		svg: 'icons/tuningNew/mods/0.svg',
	},
	VMT_CUSTOM_WINGS_2: {
		// Доп. крылья
		title: 'VMT_CUSTOM_WINGS_2',
		svg: 'icons/tuningNew/mods/1.svg',
	},
	VMT_CUSTOM_WING_FRONT: {
		// Передние крылья
		title: 'VMT_CUSTOM_WING_FRONT',
		svg: 'icons/tuningNew/mods/8.svg',
	},
	VMT_CUSTOM_WING_REAR: {
		// Задние крылья
		title: 'VMT_CUSTOM_WING_REAR',
		svg: 'icons/tuningNew/mods/9.svg',
	},
	VMT_CUSTOM_FEND: {
		// Фендеры
		title: 'VMT_CUSTOM_FEND',
		svg: 'icons/tuningNew/mods/44.svg',
	},
	VMT_CUSTOM_FEND_2: {
		// Доп. фендеры
		title: 'VMT_CUSTOM_FEND_2',
		svg: 'icons/tuningNew/mods/44.svg',
	},
	VMT_CUSTOM_CHS: {
		// Орнамент
		title: 'VMT_CUSTOM_CHS',
		svg: 'icons/tuningNew/mods/5.svg',
	},
	VMT_CUSTOM_CHS_2: {
		// Доп. орнамент
		title: 'VMT_CUSTOM_CHS_2',
		svg: 'icons/tuningNew/mods/1.svg',
	},
	VMT_CUSTOM_BADGE: {
		// Накладка
		title: 'VMT_CUSTOM_BADGE',
		svg: 'icons/tuningNew/mods/7.svg',
	},
	VMT_CUSTOM_BADGE_2: {
		// Доп. накладка
		title: 'VMT_CUSTOM_BADGE_2',
		svg: 'icons/tuningNew/mods/1.svg',
	},
	VMT_CUSTOM_FRAMES: {
		// Рамка
		title: 'VMT_CUSTOM_FRAMES',
		svg: 'icons/tuningNew/mods/42.svg',
	},
	VMT_CUSTOM_FRAMES_2: {
		// Доп. рамка
		title: 'VMT_CUSTOM_FRAMES_2',
		svg: 'icons/tuningNew/mods/1.svg',
	},
	VMT_CUSTOM_HOLDER: {
		// Держатель
		title: 'VMT_CUSTOM_HOLDER',
		svg: 'icons/tuningNew/mods/1.svg',
	},
	VMT_CUSTOM_HOLDER_2: {
		// Доп. держатель
		title: 'VMT_CUSTOM_HOLDER_2',
		svg: 'icons/tuningNew/mods/1.svg',
	},
	VMT_CUSTOM_ICON: {
		// Значок
		title: 'VMT_CUSTOM_ICON',
		svg: 'icons/tuningNew/mods/6.svg',
	},
	VMT_CUSTOM_ICON_2: {
		// Доп. значок
		title: 'VMT_CUSTOM_ICON_2',
		svg: 'icons/tuningNew/mods/6.svg',
	},
	VMT_CUSTOM_PART_INSIDE: {
		// Внутрняя часть
		title: 'VMT_CUSTOM_PART_INSIDE',
		svg: 'icons/tuningNew/mods/1.svg',
	},
	VMT_CUSTOM_PART_OUTSIDE: {
		// Внешняя часть
		title: 'VMT_CUSTOM_PART_OUTSIDE',
		svg: 'icons/tuningNew/mods/1.svg',
	},
	VMT_CUSTOM_SPLIT: {
		// Сплиттер
		title: 'VMT_CUSTOM_SPLIT',
		svg: 'icons/tuningNew/mods/1.svg',
	},
	VMT_CUSTOM_CONARDS: {
		// Кондарды
		title: 'VMT_CUSTOM_CONARDS',
		svg: 'icons/tuningNew/mods/1.svg',
	},
	VMT_CUSTOM_DIFF: {
		// Диффузор
		title: 'VMT_CUSTOM_DIFF',
		svg: 'icons/tuningNew/mods/2.svg',
	},
	VMT_CUSTOM_GRILL: {
		// Решетка
		title: 'VMT_CUSTOM_GRILL',
		svg: 'icons/tuningNew/mods/2.svg',
	},
	VMT_CUSTOM_SPOILER_ROOF: {
		// Верхний спойлер
		title: 'VMT_CUSTOM_SPOILER_ROOF',
		svg: 'icons/tuningNew/mods/0.svg',
	},
	VMT_CUSTOM_ROOF_RAILS: {
		// Рейлинги
		title: 'VMT_CUSTOM_ROOF_RAILS',
		svg: 'icons/tuningNew/mods/10.svg',
	},
	VMT_CUSTOM_ROOF_RAILS_SIDE: {
		// Боковые рейлинги
		title: 'VMT_CUSTOM_ROOF_RAILS_SIDE',
		svg: 'icons/tuningNew/mods/10.svg',
	},
	VMT_CUSTOM_ROOF_RAILS_FRONT: {
		// Передние рейлинги
		title: 'VMT_CUSTOM_ROOF_RAILS_FRONT',
		svg: 'icons/tuningNew/mods/10.svg',
	},
	VMT_CUSTOM_INTERIOR_FRONT: {
		// Передняя отделка
		title: 'VMT_CUSTOM_INTERIOR_FRONT',
		svg: 'icons/tuningNew/mods/27.svg',
	},
	VMT_CUSTOM_INTERIOR_REAR: {
		// Задняя отделка
		title: 'VMT_CUSTOM_INTERIOR_REAR',
		svg: 'icons/tuningNew/mods/27.svg',
	},
	VMT_CUSTOM_INTERIOR_ROOF: {
		// Потолок
		title: 'VMT_CUSTOM_INTERIOR_ROOF',
		svg: 'icons/tuningNew/mods/27.svg',
	},
	VMT_CUSTOM_INTERIOR_DASHBOARD: {
		// Приборная панель
		title: 'VMT_CUSTOM_INTERIOR_DASHBOARD',
		svg: 'icons/tuningNew/mods/27.svg',
	},
	VMT_CUSTOM_SEATS_FRONT: {
		// Передние сиденья
		title: 'VMT_CUSTOM_SEATS_FRONT',
		svg: 'icons/tuningNew/mods/32.svg',
	},
	VMT_CUSTOM_SEATS_REAR: {
		// Задние сиденья
		title: 'VMT_CUSTOM_SEATS_REAR',
		svg: 'icons/tuningNew/mods/32.svg',
	},
	VMT_CUSTOM_HEADLIGHT: {
		// Фары
		title: 'VMT_CUSTOM_HEADLIGHT',
		svg: 'icons/tuningNew/mods/42.svg',
	},
	VMT_CUSTOM_HEADLIGHT_FRAMES: {
		// Накладка на фары
		title: 'VMT_CUSTOM_HEADLIGHT_FRAMES',
		svg: 'icons/tuningNew/mods/42.svg',
	},
	VMT_CUSTOM_TAILLIGHT: {
		// Фонари
		title: 'VMT_CUSTOM_TAILLIGHT',
		svg: 'icons/tuningNew/mods/43.svg',
	},
	VMT_CUSTOM_TURNSIGNALS: {
		// Поворотники
		title: 'VMT_CUSTOM_TURNSIGNALS',
		svg: 'icons/tuningNew/mods/44.svg',
	},
	VMT_CUSTOM_AIRDUCT: {
		// Воздуховод
		title: 'VMT_CUSTOM_AIRDUCT',
		svg: 'icons/tuningNew/mods/4.svg',
	},
	VMT_CUSTOM_ANTENNA: {
		// Антенна
		title: 'VMT_CUSTOM_ANTENNA',
		svg: 'icons/tuningNew/mods/44.svg',
	},
	VMT_CUSTOM_ANTENNA_2: {
		// Доп. антенна
		title: 'VMT_CUSTOM_ANTENNA_2',
		svg: 'icons/tuningNew/mods/42.svg',
	},
	VMT_CUSTOM_TANK_CUP: {
		// Крышка бака
		title: 'VMT_CUSTOM_TANK_CUP',
		svg: 'icons/tuningNew/mods/45.svg',
	},
	VMT_CUSTOM_DOOR_FRAMES: {
		// Оконные рамы
		title: 'VMT_CUSTOM_DOOR_FRAMES',
		svg: 'icons/tuningNew/mods/46.svg',
	},
	VMT_CUSTOM_DOOR_HANDLES: {
		// Дверные ручки
		title: 'VMT_CUSTOM_DOOR_HANDLES',
		svg: 'icons/tuningNew/mods/46.svg',
	},
	VMT_CUSTOM_DOOR_FRAMES_HANDLES: {
		// Дверные ручки и рама
		title: 'VMT_CUSTOM_DOOR_FRAMES_HANDLES',
		svg: 'icons/tuningNew/mods/46.svg',
	},
	VMT_CUSTOM_DOOR_FRONT: {
		// Передние двери
		title: 'VMT_CUSTOM_DOOR_FRONT',
		svg: 'icons/tuningNew/mods/46.svg',
	},
	VMT_CUSTOM_DOOR_REAR: {
		// Задние двери
		title: 'VMT_CUSTOM_DOOR_REAR',
		svg: 'icons/tuningNew/mods/47.svg',
	},
	VMT_CUSTOM_DOOR_MIRRORS: {
		// Зеркала
		title: 'VMT_CUSTOM_DOOR_MIRRORS',
		svg: 'icons/tuningNew/mods/47.svg',
	},
};

export const genDetails: {
	[key: number]: {
		modTitle: string;
		title: string;
		svg: string;
	};
} = {
	1001: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_HW24_1',
		svg: 'icons/tuningNew/genDetails/1001.svg',
	},
	1002: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_HW24_2',
		svg: 'icons/tuningNew/genDetails/1002.svg',
	},
	1003: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_HW24_3',
		svg: 'icons/tuningNew/genDetails/1003.svg',
	},
	1004: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_HW24_4',
		svg: 'icons/tuningNew/genDetails/1004.svg',
	},
	1005: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_HW24_5',
		svg: 'icons/tuningNew/genDetails/1005.svg',
	},
	1006: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_HW24_6',
		svg: 'icons/tuningNew/genDetails/1006.svg',
	},
	1007: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_1',
		svg: 'icons/tuningNew/genDetails/1007.svg',
	},
	1008: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_2',
		svg: 'icons/tuningNew/genDetails/1008.svg',
	},
	1009: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_3',
		svg: 'icons/tuningNew/genDetails/1009.svg',
	},
	1010: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_4',
		svg: 'icons/tuningNew/genDetails/1010.svg',
	},
	1011: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_5',
		svg: 'icons/tuningNew/genDetails/1011.svg',
	},
	1012: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_6',
		svg: 'icons/tuningNew/genDetails/1012.svg',
	},
	1013: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_7',
		svg: 'icons/tuningNew/genDetails/1013.svg',
	},
	1014: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_8',
		svg: 'icons/tuningNew/genDetails/1014.svg',
	},
	1015: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_9',
		svg: 'icons/tuningNew/genDetails/1015.svg',
	},
	1016: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_10',
		svg: 'icons/tuningNew/genDetails/1016.svg',
	},
	1017: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_11',
		svg: 'icons/tuningNew/genDetails/1017.svg',
	},
	1018: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_12',
		svg: 'icons/tuningNew/genDetails/1018.svg',
	},
	1019: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_12',
		svg: 'icons/tuningNew/genDetails/1018.svg',
	},
	1020: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_12',
		svg: 'icons/tuningNew/genDetails/1018.svg',
	},
	1021: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_12',
		svg: 'icons/tuningNew/genDetails/1018.svg',
	},
	1022: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_12',
		svg: 'icons/tuningNew/genDetails/1018.svg',
	},
	1023: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_12',
		svg: 'icons/tuningNew/genDetails/1018.svg',
	},
	1024: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_12',
		svg: 'icons/tuningNew/genDetails/1018.svg',
	},
	1025: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_12',
		svg: 'icons/tuningNew/genDetails/1018.svg',
	},
	1026: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_12',
		svg: 'icons/tuningNew/genDetails/1018.svg',
	},
	1027: {
		modTitle: 'VMT_LIVERY_MOD',
		title: 'GEN_DETAIL_LIVERY_BP_12',
		svg: 'icons/tuningNew/genDetails/1018.svg',
	},
	5001: {
		modTitle: 'VMT_PLTHOLDER',
		title: 'GEN_DETAIL_PLTHOLDER_BP6_1',
		svg: 'icons/tuningNew/genDetails/5001.svg',
	},
	5002: {
		modTitle: 'VMT_PLTHOLDER',
		title: 'GEN_DETAIL_PLTHOLDER_BP6_2',
		svg: 'icons/tuningNew/genDetails/5002.svg',
	},
	5003: {
		modTitle: 'VMT_PLTHOLDER',
		title: 'GEN_DETAIL_PLTHOLDER_BP6_3',
		svg: 'icons/tuningNew/genDetails/5003.svg',
	},
	5004: {
		modTitle: 'VMT_PLTHOLDER',
		title: 'GEN_DETAIL_PLTHOLDER_BP6_4',
		svg: 'icons/tuningNew/genDetails/5004.svg',
	},
	5005: {
		modTitle: 'VMT_PLTHOLDER',
		title: 'GEN_DETAIL_PLTHOLDER_BP6_5',
		svg: 'icons/tuningNew/genDetails/5004.svg',
	},
	5006: {
		modTitle: 'VMT_PLTHOLDER',
		title: 'GEN_DETAIL_PLTHOLDER_CS25_6',
		svg: 'icons/tuningNew/genDetails/5004.svg',
	},
	5007: {
		modTitle: 'VMT_PLTHOLDER',
		title: 'GEN_DETAIL_PLTHOLDER_CS25_7',
		svg: 'icons/tuningNew/genDetails/5004.svg',
	},
};
