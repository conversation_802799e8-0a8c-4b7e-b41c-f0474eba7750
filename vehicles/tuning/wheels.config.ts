import { vehicleMods } from './tuning.config';

export const getWheelsSetting = (
	wheelsSetting?: {
		suspensionFront?: number;
		suspensionRear?: number;
		widthFront?: number;
		widthRear?: number;
		camberFront?: number;
		camberRear?: number;
	},
	currentWheelsSettings?: {
		suspensionFront: number;
		suspensionRear: number;
		widthFront: number;
		widthRear: number;
		camberFront: number;
		camberRear: number;
	},
) => {
	const resultData = !currentWheelsSettings
		? {
				suspensionFront: 0,
				suspensionRear: 0,
				widthFront: 0,
				widthRear: 0,
				camberFront: 0,
				camberRear: 0,
			}
		: {
				...currentWheelsSettings,
			};

	if (wheelsSetting) {
		if (wheelsSetting.suspensionFront) {
			resultData.suspensionFront += wheelsSetting.suspensionFront;
		}

		if (wheelsSetting.suspensionRear) {
			resultData.suspensionRear += wheelsSetting.suspensionRear;
		}

		if (wheelsSetting.widthFront) {
			resultData.widthFront += wheelsSetting.widthFront;
		}

		if (wheelsSetting.widthRear) {
			resultData.widthRear += wheelsSetting.widthRear;
		}

		if (wheelsSetting.camberFront) {
			resultData.camberFront += wheelsSetting.camberFront;
		}

		if (wheelsSetting.camberRear) {
			resultData.camberRear += wheelsSetting.camberRear;
		}
	}

	return resultData;
};

const getWheelSettingValue = (_value: number, defaultValue: number, addValue: number) => {
	let returnValue = _value;
	if (!returnValue) {
		returnValue = defaultValue;
	}

	returnValue += addValue;
	// if (returnValue > 0) {
	// 	returnValue += addValue;
	// } else {
	// 	returnValue -= addValue;
	// }

	return returnValue;
};

export const getWheelSettingToTuningData = (
	tuningData: {
		[modIndex: number]: string | number;
	},
	currentWheelsSettings: {
		suspensionFront?: number;
		suspensionRear?: number;
		widthFront?: number;
		widthRear?: number;
		camberFront?: number;
		camberRear?: number;
	},
	wheelConfig: {
		height: number;
		camber: number;
		trackWidth: number;
		trackWidthRear: number;
	},
) => {
	const resultData = {
		...tuningData,
	};

	resultData[vehicleMods['suspensionFront']] = getWheelSettingValue(
		(resultData[vehicleMods['suspensionFront']] as number) ?? 0,
		wheelConfig.height,
		currentWheelsSettings.suspensionFront ?? 0,
	);

	resultData[vehicleMods['suspensionRear']] = getWheelSettingValue(
		(resultData[vehicleMods['suspensionRear']] as number) ?? 0,
		wheelConfig.height,
		currentWheelsSettings.suspensionRear ?? 0,
	);

	resultData[vehicleMods['widthFront']] = getWheelSettingValue(
		(resultData[vehicleMods['widthFront']] as number) ?? 0,
		wheelConfig.trackWidth,
		currentWheelsSettings.widthFront ?? 0,
	);

	resultData[vehicleMods['camberFront']] = getWheelSettingValue(
		(resultData[vehicleMods['camberFront']] as number) ?? 0,
		wheelConfig.camber,
		currentWheelsSettings.camberFront ?? 0,
	);

	resultData[vehicleMods['widthRear']] = getWheelSettingValue(
		(resultData[vehicleMods['widthRear']] as number) ?? 0,
		wheelConfig.trackWidthRear,
		currentWheelsSettings.widthRear ?? 0,
	);

	resultData[vehicleMods['camberRear']] = getWheelSettingValue(
		(resultData[vehicleMods['camberRear']] as number) ?? 0,
		wheelConfig.camber,
		currentWheelsSettings.camberRear ?? 0,
	);

	return resultData;
};

export const vehicleWheelsConfig: {
	[key: string]: {
		height: number;
		heightRear: number;
		camber: number;
		trackWidth: number;
		trackWidthRear: number;
	};
} = {
	adder: {
		height: -0.691,
		heightRear: -0.696,
		camber: 0,
		trackWidth: 0.801,
		trackWidthRear: 0.753,
	},
	airbus: {
		height: -1.196,
		heightRear: -1.2,
		camber: 0,
		trackWidth: 1.168,
		trackWidthRear: 1.168,
	},
	airtug: {
		height: -0.545,
		heightRear: -0.545,
		camber: 0,
		trackWidth: 0.526,
		trackWidthRear: 0.477,
	},
	aleutian: {
		height: -0.783,
		heightRear: -0.783,
		camber: 0,
		trackWidth: 0.878,
		trackWidthRear: 0.878,
	},
	alpha: {
		height: -0.785,
		heightRear: -0.785,
		camber: 0,
		trackWidth: 0.77,
		trackWidthRear: 0.77,
	},
	ambulance: {
		height: -1.034,
		heightRear: -1.032,
		camber: 0,
		trackWidth: 0.949,
		trackWidthRear: 1.039,
	},
	apc: {
		height: -0.889,
		heightRear: -0.889,
		camber: 0,
		trackWidth: 1.104,
		trackWidthRear: 1.104,
	},
	ardent: {
		height: -0.452,
		heightRear: -0.43,
		camber: 0,
		trackWidth: 0.724,
		trackWidthRear: 0.72,
	},
	asbo: {
		height: -0.517,
		heightRear: -0.517,
		camber: 0,
		trackWidth: 0.714,
		trackWidthRear: 0.714,
	},
	asea: {
		height: -0.714,
		heightRear: -0.714,
		camber: 0,
		trackWidth: 0.693,
		trackWidthRear: 0.703,
	},
	asea2: {
		height: -0.714,
		heightRear: -0.714,
		camber: 0,
		trackWidth: 0.693,
		trackWidthRear: 0.703,
	},
	asterope: {
		height: -0.659,
		heightRear: -0.659,
		camber: 0,
		trackWidth: 0.772,
		trackWidthRear: 0.772,
	},
	asterope2: {
		height: -0.627,
		heightRear: -0.637,
		camber: 0,
		trackWidth: 0.774,
		trackWidthRear: 0.777,
	},
	autarch: {
		height: -0.461,
		heightRear: -0.461,
		camber: 0,
		trackWidth: 0.852,
		trackWidthRear: 0.833,
	},
	baller: {
		height: -1.167,
		heightRear: -1.167,
		camber: 0,
		trackWidth: 0.847,
		trackWidthRear: 0.848,
	},
	baller2: {
		height: -1.089,
		heightRear: -1.099,
		camber: 0,
		trackWidth: 0.869,
		trackWidthRear: 0.869,
	},
	baller3: {
		height: -1.132,
		heightRear: -1.132,
		camber: 0,
		trackWidth: 0.869,
		trackWidthRear: 0.869,
	},
	baller4: {
		height: -1.137,
		heightRear: -1.133,
		camber: 0,
		trackWidth: 0.869,
		trackWidthRear: 0.869,
	},
	baller5: {
		height: -1.132,
		heightRear: -1.132,
		camber: 0,
		trackWidth: 0.869,
		trackWidthRear: 0.869,
	},
	baller6: {
		height: -1.137,
		heightRear: -1.133,
		camber: 0,
		trackWidth: 0.869,
		trackWidthRear: 0.869,
	},
	baller8: {
		height: -0.756,
		heightRear: -0.756,
		camber: 0,
		trackWidth: 0.858,
		trackWidthRear: 0.859,
	},
	banshee: {
		height: -0.687,
		heightRear: -0.687,
		camber: 0,
		trackWidth: 0.816,
		trackWidthRear: 0.879,
	},
	banshee2: {
		height: -0.643,
		heightRear: -0.643,
		camber: 0,
		trackWidth: 0.884,
		trackWidthRear: 0.921,
	},
	barracks: {
		height: -0.909,
		heightRear: -0.909,
		camber: 0,
		trackWidth: 1.097,
		trackWidthRear: 1.097,
	},
	barracks2: {
		height: -0.958,
		heightRear: -0.946,
		camber: 0,
		trackWidth: 1.097,
		trackWidthRear: 1.119,
	},
	barracks3: {
		height: -0.909,
		heightRear: -0.909,
		camber: 0,
		trackWidth: 1.097,
		trackWidthRear: 1.097,
	},
	barrage: {
		height: -0.944,
		heightRear: -0.944,
		camber: 0,
		trackWidth: 1.045,
		trackWidthRear: 1.045,
	},
	benson: {
		height: -1.18,
		heightRear: -1.178,
		camber: 0,
		trackWidth: 1.078,
		trackWidthRear: 1.289,
	},
	bestiagts: {
		height: -0.354,
		heightRear: -0.349,
		camber: 0,
		trackWidth: 0.831,
		trackWidthRear: 0.802,
	},
	bfinjection: {
		height: -0.807,
		heightRear: -0.793,
		camber: 0,
		trackWidth: 0.621,
		trackWidthRear: 0.79,
	},
	biff: {
		height: -1.326,
		heightRear: -1.326,
		camber: 0,
		trackWidth: 1.053,
		trackWidthRear: 1.053,
	},
	bifta: {
		height: -0.639,
		heightRear: -0.644,
		camber: 0,
		trackWidth: 0.704,
		trackWidthRear: 0.735,
	},
	bison: {
		height: -0.705,
		heightRear: -0.705,
		camber: 0,
		trackWidth: 0.872,
		trackWidthRear: 0.872,
	},
	bison2: {
		height: -0.705,
		heightRear: -0.705,
		camber: 0,
		trackWidth: 0.872,
		trackWidthRear: 0.872,
	},
	bison3: {
		height: -0.705,
		heightRear: -0.705,
		camber: 0,
		trackWidth: 0.872,
		trackWidthRear: 0.872,
	},
	bjxl: {
		height: -1.072,
		heightRear: -1.072,
		camber: 0,
		trackWidth: 0.849,
		trackWidthRear: 0.849,
	},
	blade: {
		height: -0.544,
		heightRear: -0.544,
		camber: 0,
		trackWidth: 0.733,
		trackWidthRear: 0.68,
	},
	blazer: {
		height: -0.592,
		heightRear: -0.592,
		camber: 0,
		trackWidth: 0.431,
		trackWidthRear: 0.431,
	},
	blazer2: {
		height: -0.562,
		heightRear: -0.562,
		camber: 0,
		trackWidth: 0.431,
		trackWidthRear: 0.431,
	},
	blazer3: {
		height: -0.592,
		heightRear: -0.592,
		camber: 0,
		trackWidth: 0.431,
		trackWidthRear: 0.431,
	},
	blazer4: {
		height: -0.406,
		heightRear: -0.422,
		camber: 0,
		trackWidth: 0.435,
		trackWidthRear: 0.415,
	},
	blazer5: {
		height: -0.389,
		heightRear: -0.389,
		camber: 0,
		trackWidth: 0.532,
		trackWidthRear: 0.532,
	},
	blista: {
		height: -0.924,
		heightRear: -0.924,
		camber: 0,
		trackWidth: 0.718,
		trackWidthRear: 0.718,
	},
	blista2: {
		height: -0.61,
		heightRear: -0.61,
		camber: 0,
		trackWidth: 0.707,
		trackWidthRear: 0.707,
	},
	blista3: {
		height: -0.61,
		heightRear: -0.61,
		camber: 0,
		trackWidth: 0.707,
		trackWidthRear: 0.707,
	},
	bobcatxl: {
		height: -0.89,
		heightRear: -0.888,
		camber: 0,
		trackWidth: 0.915,
		trackWidthRear: 0.976,
	},
	bodhi2: {
		height: -1.119,
		heightRear: -1.119,
		camber: 0,
		trackWidth: 1.043,
		trackWidthRear: 1.048,
	},
	boor: {
		height: -0.413,
		heightRear: -0.413,
		camber: 0,
		trackWidth: 0.683,
		trackWidthRear: 0.683,
	},
	boxville: {
		height: -1.115,
		heightRear: -1.115,
		camber: 0,
		trackWidth: 0.881,
		trackWidthRear: 0.881,
	},
	boxville2: {
		height: -1.115,
		heightRear: -1.115,
		camber: 0,
		trackWidth: 0.881,
		trackWidthRear: 0.881,
	},
	boxville3: {
		height: -1.115,
		heightRear: -1.115,
		camber: 0,
		trackWidth: 0.881,
		trackWidthRear: 0.881,
	},
	boxville4: {
		height: -1.115,
		heightRear: -1.115,
		camber: 0,
		trackWidth: 0.881,
		trackWidthRear: 0.881,
	},
	boxville5: {
		height: -1.121,
		heightRear: -1.121,
		camber: 0,
		trackWidth: 0.881,
		trackWidthRear: 0.881,
	},
	brawler: {
		height: -1.037,
		heightRear: -1.037,
		camber: 0,
		trackWidth: 0.869,
		trackWidthRear: 0.869,
	},
	brickade: {
		height: -1.624,
		heightRear: -1.624,
		camber: 0,
		trackWidth: 1.206,
		trackWidthRear: 1.206,
	},
	brigham: {
		height: -0.696,
		heightRear: -0.696,
		camber: 0,
		trackWidth: 0.777,
		trackWidthRear: 0.865,
	},
	brioso: {
		height: -0.312,
		heightRear: -0.312,
		camber: 0,
		trackWidth: 0.668,
		trackWidthRear: 0.668,
	},
	brioso2: {
		height: -0.483,
		heightRear: -0.483,
		camber: 0,
		trackWidth: 0.643,
		trackWidthRear: 0.648,
	},
	brioso3: {
		height: -0.426,
		heightRear: -0.426,
		camber: 0,
		trackWidth: 0.703,
		trackWidthRear: 0.703,
	},
	broadway: {
		height: -0.444,
		heightRear: -0.444,
		camber: 0,
		trackWidth: 0.703,
		trackWidthRear: 0.703,
	},
	bruiser: {
		height: -1.39,
		heightRear: -1.39,
		camber: 0,
		trackWidth: 1.06,
		trackWidthRear: 1.06,
	},
	bruiser2: {
		height: -1.389,
		heightRear: -1.389,
		camber: 0,
		trackWidth: 1.06,
		trackWidthRear: 1.06,
	},
	bruiser3: {
		height: -1.39,
		heightRear: -1.39,
		camber: 0,
		trackWidth: 1.06,
		trackWidthRear: 1.06,
	},
	brutus: {
		height: -1.151,
		heightRear: -1.161,
		camber: 0,
		trackWidth: 0.998,
		trackWidthRear: 0.998,
	},
	brutus2: {
		height: -1.151,
		heightRear: -1.161,
		camber: 0,
		trackWidth: 0.998,
		trackWidthRear: 0.998,
	},
	brutus3: {
		height: -1.151,
		heightRear: -1.161,
		camber: 0,
		trackWidth: 0.998,
		trackWidthRear: 0.998,
	},
	btype: {
		height: -0.56,
		heightRear: -0.56,
		camber: 0,
		trackWidth: 0.726,
		trackWidthRear: 0.726,
	},
	btype2: {
		height: -0.428,
		heightRear: -0.426,
		camber: 0,
		trackWidth: 0.726,
		trackWidthRear: 0.866,
	},
	btype3: {
		height: -0.56,
		heightRear: -0.56,
		camber: 0,
		trackWidth: 0.726,
		trackWidthRear: 0.726,
	},
	buccaneer: {
		height: -0.462,
		heightRear: -0.474,
		camber: 0,
		trackWidth: 0.79,
		trackWidthRear: 0.755,
	},
	buccaneer2: {
		height: -0.349,
		heightRear: -0.349,
		camber: 0,
		trackWidth: 0.82,
		trackWidthRear: 0.824,
	},
	buffalo: {
		height: -0.746,
		heightRear: -0.746,
		camber: 0,
		trackWidth: 0.785,
		trackWidthRear: 0.785,
	},
	buffalo2: {
		height: -0.746,
		heightRear: -0.746,
		camber: 0,
		trackWidth: 0.785,
		trackWidthRear: 0.785,
	},
	buffalo3: {
		height: -0.746,
		heightRear: -0.746,
		camber: 0,
		trackWidth: 0.785,
		trackWidthRear: 0.785,
	},
	buffalo5: {
		height: -0.598,
		heightRear: -0.608,
		camber: 0,
		trackWidth: 0.844,
		trackWidthRear: 0.86,
	},
	bulldozer: {
		height: -0.592,
		heightRear: -0.615,
		camber: 0,
		trackWidth: 0.992,
		trackWidthRear: 0.992,
	},
	bullet: {
		height: -0.557,
		heightRear: -0.548,
		camber: 0,
		trackWidth: 0.838,
		trackWidthRear: 0.85,
	},
	burrito: {
		height: -0.969,
		heightRear: -0.969,
		camber: 0,
		trackWidth: 0.98,
		trackWidthRear: 0.98,
	},
	burrito2: {
		height: -0.969,
		heightRear: -0.969,
		camber: 0,
		trackWidth: 0.98,
		trackWidthRear: 0.98,
	},
	burrito3: {
		height: -0.969,
		heightRear: -0.969,
		camber: 0,
		trackWidth: 0.98,
		trackWidthRear: 0.98,
	},
	burrito4: {
		height: -0.969,
		heightRear: -0.969,
		camber: 0,
		trackWidth: 0.98,
		trackWidthRear: 0.98,
	},
	burrito5: {
		height: -0.969,
		heightRear: -0.969,
		camber: 0,
		trackWidth: 0.98,
		trackWidthRear: 0.98,
	},
	bus: {
		height: -1.196,
		heightRear: -1.2,
		camber: 0,
		trackWidth: 1.168,
		trackWidthRear: 1.168,
	},
	caddy: {
		height: -0.506,
		heightRear: -0.506,
		camber: 0,
		trackWidth: 0.552,
		trackWidthRear: 0.552,
	},
	caddy2: {
		height: -0.502,
		heightRear: -0.502,
		camber: 0,
		trackWidth: 0.478,
		trackWidthRear: 0.495,
	},
	caddy3: {
		height: -0.472,
		heightRear: -0.46,
		camber: 0,
		trackWidth: 0.535,
		trackWidthRear: 0.54,
	},
	calico: {
		height: -0.51,
		heightRear: -0.51,
		camber: 0,
		trackWidth: 0.733,
		trackWidthRear: 0.733,
	},
	camper: {
		height: -0.98,
		heightRear: -0.98,
		camber: 0,
		trackWidth: 0.949,
		trackWidthRear: 1.239,
	},
	caracara: {
		height: -1.036,
		heightRear: -1.037,
		camber: 0,
		trackWidth: 0.901,
		trackWidthRear: 0.901,
	},
	caracara2: {
		height: -0.996,
		heightRear: -0.997,
		camber: 0,
		trackWidth: 0.901,
		trackWidthRear: 0.901,
	},
	carbonizzare: {
		height: -0.63,
		heightRear: -0.627,
		camber: 0,
		trackWidth: 0.793,
		trackWidthRear: 0.818,
	},
	casco: {
		height: -0.297,
		heightRear: -0.297,
		camber: 0,
		trackWidth: 0.752,
		trackWidthRear: 0.72,
	},
	castigator: {
		height: -0.672,
		heightRear: -0.672,
		camber: 0,
		trackWidth: 0.763,
		trackWidthRear: 0.763,
	},
	cavalcade: {
		height: -0.998,
		heightRear: -0.998,
		camber: 0,
		trackWidth: 0.811,
		trackWidthRear: 0.819,
	},
	cavalcade2: {
		height: -0.998,
		heightRear: -0.998,
		camber: 0,
		trackWidth: 0.811,
		trackWidthRear: 0.819,
	},
	cavalcade3: {
		height: -0.737,
		heightRear: -0.737,
		camber: 0,
		trackWidth: 0.877,
		trackWidthRear: 0.877,
	},
	cerberus: {
		height: -1.076,
		heightRear: -1.086,
		camber: 0,
		trackWidth: 1.3,
		trackWidthRear: 1.472,
	},
	cerberus2: {
		height: -1.076,
		heightRear: -1.086,
		camber: 0,
		trackWidth: 1.3,
		trackWidthRear: 1.472,
	},
	cerberus3: {
		height: -1.076,
		heightRear: -1.086,
		camber: 0,
		trackWidth: 1.3,
		trackWidthRear: 1.472,
	},
	champion: {
		height: -0.455,
		heightRear: -0.455,
		camber: 0,
		trackWidth: 0.812,
		trackWidthRear: 0.805,
	},
	cheburek: {
		height: -0.513,
		heightRear: -0.513,
		camber: -0.008,
		trackWidth: 0.677,
		trackWidthRear: 0.676,
	},
	cheetah: {
		height: -0.404,
		heightRear: -0.421,
		camber: 0,
		trackWidth: 0.857,
		trackWidthRear: 0.806,
	},
	cheetah2: {
		height: -0.672,
		heightRear: -0.672,
		camber: 0,
		trackWidth: 0.79,
		trackWidthRear: 0.861,
	},
	chernobog: {
		height: -1.071,
		heightRear: -1.071,
		camber: 0,
		trackWidth: 1.3,
		trackWidthRear: 1.3,
	},
	chino: {
		height: -0.711,
		heightRear: -0.711,
		camber: 0,
		trackWidth: 0.873,
		trackWidthRear: 0.804,
	},
	chino2: {
		height: -0.573,
		heightRear: -0.573,
		camber: 0,
		trackWidth: 0.808,
		trackWidthRear: 0.836,
	},
	clique: {
		height: -0.453,
		heightRear: -0.453,
		camber: 0,
		trackWidth: 0.717,
		trackWidthRear: 0.717,
	},
	clique2: {
		height: -0.589,
		heightRear: -0.589,
		camber: 0,
		trackWidth: 0.721,
		trackWidthRear: 0.721,
	},
	club: {
		height: -0.419,
		heightRear: -0.419,
		camber: 0,
		trackWidth: 0.685,
		trackWidthRear: 0.681,
	},
	coach: {
		height: -2.094,
		heightRear: -2.099,
		camber: 0,
		trackWidth: 1.168,
		trackWidthRear: 1.168,
	},
	cog55: {
		height: -0.631,
		heightRear: -0.631,
		camber: 0,
		trackWidth: 0.796,
		trackWidthRear: 0.773,
	},
	cog552: {
		height: -0.631,
		heightRear: -0.631,
		camber: 0,
		trackWidth: 0.796,
		trackWidthRear: 0.773,
	},
	cogcabrio: {
		height: -0.603,
		heightRear: -0.603,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.783,
	},
	cognoscenti: {
		height: -0.745,
		heightRear: -0.745,
		camber: 0,
		trackWidth: 0.82,
		trackWidthRear: 0.82,
	},
	cognoscenti2: {
		height: -0.745,
		heightRear: -0.745,
		camber: 0,
		trackWidth: 0.82,
		trackWidthRear: 0.82,
	},
	comet2: {
		height: -0.71,
		heightRear: -0.71,
		camber: 0,
		trackWidth: 0.72,
		trackWidthRear: 0.788,
	},
	comet3: {
		height: -0.389,
		heightRear: -0.389,
		camber: 0,
		trackWidth: 0.752,
		trackWidthRear: 0.805,
	},
	comet4: {
		height: -0.625,
		heightRear: -0.625,
		camber: 0,
		trackWidth: 0.669,
		trackWidthRear: 0.672,
	},
	comet5: {
		height: -0.67,
		heightRear: -0.67,
		camber: 0,
		trackWidth: 0.71,
		trackWidthRear: 0.788,
	},
	comet6: {
		height: -0.541,
		heightRear: -0.539,
		camber: 0,
		trackWidth: 0.801,
		trackWidthRear: 0.795,
	},
	comet7: {
		height: -0.541,
		heightRear: -0.539,
		camber: 0,
		trackWidth: 0.801,
		trackWidthRear: 0.795,
	},
	contender: {
		height: -1.517,
		heightRear: -1.517,
		camber: 0,
		trackWidth: 0.887,
		trackWidthRear: 0.887,
	},
	coquette: {
		height: -0.402,
		heightRear: -0.4,
		camber: 0,
		trackWidth: 0.761,
		trackWidthRear: 0.73,
	},
	coquette2: {
		height: -0.356,
		heightRear: -0.356,
		camber: 0,
		trackWidth: 0.805,
		trackWidthRear: 0.771,
	},
	coquette3: {
		height: -0.527,
		heightRear: -0.52,
		camber: 0,
		trackWidth: 0.718,
		trackWidthRear: 0.69,
	},
	coquette4: {
		height: -0.354,
		heightRear: -0.356,
		camber: 0,
		trackWidth: 0.782,
		trackWidthRear: 0.784,
	},
	coquette5: {
		height: -0.598,
		heightRear: -0.598,
		camber: 0,
		trackWidth: 0.775,
		trackWidthRear: 0.774,
	},
	corsita: {
		height: -0.461,
		heightRear: -0.461,
		camber: 0,
		trackWidth: 0.828,
		trackWidthRear: 0.828,
	},
	coureur: {
		height: -0.422,
		heightRear: -0.42,
		camber: -0.007,
		trackWidth: 0.841,
		trackWidthRear: 0.89,
	},
	crusader: {
		height: -0.6,
		heightRear: -0.6,
		camber: 0,
		trackWidth: 0.775,
		trackWidthRear: 0.795,
	},
	cutter: {
		height: -0.681,
		heightRear: -0.681,
		camber: 0,
		trackWidth: 1.55,
		trackWidthRear: 1.412,
	},
	cyclone: {
		height: -0.41,
		heightRear: -0.41,
		camber: -0.001,
		trackWidth: 0.769,
		trackWidthRear: 0.765,
	},
	cypher: {
		height: -0.53,
		heightRear: -0.53,
		camber: 0,
		trackWidth: 0.771,
		trackWidthRear: 0.791,
	},
	deluxo: {
		height: -0.598,
		heightRear: -0.6,
		camber: 0,
		trackWidth: 0.788,
		trackWidthRear: 0.778,
	},
	deveste: {
		height: -0.392,
		heightRear: -0.392,
		camber: 0,
		trackWidth: 0.879,
		trackWidthRear: 0.904,
	},
	deviant: {
		height: -0.566,
		heightRear: -0.566,
		camber: -0.006,
		trackWidth: 0.751,
		trackWidthRear: 0.785,
	},
	dilettante: {
		height: -0.61,
		heightRear: -0.61,
		camber: 0,
		trackWidth: 0.712,
		trackWidthRear: 0.687,
	},
	dilettante2: {
		height: -0.61,
		heightRear: -0.61,
		camber: 0,
		trackWidth: 0.712,
		trackWidthRear: 0.687,
	},
	dloader: {
		height: -0.831,
		heightRear: -0.831,
		camber: 0,
		trackWidth: 0.913,
		trackWidthRear: 0.913,
	},
	docktug: {
		height: -1.386,
		heightRear: -1.386,
		camber: 0,
		trackWidth: 1.039,
		trackWidthRear: 1.115,
	},
	dominator: {
		height: -0.436,
		heightRear: -0.436,
		camber: 0,
		trackWidth: 0.765,
		trackWidthRear: 0.765,
	},
	dominator2: {
		height: -0.436,
		heightRear: -0.436,
		camber: 0,
		trackWidth: 0.765,
		trackWidthRear: 0.765,
	},
	dominator3: {
		height: -0.533,
		heightRear: -0.533,
		camber: -0.008,
		trackWidth: 0.769,
		trackWidthRear: 0.767,
	},
	dominator4: {
		height: -0.546,
		heightRear: -0.546,
		camber: 0,
		trackWidth: 0.716,
		trackWidthRear: 0.765,
	},
	dominator5: {
		height: -0.546,
		heightRear: -0.546,
		camber: 0,
		trackWidth: 0.716,
		trackWidthRear: 0.765,
	},
	dominator6: {
		height: -0.546,
		heightRear: -0.546,
		camber: 0,
		trackWidth: 0.716,
		trackWidthRear: 0.765,
	},
	dominator7: {
		height: -0.517,
		heightRear: -0.517,
		camber: 0,
		trackWidth: 0.724,
		trackWidthRear: 0.764,
	},
	dominator8: {
		height: -0.653,
		heightRear: -0.653,
		camber: 0,
		trackWidth: 0.762,
		trackWidthRear: 0.727,
	},
	dominator9: {
		height: -0.436,
		heightRear: -0.436,
		camber: 0,
		trackWidth: 0.826,
		trackWidthRear: 0.826,
	},
	dominator10: {
		height: -0.537,
		heightRear: -0.537,
		camber: 0,
		trackWidth: 0.752,
		trackWidthRear: 0.735,
	},
	dorado: {
		height: -0.738,
		heightRear: -0.738,
		camber: 0,
		trackWidth: 0.733,
		trackWidthRear: 0.733,
	},
	drafter: {
		height: -0.61,
		heightRear: -0.61,
		camber: 0,
		trackWidth: 0.81,
		trackWidthRear: 0.81,
	},
	draugur: {
		height: -1.042,
		heightRear: -1.042,
		camber: 0,
		trackWidth: 0.965,
		trackWidthRear: 0.965,
	},
	dubsta: {
		height: -0.808,
		heightRear: -0.808,
		camber: 0,
		trackWidth: 0.776,
		trackWidthRear: 0.776,
	},
	dubsta2: {
		height: -0.808,
		heightRear: -0.808,
		camber: 0,
		trackWidth: 0.776,
		trackWidthRear: 0.776,
	},
	dubsta3: {
		height: -1.092,
		heightRear: -1.092,
		camber: 0,
		trackWidth: 0.937,
		trackWidthRear: 0.941,
	},
	dukes: {
		height: -0.717,
		heightRear: -0.724,
		camber: 0,
		trackWidth: 0.77,
		trackWidthRear: 0.748,
	},
	dukes2: {
		height: -0.717,
		heightRear: -0.724,
		camber: 0,
		trackWidth: 0.77,
		trackWidthRear: 0.748,
	},
	dukes3: {
		height: -0.717,
		heightRear: -0.724,
		camber: 0,
		trackWidth: 0.77,
		trackWidthRear: 0.802,
	},
	dump: {
		height: -1.721,
		heightRear: -1.721,
		camber: 0,
		trackWidth: 2.197,
		trackWidthRear: 2.31,
	},
	dune: {
		height: -0.681,
		heightRear: -0.681,
		camber: 0,
		trackWidth: 0.647,
		trackWidthRear: 0.832,
	},
	dune2: {
		height: -0.681,
		heightRear: -0.681,
		camber: 0,
		trackWidth: 0.647,
		trackWidthRear: 0.832,
	},
	dune3: {
		height: -0.631,
		heightRear: -0.631,
		camber: 0,
		trackWidth: 0.757,
		trackWidthRear: 0.832,
	},
	dune4: {
		height: -0.541,
		heightRear: -0.541,
		camber: 0,
		trackWidth: 0.846,
		trackWidthRear: 0.846,
	},
	dune5: {
		height: -0.571,
		heightRear: -0.571,
		camber: 0,
		trackWidth: 0.846,
		trackWidthRear: 0.846,
	},
	dynasty: {
		height: -0.432,
		heightRear: -0.432,
		camber: 0,
		trackWidth: 0.697,
		trackWidthRear: 0.697,
	},
	elegy: {
		height: -0.432,
		heightRear: -0.432,
		camber: 0,
		trackWidth: 0.724,
		trackWidthRear: 0.724,
	},
	elegy2: {
		height: -0.425,
		heightRear: -0.425,
		camber: 0,
		trackWidth: 0.784,
		trackWidthRear: 0.808,
	},
	ellie: {
		height: -0.752,
		heightRear: -0.747,
		camber: -0.008,
		trackWidth: 0.729,
		trackWidthRear: 0.743,
	},
	emerus: {
		height: -0.419,
		heightRear: -0.42,
		camber: 0,
		trackWidth: 0.799,
		trackWidthRear: 0.777,
	},
	emperor: {
		height: -0.651,
		heightRear: -0.651,
		camber: 0,
		trackWidth: 0.812,
		trackWidthRear: 0.812,
	},
	emperor2: {
		height: -0.651,
		heightRear: -0.651,
		camber: 0,
		trackWidth: 0.812,
		trackWidthRear: 0.812,
	},
	emperor3: {
		height: -0.651,
		heightRear: -0.651,
		camber: 0,
		trackWidth: 0.812,
		trackWidthRear: 0.812,
	},
	entity2: {
		height: -0.487,
		heightRear: -0.492,
		camber: 0,
		trackWidth: 0.739,
		trackWidthRear: 0.754,
	},
	entity3: {
		height: -0.466,
		heightRear: -0.454,
		camber: 0,
		trackWidth: 0.84,
		trackWidthRear: 0.841,
	},
	entityxf: {
		height: -0.503,
		heightRear: -0.503,
		camber: 0,
		trackWidth: 0.82,
		trackWidthRear: 0.789,
	},
	envisage: {
		height: -0.432,
		heightRear: -0.432,
		camber: 0,
		trackWidth: 0.799,
		trackWidthRear: 0.809,
	},
	eudora: {
		height: -0.666,
		heightRear: -0.666,
		camber: 0,
		trackWidth: 0.738,
		trackWidthRear: 0.738,
	},
	euros: {
		height: -0.439,
		heightRear: -0.439,
		camber: 0,
		trackWidth: 0.748,
		trackWidthRear: 0.778,
	},
	eurosx32: {
		height: -0.577,
		heightRear: -0.577,
		camber: 0,
		trackWidth: 0.753,
		trackWidthRear: 0.753,
	},
	everon: {
		height: -1.273,
		heightRear: -1.273,
		camber: 0,
		trackWidth: 1.011,
		trackWidthRear: 1.011,
	},
	everon2: {
		height: -0.393,
		heightRear: -0.393,
		camber: -0.015,
		trackWidth: 0.834,
		trackWidthRear: 0.831,
	},
	exemplar: {
		height: -0.712,
		heightRear: -0.712,
		camber: 0,
		trackWidth: 0.762,
		trackWidthRear: 0.763,
	},
	f620: {
		height: -0.668,
		heightRear: -0.668,
		camber: 0,
		trackWidth: 0.802,
		trackWidthRear: 0.765,
	},
	faction: {
		height: -0.584,
		heightRear: -0.563,
		camber: 0,
		trackWidth: 0.772,
		trackWidthRear: 0.766,
	},
	faction2: {
		height: -0.467,
		heightRear: -0.467,
		camber: 0,
		trackWidth: 0.801,
		trackWidthRear: 0.796,
	},
	faction3: {
		height: -0.686,
		heightRear: -0.686,
		camber: 0,
		trackWidth: 0.771,
		trackWidthRear: 0.771,
	},
	fagaloa: {
		height: -0.53,
		heightRear: -0.532,
		camber: 0,
		trackWidth: 0.631,
		trackWidthRear: 0.631,
	},
	fbi: {
		height: -0.78,
		heightRear: -0.78,
		camber: 0,
		trackWidth: 0.793,
		trackWidthRear: 0.793,
	},
	fbi2: {
		height: -0.762,
		heightRear: -0.762,
		camber: 0,
		trackWidth: 0.849,
		trackWidthRear: 0.849,
	},
	felon: {
		height: -0.795,
		heightRear: -0.795,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.783,
	},
	felon2: {
		height: -0.803,
		heightRear: -0.803,
		camber: 0,
		trackWidth: 0.804,
		trackWidthRear: 0.804,
	},
	feltzer2: {
		height: -0.731,
		heightRear: -0.73,
		camber: 0,
		trackWidth: 0.816,
		trackWidthRear: 0.801,
	},
	feltzer3: {
		height: -0.292,
		heightRear: -0.295,
		camber: 0,
		trackWidth: 0.718,
		trackWidthRear: 0.665,
	},
	firetruk: {
		height: -1.348,
		heightRear: -1.348,
		camber: 0,
		trackWidth: 0.927,
		trackWidthRear: 0.904,
	},
	flashgt: {
		height: -0.904,
		heightRear: -0.904,
		camber: -0.005,
		trackWidth: 0.78,
		trackWidthRear: 0.829,
	},
	fmj: {
		height: -0.463,
		heightRear: -0.469,
		camber: 0,
		trackWidth: 0.796,
		trackWidthRear: 0.82,
	},
	forklift: {
		height: -0.604,
		heightRear: -0.604,
		camber: 0,
		trackWidth: 0.48,
		trackWidthRear: 0.49,
	},
	formula: {
		height: -0.334,
		heightRear: -0.334,
		camber: 0,
		trackWidth: 0.746,
		trackWidthRear: 0.633,
	},
	formula2: {
		height: -0.334,
		heightRear: -0.334,
		camber: 0,
		trackWidth: 0.745,
		trackWidthRear: 0.633,
	},
	fq2: {
		height: -0.828,
		heightRear: -0.828,
		camber: 0,
		trackWidth: 0.793,
		trackWidthRear: 0.793,
	},
	fr36: {
		height: -0.658,
		heightRear: -0.668,
		camber: 0,
		trackWidth: 0.753,
		trackWidthRear: 0.755,
	},
	freecrawler: {
		height: -0.84,
		heightRear: -0.84,
		camber: 0,
		trackWidth: 0.815,
		trackWidthRear: 0.78,
	},
	fugitive: {
		height: -0.844,
		heightRear: -0.844,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.783,
	},
	furia: {
		height: -0.493,
		heightRear: -0.493,
		camber: 0,
		trackWidth: 0.811,
		trackWidthRear: 0.811,
	},
	furoregt: {
		height: -0.499,
		heightRear: -0.502,
		camber: 0,
		trackWidth: 0.8,
		trackWidthRear: 0.779,
	},
	fusilade: {
		height: -0.381,
		heightRear: -0.381,
		camber: 0,
		trackWidth: 0.747,
		trackWidthRear: 0.822,
	},
	futo: {
		height: -0.586,
		heightRear: -0.586,
		camber: 0,
		trackWidth: 0.695,
		trackWidthRear: 0.695,
	},
	futo2: {
		height: -0.611,
		heightRear: -0.611,
		camber: 0,
		trackWidth: 0.695,
		trackWidthRear: 0.695,
	},
	gauntlet: {
		height: -0.688,
		heightRear: -0.688,
		camber: 0,
		trackWidth: 0.734,
		trackWidthRear: 0.783,
	},
	gauntlet2: {
		height: -0.688,
		heightRear: -0.688,
		camber: 0,
		trackWidth: 0.734,
		trackWidthRear: 0.783,
	},
	gauntlet3: {
		height: -0.456,
		heightRear: -0.456,
		camber: 0,
		trackWidth: 0.735,
		trackWidthRear: 0.75,
	},
	gauntlet4: {
		height: -0.49,
		heightRear: -0.494,
		camber: 0,
		trackWidth: 0.849,
		trackWidthRear: 0.843,
	},
	gauntlet5: {
		height: -0.456,
		heightRear: -0.456,
		camber: 0,
		trackWidth: 0.735,
		trackWidthRear: 0.75,
	},
	gauntlet6: {
		height: -0.45,
		heightRear: -0.45,
		camber: 0,
		trackWidth: 0.804,
		trackWidthRear: 0.801,
	},
	gb200: {
		height: -0.355,
		heightRear: -0.355,
		camber: -0.005,
		trackWidth: 0.72,
		trackWidthRear: 0.731,
	},
	gburrito: {
		height: -0.966,
		heightRear: -0.966,
		camber: 0,
		trackWidth: 1.003,
		trackWidthRear: 1.02,
	},
	gburrito2: {
		height: -0.958,
		heightRear: -0.966,
		camber: 0,
		trackWidth: 0.923,
		trackWidthRear: 1.02,
	},
	glendale: {
		height: -0.864,
		heightRear: -0.864,
		camber: 0,
		trackWidth: 0.7,
		trackWidthRear: 0.7,
	},
	glendale2: {
		height: -0.726,
		heightRear: -0.726,
		camber: -0.009,
		trackWidth: 0.705,
		trackWidthRear: 0.7,
	},
	gp1: {
		height: -0.446,
		heightRear: -0.468,
		camber: 0,
		trackWidth: 0.691,
		trackWidthRear: 0.698,
	},
	granger: {
		height: -0.762,
		heightRear: -0.762,
		camber: 0,
		trackWidth: 0.849,
		trackWidthRear: 0.849,
	},
	greenwood: {
		height: -0.569,
		heightRear: -0.569,
		camber: 0,
		trackWidth: 0.854,
		trackWidthRear: 0.854,
	},
	gresley: {
		height: -1.134,
		heightRear: -1.134,
		camber: 0,
		trackWidth: 0.888,
		trackWidthRear: 0.862,
	},
	growler: {
		height: -0.482,
		heightRear: -0.475,
		camber: 0,
		trackWidth: 0.737,
		trackWidthRear: 0.743,
	},
	gt500: {
		height: -0.707,
		heightRear: -0.707,
		camber: 0,
		trackWidth: 0.71,
		trackWidthRear: 0.71,
	},
	guardian: {
		height: -1.48,
		heightRear: -1.48,
		camber: 0,
		trackWidth: 0.988,
		trackWidthRear: 0.944,
	},
	habanero: {
		height: -0.878,
		heightRear: -0.878,
		camber: 0,
		trackWidth: 0.793,
		trackWidthRear: 0.793,
	},
	halftrack: {
		height: -0.834,
		heightRear: -0.763,
		camber: 0,
		trackWidth: 0.913,
		trackWidthRear: 0.88,
	},
	handler: {
		height: -1.387,
		heightRear: -1.378,
		camber: 0,
		trackWidth: 2.173,
		trackWidthRear: 1.317,
	},
	hauler: {
		height: -1.446,
		heightRear: -1.446,
		camber: 0,
		trackWidth: 1.129,
		trackWidthRear: 1.262,
	},
	hauler2: {
		height: -1.446,
		heightRear: -1.446,
		camber: 0,
		trackWidth: 1.129,
		trackWidthRear: 1.262,
	},
	hellion: {
		height: -0.801,
		heightRear: -0.801,
		camber: 0,
		trackWidth: 0.894,
		trackWidthRear: 0.894,
	},
	hermes: {
		height: -0.535,
		heightRear: -0.535,
		camber: 0,
		trackWidth: 0.777,
		trackWidthRear: 0.738,
	},
	hotknife: {
		height: -0.4,
		heightRear: -0.415,
		camber: 0,
		trackWidth: 0.721,
		trackWidthRear: 0.755,
	},
	hotring: {
		height: -0.61,
		heightRear: -0.61,
		camber: -0.013,
		trackWidth: 0.724,
		trackWidthRear: 0.732,
	},
	huntley: {
		height: -0.527,
		heightRear: -0.527,
		camber: 0,
		trackWidth: 0.846,
		trackWidthRear: 0.846,
	},
	hustler: {
		height: -0.468,
		heightRear: -0.468,
		camber: 0,
		trackWidth: 0.777,
		trackWidthRear: 0.755,
	},
	ignus: {
		height: -0.469,
		heightRear: -0.468,
		camber: 0,
		trackWidth: 0.874,
		trackWidthRear: 0.817,
	},
	imorgon: {
		height: -0.915,
		heightRear: -0.944,
		camber: 0,
		trackWidth: 0.792,
		trackWidthRear: 0.817,
	},
	impaler: {
		height: -0.503,
		heightRear: -0.511,
		camber: 0,
		trackWidth: 0.816,
		trackWidthRear: 0.789,
	},
	impaler2: {
		height: -0.508,
		heightRear: -0.503,
		camber: 0,
		trackWidth: 0.816,
		trackWidthRear: 0.821,
	},
	impaler3: {
		height: -0.443,
		heightRear: -0.447,
		camber: 0,
		trackWidth: 0.816,
		trackWidthRear: 0.789,
	},
	impaler4: {
		height: -0.508,
		heightRear: -0.503,
		camber: 0,
		trackWidth: 0.816,
		trackWidthRear: 0.821,
	},
	impaler5: {
		height: -0.401,
		heightRear: -0.401,
		camber: 0,
		trackWidth: 0.815,
		trackWidthRear: 0.829,
	},
	impaler6: {
		height: -0.601,
		heightRear: -0.601,
		camber: 0,
		trackWidth: 0.811,
		trackWidthRear: 0.811,
	},
	imperator: {
		height: -0.491,
		heightRear: -0.496,
		camber: -0.006,
		trackWidth: 0.809,
		trackWidthRear: 0.855,
	},
	imperator2: {
		height: -0.491,
		heightRear: -0.496,
		camber: -0.006,
		trackWidth: 0.809,
		trackWidthRear: 0.855,
	},
	imperator3: {
		height: -0.491,
		heightRear: -0.496,
		camber: -0.006,
		trackWidth: 0.809,
		trackWidthRear: 0.855,
	},
	infernus: {
		height: -0.594,
		heightRear: -0.594,
		camber: 0,
		trackWidth: 0.779,
		trackWidthRear: 0.793,
	},
	infernus2: {
		height: -0.588,
		heightRear: -0.589,
		camber: 0,
		trackWidth: 0.777,
		trackWidthRear: 0.824,
	},
	ingot: {
		height: -0.82,
		heightRear: -0.82,
		camber: 0,
		trackWidth: 0.679,
		trackWidthRear: 0.679,
	},
	insurgent: {
		height: -1.102,
		heightRear: -1.102,
		camber: 0,
		trackWidth: 1.052,
		trackWidthRear: 1.052,
	},
	insurgent2: {
		height: -1.102,
		heightRear: -1.102,
		camber: 0,
		trackWidth: 1.052,
		trackWidthRear: 1.052,
	},
	insurgent3: {
		height: -1.102,
		heightRear: -1.102,
		camber: 0,
		trackWidth: 1.052,
		trackWidthRear: 1.052,
	},
	intruder: {
		height: -0.736,
		heightRear: -0.736,
		camber: 0,
		trackWidth: 0.743,
		trackWidthRear: 0.77,
	},
	issi2: {
		height: -0.783,
		heightRear: -0.783,
		camber: 0,
		trackWidth: 0.701,
		trackWidthRear: 0.701,
	},
	issi3: {
		height: -0.43,
		heightRear: -0.43,
		camber: -0.006,
		trackWidth: 0.625,
		trackWidthRear: 0.621,
	},
	issi4: {
		height: -0.635,
		heightRear: -0.65,
		camber: -0.003,
		trackWidth: 0.622,
		trackWidthRear: 0.621,
	},
	issi5: {
		height: -0.635,
		heightRear: -0.65,
		camber: -0.003,
		trackWidth: 0.622,
		trackWidthRear: 0.621,
	},
	issi6: {
		height: -0.635,
		heightRear: -0.65,
		camber: -0.003,
		trackWidth: 0.622,
		trackWidthRear: 0.621,
	},
	issi7: {
		height: -0.81,
		heightRear: -0.82,
		camber: -0.005,
		trackWidth: 0.716,
		trackWidthRear: 0.734,
	},
	issi8: {
		height: -0.829,
		heightRear: -0.829,
		camber: 0,
		trackWidth: 0.803,
		trackWidthRear: 0.791,
	},
	italigtb: {
		height: -0.377,
		heightRear: -0.378,
		camber: 0,
		trackWidth: 0.738,
		trackWidthRear: 0.783,
	},
	italigtb2: {
		height: -0.38,
		heightRear: -0.378,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.786,
	},
	italigto: {
		height: -0.505,
		heightRear: -0.515,
		camber: 0,
		trackWidth: 0.73,
		trackWidthRear: 0.745,
	},
	italirsx: {
		height: -0.466,
		heightRear: -0.466,
		camber: 0,
		trackWidth: 0.819,
		trackWidthRear: 0.819,
	},
	iwagen: {
		height: -0.579,
		heightRear: -0.579,
		camber: 0,
		trackWidth: 0.837,
		trackWidthRear: 0.837,
	},
	jackal: {
		height: -0.832,
		heightRear: -0.832,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.835,
	},
	jb700: {
		height: -0.654,
		heightRear: -0.654,
		camber: 0,
		trackWidth: 0.674,
		trackWidthRear: 0.674,
	},
	jb7002: {
		height: -0.654,
		heightRear: -0.654,
		camber: 0,
		trackWidth: 0.674,
		trackWidthRear: 0.674,
	},
	jester: {
		height: -0.336,
		heightRear: -0.336,
		camber: 0,
		trackWidth: 0.786,
		trackWidthRear: 0.76,
	},
	jester2: {
		height: -0.336,
		heightRear: -0.336,
		camber: 0,
		trackWidth: 0.786,
		trackWidthRear: 0.76,
	},
	jester3: {
		height: -0.378,
		heightRear: -0.378,
		camber: 0,
		trackWidth: 0.735,
		trackWidthRear: 0.738,
	},
	jester4: {
		height: -0.475,
		heightRear: -0.475,
		camber: 0,
		trackWidth: 0.776,
		trackWidthRear: 0.785,
	},
	journey: {
		height: -0.656,
		heightRear: -0.656,
		camber: 0,
		trackWidth: 0.867,
		trackWidthRear: 0.868,
	},
	journey2: {
		height: -0.656,
		heightRear: -0.656,
		camber: 0,
		trackWidth: 0.867,
		trackWidthRear: 0.868,
	},
	jugular: {
		height: -0.543,
		heightRear: -0.543,
		camber: 0,
		trackWidth: 0.802,
		trackWidthRear: 0.785,
	},
	kalahari: {
		height: -0.675,
		heightRear: -0.675,
		camber: 0,
		trackWidth: 0.701,
		trackWidthRear: 0.701,
	},
	kamacho: {
		height: -0.926,
		heightRear: -0.926,
		camber: 0,
		trackWidth: 1.008,
		trackWidthRear: 1.008,
	},
	kanjo: {
		height: -0.563,
		heightRear: -0.563,
		camber: 0,
		trackWidth: 0.715,
		trackWidthRear: 0.723,
	},
	kanjosj: {
		height: -0.428,
		heightRear: -0.428,
		camber: 0,
		trackWidth: 0.735,
		trackWidthRear: 0.735,
	},
	khamelion: {
		height: -0.378,
		heightRear: -0.378,
		camber: 0,
		trackWidth: 0.828,
		trackWidthRear: 0.808,
	},
	khanjali: {
		height: -1.683,
		heightRear: -1.683,
		camber: 0,
		trackWidth: 1.147,
		trackWidthRear: 1.167,
	},
	komoda: {
		height: -0.447,
		heightRear: -0.447,
		camber: 0,
		trackWidth: 0.74,
		trackWidthRear: 0.74,
	},
	krieger: {
		height: -0.653,
		heightRear: -0.644,
		camber: 0,
		trackWidth: 0.81,
		trackWidthRear: 0.796,
	},
	kuruma: {
		height: -0.833,
		heightRear: -0.833,
		camber: 0,
		trackWidth: 0.751,
		trackWidthRear: 0.751,
	},
	kuruma2: {
		height: -0.833,
		heightRear: -0.833,
		camber: 0,
		trackWidth: 0.751,
		trackWidthRear: 0.751,
	},
	l35: {
		height: -0.884,
		heightRear: -0.884,
		camber: 0,
		trackWidth: 0.746,
		trackWidthRear: 0.746,
	},
	landstalker: {
		height: -0.66,
		heightRear: -0.66,
		camber: 0,
		trackWidth: 0.803,
		trackWidthRear: 0.804,
	},
	landstalker2: {
		height: -0.971,
		heightRear: -0.971,
		camber: 0,
		trackWidth: 0.862,
		trackWidthRear: 0.862,
	},
	le7b: {
		height: -0.43,
		heightRear: -0.43,
		camber: 0,
		trackWidth: 0.856,
		trackWidthRear: 0.802,
	},
	lguard: {
		height: -0.802,
		heightRear: -0.802,
		camber: 0,
		trackWidth: 0.849,
		trackWidthRear: 0.849,
	},
	limo2: {
		height: -0.702,
		heightRear: -0.705,
		camber: 0,
		trackWidth: 0.747,
		trackWidthRear: 0.744,
	},
	lm87: {
		height: -0.374,
		heightRear: -0.374,
		camber: 0,
		trackWidth: 0.81,
		trackWidthRear: 0.749,
	},
	locust: {
		height: -0.499,
		heightRear: -0.499,
		camber: -0.005,
		trackWidth: 0.768,
		trackWidthRear: 0.769,
	},
	lurcher: {
		height: -0.507,
		heightRear: -0.464,
		camber: 0,
		trackWidth: 0.79,
		trackWidthRear: 0.755,
	},
	lynx: {
		height: -0.713,
		heightRear: -0.695,
		camber: 0,
		trackWidth: 0.784,
		trackWidthRear: 0.803,
	},
	mamba: {
		height: -0.372,
		heightRear: -0.376,
		camber: 0,
		trackWidth: 0.63,
		trackWidthRear: 0.616,
	},
	manana: {
		height: -0.691,
		heightRear: -0.691,
		camber: 0,
		trackWidth: 0.798,
		trackWidthRear: 0.798,
	},
	manana2: {
		height: -0.596,
		heightRear: -0.596,
		camber: -0.011,
		trackWidth: 0.803,
		trackWidthRear: 0.798,
	},
	marshall: {
		height: -1.292,
		heightRear: -1.29,
		camber: 0,
		trackWidth: 1.435,
		trackWidthRear: 1.435,
	},
	massacro: {
		height: -0.5,
		heightRear: -0.499,
		camber: 0,
		trackWidth: 0.787,
		trackWidthRear: 0.749,
	},
	massacro2: {
		height: -0.5,
		heightRear: -0.499,
		camber: 0,
		trackWidth: 0.787,
		trackWidthRear: 0.749,
	},
	menacer: {
		height: -0.864,
		heightRear: -0.864,
		camber: 0,
		trackWidth: 1.214,
		trackWidthRear: 1.214,
	},
	mesa: {
		height: -0.62,
		heightRear: -0.62,
		camber: 0,
		trackWidth: 0.775,
		trackWidthRear: 0.795,
	},
	mesa2: {
		height: -0.62,
		heightRear: -0.62,
		camber: 0,
		trackWidth: 0.775,
		trackWidthRear: 0.795,
	},
	mesa3: {
		height: -0.891,
		heightRear: -0.891,
		camber: 0,
		trackWidth: 0.922,
		trackWidthRear: 0.922,
	},
	michelli: {
		height: -0.577,
		heightRear: -0.577,
		camber: -0.013,
		trackWidth: 0.672,
		trackWidthRear: 0.656,
	},
	minitank: {
		height: -0.438,
		heightRear: -0.438,
		camber: 0,
		trackWidth: 0.33,
		trackWidthRear: 0.33,
	},
	minivan: {
		height: -0.935,
		heightRear: -0.935,
		camber: 0,
		trackWidth: 0.85,
		trackWidthRear: 0.85,
	},
	minivan2: {
		height: -0.877,
		heightRear: -0.877,
		camber: 0,
		trackWidth: 0.85,
		trackWidthRear: 0.85,
	},
	mixer: {
		height: -0.807,
		heightRear: -0.807,
		camber: 0,
		trackWidth: 1.031,
		trackWidthRear: 1.079,
	},
	mixer2: {
		height: -1.218,
		heightRear: -1.218,
		camber: 0,
		trackWidth: 1.052,
		trackWidthRear: 1.079,
	},
	monroe: {
		height: -0.358,
		heightRear: -0.358,
		camber: 0,
		trackWidth: 0.76,
		trackWidthRear: 0.738,
	},
	monster: {
		height: -1.292,
		heightRear: -1.29,
		camber: 0,
		trackWidth: 1.435,
		trackWidthRear: 1.435,
	},
	monster3: {
		height: -1.542,
		heightRear: -1.54,
		camber: 0,
		trackWidth: 1.435,
		trackWidthRear: 1.435,
	},
	monster4: {
		height: -1.542,
		heightRear: -1.54,
		camber: 0,
		trackWidth: 1.435,
		trackWidthRear: 1.435,
	},
	monster5: {
		height: -1.542,
		heightRear: -1.54,
		camber: 0,
		trackWidth: 1.435,
		trackWidthRear: 1.435,
	},
	monstrociti: {
		height: -0.758,
		heightRear: -0.758,
		camber: 0,
		trackWidth: 0.789,
		trackWidthRear: 0.789,
	},
	moonbeam: {
		height: -0.768,
		heightRear: -0.768,
		camber: 0,
		trackWidth: 0.767,
		trackWidthRear: 0.805,
	},
	moonbeam2: {
		height: -0.596,
		heightRear: -0.596,
		camber: 0,
		trackWidth: 0.805,
		trackWidthRear: 0.864,
	},
	mower: {
		height: -0.544,
		heightRear: -0.544,
		camber: 0,
		trackWidth: 0.389,
		trackWidthRear: 0.408,
	},
	mule: {
		height: -1.455,
		heightRear: -1.455,
		camber: 0,
		trackWidth: 0.933,
		trackWidthRear: 1.065,
	},
	mule2: {
		height: -1.455,
		heightRear: -1.455,
		camber: 0,
		trackWidth: 0.933,
		trackWidthRear: 1.065,
	},
	mule3: {
		height: -1.455,
		heightRear: -1.455,
		camber: 0,
		trackWidth: 0.933,
		trackWidthRear: 1.065,
	},
	mule4: {
		height: -1.455,
		heightRear: -1.455,
		camber: 0,
		trackWidth: 0.933,
		trackWidthRear: 1.065,
	},
	nebula: {
		height: -0.549,
		heightRear: -0.549,
		camber: 0,
		trackWidth: 0.714,
		trackWidthRear: 0.714,
	},
	neo: {
		height: -0.468,
		heightRear: -0.468,
		camber: 0,
		trackWidth: 0.806,
		trackWidthRear: 0.838,
	},
	neon: {
		height: -0.73,
		heightRear: -0.725,
		camber: 0,
		trackWidth: 0.817,
		trackWidthRear: 0.83,
	},
	nero: {
		height: -0.686,
		heightRear: -0.69,
		camber: 0,
		trackWidth: 0.855,
		trackWidthRear: 0.849,
	},
	nero2: {
		height: -0.686,
		heightRear: -0.69,
		camber: 0,
		trackWidth: 0.855,
		trackWidthRear: 0.849,
	},
	nightshade: {
		height: -0.555,
		heightRear: -0.556,
		camber: 0,
		trackWidth: 0.842,
		trackWidthRear: 0.876,
	},
	nightshark: {
		height: -0.572,
		heightRear: -0.572,
		camber: 0,
		trackWidth: 0.878,
		trackWidthRear: 0.878,
	},
	ninef: {
		height: -0.723,
		heightRear: -0.723,
		camber: 0,
		trackWidth: 0.813,
		trackWidthRear: 0.778,
	},
	ninef2: {
		height: -0.719,
		heightRear: -0.719,
		camber: 0,
		trackWidth: 0.813,
		trackWidthRear: 0.778,
	},
	niobe: {
		height: -0.483,
		heightRear: -0.483,
		camber: 0,
		trackWidth: 0.800,
		trackWidthRear: 0.800,
	},
	novak: {
		height: -0.679,
		heightRear: -0.679,
		camber: 0,
		trackWidth: 0.764,
		trackWidthRear: 0.793,
	},
	omnis: {
		height: -0.517,
		heightRear: -0.517,
		camber: 0,
		trackWidth: 0.759,
		trackWidthRear: 0.759,
	},
	omnisegt: {
		height: -0.557,
		heightRear: -0.557,
		camber: 0,
		trackWidth: 0.818,
		trackWidthRear: 0.818,
	},
	openwheel1: {
		height: -0.267,
		heightRear: -0.267,
		camber: 0,
		trackWidth: 0.656,
		trackWidthRear: 0.608,
	},
	openwheel2: {
		height: -0.321,
		heightRear: -0.321,
		camber: 0,
		trackWidth: 0.651,
		trackWidthRear: 0.576,
	},
	oracle: {
		height: -0.936,
		heightRear: -0.936,
		camber: 0,
		trackWidth: 0.814,
		trackWidthRear: 0.768,
	},
	oracle2: {
		height: -0.428,
		heightRear: -0.428,
		camber: 0,
		trackWidth: 0.818,
		trackWidthRear: 0.818,
	},
	osiris: {
		height: -0.685,
		heightRear: -0.693,
		camber: 0,
		trackWidth: 0.802,
		trackWidthRear: 0.831,
	},
	outlaw: {
		height: -1.11,
		heightRear: -1.11,
		camber: 0,
		trackWidth: 0.78,
		trackWidthRear: 0.78,
	},
	packer: {
		height: -1.298,
		heightRear: -1.304,
		camber: 0,
		trackWidth: 1.262,
		trackWidthRear: 1.262,
	},
	panthere: {
		height: -0.469,
		heightRear: -0.469,
		camber: 0,
		trackWidth: 0.793,
		trackWidthRear: 0.793,
	},
	panto: {
		height: -0.435,
		heightRear: -0.435,
		camber: 0,
		trackWidth: 0.637,
		trackWidthRear: 0.633,
	},
	paradise: {
		height: -1.157,
		heightRear: -1.157,
		camber: 0,
		trackWidth: 0.921,
		trackWidthRear: 0.932,
	},
	paragon: {
		height: -0.546,
		heightRear: -0.546,
		camber: 0,
		trackWidth: 0.795,
		trackWidthRear: 0.802,
	},
	paragon2: {
		height: -0.546,
		heightRear: -0.546,
		camber: 0,
		trackWidth: 0.795,
		trackWidthRear: 0.802,
	},
	paragon3: {
		height: -0.644,
		heightRear: -0.644,
		camber: 0,
		trackWidth: 0.820,
		trackWidthRear: 0.820,
	},
	pariah: {
		height: -0.519,
		heightRear: -0.519,
		camber: -0.001,
		trackWidth: 0.726,
		trackWidthRear: 0.76,
	},
	patriot: {
		height: -0.855,
		heightRear: -0.866,
		camber: 0,
		trackWidth: 0.906,
		trackWidthRear: 0.895,
	},
	patriot2: {
		height: -0.935,
		heightRear: -0.946,
		camber: -0.005,
		trackWidth: 0.911,
		trackWidthRear: 0.895,
	},
	pbus: {
		height: -1.464,
		heightRear: -1.464,
		camber: 0,
		trackWidth: 0.991,
		trackWidthRear: 0.991,
	},
	pbus2: {
		height: -1.484,
		heightRear: -1.484,
		camber: 0,
		trackWidth: 0.991,
		trackWidthRear: 0.991,
	},
	penetrator: {
		height: -0.386,
		heightRear: -0.386,
		camber: 0,
		trackWidth: 0.838,
		trackWidthRear: 0.826,
	},
	penumbra: {
		height: -0.596,
		heightRear: -0.596,
		camber: 0,
		trackWidth: 0.762,
		trackWidthRear: 0.762,
	},
	penumbra2: {
		height: -0.777,
		heightRear: -0.777,
		camber: 0,
		trackWidth: 0.758,
		trackWidthRear: 0.739,
	},
	peyote: {
		height: -0.51,
		heightRear: -0.51,
		camber: 0,
		trackWidth: 0.798,
		trackWidthRear: 0.798,
	},
	peyote2: {
		height: -0.669,
		heightRear: -0.666,
		camber: 0,
		trackWidth: 0.798,
		trackWidthRear: 0.798,
	},
	peyote3: {
		height: -0.402,
		heightRear: -0.402,
		camber: -0.005,
		trackWidth: 0.801,
		trackWidthRear: 0.798,
	},
	pfister811: {
		height: -0.411,
		heightRear: -0.411,
		camber: 0,
		trackWidth: 0.784,
		trackWidthRear: 0.761,
	},
	phantom: {
		height: -1.349,
		heightRear: -1.298,
		camber: 0,
		trackWidth: 1.137,
		trackWidthRear: 1.231,
	},
	phantom2: {
		height: -1.349,
		heightRear: -1.298,
		camber: 0,
		trackWidth: 1.137,
		trackWidthRear: 1.231,
	},
	phantom3: {
		height: -1.249,
		heightRear: -1.198,
		camber: 0,
		trackWidth: 1.137,
		trackWidthRear: 1.231,
	},
	phoenix: {
		height: -0.564,
		heightRear: -0.564,
		camber: 0,
		trackWidth: 0.79,
		trackWidthRear: 0.761,
	},
	picador: {
		height: -0.768,
		heightRear: -0.768,
		camber: 0,
		trackWidth: 0.667,
		trackWidthRear: 0.725,
	},
	pigalle: {
		height: -0.543,
		heightRear: -0.543,
		camber: 0,
		trackWidth: 0.71,
		trackWidthRear: 0.71,
	},
	pipistrello: {
		height: -0.548,
		heightRear: -0.550,
		camber: 0,
		trackWidth: 0.865,
		trackWidthRear: 0.885,
	},
	police: {
		height: -0.757,
		heightRear: -0.757,
		camber: 0,
		trackWidth: 0.823,
		trackWidthRear: 0.823,
	},
	police2: {
		height: -0.746,
		heightRear: -0.746,
		camber: 0,
		trackWidth: 0.785,
		trackWidthRear: 0.785,
	},
	police3: {
		height: -0.888,
		heightRear: -0.889,
		camber: 0,
		trackWidth: 0.818,
		trackWidthRear: 0.818,
	},
	police4: {
		height: -0.757,
		heightRear: -0.757,
		camber: 0,
		trackWidth: 0.823,
		trackWidthRear: 0.823,
	},
	policeb: {
		height: -0.629,
		heightRear: 0,
		camber: 0,
		trackWidth: 0,
		trackWidthRear: 0,
	},
	policeold1: {
		height: -0.752,
		heightRear: -0.752,
		camber: 0,
		trackWidth: 0.86,
		trackWidthRear: 0.868,
	},
	policeold2: {
		height: -0.695,
		heightRear: -0.695,
		camber: 0,
		trackWidth: 0.818,
		trackWidthRear: 0.818,
	},
	policet: {
		height: -1.207,
		heightRear: -1.207,
		camber: 0,
		trackWidth: 0.98,
		trackWidthRear: 0.98,
	},
	pony: {
		height: -0.941,
		heightRear: -0.941,
		camber: 0,
		trackWidth: 0.928,
		trackWidthRear: 0.928,
	},
	pony2: {
		height: -0.941,
		heightRear: -0.941,
		camber: 0,
		trackWidth: 0.928,
		trackWidthRear: 0.928,
	},
	postlude: {
		height: -0.504,
		heightRear: -0.504,
		camber: 0,
		trackWidth: 0.737,
		trackWidthRear: 0.737,
	},
	pounder: {
		height: -1.297,
		heightRear: -1.293,
		camber: 0,
		trackWidth: 1.262,
		trackWidthRear: 1.262,
	},
	pounder2: {
		height: -1.298,
		heightRear: -1.294,
		camber: 0,
		trackWidth: 1.262,
		trackWidthRear: 1.262,
	},
	prairie: {
		height: -0.67,
		heightRear: -0.67,
		camber: 0,
		trackWidth: 0.748,
		trackWidthRear: 0.748,
	},
	pranger: {
		height: -0.762,
		heightRear: -0.762,
		camber: 0,
		trackWidth: 0.849,
		trackWidthRear: 0.849,
	},
	premier: {
		height: -0.657,
		heightRear: -0.657,
		camber: 0,
		trackWidth: 0.734,
		trackWidthRear: 0.734,
	},
	previon: {
		height: -0.819,
		heightRear: -0.819,
		camber: 0,
		trackWidth: 0.785,
		trackWidthRear: 0.768,
	},
	primo: {
		height: -0.681,
		heightRear: -0.681,
		camber: 0,
		trackWidth: 0.796,
		trackWidthRear: 0.796,
	},
	primo2: {
		height: -0.64,
		heightRear: -0.64,
		camber: 0,
		trackWidth: 0.776,
		trackWidthRear: 0.783,
	},
	prototipo: {
		height: -0.356,
		heightRear: -0.357,
		camber: 0,
		trackWidth: 0.913,
		trackWidthRear: 0.837,
	},
	r300: {
		height: -0.477,
		heightRear: -0.477,
		camber: 0,
		trackWidth: 0.735,
		trackWidthRear: 0.735,
	},
	radi: {
		height: -0.778,
		heightRear: -0.778,
		camber: 0,
		trackWidth: 0.884,
		trackWidthRear: 0.857,
	},
	raiden: {
		height: -0.771,
		heightRear: -0.771,
		camber: 0,
		trackWidth: 0.81,
		trackWidthRear: 0.837,
	},
	raiju: {
		height: -2.084,
		heightRear: -2.084,
		camber: 0,
		trackWidth: -0.009,
		trackWidthRear: -2.252,
	},
	rallytruck: {
		height: -1.744,
		heightRear: -1.744,
		camber: 0,
		trackWidth: 1.206,
		trackWidthRear: 1.206,
	},
	rancherxl: {
		height: -0.752,
		heightRear: -0.752,
		camber: 0,
		trackWidth: 0.86,
		trackWidthRear: 0.868,
	},
	rancherxl2: {
		height: -0.752,
		heightRear: -0.752,
		camber: 0,
		trackWidth: 0.86,
		trackWidthRear: 0.868,
	},
	rapidgt: {
		height: -0.663,
		heightRear: -0.663,
		camber: 0,
		trackWidth: 0.762,
		trackWidthRear: 0.763,
	},
	rapidgt2: {
		height: -0.663,
		heightRear: -0.663,
		camber: 0,
		trackWidth: 0.762,
		trackWidthRear: 0.763,
	},
	rapidgt3: {
		height: -0.757,
		heightRear: -0.757,
		camber: 0,
		trackWidth: 0.755,
		trackWidthRear: 0.755,
	},
	raptor: {
		height: -0.391,
		heightRear: -0.39,
		camber: 0,
		trackWidth: 0.852,
		trackWidthRear: 0,
	},
	ratel: {
		height: -0.895,
		heightRear: -0.895,
		camber: 0,
		trackWidth: 0.986,
		trackWidthRear: 0.986,
	},
	ratloader: {
		height: -0.454,
		heightRear: -0.454,
		camber: 0,
		trackWidth: 0.859,
		trackWidthRear: 0.859,
	},
	ratloader2: {
		height: -0.454,
		heightRear: -0.457,
		camber: 0,
		trackWidth: 0.859,
		trackWidthRear: 0.905,
	},
	rcbandito: {
		height: -0.214,
		heightRear: -0.214,
		camber: 0,
		trackWidth: 0.291,
		trackWidthRear: 0.311,
	},
	reaper: {
		height: -0.613,
		heightRear: -0.613,
		camber: 0,
		trackWidth: 0.726,
		trackWidthRear: 0.771,
	},
	rebel: {
		height: -0.806,
		heightRear: -0.806,
		camber: 0,
		trackWidth: 0.861,
		trackWidthRear: 0.861,
	},
	rebel2: {
		height: -0.806,
		heightRear: -0.806,
		camber: 0,
		trackWidth: 0.861,
		trackWidthRear: 0.861,
	},
	rebla: {
		height: -0.555,
		heightRear: -0.555,
		camber: 0,
		trackWidth: 0.872,
		trackWidthRear: 0.872,
	},
	regina: {
		height: -0.841,
		heightRear: -0.841,
		camber: 0,
		trackWidth: 0.795,
		trackWidthRear: 0.845,
	},
	remus: {
		height: -0.408,
		heightRear: -0.408,
		camber: 0,
		trackWidth: 0.732,
		trackWidthRear: 0.732,
	},
	rentalbus: {
		height: -0.96,
		heightRear: -0.96,
		camber: 0,
		trackWidth: 0.949,
		trackWidthRear: 1.092,
	},
	retinue: {
		height: -0.508,
		heightRear: -0.508,
		camber: 0,
		trackWidth: 0.679,
		trackWidthRear: 0.743,
	},
	retinue2: {
		height: -0.522,
		heightRear: -0.522,
		camber: 0,
		trackWidth: 0.669,
		trackWidthRear: 0.674,
	},
	revolter: {
		height: -0.634,
		heightRear: -0.634,
		camber: 0,
		trackWidth: 0.857,
		trackWidthRear: 0.857,
	},
	rhapsody: {
		height: -0.799,
		heightRear: -0.799,
		camber: 0,
		trackWidth: 0.768,
		trackWidthRear: 0.753,
	},
	rhinehart: {
		height: -0.865,
		heightRear: -0.877,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.784,
	},
	rhino: {
		height: -1.044,
		heightRear: -1.044,
		camber: 0,
		trackWidth: 1.372,
		trackWidthRear: 1.372,
	},
	riata: {
		height: -1.204,
		heightRear: -1.204,
		camber: 0,
		trackWidth: 1.122,
		trackWidthRear: 1.134,
	},
	riot: {
		height: -0.811,
		heightRear: -0.815,
		camber: 0,
		trackWidth: 1.17,
		trackWidthRear: 1.256,
	},
	riot2: {
		height: -1.007,
		heightRear: -1.011,
		camber: 0,
		trackWidth: 1.15,
		trackWidthRear: 1.231,
	},
	ripley: {
		height: -1.249,
		heightRear: -1.249,
		camber: 0,
		trackWidth: 1.001,
		trackWidthRear: 1.001,
	},
	rocoto: {
		height: -1.047,
		heightRear: -1.047,
		camber: 0,
		trackWidth: 0.813,
		trackWidthRear: 0.792,
	},
	romero: {
		height: -0.67,
		heightRear: -0.67,
		camber: 0,
		trackWidth: 0.785,
		trackWidthRear: 0.789,
	},
	rt3000: {
		height: -0.438,
		heightRear: -0.438,
		camber: 0,
		trackWidth: 0.722,
		trackWidthRear: 0.742,
	},
	rubble: {
		height: -1.324,
		heightRear: -1.324,
		camber: 0,
		trackWidth: 1.137,
		trackWidthRear: 1.231,
	},
	ruiner: {
		height: -0.825,
		heightRear: -0.825,
		camber: 0,
		trackWidth: 0.722,
		trackWidthRear: 0.722,
	},
	ruiner2: {
		height: -0.825,
		heightRear: -0.825,
		camber: 0,
		trackWidth: 0.722,
		trackWidthRear: 0.722,
	},
	ruiner3: {
		height: -0.649,
		heightRear: -0.649,
		camber: 0,
		trackWidth: 0.722,
		trackWidthRear: 0.722,
	},
	ruiner4: {
		height: -0.498,
		heightRear: -0.498,
		camber: 0,
		trackWidth: 0.76,
		trackWidthRear: 0.76,
	},
	rumpo: {
		height: -1.247,
		heightRear: -1.247,
		camber: 0,
		trackWidth: 0.921,
		trackWidthRear: 0.932,
	},
	rumpo2: {
		height: -1.247,
		heightRear: -1.247,
		camber: 0,
		trackWidth: 0.921,
		trackWidthRear: 0.932,
	},
	rumpo3: {
		height: -1.495,
		heightRear: -1.495,
		camber: 0,
		trackWidth: 0.945,
		trackWidthRear: 0.945,
	},
	ruston: {
		height: -0.423,
		heightRear: -0.393,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.783,
	},
	s80: {
		height: -0.367,
		heightRear: -0.362,
		camber: 0,
		trackWidth: 0.857,
		trackWidthRear: 0.803,
	},
	sabregt: {
		height: -0.624,
		heightRear: -0.626,
		camber: 0,
		trackWidth: 0.768,
		trackWidthRear: 0.768,
	},
	sabregt2: {
		height: -0.589,
		heightRear: -0.572,
		camber: 0,
		trackWidth: 0.768,
		trackWidthRear: 0.768,
	},
	sadler: {
		height: -0.949,
		heightRear: -0.949,
		camber: 0,
		trackWidth: 0.967,
		trackWidthRear: 0.967,
	},
	sadler2: {
		height: -0.949,
		heightRear: -0.949,
		camber: 0,
		trackWidth: 0.95,
		trackWidthRear: 0.95,
	},
	sandking: {
		height: -1.226,
		heightRear: -1.226,
		camber: 0,
		trackWidth: 1.098,
		trackWidthRear: 1.098,
	},
	sandking2: {
		height: -0.967,
		heightRear: -0.967,
		camber: 0,
		trackWidth: 1.098,
		trackWidthRear: 1.098,
	},
	savestra: {
		height: -0.46,
		heightRear: -0.46,
		camber: 0,
		trackWidth: 0.697,
		trackWidthRear: 0.71,
	},
	sc1: {
		height: -0.709,
		heightRear: -0.709,
		camber: 0,
		trackWidth: 0.716,
		trackWidthRear: 0.716,
	},
	scarab: {
		height: -0.552,
		heightRear: -0.552,
		camber: 0,
		trackWidth: 1.16,
		trackWidthRear: 1.16,
	},
	scarab2: {
		height: -0.553,
		heightRear: -0.553,
		camber: 0,
		trackWidth: 1.16,
		trackWidthRear: 1.16,
	},
	scarab3: {
		height: -0.552,
		heightRear: -0.552,
		camber: 0,
		trackWidth: 1.16,
		trackWidthRear: 1.16,
	},
	schafter2: {
		height: -0.734,
		heightRear: -0.736,
		camber: 0,
		trackWidth: 0.802,
		trackWidthRear: 0.764,
	},
	schafter3: {
		height: -0.697,
		heightRear: -0.697,
		camber: 0,
		trackWidth: 0.819,
		trackWidthRear: 0.793,
	},
	schafter4: {
		height: -0.727,
		heightRear: -0.727,
		camber: 0,
		trackWidth: 0.819,
		trackWidthRear: 0.793,
	},
	schafter5: {
		height: -0.697,
		heightRear: -0.697,
		camber: 0,
		trackWidth: 0.819,
		trackWidthRear: 0.793,
	},
	schafter6: {
		height: -0.727,
		heightRear: -0.727,
		camber: 0,
		trackWidth: 0.819,
		trackWidthRear: 0.793,
	},
	schlagen: {
		height: -0.397,
		heightRear: -0.395,
		camber: 0,
		trackWidth: 0.829,
		trackWidthRear: 0.829,
	},
	schwarzer: {
		height: -0.382,
		heightRear: -0.382,
		camber: 0,
		trackWidth: 0.831,
		trackWidthRear: 0.821,
	},
	scramjet: {
		height: -0.407,
		heightRear: -0.407,
		camber: -0.028,
		trackWidth: 0.792,
		trackWidthRear: 0.752,
	},
	seminole: {
		height: -0.721,
		heightRear: -0.721,
		camber: 0,
		trackWidth: 0.796,
		trackWidthRear: 0.796,
	},
	seminole2: {
		height: -0.551,
		heightRear: -0.551,
		camber: 0,
		trackWidth: 0.757,
		trackWidthRear: 0.757,
	},
	sentinel: {
		height: -0.485,
		heightRear: -0.48,
		camber: 0,
		trackWidth: 0.791,
		trackWidthRear: 0.791,
	},
	sentinel2: {
		height: -0.485,
		heightRear: -0.48,
		camber: 0,
		trackWidth: 0.791,
		trackWidthRear: 0.791,
	},
	sentinel3: {
		height: -0.558,
		heightRear: -0.558,
		camber: 0,
		trackWidth: 0.702,
		trackWidthRear: 0.702,
	},
	sentinel4: {
		height: -0.534,
		heightRear: -0.534,
		camber: 0,
		trackWidth: 0.731,
		trackWidthRear: 0.731,
	},
	serrano: {
		height: -0.688,
		heightRear: -0.688,
		camber: 0,
		trackWidth: 0.813,
		trackWidthRear: 0.813,
	},
	seven70: {
		height: -0.345,
		heightRear: -0.345,
		camber: -0.001,
		trackWidth: 0.903,
		trackWidthRear: 0.821,
	},
	sheava: {
		height: -0.529,
		heightRear: -0.527,
		camber: 0,
		trackWidth: 0.824,
		trackWidthRear: 0.863,
	},
	sheriff: {
		height: -0.757,
		heightRear: -0.757,
		camber: 0,
		trackWidth: 0.823,
		trackWidthRear: 0.823,
	},
	sheriff2: {
		height: -0.762,
		heightRear: -0.762,
		camber: 0,
		trackWidth: 0.849,
		trackWidthRear: 0.849,
	},
	slamtruck: {
		height: -0.48,
		heightRear: -0.48,
		camber: 0,
		trackWidth: 0.89,
		trackWidthRear: 0.997,
	},
	slamvan: {
		height: -0.847,
		heightRear: -0.848,
		camber: 0,
		trackWidth: 0.893,
		trackWidthRear: 0.867,
	},
	slamvan2: {
		height: -0.847,
		heightRear: -0.848,
		camber: 0,
		trackWidth: 0.893,
		trackWidthRear: 0.867,
	},
	slamvan3: {
		height: -0.647,
		heightRear: -0.648,
		camber: 0,
		trackWidth: 0.923,
		trackWidthRear: 0.801,
	},
	slamvan4: {
		height: -0.692,
		heightRear: -0.724,
		camber: 0,
		trackWidth: 0.878,
		trackWidthRear: 1.174,
	},
	slamvan5: {
		height: -0.687,
		heightRear: -0.722,
		camber: 0,
		trackWidth: 0.878,
		trackWidthRear: 1.028,
	},
	slamvan6: {
		height: -0.692,
		heightRear: -0.724,
		camber: 0,
		trackWidth: 0.878,
		trackWidthRear: 1.174,
	},
	sm722: {
		height: -0.439,
		heightRear: -0.439,
		camber: 0,
		trackWidth: 0.818,
		trackWidthRear: 0.778,
	},
	specter: {
		height: -0.356,
		heightRear: -0.355,
		camber: -0.001,
		trackWidth: 0.822,
		trackWidthRear: 0.821,
	},
	specter2: {
		height: -0.356,
		heightRear: -0.355,
		camber: -0.001,
		trackWidth: 0.89,
		trackWidthRear: 0.897,
	},
	speedo: {
		height: -0.89,
		heightRear: -0.89,
		camber: 0,
		trackWidth: 0.865,
		trackWidthRear: 0.865,
	},
	speedo2: {
		height: -0.89,
		heightRear: -0.89,
		camber: 0,
		trackWidth: 0.865,
		trackWidthRear: 0.865,
	},
	speedo3: {
		height: -0.89,
		heightRear: -0.89,
		camber: 0,
		trackWidth: 0.865,
		trackWidthRear: 0.865,
	},
	speedo4: {
		height: -0.89,
		heightRear: -0.89,
		camber: 0,
		trackWidth: 0.865,
		trackWidthRear: 0.865,
	},
	speedo5: {
		height: -0.89,
		heightRear: -0.89,
		camber: 0,
		trackWidth: 0.865,
		trackWidthRear: 0.865,
	},
	squaddie: {
		height: -0.883,
		heightRear: -0.883,
		camber: 0,
		trackWidth: 0.898,
		trackWidthRear: 0.898,
	},
	squaddie2: {
		height: -0.769,
		heightRear: -0.77,
		camber: 0,
		trackWidth: 0.923,
		trackWidthRear: 0.933,
	},
	squaddie3: {
		height: -0.769,
		heightRear: -0.77,
		camber: 0,
		trackWidth: 0.923,
		trackWidthRear: 0.933,
	},
	stafford: {
		height: -0.624,
		heightRear: -0.624,
		camber: 0,
		trackWidth: 0.805,
		trackWidthRear: 0.748,
	},
	stalion: {
		height: -0.62,
		heightRear: -0.62,
		camber: 0,
		trackWidth: 0.857,
		trackWidthRear: 0.821,
	},
	stalion2: {
		height: -0.62,
		heightRear: -0.62,
		camber: 0,
		trackWidth: 0.857,
		trackWidthRear: 0.821,
	},
	stanier: {
		height: -0.754,
		heightRear: -0.754,
		camber: 0,
		trackWidth: 0.831,
		trackWidthRear: 0.849,
	},
	stinger: {
		height: -0.535,
		heightRear: -0.531,
		camber: 0,
		trackWidth: 0.761,
		trackWidthRear: 0.761,
	},
	stingergt: {
		height: -0.585,
		heightRear: -0.601,
		camber: 0,
		trackWidth: 0.761,
		trackWidthRear: 0.761,
	},
	stingertt: {
		height: -0.588,
		heightRear: -0.586,
		camber: 0,
		trackWidth: 0.884,
		trackWidthRear: 0.884,
	},
	stockade: {
		height: -0.779,
		heightRear: -0.779,
		camber: 0,
		trackWidth: 1.024,
		trackWidthRear: 1.011,
	},
	stockade3: {
		height: -0.779,
		heightRear: -0.779,
		camber: 0,
		trackWidth: 1.024,
		trackWidthRear: 1.011,
	},
	stratum: {
		height: -0.547,
		heightRear: -0.547,
		camber: 0,
		trackWidth: 0.769,
		trackWidthRear: 0.769,
	},
	streiter: {
		height: -0.729,
		heightRear: -0.729,
		camber: 0,
		trackWidth: 0.802,
		trackWidthRear: 0.802,
	},
	stretch: {
		height: -0.836,
		heightRear: -0.836,
		camber: 0,
		trackWidth: 0.799,
		trackWidthRear: 0.798,
	},
	stromberg: {
		height: -0.351,
		heightRear: -0.351,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.743,
	},
	sugoi: {
		height: -0.416,
		heightRear: -0.416,
		camber: 0,
		trackWidth: 0.815,
		trackWidthRear: 0.815,
	},
	sultan: {
		height: -0.54,
		heightRear: -0.54,
		camber: 0,
		trackWidth: 0.702,
		trackWidthRear: 0.702,
	},
	sultan2: {
		height: -0.486,
		heightRear: -0.486,
		camber: 0,
		trackWidth: 0.719,
		trackWidthRear: 0.734,
	},
	sultan3: {
		height: -0.486,
		heightRear: -0.486,
		camber: 0,
		trackWidth: 0.776,
		trackWidthRear: 0.791,
	},
	sultanrs: {
		height: -0.479,
		heightRear: -0.479,
		camber: 0,
		trackWidth: 0.764,
		trackWidthRear: 0.773,
	},
	superd: {
		height: -0.664,
		heightRear: -0.664,
		camber: 0,
		trackWidth: 0.832,
		trackWidthRear: 0.832,
	},
	surano: {
		height: -0.723,
		heightRear: -0.725,
		camber: 0,
		trackWidth: 0.881,
		trackWidthRear: 0.849,
	},
	surfer: {
		height: -0.779,
		heightRear: -0.779,
		camber: 0,
		trackWidth: 0.638,
		trackWidthRear: 0.72,
	},
	surfer2: {
		height: -0.888,
		heightRear: -0.888,
		camber: 0,
		trackWidth: 0.647,
		trackWidthRear: 0.729,
	},
	surfer3: {
		height: -0.779,
		heightRear: -0.779,
		camber: 0,
		trackWidth: 0.638,
		trackWidthRear: 0.72,
	},
	surge: {
		height: -0.8,
		heightRear: -0.8,
		camber: 0,
		trackWidth: 0.712,
		trackWidthRear: 0.712,
	},
	swinger: {
		height: -0.475,
		heightRear: -0.475,
		camber: -0.014,
		trackWidth: 0.744,
		trackWidthRear: 0.724,
	},
	t20: {
		height: -0.691,
		heightRear: -0.691,
		camber: 0,
		trackWidth: 0.808,
		trackWidthRear: 0.792,
	},
	taco: {
		height: -1.115,
		heightRear: -1.115,
		camber: 0,
		trackWidth: 0.881,
		trackWidthRear: 0.881,
	},
	tahoma: {
		height: -0.432,
		heightRear: -0.432,
		camber: 0,
		trackWidth: 0.736,
		trackWidthRear: 0.736,
	},
	tailgater: {
		height: -0.785,
		heightRear: -0.785,
		camber: 0,
		trackWidth: 0.819,
		trackWidthRear: 0.819,
	},
	tailgater2: {
		height: -0.505,
		heightRear: -0.505,
		camber: 0,
		trackWidth: 0.775,
		trackWidthRear: 0.775,
	},
	taipan: {
		height: -0.46,
		heightRear: -0.47,
		camber: 0,
		trackWidth: 0.84,
		trackWidthRear: 0.83,
	},
	tampa: {
		height: -0.624,
		heightRear: -0.626,
		camber: 0,
		trackWidth: 0.768,
		trackWidthRear: 0.768,
	},
	tampa2: {
		height: -0.532,
		heightRear: -0.532,
		camber: 0,
		trackWidth: 0.898,
		trackWidthRear: 0.87,
	},
	tampa3: {
		height: -0.624,
		heightRear: -0.626,
		camber: 0,
		trackWidth: 0.768,
		trackWidthRear: 0.768,
	},
	taxi: {
		height: -0.757,
		heightRear: -0.757,
		camber: 0,
		trackWidth: 0.823,
		trackWidthRear: 0.823,
	},
	technical: {
		height: -0.806,
		heightRear: -0.806,
		camber: 0,
		trackWidth: 0.861,
		trackWidthRear: 0.861,
	},
	technical2: {
		height: -0.806,
		heightRear: -0.806,
		camber: 0,
		trackWidth: 0.861,
		trackWidthRear: 0.861,
	},
	technical3: {
		height: -0.806,
		heightRear: -0.806,
		camber: 0,
		trackWidth: 0.861,
		trackWidthRear: 0.861,
	},
	tempesta: {
		height: -0.433,
		heightRear: -0.43,
		camber: 0,
		trackWidth: 0.83,
		trackWidthRear: 0.83,
	},
	tenf: {
		height: -0.452,
		heightRear: -0.451,
		camber: 0,
		trackWidth: 0.807,
		trackWidthRear: 0.783,
	},
	tenf2: {
		height: -0.452,
		heightRear: -0.454,
		camber: 0,
		trackWidth: 0.865,
		trackWidthRear: 0.861,
	},
	terbyte: {
		height: -1.299,
		heightRear: -1.299,
		camber: 0,
		trackWidth: 1.008,
		trackWidthRear: 1.008,
	},
	terminus: {
		height: -0.847,
		heightRear: -0.847,
		camber: 0,
		trackWidth: 0.788,
		trackWidthRear: 0.788,
	},
	tezeract: {
		height: -0.281,
		heightRear: -0.268,
		camber: 0,
		trackWidth: 0.913,
		trackWidthRear: 0.898,
	},
	thrax: {
		height: -0.412,
		heightRear: -0.416,
		camber: 0,
		trackWidth: 0.791,
		trackWidthRear: 0.762,
	},
	thruster: {
		height: -1.026,
		heightRear: -1.026,
		camber: 0,
		trackWidth: 0.401,
		trackWidthRear: 0.401,
	},
	tigon: {
		height: -0.476,
		heightRear: -0.476,
		camber: 0,
		trackWidth: 0.83,
		trackWidthRear: 0.83,
	},
	tiptruck: {
		height: -0.857,
		heightRear: -0.857,
		camber: 0,
		trackWidth: 1.031,
		trackWidthRear: 1.079,
	},
	tiptruck2: {
		height: -1.121,
		heightRear: -1.121,
		camber: 0,
		trackWidth: 1.157,
		trackWidthRear: 1.157,
	},
	toreador: {
		height: -0.399,
		heightRear: -0.399,
		camber: 0,
		trackWidth: 0.695,
		trackWidthRear: 0.695,
	},
	torero: {
		height: -0.594,
		heightRear: -0.594,
		camber: 0,
		trackWidth: 0.822,
		trackWidthRear: 0.815,
	},
	torero2: {
		height: -0.466,
		heightRear: -0.467,
		camber: 0,
		trackWidth: 0.847,
		trackWidthRear: 0.844,
	},
	tornado: {
		height: -0.896,
		heightRear: -0.896,
		camber: 0,
		trackWidth: 0.751,
		trackWidthRear: 0.751,
	},
	tornado2: {
		height: -0.896,
		heightRear: -0.896,
		camber: 0,
		trackWidth: 0.751,
		trackWidthRear: 0.751,
	},
	tornado3: {
		height: -0.896,
		heightRear: -0.896,
		camber: 0,
		trackWidth: 0.751,
		trackWidthRear: 0.751,
	},
	tornado4: {
		height: -0.896,
		heightRear: -0.896,
		camber: 0,
		trackWidth: 0.751,
		trackWidthRear: 0.751,
	},
	tornado5: {
		height: -0.846,
		heightRear: -0.846,
		camber: 0,
		trackWidth: 0.751,
		trackWidthRear: 0.751,
	},
	tornado6: {
		height: -0.842,
		heightRear: -0.838,
		camber: 0,
		trackWidth: 0.787,
		trackWidthRear: 0.893,
	},
	toros: {
		height: -0.678,
		heightRear: -0.678,
		camber: 0,
		trackWidth: 0.824,
		trackWidthRear: 0.824,
	},
	tourbus: {
		height: -0.96,
		heightRear: -0.96,
		camber: 0,
		trackWidth: 0.949,
		trackWidthRear: 1.092,
	},
	towtruck: {
		height: -1.205,
		heightRear: -1.2,
		camber: 0,
		trackWidth: 1.031,
		trackWidthRear: 1.232,
	},
	towtruck2: {
		height: -0.847,
		heightRear: -0.847,
		camber: 0,
		trackWidth: 0.893,
		trackWidthRear: 0.893,
	},
	tractor: {
		height: -0.577,
		heightRear: -0.579,
		camber: 0,
		trackWidth: 0.643,
		trackWidthRear: 0.599,
	},
	tractor2: {
		height: -1.337,
		heightRear: -1.344,
		camber: 0,
		trackWidth: 0.972,
		trackWidthRear: 0.972,
	},
	tractor3: {
		height: -1.337,
		heightRear: -1.344,
		camber: 0,
		trackWidth: 0.972,
		trackWidthRear: 0.972,
	},
	trailersmall2: {
		height: -0.873,
		heightRear: -0.873,
		camber: 0,
		trackWidth: 0.997,
		trackWidthRear: 0.997,
	},
	trash: {
		height: -1.009,
		heightRear: -1.011,
		camber: 0,
		trackWidth: 1.147,
		trackWidthRear: 1.147,
	},
	trash2: {
		height: -1.009,
		heightRear: -1.011,
		camber: 0,
		trackWidth: 1.147,
		trackWidthRear: 1.147,
	},
	trophytruck: {
		height: -1.055,
		heightRear: -1.066,
		camber: 0,
		trackWidth: 0.978,
		trackWidthRear: 0.978,
	},
	trophytruck2: {
		height: -1.055,
		heightRear: -1.066,
		camber: 0,
		trackWidth: 0.986,
		trackWidthRear: 0.986,
	},
	tropos: {
		height: -0.545,
		heightRear: -0.545,
		camber: 0,
		trackWidth: 0.729,
		trackWidthRear: 0.765,
	},
	tulip: {
		height: -0.496,
		heightRear: -0.497,
		camber: 0,
		trackWidth: 0.827,
		trackWidthRear: 0.808,
	},
	tulip2: {
		height: -0.56,
		heightRear: -0.574,
		camber: 0,
		trackWidth: 0.745,
		trackWidthRear: 0.705,
	},
	turismo2: {
		height: -0.721,
		heightRear: -0.718,
		camber: 0,
		trackWidth: 0.825,
		trackWidthRear: 0.805,
	},
	turismo3: {
		height: -0.346,
		heightRear: -0.346,
		camber: 0,
		trackWidth: 0.813,
		trackWidthRear: 0.794,
	},
	turismor: {
		height: -0.263,
		heightRear: -0.274,
		camber: 0,
		trackWidth: 0.805,
		trackWidthRear: 0.818,
	},
	tyrant: {
		height: -0.328,
		heightRear: -0.328,
		camber: 0,
		trackWidth: 0.953,
		trackWidthRear: 0.976,
	},
	tyrus: {
		height: -0.287,
		heightRear: -0.287,
		camber: 0,
		trackWidth: 0.781,
		trackWidthRear: 0.781,
	},
	utillitruck: {
		height: -1.213,
		heightRear: -1.213,
		camber: 0,
		trackWidth: 1.089,
		trackWidthRear: 1.138,
	},
	utillitruck2: {
		height: -1.213,
		heightRear: -1.213,
		camber: 0,
		trackWidth: 1.089,
		trackWidthRear: 1.138,
	},
	utillitruck3: {
		height: -0.876,
		heightRear: -0.875,
		camber: 0,
		trackWidth: 0.834,
		trackWidthRear: 0.834,
	},
	vacca: {
		height: -0.627,
		heightRear: -0.627,
		camber: 0,
		trackWidth: 0.793,
		trackWidthRear: 0.82,
	},
	vagner: {
		height: -0.612,
		heightRear: -0.611,
		camber: 0,
		trackWidth: 0.848,
		trackWidthRear: 0.799,
	},
	vagrant: {
		height: -0.814,
		heightRear: -0.814,
		camber: 0,
		trackWidth: 0.788,
		trackWidthRear: 0.788,
	},
	vamos: {
		height: -0.646,
		heightRear: -0.654,
		camber: 0,
		trackWidth: 0.767,
		trackWidthRear: 0.725,
	},
	vectre: {
		height: -0.433,
		heightRear: -0.433,
		camber: 0,
		trackWidth: 0.777,
		trackWidthRear: 0.777,
	},
	verlierer2: {
		height: -0.62,
		heightRear: -0.62,
		camber: 0,
		trackWidth: 0.747,
		trackWidthRear: 0.776,
	},
	verus: {
		height: -0.686,
		heightRear: -0.686,
		camber: 0,
		trackWidth: 0.47,
		trackWidthRear: 0.44,
	},
	vetir: {
		height: -1.472,
		heightRear: -1.472,
		camber: 0,
		trackWidth: 0.842,
		trackWidthRear: 0.842,
	},
	veto: {
		height: -0.227,
		heightRear: -0.227,
		camber: 0,
		trackWidth: 0.373,
		trackWidthRear: 0.337,
	},
	veto2: {
		height: -0.228,
		heightRear: -0.228,
		camber: 0,
		trackWidth: 0.349,
		trackWidthRear: 0.314,
	},
	vigero: {
		height: -0.857,
		heightRear: -0.857,
		camber: 0,
		trackWidth: 0.763,
		trackWidthRear: 0.775,
	},
	vigero2: {
		height: -0.51,
		heightRear: -0.51,
		camber: 0,
		trackWidth: 0.792,
		trackWidthRear: 0.792,
	},
	vigero3: {
		height: -0.51,
		heightRear: -0.51,
		camber: 0,
		trackWidth: 0.792,
		trackWidthRear: 0.792,
	},
	vigilante: {
		height: -0.577,
		heightRear: -0.573,
		camber: 0,
		trackWidth: 0.906,
		trackWidthRear: 1.108,
	},
	virgo: {
		height: -0.658,
		heightRear: -0.68,
		camber: 0,
		trackWidth: 0.821,
		trackWidthRear: 0.821,
	},
	virgo2: {
		height: -0.539,
		heightRear: -0.539,
		camber: 0,
		trackWidth: 0.768,
		trackWidthRear: 0.75,
	},
	virgo3: {
		height: -0.523,
		heightRear: -0.523,
		camber: 0,
		trackWidth: 0.768,
		trackWidthRear: 0.768,
	},
	virtue: {
		height: -0.425,
		heightRear: -0.425,
		camber: 0,
		trackWidth: 0.875,
		trackWidthRear: 0.875,
	},
	viseris: {
		height: -0.468,
		heightRear: -0.467,
		camber: 0,
		trackWidth: 0.771,
		trackWidthRear: 0.77,
	},
	visione: {
		height: -0.655,
		heightRear: -0.647,
		camber: 0,
		trackWidth: 0.793,
		trackWidthRear: 0.826,
	},
	vivanite: {
		height: -0.737,
		heightRear: -0.737,
		camber: 0,
		trackWidth: 0.844,
		trackWidthRear: 0.844,
	},
	vorschlaghammer: {
		height: -0.484,
		heightRear: -0.484,
		camber: -0,
		trackWidth: 0.748,
		trackWidthRear: 0.772,
	},
	voltic: {
		height: -0.684,
		heightRear: -0.684,
		camber: 0,
		trackWidth: 0.76,
		trackWidthRear: 0.745,
	},
	voltic2: {
		height: -0.684,
		heightRear: -0.684,
		camber: 0,
		trackWidth: 0.733,
		trackWidthRear: 0.745,
	},
	voodoo: {
		height: -0.709,
		heightRear: -0.71,
		camber: 0,
		trackWidth: 0.835,
		trackWidthRear: 0.811,
	},
	voodoo2: {
		height: -0.664,
		heightRear: -0.665,
		camber: 0,
		trackWidth: 0.81,
		trackWidthRear: 0.786,
	},
	vstr: {
		height: -0.524,
		heightRear: -0.524,
		camber: 0,
		trackWidth: 0.756,
		trackWidthRear: 0.756,
	},
	warrener: {
		height: -0.3,
		heightRear: -0.3,
		camber: 0,
		trackWidth: 0.746,
		trackWidthRear: 0.746,
	},
	warrener2: {
		height: -0.331,
		heightRear: -0.331,
		camber: 0,
		trackWidth: 0.752,
		trackWidthRear: 0.752,
	},
	washington: {
		height: -0.673,
		heightRear: -0.673,
		camber: 0,
		trackWidth: 0.802,
		trackWidthRear: 0.801,
	},
	wastelander: {
		height: -1.198,
		heightRear: -1.198,
		camber: 0,
		trackWidth: 1.326,
		trackWidthRear: 1.326,
	},
	weevil: {
		height: -0.576,
		heightRear: -0.576,
		camber: 0,
		trackWidth: 0.658,
		trackWidthRear: 0.634,
	},
	weevil2: {
		height: -0.481,
		heightRear: -0.479,
		camber: 0,
		trackWidth: 0.713,
		trackWidthRear: 0.872,
	},
	windsor: {
		height: -0.576,
		heightRear: -0.576,
		camber: 0,
		trackWidth: 0.84,
		trackWidthRear: 0.86,
	},
	windsor2: {
		height: -0.576,
		heightRear: -0.576,
		camber: 0,
		trackWidth: 0.84,
		trackWidthRear: 0.86,
	},
	winky: {
		height: -0.526,
		heightRear: -0.526,
		camber: 0,
		trackWidth: 0.686,
		trackWidthRear: 0.69,
	},
	xa21: {
		height: -0.607,
		heightRear: -0.613,
		camber: 0,
		trackWidth: 0.77,
		trackWidthRear: 0.785,
	},
	xls: {
		height: -1.108,
		heightRear: -1.108,
		camber: 0,
		trackWidth: 0.818,
		trackWidthRear: 0.818,
	},
	xls2: {
		height: -1.068,
		heightRear: -1.068,
		camber: 0,
		trackWidth: 0.818,
		trackWidthRear: 0.818,
	},
	yosemite: {
		height: -0.488,
		heightRear: -0.488,
		camber: 0,
		trackWidth: 0.76,
		trackWidthRear: 0.757,
	},
	yosemite2: {
		height: -0.608,
		heightRear: -0.631,
		camber: 0,
		trackWidth: 0.913,
		trackWidthRear: 0.913,
	},
	yosemite3: {
		height: -0.626,
		heightRear: -0.626,
		camber: 0,
		trackWidth: 0.861,
		trackWidthRear: 0.861,
	},
	yosemite1500: {
		height: -0.782,
		heightRear: -0.782,
		camber: 0,
		trackWidth: 0.804,
		trackWidthRear: 0.804,
	},
	youga: {
		height: -0.656,
		heightRear: -0.656,
		camber: 0,
		trackWidth: 0.885,
		trackWidthRear: 0.898,
	},
	youga2: {
		height: -1.071,
		heightRear: -1.071,
		camber: 0,
		trackWidth: 0.885,
		trackWidthRear: 0.905,
	},
	youga3: {
		height: -1.398,
		heightRear: -1.398,
		camber: 0,
		trackWidth: 0.97,
		trackWidthRear: 0.97,
	},
	youga4: {
		height: -1.071,
		heightRear: -1.071,
		camber: 0,
		trackWidth: 0.885,
		trackWidthRear: 0.905,
	},
	z190: {
		height: -0.426,
		heightRear: -0.426,
		camber: 0,
		trackWidth: 0.7,
		trackWidthRear: 0.7,
	},
	zeno: {
		height: -0.436,
		heightRear: -0.438,
		camber: 0,
		trackWidth: 0.851,
		trackWidthRear: 0.894,
	},
	zentorno: {
		height: -0.666,
		heightRear: -0.673,
		camber: 0,
		trackWidth: 0.809,
		trackWidthRear: 0.795,
	},
	zhaba: {
		height: -1.56,
		heightRear: -1.56,
		camber: 0,
		trackWidth: 1.221,
		trackWidthRear: 1.221,
	},
	zion: {
		height: -0.493,
		heightRear: -0.493,
		camber: 0,
		trackWidth: 0.848,
		trackWidthRear: 0.829,
	},
	zion2: {
		height: -0.493,
		heightRear: -0.493,
		camber: 0,
		trackWidth: 0.848,
		trackWidthRear: 0.829,
	},
	zion3: {
		height: -0.454,
		heightRear: -0.454,
		camber: 0,
		trackWidth: 0.759,
		trackWidthRear: 0.744,
	},
	zorrusso: {
		height: -0.575,
		heightRear: -0.585,
		camber: 0,
		trackWidth: 0.847,
		trackWidthRear: 0.881,
	},
	zr350: {
		height: -0.718,
		heightRear: -0.718,
		camber: 0,
		trackWidth: 0.752,
		trackWidthRear: 0.737,
	},
	zr380: {
		height: -0.375,
		heightRear: -0.375,
		camber: -0.005,
		trackWidth: 0.769,
		trackWidthRear: 0.808,
	},
	zr3802: {
		height: -0.375,
		heightRear: -0.375,
		camber: -0.005,
		trackWidth: 0.769,
		trackWidthRear: 0.808,
	},
	zr3803: {
		height: -0.375,
		heightRear: -0.375,
		camber: -0.005,
		trackWidth: 0.769,
		trackWidthRear: 0.808,
	},
	ztype: {
		height: -0.541,
		heightRear: -0.541,
		camber: 0,
		trackWidth: 0.787,
		trackWidthRear: 0.792,
	},
	bison4: {
		height: -0.738,
		heightRear: -0.694,
		camber: 0,
		trackWidth: 0.872,
		trackWidthRear: 0.872,
	},
	clubgtr: {
		height: -0.796,
		heightRear: -0.796,
		camber: 0,
		trackWidth: 0.718,
		trackWidthRear: 0.728,
	},
	ferocid: {
		height: -0.558,
		heightRear: -0.558,
		camber: 0,
		trackWidth: 0.747,
		trackWidthRear: 0.725,
	},
	flatbed2: {
		height: -1.103,
		heightRear: -1.096,
		camber: 0,
		trackWidth: 1.262,
		trackWidthRear: 1.05,
	},
	gauntlets: {
		height: -0.786,
		heightRear: -0.786,
		camber: 0,
		trackWidth: 0.782,
		trackWidthRear: 0.783,
	},
	hellion2: {
		height: -0.747,
		heightRear: -0.747,
		camber: 0,
		trackWidth: 0.834,
		trackWidthRear: 0.834,
	},
	jackgpr: {
		height: -0.752,
		heightRear: -0.752,
		camber: -0.013,
		trackWidth: 0.82,
		trackWidthRear: 0.872,
	},
	jester5: {
		height: -0.458,
		heightRear: -0.458,
		camber: 0,
		trackWidth: 0.735,
		trackWidthRear: 0.738,
	},
	nspeedo: {
		height: -0.944,
		heightRear: -0.944,
		camber: 0,
		trackWidth: 0.882,
		trackWidthRear: 0.882,
	},
	packer2: {
		height: -1.347,
		heightRear: -1.335,
		camber: 0,
		trackWidth: 1.262,
		trackWidthRear: 1.13,
	},
	pentro: {
		height: -0.573,
		heightRear: -0.573,
		camber: 0,
		trackWidth: 0.72,
		trackWidthRear: 0.756,
	},
	pentro2: {
		height: -0.573,
		heightRear: -0.573,
		camber: 0,
		trackWidth: 0.72,
		trackWidthRear: 0.756,
	},
	pentro3: {
		height: -0.573,
		heightRear: -0.573,
		camber: 0,
		trackWidth: 0.72,
		trackWidthRear: 0.756,
	},
	pentrogpr: {
		height: -0.528,
		heightRear: -0.528,
		camber: -0.037,
		trackWidth: 0.836,
		trackWidthRear: 0.866,
	},
	pentrogpr2: {
		height: -0.528,
		heightRear: -0.528,
		camber: -0.037,
		trackWidth: 0.836,
		trackWidthRear: 0.866,
	},
	pounder3: {
		height: -1.347,
		heightRear: -1.335,
		camber: 0,
		trackWidth: 1.262,
		trackWidthRear: 1.13,
	},
	s230: {
		height: -0.528,
		heightRear: -0.528,
		camber: 0,
		trackWidth: 0.743,
		trackWidthRear: 0.744,
	},
	schwartzerc: {
		height: -0.864,
		heightRear: -0.864,
		camber: 0,
		trackWidth: 0.7,
		trackWidthRear: 0.7,
	},
	scout: {
		height: -1.058,
		heightRear: -1.059,
		camber: 0,
		trackWidth: 0.872,
		trackWidthRear: 0.872,
	},
	seraph3: {
		height: -0.545,
		heightRear: -0.545,
		camber: 0,
		trackWidth: 0.744,
		trackWidthRear: 0.744,
	},
	sigma2: {
		height: -0.329,
		heightRear: -0.329,
		camber: 0,
		trackWidth: 0.718,
		trackWidthRear: 0.718,
	},
	skart: {
		height: -0.22,
		heightRear: -0.22,
		camber: 0,
		trackWidth: 0.499,
		trackWidthRear: 0.55,
	},
	towtruck3: {
		height: -1.205,
		heightRear: -1.2,
		camber: 0,
		trackWidth: 1.031,
		trackWidthRear: 1.232,
	},
	vincent2: {
		height: -0.593,
		heightRear: -0.593,
		camber: 0,
		trackWidth: 0.734,
		trackWidthRear: 0.734,
	},
	yosemiteswb: {
		height: -0.795,
		heightRear: -0.799,
		camber: 0,
		trackWidth: 0.855,
		trackWidthRear: 0.849,
	},
	zionks: {
		height: -0.444,
		heightRear: -0.444,
		camber: 0,
		trackWidth: 0.759,
		trackWidthRear: 0.783,
	},
	zr380c: {
		height: -0.345,
		heightRear: -0.345,
		camber: 0,
		trackWidth: 0.766,
		trackWidthRear: 0.808,
	},
	zr380s: {
		height: -0.375,
		heightRear: -0.375,
		camber: 0,
		trackWidth: 0.741,
		trackWidthRear: 0.768,
	},
	'5series': {
		height: -0.714,		
		heightRear: -0.767,					
		camber: 0,				
		trackWidth: 0.815,				
		trackWidthRear: 0.815,						
	},
	'5series2': {
		height: -0.674,
		heightRear: -0.727,
		camber: 0,
		trackWidth: 0.815,
		trackWidthRear: 0.815,
	},
	'5series3':  {
		height: -0.659,
		heightRear: -0.659,
		camber: 0,
		trackWidth: 0.779,
		trackWidthRear: 0.766,
	},
	'16challenger': {
		height: -0.244,
		heightRear: -0.244,
		camber: 0,
		trackWidth: 0.796,
		trackWidthRear: 0.774,
	},
	'63gls': {
		height: -1.013,
		heightRear: -0.983,
		camber: 0,
		trackWidth: 0.856,
		trackWidthRear: 0.848,
	},
	'63gls2': {
		height: -0.604,
		heightRear: -0.604,
		camber: 0,
		trackWidth: 0.862,
		trackWidthRear: 0.873,
	},
	'190e': {
		height: -0.378,
		heightRear: -0.404,
		camber: 0,
		trackWidth: 0.719,
		trackWidthRear: 0.719,
	},
	'350z': {
		height: -0.590,
		heightRear: -0.590,
		camber: 0,
		trackWidth: 0.777,
		trackWidthRear: 0.762,
	},
	'370z': {
		height: -0.573,
		heightRear: -0.523,
		camber: 0,
		trackWidth: 0.787,
		trackWidthRear: 0.801,
	},
	'400z': {
		height: -0.454,
		heightRear: -0.454,
		camber: 0,
		trackWidth: 0.774,
		trackWidthRear: 0.776,
	},
	'488pista': {
		height: -0.517,
		heightRear: -0.517,
		camber: 0,
		trackWidth: 0.854,
		trackWidthRear: 0.868,
	},
	'718bs': {
		height: -0.665,
		heightRear: -0.661,
		camber: 0,
		trackWidth: 0.787,
		trackWidthRear: 0.741,
	},
	'720s': {
		height: -0.708,
		heightRear: -0.710,
		camber: 0,
		trackWidth: 0.799,
		trackWidthRear: 0.807,
	},
	'918s': {
		height: -0.422,
		heightRear: -0.422,
		camber: 0,
		trackWidth: 0.815,
		trackWidthRear: 0.794,
	},
	'2020mustang': {
		height: -0.327,
		heightRear: -0.354,
		camber: 0,
		trackWidth: 0.789,
		trackWidthRear: 0.794,
	},
	a4: {
		height: -0.521,
		heightRear: -0.520,
		camber: 0,
		trackWidth: 0.730,
		trackWidthRear: 0.730,
	},
	a8: {
		height: -0.608,
		heightRear: -0.608,
		camber: 0,
		trackWidth: 0.804,
		trackWidthRear: 0.807,
	},
	a42: {
		height: -0.597,
		heightRear: -0.597,
		camber: 0,
		trackWidth: 0.679,
		trackWidthRear: 0.679,
	},
	a80: {
		height: -0.476,
		heightRear: -0.434,
		camber: 0,
		trackWidth: 0.764,
		trackWidthRear: 0.756,
	},
	ab2: {
		height: -0.552,
		heightRear: -0.561,
		camber: 0,
		trackWidth: 0.857,
		trackWidthRear: 0.846,
	},
	accord: {
		height: -0.620,
		heightRear: -0.620,
		camber: 0,
		trackWidth: 0.799,
		trackWidthRear: 0.799,
	},
	aclass: {
		height: -0.912,
		heightRear: -0.912,
		camber: 0,
		trackWidth: 0.773,
		trackWidthRear: 0.773,
	},
	aclass2: {
		height: -0.932,
		heightRear: -0.932,
		camber: 0,
		trackWidth: 0.773,
		trackWidthRear: 0.773,
	},
	ae86: {
		height: -0.603,
		heightRear: -0.585,
		camber: 0,
		trackWidth: 0.671,
		trackWidthRear: 0.670,
	},
	aeroboat: {
		height: 0,
		heightRear: 0,
		camber: 0,
		trackWidth: 0,
		trackWidthRear: 0,
	},
	air: {
		height: -0.695,
		heightRear: -0.695,
		camber: 0,
		trackWidth: 0.854,
		trackWidthRear: 0.858,
	},
	amggt: {
		height: -0.560,
		heightRear: -0.566,
		camber: 0,
		trackWidth: 0.825,
		trackWidthRear: 0.845,
	},
	amggt2: {
		height: -0.608,
		heightRear: -0.608,
		camber: 0,
		trackWidth: 0.810,
		trackWidthRear: 0.810,
	},
	amgone: {
		height: -0.592,
		heightRear: -0.622,
		camber: 0,
		trackWidth: 0.849,
		trackWidthRear: 0.849,
	},
	ast: {
		height: -0.697,
		heightRear: -0.697,
		camber: 0,
		trackWidth: 0.774,
		trackWidthRear: 0.810,
	},
	astra: {
		height: -0.580,
		heightRear: -0.568,
		camber: 0,
		trackWidth: 0.789,
		trackWidthRear: 0.789,
	},
	asvj: {
		height: -0.618,
		heightRear: -0.619,
		camber: 0,
		trackWidth: 0.867,
		trackWidthRear: 0.823,
	},
	bacalar: {
		height: -0.505,
		heightRear: -0.506,
		camber: 0,
		trackWidth: 0.840,
		trackWidthRear: 0.828,
	},
	barracuda: {
		height: -0.604,
		heightRear: -0.604,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.783,
	},
	batur: {
		height: -0.652,
		heightRear: -0.652,
		camber: 0,
		trackWidth: 0.794,
		trackWidthRear: 0.794,
	},
	bdivo: {
		height: -0.659,
		heightRear: -0.673,
		camber: 0,
		trackWidth: 0.863,
		trackWidthRear: 0.834,
	},
	bentaygast: {
		height: -0.841,
		heightRear: -0.855,
		camber: 0,
		trackWidth: 0.826,
		trackWidthRear: 0.826,
	},
	bentayga2: {
		height: -0.841,
		heightRear: -0.855,
		camber: 0,
		trackWidth: 0.826,
		trackWidthRear: 0.826,
	},
	bmwe38: {
		height: -0.690,
		heightRear: -0.690,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.783,
	},
	bmwe39: {
		height: -0.883,
		heightRear: -0.903,
		camber: 0,
		trackWidth: 0.767,
		trackWidthRear: 0.761,
	},
	bmwg20: {
		height: -0.571,
		heightRear: -0.576,
		camber: 0,
		trackWidth: 0.733,
		trackWidthRear: 0.727,
	},
	bmwm7: {
		height: -0.400,
		heightRear: -0.405,
		camber: 0,
		trackWidth: 0.810,
		trackWidthRear: 0.810,
	},
	bmwx7: {
		height: -0.807,
		heightRear: -0.806,
		camber: 0,
		trackWidth: 0.833,
		trackWidthRear: 0.833,
	},
	bolide: {
		height: -0.526,
		heightRear: -0.523,
		camber: 0,
		trackWidth: 0.818,
		trackWidthRear: 0.802,
	},
	bronco: {
		height: -0.849,
		heightRear: -0.849,
		camber: 0,
		trackWidth: 0.828,
		trackWidthRear: 0.828,
	},
	brz: {
		height: -0.485,
		heightRear: -0.485,
		camber: 0,
		trackWidth: 0.768,
		trackWidthRear: 0.778,
	},
	c300: {
		height: -0.673,
		heightRear: -0.673,
		camber: 0,
		trackWidth: 0.826,
		trackWidthRear: 0.835,
	},
	camaro2: {
		height: -0.290,
		heightRear: -0.298,
		camber: 0,
		trackWidth: 0.788,
		trackWidthRear: 0.785,
	},
	camaro21: {
		height: -0.423,
		heightRear: -0.423,
		camber: 0,
		trackWidth: 0.785,
		trackWidthRear: 0.786,
	},
	camry2: {
		height: -0.515,
		heightRear: -0.515,
		camber: 0,
		trackWidth: 0.751,
		trackWidthRear: 0.741,
	},
	camry70: {
		height: -0.786,
		heightRear: -0.786,
		camber: 0,
		trackWidth: 0.764,
		trackWidthRear: 0.819,
	},
	carrera: {
		height: -0.644,
		heightRear: -0.644,
		camber: 0,
		trackWidth: 0.663,
		trackWidthRear: 0.702,
	},
	// cayen19: {
	// 	height: -0.986,
	// 	heightRear: -0.986,
	// 	camber: 0,
	// 	trackWidth: 0.793,
	// 	trackWidthRear: 0.793,
	// },
	cayen19: {
		height: -0.636,
		heightRear: -0.636,
		camber: 0,
		trackWidth: 0.819,
		trackWidthRear: 0.819,
	},
	cayenne2: {
		height: -0.529,
		heightRear: -0.528,
		camber: 0,
		trackWidth: 0.830,
		trackWidthRear: 0.838,
	},
	cc850: {
		height: -0.464,
		heightRear: -0.465,
		camber: 0,
		trackWidth: 0.841,
		trackWidthRear: 0.822,
	},
	cclass2: {
		height: -0.593,
		heightRear: -0.593,
		camber: 0,
		trackWidth: 0.734,
		trackWidthRear: 0.696,
	},
	centenario: {
		height: -0.302,
		heightRear: -0.300,
		camber: 0,
		trackWidth: 0.840,
		trackWidthRear: 0.819,
	},
	cesc21: {
		height: -0.943,
		heightRear: -0.863,
		camber: 0,
		trackWidth: 0.829,
		trackWidthRear: 0.839,
	},
	charger2: {
		height: -0.714,
		heightRear: -0.714,
		camber: 0,
		trackWidth: 0.816,
		trackWidthRear: 0.821,
	},
	charger20: {
		height: -0.844,
		heightRear: -0.845,
		camber: 0,
		trackWidth: 0.772,
		trackWidthRear: 0.772,
	},
	charger3: {
		height: -0.736,
		heightRear: -0.736,
		camber: 0,
		trackWidth: 0.944,
		trackWidthRear: 0.937,
	},
	chiron19: {
		height: -0.209,
		heightRear: -0.209,
		camber: 0,
		trackWidth: 0.870,
		trackWidthRear: 0.870,
	},
	civic: {
		height: -0.577,
		heightRear: -0.577,
		camber: 0,
		trackWidth: 0.741,
		trackWidthRear: 0.739,
	},
	civic2: {
		height: -0.641,
		heightRear: -0.641,
		camber: 0,
		trackWidth: 0.760,
		trackWidthRear: 0.771,
	},
	cle: {
		height: -0.604,
		heightRear: -0.604,
		camber: 0,
		trackWidth: 0.776,
		trackWidthRear: 0.776,
	},
	cls2: {
		height: -0.694,
		heightRear: -0.694,
		camber: 0,
		trackWidth: 0.806,
		trackWidthRear: 0.806,
	},
	cls63s: {
		height: -0.614,
		heightRear: -0.648,
		camber: 0,
		trackWidth: 0.797,
		trackWidthRear: 0.796,
	},
	continental: {
		height: -0.539,
		heightRear: -0.540,
		camber: 0,
		trackWidth: 0.839,
		trackWidthRear: 0.847,
	},
	corvett08: {
		height: -0.402,
		heightRear: -0.401,
		camber: 0,
		trackWidth: 0.797,
		trackWidthRear: 0.783,
	},
	corvette2: {
		height: -0.280,
		heightRear: -0.280,
		camber: 0,
		trackWidth: 0.808,
		trackWidthRear: 0.808,
	},
	corvette3: {
		height: -0.642,
		heightRear: -0.642,
		camber: 0,
		trackWidth: 0.714,
		trackWidthRear: 0.713,
	},
	countach: {
		height: -0.448,
		heightRear: -0.456,
		camber: 0,
		trackWidth: 0.860,
		trackWidthRear: 0.851,
	},
	countryman: {
		height: -0.736,
		heightRear: -0.736,
		camber: 0,
		trackWidth: 0.759,
		trackWidthRear: 0.759,
	},
	ct5: {
		height: -0.620,
		heightRear: -0.620,
		camber: 0,
		trackWidth: 0.770,
		trackWidthRear: 0.781,
	},
	cullinan: {
		height: -0.705,
		heightRear: -0.711,
		camber: 0,
		trackWidth: 0.884,
		trackWidthRear: 0.858,
	},
	cybertruck: {
		height: -0.906,
		heightRear: -0.906,
		camber: 0,
		trackWidth: 0.857,
		trackWidthRear: 0.858,
	},
	d8: {
		height: -0.513,
		heightRear: -0.543,
		camber: 0,
		trackWidth: 0.764,
		trackWidthRear: 0.770,
	},
	dawn: {
		height: -0.866,
		heightRear: -0.866,
		camber: 0,
		trackWidth: 0.817,
		trackWidthRear: 0.836,
	},
	daytona: {
		height: -0.767,
		heightRear: -0.767,
		camber: 0,
		trackWidth: 0.875,
		trackWidthRear: 0.858,
	},
	db11: {
		height: -0.621,
		heightRear: -0.621,
		camber: 0,
		trackWidth: 0.833,
		trackWidthRear: 0.833,
	},
	db112: {
		height: -0.621,
		heightRear: -0.621,
		camber: 0,
		trackWidth: 0.833,
		trackWidthRear: 0.833,
	},
	db5: {
		height: -0.333,
		heightRear: -0.374,
		camber: 0,
		trackWidth: 0.673,
		trackWidthRear: 0.655,
	},
	dbx: {
		height: -0.293,
		heightRear: -0.343,
		camber: 0,
		trackWidth: 0.853,
		trackWidthRear: 0.850,
	},
	defender: {
		height: -0.665,
		heightRear: -0.665,
		camber: 0,
		trackWidth: 0.844,
		trackWidthRear: 0.844,
	},
	defender2 :  {
		height: -0.689,
		heightRear: -0.689,
		camber: 0,
		trackWidth: 0.742,
		trackWidthRear: 0.742,
	},
	delorean: {
		height: -0.569,
		heightRear: -0.601,
		camber: 0,
		trackWidth: 0.790,
		trackWidthRear: 0.772,
	},
	diablo: {
		height: -0.642,
		heightRear: -0.657,
		camber: 0,
		trackWidth: 0.793,
		trackWidthRear: 0.826,
	},
	diavel: {
		height: -0.635,
		heightRear: 0,
		camber: 0,
		trackWidth: 0,
		trackWidthRear: 0,
	},
	dsprinter: {
		height: -0.974,
		heightRear: -1.062,
		camber: 0,
		trackWidth: 0.887,
		trackWidthRear: 0.888,
	},
	durango: {
		height: -0.649,
		heightRear: -0.649,
		camber: 0,
		trackWidth: 0.791,
		trackWidthRear: 0.798,
	},
	e63s: {
		height: -0.692,
		heightRear: -0.692,
		camber: 0,
		trackWidth: 0.793,
		trackWidthRear: 0.778,
	},
	eclass3: {
		height: -1.014,
		heightRear: -1.034,
		camber: 0,
		trackWidth: 0.814,
		trackWidthRear: 0.810,
	},
	eclass4: {
		height: -1.014,
		heightRear: -1.034,
		camber: 0,
		trackWidth: 0.814,
		trackWidthRear: 0.810,
	},
	eclass5: {
		height: -0.672,
		heightRear: -0.672,
		camber: 0,
		trackWidth: 0.768,
		trackWidthRear: 0.763,
	},
	eclipse: {
		height: -0.724,
		heightRear: -0.724,
		camber: 0,
		trackWidth: 0.750,
		trackWidthRear: 0.747,
	},
	emira: {
		height: -0.463,
		heightRear: -0.463,
		camber: 0,
		trackWidth: 0.795,
		trackWidthRear: 0.793,
	},
	eqs: {
		height: -0.636,
		heightRear: -0.636,
		camber: 0,
		trackWidth: 0.794,
		trackWidthRear: 0.817,
	},
	escalade2: {
		height: -0.381,
		heightRear: -0.381,
		camber: 0,
		trackWidth: 0.772,
		trackWidthRear: 0.778,
	},
	essenza: {
		height: -0.606,
		heightRear: -0.593,
		camber: 0,
		trackWidth: 0.885,
		trackWidthRear: 0.859,
	},
	etron: {
		height: -0.404,
		heightRear: -0.405,
		camber: 0,
		trackWidth: 0.842,
		trackWidthRear: 0.840,
	},
	etron2: {
		height: -0.642,
		heightRear: -0.642,
		camber: 0,
		trackWidth: 0.810,
		trackWidthRear: 0.810,
	},
	ev6: {
		height: -0.643,
		heightRear: -0.643,
		camber: 0,
		trackWidth: 0.775,
		trackWidthRear: 0.779,
	},
	evija: {
		height: -0.494,
		heightRear: -0.493,
		camber: 0,
		trackWidth: 0.849,
		trackWidthRear: 0.849,
	},
	evo10: {
		height: -0.873,
		heightRear: -0.873,
		camber: 0,
		trackWidth: 0.750,
		trackWidthRear: 0.750,
	},
	evo9: {
		height: -0.509,
		heightRear: -0.509,
		camber: 0,
		trackWidth: 0.730,
		trackWidthRear: 0.734,
	},
	f150: {
		height: -0.726,
		heightRear: -0.726,
		camber: 0,
		trackWidth: 0.953,
		trackWidthRear: 0.953,
	},
	f1502: {
		height: -0.716,
		heightRear: -0.716,
		camber: 0,
		trackWidth: 0.953,
		trackWidthRear: 0.953,
	},
	f1503: {
		height: -0.728,
		heightRear: -0.727,
		camber: 0,
		trackWidth: 0.806,
		trackWidthRear: 0.831,
	},
	ex90: {
		height: -0.638,
		heightRear: -0.638,
		camber: 0,
		trackWidth: 0.813,
		trackWidthRear: 0.792,
	},
	f7: {
		height: -0.598,
		heightRear: -0.600,
		camber: 0,
		trackWidth: 0.765,
		trackWidthRear: 0.763,
	},	
	f8: {
		height: -0.605,
		heightRear: -0.627,
		camber: 0,
		trackWidth: 0.844,
		trackWidthRear: 0.846,
	},
	f40: {
		height: -0.625,
		heightRear: -0.638,
		camber: 0,
		trackWidth: 0.784,
		trackWidthRear: 0.823,
	},
	ff12: {
		height: -0.666,
		heightRear: -0.661,
		camber: 0,
		trackWidth: 0.846,
		trackWidthRear: 0.841,
	},
	firebird: {
		height: -0.709,
		heightRear: -0.709,
		camber: 0,
		trackWidth: 0.768,
		trackWidthRear: 0.778,
	},
	focusrs: {
		height: -0.728,
		heightRear: -0.728,
		camber: 0,
		trackWidth: 0.740,
		trackWidthRear: 0.740,
	},
	fordgt: {
		height: -0.612,
		heightRear: -0.622,
		camber: 0,
		trackWidth: 0.830,
		trackWidthRear: 0.845,
	},
	ftype: {
		height: -0.815,
		heightRear: -0.820,
		camber: 0,
		trackWidth: 0.796,
		trackWidthRear: 0.783,
	},
	fury: {
		height: -0.705,
		heightRear: -0.705,
		camber: 0,
		trackWidth: 0.785,
		trackWidthRear: 0.786,
	},
	fx50s: {
		height: -0.887,
		heightRear: -0.884,
		camber: 0,
		trackWidth: 0.863,
		trackWidthRear: 0.860,
	},
	fxxk: {
		height: -0.400,
		heightRear: -0.400,
		camber: 0,
		trackWidth: 0.832,
		trackWidthRear: 0.813,
	},
	g55: {
		height: -0.726,
		heightRear: -0.723,
		camber: 0,
		trackWidth: 0.822,
		trackWidthRear: 0.841,
	},
	g63: {
		height: -0.663,
		heightRear: -0.663,
		camber: 0,
		trackWidth: 0.798,
		trackWidthRear: 0.796,
	},
	g63amg6x6: {
		height: -0.756,
		heightRear: -0.742,
		camber: 0,
		trackWidth: 0.927,
		trackWidthRear: 0.941,
	},
	galant: {
		height: -0.610,
		heightRear: -0.610,
		camber: 0,
		trackWidth: 0.727,
		trackWidthRear: 0.727,
	},
	gclass4: {
		height: -0.797,
		heightRear: -0.797,
		camber: 0,
		trackWidth: 0.863,
		trackWidthRear: 0.863,
	},
	gemera: {
		height: -0.494,
		heightRear: -0.478,
		camber: 0,
		trackWidth: 0.824,
		trackWidthRear: 0.823,
	},
	ghost: {
		height: -0.713,
		heightRear: -0.713,
		camber: 0,
		trackWidth: 0.809,
		trackWidthRear: 0.794,
	},
	giulia: {
		height: -0.486,
		heightRear: -0.486,
		camber: 0,
		trackWidth: 0.771,
		trackWidthRear: 0.799,
	},
	glc: {
		height: -0.607,
		heightRear: -0.607,
		camber: 0,
		trackWidth: 0.785,
		trackWidthRear: 0.801,
	},
	gle2: {
		height: -1.078,
		heightRear: -1.078,
		camber: 0,
		trackWidth: 0.830,
		trackWidthRear: 0.843,
	},
	gle63: {
		height: -0.671,
		heightRear: -0.670,
		camber: 0,
		trackWidth: 0.849,
		trackWidthRear: 0.896,
	},
	gnx: {
		height: -0.569,
		heightRear: -0.576,
		camber: 0,
		trackWidth: 0.790,
		trackWidthRear: 0.785,
	},
	golf2: {
		height: -0.543,
		heightRear: -0.543,
		camber: 0,
		trackWidth: 0.667,
		trackWidthRear: 0.666,
	},
	golf7r: {
		height: -0.924,
		heightRear: -0.924,
		camber: 0,
		trackWidth: 0.732,
		trackWidthRear: 0.713,
	},
	gt63s: {
		height: -0.726,
		heightRear: -0.732,
		camber: 0,
		trackWidth: 0.774,
		trackWidthRear: 0.799,
	},
	nisgtr: {
		height: -0.603,
		heightRear: -0.602,
		camber: 0,
		trackWidth: 0.789,
		trackWidthRear: 0.799,
	},
	gtr32 :  {
		height: -0.482,
		heightRear: -0.479,
		camber: 0,
		trackWidth: 0.749,
		trackWidthRear: 0.764,
	},
	gtr50: {
		height: -0.406,
		heightRear: -0.406,
		camber: 0,
		trackWidth: 0.799,
		trackWidthRear: 0.815,
	},
	hummer: {
		height: -0.916,
		heightRear: -0.916,
		camber: 0,
		trackWidth: 0.933,
		trackWidthRear: 0.888,
	},
	hummer2: {
		height: -0.906,
		heightRear: -0.906,
		camber: 0,
		trackWidth: 0.933,
		trackWidthRear: 0.888,
	},
	hummer3: {
		height: -0.851,
		heightRear: -0.851,
		camber: 0,
		trackWidth: 0.933,
		trackWidthRear: 0.888,
	},
	hummer5: {
		height: -1.049,    
		heightRear: -1.049,  
		camber: 0,  
		trackWidth: 0.878,  
		trackWidthRear: 0.878,      
	},
	huracan: {
		height: -0.531,
		heightRear: -0.549,
		camber: 0,
		trackWidth: 0.817,
		trackWidthRear: 0.809,
	},
	huracan2: {
		height: -0.531,
		heightRear: -0.549,
		camber: 0,
		trackWidth: 0.817,
		trackWidthRear: 0.809,
	},
	i8: {
		height: -0.677	,
		heightRear: -0.677,
		camber: 0,
		trackWidth: 0.816,
		trackWidthRear: 0.829,
	},
	i82: {
		height: -0.677,
		heightRear: -0.677,
		camber: 0,
		trackWidth: 0.816,
		trackWidthRear: 0.829,
	},
	idr: {
		height: -0.535,
		heightRear: -0.535,
		camber: 0,
		trackWidth: 0.790,
		trackWidthRear: 0.793,
	},
	imola: {
		height: -0.674,
		heightRear: -0.671,
		camber: 0,
		trackWidth: 0.812,
		trackWidthRear: 0.812,
	},
	impala: {
		height: -0.787,
		heightRear: -0.801,
		camber: 0,
		trackWidth: 0.853,
		trackWidthRear: 0.853,
	},
	impala2: {
		height: -0.712,
		heightRear: -0.688,
		camber: 0,
		trackWidth: 0.769,
		trackWidthRear: 0.769,
	},
	invencible: {
		height: -0.678,
		heightRear: -0.678,
		camber: 0,
		trackWidth: 0.827,
		trackWidthRear: 0.824,
	},
	ix: {
		height: -1.136,
		heightRear: -1.136,
		camber: 0,
		trackWidth: 0.838,
		trackWidthRear: 0.843,
	},
	j50: {
		height: -0.578,
		heightRear: -0.580,
		camber: 0,
		trackWidth: 0.846,
		trackWidthRear: 0.849,
	},
	jesko20: {
		height: -0.530,
		heightRear: -0.530,
		camber: 0,
		trackWidth: 0.839,
		trackWidthRear: 0.830,
	},
	jgc: {
		height: -0.996,
		heightRear: -0.937,
		camber: 0,
		trackWidth: 0.800,
		trackWidthRear: 0.799,
	},
	k5: {
		height: -0.263,
		heightRear: -0.263,
		camber: 0,
		trackWidth: 0.778,
		trackWidthRear: 0.785,
	},
	kiastinger: {
		height: -0.486,
		heightRear: -0.486,
		camber: 0,
		trackWidth: 0.831,
		trackWidthRear: 0.831,
	},
	l650: {
		height: 0,
		heightRear: 0,
		camber: 0,
		trackWidth: 0,
		trackWidthRear: 0,
	},
	laferrari: {
		height: -0.509,
		heightRear: -0.508,
		camber: 0,
		trackWidth: 0.813,
		trackWidthRear: 0.821,
	},
	lanzador: {
		height: -0.466,
		heightRear: -0.466,
		camber: 0,
		trackWidth: 0.876,
		trackWidthRear: 0.866,
	},
	lc200: {
		height: -0.849,
		heightRear: -0.820,
		camber: 0,
		trackWidth: 0.874,
		trackWidthRear: 0.870,
	},
	lc300: {
		height: -0.899,
		heightRear: -0.921,
		camber: 0,
		trackWidth: 0.829,
		trackWidthRear: 0.841,
	},
	lc500: {
		height: -0.563,
		heightRear: -0.563,
		camber: 0,
		trackWidth: 0.796,
		trackWidthRear: 0.803,
	},
	levante: {
		height: -0.841,
		heightRear: -0.841,
		camber: 0,
		trackWidth: 0.766,
		trackWidthRear: 0.820,
	},
	lex570: {
		height: -0.491,
		heightRear: -0.505,
		camber: 0,
		trackWidth: 0.858,
		trackWidthRear: 0.858,
	},
	lfa: {
		height: -0.487,
		heightRear: -0.487,
		camber: 0,
		trackWidth: 0.792,
		trackWidthRear: 0.796,
	},
	m2g42: {
		height: -0.275,
		heightRear: -0.275,
		camber: 0,
		trackWidth: 0.806,
		trackWidthRear: 0.805,
	},
	m3e46: {
		height: -0.477,
		heightRear: -0.471,
		camber: 0,
		trackWidth: 0.743,
		trackWidthRear: 0.747,
	},
	m3g81: {
		height: -0.843,
		heightRear: -0.844,
		camber: 0,
		trackWidth: 0.798,
		trackWidthRear: 0.807,
	},
	m4comp: {
		height: -0.618,
		heightRear: -0.618,
		camber: 0,
		trackWidth: 0.779,
		trackWidthRear: 0.770,
	},
	m4f82: {
		height: -0.531,
		heightRear: -0.531,
		camber: 0,
		trackWidth: 0.765,
		trackWidthRear: 0.789,
	},
	m5comp: {
		height: -0.741,
		heightRear: -0.714,
		camber: 0,
		trackWidth: 0.796,
		trackWidthRear: 0.783,
	},
	m5e60: {
		height: -0.387,
		heightRear: -0.387,
		camber: 0,
		trackWidth: 0.773,
		trackWidthRear: 0.793,
	},
	m7g70: {
		height: -0.692,
		heightRear: -0.692,
		camber: 0,
		trackWidth: 0.822,
		trackWidthRear: 0.822,
	},
	m12b: {
		height: -0.890,
		heightRear: -0.890,
		camber: 0,
		trackWidth: 0.883,
		trackWidthRear: 0.883,
	},
	m850: {
		height: -0.361,
		heightRear: -0.362,
		camber: 0,
		trackWidth: 0.779,
		trackWidthRear: 0.792,
	},
	m8gc: {
		height: -0.719,
		heightRear: -0.720,
		camber: 0,
		trackWidth: 0.847,
		trackWidthRear: 0.855,
	},
	macan: {
		height: -0.777,
		heightRear: -0.776,
		camber: 0,
		trackWidth: 0.801,
		trackWidthRear: 0.809,
	},
	macan2: {
		height: -0.940,
		heightRear: -0.919,
		camber: 0,
		trackWidth: 0.831,
		trackWidthRear: 0.814,
	},
	mark2: {
		height: -0.639,
		heightRear: -0.639,
		camber: 0,
		trackWidth: 0.765,
		trackWidthRear: 0.752,
	},
	matiz: {
		height: -0.545,
		heightRear: -0.545,
		camber: 0,
		trackWidth: 0.649,
		trackWidthRear: 0.663,
	},
	mazda3: {
		height: -0.573,
		heightRear: -0.573,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.789,
	},
	mc20: {
		height: -0.687,
		heightRear: -0.684,
		camber: 0,
		trackWidth: 0.848,
		trackWidthRear: 0.845,
	},
	missionr: {
		height: -0.187,
		heightRear: -0.187,
		camber: 0,
		trackWidth: 0.798,
		trackWidthRear: 0.843,
	},
	mistral: {
		height: -0.470,
		heightRear: -0.469,
		camber: 0,
		trackWidth: 0.860,
		trackWidthRear: 0.850,
	},
	mjc: {
		height: -0.735,
		heightRear: -0.734,
		camber: 0,
		trackWidth: 0.759,
		trackWidthRear: 0.750,
	},
	ml: {
		height: -0.634,
		heightRear: -0.634,
		camber: 0,
		trackWidth: 0.787,
		trackWidthRear: 0.787,
	},
	model3: {
		height: -0.571,
		heightRear: -0.571,
		camber: 0,
		trackWidth: 0.790,
		trackWidthRear: 0.790,
	},
	models: {
		height: -0.816,
		heightRear: -0.816,
		camber: 0,
		trackWidth: 0.828,
		trackWidthRear: 0.833,
	},
	modelt: {
		height: -0.941,
		heightRear: -0.968,
		camber: 0,
		trackWidth: 0.743,
		trackWidthRear: 0.889,
	},
	modelx: {
		height: -0.970,
		heightRear: -0.970,
		camber: 0,
		trackWidth: 0.906,
		trackWidthRear: 0.880,
	},
	// mp1: {
	// 	height: -0.693,
	// 	heightRear: -0.693,
	// 	camber: 0,
	// 	trackWidth: 0.821,
	// 	trackWidthRear: 0.821,
	// },
	msprinter: {
		height: -0.905,
		heightRear: -0.993,
		camber: 0,
		trackWidth: 0.887,
		trackWidthRear: 0.888,
	},
	mustang2: {
		height: -0.659,
		heightRear: -0.661,
		camber: 0,
		trackWidth: 0.803,
		trackWidthRear: 0.803,
	},
	mustang3: {
		height: -0.534,
		heightRear: -0.534,
		camber: 0,
		trackWidth: 0.782,
		trackWidthRear: 0.803,
	},
	mustang4: {
		height: -0.612,
		heightRear: -0.612,
		camber: 0,
		trackWidth: 0.839,
		trackWidthRear: 0.825,
	},
	mx5: {
		height: -0.568,
		heightRear: -0.568,
		camber: 0,
		trackWidth: 0.713,
		trackWidthRear: 0.715,
	},
	nevera: {
		height: -0.595,
		heightRear: -0.609,
		camber: 0,
		trackWidth: 0.844,
		trackWidthRear: 0.856,
	},
	// nisgtr: {
	// 	height: -0.420,
	// 	heightRear: -0.420,
	// 	camber: 0,
	// 	trackWidth: 0.808,
	// 	trackWidthRear: 0.821,
	// },
	noire: {
		height: -0.516,
		heightRear: -0.532,
		camber: 0,
		trackWidth: 0.814,
		trackWidthRear: 0.814,
	},
	nsx: {
		height: -0.593,
		heightRear: -0.593,
		camber: 0,
		trackWidth: 0.816,
		trackWidthRear: 0.774,
	},
	nsx2: {
		height: -0.676,
		heightRear: -0.642,
		camber: 0,
		trackWidth: 0.765,
		trackWidthRear: 0.780,
	},
	octavia18: {
		height: -0.817,
		heightRear: -0.826,
		camber: 0,
		trackWidth: 0.771,
		trackWidthRear: 0.769,
	},
	mp1: {
		height: -0.591,
		heightRear: -0.591,
		camber: 0,
		trackWidth: 0.827,
		trackWidthRear: 0.793,
	},	  
	p928: {
		height: -0.686,
		heightRear: -0.686,
		camber: 0,
		trackWidth: 0.773,
		trackWidthRear: 0.806,
	},
	panamera17turbo: {
		height: -0.508,
		heightRear: -0.522,
		camber: 0,
		trackWidth: 0.802,
		trackWidthRear: 0.802,
	},
	panamera2: {
		height: -0.569,
		heightRear: -0.569,
		camber: 0,
		trackWidth: 0.806,
		trackWidthRear: 0.788,
	},
	polestar: {
		height: -0.528,
		heightRear: -0.528,
		camber: 0,
		trackWidth: 0.814,
		trackWidthRear: 0.814,
	},
	priora: {
		height: -0.676,
		heightRear: -0.673,
		camber: 0,
		trackWidth: 0.762,
		trackWidthRear: 0.761,
	},
	pts21: {
		height: -0.580,
		heightRear: -0.585,
		camber: 0,
		trackWidth: 0.786,
		trackWidthRear: 0.819,
	},
	purosangue: {
		height: -0.648,
		heightRear: -0.649,
		camber: 0,
		trackWidth: 0.869,
		trackWidthRear: 0.872,
	},
	q60s: {
		height: -0.613,
		heightRear: -0.613,
		camber: 0,
		trackWidth: 0.786,
		trackWidthRear: 0.786,
	},
	q8: {
		height: -0.624,
		heightRear: -0.624,
		camber: 0,
		trackWidth: 0.829,
		trackWidthRear: 0.829,
	},
	r820: {
		height: -0.732,
		heightRear: -0.713,
		camber: 0,
		trackWidth: 0.802,
		trackWidthRear: 0.791,
	},
	ram: {
		height: -0.603,
		heightRear: -0.603,
		camber: 0,
		trackWidth: 0.839,
		trackWidthRear: 0.839,
	},
	ram2: {
		height: -0.613,
		heightRear: -0.613,
		camber: 0,
		trackWidth: 0.912,
		trackWidthRear: 0.911,
	},
	regalia: {
		height: -0.583,
		heightRear: -0.583,
		camber: 0,
		trackWidth: 0.910,
		trackWidthRear: 0.951,
	},
	regera: {
		height: -0.773,
		heightRear: -0.772,
		camber: 0,
		trackWidth: 0.848,
		trackWidthRear: 0.819,
	},
	reventon: {
		height: -0.613,
		heightRear: -0.616,
		camber: 0,
		trackWidth: 0.808,
		trackWidthRear: 0.885,
	},
	revuelto: {
		height: -0.633,
		heightRear: -0.632,
		camber: 0,
		trackWidth: 0.872,
		trackWidthRear: 0.828,
	},
	rio: {
		height: -0.754,
		heightRear: -0.776,
		camber: 0,
		trackWidth: 0.802,
		trackWidthRear: 0.804,
	},
	rio2: {
		height: -0.677,
		heightRear: -0.677,
		camber: 0,
		trackWidth: 0.721,
		trackWidthRear: 0.721,
	},
	roma: {
		height: -0.571,
		heightRear: -0.587,
		camber: 0,
		trackWidth: 0.814,
		trackWidthRear: 0.834,
	},
	rrab: {
		height: -0.927,
		heightRear: -0.913,
		camber: 0,
		trackWidth: 0.871,
		trackWidthRear: 0.870,
	},
	rrphantom: {
		height: -0.776,
		heightRear: -0.776,
		camber: 0,
		trackWidth: 0.835,
		trackWidthRear: 0.816,
	},
	rrphantom2: {
		height: -0.726,
		heightRear: -0.726,
		camber: 0,
		trackWidth: 0.835,
		trackWidthRear: 0.816,
	},
	rrs: {
		height: -0.784,
		heightRear: -0.783,
		camber: 0,
		trackWidth: 0.863,
		trackWidthRear: 0.863,
	},
	rs5: {
		height: -0.500,
		heightRear: -0.500,
		camber: 0,
		trackWidth: 0.779,
		trackWidthRear: 0.779,
	},
	rs6: {
		height: -0.621,
		heightRear: -0.621,
		camber: 0,
		trackWidth: 0.838,
		trackWidthRear: 0.832,
	},
	rs7: {
		height: -0.751,
		heightRear: -0.751,
		camber: 0,
		trackWidth: 0.854,
		trackWidthRear: 0.847,
	},
	rs72: {
		height: -0.723,
		heightRear: -0.719,
		camber: 0,
		trackWidth: 0.791,
		trackWidthRear: 0.793,
	},
	rsx: {
		height: -0.569,
		heightRear: -0.569,
		camber: 0,
		trackWidth: 0.744,
		trackWidthRear: 0.743,
	},
	rx7: {
		height: -0.468,
		heightRear: -0.472,
		camber: 0,
		trackWidth: 0.742,
		trackWidthRear: 0.726,
	},
	s15: {
		height: -0.742,
		heightRear: -0.742,
		camber: 0,
		trackWidth: 0.738,
		trackWidthRear: 0.744,
	},
	s2000: {
		height: -0.440,
		heightRear: -0.440,
		camber: 0,
		trackWidth: 0.723,
		trackWidthRear: 0.760,
	},
	s600: {
		height: -0.700,
		heightRear: -0.700,
		camber: 0,
		trackWidth: 0.806,
		trackWidthRear: 0.811,
	},
	s63cab: {
		height: -0.855,
		heightRear: -0.855,
		camber: 0,
		trackWidth: 0.828,
		trackWidthRear: 0.828,
	},
	s63w222: {
		height: -0.613,
		heightRear: -0.613,
		camber: 0,
		trackWidth: 0.780,
		trackWidthRear: 0.782,
	},
	samara: {
		height: -0.875,
		heightRear: -0.905,
		camber: 0,
		trackWidth: 0.747,
		trackWidthRear: 0.746,
	},
	sclass3: {
		height: -0.676,
		heightRear: -0.676,
		camber: 0,
		trackWidth: 0.826,
		trackWidthRear: 0.834,
	},
	sclass4: {
		height: -0.676,
		heightRear: -0.676,
		camber: 0,
		trackWidth: 0.826,
		trackWidthRear: 0.834,
	},
	senna: {
		height: -0.396,
		heightRear: -0.396,
		camber: 0,
		trackWidth: 0.788,
		trackWidthRear: 0.806,
	},
	sf90: {
		height: -0.565,
		heightRear: -0.550,
		camber: 0,
		trackWidth: 0.803,
		trackWidthRear: 0.807,
	},
	silverado: {
		height: -0.933,
		heightRear: -0.933,
		camber: 0,
		trackWidth: 0.852,
		trackWidthRear: 0.856,
	},
	skyline: {
		height: -0.446,
		heightRear: -0.446,
		camber: 0,
		trackWidth: 0.754,
		trackWidthRear: 0.751,
	},
	sl: {
		height: -0.648,
		heightRear: -0.648,
		camber: 0,
		trackWidth: 0.793,
		trackWidthRear: 0.796,
	},
	slr: {
		height: -0.312,
		heightRear: -0.311,
		camber: 0,
		trackWidth: 0.671,
		trackWidthRear: 0.726,
	},
	sls: {
		height: -0.540,
		heightRear: -0.538,
		camber: 0,
		trackWidth: 0.817,
		trackWidthRear: 0.826,
	},
	solaris:  {
		height: -0.533,
		heightRear: -0.535,
		camber: 0,
		trackWidth: 0.742,
		trackWidthRear: 0.757,
	},
	spectre: {
		height: -0.648,
		heightRear: -0.648,
		camber: 0,
		trackWidth: 0.797,
		trackWidthRear: 0.796,
	},
	speedtail: {
		height: -0.643,
		heightRear: -0.664,
		camber: 0,
		trackWidth: 0.801,
		trackWidthRear: 0.801,
	},
	sportage: {
		height: -0.613,
		heightRear: -0.613,
		camber: 0,
		trackWidth: 0.794,
		trackWidthRear: 0.794,
	},
	su7: {
		height: -0.617,
		heightRear: -0.622,
		camber: 0,
		trackWidth: 0.810,
		trackWidthRear: 0.820,
	},
	subwrx: {
		height: -0.840,
		heightRear: -0.840,
		camber: 0,
		trackWidth: 0.764,
		trackWidthRear: 0.754,
	},
	supragr: {
		height: -0.458,
		heightRear: -0.458,
		camber: 0,
		trackWidth: 0.826,
		trackWidthRear: 0.833,
	},
	tahoe2: {
		height: -0.640,
		heightRear: -0.640,
		camber: 0,
		trackWidth: 0.844,
		trackWidthRear: 0.844,
	},
	taycan: {
		height: -0.796,
		heightRear: -0.796,
		camber: 0,
		trackWidth: 0.842,
		trackWidthRear: 0.845,
	},
	tecnomar: {
		height: 0,
		heightRear: 0,
		camber: 0,
		trackWidth: 0,
		trackWidthRear: 0,
	},
	temerario: {
		height: -0.608,
		heightRear: -0.609,
		camber: 0,
		trackWidth: 0.855,
		trackWidthRear: 0.824,
	},
	terzo: {
		height: -0.289,
		heightRear: -0.288,
		camber: 0,
		trackWidth: 0.899,
		trackWidthRear: 0.881,
	},
	testarossa: {
		height: -0.550,
		heightRear: -0.552,
		camber: 0,
		trackWidth: 0.763,
		trackWidthRear: 0.846,
	},
	touareg: {
		height: -0.979,
		heightRear: -0.970,
		camber: 0,
		trackWidth: 0.828,
		trackWidthRear: 0.831,
	},
	touareg2: {
		height: -0.815,
		heightRear: -0.815,
		camber: 0,
		trackWidth: 0.857,
		trackWidthRear: 0.839,
	},
	tourbillon: {
		height: -0.596,
		heightRear: -0.623,
		camber: 0,
		trackWidth: 0.866,
		trackWidthRear: 0.866,
	},
	tt: {
		height: -0.646,
		heightRear: -0.646,
		camber: 0,
		trackWidth: 0.777,
		trackWidthRear: 0.777,
	},
	tt2: {
		height: -0.646,
		heightRear: -0.646,
		camber: 0,
		trackWidth: 0.777,
		trackWidthRear: 0.777,
	},
	turbor: {
		height: -0.602,
		heightRear: -0.599,
		camber: 0,
		trackWidth: 0.849,
		trackWidthRear: 0.964,
	},
	urban: {
		height: -0.865,
		heightRear: -0.865,
		camber: 0,
		trackWidth: 0.755,
		trackWidthRear: 0.755,
	},
	urus: {
		height: -0.541,
		heightRear: -0.537,
		camber: 0,
		trackWidth: 0.833,
		trackWidthRear: 0.834,
	},
	utopia: {
		height: -0.680,
		heightRear: -0.681,
		camber: 0,
		trackWidth: 0.850,
		trackWidthRear: 0.832,
	},
	valour: {
		height: -0.632,
		heightRear: -0.632,
		camber: 0,
		trackWidth: 0.829,
		trackWidthRear: 0.829,
	},
	vaz2107: {
		height: -0.539,
		heightRear: -0.539,
		camber: 0,
		trackWidth: 0.684,
		trackWidthRear: 0.684,
	},
	vaz2109: {
		height: -0.532,
		heightRear: -0.532,
		camber: 0,
		trackWidth: 0.695,
		trackWidthRear: 0.695,
	},
	vclass: {
		height: -0.712,
		heightRear: -0.6646,
		camber: 0,
		trackWidth: 0.883,
		trackWidthRear: 0.860,
	},
	velar: {
		height: -1.028,
		heightRear: -1.019,
		camber: 0,
		trackWidth: 0.909,
		trackWidthRear: 0.902,
	},
	veneno: {
		height: -0.276,
		heightRear: -0.271,
		camber: 0,
		trackWidth: 0.864,
		trackWidthRear: 0.874,
	},
	vesta: {
		height: -0.316,
		heightRear: -0.316,
		camber: 0,
		trackWidth: 0.748,
		trackWidthRear: 0.741,
	},
	veyron: {
		height: -0.575,
		heightRear: -0.581,
		camber: 0,
		trackWidth: 0.857,
		trackWidthRear: 0.853,
	},
	victoria: {
		height: -0.716,
		heightRear: -0.716,
		camber: 0,
		trackWidth: 0.819,
		trackWidthRear: 0.833,
	},
	victoria2: {
		height: -0.642,
		heightRear: -0.642,
		camber: 0,
		trackWidth: 0.910,
		trackWidthRear: 0.992,
	},
	viper: {
		height: -0.695,
		heightRear: -0.686,
		camber: 0,
		trackWidth: 0.865,
		trackWidthRear: 0.872,
	},
	viper2: {
		height: -0.588,
		heightRear: -0.588,
		camber: 0,
		trackWidth: 0.740,
		trackWidthRear: 0.788,
	},
	vulcan: {
		height: -0.488,
		heightRear: -0.493,
		camber: 0,
		trackWidth: 0.871,
		trackWidthRear: 0.850,
	},
	w210: {
		height: -0.702,
		heightRear: -0.704,
		camber: 0,
		trackWidth: 0.748,
		trackWidthRear: 0.748,
	},
	wraithb: {
		height: -0.754,
		heightRear: -0.754,
		camber: 0,
		trackWidth: 0.783,
		trackWidthRear: 0.801,
	},
	wrangler: {
		height: -0.648,
		heightRear: -0.648,
		camber: 0,
		trackWidth: 0.722,
		trackWidthRear: 0.722,
	},
	x5g05: {
		height: -1.001,
		heightRear: -1.001,
		camber: 0,
		trackWidth: 0.920,
		trackWidthRear: 0.923,
	},
	x5me70: {
		height: -0.729,
		heightRear: -0.734,
		camber: 0,
		trackWidth: 0.838,
		trackWidthRear: 0.856,
	},
	x6m: {
		height: -0.490,
		heightRear: -0.490,
		camber: 0,
		trackWidth: 0.877,
		trackWidthRear: 0.889,
	},
	// x6m2: {
	// 	height: -0.606,
	// 	heightRear: -0.614,
	// 	camber: 0,
	// 	trackWidth: 0.901,
	// 	trackWidthRear: 0.891,
	// },
	x6m2: {
		height: -0.634,
		heightRear: -0.634,
		camber: 0,
		trackWidth: 0.816,
		trackWidthRear: 0.834,
	},
	xc90: {
		height: -0.596,
		heightRear: -0.596,
		camber: 0,
		trackWidth: 0.802,
		trackWidthRear: 0.806,
	},
	xclass: {
		height: -1.184,
		heightRear: -1.164,
		camber: 0,
		trackWidth: 0.884,
		trackWidthRear: 0.882,
	},
	xclass2: {
		height: -1.365,
		heightRear: -1.365,
		camber: 0,
		trackWidth: 1.464,
		trackWidthRear: 1.464,
	},
	xes: {
		height: -0.537,
		heightRear: -0.537,
		camber: 0,
		trackWidth: 0.774,
		trackWidthRear: 0.774,
	},
	xk: {
		height: -0.563,
		heightRear: -0.563,
		camber: 0,
		trackWidth: 0.484,
		trackWidthRear: 0.484,
	},
	xm: {
		height: -1.048,
		heightRear: -1.048,
		camber: 0,
		trackWidth: 0.811,
		trackWidthRear: 0.813,
	},
	xts: {
		height: -0.678,
		heightRear: -0.678,
		camber: 0,
		trackWidth: 0.752,
		trackWidthRear: 0.752,
	},
	z4b: {
		height: -0.633,
		heightRear: -0.628,
		camber: 0,
		trackWidth: 0.775,
		trackWidthRear: 0.788,
	},
};
