import * as altClient from 'alt-client';

export const settingsTuningCamera = {
	minRadius: 0.9,
	maxRadius: 1.725,
	radius: 1.2,
	polarMin: 87.5, // 85
	polarMax: 160,
	fov: 60,
};

export enum ModCameraType {
	Default = 'default',
	BackUpAngle = 'backUpAngle',
	FrontUpAngle = 'frontUpAngle',
	FrontAngle = 'frontAngle',
	BackAngle = 'backAngle',
	Front = 'front',
	Left = 'left',
	Saloon = 'saloon',
	SaloonBack = 'saloonBack',
	Roof = 'roof',
	Wheel = 'wheel',
}

export const modToCameraType: {
	[key: number | string]: string;
} = {
	open: ModCameraType.Default,
	back: ModCameraType.Default,
	//
	0: ModCameraType.BackUpAngle,
	1: ModCameraType.FrontAngle,
	2: ModCameraType.BackAngle,
	3: ModCameraType.Left,
	4: ModCameraType.BackAngle,
	5: ModCameraType.Default,
	6: ModCameraType.FrontAngle,
	7: ModCameraType.FrontUpAngle,
	8: ModCameraType.Wheel,
	9: ModCameraType.Wheel,
	10: ModCameraType.Roof,
	11: ModCameraType.Default,
	12: ModCameraType.Default,
	13: ModCameraType.Default,
	14: ModCameraType.Default,
	15: ModCameraType.Default,
	16: ModCameraType.Default,
	17: ModCameraType.Default,
	18: ModCameraType.Default,
	19: ModCameraType.Default,
	20: ModCameraType.Default,
	21: ModCameraType.Default,
	22: ModCameraType.Front,
	23: ModCameraType.Wheel,
	24: ModCameraType.BackAngle,
	25: ModCameraType.Default,
	26: ModCameraType.Default,
	27: ModCameraType.Saloon,
	28: ModCameraType.Saloon,
	29: ModCameraType.Saloon,
	30: ModCameraType.Saloon,
	31: ModCameraType.Saloon,
	32: ModCameraType.Saloon,
	33: ModCameraType.Saloon,
	34: ModCameraType.Saloon,
	35: ModCameraType.Saloon,
	36: ModCameraType.Saloon,
	37: ModCameraType.Default,
	38: ModCameraType.Default,
	39: ModCameraType.Default,
	40: ModCameraType.Default,
	41: ModCameraType.Default,
	42: ModCameraType.Default,
	43: ModCameraType.Default,
	44: ModCameraType.Default,
	45: ModCameraType.Default,
	46: ModCameraType.Default,
	47: ModCameraType.Default,
	48: ModCameraType.Default,
	49: ModCameraType.Default,
	50: ModCameraType.Default,
	51: ModCameraType.Default,
	52: ModCameraType.Default,
	53: ModCameraType.Default,
	54: ModCameraType.Default,
	55: ModCameraType.Default,
	56: ModCameraType.Default,
	57: ModCameraType.Default,
	58: ModCameraType.Default,
	59: ModCameraType.Default,
	60: ModCameraType.Default,
	61: ModCameraType.Default,
	62: ModCameraType.Default,
	63: ModCameraType.Default,
	64: ModCameraType.Default,
	65: ModCameraType.Default,
	66: ModCameraType.Default,
	67: ModCameraType.Default,
	68: ModCameraType.Default,
	69: ModCameraType.Default,
	// Фейк
	200: ModCameraType.BackUpAngle,
	201: ModCameraType.FrontAngle,
	202: ModCameraType.BackAngle,
	203: ModCameraType.Left,
	204: ModCameraType.BackAngle,
	205: ModCameraType.Default,
	206: ModCameraType.FrontAngle,
	207: ModCameraType.FrontUpAngle,
	208: ModCameraType.Wheel,
	209: ModCameraType.Wheel,
	210: ModCameraType.Roof,
	227: ModCameraType.Saloon,
	228: ModCameraType.Saloon,
	229: ModCameraType.Saloon,
	230: ModCameraType.Saloon,
	231: ModCameraType.Saloon,
	232: ModCameraType.Saloon,
	233: ModCameraType.Saloon,
	234: ModCameraType.Saloon,
	235: ModCameraType.Saloon,
	236: ModCameraType.Saloon,
	237: ModCameraType.Default,
	238: ModCameraType.Default,
	239: ModCameraType.Default,
	240: ModCameraType.Default,
	241: ModCameraType.Default,
	242: ModCameraType.Default,
	243: ModCameraType.Default,
	244: ModCameraType.Default,
	245: ModCameraType.Default,
	246: ModCameraType.Default,
	247: ModCameraType.Default,
};

export function getOffsets(cameraType: string, min: altClient.Vector3, max: altClient.Vector3) {
	const offsets: {
		[key: string]: altClient.Vector3;
	} = {
		default: new altClient.Vector3(-1.2 + min.x, 1.2 + max.y, 1.5), // (-1.2 + min.x, 1.2 + max.y, 1.5)
		back: new altClient.Vector3(0, -1.5 + min.y, 0.5),
		backAngle: new altClient.Vector3(0.5 + max.x, -1.5 + min.y, 0.5),
		backUp: new altClient.Vector3(0, -1.5 + min.y, 1),
		backUpAngle: new altClient.Vector3(0.5 + max.x, -1.5 + min.y, 1),
		front: new altClient.Vector3(0, 1.5 + max.y, 0.5),
		frontAngle: new altClient.Vector3(-0.5 + min.x, 1.5 + max.y, 0.5),
		frontUp: new altClient.Vector3(0, 1.5 + max.y, 1),
		frontUpAngle: new altClient.Vector3(-0.5 + min.x, 1.5 + max.y, 1),
		left: new altClient.Vector3(-1.2 + min.x, 0, 0.5),
		wheel: new altClient.Vector3(-0.5 + min.x, 0.5 + max.y, 0.5),
		roof: new altClient.Vector3(-0.5 + min.x, 0.5 + max.y, 1.5),
	};

	// ПРИМЕРНЫЙ КОНФИГ 2 ВАРИАНТОВ КАМЕРЫ
	// export function getOffsets(cameraType: string, min: altClient.Vector3, max: altClient.Vector3) {
	// 	const offsets: {
	// 		[key: string]: altClient.Vector3;
	// 	} = {
	// 		NAME1: [
	// 			objectPosition: false: [ // false - камера перемещается и крутится смотря на центр (как вид от 3 лица)
	// 			offSetPosition( 0, -0.5, 3 ), // расположение центра машины, куда будет смотреть камера ( x, y, z )
	// 			offSetRotation( 0, -0.5, 3 ), // ротация, как изначально будет расположена камера ( rot.x, rot.y, rot.z )
	// 			offSetMove( min.x: -40, max.x: -40, min.y: -40, max.y: -40 ), // на сколько относительно центра и ротации будет двигаться камера
	// 			offSetFov( 40, 35 ), // стартовый и максимальный на сближение FOV
	// 			],
	// 		],
	// 		NAME2: [
	// 			objectPosition: true: [ // true - камера думает, что текущее расположение это центр и позволяет смотреть в разные стороны (как вид от 1 лица)
	// 			offSetPosition( 0, -0.5, 3 ), // расположение центра ( x, y, z )
	// 			offSetRotation( 0, -0.5 ), // ротация, как изначально будет расположена камера ( rot.x, rot.y)
	// 			offSetMove( min.x: -40, max.x: -40, min.y: -40, max.y: -40 ), // на сколько относительно центра и ротации будет двигаться камера
	// 			offSetFov( 40, 35 ), // стартовый и максимальный на сближение FOV
	// 			],
	// 		],
	// 	};

	const addOffsets: {
		[key: string]: altClient.Vector3;
	} = {
		default: new altClient.Vector3(0, 0, 0), // (0, 0, 0)
		roof: new altClient.Vector3(0, 0, 0.5),
	};

	return {
		offset: offsets[cameraType] || offsets['default'],
		addOffset: addOffsets[cameraType] || addOffsets['default'],
	};
}
