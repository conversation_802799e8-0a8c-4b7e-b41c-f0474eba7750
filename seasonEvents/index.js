export const seasonEventsEnum = {
    'winter': 1,
    'schoolDay': 2,
    'halloween': 3,
    'spring': 4,
    'summer': 5,
}

export const seasonEventsConfig = {
    [seasonEventsEnum.winter]: {
        start: '2024-11-30 23:55:00',
        end: '2025-01-14 06:00:00',
        startForTest: '2024-11-21 00:00:00',
        christmasStart: '2024-12-27 06:00:00',
        christmasEnd: '2025-01-13 06:00:00',
        christmasStartForTest: '2024-12-01 00:00:00',
        newYearStart: '2024-12-31 00:00:00',
        newYearEnd: '2025-01-01 07:00:00',
        withYear: true,
        // menu: {
        //     isActive: true,
        //     type: 'winter',
        //     categories: ['tape'],
        // },
        peds: [
            // { day: '2023-12-30 06:00', withYear: true, pedId: 287, noQuest: true }, // rent car
            // { day: '2023-12-30 06:00', withYear: true, pedId: 290, noQuest: true }, // rent car
            // { day: '2023-12-30 06:00', withYear: true, pedId: 343 }, // day 1
            // { day: '2023-12-31 10:00', withYear: true, pedId: 275 }, // day 2
            // { day: '2024-01-01 10:00', withYear: true, pedId: 337 }, // day 3
            // { day: '2024-01-02 10:00', withYear: true, pedId: 277 }, // day 4
            // { day: '2024-01-03 10:00', withYear: true, pedId: 340 }, // day 5
            // { day: '2024-01-04 10:00', withYear: true, pedId: 279 }, // day 6
            // { day: '2024-01-05 10:00', withYear: true, pedId: 338 }, // day 7
            // { day: '2024-01-06 10:00', withYear: true, pedId: 281 }, // day 8
            // { day: '2024-01-07 10:00', withYear: true, pedId: 339 }, // day 9
            // { day: '2024-01-08 10:00', withYear: true, pedId: 283 }, // day 10
            // { day: '2024-01-09 10:00', withYear: true, pedId: 341 }, // day 11
            // { day: '2024-01-10 10:00', withYear: true, pedId: 285 }, // day 12
            // { day: '2024-01-11 10:00', withYear: true, pedId: 342 }, // day 13
            // { day: '2024-01-12 10:00', withYear: true, pedId: 288 }, // day 14
            // {day: '2024-01-13 10:00', withYear: true, pedId: 344}, // day 15-16
        ],
        IPLs: [
            'MJ_XM_Apartaments1',
            'MJ_XM_Apartaments2',
            'MJ_XM_Apartaments3',
            'MJ_XM_Apartaments4',
            'MJ_XM_Apartaments5',
            'MJ_XM_Apartaments6',
            'MJ_XM_Army_Checkpoint1_01',
            'MJ_XM_Army_Checkpoint1_02',
            'MJ_XM_Army_Checkpoint2_01',
            'MJ_XM_Army_Checkpoint2_02',
            'MJ_XM_Auction',
            'MJ_XM_Automarket',
            'MJ_XM_Autoschool',
            'MJ_XM_Autoshop_1',
            'MJ_XM_Autoshop_2',
            'MJ_XM_Autoshop_3',
            'MJ_XM_Autoshop_4',
            'MJ_XM_Autoshop_5',
            'MJ_XM_Autoshop_6',
            'MJ_XM_Autoshop_7',
            'MJ_XM_Bank_Blaine',
            'MJ_XM_Bank_Fleeca1',
            'MJ_XM_Bank_Fleeca2',
            'MJ_XM_Bank_Fleeca3',
            'MJ_XM_Bank_Fleeca4',
            'MJ_XM_Bank_Fleeca5',
            'MJ_XM_Bank_Fleeca6',
            'MJ_XM_Bank_PacificStandart',
            'MJ_XM_BarPalleto',
            'MJ_XM_Barber_1',
            'MJ_XM_Barber_2',
            'MJ_XM_Barber_3',
            'MJ_XM_Barber_4',
            'MJ_XM_Barber_5',
            'MJ_XM_Barber_6',
            'MJ_XM_Barber_7',
            'MJ_XM_BeanMachine',
            'MJ_XM_Biker_AOD_01',
            'MJ_XM_Biker_AOD_02',
            'MJ_XM_Biker_TheLost_01',
            'MJ_XM_Biker_TheLost_02',
            'MJ_XM_Bookmaker',
            'MJ_XM_Business_Motosalon',
            'MJ_XM_Bussines_CarWash',
            'MJ_XM_Casino_01',
            'MJ_XM_Casino_02',
            'MJ_XM_Church1',
            'MJ_XM_Church2',
            'MJ_XM_Church3',
            'MJ_XM_CityHall_01',
            'MJ_XM_CityHall_02',
            'MJ_XM_CityHall_03',
            'MJ_XM_Cshop_1',
            'MJ_XM_Cshop_10',
            'MJ_XM_Cshop_11',
            'MJ_XM_Cshop_12',
            'MJ_XM_Cshop_13',
            'MJ_XM_Cshop_14',
            'MJ_XM_Cshop_2',
            'MJ_XM_Cshop_3',
            'MJ_XM_Cshop_4',
            'MJ_XM_Cshop_5',
            'MJ_XM_Cshop_6',
            'MJ_XM_Cshop_7',
            'MJ_XM_Cshop_8',
            'MJ_XM_Cshop_9',
            'MJ_XM_DV',
            'MJ_XM_DinnerChilliad',
            'MJ_XM_EMSSheriff_Sandy_01',
            'MJ_XM_EMSSheriff_Sandy_02',
            'MJ_XM_EMSSheriff_Sandy_03',
            'MJ_XM_EMS_Paleto_01',
            'MJ_XM_EMS_Paleto_02',
            // 'MJ_XM_EMS_PillboxHill',
            'MJ_XM_EventPed_LosSantos',
            'MJ_XM_FIB_01',
            'MJ_XM_FIB_02',
            'MJ_XM_Flightschool',
            'MJ_XM_FunicularDown',
            'MJ_XM_FunicularUp',
            'MJ_XM_Gas_1',
            'MJ_XM_Gas_10',
            'MJ_XM_Gas_11',
            'MJ_XM_Gas_12',
            'MJ_XM_Gas_13',
            'MJ_XM_Gas_14',
            'MJ_XM_Gas_15',
            'MJ_XM_Gas_16',
            'MJ_XM_Gas_17',
            'MJ_XM_Gas_18',
            'MJ_XM_Gas_19',
            'MJ_XM_Gas_2',
            'MJ_XM_Gas_20',
            'MJ_XM_Gas_21',
            'MJ_XM_Gas_22',
            'MJ_XM_Gas_23',
            'MJ_XM_Gas_24',
            'MJ_XM_Gas_25',
            'MJ_XM_Gas_26',
            'MJ_XM_Gas_27',
            'MJ_XM_Gas_28',
            'MJ_XM_Gas_3',
            'MJ_XM_Gas_4',
            'MJ_XM_Gas_5',
            'MJ_XM_Gas_6',
            'MJ_XM_Gas_7',
            'MJ_XM_Gas_8',
            'MJ_XM_Gas_9',
            'MJ_XM_Ghetto_Ballas',
            'MJ_XM_Ghetto_Bloods_01',
            'MJ_XM_Ghetto_Bloods_02',
            'MJ_XM_Ghetto_Marabunta_01',
            'MJ_XM_Ghetto_Marabunta_02',
            'MJ_XM_Ghetto_TheFamilies_02',
            'MJ_XM_Ghetto_Vagos',
            'MJ_XM_GolfClub_02',
            'MJ_XM_GolfClub_03',
            'MJ_XM_Gunshop_1',
            'MJ_XM_Gunshop_10',
            'MJ_XM_Gunshop_11',
            'MJ_XM_Gunshop_2',
            'MJ_XM_Gunshop_3',
            'MJ_XM_Gunshop_4',
            'MJ_XM_Gunshop_5',
            'MJ_XM_Gunshop_6',
            'MJ_XM_Gunshop_7',
            'MJ_XM_Gunshop_8',
            'MJ_XM_Gunshop_9',
            'MJ_XM_Gym',
            'MJ_XM_Hookies',
            'MJ_XM_HumanLabs_Enter',
            'MJ_XM_LSC1',
            'MJ_XM_LSC2',
            'MJ_XM_LSC3',
            'MJ_XM_LSC4',
            'MJ_XM_LSC5',
            'MJ_XM_LSC6',
            'MJ_XM_LSPD_MissonRow_01',
            'MJ_XM_LSPD_MissonRow_02',
            'MJ_XM_LegionSquare_02',
            'MJ_XM_LegionSquare_03',
            'MJ_XM_Leopolds',
            'MJ_XM_Mafia_Armenian_02',
            'MJ_XM_Mafia_Armenian_03',
            'MJ_XM_Mafia_CosaNostra_02',
            'MJ_XM_Mafia_CosaNostra_03',
            'MJ_XM_Mafia_Mexican',
            'MJ_XM_Mafia_Russian_02',
            'MJ_XM_Mafia_Russian_03',
            'MJ_XM_Mafia_Yakuza_02',
            'MJ_XM_Mafia_Yakuza_03',
            'MJ_XM_Makret_LosSantos',
            'MJ_XM_Market_PaletoBay',
            'MJ_XM_MazeArena',
            'MJ_XM_MazeBankTower_Top',
            'MJ_XM_Mega_Mall',
            'MJ_XM_Observatory',
            'MJ_XM_Port_Enter_1',
            'MJ_XM_Post_Enter_2',
            'MJ_XM_Prison_Inside',
            'MJ_XM_Prison_Outside_01',
            'MJ_XM_Prison_Outside_02',
            'MJ_XM_Rest_Arcade',
            'MJ_XM_Rest_BahamaMamasWest',
            'MJ_XM_Rest_Cinema1',
            'MJ_XM_Rest_Cinema2',
            'MJ_XM_Rest_Cinema2true',
            'MJ_XM_Rest_Cinema3',
            'MJ_XM_Rest_SplitSidesWest',
            'MJ_XM_Rest_Tequilala',
            'MJ_XM_Rest_ThePalace',
            'MJ_XM_Rest_VanillaUnicorn',
            'MJ_XM_Scene_SisyphusTheater',
            'MJ_XM_Scene_WinewoodBowl',
            'MJ_XM_Service_1',
            'MJ_XM_Service_2',
            'MJ_XM_Service_4',
            'MJ_XM_Service_5',
            'MJ_XM_Service_6',
            'MJ_XM_Sheriff_Paleto_01',
            'MJ_XM_Sheriff_Paleto_02',
            'MJ_XM_Spawn_Airport',
            'MJ_XM_Spawn_Chumash',
            'MJ_XM_Spawn_LSVokzal',
            'MJ_XM_Spawn_PBHotel',
            'MJ_XM_Spawn_SandyShores',
            'MJ_XM_Spawn_VespucciCanals',
            'MJ_XM_Start_01',
            'MJ_XM_Start_02',
            'MJ_XM_Start_03',
            'MJ_XM_Store_1',
            'MJ_XM_Store_10',
            'MJ_XM_Store_11',
            'MJ_XM_Store_13',
            'MJ_XM_Store_14',
            'MJ_XM_Store_15',
            'MJ_XM_Store_16',
            'MJ_XM_Store_2',
            'MJ_XM_Store_21',
            'MJ_XM_Store_22',
            'MJ_XM_Store_3',
            'MJ_XM_Store_4',
            'MJ_XM_Store_5',
            'MJ_XM_Store_6',
            'MJ_XM_Store_7',
            'MJ_XM_Store_8',
            'MJ_XM_Store_9',
            'MJ_XM_Tattoo_1',
            'MJ_XM_Tattoo_2',
            'MJ_XM_Tattoo_3',
            'MJ_XM_Tattoo_4',
            'MJ_XM_Tattoo_5',
            'MJ_XM_Tattoo_6',
            'MJ_XM_Tunnel_Enter',
            'MJ_XM_Tunnel_Exit',
            'MJ_XM_Univercity',
            'MJ_XM_WeazelNews_01',
            'MJ_XM_WeazelNews_02',
            'MJ_XM_Work_1',
            'MJ_XM_Work_10',
            'MJ_XM_Work_11',
            'MJ_XM_Work_2',
            'MJ_XM_Work_3',
            'MJ_XM_Work_4',
            'MJ_XM_Work_5',
            'MJ_XM_Work_6',
            'MJ_XM_Work_7',
            'MJ_XM_Work_8',
            'MJ_XM_Work_9',
        ]
    },
    [seasonEventsEnum.halloween]: {
        start: '2024-10-30 6:00:00',
        end: '2024-11-16 23:59:00',
        startForTest: '2024-10-03 00:00:00',
        withYear: true,
        menu: {
            isActive: true,
            type: 'halloween',
            categories: ['tape'],
        },
        peds: [
            // { day: '10-31 10:00', pedId: 391, taskName: 'photoLocation' },
            // { day: '11-01 10:00', pedId: 393, taskName: 'vampireManor' },
            // { day: '11-02 10:00', pedId: 244, taskName: 'mining' },
            // { day: '11-03 10:00', pedId: 395, taskName: 'protectionMonument' },
            // { day: '11-04 10:00', pedId: 243, taskName: 'graffitiSearch' },
            // { day: '11-05 10:00', pedId: 392, taskName: 'alchemy' },
            // { day: '11-06 10:00', pedId: 390, taskName: 'tractor' },
            // { day: '11-07 10:00', pedId: 246, taskName: 'newspaperDelivery' },
            // { day: '11-08 10:00', pedId: 396, taskName: 'escape' },
            // { day: '11-09 10:00', pedId: 397, taskName: 'bossfight' },
        ],
        IPLs: [
            'MJ_HW_Airport',
            'MJ_HW_AoD',
            'MJ_HW_ArmenianMafia',
            'MJ_HW_Auction',
            'MJ_HW_Ballas',
            'MJ_HW_Bookmaker',
            'MJ_HW_Casino',
            'MJ_HW_CityHall_01',
            'MJ_HW_CityHall_02',
            'MJ_HW_CityHall_03',
            'MJ_HW_EMSPaleto',
            // 'MJ_HW_EMSPillboxHill',
            'MJ_HW_EMSSheriffSandy_01',
            'MJ_HW_EMSSheriffSandy_02',
            'MJ_HW_EMSSheriffSandy_03',
            'MJ_HW_Families',
            'MJ_HW_FIB',
            'MJ_HW_Fox',
            'MJ_HW_GolfClub_2a',
            'MJ_HW_GolfClub_2b',
            'MJ_HW_GolfClub_2c',
            'MJ_HW_GolfClub_2d',
            'MJ_HW_GolfClub_2e',
            'MJ_HW_GolfClub_2f',
            'MJ_HW_GrandLeonClub',
            'MJ_HW_LaCosaNostra',
            'MJ_HW_Leopolds',
            'MJ_HW_LSPD_01',
            'MJ_HW_LSPD_02',
            'MJ_HW_MajesticAuto',
            'MJ_HW_Market_LosSantos',
            'MJ_HW_Mask',
            'MJ_HW_MazeBankArena',
            'MJ_HW_MexicanCartel',
            'MJ_HW_QuestPedPlace_1',
            'MJ_HW_QuestPedPlace_2',
            'MJ_HW_QuestPedPlace_3',
            'MJ_HW_QuestPedPlace_4',
            'MJ_HW_QuestPedPlace_5',
            'MJ_HW_QuestPedPlace_6',
            'MJ_HW_QuestPedPlace_7',
            'MJ_HW_RussianMafia',
            'MJ_HW_SheriffPaleto',
            'MJ_HW_Shield',
            'MJ_HW_TheLost',
            'MJ_HW_Univercity',
            'MJ_HW_WeazelNews',
            'MJ_HW_Witch_LosSantos',
            'MJ_HW_Yakuza',

            'MJ_HW_StartScreen',

            'MJ_HW_QuestPlace_FerLuci',
            'MJ_HW_QuestPlace_FrankStein',
            'MJ_HW_QuestPlace_JunkRat',
            'MJ_HW_QuestPlace_Muna',

            // 2023
            'church_halloween',
            'd1n_mj_helloween_telescopecamp',
            'd1n_mj_helloween_two_hoots_falls',
            'rm_halloween',
            'qt_hl_cave_ext',
            'shelter_halloween',
            'hunter_house',
            'mj_blood_ext',
            'mj_mara_ext',
            'mj_vagos_ext',

            // 2024
            'mj_event_props_tz_2',
            'mj_event_props_tz_3',
            'mj_event_props_tz_4',
            'mj_coroner_zombie',
            'mountain_halloween',
            'm23_1_int_placement_m23_1_interior_2_dlc_int_03_m23_1_milo_',
            'mj_boss_fight_placement'
        ]
    },
    [seasonEventsEnum.spring]: {
        start: '2024-03-08 6:00:00',
        end: '2024-03-14 23:59:00',
        startForTest: '2024-03-02 06:00:00',
        // start: '09-08 6:00:00',
        // end: '09-30 23:59:00',
        // startForTest: '09-02 06:00:00',

        withYear: true,
        menu: {
            isActive: true,
            type: 'spring',
            categories: ['tape'],
        },
        peds: [
            // { day: '02-02 06:00', pedId: 345 },
            // { day: '02-02 10:00', pedId: 346 },
            // { day: '02-02 10:00', pedId: 347 },
            // { day: '02-02 10:00', pedId: 348 },
            // { day: '02-02 10:00', pedId: 349 },
            // { day: '02-02 10:00', pedId: 350 },
            // { day: '02-02 10:00', pedId: 351 },
            // { day: '02-02 10:00', pedId: 352 },
            // // Ambient NPC park
            // { day: '02-02 10:00', pedId: 353 },
            // { day: '02-02 10:00', pedId: 354 },
            // { day: '02-02 10:00', pedId: 355 },
            // { day: '02-02 10:00', pedId: 356 },
            // { day: '02-02 10:00', pedId: 357 },
            // { day: '02-02 10:00', pedId: 358 },
            // { day: '02-02 10:00', pedId: 359 },
            // { day: '02-02 10:00', pedId: 360 },
        ],
        IPLs: [
            'maj_event_love_0'
        ]
    },
    [seasonEventsEnum.summer]: {
        start: '2024-07-26 06:00:00',
        end: '2024-08-12 06:00:00',
        startForTest: '2024-06-07 06:00:00',
        withYear: true,
        menu: {
            isActive: true,
            type: 'summer',
            categories: ['event'],
        },
        peds: [
            // { day: '07-26 06:00', pedId: 367 },
            // { day: '07-27 06:00', pedId: 368 },
            // { day: '08-04 06:00', pedId: 369 },
            // { day: '07-27 06:00', pedId: 370 },
            // { day: '07-29 06:00', pedId: 371 },
            // { day: '08-02 06:00', pedId: 372 },
            // { day: '07-29 06:00', pedId: 373 },
            // { day: '07-30 06:00', pedId: 374 },
            // { day: '08-08 06:00', pedId: 375 },
            // // { day: '07-09 06:00', pedId: 376 },
            // { day: '08-06 06:00', pedId: 377 },
            // { day: '07-28 06:00', pedId: 378 },
            // { day: '07-28 06:00', pedId: 379 },
            // // Ambient NPC park
            // // { day: '02-02 06:00', pedId: 353 },
        ],
        IPLs: [
            'maj_festival_m'
        ],
        stages: {
            'specialMethod': [
                '07-27 06:00',
                '07-28 06:00',
                '08-01 06:00',
                '08-03 06:00'
            ],
            'yesSir': [
                '07-29 06:00',
                '07-31 06:00',
                '08-02 06:00'
            ],
            'musketeer': [
                '07-30 06:00',
                '08-01 06:00',
                '08-05 06:00',
                '08-08 06:00'
            ],
            'silence': [
                '08-02 06:00'
            ],
            'corruptionBoat': [
                '08-04 06:00'
            ],
            'familyHeirloom': [
                '08-06 06:00'
            ],
            'democracy': [
                '08-08 06:00'
            ],
            'sunkenPast': [
                '07-27 06:00',
                '07-29 06:00',
                '07-31 06:00',
                '08-02 06:00',
                '08-04 06:00',
                '08-06 06:00',
            ],
            'modernBird': [
                '07-28 06:00',
                '08-07 06:00'
            ],
            'killerWhaleVision': [
                '08-01 06:00',
                '08-04 06:00',
                '08-07 06:00'
            ],
            'stingrayArticle': [
                '07-26 06:00',
                '07-31 06:00'
            ],
            'greenMedicine': [
                '07-29 06:00',
                '08-05 06:00'
            ],
            'vespucciCanals': [
                '07-28 06:00',
                '07-30 06:00',
                '08-03 06:00',
                '08-06 06:00'
            ],
            'ownAtmosphere': [
                '08-03 06:00'
            ]
        }
    }
}
