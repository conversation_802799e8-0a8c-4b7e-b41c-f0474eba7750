export const radarPropsSearchConfig = {
    awardXp: {
        1: 350,
        2: 200
    },
    propsEnum: {
        1: 'prop_peyote_gold_01',
        2: 'prop_peyote_water_01'
    },
    positions: [
        { pos: new mp.Vector3(2833.5957, -452.3736, 36.3231), },
        { pos: new mp.Vector3(2723.3538, -181.5692, 63.1143), },
        { pos: new mp.Vector3(2596.5759, 63.4154, 97.1509), },
        { pos: new mp.Vector3(2717.9736, 357.6132, 76.3414), },
        { pos: new mp.Vector3(2793.1648, 512.2945, 69.0623), },
        { pos: new mp.Vector3(2818.2329, 675.1649, 43.1304), },
        { pos: new mp.Vector3(2913.0461, 755.9736, 20.2314), },
        { pos: new mp.Vector3(2584.8264, 537.4681, 105.8960) },
        { pos: new mp.Vector3(2552.3472, 732.3165, 94.3033) },
        { pos: new mp.Vector3(2674.2725, 1111.7142, 36.2894) },
        { pos: new mp.Vector3(2675.6045, 1264.9187, 23.5341) },
        { pos: new mp.Vector3(2787.8901, 1359.7186, 24.6461) },
        { pos: new mp.Vector3(2902.3516, 1578.2770, 18.0579) },
        { pos: new mp.Vector3(2876.4658, 1683.2175, 23.6183) },
        { pos: new mp.Vector3(2757.4812, 1743.9429, 24.3766) },
        { pos: new mp.Vector3(2573.6704, 1745.1428, 24.6798) },
        { pos: new mp.Vector3(2692.9451, 1849.8066, 34.5875) },
        { pos: new mp.Vector3(2858.3076, 1868.7296, 40.6871) },
        { pos: new mp.Vector3(2945.8682, 1839.5209, 13.6432) },
        { pos: new mp.Vector3(2967.2175, 1975.7935, 44.0740) },
        { pos: new mp.Vector3(3099.4944, 1988.6505, 13.2051) },
        { pos: new mp.Vector3(3175.8989, 2170.4570, 44.7480) },
        { pos: new mp.Vector3(3127.0681, 2262.5540, 75.3978) },
        { pos: new mp.Vector3(3003.6924, 2237.8550, 97.1846) },
        { pos: new mp.Vector3(2875.7407, 2341.8462, 160.017) },
        { pos: new mp.Vector3(2796.8835, 2089.1868, 126.907) },
        { pos: new mp.Vector3(2653.2395, 2003.9604, 36.2557) },
        { pos: new mp.Vector3(2662.3911, 2109.0725, 37.3510) },
        { pos: new mp.Vector3(2717.1824, 2223.0725, 39.1201) },
        { pos: new mp.Vector3(3130.7605, 2429.1033, 64.7993) },
        { pos: new mp.Vector3(3226.0747, 2536.1802, 62.3224) },
        { pos: new mp.Vector3(3327.6133, 2665.3713, 16.9963) },
        { pos: new mp.Vector3(3235.3318, 2806.0352, 32.8689) },
        { pos: new mp.Vector3(3122.7957, 2808.7517, 68.6915) },
        { pos: new mp.Vector3(3181.9385, 2959.3977, 111.456) },
        { pos: new mp.Vector3(3412.2988, 2906.6243, 64.6813) },
        { pos: new mp.Vector3(3526.6287, 2885.5386, 20.1304) },
        { pos: new mp.Vector3(3535.6484, 3092.2285, 58.3627) },
        { pos: new mp.Vector3(3694.9978, 3075.6265, 14.2161) },
        { pos: new mp.Vector3(3688.6023, 3227.7100, 33.0037) },
        { pos: new mp.Vector3(3787.2659, 3294.6858, 25.3202) },
        { pos: new mp.Vector3(3745.2659, 3374.3867, 29.3978) },
        { pos: new mp.Vector3(3857.8418, 3483.4021, 28.8755) },
        { pos: new mp.Vector3(3924.3823, 3605.5781, 25.2021) },
        { pos: new mp.Vector3(3753.9429, 3679.5430, 32.5487) },
        { pos: new mp.Vector3(3647.0374, 3639.9561, 38.8000) },
        { pos: new mp.Vector3(3576.0923, 3579.1648, 55.2792) },
        { pos: new mp.Vector3(3487.2131, 3599.8154, 57.3854) },
        { pos: new mp.Vector3(3379.0945, 3654.7649, 51.4037) },
        { pos: new mp.Vector3(3307.3318, 3546.5276, 91.8096) },
        { pos: new mp.Vector3(3213.3362, 3348.1055, 154.002) },
        { pos: new mp.Vector3(3037.9121, 3217.9121, 99.5267) },
        { pos: new mp.Vector3(3021.4417, 3322.5364, 74.0330) },
        { pos: new mp.Vector3(2985.0198, 3636.6067, 67.3436) },
        { pos: new mp.Vector3(3045.9956, 3940.0483, 75.4315) },
        { pos: new mp.Vector3(2907.1252, 3313.8198, 90.1414) },
        { pos: new mp.Vector3(2830.7737, 3134.5715, 98.3641) },
        { pos: new mp.Vector3(2906.0308, 3000.0396, 102.391) },
        { pos: new mp.Vector3(2962.2725, 3078.5408, 144.313) },
        { pos: new mp.Vector3(3103.4109, 3104.7825, 172.149) },
        { pos: new mp.Vector3(3288.8704, 3138.1846, 252.893) },
        { pos: new mp.Vector3(3174.6199, 2958.5408, 109.030) },
        { pos: new mp.Vector3(2472.0000, 2648.6638, 43.3832) },
        { pos: new mp.Vector3(2486.6506, 2528.7034, 44.9502) },
        { pos: new mp.Vector3(2514.6331, 2417.8945, 49.8535) },
        { pos: new mp.Vector3(2535.3494, 2299.2000, 37.4352) },
        { pos: new mp.Vector3(2415.2571, 2689.6616, 43.4337) },
        { pos: new mp.Vector3(2303.6440, 2645.4990, 44.6132) },
        { pos: new mp.Vector3(2260.4043, 2488.5627, 58.3458) },
        { pos: new mp.Vector3(2403.6133, 2441.5649, 55.9363) },
        { pos: new mp.Vector3(2533.4900, 2296.4043, 36.4747) },
        { pos: new mp.Vector3(2363.9341, 2339.5913, 82.4579) },
        { pos: new mp.Vector3(2490.1055, 2178.5144, 36.0703) },
        { pos: new mp.Vector3(2511.8638, 2027.7098, 20.3495) },
        { pos: new mp.Vector3(2403.0857, 2119.0022, 80.7729) },
        { pos: new mp.Vector3(2455.4636, 1850.1758, 35.2278) },
        { pos: new mp.Vector3(2489.2615, 1752.5538, 40.1143) },
        { pos: new mp.Vector3(2391.2043, 1584.9495, 37.9575) },
        { pos: new mp.Vector3(2380.6155, 1757.2219, 59.2726) },
        { pos: new mp.Vector3(2312.7561, 1878.4747, 105.946) },
        { pos: new mp.Vector3(2282.3472, 2275.8198, 65.5743) },
        { pos: new mp.Vector3(2250.7385, 2575.3450, 59.3231) },
        { pos: new mp.Vector3(2115.3362, 2431.8857, 94.7413) },
        { pos: new mp.Vector3(2008.4176, 2406.0659, 83.8733) },
        { pos: new mp.Vector3(2086.3647, 2234.2681, 94.0505) },
        { pos: new mp.Vector3(2008.7472, 2174.6770, 109.569) },
        { pos: new mp.Vector3(2017.2660, 1971.9165, 77.2682) },
        { pos: new mp.Vector3(2080.7737, 1823.5780, 98.7179) },
        { pos: new mp.Vector3(2112.5276, 1711.7274, 101.397) },
        { pos: new mp.Vector3(2155.3186, 1477.8989, 83.8396) },
        { pos: new mp.Vector3(2324.3472, 1269.6923, 67.4784) },
        { pos: new mp.Vector3(2431.2527, 1353.2968, 49.4154) },
        { pos: new mp.Vector3(1924.5494, 1158.9626, 230.180) },
        { pos: new mp.Vector3(1877.8154, 996.7780, 274.7986) },
        { pos: new mp.Vector3(1789.4769, 728.4528, 262.5656) },
        { pos: new mp.Vector3(1659.4286, 462.1582, 248.9509) },
        { pos: new mp.Vector3(1617.1121, 224.3473, 243.3737) },
        { pos: new mp.Vector3(1234.9319, 21.5736, 75.6674) },
        { pos: new mp.Vector3(565.5428, -240.3428, 54.1165) },
        { pos: new mp.Vector3(617.6572, -112.3912, 74.0667) },
        { pos: new mp.Vector3(817.7934, 154.8132, 80.8740) },
        { pos: new mp.Vector3(1010.1758, 398.9934, 91.5399) },
        { pos: new mp.Vector3(1113.7979, 490.5494, 93.1406) },
        { pos: new mp.Vector3(1286.5582, 678.9626, 90.0403) },
        { pos: new mp.Vector3(1358.7825, 842.0308, 103.0146) },
        { pos: new mp.Vector3(1450.1011, 982.1802, 113.0234) },
        { pos: new mp.Vector3(1565.1165, 1177.6219, 95.4323) },
        { pos: new mp.Vector3(1434.4220, 1229.9473, 109.889) },
        { pos: new mp.Vector3(1332.5275, 1364.6637, 104.497) },
        { pos: new mp.Vector3(1583.4066, 1401.6527, 103.991) },
        { pos: new mp.Vector3(1602.5802, 1549.9648, 103.368) },
        { pos: new mp.Vector3(1633.8066, 1761.9561, 106.772) },
        { pos: new mp.Vector3(1699.4242, 1970.0439, 97.5216) },
        { pos: new mp.Vector3(1706.8220, 2197.1340, 82.6769) },
        { pos: new mp.Vector3(1545.2704, 2304.0000, 71.6908) },
        { pos: new mp.Vector3(1347.5604, 2268.8308, 86.4513) },
        { pos: new mp.Vector3(1335.5209, 2068.0483, 96.0388) },
        { pos: new mp.Vector3(1320.2373, 1862.0308, 91.4052) },
        { pos: new mp.Vector3(1333.1472, 1672.1934, 92.0959) },
        { pos: new mp.Vector3(1162.9319, 1974.2241, 62.8110) },
        { pos: new mp.Vector3(990.7121, 2092.4043, 50.9993) },
        { pos: new mp.Vector3(1141.2792, 1487.1560, 143.049) },
        { pos: new mp.Vector3(1234.0088, 1312.9319, 143.134) },
        { pos: new mp.Vector3(1189.9780, 1146.5802, 162.663) },
        { pos: new mp.Vector3(1247.6571, 1029.2307, 142.729) },
        { pos: new mp.Vector3(1171.2660, 835.7407, 136.5626) },
        { pos: new mp.Vector3(1086.2770, 719.9736, 157.4059) },
        { pos: new mp.Vector3(1063.3319, 675.6528, 152.3678) },
        { pos: new mp.Vector3(944.1495, 585.9429, 131.2380) },
        { pos: new mp.Vector3(871.2659, 681.4154, 157.5070) },
        { pos: new mp.Vector3(989.7890, 770.1495, 202.0410) },
        { pos: new mp.Vector3(1035.1252, 873.9956, 216.4139) },
        { pos: new mp.Vector3(1150.3912, 1014.0264, 195.031) },
        { pos: new mp.Vector3(1008.4220, 1069.2263, 266.643) },
        { pos: new mp.Vector3(869.6044, 1037.7627, 271.1926) },
        { pos: new mp.Vector3(777.5736, 1013.0505, 260.6278) },
        { pos: new mp.Vector3(707.9209, 923.3406, 253.6688) },
        { pos: new mp.Vector3(603.7187, 869.5385, 228.6974) },
        { pos: new mp.Vector3(449.7363, 1006.8660, 227.5011) },
        { pos: new mp.Vector3(384.2242, 1136.5582, 256.5502) },
        { pos: new mp.Vector3(355.2923, 919.6219, 202.4791) },
        { pos: new mp.Vector3(414.2901, 778.0615, 190.1787) },
        { pos: new mp.Vector3(453.6396, 652.1539, 191.8301) },
        { pos: new mp.Vector3(606.9626, 426.7385, 162.6293) },
        { pos: new mp.Vector3(734.6638, 399.9429, 128.0366) },
        { pos: new mp.Vector3(806.8351, 251.1033, 86.7883) },
        { pos: new mp.Vector3(1054.6549, 1024.4835, 248.580) },
        { pos: new mp.Vector3(646.4440, 1032.7913, 267.9407) },
        { pos: new mp.Vector3(868.9451, 1213.2792, 348.4153) },
        { pos: new mp.Vector3(1023.9033, 1287.3231, 261.908) },
        { pos: new mp.Vector3(1207.4506, 1433.1956, 127.918) },
        { pos: new mp.Vector3(1190.7428, 1580.7825, 105.710) },
        { pos: new mp.Vector3(1142.2417, 1704.4220, 113.680) },
        { pos: new mp.Vector3(1077.1780, 1817.7495, 98.7010) },
        { pos: new mp.Vector3(1036.9319, 1969.2395, 70.1238) },
        { pos: new mp.Vector3(908.2154, 1975.7671, 79.0879) },
        { pos: new mp.Vector3(919.0154, 1844.0439, 141.0278) },
        { pos: new mp.Vector3(1006.2330, 1748.4000, 150.750) },
        { pos: new mp.Vector3(1075.7803, 1605.5209, 151.221) },
        { pos: new mp.Vector3(1143.0198, 1436.5978, 163.758) },
        { pos: new mp.Vector3(928.5626, 1667.2880, 168.5941) },
        { pos: new mp.Vector3(828.5934, 1594.9451, 217.3239) },
        { pos: new mp.Vector3(864.7385, 1452.4088, 302.0278) },
        { pos: new mp.Vector3(874.6682, 1303.0681, 357.2446) },
        { pos: new mp.Vector3(811.4769, 1318.2329, 362.5020) },
        { pos: new mp.Vector3(660.6066, 1325.4989, 350.7913) },
        { pos: new mp.Vector3(575.0110, 1275.0593, 327.1846) },
        { pos: new mp.Vector3(494.1231, 1190.4132, 282.8865) },
        { pos: new mp.Vector3(674.5714, 1126.2329, 299.0623) },
        { pos: new mp.Vector3(721.2791, 875.5253, 235.1003) },
        { pos: new mp.Vector3(874.5758, 891.2571, 191.6278) },
        { pos: new mp.Vector3(848.9143, 783.0066, 168.4257) },
        { pos: new mp.Vector3(813.6396, 650.4132, 136.6132) },
        { pos: new mp.Vector3(562.5890, 776.8351, 200.6593) },
        { pos: new mp.Vector3(469.4901, 829.7802, 198.9407) },
        { pos: new mp.Vector3(395.2220, 687.0330, 190.7179) },
        { pos: new mp.Vector3(377.9341, 552.0527, 170.6835) },
        { pos: new mp.Vector3(465.9692, 471.0198, 154.2212) },
        { pos: new mp.Vector3(587.2088, 417.8110, 166.0330) },
        { pos: new mp.Vector3(858.6594, 446.4923, 130.6652) },
        { pos: new mp.Vector3(538.1539, 301.4242, 121.4821) },
        { pos: new mp.Vector3(289.7011, 397.4769, 114.9106) },
        { pos: new mp.Vector3(143.6571, 390.4220, 113.2931) },
        { pos: new mp.Vector3(75.8505, 412.7341, 127.9355) },
        { pos: new mp.Vector3(217.8857, 569.5385, 172.7391) },
        { pos: new mp.Vector3(178.1275, 731.0110, 205.5626) },
        { pos: new mp.Vector3(79.1341, 616.0220, 198.5194) },
        { pos: new mp.Vector3(-174.0396, 555.3099, 183.3209) },
        { pos: new mp.Vector3(-178.4967, 712.3384, 202.5297) },
        { pos: new mp.Vector3(-315.7582, 718.1407, 197.8286) },
        { pos: new mp.Vector3(-384.3956, 855.7055, 226.3890) },
        { pos: new mp.Vector3(-527.9868, 887.7363, 235.0667) },
        { pos: new mp.Vector3(-437.5121, 951.3362, 255.2697) },
        { pos: new mp.Vector3(-488.2286, 1049.7759, 310.503) },
        { pos: new mp.Vector3(-598.5494, 1064.0835, 286.610) },
        { pos: new mp.Vector3(-512.7692, 1138.0352, 318.995) },
        { pos: new mp.Vector3(-762.5011, 938.4528, 230.2476) },
        { pos: new mp.Vector3(-787.3450, 1032.2241, 257.780) },
        { pos: new mp.Vector3(-826.9846, 1208.6769, 293.046) },
        { pos: new mp.Vector3(-662.2286, 1097.2880, 269.861) },
        { pos: new mp.Vector3(-605.5780, 1161.3890, 299.264) },
        { pos: new mp.Vector3(-501.4154, 1290.3561, 313.553) },
        { pos: new mp.Vector3(-440.2681, 1238.3473, 332.913) },
        { pos: new mp.Vector3(-317.7231, 1249.7803, 334.885) },
        { pos: new mp.Vector3(-265.8330, 1382.3868, 340.731) },
        { pos: new mp.Vector3(-130.8659, 1562.7032, 306.442) },
        { pos: new mp.Vector3(-28.6418, 1538.0176, 292.9795) },
        { pos: new mp.Vector3(46.7209, 1466.5319, 276.4161) },
        { pos: new mp.Vector3(168.2769, 1406.0703, 254.1575) },
        { pos: new mp.Vector3(71.8945, 1583.7495, 250.0967) },
        { pos: new mp.Vector3(28.3253, 1672.6154, 248.1589) },
        { pos: new mp.Vector3(-63.9824, 1735.2131, 258.4711) },
        { pos: new mp.Vector3(-169.4637, 1793.6439, 227.855) },
        { pos: new mp.Vector3(-301.3714, 1556.6110, 358.154) },
        { pos: new mp.Vector3(-423.3363, 1531.2131, 385.552) },
        { pos: new mp.Vector3(-360.2769, 1389.2043, 331.211) },
        { pos: new mp.Vector3(-559.0681, 1285.3319, 314.159) },
        { pos: new mp.Vector3(-530.2681, 1410.7517, 345.770) },
        { pos: new mp.Vector3(-476.1363, 1496.9407, 384.069) },
        { pos: new mp.Vector3(-200.2813, 1585.5560, 317.748) },
        { pos: new mp.Vector3(-20.6374, 1571.5912, 295.5575) },
        { pos: new mp.Vector3(103.3582, 1643.9868, 240.2395) },
        { pos: new mp.Vector3(-207.4154, 1697.3275, 259.835) },
        { pos: new mp.Vector3(-449.5912, 1708.6022, 318.574) },
        { pos: new mp.Vector3(-476.7560, 1870.1143, 250.349) },
        { pos: new mp.Vector3(-559.6747, 1713.0066, 263.205) },
        { pos: new mp.Vector3(-601.4374, 1617.2307, 304.184) },
        { pos: new mp.Vector3(-638.2549, 1505.6703, 311.076) },
        { pos: new mp.Vector3(-713.4989, 1358.9011, 306.728) },
        { pos: new mp.Vector3(-808.5099, 1730.2682, 192.841) },
        { pos: new mp.Vector3(-610.5099, 1834.6945, 215.133) },
        { pos: new mp.Vector3(-543.8373, 2012.3473, 202.950) },
        { pos: new mp.Vector3(-464.1231, 2094.6990, 204.299) },
        { pos: new mp.Vector3(-377.1824, 1958.8220, 175.283) },
        { pos: new mp.Vector3(-343.3846, 2104.3911, 160.169) },
        { pos: new mp.Vector3(-299.4198, 2245.0549, 128.474) },
        { pos: new mp.Vector3(-338.4659, 2365.6880, 105.963) },
        { pos: new mp.Vector3(-315.7582, 2485.0286, 77.8411) },
        { pos: new mp.Vector3(-375.7582, 2613.0725, 86.8389) },
        { pos: new mp.Vector3(-510.3824, 2474.5847, 56.1384) },
        { pos: new mp.Vector3(-642.1055, 2331.6265, 72.2468) },
        { pos: new mp.Vector3(-738.9758, 2215.0945, 87.9341) },
        { pos: new mp.Vector3(-705.5868, 2095.4504, 116.865) },
        { pos: new mp.Vector3(-796.1407, 1902.4615, 166.487) },
        { pos: new mp.Vector3(-871.7934, 2018.3341, 139.022) },
        { pos: new mp.Vector3(-902.3604, 2117.0637, 146.099) },
        { pos: new mp.Vector3(-949.8857, 2269.7935, 140.269) },
        { pos: new mp.Vector3(-864.7780, 2377.3318, 117.000) },
        { pos: new mp.Vector3(-780.2110, 2484.9363, 100.857) },
        { pos: new mp.Vector3(-670.6813, 2615.4990, 44.0740) },
        { pos: new mp.Vector3(-640.4176, 2780.3340, 38.7664) },
        { pos: new mp.Vector3(-969.8242, 2623.0681, 64.2263) },
        { pos: new mp.Vector3(-1043.8945, 2475.2966, 62.423) },
        { pos: new mp.Vector3(-1011.8373, 2213.2351, 106.70) },
        { pos: new mp.Vector3(-903.6132, 2030.6901, 143.285) },
        { pos: new mp.Vector3(-1194.5934, 2536.6814, 19.405) },
        { pos: new mp.Vector3(-1285.2660, 2410.0352, 46.584) },
        { pos: new mp.Vector3(-1257.8374, 2287.3054, 67.326) },
        { pos: new mp.Vector3(-1110.1978, 2270.7693, 83.081) },
        { pos: new mp.Vector3(-1157.0637, 2103.8770, 123.97) },
        { pos: new mp.Vector3(-1090.7869, 1915.8857, 157.69) },
        { pos: new mp.Vector3(-1010.1363, 1761.4681, 211.12) },
        { pos: new mp.Vector3(-1030.6022, 1625.1428, 261.62) },
        { pos: new mp.Vector3(-1072.9187, 1394.8220, 230.39) },
        { pos: new mp.Vector3(-1298.6637, 2036.2814, 102.32) },
        { pos: new mp.Vector3(-1283.3539, 1852.9451, 113.66) },
        { pos: new mp.Vector3(-1271.7362, 1714.6813, 194.47) },
        { pos: new mp.Vector3(-1384.8264, 1746.8176, 122.89) },
        { pos: new mp.Vector3(-1370.8352, 1602.9363, 160.01) },
        { pos: new mp.Vector3(-1338.9890, 1472.0439, 152.85) },
        { pos: new mp.Vector3(-1201.2000, 1411.8594, 173.34) },
        { pos: new mp.Vector3(-1044.2638, 1298.5187, 287.18) },
        { pos: new mp.Vector3(-1195.8198, 1204.6549, 291.59) },
        { pos: new mp.Vector3(-1308.4879, 1039.3187, 264.72) },
        { pos: new mp.Vector3(-1371.0857, 858.4879, 210.095) },
        { pos: new mp.Vector3(-1510.3912, 1024.9846, 193.63) },
        { pos: new mp.Vector3(-1600.1934, 900.5802, 173.312) },
        { pos: new mp.Vector3(-1673.4594, 804.2110, 169.756) },
        { pos: new mp.Vector3(-1723.0154, 706.1802, 161.972) },
        { pos: new mp.Vector3(-1884.8044, 548.4791, 130.648) },
        { pos: new mp.Vector3(-1617.8638, 618.8835, 152.721) },
        { pos: new mp.Vector3(-1522.3517, 607.5692, 160.523) },
        { pos: new mp.Vector3(-1434.5011, 659.4066, 162.898) },
        { pos: new mp.Vector3(-1313.2880, 693.9956, 160.539) },
        { pos: new mp.Vector3(-1228.8264, 783.9956, 183.068) },
        { pos: new mp.Vector3(-1135.1208, 877.3582, 175.586) },
        { pos: new mp.Vector3(-1021.8461, 874.7341, 166.959) },
        { pos: new mp.Vector3(-990.6989, 1013.6572, 169.571) },
        { pos: new mp.Vector3(-1081.4901, 1125.2704, 204.51) },
        { pos: new mp.Vector3(-883.3978, 956.4528, 227.5516) },
        { pos: new mp.Vector3(-1259.5516, 2602.7737, 12.885) },
        { pos: new mp.Vector3(-1441.0813, 2520.8176, 29.937) },
        { pos: new mp.Vector3(-1623.6395, 2586.3823, 2.6908) },
        { pos: new mp.Vector3(-1655.3539, 2470.1406, 31.588) },
        { pos: new mp.Vector3(-1527.5604, 2314.6023, 45.489) },
        { pos: new mp.Vector3(-1500.5934, 2190.8044, 49.971) },
        { pos: new mp.Vector3(-1580.5714, 2071.6221, 79.509) },
        { pos: new mp.Vector3(-1669.2660, 1951.1604, 130.47) },
        { pos: new mp.Vector3(-1734.7384, 1836.6066, 180.22) },
        { pos: new mp.Vector3(-1855.2924, 1736.7825, 203.65) },
        { pos: new mp.Vector3(-1952.4396, 1803.2968, 170.02) },
        { pos: new mp.Vector3(-2015.7098, 1868.7957, 208.15) },
        { pos: new mp.Vector3(-1984.0747, 2036.4791, 159.05) },
        { pos: new mp.Vector3(-2014.2197, 2100.6199, 151.17) },
        { pos: new mp.Vector3(-2023.8198, 2173.2131, 120.70) },
        { pos: new mp.Vector3(-1938.8835, 2236.4702, 85.153) },
        { pos: new mp.Vector3(-1906.6813, 2326.4043, 52.600) },
        { pos: new mp.Vector3(-2037.5341, 2305.6089, 37.603) },
        { pos: new mp.Vector3(-2137.7671, 2257.8594, 54.352) },
        { pos: new mp.Vector3(-2223.8770, 2196.1450, 77.554) },
        { pos: new mp.Vector3(-2290.1934, 2148.7781, 84.698) },
        { pos: new mp.Vector3(-2272.8923, 2051.0242, 138.06) },
        { pos: new mp.Vector3(-2187.5210, 1998.0132, 181.88) },
        { pos: new mp.Vector3(-2339.7363, 1966.0088, 158.95) },
        { pos: new mp.Vector3(-2427.4944, 2128.3384, 115.33) },
        { pos: new mp.Vector3(-2477.8945, 2209.5562, 82.340) },
        { pos: new mp.Vector3(-2563.4504, 2216.9934, 61.850) },
        { pos: new mp.Vector3(-2670.9363, 2201.4197, 51.319) },
        { pos: new mp.Vector3(-2694.8572, 2077.3713, 66.332) },
        { pos: new mp.Vector3(-2592.7253, 2004.2109, 117.89) },
        { pos: new mp.Vector3(-2468.9539, 1998.1846, 156.05) },
        { pos: new mp.Vector3(-2774.0967, 2149.0154, 49.954) },
        { pos: new mp.Vector3(-2772.6462, 2026.1802, 107.63) },
        { pos: new mp.Vector3(-2655.1780, 1932.9626, 140.64) },
        { pos: new mp.Vector3(-2764.6287, 1802.5846, 111.89) },
        { pos: new mp.Vector3(-2880.2769, 1781.9868, 87.428) },
        { pos: new mp.Vector3(-2965.9121, 1769.6967, 76.678) },
        { pos: new mp.Vector3(-2940.6858, 1649.9473, 43.517) },
        { pos: new mp.Vector3(-2840.6902, 1667.2748, 65.507) },
        { pos: new mp.Vector3(-2741.5913, 1704.5538, 74.740) },
        { pos: new mp.Vector3(-2611.4900, 1782.7649, 115.01) },
        { pos: new mp.Vector3(-2712.7517, 1566.2109, 93.747) },
        { pos: new mp.Vector3(-2795.6440, 1512.5275, 83.283) },
        { pos: new mp.Vector3(-2882.0967, 1477.7935, 72.533) },
        { pos: new mp.Vector3(-2971.0286, 1386.5934, 46.921) },
        { pos: new mp.Vector3(-2943.7451, 1258.4572, 37.822) },
        { pos: new mp.Vector3(-2666.4658, 1278.5802, 144.93) },
        { pos: new mp.Vector3(-3012.4087, 1112.0703, 51.605) },
        { pos: new mp.Vector3(-3030.0791, 1004.4659, 83.115) },
        { pos: new mp.Vector3(-2841.9692, 983.9341, 161.079) },
        { pos: new mp.Vector3(-2609.6572, 881.7626, 253.837) },
        { pos: new mp.Vector3(-2488.0747, 886.4967, 262.936) },
        { pos: new mp.Vector3(-2260.3384, 902.0308, 237.021) },
        { pos: new mp.Vector3(-2097.3494, 891.6660, 203.389) },
        { pos: new mp.Vector3(-2749.7803, 1005.2967, 197.35) },
        { pos: new mp.Vector3(-2920.3911, 1103.3407, 96.409) },
        { pos: new mp.Vector3(-3029.6704, 880.0747, 64.0242) },
        { pos: new mp.Vector3(-2880.4351, 848.6901, 85.3055) },
        { pos: new mp.Vector3(-2781.9429, 709.1077, 144.869) },
        { pos: new mp.Vector3(-2589.3362, 604.4176, 206.539) },
        { pos: new mp.Vector3(-2696.4395, 548.5846, 147.380) },
        { pos: new mp.Vector3(-2883.9165, 488.4396, 51.3363) },
        { pos: new mp.Vector3(-2836.0747, 380.2418, 99.5941) },
        { pos: new mp.Vector3(-2872.2856, 265.4110, 86.9736) },
        { pos: new mp.Vector3(-2879.8813, 169.2000, 64.7993) },
        { pos: new mp.Vector3(-2766.7122, 183.3626, 80.0652) },
        { pos: new mp.Vector3(-2646.1582, 104.7165, 80.2168) },
        { pos: new mp.Vector3(-2570.3735, 10.3121, 51.7576) },
        { pos: new mp.Vector3(-2541.4812, -99.9956, 32.4476) },
        { pos: new mp.Vector3(-2439.5078, -138.2901, 36.457) },
        { pos: new mp.Vector3(-2254.9055, -238.5626, 59.221) },
        { pos: new mp.Vector3(-2227.3582, -78.2505, 108.170) },
        { pos: new mp.Vector3(-2076.3560, -18.9758, 82.3400) },
        { pos: new mp.Vector3(-2009.9473, -44.9011, 95.3817) },
        { pos: new mp.Vector3(-1867.0549, -104.3341, 91.506) },
        { pos: new mp.Vector3(-1752.9758, -69.9429, 78.2623) },
        { pos: new mp.Vector3(-1874.0176, 3.7187, 100.1838) },
        { pos: new mp.Vector3(-2042.9539, 103.3846, 125.121) },
        { pos: new mp.Vector3(-2169.4548, 339.1780, 149.823) },
        { pos: new mp.Vector3(-2044.7472, 290.0835, 103.621) },
        { pos: new mp.Vector3(-2091.5078, 462.3824, 119.746) },
        { pos: new mp.Vector3(-2089.2659, 648.3560, 161.197) },
        { pos: new mp.Vector3(-2271.5605, 754.9319, 233.887) },
        { pos: new mp.Vector3(-2393.5913, 748.4044, 270.956) },
        { pos: new mp.Vector3(-2463.5078, 622.0352, 220.676) },
        { pos: new mp.Vector3(-2466.1055, 436.3253, 178.266) },
        { pos: new mp.Vector3(-2452.0747, 227.7495, 151.340) },
        { pos: new mp.Vector3(-2443.9253, 97.7802, 109.1143) },
        { pos: new mp.Vector3(-2534.2681, 298.6681, 181.888) },
        { pos: new mp.Vector3(-2512.3120, 747.7451, 300.208) },
        { pos: new mp.Vector3(-3129.0725, 1376.9802, 21.562) },
        { pos: new mp.Vector3(-3011.9341, 1530.3824, 28.639) },
        { pos: new mp.Vector3(-3056.4658, 1669.3715, 33.138) },
        { pos: new mp.Vector3(-3078.1846, 1803.5472, 31.942) },
        { pos: new mp.Vector3(-3018.9890, 2058.6858, 37.081) },
        { pos: new mp.Vector3(-2926.4438, 2191.0286, 38.816) },
        { pos: new mp.Vector3(-2810.0308, 2244.0264, 26.786) },
        { pos: new mp.Vector3(-2712.1055, 2518.1934, 2.6740) },
        { pos: new mp.Vector3(-2678.7957, 2923.7803, 5.2520) },
        { pos: new mp.Vector3(-2638.9319, 3070.6550, 32.346) },
        { pos: new mp.Vector3(-2805.1252, 3213.3494, 31.571) },
        { pos: new mp.Vector3(-2904.1450, 3364.8528, 28.521) },
        { pos: new mp.Vector3(-2752.5364, 3393.8638, 29.448) },
        { pos: new mp.Vector3(-2467.2000, 3415.3186, 35.733) },
        { pos: new mp.Vector3(-2341.8462, 3477.0066, 37.586) },
        { pos: new mp.Vector3(-2303.2615, 3613.1077, 61.479) },
        { pos: new mp.Vector3(-2327.9473, 3749.1296, 63.249) },
        { pos: new mp.Vector3(-1849.7142, 3403.4636, 35.649) },
        { pos: new mp.Vector3(-1626.2902, 3249.0593, 46.517) },
        { pos: new mp.Vector3(-1456.8396, 3135.5342, 90.562) },
        { pos: new mp.Vector3(-1504.9451, 3015.0198, 53.105) },
        { pos: new mp.Vector3(-1358.4000, 2914.9055, 80.823) },
        { pos: new mp.Vector3(-1278.3033, 3062.2417, 91.759) },
        { pos: new mp.Vector3(-1256.1758, 3232.2065, 121.48) },
        { pos: new mp.Vector3(-1271.3934, 3358.5364, 172.85) },
        { pos: new mp.Vector3(-1180.9319, 3363.2043, 190.68) },
        { pos: new mp.Vector3(-1163.3011, 3167.2351, 121.07) },
        { pos: new mp.Vector3(-1139.9473, 2916.5012, 38.530) },
        { pos: new mp.Vector3(-1128.1187, 3250.7078, 121.26) },
        { pos: new mp.Vector3(-1218.6857, 3501.2966, 285.58) },
        { pos: new mp.Vector3(-2247.8770, 3860.2285, 114.48) },
        { pos: new mp.Vector3(-2127.2834, 3849.2175, 146.28) },
        { pos: new mp.Vector3(-2059.8198, 3956.6243, 150.95) },
        { pos: new mp.Vector3(-2086.8792, 4053.1252, 155.19) },
        { pos: new mp.Vector3(-1935.4286, 4064.0967, 226.52) },
        { pos: new mp.Vector3(-1840.5626, 4062.5408, 236.46) },
        { pos: new mp.Vector3(-1894.4572, 4182.5010, 174.44) },
        { pos: new mp.Vector3(-2005.1736, 4286.8218, 121.02) },
        { pos: new mp.Vector3(-2107.0154, 4373.3540, 113.51) },
        { pos: new mp.Vector3(-2326.3779, 4293.3232, 29.279) },
        { pos: new mp.Vector3(-2339.7891, 4211.9868, 38.311) },
        { pos: new mp.Vector3(-2398.0879, 4091.6177, 27.662) },
        { pos: new mp.Vector3(-1854.1450, 4511.3672, 21.377) },
        { pos: new mp.Vector3(-1815.2836, 4428.8569, 44.562) },
        { pos: new mp.Vector3(-1818.0791, 4333.9780, 70.629) },
        { pos: new mp.Vector3(-1666.0483, 4286.0835, 81.126) },
        { pos: new mp.Vector3(-1894.3649, 4272.8965, 139.71) },
        { pos: new mp.Vector3(-1921.3451, 3951.3494, 238.47) },
        { pos: new mp.Vector3(-1927.3187, 3740.5320, 178.48) },
        { pos: new mp.Vector3(-2056.1011, 3641.8550, 122.81) },
        { pos: new mp.Vector3(-2055.4285, 3524.5583, 76.105) },
        { pos: new mp.Vector3(-1957.5824, 3429.2307, 33.593) },
        { pos: new mp.Vector3(-1766.1758, 3318.3560, 44.798) },
        { pos: new mp.Vector3(-2006.1230, 4444.8657, 47.898) },
        { pos: new mp.Vector3(-1818.6857, 4430.0571, 44.714) },
        { pos: new mp.Vector3(-1721.4989, 4366.9316, 61.934) },
        { pos: new mp.Vector3(-1773.8242, 4180.0615, 158.40) },
        { pos: new mp.Vector3(-1617.0461, 4247.8945, 105.01) },
        { pos: new mp.Vector3(-1490.6769, 4168.4175, 100.04) },
        { pos: new mp.Vector3(-1418.4000, 4238.8877, 44.714) },
        { pos: new mp.Vector3(-1030.2461, 4244.1494, 115.29) },
        { pos: new mp.Vector3(-829.4637, 4155.4551, 211.325) },
        { pos: new mp.Vector3(-777.9956, 4075.1868, 165.493) },
        { pos: new mp.Vector3(-683.1296, 4062.8572, 163.876) },
        { pos: new mp.Vector3(-571.6616, 4165.6616, 190.970) },
        { pos: new mp.Vector3(-643.8461, 4295.9473, 127.817) },
        { pos: new mp.Vector3(-615.4154, 4036.8264, 130.176) },
        { pos: new mp.Vector3(-547.3715, 4036.8396, 137.236) },
        { pos: new mp.Vector3(-271.0549, 4016.5583, 56.2227) },
        { pos: new mp.Vector3(-260.7560, 4192.9580, 59.4073) },
        { pos: new mp.Vector3(-314.3736, 4283.5122, 48.9941) },
        { pos: new mp.Vector3(-397.0813, 4334.7690, 56.7450) },
        { pos: new mp.Vector3(-336.1978, 3922.8528, 67.7142) },
        { pos: new mp.Vector3(-325.2000, 3796.5759, 68.8770) },
        { pos: new mp.Vector3(-278.7560, 3633.0725, 68.5905) },
        { pos: new mp.Vector3(-142.3780, 3581.7495, 56.8125) },
        { pos: new mp.Vector3(11.8286, 3519.1252, 56.6271) },
        { pos: new mp.Vector3(-214.3385, 3415.7803, 111.422) },
        { pos: new mp.Vector3(-322.1934, 3398.1494, 145.223) },
        { pos: new mp.Vector3(-406.4967, 3331.2263, 144.296) },
        { pos: new mp.Vector3(-561.7582, 3247.2922, 94.4886) },
        { pos: new mp.Vector3(-564.5406, 3146.8616, 57.6212) },
        { pos: new mp.Vector3(-502.6681, 3032.0835, 35.9355) },
        { pos: new mp.Vector3(-342.8308, 2982.9626, 21.3436) },
        { pos: new mp.Vector3(-119.6440, 2885.8945, 47.4608) },
        { pos: new mp.Vector3(93.8769, 2969.0374, 50.5443) },
        { pos: new mp.Vector3(182.2549, 3165.3230, 42.3385) },
        { pos: new mp.Vector3(170.7956, 3281.8945, 40.8557) },
        { pos: new mp.Vector3(-540.7780, 3112.2065, 54.1501) },
        { pos: new mp.Vector3(-659.8286, 3286.3516, 98.6168) },
        { pos: new mp.Vector3(-750.2637, 3111.4153, 91.4220) },
        { pos: new mp.Vector3(-824.5187, 2951.3801, 33.2733) },
        { pos: new mp.Vector3(-944.1758, 2862.4219, 20.8887) },
        { pos: new mp.Vector3(-1031.8813, 3018.1846, 27.628) },
        { pos: new mp.Vector3(-1081.4769, 2869.5298, 12.682) },
        { pos: new mp.Vector3(-1192.0352, 2796.7913, 14.923) },
        { pos: new mp.Vector3(-1328.7561, 2696.7693, 5.5553) },
        { pos: new mp.Vector3(-1305.4813, 2760.1450, 16.811) },
        { pos: new mp.Vector3(-1218.8176, 2877.2307, 39.524) },
        { pos: new mp.Vector3(-1243.4506, 3004.0483, 71.707) },
        { pos: new mp.Vector3(-1105.5560, 3084.0132, 67.528) },
        { pos: new mp.Vector3(-64.5890, 3668.8879, 42.4733) },
        { pos: new mp.Vector3(250.7077, 3422.2285, 38.2271) },
        { pos: new mp.Vector3(331.4769, 3509.5913, 34.7223) },
        { pos: new mp.Vector3(246.0923, 3589.9780, 34.1326) },
        { pos: new mp.Vector3(558.2769, 3583.6616, 32.9194) },
        { pos: new mp.Vector3(726.7253, 3605.6572, 33.5597) },
        { pos: new mp.Vector3(822.1714, 3580.9187, 34.3517) },
        { pos: new mp.Vector3(988.8792, 3607.4109, 33.2733) },
        { pos: new mp.Vector3(1136.9407, 3635.5913, 33.8630) },
        { pos: new mp.Vector3(386.1494, 3441.5737, 35.0930) },
        { pos: new mp.Vector3(586.5099, 3491.7891, 34.1663) },
        { pos: new mp.Vector3(775.2264, 3498.0791, 32.7847) },
        { pos: new mp.Vector3(990.3824, 3509.1956, 33.7788) },
        { pos: new mp.Vector3(1145.9473, 3501.3625, 33.7620) },
        { pos: new mp.Vector3(336.1582, 3289.6089, 43.3158) },
        { pos: new mp.Vector3(589.7407, 3214.3252, 40.7040) },
        { pos: new mp.Vector3(725.9736, 3210.6990, 38.8506) },
        { pos: new mp.Vector3(870.1451, 3247.2000, 41.0242) },
        { pos: new mp.Vector3(1061.5912, 3272.2813, 37.5363) },
        { pos: new mp.Vector3(1302.1846, 3510.7122, 35.6659) },
        { pos: new mp.Vector3(1405.7142, 3486.0000, 35.3964) },
        { pos: new mp.Vector3(1551.0725, 3459.2703, 36.3063) },
        { pos: new mp.Vector3(1475.0901, 3375.5078, 37.4520) },
        { pos: new mp.Vector3(1316.4000, 3321.2571, 36.8960) },
        { pos: new mp.Vector3(1184.2814, 3290.3604, 37.7891) },
        { pos: new mp.Vector3(1458.7384, 3742.4438, 33.6945) },
        { pos: new mp.Vector3(1612.4572, 3838.7737, 34.0315) },
        { pos: new mp.Vector3(1696.1538, 3390.9363, 38.9685) },
        { pos: new mp.Vector3(1795.2528, 3462.6462, 39.7098) },
        { pos: new mp.Vector3(1853.1033, 3502.4834, 39.9963) },
        { pos: new mp.Vector3(1943.1428, 3544.2856, 39.0864) },
        { pos: new mp.Vector3(2060.0308, 3550.7869, 41.8499) },
        { pos: new mp.Vector3(2158.0352, 3652.7341, 38.5135) },
        { pos: new mp.Vector3(2283.1912, 3751.1604, 37.5194) },
        { pos: new mp.Vector3(2385.4153, 3761.2615, 39.6425) },
        { pos: new mp.Vector3(2471.4065, 3855.3757, 38.5304) },
        { pos: new mp.Vector3(2401.5430, 3973.0022, 37.0476) },
        { pos: new mp.Vector3(2261.7759, 3888.3955, 33.7957) },
        { pos: new mp.Vector3(2440.3911, 4125.2573, 36.3568) },
        { pos: new mp.Vector3(2430.5540, 4261.9912, 36.4916) },
        { pos: new mp.Vector3(2450.6111, 4390.7734, 35.1772) },
        { pos: new mp.Vector3(2508.0396, 4463.6836, 36.4916) },
        { pos: new mp.Vector3(2581.3845, 4372.6548, 40.4681) },
        { pos: new mp.Vector3(2542.0352, 4618.0352, 34.0483) },
        { pos: new mp.Vector3(2423.5254, 4500.9097, 32.8521) },
        { pos: new mp.Vector3(2619.3098, 4515.8770, 36.3737) },
        { pos: new mp.Vector3(2348.5979, 4641.2573, 34.8572) },
        { pos: new mp.Vector3(2183.4856, 4713.0596, 38.2609) },
        { pos: new mp.Vector3(2034.2241, 4630.5361, 39.7942) },
        { pos: new mp.Vector3(1897.4242, 4563.2046, 36.3905) },
        { pos: new mp.Vector3(2941.0549, 3416.0044, 60.1824) },
        { pos: new mp.Vector3(3028.6550, 3736.6814, 70.4608) },
        { pos: new mp.Vector3(3099.4021, 4097.1694, 75.0608) },
        { pos: new mp.Vector3(3015.5474, 4310.8086, 60.5194) },
        { pos: new mp.Vector3(2938.6023, 4546.7339, 49.4491) },
        { pos: new mp.Vector3(2819.1956, 4480.6021, 47.7642) },
        { pos: new mp.Vector3(2820.3560, 4718.3208, 46.5509) },
        { pos: new mp.Vector3(2788.7341, 4863.6792, 41.4454) },
        { pos: new mp.Vector3(2617.3713, 4929.8901, 40.8389) },
        { pos: new mp.Vector3(2558.9802, 5047.7012, 44.5458) },
        { pos: new mp.Vector3(2411.8682, 5091.0200, 46.1802) },
        { pos: new mp.Vector3(2354.1494, 5177.4067, 52.3473) },
        { pos: new mp.Vector3(1660.9055, 4605.7847, 46.4161) },
        { pos: new mp.Vector3(1564.9714, 4505.7627, 50.9150) },
        { pos: new mp.Vector3(1455.8638, 4416.9097, 46.2814) },
        { pos: new mp.Vector3(998.9143, 4373.9341, 46.7025) },
        { pos: new mp.Vector3(1056.8308, 4251.2441, 37.3846) },
        { pos: new mp.Vector3(1620.0923, 4643.1299, 53.6952) },
        { pos: new mp.Vector3(1667.6703, 5021.4990, 58.0762) },
        { pos: new mp.Vector3(1833.9165, 5173.3452, 63.5355) },
        { pos: new mp.Vector3(1987.3055, 5225.7100, 64.9341) },
        { pos: new mp.Vector3(2206.3647, 5309.3540, 126.604) },
        { pos: new mp.Vector3(2364.1846, 5274.4614, 79.1047) },
        { pos: new mp.Vector3(2504.8616, 5171.1431, 66.8044) },
        { pos: new mp.Vector3(2433.5605, 5362.5493, 77.7230) },
        { pos: new mp.Vector3(2466.3428, 5452.1011, 49.9209) },
        { pos: new mp.Vector3(2267.6836, 5608.9844, 54.7230) },
        { pos: new mp.Vector3(2127.9692, 5571.9297, 72.2975) },
        { pos: new mp.Vector3(2266.2065, 5760.9756, 146.841) },
        { pos: new mp.Vector3(2301.0461, 5880.5010, 52.2799) },
        { pos: new mp.Vector3(2071.1077, 5762.7690, 212.841) },
        { pos: new mp.Vector3(1903.1868, 5845.8857, 287.991) },
        { pos: new mp.Vector3(1751.8154, 5855.4463, 338.389) },
        { pos: new mp.Vector3(1547.4066, 5792.1099, 413.202) },
        { pos: new mp.Vector3(1350.1714, 5824.0615, 455.681) },
        { pos: new mp.Vector3(1256.3077, 5737.5034, 477.653) },
        { pos: new mp.Vector3(1118.6110, 5666.7822, 502.203) },
        { pos: new mp.Vector3(1323.9956, 5620.6152, 445.773) },
        { pos: new mp.Vector3(1499.7362, 5599.5166, 401.610) },
        { pos: new mp.Vector3(1647.4945, 5489.9604, 329.358) },
        { pos: new mp.Vector3(1812.9363, 5422.5493, 249.709) },
        { pos: new mp.Vector3(1958.4923, 5465.1299, 154.996) },
        { pos: new mp.Vector3(2039.3407, 5418.2769, 165.645) },
        { pos: new mp.Vector3(1836.0264, 6158.0044, 115.887) },
        { pos: new mp.Vector3(1621.2660, 6319.5562, 48.1853) },
        { pos: new mp.Vector3(1356.0791, 6340.6152, 37.1655) },
        { pos: new mp.Vector3(1228.0088, 6394.8657, 41.2601) },
        { pos: new mp.Vector3(1096.3912, 6397.0288, 51.1172) },
        { pos: new mp.Vector3(809.9868, 6391.3188, 52.4652) },
        { pos: new mp.Vector3(663.7187, 6378.1318, 68.2366) },
        { pos: new mp.Vector3(1078.8132, 6533.9736, 17.2660) },
        { pos: new mp.Vector3(1411.4901, 6606.0923, 10.7957) },
        { pos: new mp.Vector3(1481.7627, 6484.0220, 21.8997) },
        { pos: new mp.Vector3(1645.4901, 6560.7031, 29.2799) },
        { pos: new mp.Vector3(1718.6373, 6639.1650, 26.9714) },
        { pos: new mp.Vector3(1781.5385, 6544.6943, 68.1355) },
        { pos: new mp.Vector3(1913.4594, 6484.0220, 96.3253) },
        { pos: new mp.Vector3(2048.6375, 6507.8901, 106.671) },
        { pos: new mp.Vector3(1909.8857, 6412.9980, 58.4469) },
        { pos: new mp.Vector3(2108.6111, 6429.0596, 147.936) },
        { pos: new mp.Vector3(2183.3669, 6347.6704, 175.485) },
        { pos: new mp.Vector3(2258.4395, 6275.4858, 158.720) },
        { pos: new mp.Vector3(2192.4131, 6171.6924, 81.3121) },
        { pos: new mp.Vector3(2330.6638, 6185.5122, 174.660) },
        { pos: new mp.Vector3(2416.3911, 6250.9185, 151.963) },
        { pos: new mp.Vector3(2402.7561, 6400.8130, 91.7590) },
        { pos: new mp.Vector3(2441.8418, 6483.5737, 62.8953) },
        { pos: new mp.Vector3(2278.8660, 6525.6792, 62.0696) },
        { pos: new mp.Vector3(2256.6594, 6684.9888, 38.8169) },
        { pos: new mp.Vector3(2052.4219, 6670.9185, 53.8975) },
        { pos: new mp.Vector3(1884.4879, 6617.9736, 43.6022) },
        { pos: new mp.Vector3(1618.9978, 6661.0288, 23.2139) },
        { pos: new mp.Vector3(1423.5297, 6612.3560, 9.4476 ) },
        { pos: new mp.Vector3(2559.3889, 6305.8286, 106.098) },
        { pos: new mp.Vector3(2462.4395, 6136.0352, 179.866) },
        { pos: new mp.Vector3(2935.5693, 6295.6484, 61.7832) },
        { pos: new mp.Vector3(3146.5188, 6248.1494, 70.3934) },
        { pos: new mp.Vector3(3138.4746, 6129.2837, 86.3165) },
        { pos: new mp.Vector3(3219.6265, 6075.4287, 61.0586) },
        { pos: new mp.Vector3(3335.4460, 5952.1055, 76.4257) },
        { pos: new mp.Vector3(3268.8923, 5782.0483, 90.5626) },
        { pos: new mp.Vector3(3300.1978, 5623.5298, 46.5509) },
        { pos: new mp.Vector3(3264.3164, 5535.3364, 62.3224) },
        { pos: new mp.Vector3(3417.8110, 5486.9932, 24.3597) },
        { pos: new mp.Vector3(3311.2483, 5419.8989, 16.1875) },
        { pos: new mp.Vector3(3154.6550, 5355.0991, 26.1963) },
        { pos: new mp.Vector3(3184.3384, 5267.0112, 26.0952) },
        { pos: new mp.Vector3(3207.0593, 5189.7495, 35.6659) },
        { pos: new mp.Vector3(3080.9275, 5090.3076, 22.6915) },
        { pos: new mp.Vector3(3285.9824, 5041.7935, 22.8938) },
        { pos: new mp.Vector3(3400.7341, 4988.3604, 30.7627) },
        { pos: new mp.Vector3(3472.4175, 4855.3584, 34.2000) },
        { pos: new mp.Vector3(3422.7297, 4656.8833, 91.8938) },
        { pos: new mp.Vector3(3559.9517, 4520.5449, 46.9553) },
        { pos: new mp.Vector3(3721.9648, 4590.5142, 18.4286) },
        { pos: new mp.Vector3(3581.8418, 4696.5229, 21.0740) },
        { pos: new mp.Vector3(3714.6331, 4507.4243, 19.7935) },
        { pos: new mp.Vector3(3903.9429, 4375.6880, 22.5905) },
        { pos: new mp.Vector3(3755.8286, 4257.4287, 39.6761) },
        { pos: new mp.Vector3(3883.0286, 4036.2461, 17.8894) },
        { pos: new mp.Vector3(3792.0396, 4008.8967, 33.3407) },
        { pos: new mp.Vector3(3686.9802, 3869.2483, 31.7737) },
        { pos: new mp.Vector3(3559.6616, 3959.1824, 85.7437) },
        { pos: new mp.Vector3(3702.2373, 4145.3804, 124.430) },
        { pos: new mp.Vector3(3213.9429, 4563.1914, 177.726) },
        { pos: new mp.Vector3(3173.1165, 4388.0703, 128.660) },
        { pos: new mp.Vector3(3126.7781, 4237.9253, 112.619) },
        { pos: new mp.Vector3(3082.0747, 4053.7188, 67.2087) },
        { pos: new mp.Vector3(3043.0681, 3917.2878, 74.0498) },
        { pos: new mp.Vector3(3179.2747, 3832.7605, 84.0923) },
        { pos: new mp.Vector3(3094.9846, 3668.7166, 93.3260) },
        { pos: new mp.Vector3(3085.7407, 3507.2439, 121.835) },
        { pos: new mp.Vector3(3223.3845, 3491.3406, 76.7289) },
        { pos: new mp.Vector3(3382.5364, 3778.8528, 39.7942) },
        { pos: new mp.Vector3(3482.3735, 3874.6023, 55.8857) },
        { pos: new mp.Vector3(3571.5825, 3875.7891, 52.4147) },
        { pos: new mp.Vector3(3778.6155, 3935.6704, 27.5612) },
        { pos: new mp.Vector3(3929.6704, 4086.8440, 24.2585) },
        { pos: new mp.Vector3(3838.0220, 4165.7012, 30.3920) },
        { pos: new mp.Vector3(3831.3494, 4315.2002, 27.3590) },
        { pos: new mp.Vector3(3785.8286, 4412.8086, 14.5868) },
        { pos: new mp.Vector3(3722.0176, 3725.6968, 28.8755) },
        { pos: new mp.Vector3(748.0088, 6545.6045, 18.8667) },
        { pos: new mp.Vector3(496.6022, 6615.1914, 23.5004) },
        { pos: new mp.Vector3(338.5055, 6751.0552, 13.8455) },
        { pos: new mp.Vector3(212.5187, 6923.3540, 12.5648) },
        { pos: new mp.Vector3(80.4000, 7045.3320, 13.7781) },
        { pos: new mp.Vector3(28.2989, 6933.4551, 15.9180) },
        { pos: new mp.Vector3(-13.9912, 6785.6177, 16.5751) },
        { pos: new mp.Vector3(87.9824, 6718.9053, 40.5355) },
        { pos: new mp.Vector3(192.1319, 6668.5449, 33.8462) },
        { pos: new mp.Vector3(-86.4659, 6616.8657, 28.7576) },
        { pos: new mp.Vector3(-277.3187, 6484.4175, 11.6718) },
        { pos: new mp.Vector3(-334.4044, 6360.3428, 29.6337) },
        { pos: new mp.Vector3(-479.7363, 6330.8042, 11.8572) },
        { pos: new mp.Vector3(-609.4681, 6222.1318, 13.4916) },
        { pos: new mp.Vector3(-539.4857, 6089.6177, 14.1655) },
        { pos: new mp.Vector3(-636.1055, 5973.0068, 16.2043) },
        { pos: new mp.Vector3(-538.9714, 5854.7734, 33.2902) },
        { pos: new mp.Vector3(-502.9319, 6009.0464, 33.2733) },
        { pos: new mp.Vector3(-712.2857, 5976.5142, 14.5194) },
        { pos: new mp.Vector3(-891.9692, 6035.0244, 41.6981) },
        { pos: new mp.Vector3(-766.6154, 5828.7954, 15.6652) },
        { pos: new mp.Vector3(-790.2857, 5697.6792, 21.5289) },
        { pos: new mp.Vector3(-822.6066, 5595.6396, 25.0168) },
        { pos: new mp.Vector3(-890.9143, 5504.0044, 18.9846) },
        { pos: new mp.Vector3(-990.5538, 5438.5186, 28.4711) },
        { pos: new mp.Vector3(-1164.3956, 5327.3408, 36.424) },
        { pos: new mp.Vector3(-1312.4835, 5280.5933, 53.661) },
        { pos: new mp.Vector3(-1428.4484, 5402.1626, 23.719) },
        { pos: new mp.Vector3(-1500.6857, 5189.6045, 29.212) },
        { pos: new mp.Vector3(-1525.2000, 5026.6284, 62.726) },
        { pos: new mp.Vector3(-1705.5956, 5049.5737, 30.779) },
        { pos: new mp.Vector3(-1656.1978, 4910.8745, 59.407) },
        { pos: new mp.Vector3(-1699.7274, 4594.1538, 46.146) },
        { pos: new mp.Vector3(-1613.3143, 4555.8462, 41.546) },
        { pos: new mp.Vector3(-1508.2021, 4720.0615, 43.164) },
        { pos: new mp.Vector3(-1320.2770, 4947.7188, 150.88) },
        { pos: new mp.Vector3(-1233.3231, 5152.0747, 101.56) },
        { pos: new mp.Vector3(-960.1714, 5228.1758, 108.811) },
        { pos: new mp.Vector3(-878.6241, 5275.0288, 85.5582) },
        { pos: new mp.Vector3(-667.7275, 5506.5493, 46.9385) },
        { pos: new mp.Vector3(-523.6747, 5450.9404, 73.1736) },
        { pos: new mp.Vector3(-457.4506, 5577.0068, 71.9436) },
        { pos: new mp.Vector3(-442.0879, 5768.4263, 57.1831) },
        { pos: new mp.Vector3(-375.7187, 5867.1826, 51.6395) },
        { pos: new mp.Vector3(-314.2813, 5990.5054, 39.7605) },
        { pos: new mp.Vector3(-211.2923, 5993.3672, 63.9231) },
        { pos: new mp.Vector3(252.0264, 6345.4023, 59.9297) },
        { pos: new mp.Vector3(100.5495, 6037.5693, 186.9436) },
        { pos: new mp.Vector3(-551.6572, 5159.1167, 99.6110) },
        { pos: new mp.Vector3(-942.6594, 5113.3452, 171.711) },
        { pos: new mp.Vector3(-1079.6967, 5083.3320, 161.09) },
        { pos: new mp.Vector3(-1134.5538, 5022.3691, 165.22) },
        { pos: new mp.Vector3(-1241.3934, 5032.5361, 143.84) },
        { pos: new mp.Vector3(-1352.1890, 5004.2241, 122.42) },
        { pos: new mp.Vector3(-1437.2968, 4918.5625, 100.33) },
        { pos: new mp.Vector3(-1403.6307, 4851.0596, 127.76) },
        { pos: new mp.Vector3(-1251.9429, 4885.9517, 187.58) },
        { pos: new mp.Vector3(-1126.3385, 4755.5605, 232.99) },
        { pos: new mp.Vector3(-920.0571, 4809.4683, 307.183) },
        { pos: new mp.Vector3(-938.9407, 4708.1802, 277.595) },
        { pos: new mp.Vector3(-928.4440, 4599.4551, 234.072) },
        { pos: new mp.Vector3(-675.6791, 5127.0859, 125.829) },
        { pos: new mp.Vector3(-686.1495, 5016.1055, 165.190) },
        { pos: new mp.Vector3(-1006.6813, 5005.8066, 184.46) },
        { pos: new mp.Vector3(-1202.5187, 4792.2856, 217.52) },
        { pos: new mp.Vector3(-1059.8901, 4825.4375, 242.29) },
        { pos: new mp.Vector3(-763.7538, 4763.2222, 231.191) },
        { pos: new mp.Vector3(-748.3912, 4635.2969, 136.191) },
        { pos: new mp.Vector3(-622.5626, 4878.0923, 193.953) },
        { pos: new mp.Vector3(-534.0527, 5069.6836, 125.509) },
        { pos: new mp.Vector3(-410.2813, 5106.5405, 134.389) },
        { pos: new mp.Vector3(-397.3187, 5253.4551, 133.630) },
        { pos: new mp.Vector3(-635.0110, 4564.7207, 122.375) },
        { pos: new mp.Vector3(-549.4550, 4710.1055, 220.390) },
        { pos: new mp.Vector3(-398.3604, 4706.2944, 263.863) },
        { pos: new mp.Vector3(-223.3582, 4472.2549, 53.7627) },
        { pos: new mp.Vector3(-105.4022, 4380.0396, 69.9890) },
        { pos: new mp.Vector3(150.1978, 4357.8066, 58.0088) },
        { pos: new mp.Vector3(290.8484, 4311.9956, 46.1128) },
        { pos: new mp.Vector3(673.8989, 4314.9888, 73.0725) },
        { pos: new mp.Vector3(884.7692, 4285.5825, 45.2872) },
        { pos: new mp.Vector3(738.4088, 4420.5098, 113.8491) },
        { pos: new mp.Vector3(1145.8022, 4387.6616, 62.4235) },
        { pos: new mp.Vector3(1161.8638, 4571.7231, 74.3195) },
        { pos: new mp.Vector3(1298.8088, 4429.9253, 57.2168) },
        { pos: new mp.Vector3(1319.5253, 4611.6660, 130.648) },
        { pos: new mp.Vector3(1458.5670, 4596.9888, 58.4637) },
        { pos: new mp.Vector3(1609.4506, 4870.6284, 49.1458) },
        { pos: new mp.Vector3(1359.3363, 5067.2573, 149.031) },
        { pos: new mp.Vector3(-2088.6858, -271.9121, 19.574) },
        { pos: new mp.Vector3(-1811.1165, -542.4791, 29.751) },
        { pos: new mp.Vector3(-957.1780, -1925.5912, 15.243) },
        { pos: new mp.Vector3(-1362.4220, -2161.5562, 14.23) },
        { pos: new mp.Vector3(-1513.4637, -2433.9956, 14.82) },
        { pos: new mp.Vector3(-1659.4550, -2682.0659, 14.68) },
        { pos: new mp.Vector3(-1753.5165, -2846.5847, 15.12) },
        { pos: new mp.Vector3(-1823.0505, -3025.3845, 14.13) },
        { pos: new mp.Vector3(-1651.5560, -3227.4460, 14.70) },
        { pos: new mp.Vector3(-1270.3781, -3225.9692, 14.13) },
        { pos: new mp.Vector3(-940.2857, -3427.4109, 14.131) },
        { pos: new mp.Vector3(-766.0747, -3259.4373, 14.131) },
        { pos: new mp.Vector3(576.0923, -1494.2902, 26.0447) },
        { pos: new mp.Vector3(620.3737, -1643.1033, 24.3260) },
        { pos: new mp.Vector3(-488.5846, -1380.8176, 29.566) },
        { pos: new mp.Vector3(-476.3473, -1140.3693, 25.438) },
        { pos: new mp.Vector3(-1804.0220, 264.5802, 78.4645) },
        { pos: new mp.Vector3(-1604.9539, 329.9868, 70.5450) },
        { pos: new mp.Vector3(-1453.3187, 376.2857, 97.4037) },
        { pos: new mp.Vector3(-1262.1890, 424.1802, 89.8381) },
        { pos: new mp.Vector3(-1257.0198, 546.9099, 104.632) },
        { pos: new mp.Vector3(-1087.9121, 514.1671, 86.4850) },
        { pos: new mp.Vector3(-942.3428, 531.9824, 85.5582) },
        { pos: new mp.Vector3(-778.6154, 522.5011, 108.6761) },
        { pos: new mp.Vector3(-870.9495, 647.7231, 130.6820) },
        { pos: new mp.Vector3(-1103.9209, 704.5055, 150.396) },
        { pos: new mp.Vector3(-1807.1208, 535.3582, 154.794) },
        { pos: new mp.Vector3(-1738.3385, 617.0505, 176.361) },
        { pos: new mp.Vector3(-1379.8550, 696.2769, 176.631) },
        { pos: new mp.Vector3(-1492.2197, 744.4615, 192.790) },
        { pos: new mp.Vector3(1222.4044, -2628.3823, 35.6490) },
        { pos: new mp.Vector3(1317.3627, -2650.5100, 39.6761) },
        { pos: new mp.Vector3(1564.1274, -2625.0857, 45.3883) },
        { pos: new mp.Vector3(1735.0417, -2633.3010, 34.3348) },
        { pos: new mp.Vector3(1886.5319, -2576.0044, 33.7281) },
        { pos: new mp.Vector3(1975.7671, -2459.6045, 43.6359) },
        { pos: new mp.Vector3(1799.8154, -2466.1846, 109.6198) },
        { pos: new mp.Vector3(1745.4725, -2348.3076, 115.5677) },
        { pos: new mp.Vector3(1864.2594, -2263.8198, 158.7876) },
        { pos: new mp.Vector3(2137.8726, -2354.7561, 20.4506) },
        { pos: new mp.Vector3(2208.9363, -2197.4900, 36.6769) },
        { pos: new mp.Vector3(2034.3824, -2104.0088, 108.8110) },
        { pos: new mp.Vector3(1751.3143, -2139.2571, 113.5963) },
        { pos: new mp.Vector3(1878.7781, -1783.9517, 197.323) },
        { pos: new mp.Vector3(2024.7957, -1581.6000, 251.950) },
        { pos: new mp.Vector3(2245.3318, -1727.5648, 144.886) },
        { pos: new mp.Vector3(2446.3384, -1965.6132, 60.7047) },
        { pos: new mp.Vector3(2587.3845, -1693.6088, 19.9788) },
        { pos: new mp.Vector3(2382.0396, -1442.5319, 60.2161) },
        { pos: new mp.Vector3(2018.8616, -1250.1626, 152.704) },
        { pos: new mp.Vector3(2185.3450, -1182.4220, 158.096) },
        { pos: new mp.Vector3(2411.5913, -1125.2704, 52.1619) },
        { pos: new mp.Vector3(2642.4263, -1081.4110, 27.1736) },
        { pos: new mp.Vector3(2726.9934, -845.1693, 21.5289) },
        { pos: new mp.Vector3(2511.0989, -757.5824, 75.6337) },
        { pos: new mp.Vector3(2328.0527, -749.8417, 78.1274) },
        { pos: new mp.Vector3(2445.7451, -577.5956, 70.0564) },
        { pos: new mp.Vector3(2259.2175, -615.4813, 87.5802) },
        { pos: new mp.Vector3(2070.2373, -760.5758, 98.9033) },
        { pos: new mp.Vector3(1865.2351, -868.6550, 105.171) },
        { pos: new mp.Vector3(1720.8923, -1138.6285, 114.11) },
        { pos: new mp.Vector3(1572.8967, -1155.0989, 119.40) },
        { pos: new mp.Vector3(1368.3956, -1291.8066, 64.057) },
        { pos: new mp.Vector3(1452.0527, -1577.9341, 64.580) },
        { pos: new mp.Vector3(1517.3539, -1660.0220, 85.339) },
        { pos: new mp.Vector3(1650.8440, -1467.1121, 108.17) },
        { pos: new mp.Vector3(1404.9231, -2415.6660, 60.671) },
        { pos: new mp.Vector3(1500.1055, -2205.8242, 77.133) },
        { pos: new mp.Vector3(1595.2351, -1969.5033, 97.235) },
        { pos: new mp.Vector3(1282.2461, -1860.6593, 40.687) },
        { pos: new mp.Vector3(1237.4506, -1540.9055, 41.243) },
        { pos: new mp.Vector3(1199.2880, -2127.7847, 44.495) },
        { pos: new mp.Vector3(1264.0879, -2424.2241, 48.842) },
        { pos: new mp.Vector3(1110.0264, -2438.1230, 31.065) },
        { pos: new mp.Vector3(1068.3693, -2526.6462, 31.520) },
        { pos: new mp.Vector3(1327.4637, -1451.8154, 55.868) },
        { pos: new mp.Vector3(1445.7759, -1206.2109, 77.487) },
        { pos: new mp.Vector3(1649.4198, -1211.9473, 103.45) },
        { pos: new mp.Vector3(1747.2000, -1010.5978, 126.28) },
        { pos: new mp.Vector3(2042.4791, -784.4967, 101.868) },
        { pos: new mp.Vector3(1242.1846, -840.3692, 65.1362) },
        { pos: new mp.Vector3(1399.3846, -876.0264, 101.717) },
        { pos: new mp.Vector3(1576.6154, -846.0791, 84.6315) },
        { pos: new mp.Vector3(1600.9187, -685.5165, 134.237) },
        { pos: new mp.Vector3(1626.5011, -543.8373, 171.391) },
        { pos: new mp.Vector3(1615.7406, -377.9077, 234.847) },
        { pos: new mp.Vector3(1459.6879, -343.0813, 139.090) },
        { pos: new mp.Vector3(1216.6549, -174.6461, 66.7539) },
        { pos: new mp.Vector3(1407.2439, -273.3363, 170.447) },
        { pos: new mp.Vector3(1597.8462, -225.9297, 254.747) },
        { pos: new mp.Vector3(1760.7693, -239.0769, 288.126) },
        { pos: new mp.Vector3(1704.9890, -503.1033, 161.500) },
        { pos: new mp.Vector3(1831.1472, -708.4747, 102.576) },
        { pos: new mp.Vector3(2010.5802, -532.1143, 120.959) },
        { pos: new mp.Vector3(2109.8110, -350.0439, 178.485) },
        { pos: new mp.Vector3(2308.0088, -214.5626, 121.111) },
        { pos: new mp.Vector3(2222.9539, -117.0330, 210.516) },
        { pos: new mp.Vector3(2157.2043, 131.0901, 226.4901) },
        { pos: new mp.Vector3(2328.3164, 116.0440, 187.6681) },
        { pos: new mp.Vector3(2463.9033, 300.9231, 129.4857) },
        { pos: new mp.Vector3(2467.0154, 564.9099, 131.2212) },
        { pos: new mp.Vector3(2250.2769, 332.8484, 254.7135) },
        { pos: new mp.Vector3(2305.8330, 549.6396, 200.9626) },
        { pos: new mp.Vector3(2346.7122, 725.9604, 164.7018) },
        { pos: new mp.Vector3(2282.3604, 894.7121, 134.3217) },
        { pos: new mp.Vector3(2127.9033, 894.1187, 196.5985) },
        { pos: new mp.Vector3(2113.0154, 1069.5165, 144.650) },
        { pos: new mp.Vector3(2076.1582, 1220.5055, 140.522) },
        { pos: new mp.Vector3(1974.4879, 1413.8242, 130.513) },
        { pos: new mp.Vector3(1885.3055, 1561.6615, 95.3649) },
        { pos: new mp.Vector3(1801.8462, 1446.6857, 121.802) },
        { pos: new mp.Vector3(1784.0308, 1307.6835, 124.970) },
        { pos: new mp.Vector3(1834.7340, 1160.8748, 202.462) },
        { pos: new mp.Vector3(1733.7098, 1002.6198, 163.623) },
        { pos: new mp.Vector3(1691.4857, 789.5868, 201.3334) },
        { pos: new mp.Vector3(1513.8989, 713.4594, 101.8857) },
        { pos: new mp.Vector3(1412.8616, 580.5231, 107.3114) },
        { pos: new mp.Vector3(1280.7825, 447.0066, 103.2168) },
        { pos: new mp.Vector3(1135.7010, 352.3912, 91.4220) },
        { pos: new mp.Vector3(1692.5406, 2272.4438, 76.206) },
        { pos: new mp.Vector3(1253.1560, 2510.5188, 64.967) },
        { pos: new mp.Vector3(1515.2043, 2677.7012, 46.281) },
        { pos: new mp.Vector3(1648.0088, 2813.9868, 41.580) },
        { pos: new mp.Vector3(1829.3407, 2921.3142, 44.950) },
        { pos: new mp.Vector3(2021.0901, 2812.8396, 50.291) },
        { pos: new mp.Vector3(2279.7627, 2908.7473, 46.567) },
        { pos: new mp.Vector3(2194.0615, 3134.7957, 49.011) },
        { pos: new mp.Vector3(1984.5099, 3205.0154, 44.916) },
        { pos: new mp.Vector3(1809.9692, 3125.9604, 45.725) },
        { pos: new mp.Vector3(1685.7891, 3218.8352, 41.091) },
        { pos: new mp.Vector3(1437.0725, 3143.0901, 41.074) },
        { pos: new mp.Vector3(1192.6681, 3078.1582, 40.889) },
        { pos: new mp.Vector3(1180.9846, 2848.6550, 37.334) },
        { pos: new mp.Vector3(1285.8330, 2711.1692, 37.755) },
        { pos: new mp.Vector3(1070.1230, 2760.8835, 37.418) },
        { pos: new mp.Vector3(869.2484, 2784.7122, 58.7839) },
        { pos: new mp.Vector3(728.3472, 2817.4153, 61.6989) },
        { pos: new mp.Vector3(504.6725, 2733.8110, 44.2256) },
        { pos: new mp.Vector3(64.3912, 2821.9517, 55.683) },
        { pos: new mp.Vector3(1.7670, 2754.1978, 63.2828) },
        { pos: new mp.Vector3(-131.3275, 2688.1450, 65.2543) },
        { pos: new mp.Vector3(89.6835, 2449.3186, 87.0073) },
        { pos: new mp.Vector3(-16.9319, 2186.7166, 114.1692) },
        { pos: new mp.Vector3(-109.8330, 2208.2373, 153.5641) },
        { pos: new mp.Vector3(-217.0286, 2022.7120, 145.9648) },
        { pos: new mp.Vector3(-18.3956, 1982.2814, 184.4330) },
        { pos: new mp.Vector3(429.5077, 1702.9583, 258.9597) },
        { pos: new mp.Vector3(394.1538, 1561.3319, 287.7054) },
        { pos: new mp.Vector3(733.0154, 1561.4637, 269.8953) },
        { pos: new mp.Vector3(2926.5627, 4945.6089, 76.6447) },
        { pos: new mp.Vector3(3022.2725, 4856.0439, 131.7267) },
        { pos: new mp.Vector3(3159.9824, 4858.8394, 115.4667) },
        { pos: new mp.Vector3(2848.7605, 5272.5098, 90.6132) },
        { pos: new mp.Vector3(2961.6265, 5326.8525, 100.7737) },
        { pos: new mp.Vector3(2925.4153, 5440.2593, 169.4030) },
        { pos: new mp.Vector3(3031.1604, 5537.0376, 190.1619) },
        { pos: new mp.Vector3(3052.2988, 5672.2944, 217.4755) },
        { pos: new mp.Vector3(2988.2637, 5783.4463, 300.0732) },
        { pos: new mp.Vector3(2873.1692, 5941.5825, 356.6382) },
        { pos: new mp.Vector3(199.4374, 5256.0396, 599.1069) },
        { pos: new mp.Vector3(512.8879, 5520.1714, 775.0022) },
        { pos: new mp.Vector3(-1325.9341, 4447.4375, 23.1970) },
        { pos: new mp.Vector3(-1541.5516, 4463.7759, 18.0410) },
        { pos: new mp.Vector3(-1055.1165, 4409.5386, 16.6761) },
        { pos: new mp.Vector3(-747.0857, 4512.6460, 84.2102) },
        { pos: new mp.Vector3(423.7582, -415.4374, 44.4447) },
        { pos: new mp.Vector3(911.1824, -666.0791, 57.4022) },
        { pos: new mp.Vector3(1112.1362, -281.3934, 68.8096) },
        { pos: new mp.Vector3(1326.4484, -649.1341, 71.2190) },
        { pos: new mp.Vector3(1464.3033, -550.9714, 84.5641) },
        { pos: new mp.Vector3(1329.0857, -409.0813, 70.3766) },
        { pos: new mp.Vector3(-374.0439, -1796.2021, 23.1128) },
        { pos: new mp.Vector3(-707.9736, -1795.7935, 28.3363) },
        { pos: new mp.Vector3(-852.9363, -1878.5275, 22.6410) },
    ]
}

export const specialMethodPositions = {
    0: [
        new mp.Vector3(-1356.657, 5409.6875, 1),
        new mp.Vector3(-1372.84717, 5429.009, 1),
        new mp.Vector3(-1391.7959, 5450.849, 1),
        new mp.Vector3(-1413.41626, 5470.146, 1),
        new mp.Vector3(-1433.97827, 5478.822, 1),
        new mp.Vector3(-1459, 5473.08545, 1),
        new mp.Vector3(-1479.76868, 5463.70654, 1),
        new mp.Vector3(-1498.27283, 5446.627, 1),
        new mp.Vector3(-1509.24023, 5424.02539, 1),
        new mp.Vector3(-1504.29443, 5397.991, 1),
        new mp.Vector3(-1491.5459, 5376.27734, 1),
        new mp.Vector3(-1471.71912, 5357.187, 1),
        new mp.Vector3(-1451.18018, 5339.82275, 1),
        new mp.Vector3(-1427.81641, 5334.262, 1),
        new mp.Vector3(-1416.6792, 5345.017, 1),
        new mp.Vector3(-1437.17285, 5354.57373, 1),
        new mp.Vector3(-1455.01123, 5354.393, 1),
        new mp.Vector3(-1474.33533, 5372.616, 1),
        new mp.Vector3(-1491.98669, 5392.36, 1),
        new mp.Vector3(-1498.46509, 5415.37061, 1),
        new mp.Vector3(-1492.22864, 5432.959, 1),
        new mp.Vector3(-1478.73608, 5450.837, 1),
        new mp.Vector3(-1461.63831, 5458.309, 1),
        new mp.Vector3(-1440.71948, 5466.24756, 1),
        new mp.Vector3(-1425.20337, 5464.652, 1),
        new mp.Vector3(-1405.64819, 5454.748, 1),
        new mp.Vector3(-1390.15588, 5432.732, 1),
        new mp.Vector3(-1374.61975, 5410.16455, 1),
    ],

    1: [
        new mp.Vector3(-1628.83435, 5231.24463, 1),
        new mp.Vector3(-1625.09314, 5223.172, 1),
        new mp.Vector3(-1626.30249, 5216.39746, 1),
        new mp.Vector3(-1628.82642, 5207.94629, 1),
        new mp.Vector3(-1631.94153, 5201.878, 1),
        new mp.Vector3(-1631.26868, 5192.13135, 1),
        new mp.Vector3(-1627.15613, 5178.39844, 1),
        new mp.Vector3(-1639.47241, 5251.908, 1),
        new mp.Vector3(-1636.38232, 5264.13965, 1),
        new mp.Vector3(-1632.35657, 5274.66162, 1),
        new mp.Vector3(-1622.5415, 5280.148, 1),
        new mp.Vector3(-1612.05115, 5282.098, 1),
        new mp.Vector3(-1633.488, 5243.07959, 1),
        new mp.Vector3(-1632.46387, 5252.09033, 1),
        new mp.Vector3(-1631.18408, 5259.392, 1),
        new mp.Vector3(-1630.46924, 5265.892, 1),
        new mp.Vector3(-1626.76746, 5272.04, 1),
        new mp.Vector3(-1620.34644, 5274.37549, 1),
        new mp.Vector3(-1557.14758, 5208.227, 1),
        new mp.Vector3(-1563.04688, 5220.189, 1),
        new mp.Vector3(-1568.36829, 5231.07764, 1),
        new mp.Vector3(-1574.78772, 5243.10352, 1),
        new mp.Vector3(-1582.70959, 5255.66943, 1),
        new mp.Vector3(-1589.83887, 5266.48633, 1),
        new mp.Vector3(-1596.82422, 5276.86475, 1),
        new mp.Vector3(-1604.91443, 5279.06641, 1),
        new mp.Vector3(-1615.13367, 5276.706, 1),
        new mp.Vector3(-1606.866, 5273.48145, 1),
        new mp.Vector3(-1597.41919, 5266.467, 1),
        new mp.Vector3(-1589.87573, 5255.55566, 1),
        new mp.Vector3(-1582.78955, 5244.50537, 1),
        new mp.Vector3(-1570.58411, 5223.03174, 1),
        new mp.Vector3(-1564.14551, 5199.65234, 1),
        new mp.Vector3(-1568.63342, 5211.123, 1),
        new mp.Vector3(-1577.34082, 5233.52148, 1),
        new mp.Vector3(-1621.557, 5184.123, 1),
        new mp.Vector3(-1624.47632, 5193.55957, 1),
        new mp.Vector3(-1620.97644, 5207.924, 1),
        new mp.Vector3(-1619.4491, 5220.93457, 1),
        new mp.Vector3(-1627.89929, 5243.377, 1),
        new mp.Vector3(-1623.51453, 5235.19238, 1),
        new mp.Vector3(-1620.25879, 5229.205, 1),
    ],

    2: [
        new mp.Vector3(-1896.127, -1253.06873, 1),
        new mp.Vector3(-1887.4801, -1259.44934, 1),
        new mp.Vector3(-1875.536, -1267.64746, 1),
        new mp.Vector3(-1866.01489, -1274.68652, 1),
        new mp.Vector3(-1855.45435, -1282.65271, 1),
        new mp.Vector3(-1846.73291, -1289.01807, 1),
        new mp.Vector3(-1902.26379, -1246.762, 1),
        new mp.Vector3(-1912.41089, -1239.41382, 1),
        new mp.Vector3(-1912.109, -1195.8606, 1),
        new mp.Vector3(-1905.0011, -1185.94348, 1),
        new mp.Vector3(-1897.24377, -1177.294, 1),
        new mp.Vector3(-1888.31189, -1167.29138, 1),
        new mp.Vector3(-1878.21924, -1155.05322, 1),
        new mp.Vector3(-1869.4657, -1144.68115, 1),
        new mp.Vector3(-1860.98169, -1135.40393, 1),
        new mp.Vector3(-1846.90686, -1131.80237, 1),
        new mp.Vector3(-1832.578, -1133.19788, 1),
        new mp.Vector3(-1819.98254, -1128.61292, 1),
        new mp.Vector3(-1809.337, -1121.43665, 1),
        new mp.Vector3(-1798.21411, -1111.03052, 1),
        new mp.Vector3(-1788.56885, -1101.022, 1),
        new mp.Vector3(-1775.72473, -1089.5531, 1),
        new mp.Vector3(-1766.65784, -1080.22156, 1),
        new mp.Vector3(-1756.35364, -1067.90833, 1),
        new mp.Vector3(-1747.40332, -1062.33984, 1),
        new mp.Vector3(-1832.887, -1298.1322, 1),
        new mp.Vector3(-1821.12854, -1295.94275, 1),
        new mp.Vector3(-1808.39111, -1291.01514, 1),
        new mp.Vector3(-1798.75562, -1280.2428, 1),
        new mp.Vector3(-1788.88733, -1267.70886, 1),
        new mp.Vector3(-1782.954, -1260.99255, 1),
        new mp.Vector3(-1773.35876, -1248.592, 1),
        new mp.Vector3(-1766.71094, -1236.93188, 1),
        new mp.Vector3(-1761.59668, -1224.69263, 1),
        new mp.Vector3(-1759.7887, -1208.63391, 1),
        new mp.Vector3(-1757.59839, -1196.05652, 1),
        new mp.Vector3(-1749.36279, -1184.32251, 1),
        new mp.Vector3(-1741.8429, -1174.10938, 1),
        new mp.Vector3(-1734.73669, -1164.355, 1),
        new mp.Vector3(-1736.35449, -1185.00989, 1),
        new mp.Vector3(-1771.586, -1224.74658, 1),
        new mp.Vector3(-1909.86523, -1222.106, 1),
        new mp.Vector3(-1750.291, -1080.74133, 1),
        new mp.Vector3(-1760.80688, -1091.61, 1),
        new mp.Vector3(-1782.64612, -1112.09961, 1),
        new mp.Vector3(-1802.36023, -1131.56836, 1),
        new mp.Vector3(-1835.27478, -1142.31836, 1),
        new mp.Vector3(-1856.636, -1152.62915, 1),
        new mp.Vector3(-1874.89246, -1171.75232, 1),
        new mp.Vector3(-1890.08936, -1188.839, 1),
        new mp.Vector3(-1893.82129, -1205.5791, 1),
        new mp.Vector3(-1894.164, -1228.74939, 1),
        new mp.Vector3(-1882.078, -1247.47644, 1),
        new mp.Vector3(-1860.413, -1264.94238, 1),
        new mp.Vector3(-1845.28162, -1276.13867, 1),
        new mp.Vector3(-1826.9082, -1283.23938, 1),
        new mp.Vector3(-1809.08508, -1274.32056, 1),
        new mp.Vector3(-1795.3197, -1261.49658, 1),
        new mp.Vector3(-1782.69629, -1242.02991, 1),
        new mp.Vector3(-1772.61121, -1213.73853, 1),
        new mp.Vector3(-1764.94873, -1191.651, 1),
        new mp.Vector3(-1755.69067, -1175.8125, 1),
        new mp.Vector3(-1745.15283, -1158.47668, 1),
    ],

    3: [
        new mp.Vector3(-3429.98364, 1010.81995, 1),
        new mp.Vector3(-3420.49048, 1010.92432, 1),
        new mp.Vector3(-3413.10352, 1009.17712, 1),
        new mp.Vector3(-3404.61035, 1007.18665, 1),
        new mp.Vector3(-3397.75537, 1002.80164, 1),
        new mp.Vector3(-3392.07251, 997.4169, 1),
        new mp.Vector3(-3388.7356, 991.748657, 1),
        new mp.Vector3(-3380.29736, 990.355, 1),
        new mp.Vector3(-3370.607, 989.8715, 1),
        new mp.Vector3(-3360.9248, 989.572632, 1),
        new mp.Vector3(-3349.76636, 989.298157, 1),
        new mp.Vector3(-3338.60278, 989.0236, 1),
        new mp.Vector3(-3327.412, 988.7451, 1),
        new mp.Vector3(-3321.521, 986.5736, 1),
        new mp.Vector3(-3316.40283, 946.1718, 1),
        new mp.Vector3(-3329.14087, 945.7019, 1),
        new mp.Vector3(-3341.94385, 945.5594, 1),
        new mp.Vector3(-3357.643, 945.462341, 1),
        new mp.Vector3(-3371.64624, 945.625732, 1),
        new mp.Vector3(-3383.72217, 945.3203, 1),
        new mp.Vector3(-3396.524, 944.985168, 1),
        new mp.Vector3(-3396.60547, 935.6382, 1),
        new mp.Vector3(-3402.95459, 929.431946, 1),
        new mp.Vector3(-3412.659, 926.7553, 1),
        new mp.Vector3(-3422.736, 926.1737, 1),
        new mp.Vector3(-3432.10474, 927.1818, 1),
        new mp.Vector3(-3444.201, 932.2009, 1),
        new mp.Vector3(-3451.9104, 942.0643, 1),
        new mp.Vector3(-3455.19214, 952.47406, 1),
        new mp.Vector3(-3457.08057, 964.5667, 1),
        new mp.Vector3(-3383.582, 938.638855, 1),
        new mp.Vector3(-3322.153, 938.7808, 1),
        new mp.Vector3(-3333.51, 938.3743, 1),
        new mp.Vector3(-3347.13232, 938.1057, 1),
        new mp.Vector3(-3360.74219, 937.787231, 1),
        new mp.Vector3(-3373.99634, 937.012756, 1),
        new mp.Vector3(-3322.37622, 993.7616, 1),
        new mp.Vector3(-3332.88574, 993.79425, 1),
        new mp.Vector3(-3343.178, 993.903931, 1),
        new mp.Vector3(-3354.81665, 994.148254, 1),
        new mp.Vector3(-3366.65674, 994.404541, 1),
        new mp.Vector3(-3383.56372, 997.122, 1),
        new mp.Vector3(-3455.72925, 974.4683, 1),
        new mp.Vector3(-3452.44067, 985.5857, 1),
        new mp.Vector3(-3449.5144, 994.7911, 1),
        new mp.Vector3(-3443.1748, 1002.00812, 1),
        new mp.Vector3(-3433.14087, 1003.16406, 1),
        new mp.Vector3(-3420.276, 1003.41888, 1),
        new mp.Vector3(-3407.02954, 999.8604, 1),
        new mp.Vector3(-3397.20142, 993.702454, 1),
    ],
};

export const corruptionBoatConfig = {
    spawnAfterEnd: new mp.Vector3(-2038.870, -1034.175, 2.585), // Куда телепортировать игрока после окончания игры
    spawnAfterEndRotation: -68.0315,

    fadeTime: 0,
    completeTime: 10, // in minutes
    failCooldown: [5, 'minutes'],

    enginePartsModel: 'mj_gr_prop_gr_rsply_crate03a',
    engineBlipId: 402,
    enginePartsBlipId: 408,
    engineParts: { position: new mp.Vector3(-2069.6572, -1019.4462, 3.0615) },
    enginesPoints: [
        { position: new mp.Vector3(-2032.6549, -1029.0198, 2.5560) },
        { position: new mp.Vector3(-2040.5143, -1026.5143, 2.5560) },
        { position: new mp.Vector3(-2042.2814, -1030.5494, 2.5560) },
        { position: new mp.Vector3(-2034.4484, -1033.0681, 2.5560) },
        { position: new mp.Vector3(-2034.9099, -1035.9165, 2.5560) },
        { position: new mp.Vector3(-2042.5978, -1033.4901, 2.5560) },
        { position: new mp.Vector3(-2044.2329, -1037.4462, 2.5560) },
        { position: new mp.Vector3(-2036.3868, -1039.9780, 2.5729) },
    ],

    enginePressureChance: 5,
    engineWorkTime: 100
}

export const sunkenPastConfig = {
    // Куда телепортируем игрока
    teleportTo: [
        [
            { position: new mp.Vector3(4504.5493, 3463.5166, 4.8813), rotation: 62.3622 },
            { position: new mp.Vector3(4325.5254, 3341.3406, 3.3817), rotation: 8.5039 },
            { position: new mp.Vector3(4398.4878, 3703.0945, 3.0110), rotation: 153.0709 },
        ],
        [
            { position: new mp.Vector3(-3115.4504, -650.0967, 3.6007), rotation: -76.5354 },
            { position: new mp.Vector3(-2653.0813, -939.3231, 3.1626), rotation: -107.7165 },
            { position: new mp.Vector3(-3105.5078, -327.4550, 2.9099), rotation: 14.1732 },
        ],
        [
            { position: new mp.Vector3(-311.6703, -3032.3076, 2.4886), rotation: -25.5118 },
            { position: new mp.Vector3(-145.8989, -3112.0879, 3.0784), rotation: 0 },
            { position: new mp.Vector3(76.9187, -2994.3955, 3.0784), rotation: 51.0236 },
        ],
        [
            { position: new mp.Vector3(1991.3539, -3307.9253, 4.3253), rotation: 28.3465 },
            { position: new mp.Vector3(1657.4769, -3348.0264, 4.4432), rotation: -22.6772 },
            { position: new mp.Vector3(2199.5210, -3064.0615, 4.1567), rotation: 73.7008 },
        ],
        [
            { position: new mp.Vector3(3354.4351, -618.1583, 2.3033), rotation: 19.8425 },
            { position: new mp.Vector3(3582.1714, -429.8769, 2.4550), rotation: 79.3701 },
            { position: new mp.Vector3(2954.3472, 125.6044, 3.6512), rotation: -164.4095 },

        ],
        [
            { position: new mp.Vector3(-545.3406, 6915.4155, 5.1846), rotation: 116.2205 },
            { position: new mp.Vector3(-1040.6241, 7202.7559, 5.7069), rotation: -170.0787 },
            { position: new mp.Vector3(-1329.4550, 6330.0791, 4.6454), rotation: -59.5276 },
        ]
    ],
    vehicleToSpawn: ['submersible', 'submersible', 'submersible2', 'submersible2', 'submersible2', 'submersible2'], // Модель транспорта, которую спавним для прохождения квеста
    completeTime: 15, // Время на выполнение квеста в минутах
    failCooldown: [5, 'minutes'], // Кд при провале задания
    // Зона поиска
    findZone: [
        {
            position: new mp.Vector3(4325.2881, 3550.1143, 2.0505),
            radius: 200,
            centerBlipId: 744
        },
        {
            position: new mp.Vector3(-3005.7759, -570.5802, 2.6403),
            radius: 220,
            centerBlipId: 744
        },
        {
            position: new mp.Vector3(-184.8396, -2951.6572, 3.1458),
            radius: 150,
            centerBlipId: 744
        },
        {
            position: new mp.Vector3(1896.9758, -3134.1758, 2.1516),
            radius: 250,
            centerBlipId: 744
        },
        {
            position: new mp.Vector3(3305.0769, -351.4550, 1.4103),
            radius: 430,
            centerBlipId: 744
        },
        {
            position: new mp.Vector3(-893.6703, 6800.1626, 2),
            radius: 450,
            centerBlipId: 744
        }
    ],
    // Позиции обломков
    sunkenPositions: [
        [
            new mp.Vector3(4240.7207, 3592.9583, -44.0337),
            new mp.Vector3(4188.1846, 3574.9055, -49.9312),
            new mp.Vector3(4211.9077, 3646.3911, -38.7428),
            new mp.Vector3(4213.9121, 3603.4548, -40.8491),
            new mp.Vector3(4244.5845, 3613.8726, -45.4996),
            new mp.Vector3(4132.7603, 3532.4175, -24.5216),
            new mp.Vector3(4158.6328, 3504.2109, -28.4982)
        ],
        [
            new mp.Vector3(-2834.9275, -482.2549, -11.1091),
            new mp.Vector3(-2830.9978, -578.3736, -50.6556),
            new mp.Vector3(-2820.6199, -597.8901, -58.2887),
            new mp.Vector3(-2840.0703, -420.0923, -10.7384),
            new mp.Vector3(-2819.3274, -485.8945, -41.8770),
            new mp.Vector3(-2863.4373, -513.6923, -63.7649),
            new mp.Vector3(-2846.9407, -626.6374, -77.3458),
            new mp.Vector3(-2762.0571, -614.4923, -40.5963),
            new mp.Vector3(-2879.2351, -561.6791, -91.7018)
        ],
        [
            new mp.Vector3(-107.5253, -2872.7473, -4.7568),
            new mp.Vector3(-132.6593, -2865.3230, -7.7898),
            new mp.Vector3(-157.8330, -2857.7671, -5.6329),
            new mp.Vector3(-188.0176, -2882.6770, -15.3048),
            new mp.Vector3(-176.5187, -2856.2900, -9.1040),
            new mp.Vector3(-194.4659, -2850.7913, -13.8557),
            new mp.Vector3(-232.9582, -2869.0945, -16.8381),
            new mp.Vector3(-242.0176, -2858.9670, -18.2366),
            new mp.Vector3(-272.6242, -2890.4438, -16.1978),
            new mp.Vector3(-256.7604, -2892.1187, -18.4894),
            new mp.Vector3(-108.9494, -2870.0835, -19.2982),
            new mp.Vector3(-122.1890, -2873.4460, -13.4008)
        ],
        [
            new mp.Vector3(1753.5165, -3138.8835, -76.3854),
            new mp.Vector3(1680.4352, -3092.4395, -66.9158),
            new mp.Vector3(1713.6263, -3026.4131, -56.1824),
            new mp.Vector3(1753.2527, -3018.6331, -51.7678),
            new mp.Vector3(1728.3429, -2992.2197, -51.7172),
            new mp.Vector3(1757.0505, -2993.4460, -27.1333),
            new mp.Vector3(1878.5538, -2931.1780, -36.9063),
            new mp.Vector3(1865.8945, -2936.7957, -45.2637),
            new mp.Vector3(1836.3693, -2962.8000, -45.8367),
            new mp.Vector3(1819.4506, -2917.2131, -27.0828),
            new mp.Vector3(1827.1121, -2919.0330, -35.4066)
        ],
        [
            new mp.Vector3(3215.1033, -407.9077, -29.0542),
            new mp.Vector3(3219.6265, -402.0264, -30.9077),
            new mp.Vector3(3189.7451, -395.1429, -23.0388),
            new mp.Vector3(3199.2659, -388.5363, -6.0879),
            new mp.Vector3(3198.2900, -383.8813, -29.0374),
            new mp.Vector3(3185.7759, -356.8879, -10.2836),
            new mp.Vector3(3176.0967, -317.3802, -18.9106),
            new mp.Vector3(3157.7803, -299.3670, -14.7656),
            new mp.Vector3(3174.5540, -314.4659, -9.8286), 
            new mp.Vector3(3171.0330, -250.6286, -13.9736), 
            new mp.Vector3(3154.5627, -254.7692, -22.0278), 
            new mp.Vector3(3154.9055, -269.7890, -6.8967),
            new mp.Vector3(3125.5122, -229.4242, -13.6366), 
            new mp.Vector3(2994.5276, -85.3451, -10.2498), 
            new mp.Vector3(3131.0110, -431.0769, -35.2380)
        ],
        [
            new mp.Vector3(-1036.1670, 6736.3384, -91.3143),
            new mp.Vector3(-997.8198, 6700.1143, -41.1018),
            new mp.Vector3(-991.5692, 6704.2285, -36.0806),
            new mp.Vector3(-943.8593, 6690.5405, -30.9919),
            new mp.Vector3(-915.8110, 6674.9800, -29.1721),
            new mp.Vector3(-898.7604, 6649.0815, -28.7847),
            new mp.Vector3(-925.1208, 6589.6880, -29.0710),
            new mp.Vector3(-917.7890, 6625.1211, -27.9253),
            new mp.Vector3(-832.5363, 6652.8394, -21.4550),
            new mp.Vector3(-808.5099, 6669.7979, -11.8000),
            new mp.Vector3(-843.6923, 6667.5693, -23.9656),
            new mp.Vector3(-848.4000, 6582.9624, -29.1216),
            new mp.Vector3(-1029.6132, 6506.4922, -24.7238),
            new mp.Vector3(-994.0352, 6366.5142, -14.7488),
            new mp.Vector3(-991.6879, 6398.5054, -17.5626)
        ]
    ],

    spawnAfterEnd: new mp.Vector3(2933.5649, 418.9714, 2.8257), // Куда телепортировать игрока после окончания игры 
    spawnAfterEndRotation: 79.3701,

    fadeTime: 0, // Скорость затемнения
}

export const killerWhaleVisionConfig = {
    // Куда телепортируем игрока
    teleportTo: [
        [
            { position: new mp.Vector3(562.6550, 7032.7251, -14.6307), rotation: -170.0787 },
            { position: new mp.Vector3(502.7473, 6944.6240, -5.5150), rotation: -104.8819 },
            { position: new mp.Vector3(653.5516, 7054.5229, -3.5773), rotation: 158.7402 },
        ],
        [
            { position: new mp.Vector3(-3340.0615, 420.9495, -7.8403), rotation: -82.2047 },
            { position: new mp.Vector3(-3312.2769, 503.2747, -7.8403), rotation: -124.7244 },
            { position: new mp.Vector3(-3261.7056, 545.1429, -7.8403), rotation: -167.2441 },
        ],
        [
            { position: new mp.Vector3(3555.9956, 5166.9229, -6.2733), rotation: 104.8819 },
            { position: new mp.Vector3(3560.8484, 5082.8174, -6.0879), rotation: 127.5591 },
            { position: new mp.Vector3(3470.7166, 5040.8438, -2.7854), rotation: 62.3622 },
        ],
    ],

    spawnAfterEnd: new mp.Vector3(3614.4263, 5024.7031, 11.3348, -99.2126), // Куда телепортировать игрока после окончания игры
    spawnAfterEndRotation: -147.76049,

    startFishAmount: 4, // Количество рыбок, которое спавним при старте квеста
    maxFishAmount: { min: 40, max: 60 }, // Максимальное количество рыб

    fadeTime: 0,

    completeTime: 15, // Время на выполнение квеста в минутах
    failCooldown: [5, 'minutes'], // Кд при провале задания
    skins: ['a_c_killerwhale', 'a_c_sharkhammer', 'a_c_sharktiger'], // Скины, которые выдаем игроку в зависимости от стадии квеста
    fishModel: 'a_c_fish', // Модель педа рыбок
    fishBlipId: 51,
    fishBlips: [
        {
            position: new mp.Vector3(642.0659, 7028.0571, -4.8242),
            radius: 200,
        },
        {
            position: new mp.Vector3(-3241.1736, 444.6725, -5.5992),
            radius: 200,
        },
        {
            position: new mp.Vector3(3437.8682, 5117.0640, 0.0454),
            radius: 200,
        },
    ],

    // Маршруты рыбок
    fishRoutes: [
        [
            {
                position: new mp.Vector3(633.8638, 7045.9780, -5.9363),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(623.5253, 7036.5493, -5.7172),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(623.1824, 7014.5669, -5.4308),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(623.4857, 6999.5869, -4.8916),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(626.4132, 6985.8989, -4.9758),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(634.1539, 6970.7603, -4.4535),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(646.2198, 6955.0947, -3.7627),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(651.4022, 6971.2617, -3.7795),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(651.4022, 6971.2617, -3.7795),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(654.9099, 7016.9668, -4.2175),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(659.6572, 7035.0332, -4.6388),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(661.3714, 7051.9121, -5.0938),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(655.2527, 7063.3716, -4.4872),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(632.7956, 7059.0332, -6.1553),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(629.5912, 7045.4111, -5.7847),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(635.8549, 7032.9097, -5.4476),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(642.9626, 7019.7627, -5.1443),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(603.4813, 6978.3164, -5.6161),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(612.9758, 6968.0439, -5.3802),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(624.5143, 6954.3823, -4.3693),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(614.8748, 6937.1211, -5.5992),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(600.7516, 6948.8833, -6.3912),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(585.4154, 6973.3716, -5.9700),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(566.4659, 6972.5537, -5.6666),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(545.5385, 6974.0044, -6.4586),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(535.6879, 6969.3232, -5.9700),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(528.6857, 6954.4219, -5.7510),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(542.3604, 6946.1011, -5.8521),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(559.4110, 6941.4727, -6.0374),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(586.2989, 6936.0264, -6.3575),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(585.8242, 6917.3804, -6.3912),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(548.1758, 6924.1978, -5.7678),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(521.8682, 6936.1582, -5.8521),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(506.3736, 6950.3340, -5.3466),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(502.5890, 6964.8130, -4.4366),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(518.9934, 6989.8813, -4.4198),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(599.5385, 6902.7031, -5.6329),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
        ],
        [
            {
                position: new mp.Vector3(-3253.3054, 440.3209, -5.9194),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3247.1736, 452.9143, -5.5150),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3241.9517, 462.5802, -5.1106),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3237.1516, 471.3495, -4.7230),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3225.6133, 479.1824, -3.7457),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3210.5276, 484.1143, -2.2968),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3199.1340, 487.7934, -1.5553),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3187.1604, 488.4264, -1.0330),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3175.9121, 484.8264, 0.1633),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3179.3142, 469.9253, -0.1567),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3188.0044, 455.3407, -0.7971),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3194.8879, 443.1560, -1.0498),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3201.3230, 430.7209, -0.9656),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3208.5891, 418.8132, -0.9993),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3217.5825, 428.1890, -1.4542),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3221.4592, 445.3714, -2.9539),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3215.9604, 458.2418, -2.5494),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3211.7803, 479.1165, -3.3077),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3229.6484, 425.4462, -5.1948),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3237.3494, 410.4000, -6.5428),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3241.0549, 395.9736, -5.2454),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3249.2307, 380.5978, -3.4930),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3264.7253, 377.8681, -3.0212),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3267.4153, 405.2571, -6.8967),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3291.2175, 424.6813, -10.2667),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3284.1494, 455.6044, -9.1545),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3277.5034, 469.0417, -8.8682),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3263.7627, 487.3187, -9.7274),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3251.4990, 450.4220, -9.8455),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3238.9055, 464.3077, -6.2395),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3242.8352, 495.7846, -10.0139),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3214.1670, 483.3758, -5.2117),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3250.7473, 466.2462, -8.5985),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3250.8660, 389.1297, -4.4198),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(-3255.3889, 431.7758, -7.7391),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
        ],
        [
            {
                position: new mp.Vector3(3461.8945, 5186.8877, -1.7238),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3466.2197, 5173.6089, -1.1846),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3467.9077, 5149.7935, -2.0776),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3457.7539, 5140.2725, -3.5436),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3442.7341, 5134.7734, -3.9312),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3423.8110, 5133.2969, -4.4030),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3410.6902, 5128.6021, -3.1055),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3397.7012, 5128.6548, -2.6842),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3387.8242, 5148.5273, -1.2351),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3377.1033, 5155.8462, -1.0667),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3370.2988, 5139.8638, -1.6901),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3363.3757, 5124.4219, -1.5216),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3362.4790, 5112.8042, -1.0667),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3365.6309, 5098.1934, -1.0498),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3368.0176, 5080.4966, -2.4147),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3385.5693, 5073.3101, -3.3751),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3400.8792, 5065.9912, -3.7795),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3417.7583, 5066.8218, -3.0212),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3431.5254, 5068.7075, -2.2292),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3445.0154, 5072.1758, -1.1172),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3430.9714, 5098.4966, -0.4432),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3422.0044, 5116.8657, -0.8308),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3409.4768, 5110.9980, -0.1399),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3292.6418, 5114.7295, -1.7576),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3331.8594, 5093.9868, -0.8477),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
            {
                position: new mp.Vector3(3342.0527, 5112.1450, -1.7238),
                radius: 20,
                minimalLength: 15,
                timeBetweenWalks: 15
            },
        ],
    ]

}

export const modernBirdConfig = {
    timer: 25 * 60 * 1000,
    vehicle: {
        model: 'dodo',
        position: new mp.Vector3(1318.0747, 4215.6924, 31.4535),
        heading: 171.8944
    },
    types: {
        1: {
            birdModel: 'a_c_seagull'
        },
        2: {
            birdModel: 'a_c_cormorant'
        }
    },
    minCount: 40,
    maxCount: 60,
    birdsPositions: [
        new mp.Vector3(1438.2329, 4027.7935, 35),
        new mp.Vector3(-3358.1011, 10.8923, 5.1172),
        new mp.Vector3(-3304.6155, 305.0374, 5.1172),
        new mp.Vector3(-3303.0593, 599.8154, 5.1172),
        new mp.Vector3(-3508.7473, 700.7341, 5.1172),
        new mp.Vector3(-3520.7209, 1012.4440, 5.1172),
        new mp.Vector3(-3361.3977, 1239.2175, 5.1172),
        new mp.Vector3(-3236.4658, 1519.0154, 5.1172),
        new mp.Vector3(-3290.2153, 1798.4967, 5.1172),
        new mp.Vector3(-3207.2834, 2070.5803, 5.1172),
        new mp.Vector3(-2989.4373, 2350.2856, 5.1172),
        new mp.Vector3(-2862.9231, 2618.3472, 5.1172),
        new mp.Vector3(-2895.1252, 2871.0725, 5.1172),
        new mp.Vector3(-3107.3406, 3081.7715, 5.1172),
        new mp.Vector3(-3280.2065, 3331.6880, 5.1172),
        new mp.Vector3(-3147.6265, 3575.2219, 5.1172),
        new mp.Vector3(-2913.7979, 3656.4526, 5.1172),
        new mp.Vector3(-2717.5122, 3816.9758, 5.1172),
        new mp.Vector3(-2610.3691, 4233.5078, 5.1172),
        new mp.Vector3(-2407.3450, 4486.9976, 5.1172),
        new mp.Vector3(-2162.7429, 4681.9385, 5.1172),
        new mp.Vector3(-2065.7275, 4946.5845, 5.1172),
        new mp.Vector3(-1826.1099, 5136.5537, 5.1172),
        new mp.Vector3(-1613.3011, 5334.4746, 5.1172),
        new mp.Vector3(-1314.8572, 5536.2593, 5.1172),
        new mp.Vector3(-1063.7010, 5720.9932, 5.1172),
        new mp.Vector3(-1035.7186, 6062.5449, 5.1172),
        new mp.Vector3(-805.9385, 6219.3232, 5.1172),
        new mp.Vector3(-655.8857, 6527.2749, 5.1172),
        new mp.Vector3(-335.3143, 6716.2417, 5.1172),
        new mp.Vector3(-121.2791, 6969.5078, 5.1172),
        new mp.Vector3(-11.2484, 7232.0308, 5.1172),
        new mp.Vector3(137.2352, 7529.6045, 5.1172),
        new mp.Vector3(332.7429, 7171.1475, 5.1172),
        new mp.Vector3(577.3715, 7638.6592, 5.1172),
        new mp.Vector3(224.9934, 7702.4438, 5.1172),
        new mp.Vector3(-101.1956, 7643.4595, 5.1172),
        new mp.Vector3(-207.6264, 7462.1802, 5.1172),
        new mp.Vector3(-384.1978, 7179.7715, 5.1172),
        new mp.Vector3(-531.6263, 6922.2461, 5.1172),
        new mp.Vector3(-703.1605, 6710.9536, 5.1172),
        new mp.Vector3(-901.1473, 6572.4790, 5.1172),
        new mp.Vector3(-1170.4220, 6424.5889, 5.1172),
        new mp.Vector3(-1201.2000, 6166.8394, 5.1172),
        new mp.Vector3(-1319.8813, 6058.8262, 5.1172),
        new mp.Vector3(-1773.9956, 5762.7427, 5.1172),
        new mp.Vector3(-1919.4725, 5565.4551, 5.1172),
        new mp.Vector3(-1929.3627, 5330.1230, 5.1172),
        new mp.Vector3(-2293.6880, 5061.2046, 5.1172),
        new mp.Vector3(-2482.9714, 4763.8682, 5.1172),
        new mp.Vector3(-2622.5144, 4501.2793, 5.1172),
        new mp.Vector3(-2746.9055, 4217.3276, 5.1172),
        new mp.Vector3(-2974.8923, 4031.1033, 5.1172),
        new mp.Vector3(-3329.1956, 3930.5540, 5.1172),
        new mp.Vector3(-3627.2966, 3615.4021, 5.1172),
        new mp.Vector3(-3514.8923, 3232.2065, 5.1172),
        new mp.Vector3(-3309.4153, 2888.0044, 5.1172),
        new mp.Vector3(-3117.5869, 2584.2988, 5.1172),
        new mp.Vector3(-3375.9165, 2378.3076, 5.1172),
        new mp.Vector3(-3423.3625, 2079.3098, 5.1172),
        new mp.Vector3(-3426.8704, 1720.8264, 5.1172),
        new mp.Vector3(-3485.5649, 1389.0989, 5.1172),
        new mp.Vector3(-3730.8660, 1170.6329, 5.1172),
        new mp.Vector3(-3599.9209, 870.0132, 5.1172),
        new mp.Vector3(-3459.1252, 525.7319, 5.1172),
        new mp.Vector3(-3323.3933, 160.1670, 5.1172),
        new mp.Vector3(-3398.2944, -114.0396, 5.1172),
        new mp.Vector3(-3087.2307, -274.2198, 5.1172),
        new mp.Vector3(-3728.7297, -485.7626, 5.1172),
        new mp.Vector3(-3845.7363, -108.9494, 5.1172),
        new mp.Vector3(-3917.1824, 199.0022, 5.1172),
        new mp.Vector3(-3963.7715, 559.6616, 5.1172),
        new mp.Vector3(-3971.1560, 890.3737, 5.1172),
        new mp.Vector3(-3934.9187, 1209.2968, 5.1172),
        new mp.Vector3(-3873.0461, 1499.0505, 5.1172),
        new mp.Vector3(-3805.5166, 1798.2065, 5.1172),
        new mp.Vector3(-3775.7407, 2215.2000, 5.1172),
        new mp.Vector3(-3738.8176, 2495.4724, 5.1172),
        new mp.Vector3(-3687.0198, 2790.7385, 5.1172),
        new mp.Vector3(-3598.6682, 3116.9934, 5.1172),
        new mp.Vector3(-3485.7100, 3383.1692, 5.1172),
        new mp.Vector3(-3314.2681, 3655.2527, 5.1172),
        new mp.Vector3(-3128.1099, 3817.6484, 5.1172),
        new mp.Vector3(-3097.7671, 4044.8835, 5.1172),
        new mp.Vector3(-3005.7231, 4281.3760, 5.1172),
        new mp.Vector3(-2830.6155, 4594.6812, 5.1172),
        new mp.Vector3(-2637.7979, 4897.1606, 5.1172),
        new mp.Vector3(-2436.4746, 5187.0068, 5.1172),
        new mp.Vector3(-2178.0132, 5453.0112, 5.1172),
        new mp.Vector3(-1576.0879, 5863.3584, 5.1172),
        new mp.Vector3(-1538.7825, 6580.1538, 5.1172),
        new mp.Vector3(-1272.8308, 6913.6089, 5.1172),
        new mp.Vector3(-1008.6330, 7285.5430, 5.1172),
        new mp.Vector3(-719.1693, 7493.6309, 5.1172),
        new mp.Vector3(-339.1648, 7775.7495, 5.1172),
        new mp.Vector3(41.6044, 7957.6748, 5.1172),
        new mp.Vector3(413.7231, 7925.5386, 5.1172),
        new mp.Vector3(875.3538, 7765.9517, 5.1172),
        new mp.Vector3(1144.0615, 7524.0659, 5.1172),
        new mp.Vector3(1494.7913, 7512.9097, 5.1172),
        new mp.Vector3(2245.6089, 7075.4375, 5.1172),
        new mp.Vector3(2708.9407, 6832.5493, 5.1172),
        new mp.Vector3(3057.9429, 6610.3647, 5.1172),
        new mp.Vector3(3529.4373, 6102.3955, 5.1172),
        new mp.Vector3(3557.2351, 5769.8110, 5.1172),
        new mp.Vector3(3548.1626, 5417.5913, 5.1172),
        new mp.Vector3(3542.1758, 5182.4043, 5.1172),
        new mp.Vector3(3582.0000, 4907.5649, 5.1172),
        new mp.Vector3(3705.0989, 4753.0552, 5.1172),
        new mp.Vector3(3824.6375, 4618.9712, 5.1172),
        new mp.Vector3(4014.6067, 4470.8833, 5.1172),
        new mp.Vector3(3951.8374, 4322.8877, 5.1172),
        new mp.Vector3(4031.2878, 4135.8989, 5.1172),
        new mp.Vector3(3988.9583, 3999.8770, 5.1172),
        new mp.Vector3(3893.4990, 3819.5210, 5.1172),
        new mp.Vector3(4033.5957, 3703.4109, 5.1172),
        new mp.Vector3(3982.3252, 3475.2527, 5.1172),
        new mp.Vector3(3968.9934, 3249.4812, 5.1172),
        new mp.Vector3(3847.8594, 3126.4482, 5.1172),
        new mp.Vector3(3737.2483, 2979.2571, 5.1172),
        new mp.Vector3(3650.0044, 2858.7957, 5.1172),
        new mp.Vector3(3527.6836, 2719.0681, 5.1172),
        new mp.Vector3(3408.0527, 2605.0549, 5.1172),
        new mp.Vector3(3371.4329, 2473.7144, 5.1172),
        new mp.Vector3(3344.3867, 2261.1296, 5.1172),
        new mp.Vector3(3265.3582, 2107.5693, 5.1172),
        new mp.Vector3(3193.0945, 1950.7781, 5.1172),
        new mp.Vector3(3091.1077, 1842.7517, 5.1172),
        new mp.Vector3(2979.7715, 1744.1538, 5.1172),
        new mp.Vector3(2991.5078, 1608.3165, 5.1172),
        new mp.Vector3(2967.1648, 1449.0857, 5.1172),
        new mp.Vector3(2865.1384, 1276.3385, 5.1172),
        new mp.Vector3(2843.1692, 1098.0396, 5.1172),
        new mp.Vector3(2865.2439, 891.4813, 5.1172),
        new mp.Vector3(2976.1318, 959.5912, 5.1172),
        new mp.Vector3(3027.2834, 788.8615, 5.1172),
        new mp.Vector3(3000.6199, 661.5033, 5.1172),
        new mp.Vector3(3062.4922, 547.9517, 5.1172),
        new mp.Vector3(3017.7627, 446.3868, 5.1172),
        new mp.Vector3(2951.9604, 252.9494, 5.1172),
        new mp.Vector3(2863.6221, 125.8681, 5.1172),
        new mp.Vector3(2906.6902, -28.5758, 5.1172),
        new mp.Vector3(3009.8901, -117.6923, 5.1172),
        new mp.Vector3(2985.4021, -238.9978, 5.1172),
        new mp.Vector3(2985.9561, -374.0703, 5.1172),
        new mp.Vector3(2928.9758, -564.5670, 5.1172),
        new mp.Vector3(2915.2351, -718.8792, 5.1172),
        new mp.Vector3(2845.0681, -863.8417, 5.1172),
        new mp.Vector3(2754.1450, -996.8439, 5.1172),
        new mp.Vector3(2725.9253, -1135.0286, 5.1172),
        new mp.Vector3(2639.1165, -1305.5868, 5.1172),
        new mp.Vector3(2681.9077, -1523.2087, 5.1172),
        new mp.Vector3(2730.6331, -1721.3671, 5.1172),
        new mp.Vector3(2716.3252, -1912.1670, 5.1172),
        new mp.Vector3(2650.9055, -2112.2109, 5.1172),
        new mp.Vector3(2516.5583, -2201.0637, 5.1172),
        new mp.Vector3(2356.1274, -2190.2505, 5.1172),
        new mp.Vector3(2260.7341, -2327.6968, 5.1172),
        new mp.Vector3(2161.1340, -2477.2483, 5.1172),
        new mp.Vector3(2040.5011, -2618.0571, 5.1172),
        new mp.Vector3(1886.3341, -2730.0791, 5.1172),
        new mp.Vector3(1741.3846, -2823.0857, 5.1172),
        new mp.Vector3(2068.3252, -2927.0110, 5.1172),
        new mp.Vector3(2307.2834, -2712.7122, 5.1172),
        new mp.Vector3(2466.1582, -2523.1648, 5.1172),
        new mp.Vector3(2667.0725, -2389.3713, 5.1172),
        new mp.Vector3(2833.6616, -2251.7407, 5.1172),
        new mp.Vector3(2909.9077, -2030.2549, 5.1172),
        new mp.Vector3(2947.8989, -1793.9736, 5.1172),
        new mp.Vector3(2967.4417, -1609.0813, 5.1172),
        new mp.Vector3(2985.0198, -1368.0396, 5.1172),
        new mp.Vector3(3018.4614, -1185.6527, 5.1172),
        new mp.Vector3(3142.6418, -911.3275, 5.1172),
        new mp.Vector3(3312.3428, -377.8286, 5.1172),
        new mp.Vector3(3438.5276, -191.4989, 5.1172),
        new mp.Vector3(3346.9319, 49.7407, 5.1172),
        new mp.Vector3(3314.1362, 244.8791, 5.1172),
        new mp.Vector3(3321.2307, 514.2066, 5.1172),
        new mp.Vector3(3267.6265, 698.6374, 5.1172),
        new mp.Vector3(3241.2922, 913.5956, 5.1172),
        new mp.Vector3(3209.8418, 1183.5560, 5.1172),
        new mp.Vector3(3210.1450, 1426.7605, 5.1172),
        new mp.Vector3(3242.9539, 1622.2285, 5.1172),
        new mp.Vector3(3414.2109, 1827.3890, 5.1172),
        new mp.Vector3(3519.1516, 2037.0857, 5.1172),
        new mp.Vector3(3667.1077, 2353.5825, 5.1172),
        new mp.Vector3(3813.5342, 2567.4592, 5.1172),
        new mp.Vector3(3975.7979, 2837.6836, 5.1172),
        new mp.Vector3(4115.4067, 3105.0593, 5.1172),
        new mp.Vector3(4194.6328, 3437.4592, 5.1172),
        new mp.Vector3(4217.2881, 3690.4482, 5.1172),
        new mp.Vector3(4234.3911, 3980.9539, 5.1172),
        new mp.Vector3(4239.7451, 4513.6880, 5.1172),
        new mp.Vector3(4066.8000, 4762.5757, 5.1172),
        new mp.Vector3(3911.1824, 5030.0308, 5.1172),
        new mp.Vector3(3795.0857, 5274.9097, 5.1172),
        new mp.Vector3(3786.7253, 5601.4419, 5.1172),
        new mp.Vector3(3710.8879, 5862.8701, 5.1172),
        new mp.Vector3(3630.8308, 6123.1255, 5.1172),
        new mp.Vector3(3484.5627, 6372.4482, 5.1172),
        new mp.Vector3(3195.2307, 6573.6133, 5.1172),
        new mp.Vector3(2888.7297, 6761.0771, 5.1172),
        new mp.Vector3(2500.8660, 6897.5605, 5.1172),
        new mp.Vector3(2113.2131, 6943.1606, 5.1172),
        new mp.Vector3(2173.2791, 7205.6704, 5.1172),
        new mp.Vector3(2420.2021, 7138.1143, 5.1172),
        new mp.Vector3(2743.8066, 7021.9253, 5.1172),
        new mp.Vector3(3005.1033, 6893.1958, 5.1172),
        new mp.Vector3(3206.9143, 6785.4067, 5.1172),
        new mp.Vector3(3441.3230, 6612.6724, 5.1172),
        new mp.Vector3(3612.3955, 6433.3320, 5.1172),
        new mp.Vector3(3789.0461, 6158.1362, 5.1172),
        new mp.Vector3(3960.8572, 5806.4702, 5.1172),
        new mp.Vector3(4031.0637, 5556.2505, 5.1172),
        new mp.Vector3(4068.5010, 5324.5449, 5.1172),
        new mp.Vector3(4130.7559, 5099.3540, 5.1172),
        new mp.Vector3(4277.4727, 4827.5342, 5.1172),
        new mp.Vector3(4339.8857, 4578.1714, 5.1172),
        new mp.Vector3(4367.2749, 4300.6548, 5.1172),
        new mp.Vector3(4372.9316, 4090.5759, 5.1172),
        new mp.Vector3(4363.6616, 3861.6265, 5.1172),
        new mp.Vector3(4301.0771, 3622.6418, 5.1172),
        new mp.Vector3(4276.6152, 3280.9978, 5.1172),
        new mp.Vector3(4172.5713, 2977.7144, 5.1172),
        new mp.Vector3(3997.0549, 2629.0549, 5.1172),
        new mp.Vector3(3844.5891, 2270.7166, 5.1172),
        new mp.Vector3(3694.6946, 2021.0901, 5.1172),
        new mp.Vector3(3561.1516, 1701.7318, 5.1172),
        new mp.Vector3(3497.5518, 1514.0308, 5.1172),
        new mp.Vector3(3430.4834, 1288.5494, 5.1172),
        new mp.Vector3(3408.6331, 1017.0461, 5.1172),
        new mp.Vector3(3417.4153, 770.2945, 5.1172),
        new mp.Vector3(3514.5232, 445.0417, 5.1172),
        new mp.Vector3(3560.0439, 71.7231, 5.1172),
        new mp.Vector3(3456.5935, -298.4572, 5.1172),
        new mp.Vector3(3329.5254, -660.9099, 5.1172),
        new mp.Vector3(3332.1494, -1006.7868, 5.1172),
        new mp.Vector3(3263.0637, -1371.6791, 5.1172),
        new mp.Vector3(3172.4043, -1717.2000, 5.1172),
        new mp.Vector3(3021.4417, -2029.0945, 5.1172),
        new mp.Vector3(2797.9253, -2356.0088, 5.1172),
        new mp.Vector3(2553.9692, -2627.6836, 5.1172),
        new mp.Vector3(2278.1011, -2827.6353, 5.1172),
        new mp.Vector3(1942.5758, -3037.3186, 5.1172),
        new mp.Vector3(1738.0747, -3269.4724, 5.1172),
        new mp.Vector3(-3537.9429, -274.9319, 5.1172),
        new mp.Vector3(-3600.5276, -53.6176, 5.1172),
        new mp.Vector3(-3707.9341, 268.8528, 5.1172),
        new mp.Vector3(-3805.8066, 562.6418, 5.1172),
        new mp.Vector3(-3800.8879, 872.4264, 5.1172),
        new mp.Vector3(-3802.1143, 1142.3341, 5.1172),
        new mp.Vector3(-3698.5320, 1400.2285, 5.1172),
        new mp.Vector3(-3623.4990, 1659.4681, 5.1172),
        new mp.Vector3(-3531.6924, 1976.2814, 5.1172),
        new mp.Vector3(-3570.9363, 2145.0066, 5.1172),
        new mp.Vector3(-3563.8550, 2384.8879, 5.1172),
        new mp.Vector3(-3556.1934, 2644.7737, 5.1172),
        new mp.Vector3(-3505.1736, 2868.8440, 5.1172),
        new mp.Vector3(-3380.6638, 3061.0681, 5.1172),
        new mp.Vector3(-3111.8242, 2866.2065, 5.1172),
        new mp.Vector3(-3503.4856, 3798.8704, 5.1172),
        new mp.Vector3(-2912.7957, 3861.7847, 5.1172),
        new mp.Vector3(-2739.6660, 4045.6748, 5.1172),
        new mp.Vector3(-1746.7384, 5460.9624, 5.1172),
        new mp.Vector3(-1604.9670, 5648.3208, 5.1172),
        new mp.Vector3(-1381.6615, 5849.7231, 5.1172),
        new mp.Vector3(-1149.9561, 5926.9185, 5.1172),
        new mp.Vector3(-1062.7517, 5708.7427, 5.1172),
        new mp.Vector3(-1625.8945, 6098.6504, 5.1172),
        new mp.Vector3(-1432.8396, 6304.1802, 5.1172),
        new mp.Vector3(-1194.3561, 6557.2881, 5.1172),
        new mp.Vector3(-983.7626, 6822.1582, 5.1172),
        new mp.Vector3(-748.5231, 7103.1694, 5.1172),
        new mp.Vector3(-507.9824, 7379.6836, 5.1172),
        new mp.Vector3(-278.6505, 7641.0728, 5.1172),
        new mp.Vector3(491.1824, 7074.3691, 5.1172),
        new mp.Vector3(732.5011, 6864.8965, 5.1172),
        new mp.Vector3(963.3099, 6738.5142, 5.1172),
        new mp.Vector3(1229.9868, 6682.8789, 5.1172),
        new mp.Vector3(1454.4791, 6701.3408, 5.1172),
        new mp.Vector3(1649.4462, 6731.5781, 5.1172),
        new mp.Vector3(1933.3715, 6794.6504, 5.1172),
        new mp.Vector3(1862.3473, 7064.0571, 5.1172),
        new mp.Vector3(1650.2638, 7022.3340, 5.1172),
        new mp.Vector3(1378.5890, 6999.9824, 5.1172),
        new mp.Vector3(1088.2682, 6975.0728, 5.1172),
        new mp.Vector3(797.0637, 6984.0396, 5.1172),
        new mp.Vector3(1863.7847, 7354.7075, 5.1172),
        new mp.Vector3(3132.7649, -395.9604, 5.1172),
        new mp.Vector3(3067.5562, -688.8264, 5.1172),
        new mp.Vector3(2953.1077, -985.8329, 5.1172),
        new mp.Vector3(2830.0088, -1197.4550, 5.1172),
        new mp.Vector3(2649.7319, -1460.0176, 5.1172),
        new mp.Vector3(3182.0967, -1097.1560, 5.1172),
        new mp.Vector3(3059.1692, -1360.8132, 5.1172),
        new mp.Vector3(2897.7100, -1716.3165, 5.1172),
        new mp.Vector3(2672.5583, -2264.7561, 5.1172),
        new mp.Vector3(2446.6287, -2382.4482, 5.1172),
        new mp.Vector3(2294.3999, -2496.6594, 5.1172),
        new mp.Vector3(2137.0417, -2673.2834, 5.1172),
        new mp.Vector3(1966.8792, -2862.7781, 5.1172),
        new mp.Vector3(1663.6088, -3013.3186, 5.1172),
        new mp.Vector3(1466.5582, -3174.5935, 5.1172),
        new mp.Vector3(1359.1384, -2943.5605, 5.1172),
        new mp.Vector3(2022.4879, -3227.0637, 5.1172),
    ]
}

export const stingrayArticleConfig = {
    timer: 15 * 60 * 1000,
    checkDuration: 5000,
    types: {
        1: {
            model: 'a_c_stingray',
            playerSpawnPoint: new mp.Vector3(92.3868, 7116.7383, 26.3817),
            center: new mp.Vector3(171.0330, 7214.7427, 0.1128),
            radius: 200,
            list: [
                { position: new mp.Vector3(112.1407, 7192.0615, 0.3151), radius: 50 },
                { position: new mp.Vector3(166.6286, 7120.7998, -0.1904), radius: 50 },
                { position: new mp.Vector3(141.9561, 7142.2285, -0.2073), radius: 50 },
                { position: new mp.Vector3(16.6945, 7126.3120, 0.6183), radius: 50 },
                { position: new mp.Vector3(20.7297, 7189.6353, 0.6183), radius: 50 },
            ]
        },
        2: {
            model: 'a_c_dolphin',
            playerSpawnPoint: new mp.Vector3(3256.1802, -137.3539, 15.9517),
            center: new mp.Vector3(3283.8462, -165.6396, 34.6381),
            radius: 160,
            list: [
                { position: new mp.Vector3(3270.3560, -79.7275, 0.3488), radius: 30 },
                { position: new mp.Vector3(3203.8813, -119.3011, 0.1128), radius: 30 },
                { position: new mp.Vector3(3176.2813, -158.5978, 0.3993), radius: 30 },
                { position: new mp.Vector3(3197.1428, -201.8901, 0.2982), radius: 30 },
                { position: new mp.Vector3(3250.2988, -222.3429, -1.8418), radius: 30 },
                { position: new mp.Vector3(3275.9736, -210.4615, -0.038), radius: 30 },
                { position: new mp.Vector3(3290.8484, -191.4593, -1.0161), radius: 30 },
            ]
        }
    }
}

export const silenceTaskConfig = {
    spawnPosition: new mp.Vector3(3408.30249, 1184.65271, 6),
    passwordPositions: [
        new mp.Vector3(+3406.76245, 1191.98608, 6.088565),
        new mp.Vector3(+3410.20654, 1185.01721, 5.329954),
        new mp.Vector3(+3415.82153, 1189.03918, 8.657373),
        new mp.Vector3(+3407.752, 1190.3125, 9.187283),
        new mp.Vector3(+3418.58447, 1189.71545, 9.85733),
        new mp.Vector3(+3423.02686, 1192.22363, 8.562139),
        new mp.Vector3(+3418.45776, 1198.66345, 8.195646),
        new mp.Vector3(+3426.11475, 1196.79224, 8.500231),
        new mp.Vector3(+3428.09863, 1199.52722, 8.378583),
        new mp.Vector3(+3431.96216, 1199.93054, 8.48673),
        new mp.Vector3(+3431.345, 1203.60413, 9.123096),
        new mp.Vector3(+3422.95947, 1202.7522, 8.260092),
        new mp.Vector3(+3414.56982, 1193.46094, 8.419937),
    ],

    keyModel: 'p_car_keys_01',
    kaysPositions: [
        { x: 3436.17822, y: 1206.688, z: 5.77820444, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
        { x: 3438.79272, y: 1200.18054, z: 5.77863, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
        { x: 3431.83887, y: 1207.08313, z: 5.780226, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
        { x: 3430.74463, y: 1200.80945, z: 5.77671766, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
        { x: 3433.16626, y: 1196.901, z: 5.778952, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
        { x: 3426.975, y: 1196.35913, z: 5.7797, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
        { x: 3426.06372, y: 1193.87134, z: 5.777555, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
        { x: 3421.385, y: 1199.07959, z: 5.797059, rX: 0.0, rY: 0.0, rZ: 0.9961947, rW: 0.08715565 },
        { x: 3425.555, y: 1208.22192, z: 5.581338, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
        { x: 3420.33862, y: 1194.585, z: 5.814596, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
        { x: 3416.95825, y: 1194.5304, z: 5.773486, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
        { x: 3412.36353, y: 1188.94458, z: 5.773753, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
        { x: 3410.40845, y: 1192.26489, z: 5.779356, rX: 0.0, rY: 0.0, rZ: 0.8870108, rW: -0.4617487 },
    ]
}

export const musketeerConfig = [
    //Мушкетёр
    {
        spawnPosition: new mp.Vector3(-2054.3735, -1028.2021, 11.8909, -113.3858),

        duration: 8 * 60 * 1000, // 8 minutes

        birdCount: { min: 10, max: 14 },
        birdSpeed: { min: 15, max: 20 },
        birdCircleRadius: { min: 20, max: 30 },
        birdCenterSpawn: new mp.Vector3(-2041.6219, -1029.5077, 29.8865),

        teleportAfterEndPos: new mp.Vector3(-2068.0747, -1023.7714, 10.9077, -104.8819),
        teleportAfterEndRot: new mp.Vector3(-2068.0747, -1023.7714, 10.9077, -104.8819),
    },

    //Пугало
    {
        spawnPosition: new mp.Vector3(1868.3209, 3714.8704, 112.7311, 25.5118),

        duration: 8 * 60 * 1000, // 8 minutes

        birdCount: { min: 12, max: 18 },
        birdSpeed: { min: 16, max: 21 },
        birdCircleRadius: { min: 23, max: 33 },
        birdCenterSpawn: new mp.Vector3(1870.7605, 3713.4724, 139.4777),

        teleportAfterEndPos: new mp.Vector3(-2068.0747, -1023.7714, 10.9077, -104.8819),
        teleportAfterEndRot: new mp.Vector3(-2068.0747, -1023.7714, 10.9077, -104.8819),
    },

    //Связной
    {
        spawnPosition: new mp.Vector3(-983.5253, -2640.6199, 84.0417),

        duration: 8 * 60 * 1000, // 8 minutes

        birdCount: { min: 12, max: 18 },
        birdSpeed: { min: 17, max: 23 },
        birdCircleRadius: { min: 26, max: 36 },
        birdCenterSpawn: new mp.Vector3(-980.9802, -2634.9099, 111.4564),

        teleportAfterEndPos: new mp.Vector3(-2068.0747, -1023.7714, 10.9077, -104.8819),
        teleportAfterEndRot: new mp.Vector3(-2068.0747, -1023.7714, 10.9077, -104.8819),
    },

    //Светлячок
    {
        spawnPosition: new mp.Vector3(3432.6990, 5175.7715, 34.8008, -53.8583),

        duration: 8 * 60 * 1000, // 8 minutes

        birdCount: { min: 12, max: 18 },
        birdSpeed: { min: 18, max: 24 },
        birdCircleRadius: { min: 29, max: 39 },
        birdCenterSpawn: new mp.Vector3(3431.9736, 5175.6001, 58.6322),

        teleportAfterEndPos: new mp.Vector3(-2068.0747, -1023.7714, 10.9077, -104.8819),
        teleportAfterEndRot: new mp.Vector3(-2068.0747, -1023.7714, 10.9077, -104.8819),
    },

]

export const yesSirConfig = [
    {
        spawnPosition: new mp.Vector3(-24.9231, -2694.7781, 5.9934, 144.5669),
        heading: 180,

        duration: 10 * 60 * 1000, // 10 minutes

        // Кучки
        countBuches: { min: 15, max: 20 },

        bunches: [
            new mp.Vector3(-55.37608, -2705.54517, 4.986109),
            new mp.Vector3(-63.05532, -2706.36475, 4.97549725),
            new mp.Vector3(-61.1092377, -2700.4646, 5.07730341),
            new mp.Vector3(-55.3995476, -2700.423, 5.08184671),
            new mp.Vector3(-64.97779, -2724.25757, 4.665165),
            new mp.Vector3(-65.00135, -2719.24658, 4.73077726),
            new mp.Vector3(-64.3067551, -2712.50977, 4.81743336),
            new mp.Vector3(-60.189537, -2710.97949, 4.836038),
            new mp.Vector3(-55.40528, -2710.752, 4.84645367),
            new mp.Vector3(-56.0171127, -2714.62671, 4.765558),
            new mp.Vector3(-54.8625641, -2717.748, 4.73124838),
            new mp.Vector3(-54.8636475, -2722.86182, 4.666008),
            new mp.Vector3(-55.01019, -2728.78369, 4.63009548),
            new mp.Vector3(-59.8780251, -2732.95142, 4.582342),
            new mp.Vector3(-64.53606, -2732.707, 4.568384),
            new mp.Vector3(-68.62564, -2737.04932, 4.980906),
            new mp.Vector3(-68.87743, -2744.59253, 5.15665627),
            new mp.Vector3(-69.09102, -2751.4248, 5.07679653),
            new mp.Vector3(-69.07483, -2760.28174, 5.083985),
            new mp.Vector3(-69.1954041, -2771.15332, 5.070483),
            new mp.Vector3(-68.96188, -2779.45459, 5.08206844),
            new mp.Vector3(-27.2568, -2736.81372, 4.5845),
            new mp.Vector3(-33.1368752, -2736.3728, 4.566747),
            new mp.Vector3(-33.30225, -2730.329, 4.65708733),
            new mp.Vector3(-33.31885, -2725.24072, 4.728702),
            new mp.Vector3(-32.95275, -2718.139, 4.79568672),
            new mp.Vector3(-32.5560036, -2709.72461, 4.89371061),
            new mp.Vector3(-28.622654, -2704.15186, 5.027384),
            new mp.Vector3(-24.1598949, -2703.87964, 5.03351927),
            new mp.Vector3(-24.0006065, -2710.03882, 4.89043665),
            new mp.Vector3(-22.9434547, -2716.45557, 4.799335),
            new mp.Vector3(-22.7514763, -2722.59888, 4.73057556),
            new mp.Vector3(-23.0222931, -2728.51025, 4.64321041),
            new mp.Vector3(-23.19699, -2734.071, 4.567765),
            new mp.Vector3(-18.8503132, -2742.166, 5.071994),
            new mp.Vector3(-18.9300346, -2747.642, 5.073201),
            new mp.Vector3(-19.3094521, -2754.33179, 5.06848669),
            new mp.Vector3(-19.14957, -2761.7583, 5.06737375),
            new mp.Vector3(-18.9612637, -2739.20435, 5.07112169),
            new mp.Vector3(-19.2821617, -2732.192, 5.06946039),
            new mp.Vector3(-19.35378, -2725.39917, 5.080241),
            new mp.Vector3(-19.3423538, -2717.72168, 5.07564163),
            new mp.Vector3(-19.2387, -2711.08228, 5.074359),
            new mp.Vector3(-19.3271236, -2704.93115, 4.99119),
            new mp.Vector3(-19.4152527, -2698.36572, 5.07392836),
            new mp.Vector3(-24.774086, -2698.14746, 5.069512),
            new mp.Vector3(-28.5095768, -2698.33765, 5.138629),
            new mp.Vector3(-32.31513, -2698.39673, 5.140468),
            new mp.Vector3(-36.0550652, -2698.84058, 5.14622736),
            new mp.Vector3(-38.99909, -2698.02026, 5.169646),
            new mp.Vector3(-43.39963, -2698.14746, 5.11057758),
            new mp.Vector3(-44.80059, -2703.23218, 5.18915462),
            new mp.Vector3(-50.47988, -2703.08887, 5.12861347),
            new mp.Vector3(-48.33796, -2708.454, 5.13753128),
            new mp.Vector3(-50.2658, -2713.34351, 5.13357735),
            new mp.Vector3(-49.1568375, -2718.479, 5.13933325),
            new mp.Vector3(-50.2068748, -2723.25049, 5.12982368),
            new mp.Vector3(-48.5226974, -2728.22168, 5.14133263),
            new mp.Vector3(-49.954937, -2732.449, 5.142203),
            new mp.Vector3(-47.38649, -2736.85962, 5.13855267),
            new mp.Vector3(-44.1956863, -2731.019, 5.13448668),
            new mp.Vector3(-43.1279755, -2735.98755, 5.131421),
            new mp.Vector3(-36.1271057, -2743.3125, 5.140518),
            new mp.Vector3(-38.8566055, -2740.16846, 5.31422472),
            new mp.Vector3(-42.9319725, -2741.21216, 5.138921),
            new mp.Vector3(-46.8106956, -2741.49463, 5.138159),
            new mp.Vector3(-50.33897, -2744.25, 5.1331625),
            new mp.Vector3(-51.6796951, -2751.9104, 5.078603),
            new mp.Vector3(-51.88903, -2759.08252, 5.07426167),
            new mp.Vector3(-69.04091, -2728.67676, 11.0385323),
            new mp.Vector3(-69.04934, -2722.41846, 11.0162973),
            new mp.Vector3(-68.99132, -2714.5647, 11.032937),
            new mp.Vector3(-68.90724, -2704.76123, 11.05816),
            new mp.Vector3(-68.88188, -2698.41357, 10.9868793),
            new mp.Vector3(-49.81694, -2699.06348, 5.12788439),
            new mp.Vector3(-41.66648, -2729.38965, 5.148997),
            new mp.Vector3(-41.0899773, -2725.77661, 5.139132)
        ],

        birds: [
            {
                birdCount: { min: 4, max: 8 },
                birdSpeed: { min: 30, max: 40 },
                birdCircleRadius: { min: 5, max: 15 },
                birdCenterSpawn: new mp.Vector3(-26.7429, -2724.5935, 29.4989),
            },
            {
                birdCount: { min: 4, max: 8 },
                birdSpeed: { min: 30, max: 40 },
                birdCircleRadius: { min: 5, max: 15 },
                birdCenterSpawn: new mp.Vector3(-58.6549, -2720.1099, 23.9722),
            },
            {
                birdCount: { min: 4, max: 8 },
                birdSpeed: { min: 30, max: 40 },
                birdCircleRadius: { min: 5, max: 15 },
                birdCenterSpawn: new mp.Vector3(-59.1824, -2763.7451, 28.5385),
            },
            {
                birdCount: { min: 4, max: 8 },
                birdSpeed: { min: 30, max: 40 },
                birdCircleRadius: { min: 5, max: 15 },
                birdCenterSpawn: new mp.Vector3(-34.4703, -2754.9626, 16.6425),
            }
        ],

        teleportAfterEndPos: new mp.Vector3(-264.1055, -2495.2747, 5.9934, -124.7244),
        teleportAfterEndRot: new mp.Vector3(-264.1055, -2495.2747, 5.9934, -124.7244),
    },
    {
        spawnPosition: new mp.Vector3(-469.1736, -2657.8286, 8.7568, 34.0157),
        heading: 180,

        duration: 10 * 60 * 1000, // 10 minutes

        // Кучки
        countBuches: { min: 15, max: 20 },

        bunches: [
            new mp.Vector3(-491.1917, -2697.71753, 19.6674347),
            new mp.Vector3(-486.420135, -2699.05859, 16.3523445),
            new mp.Vector3(-493.1867, -2681.40869, 13.473876),
            new mp.Vector3(-501.1484, -2689.09229, 13.4641247),
            new mp.Vector3(-502.665833, -2697.85815, 13.465703),
            new mp.Vector3(-495.4409, -2704.4917, 13.4739866),
            new mp.Vector3(-487.570221, -2703.09473, 13.4721937),
            new mp.Vector3(-479.485931, -2695.24951, 13.46848),
            new mp.Vector3(-485.53714, -2680.13672, 7.745805),
            new mp.Vector3(-477.647125, -2687.49023, 7.745805),
            new mp.Vector3(-463.395142, -2658.29419, 7.745805),
            new mp.Vector3(-455.823242, -2666.19019, 7.745805),
            new mp.Vector3(-460.919678, -2669.88721, 7.745805),
            new mp.Vector3(-467.901245, -2662.87671, 7.745805),
            new mp.Vector3(-446.113953, -2637.264, 7.745805),
            new mp.Vector3(-437.256927, -2645.827, 7.745805),
            new mp.Vector3(-400.2525, -2615.32056, 7.745805),
            new mp.Vector3(-408.88385, -2625.697, 7.745805),
            new mp.Vector3(-416.666931, -2634.0376, 7.745805),
            new mp.Vector3(-425.948029, -2643.35083, 7.745805),
            new mp.Vector3(-437.3486, -2655.437, 7.745805),
            new mp.Vector3(-446.582031, -2664.89526, 7.745805),
            new mp.Vector3(-455.519318, -2674.06885, 7.745805),
            new mp.Vector3(-462.199768, -2681.00317, 7.745805),
            new mp.Vector3(-470.009735, -2688.89844, 7.745805),
            new mp.Vector3(-476.6658, -2696.11914, 7.745805),
            new mp.Vector3(-491.097961, -2708.17773, 7.745805),
            new mp.Vector3(-498.00293, -2714.39673, 7.745805),
            new mp.Vector3(-504.203979, -2706.18359, 7.745805),
            new mp.Vector3(-509.922119, -2707.2854, 7.745805),
            new mp.Vector3(-511.763733, -2699.38647, 7.745805),
            new mp.Vector3(-503.7119, -2688.997, 7.745805),
            new mp.Vector3(-492.680634, -2676.7063, 7.745805),
            new mp.Vector3(-483.203857, -2668.892, 7.745805),
            new mp.Vector3(-475.384674, -2661.342, 7.745805),
            new mp.Vector3(-465.702637, -2651.92529, 7.745805),
            new mp.Vector3(-456.6263, -2643.23267, 7.745805),
            new mp.Vector3(-447.276947, -2634.83057, 7.745805),
            new mp.Vector3(-435.204926, -2622.951, 7.745805),
            new mp.Vector3(-425.7764, -2613.375, 7.745805),
            new mp.Vector3(-417.659729, -2606.676, 7.745805),
            new mp.Vector3(-410.829559, -2600.596, 7.745805),
        ],

        birds: [
            {
                birdCount: { min: 4, max: 8 },
                birdSpeed: { min: 30, max: 40 },
                birdCircleRadius: { min: 5, max: 15 },
                birdCenterSpawn: new mp.Vector3(-401.5516, -2595.8638, 42.6923, 147.4016),
            },
            {
                birdCount: { min: 4, max: 8 },
                birdSpeed: { min: 30, max: 40 },
                birdCircleRadius: { min: 5, max: 15 },
                birdCenterSpawn: new mp.Vector3(-439.3978, -2631.5342, 35.9355, 136.0630),
            },
            {
                birdCount: { min: 4, max: 8 },
                birdSpeed: { min: 30, max: 40 },
                birdCircleRadius: { min: 5, max: 15 },
                birdCenterSpawn: new mp.Vector3(-487.1472, -2686.5496, 43.8550, -99.2126),
            },
            {
                birdCount: { min: 4, max: 8 },
                birdSpeed: { min: 30, max: 40 },
                birdCircleRadius: { min: 5, max: 15 },
                birdCenterSpawn: new mp.Vector3(-458.7560, -2663.5913, 35.1772),
            },
        ],

        teleportAfterEndPos: new mp.Vector3(-264.1055, -2495.2747, 5.9934, -124.7244),
        teleportAfterEndRot: new mp.Vector3(-264.1055, -2495.2747, 5.9934, -124.7244),
    },
    {
        spawnPosition: new mp.Vector3(1250.8748, -2947.2131, 9.3129, -85.0394),
        heading: 180,

        duration: 10 * 60 * 1000, // 10 minutes

        // Кучки
        countBuches: { min: 15, max: 20 },

        bunches: [
            new mp.Vector3(1247.71216, -3010.09058, 8.312157),
            new mp.Vector3(1239.01416, -3009.882, 8.312157),
            new mp.Vector3(1241.887, -3033.96216, 8.312157),
            new mp.Vector3(1236.24756, -3034.06372, 8.312157),
            new mp.Vector3(1231.301, -3025.375, 8.312157),
            new mp.Vector3(1230.0481, -3014.95386, 8.312157),
            new mp.Vector3(1230.06946, -3004.47339, 8.312157),
            new mp.Vector3(1245.18445, -3002.85181, 8.312157),
            new mp.Vector3(1235.8479, -3003.577, 8.312157),
            new mp.Vector3(1249.05847, -2908.26758, 20.3053722),
            new mp.Vector3(1244.38293, -2899.15625, 16.3134842),
            new mp.Vector3(1247.15283, -2892.74414, 16.3118668),
            new mp.Vector3(1232.43652, -2906.26245, 20.3163242),
            new mp.Vector3(1236.27063, -2895.75, 16.3067455),
            new mp.Vector3(1230.13391, -2902.16113, 16.309824),
            new mp.Vector3(1230.48218, -2918.40283, 16.3042736),
            new mp.Vector3(1230.48218, -2918.40283, 16.3042736),
            new mp.Vector3(1246.02649, -2920.91748, 16.3092079),
            new mp.Vector3(1251.05957, -2919.00732, 16.3121948),
            new mp.Vector3(1251.094, -2902.651, 16.3148518),
            new mp.Vector3(1251.63232, -2896.73242, 8.312157),
            new mp.Vector3(1250.67761, -2886.5188, 8.312157),
            new mp.Vector3(1243.72473, -2882.777, 8.312157),
            new mp.Vector3(1236.81543, -2882.707, 8.312157),
            new mp.Vector3(1230.1239, -2889.32471, 8.312157),
            new mp.Vector3(1229.50818, -2897.77734, 8.312157),
            new mp.Vector3(1227.979, -2916.51685, 8.312157),
            new mp.Vector3(1227.97815, -2924.406, 8.312157),
            new mp.Vector3(1230.74878, -2920.52148, 8.312157),
            new mp.Vector3(1235.07861, -2924.81323, 8.312157),
            new mp.Vector3(1244.69751, -2922.172, 8.312157),
            new mp.Vector3(1251.53772, -2923.569, 8.312157),
            new mp.Vector3(1251.44751, -2942.92651, 8.312157),
            new mp.Vector3(1243.56458, -2942.89258, 8.312157),
            new mp.Vector3(1235.97742, -2943.307, 8.312157),
            new mp.Vector3(1229.53845, -2944.56055, 8.312157),
            new mp.Vector3(1229.638, -2950.163, 8.312157),
            new mp.Vector3(1238.39185, -2950.78247, 8.312157),
            new mp.Vector3(1245.08423, -2951.25781, 8.312157),
            new mp.Vector3(1251.16113, -2950.32178, 8.312157),
            new mp.Vector3(1252.43347, -2969.36914, 8.312157),
            new mp.Vector3(1246.29346, -2969.224, 8.312157),
            new mp.Vector3(1239.58313, -2969.6416, 8.312157),
            new mp.Vector3(1232.873, -2969.43237, 8.312157),
            new mp.Vector3(1228.81531, -2970.0813, 8.312157),
            new mp.Vector3(1230.25256, -2985.6, 8.312157),
            new mp.Vector3(1239.60327, -2985.5603, 8.312157),
            new mp.Vector3(1248.60876, -2985.79175, 8.312157),
            new mp.Vector3(1252.05945, -3002.938, 8.312157),
            new mp.Vector3(1250.33777, -3013.321, 8.312157),
            new mp.Vector3(1249.52551, -3023.81982, 8.312157),
            new mp.Vector3(1248.68542, -3032.84448, 8.312157),
        ],

        birds: [
            {
                birdCount: { min: 4, max: 8 },
                birdSpeed: { min: 30, max: 40 },
                birdCircleRadius: { min: 5, max: 15 },
                birdCenterSpawn: new mp.Vector3(1242.6329, -2912.4790, 44.4784, 138.8976),
            },
            {
                birdCount: { min: 4, max: 8 },
                birdSpeed: { min: 30, max: 40 },
                birdCircleRadius: { min: 5, max: 15 },
                birdCenterSpawn: new mp.Vector3(1243.6219, -2959.6221, 33.9810, 175.7480),
            },
            {
                birdCount: { min: 4, max: 8 },
                birdSpeed: { min: 30, max: 40 },
                birdCircleRadius: { min: 5, max: 15 },
                birdCenterSpawn: new mp.Vector3(1240.3781, -2988.6594, 39.0864),
            },
        ],

        teleportAfterEndPos: new mp.Vector3(-264.1055, -2495.2747, 5.9934, -124.7244),
        teleportAfterEndRot: new mp.Vector3(-264.1055, -2495.2747, 5.9934, -124.7244),
    }
]

export const democracyPedsConfig = [
    {
        model: 's_m_m_highsec_01',
        spawnPosition: { x: -1379.9341, y: 6746.1099, z: 5.8755 },
        walkStyle: 'move_m@fat@a',
        walk: [
            { x: -1379.9341, y: 6746.1099, z: 5.8755 },
            { x: -1375.0022, y: 6742.6284, z: 5.8755 },
            { x: -1377.7450, y: 6734.1626, z: 5.8755 },
            { x: -1388.6110, y: 6735.5605, z: 5.8755 },
            { x: -1427.0242, y: 6748.1143, z: 5.8755 },
            { x: -1426.3649, y: 6750.6064, z: 5.8755 },
            { x: -1422.0132, y: 6749.3013, z: 5.8755 },
            { x: -1419.6923, y: 6753.9165, z: 5.8755 },
            { x: -1415.5516, y: 6757.8198, z: 5.8755 }
        ]
    },
    {
        model: 'u_m_m_jewelthief',
        spawnPosition: { x: -1372.8000, y: 6737.0771, z: 6.7010, r: -110.5512 },
        animData: ['anim@miss@low@fin@vagos@', 'idle_ped06'],
        ignoreNoise: true
    },
    {
        model: 'csb_porndudes',
        spawnPosition: { x: -1417.0813, y: 6752.0835, z: 6.5494, r: -17.0079 },
        animData: ['amb@world_human_bum_slumped@male@laying_on_right_side@base', 'base'],
        ignoreNoise: true
    },
    {
        model: 'a_f_y_beach_02',
        spawnPosition: { x: -1437.3495, y: 6756.0000, z: 5.8755, r: 107.7165 }, 
        animData: ['mp_safehouseshower@male@', 'male_shower_idle_d'],
        ignoreNoise: true
    },
    {
        model: 's_f_y_baywatch_01',
        spawnPosition: { x: -1434.7517, y: 761.8154, z: 6.8191, r: 68.0315 },
        animData: ['amb@world_human_sunbathe@male@back@idle_a', 'idle_a'],
        ignoreNoise: true
    },
    {
        model: 'u_m_y_abner',
        spawnPosition: { x: -1440.8572, y: 6761.8286, z: 5.8755, r: -113.3858 },
        animData: ['rcm_barry3', 'barry_3_sit_loop'],
        ignoreNoise: true
    },
    {
        model: 'ig_fbisuit_01',
        spawnPosition: { x: -1469.1165, y: 6768.5405, z: 8.0828 },
        walkStyle: 'move_m@fat@a',
        walk: [
            { x: -1469.1165, y: 6768.5405, z: 8.0828 },
            { x: -1453.0154, y: 6763.2661, z: 5.8755 },
            { x: -1450.8660, y: 6758.1362, z: 5.8755 },
            { x: -1432.8660, y: 6751.3320, z: 5.8923 }
        ]
    },
    {
        model: 'u_m_m_jesus_01',
        spawnPosition: { x: -1472.9011, y: 6769.8066, z: 9.4139, r: 68.0315 },
        animData: ['missfam5_yoga', 'c1_pose'],
        ignoreNoise: true
    },
    {
        model: 'u_m_y_chip',
        spawnPosition: { x: -1451.6176, y: 6769.1343, z: 8.9590, r: -22.6772 },
        animData: ['amb@prop_human_bum_shopping_cart@male@idle_a', 'idle_a'],
        ignoreNoise: true
    },
    {
        model: 'ig_bestmen',
        spawnPosition: { x: -1433.6044, y: 6752.8877, z: 8.9590, r: -158.7402 },
        animData: ['mp_army_contact', 'positive_a'],
        ignoreNoise: true
    },
    {
        model: 'cs_nigel',
        spawnPosition: { x: -1402.0747, y: 6742.6152, z: 8.9590, r: 65.1969 },
        animData: ['amb@code_human_in_car_mp_actions@dance@bodhi@ps@base', 'idle_a_fp'],
        ignoreNoise: true
    },
    {
        model: 's_m_m_highsec_02',
        spawnPosition: { x: -1425.1648, y: 6747.5210, z: 8.8755 },
        walkStyle: 'move_m@fat@a',
        ignoreNoise: true,
        walk: [
            { x: -1425.1648, y: 6747.5210, z: 8.8755 },
            { x: -1393.4769, y: 6737.2749, z: 8.8755 },
            { x: -1390.7340, y: 6742.4175, z: 8.8755 },
            { x: -1397.2087, y: 6745.5430, z: 8.8755 },
            { x: -1399.8594, y: 6752.7163, z: 8.8755 },
            { x: -1420.8660, y: 6759.6924, z: 8.8755 }
        ]
    },
    {
        model: 'a_m_y_business_03',
        spawnPosition: { x: -1382.9539, y: 6739.5957, z: 8.9590, r: -42.5197 },
        animData: ['anim@amb@nightclub@lazlow@hi_dancefloor@', 'crowddance_hi_11_handup_laz'],
        ignoreNoise: true
    },
    {
        model: 'a_f_y_bevhills_01',
        spawnPosition: { x: -1382.1230, y: 6740.5186, z: 8.9590, r: -110.5512 },
        animData: ['oddjobs@assassinate@multi@yachttarget@lapdance', 'yacht_ld_f'],
        ignoreNoise: true
    },
    {
        model: 's_f_m_maid_01',
        spawnPosition: { x: -1402.7076, y: 6750.3164, z: 11.8909, r: -19.8425 },
        animData: ['mini@dartsoutro', 'darts_outro_03_guy1'],
        ignoreNoise: true
    },
    {
        model: 'csb_reporter',
        spawnPosition: { x: -1407.4945, y: 6746.4790, z: 11.8909, r: -17.0079 },
        animData: ['amb@world_human_leaning@male@wall@back@foot_up@react_shock', 'front'],
        ignoreNoise: true
    },
    {
        model: 'u_m_m_jewelsec_01',
        spawnPosition: { x: -1413.7583, y: 6756.8174, z: 11.8909 },
        walkStyle: 'move_m@fat@a',
        ignoreNoise: true,
        walk: [
            { x: -1413.7583, y: 6756.8174, z: 11.8909 },
            { x: -1398.9890, y: 6752.0044, z: 11.8909 },
            { x: -1403.0110, y: 6740.7163, z: 11.8909 },
            { x: -1417.2924, y: 6745.3188, z: 11.8909 }
        ]
    },
    {
        model: 's_m_y_autopsy_01',
        spawnPosition: { x: -1410.6329, y: 6748.4438, z: 11.9077, r: 14.1732 },
        animData: ['amb@medic@standing@kneel@base', 'base'],
        ignoreNoise: true
    },
    {
        model: 'u_m_m_bankman',
        spawnPosition: { x: -1411.2395, y: 6749.6572, z: 11.9077, r: -73.7008 },
        animData: ['missfbi1', 'cpr_pumpchest_idle'],
        ignoreNoise: true
    },
    {
        model: 's_m_m_ccrew_01',
        spawnPosition: { x: -1375.3319, y: 6738.0791, z: 2.5897, r: 68.0315 },
        animData: ['amb@world_human_car_park_attendant@male@base', 'base'],
        ignoreNoise: true
    },
    {
        model: 's_f_y_movprem_01',
        spawnPosition: { x: -1420.8528, y: 6748.2856, z: 11.9077, r: 155.9055 },
        animData: ['anim@mp_player_intcelebrationmale@thumb_on_ears', 'thumb_on_ears'],
        ignoreNoise: true
    },
    {
        model: 'ig_groom',
        spawnPosition: { x: -1430.4132, y: 6761.3936, z: 12.7671, r: -8.5039 },
        animData: ['anim@mp_point', '1st_person_high_blocked'],
        ignoreNoise: true
    },

];

export const familyHeirloomTaskConfig = {
    spawnPosition: new mp.Vector3(123, 123, 123),

    heirloomModels: [
        'v_res_r_figclown',
        'v_res_r_figgirlclown',
        'v_res_r_figgirl',
        'v_res_r_figfemale',
        'v_res_r_figdancer',
        'v_res_r_figflamenco',
    ],

    heirloomPositions: [
        { x: -2022.848, y: -1044.454, z: 1.97763121, rX: 0.0, rY: 0.0, rZ: 0.976296, rW: -0.2164396 },
        { x: -2021.60608, y: -1041.29236, z: 2.136111, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: 0.1305262 },
        { x: -2028.42041, y: -1032.52051, z: 2.14398384, rX: 0.0, rY: 0.0, rZ: -0.3007058, rW: 0.9537169 },
        { x: -2026.00586, y: -1043.48633, z: 1.56164527, rX: 0.0, rY: 0.0, rZ: -0.5000001, rW: -0.8660254 },
        { x: -2032.73682, y: -1041.791, z: 2.70040751, rX: 0.0, rY: 0.0, rZ: 0.9659258, rW: 0.2588191 },
        { x: -2046.91064, y: -1026.22217, z: 1.56791341, rX: 0.0, rY: 0.0, rZ: -0.8433914, rW: 0.5372997 },
        { x: -2054.5708, y: -1035.20581, z: 1.564019, rX: 0.0, rY: 0.0, rZ: -0.7660444, rW: 0.6427876 },
        { x: -2057.94287, y: -1031.16736, z: 2.971843, rX: 0.0, rY: 0.0, rZ: 0.4226182, rW: 0.9063078 },
        { x: -2067.02271, y: -1027.10449, z: 2.06036329, rX: 0.0, rY: 0.0, rZ: -0.3826835, rW: 0.9238796 },
        { x: -2070.24683, y: -1015.93146, z: 2.051181, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071068 },
        { x: -2021.8501, y: -1032.14111, z: 4.87414646, rX: 0.0, rY: 0.0, rZ: -0.8870109, rW: -0.4617485 },
        { x: -2051.572, y: -1021.0473, z: 5.644813, rX: 0.0, rY: 0.0, rZ: -0.258819, rW: -0.9659258 },
        { x: -2071.56177, y: -1024.12146, z: 5.759085, rX: 0.0, rY: 0.0, rZ: -0.7933533, rW: -0.6087614 },
        { x: -2079.7146, y: -1017.87079, z: 5.85362148, rX: 0.0, rY: 0.0, rZ: -0.4617486, rW: 0.8870108 },
        { x: -2083.59424, y: -1025.99329, z: 4.8883605, rX: 0.0, rY: 0.0, rZ: 0.9848078, rW: 0.1736482 },
        { x: -2092.04736, y: -1022.673, z: 4.878502, rX: 0.0, rY: 0.0, rZ: 0.9848077, rW: 0.1736482 },
        { x: -2082.25122, y: -1015.28558, z: 4.88096333, rX: 0.0, rY: 0.0, rZ: 0.9537169, rW: 0.3007059 },
        { x: -2089.40356, y: -1010.08331, z: 4.882738, rX: 0.0, rY: 0.0, rZ: -0.7372773, rW: 0.6755902 },
        { x: -2090.21655, y: -1017.79828, z: 4.88810158, rX: 0.0, rY: 0.0, rZ: -0.9238795, rW: -0.3826835 },
        { x: -2096.664, y: -1017.08777, z: 5.09202862, rX: 0.0, rY: 0.0, rZ: 0.9914449, rW: -0.1305261 },
        { x: -2102.79224, y: -1012.51636, z: 5.86462069, rX: 0.0, rY: 0.0, rZ: -0.8870109, rW: 0.4617486 },
        { x: -2079.5625, y: -1025.17993, z: 5.761809, rX: 0.0, rY: 0.0, rZ: -0.9063078, rW: 0.4226182 },
        { x: -2116.88159, y: -1010.00787, z: 6.806333, rX: 0.0, rY: 0.0, rZ: -0.7372773, rW: -0.6755902 },
        { x: -2103.19678, y: -1010.51306, z: 7.96367073, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071068 },
        { x: -2096.99561, y: -1017.20154, z: 7.977805, rX: 0.0, rY: 0.0, rZ: -0.7660446, rW: 0.6427876 },
        { x: -2090.39844, y: -1009.90814, z: 8.848805, rX: 0.0, rY: 0.0, rZ: 0.0, rW: 1.0 },
        { x: -2088.35864, y: -1021.00507, z: 7.96952152, rX: 0.0, rY: 0.0, rZ: -0.3007058, rW: 0.953717 },
        { x: -2092.17676, y: -1010.17938, z: 7.978802, rX: 0.0, rY: 0.0, rZ: -0.08715573, rW: 0.9961947 },
        { x: -2077.76367, y: -1015.21954, z: 7.969765, rX: 0.0, rY: 0.0, rZ: -0.3826834, rW: 0.9238796 },
        { x: -2081.11353, y: -1025.59131, z: 7.97080851, rX: 0.0, rY: 0.0, rZ: -0.3826834, rW: 0.9238796 },
        { x: -2048.555, y: -1022.09021, z: 8.814025, rX: 0.0, rY: 0.0, rZ: 0.0, rW: 1.0 },
        { x: -2050.03467, y: -1024.18115, z: 8.82517052, rX: 0.0, rY: 0.0, rZ: -0.1305262, rW: 0.9914449 },
        { x: -2051.701, y: -1033.77271, z: 7.970161, rX: 0.0, rY: 0.0, rZ: -0.8870109, rW: -0.4617485 },
        { x: -2040.73291, y: -1037.918, z: 7.973559, rX: 0.0, rY: 0.0, rZ: 0.9238796, rW: -0.3826834 },
        { x: -2056.50757, y: -1023.77045, z: 10.9739418, rX: 0.0, rY: 0.0, rZ: 0.8433914, rW: 0.5372996 },
        { x: -2051.84717, y: -1022.71564, z: 10.9140921, rX: 0.0, rY: 0.0, rZ: -0.9238796, rW: -0.3826834 },
        { x: -2066.59229, y: -1028.51575, z: 11.4757271, rX: 0.0, rY: 0.0, rZ: 0.9848077, rW: 0.1736482 },
        { x: -2072.94019, y: -1018.63782, z: 10.9052734, rX: 0.0, rY: 0.0, rZ: -0.6087614, rW: -0.7933533 },
        { x: -2077.41357, y: -1021.9447, z: 10.90495, rX: 0.0, rY: 0.0, rZ: 0.9238795, rW: 0.3826834 },
        { x: -2084.331, y: -1014.6554, z: 12.6161652, rX: 0.0, rY: 0.0, rZ: -0.7071068, rW: 0.7071068 },
        { x: -2088.11865, y: -1020.76062, z: 11.7808075, rX: 0.0, rY: 0.0, rZ: 0.5, rW: 0.8660254 },
    ]
}
