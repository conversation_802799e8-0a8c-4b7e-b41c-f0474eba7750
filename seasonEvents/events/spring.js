export const springItemsIds = {
    present: 828, // Открытка
    parkToken: 829, // Жетон парка
    valentineCurrency: 830, // Валюта дня валентина
}

export const gull = {
    skin: 'a_c_seagull',

    startPos: new mp.Vector3(-1836.2505, -1200.3297, 22.9106), // Позиция, куда будет телепортирован игрок после начала игры (должно быть не в воздухе)
    startRot: 150,

    deathPos: new mp.Vector3(-1852.3781, -1231.1736, 94.5392), // Позиция, куда телепортировать игрока после смерти
    deathRot: -48,

    endPos: new mp.Vector3(-1102.9846, -1701.2836, 4.3590), // Позиция, куда телепортировать игрока после окончания игры
    endRot: 54,

    fadeIn: 1000,

    completeTime: 7, // in minutes
    deathPenalty: 30, // in seconds
    failCooldown: [3, 'minutes'],
    accessRadius: 3.3,

    npcs: [
        [
            {
                skin: 'a_m_y_hipster_03',
                position: new mp.Vector3(-1847.6703, -1214.5846, 13.8792),
                rotation: -110,
                animData: ['amb@prop_human_bum_shopping_cart@male@base', 'base'],
            },
            {
                skin: 'a_m_y_gay_02',
                position: new mp.Vector3(-1825.3715, -1222.7737, 13.0029),
                rotation: -155,
                animData: ['mini@hookers_spcokehead', 'idle_reject_loop_a'],
            },
            {
                skin: 'a_m_y_hipster_01',
                position: new mp.Vector3(-1823.8418, -1207.8989, 13.0029),
                rotation: -175,
                animData: ['anim@amb@business@bgen@bgen_no_work@', 'sit_phone_phoneputdown_idle_nowork'],
            },
            {
                skin: 'a_f_y_hipster_04',
                position: new mp.Vector3(-1840.1802, -1234.2065, 13.0029),
                rotation: 147,
                animData: ['amb@world_human_smoking@female@idle_a', 'idle_b'],
            },
			{
                skin: 'a_m_y_vinewood_04',
                position: new mp.Vector3(-1849.6088, -1178.3077, 13.0029),
                rotation: 48.2,
                animData: ['timetable@reunited@ig_10', 'base_amanda'],
            },
			{
                skin: 'a_m_y_vinewood_02',
                position: new mp.Vector3(-1810.7076, -1182.2902, 13.0029),
                rotation: -147.2,
                animData: ['anim@amb@casino@valet_scenario@pose_c@', 'look_ahead_r_a_m_y_vinewood_01'],
            },
        ],
        [
            {
                skin: 'a_m_y_hipster_03',
                position: new mp.Vector3(-1862.9011, -1226.7825, 13.0029),
                rotation: 138,
                animData: ['timetable@reunited@ig_10', 'base_amanda'],
            },
            {
                skin: 'a_m_y_hipster_01',
                position: new mp.Vector3(-1867.8726, -1201.6879, 13.0029),
                rotation: 51,
                animData: ['timetable@maid@ig_2@', 'ig_2_base'],
            },
            {
                skin: 'a_m_y_gay_02',
                position: new mp.Vector3(-1821.5868, -1216.9846, 13.0029),
                rotation: -124,
                animData: ['anim@amb@casino@valet_scenario@pose_c@', 'look_ahead_r_a_m_y_vinewood_01'],
            },
            {
                skin: 'a_f_y_hipster_04',
                position: new mp.Vector3(-1830.0132, -1226.7032, 13.0029),
                rotation: 34,
                animData: ['amb@world_human_leaning@male@wall@back@foot_up@idle_b', 'idle_d'],
            },
			{
                skin: 'a_m_y_vinewood_04',
                position: new mp.Vector3(-1793.4857, -1199.9077, 18.0747),
                rotation: -42.2,
                animData: ['amb@world_human_picnic@female@base', 'base'],
            },
			{
                skin: 'a_m_y_vinewood_02',
                position: new mp.Vector3(-1809.8638, -1187.6967, 13.5084),
                rotation: 127.5,
                animData: ['mini@hookers_spcokehead', 'idle_reject_loop_a'],
            },
        ],
        [
            {
                skin: 'a_f_y_hipster_04',
                position: new mp.Vector3(-1823.2087, -1259.8154, 13.0029),
                rotation: 150,
                animData: ['missheat', 'binoculars_loop'],
            },
            {
                skin: 'a_m_y_gay_02',
                position: new mp.Vector3(-1811.6835, -1225.0681, 19.1531),
                rotation: -133,
                animData: ['amb@world_human_smoking@female@idle_a', 'idle_b'],
            },
            {
                skin: 'a_m_y_hipster_01',
                position: new mp.Vector3(-1849.1340, -1239.0989, 13.0029),
                rotation: 136,
                animData: ['amb@prop_human_bum_shopping_cart@male@base', 'base'],
            },
            {
                skin: 'a_m_y_hipster_03',
                position: new mp.Vector3(-1867.4901, -1222.8660, 13.0029),
                rotation: -167,
                animData: ['missheat', 'binoculars_loop'],
            },
			{
                skin: 'a_m_y_vinewood_04',
                position: new mp.Vector3(-1827.0857, -1171.3187, 16.6088),
                rotation: 136,
                animData: ['mini@hookers_spcokehead', 'idle_reject_loop_a'],
            },
			{
                skin: 'a_m_y_vinewood_02',
                position: new mp.Vector3(-1789.9648, -1207.9912, 13.0029),
                rotation: -124,
                animData: ['amb@prop_human_bum_shopping_cart@male@base', 'base'],
            },
        ],
    ]
}

export const springTreasureItem = { itemId: 830, count: { min: 1, max: 3 } };

export const cupidon = {
    teleportTo: new mp.Vector3(2102.2007, 3554.1501, 42.2534), // Куда игрок будет телепортирован после начала катсцены
    teleportToRotation: 28,

    carryTo: new mp.Vector3(2047.0759, 3566.7778, 40.2217), // Куда относить предметы после того как ты их подобрал
    carryToRotation: 100,

    spawnAfterEnd: new mp.Vector3(-1702.8491, -1131.0389, 13.1523), // Куда телепортировать игрока после окончания игры
    spawnAfterEndRotation: 147.76049,

    hourTime: 23,
    timecycleModifier: 'DLC_MpSecurity_Studio_Lounge',
    noiseMultiplier: 0.5,

    cutscene: {
        cameraPosition: new mp.Vector3(2050.4624, 3564.6345, 41.3073),
        cameraRotation: new mp.Vector3(0, 0, 56),
        fov: 40,
        duration: 6000,

        pointAtCord: new mp.Vector3(2043.0957, 3566.9702, 39.5810),
    },

    fadeTime: 1000,
    completeTime: 7, // in minutes
    failCooldown: [3, 'minutes'],

    pedsConfig: [
        {
            x: 2067.1760, y: 3565.6611, z: 41.3086,
            heading: -113,
            skin: 'cs_tracydisanto',
            showInMap: true,
            wanderArea: {
                x: 2067.1760, y: 3565.6611, z: 41.3086,
                radius: 25,
                minimalLength: 0, // Минимальная длина, по которой он будет блуждать, прежде чем время ожидания между прогулками, секунды, прежде чем продолжить
                timeBetweenWalks: 5, // Продолжительность времени, в течение которого ped будет стоять неподвижно/отдыхать между прогулками
            }
        },
        {
            x: 2064.8713, y: 3566.9480, z: 40.9528,
            heading: 66,
            skin: 'cs_tenniscoach',
            showInMap: true,
            wanderArea: {
                x: 2064.8713, y: 3566.9480, z: 40.9528,
                radius: 25,
                minimalLength: 0, // Минимальная длина, по которой он будет блуждать, прежде чем время ожидания между прогулками, секунды, прежде чем продолжить
                timeBetweenWalks: 5, // Продолжительность времени, в течение которого ped будет стоять неподвижно/отдыхать между прогулками
            }
        },
        {
            x: 2053.9788, y: 3550.7188, z: 41.4110,
            heading: -100,
            skin: 'csb_tonya',
            showInMap: true,
            wanderArea: {
                x: 2064.8713, y: 3566.9480, z: 40.9528,
                radius: 25,
                minimalLength: 0, // Минимальная длина, по которой он будет блуждать, прежде чем время ожидания между прогулками, секунды, прежде чем продолжить
                timeBetweenWalks: 5, // Продолжительность времени, в течение которого ped будет стоять неподвижно/отдыхать между прогулками
            }
        },
        // {
        //     x: 2044.6749, y: 3573.3831, z: 39.3595,
        //     heading: -138,
        //     skin: 'csb_ramp_hic',
        //     showInMap: true,
        //     wanderArea: {
        //         x: 2064.8713, y: 3566.9480, z: 40.9528,
        //         radius: 25,
        //         minimalLength: 0, // Минимальная длина, по которой он будет блуждать, прежде чем время ожидания между прогулками, секунды, прежде чем продолжить
        //         timeBetweenWalks: 5, // Продолжительность времени, в течение которого ped будет стоять неподвижно/отдыхать между прогулками
        //     }
        // }
    ],

    blipItemId: 279,
    items: [
        [
            {
                position: new mp.Vector3(2082.204345703125, 3553.738525390625, 41.75186538696289),
                rotation: 94,
                model: 'prop_ghettoblast_02', // Модель объекта
                afterCarry: new mp.Vector3(2046.09228515625, 3565.568359375, 39.340450286865234),
                afterCarryRotation: 111,
            },
            {
                position: new mp.Vector3(2086.7958, 3569.72412, 41.3787),
                rotation: -20,
                model: 'prop_mr_rasberryclean', // Модель объекта
                afterCarry: new mp.Vector3(2045.9200439453125, 3566.0703125, 39.34798049926758),
                afterCarryRotation: 85,
            },
            {
                position: new mp.Vector3(2056.01220703125, 3551.17919921875, 40.63745880126953),
                rotation: 2,
                model: 'vw_prop_casino_champset', // Модель объекта
                afterCarry: new mp.Vector3(2046.5419921875, 3566.142333984375, 39.22161865234375),
                afterCarryRotation: 2.5,
            },
            {
                position: new mp.Vector3(2064.552734375, 3583.089111328125, 38.521392822265625),
                rotation: 2,
                model: 'hei_heist_acc_flowers_01', // Модель объекта
                afterCarry: new mp.Vector3(2045.9434814453125, 3566.472412109375, 39.1357307434082),
                afterCarryRotation: 3,
            }
        ],
        [
            {
                position: new mp.Vector3(2078.28564, 3584.117, 39.6432266),
                rotation: 125,
                model: 'sf_prop_sf_acc_guitar_01a', // Модель объекта
                afterCarry: new mp.Vector3(2046.302001953125, 3565.051025390625, 39.7707405090332),
                afterCarryRotation: 125,
            },
            {
                position: new mp.Vector3(2089.28857421875, 3563.849365234375, 41.85026168823242),
                rotation: 158,
                model: 'xm3_prop_xm3_pineapple_01a', // Модель объекта
                afterCarry: new mp.Vector3(2046.031005859375, 3565.71875, 39.25296401977539),
                afterCarryRotation: 110,
            },
            {
                position: new mp.Vector3(2065.248291015625, 3549.818115234375, 40.80610656738281),
                rotation: -59,
                model: 'sf_prop_sf_art_box_cig_01a', // Модель объекта
                afterCarry: new mp.Vector3(2045.933349609375, 3566.3994140625, 39.136478424072266),
                afterCarryRotation: 86,
            },
            {
                position: new mp.Vector3(2059.90966796875, 3589.920166015625, 38.075923919677734),
                rotation: 0,
                model: 'v_res_cakedome', // Модель объекта
                afterCarry: new mp.Vector3(2046.6728515625, 3566.010009765625, 39.20532989501953),
                afterCarryRotation: 86,
            }
        ],
        [
            {
                position: new mp.Vector3(2083.31689453125, 3556.13037109375, 42.26420211791992),
                rotation: 122,
                model: 'ba_prop_battle_champ_open_03', // Модель объекта
                afterCarry: new mp.Vector3(2046.1204833984375, 3565.61669921875, 39.37643051147461),
                afterCarryRotation: 109,
            },
            {
                position: new mp.Vector3(2061.69775390625, 3557.96044921875, 40.71208572387695),
                rotation: -2,
                model: 'ex_mp_h_acc_fruitbowl_02', // Модель объекта
                afterCarry: new mp.Vector3(2046.035400390625, 3566.2783203125, 39.14744186401367),
                afterCarryRotation: 16.5,
            },
            {
                position: new mp.Vector3(2060.9873046875, 3551.2724609375, 40.84672546386719),
                rotation: -2,
                model: 'vw_prop_flowers_vase_03a', // Модель объекта
                afterCarry: new mp.Vector3(2046.20849609375, 3566.92138671875, 39.14767074584961),
                afterCarryRotation: -166,
            },
            {
                position: new mp.Vector3(2059.90966796875, 3589.920166015625, 38.075923919677734),
                rotation: 0,
                model: 'v_res_cakedome', // Модель объекта
                afterCarry: new mp.Vector3(2046.6728515625, 3566.010009765625, 39.20532989501953),
                afterCarryRotation: 86,
            }
        ],
    ],
}


export const panicRoom = {
    winCount: 3,

    spawnPositions: [
        new mp.Vector3(443.916, -1261.756, -7.765),
        new mp.Vector3(443.854, -1208.930, -7.765),
        new mp.Vector3(477.254, -1229.928, -7.765),
        new mp.Vector3(490.573, -1284.199, -7.765),
        new mp.Vector3(483.078, -1311.375, -7.765),
        new mp.Vector3(448.740, -1315.128, -7.765),
        new mp.Vector3(457.402, -1250.231, -7.765),
        new mp.Vector3(485.789, -1254.996, -7.765),
    ],

    ghostPositions: [
        { pos: new mp.Vector3(494.5774, -1308.9910, -7.7651), rot: -84.85 },
        { pos: new mp.Vector3(451.6037, -1314.0542, -7.7651), rot: 0.41 },
        { pos: new mp.Vector3(474.7427, -1294.0775, -7.7651), rot: -35.34 },
        { pos: new mp.Vector3(448.0817, -1267.4104, -7.7651), rot: -38.56 },
        { pos: new mp.Vector3(489.8227, -1273.5375, -7.7651), rot: 89.90 },
        { pos: new mp.Vector3(489.7676, -1210.8809, -7.7651), rot: 44.67 },
        { pos: new mp.Vector3(444.6872, -1212.8687, -7.7651), rot: -87.11 },
        { pos: new mp.Vector3(478.8852, -1235.7916, -7.7651), rot: 179.43 },
        { pos: new mp.Vector3(447.9076, -1234.2354, -7.7651), rot: -47.48 },
        { pos: new mp.Vector3(468.0362, -1260.1484, -7.7651), rot: -87.96 }
    ],

    ghostProps: [
        'm23_1_prop_m31_ghostzombie_01a',
        'm23_1_prop_m31_ghostjohnny_01a',
        'm23_1_prop_m31_ghostrurmeth_01a',
        'm23_1_prop_m31_ghostsalton_01a',
        'm23_1_prop_m31_ghostskidrow_01a'
    ],

    angryNPCs: [
        new mp.Vector3(446.7311, -1218.2312, -7.7651),
        new mp.Vector3(488.1904, -1218.6017, -7.7651),
        new mp.Vector3(445.4449, -1244.7616, -7.7651),
        new mp.Vector3(458.7995, -1244.4521, -7.7651),
        new mp.Vector3(486.6346, -1278.2086, -7.7652),
        new mp.Vector3(463.6996, -1281.4535, -7.7665),
        new mp.Vector3(450.0973, -1274.9481, -7.7651),
        new mp.Vector3(470.2490, -1304.6156, -7.7651),
        new mp.Vector3(487.4028, -1304.9850, -7.7651),
        new mp.Vector3(455.4659, -1308.5637, -7.7651),
    ]
}

export const surpriseTask = {
    centerPosition: new mp.Vector3(66.5705, 3708.4346, 39.7549),
    heartPosition: new mp.Vector3(52.9772835, 3711.752, 39.15225),

    endCamera: {
        pos: new mp.Vector3(55.697, 3715.615, 39.595),
        pointAtCoord: new mp.Vector3(49.837, 3701.256, 40.329),
        fov: 45
    },

    endPed: {
        spawnPos: new mp.Vector3(49.9912, 3696.7385, 39.7435),
        targetPos: new mp.Vector3(54.5934, 3713.3669, 39.7435),
        model: 'u_f_o_carol'
    },

    itemsPositions: [
        [
            { pos: new mp.Vector3(57.0346, 3690.4031, 39.9213), model: 'prop_rub_tyre_01', ipl: 'maj_event_love_7' },
            { pos: new mp.Vector3(77.0922, 3734.8564, 39.5651), model: 'v_ind_cm_tyre01', ipl: 'maj_event_love_8' },
            { pos: new mp.Vector3(79.3571, 3699.7976, 39.7550), model: 'prop_paints_can07', ipl: 'maj_event_love_11' },
            { pos: new mp.Vector3(60.4578, 3729.873, 39.685997), model: 'prop_roadcone02a', ipl: 'maj_event_love_14' },
        ],
        [
            { pos: new mp.Vector3(63.3980, 3682.6348, 39.8343), model: 'prop_rub_tyre_01', ipl: 'maj_event_love_7' },
            { pos: new mp.Vector3(89.9509, 3726.3579, 39.5946), model: 'v_ind_cm_tyre01', ipl: 'maj_event_love_8' },
            { pos: new mp.Vector3(85.9588, 3709.1208, 39.7321), model: 'prop_paints_can07', ipl: 'maj_event_love_11' },
            { pos: new mp.Vector3(62.4718, 3728.8096, 41.5364), model: 'prop_roadcone02a', ipl: 'maj_event_love_14' },
        ],
        [
            { pos: new mp.Vector3(76.6067, 3703.8662, 41.0772), model: 'prop_rub_tyre_01', ipl: 'maj_event_love_7' },
            { pos: new mp.Vector3(53.5500, 3677.1340, 39.7310), model: 'v_ind_cm_tyre01', ipl: 'maj_event_love_8' },
            { pos: new mp.Vector3(35.5948, 3706.9290, 39.6155), model: 'prop_paints_can07', ipl: 'maj_event_love_11' },
            { pos: new mp.Vector3(69.3467, 3731.5962, 39.6099), model: 'prop_roadcone02a', ipl: 'maj_event_love_14' },
        ],
    ],
}

export const springRadarItems = [
    { pos: new mp.Vector3(287.28698, -963.698, 29.418), r: 69.08224 },
    { pos: new mp.Vector3(-104.4290, -588.884, 36.276), r: 245.2555 },
    { pos: new mp.Vector3(-51.07506, -579.359, 37.266), r: 82.91519 },
    { pos: new mp.Vector3(-41.40829, -408.377, 39.491), r: 189.9573 },
    { pos: new mp.Vector3(-386.7333, -101.358, 38.721), r: 209.4233 },
    { pos: new mp.Vector3(-499.0174, -289.573, 35.563), r: 327.8051 },
    { pos: new mp.Vector3(-490.7380, -222.881, 36.395), r: 131.3673 },
    { pos: new mp.Vector3(-502.5675, -242.623, 35.944), r: 172.3667 },
    { pos: new mp.Vector3(-560.5998, -255.193, 36.316), r: 200.5269 },
    { pos: new mp.Vector3(-807.5485, -219.250, 37.276), r: 90.82389 },
    { pos: new mp.Vector3(871.59118, 11.31822, 78.764), r: 248.8733 },
    { pos: new mp.Vector3(-79.14023, 307.0409, 108.20), r: 194.3140 },
    { pos: new mp.Vector3(935.76293, 126.7255, 80.385), r: 22.56856 },
    { pos: new mp.Vector3(-50.74171, 358.0960, 113.05), r: 152.1805 },
    { pos: new mp.Vector3(1000.6915, 208.7773, 82.041), r: 298.4406 },
    { pos: new mp.Vector3(1050.2285, 269.1947, 85.811), r: 302.6752 },
    { pos: new mp.Vector3(1133.4461, 345.0229, 91.343), r: 182.4607 },
    { pos: new mp.Vector3(-1.678464, 332.8521, 113.06), r: 59.92607 },
    { pos: new mp.Vector3(-68.58713, 300.7200, 112.64), r: 119.5931 },
    { pos: new mp.Vector3(361.56097, 326.8422, 103.80), r: 211.6054 },
    { pos: new mp.Vector3(1098.4749, 467.1263, 92.157), r: 140.4890 },
    { pos: new mp.Vector3(214.83825, 282.4792, 105.52), r: 239.6831 },
    { pos: new mp.Vector3(1061.3703, 482.2465, 94.069), r: 54.62414 },
    { pos: new mp.Vector3(1094.6604, 521.5472, 95.373), r: 335.1587 },
    { pos: new mp.Vector3(1445.8891, 1088.094, 114.98), r: 32.37887 },
    { pos: new mp.Vector3(1449.8762, 1092.995, 114.98), r: 22.72269 },
    { pos: new mp.Vector3(-504.9877, -69.7779, 39.575), r: 252.6901 },
    { pos: new mp.Vector3(-1886.381, -367.261, 49.063), r: 103.2862 },
    { pos: new mp.Vector3(-453.4358, -192.315, 37.226), r: 122.7803 },
    { pos: new mp.Vector3(2658.9814, 3230.918, 53.791), r: 332.2266 },
    { pos: new mp.Vector3(-473.9367, -160.447, 38.343), r: 93.32398 },
    { pos: new mp.Vector3(2659.3276, 3215.343, 53.387), r: 288.0449 },
    { pos: new mp.Vector3(-1877.203, -335.725, 50.300), r: 148.1206 },
    { pos: new mp.Vector3(-541.4060, -166.971, 38.266), r: 322.8778 },
    { pos: new mp.Vector3(2689.1811, 3296.604, 55.476), r: 275.0931 },
    { pos: new mp.Vector3(-579.4316, -185.872, 37.894), r: 20.29491 },
    { pos: new mp.Vector3(2705.1191, 3283.045, 55.378), r: 185.5034 },
    { pos: new mp.Vector3(-594.0901, -193.337, 37.694), r: 14.30724 },
    { pos: new mp.Vector3(2717.7478, 3294.524, 55.743), r: 69.59519 },
    { pos: new mp.Vector3(-1859.228, -328.950, 57.082), r: 152.5807 },
    { pos: new mp.Vector3(-1590.876, -863.037, 10.126), r: 213.5704 },
    { pos: new mp.Vector3(-1426.173, -546.165, 33.984), r: 219.7861 },
    { pos: new mp.Vector3(-1854.299, -395.372, 46.483), r: 146.3165 },
    { pos: new mp.Vector3(2843.3271, 4408.223, 48.969), r: 16.28570 },
    { pos: new mp.Vector3(2855.9982, 4413.408, 48.962), r: 12.04213 },
    { pos: new mp.Vector3(2926.9995, 4367.862, 51.140), r: 359.3851 },
    { pos: new mp.Vector3(-1899.402, -347.804, 49.234), r: 187.6000 },
    { pos: new mp.Vector3(-1906.765, -324.812, 49.065), r: 107.4138 },
    { pos: new mp.Vector3(-1598.534, -861.432, 10.125), r: 151.2719 },
    { pos: new mp.Vector3(-1906.001, -311.328, 49.202), r: 31.48308 },
    { pos: new mp.Vector3(2920.6335, 4641.420, 48.544), r: 244.6531 },
    { pos: new mp.Vector3(-1882.451, -365.010, 49.355), r: 102.1636 },
    { pos: new mp.Vector3(2924.4023, 4629.017, 48.545), r: 271.4489 },
    { pos: new mp.Vector3(2919.2795, 4625.523, 48.545), r: 44.95531 },
    { pos: new mp.Vector3(-1877.683, -272.747, 49.663), r: 357.4599 },
    { pos: new mp.Vector3(-1574.793, -879.741, 10.113), r: 109.4707 },
    { pos: new mp.Vector3(2442.2905, 4952.609, 45.304), r: 158.2226 },
    { pos: new mp.Vector3(-1577.539, -906.209, 9.7122), r: 67.92343 },
    { pos: new mp.Vector3(2441.5234, 4959.328, 45.850), r: 264.5664 },
    { pos: new mp.Vector3(-1586.087, -921.594, 9.1640), r: 46.09517 },
    { pos: new mp.Vector3(2456.3518, 4962.708, 45.445), r: 125.3220 },
    { pos: new mp.Vector3(-952.1406, -1244.78, 5.9058), r: 90.71450 },
    { pos: new mp.Vector3(-1592.846, -928.763, 8.9507), r: 53.98528 },
    { pos: new mp.Vector3(2465.4077, 4971.921, 45.512), r: 206.0357 },
    { pos: new mp.Vector3(-1603.249, -938.874, 8.6904), r: 44.48240 },
    { pos: new mp.Vector3(-1605.218, -940.763, 8.6275), r: 357.6056 },
    { pos: new mp.Vector3(2467.3408, 4985.341, 46.018), r: 248.6601 },
    { pos: new mp.Vector3(-3013.134, 124.4270, 14.971), r: 248.1115 },
    { pos: new mp.Vector3(-1655.960, -977.462, 7.3461), r: 4.189936 },
    { pos: new mp.Vector3(-2997.540, 107.3335, 14.429), r: 10.79568 },
    { pos: new mp.Vector3(2402.6032, 5032.965, 45.993), r: 50.49054 },
    { pos: new mp.Vector3(1985.7458, 3075.587, 46.974), r: 64.89990 },
    { pos: new mp.Vector3(2392.6064, 5038.237, 46.130), r: 30.76440 },
    { pos: new mp.Vector3(-1659.400, -980.431, 7.3368), r: 328.6152 },
    { pos: new mp.Vector3(2397.4663, 5048.142, 46.244), r: 291.6711 },
    { pos: new mp.Vector3(-1660.241, -979.862, 7.3425), r: 314.1371 },
    { pos: new mp.Vector3(-3019.167, 82.39601, 11.671), r: 350.0704 },
    { pos: new mp.Vector3(1982.1861, 3069.161, 46.971), r: 58.78371 },
    { pos: new mp.Vector3(2368.9118, 5077.501, 46.743), r: 156.7364 },
    { pos: new mp.Vector3(2360.5249, 5070.389, 46.601), r: 102.7221 },
    { pos: new mp.Vector3(-1665.388, -975.923, 7.4205), r: 20.53551 },
    { pos: new mp.Vector3(2355.4523, 5063.414, 46.329), r: 220.9322 },
    { pos: new mp.Vector3(-3043.770, 100.4961, 12.346), r: 290.5020 },
    { pos: new mp.Vector3(2348.5270, 5051.547, 45.853), r: 135.5328 },
    { pos: new mp.Vector3(2337.5373, 5046.793, 45.239), r: 90.72799 },
    { pos: new mp.Vector3(2323.7651, 5037.776, 44.658), r: 138.0633 },
    { pos: new mp.Vector3(1969.2565, 3048.706, 47.048), r: 66.21375 },
    { pos: new mp.Vector3(2310.0612, 5022.081, 43.595), r: 141.2165 },
    { pos: new mp.Vector3(-3061.490, 100.1006, 12.346), r: 171.9167 },
    { pos: new mp.Vector3(1964.9746, 3041.709, 47.065), r: 52.91649 },
    { pos: new mp.Vector3(-429.7566, 6013.481, 31.598), r: 316.9174 },
    { pos: new mp.Vector3(1993.4722, 3040.302, 47.031), r: 134.2440 },
    { pos: new mp.Vector3(2291.3449, 4990.284, 42.374), r: 185.1301 },
    { pos: new mp.Vector3(-3018.274, 45.99483, 10.119), r: 139.5572 },
    { pos: new mp.Vector3(-365.3989, 6144.167, 32.154), r: 106.8974 },
    { pos: new mp.Vector3(-3003.115, 47.00027, 11.608), r: 212.6437 },
    { pos: new mp.Vector3(1989.9896, 3054.857, 47.215), r: 357.6191 },
    { pos: new mp.Vector3(2312.2648, 4971.450, 41.656), r: 235.0925 },
    { pos: new mp.Vector3(1992.8482, 3053.885, 47.214), r: 303.6177 },
    { pos: new mp.Vector3(-1668.742, -979.870, 7.3607), r: 48.61613 },
    { pos: new mp.Vector3(2327.5739, 4960.005, 41.730), r: 239.4764 },
    { pos: new mp.Vector3(2337.9982, 4950.577, 41.998), r: 221.7658 },
    { pos: new mp.Vector3(-2892.448, 50.06712, 13.066), r: 135.4528 },
    { pos: new mp.Vector3(-357.3703, 6201.382, 31.489), r: 221.2032 },
    { pos: new mp.Vector3(2350.0515, 4946.148, 42.411), r: 300.7298 },
    { pos: new mp.Vector3(-1674.355, -992.105, 7.3324), r: 113.7691 },
    { pos: new mp.Vector3(2359.7731, 4958.060, 43.098), r: 323.5812 },
    { pos: new mp.Vector3(2369.2675, 4971.983, 43.529), r: 343.3251 },
    { pos: new mp.Vector3(-1667.071, -998.446, 7.3563), r: 101.2685 },
    { pos: new mp.Vector3(-2854.321, 36.64954, 12.767), r: 122.0287 },
    { pos: new mp.Vector3(2358.2229, 4982.037, 43.189), r: 91.81527 },
    { pos: new mp.Vector3(-238.9609, 6309.454, 31.478), r: 282.6239 },
    { pos: new mp.Vector3(-1667.277, -1004.99, 7.3756), r: 327.9000 },
    { pos: new mp.Vector3(2343.2800, 4984.885, 43.039), r: 70.45000 },
    { pos: new mp.Vector3(2322.8527, 4997.779, 42.099), r: 32.11159 },
    { pos: new mp.Vector3(-2893.501, 1.878305, 11.607), r: 347.7119 },
    { pos: new mp.Vector3(-1661.586, -1004.61, 7.3851), r: 54.97005 },
    { pos: new mp.Vector3(2334.1166, 5014.215, 42.903), r: 304.0926 },
    { pos: new mp.Vector3(-2933.603, -35.5618, 2.2638), r: 2.952176 },
    { pos: new mp.Vector3(-1683.826, -1004.77, 7.3889), r: 140.4677 },
    { pos: new mp.Vector3(2357.0507, 5015.616, 43.808), r: 254.4968 },
    { pos: new mp.Vector3(-2983.288, -2.22993, 5.1691), r: 14.14874 },
    { pos: new mp.Vector3(2378.1752, 5008.386, 45.115), r: 269.8233 },
    { pos: new mp.Vector3(2386.3029, 5001.648, 45.286), r: 205.2361 },
    { pos: new mp.Vector3(-3073.199, 85.54983, 9.4329), r: 258.3021 },
    { pos: new mp.Vector3(2396.7290, 4998.131, 45.856), r: 272.1561 },
    { pos: new mp.Vector3(-3096.667, 163.6636, 6.9572), r: 261.4823 },
    { pos: new mp.Vector3(2103.4787, 4913.210, 40.949), r: 167.2481 },
    { pos: new mp.Vector3(2099.3518, 4924.579, 40.224), r: 3.147207 },
    { pos: new mp.Vector3(2087.9541, 4936.498, 40.482), r: 48.69715 },
    { pos: new mp.Vector3(-1685.317, -1004.11, 7.3910), r: 140.9960 },
    { pos: new mp.Vector3(2074.9855, 4948.884, 40.660), r: 54.99174 },
    { pos: new mp.Vector3(2067.4113, 4955.800, 40.352), r: 48.81038 },
    { pos: new mp.Vector3(-1225.128, -900.278, 12.358), r: 34.83408 },
    { pos: new mp.Vector3(2057.4648, 4966.137, 40.182), r: 46.51442 },
    { pos: new mp.Vector3(2043.4644, 4972.497, 41.114), r: 122.1957 },
    { pos: new mp.Vector3(2029.4112, 4961.703, 41.400), r: 135.4673 },
    { pos: new mp.Vector3(-1719.583, -1008.28, 5.5381), r: 233.2676 },
    { pos: new mp.Vector3(2020.9189, 4953.066, 41.656), r: 133.8329 },
    { pos: new mp.Vector3(-1720.239, -1009.29, 5.4629), r: 233.8437 },
    { pos: new mp.Vector3(2008.5202, 4941.333, 41.919), r: 144.4915 },
    { pos: new mp.Vector3(1991.6254, 4924.882, 42.793), r: 142.9853 },
    { pos: new mp.Vector3(-1207.886, -776.613, 17.351), r: 131.2558 },
    { pos: new mp.Vector3(-711.9047, -1297.00, 5.1019), r: 97.42433 },
    { pos: new mp.Vector3(1977.3812, 4910.281, 44.024), r: 139.4021 },
    { pos: new mp.Vector3(-1732.554, -976.289, 6.0697), r: 7.840904 },
    { pos: new mp.Vector3(1977.2924, 4896.355, 43.965), r: 216.4508 },
    { pos: new mp.Vector3(-1202.210, -784.192, 17.327), r: 120.5868 },
    { pos: new mp.Vector3(-1730.968, -975.937, 6.1186), r: 53.74139 },
    { pos: new mp.Vector3(1987.8710, 4885.595, 43.786), r: 230.3355 },
    { pos: new mp.Vector3(-717.6813, -1272.93, 5.1019), r: 183.8025 },
    { pos: new mp.Vector3(-1731.318, -974.572, 6.1394), r: 132.9775 },
    { pos: new mp.Vector3(2003.0461, 4870.388, 43.404), r: 229.5509 },
    { pos: new mp.Vector3(1881.1383, 3688.647, 33.371), r: 138.7354 },
    { pos: new mp.Vector3(-1732.995, -973.990, 6.1529), r: 193.4972 },
    { pos: new mp.Vector3(2015.2843, 4859.196, 42.671), r: 230.2130 },
    { pos: new mp.Vector3(2027.3796, 4849.211, 42.680), r: 230.8159 },
    { pos: new mp.Vector3(-1734.189, -974.972, 6.1091), r: 246.6315 },
    { pos: new mp.Vector3(-1186.766, -875.548, 13.787), r: 39.59595 },
    { pos: new mp.Vector3(2063.8708, 4849.363, 41.798), r: 300.6186 },
    { pos: new mp.Vector3(-1773.061, -986.564, 3.8160), r: 112.4212 },
    { pos: new mp.Vector3(-1178.647, -870.090, 14.036), r: 32.41893 },
    { pos: new mp.Vector3(2070.8405, 4834.478, 41.719), r: 187.2397 },
    { pos: new mp.Vector3(-1772.540, -988.265, 3.8668), r: 108.5515 },
    { pos: new mp.Vector3(-777.6535, -1296.28, 5.1001), r: 295.5068 },
    { pos: new mp.Vector3(2079.2695, 4820.783, 41.527), r: 222.1273 },
    { pos: new mp.Vector3(2093.2822, 4819.134, 41.601), r: 295.4462 },
    { pos: new mp.Vector3(-825.3862, -1339.06, 5.1060), r: 129.4897 },
    { pos: new mp.Vector3(-1751.500, -947.682, 7.4675), r: 103.1888 },
    { pos: new mp.Vector3(2125.9384, 4838.074, 41.330), r: 304.4828 },
    { pos: new mp.Vector3(-808.3588, -1391.05, 5.0951), r: 186.3904 },
    { pos: new mp.Vector3(1867.4226, 3688.055, 34.267), r: 237.6051 },
    { pos: new mp.Vector3(1942.6900, 3725.010, 32.294), r: 256.1312 },
    { pos: new mp.Vector3(2140.7180, 4857.348, 40.970), r: 353.3001 },
    { pos: new mp.Vector3(-1736.159, -896.636, 7.7025), r: 293.1239 },
    { pos: new mp.Vector3(2135.4418, 4875.048, 40.816), r: 33.65606 },
    { pos: new mp.Vector3(-739.8139, -1312.80, 5.4503), r: 318.4575 },
    { pos: new mp.Vector3(2120.8762, 4876.268, 40.819), r: 93.53404 },
    { pos: new mp.Vector3(-1727.038, -917.411, 7.6982), r: 304.7402 },
    { pos: new mp.Vector3(2110.9150, 4887.582, 40.998), r: 12.73139 },
    { pos: new mp.Vector3(2017.8489, 3741.547, 32.035), r: 86.89480 },
    { pos: new mp.Vector3(-1290.369, -1118.14, 6.5848), r: 85.41961 },
    { pos: new mp.Vector3(-1725.998, -918.777, 7.6991), r: 328.5725 },
    { pos: new mp.Vector3(-712.0254, -1318.79, 5.1019), r: 28.41390 },
    { pos: new mp.Vector3(2091.7482, 4872.817, 41.574), r: 139.9820 },
    { pos: new mp.Vector3(2091.8825, 4862.338, 41.539), r: 190.5386 },
    { pos: new mp.Vector3(-1715.215, -931.819, 7.7026), r: 299.5883 },
    { pos: new mp.Vector3(2083.1618, 4846.820, 41.822), r: 113.1995 },
    { pos: new mp.Vector3(-690.8486, -1273.22, 9.1002), r: 82.22319 },
    { pos: new mp.Vector3(-1699.936, -946.586, 7.6840), r: 317.0031 },
    { pos: new mp.Vector3(2072.5881, 4853.496, 41.792), r: 31.50706 },
    { pos: new mp.Vector3(1965.9907, 3057.944, 46.882), r: 92.77149 },
    { pos: new mp.Vector3(-1703.096, -943.482, 7.6764), r: 308.7491 },
    { pos: new mp.Vector3(2080.2329, 4871.474, 41.580), r: 311.3931 },
    { pos: new mp.Vector3(-1153.463, -1421.89, 4.8188), r: 34.79697 },
    { pos: new mp.Vector3(1854.7048, 2592.473, 45.671), r: 247.5951 },
    { pos: new mp.Vector3(-1724.366, -893.352, 7.7305), r: 247.4086 },
    { pos: new mp.Vector3(-1157.118, -1424.25, 4.7269), r: 36.56032 },
    { pos: new mp.Vector3(2105.2014, 5021.093, 41.554), r: 321.8861 },
    { pos: new mp.Vector3(-1717.209, -875.601, 8.1373), r: 162.9358 },
    { pos: new mp.Vector3(2115.0944, 5028.653, 42.065), r: 308.7056 },
    { pos: new mp.Vector3(2123.6511, 5034.202, 42.274), r: 291.9138 },
    { pos: new mp.Vector3(-1701.977, -859.389, 8.5237), r: 207.0601 },
    { pos: new mp.Vector3(2130.5581, 5049.488, 42.891), r: 334.7928 },
    { pos: new mp.Vector3(2148.3063, 5068.050, 44.441), r: 308.0631 },
    { pos: new mp.Vector3(-1687.714, -843.272, 8.9195), r: 231.9177 },
    { pos: new mp.Vector3(1306.3593, 1197.459, 107.15), r: 130.6750 },
    { pos: new mp.Vector3(2165.6555, 5067.951, 43.816), r: 202.1791 },
    { pos: new mp.Vector3(2174.4572, 5053.654, 43.412), r: 218.0410 },
    { pos: new mp.Vector3(2190.4211, 5035.665, 43.527), r: 226.7658 },
    { pos: new mp.Vector3(-1677.231, -816.942, 11.356), r: 236.8820 },
    { pos: new mp.Vector3(2186.8422, 5021.432, 42.647), r: 136.4284 },
    { pos: new mp.Vector3(2179.1845, 5009.512, 42.020), r: 111.7168 },
    { pos: new mp.Vector3(-1663.339, -822.025, 10.018), r: 217.5733 },
    { pos: new mp.Vector3(2181.9938, 4998.919, 41.733), r: 235.7098 },
    { pos: new mp.Vector3(2194.7783, 5002.672, 42.502), r: 314.4523 },
    { pos: new mp.Vector3(-1658.026, -816.302, 10.216), r: 225.2057 },
    { pos: new mp.Vector3(2210.4924, 5017.777, 43.771), r: 295.8482 },
    { pos: new mp.Vector3(2225.0522, 5001.947, 42.946), r: 211.6967 },
    { pos: new mp.Vector3(2212.2194, 4983.650, 42.325), r: 150.3306 },
    { pos: new mp.Vector3(963.00042, -143.952, 74.485), r: 129.3218 },
    { pos: new mp.Vector3(1726.5632, 3238.036, 41.201), r: 324.0718 },
    { pos: new mp.Vector3(1718.1478, 3272.125, 41.151), r: 275.4723 },
    { pos: new mp.Vector3(955.66601, -139.008, 74.483), r: 185.5835 },
    { pos: new mp.Vector3(2212.6215, 5157.463, 57.221), r: 310.2155 },
    { pos: new mp.Vector3(1674.0832, 3260.542, 40.553), r: 209.7932 },
    { pos: new mp.Vector3(-1646.846, -816.384, 10.192), r: 165.3073 },
    { pos: new mp.Vector3(2227.3278, 5171.400, 59.009), r: 341.3430 },
    { pos: new mp.Vector3(1684.3218, 3227.602, 40.699), r: 276.1589 },
    { pos: new mp.Vector3(-1638.253, -822.957, 10.125), r: 140.1646 },
    { pos: new mp.Vector3(2219.3957, 5195.037, 61.718), r: 76.20909 },
    { pos: new mp.Vector3(975.50878, -144.300, 74.240), r: 51.84228 },
    { pos: new mp.Vector3(-1470.361, -708.823, 25.666), r: 152.8253 },
    { pos: new mp.Vector3(-132.8596, 963.6759, 236.01), r: 147.1122 },
    { pos: new mp.Vector3(-1626.552, -835.556, 10.121), r: 140.4080 },
    { pos: new mp.Vector3(1659.8265, 3220.295, 40.566), r: 273.7061 },
    { pos: new mp.Vector3(-1615.493, -845.478, 10.121), r: 144.2406 },
    { pos: new mp.Vector3(1650.3829, 3254.616, 40.533), r: 355.3673 },
    { pos: new mp.Vector3(983.90356, -119.673, 76.803), r: 98.17076 },
    { pos: new mp.Vector3(-1666.855, -551.963, 34.507), r: 138.6629 },
    { pos: new mp.Vector3(1530.8001, 3221.384, 40.527), r: 306.8005 },
    { pos: new mp.Vector3(-682.2290, 947.4793, 236.47), r: 74.14451 },
    { pos: new mp.Vector3(1539.3671, 3187.117, 40.531), r: 330.7467 },
    { pos: new mp.Vector3(-1570.815, -841.401, 10.072), r: 276.3761 },
    { pos: new mp.Vector3(-1678.880, -543.360, 35.786), r: 129.0462 },
    { pos: new mp.Vector3(2172.7976, 5121.012, 48.210), r: 226.3423 },
    { pos: new mp.Vector3(986.51910, -107.070, 74.353), r: 153.4422 },
    { pos: new mp.Vector3(1576.0664, 3197.815, 40.518), r: 287.8438 },
    { pos: new mp.Vector3(-1572.218, -844.017, 10.127), r: 245.3144 },
    { pos: new mp.Vector3(1568.6608, 3232.426, 40.700), r: 261.6626 },
    { pos: new mp.Vector3(1562.2077, 3231.358, 40.655), r: 196.3164 },
    { pos: new mp.Vector3(-1560.101, -831.384, 10.205), r: 157.4803 },
    { pos: new mp.Vector3(-1709.589, -510.966, 37.485), r: 194.3099 },
    { pos: new mp.Vector3(2115.0695, 5109.610, 46.147), r: 88.78347 },
    { pos: new mp.Vector3(1571.3355, 3197.391, 40.527), r: 197.2734 },
    { pos: new mp.Vector3(2106.9570, 5117.833, 47.265), r: 34.67999 },
    { pos: new mp.Vector3(-1557.673, -833.442, 10.229), r: 135.1448 },
    { pos: new mp.Vector3(2098.0478, 5130.707, 48.936), r: 33.46178 },
    { pos: new mp.Vector3(-1668.114, -458.985, 38.930), r: 239.4983 },
    { pos: new mp.Vector3(2086.2419, 5144.474, 50.360), r: 39.24625 },
    { pos: new mp.Vector3(1577.8701, 3154.953, 40.730), r: 354.3450 },
    { pos: new mp.Vector3(-1557.268, -857.276, 10.096), r: 8.791582 },
    { pos: new mp.Vector3(-1165.294, 736.2583, 155.36), r: 55.43413 },
    { pos: new mp.Vector3(2070.6274, 5162.823, 51.233), r: 34.49113 },
    { pos: new mp.Vector3(-1624.496, -410.090, 40.687), r: 251.5127 },
    { pos: new mp.Vector3(2077.2983, 5178.099, 52.198), r: 279.0305 },
    { pos: new mp.Vector3(-1550.691, -856.724, 10.123), r: 331.0345 },
    { pos: new mp.Vector3(954.49102, -83.0410, 78.267), r: 313.6567 },
    { pos: new mp.Vector3(2084.8032, 5169.270, 51.468), r: 220.9954 },
    { pos: new mp.Vector3(1554.4125, 3132.856, 40.586), r: 307.2897 },
    { pos: new mp.Vector3(2096.7644, 5159.522, 50.619), r: 236.2716 },
    { pos: new mp.Vector3(1534.1080, 3153.234, 40.531), r: 273.4194 },
    { pos: new mp.Vector3(926.04315, -111.334, 78.362), r: 147.1317 },
    { pos: new mp.Vector3(-1331.386, 70.19911, 53.666), r: 272.3908 },
    { pos: new mp.Vector3(-1598.955, -364.047, 45.600), r: 147.1709 },
    { pos: new mp.Vector3(1516.2073, 3145.503, 40.530), r: 278.8601 },
    { pos: new mp.Vector3(-1331.996, 50.89723, 53.576), r: 323.0398 },
    { pos: new mp.Vector3(2085.0554, 5113.333, 46.171), r: 174.1628 },
    { pos: new mp.Vector3(-1582.055, -354.252, 46.361), r: 288.7368 },
    { pos: new mp.Vector3(1544.9207, 3123.208, 40.595), r: 316.5484 },
    { pos: new mp.Vector3(980.86755, -150.909, 77.063), r: 152.4889 },
    { pos: new mp.Vector3(-827.9245, -63.9127, 37.883), r: 171.4833 },
    { pos: new mp.Vector3(-1553.770, -317.883, 47.064), r: 238.7903 },
    { pos: new mp.Vector3(-794.5974, -15.1309, 39.966), r: 110.9824 },
    { pos: new mp.Vector3(-1535.142, -282.439, 49.055), r: 231.8556 },
    { pos: new mp.Vector3(2095.4802, 5070.020, 42.967), r: 229.7586 },
    { pos: new mp.Vector3(2088.375, .059082031, 41.616), r: 132.8734 },
    { pos: new mp.Vector3(1494.2619, 3115.133, 40.538), r: 323.8847 },
    { pos: new mp.Vector3(2064.1547, 5051.165, 41.781), r: 42.55999 },
    { pos: new mp.Vector3(2053.5864, 5064.966, 41.680), r: 43.57840 },
    { pos: new mp.Vector3(1466.1652, 3102.748, 40.565), r: 307.9226 },
    { pos: new mp.Vector3(2040.0783, 5081.111, 42.561), r: 36.70477 },
    { pos: new mp.Vector3(-1528.785, -867.986, 10.218), r: 333.2274 },
    { pos: new mp.Vector3(1459.2014, 3130.461, 40.530), r: 20.08741 },
    { pos: new mp.Vector3(1408.0745, 3116.664, 40.534), r: 274.5080 },
    { pos: new mp.Vector3(2024.3820, 5102.298, 43.876), r: 35.95208 },
    { pos: new mp.Vector3(-1514.420, -263.732, 50.323), r: 269.0681 },
    { pos: new mp.Vector3(1415.7384, 3090.061, 40.541), r: 298.5909 },
    { pos: new mp.Vector3(-1260.192, -968.816, 2.2899), r: 220.8376 },
    { pos: new mp.Vector3(2035.8120, 5124.510, 45.833), r: 312.6609 },
    { pos: new mp.Vector3(-1519.785, -856.096, 10.137), r: 153.4467 },
    { pos: new mp.Vector3(1372.2093, 3076.966, 40.545), r: 225.5086 },
    { pos: new mp.Vector3(2055.8669, 5131.841, 46.340), r: 280.9990 },
    { pos: new mp.Vector3(-1497.843, -881.916, 10.428), r: 312.2842 },
    { pos: new mp.Vector3(1365.0284, 3104.252, 40.534), r: 259.6299 },
    { pos: new mp.Vector3(2071.6115, 5120.553, 45.626), r: 192.9173 },
    { pos: new mp.Vector3(2068.8610, 5098.782, 43.764), r: 168.6506 },
    { pos: new mp.Vector3(1339.3421, 3098.061, 40.534), r: 273.6178 },
    { pos: new mp.Vector3(-1480.371, -896.866, 10.023), r: 144.1127 },
    { pos: new mp.Vector3(1589.9820, 6449.674, 25.317), r: 155.4462 },
    { pos: new mp.Vector3(-1465.680, -235.940, 49.692), r: 38.35869 },
    { pos: new mp.Vector3(1955.7855, 5000.341, 41.939), r: 169.4060 },
    { pos: new mp.Vector3(1346.5186, 3070.507, 40.573), r: 305.0178 },
    { pos: new mp.Vector3(-1485.149, -910.128, 10.036), r: 308.3041 },
    { pos: new mp.Vector3(1556.9256, 6450.177, 23.824), r: 219.2537 },
    { pos: new mp.Vector3(1949.6889, 4979.290, 43.182), r: 166.4289 },
    { pos: new mp.Vector3(-1445.697, -213.182, 48.543), r: 122.5718 },
    { pos: new mp.Vector3(1327.6424, 3065.939, 40.433), r: 307.8603 },
    { pos: new mp.Vector3(1943.5772, 4963.032, 44.193), r: 157.3207 },
    { pos: new mp.Vector3(-837.3928, -930.972, 16.076), r: 36.83947 },
    { pos: new mp.Vector3(1320.2980, 3092.848, 40.533), r: 275.3072 },
    { pos: new mp.Vector3(-1461.575, -929.196, 10.066), r: 257.4852 },
    { pos: new mp.Vector3(1941.2171, 4944.341, 45.581), r: 209.4813 },
    { pos: new mp.Vector3(1960.4110, 4949.328, 43.513), r: 310.7862 },
    { pos: new mp.Vector3(1295.0349, 3086.558, 40.534), r: 288.5825 },
    { pos: new mp.Vector3(1613.9899, 6435.769, 26.079), r: 188.7485 },
    { pos: new mp.Vector3(-1474.344, -959.481, 10.192), r: 160.3066 },
    { pos: new mp.Vector3(1973.2871, 4960.992, 42.945), r: 315.7424 },
    { pos: new mp.Vector3(1302.1804, 3059.336, 40.476), r: 290.9774 },
    { pos: new mp.Vector3(1978.1820, 4981.687, 42.025), r: 331.4754 },
    { pos: new mp.Vector3(-1452.872, -328.819, 44.638), r: 294.9915 },
    { pos: new mp.Vector3(-1482.815, -948.679, 10.211), r: 139.3328 },
    { pos: new mp.Vector3(1271.1293, 3050.309, 40.574), r: 288.7438 },
    { pos: new mp.Vector3(2001.2791, 5012.530, 41.145), r: 296.3175 },
    { pos: new mp.Vector3(-1470.872, -310.704, 45.934), r: 3.728097 },
    { pos: new mp.Vector3(1263.2259, 3077.405, 40.428), r: 258.2266 },
    { pos: new mp.Vector3(2029.5416, 4994.747, 39.713), r: 217.1501 },
    { pos: new mp.Vector3(2030.7252, 4976.604, 41.089), r: 103.8493 },
    { pos: new mp.Vector3(-1408.085, -387.319, 36.596), r: 251.6324 },
    { pos: new mp.Vector3(1519.1228, 6506.123, 21.926), r: 197.4851 },
    { pos: new mp.Vector3(1238.3646, 3070.782, 40.530), r: 259.3540 },
    { pos: new mp.Vector3(2016.8101, 4965.110, 41.580), r: 135.4580 },
    { pos: new mp.Vector3(-901.1896, -631.101, 28.208), r: 255.0480 },
    { pos: new mp.Vector3(1245.4145, 3043.866, 40.410), r: 286.5353 },
    { pos: new mp.Vector3(1999.3505, 4950.392, 41.989), r: 124.4930 },
    { pos: new mp.Vector3(-1457.477, -417.561, 35.551), r: 170.8734 },
    { pos: new mp.Vector3(1513.2272, 6478.701, 22.133), r: 135.1457 },
    { pos: new mp.Vector3(1979.2535, 4928.025, 43.191), r: 112.1136 },
    { pos: new mp.Vector3(1426.7110, 6495.577, 19.878), r: 51.38775 },
    { pos: new mp.Vector3(1220.5065, 3036.403, 40.578), r: 350.2701 },
    { pos: new mp.Vector3(-1486.580, -944.619, 10.213), r: 141.7159 },
    { pos: new mp.Vector3(-1492.796, -383.068, 40.105), r: 139.5504 },
    { pos: new mp.Vector3(1975.7637, 4898.959, 43.938), r: 203.5184 },
    { pos: new mp.Vector3(1213.0170, 3063.673, 40.534), r: 263.6216 },
    { pos: new mp.Vector3(-1490.408, -385.352, 40.002), r: 135.1329 },
    { pos: new mp.Vector3(-1508.048, -934.544, 9.6845), r: 144.0820 },
    { pos: new mp.Vector3(1195.0787, 3030.574, 40.535), r: 298.4314 },
    { pos: new mp.Vector3(1305.3646, 6511.277, 19.905), r: 213.1959 },
    { pos: new mp.Vector3(-1538.997, -380.374, 43.568), r: 322.1387 },
    { pos: new mp.Vector3(1187.9718, 3057.120, 40.534), r: 289.5245 },
    { pos: new mp.Vector3(-1506.704, -950.815, 8.7871), r: 338.3881 },
    { pos: new mp.Vector3(1360.2395, 6458.480, 19.670), r: 311.8646 },
    { pos: new mp.Vector3(1986.9112, 4776.442, 41.962), r: 169.2582 },
    { pos: new mp.Vector3(1164.0371, 3021.989, 40.297), r: 299.6757 },
    { pos: new mp.Vector3(1962.7878, 4753.936, 41.844), r: 125.3046 },
    { pos: new mp.Vector3(1505.7481, 6414.597, 22.836), r: 17.90749 },
    { pos: new mp.Vector3(1155.8627, 3049.260, 40.309), r: 278.6648 },
    { pos: new mp.Vector3(-1567.559, -362.062, 46.702), r: 63.82019 },
    { pos: new mp.Vector3(1944.7084, 4740.202, 41.547), r: 122.1542 },
    { pos: new mp.Vector3(1151.3974, 3018.062, 40.302), r: 320.7867 },
    { pos: new mp.Vector3(1917.7874, 4726.002, 41.594), r: 124.0868 },
    { pos: new mp.Vector3(-1602.202, -465.458, 37.313), r: 142.6916 },
    { pos: new mp.Vector3(1909.8896, 4709.627, 41.068), r: 231.8008 },
    { pos: new mp.Vector3(-1512.510, -945.789, 9.4338), r: 323.7720 },
    { pos: new mp.Vector3(1143.6280, 3045.350, 40.283), r: 272.5675 },
    { pos: new mp.Vector3(1916.8380, 4698.837, 41.027), r: 222.6176 },
    { pos: new mp.Vector3(-1615.125, -488.354, 36.862), r: 319.1925 },
    { pos: new mp.Vector3(1131.8074, 3012.573, 40.611), r: 313.5089 },
    { pos: new mp.Vector3(1937.4058, 4685.550, 41.044), r: 252.2476 },
    { pos: new mp.Vector3(-1536.641, -942.489, 11.566), r: 308.8245 },
    { pos: new mp.Vector3(1948.6199, 4691.842, 41.365), r: 321.8394 },
    { pos: new mp.Vector3(1961.0634, 4698.629, 41.756), r: 295.6644 },
    { pos: new mp.Vector3(-1710.646, -418.674, 44.524), r: 53.74900 },
    { pos: new mp.Vector3(1125.1563, 3040.649, 40.387), r: 274.4810 },
    { pos: new mp.Vector3(50.481403, 11.77011, 69.492), r: 89.61924 },
    { pos: new mp.Vector3(-1697.059, -401.998, 46.540), r: 60.18355 },
    { pos: new mp.Vector3(1989.3168, 4711.466, 41.740), r: 305.8681 },
    { pos: new mp.Vector3(1106.7524, 3006.616, 40.564), r: 315.0667 },
    { pos: new mp.Vector3(1099.5201, 3034.382, 40.534), r: 227.6029 },
    { pos: new mp.Vector3(-1630.766, -338.607, 50.422), r: 49.34665 },
    { pos: new mp.Vector3(1069.4965, 2996.283, 41.795), r: 325.2362 },
    { pos: new mp.Vector3(1065.4822, 3007.548, 41.772), r: 259.5382 },
    { pos: new mp.Vector3(-1677.194, -387.025, 48.100), r: 61.13504 },
    { pos: new mp.Vector3(-920.7325, -109.271, 37.932), r: 241.1257 },
    { pos: new mp.Vector3(-1556.172, -952.210, 13.017), r: 301.2943 },
    { pos: new mp.Vector3(-1694.343, -407.521, 46.162), r: 66.66077 },
    { pos: new mp.Vector3(-1574.671, -944.558, 13.017), r: 76.22806 },
    { pos: new mp.Vector3(-1762.318, -422.207, 43.130), r: 231.9310 },
    { pos: new mp.Vector3(-1588.449, -959.838, 13.017), r: 57.72474 },
    { pos: new mp.Vector3(1062.6597, 3019.668, 41.756), r: 282.1632 },
    { pos: new mp.Vector3(-1606.022, -942.883, 13.342), r: 199.4324 },
    { pos: new mp.Vector3(-1624.358, -959.628, 13.017), r: 273.5494 },
    { pos: new mp.Vector3(-1599.206, -269.012, 53.134), r: 91.39152 },
    { pos: new mp.Vector3(-611.0878, -925.609, 24.562), r: 207.2602 },
    { pos: new mp.Vector3(-1614.548, -990.959, 13.017), r: 55.85605 },
    { pos: new mp.Vector3(-1642.277, -987.119, 13.017), r: 232.7366 },
    { pos: new mp.Vector3(1867.5097, 4653.240, 37.351), r: 177.8768 },
    { pos: new mp.Vector3(-1595.574, -249.063, 53.905), r: 79.02796 },
    { pos: new mp.Vector3(1157.6697, 385.9835, 91.469), r: 96.04200 },
    { pos: new mp.Vector3(-1657.852, -1003.64, 13.017), r: 220.3685 },
    { pos: new mp.Vector3(1060.4233, 3026.912, 41.744), r: 277.4096 },
    { pos: new mp.Vector3(66.496360, 7043.939, 15.540), r: 215.5625 },
    { pos: new mp.Vector3(1059.0224, 3032.856, 41.739), r: 278.0281 },
    { pos: new mp.Vector3(-1558.804, -209.441, 55.429), r: 358.5377 },
    { pos: new mp.Vector3(1824.4030, 4696.106, 39.100), r: 88.67484 },
    { pos: new mp.Vector3(1056.9965, 3039.303, 41.727), r: 284.6047 },
    { pos: new mp.Vector3(117.12212, 7016.951, 9.6759), r: 160.6034 },
    { pos: new mp.Vector3(1139.7996, 362.2668, 91.399), r: 124.2903 },
    { pos: new mp.Vector3(-1675.019, -1024.08, 13.017), r: 238.8619 },
    { pos: new mp.Vector3(-1564.284, -209.727, 55.340), r: 0.382848 },
    { pos: new mp.Vector3(1800.1612, 4699.531, 40.693), r: 73.50693 },
    { pos: new mp.Vector3(1055.6562, 3045.268, 41.725), r: 281.6743 },
    { pos: new mp.Vector3(35.598834, -1273.48, 29.503), r: 257.2660 },
    { pos: new mp.Vector3(99.164016, 6918.423, 20.453), r: 345.4313 },
    { pos: new mp.Vector3(360.73110, -135.757, 65.703), r: 8.443363 },
    { pos: new mp.Vector3(1053.7239, 3051.868, 41.733), r: 326.1080 },
    { pos: new mp.Vector3(36.791683, -1294.22, 29.313), r: 51.68408 },
    { pos: new mp.Vector3(1039.7518, 489.8842, 95.498), r: 125.2074 },
    { pos: new mp.Vector3(208.99728, 6957.354, 9.5103), r: 128.5268 },
    { pos: new mp.Vector3(-1699.230, -1052.74, 13.017), r: 235.0145 },
    { pos: new mp.Vector3(1051.8575, 3058.235, 41.722), r: 271.8281 },
    { pos: new mp.Vector3(35.460575, -1299.84, 29.293), r: 325.4474 },
    { pos: new mp.Vector3(2055.5595, 4925.883, 40.961), r: 317.7745 },
    { pos: new mp.Vector3(304.33581, 6848.554, 23.903), r: 255.7635 },
    { pos: new mp.Vector3(1050.5371, 3064.109, 41.719), r: 288.7552 },
    { pos: new mp.Vector3(-1687.230, -1071.74, 13.152), r: 48.90048 },
    { pos: new mp.Vector3(1048.8238, 3070.458, 41.710), r: 317.0611 },
    { pos: new mp.Vector3(20.904500, -1344.40, 38.316), r: 160.4532 },
    { pos: new mp.Vector3(-1553.815, -249.594, 48.345), r: 46.19425 },
    { pos: new mp.Vector3(1046.6821, 3077.182, 41.717), r: 273.6169 },
    { pos: new mp.Vector3(-1664.403, -1047.56, 13.153), r: 55.43337 },
    { pos: new mp.Vector3(19.674791, -1348.89, 36.513), r: 181.0838 },
    { pos: new mp.Vector3(354.25213, -618.479, 29.223), r: 302.9663 },
    { pos: new mp.Vector3(1539.9915, 837.8652, 77.429), r: 67.54840 },
    { pos: new mp.Vector3(248.76570, 6771.484, 15.935), r: 21.94312 },
    { pos: new mp.Vector3(1535.5552, 830.0090, 77.431), r: 131.5932 },
    { pos: new mp.Vector3(-1653.679, -1035.95, 13.152), r: 37.20539 },
    { pos: new mp.Vector3(45.342884, -1348.81, 36.692), r: 257.5725 },
    { pos: new mp.Vector3(-16.33093, -1385.42, 29.450), r: 46.26910 },
    { pos: new mp.Vector3(-1638.192, -1055.46, 13.152), r: 308.4198 },
    { pos: new mp.Vector3(124.50810, 6693.654, 38.815), r: 349.8880 },
    { pos: new mp.Vector3(-0.975863, -1385.97, 29.292), r: 300.4085 },
    { pos: new mp.Vector3(-1635.468, -1068.01, 13.153), r: 247.8052 },
    { pos: new mp.Vector3(1727.9050, 1518.381, 84.745), r: 153.0699 },
    { pos: new mp.Vector3(-1652.103, -1090.84, 13.144), r: 298.1506 },
    { pos: new mp.Vector3(189.23748, 6636.240, 31.630), r: 151.2141 },
    { pos: new mp.Vector3(-1503.574, -494.464, 35.603), r: 322.9245 },
    { pos: new mp.Vector3(2043.2889, 4915.096, 40.960), r: 134.8205 },
    { pos: new mp.Vector3(-1635.358, -1104.63, 13.024), r: 86.86220 },
    { pos: new mp.Vector3(2029.9168, 4900.895, 42.669), r: 128.7704 },
    { pos: new mp.Vector3(-1627.586, -1109.32, 13.019), r: 32.88709 },
    { pos: new mp.Vector3(2016.8485, 4888.785, 42.714), r: 133.4925 },
    { pos: new mp.Vector3(68.019592, -1385.47, 29.426), r: 270.3629 },
    { pos: new mp.Vector3(2009.6010, 4881.868, 42.933), r: 133.0971 },
    { pos: new mp.Vector3(160.66619, 6558.882, 31.976), r: 253.0619 },
    { pos: new mp.Vector3(-1622.187, -637.792, 32.443), r: 13.73344 },
    { pos: new mp.Vector3(1045.4486, 3083.776, 41.735), r: 281.9507 },
    { pos: new mp.Vector3(92.461425, -1387.13, 29.385), r: 273.3139 },
    { pos: new mp.Vector3(-1471.770, -465.787, 35.341), r: 23.87565 },
    { pos: new mp.Vector3(1043.3729, 3090.934, 41.789), r: 267.2933 },
    { pos: new mp.Vector3(-1493.839, -751.240, 25.765), r: 350.7325 },
    { pos: new mp.Vector3(1049.5397, 3092.566, 41.605), r: 261.3145 },
    { pos: new mp.Vector3(-1579.922, -1058.10, 13.017), r: 47.51738 },
    { pos: new mp.Vector3(110.42833, -1412.14, 29.400), r: 187.2691 },
    { pos: new mp.Vector3(1062.3474, 3096.102, 41.192), r: 284.1031 },
    { pos: new mp.Vector3(-1504.069, -496.399, 35.663), r: 359.4022 },
    { pos: new mp.Vector3(102.64151, -1425.21, 29.310), r: 149.7983 },
    { pos: new mp.Vector3(1074.8272, 3099.742, 40.728), r: 189.1676 },
    { pos: new mp.Vector3(2078.3576, 5197.395, 55.189), r: 305.4881 },
    { pos: new mp.Vector3(2083.5966, 2350.985, 94.263), r: 342.9463 },
    { pos: new mp.Vector3(2091.8498, 5203.943, 56.152), r: 289.2877 },
    { pos: new mp.Vector3(99.029647, -1435.65, 29.291), r: 211.5530 },
    { pos: new mp.Vector3(-1598.534, -1013.53, 13.021), r: 193.4201 },
    { pos: new mp.Vector3(2121.3986, 5214.060, 57.665), r: 288.4588 },
    { pos: new mp.Vector3(-1590.682, -551.170, 35.049), r: 51.48743 },
    { pos: new mp.Vector3(83.878753, -1454.38, 29.281), r: 147.0859 },
    { pos: new mp.Vector3(-1585.836, -547.162, 35.279), r: 10.35661 },
    { pos: new mp.Vector3(2144.9279, 5215.975, 59.231), r: 266.9260 },
    { pos: new mp.Vector3(63.968151, -1478.48, 30.347), r: 147.0859 },
    { pos: new mp.Vector3(-1564.215, -977.720, 13.017), r: 213.7118 },
    { pos: new mp.Vector3(32.870079, -1497.37, 29.305), r: 143.6702 },
    { pos: new mp.Vector3(1087.3614, 3102.326, 40.555), r: 243.2948 },
    { pos: new mp.Vector3(32.325618, -1504.53, 29.384), r: 201.9677 },
    { pos: new mp.Vector3(1093.9544, 3103.844, 40.577), r: 263.5176 },
    { pos: new mp.Vector3(1100.3048, 3106.211, 40.613), r: 268.6665 },
    { pos: new mp.Vector3(16.796983, -1525.04, 29.234), r: 139.3129 },
    { pos: new mp.Vector3(2318.8532, 2506.786, 46.257), r: 45.23045 },
    { pos: new mp.Vector3(-1534.569, -975.783, 13.017), r: 150.0472 },
    { pos: new mp.Vector3(13.279802, -1531.63, 29.286), r: 158.1720 },
    { pos: new mp.Vector3(2181.9287, 5208.279, 60.913), r: 253.6485 },
    { pos: new mp.Vector3(5.2977375, -1538.29, 29.314), r: 311.1176 },
    { pos: new mp.Vector3(1124.5889, 3112.407, 40.558), r: 283.1013 },
    { pos: new mp.Vector3(-1516.969, -983.173, 13.017), r: 92.16402 },
    { pos: new mp.Vector3(2191.7221, 5204.297, 61.385), r: 248.6158 },
    { pos: new mp.Vector3(-2.972533, -1554.50, 29.171), r: 169.7606 },
    { pos: new mp.Vector3(1137.0963, 3117.023, 40.697), r: 288.4945 },
    { pos: new mp.Vector3(2306.8173, 2558.046, 46.668), r: 346.7957 },
    { pos: new mp.Vector3(2211.4365, 5198.031, 61.771), r: 229.3737 },
    { pos: new mp.Vector3(-1567.409, -627.032, 30.011), r: 165.1400 },
    { pos: new mp.Vector3(1150.1904, 3120.119, 40.622), r: 302.5662 },
    { pos: new mp.Vector3(-16.53799, -1572.83, 29.303), r: 142.5941 },
    { pos: new mp.Vector3(-1522.391, -1011.37, 6.1186), r: 154.5097 },
    { pos: new mp.Vector3(1157.2393, 3121.712, 40.612), r: 252.3914 },
    { pos: new mp.Vector3(-1552.807, -631.639, 29.454), r: 188.0760 },
    { pos: new mp.Vector3(-27.64046, -1577.37, 29.328), r: 207.6254 },
    { pos: new mp.Vector3(2256.3586, 5174.816, 59.962), r: 260.8580 },
    { pos: new mp.Vector3(1163.3051, 3123.532, 40.592), r: 275.3850 },
    { pos: new mp.Vector3(1167.4637, 3124.624, 40.653), r: 282.0190 },
    { pos: new mp.Vector3(2326.2844, 2619.033, 46.668), r: 0.584301 },
    { pos: new mp.Vector3(-919.5592, -956.703, 2.1502), r: 334.9768 },
    { pos: new mp.Vector3(2271.0620, 5170.565, 59.904), r: 249.0458 },
    { pos: new mp.Vector3(-25.76842, -1574.35, 29.269), r: 273.7671 },
    { pos: new mp.Vector3(-1537.983, -1023.54, 6.1308), r: 140.1405 },
    { pos: new mp.Vector3(1175.1635, 3126.223, 40.577), r: 279.4315 },
    { pos: new mp.Vector3(2282.3129, 5169.177, 59.548), r: 274.3355 },
    { pos: new mp.Vector3(1181.8044, 3127.391, 40.535), r: 280.5122 },
    { pos: new mp.Vector3(-1476.154, 881.3723, 182.97), r: 286.0212 },
    { pos: new mp.Vector3(-29.10547, -1556.40, 30.683), r: 5.676197 },
    { pos: new mp.Vector3(2296.9423, 5173.687, 59.596), r: 287.2008 },
    { pos: new mp.Vector3(-611.5664, 5746.473, 33.905), r: 83.76802 },
    { pos: new mp.Vector3(-1524.779, -1047.73, 5.4831), r: 220.5808 },
    { pos: new mp.Vector3(1188.2425, 3129.166, 40.587), r: 283.7018 },
    { pos: new mp.Vector3(2302.9333, 5173.048, 58.612), r: 243.6791 },
    { pos: new mp.Vector3(-1492.005, -615.037, 30.582), r: 215.3233 },
    { pos: new mp.Vector3(1195.6848, 3131.458, 40.594), r: 286.6137 },
    { pos: new mp.Vector3(-899.0065, -1063.00, 2.1500), r: 332.2051 },
    { pos: new mp.Vector3(2318.6079, 5159.315, 54.150), r: 216.4814 },
    { pos: new mp.Vector3(1201.5710, 3133.178, 40.592), r: 286.1936 },
    { pos: new mp.Vector3(-34.10680, -1542.32, 30.678), r: 56.60393 },
    { pos: new mp.Vector3(-1524.076, -1079.55, 4.5326), r: 255.1138 },
    { pos: new mp.Vector3(2334.7019, 5142.733, 49.398), r: 225.6400 },
    { pos: new mp.Vector3(1206.8166, 3134.679, 40.635), r: 285.6661 },
    { pos: new mp.Vector3(-1475.826, 887.7521, 182.82), r: 241.9035 },
    { pos: new mp.Vector3(-650.9456, 5758.690, 27.329), r: 230.2902 },
    { pos: new mp.Vector3(-25.54973, -1533.96, 30.676), r: 267.4779 },
    { pos: new mp.Vector3(1213.2202, 3136.462, 40.559), r: 285.4346 },
    { pos: new mp.Vector3(2344.9575, 5132.786, 48.013), r: 227.0160 },
    { pos: new mp.Vector3(-1466.428, -597.578, 30.887), r: 258.4333 },
    { pos: new mp.Vector3(2351.5927, 5126.042, 48.170), r: 221.2561 },
    { pos: new mp.Vector3(1220.0517, 3138.086, 40.572), r: 283.7498 },
    { pos: new mp.Vector3(1226.2861, 3139.857, 40.585), r: 284.9265 },
    { pos: new mp.Vector3(-1494.453, 895.2659, 182.60), r: 187.7537 },
    { pos: new mp.Vector3(-1561.140, -1091.30, 5.2184), r: 340.7969 },
    { pos: new mp.Vector3(2355.8564, 5110.243, 47.455), r: 162.6428 },
    { pos: new mp.Vector3(-581.9578, 5832.920, 30.959), r: 131.3260 },
    { pos: new mp.Vector3(1231.9265, 3141.736, 40.564), r: 286.9717 },
    { pos: new mp.Vector3(2259.0148, 2995.061, 45.960), r: 340.5078 },
    { pos: new mp.Vector3(2346.8645, 5100.958, 46.875), r: 110.9954 },
    { pos: new mp.Vector3(-1083.302, -1160.96, 2.1585), r: 220.4129 },
    { pos: new mp.Vector3(2337.1914, 5091.610, 46.236), r: 124.8406 },
    { pos: new mp.Vector3(1238.7611, 3143.569, 40.575), r: 286.9125 },
    { pos: new mp.Vector3(-628.2825, 5917.304, 19.824), r: 228.6076 },
    { pos: new mp.Vector3(-56.02394, -1509.42, 32.152), r: 349.7391 },
    { pos: new mp.Vector3(-1440.839, -582.429, 30.732), r: 213.6406 },
    { pos: new mp.Vector3(1244.4509, 3145.642, 40.592), r: 275.9744 },
    { pos: new mp.Vector3(2320.6215, 5077.248, 45.801), r: 88.20351 },
    { pos: new mp.Vector3(-58.86946, -1507.79, 32.506), r: 332.7424 },
    { pos: new mp.Vector3(1250.8040, 3146.774, 40.586), r: 278.5631 },
    { pos: new mp.Vector3(2307.8730, 5081.616, 46.312), r: 60.62871 },
    { pos: new mp.Vector3(-1142.152, -1071.85, 2.1501), r: 292.7497 },
    { pos: new mp.Vector3(-1523.305, 876.1807, 181.90), r: 329.3544 },
    { pos: new mp.Vector3(1257.2633, 3149.006, 40.631), r: 288.6057 },
    { pos: new mp.Vector3(-670.9235, 5871.697, 16.837), r: 42.33005 },
    { pos: new mp.Vector3(2290.1630, 5084.139, 47.627), r: 79.98875 },
    { pos: new mp.Vector3(1263.6146, 3149.903, 40.616), r: 281.5618 },
    { pos: new mp.Vector3(2283.0073, 5086.057, 47.979), r: 67.50165 },
    { pos: new mp.Vector3(1269.9957, 3151.503, 40.534), r: 282.9223 },
    { pos: new mp.Vector3(-1391.977, -588.225, 30.251), r: 25.67448 },
    { pos: new mp.Vector3(2280.8342, 5095.740, 48.741), r: 43.74679 },
    { pos: new mp.Vector3(-1610.429, -1094.39, 4.5554), r: 211.9766 },
    { pos: new mp.Vector3(1276.0606, 3153.226, 40.602), r: 285.4200 },
    { pos: new mp.Vector3(-568.0791, 5749.313, 39.890), r: 212.1001 },
    { pos: new mp.Vector3(1282.4621, 3155.062, 40.596), r: 285.5898 },
    { pos: new mp.Vector3(-1149.466, -1029.18, 2.1501), r: 280.2127 },
    { pos: new mp.Vector3(-1384.218, -584.216, 30.191), r: 330.3479 },
    { pos: new mp.Vector3(2265.6376, 5112.091, 50.577), r: 47.26483 },
    { pos: new mp.Vector3(-1614.942, -1121.43, 2.5793), r: 168.6516 },
    { pos: new mp.Vector3(1288.4166, 3156.505, 40.585), r: 285.4342 },
    { pos: new mp.Vector3(2259.8002, 5117.300, 51.431), r: 44.93865 },
    { pos: new mp.Vector3(-1552.845, 872.4140, 181.69), r: 235.1847 },
    { pos: new mp.Vector3(1294.6352, 3158.281, 40.568), r: 285.5222 },
    { pos: new mp.Vector3(2250.9091, 5124.921, 53.226), r: 49.27080 },
    { pos: new mp.Vector3(-66.94575, -1516.24, 33.436), r: 202.3731 },
    { pos: new mp.Vector3(2239.9353, 5133.105, 55.301), r: 48.02119 },
    { pos: new mp.Vector3(-1590.829, -1134.51, 2.1554), r: 145.5069 },
    { pos: new mp.Vector3(-1383.352, -528.981, 30.739), r: 106.1407 },
    { pos: new mp.Vector3(-59.67695, -1522.21, 33.445), r: 226.0918 },
    { pos: new mp.Vector3(2226.9111, 5141.104, 56.223), r: 62.49674 },
    { pos: new mp.Vector3(-61.96848, -1526.46, 34.439), r: 107.0484 },
    { pos: new mp.Vector3(1302.2152, 3160.507, 40.537), r: 245.4844 },
    { pos: new mp.Vector3(-1489.525, 812.3440, 182.01), r: 11.13619 },
    { pos: new mp.Vector3(-67.92423, -1527.52, 34.235), r: 238.3383 },
    { pos: new mp.Vector3(2211.3601, 5144.147, 56.308), r: 140.9985 },
    { pos: new mp.Vector3(-785.0466, 5559.239, 33.057), r: 114.2289 },
    { pos: new mp.Vector3(-1562.900, -1155.52, 2.3017), r: 119.0345 },
    { pos: new mp.Vector3(-76.44270, -1520.61, 34.245), r: 98.12978 },
    { pos: new mp.Vector3(-1365.062, -530.691, 30.344), r: 207.5135 },
    { pos: new mp.Vector3(2197.4399, 5129.145, 51.910), r: 136.0451 },
    { pos: new mp.Vector3(-1533.911, -1228.98, 2.2848), r: 160.2616 },
    { pos: new mp.Vector3(-1504.058, 804.2167, 181.83), r: 17.25123 },
    { pos: new mp.Vector3(1314.9976, 3163.428, 40.529), r: 287.2894 },
    { pos: new mp.Vector3(-82.77230, -1526.60, 34.237), r: 184.2384 },
    { pos: new mp.Vector3(1320.0810, 3164.591, 40.532), r: 283.7467 },
    { pos: new mp.Vector3(2349.4829, 5181.753, 53.260), r: 303.5592 },
    { pos: new mp.Vector3(1326.8148, 3166.364, 40.534), r: 286.2057 },
    { pos: new mp.Vector3(-1374.317, -500.594, 33.157), r: 180.3828 },
    { pos: new mp.Vector3(-729.7680, 5583.100, 36.064), r: 308.2171 },
    { pos: new mp.Vector3(-1520.187, -1270.17, 1.9321), r: 102.2124 },
    { pos: new mp.Vector3(-89.30957, -1520.52, 33.767), r: 17.08455 },
    { pos: new mp.Vector3(1331.6192, 3167.702, 40.537), r: 285.1217 },
    { pos: new mp.Vector3(2365.8923, 5172.776, 50.878), r: 229.6634 },
    { pos: new mp.Vector3(2382.7597, 5167.265, 49.445), r: 219.6425 },
    { pos: new mp.Vector3(1686.5214, 3278.607, 41.091), r: 254.0810 },
    { pos: new mp.Vector3(-1569.844, 796.3355, 189.05), r: 344.0324 },
    { pos: new mp.Vector3(-1507.356, -1307.46, 2.0713), r: 113.4181 },
    { pos: new mp.Vector3(-202.9129, -1638.79, 33.837), r: 144.2939 },
    { pos: new mp.Vector3(-1412.455, -491.633, 33.193), r: 124.8011 },
    { pos: new mp.Vector3(2404.7390, 5137.118, 47.061), r: 224.1690 },
    { pos: new mp.Vector3(1701.1008, 3287.582, 41.147), r: 294.2715 },
    { pos: new mp.Vector3(-1513.219, -1324.15, 2.1223), r: 120.2529 },
    { pos: new mp.Vector3(-1578.913, 823.2888, 185.99), r: 339.9067 },
    { pos: new mp.Vector3(-1406.532, -501.082, 32.632), r: 162.8311 },
    { pos: new mp.Vector3(-225.8848, -1631.98, 33.673), r: 323.1592 },
    { pos: new mp.Vector3(-233.5098, -1630.93, 34.051), r: 0.452002 },
    { pos: new mp.Vector3(1709.7308, 3279.859, 41.376), r: 246.4108 },
    { pos: new mp.Vector3(-1403.701, -505.119, 32.385), r: 128.2881 },
    { pos: new mp.Vector3(2414.9047, 5127.746, 47.287), r: 228.0108 },
    { pos: new mp.Vector3(-1466.293, -1392.81, 2.4892), r: 164.6588 },
    { pos: new mp.Vector3(1722.2307, 3284.227, 41.064), r: 255.6786 },
    { pos: new mp.Vector3(-823.2405, -1055.14, 12.880), r: 244.1426 },
    { pos: new mp.Vector3(-1456.076, 822.5110, 184.09), r: 292.5856 },
    { pos: new mp.Vector3(-234.1071, -1665.12, 33.911), r: 211.0179 },
    { pos: new mp.Vector3(-1552.433, 4434.454, 9.5042), r: 177.2126 },
    { pos: new mp.Vector3(-1440.740, -497.608, 33.706), r: 335.4608 },
    { pos: new mp.Vector3(2438.5634, 5116.543, 46.995), r: 241.1033 },
    { pos: new mp.Vector3(-221.4861, -1668.39, 34.463), r: 220.1454 },
    { pos: new mp.Vector3(-1462.361, 924.5302, 179.31), r: 124.3168 },
    { pos: new mp.Vector3(1763.7507, 3296.241, 41.171), r: 277.9765 },
    { pos: new mp.Vector3(-1427.826, -1386.86, 4.0180), r: 316.3962 },
    { pos: new mp.Vector3(-1401.708, -548.946, 30.490), r: 305.0928 },
    { pos: new mp.Vector3(-215.5827, -1669.00, 34.463), r: 293.0383 },
    { pos: new mp.Vector3(-471.5272, -1121.82, 27.671), r: 69.06960 },
    { pos: new mp.Vector3(-1456.118, 919.9449, 179.84), r: 188.4063 },
    { pos: new mp.Vector3(2464.3295, 5099.694, 46.444), r: 131.3380 },
    { pos: new mp.Vector3(-1452.114, 899.7040, 182.41), r: 149.9076 },
    { pos: new mp.Vector3(-214.4980, -1655.69, 34.464), r: 6.305083 },
    { pos: new mp.Vector3(1767.0708, 3300.064, 41.159), r: 283.4187 },
    { pos: new mp.Vector3(-1537.508, 4469.359, 18.479), r: 345.7067 },
    { pos: new mp.Vector3(-1450.052, 885.8040, 183.05), r: 157.0981 },
    { pos: new mp.Vector3(2479.3439, 5092.558, 45.666), r: 282.8749 },
    { pos: new mp.Vector3(-207.4490, -1650.72, 34.463), r: 248.0525 },
    { pos: new mp.Vector3(-1391.177, -1387.15, 4.6849), r: 152.4753 },
    { pos: new mp.Vector3(2499.6384, 5087.145, 44.483), r: 223.5833 },
    { pos: new mp.Vector3(-1384.784, 735.9832, 182.96), r: 346.2122 },
    { pos: new mp.Vector3(-1392.691, -1397.74, 4.7214), r: 9.657229 },
    { pos: new mp.Vector3(1779.9686, 3322.469, 41.305), r: 266.5920 },
    { pos: new mp.Vector3(2521.9821, 5081.166, 43.964), r: 248.5676 },
    { pos: new mp.Vector3(-200.6425, -1674.48, 34.424), r: 184.1167 },
    { pos: new mp.Vector3(2538.3747, 5080.009, 44.023), r: 281.1427 },
    { pos: new mp.Vector3(-1396.201, 738.3948, 182.87), r: 349.4425 },
    { pos: new mp.Vector3(-1395.974, -1421.89, 3.7729), r: 329.6446 },
    { pos: new mp.Vector3(-1521.203, -454.243, 35.519), r: 211.9183 },
    { pos: new mp.Vector3(2553.4809, 5082.556, 44.342), r: 273.6938 },
    { pos: new mp.Vector3(-1499.128, 4386.998, 19.216), r: 348.4707 },
    { pos: new mp.Vector3(1770.3854, 3337.514, 41.433), r: 339.9154 },
    { pos: new mp.Vector3(-1342.181, 741.4625, 185.00), r: 17.81254 },
    { pos: new mp.Vector3(-178.9609, -1707.46, 32.147), r: 212.3640 },
    { pos: new mp.Vector3(2566.1533, 5084.280, 44.533), r: 279.0747 },
    { pos: new mp.Vector3(-1062.680, -1039.33, 2.0798), r: 308.7610 },
    { pos: new mp.Vector3(2580.9794, 5086.372, 44.895), r: 287.2330 },
    { pos: new mp.Vector3(-1377.074, -1438.36, 3.6445), r: 53.17227 },
    { pos: new mp.Vector3(-1556.469, 4380.946, 5.0364), r: 344.0706 },
    { pos: new mp.Vector3(-1152.328, 942.2536, 198.64), r: 78.46990 },
    { pos: new mp.Vector3(-1054.819, -1018.28, 2.0844), r: 210.1611 },
    { pos: new mp.Vector3(-174.9335, -1755.74, 30.054), r: 125.9037 },
    { pos: new mp.Vector3(-1163.125, 928.6611, 197.51), r: 84.87902 },
    { pos: new mp.Vector3(-1380.200, -1457.31, 4.2428), r: 313.5190 },
    { pos: new mp.Vector3(2626.6088, 5094.552, 45.211), r: 245.0090 },
    { pos: new mp.Vector3(530.65905, -197.785, 52.929), r: 21.72527 },
    { pos: new mp.Vector3(-1634.544, 4375.967, 7.5122), r: 284.0563 },
    { pos: new mp.Vector3(2631.9926, 5087.564, 45.068), r: 200.1787 },
    { pos: new mp.Vector3(-1370.530, -576.059, 30.225), r: 41.21480 },
    { pos: new mp.Vector3(-1424.133, -1535.65, 2.0521), r: 145.8448 },
    { pos: new mp.Vector3(2630.2680, 5075.901, 45.197), r: 163.1673 },
    { pos: new mp.Vector3(1818.7521, 3713.500, 33.619), r: 189.7138 },
    { pos: new mp.Vector3(2637.0043, 5064.433, 44.936), r: 233.2785 },
    { pos: new mp.Vector3(-192.7493, -1717.95, 32.664), r: 319.6156 },
    { pos: new mp.Vector3(-1374.336, -1618.07, 2.1514), r: 125.3775 },
    { pos: new mp.Vector3(485.81338, -175.019, 54.564), r: 325.3985 },
    { pos: new mp.Vector3(2637.8937, 5055.072, 44.962), r: 155.8241 },
    { pos: new mp.Vector3(1814.4726, 3711.125, 33.732), r: 211.2580 },
    { pos: new mp.Vector3(-1613.468, 4364.154, 3.6679), r: 320.2603 },
    { pos: new mp.Vector3(-196.1561, -1712.91, 32.664), r: 354.0502 },
    { pos: new mp.Vector3(2642.0642, 5044.279, 44.928), r: 215.5081 },
    { pos: new mp.Vector3(-1013.831, -993.940, 2.1501), r: 268.7706 },
    { pos: new mp.Vector3(-1358.669, -600.293, 28.618), r: 262.5802 },
    { pos: new mp.Vector3(2642.1333, 5029.413, 44.921), r: 172.2683 },
    { pos: new mp.Vector3(-1287.213, -1757.83, 2.2524), r: 129.4669 },
    { pos: new mp.Vector3(2649.0776, 5016.674, 44.886), r: 235.0354 },
    { pos: new mp.Vector3(2648.8400, 5006.638, 44.829), r: 148.4879 },
    { pos: new mp.Vector3(-987.0299, -999.469, 2.1503), r: 74.12023 },
    { pos: new mp.Vector3(2653.6218, 4999.080, 44.823), r: 235.8984 },
    { pos: new mp.Vector3(-1331.896, -630.436, 27.425), r: 220.1482 },
    { pos: new mp.Vector3(-1207.880, -1814.49, 3.9085), r: 323.2661 },
    { pos: new mp.Vector3(-219.0272, -1719.44, 32.791), r: 89.47540 },
    { pos: new mp.Vector3(2658.6074, 4989.982, 45.016), r: 179.7857 },
    { pos: new mp.Vector3(1811.3016, 3713.540, 35.876), r: 211.4599 },
    { pos: new mp.Vector3(2656.9353, 4979.040, 44.808), r: 168.8830 },
    { pos: new mp.Vector3(-1336.123, -633.599, 27.443), r: 208.6688 },
    { pos: new mp.Vector3(2662.7558, 4959.369, 44.894), r: 203.8766 },
    { pos: new mp.Vector3(-230.4997, -1724.07, 32.811), r: 65.28816 },
    { pos: new mp.Vector3(-1268.230, -1917.52, 5.8590), r: 321.0124 },
    { pos: new mp.Vector3(-919.8054, 1108.672, 219.73), r: 300.5414 },
    { pos: new mp.Vector3(2665.2009, 4944.130, 44.760), r: 168.0903 },
    { pos: new mp.Vector3(1478.8270, 1754.722, 107.95), r: 296.3624 },
    { pos: new mp.Vector3(-297.6148, -345.017, 30.036), r: 259.8641 },
    { pos: new mp.Vector3(-1270.145, -1916.02, 5.8615), r: 306.7869 },
    { pos: new mp.Vector3(2671.3959, 4929.334, 44.744), r: 204.3097 },
    { pos: new mp.Vector3(2670.7514, 4917.267, 44.738), r: 171.9551 },
    { pos: new mp.Vector3(1800.5563, 3721.914, 35.931), r: 99.97224 },
    { pos: new mp.Vector3(-1306.975, -674.653, 26.307), r: 37.58512 },
    { pos: new mp.Vector3(2676.2521, 4905.614, 44.741), r: 222.2475 },
    { pos: new mp.Vector3(-700.9773, 932.3648, 234.63), r: 12.31719 },
    { pos: new mp.Vector3(-1117.846, -1767.71, 5.8152), r: 41.71870 },
    { pos: new mp.Vector3(1393.4449, 1749.757, 97.504), r: 95.33373 },
    { pos: new mp.Vector3(-121.4918, -1770.59, 29.822), r: 254.8766 },
    { pos: new mp.Vector3(-115.4603, -1770.93, 29.859), r: 227.5800 },
    { pos: new mp.Vector3(-1179.264, -1762.25, 3.9028), r: 299.8984 },
    { pos: new mp.Vector3(2698.2905, 4809.581, 44.387), r: 198.0192 },
    { pos: new mp.Vector3(-525.6547, -87.2666, 39.966), r: 225.7752 },
    { pos: new mp.Vector3(1405.5516, 1814.504, 101.88), r: 12.72463 },
    { pos: new mp.Vector3(-1304.574, -707.059, 25.322), r: 351.8323 },
    { pos: new mp.Vector3(-1186.903, -1739.51, 4.2424), r: 256.7542 },
    { pos: new mp.Vector3(2698.9812, 4788.133, 44.432), r: 187.9354 },
    { pos: new mp.Vector3(-954.6994, -960.730, 2.1500), r: 71.18412 },
    { pos: new mp.Vector3(1803.1295, 3728.735, 35.727), r: 50.53174 },
    { pos: new mp.Vector3(-43.92703, -1831.06, 26.362), r: 224.4311 },
    { pos: new mp.Vector3(2706.1203, 4771.404, 44.390), r: 202.6013 },
    { pos: new mp.Vector3(-51.04633, -1824.53, 26.663), r: 54.29168 },
    { pos: new mp.Vector3(-1086.398, -1696.93, 4.7392), r: 218.8231 },
    { pos: new mp.Vector3(2712.3576, 4727.166, 44.320), r: 189.3728 },
    { pos: new mp.Vector3(1454.3046, 1865.533, 109.39), r: 238.4303 },
    { pos: new mp.Vector3(-1048.072, -1691.88, 4.5276), r: 54.68429 },
    { pos: new mp.Vector3(1808.9920, 3732.205, 35.751), r: 352.8990 },
    { pos: new mp.Vector3(-967.0532, -935.775, 2.1453), r: 153.6477 },
    { pos: new mp.Vector3(1584.9587, 1879.274, 101.91), r: 102.1550 },
    { pos: new mp.Vector3(-1298.613, -693.358, 25.050), r: 127.1131 },
    { pos: new mp.Vector3(-1033.729, -1663.84, 4.7646), r: 52.70338 },
    { pos: new mp.Vector3(2725.6757, 4680.264, 44.256), r: 44.62095 },
    { pos: new mp.Vector3(-13.78430, -1856.92, 24.727), r: 127.6705 },
    { pos: new mp.Vector3(1685.2818, 1646.956, 106.14), r: 252.3891 },
    { pos: new mp.Vector3(-988.3902, -904.356, 2.1504), r: 236.8291 },
    { pos: new mp.Vector3(2729.7131, 4665.309, 44.346), r: 203.0700 },
    { pos: new mp.Vector3(-1622.901, 77.22365, 61.770), r: 153.6609 },
    { pos: new mp.Vector3(-19.30522, -1854.25, 25.053), r: 97.41268 },
    { pos: new mp.Vector3(-1291.120, -686.752, 25.003), r: 299.2432 },
    { pos: new mp.Vector3(-1017.285, -1647.34, 4.5729), r: 3.415959 },
    { pos: new mp.Vector3(-1610.457, 69.42032, 61.227), r: 164.3314 },
    { pos: new mp.Vector3(1816.5362, 3730.352, 35.876), r: 359.9948 },
    { pos: new mp.Vector3(-32.00336, -1841.25, 27.051), r: 97.41268 },
    { pos: new mp.Vector3(2729.5312, 4656.930, 44.400), r: 167.0308 },
    { pos: new mp.Vector3(-1277.912, -711.492, 23.388), r: 216.4670 },
    { pos: new mp.Vector3(18.953395, -1848.04, 25.619), r: 97.41268 },
    { pos: new mp.Vector3(1502.2049, 1850.545, 107.16), r: 67.53223 },
    { pos: new mp.Vector3(-986.1858, -1628.88, 5.1203), r: 215.3138 },
    { pos: new mp.Vector3(-1595.763, 97.32575, 60.772), r: 73.39978 },
    { pos: new mp.Vector3(28.933256, -1856.54, 24.836), r: 97.41268 },
    { pos: new mp.Vector3(2736.8078, 4635.703, 44.605), r: 197.4965 },
    { pos: new mp.Vector3(1820.0354, 3724.223, 35.907), r: 276.0539 },
    { pos: new mp.Vector3(46.669464, -1869.16, 23.945), r: 97.41268 },
    { pos: new mp.Vector3(-982.2128, -1607.03, 5.2726), r: 32.01875 },
    { pos: new mp.Vector3(2736.5646, 4618.934, 44.808), r: 185.0945 },
    { pos: new mp.Vector3(-1594.744, 140.5243, 59.547), r: 243.9103 },
    { pos: new mp.Vector3(-1234.889, -763.319, 19.217), r: 224.0721 },
    { pos: new mp.Vector3(2742.7233, 4608.357, 45.091), r: 209.8285 },
    { pos: new mp.Vector3(66.270614, -1876.36, 23.810), r: 97.41268 },
    { pos: new mp.Vector3(1533.5290, 1830.883, 105.08), r: 345.4908 },
    { pos: new mp.Vector3(-1627.743, 122.9171, 61.853), r: 214.8939 },
    { pos: new mp.Vector3(-962.7293, -1599.84, 5.2727), r: 23.35833 },
    { pos: new mp.Vector3(67.480255, -1854.79, 23.506), r: 97.41268 },
    { pos: new mp.Vector3(2742.5573, 4590.252, 45.263), r: 142.8525 },
    { pos: new mp.Vector3(-1047.512, -903.133, 5.3494), r: 43.74424 },
    { pos: new mp.Vector3(41.715106, -1905.67, 22.744), r: 97.41268 },
    { pos: new mp.Vector3(-1587.026, 88.42470, 59.163), r: 328.4612 },
    { pos: new mp.Vector3(1817.6422, 3717.718, 35.707), r: 219.7446 },
    { pos: new mp.Vector3(-1238.950, -766.273, 19.237), r: 219.1823 },
    { pos: new mp.Vector3(-1571.283, 88.17139, 58.697), r: 345.2751 },
    { pos: new mp.Vector3(54.470310, -1918.47, 22.411), r: 97.41268 },
    { pos: new mp.Vector3(2748.7236, 4559.442, 46.150), r: 150.5507 },
    { pos: new mp.Vector3(-942.0789, -1574.01, 5.2727), r: 102.7994 },
    { pos: new mp.Vector3(1381.9167, 1644.106, 101.03), r: 324.6979 },
    { pos: new mp.Vector3(-1570.726, 106.0459, 57.772), r: 331.0313 },
    { pos: new mp.Vector3(2757.3818, 4545.238, 46.352), r: 224.7644 },
    { pos: new mp.Vector3(96.992347, -1912.61, 21.704), r: 97.41268 },
    { pos: new mp.Vector3(-1220.837, -785.746, 17.590), r: 35.59543 },
    { pos: new mp.Vector3(1804.3925, 3715.481, 35.868), r: 179.0885 },
    { pos: new mp.Vector3(1304.2690, 1675.901, 88.384), r: 120.5421 },
    { pos: new mp.Vector3(-1075.624, -856.031, 5.0415), r: 312.6831 },
    { pos: new mp.Vector3(-990.3378, -1586.85, 5.2797), r: 206.0956 },
    { pos: new mp.Vector3(-1468.75, .277809143, 53.357), r: 300.0570 },
    { pos: new mp.Vector3(116.66432, -1921.86, 22.166), r: 97.41268 },
    { pos: new mp.Vector3(-1067.186, -849.672, 5.0417), r: 86.46678 },
    { pos: new mp.Vector3(-1470.996, 80.84736, 54.106), r: 12.68467 },
    { pos: new mp.Vector3(-1192.238, -822.557, 14.691), r: 215.6854 },
    { pos: new mp.Vector3(1780.2158, 3731.995, 34.070), r: 96.14093 },
    { pos: new mp.Vector3(124.85041, -1932.19, 21.998), r: 97.41268 },
    { pos: new mp.Vector3(1304.8325, 1772.500, 87.686), r: 255.6309 },
    { pos: new mp.Vector3(-1481.231, 82.01240, 54.428), r: 164.9924 },
    { pos: new mp.Vector3(-997.7844, -1574.05, 5.2796), r: 196.7327 },
    { pos: new mp.Vector3(116.11137, -1956.69, 21.945), r: 97.41268 },
    { pos: new mp.Vector3(-1492.185, 82.18758, 54.967), r: 34.75865 },
    { pos: new mp.Vector3(1746.0753, 3712.240, 34.147), r: 109.7904 },
    { pos: new mp.Vector3(94.979316, -1954.88, 22.249), r: 97.41268 },
    { pos: new mp.Vector3(-1017.984, -1586.02, 5.1053), r: 309.1218 },
    { pos: new mp.Vector3(-1503.770, 81.41236, 55.414), r: 294.1760 },
    { pos: new mp.Vector3(92.157661, -1952.11, 22.249), r: 97.41268 },
    { pos: new mp.Vector3(1729.5748, 3707.761, 34.179), r: 8.004481 },
    { pos: new mp.Vector3(1319.8811, 1848.341, 91.670), r: 106.0137 },
    { pos: new mp.Vector3(87.677124, -1948.23, 21.745), r: 97.41268 },
    { pos: new mp.Vector3(-1023.005, -1597.84, 5.2139), r: 274.7365 },
    { pos: new mp.Vector3(2758.0166, 4521.453, 46.904), r: 172.1442 },
    { pos: new mp.Vector3(-1078.238, -921.828, 3.5198), r: 53.76409 },
    { pos: new mp.Vector3(-1499.033, 167.0441, 54.726), r: 137.1847 },
    { pos: new mp.Vector3(1721.8920, 3704.807, 34.185), r: 37.55930 },
    { pos: new mp.Vector3(-1109.881, -760.022, 19.339), r: 253.5517 },
    { pos: new mp.Vector3(2767.4987, 4509.335, 47.169), r: 219.8193 },
    { pos: new mp.Vector3(-1490.623, 165.0238, 54.860), r: 242.0467 },
    { pos: new mp.Vector3(1298.6456, 1904.276, 85.837), r: 46.77587 },
    { pos: new mp.Vector3(1715.6185, 3717.214, 34.194), r: 301.6707 },
    { pos: new mp.Vector3(-1136.371, -729.079, 20.709), r: 337.3791 },
    { pos: new mp.Vector3(2771.0800, 4483.949, 47.737), r: 174.3105 },
    { pos: new mp.Vector3(-1466.936, 144.0709, 53.638), r: 166.9163 },
    { pos: new mp.Vector3(2782.5876, 4461.154, 48.024), r: 207.8195 },
    { pos: new mp.Vector3(-1148.963, -715.011, 21.317), r: 333.7237 },
    { pos: new mp.Vector3(1234.7491, 1744.399, 79.791), r: 125.9830 },
    { pos: new mp.Vector3(2778.7321, 4444.408, 48.362), r: 171.5839 },
    { pos: new mp.Vector3(-1104.605, -965.539, 2.4183), r: 311.3765 },
    { pos: new mp.Vector3(2783.5815, 4438.844, 48.670), r: 248.5542 },
    { pos: new mp.Vector3(1698.6851, 3750.390, 34.377), r: 241.6193 },
    { pos: new mp.Vector3(-1455.787, 210.6844, 57.575), r: 192.6423 },
    { pos: new mp.Vector3(-1158.667, -703.939, 21.731), r: 306.1252 },
    { pos: new mp.Vector3(2785.8330, 4429.007, 48.914), r: 157.3582 },
    { pos: new mp.Vector3(494.32199, -1307.15, 29.288), r: 188.5793 },
    { pos: new mp.Vector3(211.73963, -998.422, 29.291), r: 308.4727 },
    { pos: new mp.Vector3(-1031.182, -1632.10, 4.6164), r: 143.0322 },
    { pos: new mp.Vector3(2789.7819, 4420.109, 48.942), r: 300.5415 },
    { pos: new mp.Vector3(-1434.977, 231.0277, 59.559), r: 202.9815 },
    { pos: new mp.Vector3(1301.5325, 1469.188, 98.974), r: 78.99440 },
    { pos: new mp.Vector3(502.49148, -1320.72, 29.353), r: 203.8677 },
    { pos: new mp.Vector3(1700.5260, 3753.347, 34.357), r: 227.4009 },
    { pos: new mp.Vector3(-1437.851, 235.9254, 59.863), r: 173.0303 },
    { pos: new mp.Vector3(2796.3405, 4396.601, 48.976), r: 137.3711 },
    { pos: new mp.Vector3(216.25569, -985.185, 29.791), r: 209.1080 },
    { pos: new mp.Vector3(-1086.959, -999.570, 2.1881), r: 197.1795 },
    { pos: new mp.Vector3(489.80322, -1322.82, 29.126), r: 153.7975 },
    { pos: new mp.Vector3(-1061.386, -1604.30, 4.3810), r: 176.5038 },
    { pos: new mp.Vector3(2794.9121, 4381.884, 49.380), r: 200.3836 },
    { pos: new mp.Vector3(2800.9372, 4375.479, 49.837), r: 233.0451 },
    { pos: new mp.Vector3(-1081.804, -788.758, 19.264), r: 0.054383 },
    { pos: new mp.Vector3(2806.6806, 4367.021, 49.827), r: 202.4058 },
    { pos: new mp.Vector3(244.35844, -1258.36, 29.456), r: 358.5065 },
    { pos: new mp.Vector3(-1069.680, -1026.49, 2.0672), r: 189.6439 },
    { pos: new mp.Vector3(2805.1599, 4347.154, 49.879), r: 182.7672 },
    { pos: new mp.Vector3(-1052.321, -815.626, 19.259), r: 339.0266 },
    { pos: new mp.Vector3(2815.0080, 4336.531, 50.160), r: 234.7905 },
    { pos: new mp.Vector3(1697.4843, 3774.807, 34.761), r: 255.1210 },
    { pos: new mp.Vector3(-1084.650, -1036.77, 2.1503), r: 80.77040 },
    { pos: new mp.Vector3(2815.9287, 4319.235, 50.042), r: 201.3498 },
    { pos: new mp.Vector3(-1091.310, -1560.66, 4.3153), r: 85.96451 },
    { pos: new mp.Vector3(-1033.467, -823.268, 19.237), r: 329.3038 },
    { pos: new mp.Vector3(-85.97970, -1752.88, 29.639), r: 328.0102 },
    { pos: new mp.Vector3(2828.6857, 4306.051, 50.074), r: 226.2498 },
    { pos: new mp.Vector3(1711.6530, 3784.238, 34.672), r: 249.7700 },
    { pos: new mp.Vector3(499.20596, -1324.43, 29.374), r: 287.2628 },
    { pos: new mp.Vector3(2826.6140, 4289.115, 50.134), r: 180.2657 },
    { pos: new mp.Vector3(487.08056, -1339.35, 29.256), r: 143.0951 },
    { pos: new mp.Vector3(-1062.605, -1536.25, 4.9898), r: 39.70552 },
    { pos: new mp.Vector3(-1086.409, -1065.99, 2.1503), r: 297.2241 },
    { pos: new mp.Vector3(2838.3395, 4276.590, 50.135), r: 231.2273 },
    { pos: new mp.Vector3(2838.1389, 4263.818, 50.152), r: 158.5101 },
    { pos: new mp.Vector3(1733.4482, 3759.286, 33.822), r: 280.7341 },
    { pos: new mp.Vector3(2841.0346, 4248.427, 50.025), r: 212.0911 },
    { pos: new mp.Vector3(489.37051, -1272.49, 29.537), r: 9.192323 },
    { pos: new mp.Vector3(-988.0607, -837.057, 15.254), r: 359.0659 },
    { pos: new mp.Vector3(491.17993, -1298.08, 30.288), r: 9.192323 },
    { pos: new mp.Vector3(2852.8615, 4234.902, 49.951), r: 205.6775 },
    { pos: new mp.Vector3(1747.4173, 3768.126, 33.763), r: 143.7466 },
    { pos: new mp.Vector3(-248.2105, -2061.12, 27.881), r: 279.4326 },
    { pos: new mp.Vector3(-1047.013, -1538.40, 5.1239), r: 315.9971 },
    { pos: new mp.Vector3(469.29028, -1273.60, 30.779), r: 9.192323 },
    { pos: new mp.Vector3(2856.0410, 4210.040, 49.963), r: 192.4307 },
    { pos: new mp.Vector3(1751.1663, 3778.946, 34.091), r: 128.3585 },
    { pos: new mp.Vector3(-1027.877, -892.813, 5.4312), r: 29.80001 },
    { pos: new mp.Vector3(2864.8798, 4199.322, 50.029), r: 211.1048 },
    { pos: new mp.Vector3(456.31665, -1299.38, 29.297), r: 188.2634 },
    { pos: new mp.Vector3(2866.6213, 4179.861, 50.032), r: 199.0774 },
    { pos: new mp.Vector3(1812.8067, -2566.48, 77.988), r: 184.0136 },
    { pos: new mp.Vector3(1738.9509, 3776.216, 34.078), r: 119.8944 },
    { pos: new mp.Vector3(2874.9555, 4168.990, 50.033), r: 213.3261 },
    { pos: new mp.Vector3(-219.6448, -2027.74, 27.755), r: 193.2352 },
    { pos: new mp.Vector3(2881.3027, 4152.218, 50.170), r: 202.3888 },
    { pos: new mp.Vector3(-1028.427, -901.220, 3.7213), r: 45.44879 },
    { pos: new mp.Vector3(2880.1589, 4142.125, 50.184), r: 176.8931 },
    { pos: new mp.Vector3(2886.1184, 4133.603, 50.217), r: 218.1659 },
    { pos: new mp.Vector3(1726.8765, 3794.159, 34.696), r: 125.3648 },
    { pos: new mp.Vector3(1836.0330, -2378.19, 143.22), r: 211.9640 },
    { pos: new mp.Vector3(-1048.060, -902.957, 5.3494), r: 22.48809 },
    { pos: new mp.Vector3(1689.4451, -2532.15, 84.124), r: 136.3704 },
    { pos: new mp.Vector3(585.94976, -1587.57, 28.218), r: 38.95013 },
    { pos: new mp.Vector3(-1037.233, -1551.91, 5.1152), r: 282.9431 },
    { pos: new mp.Vector3(592.18829, -1638.53, 26.364), r: 38.95013 },
    { pos: new mp.Vector3(1543.1695, -2642.48, 42.133), r: 88.60756 },
    { pos: new mp.Vector3(541.64477, -1654.10, 29.612), r: 38.95013 },
    { pos: new mp.Vector3(-1077.995, -918.281, 3.6913), r: 35.75325 },
    { pos: new mp.Vector3(1717.7022, 3807.976, 34.876), r: 72.66105 },
    { pos: new mp.Vector3(-1024.805, -1569.24, 5.1585), r: 300.1719 },
    { pos: new mp.Vector3(1419.0604, -2642.90, 44.813), r: 134.3431 },
    { pos: new mp.Vector3(533.70800, -1663.00, 29.245), r: 138.2741 },
    { pos: new mp.Vector3(1719.8297, 3816.377, 34.890), r: 42.38486 },
    { pos: new mp.Vector3(-1004.867, -1554.74, 5.2793), r: 125.6932 },
    { pos: new mp.Vector3(553.38500, -1683.67, 29.243), r: 226.1469 },
    { pos: new mp.Vector3(1471.6333, -2607.14, 49.030), r: 54.53795 },
    { pos: new mp.Vector3(-1092.209, -898.283, 3.3160), r: 217.5974 },
    { pos: new mp.Vector3(1741.4534, 3830.994, 34.786), r: 23.69050 },
    { pos: new mp.Vector3(160.90341, -923.624, 30.103), r: 1.877644 },
    { pos: new mp.Vector3(-574.0098, 518.3767, 106.41), r: 205.1733 },
    { pos: new mp.Vector3(529.00354, -1720.26, 29.702), r: 127.5311 },
    { pos: new mp.Vector3(1621.1166, -2676.33, 31.374), r: 41.66548 },
    { pos: new mp.Vector3(1756.4249, 3839.275, 34.463), r: 23.77727 },
    { pos: new mp.Vector3(-1020.658, -1515.60, 5.5947), r: 134.9377 },
    { pos: new mp.Vector3(-1013.167, -837.963, 14.162), r: 200.7822 },
    { pos: new mp.Vector3(-439.1925, 6017.745, 31.490), r: 311.0753 },
    { pos: new mp.Vector3(1774.2622, 3849.180, 34.285), r: 1.206962 },
    { pos: new mp.Vector3(2894.9389, 4094.072, 50.547), r: 199.4024 },
    { pos: new mp.Vector3(495.15249, -1714.29, 29.334), r: 338.0358 },
    { pos: new mp.Vector3(-1041.099, -871.909, 6.2911), r: 186.4682 },
    { pos: new mp.Vector3(1888.9244, -2640.18, 31.849), r: 36.71396 },
    { pos: new mp.Vector3(352.23791, -550.334, 28.955), r: 24.47226 },
    { pos: new mp.Vector3(-1040.915, -1512.17, 5.1933), r: 120.7144 },
    { pos: new mp.Vector3(492.68572, -1718.32, 29.438), r: 184.7450 },
    { pos: new mp.Vector3(2903.1164, 4064.380, 50.781), r: 192.3174 },
    { pos: new mp.Vector3(-606.7361, 498.7925, 106.97), r: 22.93943 },
    { pos: new mp.Vector3(2906.7556, 4050.156, 50.906), r: 193.1536 },
    { pos: new mp.Vector3(488.62283, -1732.49, 29.176), r: 70.10601 },
    { pos: new mp.Vector3(351.10150, -537.201, 28.926), r: 119.6344 },
    { pos: new mp.Vector3(-441.9833, 6020.534, 31.490), r: 316.1226 },
    { pos: new mp.Vector3(-1101.042, -931.481, 2.6997), r: 83.00722 },
    { pos: new mp.Vector3(2911.7802, 4050.952, 50.906), r: 9.768708 },
    { pos: new mp.Vector3(-1475.001, 875.9189, 183.53), r: 123.0797 },
    { pos: new mp.Vector3(482.70977, -1742.70, 28.985), r: 231.2920 },
    { pos: new mp.Vector3(1791.3482, 3826.614, 33.829), r: 294.6950 },
    { pos: new mp.Vector3(2908.1430, 4067.833, 50.767), r: 13.05401 },
    { pos: new mp.Vector3(1924.6274, -2246.25, 140.77), r: 42.00620 },
    { pos: new mp.Vector3(-634.7337, 505.4106, 108.03), r: 128.6258 },
    { pos: new mp.Vector3(481.25686, -1751.24, 28.721), r: 66.25552 },
    { pos: new mp.Vector3(-1100.898, -942.933, 2.5359), r: 120.4554 },
    { pos: new mp.Vector3(-1060.339, -1485.50, 5.1122), r: 77.62104 },
    { pos: new mp.Vector3(1796.4981, 3817.356, 33.617), r: 224.6580 },
    { pos: new mp.Vector3(2900.4814, 4094.288, 50.558), r: 16.39470 },
    { pos: new mp.Vector3(477.34542, -1761.79, 28.707), r: 181.8549 },
    { pos: new mp.Vector3(2030.6086, -2247.93, 89.493), r: 79.46700 },
    { pos: new mp.Vector3(478.30316, -1768.89, 28.619), r: 164.9760 },
    { pos: new mp.Vector3(1801.2686, 3809.720, 33.514), r: 289.1682 },
    { pos: new mp.Vector3(-1117.161, -945.732, 2.6189), r: 298.1979 },
    { pos: new mp.Vector3(-1155.666, 305.2008, 67.932), r: 264.6275 },
    { pos: new mp.Vector3(-1055.981, -1477.54, 5.0642), r: 38.83478 },
    { pos: new mp.Vector3(481.14013, -1782.55, 28.709), r: 199.2870 },
    { pos: new mp.Vector3(1804.3287, 3800.878, 33.552), r: 272.0516 },
    { pos: new mp.Vector3(-1146.699, 313.9957, 67.888), r: 84.29264 },
    { pos: new mp.Vector3(-1135.129, -916.256, 3.0789), r: 279.1992 },
    { pos: new mp.Vector3(-1144.110, 310.0375, 67.638), r: 207.1081 },
    { pos: new mp.Vector3(1794.9780, 3793.773, 33.568), r: 190.5003 },
    { pos: new mp.Vector3(2070.7905, -2252.75, 67.947), r: 72.35433 },
    { pos: new mp.Vector3(-1035.524, -1469.01, 5.0686), r: 21.01688 },
    { pos: new mp.Vector3(506.61495, -1772.12, 28.506), r: 27.68566 },
    { pos: new mp.Vector3(-678.9722, 496.9548, 110.02), r: 230.4709 },
    { pos: new mp.Vector3(509.71261, -1783.15, 28.502), r: 148.0824 },
    { pos: new mp.Vector3(-1151.117, 278.3141, 66.665), r: 196.1018 },
    { pos: new mp.Vector3(-1022.528, -1465.39, 5.0480), r: 333.8953 },
    { pos: new mp.Vector3(1783.2958, 3786.882, 33.726), r: 209.1178 },
    { pos: new mp.Vector3(-1121.419, -908.440, 3.0898), r: 123.9041 },
    { pos: new mp.Vector3(1774.3225, 3782.263, 33.735), r: 204.6586 },
    { pos: new mp.Vector3(2092.2763, -2141.44, 92.712), r: 171.0177 },
    { pos: new mp.Vector3(-705.5216, 489.3378, 109.04), r: 207.0653 },
    { pos: new mp.Vector3(-391.9540, 6004.586, 31.803), r: 283.7956 },
    { pos: new mp.Vector3(1766.5787, 3778.090, 33.755), r: 212.3047 },
    { pos: new mp.Vector3(-988.6297, -1448.69, 5.1852), r: 78.94806 },
    { pos: new mp.Vector3(-1163.330, 319.2590, 68.976), r: 266.9899 },
    { pos: new mp.Vector3(509.50573, -1793.69, 28.502), r: 162.3768 },
    { pos: new mp.Vector3(-1156.723, -891.058, 15.236), r: 98.62762 },
    { pos: new mp.Vector3(2172.1569, -2162.80, 60.892), r: 89.39508 },
    { pos: new mp.Vector3(1758.4849, 3773.036, 33.808), r: 183.3847 },
    { pos: new mp.Vector3(1965.9228, 2527.085, 54.788), r: 150.3278 },
    { pos: new mp.Vector3(-1386.806, -179.351, 47.389), r: 59.78713 },
    { pos: new mp.Vector3(-964.7542, -1461.74, 5.1549), r: 97.94550 },
    { pos: new mp.Vector3(1955.5267, 2511.538, 54.894), r: 145.5245 },
    { pos: new mp.Vector3(496.30291, -1811.84, 28.513), r: 344.5982 },
    { pos: new mp.Vector3(-1146.556, -934.496, 2.6943), r: 182.0477 },
    { pos: new mp.Vector3(1946.3586, 2497.913, 54.664), r: 145.8116 },
    { pos: new mp.Vector3(-323.1312, 6076.910, 31.243), r: 223.8070 },
    { pos: new mp.Vector3(492.13919, -1819.60, 28.424), r: 113.0923 },
    { pos: new mp.Vector3(1934.9753, 2481.097, 54.795), r: 145.8966 },
    { pos: new mp.Vector3(1857.4433, -2230.93, 163.33), r: 103.5555 },
    { pos: new mp.Vector3(-1309.540, -37.8615, 48.612), r: 215.5302 },
    { pos: new mp.Vector3(1923.2768, 2463.102, 54.679), r: 149.0885 },
    { pos: new mp.Vector3(-941.1729, -1514.56, 5.2796), r: 45.08745 },
    { pos: new mp.Vector3(1887.8945, -1737.77, 199.30), r: 36.21964 },
    { pos: new mp.Vector3(-757.6430, 465.0520, 102.07), r: 152.5856 },
    { pos: new mp.Vector3(-1166.953, 311.8154, 69.396), r: 63.06861 },
    { pos: new mp.Vector3(1910.1710, 2441.417, 54.654), r: 148.8051 },
    { pos: new mp.Vector3(-1340.969, -35.1953, 50.639), r: 166.2384 },
    { pos: new mp.Vector3(-935.3500, -1529.72, 5.2769), r: 48.32539 },
    { pos: new mp.Vector3(-1167.488, 303.1873, 69.197), r: 230.5761 },
    { pos: new mp.Vector3(-326.0979, 6074.453, 31.244), r: 216.7082 },
    { pos: new mp.Vector3(1825.3757, 3690.277, 34.224), r: 59.84361 },
    { pos: new mp.Vector3(-1234.757, -910.316, 11.712), r: 32.99136 },
    { pos: new mp.Vector3(1901.1530, 2425.723, 54.660), r: 152.5258 },
    { pos: new mp.Vector3(-1156.697, 296.3081, 67.835), r: 256.4020 },
    { pos: new mp.Vector3(-929.2462, -1553.64, 5.2727), r: 51.10058 },
    { pos: new mp.Vector3(-1154.231, 288.5187, 67.545), r: 199.9105 },
    { pos: new mp.Vector3(1831.8302, 3693.976, 34.224), r: 343.8370 },
    { pos: new mp.Vector3(-1248.834, -919.387, 11.326), r: 328.5005 },
    { pos: new mp.Vector3(-1161.593, 274.3723, 68.293), r: 138.6537 },
    { pos: new mp.Vector3(1883.2280, 2391.427, 53.849), r: 150.9911 },
    { pos: new mp.Vector3(-1370.946, 66.21448, 53.911), r: 103.0841 },
    { pos: new mp.Vector3(-331.4844, 6152.183, 32.313), r: 95.60331 },
    { pos: new mp.Vector3(-907.9722, -1545.79, 5.2727), r: 15.89819 },
    { pos: new mp.Vector3(484.32266, -1828.48, 27.795), r: 297.1195 },
    { pos: new mp.Vector3(1866.0324, 2356.001, 53.541), r: 153.5181 },
    { pos: new mp.Vector3(-1369.574, 44.97805, 53.910), r: 88.50129 },
    { pos: new mp.Vector3(-740.2150, 406.3541, 96.017), r: 313.6989 },
    { pos: new mp.Vector3(490.17654, -1840.83, 27.831), r: 210.9208 },
    { pos: new mp.Vector3(-331.4915, 6153.064, 32.313), r: 89.21588 },
    { pos: new mp.Vector3(-1155.530, 330.7267, 69.210), r: 325.6701 },
    { pos: new mp.Vector3(-880.1157, -1517.96, 5.2727), r: 321.2108 },
    { pos: new mp.Vector3(1886.0170, -1747.68, 200.09), r: 41.74581 },
    { pos: new mp.Vector3(1843.3958, 2296.315, 53.636), r: 106.9758 },
    { pos: new mp.Vector3(1835.5660, 3702.938, 33.771), r: 15.49775 },
    { pos: new mp.Vector3(503.93075, -1867.60, 26.349), r: 236.4082 },
    { pos: new mp.Vector3(1831.2270, 2264.577, 53.789), r: 168.1678 },
    { pos: new mp.Vector3(1839.1011, 3703.677, 33.904), r: 292.2868 },
    { pos: new mp.Vector3(-883.4503, -1493.41, 5.2796), r: 340.4645 },
    { pos: new mp.Vector3(487.54180, -1872.69, 26.454), r: 31.01491 },
    { pos: new mp.Vector3(2063.8796, -1496.78, 236.60), r: 29.71928 },
    { pos: new mp.Vector3(-1240.783, -1000.59, 4.2829), r: 9.188441 },
    { pos: new mp.Vector3(-805.8838, 293.6693, 86.122), r: 235.6104 },
    { pos: new mp.Vector3(467.93560, -1853.02, 27.774), r: 63.96580 },
    { pos: new mp.Vector3(1848.6505, 3715.221, 33.249), r: 346.5094 },
    { pos: new mp.Vector3(1824.2667, 2213.016, 53.716), r: 177.0393 },
    { pos: new mp.Vector3(1822.7645, 2184.210, 54.397), r: 177.8080 },
    { pos: new mp.Vector3(-924.3034, -1506.66, 5.1773), r: 345.5152 },
    { pos: new mp.Vector3(-799.0133, 433.1889, 91.566), r: 72.89408 },
    { pos: new mp.Vector3(1847.3924, 2006.579, 56.973), r: 189.3100 },
    { pos: new mp.Vector3(2670.0783, -1655.25, 19.579), r: 98.16143 },
    { pos: new mp.Vector3(1796.6115, 3680.368, 34.135), r: 99.45106 },
    { pos: new mp.Vector3(-424.9400, 6134.891, 31.478), r: 138.9780 },
    { pos: new mp.Vector3(1880.8292, 1893.556, 60.542), r: 200.3070 },
    { pos: new mp.Vector3(1917.6895, 1790.602, 66.269), r: 199.6980 },
    { pos: new mp.Vector3(1801.0101, 3672.296, 34.217), r: 131.7347 },
    { pos: new mp.Vector3(-1216.191, -1017.85, 2.1504), r: 161.3655 },
    { pos: new mp.Vector3(-960.9704, -1512.99, 5.1731), r: 286.7426 },
    { pos: new mp.Vector3(415.18832, -1847.75, 27.473), r: 43.98965 },
    { pos: new mp.Vector3(1955.4389, 1699.594, 70.786), r: 205.4972 },
    { pos: new mp.Vector3(-848.1045, 443.1810, 86.918), r: 168.9423 },
    { pos: new mp.Vector3(1988.6066, 1623.389, 73.802), r: 202.9183 },
    { pos: new mp.Vector3(400.87637, -1846.28, 26.881), r: 151.6749 },
    { pos: new mp.Vector3(2636.5500, -1923.29, 20.428), r: 83.77443 },
    { pos: new mp.Vector3(-983.0061, -1533.04, 5.0921), r: 163.0724 },
    { pos: new mp.Vector3(1980.5927, 1595.245, 74.881), r: 90.79640 },
    { pos: new mp.Vector3(1961.6975, 1611.742, 75.886), r: 42.67007 },
    { pos: new mp.Vector3(1803.4624, 3640.600, 34.285), r: 289.4676 },
    { pos: new mp.Vector3(1938.1311, 1683.167, 70.990), r: 31.52947 },
    { pos: new mp.Vector3(403.62414, -1867.96, 26.362), r: 226.5504 },
    { pos: new mp.Vector3(1900.0585, 1750.799, 66.128), r: 28.70875 },
    { pos: new mp.Vector3(-1207.614, -1029.17, 2.1504), r: 118.6147 },
    { pos: new mp.Vector3(-1021.382, -1548.89, 5.2114), r: 123.7606 },
    { pos: new mp.Vector3(1878.6597, 1787.561, 63.189), r: 28.75251 },
    { pos: new mp.Vector3(1860.8753, 1823.988, 64.429), r: 26.05295 },
    { pos: new mp.Vector3(397.24581, -1875.93, 26.289), r: 173.1839 },
    { pos: new mp.Vector3(1859.2752, 3655.743, 34.016), r: 9.867831 },
    { pos: new mp.Vector3(394.87991, -1880.60, 26.693), r: 138.9000 },
    { pos: new mp.Vector3(1832.6993, 1878.471, 62.254), r: 29.51432 },
    { pos: new mp.Vector3(388.52368, -1887.50, 25.558), r: 134.2912 },
    { pos: new mp.Vector3(-236.6939, 6317.019, 31.501), r: 226.3317 },
    { pos: new mp.Vector3(1817.7298, 1821.980, 68.872), r: 171.2467 },
    { pos: new mp.Vector3(1862.7770, 3657.971, 33.939), r: 27.74667 },
    { pos: new mp.Vector3(-1193.980, -1063.52, 2.1504), r: 119.2108 },
    { pos: new mp.Vector3(378.09918, -1895.70, 25.164), r: 44.61861 },
    { pos: new mp.Vector3(1789.9692, 1749.930, 73.111), r: 160.6017 },
    { pos: new mp.Vector3(372.68765, -1901.97, 24.920), r: 153.4933 },
    { pos: new mp.Vector3(1772.2736, 1694.203, 78.738), r: 163.4957 },
    { pos: new mp.Vector3(-2453.596, 3672.605, 14.022), r: 152.9366 },
    { pos: new mp.Vector3(1749.5218, 1591.531, 77.844), r: 164.8065 },
    { pos: new mp.Vector3(-233.5060, 6320.180, 31.499), r: 219.5257 },
    { pos: new mp.Vector3(1738.4249, 1551.475, 83.312), r: 166.0381 },
    { pos: new mp.Vector3(1732.5406, 1530.693, 84.114), r: 165.4625 },
    { pos: new mp.Vector3(341.31329, -1893.88, 25.428), r: 57.84448 },
    { pos: new mp.Vector3(-1076.510, -1457.54, 5.2538), r: 109.3986 },
    { pos: new mp.Vector3(-488.9855, 576.5559, 121.05), r: 42.44954 },
    { pos: new mp.Vector3(-2444.814, 3663.974, 13.897), r: 154.7312 },
    { pos: new mp.Vector3(1731.4630, 1521.167, 84.692), r: 150.5201 },
    { pos: new mp.Vector3(328.08197, -1882.52, 26.371), r: 101.5130 },
    { pos: new mp.Vector3(1721.6422, 1477.556, 84.977), r: 170.9801 },
    { pos: new mp.Vector3(-1173.044, -1088.30, 2.1920), r: 221.3824 },
    { pos: new mp.Vector3(1710.5125, 1419.572, 85.656), r: 169.8807 },
    { pos: new mp.Vector3(311.28927, -1870.21, 26.925), r: 130.0569 },
    { pos: new mp.Vector3(-225.1006, 6330.968, 32.307), r: 216.0972 },
    { pos: new mp.Vector3(-1045.805, -1447.57, 5.0577), r: 198.1479 },
    { pos: new mp.Vector3(-2449.064, 3640.723, 14.636), r: 353.1474 },
    { pos: new mp.Vector3(1702.9677, 1382.925, 86.622), r: 168.9170 },
    { pos: new mp.Vector3(304.80667, -1856.25, 27.019), r: 19.54091 },
    { pos: new mp.Vector3(1865.3526, 3656.372, 35.988), r: 345.9625 },
    { pos: new mp.Vector3(306.26257, -1852.15, 26.860), r: 323.8126 },
    { pos: new mp.Vector3(1694.6893, 1341.088, 87.144), r: 167.0136 },
    { pos: new mp.Vector3(1686.6447, 1305.589, 86.373), r: 168.8284 },
    { pos: new mp.Vector3(1872.2143, 3654.456, 35.944), r: 297.7670 },
    { pos: new mp.Vector3(311.37951, -1855.94, 27.040), r: 119.6574 },
    { pos: new mp.Vector3(-2511.318, 3609.850, 13.742), r: 287.7702 },
    { pos: new mp.Vector3(-1189.212, -1110.32, 4.3894), r: 23.33977 },
    { pos: new mp.Vector3(-1011.847, -1420.37, 5.1990), r: 149.3789 },
    { pos: new mp.Vector3(1627.9720, 1246.165, 86.673), r: 141.2852 },
    { pos: new mp.Vector3(-516.9829, 627.5422, 133.65), r: 314.7750 },
    { pos: new mp.Vector3(317.87582, -1850.17, 27.102), r: 15.06879 },
    { pos: new mp.Vector3(1875.5340, 3648.471, 35.869), r: 273.4899 },
    { pos: new mp.Vector3(1624.6230, 1235.153, 86.210), r: 170.3322 },
    { pos: new mp.Vector3(-190.7231, 6362.542, 31.485), r: 222.5985 },
    { pos: new mp.Vector3(1623.7326, 1215.012, 85.444), r: 181.7657 },
    { pos: new mp.Vector3(312.17462, -1846.27, 26.837), r: 311.0380 },
    { pos: new mp.Vector3(-990.6008, -1415.98, 5.2796), r: 9.597578 },
    { pos: new mp.Vector3(-1209.980, -1128.04, 7.8081), r: 121.1859 },
    { pos: new mp.Vector3(1624.8664, 1201.145, 85.236), r: 196.0696 },
    { pos: new mp.Vector3(322.18777, -1834.74, 27.260), r: 321.6438 },
    { pos: new mp.Vector3(-2461.737, 3622.507, 14.130), r: 90.40495 },
    { pos: new mp.Vector3(1874.5133, 3641.084, 36.086), r: 266.9058 },
    { pos: new mp.Vector3(1588.0330, 1169.537, 92.726), r: 133.3473 },
    { pos: new mp.Vector3(322.68557, -1841.86, 27.256), r: 168.6439 },
    { pos: new mp.Vector3(-508.6635, 652.9237, 139.81), r: 142.6242 },
    { pos: new mp.Vector3(1578.6770, 1145.028, 96.448), r: 161.7991 },
    { pos: new mp.Vector3(-122.3910, 6392.385, 31.485), r: 41.70156 },
    { pos: new mp.Vector3(-1196.462, -1116.86, 7.6721), r: 159.9190 },
    { pos: new mp.Vector3(332.25894, -1838.44, 27.257), r: 311.8724 },
    { pos: new mp.Vector3(1561.0793, 1090.422, 107.42), r: 157.4043 },
    { pos: new mp.Vector3(-956.2272, -1405.90, 5.2796), r: 16.07126 },
    { pos: new mp.Vector3(-2429.873, 3603.702, 22.693), r: 326.4392 },
    { pos: new mp.Vector3(1868.1346, 3637.875, 35.960), r: 184.1064 },
    { pos: new mp.Vector3(1524.3107, 1053.602, 112.72), r: 180.8734 },
    { pos: new mp.Vector3(337.10104, -1859.99, 27.323), r: 124.0765 },
    { pos: new mp.Vector3(-125.5556, 6389.095, 31.618), r: 50.31841 },
    { pos: new mp.Vector3(-487.0848, 669.7103, 146.03), r: 230.3426 },
    { pos: new mp.Vector3(342.77618, -1867.84, 27.280), r: 238.7274 },
    { pos: new mp.Vector3(-2338.437, 3425.672, 29.744), r: 101.7864 },
    { pos: new mp.Vector3(1861.1931, 3639.489, 35.977), r: 118.5924 },
    { pos: new mp.Vector3(350.87530, -1861.53, 27.474), r: 301.6371 },
    { pos: new mp.Vector3(-933.7523, -1396.83, 5.2796), r: 24.97734 },
    { pos: new mp.Vector3(1472.4523, 999.1918, 115.84), r: 128.2564 },
    { pos: new mp.Vector3(-1144.764, -1190.34, 2.5331), r: 181.5872 },
    { pos: new mp.Vector3(-461.6478, 676.4065, 153.06), r: 226.2694 },
    { pos: new mp.Vector3(1857.5460, 3645.75, .9330596), r: 117.2012 },
    { pos: new mp.Vector3(-916.5524, -1389.00, 5.2796), r: 49.75210 },
    { pos: new mp.Vector3(-1104.174, -1172.83, 2.1495), r: 130.5843 },
    { pos: new mp.Vector3(-110.6764, 6458.815, 31.467), r: 133.2369 },
    { pos: new mp.Vector3(-2306.439, 3438.794, 31.622), r: 15.79744 },
    { pos: new mp.Vector3(1859.3786, 3652.591, 35.910), r: 38.61162 },
    { pos: new mp.Vector3(338.70343, -1825.84, 27.971), r: 100.7479 },
    { pos: new mp.Vector3(333.64199, -1829.33, 27.821), r: 132.6374 },
    { pos: new mp.Vector3(-1100.182, -1179.74, 2.1495), r: 121.8998 },
    { pos: new mp.Vector3(-2309.503, 3450.532, 31.391), r: 185.1399 },
    { pos: new mp.Vector3(1420.5246, 947.2569, 103.29), r: 149.2715 },
    { pos: new mp.Vector3(1900.1358, 3706.102, 32.757), r: 186.2851 },
    { pos: new mp.Vector3(-114.6736, 6462.911, 31.468), r: 139.6930 },
    { pos: new mp.Vector3(1383.5319, 899.6810, 109.70), r: 148.1485 },
    { pos: new mp.Vector3(-2277.708, 3454.834, 31.638), r: 267.7316 },
    { pos: new mp.Vector3(-1152.841, -1088.00, 2.1501), r: 115.4919 },
    { pos: new mp.Vector3(1328.8880, 879.2518, 117.44), r: 110.1127 },
    { pos: new mp.Vector3(-2274.202, 3436.015, 32.131), r: 328.2051 },
    { pos: new mp.Vector3(1908.6700, 3711.406, 32.746), r: 249.2115 },
    { pos: new mp.Vector3(1311.1542, 820.6336, 106.72), r: 89.85097 },
    { pos: new mp.Vector3(-456.1569, 640.1719, 147.58), r: 179.6272 },
    { pos: new mp.Vector3(-1157.740, -1080.40, 2.1501), r: 117.0189 },
    { pos: new mp.Vector3(1277.9467, 757.8704, 95.865), r: 324.4590 },
    { pos: new mp.Vector3(362.03625, -1786.54, 29.051), r: 137.8735 },
    { pos: new mp.Vector3(-456.5887, 632.0451, 147.58), r: 14.53761 },
    { pos: new mp.Vector3(358.23660, -1789.81, 28.975), r: 130.9124 },
    { pos: new mp.Vector3(1910.6243, 3718.709, 32.699), r: 217.3157 },
    { pos: new mp.Vector3(1262.1335, 710.2571, 101.10), r: 155.4427 },
    { pos: new mp.Vector3(72.766479, 266.7110, 109.86), r: 227.0472 },
    { pos: new mp.Vector3(1243.9926, 679.1216, 98.203), r: 150.7090 },
    { pos: new mp.Vector3(-2260.745, 3398.301, 32.300), r: 24.15362 },
    { pos: new mp.Vector3(1217.8505, 645.6113, 97.879), r: 145.0675 },
    { pos: new mp.Vector3(1171.9865, 582.7127, 97.479), r: 141.6157 },
    { pos: new mp.Vector3(376.71060, -1782.86, 29.528), r: 135.8474 },
    { pos: new mp.Vector3(1149.2805, 554.7725, 96.879), r: 139.3098 },
    { pos: new mp.Vector3(-107.2697, 6404.400, 31.620), r: 310.0589 },
    { pos: new mp.Vector3(381.69335, -1776.52, 29.349), r: 290.0808 },
    { pos: new mp.Vector3(1129.8530, 536.6168, 96.810), r: 130.4528 },
    { pos: new mp.Vector3(1925.8968, 3715.373, 32.868), r: 221.2243 },
    { pos: new mp.Vector3(1105.7244, 506.6452, 94.756), r: 147.8626 },
    { pos: new mp.Vector3(1093.4194, 480.8674, 93.797), r: 157.9980 },
    { pos: new mp.Vector3(1048.1662, 429.2638, 91.612), r: 130.6713 },
    { pos: new mp.Vector3(-437.8309, 574.0991, 126.70), r: 235.9232 },
    { pos: new mp.Vector3(330.53579, 22.48797, 86.129), r: 63.96676 },
    { pos: new mp.Vector3(-2314.405, 3381.100, 31.481), r: 6.220066 },
    { pos: new mp.Vector3(400.47167, -1749.61, 29.380), r: 34.11777 },
    { pos: new mp.Vector3(1939.8323, 3723.380, 32.867), r: 250.5885 },
    { pos: new mp.Vector3(-48.47294, 6469.117, 31.503), r: 47.67134 },
    { pos: new mp.Vector3(402.91961, -1745.91, 29.342), r: 53.12710 },
    { pos: new mp.Vector3(-1051.483, -1258.12, 6.1841), r: 210.1252 },
    { pos: new mp.Vector3(962.06134, 380.1545, 91.728), r: 336.6826 },
    { pos: new mp.Vector3(405.38842, -1735.99, 29.281), r: 331.0231 },
    { pos: new mp.Vector3(1923.1051, 3753.879, 32.385), r: 7.325951 },
    { pos: new mp.Vector3(983.44567, 447.8539, 97.246), r: 338.2090 },
    { pos: new mp.Vector3(-2310.718, 3405.273, 30.948), r: 95.65701 },
    { pos: new mp.Vector3(-1040.803, -1251.92, 6.2274), r: 224.8891 },
    { pos: new mp.Vector3(999.44207, 475.1950, 97.423), r: 322.7003 },
    { pos: new mp.Vector3(-435.5210, 544.3494, 121.84), r: 60.87450 },
    { pos: new mp.Vector3(1908.9143, 3745.682, 32.413), r: 22.24183 },
    { pos: new mp.Vector3(1004.7475, 483.6471, 98.671), r: 321.6697 },
    { pos: new mp.Vector3(1006.6476, 486.7037, 98.758), r: 347.5507 },
    { pos: new mp.Vector3(424.18783, -1720.51, 29.594), r: 97.83798 },
    { pos: new mp.Vector3(1004.9010, 488.0491, 98.722), r: 101.7127 },
    { pos: new mp.Vector3(1003.8985, 486.7947, 98.630), r: 191.4306 },
    { pos: new mp.Vector3(-410.6715, 546.5949, 122.59), r: 196.7451 },
    { pos: new mp.Vector3(1898.0528, 3736.046, 32.674), r: 45.95293 },
    { pos: new mp.Vector3(-1028.621, -1238.98, 6.0273), r: 119.6337 },
    { pos: new mp.Vector3(428.00238, -1716.75, 29.408), r: 47.72024 },
    { pos: new mp.Vector3(1897.6140, 3740.503, 32.489), r: 20.23408 },
    { pos: new mp.Vector3(-1077.754, -1012.39, 2.2079), r: 345.1560 },
    { pos: new mp.Vector3(-1018.920, -1242.95, 5.9801), r: 193.9786 },
    { pos: new mp.Vector3(-403.9859, 529.2713, 123.32), r: 128.6710 },
    { pos: new mp.Vector3(430.23892, -1711.82, 29.283), r: 279.4992 },
    { pos: new mp.Vector3(995.82745, 495.6153, 99.989), r: 42.14368 },
    { pos: new mp.Vector3(433.39706, -1709.22, 29.813), r: 321.2182 },
    { pos: new mp.Vector3(1884.0712, 3732.257, 32.626), r: 85.71842 },
    { pos: new mp.Vector3(440.12741, -1701.89, 29.534), r: 316.8836 },
    { pos: new mp.Vector3(-47.47568, 6517.012, 31.490), r: 316.6865 },
    { pos: new mp.Vector3(956.49658, 511.5836, 110.85), r: 68.29637 },
    { pos: new mp.Vector3(-1101.353, -943.950, 2.5339), r: 248.7486 },
    { pos: new mp.Vector3(932.42724, 514.2771, 116.99), r: 70.63731 },
    { pos: new mp.Vector3(-977.2513, -1219.30, 5.2949), r: 204.8439 },
    { pos: new mp.Vector3(-369.8347, 514.2460, 119.52), r: 245.0092 },
    { pos: new mp.Vector3(449.66458, -1682.71, 29.323), r: 333.2506 },
    { pos: new mp.Vector3(1870.9746, 3722.837, 33.059), r: 308.2266 },
    { pos: new mp.Vector3(920.90319, 518.4240, 119.34), r: 112.9278 },
    { pos: new mp.Vector3(-974.1530, -1209.60, 5.2024), r: 260.3136 },
    { pos: new mp.Vector3(921.93627, 511.9710, 120.73), r: 331.3844 },
    { pos: new mp.Vector3(-55.64958, 6525.145, 31.490), r: 316.0763 },
    { pos: new mp.Vector3(924.85034, 512.8003, 120.68), r: 269.0260 },
    { pos: new mp.Vector3(470.47927, -1526.71, 29.296), r: 263.7552 },
    { pos: new mp.Vector3(-2414.636, 3469.206, 40.108), r: 292.9751 },
    { pos: new mp.Vector3(925.61285, 511.3415, 120.65), r: 165.7546 },
    { pos: new mp.Vector3(1861.4971, 3717.144, 33.104), r: 44.48242 },
    { pos: new mp.Vector3(-344.7791, 471.5743, 112.28), r: 188.0705 },
    { pos: new mp.Vector3(-333.6655, 460.6069, 110.91), r: 232.3006 },
    { pos: new mp.Vector3(486.18408, -1518.60, 29.289), r: 268.5850 },
    { pos: new mp.Vector3(926.04528, 494.5688, 120.98), r: 208.7351 },
    { pos: new mp.Vector3(-2578.467, 3391.263, 13.187), r: 250.8306 },
    { pos: new mp.Vector3(924.05639, 467.3740, 121.26), r: 169.7647 },
    { pos: new mp.Vector3(-63.67713, 6533.252, 31.490), r: 312.0725 },
    { pos: new mp.Vector3(919.50018, 444.3331, 120.17), r: 160.8430 },
    { pos: new mp.Vector3(493.36752, -1492.97, 29.232), r: 358.9656 },
    { pos: new mp.Vector3(895.04870, 402.4664, 119.56), r: 141.0323 },
    { pos: new mp.Vector3(-2544.757, 3394.974, 13.254), r: 82.29140 },
    { pos: new mp.Vector3(-291.7550, 446.8810, 108.56), r: 173.4142 },
    { pos: new mp.Vector3(876.23162, 381.3502, 118.38), r: 137.7679 },
    { pos: new mp.Vector3(487.18307, -1477.13, 29.277), r: 14.93448 },
    { pos: new mp.Vector3(844.31262, 351.8294, 118.28), r: 126.7409 },
    { pos: new mp.Vector3(815.22442, 336.6549, 116.90), r: 109.0438 },
    { pos: new mp.Vector3(-2565.317, 3444.632, 13.415), r: 242.5724 },
    { pos: new mp.Vector3(-100.3999, 6541.427, 29.809), r: 36.45114 },
    { pos: new mp.Vector3(770.97491, 329.0311, 114.94), r: 119.6575 },
    { pos: new mp.Vector3(493.26095, -1450.77, 29.294), r: 352.3372 },
    { pos: new mp.Vector3(-2720.101, 3573.993, 3.0682), r: 355.4057 },
    { pos: new mp.Vector3(-976.4070, -1185.95, 3.9725), r: 201.3274 },
    { pos: new mp.Vector3(487.13250, -1449.91, 29.282), r: 88.45516 },
    { pos: new mp.Vector3(-277.5010, 423.2749, 109.04), r: 208.0610 },
    { pos: new mp.Vector3(1828.6072, 3728.582, 33.187), r: 208.1409 },
    { pos: new mp.Vector3(743.27142, 326.7445, 113.65), r: 89.50368 },
    { pos: new mp.Vector3(-2838.066, 3589.174, 2.9416), r: 352.7791 },
    { pos: new mp.Vector3(-1874.292, 2030.029, 139.65), r: 279.2203 },
    { pos: new mp.Vector3(-964.2438, -1178.58, 4.0059), r: 203.0883 },
    { pos: new mp.Vector3(-2932.439, 3572.005, 1.8656), r: 17.93425 },
    { pos: new mp.Vector3(703.19836, 332.1511, 112.27), r: 98.40101 },
    { pos: new mp.Vector3(-1870.117, 2041.041, 139.41), r: 338.0617 },
    { pos: new mp.Vector3(-476.2497, 360.9888, 103.31), r: 317.2297 },
    { pos: new mp.Vector3(-1861.321, 2040.159, 137.83), r: 263.6538 },
    { pos: new mp.Vector3(1827.4744, 3716.606, 33.492), r: 202.9062 },
    { pos: new mp.Vector3(641.49853, 351.0275, 112.68), r: 61.92493 },
    { pos: new mp.Vector3(-3074.090, 3498.678, 1.7759), r: 81.66146 },
    { pos: new mp.Vector3(-1857.859, 2023.168, 137.80), r: 119.7694 },
    { pos: new mp.Vector3(-955.8657, -1191.68, 4.6506), r: 131.1980 },
    { pos: new mp.Vector3(-449.3681, 344.3989, 104.38), r: 19.40611 },
    { pos: new mp.Vector3(-1864.944, 2016.565, 139.59), r: 141.1867 },
    { pos: new mp.Vector3(-3136.980, 3411.646, 1.4737), r: 79.31525 },
    { pos: new mp.Vector3(603.85656, 348.6489, 119.38), r: 297.0170 },
    { pos: new mp.Vector3(-893.6583, -1429.70, 5.1602), r: 249.6309 },
    { pos: new mp.Vector3(-145.8633, 6442.492, 31.467), r: 259.1808 },
    { pos: new mp.Vector3(573.89007, 334.6842, 124.06), r: 124.9734 },
    { pos: new mp.Vector3(-1883.042, 2012.732, 141.35), r: 320.5026 },
    { pos: new mp.Vector3(-985.3358, -1140.97, 2.2002), r: 124.8190 },
    { pos: new mp.Vector3(-3181.787, 3262.895, 1.5400), r: 307.9859 },
    { pos: new mp.Vector3(545.42163, 320.5977, 127.20), r: 90.97844 },
    { pos: new mp.Vector3(-1879.931, 2029.009, 140.39), r: 315.0292 },
    { pos: new mp.Vector3(512.73773, 324.2114, 131.60), r: 75.29366 },
    { pos: new mp.Vector3(-137.6989, 6434.535, 31.462), r: 23.82076 },
    { pos: new mp.Vector3(-1883.392, 2035.959, 140.78), r: 73.53440 },
    { pos: new mp.Vector3(-1897.094, 2040.242, 140.86), r: 65.49706 },
    { pos: new mp.Vector3(-1211.052, -774.870, 17.647), r: 154.4929 },
    { pos: new mp.Vector3(-3078.541, 3200.820, 2.1647), r: 341.9599 },
    { pos: new mp.Vector3(-994.1525, -1125.06, 2.2535), r: 121.8848 },
    { pos: new mp.Vector3(-493.1587, 420.4832, 100.13), r: 347.7363 },
    { pos: new mp.Vector3(-3076.566, 3201.992, 2.2463), r: 78.31853 },
    { pos: new mp.Vector3(-1883.644, 2047.637, 140.95), r: 305.0232 },
    { pos: new mp.Vector3(-1007.895, -1132.80, 2.2207), r: 297.0746 },
    { pos: new mp.Vector3(-980.6832, -1453.91, 5.1523), r: 167.7660 },
    { pos: new mp.Vector3(-3077.265, 3204.198, 2.1536), r: 175.9798 },
    { pos: new mp.Vector3(-1908.916, 2024.044, 140.74), r: 106.7876 },
    { pos: new mp.Vector3(1854.1577, 3756.707, 33.073), r: 92.44701 },
    { pos: new mp.Vector3(-1916.021, 2024.282, 140.85), r: 54.80823 },
    { pos: new mp.Vector3(-2919.496, 3035.822, 2.5294), r: 315.7441 },
    { pos: new mp.Vector3(-1928.452, 2031.147, 140.83), r: 357.1183 },
    { pos: new mp.Vector3(472.92919, 348.1098, 136.47), r: 23.09531 },
    { pos: new mp.Vector3(393.88339, -1506.52, 29.270), r: 187.4100 },
    { pos: new mp.Vector3(-2922.151, 3068.916, 3.2153), r: 330.0459 },
    { pos: new mp.Vector3(470.83569, 358.6341, 136.65), r: 5.668961 },
    { pos: new mp.Vector3(-998.4417, -1148.72, 2.1730), r: 299.5012 },
    { pos: new mp.Vector3(1784.5489, 3797.632, 38.385), r: 228.1379 },
    { pos: new mp.Vector3(-2903.926, 3069.010, 3.2387), r: 5.130218 },
    { pos: new mp.Vector3(172.44717, 6601.700, 31.868), r: 277.4436 },
    { pos: new mp.Vector3(387.33154, -1529.69, 29.529), r: 172.1415 },
    { pos: new mp.Vector3(467.46728, 378.7809, 137.83), r: 15.51522 },
    { pos: new mp.Vector3(-2916.058, 3089.644, 2.7987), r: 337.8394 },
    { pos: new mp.Vector3(-1920.685, 2080.055, 138.66), r: 354.8518 },
    { pos: new mp.Vector3(374.35717, -1521.51, 29.291), r: 46.84689 },
    { pos: new mp.Vector3(-1923.346, 2087.019, 136.61), r: 6.129468 },
    { pos: new mp.Vector3(179.83496, 6602.802, 31.868), r: 278.1973 },
    { pos: new mp.Vector3(1759.9154, 3841.226, 34.394), r: 10.26183 },
    { pos: new mp.Vector3(455.14038, 402.3171, 138.79), r: 58.10629 },
    { pos: new mp.Vector3(-2875.316, 3079.112, 1.5133), r: 339.5439 },
    { pos: new mp.Vector3(-1916.657, 2091.220, 135.80), r: 236.7709 },
    { pos: new mp.Vector3(366.71966, -1509.76, 29.335), r: 62.15691 },
    { pos: new mp.Vector3(187.28134, 6603.942, 31.867), r: 279.3776 },
    { pos: new mp.Vector3(-1907.195, 2099.305, 135.38), r: 326.0341 },
    { pos: new mp.Vector3(459.43322, 423.7950, 140.56), r: 333.1273 },
    { pos: new mp.Vector3(-2877.373, 3102.379, 2.6943), r: 3.298726 },
    { pos: new mp.Vector3(360.71856, -1511.73, 29.308), r: 150.3767 },
    { pos: new mp.Vector3(-525.8340, 441.9444, 97.574), r: 18.48021 },
    { pos: new mp.Vector3(-2874.272, 3108.862, 3.1062), r: 144.7087 },
    { pos: new mp.Vector3(469.71728, 414.9642, 142.15), r: 213.8997 },
    { pos: new mp.Vector3(-1018.602, -1114.14, 2.2190), r: 306.2289 },
    { pos: new mp.Vector3(-1877.326, 2098.300, 139.86), r: 268.7860 },
    { pos: new mp.Vector3(-545.4368, 431.7653, 97.713), r: 134.6382 },
    { pos: new mp.Vector3(-1106.328, -1415.35, 5.1829), r: 171.7575 },
    { pos: new mp.Vector3(479.18283, 399.0922, 139.93), r: 213.9874 },
    { pos: new mp.Vector3(-2895.605, 3123.178, 2.6886), r: 151.9908 },
    { pos: new mp.Vector3(381.23916, -1537.92, 29.459), r: 254.7162 },
    { pos: new mp.Vector3(-1870.026, 2090.674, 140.23), r: 202.2176 },
    { pos: new mp.Vector3(-554.3574, 436.5196, 99.261), r: 23.01870 },
    { pos: new mp.Vector3(-1861.115, 2090.697, 140.67), r: 300.9231 },
    { pos: new mp.Vector3(397.36410, -1551.86, 29.450), r: 239.3889 },
    { pos: new mp.Vector3(1760.2606, 3825.442, 34.488), r: 23.34605 },
    { pos: new mp.Vector3(-1028.857, -1095.70, 1.9877), r: 306.3709 },
    { pos: new mp.Vector3(494.45162, 370.3326, 143.95), r: 234.4657 },
    { pos: new mp.Vector3(-1871.931, 2088.255, 140.99), r: 140.2694 },
    { pos: new mp.Vector3(404.64813, -1563.72, 29.311), r: 207.4662 },
    { pos: new mp.Vector3(-2775.136, 2711.820, 2.3233), r: 316.9278 },
    { pos: new mp.Vector3(-1071.788, -1399.82, 5.0737), r: 76.56118 },
    { pos: new mp.Vector3(-575.6782, 440.4629, 106.82), r: 133.6765 },
    { pos: new mp.Vector3(-1880.943, 2091.594, 140.99), r: 106.2041 },
    { pos: new mp.Vector3(1748.5361, 3829.019, 34.803), r: 53.97478 },
    { pos: new mp.Vector3(410.62329, -1568.92, 29.289), r: 225.7536 },
    { pos: new mp.Vector3(-1014.716, -1090.66, 2.0599), r: 137.5493 },
    { pos: new mp.Vector3(-2766.030, 2712.501, 2.3457), r: 298.5292 },
    { pos: new mp.Vector3(416.69183, -1575.24, 29.283), r: 223.0474 },
    { pos: new mp.Vector3(167.90734, 6631.919, 31.533), r: 248.2104 },
    { pos: new mp.Vector3(510.72656, 355.0171, 143.84), r: 237.1016 },
    { pos: new mp.Vector3(-2767.579, 2733.514, 2.2603), r: 306.9833 },
    { pos: new mp.Vector3(-1889.110, 2077.542, 140.99), r: 188.4826 },
    { pos: new mp.Vector3(-1068.104, -1385.72, 5.0917), r: 84.41017 },
    { pos: new mp.Vector3(430.79400, -1587.54, 29.319), r: 227.0729 },
    { pos: new mp.Vector3(524.09954, 349.1912, 141.98), r: 244.9912 },
    { pos: new mp.Vector3(-1873.826, 2071.284, 140.99), r: 248.3905 },
    { pos: new mp.Vector3(-1008.375, -1100.93, 2.1924), r: 134.9157 },
    { pos: new mp.Vector3(437.10397, -1592.85, 29.291), r: 233.5376 },
    { pos: new mp.Vector3(1724.8599, 3814.978, 34.970), r: 28.14845 },
    { pos: new mp.Vector3(-2731.428, 2738.789, 1.1475), r: 75.44544 },
    { pos: new mp.Vector3(540.67529, 351.3707, 138.37), r: 278.1875 },
    { pos: new mp.Vector3(448.00070, -1601.32, 29.332), r: 247.6206 },
    { pos: new mp.Vector3(-2742.103, 2828.005, 2.3114), r: 351.0038 },
    { pos: new mp.Vector3(-1855.851, 2058.806, 140.99), r: 181.8151 },
    { pos: new mp.Vector3(-1040.650, -1400.03, 5.5531), r: 74.39287 },
    { pos: new mp.Vector3(558.35845, 365.7651, 139.49), r: 328.7323 },
    { pos: new mp.Vector3(-657.3603, 413.5465, 101.23), r: 104.7924 },
    { pos: new mp.Vector3(-1020.137, -1082.43, 2.1685), r: 215.4966 },
    { pos: new mp.Vector3(-1855.215, 2062.014, 141.09), r: 8.132885 },
    { pos: new mp.Vector3(-2722.614, 2944.613, 2.4683), r: 101.3136 },
    { pos: new mp.Vector3(1720.7408, 3812.129, 34.957), r: 39.79541 },
    { pos: new mp.Vector3(-2684.222, 2835.758, 3.0732), r: 153.3857 },
    { pos: new mp.Vector3(376.77142, -1659.71, 48.296), r: 138.8340 },
    { pos: new mp.Vector3(-661.9168, 414.3084, 101.19), r: 195.4485 },
    { pos: new mp.Vector3(-1106.789, -1363.67, 5.1206), r: 241.6030 },
    { pos: new mp.Vector3(-1031.917, -1089.13, 2.1451), r: 208.2676 },
    { pos: new mp.Vector3(409.49179, 445.7653, 144.03), r: 77.65460 },
    { pos: new mp.Vector3(1732.8858, 3795.327, 34.712), r: 167.1191 },
    { pos: new mp.Vector3(-2606.748, 2756.197, 2.9185), r: 299.7554 },
    { pos: new mp.Vector3(-1789.088, 2034.942, 126.76), r: 326.4914 },
    { pos: new mp.Vector3(381.59948, 447.9995, 145.12), r: 84.58272 },
    { pos: new mp.Vector3(1734.7753, 3789.182, 34.498), r: 96.90998 },
    { pos: new mp.Vector3(385.89215, -1665.45, 48.302), r: 138.6059 },
    { pos: new mp.Vector3(-672.4658, 398.6647, 102.99), r: 276.7281 },
    { pos: new mp.Vector3(-1135.573, -1371.82, 5.0419), r: 134.8383 },
    { pos: new mp.Vector3(364.97775, 450.5122, 146.45), r: 57.78477 },
    { pos: new mp.Vector3(-1790.839, 2052.798, 125.54), r: 81.23332 },
    { pos: new mp.Vector3(-2510.797, 2835.821, 3.6593), r: 54.96786 },
    { pos: new mp.Vector3(-3009.680, 401.0502, 14.855), r: 258.6672 },
    { pos: new mp.Vector3(-658.9790, 397.3841, 101.25), r: 286.1210 },
    { pos: new mp.Vector3(347.71826, 466.4240, 150.18), r: 4.089534 },
    { pos: new mp.Vector3(-1035.448, -1055.74, 2.4302), r: 30.49943 },
    { pos: new mp.Vector3(365.61584, -1689.73, 48.319), r: 117.9593 },
    { pos: new mp.Vector3(-1768.595, 2054.887, 123.00), r: 316.6895 },
    { pos: new mp.Vector3(-2605.216, 2895.839, 5.0886), r: 208.4460 },
    { pos: new mp.Vector3(-624.6481, 394.4328, 105.78), r: 2.514523 },
    { pos: new mp.Vector3(-1137.981, -1359.43, 5.0626), r: 105.8877 },
    { pos: new mp.Vector3(107.23672, 6614.707, 32.013), r: 225.6084 },
    { pos: new mp.Vector3(341.61154, 484.3688, 150.65), r: 5.370962 },
    { pos: new mp.Vector3(-1757.299, 2063.002, 121.00), r: 345.1367 },
    { pos: new mp.Vector3(-2968.150, 486.4693, 15.468), r: 175.1321 },
    { pos: new mp.Vector3(356.09130, -1684.08, 48.302), r: 190.6129 },
    { pos: new mp.Vector3(340.93872, 497.0061, 151.92), r: 7.844294 },
    { pos: new mp.Vector3(1620.5380, 3759.289, 34.697), r: 233.1314 },
    { pos: new mp.Vector3(-2287.293, 2849.167, 3.9019), r: 172.7127 },
    { pos: new mp.Vector3(340.80117, 509.5793, 152.81), r: 352.2648 },
    { pos: new mp.Vector3(344.19363, -1683.12, 48.302), r: 93.14177 },
    { pos: new mp.Vector3(-1047.516, -1062.46, 2.3974), r: 35.33124 },
    { pos: new mp.Vector3(1642.8272, 3776.130, 34.774), r: 257.7786 },
    { pos: new mp.Vector3(-1126.075, -1341.17, 5.1337), r: 33.68841 },
    { pos: new mp.Vector3(-2262.760, 2811.124, 2.9348), r: 208.4132 },
    { pos: new mp.Vector3(-2967.746, 473.9098, 15.468), r: 85.02425 },
    { pos: new mp.Vector3(117.63203, 6625.021, 31.930), r: 222.6345 },
    { pos: new mp.Vector3(-559.6375, 399.2588, 110.67), r: 311.1622 },
    { pos: new mp.Vector3(336.80001, 561.6958, 156.44), r: 14.37504 },
    { pos: new mp.Vector3(39.884292, 2792.600, 57.894), r: 217.1481 },
    { pos: new mp.Vector3(343.61663, -1683.89, 43.051), r: 303.0144 },
    { pos: new mp.Vector3(-2271.348, 2733.053, 2.5161), r: 24.20086 },
    { pos: new mp.Vector3(-510.1184, 425.2308, 107.42), r: 177.1182 },
    { pos: new mp.Vector3(326.41094, 578.6348, 155.44), r: 38.90471 },
    { pos: new mp.Vector3(-1101.676, -1330.95, 5.2723), r: 29.08321 },
    { pos: new mp.Vector3(-1039.421, -1048.62, 2.2001), r: 119.5448 },
    { pos: new mp.Vector3(320.58325, 585.8150, 155.99), r: 39.27330 },
    { pos: new mp.Vector3(50.887584, 2788.400, 57.878), r: 180.4899 },
    { pos: new mp.Vector3(-490.3404, 392.3218, 110.18), r: 46.22779 },
    { pos: new mp.Vector3(-2293.417, 2759.649, 2.8992), r: 178.0438 },
    { pos: new mp.Vector3(1688.3409, 3826.094, 34.887), r: 353.7674 },
    { pos: new mp.Vector3(-3109.005, 1307.809, 20.148), r: 305.3796 },
    { pos: new mp.Vector3(306.06881, 599.3490, 155.95), r: 53.31198 },
    { pos: new mp.Vector3(-1045.290, -1037.40, 2.1925), r: 119.7167 },
    { pos: new mp.Vector3(-2371.229, 2700.447, 3.6781), r: 98.82510 },
    { pos: new mp.Vector3(344.13671, -1682.57, 37.786), r: 158.6590 },
    { pos: new mp.Vector3(288.54901, 619.7182, 156.46), r: 20.26791 },
    { pos: new mp.Vector3(-2407.220, 2690.382, 2.8691), r: 295.5975 },
    { pos: new mp.Vector3(1665.9736, 3847.494, 34.861), r: 28.81604 },
    { pos: new mp.Vector3(-1053.124, -1055.17, 2.1797), r: 303.1374 },
    { pos: new mp.Vector3(-1041.024, -1350.89, 5.5415), r: 41.13765 },
    { pos: new mp.Vector3(295.34658, 646.2092, 161.16), r: 337.0462 },
    { pos: new mp.Vector3(-446.9474, 397.8055, 112.45), r: 1.002286 },
    { pos: new mp.Vector3(344.31256, -1683.20, 32.530), r: 56.20717 },
    { pos: new mp.Vector3(1644.5870, 3832.156, 34.930), r: 42.01874 },
    { pos: new mp.Vector3(-2373.438, 2647.598, 1.3130), r: 31.97665 },
    { pos: new mp.Vector3(344.36166, -1679.78, 32.530), r: 59.10100 },
    { pos: new mp.Vector3(-1049.416, -1356.28, 5.5535), r: 5.121808 },
    { pos: new mp.Vector3(67.606246, 6640.226, 31.860), r: 199.5592 },
    { pos: new mp.Vector3(299.73434, 668.1678, 165.14), r: 353.0785 },
    { pos: new mp.Vector3(-2290.990, 2671.070, 2.1380), r: 312.6880 },
    { pos: new mp.Vector3(305.78207, 681.1149, 168.79), r: 326.4049 },
    { pos: new mp.Vector3(345.02996, -1692.90, 32.530), r: 148.8872 },
    { pos: new mp.Vector3(-2241.858, 2713.557, 2.7869), r: 242.9801 },
    { pos: new mp.Vector3(1626.8742, 3813.999, 34.857), r: 94.69594 },
    { pos: new mp.Vector3(313.29049, 706.2525, 174.52), r: 342.8580 },
    { pos: new mp.Vector3(-2428.166, 3648.347, 13.966), r: 58.63712 },
    { pos: new mp.Vector3(335.84588, -1702.76, 29.283), r: 2.952136 },
    { pos: new mp.Vector3(316.36862, 718.1192, 177.26), r: 328.2888 },
    { pos: new mp.Vector3(41.102504, 6651.638, 31.659), r: 165.0170 },
    { pos: new mp.Vector3(-2122.113, 2719.048, 3.6870), r: 230.6873 },
    { pos: new mp.Vector3(329.00677, -1697.11, 29.285), r: 55.41973 },
    { pos: new mp.Vector3(314.60607, 732.7987, 180.31), r: 10.53315 },
    { pos: new mp.Vector3(-2427.126, 3626.165, 14.602), r: 49.46418 },
    { pos: new mp.Vector3(323.64923, -1692.80, 29.528), r: 30.57791 },
    { pos: new mp.Vector3(1606.5864, 3797.083, 34.853), r: 75.67829 },
    { pos: new mp.Vector3(-398.6017, 398.6179, 109.10), r: 323.9781 },
    { pos: new mp.Vector3(313.54580, 748.5299, 182.66), r: 354.8419 },
    { pos: new mp.Vector3(-2055.837, 2678.751, 2.9056), r: 0.520154 },
    { pos: new mp.Vector3(-1012.449, -1355.26, 5.4961), r: 39.68482 },
    { pos: new mp.Vector3(315.19729, 762.6300, 183.95), r: 353.2219 },
    { pos: new mp.Vector3(-388.1754, 380.4615, 108.76), r: 197.7570 },
    { pos: new mp.Vector3(-2063.616, 2715.250, 3.7661), r: 179.0703 },
    { pos: new mp.Vector3(276.25106, -1693.68, 29.260), r: 69.75891 },
    { pos: new mp.Vector3(51.778697, 2767.896, 57.878), r: 160.3457 },
    { pos: new mp.Vector3(303.17980, 767.3609, 184.50), r: 142.2987 },
    { pos: new mp.Vector3(1580.6787, 3774.698, 34.700), r: 68.01085 },
    { pos: new mp.Vector3(40.794532, 2773.088, 58.109), r: 68.09969 },
    { pos: new mp.Vector3(-2034.431, 2699.835, 3.7298), r: 350.1951 },
    { pos: new mp.Vector3(-399.0624, 368.6790, 108.76), r: 342.9115 },
    { pos: new mp.Vector3(269.23410, -1699.87, 29.380), r: 195.7250 },
    { pos: new mp.Vector3(295.68481, 786.9263, 186.10), r: 346.6186 },
    { pos: new mp.Vector3(265.78076, -1704.44, 29.457), r: 139.0730 },
    { pos: new mp.Vector3(296.07910, 799.7280, 187.92), r: 1.419265 },
    { pos: new mp.Vector3(-2190.791, 4329.474, 49.972), r: 98.26670 },
    { pos: new mp.Vector3(-1928.977, 2701.050, 4.4669), r: 327.5602 },
    { pos: new mp.Vector3(-1035.179, -1309.73, 6.1805), r: 348.1428 },
    { pos: new mp.Vector3(1555.9322, 3755.766, 34.487), r: 114.4308 },
    { pos: new mp.Vector3(300.12109, 810.4875, 189.89), r: 331.4566 },
    { pos: new mp.Vector3(256.91766, -1714.75, 29.271), r: 142.4915 },
    { pos: new mp.Vector3(260.00997, 2618.335, 44.934), r: 316.2922 },
    { pos: new mp.Vector3(-24.04030, -143.211, 56.975), r: 316.0689 },
    { pos: new mp.Vector3(-1886.512, 2696.070, 4.2395), r: 216.4199 },
    { pos: new mp.Vector3(252.06237, -1720.39, 29.284), r: 149.3983 },
    { pos: new mp.Vector3(297.69766, 818.2817, 190.86), r: 60.09365 },
    { pos: new mp.Vector3(267.77239, 2621.419, 44.673), r: 317.4682 },
    { pos: new mp.Vector3(-1826.752, 2636.5, .1297535), r: 168.7721 },
    { pos: new mp.Vector3(296.62094, 827.3444, 191.57), r: 326.9321 },
    { pos: new mp.Vector3(277.24087, 2623.177, 44.538), r: 296.3510 },
    { pos: new mp.Vector3(247.56913, -1725.45, 29.328), r: 35.54109 },
    { pos: new mp.Vector3(-1899.859, 2603.904, 2.7418), r: 93.51667 },
    { pos: new mp.Vector3(-388.7182, 405.5195, 110.58), r: 313.3285 },
    { pos: new mp.Vector3(245.31062, -1731.76, 29.368), r: 152.5772 },
    { pos: new mp.Vector3(1574.0980, 3726.449, 34.510), r: 212.6294 },
    { pos: new mp.Vector3(297.19100, 2624.208, 44.574), r: 311.2822 },
    { pos: new mp.Vector3(311.00592, 837.7299, 192.47), r: 317.2425 },
    { pos: new mp.Vector3(-1933.346, 2548.001, 2.4941), r: 158.0691 },
    { pos: new mp.Vector3(301.06704, 2630.310, 44.597), r: 344.2906 },
    { pos: new mp.Vector3(-2214.950, 4269.446, 47.766), r: 43.74337 },
    { pos: new mp.Vector3(321.74307, 845.3610, 193.28), r: 307.1195 },
    { pos: new mp.Vector3(-1855.715, 2503.471, 3.7312), r: 246.0436 },
    { pos: new mp.Vector3(299.36434, 2646.955, 44.523), r: 344.7075 },
    { pos: new mp.Vector3(1592.5216, 3675.029, 34.408), r: 252.2862 },
    { pos: new mp.Vector3(334.15148, 855.4027, 194.46), r: 318.2720 },
    { pos: new mp.Vector3(291.44546, 2648.089, 44.543), r: 107.7437 },
    { pos: new mp.Vector3(-373.4100, 416.1447, 110.65), r: 248.1213 },
    { pos: new mp.Vector3(180.32008, -202.908, 54.439), r: 256.0379 },
    { pos: new mp.Vector3(-1940.116, 2481.821, 4.7375), r: 307.3987 },
    { pos: new mp.Vector3(218.41590, -1720.83, 29.291), r: 155.6911 },
    { pos: new mp.Vector3(1604.0717, 3658.998, 34.330), r: 17.68097 },
    { pos: new mp.Vector3(275.19821, 2643.091, 44.479), r: 153.6693 },
    { pos: new mp.Vector3(356.92617, 866.5912, 196.03), r: 327.0438 },
    { pos: new mp.Vector3(-2086.922, 2482.643, 3.1476), r: 165.1044 },
    { pos: new mp.Vector3(374.38119, 871.1489, 197.10), r: 259.8926 },
    { pos: new mp.Vector3(269.90478, -260.354, 53.996), r: 359.7978 },
    { pos: new mp.Vector3(-2.979417, 6664.982, 31.172), r: 180.7012 },
    { pos: new mp.Vector3(1627.7178, 3620.845, 35.441), r: 264.4861 },
    { pos: new mp.Vector3(270.52450, 2597.912, 44.688), r: 240.2061 },
    { pos: new mp.Vector3(393.64743, 872.0851, 198.27), r: 285.4787 },
    { pos: new mp.Vector3(229.76770, -1707.98, 29.304), r: 267.0502 },
    { pos: new mp.Vector3(243.33874, 2597.455, 45.140), r: 71.41969 },
    { pos: new mp.Vector3(409.97390, 873.4265, 198.38), r: 260.3943 },
    { pos: new mp.Vector3(-2114.420, 2447.485, 2.9885), r: 93.13307 },
    { pos: new mp.Vector3(386.37362, -263.760, 53.591), r: 117.2959 },
    { pos: new mp.Vector3(-856.8520, 695.8339, 156.08), r: 19.84406 },
    { pos: new mp.Vector3(245.76493, -1696.10, 29.173), r: 337.6455 },
    { pos: new mp.Vector3(414.09130, 860.0410, 197.25), r: 168.3122 },
    { pos: new mp.Vector3(407.97457, 853.7674, 196.32), r: 99.89530 },
    { pos: new mp.Vector3(242.89054, -1690.85, 29.292), r: 139.1356 },
    { pos: new mp.Vector3(-867.2559, 698.7737, 153.16), r: 124.3448 },
    { pos: new mp.Vector3(-1027.117, -1284.61, 6.2166), r: 33.77250 },
    { pos: new mp.Vector3(533.99035, 2674.775, 42.244), r: 249.4910 },
    { pos: new mp.Vector3(523.29779, -293.296, 46.592), r: 111.4451 },
    { pos: new mp.Vector3(-2300.737, 2426.591, 2.9046), r: 2.594068 },
    { pos: new mp.Vector3(408.53942, 840.5258, 194.47), r: 232.9517 },
    { pos: new mp.Vector3(1645.6722, 3586.416, 35.321), r: 247.9235 },
    { pos: new mp.Vector3(550.53179, 2674.624, 42.174), r: 242.8155 },
    { pos: new mp.Vector3(410.90106, 830.6707, 194.09), r: 162.8125 },
    { pos: new mp.Vector3(402.69665, 823.8291, 193.41), r: 106.3948 },
    { pos: new mp.Vector3(-876.8102, 697.9854, 154.50), r: 309.0407 },
    { pos: new mp.Vector3(563.69763, 2679.398, 42.099), r: 283.7389 },
    { pos: new mp.Vector3(472.89529, -277.156, 48.891), r: 238.9237 },
    { pos: new mp.Vector3(395.53878, 818.6309, 192.90), r: 149.8430 },
    { pos: new mp.Vector3(1657.8806, 3566.019, 35.401), r: 321.7941 },
    { pos: new mp.Vector3(-2421.497, 2559.619, 2.5173), r: 351.6531 },
    { pos: new mp.Vector3(396.36737, 811.0120, 190.77), r: 248.0922 },
    { pos: new mp.Vector3(578.68530, 2700.419, 41.751), r: 305.2272 },
    { pos: new mp.Vector3(404.13070, 805.4572, 190.41), r: 195.0117 },
    { pos: new mp.Vector3(-981.1336, -1258.01, 5.7214), r: 23.12572 },
    { pos: new mp.Vector3(-867.8573, 5416.513, 35.206), r: 348.4691 },
    { pos: new mp.Vector3(-1070.641, -1022.56, 2.1624), r: 294.7433 },
    { pos: new mp.Vector3(402.37228, 797.1220, 189.15), r: 137.3538 },
    { pos: new mp.Vector3(604.49078, 2705.666, 41.322), r: 253.5306 },
    { pos: new mp.Vector3(396.76644, 791.1469, 187.97), r: 133.1532 },
    { pos: new mp.Vector3(263.55416, -1676.62, 29.377), r: 315.0509 },
    { pos: new mp.Vector3(-952.4515, 690.3817, 156.99), r: 72.89112 },
    { pos: new mp.Vector3(384.35958, 786.8278, 187.47), r: 88.27954 },
    { pos: new mp.Vector3(630.45483, 2708.709, 41.066), r: 236.1618 },
    { pos: new mp.Vector3(-958.0230, -1246.39, 5.5760), r: 42.17932 },
    { pos: new mp.Vector3(-1059.683, -1014.23, 2.1654), r: 121.4046 },
    { pos: new mp.Vector3(522.14843, -162.871, 56.088), r: 112.9500 },
    { pos: new mp.Vector3(366.48333, 793.1596, 187.71), r: 65.31610 },
    { pos: new mp.Vector3(651.47882, 2707.313, 40.734), r: 264.7265 },
    { pos: new mp.Vector3(359.44540, 798.2653, 188.29), r: 43.50179 },
    { pos: new mp.Vector3(540.15515, -191.611, 54.481), r: 44.68408 },
    { pos: new mp.Vector3(-1069.284, -994.801, 2.2253), r: 118.1983 },
    { pos: new mp.Vector3(1667.1260, 3550.127, 35.481), r: 277.4738 },
    { pos: new mp.Vector3(-978.2069, 686.9453, 165.66), r: 1.955781 },
    { pos: new mp.Vector3(-975.2679, -1213.18, 5.2313), r: 164.7122 },
    { pos: new mp.Vector3(1010.0974, 2683.276, 39.536), r: 287.5197 },
    { pos: new mp.Vector3(342.49682, -1741.31, 29.382), r: 280.8085 },
    { pos: new mp.Vector3(-1082.666, -1002.01, 2.1893), r: 353.9268 },
    { pos: new mp.Vector3(334.74707, -1750.30, 29.509), r: 144.3022 },
    { pos: new mp.Vector3(-999.2965, 685.7809, 165.56), r: 345.1224 },
    { pos: new mp.Vector3(328.68322, -1757.56, 30.589), r: 50.47590 },
    { pos: new mp.Vector3(1076.2459, 2680.112, 38.843), r: 118.5563 },
    { pos: new mp.Vector3(325.82012, -1760.04, 29.277), r: 168.7912 },
    { pos: new mp.Vector3(1688.0534, 3512.347, 36.427), r: 250.6891 },
    { pos: new mp.Vector3(-2538.311, 2667.910, 2.9348), r: 0.597937 },
    { pos: new mp.Vector3(317.58984, -1773.14, 28.715), r: 59.03225 },
    { pos: new mp.Vector3(-1637.244, -767.372, 10.132), r: 54.43568 },
    { pos: new mp.Vector3(-2513.954, 2726.550, 2.8499), r: 92.28189 },
    { pos: new mp.Vector3(501.47576, 5598.901, 796.20), r: 160.0826 },
    { pos: new mp.Vector3(-2562.277, 2708.013, 2.8447), r: 255.3193 },
    { pos: new mp.Vector3(310.80966, -1777.45, 28.661), r: 240.5171 },
    { pos: new mp.Vector3(-1032.377, -971.469, 2.1501), r: 114.3682 },
    { pos: new mp.Vector3(-1007.368, 804.1380, 181.46), r: 165.5025 },
    { pos: new mp.Vector3(1685.1881, 3512.685, 36.397), r: 265.2453 },
    { pos: new mp.Vector3(313.12185, -1776.09, 28.686), r: 317.0549 },
    { pos: new mp.Vector3(-20.80895, 352.8492, 113.16), r: 207.4697 },
    { pos: new mp.Vector3(-1733.209, -708.919, 10.130), r: 213.0468 },
    { pos: new mp.Vector3(-2604.176, 3074.573, 32.521), r: 6.069498 },
    { pos: new mp.Vector3(-1033.311, 794.4393, 168.36), r: 230.7162 },
    { pos: new mp.Vector3(1670.6804, 3501.381, 36.421), r: 191.3097 },
    { pos: new mp.Vector3(-1062.559, -988.534, 2.1501), r: 210.2972 },
    { pos: new mp.Vector3(-33.64992, 374.5930, 114.73), r: 214.9904 },
    { pos: new mp.Vector3(-2660.759, 3292.074, 32.936), r: 57.39274 },
    { pos: new mp.Vector3(-2604.951, 3260.815, 32.938), r: 239.0429 },
    { pos: new mp.Vector3(2472.8862, -408.334, 109.52), r: 142.5951 },
    { pos: new mp.Vector3(1654.8785, 3495.311, 36.472), r: 206.6369 },
    { pos: new mp.Vector3(-77.28997, -824.445, 321.29), r: 184.3817 },
    { pos: new mp.Vector3(-1087.904, 803.4307, 176.11), r: 104.1675 },
    { pos: new mp.Vector3(2472.9885, -418.596, 109.52), r: 189.0943 },
    { pos: new mp.Vector3(2468.75, .105529785, 109.52), r: 70.52986 },
    { pos: new mp.Vector3(1639.5085, 3491.040, 36.545), r: 168.8261 },
    { pos: new mp.Vector3(-2550.567, 3229.228, 32.944), r: 295.0227 },
    { pos: new mp.Vector3(-118.8309, 304.2623, 108.07), r: 268.8486 },
    { pos: new mp.Vector3(2466.8146, -412.768, 109.52), r: 32.58427 },
    { pos: new mp.Vector3(2461.4042, -400.012, 109.52), r: 13.40048 },
    { pos: new mp.Vector3(-1085.937, 823.5209, 172.36), r: 100.9076 },
    { pos: new mp.Vector3(-1014.890, -958.869, 2.1501), r: 29.13045 },
    { pos: new mp.Vector3(1286.8142, -1714.50, 55.046), r: 199.4669 },
    { pos: new mp.Vector3(-2465.375, 3181.813, 32.965), r: 325.7340 },
    { pos: new mp.Vector3(-1098.371, 831.7609, 168.61), r: 29.45827 },
    { pos: new mp.Vector3(2508.7209, -412.966, 114.11), r: 212.6099 },
    { pos: new mp.Vector3(-2410.498, 3148.091, 33.026), r: 297.3100 },
    { pos: new mp.Vector3(-1103.492, 837.4072, 168.43), r: 314.4970 },
    { pos: new mp.Vector3(1292.1738, -1711.74, 55.081), r: 199.4863 },
    { pos: new mp.Vector3(-1009.346, -956.263, 2.1503), r: 221.6069 },
    { pos: new mp.Vector3(2579.9357, -384.555, 93.234), r: 331.2860 },
    { pos: new mp.Vector3(-2336.049, 3104.678, 33.101), r: 240.6604 },
    { pos: new mp.Vector3(2581.4345, -374.068, 93.179), r: 5.315004 },
    { pos: new mp.Vector3(-1109.255, 818.7863, 167.33), r: 300.0852 },
    { pos: new mp.Vector3(2580.6455, -355.147, 93.177), r: 9.112425 },
    { pos: new mp.Vector3(-1845.416, -388.002, 49.265), r: 73.70594 },
    { pos: new mp.Vector3(2581.7502, -344.365, 93.190), r: 354.3854 },
    { pos: new mp.Vector3(-973.5243, -1017.96, 2.1503), r: 350.0497 },
    { pos: new mp.Vector3(-1747.730, -726.266, 10.423), r: 193.2167 },
    { pos: new mp.Vector3(-1830.199, -392.770, 57.138), r: 5.503993 },
    { pos: new mp.Vector3(2541.2355, -337.973, 94.120), r: 109.8680 },
    { pos: new mp.Vector3(2529.5375, -347.255, 94.120), r: 119.2958 },
    { pos: new mp.Vector3(-969.4169, -1022.11, 2.1500), r: 22.16362 },
    { pos: new mp.Vector3(2526.5512, -346.320, 94.149), r: 86.11437 },
    { pos: new mp.Vector3(1306.6351, 1199.550, 107.09), r: 85.60724 },
    { pos: new mp.Vector3(-2278.252, 3062.176, 32.812), r: 108.6579 },
    { pos: new mp.Vector3(-977.4988, -1026.08, 2.1500), r: 27.90298 },
    { pos: new mp.Vector3(185.92665, -1924.94, 20.975), r: 342.4038 },
    { pos: new mp.Vector3(2510.4750, -366.149, 94.120), r: 4.781108 },
    { pos: new mp.Vector3(183.94805, -1928.67, 21.453), r: 156.2940 },
    { pos: new mp.Vector3(1367.5017, 1159.818, 113.75), r: 31.38865 },
    { pos: new mp.Vector3(2512.7915, -363.498, 94.122), r: 295.1465 },
    { pos: new mp.Vector3(-1747.343, -699.549, 10.165), r: 331.6128 },
    { pos: new mp.Vector3(-2250.064, 3111.671, 32.813), r: 12.62853 },
    { pos: new mp.Vector3(-1858.090, -324.092, 57.113), r: 270.3968 },
    { pos: new mp.Vector3(1370.4624, 1157.563, 113.75), r: 218.1770 },
    { pos: new mp.Vector3(173.51878, -1942.08, 21.402), r: 46.24971 },
    { pos: new mp.Vector3(-977.4650, -1017.63, 2.1503), r: 77.23180 },
    { pos: new mp.Vector3(-2195.189, 3056.706, 32.943), r: 177.4955 },
    { pos: new mp.Vector3(1372.0360, 1139.969, 113.76), r: 205.3348 },
    { pos: new mp.Vector3(2496.3066, -368.958, 94.128), r: 98.84130 },
    { pos: new mp.Vector3(167.44929, -1947.68, 19.842), r: 167.1304 },
    { pos: new mp.Vector3(-1754.017, -688.254, 10.062), r: 314.4837 },
    { pos: new mp.Vector3(1374.8675, 1128.242, 114.03), r: 222.6002 },
    { pos: new mp.Vector3(-2154.408, 3049.385, 34.212), r: 265.1096 },
    { pos: new mp.Vector3(2484.6948, -369.130, 93.735), r: 148.8060 },
    { pos: new mp.Vector3(-1153.372, 791.5995, 164.10), r: 177.8599 },
    { pos: new mp.Vector3(162.40983, -1959.76, 19.004), r: 109.8541 },
    { pos: new mp.Vector3(1395.5925, 1121.080, 114.83), r: 269.7054 },
    { pos: new mp.Vector3(-1763.595, -683.462, 10.213), r: 334.6195 },
    { pos: new mp.Vector3(2481.6147, -359.360, 93.735), r: 319.5892 },
    { pos: new mp.Vector3(156.58445, -1963.55, 19.114), r: 262.3657 },
    { pos: new mp.Vector3(-1001.962, -1033.60, 2.1503), r: 335.4479 },
    { pos: new mp.Vector3(1415.5662, 1124.861, 114.83), r: 299.3540 },
    { pos: new mp.Vector3(2482.7348, -350.324, 93.735), r: 2.365960 },
    { pos: new mp.Vector3(1419.3608, 1114.438, 114.78), r: 209.9704 },
    { pos: new mp.Vector3(2478.0886, -350.295, 93.735), r: 112.5129 },
    { pos: new mp.Vector3(2478.0383, -358.144, 93.735), r: 179.2650 },
    { pos: new mp.Vector3(-1769.112, -673.477, 10.291), r: 311.6423 },
    { pos: new mp.Vector3(1439.0155, 1132.502, 114.38), r: 33.09406 },
    { pos: new mp.Vector3(-2069.805, 2289.346, 40.164), r: 84.32363 },
    { pos: new mp.Vector3(2487.7260, -371.683, 93.735), r: 207.8858 },
    { pos: new mp.Vector3(150.06210, -1969.59, 18.566), r: 155.1060 },
    { pos: new mp.Vector3(-131.6105, 6549.920, 29.613), r: 227.9174 },
    { pos: new mp.Vector3(2487.8955, -378.892, 93.735), r: 148.6526 },
    { pos: new mp.Vector3(-1005.111, -1036.18, 2.1503), r: 56.93642 },
    { pos: new mp.Vector3(1437.1405, 1152.747, 114.07), r: 41.46392 },
    { pos: new mp.Vector3(2486.6958, -388.160, 93.735), r: 191.8812 },
    { pos: new mp.Vector3(-1781.661, -663.207, 10.408), r: 269.8271 },
    { pos: new mp.Vector3(1436.8033, 1164.755, 114.40), r: 4.361840 },
    { pos: new mp.Vector3(1425.9094, 1172.286, 114.54), r: 111.9304 },
    { pos: new mp.Vector3(1418.5107, 1177.724, 114.33), r: 52.20329 },
    { pos: new mp.Vector3(2513.9926, -406.005, 94.120), r: 218.2772 },
    { pos: new mp.Vector3(-1031.490, -1050.41, 2.1503), r: 311.4787 },
    { pos: new mp.Vector3(-1791.599, -652.553, 10.612), r: 184.9990 },
    { pos: new mp.Vector3(-1748.150, -316.941, 46.825), r: 232.9145 },
    { pos: new mp.Vector3(1408.7872, 1169.340, 114.33), r: 108.2842 },
    { pos: new mp.Vector3(2518.7407, -410.311, 94.116), r: 257.2985 },
    { pos: new mp.Vector3(-109.1992, 6542.429, 29.687), r: 40.87421 },
    { pos: new mp.Vector3(-2144.935, 3035.937, 32.810), r: 334.4843 },
    { pos: new mp.Vector3(-1719.453, -306.003, 50.804), r: 86.10096 },
    { pos: new mp.Vector3(1382.5416, 1182.904, 114.33), r: 72.83518 },
    { pos: new mp.Vector3(1639.3135, 3490.660, 36.594), r: 241.2415 },
    { pos: new mp.Vector3(-1794.582, -666.225, 10.495), r: 6.807109 },
    { pos: new mp.Vector3(2526.9606, -418.943, 94.120), r: 183.2439 },
    { pos: new mp.Vector3(1369.2541, 1183.207, 114.01), r: 111.0437 },
    { pos: new mp.Vector3(2529.8139, -421.073, 94.120), r: 211.3905 },
    { pos: new mp.Vector3(-1150.797, 772.7792, 159.03), r: 187.7527 },
    { pos: new mp.Vector3(1382.0185, 1167.566, 114.32), r: 228.3734 },
    { pos: new mp.Vector3(-2132.278, 3026.958, 32.810), r: 313.4271 },
    { pos: new mp.Vector3(1622.1312, 3506.036, 37.044), r: 217.5521 },
    { pos: new mp.Vector3(-1001.943, -1075.49, 2.1505), r: 182.2694 },
    { pos: new mp.Vector3(-1147.718, 752.6633, 156.35), r: 242.9346 },
    { pos: new mp.Vector3(2538.3105, -430.143, 94.120), r: 214.2449 },
    { pos: new mp.Vector3(-1815.031, -678.770, 10.399), r: 142.9847 },
    { pos: new mp.Vector3(-1794.681, -181.369, 60.367), r: 216.1116 },
    { pos: new mp.Vector3(-2018.096, 2978.020, 32.837), r: 281.7835 },
    { pos: new mp.Vector3(1382.8502, 1157.383, 114.33), r: 199.5515 },
    { pos: new mp.Vector3(-998.8837, -1073.78, 2.1503), r: 263.1330 },
    { pos: new mp.Vector3(1637.9233, 3514.949, 36.154), r: 231.1431 },
    { pos: new mp.Vector3(-1449.384, 872.7521, 183.51), r: 344.4380 },
    { pos: new mp.Vector3(1381.9001, 1141.423, 114.33), r: 179.4820 },
    { pos: new mp.Vector3(-1757.142, -163.329, 62.973), r: 223.0596 },
    { pos: new mp.Vector3(-2103.967, 2963.885, 32.756), r: 89.76834 },
    { pos: new mp.Vector3(-1785.007, -704.437, 10.406), r: 139.5864 },
    { pos: new mp.Vector3(-206.6566, 6447.924, 31.189), r: 270.2807 },
    { pos: new mp.Vector3(1388.7519, 1139.310, 114.37), r: 192.7770 },
    { pos: new mp.Vector3(-986.0758, -1065.46, 2.1503), r: 275.2091 },
    { pos: new mp.Vector3(-1474.687, 891.7939, 182.79), r: 263.8567 },
    { pos: new mp.Vector3(1647.2253, 3530.471, 35.919), r: 264.9036 },
    { pos: new mp.Vector3(-1729.197, -192.791, 58.505), r: 137.7669 },
    { pos: new mp.Vector3(-1974.237, 2892.048, 32.831), r: 198.5939 },
    { pos: new mp.Vector3(-1890.982, -734.996, 6.3346), r: 143.4973 },
    { pos: new mp.Vector3(1293.8055, 1078.646, 105.69), r: 311.9111 },
    { pos: new mp.Vector3(-974.5382, -1058.78, 2.1718), r: 295.5718 },
    { pos: new mp.Vector3(-1902.204, -714.843, 7.0272), r: 127.9034 },
    { pos: new mp.Vector3(1632.0052, 3556.380, 35.248), r: 308.5092 },
    { pos: new mp.Vector3(1310.0034, 1085.925, 105.54), r: 355.9909 },
    { pos: new mp.Vector3(-1949.852, 2935.053, 32.965), r: 303.2555 },
    { pos: new mp.Vector3(1313.0631, 1035.576, 105.73), r: 185.2417 },
    { pos: new mp.Vector3(1623.0715, 3563.432, 35.191), r: 325.6341 },
    { pos: new mp.Vector3(1315.0777, 984.0513, 105.87), r: 173.9407 },
    { pos: new mp.Vector3(1286.2277, 910.0181, 107.18), r: 158.5443 },
    { pos: new mp.Vector3(-2051.335, 2841.440, 32.809), r: 343.1037 },
    { pos: new mp.Vector3(-2011.268, -561.297, 11.007), r: 137.8273 },
    { pos: new mp.Vector3(1615.1047, 3577.586, 35.146), r: 302.6475 },
    { pos: new mp.Vector3(1294.1467, 970.6719, 106.44), r: 29.70680 },
    { pos: new mp.Vector3(-1985.651, 2808.268, 32.807), r: 275.9378 },
    { pos: new mp.Vector3(-315.2028, 6334.442, 30.753), r: 47.53246 },
    { pos: new mp.Vector3(1253.7694, 776.6625, 103.82), r: 156.1281 },
    { pos: new mp.Vector3(-1221.685, 680.9978, 145.25), r: 347.7879 },
    { pos: new mp.Vector3(1262.9594, 731.2196, 102.05), r: 152.5068 },
    { pos: new mp.Vector3(-2063.260, 2816.135, 32.809), r: 91.63369 },
    { pos: new mp.Vector3(1203.3458, 630.3680, 98.671), r: 154.1466 },
    { pos: new mp.Vector3(-950.9469, -1044.91, 2.1503), r: 206.5410 },
    { pos: new mp.Vector3(1609.3337, 3587.390, 35.146), r: 187.4118 },
    { pos: new mp.Vector3(1145.8688, 592.2523, 99.767), r: 104.2812 },
    { pos: new mp.Vector3(-1840.408, -609.506, 11.392), r: 89.80281 },
    { pos: new mp.Vector3(-2087.199, 2815.788, 32.960), r: 192.0687 },
    { pos: new mp.Vector3(1122.5389, 541.5267, 96.555), r: 158.8529 },
    { pos: new mp.Vector3(-1659.010, -178.148, 57.244), r: 326.3504 },
    { pos: new mp.Vector3(1093.6381, 451.2719, 91.585), r: 167.5010 },
    { pos: new mp.Vector3(1609.3548, 3587.581, 35.146), r: 321.6036 },
    { pos: new mp.Vector3(-927.9401, -1078.08, 2.1503), r: 28.98815 },
    { pos: new mp.Vector3(-1858.546, -598.973, 11.737), r: 279.6983 },
    { pos: new mp.Vector3(-2099.633, 2832.795, 32.809), r: 98.11762 },
    { pos: new mp.Vector3(-1628.714, -244.846, 53.779), r: 211.7933 },
    { pos: new mp.Vector3(2545.8186, -438.208, 93.195), r: 168.2929 },
    { pos: new mp.Vector3(924.81567, 73.02637, 79.232), r: 17.19277 },
    { pos: new mp.Vector3(-1647.721, -272.442, 52.843), r: 357.4518 },
    { pos: new mp.Vector3(-2279.304, 2964.379, 32.873), r: 328.9210 },
    { pos: new mp.Vector3(2530.7741, -453.331, 93.195), r: 124.0821 },
    { pos: new mp.Vector3(-915.6181, -1094.12, 2.1500), r: 318.2288 },
    { pos: new mp.Vector3(-1874.236, -612.002, 11.874), r: 196.1014 },
    { pos: new mp.Vector3(2518.2373, -456.201, 92.992), r: 68.64333 },
    { pos: new mp.Vector3(-2358.840, 2954.120, 32.869), r: 5.455382 },
    { pos: new mp.Vector3(-1262.304, 651.1171, 140.47), r: 203.0771 },
    { pos: new mp.Vector3(2509.2282, -461.313, 93.098), r: 152.0526 },
    { pos: new mp.Vector3(906.27905, 54.73236, 79.289), r: 136.0637 },
    { pos: new mp.Vector3(-2366.005, 2973.725, 32.827), r: 15.29961 },
    { pos: new mp.Vector3(901.55908, 47.17639, 79.211), r: 159.4001 },
    { pos: new mp.Vector3(-2402.006, 2979.457, 33.332), r: 22.87851 },
    { pos: new mp.Vector3(866.40295, 11.71697, 79.219), r: 117.2582 },
    { pos: new mp.Vector3(-1901.189, -612.196, 11.617), r: 288.9703 },
    { pos: new mp.Vector3(-916.2179, -1114.59, 2.1500), r: 29.78231 },
    { pos: new mp.Vector3(848.28607, -6.90449, 80.086), r: 132.4259 },
    { pos: new mp.Vector3(2466.5122, -447.511, 92.994), r: 314.9018 },
    { pos: new mp.Vector3(1596.4638, 3609.942, 35.146), r: 319.8787 },
    { pos: new mp.Vector3(-1289.101, 619.5916, 145.17), r: 101.0513 },
    { pos: new mp.Vector3(827.97326, -33.0071, 80.751), r: 140.1050 },
    { pos: new mp.Vector3(-418.0173, 6263.643, 30.384), r: 198.4506 },
    { pos: new mp.Vector3(-1871.174, -638.971, 11.056), r: 294.6024 },
    { pos: new mp.Vector3(2480.6601, -459.521, 92.993), r: 209.2360 },
    { pos: new mp.Vector3(1609.5577, 3614.015, 35.291), r: 159.7165 },
    { pos: new mp.Vector3(829.79486, -49.9916, 80.577), r: 201.1198 },
    { pos: new mp.Vector3(-2441.275, 2908.986, 32.809), r: 317.0988 },
    { pos: new mp.Vector3(2434.7858, -379.195, 93.119), r: 130.1310 },
    { pos: new mp.Vector3(849.96728, -68.8579, 80.560), r: 215.8422 },
    { pos: new mp.Vector3(1599.6369, 3631.262, 35.276), r: 121.1774 },
    { pos: new mp.Vector3(-866.2371, -1104.78, 2.4876), r: 166.1496 },
    { pos: new mp.Vector3(2461.6374, -324.895, 92.998), r: 346.9432 },
    { pos: new mp.Vector3(1595.0567, 3634.576, 35.263), r: 154.5086 },
    { pos: new mp.Vector3(889.69653, -89.1737, 78.827), r: 6.322105 },
    { pos: new mp.Vector3(-1888.748, -606.794, 11.825), r: 139.4274 },
    { pos: new mp.Vector3(-2410.854, 2994.285, 32.883), r: 349.6752 },
    { pos: new mp.Vector3(2472.0039, -317.751, 92.994), r: 309.8264 },
    { pos: new mp.Vector3(887.79351, -71.5989, 78.764), r: 19.40147 },
    { pos: new mp.Vector3(876.66992, -65.1248, 78.892), r: 41.80063 },
    { pos: new mp.Vector3(-2448.822, 3016.603, 32.809), r: 32.35382 },
    { pos: new mp.Vector3(1584.4287, 3628.885, 35.213), r: 313.6267 },
    { pos: new mp.Vector3(867.63739, -58.6268, 78.850), r: 70.82250 },
    { pos: new mp.Vector3(-1909.368, -588.776, 11.862), r: 143.0390 },
    { pos: new mp.Vector3(861.05548, -54.7742, 78.894), r: 51.02682 },
    { pos: new mp.Vector3(-880.1704, -1088.03, 2.1583), r: 126.8123 },
    { pos: new mp.Vector3(-2507.070, 3027.216, 32.806), r: 54.68708 },
    { pos: new mp.Vector3(852.72924, -47.1187, 78.764), r: 41.18240 },
    { pos: new mp.Vector3(2498.1562, -292.554, 93.567), r: 229.8619 },
    { pos: new mp.Vector3(-2522.023, 3064.631, 32.839), r: 8.439055 },
    { pos: new mp.Vector3(843.89398, -38.4028, 78.777), r: 96.21131 },
    { pos: new mp.Vector3(2500.5524, -287.427, 93.595), r: 308.6121 },
    { pos: new mp.Vector3(1579.8398, 3644.844, 34.572), r: 17.50322 },
    { pos: new mp.Vector3(-1928.638, -599.864, 11.398), r: 146.5172 },
    { pos: new mp.Vector3(-1562.999, -68.8859, 54.979), r: 253.5598 },
    { pos: new mp.Vector3(834.11444, -42.4424, 78.775), r: 162.9480 },
    { pos: new mp.Vector3(-888.9534, -1071.97, 2.1628), r: 98.71262 },
    { pos: new mp.Vector3(387.25942, -2141.82, 16.725), r: 213.7202 },
    { pos: new mp.Vector3(2497.8059, -288.687, 93.451), r: 107.6364 },
    { pos: new mp.Vector3(-1924.665, 2840.984, 32.717), r: 335.3688 },
    { pos: new mp.Vector3(1587.1040, 3649.898, 34.502), r: 57.13505 },
    { pos: new mp.Vector3(2500.3623, -289.526, 93.650), r: 232.0231 },
    { pos: new mp.Vector3(929.34393, -25.0031, 78.845), r: 73.26044 },
    { pos: new mp.Vector3(379.74957, -2146.75, 16.022), r: 291.2889 },
    { pos: new mp.Vector3(-1290.673, 650.9095, 149.84), r: 268.7415 },
    { pos: new mp.Vector3(917.69854, -16.5805, 78.777), r: 75.82520 },
    { pos: new mp.Vector3(-1932.975, -569.565, 11.821), r: 143.1487 },
    { pos: new mp.Vector3(-900.2017, -1062.38, 2.1500), r: 298.7282 },
    { pos: new mp.Vector3(2511.6203, -277.860, 92.992), r: 321.5993 },
    { pos: new mp.Vector3(-1911.266, 2823.151, 32.790), r: 10.73579 },
    { pos: new mp.Vector3(368.83813, -2149.53, 15.085), r: 91.86459 },
    { pos: new mp.Vector3(-1943.450, 2802.174, 32.598), r: 327.7756 },
    { pos: new mp.Vector3(-903.8919, -1056.67, 2.1500), r: 295.4822 },
    { pos: new mp.Vector3(-1954.999, -550.626, 11.843), r: 137.6056 },
    { pos: new mp.Vector3(369.67639, -2124.34, 16.276), r: 306.3887 },
    { pos: new mp.Vector3(-1452.142, -14.3529, 54.652), r: 125.0515 },
    { pos: new mp.Vector3(368.03958, -2125.49, 16.196), r: 106.1292 },
    { pos: new mp.Vector3(-1776.408, 2836.807, 32.950), r: 316.5866 },
    { pos: new mp.Vector3(-911.0427, -1033.95, 2.1502), r: 256.2414 },
    { pos: new mp.Vector3(-1786.907, 2836.519, 32.950), r: 58.01154 },
    { pos: new mp.Vector3(-1981.389, -534.185, 11.628), r: 147.8864 },
    { pos: new mp.Vector3(-1444.173, 17.44282, 52.605), r: 233.7572 },
    { pos: new mp.Vector3(-1728.828, 2832.996, 33.652), r: 56.84300 },
    { pos: new mp.Vector3(785.56512, -73.4761, 80.428), r: 331.6606 },
    { pos: new mp.Vector3(-1990.543, -519.114, 11.867), r: 51.89032 },
    { pos: new mp.Vector3(2546.1445, -266.453, 94.490), r: 190.3214 },
    { pos: new mp.Vector3(-1707.844, 2879.880, 32.962), r: 66.91756 },
    { pos: new mp.Vector3(-1333.894, 632.0168, 135.70), r: 249.3781 },
    { pos: new mp.Vector3(-1373.128, 226.5343, 58.630), r: 139.0396 },
    { pos: new mp.Vector3(274.59716, -2142.16, 15.650), r: 357.0494 },
    { pos: new mp.Vector3(2556.7917, -286.873, 93.487), r: 304.6510 },
    { pos: new mp.Vector3(-1681.336, 2897.671, 31.988), r: 292.3677 },
    { pos: new mp.Vector3(-1993.876, -493.039, 11.498), r: 52.69991 },
    { pos: new mp.Vector3(265.31341, -2130.54, 16.013), r: 68.79485 },
    { pos: new mp.Vector3(2553.3532, -290.307, 93.457), r: 104.3599 },
    { pos: new mp.Vector3(-923.4635, -1025.56, 2.4799), r: 123.5631 },
    { pos: new mp.Vector3(-1690.024, 2922.597, 33.072), r: 241.4325 },
    { pos: new mp.Vector3(2554.4196, -294.892, 93.490), r: 195.4147 },
    { pos: new mp.Vector3(252.82598, -2117.57, 16.754), r: 8.302908 },
    { pos: new mp.Vector3(-2017.849, -513.672, 11.697), r: 140.6763 },
    { pos: new mp.Vector3(-1698.758, 2974.731, 32.584), r: 209.5758 },
    { pos: new mp.Vector3(2556.7512, -291.734, 93.626), r: 311.4656 },
    { pos: new mp.Vector3(242.39340, -2103.40, 17.107), r: 44.77846 },
    { pos: new mp.Vector3(-1726.301, 2959.422, 32.879), r: 157.3574 },
    { pos: new mp.Vector3(-914.0769, -1026.69, 2.1503), r: 350.8805 },
    { pos: new mp.Vector3(237.14523, -2096.25, 17.275), r: 24.97386 },
    { pos: new mp.Vector3(-1643.412, -123.562, 59.108), r: 158.8779 },
    { pos: new mp.Vector3(-1454.676, 529.8460, 118.73), r: 252.0229 },
    { pos: new mp.Vector3(-1719.330, 3011.649, 33.064), r: 188.7341 },
    { pos: new mp.Vector3(-1641.985, -130.237, 58.784), r: 223.8402 },
    { pos: new mp.Vector3(-919.4479, -1012.48, 2.1503), r: 239.4189 },
    { pos: new mp.Vector3(-1644.546, -138.240, 58.734), r: 101.4256 },
    { pos: new mp.Vector3(-1712.758, 3143.873, 32.942), r: 152.9393 },
    { pos: new mp.Vector3(283.72753, -2057.09, 18.766), r: 59.92488 },
    { pos: new mp.Vector3(-1638.605, -153.194, 57.598), r: 218.2920 },
    { pos: new mp.Vector3(-2085.975, -464.299, 9.1838), r: 137.7553 },
    { pos: new mp.Vector3(2588.3999, -291.233, 93.040), r: 142.9529 },
    { pos: new mp.Vector3(-1703.436, 3196.726, 34.144), r: 152.9393 },
    { pos: new mp.Vector3(2588.6108, -294.898, 93.078), r: 170.1929 },
    { pos: new mp.Vector3(-1476.267, 470.6238, 115.33), r: 218.0869 },
    { pos: new mp.Vector3(299.47982, -2038.19, 19.850), r: 325.9048 },
    { pos: new mp.Vector3(2585.9877, -298.093, 93.078), r: 108.5104 },
    { pos: new mp.Vector3(1587.0577, 3649.928, 34.501), r: 57.23050 },
    { pos: new mp.Vector3(-1127.342, 291.6229, 66.141), r: 326.4409 },
    { pos: new mp.Vector3(304.79177, -2047.57, 20.247), r: 204.0155 },
    { pos: new mp.Vector3(-1737.124, 3243.848, 32.803), r: 324.8614 },
    { pos: new mp.Vector3(1571.6236, 3644.823, 34.401), r: 35.23853 },
    { pos: new mp.Vector3(2571.1989, -290.144, 93.073), r: 225.7039 },
    { pos: new mp.Vector3(-1443.300, 476.5034, 115.25), r: 97.53391 },
    { pos: new mp.Vector3(315.54553, -2062.17, 20.155), r: 185.6917 },
    { pos: new mp.Vector3(-1610.678, -175.077, 55.724), r: 206.8302 },
    { pos: new mp.Vector3(-1750.731, 3251.621, 32.803), r: 23.40950 },
    { pos: new mp.Vector3(2569.7143, -293.083, 93.078), r: 139.1137 },
    { pos: new mp.Vector3(-2050.837, -428.390, 11.483), r: 334.5189 },
    { pos: new mp.Vector3(2569.5615, -298.094, 93.078), r: 183.4717 },
    { pos: new mp.Vector3(-1619.432, -179.416, 55.767), r: 127.2945 },
    { pos: new mp.Vector3(330.89181, -2076.61, 19.528), r: 234.6406 },
    { pos: new mp.Vector3(2572.1440, -298.354, 93.078), r: 261.3765 },
    { pos: new mp.Vector3(-1776.897, 3266.373, 32.807), r: 345.8907 },
    { pos: new mp.Vector3(-999.7573, 358.2241, 71.983), r: 73.23719 },
    { pos: new mp.Vector3(-1636.475, -194.443, 55.360), r: 92.71181 },
    { pos: new mp.Vector3(348.69223, -2077.30, 21.068), r: 280.3448 },
    { pos: new mp.Vector3(350.62762, -2069.93, 20.951), r: 39.40150 },
    { pos: new mp.Vector3(-1794.198, 3276.981, 32.806), r: 337.3847 },
    { pos: new mp.Vector3(-1654.700, -176.717, 57.102), r: 6.407023 },
    { pos: new mp.Vector3(345.68951, -2064.40, 20.890), r: 40.53110 },
    { pos: new mp.Vector3(-2019.400, -400.018, 10.908), r: 110.6908 },
    { pos: new mp.Vector3(2577.8029, -322.852, 93.077), r: 291.5135 },
    { pos: new mp.Vector3(-1023.114, 352.1287, 70.916), r: 323.7684 },
    { pos: new mp.Vector3(2578.6972, -318.874, 92.994), r: 340.6271 },
    { pos: new mp.Vector3(-1667.565, -160.021, 57.690), r: 324.6643 },
    { pos: new mp.Vector3(1550.3148, 3639.082, 34.521), r: 99.89281 },
    { pos: new mp.Vector3(2580.9016, -317.922, 92.995), r: 274.1777 },
    { pos: new mp.Vector3(-923.4151, -1002.34, 2.1503), r: 144.8191 },
    { pos: new mp.Vector3(325.88192, -2046.97, 20.769), r: 51.66440 },
    { pos: new mp.Vector3(-1528.769, 448.7917, 109.64), r: 90.14163 },
    { pos: new mp.Vector3(-1997.173, -420.753, 11.602), r: 77.77627 },
    { pos: new mp.Vector3(2581.5615, -320.823, 92.995), r: 181.6006 },
    { pos: new mp.Vector3(-1679.348, -138.172, 59.927), r: 357.6157 },
    { pos: new mp.Vector3(316.02716, -2039.20, 20.966), r: 45.84646 },
    { pos: new mp.Vector3(1547.0933, 3648.028, 34.511), r: 184.5800 },
    { pos: new mp.Vector3(-1510.849, 455.6953, 111.76), r: 302.0507 },
    { pos: new mp.Vector3(304.70983, -2031.58, 20.439), r: 22.91603 },
    { pos: new mp.Vector3(-1820.916, 3292.191, 32.821), r: 328.7567 },
    { pos: new mp.Vector3(-1003.911, 386.0345, 72.242), r: 144.5904 },
    { pos: new mp.Vector3(-938.2465, -1000.07, 2.3879), r: 131.3769 },
    { pos: new mp.Vector3(-1833.472, 3299.218, 32.820), r: 334.2501 },
    { pos: new mp.Vector3(322.29635, -2017.36, 21.213), r: 310.0247 },
    { pos: new mp.Vector3(1518.8934, 3635.443, 34.747), r: 184.1965 },
    { pos: new mp.Vector3(-2032.321, -462.985, 11.434), r: 355.2937 },
    { pos: new mp.Vector3(335.73303, -2001.33, 22.995), r: 324.0379 },
    { pos: new mp.Vector3(2486.6318, -609.452, 74.683), r: 106.1446 },
    { pos: new mp.Vector3(-934.3551, -985.450, 2.1503), r: 159.3880 },
    { pos: new mp.Vector3(340.45669, -2003.36, 22.862), r: 282.7284 },
    { pos: new mp.Vector3(1523.2281, 3627.079, 34.751), r: 117.8731 },
    { pos: new mp.Vector3(-1874.527, 3229.740, 32.846), r: 155.5241 },
    { pos: new mp.Vector3(-1695.426, -144.199, 58.093), r: 121.3712 },
    { pos: new mp.Vector3(-1061.304, 421.4448, 72.980), r: 8.471017 },
    { pos: new mp.Vector3(1508.9453, 3618.920, 34.769), r: 76.76232 },
    { pos: new mp.Vector3(358.32034, -2017.16, 22.346), r: 242.9810 },
    { pos: new mp.Vector3(2493.0253, -549.037, 68.799), r: 344.8525 },
    { pos: new mp.Vector3(365.39321, -2023.53, 22.237), r: 242.5022 },
    { pos: new mp.Vector3(-1709.989, -183.477, 57.861), r: 286.0157 },
    { pos: new mp.Vector3(2468.0258, -522.019, 69.470), r: 48.90260 },
    { pos: new mp.Vector3(-1861.315, 3221.494, 32.846), r: 217.0591 },
    { pos: new mp.Vector3(-1584.714, 459.1536, 108.98), r: 348.6595 },
    { pos: new mp.Vector3(-943.0893, -972.056, 2.1501), r: 32.20498 },
    { pos: new mp.Vector3(1503.9246, 3629.625, 34.830), r: 250.0786 },
    { pos: new mp.Vector3(-1090.236, 463.3955, 78.540), r: 152.1959 },
    { pos: new mp.Vector3(2443.8120, -498.392, 70.457), r: 19.20862 },
    { pos: new mp.Vector3(-1970.926, -498.687, 11.859), r: 318.2750 },
    { pos: new mp.Vector3(385.37051, -2012.34, 23.470), r: 290.3881 },
    { pos: new mp.Vector3(-1834.235, 3206.477, 32.801), r: 142.7291 },
    { pos: new mp.Vector3(-1732.516, -184.663, 58.476), r: 69.31193 },
    { pos: new mp.Vector3(2418.7512, -479.962, 71.870), r: 55.87875 },
    { pos: new mp.Vector3(-953.4313, -970.337, 2.1500), r: 327.9206 },
    { pos: new mp.Vector3(1478.8367, 3621.687, 34.711), r: 128.3807 },
    { pos: new mp.Vector3(2375.5202, -455.030, 72.786), r: 67.92218 },
    { pos: new mp.Vector3(-1961.683, -506.769, 11.869), r: 321.0222 },
    { pos: new mp.Vector3(-1818.245, 3196.079, 32.800), r: 141.6277 },
    { pos: new mp.Vector3(1484.1965, 3605.719, 34.861), r: 52.13194 },
    { pos: new mp.Vector3(-365.1849, 6129.626, 32.041), r: 51.63465 },
    { pos: new mp.Vector3(1463.4252, 3616.347, 34.823), r: 306.4716 },
    { pos: new mp.Vector3(-1790.863, 3181.207, 32.828), r: 240.3638 },
    { pos: new mp.Vector3(-956.4747, -963.980, 2.1500), r: 306.2720 },
    { pos: new mp.Vector3(1436.9725, 3691.090, 33.742), r: 19.37082 },
    { pos: new mp.Vector3(-1957.234, -513.024, 11.903), r: 255.6194 },
    { pos: new mp.Vector3(2348.0349, -470.621, 87.188), r: 70.36653 },
    { pos: new mp.Vector3(-1778.772, 3172.798, 32.828), r: 167.6603 },
    { pos: new mp.Vector3(1450.0004, 3697.265, 33.826), r: 17.13482 },
    { pos: new mp.Vector3(-1715.677, -218.040, 57.614), r: 351.7418 },
    { pos: new mp.Vector3(-1052.313, 473.6403, 80.869), r: 221.5213 },
    { pos: new mp.Vector3(2336.9514, -478.730, 87.575), r: 174.6652 },
    { pos: new mp.Vector3(-953.4404, -958.643, 2.1453), r: 351.1997 },
    { pos: new mp.Vector3(-1945.820, -520.079, 11.858), r: 320.5797 },
    { pos: new mp.Vector3(-1709.164, -228.594, 56.250), r: 136.6043 },
    { pos: new mp.Vector3(2330.9045, -489.613, 90.132), r: 128.7409 },
    { pos: new mp.Vector3(2312.0568, -499.346, 92.506), r: 109.8869 },
    { pos: new mp.Vector3(-1700.848, -242.186, 54.457), r: 262.7587 },
    { pos: new mp.Vector3(1509.8541, 3742.052, 34.416), r: 174.9154 },
    { pos: new mp.Vector3(-966.3234, -948.172, 2.1858), r: 320.0403 },
    { pos: new mp.Vector3(-1939.741, -525.832, 11.857), r: 290.3387 },
    { pos: new mp.Vector3(-1805.749, 3153.195, 32.864), r: 185.1420 },
    { pos: new mp.Vector3(327.35839, -1943.03, 24.767), r: 208.5985 },
    { pos: new mp.Vector3(-1688.102, -225.941, 56.663), r: 338.2237 },
    { pos: new mp.Vector3(-1866.886, 3187.164, 32.878), r: 136.7193 },
    { pos: new mp.Vector3(2235.7783, -527.276, 91.212), r: 148.0964 },
    { pos: new mp.Vector3(331.97137, -1939.75, 24.757), r: 322.0059 },
    { pos: new mp.Vector3(-960.8910, -945.045, 2.1501), r: 118.3087 },
    { pos: new mp.Vector3(-1928.050, -534.255, 11.824), r: 346.9147 },
    { pos: new mp.Vector3(-1670.455, -204.954, 56.671), r: 323.8804 },
    { pos: new mp.Vector3(2207.6843, -547.550, 93.685), r: 123.1025 },
    { pos: new mp.Vector3(2182.3256, -563.625, 94.978), r: 124.8576 },
    { pos: new mp.Vector3(-1915.175, 3207.395, 32.810), r: 254.7727 },
    { pos: new mp.Vector3(319.47375, -1917.93, 25.505), r: 355.2343 },
    { pos: new mp.Vector3(-1004.878, 493.0663, 79.335), r: 182.1200 },
    { pos: new mp.Vector3(1555.6076, 3771.562, 34.367), r: 157.4781 },
    { pos: new mp.Vector3(2155.7878, -582.389, 96.519), r: 124.3407 },
    { pos: new mp.Vector3(-1653.570, -188.492, 55.724), r: 266.7275 },
    { pos: new mp.Vector3(2117.9094, -616.200, 98.150), r: 133.3116 },
    { pos: new mp.Vector3(-973.1452, -940.076, 2.2077), r: 209.7405 },
    { pos: new mp.Vector3(-1464.047, 533.8464, 125.44), r: 172.9221 },
    { pos: new mp.Vector3(-1953.505, 3143.911, 32.748), r: 147.3073 },
    { pos: new mp.Vector3(-1641.437, -193.902, 55.273), r: 216.5835 },
    { pos: new mp.Vector3(-1916.977, -543.157, 11.810), r: 321.7185 },
    { pos: new mp.Vector3(2062.3806, -659.225, 96.653), r: 131.3188 },
    { pos: new mp.Vector3(1590.4259, 3798.712, 34.722), r: 277.7551 },
    { pos: new mp.Vector3(2034.0133, -683.411, 94.443), r: 130.2139 },
    { pos: new mp.Vector3(284.78768, -1888.05, 26.948), r: 42.56809 },
    { pos: new mp.Vector3(1608.9827, 3816.187, 34.793), r: 310.5567 },
    { pos: new mp.Vector3(1628.8034, 3833.212, 34.856), r: 310.6583 },
    { pos: new mp.Vector3(2005.9720, -705.771, 92.869), r: 122.3111 },
    { pos: new mp.Vector3(-378.3347, 6120.421, 31.479), r: 44.64043 },
    { pos: new mp.Vector3(-967.7897, -933.434, 2.1501), r: 23.66193 },
    { pos: new mp.Vector3(-1908.788, -548.902, 11.769), r: 278.4343 },
    { pos: new mp.Vector3(1649.0343, 3852.017, 34.786), r: 314.8196 },
    { pos: new mp.Vector3(1987.9752, -722.412, 92.482), r: 123.1201 },
    { pos: new mp.Vector3(-951.0902, 452.6588, 80.393), r: 108.8471 },
    { pos: new mp.Vector3(1668.4401, 3870.975, 35.228), r: 314.2904 },
    { pos: new mp.Vector3(1691.0782, 3892.600, 34.811), r: 313.6631 },
    { pos: new mp.Vector3(2015.4901, -750.143, 98.779), r: 162.6817 },
    { pos: new mp.Vector3(-1890.146, -564.465, 11.695), r: 324.3031 },
    { pos: new mp.Vector3(-381.5226, 6116.787, 31.479), r: 42.77461 },
    { pos: new mp.Vector3(1985.8289, -797.315, 103.87), r: 148.0020 },
    { pos: new mp.Vector3(-982.8760, -907.386, 2.1504), r: 73.66979 },
    { pos: new mp.Vector3(1720.5017, 3916.877, 34.751), r: 301.2295 },
    { pos: new mp.Vector3(1892.8934, -883.790, 118.69), r: 134.0115 },
    { pos: new mp.Vector3(1752.5864, 3932.732, 35.091), r: 297.9417 },
    { pos: new mp.Vector3(152.91990, -1780.85, 29.073), r: 292.6312 },
    { pos: new mp.Vector3(1777.1738, 3941.195, 34.378), r: 288.3946 },
    { pos: new mp.Vector3(-1883.022, -570.925, 11.659), r: 345.9733 },
    { pos: new mp.Vector3(-2110.997, 3297.590, 32.810), r: 150.3873 },
    { pos: new mp.Vector3(1821.3431, 3950.280, 34.546), r: 287.4718 },
    { pos: new mp.Vector3(-859.4182, 456.8263, 87.541), r: 289.2204 },
    { pos: new mp.Vector3(1840.1406, -854.809, 94.685), r: 48.29142 },
    { pos: new mp.Vector3(1845.6468, 3951.589, 33.055), r: 275.1749 },
    { pos: new mp.Vector3(-1000.685, -914.068, 2.1858), r: 33.56301 },
    { pos: new mp.Vector3(133.11398, -1767.85, 29.283), r: 20.11111 },
    { pos: new mp.Vector3(1758.7081, -839.359, 72.293), r: 86.92404 },
    { pos: new mp.Vector3(1879.1755, 3954.751, 33.007), r: 277.4584 },
    { pos: new mp.Vector3(-274.3175, 6072.664, 31.450), r: 180.9191 },
    { pos: new mp.Vector3(1907.9979, 3952.072, 33.149), r: 249.6757 },
    { pos: new mp.Vector3(1695.7543, -884.229, 68.991), r: 122.2304 },
    { pos: new mp.Vector3(-1010.877, -920.116, 2.1858), r: 48.21660 },
    { pos: new mp.Vector3(1936.7227, 3937.310, 32.933), r: 241.0005 },
    { pos: new mp.Vector3(-874.6785, 512.0127, 91.576), r: 259.7839 },
    { pos: new mp.Vector3(121.53766, -1758.31, 29.406), r: 296.3357 },
    { pos: new mp.Vector3(1675.5134, -895.709, 68.133), r: 141.9002 },
    { pos: new mp.Vector3(-1830.286, -520.225, 29.052), r: 345.1064 },
    { pos: new mp.Vector3(1952.5161, 3921.551, 32.435), r: 214.9891 },
    { pos: new mp.Vector3(1968.3288, 3896.834, 32.212), r: 210.4040 },
    { pos: new mp.Vector3(1655.5219, -909.086, 66.794), r: 121.0488 },
    { pos: new mp.Vector3(1982.5561, 3898.926, 33.168), r: 251.0545 },
    { pos: new mp.Vector3(2000.0827, 3907.503, 35.822), r: 114.0023 },
    { pos: new mp.Vector3(1369.1662, -1070.52, 52.798), r: 115.5149 },
    { pos: new mp.Vector3(1377.5604, -1061.63, 52.701), r: 292.1213 },
    { pos: new mp.Vector3(-1734.427, -572.322, 37.448), r: 349.7412 },
    { pos: new mp.Vector3(1400.3571, -1048.33, 52.820), r: 301.0062 },
    { pos: new mp.Vector3(-252.9156, -1840.92, 28.543), r: 249.5840 },
    { pos: new mp.Vector3(1419.2956, -1038.03, 53.180), r: 296.5051 },
    { pos: new mp.Vector3(-1685.687, -581.082, 34.237), r: 322.0129 },
    { pos: new mp.Vector3(-919.3390, 559.5774, 99.641), r: 282.2233 },
    { pos: new mp.Vector3(-1637.119, 228.9405, 60.733), r: 273.3349 },
    { pos: new mp.Vector3(-235.6883, -1877.92, 27.955), r: 187.3201 },
    { pos: new mp.Vector3(-1619.450, -574.331, 33.539), r: 60.37381 },
    { pos: new mp.Vector3(1457.0085, -1021.01, 54.750), r: 294.3746 },
    { pos: new mp.Vector3(-239.0887, -1884.17, 27.755), r: 114.4416 },
    { pos: new mp.Vector3(-2308.110, 3391.117, 30.985), r: 38.35386 },
    { pos: new mp.Vector3(-1688.109, 200.4137, 63.666), r: 296.0963 },
    { pos: new mp.Vector3(-248.4602, -1894.68, 27.755), r: 77.15055 },
    { pos: new mp.Vector3(-1566.773, -669.543, 28.920), r: 314.6570 },
    { pos: new mp.Vector3(-941.8356, 571.4477, 101.24), r: 316.8662 },
    { pos: new mp.Vector3(1981.7159, 3885.821, 32.398), r: 102.1634 },
    { pos: new mp.Vector3(-1725.514, 25.48687, 66.797), r: 42.21690 },
    { pos: new mp.Vector3(-257.7364, -1905.80, 27.755), r: 129.4037 },
    { pos: new mp.Vector3(-1557.554, -694.448, 29.548), r: 27.96691 },
    { pos: new mp.Vector3(2001.2817, 3897.737, 35.846), r: 300.0444 },
    { pos: new mp.Vector3(2030.0747, 3914.425, 33.518), r: 298.8984 },
    { pos: new mp.Vector3(-260.4539, -1920.64, 27.970), r: 94.98508 },
    { pos: new mp.Vector3(-969.0421, 580.2444, 101.82), r: 318.1924 },
    { pos: new mp.Vector3(2055.5771, 3928.833, 32.990), r: 305.5660 },
    { pos: new mp.Vector3(-1480.338, -757.155, 24.736), r: 292.3534 },
    { pos: new mp.Vector3(-69.02714, -814.238, 326.17), r: 109.4556 },
    { pos: new mp.Vector3(2049.1032, 3938.316, 32.602), r: 29.25272 },
    { pos: new mp.Vector3(-240.3452, -1920.01, 27.755), r: 270.2276 },
    { pos: new mp.Vector3(-78.00357, -813.373, 326.24), r: 115.9192 },
    { pos: new mp.Vector3(-81.57307, -822.389, 326.17), r: 200.3871 },
    { pos: new mp.Vector3(2023.8111, 3919.877, 33.709), r: 306.1685 },
    { pos: new mp.Vector3(-76.32137, -827.186, 326.09), r: 267.9306 },
    { pos: new mp.Vector3(-220.7446, -1931.87, 27.755), r: 308.9858 },
    { pos: new mp.Vector3(-1466.921, -776.800, 23.839), r: 266.2884 },
    { pos: new mp.Vector3(-68.14672, -822.736, 326.09), r: 340.7159 },
    { pos: new mp.Vector3(2056.4550, 3938.980, 34.120), r: 69.40603 },
    { pos: new mp.Vector3(-1123.227, 555.6320, 102.51), r: 11.19320 },
    { pos: new mp.Vector3(2057.1662, 3937.310, 34.031), r: 198.6619 },
    { pos: new mp.Vector3(-1446.495, -779.039, 23.708), r: 352.5711 },
    { pos: new mp.Vector3(-2298.899, 3384.315, 31.065), r: 227.5018 },
    { pos: new mp.Vector3(-220.2761, -1910.03, 27.755), r: 357.6374 },
    { pos: new mp.Vector3(-1726.573, 47.46314, 67.224), r: 336.3947 },
    { pos: new mp.Vector3(-62.38391, -823.517, 322.41), r: 246.6071 },
    { pos: new mp.Vector3(2057.8413, 3935.610, 33.921), r: 200.7047 },
    { pos: new mp.Vector3(-1022.281, -919.510, 2.2288), r: 213.5166 },
    { pos: new mp.Vector3(-572.1846, 952.9676, 242.65), r: 32.54280 },
    { pos: new mp.Vector3(-1741.244, 72.06884, 67.692), r: 62.98574 },
    { pos: new mp.Vector3(-65.91226, -828.790, 322.28), r: 150.6281 },
    { pos: new mp.Vector3(-215.5003, -1904.53, 27.755), r: 165.9303 },
    { pos: new mp.Vector3(1990.3232, 3860.825, 33.941), r: 171.2889 },
    { pos: new mp.Vector3(-1759.473, 83.73716, 68.603), r: 150.4330 },
    { pos: new mp.Vector3(2004.7729, 3832.569, 32.518), r: 211.4479 },
    { pos: new mp.Vector3(-1023.226, -927.623, 2.1463), r: 31.59496 },
    { pos: new mp.Vector3(-73.00292, -832.303, 322.41), r: 258.3066 },
    { pos: new mp.Vector3(-2259.470, 3355.372, 33.254), r: 284.4062 },
    { pos: new mp.Vector3(-1199.688, 556.6676, 100.17), r: 178.9155 },
    { pos: new mp.Vector3(-1413.618, -748.942, 23.337), r: 128.6563 },
    { pos: new mp.Vector3(-142.1873, 6195.930, 31.215), r: 38.80593 },
    { pos: new mp.Vector3(-237.4658, 702.9473, 207.55), r: 34.33523 },
    { pos: new mp.Vector3(-1028.460, -930.026, 2.1483), r: 38.74847 },
    { pos: new mp.Vector3(2024.4770, 3829.868, 33.572), r: 301.0904 },
    { pos: new mp.Vector3(2036.1074, 3836.146, 35.991), r: 299.1167 },
    { pos: new mp.Vector3(-85.53384, -827.679, 322.43), r: 357.7593 },
    { pos: new mp.Vector3(-133.6670, 6204.081, 31.209), r: 47.70645 },
    { pos: new mp.Vector3(2063.2880, 3851.121, 33.661), r: 300.6278 },
    { pos: new mp.Vector3(-1392.836, -755.399, 24.222), r: 129.8375 },
    { pos: new mp.Vector3(2092.3081, 3867.529, 33.231), r: 302.3349 },
    { pos: new mp.Vector3(-220.8480, 609.2500, 190.78), r: 142.5221 },
    { pos: new mp.Vector3(-1261.753, 500.4089, 96.825), r: 92.80720 },
    { pos: new mp.Vector3(2122.9372, 3885.062, 33.156), r: 299.3957 },
    { pos: new mp.Vector3(-2344.775, 3256.780, 32.959), r: 345.3813 },
    { pos: new mp.Vector3(-1040.238, -925.260, 2.4165), r: 208.2942 },
    { pos: new mp.Vector3(-89.10341, -821.115, 322.34), r: 312.7076 },
    { pos: new mp.Vector3(2141.2033, 3895.686, 33.147), r: 302.5698 },
    { pos: new mp.Vector3(-1390.255, -774.003, 20.264), r: 144.7286 },
    { pos: new mp.Vector3(2148.5373, 3895.498, 33.167), r: 191.9807 },
    { pos: new mp.Vector3(-197.2018, -1919.58, 27.755), r: 236.3736 },
    { pos: new mp.Vector3(-88.06896, -814.376, 322.41), r: 209.8620 },
    { pos: new mp.Vector3(2148.9479, 3894.012, 33.198), r: 152.8037 },
    { pos: new mp.Vector3(-2328.282, 3244.019, 33.057), r: 334.3006 },
    { pos: new mp.Vector3(-1046.301, -940.251, 2.1859), r: 47.05662 },
    { pos: new mp.Vector3(2147.6562, 3896.562, 33.187), r: 74.80789 },
    { pos: new mp.Vector3(-84.17799, -808.616, 322.38), r: 22.43528 },
    { pos: new mp.Vector3(-1239.685, 495.2474, 93.863), r: 143.4400 },
    { pos: new mp.Vector3(-2326.003, 3242.357, 33.020), r: 304.3410 },
    { pos: new mp.Vector3(2145.6218, 3889.512, 33.160), r: 139.9474 },
    { pos: new mp.Vector3(-217.4747, -1951.50, 27.750), r: 252.9928 },
    { pos: new mp.Vector3(2125.8752, 3877.827, 32.987), r: 117.2124 },
    { pos: new mp.Vector3(-77.66815, -804.567, 322.34), r: 349.3358 },
    { pos: new mp.Vector3(2103.0651, 3865.037, 33.039), r: 120.1015 },
    { pos: new mp.Vector3(-1052.668, -943.822, 2.1859), r: 23.96970 },
    { pos: new mp.Vector3(-213.0513, -1970.14, 27.755), r: 251.1738 },
    { pos: new mp.Vector3(2080.3796, 3851.854, 33.239), r: 120.1774 },
    { pos: new mp.Vector3(2064.6584, 3843.041, 33.648), r: 118.9172 },
    { pos: new mp.Vector3(-70.52319, -805.842, 322.39), r: 19.78855 },
    { pos: new mp.Vector3(-2309.562, 3232.725, 32.977), r: 104.8053 },
    { pos: new mp.Vector3(-1357.901, -796.650, 19.345), r: 136.3463 },
    { pos: new mp.Vector3(-217.2545, -1971.61, 27.755), r: 132.7021 },
    { pos: new mp.Vector3(-1663.029, 87.86538, 64.096), r: 258.8104 },
    { pos: new mp.Vector3(2045.4199, 3831.934, 35.731), r: 120.3755 },
    { pos: new mp.Vector3(-95.33235, 6303.855, 31.455), r: 320.1290 },
    { pos: new mp.Vector3(-223.9618, -1973.43, 27.755), r: 357.2520 },
    { pos: new mp.Vector3(-2258.343, 3207.020, 32.809), r: 292.0661 },
    { pos: new mp.Vector3(-63.73738, -809.370, 322.32), r: 131.8516 },
    { pos: new mp.Vector3(2029.5950, 3821.789, 33.638), r: 117.2712 },
    { pos: new mp.Vector3(-1332.376, -811.663, 17.245), r: 95.67663 },
    { pos: new mp.Vector3(-2246.116, 3229.704, 32.810), r: 239.5143 },
    { pos: new mp.Vector3(-61.30928, -816.347, 322.36), r: 253.2362 },
    { pos: new mp.Vector3(-1062.681, -944.685, 2.1735), r: 261.7161 },
    { pos: new mp.Vector3(2022.3551, 3804.506, 33.068), r: 125.6729 },
    { pos: new mp.Vector3(-108.6543, 6307.958, 31.467), r: 229.3422 },
    { pos: new mp.Vector3(-230.2279, -1974.73, 29.946), r: 346.6169 },
    { pos: new mp.Vector3(2034.9960, 3780.842, 32.126), r: 205.4264 },
    { pos: new mp.Vector3(-2231.969, 3254.103, 32.810), r: 239.3711 },
    { pos: new mp.Vector3(2048.7387, 3756.952, 32.271), r: 208.3461 },
    { pos: new mp.Vector3(-228.8974, -1958.90, 27.764), r: 351.5585 },
    { pos: new mp.Vector3(-61.77000, -823.608, 322.35), r: 238.7218 },
    { pos: new mp.Vector3(-1305.236, -801.766, 17.572), r: 313.8521 },
    { pos: new mp.Vector3(2062.2426, 3734.307, 33.008), r: 203.9783 },
    { pos: new mp.Vector3(-103.8717, 6314.040, 31.490), r: 142.9103 },
    { pos: new mp.Vector3(-1456.323, 51.08134, 52.684), r: 279.8976 },
    { pos: new mp.Vector3(-66.35002, -829.273, 322.40), r: 211.7983 },
    { pos: new mp.Vector3(-1449.697, 47.07121, 52.576), r: 200.7462 },
    { pos: new mp.Vector3(-249.8678, 106.3322, 69.474), r: 67.16915 },
    { pos: new mp.Vector3(-1070.029, -955.371, 2.3945), r: 263.9379 },
    { pos: new mp.Vector3(-2185.980, 3253.529, 32.796), r: 156.6589 },
    { pos: new mp.Vector3(-1300.257, -778.655, 19.469), r: 127.7313 },
    { pos: new mp.Vector3(-72.67677, -832.753, 322.39), r: 284.1719 },
    { pos: new mp.Vector3(-116.7949, 6322.049, 31.490), r: 317.0397 },
    { pos: new mp.Vector3(-2113.848, 3215.963, 32.810), r: 195.7334 },
    { pos: new mp.Vector3(-1315.003, -763.469, 20.360), r: 154.4797 },
    { pos: new mp.Vector3(2062.2519, 3734.308, 33.010), r: 224.6214 },
    { pos: new mp.Vector3(-247.6397, -1932.33, 28.185), r: 12.53969 },
    { pos: new mp.Vector3(-85.59947, -828.036, 322.37), r: 118.0169 },
    { pos: new mp.Vector3(-243.9516, -1925.29, 27.755), r: 351.4970 },
    { pos: new mp.Vector3(2074.9272, 3735.133, 32.985), r: 300.1489 },
    { pos: new mp.Vector3(-2096.041, 3244.139, 33.715), r: 184.8879 },
    { pos: new mp.Vector3(-79.96006, -831.822, 322.40), r: 191.0267 },
    { pos: new mp.Vector3(-1327.236, -765.269, 20.404), r: 324.9584 },
    { pos: new mp.Vector3(2112.9431, 3756.786, 32.968), r: 300.9026 },
    { pos: new mp.Vector3(-96.53655, 6331.683, 31.490), r: 48.75013 },
    { pos: new mp.Vector3(-255.2514, -1915.42, 27.755), r: 114.8683 },
    { pos: new mp.Vector3(2146.5644, 3776.243, 33.383), r: 299.6335 },
    { pos: new mp.Vector3(-2061.473, 3251.564, 32.810), r: 190.8417 },
    { pos: new mp.Vector3(-261.3122, -1911.86, 27.755), r: 15.29913 },
    { pos: new mp.Vector3(2181.9921, 3796.234, 33.511), r: 300.9439 },
    { pos: new mp.Vector3(-1335.008, -744.556, 22.391), r: 130.5687 },
    { pos: new mp.Vector3(2213.2526, 3814.647, 33.622), r: 299.0944 },
    { pos: new mp.Vector3(-2068.530, 3238.393, 32.810), r: 287.3818 },
    { pos: new mp.Vector3(-263.5440, -1925.23, 29.946), r: 340.6882 },
    { pos: new mp.Vector3(2247.1835, 3833.985, 34.144), r: 300.5855 },
    { pos: new mp.Vector3(-80.80093, 6338.382, 31.590), r: 226.5099 },
    { pos: new mp.Vector3(2283.0920, 3854.792, 34.523), r: 301.6140 },
    { pos: new mp.Vector3(2312.7829, 3872.135, 34.829), r: 304.6226 },
    { pos: new mp.Vector3(-1352.874, -732.132, 22.788), r: 257.8814 },
    { pos: new mp.Vector3(-260.5342, -1932.16, 29.946), r: 322.5550 },
    { pos: new mp.Vector3(2344.5854, 3893.938, 35.289), r: 305.8568 },
    { pos: new mp.Vector3(-2025.238, 3236.206, 32.810), r: 42.37913 },
    { pos: new mp.Vector3(2372.9328, 3916.230, 35.612), r: 317.4641 },
    { pos: new mp.Vector3(2397.2585, 3940.829, 35.983), r: 317.0967 },
    { pos: new mp.Vector3(-265.7728, -1927.73, 29.946), r: 326.6235 },
    { pos: new mp.Vector3(-62.66351, 6335.421, 31.299), r: 222.4032 },
    { pos: new mp.Vector3(2424.3222, 3974.389, 36.594), r: 323.6720 },
    { pos: new mp.Vector3(-2066.952, -423.508, 10.636), r: 16.99180 },
    { pos: new mp.Vector3(-2013.285, 3276.671, 32.809), r: 103.5656 },
    { pos: new mp.Vector3(-1386.416, -700.994, 24.483), r: 215.8578 },
    { pos: new mp.Vector3(-921.9290, -844.507, 15.471), r: 62.09852 },
    { pos: new mp.Vector3(2449.6174, 4014.315, 37.361), r: 333.0084 },
    { pos: new mp.Vector3(-271.5111, -1922.86, 29.946), r: 325.0925 },
    { pos: new mp.Vector3(-2026.751, 3285.304, 32.809), r: 149.1587 },
    { pos: new mp.Vector3(2468.1215, 4052.669, 37.514), r: 336.0272 },
    { pos: new mp.Vector3(-2039.282, 3292.540, 32.810), r: 149.9886 },
    { pos: new mp.Vector3(-284.5758, -1910.47, 29.946), r: 294.1098 },
    { pos: new mp.Vector3(2490.7207, 4105.436, 37.997), r: 336.8985 },
    { pos: new mp.Vector3(-1320.574, -673.998, 26.514), r: 354.5875 },
    { pos: new mp.Vector3(-2056.370, 3303.848, 32.810), r: 148.8373 },
    { pos: new mp.Vector3(2502.1586, 4129.093, 38.408), r: 336.4451 },
    { pos: new mp.Vector3(-294.3876, -1903.06, 29.946), r: 323.3428 },
    { pos: new mp.Vector3(-1313.311, -643.005, 26.469), r: 153.4131 },
    { pos: new mp.Vector3(-2083.251, 3321.616, 32.946), r: 150.6768 },
    { pos: new mp.Vector3(2509.5131, 4115.229, 38.414), r: 150.0690 },
    { pos: new mp.Vector3(-299.8676, -1899.06, 29.946), r: 322.6085 },
    { pos: new mp.Vector3(421.97564, -972.211, 32.387), r: 76.76767 },
    { pos: new mp.Vector3(-2117.950, 3339.107, 32.810), r: 147.9152 },
    { pos: new mp.Vector3(2492.9550, 4080.353, 37.898), r: 155.9200 },
    { pos: new mp.Vector3(-1257.751, -722.213, 22.125), r: 155.7937 },
    { pos: new mp.Vector3(418.97650, -968.631, 29.391), r: 23.11471 },
    { pos: new mp.Vector3(-304.6153, -1894.51, 29.946), r: 321.6537 },
    { pos: new mp.Vector3(2477.0822, 4043.772, 37.612), r: 155.7498 },
    { pos: new mp.Vector3(-2148.016, 3351.880, 32.775), r: 142.3356 },
    { pos: new mp.Vector3(-30.18311, 6322.554, 33.794), r: 45.45384 },
    { pos: new mp.Vector3(413.12310, -964.771, 29.477), r: 84.19111 },
    { pos: new mp.Vector3(2450.9311, 3991.803, 36.914), r: 148.2613 },
    { pos: new mp.Vector3(-310.8171, -1890.53, 29.946), r: 329.3143 },
    { pos: new mp.Vector3(2423.2600, 3952.100, 36.304), r: 137.1143 },
    { pos: new mp.Vector3(-2200.093, 3427.819, 32.392), r: 60.86817 },
    { pos: new mp.Vector3(412.22943, -616.875, 28.710), r: 105.0920 },
    { pos: new mp.Vector3(-317.6423, -1886.95, 29.946), r: 329.7468 },
    { pos: new mp.Vector3(408.81127, -617.546, 28.798), r: 94.39681 },
    { pos: new mp.Vector3(2393.0849, 3918.752, 35.773), r: 128.7103 },
    { pos: new mp.Vector3(-2174.684, 3488.690, 31.775), r: 204.5632 },
    { pos: new mp.Vector3(407.10910, -610.494, 28.705), r: 324.8247 },
    { pos: new mp.Vector3(2355.7282, 3886.987, 35.319), r: 127.5846 },
    { pos: new mp.Vector3(-325.0234, -1884.00, 29.946), r: 334.2175 },
    { pos: new mp.Vector3(-1581.649, -537.437, 35.597), r: 44.57978 },
    { pos: new mp.Vector3(1042.0980, -176.165, 70.060), r: 138.0057 },
    { pos: new mp.Vector3(-2089.278, 3446.983, 30.923), r: 241.3046 },
    { pos: new mp.Vector3(2328.3745, 3868.023, 34.960), r: 122.3847 },
    { pos: new mp.Vector3(-579.8160, -20.6703, 44.681), r: 292.8976 },
    { pos: new mp.Vector3(-331.7784, -1882.12, 29.946), r: 353.2425 },
    { pos: new mp.Vector3(456.83770, -596.610, 28.499), r: 83.18920 },
    { pos: new mp.Vector3(2293.2155, 3846.544, 34.606), r: 119.8705 },
    { pos: new mp.Vector3(-2068.313, 3420.082, 31.364), r: 345.1841 },
    { pos: new mp.Vector3(2259.9929, 3827.630, 34.195), r: 119.5789 },
    { pos: new mp.Vector3(454.40335, -605.435, 28.553), r: 177.8137 },
    { pos: new mp.Vector3(-1555.680, -520.490, 35.714), r: 85.59841 },
    { pos: new mp.Vector3(-339.4797, -1880.69, 29.946), r: 355.5864 },
    { pos: new mp.Vector3(2220.9670, 3805.254, 33.783), r: 120.1025 },
    { pos: new mp.Vector3(-2046.801, 3341.706, 32.742), r: 59.43667 },
    { pos: new mp.Vector3(453.15332, -623.925, 28.540), r: 175.6735 },
    { pos: new mp.Vector3(-346.2070, -1880.10, 29.946), r: 336.6138 },
    { pos: new mp.Vector3(2184.5534, 3784.564, 34.146), r: 119.9758 },
    { pos: new mp.Vector3(-2153.008, 3399.499, 33.475), r: 40.84550 },
    { pos: new mp.Vector3(451.01876, -645.114, 28.470), r: 174.6871 },
    { pos: new mp.Vector3(41.453346, 6457.500, 31.425), r: 224.4215 },
    { pos: new mp.Vector3(2141.8591, 3759.467, 33.155), r: 120.4195 },
    { pos: new mp.Vector3(-353.0376, -1880.10, 29.946), r: 272.1895 },
    { pos: new mp.Vector3(450.71792, -655.024, 28.376), r: 179.7321 },
    { pos: new mp.Vector3(2095.4912, 3732.737, 33.009), r: 119.5715 },
    { pos: new mp.Vector3(-2314.926, 3448.342, 31.322), r: 179.3005 },
    { pos: new mp.Vector3(-360.7115, -1881.40, 29.946), r: 9.947345 },
    { pos: new mp.Vector3(2055.3479, 3709.972, 33.028), r: 118.9563 },
    { pos: new mp.Vector3(34.749317, 6450.855, 31.425), r: 216.3259 },
    { pos: new mp.Vector3(-1535.062, -551.161, 33.833), r: 173.2784 },
    { pos: new mp.Vector3(-367.8474, -1883.16, 29.946), r: 27.29255 },
    { pos: new mp.Vector3(2019.6578, 3689.602, 33.124), r: 120.1020 },
    { pos: new mp.Vector3(-2314.322, 3436.189, 31.483), r: 4.484783 },
    { pos: new mp.Vector3(-886.7202, -797.237, 15.913), r: 154.2380 },
    { pos: new mp.Vector3(-374.8095, -1885.39, 29.946), r: 18.46025 },
    { pos: new mp.Vector3(-2305.035, 3436.661, 31.458), r: 21.30879 },
    { pos: new mp.Vector3(-1087.560, 684.6724, 142.99), r: 278.9919 },
    { pos: new mp.Vector3(1977.0035, 3664.480, 33.296), r: 123.4446 },
    { pos: new mp.Vector3(-381.8439, -1889.02, 29.946), r: 34.49432 },
    { pos: new mp.Vector3(-889.1517, -808.750, 15.920), r: 299.9293 },
    { pos: new mp.Vector3(1937.5363, 3641.572, 33.456), r: 120.6124 },
    { pos: new mp.Vector3(-1544.912, -584.079, 33.911), r: 336.0477 },
    { pos: new mp.Vector3(-1078.011, 678.5984, 142.93), r: 243.3621 },
    { pos: new mp.Vector3(-1314.257, 1012.824, 263.35), r: 152.6044 },
    { pos: new mp.Vector3(1897.1442, 3618.449, 34.129), r: 120.3671 },
    { pos: new mp.Vector3(-388.3503, -1893.04, 29.946), r: 36.95034 },
    { pos: new mp.Vector3(-2349.144, 3439.300, 29.741), r: 160.7931 },
    { pos: new mp.Vector3(-1287.437, 1086.540, 280.11), r: 340.7535 },
    { pos: new mp.Vector3(1860.4801, 3597.017, 34.694), r: 119.8929 },
    { pos: new mp.Vector3(73.392333, 6473.859, 31.260), r: 220.1620 },
    { pos: new mp.Vector3(-1054.212, 660.7673, 141.99), r: 233.5156 },
    { pos: new mp.Vector3(-393.9700, -1897.47, 29.946), r: 45.63648 },
    { pos: new mp.Vector3(1821.9475, 3574.827, 35.702), r: 121.6428 },
    { pos: new mp.Vector3(-1012.673, 1304.533, 297.76), r: 308.8004 },
    { pos: new mp.Vector3(504.23974, -352.065, 43.528), r: 325.6326 },
    { pos: new mp.Vector3(-2362.374, 3417.998, 30.639), r: 267.3303 },
    { pos: new mp.Vector3(-904.0579, -797.935, 15.910), r: 218.0485 },
    { pos: new mp.Vector3(1779.1904, 3549.600, 35.790), r: 120.7340 },
    { pos: new mp.Vector3(-713.3086, 1293.681, 298.72), r: 259.3282 },
    { pos: new mp.Vector3(-404.4719, -1908.19, 29.946), r: 47.75117 },
    { pos: new mp.Vector3(-1027.491, 647.7622, 141.45), r: 250.7514 },
    { pos: new mp.Vector3(1743.7210, 3529.056, 36.016), r: 120.8852 },
    { pos: new mp.Vector3(-2367.247, 3414.007, 32.056), r: 29.77607 },
    { pos: new mp.Vector3(-1582.605, -596.002, 34.978), r: 157.5895 },
    { pos: new mp.Vector3(-636.6538, 1342.526, 292.11), r: 316.5140 },
    { pos: new mp.Vector3(260.80606, -878.696, 28.999), r: 116.3877 },
    { pos: new mp.Vector3(1711.6484, 3510.010, 36.321), r: 119.3507 },
    { pos: new mp.Vector3(-914.9197, -809.316, 15.828), r: 14.15479 },
    { pos: new mp.Vector3(-410.3753, -1919.23, 29.946), r: 71.28643 },
    { pos: new mp.Vector3(-2352.953, 3404.036, 31.965), r: 226.6347 },
    { pos: new mp.Vector3(-522.1986, 1332.685, 302.31), r: 276.0199 },
    { pos: new mp.Vector3(-995.4024, 647.6901, 139.97), r: 281.3421 },
    { pos: new mp.Vector3(-412.6247, -1925.37, 29.946), r: 82.75144 },
    { pos: new mp.Vector3(-477.5567, 1332.773, 308.06), r: 264.3593 },
    { pos: new mp.Vector3(-2336.085, 3394.637, 31.442), r: 270.4502 },
    { pos: new mp.Vector3(-1596.279, -573.307, 34.978), r: 76.86061 },
    { pos: new mp.Vector3(246.21426, -1261.54, 29.200), r: 189.9111 },
    { pos: new mp.Vector3(1691.8476, 3499.383, 36.409), r: 319.9088 },
    { pos: new mp.Vector3(-414.6806, -1932.65, 29.946), r: 81.22363 },
    { pos: new mp.Vector3(-934.1427, -809.023, 15.901), r: 349.9386 },
    { pos: new mp.Vector3(-2321.820, 3382.095, 31.091), r: 308.9629 },
    { pos: new mp.Vector3(-431.8089, 1128.876, 325.76), r: 261.6238 },
    { pos: new mp.Vector3(248.64575, -1277.92, 29.329), r: 230.7637 },
    { pos: new mp.Vector3(161.77255, 6555.091, 31.829), r: 203.2978 },
    { pos: new mp.Vector3(-415.9969, -1940.39, 29.946), r: 81.58734 },
    { pos: new mp.Vector3(247.65272, -1281.84, 29.459), r: 120.7505 },
    { pos: new mp.Vector3(-417.5133, 1127.357, 325.90), r: 293.7809 },
    { pos: new mp.Vector3(-936.2286, 689.8703, 157.18), r: 50.42988 },
    { pos: new mp.Vector3(241.56808, -1279.61, 29.488), r: 351.4051 },
    { pos: new mp.Vector3(1605.3337, 1281.477, 89.727), r: 209.7575 },
    { pos: new mp.Vector3(-2310.141, 3408.584, 30.948), r: 99.55332 },
    { pos: new mp.Vector3(-407.0968, 1141.458, 325.90), r: 357.0694 },
    { pos: new mp.Vector3(-416.3954, -1947.33, 29.946), r: 90.68251 },
    { pos: new mp.Vector3(243.10862, -1265.65, 29.469), r: 350.7379 },
    { pos: new mp.Vector3(175.08409, 6564.578, 32.063), r: 204.0858 },
    { pos: new mp.Vector3(-921.4708, -778.415, 15.783), r: 56.59122 },
    { pos: new mp.Vector3(243.89672, -1257.93, 29.475), r: 355.0200 },
    { pos: new mp.Vector3(-400.3121, 1162.577, 325.91), r: 338.6503 },
    { pos: new mp.Vector3(-416.2457, -1954.49, 29.946), r: 94.75714 },
    { pos: new mp.Vector3(1884.0521, 3869.960, 33.196), r: 29.03020 },
    { pos: new mp.Vector3(-2308.227, 3391.187, 30.984), r: 49.69786 },
    { pos: new mp.Vector3(-417.8778, 1169.376, 325.90), r: 55.12644 },
    { pos: new mp.Vector3(-910.5431, -778.489, 15.925), r: 188.1628 },
    { pos: new mp.Vector3(-415.2965, -1962.26, 29.946), r: 105.3076 },
    { pos: new mp.Vector3(-905.0923, 661.7100, 135.00), r: 275.6350 },
    { pos: new mp.Vector3(1876.0739, 3896.698, 33.705), r: 19.60418 },
    { pos: new mp.Vector3(-434.5859, 1174.012, 325.85), r: 66.60258 },
    { pos: new mp.Vector3(1493.3974, 1827.515, 107.71), r: 166.8227 },
    { pos: new mp.Vector3(-894.9716, 663.1759, 134.54), r: 275.6535 },
    { pos: new mp.Vector3(-1604.764, -479.394, 37.077), r: 223.3357 },
    { pos: new mp.Vector3(1868.7988, 3920.755, 32.878), r: 15.73832 },
    { pos: new mp.Vector3(194.31941, 6531.104, 31.957), r: 16.02022 },
    { pos: new mp.Vector3(-2298.940, 3383.925, 31.065), r: 232.3284 },
    { pos: new mp.Vector3(-413.1480, -1969.98, 29.946), r: 109.7459 },
    { pos: new mp.Vector3(-403.7404, 1204.640, 326.45), r: 334.2836 },
    { pos: new mp.Vector3(1864.9873, 3943.666, 33.019), r: 9.007520 },
    { pos: new mp.Vector3(-876.6783, -736.248, 24.219), r: 0.111445 },
    { pos: new mp.Vector3(-875.5123, 655.2142, 133.18), r: 175.9551 },
    { pos: new mp.Vector3(1895.1739, 3942.051, 32.502), r: 264.3260 },
    { pos: new mp.Vector3(-411.0648, -1975.61, 29.946), r: 115.1302 },
    { pos: new mp.Vector3(1172.8160, 2052.851, 58.677), r: 186.5780 },
    { pos: new mp.Vector3(1925.9377, 3929.875, 32.586), r: 237.2131 },
    { pos: new mp.Vector3(-438.2305, 1075.949, 352.39), r: 115.5760 },
    { pos: new mp.Vector3(-1621.677, -449.047, 38.762), r: 124.1441 },
    { pos: new mp.Vector3(-821.2903, 630.1214, 128.06), r: 227.8274 },
    { pos: new mp.Vector3(-876.6355, -730.922, 24.460), r: 180.8795 },
    { pos: new mp.Vector3(1946.5771, 3908.155, 32.219), r: 212.6981 },
    { pos: new mp.Vector3(-407.7093, -1982.51, 29.946), r: 118.8410 },
    { pos: new mp.Vector3(-2360.890, 3390.659, 32.832), r: 147.8383 },
    { pos: new mp.Vector3(834.00952, 2084.735, 68.143), r: 163.0005 },
    { pos: new mp.Vector3(-403.5662, -1989.33, 29.946), r: 124.8073 },
    { pos: new mp.Vector3(-798.6922, 611.9866, 127.88), r: 209.8096 },
    { pos: new mp.Vector3(1812.3076, 3936.073, 34.492), r: 99.20314 },
    { pos: new mp.Vector3(-2352.458, 3385.826, 32.832), r: 146.0230 },
    { pos: new mp.Vector3(-399.2979, -1994.82, 29.946), r: 122.0424 },
    { pos: new mp.Vector3(1777.7097, 3929.577, 34.184), r: 104.6980 },
    { pos: new mp.Vector3(-879.5643, -688.853, 26.026), r: 52.33618 },
    { pos: new mp.Vector3(-1662.122, -444.572, 39.650), r: 67.95310 },
    { pos: new mp.Vector3(-2326.706, 3370.729, 32.915), r: 199.8242 },
    { pos: new mp.Vector3(-394.2689, -2000.02, 29.946), r: 131.0649 },
    { pos: new mp.Vector3(1724.2498, 3904.080, 34.246), r: 124.2362 },
    { pos: new mp.Vector3(-779.2737, 568.3109, 125.68), r: 179.7131 },
    { pos: new mp.Vector3(-2306.867, 3359.026, 32.857), r: 160.0967 },
    { pos: new mp.Vector3(1693.6259, 3879.838, 34.841), r: 129.9980 },
    { pos: new mp.Vector3(-884.5194, -684.776, 25.462), r: 228.9652 },
    { pos: new mp.Vector3(-388.2507, -2005.09, 29.946), r: 143.5073 },
    { pos: new mp.Vector3(1675.4322, 3862.120, 35.022), r: 135.5660 },
    { pos: new mp.Vector3(-1676.086, -451.468, 39.617), r: 259.1512 },
    { pos: new mp.Vector3(1659.4584, 3846.946, 35.394), r: 135.2900 },
    { pos: new mp.Vector3(-382.4522, -2009.96, 29.946), r: 141.6883 },
    { pos: new mp.Vector3(763.26275, 6475.920, 30.181), r: 119.5301 },
    { pos: new mp.Vector3(1635.9461, 3823.177, 34.953), r: 135.3263 },
    { pos: new mp.Vector3(1603.1450, 3793.386, 34.804), r: 133.2984 },
    { pos: new mp.Vector3(-162.3220, 1956.892, 194.11), r: 306.6100 },
    { pos: new mp.Vector3(-2281.974, 3350.517, 33.032), r: 107.2617 },
    { pos: new mp.Vector3(-376.6868, -2014.80, 29.946), r: 140.8878 },
    { pos: new mp.Vector3(-67.51928, -815.868, 326.08), r: 321.0222 },
    { pos: new mp.Vector3(-736.3544, 558.1228, 130.03), r: 237.6130 },
    { pos: new mp.Vector3(-2312.003, 3323.312, 32.993), r: 60.51836 },
    { pos: new mp.Vector3(-374.8583, -2017.52, 29.946), r: 144.6714 },
    { pos: new mp.Vector3(806.84753, 6481.164, 23.521), r: 79.45455 },
    { pos: new mp.Vector3(-882.9636, -670.916, 27.860), r: 10.30570 },
    { pos: new mp.Vector3(-363.6651, -2027.04, 29.946), r: 141.3243 },
    { pos: new mp.Vector3(-710.6372, 538.7478, 119.87), r: 208.9240 },
    { pos: new mp.Vector3(-2397.812, 3347.738, 32.829), r: 326.2225 },
    { pos: new mp.Vector3(-354.7038, -2033.25, 29.946), r: 141.7610 },
    { pos: new mp.Vector3(-2405.963, 3353.065, 32.829), r: 328.9964 },
    { pos: new mp.Vector3(-703.7456, 519.1837, 110.65), r: 206.3927 },
    { pos: new mp.Vector3(1083.9255, 6508.986, 21.019), r: 190.1288 },
    { pos: new mp.Vector3(-1670.972, -469.767, 38.582), r: 266.3740 },
    { pos: new mp.Vector3(-348.9478, -2038.08, 29.946), r: 135.7947 },
    { pos: new mp.Vector3(-343.1826, -2042.90, 29.946), r: 145.6176 },
    { pos: new mp.Vector3(-867.0954, -681.545, 27.877), r: 273.7944 },
    { pos: new mp.Vector3(518.54711, 1334.017, 288.96), r: 338.4687 },
    { pos: new mp.Vector3(-335.9382, -2047.54, 29.946), r: 154.2960 },
    { pos: new mp.Vector3(-1709.938, -510.126, 37.664), r: 173.1523 },
    { pos: new mp.Vector3(-768.3886, 1658.760, 202.58), r: 359.0800 },
    { pos: new mp.Vector3(625.90850, 1393.051, 320.68), r: 237.3818 },
    { pos: new mp.Vector3(-2415.342, 3379.351, 32.974), r: 51.86362 },
    { pos: new mp.Vector3(-668.9555, 846.1809, 225.05), r: 254.7079 },
    { pos: new mp.Vector3(1465.8228, 6548.032, 14.143), r: 88.26116 },
    { pos: new mp.Vector3(-331.1793, -2050.15, 29.946), r: 153.9978 },
    { pos: new mp.Vector3(813.85040, 1356.043, 348.80), r: 254.0257 },
    { pos: new mp.Vector3(-2420.153, 3369.567, 32.974), r: 63.51444 },
    { pos: new mp.Vector3(822.22033, 1297.584, 364.39), r: 144.8702 },
    { pos: new mp.Vector3(-1720.615, -499.850, 38.880), r: 162.7681 },
    { pos: new mp.Vector3(-676.1441, 858.4104, 225.15), r: 291.3342 },
    { pos: new mp.Vector3(1587.1484, 3778.787, 34.662), r: 123.1814 },
    { pos: new mp.Vector3(-323.6910, -2053.09, 29.946), r: 161.3344 },
    { pos: new mp.Vector3(-2454.190, 3369.247, 32.827), r: 268.1276 },
    { pos: new mp.Vector3(1558.8759, 3757.716, 34.313), r: 125.2250 },
    { pos: new mp.Vector3(721.31164, 1278.754, 360.29), r: 319.5092 },
    { pos: new mp.Vector3(-316.9493, -2055.12, 29.946), r: 165.7279 },
    { pos: new mp.Vector3(1533.8056, 3741.081, 34.533), r: 122.9862 },
    { pos: new mp.Vector3(1501.8239, 3722.043, 34.521), r: 118.6117 },
    { pos: new mp.Vector3(-2453.918, 3332.545, 32.977), r: 18.14935 },
    { pos: new mp.Vector3(780.74139, 1270.031, 360.59), r: 282.6413 },
    { pos: new mp.Vector3(-309.7261, -2056.37, 29.946), r: 172.0295 },
    { pos: new mp.Vector3(1479.9645, 3711.162, 34.134), r: 111.9270 },
    { pos: new mp.Vector3(-779.9083, 1285.604, 259.19), r: 47.06078 },
    { pos: new mp.Vector3(1471.1474, 6511.558, 20.871), r: 170.9299 },
    { pos: new mp.Vector3(1451.0853, 3699.263, 33.933), r: 112.8748 },
    { pos: new mp.Vector3(-1755.443, -524.114, 38.589), r: 332.5967 },
    { pos: new mp.Vector3(766.03143, 1205.747, 331.27), r: 122.7790 },
    { pos: new mp.Vector3(1429.6966, 3690.502, 33.677), r: 110.6668 },
    { pos: new mp.Vector3(-2468.440, 3322.863, 32.976), r: 70.64894 },
    { pos: new mp.Vector3(-302.4669, -2056.87, 29.946), r: 180.4747 },
    { pos: new mp.Vector3(1398.4522, 3679.503, 33.710), r: 107.7616 },
    { pos: new mp.Vector3(755.46862, 1191.208, 326.62), r: 221.1002 },
    { pos: new mp.Vector3(-2494.173, 3319.797, 32.803), r: 311.4818 },
    { pos: new mp.Vector3(-294.1813, -2056.58, 29.946), r: 186.8246 },
    { pos: new mp.Vector3(-1771.219, -495.442, 39.541), r: 286.1002 },
    { pos: new mp.Vector3(-609.1911, 807.0979, 195.28), r: 248.3255 },
    { pos: new mp.Vector3(777.38757, 1181.044, 325.28), r: 244.8094 },
    { pos: new mp.Vector3(-2510.127, 3292.051, 32.806), r: 169.5547 },
    { pos: new mp.Vector3(-287.5476, -2055.62, 29.946), r: 191.6349 },
    { pos: new mp.Vector3(794.69769, 1189.800, 325.78), r: 243.5832 },
    { pos: new mp.Vector3(-588.0249, 798.7794, 191.26), r: 201.3710 },
    { pos: new mp.Vector3(-280.3748, -2053.85, 29.946), r: 197.4444 },
    { pos: new mp.Vector3(-1797.203, -472.602, 41.004), r: 279.9675 },
    { pos: new mp.Vector3(-2448.645, 2941.561, 32.956), r: 27.78115 },
    { pos: new mp.Vector3(598.71868, 1199.840, 310.17), r: 113.3646 },
    { pos: new mp.Vector3(-866.9588, -704.263, 27.089), r: 267.9268 },
    { pos: new mp.Vector3(539.82305, 1195.424, 297.24), r: 62.06274 },
    { pos: new mp.Vector3(-2469.057, 2966.377, 32.861), r: 96.92249 },
    { pos: new mp.Vector3(-272.7332, -2051.89, 29.946), r: 197.6634 },
    { pos: new mp.Vector3(-570.9017, 780.0266, 186.58), r: 234.6062 },
    { pos: new mp.Vector3(1597.2468, 6449.367, 25.317), r: 156.2512 },
    { pos: new mp.Vector3(-2555.642, 2989.604, 38.022), r: 336.5834 },
    { pos: new mp.Vector3(400.17047, 1072.829, 253.38), r: 168.6315 },
    { pos: new mp.Vector3(-266.7734, -2048.10, 29.946), r: 209.2390 },
    { pos: new mp.Vector3(-552.4579, 774.1682, 184.83), r: 228.7109 },
    { pos: new mp.Vector3(-1761.860, -423.666, 42.929), r: 233.7926 },
    { pos: new mp.Vector3(307.89859, 994.8413, 212.00), r: 168.6315 },
    { pos: new mp.Vector3(-260.4106, -2044.13, 29.946), r: 217.8420 },
    { pos: new mp.Vector3(1577.9721, 6458.420, 25.317), r: 153.0342 },
    { pos: new mp.Vector3(279.65505, 1007.305, 211.17), r: 126.2038 },
    { pos: new mp.Vector3(-254.6024, -2039.61, 29.946), r: 223.5848 },
    { pos: new mp.Vector3(-535.7249, 753.4713, 182.93), r: 264.4645 },
    { pos: new mp.Vector3(-867.4069, -723.614, 25.800), r: 268.0414 },
    { pos: new mp.Vector3(224.44000, 934.7686, 210.51), r: 139.9998 },
    { pos: new mp.Vector3(-2025.961, 3222.524, 40.556), r: 170.5747 },
    { pos: new mp.Vector3(-251.4846, -2037.58, 29.946), r: 282.1831 },
    { pos: new mp.Vector3(-585.5380, 340.5888, 85.466), r: 162.9679 },
    { pos: new mp.Vector3(-1747.064, -386.418, 45.442), r: 294.8251 },
    { pos: new mp.Vector3(84.092239, 813.9197, 214.29), r: 185.8994 },
    { pos: new mp.Vector3(-2014.403, 3248.218, 32.810), r: 51.82814 },
    { pos: new mp.Vector3(-866.8149, -741.705, 23.907), r: 257.5609 },
    { pos: new mp.Vector3(-513.1827, 729.6823, 156.13), r: 226.1942 },
    { pos: new mp.Vector3(128.14146, 641.5363, 207.94), r: 112.2793 },
    { pos: new mp.Vector3(-513.6297, 721.7955, 155.16), r: 146.2585 },
    { pos: new mp.Vector3(-1779.658, -365.552, 45.012), r: 311.5139 },
    { pos: new mp.Vector3(-2073.639, 3197.459, 32.868), r: 22.70858 },
    { pos: new mp.Vector3(-866.7992, -758.794, 22.204), r: 255.9292 },
    { pos: new mp.Vector3(-378.3186, 92.04711, 64.487), r: 144.1714 },
    { pos: new mp.Vector3(-2075.734, 3209.625, 32.821), r: 244.5018 },
    { pos: new mp.Vector3(-346.4795, 954.9706, 233.27), r: 299.3561 },
    { pos: new mp.Vector3(-482.2821, 716.5380, 156.05), r: 0.547986 },
    { pos: new mp.Vector3(-2056.099, 3229.742, 32.779), r: 90.13257 },
    { pos: new mp.Vector3(-866.7445, -777.216, 20.639), r: 263.4216 },
    { pos: new mp.Vector3(-1807.933, -374.201, 45.826), r: 46.46921 },
    { pos: new mp.Vector3(-473.4110, 684.3132, 152.17), r: 222.0205 },
    { pos: new mp.Vector3(-277.2635, -2082.41, 27.755), r: 54.76654 },
    { pos: new mp.Vector3(-255.6233, 50.97851, 61.503), r: 180.1624 },
    { pos: new mp.Vector3(1727.4608, 6409.121, 34.313), r: 172.1825 },
    { pos: new mp.Vector3(-283.2386, -2072.73, 27.755), r: 2.498235 },
    { pos: new mp.Vector3(-459.5232, 655.2626, 149.96), r: 49.23308 },
    { pos: new mp.Vector3(-867.2125, -801.326, 19.362), r: 249.5822 },
    { pos: new mp.Vector3(-1900.551, 3325.847, 32.960), r: 141.6493 },
    { pos: new mp.Vector3(1723.8999, 6410.635, 34.178), r: 148.8847 },
    { pos: new mp.Vector3(1396.0360, 3678.037, 33.642), r: 110.0377 },
    { pos: new mp.Vector3(-1886.246, 3315.539, 32.946), r: 82.97094 },
    { pos: new mp.Vector3(-439.9990, 640.7153, 150.68), r: 273.3424 },
    { pos: new mp.Vector3(1285.7508, 3638.497, 33.326), r: 109.6337 },
    { pos: new mp.Vector3(-318.2181, -2064.39, 27.755), r: 87.00165 },
    { pos: new mp.Vector3(-1868.375, -384.706, 47.637), r: 189.7633 },
    { pos: new mp.Vector3(1244.9573, 3627.058, 33.475), r: 103.6552 },
    { pos: new mp.Vector3(-1907.152, 3284.770, 32.944), r: 41.36354 },
    { pos: new mp.Vector3(1194.5146, 3618.346, 33.676), r: 95.93918 },
    { pos: new mp.Vector3(-850.8593, -814.853, 19.566), r: 87.90031 },
    { pos: new mp.Vector3(1148.8135, 3618.747, 33.770), r: 88.96761 },
    { pos: new mp.Vector3(1702.9468, 6416.032, 32.764), r: 151.4278 },
    { pos: new mp.Vector3(-1918.573, 3262.699, 32.936), r: 1.447489 },
    { pos: new mp.Vector3(-1889.302, -368.337, 49.064), r: 94.26882 },
    { pos: new mp.Vector3(1096.2006, 3620.641, 33.773), r: 87.51033 },
    { pos: new mp.Vector3(-500.9130, 252.3318, 83.375), r: 86.49506 },
    { pos: new mp.Vector3(1043.8308, 3624.177, 33.050), r: 86.02918 },
    { pos: new mp.Vector3(-493.9539, 250.4705, 83.325), r: 241.2367 },
    { pos: new mp.Vector3(-851.7138, -788.504, 19.888), r: 98.45911 },
    { pos: new mp.Vector3(989.52136, 3626.077, 32.471), r: 89.47106 },
    { pos: new mp.Vector3(-302.5354, -2066.55, 27.755), r: 226.9120 },
    { pos: new mp.Vector3(-483.3876, 248.8285, 83.308), r: 270.2963 },
    { pos: new mp.Vector3(1700.3280, 6416.909, 32.764), r: 341.1177 },
    { pos: new mp.Vector3(973.11706, 3626.281, 32.309), r: 90.91796 },
    { pos: new mp.Vector3(-405.0299, 614.0413, 148.03), r: 233.1057 },
    { pos: new mp.Vector3(-465.8485, 247.4781, 83.388), r: 265.1433 },
    { pos: new mp.Vector3(-1922.699, -374.113, 49.397), r: 289.8257 },
    { pos: new mp.Vector3(-851.1375, -769.005, 21.282), r: 83.27119 },
    { pos: new mp.Vector3(-1932.533, 3271.001, 32.957), r: 308.7737 },
    { pos: new mp.Vector3(941.34802, 3626.562, 32.372), r: 90.12592 },
    { pos: new mp.Vector3(-334.9156, -2058.74, 27.755), r: 161.0457 },
    { pos: new mp.Vector3(907.75512, 3626.894, 32.453), r: 86.39151 },
    { pos: new mp.Vector3(-1943.097, -352.169, 47.530), r: 334.7695 },
    { pos: new mp.Vector3(873.52447, 3627.163, 32.919), r: 87.69117 },
    { pos: new mp.Vector3(-348.3244, -2049.76, 27.988), r: 63.96465 },
    { pos: new mp.Vector3(-451.9578, 245.8110, 83.504), r: 263.6636 },
    { pos: new mp.Vector3(-401.2037, 584.7164, 124.62), r: 102.8942 },
    { pos: new mp.Vector3(-881.5013, -855.846, 19.123), r: 280.6297 },
    { pos: new mp.Vector3(-1945.200, 3278.072, 32.960), r: 325.2723 },
    { pos: new mp.Vector3(868.61700, 3640.166, 32.850), r: 225.4869 },
    { pos: new mp.Vector3(1697.2312, 6426.691, 32.763), r: 156.5228 },
    { pos: new mp.Vector3(936.71185, 3639.936, 32.380), r: 271.2018 },
    { pos: new mp.Vector3(-395.6278, 582.9406, 124.62), r: 192.2562 },
    { pos: new mp.Vector3(-360.6347, -2040.45, 27.755), r: 49.05995 },
    { pos: new mp.Vector3(1010.0426, 3637.412, 32.519), r: 266.4396 },
    { pos: new mp.Vector3(1050.3471, 3635.341, 33.110), r: 268.3179 },
    { pos: new mp.Vector3(-884.0175, -847.629, 19.123), r: 285.2959 },
    { pos: new mp.Vector3(-2021.774, 3314.971, 32.907), r: 91.54865 },
    { pos: new mp.Vector3(1700.1372, 6425.473, 32.764), r: 157.8647 },
    { pos: new mp.Vector3(-1910.767, -325.597, 48.813), r: 96.99263 },
    { pos: new mp.Vector3(-504.5513, 252.7636, 83.384), r: 84.95346 },
    { pos: new mp.Vector3(-411.3072, 590.1290, 124.62), r: 127.4817 },
    { pos: new mp.Vector3(1100.3328, 3632.391, 33.816), r: 268.4992 },
    { pos: new mp.Vector3(1140.4943, 3632.386, 34.966), r: 268.5259 },
    { pos: new mp.Vector3(-387.9900, -2017.14, 27.769), r: 116.0657 },
    { pos: new mp.Vector3(-1998.288, 3322.083, 32.960), r: 236.2599 },
    { pos: new mp.Vector3(-416.4886, 588.4151, 124.68), r: 291.5803 },
    { pos: new mp.Vector3(1190.8171, 3630.420, 33.738), r: 276.4868 },
    { pos: new mp.Vector3(-786.6702, 216.1769, 76.326), r: 102.7465 },
    { pos: new mp.Vector3(-866.3458, -860.503, 18.799), r: 276.6133 },
    { pos: new mp.Vector3(-1991.440, 3338.273, 32.959), r: 36.55635 },
    { pos: new mp.Vector3(1240.2227, 3638.677, 33.475), r: 282.4345 },
    { pos: new mp.Vector3(-407.1055, 587.5150, 124.63), r: 245.7691 },
    { pos: new mp.Vector3(-1897.606, -285.655, 49.382), r: 31.94519 },
    { pos: new mp.Vector3(-400.0821, -2006.37, 28.156), r: 122.6237 },
    { pos: new mp.Vector3(-780.7438, 215.3867, 76.074), r: 50.47740 },
    { pos: new mp.Vector3(-1969.805, 3362.645, 32.949), r: 236.5613 },
    { pos: new mp.Vector3(1668.2637, 6413.732, 30.356), r: 164.8022 },
    { pos: new mp.Vector3(-410.8359, -1994.39, 27.861), r: 75.74256 },
    { pos: new mp.Vector3(-796.1415, 215.9270, 76.374), r: 91.87154 },
    { pos: new mp.Vector3(-839.2144, -852.568, 19.445), r: 49.78495 },
    { pos: new mp.Vector3(-1970.341, 3363.581, 32.924), r: 30.42653 },
    { pos: new mp.Vector3(-416.8526, 555.1603, 124.28), r: 103.0175 },
    { pos: new mp.Vector3(-806.3579, 215.7475, 75.995), r: 90.57009 },
    { pos: new mp.Vector3(-1881.320, -320.594, 49.370), r: 69.71762 },
    { pos: new mp.Vector3(-1931.254, 3356.993, 32.890), r: 192.1397 },
    { pos: new mp.Vector3(-419.3880, -1979.12, 27.804), r: 120.4704 },
    { pos: new mp.Vector3(-884.6159, 228.3700, 72.843), r: 70.52230 },
    { pos: new mp.Vector3(-948.0717, -632.720, 26.732), r: 263.8529 },
    { pos: new mp.Vector3(-891.1044, 231.2906, 72.169), r: 64.51364 },
    { pos: new mp.Vector3(-396.3899, 470.0595, 120.19), r: 122.8490 },
    { pos: new mp.Vector3(-822.2515, -845.068, 20.189), r: 2.659514 },
    { pos: new mp.Vector3(-901.1567, 236.0978, 71.151), r: 81.90561 },
    { pos: new mp.Vector3(-423.9566, -1964.03, 27.812), r: 36.82759 },
    { pos: new mp.Vector3(-1954.554, 3314.716, 32.960), r: 352.1445 },
    { pos: new mp.Vector3(-948.6904, 254.4365, 70.308), r: 62.93927 },
    { pos: new mp.Vector3(-1875.612, -353.537, 49.327), r: 349.3907 },
    { pos: new mp.Vector3(-955.5170, 256.8544, 70.032), r: 88.33423 },
    { pos: new mp.Vector3(-1960.773, 3347.601, 32.960), r: 260.2148 },
    { pos: new mp.Vector3(1595.8276, 6561.188, 13.436), r: 199.5480 },
    { pos: new mp.Vector3(-425.5602, -1946.76, 27.742), r: 83.48921 },
    { pos: new mp.Vector3(-788.5909, -844.219, 21.308), r: 0.378657 },
    { pos: new mp.Vector3(-1993.253, 3305.433, 32.960), r: 262.7712 },
    { pos: new mp.Vector3(-1864.467, -344.579, 49.837), r: 142.8641 },
    { pos: new mp.Vector3(-994.0191, 263.8199, 67.947), r: 85.17587 },
    { pos: new mp.Vector3(-1003.307, 262.5826, 67.170), r: 92.79471 },
    { pos: new mp.Vector3(-423.2950, -1930.99, 27.953), r: 21.48448 },
    { pos: new mp.Vector3(-797.6212, -844.622, 21.008), r: 7.304533 },
    { pos: new mp.Vector3(-1011.600, 262.3945, 66.592), r: 91.49725 },
    { pos: new mp.Vector3(-461.7229, 454.3586, 107.49), r: 38.03632 },
    { pos: new mp.Vector3(-417.0687, -1914.34, 28.169), r: 78.52644 },
    { pos: new mp.Vector3(-812.4296, -844.625, 20.516), r: 17.32337 },
    { pos: new mp.Vector3(-1848.148, -356.841, 49.387), r: 144.9504 },
    { pos: new mp.Vector3(-1167.866, 248.6626, 67.944), r: 98.04892 },
    { pos: new mp.Vector3(-1175.279, 247.1311, 67.753), r: 101.9929 },
    { pos: new mp.Vector3(-1841.794, 3286.816, 32.775), r: 236.9386 },
    { pos: new mp.Vector3(-1184.313, 245.2167, 68.182), r: 102.0917 },
    { pos: new mp.Vector3(-1193.255, 243.2195, 67.994), r: 102.0953 },
    { pos: new mp.Vector3(-1821.213, 3275.381, 32.686), r: 96.91535 },
    { pos: new mp.Vector3(-448.7221, 417.1882, 109.20), r: 184.4757 },
    { pos: new mp.Vector3(-814.4283, -823.311, 20.009), r: 185.2782 },
    { pos: new mp.Vector3(-1201.945, 241.3698, 67.994), r: 102.0886 },
    { pos: new mp.Vector3(-1836.777, 3253.636, 32.669), r: 77.44226 },
    { pos: new mp.Vector3(-393.6968, -1885.61, 27.759), r: 2.812241 },
    { pos: new mp.Vector3(-1211.458, 239.2061, 67.658), r: 102.6593 },
    { pos: new mp.Vector3(-1842.707, -382.200, 49.310), r: 312.3665 },
    { pos: new mp.Vector3(-1855.071, 3224.660, 32.842), r: 36.10162 },
    { pos: new mp.Vector3(-1221.276, 236.9855, 66.931), r: 102.6991 },
    { pos: new mp.Vector3(1245.0456, 3639.973, 33.592), r: 285.0183 },
    { pos: new mp.Vector3(-826.1937, -822.998, 19.630), r: 163.4414 },
    { pos: new mp.Vector3(-462.8626, 392.2760, 103.35), r: 168.1072 },
    { pos: new mp.Vector3(-550.9639, -262.988, 35.725), r: 317.1586 },
    { pos: new mp.Vector3(1280.8217, 3650.279, 33.177), r: 287.3714 },
    { pos: new mp.Vector3(-1265.608, 223.4022, 62.078), r: 108.2153 },
    { pos: new mp.Vector3(-378.3606, -1877.65, 27.973), r: 65.12454 },
    { pos: new mp.Vector3(-538.7379, -264.489, 35.488), r: 307.6043 },
    { pos: new mp.Vector3(-1293.906, 219.4302, 59.468), r: 94.71812 },
    { pos: new mp.Vector3(-461.7536, 366.2257, 104.31), r: 268.9990 },
    { pos: new mp.Vector3(-1828.820, -366.509, 50.137), r: 192.5454 },
    { pos: new mp.Vector3(-532.6359, -253.261, 35.744), r: 57.05998 },
    { pos: new mp.Vector3(-1329.444, 216.3792, 58.860), r: 97.91567 },
    { pos: new mp.Vector3(-363.1911, -1872.75, 27.838), r: 340.6782 },
    { pos: new mp.Vector3(-838.8591, -822.449, 19.423), r: 209.8569 },
    { pos: new mp.Vector3(-1353.948, 212.3605, 58.884), r: 101.5262 },
    { pos: new mp.Vector3(-538.1244, -240.762, 36.291), r: 45.82719 },
    { pos: new mp.Vector3(67.011566, 7051.257, 16.653), r: 12.30522 },
    { pos: new mp.Vector3(-429.9661, 363.9856, 106.54), r: 275.6699 },
    { pos: new mp.Vector3(-550.1813, -246.675, 36.631), r: 151.3399 },
    { pos: new mp.Vector3(-346.1069, -1871.11, 27.755), r: 261.9125 },
    { pos: new mp.Vector3(-1424.871, 157.7540, 55.558), r: 157.9197 },
    { pos: new mp.Vector3(-1872.983, 3239.550, 32.832), r: 206.9035 },
    { pos: new mp.Vector3(-559.3042, -255.472, 36.372), r: 130.3437 },
    { pos: new mp.Vector3(-1435.121, 128.0677, 52.972), r: 180.4180 },
    { pos: new mp.Vector3(-803.1153, -822.606, 20.415), r: 220.8184 },
    { pos: new mp.Vector3(-1436.465, 102.7612, 52.366), r: 185.9694 },
    { pos: new mp.Vector3(-421.2629, 341.7422, 105.99), r: 46.27989 },
    { pos: new mp.Vector3(-505.4951, -244.655, 36.071), r: 291.4905 },
    { pos: new mp.Vector3(-1826.478, -321.369, 43.284), r: 316.6412 },
    { pos: new mp.Vector3(-1435.386, 83.64031, 52.487), r: 182.2046 },
    { pos: new mp.Vector3(-330.1592, -1873.45, 27.970), r: 275.6663 },
    { pos: new mp.Vector3(-491.1174, -244.850, 35.904), r: 323.1051 },
    { pos: new mp.Vector3(-761.3687, -815.818, 22.631), r: 211.7175 },
    { pos: new mp.Vector3(-1430.755, 36.23487, 52.638), r: 186.3497 },
    { pos: new mp.Vector3(-1645.038, 3044.454, 31.404), r: 285.7317 },
    { pos: new mp.Vector3(-481.6340, -238.296, 36.085), r: 123.8063 },
    { pos: new mp.Vector3(-1560.507, 3020.849, 33.089), r: 153.5528 },
    { pos: new mp.Vector3(-1426.879, 17.72923, 52.782), r: 334.1127 },
    { pos: new mp.Vector3(-313.6040, -1878.86, 27.782), r: 54.92931 },
    { pos: new mp.Vector3(-489.2261, -231.614, 36.213), r: 353.2673 },
    { pos: new mp.Vector3(-416.5648, 318.1033, 103.59), r: 170.4341 },
    { pos: new mp.Vector3(-1596.073, 2935.064, 32.859), r: 266.8393 },
    { pos: new mp.Vector3(-1792.303, -345.113, 44.577), r: 320.4294 },
    { pos: new mp.Vector3(-497.9570, -227.176, 36.436), r: 91.42803 },
    { pos: new mp.Vector3(-1413.981, 3049.328, 98.052), r: 229.7800 },
    { pos: new mp.Vector3(-752.3370, -803.161, 23.477), r: 248.6737 },
    { pos: new mp.Vector3(142.33767, 6864.437, 28.336), r: 353.0481 },
    { pos: new mp.Vector3(-300.0517, -1887.07, 27.802), r: 281.0131 },
    { pos: new mp.Vector3(-1359.561, 3015.696, 104.91), r: 224.1221 },
    { pos: new mp.Vector3(-392.2856, 300.2791, 84.891), r: 11.91257 },
    { pos: new mp.Vector3(-1313.406, 2992.560, 74.134), r: 231.5762 },
    { pos: new mp.Vector3(-1216.100, 2911.305, 51.464), r: 211.3743 },
    { pos: new mp.Vector3(-751.7769, -788.749, 24.532), r: 275.2864 },
    { pos: new mp.Vector3(-1245.945, 2794.327, 20.126), r: 181.6407 },
    { pos: new mp.Vector3(-288.2127, -1897.49, 27.976), r: 319.0409 },
    { pos: new mp.Vector3(-384.1954, 272.9628, 84.723), r: 93.72602 },
    { pos: new mp.Vector3(-1234.388, 2726.406, 9.0591), r: 189.8595 },
    { pos: new mp.Vector3(-1741.670, -338.140, 46.428), r: 176.6584 },
    { pos: new mp.Vector3(290.10247, -126.496, 69.389), r: 17.49887 },
    { pos: new mp.Vector3(96.169082, 6801.001, 20.176), r: 276.8365 },
    { pos: new mp.Vector3(-1198.558, 2781.173, 14.083), r: 311.0997 },
    { pos: new mp.Vector3(-374.8288, 280.4042, 84.819), r: 235.3931 },
    { pos: new mp.Vector3(-1125.330, 2841.247, 14.505), r: 312.4536 },
    { pos: new mp.Vector3(-260.4496, -1920.58, 27.950), r: 325.3631 },
    { pos: new mp.Vector3(-1100.227, 2886.001, 15.755), r: 192.8880 },
    { pos: new mp.Vector3(-751.7395, -748.698, 26.971), r: 318.2896 },
    { pos: new mp.Vector3(370.23876, -144.561, 64.512), r: 304.7404 },
    { pos: new mp.Vector3(-1700.112, -415.514, 45.571), r: 49.21377 },
    { pos: new mp.Vector3(-1121.859, 2922.547, 37.852), r: 206.7722 },
    { pos: new mp.Vector3(-237.7665, -1942.78, 27.858), r: 296.5895 },
    { pos: new mp.Vector3(-737.8853, -747.715, 27.030), r: 90.11659 },
    { pos: new mp.Vector3(-1398.703, -23.0687, 52.890), r: 213.5006 },
    { pos: new mp.Vector3(-340.6894, 298.8140, 85.438), r: 118.2830 },
    { pos: new mp.Vector3(-1036.074, 2901.457, 10.906), r: 176.0622 },
    { pos: new mp.Vector3(364.43057, -88.4412, 67.507), r: 227.4219 },
    { pos: new mp.Vector3(-1645.760, -358.452, 49.791), r: 296.6325 },
    { pos: new mp.Vector3(-1384.494, -35.2315, 52.915), r: 257.1981 },
    { pos: new mp.Vector3(-247.8688, -1930.78, 27.756), r: 356.6582 },
    { pos: new mp.Vector3(-1066.496, 3013.289, 38.857), r: 155.5335 },
    { pos: new mp.Vector3(-185.2278, 6561.821, 11.104), r: 140.0450 },
    { pos: new mp.Vector3(-1366.341, -35.8553, 52.063), r: 275.3354 },
    { pos: new mp.Vector3(-338.4786, 281.5994, 85.592), r: 186.9232 },
    { pos: new mp.Vector3(-1352.917, -34.6365, 51.513), r: 274.8414 },
    { pos: new mp.Vector3(-1149.407, 3089.115, 91.821), r: 168.7024 },
    { pos: new mp.Vector3(-1621.544, -329.439, 50.815), r: 38.86026 },
    { pos: new mp.Vector3(-1344.452, -32.1918, 51.357), r: 301.6786 },
    { pos: new mp.Vector3(-229.7336, -1958.04, 27.998), r: 291.4930 },
    { pos: new mp.Vector3(-1372.268, 3067.023, 113.68), r: 112.0866 },
    { pos: new mp.Vector3(355.43035, 162.8189, 103.03), r: 194.0895 },
    { pos: new mp.Vector3(-755.9263, -703.412, 29.783), r: 270.7649 },
    { pos: new mp.Vector3(-224.7730, -1973.08, 27.848), r: 272.3993 },
    { pos: new mp.Vector3(-1583.527, -338.594, 47.480), r: 285.6404 },
    { pos: new mp.Vector3(-326.2416, 305.4789, 86.350), r: 152.8517 },
    { pos: new mp.Vector3(-421.9327, 6368.851, 13.724), r: 26.22873 },
    { pos: new mp.Vector3(-1649.623, 3115.033, 31.676), r: 305.7668 },
    { pos: new mp.Vector3(-1661.927, 3150.151, 31.654), r: 309.6532 },
    { pos: new mp.Vector3(-222.6035, -1989.34, 27.755), r: 270.4241 },
    { pos: new mp.Vector3(-755.8956, -714.609, 29.347), r: 264.7986 },
    { pos: new mp.Vector3(-311.6643, 301.5443, 92.844), r: 245.0350 },
    { pos: new mp.Vector3(-1310.193, -34.6880, 49.040), r: 282.2293 },
    { pos: new mp.Vector3(-1652.991, 3181.388, 31.980), r: 313.2825 },
    { pos: new mp.Vector3(-1307.912, -40.5887, 48.245), r: 159.2046 },
    { pos: new mp.Vector3(-224.8720, -2005.90, 27.757), r: 266.7462 },
    { pos: new mp.Vector3(-1299.896, -38.4491, 48.048), r: 287.7855 },
    { pos: new mp.Vector3(-1655.179, 3242.073, 39.993), r: 114.2491 },
    { pos: new mp.Vector3(-1286.367, -42.5494, 46.966), r: 253.1145 },
    { pos: new mp.Vector3(-1741.708, 3297.301, 36.677), r: 111.8997 },
    { pos: new mp.Vector3(-1517.704, -791.569, 18.176), r: 276.3914 },
    { pos: new mp.Vector3(105.48065, 363.4511, 113.75), r: 129.3741 },
    { pos: new mp.Vector3(-733.7645, -689.406, 30.294), r: 96.68741 },
    { pos: new mp.Vector3(-1772.884, 3331.426, 49.482), r: 189.0193 },
    { pos: new mp.Vector3(-1271.247, -48.3737, 45.750), r: 245.6261 },
    { pos: new mp.Vector3(2181.5187, 3017.102, 45.410), r: 255.5105 },
    { pos: new mp.Vector3(-1771.566, 3328.459, 49.646), r: 197.5209 },
    { pos: new mp.Vector3(-1254.144, -56.2354, 44.595), r: 244.7244 },
    { pos: new mp.Vector3(-444.2557, 6242.725, 29.312), r: 257.0206 },
    { pos: new mp.Vector3(2247.6953, 3016.975, 45.193), r: 248.4424 },
    { pos: new mp.Vector3(-1242.825, -66.8841, 43.870), r: 235.0694 },
    { pos: new mp.Vector3(2278.7336, 3011.393, 45.740), r: 230.0293 },
    { pos: new mp.Vector3(-1897.627, 3395.969, 53.981), r: 129.0707 },
    { pos: new mp.Vector3(-718.5877, -673.717, 30.338), r: 338.1907 },
    { pos: new mp.Vector3(-1230.099, -74.9430, 43.132), r: 238.9745 },
    { pos: new mp.Vector3(-1453.362, -872.717, 10.885), r: 156.4372 },
    { pos: new mp.Vector3(-1210.165, -86.6167, 41.938), r: 238.5375 },
    { pos: new mp.Vector3(-2046.676, 3444.267, 30.921), r: 133.0499 },
    { pos: new mp.Vector3(-1194.571, -94.5713, 40.935), r: 238.6072 },
    { pos: new mp.Vector3(-297.2915, 326.8648, 93.737), r: 26.19714 },
    { pos: new mp.Vector3(-760.2453, -672.650, 30.278), r: 1.502031 },
    { pos: new mp.Vector3(-1178.376, -104.193, 40.176), r: 245.3151 },
    { pos: new mp.Vector3(-2100.182, 3420.813, 32.982), r: 123.6733 },
    { pos: new mp.Vector3(-1138.712, -125.203, 39.156), r: 241.8616 },
    { pos: new mp.Vector3(-297.2333, 323.5828, 93.736), r: 31.85668 },
    { pos: new mp.Vector3(-2138.523, 3534.076, 74.361), r: 202.6219 },
    { pos: new mp.Vector3(2287.8520, 2992.220, 46.280), r: 337.9560 },
    { pos: new mp.Vector3(-1116.490, -136.616, 38.613), r: 245.0108 },
    { pos: new mp.Vector3(-2131.655, 3514.247, 72.483), r: 168.5147 },
    { pos: new mp.Vector3(2262.2702, 2999.588, 45.434), r: 76.67614 },
    { pos: new mp.Vector3(-1075.437, -156.370, 37.767), r: 239.7076 },
    { pos: new mp.Vector3(2227.6682, 3004.516, 45.044), r: 86.02219 },
    { pos: new mp.Vector3(-804.5095, -675.750, 28.294), r: 39.75351 },
    { pos: new mp.Vector3(-1058.418, -164.260, 37.873), r: 229.9123 },
    { pos: new mp.Vector3(-1345.150, -804.502, 19.094), r: 130.2080 },
    { pos: new mp.Vector3(2193.0019, 3003.893, 45.372), r: 67.27381 },
    { pos: new mp.Vector3(-1052.909, -165.616, 37.988), r: 290.4659 },
    { pos: new mp.Vector3(2133.7243, 2999.070, 45.202), r: 88.14849 },
    { pos: new mp.Vector3(-1038.526, -163.102, 38.312), r: 270.8654 },
    { pos: new mp.Vector3(-333.9589, 6170.856, 32.424), r: 41.05746 },
    { pos: new mp.Vector3(-1030.252, -161.143, 38.183), r: 272.0238 },
    { pos: new mp.Vector3(-2431.204, 3504.859, 33.121), r: 230.6838 },
    { pos: new mp.Vector3(-813.8490, -670.261, 27.973), r: 5.268590 },
    { pos: new mp.Vector3(-1006.305, -151.301, 37.773), r: 292.1026 },
    { pos: new mp.Vector3(-2435.722, 3439.822, 43.521), r: 129.6594 },
    { pos: new mp.Vector3(-978.6360, -143.208, 37.993), r: 280.7559 },
    { pos: new mp.Vector3(2162.5053, 2972.427, 46.450), r: 100.4626 },
    { pos: new mp.Vector3(-2441.412, 3431.366, 43.219), r: 299.0936 },
    { pos: new mp.Vector3(-927.3124, -114.688, 37.705), r: 298.0087 },
    { pos: new mp.Vector3(2177.6564, 2952.368, 46.325), r: 199.3036 },
    { pos: new mp.Vector3(-824.8543, -669.721, 27.848), r: 344.1921 },
    { pos: new mp.Vector3(-2448.515, 3441.549, 40.742), r: 322.0328 },
    { pos: new mp.Vector3(2189.1882, 2915.969, 46.377), r: 192.7458 },
    { pos: new mp.Vector3(-2437.302, 3462.530, 41.894), r: 287.7968 },
    { pos: new mp.Vector3(-931.0260, -102.514, 38.405), r: 301.8074 },
    { pos: new mp.Vector3(-336.8425, 6058.470, 31.211), r: 222.9857 },
    { pos: new mp.Vector3(-2420.714, 3457.724, 40.901), r: 160.1639 },
    { pos: new mp.Vector3(2192.4851, 2867.652, 47.052), r: 180.9243 },
    { pos: new mp.Vector3(-921.1629, -95.3165, 38.360), r: 311.4832 },
    { pos: new mp.Vector3(2192.1821, 2824.590, 48.348), r: 177.9133 },
    { pos: new mp.Vector3(-2486.977, 3386.281, 35.099), r: 257.9702 },
    { pos: new mp.Vector3(-2486.781, 3407.941, 32.216), r: 314.3757 },
    { pos: new mp.Vector3(2177.5219, 2792.996, 50.858), r: 133.1585 },
    { pos: new mp.Vector3(-921.5845, -107.726, 38.050), r: 161.1403 },
    { pos: new mp.Vector3(-849.5540, -672.117, 27.784), r: 58.86179 },
    { pos: new mp.Vector3(-2486.199, 3443.201, 31.074), r: 312.7817 },
    { pos: new mp.Vector3(2146.0935, 2766.117, 50.084), r: 131.6840 },
    { pos: new mp.Vector3(1716.9630, -1690.68, 112.45), r: 187.3234 },
    { pos: new mp.Vector3(2100.9946, 2730.534, 48.573), r: 128.5164 },
    { pos: new mp.Vector3(2043.8740, 2685.006, 47.375), r: 129.2718 },
    { pos: new mp.Vector3(-2484.314, 3512.403, 16.717), r: 68.56253 },
    { pos: new mp.Vector3(-827.9571, -251.102, 37.562), r: 237.9603 },
    { pos: new mp.Vector3(1719.5159, -1681.86, 112.61), r: 276.3183 },
    { pos: new mp.Vector3(-2502.131, 3502.262, 14.246), r: 98.19335 },
    { pos: new mp.Vector3(1987.8598, 2639.157, 46.484), r: 129.8101 },
    { pos: new mp.Vector3(-460.2966, 6014.333, 31.490), r: 87.57900 },
    { pos: new mp.Vector3(-2507.610, 3480.142, 14.608), r: 139.2937 },
    { pos: new mp.Vector3(1948.6717, 2607.864, 45.967), r: 117.8305 },
    { pos: new mp.Vector3(-819.9348, -266.455, 37.710), r: 202.7363 },
    { pos: new mp.Vector3(1917.8594, 2598.766, 46.078), r: 92.28929 },
    { pos: new mp.Vector3(-2536.320, 3418.607, 13.388), r: 80.66098 },
    { pos: new mp.Vector3(1892.8963, 2598.138, 45.853), r: 88.54594 },
    { pos: new mp.Vector3(1733.7687, -1674.71, 112.61), r: 137.2827 },
    { pos: new mp.Vector3(-736.5810, -340.454, 36.964), r: 220.5628 },
    { pos: new mp.Vector3(1888.1844, 2613.670, 45.677), r: 341.1784 },
    { pos: new mp.Vector3(-717.0425, -348.096, 36.094), r: 220.5628 },
    { pos: new mp.Vector3(1727.3878, -1642.63, 112.57), r: 188.6342 },
    { pos: new mp.Vector3(-2545.797, 3386.052, 13.399), r: 84.61430 },
    { pos: new mp.Vector3(-703.0748, -353.536, 35.729), r: 220.5628 },
    { pos: new mp.Vector3(1921.5791, 2611.832, 46.168), r: 278.4947 },
    { pos: new mp.Vector3(-2582.317, 3395.305, 13.298), r: 233.8827 },
    { pos: new mp.Vector3(-44.75052, 6300.472, 31.630), r: 29.11025 },
    { pos: new mp.Vector3(1720.6442, -1633.46, 112.51), r: 325.3428 },
    { pos: new mp.Vector3(1947.5878, 2620.953, 45.928), r: 313.9077 },
    { pos: new mp.Vector3(-574.7482, -371.694, 36.399), r: 220.5628 },
    { pos: new mp.Vector3(1979.8802, 2647.059, 46.318), r: 311.3350 },
    { pos: new mp.Vector3(-563.8682, -369.133, 36.137), r: 220.5628 },
    { pos: new mp.Vector3(-2402.522, 3425.466, 33.273), r: 276.1429 },
    { pos: new mp.Vector3(2.3052568, 284.8439, 112.89), r: 28.78476 },
    { pos: new mp.Vector3(2015.7667, 2677.506, 47.014), r: 307.7348 },
    { pos: new mp.Vector3(-505.2919, -371.615, 36.533), r: 220.5628 },
    { pos: new mp.Vector3(2053.5617, 2707.696, 47.579), r: 310.2640 },
    { pos: new mp.Vector3(-477.5286, -373.685, 36.045), r: 220.5628 },
    { pos: new mp.Vector3(1737.4244, -1620.42, 112.42), r: 111.2631 },
    { pos: new mp.Vector3(2095.8081, 2741.678, 48.787), r: 308.4499 },
    { pos: new mp.Vector3(-444.1238, -379.613, 34.263), r: 220.5628 },
    { pos: new mp.Vector3(-439.1351, -379.292, 34.357), r: 220.5628 },
    { pos: new mp.Vector3(2132.6101, 2770.928, 49.748), r: 308.9488 },
    { pos: new mp.Vector3(2168.9936, 2800.116, 48.951), r: 308.7185 },
    { pos: new mp.Vector3(-400.8625, -384.936, 34.253), r: 220.5628 },
    { pos: new mp.Vector3(-2265.111, 3352.803, 33.056), r: 8.391892 },
    { pos: new mp.Vector3(1736.6762, -1614.45, 112.44), r: 93.90258 },
    { pos: new mp.Vector3(-364.1279, -388.695, 32.340), r: 220.5628 },
    { pos: new mp.Vector3(2182.0083, 2839.304, 47.187), r: 356.7211 },
    { pos: new mp.Vector3(2180.6130, 2889.710, 46.721), r: 5.437282 },
    { pos: new mp.Vector3(-2267.372, 3374.513, 32.440), r: 146.4125 },
    { pos: new mp.Vector3(1728.5037, -1602.66, 112.50), r: 194.3662 },
    { pos: new mp.Vector3(2170.2763, 2940.705, 46.472), r: 19.38755 },
    { pos: new mp.Vector3(-339.3854, -392.254, 32.310), r: 220.5628 },
    { pos: new mp.Vector3(-323.5312, -393.978, 31.279), r: 220.5628 },
    { pos: new mp.Vector3(1727.6016, -1595.75, 112.52), r: 15.98819 },
    { pos: new mp.Vector3(2134.2854, 2976.685, 46.192), r: 57.50516 },
    { pos: new mp.Vector3(-2262.124, 3379.352, 32.690), r: 215.1800 },
    { pos: new mp.Vector3(91.657402, 6395.713, 31.375), r: 323.2709 },
    { pos: new mp.Vector3(-288.8750, -398.656, 31.528), r: 220.5628 },
    { pos: new mp.Vector3(2086.2731, 2992.760, 44.910), r: 74.75493 },
    { pos: new mp.Vector3(-2191.761, 3351.201, 33.101), r: 239.8793 },
    { pos: new mp.Vector3(-2165.117, 3353.351, 33.110), r: 146.1943 },
    { pos: new mp.Vector3(-220.4087, -423.643, 31.559), r: 71.49260 },
    { pos: new mp.Vector3(1739.2845, -1592.23, 112.56), r: 11.19334 },
    { pos: new mp.Vector3(2024.0667, 2983.243, 45.201), r: 116.9661 },
    { pos: new mp.Vector3(-218.3645, -427.009, 31.805), r: 217.8017 },
    { pos: new mp.Vector3(-2176.415, 3321.585, 32.820), r: 58.61228 },
    { pos: new mp.Vector3(1973.3847, 2973.386, 46.095), r: 99.11648 },
    { pos: new mp.Vector3(1905.9334, 2958.159, 45.767), r: 104.6267 },
    { pos: new mp.Vector3(-2188.127, 3292.788, 32.810), r: 191.2866 },
    { pos: new mp.Vector3(-163.1368, -397.407, 34.011), r: 140.6153 },
    { pos: new mp.Vector3(1854.9501, 2943.363, 45.706), r: 108.3346 },
    { pos: new mp.Vector3(1818.6791, 2931.805, 45.686), r: 109.3423 },
    { pos: new mp.Vector3(-169.4140, -416.337, 35.276), r: 160.3436 },
    { pos: new mp.Vector3(-2205.102, 3310.229, 32.979), r: 113.4765 },
    { pos: new mp.Vector3(1774.9259, 2914.183, 45.676), r: 111.9607 },
    { pos: new mp.Vector3(-174.7210, -431.428, 35.305), r: 160.3436 },
    { pos: new mp.Vector3(1704.8494, -1572.71, 112.59), r: 246.5981 },
    { pos: new mp.Vector3(-180.3556, -448.501, 35.488), r: 160.3436 },
    { pos: new mp.Vector3(1733.3095, 2892.954, 45.119), r: 118.7217 },
    { pos: new mp.Vector3(-185.3184, -462.654, 35.226), r: 160.3436 },
    { pos: new mp.Vector3(-188.2962, -471.145, 35.069), r: 160.3436 },
    { pos: new mp.Vector3(1697.0058, -1568.80, 112.59), r: 81.64445 },
    { pos: new mp.Vector3(1697.7004, 2871.348, 43.270), r: 122.4059 },
    { pos: new mp.Vector3(-2172.245, 3284.799, 32.810), r: 17.02322 },
    { pos: new mp.Vector3(1664.1314, 2850.432, 41.314), r: 123.4383 },
    { pos: new mp.Vector3(-237.5011, 306.5588, 95.850), r: 261.1363 },
    { pos: new mp.Vector3(1629.9991, 2828.677, 39.618), r: 124.7316 },
    { pos: new mp.Vector3(1678.5513, -1592.24, 112.44), r: 63.77829 },
    { pos: new mp.Vector3(1599.0810, 2807.492, 38.675), r: 123.7953 },
    { pos: new mp.Vector3(1676.6102, -1595.76, 112.43), r: 84.22480 },
    { pos: new mp.Vector3(1560.9906, 2781.577, 38.181), r: 123.8599 },
    { pos: new mp.Vector3(-231.0759, -901.007, 32.071), r: 7.422945 },
    { pos: new mp.Vector3(-231.9517, 294.2187, 92.186), r: 130.1680 },
    { pos: new mp.Vector3(1528.7414, 2759.217, 37.995), r: 124.7529 },
    { pos: new mp.Vector3(-2088.692, 3281.915, 40.580), r: 128.0259 },
    { pos: new mp.Vector3(-237.1282, -904.707, 32.013), r: 148.0659 },
    { pos: new mp.Vector3(1498.4572, 2738.924, 37.838), r: 123.3289 },
    { pos: new mp.Vector3(1484.0819, 2729.736, 37.718), r: 122.5837 },
    { pos: new mp.Vector3(1676.9639, -1606.95, 112.46), r: 103.0547 },
    { pos: new mp.Vector3(-240.1603, -904.376, 31.988), r: 101.7651 },
    { pos: new mp.Vector3(-2107.744, 3271.831, 38.732), r: 18.16950 },
    { pos: new mp.Vector3(1682.7514, -1619.63, 112.48), r: 152.8659 },
    { pos: new mp.Vector3(-246.7481, -901.275, 32.015), r: 181.7705 },
    { pos: new mp.Vector3(1469.0954, 2720.862, 37.602), r: 115.7937 },
    { pos: new mp.Vector3(-2137.930, 3316.217, 38.732), r: 145.3325 },
    { pos: new mp.Vector3(-244.3278, -903.177, 31.859), r: 21.09235 },
    { pos: new mp.Vector3(1421.6058, 2698.511, 37.491), r: 109.9483 },
    { pos: new mp.Vector3(1381.3847, 2685.488, 37.485), r: 106.7661 },
    { pos: new mp.Vector3(1680.9874, -1632.47, 112.40), r: 99.63195 },
    { pos: new mp.Vector3(-249.5995, -900.169, 32.015), r: 148.7551 },
    { pos: new mp.Vector3(1337.8096, 2677.674, 37.586), r: 92.30622 },
    { pos: new mp.Vector3(1286.7099, 2675.803, 37.555), r: 90.12567 },
    { pos: new mp.Vector3(-2168.743, 3268.057, 38.732), r: 312.7530 },
    { pos: new mp.Vector3(-252.5758, -899.601, 31.856), r: 104.0060 },
    { pos: new mp.Vector3(-2115.425, 3230.593, 32.810), r: 23.87794 },
    { pos: new mp.Vector3(152.49749, 146.1744, 105.65), r: 159.9967 },
    { pos: new mp.Vector3(1271.8085, 2675.379, 37.728), r: 32.29426 },
    { pos: new mp.Vector3(-459.5014, 6331.271, 13.075), r: 39.52166 },
    { pos: new mp.Vector3(1244.8266, 2675.802, 37.506), r: 92.40842 },
    { pos: new mp.Vector3(-2164.471, 3273.196, 32.810), r: 204.4059 },
    { pos: new mp.Vector3(1220.2285, 2675.726, 37.554), r: 89.25682 },
    { pos: new mp.Vector3(1183.7469, 2676.041, 37.728), r: 84.66630 },
    { pos: new mp.Vector3(1154.4448, 2677.442, 38.074), r: 88.47061 },
    { pos: new mp.Vector3(-2173.420, 3252.867, 32.810), r: 263.1398 },
    { pos: new mp.Vector3(1121.5100, 2678.496, 38.354), r: 87.82513 },
    { pos: new mp.Vector3(-387.0280, 6381.235, 14.089), r: 22.69117 },
    { pos: new mp.Vector3(1074.3833, 2681.895, 38.905), r: 86.69580 },
    { pos: new mp.Vector3(-289.5466, -1101.00, 24.112), r: 293.0941 },
    { pos: new mp.Vector3(213.50111, 96.65306, 106.07), r: 240.8270 },
    { pos: new mp.Vector3(1008.3410, 2685.682, 39.599), r: 87.06547 },
    { pos: new mp.Vector3(963.44628, 2687.938, 40.210), r: 86.57991 },
    { pos: new mp.Vector3(-297.9497, -1095.70, 23.997), r: 71.72493 },
    { pos: new mp.Vector3(-300.1267, -1096.46, 24.006), r: 171.1196 },
    { pos: new mp.Vector3(907.76867, 2690.418, 40.781), r: 88.16753 },
    { pos: new mp.Vector3(-303.9804, -1125.71, 23.543), r: 219.6092 },
    { pos: new mp.Vector3(848.03881, 2692.438, 40.656), r: 87.99127 },
    { pos: new mp.Vector3(-306.9561, -1126.34, 23.698), r: 80.55578 },
    { pos: new mp.Vector3(-314.3866, -1126.08, 24.251), r: 91.28443 },
    { pos: new mp.Vector3(785.71624, 2693.535, 40.133), r: 88.98122 },
    { pos: new mp.Vector3(-67.32754, 6566.079, 31.492), r: 127.0085 },
    { pos: new mp.Vector3(225.07456, 118.5655, 102.59), r: 127.5010 },
    { pos: new mp.Vector3(784.47998, 2706.183, 40.092), r: 220.5452 },
    { pos: new mp.Vector3(833.54479, 2704.943, 40.702), r: 266.3995 },
    { pos: new mp.Vector3(-2128.215, 3223.067, 32.810), r: 23.48459 },
    { pos: new mp.Vector3(-343.1435, -1122.70, 27.461), r: 78.85612 },
    { pos: new mp.Vector3(907.54559, 2702.057, 40.702), r: 265.8312 },
    { pos: new mp.Vector3(-349.5394, -1121.45, 28.031), r: 79.08901 },
    { pos: new mp.Vector3(-359.1296, -1120.28, 28.798), r: 79.89768 },
    { pos: new mp.Vector3(961.06292, 2700.167, 40.169), r: 267.3526 },
    { pos: new mp.Vector3(1021.1558, 2697.101, 39.373), r: 267.6041 },
    { pos: new mp.Vector3(-477.3666, -1124.88, 26.878), r: 95.53569 },
    { pos: new mp.Vector3(1070.8698, 2694.360, 38.904), r: 264.9472 },
    { pos: new mp.Vector3(1121.9151, 2691.386, 38.417), r: 268.2608 },
    { pos: new mp.Vector3(-488.9567, -1150.88, 22.289), r: 186.4629 },
    { pos: new mp.Vector3(1161.8350, 2690.008, 37.930), r: 265.5150 },
    { pos: new mp.Vector3(-469.5651, -1173.44, 22.317), r: 210.2633 },
    { pos: new mp.Vector3(1209.8273, 2688.191, 37.563), r: 270.5958 },
    { pos: new mp.Vector3(-2256.673, 3260.641, 32.810), r: 189.0267 },
    { pos: new mp.Vector3(-462.2831, -1213.29, 22.479), r: 183.1206 },
    { pos: new mp.Vector3(1246.1322, 2688.504, 37.524), r: 269.6575 },
    { pos: new mp.Vector3(-1147.429, -1988.28, 13.160), r: 351.5131 },
    { pos: new mp.Vector3(-465.3173, -1242.30, 24.362), r: 161.8651 },
    { pos: new mp.Vector3(1296.2241, 2688.185, 37.478), r: 272.3343 },
    { pos: new mp.Vector3(-2278.916, 3224.966, 32.810), r: 206.3759 },
    { pos: new mp.Vector3(-475.2276, -1268.76, 25.633), r: 159.1053 },
    { pos: new mp.Vector3(1332.2734, 2689.594, 37.607), r: 274.2936 },
    { pos: new mp.Vector3(-486.1584, -1291.64, 26.877), r: 156.3070 },
    { pos: new mp.Vector3(1384.3520, 2698.968, 37.598), r: 284.8597 },
    { pos: new mp.Vector3(-1136.923, -1998.26, 13.169), r: 317.2269 },
    { pos: new mp.Vector3(-499.1042, -1320.22, 28.598), r: 147.7941 },
    { pos: new mp.Vector3(1433.4090, 2716.391, 37.442), r: 294.9408 },
    { pos: new mp.Vector3(-509.1976, -1354.90, 29.387), r: 187.0462 },
    { pos: new mp.Vector3(-2244.614, 3284.083, 32.810), r: 236.1788 },
    { pos: new mp.Vector3(-496.2887, -1381.78, 29.514), r: 220.4138 },
    { pos: new mp.Vector3(1472.6883, 2737.252, 37.641), r: 197.4740 },
    { pos: new mp.Vector3(-477.4971, -1394.21, 29.729), r: 230.0662 },
    { pos: new mp.Vector3(-1104.745, -1970.98, 13.151), r: 288.0630 },
    { pos: new mp.Vector3(-2301.050, 3181.985, 32.810), r: 255.8734 },
    { pos: new mp.Vector3(1523.5158, 2769.868, 37.959), r: 301.9391 },
    { pos: new mp.Vector3(1490.1491, -1880.76, 71.712), r: 317.3124 },
    { pos: new mp.Vector3(-470.3102, -1381.33, 28.219), r: 353.3768 },
    { pos: new mp.Vector3(1569.7354, 2801.944, 38.374), r: 305.6840 },
    { pos: new mp.Vector3(-2292.633, 3193.893, 32.810), r: 214.0088 },
    { pos: new mp.Vector3(-1103.175, -2005.22, 13.136), r: 286.1318 },
    { pos: new mp.Vector3(-462.1134, -1365.49, 25.972), r: 350.8248 },
    { pos: new mp.Vector3(1613.8227, 2832.318, 39.276), r: 302.1625 },
    { pos: new mp.Vector3(-452.9663, -1363.71, 24.725), r: 284.8429 },
    { pos: new mp.Vector3(1455.5103, -1899.69, 71.952), r: 157.7884 },
    { pos: new mp.Vector3(-445.7681, -1357.84, 23.518), r: 5.334133 },
    { pos: new mp.Vector3(1647.2443, 2854.240, 40.604), r: 301.0817 },
    { pos: new mp.Vector3(-1040.561, -2062.60, 13.277), r: 144.8992 },
    { pos: new mp.Vector3(-453.4364, -1347.79, 24.683), r: 55.34280 },
    { pos: new mp.Vector3(1698.1788, 2886.088, 43.687), r: 303.0874 },
    { pos: new mp.Vector3(1457.6397, -1894.99, 90.905), r: 276.3475 },
    { pos: new mp.Vector3(1749.4411, 2914.994, 45.686), r: 295.9258 },
    { pos: new mp.Vector3(-2159.406, 3145.510, 32.810), r: 58.83883 },
    { pos: new mp.Vector3(-455.0355, -1336.68, 24.766), r: 357.6821 },
    { pos: new mp.Vector3(-2158.434, 3144.664, 32.810), r: 241.0674 },
    { pos: new mp.Vector3(1804.6823, 2940.002, 45.525), r: 287.3470 },
    { pos: new mp.Vector3(-454.6814, -1327.69, 24.712), r: 358.0594 },
    { pos: new mp.Vector3(1450.4676, -1892.22, 90.906), r: 51.65212 },
    { pos: new mp.Vector3(1863.8986, 2958.842, 45.736), r: 285.6005 },
    { pos: new mp.Vector3(-2143.971, 3168.277, 32.810), r: 267.2563 },
    { pos: new mp.Vector3(-448.1528, -1316.93, 24.009), r: 358.1039 },
    { pos: new mp.Vector3(-1049.769, -1919.63, 13.109), r: 53.92784 },
    { pos: new mp.Vector3(247.28126, 127.5171, 106.39), r: 252.0172 },
    { pos: new mp.Vector3(1902.6262, 2970.085, 45.680), r: 286.3475 },
    { pos: new mp.Vector3(-2146.061, 3169.975, 32.810), r: 58.10242 },
    { pos: new mp.Vector3(-449.2475, -1303.47, 24.140), r: 357.1260 },
    { pos: new mp.Vector3(1967.4194, 2985.022, 45.689), r: 280.8362 },
    { pos: new mp.Vector3(-456.7227, -1299.40, 24.826), r: 87.28464 },
    { pos: new mp.Vector3(1291.2924, -1916.26, 43.263), r: 12.07552 },
    { pos: new mp.Vector3(-997.8493, -1849.63, 18.123), r: 227.9535 },
    { pos: new mp.Vector3(270.02633, 133.8661, 103.67), r: 148.4869 },
    { pos: new mp.Vector3(-467.7477, -1294.00, 25.608), r: 5.448708 },
    { pos: new mp.Vector3(-463.2487, -1282.20, 25.047), r: 319.1789 },
    { pos: new mp.Vector3(1291.8007, -1932.49, 43.263), r: 169.3494 },
    { pos: new mp.Vector3(-941.5447, -1817.65, 19.806), r: 22.92619 },
    { pos: new mp.Vector3(-1964.254, 3029.516, 32.810), r: 238.0389 },
    { pos: new mp.Vector3(271.34704, 85.09203, 111.36), r: 192.7041 },
    { pos: new mp.Vector3(-454.6392, -1266.24, 24.334), r: 337.4115 },
    { pos: new mp.Vector3(-859.4358, -1707.04, 19.054), r: 223.5847 },
    { pos: new mp.Vector3(-444.3233, -1263.66, 23.489), r: 258.9225 },
    { pos: new mp.Vector3(1264.9711, -1956.11, 43.263), r: 288.4028 },
    { pos: new mp.Vector3(-732.1968, -1584.50, 14.467), r: 40.31360 },
    { pos: new mp.Vector3(-437.2236, -1272.27, 22.288), r: 200.0138 },
    { pos: new mp.Vector3(-1918.660, 3051.970, 32.809), r: 242.2152 },
    { pos: new mp.Vector3(-441.1843, -1276.34, 23.038), r: 126.7240 },
    { pos: new mp.Vector3(1256.9207, -1966.51, 43.263), r: 256.3140 },
    { pos: new mp.Vector3(56.831577, 6502.692, 31.541), r: 315.0746 },
    { pos: new mp.Vector3(-444.8877, -1288.95, 23.698), r: 205.3591 },
    { pos: new mp.Vector3(-1884.296, 3032.177, 32.810), r: 237.4315 },
    { pos: new mp.Vector3(-438.2175, -1295.43, 22.575), r: 229.7128 },
    { pos: new mp.Vector3(-639.8623, -1484.06, 10.646), r: 83.43756 },
    { pos: new mp.Vector3(-435.5200, -1306.91, 22.266), r: 174.7304 },
    { pos: new mp.Vector3(-436.6373, -1325.59, 22.632), r: 185.3737 },
    { pos: new mp.Vector3(1250.5228, -1962.19, 43.263), r: 99.13133 },
    { pos: new mp.Vector3(-436.1177, -1373.03, 22.306), r: 182.4092 },
    { pos: new mp.Vector3(1248.3980, -1969.79, 43.264), r: 95.96551 },
    { pos: new mp.Vector3(-435.2173, -1402.95, 22.080), r: 178.3567 },
    { pos: new mp.Vector3(-435.7203, -1436.52, 22.269), r: 180.1279 },
    { pos: new mp.Vector3(-666.3418, -1442.79, 10.632), r: 267.9500 },
    { pos: new mp.Vector3(-1835.900, 3028.285, 32.810), r: 30.76451 },
    { pos: new mp.Vector3(2045.7607, 3169.878, 45.253), r: 118.7248 },
    { pos: new mp.Vector3(-448.4495, -1456.57, 21.918), r: 136.1392 },
    { pos: new mp.Vector3(75.202354, 6354.290, 31.375), r: 86.11325 },
    { pos: new mp.Vector3(1250.6876, -1993.58, 43.739), r: 85.83845 },
    { pos: new mp.Vector3(-486.2409, -1456.34, 19.032), r: 77.32572 },
    { pos: new mp.Vector3(-1844.333, 3082.377, 32.821), r: 187.9614 },
    { pos: new mp.Vector3(-683.0369, -1654.56, 24.831), r: 297.9210 },
    { pos: new mp.Vector3(2037.4008, 3179.983, 45.236), r: 200.1002 },
    { pos: new mp.Vector3(-535.3052, -1431.55, 18.818), r: 111.9871 },
    { pos: new mp.Vector3(-1734.914, 3021.183, 32.830), r: 27.51359 },
    { pos: new mp.Vector3(-643.4916, -1665.70, 25.311), r: 101.1764 },
    { pos: new mp.Vector3(-624.1122, -1446.20, 10.558), r: 108.4805 },
    { pos: new mp.Vector3(2028.7934, 3165.135, 45.351), r: 144.5575 },
    { pos: new mp.Vector3(-683.8930, -1771.52, 24.877), r: 236.6647 },
    { pos: new mp.Vector3(55.873538, 6340.336, 31.364), r: 334.7055 },
    { pos: new mp.Vector3(-1733.336, 3117.448, 32.858), r: 131.0877 },
    { pos: new mp.Vector3(-725.7235, -1639.56, 25.769), r: 113.4840 },
    { pos: new mp.Vector3(-1501.569, 2734.186, 15.894), r: 220.2648 },
    { pos: new mp.Vector3(1542.7189, -2146.87, 77.485), r: 88.67777 },
    { pos: new mp.Vector3(-731.9427, -1651.44, 26.584), r: 177.7114 },
    { pos: new mp.Vector3(2042.9482, 3162.601, 45.264), r: 130.7181 },
    { pos: new mp.Vector3(-735.1445, -1671.77, 27.700), r: 162.6227 },
    { pos: new mp.Vector3(-1470.479, 2658.321, 2.4710), r: 47.23997 },
    { pos: new mp.Vector3(-1565.610, 2693.697, 4.0520), r: 52.31301 },
    { pos: new mp.Vector3(1537.8852, -2135.69, 77.140), r: 170.7361 },
    { pos: new mp.Vector3(-732.9740, -1691.35, 28.046), r: 218.4819 },
    { pos: new mp.Vector3(-1611.644, 2782.234, 17.411), r: 12.51352 },
    { pos: new mp.Vector3(-588.9765, -1809.65, 23.273), r: 143.1586 },
    { pos: new mp.Vector3(321.35104, 80.22083, 105.28), r: 271.4362 },
    { pos: new mp.Vector3(2053.8957, 3174.694, 45.168), r: 347.1957 },
    { pos: new mp.Vector3(-714.7509, -1692.60, 27.002), r: 275.4532 },
    { pos: new mp.Vector3(-1617.307, 2727.443, 6.3619), r: 193.7067 },
    { pos: new mp.Vector3(-1026.817, -2743.52, 20.169), r: 19.12310 },
    { pos: new mp.Vector3(2055.5317, 3186.136, 45.186), r: 69.87792 },
    { pos: new mp.Vector3(1520.8040, -2113.96, 76.867), r: 287.2528 },
    { pos: new mp.Vector3(371.95849, 89.86164, 112.84), r: 307.7538 },
    { pos: new mp.Vector3(2048.7729, 3189.625, 45.174), r: 103.9409 },
    { pos: new mp.Vector3(394.80984, 82.16240, 112.84), r: 108.9048 },
    { pos: new mp.Vector3(-1046.198, -2744.92, 21.359), r: 329.4416 },
    { pos: new mp.Vector3(1522.5650, -2091.66, 77.016), r: 243.1469 },
    { pos: new mp.Vector3(407.25567, -1733.03, 29.287), r: 311.2318 },
    { pos: new mp.Vector3(1538.6669, -2108.19, 77.068), r: 37.79284 },
    { pos: new mp.Vector3(409.82135, -1730.13, 29.332), r: 330.1562 },
    { pos: new mp.Vector3(1967.0750, 3075.150, 46.902), r: 17.27596 },
    { pos: new mp.Vector3(-2343.409, 3273.468, 32.976), r: 353.9375 },
    { pos: new mp.Vector3(418.59060, -1719.64, 29.254), r: 311.3124 },
    { pos: new mp.Vector3(423.33221, -1715.03, 29.276), r: 314.8979 },
    { pos: new mp.Vector3(-2334.509, 3307.741, 32.827), r: 225.9670 },
    { pos: new mp.Vector3(428.55392, -1708.67, 29.251), r: 329.2472 },
    { pos: new mp.Vector3(1552.7131, -2092.09, 77.167), r: 52.79455 },
    { pos: new mp.Vector3(1949.0892, 3061.143, 46.782), r: 52.63867 },
    { pos: new mp.Vector3(435.11053, -1700.15, 29.282), r: 320.3411 },
    { pos: new mp.Vector3(-2326.270, 3305.112, 32.827), r: 200.3725 },
    { pos: new mp.Vector3(-1040.064, -2748.51, 21.359), r: 330.7051 },
    { pos: new mp.Vector3(443.79800, -1690.10, 29.256), r: 318.7671 },
    { pos: new mp.Vector3(1937.7399, 3041.831, 46.280), r: 88.56743 },
    { pos: new mp.Vector3(448.37460, -1684.90, 29.308), r: 318.7619 },
    { pos: new mp.Vector3(-2315.656, 3303.015, 32.827), r: 245.8725 },
    { pos: new mp.Vector3(1929.9641, 3020.171, 45.879), r: 181.9223 },
    { pos: new mp.Vector3(1553.5698, -2069.41, 77.123), r: 44.69495 },
    { pos: new mp.Vector3(-2307.305, 3304.286, 32.826), r: 248.3979 },
    { pos: new mp.Vector3(1934.9281, 2992.169, 45.724), r: 149.4152 },
    { pos: new mp.Vector3(-1048.629, -2732.04, 20.169), r: 273.8637 },
    { pos: new mp.Vector3(-2298.785, 3306.721, 32.826), r: 252.4284 },
    { pos: new mp.Vector3(-273.2489, 6642.026, 7.3805), r: 224.7763 },
    { pos: new mp.Vector3(-2292.880, 3308.801, 32.826), r: 219.7371 },
    { pos: new mp.Vector3(-1062.367, -2720.58, 20.169), r: 332.6851 },
    { pos: new mp.Vector3(1190.4243, -469.923, 66.330), r: 39.05385 },
    { pos: new mp.Vector3(1763.9144, 3058.256, 61.887), r: 127.1090 },
    { pos: new mp.Vector3(1191.5352, -462.904, 66.567), r: 358.7411 },
    { pos: new mp.Vector3(1398.3891, -2071.01, 51.998), r: 57.44503 },
    { pos: new mp.Vector3(1193.4725, -452.831, 66.841), r: 340.8801 },
    { pos: new mp.Vector3(1764.7440, 3047.815, 62.360), r: 154.5521 },
    { pos: new mp.Vector3(-1011.901, -2748.56, 20.169), r: 187.6896 },
    { pos: new mp.Vector3(1197.5781, -436.122, 67.309), r: 345.8809 },
    { pos: new mp.Vector3(1758.4970, 3042.360, 62.388), r: 25.06549 },
    { pos: new mp.Vector3(1396.4772, -2049.61, 51.998), r: 156.8904 },
    { pos: new mp.Vector3(1199.2980, -429.249, 67.510), r: 346.0686 },
    { pos: new mp.Vector3(1745.3275, 3041.660, 61.695), r: 56.49313 },
    { pos: new mp.Vector3(1201.8637, -418.841, 67.792), r: 346.1228 },
    { pos: new mp.Vector3(1381.3231, -2043.62, 51.998), r: 67.87833 },
    { pos: new mp.Vector3(-2132.847, 3519.383, 74.173), r: 132.1571 },
    { pos: new mp.Vector3(1719.3581, 3049.909, 59.090), r: 11.29616 },
    { pos: new mp.Vector3(1372.6322, -2049.54, 51.998), r: 30.59555 },
    { pos: new mp.Vector3(-2127.992, 3592.941, 103.64), r: 96.20906 },
    { pos: new mp.Vector3(-956.9818, -2753.74, 20.169), r: 14.45154 },
    { pos: new mp.Vector3(1706.2950, 3066.929, 55.370), r: 38.51490 },
    { pos: new mp.Vector3(1200.5269, -351.451, 69.132), r: 0.793445 },
    { pos: new mp.Vector3(405.41448, 6639.511, 28.324), r: 125.1129 },
    { pos: new mp.Vector3(1693.6416, 3095.128, 49.623), r: 22.80708 },
    { pos: new mp.Vector3(1196.3696, -345.124, 69.177), r: 49.83387 },
    { pos: new mp.Vector3(1683.7590, 3116.584, 45.026), r: 27.81417 },
    { pos: new mp.Vector3(-2149.040, 3697.813, 146.10), r: 85.63459 },
    { pos: new mp.Vector3(1191.5889, -343.637, 69.407), r: 100.9268 },
    { pos: new mp.Vector3(1361.5709, -2030.22, 52.000), r: 28.58914 },
    { pos: new mp.Vector3(1369.2207, -2023.83, 52.077), r: 358.4956 },
    { pos: new mp.Vector3(-2029.907, 3791.053, 198.55), r: 180.8919 },
    { pos: new mp.Vector3(1692.7788, 3121.289, 46.061), r: 252.4897 },
    { pos: new mp.Vector3(1180.3659, -346.400, 69.305), r: 159.6016 },
    { pos: new mp.Vector3(-910.1762, -2725.48, 20.169), r: 54.20226 },
    { pos: new mp.Vector3(1700.0865, 3102.348, 50.288), r: 206.5573 },
    { pos: new mp.Vector3(-1942.656, 3897.012, 235.72), r: 94.88278 },
    { pos: new mp.Vector3(1185.8741, -350.406, 69.322), r: 246.9255 },
    { pos: new mp.Vector3(1716.7274, 3064.398, 56.670), r: 207.1072 },
    { pos: new mp.Vector3(1750.9619, 3064.786, 61.246), r: 298.6380 },
    { pos: new mp.Vector3(-1825.376, 3981.734, 273.87), r: 80.11518 },
    { pos: new mp.Vector3(962.73931, -1812.88, 31.080), r: 51.38023 },
    { pos: new mp.Vector3(1766.0119, 3059.300, 62.098), r: 241.9742 },
    { pos: new mp.Vector3(-883.9004, -2682.37, 20.169), r: 55.20520 },
    { pos: new mp.Vector3(-1714.734, 3966.759, 299.64), r: 79.63148 },
    { pos: new mp.Vector3(961.30590, -1818.62, 31.228), r: 184.1983 },
    { pos: new mp.Vector3(-1604.190, 3921.150, 342.31), r: 129.6926 },
    { pos: new mp.Vector3(-1370.786, 3870.520, 428.36), r: 164.6137 },
    { pos: new mp.Vector3(-545.4163, -1824.53, 23.032), r: 246.7492 },
    { pos: new mp.Vector3(-1219.783, 3852.105, 488.78), r: 260.0222 },
    { pos: new mp.Vector3(960.35968, -1836.04, 31.476), r: 112.4754 },
    { pos: new mp.Vector3(-1208.200, 3847.756, 489.06), r: 286.5016 },
    { pos: new mp.Vector3(-840.2253, -2586.45, 13.964), r: 237.8820 },
    { pos: new mp.Vector3(-1193.180, 3859.649, 489.03), r: 251.0653 },
    { pos: new mp.Vector3(-1180.323, 3861.881, 488.55), r: 218.5038 },
    { pos: new mp.Vector3(-1171.772, 3842.190, 486.59), r: 166.9390 },
    { pos: new mp.Vector3(-694.2783, -2038.05, 8.8903), r: 194.7093 },
    { pos: new mp.Vector3(-1173.620, 3824.694, 483.71), r: 306.1932 },
    { pos: new mp.Vector3(-56.23862, 58.35219, 72.296), r: 204.5258 },
    { pos: new mp.Vector3(-1155.464, 3836.128, 482.14), r: 51.62040 },
    { pos: new mp.Vector3(-833.8706, -2576.61, 13.962), r: 242.0301 },
    { pos: new mp.Vector3(-727.0150, -1957.23, 8.2314), r: 30.71782 },
    { pos: new mp.Vector3(-1079.875, 3843.397, 440.00), r: 300.4660 },
    { pos: new mp.Vector3(-1020.309, 3829.637, 434.56), r: 275.6698 },
    { pos: new mp.Vector3(-828.4388, -2559.20, 13.964), r: 242.1080 },
    { pos: new mp.Vector3(-965.9892, 3815.226, 430.07), r: 315.6755 },
    { pos: new mp.Vector3(-806.2646, -1981.73, 9.2635), r: 217.0106 },
    { pos: new mp.Vector3(2020.7503, 3067.011, 47.050), r: 91.31639 },
    { pos: new mp.Vector3(-818.7016, -2540.67, 13.964), r: 253.6762 },
    { pos: new mp.Vector3(-795.4655, -2537.45, 13.795), r: 61.13007 },
    { pos: new mp.Vector3(1959.0391, 3096.346, 46.939), r: 201.9429 },
    { pos: new mp.Vector3(-2314.208, 3260.553, 32.828), r: 109.3755 },
    { pos: new mp.Vector3(943.31463, -1801.98, 31.210), r: 202.1006 },
    { pos: new mp.Vector3(-21.24109, 6637.882, 30.861), r: 210.0135 },
    { pos: new mp.Vector3(1951.9389, 3083.052, 46.832), r: 242.1066 },
    { pos: new mp.Vector3(-809.6841, -2031.66, 9.5377), r: 355.7664 },
    { pos: new mp.Vector3(-813.0684, -2567.82, 13.756), r: 65.79166 },
    { pos: new mp.Vector3(930.25262, -1809.44, 29.729), r: 236.2178 },
    { pos: new mp.Vector3(1940.6878, 3066.307, 46.876), r: 319.6450 },
    { pos: new mp.Vector3(-829.2280, -2595.07, 13.756), r: 60.68243 },
    { pos: new mp.Vector3(-916.6082, -2039.74, 9.4049), r: 227.8750 },
    { pos: new mp.Vector3(-2320.628, 3266.698, 32.827), r: 343.9491 },
    { pos: new mp.Vector3(1932.1406, 3047.202, 46.352), r: 239.7002 },
    { pos: new mp.Vector3(-908.6256, -2036.96, 9.4310), r: 197.2124 },
    { pos: new mp.Vector3(-789.9036, -2526.55, 13.863), r: 65.08684 },
    { pos: new mp.Vector3(973.93804, -1831.32, 31.280), r: 353.4508 },
    { pos: new mp.Vector3(1935.8966, 3059.306, 46.588), r: 251.9634 },
    { pos: new mp.Vector3(979.72937, -1831.78, 31.346), r: 256.3059 },
    { pos: new mp.Vector3(-2352.099, 3241.739, 32.989), r: 322.9694 },
    { pos: new mp.Vector3(-774.8494, -2498.62, 14.051), r: 66.64764 },
    { pos: new mp.Vector3(981.58276, -1812.25, 31.317), r: 177.2843 },
    { pos: new mp.Vector3(-2431.196, 3277.288, 32.977), r: 25.73986 },
    { pos: new mp.Vector3(-2418.301, 3269.690, 32.975), r: 288.1796 },
    { pos: new mp.Vector3(986.38983, -1779.61, 31.217), r: 358.3252 },
    { pos: new mp.Vector3(992.73803, -1777.65, 31.504), r: 282.1646 },
    { pos: new mp.Vector3(-765.0241, -2482.76, 14.071), r: 65.85719 },
    { pos: new mp.Vector3(-884.3593, -2038.77, 9.4663), r: 137.0575 },
    { pos: new mp.Vector3(-2416.229, 3317.742, 32.830), r: 237.1853 },
    { pos: new mp.Vector3(1054.4351, -1788.37, 35.661), r: 332.7036 },
    { pos: new mp.Vector3(310.74176, 6496.894, 29.363), r: 125.2527 },
    { pos: new mp.Vector3(-2413.816, 3323.233, 32.829), r: 250.1557 },
    { pos: new mp.Vector3(-942.6812, -2158.86, 8.9911), r: 127.0334 },
    { pos: new mp.Vector3(-758.8469, -2469.58, 14.240), r: 57.49618 },
    { pos: new mp.Vector3(301.18911, 6496.183, 29.705), r: 227.9674 },
    { pos: new mp.Vector3(-2409.563, 3328.156, 32.829), r: 324.0505 },
    { pos: new mp.Vector3(1161.3780, -1670.70, 36.147), r: 346.7832 },
    { pos: new mp.Vector3(-893.0411, -2177.61, 8.7848), r: 183.3160 },
    { pos: new mp.Vector3(-750.5552, -2454.40, 14.463), r: 38.31472 },
    { pos: new mp.Vector3(301.27905, 6489.595, 29.734), r: 315.4464 },
    { pos: new mp.Vector3(-2406.184, 3333.580, 32.828), r: 256.8345 },
    { pos: new mp.Vector3(2055.1501, 3392.309, 45.299), r: 183.2238 },
    { pos: new mp.Vector3(310.27545, 6488.872, 29.412), r: 46.78003 },
    { pos: new mp.Vector3(1187.2006, -1635.49, 42.135), r: 275.2667 },
    { pos: new mp.Vector3(-741.1943, -2438.93, 14.596), r: 48.70063 },
    { pos: new mp.Vector3(-855.0082, -2246.22, 6.9472), r: 106.5137 },
    { pos: new mp.Vector3(2053.0917, 3422.049, 44.324), r: 355.1764 },
    { pos: new mp.Vector3(-1901.588, 2792.927, 32.734), r: 295.8695 },
    { pos: new mp.Vector3(1194.4315, -1631.95, 43.657), r: 169.4470 },
    { pos: new mp.Vector3(-1842.246, 2777.052, 32.806), r: 314.4433 },
    { pos: new mp.Vector3(2044.2495, 3447.663, 43.825), r: 47.31134 },
    { pos: new mp.Vector3(-729.9856, -2421.85, 14.597), r: 62.17521 },
    { pos: new mp.Vector3(1198.1838, -1624.32, 44.853), r: 197.1260 },
    { pos: new mp.Vector3(2035.8072, 3434.498, 44.108), r: 181.7916 },
    { pos: new mp.Vector3(-1823.394, 2816.996, 32.936), r: 211.8145 },
    { pos: new mp.Vector3(416.18435, 6471.078, 28.810), r: 51.27353 },
    { pos: new mp.Vector3(2042.9097, 3414.839, 44.397), r: 207.4190 },
    { pos: new mp.Vector3(-1798.917, 2858.214, 32.936), r: 305.1204 },
    { pos: new mp.Vector3(-722.0300, -2412.32, 14.648), r: 53.13654 },
    { pos: new mp.Vector3(1178.4338, -1613.03, 44.853), r: 205.9419 },
    { pos: new mp.Vector3(2051.1264, 3394.937, 45.068), r: 196.2003 },
    { pos: new mp.Vector3(-1774.508, 2899.521, 32.891), r: 317.0418 },
    { pos: new mp.Vector3(-1754.377, 2936.348, 32.958), r: 277.4721 },
    { pos: new mp.Vector3(-875.1828, -2260.04, 6.7090), r: 58.61853 },
    { pos: new mp.Vector3(-747.5808, -2408.39, 14.610), r: 237.8363 },
    { pos: new mp.Vector3(1175.7958, -1625.55, 41.461), r: 229.5940 },
    { pos: new mp.Vector3(-737.1956, -2391.96, 14.837), r: 291.3775 },
    { pos: new mp.Vector3(-927.2654, -2331.98, 6.7090), r: 223.2626 },
    { pos: new mp.Vector3(-769.3807, -2360.18, 14.837), r: 316.2565 },
    { pos: new mp.Vector3(-780.5128, -2349.09, 14.945), r: 299.9412 },
    { pos: new mp.Vector3(1177.1584, -1576.94, 34.842), r: 214.4100 },
    { pos: new mp.Vector3(-146.4988, 6213.611, 31.278), r: 51.95883 },
    { pos: new mp.Vector3(-903.8427, -2245.96, 6.7090), r: 184.1274 },
    { pos: new mp.Vector3(1188.8857, -1577.39, 34.692), r: 87.83504 },
    { pos: new mp.Vector3(-811.8764, -2316.91, 13.524), r: 314.1758 },
    { pos: new mp.Vector3(-2421.490, 3796.832, 22.677), r: 57.19804 },
    { pos: new mp.Vector3(-2462.144, 3826.760, 18.709), r: 282.6149 },
    { pos: new mp.Vector3(-2427.754, 3984.218, 17.610), r: 273.5811 },
    { pos: new mp.Vector3(-851.3328, -2277.66, 7.8893), r: 315.8016 },
    { pos: new mp.Vector3(-764.9301, -2358.97, 15.004), r: 15.08818 },
    { pos: new mp.Vector3(-220.0904, 6172.762, 31.331), r: 219.5994 },
    { pos: new mp.Vector3(-2474.275, 4087.667, 9.4483), r: 195.7395 },
    { pos: new mp.Vector3(1194.3642, -1516.06, 34.848), r: 2.353431 },
    { pos: new mp.Vector3(-823.1295, -2276.65, 9.0922), r: 141.4763 },
    { pos: new mp.Vector3(-2407.215, 4146.039, 18.940), r: 35.90662 },
    { pos: new mp.Vector3(1183.6021, -1515.13, 34.849), r: 306.4425 },
    { pos: new mp.Vector3(-2406.010, 4241.282, 10.192), r: 28.63431 },
    { pos: new mp.Vector3(-685.5064, -2391.66, 13.944), r: 8.934406 },
    { pos: new mp.Vector3(-2363.188, 4222.783, 25.179), r: 63.32467 },
    { pos: new mp.Vector3(-2330.221, 4284.569, 28.869), r: 281.1136 },
    { pos: new mp.Vector3(-2311.602, 4304.641, 30.067), r: 290.0986 },
    { pos: new mp.Vector3(-788.7717, -2304.09, 14.054), r: 166.1996 },
    { pos: new mp.Vector3(-667.7416, -2370.66, 13.944), r: 108.6003 },
    { pos: new mp.Vector3(-2298.983, 4334.817, 16.648), r: 43.35034 },
    { pos: new mp.Vector3(2087.6501, 3672.880, 38.306), r: 299.7478 },
    { pos: new mp.Vector3(-2304.948, 4395.006, 8.8161), r: 171.1889 },
    { pos: new mp.Vector3(2095.3312, 3676.243, 38.461), r: 353.8716 },
    { pos: new mp.Vector3(-2316.219, 4384.645, 7.8476), r: 207.9614 },
    { pos: new mp.Vector3(-619.9353, -2298.07, 13.828), r: 217.9471 },
    { pos: new mp.Vector3(-768.4113, -2323.40, 14.845), r: 135.9404 },
    { pos: new mp.Vector3(1156.6865, -1463.12, 34.843), r: 68.24435 },
    { pos: new mp.Vector3(2097.7399, 3671.759, 38.618), r: 205.3338 },
    { pos: new mp.Vector3(-2313.749, 4383.070, 8.3580), r: 81.60220 },
    { pos: new mp.Vector3(1168.6038, -1454.95, 34.828), r: 315.2078 },
    { pos: new mp.Vector3(2090.6037, 3667.540, 38.458), r: 180.3143 },
    { pos: new mp.Vector3(-2316.581, 4382.335, 7.9733), r: 335.2453 },
    { pos: new mp.Vector3(1186.3366, -1456.84, 34.900), r: 350.8036 },
    { pos: new mp.Vector3(-753.1849, -2349.26, 14.966), r: 187.6056 },
    { pos: new mp.Vector3(2110.0068, 3638.613, 37.756), r: 220.2850 },
    { pos: new mp.Vector3(-2320.544, 4374.590, 7.9641), r: 211.3202 },
    { pos: new mp.Vector3(1198.5617, -1462.98, 34.846), r: 10.07357 },
    { pos: new mp.Vector3(-825.1015, -2589.27, 13.756), r: 71.67915 },
    { pos: new mp.Vector3(1207.1101, -1462.49, 34.842), r: 0.501961 },
    { pos: new mp.Vector3(2122.7639, 3632.794, 38.038), r: 66.65849 },
    { pos: new mp.Vector3(-2319.706, 4371.937, 8.2724), r: 5.138883 },
    { pos: new mp.Vector3(-737.7639, -2365.70, 15.008), r: 135.8057 },
    { pos: new mp.Vector3(1224.9500, -1457.57, 34.991), r: 307.6146 },
    { pos: new mp.Vector3(-2315.055, 4397.212, 6.9136), r: 243.6742 },
    { pos: new mp.Vector3(-845.5653, -2555.65, 13.756), r: 222.7237 },
    { pos: new mp.Vector3(1238.3591, -1459.50, 34.963), r: 322.4735 },
    { pos: new mp.Vector3(-724.1426, -2378.25, 15.008), r: 149.8919 },
    { pos: new mp.Vector3(-2299.926, 4388.151, 10.722), r: 94.03147 },
    { pos: new mp.Vector3(-861.3330, -2581.22, 13.756), r: 294.6680 },
    { pos: new mp.Vector3(-2251.447, 4505.291, 2.2110), r: 41.93114 },
    { pos: new mp.Vector3(-714.5762, -2375.62, 15.008), r: 260.5783 },
    { pos: new mp.Vector3(-2164.869, 4603.867, 2.3822), r: 297.4317 },
    { pos: new mp.Vector3(1558.5698, 3576.997, 35.436), r: 298.8027 },
    { pos: new mp.Vector3(-769.5062, 5513.589, 34.864), r: 160.0922 },
    { pos: new mp.Vector3(-2017.335, 4555.077, 4.6590), r: 303.0452 },
    { pos: new mp.Vector3(1561.1632, 3564.826, 35.436), r: 329.8144 },
    { pos: new mp.Vector3(-1953.088, 4578.162, 3.4794), r: 260.7241 },
    { pos: new mp.Vector3(-875.9389, -2648.96, 13.961), r: 93.33872 },
    { pos: new mp.Vector3(1273.2583, -1524.91, 43.438), r: 291.3191 },
    { pos: new mp.Vector3(-700.1130, -2359.01, 15.008), r: 225.2111 },
    { pos: new mp.Vector3(-1916.906, 4534.402, 12.842), r: 288.5322 },
    { pos: new mp.Vector3(-1899.581, 4490.105, 28.390), r: 198.3990 },
    { pos: new mp.Vector3(-1917.719, 4468.073, 32.659), r: 98.88939 },
    { pos: new mp.Vector3(1574.4273, 3578.182, 35.436), r: 98.52574 },
    { pos: new mp.Vector3(-775.5662, 5592.847, 33.623), r: 201.5379 },
    { pos: new mp.Vector3(-1946.225, 4466.169, 33.929), r: 126.8265 },
    { pos: new mp.Vector3(1270.0153, -1565.93, 53.610), r: 352.6071 },
    { pos: new mp.Vector3(-766.9041, 5585.940, 33.605), r: 84.59974 },
    { pos: new mp.Vector3(-1947.573, 4454.721, 35.753), r: 88.75193 },
    { pos: new mp.Vector3(1285.6553, -1571.62, 50.940), r: 246.4068 },
    { pos: new mp.Vector3(1283.9390, -1587.14, 51.752), r: 54.52708 },
    { pos: new mp.Vector3(-766.9039, 5580.913, 33.605), r: 95.95691 },
    { pos: new mp.Vector3(-1968.924, 4463.449, 39.103), r: 267.4029 },
    { pos: new mp.Vector3(1279.2729, -1589.68, 52.062), r: 49.98988 },
    { pos: new mp.Vector3(-1994.774, 4476.714, 50.898), r: 210.6792 },
    { pos: new mp.Vector3(1283.4145, -1600.63, 54.225), r: 6.244649 },
    { pos: new mp.Vector3(-1095.454, -2605.98, 20.169), r: 252.4279 },
    { pos: new mp.Vector3(-1991.535, 4394.060, 65.794), r: 320.3228 },
    { pos: new mp.Vector3(1288.4942, -1599.55, 54.203), r: 18.18028 },
    { pos: new mp.Vector3(-884.1650, -2244.72, 5.9890), r: 323.9783 },
    { pos: new mp.Vector3(-708.0094, 5789.419, 17.330), r: 68.93465 },
    { pos: new mp.Vector3(-1862.234, 4438.697, 47.841), r: 324.1554 },
    { pos: new mp.Vector3(-1074.834, -2571.04, 20.169), r: 236.4916 },
    { pos: new mp.Vector3(1289.9038, -1625.32, 54.586), r: 270.3225 },
    { pos: new mp.Vector3(-1826.386, 4456.393, 40.141), r: 293.7439 },
    { pos: new mp.Vector3(-1760.984, 4441.150, 27.744), r: 354.5646 },
    { pos: new mp.Vector3(-1019.030, -2474.12, 20.169), r: 242.4764 },
    { pos: new mp.Vector3(-1759.122, 4471.188, 6.7954), r: 90.24756 },
    { pos: new mp.Vector3(1312.4392, -1649.41, 52.145), r: 301.2912 },
    { pos: new mp.Vector3(-700.3342, 5807.420, 17.324), r: 64.24974 },
    { pos: new mp.Vector3(-961.4157, -2502.29, 14.270), r: 67.87220 },
    { pos: new mp.Vector3(-1675.895, 4437.507, 5.5521), r: 1.147949 },
    { pos: new mp.Vector3(-702.6042, 5802.493, 17.327), r: 65.70426 },
    { pos: new mp.Vector3(-955.5741, -2490.84, 14.269), r: 51.98321 },
    { pos: new mp.Vector3(1276.4307, -1631.92, 53.340), r: 177.8997 },
    { pos: new mp.Vector3(-994.7481, -2439.51, 13.778), r: 292.2972 },
    { pos: new mp.Vector3(1265.2453, -1639.79, 53.228), r: 133.7483 },
    { pos: new mp.Vector3(-943.9798, -2460.12, 13.980), r: 264.3890 },
    { pos: new mp.Vector3(-690.0297, 5829.245, 17.315), r: 58.18246 },
    { pos: new mp.Vector3(-928.1512, -2455.45, 13.980), r: 132.0238 },
    { pos: new mp.Vector3(-1035.589, -2501.79, 13.944), r: 240.7167 },
    { pos: new mp.Vector3(1220.7093, -1663.55, 47.770), r: 162.0900 },
    { pos: new mp.Vector3(-1052.799, -2530.35, 13.944), r: 190.2057 },
    { pos: new mp.Vector3(1224.3872, -1662.27, 47.770), r: 200.6831 },
    { pos: new mp.Vector3(-901.0793, -2459.78, 14.160), r: 97.00935 },
    { pos: new mp.Vector3(232.69918, -420.004, 124.98), r: 20.96569 },
    { pos: new mp.Vector3(-897.3716, -2471.43, 13.990), r: 344.6720 },
    { pos: new mp.Vector3(-1672.956, 4459.895, 1.8849), r: 259.0401 },
    { pos: new mp.Vector3(-1073.716, -2569.05, 13.926), r: 246.5800 },
    { pos: new mp.Vector3(-675.5744, 5821.583, 17.330), r: 62.67519 },
    { pos: new mp.Vector3(-1654.228, 4451.404, 1.9773), r: 38.02843 },
    { pos: new mp.Vector3(74.513572, -338.837, 67.197), r: 73.15132 },
    { pos: new mp.Vector3(-882.2780, -2480.96, 13.989), r: 290.9210 },
    { pos: new mp.Vector3(-1642.773, 4481.719, 3.3791), r: 141.6554 },
    { pos: new mp.Vector3(1250.1600, -1737.80, 51.637), r: 221.6129 },
    { pos: new mp.Vector3(-1092.564, -2597.27, 13.769), r: 261.6414 },
    { pos: new mp.Vector3(-679.9367, 5812.899, 17.330), r: 7.876650 },
    { pos: new mp.Vector3(-1664.243, 4493.218, 1.8839), r: 199.4716 },
    { pos: new mp.Vector3(-852.1484, -2497.86, 13.980), r: 202.2040 },
    { pos: new mp.Vector3(1286.5635, -1724.81, 53.343), r: 238.1805 },
    { pos: new mp.Vector3(-1622.940, 4538.750, 44.416), r: 254.8459 },
    { pos: new mp.Vector3(-1109.834, -2641.34, 13.782), r: 295.6655 },
    { pos: new mp.Vector3(1298.3267, -1734.38, 53.879), r: 21.40746 },
    { pos: new mp.Vector3(-1431.534, 4311.956, 2.7674), r: 111.5888 },
    { pos: new mp.Vector3(-1105.002, -2672.94, 14.023), r: 251.4046 },
    { pos: new mp.Vector3(1313.8000, -1727.54, 54.351), r: 313.3344 },
    { pos: new mp.Vector3(-1559.140, 4340.063, 2.9342), r: 283.1914 },
    { pos: new mp.Vector3(-589.1460, 5942.006, 26.112), r: 147.2097 },
    { pos: new mp.Vector3(-1079.841, -2710.55, 13.944), r: 310.4754 },
    { pos: new mp.Vector3(1336.6844, -1722.83, 57.080), r: 355.9371 },
    { pos: new mp.Vector3(-1555.410, 4347.677, 1.0449), r: 156.7957 },
    { pos: new mp.Vector3(-569.4608, 5962.593, 26.322), r: 343.9017 },
    { pos: new mp.Vector3(-1431.975, 4314.375, 2.1369), r: 216.3298 },
    { pos: new mp.Vector3(1373.3272, -1711.75, 64.940), r: 309.6119 },
    { pos: new mp.Vector3(-1364.986, 4308.406, 3.4385), r: 231.2456 },
    { pos: new mp.Vector3(-1347.364, 4287.892, 4.7636), r: 127.2870 },
    { pos: new mp.Vector3(-551.0341, 6041.325, 22.948), r: 341.6854 },
    { pos: new mp.Vector3(-1345.883, 4241.079, 12.192), r: 88.43430 },
    { pos: new mp.Vector3(-1131.130, -2684.04, 13.943), r: 251.5931 },
    { pos: new mp.Vector3(1341.3006, -1583.09, 54.058), r: 286.6945 },
    { pos: new mp.Vector3(-542.1405, 6039.590, 23.676), r: 341.8204 },
    { pos: new mp.Vector3(-879.1652, -2575.32, 14.003), r: 121.3530 },
    { pos: new mp.Vector3(-1360.200, 4225.994, 17.299), r: 189.7383 },
    { pos: new mp.Vector3(-1144.478, -2704.69, 13.957), r: 314.6308 },
    { pos: new mp.Vector3(-894.9980, -2582.28, 13.980), r: 291.6492 },
    { pos: new mp.Vector3(-1352.999, 4207.149, 19.566), r: 80.11493 },
    { pos: new mp.Vector3(1358.9332, -1576.03, 53.835), r: 228.3977 },
    { pos: new mp.Vector3(-1372.736, 4185.462, 23.947), r: 320.8167 },
    { pos: new mp.Vector3(-594.4140, 6088.879, 9.5186), r: 114.9156 },
    { pos: new mp.Vector3(-926.9629, -2569.93, 13.918), r: 167.4749 },
    { pos: new mp.Vector3(-1367.417, 4232.518, 21.932), r: 2.046022 },
    { pos: new mp.Vector3(1374.4154, -1546.56, 56.504), r: 17.53967 },
    { pos: new mp.Vector3(-955.1298, -2551.98, 13.938), r: 142.9754 },
    { pos: new mp.Vector3(-1371.555, 4215.788, 25.989), r: 39.50234 },
    { pos: new mp.Vector3(1386.8946, -1539.46, 56.709), r: 329.3013 },
    { pos: new mp.Vector3(-1385.726, 4233.372, 30.327), r: 267.8034 },
    { pos: new mp.Vector3(-1382.738, 4250.376, 34.537), r: 127.1064 },
    { pos: new mp.Vector3(1356.6607, -1551.37, 55.942), r: 342.7139 },
    { pos: new mp.Vector3(-1427.694, 4242.834, 46.951), r: 139.5497 },
    { pos: new mp.Vector3(-1035.722, -2742.88, 13.878), r: 333.7699 },
    { pos: new mp.Vector3(-1061.810, -2665.06, 13.756), r: 116.6901 },
    { pos: new mp.Vector3(-363.7882, 1146.160, 324.69), r: 250.6520 },
    { pos: new mp.Vector3(1319.8763, -1561.98, 50.956), r: 25.59953 },
    { pos: new mp.Vector3(-1408.492, 4200.773, 48.742), r: 135.5855 },
    { pos: new mp.Vector3(-1043.252, -2738.69, 13.882), r: 325.1085 },
    { pos: new mp.Vector3(-518.7804, 5887.094, 32.962), r: 143.9278 },
    { pos: new mp.Vector3(1320.9468, -1531.76, 51.410), r: 192.6160 },
    { pos: new mp.Vector3(-1034.221, -2686.79, 14.062), r: 246.1434 },
    { pos: new mp.Vector3(-1357.684, 4133.557, 63.146), r: 134.1481 },
    { pos: new mp.Vector3(1336.4412, -1527.78, 53.789), r: 174.7530 },
    { pos: new mp.Vector3(-1312.237, 4188.885, 63.106), r: 35.84756 },
    { pos: new mp.Vector3(-502.8156, 5899.814, 32.810), r: 320.3479 },
    { pos: new mp.Vector3(-1293.024, 4282.347, 69.387), r: 211.9236 },
    { pos: new mp.Vector3(-1221.855, 4302.578, 74.364), r: 213.5939 },
    { pos: new mp.Vector3(1380.0895, -1522.69, 57.315), r: 197.2725 },
    { pos: new mp.Vector3(-1127.039, 4299.278, 88.398), r: 203.0799 },
    { pos: new mp.Vector3(-1045.758, 4191.211, 121.52), r: 218.3630 },
    { pos: new mp.Vector3(1406.6898, -1504.00, 59.359), r: 258.7100 },
    { pos: new mp.Vector3(1416.2053, -1495.94, 60.013), r: 169.8714 },
    { pos: new mp.Vector3(-479.9993, 5957.482, 32.549), r: 158.5079 },
    { pos: new mp.Vector3(1415.6331, -1477.39, 60.264), r: 248.6866 },
    { pos: new mp.Vector3(-958.4610, 4159.611, 137.04), r: 225.1748 },
    { pos: new mp.Vector3(-510.1432, 5951.114, 35.750), r: 225.5808 },
    { pos: new mp.Vector3(1432.8267, -1491.32, 63.224), r: 174.4146 },
    { pos: new mp.Vector3(-768.0396, 4064.861, 155.44), r: 225.2273 },
    { pos: new mp.Vector3(-626.1807, 4015.564, 127.26), r: 165.0995 },
    { pos: new mp.Vector3(624.66125, 2742.295, 42.019), r: 210.0324 },
    { pos: new mp.Vector3(1444.6484, -1499.20, 63.231), r: 250.3957 },
    { pos: new mp.Vector3(1439.4010, -1504.46, 63.002), r: 244.5327 },
    { pos: new mp.Vector3(-420.4607, 3955.877, 66.286), r: 143.1228 },
    { pos: new mp.Vector3(587.85845, 2739.640, 42.069), r: 141.7383 },
    { pos: new mp.Vector3(-316.4275, 1300.230, 346.21), r: 337.4684 },
    { pos: new mp.Vector3(563.95281, 2737.962, 42.243), r: 135.3752 },
    { pos: new mp.Vector3(-346.8739, 3949.428, 69.736), r: 354.5003 },
    { pos: new mp.Vector3(-244.5180, 3925.839, 41.327), r: 232.3585 },
    { pos: new mp.Vector3(553.51885, 2737.077, 42.163), r: 218.8070 },
    { pos: new mp.Vector3(-159.8128, 3902.282, 31.688), r: 52.82238 },
    { pos: new mp.Vector3(-202.9339, 3931.156, 34.682), r: 45.84576 },
    { pos: new mp.Vector3(554.72009, 2716.737, 42.161), r: 297.6390 },
    { pos: new mp.Vector3(-195.6061, 3951.497, 32.627), r: 161.1364 },
    { pos: new mp.Vector3(585.52014, 2718.497, 42.161), r: 272.3771 },
    { pos: new mp.Vector3(-226.5383, 4058.056, 34.042), r: 293.9100 },
    { pos: new mp.Vector3(-225.3222, 4110.161, 39.013), r: 38.03947 },
    { pos: new mp.Vector3(-582.0878, 5716.790, 37.420), r: 242.1435 },
    { pos: new mp.Vector3(587.97027, 2703.101, 41.595), r: 155.5738 },
    { pos: new mp.Vector3(-299.1167, 4249.684, 44.963), r: 134.8920 },
    { pos: new mp.Vector3(603.75128, 2703.839, 41.290), r: 228.7659 },
    { pos: new mp.Vector3(-386.9909, 4314.450, 53.930), r: 163.7488 },
    { pos: new mp.Vector3(-453.9987, 1599.282, 359.24), r: 123.0262 },
    { pos: new mp.Vector3(-392.2022, 4381.950, 54.665), r: 15.81101 },
    { pos: new mp.Vector3(-743.9144, 5547.571, 33.601), r: 128.4735 },
    { pos: new mp.Vector3(-521.3931, 4370.687, 67.472), r: 189.6404 },
    { pos: new mp.Vector3(-487.9208, 1588.769, 368.81), r: 134.6465 },
    { pos: new mp.Vector3(-585.2267, 4370.874, 52.973), r: 177.6893 },
    { pos: new mp.Vector3(-842.5304, 5403.059, 34.615), r: 300.9700 },
    { pos: new mp.Vector3(-699.6201, 5432.606, 45.894), r: 61.61314 },
    { pos: new mp.Vector3(656.04644, 2712.550, 40.947), r: 187.5624 },
    { pos: new mp.Vector3(668.22473, 2711.802, 40.718), r: 206.2348 },
    { pos: new mp.Vector3(-602.7368, 5526.333, 49.505), r: 227.2042 },
    { pos: new mp.Vector3(676.51641, 2706.975, 40.568), r: 255.0921 },
    { pos: new mp.Vector3(727.92218, 2706.593, 40.176), r: 269.5535 },
    { pos: new mp.Vector3(-551.5070, 5375.642, 70.447), r: 80.83349 },
    { pos: new mp.Vector3(-159.1274, 3683.095, 44.247), r: 87.63333 },
    { pos: new mp.Vector3(-239.9047, 3679.436, 63.082), r: 289.3907 },
    { pos: new mp.Vector3(-52.26022, 3647.881, 50.319), r: 150.4643 },
    { pos: new mp.Vector3(-601.3906, 5283.958, 70.722), r: 289.9799 },
    { pos: new mp.Vector3(-3.626581, 3720.939, 39.330), r: 249.0706 },
    { pos: new mp.Vector3(54.935546, 3765.459, 39.628), r: 202.4464 },
    { pos: new mp.Vector3(83.629890, 3756.612, 39.746), r: 173.8083 },
    { pos: new mp.Vector3(123.22492, 3719.701, 39.752), r: 77.15069 },
    { pos: new mp.Vector3(-617.4263, 5256.922, 73.175), r: 95.12940 },
    { pos: new mp.Vector3(15.989356, 3671.723, 39.552), r: 334.3818 },
    { pos: new mp.Vector3(44.927944, 3693.604, 39.747), r: 112.6040 },
    { pos: new mp.Vector3(2561.4436, 4704.951, 33.592), r: 43.07841 },
    { pos: new mp.Vector3(72.708473, 3655.243, 39.677), r: 240.9757 },
    { pos: new mp.Vector3(2530.2172, 4672.670, 33.600), r: 45.20420 },
    { pos: new mp.Vector3(126.39619, 3749.985, 39.106), r: 313.5069 },
    { pos: new mp.Vector3(170.60319, 3640.603, 31.832), r: 241.9438 },
    { pos: new mp.Vector3(121.12403, 3548.185, 39.442), r: 120.5849 },
    { pos: new mp.Vector3(116.08489, 3415.145, 38.937), r: 45.99847 },
    { pos: new mp.Vector3(-833.8652, 5313.125, 77.339), r: 286.2339 },
    { pos: new mp.Vector3(11.222527, 3244.334, 43.714), r: 67.31211 },
    { pos: new mp.Vector3(-858.5395, 5319.448, 78.073), r: 98.52737 },
    { pos: new mp.Vector3(-235.6541, 3094.668, 38.490), r: 57.00422 },
    { pos: new mp.Vector3(-384.3408, 3059.531, 31.651), r: 76.19976 },
    { pos: new mp.Vector3(-472.0847, 2988.924, 26.782), r: 143.9923 },
    { pos: new mp.Vector3(-497.0557, -195.517, 37.210), r: 21.95324 },
    { pos: new mp.Vector3(-925.3942, 5296.910, 80.617), r: 211.8536 },
    { pos: new mp.Vector3(-496.7410, 2992.898, 26.734), r: 203.7561 },
    { pos: new mp.Vector3(-506.1343, -173.552, 37.965), r: 299.7142 },
    { pos: new mp.Vector3(-489.6789, 2976.423, 26.843), r: 337.4265 },
    { pos: new mp.Vector3(-510.7838, -160.177, 38.342), r: 20.98741 },
    { pos: new mp.Vector3(-521.0203, -154.700, 38.699), r: 86.51052 },
    { pos: new mp.Vector3(-531.2508, -151.862, 38.595), r: 75.81409 },
    { pos: new mp.Vector3(-542.5288, -162.552, 38.402), r: 211.5241 },
    { pos: new mp.Vector3(-845.2442, 5429.782, 34.364), r: 38.68397 },
    { pos: new mp.Vector3(-509.1547, -111.624, 39.061), r: 136.5235 },
    { pos: new mp.Vector3(-851.1469, 5426.117, 34.570), r: 32.61560 },
    { pos: new mp.Vector3(-480.0409, -133.991, 38.823), r: 6.974265 },
    { pos: new mp.Vector3(-1082.932, 2854.804, 12.495), r: 48.11502 },
    { pos: new mp.Vector3(-434.4233, -212.751, 36.409), r: 207.2665 },
    { pos: new mp.Vector3(-444.4862, -220.736, 36.362), r: 136.0745 },
    { pos: new mp.Vector3(-1518.595, 2671.926, 3.5475), r: 18.30094 },
    { pos: new mp.Vector3(-450.0728, -210.759, 36.605), r: 26.23961 },
    { pos: new mp.Vector3(-454.0140, -195.867, 37.103), r: 18.70371 },
    { pos: new mp.Vector3(-1631.314, 2717.257, 6.9301), r: 195.3043 },
    { pos: new mp.Vector3(-462.3843, -191.979, 37.301), r: 111.3681 },
    { pos: new mp.Vector3(-1723.067, 2666.317, 2.6917), r: 9.148279 },
    { pos: new mp.Vector3(2387.9467, 4298.105, 35.200), r: 276.5133 },
    { pos: new mp.Vector3(-1867.716, 2671.568, 3.6228), r: 21.15621 },
    { pos: new mp.Vector3(2387.9484, 4294.850, 35.193), r: 245.2975 },
    { pos: new mp.Vector3(-2690.866, 2309.588, 18.584), r: 71.43727 },
    { pos: new mp.Vector3(-2683.060, 2340.625, 16.489), r: 93.23926 },
    { pos: new mp.Vector3(-2735.762, 2302.815, 18.144), r: 281.0773 },
    { pos: new mp.Vector3(1192.1622, -3250.50, 6.0923), r: 89.66001 },
    { pos: new mp.Vector3(-2628.006, 2297.202, 25.497), r: 152.5464 },
    { pos: new mp.Vector3(-2572.173, 2299.317, 31.824), r: 178.1219 },
    { pos: new mp.Vector3(-2474.701, 2305.913, 32.117), r: 255.9971 },
    { pos: new mp.Vector3(-2498.116, 2306.544, 34.028), r: 178.3381 },
    { pos: new mp.Vector3(-2214.241, 2323.844, 32.180), r: 202.4310 },
    { pos: new mp.Vector3(-2967.189, 2127.280, 41.508), r: 228.1573 },
    { pos: new mp.Vector3(1490.6472, 3445.526, 47.764), r: 307.4730 },
    { pos: new mp.Vector3(-3007.954, 1914.891, 28.482), r: 50.62326 },
    { pos: new mp.Vector3(621.81091, -3018.62, 6.0452), r: 322.1868 },
    { pos: new mp.Vector3(618.72955, -3008.30, 6.0452), r: 277.4644 },
    { pos: new mp.Vector3(-2970.167, 1568.452, 29.054), r: 31.66737 },
    { pos: new mp.Vector3(615.00921, -3058.81, 6.0692), r: 356.2648 },
    { pos: new mp.Vector3(-3086.595, 1198.664, 20.363), r: 160.6628 },
    { pos: new mp.Vector3(-599.9562, -933.743, 23.864), r: 31.96691 },
    { pos: new mp.Vector3(614.61267, -3099.65, 6.0692), r: 270.8399 },
    { pos: new mp.Vector3(-3156.531, 1125.763, 20.852), r: 43.50090 },
    { pos: new mp.Vector3(603.27191, -3140.99, 6.0692), r: 269.5389 },
    { pos: new mp.Vector3(-3162.094, 1043.759, 20.818), r: 252.8862 },
    { pos: new mp.Vector3(596.85479, -3139.77, 6.0692), r: 231.7127 },
    { pos: new mp.Vector3(-586.1803, 5610.266, 39.097), r: 149.8329 },
    { pos: new mp.Vector3(590.55157, -3140.70, 6.0749), r: 329.6787 },
    { pos: new mp.Vector3(581.14770, -3126.74, 6.0700), r: 59.18290 },
    { pos: new mp.Vector3(-612.7954, -945.738, 21.895), r: 85.97628 },
    { pos: new mp.Vector3(558.02343, -3123.20, 6.0692), r: 315.4495 },
    { pos: new mp.Vector3(-611.9002, -916.366, 24.560), r: 269.9873 },
    { pos: new mp.Vector3(-612.0977, -924.777, 24.562), r: 178.5809 },
    { pos: new mp.Vector3(-308.6075, 6037.105, 31.459), r: 118.6853 },
    { pos: new mp.Vector3(-621.7897, -946.737, 21.748), r: 124.5762 },
    { pos: new mp.Vector3(-3194.529, 1188.938, 9.1124), r: 242.1647 },
    { pos: new mp.Vector3(-510.9023, 1550.651, 373.55), r: 23.56729 },
    { pos: new mp.Vector3(484.10003, -3096.76, 6.0700), r: 255.8845 },
    { pos: new mp.Vector3(-640.3905, -916.112, 24.256), r: 14.85788 },
    { pos: new mp.Vector3(-563.7039, 1515.885, 333.94), r: 128.1607 },
    { pos: new mp.Vector3(-3202.193, 1147.932, 9.5310), r: 210.3349 },
    { pos: new mp.Vector3(-639.1213, -896.065, 25.031), r: 45.15966 },
    { pos: new mp.Vector3(484.69903, -3067.82, 6.0696), r: 29.74657 },
    { pos: new mp.Vector3(-590.7646, 1428.889, 334.83), r: 160.4464 },
    { pos: new mp.Vector3(-641.3262, -882.124, 24.952), r: 309.2217 },
    { pos: new mp.Vector3(-3186.457, 1236.276, 10.293), r: 256.0269 },
    { pos: new mp.Vector3(-691.5991, 1382.835, 307.13), r: 117.9169 },
    { pos: new mp.Vector3(482.26559, -3052.52, 6.1519), r: 280.0286 },
    { pos: new mp.Vector3(1388.9741, 4337.314, 42.979), r: 98.60660 },
    { pos: new mp.Vector3(-663.4447, 1461.120, 306.43), r: 341.2874 },
    { pos: new mp.Vector3(-3103.137, 1334.414, 20.742), r: 236.1638 },
    { pos: new mp.Vector3(-895.5570, 1446.777, 289.98), r: 92.05382 },
    { pos: new mp.Vector3(468.93173, -3048.28, 6.1573), r: 63.88902 },
    { pos: new mp.Vector3(-3127.880, 798.9920, 17.565), r: 288.2633 },
    { pos: new mp.Vector3(282.38049, 6798.540, 15.696), r: 257.7932 },
    { pos: new mp.Vector3(1351.9959, 4319.338, 37.956), r: 346.0347 },
    { pos: new mp.Vector3(-3080.324, 735.5178, 21.844), r: 313.0677 },
    { pos: new mp.Vector3(447.87313, -3050.51, 6.0704), r: 282.0632 },
    { pos: new mp.Vector3(1338.2746, 4324.139, 38.018), r: 343.3415 },
    { pos: new mp.Vector3(-1003.211, 1310.308, 296.42), r: 81.28653 },
    { pos: new mp.Vector3(-1145.981, 1236.331, 277.64), r: 109.5351 },
    { pos: new mp.Vector3(1334.6619, 4325.463, 38.036), r: 54.27318 },
    { pos: new mp.Vector3(451.17623, -3078.99, 6.0696), r: 0.513970 },
    { pos: new mp.Vector3(-1305.868, 1132.152, 279.46), r: 109.6016 },
    { pos: new mp.Vector3(1326.7020, 4326.407, 38.178), r: 54.53036 },
    { pos: new mp.Vector3(-1450.003, 1188.644, 232.13), r: 60.43838 },
    { pos: new mp.Vector3(-397.2911, 6317.948, 28.956), r: 224.8946 },
    { pos: new mp.Vector3(-1685.233, 1376.398, 166.49), r: 186.7174 },
    { pos: new mp.Vector3(-3075.989, 532.8903, 2.3569), r: 77.05503 },
    { pos: new mp.Vector3(1321.9862, 4318.587, 38.148), r: 346.8402 },
    { pos: new mp.Vector3(471.72830, -3030.92, 6.0868), r: 274.5994 },
    { pos: new mp.Vector3(-3078.135, 535.0017, 2.3569), r: 200.4158 },
    { pos: new mp.Vector3(-1812.083, 1437.659, 220.68), r: 214.1748 },
    { pos: new mp.Vector3(1319.6275, 4309.580, 38.040), r: 196.7061 },
    { pos: new mp.Vector3(1318.6945, 4304.492, 38.044), r: 232.1899 },
    { pos: new mp.Vector3(-3103.141, 405.3718, 2.3568), r: 51.32268 },
    { pos: new mp.Vector3(508.33773, -3004.66, 6.0444), r: 282.6012 },
    { pos: new mp.Vector3(-1921.028, 1428.934, 266.20), r: 282.7610 },
    { pos: new mp.Vector3(529.12335, -3003.98, 6.0444), r: 243.4539 },
    { pos: new mp.Vector3(-3110.485, 255.2054, 9.4870), r: 331.5431 },
    { pos: new mp.Vector3(-2348.652, 1339.941, 336.29), r: 155.9289 },
    { pos: new mp.Vector3(582.90728, -3004.70, 6.0452), r: 272.9579 },
    { pos: new mp.Vector3(-2989.536, 315.9427, 14.884), r: 49.94081 },
    { pos: new mp.Vector3(1315.7164, 4285.766, 33.908), r: 122.8563 },
    { pos: new mp.Vector3(1314.2308, 4278.952, 33.909), r: 170.1926 },
    { pos: new mp.Vector3(1311.4880, 4264.737, 33.908), r: 140.0357 },
    { pos: new mp.Vector3(1309.9907, 4257.749, 33.909), r: 175.6538 },
    { pos: new mp.Vector3(1307.1933, 4243.724, 33.908), r: 170.5603 },
    { pos: new mp.Vector3(1305.8533, 4236.182, 33.909), r: 169.7706 },
    { pos: new mp.Vector3(1304.3321, 4228.943, 33.908), r: 171.3683 },
    { pos: new mp.Vector3(1302.9387, 4220.669, 33.908), r: 144.7674 },
    { pos: new mp.Vector3(1299.3746, 4215.110, 33.908), r: 339.8902 },
    { pos: new mp.Vector3(501.63311, 5603.718, 797.91), r: 176.2038 },
    { pos: new mp.Vector3(-362.1309, 1458.142, 288.89), r: 108.9446 },
    { pos: new mp.Vector3(1340.8770, 4225.100, 33.915), r: 78.44190 },
    { pos: new mp.Vector3(-359.3029, 1447.780, 288.88), r: 180.7348 },
    { pos: new mp.Vector3(1331.0296, 4228.786, 33.915), r: 170.0515 },
    { pos: new mp.Vector3(503.18707, 5630.244, 792.79), r: 358.7370 },
    { pos: new mp.Vector3(1338.1035, 4227.437, 33.915), r: 161.8356 },
    { pos: new mp.Vector3(-236.7734, 1480.495, 288.88), r: 230.8003 },
    { pos: new mp.Vector3(535.09948, 5630.078, 777.91), r: 286.4772 },
    { pos: new mp.Vector3(1323.4859, 4230.527, 33.915), r: 149.3214 },
    { pos: new mp.Vector3(-239.9766, 1491.733, 289.03), r: 283.1843 },
    { pos: new mp.Vector3(560.11505, 5580.729, 751.56), r: 191.6265 },
    { pos: new mp.Vector3(1316.5991, 4231.795, 33.872), r: 166.2546 },
    { pos: new mp.Vector3(40.147315, -2652.95, 6.0045), r: 23.92366 },
    { pos: new mp.Vector3(1310.2956, 4232.893, 33.915), r: 134.4079 },
    { pos: new mp.Vector3(595.22521, 5467.633, 726.12), r: 221.3176 },
    { pos: new mp.Vector3(228.16503, 1329.327, 238.11), r: 307.6486 },
    { pos: new mp.Vector3(1300.5772, 4240.486, 33.902), r: 346.0605 },
    { pos: new mp.Vector3(230.47091, 1351.559, 238.76), r: 332.7308 },
    { pos: new mp.Vector3(7.3626427, -2653.71, 6.0050), r: 311.0348 },
    { pos: new mp.Vector3(1301.7139, 4247.472, 33.916), r: 235.0695 },
    { pos: new mp.Vector3(154.58033, 1514.761, 237.58), r: 77.43195 },
    { pos: new mp.Vector3(219.67280, 5279.252, 622.91), r: 185.2390 },
    { pos: new mp.Vector3(1303.3461, 4253.797, 33.942), r: 244.6306 },
    { pos: new mp.Vector3(20.086717, -2638.19, 6.0322), r: 102.3517 },
    { pos: new mp.Vector3(153.81327, 1644.090, 228.94), r: 107.9742 },
    { pos: new mp.Vector3(1305.2026, 4262.338, 33.908), r: 248.5013 },
    { pos: new mp.Vector3(421.97906, 6544.719, 27.731), r: 246.3055 },
    { pos: new mp.Vector3(38.019081, -2668.14, 6.0083), r: 52.56310 },
    { pos: new mp.Vector3(1306.2626, 4268.052, 34.141), r: 322.5655 },
    { pos: new mp.Vector3(1307.2290, 4275.161, 33.909), r: 337.0160 },
    { pos: new mp.Vector3(18.516119, -2687.96, 6.0119), r: 187.7755 },
    { pos: new mp.Vector3(192.43948, 5210.029, 577.76), r: 182.2470 },
    { pos: new mp.Vector3(689.81317, 1716.890, 185.79), r: 326.6557 },
    { pos: new mp.Vector3(18.255954, -2692.98, 6.0114), r: 352.8138 },
    { pos: new mp.Vector3(1277.9344, 4303.306, 37.670), r: 114.3256 },
    { pos: new mp.Vector3(913.04852, 1736.326, 166.36), r: 250.0117 },
    { pos: new mp.Vector3(-629.6872, 974.3021, 241.21), r: 9.805464 },
    { pos: new mp.Vector3(159.57194, 5204.939, 574.10), r: 158.3649 },
    { pos: new mp.Vector3(20.001724, -2704.00, 6.0082), r: 271.1614 },
    { pos: new mp.Vector3(842.44763, 1783.239, 149.02), r: 93.81546 },
    { pos: new mp.Vector3(-662.9259, 982.7255, 238.94), r: 326.4498 },
    { pos: new mp.Vector3(1301.0902, 4318.583, 38.155), r: 341.4438 },
    { pos: new mp.Vector3(878.88897, 1839.489, 141.48), r: 35.80661 },
    { pos: new mp.Vector3(776.15716, 1824.583, 131.32), r: 156.7918 },
    { pos: new mp.Vector3(-619.1355, 921.8402, 238.07), r: 145.5181 },
    { pos: new mp.Vector3(801.93884, 1972.548, 103.14), r: 358.8901 },
    { pos: new mp.Vector3(43.844581, -2686.00, 6.0130), r: 271.1173 },
    { pos: new mp.Vector3(132.86293, 5169.266, 551.48), r: 140.5942 },
    { pos: new mp.Vector3(48.562835, -2685.96, 6.0108), r: 93.70354 },
    { pos: new mp.Vector3(56.766242, -2668.10, 6.0092), r: 257.9941 },
    { pos: new mp.Vector3(118.89615, 5139.875, 520.80), r: 170.0544 },
    { pos: new mp.Vector3(851.90136, 2056.512, 69.160), r: 21.87397 },
    { pos: new mp.Vector3(-497.4410, 807.7185, 185.28), r: 180.2217 },
    { pos: new mp.Vector3(81.312469, 5074.054, 495.98), r: 149.7892 },
    { pos: new mp.Vector3(1290.9536, 4320.416, 38.117), r: 37.13652 },
    { pos: new mp.Vector3(58.978397, -2667.09, 6.0073), r: 355.4053 },
    { pos: new mp.Vector3(55.670803, -2666.51, 6.0089), r: 340.4842 },
    { pos: new mp.Vector3(1282.7310, 4327.176, 38.790), r: 323.7320 },
    { pos: new mp.Vector3(101.67066, 5020.047, 464.86), r: 129.9589 },
    { pos: new mp.Vector3(732.63970, 2514.614, 73.192), r: 270.1939 },
    { pos: new mp.Vector3(63.496189, -2687.95, 6.0046), r: 267.7267 },
    { pos: new mp.Vector3(67.369491, 5009.123, 460.62), r: 84.81380 },
    { pos: new mp.Vector3(1286.4116, 4343.145, 39.357), r: 229.9262 },
    { pos: new mp.Vector3(732.05236, 2533.579, 73.209), r: 224.7082 },
    { pos: new mp.Vector3(73.550994, -2696.97, 6.0012), r: 78.69554 },
    { pos: new mp.Vector3(765.24554, 2537.756, 74.087), r: 163.1798 },
    { pos: new mp.Vector3(1301.5201, 4334.988, 38.600), r: 205.4357 },
    { pos: new mp.Vector3(764.66314, 2512.054, 73.444), r: 138.5789 },
    { pos: new mp.Vector3(112.59940, -2697.46, 6.0002), r: 292.4940 },
    { pos: new mp.Vector3(1310.3201, 4360.551, 40.917), r: 302.0554 },
    { pos: new mp.Vector3(118.28280, -2684.85, 6.0011), r: 269.8995 },
    { pos: new mp.Vector3(8.2418918, 5016.196, 448.60), r: 147.6875 },
    { pos: new mp.Vector3(449.63256, 6463.928, 28.962), r: 240.6820 },
    { pos: new mp.Vector3(1315.5250, 4382.259, 41.261), r: 176.0955 },
    { pos: new mp.Vector3(109.13430, -2716.41, 6.0008), r: 43.24576 },
    { pos: new mp.Vector3(1329.1676, 4382.859, 44.224), r: 203.1470 },
    { pos: new mp.Vector3(106.18970, -2719.44, 6.0019), r: 44.75194 },
    { pos: new mp.Vector3(1335.3793, 4381.719, 44.348), r: 169.1999 },
    { pos: new mp.Vector3(-10.74917, 5006.154, 435.99), r: 137.7144 },
    { pos: new mp.Vector3(1342.5821, 4380.411, 44.347), r: 262.0514 },
    { pos: new mp.Vector3(973.02258, 2714.513, 39.483), r: 125.0296 },
    { pos: new mp.Vector3(50.099826, -2748.03, 6.0044), r: 275.1842 },
    { pos: new mp.Vector3(1352.7891, 4378.614, 44.343), r: 264.5895 },
    { pos: new mp.Vector3(1167.0258, 2612.269, 40.172), r: 351.8794 },
    { pos: new mp.Vector3(1358.0354, 4377.722, 44.342), r: 184.2167 },
    { pos: new mp.Vector3(44.626842, -2746.41, 6.0010), r: 95.83277 },
    { pos: new mp.Vector3(1265.5744, 2693.411, 37.772), r: 151.8953 },
    { pos: new mp.Vector3(-44.83334, 4954.133, 403.13), r: 116.1520 },
    { pos: new mp.Vector3(16.901914, -2748.77, 6.0043), r: 72.25714 },
    { pos: new mp.Vector3(1374.1110, 4380.956, 44.990), r: 9.863056 },
    { pos: new mp.Vector3(1.1926773, -2735.70, 6.0047), r: 250.7341 },
    { pos: new mp.Vector3(-76.68074, 4935.551, 389.72), r: 108.3168 },
    { pos: new mp.Vector3(1745.9809, 3295.455, 41.128), r: 126.8658 },
    { pos: new mp.Vector3(1383.2932, 4381.815, 44.242), r: 244.0865 },
    { pos: new mp.Vector3(1711.9697, 3314.791, 41.153), r: 109.2276 },
    { pos: new mp.Vector3(1630.5091, 3200.657, 40.922), r: 353.0892 },
    { pos: new mp.Vector3(1589.3833, 3158.941, 40.839), r: 56.40121 },
    { pos: new mp.Vector3(-27.35870, -2683.22, 6.0043), r: 288.7396 },
    { pos: new mp.Vector3(-26.64358, -2657.58, 6.0045), r: 19.81494 },
    { pos: new mp.Vector3(1576.4907, 3196.613, 40.532), r: 297.1309 },
    { pos: new mp.Vector3(1433.4321, 4382.552, 44.339), r: 1.585271 },
    { pos: new mp.Vector3(1441.8037, 4373.746, 44.242), r: 221.3527 },
    { pos: new mp.Vector3(510.00448, 3175.333, 46.865), r: 38.79566 },
    { pos: new mp.Vector3(-23.58045, -2644.74, 6.0182), r: 210.2297 },
    { pos: new mp.Vector3(-446.7807, 6084.445, 31.654), r: 264.6531 },
    { pos: new mp.Vector3(443.98016, 3084.153, 41.817), r: 53.11341 },
    { pos: new mp.Vector3(-12.54931, -2631.51, 6.1597), r: 342.6329 },
    { pos: new mp.Vector3(-109.4656, 4936.928, 373.48), r: 120.3345 },
    { pos: new mp.Vector3(-18.91172, -2632.12, 6.1923), r: 352.6465 },
    { pos: new mp.Vector3(-414.2396, -252.859, 36.254), r: 320.8425 },
    { pos: new mp.Vector3(603.66540, 3027.421, 42.034), r: 22.88314 },
    { pos: new mp.Vector3(-497.5099, 4316.931, 89.711), r: 187.8307 },
    { pos: new mp.Vector3(-420.8503, -256.833, 36.316), r: 108.2995 },
    { pos: new mp.Vector3(-426.8111, -265.858, 36.215), r: 186.2814 },
    { pos: new mp.Vector3(-164.1430, 4898.380, 340.31), r: 105.2170 },
    { pos: new mp.Vector3(642.85095, 3012.531, 43.261), r: 97.99120 },
    { pos: new mp.Vector3(-134.7735, -2629.29, 6.0002), r: 354.0220 },
    { pos: new mp.Vector3(-417.5887, -271.148, 36.105), r: 251.9242 },
    { pos: new mp.Vector3(-397.6293, -272.530, 35.238), r: 264.0765 },
    { pos: new mp.Vector3(-199.2284, 4892.854, 325.99), r: 71.30228 },
    { pos: new mp.Vector3(-384.3804, -277.154, 34.496), r: 225.6361 },
    { pos: new mp.Vector3(652.82012, 2916.043, 42.023), r: 156.4797 },
    { pos: new mp.Vector3(-193.4230, -2633.89, 6.2010), r: 5.535120 },
    { pos: new mp.Vector3(-383.1574, -289.490, 33.875), r: 138.2190 },
    { pos: new mp.Vector3(700.87359, 2880.280, 50.526), r: 151.4244 },
    { pos: new mp.Vector3(-221.3879, 4907.772, 313.96), r: 91.20872 },
    { pos: new mp.Vector3(-816.9227, 5409.867, 34.249), r: 0.559243 },
    { pos: new mp.Vector3(-502.2515, 4312.313, 89.796), r: 205.1584 },
    { pos: new mp.Vector3(-374.1896, -298.449, 33.249), r: 231.4596 },
    { pos: new mp.Vector3(-354.5827, -299.771, 32.321), r: 283.7073 },
    { pos: new mp.Vector3(-497.1709, 4312.412, 89.799), r: 289.9934 },
    { pos: new mp.Vector3(904.28186, 2844.680, 58.992), r: 154.3451 },
    { pos: new mp.Vector3(-254.1620, 4906.911, 300.90), r: 79.00519 },
    { pos: new mp.Vector3(-342.1871, -307.033, 31.435), r: 294.1423 },
    { pos: new mp.Vector3(-218.5165, -2649.74, 6.0002), r: 1.373345 },
    { pos: new mp.Vector3(-817.1416, 5420.286, 34.049), r: 209.8898 },
    { pos: new mp.Vector3(-342.2117, -315.939, 31.184), r: 155.6425 },
    { pos: new mp.Vector3(-227.2127, -2649.69, 6.0002), r: 0.439428 },
    { pos: new mp.Vector3(1098.1678, 2659.315, 38.140), r: 6.498911 },
    { pos: new mp.Vector3(-335.1784, -327.034, 30.758), r: 218.9359 },
    { pos: new mp.Vector3(-503.7442, 4320.419, 89.794), r: 153.6337 },
    { pos: new mp.Vector3(-318.2792, -322.972, 30.700), r: 340.9892 },
    { pos: new mp.Vector3(1101.8979, 2659.253, 38.140), r: 37.62985 },
    { pos: new mp.Vector3(-231.7979, -2649.69, 6.0002), r: 357.0961 },
    { pos: new mp.Vector3(-306.7976, -328.760, 30.406), r: 282.1362 },
    { pos: new mp.Vector3(1105.3127, 2658.917, 38.140), r: 348.2369 },
    { pos: new mp.Vector3(-299.7305, -345.659, 30.007), r: 227.7193 },
    { pos: new mp.Vector3(-505.4476, 4329.508, 89.794), r: 231.8832 },
    { pos: new mp.Vector3(-270.5762, -2647.19, 6.1588), r: 60.13224 },
    { pos: new mp.Vector3(-311.3723, 4944.865, 268.16), r: 6.521759 },
    { pos: new mp.Vector3(-500.2023, 4329.254, 89.800), r: 291.4594 },
    { pos: new mp.Vector3(-505.4467, 4338.213, 89.800), r: 112.6936 },
    { pos: new mp.Vector3(-501.6663, 4339.306, 89.795), r: 284.2339 },
    { pos: new mp.Vector3(1193.7807, 1835.256, 78.537), r: 134.4331 },
    { pos: new mp.Vector3(-2507.968, 756.8825, 301.79), r: 247.3208 },
    { pos: new mp.Vector3(-337.9136, 5002.291, 223.03), r: 130.5196 },
    { pos: new mp.Vector3(-2415.568, 757.5928, 284.98), r: 267.7845 },
    { pos: new mp.Vector3(-357.4506, 4969.136, 203.70), r: 157.0047 },
    { pos: new mp.Vector3(-223.3343, 6132.028, 31.434), r: 44.98857 },
    { pos: new mp.Vector3(-509.3360, 4359.467, 89.794), r: 284.8294 },
    { pos: new mp.Vector3(-503.8422, 4359.926, 89.793), r: 278.2923 },
    { pos: new mp.Vector3(-505.1743, 4373.758, 89.794), r: 5.734300 },
    { pos: new mp.Vector3(-511.3326, 4378.624, 89.788), r: 127.6615 },
    { pos: new mp.Vector3(-514.7642, 4399.200, 89.794), r: 357.3376 },
    { pos: new mp.Vector3(-508.7453, 4400.100, 89.794), r: 282.7029 },
    { pos: new mp.Vector3(-513.2614, 4433.006, 89.795), r: 5.678562 },
    { pos: new mp.Vector3(-519.5958, 4432.291, 89.795), r: 98.94512 },
    { pos: new mp.Vector3(-1146.857, -1464.83, 7.6906), r: 247.2630 },
    { pos: new mp.Vector3(-1145.255, -1467.04, 7.6907), r: 302.7701 },
    { pos: new mp.Vector3(1675.9265, -1934.62, 113.03), r: 188.5687 },
    { pos: new mp.Vector3(-1134.106, -1471.34, 4.6092), r: 208.6630 },
    { pos: new mp.Vector3(1702.8726, -2111.40, 107.36), r: 88.04413 },
    { pos: new mp.Vector3(-1138.234, -1474.22, 4.5378), r: 279.7226 },
    { pos: new mp.Vector3(-1404.854, 101.3001, 53.438), r: 195.5942 },
    { pos: new mp.Vector3(1619.4271, -2251.09, 107.10), r: 276.8414 },
    { pos: new mp.Vector3(-525.0692, 4471.949, 89.782), r: 10.57455 },
    { pos: new mp.Vector3(-1388.461, 111.4414, 55.185), r: 236.2209 },
    { pos: new mp.Vector3(1700.1040, -2356.14, 100.85), r: 102.0137 },
    { pos: new mp.Vector3(-518.6659, 4472.562, 89.794), r: 271.4693 },
    { pos: new mp.Vector3(-1139.879, -1478.97, 4.3369), r: 222.5810 },
    { pos: new mp.Vector3(1639.8073, -2416.38, 92.721), r: 129.9900 },
    { pos: new mp.Vector3(-1382.168, 131.9729, 55.865), r: 20.79567 },
    { pos: new mp.Vector3(1635.5104, -2515.46, 74.961), r: 231.4474 },
    { pos: new mp.Vector3(-520.8906, 4488.340, 89.794), r: 2.754157 },
    { pos: new mp.Vector3(1695.1580, -2527.70, 85.725), r: 64.60282 },
    { pos: new mp.Vector3(149.80398, 6502.669, 31.621), r: 64.06436 },
    { pos: new mp.Vector3(-527.3132, 4488.365, 89.794), r: 92.91662 },
    { pos: new mp.Vector3(-1363.378, 147.7325, 56.440), r: 246.4793 },
    { pos: new mp.Vector3(1557.7586, -2564.76, 58.325), r: 170.7038 },
    { pos: new mp.Vector3(-371.0480, 4923.292, 197.17), r: 168.4045 },
    { pos: new mp.Vector3(1542.9233, -2577.53, 54.373), r: 330.2338 },
    { pos: new mp.Vector3(-1347.613, 121.8068, 56.355), r: 212.5730 },
    { pos: new mp.Vector3(-1340.325, 115.4775, 56.291), r: 185.6447 },
    { pos: new mp.Vector3(1437.5773, -2585.17, 48.301), r: 116.0002 },
    { pos: new mp.Vector3(-1334.377, 106.1573, 56.351), r: 280.9655 },
    { pos: new mp.Vector3(1455.9969, -2591.06, 48.627), r: 203.4751 },
    { pos: new mp.Vector3(-1021.752, -117.762, 41.348), r: 217.9377 },
    { pos: new mp.Vector3(-463.6733, 4826.966, 219.79), r: 143.8416 },
    { pos: new mp.Vector3(-973.5387, -77.5489, 40.880), r: 0.605845 },
    { pos: new mp.Vector3(-522.4477, 4499.159, 89.794), r: 99.44146 },
    { pos: new mp.Vector3(1295.1184, -2564.36, 44.208), r: 358.2235 },
    { pos: new mp.Vector3(-528.6091, 4498.041, 89.790), r: 103.1462 },
    { pos: new mp.Vector3(1257.5494, -2558.62, 42.715), r: 121.3957 },
    { pos: new mp.Vector3(-488.9773, 4763.816, 227.30), r: 158.9924 },
    { pos: new mp.Vector3(1214.7362, -2592.69, 40.381), r: 150.6135 },
    { pos: new mp.Vector3(-1322.509, 266.6331, 62.979), r: 154.5430 },
    { pos: new mp.Vector3(1170.4787, -2578.15, 35.104), r: 12.13572 },
    { pos: new mp.Vector3(-1242.386, 254.9669, 65.496), r: 208.7183 },
    { pos: new mp.Vector3(1422.7366, 6352.908, 23.985), r: 215.3907 },
    { pos: new mp.Vector3(1037.9066, -2611.58, 10.115), r: 289.7859 },
    { pos: new mp.Vector3(1055.4284, -2613.73, 10.114), r: 9.709405 },
    { pos: new mp.Vector3(-822.3974, 298.2687, 86.290), r: 67.56450 },
    { pos: new mp.Vector3(-201.1813, 4229.079, 46.967), r: 145.6675 },
    { pos: new mp.Vector3(-496.0617, 4728.841, 242.14), r: 184.4485 },
    { pos: new mp.Vector3(-832.3924, 284.6689, 86.478), r: 232.7536 },
    { pos: new mp.Vector3(1438.0632, 6335.970, 23.988), r: 3.198807 },
    { pos: new mp.Vector3(-188.7364, 4218.208, 44.843), r: 137.3688 },
    { pos: new mp.Vector3(1480.3505, 6351.505, 23.745), r: 35.94857 },
    { pos: new mp.Vector3(-397.3682, 259.0065, 84.141), r: 278.2964 },
    { pos: new mp.Vector3(-582.6735, 4704.252, 211.41), r: 132.4201 },
    { pos: new mp.Vector3(-107.2273, 411.5583, 113.86), r: 67.54391 },
    { pos: new mp.Vector3(1484.9351, 6359.529, 23.722), r: 86.13347 },
    { pos: new mp.Vector3(-89.99250, 408.4948, 113.94), r: 286.2511 },
    { pos: new mp.Vector3(-72.12182, 397.0194, 114.10), r: 236.5686 },
    { pos: new mp.Vector3(-608.6678, 4636.539, 172.72), r: 186.8963 },
    { pos: new mp.Vector3(-42.04586, 384.0898, 113.65), r: 251.7508 },
    { pos: new mp.Vector3(1499.6533, 6335.824, 23.799), r: 267.9699 },
    { pos: new mp.Vector3(5.6851201, 365.8541, 113.15), r: 301.9916 },
    { pos: new mp.Vector3(1506.3286, 6342.104, 24.048), r: 194.1246 },
    { pos: new mp.Vector3(-630.5076, 4608.849, 149.57), r: 149.1037 },
    { pos: new mp.Vector3(1515.0583, 6330.992, 24.074), r: 45.21722 },
    { pos: new mp.Vector3(93.404266, -2019.37, 18.336), r: 250.8176 },
    { pos: new mp.Vector3(1529.7752, 6336.896, 24.123), r: 58.04822 },
    { pos: new mp.Vector3(977.40356, -2609.58, 6.6646), r: 116.8666 },
    { pos: new mp.Vector3(1534.5698, 6322.910, 24.193), r: 0.981145 },
    { pos: new mp.Vector3(899.89562, -2608.05, 6.0174), r: 175.2838 },
    { pos: new mp.Vector3(83.828750, -2022.97, 19.704), r: 181.8320 },
    { pos: new mp.Vector3(562.63427, -2580.73, 6.5359), r: 245.1585 },
    { pos: new mp.Vector3(775.61206, 176.2275, 81.326), r: 251.1848 },
    { pos: new mp.Vector3(-769.0074, 4512.158, 90.722), r: 174.2865 },
    { pos: new mp.Vector3(170.79995, -2058.64, 18.321), r: 322.8566 },
    { pos: new mp.Vector3(-426.1251, 5861.533, 44.134), r: 339.4585 },
    { pos: new mp.Vector3(-651.7601, 4287.711, 133.52), r: 195.4635 },
    { pos: new mp.Vector3(1663.8383, 3.333536, 173.77), r: 55.49975 },
    { pos: new mp.Vector3(-563.5516, 4195.069, 192.21), r: 241.6859 },
    { pos: new mp.Vector3(-435.6293, 5846.652, 44.121), r: 157.5934 },
    { pos: new mp.Vector3(1616.0181, 344.3648, 258.26), r: 36.58049 },
    { pos: new mp.Vector3(-526.3491, 4195.613, 193.20), r: 158.1815 },
    { pos: new mp.Vector3(2573.0129, 2566.564, 34.984), r: 310.2177 },
    { pos: new mp.Vector3(2548.6213, 2661.925, 39.741), r: 347.3778 },
    { pos: new mp.Vector3(262.08532, -2022.06, 18.840), r: 241.0095 },
    { pos: new mp.Vector3(-469.7468, -1447.80, 76.984), r: 253.4617 },
    { pos: new mp.Vector3(-565.6865, 4125.924, 177.76), r: 160.9808 },
    { pos: new mp.Vector3(271.98187, -2011.00, 19.569), r: 301.3552 },
    { pos: new mp.Vector3(-475.1792, -1441.84, 76.984), r: 41.28803 },
    { pos: new mp.Vector3(285.52368, -1994.96, 20.479), r: 203.0969 },
    { pos: new mp.Vector3(1078.0107, 4243.887, 35.883), r: 2.363381 },
    { pos: new mp.Vector3(1056.4710, 4282.400, 37.520), r: 39.18905 },
    { pos: new mp.Vector3(292.32379, -1986.07, 21.158), r: 8.273559 },
    { pos: new mp.Vector3(493.65692, 5519.058, 778.23), r: 14.59601 },
    { pos: new mp.Vector3(487.89639, 5589.614, 794.00), r: 60.22472 },
    { pos: new mp.Vector3(-540.1877, 4011.773, 120.08), r: 198.5932 },
    { pos: new mp.Vector3(-178.5961, 6299.664, 31.489), r: 149.4072 },
    { pos: new mp.Vector3(330.04449, -1941.78, 24.761), r: 335.5669 },
    { pos: new mp.Vector3(505.93225, 5597.225, 795.88), r: 203.0889 },
    { pos: new mp.Vector3(990.76281, 3585.085, 33.564), r: 297.1757 },
    { pos: new mp.Vector3(-531.8395, 3972.860, 97.937), r: 200.8849 },
    { pos: new mp.Vector3(-218.1745, 6258.709, 31.489), r: 83.59084 },
    { pos: new mp.Vector3(1000.2642, 3585.238, 33.574), r: 273.5644 },
    { pos: new mp.Vector3(-517.2928, 3950.783, 91.278), r: 253.7113 },
    { pos: new mp.Vector3(1003.3258, 3575.891, 33.683), r: 177.8442 },
    { pos: new mp.Vector3(420.45452, 856.6541, 196.34), r: 313.2768 },
    { pos: new mp.Vector3(-247.5882, 6258.087, 31.489), r: 277.0057 },
    { pos: new mp.Vector3(437.33728, 809.2263, 195.66), r: 232.1520 },
    { pos: new mp.Vector3(405.77124, 720.8812, 196.68), r: 74.62737 },
    { pos: new mp.Vector3(-471.6036, 3978.388, 75.612), r: 252.6316 },
    { pos: new mp.Vector3(269.07754, 6437.75, .8968257), r: 58.73168 },
    { pos: new mp.Vector3(314.17611, 806.6664, 190.34), r: 333.1047 },
    { pos: new mp.Vector3(263.76458, 6478.891, 30.621), r: 78.88655 },
    { pos: new mp.Vector3(312.01577, 867.6392, 196.36), r: 113.2861 },
    { pos: new mp.Vector3(255.09057, 6529.466, 30.852), r: 8.781133 },
    { pos: new mp.Vector3(271.42468, 857.7165, 200.39), r: 326.1661 },
    { pos: new mp.Vector3(-217.5268, 6200.420, 31.489), r: 50.97858 },
    { pos: new mp.Vector3(211.68797, 6576.584, 31.875), r: 155.2267 },
    { pos: new mp.Vector3(221.85289, 908.2127, 208.89), r: 142.0325 },
    { pos: new mp.Vector3(116.78372, -1816.10, 26.891), r: 207.5298 },
    { pos: new mp.Vector3(60.564537, 999.1717, 211.20), r: 100.1946 },
    { pos: new mp.Vector3(-4.265078, 1007.800, 216.98), r: 196.8953 },
    { pos: new mp.Vector3(105.25320, -1830.04, 26.139), r: 147.6378 },
    { pos: new mp.Vector3(-251.9576, 6384.200, 30.672), r: 102.7582 },
    { pos: new mp.Vector3(-432.8826, 3940.490, 68.537), r: 304.6801 },
    { pos: new mp.Vector3(-341.2595, 6311.664, 31.502), r: 126.0724 },
    { pos: new mp.Vector3(-2.415313, 843.4835, 199.28), r: 256.4591 },
    { pos: new mp.Vector3(90.043251, -1848.74, 25.064), r: 148.9291 },
    { pos: new mp.Vector3(102.80583, 648.0441, 206.68), r: 115.4758 },
    { pos: new mp.Vector3(-393.4173, 3926.437, 77.464), r: 279.6507 },
    { pos: new mp.Vector3(76.480133, -1872.07, 23.355), r: 170.6652 },
    { pos: new mp.Vector3(-69.54252, 608.0241, 207.17), r: 133.3378 },
    { pos: new mp.Vector3(-1113.110, 4883.948, 215.35), r: 310.6535 },
    { pos: new mp.Vector3(-1080.097, 4928.524, 213.19), r: 81.86156 },
    { pos: new mp.Vector3(-354.9788, 3919.069, 73.838), r: 286.3863 },
    { pos: new mp.Vector3(-327.7115, 650.0061, 173.51), r: 115.0288 },
    { pos: new mp.Vector3(-1163.427, 4925.770, 222.92), r: 174.1617 },
    { pos: new mp.Vector3(-254.4806, 3977.676, 51.023), r: 26.74220 },
    { pos: new mp.Vector3(-1880.507, 1969.815, 143.35), r: 344.8683 },
    { pos: new mp.Vector3(408.05270, 6545.170, 27.158), r: 8.149019 },
    { pos: new mp.Vector3(920.69946, 41.53931, 81.096), r: 59.76486 },
    { pos: new mp.Vector3(927.60620, 51.22679, 81.106), r: 46.95137 },
    { pos: new mp.Vector3(916.98699, 62.09719, 80.895), r: 230.3090 },
    { pos: new mp.Vector3(-83.68326, -822.621, 321.28), r: 110.3854 },
    { pos: new mp.Vector3(-323.0403, -1964.07, 66.666), r: 156.6973 },
    { pos: new mp.Vector3(901.49176, 13.70679, 78.861), r: 141.6151 },
    { pos: new mp.Vector3(-276.6125, 6269.523, 31.446), r: 44.34231 },
    { pos: new mp.Vector3(891.46704, 19.86932, 78.897), r: 107.3203 },
    { pos: new mp.Vector3(-280.9773, 6234.891, 31.489), r: 40.01850 },
    { pos: new mp.Vector3(1803.7318, 1768.639, 73.338), r: 30.33609 },
    { pos: new mp.Vector3(935.53253, 68.61181, 79.082), r: 135.5270 },
    { pos: new mp.Vector3(926.52868, 78.43154, 78.750), r: 53.97610 },
    { pos: new mp.Vector3(1511.3420, 1698.875, 110.76), r: 162.9683 },
    { pos: new mp.Vector3(1518.1992, 1732.108, 109.95), r: 358.7480 },
    { pos: new mp.Vector3(940.97729, 96.38748, 79.449), r: 358.4697 },
    { pos: new mp.Vector3(949.26855, 103.6759, 80.530), r: 135.0496 },
    { pos: new mp.Vector3(950.00738, 103.2687, 80.530), r: 184.3225 },
    { pos: new mp.Vector3(951.55865, 102.0370, 80.530), r: 159.8782 },
    { pos: new mp.Vector3(1031.0181, -33.2159, 76.225), r: 220.0598 },
    { pos: new mp.Vector3(-454.5097, 6055.384, 31.481), r: 208.2208 },
    { pos: new mp.Vector3(955.15563, 110.9101, 81.250), r: 62.57267 },
    { pos: new mp.Vector3(1038.0583, -51.9547, 76.549), r: 38.13391 },
    { pos: new mp.Vector3(960.73638, 114.5570, 81.464), r: 81.37625 },
    { pos: new mp.Vector3(1061.2984, -35.8975, 77.721), r: 15.65658 },
    { pos: new mp.Vector3(1055.1186, -18.7851, 78.812), r: 211.7440 },
    { pos: new mp.Vector3(966.62524, 118.9667, 81.690), r: 80.56646 },
    { pos: new mp.Vector3(1085.8127, -25.3267, 80.892), r: 7.774716 },
    { pos: new mp.Vector3(-424.3414, 6121.622, 31.492), r: 231.4360 },
    { pos: new mp.Vector3(1079.5196, -8.11201, 80.708), r: 251.4147 },
    { pos: new mp.Vector3(976.61035, 110.5263, 80.986), r: 232.6550 },
    { pos: new mp.Vector3(1092.0175, 5.592523, 80.890), r: 319.4329 },
    { pos: new mp.Vector3(1101.4945, -7.10654, 80.890), r: 105.4690 },
    { pos: new mp.Vector3(988.34790, 104.4944, 80.990), r: 30.30531 },
    { pos: new mp.Vector3(1114.2691, 13.05341, 80.889), r: 46.85173 },
    { pos: new mp.Vector3(1102.8748, 21.81273, 80.890), r: 285.3514 },
    { pos: new mp.Vector3(1129.6834, 33.51752, 80.887), r: 43.28148 },
    { pos: new mp.Vector3(1119.6151, 43.23714, 80.890), r: 300.1580 },
    { pos: new mp.Vector3(949.68872, 84.63579, 79.243), r: 11.64071 },
    { pos: new mp.Vector3(1095.9395, 104.7899, 81.466), r: 65.45544 },
    { pos: new mp.Vector3(1099.3458, 110.7172, 81.504), r: 29.40476 },
    { pos: new mp.Vector3(1101.8905, 115.8972, 81.626), r: 303.5042 },
    { pos: new mp.Vector3(-107.8851, 6528.590, 29.858), r: 2.755915 },
    { pos: new mp.Vector3(1108.3933, 125.3791, 81.688), r: 83.52918 },
    { pos: new mp.Vector3(882.42913, 9.138663, 78.908), r: 287.1299 },
    { pos: new mp.Vector3(1121.2779, 145.5197, 81.504), r: 19.84214 },
    { pos: new mp.Vector3(1140.9903, 174.5950, 81.273), r: 209.9237 },
    { pos: new mp.Vector3(895.48254, -0.52431, 78.903), r: 318.0639 },
    { pos: new mp.Vector3(-59.22793, 6451.337, 31.493), r: 44.69954 },
    { pos: new mp.Vector3(1187.9659, 187.4462, 79.987), r: 294.1802 },
    { pos: new mp.Vector3(883.01696, -11.7651, 78.895), r: 115.8847 },
    { pos: new mp.Vector3(1222.0754, 201.9234, 79.931), r: 12.40506 },
    { pos: new mp.Vector3(892.43829, -19.1801, 78.903), r: 251.0956 },
    { pos: new mp.Vector3(1232.1646, 225.2521, 79.677), r: 2.691177 },
    { pos: new mp.Vector3(899.52325, -25.5334, 78.764), r: 332.6253 },
    { pos: new mp.Vector3(1251.3631, 228.4686, 81.809), r: 134.2005 },
    { pos: new mp.Vector3(1238.6721, 202.0301, 81.105), r: 139.6808 },
    { pos: new mp.Vector3(909.80358, -30.5126, 78.822), r: 257.3190 },
    { pos: new mp.Vector3(1192.9816, 251.3442, 81.080), r: 73.76908 },
    { pos: new mp.Vector3(919.13806, -35.5494, 78.851), r: 6.944406 },
    { pos: new mp.Vector3(1127.5288, 159.4830, 81.838), r: 189.1283 },
    { pos: new mp.Vector3(929.57678, -41.0895, 78.849), r: 231.1764 },
    { pos: new mp.Vector3(501.33398, 5601.691, 796.72), r: 176.9872 },
    { pos: new mp.Vector3(1135.0195, 268.8423, 80.990), r: 318.0117 },
    { pos: new mp.Vector3(1162.5737, 294.5547, 80.980), r: 165.8673 },
    { pos: new mp.Vector3(920.02850, -73.6148, 78.849), r: 139.5713 },
    { pos: new mp.Vector3(912.74353, -85.1905, 78.819), r: 115.6167 },
    { pos: new mp.Vector3(919.96868, -91.0727, 78.848), r: 240.9924 },
    { pos: new mp.Vector3(1287.0793, 255.8447, 80.990), r: 81.06930 },
    { pos: new mp.Vector3(903.53009, -82.4183, 78.818), r: 297.7323 },
    { pos: new mp.Vector3(894.90539, -75.5951, 78.849), r: 70.88465 },
    { pos: new mp.Vector3(886.45520, -70.8354, 78.893), r: 266.1150 },
    { pos: new mp.Vector3(1239.2011, -58.7588, 63.045), r: 173.7168 },
    { pos: new mp.Vector3(876.59161, -64.5898, 78.894), r: 51.63510 },
    { pos: new mp.Vector3(867.88848, -58.9019, 78.873), r: 181.1156 },
    { pos: new mp.Vector3(1213.6219, -105.209, 58.792), r: 55.90012 },
    { pos: new mp.Vector3(1161.3742, -152.027, 56.233), r: 341.0350 },
    { pos: new mp.Vector3(859.43786, -53.6045, 78.894), r: 51.78122 },
    { pos: new mp.Vector3(1198.4079, -225.531, 56.617), r: 114.4258 },
    { pos: new mp.Vector3(851.45654, -45.4975, 78.848), r: 141.5287 },
    { pos: new mp.Vector3(1235.4489, -323.949, 70.066), r: 100.3638 },
    { pos: new mp.Vector3(841.70684, -37.7639, 78.834), r: 91.78744 },
    { pos: new mp.Vector3(1279.3459, -386.773, 69.256), r: 110.2798 },
    { pos: new mp.Vector3(-121.5076, 3635.820, 45.033), r: 233.3350 },
    { pos: new mp.Vector3(1304.1318, -452.898, 69.260), r: 135.4379 },
    { pos: new mp.Vector3(1295.3919, -507.841, 70.725), r: 55.72792 },
    { pos: new mp.Vector3(1315.1986, -650.919, 69.148), r: 140.6415 },
    { pos: new mp.Vector3(-363.5319, 6098.308, 31.496), r: 139.3235 },
    { pos: new mp.Vector3(1309.1665, -750.063, 65.703), r: 299.0411 },
    { pos: new mp.Vector3(-58.53376, 3617.575, 47.172), r: 256.8290 },
    { pos: new mp.Vector3(1362.9127, -795.812, 67.567), r: 313.5477 },
    { pos: new mp.Vector3(1232.5389, -823.363, 63.277), r: 87.32658 },
    { pos: new mp.Vector3(-21.34969, 3655.743, 41.306), r: 319.7376 },
    { pos: new mp.Vector3(1184.4691, -899.549, 59.506), r: 207.0130 },
    { pos: new mp.Vector3(-285.0212, 6323.690, 32.627), r: 260.8091 },
    { pos: new mp.Vector3(870.51031, -3.14763, 78.847), r: 264.6256 },
    { pos: new mp.Vector3(1330.1577, -1413.74, 62.820), r: 126.8724 },
    { pos: new mp.Vector3(21.788887, 3688.049, 39.691), r: 299.5661 },
    { pos: new mp.Vector3(1380.0589, -1447.15, 65.196), r: 172.0631 },
    { pos: new mp.Vector3(1473.6569, -1461.88, 71.743), r: 274.7946 },
    { pos: new mp.Vector3(546.92260, 260.1138, 103.09), r: 68.20655 },
    { pos: new mp.Vector3(1513.5017, -1489.77, 70.228), r: 84.31092 },
    { pos: new mp.Vector3(1473.9934, -1519.97, 64.703), r: 56.22478 },
    { pos: new mp.Vector3(1412.0944, -1560.62, 57.355), r: 23.61531 },
    { pos: new mp.Vector3(15.262588, 3745.470, 39.646), r: 127.1971 },
    { pos: new mp.Vector3(1510.0466, -1668.37, 83.894), r: 35.58708 },
    { pos: new mp.Vector3(1558.4064, -1601.80, 91.179), r: 96.33624 },
    { pos: new mp.Vector3(-239.7015, -1454.03, 31.320), r: 236.2861 },
    { pos: new mp.Vector3(1580.7105, -1563.60, 92.783), r: 319.5001 },
    { pos: new mp.Vector3(46.463146, 3756.150, 39.652), r: 228.3740 },
    { pos: new mp.Vector3(1636.2377, -1525.36, 107.69), r: 317.2944 },
    { pos: new mp.Vector3(-222.9837, -1467.04, 31.354), r: 231.5406 },
    { pos: new mp.Vector3(1647.0192, -1450.09, 108.70), r: 357.1395 },
    { pos: new mp.Vector3(1658.5501, -1414.18, 108.78), r: 330.4915 },
    { pos: new mp.Vector3(1668.7882, -1421.33, 110.98), r: 57.18169 },
    { pos: new mp.Vector3(-198.7301, -1485.37, 31.573), r: 232.1350 },
    { pos: new mp.Vector3(1716.5825, -1391.27, 112.04), r: 304.5037 },
    { pos: new mp.Vector3(-203.4545, -1497.24, 31.808), r: 130.9804 },
    { pos: new mp.Vector3(1774.6569, -1404.10, 110.88), r: 141.8335 },
    { pos: new mp.Vector3(1807.7625, -1438.73, 120.97), r: 120.2377 },
    { pos: new mp.Vector3(114.82115, 3716.835, 39.730), r: 189.5729 },
    { pos: new mp.Vector3(-219.9889, -1515.34, 31.574), r: 146.8510 },
    { pos: new mp.Vector3(1841.7344, -1450.54, 126.08), r: 32.28896 },
    { pos: new mp.Vector3(-230.2285, -1527.50, 31.791), r: 153.3287 },
    { pos: new mp.Vector3(1899.3779, -1310.56, 133.24), r: 317.1557 },
    { pos: new mp.Vector3(1917.6003, -1293.02, 131.82), r: 56.90071 },
    { pos: new mp.Vector3(-239.9068, -1539.82, 31.554), r: 213.2606 },
    { pos: new mp.Vector3(1918.1860, -1217.79, 119.68), r: 3.970567 },
    { pos: new mp.Vector3(-241.2288, -1546.58, 31.871), r: 200.0429 },
    { pos: new mp.Vector3(1958.8994, -1068.65, 94.253), r: 19.17775 },
    { pos: new mp.Vector3(-245.1862, -1565.99, 32.912), r: 162.2904 },
    { pos: new mp.Vector3(1992.7670, -975.647, 80.813), r: 279.1770 },
    { pos: new mp.Vector3(-247.2743, -1572.41, 32.697), r: 161.9951 },
    { pos: new mp.Vector3(-253.7250, -1572.61, 32.018), r: 286.8239 },
    { pos: new mp.Vector3(1983.7291, -922.566, 78.965), r: 91.25385 },
    { pos: new mp.Vector3(2045.0555, -876.445, 79.758), r: 189.3310 },
    { pos: new mp.Vector3(2130.8454, -832.970, 78.319), r: 281.7086 },
    { pos: new mp.Vector3(-257.9366, -1601.97, 32.001), r: 213.2707 },
    { pos: new mp.Vector3(-255.9235, -1615.30, 31.848), r: 254.9119 },
    { pos: new mp.Vector3(2445.6125, -691.069, 62.389), r: 15.73180 },
    { pos: new mp.Vector3(-255.2581, -1626.40, 31.919), r: 179.4939 },
    { pos: new mp.Vector3(2565.3852, -665.543, 54.447), r: 266.9649 },
    { pos: new mp.Vector3(2626.1281, -677.247, 46.516), r: 303.2362 },
    { pos: new mp.Vector3(-255.3872, -1642.85, 31.848), r: 184.6290 },
    { pos: new mp.Vector3(2673.1520, -671.473, 41.761), r: 273.5762 },
    { pos: new mp.Vector3(-270.3374, -1641.71, 31.983), r: 58.47595 },
    { pos: new mp.Vector3(2692.8488, -660.189, 34.270), r: 56.49068 },
    { pos: new mp.Vector3(-286.5630, -1637.80, 32.053), r: 84.23430 },
    { pos: new mp.Vector3(2689.4023, -705.250, 39.730), r: 143.9908 },
    { pos: new mp.Vector3(-302.4900, -1641.34, 32.185), r: 244.1325 },
    { pos: new mp.Vector3(2667.0493, -767.075, 37.255), r: 28.84913 },
    { pos: new mp.Vector3(-299.4423, -1633.14, 31.848), r: 342.6680 },
    { pos: new mp.Vector3(2648.3349, -794.947, 35.125), r: 242.6138 },
    { pos: new mp.Vector3(2689.7377, -789.211, 31.297), r: 250.8139 },
    { pos: new mp.Vector3(-290.5036, -1607.61, 32.016), r: 335.2294 },
    { pos: new mp.Vector3(2704.0310, -819.625, 27.336), r: 313.2993 },
    { pos: new mp.Vector3(-280.5807, -1598.20, 31.851), r: 248.4216 },
    { pos: new mp.Vector3(2735.7370, -760.245, 22.840), r: 43.55061 },
    { pos: new mp.Vector3(-270.2777, -1598.48, 31.848), r: 281.6748 },
    { pos: new mp.Vector3(2700.3120, -708.164, 18.936), r: 317.7568 },
    { pos: new mp.Vector3(2747.7536, -690.733, 10.288), r: 212.7348 },
    { pos: new mp.Vector3(-262.8034, -1584.46, 31.849), r: 351.2683 },
    { pos: new mp.Vector3(2790.6787, -731.329, 6.6067), r: 14.89555 },
    { pos: new mp.Vector3(439.57519, 6508.722, 28.643), r: 96.69898 },
    { pos: new mp.Vector3(-259.1806, -1567.51, 31.876), r: 308.7148 },
    { pos: new mp.Vector3(2823.7553, -650.539, 1.9023), r: 196.8764 },
    { pos: new mp.Vector3(1495.4038, 1176.735, 115.02), r: 131.2168 },
    { pos: new mp.Vector3(-267.5450, -1559.76, 32.024), r: 116.5819 },
    { pos: new mp.Vector3(2823.1845, -652.257, 1.9222), r: 217.2916 },
    { pos: new mp.Vector3(2825.2470, -654.155, 1.9234), r: 269.0144 },
    { pos: new mp.Vector3(-273.1887, -1577.54, 31.849), r: 143.6692 },
    { pos: new mp.Vector3(2825.8076, -653.030, 1.9088), r: 322.4948 },
    { pos: new mp.Vector3(2826.7519, -650.983, 1.8829), r: 130.7660 },
    { pos: new mp.Vector3(-241.8354, -1577.27, 33.675), r: 251.0121 },
    { pos: new mp.Vector3(2821.4912, -737.486, 2.2396), r: 247.4804 },
    { pos: new mp.Vector3(2821.0241, -739.498, 2.1455), r: 209.7045 },
    { pos: new mp.Vector3(2820.5102, -741.211, 2.0797), r: 191.7585 },
    { pos: new mp.Vector3(2820.2331, -743.720, 2.0824), r: 214.3513 },
    { pos: new mp.Vector3(2821.0742, -747.635, 2.2906), r: 218.9276 },
    { pos: new mp.Vector3(2824.1987, -754.282, 1.9503), r: 42.58386 },
    { pos: new mp.Vector3(2790.0920, -763.954, 8.2538), r: 176.6552 },
    { pos: new mp.Vector3(2768.7075, -854.566, 11.895), r: 163.2853 },
    { pos: new mp.Vector3(2709.3085, -857.837, 23.676), r: 64.81821 },
    { pos: new mp.Vector3(2650.4816, -835.143, 35.990), r: 316.5130 },
    { pos: new mp.Vector3(2682.8352, -866.567, 25.699), r: 257.8855 },
    { pos: new mp.Vector3(2627.3730, -987.246, 28.785), r: 232.8800 },
    { pos: new mp.Vector3(1436.4062, 1021.515, 114.16), r: 139.1747 },
    { pos: new mp.Vector3(2647.5725, -998.331, 21.378), r: 93.89533 },
    { pos: new mp.Vector3(2669.9228, -1019.20, 1.8376), r: 293.4992 },
    { pos: new mp.Vector3(2509.0319, -1221.12, 3.0043), r: 261.6720 },
    { pos: new mp.Vector3(2511.0124, -1223.33, 2.8411), r: 264.6038 },
    { pos: new mp.Vector3(2513.7353, -1223.38, 2.7158), r: 351.9867 },
    { pos: new mp.Vector3(2513.6496, -1220.64, 2.7441), r: 100.0014 },
    { pos: new mp.Vector3(2511.2683, -1219.97, 2.8469), r: 202.0997 },
    { pos: new mp.Vector3(2507.5354, -1219.07, 3.0703), r: 238.3830 },
    { pos: new mp.Vector3(2511.8310, -1216.27, 2.8538), r: 210.5663 },
    { pos: new mp.Vector3(154.56536, 3351.005, 48.833), r: 241.0651 },
    { pos: new mp.Vector3(2481.5415, -1657.58, 35.507), r: 153.4309 },
    { pos: new mp.Vector3(2525.0974, -1751.21, 37.257), r: 199.9512 },
    { pos: new mp.Vector3(89.993423, 3383.513, 49.472), r: 99.29438 },
    { pos: new mp.Vector3(176.31735, 7031.146, 2.1693), r: 322.5025 },
    { pos: new mp.Vector3(2431.7871, -1756.55, 58.942), r: 176.7024 },
    { pos: new mp.Vector3(2427.4665, -1634.73, 47.948), r: 331.5103 },
    { pos: new mp.Vector3(2772.4387, -1561.05, 4.2200), r: 292.0025 },
    { pos: new mp.Vector3(2816.8974, -1498.32, 12.165), r: 325.5896 },
    { pos: new mp.Vector3(2820.2998, -1455.52, 9.3129), r: 322.7078 },
    { pos: new mp.Vector3(2852.7087, -1414.14, 13.504), r: 354.4180 },
    { pos: new mp.Vector3(2855.1650, -1339.37, 15.630), r: 27.45296 },
    { pos: new mp.Vector3(2857.5305, -1317.14, 10.363), r: 353.6169 },
    { pos: new mp.Vector3(-52.91240, 2881.009, 59.184), r: 337.1400 },
    { pos: new mp.Vector3(3039.3984, -301.107, 13.451), r: 357.1644 },
    { pos: new mp.Vector3(3143.5895, -77.4764, 19.738), r: 327.2191 },
    { pos: new mp.Vector3(3289.4912, -145.017, 16.009), r: 216.2173 },
    { pos: new mp.Vector3(1753.7200, 6383.575, 36.421), r: 242.5226 },
    { pos: new mp.Vector3(3254.5249, -184.416, 22.837), r: 149.4002 },
    { pos: new mp.Vector3(3376.8500, -46.6154, 5.4361), r: 307.9735 },
    { pos: new mp.Vector3(3388.5173, 0.087090, 3.7193), r: 182.7235 },
    { pos: new mp.Vector3(2112.8039, 6019.586, 50.948), r: 62.88439 },
    { pos: new mp.Vector3(3331.0493, -272.258, 5.6446), r: 251.6496 },
    { pos: new mp.Vector3(15.080938, 2061.882, 161.31), r: 215.6972 },
    { pos: new mp.Vector3(2123.0744, 6033.404, 50.926), r: 50.48737 },
    { pos: new mp.Vector3(-57.50678, 2017.380, 177.56), r: 104.0898 },
    { pos: new mp.Vector3(144.77418, 1672.354, 228.61), r: 219.8925 },
    { pos: new mp.Vector3(3067.4843, -4824.03, 15.261), r: 354.5431 },
    { pos: new mp.Vector3(2043.1540, 6136.660, 49.085), r: 225.0649 },
    { pos: new mp.Vector3(3048.8911, -4781.49, 15.259), r: 14.97333 },
    { pos: new mp.Vector3(739.43933, 1302.219, 360.29), r: 314.1937 },
    { pos: new mp.Vector3(3034.8898, -4737.50, 15.261), r: 249.2450 },
    { pos: new mp.Vector3(751.01000, 1316.137, 359.85), r: 234.4711 },
    { pos: new mp.Vector3(1860.1694, 6367.725, 41.019), r: 71.85904 },
    { pos: new mp.Vector3(3081.3291, -4629.31, 15.261), r: 64.64973 },
    { pos: new mp.Vector3(760.23449, 1302.445, 360.29), r: 315.0750 },
    { pos: new mp.Vector3(3093.4011, -4696.40, 15.262), r: 303.9501 },
    { pos: new mp.Vector3(1491.6989, 6412.780, 22.262), r: 350.8856 },
    { pos: new mp.Vector3(3126.9096, -4798.92, 15.261), r: 108.8334 },
    { pos: new mp.Vector3(1479.0186, 6415.689, 22.238), r: 358.4728 },
    { pos: new mp.Vector3(3081.3471, -4831.30, 15.256), r: 13.99006 },
    { pos: new mp.Vector3(1468.0291, 6511.954, 20.732), r: 178.1139 },
    { pos: new mp.Vector3(782.96392, 1274.351, 360.29), r: 3.273754 },
    { pos: new mp.Vector3(808.68133, 1266.715, 360.41), r: 136.6923 },
    { pos: new mp.Vector3(-930.0797, 5434.100, 37.510), r: 159.4134 },
    { pos: new mp.Vector3(840.19873, 1299.223, 364.02), r: 317.0927 },
    { pos: new mp.Vector3(843.83465, 1366.936, 349.38), r: 15.49732 },
    { pos: new mp.Vector3(-2184.030, 217.0986, 198.59), r: 200.4543 },
    { pos: new mp.Vector3(491.18023, 1441.801, 351.63), r: 80.64077 },
    { pos: new mp.Vector3(363.69241, 1393.659, 313.19), r: 138.5682 },
    { pos: new mp.Vector3(55.554992, 1185.177, 270.14), r: 266.9597 },
    { pos: new mp.Vector3(-982.1614, 5413.631, 40.015), r: 297.6072 },
    { pos: new mp.Vector3(-2281.234, 202.8495, 167.60), r: 122.8157 },
    { pos: new mp.Vector3(-2270.044, 178.1676, 167.60), r: 27.82790 },
    { pos: new mp.Vector3(-1040.226, 5328.871, 44.358), r: 42.43850 },
    { pos: new mp.Vector3(-2282.580, 166.9140, 167.60), r: 108.9375 },
    { pos: new mp.Vector3(-1055.271, 5321.893, 45.366), r: 74.98544 },
    { pos: new mp.Vector3(-2276.442, -242.331, 64.319), r: 8.757032 },
    { pos: new mp.Vector3(-2262.493, -194.440, 77.272), r: 165.9575 },
    { pos: new mp.Vector3(-2254.436, -112.516, 97.430), r: 321.1708 },
    { pos: new mp.Vector3(-2239.420, -82.5862, 108.86), r: 81.12023 },
    { pos: new mp.Vector3(-2281.366, 14.81814, 123.03), r: 14.27828 },
    { pos: new mp.Vector3(-160.4568, -1549.62, 35.017), r: 214.5812 },
    { pos: new mp.Vector3(-2254.380, 71.34197, 146.92), r: 289.6583 },
    { pos: new mp.Vector3(-2211.376, 99.92501, 160.94), r: 318.8439 },
    { pos: new mp.Vector3(-154.6711, -1541.54, 35.037), r: 250.1216 },
    { pos: new mp.Vector3(-2198.964, 117.7946, 165.92), r: 261.8738 },
    { pos: new mp.Vector3(-1481.420, 5002.979, 62.858), r: 126.4919 },
    { pos: new mp.Vector3(-2144.174, 135.1242, 167.52), r: 272.0504 },
    { pos: new mp.Vector3(-2160.787, 210.3890, 169.03), r: 11.04465 },
    { pos: new mp.Vector3(-173.7541, -1566.12, 35.258), r: 158.0393 },
    { pos: new mp.Vector3(-182.8982, -1578.80, 35.539), r: 158.0657 },
    { pos: new mp.Vector3(-1492.126, 4974.520, 63.763), r: 92.98439 },
    { pos: new mp.Vector3(-191.3689, -1589.62, 34.789), r: 177.4660 },
    { pos: new mp.Vector3(-190.8194, -1589.13, 34.753), r: 340.7287 },
    { pos: new mp.Vector3(-188.2407, -1599.11, 34.430), r: 201.0507 },
    { pos: new mp.Vector3(-189.4467, 1054.226, 233.17), r: 151.3895 },
    { pos: new mp.Vector3(-191.4216, -1617.33, 33.855), r: 197.2491 },
    { pos: new mp.Vector3(-175.2023, -1623.38, 33.566), r: 165.9617 },
    { pos: new mp.Vector3(-1563.050, 4963.287, 62.038), r: 293.6448 },
    { pos: new mp.Vector3(-1490.378, 4985.395, 63.134), r: 84.11457 },
    { pos: new mp.Vector3(-2510.016, 252.3915, 173.00), r: 60.98905 },
    { pos: new mp.Vector3(-2553.528, 287.1137, 187.30), r: 60.98905 },
    { pos: new mp.Vector3(-2545.058, 259.9291, 182.88), r: 270.9239 },
    { pos: new mp.Vector3(-2590.490, 365.4026, 211.38), r: 294.3927 },
    { pos: new mp.Vector3(-242.2119, 1052.240, 235.56), r: 71.83702 },
    { pos: new mp.Vector3(-2504.986, 757.5464, 302.01), r: 268.6179 },
    { pos: new mp.Vector3(-248.9658, 1035.052, 234.95), r: 181.6526 },
    { pos: new mp.Vector3(-190.6017, -1654.89, 33.659), r: 201.8329 },
    { pos: new mp.Vector3(-176.0874, -1658.59, 33.357), r: 82.17790 },
    { pos: new mp.Vector3(-175.4917, -1675.12, 33.264), r: 154.3829 },
    { pos: new mp.Vector3(-166.7686, -1692.83, 32.486), r: 178.6917 },
    { pos: new mp.Vector3(-2365.256, 3286.114, 32.858), r: 316.2236 },
    { pos: new mp.Vector3(-149.6489, -1708.40, 30.444), r: 186.7985 },
    { pos: new mp.Vector3(-2372.504, 3292.850, 32.826), r: 13.69085 },
    { pos: new mp.Vector3(-153.3140, -1735.18, 30.131), r: 172.7952 },
    { pos: new mp.Vector3(-325.5298, 987.8415, 233.38), r: 167.8660 },
    { pos: new mp.Vector3(-159.6443, -1741.71, 30.098), r: 173.2705 },
    { pos: new mp.Vector3(-342.2314, 975.9031, 233.32), r: 116.2881 },
    { pos: new mp.Vector3(-171.7593, -1758.89, 29.890), r: 169.2327 },
    { pos: new mp.Vector3(-2371.973, 3361.267, 32.832), r: 331.9723 },
    { pos: new mp.Vector3(-181.9929, -1767.55, 29.803), r: 279.1370 },
    { pos: new mp.Vector3(-2361.779, 3355.190, 32.832), r: 336.3839 },
    { pos: new mp.Vector3(-2235.322, 3480.819, 30.246), r: 171.1199 },
    { pos: new mp.Vector3(-2203.267, 3479.474, 30.127), r: 191.0220 },
    { pos: new mp.Vector3(-2106.528, 3455.394, 30.291), r: 181.1168 },
    { pos: new mp.Vector3(-2020.227, 3384.996, 31.126), r: 74.96928 },
    { pos: new mp.Vector3(-88.75854, -1788.02, 28.674), r: 305.0091 },
    { pos: new mp.Vector3(-78.27782, -1796.13, 28.142), r: 255.8836 },
    { pos: new mp.Vector3(-69.90018, -1805.07, 27.650), r: 314.9594 },
    { pos: new mp.Vector3(-52.03549, -1817.91, 26.771), r: 302.5576 },
    { pos: new mp.Vector3(-1599.919, 2806.083, 17.182), r: 37.08495 },
    { pos: new mp.Vector3(-1633.173, 2862.913, 24.880), r: 148.3144 },
    { pos: new mp.Vector3(-35.37038, -1833.44, 26.011), r: 277.7959 },
    { pos: new mp.Vector3(-18.15268, -1847.56, 25.146), r: 275.0884 },
    { pos: new mp.Vector3(-1650.682, 2865.468, 26.792), r: 302.3770 },
    { pos: new mp.Vector3(-9.955548, -1853.65, 24.720), r: 267.4518 },
    { pos: new mp.Vector3(-1668.839, 2887.016, 30.165), r: 234.9211 },
    { pos: new mp.Vector3(-2.905713, -1859.77, 24.341), r: 312.5495 },
    { pos: new mp.Vector3(8.6305494, -1869.72, 23.665), r: 306.9275 },
    { pos: new mp.Vector3(-1704.584, 2847.967, 32.850), r: 127.3516 },
    { pos: new mp.Vector3(23.229627, -1881.49, 22.979), r: 256.4412 },
    { pos: new mp.Vector3(-2075.584, 2798.288, 32.956), r: 289.4153 },
    { pos: new mp.Vector3(38.751010, -1895.32, 22.050), r: 271.1884 },
    { pos: new mp.Vector3(47.406539, -1902.32, 21.628), r: 254.3713 },
    { pos: new mp.Vector3(-2108.664, 2796.024, 32.805), r: 111.7398 },
    { pos: new mp.Vector3(-385.7373, 765.6100, 218.86), r: 238.3523 },
    { pos: new mp.Vector3(-2136.304, 2812.553, 32.802), r: 155.4564 },
    { pos: new mp.Vector3(-376.8937, 746.0996, 207.20), r: 191.2602 },
    { pos: new mp.Vector3(-2078.609, 2788.130, 32.784), r: 3.192505 },
    { pos: new mp.Vector3(-282.6079, 719.2984, 205.64), r: 231.3870 },
    { pos: new mp.Vector3(-2091.061, 2789.109, 32.834), r: 24.36187 },
    { pos: new mp.Vector3(-248.0149, 689.1607, 199.84), r: 220.0851 },
    { pos: new mp.Vector3(-2361.958, 3242.963, 97.876), r: 150.8973 },
    { pos: new mp.Vector3(-207.9483, 595.0774, 192.86), r: 226.9112 },
    { pos: new mp.Vector3(-2364.419, 3254.457, 91.806), r: 58.49402 },
    { pos: new mp.Vector3(30.608352, 351.8572, 112.57), r: 241.7232 },
    { pos: new mp.Vector3(223.64576, 407.7571, 117.30), r: 256.9616 },
    { pos: new mp.Vector3(-2080.125, 3282.898, 32.810), r: 174.5406 },
    { pos: new mp.Vector3(592.56744, 327.4791, 120.45), r: 318.9778 },
    { pos: new mp.Vector3(-2078.994, 3282.714, 36.791), r: 175.4391 },
    { pos: new mp.Vector3(-2078.989, 3282.730, 40.809), r: 179.8975 },
    { pos: new mp.Vector3(-2078.951, 3282.876, 44.837), r: 188.1874 },
    { pos: new mp.Vector3(-1947.833, 3201.176, 32.810), r: 35.47519 },
    { pos: new mp.Vector3(-1950.598, 3207.556, 32.810), r: 219.4980 },
    { pos: new mp.Vector3(-1948.874, 3207.764, 36.812), r: 198.1901 },
    { pos: new mp.Vector3(-1948.875, 3207.787, 40.831), r: 207.9259 },
    { pos: new mp.Vector3(-1948.861, 3207.851, 44.859), r: 216.1795 },
    { pos: new mp.Vector3(-1676.549, 3222.944, 32.960), r: 64.34824 },
    { pos: new mp.Vector3(-1740.227, 2933.478, 32.935), r: 327.1431 },
    { pos: new mp.Vector3(-1744.958, 2925.922, 32.935), r: 148.2116 },
    { pos: new mp.Vector3(-1753.555, 2910.929, 32.946), r: 307.7215 },
    { pos: new mp.Vector3(-1757.883, 2903.593, 32.948), r: 149.7917 },
    { pos: new mp.Vector3(-1765.108, 2887.118, 32.956), r: 287.3466 },
    { pos: new mp.Vector3(-1767.426, 2882.370, 32.955), r: 151.6774 },
    { pos: new mp.Vector3(-1777.958, 2866.175, 32.954), r: 345.3091 },
    { pos: new mp.Vector3(-1782.991, 2858.691, 32.955), r: 145.6358 },
    { pos: new mp.Vector3(-1791.046, 2845.631, 32.950), r: 3.464322 },
    { pos: new mp.Vector3(-1794.301, 2839.820, 32.954), r: 148.7424 },
    { pos: new mp.Vector3(-2343.574, 3261.230, 32.827), r: 301.9155 },
    { pos: new mp.Vector3(-2340.526, 3268.207, 32.827), r: 226.5239 },
    { pos: new mp.Vector3(-2381.604, 3365.626, 32.819), r: 332.3601 },
    { pos: new mp.Vector3(-2391.235, 3370.299, 32.817), r: 325.8591 },
    { pos: new mp.Vector3(-2400.067, 3374.877, 32.814), r: 333.2671 },
    { pos: new mp.Vector3(-2383.283, 3385.528, 32.832), r: 143.3316 },
    { pos: new mp.Vector3(-2375.020, 3380.078, 32.832), r: 146.3507 },
    { pos: new mp.Vector3(-2365.307, 3375.564, 32.832), r: 149.5554 },
    { pos: new mp.Vector3(-2355.820, 3370.405, 32.832), r: 148.5648 },
    { pos: new mp.Vector3(-2346.376, 3363.774, 32.832), r: 150.0194 },
    { pos: new mp.Vector3(-2337.041, 3358.973, 32.832), r: 149.3361 },
    { pos: new mp.Vector3(-2327.994, 3353.611, 32.832), r: 146.9508 },
    { pos: new mp.Vector3(-2318.889, 3348.227, 32.834), r: 153.4035 },
    { pos: new mp.Vector3(-2309.490, 3342.958, 32.834), r: 148.9856 },
    { pos: new mp.Vector3(-2319.019, 3316.969, 32.829), r: 149.6732 },
    { pos: new mp.Vector3(-2327.192, 3322.253, 32.830), r: 151.3347 },
    { pos: new mp.Vector3(-2336.368, 3327.406, 32.832), r: 153.4349 },
    { pos: new mp.Vector3(-2331.117, 3336.632, 32.832), r: 328.0744 },
    { pos: new mp.Vector3(-2321.544, 3332.429, 32.832), r: 138.5188 },
    { pos: new mp.Vector3(-2312.936, 3327.429, 32.832), r: 350.8272 },
    { pos: new mp.Vector3(-2294.470, 3325.197, 32.994), r: 337.1697 },
    { pos: new mp.Vector3(-2241.423, 3325.001, 33.000), r: 317.2358 },
    { pos: new mp.Vector3(-2139.633, 3179.120, 32.810), r: 330.1036 },
    { pos: new mp.Vector3(-2074.670, 3141.254, 32.810), r: 300.9768 },
    { pos: new mp.Vector3(-2008.984, 3104.010, 32.810), r: 322.7227 },
    { pos: new mp.Vector3(-1943.854, 3066.450, 32.810), r: 310.3275 },
    { pos: new mp.Vector3(-2168.313, 3129.456, 32.810), r: 190.0836 },
    { pos: new mp.Vector3(-2102.744, 3092.692, 32.810), r: 160.3163 },
    { pos: new mp.Vector3(-2037.041, 3055.289, 32.810), r: 146.7285 },
    { pos: new mp.Vector3(-1972.172, 3017.451, 32.810), r: 148.2413 },
    { pos: new mp.Vector3(-2141.450, 2994.948, 32.810), r: 27.55607 },
    { pos: new mp.Vector3(-149.8464, 6202.880, 31.320), r: 48.53041 },
    { pos: new mp.Vector3(-2439.302, 3280.945, 32.977), r: 282.9680 },
    { pos: new mp.Vector3(-1938.071, 3304.948, 32.960), r: 199.7890 },
    { pos: new mp.Vector3(-1943.885, 3298.699, 32.960), r: 53.40822 },
    { pos: new mp.Vector3(-1932.235, 3270.288, 32.956), r: 350.7068 },
    { pos: new mp.Vector3(-1915.094, 3263.183, 32.915), r: 22.99234 },
    { pos: new mp.Vector3(-1906.185, 3278.332, 32.734), r: 19.92389 },
    { pos: new mp.Vector3(-1903.371, 3284.118, 32.922), r: 226.1415 },
    { pos: new mp.Vector3(-1890.698, 3304.347, 32.926), r: 178.9070 },
    { pos: new mp.Vector3(-1881.891, 3321.300, 32.959), r: 349.3905 },
    { pos: new mp.Vector3(-3406.126, 968.8585, 8.2915), r: 289.8878 },
    { pos: new mp.Vector3(-3405.996, 966.2838, 8.2915), r: 244.4705 },
    { pos: new mp.Vector3(-1927.163, 3345.005, 32.960), r: 127.5411 },
    { pos: new mp.Vector3(-3412.116, 956.3339, 8.3466), r: 338.8762 },
    { pos: new mp.Vector3(-1962.205, 3360.291, 32.960), r: 202.8517 },
    { pos: new mp.Vector3(-3427.511, 965.5466, 8.3466), r: 283.8592 },
    { pos: new mp.Vector3(-1691.004, 2795.110, 29.141), r: 170.1879 },
    { pos: new mp.Vector3(-1586.473, 2419.609, 23.933), r: 287.3660 },
    { pos: new mp.Vector3(-1620.689, 2366.771, 37.193), r: 325.2353 },
    { pos: new mp.Vector3(-2519.008, 2308.825, 33.215), r: 348.8332 },
    { pos: new mp.Vector3(-2584.414, 2340.170, 30.264), r: 198.4688 },
    { pos: new mp.Vector3(-2639.151, 2335.746, 21.743), r: 101.6450 },
    { pos: new mp.Vector3(-2680.098, 2422.801, 2.9832), r: 82.25402 },
    { pos: new mp.Vector3(-2646.310, 2696.706, 2.7539), r: 357.5067 },
    { pos: new mp.Vector3(-2644.086, 2743.652, 2.8442), r: 201.4332 },
    { pos: new mp.Vector3(-2629.606, 2832.352, 3.6042), r: 344.5465 },
    { pos: new mp.Vector3(-2618.312, 2926.353, 6.9848), r: 8.597662 },
    { pos: new mp.Vector3(-2604.474, 2951.068, 10.837), r: 128.9340 },
    { pos: new mp.Vector3(-2702.136, 3060.897, 10.555), r: 119.8871 },
    { pos: new mp.Vector3(-2796.413, 3088.584, 8.2453), r: 261.8073 },
    { pos: new mp.Vector3(-2881.781, 3202.343, 12.434), r: 139.5102 },
    { pos: new mp.Vector3(-2050.253, 4496.673, 55.178), r: 116.7239 },
    { pos: new mp.Vector3(-1994.224, 4524.485, 44.845), r: 140.2646 },
    { pos: new mp.Vector3(-1803.416, 4616.864, 1.9611), r: 152.3757 },
    { pos: new mp.Vector3(-680.8717, 3615.389, 292.44), r: 202.7715 },
    { pos: new mp.Vector3(-217.0853, 3646.862, 52.197), r: 260.6284 },
    { pos: new mp.Vector3(-150.0461, 3650.429, 39.103), r: 139.2841 },
    { pos: new mp.Vector3(-205.9833, 3599.871, 54.295), r: 333.5534 },
    { pos: new mp.Vector3(-805.1229, 2844.815, 17.989), r: 185.3774 },
    { pos: new mp.Vector3(-1234.194, 2730.919, 9.7576), r: 163.7457 },
    { pos: new mp.Vector3(-1379.677, 2601.109, 2.9138), r: 42.16873 },
    { pos: new mp.Vector3(-158.3370, -594.975, 167.00), r: 128.3143 },
    { pos: new mp.Vector3(-156.7032, -597.048, 167.00), r: 157.1754 },
    { pos: new mp.Vector3(-155.1515, -599.018, 167.00), r: 130.1488 },
    { pos: new mp.Vector3(-153.3945, -600.756, 167.00), r: 139.5891 },
    { pos: new mp.Vector3(85.580390, -371.715, 42.033), r: 165.7385 },
    { pos: new mp.Vector3(-25.12524, -1087.12, 26.573), r: 21.70269 },
    { pos: new mp.Vector3(147.33522, -1341.79, 29.202), r: 0.724955 },
    { pos: new mp.Vector3(-16.07858, -1424.63, 30.643), r: 156.0847 },
    { pos: new mp.Vector3(254.88482, -2214.95, 9.1988), r: 349.5660 },
    { pos: new mp.Vector3(249.30238, -2388.18, 5.9976), r: 214.2153 },
    { pos: new mp.Vector3(307.24533, -2478.65, 6.0766), r: 164.5162 },
    { pos: new mp.Vector3(319.34136, -2573.01, 6.0674), r: 154.2249 },
    { pos: new mp.Vector3(-3066.898, 602.3258, 2.4583), r: 34.45008 },
    { pos: new mp.Vector3(-3276.432, 956.1422, 3.4986), r: 39.30133 },
    { pos: new mp.Vector3(-3212.275, 1263.030, 4.7161), r: 75.96283 },
    { pos: new mp.Vector3(-3083.187, 1455.938, 20.537), r: 249.3422 },
    { pos: new mp.Vector3(-2725.801, 2328.504, 17.274), r: 270.3160 },
]
