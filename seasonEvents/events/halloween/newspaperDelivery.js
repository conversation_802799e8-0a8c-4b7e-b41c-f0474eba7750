
const weaponName = 'weapon_statuehalloween'

const duration = 20;

const pointSettings = [
    {
        markerId: 42,
        scale: 1.5,
        color: [126, 0, 0, 95]
    },
    {
        blipId: 1,
        color: 6,
        alpha: 255,
        shortRange: false,
    },
]

const MIN_PROGRESS_FOR_DONE = 15;
const failCooldown = 3;
const completeCooldown = 8;


const positions = [
    [
		new mp.Vector3(1286.7957, -1604.9275, 61.3231),
		new mp.Vector3(1230.6593, -1588.1143, 59.1326),
		new mp.Vector3(1196.8352, -1664.8088, 47.7927),
		new mp.Vector3(1261.3846, -1765.7406, 55.5773),
		new mp.Vector3(1290.0659, -1706.5450, 64.0864),
		new mp.Vector3(1346.2946, -1691.3802, 65.2828),
		new mp.Vector3(1333.9780, -1567.7406, 59.6381),
		new mp.Vector3(1339.2660, -1521.4550, 60.0088),
		new mp.Vector3(1436.5582, -1493.1560, 70.9949),
		new mp.Vector3(1378.8923, -1514.2682, 63.4967),
		new mp.Vector3(1367.9868, -1734.3693, 71.0454),
		new mp.Vector3(1369.0945, -1833.3627, 64.3224),
		new mp.Vector3(1296.9099, -1742.6637, 61.1377),
		new mp.Vector3(1193.3275, -1769.5912, 45.5179),
		new mp.Vector3(1194.8176, -1620.8835, 50.8257),
	],
	[ 
		new mp.Vector3(1389.9297, -571.2132, 82.9077),
		new mp.Vector3(1340.6901, -598.1011, 79.7230),
		new mp.Vector3(1328.8879, -535.4374, 77.2799),
		new mp.Vector3(1247.5780, -621.3362, 75.8308),
		new mp.Vector3(1204.8792, -556.2462, 74.5165),
		new mp.Vector3(1223.6044, -669.7978, 68.8381),
		new mp.Vector3(1118.4923, -646.6418, 63.7325),
		new mp.Vector3(1091.5516, -485.0769, 71.6183),
		new mp.Vector3(1101.2307, -413.4330, 76.0330),
		new mp.Vector3(1057.7274, -378.0000, 74.3143),
		new mp.Vector3(981.7846, -433.4769, 70.9780),
		new mp.Vector3(908.1099, -488.5450, 67.8271),
		new mp.Vector3(848.0967, -532.8132, 64.2550),
		new mp.Vector3(958.5231, -668.4264, 67.0183),
		new mp.Vector3(995.4066, -730.5758, 63.4293),
	], 
	[
		new mp.Vector3(524.1758, 3083.3142, 46.7817),
		new mp.Vector3(415.5692, 2965.2922, 47.6410),
		new mp.Vector3(193.9648, 3029.5781, 49.0732),
		new mp.Vector3(243.1516, 3103.4241, 47.9106),
		new mp.Vector3(164.1099, 3127.3054, 51.4491),
		new mp.Vector3(248.5714, 3170.5715, 47.9275),
		new mp.Vector3(-38.0308, 2871.8506, 64.7942),
		new mp.Vector3(-23.3143, 3031.4109, 46.4952),
		new mp.Vector3(395.4330, 3578.3867, 39.6036),
		new mp.Vector3(456.9099, 3560.1362, 40.0754),
		new mp.Vector3(914.3340, 3563.3669, 40.1091),
		new mp.Vector3(995.0769, 3573.8110, 40.4967),
		new mp.Vector3(961.9385, 3626.3340, 38.2557),
		new mp.Vector3(911.0374, 3644.6770, 37.6154),
		new mp.Vector3(866.0835, 3674.0571, 38.4073),
	],
    [
        new mp.Vector3(-180.719, 593.80321, 199.628),
		new mp.Vector3(-246.1582, 621.0066, 194.3356),
        new mp.Vector3(-225.869, 592.84611, 192.228),
		new mp.Vector3(-324.3297, 622.1671, 177.9912),
        new mp.Vector3(-247.484, 616.56541, 189.810),
		new mp.Vector3(-445.1604, 684.5670, 158.5970),
        new mp.Vector3(-292.802, 605.46011, 182.803),
        new mp.Vector3(-309.806, 641.74261, 178.129),
		new mp.Vector3(-556.4967, 662.0308, 150.7957),
        new mp.Vector3(-398.157, 670.62431, 164.883),
        new mp.Vector3(-445.011, 683.88491, 154.951),
		new mp.Vector3(-702.9495, 589.4901, 148.6388),
        new mp.Vector3(-472.475, 651.75431, 146.188),
        new mp.Vector3(-555.485, 664.71211, 147.157),
        new mp.Vector3(-611.180, 676.84971, 151.698),
    ],
    [
        new mp.Vector3(-26.6464, -1850.353, 27.7030),
		new mp.Vector3(-3.8901, -1871.6176, 29.0725),
        new mp.Vector3(-0.90750, -1870.297, 25.9283),
        new mp.Vector3(43.89150, -1868.554, 24.8890),
		new mp.Vector3(47.5912, -1866.4615, 28.5670),
        new mp.Vector3(55.61330, -1919.303, 23.7081),
		new mp.Vector3(71.3539, -1936.7076, 26.1238),
        new mp.Vector3(99.29810, -1915.021, 23.0190),
        new mp.Vector3(113.4599, -1927.163, 22.7483),
		new mp.Vector3(126.4615, -1930.8000, 26.4945),
        new mp.Vector3(111.6053, -1958.428, 22.8267),
        new mp.Vector3(87.12810, -1952.830, 22.8455),
		new mp.Vector3(114.7121, -1961.1956, 26.1238),
        new mp.Vector3(81.02620, -1943.946, 22.9625),
        new mp.Vector3(74.68410, -1937.455, 23.0063),
    ],
    [
        new mp.Vector3(328.2229, -1942.653, 26.7725),
		new mp.Vector3(341.1297, -1977.7318, 29.5948),	
        new mp.Vector3(315.2118, -1956.995, 26.3297),
		new mp.Vector3(289.9385, -2031.4022, 24.8938),
        new mp.Vector3(297.9071, -1975.036, 24.3805),
		new mp.Vector3(270.2374, -1917.8242, 30.9092),
        new mp.Vector3(288.6981, -1987.357, 23.1680),
		new mp.Vector3(255.7055, -2023.9121, 24.1523),
        new mp.Vector3(260.8975, -2023.155, 20.8033),
		new mp.Vector3(311.1692, -1956.3561, 29.5948),
        new mp.Vector3(241.8344, -2044.591, 20.0126),
        new mp.Vector3(221.8173, -2036.032, 19.9974),
        new mp.Vector3(266.9964, -1915.573, 27.8723),
        new mp.Vector3(246.9169, -1934.483, 26.3781),
        new mp.Vector3(200.4261, -1896.878, 26.2538),
    ],
    [
        new mp.Vector3(-434.725, 6259.4414, 32.2097),
	    new mp.Vector3(-357.4550, 6339.6001, 35.0205),
        new mp.Vector3(-399.405, 6311.8564, 30.8392),
		new mp.Vector3(-273.2176, 6352.5098, 38.4579),
        new mp.Vector3(-378.843, 6257.4048, 33.4873),
		new mp.Vector3(-216.0264, 6397.1343, 38.6937),		
        new mp.Vector3(-357.535, 6332.3975, 31.8358),
		new mp.Vector3(-24.7121, 6597.5869, 38.0872),
        new mp.Vector3(-305.326, 6333.8735, 34.4637),
		new mp.Vector3(55.0418, 6646.2197, 37.8176),				
        new mp.Vector3(-270.216, 6396.9507, 32.9396),
        new mp.Vector3(-208.253, 6442.7959, 33.2444),
        new mp.Vector3(-191.020, 6413.6431, 33.9135),
        new mp.Vector3(-128.251, 6549.4146, 31.4915),
        new mp.Vector3(-107.215, 6531.6792, 31.8582),
    ],
    [
        new mp.Vector3(-3211.56, 910.94731, 15.9860),
		new mp.Vector3(-3228.0132, 924.2637, 21.4564),
        new mp.Vector3(-3226.31, 929.02161, 15.9246),
		new mp.Vector3(-3237.5869, 949.8066, 20.7488),
        new mp.Vector3(-3234.89, 951.87691, 15.2013),
		new mp.Vector3(-3240.4878, 1038.1318, 17.5809),	
        new mp.Vector3(-3246.71, 1026.9492, 13.7577),
		new mp.Vector3(-3237.6001, 1052.9934, 16.1992),		
        new mp.Vector3(-3229.29, 1081.2852, 12.8093),
		new mp.Vector3(-3223.8198, 1104.1714, 15.5927),		
        new mp.Vector3(-3196.33, 1161.3246, 11.65961),
        new mp.Vector3(-3187.93, 1203.3719, 11.48561),
        new mp.Vector3(-3184.75, 1223.5190, 12.0155),
        new mp.Vector3(-3184.48, 1273.3983, 14.6026),
        new mp.Vector3(-3179.77, 1292.6359, 16.4230),
    ]
];

export default {
    weaponName,
    duration,
    pointSettings,
    MIN_PROGRESS_FOR_DONE,
    failCooldown,
    positions,
    completeCooldown
}
