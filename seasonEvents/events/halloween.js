export const tractorConfig = {
    failCooldown: 3, // Минуты
    completeCooldown: 10, // Часов
    spawnLocationsGnomes: [
        { x: 2546.8378, y: 4806.5800, z: 33.4418, r: 105.1037 },
        { x: 2510.5219, y: 4833.1997, z: 35.5814, r: 149.3074 },
        { x: 2494.3083, y: 4861.3481, z: 37.0254, r: -37.7861 },
        { x: 2554.4125, y: 4796.5131, z: 33.0068, r: 14.3415 },
        { x: 2474.7343, y: 4853.4837, z: 36.9771, r: -63.3572 },
        { x: 2562.8520, y: 4809.7983, z: 33.1274, r: 171.2517 },
        { x: 2530.0195, y: 4799.7041, z: 33.8011, r: 48.1517 },
        { x: 2528.3713, y: 4824.8383, z: 34.3413, r: 38.8756 },
        { x: 2497.6188, y: 4845.3115, z: 35.9724, r: 167.5619 },
        { x: 2478.2900, y: 4835.1299, z: 35.4132, r: -31.1811 },
        { x: 2467.9648, y: 4847.8418, z: 36.7443, r: -70.8661 },
        { x: 2488.0352, y: 4864.167, z: 37.452, r: -130.3937 },
        { x: 2496.5803, y: 4870.1406, z: 37.8733, r: 141.7323 },
        { x: 2544.6067, y: 4827.0991, z: 34.2, r: 141.7323 },
        { x: 2556.2109, y: 4782.8701, z: 32.8184, r: 42.5197 },
        { x: 2542.3779, y: 4790.2681, z: 33.4586, r: -31.1811 },
        { x: 2528.479, y: 4839.7056, z: 35.4637, r: 153.0709 },
        { x: 2509.0022, y: 4855.2925, z: 37.0813, r: 178.5827 },
        { x: 2517.6396, y: 4814.1099, z: 33.9978, r: -22.6772 },
        { x: 2565.4812, y: 4802.2944, z: 32.9868, r: 93.5433 },
        { x: 2536.1406, y: 4815.7715, z: 33.9304, r: -136.063 },
        { x: 2512.5891, y: 4844.0439, z: 36.0535, r: 42.5197 },
        { x: 2483.2747, y: 4848.4746, z: 36.0029, r: -124.7244 },
        { x: 2499.6265, y: 4830.3296, z: 35.5143, r: -102.0473 },
        { x: 2511.9297, y: 4821.6396, z: 34.4022, r: -51.0236 },
        { x: 2550.7649, y: 4816.8789, z: 33.7281, r: 85.0394 },
        { x: 2530.8396, y: 4808.8877, z: 33.6776, r: 51.0236 },
        { x: 2521.0549, y: 4831.3057, z: 34.8572, r: 25.5118 },
        { x: 2539.2263, y: 4800.3691, z: 33.4081, r: 14.1732 },
        { x: 2556.29, y: 4805.3013, z: 33.1216, r: 65.1969 }
    ],

    distToFail: 200,
    centerPosition: new mp.Vector3(2554.4125, 4796.5131, 33.0068),
    heading: 14.3415,
    centerZone: new mp.Vector3(2513, 4832, 35),
    gnomeDuration: 10, // В минутах
    gnomesAmount: 30,
    weapon: 'weapon_statuehalloween',
    // ipls: ['d1n_hei_cs2_02_wall'],
    gnomePed: 'jasonp1',

    tractorModel: 'tractor',



    props: [
        {
            model: 'prop_ghettoblast_02',
            position: new mp.Vector3(),
            rotation: 90
        },
        {
            model: 'prop_ghettoblast_02',
            position: new mp.Vector3(),
            rotation: 90
        },
        {
            model: 'prop_ghettoblast_02',
            position: new mp.Vector3(),
            rotation: 90
        },
        {
            model: 'prop_ghettoblast_02',
            position: new mp.Vector3(),
            rotation: 90
        },
        {
            model: 'prop_ghettoblast_02',
            position: new mp.Vector3(),
            rotation: 90
        },
        {
            model: 'prop_ghettoblast_02',
            position: new mp.Vector3(),
            rotation: 90
        }
    ]
}

export const riftsConfig = {
    insideTimecycle: 'NG_filmnoir_BW02',
    outerTimecycle: null,
    insideWeather: 'HALLOWEEN',
    overrideHour: 1,
    enterSound: 'halloween/rift-enter',
    exitSound: 'halloween/rift-enter',
    insideAmb: 'halloween/rift-inside',
    riftProps: [
        { prop: 'mj_hw23_ray', positionOffset: { x: 0, y: 0, z: 175 } },
    ],
    timcycleRadius: 400,
    outerRadius: 300,
    innerRadius: 10,

    maxRifts: 15,
    spawnRiftsInterval: 20 * 60 * 1000, // Спавним раз в 20 минут.

    ped: {
        model: 'vampiretwo',
        maxPeds: [1, 3],
        minDist: 40,
        weapon: 'weapon_pistol',
        crystalReward: 75

    },
    centers: [
        { x: -2135.87, y: -491.193, z: 2.7329 },
        { x: -3170.88, y: 752.0660, z: 2.7296 },
        { x: -2962.14, y: 1276.114, z: 37.234 },
        { x: -2767.72, y: 2499.786, z: 2.6069 },
        { x: -2373.43, y: 2709.167, z: 3.0461 },
        { x: -2919.37, y: 3098.920, z: 2.4275 },
        { x: -2187.38, y: 5182.368, z: 16.344 },
        { x: -1396.40, y: 5214.469, z: 4.1284 },
        { x: -347.673, y: 6491.003, z: 3.1897 },
        { x: 164.3724, y: 7355.488, z: 11.798 },
        { x: 222.7280, y: 7015.521, z: 2.0244 },
        { x: 556.9207, y: 6664.817, z: 10.255 },
        { x: 1271.968, y: 6583.241, z: 2.4286 },
        { x: 1560.110, y: 6642.383, z: 2.3897 },
        { x: 3677.922, y: 4959.341, z: 17.759 },
        { x: 3470.461, y: 2579.884, z: 15.476 },
        { x: 2960.835, y: 1864.875, z: 12.888 },
        { x: 2409.776, y: 1476.983, z: 40.547 },
        { x: 2726.226, y: 1365.376, z: 24.524 },
        { x: 2556.135, y: 649.5126, z: 101.31 },
        { x: 2551.626, y: -29.0720, z: 97.545 },
        { x: 2420.794, y: -555.215, z: 74.218 },
        { x: 1492.302, y: -2357.69, z: 73.590 },
        { x: 1585.371, y: -2010.71, z: 93.977 },
        { x: 1134.466, y: -2525.08, z: 33.163 },
        { x: 978.8765, y: -2636.22, z: 5.2550 },
        { x: 191.0439, y: -2983.12, z: 5.8118 },
        { x: -1269.87, y: -2152.36, z: 13.924 },
        { x: -844.627, y: -1892.35, z: 20.155 },
        { x: -1332.84, y: -1674.73, z: 2.4835 },
        { x: -1690.22, y: -2594.97, z: 7.4403 },
        { x: -600.947, y: -1321.10, z: 12.323 },
        { x: -1745.90, y: -84.1049, z: 80.367 },
        { x: -1679.43, y: 490.6518, z: 128.87 },
        { x: -2159.80, y: 150.0691, z: 168.67 },
        { x: -1217.34, y: 873.5213, z: 192.58 },
        { x: -627.257, y: 948.0981, z: 241.45 },
        { x: -221.142, y: 1263.648, z: 306.24 },
        { x: -809.432, y: 1706.448, z: 195.45 },
        { x: -655.664, y: 2767.734, z: 39.644 },
        { x: -15.6014, y: 3358.120, z: 40.934 },
        { x: 376.8708, y: 3160.508, z: 50.559 },
        { x: 409.1358, y: 2895.721, z: 41.236 },
        { x: 956.6750, y: 3006.430, z: 40.161 },
        { x: 1266.905, y: 2830.566, z: 47.410 },
        { x: 1656.765, y: 3013.515, z: 56.351 },
        { x: 1884.662, y: 3420.049, z: 41.903 },
        { x: 1982.739, y: 3378.863, z: 43.839 },
        { x: 2075.629, y: 3142.784, z: 43.904 },
        { x: 1385.497, y: 3040.031, z: 42.940 },
        { x: 1062.745, y: 2755.105, z: 37.681 },
        { x: 1020.724, y: 4332.820, z: 41.463 },
        { x: 1672.728, y: 4621.152, z: 45.985 },
        { x: 318.1275, y: 4310.914, z: 47.477 },
        { x: -551.669, y: 4188.748, z: 191.93 },
        { x: -1323.51, y: 4459.676, z: 20.966 },
        { x: 1797.594, y: 4645.921, z: 38.795 },
        { x: 2864.788, y: 4634.752, z: 48.747 },
        { x: 656.0869, y: 6476.525, z: 30.581 },
        { x: 260.8394, y: 6461.851, z: 31.312 },
        { x: -557.095, y: 6240.411, z: 8.9921 },
        { x: -1631.62, y: 4738.443, z: 53.191 },
        { x: -383.560, y: 4342.455, z: 56.082 },
        { x: -149.877, y: 3819.425, z: 32.771 },
        { x: 340.2723, y: 3562.991, z: 33.487 },
        { x: 1430.015, y: 1491.601, z: 113.71 },
        { x: 1647.994, y: 2167.847, z: 79.463 },
        { x: 2530.334, y: 2672.713, z: 39.980 },
        { x: 1262.345, y: 2438.426, z: 75.347 },
        { x: 1292.506, y: 64.73381, z: 72.375 },
        { x: 1374.619, y: -738.361, z: 67.232 },
        { x: 1281.390, y: -1345.03, z: 44.570 },
    ],
}

export const aliensConfig = {
    pedModel: 's_m_m_movalien_01',
    maxPeds: 10,
    weapon: 'weapon_raycarbine',
    spawnPoints: [],
    minDist: 40,
    minDistPeds: 10,
    pedVariations: {},
    dimension: 0,
    crystalReward: 100,
    logicId: 2
}

export const infectedAnimalsZones = [{
    pedModel: 'A_C_Boar_02',
    maxPeds: 8,
    spawnPoints: [
        { x: 18.959101, y: 2419.2534, z: 93.9360 },
        { x: 104.74491, y: 2525.9272, z: 69.2208 },
        { x: 39.872901, y: 2658.0119, z: 79.6102 },
        { x: 162.85541, y: 2850.3847, z: 48.3599 },
        { x: 19.705001, y: 2923.8649, z: 56.3645 },
        { x: 111.65331, y: 3034.9587, z: 45.6875 },
        { x: 181.55181, y: 3159.8505, z: 42.4342 },
        { x: 396.57111, y: 3044.4202, z: 43.2497 },
        { x: 438.23631, y: 3402.6086, z: 41.5264 },
        { x: 948.79161, y: 3260.3911, z: 41.2261 },
        { x: 1201.0655, y: 3478.2062, z: 38.2088 },
        { x: 1304.7310, y: 3331.5014, z: 36.2662 },
        { x: 651.05491, y: 3130.0993, z: 41.9652 },
        { x: 461.54431, y: 2903.3369, z: 42.8719 },
        { x: 680.99621, y: 3030.5692, z: 44.7477 },
        { x: 905.61711, y: 2943.4350, z: 42.8360 },
        { x: 969.47581, y: 3019.8132, z: 40.0612 },
        { x: 1062.8015, y: 2899.1865, z: 38.5962 },
        { x: 1171.9381, y: 3153.4023, z: 39.3665 },
        { x: 1371.3997, y: 3222.5957, z: 38.9061 },
        { x: 1621.1088, y: 3323.2744, z: 39.5339 },
        { x: 1351.9710, y: 3023.7607, z: 43.1046 },
        { x: 1339.3022, y: 2743.6713, z: 51.8563 },
        { x: 1343.2614, y: 2908.8757, z: 40.9103 },
        { x: 1615.2302, y: 2956.0305, z: 55.0321 },
        { x: 1585.8121, y: 3058.0910, z: 44.9238 },
        { x: 1736.9394, y: 3198.3459, z: 43.1280 },
        { x: 1888.3927, y: 3087.9211, z: 45.9666 },
        { x: 2004.5546, y: 3234.3063, z: 44.8835 },
        { x: 2212.2097, y: 3120.4331, z: 48.8566 },
        { x: 2338.8574, y: 3318.6870, z: 46.1479 },
        { x: 2472.9277, y: 3141.7414, z: 46.4272 },
        { x: 2103.7207, y: 3353.7392, z: 45.7500 },
        { x: 1939.5032, y: 3376.2033, z: 43.6637 },
        { x: 1826.1197, y: 3451.8806, z: 40.5254 },
        { x: 2052.5690, y: 3549.1345, z: 41.3407 },
        { x: 2437.5708, y: 3746.1550, z: 43.2062 },
        { x: 2813.0231, y: 3894.1450, z: 48.0264 },
        { x: -744.6969, y: 5939.9394, z: 18.9749 },
        { x: -857.1085, y: 5998.1791, z: 28.0066 },
        { x: -825.8069, y: 5673.3095, z: 20.2945 },
        { x: -690.7800, y: 5641.1992, z: 32.7701 },
        { x: -451.1111, y: 5776.8833, z: 55.1369 },
        { x: -528.4291, y: 5658.5771, z: 47.4412 },
        { x: -485.3531, y: 5461.4125, z: 83.6572 },
        { x: -647.3708, y: 5516.7568, z: 46.9933 },
        { x: -637.3662, y: 5156.2412, z: 110.522 },
        { x: -716.5007, y: 5046.9365, z: 151.297 },
        { x: -693.1230, y: 5192.0078, z: 103.911 },
        { x: -793.5481, y: 5218.6655, z: 106.234 },
        { x: -949.9818, y: 5338.5917, z: 66.8241 },
        { x: -1104.184, y: 5223.1494, z: 92.2694 },
        { x: -838.8751, y: 5570.2514, z: 31.7103 },
        { x: -602.2922, y: 2921.8635, z: 14.9195 },
        { x: -238.3522, y: 3063.5153, z: 34.4149 },
        { x: -134.1596, y: 3083.1118, z: 22.2131 },
        { x: -93.31011, y: 3143.3859, z: 33.2972 },
        { x: 25.463712, y: 3159.7312, z: 29.1244 },
        { x: 103.30061, y: 2987.8857, z: 49.2810 },
        { x: -21.66051, y: 2909.8254, z: 57.5952 },
        { x: -2490.986, y: 2666.4953, z: 3.21891 },
        { x: -2443.440, y: 2567.2831, z: 1.05055 },
        { x: -2250.214, y: 2537.0959, z: 4.43121 },
        { x: -1874.472, y: 2489.9914, z: 3.29251 },
        { x: -1477.867, y: 2311.1459, z: 16.3404 },
        { x: -1405.135, y: 2217.0588, z: 27.5498 },
        { x: -1137.210, y: 2291.9836, z: 85.6086 },
        { x: 546.35831, y: 3125.1555, z: 40.1615 },
        { x: 703.00371, y: 3128.4396, z: 43.9266 },
        { x: 888.52921, y: 3109.6784, z: 40.7186 },
        { x: 1169.7072, y: 3333.3522, z: 42.8948 },
        { x: 1369.8391, y: 3439.6198, z: 36.9439 },
        { x: 1493.0731, y: 3431.9968, z: 40.1892 },
        { x: 1449.8984, y: 4575.9667, z: 59.4004 },
        { x: 1267.9067, y: 4391.9384, z: 45.2174 },
        { x: 1024.3708, y: 4371.5375, z: 41.5074 },
        { x: 1056.2561, y: 4314.0439, z: 37.3531 },
        { x: 1044.6961, y: 4234.5219, z: 35.4307 },
        { x: 987.92171, y: 4301.0424, z: 33.1208 },
        { x: -935.5517, y: 4368.0532, z: 13.7831 },
        { x: 703.34191, y: 2155.9257, z: 58.9117 },
        { x: 574.14531, y: 2209.7912, z: 65.5200 },
        { x: 551.32201, y: 2232.7421, z: 66.0382 },
        { x: 534.34831, y: 2286.4982, z: 62.4173 },
        { x: 493.23521, y: 2266.8918, z: 66.5953 },
        { x: 481.58031, y: 2219.3872, z: 71.9271 },
        { x: 721.88641, y: 3228.3249, z: 39.4024 },
        { x: 691.92301, y: 3142.6054, z: 41.6645 },
        { x: 743.43701, y: 2805.6088, z: 64.8859 },
        { x: 795.06488, y: 2801.0825, z: 65.2966 },
        { x: 2718.9667, y: 5194.8291, z: 45.3876 },
        { x: 2731.2475, y: 5230.6191, z: 51.4937 },
        { x: 2944.8181, y: 5292.2993, z: 107.216 },
        { x: 2932.8320, y: 5334.0327, z: 101.740 },
        { x: 1617.8906, y: 6619.1430, z: 15.4319 },
        { x: 1657.6676, y: 6641.8579, z: 13.5345 },
        { x: 1701.6589, y: 6601.2666, z: 28.2772 },
        { x: 1709.6422, y: 6548.7773, z: 48.5935 },
        { x: -514.1639, y: 5466.5683, z: 75.6143 },
        { x: -540.8386, y: 5503.1318, z: 62.7997 },
        { x: -582.4221, y: 5486.9189, z: 56.6333 },
        { x: -601.0067, y: 5530.6240, z: 50.0050 },
        { x: -674.2047, y: 5173.4580, z: 108.039 },
        { x: -685.3888, y: 5289.4560, z: 66.5645 },
        { x: -894.4047, y: 5261.5874, z: 85.2793 },
        { x: -955.5621, y: 5286.7524, z: 80.6187 },
        { x: -987.4320, y: 5262.2514, z: 75.6740 },
        { x: -1054.586, y: 5280.3500, z: 50.6531 },
        { x: -1417.915, y: 4457.2685, z: 24.5553 },
        { x: -1542.600, y: 4403.2172, z: 8.91801 },
        { x: -1497.200, y: 4392.2543, z: 20.2450 },
        { x: -1596.444, y: 4570.4233, z: 33.2261 },
        { x: -1523.467, y: 4591.2954, z: 30.8664 },
        { x: -1623.721, y: 4629.2143, z: 42.8040 },
        { x: -1648.331, y: 4618.7192, z: 43.5295 },
        { x: 2647.5161, y: 4071.9252, z: 44.4188 },
        { x: 1555.8325, y: 4440.3095, z: 37.9445 },
        { x: 1607.6616, y: 4474.2270, z: 34.0556 },
        { x: 1490.2128, y: 4392.4746, z: 45.1403 },
        { x: 1455.5256, y: 4428.7255, z: 46.0192 },
        { x: 1490.8140, y: 4474.4682, z: 46.3456 },
        { x: 1465.9147, y: 4470.0957, z: 44.3844 },
        { x: 1450.1461, y: 4430.7207, z: 47.5699 },
        { x: 1445.1806, y: 4418.0361, z: 47.1485 },
        { x: 1205.3751, y: 4529.2119, z: 54.0240 },
        { x: 1192.4079, y: 4539.6855, z: 59.9559 },
        { x: 1188.4482, y: 4558.9677, z: 63.4200 },
        { x: 1201.0706, y: 4577.9936, z: 59.4714 },
        { x: 1177.1295, y: 4581.4951, z: 69.0010 },
        { x: 1179.0382, y: 4599.6157, z: 69.3242 },
        { x: 1198.3231, y: 4616.1040, z: 67.5576 },
        { x: 1180.9606, y: 4637.6821, z: 71.9265 },
        { x: 1152.6215, y: 4641.8398, z: 78.0505 },
        { x: 1150.8601, y: 4672.3754, z: 80.1776 },
        { x: 1202.2233, y: 4651.0678, z: 77.3533 },
        { x: 249.54921, y: 4335.1748, z: 44.2023 },
        { x: 250.23251, y: 4325.0336, z: 44.3005 },
        { x: 269.86281, y: 4290.7241, z: 42.1130 },
        { x: 281.69281, y: 4280.9711, z: 41.4286 },
        { x: 299.89411, y: 4275.0024, z: 42.2713 },
        { x: 328.58621, y: 4279.1289, z: 42.2326 },
        { x: 328.41261, y: 4296.9702, z: 45.7490 },
        { x: 316.41301, y: 4314.5102, z: 47.6553 },
        { x: 336.90311, y: 4339.3041, z: 49.7082 },
        { x: 304.84301, y: 4366.7752, z: 51.2922 },
        { x: 283.56571, y: 4408.3549, z: 48.2613 },
        { x: 278.15661, y: 4354.1625, z: 48.4126 },
        { x: -296.3391, y: 4306.2031, z: 37.0651 },
        { x: -304.2041, y: 4197.4707, z: 48.6851 },
        { x: 966.77981, y: 3472.6274, z: 41.1555 },
        { x: 1106.7221, y: 3455.6765, z: 33.7812 },
        { x: 800.48931, y: 4248.2627, z: 59.2435 },
        { x: 1056.7797, y: 4269.7959, z: 37.896 },
        { x: 1197.0282, y: 4373.9971, z: 48.402 },
        { x: 1549.9725, y: 4593.2373, z: 53.082 },
        { x: 577.55101, y: 3066.4319, z: 40.8751 },
        { x: 808.53781, y: 2909.0005, z: 49.6160 },
        { x: 1053.9515, y: 3486.6553, z: 32.698 },
        { x: 749.90511, y: 3063.9429, z: 46.7001 },
        { x: 798.13921, y: 3383.6379, z: 68.9046 },
        { x: 276.03041, y: 2787.5493, z: 43.8851 },
        { x: 802.55261, y: 3171.6909, z: 41.8130 },
        { x: 301.92501, y: 3207.4695, z: 44.6499 },
        { x: 908.29511, y: 3172.2783, z: 39.1307 },
        { x: 1086.6422, y: 3172.0159, z: 39.926 },
        { x: 818.31841, y: 3257.1626, z: 38.0705 },
        { x: -2137.443, y: 2451.7390, z: 2.954 },
        { x: 1162.7660, y: 3328.8452, z: 43.707 },
        { x: -2380.406, y: 2412.2283, z: 10.37 },
        { x: -1970.328, y: 2598.0503, z: 1.616 },
        { x: -1955.884, y: 4531.7437, z: 7.844 },
        { x: -484.1475, y: 2945.2437, z: 22.747 },
        { x: 20.692511, y: 3318.6394, z: 38.68761 },
        { x: -380.4299, y: 2941.7549, z: 23.341 },
        { x: -131.5938, y: 3034.4380, z: 29.907 },
        { x: -1949.654, y: 4479.5317, z: 27.12 },
        { x: 162.15111, y: 3301.7603, z: 40.8602 },
        { x: -33.98491, y: 3631.0347, z: 46.2404 },
        { x: -1864.420, y: 4527.8623, z: 21.59 },
        { x: 261.30291, y: 3226.6387, z: 42.6976 },
        { x: -1842.849, y: 4438.0107, z: 45.78 },
        { x: 602.27811, y: 2955.2126, z: 40.5540 },
        { x: 757.28511, y: 3007.7700, z: 47.2254 },
        { x: -240.1382, y: 3688.2231, z: 61.214 },
        { x: 844.28561, y: 3038.7141, z: 40.3703 },
        { x: -1783.665, y: 4422.9492, z: 35.92 },
        { x: -1692.138, y: 4421.3872, z: 14.79 },
        { x: 1653.9420, y: 3441.4890, z: 36.503 },
        { x: -278.7974, y: 4013.9417, z: 58.363 },
        { x: 2294.0667, y: 3735.2388, z: 37.767 },
        { x: 93.284911, y: 3628.5532, z: 39.72321 },
        { x: 2314.5676, y: 3594.1541, z: 47.650 },
        { x: 2491.4922, y: 4944.2334, z: 44.246 },
        { x: 750.21801, y: 3464.2661, z: 43.8060 },
        { x: -1558.276, y: 4612.5586, z: 21.67 },
        { x: 624.34751, y: 2882.8259, z: 39.2985 },
        { x: 121.33861, y: 2725.0796, z: 50.9266 },
        { x: 1125.7174, y: 3274.5051, z: 38.225 },
        { x: 2303.8359, y: 4756.7749, z: 37.248 },
        { x: 115.29751, y: 3166.3784, z: 41.0452 },
        { x: 63.249011, y: 3283.5017, z: 35.97941 },
        { x: 1176.0928, y: 2784.9609, z: 34.777 },
        { x: 64.321611, y: 3555.9585, z: 48.31561 },
        { x: 2159.4783, y: 4990.2861, z: 41.346 },
        { x: -27.88101, y: 3730.4419, z: 39.2691 },
        { x: -128.1670, y: 3792.5774, z: 32.910 },
        { x: 2127.3826, y: 4992.0552, z: 41.485 },
        { x: -179.1002, y: 3843.4758, z: 31.382 },
        { x: 1955.4063, y: 2852.3018, z: 49.666 },
        { x: -1551.011, y: 4234.6064, z: 67.81 },
        { x: -301.9977, y: 3891.5894, z: 47.102 },
        { x: 1751.5353, y: 3148.6523, z: 43.475 },
        { x: 2242.3081, y: 5168.7314, z: 59.256 },
        { x: -1498.537, y: 4227.1807, z: 59.04 },
        { x: -235.6701, y: 3673.9160, z: 61.792 },
        { x: 174.78681, y: 3221.6414, z: 42.1985 },
        { x: -1385.947, y: 4233.5317, z: 29.63 },
        { x: 2267.9927, y: 3432.7563, z: 66.407 },
        { x: 2.4931111, y: 2475.0767, z: 91.6817 },
        { x: -1392.413, y: 4399.3467, z: 37.37 },
        { x: 2177.4045, y: 4945.9946, z: 41.337 },
        { x: -120.0166, y: 2688.9766, z: 65.572 },
        { x: 2561.2454, y: 3447.8062, z: 61.532 },
        { x: -101.0493, y: 2570.0725, z: 105.67 },
        { x: 2430.7422, y: 4948.9565, z: 45.976 },
        { x: -1329.491, y: 4423.0098, z: 28.76 },
        { x: 2722.3513, y: 3711.6499, z: 67.468 },
        { x: -378.6553, y: 2610.8484, z: 87.188 },
        { x: 2641.0771, y: 3970.7095, z: 46.446 },
        { x: -483.3531, y: 2959.9871, z: 24.867 },
        { x: -560.8040, y: 3089.9678, z: 49.119 },
        { x: 2595.4629, y: 4177.5620, z: 43.562 },
        { x: 2494.2102, y: 4290.3120, z: 39.165 },
        { x: 1638.9915, y: 3156.0886, z: 44.439 },
        { x: 1905.5961, y: 4721.0747, z: 40.775 },
        { x: 1575.3193, y: 3291.6577, z: 42.225 },
        { x: -1178.700, y: 4444.7324, z: 27.961 },
        { x: 1298.8594, y: 3473.4453, z: 37.342 },
        { x: 1767.9247, y: 4632.1382, z: 39.212 },
        { x: -984.8452, y: 4419.2578, z: 18.584 },
        { x: -1099.785, y: 4419.6118, z: 12.90 },
        { x: 1253.8022, y: 3590.8318, z: 34.315 },
        { x: 749.39931, y: 3564.3560, z: 33.1387 },
        { x: 2532.2676, y: 5010.8804, z: 43.641 },
        { x: 1476.9778, y: 4411.0645, z: 46.659 },
        { x: 539.77701, y: 3548.4922, z: 34.0408 },
        { x: -1084.018, y: 4342.2734, z: 16.46 },
        { x: 638.17871, y: 3459.0229, z: 52.8382 },
        { x: 1603.7527, y: 4543.8911, z: 47.246 },
        { x: -982.8118, y: 4416.0649, z: 18.560 },
        { x: 612.88821, y: 3333.1404, z: 77.7985 },
    ],
    minDist: 40,
    minDistPeds: 10,
    pedVariations: {},
    crystalReward: 75
},
{
    pedModel: 'A_C_MtLion_02',
    maxPeds: 8,
    spawnPoints: {},
    minDist: 40,
    minDistPeds: 10,
    pedVariations: {},
    crystalReward: 75
},
{
    pedModel: 'A_C_Deer_02',
    maxPeds: 8,
    spawnPoints: [
        { x: 620.01242, y: 5641.6811, z: 735.528 },
        { x: 810.68612, y: 5648.5521, z: 675.796 },
        { x: 989.41702, y: 5244.1871, z: 329.594 },
        { x: 1309.0782, y: 4772.4471, z: 182.506 },
        { x: 1406.6182, y: 4719.2061, z: 123.040 },
        { x: 1467.4746, y: 4851.5181, z: 115.602 },
        { x: 1441.2167, y: 4997.5541, z: 91.7537 },
        { x: 1360.8811, y: 5057.3361, z: 144.196 },
        { x: 1314.7432, y: 5240.4971, z: 233.905 },
        { x: 1505.0875, y: 5228.7811, z: 169.244 },
        { x: 1747.4625, y: 5192.3951, z: 102.984 },
        { x: 1851.4871, y: 5289.0481, z: 106.010 },
        { x: 1062.8015, y: 2899.1861, z: 38.5962 },
        { x: 1988.3277, y: 5484.3701, z: 138.907 },
        { x: 2181.5820, y: 5807.0801, z: 169.242 },
        { x: 2384.3063, y: 5919.5711, z: 71.7677 },
        { x: 1819.9729, y: 5259.6531, z: 84.8826 },
        { x: 358.74705, y: 934.91781, z: 204.322 },
        { x: 281.10425, y: 912.93321, z: 209.875 },
        { x: 234.88975, y: 985.92631, z: 225.212 },
        { x: 502.13215, y: 821.17431, z: 203.650 },
        { x: 484.04475, y: 704.54471, z: 195.029 },
        { x: 400.04895, y: 646.02021, z: 179.880 },
        { x: 468.24805, y: 532.68741, z: 175.623 },
        { x: 738.83965, y: 398.64871, z: 127.229 },
        { x: 2052.6590, y: 3549.1341, z: 41.3407 },
        { x: 1162.9478, y: 821.50881, z: 136.585 },
        { x: 1003.3970, y: 1142.7181, z: 259.105 },
        { x: 1089.9862, y: 1173.9641, z: 204.120 },
        { x: 1006.0548, y: 1389.0491, z: 212.943 },
        { x: 992.99822, y: 1602.9521, z: 186.909 },
        { x: 942.25713, y: 1577.6861, z: 222.961 },
        { x: 785.87913, y: 1569.2811, z: 250.059 },
        { x: 706.42463, y: 1651.8191, z: 219.444 },
        { x: 497.72263, y: 1696.5261, z: 267.106 },
        { x: 428.94383, y: 1631.5641, z: 292.214 },
        { x: 472.64883, y: 1554.1411, z: 317.226 },
        { x: 353.83813, y: 1462.1781, z: 301.836 },
        { x: 171.14043, y: 1315.0081, z: 260.409 },
        { x: 125.90223, y: 1358.9081, z: 269.362 },
        { x: 31.450912, y: 1356.2231, z: 292.497 },
        { x: -38.64103, y: 1267.3801, z: 298.068 },
        { x: -356.8036, y: 1331.0531, z: 340.488 },
        { x: -538.0286, y: 1266.2771, z: 317.720 },
        { x: -713.5949, y: 1242.2201, z: 288.144 },
        { x: -904.0318, y: 1320.6541, z: 298.612 },
        { x: -1056.426, y: 1247.7281, z: 265.447 },
        { x: -996.8899, y: 1049.5841, z: 171.596 },
        { x: -1072.715, y: 958.28101, z: 164.599 },
        { x: -1199.024, y: 979.76571, z: 221.483 },
        { x: -1222.469, y: 852.45671, z: 188.515 },
        { x: 1581.9949, y: -2610.205, z: 49.095 },
        { x: 1770.7108, y: -2455.232, z: 113.28 },
        { x: 2067.3657, y: -2339.833, z: 54.069 },
        { x: 1893.0331, y: -2015.132, z: 158.22 },
        { x: 2133.8574, y: -1680.126, z: 207.24 },
        { x: 2255.5129, y: -1331.148, z: 119.51 },
        { x: 2458.3992, y: -1380.929, z: 27.815 },
        { x: 2600.3374, y: -1149.897, z: 38.076 },
        { x: 2382.7197, y: -924.5599, z: 148.512 },
        { x: 2062.2871, y: -303.1644, z: 209.256 },
        { x: 2005.8639, y: 532.66221, z: 164.8296 },
        { x: 1898.1935, y: 441.07351, z: 163.9784 },
        { x: 2154.5454, y: -19.40651, z: 226.6820 },
        { x: 1971.8063, y: -106.3879, z: 219.911 },
        { x: 2177.9648, y: 140.75251, z: 227.936 },
        { x: 319.26791, y: 814.28451, z: 191.4862 },
        { x: 258.20861, y: 1040.4965, z: 228.4601 },
        { x: -43.85291, y: 1134.9072, z: 262.7777 },
        { x: -213.9984, y: 1132.6241, z: 279.667 },
        { x: 1591.9091, y: 2205.3765, z: 78.8467 },
        { x: -1762.678, y: 1283.5880, z: 193.45 },
        { x: -1745.565, y: 1434.8567, z: 195.38 },
        { x: -1851.728, y: 1651.1053, z: 234.61 },
        { x: -2098.039, y: 1580.1163, z: 277.92 },
        { x: -2277.329, y: 1511.4095, z: 296.11 },
        { x: -2193.766, y: 1811.3297, z: 244.65 },
        { x: -2240.277, y: 1500.6581, z: 313.10 },
        { x: 1891.8495, y: 447.77001, z: 165.9080 },
        { x: -2556.159, y: 840.32511, z: 277.683 },
        { x: 1777.1219, y: 464.40101, z: 171.6486 },
        { x: 1791.7986, y: 261.06691, z: 173.7817 },
        { x: 1806.3845, y: -86.52181, z: 189.4629 },
        { x: -1929.618, y: 1349.1730, z: 214.01 },
        { x: 2159.5503, y: -21.51381, z: 228.8576 },
        { x: 2407.2656, y: 559.17121, z: 147.8023 },
        { x: -2352.730, y: 834.49681, z: 249.515 },
        { x: 452.48341, y: 1039.2495, z: 234.9071 },
        { x: -2543.603, y: 545.93801, z: 240.408 },
        { x: 379.41551, y: 1108.4783, z: 248.5933 },
        { x: -2476.695, y: 149.78371, z: 136.405 },
        { x: -713.3204, y: 1325.6093, z: 304.870 },
        { x: -2133.282, y: 898.71881, z: 216.225 },
        { x: 1068.5337, y: 4262.4980, z: 37.4769 },
        { x: 35.867311, y: 3751.7722, z: 39.61591 },
        { x: -78.72631, y: 3630.0791, z: 46.6667 },
        { x: 2149.1033, y: -1934.210, z: 111.8 },
        { x: 2040.8801, y: -2114.811, z: 111.3 },
        { x: 62.858811, y: 3555.3279, z: 48.44531 },
        { x: 1827.6882, y: -2342.555, z: 147.2 },
        { x: 1793.6339, y: -2498.849, z: 102.1 },
        { x: 130.91991, y: 3635.8357, z: 39.5672 },
        { x: 1616.4285, y: -2647.597, z: 39.05 },
        { x: 390.62261, y: 3374.3394, z: 46.4397 },
        { x: 310.37401, y: 3516.2866, z: 34.1862 },
        { x: 226.35941, y: 3459.9795, z: 32.9030 },
        { x: 303.10151, y: 3304.2126, z: 41.3113 },
        { x: -2366.012, y: 871.03041, z: 257.69 },
        { x: 2201.4536, y: -2004.206, z: 75.08 },
        { x: 721.89201, y: 3238.2395, z: 39.1488 },
        { x: 998.82291, y: 3447.1006, z: 39.7781 },
        { x: 1593.6190, y: -2606.543, z: 50.31 },
        { x: 2019.1787, y: -2374.216, z: 76.12 },
        { x: 1147.5594, y: 3349.8938, z: 41.804 },
        { x: 1176.3829, y: 3267.0310, z: 39.218 },
        { x: 1293.0923, y: 3351.0369, z: 38.671 },
        { x: 2012.4436, y: -2230.255, z: 91.38 },
        { x: 2305.8391, y: -1961.114, z: 57.64 },
        { x: 1411.7428, y: 3270.3286, z: 39.147 },
        { x: 2051.1946, y: -1274.572, z: 165.7 },
        { x: 1462.9161, y: 3427.3948, z: 39.982 },
        { x: 2019.2628, y: -1558.272, z: 241.9 },
        { x: 1976.1814, y: -1611.769, z: 232.1 },
        { x: 1953.9592, y: -1853.166, z: 135.7 },
        { x: 2028.9661, y: -1968.213, z: 101.5 },
        { x: 2501.4456, y: 3440.7866, z: 50.351 },
        { x: 2119.0166, y: -1972.256, z: 105.9 },
        { x: 2203.7771, y: -2196.612, z: 39.27 },
        { x: 1957.3623, y: -2399.490, z: 74.34 },
    ],
    minDist: 40,
    minDistPeds: 10,
    pedVariations: {},
    crystalReward: 75
},
{
    pedModel: 'A_C_Coyote_02',
    maxPeds: 8,
    spawnPoints: [
        { x: 1185.5618, y: 3211.7028, z: 40.6438 },
        { x: 1308.4089, y: 3213.3129, z: 36.9446 },
        { x: 1446.6484, y: 3392.0246, z: 41.6324 },
        { x: 1610.2708, y: 3298.6921, z: 39.0321 },
        { x: 1672.3691, y: 3158.6323, z: 46.7561 },
        { x: 1735.5582, y: 3198.0190, z: 43.1206 },
        { x: 1828.9938, y: 3393.3188, z: 41.6191 },
        { x: 1927.0404, y: 3368.8920, z: 43.2143 },
        { x: 1998.5104, y: 3479.4062, z: 43.4286 },
        { x: 2231.9145, y: 3471.3793, z: 52.3551 },
        { x: 2234.5505, y: 3470.9541, z: 52.1583 },
        { x: 2180.8479, y: 3092.4299, z: 45.0901 },
        { x: 1910.6912, y: 3075.4001, z: 46.6305 },
        { x: 1667.7844, y: 3022.2548, z: 56.5371 },
        { x: 1331.8817, y: 2758.7954, z: 51.2760 },
        { x: 1477.6254, y: 2411.1640, z: 49.1609 },
        { x: 2476.0134, y: 3317.2507, z: 51.3775 },
        { x: 2577.8759, y: 3320.8806, z: 55.3522 },
        { x: 2596.4145, y: 3503.4526, z: 51.6525 },
        { x: 2426.1252, y: 3460.8356, z: 58.9913 },
        { x: 1983.6755, y: 3246.6862, z: 46.7848 },
        { x: 1850.4731, y: 3354.1437, z: 43.5590 },
        { x: 1379.4390, y: 3048.4926, z: 43.1873 },
        { x: 1350.9699, y: 2889.7954, z: 41.1127 },
        { x: 1401.2280, y: 3270.4465, z: 38.6359 },
        { x: 1954.2773, y: 3483.4096, z: 41.0406 },
        { x: 2192.8706, y: 3560.8374, z: 45.9692 },
        { x: 2395.1088, y: 3765.4851, z: 38.6309 },
        { x: 2384.9663, y: 3874.4375, z: 36.2142 },
        { x: 2387.6059, y: 3875.3820, z: 36.1189 },
        { x: 2657.3779, y: 4172.5410, z: 42.1643 },
        { x: 2683.0629, y: 4196.2236, z: 43.3374 },
        { x: 2764.2155, y: 4229.6113, z: 48.4507 },
        { x: 2700.5822, y: 33988.990, z: 57.1145 },
        { x: 2587.5747, y: 3222.4143, z: 55.8303 },
        { x: 2457.6208, y: 3105.5219, z: 48.3043 },
        { x: 2195.8564, y: 3188.1381, z: 54.3232 },
        { x: 2098.0837, y: 3333.7790, z: 46.4972 },
        { x: 1918.3092, y: 3450.8298, z: 46.1504 },
        { x: 1777.8679, y: 3207.9465, z: 43.8048 },
        { x: 1555.4508, y: 3046.0463, z: 43.3941 },
        { x: 1355.9436, y: 2923.2119, z: 40.4755 },
        { x: 1074.5998, y: 2934.7971, z: 38.7533 },
        { x: 1025.8798, y: 3054.0371, z: 40.0483 },
        { x: 908.60521, y: 3215.1098, z: 38.0827 },
        { x: 1062.6674, y: 3284.2602, z: 37.2652 },
        { x: 1991.6927, y: 3350.1865, z: 44.0098 },
        { x: 2156.5288, y: 3299.1877, z: 47.3866 },
        { x: 2497.5024, y: 3123.9270, z: 48.1173 },
        { x: 2344.7988, y: 3286.0515, z: 47.0114 },
        { x: 1933.1950, y: 3414.1208, z: 43.0162 },
        { x: 832.91891, y: 3290.6257, z: 44.838 },
        { x: 668.71571, y: 3284.4565, z: 54.180 },
        { x: 473.93531, y: 3147.6675, z: 43.883 },
        { x: 424.82281, y: 3037.7898, z: 41.524 },
        { x: 318.05171, y: 3052.2966, z: 42.017 },
        { x: 304.21781, y: 3081.1777, z: 41.927 },
        { x: 275.01041, y: 3080.4631, z: 42.488 },
        { x: -536.6743, y: 2991.9280, z: 28.955 },
        { x: -653.4081, y: 2988.1296, z: 23.319 },
        { x: 256.96321, y: 3049.7104, z: 42.979 },
        { x: -699.1955, y: 2951.3054, z: 22.188 },
        { x: 326.24551, y: 3480.1333, z: 35.517 },
        { x: -760.6802, y: 2898.7720, z: 23.363 },
        { x: -1151.623, y: 2835.1831, z: 15.143 },
        { x: -1252.559, y: 2728.5039, z: 9.1930 },
        { x: -1364.883, y: 2682.6707, z: 4.0241 },
        { x: -1488.206, y: 2754.9653, z: 18.379 },
        { x: -1510.239, y: 2823.7920, z: 31.222 },
        { x: -1616.997, y: 2953.3262, z: 32.073 },
        { x: -1836.605, y: 3353.6873, z: 33.253 },
        { x: -2006.011, y: 3420.5713, z: 31.172 },
        { x: -2128.575, y: 3384.6665, z: 32.386 },
        { x: -2289.297, y: 3524.5205, z: 55.380 },
        { x: -2443.988, y: 3450.4719, z: 41.832 },
        { x: -2710.986, y: 3372.5845, z: 31.702 },
        { x: -2822.645, y: 3398.3484, z: 30.597 },
        { x: -2880.796, y: 3351.0894, z: 32.778 },
        { x: -319.8401, y: 3939.8459, z: 60.230 },
        { x: -2850.629, y: 3255.5464, z: 32.053 },
        { x: -2743.607, y: 3157.6663, z: 30.746 },
        { x: -311.9307, y: 3803.0278, z: 67.661 },
        { x: -2467.747, y: 2892.0779, z: 33.258 },
        { x: -316.7710, y: 3785.5588, z: 68.430 },
        { x: -2370.588, y: 2909.5208, z: 32.005 },
        { x: -2176.694, y: 2839.1860, z: 28.976 },
        { x: -2106.195, y: 2776.8108, z: 32.322 },
        { x: -1967.559, y: 2779.1541, z: 30.561 },
        { x: -1811.526, y: 2766.1370, z: 30.298 },
        { x: -1830.299, y: 2522.8782, z: 3.2090 },
        { x: -1938.834, y: 2500.8340, z: 2.9984 },
        { x: -2027.581, y: 2480.3315, z: 2.9264 },
        { x: -2112.804, y: 2477.5972, z: 1.1687 },
        { x: -2200.572, y: 2494.0386, z: 6.1757 },
        { x: -1.485911, y: 3422.2390, z: 48.543 },
        { x: -2325.157, y: 2439.2615, z: 4.6882 },
        { x: -38.51181, y: 3444.6963, z: 53.720 },
        { x: -2455.550, y: 2434.9380, z: 5.4016 },
        { x: -2550.717, y: 2488.0242, z: 2.8978 },
        { x: 937.13531, y: 3387.0413, z: 66.917 },
        { x: -1129.261, y: 3188.4495, z: 110.22 },
        { x: 935.62301, y: 3386.7612, z: 67.464 },
        { x: -1121.163, y: 3205.7285, z: 110.32 },
        { x: 606.65161, y: 3385.6030, z: 87.813 },
        { x: 606.65161, y: 3385.6030, z: 87.813 },
        { x: -1087.039, y: 3220.6592, z: 89.803 },
        { x: 297.63621, y: 3338.6047, z: 43.586 },
        { x: 309.53501, y: 3507.1265, z: 35.115 },
        { x: -1080.041, y: 3197.2031, z: 79.922 },
        { x: 3026.6909, y: 3317.5393, z: 74.814 },
        { x: 915.69231, y: 2968.6089, z: 40.310 },
        { x: -2016.751, y: 3806.8796, z: 201.77 },
        { x: 505.16901, y: 3539.7029, z: 31.992 },
        { x: -2167.602, y: 3894.3289, z: 122.27 },
        { x: 622.91511, y: 3541.2935, z: 32.731 },
        { x: -1059.115, y: 3127.9868, z: 57.684 },
        { x: 741.19691, y: 3574.0552, z: 33.372 },
        { x: 791.51681, y: 3591.0095, z: 34.431 },
        { x: 826.03831, y: 3587.8845, z: 34.119 },
        { x: -2167.872, y: 4054.7297, z: 109.95 },
        { x: 2591.3542, y: 3639.8765, z: 96.649 },
        { x: 1096.4749, y: 3480.2556, z: 32.845 },
        { x: 1198.8145, y: 3573.7666, z: 34.356 },
        { x: -2264.205, y: 4361.3589, z: 42.513 },
        { x: -2021.916, y: 4407.7412, z: 67.596 },
        { x: 36.070511, y: 2613.7864, z: 83.802 },
        { x: -877.8873, y: 3081.5588, z: 78.710 },
        { x: 3105.1382, y: 4347.5405, z: 96.918 },
        { x: 3089.1335, y: 4262.2744, z: 92.917 },
        { x: -1969.156, y: 4384.5669, z: 53.508 },
        { x: -837.9716, y: 3119.1060, z: 86.359 },
        { x: 271.22781, y: 2431.8950, z: 52.067 },
        { x: 3206.7808, y: 3943.9463, z: 94.510 },
        { x: 800.00071, y: 2149.0730, z: 52.323 },
        { x: -1968.585, y: 4561.3638, z: 7.7597 },
        { x: 2551.0359, y: 4064.3613, z: 45.048 },
        { x: 3296.9141, y: 3374.6606, z: 115.12 },
        { x: 1268.3254, y: 2416.4382, z: 75.682 },
        { x: 3124.2771, y: 3218.7244, z: 90.196 },
        { x: -727.7234, y: 3153.8118, z: 102.09 },
        { x: 2866.7490, y: 3059.2185, z: 74.647 },
        { x: 2168.0952, y: 4701.9595, z: 37.276 },
        { x: -523.2910, y: 3069.3042, z: 43.308 },
        { x: -1844.349, y: 4552.3896, z: 6.0211 },
        { x: 2326.2881, y: 4620.0200, z: 35.817 },
        { x: -481.4756, y: 3244.8645, z: 98.487 },
        { x: 2945.2036, y: 4540.4834, z: 49.352 },
        { x: 2582.8958, y: 3849.3855, z: 65.597 },
        { x: 2752.2190, y: 4048.0168, z: 60.286 },
        { x: -670.0973, y: 4021.1284, z: 131.52 },
        { x: 2779.6013, y: 4123.1807, z: 45.424 },
        { x: 2771.6616, y: 4221.9688, z: 46.756 },
        { x: 2635.2822, y: 4133.1533, z: 41.403 },
        { x: -1720.094, y: 4419.6875, z: 21.697 },
        { x: 2579.3455, y: 3863.8359, z: 63.565 },
        { x: 2372.1641, y: 3630.3516, z: 45.208 },
        { x: -741.8572, y: 4078.6404, z: 165.79 },
        { x: 2012.4268, y: 3503.0098, z: 40.631 },
        { x: 1913.3468, y: 3401.8650, z: 42.110 },
        { x: -796.5361, y: 4086.5381, z: 177.25 },
        { x: -1293.178, y: 2218.7346, z: 51.779 },
        { x: 2489.2490, y: 2191.4709, z: 34.889 },
        { x: 2399.7263, y: 2476.9636, z: 55.633 },
        { x: -1654.723, y: 4241.9414, z: 83.705 },
        { x: 2278.2258, y: 2285.2632, z: 64.551 },
        { x: -839.3210, y: 4122.2056, z: 195.82 },
        { x: 2189.6042, y: 2154.2104, z: 114.15 },
        { x: 2075.4187, y: 2214.2471, z: 92.790 },
        { x: 2140.2092, y: 2498.2966, z: 72.782 },
        { x: 2258.4001, y: 2613.8625, z: 51.284 },
        { x: -800.1989, y: 4168.8569, z: 196.03 },
        { x: 2406.1482, y: 2784.3835, z: 42.651 },
        { x: 2746.8262, y: 2518.9016, z: 79.256 },
        { x: 2904.0430, y: 2583.8438, z: 142.02 },
        { x: 2749.3040, y: 2489.9436, z: 68.980 },
        { x: 3430.5115, y: 4449.8940, z: 184.22 },
        { x: 3428.4802, y: 4386.6440, z: 213.24 },
        { x: 2630.1118, y: 2239.3291, z: 29.723 },
        { x: 2440.6394, y: 4487.7993, z: 33.459 },
        { x: 3438.5574, y: 4187.9526, z: 238.44 },
        { x: 2538.1794, y: 2365.2737, z: 50.634 },
        { x: -724.3038, y: 4239.6216, z: 161.28 },
        { x: 3367.3733, y: 4012.4756, z: 187.61 },
        { x: 2622.9060, y: 3724.0298, z: 94.886 },
        { x: -718.2764, y: 4284.0762, z: 134.50 },
        { x: 2836.9927, y: 2980.8159, z: 65.300 },
        { x: 1041.7777, y: 3158.3118, z: 39.109 },
        { x: 2688.8652, y: 3382.5229, z: 57.364 },
        { x: 2597.9636, y: 3562.6936, z: 52.559 },
        { x: -715.3034, y: 4351.8354, z: 75.868 },
        { x: 494.16531, y: 3556.5752, z: 33.868 },
        { x: -1433.904, y: 4234.2227, z: 48.414 },
        { x: 656.16091, y: 2455.3828, z: 61.4911 },
        { x: 654.29831, y: 2230.6956, z: 57.0511 },
        { x: 663.98771, y: 2472.7590, z: 63.4211 },
        { x: 609.66691, y: 2232.7346, z: 60.6811 },
        { x: 399.90531, y: 2517.6440, z: 43.9211 },
        { x: 521.74041, y: 2924.2471, z: 40.6111 },
        { x: 368.84371, y: 2776.8533, z: 55.5611 },
        { x: 807.39131, y: 2108.1343, z: 63.6011 },
        { x: 873.33521, y: 2771.7432, z: 56.4711 },
        { x: 457.79011, y: 2313.9021, z: 68.0811 },
        { x: 815.81551, y: 2512.1897, z: 71.5511 },
        { x: 276.94251, y: 2167.1865, z: 86.5311 },
        { x: 1657.6141, y: 2365.7495, z: 46.8111 },
        { x: 116.52871, y: 2219.8140, z: 116.911 },
        { x: -7.558511, y: 2331.3918, z: 137.251 },
        { x: 913.61761, y: 1893.5726, z: 120.211 },
        { x: 1451.7791, y: 2443.3596, z: 53.0111 },
        { x: -139.6421, y: 2074.1311, z: 181.111 },
        { x: -35.01581, y: 2474.3997, z: 97.0211 },
        { x: 383.95881, y: 2205.4231, z: 89.6211 },
        { x: 3.4100111, y: 2583.6497, z: 86.2995 },
        { x: -104.9301, y: 2659.0220, z: 70.0111 },
        { x: -1129.511, y: 2334.0581, z: 98.1110 },
        { x: -556.8011, y: 2646.1909, z: 45.6111 },
        { x: -330.6141, y: 2600.3030, z: 79.2111 },
        { x: 1558.8511, y: 2720.2048, z: 45.8111 },
        { x: -375.0621, y: 2469.0488, z: 99.4111 },
        { x: 397.27491, y: 1977.4644, z: 120.011 },
        { x: -929.0231, y: 2250.6660, z: 142.111 },
        { x: 175.19251, y: 2297.5408, z: 93.0411 },
        { x: 1422.9286, y: 2579.4294, z: 37.0111 },
        { x: -410.3392, y: 2346.1882, z: 140.111 },
        { x: -932.4100, y: 1877.3972, z: 128.111 },
        { x: 1137.8115, y: 1996.6949, z: 60.0111 },
        { x: -480.0864, y: 2218.5364, z: 157.111 },
        { x: -512.1473, y: 2482.2405, z: 55.6111 },
        { x: 1229.6274, y: 2463.2034, z: 74.5111 },
        { x: 3.1080111, y: 1815.3536, z: 209.624 },
        { x: -606.3634, y: 2249.1426, z: 102.111 },
        { x: -666.9926, y: 2031.7776, z: 133.111 },
        { x: -709.7921, y: 2092.0874, z: 115.111 },
        { x: -700.7966, y: 2208.9524, z: 94.8111 },
        { x: 1043.8462, y: 2640.8269, z: 41.1111 },
        { x: 279.26521, y: 1968.9978, z: 149.711 },
        { x: -663.5043, y: 2394.2690, z: 52.4111 },
        { x: 57.038611, y: 1797.1505, z: 200.701 },
        { x: 1288.4940, y: 2060.8765, z: 87.2111 },
        { x: -588.3001, y: 2324.2190, z: 69.7111 },
        { x: -822.9136, y: 2135.4783, z: 101.111 },
        { x: -466.5092, y: 2218.8176, z: 159.111 },
        { x: -814.7744, y: 2383.3025, z: 96.2111 },
        { x: -285.8010, y: 2444.3872, z: 75.7111 },
        { x: -376.3612, y: 2511.6138, z: 93.7111 },
        { x: 1378.3145, y: 1837.9843, z: 99.0111 },
        { x: -222.7571, y: 2354.2324, z: 92.8111 },
        { x: -706.1343, y: 2550.8772, z: 58.0111 },
        { x: -227.1344, y: 2255.8662, z: 103.111 },
        { x: 1548.6147, y: 2720.3369, z: 45.7111 },
        { x: -489.1849, y: 2108.9106, z: 193.111 },
        { x: -34.25711, y: 2938.5125, z: 53.9111 },
        { x: -840.9823, y: 1965.4264, z: 153.111 },
        { x: -112.8113, y: 2931.1643, z: 43.9111 },
        { x: 1255.6570, y: 2153.9634, z: 75.7111 },
        { x: 1498.1121, y: 3530.0117, z: 36.2111 },
        { x: 1146.1986, y: 1819.5302, z: 72.8111 },
        { x: -1939.719, y: 2225.6406, z: 89.1115 },
        { x: 1168.5022, y: 2506.7698, z: 60.4111 },
        { x: -1569.443, y: 2016.9602, z: 75.1111 },
        { x: 935.79221, y: 2211.7285, z: 48.2711 },
        { x: 961.40191, y: 2555.3105, z: 57.5911 },
        { x: 1338.5001, y: 2427.4763, z: 72.1111 },
        { x: -800.4505, y: 1760.6566, z: 185.111 },
        { x: 88.304311, y: 1984.5060, z: 168.111 },
        { x: 737.67061, y: 2552.1619, z: 76.7511 },
        { x: 1520.5415, y: 2248.8350, z: 73.0111 },
        { x: -602.7112, y: 2222.8396, z: 116.111 },
        { x: 737.66371, y: 2552.8538, z: 76.8211 },
        { x: -416.0991, y: 2530.8767, z: 96.3111 },
    ],
    minDist: 40,
    minDistPeds: 10,
    pedVariations: {},
    crystalReward: 75
}]

export const photoLocationsConfig = {
    failCooldown: 3, // Минуты
    completeCooldown: 6, // Часов
    crystalReward: 100,
    hint: {
        title: 'fun.seasonEvents.halloween.photoLocation.hints.1.title',
        desc: 'fun.seasonEvents.halloween.photoLocation.hints.1.text',
        timeout: 25000,
    },
    locations: [
        {
            findZoneBlip: {
                radius: 900
            },
            locationPosition: new mp.Vector3(-1366.1802, -1428.3033, 4.2916),
            locationProp: 'halloween2024_photo_1',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 800
            },
            locationPosition: new mp.Vector3(2089.9385, 3588.7253, 40.3502),
            locationProp: 'halloween2024_photo_2',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 800
            },
            locationPosition: new mp.Vector3(2327.9604, 2536.3120, 46.6520),
            locationProp: 'halloween2024_photo_3',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 600
            },
            locationPosition: new mp.Vector3(-113.5517, 4617.7056, 124.3297),
            locationProp: 'halloween2024_photo_4',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 600
            },
            locationPosition: new mp.Vector3(777.2176, 2518.5759, 74.7744),
            locationProp: 'halloween2024_photo_5',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 600
            },
            locationPosition: new mp.Vector3(-419.6308, 1596.9231, 354.9194),
            locationProp: 'halloween2024_photo_6',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 600
            },
            locationPosition: new mp.Vector3(2399.3801, 4957.1606, 44.2424),
            locationProp: 'halloween2024_photo_7',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 600
            },
            locationPosition: new mp.Vector3(-559.1736, 4922.0308, 165.6622),
            locationProp: 'halloween2024_photo_8',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 800
            },
            locationPosition: new mp.Vector3(-685.6219, 5848.3516, 16.7942),
            locationProp: 'halloween2024_photo_9',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 800
            },
            locationPosition: new mp.Vector3(1709.9077, 58.6813, 171.4586),
            locationProp: 'halloween2024_photo_10',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 600
            },
            locationPosition: new mp.Vector3(1893.1912, 4892.6108, 47.0901),
            locationProp: 'halloween2024_photo_11',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 600
            },
            locationPosition: new mp.Vector3(-382.5626, 1234.8528, 326.0725),
            locationProp: 'halloween2024_photo_12',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 600
            },
            locationPosition: new mp.Vector3(2009.1956, 3843.4548, 35.9861),
            locationProp: 'halloween2024_photo_13',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 1000
            },
            locationPosition: new mp.Vector3(3347.0637, 5156.4263, 19.9282),
            locationProp: 'halloween2024_photo_14',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 800
            },
            locationPosition: new mp.Vector3(1506.3824, 3524.6375, 36.2388),
            locationProp: 'halloween2024_photo_15',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 600
            },
            locationPosition: new mp.Vector3(-434.1494, 5437.0815, 76.8638),
            locationProp: 'halloween2024_photo_16',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 900
            },
            locationPosition: new mp.Vector3(-1368.8967, 2671.2000, 3.0110),
            locationProp: 'halloween2024_photo_17',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 800
            },
            locationPosition: new mp.Vector3(-1554.6593, 2113.7935, 66.7539),
            locationProp: 'halloween2024_photo_18',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 800
            },
            locationPosition: new mp.Vector3(3776.7957, 4467.1519, 6.1956),
            locationProp: 'halloween2024_photo_19',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 700
            },
            locationPosition: new mp.Vector3(1201.9517, 1782.3297, 76.9312),
            locationProp: 'halloween2024_photo_20',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 700
            },
            locationPosition: new mp.Vector3(1840.4440, 3279.8901, 43.4000),
            locationProp: 'halloween2024_photo_21',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 700
            },
            locationPosition: new mp.Vector3(2313.8242, 3177.2703, 47.4440),
            locationProp: 'halloween2024_photo_22',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 1000
            },
            locationPosition: new mp.Vector3(-50.9407, 6273.9165, 31.3861),
            locationProp: 'halloween2024_photo_23',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 800
            },
            locationPosition: new mp.Vector3(240.8571, 2820.6331, 43.5685),
            locationProp: 'halloween2024_photo_24',
            itemId: 984
        },
        {
            findZoneBlip: {
                radius: 1000
            },
            locationPosition: new mp.Vector3(1478.4659, 6437.4463, 22.0007),
            locationProp: 'halloween2024_photo_25',
            itemId: 984
        },
    ]
}

export const alchemyConfig = {
    failCooldown: 3, // Минуты
    completeCooldown: 12, // Часов
    cookOptions: [95, 96, 99, 158, 242, 243, 244, 283, 288, 320, 341, 349, 362, 364, 388, 452, 453, 456, 458, 526],
    hint: {
        title: 'fun.seasonEvents.halloween.alchemy.hints.1.title',
        desc: 'fun.seasonEvents.halloween.alchemy.hints.1.text',
        timeout: 25000,
    },

    teleportTo: {
        position: new mp.Vector3(2329.26, 2571.42, 46.71),
        rotation: 144
    },

    fadeTime: 1000,

    spawnAfterEnd: {
        position: new mp.Vector3(-1134.9099, 4941.2441, 222.2609),
        rotation: 104
    },

    ambientMusic: {
        path: 'university.seamstress',
        options: {
            volume: 25,
            fade: 2000,
            waitTime: 1000,
            looped: false,
            force: true
        }
    },
}

export const protectionMonumentConfig = {
    failCooldown: 3, // Минуты
    completeCooldown: 8, // Часов
    startPos: [{ x: -289.5165, y: 2833.5298, z: 55.4982, rot: -34.0157 }, { x: -286.4967, y: 2832.7781, z: 55.4982, rot: -14.1732 }],
    tpAfterEnd: { x: -290.9802, y: 2838.7122, z: 55.2960, rot: -113.3858 },
    portal: { x: -286.6549, y: 2838.1582, z: 55.0095, radius: 1.5 },
    playersWeapon: {
        weapon: 'weapon_pistol_mk2',
        ammo: 150,
    },

    startSpawnPedsRate: 1850, // Как часто будет спавн педов сначала
    spawnRateDecrease: 4, // На сколько будет ускорен спавн после каждого спавна

    duration: 6 * 60 * 1000,
    countZombieSkipLoose: 20, // Количество пропущенных зомби, после которых игрок проигрывает
    timecycleModifier: 'Forest',
    weather: 'CLOUDS',
    hourTime: 21,

    itemsSpawnChance: 12, // N% шанс спавна предмета каждую секунду
    itemsPos: [ // Позиции для спавна предметов
        { x: -296.6901, y: 2856.1978, z: 54.0491 },
        { x: -301.7407, y: 2843.446, z: 55.7678 },
        { x: -287.0505, y: 2850.1846, z: 53.9648 },
        { x: -279.8901, y: 2847.6924, z: 53.7795 },
        { x: -270.5143, y: 2838.1846, z: 53.7458 },
        { x: -274.3648, y: 2831.3406, z: 54.6388 },
        { x: -285.2308, y: 2823.521, z: 57.3348 },
        { x: -302.1626, y: 2816.5847, z: 59.3231 },
        { x: -302.4396, y: 2826.6858, z: 58.8008 },
        { x: -321.2835, y: 2833.2395, z: 57.7391 },
        { x: -318.211, y: 2841.5474, z: 56.8293 },
        { x: -309.1253, y: 2852.2681, z: 55.0938 },
        { x: -288.2505, y: 2828.0044, z: 56.3575 },
        { x: -293.5253, y: 2814.9363, z: 58.9692 }
    ],
    items: [
        {
            model: 'w_pi_pistolmk2',
            weapon: 'weapon_pistol_mk2',
            ammo: 115,
        },
        {
            model: 'w_ar_carbinerifle_reh',
            weapon: 'weapon_tacticalrifle',
            ammo: 150,
        },
        {
            model: 'w_sb_gusenberg',
            weapon: 'weapon_gusenberg',
            ammo: 150,
        },
        {
            model: 'h4_prop_h4_pumpshotgunh4',
            weapon: 'weapon_pumpshotgun',
            ammo: 120,
        },
        {
            model: 'w_mg_combatmg_luxe',
            weapon: 'weapon_mg',
            ammo: 160,
        },
        {
            model: 'prop_armour_pickup',
            armor: 100,
        },
        {
            model: 'prop_armour_pickup',
            armor: 100,
        },
        {
            model: 'prop_ld_health_pack',
            medkit: 100,
        },
        {
            model: 'prop_ld_health_pack',
            medkit: 100,
        }
    ],

    peds: [
        {
            model: 'vampireone',
            weapon: 'weapon_stone_hatchet',
            armour: 100,
            health: 100,
            combatAbility: 7
        },
        {
            model: 'jasonp1',
            speed: 1.2,
        },
        {
            model: 'vampiretwo',
            speed: 1.2,
        },
        {
            model: 'jasonsavini',
            weapon: 'weapon_snspistol',
            armour: 100,
            health: 100,
            combatAbility: 2
        },
        {
            model: 'vampireone',
            speed: 1.2,
        },
    ],
    pedsPos: {
        angry: [ // С оружием
            { x: -267.0461, y: 2835.8506, z: 53.4762 },
            { x: -297.6527, y: 2814.5671, z: 59.1040 }
        ],
        runners: [ // Без оружия
            { x: -276.6989, y: 2817.7847, z: 55.9363 },
            { x: -325.9253, y: 2831.0769, z: 57.6549 },
            { x: -317.4857, y: 2847.0198, z: 55.8352 }
        ]
    }
}

export const graffitiConfig = {
    failCooldown: 3, // Минуты
    completeCooldown: 8, // Часов
    MIN_PROGRESS_FOR_DONE: 3,

    pedModels: ['spirit', 'sadako'],
    ATTACK_DISTANCE: 10,
    DELETE_DISTANCE: 50,
    SPAWN_RADIUS: 10,

    graffitiPositions: [
        [
            new mp.Vector3(636.542053, -1006.25574, 37.7440948),
            new mp.Vector3(684.537048, -1043.38367, 24.88627),
            new mp.Vector3(649.572144, -1021.002, 22.63546),
            new mp.Vector3(657.4744, -1010.02582, 22.649332)
        ],
        [
            new mp.Vector3(-1641.94409, -1024.961, 14.0359764),
            new mp.Vector3(-1646.585, -1058.237, 15.89256),
            new mp.Vector3(-1635.445, -1059.624, 14.48073),
            new mp.Vector3(-1631.714, -1063.43, 13.57073)
        ],
        [
            new mp.Vector3(523.388, -1940.501, 25.53448),
            new mp.Vector3(526.1264, -1917.47168, 28.3471031),
            new mp.Vector3(517.5582, -1979.26282, 25.2264214),
            new mp.Vector3(509.5212, -1950.501, 26.72249)
        ],
        [
            new mp.Vector3(-137.307037, 1941.90881, 196.991684),
            new mp.Vector3(-96.85609, 1886.292, 197.7525),
            new mp.Vector3(-31.48516, 1945.721, 190.7971),
            new mp.Vector3(-39.0192032, 1903.14026, 196.541321)
        ],
        [
            new mp.Vector3(-147.1401, 3646.885, 41.47631),
            new mp.Vector3(-208.511, 3624.89, 52.19183),
            new mp.Vector3(-208.926743, 3665.88745, 55.3486977),
            new mp.Vector3(-204.8913, 3596.619, 55.40734)
        ],
        [
            new mp.Vector3(-413.283661, 3965.52051, 71.37533),
            new mp.Vector3(-418.9799, 4006.966, 81.93324),
            new mp.Vector3(-429.953, 4038.782, 84.78872),
            new mp.Vector3(-408.453583, 3939.916, 70.65322)
        ],
        [
            new mp.Vector3(-745.658447, 5549.124, 34.6660233),
            new mp.Vector3(-748.8891, 5594.73, 35.15364),
            new mp.Vector3(-756.367, 5590.897, 39.51357),
            new mp.Vector3(-773.9245, 5605.30469, 33.35958)
        ],
        [
            new mp.Vector3(408.532379, 6539.107, 28.3721447),
            new mp.Vector3(435.5938, 6474.818, 29.53972),
            new mp.Vector3(433.4061, 6499.984, 28.42584),
            new mp.Vector3(447.7202, 6462.463, 29.41282)
        ],
        [
            new mp.Vector3(1539.024, 6317.14, 24.84837),
            new mp.Vector3(1504.649, 6365.368, 16.3098183),
            new mp.Vector3(1473.724, 6381.913, 25.35245),
            new mp.Vector3(1423.937, 6333.965, 24.45669)
        ],
        [
            new mp.Vector3(2256.18262, 5163.21533, 59.9229126),
            new mp.Vector3(2231.54419, 5184.425, 60.8176537),
            new mp.Vector3(2305.7207, 5189.956, 56.9900436),
            new mp.Vector3(2206.518, 5198.36426, 61.77174)
        ],
        [
            new mp.Vector3(2472.123, 1596.259, 32.7769279),
            new mp.Vector3(2452.508, 1592.529, 35.38407),
            new mp.Vector3(2455.517, 1462.628, 36.73748),
            new mp.Vector3(2431.743, 1524.11, 35.09024)
        ],
        [
            new mp.Vector3(1921.903, 601.6059, 181.5357),
            new mp.Vector3(1902.281, 611.1897, 183.1538),
            new mp.Vector3(1925.747, 608.3258, 178.8527),
            new mp.Vector3(1968.483, 583.1958, 162.3016)
        ],
        [
            new mp.Vector3(1650.48828, 24.5616856, 175.576462),
            new mp.Vector3(1638.54871, 21.5073318, 174.486969),
            new mp.Vector3(1665.70361, -30.90739, 177.522186),
            new mp.Vector3(1666.41016, -29.049675, 174.858917)
        ],
        [
            new mp.Vector3(615.334, -462.192566, 27.01667),
            new mp.Vector3(579.9079, -429.3315, 26.08955),
            new mp.Vector3(658.7028, -406.0322, 28.67293),
            new mp.Vector3(643.665833, -455.17514, 27.343977)
        ]
    ],

    terrainPositions: [
        new mp.Vector3(658.5062, -1081.152, 25.949398),
        new mp.Vector3(-1622.78687, -1075.84961, 16.5502663),
        new mp.Vector3(542.2196, -1948.60461, 24.88985),
        new mp.Vector3(-78.6843, 1921.792, 196.6819),
        new mp.Vector3(-183.714111, 3636.67334, 54.2744751),
        new mp.Vector3(-417.021576, 3989.655, 80.98865),
        new mp.Vector3(-757.5544, 5579.46631, 38.430687),
        new mp.Vector3(434.206818, 6495.80762, 29.6309662),
        new mp.Vector3(1486.97815, 6350.804, 25.1583748),
        new mp.Vector3(2251.85034, 5180.08447, 62.3779831),
        new mp.Vector3(2457.05518, 1549.6792, 34.81787),
        new mp.Vector3(1935.55994, 601.169067, 176.2385),
        new mp.Vector3(1659.288, -0.152911663, 174.789246),
        new mp.Vector3(623.431641, -443.012756, 26.6002789)
    ]
}

export const qrCodesConfig = {
    SETTINGS_RECEIVED_PRESENTS_KEY: 'qrCodes_presents',
    SETTINGS_PRESENTS_FOUND: 'qrCodes_presents_found',
    NEED_CRYSTALS: 50000,

    QR_PARTS_ACTIVE: [
        '2024-10-31 00:00:00',
        '2024-10-31 00:00:00',
        '2024-10-31 00:00:00',
        '2024-10-31 00:00:00',
        '2024-11-1 00:00:00',
        '2024-11-3 00:00:00',
        '2024-11-5 00:00:00',
        '2024-11-7 00:00:00',
    ],

    presents: [
        [
            { type: 'clothes', value: [{ itemId: 43, gender: 0, component: 7, drawable: 2150, texture: 0, isProp: 0 }] },
            { type: 'clothes', value: [{ itemId: 43, gender: 1, component: 7, drawable: 2128, texture: 0, isProp: 0 }] },
            { type: 'case', value: { case: 'halloween2024' } },
            { type: 'case', value: { case: 'halloween2024' } },
        ],
        [
            { type: 'clothes', value: [{ itemId: 43, gender: 0, component: 7, drawable: 2150, texture: 1, isProp: 0 }] },
            { type: 'clothes', value: [{ itemId: 43, gender: 1, component: 7, drawable: 2128, texture: 1, isProp: 0 }] },
            { type: 'case', value: { case: 'halloween2024' } },
        ],
        [
            { type: 'clothes', value: [{ itemId: 43, gender: 0, component: 7, drawable: 2150, texture: 5, isProp: 0 }] },
            { type: 'clothes', value: [{ itemId: 43, gender: 1, component: 7, drawable: 2128, texture: 5, isProp: 0 }] },
        ],
        [
            { type: 'case', value: { case: 'halloween2024' } },
            { type: 'case', value: { case: 'halloween2024' } },
            { type: 'case', value: { case: 'halloween2024' } },
            { type: 'case', value: { case: 'halloween2024' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2024' } },
            { type: 'case', value: { case: 'halloween2024' } },
            { type: 'case', value: { case: 'halloween2024' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2024' } },
            { type: 'case', value: { case: 'halloween2024' } },
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2024' } },
            { type: 'case', value: { case: 'halloween2023' } },
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2024' } },
            { type: 'case', value: { case: 'halloween2023' } },
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2024' } },
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2024' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2023' } },
        ],
        [
            { type: 'case', value: { case: 'halloween2023' } },
        ],
    ]
}

export const vampireManorConfig = {
    failCooldown: 3, // Минуты
    completeCooldown: 8, // Часов
    // Спавны игрока
    SPAWN_POSITIONS: [
        {
            pos: { x: -1487.5253, y: 806.8879, z: 181.9392 },
            heading: 28.3465
        },
        {
            pos: { x: -1558.299, y: 874.7473, z: 181.7032 },
            heading: -107.7165
        },
        {
            pos: { x: -1501.6483, y: 906, z: 181.6022 },
            heading: -164.4095
        }
    ],

    // Точки выхода
    EXIT_POSITIONS: [
        {
            pos: { x: -1479.9033, y: 885.1912, z: 182.8154 },
        },
        {
            pos: { x: -1593.1516, y: 782.0835, z: 189.1846 },
        },
    ],

    // Педы которые танцуют. Позиция и хединг
    DANCING_PEDS: [
        {
            pos: { x: -1521.1121, y: 838.4703, z: 181.5853 },
            seeFov: 0.1,
            seeDist: 0.1,
            heading: -25.6772
        },
        {
            pos: { x: -1516.5099, y: 839.0110, z: 181.6527 },
            seeFov: 0.1,
            seeDist: 0.1,
            heading: 59.8661
        },
        {
            pos: { x: -1519.2263, y: 834.4352, z: 181.5853 },
            seeFov: 0.1,
            seeDist: 0.1,
            heading: -28.8346
        },
        {
            pos: { x: -1515.3495, y: 834.5802, z: 181.5853 },
            seeFov: 0.1,
            seeDist: 0.1,
            heading: 42.5276
        },
        {
            pos: { x: -1518.4747, y: 838.0747, z: 182.2087 },
            seeFov: 0.1,
            seeDist: 0.1,
            heading: 22.6929
        }
    ],

    // Модели педов. Для каждого педа берется рандомная
    PED_MODELS: [
        { model: 'cs_dreyfuss', gender: 'male' },
        { model: 'a_f_y_beach_02', gender: 'female' },
        { model: 'cs_dreyfuss', gender: 'male' },
        { model: 'u_m_m_prolsec_01', gender: 'male' },
        { model: 'mp_f_deadhooker', gender: 'female' },
        { model: 'a_m_o_salton_01', gender: 'male' },
        { model: 's_m_m_strpreach_01', gender: 'male' },
        { model: 's_m_y_clubbar_01', gender: 'male' },
        { model: 'ig_kerrymcintosh', gender: 'female' },
        { model: 'ig_sol', gender: 'male' },
        { model: 'u_f_m_miranda', gender: 'female' },
        { model: 'u_f_y_hotposh_01', gender: 'female' },
        { model: 'u_f_y_danceburl_01', gender: 'female' },
        { model: 'u_f_y_mistress', gender: 'female' },
        { model: 'u_f_y_spyactress', gender: 'female' }
    ],

    // Анимации для танцующих педов. Для каждого педа берется рандомная
    DANCE_ANIMATIONS: [
        {
            dict: 'anim@amb@nightclub@mini@dance@dance_solo@male@var_a@',
            name: 'high_center'
        },
        {
            dict: 'anim@amb@nightclub@lazlow@hi_railing@',
            name: 'ambclub_09_mi_hi_bellydancer_laz'
        },
        {
            dict: 'anim@amb@nightclub@lazlow@hi_railing@',
            name: 'ambclub_12_mi_hi_bootyshake_laz'
        },
        {
            dict: 'anim@amb@casino@mini@dance@dance_solo@female@var_a@',
            name: 'high_right_up'
        },
        {
            dict: 'anim@amb@nightclub_island@dancers@beachdance@',
            name: 'mi_idle_a_f02'
        },
    ],

    // Настройка interactions для взаимодействия с пистолетом
    PISTOL_INTERACTION_PARAMS: {
        pos: { x: -1511.81005859375, y: 835.8414916992188, z: 180.60035705566406 },
        heading: -80,
        hintOffset: { x: 0.6, y: 0.02, z: 1.1 },
    },

    GAME_STATE: {
        SearchingItems: 1,
        SearhingForPistol: 2,
        LeaveLocation: 3,
    },


    // Патрульные маршруты педов
    PEDS_PATROL_ROUTES: [
        // Маршрут педа 1
        {
            points: [
                {
                    pos: { x: -1490.0044, y: 852.5406, z: 181.5853 }, // точка куда пойдет пед
                    lookAt: { x: -1488.9495, y: 853.0417, z: 181.5853 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1479.4022, y: 857.8154, z: 182.5458 }, // точка куда пойдет пед
                    lookAt: { x: -1478.1626, y: 859.0022, z: 182.6805 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1482.7649, y: 866.822, z: 182.5963 }, // точка куда пойдет пед
                    lookAt: { x: -1483.4637, y: 867.7978, z: 182.5795 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 2
        {
            points: [
                {
                    pos: { x: -1483.9121, y: 850.1011, z: 181.9392 }, // точка куда пойдет пед
                    lookAt: { x: -1483.1736, y: 848.4528, z: 181.9224 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1482.989, y: 839.0374, z: 181.6527 }, // точка куда пойдет пед
                    lookAt: { x: -1483.5692, y: 837.811, z: 181.6359 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1492.2065, y: 835.7802, z: 181.6527 }, // точка куда пойдет пед
                    lookAt: { x: -1492.299, y: 834.1714, z: 181.6359 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 3
        {
            points: [
                {
                    pos: { x: -1475.2615, y: 835.5956, z: 182.1077 }, // точка куда пойдет пед
                    lookAt: { x: -1476.3561, y: 837.0725, z: 182.0403 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1482.7252, y: 872.8351, z: 182.748 }, // точка куда пойдет пед
                    lookAt: { x: -1483.3319, y: 875.011, z: 182.8154 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 4
        {
            points: [
                {
                    pos: { x: -1490.3868, y: 877.8989, z: 182.7142 }, // точка куда пойдет пед
                    lookAt: { x: -1488.9626, y: 878.5319, z: 182.866 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1498.1802, y: 879.8373, z: 182.5458 }, // точка куда пойдет пед
                    lookAt: { x: -1498.5099, y: 879.877, z: 182.5289 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1507.556, y: 874.8132, z: 182.0066 }, // точка куда пойдет пед
                    lookAt: { x: -1510.3912, y: 874.7736, z: 182.074 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 5
        {
            points: [
                {
                    pos: { x: -1503.4154, y: 861.0725, z: 181.5853 }, // точка куда пойдет пед
                    lookAt: { x: -1502.611, y: 859.345, z: 181.5853 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1513.701, y: 858.0264, z: 181.8044 }, // точка куда пойдет пед
                    lookAt: { x: -1515.5736, y: 857.8813, z: 181.855 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1513.6088, y: 853.2527, z: 181.5853 }, // точка куда пойдет пед
                    lookAt: { x: -1515.8638, y: 856.0352, z: 181.5853 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 6
        {
            points: [
                {
                    pos: { x: -1506.7913, y: 864.9626, z: 181.7539 }, // точка куда пойдет пед
                    lookAt: { x: -1506.1846, y: 866.1627, z: 181.7708 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1520.0176, y: 863.644, z: 181.6696 }, // точка куда пойдет пед
                    lookAt: { x: -1521.3627, y: 864.1714, z: 181.6696 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1535.9209, y: 855.4418, z: 181.5348 }, // точка куда пойдет пед
                    lookAt: { x: -1538.0967, y: 854.0044, z: 181.5011 } // точка куда будет смотреть пед когда дойдет до pos
                },
            ]
        },
        // Маршрут педа 7
        {
            points: [
                {
                    pos: { x: -1520.5582, y: 854.3472, z: 181.5853 }, // точка куда пойдет пед
                    lookAt: { x: -1518.8835, y: 853.3187, z: 181.5853 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1533.2836, y: 862.1407, z: 181.6864 }, // точка куда пойдет пед
                    lookAt: { x: -1534.0483, y: 864.3033, z: 181.6527 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1528.9714, y: 868.1934, z: 181.7539 }, // точка куда пойдет пед
                    lookAt: { x: -1527.5472, y: 869.0374, z: 181.7876 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 8
        {
            points: [
                {
                    pos: { x: -1499.6044, y: 886.9055, z: 182.2424 }, // точка куда пойдет пед
                    lookAt: { x: -1501.0945, y: 886.4835, z: 182.1919 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1529.6835, y: 879.8505, z: 181.7032 }, // точка куда пойдет пед
                    lookAt: { x: -1529.5121, y: 878.3209, z: 181.6864 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 9
        {
            points: [
                {
                    pos: { x: -1499.6967, y: 876.7648, z: 182.1582 }, // точка куда пойдет пед
                    lookAt: { x: -1498.2726, y: 875.6572, z: 182.1077 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1488.3561, y: 864.2374, z: 182.1414 }, // точка куда пойдет пед
                    lookAt: { x: -1488.2638, y: 862.589, z: 182.074 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 10
        {
            points: [
                {
                    pos: { x: -1539.8901, y: 838.8395, z: 181.855 }, // точка куда пойдет пед
                    lookAt: { x: -1538.6901, y: 840.4088, z: 181.7876 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1527.9561, y: 819.5077, z: 181.6864 }, // точка куда пойдет пед
                    lookAt: { x: -1526.1362, y: 816.4879, z: 181.7876 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 11
        {
            points: [
                {
                    pos: { x: -1517.9341, y: 824.545, z: 181.6864 }, // точка куда пойдет пед
                    lookAt: { x: -1517.5121, y: 826.3384, z: 181.6864 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1504.4308, y: 818.0703, z: 181.7369 }, // точка куда пойдет пед
                    lookAt: { x: -1502.0967, y: 816.4088, z: 181.7876 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 12
        {
            points: [
                {
                    pos: { x: -1504.9978, y: 832.8132, z: 181.6527 }, // точка куда пойдет пед
                    lookAt: { x: -1505.2616, y: 833.8945, z: 181.6527 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1485.5868, y: 825.1385, z: 181.6359 }, // точка куда пойдет пед
                    lookAt: { x: -1484.0571, y: 828.0527, z: 181.6022 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 13
        {
            points: [
                {
                    pos: { x: -1465.9912, y: 824.4, z: 183.2704 }, // точка куда пойдет пед
                    lookAt: { x: -1466.1626, y: 828.6198, z: 183.2535 } // точка куда будет смотреть пед когда дойдет до pos
                },
                {
                    pos: { x: -1477.6088, y: 868.1143, z: 183.0681 }, // точка куда пойдет пед
                    lookAt: { x: -1478.3209, y: 870.8307, z: 183.085 } // точка куда будет смотреть пед когда дойдет до pos
                }
            ]
        },
        // Маршрут педа 14
        {
            points: [
                {
                    pos: { x: -1490.8616, y: 874.2462, z: 182.3773 },
                    lookAt: { x: -1492.4572, y: 873.0066, z: 182.2594 }
                },
                {
                    pos: { x: -1505.1560, y: 864.8307, z: 181.7369 },
                    lookAt: { x: -1508.4528, y: 867.8770, z: 181.6022 }
                }
            ]
        },

        // Маршрут педа 15
        {
            points: [
                {
                    pos: { x: -1503.2836, y: 874.9583, z: 181.9392 },
                    lookAt: { x: -1502.9802, y: 875.5912, z: 181.9897 }
                },
                {
                    pos: { x: -1517.9209, y: 857.8286, z: 181.8381 },
                    lookAt: { x: -1518.2241, y: 855.9033, z: 181.7032 }
                }
            ]
        },

        // Маршрут педа 16
        {
            points: [
                {
                    pos: { x: -1520.4264, y: 875.4066, z: 181.7539 },
                    lookAt: { x: -1526.5450, y: 876.0923, z: 181.8718 }
                },
                {
                    pos: { x: -1528.2858, y: 850.5758, z: 181.5853 },
                    lookAt: { x: -1528.6813, y: 851.1561, z: 181.5853 }
                }
            ]
        },

        // Маршрут педа 17
        {
            points: [
                {
                    pos: { x: -1543.2924, y: 864.7648, z: 181.4000 },
                    lookAt: { x: -1542.8572, y: 863.4857, z: 181.4169 }
                },
                {
                    pos: { x: -1533.6923, y: 843.4813, z: 181.5853 },
                    lookAt: { x: -1533.5341, y: 843.1252, z: 181.5853 }
                }
            ]
        },

        // Маршрут педа 18
        {
            points: [
                {
                    pos: { x: -1558.3121, y: 846.0791, z: 183.0850 },
                    lookAt: { x: -1557.4550, y: 845.3406, z: 183.0344 }
                },
                {
                    pos: { x: -1536.0264, y: 824.4264, z: 181.4674 },
                    lookAt: { x: -1534.9714, y: 824.3737, z: 181.4674 }
                }
            ]
        },

        // Маршрут педа 19
        {
            points: [
                {
                    pos: { x: -1534.6681, y: 812.5978, z: 181.9224 },
                    lookAt: { x: -1533.5472, y: 812.5319, z: 181.8213 }
                },
                {
                    pos: { x: -1502.3341, y: 810.3297, z: 181.8887 },
                    lookAt: { x: -1500.5934, y: 811.8329, z: 181.8550 }
                }
            ]
        },

        // Маршрут педа 20
        {
            points: [
                {
                    pos: { x: -1471.4769, y: 849.9692, z: 183.1355 },
                    lookAt: { x: -1472.6637, y: 851.3406, z: 182.9839 }
                },
                {
                    pos: { x: -1484.7032, y: 851.3406, z: 181.9055 },
                    lookAt: { x: -1483.7539, y: 847.6219, z: 181.7539 }
                }
            ]
        },

        // Маршрут педа 21
        {
            points: [
                {
                    pos: { x: -1482.3824, y: 882.3297, z: 182.9333 },
                    lookAt: { x: -1481.2483, y: 883.0154, z: 182.7986 }
                },
                {
                    pos: { x: -1485.8901, y: 867.0066, z: 182.4110 },
                    lookAt: { x: -1485.0461, y: 866.4264, z: 182.3268 }
                }
            ]
        },

        // Маршрут педа 22
        {
            points: [
                {
                    pos: { x: -1509.5868, y: 854.8615, z: 181.5853 },
                    lookAt: { x: -1510.1934, y: 855.9561, z: 181.5853 }
                },
                {
                    pos: { x: -1515.9033, y: 868.0088, z: 181.6527 },
                    lookAt: { x: -1517.0769, y: 867.7319, z: 181.6864 }
                }
            ]
        },
    ],

    // Точки которые игрок должен лутать
    SEARCH_POINTS: [
        {
            pos: { x: -1507.5103759765625, y: 838.317626953125, z: 181.60000610351562 },
            heading: -155,
            hintOffset: { x: 0.5, y: 0, z: 1 },
        },
        {
            pos: { x: -1509.393798828125, y: 845.9979858398438, z: 181.5998992919922 },
            heading: 25,
            hintOffset: { x: 0.3, y: 0, z: 0.7 },
        },
        {
            pos: { x: -1512.6142578125, y: 849.0635375976562, z: 181.59994506835938 },
            heading: 205,
            hintOffset: { x: 0.4, y: 0, z: 0.8 },
        },
        {
            pos: { x: -1502.722900390625, y: 840.6906127929688, z: 181.59979248046875 },
            heading: 205,
            hintOffset: { x: 0.3, y: 0, z: 0.9 },
        },
        {
            pos: { x: -1508.832763671875, y: 850.0465698242188, z: 181.5999755859375 },
            heading: 205,
            hintOffset: { x: 0.3, y: 0, z: 0.85 },
        },
        {
            pos: { x: -1496.9033203125, y: 847.4425048828125, z: 181.5998077392578 },
            heading: -65,
            hintOffset: { x: 0.5, y: 0, z: 1 },
        },
        {
            pos: { x: -1492.911376953125, y: 845.3357543945312, z: 181.5999755859375 },
            heading: 205,
            hintOffset: { x: 0.5, y: 0, z: 0.7 },
        },
        {
            pos: { x: -1493.67724609375, y: 854.0881958007812, z: 181.59982299804688 },
            heading: 115,
            hintOffset: { x: 0.5, y: 0, z: 1 },
        },
        {
            pos: { x: -1497.7469482421875, y: 842.9000244140625, z: 181.59974670410156 },
            heading: -160,
            hintOffset: { x: 0.5, y: 0, z: 0.7 },
        },
        {
            pos: { x: -1494.56005859375, y: 857.2911376953125, z: 181.59986877441406 },
            heading: -90,
            hintOffset: { x: 0.7, y: 0, z: 1.1 },
        },
        {
            pos: { x: -1493.2930908203125, y: 863.130615234375, z: 181.59986877441406 },
            heading: 30,
            hintOffset: { x: 0.3, y: 0, z: 0.7 },
        },
        {
            pos: { x: -1517.6591796875, y: 847.3461303710938, z: 181.59979248046875 },
            heading: -190,
            hintOffset: { x: 0.6, y: 0.2, z: 0 },
        },
        {
            pos: { x: -1526.0234375, y: 838.856201171875, z: 186.19993591308594 },
            heading: -145,
            hintOffset: { x: 0.3, y: 0, z: 0.9 },
        },
        {
            pos: { x: -1511.6602783203125, y: 837.95703125, z: 186.19973754882812 },
            heading: 30,
            hintOffset: { x: 0.7, y: 0, z: 1 },
        },
        {
            pos: { x: -1518.2664794921875, y: 837.2075805664062, z: 186.1997528076172 },
            heading: 30,
            hintOffset: { x: 0.2, y: 0, z: 1 },
        },
        {
            pos: { x: -1516.5, y: 844.6674194335938, z: 186.19984436035156 },
            heading: -220,
            hintOffset: { x: 0.4, y: 0, z: 0.8 },
        },
        {
            pos: { x: -1522.828857421875, y: 840.1630859375, z: 180.59991455078125 },
            heading: 120,
            hintOffset: { x: 0.5, y: 0, z: 1 },
        }
    ],

    // Педы которые стоят на месте и крутятся
    PED_WATCHER: [
        {
            pos: { x: -1496.7428, y: 851.8945, z: 181.5853 },
            seeFov: 45,
            seeDist: 5.5,
            watchConfig: [
                {
                    heading: -110,
                    duration: 5500,
                },
                {
                    heading: -150,
                    duration: 6000,
                },
                {
                    heading: -175,
                    duration: 5000,
                },
                {
                    heading: 150,
                    duration: 5000,
                },
            ]
        },
        {
            pos: { x: -1502.2814, y: 849.7582, z: 181.5853 },
            seeFov: 45,
            seeDist: 4,
            watchConfig: [
                {
                    heading: 0,
                    duration: 5000,
                },
                {
                    heading: 79,
                    duration: 4000,
                },
                {
                    heading: 138,
                    duration: 5000,
                },
            ]
        },
        {
            pos: { x: -1515.0989, y: 846.9099, z: 181.5853 },
            seeFov: 45,
            seeDist: 4,
            watchConfig: [
                {
                    heading: 73,
                    duration: 2000,
                },
                {
                    heading: 45,
                    duration: 6000,
                },
                {
                    heading: 17,
                    duration: 4000,
                },
                {
                    heading: -25,
                    duration: 4000,
                },
            ]
        },
        {
            pos: { x: -1515.1912, y: 841.8066, z: 186.1853 },
            seeFov: 45,
            seeDist: 4,
            watchConfig: [
                {
                    heading: 25,
                    duration: 5000,
                },
                {
                    heading: -5,
                    duration: 5000,
                },
                {
                    heading: -82,
                    duration: 3000,
                },
                {
                    heading: -150,
                    duration: 6000,
                },
            ]
        },
        {
            pos: { x: -1520.1890, y: 847.0022, z: 180.8777 },
            seeFov: 45,
            seeDist: 4,
            watchConfig: [
                {
                    heading: -19,
                    duration: 2000,
                },
            ]
        },
        {
            pos: { x: -1498.9714, y: 859.2923, z: 181.5853 },
            seeFov: 45,
            seeDist: 5.6,
            watchConfig: [
                {
                    heading: 34,
                    duration: 5000,
                },
                {
                    heading: -42,
                    duration: 4000,
                },
                {
                    heading: -98,
                    duration: 4000,
                },
            ]
        },
        {
            pos: { x: -1490.3868, y: 844.7341, z: 181.5853 },
            seeFov: 45,
            seeDist: 4,
            watchConfig: [
                {
                    heading: -144,
                    duration: 5000,
                },
                {
                    heading: 136,
                    duration: 4000,
                },
                {
                    heading: 116,
                    duration: 2000,
                },
                {
                    heading: 70,
                    duration: 4500,
                },
            ]
        },
        {
            pos: { x: -1501.411, y: 839.7231, z: 181.5853 },
            seeFov: 45,
            seeDist: 4,
            watchConfig: [
                {
                    heading: -116,
                    duration: 5000,
                },
                {
                    heading: 152,
                    duration: 4000,
                },
                {
                    heading: 104,
                    duration: 4500,
                },
                {
                    heading: 65,
                    duration: 2000,
                },
            ]
        },
        {
            pos: { x: -1507.9253, y: 851.9341, z: 181.5853 },
            seeFov: 45,
            seeDist: 4,
            watchConfig: [
                {
                    heading: 133,
                    duration: 4000,
                },
                {
                    heading: 85,
                    duration: 5000,
                },
                {
                    heading: 53,
                    duration: 6000,
                },
                {
                    heading: 0,
                    duration: 4500,
                },
            ]
        },
        {
            pos: { x: -1505.4462, y: 837.6528, z: 181.5853 },
            seeFov: 45,
            seeDist: 4,
            watchConfig: [
                {
                    heading: 161,
                    duration: 4000,
                },
                {
                    heading: 121,
                    duration: 4000,
                },
                {
                    heading: 99,
                    duration: 5500,
                },
                {
                    heading: 73,
                    duration: 2000,
                },
            ]
        },
        {
            pos: { x: -1528.0747, y: 843.666, z: 181.5853 },
            seeFov: 45,
            seeDist: 4,
            watchConfig: [
                {
                    heading: -17,
                    duration: 4000,
                },
                {
                    heading: -48,
                    duration: 5000,
                },
                {
                    heading: -85,
                    duration: 4500,
                },
                {
                    heading: -121,
                    duration: 2500,
                },
            ]
        },
        {
            pos: { x: -1511.8418, y: 836.0439, z: 186.1853 },
            seeFov: 45,
            seeDist: 4,
            watchConfig: [
                {
                    heading: -14,
                    duration: 2000,
                },
                {
                    heading: -51,
                    duration: 5000,
                },
                {
                    heading: -102,
                    duration: 4500,
                },
                {
                    heading: -130,
                    duration: 2500,
                },
            ]
        },
        {
            pos: { x: -1524.0000, y: 841.3450, z: 186.1853 },
            seeFov: 45,
            seeDist: 4,
            watchConfig: [
                {
                    heading: 147,
                    duration: 2000,
                },
                {
                    heading: 116,
                    duration: 5000,
                },
                {
                    heading: 65,
                    duration: 5500,
                },
                {
                    heading: -8,
                    duration: 3500,
                },
            ]
        },
    ],

    FX: [
        {
            pos: { x: -1518.449, y: 837.658, z: 183.9087 },
            rot: { x: 0, y: 180, z: 0 },
            timeout: -1, // -1 значит будет работать бесконечно,
            fxLib: 'core',
            fxName: 'scrape_blood_car',
            scale: 3
        },
        {
            pos: { x: -1520.862, y: 836.548, z: 183.9087 },
            rot: { x: 0, y: 180, z: 0 },
            timeout: -1, // -1 значит будет работать бесконечно,
            fxLib: 'core',
            fxName: 'scrape_blood_car',
            scale: 3
        },
        {
            pos: { x: -1516.025, y: 838.908, z: 183.9087 },
            rot: { x: 0, y: 180, z: 0 },
            timeout: -1, // -1 значит будет работать бесконечно,
            fxLib: 'core',
            fxName: 'scrape_blood_car',
            scale: 3
        },
    ],

    TARGET_SCORE: 3,

    // Угол обзора педов - искателей
    SEEKER_VIEW_ANGLE: 60,

    // Дальность обзора педов - искателей
    SEEKER_VIEW_DIST: 8.5,

    SEEKER_CRAWLING_ANGLE_REDUCER: 1.25,
    SEEKER_CRAWLING_DIST_REDUCER: 1.25,

    MISSION_DURATION: 15 * 60 * 1000,

    AMMO_AMOUNT: 5,

    TRASNSFORM_PED_MODEL: 'vampireone',

    MARKER_TEXTURE: 'mp_alerttriangle',
    MARKER_DICT: 'commonmenu',

    ZONE_CENTER: { x: -1518.449, y: 837.658, z: 183.9087 },

    // RETURN_POSITION:
}

export const miningConfig = {
    failCooldown: 3, // Минуты
    completeCooldown: 10, // Часов
    SPAWN_POSITION: { x: -598.0088, y: 2094.4614, z: 131.2212 }, // Спавн игрока у шахты
    SPAWN_IN_POSITION: { x: -595.1208, y: 2085.2571, z: 131.3729 }, // Спавн игрока в шахте
    BOMB_POSITION: { x: -595.9811, y: 2088.4832, z: 131.4121 }, // Позиция триггера бомбы

    pedModels: ['spirit', 'sadako'], // Модели и количество педов, которые спавнятся
    DELETE_DISTANCE: 50, // дистанция удаления педа
    SPAWN_RADIUS: 3, // радиус спавна педов

    EFFECTS_DURATION: 2000, // Время переходов затухания в мс
    DURATION: 15 * 60 * 1000, // Время на выполнение части в шахте в мс
    TIMECYCLE: 'NG_filmic06', // Тайм-цикл

    COLLECT_OBJECTS_TO_WIN: 3, // Количество объектов, которое нужно найти для победы

    // Объекты для поиска в шахте
    objects: [
        [
            {
                propModel: 'ch_prop_ch_trophy_monkey_01a',
                position: { x: -447.679443, y: 2014.166, z: 122.580154 },
                rotation: { x: 0, y: 0, z: 0 }
            },
            {
                propModel: 'h4_prop_h4_cash_bon_01a',
                position: { x: -486.5202, y: 1894.70923, z: 119.049759 },
                rotation: { x: 0, y: 0, z: 170 }
            },
            {
                propModel: 'h4_prop_h4_gold_stack_01a',
                position: { x: -530.6129, y: 1899.60168, z: 122.833855 },
                rotation: { x: 0, y: 0, z: 170 }
            },
            {
                propModel: 'xs_prop_trophy_cup_01a',
                position: { x: -544.9247, y: 1901.03052, z: 122.140327 },
                rotation: { x: -53.99, y: -4.21, z: 28.36 }
            }
        ],
        [
            {
                propModel: 'prop_cash_case_01',
                position: { x: -537.628967, y: 1910.22913, z: 122.432487 },
                rotation: { x: 0, y: 0, z: -75 }
            },
            {
                propModel: 'ch_prop_ch_trophy_monkey_01a',
                position: { x: -447.679443, y: 2014.166, z: 122.580154 },
                rotation: { x: 0, y: 0, z: 0 }
            },
            {
                propModel: 'h4_prop_h4_gold_stack_01a',
                position: { x: -543.4825, y: 1967.71545, z: 126.80806 },
                rotation: { x: 0, y: 0, z: 170 }
            },
            {
                propModel: 'tr_prop_tr_trophy_camhedz_01a',
                position: { x: -451.514282, y: 2055.16724, z: 121.175056 },
                rotation: { x: -4.76, y: -2.01, z: -72.08 }
            },
        ],
        [
            {
                propModel: 'ch_prop_ch_trophy_monkey_01a',
                position: { x: -447.679443, y: 2014.166, z: 122.580154 },
                rotation: { x: 0, y: 0, z: 0 }
            },
            {
                propModel: 'h4_prop_h4_cash_bon_01a',
                position: { x: -468.782776, y: 2071.9646, z: 119.592857 },
                rotation: { x: 0, y: 0, z: 125 }
            },
            {
                propModel: 'h4_prop_h4_gold_stack_01a',
                position: { x: -465.0683, y: 1993.51611, z: 122.550842 },
                rotation: { x: 0, y: 0, z: 170 }
            },
            {
                propModel: 'xs_prop_trophy_cup_01a',
                position: { x: -543.0157, y: 1982.6189, z: 126.0245 },
                rotation: { x: 2.36, y: 3.53, z: 54.1 }
            },
        ]
    ]
}

export const escapeConfig = {
    failCooldown: 3, // Минуты
    completeCooldown: 10, // Часов
    completeTime: 15, // Минуты
    hints: [
        {
            title: 'fun.seasonEvents.halloween.escape.hints.1.title',
            desc: 'fun.seasonEvents.halloween.escape.hints.1.text',
            timeout: 25000,
        },
        {
            title: 'fun.seasonEvents.halloween.escape.hints.2.title',
            desc: 'fun.seasonEvents.halloween.escape.hints.2.text',
            timeout: 20000,
        },
        {
            title: 'fun.seasonEvents.halloween.escape.hints.3.title',
            desc: 'fun.seasonEvents.halloween.escape.hints.3.text',
            timeout: 20000,
        }
    ],
    // Оружие, которое выдаем на оружейном складе
    weapon: 'weapon_mg',

    // Сколько раз нужно пройти мини-игру взлома дверей
    puzzleCompleteCount: 2,

    // Какие иплки подгружать при старте задания
    ipls: ['bridge_halloween'],

    // Что-то со старого хэллоуина, связанное с дверьми
    doors: [
        {
            model: 'qt_hl_lab_door',
            position: {
                x: -1878.099,
                y: 3751.151,
                z: -100.916
            }
        },
        {
            model: 'qt_hl_lab_door',
            position: {
                x: -1878.099,
                y: 3748.418,
                z: -100.9386
            }
        },
        {
            model: 'qt_hl_lab_door',
            position: {
                x: -1885.851,
                y: 3753.232,
                z: -100.9248
            }
        },
        {
            model: 'qt_hl_lab_door',
            position: {
                x: -1901.928,
                y: 3753.232,
                z: -100.8923
            }
        },
        {
            model: 'qt_hl_lab_door',
            position: {
                x: -1893.823,
                y: 3746.825,
                z: -100.8546
            }
        }
    ],

    // Партикл у портала невидимого моста
    portalParticleFx: {
        position: new mp.Vector3(-1217.0378, 4572.7197, 193.4865),
        fxLib: 'scr_xs_pits',
        fxName: 'scr_xs_sf_pit_long',
        duration: 1, // В минутах
        scale: 2
    },

    // maxPeds - прямо пропорционально зависит от pedSpawnPosition. Если педов больше чем 5, нужно добавить ровно столько же координат для спавна
    maxPeds: 5,
    pedModel: 'jasonsavini',
    pedWeapon: 'weapon_pistol_mk2',
    pedSpawnPosition: [
        {
            position: new mp.Vector3(-1912.2858, 3752.0176, -99.6549),
            heading: 87
        },
        {
            position: new mp.Vector3(-1912.4044, 3747.5869, -99.6549),
            heading: 48
        },
        {
            position: new mp.Vector3(-1922.2549, 3749.6045, -99.6549),
            heading: -90.2205
        },
        {
            position: new mp.Vector3(-1904.4396, 3747.4285, -99.6549),
            heading: 76.5512
        },
        {
            position: new mp.Vector3(-1896.0923, 3751.8198, -99.6549),
            heading: 87.3780
        }
    ],


    // Куда телепортируем игрока при начале квеста
    startPosition: {
        position: new mp.Vector3(-1135.9517, 4660.2988, 243.8792),
        heading: 136,
        fade: 2000 // Затемнение экрана перед телепортом
    },

    // Координаты колшейпа портала за/на невидимом мосту
    portalColshape: {
        position: new mp.Vector3(-1210.9846, 4578.2373, 195.9978),
        radius: 2
    },

    // Координаты блипа портала
    portalBlip: {
        id: 171,
        position: new mp.Vector3(-1210.9846, 4578.2373, 194.9978),
        optionals: {
            color: 4
        }
    },

    portalMarker: {
        id: 42,
        position: new mp.Vector3(-1210.6945, 4578.5010, 197.0703),
        scale: 4.5,
        optionals: {
            color: [57, 0, 222, 95]
        }
    },

    // Координаты колшейпа оружейного склада, который выдает оружие и спавнит нпс
    warehouseColshape: {
        position: new mp.Vector3(-1892.7825, 3740.1758, -99.6549),
        radius: 1.5
    },

    // Координаты колшейпа мини-игры взлома двери
    puzzleColshape: {
        position: new mp.Vector3(-1877.1296, 3749.8154, -99.6549),
        radius: 1.5
    },

    // Координаты маркера склада
    warehouseMarker: {
        id: 44,
        position: new mp.Vector3(-1892.7825, 3740.1758, -99.6549),
        scale: 1.1,
        optionals: {
            color: [163, 219, 217, 155],
        }
    },

    // Координаты блипа склада
    warehouseBlip: {
        id: 313,
        position: new mp.Vector3(-1892.7825, 3740.1758, -99.6549)
    },

    // Координаты финального колшейпа, на прошлом хэллоуине вроде было что-то типа лифта, куда игрок сбегает после того как убьет всех нпс
    exitColshape: {
        position: new mp.Vector3(-1922.1099, 3749.7627, -99.6549),
        radius: 1
    },

    // Координаты блипа выхода
    exitBlip: {
        id: 126,
        position: new mp.Vector3(-1922.1099, 3749.7627, -99.6549)
    },

    // Координаты маркера выхода
    exitMarker: {
        id: 42,
        position: new mp.Vector3(-1922.1099, 3749.7627, -99.6549),
        scale: 2,
        optionals: {
            color: [148, 7, 0, 155]
        }
    },

    // Куда телепортируем игрока после того, как он пройдет по невидимому мосту
    teleportInPortal: {
        position: new mp.Vector3(-1869.8374, 3750.2109, -99.8572),
        heading: 85,
        fade: 2000 // Затемнение экрана перед телепортом
    },

    // Куда возвращаем игрока после окончания квеста
    teleportAfterEnd: {
        position: new mp.Vector3(-1127.6835, 4740.6064, 236.9707),
        heading: 2.8,
        fade: 2000 // Затемнение экрана перед телепортом
    }
}
