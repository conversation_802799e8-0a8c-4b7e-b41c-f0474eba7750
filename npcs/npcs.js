/**
 * Доступные параметры на каждый npc.
 * @param  {Number}     id                      ИД NPC (Обязательный)
 * @param  {String}     skin                    Скин педа (Обязательный).
 * @param  {String}     description             Описание педа (Обязательный чтобы понимать что это).
 * @param  {Number}     posX                    X кооридана педа (Обязательный).
 * @param  {Number}     posY                    Y кооридана педа (Обязательный).
 * @param  {Number}     posZ                    Z кооридана педа (Обязательный).
 * @param  {Number}     posA                    A кооридана педа (Обязательный).
 * @param  {Number}     dimension               Дименшен педа (Обязательный).
 * @param  {Number}     dialogId                ID диалога педа.
 * @param  {String}     type                    Ивент вызова для внутренней системы (Обычно для всяких магазинов юзается).
 * @param  {String}     animDic                 Библиотека анимации
 * @param  {String}     animName                Название анимации
 * @param  {String}     scenario                Какой сценарий анимки применять педу
 * @param  {String}     speechName              Название речи (Гташная)
 * @param  {String}     voiceName               Название голоса (Гташная)
 * @param  {String}     speechParam             Параметры речи (Гташная)
 * @param  {Number}     timer                   Каждые сколько MS вызывать речь
 * @param  {Number}     timerId                 ID речи которое используется в моде
 * @param  {Boolean}    snowBalls               Будет ли кидать этот NPC снежки в игрока
 * @param  {Boolean}    enabled                 Включен ли NPC на сервере (По умолчанию true, если false то NPC не будет создан)
 * @param  {Number}     room                    ID комнаты в которой будет рендериться NPC
 * @param  {Number}     radius                  Радиус взаимодействия

 * @param  {Object}     blip                    Добавляет NPC метку на карте
 * @param  {Number}     blip.id                 ID блипа
 * @param  {Number}     blip.color              Цвет блипа
 * @param  {String}     blip.name               Перевод блипа (i18n)
 * @param  {Number}     blip.dimension          Дименшен для блипа
 * @param  {Boolean}    blip.shortRange         Показывать ли блип только на близком расстоянии
 *
 * @param  {Object}     gpt                     Добавляем инфорацию для GPT
 * @param  {Number}     gpt.name                Имя NPC для GPT
 * @param  {Number}     gpt.description         Описание роли NPC для GPT, с которой будет работать GPT
 *
 * @param  {Number}     houseInteriorId         ИД интерьера семейного педа
 */

module.exports = [
	{
		id: 1,
		dialogId: 3,
		description: 'Помощник на спавне',
		gpt: {
			name: 'Ivan Chashin',
			description:
				'You will now engage in a conversation while playing the role of {name}, the guide for beginners in the game GTA 5 (Majestic RP) in Los Santos. Your purpose is to help newbies get started playing Majestic RP (GTA 5 RP server). Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions for beginners so they can start playing. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 's_m_y_airworker',
		posX: -1032.0163,
		posY: -2734.165,
		posZ: 20.1692,
		posA: 115,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechName: 'GENERIC_HI',
		voiceName: 'TAXIKWAK',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '2',
		dialogId: '1',
		description: 'Руководитель мусоропереработки',
		type: null,
		skin: 's_m_y_garbage',
		posX: '-430.8413',
		posY: '-1726.2239',
		posZ: '18.9448',
		posA: '45.2',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '3',
		dialogId: '2',
		description: 'Заведующий Downtown Cab Co',
		type: null,
		skin: 's_m_y_uscg_01',
		posX: '1157.4179',
		posY: '-3194.2211',
		posZ: '-39.0079',
		posA: '267.7',
		dimension: '10',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '4',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_y_shop_mid',
		posX: '126.9975',
		posY: '-224.2796',
		posZ: '54.5578',
		posA: '65.4312',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '5',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_m_shop_high',
		posX: '-165.1346',
		posY: '-303.3208',
		posZ: '39.7333',
		posA: '247.2602',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '6',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_y_shop_mid',
		posX: '-816.6813',
		posY: '-1072.8508',
		posZ: '11.3281',
		posA: '115.8267',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '7',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_y_shop_low',
		posX: '-1095.7963',
		posY: '2712.1970',
		posZ: '19.1078',
		posA: '129.3448',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '8',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_m_shop_high',
		posX: '-708.9946',
		posY: '-151.7132',
		posZ: '37.4151',
		posA: '110.5459',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '9',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_m_shop_high',
		posX: '-1448.6315',
		posY: '-237.7984',
		posZ: '49.8134',
		posA: '43.9110',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '10',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_y_shop_low',
		posX: '1692.0214',
		posY: '4817.2158',
		posZ: '42.0630',
		posA: '2.4073',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '11',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_y_shop_low',
		posX: '-0.8151',
		posY: '6510.6621',
		posZ: '31.8778',
		posA: '317.1766',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '12',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_y_shop_mid',
		posX: '612.9187',
		posY: '2762.6909',
		posZ: '42.0880',
		posA: '267.2184',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '13',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_y_shop_mid',
		posX: '-1193.8819',
		posY: '-766.9017',
		posZ: '17.3161',
		posA: '213.2270',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '14',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_y_shop_low',
		posX: '78.0489',
		posY: '-1387.5937',
		posZ: '29.3761',
		posA: '175.8661',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '15',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_y_shop_low',
		posX: '1201.9716',
		posY: '2707.4838',
		posZ: '38.2226',
		posA: '89.1718',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '16',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_y_shop_mid',
		posX: '-3169.3591',
		posY: '1043.2304',
		posZ: '20.8632',
		posA: '71.1637',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 17,
		dialogId: 4,
		description: 'Сотрудник банка',
		type: null,
		skin: 'ig_andreas',
		posX: 1188.57,
		posY: 2702.31,
		posZ: 38.16,
		posA: 182.6,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'S_M_M_TRUCKER_01_BLACK_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 18,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 24.4055,
		posY: -1345.5687,
		posZ: 29.497,
		posA: 265.4064,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 19,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: -1221.4847,
		posY: -908.0635,
		posZ: 12.3264,
		posA: 34.9277,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 20,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: -1486.4918,
		posY: -377.6942,
		posZ: 40.1634,
		posA: 133.4734,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 21,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 372.9071,
		posY: 328.0604,
		posZ: 103.5664,
		posA: 255.5813,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 22,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 1134.2299,
		posY: -983.0878,
		posZ: 46.4158,
		posA: 276.1877,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 23,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 2555.2957,
		posY: 380.8274,
		posZ: 108.6229,
		posA: 353.5046,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 24,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 2676.3984,
		posY: 3280.2617,
		posZ: 55.2411,
		posA: 327.4132,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 25,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 1959.1921,
		posY: 3741.4338,
		posZ: 32.3438,
		posA: 303.0154,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 26,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 1391.606,
		posY: 3606.0178,
		posZ: 34.9809,
		posA: 195.7358,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 27,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 1165.6055,
		posY: 2710.8682,
		posZ: 38.1577,
		posA: 176.4439,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 28,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 549.3413,
		posY: 2669.72,
		posZ: 42.1565,
		posA: 102.7636,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 29,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 1698.5593,
		posY: 4922.4966,
		posZ: 42.0637,
		posA: 322.1143,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 30,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 1728.5394,
		posY: 6416.7476,
		posZ: 35.0372,
		posA: 244.4688,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 31,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: -3243.9702,
		posY: 1000.0651,
		posZ: 12.8307,
		posA: 359.0963,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 32,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: -3040.511,
		posY: 583.9762,
		posZ: 7.9089,
		posA: 23.4136,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 33,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: -2966.3379,
		posY: 391.1537,
		posZ: 15.0433,
		posA: 81.2685,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	/*     {
			id: '34',
			dialogId: '10',
			description: 'Владелец виноградника',
			type: null,
			skin: 's_m_m_gardener_01',
			posX: '-1873.0596',
			posY: '2069.8693',
			posZ: '140.9975',
			posA: '340.64',
			dimension: '0',
			speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
		}, */
	{
		id: 35,
		dialogId: 104,
		description: 'Владелец шахты',
		gpt: {
			name: 'Yannis Whit',
			description:
				'You will now engage in a conversation while playing the role of {name}, the owner of a mine. Your purpose is to discuss topics related to the mine and invite others to work there. Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions related to the mine and employment opportunities. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 's_m_y_dockwork_01',
		posX: 1353.29,
		posY: -258.15,
		posZ: 94.19,
		posA: 136.38,
		dimension: -1,
		speechName: 'GENERIC_HI',
		voiceName: 'G_M_Y_ARMGOON_02_WHITE_ARMENIAN_MINI_02',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 36,
		dialogId: 5,
		description: 'Медик',
		gpt: {
			name: 'Eric Steinmann',
			description:
				'You will now engage in a conversation while playing the role of {name}, a doctor who can heal people. Your purpose is to discuss topics related to medicine, health, and well-being. Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions related to medical conditions, treatments, and general health advice. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 's_m_m_paramedic_01',
		posX: 1144.4967,
		posY: -1538.5978,
		posZ: 35.0256,
		posA: -39.68,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '37',
		dialogId: null,
		description: 'Менеджер GoPostal',
		type: null,
		skin: 'cs_andreas',
		posX: '1157.4179',
		posY: '-3194.2211',
		posZ: '-39.0079',
		posA: '267.7',
		dimension: '11',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	// {
	//     id: '38',
	//     dialogId: '1',
	//     description: 'Руководитель мусоропереработки',
	//     type: null,
	//     skin: 's_m_y_garbage',
	//     posX: '2338.7729',
	//     posY: '3139.9064',
	//     posZ: '48.2029',
	//     posA: '95.7',
	//     dimension: '0',
	//     animDic: null,
	//     animName: null,
	//     speechName: null,
	//     voiceName: null,
	//     speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
	// },
	// {
	//     id: '39',
	//     dialogId: null,
	//     description: null,
	//     type: 'barber_shop',
	//     skin: 's_f_m_fembarber',
	//     posX: '817.349',
	//     posY: '-184.541',
	//     posZ: '37.569',
	//     posA: '134.069',
	//     dimension: '0',
	//     animDic: null,
	//     animName: null,
	//     speechName: null,
	//     voiceName: null,
	//     speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
	// },
	{
		id: '40',
		dialogId: null,
		description: null,
		type: 'barber_shop',
		skin: 's_f_m_fembarber',
		posX: '134.749',
		posY: '-1708.106',
		posZ: '29.292',
		posA: '146.281',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '41',
		dialogId: null,
		description: null,
		type: 'barber_shop',
		skin: 's_f_m_fembarber',
		posX: '-1284.038',
		posY: '-1115.635',
		posZ: '6.990',
		posA: '85.177',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '42',
		dialogId: null,
		description: null,
		type: 'barber_shop',
		skin: 's_f_m_fembarber',
		posX: '1930.855',
		posY: '3728.141',
		posZ: '32.844',
		posA: '220.243',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '43',
		dialogId: null,
		description: null,
		type: 'barber_shop',
		skin: 's_f_m_fembarber',
		posX: '1211.521',
		posY: '-470.704',
		posZ: '66.208',
		posA: '79.543',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '44',
		dialogId: null,
		description: null,
		type: 'barber_shop',
		skin: 's_f_m_fembarber',
		posX: '-30.804',
		posY: '-151.648',
		posZ: '57.077',
		posA: '349.238',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '45',
		dialogId: null,
		description: null,
		type: 'barber_shop',
		skin: 's_f_m_fembarber',
		posX: '-278.205',
		posY: '6230.279',
		posZ: '31.696',
		posA: '49.216',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '46',
		dialogId: null,
		description: 'Асистент офиса',
		type: null,
		skin: 'mp_f_execpa_01',
		posX: '-139.0630',
		posY: '-633.9569',
		posZ: '168.8205',
		posA: '352.54',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '48',
		dialogId: null,
		description: null,
		type: 'ammo_shop',
		skin: 's_m_m_ammucountry',
		posX: '1692.733',
		posY: '3761.895',
		posZ: '34.705',
		posA: '218.535',
		dimension: '0',
		speechName: 'AMMUCITY',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '49',
		dialogId: null,
		description: null,
		type: 'ammo_shop',
		skin: 's_m_m_ammucountry',
		posX: '-330.933',
		posY: '6085.677',
		posZ: '31.455',
		posA: '207.323',
		dimension: '0',
		speechName: 'AMMUCITY',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '50',
		dialogId: null,
		description: null,
		type: 'ammo_shop',
		skin: 's_m_y_ammucity_01',
		posX: '253.629',
		posY: '-51.305',
		posZ: '69.941',
		posA: '59.656',
		dimension: '0',
		speechName: 'AMMUCITY',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '51',
		dialogId: null,
		description: null,
		type: 'ammo_shop',
		skin: 's_m_y_ammucity_01',
		posX: '841.363',
		posY: '-1035.350',
		posZ: '28.195',
		posA: '328.528',
		dimension: '0',
		speechName: 'AMMUCITY',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '52',
		dialogId: null,
		description: null,
		type: 'ammo_shop',
		skin: 's_m_y_ammucity_01',
		posX: '-661.317',
		posY: '-933.515',
		posZ: '21.829',
		posA: '152.798',
		dimension: '0',
		speechName: 'AMMUCITY',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '53',
		dialogId: null,
		description: null,
		type: 'ammo_shop',
		skin: 's_m_y_ammucity_01',
		posX: '-1304.413',
		posY: '-395.902',
		posZ: '36.696',
		posA: '44.440',
		dimension: '0',
		speechName: 'AMMUCITY',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '54',
		dialogId: null,
		description: null,
		type: 'ammo_shop',
		skin: 's_m_y_ammucity_01',
		posX: '-1118.037',
		posY: '2700.568',
		posZ: '18.554',
		posA: '196.070',
		dimension: '0',
		speechName: 'AMMUCITY',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '55',
		dialogId: null,
		description: null,
		type: 'ammo_shop',
		skin: 's_m_y_ammucity_01',
		posX: '2566.596',
		posY: '292.286',
		posZ: '108.735',
		posA: '337.291',
		dimension: '0',
		speechName: 'AMMUCITY',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '56',
		dialogId: null,
		description: null,
		type: 'ammo_shop',
		skin: 's_m_y_ammucity_01',
		posX: '-3173.182',
		posY: '1089.176',
		posZ: '20.839',
		posA: '223.930',
		dimension: '0',
		speechName: 'AMMUCITY',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '57',
		dialogId: null,
		description: null,
		type: 'ammo_shop',
		skin: 's_m_y_ammucity_01',
		posX: '23.394',
		posY: '-1105.455',
		posZ: '29.797',
		posA: '147.921',
		dimension: '0',
		speechName: 'AMMUCITY',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '58',
		dialogId: '6',
		description: 'Секретарь FIB',
		type: null,
		skin: 'cs_debra',
		posX: '2522.968',
		posY: '-337.827',
		posZ: '101.893',
		posA: '28.123',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '59',
		dialogId: '7',
		description: 'Сотрудница автошколы',
		type: null,
		skin: 's_f_m_shop_high',
		posX: -928.9597,
		posY: -2049.093,
		posZ: 9.5364,
		posA: 135.058,
		dimension: '0',
		speechName: 'S_F_M_SHOP_HIGH_WHITE_MINI_01',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	// {
	//     id: '60',
	//     dialogId: '8',
	//     description: 'Секретарь Банка',
	//     type: null,
	//     skin: 'cs_debra',
	//     posX: '251.4405',
	//     posY: '212.5283',
	//     posZ: '106.2868',
	//     posA: '8.87',
	//     dimension: '0',
	//     speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
	// },
	{
		id: '61',
		dialogId: null,
		description: null,
		type: 'clothes_shop',
		skin: 's_f_y_shop_mid',
		posX: '422.9074',
		posY: '-811.5448',
		posZ: '29.4870',
		posA: '354.59',
		dimension: '-1',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '62',
		dialogId: null,
		description: null,
		type: 'ammo_shop',
		skin: 's_m_y_ammucity_01',
		posX: '809.6042',
		posY: '-2159.2922',
		posZ: '29.6190',
		posA: '1.9544',
		dimension: '0',
		speechName: 'AMMUCITY',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 63,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: -706.0665,
		posY: -913.7526,
		posZ: 19.2156,
		posA: 86.2099,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 64,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: -46.8849,
		posY: -1758.1702,
		posZ: 29.421,
		posA: 74.9919,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 65,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 1164.7438,
		posY: -322.651,
		posZ: 69.2051,
		posA: 85,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 66,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: -1819.3898,
		posY: 793.4288,
		posZ: 138.0842,
		posA: 130.02,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '67',
		dialogId: '0',
		description: 'Управляющий LSP',
		type: null,
		skin: 's_m_m_ups_01',
		posX: '1196.28',
		posY: '-3252.32',
		posZ: '7.09',
		posA: '93.67',
		dimension: '0',
		animDic: 'amb@world_human_clipboard@male@idle_a',
		animName: 'idle_c',
		speechName: 'GENERIC_HI',
		voiceName: 'S_M_M_TRUCKER_01_BLACK_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '68',
		dialogId: 13,
		description: 'Асистент в мэрии',
		gpt: {
			name: 'Maria Lopez',
			description:
				'You will now engage in a conversation while playing the role of {name}, an assistant at the town hall of Los Santos. Your purpose is to discuss topics related to the state legislature of Los Santos and provide information about local government matters. Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions related to the town hall, local legislation, and government services in Los Santos. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 's_f_y_airhostess_01',
		posX: '-548.8806',
		posY: '-197.5025',
		posZ: '38.0871',
		posA: '199.3983',
		dimension: '0',
		speechParam: null,
	},
	{
		id: '69',
		dialogId: null,
		description: null,
		type: 'barber_shop',
		skin: 's_m_m_hairdress_01',
		posX: '-821.875',
		posY: '-183.3861',
		posZ: '37.5689',
		posA: '204.10',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '70',
		dialogId: null,
		description: null,
		type: 'tattoo_shop',
		skin: 'u_m_y_tattoo_01',
		posX: '319.8908',
		posY: '181.3249',
		posZ: '103.5865',
		posA: '248.5782',
		dimension: '-1',
		speechName: 'GENERIC_HI',
		voiceName: 'U_M_Y_TATTOO_01_WHITE_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '71',
		dialogId: null,
		description: null,
		type: 'tattoo_shop',
		skin: 'u_m_y_tattoo_01',
		posX: '1324.8378',
		posY: '-1650.5114',
		posZ: '52.2752',
		posA: '130.9323',
		dimension: '-1',
		speechName: 'GENERIC_HI',
		voiceName: 'U_M_Y_TATTOO_01_WHITE_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '72',
		dialogId: null,
		description: null,
		type: 'tattoo_shop',
		skin: 'u_m_y_tattoo_01',
		posX: '-1151.8427',
		posY: '-1424.2429',
		posZ: '4.9545',
		posA: '110.1260',
		dimension: '-1',
		speechName: 'GENERIC_HI',
		voiceName: 'U_M_Y_TATTOO_01_WHITE_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '73',
		dialogId: null,
		description: null,
		type: 'tattoo_shop',
		skin: 'u_m_y_tattoo_01',
		posX: '-3170.8445',
		posY: '1073.1803',
		posZ: '20.8292',
		posA: '338.3533',
		dimension: '-1',
		speechName: 'GENERIC_HI',
		voiceName: 'U_M_Y_TATTOO_01_WHITE_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '74',
		dialogId: null,
		description: null,
		type: 'tattoo_shop',
		skin: 'u_m_y_tattoo_01',
		posX: '1862.4785',
		posY: '3748.363',
		posZ: '33.0319',
		posA: '27.6441',
		dimension: '-1',
		speechName: 'GENERIC_HI',
		voiceName: 'U_M_Y_TATTOO_01_WHITE_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '75',
		dialogId: null,
		description: null,
		type: 'tattoo_shop',
		skin: 'u_m_y_tattoo_01',
		posX: '-292.0688',
		posY: '6199.7891',
		posZ: '31.4871',
		posA: '223.9151',
		dimension: '-1',
		speechName: 'GENERIC_HI',
		voiceName: 'U_M_Y_TATTOO_01_WHITE_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	// {
	//     id: 76,
	//     dialogId: 12,
	//     description: 'Менеджер строительной компании',
	//     type: null,
	//     skin: 's_m_m_dockwork_01',
	//     posX: -167.5492,
	//     posY: -1027.7651,
	//     posZ: 27.2748,
	//     posA: 150.55,
	//     dimension: -1,
	//     speechName: 'GENERIC_HI',
	//     voiceName: 'G_M_Y_ARMGOON_02_WHITE_ARMENIAN_MINI_02',
	//     speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
	// },
	{
		id: 77,
		dialogId: 11,
		description: 'Лесоруб',
		gpt: {
			name: 'Donald Curry',
			description:
				'You will now engage in a conversation while playing the role of {name}, a lumberjack from a sawmill in Paleto Bay. Your purpose is to discuss topics related to lumberjacking and teach others about safety in this profession. Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions related to lumberjacking, sawmill operations, and safety practices in the industry. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 's_m_y_construct_01',
		posX: -840.78,
		posY: 5401.11,
		posZ: 34.61,
		posA: 350.66,
		dimension: 0,
		speechName: 'GREET_ACROSS_STREET',
		voiceName: 'A_M_Y_EPSILON_01_KOREAN_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '78',
		dialogId: '14',
		description: 'Управляющий LSP в Палето',
		type: null,
		skin: 's_m_m_ups_01',
		posX: '75.2310',
		posY: '6355.3354',
		posZ: '31.3758',
		posA: '75.96',
		dimension: '0',
		animDic: 'amb@world_human_clipboard@male@idle_a',
		animName: 'idle_c',
		speechName: 'GENERIC_HI',
		voiceName: 'S_M_M_TRUCKER_01_BLACK_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '80',
		dialogId: '16',
		description: 'Сотрудник полиции',
		type: null,
		skin: 's_m_y_sheriff_01',
		posX: '-453.0871',
		posY: '6006.1132',
		posZ: '31.4612',
		posA: '42.6112',
		dimension: '0',
		speechName: 'GENERIC_HI',
		voiceName: 'S_M_Y_SHERIFF_01_WHITE_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 81,
		dialogId: 15,
		description: 'Сотрудник полиции',
		type: null,
		skin: 's_m_y_cop_01',
		posX: 442.7473,
		posY: -981.9561,
		posZ: 30.6783,
		posA: 85.0394,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'S_M_Y_COP_01_BLACK_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 82,
		dialogId: 17,
		description: 'Помощник на спавне',
		gpt: {
			name: 'Jung Lee',
			description:
				'You will now engage in a conversation while playing the role of {name}, the guide for beginners in the game GTA 5 (Majestic RP) in Los Santos. Your purpose is to help newbies get started playing Majestic RP (GTA 5 RP server). Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions for beginners so they can start playing. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 'csb_agent',
		posX: 415.0643,
		posY: -621.8459,
		posZ: 28.7021,
		posA: 226.34,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechName: 'GENERIC_HI',
		voiceName: 'STRETCH',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 83,
		dialogId: 18,
		description: 'Помощник на спавне',
		gpt: {
			name: 'Chris Formage',
			description:
				'You will now engage in a conversation while playing the role of {name}, the guide for beginners in the game GTA 5 (Majestic RP) in Los Santos. Your purpose is to help newbies get started playing Majestic RP (GTA 5 RP server). Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions for beginners so they can start playing. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 'csb_agent',
		posX: -178.2822,
		posY: 6435.0571,
		posZ: 31.9157,
		posA: 252.44,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechName: 'GENERIC_HI',
		voiceName: 'STRETCH',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 84,
		dialogId: 19,
		description: 'Помощник на спавне',
		gpt: {
			name: 'Jack Haley',
			description:
				'You will now engage in a conversation while playing the role of {name}, the guide for beginners in the game GTA 5 (Majestic RP) in Los Santos. Your purpose is to help newbies get started playing Majestic RP (GTA 5 RP server). Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions for beginners so they can start playing. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 'csb_agent',
		posX: -3132.08,
		posY: 1130.48,
		posZ: 20.67,
		posA: 18.67,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechName: 'GENERIC_HI',
		voiceName: 'STRETCH',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '85',
		dialogId: '20',
		description: 'Механник',
		type: null,
		skin: 's_m_y_xmech_01',
		posX: '540.1097',
		posY: '-172.5578',
		posZ: '54.4813',
		posA: '125.77',
		dimension: '0',
		speechName: 'GENERIC_HI',
		voiceName: 'S_M_M_GENERICMECHANIC_01_BLACK_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '86',
		dialogId: '25',
		description: 'Drug Dealer',
		type: null,
		skin: 'ig_g',
		posX: -256.6,
		posY: -1518.87,
		posZ: 31.56,
		posA: 207.85,
		dimension: 0,
	},
	{
		id: 87,
		dialogId: 26,
		description: 'Drug Dealer',
		type: null,
		skin: 'ig_ballasog',
		posX: -96.0702,
		posY: -1798.9539,
		posZ: 28.9942,
		posA: 347.018,
		dimension: 0,
	},
	{
		id: '88',
		dialogId: '28',
		description: 'Drug Dealer',
		type: null,
		skin: 'g_m_y_mexgoon_01',
		posX: '979.947',
		posY: '-1831.669',
		posZ: '31.350',
		posA: '25.72',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		room: 730134193,
	},
	{
		id: '89',
		dialogId: '29',
		description: 'Drug Dealer',
		type: null,
		skin: 'g_m_y_salvagoon_01',
		posX: '1019.2890',
		posY: '-2546.2448',
		posZ: '28.3023',
		posA: '-52.9065',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '90',
		dialogId: '27',
		description: 'Drug Dealer',
		type: null,
		skin: 'ig_claypain',
		posX: '468.144',
		posY: '-1302.354',
		posZ: '29.203',
		posA: '182.02',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '91',
		dialogId: '21',
		description: 'Strada Boss',
		type: null,
		skin: 'ig_abigail',
		posX: '1391.777',
		posY: '1136.80',
		posZ: '114.4432',
		posA: '47.81',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 92,
		dialogId: 22,
		description: 'Plaza Boss',
		type: null,
		skin: 'g_m_m_mexboss_01',
		posX: 398.21,
		posY: -19.87,
		posZ: 91.93,
		posA: 328.57,
		dimension: 0,
	},
	{
		id: '93',
		dialogId: '24',
		description: 'Shateigashira',
		type: null,
		skin: 'g_m_m_korboss_01',
		posX: '-1535.2504',
		posY: '90.1558',
		posZ: '56.7728',
		posA: '-95.5976',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '94',
		dialogId: '23',
		description: 'Авторитет',
		type: null,
		skin: 'ig_popov',
		posX: '-1500.29',
		posY: '858.000',
		posZ: '181.5948',
		posA: '52.69',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '95',
		dialogId: '30',
		description: 'Владелец пирса',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: '-766.6239',
		posY: '-1472.0937',
		posZ: '5.000',
		posA: '254.14',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '96',
		dialogId: '31',
		description: 'Владелец пирса',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: '1320.2692',
		posY: '4315.0327',
		posZ: '38.1433',
		posA: '47.63',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '97',
		dialogId: 33,
		description: 'Angels of Death MC',
		type: null,
		skin: 'mp_m_weapexp_01',
		posX: '1984.3500',
		posY: '3054.4316',
		posZ: '47.2151',
		posA: '263.60',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '98',
		dialogId: 34,
		description: 'The LostMC',
		type: null,
		skin: 'g_m_y_lost_02',
		posX: '988.0007',
		posY: '-95.1099',
		posZ: '74.8455',
		posA: '201.00',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	// {
	//     id: 99,
	//     dialogId: 32,
	//     description: 'Прораб мясокомбината',
	//     type: null,
	//     skin: 's_m_y_chef_01',
	//     posX: 965.7965,
	//     posY: -2127.4157,
	//     posZ: 31.4681,
	//     posA: 335.33,
	//     dimension: -1
	// },
	{
		id: 100,
		dialogId: 35,
		description: 'Бармен Bahama mama',
		type: null,
		skin: 's_m_y_barman_01',
		posX: -1376.2229,
		posY: -628.4956,
		posZ: 30.8196,
		posA: 32.78,
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 101,
		dialogId: 36,
		description: 'Бармен стрип клуба',
		type: null,
		skin: 'mp_f_stripperlite',
		posX: 128.79,
		posY: -1282.87,
		posZ: 29.27,
		posA: 113.73,
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 102,
		dialogId: 37,
		description: 'Бармен казино',
		type: null,
		skin: 's_f_y_casino_01',
		posX: 1109.99,
		posY: 208.18,
		posZ: -49.44,
		posA: 81.29,
		dimension: 55000,
	},
	{
		id: 103,
		dialogId: 37,
		description: 'Бармен казино',
		type: null,
		skin: 's_f_y_casino_01',
		posX: 959.44,
		posY: 70.94,
		posZ: 112.55,
		posA: 143.72,
		dimension: 0,
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 104,
		dialogId: 37,
		description: 'Бармен казино',
		type: null,
		skin: 's_f_y_casino_01',
		posX: 947.0,
		posY: 16.26,
		posZ: 116.16,
		posA: 58.1,
		dimension: 0,
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '105',
		dialogId: 38,
		description: 'Бармен The Palace',
		type: null,
		skin: 's_f_y_bartender_01',
		posX: '-1585.00',
		posY: '-3012.90',
		posZ: '-76',
		posA: '92.70',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '106',
		dialogId: 38,
		description: 'Бармен The Palace',
		type: null,
		skin: 's_f_y_bartender_01',
		posX: '-1578.00',
		posY: '-3016.70',
		posZ: '-79.0',
		posA: '359.28',
		dimension: '876',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 107,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: -161.15,
		posY: 6321.32,
		posZ: 31.58,
		posA: 318.45,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 108,
		dialogId: null,
		description: null,
		type: 'item_shop',
		skin: 'mp_m_shopkeep_01',
		posX: 161.76,
		posY: 6642.8,
		posZ: 31.69,
		posA: 225.51,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'MP_M_SHOPKEEP_01_LATINO_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 109,
		dialogId: 39,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: 1852.94,
		posY: 2582.13,
		posZ: 45.67,
		posA: 300,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '110',
		dialogId: 40,
		description: 'Pops Diner',
		type: null,
		skin: 'a_f_m_tourist_01',
		posX: '1590.5451',
		posY: '6456.0083',
		posZ: '26.0139',
		posA: '155.1792',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '111',
		dialogId: 41,
		description: 'Инструктор летной школы',
		type: null,
		skin: 's_m_m_pilot_01',
		posX: '-1155.13',
		posY: '-2716.31',
		posZ: '19.88',
		posA: '300.25',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '112',
		dialogId: 42,
		description: 'Irishman',
		type: null,
		skin: 'csb_miguelmadrazo',
		posX: -3010.5361,
		posY: 80.8002,
		posZ: 11.6807,
		posA: 22.9474,
		dimension: 0,
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '113',
		dialogId: 69,
		description: 'Бармен Arcades',
		type: null,
		skin: 's_f_y_bartender_01',
		posX: '2723.72',
		posY: '-385.74',
		posZ: '-48.99',
		posA: '276.87',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	// {
	//     id: 114,
	//     dialogId: 76,
	//     description: 'Работник бургершота',
	//     type: null,
	//     skin: 'u_m_y_burgerdrug_01',
	//     posX: -1174.97,
	//     posY: -894.48,
	//     posZ: 13.79,
	//     posA: 5.91,
	//     dimension: 0,
	// },
	{
		id: '115',
		dialogId: 44,
		description: 'Армеец',
		type: null,
		skin: 's_m_y_armymech_01',
		posX: '-2342.91',
		posY: '3263.91',
		posZ: '32.82',
		posA: '269.48',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '116',
		dialogId: 45,
		description: 'Weazel News',
		type: null,
		skin: 'ig_lifeinvad_01',
		posX: '-586.63',
		posY: '-921.34',
		posZ: '23.86',
		posA: '135.70',
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	// {
	//     id: '117',
	//     dialogId: 46,
	//     description: 'El Consejero',
	//     type: null,
	//     skin: 'mp_m_forgery_01',
	//     posX: '-3020.2241',
	//     posY: '82.8534',
	//     posZ: '11.6655',
	//     posA: '6.8425',
	//     dimension: '0',
	//     speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
	// },
	{
		id: 118,
		dialogId: 47,
		description: 'Руководитель автобусной компании',
		type: null,
		skin: 'a_m_o_ktown_01',
		posX: -767.7775,
		posY: -2064.027,
		posZ: 9.0151,
		posA: -14.736,
		dimension: -1,
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	// {
	//     id: '119',
	//     dialogId: 48,
	//     description: 'Thomas Jackson',
	//     type: null,
	//     skin: 's_m_y_chef_01',
	//     posX: '1595.49',
	//     posY: '6454.47',
	//     posZ: '26.01',
	//     posA: '92.2',
	//     dimension: '0',
	//     speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
	// },
	// {
	//     id: '120',
	//     dialogId: 49,
	//     description: 'Eliot Anderson',
	//     type: null,
	//     skin: 'a_m_y_business_02',
	//     posX: -1275.816,
	//     posY: 316.347,
	//     posZ: 65.511,
	//     posA: 166.02,
	//     dimension: '0',
	//     speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
	// },
	{
		id: '121',
		dialogId: 50,
		description: 'Roman Turchak',
		type: null,
		skin: 'ig_siemonyetarian',
		posX: -1592.87,
		posY: -864.81,
		posZ: 10.1,
		posA: 183,
		dimension: '0',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '122',
		dialogId: '51',
		description: 'Медик',
		type: null,
		skin: 's_m_m_paramedic_01',
		posX: -251.73,
		posY: 6335.38,
		posZ: 32.42,
		posA: 172.8,
		dimension: 0,
		animDic: 'amb@world_human_clipboard@male@idle_a',
		animName: 'idle_c',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '123',
		dialogId: '52',
		description: 'Медик',
		type: null,
		skin: 's_m_m_paramedic_01',
		posX: 1830.56,
		posY: 3676.52,
		posZ: 34.26,
		posA: 211.58,
		dimension: 0,
		animDic: 'amb@world_human_clipboard@male@idle_a',
		animName: 'idle_c',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: '124',
		dialogId: '53',
		description: 'Путешественник',
		type: null,
		skin: 'a_m_y_skater_01',
		posX: '223.26',
		posY: '5254.10',
		posZ: '602.61',
		posA: '315.29',
		dimension: '0',
		animDic: 'anim@amb@business@bgen@bgen_no_work@',
		animName: 'sit_phone_phoneputdown_sleeping_nowork',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 125,
		dialogId: 54,
		description: 'Рыбак',
		type: null,
		skin: 's_m_y_grip_01',
		posX: -1680.73,
		posY: 4462.7,
		posZ: 1.68,
		posA: 354.07,
		dimension: '0',
		animDic: 'amb@world_human_stand_fishing@idle_a',
		animName: 'idle_a',
		speechName: 'GENERIC_HI',
		voiceName: 'A_M_M_BEACH_01_LATINO_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	// {
	//     id: '126',
	//     dialogId: 55,
	//     description: 'Мафиози',
	//     type: null,
	//     skin: 'u_m_m_spyactor',
	//     posX: '-57.56',
	//     posY: '360.97',
	//     posZ: '113.05',
	//     posA: '176.38',
	//     dimension: '0',
	//     speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
	// },
	{
		id: 127,
		dialogId: 56,
		description: 'Баскетболист',
		type: null,
		skin: 's_m_y_prismuscl_01',
		posX: 55.72,
		posY: 44.09,
		posZ: 73.51,
		posA: 261.8,
		dimension: 0,
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 128,
		dialogId: 57,
		description: 'Качок',
		type: null,
		skin: 'a_m_y_musclbeac_02',
		posX: -1195.29,
		posY: -1568.0,
		posZ: 4.62,
		posA: 119.56,
		dimension: 0,
		animDic: 'amb@world_human_muscle_flex@arms_in_front@base',
		animName: 'base',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 129,
		dialogId: 58,
		description: 'Старушка',
		type: null,
		skin: 'a_f_m_salton_01',
		posX: -455.08,
		posY: 6061.31,
		posZ: 31.74,
		posA: 336.74,
		dimension: 0,
		animDic: 'switch@trevor@floyd_crying',
		animName: 'console_end_loop_floyd',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 130,
		dialogId: 59,
		description: 'Охотник',
		type: null,
		skin: 'ig_hunter',
		posX: -1487.82,
		posY: 4584.45,
		posZ: 39.38,
		posA: 125.57,
		dimension: 0,
		animDic: 'oddjobs@hunter',
		animName: 'idle_a',
		speechName: 'GREET_ACROSS_STREET',
		voiceName: 'A_M_Y_HIPPY_01_WHITE_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 131,
		dialogId: 60,
		description: 'Почтальон',
		type: null,
		skin: 's_m_m_postal_02',
		posX: -233.77,
		posY: -913.99,
		posZ: 32.31,
		posA: 318.1,
		dimension: 0,
	},
	{
		id: 132,
		dialogId: 61,
		description: 'Бармен Eastls Hospital',
		type: null,
		skin: 'mp_f_chbar_01',
		posX: 1136.72,
		posY: -1557.08,
		posZ: 35.02,
		posA: -8.5,
		dimension: 0,
	},
	{
		id: 133,
		dialogId: 43,
		description: 'Фермер',
		type: null,
		skin: 'a_m_m_farmer_01',
		posX: 2034.54,
		posY: 4983.47,
		posZ: 41.09,
		posA: 216.0,
		dimension: 0,
		timer: 45000,
		timerId: 8,
		snowBalls: true,
	},
	// {
	//     id: 134,
	//     dialogId: 62,
	//     description: 'Швейцар',
	//     type: null,
	//     skin: 'u_m_m_spyactor',
	//     posX: -1570.71,
	//     posY: -575.03,
	//     posZ: 108.52,
	//     posA: 34.89,
	//     dimension: 0,
	// },
	{
		id: 135,
		dialogId: 63,
		description: 'Бомж',
		type: null,
		skin: 'u_m_y_militarybum',
		posX: 121.37,
		posY: -1180.29,
		posZ: 30.96,
		posA: 232.35,
		dimension: 0,
		animDic: 'amb@world_human_bum_slumped@male@laying_on_left_side@base',
		animName: 'base',
		speechName: 'GENERIC_HI',
		voiceName: 'JOSEF',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 136,
		dialogId: 64,
		description: 'Брат дудушкина',
		type: null,
		skin: 'cs_josef',
		posX: 1408.26,
		posY: 3605.71,
		posZ: 39.0,
		posA: 53.9,
		dimension: 0,
		speechName: 'GENERIC_HI',
		voiceName: 'JOE',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 137,
		dialogId: 65,
		description: 'Сотрудница автошколы',
		type: null,
		skin: 's_f_m_shop_high',
		posX: -929.276,
		posY: -2036.712,
		posZ: 9.536,
		posA: -43.343,
		dimension: 0,
		speechName: 'S_F_M_SHOP_HIGH_WHITE_MINI_01',
		voiceName: 'SHOP_GREET',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 138,
		dialogId: 66,
		description: 'Сотрудница автошколы',
		type: null,
		skin: 'a_m_y_hippy_01',
		posX: 2221.3,
		posY: 5612.91,
		posZ: 54.68,
		posA: 95.35,
		dimension: 0,
		animDic: 'anim@amb@business@cfm@cfm_machine_no_work@',
		animName: 'transition_sleep_operator',
	},
	{
		id: 139,
		dialogId: 67,
		description: 'Владелец лодочной',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: -273.65,
		posY: 6640.99,
		posZ: 7.37,
		posA: 212.48,
		dimension: 0,
	},
	{
		id: 140,
		dialogId: 68,
		description: 'Студентка университета',
		type: null,
		skin: 'a2nier',
		posX: -1550.03,
		posY: 201.24,
		posZ: 63.89,
		posA: 73.18,
		dimension: 0,
		scenario: 'WORLD_HUMAN_STAND_MOBILE_UPRIGHT',
		timer: 95000,
		timerId: 19,
		enabled: false,
		blip: { id: 439, color: 5, name: 'blips.schoolPed', dimension: 0, shortRange: false },
	},
	{
		id: 141,
		dialogId: 61,
		description: 'Бармен FIB',
		type: null,
		skin: 'mp_f_chbar_01',
		posX: 2516.1032,
		posY: -342.1897,
		posZ: 94.092,
		posA: 179.066,
		dimension: 0,
	},

	{
		id: 142,
		dialogId: 70,
		description: 'Weapons seller',
		type: null,
		skin: 'csb_chin_goon',
		posX: -1234.686,
		posY: -1505.588,
		posZ: 4.373124,
		posA: -59.27837,
		dimension: -1,
		animDic: 'missfbi3_party_d',
		animName: 'stand_talk_loop_b_male1',
		timer: 101000,
		timerId: 2,
		snowBalls: true,
		marketId: 1, //+
	},
	{
		id: 143,
		dialogId: 71,
		description: 'Diamond seller',
		type: null,
		skin: 'a_m_o_ktown_01',
		posX: -1265.506,
		posY: -1461.871,
		posZ: 4.36823,
		posA: -43.99965,
		dimension: -1,
		animDic: 'rcmme_amanda1',
		animName: 'stand_loop_cop',
		timer: 100000,
		timerId: 3,
		snowBalls: true,
		marketId: 1, //+
	},
	{
		id: 144,
		dialogId: 72,
		description: 'Clothes seller',
		type: null,
		skin: 'a_m_m_eastsa_02',
		posX: -1283.908,
		posY: -1433.557,
		posZ: 4.608918,
		posA: -31.99958,
		dimension: -1,
		animDic: 'anim@heists@ornate_bank@chat_manager',
		animName: 'poor_clothes',
		timer: 100000,
		timerId: 14,
		snowBalls: true,
		marketId: 1, //+
	},

	{
		id: 145,
		dialogId: 87,
		description: 'Ambient Ped Market',
		type: null,
		skin: 'cs_jewelass',
		posX: -1239.81,
		posY: -1473.09,
		posZ: 4.34,
		posA: 350.63,
		dimension: -1,
		scenario: 'WORLD_HUMAN_STAND_MOBILE_UPRIGHT',
		timer: 80000,
		timerId: 5,
	},
	{
		id: 146,
		dialogId: 73,
		description: 'Fireworks Seller',
		type: null,
		skin: 'ig_kerrymcintosh',
		posX: -1254.983,
		posY: -1491.666,
		posZ: 4.327007,
		posA: 24.99988,
		dimension: -1,
		animDic: 'rcmme_amanda1',
		animName: 'stand_loop_cop',
		speechName: 'GENERIC_HI',
		voiceName: 'A_F_M_KTOWN_02_CHINESE_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		timer: 84000,
		timerId: 15,
		snowBalls: true,
		marketId: 1, //+
	},
	{
		id: 147,
		description: 'Ambient Ped Market 3',
		type: null,
		skin: 'a_m_m_mexlabor_01',
		posX: -1244.82,
		posY: -1464.88,
		posZ: 4.27,
		posA: 151.69,
		dimension: -1,
		animDic: 'anim@heists@ornate_bank@chat_manager',
		animName: 'average_car',
	},
	{
		id: 148,
		description: 'Ambient Ped Market 4',
		type: null,
		skin: 'a_m_y_methhead_01',
		posX: -1246.16,
		posY: -1465.04,
		posZ: 4.25,
		posA: 263.12,
		dimension: -1,
		animDic: 'anim@heists@ornate_bank@chat_manager',
		animName: 'wear_weird',
	},
	{
		id: 149,
		description: 'Ambient Ped Market 4',
		gpt: {
			name: 'James Richards',
			description:
				'You will now engage in a conversation while playing the role of {name}, a foreman working at a market. Your purpose is to discuss topics related to the market and your responsibilities in overseeing the work there. Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions related to market operations, employee management, and workplace practices in the market setting. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 's_m_y_construct_02',
		posX: -1256.22,
		posY: -1466.8,
		posZ: 4.31,
		posA: 324.75,
		dimension: -1,
		animDic: 'missheist_agency2aig_3',
		animName: 'chat_a_worker1',
	},
	{
		id: 150,
		description: 'Ambient Ped Market 5',
		dialogId: 88,
		type: null,
		skin: 'u_m_y_mani',
		posX: -1223.41,
		posY: -1462.52,
		posZ: 4.32,
		posA: 72.3,
		dimension: -1,
		scenario: 'WORLD_HUMAN_SMOKING',
	},
	{
		id: 151,
		description: 'Ambient Ped Market 6',
		type: null,
		skin: 's_m_y_construct_01',
		posX: -1254.7,
		posY: -1454.78,
		posZ: 4.36,
		posA: 38.66,
		dimension: -1,
		scenario: 'WORLD_HUMAN_WELDING',
	},
	{
		id: 152,
		description: 'Ambient Ped Market 7',
		type: null,
		skin: 'a_m_y_indian_01',
		posX: -1268.37,
		posY: -1451.31,
		posZ: 4.58,
		posA: 111.29,
		dimension: -1,
		scenario: 'WORLD_HUMAN_WINDOW_SHOP_BROWSE',
	},
	{
		id: 153,
		description: 'Ambient Ped Market 8',
		type: null,
		skin: 'a_f_y_hipster_04',
		posX: -1269.5,
		posY: -1436.38,
		posZ: 4.4,
		posA: 180.65,
		dimension: -1,
		scenario: 'WORLD_HUMAN_DRINKING',
	},
	{
		id: 154,
		description: 'Ambient Ped Market 9',
		type: null,
		skin: 'a_m_y_hipster_03',
		posX: -1270.73,
		posY: -1435.53,
		posZ: 4.42,
		posA: 196.59,
		dimension: -1,
		scenario: 'WORLD_HUMAN_DRINKING_FACILITY',
	},
	{
		id: 155,
		description: 'Ambient Ped Market 11',
		type: null,
		skin: 'cs_beverly',
		posX: -1233.0,
		posY: -1482.91,
		posZ: 4.35,
		posA: 302.21,
		dimension: -1,
		scenario: 'WORLD_HUMAN_WINDOW_SHOP_BROWSE',
	},
	{
		id: 156,
		description: 'Ambient Ped Market 12',
		type: null,
		skin: 'g_m_y_famdnf_01',
		posX: -1256.02,
		posY: -1482.66,
		posZ: 4.33,
		posA: 108.24,
		dimension: -1,
		scenario: 'WORLD_HUMAN_HANG_OUT_STREET',
	},
	{
		id: 157,
		description: 'Ambient Ped Market 13',
		type: null,
		skin: 'a_m_y_eastsa_02',
		posX: -1257.6,
		posY: -1481.89,
		posZ: 4.33,
		posA: 181.58,
		dimension: -1,
		scenario: 'WORLD_HUMAN_HANG_OUT_STREET',
	},
	{
		id: 158,
		description: 'Ambient Ped Market 14',
		dialogId: 78,
		type: null,
		skin: 'a_c_retriever',
		posX: -1236.37,
		posY: -1457.18,
		posZ: 3.67,
		posA: 237.34,
		dimension: -1,
		scenario: 'WORLD_DOG_BARKING_RETRIEVER',
	},
	{
		id: 159,
		description: 'Ambient Ped Market 15',
		dialogId: 89,
		type: null,
		skin: 'a_m_m_tourist_01',
		posX: -1249.19,
		posY: -1469.31,
		posZ: 4.26,
		posA: 101.75,
		dimension: -1,
		scenario: 'WORLD_HUMAN_TOURIST_MAP',
	},
	{
		id: 160,
		description: 'Ambient Ped Market 16',
		type: null,
		skin: 'a_c_cat_01',
		posX: -1261.67,
		posY: -1468.11,
		posZ: 4.0,
		posA: 138.04,
		dimension: -1,
		scenario: 'WORLD_CAT_SLEEPING_GROUND',
	},
	{
		id: 161,
		dialogId: 74,
		description: 'Mushroom Seller',
		type: null,
		skin: 'a_m_o_salton_01',
		posX: -1269.592,
		posY: -1483.402,
		posZ: 4.343618,
		posA: -54.99963,
		dimension: -1,
		animDic: 'rcmme_amanda1',
		animName: 'stand_loop_cop',
		timer: 120000,
		timerId: 4,
		speechName: 'GENERIC_HI',
		voiceName: 'A_M_O_SALTON_01_WHITE_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		snowBalls: true,
		marketId: 1, //+
	},
	{
		id: 162,
		dialogId: 75,
		description: 'Garbage buying',
		type: null,
		skin: 'a_f_y_hipster_02',
		posX: -1242.381,
		posY: -1493.927,
		posZ: 4.35271,
		posA: -45.99974,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_female@stand@01b@idles',
		animName: 'idle_c',
		speechName: 'GENERIC_HI',
		voiceName: 'A_F_Y_HIPSTER_02_WHITE_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		timer: 70000,
		timerId: 9,
		snowBalls: true,
		marketId: 1,
	},
	{
		// Used on Events (Halloween)
		id: 163,
		dialogId: 77,
		description: 'Eva Halloween',
		type: null,
		skin: 'a_f_m_ktown_02',
		posX: -1249.062,
		posY: -1473.634,
		posZ: 4.270159,
		posA: -56.99983,
		dimension: -1,
		animDic: 'timetable@maid@ig_8@',
		animName: 'ig_8_p3_itlooksbroken',
		speechName: 'GENERIC_HI',
		voiceName: 'S_F_Y_COP_01_WHITE_FULL_02',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		blip: { id: 484, color: 5, name: 'blips.halloweenPed', dimension: 0, shortRange: false },
		timer: 45000,
		timerId: 6,
		enabled: false,
	},
	{
		// Used on Events (New Year)
		id: 164,
		dialogId: 81,
		description: 'Eva NewYear',
		type: null,
		skin: 'a_f_o_ktown_01',
		posX: -1249.062,
		posY: -1473.634,
		posZ: 4.270159,
		posA: -56.99983,
		dimension: -1,
		animDic: 'timetable@maid@ig_8@',
		animName: 'ig_8_p3_itlooksbroken',
		speechName: 'GENERIC_HI',
		voiceName: 'S_F_Y_COP_01_WHITE_FULL_02',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		timer: 45000,
		timerId: 7,
		snowBalls: true,
		enabled: false,
		blip: { id: 307, color: 2, name: 'blips.xmasPed', dimension: 0, shortRange: false },
	},
	{
		id: 165,
		dialogId: 80,
		description: 'Mechanical parts seller',
		type: null,
		skin: 'g_m_m_chigoon_01',
		posX: -1265.8,
		posY: -1469.28,
		posZ: 4.329001,
		posA: -123.9986,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_b',
		timer: 60000,
		timerId: 13,
		snowBalls: true,
		marketId: 1, //+
	},
	{
		id: 166,
		dialogId: 79,
		description: 'Food seller',
		type: null,
		skin: 's_m_m_strvend_01',
		posX: -1273.839,
		posY: -1448.111,
		posZ: 4.61416,
		posA: -110.9993,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_a',
		speechName: 'SHOP_GREET',
		voiceName: 'MELVIN',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		snowBalls: true,
		marketId: 1, //+
	},
	{
		id: 167,
		description: 'Unknown Ped Market 17',
		type: null,
		skin: 'a_f_m_ktown_01',
		posX: -1278.82,
		posY: -1441.02,
		posZ: 4.6,
		posA: 306.42,
		dimension: -1,
		animDic: 'anim@heists@ornate_bank@chat_manager',
		animName: 'average_car',
		timer: 80000,
		timerId: 1,
		snowBalls: true,
	},
	{
		id: 168,
		dialogId: 37,
		description: 'Бармен Casino Club',
		type: null,
		skin: 's_m_y_barman_01',
		posX: 1569.51,
		posY: 249.9,
		posZ: -46.0,
		posA: 87.74,
		dimension: 0,
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 169,
		description: 'Бот банка',
		type: null,
		skin: 'mp_f_boatstaff_01',
		posX: 313.77,
		posY: -280.52,
		posZ: 54.16,
		posA: 341.59,
		dimension: 0,
		bankHeistId: 1,
	},
	{
		id: 170,
		description: 'Бот банка',
		type: null,
		skin: 'mp_f_boatstaff_01',
		posX: 149.37,
		posY: -1042.11,
		posZ: 29.36,
		posA: 338.45,
		dimension: 0,
		bankHeistId: 2,
	},
	{
		id: 180,
		description: 'Бот банка',
		type: null,
		skin: 'mp_f_boatstaff_01',
		posX: -1211.91,
		posY: -332.01,
		posZ: 37.78,
		posA: 25.24,
		dimension: 0,
		bankHeistId: 3,
	},
	{
		id: 181,
		description: 'Бот банка',
		type: null,
		skin: 'mp_f_boatstaff_01',
		posX: -2960.94,
		posY: 482.86,
		posZ: 15.69,
		posA: 86.41,
		dimension: 0,
		bankHeistId: 4,
	},
	{
		id: 182,
		description: 'Бот банка',
		type: null,
		skin: 'mp_f_boatstaff_01',
		posX: 1174.9,
		posY: 2708.42,
		posZ: 38.08,
		posA: 179.01,
		dimension: 0,
		bankHeistId: 5,
	},
	{
		id: 183,
		description: 'Бот банка',
		type: null,
		skin: 'mp_f_boatstaff_01',
		posX: -351.41,
		posY: -51.3,
		posZ: 49.03,
		posA: 340.83,
		dimension: 0,
		bankHeistId: 6,
	},
	{
		id: 184,
		description: 'Moodyman DJ Club',
		type: null,
		skin: 'ig_isldj_04',
		posX: 1551.785,
		posY: 249.9202,
		posZ: -48.2297,
		posA: 180,
		dimension: 0,
		animDic: 'anim@amb@nightclub@dancers@crowddance_facedj@',
		animName: 'hi_dance_facedj_15_v1_male^1',
	},
	{
		id: 185,
		dialogId: 36,
		description: 'Бармен Tequi la la ',
		type: null,
		skin: 's_m_y_barman_01',
		posX: -561.79,
		posY: 287.17,
		posZ: 82.17,
		posA: 262.24,
		dimension: 0,
	},
	{
		id: 186,
		dialogId: 82,
		description: 'Ассистент аукциона',
		type: null,
		skin: 'a_f_m_bevhills_01',
		posX: -2301.8770,
        posY: 319.1077,
        posZ: 45.8096,
        posA: 5.6693,
		dimension: -1,
	},
	{
		id: 187,
		dialogId: null,
		description: 'Магазин ирл одежды',
		type: 'clothes_shop',
		skin: 's_f_m_shop_high',
		posX: -721.6,
		posY: -387.13,
		posZ: 34.82,
		posA: 339.52,
		dimension: 0,
	},
	{
		id: 188,
		description: 'Ambient Ped Market 18',
		type: null,
		skin: 's_m_m_strperf_01',
		posX: -1248.28,
		posY: -1482.6,
		posZ: 4.36,
		posA: 326.95,
		dimension: -1,
		scenario: 'WORLD_HUMAN_HUMAN_STATUE',
	},
	{
		id: 189,
		description: 'Church Priest',
		type: null,
		dialogId: 83,
		skin: 'ig_priest',
		posX: -1671.43,
		posY: -271.06,
		posZ: 51.88,
		posA: 240.19,
		dimension: 0,
	},
	{
		id: 190,
		description: 'Church Priest',
		type: null,
		dialogId: 83,
		skin: 'ig_priest',
		posX: -309.19,
		posY: 6164.72,
		posZ: 32.3,
		posA: 232.37,
		dimension: 0,
	},
	{
		id: 191,
		description: 'Church Priest',
		type: null,
		dialogId: 83,
		skin: 'ig_priest',
		posX: -306.27,
		posY: 2801.21,
		posZ: 59.44,
		posA: 153.66,
		dimension: 0,
	},
	{
		id: 192,
		description: 'Ambient Ped Market 19',
		type: null,
		skin: 'a_f_y_skater_01',
		posX: -1262.56,
		posY: -1448.13,
		posZ: 4.37,
		posA: 173.1,
		dimension: -1,
		animDic: 'rcm_barry3',
		animName: 'bar_3_rcm_barry_standing_on_street_fidget',
		timer: 30000,
		timerId: 10,
		snowBalls: true,
	},
	{
		id: 193,
		description: 'Ambient Ped Market 20',
		gpt: {
			name: 'James Raynor',
			description:
				'You will now engage in a conversation while playing the role of {name}, an animator working at the market in Los Santos city. Your purpose is to discuss topics related to your work as an animator and the events taking place at the market. Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions related to your responsibilities as an animator, event planning, and creating engaging experiences for visitors at the market. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 'u_m_y_rsranger_01',
		posX: -1236.12,
		posY: -1486.01,
		posZ: 4.33,
		posA: 82.13,
		dimension: -1,
		timer: 55000,
		timerId: 16,
		snowBalls: true,
		scenario: 'WORLD_HUMAN_SUPERHERO',
	},
	{
		id: 199,
		dialogId: 84,
		description: 'Lumberjack Seller',
		type: null,
		skin: 'a_m_m_salton_01',
		posX: -1292.92,
		posY: -1420.16,
		posZ: 4.36,
		posA: -54.86,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_a',
		timer: 64000,
		timerId: 17,
		snowBalls: true,
		marketId: 1, //+
	},
	{
		id: 200,
		dialogId: 85,
		description: 'Fisherman Seller',
		type: null,
		skin: 's_m_y_chef_01',
		posX: -1231.72,
		posY: -1509.38,
		posZ: 4.35,
		posA: 299.75,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_a',
		marketId: 1, //+
	},
	{
		id: 201,
		dialogId: 86,
		description: 'Mushroomer',
		gpt: {
			name: 'Taylor Milton',
			description:
				'You will now engage in a conversation while playing the role of {name}, a person living in Paleto Bay who is knowledgeable about picking mushrooms in the forest. Your purpose is to discuss topics related to mushroom foraging and share your expertise with others. Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions related to mushroom picking and forest exploration. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 'a_m_m_indian_01',
		posX: -618.17,
		posY: 5674.28,
		posZ: 36.43,
		posA: 241.4,
		dimension: -1,
		animDic: 'rcm_epsilonism4',
		animName: 'eps_4_ig_1_marnie_lookaround_base_marnie',
	},
	{
		id: 202,
		dialogId: 90,
		description: 'Бармен букмейкерки',
		type: null,
		skin: 's_m_y_barman_01',
		posX: 511.18,
		posY: 144.66,
		posZ: 75.67,
		posA: 163.45,
		dimension: -1,
	},
	// {
	//     id: 203,
	//     dialogId: 91,
	//     description: 'Бармен бара в палето',
	//     type: null,
	//     skin: 's_m_y_barman_01',
	//     posX: -305.041,
	//     posY: 6268.61,
	//     posZ: 31.526,
	//     posA: 219.201,
	//     dimension: -1,
	// },
	{
		id: 204,
		dialogId: null,
		description: 'NPC Салона связи',
		// type: 'mobile_shops',
		skin: 's_f_y_shop_mid',
		posX: -753.51,
		posY: 265.46,
		posZ: 77.33,
		posA: 205.26,
		dimension: -1,
	},
	{
		id: 205,
		dialogId: null,
		description: 'NPC Салона связи',
		// type: 'mobile_shops',
		skin: 's_f_y_shop_mid',
		posX: -776.86,
		posY: -618.53,
		posZ: 30.27,
		posA: 0.25,
		dimension: -1,
	},
	{
		id: 206,
		dialogId: null,
		description: 'NPC Салона связи',
		// type: 'mobile_shops',
		skin: 's_f_y_shop_mid',
		posX: -627.62,
		posY: -275.0,
		posZ: 35.57,
		posA: 121.27,
		dimension: -1,
	},
	{
		id: 207,
		description: 'Ambient Ped Market 21',
		type: null,
		skin: 'a_m_m_mexcntry_01',
		posX: -1224.39,
		posY: -1463.33,
		posZ: 4.32,
		posA: 4.42,
		dimension: -1,
		animDic: 'amb@world_human_musician@guitar@male@base',
		animName: 'base',
	},
	{
		id: 208,
		dialogId: 92,
		description: 'Бармен тюрьмы',
		type: null,
		skin: 's_m_m_linecook',
		posX: 1779.57,
		posY: 2591.42,
		posZ: 45.79,
		posA: 181.97,
		dimension: 0,
	},
	{
		id: 209,
		dialogId: 93,
		description: 'Надзиратель',
		type: null,
		skin: 's_m_m_prisguard_01',
		posX: 1782.625,
		posY: 2589.31,
		posZ: 45.798,
		posA: 167.822,
		dimension: 0,
	},
	{
		id: 210,
		dialogId: 85,
		description: 'Fisherman Seller Paleto',
		type: null,
		skin: 's_m_y_chef_01',
		posX: -719.7495,
		posY: 5821.5825,
		posZ: 18.1084,
		posA: 62.36,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_a',
		marketId: 2,
	},
	{
		id: 211,
		dialogId: 74,
		description: 'Mushroom Seller Paleto',
		type: null,
		skin: 'a_m_o_salton_01',
		posX: -720.4615,
		posY: 5828.4263,
		posZ: 18.1084,
		posA: 116.2205,
		dimension: -1,
		animDic: 'rcmme_amanda1',
		animName: 'stand_loop_cop',
		timer: 120000,
		timerId: 4,
		speechName: 'GENERIC_HI',
		voiceName: 'A_M_O_SALTON_01_WHITE_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		snowBalls: true,
		marketId: 2,
	},
	{
		id: 212,
		dialogId: 75,
		description: 'Garbage Buying Paleto',
		type: null,
		skin: 'a_f_y_hipster_02',
		posX: -723.2703,
		posY: 5831.1694,
		posZ: 18.0916,
		posA: 147.4016,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_female@stand@01b@idles',
		animName: 'idle_c',
		speechName: 'GENERIC_HI',
		voiceName: 'A_F_Y_HIPSTER_02_WHITE_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		timer: 70000,
		timerId: 9,
		snowBalls: true,
		marketId: 2,
	},
	{
		id: 213,
		dialogId: 80,
		description: 'Mechanical Parts Seller Paleto',
		type: null,
		skin: 'g_m_m_chigoon_01',
		posX: -733.5428,
		posY: 5835.4551,
		posZ: 18.0916,
		posA: 161.5748,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_b',
		timer: 60000,
		timerId: 13,
		snowBalls: true,
		marketId: 2,
	},
	{
		id: 214,
		dialogId: 79,
		description: 'Food seller',
		type: null,
		skin: 's_m_m_strvend_01',
		posX: -739.0549,
		posY: 5825.7759,
		posZ: 18.4454,
		posA: -113.3858,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_a',
		speechName: 'SHOP_GREET',
		voiceName: 'MELVIN',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		snowBalls: true,
		marketId: 2,
	},
	{
		id: 215,
		dialogId: 71,
		description: 'Diamond Seller Paleto',
		type: null,
		skin: 'a_m_o_ktown_01',
		posX: -745.6879,
		posY: 5819.0508,
		posZ: 18.0916,
		posA: -121.8898,
		dimension: -1,
		animDic: 'rcmme_amanda1',
		animName: 'stand_loop_cop',
		timer: 100000,
		timerId: 3,
		snowBalls: true,
		marketId: 2,
	},
	{
		id: 216,
		dialogId: 70,
		description: 'Оружейник Paleto',
		type: null,
		skin: 'csb_chin_goon',
		posX: -747.5341,
		posY: 5811.4419,
		posZ: 18.0916,
		posA: -113.3858,
		dimension: -1,
		animDic: 'missfbi3_party_d',
		animName: 'stand_talk_loop_b_male1',
		timer: 101000,
		timerId: 2,
		snowBalls: true,
		marketId: 2,
	},
	{
		id: 217,
		dialogId: 72,
		description: 'Clothes Seller Paleto',
		type: null,
		skin: 'a_m_m_eastsa_02',
		posX: -750.3033,
		posY: 5805.7188,
		posZ: 18.0916,
		posA: -121.8898,
		dimension: -1,
		animDic: 'anim@heists@ornate_bank@chat_manager',
		animName: 'poor_clothes',
		timer: 100000,
		timerId: 14,
		snowBalls: true,
		marketId: 2,
	},
	{
		id: 218,
		dialogId: 73,
		description: 'Fireworks Seller Paleto',
		type: null,
		skin: 'ig_kerrymcintosh',
		posX: -738.6198,
		posY: 5795.8154,
		posZ: 18.0916,
		posA: 56.6929,
		dimension: -1,
		animDic: 'rcmme_amanda1',
		animName: 'stand_loop_cop',
		speechName: 'GENERIC_HI',
		voiceName: 'A_F_M_KTOWN_02_CHINESE_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		timer: 84000,
		timerId: 15,
		snowBalls: true,
		marketId: 2,
	},
	{
		id: 219,
		dialogId: 84,
		description: 'Lumberjack Seller Paleto',
		type: null,
		skin: 'a_m_m_salton_01',
		posX: -732.9758,
		posY: 5806.6416,
		posZ: 18.0579,
		posA: 62.3622,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_a',
		timer: 64000,
		timerId: 17,
		snowBalls: true,
		marketId: 2,
	},
	{
		id: 220,
		description: 'Ambient Ped Market Paleto 1',
		type: null,
		skin: 'a_c_cat_01',
		posX: -730.8527,
		posY: 5817.165,
		posZ: 17.03,
		posA: 141.7323,
		dimension: -1,
		animDic: 'creatures@cat@amb@world_cat_sleeping_ledge@idle_a',
		animName: 'idle_a',
	},
	{
		id: 221,
		description: 'Ambient Ped Market Paleto 2',
		type: null,
		skin: 'a_m_y_eastsa_02',
		posX: -742.233,
		posY: 5810.4131,
		posZ: 18.0916,
		posA: -56.6929,
		dimension: -1,
		scenario: 'WORLD_HUMAN_DRINKING',
	},
	{
		id: 222,
		description: 'Ambient Ped Market Paleto 3',
		type: null,
		skin: 'a_f_y_eastsa_03',
		posX: -742.2198,
		posY: 5811.0728,
		posZ: 18.0916,
		posA: -87.874,
		dimension: -1,
		timer: 30000,
		timerId: 10,
		snowBalls: true,
		scenario: 'WORLD_HUMAN_CHEERING',
	},
	{
		id: 223,
		description: 'Ambient Ped Market Paleto 4',
		type: null,
		skin: 'a_m_y_indian_01',
		posX: -741.4681,
		posY: 5797.6353,
		posZ: 18.0916,
		posA: -59.5276,
		dimension: -1,
		scenario: 'WORLD_HUMAN_WINDOW_SHOP_BROWSE',
	},
	{
		id: 224,
		description: 'Ambient Ped Market Paleto 5',
		type: null,
		skin: 'a_c_shepherd',
		posX: -727.6219,
		posY: 5824.1143,
		posZ: 17.6024,
		posA: -117.0473,
		dimension: -1,
		scenario: 'WORLD_DOG_BARKING_SHEPHERD',
	},
	{
		id: 225,
		description: 'Ambient Ped Market Paleto 6',
		type: null,
		skin: 'player_zero',
		posX: -726.0527,
		posY: 5823.7715,
		posZ: 18.1416,
		posA: 62.3622,
		dimension: -1,
		animDic: 'switch@franklin@plays_w_dog',
		animName: '001916_01_fras_v2_9_plays_w_dog_idle',
	},
	{
		id: 226,
		description: 'Ambient Ped Market Paleto 7',
		type: null,
		skin: 'a_m_m_skater_01',
		posX: -726.9363,
		posY: 5809.2925,
		posZ: 18.1784,
		posA: -55.0236,
		dimension: -1,
		animDic: 'timetable@reunited@ig_10',
		animName: 'isthisthebest_amanda',
	},
	{
		id: 227,
		description: 'Ambient Ped Market Paleto 8',
		type: null,
		skin: 'a_m_y_skater_01',
		posX: -725.6835,
		posY: 5808.6987,
		posZ: 18.1252,
		posA: 81.0236,
		dimension: -1,
		scenario: 'WORLD_HUMAN_TOURIST_MAP',
	},
	{
		id: 228,
		description: 'World Quests',
		type: null,
		dialogId: 94,
		skin: 'u_m_y_baygor',
		posX: -1277.482,
		posY: -1411.837,
		posZ: 4.333124,
		posA: 118.441,
		dimension: -1,
		timer: 120000,
		timerId: 18,
		snowBalls: true,
	},
	{
		id: 229,
		dialogId: 95,
		description: 'Бармен авторынка',
		type: null,
		skin: 's_m_m_linecook',
		posX: -1576.91,
		posY: -888.93,
		posZ: 10.31,
		posA: 128.07,
		dimension: 0,
	},
	{
		id: 230,
		description: 'World Quests Mirror Park',
		type: null,
		dialogId: 94,
		skin: 'u_m_y_baygor',
		posX: 1146.446,
		posY: -650.272,
		posZ: 56.92,
		posA: 0.0,
		dimension: 0,
		timer: 120000,
		timerId: 18,
		snowBalls: true,
	},
	{
		id: 231,
		description: 'World Quests Sandy Shores',
		type: null,
		dialogId: 94,
		skin: 'u_m_y_baygor',
		posX: 1962.677,
		posY: 3698.8,
		posZ: 32.35043,
		posA: -31.10276,
		dimension: 0,
		timer: 120000,
		timerId: 18,
		snowBalls: true,
	},
	// {
	//     id: 232,
	//     description: 'Blitz Контрабанда',
	//     type: null,
	//     dialogId: 96,
	//     skin: 'ig_juanstrickler',
	//     posX: -2165.7187,
	//     posY: 5197.3686,
	//     posZ: 16.8803,
	//     posA: 97.5945,
	//     dimension: 0,
	// },
	// {
	//     id: 233,
	//     description: 'Proxy Monkey',
	//     type: null,
	//     dialogId: 97,
	//     skin: 'u_m_m_streetart_01',
	//     posX: 244.6552,
	//     posY: 374.3814,
	//     posZ: 105.7381,
	//     posA: 162.4304,
	//     dimension: 0,
	// },
	{
		id: 234,
		dialogId: 98,
		description: 'Помощник на спавне',
		gpt: {
			name: 'Jung Lee',
			description:
				'You will now engage in a conversation while playing the role of {name}, the guide for beginners in the game GTA 5 (Majestic RP) in Los Santos. Your purpose is to help newbies get started playing Majestic RP (GTA 5 RP server). Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions for beginners so they can start playing. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 'csb_agent',
		posX: 1539.18,
		posY: 3767.2,
		posZ: 34.05,
		posA: 58.46,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechName: 'GENERIC_HI',
		voiceName: 'STRETCH',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 235,
		dialogId: 99,
		description: 'Менеджер шахты',
		type: null,
		skin: 's_m_m_dockwork_01',
		posX: 2951.98,
		posY: 2752.78,
		posZ: 43.41,
		posA: 224.52,
		dimension: 0,
	},
	{
		id: 236,
		dialogId: 100,
		description: 'Помощник на спавне',
		gpt: {
			name: 'Jung Lee',
			description:
				'You will now engage in a conversation while playing the role of {name}, the guide for beginners in the game GTA 5 (Majestic RP) in Los Santos. Your purpose is to help newbies get started playing Majestic RP (GTA 5 RP server). Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions for beginners so they can start playing. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 'csb_agent',
		posX: -833.83,
		posY: -1217.98,
		posZ: 6.92,
		posA: 264.83,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechName: 'GENERIC_HI',
		voiceName: 'STRETCH',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 237,
		dialogId: 101,
		description: 'Аренда авто',
		type: null,
		skin: 'csb_agent',
		posX: 1236.9967,
		posY: -332.6132,
		posZ: 69.0819,
		posA: 165.8373,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
	},
	{
		id: 238,
		dialogId: 102,
		description: 'Аренда авто',
		type: null,
		skin: 'csb_agent',
		posX: -171.36,
		posY: -1039.6,
		posZ: 27.33,
		posA: -0.99,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
	},
	{
		id: 239,
		description: 'Бот банка',
		type: null,
		skin: 'mp_f_boatstaff_01',
		posX: -111.19,
		posY: 6470.09,
		posZ: 31.62,
		posA: 134.29,
		dimension: 0,
	},
	{
		id: 240,
		description: 'Бот банка',
		type: null,
		skin: 'mp_f_boatstaff_01',
		posX: 248.34,
		posY: 224.61,
		posZ: 106.28,
		posA: -178.68,
		dimension: 0,
	},
	{
		id: 241,
		dialogId: 103,
		description: 'Аренда авто',
		type: null,
		skin: 'csb_agent',
		posX: 2473.3267,
		posY: 4614.4766,
		posZ: 36.4581,
		posA: 6.76,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
	},
	{
		id: 242,
		dialogId: null,
		description: 'Пожарный',
		type: 'firefighter',
		skin: 's_m_y_fireman_01',
		posX: 1207.6615,
		posY: -1487.2748,
		posZ: 34.8403,
		posA: 147.4016,
		dimension: 0,
	},
	{
		// Used on Events (Halloween 2024)
		id: 243,
		dialogId: 109,
		description: 'Search graffiti task',
		type: null,
		skin: 'u_f_y_corpse_01',
		posX: 276.0791,
		posY: 4345.8726,
		posZ: 47.5619,
		posA: 164.4095,
		dimension: 0,
		blip: { id: 484, color: 5, name: 'blips.halloweenPed5', dimension: 0, shortRange: true },
		animDic: 'special_ped@zombie@monologue_1@monologue_1g',
		animName: 'iamundead_6',
		enabled: false,
	},
	{
		// Used on Events (Halloween 2024)
		id: 244,
		dialogId: 108,
		description: 'Mining task',
		type: null,
		skin: 'a_c_coyote_02',
		posX: -600.6857,
		posY: 2091.4946,
		posZ: 130.918,
		posA: 0,
		dimension: 0,
		blip: { id: 484, color: 5, name: 'blips.halloweenPed3', dimension: 0, shortRange: true },
		animDic: 'creatures@coyote@amb@world_coyote_howl@idle_a',
		animName: 'idle_b',
		enabled: false,
	},
	{
		// Used on Events (Halloween)
		id: 245,
		dialogId: 164,
		description: 'Jimmy Boston',
		type: null,
		skin: 'u_m_o_finguru_01',
		mask: 'mj_hw_mask_2',
		posX: -1576.709,
		posY: 2100.906,
		posZ: 68.355,
		posA: -35.752,
		dimension: 0,
		blip: { id: 484, color: 5, name: 'blips.halloweenPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		// Used on Events (Halloween 2024)
		id: 246,
		dialogId: 107,
		description: 'Newspaper delivery task',
		type: null,
		skin: 's_m_m_doctor_01',
		posX: 246.3956,
		posY: -1387.7539,
		posZ: 30.712,
		posA: 178.5827,
		dimension: 0,
		blip: { id: 484, color: 5, name: 'blips.halloweenPed8', dimension: 0, shortRange: true },
		scenario: 'WORLD_HUMAN_AA_SMOKE',
		enabled: false,
	},
	{
		// Used on Events (Halloween)
		id: 247,
		dialogId: 106,
		description: 'Ann Fitzroy',
		type: null,
		skin: 'a_m_m_tranvest_02',
		mask: 'mj_hw_mask_2',
		posX: 275.871,
		posY: 4348.772,
		posZ: 47.707,
		posA: 149.003,
		dimension: 0,
		blip: { id: 484, color: 5, name: 'blips.halloweenPed', dimension: 0, shortRange: false },
		enabled: false,
	},

	{
		// Used on Events (Halloween)
		id: 248,
		dialogId: 111,
		description: 'Baygor Earthsong',
		type: null,
		skin: 'a_m_o_acult_02',
		mask: 'mj_hw_mask_2',
		posX: -1135.981,
		posY: 4660.238,
		posZ: 243.902,
		posA: -37.253,
		dimension: 0,
		blip: { id: 484, color: 5, name: 'blips.halloweenPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		// Used on Events (Halloween)
		id: 249,
		dialogId: 163,
		description: 'John Doe',
		type: null,
		skin: 's_m_m_mariachi_01',
		mask: 'mj_hw_mask_1',
		posX: -598.7607,
		posY: 2100.1345,
		posZ: 130.1275,
		posA: 61.0719,
		dimension: 0,
		blip: { id: 484, color: 5, name: 'blips.halloweenPed', dimension: 0, shortRange: false, isQuestNotCompleted: 34 },
		enabled: false,
	},
	{
		// Used on Events (Halloween)
		id: 250,
		dialogId: 112,
		description: 'Cat of Eva',
		type: null,
		skin: 'a_c_cat_01',
		posX: -252.71,
		posY: 6340.93,
		posZ: 36.799,
		posA: -150.3,
		dimension: 0,
		scenario: 'WORLD_CAT_SLEEPING_GROUND',
	},
	{
		// Used on Events (Halloween)
		id: 251,
		description: 'Crow of Julia',
		type: null,
		skin: 'a_c_crow',
		posX: 2558.87769,
		posY: 6157.28027,
		posZ: 161.738754,
		posA: -75.8105698,
		dimension: 0,
		animDic: 'creatures@crow@amb@world_crow_feeding@base',
		animName: 'base',
		enabled: false,
	},
	{
		// Used on Events (Halloween)
		id: 252,
		description: 'Crow of Julia',
		type: null,
		skin: 'a_c_crow',
		posX: 2560.28271,
		posY: 6156.00488,
		posZ: 161.712936,
		posA: -4.44030809,
		dimension: 0,
		animDic: 'creatures@crow@amb@world_crow_feeding@idle_a',
		animName: 'idle_b',
		enabled: false,
	},
	{
		// Used on Events (Halloween)
		id: 253,
		description: 'Crow of Muna',
		type: null,
		skin: 'a_c_crow',
		posX: -1906.22363,
		posY: 1390.38477,
		posZ: 220.129578,
		posA: -119.27977,
		dimension: 0,
		animDic: 'creatures@crow@amb@world_crow_feeding@idle_a',
		animName: 'idle_b',
		enabled: false,
	},
	{
		id: 254,
		dialogId: 85,
		description: 'Fisherman Seller Sandy',
		type: null,
		skin: 's_m_y_chef_01',
		posX: 1801.01,
		posY: 4592.46,
		posZ: 37.68,
		posA: 101.85,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_a',
		marketId: 3,
	},
	{
		id: 255,
		dialogId: 74,
		description: 'Mushroom Seller Sandy',
		type: null,
		skin: 'a_m_o_salton_01',
		posX: 1800.7,
		posY: 4597.06,
		posZ: 37.68,
		posA: 112.55,
		dimension: -1,
		animDic: 'rcmme_amanda1',
		animName: 'stand_loop_cop',
		timer: 120000,
		timerId: 4,
		speechName: 'GENERIC_HI',
		voiceName: 'A_M_O_SALTON_01_WHITE_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		snowBalls: true,
		marketId: 3,
	},
	{
		id: 256,
		dialogId: 75,
		description: 'Garbage Buying Sandy',
		type: null,
		skin: 'a_f_y_hipster_02',
		posX: 1803.51,
		posY: 4610.9,
		posZ: 37.68,
		posA: 170.98,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_female@stand@01b@idles',
		animName: 'idle_c',
		speechName: 'GENERIC_HI',
		voiceName: 'A_F_Y_HIPSTER_02_WHITE_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		timer: 70000,
		timerId: 9,
		snowBalls: true,
		marketId: 3,
	},
	{
		id: 257,
		dialogId: 80,
		description: 'Mechanical Parts Seller Sandy',
		type: null,
		skin: 'g_m_m_chigoon_01',
		posX: 1790.37,
		posY: 4610.08,
		posZ: 37.68,
		posA: -170.39,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_b',
		timer: 60000,
		timerId: 13,
		snowBalls: true,
		marketId: 3,
	},
	{
		id: 258,
		dialogId: 79,
		description: 'Food seller Sandy',
		type: null,
		skin: 's_m_m_strvend_01',
		posX: 1792.57,
		posY: 4588.11,
		posZ: 37.73,
		posA: -170.54,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_a',
		speechName: 'SHOP_GREET',
		voiceName: 'MELVIN',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		snowBalls: true,
	},
	{
		id: 259,
		dialogId: 71,
		description: 'Diamond Seller Sandy',
		type: null,
		skin: 'a_m_o_ktown_01',
		posX: 1781.31,
		posY: 4608.0,
		posZ: 37.68,
		posA: -126.34,
		dimension: -1,
		animDic: 'rcmme_amanda1',
		animName: 'stand_loop_cop',
		timer: 100000,
		timerId: 3,
		snowBalls: true,
		marketId: 3,
	},
	{
		id: 260,
		dialogId: 70,
		description: 'Оружейник Sandy',
		type: null,
		skin: 'csb_chin_goon',
		posX: 1783.63,
		posY: 4594.59,
		posZ: 37.68,
		posA: -81.22,
		dimension: -1,
		animDic: 'missfbi3_party_d',
		animName: 'stand_talk_loop_b_male1',
		timer: 101000,
		timerId: 2,
		snowBalls: true,
		marketId: 3,
	},
	{
		id: 261,
		dialogId: 72,
		description: 'Clothes Seller Sandy',
		type: null,
		skin: 'a_m_m_eastsa_02',
		posX: 1783.73,
		posY: 4591.79,
		posZ: 37.68,
		posA: -94.78,
		dimension: -1,
		animDic: 'anim@heists@ornate_bank@chat_manager',
		animName: 'poor_clothes',
		timer: 100000,
		timerId: 14,
		snowBalls: true,
		marketId: 3,
	},
	{
		id: 262,
		dialogId: 73,
		description: 'Fireworks Seller Sandy',
		type: null,
		skin: 'ig_kerrymcintosh',
		posX: 1790.97,
		posY: 4592.3,
		posZ: 37.68,
		posA: 0.12,
		dimension: -1,
		animDic: 'rcmme_amanda1',
		animName: 'stand_loop_cop',
		speechName: 'GENERIC_HI',
		voiceName: 'A_F_M_KTOWN_02_CHINESE_MINI_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		timer: 84000,
		timerId: 15,
		snowBalls: true,
		marketId: 3,
	},
	{
		id: 263,
		dialogId: 84,
		description: 'Lumberjack Seller Sandy',
		type: null,
		skin: 'a_m_m_salton_01',
		posX: 1794.39,
		posY: 4610.32,
		posZ: 37.68,
		posA: -168.14,
		dimension: -1,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_a',
		timer: 64000,
		timerId: 17,
		snowBalls: true,
		marketId: 3,
	},
	{
		id: 264,
		dialogId: 113,
		description: 'Farmer Seller Sandy',
		type: null,
		skin: 'a_m_m_farmer_01',
		posX: 1808.0,
		posY: 4603.59,
		posZ: 37.73,
		posA: 93.89,
		dimension: -1,
		scenario: 'WORLD_HUMAN_SMOKING_POT',
		timer: 45000,
		timerId: 8,
		snowBalls: true,
	},
	{
		id: 265,
		dialogId: 87,
		description: 'Ambient Ped Market Sandy',
		type: null,
		skin: 'cs_jewelass',
		posX: 1799.65,
		posY: 4606.04,
		posZ: 37.68,
		posA: 134.93,
		dimension: -1,
		scenario: 'WORLD_HUMAN_STAND_MOBILE_UPRIGHT',
		timer: 80000,
		timerId: 5,
	},
	{
		id: 266,
		// dialogId: 87,
		description: 'Ambient Ped Market 2 Sandy',
		type: null,
		skin: 'cs_josh',
		posX: 1798.6,
		posY: 4605.14,
		posZ: 37.68,
		posA: -49.96,
		dimension: -1,
		scenario: 'WORLD_HUMAN_DRINKING_CASINO_TERRACE',
		timer: 30000,
		timerId: 10,
	},
	{
		id: 267,
		description: 'Ambient Ped Market 3 Sandy',
		gpt: {
			name: 'Louis Anderson',
			description:
				'You will now engage in a conversation while playing the role of {name}, a market shopper living in a suburb of Sandy Shores who is experiencing financial difficulties. Your purpose is to discuss topics related to your experiences as a shopper, budgeting, and strategies for managing expenses. Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions related to budget shopping, financial challenges, and tips for making the most out of limited resources. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 'a_m_y_indian_01',
		posX: 1794.01,
		posY: 4596.02,
		posZ: 37.68,
		posA: 120.29,
		dimension: -1,
		scenario: 'WORLD_HUMAN_WINDOW_SHOP_BROWSE',
	},
	{
		id: 268,
		description: 'Ambient Ped Market 4 Sandy',
		type: null,
		skin: 'a2nier',
		posX: 1789.1,
		posY: 4606.13,
		posZ: 37.68,
		posA: -142.88,
		dimension: -1,
		animDic: 'anim@amb@casino@mini@dance@dance_solo@female@var_a@',
		animName: 'med_center',
	},
	{
		id: 269,
		dialogId: 113,
		description: 'Farmer Seller Paleto',
		type: null,
		skin: 'a_m_m_farmer_01',
		posX: -739.5428,
		posY: 5791.1343,
		posZ: 18.0916,
		posA: 59.5276,
		dimension: -1,
		scenario: 'WORLD_HUMAN_SMOKING_POT',
		timer: 46000,
		timerId: 8,
		snowBalls: true,
	},
	{
		id: 270,
		dialogId: 113,
		description: 'Farmer Seller LS',
		type: null,
		skin: 'a_m_m_farmer_01',
		posX: -1281.64,
		posY: -1437.97,
		posZ: 4.58,
		posA: -54.89,
		dimension: -1,
		scenario: 'WORLD_HUMAN_SMOKING_POT',
		timer: 49000,
		timerId: 8,
		snowBalls: true,
	},
	/* {
		id: 271,
		dialogId: 114,
		description: 'Quest Canceller',
		type: null,
		skin: 's_m_o_busker_01',
		posX: 1790.4536,
		posY: 4599.3056,
		posZ: 37.6827,
		posA: 139.4125,
		dimension: -1,
		scenario: 'WORLD_HUMAN_SMOKING_POT',
	},
	{
		id: 272,
		dialogId: 114,
		description: 'Quest Canceller',
		type: null,
		skin: 's_m_o_busker_01',
		posX: -1250.4440,
		posY: -1478.7061,
		posZ: 4.35665,
		posA: -64.0505,
		dimension: -1,
		scenario: 'WORLD_HUMAN_SMOKING_POT',
	},
	{
		id: 273,
		dialogId: 114,
		description: 'Quest Canceller',
		type: null,
		skin: 's_m_o_busker_01',
		posX: -288.4160,
		posY: 6126.8486,
		posZ: 31.4931,
		posA: -177.1065,
		dimension: -1,
		scenario: 'WORLD_HUMAN_SMOKING_POT',
	}, */
	{
		id: 274,
		dialogId: 115,
		description: 'Casino Chips Trader',
		type: null,
		skin: 's_f_y_casino_01',
		posX: 1117.24,
		posY: 220.02,
		posZ: -49.42,
		posA: 89.44,
		dimension: -1,
		animDic: 'anim_casino_b@amb@casino@games@roulette@dealer_female',
		animName: 'idle',
	},
	// Winter event
	{
		id: 275,
		dialogId: 118,
		description: 'Zoe',
		type: null,
		skin: 'ig_mrs_thornhill',
		posX: 3244.714,
		posY: -4671.385,
		posZ: 114.212,
		posA: 136.072,
		dimension: -1,
		enabled: false,
	},
	// {
	//     id: 276,
	//     dialogId: 116,
	//     description: 'Liam Xmas',
	//     type: null,
	//     skin: 'ohrana',
	//     posX: 5291.092,
	//     posY: -5188.414,
	//     posZ: 82.835,
	//     posA: 72,
	//     dimension: -1,
	//     scenario: 'WORLD_HUMAN_SMOKING_POT',
	//     enabled: false
	// },
	// Winter event
	{
		id: 277,
		dialogId: 117,
		description: 'Avari',
		type: null,
		skin: 'elfthird',
		posX: 3218.071,
		posY: -4905.389,
		posZ: 113.051,
		posA: 37.895,
		dimension: -1,
		scenario: 'WORLD_HUMAN_GUARD_STAND',
		enabled: false,
	},
	// {
	//     id: 278,
	//     dialogId: 119,
	//     description: 'Batty Xmas',
	//     type: null,
	//     skin: 'a_f_m_prolhost_01',
	//     posX: 3220.493,
	//     posY: -4590.912,
	//     posZ: 117.276,
	//     posA: -126.00,
	//     dimension: -1,
	//     animDic: 'rcmme_amanda1',
	//     animName: 'stand_loop_cop',
	//     enabled: false
	// },
	// Winter event
	{
		id: 279,
		dialogId: 120,
		description: 'Barbara',
		type: null,
		skin: 'u_f_m_promourn_01',
		posX: 3211.958,
		posY: -4584.966,
		posZ: 117.654,
		posA: 141.859,
		dimension: -1,
		enabled: false,
	},
	{
		id: 280,
		dialogId: 121,
		description: 'Ernest Xmas',
		type: null,
		skin: 'chelthree',
		posX: -475.195,
		posY: 4920.027,
		posZ: 146.901,
		posA: 146.0,
		dimension: -1,
		animDic: 'amb@world_human_bum_standing@twitchy@base',
		animName: 'base',
		enabled: false,
	},
	// Winter event
	{
		id: 281,
		dialogId: 122,
		description: 'Henry',
		type: null,
		skin: 'ig_brad',
		posX: 3329.038,
		posY: -4898.302,
		posZ: 111.518,
		posA: -88.406,
		dimension: -1,
		enabled: false,
	},
	// {
	//     id: 282,
	//     dialogId: 123,
	//     description: 'Jodie Xmas',
	//     type: null,
	//     skin: 'ig_mrs_thornhill',
	//     posX: 3556.81,
	//     posY: -4688.49,
	//     posZ: 115.04 - 0.5,
	//     posA: 70,
	//     dimension: -1,
	//     animDic: 'random@robbery',
	//     animName: 'sit_down_idle_01',
	//     enabled: false
	// },
	// Winter event
	{
		id: 283,
		dialogId: 124,
		description: 'Krica',
		type: null,
		skin: 'a_f_m_prolhost_01',
		posX: 3188.216,
		posY: -4772.621,
		posZ: 112.169,
		posA: -104.243,
		dimension: -1,
		enabled: false,
	},
	// {
	//     id: 284,
	//     dialogId: 125,
	//     description: 'Jenny Xmas',
	//     type: null,
	//     skin: 'u_f_m_promourn_01',
	//     posX: 3327.225,
	//     posY: -4904.618,
	//     posZ: 111.389,
	//     posA: -103,
	//     dimension: -1,
	//     animDic: 'amb@world_human_clipboard@male@idle_b',
	//     animName: 'idle_d',
	//     enabled: false
	// },
	// Winter event
	{
		id: 285,
		dialogId: 126,
		description: 'Walter',
		type: null,
		skin: 'u_m_m_promourn_01',
		posX: 3239.473,
		posY: -4742.542,
		posZ: 114.141,
		posA: 157.93,
		dimension: -1,
		enabled: false,
	},
	{
		id: 286,
		dialogId: 127,
		description: 'Douglas Xmas',
		type: null,
		skin: 'ig_hunter',
		posX: 4472.178,
		posY: -5042.085,
		posZ: 112.424,
		posA: 3.5,
		dimension: -1,
		enabled: false,
	},
	{
		id: 287,
		dialogId: 128,
		description: 'Аренда авто Yankton',
		type: null,
		skin: 'csb_agent',
		posX: 5465.6309,
		posY: -5116.0747,
		posZ: 78.3971,
		posA: -102.0,
		dimension: -1,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		enabled: false,
		blip: { id: 811, color: 4, name: 'blips.carRent', dimension: -1, shortRange: false },
	},
	{
		id: 288,
		dialogId: 129,
		description: 'Horace',
		type: null,
		skin: 'ig_mp_agent14',
		posX: 5471.747,
		posY: -5202.097,
		posZ: 79.035,
		posA: -17.7703,
		dimension: -1,
		enabled: false,
	},
	// {
	//     id: 289,
	//     dialogId: 130,
	//     description: 'Travis Xmas',
	//     type: null,
	//     skin: 'u_m_m_edtoh',
	//     posX: 3147.84,
	//     posY: -4823.72,
	//     posZ: 111.81,
	//     posA: -105.46,
	//     dimension: -1,
	//     enabled: false
	// },
	{
		id: 290,
		dialogId: 131,
		description: 'Аренда авто Yankton',
		type: null,
		skin: 'csb_agent',
		posX: 3190.23,
		posY: -4842.74,
		posZ: 111.86,
		posA: -94.89,
		dimension: -1,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		enabled: false,
		blip: { id: 811, color: 4, name: 'blips.carRent', dimension: -1, shortRange: false },
	},
	{
		id: 291,
		dialogId: 132,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: 297.9067,
		posY: -2534.3762,
		posZ: 5.8473,
		posA: -148.9113,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 292,
		dialogId: 133,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: -2190.0432,
		posY: -391.5504,
		posZ: 13.4667,
		posA: -51.4626,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 293,
		dialogId: 134,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: -1133.9919,
		posY: 2680.8132,
		posZ: 18.3432,
		posA: 154.6303,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 294,
		dialogId: 135,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: 584.9794,
		posY: 2719.4458,
		posZ: 42.0602,
		posA: 4.3549,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 295,
		dialogId: 136,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: -2201.8779,
		posY: 4275.1733,
		posZ: 48.4595,
		posA: 141.2574,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 296,
		dialogId: 137,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: 2553.5735,
		posY: 2613.0928,
		posZ: 37.9552,
		posA: -6.4224,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 297,
		dialogId: 138,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: 2578.772,
		posY: 463.3491,
		posZ: 108.6071,
		posA: -174.0412,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 298,
		dialogId: 139,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: 2671.0642,
		posY: 1598.744,
		posZ: 24.5007,
		posA: -77.1369,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 299,
		dialogId: 140,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: -1506.812,
		posY: 1505.0989,
		posZ: 115.2887,
		posA: -100.4448,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 300,
		dialogId: 141,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: 793.7261,
		posY: -3020.3835,
		posZ: 6.0209,
		posA: -134.8624,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 301,
		dialogId: 142,
		description: 'Армейский повар',
		type: null,
		skin: 's_m_m_linecook',
		posX: -2293.632,
		posY: 3237.912,
		posZ: 36.507,
		posA: 59.897,
		dimension: 0,
	},
	{
		id: 302,
		description: 'Chat GPT Beta npc',
		gpt: {
			name: 'Anthony Smith',
			description:
				'You will now engage in a conversation while playing the role of {name}, a resident of Los Santos who is currently wandering around the Los Santos Market. Your purpose is to discuss topics related to your experiences and observations at the market. Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions related to market atmosphere, products, vendors, and any interesting events or encounters you experience while at the Los Santos Market. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 'a_m_m_soucent_03',
		posX: -1233.54,
		posY: -1468.42,
		posZ: 4.3,
		posA: -12.29,
		dimension: 0,
		animDic: 'random@street_race',
		animName: '_car_b_chatting_male',
	},
	{
		id: 303,
		description: 'Учитель Университета CHAT GPT',
		// gpt: {
		//     name: 'Samantha Smith',
		//     description: 'You will now engage in a conversation while playing the role of {name}, a teacher who is currently at a university. Your purpose is to discuss topics related to school, college, or university and provide help with homework-related questions. Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions related to academic subjects, teaching methods, and offering guidance on homework assignments or projects. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).'
		// },
		dialogId: 206,
		type: null,
		skin: 'csb_anita',
		posX: -1656.421,
		posY: 164.129,
		posZ: 61.759,
		posA: -37.471,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
	},
	{
		id: 304,
		dialogId: 143,
		description: 'Maria LaGuerta',
		type: null,
		skin: 'u_f_y_jewelass_01',
		posX: -138.474,
		posY: -633.884,
		posZ: 168.82,
		posA: 6.463,
		dimension: -1,
	},
	{
		id: 305,
		dialogId: 144,
		description: 'Jamie Batista',
		type: null,
		skin: 'u_f_y_jewelass_01',
		posX: -72.562,
		posY: -814.2,
		posZ: 243.386,
		posA: 155.632,
		dimension: -1,
	},
	{
		id: 306,
		dialogId: 145,
		description: 'Rebecca Harris',
		type: null,
		skin: 'u_f_y_jewelass_01',
		posX: -1570.672,
		posY: -574.7,
		posZ: 108.522,
		posA: 31.599,
		dimension: -1,
	},
	{
		id: 307,
		dialogId: 146,
		description: 'Maggie Hart',
		type: null,
		skin: 'u_f_y_jewelass_01',
		posX: -1379.814,
		posY: -476.932,
		posZ: 72.042,
		posA: 95.907,
		dimension: -1,
	},
	{
		id: 308,
		dialogId: 147,
		description: 'Начальник тюрьмы',
		type: null,
		skin: 's_m_m_armoured_02',
		posX: 1836.8,
		posY: 2590.605,
		posZ: 45.924,
		posA: -94.732,
		dimension: 0,
		// scenario: 'WORLD_HUMAN_CLIPBOARD'
		animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		animName: 'transition_sleep_lazyworker',
	},
	{
		id: 309,
		dialogId: 149,
		description: 'Бот для сдачи контракта "поддельная доверенность"',
		type: null,
		skin: 'cs_drfriedlander',
		posX: 251.869,
		posY: -1076.685,
		posZ: 29.294,
		posA: -178.636,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 310,
		dialogId: 150,
		description: 'Бот для сдачи контракта "поддельная доверенность"',
		type: null,
		skin: 's_m_y_doorman_01',
		posX: 263.39,
		posY: -995.65,
		posZ: -99,
		posA: -150,
		dimension: -1,
		houseInteriorId: 3,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 311,
		dialogId: 150,
		description: 'Бот для сдачи контракта "поддельная доверенность"',
		type: null,
		skin: 's_m_y_doorman_01',
		posX: -14.77,
		posY: -1427.6,
		posZ: 31.1,
		posA: -147,
		dimension: -1,
		houseInteriorId: 4,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 312,
		dialogId: 150,
		description: 'Бот для сдачи контракта "поддельная доверенность"',
		type: null,
		skin: 's_m_y_doorman_01',
		posX: -1158.39,
		posY: -1518.21,
		posZ: 10.63,
		posA: -101,
		dimension: -1,
		houseInteriorId: 5,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 313,
		dialogId: 150,
		description: 'Бот для сдачи контракта "поддельная доверенность"',
		type: null,
		skin: 's_m_y_doorman_01',
		posX: 345.045,
		posY: -996.679,
		posZ: -99.196,
		posA: 152.687,
		dimension: -1,
		houseInteriorId: 6,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 314,
		dialogId: 150,
		description: 'Бот для сдачи контракта "поддельная доверенность"',
		type: null,
		skin: 's_m_y_doorman_01',
		posX: 368.48,
		posY: 404.46,
		posZ: 145.5,
		posA: -32.6,
		dimension: -1,
		houseInteriorId: 7,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 315,
		dialogId: 150,
		description: 'Бот для сдачи контракта "поддельная доверенность"',
		type: null,
		skin: 's_m_y_doorman_01',
		posX: -776.207,
		posY: 612.447,
		posZ: 143.73,
		posA: -97,
		dimension: -1,
		houseInteriorId: 8,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 316,
		dialogId: 150,
		description: 'Бот для сдачи контракта "поддельная доверенность"',
		type: null,
		skin: 's_m_y_doorman_01',
		posX: -3.918,
		posY: 532.7712,
		posZ: 175.34,
		posA: -115.39,
		dimension: -1,
		houseInteriorId: 9,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 317,
		dialogId: 150,
		description: 'Бот для сдачи контракта "поддельная доверенность"',
		type: null,
		skin: 's_m_y_doorman_01',
		posX: -799.25,
		posY: 176.77,
		posZ: 72.834,
		posA: 45.21,
		dimension: -1,
		houseInteriorId: 10,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 318,
		dialogId: 151,
		description: 'mexican',
		type: null,
		skin: 'a_m_y_busicas_01',
		posX: 358.94,
		posY: 3.331,
		posZ: 82.997,
		posA: -120.171,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 319,
		dialogId: 152,
		description: 'lost',
		type: null,
		skin: 'cs_terry',
		posX: 960.6755,
		posY: -122.5115,
		posZ: 74.3531,
		posA: -153.1455,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 320,
		dialogId: 153,
		description: 'aod',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: 2002.913,
		posY: 3050.3379,
		posZ: 47.2080,
		posA: -7.9236,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 321,
		dialogId: 154,
		description: 'arm',
		type: null,
		skin: 'g_m_m_armlieut_01',
		posX: -1896.348,
		posY: 2053.07,
		posZ: 140.953,
		posA: 148.946,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 322,
		dialogId: 155,
		description: 'cayo perico drugs bot',
		type: null,
		skin: 's_m_m_ciasec_01',
		posX: 4995.879,
		posY: -5734.163,
		posZ: 19.88,
		posA: 36.921,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		peechName: 'GENERIC_HI',
		voiceName: 'S_M_Y_COP_01_BLACK_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 323,
		dialogId: 156,
		description: 'containers',
		type: null,
		skin: 'cs_molly',
		posX: 838.27,
		posY: -2926.82,
		posZ: 5.89,
		posA: -90,
		dimension: 0,
	},
	{
		id: 324,
		dialogId: 158,
		description: 'Bill Brooks',
		type: null,
		skin: 'a_m_m_salton_02',
		// 1964.354 4633.874 40.725
		posX: 1964.354,
		posY: 4633.874,
		posZ: 40.725,
		posA: 36.71,
		dimension: -1,
		animDic: 'timetable@maid@couch@',
		animName: 'base',
	},
	{
		id: 325,
		dialogId: 159,
		description: 'Sarah Brooks',
		type: null,
		skin: 'a_f_m_eastsa_01',
		posX: 1298.86,
		posY: -1738.64,
		posZ: 53.86,
		posA: 2.83,
		dimension: -1,
		scenario: 'WORLD_HUMAN_SMOKING',
	},
	{
		id: 326,
		dialogId: 160,
		description: 'Ragnar Killpatrick',
		type: null,
		skin: 'u_m_o_taphillbilly',
		posX: 1150.414,
		posY: -454.711,
		posZ: 66.984,
		posA: 174.122,
		dimension: -1,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
	},
	{
		id: 327,
		dialogId: 162,
		description: 'Gustav Bramsmen',
		type: null,
		skin: 's_m_m_gaffer_01',
		posX: 1200.645,
		posY: -3120.962,
		posZ: 5.54,
		posA: 337.27,
		dimension: -1,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
	},

	{
		id: 329,
		dialogId: 161,
		description: 'Joe Bob',
		type: null,
		skin: 'cs_old_man1a',
		posX: 335.766,
		posY: -1090.0,
		posZ: 29.55,
		posA: 178.28,
		dimension: -1,
		animDic: 'rcm_barry3',
		animName: 'barry_3_sit_loop',
	},
	{
		id: 330,
		dialogId: null,
		description: 'Собака Купер',
		type: null,
		skin: 'a_c_retriever',
		posX: 1673.947,
		posY: 4976.899,
		posZ: 41.712,
		posA: 319.4,
		dimension: -1,
		scenario: 'WORLD_DOG_BARKING_RETRIEVER',
	},
	{
		id: 331,
		dialogId: null,
		description: 'Кошка',
		type: null,
		skin: 'a_c_cat_01',
		posX: 1676.981,
		posY: 4979.544,
		posZ: 42.743,
		posA: 146.91,
		dimension: -1,
	},
	{
		// Used on Events (Halloween)
		id: 332,
		dialogId: 166,
		description: 'Baygor Earthsong',
		type: null,
		skin: 'a_m_o_acult_02',
		mask: 'mj_hw_mask_2',
		posX: 1575.216,
		posY: 6452.541,
		posZ: 25.027,
		posA: -152.641,
		dimension: 0,
		blip: { id: 484, color: 5, name: 'blips.halloweenPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		// Used on Events (Halloween)
		id: 333,
		dialogId: 167,
		description: 'Baygor Earthsong',
		type: null,
		skin: 'a_m_o_acult_02',
		mask: 'mj_hw_mask_2',
		posX: 2559.184,
		posY: 6156.103,
		posZ: 162.023,
		posA: -34.76,
		dimension: 0,
		blip: { id: 484, color: 5, name: 'blips.halloweenPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		// Used on Events (Halloween)
		id: 334,
		dialogId: 168,
		description: 'Baygor Earthsong',
		type: null,
		skin: 's_m_m_mariachi_01',
		mask: 'mj_hw_mask_1',
		posX: 3065.2045,
		posY: 2220.134,
		posZ: 3.102,
		posA: -134.57,
		dimension: 0,
		// animDic: 'mp_army_contact',
		// animName: 'positive_a',
		blip: { id: 484, color: 5, name: 'blips.halloweenPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		id: 335,
		dialogId: 169,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: -1306.8501,
		posY: 267.4699,
		posZ: 63.5965,
		posA: -38.238,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 336,
		dialogId: 170,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: -1232.4061,
		posY: -185.4266,
		posZ: 39.2111,
		posA: -41.56,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 337,
		dialogId: 172,
		description: 'Аренда авто',
		type: null,
		skin: 's_m_m_snowcop_01',
		posX: 3573.5867,
		posY: -4678.6226,
		posZ: 114.3318,
		posA: -76.879,
		dimension: -1,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		enabled: false,
	},
	{
		id: 338,
		dialogId: 173,
		description: 'Аренда авто',
		type: null,
		skin: 'g_m_m_chemwork_01',
		posX: 3288.58,
		posY: -4567.048,
		posZ: 118.168,
		posA: 147.336,
		dimension: -1,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		enabled: false,
	},
	{
		id: 339,
		dialogId: 175,
		description: 'Аренда авто',
		type: null,
		skin: 'g_m_y_korean_02',
		posX: 5339.351,
		posY: -5208.583,
		posZ: 82.764,
		posA: -131.86,
		dimension: -1,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		enabled: false,
	},
	{
		id: 340,
		dialogId: 176,
		description: 'Бот для ивента на новый год Yankton',
		type: null,
		skin: 'g_m_m_chiboss_01',
		posX: 3250.1472,
		posY: -4815.5825,
		posZ: 113.28,
		posA: 160.9614,
		dimension: -1,
		enabled: false,
	},
	{
		id: 341,
		dialogId: 177,
		description: 'Бот для ивента на новый год Yankton',
		type: null,
		skin: 'u_m_m_edtoh',
		posX: 3526.483,
		posY: -4664.63,
		posZ: 114.15,
		posA: 171.511,
		dimension: -1,
		enabled: false,
	},
	{
		id: 342,
		dialogId: 178,
		description: 'Бот для ивента на новый год Yankton',
		type: null,
		skin: 'ig_hunter',
		posX: 4494.177,
		posY: -5050.305,
		posZ: 111.784,
		posA: -120.75,
		dimension: -1,
		enabled: false,
	},
	{
		id: 343,
		dialogId: 179,
		description: 'Бот для ивента на новый год Yankton',
		type: null,
		skin: 'u_m_m_filmdirector',
		posX: 3151.803,
		posY: -4825.012,
		posZ: 111.744,
		posA: -134.329,
		dimension: -1,
		enabled: false,
	},
	{
		id: 344,
		dialogId: 180,
		description: 'Бот для ивента на новый год Yankton',
		type: null,
		skin: 'santaclaus',
		posX: 3135.908,
		posY: -4827.556,
		posZ: 111.814,
		posA: 86.305,
		dimension: -1,
		enabled: false,
	},
	/**      {
	 id: 332,
	 dialogId: null,
	 description: 'westy',
	 type: null,
	 skin: 'a_c_westy',
	 posX: 1353.29,
	 posY: -258.15,
	 posZ: 94.19,
	 posA: 136.38,
	 dimension: -1,
	 scenario: 'WORLD_HUMAN_CLIPBOARD'
	 },
	 scenario: 'WORLD_HUMAN_CLIPBOARD'
	 },
	 */
	{
		// Used on Events (Spring Day)
		id: 345,
		dialogId: 181,
		description: 'Seed',
		type: null,
		skin: 'a_m_y_hippy_01',
		posX: -1690.52,
		posY: -1114.0448,
		posZ: 13.1523,
		posA: -136.4552,
		dimension: 0,
		enabled: false,
		blip: { id: 463, color: 73, name: 'blips.springPed', dimension: 0, shortRange: true },
	},
	{
		// Used on Events (Spring Day)
		id: 346,
		dialogId: 182,
		description: 'Eric',
		type: null,
		skin: 'u_m_y_rsranger_01',
		posX: -1109.746,
		posY: -1694.0586,
		posZ: 4.5646,
		posA: -55.5879,
		dimension: 0,
		enabled: false,
		blip: { id: 463, color: 73, name: 'blips.springPed', dimension: 0, shortRange: true },
	},
	{
		// Used on Events (Spring Day)
		id: 347,
		dialogId: 183,
		description: 'Derek',
		type: null,
		skin: 'a_m_y_salton_01',
		posX: 411.6736,
		posY: 6634.2178,
		posZ: 28.243,
		posA: 121.794,
		dimension: 0,
		enabled: false,
		blip: { id: 463, color: 73, name: 'blips.springPed', dimension: 0, shortRange: true },
	},
	{
		// Used on Events (Spring Day)
		id: 348,
		description: 'Tir guy',
		type: null,
		skin: 'a_m_m_polynesian_01',
		posX: -1637.4406,
		posY: -1107.4893,
		posZ: 13.0268,
		posA: 56.35,
		dimension: 0,
		enabled: false,
		blip: { id: 103, color: 73, name: 'blips.tirPed', dimension: 0, shortRange: true },
	},
	{
		// Used on Events (Spring Day)
		id: 349,
		dialogId: 184,
		description: 'Molly Диджей',
		type: null,
		skin: 'a_f_y_beach_01',
		animDic: 'anim@amb@nightclub@mini@dance@dance_solo@female@var_a@',
		animName: 'low_center_down',
		posX: -1602.5162,
		posY: -1062.8053,
		posZ: 13.0176,
		posA: 52.48908,
		dimension: 0,
		enabled: false,
		blip: { id: 126, color: 73, name: 'blips.dancePed', dimension: 0, shortRange: true },
	},
	{
		// Used on Events (Spring Day)
		id: 350,
		description: 'Panic room',
		type: null,
		skin: 'u_m_o_filmnoir',
		animDic: 'anim@mp_celebration@idles@male',
		animName: 'celebration_idle_m_b',
		posX: -1654.478,
		posY: -1094.3668,
		posZ: 13.1369,
		posA: -141.5659,
		dimension: 0,
		enabled: false,
		blip: { id: 362, color: 73, name: 'blips.panicRoom', dimension: 0, shortRange: true },
	},
	{
		// Used on Events (Spring Day)
		id: 351,
		dialogId: 185,
		description: 'Luna',
		type: null,
		skin: 'a_f_o_soucent_01',
		animDic: 'timetable@reunited@ig_10',
		animName: 'base_amanda',
		posX: 452.4,
		posY: -783.5077,
		posZ: 27.3422,
		posA: -70.8661,
		dimension: 0,
		enabled: false,
		blip: { id: 463, color: 73, name: 'blips.springPed', dimension: 0, shortRange: true },
	},
	{
		// Used on Events (Spring Day)
		id: 352,
		dialogId: 186,
		description: 'Helen Кондитер',
		type: null,
		skin: 'ig_magenta',
		animDic: 'amb@world_human_hang_out_street@female_arms_crossed@base',
		animName: 'base',
		posX: 2561.9341,
		posY: 2590.866,
		posZ: 38.0754,
		posA: -70.8661,
		dimension: 0,
		enabled: false,
		blip: { id: 463, color: 73, name: 'blips.springPed', dimension: 0, shortRange: true },
	},
	{
		id: 353,
		dialogId: null,
		description: 'Ambient NPC park (spring event)',
		type: null,
		skin: 'u_m_y_pogo_01',
		posX: -1636.7522,
		posY: -1092.2234,
		posZ: 13.5768,
		posA: -38,
		dimension: 0,
		animDic: 'anim@heists@fleeca_bank@hostages@intro',
		animName: 'intro_loop_ped_a',
		enabled: false,
	},
	{
		id: 354,
		dialogId: null,
		description: 'Ambient NPC park (spring event)',
		type: null,
		skin: 'u_m_y_juggernaut_01',
		posX: -1646.1626,
		posY: -1078.8,
		posZ: 13.1545,
		posA: -178,
		dimension: 0,
		animDic: 'anim@amb@business@bgen@bgen_no_work@',
		animName: 'sit_phone_phoneputdown_idle_nowork',
		enabled: false,
	},
	{
		id: 355,
		dialogId: null,
		description: 'Ambient NPC park (spring event)',
		type: null,
		skin: 'u_m_y_imporage',
		posX: -1615.1525,
		posY: -1073.2405,
		posZ: 13.0186,
		posA: 24.3,
		dimension: 0,
		animDic: 'amb@world_human_muscle_flex@arms_at_side@base',
		animName: 'base',
		enabled: false,
	},
	{
		id: 356,
		dialogId: null,
		description: 'Ambient NPC park (spring event)',
		type: null,
		skin: 'u_m_y_guido_01',
		posX: -1622.5876,
		posY: -1067.0992,
		posZ: 13.0613,
		posA: 99,
		dimension: 0,
		animDic: 'anim@mp_player_intcelebrationfemale@face_palm',
		animName: 'face_palm',
		enabled: false,
	},
	{
		id: 357,
		dialogId: null,
		description: 'Ambient NPC park (spring event)',
		type: null,
		skin: 'u_f_o_eileen',
		posX: -1624.009,
		posY: -1066.7073,
		posZ: 13.0753,
		posA: 139.6,
		dimension: 0,
		animDic: 'amb@world_human_bum_standing@depressed@idle_a',
		animName: 'idle_a',
		enabled: false,
	},
	{
		id: 358,
		dialogId: null,
		description: 'Ambient NPC park (spring event)',
		type: null,
		skin: 's_m_y_mime',
		posX: -1640.1758,
		posY: -1087.978,
		posZ: 13.0535,
		posA: -39.6,
		dimension: 0,
		animDic: 'timetable@mime@ig_1',
		animName: 'cowboy_riding_horse',
		enabled: false,
	},
	{
		id: 359,
		dialogId: null,
		description: 'Ambient NPC park (spring event)',
		type: null,
		skin: 's_m_y_clown_01',
		posX: -1624.3517,
		posY: -1083.9297,
		posZ: 13.0029,
		posA: 122,
		dimension: 0,
		scenario: 'WORLD_HUMAN_SMOKING',
		enabled: false,
	},
	{
		id: 360,
		dialogId: null,
		description: 'Ambient NPC park (spring event)',
		type: null,
		skin: 's_m_m_gardener_01',
		posX: -1611.1516,
		posY: -1045.6747,
		posZ: 13.104,
		posA: -70.8,
		dimension: 0,
		animDic: 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@',
		animName: 'machinic_loop_mechandplayer',
		enabled: false,
	},
	{
		id: 361,
		dialogId: 115,
		description: 'Casino Chips Trader',
		type: null,
		skin: 's_f_y_casino_01',
		posX: 1170.5287,
		posY: 194.2542,
		posZ: -50.6361,
		posA: 166.496,
		dimension: -1,
		animDic: 'anim_casino_b@amb@casino@games@roulette@dealer_female',
		animName: 'idle',
	},
	{
		id: 362,
		dialogId: 187,
		description: 'dmv reception',
		type: null,
		skin: 'a_f_y_femaleagent',
		posX: -1072.415,
		posY: -824.235,
		posZ: 5.208,
		posA: -139.99,
		dimension: 0,
		radius: 4,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		peechName: 'GENERIC_HI',
		voiceName: 'S_M_Y_COP_01_BLACK_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		blip: { id: 498, color: 0, name: 'blips.dmvReception', dimension: 0, shortRange: true },
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 363,
		dialogId: 189,
		description: 'dmv 2',
		type: null,
		skin: 'ig_trafficwarden',
		posX: -1131.839,
		posY: -849.217,
		posZ: 13.566,
		posA: 120.43,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		peechName: 'GENERIC_HI',
		voiceName: 'S_M_Y_COP_01_BLACK_FULL_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
		blip: { id: 426, color: 0, name: 'blips.dmvImpound', dimension: 0, shortRange: true },
		// animDic: 'anim@amb@business@cfid@cfid_desk_no_work_bgen_chair_no_work@',
		// animName: 'transition_sleep_lazyworker',
	},
	{
		id: 364,
		dialogId: 190,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: 1961.7891,
		posY: 4650.8174,
		posZ: 40.7546,
		posA: -167.244,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 365,
		dialogId: null,
		description: 'Кассир мотосалон',
		type: null,
		skin: 'g_m_y_lost_01',
		posX: 1148.0308,
		posY: -789.1385,
		posZ: 57.6381,
		posA: 5.6693,
		dimension: 0,
	},
	{
		id: 366,
		description: 'Fisherman Equip Seller',
		type: null,
		skin: 'csb_cletus',
		posX: -1602.1582,
		posY: 5197.7275,
		posZ: 4.3253,
		posA: -62.3622,
		dimension: 0,
		animDic: 'anim@amb@casino@hangout@ped_male@stand@01b@idles',
		animName: 'idle_a',
	},
	// Summer event - start
	{
		id: 367,
		dialogId: 191,
		description: 'Sofia',
		type: null,
		skin: 'a_f_m_bevhills_01',
		posX: -1744.2197,
		posY: -805.5297,
		posZ: 9.0432,
		posA: -107.7165,
		dimension: -1,
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		id: 368,
		dialogId: 192,
		description: 'Fabien',
		type: null,
		skin: 'ig_fabien',
		posX: -1437.3495,
		posY: 5402.5449,
		posZ: 24.5956,
		posA: -93.5433,
		dimension: 0,
		animDic: 'rcmcollect_paperleadinout@',
		animName: 'meditiate_idle',
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		id: 369,
		dialogId: 193,
		description: 'Bill',
		type: null,
		skin: 's_m_y_marine_02',
		posX: -2039.6967,
		posY: -1034.5714,
		posZ: 2.585,
		posA: -68.0315,
		dimension: 0,
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		id: 370,
		dialogId: 195,
		description: 'Patrick',
		type: null,
		skin: 'cs_beverly',
		posX: 2970.8044,
		posY: 496.2198,
		posZ: 33.189,
		posA: -124.7244,
		dimension: 0,
		animDic: 'amb@medic@standing@kneel@base',
		animName: 'base',
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		id: 371,
		dialogId: 194,
		description: 'Rick',
		type: null,
		skin: 'a_m_o_acult_02',
		posX: 3634.1538,
		posY: 4997.8418,
		posZ: 12.6996,
		posA: -147.4016,
		dimension: 0,
		animDic: 'mp_army_contact',
		animName: 'positive_a',
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		id: 372,
		dialogId: 196,
		description: 'Sara',
		type: null,
		skin: 'a_f_m_beach_01',
		posX: -1381.7803,
		posY: 6740.0835,
		posZ: 5.8755,
		posA: -110.5512,
		dimension: 0,
		animName: 'base',
		animDic: 'amb@world_human_jog_standing@male@base',
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		id: 373,
		dialogId: 197,
		description: 'Hartman',
		type: null,
		skin: 'mp_m_exarmy_01',
		posX: -277.2659,
		posY: -2503.4241,
		posZ: 5.9934,
		posA: -153.0709,
		dimension: 0,
		// animDic: 'mp_army_contact',
		// animName: 'positive_a',
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		id: 374,
		dialogId: 198,
		description: 'Abelino',
		type: null,
		skin: 'a_m_m_indian_01',
		posX: -2058.7649,
		posY: -1019.6176,
		posZ: 11.8909,
		posA: -20.8425,
		dimension: 0,
		animDic: 'random@car_thief@victimpoints_ig_3',
		animName: 'arms_waving',
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		id: 375,
		dialogId: 199,
		description: 'Pablo',
		type: null,
		skin: 'csb_rashcosvki',
		posX: 441.7319,
		posY: 5582.7427,
		posZ: 781.186,
		posA: 48.189,
		dimension: 0,
		animDic: 'anim@miss@low@fin@vagos@',
		animName: 'idle_ped06',
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	// {
	//     id: 376,
	//     dialogId: 200,
	//     description: 'Rick',
	//     type: null,
	//     skin: 'a_m_o_acult_02',
	//     posX: 3634.1538,
	//     posY: 4997.8418,
	//     posZ: 12.6996,
	//     posA: -147.4016,
	//     dimension: 0,
	//     blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
	//     enabled: false,
	// },
	{
		id: 377,
		dialogId: 201,
		description: 'Fred',
		type: null,
		skin: 'cs_bankman',
		posX: -2089.5957,
		posY: -1022.8088,
		posZ: 5.8923,
		posA: 15.1732,
		dimension: 0,
		animDic: 'switch@michael@tv_w_kids',
		animName: '001520_02_mics3_14_tv_w_kids_idle_mic',
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	// {
	//     id: 365,
	//     dialogId: 191,
	//     description: 'Бот кино',
	//     type: null,
	//     skin: 'u_m_m_aldinapoli',
	//     posX: -1687.43,
	//     posY: -891.9,
	//     posZ: 8.33,
	//     posA: 2.8,
	//     dimension: 0,
	//     animDic: 'missmic4premiere',
	//     animName: 'crowd_c_idle_01',
	//     speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
	// },
	{
		id: 378,
		dialogId: 202,
		description: 'Pirs',
		type: null,
		skin: 'cs_old_man1a',
		posX: 2151.0593,
		posY: 4788.6064,
		posZ: 40.9568,
		posA: -96.378,
		dimension: 0,
		// animDic: 'switch@michael@tv_w_kids',
		// animName: '001520_02_mics3_14_tv_w_kids_idle_mic',
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	{
		id: 379,
		dialogId: 203,
		description: 'Tyler',
		type: null,
		skin: 's_m_y_baywatch_01',
		posX: -1519.2131,
		posY: -1272.8176,
		posZ: 3.4491,
		posA: 93.5433,
		dimension: 0,
		animDic: 'amb@prop_human_bum_shopping_cart@male@idle_a',
		animName: 'idle_a',
		blip: { id: 439, color: 5, name: 'blips.summerPed', dimension: 0, shortRange: false },
		enabled: false,
	},
	// {
	//     id: 380,
	//     dialogId: 204,
	//     description: 'Бармен концерт 1',
	//     type: null,
	//     skin: 'ig_tracydisanto',
	//     posX: -1785.0330,
	//     posY: -815.8154,
	//     posZ: 8.7062,
	//     posA: -34.0157,
	//     dimension: -1,
	//     speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
	// },
	// {
	//     id: 381,
	//     dialogId: 205,
	//     description: 'Бармен концерт 2',
	//     type: null,
	//     skin: 'ig_tylerdix',
	//     posX: -1836.6198,
	//     posY: -768.9890,
	//     posZ: 8.7062,
	//     posA: -45.3543,
	//     dimension: -1,
	//     speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
	// },
	// Summer event - end

	// University - start
	{
		id: 380,
		dialogId: 207,
		description: 'Библиотекарь в университете',
		type: null,
		skin: 'cs_mrs_thornhill',
		posX: -1561.556,
		posY: 202.0484,
		posZ: 59.34,
		posA: 22.6772,
		dimension: 0,
		animDic: 'amb@world_human_stand_impatient@female@no_sign@base',
		animName: 'base',
	},

	{
		id: 381,
		dialogId: 208,
		description: 'Доктор в университете',
		type: null,
		skin: 's_m_m_scientist_01',
		posX: -1648.8,
		posY: 157.2,
		posZ: 70.5619,
		posA: -53.8583,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'amb@prop_human_bum_shopping_cart@male@idle_a',
		// animName: 'idle_a',
	},
	{
		id: 382,
		dialogId: 209,
		description: 'Инженер в университете',
		type: null,
		skin: 's_m_m_lathandy_01',
		posX: -1659.9561,
		posY: 184.8659,
		posZ: 70.5619,
		posA: -56.6929,
		dimension: 0,
		animDic: 'amb@world_human_cop_idles@male@idle_b',
		animName: 'idle_e',
	},
	{
		id: 383,
		dialogId: 210,
		description: 'Повар в университете',
		type: null,
		skin: 's_m_y_chef_01',
		posX: -1653.7054,
		posY: 186.1846,
		posZ: 66.6022,
		posA: 116.2205,
		dimension: 0,
		animDic: 'anim@heists@heist_corona@single_team',
		animName: 'single_team_loop_boss',
	},
	{
		id: 384,
		dialogId: 211,
		description: 'Тренер в университете',
		type: null,
		skin: 'cs_maryann',
		posX: -1778.8748,
		posY: 185.6703,
		posZ: 64.3612,
		posA: -150.2362,
		dimension: 0,
		scenario: 'WORLD_HUMAN_YOGA',
		// animDic: 'amb@prop_human_bum_shopping_cart@male@idle_a',
		// animName: 'idle_a',
	},
	{
		id: 385,
		dialogId: 212,
		description: 'Юрист в университете',
		type: null,
		skin: 'cs_nigel',
		posX: -1661.8418,
		posY: 183.1648,
		posZ: 70.5619,
		posA: 100.5512,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		// animDic: 'amb@prop_human_bum_shopping_cart@male@idle_a',
		// animName: 'idle_a',
	},
	{
		id: 386,
		dialogId: 213,
		description: 'Преподаватель Испанского в университете',
		type: null,
		skin: 'csb_oscar',
		posX: -1661.9341,
		posY: 183.7187,
		posZ: 66.6022,
		posA: 136.0709,
		dimension: 0,
		scenario: 'WORLD_HUMAN_TOURIST_MAP',
		// animDic: 'amb@prop_human_bum_shopping_cart@male@idle_a',
		// animName: 'idle_a',
	},
	{
		id: 387,
		dialogId: 214,
		description: 'Преподаватель Японского в университете',
		type: null,
		skin: 'IG_GeorginaCheng',
		posX: -1651.2263,
		posY: 159.3363,
		posZ: 66.6022,
		posA: 107.7165,
		dimension: 0,
		// scenario: 'WORLD_HUMAN_STAND_MOBILE_UPRIGHT',
		// animDic: 'amb@prop_human_bum_shopping_cart@male@idle_a',
		// animName: 'idle_a',
	},
	{
		id: 388,
		dialogId: 215,
		description: 'Преподаватель Русского в университете',
		type: null,
		skin: 'ig_agatha',
		posX: -1648.167,
		posY: 159.4549,
		posZ: 66.6022,
		posA: -62.3622,
		dimension: 0,
		animDic: 'mini@hookers_spcokehead',
		animName: 'idle_reject_loop_a',
	},
	{
		id: 389,
		dialogId: 216,
		description: 'Преподаватель Итальянского в университете',
		type: null,
		skin: 'u_f_y_comjane',
		posX: -1667.5385,
		posY: 179.9736,
		posZ: 61.7495,
		posA: -25.3622,
		dimension: 0,
		scenario: 'WORLD_HUMAN_TOURIST_MAP',
		// animDic: 'amb@prop_human_bum_shopping_cart@male@idle_a',
		// animName: 'idle_a',
	},
	// University - end

	// Halloween 2024 - start
	// {
	//     id: 390,
	//     dialogId: 217,
	//     description: 'NPC Задания с трактором',
	//     type: null,
	//     skin: 'ig_old_man2',
	//     posX: -44.3868,
	//     posY: 2880.0659,
	//     posZ: 59.0535,
	//     posA: -96.3780,
	//     dimension: 0,
	//     blip: { id: 484, color: 5, name: 'blips.halloweenPed7', dimension: 0, shortRange: true },
	//     animDic: 'mini@repair',
	//     animName: 'fixing_a_ped',
	//     enabled: false
	// },
	// {
	//     id: 391,
	//     dialogId: 218,
	//     description: 'NPC Задания с фотографиями',
	//     type: null,
	//     skin: 'a_m_o_acult_02',
	//     posX: 1784.2153,
	//     posY: 3889.8594,
	//     posZ: 34.7391,
	//     posA: -2.8346,
	//     dimension: 0,
	//     scenario: 'WORLD_HUMAN_STUPOR',
	//     blip: { id: 484, color: 5, name: 'blips.halloweenPed1', dimension: 0, shortRange: false },
	//     enabled: false
	//     // animDic: 'mini@repair',
	//     // animName: 'fixing_a_ped'
	// },
	// {
	//     id: 392,
	//     dialogId: 219,
	//     description: 'NPC Задания с алхимией',
	//     type: null,
	//     skin: 'a_m_y_acult_02',
	//     posX: -1133.6307,
	//     posY: 4947.3496,
	//     posZ: 222.2609,
	//     posA: -51.0819,
	//     dimension: 0,
	//     blip: { id: 484, color: 5, name: 'blips.halloweenPed6', dimension: 0, shortRange: true },
	//     enabled: false,
	//     // scenario: 'WORLD_HUMAN_STUPOR',
	//     animDic: 'rcmbarry',
	//     animName: 'bar_1_teleport_mic',
	// },
	// {
	//     id: 393,
	//     dialogId: 220,
	//     description: 'NPC Задания 2',
	//     type: null,
	//     skin: 'mp_m_exarmy_01',
	//     posX: 2440.4043,
	//     posY: 4954.5098,
	//     posZ: 45.7927,
	//     posA: -14.1732,
	//     dimension: 0,
	//     blip: { id: 484, color: 5, name: 'blips.halloweenPed2', dimension: 0, shortRange: true },
	//     animDic: 'anim@amb@casino@hangout@ped_male@stand@02b@base',
	//     animName: 'base',
	//     enabled: false,
	// },
	// {
	//     id: 394,
	//     description: 'NPC Задания 2',
	//     type: null,
	//     skin: 'ig_omega',
	//     posX: 2437.8462,
	//     posY: 4957.0815,
	//     posZ: 46.0791,
	//     posA: 42.5197,
	//     dimension: 0,
	//     scenario: 'WORLD_HUMAN_HAMMERING',
	//     // enabled: false,
	// },
	// {
	//     id: 395,
	//     dialogId: 222,
	//     description: 'NPC Задания 4',
	//     type: null,
	//     skin: 'cs_dreyfuss',
	//     posX: -288.8703,
	//     posY: 2833.9780,
	//     posZ: 55.4813,
	//     posA: -31.1811,
	//     dimension: 0,
	//     blip: { id: 484, color: 5, name: 'blips.halloweenPed4', dimension: 0, shortRange: true },
	//     scenario: 'WORLD_HUMAN_INSPECT_CROUCH',
	//     enabled: false,
	// },
	// {
	//     id: 396,
	//     dialogId: 223,
	//     description: 'NPC Задания "Побег"',
	//     type: null,
	//     skin: 'u_m_o_filmnoir',
	//     posX: -1136.2946,
	//     posY: 4659.5474,
	//     posZ: 243.9297,
	//     posA: -45.3543,
	//     dimension: 0,
	//     blip: { id: 484, color: 5, name: 'blips.halloweenPed9', dimension: 0, shortRange: true },
	//     enabled: false,
	//     // scenario: 'WORLD_HUMAN_STUPOR',
	//     animDic: 'misscarsteal1leadin',
	//     animName: 'devon_idle_02',
	// },
	// {
	//     id: 397,
	//     dialogId: 224,
	//     description: 'NPC Задания "Сражение"',
	//     type: null,
	//     skin: 'u_m_m_jesus_01',
	//     posX: 2009.8945,
	//     posY: -1919.6571,
	//     posZ: 112.2146,
	//     posA: -167.2441,
	//     dimension: 0,
	//     blip: { id: 484, color: 5, name: 'blips.halloweenPed10', dimension: 0, shortRange: true },
	//     enabled: false,
	//     // scenario: 'WORLD_HUMAN_STUPOR',
	//     animDic: 'missfam5_yoga',
	//     animName: 'c1_pose',
	// },
	// Halloween 2024 - end

	// Winter 2025 - start
	{
		id: 390,
		dialogId: 234,
		description: 'Задание мини-игры на Арене',
		type: null,
		skin: 'u_f_m_promourn_01',
		posX: 3154.1802,
		posY: -4834.8657,
		posZ: 111.7429,
		posA: -68.0315,
		dimension: -1,
		enabled: false,
		blip: { id: 480, color: 3, name: 'blips.xmasQuestPed', dimension: -1, shortRange: false },
	},
	{
		id: 391,
		dialogId: 230,
		description: 'Задание спасение застрявшего транспорта',
		type: null,
		skin: 's_m_m_snowcop_01',
		posX: 3318.9495,
		posY: -4897.0156,
		posZ: 111.6586,
		posA: -138.8976,
		dimension: -1,
		enabled: false,
		blip: { id: 480, color: 3, name: 'blips.xmasQuestPed', dimension: -1, shortRange: false },
	},
	{
		id: 392,
		dialogId: 235,
		description: 'Задание возвращение эльфов в чувства',
		type: null,
		skin: 'a_f_m_prolhost_01',
		posX: 3549.666,
		posY: -4691.9209,
		posZ: 114.1187,
		posA: 133.2283,
		dimension: -1,
		enabled: false,
		blip: { id: 480, color: 3, name: 'blips.xmasQuestPed', dimension: -1, shortRange: false },
	},
	{
		id: 393,
		dialogId: 233,
		description: 'Задание мини-игра найди пару',
		type: null,
		skin: 'u_m_m_promourn_01',
		posX: 4480.3779,
		posY: -5074.022,
		posZ: 112.0798,
		posA: -164.4095,
		dimension: -1,
		enabled: false,
		blip: { id: 480, color: 3, name: 'blips.xmasQuestPed', dimension: -1, shortRange: false },
	},
	{
		id: 394,
		dialogId: 231,
		description: 'Задание снять чары гринча с эльфов',
		type: null,
		skin: 'ig_hunter',
		posX: 3884.2549,
		posY: -5045.6836,
		posZ: 110.9004,
		posA: -161.5748,
		dimension: -1,
		enabled: false,
		blip: { id: 480, color: 3, name: 'blips.xmasQuestPed', dimension: -1, shortRange: false },
	},
	{
		id: 395,
		dialogId: 232,
		description: 'Задание Мини игра - три в ряд',
		type: null,
		skin: 'ig_talina',
		posX: 5439.877,
		posY: -5183.4199,
		posZ: 78.734,
		posA: 73.7008,
		dimension: -1,
		enabled: false,
		blip: { id: 480, color: 3, name: 'blips.xmasQuestPed', dimension: -1, shortRange: false },
	},
	{
		id: 396,
		dialogId: 236,
		description: 'Задание Доставить подарки на санях',
		type: null,
		skin: 'elfsecond',
		posX: 3267.2703,
		posY: -4687.9121,
		posZ: 113.5626,
		posA: -175.748,
		dimension: -1,
		enabled: false,
		animDic: 'random@robbery',
		animName: 'sit_down_idle_01',
		blip: { id: 480, color: 3, name: 'blips.xmasQuestPed', dimension: -1, shortRange: false },
	},
	// Winter 2025 - end

	{
		id: 397,
		dialogId: 238,
		description: 'Медик 2',
		gpt: {
			name: 'Michel Carver',
			description:
				'You will now engage in a conversation while playing the role of {name}, a doctor who can heal people. Your purpose is to discuss topics related to medicine, health, and well-being. Act like a real human and avoid revealing that you are an AI model. Focus on providing information and answering questions related to medical conditions, treatments, and general health advice. Strictly focus on gameplay help (settings, jobs, rules). Never use/provide obscene, derogatory, or politically charged words (e.g., пидор, fag, nigger), NSFW content, insults, or Twitch-banned topics. Immediately terminate forbidden requests (e.g., word-guessing for slurs) with: I can’t assist with that. Ask about GTA RP mechanics instead. Your responses are auto-filtered; violations trigger a system crash (Error 403).',
		},
		type: null,
		skin: 's_m_m_doctor_01',
		posX: 1132.9451,
		posY: -1558.7605,
		posZ: 35.0088,
		posA: 90,
		dimension: 0,
		scenario: 'WORLD_HUMAN_CLIPBOARD',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 398,
		dialogId: 225,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: -689.789,
		posY: -1399.7539,
		posZ: 4.9993,
		posA: 133.2283,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 399,
		dialogId: 226,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: -1185.1516,
		posY: -1507.6879,
		posZ: 4.3759,
		posA: -8.5039,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 400,
		dialogId: 227,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: -684.3824,
		posY: 5815.3848,
		posZ: 17.3165,
		posA: -2.8346,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 401,
		dialogId: 228,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: 687.033,
		posY: 601.9121,
		posZ: 128.896,
		posA: -48.189,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 402,
		dialogId: 229,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: -818.8351,
		posY: 5422.3647,
		posZ: 34.082,
		posA: -110.5512,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},
	{
		id: 403,
		dialogId: 237,
		description: 'Аренда авто',
		type: null,
		skin: 'u_m_m_aldinapoli',
		posX: 984.0527,
		posY: -1400.8616,
		posZ: 31.504,
		posA: 164.4095,
		dimension: 0,
		animDic: 'missmic4premiere',
		animName: 'crowd_c_idle_01',
		speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED',
	},

	// Valentine event
	{
		id: 404,
		dialogId: 239,
		description: 'Продавец валентинок',
		enabled: false,
		type: null,
		skin: 'ig_bestmen',
		posX: -320.7297, //-320.7297, 6172.6021, 32.3129,
		posY: 6172.6021,
		posZ: 32.3129,
		posA: 36.850,
		dimension: 0,
		scenario: 'WORLD_HUMAN_MUSICIAN',
		blip: { id: 489, color: 1, name: 'blips.valentineMarket', dimension: 0, shortRange: false },
	}
	// Valentine event
];
