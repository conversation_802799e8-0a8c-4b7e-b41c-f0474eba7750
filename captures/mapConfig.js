export const ZoneTypes = Object.freeze({
    COMMON: 0,
    SPECIAL: 1,
    CHECKPOINT: 2,
});

export const Buffs = Object.freeze({
    MANSION: 1,
    BANDS_TERRITORY: 2, // территория банд
    HELIPAD: 3,
    CASION: 4,
    EMS: 5,
    GOV: 6,
    JA<PERSON>: 7,
    FIB: 8,
    SHERIFF: 9,
    LSPD: 10,
    BUNKER: 11, // cahnge to sputnik
    WINDMILL: 12,
    HYDROELECTRIC: 13,
    OILRIG: 14,
    DRUG: 15,
    XXVI: 16,
    LABORATORY: 17,
    BUILD_COMP: 18,
    SPUTNIK: 19,
    FIRESTATION: 20,
    HOTEL: 21,
    SHIP: 22,
    ENCRYPT: 23,
    SANG: 24,

    //бизаки
    BIZ_ITEM_SHOPS: 100,
    BIZ_AMMO_SHOPS: 101,
    BIZ_BARBER_SHOPS: 102,
    BIZ_CLOTHES_SHOPS: 103,
    BIZ_TUNING_SHOPS: 104,
    BIZ_FUEL_STATIONS: 105,
    BIZ_TATTOO_SHOPS: 106,
    BIZ_AUTO_SHOPS: 107,
    BIZ_CARWASH: 108,

    //улучшения бизаков (предприятия)
    BIZ_ITEM_SHOPS_UPGRADE: 200,
    BIZ_AMMO_SHOPS_UPGRADE: 201,
    BIZ_BARBER_SHOPS_UPGRADE: 202,
    BIZ_CLOTHES_SHOPS_UPGRADE: 203,
    BIZ_TUNING_SHOPS_UPGRADE: 204,
    BIZ_FUEL_STATIONS_UPGRADE: 205,
    BIZ_TATTOO_SHOPS_UPGRADE: 206,
    BIZ_AUTO_SHOPS_UPGRADE: 207,
    BIZ_CARWASH_UPGRADE: 208,

    //работы
    JOBS_MINER: 300,
    JOBS_MUSHRUM: 301,
    JOBS_FISHERMAN: 302,
    JOBS_LUMERJACK: 303,
    JOBS_FARMER: 304,

    //доступ к крафту
    CRAFT_MILLITARY: 400,
    CRAFT_SCIENCE: 401,
    CRAFT_MEDICAL: 402,

    //улучшения доступ к крафту(секретные технологии)
    CRAFT_MILLITARY_UPGRADE: 500,
    CRAFT_SCIENCE_UPGRADE: 501,
    CRAFT_MEDICAL_UPGRADE: 502,

    //Контракты(место исследований)
    CONTRACTS_MONEY: 600,
    CONTRACTS_REPUTATION_LEGAL: 601,
    CONTRACTS_REPUTATION_ILLEGAL: 602,

    // клетки саботажа
    SABOTAGE_SATELLITE: 701,
    SABOTAGE_AIRPORT: 702,
    SABOTAGE_JET: 703,

    // предприятия свободного назначения
    ENTERPRISE: 800,
});

export const BuffsConfig = Object.freeze({
    [Buffs.MANSION]: {
        name: 'mansion',
        icon: 15,
        levels: [],
        // overtakeReduse: 1,
        maxPerFraction: 1,
        // require: [],
        extraDefence: 1,
        extraPlayers: 50,
        impact: 3,
    },
    [Buffs.HELIPAD]: {
        name: 'helipad',
        icon: 14,

        levels: [
            { value: 0.10, viewTitle: '10%' },
            { value: 0.20, viewTitle: '20%' },
            { value: 0.30, viewTitle: '30%' },
            { value: 0.40, viewTitle: '40%' },
            { value: 0.50, viewTitle: '50%' },
            { value: 0.60, viewTitle: '60%' },
        ],
        useTrans: false,
        levelViewType: 'inline',
        levelTitle: 'discountPercentage',

        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [],
        impact: 2,
    },
    [Buffs.CASION]: {
        name: 'casino',
        icon: 13,
        levels: [
            { value: 0.10, viewTitle: '-10%' },
            { value: 0.20, viewTitle: '-20%' },
            { value: 0.30, viewTitle: '-30%' },
            { value: 0.40, viewTitle: '-40%' },
            { value: 0.50, viewTitle: '-50%' },
            { value: 0.60, viewTitle: '-60%' },
        ],
        levelTitle: 'discountCommision',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [],
        impact: 2,
    },
    [Buffs.EMS]: {
        name: 'ems',
        icon: 25,
        levels: [
            { value: 0.05, viewTitle: '5%' },
            { value: 0.10, viewTitle: '10%' },
            { value: 0.15, viewTitle: '15%' },
            { value: 0.20, viewTitle: '20%' },
            { value: 0.25, viewTitle: '25%' },
            { value: 0.30, viewTitle: '30%' },
        ],
        levelTitle: 'chancePercentage',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [],
        impact: 2,
    },
    [Buffs.GOV]: {
        name: 'gov',
        icon: 43,
        levels: [
            { value: 0.10, viewTitle: '10%' },
            { value: 0.20, viewTitle: '20%' },
            { value: 0.30, viewTitle: '30%' },
            { value: 0.40, viewTitle: '40%' },
            { value: 0.50, viewTitle: '50%' },
            { value: 0.60, viewTitle: '60%' },
        ],
        overtakeReduse: 1,
        maxPerFraction: 1,
        levelTitle: 'discountPercentage',
        useTrans: false,
        // require: ['office'],
        //extraDefence: 1,
        impact: 2,
    },
    [Buffs.JAIL]: {
        name: 'jail',
        icon: 11,
        levels: [
            { value: 0.10, viewTitle: '10%' },
            { value: 0.20, viewTitle: '20%' },
            { value: 0.30, viewTitle: '30%' },
            { value: 0.40, viewTitle: '40%' },
            { value: 0.50, viewTitle: '50%' },
            { value: 0.60, viewTitle: '60%' },
        ],
        levelTitle: 'discountPercentage',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [],
        impact: 2,
    },
    [Buffs.FIB]: {
        name: 'fib',
        icon: 12,
        levels: [],
        overtakeReduse: 2,
        maxPerFraction: 1,
        require: [Buffs.CRAFT_MILLITARY],
        //extraDefence: 1,
        impact: 4,
    },
    [Buffs.SHERIFF]: {
        name: 'sheriff',
        icon: 9,
        levels: [
            { value: 1, viewTitle: '+1' },
        ],
        levelTitle: 'addAttacks',
        useTrans: false,
        maxPerFraction: 1,
        require: [],
        impact: 4,
        //extraAttacks: 1,
    },
    [Buffs.LSPD]: {
        name: 'lspd',
        icon: 10,
        levels: [
            { value: -1, viewTitle: '-1' },
        ],
        useTrans: false,
        levelTitle: 'removeDef',
        maxPerFraction: 1,
        require: [],
        impact: 4,
        //removeDef: 1,
    },
    [Buffs.BUNKER]: {
        name: 'sputnik',
        icon: 34,
        levels: [{ value: 1, viewTitle: 'diversyProtect' }],
        useTrans: true,
        levelTitle: 'effectProtection',
        require: [],
        // extraDefence: 1,
        impact: 2,
    },
    [Buffs.WINDMILL]: {
        name: 'windill',
        icon: 19,
        levels: [],
        overtakeReduse: 2,
        maxPerFraction: 1,
        require: [],
        impact: 2,
    },
    [Buffs.HYDROELECTRIC]: {
        name: 'hydroelectric',
        icon: 18,
        levels: [],
        overtakeReduse: 2,
        maxPerFraction: 1,
        require: [],
        //extraDefence: 1,
        impact: 2,
    },
    [Buffs.OILRIG]: {
        name: 'OILRIG',
        icon: 33,
        levels: [
            { value: 6, viewTitle: '4_PerDay' },
            { value: 4, viewTitle: '6_PerDay' },
            { value: 3, viewTitle: '8_PerDay' },
            { value: 2, viewTitle: '12_PerDay' },
            { value: 1, viewTitle: '24_PerDay' },
            { value: 1, viewTitle: '48_PerDay' },
        ],
        useTrans: true,
        levelTitle: 'oilAmount',
        levelViewType: 'inline',
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [],
        requirePerks: [
            {
                title: 'family.perks.oneOfPerk',
                items: [
                    'vehicleMaterials-bobcatxl',
                    'vehicleMaterials-speedo',
                    'vehicleMaterials-guardian'
                ]
            }
        ],
        impact: 3,
    },
    [Buffs.DRUG]: {
        name: 'WEED',
        icon: 32,
        levels: [
            { value: 0.02, viewTitle: '2%' },
            { value: 0.04, viewTitle: '4%' },
            { value: 0.06, viewTitle: '6%' },
            { value: 0.08, viewTitle: '8%' },
            { value: 0.10, viewTitle: '10%' },
            { value: 0.12, viewTitle: '12%' },
        ],
        levelTitle: 'productAmount',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [],
        extraDefence: 1,
        requirePerks: [
            {
                title: 'family.perks.oneOfPerk',
                items: [
                    'vehicleMaterials-bobcatxl',
                    'vehicleMaterials-speedo',
                    'vehicleMaterials-guardian'
                ]
            }
        ],
        impact: 2,
    },
    [Buffs.XXVI]: {
        name: 'xxvi',
        icon: 31,
        levels: [
            { value: 0.05, viewTitle: '+5%' },
            { value: 0.10, viewTitle: '+10%' },
            { value: 0.15, viewTitle: '+15%' },
            { value: 0.20, viewTitle: '+20%' },
            { value: 0.25, viewTitle: '+25%' },
            { value: 0.30, viewTitle: '+30%' },
        ],
        levelTitle: 'increasePercentage',
        useTrans: false,
        overtakeReduse: 1,
        require: [Buffs.ENTERPRISE],
        impact: 2,
        // extraDefence: 1,
    },
    [Buffs.LABORATORY]: {
        name: 'laboratory',
        icon: 20,
        levels: [{ value: 1, viewTitle: '6' }],
        levelTitle: 'extraLevel',
        useTrans: false,
        require: [],
        //extraDefence: 1,
        maxPerFraction: 1,
        impact: 3,
    },
    [Buffs.BUILD_COMP]: {
        name: 'build_comp',
        icon: 21,
        levels: [
            { value: 0.1, viewTitle: '-10%' },
            { value: 0.2, viewTitle: '-20%' },
            { value: 0.3, viewTitle: '-30%' },
            { value: 0.4, viewTitle: '-40%' },
            { value: 0.5, viewTitle: '-50%' },
            { value: 0.6, viewTitle: '-60%' },
        ],
        useTrans: false,
        levelTitle: 'reduceDuration',
        overtakeReduse: 1,
        require: [],
        impact: 2,
    },
    [Buffs.SPUTNIK]: {
        name: 'antena',
        icon: 22,
        levels: [
            { value: 1, viewTitle: '+1' },
            { value: 2, viewTitle: '+2' },
            { value: 3, viewTitle: '+3' },
            { value: 4, viewTitle: '+4' },
            { value: 5, viewTitle: '+5' },
        ],
        levelTitle: 'buffStatus',
        levelViewType: 'inline',
        useTrans: false,
        overtakeReduse: 1,
        require: [],
        extraDefence: 1,
        maxPerFraction: 1,
        impact: 3,
    },
    [Buffs.FIRESTATION]: {
        name: 'firestation',
        icon: 0,
        levels: [
            { value: 0.1 },
            { value: 0.2 },
            { value: 0.3 },
            { value: 0.4 },
            { value: 0.5 },
        ],
        overtakeReduse: 1,
        require: [],
        impact: 2,
    },
    [Buffs.HOTEL]: {
        name: 'hotel',
        icon: 62,
        levelTitle: 'addMembers',
        levelViewType: 'inline',
        levels: [
            { value: 1, viewTitle: '+1' },
            { value: 2, viewTitle: '+2' },
            { value: 3, viewTitle: '+3' },
            { value: 4, viewTitle: '+4' },
            { value: 5, viewTitle: '+5' },
            { value: 6, viewTitle: '+6' },
        ],
        require: [],
        impact: 1,
    },
    [Buffs.SHIP]: {
        name: 'ship',
        icon: 60,
        levels: [
            { value: 1, viewTitle: '+1' },
        ],
        levelTitle: 'addAttacks',
        useTrans: false,
        maxPerFraction: 1,
        require: [],
        //impact: 1,
        extraAttacks: 1,
    },
    [Buffs.ENCRYPT]: {
        name: 'enctypt',
        icon: 61,
        levels: [
            { value: -1, viewTitle: '-1' },
        ],
        useTrans: false,
        levelTitle: 'removeDef',
        maxPerFraction: 1,
        require: [],
        //impact: 3,
        removeDef: 1,
    },
    [Buffs.SANG]: {
        name: 'sang',
        icon: 63,
        levels: [],
        maxPerFraction: 1,
        require: [],
        impact: 4,
        //removeDef: 1,
    },
    [Buffs.BIZ_ITEM_SHOPS]: {
        name: 'item_shop',
        icon: 50,
        levels: [],
        overtakeReduse: 1,
        require: [],
        impact: 1,
    },

    [Buffs.BIZ_AMMO_SHOPS]: {
        name: 'ammo_shop',
        icon: 53,
        levels: [],
        overtakeReduse: 1,
        require: [],
        impact: 1,
    },

    [Buffs.BIZ_BARBER_SHOPS]: {
        name: 'barber_shop',
        icon: 52,
        levels: [],
        overtakeReduse: 1,
        require: [],
        impact: 1,
    },

    [Buffs.BIZ_AUTO_SHOPS]: {
        name: 'auto_shop',
        icon: 54,
        levels: [],
        overtakeReduse: 1,
        require: [],
        impact: 1,
    },

    [Buffs.BIZ_CLOTHES_SHOPS]: {
        name: 'clothes_shop',
        icon: 49,
        levels: [],
        overtakeReduse: 1,
        require: [],
        impact: 1,
    },

    [Buffs.BIZ_FUEL_STATIONS]: {
        name: 'fuel_shop',
        icon: 56,
        levels: [],
        overtakeReduse: 1,
        require: [],
        impact: 1,
    },

    [Buffs.BIZ_TATTOO_SHOPS]: {
        name: 'tattoo_shop',
        icon: 55,
        levels: [],
        overtakeReduse: 1,
        require: [],
        impact: 1,
    },

    [Buffs.BIZ_TUNING_SHOPS]: {
        name: 'tuning_shop',
        icon: 51,
        levels: [],
        overtakeReduse: 1,
        require: [],
        impact: 1,
    },

    [Buffs.BIZ_CARWASH]: {
        name: 'carwash_shop',
        icon: 59,
        levels: [],
        overtakeReduse: 1,
        require: [],
        impact: 1,
    },

    [Buffs.BIZ_ITEM_SHOPS_UPGRADE]: {
        name: 'item_shop_upgrade',
        icon: 38,
        levels: [
            { value: 0.05, viewTitle: '+5%' },
            { value: 0.10, viewTitle: '+10%' },
            { value: 0.15, viewTitle: '+15%' },
            { value: 0.20, viewTitle: '+20%' },
            { value: 0.25, viewTitle: '+25%' },
            { value: 0.30, viewTitle: '+30%' },
        ],
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [Buffs.BIZ_ITEM_SHOPS],
        levelTitle: 'increasePercentage',
        impact: 3,
    },

    [Buffs.BIZ_AMMO_SHOPS_UPGRADE]: {
        name: 'ammo_shop_upgrade',
        icon: 41,
        levels: [
            { value: 0.05, viewTitle: '+5%' },
            { value: 0.10, viewTitle: '+10%' },
            { value: 0.15, viewTitle: '+15%' },
            { value: 0.20, viewTitle: '+20%' },
            { value: 0.25, viewTitle: '+25%' },
            { value: 0.30, viewTitle: '+30%' },
        ],
        levelTitle: 'increasePercentage',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,

        require: [Buffs.BIZ_AMMO_SHOPS],
        impact: 3,
    },

    [Buffs.BIZ_BARBER_SHOPS_UPGRADE]: {
        name: 'barber_shop_upgrade',
        icon: 40,
        levels: [
            { value: 0.05, viewTitle: '+5%' },
            { value: 0.10, viewTitle: '+10%' },
            { value: 0.15, viewTitle: '+15%' },
            { value: 0.20, viewTitle: '+20%' },
            { value: 0.25, viewTitle: '+25%' },
            { value: 0.30, viewTitle: '+30%' },
        ],
        levelTitle: 'increasePercentage',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [Buffs.BIZ_BARBER_SHOPS],
        impact: 3,
    },

    [Buffs.BIZ_CLOTHES_SHOPS_UPGRADE]: {
        name: 'clothes_shop_upgrade',
        icon: 37,
        levels: [
            { value: 0.05, viewTitle: '+5%' },
            { value: 0.10, viewTitle: '+10%' },
            { value: 0.15, viewTitle: '+15%' },
            { value: 0.20, viewTitle: '+20%' },
            { value: 0.25, viewTitle: '+25%' },
            { value: 0.30, viewTitle: '+30%' },
        ],
        levelTitle: 'increasePercentage',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [Buffs.BIZ_CLOTHES_SHOPS],
        impact: 3,
    },

    [Buffs.BIZ_TUNING_SHOPS_UPGRADE]: {
        name: 'tuning_shop_upgrade',
        icon: 39,
        levels: [
            { value: 0.05, viewTitle: '+5%' },
            { value: 0.10, viewTitle: '+10%' },
            { value: 0.15, viewTitle: '+15%' },
            { value: 0.20, viewTitle: '+20%' },
            { value: 0.25, viewTitle: '+25%' },
            { value: 0.30, viewTitle: '+30%' },
        ],
        levelTitle: 'increasePercentage',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [Buffs.BIZ_TUNING_SHOPS],
        impact: 3,
    },

    [Buffs.BIZ_FUEL_STATIONS_UPGRADE]: {
        name: 'fuel_shop_upgrade',
        icon: 36,
        levels: [
            { value: 0.05, viewTitle: '+5%' },
            { value: 0.10, viewTitle: '+10%' },
            { value: 0.15, viewTitle: '+15%' },
            { value: 0.20, viewTitle: '+20%' },
            { value: 0.25, viewTitle: '+25%' },
            { value: 0.30, viewTitle: '+30%' },
        ],
        levelTitle: 'increasePercentage',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [Buffs.BIZ_FUEL_STATIONS],
        impact: 3,
    },

    [Buffs.BIZ_TATTOO_SHOPS_UPGRADE]: {
        name: 'tattoo_shop_upgrade',
        icon: 35,
        levels: [
            { value: 0.05, viewTitle: '+5%' },
            { value: 0.10, viewTitle: '+10%' },
            { value: 0.15, viewTitle: '+15%' },
            { value: 0.20, viewTitle: '+20%' },
            { value: 0.25, viewTitle: '+25%' },
            { value: 0.30, viewTitle: '+30%' },
        ],
        levelTitle: 'increasePercentage',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [Buffs.BIZ_TATTOO_SHOPS],
        impact: 3,
    },

    [Buffs.BIZ_AUTO_SHOPS_UPGRADE]: {
        name: 'auto_shop_upgrade',
        icon: 42,
        levels: [
            { value: 0.05, viewTitle: '+5%' },
            { value: 0.10, viewTitle: '+10%' },
            { value: 0.15, viewTitle: '+15%' },
            { value: 0.20, viewTitle: '+20%' },
            { value: 0.25, viewTitle: '+25%' },
            { value: 0.30, viewTitle: '+30%' },
        ],
        levelTitle: 'increasePercentage',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [Buffs.BIZ_AUTO_SHOPS],
        impact: 3,
    },

    [Buffs.BIZ_CARWASH_UPGRADE]: {
        name: 'carwash_shop_upgrade',
        icon: 58,
        levels: [
            { value: 0.05, viewTitle: '+5%' },
            { value: 0.10, viewTitle: '+10%' },
            { value: 0.15, viewTitle: '+15%' },
            { value: 0.20, viewTitle: '+20%' },
            { value: 0.25, viewTitle: '+25%' },
            { value: 0.30, viewTitle: '+30%' },
        ],
        levelTitle: 'increasePercentage',
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [Buffs.BIZ_CARWASH],
        impact: 3,
    },

    [Buffs.CRAFT_MILLITARY]: {
        name: 'craft_millitary',
        icon: 6,
        levels: [],
        overtakeReduse: 2,
        maxPerFraction: 1,
        require: [],
        // extraDefence: 1,
        requirePerks: [
            {
                title: 'family.perks.oneOfPerk',
                items: [
                    'vehicleMaterials-bobcatxl',
                    'vehicleMaterials-speedo',
                    'vehicleMaterials-guardian'
                ]
            }
        ],
        itemsInfo: {
            title: 'family.craft.green',
            items: [263, 241, 259, 267, 270, 271, 326, 331],
        },
        //impact: 3,
    },

    [Buffs.CRAFT_MEDICAL]: {
        name: 'craft_medical',
        icon: 5,
        levels: [],
        overtakeReduse: 2,
        maxPerFraction: 1,
        require: [],
        // extraDefence: 1,
        requirePerks: [
            {
                title: 'family.perks.oneOfPerk',
                items: [
                    'vehicleMaterials-bobcatxl',
                    'vehicleMaterials-speedo',
                    'vehicleMaterials-guardian'
                ]
            }
        ],
        itemsInfo: {
            title: 'family.craft.red',
            items: [571, 573],
        },
        //impact: 3,
    },

    [Buffs.CRAFT_SCIENCE]: {
        name: 'craft_science',
        icon: 7,
        levels: [],
        overtakeReduse: 2,
        maxPerFraction: 1,
        require: [],
        // extraDefence: 1,
        requirePerks: [
            {
                title: 'family.perks.oneOfPerk',
                items: [
                    'vehicleMaterials-bobcatxl',
                    'vehicleMaterials-speedo',
                    'vehicleMaterials-guardian'
                ]
            }
        ],
        itemsInfo: {
            title: 'family.craft.blue',
            items: [353, 443, 92],
        },
        //impact: 3,
    },

    [Buffs.CRAFT_MEDICAL_UPGRADE]: {
        name: 'craft_medical_upgrade',
        icon: 44,
        levels: [
            { value: 0.05, viewTitle: '-5%' },
            { value: 0.10, viewTitle: '-10%' },
            { value: 0.15, viewTitle: '-15%' },
            { value: 0.20, viewTitle: '-20%' },
            { value: 0.25, viewTitle: '-25%' },
            { value: 0.30, viewTitle: '-30%' },
        ],
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [Buffs.CRAFT_MEDICAL],
        levelTitle: 'discountPercentage',

        //extraDefence: 1,
        impact: 3,
    },

    [Buffs.CRAFT_MILLITARY_UPGRADE]: {
        name: 'craft_millitary_upgrade',
        icon: 46,
        levels: [
            { value: 0.05, viewTitle: '-5%' },
            { value: 0.10, viewTitle: '-10%' },
            { value: 0.15, viewTitle: '-15%' },
            { value: 0.20, viewTitle: '-20%' },
            { value: 0.25, viewTitle: '-25%' },
            { value: 0.30, viewTitle: '-30%' },
        ],
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [Buffs.CRAFT_MILLITARY],
        levelTitle: 'discountPercentage',
        impact: 3,

        //extraDefence: 1,
    },

    [Buffs.CRAFT_SCIENCE_UPGRADE]: {
        name: 'craft_science_upgrade',
        icon: 45,
        levels: [
            { value: 0.05, viewTitle: '-5%' },
            { value: 0.10, viewTitle: '-10%' },
            { value: 0.15, viewTitle: '-15%' },
            { value: 0.20, viewTitle: '-20%' },
            { value: 0.25, viewTitle: '-25%' },
            { value: 0.30, viewTitle: '-30%' },
        ],
        useTrans: false,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [Buffs.CRAFT_SCIENCE],
        levelTitle: 'discountPercentage',
        impact: 3,

        //extraDefence: 1,
    },

    [Buffs.JOBS_FARMER]: {
        name: 'jobs_farmer',
        icon: 24,

        levels: [
            { value: 1, viewTitle: 'orangesAndWheat' },
            { value: 2, viewTitle: 'potato' },
            { value: 3, viewTitle: 'cabbage' },
            { value: 4, viewTitle: 'corn' },
            { value: 5, viewTitle: 'pumpkin' },
            { value: 6, viewTitle: 'bananas' },
        ],

        useTrans: true,
        levelViewType: 'block',
        levelTitle: 'farm',

        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [],
        extraDefence: 1,
        requirePerks: [
            {
                title: 'family.perks.oneOfPerk',
                items: [
                    'vehicleMaterials-bobcatxl',
                    'vehicleMaterials-speedo',
                    'vehicleMaterials-guardian'
                ]
            }
        ],
        impact: 2,
    },

    [Buffs.JOBS_MINER]: {
        name: 'jobs_miner',
        icon: 30,
        levels: [
            // { value: 0 },
            { value: 1, viewTitle: 'Fe' },
            { value: 2, viewTitle: 'Ag' },
            { value: 3, viewTitle: 'Cu' },
            { value: 4, viewTitle: 'Sn' },
            { value: 5, viewTitle: 'Au' },
            { value: 6, viewTitle: 'Other' },
        ],
        useTrans: true,
        levelViewType: 'block',
        levelTitle: 'miningOre',
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [],
        extraDefence: 1,
        requirePerks: [
            {
                title: 'family.perks.oneOfPerk',
                items: [
                    'vehicleMaterials-bobcatxl',
                    'vehicleMaterials-speedo',
                    'vehicleMaterials-guardian'
                ]
            }
        ],
        impact: 2,
    },

    [Buffs.JOBS_FISHERMAN]: {
        name: 'jobs_fisherman',
        icon: 26,
        levels: [
            { value: 0.003, viewTitle: '0.3%' },
            { value: 0.006, viewTitle: '0.6%' },
            { value: 0.009, viewTitle: '0.9%' },
            { value: 0.012, viewTitle: '1.2%' },
            { value: 0.015, viewTitle: '1.5%' },
            { value: 0.018, viewTitle: '1.8%' },
        ],
        levelTitle: 'productAmount',
        useTrans: false,
        overtakeReduse: 1,
        require: [],
        extraDefence: 1,
        requirePerks: [
            {
                title: 'family.perks.oneOfPerk',
                items: [
                    'vehicleMaterials-bobcatxl',
                    'vehicleMaterials-speedo',
                    'vehicleMaterials-guardian'
                ]
            }
        ],
        impact: 2,
    },

    [Buffs.JOBS_MUSHRUM]: {
        name: 'jobs_mushrum',
        icon: 29,
        levels: [
            { value: 0.003, viewTitle: '0.9%' },
            { value: 0.006, viewTitle: '1.8%' },
            { value: 0.009, viewTitle: '2.7%' },
            { value: 0.012, viewTitle: '3.6%' },
            { value: 0.015, viewTitle: '4.5%' },
            { value: 0.018, viewTitle: '5.4%' },
        ],
        levelTitle: 'productAmount',
        useTrans: false,
        overtakeReduse: 1,
        require: [],
        extraDefence: 1,
        requirePerks: [
            {
                title: 'family.perks.oneOfPerk',
                items: [
                    'vehicleMaterials-bobcatxl',
                    'vehicleMaterials-speedo',
                    'vehicleMaterials-guardian'
                ]
            }
        ],
        impact: 2,
    },

    [Buffs.JOBS_LUMERJACK]: {
        name: 'jobs_wood',
        icon: 27,
        levels: [
            { value: 0.003, viewTitle: '0.6%' },
            { value: 0.006, viewTitle: '1.2%' },
            { value: 0.009, viewTitle: '1.8%' },
            { value: 0.012, viewTitle: '2.4%' },
            { value: 0.015, viewTitle: '3.0%' },
            { value: 0.018, viewTitle: '3.6%' },
        ],
        levelTitle: 'productAmount',
        useTrans: false,
        overtakeReduse: 1,
        require: [],
        extraDefence: 1,
        requirePerks: [
            {
                title: 'family.perks.oneOfPerk',
                items: [
                    'vehicleMaterials-bobcatxl',
                    'vehicleMaterials-speedo',
                    'vehicleMaterials-guardian'
                ]
            }
        ],
        impact: 2,
    },

    [Buffs.CONTRACTS_MONEY]: {
        name: 'contracts_money',
        icon: 8,
        levels: [
            { value: 0.02 },
            { value: 0.04 },
            { value: 0.06 },
            { value: 0.08 },
            { value: 0.10 },
        ],
        overtakeReduse: 2,
        maxPerFraction: 3,
        require: [],
        //extraDefence: 1,
        impact: 3,
    },

    [Buffs.CONTRACTS_REPUTATION_LEGAL]: {
        name: 'contracts_rep_legal',
        icon: 17,
        levels: [
            { value: 0.02 },
            { value: 0.04 },
            { value: 0.06 },
            { value: 0.08 },
            { value: 0.10 },
        ],
        overtakeReduse: 2,
        maxPerFraction: 1,
        require: [],
        //extraDefence: 1,
        impact: 3,
    },

    [Buffs.SABOTAGE_SATELLITE]: {
        name: 'contracts_rep_legal',
        icon: 34,
        levels: [],
        overtakeReduse: 2,
        maxPerFraction: 1,
        require: [],
        effectDuration: 8 * 60 * 60 * 1000,
        effectCooldown: 2 * 24 * 60 * 60 * 1000,
        // effectDuration: 3 * 60 * 1000,
        // effectCooldown: 3 * 60 * 1000,
        impact: 2,
    },

    [Buffs.SABOTAGE_AIRPORT]: {
        name: 'airport',
        icon: 47,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [],
        effectDuration: 10 * 60 * 60 * 1000,
        effectCooldown: 2 * 24 * 60 * 60 * 1000,
        // effectDuration: 3 * 60 * 1000,
        // effectCooldown: 3 * 60 * 1000,
        useTrans: true,
        levels: [
            { value: 1, viewTitle: 'reinforcment' },
        ],
        // extraDefence: 1,
        takeLevel: 1,
        levelTitle: 'action',
        impact: 2,
    },

    [Buffs.SABOTAGE_JET]: {
        name: 'jet',
        icon: 48,
        overtakeReduse: 1,
        maxPerFraction: 1,
        require: [],
        effectDuration: 8 * 60 * 60 * 1000,
        effectCooldown: 2 * 24 * 60 * 60 * 1000,
        useTrans: true,
        levels: [
            { value: 1, viewTitle: 'diversy' },
        ],
        // extraDefence: 1,
        levelTitle: 'action',
        impact: 2,
    },

    [Buffs.ENTERPRISE]: {
        name: 'enterprise',
        icon: 28,
        levels: [],
        overtakeReduse: 2,
        extraDefence: 1,
        maxPerFraction: 2,
        requirePerks: [
            'roomFree',
            {
                title: 'family.perks.oneOfPerk',
                items: [
                    'vehicleMaterials-bobcatxl',
                    'vehicleMaterials-speedo',
                    'vehicleMaterials-guardian'
                ]
            }
        ],
        impact: 3,
    }
});

export const rareSize = 280;

export const zonesPropsHash = Object.freeze({
    zoneId: 0,
    fractionId: 1,
    level: 2,
    actionData: 3, // array
    debuffData: 4, // array
    effectCooldown: 5,
    controllStartTime: 6,
    impactTicks: 7,
});

export const actionDataPropsHash = Object.freeze({
    // type: 0,
    startTime: 0,
    attackerId: 1,
    defenderId: 2,
    peopleCount: 3,
    caliberId: 4,
    attackerName: 5,
});

export const actionTypePropsHash = Object.freeze({
    attack: 1,
    defence: 2,
    anotherAttack: 3,
});

export const debuffPropsHash = Object.freeze({
    fromZoneId: 0,
    endTime: 1,
});

export const warehouseSubtypes = [
    Buffs.JOBS_FARMER,
    Buffs.JOBS_FISHERMAN,
    Buffs.JOBS_LUMERJACK,
    Buffs.JOBS_MINER,
    Buffs.JOBS_MUSHRUM,
    Buffs.OILRIG,
    Buffs.DRUG
];

export const craftSubtypes = [
    Buffs.CRAFT_MEDICAL,
    Buffs.CRAFT_MILLITARY,
    Buffs.CRAFT_SCIENCE
];

export const mapBizPrices = {
    [Buffs.BIZ_TUNING_SHOPS]: {
        1: 2_835,
        2: 2_457,
        3: 2_457,
        4: 1_953,
        5: 1_575,
        6: 2_457,
    },

    [Buffs.BIZ_ITEM_SHOPS]: {
        1: 2_016,
        2: 1_736,
        3: 2_184,
        4: 1_904,
        5: 1_904,
        6: 2_576,
        7: 2_296,
        8: 2_184,
        9: 2_240,
        10: 1_680,
        11: 2_632,
        12: 1_848,
        13: 2_576,
        14: 2_184,
        15: 2_352,
        16: 2_351,
        17: 2_576,
        18: 1_736,
        19: 1_960,
        20: 2_464,
        21: 1_344,
        22: 2_296,
    },

    [Buffs.BIZ_AMMO_SHOPS]: {
        1:  2_205,
        2:  1_813,
        3:  2_254,
        4:  1_568,
        5:  1_862,
        6:  1_813,
        7:  2_254,
        8:  2_156,
        9:  2_303,
        10: 1_666,
        11: 1_764,
    },

    [Buffs.BIZ_TATTOO_SHOPS]: {
        1: 630,
        2: 1_890,
        3: 1_680,
        4: 1_680,
        5: 1_050,
        6: 1_638,
    },

    [Buffs.BIZ_CARWASH]: {
        1: 945,
        2: 1_505,
        3: 910,
        4: 490,
        5: 1_295,
    },

    [Buffs.BIZ_FUEL_STATIONS]: {
       1:  1_120,
       2:  1_008,
       3:  952,
       4:  1_120,
       5:  1_260,
       6:  1_260,
       7:  952,
       8:  1_008,
       9:  532,
       10: 980,
       11: 1_400,
       12: 700,
       13: 784,
       14: 1_036,
       15: 1_176,
       16: 1_176,
       17: 1_008,
       18: 1_204,
       19: 896,
       20: 1_120,
       21: 896,
       22: 1_400,
       23: 924,
       24: 840,
       25: 1_400,
       26: 812,
       27: 980,
       28: 1_064,
    },

    [Buffs.BIZ_CLOTHES_SHOPS]: {
        1: 882,
        2: 882,
        3: 273,
        4: 903,
        5: 882,
        6: 966,
        7: 588,
        8: 420,
        9: 777,
        10: 945,
        11: 798,
        12: 609,
        13: 756,
        14: 840,
        15: 903,
    },

    [Buffs.BIZ_AUTO_SHOPS]: {
        1: 756,
        2: 392,
        3: 700,
        4: 294,
        5: 602,
        6: 700,
        7: 504,
        8: 448,
        9: 700,
        10: 574,
    },

    [Buffs.BIZ_BARBER_SHOPS]: {
        1: 245,
        2: 245,
        3: 294,
        4: 245,
        5: 231,
        6: 315,
        7: 224,
    }
}
