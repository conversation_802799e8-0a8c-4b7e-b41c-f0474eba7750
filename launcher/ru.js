export default {
    launcher: {
        'servers-list': 'Список серверов',
        connection: 'Соединение',
        milliseconds: 'мс',
        play: 'Играть',
        notifications: {
            error: 'Ошибка',
            warning: 'Внимание',
            success: 'Успешно',
            mod: {
                uninstall: {
                    title: 'Успешно',
                    text: 'Мод успешно удален!',
                },
            },
            cache: {
                clear: {
                    title: 'Успешно',
                    text: 'Кэш успешно очищен',
                },
            },
            'permissions-fix': {
                title: 'Успешно',
                text: 'Права доступа к файлам игры успешно исправлены',
            },
            account: {
                loginError: {
                    required: {
                        title: 'Ошибка',
                        text: 'Необходимо ввести Логин!',
                    },
                    minLength: {
                        title: 'Ош<PERSON>бка',
                        text: 'Указанный Логин слишком короткий!',
                    },
                    maxLength: {
                        title: 'О<PERSON><PERSON><PERSON><PERSON>а',
                        text: 'Указанный Логин слишком длинный!',
                    },
                },
                emailError: {
                    email: {
                        title: 'Ошибка',
                        text: 'Некорректный Email!',
                    },
                    required: {
                        title: 'Ошибка',
                        text: 'Необходимо ввести Email!',
                    },
                    minLength: {
                        title: 'Ошибка',
                        text: 'Указанный Email слишком короткий!',
                    },
                    maxLength: {
                        title: 'Ошибка',
                        text: 'Указанный Email слишком длинный!',
                    },
                },
                passwordError: {
                    required: {
                        title: 'Ошибка',
                        text: 'Необходимо ввести пароль!',
                    },
                    minLength: {
                        title: 'Ошибка',
                        text: 'Указанный пароль слишком короткий!',
                    },
                    maxLength: {
                        title: 'Ошибка',
                        text: 'Указанный пароль слишком длинный!',
                    },
                    sameAs: {
                        title: 'Ошибка',
                        text: 'Пароли не совпадают!',
                    },
                },
                forgotPassword: {
                    success: {
                        title: 'Успешно',
                        text: 'Вы успешно сменили пароль!',
                    },
                    codeError: {
                        title: 'Ошибка',
                        text: 'Некорректный код!',
                    },
                },
            },
            installGame: {
                badPath: {
                    title: 'Ошибка',
                    text: 'Некорректный путь!',
                },
            },
            getGift: {
                'select-server': 'Выберите сервер!',
            },
            reverifyFiles: {
                text: 'При следующем запуске игры, все файлы будут автоматически проверены на целостность.',
            },
            gameAlive: {
                title: 'Успешно',
                text: 'Вы успешно закрыли процессы игры!'
            }
        },
        modal: {
            close: 'Закрыть',
            connect: {
                title: 'ручное подключение',
                ip: 'Адрес сервера',
                password: 'Пароль (если есть)',
                text: 'Адрес сервера должен быть в формате <mark>address:port</mark><br/>Например: <mark>127.0.0.1:7788</mark> или <mark>example.server.com:443</mark>',
                discard: 'Отмена',
                connect: 'Подключиться',
            },
            runGameError: {
                title: 'Не удалось запустить игру',
                'reason-title': 'Причина',
                'possibleSolutions-title': 'Возможные решения',

                1000: {
                    reason: 'Не удалось запустить файл мультиплеера',
                    possibleSolutions: {
                        tryAgain: 'Попробуйте еще раз нажать играть',
                        reboot: 'Попробуйте перезагрузить компьютер',
                    },
                },
            },
            installGameError: {
                title: 'Не удалось установить игру',
                'reason-title': 'Причина',
                'possibleSolutions-title': 'Возможные решения',

                1001: {
                    reason: 'Не удалось получить доступ к директории игры',
                    possibleSolutions: {
                        runAsAdmin: 'Попробуйте запустить лаунчер от имени администратора',
                        changeAccess: 'Попробуйте изменить права доступа к директории игры "Games"',
                        reboot: 'Попробуйте перезагрузить компьютер',
                    },
                },
            },
            startGameError: {
                title: 'Ошибка при внедрении патчей',
                'reason-title': 'Причина',
                'possibleSolutions-title': 'Возможные решения',

                1001: {
                    reason: 'Не удалось внедрить патчи в игру',
                    possibleSolutions: {
                        runAsAdmin: 'Попробуйте запустить лаунчер от имени администратора',
                        changeAccess: 'Попробуйте запустить Rockstar Games Launcher отдельно',
                        reboot: 'Попробуйте перезагрузить компьютер',
                    },
                },
            },
            notEnoughDiskSpace: {
                title: 'Недостаточно места на диске',
                'reason-title': 'Причина',
                'possibleSolutions-title': 'Возможные решения',

                1002: {
                    reason: 'На диске {disk} свободно {freeSpace}',
                    reason2: 'Требуется минимум {requiredSpace}',
                    possibleSolutions: {
                        freeSpace: 'Освободите место на диске {disk}',
                    },
                },
            },
            enchancedGTAV: {
                title: 'Неправильная версия GTA V',
                'reason-title': 'Причина',
                'possibleSolutions-title': 'Возможные решения',

                1003: {
                    reason: 'Вы используете GTA V Enchanced вместо GTA V Legacy',
                    possibleSolutions: {
                        steam_gtaLegacy: 'Если вы используете Steam, то вам нужно скачать Grand Theft Auto V Legacy',
                        epic_gtaLegacy: 'Если вы используете Epic Games, то вам нужно скачать обычную Grand Theft Auto V',
                        rgl_gtaLegacy: 'Если вы используете Rockstar Games Launcher, то вам нужно скачать Grand Theft Auto V Legacy',
                    },
                },
            },
            getGift: {
                'choose-server': 'Выберите сервер',
                description: 'Подарочный предмет доступен на всех персонажах одного сервера по вашему выбору',
                get: 'Получить',
                close: 'Закрыть',
                success: 'Вы успешно забрали подарок!',
                error: {
                    1315: 'Не так часто!',
                    1101: 'Вы не зашли в игру с лаунчера!',
                    1317: 'Вы уже получили подарок на этом сервере!',
                    default: 'Неизвестная ошибка',
                },
            },
            BuyingKey: {
                buyingKey: 'Купить ключ GTA V Legacy',
                secure: 'Безопасно',
                'text-ru':
                    'Страница с ключом для игры Grand Theft Auto V Legacy в магазине Rockstar будет открыта вскоре после оплаты.',
                text: 'Требуется лицензионная версия Grand Theft Auto V Legacy. <br /> Приобретите её в одном из официальных магазинов:',
                'path-title': 'Укажите путь к игре',
                'click-to-choose': 'Выберите папку с игрой',
                'successful-select': 'Путь выбран успешно!',
                'enter-email': 'Введите ваш Email',
                selectPayment: 'Выберите способ оплаты',
                'user-agreement': 'Я ознакомился с <span id="rulesUrl">условиями оферты</span>',
                continue: 'Продолжить',
                cancel: 'Отмена',
            },
            noGTA: {
                gtaNotFound: 'GTA V Legacy не найдена',
                youHaveGTA: 'У вас куплена GTA V Legacy?',
                text: 'Для игры на Majestic требуется лицензионная версия игры Grand Theft Auto V Legacy. Ключ игры можно быстро и удобно купить в нашем магазине.',
                buy: 'нет, купить',
                have: 'да, есть',
                title: 'GTA V Legacy не найдена',
                'path-title': 'Укажите путь к игре',
                'click-to-choose': 'Выберите папку с игрой',
                'successful-select': 'Путь выбран успешно!',
            },
            selectGTA: {
                gtaNotFound: 'Выберите путь к GTA V Legacy',
                text: 'Для игры на Majestic требуется лицензионная версия игры Grand Theft Auto V Legacy. Выберите путь к игре на вашем компьютере.',
                title: 'Выберите путь к GTA V Legacy',
                'path-title': 'Укажите путь к игре',
                'click-to-choose': 'Выберите папку с игрой',
            },
            rageUndefined: {
                HDD: 'Жесткий диск',
                available: 'Доступно',
                'game-files': 'ИГРОВЫЕ ФАЙЛЫ',
                'required-space': 'Потребуется около {size} ГБ свободного места',
                'choose-disk-for-installation': 'Выберите диск для установки игровых файлов',
                'searching-HDD': 'Поиск жесткого диска...',
                install: 'Установить',
                set: 'Сменить',
            },
            remove: {
                title: 'Подтвердите удаление',
                confirmation: 'Вы точно хотите удалить <span class="value">{title}</span>?',
                accept: 'Да, удалить',
                cancel: 'Я передумал',
            },
            failPayment: {
                error: 'Ошибка',
                description:
                    'К сожалению, произошла ошибка <br/> при обработке вашего платежа. <br/> <br/> Пожалуйста, попробуйте ещё раз <br/> или <span id="supportUrl">обратитесь в службу поддержки</span>.',
                back: 'Назад',
                'to-main-page': 'На главную страницу',
                payment: 'Оплата',
            },
            successPayment: {
                success: 'Успешно',
                'replenish-account': 'Вы пополнили счёт!',
                'replenish-account-on': 'Вы пополнили счёт на <span class="amount">{amount}</span>',
                'funds-credited-account': 'Средства будут зачислены на ваш <br/> аккаунт в ближайшее время',
                'to-main-page': 'На главную страницу',
                back: 'Назад',
                payment: 'Оплата',
            },
            RegionSelector: {
                selectRegion: 'Выберите регион',
                discription:
                    'От него зависят доступные сервера. Вы можете изменить свой выбор в любой момент в настройках.',
                avaiableServers: 'Доступные сервера',
                playStart: 'Начать игру',

                regions: {
                    eu: 'Глобальный',
                    ru: 'СНГ',
                    test: 'Тестовый',
                },
            },
            unknownBranch: {
                title: 'Не удалось найти тестовую ветку',
                description: 'При попытке скачать ветку {value} произошла ошибка, убедитесь что выбранная ветка существует и валидная'
            },
            incorrectToken: {
                title: 'Произошла ошибка во время скачивания',
                description: 'Не удалось скачать выбранную тестовую ветку, проверьте ваш CDN токен на валидность и повторите попытку'
            },
            gameAlive: {
                title: 'Игра уже запущена',
                confirmation: 'Хотите ли закрыть игру перед повторным запуском?',
                accept: 'Да, хочу',
                cancel: 'Отмена',
            }
        },
        updater: {
            uploadedSize: '{uploadedSize} ГБ из {totalDownloadSize} ГБ',
            speed: '{downloadSpeed} МБ/с',
            leftTime: 'Осталось примерно {leftTime}',
            seconds: '{value} {value, plural, one{секунда} few{секунды} many{секунд} other {секунд}}',
            minutes: '{value} {value, plural, one{минута} few{минуты} many{минут} other {минут}}',
            'checking-for-updates': 'Проверка обновлений',
            updating: 'Загрузка обновлений',
        },
        loader: {
            cancel: 'Отмена',
            titles: {
                game: {
                    install: 'Установка файлов игры',
                    verifying: 'Проверка файлов',
                    starting: 'Запуск и патч игры',
                    backup: 'Резервное копирование файлов игры',
                    resolving: 'Обработка данных'
                },
                mods: {
                    install: 'Установка модификации {mod}',
                },
            },
            info: {
                speed: '{uploadedSize} ГБ из {totalDownloadSize} ГБ',
                leftTime: 'Осталось примерно {leftTime}',
                downloadSpeed: '{value} МБ/с',
                seconds: '{value} {value, plural, one{секунда} few{секунды} many{секунд} other {секунд}}',
                minutes: '{value} {value, plural, one{минута} few{минуты} many{минут} other {минут}}',
            },
        },
        navbar: {
            play: 'Играть',
            connect: 'Ввести IP',
            donate: 'Магазин',
            news: 'Новости',
            forum: 'Форум',
            mods: 'Моды',
            settings: 'Настройки',
            account: {
                signIn: 'Войти',
                signUp: 'Регистрация',
                profileConfiguration: 'Настройки профиля',
                signOut: 'Выйти из аккаунта',
            },
            comingSoon: 'Скоро',
        },
        servers: {
            server: 'Сервер',
            polski: 'Польский',
            deutsch: 'Немецкий',
            majestic: 'Majestic',
            'play-now': 'Сейчас играют',
            'recommend-for-beginners': 'Cоветуем для новичков',
            'last-time-visited': 'Заходили в последний раз',
            'all-servers': 'Все сервера',
            'in-other-languages': 'На других языках',
            tooltip: {
                techWorks: 'На сервере ведутся технические работы',
                seasonPassMultiplier: 'Получаемый опыт зимнего пропуска увеличен на {value}%',
            },
            advantages: {
                gift: 'Подарок',
                newServer: 'Новый',
                experience: 'X{value} Опыта',
                seasonPassMultiplier: 'X{value}',
            },
        },
        select: {
            'enter-value': 'Введите значение...',
        },
        donate: {
            back: 'Назад',
            'buy-coins': 'Купить валюту',
            cashbox: 'Покупка коинов для логина {login} на {server} сервере',
            'choose-server': 'Выберите сервер',
            'enter-login': 'Введите ваш логин',
            'enter-sum': 'Введите сумму',
            'enter-email': 'Введите email адрес',
            'min-bonus-value': 'от {from}р.',
            pay: 'Перейти к оплате',
            'rules-checked': 'Я ознакомился с <span id="rulesUrl">условиями оферты</span>',
            'rules-agree':
                'Я даю согласие на то, чтобы Majestic RP немедленно начала выполнять контракт, активировав цифровой контент (включая цифровую валюту и любой контент, активированный с использованием цифровой валюты) в моей учетной записи. Я принимаю, что теряю право расторгнуть этот договор и получить возмещение после активации цифрового контента в моей учетной записи.',
            info: 'Информация',
            exchange: 'Курс валют: 1р.',
            description:
                'За Majestic Coins можно приобрести: уникальный транспорт, недвижимость, бизнес, игровую валюту, и многое другое. После оплаты MC будут начислены на ваш счёт на игровом сервере автоматически.',
            warning: 'В случае, если Вы неправильно ввели данные, перевода средств на нужный аккаунт не будет.',
            'contract-offer': 'Договор оферты',
            selectPayment: 'Выберите способ оплаты',
            selectPaymentOther: 'Другие способы оплаты',
            replenish: {
                cardTitles: {
                    card_ru1: 'Карты банков России 1',
                    crypto: 'Другие криптовалюты',
                    erc20: 'USDT Tether (ERC20)',
                    trc20: 'USDT Tether (TRC20)',
                    kinguin: 'Ключи активации',
                    steam: 'Оплата скинами Steam',
                },
                titles: {
                    card_ru1: 'Карты банков России 1',
                    card_ru2: 'Карты банков России 2',
                    card_ru3: 'Карты банков России 3',
                    sbp: 'Система Быстрых Платежей',
                    qiwi: 'Qiwi',
                    yandexpay: 'Yandex Pay',
                    card_ua: 'Карты банков Украины',
                    card_world1: 'Иностранные карты 1',
                    card_world2: 'Иностранные карты 2',
                    paypal: 'PayPal',
                    kinguin: 'Ключи активации Kinguin',
                    steam: 'Предметами из Steam',
                    yoomoney: 'Юmoney',
                    bitcoin: 'Bitcoin',
                    ethereum: 'Ethereum',
                    trc20: 'USDT Tether (TRC20)',
                    erc20: 'USDT Tether (ERC20)',
                    crypto: 'Другие криптовалюты',
                    direct_banking: 'Оплата с Банковского счета',
                    cards_eu: 'Оплата картой 1',
                    paysafecard: 'paysafecard',
                    blik: 'blik',
                    paypalych: 'Оплата картой 2',
                    dolyame: 'Оплата долями',
                },
            },
        },
        settings: {
            'on-main': 'На главную',
            categories: {
                main: 'Основное',
                patch: 'Патчи',
                additional: 'Дополнительно',
                developer: 'Разработчик',
            },
            descriptions: {
                shadowplayOverlay:
                    'Данная функция позволяет в игре записывать видео и снимать скриншоты. <br> Если вы испытываете проблемы с игрой <span class="highlight">(вылеты, зависания и т.д.)</span>, попробуйте отключить эту функцию.',
                discordOverlay:
                    'Данная функция позволяет в игре открывать Discord меню. <br> Если вы испытываете проблемы с игрой <span class="highlight">(вылеты, зависания и т.д.)</span>, попробуйте отключить эту функцию.',
                devSettings:
                    'Подсказка: Чтобы сохранить данные в поле ввода, нажмите ENTER'
            },
            actions: {
                fix: 'Починить',
                clearCache: 'Очистить',
                changeMAJESTICFolder: 'Сменить',
                changeGTAFolder: 'Сменить',
                fixPermissions: 'Починить',
                reverifyFiles: 'Проверить',
            },
            titles: {
                region: 'Регион',
                branch: 'Ветка',
                cdnToken: 'CDN Токен',
                enableDebug: 'Включить отладку',
                skipBranchCheck: 'Пропустить проверку ветки',
                language: 'Язык',
                interfaceVolume: 'Громкость интерфейса',
                hideAfterBootGame: 'Сворачивать лаунчер после запуска игры',
                downloadInLauncher: 'Скачивать файлы игры в лаунчере',
                enableCEFGPU: 'Аппаратное ускорение интерфейса',
                patchGTA: 'Применять патч для игры',
                clearCache: 'Очистить игровой кэш',
                forceClearCache: 'Полностью очистить игровой кэш',
                changeMAJESTICFolder: 'Сменить место установки Majestic RP',
                changeGTAFolder: 'Сменить место установки GTA V Legacy',
                fixPermissions: 'Починить права доступа к файлам игры',
                reverifyFiles: 'Принудительно проверить файлы игры',
                shadowplayOverlay: 'Включить NVIDIA Shadowplay Overlay',
                discordOverlay: 'Включить Discord Overlay',
                pass: 'Пароль',
                range: 'Рэнж',
            },
            placeholders: {
                enterPass: 'Введите пароль',
            },
        },
        account: {
            signIn: {
                logInAccount: 'Войти в аккаунт',
                signIn: 'Войти',
                signUp: 'Регистрация',
                enterEmail: 'Введите email',
                enterPass: 'Введите пароль',
                forgotPassword: 'Забыли пароль?',
            },
            signUp: {
                signUp: 'Регистрация',
                enterLogin: 'Введите логин',
                enterEmail: 'Введите email',
                enterPass: 'Введите пароль',
                repeatPass: 'Повторите пароль',
                alreadyCreateAccount: 'Уже есть аккаунт?',
                'agree-newsletters': 'Я согласен принимать информационные рассылки',
                'accept-rules': 'Я ознакомился с <span id="rulesUrl">правилами сервера</span> и принимаю их',
                'confirm-age': 'Я подтверждаю, что мне есть 18 лет',
            },
            profileConfiguration: {},
            forgotPassword: {
                passwordReset: 'Cброс пароля',
                enterEmail: 'Введите email',
                enterPass: 'Введите пароль',
                repeatPass: 'Повторите пароль',
                continue: 'Продолжить',
                cancel: 'Отмена',
                enterCode: 'Введите  код, отправленный на почту ',
            },
        },
        mods: {
            sort: 'Сортировка',
            sortItems: {
                graphics: 'Графика',
                interface: 'Интерфейс',
                sounds: 'Звуки',
                other: 'Другое',
            },
            install: 'Установить',
            uninstall: 'Удалить',
            more: 'Подробнее',
            back: 'Назад',
            empty: 'Тут пока что ничего нет, Следите за новостями чтобы не пропустить новые моды',
            systemRequirements: 'Системные требования',
            operatingSystem: 'Операционная система',
            processor: 'Процессор',
            ram: 'Оперативная память',
            videoCard: 'Видеокарта',
            soundCard: 'Звуковая карта',
            freeDiskSpace: 'Свободное место на жестком диске',
            berkleyRedux: {
                title: 'Berkley Redux',
                description: 'Бесплатная графическая модификация от Berkley на основе Redux.',
            },
            evolvedGraphics: {
                title: 'Evolved Graphics',
                description:
                    'Глобальный мод, улучшающий графику до уровня современных игр. Игровое освещение, погода, эффекты и постобработка возведены до совершенства, а благодаря графическим эффектам ENB картинка в игре становится чёткой и фотореалистичной.',
            },
            atlasPopularPlaces: {
                title: 'Атлас c популярными местами',
                description:
                    'Поверх карты наложены местоположения зелёных зон, названия популярных мест и многое другое',
            },
            loadingScreens: {
                title: 'Загрузочные экраны',
                description: '',
            },
            strongMotionBlur: {
                title: 'Сильное размытие в движении',
                description: 'Для любителей кинематографичной картинки',
            },
            purpleTracer: {
                title: 'фиолетовый трейсер',
                description:
                    'The Show Must Go On! Теперь при стрельбе из оружия появляются яркие эффекты трассирующих патронов.',
            },
        },
        discord: {
            state: 'Играет на Majestic',
            details: 'Используя лаунчер',
        },
        tray: {
            open: 'Открыть',
            close: 'Закрыть',
        },
        gift: {
            'pickUp-gift': 'Не забудь забрать свой подарок!',
            get: 'Получить',
        },
        competition: {
            title: 'Разыгрываем',
            'sub-title': 'Настоящую BMW M3',
            participate: 'Участвовать',
        },
        crash: {
            title: 'критическая ошибка',
            problemTitle: 'Если проблема повторяется, попробуйте следующие шаги:',
            sol1: 'Перезапустите Majestic RP или персональный компьютер',
            sol2: 'Запустите Majestic RP от имени администратора',
            sol3: 'Проверьте целостность фаилов игры',
            probt1: 'Не помогает? Проверьте нашу страницу',
            probt2: 'частых проблем',
            probt3: 'или',
            probt4: 'обратитесь в поддержку',
            crashId: 'ID краша:',
            errorCode: 'Код ошибки:',
            logDir: 'Папка с логами',
            gtavDir: 'Папка GTA V',
            crashReport: 'Файл краш-репорта',
            exit: 'выйти',
            reload: 'перезапустить',
            crashIdCopy: 'ID краша скопирован в буфер обмена',
            errorCodeCopy: 'Код ошибки скопирован в буфер обмена',
        }
    },
};
