export default {
    launcher: {
        'servers-list': 'Lista de servidores',
        connection: 'Conexión',
        milliseconds: 'ms',
        play: 'Jugar',
        notifications: {
            error: 'Error',
            warning: 'Atención',
            success: 'Éxito',
            mod: {
                uninstall: {
                    title: '<PERSON>xito',
                    text: '¡Mod eliminado con éxito!'
                }
            },
            cache: {
                clear: {
                    title: 'Éxito',
                    text: 'Caché borrada correctamente'
                }
            },
            'permissions-fix': {
                title: 'Con éxito',
                text: 'Permisos de archivos de juego arreglados con éxito'
            },
            account: {
                loginError: {
                    required: {
                        title: 'Error',
                        text: '¡El inicio de sesión es obligatorio!'
                    },
                    minLength: {
                        title: 'Error',
                        text: '¡El inicio de sesión especificado es demasiado corto!'
                    },
                    maxLength: {
                        title: 'Error',
                        text: '¡El inicio de sesión especificado es demasiado largo!'
                    }
                },
                emailError: {
                    email: {
                        title: 'Error',
                        text: '¡Correo electrónico no válido!'
                    },
                    required: {
                        title: 'Error',
                        text: '¡El correo electrónico es obligatorio!'
                    },
                    minLength: {
                        title: 'Error',
                        text: '¡El correo electrónico especificado es demasiado corto!'
                    },
                    maxLength: {
                        title: 'Error',
                        text: '¡El correo electrónico especificado es demasiado largo!'
                    }
                },
                passwordError: {
                    required: {
                        title: 'Error',
                        text: '¡La contraseña es obligatoria!'
                    },
                    minLength: {
                        title: 'Error',
                        text: '¡La contraseña que has introducido es demasiado corta!'
                    },
                    maxLength: {
                        title: 'Error',
                        text: '¡La contraseña especificada es demasiado larga!'
                    },
                    sameAs: {
                        title: 'Error',
                        text: '¡Las contraseñas no coinciden!'
                    }
                },
                forgotPassword: {
                    success: {
                        title: 'Éxito',
                        text: 'Ha cambiado la contraseña con éxito!'
                    },
                    codeError: {
                        title: 'Error',
                        text: '¡Código no válido!'
                    }
                }
            },
            installGame: {
                badPath: {
                    title: 'Error',
                    text: '¡Ruta no válida!'
                }
            },
            getGift: { 'select-server': '¡Elige un servidor!' },
            reverifyFiles: { text: 'La próxima vez que ejecutes el juego, se comprobará automáticamente la integridad de todos los archivos.' },
            gameAlive: {
                title: 'Éxito',
                text: '¡Has cerrado correctamente los procesos del juego!'
            }
        },
        modal: {
            close: 'Cerrar',
            connect: {
                title: 'conexión manual',
                ip: 'Dirección del servidor',
                password: 'Contraseña (si está disponible)',
                text: 'La dirección del servidor debe tener el formato <mark>dirección:puerto</mark><br/>Por ejemplo: <mark>*********.1:7788</mark> o <mark>ejemplo.servidor.com:443</mark>',
                discard: 'Cancelar',
                connect: 'Conecta'
            },
            runGameError: {
                title: 'Error al iniciar el juego',
                'reason-title': 'Razón',
                'possibleSolutions-title': 'Posibles soluciones',
                1000: {
                    reason: 'Error al ejecutar el archivo multijugador',
                    possibleSolutions: {
                        tryAgain: 'Intenta hacer clic en reproducir de nuevo',
                        reboot: 'Intenta reiniciar tu ordenador'
                    }
                }
            },
            installGameError: {
                title: 'Error al instalar el juego',
                'reason-title': 'Razón',
                'possibleSolutions-title': 'Posibles soluciones',
                1001: {
                    reason: 'Error al acceder al directorio del juego',
                    possibleSolutions: {
                        runAsAdmin: 'Intenta ejecutar el Lanzador como administrador',
                        changeAccess: 'Prueba a cambiar los permisos del directorio del juego "Juegos".',
                        reboot: 'Prueba a reiniciar el ordenador'
                    }
                }
            },
            startGameError: {
                title: 'Error al aplicar parches',
                'reason-title': 'Razón',
                'possibleSolutions-title': 'Posibles soluciones',
                1001: {
                    reason: 'No se aplicaron los parches en el juego',
                    possibleSolutions: {
                        runAsAdmin: 'Intenta ejecutar el Lanzador como administrador',
                        changeAccess: 'Prueba a ejecutar Rockstar Games Launcher por separado',
                        reboot: 'Intenta reiniciar tu ordenador'
                    }
                }
            },
            notEnoughDiskSpace: {
                title: 'Espacio discal insuficiente',
                'reason-title': 'Razón',
                'possibleSolutions-title': 'Posibles soluciones',
                1002: {
                    reason: '{disk} {freeSpace}El disco es gratuito',
                    reason2: '{requiredSpace}Mínimo requerido',
                    possibleSolutions: { freeSpace: '{disk}Liberar espacio en disco' }
                }
            },
            enchancedGTAV: {
                title: 'Versión incorrecta de GTA V',
                'reason-title': 'Razón',
                'possibleSolutions-title': 'Posibles soluciones',
                1003: {
                    reason: 'Estás utilizando GTA V Enchanced en lugar de GTA V Legacy',
                    possibleSolutions: {
                        steam_gtaLegacy: 'Si utilizas Steam, tendrás que descargar Grand Theft Auto V Legacy',
                        epic_gtaLegacy: 'Si utilizas Epic Games, tendrás que descargar la versión normal de Grand Theft Auto V',
                        rgl_gtaLegacy: 'Si utilizas Rockstar Games Launcher, tendrás que descargar Grand Theft Auto V Legacy'
                    }
                }
            },
            getGift: {
                'choose-server': 'Elige un servidor',
                description: 'Artículo de regalo disponible en todos los personajes de un servidor de su elección',
                get: 'Reclamar',
                close: 'Cerrar',
                success: '¡Has recogido con éxito tu regalo!',
                error: {
                    1315: '¡No tan a menudo!',
                    1101: '¡No entraste en el juego desde el Lanzador!',
                    1317: '¡Ya has recibido un regalo en este servidor!',
                    default: 'Error desconocido'
                }
            },
            BuyingKey: {
                buyingKey: 'Comprar GTA V Legacy key',
                secure: 'Seguro',
                'text-ru': 'La página con la clave para el juego Grand Theft Auto V Legacy en la tienda Rockstar se abrirá poco después del pago.',
                text: 'Se necesita una versión con licencia de Grand Theft Auto V Legacy. <br /> Cómprala en una de las tiendas oficiales:',
                'path-title': 'Especifica la ruta al juego',
                'click-to-choose': 'Selecciona la carpeta con el juego',
                'successful-select': '¡El camino ha sido elegido con éxito!',
                'enter-email': 'Introduce tu correo electrónico',
                selectPayment: 'Elige el método de pago',
                'user-agreement': 'He leído <span id="rulesUrl">términos y condiciones de la oferta</span>',
                continue: 'Continuar',
                cancel: 'Cancelar'
            },
            noGTA: {
                gtaNotFound: 'No se ha encontrado el legado de GTA V',
                youHaveGTA: '¿Has comprado GTA V Legacy?',
                text: 'Para jugar a Majestic se necesita una versión con licencia de Grand Theft Auto V Legacy. La clave del juego puede adquirirse rápida y cómodamente en nuestra tienda.',
                buy: 'no, comprar',
                have: 'sí, la tengo',
                title: 'No se ha encontrado el legado de GTA V',
                'path-title': 'Especifica la ruta al juego',
                'click-to-choose': 'Selecciona la carpeta con el juego',
                'successful-select': '¡El camino ha sido elegido con éxito!'
            },
            selectGTA: {
                gtaNotFound: 'Selecciona la ruta a GTA V Legacy',
                text: 'Jugar a Majestic requiere una versión con licencia de Grand Theft Auto V Legacy. Selecciona la ruta al juego en tu ordenador.',
                title: 'Selecciona la ruta a GTA V Legacy',
                'path-title': 'Especifica la ruta al juego',
                'click-to-choose': 'Selecciona la carpeta con el juego'
            },
            rageUndefined: {
                HDD: 'Disco duro',
                available: 'Disponible',
                'game-files': 'ARCHIVOS DEL JUEGO',
                'required-space': 'Requerirá unos {size} GB de espacio libre',
                'choose-disk-for-installation': 'Escoge el disco para instalar los archivos del juego',
                'searching-HDD': 'Buscando el disco duro...',
                install: 'Instalar',
                set: 'Cambiar'
            },
            remove: {
                title: 'Confirmar eliminación',
                confirmation: '¿Está seguro de que desea eliminar <span class="value">{title}</span>?',
                accept: 'Sí, eliminar',
                cancel: 'He cambiado de opinión'
            },
            failPayment: {
                error: 'Error',
                description: 'Lo sentimos, se ha producido un error <br/> al procesar tu pago. <br/> <br/> Inténtalo de nuevo <br/> o <span id="supportUrl">ponte en contacto con el servicio de asistencia</span>.',
                back: 'Volver atrás',
                'to-main-page': 'A la página principal',
                payment: 'Pago'
            },
            successPayment: {
                success: 'Éxito',
                'replenish-account': '¡Has reabastecido tu cuenta!',
                'replenish-account-on': 'Has reabastecido tu cuenta antes del <span class="amount">{amount}</span>',
                'funds-credited-account': 'Los fondos se acreditarán en su cuenta <br/> en breve',
                'to-main-page': 'A la página principal',
                back: 'Volver atrás',
                payment: 'Pago'
            },
            RegionSelector: {
                selectRegion: 'Selecciona una región',
                discription: 'Los servidores disponibles dependen de ello. Puedes cambiar tu elección en cualquier momento en los ajustes.',
                avaiableServers: 'Servidores disponibles',
                playStart: 'Iniciar el juego',
                regions: {
                    eu: 'global',
                    ru: 'CIS',
                    test: 'Prueba'
                }
            },
            unknownBranch: {
                title: 'No se ha podido encontrar una rama de prueba',
                description: 'Se ha producido un error al intentar descargar la rama {value} , asegúrate de que la rama seleccionada existe y es válida.'
            },
            incorrectToken: {
                title: 'Se ha producido un error durante la descarga',
                description: 'No se ha podido descargar la rama de prueba seleccionada, comprueba la validez de tu token CDN e inténtalo de nuevo'
            },
            gameAlive: {
                title: 'El juego ya se ha lanzado',
                confirmation: '¿Quieres cerrar el juego antes de reiniciarlo?',
                accept: 'Sí, lo sé.',
                cancel: 'Cancelar'
            }
        },
        updater: {
            uploadedSize: '{uploadedSize} GB de {totalDownloadSize} GB',
            speed: '{downloadSpeed} MB/s',
            leftTime: 'Resto aproximadamente {leftTime}',
            seconds: '{value} {value, plural, one{segundos} few{segundos} many{segundos} other {segundos}}',
            minutes: '{value} {value, plural, one{minuto} few{minutos} many{minutos} other {minutos}}',
            'checking-for-updates': 'Buscar actualizaciones',
            updating: 'Descargar actualizaciones'
        },
        loader: {
            cancel: 'Cancelar',
            titles: {
                game: {
                    install: 'Instalación de archivos de juego',
                    verifying: 'Comprobando archivos',
                    starting: 'Iniciar y parchear el juego',
                    backup: 'Copia de seguridad de los archivos del juego',
                    resolving: 'Tratamiento de datos'
                },
                mods: { install: 'Instalación de la modificación {mod}' }
            },
            info: {
                speed: '{uploadedSize} GB de {totalDownloadSize} GB',
                leftTime: 'Resto aproximadamente {leftTime}',
                downloadSpeed: '{value} MB/s',
                seconds: '{value} {value, plural, one{segundos} few{segundos} many{segundos} other {segundos}}',
                minutes: '{value} {value, plural, one{minuto} few{minutos} many{minutos} other {minutos}}'
            }
        },
        navbar: {
            play: 'Jugar',
            connect: 'Introducir IP',
            donate: 'Tienda',
            news: 'Noticias',
            forum: 'Foro',
            mods: 'Mods',
            settings: 'Ajustes',
            account: {
                signIn: 'Entrar',
                signUp: 'Registrarse',
                profileConfiguration: 'Configuración del perfil',
                signOut: 'Cerrar sesión'
            },
            comingSoon: 'Próximamente'
        },
        servers: {
            server: 'Servidor',
            polski: 'Polish',
            deutsch: 'German',
            majestic: 'Majestuoso',
            'play-now': 'Jugando ahora',
            'recommend-for-beginners': 'Consejos para principiantes',
            'last-time-visited': 'Visitado por última vez',
            'all-servers': 'Todos los servidores',
            'in-other-languages': 'En otros idiomas',
            tooltip: {
                techWorks: 'Se están realizando trabajos técnicos en el servidor',
                seasonPassMultiplier: '{value}La experiencia obtenida con el Pase de Invierno aumenta un %.'
            },
            advantages: {
                gift: 'Regalo',
                newServer: 'Nuevo',
                experience: 'X{value} Experiencia',
                seasonPassMultiplier: 'X{value}'
            }
        },
        select: { 'enter-value': 'Introduce el valor...' },
        donate: {
            back: 'Volver atrás',
            'buy-coins': 'Comprar moneda',
            cashbox: 'Compra las monedas para el usuario {login} en el servidor {server}',
            'choose-server': 'Elige un servidor',
            'enter-login': 'Introduce tu nombre de usuario',
            'enter-sum': 'Introduce el importe',
            'enter-email': 'Introduzca su dirección de correo electrónico',
            'min-bonus-value': 'desde {from}frotar.',
            pay: 'Ir al pago',
            'rules-checked': 'He leído los <span id="rulesUrl">términos de la oferta</span>',
            'rules-agree': 'Doy mi consentimiento para que Majestic RP ejecute inmediatamente el contrato activando el contenido digital (incluida la moneda digital y cualquier contenido activado con moneda digital) en mi cuenta. Acepto que pierdo el derecho a rescindir este contrato y recibir un reembolso después de activar el contenido digital en mi cuenta.',
            info: 'Mi familia',
            exchange: 'El cambio de moneda: 1',
            description: 'Por Majestic Coins se puede comprar: vehiculo especial, inmuebles, negocios, moneda del juego, y mucho más. Después del pago, el MC se cargará a su cuenta en el servidor de juego de forma automática.',
            warning: 'En caso si haya ingresado incorrectamente los datos, la transferencia a la cuenta necesaria no se realizará.',
            'contract-offer': 'Contrato de oferta',
            selectPayment: 'Elige el método de pago',
            selectPaymentOther: 'Otros métodos de pago',
            replenish: {
                cardTitles: {
                    card_ru1: 'Tarjetas de bancos rusos 1',
                    crypto: 'Otras criptomonedas',
                    erc20: 'Amarre USDT (ERC20)',
                    trc20: 'Amarre USDT (TRC20)',
                    kinguin: 'Teclas de activación',
                    steam: 'Paga con skins de Steam'
                },
                titles: {
                    card_ru1: 'Tarjetas de bancos rusos 1',
                    card_ru2: 'Tarjetas de bancos rusos 2',
                    card_ru3: 'Tarjetas de bancos rusos 3',
                    sbp: 'Sistema de pagos más rápido',
                    qiwi: 'Qiwi',
                    yandexpay: 'Yandex Pay',
                    card_ua: 'Tarjetas de bancos ucranianos',
                    card_world1: 'Tarjetas extranjeras 1',
                    card_world2: 'Tarjetas extranjeras 2',
                    paypal: 'PayPal',
                    kinguin: 'Teclas de activación de Kinguin',
                    steam: 'Artículos de Steam',
                    yoomoney: '„dinero“',
                    bitcoin: 'Bitcoin',
                    ethereum: 'Ethereum',
                    trc20: 'Amarre USDT (TRC20)',
                    erc20: 'Amarre USDT (ERC20)',
                    crypto: 'Otras criptomonedas',
                    direct_banking: 'Pago desde la cuenta bancaria',
                    cards_eu: 'Pago con tarjeta 1',
                    paysafecard: 'paysafecard',
                    blik: 'blik',
                    paypalych: 'Pago con tarjeta 2',
                    dolyame: 'Pago a plazos'
                }
            }
        },
        settings: {
            'on-main': 'Ir al inicio',
            categories: {
                main: 'Principal',
                patch: 'Parches',
                additional: 'Adicional',
                developer: 'Desarrollador'
            },
            descriptions: {
                shadowplayOverlay: 'Esta función te permite grabar vídeo y hacer capturas de pantalla en el juego. <br> Si tienes problemas con el juego <span class="highlight">(bloqueos, congelaciones, etc.)</span>, prueba a desactivar esta función.',
                discordOverlay: 'Esta función te permite abrir el menú de Discord en el juego. <br> Si tienes problemas con el juego <span class="highlight">(cuelgues, bloqueos, etc.)</span>, prueba a desactivar esta función.',
                devSettings: 'Sugerencia: Para guardar los datos en el campo de entrada, pulsa ENTER'
            },
            actions: {
                fix: 'Arréglalo',
                clearCache: 'Borrar',
                changeMAJESTICFolder: 'Cambiar',
                changeGTAFolder: 'Cambiar',
                fixPermissions: 'Arréglalo',
                reverifyFiles: 'Compruébalo'
            },
            titles: {
                region: 'Región',
                branch: 'Rama',
                cdnToken: 'Token CDN',
                enableDebug: 'Activar depuración',
                skipBranchCheck: 'Saltar comprobación de rama',
                language: 'Lengua',
                interfaceVolume: 'Volumen de la interfaz',
                hideAfterBootGame: 'Minimizar el lanzador después de iniciar el juego',
                downloadInLauncher: 'Descargar archivos del juego en el lanzador',
                enableCEFGPU: 'Aceleración del hardware de la interfaz',
                patchGTA: 'Aplicar el parche al juego',
                clearCache: 'Borrar la caché del juego',
                forceClearCache: 'Borrar completamente la caché del juego',
                changeMAJESTICFolder: 'Cambia la ubicación de instalación del RP Majestic',
                changeGTAFolder: 'Cambiar la ubicación de instalación de GTA V Legacy',
                fixPermissions: 'Arreglar los derechos de acceso a los archivos del juego',
                reverifyFiles: 'Obligado a comprobar los archivos del juego',
                shadowplayOverlay: 'Activar NVIDIA Shadowplay Overlay',
                discordOverlay: 'Activar la superposición de Discordia',
                pass: 'Contraseña',
                range: 'Rango'
            },
            placeholders: { enterPass: 'Introduce la contraseña' }
        },
        account: {
            signIn: {
                logInAccount: 'Inicia sesión en tu cuenta',
                signIn: 'Entrar',
                signUp: 'Registrarse',
                enterEmail: 'Introduce tu correo electrónico',
                enterPass: 'Introduce la contraseña',
                forgotPassword: '¿Has olvidado contraseña?'
            },
            signUp: {
                signUp: 'Registrarse',
                enterLogin: 'Introduce tu nombre de usuario',
                enterEmail: 'Introduce tu correo electrónico',
                enterPass: 'Introduce la contraseña',
                repeatPass: 'Repite la contraseña',
                alreadyCreateAccount: '¿Ya tienes una cuenta?',
                'agree-newsletters': 'Acepto recibir boletines informativos',
                'accept-rules': 'He leído y acepto las <span id="rulesUrl">reglas del servidor</span>',
                'confirm-age': 'Certifico que tengo al menos 18 años'
            },
            profileConfiguration: {},
            forgotPassword: {
                passwordReset: 'Restablecer contraseña',
                enterEmail: 'Introduce tu correo electrónico',
                enterPass: 'Introduce la contraseña',
                repeatPass: 'Repite la contraseña',
                continue: 'Continuar',
                cancel: 'Cancelar',
                enterCode: 'Introduce el código enviado a tu correo electrónico '
            }
        },
        mods: {
            sort: 'Clasificación',
            sortItems: {
                graphics: 'Gráficos',
                interface: 'Interfaz',
                sounds: 'Sonidos',
                other: 'Otro'
            },
            install: 'Instalar',
            uninstall: 'Eliminar',
            more: 'Más detallado',
            back: 'Volver atrás',
            empty: 'Todavía no hay nada, sigue las noticias para no perderte los nuevos mods',
            systemRequirements: 'Requisitos del sistema',
            operatingSystem: 'Sistema operativo',
            processor: 'Procesador',
            ram: 'RAM',
            videoCard: 'Tarjeta gráfica',
            soundCard: 'Tarjeta de sonido',
            freeDiskSpace: 'Espacio libre en el disco duro',
            berkleyRedux: {
                title: 'Berkley Redux',
                description: 'Modificación gráfica gratuita de Berkley basada en Redux.'
            },
            evolvedGraphics: {
                title: 'Gráficos evolucionados',
                description: 'Un mod global que mejora los gráficos al nivel de los juegos modernos. La iluminación del juego, el clima, los efectos y el posprocesamiento se erigen a la perfección, y gracias a los efectos gráficos de eNB, la imagen en el juego se vuelve clara y fotorrealista.'
            },
            atlasPopularPlaces: {
                title: 'Atlas con lugares populares',
                description: 'En la parte superior del mapa se superponen las ubicaciones de las áreas verdes, los nombres de los lugares populares y más'
            },
            loadingScreens: {
                title: 'Cargando pantallas',
                description: ''
            },
            strongMotionBlur: {
                title: 'Desenfoque de movimiento severo',
                description: 'Para los amantes de las imágenes cinematográficas'
            },
            purpleTracer: {
                title: 'trazador púrpura',
                description: '¡El espectáculo debe continuar! Ahora, al disparar desde un arma, aparecen efectos brillantes de cartuchos trazadores.'
            }
        },
        discord: {
            state: 'Juega en Majestic',
            details: 'Usando el lanzador'
        },
        tray: {
            open: 'Abrir',
            close: 'Cerrar'
        },
        gift: {
            'pickUp-gift': '¡No olvides recoger tu regalo!',
            get: 'Reclamar'
        },
        competition: {
            title: 'Sorteamos',
            'sub-title': 'Un auténtico BMW M3.',
            participate: 'Participa'
        },
        crash: {
            title: 'error crítico',
            problemTitle: 'Si el problema se repite, prueba los siguientes pasos:',
            sol1: 'Reinicia Majestic RP u ordenador personal',
            sol2: 'Ejecuta Majestic RP como administrador',
            sol3: 'Comprueba la integridad de los archivos del juego',
            probt1: '¿No te ayuda? Consulta nuestra página',
            probt2: 'problemas frecuentes',
            probt3: 'o',
            probt4: 'contacta con soporte',
            crashId: 'Identificación del accidente:',
            errorCode: 'Código de error:',
            logDir: 'Carpeta Registros',
            gtavDir: 'Carpeta GTA V',
            crashReport: 'Archivo de informe de colisión',
            exit: 'salir',
            reload: 'reinicia',
            crashIdCopy: 'ID de colisión copiado en el portapapeles',
            errorCodeCopy: 'Código de error copiado al portapapeles'
        }
    }
};