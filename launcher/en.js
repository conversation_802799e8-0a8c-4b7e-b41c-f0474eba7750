export default {
    launcher: {
        'servers-list': 'Server List',
        connection: 'Connection',
        milliseconds: 'mc',
        play: 'Play',
        notifications: {
            error: 'Failure',
            warning: 'Attention',
            success: 'Successful',
            mod: {
                uninstall: {
                    title: 'Successful',
                    text: 'Mod successfully deleted!'
                }
            },
            cache: {
                clear: {
                    title: 'Successful',
                    text: 'C<PERSON> cleared successfully'
                }
            },
            'permissions-fix': {
                title: 'Success',
                text: 'Access rights to game files successfully fixed'
            },
            account: {
                loginError: {
                    required: {
                        title: 'Failure',
                        text: 'Login is required!'
                    },
                    minLength: {
                        title: 'Failure',
                        text: 'The specified Login is too short!'
                    },
                    maxLength: {
                        title: 'Failure',
                        text: 'The specified Login is too long!'
                    }
                },
                emailError: {
                    email: {
                        title: 'Failure',
                        text: 'Invalid Email!'
                    },
                    required: {
                        title: 'Failure',
                        text: 'Email is required!'
                    },
                    minLength: {
                        title: 'Failure',
                        text: 'The specified Email is too short!'
                    },
                    maxLength: {
                        title: 'Failure',
                        text: 'The specified Email is too long!'
                    }
                },
                passwordError: {
                    required: {
                        title: 'Failure',
                        text: 'Password is required!'
                    },
                    minLength: {
                        title: 'Failure',
                        text: 'The password you entered is too short!'
                    },
                    maxLength: {
                        title: 'Failure',
                        text: 'The specified password is too long!'
                    },
                    sameAs: {
                        title: 'Failure',
                        text: 'Passwords don\'t match!'
                    }
                },
                forgotPassword: {
                    success: {
                        title: 'Successful',
                        text: 'You have successfully changed your password!'
                    },
                    codeError: {
                        title: 'Failure',
                        text: 'Invalid code!'
                    }
                }
            },
            installGame: {
                badPath: {
                    title: 'Failure',
                    text: 'Invalid path!'
                }
            },
            getGift: { 'select-server': 'Select server!' },
            reverifyFiles: { text: 'The next time you run the game, all files will be automatically checked for integrity.' },
            gameAlive: {
                title: 'Successful',
                text: 'You have successfully closed the game processes!'
            }
        },
        modal: {
            close: 'Close',
            connect: {
                title: 'manual connection',
                ip: 'Server address',
                password: 'Password (if available)',
                text: 'The server address must be in the format <mark>address:port</mark><br/>For example: <mark>*********.1:7788</mark> or <mark>example.server.com:443</mark>',
                discard: 'Cancellation',
                connect: 'Connect'
            },
            runGameError: {
                title: 'Failed to launch the game',
                'reason-title': 'Reason',
                'possibleSolutions-title': 'Possible solutions',
                1000: {
                    reason: 'Failed to run multiplayer file',
                    possibleSolutions: {
                        tryAgain: 'Try clicking play again',
                        reboot: 'Try restarting your computer'
                    }
                }
            },
            installGameError: {
                title: 'Failed to install the game',
                'reason-title': 'Reason',
                'possibleSolutions-title': 'Possible solutions',
                1001: {
                    reason: 'Failed to access the game directory',
                    possibleSolutions: {
                        runAsAdmin: 'Try running the Launcher as administrator',
                        changeAccess: 'Try changing the permissions of the game directory "Games"',
                        reboot: 'Try restarting your computer'
                    }
                }
            },
            startGameError: {
                title: 'Error when implementing patches',
                'reason-title': 'Reason',
                'possibleSolutions-title': 'Possible solutions',
                1001: {
                    reason: 'Failed to implement patches in the game',
                    possibleSolutions: {
                        runAsAdmin: 'Try running the Launcher as administrator',
                        changeAccess: 'Try running Rockstar Games Launcher separately',
                        reboot: 'Try restarting your computer'
                    }
                }
            },
            notEnoughDiskSpace: {
                title: 'Not enough disk space',
                'reason-title': 'Reason',
                'possibleSolutions-title': 'Possible solutions',
                1002: {
                    reason: 'The disk {disk} is free {freeSpace}',
                    reason2: 'Minimum required {requiredSpace}',
                    possibleSolutions: { freeSpace: 'Free up disk space {disk}' }
                }
            },
            enchancedGTAV: {
                title: 'Wrong version of GTA V',
                'reason-title': 'Reason',
                'possibleSolutions-title': 'Possible solutions',
                1003: {
                    reason: 'You are using GTA V Enchanced instead of GTA V Legacy',
                    possibleSolutions: {
                        steam_gtaLegacy: 'If you\'re using Steam, you\'ll need to download Grand Theft Auto V Legacy',
                        epic_gtaLegacy: 'If you\'re using Epic Games, you\'ll need to download the regular Grand Theft Auto V',
                        rgl_gtaLegacy: 'If you\'re using Rockstar Games Launcher, you\'ll need to download Grand Theft Auto V Legacy'
                    }
                }
            },
            getGift: {
                'choose-server': 'Select a server',
                description: 'Gift item available on all characters of one server of your choice',
                get: 'Claim',
                close: 'Close',
                success: 'You have successfully claimed your gift!',
                error: {
                    1315: 'Not so often!',
                    1101: 'You didn\'t log into the game from the launcher!',
                    1317: 'You have already received a gift on this server!',
                    default: 'Unknown error'
                }
            },
            BuyingKey: {
                buyingKey: 'Buy GTA V Legacy key',
                secure: 'Safely',
                'text-ru': 'The page with the key for the game Grand Theft Auto V Legacy in the Rockstar store will be opened shortly after payment.',
                text: 'A licensed version of Grand Theft Auto V Legacy is required. <br /> Purchase it from one of the official stores:',
                'path-title': 'Specify the path to the game',
                'click-to-choose': 'Select Game Folder',
                'successful-select': 'Path selected successfully!',
                'enter-email': 'Enter your Email',
                selectPayment: 'Choose a payment method',
                'user-agreement': 'I have read <span id="rulesUrl">terms of offer</span>',
                continue: 'Continue',
                cancel: 'Cancellation'
            },
            noGTA: {
                gtaNotFound: 'GTA V Legacy not found',
                youHaveGTA: 'Have you purchased GTA V Legacy?',
                text: 'Playing Majestic requires a licensed version of Grand Theft Auto V Legacy. The game key can be purchased quickly and conveniently in our store.',
                buy: 'no, buy',
                have: 'yes, I have',
                title: 'GTA V Legacy not found',
                'path-title': 'Specify the path to the game',
                'click-to-choose': 'Select Game Folder',
                'successful-select': 'Path selected successfully!'
            },
            selectGTA: {
                gtaNotFound: 'Select the path to GTA V Legacy',
                text: 'Playing Majestic requires a licensed version of Grand Theft Auto V Legacy. Select the path to the game on your computer.',
                title: 'Select the path to GTA V Legacy',
                'path-title': 'Specify the path to the game',
                'click-to-choose': 'Select Game Folder'
            },
            rageUndefined: {
                HDD: 'Hard Drive',
                available: 'Available',
                'game-files': 'GAME FILES',
                'required-space': 'It will take about {size} GB of free space',
                'choose-disk-for-installation': 'Select the disk to install the game files',
                'searching-HDD': 'Finding the hard drive...',
                install: 'Install',
                set: 'Change'
            },
            remove: {
                title: 'Confirm deletion',
                confirmation: 'Are you sure you want to delete <span class="value">{title}</span>?',
                accept: 'Yes, delete',
                cancel: 'I changed my mind'
            },
            failPayment: {
                error: 'Failure',
                description: 'Sorry, there was an error <br/> processing your payment. <br/> <br/> Please try again <br/> or <span id="supportUrl">contact support</span>.',
                back: 'Back',
                'to-main-page': 'To the main page',
                payment: 'Payment'
            },
            successPayment: {
                success: 'Successful',
                'replenish-account': 'You replenished your account!',
                'replenish-account-on': 'You replenished your account by <span class="amount">{amount}</span>',
                'funds-credited-account': 'Funds will be credited to your <br/> account shortly',
                'to-main-page': 'To the main page',
                back: 'Back',
                payment: 'Payment'
            },
            RegionSelector: {
                selectRegion: 'Select a region',
                discription: 'The available servers depend on it. You can change your choice at any time in the settings.',
                avaiableServers: 'Available servers',
                playStart: 'Launch the game',
                regions: {
                    eu: 'global',
                    ru: 'CIS',
                    test: 'Test'
                }
            },
            unknownBranch: {
                title: 'Could not find a test branch',
                description: 'An error occurred while trying to download the {value} branch, please make sure that the selected branch exists and is valid.'
            },
            incorrectToken: {
                title: 'An error occurred during download',
                description: 'Failed to download the selected test branch, check your CDN token for validity and try again'
            },
            gameAlive: {
                title: 'The game has already been launched',
                confirmation: 'Do you want to close the game before restarting it?',
                accept: 'Yes, I do',
                cancel: 'Cancellation'
            }
        },
        updater: {
            uploadedSize: '{uploadedSize} GB out of {totalDownloadSize} GB',
            speed: '{downloadSpeed} MB/s',
            leftTime: 'Remaining approximately {leftTime}',
            seconds: '{value} {value, plural, one{seconds} few{seconds} many{seconds} other {seconds}}',
            minutes: '{value} {value, plural, one{minute} few{minutes} many{minutes} other {minutes}}',
            'checking-for-updates': 'Check for updates',
            updating: 'Updates download'
        },
        loader: {
            cancel: 'Cancellation',
            titles: {
                game: {
                    install: 'Installing game files',
                    verifying: 'Checking files',
                    starting: 'Launch and game patch',
                    backup: 'Backing up game files',
                    resolving: 'Data processing'
                },
                mods: { install: 'Installation of modification {mod}' }
            },
            info: {
                speed: '{uploadedSize} GB out of {totalDownloadSize} GB',
                leftTime: 'Remaining approximately {leftTime}',
                downloadSpeed: '{value} MB/s',
                seconds: '{value} {value, plural, one{seconds} few{seconds} many{seconds} other {seconds}}',
                minutes: '{value} {value, plural, one{minute} few{minutes} many{minutes} other {minutes}}'
            }
        },
        navbar: {
            play: 'Play',
            connect: 'Enter IP',
            donate: 'Shop',
            news: 'News',
            forum: 'Forum',
            mods: 'Mods',
            settings: 'Settings',
            account: {
                signIn: 'Log In',
                signUp: 'Registration',
                profileConfiguration: 'Profile Settings',
                signOut: 'Log out of your account'
            },
            comingSoon: 'Coming soon'
        },
        servers: {
            server: 'Server',
            polski: 'Polish',
            deutsch: 'German',
            majestic: 'Majestic',
            'play-now': 'Now playing',
            'recommend-for-beginners': 'Advice for beginners',
            'last-time-visited': 'Visited for the last time',
            'all-servers': 'All Servers',
            'in-other-languages': 'In other languages',
            tooltip: {
                techWorks: 'The server is undergoing technical work',
                seasonPassMultiplier: 'Winter Pass experience gained increased by {value}%'
            },
            advantages: {
                gift: 'Gift',
                newServer: 'New',
                experience: 'X{value} Experience',
                seasonPassMultiplier: 'X{value}'
            }
        },
        select: { 'enter-value': 'Enter a value...' },
        donate: {
            back: 'Back',
            'buy-coins': 'Buy currency',
            cashbox: 'Buying coins for the login {login} on the {server} server',
            'choose-server': 'Select a server',
            'enter-login': 'Enter your login',
            'enter-sum': 'Enter the amount',
            'enter-email': 'Enter the email address',
            'min-bonus-value': 'from {from}rub.',
            pay: 'Proceed to payment',
            'rules-checked': 'I have read the <span id="rulesUrl">terms of the offer</span>',
            'rules-agree': 'I consent to Majestic RP immediately executing the contract by activating digital content (including digital currency and any content activated using digital currency) in my account. I accept that I lose the right to terminate this contract and receive a refund after activating the digital content in my account.',
            info: 'Information',
            exchange: 'Exchange rate: 1rub.',
            description: 'For Majestic Coins you can buy: unique vehicles, real estate, business, game currency, and much more. After payment, MC will be credited to your account on the game server automatically.',
            warning: 'If you entered the data incorrectly, the funds will not be transferred to the correct account.',
            'contract-offer': 'Offer Agreement',
            selectPayment: 'Choose a payment method',
            selectPaymentOther: 'Other payment methods',
            replenish: {
                cardTitles: {
                    card_ru1: 'Russia Bank Cards 1',
                    crypto: 'Other cryptocurrencies',
                    erc20: 'USDT Tether (ERC20)',
                    trc20: 'USDT Tether (TRC20)',
                    kinguin: 'Activation Keys',
                    steam: 'Steam Skin Payment'
                },
                titles: {
                    card_ru1: 'Bank cards of Russia 1',
                    card_ru2: 'Russia Bank Cards 2',
                    card_ru3: 'Russian Bank Cards 3',
                    sbp: 'Fast Payments',
                    qiwi: 'Qiwi',
                    yandexpay: 'Yandex Pay',
                    card_ua: 'Ukraine Bank Cards',
                    card_world1: 'Foreign Cards 1',
                    card_world2: 'Foreign Cards 2',
                    paypal: 'PayPal',
                    kinguin: 'Kinguin Activation Keys',
                    steam: 'Steam Items',
                    yoomoney: 'YooMoney',
                    bitcoin: 'Bitcoin',
                    ethereum: 'Ethereum',
                    trc20: 'USDT Tether (TRC20)',
                    erc20: 'USDT Tether (ERC20)',
                    crypto: 'Other cryptocurrencies',
                    direct_banking: 'Payment from Bank account',
                    cards_eu: 'Payment by card 1',
                    paysafecard: 'paysafecard',
                    blik: 'blik',
                    paypalych: 'Payment by card 2',
                    dolyame: 'Payment in installments'
                }
            }
        },
        settings: {
            'on-main': 'To the main page',
            categories: {
                main: 'General',
                patch: 'Patches',
                additional: 'Additional',
                developer: 'Developer'
            },
            descriptions: {
                shadowplayOverlay: 'This function allows you to record video and take screenshots in the game. <br> If you experience problems with the game <span class="highlight">(crashes, freezes, etc.)</span>, try disabling this feature.',
                discordOverlay: 'This function allows you to open Discord menu in the game. <br> If you are experiencing problems with the game <span class="highlight">(crashes, freezes, etc.)</span>, try disabling this feature.',
                devSettings: 'Hint: To save the data in the input field, press ENTER'
            },
            actions: {
                fix: 'Repair',
                clearCache: 'Clear',
                changeMAJESTICFolder: 'Change',
                changeGTAFolder: 'Change',
                fixPermissions: 'Repair',
                reverifyFiles: 'Check it out'
            },
            titles: {
                region: 'Region',
                branch: 'Branch',
                cdnToken: 'CDN Token',
                enableDebug: 'Enable debugging',
                skipBranchCheck: 'Skip branch check',
                language: 'Language',
                interfaceVolume: 'Interface Volume',
                hideAfterBootGame: 'Minimize the Launcher after starting the game',
                downloadInLauncher: 'Download game files in the launcher',
                enableCEFGPU: 'Hardware interface acceleration',
                patchGTA: 'Apply the patch to the game',
                clearCache: 'Clear game cache',
                forceClearCache: 'Completely clear the game cache',
                changeMAJESTICFolder: 'Change Majestic RP installation location',
                changeGTAFolder: 'Change the installation location of GTA V Legacy',
                fixPermissions: 'Fix access rights to game files',
                reverifyFiles: 'Forced to check the game files',
                shadowplayOverlay: 'Enable NVIDIA Shadowplay Overlay',
                discordOverlay: 'Enable Discord Overlay',
                pass: 'Password',
                range: 'Range'
            },
            placeholders: { enterPass: 'Enter the password' }
        },
        account: {
            signIn: {
                logInAccount: 'Log in to your account',
                signIn: 'Log In',
                signUp: 'Registration',
                enterEmail: 'Enter email',
                enterPass: 'Enter the password',
                forgotPassword: 'Forgot your password?'
            },
            signUp: {
                signUp: 'Registration',
                enterLogin: 'Enter Login',
                enterEmail: 'Enter email',
                enterPass: 'Enter the password',
                repeatPass: 'Repeat password',
                alreadyCreateAccount: 'Already have an account?',
                'agree-newsletters': 'I agree to receive newsletters',
                'accept-rules': 'I have read and agree to the <span id="rulesUrl">server</span> rules',
                'confirm-age': 'I confirm that I am 18 years old'
            },
            profileConfiguration: {},
            forgotPassword: {
                passwordReset: 'Password Reset',
                enterEmail: 'Enter email',
                enterPass: 'Enter the password',
                repeatPass: 'Repeat password',
                continue: 'Continue',
                cancel: 'Cancellation',
                enterCode: 'Enter the code sent to your email '
            }
        },
        mods: {
            sort: 'Sorting',
            sortItems: {
                graphics: 'Graphics',
                interface: 'Interface',
                sounds: 'Sounds',
                other: 'Other'
            },
            install: 'Install',
            uninstall: 'Remove',
            more: 'View Details',
            back: 'Back',
            empty: 'There is nothing here yet, follow the news so as not to miss new mods',
            systemRequirements: 'System requirements',
            operatingSystem: 'Operating System',
            processor: 'Processor',
            ram: 'RAM',
            videoCard: 'Graphics card',
            soundCard: 'Sound Card',
            freeDiskSpace: 'Free hard disk space',
            berkleyRedux: {
                title: 'Berkley Redux',
                description: 'Free graphical modification from Berkley based on Redux.'
            },
            evolvedGraphics: {
                title: 'Evolved Graphics',
                description: 'A global mod that improves the graphics to the level of modern games. Game lighting, weather, effects and post-processing are erected to perfection, and thanks to the graphic effects of ENB, the picture in the game becomes clear and photorealistic.'
            },
            atlasPopularPlaces: {
                title: 'Atlas with popular places',
                description: 'Overlaid on top of the map are the locations of green areas, the names of popular places, and more'
            },
            loadingScreens: {
                title: 'Loading screens',
                description: ''
            },
            strongMotionBlur: {
                title: 'Severe motion blur',
                description: 'For lovers of cinematic images'
            },
            purpleTracer: {
                title: 'purple tracer',
                description: 'The Show Must Go On! Now, when shooting from a weapon, bright effects of tracer cartridges appear.'
            }
        },
        discord: {
            state: 'Playing on Majestic',
            details: 'Using the launcher'
        },
        tray: {
            open: 'Open',
            close: 'Close'
        },
        gift: {
            'pickUp-gift': 'Don\'t forget to pick up your gift!',
            get: 'Claim'
        },
        competition: {
            title: 'Draw',
            'sub-title': 'A real BMW M3',
            participate: 'Participate'
        },
        crash: {
            title: 'critical error',
            problemTitle: 'If the problem recurs, try the following steps:',
            sol1: 'Restart Majestic RP or personal computer',
            sol2: 'Run Majestic RP as administrator',
            sol3: 'Check the integrity of the game files',
            probt1: 'Doesn\'t work? Check our page',
            probt2: 'frequent problems',
            probt3: 'or',
            probt4: 'contact support',
            crashId: 'Crash ID:',
            errorCode: 'Error code:',
            logDir: 'Logs folder',
            gtavDir: 'GTA V folder',
            crashReport: 'Crash report file',
            exit: 'logout',
            reload: 'restart',
            crashIdCopy: 'Crash ID copied to clipboard',
            errorCodeCopy: 'Error code copied to clipboard'
        }
    }
};