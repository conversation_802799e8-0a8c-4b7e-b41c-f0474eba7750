export default {
    launcher: {
        'servers-list': 'Liste des serveurs',
        connection: 'Connexion',
        milliseconds: 'ms',
        play: 'Jouer',
        notifications: {
            error: 'Erreur',
            warning: 'Attention',
            success: 'Succ<PERSON>',
            mod: {
                uninstall: {
                    title: 'Succ<PERSON>',
                    text: 'Mod supprimé avec succès !'
                }
            },
            cache: {
                clear: {
                    title: 'Succ<PERSON>',
                    text: 'Cache effacé avec succès'
                }
            },
            'permissions-fix': {
                title: 'Ave<PERSON> succès',
                text: 'Les permissions des fichiers de jeu ont été corrigées avec succès'
            },
            account: {
                loginError: {
                    required: {
                        title: 'Erreur',
                        text: 'Une connexion est requise !'
                    },
                    minLength: {
                        title: 'Erreur',
                        text: 'La connexion spécifiée est trop courte !'
                    },
                    maxLength: {
                        title: 'Erreur',
                        text: 'La connexion spécifiée est trop longue !'
                    }
                },
                emailError: {
                    email: {
                        title: '<PERSON><PERSON>ur',
                        text: 'E-mail invalide !'
                    },
                    required: {
                        title: 'Erreur',
                        text: 'Une adresse e-mail est requise !'
                    },
                    minLength: {
                        title: 'Erreur',
                        text: 'L\'e-mail spécifié est trop court !'
                    },
                    maxLength: {
                        title: 'Erreur',
                        text: 'L\'e-mail spécifié est trop long !'
                    }
                },
                passwordError: {
                    required: {
                        title: 'Erreur',
                        text: 'Le mot de passe est obligatoire !'
                    },
                    minLength: {
                        title: 'Erreur',
                        text: 'Le mot de passe que vous avez saisi est trop court !'
                    },
                    maxLength: {
                        title: 'Erreur',
                        text: 'Le mot de passe spécifié est trop long !'
                    },
                    sameAs: {
                        title: 'Erreur',
                        text: 'Les mots de passe ne correspondent pas !'
                    }
                },
                forgotPassword: {
                    success: {
                        title: 'Succès',
                        text: 'Vous avez modifié votre mot de passe avec succès !'
                    },
                    codeError: {
                        title: 'Erreur',
                        text: 'Code invalide !'
                    }
                }
            },
            installGame: {
                badPath: {
                    title: 'Erreur',
                    text: 'Chemin invalide !'
                }
            },
            getGift: { 'select-server': 'Sélectionnez un serveur !' },
            reverifyFiles: { text: 'La prochaine fois que tu lanceras le jeu, l\'intégrité de tous les fichiers sera automatiquement vérifiée.' },
            gameAlive: {
                title: 'Succès',
                text: 'Tu as réussi à fermer les processus de jeu !'
            }
        },
        modal: {
            close: 'Fermer',
            connect: {
                title: 'connexion manuelle',
                ip: 'Adresse du serveur',
                password: 'Mot de passe (si disponible)',
                text: 'L\'adresse du serveur doit être au format <mark>adresse:port</mark><br/>Par exemple : <mark>*********.1:7788</mark> ou <mark>exemple.server.com:443</mark>',
                discard: 'Annuler',
                connect: 'Connecte-toi'
            },
            runGameError: {
                title: 'Échec du démarrage de la partie',
                'reason-title': 'Motif',
                'possibleSolutions-title': 'Solutions possibles',
                1000: {
                    reason: 'Échec de l\'exécution du fichier multijoueur',
                    possibleSolutions: {
                        tryAgain: 'Essayez de cliquer à nouveau sur Lecture',
                        reboot: 'Essayez de redémarrer votre ordinateur'
                    }
                }
            },
            installGameError: {
                title: 'L\'installation du jeu a échoué',
                'reason-title': 'Raison',
                'possibleSolutions-title': 'Solutions possibles',
                1001: {
                    reason: 'Échec de l\'accès au répertoire du jeu',
                    possibleSolutions: {
                        runAsAdmin: 'Essaie d\'exécuter le Launcher en tant qu\'administrateur',
                        changeAccess: 'Essaie de modifier les permissions du répertoire du jeu "Games"',
                        reboot: 'Essaie de redémarrer ton ordinateur'
                    }
                }
            },
            startGameError: {
                title: 'Erreur lors de la mise en œuvre des correctifs',
                'reason-title': 'Motif',
                'possibleSolutions-title': 'Solutions possibles',
                1001: {
                    reason: 'N\'a pas réussi à mettre en œuvre les correctifs dans le jeu',
                    possibleSolutions: {
                        runAsAdmin: 'Essaie d\'exécuter le Launcher en tant qu\'administrateur',
                        changeAccess: 'Essaie d\'exécuter Rockstar Games Launcher séparément',
                        reboot: 'Essayez de redémarrer votre ordinateur'
                    }
                }
            },
            notEnoughDiskSpace: {
                title: 'Espace discal insuffisant',
                'reason-title': 'Motif',
                'possibleSolutions-title': 'Solutions possibles',
                1002: {
                    reason: '{disk} {freeSpace}Le disque est gratuit',
                    reason2: '{requiredSpace}Minimum requis',
                    possibleSolutions: { freeSpace: '{disk}Libère de l\'espace disque' }
                }
            },
            enchancedGTAV: {
                title: 'Mauvaise version de GTA V',
                'reason-title': 'Motif',
                'possibleSolutions-title': 'Solutions possibles',
                1003: {
                    reason: 'Tu utilises GTA V Enchanced au lieu de GTA V Legacy.',
                    possibleSolutions: {
                        steam_gtaLegacy: 'Si tu utilises Steam, tu devras télécharger Grand Theft Auto V Legacy.',
                        epic_gtaLegacy: 'Si tu utilises Epic Games, tu devras télécharger la version normale de Grand Theft Auto V.',
                        rgl_gtaLegacy: 'Si tu utilises le lanceur Rockstar Games, tu devras télécharger Grand Theft Auto V Legacy.'
                    }
                }
            },
            getGift: {
                'choose-server': 'Sélectionner un serveur',
                description: 'Article cadeau disponible sur tous les personnages d\'un serveur de votre choix',
                get: 'Réclamation',
                close: 'Fermer',
                success: 'Tu as récupéré ton cadeau avec succès !',
                error: {
                    1315: 'Pas tant que ça !',
                    1101: 'Tu ne t\'es pas connecté au jeu à partir du lanceur !',
                    1317: 'Tu as déjà reçu un cadeau sur ce serveur !',
                    default: 'Erreur inconnue'
                }
            },
            BuyingKey: {
                buyingKey: 'Acheter la clé GTA V Legacy',
                secure: 'Sûr',
                'text-ru': 'La page contenant la clé du jeu Grand Theft Auto V Legacy dans la boutique Rockstar sera ouverte peu de temps après le paiement.',
                text: 'Une version sous licence de Grand Theft Auto V Legacy est nécessaire. <br /> Achète-la dans l\'une des boutiques officielles :',
                'path-title': 'Spécifie le chemin d\'accès au jeu',
                'click-to-choose': 'Sélectionne le dossier contenant le jeu',
                'successful-select': 'Le chemin a été choisi avec succès !',
                'enter-email': 'Saisis ton courriel',
                selectPayment: 'Sélectionne un mode de paiement',
                'user-agreement': 'J\'ai lu <span id="rulesUrl">les conditions générales de l\'offre</span>',
                continue: 'A suivre',
                cancel: 'Annulation'
            },
            noGTA: {
                gtaNotFound: 'GTA V Legacy introuvable',
                youHaveGTA: 'As-tu acheté GTA V Legacy ?',
                text: 'Pour jouer à Majestic, tu as besoin d\'une version sous licence de Grand Theft Auto V Legacy. La clé de jeu peut être achetée rapidement et confortablement dans notre boutique.',
                buy: 'non, achète',
                have: 'oui, il y en a',
                title: 'GTA V Legacy introuvable',
                'path-title': 'Spécifie le chemin d\'accès au jeu',
                'click-to-choose': 'Sélectionne le dossier contenant le jeu',
                'successful-select': 'Le chemin a été choisi avec succès !'
            },
            selectGTA: {
                gtaNotFound: 'Sélectionne le chemin d\'accès à GTA V Legacy',
                text: 'Jouer à Majestic nécessite une version sous licence de Grand Theft Auto V Legacy. Sélectionne le chemin d\'accès au jeu sur ton ordinateur.',
                title: 'Sélectionne le chemin d\'accès à GTA V Legacy',
                'path-title': 'Spécifie le chemin d\'accès au jeu',
                'click-to-choose': 'Sélectionne le dossier contenant le jeu'
            },
            rageUndefined: {
                HDD: 'Poil/dd',
                available: 'Abordable',
                'game-files': 'FICHIERS DE JEU',
                'required-space': 'Il faudra environ {size} Go d\'espace libre',
                'choose-disk-for-installation': 'Sélectionnez le lecteur pour installer les fichiers de jeu',
                'searching-HDD': 'Recherche d\'un disque dur...',
                install: 'Installer',
                set: 'Modifier'
            },
            remove: {
                title: 'Confirmer la suppression',
                confirmation: 'Voulez-vous vraiment supprimer <span class="value">{title}</span>?',
                accept: 'Oui, supprimer',
                cancel: 'J\'ai changé d\'avis'
            },
            failPayment: {
                error: 'Erreur',
                description: 'Désolé, il y a eu une erreur <br/> lors du traitement de votre paiement. <br/> <br/> Veuillez réessayer <br/> ou <span id="supportUrl">contacter l\'assistance</span>.',
                back: 'Retour',
                'to-main-page': 'Aller à la page d\'accueil',
                payment: 'Paiement'
            },
            successPayment: {
                success: 'Succès',
                'replenish-account': 'Vous avez réapprovisionné votre compte !',
                'replenish-account-on': 'Vous avez réapprovisionné votre compte de <span class="amount">{amount}</span>',
                'funds-credited-account': 'Les fonds seront crédités sur votre compte <br/> sous peu',
                'to-main-page': 'Aller à la page d\'accueil',
                back: 'Retour',
                payment: 'Paiement'
            },
            RegionSelector: {
                selectRegion: 'Sélectionne une région',
                discription: 'Les serveurs disponibles en dépendent. Tu peux modifier ton choix à tout moment dans les paramètres.',
                avaiableServers: 'Serveurs disponibles',
                playStart: 'Commence le jeu',
                regions: {
                    eu: 'mondial',
                    ru: 'CIS',
                    test: 'Test'
                }
            },
            unknownBranch: {
                title: 'Impossible de trouver une branche de test',
                description: 'Une erreur s\'est produite lors du téléchargement de la branche {value} , assure-toi que la branche sélectionnée existe et qu\'elle est valide.'
            },
            incorrectToken: {
                title: 'Une erreur s\'est produite pendant le téléchargement',
                description: 'Échec du téléchargement de la branche de test sélectionnée, vérifie la validité de ton jeton CDN et réessaie.'
            },
            gameAlive: {
                title: 'Le jeu a déjà été lancé',
                confirmation: 'Veux-tu fermer le jeu avant de le redémarrer ?',
                accept: 'Oui, je le sais.',
                cancel: 'Annuler'
            }
        },
        updater: {
            uploadedSize: '{uploadedSize} Go sur {totalDownloadSize} Go',
            speed: '{downloadSpeed} Mo/s',
            leftTime: 'Reste environ {leftTime}',
            seconds: '{value} {value, plural, one{secondes} few{secondes} many{secondes} other {secondes}}',
            minutes: '{value} {value, plural, one{minute} few{minutes} many{minutes} other {minutes}}',
            'checking-for-updates': 'Vérifier les mises à jour',
            updating: 'Téléchargement des mises à jour'
        },
        loader: {
            cancel: 'Annuler',
            titles: {
                game: {
                    install: 'Installation des fichiers de jeu',
                    verifying: 'Vérification des fichiers',
                    starting: 'Lancer et patcher le jeu',
                    backup: 'Sauvegarde des fichiers de jeu',
                    resolving: 'Traitement des données'
                },
                mods: { install: 'Installation de la modification {mod}' }
            },
            info: {
                speed: '{uploadedSize} Go sur {totalDownloadSize} Go',
                leftTime: 'Reste environ {leftTime}',
                downloadSpeed: '{value} Mo/s',
                seconds: '{value} {value, plural, one{secondes} few{secondes} many{secondes} other {secondes}}',
                minutes: '{value} {value, plural, one{minute} few{minutes} many{minutes} other {minutes}}'
            }
        },
        navbar: {
            play: 'Lecture',
            connect: 'Saisir l\'IP',
            donate: 'Boutique',
            news: 'Nouvelles',
            forum: 'Forum',
            mods: 'Mods',
            settings: 'Réglages',
            account: {
                signIn: 'Se connecter',
                signUp: 'Inscription',
                profileConfiguration: 'Paramètres du profil',
                signOut: 'Se déconnecter'
            },
            comingSoon: 'Bientôt disponible'
        },
        servers: {
            server: 'Serveur',
            polski: 'Polonais',
            deutsch: 'Allemand',
            majestic: 'Majestueux',
            'play-now': 'En cours de lecture',
            'recommend-for-beginners': 'Conseils pour les débutants',
            'last-time-visited': 'Visité pour la dernière fois',
            'all-servers': 'Tous les serveurs',
            'in-other-languages': 'Dans d\'autres langues',
            tooltip: {
                techWorks: 'Des travaux techniques sont en cours sur le serveur',
                seasonPassMultiplier: '{value}L\'expérience acquise au col d\'hiver a augmenté de %'
            },
            advantages: {
                gift: 'Cadeau',
                newServer: 'Nouveau',
                experience: 'X{value} Expérience',
                seasonPassMultiplier: '{value}X'
            }
        },
        select: { 'enter-value': 'Entrez une valeur...' },
        donate: {
            back: 'Retour',
            'buy-coins': 'Acheter des devises',
            cashbox: 'Achat de pièces pour login {login} sur {server} serveur',
            'choose-server': 'Sélectionner un serveur',
            'enter-login': 'Saisissez votre nom d\'utilisateur',
            'enter-sum': 'Saisir le montant',
            'enter-email': 'Saisissez votre adresse e-mail',
            'min-bonus-value': 'à partir de {from}frottement.',
            pay: 'Accéder au paiement',
            'rules-checked': 'J\'ai lu les <span id="rulesUrl">conditions de l\'offre</span>',
            'rules-agree': 'Je consens à ce que Majestic RP exécute immédiatement le contrat en activant le contenu numérique (y compris la monnaie numérique et tout contenu activé à l\'aide de la monnaie numérique) dans mon compte. J\'accepte de perdre le droit de résilier ce contrat et de recevoir un remboursement après avoir activé le contenu numérique de mon compte.',
            info: 'Ma famille',
            exchange: 'Taux de change : R1',
            description: 'Les pièces majestueuses peuvent être utilisées pour acheter : des transports uniques, des biens immobiliers, des affaires, de la monnaie dans le jeu, et bien plus encore. Après le paiement, MC sera automatiquement crédité sur votre compte sur le serveur de jeu.',
            warning: 'Si vous avez saisi les données de manière incorrecte, vous ne pourrez pas transférer de fonds vers le compte souhaité.',
            'contract-offer': 'Convention D\'OFFRE',
            selectPayment: 'Veuillez sélectionner un mode de paiement',
            selectPaymentOther: 'Autres modes de paiement',
            replenish: {
                cardTitles: {
                    card_ru1: 'Cartes des banques russes 1',
                    crypto: 'Autres crypto-monnaies',
                    erc20: 'Attache USDT (ERC20)',
                    trc20: 'Attache USDT (TRC20)',
                    kinguin: 'Clés d\'activation',
                    steam: 'Payer avec les skins Steam'
                },
                titles: {
                    card_ru1: 'Cartes des banques russes 1',
                    card_ru2: 'Cartes des banques russes 2',
                    card_ru3: 'Cartes des banques russes 3',
                    sbp: 'Système de paiement plus rapide',
                    qiwi: 'Qiwi',
                    yandexpay: 'Yandex Pay',
                    card_ua: 'Cartes des banques ukrainiennes',
                    card_world1: 'Cartes étrangères 1',
                    card_world2: 'Cartes étrangères 2',
                    paypal: 'PayPal',
                    kinguin: 'Clés d\'activation Kinguin',
                    steam: 'Articles de Steam',
                    yoomoney: '‹ argent',
                    bitcoin: 'Bitcoin',
                    ethereum: 'Ethereum',
                    trc20: 'Attache USDT (TRC20)',
                    erc20: 'Attache USDT (ERC20)',
                    crypto: 'Autres crypto-monnaies',
                    direct_banking: 'Paiement à partir du compte bancaire',
                    cards_eu: 'Paiement par carte 1',
                    paysafecard: 'paysafecard',
                    blik: 'blik',
                    paypalych: 'Paiement par carte 2',
                    dolyame: 'Paiement échelonné'
                }
            }
        },
        settings: {
            'on-main': 'Retour à la page d\'accueil',
            categories: {
                main: 'De base',
                patch: 'Les écussons',
                additional: 'En plus',
                developer: 'Développeur'
            },
            descriptions: {
                shadowplayOverlay: 'Cette fonction te permet d\'enregistrer des vidéos et de faire des captures d\'écran dans le jeu. <br> Si tu rencontres des problèmes avec le jeu <span class="highlight">(plantage, gel, etc.)</span>, essaie de désactiver cette fonction.',
                discordOverlay: 'Cette fonction te permet d\'ouvrir le menu Discord dans le jeu. <br> Si tu rencontres des problèmes avec le jeu <span class="highlight">(plantages, blocages, etc.)</span>, essaie de désactiver cette fonction.',
                devSettings: 'Conseil : Pour enregistrer les données dans le champ de saisie, appuie sur la touche ENTER.'
            },
            actions: {
                fix: 'Répare-le',
                clearCache: 'Clair',
                changeMAJESTICFolder: 'Changer',
                changeGTAFolder: 'Changer',
                fixPermissions: 'Répare-le',
                reverifyFiles: 'Vérifie-le'
            },
            titles: {
                region: 'Région',
                branch: 'Branche',
                cdnToken: 'Jeton CDN',
                enableDebug: 'Activer le débogage',
                skipBranchCheck: 'Ignorer la vérification de la branche',
                language: 'Langue',
                interfaceVolume: 'Volume de l\'interface',
                hideAfterBootGame: 'Réduire le lanceur après le lancement du jeu',
                downloadInLauncher: 'Télécharger les fichiers du jeu dans le lanceur',
                enableCEFGPU: 'Accélération matérielle de l\'interface',
                patchGTA: 'Appliquer le patch au jeu',
                clearCache: 'Effacer le cache du jeu',
                forceClearCache: 'Effacer complètement le cache du jeu',
                changeMAJESTICFolder: 'Modifier l\'emplacement d\'installation du RP Majestic',
                changeGTAFolder: 'Modifier l\'emplacement d\'installation de GTA V Legacy',
                fixPermissions: 'Fixe les droits d\'accès aux fichiers de jeu',
                reverifyFiles: 'Obligé de vérifier les fichiers du jeu',
                shadowplayOverlay: 'Activer la superposition NVIDIA Shadowplay',
                discordOverlay: 'Activer la superposition Discord',
                pass: 'Mot de passe',
                range: 'Gamme'
            },
            placeholders: { enterPass: 'Saisir le mot de passe' }
        },
        account: {
            signIn: {
                logInAccount: 'Connectez-vous à votre compte',
                signIn: 'Se connecter',
                signUp: 'Inscription',
                enterEmail: 'Saisir l\'adresse e-mail',
                enterPass: 'Saisir le mot de passe',
                forgotPassword: 'Mot de passe oublié ?'
            },
            signUp: {
                signUp: 'Inscription',
                enterLogin: 'Saisissez votre nom d\'utilisateur',
                enterEmail: 'Saisir l\'adresse e-mail',
                enterPass: 'Saisir le mot de passe',
                repeatPass: 'Répéter le mot de passe',
                alreadyCreateAccount: 'Vous avez déjà un compte ?',
                'agree-newsletters': 'J\'accepte de recevoir des newsletters',
                'accept-rules': 'J\'ai lu et j\'accepte les règles <span id="rulesUrl">serveur</span>',
                'confirm-age': 'Je certifie avoir au moins 18 ans'
            },
            profileConfiguration: {},
            forgotPassword: {
                passwordReset: 'Réinitialiser le mot de passe',
                enterEmail: 'Saisir l\'adresse e-mail',
                enterPass: 'Saisir le mot de passe',
                repeatPass: 'Répéter le mot de passe',
                continue: 'Continuer',
                cancel: 'Annuler',
                enterCode: 'Saisissez le code envoyé à votre adresse e-mail '
            }
        },
        mods: {
            sort: 'Tri',
            sortItems: {
                graphics: 'Graphiques',
                interface: 'Interface',
                sounds: 'Sons',
                other: 'Autre'
            },
            install: 'Installer',
            uninstall: 'Supprimer',
            more: 'En savoir plus',
            back: 'Retour',
            empty: 'Il n\'y a encore rien ici, suivez l\'actualité pour ne pas rater les nouveaux mods',
            systemRequirements: 'Configuration requise',
            operatingSystem: 'Système d\'exploitation',
            processor: 'Processeur',
            ram: 'RAM',
            videoCard: 'Carte graphique',
            soundCard: 'Carte son',
            freeDiskSpace: 'Espace libre sur le disque dur',
            berkleyRedux: {
                title: 'Berkley Redux',
                description: 'Modification graphique gratuite de Berkley basée sur Redux.'
            },
            evolvedGraphics: {
                title: 'Graphiques évolués',
                description: 'Un mod global qui améliore les graphismes au niveau des jeux modernes. L\'éclairage du jeu, la météo, les effets et le post-traitement sont érigés à la perfection, et grâce aux effets graphiques d\'eNB, l\'image dans le jeu devient claire et photoréaliste.'
            },
            atlasPopularPlaces: {
                title: 'Atlas avec des lieux populaires',
                description: 'Sur le dessus de la carte sont superposés les emplacements des espaces verts, les noms des lieux populaires, et plus encore'
            },
            loadingScreens: {
                title: 'Écrans de chargement',
                description: ''
            },
            strongMotionBlur: {
                title: 'Flou de mouvement sévère',
                description: 'Pour les amateurs d\'images cinématographiques'
            },
            purpleTracer: {
                title: 'traceur violet',
                description: 'Le spectacle doit continuer ! Maintenant, lorsque vous tirez à partir d\'une arme, des effets lumineux de cartouches de traceur apparaissent.'
            }
        },
        discord: {
            state: 'Joue sur Majestic',
            details: 'En utilisant le lanceur'
        },
        tray: {
            open: 'Ouvrir',
            close: 'Fermer'
        },
        gift: {
            'pickUp-gift': 'N\'oubliez pas de ramasser votre cadeau !',
            get: 'Réclamation'
        },
        competition: {
            title: 'Nous tirons au sort',
            'sub-title': 'Une vraie BMW M3.',
            participate: 'Participe'
        },
        crash: {
            title: 'erreur critique',
            problemTitle: 'Si le problème se reproduit, essaie les étapes suivantes :',
            sol1: 'Redémarre Majestic RP ou l\'ordinateur personnel',
            sol2: 'Exécute Majestic RP en tant qu\'administrateur',
            sol3: 'Vérifie l\'intégrité des fichiers du jeu',
            probt1: 'Tu n\'as pas besoin d\'aide ? Consulte notre page',
            probt2: 'problèmes fréquents',
            probt3: 'ou',
            probt4: 'contacter l\'assistance',
            crashId: 'ID de l\'accident :',
            errorCode: 'Code d\'erreur :',
            logDir: 'Dossier des journaux',
            gtavDir: 'Dossier GTA V',
            crashReport: 'Fichier de rapport d\'accident',
            exit: 'sors',
            reload: 'redémarrer',
            crashIdCopy: 'L\'identifiant de l\'accident est copié dans le presse-papiers',
            errorCodeCopy: 'Code d\'erreur copié dans le presse-papiers'
        }
    }
};