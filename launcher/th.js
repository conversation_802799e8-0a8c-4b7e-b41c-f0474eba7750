export default {
    launcher: {
        'servers-list': 'รายชื่อเซิร์ฟเวอร์',
        connection: 'การเชื่อมต่อ',
        milliseconds: 'นางสาว',
        play: 'เล่น',
        notifications: {
            error: 'ข้อผิดพลาด',
            warning: 'ข้อควรระวัง',
            success: 'สำเร็จ',
            mod: {
                uninstall: {
                    title: 'สำเร็จ',
                    text: 'ลบ Mod สำเร็จแล้ว!'
                }
            },
            cache: {
                clear: {
                    title: 'สำเร็จ',
                    text: 'ล้างแคชสำเร็จแล้ว'
                }
            },
            'permissions-fix': {
                title: 'สำเร็จ',
                text: 'แก้ไขสิทธิ์ไฟล์เกมเรียบร้อยแล้ว'
            },
            account: {
                loginError: {
                    required: {
                        title: 'ข้อผิดพลาด',
                        text: 'ต้องเข้าสู่ระบบ!'
                    },
                    minLength: {
                        title: 'ข้อผิดพลาด',
                        text: 'การเข้าสู่ระบบที่ระบุสั้นเกินไป!'
                    },
                    maxLength: {
                        title: 'ข้อผิดพลาด',
                        text: 'การเข้าสู่ระบบที่ระบุยาวเกินไป!'
                    }
                },
                emailError: {
                    email: {
                        title: 'ข้อผิดพลาด',
                        text: 'อีเมลไม่ถูกต้อง!'
                    },
                    required: {
                        title: 'ข้อผิดพลาด',
                        text: 'ต้องระบุอีเมล!'
                    },
                    minLength: {
                        title: 'ข้อผิดพลาด',
                        text: 'อีเมลที่ระบุสั้นเกินไป!'
                    },
                    maxLength: {
                        title: 'ข้อผิดพลาด',
                        text: 'อีเมลที่ระบุยาวเกินไป!'
                    }
                },
                passwordError: {
                    required: {
                        title: 'ข้อผิดพลาด',
                        text: 'ต้องระบุรหัสผ่าน!'
                    },
                    minLength: {
                        title: 'ข้อผิดพลาด',
                        text: 'รหัสผ่านที่คุณป้อนสั้นเกินไป!'
                    },
                    maxLength: {
                        title: 'ข้อผิดพลาด',
                        text: 'รหัสผ่านที่ระบุยาวเกินไป!'
                    },
                    sameAs: {
                        title: 'ข้อผิดพลาด',
                        text: 'รหัสผ่านไม่ตรงกัน!'
                    }
                },
                forgotPassword: {
                    success: {
                        title: 'สำเร็จ',
                        text: 'คุณได้เปลี่ยนรหัสผ่านของคุณเรียบร้อยแล้ว!'
                    },
                    codeError: {
                        title: 'ข้อผิดพลาด',
                        text: 'รหัสไม่ถูกต้อง!'
                    }
                }
            },
            installGame: {
                badPath: {
                    title: 'ข้อผิดพลาด',
                    text: 'เส้นทางไม่ถูกต้อง!'
                }
            },
            getGift: { 'select-server': 'เลือกเซิร์ฟเวอร์!' },
            reverifyFiles: { text: 'При следующем запуске игры, все файлы будут автоматически проверены на целостность.' },
            gameAlive: {
                title: 'สำเร็จ',
                text: 'Вы успешно закрыли процессы игры!'
            }
        },
        modal: {
            close: 'การปิด',
            connect: {
                title: 'ручное подключение',
                ip: 'Адрес сервера',
                password: 'Пароль (если есть)',
                text: 'Адрес сервера должен быть в формате <mark>address:port</mark><br/>Например: <mark>127.0.0.1:7788</mark> или <mark>example.server.com:443</mark>',
                discard: 'ยกเลิก',
                connect: 'Подключиться'
            },
            runGameError: {
                title: 'ไม่สามารถเริ่มเกมได้',
                'reason-title': 'เหตุผล',
                'possibleSolutions-title': 'วิธีแก้ปัญหาที่เป็นไปได้',
                1000: {
                    reason: 'ล้มเหลวในการเรียกใช้ไฟล์หลายผู้เล่น',
                    possibleSolutions: {
                        tryAgain: 'ลองคลิกเล่นอีกครั้ง',
                        reboot: 'ลองรีสตาร์ทคอมพิวเตอร์ของคุณ'
                    }
                }
            },
            installGameError: {
                title: 'ติดตั้งเกมไม่สำเร็จ',
                'reason-title': 'เหตุผล',
                'possibleSolutions-title': 'วิธีแก้ปัญหาที่เป็นไปได้',
                1001: {
                    reason: 'การเข้าถึงไดเรกทอรีเกมล้มเหลว',
                    possibleSolutions: {
                        runAsAdmin: 'ลองเปิดตัวเรียกใช้งานในฐานะผู้ดูแลระบบ',
                        changeAccess: 'ลองเปลี่ยนสิทธิ์การเข้าถึงไดเรกทอรีเกม',
                        reboot: 'ลองรีสตาร์ทคอมพิวเตอร์ของคุณ'
                    }
                }
            },
            startGameError: {
                title: 'Ошибка при внедрении патчей',
                'reason-title': 'เหตุผล',
                'possibleSolutions-title': 'วิธีแก้ปัญหาที่เป็นไปได้',
                1001: {
                    reason: 'Не удалось внедрить патчи в игру',
                    possibleSolutions: {
                        runAsAdmin: 'ลองเปิดตัวเรียกใช้งานในฐานะผู้ดูแลระบบ',
                        changeAccess: 'Попробуйте запустить Rockstar Games Launcher отдельно',
                        reboot: 'ลองรีสตาร์ทคอมพิวเตอร์ของคุณ'
                    }
                }
            },
            notEnoughDiskSpace: {
                title: 'พื้นที่ดิสก์ไม่เพียงพอ',
                'reason-title': 'เหตุผล',
                'possibleSolutions-title': 'วิธีแก้ปัญหาที่เป็นไปได้',
                1002: {
                    reason: 'ดิสก์ใช้งานได้ {disk} ฟรี {freeSpace}',
                    reason2: 'ต้องมีขั้นต่ำ {requiredSpace}',
                    possibleSolutions: { freeSpace: 'เพิ่มพื้นที่ว่างในดิสก์ {disk}' }
                }
            },
            enchancedGTAV: {
                title: 'Неправильная версия GTA V',
                'reason-title': 'เหตุผล',
                'possibleSolutions-title': 'วิธีแก้ปัญหาที่เป็นไปได้',
                1003: {
                    reason: 'Вы используете GTA V Enchanced вместо GTA V Legacy',
                    possibleSolutions: {
                        steam_gtaLegacy: 'Если вы используете Steam, то вам нужно скачать Grand Theft Auto V Legacy',
                        epic_gtaLegacy: 'Если вы используете Epic Games, то вам нужно скачать обычную Grand Theft Auto V',
                        rgl_gtaLegacy: 'Если вы используете Rockstar Games Launcher, то вам нужно скачать Grand Theft Auto V Legacy'
                    }
                }
            },
            getGift: {
                'choose-server': 'เลือกเซิร์ฟเวอร์',
                description: 'ไอเท็มของขวัญมีอยู่ในทุกตัวอักษรของเซิร์ฟเวอร์เดียวที่คุณเลือก',
                get: 'รับ',
                close: 'ปิด',
                success: 'คุณได้รับของขวัญเรียบร้อยแล้ว!',
                error: {
                    1315: 'ไม่บ่อยนัก!',
                    1101: 'คุณยังไม่ได้เข้าสู่เกมจากตัวเรียกใช้งาน!',
                    1317: 'คุณได้รับของขวัญบนเซิร์ฟเวอร์นี้แล้ว!',
                    default: 'ข้อผิดพลาดที่ไม่ทราบสาเหตุ'
                }
            },
            BuyingKey: {
                buyingKey: 'ซื้อคีย์ GTA V Legacy',
                secure: 'ปลอดภัย',
                'text-ru': 'หน้าสำหรับคีย์เกม Grand Theft Auto V Legacy ในร้าน Rockstar จะเปิดขึ้นในไม่ช้า หลังจากชำระเงินแล้ว.',
                text: 'ต้องการรุ่นลิขสิทธิ์ Grand Theft Auto V Legacy. <br /> ซื้อได้จากร้านค้าอย่างเป็นทางการ:',
                'path-title': 'ระบุเส้นทางไปยังเกม',
                'click-to-choose': 'เลือกโฟลเดอร์เกม',
                'successful-select': 'เลือกเส้นทางเรียบร้อยแล้ว!',
                'enter-email': 'กรอกอีเมลของคุณ',
                selectPayment: 'โปรดเลือกวิธีชำระเงิน',
                'user-agreement': 'ฉันได้อ่าน <span id="rulesUrl">ข้อกำหนดของข้อเสนอแล้ว</span>',
                continue: 'ดำเนินการต่อ',
                cancel: 'ยกเลิก'
            },
            noGTA: {
                gtaNotFound: 'GTA V Legacy ไม่พบ',
                youHaveGTA: 'คุณมี GTA V Legacy หรือไม่?',
                text: 'การเล่นบน Majestic ต้องการรุ่นลิขสิทธิ์ของเกม Grand Theft Auto V Legacy คีย์เกมสามารถซื้อได้อย่างรวดเร็วและสะดวกในร้านของเรา.',
                buy: 'ไม่ซื้อ',
                have: 'ใช่มี',
                title: 'GTA V Legacy ไม่พบ',
                'path-title': 'ระบุเส้นทางไปยังเกม',
                'click-to-choose': 'เลือกโฟลเดอร์เกม',
                'successful-select': 'เลือกเส้นทางเรียบร้อยแล้ว!'
            },
            selectGTA: {
                gtaNotFound: 'เลือกเส้นทางไปยัง GTA V Legacy',
                text: 'การเล่นบน Majestic ต้องการรุ่นลิขสิทธิ์ของเกม Grand Theft Auto V Legacy โปรดเลือกเส้นทางไปยังเกมในคอมพิวเตอร์ของคุณ.',
                title: 'เลือกเส้นทางไปยัง GTA V Legacy',
                'path-title': 'ระบุเส้นทางไปยังเกม',
                'click-to-choose': 'เลือกโฟลเดอร์เกม'
            },
            rageUndefined: {
                HDD: 'ฮาร์ดไดรฟ์',
                available: 'พร้อมใช้งาน',
                'game-files': 'ไฟล์เกม',
                'required-space': 'จะใช้พื้นที่ว่างประมาณ {size} GB',
                'choose-disk-for-installation': 'เลือกดิสก์ที่จะติดตั้งไฟล์เกมบน',
                'searching-HDD': 'ค้นหาฮาร์ดไดรฟ์..',
                install: 'ติดตั้ง',
                set: 'เปลี่ยน'
            },
            remove: {
                title: 'ยืนยันการลบ',
                confirmation: 'คุณแน่ใจหรือไม่ว่าต้องการลบ <span class="value">{title}</span>?',
                accept: 'ใช่ลบเลย',
                cancel: 'ฉันเปลี่ยนใจ'
            },
            failPayment: {
                error: 'ข้อผิดพลาด',
                description: 'ขออภัยเกิดข้อผิดพลาด <br/> ในการประมวลผลการชำระเงินของคุณ <br/> <br/> โปรดลองอีกครั้ง <br/> หรือ <span id="supportUrl">ติดต่อฝ่ายสนับสนุน</span>.',
                back: 'ย้อนกลับ',
                'to-main-page': 'ไปที่หน้าแรก',
                payment: 'การชำระเงิน'
            },
            successPayment: {
                success: 'สำเร็จ',
                'replenish-account': 'คุณเติมเงินเข้าบัญชีของคุณแล้ว!',
                'replenish-account-on': 'คุณเติมเงินเข้าบัญชีของคุณ <span class="amount">{amount}</span>',
                'funds-credited-account': 'เงินจะเข้าบัญชี <br/> ของคุณในไม่ช้า',
                'to-main-page': 'ไปที่หน้าแรก',
                back: 'ย้อนกลับ',
                payment: 'การชำระเงิน'
            },
            RegionSelector: {
                selectRegion: 'เลือกภูมิภาค',
                discription: 'เซิร์ฟเวอร์ที่มีอยู่จะขึ้นอยู่กับเซิร์ฟเวอร์นั้นคุณสามารถเปลี่ยนตัวเลือกของคุณได้ตลอดเวลาในการตั้งค่า',
                avaiableServers: 'เซิร์ฟเวอร์ที่พร้อมใช้งาน',
                playStart: 'เริ่มเกม',
                regions: {
                    eu: 'ส่วนกลาง',
                    ru: 'CIS',
                    test: 'ทดสอบ'
                }
            },
            unknownBranch: {
                title: 'Не удалось найти тестовую ветку',
                description: 'При попытке скачать ветку {value} произошла ошибка, убедитесь что выбранная ветка существует и валидная'
            },
            incorrectToken: {
                title: 'Произошла ошибка во время скачивания',
                description: 'Не удалось скачать выбранную тестовую ветку, проверьте ваш CDN токен на валидность и повторите попытку'
            },
            gameAlive: {
                title: 'Игра уже запущена',
                confirmation: 'Хотите ли закрыть игру перед повторным запуском?',
                accept: 'Да, хочу',
                cancel: 'ยกเลิก'
            }
        },
        updater: {
            uploadedSize: '{uploadedSize} GB จาก {totalDownloadSize} GB',
            speed: '{downloadSpeed} MB/s',
            leftTime: 'โดยประมาณ {leftTime}',
            seconds: '{value} {value, plural, one{~ วินาที ~~ วินาที} few{~ วินาที} many{~ วินาที} other {~ วินาที}}~',
            minutes: '{value} {value, plural, one{~ นาที ~~ นาที} few{~ นาที} many{~ นาที} other {~ นาที}}~',
            'checking-for-updates': 'ตรวจสอบการอัพเดท',
            updating: 'กำลังดาวน์โหลดอัพเดท'
        },
        loader: {
            cancel: 'ยกเลิก',
            titles: {
                game: {
                    install: 'การติดตั้งไฟล์เกม',
                    verifying: 'กำลังตรวจสอบไฟล์',
                    starting: 'Запуск и патч игры',
                    backup: 'Резервное копирование файлов игры',
                    resolving: 'Обработка данных'
                },
                mods: { install: 'การติดตั้งการดัดแปลง {mod}' }
            },
            info: {
                speed: '{uploadedSize} GB จาก {totalDownloadSize} GB',
                leftTime: 'โดยประมาณ {leftTime}',
                downloadSpeed: '{value} МБ/с',
                seconds: '{value} {value, plural, one{~ วินาที ~~ วินาที} few{~ วินาที} many{~ วินาที} other {~ วินาที}}~',
                minutes: '{value} {value, plural, one{~ นาที ~~ นาที} few{~ นาที} many{~ นาที} other {~ นาที}}~'
            }
        },
        navbar: {
            play: 'เล่น',
            connect: 'Ввести IP',
            donate: 'ร้านค้า',
            news: 'ข่าวประชาสัมพันธ์',
            forum: 'ฟอรั่ม',
            mods: 'Mods',
            settings: 'ตั้งค่า',
            account: {
                signIn: 'เข้าสู่ระบบ',
                signUp: 'การลงทะเบียน',
                profileConfiguration: 'การตั้งค่าโปรไฟล์',
                signOut: 'ออกจากระบบ'
            },
            comingSoon: 'Скоро'
        },
        servers: {
            server: 'เซิร์ฟเวอร์',
            polski: 'โปแลนด์',
            deutsch: 'เยอรมัน',
            majestic: 'มาเจสติก',
            'play-now': 'กำลังเล่น',
            'recommend-for-beginners': 'คำแนะนำสำหรับผู้เริ่มต้น',
            'last-time-visited': 'เข้าชมครั้งล่าสุด',
            'all-servers': 'เซิร์ฟเวอร์ทั้งหมด',
            'in-other-languages': 'ในภาษาอื่นๆ',
            tooltip: {
                techWorks: 'กำลังดำเนินการทางเทคนิคบนเซิร์ฟเวอร์',
                seasonPassMultiplier: 'รับประสบการณ์ Winter Pass เพิ่มขึ้น {value}อีก %'
            },
            advantages: {
                gift: 'ของขวัญ',
                newServer: 'ใหม่',
                experience: '{value} ประสบการณ์ X',
                seasonPassMultiplier: 'X{value}'
            }
        },
        select: { 'enter-value': 'ป้อนค่า..' },
        donate: {
            back: 'ย้อนกลับ',
            'buy-coins': 'สกุลเงินซื้อ',
            cashbox: 'ซื้อเหรียญเพื่อเข้าสู่ระบบ {login} ใน {server} เซิร์ฟเวอร์',
            'choose-server': 'เลือกเซิร์ฟเวอร์',
            'enter-login': 'ป้อนชื่อผู้ใช้ของคุณ',
            'enter-sum': 'ใส่จำนวนเงิน',
            'enter-email': 'กรอกอีเมลของคุณ',
            'min-bonus-value': 'ตั้งแต่ {from}ถู',
            pay: 'ไปที่การชำระเงิน',
            'rules-checked': 'ฉันได้อ่าน <span id="rulesUrl">ข้อกำหนดของข้อเสนอ</span>',
            'rules-agree': 'ข้าพเจ้ายินยอมให้ Majestic RP ดำเนินการตามสัญญาทันทีโดยการเปิดใช้งานเนื้อหาดิจิทัล (รวมถึงสกุลเงินดิจิทัลและเนื้อหาใดๆที่เปิดใช้งานโดยใช้สกุลเงินดิจิทัล) ในบัญชีของข้าพเจ้าข้าพเจ้ายอมรับว่าข้าพเจ้าสูญเสียสิทธิ์ในการยกเลิกสัญญานี้และได้รับเงินคืนหลังจากเปิดใช้งานเนื้อหาดิจิทัลในบัญชีของข้าพเจ้า',
            info: 'ครอบครัวของฉัน',
            exchange: 'อัตราแลกเปลี่ยน: R1',
            description: 'เหรียญมาเจสติกสามารถใช้ในการซื้อ: การขนส่งที่ไม่เหมือนใครอสังหาริมทรัพย์ธุรกิจสกุลเงินในเกมและอื่นๆอีกมากมายหลังจากชำระเงินแล้ว MC จะถูกโอนไปยังบัญชีของคุณบนเซิร์ฟเวอร์เกมโดยอัตโนมัติ',
            warning: 'หากคุณป้อนข้อมูลไม่ถูกต้องคุณจะไม่สามารถโอนเงินไปยังบัญชีที่ต้องการได้',
            'contract-offer': 'ข้อตกลงข้อเสนอ',
            selectPayment: 'โปรดเลือกวิธีชำระเงิน',
            selectPaymentOther: 'วิธีชำระเงินอื่นๆ',
            replenish: {
                cardTitles: {
                    card_ru1: 'บัตรของธนาคารรัสเซีย 1',
                    crypto: 'สกุลเงินดิจิทัลอื่นๆ',
                    erc20: 'USDT Tether (ERC20)',
                    trc20: 'USDT Tether (TRC20)',
                    kinguin: 'คีย์การเปิดใช้งาน',
                    steam: 'ชำระเงินด้วยสกิน Steam'
                },
                titles: {
                    card_ru1: 'บัตรของธนาคารรัสเซีย 1',
                    card_ru2: 'บัตรของธนาคารรัสเซีย 2',
                    card_ru3: 'บัตรของธนาคารรัสเซีย 3',
                    sbp: 'ระบบการชำระเงินที่เร็วขึ้น',
                    qiwi: 'Qiwi',
                    yandexpay: 'Yandex Pay',
                    card_ua: 'บัตรของธนาคารยูเครน',
                    card_world1: 'บัตรต่างประเทศ 1',
                    card_world2: 'บัตรต่างประเทศ 2',
                    paypal: 'PayPal',
                    kinguin: 'คีย์การเปิดใช้งาน Kinguin',
                    steam: 'ไอเท็มจาก Steam',
                    yoomoney: '̍money',
                    bitcoin: 'Bitcoin',
                    ethereum: 'อีเธอเรียม',
                    trc20: 'USDT Tether (TRC20)',
                    erc20: 'USDT Tether (ERC20)',
                    crypto: 'สกุลเงินดิจิทัลอื่นๆ',
                    direct_banking: 'การชำระเงินจากบัญชีธนาคาร',
                    cards_eu: 'การชำระเงินด้วยบัตร 1',
                    paysafecard: 'paysafecard',
                    blik: 'blik',
                    paypalych: 'การชำระเงินด้วยบัตร 2',
                    dolyame: 'การชำระเงินด้วยหุ้น'
                }
            }
        },
        settings: {
            'on-main': 'กลับไปที่หน้าแรก',
            categories: {
                main: 'ทั่วไป',
                patch: 'แพทช์',
                additional: 'ไม่บังคับ',
                developer: 'Разработчик'
            },
            descriptions: {
                shadowplayOverlay: 'ฟีเจอร์นี้ช่วยให้คุณบันทึกวิดีโอและจับภาพหน้าจอในเกมได้ <br> หากคุณประสบปัญหาเกี่ยวกับเกม <span class="highlight">(ขัดข้องค้างฯลฯ)</span>ให้ลองปิดใช้งานฟีเจอร์นี้',
                discordOverlay: 'ฟีเจอร์นี้ช่วยให้คุณเปิดเมนู Discord ในเกมได้ <br> หากคุณประสบปัญหาเกี่ยวกับเกม <span class="highlight">(ขัดข้องค้างฯลฯ) ให้</span>ลองปิดใช้งานฟีเจอร์นี้',
                devSettings: 'Подсказка: Чтобы сохранить данные в поле ввода, нажмите ENTER'
            },
            actions: {
                fix: 'ซ่อมแซม',
                clearCache: 'ล้าง',
                changeMAJESTICFolder: 'เปลี่ยน',
                changeGTAFolder: 'เปลี่ยน',
                fixPermissions: 'ซ่อมแซม',
                reverifyFiles: 'Проверить'
            },
            titles: {
                region: 'ภูมิภาค',
                branch: 'Ветка',
                cdnToken: 'CDN Токен',
                enableDebug: 'Включить отладку',
                skipBranchCheck: 'Пропустить проверку ветки',
                language: 'ภาษา',
                interfaceVolume: 'ปริมาณอินเทอร์เฟซ',
                hideAfterBootGame: 'ยุบตัวเรียกใช้งานหลังจากเปิดเกม',
                downloadInLauncher: 'ดาวน์โหลดไฟล์เกมในตัวเรียกใช้งาน',
                enableCEFGPU: 'การเร่งความเร็วฮาร์ดแวร์อินเทอร์เฟซ',
                patchGTA: 'นำแพตช์ไปใช้กับเกม',
                clearCache: 'ล้างแคชเกม',
                forceClearCache: 'ล้างแคชเกมอย่างสมบูรณ์',
                changeMAJESTICFolder: 'เปลี่ยนสถานที่ติดตั้ง Majestic RP',
                changeGTAFolder: 'เปลี่ยนตำแหน่งการติดตั้ง GTA V Legacy',
                fixPermissions: 'ซ่อมแซมสิทธิ์การเข้าถึงไฟล์เกม',
                reverifyFiles: 'Принудительно проверить файлы игры',
                shadowplayOverlay: 'เปิดใช้งาน NVIDIA Shadowplay Overlay',
                discordOverlay: 'เปิดใช้งาน Discord Overlay',
                pass: 'รหัสผ่าน',
                range: 'ช่วง'
            },
            placeholders: { enterPass: 'ใส่รหัสผ่าน' }
        },
        account: {
            signIn: {
                logInAccount: 'เข้าสู่ระบบบัญชีของคุณ',
                signIn: 'เข้าสู่ระบบ',
                signUp: 'การลงทะเบียน',
                enterEmail: 'กรอกอีเมล',
                enterPass: 'ใส่รหัสผ่าน',
                forgotPassword: 'ลืมรหัสผ่านใช่ไหม?'
            },
            signUp: {
                signUp: 'การลงทะเบียน',
                enterLogin: 'ป้อนชื่อผู้ใช้ของคุณ',
                enterEmail: 'กรอกอีเมล',
                enterPass: 'ใส่รหัสผ่าน',
                repeatPass: 'ใส่รหัสผ่านอีกครั้ง',
                alreadyCreateAccount: 'มีบัญชีอยู่แล้วใช่ไหม?',
                'agree-newsletters': 'ฉันตกลงที่จะรับจดหมายข่าว',
                'accept-rules': 'ฉันได้อ่านและยอมรับกฎ <span id="rulesUrl">เซิร์ฟเวอร์</span> แล้ว',
                'confirm-age': 'ข้าพเจ้าขอรับรองว่าข้าพเจ้ามีอายุอย่างน้อย 18 ปี'
            },
            profileConfiguration: {},
            forgotPassword: {
                passwordReset: 'รีเซ็ตรหัสผ่าน',
                enterEmail: 'กรอกอีเมล',
                enterPass: 'ใส่รหัสผ่าน',
                repeatPass: 'ใส่รหัสผ่านอีกครั้ง',
                continue: 'ดำเนินการต่อ',
                cancel: 'ยกเลิก',
                enterCode: 'ป้อนรหัสที่ส่งไปยังอีเมลของคุณ '
            }
        },
        mods: {
            sort: 'การเรียงลำดับ',
            sortItems: {
                graphics: 'กราฟิก',
                interface: 'อินเตอร์เฟซ',
                sounds: 'เสียง',
                other: 'อื่นๆ'
            },
            install: 'ติดตั้ง',
            uninstall: 'ลบออก',
            more: 'ดูข้อมูลเพิ่มเติม',
            back: 'ย้อนกลับ',
            empty: 'ยังไม่มีอะไรที่นี่ติดตามข่าวเพื่อไม่ให้พลาดม็อดใหม่ๆ',
            systemRequirements: 'ความต้องการของระบบ',
            operatingSystem: 'ระบบปฏิบัติการ',
            processor: 'ตัวประมวลผล',
            ram: 'แรม',
            videoCard: 'การ์ดแสดงผล',
            soundCard: 'การ์ดเสียง',
            freeDiskSpace: 'พื้นที่ว่างในฮาร์ดดิสก์',
            berkleyRedux: {
                title: 'Berkley Redux',
                description: 'การดัดแปลงกราฟิกฟรีจาก Berkley บนพื้นฐานของ Redux'
            },
            evolvedGraphics: {
                title: 'กราฟิกที่พัฒนาแล้ว',
                description: 'ม็อดระดับโลกที่ปรับปรุงกราฟิกให้อยู่ในระดับของเกมสมัยใหม่แสงเกมสภาพอากาศเอฟเฟกต์และหลังการประมวลผลถูกสร้างขึ้นเพื่อความสมบูรณ์แบบและด้วยเอฟเฟ็กต์กราฟิกของ eNB ทำให้ภาพในเกมมีความชัดเจนและเหมือนจริง'
            },
            atlasPopularPlaces: {
                title: 'แอตลาสกับสถานที่ยอดนิยม',
                description: 'ภาพซ้อนด้านบนของแผนที่คือตำแหน่งที่ตั้งของพื้นที่สีเขียวชื่อสถานที่ยอดนิยมและอื่นๆ'
            },
            loadingScreens: {
                title: 'กำลังโหลดหน้าจอ',
                description: ''
            },
            strongMotionBlur: {
                title: 'เบลอการเคลื่อนไหวอย่างรุนแรง',
                description: 'สำหรับคนรักภาพยนต์'
            },
            purpleTracer: {
                title: 'ตัวติดตามสีม่วง',
                description: 'การแสดงต้องดำเนินต่อไป! ตอนนี้เมื่อยิงจากอาวุธผลกระทบที่สดใสของตลับหมึกติดตามจะปรากฏขึ้น'
            }
        },
        discord: {
            state: 'เล่นบนมาเจสติก',
            details: 'การใช้ตัวเปิด'
        },
        tray: {
            open: 'เปิด',
            close: 'ปิด'
        },
        gift: {
            'pickUp-gift': 'อย่าลืมรับของขวัญของคุณ!',
            get: 'รับ'
        },
        competition: {
            title: 'แจกของรางวัล',
            'sub-title': 'บีเอ็มดับเบิลยู M3 ตัวจริง',
            participate: 'เข้าร่วมเลย'
        },
        crash: {
            title: 'критическая ошибка',
            problemTitle: 'Если проблема повторяется, попробуйте следующие шаги:',
            sol1: 'Перезапустите Majestic RP или персональный компьютер',
            sol2: 'Запустите Majestic RP от имени администратора',
            sol3: 'Проверьте целостность фаилов игры',
            probt1: 'Не помогает? Проверьте нашу страницу',
            probt2: 'частых проблем',
            probt3: 'или',
            probt4: 'обратитесь в поддержку',
            crashId: 'ID краша:',
            errorCode: 'Код ошибки:',
            logDir: 'Папка с логами',
            gtavDir: 'Папка GTA V',
            crashReport: 'Файл краш-репорта',
            exit: 'выйти',
            reload: 'перезапустить',
            crashIdCopy: 'ID краша скопирован в буфер обмена',
            errorCodeCopy: 'Код ошибки скопирован в буфер обмена'
        }
    }
};