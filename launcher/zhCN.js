export default {
    launcher: {
        'servers-list': '服务器列表',
        connection: '连接',
        milliseconds: '女士',
        play: '游戏',
        notifications: {
            error: '错误',
            warning: '注意',
            success: '成功',
            mod: {
                uninstall: {
                    title: '成功',
                    text: '模块已成功删除！'
                }
            },
            cache: {
                clear: {
                    title: '成功',
                    text: '缓存已成功清除'
                }
            },
            'permissions-fix': {
                title: '成功',
                text: '成功修复游戏文件权限'
            },
            account: {
                loginError: {
                    required: {
                        title: '错误',
                        text: '登录是必需的！'
                    },
                    minLength: {
                        title: '错误',
                        text: '指定的登录名太短！'
                    },
                    maxLength: {
                        title: '错误',
                        text: '指定的登录名太长！'
                    }
                },
                emailError: {
                    email: {
                        title: '错误',
                        text: '电子邮箱无效！'
                    },
                    required: {
                        title: '错误',
                        text: '电子邮箱为必填项！'
                    },
                    minLength: {
                        title: '错误',
                        text: '指定的电子邮件太短！'
                    },
                    maxLength: {
                        title: '错误',
                        text: '指定的电子邮件太长！'
                    }
                },
                passwordError: {
                    required: {
                        title: '错误',
                        text: '密码为必填项！'
                    },
                    minLength: {
                        title: '错误',
                        text: '您输入的密码太短！'
                    },
                    maxLength: {
                        title: '错误',
                        text: '指定的密码太长！'
                    },
                    sameAs: {
                        title: '错误',
                        text: '密码不匹配！'
                    }
                },
                forgotPassword: {
                    success: {
                        title: '成功',
                        text: '您已成功更改密码！'
                    },
                    codeError: {
                        title: '错误',
                        text: '无效的代码！'
                    }
                }
            },
            installGame: {
                badPath: {
                    title: '错误',
                    text: '路径无效！'
                }
            },
            getGift: { 'select-server': '选择一个服务器！' },
            reverifyFiles: { text: '下次运行游戏时，将自动检查所有文件的完整性。' },
            gameAlive: {
                title: '成功',
                text: '您已成功关闭游戏进程！'
            }
        },
        modal: {
            close: '关闭',
            connect: {
                title: '手动连接',
                ip: '服务器地址',
                password: '密码（如有）',
                text: '服务器地址的格式必须是<mark>地址:端口</mark><br/>，例如<mark>*********.1:7788</mark>或<mark>example.server.com:443</mark>',
                discard: '取消',
                connect: '连接'
            },
            runGameError: {
                title: '开始游戏失败',
                'reason-title': '原因',
                'possibleSolutions-title': '可能的解决方案',
                1000: {
                    reason: '多人游戏文件运行失败',
                    possibleSolutions: {
                        tryAgain: '尝试再次单击播放',
                        reboot: '尝试重新启动计算机'
                    }
                }
            },
            installGameError: {
                title: '安装游戏失败',
                'reason-title': '原因',
                'possibleSolutions-title': '可能的解决方案',
                1001: {
                    reason: '访问游戏目录失败',
                    possibleSolutions: {
                        runAsAdmin: '尝试以管理员身份运行启动器',
                        changeAccess: '尝试更改游戏目录 "Games "的权限',
                        reboot: '尝试重启电脑'
                    }
                }
            },
            startGameError: {
                title: '执行修补程序时出错',
                'reason-title': '原因',
                'possibleSolutions-title': '可能的解决方案',
                1001: {
                    reason: '未能在游戏中实施补丁',
                    possibleSolutions: {
                        runAsAdmin: '尝试以管理员身份运行启动器',
                        changeAccess: '尝试单独运行 Rockstar Games Launcher',
                        reboot: '尝试重新启动计算机'
                    }
                }
            },
            notEnoughDiskSpace: {
                title: '椎间盘空间不足',
                'reason-title': '原因',
                'possibleSolutions-title': '可能的解决方案',
                1002: {
                    reason: '{disk} {freeSpace}光盘免费',
                    reason2: '{requiredSpace}最低要求',
                    possibleSolutions: { freeSpace: '{disk}释放磁盘空间' }
                }
            },
            enchancedGTAV: {
                title: '错误版本的《GTA V',
                'reason-title': '原因',
                'possibleSolutions-title': '可能的解决方案',
                1003: {
                    reason: '您使用的是《GTA V Enchanced》，而不是《GTA V Legacy》。',
                    possibleSolutions: {
                        steam_gtaLegacy: '如果您使用 Steam，则需要下载《侠盗猎车手 5：遗产》。',
                        epic_gtaLegacy: '如果您使用的是 Epic Games，则需要下载常规的《侠盗猎车手 5',
                        rgl_gtaLegacy: '如果使用 Rockstar Games Launcher，则需要下载《侠盗猎车手 5：遗产》。'
                    }
                }
            },
            getGift: {
                'choose-server': '选择一个服务器',
                description: '您选择的一台服务器的所有角色都可以获得礼物',
                get: '领取',
                close: '关闭',
                success: '您已成功领取礼物！',
                error: {
                    1315: '不那么常！',
                    1101: '你没有从启动器登录游戏！',
                    1317: '您已在本服务器上收到一份礼物！',
                    default: '未知错误'
                }
            },
            BuyingKey: {
                buyingKey: '购买 GTA V Legacy 密钥',
                secure: '安全',
                'text-ru': '付款后，Rockstar 商店中的 "侠盗猎车手 5 遗产 "游戏密钥页面将很快打开。',
                text: '需要《侠盗猎车手 5：传奇》的授权版本。 <br /> 请从官方商店购买：',
                'path-title': '指定游戏路径',
                'click-to-choose': '选择包含游戏的文件夹',
                'successful-select': '路径已成功选择！',
                'enter-email': '输入您的电子邮件',
                selectPayment: '选择付款方式',
                'user-agreement': '我已阅读 <span id="rulesUrl">优惠的条款和条件</span>',
                continue: '待续',
                cancel: '取消'
            },
            noGTA: {
                gtaNotFound: '未找到《GTA V》遗产',
                youHaveGTA: '您买过 GTA V Legacy 吗？',
                text: '玩 Majestic 需要《侠盗猎车手 5》正版授权。游戏密钥可在我们的商店中方便快捷地购买。',
                buy: '不，购买',
                have: '有',
                title: '未找到《GTA V》遗产',
                'path-title': '指定游戏路径',
                'click-to-choose': '选择包含游戏的文件夹',
                'successful-select': '路径已成功选择！'
            },
            selectGTA: {
                gtaNotFound: '选择《GTA V》遗产的路径',
                text: '玩 Majestic 需要《侠盗猎车手 5：传奇》的授权版本。选择电脑上的游戏路径。',
                title: '选择《GTA V》遗产的路径',
                'path-title': '指定游戏路径',
                'click-to-choose': '选择包含游戏的文件夹'
            },
            rageUndefined: {
                HDD: '硬盘驱动器',
                available: '可供选择',
                'game-files': '游戏文件',
                'required-space': '它将需要大约 {size} GB的可用空间',
                'choose-disk-for-installation': '选择要安装游戏文件的磁盘',
                'searching-HDD': '搜索硬盘驱动器。..',
                install: '安装/安装',
                set: '更改'
            },
            remove: {
                title: '确认删除',
                confirmation: '您确定要删除 <span class="value">{title}</span>吗？',
                accept: '是的，删除',
                cancel: '我改变主意了'
            },
            failPayment: {
                error: '错误',
                description: '很遗憾，在处理您的付款时出现错误 <br/> 。 <br/> <br/> 请重试 <br/> 或 <span id="supportUrl">联系支持部门</span>。',
                back: '返回',
                'to-main-page': '转到主页',
                payment: '付款'
            },
            successPayment: {
                success: '成功',
                'replenish-account': '您已为账号充值！',
                'replenish-account-on': '您在 <span class="amount">{amount}</span>之前补充了您的帐户',
                'funds-credited-account': '款项将很快存入您的 <br/> 账户',
                'to-main-page': '转到主页',
                back: '返回',
                payment: '付款'
            },
            RegionSelector: {
                selectRegion: '选择区域',
                discription: '可用的服务器取决于它。您可以随时在设置中更改您的选择。',
                avaiableServers: '可用服务器',
                playStart: '开始游戏',
                regions: {
                    eu: '全球',
                    ru: '独联体',
                    test: '测试'
                }
            },
            unknownBranch: {
                title: '找不到测试分支',
                description: '尝试下载 {value} 分支时发生错误，请确保所选分支存在且有效'
            },
            incorrectToken: {
                title: '下载过程中出现错误',
                description: '下载所选测试分支失败，请检查 CDN 标记是否有效，然后重试'
            },
            gameAlive: {
                title: '游戏已经推出',
                confirmation: '您想先关闭游戏再重新启动吗？',
                accept: '是的，我想',
                cancel: '取消'
            }
        },
        updater: {
            uploadedSize: '{uploadedSize} GB/ {totalDownloadSize} GB',
            speed: '{downloadSpeed} MB/s',
            leftTime: '剩余约 {leftTime}',
            seconds: '{value} {value, plural, one{秒} few{秒} many{秒} other {秒}}',
            minutes: '{value} {value, plural, one{分钟} few{分钟} many{分钟} other {分钟}}',
            'checking-for-updates': '检查更新',
            updating: '正在下载更新'
        },
        loader: {
            cancel: '取消',
            titles: {
                game: {
                    install: '正在安装游戏文件',
                    verifying: '正在检查文件',
                    starting: '启动和修补游戏',
                    backup: '备份游戏文件',
                    resolving: '数据处理'
                },
                mods: { install: '安装改装 {mod}' }
            },
            info: {
                speed: '{uploadedSize} GB/ {totalDownloadSize} GB',
                leftTime: '剩余约 {leftTime}',
                downloadSpeed: '{value} 兆字节/秒',
                seconds: '{value} {value, plural, one{秒} few{秒} many{秒} other {秒}}',
                minutes: '{value} {value, plural, one{分钟} few{分钟} many{分钟} other {分钟}}'
            }
        },
        navbar: {
            play: '播放',
            connect: '输入 IP',
            donate: '商店',
            news: '新闻',
            forum: '论坛',
            mods: '改装配件',
            settings: '设置',
            account: {
                signIn: '登入',
                signUp: '注册',
                profileConfiguration: '个人资料设置',
                signOut: '退出'
            },
            comingSoon: '很快'
        },
        servers: {
            server: '服务器',
            polski: 'Polish',
            deutsch: 'German',
            majestic: '雄伟',
            'play-now': '正在播放',
            'recommend-for-beginners': '对初学者的建议',
            'last-time-visited': '最后一次访问',
            'all-servers': '所有伺服器',
            'in-other-languages': '其他语言版本',
            tooltip: {
                techWorks: '服务器正在进行技术工作',
                seasonPassMultiplier: '{value}获得的冬季通行证经验增加 %'
            },
            advantages: {
                gift: '礼品',
                newServer: '新',
                experience: 'X{value} 经验',
                seasonPassMultiplier: '{value}X'
            }
        },
        select: { 'enter-value': '输入值。..' },
        donate: {
            back: '返回',
            'buy-coins': '购买货币',
            cashbox: '在 {server} 台服务器上购买金币登录 {login}',
            'choose-server': '选择一个服务器',
            'enter-login': '输入您的用户名',
            'enter-sum': '输入金额',
            'enter-email': '输入您的电子邮件地址',
            'min-bonus-value': '从 {from}擦。',
            pay: '前往付款',
            'rules-checked': '我已阅读优惠的 <span id="rulesUrl">个条款</span>',
            'rules-agree': '我同意Majestic RP通过激活我的帐户中的数字内容（包括数字货币和使用数字货币激活的任何内容）立即执行合同。我接受，在激活帐户中的数字内容后，我失去终止本合同并获得退款的权利。',
            info: '我的家人',
            exchange: '汇率： R1',
            description: 'Majestic Coins可用于购买：独特的运输、房地产、商业、游戏内货币等。付款后， MC将自动记入您在游戏服务器上的帐户。',
            warning: '如果您输入的数据不正确，您将无法将资金转入所需的账户。',
            'contract-offer': '报价协议',
            selectPayment: '请选择付款方式',
            selectPaymentOther: '其他付款方式',
            replenish: {
                cardTitles: {
                    card_ru1: '俄罗斯银行卡1',
                    crypto: '其他加密货币',
                    erc20: 'USDT Tether （ ERC20 ）',
                    trc20: 'USDT Tether (TRC20)',
                    kinguin: '激活密钥',
                    steam: '使用Steam皮肤付款'
                },
                titles: {
                    card_ru1: '俄罗斯银行卡1',
                    card_ru2: '俄罗斯银行卡2',
                    card_ru3: '俄罗斯银行卡3',
                    sbp: '更快的支付系统',
                    qiwi: 'Qiwi',
                    yandexpay: 'Yandex Pay',
                    card_ua: '乌克兰银行卡',
                    card_world1: '外国卡牌1',
                    card_world2: '外卡2',
                    paypal: 'PayPal',
                    kinguin: 'Kinguin激活密钥',
                    steam: '来自Steam的物品',
                    yoomoney: '金钱',
                    bitcoin: '比特币',
                    ethereum: '以太坊',
                    trc20: 'USDT Tether (TRC20)',
                    erc20: 'USDT Tether （ ERC20 ）',
                    crypto: '其他加密货币',
                    direct_banking: '银行账户付款',
                    cards_eu: '卡支付1',
                    paysafecard: 'paysafecard',
                    blik: 'blik',
                    paypalych: '银行卡付款2',
                    dolyame: '分期付款'
                }
            }
        },
        settings: {
            'on-main': '返回首页',
            categories: {
                main: '基本',
                patch: '补丁',
                additional: '此外',
                developer: '开发人员'
            },
            descriptions: {
                shadowplayOverlay: '此功能允许您在游戏中录制视频和截图。 <br> 如果您在游戏 <span class="highlight">（崩溃、冻结等）</span>，请尝试禁用此功能。',
                discordOverlay: '此功能允许您在游戏中打开 Discord 菜单。 <br> 如果您在游戏 <span class="highlight">（崩溃、挂起等）</span>，请尝试禁用此功能。',
                devSettings: '提示：要保存输入框中的数据，请按 ENTER 键'
            },
            actions: {
                fix: '修复',
                clearCache: '清晰',
                changeMAJESTICFolder: '改变',
                changeGTAFolder: '改变',
                fixPermissions: '修复',
                reverifyFiles: '查看'
            },
            titles: {
                region: '地区',
                branch: '分支机构',
                cdnToken: 'CDN 令牌',
                enableDebug: '启用调试',
                skipBranchCheck: '跳过分支检查',
                language: '语言',
                interfaceVolume: '接口音量',
                hideAfterBootGame: '游戏启动后折叠启动器',
                downloadInLauncher: '在启动器中下载游戏文件',
                enableCEFGPU: '接口硬件加速',
                patchGTA: '将补丁应用于游戏',
                clearCache: '清除游戏缓存',
                forceClearCache: '完全清除游戏缓存',
                changeMAJESTICFolder: '更改 Majestic RP 的安装位置',
                changeGTAFolder: '更改《GTA V》遗产的安装位置',
                fixPermissions: '修复游戏文件的访问权限',
                reverifyFiles: '被迫检查游戏文件',
                shadowplayOverlay: '启用英伟达™（NVIDIA®）Shadowplay 叠加功能',
                discordOverlay: '启用 Discord 叠加',
                pass: '密码',
                range: '范围'
            },
            placeholders: { enterPass: '输入密码' }
        },
        account: {
            signIn: {
                logInAccount: '登录您的账号',
                signIn: '登入',
                signUp: '注册',
                enterEmail: '输入电子邮箱',
                enterPass: '输入密码',
                forgotPassword: '忘记密码？'
            },
            signUp: {
                signUp: '注册',
                enterLogin: '输入您的用户名',
                enterEmail: '输入电子邮箱',
                enterPass: '输入密码',
                repeatPass: '重复密码',
                alreadyCreateAccount: '已经有账号了？',
                'agree-newsletters': '我同意接收时事通讯',
                'accept-rules': '我已阅读并同意 <span id="rulesUrl">服务器</span> 规则',
                'confirm-age': '我证明我已年满18周岁'
            },
            profileConfiguration: {},
            forgotPassword: {
                passwordReset: '密码重置',
                enterEmail: '输入电子邮箱',
                enterPass: '输入密码',
                repeatPass: '重复密码',
                continue: '继续',
                cancel: '取消',
                enterCode: '输入发送到您电子邮箱的代码 '
            }
        },
        mods: {
            sort: '排序',
            sortItems: {
                graphics: '图形',
                interface: '接口',
                sounds: '声音',
                other: '其他'
            },
            install: '安装',
            uninstall: '移除',
            more: '了解更多',
            back: '返回',
            empty: '这里没有任何内容，请关注新闻，以免错过新的改装配件',
            systemRequirements: '系统要求',
            operatingSystem: '操作系统',
            processor: '处理器',
            ram: 'RAM',
            videoCard: '显卡',
            soundCard: '声卡',
            freeDiskSpace: '可用硬盘空间',
            berkleyRedux: {
                title: 'Berkley Redux',
                description: '基于Redux的Berkley免费图形修改。'
            },
            evolvedGraphics: {
                title: '进化图形',
                description: '将图形提升到现代游戏水平的全局模组。游戏灯光、天气、效果和后期处理完美搭建，得益于eNB的图形效果，游戏中的画面变得清晰逼真。'
            },
            atlasPopularPlaces: {
                title: '热门景点的地图集',
                description: '地图顶部叠加了绿色区域的位置、热门地点的名称等'
            },
            loadingScreens: {
                title: '正在加载屏幕',
                description: ''
            },
            strongMotionBlur: {
                title: '严重运动模糊',
                description: '适合电影影像爱好者'
            },
            purpleTracer: {
                title: '紫色示踪剂',
                description: '演出必须继续！现在，从武器射击时，会出现曳光弹的明亮效果。'
            }
        },
        discord: {
            state: '威严的戏剧',
            details: '使用启动器'
        },
        tray: {
            open: '打开',
            close: '关闭'
        },
        gift: {
            'pickUp-gift': '别忘了领取您的礼物！',
            get: '领取'
        },
        competition: {
            title: '我们正在抽奖',
            'sub-title': '真正的宝马 M3',
            participate: '参与'
        },
        crash: {
            title: '严重错误',
            problemTitle: '如果问题再次出现，请尝试以下步骤：',
            sol1: '重新启动 Majestic RP 或个人电脑',
            sol2: '以管理员身份运行 Majestic RP',
            sol3: '检查游戏文件的完整性',
            probt1: '没有帮助？查看我们的页面',
            probt2: '常见问题',
            probt3: '或',
            probt4: '联系支持',
            crashId: '碰撞 ID：',
            errorCode: '错误代码：',
            logDir: '日志文件夹',
            gtavDir: 'GTA V》文件夹',
            crashReport: '碰撞报告文件',
            exit: '出',
            reload: '重新启动',
            crashIdCopy: '将碰撞 ID 复制到剪贴板',
            errorCodeCopy: '错误代码复制到剪贴板'
        }
    }
};