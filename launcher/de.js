export default {
    launcher: {
        'servers-list': '<PERSON>e der Server',
        connection: 'Verbindung',
        milliseconds: 'ms',
        play: '<PERSON><PERSON><PERSON>',
        notifications: {
            error: '<PERSON><PERSON>',
            warning: 'Achtung',
            success: 'Erfolg',
            mod: {
                uninstall: {
                    title: 'Erfolg',
                    text: 'Mod erfolgreich gelöscht!'
                }
            },
            cache: {
                clear: {
                    title: 'Erfolg',
                    text: '<PERSON><PERSON> erfolgreich geleert'
                }
            },
            'permissions-fix': {
                title: '<PERSON><PERSON>olgreich',
                text: 'Die Zugriffsrechte auf die Spieldateien wurden erfolgreich repariert'
            },
            account: {
                loginError: {
                    required: {
                        title: '<PERSON><PERSON>',
                        text: 'Bitte geben Sie den Login ein!'
                    },
                    minLength: {
                        title: '<PERSON><PERSON>',
                        text: 'Der angegebene Login ist zu kurz!'
                    },
                    maxLength: {
                        title: '<PERSON><PERSON>',
                        text: 'Der angegebene Login ist zu lang!'
                    }
                },
                emailError: {
                    email: {
                        title: '<PERSON><PERSON>',
                        text: 'Ung<PERSON>ltige E-Mail-Adresse!'
                    },
                    required: {
                        title: '<PERSON><PERSON>',
                        text: 'Bitte geben Sie die E-Mail ein!'
                    },
                    minLength: {
                        title: 'Fehler',
                        text: 'Die angegebene E-Mail ist zu kurz!'
                    },
                    maxLength: {
                        title: 'Fehler',
                        text: 'Die angegebene E-Mail ist zu lang!'
                    }
                },
                passwordError: {
                    required: {
                        title: 'Fehler',
                        text: 'Bitte geben Sie das Passwort ein!'
                    },
                    minLength: {
                        title: 'Fehler',
                        text: 'Das eingegebene Passwort ist zu kurz!'
                    },
                    maxLength: {
                        title: 'Fehler',
                        text: 'Das angegebene Passwort ist zu lang!'
                    },
                    sameAs: {
                        title: 'Fehler',
                        text: 'Passwörter stimmen nicht überein!'
                    }
                },
                forgotPassword: {
                    success: {
                        title: 'Erfolg',
                        text: 'Sie haben Ihr Passwort erfolgreich geändert!'
                    },
                    codeError: {
                        title: 'Fehler',
                        text: 'Ungültiger Code!'
                    }
                }
            },
            installGame: {
                badPath: {
                    title: 'Fehler',
                    text: 'Ungültiger Pfad!'
                }
            },
            getGift: { 'select-server': 'Wählen Sie einen Server aus!' },
            reverifyFiles: { text: 'Wenn du das Spiel das nächste Mal startest, werden alle Dateien automatisch auf ihre Integrität überprüft.' },
            gameAlive: {
                title: 'Erfolg',
                text: 'Du hast die Spielprozesse erfolgreich abgeschlossen!'
            }
        },
        modal: {
            close: 'Schließen',
            connect: {
                title: 'manuelle Verbindung',
                ip: 'Server Adresse',
                password: 'Passwort (falls vorhanden)',
                text: 'Die Serveradresse muss im Format <mark>Adresse:Port</mark><br/>angegeben werden. Zum Beispiel: <mark>*********.1:7788</mark> oder <mark>example.server.com:443</mark>',
                discard: 'Abbruch',
                connect: 'Verbinde'
            },
            runGameError: {
                title: 'Das Spiel konnte nicht gestartet werden',
                'reason-title': 'Grund',
                'possibleSolutions-title': 'Mögliche Lösungen',
                1000: {
                    reason: 'Die Multiplayer-Datei konnte nicht gestartet werden',
                    possibleSolutions: {
                        tryAgain: 'Klicke erneut auf „Spielen“.',
                        reboot: 'Versuchen Sie, Ihren Computer neu zu starten'
                    }
                }
            },
            installGameError: {
                title: 'Das Spiel konnte nicht installiert werden',
                'reason-title': 'Grund',
                'possibleSolutions-title': 'Mögliche Lösungen',
                1001: {
                    reason: 'Zugriff auf das Spielverzeichnis konnte nicht gewährt werden',
                    possibleSolutions: {
                        runAsAdmin: 'Versuche, den Launcher als Administrator auszuführen',
                        changeAccess: 'Versuchen Sie, die Zugriffsrechte für das Verzeichnis "Games" zu ändern',
                        reboot: 'Versuche deinen Computer neu zu starten'
                    }
                }
            },
            startGameError: {
                title: 'Fehler bei der Implementierung von Patches',
                'reason-title': 'Grund',
                'possibleSolutions-title': 'Mögliche Lösungen',
                1001: {
                    reason: 'Patches nicht in das Spiel implementiert',
                    possibleSolutions: {
                        runAsAdmin: 'Versuche, den Launcher als Administrator auszuführen',
                        changeAccess: 'Versuche, den Rockstar Games Launcher separat auszuführen',
                        reboot: 'Versuchen Sie, Ihren Computer neu zu starten'
                    }
                }
            },
            notEnoughDiskSpace: {
                title: 'Nicht genügend Speicherplatz auf der Festplatte',
                'reason-title': 'Grund',
                'possibleSolutions-title': 'Mögliche Lösungen',
                1002: {
                    reason: 'Auf der Festplatte {disk} sind {freeSpace} frei',
                    reason2: 'Mindestens {requiredSpace} wird benötigt',
                    possibleSolutions: { freeSpace: 'Bitte machen Sie Speicherplatz auf der Festplatte {disk} frei' }
                }
            },
            enchancedGTAV: {
                title: 'Falsche Version von GTA V',
                'reason-title': 'Grund',
                'possibleSolutions-title': 'Mögliche Lösungen',
                1003: {
                    reason: 'Du verwendest GTA V Enchanced anstelle von GTA V Legacy',
                    possibleSolutions: {
                        steam_gtaLegacy: 'Wenn du Steam verwendest, musst du Grand Theft Auto V Legacy herunterladen.',
                        epic_gtaLegacy: 'Wenn du Epic Games nutzt, musst du die reguläre Version von Grand Theft Auto V herunterladen.',
                        rgl_gtaLegacy: 'Wenn du den Rockstar Games Launcher verwendest, musst du Grand Theft Auto V Legacy herunterladen.'
                    }
                }
            },
            getGift: {
                'choose-server': 'Wählen Sie einen Server aus',
                description: 'Geschenkartikel auf allen Charakteren eines Servers deiner Wahl verfügbar',
                get: 'Abholen',
                close: 'Schließen',
                success: 'Du hast dein Geschenk erfolgreich abgeholt!',
                error: {
                    1315: 'Nicht so oft!',
                    1101: 'Du hast dich nicht über den Launcher in das Spiel eingeloggt!',
                    1317: 'Du hast bereits ein Geschenk auf diesem Server erhalten!',
                    default: 'Unbekannter Fehler'
                }
            },
            BuyingKey: {
                buyingKey: 'GTA V Legacy Schlüssel kaufen',
                secure: 'Sicher',
                'text-ru': 'Die Seite mit dem Schlüssel für das Spiel Grand Theft Auto V Legacy im Rockstar-Shop wird kurz nach der Bezahlung geöffnet.',
                text: 'Es wird eine lizenzierte Version von Grand Theft Auto V Legacy benötigt. <br /> Erwerbe sie in einem der offiziellen Shops:',
                'path-title': 'Gib den Pfad zum Spiel an',
                'click-to-choose': 'Wähle den Ordner mit dem Spiel',
                'successful-select': 'Der Weg ist erfolgreich gewählt!',
                'enter-email': 'Gib deine E-Mail ein',
                selectPayment: 'Wählen Sie eine Zahlungsmethode aus',
                'user-agreement': 'Ich habe die <span id="rulesUrl">Geschäftsbedingungen des Angebots</span>gelesen.',
                continue: 'Fortfahren',
                cancel: 'Abbrechen'
            },
            noGTA: {
                gtaNotFound: 'GTA V Vermächtnis nicht gefunden',
                youHaveGTA: 'Hast du GTA V Legacy gekauft?',
                text: 'Um Majestic zu spielen, benötigst du eine lizenzierte Version von Grand Theft Auto V Legacy. Der Game Key kann schnell und bequem in unserem Shop erworben werden.',
                buy: 'nein, kaufen',
                have: 'ja, ich habe es',
                title: 'GTA V Vermächtnis nicht gefunden',
                'path-title': 'Gib den Pfad zum Spiel an',
                'click-to-choose': 'Wähle den Ordner mit dem Spiel',
                'successful-select': 'Der Weg ist erfolgreich gewählt!'
            },
            selectGTA: {
                gtaNotFound: 'Wähle den Pfad zu GTA V Legacy',
                text: 'Um Majestic zu spielen, brauchst du eine lizenzierte Version von Grand Theft Auto V Legacy. Wähle den Pfad zu dem Spiel auf deinem Computer.',
                title: 'Wähle den Pfad zu GTA V Legacy',
                'path-title': 'Gib den Pfad zum Spiel an',
                'click-to-choose': 'Wähle den Ordner mit dem Spiel'
            },
            rageUndefined: {
                HDD: 'Festplatte',
                available: 'Verfügbar',
                'game-files': 'SPIELDATEIEN',
                'required-space': 'Es werden etwa {size} GB freier Speicherplatz benötigt',
                'choose-disk-for-installation': 'Wählen Sie das Laufwerk für die Installation der Spieldateien',
                'searching-HDD': 'Suche nach der Festplatte...',
                install: 'Einstellen',
                set: 'Wechseln'
            },
            remove: {
                title: 'Löschung bestätigen',
                confirmation: 'Möchten Sie <span class="value">{title}</span>wirklich löschen?',
                accept: 'Ja, löschen',
                cancel: 'Ich habe meine Meinung geändert'
            },
            failPayment: {
                error: 'Fehler',
                description: 'Leider ist bei der Bearbeitung deiner Zahlung ein Fehler <br/> aufgetreten. <br/> <br/> Bitte versuche es erneut <br/> oder <span id="supportUrl">kontaktiere den Support</span>.',
                back: 'Zurück',
                'to-main-page': 'Zur Startseite',
                payment: 'Bezahlung'
            },
            successPayment: {
                success: 'Erfolg',
                'replenish-account': 'Sie haben Ihr Konto aufgefüllt!',
                'replenish-account-on': 'Sie haben Ihr Konto um <span class="amount">{amount}</span>aufgefüllt',
                'funds-credited-account': 'Das Geld wird in Kürze auf deinem <br/> Konto gutgeschrieben.',
                'to-main-page': 'Zur Startseite',
                back: 'Zurück',
                payment: 'Bezahlung'
            },
            RegionSelector: {
                selectRegion: 'Wähle eine Region',
                discription: 'Davon hängen die verfügbaren Server ab. Du kannst deine Wahl jederzeit in den Einstellungen ändern.',
                avaiableServers: 'Verfügbare Server',
                playStart: 'Spiel starten',
                regions: {
                    eu: 'global',
                    ru: 'CIS',
                    test: 'Test'
                }
            },
            unknownBranch: {
                title: 'Konnte keinen Testzweig finden',
                description: 'Beim Versuch, den Zweig {value} herunterzuladen, ist ein Fehler aufgetreten. Stelle sicher, dass der ausgewählte Zweig existiert und gültig ist.'
            },
            incorrectToken: {
                title: 'Beim Herunterladen ist ein Fehler aufgetreten',
                description: 'Der Download des ausgewählten Testzweigs ist fehlgeschlagen. Überprüfe dein CDN-Token auf Gültigkeit und versuche es erneut.'
            },
            gameAlive: {
                title: 'Das Spiel wurde bereits veröffentlicht',
                confirmation: 'Willst du das Spiel schließen, bevor du es neu startest?',
                accept: 'Ja, ich will',
                cancel: 'Abbruch'
            }
        },
        updater: {
            uploadedSize: '{uploadedSize} GB von {totalDownloadSize} GB',
            speed: '{downloadSpeed} MB/s',
            leftTime: 'Verbleibend ca. {leftTime}',
            seconds: '{value} {value, plural, one{Sekunden} few{Sekunden} many{Sekunden} other {Sekunden}}',
            minutes: '{value} {value, plural, one{Minute} few{Minuten} many{Minuten} other {Minuten}}',
            'checking-for-updates': 'Nach Updates suchen',
            updating: 'Updates werden geladen'
        },
        loader: {
            cancel: 'Abbruch',
            titles: {
                game: {
                    install: 'Spieldateien installieren',
                    verifying: 'Dateien werden überprüft',
                    starting: 'Starten und Patchen des Spiels',
                    backup: 'Sichern von Spieldateien',
                    resolving: 'Datenverarbeitung'
                },
                mods: { install: 'Installation der Modifikation {mod}' }
            },
            info: {
                speed: '{uploadedSize} GB von {totalDownloadSize} GB',
                leftTime: 'Verbleibend ca. {leftTime}',
                downloadSpeed: '{value} MB/s',
                seconds: '{value} {value, plural, one{Sekunden} few{Sekunden} many{Sekunden} other {Sekunden}}',
                minutes: '{value} {value, plural, one{Minute} few{Minuten} many{Minuten} other {Minuten}}'
            }
        },
        navbar: {
            play: 'Spielen',
            connect: 'IP eingeben',
            donate: 'Geschäft',
            news: 'Nachrichten',
            forum: 'Forum',
            mods: 'Mods',
            settings: 'Einstellungen',
            account: {
                signIn: 'Login',
                signUp: 'Registrieren',
                profileConfiguration: 'Profileinstellungen',
                signOut: 'Abmelden'
            },
            comingSoon: 'Bald.'
        },
        servers: {
            server: 'Server',
            polski: 'Polnisch',
            deutsch: 'Deutsch',
            majestic: 'Majestätisch',
            'play-now': 'Jetzt spielen',
            'recommend-for-beginners': 'Wir empfehlen für Anfänger',
            'last-time-visited': 'Zum letzten Mal besucht',
            'all-servers': 'Alle Server',
            'in-other-languages': 'In anderen Sprachen',
            tooltip: {
                techWorks: 'Auf dem Server werden technische Arbeiten durchgeführt',
                seasonPassMultiplier: 'Erfahrungsgewinn des Winterpasses wurde um {value}% erhöht'
            },
            advantages: {
                gift: 'Geschenk',
                newServer: 'Neu',
                experience: 'X{value} Erfahrung',
                seasonPassMultiplier: 'X{value}'
            }
        },
        select: { 'enter-value': 'Geben Sie den Wert ein...' },
        donate: {
            back: 'Zurück',
            'buy-coins': 'Währung kaufen',
            cashbox: 'Kauf von Münzen für die Anmeldung {login} auf {server} Server',
            'choose-server': 'Wählen Sie einen Server aus',
            'enter-login': 'Geben Sie Ihren Benutzernamen ein',
            'enter-sum': 'Geben Sie den Betrag ein',
            'enter-email': 'Geben Sie die E-Mail-Adresse ein',
            'min-bonus-value': 'ab {from}RUB.',
            pay: 'Zur Zahlung gehen',
            'rules-checked': 'Ich habe die <span id="rulesUrl">Bedingungen des Angebots gelesen</span>',
            'rules-agree': 'Ich bin damit einverstanden, dass Majestic RP den Vertrag sofort ausführt, indem ich digitale Inhalte (einschließlich digitaler Währung und aller mit digitaler Währung aktivierten Inhalte) in meinem Konto aktiviere. Ich akzeptiere, dass ich das Recht verliere, diesen Vertrag zu kündigen und eine Rückerstattung zu erhalten, nachdem ich die digitalen Inhalte in meinem Konto aktiviert habe.',
            info: 'Meine Familie',
            exchange: 'Wechselskurs: 1r.',
            description: 'Majestic Coins können zum Kauf von: einzigartigen Fahrzeugen, Immobilien, Geschäften, Spielwährung und vieles mehr verwendet werden. Nach der Bezahlung werden MCs Ihrem Konto auf dem Spielserver automatisch gutgeschrieben.',
            warning: 'Wenn Sie die Daten falsch eingegeben haben, wird es keine Überweisung auf das gewünschte Konto geben.',
            'contract-offer': 'Angebotsvertrag',
            selectPayment: 'Wählen Sie eine Zahlungsmethode aus',
            selectPaymentOther: 'Andere Zahlungsmethoden',
            replenish: {
                cardTitles: {
                    card_ru1: 'Karten russischer Banken 1',
                    crypto: 'Andere Kryptowährungen',
                    erc20: 'USDT-Tether (ERC20)',
                    trc20: 'USDT-Tether (TRC20)',
                    kinguin: 'Aktivierungstasten',
                    steam: 'Mit Steam-Skins bezahlen'
                },
                titles: {
                    card_ru1: 'Karten russischer Banken 1',
                    card_ru2: 'Karten russischer Banken 2',
                    card_ru3: 'Karten russischer Banken 3',
                    sbp: 'Schnelleres Zahlungssystem',
                    qiwi: 'Qiwi',
                    yandexpay: 'Yandex Pay',
                    card_ua: 'Karten ukrainischer Banken',
                    card_world1: 'Ausländische Karten 1',
                    card_world2: 'Ausländische Karten 2',
                    paypal: 'PayPal',
                    kinguin: 'Kinguin-Aktivierungsschlüssel',
                    steam: 'Artikel von Steam',
                    yoomoney: 'Geld',
                    bitcoin: 'Bitcoin',
                    ethereum: 'Ethereum',
                    trc20: 'USDT-Tether (TRC20)',
                    erc20: 'USDT-Tether (ERC20)',
                    crypto: 'Andere Kryptowährungen',
                    direct_banking: 'Zahlung vom Bankkonto',
                    cards_eu: 'Zahlung mit Karte 1',
                    paysafecard: 'paysafecard',
                    blik: 'blik',
                    paypalych: 'Zahlung mit Karte 2',
                    dolyame: 'Zahlung in Raten'
                }
            }
        },
        settings: {
            'on-main': 'Zur Startseite',
            categories: {
                main: 'Startseite',
                patch: 'Patches',
                additional: 'Zusätzlich',
                developer: 'Entwickler'
            },
            descriptions: {
                shadowplayOverlay: 'Mit dieser Funktion kannst du im Spiel Videos aufnehmen und Screenshots machen. <br> Wenn du Probleme mit dem Spiel <span class="highlight">(Abstürze, Einfrieren usw.)</span>hast, versuche, diese Funktion zu deaktivieren.',
                discordOverlay: 'Mit dieser Funktion kannst du das Discord-Menü im Spiel öffnen. <br> Wenn du Probleme mit dem Spiel <span class="highlight">(Abstürze, Hänger usw.)</span>hast, versuche, diese Funktion zu deaktivieren.',
                devSettings: 'Tipp: Um die Daten im Eingabefeld zu speichern, drücke ENTER'
            },
            actions: {
                fix: 'Repariere es',
                clearCache: 'Löschen',
                changeMAJESTICFolder: 'Wechseln',
                changeGTAFolder: 'Wechseln',
                fixPermissions: 'Repariere es',
                reverifyFiles: 'Schau es dir an'
            },
            titles: {
                region: 'Region',
                branch: 'Zweigstelle',
                cdnToken: 'CDN Token',
                enableDebug: 'Debugging einschalten',
                skipBranchCheck: 'Zweigprüfung überspringen',
                language: 'Sprache',
                interfaceVolume: 'Interface Volumen',
                hideAfterBootGame: 'Launcher nach dem Spielstart minimieren',
                downloadInLauncher: 'Spieldateien im Launcher herunterladen',
                enableCEFGPU: 'Hardwarebeschleunigung der Schnittstelle',
                patchGTA: 'Patch für das Spiel anwenden',
                clearCache: 'Spiel-Cache leeren',
                forceClearCache: 'Spielcache vollständig leeren',
                changeMAJESTICFolder: 'Ändere den Installationsort von Majestic RP',
                changeGTAFolder: 'Ändere den Installationsort von GTA V Legacy',
                fixPermissions: 'Zugriffsrechte auf Spieldateien korrigieren',
                reverifyFiles: 'Gezwungen, die Spieldateien zu überprüfen',
                shadowplayOverlay: 'NVIDIA Shadowplay Overlay aktivieren',
                discordOverlay: 'Discord Overlay aktivieren',
                pass: 'Passwort',
                range: 'Bereich'
            },
            placeholders: { enterPass: 'Geben Sie Ihr Passwort ein' }
        },
        account: {
            signIn: {
                logInAccount: 'Melden Sie sich bei Ihrem Konto an',
                signIn: 'Login',
                signUp: 'Registrieren',
                enterEmail: 'E-Mail-Adresse eingeben',
                enterPass: 'Geben Sie Ihr Passwort ein',
                forgotPassword: 'Passwort vergessen?'
            },
            signUp: {
                signUp: 'Registrieren',
                enterLogin: 'Geben Sie Ihren Benutzernamen ein',
                enterEmail: 'E-Mail-Adresse eingeben',
                enterPass: 'Geben Sie Ihr Passwort ein',
                repeatPass: 'Passwort wiederholen',
                alreadyCreateAccount: 'Sie haben bereits ein Konto?',
                'agree-newsletters': 'Ich stimme dem Erhalt von Newslettern zu',
                'accept-rules': 'Ich habe die Regeln von <span id="rulesUrl">Server</span> gelesen und stimme ihnen zu',
                'confirm-age': 'Ich bestätige, dass ich mindestens 18 Jahre alt bin'
            },
            profileConfiguration: {},
            forgotPassword: {
                passwordReset: 'Passwort zurücksetzen',
                enterEmail: 'E-Mail-Adresse eingeben',
                enterPass: 'Geben Sie Ihr Passwort ein',
                repeatPass: 'Passwort wiederholen',
                continue: 'Fortfahren',
                cancel: 'Abbruch',
                enterCode: 'Geben Sie den an Ihre E-Mail-Adresse gesendeten Code ein '
            }
        },
        mods: {
            sort: 'Sortierung',
            sortItems: {
                graphics: 'Grafik',
                interface: 'Schnittstelle',
                sounds: 'Töne',
                other: 'Sonstiges'
            },
            install: 'Einstellen',
            uninstall: 'Entfernen',
            more: 'Mehr',
            back: 'Zurück',
            empty: 'Hier ist noch nichts, folgen Sie den Nachrichten, um keine neuen Mods zu verpassen',
            systemRequirements: 'Systemanforderungen',
            operatingSystem: 'Betriebssystem',
            processor: 'Prozessor',
            ram: 'RAM',
            videoCard: 'Grafikkarte',
            soundCard: 'Soundkarte',
            freeDiskSpace: 'Freier Festplattenspeicher',
            berkleyRedux: {
                title: 'Berkley Redux',
                description: 'Kostenlose grafische Modifikation von Berkley basierend auf Redux.'
            },
            evolvedGraphics: {
                title: 'Entwickelte Grafiken',
                description: 'Eine globale Mod, die die Grafik auf das Niveau moderner Spiele verbessert. Spielbeleuchtung, Wetter, Effekte und Nachbearbeitung sind perfekt aufgebaut und dank der grafischen Effekte von ENB wird das Bild im Spiel klar und fotorealistisch.'
            },
            atlasPopularPlaces: {
                title: 'Atlas mit beliebten Orten',
                description: 'Oben auf der Karte sind die Standorte von Grünflächen, die Namen beliebter Orte und mehr'
            },
            loadingScreens: {
                title: 'Bildschirme werden geladen',
                description: ''
            },
            strongMotionBlur: {
                title: 'Starke Bewegungsunschärfe',
                description: 'Für Liebhaber von Filmbildern'
            },
            purpleTracer: {
                title: 'lila Tracer',
                description: 'Die Show muss weitergehen! Wenn man nun aus einer Waffe schießt, erscheinen helle Effekte von Tracer-Kartuschen.'
            }
        },
        discord: {
            state: 'Spielt auf Majestic',
            details: 'Mit dem Launcher'
        },
        tray: {
            open: 'Öffnen',
            close: 'Schließen'
        },
        gift: {
            'pickUp-gift': 'Vergiss nicht, dein Geschenk abzuholen!',
            get: 'Abholen'
        },
        competition: {
            title: 'Wir verlosen',
            'sub-title': 'Ein echter BMW M3.',
            participate: 'Teilnehmen'
        },
        crash: {
            title: 'kritischer Fehler',
            problemTitle: 'Wenn das Problem erneut auftritt, versuche die folgenden Schritte:',
            sol1: 'Starte Majestic RP oder deinen Computer neu',
            sol2: 'Führe Majestic RP als Administrator aus',
            sol3: 'Überprüfe die Integrität der Spieldateien',
            probt1: 'Nicht hilfreich? Schau auf unsere Seite',
            probt2: 'häufige Probleme',
            probt3: 'oder',
            probt4: 'Kontakt zur Unterstützung',
            crashId: 'Crash ID:',
            errorCode: 'Fehlercode:',
            logDir: 'Ordner "Logs',
            gtavDir: 'GTA V Ordner',
            crashReport: 'Absturzbericht-Datei',
            exit: 'abmelden',
            reload: 'Neustart',
            crashIdCopy: 'Crash ID in die Zwischenablage kopiert',
            errorCodeCopy: 'Fehlercode in die Zwischenablage kopiert'
        }
    }
};