export default {
    launcher: {
        'servers-list': 'Lista de servidores',
        connection: '<PERSON><PERSON><PERSON>',
        milliseconds: 'ms',
        play: '<PERSON><PERSON>',
        notifications: {
            error: '<PERSON>alha',
            warning: 'Aten<PERSON>',
            success: 'Sucesso',
            mod: {
                uninstall: {
                    title: 'Sucesso',
                    text: 'Mod excluído com sucesso!'
                }
            },
            cache: {
                clear: {
                    title: 'Sucesso',
                    text: 'Cache limpo com sucesso'
                }
            },
            'permissions-fix': {
                title: 'Sucesso',
                text: 'Permissões de arquivo de jogo corrigidas com sucesso'
            },
            account: {
                loginError: {
                    required: {
                        title: '<PERSON>alha',
                        text: 'Login é necessário!'
                    },
                    minLength: {
                        title: '<PERSON>alha',
                        text: 'O login especificado é muito curto!'
                    },
                    maxLength: {
                        title: 'Falha',
                        text: 'O login especificado é muito longo!'
                    }
                },
                emailError: {
                    email: {
                        title: '<PERSON>al<PERSON>',
                        text: 'E-mail inválido!'
                    },
                    required: {
                        title: '<PERSON>al<PERSON>',
                        text: 'E-mail é obrigatório!'
                    },
                    minLength: {
                        title: '<PERSON>alha',
                        text: 'O e-mail especificado é muito curto!'
                    },
                    maxLength: {
                        title: 'Falha',
                        text: 'O e-mail especificado é muito longo!'
                    }
                },
                passwordError: {
                    required: {
                        title: 'Falha',
                        text: 'Senha requerida!'
                    },
                    minLength: {
                        title: 'Falha',
                        text: 'A senha que você digitou é muito curta!'
                    },
                    maxLength: {
                        title: 'Falha',
                        text: 'A senha especificada é muito longa!'
                    },
                    sameAs: {
                        title: 'Falha',
                        text: 'As senhas não coincidem!'
                    }
                },
                forgotPassword: {
                    success: {
                        title: 'Sucesso',
                        text: 'A sua senha foi mudada com sucesso!'
                    },
                    codeError: {
                        title: 'Falha',
                        text: 'Código inválido!'
                    }
                }
            },
            installGame: {
                badPath: {
                    title: 'Falha',
                    text: 'Caminho inválido!'
                }
            },
            getGift: { 'select-server': 'Selecione servidor!' },
            reverifyFiles: { text: 'Na próxima vez que você executar o jogo, a integridade de todos os arquivos será verificada automaticamente.' },
            gameAlive: {
                title: 'Sucesso',
                text: 'Você fechou os processos do jogo com sucesso!'
            }
        },
        modal: {
            close: 'Fechar',
            connect: {
                title: 'conexão manual',
                ip: 'Endereço do servidor',
                password: 'Senha (se disponível)',
                text: 'O endereço do servidor deve estar no formato <mark>endereço:porta</mark><br/>. Por exemplo: <mark>*********.1:7788</mark> ou <mark>example.server.com:443</mark>',
                discard: 'Cancelamento',
                connect: 'Conectar'
            },
            runGameError: {
                title: 'Falha ao iniciar o jogo',
                'reason-title': 'Razão',
                'possibleSolutions-title': 'Soluções possíveis',
                1000: {
                    reason: 'Falha ao executar arquivo multijogador',
                    possibleSolutions: {
                        tryAgain: 'Tente clicar em jogar novamente',
                        reboot: 'Tente reiniciar seu computador'
                    }
                }
            },
            installGameError: {
                title: 'Falha ao instalar o jogo',
                'reason-title': 'Motivo',
                'possibleSolutions-title': 'Soluções possíveis',
                1001: {
                    reason: 'Falha ao acessar o diretório do jogo',
                    possibleSolutions: {
                        runAsAdmin: 'Tente executar o Launcher como administrador',
                        changeAccess: 'Tente alterar as permissões do diretório do jogo "Games"',
                        reboot: 'Tente reiniciar seu computador'
                    }
                }
            },
            startGameError: {
                title: 'Erro ao implementar patches',
                'reason-title': 'Razão',
                'possibleSolutions-title': 'Soluções possíveis',
                1001: {
                    reason: 'Falha ao implementar patches no jogo',
                    possibleSolutions: {
                        runAsAdmin: 'Tente executar o Launcher como administrador',
                        changeAccess: 'Tente executar o Rockstar Games Launcher separadamente',
                        reboot: 'Tente reiniciar seu computador'
                    }
                }
            },
            notEnoughDiskSpace: {
                title: 'Espaço insuficiente no disco',
                'reason-title': 'Motivo',
                'possibleSolutions-title': 'Soluções possíveis',
                1002: {
                    reason: '{disk} {freeSpace}O disco é gratuito',
                    reason2: '{requiredSpace}Mínimo exigido',
                    possibleSolutions: { freeSpace: '{disk}Liberar espaço em disco' }
                }
            },
            enchancedGTAV: {
                title: 'Versão errada do GTA V',
                'reason-title': 'Razão',
                'possibleSolutions-title': 'Soluções possíveis',
                1003: {
                    reason: 'Você está usando o GTA V Enchanced em vez do GTA V Legacy',
                    possibleSolutions: {
                        steam_gtaLegacy: 'Se estiver usando o Steam, você precisará fazer o download do Grand Theft Auto V Legacy',
                        epic_gtaLegacy: 'Se estiver usando a Epic Games, você precisará fazer o download da versão regular do Grand Theft Auto V',
                        rgl_gtaLegacy: 'Se estiver usando o Rockstar Games Launcher, você precisará fazer o download do Grand Theft Auto V Legacy'
                    }
                }
            },
            getGift: {
                'choose-server': 'Selecione um servidor',
                description: 'Item de presente disponível em todos os personagens de um servidor de sua escolha',
                get: 'Reivindicar',
                close: 'Fechar',
                success: 'Você reivindicou seu presente com sucesso!',
                error: {
                    1315: 'Não tão frequente!',
                    1101: 'Você não entrou no jogo pelo inicializador!',
                    1317: 'Você já recebeu um presente neste servidor!',
                    default: 'Erro desconhecido'
                }
            },
            BuyingKey: {
                buyingKey: 'Comprar GTA V Legacy key',
                secure: 'Seguro',
                'text-ru': 'A página com a chave do jogo Grand Theft Auto V Legacy na loja da Rockstar será aberta logo após o pagamento.',
                text: 'Você precisa de uma versão licenciada do Grand Theft Auto V Legacy. <br /> Você pode comprá-la em uma das lojas oficiais:',
                'path-title': 'Especifique o caminho para o jogo',
                'click-to-choose': 'Selecione a pasta com o jogo',
                'successful-select': 'O caminho foi escolhido com sucesso!',
                'enter-email': 'Digite seu e-mail',
                selectPayment: 'Selecione um método de pagamento',
                'user-agreement': 'Eu li os termos e condições da oferta <span id="rulesUrl"></span>',
                continue: 'Para continuar',
                cancel: 'Cancelamento'
            },
            noGTA: {
                gtaNotFound: 'GTA V Legacy não encontrado',
                youHaveGTA: 'Você já comprou o GTA V Legacy?',
                text: 'Para jogar Majestic, você precisa de uma versão licenciada do Grand Theft Auto V Legacy. Você pode adquirir a chave do jogo de forma rápida e conveniente em nossa loja.',
                buy: 'Não, compre',
                have: 'Sim, existe',
                title: 'GTA V Legacy não encontrado',
                'path-title': 'Especifique o caminho para o jogo',
                'click-to-choose': 'Selecione a pasta com o jogo',
                'successful-select': 'O caminho foi escolhido com sucesso!'
            },
            selectGTA: {
                gtaNotFound: 'Selecione o caminho para o GTA V Legacy',
                text: 'Para jogar o Majestic, você precisa de uma versão licenciada do Grand Theft Auto V Legacy. Selecione o caminho para o jogo em seu computador.',
                title: 'Selecione o caminho para o GTA V Legacy',
                'path-title': 'Especifique o caminho para o jogo',
                'click-to-choose': 'Selecione a pasta com o jogo'
            },
            rageUndefined: {
                HDD: 'Disco Rígido',
                available: 'Disponível',
                'game-files': 'ARQUIVOS DE JOGO',
                'required-space': 'Precisará de cerca de {size} GB de espaço livre',
                'choose-disk-for-installation': 'Selecione o disco para instalar os arquivos do jogo',
                'searching-HDD': 'Encontrando o disco rígido...',
                install: 'Instalar',
                set: 'Alterar'
            },
            remove: {
                title: 'Confirmar exclusão',
                confirmation: 'Tem certeza de que deseja excluir <span class="value">{title}</span>?',
                accept: 'Sim, excluir',
                cancel: 'Eu mudei de ideia'
            },
            failPayment: {
                error: 'Falha',
                description: 'Desculpe, ocorreu um erro <br/> ao processar seu pagamento. <br/> <br/> Tente novamente <br/> ou <span id="supportUrl">entre em contato com o suporte</span>.',
                back: 'Volta',
                'to-main-page': 'Para a página principal',
                payment: 'Pagamento'
            },
            successPayment: {
                success: 'Sucesso',
                'replenish-account': 'Você reabasteceu sua conta!',
                'replenish-account-on': 'Você reabasteceu sua conta em <span class="amount">{amount}</span>',
                'funds-credited-account': 'Os fundos serão creditados em sua conta <br/> em breve',
                'to-main-page': 'Para a página principal',
                back: 'Volta',
                payment: 'Pagamento'
            },
            RegionSelector: {
                selectRegion: 'Selecione uma região',
                discription: 'Os servidores disponíveis dependem disso. Você pode alterar sua escolha a qualquer momento nas configurações.',
                avaiableServers: 'Servidores disponíveis',
                playStart: 'Iniciar o jogo',
                regions: {
                    eu: 'global',
                    ru: 'CIS',
                    test: 'Teste'
                }
            },
            unknownBranch: {
                title: 'Não foi possível encontrar um ramo de teste',
                description: 'Ocorreu um erro ao tentar fazer o download da ramificação {value} . Verifique se a ramificação selecionada existe e é válida'
            },
            incorrectToken: {
                title: 'Ocorreu um erro durante o download',
                description: 'Falha ao fazer o download da ramificação de teste selecionada, verifique a validade do token CDN e tente novamente'
            },
            gameAlive: {
                title: 'O jogo já foi lançado',
                confirmation: 'Você deseja fechar o jogo antes de reiniciá-lo?',
                accept: 'Sim, eu sei.',
                cancel: 'Cancelamento'
            }
        },
        updater: {
            uploadedSize: '{uploadedSize} GB de {totalDownloadSize} GB',
            speed: '{downloadSpeed} MB/s',
            leftTime: 'Aproximadamente {leftTime} minutos restantes',
            seconds: '{value} {value, plural, one{segundos} few{segundos} many{segundos} other {segundos}}',
            minutes: '{value} {value, plural, one{minuto} few{minutos} many{minutos} other {minutos}}',
            'checking-for-updates': 'Verificar atualizações',
            updating: 'Baixar atualizações'
        },
        loader: {
            cancel: 'Cancelamento',
            titles: {
                game: {
                    install: 'Instalando arquivos do jogo',
                    verifying: 'Verificando arquivos',
                    starting: 'Iniciar e aplicar patches ao jogo',
                    backup: 'Fazendo backup dos arquivos do jogo',
                    resolving: 'Processamento de dados'
                },
                mods: { install: 'Instalação da modificação {mod}' }
            },
            info: {
                speed: '{uploadedSize} GB de {totalDownloadSize} GB',
                leftTime: 'Aproximadamente {leftTime} minutos restantes',
                downloadSpeed: '{value} MB/s',
                seconds: '{value} {value, plural, one{segundos} few{segundos} many{segundos} other {segundos}}',
                minutes: '{value} {value, plural, one{minuto} few{minutos} many{minutos} other {minutos}}'
            }
        },
        navbar: {
            play: 'Jogar',
            connect: 'Inserir IP',
            donate: 'Loja',
            news: 'News',
            forum: 'Fórum',
            mods: 'Mods',
            settings: 'Configurações',
            account: {
                signIn: 'Conecte-se',
                signUp: 'Cadastro',
                profileConfiguration: 'Configurações de perfil',
                signOut: 'Sair da sua conta'
            },
            comingSoon: 'Em breve'
        },
        servers: {
            server: 'Servidor',
            polski: 'Polonês',
            deutsch: 'Alemão',
            majestic: 'Majestic',
            'play-now': 'Jogar Agora',
            'recommend-for-beginners': 'Conselhos para iniciantes',
            'last-time-visited': 'Visitado pela última vez',
            'all-servers': 'Todos os servidores',
            'in-other-languages': 'Em outros idiomas',
            tooltip: {
                techWorks: 'O trabalho técnico está sendo realizado no servidor',
                seasonPassMultiplier: '{value}A experiência adquirida com o Winter Pass aumentou em %.'
            },
            advantages: {
                gift: 'Presente',
                newServer: 'Novo',
                experience: 'X{value} Experiência',
                seasonPassMultiplier: '{value}X'
            }
        },
        select: { 'enter-value': 'Insira um valor...' },
        donate: {
            back: 'Volta',
            'buy-coins': 'Comprar moeda',
            cashbox: 'Comprando moedas para o login {login} no servidor {server}',
            'choose-server': 'Selecione um servidor',
            'enter-login': 'Insira seu Login',
            'enter-sum': 'Insira a Quantidade',
            'enter-email': 'Insira seu endereço de email',
            'min-bonus-value': 'de {from}rub.',
            pay: 'Proceder ao pagamento',
            'rules-checked': 'Eu li os <span id="rulesUrl">termos da oferta</span>',
            'rules-agree': 'Consinto que a Majestic RP execute imediatamente o contrato ativando conteúdo digital (incluindo moeda digital e qualquer conteúdo ativado usando moeda digital) em minha conta. Aceito que perco o direito de rescindir este contrato e receber um reembolso após ativar o conteúdo digital em minha conta.',
            info: 'Informação',
            exchange: 'Taxa de câmbio: 1rub.',
            description: 'Para Majestic Coins você pode comprar: veículos exclusivos, imóveis, negócios, moeda do jogo e muito mais. Após o pagamento, o MC será creditado em sua conta no servidor do jogo automaticamente.',
            warning: 'Se você inseriu os dados incorretamente, os fundos não serão transferidos para a conta correta.',
            'contract-offer': 'Oferta de Contrato',
            selectPayment: 'Escolha um método de pagamento',
            selectPaymentOther: 'Outras formas de pagamento',
            replenish: {
                cardTitles: {
                    card_ru1: 'Cartões Bancários da Rússia 1',
                    crypto: 'Outras criptomoedas',
                    erc20: 'USDT Tether (ERC20)',
                    trc20: 'USDT Tether (TRC20)',
                    kinguin: 'Chaves de Ativação',
                    steam: 'Pagamento de Skin na Steam'
                },
                titles: {
                    card_ru1: 'Cartões de Banco da Russia 1',
                    card_ru2: 'Cartões Bancários da Rússia 2',
                    card_ru3: 'Cartões Bancários da Rússia 3',
                    sbp: 'Pagamentos Rápidos',
                    qiwi: 'Qiwi',
                    yandexpay: 'YooMoney',
                    card_ua: 'Cartões de Banco da Ucrânia',
                    card_world1: 'Cartões Estrangeiros 1',
                    card_world2: 'Cartões Estrangeiros 2',
                    paypal: 'PayPal',
                    kinguin: 'Chaves de ativação Kinguin',
                    steam: 'Itens da Steam',
                    yoomoney: 'YooMoney',
                    bitcoin: 'Bitcoin',
                    ethereum: 'Ethereum',
                    trc20: 'USDT Tether (TRC20)',
                    erc20: 'USDT Tether (ERC20)',
                    crypto: 'Outras criptomoedas',
                    direct_banking: 'Bacamento pela conta Bancária',
                    cards_eu: 'Pagamento por cartão 1',
                    paysafecard: 'Paysafecard',
                    blik: 'Blik',
                    paypalych: 'Pagamento por cartão 2',
                    dolyame: 'Pagamento em parcelas'
                }
            }
        },
        settings: {
            'on-main': 'Para a página principal',
            categories: {
                main: 'Geral',
                patch: 'patch',
                additional: 'Adicional',
                developer: 'Desenvolvedor'
            },
            descriptions: {
                shadowplayOverlay: 'Essa função permite que você grave vídeos e faça capturas de tela no jogo. <br> Se você estiver tendo problemas com o jogo <span class="highlight">(travamentos, congelamentos, etc.)</span>, tente desativar esse recurso.',
                discordOverlay: 'Essa função permite que você abra o menu do Discord no jogo. <br> Se você estiver tendo problemas com o jogo <span class="highlight">(falhas, travamentos, etc.)</span>, tente desativar esse recurso.',
                devSettings: 'Dica: Para salvar os dados no campo de entrada, pressione ENTER'
            },
            actions: {
                fix: 'Reparar',
                clearCache: 'Limpar',
                changeMAJESTICFolder: 'Alterar',
                changeGTAFolder: 'Alterar',
                fixPermissions: 'Reparar',
                reverifyFiles: 'Dê uma olhada'
            },
            titles: {
                region: 'Região',
                branch: 'Filial',
                cdnToken: 'Token de CDN',
                enableDebug: 'Ativar a depuração',
                skipBranchCheck: 'Ignorar verificação da filial',
                language: 'Idioma',
                interfaceVolume: 'Volume da interface',
                hideAfterBootGame: 'Minimize o Launcher após iniciar o jogo',
                downloadInLauncher: 'Baixar arquivos do jogo no inicializador',
                enableCEFGPU: 'Aceleração da interface de hardware',
                patchGTA: 'Aplique o patch ao jogo',
                clearCache: 'Limpar cache do jogo',
                forceClearCache: 'Limpe completamente o cache do jogo',
                changeMAJESTICFolder: 'Alterar o local de instalação do Majestic RP',
                changeGTAFolder: 'Altere o local de instalação do GTA V Legacy',
                fixPermissions: 'Corrigir os direitos de acesso aos arquivos do jogo',
                reverifyFiles: 'Forçado a verificar os arquivos do jogo',
                shadowplayOverlay: 'Ativar a sobreposição do NVIDIA Shadowplay',
                discordOverlay: 'Ativar a sobreposição do Discord',
                pass: 'Senha',
                range: 'Faixa'
            },
            placeholders: { enterPass: 'Digite a senha' }
        },
        account: {
            signIn: {
                logInAccount: 'Faça login na sua conta',
                signIn: 'Conecte-se',
                signUp: 'Cadastro',
                enterEmail: 'Insira o e-mail',
                enterPass: 'Digite a senha',
                forgotPassword: 'Esqueceu sua senha?'
            },
            signUp: {
                signUp: 'Cadastro',
                enterLogin: 'Inserir login',
                enterEmail: 'Insira o e-mail',
                enterPass: 'Digite a senha',
                repeatPass: 'Repita sua senha',
                alreadyCreateAccount: 'Já tem uma conta?',
                'agree-newsletters': 'Concordo em receber newsletters',
                'accept-rules': 'Eu li e concordo com as regras do <span id="rulesUrl">servidor</span>',
                'confirm-age': 'Confirmo que tenho 18 anos'
            },
            profileConfiguration: {},
            forgotPassword: {
                passwordReset: 'Redefinição de senha',
                enterEmail: 'Insira o e-mail',
                enterPass: 'Digite a senha',
                repeatPass: 'Repita sua senha',
                continue: 'Continuar',
                cancel: 'Cancelamento',
                enterCode: 'Digite o código enviado para seu e-mail'
            }
        },
        mods: {
            sort: 'Ordenação',
            sortItems: {
                graphics: 'Gráficos',
                interface: 'Interface',
                sounds: 'Sons',
                other: 'Outros'
            },
            install: 'Instalar',
            uninstall: 'Remover',
            more: 'Ver Detalhes',
            back: 'Volta',
            empty: 'Ainda não tem nada aqui, acompanhe as novidades para não perder novos mods',
            systemRequirements: 'Requisitos de sistema',
            operatingSystem: 'Sistema Operacional',
            processor: 'Processador',
            ram: 'RAM',
            videoCard: 'Placa de vídeo',
            soundCard: 'Placa de Som',
            freeDiskSpace: 'Espaço livre no disco rígido',
            berkleyRedux: {
                title: 'Berkley Redux',
                description: 'Modificação gráfica gratuita de Berkley baseada em Redux.'
            },
            evolvedGraphics: {
                title: 'Gráficos evoluídos',
                description: 'Um mod global que melhora os gráficos ao nível dos jogos modernos. A iluminação do jogo, o clima, os efeitos e o pós-processamento são criados com perfeição e, graças aos efeitos gráficos do ENB, a imagem do jogo torna-se clara e fotorrealista.'
            },
            atlasPopularPlaces: {
                title: 'Atlas com lugares populares',
                description: 'Sobrepostos no topo do mapa estão as localizações das áreas verdes, os nomes dos lugares populares e muito mais'
            },
            loadingScreens: {
                title: 'Carregando telas',
                description: ''
            },
            strongMotionBlur: {
                title: 'Desfoque de movimento grave',
                description: 'Para os amantes de imagens cinematográficas'
            },
            purpleTracer: {
                title: 'Tracer roxo',
                description: 'O show tem que continuar! Agora, ao disparar com uma arma, aparecem efeitos brilhantes de cartuchos traçadores.'
            }
        },
        discord: {
            state: 'Jogando em Majestic',
            details: 'Usando o iniciador'
        },
        tray: {
            open: 'Abrir',
            close: 'Fechar'
        },
        gift: {
            'pickUp-gift': 'Não se esqueça de pegar seu presente!',
            get: 'Reivindicar'
        },
        competition: {
            title: 'Estamos sorteando',
            'sub-title': 'Um verdadeiro BMW M3.',
            participate: 'Participar'
        },
        crash: {
            title: 'erro crítico',
            problemTitle: 'Se o problema voltar a ocorrer, tente as etapas a seguir:',
            sol1: 'Reinicie o Majestic RP ou o computador pessoal',
            sol2: 'Execute o Majestic RP como administrador',
            sol3: 'Verifique a integridade dos arquivos do jogo',
            probt1: 'Você não está ajudando? Consulte nossa página',
            probt2: 'problemas frequentes',
            probt3: 'ou',
            probt4: 'Entre em contato com o suporte',
            crashId: 'Identificação da colisão:',
            errorCode: 'Código de erro:',
            logDir: 'Pasta de registros',
            gtavDir: 'Pasta do GTA V',
            crashReport: 'Arquivo de relatório de colisão',
            exit: 'sair',
            reload: 'reiniciar',
            crashIdCopy: 'ID da colisão copiada para a área de transferência',
            errorCodeCopy: 'Código de erro copiado para a área de transferência'
        }
    }
};