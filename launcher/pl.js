export default {
    launcher: {
        'servers-list': '<PERSON>a serwerów',
        connection: '<PERSON><PERSON><PERSON>czenie',
        milliseconds: 'ms',
        play: '<PERSON><PERSON>',
        notifications: {
            error: 'B<PERSON>ą<PERSON>',
            warning: '<PERSON><PERSON><PERSON>',
            success: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
            mod: {
                uninstall: {
                    title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
                    text: 'Mody<PERSON><PERSON><PERSON><PERSON> została pomyślnie usunięta!'
                }
            },
            cache: {
                clear: {
                    title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
                    text: '<PERSON><PERSON><PERSON><PERSON> podręczna została pomyślnie wyczyszczona'
                }
            },
            'permissions-fix': {
                title: '<PERSON><PERSON><PERSON>lnie',
                text: 'Pomyślnie naprawiono uprawnienia do plików gry'
            },
            account: {
                loginError: {
                    required: {
                        title: '<PERSON><PERSON><PERSON><PERSON>',
                        text: '<PERSON>gowanie jest wymagane!'
                    },
                    minLength: {
                        title: 'Błąd',
                        text: 'Podany login jest zbyt krótki!'
                    },
                    maxLength: {
                        title: '<PERSON><PERSON><PERSON><PERSON>',
                        text: '<PERSON><PERSON>y login jest zbyt długi!'
                    }
                },
                emailError: {
                    email: {
                        title: '<PERSON><PERSON><PERSON><PERSON>',
                        text: '<PERSON><PERSON><PERSON><PERSON><PERSON>owy adres e-mail!'
                    },
                    required: {
                        title: '<PERSON><PERSON><PERSON><PERSON>',
                        text: 'Adres e-mail jest wymagany!'
                    },
                    minLength: {
                        title: 'Błąd',
                        text: 'Podany adres e-mail jest zbyt krótki!'
                    },
                    maxLength: {
                        title: 'Błąd',
                        text: 'Podany adres e-mail jest zbyt długi!'
                    }
                },
                passwordError: {
                    required: {
                        title: 'Błąd',
                        text: 'Hasło jest wymagane!'
                    },
                    minLength: {
                        title: 'Błąd',
                        text: 'Wprowadzone hasło jest za krótkie!'
                    },
                    maxLength: {
                        title: 'Błąd',
                        text: 'Podane hasło jest zbyt długie!'
                    },
                    sameAs: {
                        title: 'Błąd',
                        text: 'Hasła się nie zgadzają!'
                    }
                },
                forgotPassword: {
                    success: {
                        title: 'Powodzenie',
                        text: 'Pomyślnie zmieniłeś hasło!'
                    },
                    codeError: {
                        title: 'Błąd',
                        text: 'Nieprawidłowy kod!'
                    }
                }
            },
            installGame: {
                badPath: {
                    title: 'Błąd',
                    text: 'Nieprawidłowa ścieżka!'
                }
            },
            getGift: { 'select-server': 'Wybierz serwer!' },
            reverifyFiles: { text: 'Gdy następnym razem uruchomisz grę, wszystkie pliki zostaną automatycznie sprawdzone pod kątem integralności.' },
            gameAlive: {
                title: 'Powodzenie',
                text: 'Pomyślnie zamknąłeś procesy gry!'
            }
        },
        modal: {
            close: 'Zamknij',
            connect: {
                title: 'połączenie ręczne',
                ip: 'Adres serwera',
                password: 'Hasło (jeśli jest dostępne)',
                text: 'Adres serwera musi być w formacie <mark>adres:port</mark><br/>Na przykład: <mark>127.0.0.1:7788</mark> lub <mark>example.server.com:443</mark>',
                discard: 'Anuluj',
                connect: 'Połącz'
            },
            runGameError: {
                title: 'Nie udało się rozpocząć gry',
                'reason-title': 'Przyczyna',
                'possibleSolutions-title': 'Możliwe rozwiązania',
                1000: {
                    reason: 'Nie udało się uruchomić pliku wieloosobowego',
                    possibleSolutions: {
                        tryAgain: 'Spróbuj ponownie kliknąć przycisk odtwarzania',
                        reboot: 'Spróbuj ponownie uruchomić komputer'
                    }
                }
            },
            installGameError: {
                title: 'Nie udało się zainstalować gry',
                'reason-title': 'Przyczyna',
                'possibleSolutions-title': 'Możliwe rozwiązania',
                1001: {
                    reason: 'Nie udało się uzyskać dostępu do katalogu gry',
                    possibleSolutions: {
                        runAsAdmin: 'Spróbuj uruchomić program uruchamiający jako administrator',
                        changeAccess: 'Spróbuj zmienić uprawnienia katalogu gry "Games".',
                        reboot: 'Spróbuj ponownie uruchomić komputer'
                    }
                }
            },
            startGameError: {
                title: 'Błąd podczas wdrażania poprawek',
                'reason-title': 'Przyczyna',
                'possibleSolutions-title': 'Możliwe rozwiązania',
                1001: {
                    reason: 'Nie zaimplementowano poprawek w grze',
                    possibleSolutions: {
                        runAsAdmin: 'Spróbuj uruchomić program uruchamiający jako administrator',
                        changeAccess: 'Spróbuj uruchomić Rockstar Games Launcher osobno',
                        reboot: 'Spróbuj ponownie uruchomić komputer'
                    }
                }
            },
            notEnoughDiskSpace: {
                title: 'Niewystarczająca przestrzeń dyskowa',
                'reason-title': 'Przyczyna',
                'possibleSolutions-title': 'Możliwe rozwiązania',
                1002: {
                    reason: '{disk} {freeSpace}Płyta jest bezpłatna',
                    reason2: '{requiredSpace}Wymagane minimum',
                    possibleSolutions: { freeSpace: '{disk}Zwolnij miejsce na dysku' }
                }
            },
            enchancedGTAV: {
                title: 'Zła wersja GTA V',
                'reason-title': 'Przyczyna',
                'possibleSolutions-title': 'Możliwe rozwiązania',
                1003: {
                    reason: 'Używasz GTA V Enchanced zamiast GTA V Legacy.',
                    possibleSolutions: {
                        steam_gtaLegacy: 'Jeśli korzystasz ze Steam, musisz pobrać Grand Theft Auto V Legacy',
                        epic_gtaLegacy: 'Jeśli korzystasz z Epic Games, musisz pobrać zwykłą wersję Grand Theft Auto V',
                        rgl_gtaLegacy: 'Jeśli korzystasz z Rockstar Games Launcher, musisz pobrać Grand Theft Auto V Legacy'
                    }
                }
            },
            getGift: {
                'choose-server': 'Wybierz serwer',
                description: 'Produkt upominkowy dostępny na wszystkich postaciach z jednego wybranego serwera',
                get: 'Odbierz',
                close: 'Zamknąć',
                success: 'Udało ci się odebrać prezent!',
                error: {
                    1315: 'Nie tak często!',
                    1101: 'Nie zalogowałeś się do gry z poziomu programu uruchamiającego!',
                    1317: 'Otrzymałeś już prezent na tym serwerze!',
                    default: 'Nieznany błąd'
                }
            },
            BuyingKey: {
                buyingKey: 'Kup klucz GTA V Legacy',
                secure: 'Bezpieczny',
                'text-ru': 'Strona z kluczem do gry Grand Theft Auto V Legacy w sklepie Rockstar zostanie otwarta wkrótce po dokonaniu płatności.',
                text: 'Wymagana jest licencjonowana wersja Grand Theft Auto V Legacy. <br /> Kup ją w jednym z oficjalnych sklepów:',
                'path-title': 'Określ ścieżkę do gry',
                'click-to-choose': 'Wybierz folder z grą',
                'successful-select': 'Ścieżka została wybrana pomyślnie!',
                'enter-email': 'Wprowadź swój adres e-mail',
                selectPayment: 'Wybierz metodę płatności',
                'user-agreement': 'Zapoznałem się z warunkami oferty <span id="rulesUrl"></span>',
                continue: 'Kontynuować',
                cancel: 'Anuluj'
            },
            noGTA: {
                gtaNotFound: 'Nie znaleziono GTA V Legacy',
                youHaveGTA: 'Kupiłeś GTA V Legacy?',
                text: 'Do gry w Majestic wymagana jest licencjonowana wersja Grand Theft Auto V Legacy. Klucz do gry możesz zakupić szybko i wygodnie w naszym sklepie.',
                buy: 'nie, kup',
                have: 'tak, jest',
                title: 'Nie znaleziono GTA V Legacy',
                'path-title': 'Określ ścieżkę do gry',
                'click-to-choose': 'Wybierz folder z grą',
                'successful-select': 'Ścieżka została wybrana pomyślnie!'
            },
            selectGTA: {
                gtaNotFound: 'Wybierz ścieżkę do GTA V Legacy',
                text: 'Gra Majestic wymaga licencjonowanej wersji Grand Theft Auto V Legacy. Wybierz ścieżkę do gry na swoim komputerze.',
                title: 'Wybierz ścieżkę do GTA V Legacy',
                'path-title': 'Określ ścieżkę do gry',
                'click-to-choose': 'Wybierz folder z grą'
            },
            rageUndefined: {
                HDD: 'Dysk twardy',
                available: 'Dostępne',
                'game-files': 'PLIKI GRY',
                'required-space': 'Zajmie to około {size} GB wolnego miejsca',
                'choose-disk-for-installation': 'Wybierz dysk do zainstalowania plików gry',
                'searching-HDD': 'Znalezienie dysku twardego...',
                install: 'Zainstaluj',
                set: 'Zmienić'
            },
            remove: {
                title: 'Potwierdź usunięcie',
                confirmation: 'Czy na pewno chcesz usunąć <span class="value">{title}</span>?',
                accept: 'Tak, usuń',
                cancel: 'Zmieniłem zdanie.'
            },
            failPayment: {
                error: 'Błąd',
                description: 'Przepraszamy, wystąpił błąd <br/> podczas przetwarzania płatności. <br/> <br/> Spróbuj ponownie <br/> lub <span id="supportUrl">razy skontaktuj się z pomocą techniczną</span>.',
                back: 'Przejdź z powrotem',
                'to-main-page': 'Przejdź do strony głównej',
                payment: 'Zapłata'
            },
            successPayment: {
                success: 'Powodzenie',
                'replenish-account': 'Twoje konto zostało uzupełnione!',
                'replenish-account-on': 'Uzupełniłeś konto do <span class="amount">{amount}</span>',
                'funds-credited-account': 'Środki zostaną wkrótce przelane na Twoje konto <br/>',
                'to-main-page': 'Przejdź do strony głównej',
                back: 'Przejdź z powrotem',
                payment: 'Zapłata'
            },
            RegionSelector: {
                selectRegion: 'Wybierz region',
                discription: 'Od tego zależą dostępne serwery. Możesz zmienić swój wybór w dowolnym momencie w ustawieniach.',
                avaiableServers: 'Dostępne serwery',
                playStart: 'Rozpocznij grę',
                regions: {
                    eu: 'globalny',
                    ru: 'WNP',
                    test: 'Test'
                }
            },
            unknownBranch: {
                title: 'Nie można znaleźć gałęzi testowej',
                description: 'Wystąpił błąd podczas próby pobrania gałęzi {value} , upewnij się, że wybrana gałąź istnieje i jest prawidłowa.'
            },
            incorrectToken: {
                title: 'Wystąpił błąd podczas pobierania',
                description: 'Nie udało się pobrać wybranej gałęzi testowej, sprawdź ważność tokena CDN i spróbuj ponownie.'
            },
            gameAlive: {
                title: 'Gra została już uruchomiona',
                confirmation: 'Czy chcesz zamknąć grę przed jej ponownym uruchomieniem?',
                accept: 'Tak, wiem.',
                cancel: 'Anuluj'
            }
        },
        updater: {
            uploadedSize: '{uploadedSize} GB z {totalDownloadSize} GB',
            speed: '{downloadSpeed} MB/s',
            leftTime: 'Pozostało około {leftTime}',
            seconds: '{value} {value, plural, one{sekundy} few{sekundy} many{sekundy} other {sekundy}}',
            minutes: '{value} {value, plural, one{minuty} few{minuty} many{minuty} other {minuty}}',
            'checking-for-updates': 'Sprawdź aktualizacje',
            updating: 'Pobieranie aktualizacji'
        },
        loader: {
            cancel: 'Anuluj',
            titles: {
                game: {
                    install: 'Instalowanie plików gry',
                    verifying: 'Sprawdzanie plików',
                    starting: 'Uruchamianie i łatanie gry',
                    backup: 'Tworzenie kopii zapasowych plików gry',
                    resolving: 'Przetwarzanie danych'
                },
                mods: { install: 'Instalacja modyfikacji {mod}' }
            },
            info: {
                speed: '{uploadedSize} GB z {totalDownloadSize} GB',
                leftTime: 'Pozostało około {leftTime}',
                downloadSpeed: '{value} MB/s',
                seconds: '{value} {value, plural, one{sekundy} few{sekundy} many{sekundy} other {sekundy}}',
                minutes: '{value} {value, plural, one{minuty} few{minuty} many{minuty} other {minuty}}'
            }
        },
        navbar: {
            play: 'Graj',
            connect: 'Wprowadź adres IP',
            donate: 'Sklep',
            news: 'Wiadomości',
            forum: 'Forum',
            mods: 'Modyfikacje',
            settings: 'Ustawienia',
            account: {
                signIn: 'Wejdź',
                signUp: 'Rejestracja',
                profileConfiguration: 'Ustawienia profilu',
                signOut: 'Wyloguj się'
            },
            comingSoon: 'Wkrótce.'
        },
        servers: {
            server: 'Serwer',
            polski: 'Polski',
            deutsch: 'Niemiecki',
            majestic: 'Majestic',
            'play-now': 'Teraz odtwarzane',
            'recommend-for-beginners': 'Porady dla początkujących',
            'last-time-visited': 'Odwiedzono po raz ostatni',
            'all-servers': 'Wszystkie serwery',
            'in-other-languages': 'W innych językach',
            tooltip: {
                techWorks: 'Na serwerze prowadzone są prace techniczne',
                seasonPassMultiplier: '{value}Zdobyte doświadczenie w przepustce zimowej zwiększone o %.'
            },
            advantages: {
                gift: 'Prezent',
                newServer: 'Nowy',
                experience: 'X{value} Doświadczenie',
                seasonPassMultiplier: '{value}X'
            }
        },
        select: { 'enter-value': 'Wprowadź wartość...' },
        donate: {
            back: 'Przejdź z powrotem',
            'buy-coins': 'Kup walutę',
            cashbox: 'Kupno monet dla loginu {login} na serwerze {server}',
            'choose-server': 'Wybierz serwer',
            'enter-login': 'Podaj swój login',
            'enter-sum': 'Wpisz kwotę',
            'enter-email': 'Wpisz adres email',
            'min-bonus-value': 'od {from}RUB.',
            pay: 'Przejdź do płatności',
            'rules-checked': 'Zapoznałem się z <span id="rulesUrl">warunkami oferty</span>',
            'rules-agree': 'Wyrażam zgodę na natychmiastowe zawarcie umowy przez Majestic RP poprzez aktywację treści cyfrowych (w tym waluty cyfrowej i wszelkich treści aktywowanych przy użyciu waluty cyfrowej) na moim koncie. Akceptuję, że tracę prawo do rozwiązania niniejszej umowy i otrzymania zwrotu pieniędzy po aktywacji treści cyfrowych na moim koncie.',
            info: 'Moja rodzina',
            exchange: 'Kurs walutowy: 1p.',
            description: 'Za Majestic Coins można kupić: unikalny transport, nieruchomości, biznes, walutę gry i wiele innych. Po dokonaniu płatności MC zostaną automatycznie naliczone na twoje konto na serwerze gry.',
            warning: 'Jeśli dane zostaną wprowadzone niepoprawnie, środki nie zostaną przekazane na właściwe konto.',
            'contract-offer': 'Porozumienie Ofertowe',
            selectPayment: 'Wybierz metodę płatności',
            selectPaymentOther: 'Inne metody płatności',
            replenish: {
                cardTitles: {
                    card_ru1: 'Karty rosyjskich banków 1',
                    crypto: 'Inne kryptowaluty',
                    erc20: 'USDT Tether (ERC20)',
                    trc20: 'USDT Tether (TRC20)',
                    kinguin: 'Klucze aktywacyjne',
                    steam: 'Płać za pomocą skórek Steam'
                },
                titles: {
                    card_ru1: 'Karty rosyjskich banków 1',
                    card_ru2: 'Karty rosyjskich banków 2',
                    card_ru3: 'Karty rosyjskich banków 3',
                    sbp: 'System szybszych płatności',
                    qiwi: 'Qiwi',
                    yandexpay: 'Yandex Pay',
                    card_ua: 'Karty ukraińskich banków',
                    card_world1: 'Zagraniczne karty 1',
                    card_world2: 'Karty zagraniczne 2',
                    paypal: 'PayPal',
                    kinguin: 'Klucze aktywacyjne Kinguin',
                    steam: 'Pozycje ze Steam',
                    yoomoney: 'Юmoney',
                    bitcoin: 'Bitcoin',
                    ethereum: 'Ethereum',
                    trc20: 'USDT Tether (TRC20)',
                    erc20: 'USDT Tether (ERC20)',
                    crypto: 'Inne kryptowaluty',
                    direct_banking: 'Płatność z Rachunku Bankowego',
                    cards_eu: 'Płatność kartą 1',
                    paysafecard: 'paysafecard',
                    blik: 'blik',
                    paypalych: 'Płatność kartą 2',
                    dolyame: 'Płatność w ratach'
                }
            }
        },
        settings: {
            'on-main': 'Na główną',
            categories: {
                main: 'Główne',
                patch: 'Naszywki',
                additional: 'Dodatkowe',
                developer: 'Deweloper'
            },
            descriptions: {
                shadowplayOverlay: 'Ta funkcja umożliwia nagrywanie wideo i robienie zrzutów ekranu w grze. <br> Jeśli masz problemy z grą <span class="highlight">(zawiesza się, zawiesza się itp.)</span>, spróbuj wyłączyć tę funkcję.',
                discordOverlay: 'Ta funkcja umożliwia otwarcie menu Discord w grze. <br> Jeśli masz problemy z grą <span class="highlight">(awarie, zawieszanie się itp.)</span>, spróbuj wyłączyć tę funkcję.',
                devSettings: 'Wskazówka: Aby zapisać dane w polu wprowadzania, naciśnij ENTER'
            },
            actions: {
                fix: 'Napraw to',
                clearCache: 'Oczyścić',
                changeMAJESTICFolder: 'Zmień',
                changeGTAFolder: 'Zmień',
                fixPermissions: 'Napraw to',
                reverifyFiles: 'Sprawdź to'
            },
            titles: {
                region: 'Region',
                branch: 'Oddział',
                cdnToken: 'Token CDN',
                enableDebug: 'Włącz debugowanie',
                skipBranchCheck: 'Pomiń sprawdzanie oddziałów',
                language: 'Język',
                interfaceVolume: 'Głośność interfejsu',
                hideAfterBootGame: 'Minimalizuj program uruchamiający po uruchomieniu gry',
                downloadInLauncher: 'Pobierz pliki gry z programu uruchamiającego',
                enableCEFGPU: 'Przyspieszenie sprzętowe interfejsu',
                patchGTA: 'Zastosuj patch do gry',
                clearCache: 'Wyczyść pamięć podręczną gry',
                forceClearCache: 'Całkowicie wyczyść pamięć podręczną gry',
                changeMAJESTICFolder: 'Zmień lokalizację instalacji Majestic RP',
                changeGTAFolder: 'Zmień lokalizację instalacji GTA V Legacy',
                fixPermissions: 'Popraw prawa dostępu do plików gry',
                reverifyFiles: 'Zmuszony do sprawdzenia plików gry',
                shadowplayOverlay: 'Włącz nakładkę NVIDIA Shadowplay',
                discordOverlay: 'Włącz nakładkę Discord',
                pass: 'Hasło',
                range: 'Zakres'
            },
            placeholders: { enterPass: 'Wpisz hasło' }
        },
        account: {
            signIn: {
                logInAccount: 'Zaloguj się na swoje konto',
                signIn: 'Wejdź',
                signUp: 'Rejestracja',
                enterEmail: 'Wpisz adres e-mail',
                enterPass: 'Wpisz hasło',
                forgotPassword: 'Zapomniałeś hasła?'
            },
            signUp: {
                signUp: 'Rejestracja',
                enterLogin: 'Wpisz swoją nazwę użytkownika',
                enterEmail: 'Wpisz adres e-mail',
                enterPass: 'Wpisz hasło',
                repeatPass: 'Powtórz hasło',
                alreadyCreateAccount: 'Masz już konto?',
                'agree-newsletters': 'Wyrażam zgodę na otrzymywanie newsletterów',
                'accept-rules': 'Przeczytałem regulamin serwera <span id="rulesUrl"></span> i akceptuję go.',
                'confirm-age': 'Oświadczam, że mam ukończone 18 lat'
            },
            profileConfiguration: {},
            forgotPassword: {
                passwordReset: 'Resetowanie hasła',
                enterEmail: 'Wpisz adres e-mail',
                enterPass: 'Wpisz hasło',
                repeatPass: 'Powtórz hasło',
                continue: 'Kontynuować',
                cancel: 'Anuluj',
                enterCode: 'Wpisz  kod wysłany na Twój adres e-mail '
            }
        },
        mods: {
            sort: 'Sortowanie',
            sortItems: {
                graphics: 'Grafika',
                interface: 'Interfejs',
                sounds: 'Dźwięk',
                other: 'Inne'
            },
            install: 'Zainstaluj',
            uninstall: 'Usuń',
            more: 'Szczegółowy',
            back: 'Przejdź z powrotem',
            empty: 'Nic tu jeszcze nie ma, śledź wiadomości, żeby nie przegapić nowych modów',
            systemRequirements: 'Wymagania systemowe',
            operatingSystem: 'System operacyjny',
            processor: 'Podmiot przetwarzający',
            ram: 'Ram',
            videoCard: 'Karta graficzna',
            soundCard: 'Karta dźwiękowa',
            freeDiskSpace: 'Wolne miejsce na dysku twardym',
            berkleyRedux: {
                title: 'Berkley Redux',
                description: 'Darmowa modyfikacja graficzna od Berkley na bazie Redux.'
            },
            evolvedGraphics: {
                title: 'Rozwinięta grafika',
                description: 'Globalny mod poprawiający grafikę do poziomu nowoczesnych gier. Oświetlenie gry, pogoda, efekty i post-processing są wznoszone do perfekcji, a dzięki efektom graficznym eNB obraz w grze staje się wyraźny i fotorealistyczny.'
            },
            atlasPopularPlaces: {
                title: 'Atlas z popularnymi miejscami',
                description: 'Na górze mapy znajdują się lokalizacje terenów zielonych, nazwy popularnych miejsc i nie tylko'
            },
            loadingScreens: {
                title: 'Ładowanie ekranów',
                description: ''
            },
            strongMotionBlur: {
                title: 'Silne rozmycie ruchu',
                description: 'Dla miłośników kinowych obrazów'
            },
            purpleTracer: {
                title: 'purpurowy znacznik',
                description: 'Przedstawienie musi trwać! Teraz podczas strzelania z broni pojawiają się jasne efekty nabojów znacznikowych.'
            }
        },
        discord: {
            state: 'Gra na Majestic',
            details: 'Korzystanie z programu uruchamiającego'
        },
        tray: {
            open: 'Otwórz',
            close: 'Zamknąć'
        },
        gift: {
            'pickUp-gift': 'Nie zapomnij odebrać prezentu!',
            get: 'Odbierz'
        },
        competition: {
            title: 'Bierzemy udział w loterii',
            'sub-title': 'Prawdziwe BMW M3.',
            participate: 'Weź udział'
        },
        crash: {
            title: 'błąd krytyczny',
            problemTitle: 'Jeśli problem się powtórzy, spróbuj wykonać następujące czynności:',
            sol1: 'Uruchom ponownie Majestic RP lub komputer osobisty',
            sol2: 'Uruchom Majestic RP jako administrator',
            sol3: 'Sprawdź integralność plików gry',
            probt1: 'Nie pomagasz? Sprawdź naszą stronę',
            probt2: 'częste problemy',
            probt3: 'lub',
            probt4: 'skontaktuj się z pomocą techniczną',
            crashId: 'Crash ID:',
            errorCode: 'Kod błędu:',
            logDir: 'Folder dzienników',
            gtavDir: 'Folder GTA V',
            crashReport: 'Plik raportu o awarii',
            exit: 'wyjdź',
            reload: 'restart',
            crashIdCopy: 'Identyfikator awarii skopiowany do schowka',
            errorCodeCopy: 'Kod błędu skopiowany do schowka'
        }
    }
};