export default {
    launcher: {
        'servers-list': 'サーバー一覧',
        connection: '接続',
        milliseconds: 'ms',
        play: 'プレイ',
        notifications: {
            error: 'エラー',
            warning: '注意',
            success: '成功',
            mod: {
                uninstall: {
                    title: '成功',
                    text: 'MODが正常に削除されました！'
                }
            },
            cache: {
                clear: {
                    title: '成功',
                    text: 'キャッシュが正常にクリアされました'
                }
            },
            'permissions-fix': {
                title: '成功裏に',
                text: 'ゲームファイルのパーミッションの修正に成功'
            },
            account: {
                loginError: {
                    required: {
                        title: 'エラー',
                        text: 'ログインが必要です！'
                    },
                    minLength: {
                        title: 'エラー',
                        text: '指定されたログインが短すぎます！'
                    },
                    maxLength: {
                        title: 'エラー',
                        text: '指定されたログインが長すぎます！'
                    }
                },
                emailError: {
                    email: {
                        title: 'エラー',
                        text: '無効なメールアドレスです！'
                    },
                    required: {
                        title: 'エラー',
                        text: 'メールアドレスが必要です！'
                    },
                    minLength: {
                        title: 'エラー',
                        text: '指定されたメールアドレスが短すぎます！'
                    },
                    maxLength: {
                        title: 'エラー',
                        text: '指定されたメールアドレスが長すぎます！'
                    }
                },
                passwordError: {
                    required: {
                        title: 'エラー',
                        text: 'パスワードが必要です！'
                    },
                    minLength: {
                        title: 'エラー',
                        text: '入力したパスワードが短すぎます！'
                    },
                    maxLength: {
                        title: 'エラー',
                        text: '指定されたパスワードが長すぎます！'
                    },
                    sameAs: {
                        title: 'エラー',
                        text: 'パスワードが一致しません！'
                    }
                },
                forgotPassword: {
                    success: {
                        title: '成功',
                        text: 'パスワードが正常に変更されました！'
                    },
                    codeError: {
                        title: 'エラー',
                        text: '無効なコードです！'
                    }
                }
            },
            installGame: {
                badPath: {
                    title: 'エラー',
                    text: '無効なパスです！'
                }
            },
            getGift: { 'select-server': 'サーバーを選択してください！' },
            reverifyFiles: { text: '次にゲームを実行すると、すべてのファイルの整合性が自動的にチェックされます。' },
            gameAlive: {
                title: '成功',
                text: 'ゲームプロセスは正常に終了しました！'
            }
        },
        modal: {
            close: '閉じるには',
            connect: {
                title: 'マニュアル接続',
                ip: 'サーバーアドレス',
                password: 'パスワード（あれば）',
                text: 'サーバー・アドレスは、<mark>address:port</mark><br/>の形式でなければならない：<mark>*********.1:7788</mark>または<mark>example.server.com:443</mark>',
                discard: 'キャンセル',
                connect: '接続'
            },
            runGameError: {
                title: 'ゲームを開始できませんでした',
                'reason-title': '理由',
                'possibleSolutions-title': '考えられる解決策',
                1000: {
                    reason: 'マルチプレイヤーファイルの実行に失敗しました',
                    possibleSolutions: {
                        tryAgain: '再生をもう一度クリックしてみてください',
                        reboot: 'コンピューターを再起動してみてください'
                    }
                }
            },
            installGameError: {
                title: 'ゲームのインストールに失敗しました',
                'reason-title': '理由',
                'possibleSolutions-title': '可能な解決策',
                1001: {
                    reason: 'ゲームディレクトリへのアクセスに失敗しました',
                    possibleSolutions: {
                        runAsAdmin: 'ランチャーを管理者として実行してみてください。',
                        changeAccess: 'ゲーム・ディレクトリ "Games "のパーミッションを変更してみてください。',
                        reboot: 'コンピュータを再起動してみる'
                    }
                }
            },
            startGameError: {
                title: 'パッチ適用時のエラー',
                'reason-title': '理由',
                'possibleSolutions-title': '考えられる解決策',
                1001: {
                    reason: 'ゲームへのパッチ実装に失敗',
                    possibleSolutions: {
                        runAsAdmin: 'ランチャーを管理者として実行してみてください。',
                        changeAccess: 'Rockstar Games Launcherを別に起動してみてください。',
                        reboot: 'コンピューターを再起動してみてください'
                    }
                }
            },
            notEnoughDiskSpace: {
                title: '椎間板スペース不足',
                'reason-title': '理由',
                'possibleSolutions-title': '可能な解決策',
                1002: {
                    reason: '{disk} {freeSpace}ディスクは無料',
                    reason2: '{requiredSpace}最低限必要なもの',
                    possibleSolutions: { freeSpace: '{disk}ディスクの空き容量' }
                }
            },
            enchancedGTAV: {
                title: 'GTA Vのバージョンが違う',
                'reason-title': '理由',
                'possibleSolutions-title': '考えられる解決策',
                1003: {
                    reason: 'あなたはGTA VレガシーではなくGTA Vエンシャンスドを使っている',
                    possibleSolutions: {
                        steam_gtaLegacy: 'Steamをお使いの場合は、『グランド・セフト・オートVレガシー』をダウンロードする必要があります。',
                        epic_gtaLegacy: 'Epic Gamesを使用している場合は、通常のGrand Theft Auto Vをダウンロードする必要があります。',
                        rgl_gtaLegacy: 'Rockstar Games Launcherを使用している場合は、Grand Theft Auto V Legacyをダウンロードする必要があります。'
                    }
                }
            },
            getGift: {
                'choose-server': 'サーバーを選択',
                description: 'お好みの1つのサーバーの全キャラクターで利用可能なギフトアイテム',
                get: '獲得',
                close: '閉じる',
                success: 'あなたはプレゼントを受け取ることができた！',
                error: {
                    1315: 'そうでもないよ！',
                    1101: 'ランチャーからゲームにログインしていない！',
                    1317: 'このサーバーでは、すでにギフトを受け取っています！',
                    default: '不明なエラー'
                }
            },
            BuyingKey: {
                buyingKey: 'GTA Vレガシー・キーを購入する',
                secure: '安全',
                'text-ru': 'お支払い後すぐに、Rockstarショップのゲーム「グランド・セフト・オートVレガシー」のキーが記載されたページが開きます。',
                text: 'グランド・セフト・オートVレガシー』のライセンス版が必要です。 <br /> 公式ショップでご購入ください：',
                'path-title': 'ゲームへのパスを指定する',
                'click-to-choose': 'ゲームのあるフォルダを選択する',
                'successful-select': 'パスの選択は成功した！',
                'enter-email': 'メールアドレスを入力してください',
                selectPayment: '支払い方法を選択する',
                'user-agreement': '<span id="rulesUrl">オファーの条件</span>を読みました。',
                continue: 'つづく',
                cancel: 'キャンセル'
            },
            noGTA: {
                gtaNotFound: 'GTA V レガシーが見つかりません',
                youHaveGTA: 'GTA Vレガシーを購入しましたか？',
                text: 'Majesticをプレイするには、Grand Theft Auto V Legacyのライセンス版が必要です。ゲームキーは当ショップで素早く便利に購入できます。',
                buy: 'いや、買う',
                have: 'はい、あります',
                title: 'GTA V レガシーが見つかりません',
                'path-title': 'ゲームへのパスを指定する',
                'click-to-choose': 'ゲームのあるフォルダを選択する',
                'successful-select': 'パスの選択は成功した！'
            },
            selectGTA: {
                gtaNotFound: 'GTA V レガシーへのパスを選択',
                text: 'MajesticをプレイするにはGrand Theft Auto V Legacyのライセンス版が必要です。コンピューター上のゲームへのパスを選択してください。',
                title: 'GTA V レガシーへのパスを選択',
                'path-title': 'ゲームへのパスを指定する',
                'click-to-choose': 'ゲームのあるフォルダを選択する'
            },
            rageUndefined: {
                HDD: 'ハードドライブ',
                available: '利用可能',
                'game-files': 'ゲームファイル',
                'required-space': '約 {size} GBの空き容量が必要です',
                'choose-disk-for-installation': 'ゲームファイルをインストールするディスクを選択します',
                'searching-HDD': 'ハードドライブを検索します。..',
                install: 'インストール',
                set: '変更'
            },
            remove: {
                title: '削除の確認',
                confirmation: '<span class="value">{title}</span>を削除してもよろしいですか？',
                accept: 'はい、削除します',
                cancel: '気が変わった'
            },
            failPayment: {
                error: 'エラー',
                description: '申し訳ありませんが、お支払い処理中にエラー <br/> が発生しました。 <br/> <br/> もう一度お試しください <br/> または <span id="supportUrl">サポートにお問い合わせください</span>.',
                back: '戻る',
                'to-main-page': 'ホームページへ',
                payment: '支払い'
            },
            successPayment: {
                success: '成功',
                'replenish-account': 'アカウントを補充しました！',
                'replenish-account-on': '<span class="amount">{amount}</span>までにアカウントを補充しました',
                'funds-credited-account': '残高はまもなく <br/> 口座に入金されます',
                'to-main-page': 'ホームページへ',
                back: '戻る',
                payment: '支払い'
            },
            RegionSelector: {
                selectRegion: '地域を選択',
                discription: '利用可能なサーバーはそれによって異なります。選択したサーバーは設定でいつでも変更できます。',
                avaiableServers: '利用可能なサーバー',
                playStart: '試合開始',
                regions: {
                    eu: 'グローバル',
                    ru: 'CIS',
                    test: 'テスト'
                }
            },
            unknownBranch: {
                title: 'テストブランチが見つかりません',
                description: '{value} ブランチをダウンロードしようとしてエラーが発生しました。選択したブランチが存在し、有効であることを確認してください。'
            },
            incorrectToken: {
                title: 'ダウンロード中にエラーが発生しました',
                description: '選択したテストブランチのダウンロードに失敗しました。'
            },
            gameAlive: {
                title: '試合はすでに開始されている',
                confirmation: '再起動する前にゲームを終了しますか？',
                accept: 'ええ、そうです。',
                cancel: 'キャンセル'
            }
        },
        updater: {
            uploadedSize: '{totalDownloadSize} GB中{uploadedSize} GB',
            speed: '{downloadSpeed} MB/秒',
            leftTime: '残り約 {leftTime}',
            seconds: '{value} {value, plural, one{秒} few{秒} many{秒} other {秒}}',
            minutes: '{value} {value, plural, one{分} few{分} many{分} other {分}}',
            'checking-for-updates': 'アップデートの確認',
            updating: 'アップデートをダウンロードしています'
        },
        loader: {
            cancel: 'キャンセル',
            titles: {
                game: {
                    install: 'ゲームファイルをインストールしています',
                    verifying: 'ファイルをチェックしています',
                    starting: 'ゲームの起動とパッチ適用',
                    backup: 'ゲームファイルのバックアップ',
                    resolving: 'データ処理'
                },
                mods: { install: '修正のインストール {mod}' }
            },
            info: {
                speed: '{totalDownloadSize} GB中{uploadedSize} GB',
                leftTime: '残り約 {leftTime}',
                downloadSpeed: '{value} MB/s',
                seconds: '{value} {value, plural, one{秒} few{秒} many{秒} other {秒}}',
                minutes: '{value} {value, plural, one{分} few{分} many{分} other {分}}'
            }
        },
        navbar: {
            play: '再生',
            connect: 'IPを入力',
            donate: 'ストア',
            news: 'ニュース',
            forum: 'フォーラム',
            mods: 'モッド',
            settings: '設定',
            account: {
                signIn: 'ログイン',
                signUp: '登録',
                profileConfiguration: 'プロフィール設定',
                signOut: 'ログアウト'
            },
            comingSoon: 'もうすぐだ。'
        },
        servers: {
            server: 'サーバー',
            polski: 'ポーランド語',
            deutsch: 'ドイツ語',
            majestic: 'マジェスティック',
            'play-now': '今プレイしています',
            'recommend-for-beginners': '初心者向けアドバイス',
            'last-time-visited': '最後に訪問しました',
            'all-servers': 'すべてのサーバー',
            'in-other-languages': '他の言語で',
            tooltip: {
                techWorks: 'サーバーで技術的な作業が行われている',
                seasonPassMultiplier: '{value}ウィンター・パスの獲得経験値が%増加'
            },
            advantages: {
                gift: 'ギフト',
                newServer: '新しい',
                experience: 'X{value} 経験',
                seasonPassMultiplier: '{value}X'
            }
        },
        select: { 'enter-value': '値を入力します。..' },
        donate: {
            back: '戻る',
            'buy-coins': '通貨を買う',
            cashbox: '{server} サーバーでログイン用コインを購入 {login}',
            'choose-server': 'サーバーを選択',
            'enter-login': 'ユーザー名を入力してください',
            'enter-sum': '金額を入力してください',
            'enter-email': 'メールアドレスを入力してください',
            'min-bonus-value': '{from}RUBから。',
            pay: 'お支払いページへ',
            'rules-checked': '<span id="rulesUrl">オファーの条件</span>を読みました。',
            'rules-agree': '私は、私のアカウントでデジタルコンテンツ（デジタル通貨およびデジタル通貨を使用してアクティブ化されたコンテンツを含む）をアクティブ化することにより、Majestic RPが直ちに契約を実行することに同意します。私は、アカウント内のデジタルコンテンツを有効にした後、この契約を終了し、払い戻しを受ける権利を失うことに同意します。',
            info: '私の家族',
            exchange: '為替レート： R 1',
            description: 'マジェスティックコインは、ユニークな輸送、不動産、ビジネス、ゲーム内通貨などの購入に使用できます。お支払い後、MCは自動的にゲームサーバー上のアカウントに入金されます。',
            warning: 'データを誤って入力した場合、ご希望の口座に送金することはできません。',
            'contract-offer': 'オファー契約',
            selectPayment: 'お支払い方法を選択してください',
            selectPaymentOther: 'その他の支払い方法',
            replenish: {
                cardTitles: {
                    card_ru1: 'ロシアの銀行のカード1',
                    crypto: 'その他の暗号通貨',
                    erc20: 'USDTテザー（ ERC 20 ）',
                    trc20: 'USDTテザー（ TRC 20 ）',
                    kinguin: 'アクティベーションキー',
                    steam: 'Steamスキンで支払う'
                },
                titles: {
                    card_ru1: 'ロシアの銀行のカード1',
                    card_ru2: 'ロシアの銀行のカード2',
                    card_ru3: 'ロシアの銀行のカード3',
                    sbp: '迅速な支払いシステム',
                    qiwi: 'Qiwi',
                    yandexpay: 'Yandex Pay',
                    card_ua: 'ウクライナの銀行のカード',
                    card_world1: '外国のカード1',
                    card_world2: '外国のカード2',
                    paypal: 'PayPal',
                    kinguin: 'キングイン・アクティベーションキー',
                    steam: 'Steamからのアイテム',
                    yoomoney: 'MONEY',
                    bitcoin: 'ビットコイン',
                    ethereum: 'イーサリアム',
                    trc20: 'USDTテザー（ TRC 20 ）',
                    erc20: 'USDTテザー（ ERC 20 ）',
                    crypto: 'その他の暗号通貨',
                    direct_banking: '銀行口座からのお支払い',
                    cards_eu: 'カードでのお支払い1',
                    paysafecard: 'paysafecard',
                    blik: 'blik',
                    paypalych: 'カードでのお支払い2',
                    dolyame: '分割払い'
                }
            }
        },
        settings: {
            'on-main': 'ホームページに戻る',
            categories: {
                main: 'ベーシック',
                patch: 'パッチ',
                additional: 'さらに',
                developer: '開発者'
            },
            descriptions: {
                shadowplayOverlay: 'この機能により、ゲーム中のビデオ録画やスクリーンショットの撮影が可能になります。 <br> ゲームに問題がある場合 <span class="highlight">（クラッシュ、フリーズなど）</span>、この機能を無効にしてみてください。',
                discordOverlay: 'この機能により、ゲーム内でDiscordメニューを開くことができます。 <br> ゲームに問題がある場合 <span class="highlight">(クラッシュ、ハングアップなど)</span>、この機能を無効にしてみてください。',
                devSettings: 'ヒント：入力フィールドのデータを保存するには、ENTERを押します。'
            },
            actions: {
                fix: '修正する',
                clearCache: 'クリア',
                changeMAJESTICFolder: '変更',
                changeGTAFolder: '変更',
                fixPermissions: '修正する',
                reverifyFiles: '見てみよう'
            },
            titles: {
                region: '地域',
                branch: '支店',
                cdnToken: 'CDNトークン',
                enableDebug: 'デバッグを有効にする',
                skipBranchCheck: '分岐チェックをスキップする',
                language: '言語',
                interfaceVolume: 'インターフェース・ボリューム',
                hideAfterBootGame: 'ゲームの起動後にランチャーを折りたたむ',
                downloadInLauncher: 'ランチャーでゲームファイルをダウンロード',
                enableCEFGPU: 'インターフェースハードウェアアクセラレーション',
                patchGTA: 'ワッペンをゲームに適用する',
                clearCache: 'ゲームキャッシュをクリア',
                forceClearCache: 'ゲームキャッシュを完全にクリアする',
                changeMAJESTICFolder: 'Majestic RPのインストール場所の変更',
                changeGTAFolder: 'GTA V レガシーのインストール先を変更する',
                fixPermissions: 'ゲームファイルへのアクセス権を修正',
                reverifyFiles: 'ゲームファイルを強制的にチェック',
                shadowplayOverlay: 'NVIDIA Shadowplayオーバーレイを有効にする',
                discordOverlay: 'Discordオーバーレイを有効にする',
                pass: 'パスワード',
                range: '範囲'
            },
            placeholders: { enterPass: 'パスワードを入力' }
        },
        account: {
            signIn: {
                logInAccount: 'アカウントにログイン',
                signIn: 'ログイン',
                signUp: '登録',
                enterEmail: 'メールアドレスを入力',
                enterPass: 'パスワードを入力',
                forgotPassword: 'パスワードをお忘れですか？'
            },
            signUp: {
                signUp: '登録',
                enterLogin: 'ユーザー名を入力してください',
                enterEmail: 'メールアドレスを入力',
                enterPass: 'パスワードを入力',
                repeatPass: 'パスワードを再入力してください',
                alreadyCreateAccount: 'すでにアカウントをお持ちですか？',
                'agree-newsletters': 'ニュースレターを受け取ることに同意します',
                'accept-rules': '<span id="rulesUrl">サーバー</span> ルールを読み、同意しました',
                'confirm-age': '18歳以上であることを証明します'
            },
            profileConfiguration: {},
            forgotPassword: {
                passwordReset: 'パスワードのリセット',
                enterEmail: 'メールアドレスを入力',
                enterPass: 'パスワードを入力',
                repeatPass: 'パスワードを再入力してください',
                continue: '続ける',
                cancel: 'キャンセル',
                enterCode: 'メールアドレスに送信されたコードを入力してください '
            }
        },
        mods: {
            sort: '並べ替え',
            sortItems: {
                graphics: 'グラフィック',
                interface: 'インターフェース',
                sounds: 'サウンド',
                other: 'その他'
            },
            install: 'インストール',
            uninstall: '削除',
            more: 'もっと詳しく',
            back: '戻る',
            empty: 'まだ何もありません。新しいモッドを見逃さないようにニュースをフォローしてください',
            systemRequirements: 'システム要件',
            operatingSystem: 'オペレーティングシステム',
            processor: 'プロセッサー',
            ram: 'RAM',
            videoCard: 'グラフィックカード',
            soundCard: 'サウンドカード',
            freeDiskSpace: 'ハードディスクの空き容量',
            berkleyRedux: {
                title: 'Berkley Redux',
                description: 'Reduxに基づくBerkleyの無料グラフィカル修正。'
            },
            evolvedGraphics: {
                title: '進化したグラフィックス',
                description: '現代のゲームのレベルにグラフィックを向上させるグローバルモッド。ゲームのライティング、天候、エフェクト、後処理が完璧に組み立てられており、eNBのグラフィックエフェクトのおかげで、ゲーム内の画像が鮮明でフォトリアルになります。'
            },
            atlasPopularPlaces: {
                title: '人気スポットが集まるアトラス',
                description: '地図の上部には、緑地の場所、人気スポットの名前などが表示されます'
            },
            loadingScreens: {
                title: '画面を読み込んでいます',
                description: ''
            },
            strongMotionBlur: {
                title: '深刻な動きのぼかし',
                description: 'シネマティックイメージが大好きな方へ'
            },
            purpleTracer: {
                title: 'パープルトレーサー',
                description: 'ショーを続けなければ！武器から射撃すると、トレーサーカートリッジの明るい効果が現れます。'
            }
        },
        discord: {
            state: 'Majesticでのプレイ',
            details: 'ランチャーを使用する'
        },
        tray: {
            open: '開く',
            close: '閉じる'
        },
        gift: {
            'pickUp-gift': 'ギフトの受け取りをお忘れなく！',
            get: '獲得'
        },
        competition: {
            title: '抽選で',
            'sub-title': '本物のBMW M3。',
            participate: '参加する'
        },
        crash: {
            title: 'クリティカルエラー',
            problemTitle: '問題が再発した場合は、以下の手順を試してください：',
            sol1: 'Majestic RPまたはパソコンを再起動する',
            sol2: '管理者としてMajestic RPを実行する',
            sol3: 'ゲームファイルの完全性をチェックする',
            probt1: 'お役に立ちませんか？私たちのページをチェック',
            probt2: '頻発する問題',
            probt3: 'または',
            probt4: 'コンタクトサポート',
            crashId: 'クラッシュID',
            errorCode: 'エラーコード',
            logDir: 'ログフォルダ',
            gtavDir: 'GTA Vフォルダ',
            crashReport: 'クラッシュ・レポート・ファイル',
            exit: '出てくる',
            reload: 'リスタート',
            crashIdCopy: 'クリップボードにコピーされたクラッシュID',
            errorCodeCopy: 'クリップボードにコピーされたエラーコード'
        }
    }
};