class _ItemData {
    itemId = 0;
    count = 0;
    chance = 0;

    constructor(itemId = 0, count = 0, chance = 0) {
        this.itemId = itemId;
        this.count = count;
        this.chance = chance;
    }

    getMinimaze() {
        return [
            this.itemId,
            this.count
        ]
    }
}

export const ItemData = _ItemData;

class _CraftData {
    id = 0;
    craftingItems = [];
    craftingMoney = 0;
    createdItem = new _ItemData();
    chance = 0;
    time = 0;//В минутах
    maxCraftCount = 10;

    constructor(id, craftingMoney, craftingItems, createdItem, chance, time, maxCraftCount = 10) {
        this.id = id;
        this.craftingMoney = craftingMoney;
        this.craftingItems = craftingItems;
        this.createdItem = createdItem;
        this.chance = chance;
        this.time = time;
        this.maxCraftCount = maxCraftCount;
    }

    getMinimaze() {
        let craftingItems = [];

        this.craftingItems.forEach((item) => {
            craftingItems.push(item.getMinimaze());
        })

        return [
            this.id,
            this.craftingMoney,
            craftingItems,
            this.createdItem.getMinimaze(),
            this.chance,
            this.time,
            this.maxCraftCount
        ]
    }

    getChance(level, vipLevel) {
        let chance = Array.isArray(this.chance) ?
            this.chance[level] :
            this.chance;

        chance *= (vipLevel ? 2 : 1);

        return chance > 100 ? 100 : chance;
    }
}

export const CraftData = _CraftData;

class _PumpingData {
    id = 0;
    title = '';
    description = '';
    craftingItems = [];
    craftingMoney = 0;
    time = 0;

    constructor(id, description, craftingMoney, craftingItems, time, title = null) {
        this.id = id;
        this.description = description;
        this.craftingMoney = craftingMoney;
        this.craftingItems = craftingItems;
        this.time = time;
        this.title = title;
    }

    getMinimaze() {
        let craftingItems = [];

        this.craftingItems.forEach((item) => {
            craftingItems.push(item.getMinimaze());
        })

        return [
            this.id,
            this.description,
            this.craftingMoney,
            craftingItems,
            this.time
        ]
    }

    getMinimazeTitle() {
        let craftingItems = [];

        this.craftingItems.forEach((item) => {
            craftingItems.push(item.getMinimaze());
        })

        return [
            this.id,
            this.title,
            this.description,
            this.craftingMoney,
            craftingItems,
            this.time
        ]
    }
}

export const PumpingData = _PumpingData;


export const houseWorkShop = [
    null,
    {
        pumping: new PumpingData(1, 'cef.familyControl.desc.house.0', 150_000, [
            new ItemData(631, 24),
            new ItemData(634, 15)
        ], 6),
        crafts: [
            new CraftData(0, 1_000, [
                new ItemData(643, 8),
                new ItemData(15, 6),
                new ItemData(536, 16),
            ], new ItemData(655, 1), [35, 40, 45], 60 * 32, 1),

            new CraftData(1, 1_000, [
                new ItemData(643, 6),
                new ItemData(689, 8),
                new ItemData(611, 6),
                new ItemData(543, 8)
            ], new ItemData(675, 1), [35, 40, 45], 60 * 40, 1),

            new CraftData(2, 1_000, [
                new ItemData(643, 10),
                new ItemData(612, 6),
                new ItemData(540, 40)
            ], new ItemData(661, 1), [35, 40, 45], 60 * 48, 1),

            // new CraftData(3, 250, [
            //     new ItemData(799, 1),
            //     new ItemData(800, 2),
            //     new ItemData(611, 1),
            //     new ItemData(612, 1),
            //     new ItemData(798, 4),
            //     new ItemData(802, 1),
            // ], new ItemData(442, 1), [40, 44, 48], 60 * 6, 2),

            new CraftData(4, 50, [
                new ItemData(805, 1),
                new ItemData(806, 1),
            ], new ItemData(414, 1), [46, 48, 50], 60 * 2, 5),

            new CraftData(5, 50, [
                new ItemData(799, 1),
                new ItemData(798, 4),
                new ItemData(802, 2),
            ], new ItemData(415, 1), [42, 46, 50], 60 * 4, 2),

            new CraftData(21, 50, [
                new ItemData(863, 1000),
                new ItemData(867, 1000),
                new ItemData(246, 1)
            ], new ItemData(853, 100), [45, 55, 55], 60 * 3, 1),

            new CraftData(22, 100, [
                new ItemData(600, 1),
                new ItemData(609, 2),
                new ItemData(246, 2)
            ], new ItemData(857, 100), [45, 55, 55], 60 * 3, 1),

            new CraftData(23, 100, [
                new ItemData(950, 2),
                new ItemData(906, 300),
                new ItemData(853, 20),
                new ItemData(251, 1)
            ], new ItemData(859, 100), [45, 55, 55], 60 * 3, 1),

            new CraftData(24, 150, [
                new ItemData(387, 4),
                new ItemData(385, 2),
                new ItemData(848, 20),
                new ItemData(856, 20)
            ], new ItemData(861, 100), [45, 55, 55], 60 * 3, 1),

            new CraftData(25, 300, [
                new ItemData(912, 12),
                new ItemData(949, 8),
                new ItemData(932, 1)
            ], new ItemData(936, 3), [40, 45, 55], 60 * 5, 1),

            new CraftData(26, 500, [
                new ItemData(914, 12),
                new ItemData(950, 1),
                new ItemData(577, 20),
                new ItemData(385, 1),
                new ItemData(805, 5)
            ], new ItemData(937, 3), [40, 45, 55], 60 * 5, 1),

            new CraftData(27, 700, [
                new ItemData(915, 12),
                new ItemData(952, 5),
                new ItemData(906, 300),
                new ItemData(578, 1),
                new ItemData(531, 1)
            ], new ItemData(940, 3), [40, 45, 55], 60 * 5, 1),

            new CraftData(28, 1_000, [
                new ItemData(951, 6),
                new ItemData(950, 3),
                new ItemData(906, 300),
                new ItemData(938, 1),
                new ItemData(915, 12),
                new ItemData(802, 5)
            ], new ItemData(943, 3), [40, 45, 55], 60 * 5, 1),

            new CraftData(29, 1_000, [
                new ItemData(915, 12),
                new ItemData(949, 10),
                new ItemData(951, 8),
                new ItemData(937, 1),
                new ItemData(805, 5)
            ], new ItemData(946, 3), [40, 45, 55], 60 * 5, 1),

            new CraftData(30, 1_000, [
                new ItemData(940, 1),
                new ItemData(915, 12),
                new ItemData(952, 8),
                new ItemData(937, 1),
                new ItemData(949, 10),
                new ItemData(804, 2)
            ], new ItemData(948, 3), [40, 45, 55], 60 * 5, 1)

        ]
    },
    {
        pumping: new PumpingData(2, 'cef.familyControl.desc.house.1', 450_000, [
            new ItemData(631, 48),
            new ItemData(632, 24),
            new ItemData(634, 30),
            new ItemData(635, 15)
        ], 12),
        crafts: [
            new CraftData(6, 2_000, [
                new ItemData(643, 16),
                new ItemData(644, 8),
                new ItemData(15, 12),
                new ItemData(536, 32)
            ], new ItemData(656, 1), [0, 35, 40], 60 * 32, 1),

            new CraftData(7, 2_000, [
                new ItemData(643, 12),
                new ItemData(644, 6),
                new ItemData(689, 16),
                new ItemData(690, 8),
                new ItemData(543, 16),
                new ItemData(611, 12)
            ], new ItemData(676, 1), [0, 35, 40], 60 * 40, 1),

            new CraftData(8, 2_000, [
                new ItemData(643, 20),
                new ItemData(644, 10),
                new ItemData(612, 12),
                new ItemData(540, 80)
            ], new ItemData(662, 1), [0, 35, 40], 60 * 48, 1),

            new CraftData(9, 250, [
                new ItemData(397, 1),
                new ItemData(613, 1),
                new ItemData(798, 2),
                new ItemData(802, 2),
                new ItemData(801, 2),
                new ItemData(59, 1)
            ], new ItemData(445, 1), [0, 38, 42], 60 * 12, 2),

            new CraftData(10, 500, [
                new ItemData(611, 1),
                new ItemData(612, 2),
                new ItemData(613, 3),
                new ItemData(15, 1)
            ], new ItemData(791, 1), [0, 35, 40], 60 * 6, 2),

            new CraftData(20, 1_000, [
                new ItemData(592, 1),
                new ItemData(15, 2),
                new ItemData(611, 3),
                new ItemData(612, 3),
                new ItemData(613, 3),
                new ItemData(644, 1)
            ], new ItemData(835, 1), [0, 40, 45], 60 * 60, 1),

            new CraftData(31, 2_500, [
                new ItemData(950, 15),
                new ItemData(19, 3),
                new ItemData(97, 1),
                new ItemData(916, 1),
                new ItemData(802, 5)
            ], new ItemData(923, 1), [0, 55, 55], 60 * 10, 1),

            new CraftData(32, 5_000, [
                new ItemData(950, 26),
                new ItemData(906, 300),
                new ItemData(952, 8),
                new ItemData(805, 8),
                new ItemData(923, 1),
                new ItemData(918, 1)
            ], new ItemData(924, 1), [0, 42, 48], 60 * 25, 1),

            new CraftData(33, 10_000, [
                new ItemData(951, 36),
                new ItemData(949, 40),
                new ItemData(924, 1),
                new ItemData(908, 300),
                new ItemData(920, 1),
                new ItemData(802, 25),
                new ItemData(804, 5)
            ], new ItemData(925, 1), [0, 40, 45], 60 * 35, 1),

            new CraftData(34, 15_000, [
                new ItemData(952, 45),
                new ItemData(951, 50),
                new ItemData(950, 30),
                new ItemData(925, 1),
                new ItemData(804, 8),
                new ItemData(805, 20)
            ], new ItemData(926, 1), [0, 38, 42], 60 * 60, 1)
        ]
    },
    {
        pumping: new PumpingData(3, 'cef.familyControl.desc.house.2', 1_350_000, [
            new ItemData(631, 96),
            new ItemData(632, 48),
            new ItemData(633, 24),
            new ItemData(634, 60),
            new ItemData(635, 30),
            new ItemData(636, 15)
        ], 24),
        crafts: [
            new CraftData(11, 3_000, [
                new ItemData(643, 32),
                new ItemData(644, 16),
                new ItemData(645, 8),
                new ItemData(15, 24),
                new ItemData(536, 64)
            ], new ItemData(657, 1), [0, 0, 35], 60 * 32, 1),

            new CraftData(12, 3_000, [
                new ItemData(643, 24),
                new ItemData(644, 12),
                new ItemData(645, 6),
                new ItemData(689, 32),
                new ItemData(690, 16),
                new ItemData(691, 8),
                new ItemData(543, 32),
                new ItemData(611, 24)
            ], new ItemData(677, 1), [0, 0, 35], 60 * 40, 1),

            new CraftData(13, 3_000, [
                new ItemData(643, 40),
                new ItemData(644, 20),
                new ItemData(645, 10),
                new ItemData(612, 24),
                new ItemData(540, 160)
            ], new ItemData(663, 1), [0, 0, 35], 60 * 48, 1),

            new CraftData(14, 1_000, [
                new ItemData(574, 250),
                new ItemData(651, 200),
                new ItemData(652, 100),
                new ItemData(653, 50),
                new ItemData(654, 25)
            ], new ItemData(650, 1), [0, 0, 35], 1, 10),

            new CraftData(15, 1_000, [
                new ItemData(469, 25)
            ], new ItemData(649, 1), [0, 0, 35], 1, 10),

            new CraftData(16, 10_000, [
                new ItemData(649, 3),
                new ItemData(650, 5)
            ], new ItemData(669, 1), [0, 0, 1], 1, 10),

            new CraftData(17, 10_000, [
                new ItemData(649, 3),
                new ItemData(650, 5)
            ], new ItemData(670, 1), [0, 0, 1], 1, 10),

            new CraftData(18, 10_000, [
                new ItemData(649, 3),
                new ItemData(650, 5)
            ], new ItemData(672, 1), [0, 0, 1], 1, 10),

            new CraftData(19, 10_000, [
                new ItemData(650, 10)
            ], new ItemData(673, 1), [0, 0, 1], 1, 10),
        ]
    }
]
