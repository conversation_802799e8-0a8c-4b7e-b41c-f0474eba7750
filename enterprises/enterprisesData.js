import {CraftData, ItemData, PumpingData} from './craft';

export const enterprisesType = {
    none: 0,
    fakeSeal: 1,
    productionIllegalGreen: 2,
    productionIllegalBlue: 3,
    productionIllegalWhite: 4
}

export const enterprises = {
    [enterprisesType.none]: null,

    [enterprisesType.fakeSeal]: [
        {
            interiorId: 246785,
            interiorProps: [
                'production',
                'security_low',
                'interior_basic',
                'chair01',
                'chair02',
                'chair03',
                'chair04',
                'chair05',
                'chair06',
                'chair07',
            ],
            exitPosition: new mp.Vector3(1173.3984, -3196.5529, -39.0079),
            exitPositionA: 84,
            warehousePosition: new mp.Vector3(1161.312, -3191.166, -39.008),
            npc: {
                dialogId: '148',
                description: 'Заведующий Downtown Cab Co',
                type: null,
                skin: 'a_m_m_og_boss_01',
                posX: '1157.4179',
                posY: '-3194.2211',
                posZ: '-39.0079',
                posA: '267.7',
                speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
            },
            //
            pumping: new PumpingData(0, 'cef.familyControl.desc.fakeSeal.0', 100_000, [
                new ItemData(631, 10),
                new ItemData(643, 6)
            ], 6, 'cef.familyControl.title.fakeSeal'),
            crafts: [
                new CraftData(0, 0, [
                    new ItemData(678, 4),
                    new ItemData(681, 4)
                ], new ItemData(684, 1), [100, 100 , 100], 60 * 60 * 2, 4),// , 2) - лимит
                new CraftData(1, 0, [
                    new ItemData(678, 1),
                    new ItemData(681, 1)
                ], new ItemData(685, 1), [100, 100 , 100], 60 * 60 * 4, 2)
            ]
        },

        {
            interiorId: 246785,
            interiorProps: [
                'equipment_basic',
                'production',
                'security_low',
                'clutter',
                'interior_upgrade',
                'chair01',
                'chair02',
                'chair03',
                'chair04',
                'chair05',
                'chair06',
                'chair07',
            ],
            exitPosition: new mp.Vector3(1173.3984, -3196.5529, -39.0079),
            exitPositionA: 84,
            warehousePosition: new mp.Vector3(1161.312, -3191.166, -39.008),
            npc: {
                dialogId: '148',
                description: 'Заведующий Downtown Cab Co',
                type: null,
                skin: 'a_m_m_og_boss_01',
                posX: '1157.4179',
                posY: '-3194.2211',
                posZ: '-39.0079',
                posA: '267.7',
                speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
            },
            //
            pumping: new PumpingData(1, 'cef.familyControl.desc.fakeSeal.1', 300_000, [
                new ItemData(631, 20),
                new ItemData(632, 10),
                new ItemData(643, 12),
                new ItemData(644, 6),
            ], 24),
            crafts: [
                new CraftData(2, 0, [
                    new ItemData(679, 4),
                    new ItemData(681, 8),
                    new ItemData(682, 4)
                ], new ItemData(686, 1), [0, 100 , 100], 60 * 60 * 2, 2),
                new CraftData(3, 0, [
                    new ItemData(679, 8),
                    new ItemData(683, 4)
                ], new ItemData(687, 1), [0, 100 , 100], 60 * 60 * 4, 2)
            ]
        },

        {
            interiorId: 246785,
            interiorProps: [
                'equipment_upgrade',
                'production',
                'security_high',
                'set_up',
                'interior_upgrade',
                'chair01',
                'chair02',
                'chair03',
                'chair04',
                'chair05',
                'chair06',
                'chair07',
            ],
            exitPosition: new mp.Vector3(1173.3984, -3196.5529, -39.0079),
            exitPositionA: 84,
            warehousePosition: new mp.Vector3(1161.312, -3191.166, -39.008),
            npc: {
                dialogId: '148',
                description: 'Заведующий Downtown Cab Co',
                type: null,
                skin: 'a_m_m_og_boss_01',
                posX: '1157.4179',
                posY: '-3194.2211',
                posZ: '-39.0079',
                posA: '267.7',
                speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
            },
            //
            pumping: new PumpingData(2, 'cef.familyControl.desc.fakeSeal.2', 900_000, [
                new ItemData(631, 40),
                new ItemData(632, 20),
                new ItemData(633, 10),
                new ItemData(643, 24),
                new ItemData(644, 12),
                new ItemData(645, 6),
            ], 48),
            crafts: [
                new CraftData(4, 0, [
                    new ItemData(680, 1),
                    new ItemData(683, 2),
                    new ItemData(286, 50_000)
                ], new ItemData(688, 100_000), [0, 0 , 100], 60 * 60 * 2, 1),
                new CraftData(5, 0, [
                    new ItemData(680, 2),
                    new ItemData(683, 4),
                    new ItemData(286, 100_000)
                ], new ItemData(688, 300_000), [0, 0 , 100], 60 * 60 * 4, 1),
                new CraftData(6, 0, [
                    new ItemData(680, 4),
                    new ItemData(683, 8),
                    new ItemData(286, 200_000)
                ], new ItemData(688, 900_000), [0, 0 , 100], 60 * 60 * 8, 1)
            ]
        }
    ],

    [enterprisesType.productionIllegalGreen]: [
        {
            interiorId: 247297, //изменить
            interiorProps: [
                'weed_growtha_stage',
                'weed_upgrade_equip',
                'weed_drying',
                'weed_growthb_stage3',
                'weed_growthc_stage2',
                'weed_growthd_stage3',
                'weed_growthe_stage2',
                'weed_growthf_stage1',
                'weed_growthg_stage3',
                'weed_growthh_stage3',
                'weed_growthi_stage2',
                'weed_security_upgrade',
                'weed_production',
                'weed_set_up',
                'weed_hosea',
                'weed_hoseb',
                'weed_hosec',
                'weed_hosed',
                'weed_hosee',
                'weed_hosef',
                'weed_hoseg',
                'weed_hoseh',
                'weed_hosei',
                'light_growtha_stage23_upgrade',
                'light_growthb_stage23_upgrade',
                'light_growthc_stage23_upgrade',
                'light_growthd_stage23_upgrade',
                'light_growthe_stage23_upgrade',
                'light_growthf_stage23_upgrade',
                'light_growthg_stage23_upgrade',
                'light_growthh_stage23_upgrade',
                'light_growthi_stage23_upgrade',
                'weed_low_security',
                'weed_chairs',
            ],
            exitPosition: new mp.Vector3(1066.131, -3183.529, -39.163),
            exitPositionA: 84,
            warehousePosition: new mp.Vector3(1061.357, -3183.044, -39.164),
            npc: {
                dialogId: '148',
                description: 'Заведующий Downtown Cab Co',
                type: null,
                skin: 'a_m_m_og_boss_01',
                posX: '1063.936',
                posY: '-3188.882',
                posZ: '-39.143',
                posA: '54.08',
                speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
            },
            //
            pumping: new PumpingData(0, 'cef.familyControl.desc.productionIllegalGreen.0', 400_000, [
                new ItemData(69, 5),
                new ItemData(631, 8),
                new ItemData(632, 4),
                new ItemData(643, 6),
                new ItemData(644, 3)
            ], 12, 'cef.familyControl.title.productionIllegalGreen'),
            crafts: [
                new CraftData(0, 0, [
                    new ItemData(393, 4),
                    new ItemData(692, 20),
                    new ItemData(77, 20),
                    new ItemData(689, 1)
                ], new ItemData(747, 400), [100, 100, 100], 60 * 60 * 4, 1),
                new CraftData(1, 0, [
                    new ItemData(393, 8),
                    new ItemData(692, 20),
                    new ItemData(693, 10),
                    new ItemData(77, 40),
                    new ItemData(689, 2)
                ], new ItemData(748, 2000), [100, 100, 100], 60 * 60 * 8, 1),
                new CraftData(2, 0, [
                    new ItemData(393, 16),
                    new ItemData(692, 20),
                    new ItemData(693, 10),
                    new ItemData(694, 5),
                    new ItemData(77, 80),
                    new ItemData(689, 4)
                ], new ItemData(749, 10000), [100, 100, 100], 60 * 60 * 16, 1)
            ]
        },
    ],
    [enterprisesType.productionIllegalBlue]: [
        {
            interiorId: 247553,  //изменить
            interiorProps: [
                'equipment_basic',
                'security_lo',
                'coke_cut_01',
                'coke_cut_03',
                'security_high',
                'equipment_basic',
                'production_upgrade',
                'equipment_upgrade',
                'production_basic',
                'coke_cut_05',
                'set_up',
                'table_equipment',
                'table_equipment_upgrade',
                'coke_press_upgrade',
            ],
            exitPosition: new mp.Vector3(1088.642, -3187.887, -38.993),
            exitPositionA: 84,
            warehousePosition: new mp.Vector3(1094.626, -3193.663, -38.993),
            npc: {
                dialogId: '148',
                description: 'Заведующий Downtown Cab Co',
                type: null,
                skin: 'a_m_m_og_boss_01',
                posX: '1087.459',
                posY: '-3199.025',
                posZ: '-38.993',
                posA: '-29.87',
                speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
            },
            //
            pumping: new PumpingData(0, 'cef.familyControl.desc.productionIllegalBlue.0', 800_000, [

                new ItemData(70, 5),
                new ItemData(631, 16),
                new ItemData(632, 8),
                new ItemData(633, 4),
                new ItemData(643, 12),
                new ItemData(644, 6),
                new ItemData(645, 3)
            ], 24, 'cef.familyControl.title.productionIllegalBlue'),
            crafts: [
                new CraftData(0, 0, [
                    new ItemData(515, 2),
                    new ItemData(4, 1),
                    new ItemData(509, 1),
                    new ItemData(690, 1)
                ], new ItemData(750, 100), [100, 100, 100], 60 * 60 * 4, 1),

                new CraftData(1, 0, [
                    new ItemData(515, 4),
                    new ItemData(4, 2),
                    new ItemData(509, 2),
                    new ItemData(510, 1),
                    new ItemData(690, 2)
                ], new ItemData(751, 500), [100, 100, 100], 60 * 60 * 8, 1),

                new CraftData(2, 0, [
                    new ItemData(515, 8),
                    new ItemData(4, 4),
                    new ItemData(509, 4),
                    new ItemData(510, 2),
                    new ItemData(512, 1),
                    new ItemData(690, 4)
                ], new ItemData(752, 2500), [100, 100, 100], 60 * 60 * 16, 1)
            ]
        }
    ],
    [enterprisesType.productionIllegalWhite]: [
        {
            interiorId: 247041,  //изменить
            interiorProps: [
                'meth_lab_upgrade',
                'meth_lab_production',
                'meth_lab_security_high',
                'meth_lab_setup',
                'meth_lab_empty',
            ],//{"x":"1161.312","y":"-3191.166","z":"-39.008","dimension":0}
            exitPosition: new mp.Vector3(997.285, -3200.737, -36.393),
            exitPositionA: 84,
            warehousePosition: new mp.Vector3(1004.190, -3200.521, -38.993),
            npc: {
                dialogId: '148',
                description: 'Заведующий Downtown Cab Co',
                type: null,
                skin: 'a_m_m_og_boss_01',
                posX: '1016.678',
                posY: '-3196.164',
                posZ: '-38.993',
                posA: '88.12',
                speechParam: 'SPEECH_PARAMS_FORCE_SHOUTED'
            },
            //
            pumping: new PumpingData(0, 'cef.familyControl.desc.productionIllegalWhite.0', 1_600_000, [
                new ItemData(71, 5),
                new ItemData(631, 32),
                new ItemData(632, 16),
                new ItemData(633, 8),
                new ItemData(643, 24),
                new ItemData(644, 12),
                new ItemData(645, 6)
            ], 48, 'cef.familyControl.title.productionIllegalWhite'),
            crafts: [
                new CraftData(0, 0, [
                    new ItemData(389, 12),
                    new ItemData(77, 40),
                    new ItemData(517, 1),
                    new ItemData(691, 1)
                ], new ItemData(753, 100), [100, 100, 100], 60 * 60 * 4, 1),
                new CraftData(1, 0, [
                    new ItemData(389, 24),
                    new ItemData(77, 80),
                    new ItemData(517, 2),
                    new ItemData(513, 1),
                    new ItemData(691, 2)
                ], new ItemData(754, 250), [100, 100, 100], 60 * 60 * 8, 1),
                new CraftData(2, 0, [
                    new ItemData(389, 48),
                    new ItemData(77, 160),
                    new ItemData(517, 4),
                    new ItemData(513, 2),
                    new ItemData(514, 1),
                    new ItemData(691, 4)
                ], new ItemData(755, 625), [100, 100, 100], 60 * 60 * 16, 1)
            ]
        },
    ],
}
